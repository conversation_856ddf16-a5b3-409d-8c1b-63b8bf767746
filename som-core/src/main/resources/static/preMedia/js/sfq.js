$(function() {

	var Accordion = function(el, multiple) {
		this.el = el || {};
		this.multiple = multiple || false;

		// Variables privadas
		var links = this.el.find('.link');
		// Evento
		links.on('click', {el: this.el, multiple: this.multiple}, this.dropdown)
	}

	Accordion.prototype.dropdown = function(e) {
		var $el = e.data.el;
			$this = $(this),
			$next = $this.next();

		$next.slideToggle();
		$this.parent().toggleClass('open');

		if($this.parent().hasClass("open")){
			$this.context.children[1].style.display = "none"
			$this.context.children[2].style.display = "block"
		} else {
			$this.context.children[1].style.display = "block"
			$this.context.children[2].style.display = "none"
		}

		if (!e.data.multiple) {
			$el.find('.submenu').not($next).slideUp().parent().removeClass('open');
		};
	}

	var accordion1 = new Accordion($('#accordion'), false);
	var accordion2 = new Accordion($('#accordion_2'), false);
	var accordion3 = new Accordion($('#accordion_3'), false);
	var accordion4 = new Accordion($('#accordion_4'), false);
	var accordion5 = new Accordion($('#accordion_5'), false);
	var accordion6 = new Accordion($('#accordion_6'), false);
	var accordion7 = new Accordion($('#accordion_7'), false);
	var ac_1 = new Accordion($('#ac_1'), false);
	var ac_2 = new Accordion($('#ac_2'), false);



	/**
	 *
	 * 二级菜单
	 */
	 var SecondaryMenu = function(el, multiple) {
		this.el = el || {};
		this.multiple = multiple || false;

		// Variables privadas
		var links = this.el.find('.secondary-menu');
		// Evento
		links.on('click', {el: this.el, multiple: this.multiple}, this.dropdown)
	}

	SecondaryMenu.prototype.dropdown = function(e) {
		var $el = e.data.el;
			$this = $(this),
			$next = $this.next();

		$next.slideToggle();
		$this.parent().toggleClass('open');

		if (!e.data.multiple) {
			$el.find('.submenu').not($next).slideUp().parent().removeClass('open');
		};
	}

	var accordion3 = new SecondaryMenu($('#secondaryMenu'), false);
	var accordion4 = new SecondaryMenu($('#secondaryMenu1'), false);
});
