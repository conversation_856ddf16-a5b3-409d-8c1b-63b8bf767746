* {
    margin: 0;
    padding: 0;
}

.container {
    height: 920px;
    width: 700px;
    color: black;
    margin: 0 auto;
    background-color: #deebf7;
    border-radius: 25px 25px 5px 5px;
}

body {
    font-family: 微软雅黑;
}

.header {
    height: 1%;
    width: 100%;
    position: relative;
    text-align: center;
    top: 10px
}

.header-title {
    font-size: 22px;
    font-weight: bold
}

.content {
    height: 96%;
    width: 100%;
    position: relative;
    overflow: auto;
}

.content-inner {
    height: 96%;
    padding-right: 5px;
}

.content::-webkit-scrollbar {
    width: 6px;
    height: 6px;
    background: transparent;
}

.content::-webkit-scrollbar-thumb {
    background: transparent;
    border-radius: 4px;
}

.content:hover::-webkit-scrollbar-thumb {
    background: hsla(0, 0%, 53%, 0.4);
}

.content:hover::-webkit-scrollbar-track {
    background: hsla(0, 0%, 53%, 0.1);
}

.in-group-circumstance {
    width: 100%;
}

.title {
    height: 5%;
    width: 100%;
    font-size: 18px;
    font-weight: bold;
    position: relative;
    margin-top: 0.5rem;
}

.hop-title {
    width: 100%;
    font-size: 18px;
    font-weight: bold;
    margin-top: 0.5rem;
}

.title img {
    padding: 0 2%;
}

.divider {
    background-color: #dcdfe6;
    position: relative;
    display: block;
    height: 1px;
    width: 96%;
    margin: 1.5%
}

/**
    统一flex布局
*/
.in-group-base,
.in-group-diagnose,
.in-group-diagnose .in-group-diagnose-details,
.title,
.in-group-dip-group {
    display: flex;
    justify-content: left;
    align-items: center;
}

.hop-title,
.in-group-base div {
    display: flex;
    justify-content: center;
    align-items: center;
}

/**
    DIP入组信息
*/
.in-group-content {
    width: 98%;
    padding-left: 2%;
    color: #797979;
}

.in-group-dip-group {
    height: 25%;
    width: 100%;
    font-weight: 600;
    font-size: 16px;
    padding-top: 1%;
    display: flex;
    align-items: center;

}

.in-group-dip-group div {
    margin-left: 2%;
}

.in-group-diagnose {
    height: 100%;
    padding-top: 2%;
}

.in-group-diagnose .in-group-diagnose-details {
    width: 50%;
}

.in-group-base-info {
    font-size: 12px;
    height: 80%;
    width: 88%;
    display: flex;
    flex-direction: column;
}

.in-group-base-info div img {
    margin-right: 0.5rem;
}

.in-group-base-info .in-group-base {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 2%;
}

.operation {
    min-height: 4rem;
    display: flex;
    align-items: center;
}

.operation::-webkit-scrollbar {
    width: 6px;
    height: 6px;
    background: transparent;
}

.operation::-webkit-scrollbar-thumb {
    background: transparent;
    border-radius: 4px;
}

.operation:hover::-webkit-scrollbar-thumb {
    background: hsla(0, 0%, 53%, 0.4);
}

.operation:hover::-webkit-scrollbar-track {
    background: hsla(0, 0%, 53%, 0.1);
}

.operation p {
    display: block;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}


/**
    DIP支付预测
*/
.pre-pay {
    width: 100%;
    /*margin-top: 2.5rem;*/
}

.pre-pay-content {
    width: 96%;
    padding: 2% 2% 0 2%;
    position: relative;
}

.pre-pay-content-range {
    width: 100%;
    height: 25px;
    display: flex;
    flex-wrap: nowrap;
}

.range-item {
    text-align: center;
    line-height: 25px;
    font-size: 12px;
    position: relative;
}

/*  */

.low {
    width: 20%;
    height: 100%;
    border-right: 1px solid #1296dbab;
    /*border-right: none;*/
    border-radius: 25px 0 0 25px;
    background-image: linear-gradient(to right, rgba(158,173,189, 0.63), rgba(0,205,209, 0.53));

}

.normal {
    width: 20%;
    height: 100%;
    border-right: 1px solid #1296dbab;
    /*border-right: none;*/
    background-color: rgba(0,205,209, 0.53);
}

.normal-to-high {
    width: 40%;
    height: 100%;
    border-right: 1px solid #00A584ab;
    /*border-right: none;*/
    background-image:linear-gradient(to right, rgba(0,205,209, 0.53), rgba(224, 107, 85, 0.7));
}

.high {
    position: relative;
    width: 20%;
    height: 100%;
    border-radius: 0 25px 25px 0;
    background-color: rgba(224, 107, 85, 0.7);
}

.range-money {
    position: absolute;
    right: -18%;
}

.range-money-normal {
    position: absolute;
    right: -20%;
}

.tooltip__popper.is-dark {
    display: inline-block;
    background: #3BC08E99;
    color: #ffffff;
}

.tooltip__popper {
    height: 15px;
    border-radius: 15px;
    padding: 10px;
    z-index: 2000;
    font-size: 12px;
    line-height: 1.2;
    min-width: 10px;
    word-wrap: break-word;
    position: relative;
}

.tooltip__popper .popper__arrow {
    border-width: 6px;
}

.tooltip__popper .popper__arrow, .tooltip__popper .popper__arrow:after {
    display: block;
    width: 0;
    height: 0;
    border-color: transparent;
    border-style: solid;
}

.tooltip__popper .popper__arrow:after {
    position: absolute;
    bottom: -7px;
    left: 26px;
    margin: -5px;
    border-top-color: #3BC08E99;
    border-bottom-width: 1px;
    content: " ";
    border-width: 6px;
}


/**
    病组智能推荐
*/
.group-recommend {
    width: 100%;
}

.group-recommend-content {
    width: 96%;
    padding: 0 2%;
    position: relative;
}

.core-group {
    width: 100%;
}

/**
    病案质控
*/
.record-qc {
    width: 100%;
}

.record-content {
    width: 96%;
    padding: 0 2%;
    position: relative;
}

/**
    清单质控
 */
.sl-content {
    width: 100%;
    /*padding: 0 2%;*/
}

.sl-content-item {
    display: block;
    height: 100%;
}

.sl-content-item::-webkit-scrollbar {
    width: 6px;
    height: 6px;
    background: transparent;
}

.sl-content-item::-webkit-scrollbar-thumb {
    background: transparent;
    border-radius: 4px;
}

.sl-content-item:hover::-webkit-scrollbar-thumb {
    background: hsla(0, 0%, 53%, 0.4);
}

.sl-content-item:hover::-webkit-scrollbar-track {
    background: hsla(0, 0%, 53%, 0.1);
}

/*!***/
/*    德阳新增*/
/* *!*/
/*.group-dy-content{*/
/*    width: 96%;*/
/*    padding: 0 2%;*/
/*    position: relative;*/
/*}*/

/*tab实现*/

/* Style the tab */

.top_tabs {
    display: flex;
    width: 96%;
    margin: 0 2%;
    margin-top: 1.5rem;
    border-bottom: 1px solid #ddd;
}

.top_tabs .tab {
    height: 5%;
    font-size: 16px;
    /*font-weight: bold;*/
    margin-top: 0.5rem;
    padding: 10px;
    border: none;
    outline: none;
    cursor: pointer;
    color: #666666;
    background-color: #fafafa;
    display: flex;
    margin-right: 4px;
    border-radius: 4px 4px 0px 0px;

}

.top_tabs .tab.active {
    color: rgba(18, 150, 219, 0.9) !important;
    border-bottom: 2px solid rgba(18, 150, 219, 0.9) !important;
}

/* Style the buttons that are used to open the tab content */
.top_tabs .tab:hover {
    color: rgba(18, 150, 219, 0.9);
}

/* Style the tab content */
.tabcontent {
    width: 100%;
    /*margin: 0 2%;*/
    /*padding-top: 1.5rem;*/
}
.top_tabs .tab_icon{
    height: 25px;
    width: 25px;
    padding-right: 2px;
}

.top_tabs .tab span{
   display: flex;
    line-height: 25px;
}
 span.warn-tips{
     color: #3BC08E99;font-size: 12px;
 }

 .in-ccmcc-base{
     display: flex;
     padding-top: 1%;
 }

.in-ccmcc-base div {
    display: flex;
    /* justify-content: center; */
    align-items: center;
    flex: 1;
    justify-content: flex-start;
}

#content2 .none_item_content{
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 22px;
    font-weight: 600;
}
