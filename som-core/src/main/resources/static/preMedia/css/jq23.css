@charset "utf-8";

#tabs2 {
	overflow: hidden;
	width: 100%;
	margin: 0;
	list-style: none;
	position: relative;
	background: #fff;
}
#tabs2:after{
	content: "";
	position: absolute;
	left: 2%;
	bottom: 0;
	width: 96%;
	height: 2px;
	background-color: #E4E7ED;
	z-index: 1;
}
#tabs2 li {
	float: left;
}
#tabs2 a {
	position: relative;
	background: #fff;
	padding: .7em 0;
	text-align: center;
	width: 120px;
	float: left;
	text-decoration: none;
	color: #444;
	z-index: 3;
}
 #tabs2 a:hover, #tabs2 a:hover::after, #tabs2 a:focus, #tabs2 a:focus::after {
	 color: rgba(91,174,99,0.53);
}
#tabs2 a:focus {
	outline: 0;
}

#content2 {
	background: #fff;
	height: calc(300px - 4%);
	position: relative;
	padding: 2%;
	z-index: 2;
}
#content2 h2, #content2 h3, #content2 p {
	margin: 0 0 15px 0;
}

