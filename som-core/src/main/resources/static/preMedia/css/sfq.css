ul {
	list-style-type: none;
}

a {
	color: #b63b4d;
	text-decoration: none;
}

/** =======================
 * Contenedor Principal
 ===========================*/
h1 {
 	color: #FFF;
 	font-size: 24px;
 	font-weight: 400;
 	text-align: center;
 	margin-top: 80px;
 }

h1 a {
 	color: #c12c42;
 	font-size: 16px;
 }

 .accordion {
 	width: 100%;
	 margin-top: 2rem;
 	background: #FFF;
 	-webkit-border-radius: 4px;
 	-moz-border-radius: 4px;
 	border-radius: 4px;
 }

.accordion .link {
	cursor: pointer;
	display: block;
	padding: 15px 15px 15px 42px;
	color: #4D4D4D;
	font-size: 14px;
	font-weight: 700;
	border-bottom: 1px solid #CCC;
	position: relative;
	-webkit-transition: all 0.4s ease;
	-o-transition: all 0.4s ease;
	transition: all 0.4s ease;
}
.secondary-menu{
	cursor: pointer !important;
	display: block;
}

.accordion li:last-child .link {
	border-bottom: 0;
}

.accordion li i {
	position: absolute;
	top: 16px;
	left: 12px;
	font-size: 18px;
	color: #595959;
	-webkit-transition: all 0.4s ease;
	-o-transition: all 0.4s ease;
	transition: all 0.4s ease;
}

.accordion li i.fa-chevron-down {
	right: 12px;
	left: auto;
	font-size: 16px;
}

.accordion li.open .link {
	color: #b63b4d;
}

.accordion li.open i {
	color: #b63b4d;
}
.accordion li.open i.fa-chevron-down {
	-webkit-transform: rotate(180deg);
	-ms-transform: rotate(180deg);
	-o-transform: rotate(180deg);
	transform: rotate(180deg);
}

/**
 * Submenu
 -----------------------------*/
 .submenu, .secondary-submenu {
 	display: none;
 	background: rgba(74,165,211, 0.29);
 	font-size: 14px;
 }

 .submenu li {
 	/*border-bottom: 1px solid #4b4a5e;*/
	 position: relative;
 }
 .secondary-submenu li {
 	/*border-bottom: 1px solid #4b4a5e;*/
	 position: relative;
 }

 .submenu a {
 	display: block;
 	text-decoration: none;
	 color: black;
	 cursor: default;
 	padding: 12px;
 	padding-left: 42px;
 	-webkit-transition: all 0.25s ease;
 	-o-transition: all 0.25s ease;
 	transition: all 0.25s ease;
 }

 .submenu a:hover {
 	background: rgba(18, 150, 219, 0.4);
 	color: #FFF;
 }

 .submenu .a-hover{
	background: rgba(18, 150, 219, 0.4);
 }

 .secondary-submenu a:hover{
	color: black;
	background: none;
 }


.sfq-header {
	width: 95%;
}
.sfq-header p {
	display: inline-block;
	font-size: 12px;
	overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}
.des-icon{
	position: absolute;
	left: 12px;
	top: 10px;
}
