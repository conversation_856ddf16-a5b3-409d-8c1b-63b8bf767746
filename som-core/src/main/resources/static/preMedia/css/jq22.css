@charset "utf-8";

#tabs {
	overflow: hidden;
	width: 96%;
	margin: 0;
	padding: 0 2%;
	list-style: none;
	/*background: #fff;*/
	position: relative;
}
#tabs:after{
	content: "";
	position: absolute;
	left: 2%;
	bottom: 0;
	width: 96%;
	height: 2px;
	background-color: #E4E7ED;
	z-index: 1;
}
#tabs li {
	float: left;
}
#tabs a {
	position: relative;
	/*background: #fff;*/
	padding: .7em 0;
	width: 120px;
	text-align: center;
	float: left;
	text-decoration: none;
	color: #444;
}
 #tabs a:hover, #tabs a:hover::after, #tabs a:focus, #tabs a:focus::after {
	 color: rgba(18, 150, 219, 0.9);
}
 .active{
	 color: rgba(18, 150, 219, 0.9)!important;
	 border-bottom: 2px solid rgba(18, 150, 219, 0.9)!important;
	 z-index: 10;
 }
#tabs a:focus {
	outline: 0;
}

#content {
	/*background: #fff;*/
	/*padding: 2em;*/
	height: 400px;
	position: relative;
	z-index: 2;
	font-size: 13px;
}
#content h2, #content h3, #content p {
	margin: 0 0 15px 0;
}
/* ------------------------------------------------- */

#about {
	color: #999;
}
#about a {
	color: #eee;
}

.sl-content-padding1{
	padding: 0 2%;
}

.sl-content-padding2{
	padding: 2% 2% 0 2%;
}

.sl-qi-item-wrap{
	padding: 2% 0 2% 2%;
	border: 1px solid #EBEEF5;
	width: 98%;
	margin-bottom: 1%;
}

.sl-qi-item-wrap-title{
	color: #e6a23c;
	border-bottom: 1px solid #EBEEF5;
	width: 98%;
	padding: 1% 0;
	font-size: 15px;
}
.sl-qi-complete{
	padding: 25% 0% 0% 30%;
	width: 98%;
	font-size: 30px;
	font-weight:bold;
}
