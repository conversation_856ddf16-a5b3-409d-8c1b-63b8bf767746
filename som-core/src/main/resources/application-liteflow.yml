liteflow:
  rule-source-ext-data-map:
    applicationName: somplatform
    #是否开启SQL日志
    sqlLogEnabled: true
    #是否开启SQL数据轮询自动刷新机制 默认不开启
    pollingEnabled: true
    pollingIntervalSeconds: 60
    pollingStartSeconds: 60
    #以下是chain表的配置，这个一定得有
    chainTableName: som_chain
    chainApplicationNameField: application_name
    chainNameField: chain_name
    elDataField: el_data
    chainEnableField: chain_enable
    #以下是script表的配置，如果使用了脚本这个必须得有
    scriptTableName: som_chain_script
    scriptApplicationNameField: application_name
    scriptIdField: script_id
    scriptNameField: script_name
    scriptDataField: script_data
    scriptTypeField: script_type
    scriptLanguageField: script_language
    scriptEnableField: script_enable

  print-banner: false