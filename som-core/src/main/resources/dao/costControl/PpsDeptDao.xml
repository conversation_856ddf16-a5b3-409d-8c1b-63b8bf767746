<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.som.dao.costControl.PpsDeptDao">
    <select id="list" resultType="com.my.som.vo.costControl.PpsDeptIndexVo">
        select
            a.dscg_caty_codg_inhosp AS priOutHosDeptCode,
            IFNULL(a.dscg_caty_name_inhosp,'未填写科室') AS priOutHosDeptName,
            COUNT(1) AS balanceMedicalRecordNum,
            COUNT(case when a.grp_stas = '1' then 1 else null end) AS groupNum,
            COUNT(case when a.grp_stas != '1' then 1 else null end) AS noGroupNum,
            IFNULL(count(distinct (case when a.grp_stas = '1' then a.dis_gp_codg else null end)),0) AS ppsGroupNum,
            ROUND(IFNULL(SUM(c.cd_grp_wt),0),2) AS totalPpsGroupWeight, <!--总权重-->
            ROUND(AVG(b.act_ipt), 2) AS avgDays, <!--平均住院日-->
            ROUND(AVG(case when a.grp_stas = '1' then b.act_ipt else null end), 2) AS inGroupAvgDays, <!--入组平均住院日-->
            ROUND(AVG(b.ipt_sumfee), 2) AS avgCost, <!--平均住院费用-->
            ROUND(AVG(case when a.grp_stas = '1' then b.ipt_sumfee else null end), 2) AS inGroupAvgCost,<!--入组平均住院费用-->
            ROUND(IFNULL(sum(IFNULL(b.act_ipt,0)/NULLIF(c.standard_ave_hosp_day, 0))/COUNT(1),0),2) as timeIndex, <!--  时间消耗指数   -->
            ROUND(IFNULL(sum(IFNULL(b.ipt_sumfee,0)/NULLIF(c.standard_avg_fee, 0))/COUNT(1),0),2) as costIndex,  <!--  费用消耗指数   -->
            IFNULL(CONCAT(ROUND(SUM(b.drugfee)/NULLIF(SUM(b.ipt_sumfee),0)*100,2),'%'),0) AS medicalCostRate,<!--  药品费占比   -->
            IFNULL(CONCAT(ROUND(SUM(b.mcs_fee)/NULLIF(SUM(b.ipt_sumfee),0)*100,2),'%'),0) AS materialCostRate <!--  耗材费占比   -->
        from sts_pps_group_record a
        left join som_drg_grp_info b on a.SETTLE_LIST_ID = b.SETTLE_LIST_ID
        left join som_cd_standard_info c on a.dis_gp_codg=c.dis_gp_codg AND c.hosp_lv = #{queryParam.hospLv}
        where 1 = 1
        <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
            AND  a.HOSPITAL_ID = #{queryParam.hospitalId}
        </if>
        <if test="queryParam.b16c!=null and queryParam.b16c!=''">
            AND a.dscg_caty_codg_inhosp = #{queryParam.b16c}
        </if>
        <if test="queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and
        queryParam.cy_end_date!=null and queryParam.cy_end_date!=''">
            AND a.dscg_time BETWEEN #{queryParam.cy_start_date} and #{queryParam.cy_end_date}
        </if>
        GROUP BY a.dscg_caty_codg_inhosp,a.dscg_caty_name_inhosp
        ORDER BY totalPpsGroupWeight desc
    </select>

    <select id="getCountInfo" resultType="com.my.som.vo.costControl.PpsDeptCountVo">
        select
            x.dscg_caty_codg_inhosp AS priOutHosDeptCode,
            IFNULL(x.dscg_caty_name_inhosp,'未填写科室') AS priOutHosDeptName,
            IFNULL(count(1),0) AS totalBalanceMedicalRecordNum,
            IFNULL(ROUND(count(1) / NULLIF(y.totalMedicalNum,0)*100,2),0) AS totalBalanceMedicalRecordNumRate,
            IFNULL(count(case when x.grp_stas = '1' then 1 else null end),0)   AS inGroupMedicalNum,
            IFNULL(ROUND(count(case when x.grp_stas = '1' then 1 else null end) /
              NULLIF(y.inGroupMedicalNum,0)*100,2),0) AS  inGroupMedicalNumRate,
            IFNULL(count(case when x.grp_stas != '1' then 1 else null end),0)  AS notGroupMedicalNum,
            IFNULL(ROUND(count(case when x.grp_stas != '1' then 1 else null end) /
              NULLIF(y.inGroupMedicalNum,0)*100,2),0) AS  notGroupMedicalNumRate,
            IFNULL(count(distinct (case when x.grp_stas = '1' then x.dis_gp_codg else null end)),0)   AS ppsGroupNum,
            IFNULL(ROUND(count(distinct (case when x.grp_stas = '1' then x.dis_gp_codg else null end)) /
              NULLIF(y.ppsGroupNum,0)*100,2),0) AS  ppsGroupNumRate,
            IFNULL(SUM(CASE WHEN x.grp_stas = '1' THEN x.cd_grp_wt ELSE NULL END),0)    AS totalPpsGroupWeight,
            IFNULL(ROUND(SUM(CASE WHEN x.grp_stas = '1' THEN x.cd_grp_wt ELSE NULL END) /
                NULLIF(y.totalPpsGroupWeight,0)*100,2),0) AS  totalPpsGroupWeightRate,
            IFNULL(ROUND(AVG(x.act_ipt), 2),0)   AS avgDays,
            y.hosAvgDays as hosAvgDays,
            IFNULL(ROUND(AVG(CASE WHEN x.grp_stas = '1' THEN x.act_ipt ELSE NULL END), 2),0)   AS inGroupAvgDays,
            y.hosInGroupAvgDays as hosInGroupAvgDays,
            IFNULL(ROUND(AVG(x.ipt_sumfee), 2),0)  AS avgCost,
            y.hosAvgCost as hosAvgCost,
            IFNULL(ROUND(AVG(CASE WHEN x.grp_stas = '1' THEN x.ipt_sumfee ELSE NULL END), 2),0)    AS inGroupAvgCost,
            y.hosInGroupAvgCost as hosInGroupAvgCost,
            IFNULL(ROUND(SUM(CASE WHEN x.grp_stas = '1' THEN x.act_ipt ELSE NULL END / NULLIF(x.standard_ave_hosp_day,0)) /
            NULLIF(COUNT(CASE WHEN x.grp_stas = '1' THEN 1 ELSE NULL END),0), 2),0)  AS timeIndex,
            y.hosTimeIndex as hosTimeIndex,
            IFNULL(ROUND(SUM(CASE WHEN x.grp_stas = '1' THEN x.ipt_sumfee ELSE NULL END / NULLIF(x.standard_avg_fee,0)) /
            NULLIF(COUNT(CASE WHEN x.grp_stas = '1' THEN 1 ELSE NULL END),0), 2),0)   AS costIndex,
            y.hosCostIndex as hosCostIndex,
            IFNULL(ROUND(AVG(x.drugfee), 2),0)   AS avgDrugFee,
            y.hosAvgMedicalCost as hosAvgMedicalCost,
            IFNULL(ROUND(AVG(x.mcs_fee), 2),0)   AS avgMcsFee,
            y.hosAvgMaterialCost as hosAvgMaterialCost
        from (
            select
                a.dscg_caty_codg_inhosp as dscg_caty_codg_inhosp,
                a.dscg_caty_name_inhosp AS dscg_caty_name_inhosp,
                a.grp_stas as grp_stas,
                a.dis_gp_codg as dis_gp_codg,
                b.act_ipt as act_ipt,
                b.ipt_sumfee as ipt_sumfee,
                c.cd_grp_wt as cd_grp_wt,
                c.standard_ave_hosp_day as standard_ave_hosp_day,
                c.standard_avg_fee as standard_avg_fee,
                b.drugfee as drugfee,
                b.mcs_fee as mcs_fee
            from sts_pps_group_record a
            left join som_drg_grp_info b on a.SETTLE_LIST_ID = b.SETTLE_LIST_ID
            left join som_cd_standard_info c on a.dis_gp_codg=c.dis_gp_codg AND c.hosp_lv = #{queryParam.hospLv}
            where 1 = 1
            <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
                AND  a.HOSPITAL_ID = #{queryParam.hospitalId}
            </if>
            <if test="queryParam.b16c!=null and queryParam.b16c!=''">
                AND a.dscg_caty_codg_inhosp = #{queryParam.b16c}
            </if>
            <if test="queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and
            queryParam.cy_end_date!=null and queryParam.cy_end_date!=''">
                AND a.dscg_time BETWEEN #{queryParam.cy_start_date} and #{queryParam.cy_end_date}
            </if>
        )x
        cross join (
            select
                IFNULL(count(1),0) AS totalMedicalNum,
                IFNULL(count(case when a.grp_stas = '1' then 1 else null end),0) AS inGroupMedicalNum,
                IFNULL(count(case when a.grp_stas != '1' then 1 else null end),0) AS notGroupMedicalNum,
                IFNULL(count(distinct (case when a.grp_stas = '1' then a.dis_gp_codg else null end)),0) AS ppsGroupNum,
                IFNULL(SUM(CASE WHEN a.grp_stas = '1' THEN c.cd_grp_wt ELSE NULL END),0) AS totalPpsGroupWeight,
                IFNULL(ROUND(AVG(b.act_ipt), 2),0) AS hosAvgDays,
                IFNULL(ROUND(AVG(CASE WHEN a.grp_stas = '1' THEN b.act_ipt ELSE NULL END), 2),0) AS hosInGroupAvgDays,
                IFNULL(ROUND(AVG(b.ipt_sumfee), 2),0) AS hosAvgCost,
                IFNULL(ROUND(AVG(CASE WHEN a.grp_stas = '1' THEN b.ipt_sumfee ELSE NULL END), 2),0) AS hosInGroupAvgCost,
                IFNULL(ROUND(SUM(CASE WHEN a.grp_stas = '1' THEN b.act_ipt ELSE NULL END / NULLIF(c.standard_ave_hosp_day,0))
                /NULLIF(COUNT(CASE WHEN a.grp_stas = '1' THEN 1 ELSE NULL END),0), 2),0) AS hosTimeIndex,
                IFNULL(ROUND(SUM(CASE WHEN a.grp_stas = '1' THEN b.ipt_sumfee ELSE NULL END /
                NULLIF(c.standard_avg_fee,0)) /NULLIF(COUNT(CASE WHEN a.grp_stas = '1' THEN 1 ELSE NULL END),0), 2),0) AS hosCostIndex,
                IFNULL(ROUND(AVG(b.drugfee), 2),0) AS hosAvgMedicalCost,
                IFNULL(ROUND(AVG(b.mcs_fee), 2),0)   AS hosAvgMaterialCost
            from sts_pps_group_record a
            left join som_drg_grp_info b on a.SETTLE_LIST_ID = b.SETTLE_LIST_ID
            left join som_cd_standard_info c on a.dis_gp_codg=c.dis_gp_codg AND c.hosp_lv = #{queryParam.hospLv}
            where 1 = 1
            <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
                AND  a.HOSPITAL_ID = #{queryParam.hospitalId}
            </if>
            <if test="queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and queryParam.cy_end_date!=null and queryParam.cy_end_date!=''">
                AND a.dscg_time BETWEEN #{queryParam.cy_start_date} and #{queryParam.cy_end_date}
            </if>
        ) y
        GROUP BY x.dscg_caty_codg_inhosp,x.dscg_caty_name_inhosp
        ORDER BY totalPpsGroupWeight desc
    </select>
</mapper>