<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.som.dao.costControl.PpsGroupKnowledgeBaseDao">
    <select id="list" resultType="com.my.som.vo.costControl.PpsGroupKnowledgeVo">
        select
            a.icd_codg AS mainDiagDiseCodg,
            a.ICD_NAME AS mainDiagDiseName,
            a.grp_flag AS grpFlag,
            a.grp_flag AS ppsGroupDetail,
            a.grper_type AS  grperType,
            a.item_codg AS itemCodg,
            a.itemname AS itemname,
            IFNULL(b.medcas_val,0) AS medcasVal,
            ROUND(b.standard_avg_fee,2) AS avgCost,
            ROUND(b.standard_ave_hosp_day,2) AS avgDays,
            ROUND(b.avg_drug_fee,2) AS avgDrugFee,
            ROUND(b.avg_mcs_fee,2) AS avgMaterailCost,
            ROUND(b.avg_abt_fee,2) AS avgAbtFee,
            ROUND(b.inspect_fee_standard_val,2) inspectFeeStandardVal
        from som_cd_dise_val_pay_dis_gp a
        left join som_cd_standard_info b on a.dis_gp_name = b.dis_gp_name AND  b.HOSPITAL_ID = #{queryParam.hospitalId}
        AND b.hosp_lv = #{queryParam.hospLv}
        <if test="queryParam.year!=null and queryParam.year!=''">
            AND  b.STANDARD_YEAR = #{queryParam.year}
        </if>
        where 1 = 1
        <if test="queryParam.queryPpsGroup!=null and queryParam.queryPpsGroup!=''">
            AND  b.dis_gp_name = (SELECT distinct dis_gp_name FROM sts_pps_group_record WHERE dis_gp_codg=#{queryParam.queryPpsGroup})
        </if>
        ORDER BY a.icd_codg
    </select>

    <select id="getCountByCoverRate" resultType="com.my.som.vo.costControl.PpsGroupCoverRateVo">
        select
            count(distinct(case when b.medcas_val is not null then a.icd_codg else null end)) AS mainCodeCoverNum,
            count(distinct(case when b.medcas_val is null then a.icd_codg else null end)) AS mainCodeNotCoverNum,
            count(case when b.medcas_val is not null then 1 else null end) AS ppsGroupCoverNum,
            count(case when b.medcas_val is null then 1 else null end) AS ppsGroupNumNotCoverNum
        from som_cd_dise_val_pay_dis_gp a
        left join som_cd_standard_info b on a.dis_gp_name = b.dis_gp_name AND  b.HOSPITAL_ID = #{queryParam.hospitalId}
        AND b.hosp_lv = #{queryParam.hospLv}
        <if test="queryParam.year!=null and queryParam.year!=''">
            AND  b.STANDARD_YEAR = #{queryParam.year}
        </if>
    </select>

    <select id="getCountByGroupClass" resultType="com.my.som.vo.costControl.PpsInGroupIndexVo">
        select
            COUNT(case when a.GROUP_CLASS = '主诊断组' then 1 else null end) mainDiagnoseCodeGroupNum,
            COUNT(case when a.GROUP_CLASS = '手术组' then 1 else null end) operationGroupNum,
            COUNT(case when a.GROUP_CLASS = '项目组' then 1 else null end) projectGroupNum,
            COUNT(case when a.GROUP_CLASS = '放疗组' then 1 else null end) radioGroupNum,
            COUNT(case when a.GROUP_CLASS = '化疗组' then 1 else null end) chemotherapyGroupNum,
            COUNT(case when a.GROUP_CLASS = '年龄组' then 1 else null end) ageGroupNum,
            COUNT(case when a.GROUP_CLASS = '年龄并发症手术组' then 1 else null end) ageComplicationOprGroupNum,
            COUNT(case when a.GROUP_CLASS = '年龄手术组' then 1 else null end) ageOperationGroupNum,
            COUNT(case when a.GROUP_CLASS = '国产介入组' then 1 else null end) demesticGroupNum,
            COUNT(case when a.GROUP_CLASS = '进口介入组' then 1 else null end) importGroupNum,
            COUNT(case when a.GROUP_CLASS = '康复组' then 1 else null end) recoverGroupNum,
            COUNT(case when a.GROUP_CLASS = '其他组' then 1 else null end) otherGroupNum,
            COUNT(case when a.grp_stas = '1' then 1 else null end) AS ppsInGroupNum
        from sts_pps_group_record a
        where 1 = 1
        <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
            AND  a.HOSPITAL_ID = #{queryParam.hospitalId}
        </if>
        <if test="queryParam.year!=null and queryParam.year!=''">
            <![CDATA[
                  AND (SUBSTR(a.dscg_time,1,4) = #{queryParam.year}
                  OR SUBSTR(a.dscg_time,1,4) = #{queryParam.year}-1
                  OR SUBSTR(a.dscg_time,1,4) = #{queryParam.year}-2
                  OR SUBSTR(a.dscg_time,1,4) = #{queryParam.year}-3
                  )
            ]]>
        </if>
        <if test="queryParam.year==null or queryParam.year==''">
            <![CDATA[
                  AND (SUBSTR(a.dscg_time,1,4) = DATE_FORMAT(NOW(), '%Y')
                  OR SUBSTR(a.dscg_time,1,4) = DATE_FORMAT(NOW(), '%Y')-1
                  OR SUBSTR(a.dscg_time,1,4) = DATE_FORMAT(NOW(), '%Y')-2
                  OR SUBSTR(a.dscg_time,1,4) = DATE_FORMAT(NOW(), '%Y')-3
                  )
            ]]>
        </if>
    </select>

</mapper>