<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.som.dao.costControl.PpsInGroupStatisticDao">
    <select id="list" resultType="com.my.som.vo.costControl.PpsInGroupStatisticVo">
        select
            a.dscg_caty_codg_inhosp AS priOutHosDeptCode,
            IFNULL(a.dscg_caty_name_inhosp,'未填写科室') AS priOutHosDeptName,
            IFNULL(ROUND(NULLIF(sum(case when a.grp_stas = '1' then 1 else 0 end),0) /
              NULLIF(count(1),0) * 100,2),0) AS inGroupRate,
            IFNULL(count(distinct (case when a.grp_stas = '1' then a.dis_gp_codg else null end)),0) AS ppsGroupNum,
            COUNT(1) AS balanceMedicalRecordNum,
            COUNT(case when a.grp_stas = '1' then 1 else null end) AS groupNum,
            COUNT(case when a.grp_stas != '1' then 1 else null end) AS noGroupNum,
            COUNT(case when a.grp_stas != '1' and a.grp_fale_rea='主要诊断为空' then 1 else null end) AS noGroupByEmptyIcdNum,
            COUNT(case when a.grp_stas != '1' and a.grp_fale_rea='无分组方案' then 1 else null end) AS noGroupByNoIcdNum,
            COUNT(case when a.grp_stas != '1' and a.grp_fale_rea='主要诊断填写不规范' then 1 else null end) AS noGroupByErrorIcdNum,
            COUNT(case when a.GROUP_CLASS = '主诊断组' then 1 else null end) mainDiagnoseCodeGroupNum,
            COUNT(case when a.GROUP_CLASS = '手术组' then 1 else null end) operationGroupNum,
            COUNT(case when a.GROUP_CLASS = '项目组' then 1 else null end) projectGroupNum,
            COUNT(case when a.GROUP_CLASS = '放疗组' then 1 else null end) radioGroupNum,
            COUNT(case when a.GROUP_CLASS = '化疗组' then 1 else null end) chemotherapyGroupNum,
            COUNT(case when a.GROUP_CLASS = '年龄组' then 1 else null end) ageGroupNum,
            COUNT(case when a.GROUP_CLASS = '年龄并发症手术组' then 1 else null end) ageComplicationOprGroupNum,
            COUNT(case when a.GROUP_CLASS = '年龄手术组' then 1 else null end) ageOperationGroupNum,
            COUNT(case when a.GROUP_CLASS = '国产介入组' then 1 else null end) demesticGroupNum,
            COUNT(case when a.GROUP_CLASS = '进口介入组' then 1 else null end) importGroupNum,
            COUNT(case when a.GROUP_CLASS = '康复组' then 1 else null end) recoverGroupNum,
            COUNT(case when a.GROUP_CLASS = '其他组' then 1 else null end) otherGroupNum
        from sts_pps_group_record a
        where 1 = 1
        <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
            AND  a.HOSPITAL_ID = #{queryParam.hospitalId}
        </if>
        <if test="queryParam.b16c!=null and queryParam.b16c!=''">
            AND a.dscg_caty_codg_inhosp = #{queryParam.b16c}
        </if>
        <if test="queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and
        queryParam.cy_end_date!=null and queryParam.cy_end_date!=''">
            AND a.dscg_time BETWEEN #{queryParam.cy_start_date} and #{queryParam.cy_end_date}
        </if>
        GROUP BY a.dscg_caty_codg_inhosp,a.dscg_caty_name_inhosp
        ORDER BY inGroupRate desc
    </select>

    <select id="getCount" resultType="com.my.som.vo.costControl.PpsInGroupCountVo">
        select
          x.*,
          y.medicalRecordNum as medicalRecordNum
        from (
            select
                IFNULL(count(distinct (case when a.grp_stas = '1' then a.dis_gp_codg else null end)),0) AS ppsGroupNum,
                IFNULL(ROUND(NULLIF(sum(case when a.grp_stas = '1' then 1 else 0 end),0) /
                    NULLIF(count(1),0) * 100,2),0) AS inGroupRate,
                COUNT(1) AS balanceMedicalRecordNum,
                COUNT(case when a.grp_stas = '1' then 1 else null end) AS ppsInGroupNum,
                COUNT(case when a.grp_stas != '1' then 1 else null end) AS ppsNotInGroupNum
            from sts_pps_group_record a
            where 1 = 1
            <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
                AND  a.HOSPITAL_ID = #{queryParam.hospitalId}
            </if>
            <if test="queryParam.b16c!=null and queryParam.b16c!=''">
                AND a.dscg_caty_codg_inhosp = #{queryParam.b16c}
            </if>
            <if test="queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and
            queryParam.cy_end_date!=null and queryParam.cy_end_date!=''">
                AND a.dscg_time BETWEEN #{queryParam.cy_start_date} and #{queryParam.cy_end_date}
            </if>
        )x cross join
        (
            select
              count(1) as medicalRecordNum
            from som_drg_grp_info
            where 1 = 1
            <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
                AND  HOSPITAL_ID = #{queryParam.hospitalId}
            </if>
            <if test="queryParam.b16c!=null and queryParam.b16c!=''">
                AND dscg_caty_codg_inhosp = #{queryParam.b16c}
            </if>
            <if test="queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and
            queryParam.cy_end_date!=null and queryParam.cy_end_date!=''">
                AND dscg_time BETWEEN #{queryParam.cy_start_date} and #{queryParam.cy_end_date}
            </if>
        )y
    </select>

    <select id="getInGroupIndex" resultType="com.my.som.vo.costControl.PpsInGroupIndexVo">
        select
            COUNT(case when a.GROUP_CLASS = '主诊断组' then 1 else null end) mainDiagnoseCodeGroupNum,
            COUNT(case when a.GROUP_CLASS = '手术组' then 1 else null end) operationGroupNum,
            COUNT(case when a.GROUP_CLASS = '项目组' then 1 else null end) projectGroupNum,
            COUNT(case when a.GROUP_CLASS = '放疗组' then 1 else null end) radioGroupNum,
            COUNT(case when a.GROUP_CLASS = '化疗组' then 1 else null end) chemotherapyGroupNum,
            COUNT(case when a.GROUP_CLASS = '年龄组' then 1 else null end) ageGroupNum,
            COUNT(case when a.GROUP_CLASS = '年龄并发症手术组' then 1 else null end) ageComplicationOprGroupNum,
            COUNT(case when a.GROUP_CLASS = '年龄手术组' then 1 else null end) ageOperationGroupNum,
            COUNT(case when a.GROUP_CLASS = '国产介入组' then 1 else null end) demesticGroupNum,
            COUNT(case when a.GROUP_CLASS = '进口介入组' then 1 else null end) importGroupNum,
            COUNT(case when a.GROUP_CLASS = '康复组' then 1 else null end) recoverGroupNum,
            COUNT(case when a.GROUP_CLASS = '其他组' then 1 else null end) otherGroupNum,
            COUNT(case when a.grp_stas = '1' then 1 else null end) AS ppsInGroupNum
        from sts_pps_group_record a
        where 1 = 1
        <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
            AND  a.HOSPITAL_ID = #{queryParam.hospitalId}
        </if>
        <if test="queryParam.b16c!=null and queryParam.b16c!=''">
            AND a.dscg_caty_codg_inhosp = #{queryParam.b16c}
        </if>
        <if test="queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and
        queryParam.cy_end_date!=null and queryParam.cy_end_date!=''">
            AND a.dscg_time BETWEEN #{queryParam.cy_start_date} and #{queryParam.cy_end_date}
        </if>
    </select>

    <select id="getNotInPpsGroup" resultType="com.my.som.vo.costControl.NotInPpsGroupCountVo">
        select
            '主要诊断为空' AS notInPpsGroupReason,
            COUNT(1) AS medcasVal
        from sts_pps_group_record a
        where a.grp_stas != '1' and a.grp_fale_rea ='主要诊断为空'
        <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
            AND  a.HOSPITAL_ID = #{queryParam.hospitalId}
        </if>
        <if test="queryParam.b16c!=null and queryParam.b16c!=''">
            AND a.dscg_caty_codg_inhosp = #{queryParam.b16c}
        </if>
        <if test="queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and
        queryParam.cy_end_date!=null and queryParam.cy_end_date!=''">
            AND a.dscg_time BETWEEN #{queryParam.cy_start_date} and #{queryParam.cy_end_date}
        </if>
        union all
        select
            '无分组方案' AS notInPpsGroupReason,
            COUNT(1) AS medcasVal
        from sts_pps_group_record a
        where a.grp_stas != '1' and a.grp_fale_rea ='无分组方案'
        <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
            AND  a.HOSPITAL_ID = #{queryParam.hospitalId}
        </if>
        <if test="queryParam.b16c!=null and queryParam.b16c!=''">
            AND a.dscg_caty_codg_inhosp = #{queryParam.b16c}
        </if>
        <if test="queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and
        queryParam.cy_end_date!=null and queryParam.cy_end_date!=''">
            AND a.dscg_time BETWEEN #{queryParam.cy_start_date} and #{queryParam.cy_end_date}
        </if>
        union all
        select
            '主要诊断填写不规范' AS notInPpsGroupReason,
            COUNT(1) AS medcasVal
        from sts_pps_group_record a
        where a.grp_stas != '1' and a.grp_fale_rea ='主要诊断填写不规范'
        <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
            AND  a.HOSPITAL_ID = #{queryParam.hospitalId}
        </if>
        <if test="queryParam.b16c!=null and queryParam.b16c!=''">
            AND a.dscg_caty_codg_inhosp = #{queryParam.b16c}
        </if>
        <if test="queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and
        queryParam.cy_end_date!=null and queryParam.cy_end_date!=''">
            AND a.dscg_time BETWEEN #{queryParam.cy_start_date} and #{queryParam.cy_end_date}
        </if>
    </select>

</mapper>