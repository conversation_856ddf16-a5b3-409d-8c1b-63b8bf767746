<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.som.dao.costControl.PpsDoctorDao">
    <select id="list" resultType="com.my.som.vo.costControl.PpsDoctorIndexVo">
        select
            <if test="queryParam.queryType==1">
                x.dscg_caty_codg_inhosp AS priOutHosDeptCode,
                x.dscg_caty_name_inhosp AS priOutHosDeptName,
            </if>
            x.drCodg AS drCodg,
            x.drName AS drName,
            <if test="queryParam.queryType==2">
                group_concat(distinct x.dscg_caty_name_inhosp) AS doctorDepts,
            </if>
            IFNULL(COUNT(1),0) AS balanceMedicalRecordNum,
            IFNULL(COUNT(case when x.grp_stas = '1' then 1 else null end),0) AS groupNum,
            IFNULL(COUNT(case when x.grp_stas != '1' then 1 else null end),0) AS noGroupNum,
            IFNULL(count(distinct (case when x.grp_stas = '1' then x.dis_gp_codg else null end)),0) AS ppsGroupNum,
            IFNULL(ROUND(IFNULL(SUM(x.cd_grp_wt),0),2),0) AS totalPpsGroupWeight, <!--总权重-->
            IFNULL(ROUND(AVG(x.act_ipt), 2),0) AS avgDays, <!--平均住院日-->
            IFNULL(ROUND(AVG(case when x.grp_stas = '1' then x.act_ipt else null end), 2),0) AS inGroupAvgDays, <!--入组平均住院日-->
            IFNULL(ROUND(AVG(x.ipt_sumfee), 2),0) AS avgCost, <!--平均住院费用-->
            IFNULL(ROUND(AVG(case when x.grp_stas = '1' then x.ipt_sumfee else null end), 2),0) AS inGroupAvgCost,<!--入组平均住院费用-->
            IFNULL(ROUND(IFNULL(sum(IFNULL(x.act_ipt,0)/NULLIF(x.standard_ave_hosp_day, 0))/COUNT(1),0),2),0) as timeIndex, <!--  时间消耗指数   -->
            IFNULL(ROUND(IFNULL(sum(IFNULL(x.ipt_sumfee,0)/NULLIF(x.standard_avg_fee, 0))/COUNT(1),0),2),0) as costIndex,  <!--  费用消耗指数   -->
            IFNULL(CONCAT(ROUND(SUM(x.drugfee)/NULLIF(SUM(x.ipt_sumfee),0)*100,2),'%'),0) AS medicalCostRate,<!--  药品费占比   -->
            IFNULL(CONCAT(ROUND(SUM(x.mcs_fee)/NULLIF(SUM(x.ipt_sumfee),0)*100,2),'%'),0) AS materialCostRate <!--  耗材费占比   -->
        from
        (
            select
                <if test="queryParam.doctorType!=null and queryParam.doctorType!=''">
                    (CASE #{queryParam.doctorType}#
                    WHEN 'dct0' THEN e.ipdr_code
                    WHEN 'dct1' THEN e.atddr_code
                    WHEN 'dct2' THEN e.deptdrt_code
                    WHEN 'dct3' THEN e.chfdr_code
                    WHEN 'dct4' THEN e.train_dr_code
                    WHEN 'dct5' THEN e.qltctrl_dr_code
                    WHEN 'dct6' THEN e.intn_dr
                    ELSE  e.ipdr_name
                    END) AS drCodg,
                    (CASE #{queryParam.doctorType}#
                    WHEN 'dct0' THEN e.ipdr_name
                    WHEN 'dct1' THEN e.atddr_name
                    WHEN 'dct2' THEN e.deptdrt_name
                    WHEN 'dct3' THEN e.chfdr_name
                    WHEN 'dct4' THEN e.train_dr_name
                    WHEN 'dct5' THEN e.qltctrl_dr_name
                    WHEN 'dct6' THEN e.intn_dr
                    ELSE  e.ipdr_name
                    END) AS drName,
                </if>
                <if test="queryParam.doctorType==null or queryParam.doctorType==''">
                    c.drCodg as drCodg,
                    c.drName as drName,
                </if>
                a.dscg_caty_codg_inhosp as dscg_caty_codg_inhosp,
                a.dscg_caty_name_inhosp AS dscg_caty_name_inhosp,
                a.grp_stas as grp_stas,
                a.dis_gp_codg as dis_gp_codg,
                f.cd_grp_wt as cd_grp_wt,
                e.act_ipt as act_ipt,
                e.ipt_sumfee as ipt_sumfee,
                f.standard_ave_hosp_day as standard_ave_hosp_day,
                f.standard_avg_fee as standard_avg_fee,
                e.drugfee as drugfee,
                e.mcs_fee as mcs_fee
            from sts_pps_group_record a
            left join som_drg_grp_info e on a.SETTLE_LIST_ID = e.SETTLE_LIST_ID
            left join som_cd_standard_info f on a.dis_gp_codg=f.dis_gp_codg AND f.hosp_lv = #{queryParam.hospLv}
            <if test="queryParam.doctorType==null or queryParam.doctorType==''">
                inner join (
                    select
                        b.settle_list_id as settle_list_id,
                        b.drCodg as drCodg,
                        b.drName as drName
                    from (
                    select
                    SETTLE_LIST_ID as settle_list_id,
                    ipdr_code as drCodg,
                    ipdr_name as drName
                    FROM som_drg_grp_info
                    where ipdr_name is not null and ipdr_name !='-' and ipdr_name !='--'
                    <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
                        AND  HOSPITAL_ID = #{queryParam.hospitalId}
                    </if>
                    <if test="queryParam.b16c!=null and queryParam.b16c!=''">
                        AND `PRI_OUTHOS_DEPT_CODE` = #{queryParam.b16c}
                    </if>
                    <if test="queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and queryParam.cy_end_date!=null and queryParam.cy_end_date!=''">
                        AND dscg_time BETWEEN #{queryParam.cy_start_date} and #{queryParam.cy_end_date}
                    </if>
                    <if test="queryParam.drCodg!=null and queryParam.drCodg!=''">
                        AND `INHOS_DOCTOR_CODE` = #{queryParam.drCodg}
                    </if>
                    union all
                    select
                    SETTLE_LIST_ID as settle_list_id,
                    atddr_code as drCodg,
                    atddr_name as drName
                    FROM som_drg_grp_info
                    where atddr_name is not null and atddr_name !='-' and atddr_name !='--'
                    <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
                        AND  HOSPITAL_ID = #{queryParam.hospitalId}
                    </if>
                    <if test="queryParam.b16c!=null and queryParam.b16c!=''">
                        AND `PRI_OUTHOS_DEPT_CODE` = #{queryParam.b16c}
                    </if>
                    <if test="queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and queryParam.cy_end_date!=null and queryParam.cy_end_date!=''">
                        AND dscg_time BETWEEN #{queryParam.cy_start_date} and #{queryParam.cy_end_date}
                    </if>
                    <if test="queryParam.drCodg!=null and queryParam.drCodg!=''">
                        AND `ATTENDING_DOCTOR_CODE` = #{queryParam.drCodg}
                    </if>
                    union all
                    select
                    SETTLE_LIST_ID as settle_list_id,
                    deptdrt_code as drCodg,
                    deptdrt_name as drName
                    FROM som_drg_grp_info
                    where deptdrt_name is not null and deptdrt_name !='-' and deptdrt_name !='--'
                    <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
                        AND  HOSPITAL_ID = #{queryParam.hospitalId}
                    </if>
                    <if test="queryParam.b16c!=null and queryParam.b16c!=''">
                        AND `PRI_OUTHOS_DEPT_CODE` = #{queryParam.b16c}
                    </if>
                    <if test="queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and queryParam.cy_end_date!=null and queryParam.cy_end_date!=''">
                        AND dscg_time BETWEEN #{queryParam.cy_start_date} and #{queryParam.cy_end_date}
                    </if>
                    <if test="queryParam.drCodg!=null and queryParam.drCodg!=''">
                        AND `DEPT_DIRECTOR_CODE` = #{queryParam.drCodg}
                    </if>
                    union all
                    select
                    SETTLE_LIST_ID as settle_list_id,
                    chfdr_code as drCodg,
                    chfdr_name as drName
                    FROM
                    som_drg_grp_info
                    where chfdr_name is not null and chfdr_name !='-' and chfdr_name !='--'
                    <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
                        AND  HOSPITAL_ID = #{queryParam.hospitalId}
                    </if>
                    <if test="queryParam.b16c!=null and queryParam.b16c!=''">
                        AND `PRI_OUTHOS_DEPT_CODE` = #{queryParam.b16c}
                    </if>
                    <if test="queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and
                                                            queryParam.cy_end_date!=null and queryParam.cy_end_date!=''">
                        AND dscg_time BETWEEN #{queryParam.cy_start_date} and #{queryParam.cy_end_date}
                    </if>
                    <if test="queryParam.drCodg!=null and queryParam.drCodg!=''">
                        AND `MEDICAL_DIRECTOR_CODE` = #{queryParam.drCodg}
                    </if>
                    union all
                    select
                    SETTLE_LIST_ID as settle_list_id,
                    train_dr_code as drCodg,
                    train_dr_name as drName
                    FROM som_drg_grp_info
                    where train_dr_name is not null and train_dr_name !='-' and train_dr_name !='--'
                    <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
                        AND  HOSPITAL_ID = #{queryParam.hospitalId}
                    </if>
                    <if test="queryParam.b16c!=null and queryParam.b16c!=''">
                        AND `PRI_OUTHOS_DEPT_CODE` = #{queryParam.b16c}
                    </if>
                    <if test="queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and
                                                queryParam.cy_end_date!=null and queryParam.cy_end_date!=''">
                        AND dscg_time BETWEEN #{queryParam.cy_start_date} and #{queryParam.cy_end_date}
                    </if>
                    <if test="queryParam.drCodg!=null and queryParam.drCodg!=''">
                        AND `REFRESHER_DOCTOR_CODE` = #{queryParam.drCodg}
                    </if>
                    union all
                    select
                    SETTLE_LIST_ID as settle_list_id,
                    qltctrl_dr_code as drCodg,
                    qltctrl_dr_name as drName
                    FROM som_drg_grp_info
                    where qltctrl_dr_name is not null and qltctrl_dr_name !='-' and qltctrl_dr_name !='--'
                    <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
                        AND  HOSPITAL_ID = #{queryParam.hospitalId}
                    </if>
                    <if test="queryParam.b16c!=null and queryParam.b16c!=''">
                        AND `PRI_OUTHOS_DEPT_CODE` = #{queryParam.b16c}
                    </if>
                    <if test="queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and
                                                    queryParam.cy_end_date!=null and queryParam.cy_end_date!=''">
                        AND dscg_time BETWEEN #{queryParam.cy_start_date} and #{queryParam.cy_end_date}
                    </if>
                    <if test="queryParam.drCodg!=null and queryParam.drCodg!=''">
                        AND `QUALITY_DOCTOR_CODE` = #{queryParam.drCodg}
                    </if>
                    union all
                    select
                    SETTLE_LIST_ID as settle_list_id,
                    intn_dr as drCodg,
                    intn_dr as drName
                    FROM som_drg_grp_info
                    where intn_dr is not null and intn_dr !='-' and intn_dr !='--'
                    <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
                        AND  HOSPITAL_ID = #{queryParam.hospitalId}
                    </if>
                    <if test="queryParam.b16c!=null and queryParam.b16c!=''">
                        AND `PRI_OUTHOS_DEPT_CODE` = #{queryParam.b16c}
                    </if>
                    <if test="queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and
                                                        queryParam.cy_end_date!=null and queryParam.cy_end_date!=''">
                        AND dscg_time BETWEEN #{queryParam.cy_start_date} and #{queryParam.cy_end_date}
                    </if>
                    <if test="queryParam.drCodg!=null and queryParam.drCodg!=''">
                        AND `INTERN_DOCTOR_NAME` = #{queryParam.drCodg}
                    </if>
                    ) b group by settle_list_id,drCodg,drName
                ) c on e.settle_list_id = c.settle_list_id
            </if>
            where 1=1
            <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
                AND  a.HOSPITAL_ID = #{queryParam.hospitalId}
            </if>
            <if test="queryParam.b16c!=null and queryParam.b16c!=''">
                AND a.dscg_caty_codg_inhosp = #{queryParam.b16c}
            </if>
            <if test="queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and
                                                        queryParam.cy_end_date!=null and queryParam.cy_end_date!=''">
                AND a.dscg_time BETWEEN #{queryParam.cy_start_date} and #{queryParam.cy_end_date}
            </if>
        ) x
        where 1 = 1
        <if test="queryParam.drCodg!=null and queryParam.drCodg!=''">
            AND x.drCodg= #{queryParam.drCodg}
        </if>
        <if test="queryParam.queryType==1">
            GROUP BY x.dscg_caty_codg_inhosp,x.dscg_caty_name_inhosp,x.drCodg,x.drName
        </if>
        <if test="queryParam.queryType==2">
            GROUP BY x.drCodg,x.drName
        </if>
        ORDER BY totalPpsGroupWeight desc
    </select>

    <select id="getCountInfo" resultType="com.my.som.vo.costControl.PpsDoctorCountVo">
        select
            x.drCodg AS drCodg,
            x.drName AS drName,
            IFNULL(count(1),0) AS totalBalanceMedicalRecordNum,
            IFNULL(ROUND(count(1) / NULLIF(y.totalMedicalNum,0)*100,2),0) AS totalBalanceMedicalRecordNumRate,
            IFNULL(count(case when x.grp_stas = '1' then 1 else null end),0)   AS inGroupMedicalNum,
            IFNULL(ROUND(count(case when x.grp_stas = '1' then 1 else null end) /
              NULLIF(y.inGroupMedicalNum,0)*100,2),0) AS  inGroupMedicalNumRate,
            IFNULL(count(case when x.grp_stas != '1' then 1 else null end),0)  AS notGroupMedicalNum,
            IFNULL(ROUND(count(case when x.grp_stas != '1' then 1 else null end) /
              NULLIF(y.inGroupMedicalNum,0)*100,2),0) AS  notGroupMedicalNumRate,
            IFNULL(count(distinct (case when x.grp_stas = '1' then x.dis_gp_codg else null end)),0)   AS ppsGroupNum,
            IFNULL(ROUND(count(distinct (case when x.grp_stas = '1' then x.dis_gp_codg else null end)) /
              NULLIF(y.ppsGroupNum,0)*100,2),0) AS  ppsGroupNumRate,
            IFNULL(SUM(CASE WHEN x.grp_stas = '1' THEN x.cd_grp_wt ELSE NULL END),0)    AS totalPpsGroupWeight,
            IFNULL(ROUND(SUM(CASE WHEN x.grp_stas = '1' THEN x.cd_grp_wt ELSE NULL END) /
                NULLIF(y.totalPpsGroupWeight,0)*100,2),0) AS  totalPpsGroupWeightRate,
            IFNULL(ROUND(AVG(x.act_ipt), 2),0)   AS avgDays,
            y.hosAvgDays as hosAvgDays,
            IFNULL(ROUND(AVG(CASE WHEN x.grp_stas = '1' THEN x.act_ipt ELSE NULL END), 2),0)   AS inGroupAvgDays,
            y.hosInGroupAvgDays as hosInGroupAvgDays,
            IFNULL(ROUND(AVG(x.ipt_sumfee), 2),0)  AS avgCost,
            y.hosAvgCost as hosAvgCost,
            IFNULL(ROUND(AVG(CASE WHEN x.grp_stas = '1' THEN x.ipt_sumfee ELSE NULL END), 2),0)    AS inGroupAvgCost,
            y.hosInGroupAvgCost as hosInGroupAvgCost,
            IFNULL(ROUND(SUM(CASE WHEN x.grp_stas = '1' THEN x.act_ipt ELSE NULL END / NULLIF(x.standard_ave_hosp_day,0)) /
            NULLIF(COUNT(CASE WHEN x.grp_stas = '1' THEN 1 ELSE NULL END),0), 2),0)  AS timeIndex,
            y.hosTimeIndex as hosTimeIndex,
            IFNULL(ROUND(SUM(CASE WHEN x.grp_stas = '1' THEN x.ipt_sumfee ELSE NULL END / NULLIF(x.standard_avg_fee,0)) /
            NULLIF(COUNT(CASE WHEN x.grp_stas = '1' THEN 1 ELSE NULL END),0), 2),0)   AS costIndex,
            y.hosCostIndex as hosCostIndex,
            IFNULL(ROUND(AVG(x.drugfee), 2),0)   AS avgDrugFee,
            y.hosAvgMedicalCost as hosAvgMedicalCost,
            IFNULL(ROUND(AVG(x.mcs_fee), 2),0)   AS avgMcsFee,
            y.hosAvgMaterialCost as hosAvgMaterialCost
        from (
        select
        <if test="queryParam.doctorType!=null and queryParam.doctorType!=''">
            (CASE #{queryParam.doctorType}#
            WHEN 'dct0' THEN e.ipdr_code
            WHEN 'dct1' THEN e.atddr_code
            WHEN 'dct2' THEN e.deptdrt_code
            WHEN 'dct3' THEN e.chfdr_code
            WHEN 'dct4' THEN e.train_dr_code
            WHEN 'dct5' THEN e.qltctrl_dr_code
            WHEN 'dct6' THEN e.intn_dr
            ELSE  e.ipdr_name
            END) AS drCodg,
            (CASE #{queryParam.doctorType}#
            WHEN 'dct0' THEN e.ipdr_name
            WHEN 'dct1' THEN e.atddr_name
            WHEN 'dct2' THEN e.deptdrt_name
            WHEN 'dct3' THEN e.chfdr_name
            WHEN 'dct4' THEN e.train_dr_name
            WHEN 'dct5' THEN e.qltctrl_dr_name
            WHEN 'dct6' THEN e.intn_dr
            ELSE  e.ipdr_name
            END) AS drName,
        </if>
        <if test="queryParam.doctorType==null or queryParam.doctorType==''">
            c.drCodg as drCodg,
            c.drName as drName,
        </if>
        a.dscg_caty_codg_inhosp as dscg_caty_codg_inhosp,
        a.dscg_caty_name_inhosp AS dscg_caty_name_inhosp,
        a.grp_stas as grp_stas,
        a.dis_gp_codg as dis_gp_codg,
        f.cd_grp_wt as cd_grp_wt,
        e.act_ipt as act_ipt,
        e.ipt_sumfee as ipt_sumfee,
        f.standard_ave_hosp_day as standard_ave_hosp_day,
        f.standard_avg_fee as standard_avg_fee,
        e.drugfee as drugfee,
        e.mcs_fee as mcs_fee
        from sts_pps_group_record a
        left join som_drg_grp_info e on a.SETTLE_LIST_ID = e.SETTLE_LIST_ID
        left join som_cd_standard_info f on a.dis_gp_codg=f.dis_gp_codg AND f.hosp_lv = #{queryParam.hospLv}
        <if test="queryParam.doctorType==null or queryParam.doctorType==''">
            inner join (
            select
            b.settle_list_id as settle_list_id,
            b.drCodg as drCodg,
            b.drName as drName
            from (
            select
            SETTLE_LIST_ID as settle_list_id,
            ipdr_code as drCodg,
            ipdr_name as drName
            FROM som_drg_grp_info
            where ipdr_name is not null and ipdr_name !='-' and ipdr_name !='--'
            <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
                AND  HOSPITAL_ID = #{queryParam.hospitalId}
            </if>
            <if test="queryParam.b16c!=null and queryParam.b16c!=''">
                AND `PRI_OUTHOS_DEPT_CODE` = #{queryParam.b16c}
            </if>
            <if test="queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and queryParam.cy_end_date!=null and queryParam.cy_end_date!=''">
                AND dscg_time BETWEEN #{queryParam.cy_start_date} and #{queryParam.cy_end_date}
            </if>
            <if test="queryParam.drCodg!=null and queryParam.drCodg!=''">
                AND `INHOS_DOCTOR_CODE` = #{queryParam.drCodg}
            </if>
            union all
            select
            SETTLE_LIST_ID as settle_list_id,
            atddr_code as drCodg,
            atddr_name as drName
            FROM som_drg_grp_info
            where atddr_name is not null and atddr_name !='-' and atddr_name !='--'
            <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
                AND  HOSPITAL_ID = #{queryParam.hospitalId}
            </if>
            <if test="queryParam.b16c!=null and queryParam.b16c!=''">
                AND `PRI_OUTHOS_DEPT_CODE` = #{queryParam.b16c}
            </if>
            <if test="queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and queryParam.cy_end_date!=null and queryParam.cy_end_date!=''">
                AND dscg_time BETWEEN #{queryParam.cy_start_date} and #{queryParam.cy_end_date}
            </if>
            <if test="queryParam.drCodg!=null and queryParam.drCodg!=''">
                AND `ATTENDING_DOCTOR_CODE` = #{queryParam.drCodg}
            </if>
            union all
            select
            SETTLE_LIST_ID as settle_list_id,
            deptdrt_code as drCodg,
            deptdrt_name as drName
            FROM som_drg_grp_info
            where deptdrt_name is not null and deptdrt_name !='-' and deptdrt_name !='--'
            <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
                AND  HOSPITAL_ID = #{queryParam.hospitalId}
            </if>
            <if test="queryParam.b16c!=null and queryParam.b16c!=''">
                AND `PRI_OUTHOS_DEPT_CODE` = #{queryParam.b16c}
            </if>
            <if test="queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and queryParam.cy_end_date!=null and queryParam.cy_end_date!=''">
                AND dscg_time BETWEEN #{queryParam.cy_start_date} and #{queryParam.cy_end_date}
            </if>
            <if test="queryParam.drCodg!=null and queryParam.drCodg!=''">
                AND `DEPT_DIRECTOR_CODE` = #{queryParam.drCodg}
            </if>
            union all
            select
            SETTLE_LIST_ID as settle_list_id,
            chfdr_code as drCodg,
            chfdr_name as drName
            FROM
            som_drg_grp_info
            where chfdr_name is not null and chfdr_name !='-' and chfdr_name !='--'
            <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
                AND  HOSPITAL_ID = #{queryParam.hospitalId}
            </if>
            <if test="queryParam.b16c!=null and queryParam.b16c!=''">
                AND `PRI_OUTHOS_DEPT_CODE` = #{queryParam.b16c}
            </if>
            <if test="queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and
                                                            queryParam.cy_end_date!=null and queryParam.cy_end_date!=''">
                AND dscg_time BETWEEN #{queryParam.cy_start_date} and #{queryParam.cy_end_date}
            </if>
            <if test="queryParam.drCodg!=null and queryParam.drCodg!=''">
                AND `MEDICAL_DIRECTOR_CODE` = #{queryParam.drCodg}
            </if>
            union all
            select
            SETTLE_LIST_ID as settle_list_id,
            train_dr_code as drCodg,
            train_dr_name as drName
            FROM som_drg_grp_info
            where train_dr_name is not null and train_dr_name !='-' and train_dr_name !='--'
            <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
                AND  HOSPITAL_ID = #{queryParam.hospitalId}
            </if>
            <if test="queryParam.b16c!=null and queryParam.b16c!=''">
                AND `PRI_OUTHOS_DEPT_CODE` = #{queryParam.b16c}
            </if>
            <if test="queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and
                                                queryParam.cy_end_date!=null and queryParam.cy_end_date!=''">
                AND dscg_time BETWEEN #{queryParam.cy_start_date} and #{queryParam.cy_end_date}
            </if>
            <if test="queryParam.drCodg!=null and queryParam.drCodg!=''">
                AND `REFRESHER_DOCTOR_CODE` = #{queryParam.drCodg}
            </if>
            union all
            select
            SETTLE_LIST_ID as settle_list_id,
            qltctrl_dr_code as drCodg,
            qltctrl_dr_name as drName
            FROM som_drg_grp_info
            where qltctrl_dr_name is not null and qltctrl_dr_name !='-' and qltctrl_dr_name !='--'
            <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
                AND  HOSPITAL_ID = #{queryParam.hospitalId}
            </if>
            <if test="queryParam.b16c!=null and queryParam.b16c!=''">
                AND `PRI_OUTHOS_DEPT_CODE` = #{queryParam.b16c}
            </if>
            <if test="queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and
                                                    queryParam.cy_end_date!=null and queryParam.cy_end_date!=''">
                AND dscg_time BETWEEN #{queryParam.cy_start_date} and #{queryParam.cy_end_date}
            </if>
            <if test="queryParam.drCodg!=null and queryParam.drCodg!=''">
                AND `QUALITY_DOCTOR_CODE` = #{queryParam.drCodg}
            </if>
            union all
            select
            SETTLE_LIST_ID as settle_list_id,
            intn_dr as drCodg,
            intn_dr as drName
            FROM som_drg_grp_info
            where intn_dr is not null and intn_dr !='-' and intn_dr !='--'
            <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
                AND  HOSPITAL_ID = #{queryParam.hospitalId}
            </if>
            <if test="queryParam.b16c!=null and queryParam.b16c!=''">
                AND `PRI_OUTHOS_DEPT_CODE` = #{queryParam.b16c}
            </if>
            <if test="queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and
                                                        queryParam.cy_end_date!=null and queryParam.cy_end_date!=''">
                AND dscg_time BETWEEN #{queryParam.cy_start_date} and #{queryParam.cy_end_date}
            </if>
            <if test="queryParam.drCodg!=null and queryParam.drCodg!=''">
                AND `INTERN_DOCTOR_NAME` = #{queryParam.drCodg}
            </if>
            ) b group by settle_list_id,drCodg,drName
            ) c on e.settle_list_id = c.settle_list_id
        </if>
        where 1=1
        <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
            AND  a.HOSPITAL_ID = #{queryParam.hospitalId}
        </if>
        <if test="queryParam.b16c!=null and queryParam.b16c!=''">
            AND a.dscg_caty_codg_inhosp = #{queryParam.b16c}
        </if>
        <if test="queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and
                                                        queryParam.cy_end_date!=null and queryParam.cy_end_date!=''">
            AND a.dscg_time BETWEEN #{queryParam.cy_start_date} and #{queryParam.cy_end_date}
        </if>
        ) x
        cross join (
            select
                IFNULL(count(1),0) AS totalMedicalNum,
                IFNULL(count(case when a.grp_stas = '1' then 1 else null end),0) AS inGroupMedicalNum,
                IFNULL(count(case when a.grp_stas != '1' then 1 else null end),0) AS notGroupMedicalNum,
                IFNULL(count(distinct (case when a.grp_stas = '1' then a.dis_gp_codg else null end)),0) AS ppsGroupNum,
                IFNULL(SUM(CASE WHEN a.grp_stas = '1' THEN c.cd_grp_wt ELSE NULL END),0) AS totalPpsGroupWeight,
                IFNULL(ROUND(AVG(b.act_ipt), 2),0) AS hosAvgDays,
                IFNULL(ROUND(AVG(CASE WHEN a.grp_stas = '1' THEN b.act_ipt ELSE NULL END), 2),0) AS hosInGroupAvgDays,
                IFNULL(ROUND(AVG(b.ipt_sumfee), 2),0) AS hosAvgCost,
                IFNULL(ROUND(AVG(CASE WHEN a.grp_stas = '1' THEN b.ipt_sumfee ELSE NULL END), 2),0) AS hosInGroupAvgCost,
                IFNULL(ROUND(SUM(CASE WHEN a.grp_stas = '1' THEN b.act_ipt ELSE NULL END / NULLIF(c.standard_ave_hosp_day,0))
                /NULLIF(COUNT(CASE WHEN a.grp_stas = '1' THEN 1 ELSE NULL END),0), 2),0) AS hosTimeIndex,
                IFNULL(ROUND(SUM(CASE WHEN a.grp_stas = '1' THEN b.ipt_sumfee ELSE NULL END /
                NULLIF(c.standard_avg_fee,0)) /NULLIF(COUNT(CASE WHEN a.grp_stas = '1' THEN 1 ELSE NULL END),0), 2),0) AS hosCostIndex,
                IFNULL(ROUND(AVG(b.drugfee), 2),0) AS hosAvgMedicalCost,
                IFNULL(ROUND(AVG(b.mcs_fee), 2),0)   AS hosAvgMaterialCost
            from sts_pps_group_record a
            left join som_drg_grp_info b on a.SETTLE_LIST_ID = b.SETTLE_LIST_ID
            left join som_cd_standard_info c on a.dis_gp_codg=c.dis_gp_codg AND c.hosp_lv = #{queryParam.hospLv}
            where 1 = 1
            <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
                AND  a.HOSPITAL_ID = #{queryParam.hospitalId}
            </if>
            <if test="queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and queryParam.cy_end_date!=null and queryParam.cy_end_date!=''">
                AND a.dscg_time BETWEEN #{queryParam.cy_start_date} and #{queryParam.cy_end_date}
            </if>
        ) y
        where 1 = 1
        <if test="queryParam.drCodg!=null and queryParam.drCodg!=''">
            AND x.drCodg= #{queryParam.drCodg}
        </if>
        <if test="queryParam.queryType==1">
            GROUP BY x.dscg_caty_codg_inhosp,x.dscg_caty_name_inhosp,x.drCodg,x.drName
        </if>
        <if test="queryParam.queryType==2">
            GROUP BY x.drCodg,x.drName
        </if>
        ORDER BY totalPpsGroupWeight desc
    </select>
</mapper>