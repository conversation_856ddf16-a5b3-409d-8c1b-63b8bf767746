<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.som.dao.costControl.CdDrgsPpsDao">
    <select id="getPpsGroupAndCostControl" resultType="com.my.som.vo.costControl.PpsGroupAndCostControlVo">
            select
                a.main_diag_dise_codg as mainDiagDiseCodg,
                a.dis_gp_codg AS ppsGroupCode,
                a.dis_gp_name AS ppsGroupName,
                COUNT(1) AS medcasVal,
                COUNT(DISTINCT(a.dscg_caty_codg_inhosp)) AS deptNum,
                IFNULL(ROUND(AVG(ipt_sumfee), 2),0) AS hosAvgCost,
                IFNULL(MAX(b.refer_avg_fee),0) as cityAvgCost,
                IFNULL(ROUND(AVG(act_ipt), 2),0) AS hosAvgDays,
                IFNULL(MAX(b.refer_time),0) as cityAvgDays,
                (case when MAX(b.refer_avg_fee) is null then '暂无该病种数据' else
                (CONCAT(ROUND(IFNULL(MAX(b.refer_avg_fee),0)*e.control_fee_stsb_min_prop,1),'-',ROUND(IFNULL(MAX(b.refer_avg_fee),0)*e.control_fee_stsb_max_prop,1)))
                end) as costControlRange,

                IFNULL(ROUND(SUM(ipt_sumfee_in_selfpay_amt),2),0) AS iptSumfeeInSelfpayAmt,
                IFNULL(ROUND(SUM(com_med_servfee),2),0) AS comMedServfee,
                IFNULL(ROUND(SUM(CHECK_COST1),2),0) AS checkCost1,
                IFNULL(ROUND(SUM(rhab_fee),2),0) AS rhabFee,
                IFNULL(ROUND(SUM(diag_fee),2),0) AS diagFee,
                IFNULL(ROUND(SUM(TREATMENT_COST1),2),0) AS treatmentCost1,
                IFNULL(ROUND(SUM(drugfee),2),0) AS drugfee,
                IFNULL(ROUND(SUM(abt_fee),2),0) AS abtFee,
                IFNULL(ROUND(SUM(blood_blo_pro),2),0) AS bloodBloPro,
                IFNULL(ROUND(SUM(mcs_fee),2),0) AS mcsFee,
                IFNULL(ROUND(SUM(OTHER_COST1),2),0) AS otherCost1,

                IFNULL(ROUND(SUM(BED_COST),2),0) AS bedCost,
                IFNULL(ROUND(SUM(DIAGNOSTIC_COST),2),0) AS diagnosticCost,
                IFNULL(ROUND(SUM(CHECK_COST2),2),0) AS checkCost2,
                IFNULL(ROUND(SUM(LABORATORY_COST),2),0) AS laboratoryCost,
                IFNULL(ROUND(SUM(TREATMENT_COST2),2),0) AS treatmentCost2,
                IFNULL(ROUND(SUM(OPERATIVE_COST),2),0) AS operativeCost,
                IFNULL(ROUND(SUM(CARE_COST),2),0) AS careCost,
                IFNULL(ROUND(SUM(HEATH_MATERIAL_COST),2),0) AS heathMaterialCost,
                IFNULL(ROUND(SUM(WEST_MEDICAL_COST),2),0) AS westMedicalCost,
                IFNULL(ROUND(SUM(CHINESE_PIECE_COST),2),0) AS chinesePieceCost,
                IFNULL(ROUND(SUM(CHINESE_MEDICAL_COST),2),0) AS chineseMedicalCost,
                IFNULL(ROUND(SUM(CONSULTATION_COST),2),0) AS consultationCost,
                IFNULL(ROUND(SUM(REGISTRATION_COST),2),0) AS registrationCost,
                IFNULL(ROUND(SUM(OTHER_COST2),2),0) AS otherCost2
            from sts_pps_group_record a
            LEFT JOIN som_cd_dise_val_pay_dis_gp b on a.dis_gp_name = b.dis_gp_name
            LEFT JOIN som_drg_grp_info c on a.SETTLE_LIST_ID = c.SETTLE_LIST_ID
            cross join som_cd_control_fee_stsb_cfg e
            where a.grp_stas = '1'
            <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
                AND  c.HOSPITAL_ID = #{queryParam.hospitalId}
            </if>
            <if test="queryParam.b16c!=null and queryParam.b16c!=''">
                AND ( a.dscg_caty_codg_inhosp = #{queryParam.b16c} or
                a.dscg_caty_name_inhosp = (select NAME from som_dept where TYPE='2' and CODE=#{queryParam.b16c} AND HOSPITAL_ID = #{queryParam.hospitalId} AND ACTIVE_FLAG='1')
                )
            </if>
            <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
                AND  c.HOSPITAL_ID = #{queryParam.hospitalId}
            </if>
            <if test="queryParam.queryPpsGroup!=null and queryParam.queryPpsGroup!=''">
                AND a.dis_gp_codg = #{queryParam.queryPpsGroup}
            </if>
            <if test="queryParam.drCodg!=null and queryParam.drCodg!=''">
                AND (
                c.deptdrt_code = #{queryParam.drCodg} OR
                c.chfdr_code = #{queryParam.drCodg} OR
                c.atddr_code = #{queryParam.drCodg} OR
                c.ipdr_code = #{queryParam.drCodg} OR
                c.train_dr_code = #{queryParam.drCodg} OR
                c.intn_dr = #{queryParam.drCodg} OR
                c.qltctrl_dr_code = #{queryParam.drCodg}
                )
            </if>
            <if test="queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and
            queryParam.cy_end_date!=null and queryParam.cy_end_date!=''">
                AND a.dscg_time BETWEEN #{queryParam.cy_start_date} and #{queryParam.cy_end_date}
            </if>
            GROUP BY a.main_diag_dise_codg,a.dis_gp_codg,a.dis_gp_name
    </select>

    <select id="list" resultType="com.my.som.vo.costControl.CdDrgsPpsMainInfo">
        select
            a.NAME AS name,
            a.gend AS gend,
            a.AGE AS age,
            c.psn_insu_type AS psnInsuType,
            case when c.adm_caty_name_inhosp is null then (select NAME from som_dept where TYPE='2' and CODE=c.adm_caty_codg_inhosp AND ACTIVE_FLAG='1')
            else c.adm_caty_name_inhosp end AS priInHosDeptName,
            a.dscg_caty_name_inhosp AS priOutHosDeptName,
            c.chfdr_name AS chfdrName,
            a.main_diag_dise_codg AS main_diag_dise_codg,
            a.main_diag_dise_name AS main_diag_dise_name,
            a.dis_gp_name ppsGroupName,
            (case when b.refer_avg_fee is null then '暂无该病种数据' else
            (CONCAT(ROUND(IFNULL(b.refer_avg_fee,0)*e.control_fee_stsb_min_prop,1),'-',ROUND(IFNULL(b.refer_avg_fee,0)*e.control_fee_stsb_max_prop,1)))
            end) as costControlRange,
            IFNULL(b.stand_val,0) AS stand_val,
            IFNULL(b.dif_cof,0) AS dif_cof,
            IFNULL(b.pre_pt_val,0) AS pre_pt_val,
            IFNULL(ROUND(c.ipt_sumfee,2),0) AS totalInHosCost,
            IFNULL(c.act_ipt,0) AS inHosDays,
            IFNULL(b.refer_avg_fee,0) AS refer_avg_fee,
            IFNULL(b.refer_time,0) AS refer_time,

            IFNULL(ROUND(ipt_sumfee_in_selfpay_amt,2),0) AS iptSumfeeInSelfpayAmt,
            IFNULL(ROUND(com_med_servfee,2),0) AS comMedServfee,
            IFNULL(ROUND(CHECK_COST1,2),0) AS checkCost1,
            IFNULL(ROUND(rhab_fee,2),0) AS rhabFee,
            IFNULL(ROUND(diag_fee,2),0) AS diagFee,
            IFNULL(ROUND(TREATMENT_COST1,2),0) AS treatmentCost1,
            IFNULL(ROUND(drugfee,2),0) AS drugfee,
            IFNULL(ROUND(abt_fee,2),0) AS abtFee,
            IFNULL(ROUND(blood_blo_pro,2),0) AS bloodBloPro,
            IFNULL(ROUND(mcs_fee,2),0) AS mcsFee,
            IFNULL(ROUND(OTHER_COST1,2),0) AS otherCost1,

            IFNULL(ROUND(BED_COST,2),0) AS bedCost,
            IFNULL(ROUND(DIAGNOSTIC_COST,2),0) AS diagnosticCost,
            IFNULL(ROUND(CHECK_COST2,2),0) AS checkCost2,
            IFNULL(ROUND(LABORATORY_COST,2),0) AS laboratoryCost,
            IFNULL(ROUND(TREATMENT_COST2,2),0) AS treatmentCost2,
            IFNULL(ROUND(OPERATIVE_COST,2),0) AS operativeCost,
            IFNULL(ROUND(CARE_COST,2),0) AS careCost,
            IFNULL(ROUND(HEATH_MATERIAL_COST,2),0) AS heathMaterialCost,
            IFNULL(ROUND(WEST_MEDICAL_COST,2),0) AS westMedicalCost,
            IFNULL(ROUND(CHINESE_PIECE_COST,2),0) AS chinesePieceCost,
            IFNULL(ROUND(CHINESE_MEDICAL_COST,2),0) AS chineseMedicalCost,
            IFNULL(ROUND(CONSULTATION_COST,2),0) AS consultationCost,
            IFNULL(ROUND(REGISTRATION_COST,2),0) AS registrationCost,
            IFNULL(ROUND(OTHER_COST2,2),0) AS otherCost2
        from sts_pps_group_record a
        LEFT JOIN som_cd_dise_val_pay_dis_gp b on a.dis_gp_name = b.dis_gp_name
        LEFT JOIN som_drg_grp_info c on a.SETTLE_LIST_ID = c.SETTLE_LIST_ID
        cross join som_cd_control_fee_stsb_cfg e
        where 1 = 1
        <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
            AND  c.HOSPITAL_ID = #{queryParam.hospitalId}
        </if>
        <if test="queryParam.b16c!=null and queryParam.b16c!=''">
            AND a.dscg_caty_codg_inhosp = #{queryParam.b16c}
        </if>
        <if test="queryParam.drCodg!=null and queryParam.drCodg!=''">
            AND (
            c.deptdrt_code = #{queryParam.drCodg} OR
            c.chfdr_code = #{queryParam.drCodg} OR
            c.atddr_code = #{queryParam.drCodg} OR
            c.ipdr_code = #{queryParam.drCodg} OR
            c.train_dr_code = #{queryParam.drCodg} OR
            c.intn_dr = #{queryParam.drCodg} OR
            c.qltctrl_dr_code = #{queryParam.drCodg}
            )
        </if>
        <if test="queryParam.queryPpsGroup!=null and queryParam.queryPpsGroup!=''">
            AND a.dis_gp_codg = #{queryParam.queryPpsGroup}
        </if>
        <if test="queryParam.a11!=null and queryParam.a11!=''">
            AND a.NAME = #{queryParam.a11}
        </if>
        <if test="queryParam.queryIcd!=null and queryParam.queryIcd!=''">
            AND a.main_diag_dise_codg = #{queryParam.queryIcd}
        </if>
        <if test="queryParam.grpStas!=null and queryParam.grpStas!=''">
            AND a.grp_stas = #{queryParam.grpStas}
        </if>
        <if test="queryParam.grpFaleRea!=null and queryParam.grpFaleRea!=''">
            AND a.grp_fale_rea = #{queryParam.grpFaleRea}
        </if>
        <if test="queryParam.groupClass!=null and queryParam.groupClass!=''">
            AND a.GROUP_CLASS = #{queryParam.groupClass}
        </if>
        <if test="queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and
        queryParam.cy_end_date!=null and queryParam.cy_end_date!=''">
            AND a.dscg_time BETWEEN #{queryParam.cy_start_date} and #{queryParam.cy_end_date}
        </if>
    </select>

    <select id="getGroupNumTop10" resultType="com.my.som.vo.costControl.PpsInGroupNumTop10Vo">
        select
            CONCAT(dis_gp_codg,dis_gp_name) AS ppsGroupCodeAndName,
            COUNT(1) AS balanceMedicalRecordNum
        from sts_pps_group_record a
        where grp_stas='1'
        <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
            AND  a.HOSPITAL_ID = #{queryParam.hospitalId}
        </if>
        <if test="queryParam.b16c!=null and queryParam.b16c!=''">
            AND a.dscg_caty_codg_inhosp = #{queryParam.b16c}
        </if>
        <if test="queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and
        queryParam.cy_end_date!=null and queryParam.cy_end_date!=''">
            AND a.dscg_time BETWEEN #{queryParam.cy_start_date} and #{queryParam.cy_end_date}
        </if>
        group by CONCAT(dis_gp_codg,dis_gp_name)
    </select>

    <select id="getGroupCostTop10" resultType="com.my.som.vo.costControl.PpsInGroupCostTop10Vo">
        select
            CONCAT(a.dis_gp_codg,a.dis_gp_name) AS ppsGroupCodeAndName,
            IFNULL(ROUND(sum(b.ipt_sumfee),2),0) AS inHosCost
        from sts_pps_group_record a LEFT join som_drg_grp_info b ON a.SETTLE_LIST_ID = b.SETTLE_LIST_ID
        where a.grp_stas='1'
        <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
            AND  a.HOSPITAL_ID = #{queryParam.hospitalId}
        </if>
        <if test="queryParam.b16c!=null and queryParam.b16c!=''">
            AND a.dscg_caty_codg_inhosp = #{queryParam.b16c}
        </if>
        <if test="queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and
        queryParam.cy_end_date!=null and queryParam.cy_end_date!=''">
            AND a.dscg_time BETWEEN #{queryParam.cy_start_date} and #{queryParam.cy_end_date}
        </if>
        group by CONCAT(a.dis_gp_codg,a.dis_gp_name)
    </select>

    <select id="getCostCountInfo" resultType="com.my.som.vo.costControl.MedicalAndSettleListCostVo">
        select
            IFNULL(SUM(TOTAL_BASE_COST),0) AS sumfee,
            IFNULL(SUM(BED_COST),0) AS bedCost,
            IFNULL(SUM(DIAGNOSTIC_COST),0) AS diagnosticCost,
            IFNULL(SUM(CHECK_COST2),0) AS checkCost2,
            IFNULL(SUM(LABORATORY_COST),0) AS laboratoryCost,
            IFNULL(SUM(TREATMENT_COST2),0) AS treatmentCost2,
            IFNULL(SUM(OPERATIVE_COST),0) AS operativeCost,
            IFNULL(SUM(CARE_COST),0) AS careCost,
            IFNULL(SUM(HEATH_MATERIAL_COST),0) AS heathMaterialCost,
            IFNULL(SUM(WEST_MEDICAL_COST),0) AS westMedicalCost,
            IFNULL(SUM(CHINESE_PIECE_COST),0) AS chinesePieceCost,
            IFNULL(SUM(CHINESE_MEDICAL_COST),0) AS chineseMedicalCost,
            IFNULL(SUM(CONSULTATION_COST),0) AS consultationCost,
            IFNULL(SUM(REGISTRATION_COST),0) AS registrationCost,
            IFNULL(SUM(OTHER_COST2),0) AS otherCost2,
            IFNULL(SUM(case when b.grp_stas='1' then TOTAL_BASE_COST else 0 end),0) AS totalCostInPps,
            IFNULL(SUM(case when b.grp_stas='1' then BED_COST else 0 end),0) AS bedCostInPps,
            IFNULL(SUM(case when b.grp_stas='1' then DIAGNOSTIC_COST else 0 end),0) AS diagnosticCostInPps,
            IFNULL(SUM(case when b.grp_stas='1' then CHECK_COST2 else 0 end),0) AS checkCost2InPps,
            IFNULL(SUM(case when b.grp_stas='1' then LABORATORY_COST else 0 end),0) AS laboratoryCostInPps,
            IFNULL(SUM(case when b.grp_stas='1' then TREATMENT_COST2 else 0 end),0) AS treatmentCost2InPps,
            IFNULL(SUM(case when b.grp_stas='1' then OPERATIVE_COST else 0 end),0) AS operativeCostInPps,
            IFNULL(SUM(case when b.grp_stas='1' then CARE_COST else 0 end),0) AS careCostInPps,
            IFNULL(SUM(case when b.grp_stas='1' then HEATH_MATERIAL_COST else 0 end),0) AS heathMaterialCostInPps,
            IFNULL(SUM(case when b.grp_stas='1' then WEST_MEDICAL_COST else 0 end),0) AS westMedicalCostInPps,
            IFNULL(SUM(case when b.grp_stas='1' then CHINESE_PIECE_COST else 0 end),0) AS chinesePieceCostInPps,
            IFNULL(SUM(case when b.grp_stas='1' then CHINESE_MEDICAL_COST else 0 end),0) AS chineseMedicalCostInPps,
            IFNULL(SUM(case when b.grp_stas='1' then CONSULTATION_COST else 0 end),0) AS consultationCostInPps,
            IFNULL(SUM(case when b.grp_stas='1' then REGISTRATION_COST else 0 end),0) AS registrationCostInPps,
            IFNULL(SUM(case when b.grp_stas='1' then OTHER_COST2 else 0 end),0) AS otherCost2InPps,

            IFNULL(SUM(ipt_sumfee),0) AS iptSumfee,
            IFNULL(SUM(ipt_sumfee_in_selfpay_amt),0) AS iptSumfeeInSelfpayAmt,
            IFNULL(SUM(com_med_servfee),0) AS comMedServfee,
            IFNULL(SUM(CHECK_COST1),0) AS checkCost1,
            IFNULL(SUM(rhab_fee),0) AS rhabFee,
            IFNULL(SUM(diag_fee),0) AS diagFee,
            IFNULL(SUM(TREATMENT_COST1),0) AS treatmentCost1,
            IFNULL(SUM(drugfee),0) AS drugfee,
            IFNULL(SUM(abt_fee),0) AS abtFee,
            IFNULL(SUM(blood_blo_pro),0) AS bloodBloPro,
            IFNULL(SUM(mcs_fee),0) AS mcsFee,
            IFNULL(SUM(OTHER_COST1),0) AS otherCost1,
            IFNULL(SUM(case when b.grp_stas='1' then ipt_sumfee else 0 end),0) AS inhosTotalCostInPps,
            IFNULL(SUM(case when b.grp_stas='1' then ipt_sumfee_in_selfpay_amt else 0 end),0) AS inhosSelfPayCostInPps,
            IFNULL(SUM(case when b.grp_stas='1' then com_med_servfee else 0 end),0) AS serviceCostInPps,
            IFNULL(SUM(case when b.grp_stas='1' then CHECK_COST1 else 0 end),0) AS checkCost1InPps,
            IFNULL(SUM(case when b.grp_stas='1' then rhab_fee else 0 end),0) AS recoverCostInPps,
            IFNULL(SUM(case when b.grp_stas='1' then diag_fee else 0 end),0) AS diagnoseCostInPps,
            IFNULL(SUM(case when b.grp_stas='1' then TREATMENT_COST1 else 0 end),0) AS treatmentCost1InPps,
            IFNULL(SUM(case when b.grp_stas='1' then drugfee else 0 end),0) AS medicalCostInPps,
            IFNULL(SUM(case when b.grp_stas='1' then abt_fee else 0 end),0) AS antibioticCostInPps,
            IFNULL(SUM(case when b.grp_stas='1' then blood_blo_pro else 0 end),0) AS bloodCostInPps,
            IFNULL(SUM(case when b.grp_stas='1' then mcs_fee else 0 end),0) AS materialCostInPps,
            IFNULL(SUM(case when b.grp_stas='1' then OTHER_COST1 else 0 end),0) AS otherCost1InPps
        from som_drg_grp_info a
        LEFT JOIN sts_pps_group_record b ON a.SETTLE_LIST_ID = b.SETTLE_LIST_ID
        where 1 = 1
        <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
            AND  a.HOSPITAL_ID = #{queryParam.hospitalId}
        </if>
        <if test="queryParam.b16c!=null and queryParam.b16c!=''">
            AND a.dscg_caty_codg_inhosp = #{queryParam.b16c}
        </if>
        <if test="queryParam.queryPpsGroup!=null and queryParam.queryPpsGroup!=''">
            AND b.dis_gp_codg = #{queryParam.queryPpsGroup}
        </if>
        <if test="queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and
        queryParam.cy_end_date!=null and queryParam.cy_end_date!=''">
            AND a.dscg_time BETWEEN #{queryParam.cy_start_date} and #{queryParam.cy_end_date}
        </if>
    </select>
</mapper>