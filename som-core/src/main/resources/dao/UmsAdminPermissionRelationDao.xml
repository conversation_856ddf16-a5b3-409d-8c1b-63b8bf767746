<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.som.dao.UmsAdminPermissionRelationDao">
    <!--批量新增回写主键支持-->
    <insert id="insertList">
        INSERT INTO som_user_mgt_back_user_perm_rlts (admin_id, permission_id, type) VALUES
        <foreach collection="list" separator="," item="item" index="index">
            (#{item.adminId,jdbcType=BIGINT},
            #{item.permissionId,jdbcType=BIGINT},
            #{item.type,jdbcType=INTEGER})
        </foreach>
    </insert>
</mapper>