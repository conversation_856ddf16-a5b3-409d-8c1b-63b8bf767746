<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.som.dao.doctorDiagnoseInterface.BusIcdDao">
    <resultMap id="BaseResultMap" type="com.my.som.model.common.SomIcdCrsp">
        <id column="ID" jdbcType="BIGINT" property="id" />
        <result column="ICD_TYPE" jdbcType="VARCHAR" property="icdType" />
        <result column="icd_codg_ver" jdbcType="VARCHAR" property="icdCodgVer" />
        <result column="icd_codg" jdbcType="VARCHAR" property="icdCodg" />
        <result column="ICD_NAME" jdbcType="VARCHAR" property="icdName" />
        <result column="ACTIVE_FLAG" jdbcType="VARCHAR" property="activeFlag" />
        <result column="HOSPITAL_ID" jdbcType="VARCHAR" property="hospitalId" />
    </resultMap>
    <sql id="Base_Column_List">
          icd_codg, ICD_NAME
      </sql>
    <select id="queryLikeIcdsByPram" resultMap="BaseResultMap">
        select
          distinct
          <include refid="Base_Column_List" />
        from som_icd_crsp
        where length(icd_codg)>0
        <if test="busIcdQueryParam.active_flag!=null and busIcdQueryParam.active_flag!=''">
            AND active_flag = #{busIcdQueryParam.active_flag}
        </if>
        <if test="busIcdQueryParam.icd_type!=null and busIcdQueryParam.icd_type!=''">
            AND icd_type = #{busIcdQueryParam.icd_type}
        </if>
        <if test="busIcdQueryParam.icd_codg_ver!=null and busIcdQueryParam.icd_codg_ver!=''">
            AND icd_codg_ver = #{busIcdQueryParam.icd_codg_ver}
        </if>
        <if test="busIcdQueryParam.icd_codg!=null and busIcdQueryParam.icd_codg!=''">
            AND icd_codg = #{busIcdQueryParam.icd_codg}
        </if>
        <if test="busIcdQueryParam.icd_name!=null and busIcdQueryParam.icd_name!=''">
            AND icd_name = #{busIcdQueryParam.icd_name}
        </if>
<!--        <if test="busIcdQueryParam.hospital_id!=null and busIcdQueryParam.hospital_id!=''">-->
<!--            AND hospital_id = #{busIcdQueryParam.hospital_id}-->
<!--        </if>-->
        <if test="busIcdQueryParam.likeQueryString!=null and busIcdQueryParam.likeQueryString!=''">
            AND (
                icd_codg LIKE concat("%",#{busIcdQueryParam.likeQueryString},"%")
                OR icd_name LIKE concat("%",#{busIcdQueryParam.likeQueryString},"%")
            )
        </if>
        ORDER BY icd_codg,ICD_NAME
        limit 0,100
    </select>
</mapper>