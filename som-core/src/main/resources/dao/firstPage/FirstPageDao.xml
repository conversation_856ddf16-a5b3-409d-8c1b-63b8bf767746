<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.som.dao.FirstPage.FirstPageDao">

    <select id="getCountInfo" resultType="com.my.som.vo.firstPage.FirstPageCountVo">
           select
              IFNULL(e.inHosMedicalRecordNum,0) AS inHosMedicalRecordNum,
              IFNULL(f.lastYearInHosMedicalRecordNum,0) AS lastYearInHosMedicalRecordNum,
              IFNULL(g.lastMonthInHosMedicalRecordNum,0) AS lastMonthInHosMedicalRecordNum,
              IFNULL(w.outHosMedicalRecordNum,0) AS outHosMedicalRecordNum,
              IFNULL(w.lastYearOutHosMedicalRecordNum,0) AS lastYearOutHosMedicalRecordNum,
              IFNULL(w.lastMonthOutHosMedicalRecordNum,0) AS lastMonthOutHosMedicalRecordNum,
              IFNULL(w.drgInGroupMedcasVal,0) AS drgInGroupMedcasVal,
              IFNULL(w.lastMonthInGroupNum,0) AS lastMonthInGroupNum,
              IFNULL(w.lastYearInGroupNum,0) AS lastYearInGroupNum,
              IFNULL(w.inHosCost,0) AS inHosCost,
              IFNULL(w.lastYearInHosCost,0) AS lastYearInHosCost,
              IFNULL(w.lastMonthInHosCost,0) AS lastMonthInHosCost,
              o.*,
              p.*,
              q.*,
              r.*
           from (
            select
                COUNT(CASE WHEN a.dscg_time BETWEEN #{queryParam.cy_start_date} and CONCAT(#{queryParam.cy_end_date},' 23:59:59')
                    THEN 1 ELSE NULL END)  AS outHosMedicalRecordNum,
                COUNT(CASE WHEN a.dscg_time BETWEEN #{queryParam.lastYear_cy_start_date} and CONCAT(#{queryParam.lastYear_cy_end_date},' 23:59:59')
                    THEN 1 ELSE NULL END)  AS lastYearOutHosMedicalRecordNum,
                COUNT(CASE WHEN a.dscg_time BETWEEN #{queryParam.lastMonth_cy_start_date} and CONCAT(#{queryParam.lastMonth_cy_end_date},' 23:59:59')
                    THEN 1 ELSE NULL END)  AS lastMonthOutHosMedicalRecordNum,
                COUNT(CASE WHEN a.dscg_time BETWEEN #{queryParam.cy_start_date} and CONCAT(#{queryParam.cy_end_date},' 23:59:59')
                    AND a.grp_stas = '1' then 1 ELSE NULL END) AS drgInGroupMedcasVal,
                COUNT(CASE WHEN a.dscg_time BETWEEN #{queryParam.lastMonth_cy_start_date} and CONCAT(#{queryParam.lastMonth_cy_end_date},' 23:59:59')
                    AND a.grp_stas = '1' then 1 ELSE NULL END) AS lastMonthInGroupNum,
                COUNT(CASE WHEN a.dscg_time BETWEEN #{queryParam.lastYear_cy_start_date} and CONCAT(#{queryParam.lastYear_cy_end_date},' 23:59:59')
                    AND a.grp_stas = '1' then 1 ELSE NULL END) AS lastYearInGroupNum,
                SUM(CASE WHEN a.dscg_time BETWEEN #{queryParam.cy_start_date} and CONCAT(#{queryParam.cy_end_date},' 23:59:59')
                    then a.ipt_sumfee ELSE 0 END) AS inHosCost,
                SUM(CASE WHEN a.dscg_time BETWEEN #{queryParam.lastYear_cy_start_date} and CONCAT(#{queryParam.lastYear_cy_end_date},' 23:59:59')
                    then a.ipt_sumfee ELSE 0 END) AS lastYearInHosCost,
                SUM(CASE WHEN a.dscg_time BETWEEN #{queryParam.lastMonth_cy_start_date} and CONCAT(#{queryParam.lastMonth_cy_end_date},' 23:59:59')
                    then a.ipt_sumfee ELSE 0 END) AS lastMonthInHosCost
            from som_drg_grp_info a
            where 1 = 1
            <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
                AND  a.HOSPITAL_ID = #{queryParam.hospitalId}
            </if>
            <if test="queryParam.b16c!=null and queryParam.b16c!=''">
                AND a.dscg_caty_codg_inhosp = #{queryParam.b16c}
            </if>
            <if test="queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and
                    queryParam.cy_end_date!=null and queryParam.cy_end_date!=''">
                AND (
                    (a.dscg_time BETWEEN #{queryParam.cy_start_date} and CONCAT(#{queryParam.cy_end_date},' 23:59:59'))
                    OR
                    (a.dscg_time BETWEEN #{queryParam.lastMonth_cy_start_date} and CONCAT(#{queryParam.lastMonth_cy_end_date},' 23:59:59'))
                    OR
                    (a.dscg_time BETWEEN #{queryParam.lastYear_cy_start_date} and CONCAT(#{queryParam.lastYear_cy_end_date},' 23:59:59'))
                )
            </if>
            <if test="queryParam.doctorIdList!=null">
                AND (
                a.deptdrt_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                a.chfdr_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                a.atddr_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                a.ipdr_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                a.resp_nurs_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                a.train_dr_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                a.intn_dr in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                a.codr_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                a.qltctrl_dr_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                a.qltctrl_nurs_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                )
            </if>
            ) w
            cross join (
                select COUNT(1) as inHosMedicalRecordNum
                from som_drg_grp_info
                where adm_time BETWEEN #{queryParam.cy_start_date} and CONCAT(#{queryParam.cy_end_date},' 23:59:59')
                <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
                    AND HOSPITAL_ID = #{queryParam.hospitalId}
                </if>
                <if test="queryParam.b16c!=null and queryParam.b16c!=''">
                    AND dscg_caty_codg_inhosp = #{queryParam.b16c}
                </if>
                <if test="queryParam.doctorIdList!=null">
                    AND (
                    deptdrt_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    chfdr_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    atddr_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    ipdr_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    resp_nurs_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    train_dr_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    intn_dr in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    codr_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    qltctrl_dr_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    qltctrl_nurs_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    )
                </if>
            ) e
            cross join (
                select COUNT(1) as lastYearInHosMedicalRecordNum
                from som_drg_grp_info
                where adm_time BETWEEN #{queryParam.lastYear_cy_start_date} and CONCAT(#{queryParam.lastYear_cy_end_date},' 23:59:59')
                <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
                    AND HOSPITAL_ID = #{queryParam.hospitalId}
                </if>
                <if test="queryParam.b16c!=null and queryParam.b16c!=''">
                    AND dscg_caty_codg_inhosp = #{queryParam.b16c}
                </if>
                <if test="queryParam.doctorIdList!=null">
                    AND (
                    deptdrt_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    chfdr_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    atddr_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    ipdr_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    resp_nurs_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    train_dr_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    intn_dr in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    codr_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    qltctrl_dr_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    qltctrl_nurs_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    )
                </if>
            ) f
            cross join (
                select COUNT(1) as lastMonthInHosMedicalRecordNum
                from som_drg_grp_info
                where adm_time BETWEEN #{queryParam.lastMonth_cy_start_date} and CONCAT(#{queryParam.lastMonth_cy_end_date},' 23:59:59')
                <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
                    AND HOSPITAL_ID = #{queryParam.hospitalId}
                </if>
                <if test="queryParam.b16c!=null and queryParam.b16c!=''">
                    AND dscg_caty_codg_inhosp = #{queryParam.b16c}
                </if>
                <if test="queryParam.doctorIdList!=null">
                    AND (
                    deptdrt_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    chfdr_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    atddr_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    ipdr_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    resp_nurs_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    train_dr_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    intn_dr in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    codr_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    qltctrl_dr_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    qltctrl_nurs_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    )
                </if>
            ) g
            cross join (
                  select
                    IFNULL(count(distinct(case when x.err_type in ('CE01','CE02','CE03','CE04') then x.settle_list_id else null end)),0)  AS completeErrorNum
                  from som_setl_invy_chk_err_rcd x
                  where x.settle_list_id in (
                        select
                          settle_list_id
                        from som_drg_grp_info a
                        WHERE a.dscg_time BETWEEN #{queryParam.cy_start_date} and CONCAT(#{queryParam.cy_end_date},' 23:59:59')
                        <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
                            AND  a.HOSPITAL_ID = #{queryParam.hospitalId}
                        </if>
                        <if test="queryParam.b16c!=null and queryParam.b16c!=''">
                            AND a.dscg_caty_codg_inhosp = #{queryParam.b16c}
                        </if>
                        <if test="queryParam.doctorIdList!=null">
                            AND (
                            a.deptdrt_code in
                            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                                #{item}
                            </foreach>
                            OR
                            a.chfdr_code in
                            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                                #{item}
                            </foreach>
                            OR
                            a.atddr_code in
                            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                                #{item}
                            </foreach>
                            OR
                            a.ipdr_code in
                            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                                #{item}
                            </foreach>
                            OR
                            a.resp_nurs_code in
                            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                                #{item}
                            </foreach>
                            OR
                            a.train_dr_code in
                            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                                #{item}
                            </foreach>
                            OR
                            a.intn_dr in
                            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                                #{item}
                            </foreach>
                            OR
                            a.codr_code in
                            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                                #{item}
                            </foreach>
                            OR
                            a.qltctrl_dr_code in
                            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                                #{item}
                            </foreach>
                            OR
                            a.qltctrl_nurs_code in
                            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                                #{item}
                            </foreach>
                            )
                        </if>
                  )
            ) o
            cross join (
                  select
                    IFNULL(count(distinct(case when x.err_type in ('LE01','LE02','LE03','LE04','LE05','LE06','LE07') then x.settle_list_id else null end)),0)  AS logicErrorNum
                  from som_setl_invy_chk_err_rcd x
                  where x.settle_list_id in (
                        select
                          settle_list_id
                        from som_drg_grp_info a
                        WHERE a.dscg_time BETWEEN #{queryParam.cy_start_date} and CONCAT(#{queryParam.cy_end_date},' 23:59:59')
                        <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
                            AND  a.HOSPITAL_ID = #{queryParam.hospitalId}
                        </if>
                        <if test="queryParam.b16c!=null and queryParam.b16c!=''">
                            AND a.dscg_caty_codg_inhosp = #{queryParam.b16c}
                        </if>
                        <if test="queryParam.doctorIdList!=null">
                            AND (
                            a.deptdrt_code in
                            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                                #{item}
                            </foreach>
                            OR
                            a.chfdr_code in
                            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                                #{item}
                            </foreach>
                            OR
                            a.atddr_code in
                            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                                #{item}
                            </foreach>
                            OR
                            a.ipdr_code in
                            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                                #{item}
                            </foreach>
                            OR
                            a.resp_nurs_code in
                            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                                #{item}
                            </foreach>
                            OR
                            a.train_dr_code in
                            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                                #{item}
                            </foreach>
                            OR
                            a.intn_dr in
                            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                                #{item}
                            </foreach>
                            OR
                            a.codr_code in
                            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                                #{item}
                            </foreach>
                            OR
                            a.qltctrl_dr_code in
                            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                                #{item}
                            </foreach>
                            OR
                            a.qltctrl_nurs_code in
                            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                                #{item}
                            </foreach>
                            )
                        </if>
                  )
            ) p
            cross join (
                  select
                    IFNULL(round(sum(x.refer_sco)/NULLIF(count(1),0),2),100)  AS refer_sco
                  from som_setl_invy_qlt_dedu_point_detl x
                  where x.settle_list_id in (
                        select
                          settle_list_id
                        from som_drg_grp_info a
                        WHERE a.dscg_time BETWEEN #{queryParam.cy_start_date} and CONCAT(#{queryParam.cy_end_date},' 23:59:59')
                        <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
                            AND  a.HOSPITAL_ID = #{queryParam.hospitalId}
                        </if>
                        <if test="queryParam.b16c!=null and queryParam.b16c!=''">
                            AND a.dscg_caty_codg_inhosp = #{queryParam.b16c}
                        </if>
                        <if test="queryParam.doctorIdList!=null">
                            AND (
                            a.deptdrt_code in
                            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                                #{item}
                            </foreach>
                            OR
                            a.chfdr_code in
                            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                                #{item}
                            </foreach>
                            OR
                            a.atddr_code in
                            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                                #{item}
                            </foreach>
                            OR
                            a.ipdr_code in
                            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                                #{item}
                            </foreach>
                            OR
                            a.resp_nurs_code in
                            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                                #{item}
                            </foreach>
                            OR
                            a.train_dr_code in
                            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                                #{item}
                            </foreach>
                            OR
                            a.intn_dr in
                            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                                #{item}
                            </foreach>
                            OR
                            a.codr_code in
                            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                                #{item}
                            </foreach>
                            OR
                            a.qltctrl_dr_code in
                            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                                #{item}
                            </foreach>
                            OR
                            a.qltctrl_nurs_code in
                            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                                #{item}
                            </foreach>
                            )
                        </if>
                  )
            ) q
            cross join (
                  select
                      IFNULL(count(distinct (case when a.grp_stas = '1' then a.drg_codg else null end)),0) AS drgNum,
                      IFNULL(ROUND(NULLIF(sum(case when a.grp_stas = '1' then 1 else 0 end),0) /
                         NULLIF(sum(case when a.chk_flag = '1' then 1 else null end),0) * 100,2),0) AS inGroupRate,
                      IFNULL(ROUND(SUM(a.ipt_sumfee)/NULLIF(count(1),0), 2),0) AS avgCost,
                      IFNULL(ROUND(SUM(a.act_ipt)/NULLIF(count(1),0), 2),0) AS avgDays,
                      IFNULL(round(SUM(CASE WHEN a.grp_stas = '1' THEN a.drg_wt ELSE NULL END)/
                        NULLIF (COUNT(CASE WHEN a.grp_stas = '1' THEN 1 ELSE NULL END),0),2),0)   AS cmi,
                      ROUND(IFNULL(SUM(a.drg_wt),0),2) AS totalWeight,
                      ROUND(IFNULL(sum(IFNULL(a.act_ipt,0)/NULLIF(a.standard_ave_hosp_day, 0))/COUNT(1),0),2) as timeIndex,
                      ROUND(IFNULL(sum(IFNULL(a.ipt_sumfee,0)/NULLIF(a.standard_avg_fee, 0))/COUNT(1),0),2) as costIndex
                  from som_drg_grp_info a
                  WHERE a.dscg_time BETWEEN #{queryParam.cy_start_date} and CONCAT(#{queryParam.cy_end_date},' 23:59:59')
                  <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
                      AND  a.HOSPITAL_ID = #{queryParam.hospitalId}
                  </if>
                  <if test="queryParam.b16c!=null and queryParam.b16c!=''">
                      AND a.dscg_caty_codg_inhosp = #{queryParam.b16c}
                  </if>
                    <if test="queryParam.doctorIdList!=null">
                        AND (
                        a.deptdrt_code in
                        <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                        OR
                        a.chfdr_code in
                        <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                        OR
                        a.atddr_code in
                        <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                        OR
                        a.ipdr_code in
                        <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                        OR
                        a.resp_nurs_code in
                        <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                        OR
                        a.train_dr_code in
                        <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                        OR
                        a.intn_dr in
                        <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                        OR
                        a.codr_code in
                        <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                        OR
                        a.qltctrl_dr_code in
                        <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                        OR
                        a.qltctrl_nurs_code in
                        <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                        )
                    </if>
            ) r
    </select>

    <select id="getDrgIndexInfo" resultType="com.my.som.vo.firstPage.FirstPageCountVo">
        select
            x.mon_name as month,
            y.drgNum as drgNum,
            y.cmi as cmi,
            y.totalWeight as totalWeight,
            y.timeIndex as timeIndex,
            y.costIndex as costIndex
        from som_mon_cfg x
        left join (
            select
                SUBSTR(a.dscg_time,1,7) as time,
                IFNULL(count(distinct (case when a.grp_stas = '1' then a.drg_codg else null end)),0) AS drgNum,
                IFNULL(round(SUM(CASE WHEN a.grp_stas = '1' THEN a.drg_wt ELSE NULL END)/
                  NULLIF (COUNT(CASE WHEN a.grp_stas = '1' THEN 1 ELSE NULL END),0),2),0)   AS cmi,
                ROUND(IFNULL(SUM(a.drg_wt),0),2) AS totalWeight,
                ROUND(IFNULL(sum(IFNULL(a.act_ipt,0)/NULLIF(a.standard_ave_hosp_day, 0))/COUNT(1),0),2) as timeIndex,
                ROUND(IFNULL(sum(IFNULL(a.ipt_sumfee,0)/NULLIF(a.standard_avg_fee, 0))/COUNT(1),0),2) as costIndex
            from som_drg_grp_info a
            WHERE 1=1
            <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
                AND  a.HOSPITAL_ID = #{queryParam.hospitalId}
            </if>
            <if test="queryParam.b16c!=null and queryParam.b16c!=''">
                AND a.dscg_caty_codg_inhosp = #{queryParam.b16c}
            </if>
            <if test="queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and queryParam.cy_end_date!=null and queryParam.cy_end_date!=''">
                AND a.dscg_time BETWEEN #{queryParam.cy_start_date} and CONCAT(#{queryParam.cy_end_date},' 23:59:59')
            </if>
            <if test="queryParam.doctorIdList!=null">
                AND (
                a.deptdrt_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                a.chfdr_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                a.atddr_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                a.ipdr_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                a.resp_nurs_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                a.train_dr_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                a.intn_dr in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                a.codr_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                a.qltctrl_dr_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                a.qltctrl_nurs_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                )
            </if>
            GROUP BY SUBSTR(a.dscg_time,1,7)
        ) y on x.mon_digl = SUBSTR(y.time,6,2)
        order by x.mon_digl
    </select>

    <select id="getMedicalTreatmentCostInfo" resultType="com.my.som.vo.firstPage.MedicalTreatmentCostVo">
        select
              IFNULL(SUM(ipt_sumfee),0) AS iptSumfee,
              IFNULL(count(1),0) AS totalPatient,
              IFNULL(SUM(ipt_sumfee_in_selfpay_amt),0) AS iptSumfeeInSelfpayAmt,
              <![CDATA[ COUNT(CASE WHEN ipt_sumfee_in_selfpay_amt>0 and ipt_sumfee_in_selfpay_amt!='' and ipt_sumfee_in_selfpay_amt!='-' and ipt_sumfee_in_selfpay_amt!='--' THEN 1 ELSE NULL END) ]]> AS inhosSelfPayCostPatient,
              IFNULL(SUM(com_med_servfee),0) AS comMedServfee,
              <![CDATA[ COUNT(CASE WHEN com_med_servfee>0 and com_med_servfee!='' and com_med_servfee!='-' and com_med_servfee!='--' THEN 1 ELSE NULL END) ]]> AS serviceCostPatient,
              IFNULL(SUM(rhab_fee),0) AS rhabFee,
              <![CDATA[ COUNT(CASE WHEN rhab_fee>0 and rhab_fee!='' and rhab_fee!='-' and rhab_fee!='--' THEN 1 ELSE NULL END) ]]> AS recoverCostPatient,
              IFNULL(SUM(diag_fee),0) AS diagFee,
              <![CDATA[ COUNT(CASE WHEN diag_fee>0 and diag_fee!='' and diag_fee!='-' and diag_fee!='--' THEN 1 ELSE NULL END) ]]> AS diagnoseCostPatient,
              IFNULL(SUM(treat_fee),0) AS treatFee,
              <![CDATA[ COUNT(CASE WHEN treat_fee>0 and treat_fee!='' and treat_fee!='-' and treat_fee!='--' THEN 1 ELSE NULL END) ]]> AS treatmentCostPatient,
              IFNULL(SUM(drugfee),0) AS drugfee,
              <![CDATA[ COUNT(CASE WHEN drugfee>0 and drugfee!='' and drugfee!='-' and drugfee!='--' THEN 1 ELSE NULL END) ]]> AS medicalCostPatient,
              IFNULL(SUM(blood_blo_pro),0) AS bloodBloPro,
              <![CDATA[ COUNT(CASE WHEN blood_blo_pro>0 and blood_blo_pro!='' and blood_blo_pro!='-' and blood_blo_pro!='--' THEN 1 ELSE NULL END) ]]> AS bloodCostPatient,
              IFNULL(SUM(mcs_fee),0) AS mcsFee,
              <![CDATA[ COUNT(CASE WHEN mcs_fee>0 and mcs_fee!='' and mcs_fee!='-' and mcs_fee!='--' THEN 1 ELSE NULL END) ]]> AS materialCostPatient,
              IFNULL(SUM(tcm_oth),0) AS chineseOtherCost,
              <![CDATA[ COUNT(CASE WHEN tcm_oth>0 and tcm_oth!='' and tcm_oth!='-' and tcm_oth!='--' THEN 1 ELSE NULL END) ]]> AS chineseOtherCostPatient,
              IFNULL(SUM(oth_fee),0) AS othFee,
              <![CDATA[ COUNT(CASE WHEN oth_fee>0 and oth_fee!='' and oth_fee!='-' and oth_fee!='--' THEN 1 ELSE NULL END) ]]> AS otherCostPatient,
              IFNULL(SUM(abt_fee),0) AS abtFee,
              <![CDATA[ COUNT(CASE WHEN abt_fee>0 and abt_fee!='' and abt_fee!='-' and abt_fee!='--' THEN 1 ELSE NULL END) ]]> AS antibioticCostPatient,
              IFNULL(SUM(inspect_fee),0) AS inspectFee,
              <![CDATA[ COUNT(CASE WHEN inspect_fee>0 and inspect_fee!='' and inspect_fee!='-' and inspect_fee!='--' THEN 1 ELSE NULL END) ]]> AS inspectionCostPatient
        from som_drg_grp_info a
        WHERE 1=1
        <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
            AND  a.HOSPITAL_ID = #{queryParam.hospitalId}
        </if>
        <if test="queryParam.b16c!=null and queryParam.b16c!=''">
            AND a.dscg_caty_codg_inhosp = #{queryParam.b16c}
        </if>
        <if test="queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and queryParam.cy_end_date!=null and queryParam.cy_end_date!=''">
              AND a.dscg_time BETWEEN #{queryParam.cy_start_date} and CONCAT(#{queryParam.cy_end_date},' 23:59:59')
        </if>
        <if test="queryParam.doctorIdList!=null">
            AND (
            a.deptdrt_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.chfdr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.atddr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.ipdr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.resp_nurs_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.train_dr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.intn_dr in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.codr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.qltctrl_dr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.qltctrl_nurs_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            )
        </if>
    </select>

    <select id="getDoctorDrgIndexInfo" resultType="com.my.som.vo.firstPage.FirstPageCountVo">
        select
            x.drName AS drName,
            IFNULL(count(distinct (case when x.grp_stas = '1' then x.drg_codg else null end)),0) AS drgNum,
            IFNULL(round(SUM(CASE WHEN x.grp_stas = '1' THEN x.drg_wt ELSE NULL END)/
              NULLIF (COUNT(CASE WHEN x.grp_stas = '1' THEN 1 ELSE NULL END),0),2),0)   AS cmi,
            ROUND(IFNULL(SUM(x.drg_wt),0),2) AS totalWeight,
            ROUND(IFNULL(sum(IFNULL(x.act_ipt,0)/NULLIF(x.standard_ave_hosp_day, 0))/COUNT(1),0),2) as timeIndex,
            ROUND(IFNULL(sum(IFNULL(x.ipt_sumfee,0)/NULLIF(x.standard_avg_fee, 0))/COUNT(1),0),2) as costIndex
        from (
            select
                c.drCodg as drCodg,
                c.drName as drName,
                a.grp_stas as grp_stas,
                a.drg_codg as drg_codg,
                a.drg_wt as drg_wt,
                a.act_ipt as act_ipt,
                a.ipt_sumfee as ipt_sumfee,
                a.standard_ave_hosp_day as standard_ave_hosp_day,
                a.standard_avg_fee as standard_avg_fee
            from som_drg_grp_info a
            inner join (
                select
                    b.settle_list_id as settle_list_id,
                    b.drCodg as drCodg,
                    b.drName as drName
                from (
                    select
                        SETTLE_LIST_ID as settle_list_id,
                        ipdr_code as drCodg,
                        ipdr_name as drName
                    FROM som_drg_grp_info
                    where ipdr_name is not null and ipdr_name !='-' and ipdr_name !='--'
                    <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
                        AND  HOSPITAL_ID = #{queryParam.hospitalId}
                    </if>
                    <if test="queryParam.b16c!=null and queryParam.b16c!=''">
                        AND dscg_caty_codg_inhosp = #{queryParam.b16c}
                    </if>
                    <if test="queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and
                                                        queryParam.cy_end_date!=null and queryParam.cy_end_date!=''">
                        AND dscg_time BETWEEN #{queryParam.cy_start_date} and CONCAT(#{queryParam.cy_end_date},' 23:59:59')
                    </if>
                    <if test="queryParam.doctorIdList!=null">
                        AND (
                        deptdrt_code in
                        <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                        OR
                        chfdr_code in
                        <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                        OR
                        atddr_code in
                        <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                        OR
                        ipdr_code in
                        <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                        OR
                        resp_nurs_code in
                        <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                        OR
                        train_dr_code in
                        <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                        OR
                        intn_dr in
                        <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                        OR
                        codr_code in
                        <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                        OR
                        qltctrl_dr_code in
                        <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                        OR
                        qltctrl_nurs_code in
                        <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                        )
                    </if>
                    <!--union all
                    select
                        SETTLE_LIST_ID as settle_list_id,
                        atddr_code as drCodg,
                        atddr_name as drName
                    FROM som_drg_grp_info
                    where atddr_name is not null and atddr_name !='-' and atddr_name !='&#45;&#45;'
                    <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
                        AND  HOSPITAL_ID = #{queryParam.hospitalId}
                    </if>
                    <if test="queryParam.b16c!=null and queryParam.b16c!=''">
                        AND `PRI_OUTHOS_DEPT_CODE` = #{queryParam.b16c}
                    </if>
                    <if test="queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and
                                                        queryParam.cy_end_date!=null and queryParam.cy_end_date!=''">
                        <![CDATA[
                            AND STR_TO_DATE(dscg_time,"%Y-%m-%d") >= STR_TO_DATE(#{queryParam.cy_start_date},"%Y-%m-%d")
                            AND STR_TO_DATE(dscg_time,"%Y-%m-%d") <= STR_TO_DATE(CONCAT(#{queryParam.cy_end_date},' 23:59:59'),"%Y-%m-%d")
                        ]]>
                    </if>
                    union all
                    select
                        SETTLE_LIST_ID as settle_list_id,
                        deptdrt_code as drCodg,
                        deptdrt_name as drName
                    FROM som_drg_grp_info
                    where deptdrt_name is not null and deptdrt_name !='-' and deptdrt_name !='&#45;&#45;'
                    <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
                        AND  HOSPITAL_ID = #{queryParam.hospitalId}
                    </if>
                    <if test="queryParam.b16c!=null and queryParam.b16c!=''">
                        AND `PRI_OUTHOS_DEPT_CODE` = #{queryParam.b16c}
                    </if>
                    <if test="queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and
                                                                    queryParam.cy_end_date!=null and queryParam.cy_end_date!=''">
                        <![CDATA[
                            AND STR_TO_DATE(dscg_time,"%Y-%m-%d") >= STR_TO_DATE(#{queryParam.cy_start_date},"%Y-%m-%d")
                            AND STR_TO_DATE(dscg_time,"%Y-%m-%d") <= STR_TO_DATE(CONCAT(#{queryParam.cy_end_date},' 23:59:59'),"%Y-%m-%d")
                        ]]>
                    </if>
                    union all
                    select
                        SETTLE_LIST_ID as settle_list_id,
                        chfdr_code as drCodg,
                        chfdr_name as drName
                    FROM som_drg_grp_info
                    where chfdr_name is not null and chfdr_name !='-' and chfdr_name !='&#45;&#45;'
                    <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
                        AND  HOSPITAL_ID = #{queryParam.hospitalId}
                    </if>
                    <if test="queryParam.b16c!=null and queryParam.b16c!=''">
                        AND `PRI_OUTHOS_DEPT_CODE` = #{queryParam.b16c}
                    </if>
                    <if test="queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and
                                                            queryParam.cy_end_date!=null and queryParam.cy_end_date!=''">
                        <![CDATA[
                            AND STR_TO_DATE(dscg_time,"%Y-%m-%d") >= STR_TO_DATE(#{queryParam.cy_start_date},"%Y-%m-%d")
                            AND STR_TO_DATE(dscg_time,"%Y-%m-%d") <= STR_TO_DATE(CONCAT(#{queryParam.cy_end_date},' 23:59:59'),"%Y-%m-%d")
                        ]]>
                    </if>
                    union all
                    select
                        SETTLE_LIST_ID as settle_list_id,
                        train_dr_code as drCodg,
                        train_dr_name as drName
                    FROM som_drg_grp_info
                    where train_dr_name is not null and train_dr_name !='-' and train_dr_name !='&#45;&#45;'
                    <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
                        AND  HOSPITAL_ID = #{queryParam.hospitalId}
                    </if>
                    <if test="queryParam.b16c!=null and queryParam.b16c!=''">
                        AND `PRI_OUTHOS_DEPT_CODE` = #{queryParam.b16c}
                    </if>
                    <if test="queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and
                                                queryParam.cy_end_date!=null and queryParam.cy_end_date!=''">
                        <![CDATA[
                            AND STR_TO_DATE(dscg_time,"%Y-%m-%d") >= STR_TO_DATE(#{queryParam.cy_start_date},"%Y-%m-%d")
                            AND STR_TO_DATE(dscg_time,"%Y-%m-%d") <= STR_TO_DATE(CONCAT(#{queryParam.cy_end_date},' 23:59:59'),"%Y-%m-%d")
                        ]]>
                    </if>
                    union all
                    select
                        SETTLE_LIST_ID as settle_list_id,
                        qltctrl_dr_code as drCodg,
                        qltctrl_dr_name as drName
                    FROM som_drg_grp_info
                    where qltctrl_dr_name is not null and qltctrl_dr_name !='-' and qltctrl_dr_name !='&#45;&#45;'
                    <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
                        AND  HOSPITAL_ID = #{queryParam.hospitalId}
                    </if>
                    <if test="queryParam.b16c!=null and queryParam.b16c!=''">
                        AND `PRI_OUTHOS_DEPT_CODE` = #{queryParam.b16c}
                    </if>
                    <if test="queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and
                                                    queryParam.cy_end_date!=null and queryParam.cy_end_date!=''">
                        <![CDATA[
                            AND STR_TO_DATE(dscg_time,"%Y-%m-%d") >= STR_TO_DATE(#{queryParam.cy_start_date},"%Y-%m-%d")
                            AND STR_TO_DATE(dscg_time,"%Y-%m-%d") <= STR_TO_DATE(CONCAT(#{queryParam.cy_end_date},' 23:59:59'),"%Y-%m-%d")
                        ]]>
                    </if>
                    union all
                    select
                        SETTLE_LIST_ID as settle_list_id,
                        intn_dr as drCodg,
                        intn_dr as drName
                    FROM som_drg_grp_info
                    where intn_dr is not null and intn_dr !='-' and intn_dr !='&#45;&#45;'
                    <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
                        AND  HOSPITAL_ID = #{queryParam.hospitalId}
                    </if>
                    <if test="queryParam.b16c!=null and queryParam.b16c!=''">
                        AND `PRI_OUTHOS_DEPT_CODE` = #{queryParam.b16c}
                    </if>
                    <if test="queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and
                                                        queryParam.cy_end_date!=null and queryParam.cy_end_date!=''">
                        <![CDATA[
                            AND STR_TO_DATE(dscg_time,"%Y-%m-%d") >= STR_TO_DATE(#{queryParam.cy_start_date},"%Y-%m-%d")
                            AND STR_TO_DATE(dscg_time,"%Y-%m-%d") <= STR_TO_DATE(CONCAT(#{queryParam.cy_end_date},' 23:59:59'),"%Y-%m-%d")
                        ]]>
                    </if>-->
                ) b
                group by settle_list_id,drCodg,drName
            ) c on a.settle_list_id = c.settle_list_id
        where 1=1
        <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
            AND  a.HOSPITAL_ID = #{queryParam.hospitalId}
        </if>
        <if test="queryParam.b16c!=null and queryParam.b16c!=''">
            AND a.dscg_caty_codg_inhosp = #{queryParam.b16c}
        </if>
        <if test="queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and
                                                queryParam.cy_end_date!=null and queryParam.cy_end_date!=''">
            AND dscg_time BETWEEN #{queryParam.cy_start_date} and CONCAT(#{queryParam.cy_end_date},' 23:59:59')
        </if>
        <if test="queryParam.doctorIdList!=null">
            AND (
            a.deptdrt_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.chfdr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.atddr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.ipdr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.resp_nurs_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.train_dr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.intn_dr in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.codr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.qltctrl_dr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.qltctrl_nurs_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            )
        </if>
        ) x
        group by x.drName
    </select>

    <select id="getOperativeInfo" resultType="java.util.Map">
        select
        COUNT(CASE WHEN
        <![CDATA[ STR_TO_DATE(a.dscg_time,"%Y-%m-%d") >= STR_TO_DATE(#{queryParam.cy_start_date},"%Y-%m-%d") ]]>
        AND <![CDATA[ STR_TO_DATE(a.dscg_time,"%Y-%m-%d") <= STR_TO_DATE(CONCAT(#{queryParam.cy_end_date},' 23:59:59'),"%Y-%m-%d") ]]>
        AND b.oprn_oprt_lv = '1'
        THEN 1 ELSE NULL END) AS oneLvlOprHosIssueNum,
        COUNT(CASE WHEN
        <![CDATA[ STR_TO_DATE(a.dscg_time,"%Y-%m-%d") >= STR_TO_DATE(#{queryParam.lastMonth_cy_start_date},"%Y-%m-%d") ]]>
        AND <![CDATA[ STR_TO_DATE(a.dscg_time,"%Y-%m-%d") <= STR_TO_DATE(#{queryParam.lastMonth_cy_start_date},"%Y-%m-%d") ]]>
        AND b.oprn_oprt_lv = '1'
        THEN 1 ELSE NULL END) AS oneLvlOprHosLastMonthNum,
        COUNT(CASE WHEN
        <![CDATA[ STR_TO_DATE(a.dscg_time,"%Y-%m-%d") >= STR_TO_DATE(#{queryParam.lastYear_cy_start_date},"%Y-%m-%d") ]]>
        AND <![CDATA[ STR_TO_DATE(a.dscg_time,"%Y-%m-%d") <= STR_TO_DATE(#{queryParam.lastYear_cy_start_date},"%Y-%m-%d") ]]>
        AND b.oprn_oprt_lv = '1'
        THEN 1 ELSE NULL END) AS oneLvlOprHosLastYearNum,
        COUNT(CASE WHEN
        <![CDATA[ STR_TO_DATE(a.dscg_time,"%Y-%m-%d") >= STR_TO_DATE(#{queryParam.cy_start_date},"%Y-%m-%d") ]]>
        AND <![CDATA[ STR_TO_DATE(a.dscg_time,"%Y-%m-%d") <= STR_TO_DATE(CONCAT(#{queryParam.cy_end_date},' 23:59:59'),"%Y-%m-%d") ]]>
        AND c.oprn_lv = '1'
        THEN 1 ELSE NULL END) AS oneLvlOprStandradNum,
        COUNT(CASE WHEN
        <![CDATA[ STR_TO_DATE(a.dscg_time,"%Y-%m-%d") >= STR_TO_DATE(#{queryParam.cy_start_date},"%Y-%m-%d") ]]>
        AND <![CDATA[ STR_TO_DATE(a.dscg_time,"%Y-%m-%d") <= STR_TO_DATE(CONCAT(#{queryParam.cy_end_date},' 23:59:59'),"%Y-%m-%d") ]]>
        AND b.oprn_oprt_lv = '2'
        THEN 1 ELSE NULL END) AS twoLvlOprHosIssueNum,
        COUNT(CASE WHEN
        <![CDATA[ STR_TO_DATE(a.dscg_time,"%Y-%m-%d") >= STR_TO_DATE(#{queryParam.lastMonth_cy_start_date},"%Y-%m-%d") ]]>
        AND <![CDATA[ STR_TO_DATE(a.dscg_time,"%Y-%m-%d") <= STR_TO_DATE(#{queryParam.lastMonth_cy_start_date},"%Y-%m-%d") ]]>
        AND b.oprn_oprt_lv = '2'
        THEN 1 ELSE NULL END) AS twoLvlOprHosLastMonthNum,
        COUNT(CASE WHEN
        <![CDATA[ STR_TO_DATE(a.dscg_time,"%Y-%m-%d") >= STR_TO_DATE(#{queryParam.lastYear_cy_start_date},"%Y-%m-%d") ]]>
        AND <![CDATA[ STR_TO_DATE(a.dscg_time,"%Y-%m-%d") <= STR_TO_DATE(#{queryParam.lastYear_cy_start_date},"%Y-%m-%d") ]]>
        AND b.oprn_oprt_lv = '2'
        THEN 1 ELSE NULL END) AS twoLvlOprHosLastYearNum,
        COUNT(CASE WHEN
        <![CDATA[ STR_TO_DATE(a.dscg_time,"%Y-%m-%d") >= STR_TO_DATE(#{queryParam.cy_start_date},"%Y-%m-%d") ]]>
        AND <![CDATA[ STR_TO_DATE(a.dscg_time,"%Y-%m-%d") <= STR_TO_DATE(CONCAT(#{queryParam.cy_end_date},' 23:59:59'),"%Y-%m-%d") ]]>
        AND c.oprn_lv = '2'
        THEN 1 ELSE NULL END) AS twoLvlOprStandradNum,
        COUNT(CASE WHEN
        <![CDATA[ STR_TO_DATE(a.dscg_time,"%Y-%m-%d") >= STR_TO_DATE(#{queryParam.cy_start_date},"%Y-%m-%d") ]]>
        AND <![CDATA[ STR_TO_DATE(a.dscg_time,"%Y-%m-%d") <= STR_TO_DATE(CONCAT(#{queryParam.cy_end_date},' 23:59:59'),"%Y-%m-%d") ]]>
        AND b.oprn_oprt_lv = '3'
        THEN 1 ELSE NULL END) AS threeLvlOprHosIssueNum,
        COUNT(CASE WHEN
        <![CDATA[ STR_TO_DATE(a.dscg_time,"%Y-%m-%d") >= STR_TO_DATE(#{queryParam.lastMonth_cy_start_date},"%Y-%m-%d") ]]>
        AND <![CDATA[ STR_TO_DATE(a.dscg_time,"%Y-%m-%d") <= STR_TO_DATE(#{queryParam.lastMonth_cy_start_date},"%Y-%m-%d") ]]>
        AND b.oprn_oprt_lv = '3'
        THEN 1 ELSE NULL END) AS threeLvlOprHosLastMonthNum,
        COUNT(CASE WHEN
        <![CDATA[ STR_TO_DATE(a.dscg_time,"%Y-%m-%d") >= STR_TO_DATE(#{queryParam.lastYear_cy_start_date},"%Y-%m-%d") ]]>
        AND <![CDATA[ STR_TO_DATE(a.dscg_time,"%Y-%m-%d") <= STR_TO_DATE(#{queryParam.lastYear_cy_start_date},"%Y-%m-%d") ]]>
        AND b.oprn_oprt_lv = '3'
        THEN 1 ELSE NULL END) AS threeLvlOprHosLastYearNum,
        COUNT(CASE WHEN
        <![CDATA[ STR_TO_DATE(a.dscg_time,"%Y-%m-%d") >= STR_TO_DATE(#{queryParam.cy_start_date},"%Y-%m-%d") ]]>
        AND <![CDATA[ STR_TO_DATE(a.dscg_time,"%Y-%m-%d") <= STR_TO_DATE(CONCAT(#{queryParam.cy_end_date},' 23:59:59'),"%Y-%m-%d") ]]>
        AND c.oprn_lv = '3'
        THEN 1 ELSE NULL END) AS threeLvlOprStandradNum,
        COUNT(CASE WHEN
        <![CDATA[ STR_TO_DATE(a.dscg_time,"%Y-%m-%d") >= STR_TO_DATE(#{queryParam.cy_start_date},"%Y-%m-%d") ]]>
        AND <![CDATA[ STR_TO_DATE(a.dscg_time,"%Y-%m-%d") <= STR_TO_DATE(CONCAT(#{queryParam.cy_end_date},' 23:59:59'),"%Y-%m-%d") ]]>
        AND b.oprn_oprt_lv = '4'
        THEN 1 ELSE NULL END) AS fourLvlOprHosIssueNum,
        COUNT(CASE WHEN
        <![CDATA[ STR_TO_DATE(a.dscg_time,"%Y-%m-%d") >= STR_TO_DATE(#{queryParam.lastMonth_cy_start_date},"%Y-%m-%d") ]]>
        AND <![CDATA[ STR_TO_DATE(a.dscg_time,"%Y-%m-%d") <= STR_TO_DATE(#{queryParam.lastMonth_cy_start_date},"%Y-%m-%d") ]]>
        AND b.oprn_oprt_lv = '4'
        THEN 1 ELSE NULL END) AS fourLvlOprHosLastMonthNum,
        COUNT(CASE WHEN
        <![CDATA[ STR_TO_DATE(a.dscg_time,"%Y-%m-%d") >= STR_TO_DATE(#{queryParam.lastYear_cy_start_date},"%Y-%m-%d") ]]>
        AND <![CDATA[ STR_TO_DATE(a.dscg_time,"%Y-%m-%d") <= STR_TO_DATE(#{queryParam.lastYear_cy_start_date},"%Y-%m-%d") ]]>
        AND b.oprn_oprt_lv = '4'
        THEN 1 ELSE NULL END) AS fourLvlOprHosLastYearNum,
        COUNT(CASE WHEN
        <![CDATA[ STR_TO_DATE(a.dscg_time,"%Y-%m-%d") >= STR_TO_DATE(#{queryParam.cy_start_date},"%Y-%m-%d") ]]>
        AND <![CDATA[ STR_TO_DATE(a.dscg_time,"%Y-%m-%d") <= STR_TO_DATE(CONCAT(#{queryParam.cy_end_date},' 23:59:59'),"%Y-%m-%d") ]]>
        AND c.oprn_lv = '4'
        THEN 1 ELSE NULL END) AS fourLvlOprStandradNum,
        COUNT(CASE WHEN
        <![CDATA[ STR_TO_DATE(a.dscg_time,"%Y-%m-%d") >= STR_TO_DATE(#{queryParam.cy_start_date},"%Y-%m-%d") ]]>
        AND <![CDATA[ STR_TO_DATE(a.dscg_time,"%Y-%m-%d") <= STR_TO_DATE(CONCAT(#{queryParam.cy_end_date},' 23:59:59'),"%Y-%m-%d") ]]>
        AND (b.oprn_oprt_lv not in ('1' ,'2' , '3' , '4') or b.oprn_oprt_lv is null)
        THEN 1 ELSE NULL END) AS otherLvlOprHosIssueNum,
        COUNT(CASE WHEN
        <![CDATA[ STR_TO_DATE(a.dscg_time,"%Y-%m-%d") >= STR_TO_DATE(#{queryParam.lastMonth_cy_start_date},"%Y-%m-%d") ]]>
        AND <![CDATA[ STR_TO_DATE(a.dscg_time,"%Y-%m-%d") <= STR_TO_DATE(#{queryParam.lastMonth_cy_start_date},"%Y-%m-%d") ]]>
        AND (b.oprn_oprt_lv not in ('1' ,'2' , '3' , '4') or b.oprn_oprt_lv is null)
        THEN 1 ELSE NULL END) AS otherLvlOprHosLastMonthNum,
        COUNT(CASE WHEN
        <![CDATA[ STR_TO_DATE(a.dscg_time,"%Y-%m-%d") >= STR_TO_DATE(#{queryParam.lastYear_cy_start_date},"%Y-%m-%d") ]]>
        AND <![CDATA[ STR_TO_DATE(a.dscg_time,"%Y-%m-%d") <= STR_TO_DATE(#{queryParam.lastYear_cy_start_date},"%Y-%m-%d") ]]>
        AND (b.oprn_oprt_lv not in ('1' ,'2' , '3' , '4') or b.oprn_oprt_lv is null)
        THEN 1 ELSE NULL END) AS otherLvlOprHosLastYearNum,
        COUNT(CASE WHEN
        <![CDATA[ STR_TO_DATE(a.dscg_time,"%Y-%m-%d") >= STR_TO_DATE(#{queryParam.cy_start_date},"%Y-%m-%d") ]]>
        AND <![CDATA[ STR_TO_DATE(a.dscg_time,"%Y-%m-%d") <= STR_TO_DATE(CONCAT(#{queryParam.cy_end_date},' 23:59:59'),"%Y-%m-%d") ]]>
        AND (c.oprn_lv not in ('1' ,'2' , '3' , '4') or c.oprn_lv is null)
        THEN 1 ELSE NULL END) AS otherLvlOprStandradNum

        from som_drg_grp_info a inner join som_oprn_oprt_info b on a.SETTLE_LIST_ID = b.SETTLE_LIST_ID
        left join som_oprn_lv c on b.c35c = c.oprn_oprt_codg AND c.enab_flag = #{queryParam.enabFlag}
        AND c.ACTIVE_FLAG = #{queryParam.activeFlag}
        where b.C35C is not null AND b.C35C!='-' and  b.C35C!='--'
        <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
            AND  a.HOSPITAL_ID = #{queryParam.hospitalId}
        </if>
        <if test="queryParam.b16c!=null and queryParam.b16c!=''">
            AND a.dscg_caty_codg_inhosp = #{queryParam.b16c}
        </if>
        <if test="queryParam.queryDrg!=null and queryParam.queryDrg!=''">
            AND a.drg_codg = #{queryParam.queryDrg}
        </if>
        <if test="queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and
        queryParam.cy_end_date!=null and queryParam.cy_end_date!=''">
            <![CDATA[
            AND (
                (STR_TO_DATE(a.dscg_time,"%Y-%m-%d") >= STR_TO_DATE(#{queryParam.cy_start_date},"%Y-%m-%d")
                AND STR_TO_DATE(a.dscg_time,"%Y-%m-%d") <= STR_TO_DATE(CONCAT(#{queryParam.cy_end_date},' 23:59:59'),"%Y-%m-%d"))
                OR
                (STR_TO_DATE(a.dscg_time,"%Y-%m-%d") >= STR_TO_DATE(#{queryParam.lastMonth_cy_start_date},"%Y-%m-%d")
                AND STR_TO_DATE(a.dscg_time,"%Y-%m-%d") <= STR_TO_DATE(CONCAT(#{queryParam.lastMonth_cy_end_date},' 23:59:59'),"%Y-%m-%d"))
                OR
                (STR_TO_DATE(a.dscg_time,"%Y-%m-%d") >= STR_TO_DATE(#{queryParam.lastYear_cy_start_date},"%Y-%m-%d")
                AND STR_TO_DATE(a.dscg_time,"%Y-%m-%d") <= STR_TO_DATE(#{queryParam.lastYear_cy_start_date},"%Y-%m-%d"))
            )]]>
        </if>
        <if test="queryParam.doctorIdList!=null">
            AND (
            a.deptdrt_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.chfdr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.atddr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.ipdr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.resp_nurs_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.train_dr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.intn_dr in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.codr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.qltctrl_dr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.qltctrl_nurs_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            )
        </if>
    </select>

    <select id="getDiseaseInfo" resultType="java.util.Map">
        select
            IFNULL((x.a00b99+x.c00d48+x.d50d89+x.e00e90+x.f00f99+x.g00g99+x.h00h59+x.h60h95+x.i00i99+x.j00j99+x.k00k93+x.l00l99+x.m00m99+x.n00n99+x.o00o99+x.p00p96
            +x.q00q99+x.r00r99+x.s00t98+x.u00u99+x.v01y98+x.z00z99+x.other),0) AS totalPatients,
            x.*
        from(
            select
            IFNULL(COUNT(DISTINCT(case when SUBSTR(b.dscg_diag_codg,1,1)='A' or SUBSTR(b.dscg_diag_codg,1,1)='B' then a.SETTLE_LIST_ID else null end)),0) AS a00b99,
            <![CDATA[ IFNULL(COUNT(DISTINCT(case when (SUBSTR(b.dscg_diag_codg,1,1)='C') or
                       (SUBSTR(b.dscg_diag_codg,1,1)='D' and CONVERT(substr(b.dscg_diag_codg,2,2),SIGNED)>=0 and CONVERT(SUBSTR(b.dscg_diag_codg,2,2),SIGNED)<=48) then a.SETTLE_LIST_ID else null end)),0) ]]> AS c00d48,
            <![CDATA[ IFNULL(COUNT(DISTINCT(case when (SUBSTR(b.dscg_diag_codg,1,1)='D' and CONVERT(substr(b.dscg_diag_codg,2,2),SIGNED)>=50 and CONVERT(SUBSTR(b.dscg_diag_codg,2,2),SIGNED)<=89)
                        then a.SETTLE_LIST_ID else null end)),0) ]]> AS d50d89,
            <![CDATA[ IFNULL(COUNT(DISTINCT(case when (SUBSTR(b.dscg_diag_codg,1,1)='E' and CONVERT(substr(b.dscg_diag_codg,2,2),SIGNED)>=0 and CONVERT(SUBSTR(b.dscg_diag_codg,2,2),SIGNED)<=90)
                        then a.SETTLE_LIST_ID else null end)),0) ]]> AS e00e90,
            IFNULL(COUNT(DISTINCT(case when SUBSTR(b.dscg_diag_codg,1,1)='F' then a.SETTLE_LIST_ID else null end)),0) AS f00f99,
            IFNULL(COUNT(DISTINCT(case when SUBSTR(b.dscg_diag_codg,1,1)='G' then a.SETTLE_LIST_ID else null end)),0) AS g00g99,
            <![CDATA[ IFNULL(COUNT(DISTINCT(case when (SUBSTR(b.dscg_diag_codg,1,1)='H' and CONVERT(substr(b.dscg_diag_codg,2,2),SIGNED)>=0 and CONVERT(SUBSTR(b.dscg_diag_codg,2,2),SIGNED)<=59)
                        then a.SETTLE_LIST_ID else null end)),0) ]]> AS h00h59,
            <![CDATA[ IFNULL(COUNT(DISTINCT(case when (SUBSTR(b.dscg_diag_codg,1,1)='H' and CONVERT(substr(b.dscg_diag_codg,2,2),SIGNED)>=60 and CONVERT(SUBSTR(b.dscg_diag_codg,2,2),SIGNED)<=95)
                        then a.SETTLE_LIST_ID else null end)),0) ]]> AS h60h95,
            IFNULL(COUNT(DISTINCT(case when SUBSTR(b.dscg_diag_codg,1,1)='I' then a.SETTLE_LIST_ID else null end)),0) AS i00i99,
            IFNULL(COUNT(DISTINCT(case when SUBSTR(b.dscg_diag_codg,1,1)='J' then a.SETTLE_LIST_ID else null end)),0) AS j00j99,
            <![CDATA[ IFNULL(COUNT(DISTINCT(case when (SUBSTR(b.dscg_diag_codg,1,1)='K' and CONVERT(substr(b.dscg_diag_codg,2,2),SIGNED)>=0 and CONVERT(SUBSTR(b.dscg_diag_codg,2,2),SIGNED)<=93)
                        then a.SETTLE_LIST_ID else null end)),0) ]]> AS k00k93,
            IFNULL(COUNT(DISTINCT(case when SUBSTR(b.dscg_diag_codg,1,1)='L' then a.SETTLE_LIST_ID else null end)),0) AS l00l99,
            IFNULL(COUNT(DISTINCT(case when SUBSTR(b.dscg_diag_codg,1,1)='M' then a.SETTLE_LIST_ID else null end)),0) AS m00m99,
            IFNULL(COUNT(DISTINCT(case when SUBSTR(b.dscg_diag_codg,1,1)='N' then a.SETTLE_LIST_ID else null end)),0) AS n00n99,
            IFNULL(COUNT(DISTINCT(case when SUBSTR(b.dscg_diag_codg,1,1)='O' then a.SETTLE_LIST_ID else null end)),0) AS o00o99,
            <![CDATA[ IFNULL(COUNT(DISTINCT(case when (SUBSTR(b.dscg_diag_codg,1,1)='P' and CONVERT(substr(b.dscg_diag_codg,2,2),SIGNED)>=0 and CONVERT(SUBSTR(b.dscg_diag_codg,2,2),SIGNED)<=96)
                        then a.SETTLE_LIST_ID else null end)),0) ]]> AS p00p96,
            IFNULL(COUNT(DISTINCT(case when SUBSTR(b.dscg_diag_codg,1,1)='Q' then a.SETTLE_LIST_ID else null end)),0) AS q00q99,
            IFNULL(COUNT(DISTINCT(case when SUBSTR(b.dscg_diag_codg,1,1)='R' then a.SETTLE_LIST_ID else null end)),0) AS r00r99,
            <![CDATA[ IFNULL(COUNT(DISTINCT(case when (SUBSTR(b.dscg_diag_codg,1,1)='S') or
                        (SUBSTR(b.dscg_diag_codg,1,1)='T' and CONVERT(substr(b.dscg_diag_codg,2,2),SIGNED)>=0 and CONVERT(SUBSTR(b.dscg_diag_codg,2,2),SIGNED)<=98) then a.SETTLE_LIST_ID else null end)),0) ]]> AS s00t98,
            IFNULL(COUNT(DISTINCT(case when SUBSTR(b.dscg_diag_codg,1,1)='U' then a.SETTLE_LIST_ID else null end)),0) AS u00u99,
            <![CDATA[ IFNULL(COUNT(DISTINCT(case when (SUBSTR(b.dscg_diag_codg,1,1)='V') or (SUBSTR(b.dscg_diag_codg,1,1)='W') or (SUBSTR(b.dscg_diag_codg,1,1)='X') or
                        (SUBSTR(b.dscg_diag_codg,1,1)='Y' and CONVERT(substr(b.dscg_diag_codg,2,2),SIGNED)>=01 and CONVERT(SUBSTR(b.dscg_diag_codg,2,2),SIGNED)<=98) then a.SETTLE_LIST_ID else null end)),0) ]]> AS v01y98,
            IFNULL(COUNT(DISTINCT(case when SUBSTR(b.dscg_diag_codg,1,1)='Z' then a.SETTLE_LIST_ID else null end)),0) AS z00z99,
            <![CDATA[ IFNULL(COUNT(DISTINCT(case when (SUBSTR(b.dscg_diag_codg,1,3)='D49') or
                        (SUBSTR(b.dscg_diag_codg,1,1)='D' and CONVERT(substr(b.dscg_diag_codg,2,2),SIGNED)>=90 and CONVERT(SUBSTR(b.dscg_diag_codg,2,2),SIGNED)<=99) or
                        (SUBSTR(b.dscg_diag_codg,1,1)='E' and CONVERT(substr(b.dscg_diag_codg,2,2),SIGNED)>=91 and CONVERT(SUBSTR(b.dscg_diag_codg,2,2),SIGNED)<=99) or
                        (SUBSTR(b.dscg_diag_codg,1,1)='K' and CONVERT(substr(b.dscg_diag_codg,2,2),SIGNED)>=94 and CONVERT(SUBSTR(b.dscg_diag_codg,2,2),SIGNED)<=99) or
                        (SUBSTR(b.dscg_diag_codg,1,1)='P' and CONVERT(substr(b.dscg_diag_codg,2,2),SIGNED)>=97 and CONVERT(SUBSTR(b.dscg_diag_codg,2,2),SIGNED)<=99)
                        then a.SETTLE_LIST_ID else null end)),0)  ]]> AS other
            from som_drg_grp_info a
            inner join som_diag b on a.SETTLE_LIST_ID = b.SETTLE_LIST_ID
            where b.dscg_diag_codg is not null and b.dscg_diag_codg!='-' and  b.dscg_diag_codg!='--'
            <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
                AND  a.HOSPITAL_ID = #{queryParam.hospitalId}
            </if>
            <if test="queryParam.b16c!=null and queryParam.b16c!=''">
                AND a.dscg_caty_codg_inhosp = #{queryParam.b16c}
            </if>
            <if test="queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and
                    queryParam.cy_end_date!=null and queryParam.cy_end_date!=''">
                AND a.dscg_time BETWEEN #{queryParam.cy_start_date} and CONCAT(#{queryParam.cy_end_date},' 23:59:59')
            </if>
            <if test="queryParam.doctorIdList!=null">
                AND (
                a.deptdrt_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                a.chfdr_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                a.atddr_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                a.ipdr_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                a.resp_nurs_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                a.train_dr_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                a.intn_dr in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                a.codr_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                a.qltctrl_dr_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                a.qltctrl_nurs_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                )
            </if>
        )x
    </select>

    <select id="getDiseaseCostInfo" resultType="com.my.som.vo.firstPage.FirstPageDiseaseCostVo">
        select
            x.*,
            IFNULL((select IFNULL(ROUND(y.ave_inpf,2),0) from som_dise_community_fee_time_cosm_stt y where y.icd_codg = x.icdCodg
            AND y.ACTIVE_FLAG = #{queryParam.activeFlag} AND y.enab_flag = #{queryParam.enabFlag} AND y.hosp_lv = #{queryParam.hospLv} LIMIT 1),0)  AS mAvgCost,
            IFNULL((select IFNULL(ROUND(y.community_ave_inpf,2),0) from som_dise_community_fee_time_cosm_stt y where y.icd_codg = x.icdCodg
            AND y.grper_type = '1' AND y.ACTIVE_FLAG = #{queryParam.activeFlag} AND y.enab_flag = #{queryParam.enabFlag} AND y.hosp_lv = #{queryParam.hospLv}),0)  AS m1AvgCost,
            IFNULL((select IFNULL(ROUND(y.community_ave_inpf,2),0) from som_dise_community_fee_time_cosm_stt y where y.icd_codg = x.icdCodg
            AND y.grper_type = '2' AND y.ACTIVE_FLAG = #{queryParam.activeFlag} AND y.enab_flag = #{queryParam.enabFlag} AND y.hosp_lv = #{queryParam.hospLv}),0)  AS m2AvgCost,
            IFNULL((select IFNULL(ROUND(y.community_ave_inpf,2),0) from som_dise_community_fee_time_cosm_stt y where y.icd_codg = x.icdCodg
            AND y.grper_type = '3' AND y.ACTIVE_FLAG = #{queryParam.activeFlag} AND y.enab_flag = #{queryParam.enabFlag} AND y.hosp_lv = #{queryParam.hospLv}),0)  AS m3AvgCost,
            IFNULL((select IFNULL(ROUND(y.community_ave_inpf,2),0) from som_dise_community_fee_time_cosm_stt y where y.icd_codg = x.icdCodg
            AND y.grper_type = '4' AND y.grper_type = '4' AND y.ACTIVE_FLAG = #{queryParam.activeFlag} AND y.enab_flag = #{queryParam.enabFlag} AND y.hosp_lv = #{queryParam.hospLv}),0)  AS m4AvgCost,
            IFNULL((select IFNULL(ROUND(y.community_ave_inpf ,2),0) from som_dise_community_fee_time_cosm_stt y where y.icd_codg = x.icdCodg
            AND y.community_ave_ipt_days = '1' AND y.community_ave_ipt_days = '1' AND y.ACTIVE_FLAG = #{queryParam.activeFlag} AND y.enab_flag = #{queryParam.enabFlag} AND y.hosp_lv = #{queryParam.hospLv}),0)  AS m1AvgDays,
            IFNULL((select IFNULL(ROUND(y.community_ave_inpf ,2),0) from som_dise_community_fee_time_cosm_stt y where y.icd_codg = x.icdCodg
            AND y.community_ave_ipt_days = '2' AND y.community_ave_ipt_days = '2' AND y.ACTIVE_FLAG = #{queryParam.activeFlag} AND y.enab_flag = #{queryParam.enabFlag} AND y.hosp_lv = #{queryParam.hospLv}),0)  AS m2AvgDays,
            IFNULL((select IFNULL(ROUND(y.community_ave_inpf,2),0) from som_dise_community_fee_time_cosm_stt y where y.icd_codg = x.icdCodg
            AND y.community_ave_ipt_days = '3' AND y.community_ave_ipt_days = '3' AND y.ACTIVE_FLAG = #{queryParam.activeFlag} AND y.enab_flag = #{queryParam.enabFlag} AND y.hosp_lv = #{queryParam.hospLv}),0)  AS m3AvgDays,
            IFNULL((select IFNULL(ROUND(y.community_ave_inpf ,2),0) from som_dise_community_fee_time_cosm_stt y where y.icd_codg = x.icdCodg
            AND y.community_ave_ipt_days = '4' AND y.community_ave_ipt_days = '4' AND y.ACTIVE_FLAG = #{queryParam.activeFlag} AND y.enab_flag = #{queryParam.enabFlag} AND y.hosp_lv = #{queryParam.hospLv}),0)  AS m4AvgDays
        from(
            select
                b.dscg_diag_codg AS icdCodg,
                CONCAT(b.dscg_diag_codg,b.dscg_diag_name) AS icdCodeAndName,
                IFNULL(COUNT(distinct a.SETTLE_LIST_ID), 0) AS totalPatients,
                IFNULL(ROUND(AVG(ipt_sumfee), 2),0) AS avgCost,
                IFNULL(ROUND(AVG(act_ipt), 2),0) AS avgInHosDays
            from som_drg_grp_info a
            inner join som_diag b on a.SETTLE_LIST_ID = b.SETTLE_LIST_ID and a.medcas_type = b.TYPE
            where b.dscg_diag_codg is not null and b.dscg_diag_codg!='-' and  b.dscg_diag_codg!='--'
            <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
                AND  a.HOSPITAL_ID = #{queryParam.hospitalId}
            </if>
            <if test="queryParam.b16c!=null and queryParam.b16c!=''">
                AND a.dscg_caty_codg_inhosp = #{queryParam.b16c}
            </if>
            <if test="queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and
                queryParam.cy_end_date!=null and queryParam.cy_end_date!=''">
                AND a.dscg_time BETWEEN #{queryParam.cy_start_date} and CONCAT(#{queryParam.cy_end_date},' 23:59:59')
            </if>
            <if test="queryParam.doctorIdList!=null">
                AND (
                a.deptdrt_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                a.chfdr_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                a.atddr_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                a.ipdr_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                a.resp_nurs_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                a.train_dr_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                a.intn_dr in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                a.codr_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                a.qltctrl_dr_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                a.qltctrl_nurs_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                )
            </if>
            GROUP BY b.dscg_diag_codg
        )x
        ORDER BY x.totalPatients desc
    </select>

    <select id="getDipCostInfo" resultType="com.my.som.vo.firstPage.FirstPageDipCostVo">
        select
            x.*,
            y.inHosAvgCost as inHosAvgCost,
            y.inHosAvgDays as inHosAvgDays
        from(
            select
                w.*
            from(
                select
                    a.NAME as name,
                    b.dip_codg AS dipCodg,
                    b.DIP_NAME AS dipName,
                    IFNULL(ROUND(a.ipt_sumfee, 2),0) AS inHosTotalCost,
                    IFNULL(ROUND(a.act_ipt, 2),0) AS inHosDays,
                    <![CDATA[
                        case when convert(AES_DECRYPT(UNHEX(c.dip_setl_fee),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) using utf8)=0 or convert(AES_DECRYPT(UNHEX(c.dip_setl_fee),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) using utf8) is null then '暂无参考标准'
                             when a.ipt_sumfee < convert(AES_DECRYPT(UNHEX(c.dip_setl_fee),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) using utf8)*e.control_fee_stsb_min_prop then '费用偏低'
                             when a.ipt_sumfee > convert(AES_DECRYPT(UNHEX(c.dip_setl_fee),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) using utf8)*e.control_fee_stsb_max_prop then '费用偏高'
                             when convert(AES_DECRYPT(UNHEX(c.dip_setl_fee),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) using utf8)*e.control_fee_stsb_min_prop <= a.ipt_sumfee <= convert(AES_DECRYPT(UNHEX(c.dip_setl_fee),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) using utf8)*e.control_fee_stsb_max_prop then '平稳区间'
                             else '无法预警'
                        end
                       ]]> AS costWarn
                from som_drg_grp_info a
                inner JOIN som_dip_grp_rcd b on a.SETTLE_LIST_ID = b.SETTLE_LIST_ID
                LEFT JOIN som_dip_standard c on b.dip_codg = c.dip_codg AND SUBSTR(a.dscg_time,1,4) = c.STANDARD_YEAR AND c.ACTIVE_FLAG = '1'
                cross join som_cd_control_fee_stsb_cfg e
                where 1=1
                <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
                    AND  a.HOSPITAL_ID = #{queryParam.hospitalId}
                </if>
                <if test="queryParam.b16c!=null and queryParam.b16c!=''">
                    AND ( a.dscg_caty_codg_inhosp = #{queryParam.b16c} or
                    a.dscg_caty_name_inhosp  = (select NAME from som_dept where TYPE='2' and CODE=#{queryParam.b16c} AND HOSPITAL_ID = #{queryParam.hospitalId} AND ACTIVE_FLAG='1')
                    )
                </if>
                <if test="queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and
                        queryParam.cy_end_date!=null and queryParam.cy_end_date!=''">
                    AND a.dscg_time BETWEEN #{queryParam.cy_start_date} and CONCAT(#{queryParam.cy_end_date},' 23:59:59')
                </if>
                <if test="queryParam.doctorIdList!=null">
                    AND (
                    a.deptdrt_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    a.chfdr_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    a.atddr_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    a.ipdr_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    a.resp_nurs_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    a.train_dr_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    a.intn_dr in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    a.codr_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    a.qltctrl_dr_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    a.qltctrl_nurs_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    )
                </if>
            )w
            where dipCodg is not null
        )x left join (
            select
                b.dip_codg AS dipCodg,
                b.DIP_NAME AS dipName,
                IFNULL(ROUND(avg(a.ipt_sumfee), 2),0) AS inHosAvgCost,
                IFNULL(ROUND(avg(a.act_ipt), 2),0) AS inHosAvgDays
            from som_drg_grp_info a
            inner JOIN som_dip_grp_rcd b on a.SETTLE_LIST_ID = b.SETTLE_LIST_ID
            where 1=1
            <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
                AND  a.HOSPITAL_ID = #{queryParam.hospitalId}
            </if>
            <if test="queryParam.b16c!=null and queryParam.b16c!=''">
                AND ( a.dscg_caty_codg_inhosp = #{queryParam.b16c} or
                a.dscg_caty_name_inhosp  = (select NAME from som_dept where TYPE='2' and CODE=#{queryParam.b16c} AND HOSPITAL_ID = #{queryParam.hospitalId} AND ACTIVE_FLAG='1')
                )
            </if>
            <if test="queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and
                        queryParam.cy_end_date!=null and queryParam.cy_end_date!=''">
                AND dscg_time BETWEEN #{queryParam.cy_start_date} and CONCAT(#{queryParam.cy_end_date},' 23:59:59')
            </if>
            <if test="queryParam.doctorIdList!=null">
                AND (
                a.deptdrt_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                a.chfdr_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                a.atddr_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                a.ipdr_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                a.resp_nurs_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                a.train_dr_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                a.intn_dr in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                a.codr_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                a.qltctrl_dr_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                a.qltctrl_nurs_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                )
            </if>
            GROUP BY  b.dip_codg,b.DIP_NAME
        )y on x.dipCodg = y.dipCodg
        order by x.costWarn desc,x.dipCodg
    </select>

    <select id="getIndexTop10DrgsInfo" resultType="com.my.som.vo.firstPage.FirstPageCountVo">
        select
            CONCAT(drg_codg,DRG_NAME) AS drgsCodeAndName,
            COUNT(1) AS outHosMedicalRecordNum, <!--入组病案数-->
            ROUND(IFNULL(drg_wt,0), 2) AS drgWt,  <!--权重-->
            ROUND(IFNULL(SUM(drg_wt),0),2) AS totalWeight, <!--总权重-->
            ROUND(IFNULL(standard_avg_fee,0), 2) AS avgCost, <!--平均住院费用-->
            ROUND(IFNULL(standard_ave_hosp_day,0), 2) AS avgDays, <!--平均住院日-->
            ROUND(IFNULL(sum(IFNULL(act_ipt,0)/NULLIF(standard_ave_hosp_day, 0))/COUNT(1),0),2) as timeIndex, <!--  时间消耗指数   -->
            ROUND(IFNULL(sum(IFNULL(ipt_sumfee,0)/NULLIF(standard_avg_fee, 0))/COUNT(1),0),2) as costIndex  <!--  费用消耗指数   -->
        from som_drg_grp_info
        where grp_stas = '1'
        <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
            AND  HOSPITAL_ID = #{queryParam.hospitalId}
        </if>
        <if test="queryParam.b16c!=null and queryParam.b16c!=''">
            AND dscg_caty_codg_inhosp = #{queryParam.b16c}
        </if>
        <if test="queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and
        queryParam.cy_end_date!=null and queryParam.cy_end_date!=''">
            AND dscg_time BETWEEN #{queryParam.cy_start_date} and CONCAT(#{queryParam.cy_end_date},' 23:59:59')
        </if>
        <if test="queryParam.doctorIdList!=null">
            AND (
            deptdrt_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            chfdr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            atddr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            ipdr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            resp_nurs_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            train_dr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            intn_dr in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            codr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            qltctrl_dr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            qltctrl_nurs_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            )
        </if>
        GROUP BY CONCAT(drg_codg,DRG_NAME)
    </select>

    <select id="getIndexTop10DipInfo" resultType="com.my.som.vo.firstPage.FirstPageCountVo">
        select
            CONCAT(a.dip_codg,a.DIP_NAME) AS dipCodeAndName,
            COUNT(1) AS outHosMedicalRecordNum, <!--入组病案数-->
            ROUND(IFNULL(avg(ipt_sumfee),0), 2) AS avgCost <!--平均住院费用-->
        from som_dip_grp_rcd a
        inner join som_drg_grp_info b on a.SETTLE_LIST_ID = b.SETTLE_LIST_ID
        where a.grp_stas = '1'
        <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
            AND  b.HOSPITAL_ID = #{queryParam.hospitalId}
        </if>
        <if test="queryParam.b16c!=null and queryParam.b16c!=''">
            AND b.dscg_caty_codg_inhosp = #{queryParam.b16c}
        </if>
        <if test="queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and
        queryParam.cy_end_date!=null and queryParam.cy_end_date!=''">
            AND b.dscg_time BETWEEN #{queryParam.cy_start_date} and CONCAT(#{queryParam.cy_end_date},' 23:59:59')
        </if>
        <if test="queryParam.doctorIdList!=null">
            AND (
            b.deptdrt_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            b.chfdr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            b.atddr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            b.ipdr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            b.resp_nurs_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            b.train_dr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            b.intn_dr in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            b.codr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            b.qltctrl_dr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            b.qltctrl_nurs_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            )
        </if>
        GROUP BY CONCAT(a.dip_codg,a.DIP_NAME)
    </select>

    <select id="getSettleListMedicalCostCountInfo" resultType="com.my.som.vo.firstPage.SettleListMedicalCostCountVo">
        select
          med_chrg_itemname AS medChrgItemname,
          IFNULL(SUM(amt),0) AS itemTotalCost,
          IFNULL(count(CASE WHEN amt > 0 THEN 1 ELSE NULL END),0) AS itemTotalPatient
        from som_hi_setl_invy_med_fee_info
        where hi_setl_invy_id in (
            select SETTLE_LIST_ID
            from som_drg_grp_info a
            WHERE 1=1
            <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
                AND  a.HOSPITAL_ID = #{queryParam.hospitalId}
            </if>
            <if test="queryParam.b16c!=null and queryParam.b16c!=''">
                AND a.dscg_caty_codg_inhosp = #{queryParam.b16c}
            </if>
            <if test="queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and queryParam.cy_end_date!=null and queryParam.cy_end_date!=''">
                AND a.dscg_time BETWEEN #{queryParam.cy_start_date} and CONCAT(#{queryParam.cy_end_date},' 23:59:59')
            </if>
            <if test="queryParam.doctorIdList!=null">
                AND (
                a.deptdrt_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                a.chfdr_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                a.atddr_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                a.ipdr_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                a.resp_nurs_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                a.train_dr_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                a.intn_dr in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                a.codr_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                a.qltctrl_dr_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                a.qltctrl_nurs_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                )
            </if>
        )
        group by med_chrg_itemname
    </select>
</mapper>
