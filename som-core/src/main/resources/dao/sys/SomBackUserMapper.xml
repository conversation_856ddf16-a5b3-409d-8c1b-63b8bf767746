<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.som.dao.sys.SomBackUserMapper">
  <resultMap id="BaseResultMap" type="com.my.som.common.vo.SomBackUser">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="username" jdbcType="VARCHAR" property="username" />
    <result column="nknm" jdbcType="VARCHAR" property="nknm" />
    <result column="password" jdbcType="VARCHAR" property="password" />
    <result column="user_err_pwd_cnt" jdbcType="SMALLINT" property="userErrPwdCnt" />
    <result column="user_updt_time" jdbcType="TIMESTAMP" property="userUpdtTime" />
    <result column="is_lck_user" jdbcType="SMALLINT" property="isLckUser" />
    <result column="argt_seq" jdbcType="SMALLINT" property="argtSeq" />
    <result column="icon" jdbcType="VARCHAR" property="icon" />
    <result column="email" jdbcType="VARCHAR" property="email" />
    <result column="memo_info" jdbcType="VARCHAR" property="memo_info" />
    <result column="crte_time" jdbcType="TIMESTAMP" property="crteTime" />
    <result column="expire_time" jdbcType="VARCHAR" property="expireTime" />
    <result column="last_lgin_time" jdbcType="TIMESTAMP" property="lastLginTime" />
    <result column="blng_org_org_id" jdbcType="VARCHAR" property="blngOrgOrgId" />
    <result column="blng_org_org_name" jdbcType="VARCHAR" property="blngOrgOrgName" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="message" jdbcType="VARCHAR" property="message" />
    <result column="fee_stas" jdbcType="INTEGER" property="feeStas" />
    <result column="hospital_id" jdbcType="VARCHAR" property="hospitalId" />
    <result column="dr_codg" jdbcType="VARCHAR" property="drCodg" />
    <result column="enable_se_ans" jdbcType="VARCHAR" property="enableSeAns" />
    <result column="hospital_name" jdbcType="VARCHAR" property="hospitalName" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    a.id, a.username, a.nknm, a.password, a.user_err_pwd_cnt, a.user_updt_time, a.is_lck_user,
    a.argt_seq, a.icon, a.email, a.memo_info, a.crte_time, convert(AES_DECRYPT(UNHEX(expire_time),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) using utf8) as expire_time, a.last_lgin_time, a.blng_org_org_id, a.blng_org_org_name,
    a.status,message,a.fee_stas, a.hospital_id, a.dr_codg,enable_se_ans
  </sql>
  <select id="selectByExample" parameterType="com.my.som.dto.SomBackUserExample" resultMap="BaseResultMap">
      select
      <if test="distinct">
          distinct
      </if>
      <include refid="Base_Column_List"/>
      ,b.org_name as hospital_name
      from som_back_user a
               left join som_bas_dept b on a.hospital_id = b.org_id
      <if test="_parameter != null">
          <include refid="Example_Where_Clause"/>
      </if>
      <if test="orderByClause != null">
          order by ${orderByClause}
      </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from som_back_user
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from som_back_user
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.my.som.dto.SomBackUserExample">
    delete from som_back_user
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.my.som.common.vo.SomBackUser">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into som_back_user (username, nknm, password,
      user_err_pwd_cnt, user_updt_time,
      is_lck_user, argt_seq, icon,
      email, memo_info, crte_time, expire_time,
      last_lgin_time, blng_org_org_id, blng_org_org_name,
      status,fee_stas,hospital_id,dr_codg)
    values (#{username,jdbcType=VARCHAR}, #{nknm,jdbcType=VARCHAR}, #{password,jdbcType=VARCHAR},
      #{userErrPwdCnt,jdbcType=SMALLINT}, #{userUpdtTime,jdbcType=TIMESTAMP},
      #{isLckUser,jdbcType=SMALLINT}, #{argtSeq,jdbcType=SMALLINT}, #{icon,jdbcType=VARCHAR},
      #{email,jdbcType=VARCHAR}, #{memo_info,jdbcType=VARCHAR}, #{crteTime,jdbcType=TIMESTAMP},
      HEX(AES_ENCRYPT(#{expireTime,jdbcType=VARCHAR},${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY})),
      #{lastLginTime,jdbcType=TIMESTAMP}, #{blngOrgOrgId,jdbcType=VARCHAR}, #{blngOrgOrgName,jdbcType=VARCHAR},
      #{status,jdbcType=INTEGER},#{feeStas,jdbcType=INTEGER},#{hospitalId,jdbcType=VARCHAR},#{drCodg})
  </insert>
  <insert id="insertSelective" parameterType="com.my.som.common.vo.SomBackUser">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into som_back_user
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="username != null">
        username,
      </if>
      <if test="nknm != null">
        nknm,
      </if>
      <if test="password != null">
        password,
      </if>
      <if test="userErrPwdCnt != null">
        user_err_pwd_cnt,
      </if>
      <if test="userUpdtTime != null">
        user_updt_time,
      </if>
      <if test="isLckUser != null">
        is_lck_user,
      </if>
      <if test="argtSeq != null">
        argt_seq,
      </if>
      <if test="icon != null">
        icon,
      </if>
      <if test="email != null">
        email,
      </if>
      <if test="memo_info != null">
        memo_info,
      </if>
      <if test="crteTime != null">
        crte_time,
      </if>
      <if test="expireTime != null">
        expire_time,
      </if>
      <if test="lastLginTime != null">
        last_lgin_time,
      </if>
      <if test="blngOrgOrgId != null">
        blng_org_org_id,
      </if>
      <if test="blngOrgOrgName != null">
        blng_org_org_name,
      </if>
      <if test="status != null">
        status,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="username != null">
        #{username,jdbcType=VARCHAR},
      </if>
      <if test="nknm != null">
        #{nknm,jdbcType=VARCHAR},
      </if>
      <if test="password != null">
        #{password,jdbcType=VARCHAR},
      </if>
      <if test="userErrPwdCnt != null">
        #{userErrPwdCnt,jdbcType=SMALLINT},
      </if>
      <if test="userUpdtTime != null">
        #{userUpdtTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isLckUser != null">
        #{isLckUser,jdbcType=SMALLINT},
      </if>
      <if test="argtSeq != null">
        #{argtSeq,jdbcType=SMALLINT},
      </if>
      <if test="icon != null">
        #{icon,jdbcType=VARCHAR},
      </if>
      <if test="email != null">
        #{email,jdbcType=VARCHAR},
      </if>
      <if test="memo_info != null">
        #{memo_info,jdbcType=VARCHAR},
      </if>
      <if test="crteTime != null">
        #{crteTime,jdbcType=TIMESTAMP},
      </if>
      <if test="expireTime != null">
        HEX(AES_ENCRYPT(#{expireTime,jdbcType=VARCHAR},${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY})),
      </if>
      <if test="lastLginTime != null">
        #{lastLginTime,jdbcType=TIMESTAMP},
      </if>
      <if test="blngOrgOrgId != null">
        #{blngOrgOrgId,jdbcType=VARCHAR},
      </if>
      <if test="blngOrgOrgName != null">
        #{blngOrgOrgName,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.my.som.dto.SomBackUserExample" resultType="java.lang.Long">
    select count(*) from som_back_user
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update som_back_user
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.username != null">
        username = #{record.username,jdbcType=VARCHAR},
      </if>
      <if test="record.nknm != null">
        nknm = #{record.nknm,jdbcType=VARCHAR},
      </if>
      <if test="record.password != null">
        password = #{record.password,jdbcType=VARCHAR},
      </if>
      <if test="record.userErrPwdCnt != null">
        user_err_pwd_cnt = #{record.userErrPwdCnt,jdbcType=SMALLINT},
      </if>
      <if test="record.userUpdtTime != null">
        user_updt_time = #{record.userUpdtTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.isLckUser != null">
        is_lck_user = #{record.isLckUser,jdbcType=SMALLINT},
      </if>
      <if test="record.argtSeq != null">
        argt_seq = #{record.argtSeq,jdbcType=SMALLINT},
      </if>
      <if test="record.icon != null">
        icon = #{record.icon,jdbcType=VARCHAR},
      </if>
      <if test="record.email != null">
        email = #{record.email,jdbcType=VARCHAR},
      </if>
      <if test="record.memo_info != null">
        memo_info = #{record.memo_info,jdbcType=VARCHAR},
      </if>
      <if test="record.crteTime != null">
        crte_time = #{record.crteTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.expireTime != null">
        expire_time = HEX(AES_ENCRYPT(#{record.expireTime,jdbcType=VARCHAR},${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY})),
      </if>
      <if test="record.lastLginTime != null">
        last_lgin_time = #{record.lastLginTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.blngOrgOrgId != null">
        blng_org_org_id = #{record.blngOrgOrgId,jdbcType=VARCHAR},
      </if>
      <if test="record.blngOrgOrgName != null">
        blng_org_org_name = #{record.blngOrgOrgName,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update som_back_user
    set id = #{record.id,jdbcType=BIGINT},
      username = #{record.username,jdbcType=VARCHAR},
      nknm = #{record.nknm,jdbcType=VARCHAR},
      password = #{record.password,jdbcType=VARCHAR},
      user_err_pwd_cnt = #{record.userErrPwdCnt,jdbcType=SMALLINT},
      user_updt_time = #{record.userUpdtTime,jdbcType=TIMESTAMP},
      is_lck_user = #{record.isLckUser,jdbcType=SMALLINT},
      argt_seq = #{record.argtSeq,jdbcType=SMALLINT},
      icon = #{record.icon,jdbcType=VARCHAR},
      email = #{record.email,jdbcType=VARCHAR},
      memo_info = #{record.memo_info,jdbcType=VARCHAR},
      crte_time = #{record.crteTime,jdbcType=TIMESTAMP},
      expire_time = HEX(AES_ENCRYPT(#{record.expireTime,jdbcType=VARCHAR},${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY})),
      last_lgin_time = #{record.lastLginTime,jdbcType=TIMESTAMP},
      blng_org_org_id = #{record.blngOrgOrgId,jdbcType=VARCHAR},
      blng_org_org_name = #{record.blngOrgOrgName,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.my.som.common.vo.SomBackUser">
    update som_back_user
    <set>
      <if test="username != null">
        username = #{username,jdbcType=VARCHAR},
      </if>
      <if test="nknm != null">
        nknm = #{nknm,jdbcType=VARCHAR},
      </if>
      <if test="password != null">
        password = #{password,jdbcType=VARCHAR},
      </if>
      <if test="userErrPwdCnt != null">
        user_err_pwd_cnt = #{userErrPwdCnt,jdbcType=SMALLINT},
      </if>
      <if test="userUpdtTime != null">
        user_updt_time = #{userUpdtTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isLckUser != null">
        is_lck_user = #{isLckUser,jdbcType=SMALLINT},
      </if>
      <if test="argtSeq != null">
        argt_seq = #{argtSeq,jdbcType=SMALLINT},
      </if>
      <if test="icon != null">
        icon = #{icon,jdbcType=VARCHAR},
      </if>
      <if test="email != null">
        email = #{email,jdbcType=VARCHAR},
      </if>
      <if test="memo_info != null">
        memo_info = #{memo_info,jdbcType=VARCHAR},
      </if>
      <if test="crteTime != null">
        crte_time = #{crteTime,jdbcType=TIMESTAMP},
      </if>
      <if test="expireTime != null">
        expire_time = HEX(AES_ENCRYPT(#{expireTime,jdbcType=VARCHAR},${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY})),
      </if>
      <if test="lastLginTime != null">
        last_lgin_time = #{lastLginTime,jdbcType=TIMESTAMP},
      </if>
      <if test="blngOrgOrgId != null">
        blng_org_org_id = #{blngOrgOrgId,jdbcType=VARCHAR},
        blng_org_org_name = (select NAME from som_dept where CODE = #{blngOrgOrgId,jdbcType=VARCHAR}),
      </if>
<!--      <if test="blngOrgOrgName != null">-->
<!--        blng_org_org_name = #{blngOrgOrgName,jdbcType=VARCHAR},-->
<!--      </if>-->
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
<!--  <update id="modifyInformation">-->
<!--    update som_back_user-->
<!--    <set>-->
<!--      nknm = #{nickname},-->
<!--      username = #{username}-->
<!--      <if test="password != null and password != ''">-->
<!--        <trim prefix=",">-->
<!--            password = #{password}-->
<!--        </trim>-->
<!--      </if>-->
<!--    </set>-->
<!--    where id = #{id}-->
<!--  </update>-->
  <update id="modifyInformation">
    update som_back_user set password = #{password} where username = #{username}
    <!-- <set> -->
      <!-- nknm = #{nickname}, -->
      <!-- <if test="password != null and password != ''"> -->
        <!-- <trim prefix=","> -->
    <!-- password = #{password} -->
        <!-- </trim> -->
      <!-- </if> -->
    <!-- </set> -->
    <!-- where username = #{username} -->
    <!-- id = #{id} -->
  </update>
  <update id="updateByPrimaryKey" parameterType="com.my.som.common.vo.SomBackUser">
    update som_back_user
    set username = #{username,jdbcType=VARCHAR},
      nknm = #{nknm,jdbcType=VARCHAR},
      password = #{password,jdbcType=VARCHAR},
      user_err_pwd_cnt = #{userErrPwdCnt,jdbcType=SMALLINT},
      user_updt_time = #{userUpdtTime,jdbcType=TIMESTAMP},
      is_lck_user = #{isLckUser,jdbcType=SMALLINT},
      argt_seq = #{argtSeq,jdbcType=SMALLINT},
      icon = #{icon,jdbcType=VARCHAR},
      email = #{email,jdbcType=VARCHAR},
      memo_info = #{memo_info,jdbcType=VARCHAR},
      crte_time = #{crteTime,jdbcType=TIMESTAMP},
      expire_time = HEX(AES_ENCRYPT(#{expireTime,jdbcType=VARCHAR},${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY})),
      last_lgin_time = #{lastLginTime,jdbcType=TIMESTAMP},
      blng_org_org_id = #{blngOrgOrgId,jdbcType=VARCHAR},
      blng_org_org_name = #{blngOrgOrgName,jdbcType=VARCHAR},
      status = #{status,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <!-- 修改用户信息 -->
  <update id="modifySysUserInfo">
    UPDATE som_back_user
    <set>
      enable_se_ans = #{activeFlag,jdbcType=VARCHAR}
    </set>
    WHERE username = #{username,jdbcType=VARCHAR}
  </update>

  <select id="selectMinExpireTime" resultType="java.lang.String" parameterType="com.my.som.common.vo.SomBackUser">
      select min(convert(AES_DECRYPT(UNHEX(expire_time),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) using utf8)) from som_back_user
      where username!=#{username,jdbcType=VARCHAR}
      and expire_time!=HEX(AES_ENCRYPT(#{expireTime,jdbcType=VARCHAR},${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}))
  </select>

  <insert id="batchInsertUser">
    insert into som_back_user (username, nknm, password,
                          user_err_pwd_cnt, user_updt_time,
                          is_lck_user, argt_seq, icon,
                          email, memo_info, crte_time, expire_time,
                          last_lgin_time, blng_org_org_id, blng_org_org_name,
                          status,fee_stas,hospital_id,dr_codg)
    values
    <foreach collection="list" item="item" separator=",">
      (
        #{item.username,jdbcType=VARCHAR}, #{item.nknm,jdbcType=VARCHAR}, #{item.password,jdbcType=VARCHAR},
        #{item.userErrPwdCnt,jdbcType=SMALLINT}, #{item.userUpdtTime,jdbcType=TIMESTAMP},
        #{item.isLckUser,jdbcType=SMALLINT}, #{item.argtSeq,jdbcType=SMALLINT}, #{item.icon,jdbcType=VARCHAR},
        #{item.email,jdbcType=VARCHAR}, #{item.memo_info,jdbcType=VARCHAR}, #{item.crteTime,jdbcType=TIMESTAMP},
        HEX(AES_ENCRYPT(#{item.expireTime,jdbcType=VARCHAR},${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY})),
        #{item.lastLginTime,jdbcType=TIMESTAMP}, #{item.blngOrgOrgId,jdbcType=VARCHAR}, #{item.blngOrgOrgName,jdbcType=VARCHAR},
        #{item.status,jdbcType=INTEGER},#{item.feeStas,jdbcType=INTEGER},#{item.hospitalId,jdbcType=VARCHAR},#{item.drCodg,jdbcType=VARCHAR}
      )
    </foreach>
  </insert>


  <select id="selectByUserName" resultType="java.lang.String">
    select id
    from som_back_user
    where nknm in
    <foreach collection="list" item="name" open="(" separator="," close=")">
        #{name}
  </foreach>
  </select>

  <select id="selectByWorkerPower" resultType="java.lang.String">
    select id
    from som_back_user
    where blng_org_org_id in
    <foreach collection="list" item="name" open="(" separator="," close=")">
      #{name}
    </foreach>
  </select>

  <select id="selectByWorkerName" resultType="com.my.som.common.vo.SysUserBase">
    SELECT
      id, username, nknm as nknm, password, user_err_pwd_cnt, user_updt_time, is_lck_user,
      argt_seq, icon, email, memo_info, crte_time,expire_time,last_lgin_time, blng_org_org_id as blngOrgOrgId, blng_org_org_name,
      status
    from som_back_user
  </select>
  <select id="selectByName" resultType="com.my.som.common.vo.SysUserBase">
    SELECT
      id, username, nknm as nknm, password, user_err_pwd_cnt, user_updt_time, is_lck_user,
      argt_seq, icon, email, memo_info, crte_time,expire_time,last_lgin_time, blng_org_org_id as blngOrgOrgId, blng_org_org_name,
      status
    from som_back_user where username = #{username} and nknm = #{nknm}
  </select>

  <select id="queryHospitalInfo" resultType="java.util.HashMap">
    select HOSPITAL_ID AS value,
           medins_name AS label
    from som_hosp_info
  </select>
</mapper>
