<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.som.dao.sys.SomRoleOrgMapper">
  <resultMap id="BaseResultMap" type="com.my.som.vo.SomRoleOrg">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="role_id" jdbcType="BIGINT" property="roleId" />
    <result column="dept_id" jdbcType="BIGINT" property="deptId" />
    <result column="crter" jdbcType="BIGINT" property="crter" />
    <result column="crte_time" jdbcType="TIMESTAMP" property="crteTime" />
    <result column="updt_psn" jdbcType="BIGINT" property="updtPsn" />
    <result column="updt_time" jdbcType="TIMESTAMP" property="updtTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, role_id, dept_id, crter, crte_time, updt_psn, updt_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from som_role_org
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from som_role_org
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.my.som.vo.SomRoleOrg">
    insert into som_role_org (id, role_id, dept_id, 
      crter, crte_time, updt_psn, 
      updt_time)
    values (#{id,jdbcType=BIGINT}, #{roleId,jdbcType=BIGINT}, #{deptId,jdbcType=BIGINT}, 
      #{crter,jdbcType=BIGINT}, #{crteTime,jdbcType=TIMESTAMP}, #{updtPsn,jdbcType=BIGINT}, 
      #{updtTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.my.som.vo.SomRoleOrg">
    insert into som_role_org
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="roleId != null">
        role_id,
      </if>
      <if test="deptId != null">
        dept_id,
      </if>
      <if test="crter != null">
        crter,
      </if>
      <if test="crteTime != null">
        crte_time,
      </if>
      <if test="updtPsn != null">
        updt_psn,
      </if>
      <if test="updtTime != null">
        updt_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="roleId != null">
        #{roleId,jdbcType=BIGINT},
      </if>
      <if test="deptId != null">
        #{deptId,jdbcType=BIGINT},
      </if>
      <if test="crter != null">
        #{crter,jdbcType=BIGINT},
      </if>
      <if test="crteTime != null">
        #{crteTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updtPsn != null">
        #{updtPsn,jdbcType=BIGINT},
      </if>
      <if test="updtTime != null">
        #{updtTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.my.som.vo.SomRoleOrg">
    update som_role_org
    <set>
      <if test="roleId != null">
        role_id = #{roleId,jdbcType=BIGINT},
      </if>
      <if test="deptId != null">
        dept_id = #{deptId,jdbcType=BIGINT},
      </if>
      <if test="crter != null">
        crter = #{crter,jdbcType=BIGINT},
      </if>
      <if test="crteTime != null">
        crte_time = #{crteTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updtPsn != null">
        updt_psn = #{updtPsn,jdbcType=BIGINT},
      </if>
      <if test="updtTime != null">
        updt_time = #{updtTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.my.som.vo.SomRoleOrg">
    update som_role_org
    set role_id = #{roleId,jdbcType=BIGINT},
      dept_id = #{deptId,jdbcType=BIGINT},
      crter = #{crter,jdbcType=BIGINT},
      crte_time = #{crteTime,jdbcType=TIMESTAMP},
      updt_psn = #{updtPsn,jdbcType=BIGINT},
      updt_time = #{updtTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>