<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.som.dao.sys.SomMenuMgtMapper">
  <resultMap id="BaseResultMap" type="com.my.som.vo.SomMenuMgt">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="prnt_menu_id_lv1_menu" jdbcType="BIGINT" property="prntMenuIdLv1Menu" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="url" jdbcType="VARCHAR" property="url" />
    <result column="auth" jdbcType="VARCHAR" property="auth" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="icon" jdbcType="VARCHAR" property="icon" />
    <result column="disp_seq" jdbcType="INTEGER" property="dispSeq" />
    <result column="crter" jdbcType="BIGINT" property="crter" />
    <result column="crte_time" jdbcType="TIMESTAMP" property="crteTime" />
    <result column="updt_psn" jdbcType="BIGINT" property="updtPsn" />
    <result column="updt_time" jdbcType="TIMESTAMP" property="updtTime" />
    <result column="is_del" jdbcType="TINYINT" property="isDel" />
    <result column="is_hide" jdbcType="VARCHAR" property="is_hide" />
    <result column="is_cahe" jdbcType="VARCHAR" property="is_cahe" />
    <result column="is_double_cahe" jdbcType="VARCHAR" property="isDoubleCahe" />
    <result column="is_admin_menu" jdbcType="VARCHAR" property="isAdminMenu" />
  </resultMap>
  <sql id="Base_Column_List">
    id, prnt_menu_id_lv1_menu, name, url, auth, type, icon, disp_seq, crter, crte_time,
    updt_psn, updt_time, is_del, is_hide, is_cahe, is_double_cahe, is_admin_menu
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from som_menu_mgt
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from som_menu_mgt
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.my.som.vo.SomMenuMgt">
    insert into som_menu_mgt (id, prnt_menu_id_lv1_menu, name,
      url, auth, type, icon,
      disp_seq, crter, crte_time,
      updt_psn, updt_time, is_del,
      is_hide)
    values (#{id,jdbcType=BIGINT}, #{prntMenuIdLv1Menu,jdbcType=BIGINT}, #{name,jdbcType=VARCHAR},
      #{url,jdbcType=VARCHAR}, #{auth,jdbcType=VARCHAR}, #{type,jdbcType=INTEGER}, #{icon,jdbcType=VARCHAR},
      #{dispSeq,jdbcType=INTEGER}, #{crter,jdbcType=BIGINT}, #{crteTime,jdbcType=TIMESTAMP},
      #{updtPsn,jdbcType=BIGINT}, #{updtTime,jdbcType=TIMESTAMP}, #{isDel,jdbcType=TINYINT},
      #{is_hide,jdbcType=VARCHAR})
      )
  </insert>
  <insert id="insertSelective" parameterType="com.my.som.vo.SomMenuMgt">
    insert into som_menu_mgt
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="prntMenuIdLv1Menu != null">
        prnt_menu_id_lv1_menu,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="url != null">
        url,
      </if>
      <if test="auth != null">
        auth,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="icon != null">
        icon,
      </if>
      <if test="dispSeq != null">
        disp_seq,
      </if>
      <if test="crter != null">
        crter,
      </if>
      <if test="crteTime != null">
        crte_time,
      </if>
      <if test="updtPsn != null">
        updt_psn,
      </if>
      <if test="updtTime != null">
        updt_time,
      </if>
      <if test="isDel != null">
        is_del,
      </if>
      <if test="is_hide != null">
        is_hide,
      </if>
      <if test="is_cahe != null">
        is_cahe,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="prntMenuIdLv1Menu != null">
        #{prntMenuIdLv1Menu,jdbcType=BIGINT},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="url != null">
        #{url,jdbcType=VARCHAR},
      </if>
      <if test="auth != null">
        #{auth,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="icon != null">
        #{icon,jdbcType=VARCHAR},
      </if>
      <if test="dispSeq != null">
        #{dispSeq,jdbcType=INTEGER},
      </if>
      <if test="crter != null">
        #{crter,jdbcType=BIGINT},
      </if>
      <if test="crteTime != null">
        #{crteTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updtPsn != null">
        #{updtPsn,jdbcType=BIGINT},
      </if>
      <if test="updtTime != null">
        #{updtTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDel != null">
        #{isDel,jdbcType=TINYINT},
      </if>
      <if test="is_hide != null">
        #{is_hide,jdbcType=VARCHAR},
      </if>
      <if test="is_cahe != null">
        #{is_cahe,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.my.som.vo.SomMenuMgt">
    update som_menu_mgt
    <set>
      <if test="prntMenuIdLv1Menu != null">
        prnt_menu_id_lv1_menu = #{prntMenuIdLv1Menu,jdbcType=BIGINT},
      </if>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="url != null">
        url = #{url,jdbcType=VARCHAR},
      </if>
      <if test="auth != null">
        auth = #{auth,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=INTEGER},
      </if>
      <if test="icon != null">
        icon = #{icon,jdbcType=VARCHAR},
      </if>
      <if test="dispSeq != null">
        disp_seq = #{dispSeq,jdbcType=INTEGER},
      </if>
      <if test="crter != null">
        crter = #{crter,jdbcType=BIGINT},
      </if>
      <if test="crteTime != null">
        crte_time = #{crteTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updtPsn != null">
        updt_psn = #{updtPsn,jdbcType=BIGINT},
      </if>
      <if test="updtTime != null">
        updt_time = #{updtTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDel != null">
        is_del = #{isDel,jdbcType=TINYINT},
      </if>
      <if test="is_hide != null">
        is_hide = #{is_hide,jdbcType=VARCHAR},
      </if>
      <if test="is_cahe != null">
        is_cahe = #{is_cahe,jdbcType=VARCHAR},
      </if>
      <if test="isDoubleCahe != null">
        is_double_cahe = #{isDoubleCahe,jdbcType=VARCHAR},
      </if>
      <if test="isAdminMenu != null">
        is_admin_menu = #{isAdminMenu,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.my.som.vo.SomMenuMgt">
    update som_menu_mgt
    set prnt_menu_id_lv1_menu = #{prntMenuIdLv1Menu,jdbcType=BIGINT},
      name = #{name,jdbcType=VARCHAR},
      url = #{url,jdbcType=VARCHAR},
      auth = #{auth,jdbcType=VARCHAR},
      type = #{type,jdbcType=INTEGER},
      icon = #{icon,jdbcType=VARCHAR},
      disp_seq = #{dispSeq,jdbcType=INTEGER},
      crter = #{crter,jdbcType=BIGINT},
      crte_time = #{crteTime,jdbcType=TIMESTAMP},
      updt_psn = #{updtPsn,jdbcType=BIGINT},
      updt_time = #{updtTime,jdbcType=TIMESTAMP},
      is_del = #{isDel,jdbcType=TINYINT},
      is_hide = #{is_hide,jdbcType=VARCHAR},
      is_cahe = #{is_cahe,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="findAll"  resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from som_menu_mgt
    where is_del = 0
    <if test="userName != null and userName != 'developer'">
      AND m.is_admin_menu = '0'
    </if>
  </select>
  <select id="findPage"  resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from som_menu_mgt
    where is_del = 0
  </select>
  <select id="findPageByName" parameterType="java.lang.String" resultMap="BaseResultMap">
  	<bind name="pattern" value="'%' + _parameter.name + '%'" />
    select
    <include refid="Base_Column_List" />
    from som_menu_mgt
    where name like #{pattern}
    and is_del = 0
  </select>

  <select id="findByUserName" parameterType="java.lang.String" resultMap="BaseResultMap">
  	select
      m.id, m.prnt_menu_id_lv1_menu, m.name,
      m.url, m.auth, m.type, m.icon,
      m.disp_seq, m.crter, m.crte_time,
      m.updt_psn, m.updt_time, m.is_del, m.is_hide, m.is_cahe
  	 from som_menu_mgt m, som_back_user u, som_user_role ur, som_role_menu rm
  	where
          u.id = ur.user_id
      and m.is_del = 0
      and rm.menu_id = m.id
      and ur.role_id = rm.role_id
      and u.username = #{userName,jdbcType=VARCHAR}
      <if test="userName != null and userName != 'developer'">
        AND m.is_admin_menu = '0'
      </if>
  </select>

  <select id="findByUserId" parameterType="java.lang.Long" resultMap="BaseResultMap">
  	select
      m.id, m.prnt_menu_id_lv1_menu, m.name,
      m.url, m.auth, m.type, m.icon,
      m.disp_seq, m.crter, m.crte_time,
      m.updt_psn, m.updt_time, m.is_del, m.is_hide, m.is_cahe
  	 from som_menu_mgt m, som_back_user u, som_user_role ur, som_role_menu rm
  	where
  	    u.id = ur.user_id
  	and m.is_del = 0
  	and rm.menu_id = m.id
  	and ur.role_id = rm.role_id
  	and u.id = #{userId,jdbcType=BIGINT}
  </select>

  <select id="findRoleMenus" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select m.id, m.prnt_menu_id_lv1_menu, m.name,
      m.url, m.auth, m.type, m.icon,
      m.disp_seq, m.crter, m.crte_time,
      m.updt_psn, m.updt_time, m.is_del, m.is_hide, m.is_cahe
    from som_menu_mgt m, som_role_menu rm
    where
        m.id = rm.menu_id
    and m.is_del = 0
    and rm.role_id = #{roleId,jdbcType=BIGINT}
  </select>

  <!-- 查询双重缓存数据 -->
  <select id="queryDoubleCacheData" resultType="com.my.som.vo.SomMenuMgt">
    select
    <include refid="Base_Column_List" />
    from som_menu_mgt
    where is_del = 0
    AND is_double_cahe = '1'
  </select>
</mapper>
