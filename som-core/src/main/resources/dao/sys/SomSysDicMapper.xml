<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.som.dao.sys.SomSysDicMapper">

    <resultMap id="BaseResultMap" type="com.my.som.vo.SomSysCode">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="data_val" jdbcType="VARCHAR" property="dataVal" />
        <result column="labl_name" jdbcType="VARCHAR" property="lablName" />
        <result column="code_type" jdbcType="VARCHAR" property="codeType" />
        <result column="dscr" jdbcType="VARCHAR" property="dscr" />
        <result column="srt" jdbcType="DECIMAL" property="srt" />
        <result column="crter" jdbcType="BIGINT" property="crter" />
        <result column="crte_time" jdbcType="TIMESTAMP" property="crteTime" />
        <result column="updt_psn" jdbcType="BIGINT" property="updtPsn" />
        <result column="updt_time" jdbcType="TIMESTAMP" property="updtTime" />
        <result column="memo_info" jdbcType="VARCHAR" property="memo_info" />
        <result column="is_del" jdbcType="TINYINT" property="isDel" />
    </resultMap>

    <sql id="Base_Column_List">
        id, data_val, labl_name, code_type, dscr, srt, crter, crte_time, updt_psn,
    updt_time, memo_info, is_del
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        DELETE FROM som_sys_dic
        WHERE id = #{id,jdbcType=BIGINT}
    </delete>

    <insert id="insertSelective" parameterType="com.my.som.vo.SomSysCode">
        INSERT INTO som_sys_dic
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="dataVal != null">
                data_val,
            </if>
            <if test="lablName != null">
                labl_name,
            </if>
            <if test="codeType != null">
                code_type,
            </if>
            <if test="dscr != null">
                dscr,
            </if>
            <if test="srt != null">
                srt,
            </if>
            <if test="crter != null">
                crter,
            </if>
            <if test="crteTime != null">
                crte_time,
            </if>
            <if test="updtPsn != null">
                updt_psn,
            </if>
            <if test="updtTime != null">
                updt_time,
            </if>
            <if test="memo_info != null">
                memo_info,
            </if>
            <if test="isDel != null">
                is_del,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="dataVal != null">
                #{dataVal,jdbcType=VARCHAR},
            </if>
            <if test="lablName != null">
                #{lablName,jdbcType=VARCHAR},
            </if>
            <if test="codeType != null">
                #{codeType,jdbcType=VARCHAR},
            </if>
            <if test="dscr != null">
                #{dscr,jdbcType=VARCHAR},
            </if>
            <if test="srt != null">
                #{srt,jdbcType=DECIMAL},
            </if>
            <if test="crter != null">
                #{crter,jdbcType=BIGINT},
            </if>
            <if test="crteTime != null">
                #{crteTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updtPsn != null">
                #{updtPsn,jdbcType=BIGINT},
            </if>
            <if test="updtTime != null">
                #{updtTime,jdbcType=TIMESTAMP},
            </if>
            <if test="memo_info != null">
                #{memo_info,jdbcType=VARCHAR},
            </if>
            <if test="isDel != null">
                #{isDel,jdbcType=TINYINT},
            </if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.my.som.vo.SomSysCode">
        UPDATE som_sys_dic
        <set>
            <if test="dataVal != null">
                data_val = #{dataVal,jdbcType=VARCHAR},
            </if>
            <if test="lablName != null">
                labl_name = #{lablName,jdbcType=VARCHAR},
            </if>
            <if test="codeType != null">
                code_type = #{codeType,jdbcType=VARCHAR},
            </if>
            <if test="dscr != null">
                dscr = #{dscr,jdbcType=VARCHAR},
            </if>
            <if test="srt != null">
                srt = #{srt,jdbcType=DECIMAL},
            </if>
            <if test="crter != null">
                crter = #{crter,jdbcType=BIGINT},
            </if>
            <if test="crteTime != null">
                crte_time = #{crteTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updtPsn != null">
                updt_psn = #{updtPsn,jdbcType=BIGINT},
            </if>
            <if test="updtTime != null">
                updt_time = #{updtTime,jdbcType=TIMESTAMP},
            </if>
            <if test="memo_info != null">
                memo_info = #{memo_info,jdbcType=VARCHAR},
            </if>
            <if test="isDel != null">
                is_del = #{isDel,jdbcType=TINYINT},
            </if>
        </set>
        WHERE id = #{id,jdbcType=BIGINT}
    </update>

    <select id="findPage" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM som_sys_dic
    </select>

    <select id="findPageByLabel" parameterType="java.lang.String" resultMap="BaseResultMap">
        <bind name="pattern" value="'%' + _parameter.lablName + '%'"/>
        SELECT
        <include refid="Base_Column_List" />
        FROM som_sys_dic
        WHERE (
            labl_name LIKE #{pattern}
         OR code_type LIKE #{pattern}
         OR dscr LIKE #{pattern}
        )
        AND is_del = '0'
    </select>
    <select id="selectByValue" resultType="com.my.som.vo.SomSysCode">
        select labl_name as lablName, data_val as dataVal
        from som_sys_dic
        where data_val = #{dataVal,jdbcType=VARCHAR} and code_type = 'QDJYCWLX'
    </select>
    <select id="queryDictByCodeType" resultType="com.my.som.vo.SomSysCode">
        select data_val as dataVal, labl_name as lablName
        from som_sys_dic
        where code_type = #{codeType}
    </select>

</mapper>