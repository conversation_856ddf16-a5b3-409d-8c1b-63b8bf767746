<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.som.dao.sys.SomRoleMgtMapper">
  <resultMap id="BaseResultMap" type="com.my.som.vo.SomRoleMgt">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="memo_info" jdbcType="VARCHAR" property="memo_info" />
    <result column="crter" jdbcType="BIGINT" property="crter" />
    <result column="crte_time" jdbcType="TIMESTAMP" property="crteTime" />
    <result column="updt_psn" jdbcType="BIGINT" property="updtPsn" />
    <result column="updt_time" jdbcType="TIMESTAMP" property="updtTime" />
    <result column="is_del" jdbcType="TINYINT" property="isDel" />
  </resultMap>
  <sql id="Base_Column_List">
    id, name, memo_info, crter, crte_time, updt_psn, updt_time, 
    is_del
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from som_role_mgt
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from som_role_mgt
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.my.som.vo.SomRoleMgt">
    insert into som_role_mgt (id, name, memo_info, 
      crter, crte_time, updt_psn, 
      updt_time, is_del)
    values (#{id,jdbcType=BIGINT}, #{name,jdbcType=VARCHAR}, #{memo_info,jdbcType=VARCHAR}, 
      #{crter,jdbcType=BIGINT}, #{crteTime,jdbcType=TIMESTAMP}, #{updtPsn,jdbcType=BIGINT}, 
      #{updtTime,jdbcType=TIMESTAMP}, #{isDel,jdbcType=TINYINT})
  </insert>
  <insert id="insertSelective" parameterType="com.my.som.vo.SomRoleMgt">
    insert into som_role_mgt
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="memo_info != null">
        memo_info,
      </if>
      <if test="crter != null">
        crter,
      </if>
      <if test="crteTime != null">
        crte_time,
      </if>
      <if test="updtPsn != null">
        updt_psn,
      </if>
      <if test="updtTime != null">
        updt_time,
      </if>
      <if test="isDel != null">
        is_del,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="memo_info != null">
        #{memo_info,jdbcType=VARCHAR},
      </if>
      <if test="crter != null">
        #{crter,jdbcType=BIGINT},
      </if>
      <if test="crteTime != null">
        #{crteTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updtPsn != null">
        #{updtPsn,jdbcType=BIGINT},
      </if>
      <if test="updtTime != null">
        #{updtTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDel != null">
        #{isDel,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.my.som.vo.SomRoleMgt">
    update som_role_mgt
    <set>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="memo_info != null">
        memo_info = #{memo_info,jdbcType=VARCHAR},
      </if>
      <if test="crter != null">
        crter = #{crter,jdbcType=BIGINT},
      </if>
      <if test="crteTime != null">
        crte_time = #{crteTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updtPsn != null">
        updt_psn = #{updtPsn,jdbcType=BIGINT},
      </if>
      <if test="updtTime != null">
        updt_time = #{updtTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDel != null">
        is_del = #{isDel,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.my.som.vo.SomRoleMgt">
    update som_role_mgt
    set name = #{name,jdbcType=VARCHAR},
      memo_info = #{memo_info,jdbcType=VARCHAR},
      crter = #{crter,jdbcType=BIGINT},
      crte_time = #{crteTime,jdbcType=TIMESTAMP},
      updt_psn = #{updtPsn,jdbcType=BIGINT},
      updt_time = #{updtTime,jdbcType=TIMESTAMP},
      is_del = #{isDel,jdbcType=TINYINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="findPage" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from som_role_mgt
  </select>
  <select id="findAll" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from som_role_mgt
  </select>
  <select id="findPageByName" parameterType="java.lang.String" resultMap="BaseResultMap">
  	<bind name="pattern" value="'%' + _parameter.name + '%'" />
  	select 
    <include refid="Base_Column_List" />
    from som_role_mgt
    where name like #{pattern}
  </select>
  <select id="findByName" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from som_role_mgt
    where name = #{name,jdbcType=VARCHAR}
  </select>
</mapper>