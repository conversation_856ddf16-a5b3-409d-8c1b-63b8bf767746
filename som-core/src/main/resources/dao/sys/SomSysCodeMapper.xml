<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.som.dao.sys.SomSysCodeMapper">
  <resultMap id="BaseResultMap" type="com.my.som.vo.SomSysCode">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="data_val" jdbcType="VARCHAR" property="dataVal" />
    <result column="labl_name" jdbcType="VARCHAR" property="lablName" />
    <result column="code_type" jdbcType="VARCHAR" property="codeType" />
    <result column="dscr" jdbcType="VARCHAR" property="dscr" />
    <result column="srt" jdbcType="DECIMAL" property="srt" />
    <result column="crter" jdbcType="BIGINT" property="crter" />
    <result column="crte_time" jdbcType="TIMESTAMP" property="crteTime" />
    <result column="updt_psn" jdbcType="BIGINT" property="updtPsn" />
    <result column="updt_time" jdbcType="TIMESTAMP" property="updtTime" />
    <result column="memo_info" jdbcType="VARCHAR" property="memo_info" />
    <result column="is_del" jdbcType="TINYINT" property="isDel" />
  </resultMap>
  <sql id="Base_Column_List">
    id, data_val, labl_name, code_type, dscr, srt, crter, crte_time, updt_psn,
    updt_time, memo_info, is_del
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from som_sys_code
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from som_sys_code
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.my.som.vo.SomSysCode">
    insert into som_sys_code (id, data_val, labl_name,
      code_type, dscr, srt,
      crter, crte_time, updt_psn, 
      updt_time, memo_info, is_del
      )
    values (#{id,jdbcType=BIGINT}, #{data_val,jdbcType=VARCHAR}, #{labl_name,jdbcType=VARCHAR},
      #{code_type,jdbcType=VARCHAR}, #{dscr,jdbcType=VARCHAR}, #{srt,jdbcType=DECIMAL},
      #{crter,jdbcType=BIGINT}, #{crteTime,jdbcType=TIMESTAMP}, #{updtPsn,jdbcType=BIGINT}, 
      #{updtTime,jdbcType=TIMESTAMP}, #{memo_info,jdbcType=VARCHAR}, #{isDel,jdbcType=TINYINT}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.my.som.vo.SomSysCode">
    insert into som_sys_code
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="dataVal != null">
        data_val,
      </if>
      <if test="lablName != null">
        labl_name,
      </if>
      <if test="codeType != null">
        code_type,
      </if>
      <if test="dscr != null">
        dscr,
      </if>
      <if test="srt != null">
        srt,
      </if>
      <if test="crter != null">
        crter,
      </if>
      <if test="crteTime != null">
        crte_time,
      </if>
      <if test="updtPsn != null">
        updt_psn,
      </if>
      <if test="updtTime != null">
        updt_time,
      </if>
      <if test="memo_info != null">
        memo_info,
      </if>
      <if test="isDel != null">
        is_del,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="dataVal != null">
        #{dataVal,jdbcType=VARCHAR},
      </if>
      <if test="lablName != null">
        #{lablName,jdbcType=VARCHAR},
      </if>
      <if test="codeType != null">
        #{codeType,jdbcType=VARCHAR},
      </if>
      <if test="dscr != null">
        #{dscr,jdbcType=VARCHAR},
      </if>
      <if test="srt != null">
        #{srt,jdbcType=DECIMAL},
      </if>
      <if test="crter != null">
        #{crter,jdbcType=BIGINT},
      </if>
      <if test="crteTime != null">
        #{crteTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updtPsn != null">
        #{updtPsn,jdbcType=BIGINT},
      </if>
      <if test="updtTime != null">
        #{updtTime,jdbcType=TIMESTAMP},
      </if>
      <if test="memo_info != null">
        #{memo_info,jdbcType=VARCHAR},
      </if>
      <if test="isDel != null">
        #{isDel,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.my.som.vo.SomSysCode">
    update som_sys_code
    <set>
      <if test="dataVal != null">
        data_val = #{dataVal,jdbcType=VARCHAR},
      </if>
      <if test="lablName != null">
        labl_name = #{lablName,jdbcType=VARCHAR},
      </if>
      <if test="codeType != null">
        code_type = #{codeType,jdbcType=VARCHAR},
      </if>
      <if test="dscr != null">
        dscr = #{dscr,jdbcType=VARCHAR},
      </if>
      <if test="srt != null">
        srt = #{srt,jdbcType=DECIMAL},
      </if>
      <if test="crter != null">
        crter = #{crter,jdbcType=BIGINT},
      </if>
      <if test="crteTime != null">
        crte_time = #{crteTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updtPsn != null">
        updt_psn = #{updtPsn,jdbcType=BIGINT},
      </if>
      <if test="updtTime != null">
        updt_time = #{updtTime,jdbcType=TIMESTAMP},
      </if>
      <if test="memo_info != null">
        memo_info = #{memo_info,jdbcType=VARCHAR},
      </if>
      <if test="isDel != null">
        is_del = #{isDel,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.my.som.vo.SomSysCode">
    update som_sys_code
    set
      data_val = #{dataVal,jdbcType=VARCHAR},
      labl_name = #{lablName,jdbcType=VARCHAR},
      code_type = #{codeType,jdbcType=VARCHAR},
      dscr = #{dscr,jdbcType=VARCHAR},
      srt = #{srt,jdbcType=DECIMAL},
      crter = #{crter,jdbcType=BIGINT},
      crte_time = #{crteTime,jdbcType=TIMESTAMP},
      updt_psn = #{updtPsn,jdbcType=BIGINT},
      updt_time = #{updtTime,jdbcType=TIMESTAMP},
      memo_info = #{memo_info,jdbcType=VARCHAR},
      is_del = #{isDel,jdbcType=TINYINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="findPage" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from som_sys_code
  </select>
  <select id="findPageByLabel" parameterType="java.lang.String" resultMap="BaseResultMap">
  	<bind name="pattern" value="'%' + _parameter.lablName + '%'" />
  	select 
    <include refid="Base_Column_List" />
    from som_sys_code
    where (labl_name like #{pattern}
    or code_type like #{pattern}
    or dscr like #{pattern}
    )
    and is_del = '0'
  </select>
  <select id="findByLabel" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from som_sys_code
    where (labl_name LIKE concat("%",#{lablName,jdbcType=VARCHAR},"%"))
    or (code_type LIKE concat("%",#{lablName,jdbcType=VARCHAR},"%"))
    or (dscr LIKE concat("%",#{lablName,jdbcType=VARCHAR},"%"))
  </select>
  <select id="queryDictByCodeType" resultType="com.my.som.vo.SomSysCode">
    select data_val as dataVal, labl_name as lablName
    from som_sys_code
    where code_type = #{codeType}
  </select>
</mapper>