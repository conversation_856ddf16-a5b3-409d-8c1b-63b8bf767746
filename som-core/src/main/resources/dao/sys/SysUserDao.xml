<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.som.dao.sys.SysUserDao">
    <resultMap id="BaseResultMap" type="com.my.som.common.vo.SomBackUser">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="username" jdbcType="VARCHAR" property="username"/>
        <result column="nknm" jdbcType="VARCHAR" property="nknm"/>
        <result column="password" jdbcType="VARCHAR" property="password"/>
        <result column="user_err_pwd_cnt" jdbcType="SMALLINT" property="userErrPwdCnt"/>
        <result column="user_updt_time" jdbcType="TIMESTAMP" property="userUpdtTime"/>
        <result column="is_lck_user" jdbcType="SMALLINT" property="isLckUser"/>
        <result column="argt_seq" jdbcType="SMALLINT" property="argtSeq"/>
        <result column="icon" jdbcType="VARCHAR" property="icon"/>
        <result column="email" jdbcType="VARCHAR" property="email"/>
        <result column="memo_info" jdbcType="VARCHAR" property="memo_info"/>
        <result column="crte_time" jdbcType="TIMESTAMP" property="crteTime"/>
        <result column="expire_time" jdbcType="VARCHAR" property="expireTime"/>
        <result column="last_lgin_time" jdbcType="TIMESTAMP" property="lastLginTime"/>
        <result column="blng_org_org_id" jdbcType="VARCHAR" property="blngOrgOrgId"/>
        <result column="blng_org_org_name" jdbcType="VARCHAR" property="blngOrgOrgName"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="hospital_id" jdbcType="VARCHAR" property="hospitalId"/>
    </resultMap>

    <!--根据orgid查询对应的用户列表-->
    <select id="queryUserByOrgId" resultMap="BaseResultMap">
        select a.id,
               a.username,
               a.nknm,
               a.password,
               a.user_err_pwd_cnt,
               DATE_FORMAT(a.user_updt_time, '%Y-%m-%d %H:%i:%s') as user_updt_time,
               a.is_lck_user,
               a.argt_seq,
               a.icon,
               a.email,
               a.memo_info,
               DATE_FORMAT(a.crte_time, '%Y-%m-%d %H:%i:%s')      as crte_time,
               convert(AES_DECRYPT(UNHEX(a.expire_time), ${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) using
                       utf8)                                      as expire_time,
               DATE_FORMAT(a.last_lgin_time, '%Y-%m-%d %H:%i:%s') as last_lgin_time,
               a.blng_org_org_id,
               b.org_name                                         as blng_org_org_name,
               a.status,
               a.hospital_id
        from som_back_user a
                 inner join som_bas_dept b on a.blng_org_org_id = b.org_id
        where a.status = 1
        <choose>
            <when test="queryParam.hasSubOrg == 1">
                and (a.blng_org_org_id = #{queryParam.blngOrgOrgId,jdbcType=VARCHAR}
                    or instr(b.dept_id_path, CONCAT(#{queryParam.blngOrgOrgId,jdbcType=VARCHAR}, '/')) > 0)
            </when>
            <otherwise>
                and a.blng_org_org_id = #{queryParam.blngOrgOrgId,jdbcType=VARCHAR}
            </otherwise>
        </choose>
        <if test="queryParam.username != null">
            and instr(a.username, #{queryParam.username,jdbcType=VARCHAR}) > 0
        </if>
        <if test="queryParam.nknm != null">
            and instr(a.nknm, #{queryParam.nknm,jdbcType=VARCHAR}) > 0
        </if>
        <if test="queryParam.hospitalId != null and queryParam.hospitalId != 'developer'">
            and a.hospital_id = #{queryParam.hospitalId}
        </if>
        order by b.org_id
    </select>
</mapper>
