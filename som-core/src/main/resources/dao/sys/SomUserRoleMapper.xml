<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.som.dao.sys.SomUserRoleMapper">
  <resultMap id="BaseResultMap" type="com.my.som.common.vo.SomUserRole">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="role_id" jdbcType="BIGINT" property="roleId" />
  </resultMap>
  <sql id="Base_Column_List">
    id, user_id, role_id
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from som_user_role
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from som_user_role
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.my.som.common.vo.SomUserRole">
    insert into som_user_role (id, user_id, role_id
      )
    values (#{id,jdbcType=BIGINT}, #{userId,jdbcType=BIGINT}, #{roleId,jdbcType=BIGINT}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.my.som.common.vo.SomUserRole">
    insert into som_user_role
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="roleId != null">
        role_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=BIGINT},
      </if>
      <if test="roleId != null">
        #{roleId,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>

  <!-- 批量新增用户角色 -->
  <insert id="batchInsertSysUserRole">
    INSERT INTO   som_user_role (user_id,role_id,crter,crte_time,updt_psn,updt_time)
    VALUES
    <foreach collection="list" item="item" separator=",">
           (
               #{item.userId,jdbcType=VARCHAR},
               #{item.roleId,jdbcType=VARCHAR},
               #{item.crter,jdbcType=VARCHAR},
               #{item.crteTime,jdbcType=VARCHAR},
               #{item.updtPsn,jdbcType=VARCHAR},
               #{item.updtTime,jdbcType=VARCHAR}
            )
    </foreach>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.my.som.common.vo.SomUserRole">
    update som_user_role
    <set>
      <if test="userId != null">
        user_id = #{userId,jdbcType=BIGINT},
      </if>
      <if test="roleId != null">
        role_id = #{roleId,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.my.som.common.vo.SomUserRole">
    update som_user_role
    set user_id = #{userId,jdbcType=BIGINT},
      role_id = #{roleId,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="findUserRoles" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from som_user_role
    where user_id = #{userId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByUserId" parameterType="java.lang.Long">
    delete from som_user_role
    where user_id = #{userId,jdbcType=BIGINT}
  </delete>
</mapper>