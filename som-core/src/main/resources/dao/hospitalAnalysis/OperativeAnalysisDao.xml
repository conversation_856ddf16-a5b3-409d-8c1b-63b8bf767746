<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.som.dao.hospitalAnalysis.OperativeAnalysisDao">
    <select id="list" resultType="com.my.som.vo.hospitalAnalysis.OperativeAnalysisInfo">
        select p.*
                <if test="queryParam.queryType==1">
                    <trim prefix=",">
                        IFNULL(m.name,'未填写科室') AS priOutHosDeptName
                    </trim>
                </if>
        from (
        select
            <if test="queryParam.queryType==1">
                dscg_caty_codg_inhosp AS priOutHosDeptCode,
                a.HOSPITAL_ID,
            </if>
            <if test="queryParam.queryType==2">
                drg_codg AS drgCodg,
                CONCAT(drg_codg,DRG_NAME) AS drgName,
            </if>
            COUNT(1) AS totalOprs,
            COUNT(CASE WHEN b.oprn_oprt_lv = '1' THEN 1 ELSE NULL END) AS oneLvlOprHos,
            IFNULL(CONCAT(ROUND(COUNT(CASE WHEN b.oprn_oprt_lv = '1' THEN 1 ELSE NULL END)/NULLIF(COUNT(1),0)*100,2),'%'),0) AS oneLvlOprHosRate,
            COUNT(CASE WHEN c.oprn_lv = '1' THEN 1 ELSE NULL END) AS oneLvlOprStan,
            IFNULL(CONCAT(ROUND(COUNT(CASE WHEN c.oprn_lv = '1' THEN 1 ELSE NULL END)/NULLIF(COUNT(1),0)*100,2),'%'),0) AS oneLvlOprStanRate,
            COUNT(CASE WHEN b.oprn_oprt_lv = '2' THEN 1 ELSE NULL END) AS twoLvlOprHos,
            IFNULL(CONCAT(ROUND(COUNT(CASE WHEN b.oprn_oprt_lv = '2' THEN 1 ELSE NULL END)/NULLIF(COUNT(1),0)*100,2),'%'),0) AS twoLvlOprHosRate,
            COUNT(CASE WHEN c.oprn_lv = '2' THEN 1 ELSE NULL END) AS twoLvlOprStan,
            IFNULL(CONCAT(ROUND(COUNT(CASE WHEN c.oprn_lv = '2' THEN 1 ELSE NULL END)/NULLIF(COUNT(1),0)*100,2),'%'),0) AS twoLvlOprStanRate,
            COUNT(CASE WHEN b.oprn_oprt_lv = '3' THEN 1 ELSE NULL END) AS threeLvlOprHos,
            IFNULL(CONCAT(ROUND(COUNT(CASE WHEN b.oprn_oprt_lv = '3' THEN 1 ELSE NULL END)/NULLIF(COUNT(1),0)*100,2),'%'),0) AS threeLvlOprHosRate,
            COUNT(CASE WHEN c.oprn_lv = '3' THEN 1 ELSE NULL END) AS threeLvlOprStan,
            IFNULL(CONCAT(ROUND(COUNT(CASE WHEN c.oprn_lv = '3' THEN 1 ELSE NULL END)/NULLIF(COUNT(1),0)*100,2),'%'),0) AS threeLvlOprStanRate,
            COUNT(CASE WHEN b.oprn_oprt_lv = '4' THEN 1 ELSE NULL END) AS fourLvlOprHos,
            IFNULL(CONCAT(ROUND(COUNT(CASE WHEN b.oprn_oprt_lv = '4' THEN 1 ELSE NULL END)/NULLIF(COUNT(1),0)*100,2),'%'),0) AS fourLvlOprHosRate,
            COUNT(CASE WHEN c.oprn_lv = '4' THEN 1 ELSE NULL END) AS fourLvlOprStan,
            IFNULL(CONCAT(ROUND(COUNT(CASE WHEN c.oprn_lv = '4' THEN 1 ELSE NULL END)/NULLIF(COUNT(1),0)*100,2),'%'),0) AS fourLvlOprStanRate,
            COUNT(CASE WHEN b.oprn_oprt_lv not in ('1' ,'2' , '3' , '4') or b.oprn_oprt_lv is null THEN 1 ELSE NULL END) AS otherLvlOprHos,
            IFNULL(CONCAT(ROUND(COUNT(CASE WHEN b.oprn_oprt_lv not in ('1' ,'2' , '3' , '4') or b.oprn_oprt_lv is null THEN 1 ELSE NULL END)/NULLIF(COUNT(1),0)*100,2),'%'),0) AS otherLvlOprHosRate,
            COUNT(CASE WHEN c.oprn_lv not in ('1' ,'2' , '3' , '4') or c.oprn_lv is null THEN 1 ELSE NULL END) AS otherOprStan,
            IFNULL(CONCAT(ROUND(COUNT(CASE WHEN c.oprn_lv not in ('1' ,'2' , '3' , '4') or c.oprn_lv is null THEN 1 ELSE NULL END)/
                NULLIF(COUNT(1),0)*100,2),'%'),0) AS otherOprStanRate
        from som_drg_grp_info a
        <if test='queryParam.enableSeAns != null and queryParam.enableSeAns != "" and queryParam.enableSeAns == "1"'>
            INNER JOIN som_hi_invy_bas_info a1 on a1.id=a.SETTLE_LIST_ID
            INNER JOIN som_setl_cas_crsp p on a1.k00=p.k00
        </if>
        inner join som_oprn_oprt_info b on a.SETTLE_LIST_ID = b.SETTLE_LIST_ID
        <!--left join som_oprn_lv c on b.c35c = c.oprn_oprt_codg-->
        inner join
        (
            SELECT y.oprn_oprt_codg,y.oprn_lv from som_codg_crsp x
            left join som_oprn_lv y
            on x.icd_codg=y.oprn_oprt_codg
            AND  x.icd_codg_ver='9'
            AND x.crsp_icd_codg_ver='10'
            AND y.enab_flag = #{queryParam.enabFlag} AND y.ACTIVE_FLAG = #{queryParam.activeFlag}
        )c on c.oprn_oprt_codg=b.C35C
        where  b.C35C is not null AND b.C35C!='-' and  b.C35C!='--'
        <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
            AND  a.HOSPITAL_ID = #{queryParam.hospitalId}
        </if>
        <if test="queryParam.b16c!=null and queryParam.b16c!=''">
            AND a.dscg_caty_codg_inhosp = #{queryParam.b16c}
        </if>
        <if test="queryParam.drCodg!=null and queryParam.drCodg!=''">
            AND a.ipdr_code = #{queryParam.drCodg}
        </if>
        <if test="queryParam.queryDrg!=null and queryParam.queryDrg!=''">
            AND a.drg_codg = #{queryParam.queryDrg}
        </if>
        <if test="queryParam.inStartTime != null and queryParam.inStartTime != '' and
                  queryParam.inEndTime != null and queryParam.inEndTime != ''">
            AND a.adm_time BETWEEN #{queryParam.inStartTime} and CONCAT(#{queryParam.inEndTime},' 23:59:59')
        </if>
        <if test="queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and
        queryParam.cy_end_date!=null and queryParam.cy_end_date!=''">
            AND a.dscg_time BETWEEN #{queryParam.cy_start_date} and CONCAT(#{queryParam.cy_end_date},' 23:59:59')
        </if>
        <if test="queryParam.seStartTime!=null and queryParam.seStartTime!='' and queryParam.seEndTime!=null and queryParam.seEndTime!=''">
            AND a.setl_end_time BETWEEN #{queryParam.seStartTime} and CONCAT(#{queryParam.seEndTime},' 23:59:59')
        </if>
        <if test="queryParam.doctorIdList!=null">
            AND (
            a.deptdrt_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.chfdr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.atddr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.ipdr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.resp_nurs_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.train_dr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.intn_dr in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.codr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.qltctrl_dr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.qltctrl_nurs_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            )
        </if>
        <if test="queryParam.queryType==1">
            GROUP BY a.dscg_caty_codg_inhosp,a.dscg_caty_name_inhosp, a.HOSPITAL_ID
        </if>
        <if test="queryParam.queryType==2">
            GROUP BY a.drg_codg,a.DRG_NAME
        </if>
        order by totalOprs desc
        ) p
        <if test="queryParam.queryType==1">
          left join som_dept m
          on p.priOutHosDeptCode = m.CODE
          AND p.HOSPITAL_ID = m.HOSPITAL_ID
        </if>

    </select>

    <select id="getCountInfo" resultType="java.util.Map">
        select
            COUNT(CASE WHEN a.${queryParam.busKeyField} BETWEEN #{queryParam.cy_start_date} and CONCAT(#{queryParam.cy_end_date},' 23:59:59')
                AND b.oprn_oprt_lv = '1'
                THEN 1 ELSE NULL END) AS oneLvlOprHosIssueNum,
            COUNT(CASE WHEN a.${queryParam.busKeyField} BETWEEN #{queryParam.lastMonth_cy_start_date} and CONCAT(CONCAT(#{queryParam.lastMonth_cy_end_date},' 23:59:59'),' 23:59:59')
                AND b.oprn_oprt_lv = '1'
                THEN 1 ELSE NULL END) AS oneLvlOprHosLastMonthNum,
            COUNT(CASE WHEN a.${queryParam.busKeyField} BETWEEN #{queryParam.lastYear_cy_start_date} and CONCAT(CONCAT(#{queryParam.lastYear_cy_end_date},' 23:59:59'),' 23:59:59')
                AND b.oprn_oprt_lv = '1'
                THEN 1 ELSE NULL END) AS oneLvlOprHosLastYearNum,
            COUNT(CASE WHEN  a.${queryParam.busKeyField} BETWEEN #{queryParam.cy_start_date} and CONCAT(#{queryParam.cy_end_date},' 23:59:59')
                AND c.oprn_lv = '1'
                THEN 1 ELSE NULL END) AS oneLvlOprStandradNum,
            COUNT(CASE WHEN a.${queryParam.busKeyField} BETWEEN #{queryParam.cy_start_date} and CONCAT(#{queryParam.cy_end_date},' 23:59:59')
                AND b.oprn_oprt_lv = '2'
                THEN 1 ELSE NULL END) AS twoLvlOprHosIssueNum,
            COUNT(CASE WHEN a.${queryParam.busKeyField} BETWEEN #{queryParam.lastMonth_cy_start_date} and CONCAT(#{queryParam.lastMonth_cy_end_date},' 23:59:59')
                AND b.oprn_oprt_lv = '2'
                THEN 1 ELSE NULL END) AS twoLvlOprHosLastMonthNum,
            COUNT(CASE WHEN  a.${queryParam.busKeyField} BETWEEN #{queryParam.lastYear_cy_start_date} and CONCAT(CONCAT(#{queryParam.lastYear_cy_end_date},' 23:59:59'),' 23:59:59')
                AND b.oprn_oprt_lv = '2'
                THEN 1 ELSE NULL END) AS twoLvlOprHosLastYearNum,
            COUNT(CASE WHEN a.${queryParam.busKeyField} BETWEEN #{queryParam.cy_start_date} and CONCAT(#{queryParam.cy_end_date},' 23:59:59')
                AND c.oprn_lv = '2'
                THEN 1 ELSE NULL END) AS twoLvlOprStandradNum,
            COUNT(CASE WHEN a.${queryParam.busKeyField} BETWEEN #{queryParam.cy_start_date} and CONCAT(#{queryParam.cy_end_date},' 23:59:59')
                AND b.oprn_oprt_lv = '3'
                THEN 1 ELSE NULL END) AS threeLvlOprHosIssueNum,
            COUNT(CASE WHEN  a.${queryParam.busKeyField} BETWEEN #{queryParam.lastMonth_cy_start_date} and CONCAT(#{queryParam.lastMonth_cy_end_date},' 23:59:59')
                AND b.oprn_oprt_lv = '3'
                THEN 1 ELSE NULL END) AS threeLvlOprHosLastMonthNum,
            COUNT(CASE WHEN a.${queryParam.busKeyField} BETWEEN #{queryParam.lastYear_cy_start_date} and CONCAT(#{queryParam.lastYear_cy_end_date},' 23:59:59')
                AND b.oprn_oprt_lv = '3'
                THEN 1 ELSE NULL END) AS threeLvlOprHosLastYearNum,
            COUNT(CASE WHEN  a.${queryParam.busKeyField} BETWEEN #{queryParam.cy_start_date} and CONCAT(#{queryParam.cy_end_date},' 23:59:59')
                AND c.oprn_lv = '3'
                THEN 1 ELSE NULL END) AS threeLvlOprStandradNum,
            COUNT(CASE WHEN a.${queryParam.busKeyField} BETWEEN #{queryParam.cy_start_date} and CONCAT(#{queryParam.cy_end_date},' 23:59:59')
                AND b.oprn_oprt_lv = '4'
                THEN 1 ELSE NULL END) AS fourLvlOprHosIssueNum,
            COUNT(CASE WHEN  a.${queryParam.busKeyField} BETWEEN #{queryParam.lastMonth_cy_start_date} and CONCAT(#{queryParam.lastMonth_cy_end_date},' 23:59:59')
                AND b.oprn_oprt_lv = '4'
                THEN 1 ELSE NULL END) AS fourLvlOprHosLastMonthNum,
            COUNT(CASE WHEN  a.${queryParam.busKeyField} BETWEEN #{queryParam.lastYear_cy_start_date} and CONCAT(#{queryParam.lastYear_cy_end_date},' 23:59:59')
                AND b.oprn_oprt_lv = '4'
                THEN 1 ELSE NULL END) AS fourLvlOprHosLastYearNum,
            COUNT(CASE WHEN a.${queryParam.busKeyField} BETWEEN #{queryParam.cy_start_date} and CONCAT(#{queryParam.cy_end_date},' 23:59:59')
                AND c.oprn_lv = '4'
                THEN 1 ELSE NULL END) AS fourLvlOprStandradNum,
            COUNT(CASE WHEN  a.${queryParam.busKeyField} BETWEEN #{queryParam.cy_start_date} and CONCAT(#{queryParam.cy_end_date},' 23:59:59')
                AND (b.oprn_oprt_lv not in ('1' ,'2' , '3' , '4') or b.oprn_oprt_lv is null)
                THEN 1 ELSE NULL END) AS otherLvlOprHosIssueNum,
            COUNT(CASE WHEN a.${queryParam.busKeyField} BETWEEN #{queryParam.lastMonth_cy_start_date} and CONCAT(#{queryParam.lastMonth_cy_end_date},' 23:59:59')
                AND (b.oprn_oprt_lv not in ('1' ,'2' , '3' , '4') or b.oprn_oprt_lv is null)
                THEN 1 ELSE NULL END) AS otherLvlOprHosLastMonthNum,
            COUNT(CASE WHEN a.${queryParam.busKeyField} BETWEEN #{queryParam.lastYear_cy_start_date} and CONCAT(#{queryParam.lastYear_cy_end_date},' 23:59:59')
                AND (b.oprn_oprt_lv not in ('1' ,'2' , '3' , '4') or b.oprn_oprt_lv is null)
                THEN 1 ELSE NULL END) AS otherLvlOprHosLastYearNum,
            COUNT(CASE WHEN  a.${queryParam.busKeyField} BETWEEN #{queryParam.cy_start_date} and CONCAT(#{queryParam.cy_end_date},' 23:59:59')
                AND (c.oprn_lv not in ('1' ,'2' , '3' , '4') or c.oprn_lv is null)
                THEN 1 ELSE NULL END) AS otherLvlOprStandradNum

        from som_drg_grp_info a
        <if test='queryParam.enableSeAns != null and queryParam.enableSeAns != "" and queryParam.enableSeAns == "1"'>
            INNER JOIN som_hi_invy_bas_info a1 on a1.id=a.SETTLE_LIST_ID
            INNER JOIN som_setl_cas_crsp p on a1.k00=p.k00
        </if>
        inner join som_oprn_oprt_info b on a.SETTLE_LIST_ID = b.SETTLE_LIST_ID
        left join som_oprn_lv c on b.c35c = c.oprn_oprt_codg AND c.enab_flag = #{queryParam.enabFlag}
            AND c.ACTIVE_FLAG = #{queryParam.activeFlag}
        where b.C35C is not null AND b.C35C!='-' and  b.C35C!='--'
        <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
            AND  a.HOSPITAL_ID = #{queryParam.hospitalId}
        </if>
        <if test="queryParam.b16c!=null and queryParam.b16c!=''">
            AND a.dscg_caty_codg_inhosp = #{queryParam.b16c}
        </if>
        <if test="queryParam.queryDrg!=null and queryParam.queryDrg!=''">
            AND a.drg_codg = #{queryParam.queryDrg}
        </if>
        <if test="queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and
        queryParam.cy_end_date!=null and queryParam.cy_end_date!=''">
            AND (
                (a.${queryParam.busKeyField} BETWEEN #{queryParam.cy_start_date} and CONCAT(#{queryParam.cy_end_date},' 23:59:59'))
                OR
                (a.${queryParam.busKeyField} BETWEEN #{queryParam.lastMonth_cy_start_date} and CONCAT(#{queryParam.lastMonth_cy_end_date},' 23:59:59'))
                OR
                (a.${queryParam.busKeyField} BETWEEN #{queryParam.lastYear_cy_start_date} and CONCAT(#{queryParam.lastYear_cy_end_date},' 23:59:59'))
            )
        </if>
        <if test="queryParam.doctorIdList!=null">
            AND (
            a.deptdrt_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.chfdr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.atddr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.ipdr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.resp_nurs_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.train_dr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.intn_dr in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.codr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.qltctrl_dr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.qltctrl_nurs_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            )
        </if>
    </select>
</mapper>
