<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.som.dao.hospitalAnalysis.DrgsAnalysisDao">
    <select id="list" resultType="com.my.som.vo.hospitalAnalysis.DrgsAnalysisInfo">
        SELECT a.drg_codg AS drgsCode,
               a.DRG_NAME AS drgsName,
                <if test="queryParam.inStartTime != null and queryParam.inStartTime != '' and
                      queryParam.inEndTime != null and queryParam.inEndTime != ''">
                    SUBSTR(a.adm_time,1,4) AS standardYear,<!--标杆年份-->
                </if>
                <if test="queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and
                    queryParam.cy_end_date!=null and queryParam.cy_end_date!=''">
                    SUBSTR(a.dscg_time,1,4) AS standardYear,<!--标杆年份-->
                </if>
                <if test="queryParam.seStartTime!=null and queryParam.seStartTime!='' and queryParam.seEndTime!=null and queryParam.seEndTime!=''">
                    SUBSTR(a.setl_end_time,1,4) AS standardYear,<!--标杆年份-->
                </if>
               COUNT(1) AS medcasVal, <!--入组病案数-->
               COUNT(DISTINCT(a.dscg_caty_codg_inhosp)) AS deptNum,<!--科室数-->
               ROUND(IFNULL(a.drg_wt,0), 2) AS drgWt,  <!--权重-->
               ROUND( IFNULL( AVG( a.ipt_sumfee ), 0 ), 2 ) AS byAvgCost,<!--平均住院费用（本院）-->
               ROUND( IFNULL( AVG( a.act_ipt ), 0 ), 2 ) AS byAvgDays,<!--平均住院天数（本院）-->
               ROUND(IFNULL(SUM(a.drg_wt),0),2) AS totalDrgWeight, <!--总权重-->
        <choose>
            <when test="queryParam.feeMulAdjmConf != null and queryParam.feeMulAdjmConf != '' and queryParam.feeMulAdjmConf == 'true' ">
                case when
                b.a54 in ('1','01','310') then '职工'
                else '居民' end as categories,
                case when
                LTRIM(RTRIM(b.insuplc_admdvs)) = #{queryParam.insuplcAdmdvs}   THEN '市医保'
                else '省本级'
                END as standType,
                case when
                b.a54 in ('1','01','310') then ROUND( MAX( IFNULL( AES_DECRYPT( UNHEX( c.standard_avg_fee ), ${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY} ), 0 ))* c.adjm_cof, 2 )
                else ROUND( MAX( IFNULL( AES_DECRYPT( UNHEX( c.standard_avg_fee ), ${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY} ), 0 ))* c.adjm_resid_cof, 2 )
                end as standardCost,
                CASE

                WHEN b.a54 IN ( '1', '01', '310' ) THEN
                ROUND( IFNULL( sum( IFNULL( a.ipt_sumfee, 0 )/ IFNULL( AES_DECRYPT( UNHEX( c.standard_avg_fee ), ${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY} )* c.adjm_cof, 0 )) / COUNT( 1 ), 0 ), 2 )
                ELSE ROUND(  IFNULL( sum( IFNULL( a.ipt_sumfee, 0 )/ IFNULL( AES_DECRYPT( UNHEX( c.standard_avg_fee ), ${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY} )* c.adjm_resid_cof, 0 )) / COUNT( 1 ), 0  ),  2 )
                end as costIndex,
            </when>
            <otherwise>
                ROUND(MAX(IFNULL(AES_DECRYPT(UNHEX(c.standard_avg_fee),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}),0)),2) AS standardCost,
                ROUND(IFNULL(sum(IFNULL(a.ipt_sumfee,0)/NULLIF(a.standard_avg_fee, 0))/COUNT(1),0),2) as costIndex,  <!--  费用消耗指数   -->
            </otherwise>
        </choose>
               ROUND(IFNULL(MAX(a.standard_avg_fee),0), 2) AS avgCost, <!--平均住院费用-->
               ROUND(IFNULL(MAX(a.standard_ave_hosp_day),0), 2) AS avgDays, <!--平均住院日-->
               ROUND(IFNULL(sum(IFNULL(a.act_ipt,0)/NULLIF(a.standard_ave_hosp_day, 0))/COUNT(1),0),2) as timeIndex <!--  时间消耗指数   -->
         FROM som_drg_grp_info a
        left join som_hi_invy_bas_info b  on a.settle_list_id = b.id
        left join som_drg_standard c on c.hospital_id =a.hospital_id
        and b.insuplc_admdvs =c.insuplc_admdvs and left(ifnull(b.d37,b.b15),4)= c.standard_year
        and a.drg_codg = c.drg_codg
        <if test='queryParam.enableSeAns != null and queryParam.enableSeAns != "" and queryParam.enableSeAns == "1"'>
            LEFT JOIN som_hi_invy_bas_info q
            ON q.ID = a.SETTLE_LIST_ID
            INNER JOIN som_setl_cas_crsp p
            ON q.k00 = p.k00
        </if>
        WHERE a.grp_stas = '1'
        <if test="queryParam.deptCode != null and queryParam.deptCode != ''">
            and a.dscg_caty_codg_inhosp = #{queryParam.deptCode}
        </if>
        <if test="queryParam.drCodg!=null and queryParam.drCodg!=''">
            AND a.ipdr_code = #{queryParam.drCodg}
        </if>
        <if test="queryParam.b16c!=null and queryParam.b16c!=''">
            AND a.dscg_caty_codg_inhosp = #{queryParam.b16c}
        </if>
        <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
            AND  a.HOSPITAL_ID = #{queryParam.hospitalId}
        </if>
        <if test="queryParam.queryDrg!=null and queryParam.queryDrg!=''">
            AND a.`drg_codg` = #{queryParam.queryDrg}
        </if>
        <if test="queryParam.inStartTime != null and queryParam.inStartTime != '' and
                          queryParam.inEndTime != null and queryParam.inEndTime != ''">
            AND a.adm_time BETWEEN #{queryParam.inStartTime} AND CONCAT(#{queryParam.inEndTime},' 23:59:59')
        </if>
        <if test="queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and
                        queryParam.cy_end_date!=null and queryParam.cy_end_date!=''">
            AND a.dscg_time BETWEEN #{queryParam.cy_start_date} and CONCAT(#{queryParam.cy_end_date},' 23:59:59')
        </if>
        <if test="queryParam.seStartTime!=null and queryParam.seStartTime!='' and queryParam.seEndTime!=null and queryParam.seEndTime!=''">
            AND a.setl_end_time BETWEEN #{queryParam.seStartTime,jdbcType=VARCHAR} and CONCAT(#{queryParam.seEndTime,jdbcType=VARCHAR},' 23:59:59')
        </if>
        GROUP BY a.drg_codg,
        a.DRG_NAME,
        <if test="queryParam.feeMulAdjmConf != null and queryParam.feeMulAdjmConf != '' and queryParam.feeMulAdjmConf == 'true' ">
            b.insuplc_admdvs,b.a54,c.adjm_cof,c.adjm_resid_cof,
        </if>
        <if test="queryParam.inStartTime != null and queryParam.inStartTime != '' and
                      queryParam.inEndTime != null and queryParam.inEndTime != ''">
            SUBSTR(a.adm_time,1,4),
        </if>
        <if test="queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and
                    queryParam.cy_end_date!=null and queryParam.cy_end_date!=''">
            SUBSTR(a.dscg_time,1,4),
        </if>
        <if test="queryParam.seStartTime!=null and queryParam.seStartTime!='' and queryParam.seEndTime!=null and queryParam.seEndTime!=''">
            SUBSTR(a.setl_end_time,1,4),
        </if>
                 ROUND(IFNULL(a.drg_wt,0), 2)
        ORDER BY totalDrgWeight desc
    </select>

    <select id="getCountInfo" resultType="java.util.Map">
        select
            COUNT(CASE WHEN right(drg_codg,1) = '1' THEN 1 ELSE NULL END) AS seriousComplicationMedicalNum,<!--伴严重合并症及伴随病的DRGs组病案数-->
            IFNULL(COUNT(DISTINCT(CASE WHEN right(drg_codg,1) = '1' AND grp_stas = '1' THEN drg_codg ELSE NULL END)),0) AS seriousComplicationDrgsNum,<!--伴严重合并症及伴随病的DRGs组DRGs组数-->
            IFNULL(CONCAT(ROUND((IFNULL(COUNT(DISTINCT(CASE WHEN right(drg_codg,1) = '1' THEN drg_codg ELSE NULL END)),0)/NULLIF(COUNT(DISTINCT(drg_codg)),0))*100,2),'%'),0)
                AS seriousComplicationDrgsRate,<!--伴严重合并症及伴随病的DRGs组DRGs组数占比-->
            IFNULL(ROUND(SUM(CASE WHEN right(drg_codg,1) = '1' THEN drg_wt ELSE 0 END),2),0) AS seriousComplicationTotalDrgWeight, <!--伴严重合并症及伴随病的DRGs组总权重-->
            IFNULL(ROUND(SUM(CASE WHEN right(drg_codg,1) = '1' THEN drg_wt ELSE 0 END) /
                NULLIF(COUNT(CASE WHEN right(drg_codg,1) = '1' THEN 1 ELSE NULL END),0), 2),0) AS seriousComplicationCmi,  <!--伴严重合并症及伴随病的DRGs组CMI指数-->

            COUNT(CASE WHEN right(drg_codg,1) = '2' THEN 1 ELSE NULL END) AS comorbiditiesComplicationMedicalNum,<!-- 伴合并症或并发症的DRGs组病案数 -->
            IFNULL(COUNT(DISTINCT(CASE WHEN right(drg_codg,1) = '2' AND grp_stas = '1' THEN drg_codg ELSE NULL END)),0) AS comorbiditiesComplicationDrgsNum,<!-- 伴合并症或并发症的DRGs组DRGs组数 -->
            IFNULL(CONCAT(ROUND((IFNULL(COUNT(DISTINCT(CASE WHEN right(drg_codg,1) = '2' THEN drg_codg ELSE NULL END)),0)/NULLIF(COUNT(DISTINCT(drg_codg)),0))*100,2),'%'),0)
                AS comorbiditiesComplicationDrgsRate,<!-- 伴合并症或并发症的DRGs组DRGs组数占比 -->
            IFNULL(ROUND(SUM(CASE WHEN right(drg_codg,1) = '2' THEN drg_wt ELSE 0 END),2),0) AS comorbiditiesComplicationTotalDrgWeight,<!-- 伴合并症或并发症的DRGs组总权重 -->
            IFNULL(ROUND(SUM(CASE WHEN right(drg_codg,1) = '2' THEN drg_wt ELSE 0 END) /
                NULLIF(COUNT(CASE WHEN right(drg_codg,1) = '2' THEN 1 ELSE NULL END),0), 2),0) AS comorbiditiesComplicationCmi,<!-- 伴合并症或并发症的DRGs组CMI指数 -->

            COUNT(CASE WHEN right(drg_codg,1) = '3' THEN 1 ELSE NULL END) AS normalComplicationMedicalNum,<!--伴一般合并症及伴随病的DRGs组病案数-->
            IFNULL(COUNT(DISTINCT(CASE WHEN right(drg_codg,1) = '3' AND grp_stas = '1' THEN drg_codg ELSE NULL END)),0) AS normalComplicationDrgsNum,<!--伴一般合并症及伴随病的DRGs组DRGs组数-->
            IFNULL(CONCAT(ROUND((IFNULL(COUNT(DISTINCT(CASE WHEN right(drg_codg,1) = '3' THEN drg_codg ELSE NULL END)),0)/NULLIF(COUNT(DISTINCT(drg_codg)),0))*100,2),'%'),0)
                AS normalComplicationDrgsRate,<!--伴一般合并症及伴随病的DRGs组DRGs组数占比-->
            IFNULL(ROUND(SUM(CASE WHEN right(drg_codg,1) = '3' THEN drg_wt ELSE 0 END),2),0) AS normalComplicationTotalDrgWeight, <!--伴一般合并症及伴随病的DRGs组总权重-->
            IFNULL(ROUND(SUM(CASE WHEN right(drg_codg,1) = '3' THEN drg_wt ELSE 0 END) /
                NULLIF(COUNT(CASE WHEN right(drg_codg,1) = '3' THEN 1 ELSE NULL END),0), 2),0) AS normalComplicationCmi,  <!--伴一般合并症及伴随病的DRGs组CMI指数-->

            COUNT(CASE WHEN right(drg_codg,1) = '4' THEN 1 ELSE NULL END) AS noSeriousComorbiditiesComplicationMedicalNum, <!--不伴严重合并症或并发症的DRGs组病案数-->
            IFNULL(COUNT(DISTINCT(CASE WHEN right(drg_codg,1) = '4' AND grp_stas = '1' THEN drg_codg ELSE NULL END)),0) AS noSeriousComorbiditiesComplicationDrgsNum,<!--不伴严重合并症或并发症的DRGs组DRGs组数-->
            IFNULL(CONCAT(ROUND((IFNULL(COUNT(DISTINCT(CASE WHEN right(drg_codg,1) = '4' THEN drg_codg ELSE NULL END)),0)/NULLIF(COUNT(DISTINCT(drg_codg)),0))*100,2),'%'),0)
                AS noSeriousComorbiditiesComplicationDrgsRate,<!--不伴严重合并症或并发症的DRGs组DRGs组数占比-->
            IFNULL(ROUND(SUM(CASE WHEN right(drg_codg,1) = '4' THEN drg_wt ELSE 0 END),2),0) AS noSeriousComorbiditiesComplicationTotalDrgWeight,<!--不伴严重合并症或并发症的DRGs组DRGs组总权重-->
            IFNULL(ROUND(SUM(CASE WHEN right(drg_codg,1) = '4' THEN drg_wt ELSE 0 END) /
                NULLIF(COUNT(CASE WHEN right(drg_codg,1) = '4' THEN 1 ELSE NULL END),0), 2),0) AS noSeriousComorbiditiesComplicationCmi, <!--不伴严重合并症或并发症的DRGs组DRGs组CMI指数-->

            COUNT(CASE WHEN right(drg_codg,1) = '5' THEN 1 ELSE NULL END) AS noComplicationMedicalNum,<!--不伴合并症及伴随病的DRGs组病案数-->
            IFNULL(COUNT(DISTINCT(CASE WHEN right(drg_codg,1) = '5' AND grp_stas = '1' THEN drg_codg ELSE NULL END)),0) AS noComplicationDrgsNum,<!--不伴合并症及伴随病的DRGs组DRGs组数-->
            IFNULL(CONCAT(ROUND((IFNULL(COUNT(DISTINCT(CASE WHEN right(drg_codg,1) = '5' THEN drg_codg ELSE NULL END)),0)/NULLIF(COUNT(DISTINCT(drg_codg)),0))*100,2),'%'),0)
                AS noComplicationDrgsRate,<!--不伴合并症及伴随病的DRGs组DRGs组数占比-->
            IFNULL(ROUND(SUM(CASE WHEN right(drg_codg,1) = '5' THEN drg_wt ELSE 0 END),2),0) AS noComplicationTotalDrgWeight, <!--不伴合并症及伴随病的DRGs组总权重-->
            IFNULL(ROUND(SUM(CASE WHEN right(drg_codg,1) = '5' THEN drg_wt ELSE 0 END) /
                NULLIF(COUNT(CASE WHEN right(drg_codg,1) = '5' THEN 1 ELSE NULL END),0), 2),0) AS noComplicationCmi,  <!--不伴合并症及伴随病的DRGs组CMI指数-->

            COUNT(CASE WHEN right(drg_codg,1) = '9' THEN 1 ELSE NULL END) AS notDifferentiatedMedicalNum, <!--未作区分DRGs组病案数-->
            IFNULL(COUNT(DISTINCT(CASE WHEN right(drg_codg,1) = '9' AND grp_stas = '1' THEN drg_codg ELSE NULL END)),0) AS notDifferentiatedDrgsNum, <!--未作区分DRGs组DRGs组数-->
            IFNULL(CONCAT(ROUND((IFNULL(COUNT(DISTINCT(CASE WHEN right(drg_codg,1) = '9' THEN drg_codg ELSE NULL END)),0)/NULLIF(COUNT(DISTINCT(drg_codg)),0))*100,2),'%'),0)
                AS notDifferentiatedDrgsRate,<!--未作区分DRGs组DRGs组数占比-->
            IFNULL(ROUND(SUM(CASE WHEN right(drg_codg,1) = '9' THEN drg_wt ELSE 0 END),2),0) AS notDifferentiatedTotalDrgWeight, <!--未作区分DRGs组总权重-->
            IFNULL(ROUND(SUM(CASE WHEN right(drg_codg,1) = '9' THEN drg_wt ELSE 0 END) /
                NULLIF(COUNT(CASE WHEN right(drg_codg,1) = '9' THEN 1 ELSE NULL END),0), 2),0) AS notDifferentiatedCmi,  <!--未作区分DRGs组CMI指数-->

            COUNT(CASE WHEN SUBSTR(drg_codg,2,1) in ('R','S','T','U','V','W','X','Y','Z')  THEN 1 ELSE NULL END) AS medicalDeptMedicalNum,<!--内科组的DRGs病案数-->
            IFNULL(COUNT(DISTINCT(CASE WHEN SUBSTR(drg_codg,2,1) in ('R','S','T','U','V','W','X','Y','Z') AND grp_stas = '1' THEN drg_codg ELSE NULL END)),0) AS medicalDeptDrgsNum,<!--内科组的DRGs组DRGs组数-->
            IFNULL(CONCAT(ROUND((IFNULL(COUNT(DISTINCT(CASE WHEN SUBSTR(drg_codg,2,1) in ('R','S','T','U','V','W','X','Y','Z') THEN drg_codg ELSE NULL END)),0)/NULLIF(COUNT(DISTINCT(drg_codg)),0))*100,2),'%'),0)
                AS medicalDeptDrgsRate,<!--内科组DRGs组数占比-->
            IFNULL(ROUND(SUM(CASE WHEN SUBSTR(drg_codg,2,1) in ('R','S','T','U','V','W','X','Y','Z') THEN drg_wt ELSE 0 END),2),0) AS medicalDeptTotalDrgWeight, <!--内科组的DRGs组总权重-->
            IFNULL(ROUND(SUM(CASE WHEN SUBSTR(drg_codg,2,1) in ('R','S','T','U','V','W','X','Y','Z') THEN drg_wt ELSE 0 END) /
                NULLIF(COUNT(CASE WHEN SUBSTR(drg_codg,2,1) in ('R','S','T','U','V','W','X','Y','Z') THEN 1 ELSE NULL END),0), 2),0) AS medicalDeptCmi,  <!--内科组的DRGs组CMI指数-->

            COUNT(CASE WHEN SUBSTR(drg_codg,2,1) in ('K','L','M','N','P','Q')  THEN 1 ELSE NULL END) AS notOperationMedicalNum,<!--非手术室操作组的DRGs病案数-->
            IFNULL(COUNT(DISTINCT(CASE WHEN SUBSTR(drg_codg,2,1) in ('K','L','M','N','P','Q') AND grp_stas = '1' THEN drg_codg ELSE NULL END)),0) AS notOperationDrgsNum,<!--非手术室操作组的DRGs组DRGs组数-->
            IFNULL(CONCAT(ROUND((IFNULL(COUNT(DISTINCT(CASE WHEN SUBSTR(drg_codg,2,1) in ('K','L','M','N','P','Q') THEN drg_codg ELSE NULL END)),0)/NULLIF(COUNT(DISTINCT(drg_codg)),0))*100,2),'%'),0)
                AS notOperationDrgsRate,<!--非手术室操作组DRGs组数占比-->
            IFNULL(ROUND(SUM(CASE WHEN SUBSTR(drg_codg,2,1) in ('K','L','M','N','P','Q') THEN drg_wt ELSE 0 END),2),0) AS notOperationTotalDrgWeight, <!--非手术室操作组的DRGs组总权重-->
            IFNULL(ROUND(SUM(CASE WHEN SUBSTR(drg_codg,2,1) in ('K','L','M','N','P','Q') THEN drg_wt ELSE 0 END) /
                NULLIF(COUNT(CASE WHEN SUBSTR(drg_codg,2,1) in ('K','L','M','N','P','Q') THEN 1 ELSE NULL END),0), 2),0) AS notOperationCmi,  <!--非手术室操作组的DRGs组CMI指数-->

            COUNT(CASE WHEN SUBSTR(drg_codg,2,1) in ('A','B','C','D','E','F','G','H','J')  THEN 1 ELSE NULL END) AS surgeryDeptMedicalNum,<!--外科组的DRGs病案数-->
            IFNULL(COUNT(DISTINCT(CASE WHEN SUBSTR(drg_codg,2,1) in ('A','B','C','D','E','F','G','H','J') AND grp_stas = '1' THEN drg_codg ELSE NULL END)),0) AS surgeryDeptDrgsNum,<!--外科组的DRGs组DRGs组数-->
            IFNULL(CONCAT(ROUND((IFNULL(COUNT(DISTINCT(CASE WHEN SUBSTR(drg_codg,2,1) in ('A','B','C','D','E','F','G','H','J') THEN drg_codg ELSE NULL END)),0)/NULLIF(COUNT(DISTINCT(drg_codg)),0))*100,2),'%'),0)
                AS surgeryDeptDrgsRate,<!--外科组DRGs组数占比-->
            IFNULL(ROUND(SUM(CASE WHEN SUBSTR(drg_codg,2,1) in ('A','B','C','D','E','F','G','H','J') THEN drg_wt ELSE 0 END),2),0) AS surgeryDeptTotalDrgWeight, <!--外科组的DRGs组总权重-->
            IFNULL(ROUND(SUM(CASE WHEN SUBSTR(drg_codg,2,1) in ('A','B','C','D','E','F','G','H','J') THEN drg_wt ELSE 0 END) /
                NULLIF(COUNT(CASE WHEN SUBSTR(drg_codg,2,1) in ('A','B','C','D','E','F','G','H','J') THEN 1 ELSE NULL END),0), 2),0) AS surgeryDeptCmi  <!--外科组的DRGs组CMI指数-->
        from som_drg_grp_info a
        <if test='queryParam.enableSeAns != null and queryParam.enableSeAns != "" and queryParam.enableSeAns == "1"'>
            LEFT JOIN som_hi_invy_bas_info q
            ON q.ID = a.SETTLE_LIST_ID
            INNER JOIN som_setl_cas_crsp p
            ON q.k00 = p.k00
        </if>
        where 1 = 1
        <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
            AND  a.HOSPITAL_ID = #{queryParam.hospitalId}
        </if>
        <if test="queryParam.b16c!=null and queryParam.b16c!=''">
            AND a.dscg_caty_codg_inhosp = #{queryParam.b16c}
        </if>
        <if test="queryParam.drCodg!=null and queryParam.drCodg!=''">
            AND a.ipdr_code = #{queryParam.drCodg}
        </if>
        <if test="queryParam.queryDrg!=null and queryParam.queryDrg!=''">
            AND a.`drg_codg` = #{queryParam.queryDrg}
        </if>
        <if test="queryParam.inStartTime!=null and queryParam.inStartTime!='' and
        queryParam.inEndTime!=null and queryParam.inEndTime!=''">
            AND a.adm_time BETWEEN #{queryParam.inStartTime} and CONCAT(#{queryParam.inEndTime},' 23:59:59')
        </if>
        <if test="queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and
        queryParam.cy_end_date!=null and queryParam.cy_end_date!=''">
            AND a.dscg_time BETWEEN #{queryParam.cy_start_date} and CONCAT(#{queryParam.cy_end_date},' 23:59:59')
        </if>
        <if test="queryParam.seStartTime!=null and queryParam.seStartTime!='' and queryParam.seEndTime!=null and queryParam.seEndTime!=''">
            AND a.setl_end_time BETWEEN #{queryParam.seStartTime,jdbcType=VARCHAR} and CONCAT(#{queryParam.seEndTime,jdbcType=VARCHAR},' 23:59:59')
        </if>
        <if test="queryParam.doctorIdList!=null">
            AND (
            a.deptdrt_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.chfdr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.atddr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.ipdr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.resp_nurs_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.train_dr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.intn_dr in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.codr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.qltctrl_dr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.qltctrl_nurs_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            )
        </if>
    </select>
</mapper>
