<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.som.dao.hospitalAnalysis.CostAnalysisDao">
    <select id="list" resultType="java.util.HashMap">
    SELECT a.*
           <if test="queryParam.queryType==1">
                <trim prefix=",">
                    IFNULL(b.name,'未填写科室') AS priOutHosDeptName
                </trim>
            </if>
    FROM (
        select
            <if test="queryParam.queryType==1">
                a.dscg_caty_codg_inhosp AS priOutHosDeptCode,
                a.HOSPITAL_ID,
            </if>
            <if test="queryParam.queryType==2">
                a.drg_codg AS drgCodg,
                CONCAT(a.drg_codg,a.DRG_NAME) AS drgName,
            </if>
            <if test="queryParam.dataType==1">
                IFNULL(ROUND(SUM(a.psn_selfpay),2),0) AS psnSelfpay,
                IFNULL(ROUND(SUM(a.psn_ownpay),2),0) AS psnOwnpay,
                IFNULL(ROUND(SUM(a.acct_pay),2),0) AS acctPay,
                IFNULL(ROUND(SUM(a.psn_cashpay),2),0) AS psnCashpay,
                IFNULL(ROUND(SUM(a.ipt_sumfee),2),0) AS iptSumfee,
                IFNULL(ROUND(SUM(p.med_ins_fund),2),0) AS medInsFund
            </if>
            <if test="queryParam.dataType==2">
                IFNULL(ROUND(SUM(a.ipt_sumfee),2),0) AS iptSumfee,
                IFNULL(ROUND(SUM(a.ipt_sumfee_in_selfpay_amt),2),0) AS iptSumfeeInSelfpayAmt,
                IFNULL(CONCAT(ROUND(SUM(a.ipt_sumfee_in_selfpay_amt)/NULLIF(SUM(a.ipt_sumfee),0)*100,2),'%'),0) AS inhosSelfPayCostRate,
                IFNULL(ROUND(SUM(a.com_med_servfee),2),0) AS comMedServfee,
                IFNULL(CONCAT(ROUND(SUM(a.com_med_servfee)/NULLIF(SUM(a.ipt_sumfee),0)*100,2),'%'),0) AS serviceCostRate,
                IFNULL(ROUND(SUM(a.rhab_fee),2),0) AS rhabFee,
                IFNULL(CONCAT(ROUND(SUM(a.rhab_fee)/NULLIF(SUM(a.ipt_sumfee),0)*100,2),'%'),0) AS recoverCostRate,
                IFNULL(ROUND(SUM(a.diag_fee),2),0) AS diagFee,
                IFNULL(CONCAT(ROUND(SUM(a.diag_fee)/NULLIF(SUM(a.ipt_sumfee),0)*100,2),'%'),0) AS diagnoseCostRate,
                IFNULL(ROUND(SUM(a.treat_fee),2),0) AS treatFee,
                IFNULL(CONCAT(ROUND(SUM(a.treat_fee)/NULLIF(SUM(a.ipt_sumfee),0)*100,2),'%'),0) AS treatmentCostRate,
                IFNULL(ROUND(SUM(a.drugfee),2),0) AS drugfee,
                IFNULL(CONCAT(ROUND(SUM(a.drugfee)/NULLIF(SUM(a.ipt_sumfee),0)*100,2),'%'),0) AS medicalCostRate,
                IFNULL(ROUND(SUM(a.blood_blo_pro),2),0) AS bloodBloPro,
                IFNULL(CONCAT(ROUND(SUM(a.blood_blo_pro)/NULLIF(SUM(a.ipt_sumfee),0)*100,2),'%'),0) AS bloodCostRate,
                IFNULL(ROUND(SUM(a.mcs_fee),2),0) AS mcsFee,
                IFNULL(CONCAT(ROUND(SUM(a.mcs_fee)/NULLIF(SUM(a.ipt_sumfee),0)*100,2),'%'),0) AS materialCostRate,
                IFNULL(ROUND(SUM(a.tcm_oth),2),0) AS tcmOth,
                IFNULL(CONCAT(ROUND(SUM(a.tcm_oth)/NULLIF(SUM(a.ipt_sumfee),0)*100,2),'%'),0) AS chineseOtherRate,
                IFNULL(ROUND(SUM(a.oth_fee),2),0) AS othFee,
                IFNULL(CONCAT(ROUND(SUM(a.oth_fee)/NULLIF(SUM(a.ipt_sumfee),0)*100,2),'%'),0) AS otherCostRate,
                IFNULL(ROUND(SUM(a.abt_fee),2),0) AS abtFee,
                IFNULL(CONCAT(ROUND(SUM(a.abt_fee)/NULLIF(SUM(a.ipt_sumfee),0)*100,2),'%'),0) AS antibioticCostRate,
                IFNULL(ROUND(SUM(a.inspect_fee),2),0) AS inspectFee,
                IFNULL(CONCAT(ROUND(SUM(a.inspect_fee)/NULLIF(SUM(a.ipt_sumfee),0)*100,2),'%'),0) AS inspectionCostRate
            </if>
        from som_drg_grp_info a
        INNER JOIN som_hi_invy_bas_info a1 on a1.id=a.SETTLE_LIST_ID
        LEFT JOIN som_setl_info p on a1.k00=p.unique_id
<!--        <if test='queryParam.enableSeAns != null and queryParam.enableSeAns != "" and queryParam.enableSeAns == "1"'>-->
<!--            INNER JOIN som_hi_invy_bas_info a1 on a1.id=som_drg_grp_info.SETTLE_LIST_ID-->
<!--            INNER JOIN som_setl_cas_crsp p on a1.k00=p.k00-->
<!--        </if>-->
        where 1 = 1 AND grp_stas = 1
        <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
            AND  a.HOSPITAL_ID = #{queryParam.hospitalId}
        </if>
        <if test="queryParam.b16c!=null and queryParam.b16c!=''">
            AND dscg_caty_codg_inhosp = #{queryParam.b16c}
        </if>
        <if test="queryParam.drCodg!=null and queryParam.drCodg!=''">
            AND ipdr_code = #{queryParam.drCodg}
        </if>
        <if test="queryParam.queryDrg!=null and queryParam.queryDrg!=''">
            AND `drg_codg` = #{queryParam.queryDrg}
        </if>
        <if test="queryParam.inStartTime != null and queryParam.inStartTime != '' and
                  queryParam.inEndTime != null and queryParam.inEndTime != ''">
            AND adm_time BETWEEN #{queryParam.inStartTime} AND CONCAT(#{queryParam.inEndTime},' 23:59:59')
        </if>
        <if test="queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and
        queryParam.cy_end_date!=null and queryParam.cy_end_date!=''">
            AND dscg_time BETWEEN #{queryParam.cy_start_date} and CONCAT(#{queryParam.cy_end_date},' 23:59:59')
        </if>
        <if test="queryParam.seStartTime!=null and queryParam.seStartTime!='' and
                queryParam.seEndTime!=null and queryParam.seEndTime!=''">
            AND setl_end_time BETWEEN #{queryParam.seStartTime} and CONCAT(#{queryParam.seEndTime},' 23:59:59')
        </if>
        <if test="queryParam.doctorIdList!=null">
            AND (
            deptdrt_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            chfdr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            atddr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            ipdr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            resp_nurs_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            train_dr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            intn_dr in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            codr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            qltctrl_dr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            qltctrl_nurs_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            )
        </if>
        <if test="queryParam.queryType==1">
            GROUP BY dscg_caty_codg_inhosp,dscg_caty_name_inhosp, a.HOSPITAL_ID
        </if>
        <if test="queryParam.queryType==2">
            GROUP BY drg_codg,DRG_NAME
        </if>

    ) a
    <if test="queryParam.queryType==1">
        left join som_dept b on a.priOutHosDeptCode = b.code
        AND a.HOSPITAL_ID = b.HOSPITAL_ID
    </if>
    </select>


<!--    <sql id="settleListIdSubquery">-->
<!--        SELECT SETTLE_LIST_ID-->
<!--        FROM som_drg_grp_info-->
<!--        <if test='queryParam.enableSeAns != null and queryParam.enableSeAns != "" and queryParam.enableSeAns == "1"'>-->
<!--            INNER JOIN som_hi_invy_bas_info a1 ON a1.id = som_drg_grp_info.SETTLE_LIST_ID-->
<!--            INNER JOIN som_setl_cas_crsp p ON a1.k00 = p.k00-->
<!--        </if>-->
<!--        WHERE 1 = 1-->
<!--        <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">-->
<!--            AND som_drg_grp_info.HOSPITAL_ID = #{queryParam.hospitalId}-->
<!--        </if>-->
<!--        <if test="queryParam.b16c!=null and queryParam.b16c!=''">-->
<!--            AND dscg_caty_codg_inhosp = #{queryParam.b16c}-->
<!--        </if>-->
<!--        <if test="queryParam.queryDrg!=null and queryParam.queryDrg!=''">-->
<!--            AND `drg_codg` = #{queryParam.queryDrg}-->
<!--        </if>-->
<!--        <if test="queryParam.tb16cList!=null and queryParam.tb16cList.size()>0">-->
<!--            AND dscg_caty_codg_inhosp IN-->
<!--            <foreach collection="queryParam.tb16cList" item="tb16c" open="(" separator="," close=")">-->
<!--                #{tb16c}-->
<!--            </foreach>-->
<!--        </if>-->
<!--        <if test="queryParam.drgCodgList!=null and queryParam.drgCodgList.size()>0">-->
<!--            AND `drg_codg` IN-->
<!--            <foreach collection="queryParam.drgCodgList" item="drgCodg" open="(" separator="," close=")">-->
<!--                #{drgCodg}-->
<!--            </foreach>-->
<!--        </if>-->
<!--        <if test="queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and-->
<!--                queryParam.cy_end_date!=null and queryParam.cy_end_date!=''">-->
<!--            AND dscg_time BETWEEN #{queryParam.cy_start_date} AND CONCAT(#{queryParam.cy_end_date}, ' 23:59:59')-->
<!--        </if>-->
<!--        <if test="queryParam.inStartTime != null and queryParam.inStartTime != '' and-->
<!--                  queryParam.inEndTime != null and queryParam.inEndTime != ''">-->
<!--            AND adm_time BETWEEN #{queryParam.inStartTime} AND CONCAT(#{queryParam.inEndTime}, ' 23:59:59')-->
<!--        </if>-->
<!--        <if test="queryParam.seStartTime!=null and queryParam.seStartTime!='' and-->
<!--                queryParam.seEndTime!=null and queryParam.seEndTime!=''">-->
<!--            AND setl_end_time BETWEEN #{queryParam.seStartTime} AND CONCAT(#{queryParam.seEndTime}, ' 23:59:59')-->
<!--        </if>-->
<!--        <if test="queryParam.doctorIdList!=null">-->
<!--            AND (-->
<!--            deptdrt_code IN-->
<!--            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">-->
<!--                #{item}-->
<!--            </foreach>-->
<!--            OR chfdr_code IN-->
<!--            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">-->
<!--                #{item}-->
<!--            </foreach>-->
<!--            OR atddr_code IN-->
<!--            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">-->
<!--                #{item}-->
<!--            </foreach>-->
<!--            OR ipdr_code IN-->
<!--            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">-->
<!--                #{item}-->
<!--            </foreach>-->
<!--            OR resp_nurs_code IN-->
<!--            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">-->
<!--                #{item}-->
<!--            </foreach>-->
<!--            OR train_dr_code IN-->
<!--            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">-->
<!--                #{item}-->
<!--            </foreach>-->
<!--            OR intn_dr IN-->
<!--            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">-->
<!--                #{item}-->
<!--            </foreach>-->
<!--            OR codr_code IN-->
<!--            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">-->
<!--                #{item}-->
<!--            </foreach>-->
<!--            OR qltctrl_dr_code IN-->
<!--            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">-->
<!--                #{item}-->
<!--            </foreach>-->
<!--            OR qltctrl_nurs_code IN-->
<!--            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">-->
<!--                #{item}-->
<!--            </foreach>-->
<!--            )-->
<!--        </if>-->
<!--    </sql>-->

<!--    <select id="getMedicalCostList" resultType="java.util.HashMap">-->
<!--        SELECT-->
<!--        x.medChrgItemname,-->
<!--        IFNULL(y.cost, 0) AS cost-->
<!--        FROM (-->
<!--        SELECT-->
<!--        med_chrg_itemname AS medChrgItemname-->
<!--        FROM som_hi_setl_invy_med_fee_info-->
<!--        WHERE hi_setl_invy_id IN (-->
<!--        <include refid="settleListIdSubquery"/>-->
<!--        )-->
<!--        AND med_chrg_itemname != ''-->
<!--        GROUP BY med_chrg_itemname-->
<!--        ) x-->
<!--        LEFT JOIN (-->
<!--        SELECT-->
<!--        med_chrg_itemname AS medChrgItemname,-->
<!--        IFNULL(ROUND(SUM(amt), 2), 0) AS cost-->
<!--        FROM som_hi_setl_invy_med_fee_info-->
<!--        WHERE hi_setl_invy_id IN (-->
<!--        <include refid="settleListIdSubquery"/>-->
<!--        )-->
<!--        AND med_chrg_itemname != ''-->
<!--        GROUP BY med_chrg_itemname-->
<!--        ) y ON x.medChrgItemname = y.medChrgItemname-->
<!--        ORDER BY x.medChrgItemname ASC-->
<!--    </select>-->

    <select id="getMedicalCostList" resultType="java.util.HashMap">
        select
        x.medChrgItemname,
        IFNULL(cost,0) as cost
        from (
        select
        med_chrg_itemname as medChrgItemname
        from som_hi_setl_invy_med_fee_info
        where hi_setl_invy_id in (
        select
        SETTLE_LIST_ID
        from som_drg_grp_info
        <if test='queryParam.enableSeAns != null and queryParam.enableSeAns != "" and queryParam.enableSeAns == "1"'>
            INNER JOIN som_hi_invy_bas_info a1 on a1.id=som_drg_grp_info.SETTLE_LIST_ID
            INNER JOIN som_setl_cas_crsp p on a1.k00=p.k00
        </if>
        where 1=1
<!--        <if test="queryParam.tb16cList!=null and queryParam.tb16cList.size()>0">-->
<!--            AND dscg_caty_codg_inhosp IN-->
<!--            <foreach collection="queryParam.tb16cList" item="tb16c" open="(" separator="," close=")">-->
<!--                #{tb16c}-->
<!--            </foreach>-->
<!--        </if>-->
<!--        <if test="queryParam.drgCodgList!=null and queryParam.drgCodgList.size()>0">-->
<!--            AND `drg_codg` IN-->
<!--            <foreach collection="queryParam.drgCodgList" item="drgCodg" open="(" separator="," close=")">-->
<!--                #{drgCodg}-->
<!--            </foreach>-->
<!--        </if>-->
        <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
            AND  som_drg_grp_info.HOSPITAL_ID = #{queryParam.hospitalId}
        </if>
        <if test="queryParam.b16c!=null and queryParam.b16c!=''">
            AND dscg_caty_codg_inhosp = #{queryParam.b16c}
        </if>
        <if test="queryParam.queryDrg!=null and queryParam.queryDrg!=''">
            AND `drg_codg` = #{queryParam.queryDrg}
        </if>
        <if test="queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and
                        queryParam.cy_end_date!=null and queryParam.cy_end_date!=''">
            AND dscg_time BETWEEN #{queryParam.cy_start_date} and CONCAT(#{queryParam.cy_end_date},' 23:59:59')
        </if>
        <if test="queryParam.inStartTime != null and queryParam.inStartTime != '' and
                          queryParam.inEndTime != null and queryParam.inEndTime != ''">
            AND adm_time BETWEEN #{queryParam.inStartTime} AND CONCAT(#{queryParam.inEndTime},' 23:59:59')
        </if>
        <if test="queryParam.seStartTime!=null and queryParam.seStartTime!='' and
                        queryParam.seEndTime!=null and queryParam.seEndTime!=''">
            AND setl_end_time BETWEEN #{queryParam.seStartTime} and CONCAT(#{queryParam.seEndTime},' 23:59:59')
        </if>
        <if test="queryParam.doctorIdList!=null">
            AND (
            deptdrt_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            chfdr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            atddr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            ipdr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            resp_nurs_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            train_dr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            intn_dr in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            codr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            qltctrl_dr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            qltctrl_nurs_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            )
        </if>
        )
        and med_chrg_itemname!=''
        group by med_chrg_itemname
        )x left join(
        select
        med_chrg_itemname as medChrgItemname,
        IFNULL(ROUND(SUM(amt),2),0) as cost
        from som_hi_setl_invy_med_fee_info
        where hi_setl_invy_id in (
        select
        SETTLE_LIST_ID
        from som_drg_grp_info
        <if test='queryParam.enableSeAns != null and queryParam.enableSeAns != "" and queryParam.enableSeAns == "1"'>
            INNER JOIN som_hi_invy_bas_info a1 on a1.id=som_drg_grp_info.SETTLE_LIST_ID
            INNER JOIN som_setl_cas_crsp p on a1.k00=p.k00
        </if>
        where 1=1
        <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
            AND  som_drg_grp_info.HOSPITAL_ID = #{queryParam.hospitalId}
        </if>
        <if test="queryParam.b16c!=null and queryParam.b16c!=''">
            AND dscg_caty_codg_inhosp = #{queryParam.b16c}
        </if>
        <if test="queryParam.queryDrg!=null and queryParam.queryDrg!=''">
            AND `drg_codg` = #{queryParam.queryDrg}
        </if>
        <if test="queryParam.tb16c!=null and queryParam.tb16c!=''">
            AND dscg_caty_codg_inhosp = #{queryParam.b16c}
        </if>
        <if test="queryParam.tQueryDrg!=null and queryParam.tQueryDrg!=''">
            AND `drg_codg` = #{queryParam.tQueryDrg}
        </if>
        <if test="queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and
                    queryParam.cy_end_date!=null and queryParam.cy_end_date!=''">
            AND dscg_time BETWEEN #{queryParam.cy_start_date} and CONCAT(#{queryParam.cy_end_date},' 23:59:59')
        </if>
        <if test="queryParam.inStartTime != null and queryParam.inStartTime != '' and
                                  queryParam.inEndTime != null and queryParam.inEndTime != ''">
            AND adm_time BETWEEN #{queryParam.inStartTime} AND CONCAT(#{queryParam.inEndTime},' 23:59:59')
        </if>
        <if test="queryParam.seStartTime!=null and queryParam.seStartTime!='' and
                                queryParam.seEndTime!=null and queryParam.seEndTime!=''">
            AND setl_end_time BETWEEN #{queryParam.seStartTime} and CONCAT(#{queryParam.seEndTime},' 23:59:59')
        </if>
        <if test="queryParam.doctorIdList!=null">
            AND (
            deptdrt_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            chfdr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            atddr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            ipdr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            resp_nurs_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            train_dr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            intn_dr in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            codr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            qltctrl_dr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            qltctrl_nurs_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            )
        </if>
        )
        and med_chrg_itemname!=''
        group by  med_chrg_itemname
        )y on x.medChrgItemname = y.medChrgItemname
        order by x.medChrgItemname asc
    </select>





    <select id="getFundPayList" resultType="java.util.Map">
        select
          x.fundPayType,
          IFNULL(y.cost,0) as cost
        from (
            select
              FUND_PAY_TYPE as fundPayType
            from som_fund_pay
            where hi_setl_invy_id in (
                select
                  SETTLE_LIST_ID
                from som_drg_grp_info
                <if test='queryParam.enableSeAns != null and queryParam.enableSeAns != "" and queryParam.enableSeAns == "1"'>
                    INNER JOIN som_hi_invy_bas_info a on a.id=som_drg_grp_info.SETTLE_LIST_ID
                    INNER JOIN som_setl_cas_crsp p on a.k00=p.k00
                </if>
                where 1=1
                <if test="queryParam.tb16cList!=null and queryParam.tb16cList.size()>0">
                    AND dscg_caty_codg_inhosp IN
                    <foreach collection="queryParam.tb16cList" item="tb16c" open="(" separator="," close=")">
                        #{tb16c}
                    </foreach>
                </if>
                <if test="queryParam.drgCodgList!=null and queryParam.drgCodgList.size()>0">
                    AND `drg_codg` IN
                    <foreach collection="queryParam.drgCodgList" item="drgCodg" open="(" separator="," close=")">
                        #{drgCodg}
                    </foreach>
                </if>

                <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
                    AND  som_drg_grp_info.HOSPITAL_ID = #{queryParam.hospitalId}
                </if>
                <if test="queryParam.b16c!=null and queryParam.b16c!=''">
                    AND dscg_caty_codg_inhosp = #{queryParam.b16c}
                </if>
                <if test="queryParam.queryDrg!=null and queryParam.queryDrg!=''">
                    AND `drg_codg` = #{queryParam.queryDrg}
                </if>
                <if test="queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and
                        queryParam.cy_end_date!=null and queryParam.cy_end_date!=''">
                    AND dscg_time BETWEEN #{queryParam.cy_start_date} and CONCAT(#{queryParam.cy_end_date},' 23:59:59')
                </if>
                <if test="queryParam.inStartTime != null and queryParam.inStartTime != '' and
                                  queryParam.inEndTime != null and queryParam.inEndTime != ''">
                    AND adm_time BETWEEN #{queryParam.inStartTime} AND CONCAT(#{queryParam.inEndTime},' 23:59:59')
                </if>
                <if test="queryParam.seStartTime!=null and queryParam.seStartTime!='' and
                                queryParam.seEndTime!=null and queryParam.seEndTime!=''">
                    AND setl_end_time BETWEEN #{queryParam.seStartTime} and CONCAT(#{queryParam.seEndTime},' 23:59:59')
                </if>
                <if test="queryParam.doctorIdList!=null">
                    AND (
                    deptdrt_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    chfdr_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    atddr_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    ipdr_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    resp_nurs_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    train_dr_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    intn_dr in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    codr_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    qltctrl_dr_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    qltctrl_nurs_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    )
                </if>
            )
            and FUND_PAY_TYPE!=''
            group by FUND_PAY_TYPE order by FUND_PAY_TYPE asc
        )x left join(
            select
              FUND_PAY_TYPE as fundPayType,
              IFNULL(ROUND(SUM(FUND_PAYAMT),2),0) as cost
            from som_fund_pay
            where hi_setl_invy_id in (
                select
                  SETTLE_LIST_ID
                from som_drg_grp_info
                <if test='queryParam.enableSeAns != null and queryParam.enableSeAns != "" and queryParam.enableSeAns == "1"'>
                    INNER JOIN som_hi_invy_bas_info a on a.id=som_drg_grp_info.SETTLE_LIST_ID
                    INNER JOIN som_setl_cas_crsp p on a.k00=p.k00
                </if>
                where 1=1
                <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
                    AND  som_drg_grp_info.HOSPITAL_ID = #{queryParam.hospitalId}
                </if>
                <if test="queryParam.b16c!=null and queryParam.b16c!=''">
                    AND dscg_caty_codg_inhosp = #{queryParam.b16c}
                </if>
                <if test="queryParam.queryDrg!=null and queryParam.queryDrg!=''">
                    AND `drg_codg` = #{queryParam.queryDrg}
                </if>
                <if test="queryParam.tb16c!=null and queryParam.tb16c!=''">
                    AND dscg_caty_codg_inhosp = #{queryParam.b16c}
                </if>
                <if test="queryParam.tQueryDrg!=null and queryParam.tQueryDrg!=''">
                    AND `drg_codg` = #{queryParam.tQueryDrg}
                </if>
                <if test="queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and
                            queryParam.cy_end_date!=null and queryParam.cy_end_date!=''">
                    AND dscg_time BETWEEN #{queryParam.cy_start_date} and CONCAT(#{queryParam.cy_end_date},' 23:59:59')
                </if>
                <if test="queryParam.inStartTime != null and queryParam.inStartTime != '' and
                                  queryParam.inEndTime != null and queryParam.inEndTime != ''">
                    AND adm_time BETWEEN #{queryParam.inStartTime} AND CONCAT(#{queryParam.inEndTime},' 23:59:59')
                </if>
                <if test="queryParam.seStartTime!=null and queryParam.seStartTime!='' and
                                queryParam.seEndTime!=null and queryParam.seEndTime!=''">
                    AND setl_end_time BETWEEN #{queryParam.seStartTime} and CONCAT(#{queryParam.seEndTime},' 23:59:59')
                </if>
                <if test="queryParam.doctorIdList!=null">
                    AND (
                    deptdrt_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    chfdr_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    atddr_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    ipdr_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    resp_nurs_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    train_dr_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    intn_dr in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    codr_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    qltctrl_dr_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    qltctrl_nurs_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    )
                </if>
            )
            and FUND_PAY_TYPE!=''
            group by FUND_PAY_TYPE order by FUND_PAY_TYPE asc
        )y on x.fundPayType = y.fundPayType
        order by x.fundPayType asc
    </select>

    <select id="getInHosMedicalCostList" resultType="com.my.som.vo.firstPage.MedicalTreatmentCostVo">
        select
            IFNULL(SUM(com_med_servfee),0)+IFNULL(SUM(rhab_fee),0)+
            IFNULL(SUM(diag_fee),0)+IFNULL(SUM(treat_fee),0)+IFNULL(SUM(drugfee),0)+
            IFNULL(SUM(blood_blo_pro),0)+IFNULL(SUM(mcs_fee),0)+IFNULL(SUM(tcm_oth),0)+
            IFNULL(SUM(oth_fee),0) AS iptSumfee,
            <!-- IFNULL(SUM(ipt_sumfee_in_selfpay_amt),0) AS iptSumfeeInSelfpayAmt, -->
            IFNULL(SUM(com_med_servfee),0) AS comMedServfee,
            IFNULL(SUM(rhab_fee),0) AS rhabFee,
            IFNULL(SUM(diag_fee),0) AS diagFee,
            IFNULL(SUM(treat_fee),0) AS treatFee,
            IFNULL(SUM(drugfee),0) AS drugfee,
            IFNULL(SUM(blood_blo_pro),0) AS bloodBloPro,
            IFNULL(SUM(mcs_fee),0) AS mcsFee,
            IFNULL(SUM(tcm_oth),0) AS chineseOtherCost,
            IFNULL(SUM(oth_fee),0) AS othFee,
            IFNULL(SUM(abt_fee),0) AS abtFee,
            IFNULL(SUM(inspect_fee),0) AS inspectFee
        from som_drg_grp_info a
        <if test='queryParam.enableSeAns != null and queryParam.enableSeAns != "" and queryParam.enableSeAns == "1"'>
            INNER JOIN som_hi_invy_bas_info a1 on a1.id=a.SETTLE_LIST_ID
            INNER JOIN som_setl_cas_crsp p on a1.k00=p.k00
        </if>
        WHERE 1=1
        <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
            AND  a.HOSPITAL_ID = #{queryParam.hospitalId}
        </if>
        <if test="queryParam.b16c!=null and queryParam.b16c!=''">
            AND a.dscg_caty_codg_inhosp = #{queryParam.b16c}
        </if>
        <if test="queryParam.queryDrg!=null and queryParam.queryDrg!=''">
            AND a.drg_codg = #{queryParam.queryDrg}
        </if>
        <if test="queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and queryParam.cy_end_date!=null and queryParam.cy_end_date!=''">
            AND a.dscg_time BETWEEN #{queryParam.cy_start_date} and CONCAT(#{queryParam.cy_end_date},' 23:59:59')
        </if>
        <if test="queryParam.inStartTime != null and queryParam.inStartTime != '' and queryParam.inEndTime != null and queryParam.inEndTime != ''">
            AND a.adm_time BETWEEN #{queryParam.inStartTime} AND CONCAT(#{queryParam.inEndTime},' 23:59:59')
        </if>
        <if test="queryParam.seStartTime!=null and queryParam.seStartTime!='' and queryParam.seEndTime!=null and queryParam.seEndTime!=''">
            AND a.setl_end_time BETWEEN #{queryParam.seStartTime} and CONCAT(#{queryParam.seEndTime},' 23:59:59')
        </if>
        <if test="queryParam.doctorIdList!=null">
            AND (
            a.deptdrt_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.chfdr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.atddr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.ipdr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.resp_nurs_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.train_dr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.intn_dr in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.codr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.qltctrl_dr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.qltctrl_nurs_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            )
        </if>
    </select>

    <select id="getSettleListMedicalCostList" resultType="com.my.som.vo.firstPage.SettleListMedicalCostCountVo">
        select
        ifnull(t2.labl_name,'其他医疗费') AS medChrgItemname,
            IFNULL(SUM(t1.amt),0) AS itemTotalCost
        from som_hi_setl_invy_med_fee_info t1
        left join (SELECT data_val ,labl_name from  som_dic_code where code_type = 'MED_CHRGITM' ) t2 ON
        RIGHT('00' + t1.med_chrg_itemname, 2) = t2.data_val
        where hi_setl_invy_id in (
            select SETTLE_LIST_ID
            from som_drg_grp_info a

            <if test='queryParam.enableSeAns != null and queryParam.enableSeAns != "" and queryParam.enableSeAns == "1"'>
                INNER JOIN som_hi_invy_bas_info a1 on a1.id=a.SETTLE_LIST_ID
                INNER JOIN som_setl_cas_crsp p on a1.k00=p.k00
            </if>
            WHERE 1=1
            <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
                AND  a.HOSPITAL_ID = #{queryParam.hospitalId}
            </if>
            <if test="queryParam.b16c!=null and queryParam.b16c!=''">
                AND a.dscg_caty_codg_inhosp = #{queryParam.b16c}
            </if>
            <if test="queryParam.queryDrg!=null and queryParam.queryDrg!=''">
                AND a.drg_codg = #{queryParam.queryDrg}
            </if>
            <if test="queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and queryParam.cy_end_date!=null and queryParam.cy_end_date!=''">
                AND a.dscg_time BETWEEN #{queryParam.cy_start_date} and CONCAT(#{queryParam.cy_end_date},' 23:59:59')
            </if>
            <if test="queryParam.inStartTime != null and queryParam.inStartTime != '' and queryParam.inEndTime != null and queryParam.inEndTime != ''">
                AND a.adm_time BETWEEN #{queryParam.inStartTime} AND CONCAT(#{queryParam.inEndTime},' 23:59:59')
            </if>
            <if test="queryParam.seStartTime!=null and queryParam.seStartTime!='' and queryParam.seEndTime!=null and queryParam.seEndTime!=''">
                AND a.setl_end_time BETWEEN #{queryParam.seStartTime} and CONCAT(#{queryParam.seEndTime},' 23:59:59')
            </if>
            <if test="queryParam.doctorIdList!=null">
                AND (
                a.deptdrt_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                a.chfdr_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                a.atddr_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                a.ipdr_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                a.resp_nurs_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                a.train_dr_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                a.intn_dr in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                a.codr_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                a.qltctrl_dr_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                a.qltctrl_nurs_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                )
            </if>
        )
        and t1.med_chrg_itemname!=''
        group by t2.labl_name
    </select>

    <select id="getSettleListFundPayList" resultType="com.my.som.vo.firstPage.SettleListMedicalCostCountVo">
        SELECT
        ifnull(t2.labl_name,'其他基金')  AS medChrgItemname,
        IFNULL( SUM( t1.FUND_PAYAMT ), 0 ) AS itemTotalCost
        FROM
        som_fund_pay t1
        left join (SELECT data_val ,labl_name from  som_dic_code where code_type = 'FUND_PAY_TYPE' ) t2
        on t1.FUND_PAY_TYPE =  t2.data_val
        where t1.hi_setl_invy_id in (
            select SETTLE_LIST_ID
            from som_drg_grp_info a
            <if test='queryParam.enableSeAns != null and queryParam.enableSeAns != "" and queryParam.enableSeAns == "1"'>
                INNER JOIN som_hi_invy_bas_info a1 on a1.id=a.SETTLE_LIST_ID
                INNER JOIN som_setl_cas_crsp p on a1.k00=p.k00
            </if>
            WHERE 1=1
            <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
                AND  a.HOSPITAL_ID = #{queryParam.hospitalId}
            </if>
            <if test="queryParam.b16c!=null and queryParam.b16c!=''">
                AND a.dscg_caty_codg_inhosp = #{queryParam.b16c}
            </if>
            <if test="queryParam.queryDrg!=null and queryParam.queryDrg!=''">
                AND a.drg_codg = #{queryParam.queryDrg}
            </if>
            <if test="queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and queryParam.cy_end_date!=null and queryParam.cy_end_date!=''">
                AND a.dscg_time BETWEEN #{queryParam.cy_start_date} and CONCAT(#{queryParam.cy_end_date},' 23:59:59')
            </if>
            <if test="queryParam.inStartTime != null and queryParam.inStartTime != '' and queryParam.inEndTime != null and queryParam.inEndTime != ''">
                AND a.adm_time BETWEEN #{queryParam.inStartTime} AND CONCAT(#{queryParam.inEndTime},' 23:59:59')
            </if>
            <if test="queryParam.seStartTime!=null and queryParam.seStartTime!='' and queryParam.seEndTime!=null and queryParam.seEndTime!=''">
                AND a.setl_end_time BETWEEN #{queryParam.seStartTime} and CONCAT(#{queryParam.seEndTime},' 23:59:59')
            </if>
            <if test="queryParam.doctorIdList!=null">
                AND (
                a.deptdrt_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                a.chfdr_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                a.atddr_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                a.ipdr_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                a.resp_nurs_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                a.train_dr_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                a.intn_dr in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                a.codr_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                a.qltctrl_dr_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                a.qltctrl_nurs_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                )
            </if>
        )
        and t1.FUND_PAY_TYPE!=''
        group by  t2.labl_name
    </select>

    <select id="getPersonalPayList" resultType="com.my.som.vo.hospitalAnalysis.PsnSelfpayVo">
        select
            IFNULL(SUM(psn_selfpay),0) AS psnSelfpay,
            IFNULL(SUM(psn_ownpay),0) AS psnOwnpay,
            IFNULL(SUM(acct_pay),0) AS acctPay,
            IFNULL(SUM(psn_cashpay),0) AS psnCashpay
        from som_drg_grp_info a
        <if test='queryParam.enableSeAns != null and queryParam.enableSeAns != "" and queryParam.enableSeAns == "1"'>
            INNER JOIN som_hi_invy_bas_info a1 on a1.id=a.SETTLE_LIST_ID
            INNER JOIN som_setl_cas_crsp p on a1.k00=p.k00
        </if>
        WHERE 1=1
        <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
            AND  a.HOSPITAL_ID = #{queryParam.hospitalId}
        </if>
        <if test="queryParam.b16c!=null and queryParam.b16c!=''">
            AND a.dscg_caty_codg_inhosp = #{queryParam.b16c}
        </if>
        <if test="queryParam.queryDrg!=null and queryParam.queryDrg!=''">
            AND a.drg_codg = #{queryParam.queryDrg}
        </if>
        <if test="queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and queryParam.cy_end_date!=null and queryParam.cy_end_date!=''">
            AND a.dscg_time BETWEEN #{queryParam.cy_start_date} and CONCAT(#{queryParam.cy_end_date},' 23:59:59')
        </if>
        <if test="queryParam.inStartTime != null and queryParam.inStartTime != '' and queryParam.inEndTime != null and queryParam.inEndTime != ''">
            AND a.adm_time BETWEEN #{queryParam.inStartTime} AND CONCAT(#{queryParam.inEndTime},' 23:59:59')
        </if>
        <if test="queryParam.seStartTime!=null and queryParam.seStartTime!='' and queryParam.seEndTime!=null and queryParam.seEndTime!=''">
            AND a.setl_end_time BETWEEN #{queryParam.seStartTime} and CONCAT(#{queryParam.seEndTime},' 23:59:59')
        </if>
        <if test="queryParam.doctorIdList!=null">
            AND (
            a.deptdrt_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.chfdr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.atddr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.ipdr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.resp_nurs_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.train_dr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.intn_dr in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.codr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.qltctrl_dr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.qltctrl_nurs_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            )
        </if>
    </select>

</mapper>
