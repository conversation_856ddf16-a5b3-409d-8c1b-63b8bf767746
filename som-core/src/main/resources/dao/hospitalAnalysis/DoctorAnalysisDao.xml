<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.som.dao.hospitalAnalysis.DoctorAnalysisDao">
    <select id="list" resultType="com.my.som.vo.hospitalAnalysis.DoctorAnalysisInfo">
       select p.*
              <if test="queryParam.queryType==1">
                  <trim prefix=",">
                      m.name AS priOutHosDeptName
                  </trim>
              </if>
       from (
        select
            <if test="queryParam.queryType==1">
                x.dscg_caty_codg_inhosp AS priOutHosDeptCode,
                x.HOSPITAL_ID,
            </if>
            x.drCodg AS drCodg,
            x.drName AS drName,
            <if test="queryParam.queryType==2">
               x.dscg_caty_name_inhosp  AS doctorDepts,
            </if>
            IFNULL(count(1),0) AS totalMedicalNum,
            IFNULL(count(case when x.grp_stas = '1' then 1 else null end),0)   AS inGroupMedicalNum,
            IFNULL(count(case when x.grp_stas != '1' then 1 else null end),0)  AS notGroupMedicalNum,
           IFNULL( count( CASE WHEN x.diseType = '3' THEN 1 ELSE NULL END ), 0 ) AS normalMedicalNum,
            IFNULL(count(distinct (case when x.grp_stas = '1' then x.drg_codg else null end)),0)   AS drgGroupNum,
            IFNULL(round(SUM(CASE WHEN x.grp_stas = '1' THEN x.drg_wt ELSE NULL END)/
                NULLIF (COUNT(CASE WHEN x.grp_stas = '1' THEN 1 ELSE NULL END),0),2),0)   AS cmi,
            IFNULL(SUM(CASE WHEN x.grp_stas = '1' THEN x.drg_wt ELSE NULL END),0)    AS totalDrgWeight,
            IFNULL(ROUND(AVG(x.act_ipt), 2),0)   AS avgDays,
            IFNULL(ROUND(AVG(CASE WHEN x.grp_stas = '1' THEN x.act_ipt ELSE NULL END), 2),0)   AS inGroupAvgDays,
            IFNULL(ROUND(AVG(x.ipt_sumfee), 2),0)  AS avgCost,
            IFNULL(ROUND(AVG(CASE WHEN x.grp_stas = '1' THEN x.ipt_sumfee ELSE NULL END), 2),0)    AS inGroupAvgCost,
            IFNULL(ROUND(SUM(CASE WHEN x.grp_stas = '1' THEN x.act_ipt ELSE NULL END / NULLIF(x.standard_ave_hosp_day,0)) /
                NULLIF(COUNT(CASE WHEN x.grp_stas = '1' THEN 1 ELSE NULL END),0), 2),0)  AS timeIndex,
            IFNULL(ROUND(SUM(CASE WHEN x.grp_stas = '1' THEN x.ipt_sumfee ELSE NULL END / NULLIF(x.standard_avg_fee,0)) /
                NULLIF(COUNT(CASE WHEN x.grp_stas = '1' THEN 1 ELSE NULL END),0), 2),0)   AS costIndex,

        IFNULL(ROUND(SUM(x.drugfee),2),0) AS medicalCost,<!--  药品费   -->
        IFNULL(CONCAT(ROUND(SUM(x.drugfee)/NULLIF(SUM(x.ipt_sumfee),0)*100,2),'%'),0) AS medicalCostRate,<!--  药品费占比   -->
        IFNULL(ROUND(SUM(x.mcs_fee),2),0) AS materialCost, <!--  耗材费   -->
        IFNULL(CONCAT(ROUND(SUM(x.mcs_fee)/NULLIF(SUM(x.ipt_sumfee),0)*100,2),'%'),0) AS materialCostRate, <!--  耗材费占比   -->
            IFNULL(ROUND(SUM(x.totlSco),2),0) AS totlSco,
           -- x.price,
            IFNULL(ROUND(SUM(x.referSco),2),0) AS referSco,
            IFNULL(ROUND(SUM(x.ipt_sumfee),2),0) AS iptSumfee,
            IFNULL(ROUND(SUM(x.preCost),2),0) AS preCost,
            IFNULL(ROUND(SUM(x.preCost - x.ipt_sumfee),2),0) AS diff
        from (
            select
                <if test="queryParam.doctorType!=null and queryParam.doctorType!=''">
                    (CASE #{queryParam.doctorType}#
                        WHEN 'dct0' THEN a.ipdr_code
                        WHEN 'dct1' THEN a.atddr_code
                        WHEN 'dct2' THEN a.deptdrt_code
                        WHEN 'dct3' THEN a.chfdr_code
                        WHEN 'dct4' THEN a.train_dr_code
                        WHEN 'dct5' THEN a.qltctrl_dr_code
                        WHEN 'dct6' THEN a.intn_dr
                        ELSE  a.ipdr_name
                    END) AS drCodg,
                    (CASE #{queryParam.doctorType}#
                        WHEN 'dct0' THEN a.ipdr_name
                        WHEN 'dct1' THEN a.atddr_name
                        WHEN 'dct2' THEN a.deptdrt_name
                        WHEN 'dct3' THEN a.chfdr_name
                        WHEN 'dct4' THEN a.train_dr_name
                        WHEN 'dct5' THEN a.qltctrl_dr_name
                        WHEN 'dct6' THEN a.intn_dr
                        ELSE  a.ipdr_name
                    END) AS drName,
                </if>
                <if test="queryParam.doctorType==null or queryParam.doctorType==''">
                    c.drCodg as drCodg,
                    c.drName as drName,
                </if>
                a.grp_stas as grp_stas,
                a.drg_codg as drg_codg,
                a.drg_wt as drg_wt,
                a.act_ipt as act_ipt,
                a.ipt_sumfee as ipt_sumfee,
                a.standard_ave_hosp_day as standard_ave_hosp_day,
                a.standard_avg_fee as standard_avg_fee,
                a.dscg_caty_codg_inhosp as dscg_caty_codg_inhosp,
                c.dscg_caty_name_inhosp AS dscg_caty_name_inhosp,
                a.drugfee as drugfee,
                a.mcs_fee as mcs_fee,
                a.HOSPITAL_ID,
                sco.price,
                sco.totl_sco  AS totlSco,
                sco.refer_sco AS referSco,
                sco.forecast_fee AS preCost,
                sco.profitloss AS profitloss,
                sco.dise_type as diseType
            from som_drg_grp_info a
        LEFT JOIN som_drg_sco sco ON a.settle_list_id = sco.settle_list_id
            <if test="queryParam.enableSeAns != null and queryParam.enableSeAns != '' and queryParam.enableSeAns == 1">
                INNER JOIN som_hi_invy_bas_info b
                    ON a.SETTLE_LIST_ID = b.ID
                INNER JOIN som_setl_cas_crsp p
                    ON b.K00 = p.K00
            </if>
            <if test="queryParam.doctorType==null or queryParam.doctorType==''">
                inner join (
                    select
                        b.settle_list_id as settle_list_id,
                        b.drCodg as drCodg,
                        b.drName as drName,
                b.dscg_caty_name_inhosp
                from (
                        select
                            a.SETTLE_LIST_ID as settle_list_id,
                a.ipdr_code as drCodg,
                a.ipdr_name as drName,
                d.blng_org_org_name as dscg_caty_name_inhosp
                        FROM som_drg_grp_info a
                left join som_back_user d on d.username = a.ipdr_code
                        <if test="queryParam.enableSeAns != null and queryParam.enableSeAns != '' and queryParam.enableSeAns == 1">
                            INNER JOIN som_hi_invy_bas_info b
                            ON som_drg_grp_info.SETTLE_LIST_ID = b.ID
                            INNER JOIN som_setl_cas_crsp p
                            ON b.K00 = p.K00
                        </if>
                        where a.ipdr_name is not null and ipdr_name !='-' and ipdr_name !='未填写'
                        <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
                            AND  a.HOSPITAL_ID = #{queryParam.hospitalId}
                        </if>
                        <if test="queryParam.b16c!=null and queryParam.b16c!=''">
                            AND a.dscg_caty_codg_inhosp = #{queryParam.b16c}
                        </if>
                        <if test="queryParam.inStartTime != null and queryParam.inStartTime != '' and
                                          queryParam.inEndTime != null and queryParam.inEndTime != ''">
                            AND a.adm_time BETWEEN #{queryParam.inStartTime} and CONCAT(#{queryParam.inEndTime},' 23:59:59')
                        </if>
                        <if test="queryParam.cy_start_date != null and queryParam.cy_start_date != '' and
                                  queryParam.cy_end_date != null and queryParam.cy_end_date != ''">
                            AND a.dscg_time BETWEEN #{queryParam.cy_start_date} and CONCAT(#{queryParam.cy_end_date},' 23:59:59')
                        </if>
                        <if test="queryParam.seStartTime!=null and queryParam.seStartTime!='' and queryParam.seEndTime!=null and queryParam.seEndTime!=''">
                            AND a.setl_end_time BETWEEN #{queryParam.seStartTime} and CONCAT(#{queryParam.seEndTime},' 23:59:59')
                        </if>
                        <if test="queryParam.drCodg!=null and queryParam.drCodg!=''">
                            AND a.ipdr_code  = #{queryParam.drCodg}
                        </if>
                    ) b group by settle_list_id,drCodg,drName,
                dscg_caty_name_inhosp
                ) c on a.settle_list_id = c.settle_list_id
            </if>
            where 1=1
            <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
                AND  a.HOSPITAL_ID = #{queryParam.hospitalId}
            </if>
            <if test="queryParam.b16c!=null and queryParam.b16c!=''">
                AND a.dscg_caty_codg_inhosp = #{queryParam.b16c}
            </if>
            <if test="queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and
                                                    queryParam.cy_end_date!=null and queryParam.cy_end_date!=''">
                AND a.dscg_time BETWEEN #{queryParam.cy_start_date} and CONCAT(#{queryParam.cy_end_date},' 23:59:59')
            </if>
            <if test="queryParam.inStartTime != null and queryParam.inStartTime != '' and
                      queryParam.inEndTime != null and queryParam.inEndTime != ''">
                AND a.adm_time BETWEEN #{queryParam.inStartTime} and CONCAT(#{queryParam.inEndTime},' 23:59:59')
            </if>
            <if test="queryParam.seStartTime!=null and queryParam.seStartTime!='' and queryParam.seEndTime!=null and queryParam.seEndTime!=''">
                AND a.setl_end_time BETWEEN #{queryParam.seStartTime} and CONCAT(#{queryParam.seEndTime},' 23:59:59')
            </if>
            <if test="queryParam.doctorIdList!=null">
                AND (
                a.deptdrt_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                a.chfdr_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                a.atddr_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                a.ipdr_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                a.resp_nurs_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                a.train_dr_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                a.intn_dr in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                a.codr_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                a.qltctrl_dr_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                a.qltctrl_nurs_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                )
            </if>
        ) x
        where x.drName is not null and x.drName !='-' and x.drName !='未填写'
        <if test="queryParam.drCodg!=null and queryParam.drCodg!=''">
            AND x.drCodg= #{queryParam.drCodg}
        </if>
        <if test="queryParam.doctorIdList!=null">
            AND x.drCodg in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="queryParam.queryType==1">
            GROUP BY x.dscg_caty_codg_inhosp,x.dscg_caty_name_inhosp,x.drCodg,x.drName, x.HOSPITAL_ID
        </if>
        <if test="queryParam.queryType==2">
            GROUP BY x.drCodg,x.drName, x.dscg_caty_name_inhosp
        </if>
        ORDER BY totalDrgWeight desc
       ) p
        <if test="queryParam.queryType==1">
            left join som_dept m
            on p.priOutHosDeptCode = m.code
            AND p.HOSPITAL_ID = m.HOSPITAL_ID
        </if>

    </select>

    <select id="getCountInfo" resultType="com.my.som.vo.hospitalAnalysis.DoctorAnalysisCountInfo">
        select
            x.drCodg AS drCodg,
            x.drName AS drName,
            IFNULL(count(1),0) AS totalMedicalNum,
            IFNULL(ROUND(count(1) / NULLIF(MAX(y.totalMedicalNum),0)*100,2),0) AS totalMedicalNumRate,
            IFNULL(count(case when x.grp_stas = '1' then 1 else null end),0)   AS inGroupMedicalNum,
            IFNULL(ROUND(count(case when x.grp_stas = '1' then 1 else null end) /
              NULLIF(MAX(y.inGroupMedicalNum),0)*100,2),0) AS  inGroupMedicalNumRate,
            IFNULL(count(case when x.grp_stas != '1' then 1 else null end),0)  AS notGroupMedicalNum,
            IFNULL(ROUND(count(case when x.grp_stas != '1' then 1 else null end) /
              NULLIF(MAX(y.inGroupMedicalNum),0)*100,2),0) AS  notGroupMedicalNumRate,
            IFNULL(count(distinct (case when x.grp_stas = '1' then x.drg_codg else null end)),0)   AS drgGroupNum,
            IFNULL(ROUND(count(distinct (case when x.grp_stas = '1' then x.drg_codg else null end)) /
              NULLIF(MAX(y.drgGroupNum),0)*100,2),0) AS  drgGroupNumRate,
            IFNULL(round(SUM(CASE WHEN x.grp_stas = '1' THEN x.drg_wt ELSE NULL END)/
              NULLIF (COUNT(CASE WHEN x.grp_stas = '1' THEN 1 ELSE NULL END),0),2),0)   AS cmi,
            IFNULL(MAX(y.hosCmi),0) AS hosCmi,
            IFNULL(SUM(CASE WHEN x.grp_stas = '1' THEN x.drg_wt ELSE NULL END),0)    AS totalDrgWeight,
            IFNULL(ROUND(SUM(CASE WHEN x.grp_stas = '1' THEN x.drg_wt ELSE NULL END) /
                NULLIF(MAX(y.totalDrgWeight),0)*100,2),0) AS  totalDrgWeightRate,
            IFNULL(ROUND(AVG(x.act_ipt), 2),0)   AS avgDays,
            IFNULL(MAX(y.hosAvgDays),0) as hosAvgDays,
            IFNULL(ROUND(AVG(CASE WHEN x.grp_stas = '1' THEN x.act_ipt ELSE NULL END), 2),0)   AS inGroupAvgDays,
            IFNULL(MAX(y.hosInGroupAvgDays),0) as hosInGroupAvgDays,
            IFNULL(ROUND(AVG(x.ipt_sumfee), 2),0)  AS avgCost,
            IFNULL(MAX(y.hosAvgCost),0) as hosAvgCost,
            IFNULL(ROUND(AVG(CASE WHEN x.grp_stas = '1' THEN x.ipt_sumfee ELSE NULL END), 2),0)    AS inGroupAvgCost,
            IFNULL(MAX(y.hosInGroupAvgCost),0) as hosInGroupAvgCost,
            IFNULL(ROUND(SUM(CASE WHEN x.grp_stas = '1' THEN x.act_ipt ELSE NULL END / NULLIF(x.standard_ave_hosp_day,0)) /
              NULLIF(COUNT(CASE WHEN x.grp_stas = '1' THEN 1 ELSE NULL END),0), 2),0)  AS timeIndex,
            IFNULL(MAX(y.hosTimeIndex),0) as hosTimeIndex,
            IFNULL(ROUND(SUM(CASE WHEN x.grp_stas = '1' THEN x.ipt_sumfee ELSE NULL END / NULLIF(x.standard_avg_fee,0)) /
              NULLIF(COUNT(CASE WHEN x.grp_stas = '1' THEN 1 ELSE NULL END),0), 2),0)   AS costIndex,
            IFNULL(MAX(y.hosCostIndex),0) as hosCostIndex,
            IFNULL(ROUND(AVG(x.drugfee), 2),0)   AS avgDrugFee,
            IFNULL(MAX(y.hosAvgMedicalCost),0) as hosAvgMedicalCost,
            IFNULL(ROUND(AVG(x.mcs_fee), 2),0)   AS avgMcsFee,
            IFNULL(MAX(y.hosAvgMaterialCost),0) as hosAvgMaterialCost
        from (
        select
        <if test="queryParam.doctorType!=null and queryParam.doctorType!=''">
            (CASE #{queryParam.doctorType}#
            WHEN 'dct0' THEN a.ipdr_code
            WHEN 'dct1' THEN a.atddr_code
            WHEN 'dct2' THEN a.deptdrt_code
            WHEN 'dct3' THEN a.chfdr_code
            WHEN 'dct4' THEN a.train_dr_code
            WHEN 'dct5' THEN a.qltctrl_dr_code
            WHEN 'dct6' THEN a.intn_dr
            ELSE  a.ipdr_name
            END) AS drCodg,
            (CASE #{queryParam.doctorType}#
            WHEN 'dct0' THEN a.ipdr_name
            WHEN 'dct1' THEN a.atddr_name
            WHEN 'dct2' THEN a.deptdrt_name
            WHEN 'dct3' THEN a.chfdr_name
            WHEN 'dct4' THEN a.train_dr_name
            WHEN 'dct5' THEN a.qltctrl_dr_name
            WHEN 'dct6' THEN a.intn_dr
            ELSE  a.ipdr_name
            END) AS drName,
        </if>
        <if test="queryParam.doctorType==null or queryParam.doctorType==''">
            c.drCodg as drCodg,
            c.drName as drName,
        </if>
        a.grp_stas as grp_stas,
        a.drg_codg as drg_codg,
        a.drg_wt as drg_wt,
        a.act_ipt as act_ipt,
        a.ipt_sumfee as ipt_sumfee,
        a.standard_ave_hosp_day as standard_ave_hosp_day,
        a.standard_avg_fee as standard_avg_fee,
        a.dscg_caty_codg_inhosp as dscg_caty_codg_inhosp,
        a.dscg_caty_name_inhosp AS dscg_caty_name_inhosp,
        a.drugfee as drugfee,
        a.mcs_fee as mcs_fee
        from som_drg_grp_info a
        <if test="queryParam.enableSeAns != null and queryParam.enableSeAns != '' and queryParam.enableSeAns == 1">
            INNER JOIN som_hi_invy_bas_info b
            ON a.SETTLE_LIST_ID = b.ID
            INNER JOIN som_setl_cas_crsp p
            ON b.K00 = p.K00
        </if>
        <if test="queryParam.doctorType==null or queryParam.doctorType==''">
            inner join (
            select
                b.settle_list_id as settle_list_id,
                b.drCodg as drCodg,
                b.drName as drName
            from (
            select
                SETTLE_LIST_ID as settle_list_id,
                ipdr_code as drCodg,
                ipdr_name as drName
            FROM som_drg_grp_info
            <if test="queryParam.enableSeAns != null and queryParam.enableSeAns != '' and queryParam.enableSeAns == 1">
                INNER JOIN som_hi_invy_bas_info b
                ON som_drg_grp_info.SETTLE_LIST_ID = b.ID
                INNER JOIN som_setl_cas_crsp p
                ON b.K00 = p.K00
            </if>
            where ipdr_name is not null and ipdr_name !='-' and ipdr_name !='未填写'
            <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
                AND  som_drg_grp_info.HOSPITAL_ID = #{queryParam.hospitalId}
            </if>
            <if test="queryParam.b16c!=null and queryParam.b16c!=''">
                AND dscg_caty_codg_inhosp = #{queryParam.b16c}
            </if>
            <if test="queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and
                                                queryParam.cy_end_date!=null and queryParam.cy_end_date!=''">
                AND dscg_time BETWEEN #{queryParam.cy_start_date} and CONCAT(#{queryParam.cy_end_date},' 23:59:59')
            </if>
            <if test="queryParam.inStartTime != null and queryParam.inStartTime != '' and
                  queryParam.inEndTime != null and queryParam.inEndTime != ''">
                AND adm_time BETWEEN #{queryParam.inStartTime} and CONCAT(#{queryParam.inEndTime},' 23:59:59')
            </if>
            <if test="queryParam.seStartTime!=null and queryParam.seStartTime!='' and queryParam.seEndTime!=null and queryParam.seEndTime!=''">
                AND setl_end_time BETWEEN #{queryParam.seStartTime} and CONCAT(#{queryParam.seEndTime},' 23:59:59')
            </if>
            <if test="queryParam.drCodg!=null and queryParam.drCodg!=''">
                AND `INHOS_DOCTOR_CODE` = #{queryParam.drCodg}
            </if>
                ) b group by settle_list_id,drCodg,drName
            ) c on a.settle_list_id = c.settle_list_id
        </if>
        where 1=1
        <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
            AND  a.HOSPITAL_ID = #{queryParam.hospitalId}
        </if>
        <if test="queryParam.b16c!=null and queryParam.b16c!=''">
            AND a.dscg_caty_codg_inhosp = #{queryParam.b16c}
        </if>
        <if test="queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and
                                                queryParam.cy_end_date!=null and queryParam.cy_end_date!=''">
            AND a.dscg_time BETWEEN #{queryParam.cy_start_date} and CONCAT(#{queryParam.cy_end_date},' 23:59:59')
        </if>
        <if test="queryParam.inStartTime != null and queryParam.inStartTime != '' and
                  queryParam.inEndTime != null and queryParam.inEndTime != ''">
            AND a.adm_time BETWEEN #{queryParam.inStartTime} and CONCAT(#{queryParam.inEndTime},' 23:59:59')
        </if>
        <if test="queryParam.seStartTime!=null and queryParam.seStartTime!='' and queryParam.seEndTime!=null and queryParam.seEndTime!=''">
            AND a.setl_end_time BETWEEN #{queryParam.seStartTime} and CONCAT(#{queryParam.seEndTime},' 23:59:59')
        </if>
        <if test="queryParam.doctorIdList!=null">
            AND (
            a.deptdrt_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.chfdr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.atddr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.ipdr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.resp_nurs_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.train_dr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.intn_dr in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.codr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.qltctrl_dr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.qltctrl_nurs_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            )
        </if>
        ) x
        cross join (
            select
                IFNULL(count(1),0) AS totalMedicalNum,
                IFNULL(count(case when grp_stas = '1' then 1 else null end),0) AS inGroupMedicalNum,
                IFNULL(count(case when grp_stas != '1' then 1 else null end),0) AS notGroupMedicalNum,
                IFNULL(count(distinct (case when grp_stas = '1' then drg_codg else null end)),0) AS drgGroupNum,
                IFNULL(round(SUM(CASE WHEN grp_stas = '1' THEN drg_wt ELSE NULL END)/
                NULLIF (COUNT(CASE WHEN grp_stas = '1' THEN 1 ELSE NULL END),0),2),0) AS hosCmi,
                IFNULL(SUM(CASE WHEN grp_stas = '1' THEN drg_wt ELSE NULL END),0) AS totalDrgWeight,
                IFNULL(ROUND(AVG(act_ipt), 2),0) AS hosAvgDays,
                IFNULL(ROUND(AVG(CASE WHEN grp_stas = '1' THEN act_ipt ELSE NULL END), 2),0) AS hosInGroupAvgDays,
                IFNULL(ROUND(AVG(ipt_sumfee), 2),0) AS hosAvgCost,
                IFNULL(ROUND(AVG(CASE WHEN grp_stas = '1' THEN ipt_sumfee ELSE NULL END), 2),0) AS hosInGroupAvgCost,
                IFNULL(ROUND(SUM(CASE WHEN grp_stas = '1' THEN act_ipt ELSE NULL END / NULLIF(standard_ave_hosp_day,0))
                  /NULLIF(COUNT(CASE WHEN grp_stas = '1' THEN 1 ELSE NULL END),0), 2),0) AS hosTimeIndex,
                IFNULL(ROUND(SUM(CASE WHEN grp_stas = '1' THEN ipt_sumfee ELSE NULL END /
                  NULLIF(standard_avg_fee,0)) /NULLIF(COUNT(CASE WHEN grp_stas = '1' THEN 1 ELSE NULL END),0), 2),0) AS hosCostIndex,
                IFNULL(ROUND(AVG(drugfee), 2),0) AS hosAvgMedicalCost,
                IFNULL(ROUND(AVG(mcs_fee), 2),0)   AS hosAvgMaterialCost
            from som_drg_grp_info
            <if test="queryParam.enableSeAns != null and queryParam.enableSeAns != '' and queryParam.enableSeAns == 1">
                INNER JOIN som_hi_invy_bas_info b
                ON som_drg_grp_info.SETTLE_LIST_ID = b.ID
                INNER JOIN som_setl_cas_crsp p
                ON b.K00 = p.K00
            </if>
            where 1 = 1
            <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
                AND  HOSPITAL_ID = #{queryParam.hospitalId}
            </if>
            <if test="queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and
                                                    queryParam.cy_end_date!=null and queryParam.cy_end_date!=''">
                AND dscg_time BETWEEN #{queryParam.cy_start_date} and CONCAT(#{queryParam.cy_end_date},' 23:59:59')
            </if>
            <if test="queryParam.inStartTime != null and queryParam.inStartTime != '' and
                      queryParam.inEndTime != null and queryParam.inEndTime != ''">
                AND adm_time BETWEEN #{queryParam.inStartTime} and CONCAT(#{queryParam.inEndTime},' 23:59:59')
            </if>
            <if test="queryParam.seStartTime!=null and queryParam.seStartTime!='' and queryParam.seEndTime!=null and queryParam.seEndTime!=''">
                AND setl_end_time BETWEEN #{queryParam.seStartTime} and CONCAT(#{queryParam.seEndTime},' 23:59:59')
            </if>
        ) y
        where x.drName is not null and x.drName !='-' and x.drName !='未填写'
        <if test="queryParam.drCodg!=null and queryParam.drCodg!=''">
            AND x.drCodg= #{queryParam.drCodg}
        </if>
        <if test="queryParam.doctorIdList!=null">
            AND x.drCodg in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="queryParam.queryType==1">
            GROUP BY x.dscg_caty_codg_inhosp,x.dscg_caty_name_inhosp,x.drCodg,x.drName
        </if>
        <if test="queryParam.queryType==2">
            GROUP BY x.drCodg,x.drName
        </if>
        ORDER BY totalDrgWeight desc
    </select>
</mapper>
