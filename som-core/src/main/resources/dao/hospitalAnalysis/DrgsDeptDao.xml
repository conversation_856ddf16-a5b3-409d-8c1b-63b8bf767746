<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.som.dao.hospitalAnalysis.DrgsDeptDao">
    <select id="list" resultType="com.my.som.vo.hospitalAnalysis.DrgsDeptIndexVo">
        SELECT a.*,
               b.NAME as priOutHosDeptName
        FROM
             (
                 SELECT a.dscg_caty_codg_inhosp AS priOutHosDeptCode,
                        a.dscg_caty_codg_inhosp AS dept_code,
                        a.HOSPITAL_ID,

                        IFNULL(count(1),0) AS totalMedicalNum,
                        COUNT(case when a.grp_stas = '1' then 1 else null end) AS inGroupMedicalNum,
                        COUNT(case when a.grp_stas != '1' then 1 else null end) AS notGroupMedicalNum,
                        IFNULL(count(distinct (case when a.grp_stas = '1' then a.drg_codg else null end)),0) AS drgGroupNum,
                        IFNULL(round(SUM(CASE WHEN a.grp_stas = '1' THEN a.drg_wt ELSE NULL END)/
                                     NULLIF (COUNT(CASE WHEN a.grp_stas = '1' THEN 1 ELSE NULL END),0),2),0)   AS cmi,
                        ROUND(IFNULL(SUM(a.drg_wt),0),2) AS totalDrgWeight, <!--总权重-->
                        IFNULL(ROUND(AVG(a.act_ipt), 2),0)   AS avgDays,
                        IFNULL(ROUND(AVG(CASE WHEN a.grp_stas = '1' THEN a.act_ipt ELSE NULL END), 2),0)   AS inGroupAvgDays,
                        IFNULL(ROUND(AVG(a.ipt_sumfee), 2),0)  AS avgCost,
                        IFNULL(ROUND(AVG(CASE WHEN a.grp_stas = '1' THEN a.ipt_sumfee ELSE NULL END), 2),0)    AS inGroupAvgCost,
                        IFNULL(ROUND(SUM(CASE WHEN a.grp_stas = '1' THEN a.act_ipt ELSE NULL END / NULLIF(a.standard_ave_hosp_day,0)) /
                                     NULLIF(COUNT(CASE WHEN a.grp_stas = '1' THEN 1 ELSE NULL END),0), 2),0)  AS timeIndex,
                        IFNULL(ROUND(SUM(CASE WHEN a.grp_stas = '1' THEN a.ipt_sumfee ELSE NULL END / NULLIF(a.standard_avg_fee,0)) /
                                     NULLIF(COUNT(CASE WHEN a.grp_stas = '1' THEN 1 ELSE NULL END),0), 2),0)   AS costIndex,

                         IFNULL(ROUND(SUM(a.drugfee),2),0) AS medicalCost,<!--  药品费   -->
                         IFNULL(CONCAT(ROUND(SUM(a.drugfee)/NULLIF(SUM(a.ipt_sumfee),0)*100,2),'%'),0) AS medicalCostRate,<!--  药品费占比   -->
                         IFNULL(ROUND(SUM(a.mcs_fee),2),0) AS materialCost, <!--  耗材费   -->
                         IFNULL(CONCAT(ROUND(SUM(a.mcs_fee)/NULLIF(SUM(a.ipt_sumfee),0)*100,2),'%'),0) AS materialCostRate, <!--  耗材费占比   -->
                        e.price,
                         IFNULL(ROUND(IFNULL(SUM(e.totlSco),0),2),0) AS totlSco,
                         IFNULL(ROUND(IFNULL(SUM(a.ipt_sumfee),0),2),0) AS sumfee,<!--  总费用   -->
                         IFNULL(ROUND(IFNULL(SUM(e.ycCost),0),2),0) AS ycCost,<!--  药品费   -->
                         IFNULL(ROUND(IFNULL(SUM(e.ycCost),0) - IFNULL(SUM(a.ipt_sumfee),0),2),0) AS ykje  <!--  盈亏金额   -->
                 from som_drg_grp_info a
                    LEFT JOIN
                    (
                        SELECT
                            a.SETTLE_LIST_ID,
                            a.dise_type,
                            a.price,
                            a.totl_sco AS totlSco,
                            a.forecast_fee AS ycCost,
                            a.profitloss AS profitloss,
                            a.sumfee
                            FROM som_drg_sco a
                    ) e
                        ON a.SETTLE_LIST_ID = e.SETTLE_LIST_ID
                <if test="queryParam.enableSeAns != null and queryParam.enableSeAns != '' and queryParam.enableSeAns == 1">
                    INNER JOIN som_hi_invy_bas_info b
                    ON a.SETTLE_LIST_ID = b.ID
                    INNER JOIN som_setl_cas_crsp p
                    ON b.K00 = p.K00
                </if>
                 where 1 = 1
                 <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
                   AND a.HOSPITAL_ID = #{queryParam.hospitalId}
                 </if>
                 <if test="queryParam.b16c!=null and queryParam.b16c!=''">
                   AND a.dscg_caty_codg_inhosp = #{queryParam.b16c}
                 </if>
                 <if test="queryParam.drCodg!=null and queryParam.drCodg!=''">
                   AND a.ipdr_code = #{queryParam.drCodg}
                 </if>
        <if test="queryParam.inStartTime != null and queryParam.inStartTime != '' and
        queryParam.inEndTime != null and queryParam.inEndTime != ''">
            AND a.adm_time BETWEEN #{queryParam.inStartTime} and CONCAT(#{queryParam.inEndTime},' 23:59:59')
        </if>
        <if test="queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and
        queryParam.cy_end_date!=null and queryParam.cy_end_date!=''">
            AND a.dscg_time BETWEEN #{queryParam.cy_start_date} and CONCAT(#{queryParam.cy_end_date},' 23:59:59')
        </if>
        <if test="queryParam.seStartTime!=null and queryParam.seStartTime!='' and queryParam.seEndTime!=null and queryParam.seEndTime!=''">
            AND a.setl_end_time BETWEEN #{queryParam.seStartTime} and CONCAT(#{queryParam.seEndTime},' 23:59:59')
        </if>
        <if test="queryParam.doctorIdList!=null">
            AND (
            a.deptdrt_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.chfdr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.atddr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.ipdr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.resp_nurs_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.train_dr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.intn_dr in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.codr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.qltctrl_dr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.qltctrl_nurs_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            )
        </if>
        GROUP BY a.HOSPITAL_ID,a.dscg_caty_codg_inhosp,   a.price
        ORDER BY totalDrgWeight desc
        ) a
        left join som_dept b
        on a.priOutHosDeptCode = b.CODE
        AND a.HOSPITAL_ID = b.HOSPITAL_ID
    </select>

    <select id="getCountInfo" resultType="com.my.som.vo.hospitalAnalysis.DrgsDeptCountVo">
        select
            x.dscg_caty_codg_inhosp AS priOutHosDeptCode,
            IFNULL(x.dscg_caty_name_inhosp,'未填写科室') AS priOutHosDeptName,
            IFNULL(count(1),0) AS totalMedicalNum,
            IFNULL(ROUND(count(1) / NULLIF(MAX(y.totalMedicalNum),0)*100,2),0) AS totalMedicalNumRate,
            IFNULL(count(case when x.grp_stas = '1' then 1 else null end),0)   AS inGroupMedicalNum,
            IFNULL(ROUND(count(case when x.grp_stas = '1' then 1 else null end) /
              NULLIF(MAX(y.inGroupMedicalNum),0)*100,2),0) AS  inGroupMedicalNumRate,
            IFNULL(count(case when x.grp_stas != '1' then 1 else null end),0)  AS notGroupMedicalNum,
            IFNULL(ROUND(count(case when x.grp_stas != '1' then 1 else null end) /
              NULLIF(MAX(y.inGroupMedicalNum),0)*100,2),0) AS  notGroupMedicalNumRate,
            IFNULL(count(distinct (case when x.grp_stas = '1' then x.drg_codg else null end)),0)   AS drgGroupNum,
            IFNULL(ROUND(count(distinct (case when x.grp_stas = '1' then x.drg_codg else null end)) /
              NULLIF(MAX(y.drgGroupNum),0)*100,2),0) AS  drgGroupNumRate,
            IFNULL(round(SUM(CASE WHEN x.grp_stas = '1' THEN x.drg_wt ELSE NULL END)/
              NULLIF (COUNT(CASE WHEN x.grp_stas = '1' THEN 1 ELSE NULL END),0),2),0)   AS cmi,
            IFNULL(MAX(y.hosCmi),0) AS hosCmi,
            IFNULL(SUM(CASE WHEN x.grp_stas = '1' THEN x.drg_wt ELSE NULL END),0)    AS totalDrgWeight,
            IFNULL(ROUND(SUM(CASE WHEN x.grp_stas = '1' THEN x.drg_wt ELSE NULL END) /
                NULLIF(MAX(y.totalDrgWeight),0)*100,2),0) AS  totalDrgWeightRate,
            IFNULL(ROUND(AVG(x.act_ipt), 2),0)   AS avgDays,
            IFNULL(MAX(y.hosAvgDays),0) as hosAvgDays,
            IFNULL(ROUND(AVG(CASE WHEN x.grp_stas = '1' THEN x.act_ipt ELSE NULL END), 2),0)   AS inGroupAvgDays,
            IFNULL(MAX(y.hosInGroupAvgDays),0) as hosInGroupAvgDays,
            IFNULL(ROUND(AVG(x.ipt_sumfee), 2),0)  AS avgCost,
            IFNULL(MAX(y.hosAvgCost),0) as hosAvgCost,
            IFNULL(ROUND(AVG(CASE WHEN x.grp_stas = '1' THEN x.ipt_sumfee ELSE NULL END), 2),0)    AS inGroupAvgCost,
            IFNULL(MAX(y.hosInGroupAvgCost),0) as hosInGroupAvgCost,
            IFNULL(ROUND(SUM(CASE WHEN x.grp_stas = '1' THEN x.act_ipt ELSE NULL END / NULLIF(x.standard_ave_hosp_day,0)) /
            NULLIF(COUNT(CASE WHEN x.grp_stas = '1' THEN 1 ELSE NULL END),0), 2),0)  AS timeIndex,
            IFNULL(MAX(y.hosTimeIndex),0) as hosTimeIndex,
            IFNULL(ROUND(SUM(CASE WHEN x.grp_stas = '1' THEN x.ipt_sumfee ELSE NULL END / NULLIF(x.standard_avg_fee,0)) /
            NULLIF(COUNT(CASE WHEN x.grp_stas = '1' THEN 1 ELSE NULL END),0), 2),0)   AS costIndex,
            IFNULL(MAX(y.hosCostIndex),0) as hosCostIndex,
            IFNULL(ROUND(AVG(x.drugfee), 2),0)   AS avgDrugFee,
            IFNULL(MAX(y.hosAvgMedicalCost),0) as hosAvgMedicalCost,
            IFNULL(ROUND(AVG(x.mcs_fee), 2),0)   AS avgMcsFee,
            IFNULL(MAX(y.hosAvgMaterialCost),0) as hosAvgMaterialCost
        from (
            select
                a.grp_stas as grp_stas,
                a.drg_codg as drg_codg,
                a.drg_wt as drg_wt,
                a.act_ipt as act_ipt,
                a.ipt_sumfee as ipt_sumfee,
                a.standard_ave_hosp_day as standard_ave_hosp_day,
                a.standard_avg_fee as standard_avg_fee,
                a.dscg_caty_codg_inhosp as dscg_caty_codg_inhosp,
                a.dscg_caty_name_inhosp AS dscg_caty_name_inhosp,
                a.drugfee as drugfee,
                a.mcs_fee as mcs_fee
            from som_drg_grp_info a
            <if test="queryParam.enableSeAns != null and queryParam.enableSeAns != '' and queryParam.enableSeAns == 1">
                INNER JOIN som_hi_invy_bas_info b
                ON a.SETTLE_LIST_ID = b.ID
                INNER JOIN som_setl_cas_crsp p
                ON b.K00 = p.K00
            </if>
            where 1 = 1
            <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
                AND  a.HOSPITAL_ID = #{queryParam.hospitalId}
            </if>
            <if test="queryParam.b16c!=null and queryParam.b16c!=''">
                AND a.dscg_caty_codg_inhosp = #{queryParam.b16c}
            </if>
            <if test="queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and
                                                    queryParam.cy_end_date!=null and queryParam.cy_end_date!=''">
                AND a.dscg_time BETWEEN #{queryParam.cy_start_date} and CONCAT(#{queryParam.cy_end_date},' 23:59:59')
            </if>
            <if test="queryParam.inStartTime != null and queryParam.inStartTime != '' and
                      queryParam.inEndTime != null and queryParam.inEndTime != ''">
                AND a.adm_time BETWEEN #{queryParam.inStartTime} and CONCAT(#{queryParam.inEndTime},' 23:59:59')
            </if>
            <if test="queryParam.seStartTime!=null and queryParam.seStartTime!='' and queryParam.seEndTime!=null and queryParam.seEndTime!=''">
                AND a.setl_end_time BETWEEN #{queryParam.seStartTime} and CONCAT(#{queryParam.seEndTime},' 23:59:59')
            </if>
            <if test="queryParam.doctorIdList!=null">
                AND (
                a.deptdrt_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                a.chfdr_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                a.atddr_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                a.ipdr_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                a.resp_nurs_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                a.train_dr_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                a.intn_dr in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                a.codr_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                a.qltctrl_dr_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                a.qltctrl_nurs_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                )
            </if>
        )x
        cross join (
            select
                IFNULL(count(1),0) AS totalMedicalNum,
                IFNULL(count(case when grp_stas = '1' then 1 else null end),0) AS inGroupMedicalNum,
                IFNULL(count(case when grp_stas != '1' then 1 else null end),0) AS notGroupMedicalNum,
                IFNULL(count(distinct (case when grp_stas = '1' then drg_codg else null end)),0) AS drgGroupNum,
                IFNULL(round(SUM(CASE WHEN grp_stas = '1' THEN drg_wt ELSE NULL END)/
                  NULLIF (COUNT(CASE WHEN grp_stas = '1' THEN 1 ELSE NULL END),0),2),0) AS hosCmi,
                IFNULL(SUM(CASE WHEN grp_stas = '1' THEN drg_wt ELSE NULL END),0) AS totalDrgWeight,
                IFNULL(ROUND(AVG(act_ipt), 2),0) AS hosAvgDays,
                IFNULL(ROUND(AVG(CASE WHEN grp_stas = '1' THEN act_ipt ELSE NULL END), 2),0) AS hosInGroupAvgDays,
                IFNULL(ROUND(AVG(ipt_sumfee), 2),0) AS hosAvgCost,
                IFNULL(ROUND(AVG(CASE WHEN grp_stas = '1' THEN ipt_sumfee ELSE NULL END), 2),0) AS hosInGroupAvgCost,
                IFNULL(ROUND(SUM(CASE WHEN grp_stas = '1' THEN act_ipt ELSE NULL END / NULLIF(standard_ave_hosp_day,0))
                  /NULLIF(COUNT(CASE WHEN grp_stas = '1' THEN 1 ELSE NULL END),0), 2),0) AS hosTimeIndex,
                IFNULL(ROUND(SUM(CASE WHEN grp_stas = '1' THEN ipt_sumfee ELSE NULL END /
                  NULLIF(standard_avg_fee,0)) /NULLIF(COUNT(CASE WHEN grp_stas = '1' THEN 1 ELSE NULL END),0), 2),0) AS hosCostIndex,
                IFNULL(ROUND(AVG(a.drugfee), 2),0) AS hosAvgMedicalCost,
                IFNULL(ROUND(AVG(a.mcs_fee), 2),0) AS hosAvgMaterialCost
            from som_drg_grp_info a
            <if test="queryParam.enableSeAns != null and queryParam.enableSeAns != '' and queryParam.enableSeAns == 1">
                INNER JOIN som_hi_invy_bas_info b
                ON a.SETTLE_LIST_ID = b.ID
                INNER JOIN som_setl_cas_crsp p
                ON b.K00 = p.K00
            </if>
            where 1 = 1
            <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
                AND  a.HOSPITAL_ID = #{queryParam.hospitalId}
            </if>
            <if test="queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and
                                                        queryParam.cy_end_date!=null and queryParam.cy_end_date!=''">
                AND a.dscg_time BETWEEN #{queryParam.cy_start_date} and CONCAT(#{queryParam.cy_end_date},' 23:59:59')
            </if>
            <if test="queryParam.inStartTime != null and queryParam.inStartTime != '' and
                          queryParam.inEndTime != null and queryParam.inEndTime != ''">
                AND a.adm_time BETWEEN #{queryParam.inStartTime} and CONCAT(#{queryParam.inEndTime},' 23:59:59')
            </if>
            <if test="queryParam.seStartTime!=null and queryParam.seStartTime!='' and queryParam.seEndTime!=null and queryParam.seEndTime!=''">
                AND a.setl_end_time BETWEEN #{queryParam.seStartTime} and CONCAT(#{queryParam.seEndTime},' 23:59:59')
            </if>
        ) y
        GROUP BY x.dscg_caty_codg_inhosp,x.dscg_caty_name_inhosp
        ORDER BY totalDrgWeight desc
    </select>
</mapper>
