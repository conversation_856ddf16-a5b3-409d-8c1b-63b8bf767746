<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.som.dao.hospitalAnalysis.DipSafetyAndQualityDao">

    <select id="list" resultType="com.my.som.vo.hospitalAnalysis.RiskLevelMedicalDetailVo">
        select
            x.patientId AS patientId,
            x.name AS name,
            x.dipCode AS dipCode,
            x.dipName AS dipName,
            x.priOutHosDeptName AS priOutHosDeptName,
            y.riskLevel AS riskLevel,
            x.dscgWay AS dscgWay,
            x.standardInHosDays as standardInHosDays,
            x.inHosDays AS inHosDays,
            x.standardInHosCost as standardInHosCost,
            x.inHosTotalCost  AS inHosTotalCost,
            x.mainDiagnoseCodeAndName AS mainDiagnoseCodeAndName,
            x.mainOperativeCodeAndName AS mainOperativeCodeAndName
        from(
            select
                a.dip_codg,
                a.PATIENT_ID AS patientId,
                a.NAME AS name,
                a.dip_codg AS dipCode,
                a.dip_name AS dipName,
                a.dscg_caty_name_inhosp AS priOutHosDeptName,
                a.dscg_way AS dscgWay,
                a.standard_ave_hosp_day as standardInHosDays,
                a.act_ipt AS inHosDays,
                a.standard_avg_fee as standardInHosCost,
                a.ipt_sumfee  AS inHosTotalCost,
                CONCAT(a.main_diag_dise_codg,a.main_diag_dise_name) AS mainDiagnoseCodeAndName,
                CONCAT(a.main_oprn_oprt_codg,a.main_oprn_oprt_name) AS mainOperativeCodeAndName
            from som_dip_grp_info a
            <if test="queryParam.enableSeAns != null and queryParam.enableSeAns != '' and queryParam.enableSeAns == 1">
                INNER JOIN som_hi_invy_bas_info b
                ON a.SETTLE_LIST_ID = b.ID
                INNER JOIN som_setl_cas_crsp p
                ON b.K00 = p.K00
            </if>
            where a.dip_wt is not null
            <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
                AND a.HOSPITAL_ID = #{queryParam.hospitalId}
            </if>
            <if test="queryParam.b16c!=null and queryParam.b16c!=''">
                AND a.dscg_caty_codg_inhosp = #{queryParam.b16c}
            </if>
            <if test="queryParam.drCodg!=null and queryParam.drCodg!=''">
                AND a.ipdr_code = #{queryParam.drCodg}
            </if>
            <if test="queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and
                    queryParam.cy_end_date!=null and queryParam.cy_end_date!=''">
                AND a.dscg_time BETWEEN #{queryParam.cy_start_date} and CONCAT(#{queryParam.cy_end_date},' 23:59:59')
            </if>
            <if test="queryParam.seStartTime!=null and queryParam.seStartTime!='' and
                        queryParam.seEndTime!=null and queryParam.seEndTime!=''">
                AND a.setl_end_time BETWEEN #{queryParam.seStartTime} and CONCAT(#{queryParam.seEndTime},' 23:59:59')
            </if>
            <if test="queryParam.queryDrg!=null and queryParam.queryDrg!=''">
                AND a.dip_codg = #{queryParam.queryDrg}
            </if>
            <if test="queryParam.lyfsList!=null">
                AND a.dscg_way in
                <foreach collection="queryParam.lyfsList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="queryParam.doctorIdList!=null">
                AND (
                a.deptdrt_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                a.chfdr_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                a.atddr_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                a.ipdr_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                a.resp_nurs_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                a.train_dr_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                a.intn_dr in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                a.codr_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                a.qltctrl_dr_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                a.qltctrl_nurs_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                )
            </if>
        ) x
        LEFT JOIN (
            select
                a.dip_codg,
                <![CDATA[
                   (CASE
                    WHEN c.dead_ratio = 0  THEN 0
                    WHEN c.dead_ratio > 0 AND c.dead_ratio < b.ratio_low THEN 1
                    WHEN c.dead_ratio >= b.ratio_low AND c.dead_ratio < b.ratio_mid THEN 2
                    WHEN c.dead_ratio >= b.ratio_mid AND c.dead_ratio < b.ratio_hig THEN 3
                    WHEN c.dead_ratio >= b.ratio_hig THEN 4
                    ELSE NULL
                    END
                ) ]]> AS riskLevel
            from (
                select
                    dip_codg,
                    DIP_NAME
                from
                som_dip_grp_info
                <if test="queryParam.enableSeAns != null and queryParam.enableSeAns != '' and queryParam.enableSeAns == 1">
                    INNER JOIN som_hi_invy_bas_info b
                    ON som_dip_grp_info.SETTLE_LIST_ID = b.ID
                    INNER JOIN som_setl_cas_crsp p
                    ON b.K00 = p.K00
                </if>
                where 1=1
                <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
                    AND  som_dip_grp_info.HOSPITAL_ID = #{queryParam.hospitalId}
                </if>
                <if test="queryParam.b16c!=null and queryParam.b16c!=''">
                    AND dscg_caty_codg_inhosp = #{queryParam.b16c}
                </if>
                <if test="queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and
                        queryParam.cy_end_date!=null and queryParam.cy_end_date!=''">
                    AND dscg_time BETWEEN #{queryParam.cy_start_date} and CONCAT(#{queryParam.cy_end_date},' 23:59:59')
                </if>
                <if test="queryParam.seStartTime!=null and queryParam.seStartTime!='' and
                                queryParam.seEndTime!=null and queryParam.seEndTime!=''">
                    AND som_dip_grp_info.setl_end_time BETWEEN #{queryParam.seStartTime} and CONCAT(#{queryParam.seEndTime},' 23:59:59')
                </if>
                <if test="queryParam.doctorIdList!=null">
                    AND (
                    deptdrt_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    chfdr_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    atddr_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    ipdr_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    resp_nurs_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    train_dr_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    intn_dr in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    codr_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    qltctrl_dr_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    qltctrl_nurs_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    )
                </if>
            )a CROSS JOIN (
                SELECT
                    EXP(AVG(LN(z.dead_ratio)) - STDDEV(LN(z.dead_ratio))) AS ratio_low,
                    EXP(AVG(LN(z.dead_ratio))) AS ratio_mid,
                    EXP(AVG(LN(z.dead_ratio)) + STDDEV(LN(z.dead_ratio))) AS ratio_hig
                FROM (
                    SELECT
                        dip_codg,
                        (COUNT(CASE WHEN dscg_way = '5' THEN 1 ELSE NULL END) / COUNT(1)) AS dead_ratio
                    FROM som_dip_grp_info
                    <if test="queryParam.enableSeAns != null and queryParam.enableSeAns != '' and queryParam.enableSeAns == 1">
                        INNER JOIN som_hi_invy_bas_info b
                        ON som_dip_grp_info.SETTLE_LIST_ID = b.ID
                        INNER JOIN som_setl_cas_crsp p
                        ON b.K00 = p.K00
                    </if>
                    WHERE 1 = 1
                    <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
                        AND  som_dip_grp_info.HOSPITAL_ID = #{queryParam.hospitalId}
                    </if>
                    GROUP BY dip_codg
                ) z
                WHERE z.dead_ratio > 0
            ) b
            inner join  (
                SELECT
                    dip_codg,
                    (COUNT(CASE WHEN dscg_way = '5' THEN 1 ELSE NULL END) / COUNT(1)) AS dead_ratio
                FROM som_dip_grp_info
                <if test="queryParam.enableSeAns != null and queryParam.enableSeAns != '' and queryParam.enableSeAns == 1">
                    INNER JOIN som_hi_invy_bas_info b
                    ON som_dip_grp_info.SETTLE_LIST_ID = b.ID
                    INNER JOIN som_setl_cas_crsp p
                    ON b.K00 = p.K00
                </if>
                WHERE 1 = 1
                <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
                    AND  som_dip_grp_info.HOSPITAL_ID = #{queryParam.hospitalId}
                </if>
                GROUP BY dip_codg
            ) c on a.dip_codg=c.dip_codg
            GROUP BY a.dip_codg,c.dead_ratio,b.ratio_low,b.ratio_mid,b.ratio_hig
        ) y on x.dip_codg = y.dip_codg
        where 1=1
        <if test="queryParam.riskLevelList!=null">
            AND  y.riskLevel in
            <foreach collection="queryParam.riskLevelList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        order by y.riskLevel desc
    </select>

    <select id="getRiskLevelData" resultType="com.my.som.vo.hospitalAnalysis.RiskLevelDataVo">
        select
            x.*
        from(
            select
                a.dip_codg AS bke716,
                a.DIP_NAME AS bke717,
                COUNT(case when grp_stas='1' then 1 else null end) AS bke766,
                ROUND((case when c.dead_ratio > 0 then MAX(LN(c.dead_ratio)) else null end),2) as lnbke728,
                COUNT(CASE WHEN a.dscg_way = '5' THEN 1 ELSE NULL END) AS bke818,
                <![CDATA[MAX((CASE
                    WHEN c.dead_ratio = 0  THEN 0
                    WHEN c.dead_ratio > 0 AND c.dead_ratio < b.ratio_low THEN 1
                    WHEN c.dead_ratio >= b.ratio_low AND c.dead_ratio < b.ratio_mid THEN 2
                    WHEN c.dead_ratio >= b.ratio_mid AND c.dead_ratio < b.ratio_hig THEN 3
                    WHEN c.dead_ratio >= b.ratio_hig THEN 4
                    ELSE NULL
                    END
                )) ]]> AS bke725,
                ROUND((case when c.dead_ratio > 0 then MAX(LN(a.dip_wt)) else null end),2) as lnbke779
            from (
              select
                dip_codg,
                DIP_NAME,
                grp_stas,
                dscg_way,
                dip_wt
              from
              som_dip_grp_info
            <if test="queryParam.enableSeAns != null and queryParam.enableSeAns != '' and queryParam.enableSeAns == 1">
                INNER JOIN som_hi_invy_bas_info b
                ON som_dip_grp_info.SETTLE_LIST_ID = b.ID
                INNER JOIN som_setl_cas_crsp p
                ON b.K00 = p.K00
            </if>
               where 1=1
              <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
                  AND  som_dip_grp_info.HOSPITAL_ID = #{queryParam.hospitalId}
              </if>
              <if test="queryParam.b16c!=null and queryParam.b16c!=''">
                  AND dscg_caty_codg_inhosp = #{queryParam.b16c}
              </if>
                <if test="queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and
                                queryParam.cy_end_date!=null and queryParam.cy_end_date!=''">
                    AND dscg_time BETWEEN #{queryParam.cy_start_date} and CONCAT(#{queryParam.cy_end_date},' 23:59:59')
                </if>
                <if test="queryParam.seStartTime!=null and queryParam.seStartTime!='' and
                                        queryParam.seEndTime!=null and queryParam.seEndTime!=''">
                    AND som_dip_grp_info.setl_end_time BETWEEN #{queryParam.seStartTime} and CONCAT(#{queryParam.seEndTime},' 23:59:59')
                </if>
                <if test="queryParam.doctorIdList!=null">
                    AND (
                    deptdrt_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    chfdr_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    atddr_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    ipdr_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    resp_nurs_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    train_dr_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    intn_dr in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    codr_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    qltctrl_dr_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    qltctrl_nurs_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    )
                </if>
            )a CROSS JOIN (
                SELECT
                    EXP(AVG(LN(z.dead_ratio)) - STDDEV(LN(z.dead_ratio))) AS ratio_low,
                    EXP(AVG(LN(z.dead_ratio))) AS ratio_mid,
                    EXP(AVG(LN(z.dead_ratio)) + STDDEV(LN(z.dead_ratio))) AS ratio_hig
                FROM (
                    SELECT
                        dip_codg,
                        (COUNT(CASE WHEN dscg_way = '5' THEN 1 ELSE NULL END) / COUNT(1)) AS dead_ratio
                    FROM som_dip_grp_info
                    <if test="queryParam.enableSeAns != null and queryParam.enableSeAns != '' and queryParam.enableSeAns == 1">
                        INNER JOIN som_hi_invy_bas_info b
                        ON som_dip_grp_info.SETTLE_LIST_ID = b.ID
                        INNER JOIN som_setl_cas_crsp p
                        ON b.K00 = p.K00
                    </if>
                    WHERE 1 = 1
                    <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
                        AND  som_dip_grp_info.HOSPITAL_ID = #{queryParam.hospitalId}
                    </if>
                    GROUP BY dip_codg
                ) z
                WHERE z.dead_ratio > 0
            ) b
            inner join  (
                SELECT
                    dip_codg,
                    (COUNT(CASE WHEN dscg_way = '5' THEN 1 ELSE NULL END) / COUNT(1)) AS dead_ratio
                FROM som_dip_grp_info
                <if test="queryParam.enableSeAns != null and queryParam.enableSeAns != '' and queryParam.enableSeAns == 1">
                    INNER JOIN som_hi_invy_bas_info b
                    ON som_dip_grp_info.SETTLE_LIST_ID = b.ID
                    INNER JOIN som_setl_cas_crsp p
                    ON b.K00 = p.K00
                </if>
                WHERE 1 = 1
                <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
                    AND  som_dip_grp_info.HOSPITAL_ID = #{queryParam.hospitalId}
                </if>
                GROUP BY dip_codg
            ) c on a.dip_codg=c.dip_codg
            GROUP BY a.dip_codg, a.DIP_NAME
        )x
        where x.lnbke728 is not null and x.lnbke779 is not null
    </select>

    <select id="getRiskLevelLine" resultType="com.my.som.vo.hospitalAnalysis.RiskGradeLineVo">
        select
            round(AVG(LN(a.dead_ratio))-STDDEV(LN(a.dead_ratio)),2) as risk_low_line,
            round(AVG(LN(a.dead_ratio)),2) as risk_mid_line,
            round(AVG(LN(a.dead_ratio))+STDDEV(LN(a.dead_ratio)),2) as risk_hig_line
        from (
            SELECT
                dip_codg,
                (COUNT(CASE WHEN dscg_way = '5' THEN 1 ELSE NULL END) / COUNT(1)) AS dead_ratio
            FROM som_dip_grp_info
            WHERE 1 = 1
            <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
                AND  HOSPITAL_ID = #{queryParam.hospitalId}
            </if>
            GROUP BY dip_codg
        ) a
        where a.dead_ratio != 0
    </select>
</mapper>
