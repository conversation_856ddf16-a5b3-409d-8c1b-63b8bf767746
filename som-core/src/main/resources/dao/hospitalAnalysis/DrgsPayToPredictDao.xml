<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.my.som.dao.hospitalAnalysis.DrgsPayToPredictDao">

    <!-- 查询数据 -->
    <select id="getList" resultType="com.my.som.vo.hospitalAnalysis.DrgsPayToPredictVo">
        SELECT a.*
        FROM
        (
        SELECT a.*,
        <choose>
            <when test="feeStas == 0">
                ROUND(IFNULL(a.preCost, 0) - IFNULL(a.sumfee, 0), 2) AS profitAndLoss,
            </when>
            <when test="feeStas == 1">
                ROUND(IFNULL(a.preCost, 0) - IFNULL(a.fbTotalCost, 0), 2) AS profitAndLoss,
            </when>
        </choose>
        b.name AS dept,     IFNULL(y.digsNum,0) AS digsNum,
        y.otherDiagnoseCodeAndName AS otherDiagnoseCodeAndName,
        IFNULL(z.oprNum,0) AS oprNum,
        z.oneLevelOprCodeAndName AS oneLevelOprCodeAndName,

        z.twoLevelOprCodeAndName AS twoLevelOprCodeAndName,
        z.twoLevelStanOprCodeAndName AS twoLevelStanOprCodeAndName,
        z.threeLevelOprCodeAndName AS threeLevelOprCodeAndName,
        z.threeLevelStanOprCodeAndName AS threeLevelStanOprCodeAndName,
        z.fourLevelOprCodeAndName AS fourLevelOprCodeAndName,
        z.fourLevelStanOprCodeAndName AS fourLevelStanOprCodeAndName,
        t1.labl_name as dscgWay
        FROM
        (
        SELECT a.patientId,
        a.standardInHosDays,
        a.standardInHosCost,
        a.inHosDays,
        a.HOSPITAL_ID,
        a.attendingPhysician,
        a.settlementTime,
        a.conditionDiagnosis,
        a.id,
        a.name,
        a.drgCodg,
        a.drgName,
        a.recodeType,
        a.deptCode,
        a.sumfee,
        a.stsbFee,
        a.insuredType,
        a.admDiag,
        round(a.calculateScore, 2) AS calculateScore,
        round(a.addScore, 2)       AS addScore,
        round(a.totlSco, 2)        AS totlSco,
        a.refer_sco,
        a.adjm_cof,
        a.uplmtMag,
        a.hospCof,
        a.dscg_way,
        a.mainDiagnoseCodeAndName,
        a.mainOperativeCodeAndName,
        CASE WHEN a.ydjy= 1 THEN '异地' ELSE '本地' END AS ydjy,
        a.isRemote,
        a.listSerialNumFlag,
        a.medicalCost,a.medicalCostRate,a.materialCost,a.materialCostRate,a.Amount370100,
        a.psnSelfpay,a.psnOwnpay,a.acctPay,a.psnCashpay
        <choose>
            <when test="feeStas == 0">
                ,
                ROUND(a.forecast_fee, 2)  AS preCost,
                a.price AS runPrice
            </when>
            <when test="feeStas == 1">
                ,
                ROUND(a.ycCost,2) AS preCost, a.fbTotalCost
            </when>
        </choose>
        FROM
        (
        SELECT
        q.B20+0 AS inHosDays,
        ROUND(IFNULL(AES_DECRYPT(UNHEX(d.standard_ave_hosp_day),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}),0), 2) as standardInHosDays,
        <choose>
        <when test="feeMulAdjmConf != null and feeMulAdjmConf != '' and feeMulAdjmConf == 'true' ">
            CASE
            WHEN q.a54 in ('1','01','310') THEN  ROUND(IFNULL( d.adjm_cof *  ROUND(IFNULL(AES_DECRYPT(UNHEX(d.standard_avg_fee),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}),0), 4),0), 4)
            ELSE ROUND(IFNULL( d.adjm_resid_cof  *  ROUND(IFNULL(AES_DECRYPT(UNHEX(d.standard_avg_fee),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}),0), 4),0), 4)
            END as standardInHosCost,
        </when>
        <otherwise>
            ROUND(IFNULL(AES_DECRYPT(UNHEX(d.standard_avg_fee),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}),0), 2) as standardInHosCost,
        </otherwise>
        </choose>

        IFNULL(ROUND(IFNULL(a.drugfee,0),2),0) AS medicalCost,  /*药物费用*/
        CONCAT(IFNULL(ROUND(IFNULL(a.drugfee,0)/NULLIF(b.sumfee,0)*100,2),0), '%') AS medicalCostRate, /*药物占比*/
        IFNULL(ROUND(IFNULL(a.mcs_fee,0),2),0) AS materialCost,  /*耗材费*/
        CONCAT(IFNULL(ROUND(IFNULL(a.mcs_fee,0)/NULLIF( b.sumfee,0)*100,2),0),  '%') AS materialCostRate , /*耗材费*/
        IFNULL(fp.370100_amount,0) AS Amount370100,
        IFNULL(psn_selfpay,0) AS psnSelfpay,
        IFNULL(psn_ownpay,0) AS psnOwnpay,
        IFNULL(acct_pay,0) AS acctPay,
        IFNULL(psn_cashpay,0) AS psnCashpay,
        a.PATIENT_ID            AS patientId,
        a.dscg_way ,
        a.HOSPITAL_ID,
        a.SETTLE_LIST_ID        as id,
        a.`NAME`                AS name,
        a.medcas_type           AS recodeType,
        a.dscg_caty_codg_inhosp AS deptCode,
        a.ipt_sumfee            AS sumfee,
        b.dise_type             AS stsbFee,
        q.ydjy,
        CASE
        WHEN q.a56  IS NULL or q.a56 =''  THEN '未传值'
        WHEN SUBSTRING(q.a56, 1, 4) = SUBSTRING(#{provLevelInsuplcAdmdvs}, 1, 4)THEN '省本级'
        WHEN SUBSTRING(q.a56, 1, 4) = SUBSTRING(#{insuplcAdmdvs}, 1, 4) THEN '市医保'
        WHEN SUBSTRING(q.a56, 1, 2) = SUBSTRING(#{insuplcAdmdvs}, 1, 2) AND SUBSTRING(q.a56, 1, 4) != SUBSTRING(#{insuplcAdmdvs}, 1, 4) THEN CONCAT('省内异地-', t2.cityName)
        ELSE '省外异地'
        END AS isRemote,
        <choose>
            <when test="feeStas == 0">
                a.drg_codg            AS drgCodg,
                a.DRG_NAME            AS drgName,
                CASE
                WHEN b.insu_type = 1 or b.insu_type = 310  THEN 1
                WHEN b.insu_type = 2 or b.insu_type = 390 THEN 2
                ELSE 9 END        AS insuredType,
                b.setl_sco            AS calculateScore,
                IFNULL(b.incr_sco, 0) AS addScore,
                b.totl_sco            AS totlSco,
                b.forecast_fee,
                ROUND(b.refer_sco, 2) AS refer_sco,
            </when>
            <when test="feeStas == 1">
                e.drg_codg     AS drgCodg,
                e.DRG_NAME     AS drgName,
                e.INSURED_TYPE AS insuredType,
                e.incr_sco     AS addScore,
                e.setl_sco     AS calculateScore,
                e.setl_sco     AS totlSco,
                e.bas_sco      AS refer_sco,
                e.ipt_sumfee   AS fbTotalCost,
                e.ycCost,
            </when>
        </choose>
        b.adjm_cof  AS adjm_cof,
        b.uplmt_mag AS uplmtMag,
        b.hosp_cof  AS hospCof,
        a.adm_time,
        a.dscg_time,
        a.setl_end_time,
        SUBSTR(a.dscg_time, 1, 7) as ym_time,
        b.price,b.ym,
        q.b52n AS attendingPhysician,
        q.d36  AS settlementTime,
        w.conditionDiagnosis,
        q.adm_diag AS admDiag,
        q.d35 as listSerialNumFlag,
        CONCAT(a.main_diag_dise_codg,a.main_diag_dise_name) AS mainDiagnoseCodeAndName,
        CONCAT(a.main_oprn_oprt_codg,a.main_oprn_oprt_name) AS mainOperativeCodeAndName
        FROM som_drg_grp_info a
        LEFT JOIN som_hi_invy_bas_info q
        ON q.ID = a.SETTLE_LIST_ID
        LEFT JOIN som_drg_standard d
        ON a.drg_codg = d.drg_codg
        and q.insuplc_admdvs = d.insuplc_admdvs
        AND SUBSTR(a.setl_end_time,1,4) = d.STANDARD_YEAR
        AND a.HOSPITAL_ID = d.HOSPITAL_ID
        LEFT JOIN (
        SELECT SUBSTR(data_val, 1, 4) AS cbd, labl_name  as  cityName
        FROM `som_sys_code`
        WHERE code_type = 'XZQHDM'
        AND data_val LIKE '%00'
        AND SUBSTRING(data_val, 1, 2) = SUBSTRING(#{insuplcAdmdvs}, 1, 2)
        union
        SELECT
        CONCAT(SUBSTR(data_val, 1, 2), '99') AS cbd,
        labl_name AS cityName
        FROM
        `som_sys_code`
        WHERE
        code_type = 'XZQHDM'
        AND data_val LIKE '%0000'
        AND SUBSTRING(data_val, 1, 2) = SUBSTRING(#{insuplcAdmdvs}, 1, 2)

        ) t2 ON t2.cbd = SUBSTRING(q.a56, 1, 4)
        LEFT JOIN (
        SELECT
        SETTLE_LIST_ID,
        GROUP_CONCAT(dscg_diag_name ORDER BY seq ASC) AS conditionDiagnosis
        FROM
        som_diag
        GROUP BY
        SETTLE_LIST_ID
        ) w
        ON w.SETTLE_LIST_ID = a.SETTLE_LIST_ID
        LEFT JOIN (
        SELECT
        hi_setl_invy_id,
        SUM(CASE WHEN fund_pay_type  = '370100' THEN fund_payamt  ELSE 0 END) AS `370100_amount`
        FROM som_fund_pay
        GROUP BY hi_setl_invy_id
        ) fp ON a.SETTLE_LIST_ID = fp.hi_setl_invy_id
        <if test='enableSeAns != null and enableSeAns != "" and enableSeAns == "1"'>
            --                                        LEFT JOIN som_hi_invy_bas_info q
            --                                        ON q.ID = a.SETTLE_LIST_ID
            INNER JOIN som_setl_cas_crsp p
            ON q.k00 = p.k00
        </if>
        LEFT JOIN som_drg_sco b
        ON a.SETTLE_LIST_ID = b.SETTLE_LIST_ID
        <choose>
            <when test="inStartTime != null and inStartTime != ''">
                and  SUBSTR(a.dscg_time, 1, 7) = b.ym
            </when>
            <when test="cy_start_date != null and cy_start_date != ''">
                and   SUBSTR(a.dscg_time,1,7) = b.ym
            </when>
            <when test="seStartTime != null and seStartTime != ''">
                and   SUBSTR(a.setl_end_time,1,7) = b.ym
            </when>
        </choose>
        <if test="feeStas == 1">
            INNER JOIN
            (
            SELECT a.drg_codg,
            a.DRG_NAME,
            a.rid_idt_codg,
            a.medcas_codg,
            a.adm_time,
            a.dscg_time,
            a.setl_time,
            a.is_in_group,
            b.bas_sco,
            b.incr_sco,
            b.sumfee   AS ipt_sumfee,
            b.INSURED_TYPE,
            b.MED_TYPE AS dise_type,
            b.setl_pt_val,
            b.dfr_fee  AS ycCost,
            b.MED_TYPE,
            b.setl_sco
            FROM som_drg_grp_fbck a
            LEFT JOIN som_drg_pt_val_pay b
            ON a.rid_idt_codg = b.rid_idt_codg
            <where>
                <if test="inStartTime != null and inStartTime != '' and inEndTime != null and inEndTime != ''">
                    <![CDATA[
                    WHERE a.adm_time >= #{inStartTime,jdbcType=VARCHAR}
                      AND a.adm_time <= SUBSTR(#{inEndTime,jdbcType=VARCHAR}
                        , 1
                        , 10)
                    ]]>
                </if>
                <if test="cy_start_date != null and cy_start_date != '' and cy_end_date != null and cy_end_date != ''">
                    <![CDATA[
                    WHERE a.dscg_time >= #{cy_start_date,jdbcType=VARCHAR}
                      AND a.dscg_time <= SUBSTR(#{cy_end_date,jdbcType=VARCHAR}
                        , 1
                        , 10)
                    ]]>
                </if>
                <if test="seStartTime != null and seStartTime != '' and seEndTime != null and seEndTime != ''">
                    <![CDATA[
                    WHERE a.setl_time >= #{seStartTime,jdbcType=VARCHAR}
                      AND a.setl_time <= SUBSTR(#{seEndTime,jdbcType=VARCHAR}
                        , 1
                        , 10)
                    ]]>
                </if>
            </where>
            <!--
             <![CDATA[
             WHERE a.dscg_time >= #{cy_start_date,jdbcType=VARCHAR} AND a.dscg_time <= SUBSTR(#{cy_end_date,jdbcType=VARCHAR},1,10)
            ]]>
             -->
            ) e
            ON a.PATIENT_ID = e.medcas_codg
            AND SUBSTR(a.adm_time, 1, 10) = e.adm_time
            AND SUBSTR(a.dscg_time, 1, 10) = e.dscg_time
            AND SUBSTR(a.setl_end_time, 1, 10) = e.setl_time
        </if>
        WHERE 1 = 1
        <if test="isRemote != null and isRemote != ''">
            <if test="isRemote == 0">
                AND SUBSTRING(q.a56,1,4)
                = SUBSTRING(#{insuplcAdmdvs},1,4)
                <!--                AND a.YDJY = '否'-->
            </if>
            <if test="isRemote == 1">
                <!--省内异地-->
                AND  SUBSTRING(q.a56,1,4)
                != SUBSTRING(#{insuplcAdmdvs},1,4)
                AND  SUBSTRING(q.a56,1,2)
                = SUBSTRING(#{insuplcAdmdvs},1,2)
                AND  SUBSTRING(q.a56,1,4)
                != #{provLevelInsuplcAdmdvs}
                <!--                AND a.YDJY = '是'-->
            </if>
            <if test="isRemote == 2">
                <!--省外异地-->
                AND  SUBSTRING(q.a56,1,2)
                != SUBSTRING(#{insuplcAdmdvs},1,2)
            </if>
            <if test="isRemote == 3">
                <!--省本级-->
                AND  SUBSTRING(q.a56,1,4)
                = #{provLevelInsuplcAdmdvs}
                <!--                AND a.YDJY = '是'-->
            </if>
            <if test="isRemote == 4">
                <!--未传值-->
                AND (q.a56  IS NULL or q.a56 ='')
            </if>

        </if>
        <if test="inStartTime != null and inStartTime != '' and
        inEndTime != null and inEndTime != ''">
            AND a.adm_time BETWEEN #{inStartTime} AND CONCAT(#{inEndTime}, ' 23:59:59')
        </if>
        <if test="cy_start_date != null and cy_start_date != ''">
            AND a.dscg_time BETWEEN #{cy_start_date,jdbcType=VARCHAR}
            AND CONCAT(#{cy_end_date,jdbcType=VARCHAR}, ' 23:59:59')
        </if>
        <if test="seStartTime != null and seStartTime != '' and
        seEndTime != null and seEndTime != ''">
            AND a.setl_end_time BETWEEN #{seStartTime,jdbcType=VARCHAR} AND CONCAT(#{seEndTime,jdbcType=VARCHAR}, ' 23:59:59')
        </if>

        <if test="icdCodg != null and icdCodg != ''">
            AND a.main_diag_dise_codg = #{icdCodg,jdbcType=VARCHAR}
        </if>
        <if test="drgCodg != null and drgCodg != ''">
            AND a.drg_codg = #{drgCodg,jdbcType=VARCHAR}
        </if>
        <if test="deptCode != null and deptCode != ''">
            AND a.dscg_caty_codg_inhosp = #{deptCode,jdbcType=VARCHAR}
        </if>
        <include refid="com.my.som.mapper.newBusiness.common.NewBusinessCommonAnalysisMapper.queryBusCommon">
        </include>
        <if test="bah != null and bah != ''">
            AND INSTR(a.PATIENT_ID, #{bah,jdbcType=VARCHAR}) > 0
        </if>
        <if test="categories != null and categories != ''">
            <if test="categories == 9">
                AND (b.insu_type != '1' AND b.insu_type != '2')
            </if>
            <if test="categories != 9">
                AND b.insu_type = #{categories}
            </if>
        </if>
        <if test="costSection != null and costSection != ''">
            AND b.dise_type = #{costSection}
        </if>
        <if test="drgGroup != null and drgGroup != ''">
            and a.drg_codg = #{drgGroup,jdbcType=VARCHAR}
        </if>
        <if test="recordType != null and recordType != ''">
            and a.medcas_type = #{recordType}
        </if>
        <if test="dept != null and dept != ''">
            and a.dscg_caty_codg_inhosp = #{dept}
        </if>
        <if test="diseType != null and diseType != ''">
            and b.dise_type = #{diseType}
        </if>
        ) a


        ) a
        LEFT JOIN som_dept b
        ON a.deptCode = b.`CODE`
        AND a.HOSPITAL_ID = b.HOSPITAL_ID
        LEFT JOIN (
        SELECT
        a.SETTLE_LIST_ID AS hiSetlInvyId,
        count( 1 ) AS oprNum,
        group_concat(
        IF
        ( m.oprn_oprt_lv = '1', CONCAT( m.C35C, m.C36N ), NULL )) AS oneLevelOprCodeAndName,
        group_concat(
        IF
        ( n.oprn_lv = '1', CONCAT( m.C35C, m.C36N ), NULL )) AS oneLevelStanOprCodeAndName,
        group_concat(
        IF
        ( m.oprn_oprt_lv = '2', CONCAT( m.C35C, m.C36N ), NULL )) AS twoLevelOprCodeAndName,
        group_concat(
        IF
        ( n.oprn_lv = '2', CONCAT( m.C35C, m.C36N ), NULL )) AS twoLevelStanOprCodeAndName,
        group_concat(
        IF
        ( m.oprn_oprt_lv = '3', CONCAT( m.C35C, m.C36N ), NULL )) AS threeLevelOprCodeAndName,
        group_concat(
        IF
        ( n.oprn_lv = '3', CONCAT( m.C35C, m.C36N ), NULL )) AS threeLevelStanOprCodeAndName,
        group_concat(
        IF
        ( m.oprn_oprt_lv = '4', CONCAT( m.C35C, m.C36N ), NULL )) AS fourLevelOprCodeAndName,
        group_concat(
        IF
        ( n.oprn_lv = '4', CONCAT( m.C35C, m.C36N ), NULL )) AS fourLevelStanOprCodeAndName
        FROM
        som_drg_grp_info a
        INNER JOIN som_oprn_oprt_info m ON a.SETTLE_LIST_ID = m.SETTLE_LIST_ID
        LEFT JOIN som_oprn_lv n ON m.c35c = n.oprn_oprt_codg
        WHERE
        m.C35C IS NOT NULL
        AND m.C35C != '-'
        AND m.C35C != '--'
        <if test="inStartTime != null and inStartTime != '' and
        inEndTime != null and inEndTime != ''">
            AND a.adm_time BETWEEN #{inStartTime} AND CONCAT(#{inEndTime}, ' 23:59:59')
        </if>
        <if test="cy_start_date != null and cy_start_date != ''">
            AND a.dscg_time BETWEEN #{cy_start_date,jdbcType=VARCHAR}
            AND CONCAT(#{cy_end_date,jdbcType=VARCHAR}, ' 23:59:59')
        </if>
        <if test="seStartTime != null and seStartTime != '' and
        seEndTime != null and seEndTime != ''">
            AND a.setl_end_time BETWEEN #{seStartTime,jdbcType=VARCHAR} AND CONCAT(#{seEndTime,jdbcType=VARCHAR}, ' 23:59:59')
        </if>
        GROUP BY
        a.SETTLE_LIST_ID
        ) z ON z.hiSetlInvyId = a.id
        left join (SELECT data_val, labl_name FROM `som_dic_code` where code_type = 'DSCG_WAY') t1 on t1.data_val = a.dscg_way
        LEFT JOIN (
        SELECT
        a.SETTLE_LIST_ID AS hiSetlInvyId,
        count( 1 ) AS digsNum,
        group_concat(
        IF
        ( m.seq != '0', CONCAT( m.dscg_diag_codg, m.dscg_diag_name ), NULL )) AS otherDiagnoseCodeAndName
        FROM
        som_drg_grp_info a
        INNER JOIN som_diag m ON a.SETTLE_LIST_ID = m.SETTLE_LIST_ID
        WHERE
        m.TYPE = '1'
        AND m.dscg_diag_codg != '-'
        AND m.dscg_diag_codg != '--'
        <if test="inStartTime != null and inStartTime != '' and
        inEndTime != null and inEndTime != ''">
            AND a.adm_time BETWEEN #{inStartTime} AND CONCAT(#{inEndTime}, ' 23:59:59')
        </if>
        <if test="cy_start_date != null and cy_start_date != ''">
            AND a.dscg_time BETWEEN #{cy_start_date,jdbcType=VARCHAR}
            AND CONCAT(#{cy_end_date,jdbcType=VARCHAR}, ' 23:59:59')
        </if>
        <if test="seStartTime != null and seStartTime != '' and
        seEndTime != null and seEndTime != ''">
            AND a.setl_end_time BETWEEN #{seStartTime,jdbcType=VARCHAR} AND CONCAT(#{seEndTime,jdbcType=VARCHAR}, ' 23:59:59')
        </if>
        GROUP BY
        a.SETTLE_LIST_ID
        ) y ON a.id = y.hiSetlInvyId
        ) a
        <where>
            <if test="isLoss != null and isLoss != ''">
                <if test="isLoss == 1">
                    <![CDATA[
                    AND a.profitAndLoss
                      < 0
                    ]]>
                </if>
                <if test="isLoss == 0">
                    <![CDATA[
                    AND a.profitAndLoss >= 0
                    ]]>
                </if>
            </if>

        </where>
    </select>

    <!--查询城职城乡结算信息 -->
    <select id="getListPoint" resultType="com.my.som.vo.hospitalAnalysis.DrgsPayToPredictVo">
        SELECT a.*,
        b.*
        FROM
        (
        SELECT COUNT(1) AS totalCount,
        <choose>
            <when test="feeStas == 0">
                a.insu_type AS insuType,
                <![CDATA[
                                    IFNULL(SUM(CASE WHEN a.dise_type = 2 THEN IFNULL(a.totl_sco,0) ELSE NULL END),0) AS lowPoint,
                                    IFNULL(SUM(CASE WHEN a.dise_type = 1 THEN IFNULL(a.totl_sco,0) ELSE NULL END),0) AS upPoint,
                                    IFNULL(SUM(CASE WHEN a.dise_type = 3 THEN IFNULL(a.totl_sco,0) ELSE NULL END),0) AS NCPoint,
                                    IFNULL(SUM(CASE WHEN a.dise_type = 4 THEN IFNULL(a.totl_sco,0) ELSE NULL END),0) AS WBPoint,
                                    IFNULL(SUM(CASE WHEN a.dise_type = 7 THEN IFNULL(a.totl_sco,0) ELSE NULL END),0) AS WBLPoint,
                                    IFNULL(SUM(CASE WHEN a.dise_type = 2 THEN IFNULL(a.ipt_sumfee,0) ELSE NULL END),0) AS lowCost,
                                    IFNULL(SUM(CASE WHEN a.dise_type = 1 THEN IFNULL(a.ipt_sumfee,0) ELSE NULL END),0) AS upCost,
                                    IFNULL(SUM(CASE WHEN a.dise_type = 3 THEN IFNULL(a.ipt_sumfee,0) ELSE NULL END),0) AS NCCost,
                                    IFNULL(SUM(CASE WHEN a.dise_type = 4 THEN IFNULL(a.ipt_sumfee,0) ELSE NULL END),0) AS WBCost,
                                    IFNULL(SUM(CASE WHEN a.dise_type = 7 THEN IFNULL(a.ipt_sumfee,0) ELSE NULL END),0) AS WBLCost
                                ]]>
            </when>
            <when test="feeStas == 1">
                a.insu_type AS insuType,
                IFNULL(SUM(CASE WHEN a.dise_type = 1 THEN a.setl_sco ELSE NULL END),0) AS upPoint,
                IFNULL(SUM(CASE WHEN a.dise_type = 2 THEN a.setl_sco ELSE NULL END),0) AS lowPoint,
                IFNULL(SUM(CASE WHEN a.dise_type = 3 THEN a.setl_sco ELSE NULL END),0) AS NCPoint,
                IFNULL(SUM(CASE WHEN a.dise_type = 4 THEN a.setl_sco ELSE NULL END),0) AS WBPoint,
                IFNULL(SUM(CASE WHEN a.dise_type = 7 THEN a.setl_sco ELSE NULL END),0) AS WBLPoint,
                IFNULL(SUM(CASE WHEN a.dise_type = 1 THEN a.ipt_sumfee ELSE NULL END),0) AS upCost,
                IFNULL(SUM(CASE WHEN a.dise_type = 2 THEN a.ipt_sumfee ELSE NULL END),0) AS lowCost,
                IFNULL(SUM(CASE WHEN a.dise_type = 3 THEN a.ipt_sumfee ELSE NULL END),0) AS NCCost,
                IFNULL(SUM(CASE WHEN a.dise_type = 4 THEN a.ipt_sumfee ELSE NULL END),0) AS WBCost,
                IFNULL(SUM(CASE WHEN a.dise_type = 7 THEN a.ipt_sumfee ELSE NULL END),0) AS WBLCost
            </when>
        </choose>
        FROM
        (
        SELECT
        c.insu_type  as insu_type,
        a.ipt_sumfee,
        c.totl_sco,
        c.dise_type
        <if test="feeStas == 1">
            ,e.setl_sco
        </if>
        FROM som_drg_grp_info a
        <if test='enableSeAns != null and enableSeAns != "" and enableSeAns == "1"'>
            LEFT JOIN som_hi_invy_bas_info q
            ON q.ID = a.SETTLE_LIST_ID
            INNER JOIN som_setl_cas_crsp p
            ON q.k00 = p.k00
        </if>
        LEFT JOIN som_drg_sco c
        ON a.SETTLE_LIST_ID = c.SETTLE_LIST_ID
        LEFT JOIN som_drg_standard d
        ON a.drg_codg = d.drg_codg
        AND SUBSTR(a.setl_end_time,1,4) = d.STANDARD_YEAR
        AND a.HOSPITAL_ID = d.HOSPITAL_ID
        and c.insuplc_admdvs =d.insuplc_admdvs
        LEFT JOIN som_hi_invy_bas_info q
        ON q.ID = a.SETTLE_LIST_ID
        <if test="feeStas == 1">
            INNER JOIN
            (
            SELECT a.drg_codg,
            a.DRG_NAME,
            a.rid_idt_codg,
            a.medcas_codg,
            a.adm_time,
            a.dscg_time,
            a.setl_time,
            a.is_in_group,
            b.bas_sco,
            b.incr_sco,
            b.sumfee AS ipt_sumfee,
            b.INSURED_TYPE,
            b.MED_TYPE AS dise_type,
            b.setl_pt_val,
            b.dfr_fee AS ycCost,
            b.MED_TYPE,
            b.setl_sco
            FROM som_drg_grp_fbck a
            LEFT JOIN som_drg_pt_val_pay b
            ON a.rid_idt_codg = b.rid_idt_codg
            <!--
              <![CDATA[
                WHERE a.dscg_time >= SUBSTR(#{cy_start_date,jdbcType=VARCHAR},1,10) AND a.dscg_time <= SUBSTR(#{cy_end_date,jdbcType=VARCHAR},1,10)
              ]]>
             -->
            <if test="inStartTime != null and inStartTime != '' and inEndTime != null and inEndTime != ''">
                AND a.adm_time BETWEEN #{inStartTime} AND CONCAT(#{inEndTime},' 23:59:59')
            </if>
            <if test="cy_start_date != null and cy_start_date !=''">
                <![CDATA[
                                    WHERE a.dscg_time >= SUBSTR(#{cy_start_date,jdbcType=VARCHAR},1,10) AND a.dscg_time <= SUBSTR(#{cy_end_date,jdbcType=VARCHAR},1,10)
                                  ]]>
            </if>
            <if test="seStartTime!=null and seStartTime!='' and seEndTime!=null and seEndTime!=''">
                AND a.setl_time BETWEEN #{seStartTime,jdbcType=VARCHAR} and CONCAT(#{seEndTime,jdbcType=VARCHAR},' 23:59:59')
            </if>
            ) e
            ON a.PATIENT_ID = e.medcas_codg
            AND SUBSTR(a.adm_time,1,10) = e.adm_time
            AND SUBSTR(a.dscg_time,1,10) = e.dscg_time
            AND SUBSTR(a.setl_end_time,1,10) = e.setl_time
        </if>
        WHERE 1 = 1
        <include refid="Convert"></include>
        <include refid="com.my.som.mapper.newBusiness.common.NewBusinessCommonAnalysisMapper.queryBusCommon"></include>
        <if test="isRemote != null and isRemote != ''">
            <if test="isRemote == 0">
                AND SUBSTRING(q.a56,1,4)
                = SUBSTRING(#{insuplcAdmdvs},1,4)
                <!--                AND a.YDJY = '否'-->
            </if>
            <if test="isRemote == 1">
                <!--省内异地-->
                AND  SUBSTRING(q.a56,1,4)
                != SUBSTRING(#{insuplcAdmdvs},1,4)
                AND  SUBSTRING(q.a56,1,2)
                = SUBSTRING(#{insuplcAdmdvs},1,2)
                AND  SUBSTRING(q.a56,1,4)
                != #{provLevelInsuplcAdmdvs}
                <!--                AND a.YDJY = '是'-->
            </if>
            <if test="isRemote == 2">
                <!--省外异地-->
                AND  SUBSTRING(q.a56,1,2)
                != SUBSTRING(#{insuplcAdmdvs},1,2)
            </if>
            <if test="isRemote == 3">
                <!--省本级-->
                AND  SUBSTRING(q.a56,1,4)
                = #{provLevelInsuplcAdmdvs}
                <!--                AND a.YDJY = '是'-->
            </if>
            <if test="isRemote == 4">
                <!--未传值-->
                AND (q.a56  IS NULL or q.a56 ='')
            </if>

        </if>
        <if test="inStartTime != null and inStartTime != '' and inEndTime != null and inEndTime != ''">
            AND a.adm_time BETWEEN #{inStartTime} AND CONCAT(#{inEndTime},' 23:59:59')
        </if>
        <if test="cy_start_date != null and cy_start_date !=''">
            <![CDATA[
                                AND a.dscg_time BETWEEN #{cy_start_date,jdbcType=VARCHAR} AND #{cy_end_date,jdbcType=VARCHAR}
                            ]]>
        </if>
        <if test="seStartTime!=null and seStartTime!='' and seEndTime!=null and seEndTime!=''">
            AND a.setl_end_time BETWEEN #{seStartTime,jdbcType=VARCHAR} and CONCAT(#{seEndTime,jdbcType=VARCHAR},' 23:59:59')
        </if>

        ) a
        GROUP BY
        <choose>
            <when test="feeStas == 0">
                a.insu_type
            </when>
            <when test="feeStas == 1">
                e.INSURED_TYPE
            </when>
        </choose>
        ) a
        CROSS JOIN
        (
        SELECT MAX(CASE WHEN `key` = 'CX_PRICE' THEN `VALUE` ELSE NULL END) AS cxPrice,
        MAX(case when `key` = 'CZ_PRICE' THEN `VALUE` ELSE NULL END) AS czPrice,
        MAX(case when `key` = 'PRICE' THEN `VALUE` ELSE NULL END) AS price
        FROM som_drg_gen_cfg
        WHERE `type` = 'PREDICTED_PRICE'
        GROUP BY `type`
        ) b
    </select>
    <!-- 查询前几月数据 -->
    <select id="getMonthData" resultType="com.my.som.vo.hospitalAnalysis.DrgsPayToPredictVo">
        select c.month,
        round((sum(c.payCost)/10000),2) as sumfee,
        round((sum(c.cost)/10000),2) as predictCost,
        (round(((sum(c.cost)-sum(c.payCost))/sum(c.cost))*100,2)) as cyb
        from (
        SELECT
        a.month,a.payCost,
        <choose>
            <when test="feeStas == 0">
                <!--                CASE WHEN a.insuType = 1 THEN a.point * b.czPrice-->
                <!--                WHEN a.insuType = 2 THEN a.point * b.cxPrice-->
                <!--                ELSE a.point * b.price END AS cost-->
                a.point * a.price  AS cost
            </when>
            <when test="feeStas == 1">
                a.cost
            </when>
        </choose>
        FROM
        (
        SELECT
        SUBSTR(a.${busKeyField},1,7) AS month
        <choose>
            <when test="feeStas == 0">
                , SUM(a.ipt_sumfee) AS payCost,
                c.insu_type AS insuType,
                SUM(IFNULL(c.totl_sco,0)) AS point,
                c.price as price

            </when>
            <when test="feeStas == 1">
                ,SUM(e.ipt_sumfee) AS payCost,
                e.INSURED_TYPE AS insuType,
                SUM(e.ycCost) cost
            </when>
        </choose>
        FROM som_drg_grp_info a
        LEFT JOIN som_hi_invy_bas_info q
        ON q.ID = a.SETTLE_LIST_ID
        <if test='enableSeAns != null and enableSeAns != "" and enableSeAns == "1"'>

            INNER JOIN som_setl_cas_crsp p
            ON q.k00 = p.k00
        </if>
        LEFT JOIN som_drg_sco c
        ON a.SETTLE_LIST_ID = c.SETTLE_LIST_ID
        <if test="feeStas == 1">
            INNER JOIN
            (
            SELECT
            a.drg_codg,
            a.DRG_NAME,
            a.rid_idt_codg,
            a.medcas_codg,
            a.adm_time,
            a.dscg_time,
            a.setl_time,
            a.is_in_group,
            b.bas_sco,
            b.incr_sco,
            b.sumfee AS ipt_sumfee,
            b.INSURED_TYPE,
            b.MED_TYPE AS dise_type,
            b.setl_pt_val,
            b.dfr_fee AS ycCost,
            b.MED_TYPE,
            b.setl_sco
            FROM som_drg_grp_fbck a
            LEFT JOIN som_drg_pt_val_pay b
            ON a.rid_idt_codg = b.rid_idt_codg
            <![CDATA[
                                                WHERE SUBSTR(a.${busKeyField},1,7) >= #{begnDate,jdbcType=VARCHAR} AND SUBSTR(a.${busKeyField},1,7) <= #{expiDate,jdbcType=VARCHAR}
                                                ]]>
            ) e
            ON a.PATIENT_ID = e.medcas_codg
            AND SUBSTR(a.adm_time,1,10) = e.adm_time
            AND SUBSTR(a.dscg_time,1,10) = e.dscg_time
            AND SUBSTR(a.setl_end_time,1,10) = e.setl_time
        </if>
        WHERE 1=1
        <if test="isRemote != null and isRemote != ''">
            <if test="isRemote == 0">
                AND SUBSTRING(q.a56,1,4)
                = SUBSTRING(#{insuplcAdmdvs},1,4)
                <!--                AND a.YDJY = '否'-->
            </if>
            <if test="isRemote == 1">
                <!--省内异地-->
                AND  SUBSTRING(q.a56,1,4)
                != SUBSTRING(#{insuplcAdmdvs},1,4)
                AND  SUBSTRING(q.a56,1,2)
                = SUBSTRING(#{insuplcAdmdvs},1,2)
                AND  SUBSTRING(q.a56,1,4)
                != #{provLevelInsuplcAdmdvs}
                <!--                AND a.YDJY = '是'-->
            </if>
            <if test="isRemote == 2">
                <!--省外异地-->
                AND  SUBSTRING(q.a56,1,2)
                != SUBSTRING(#{insuplcAdmdvs},1,2)
            </if>
            <if test="isRemote == 3">
                <!--省本级-->
                AND  SUBSTRING(q.a56,1,4)
                = #{provLevelInsuplcAdmdvs}
                <!--                AND a.YDJY = '是'-->
            </if>
            <if test="isRemote == 4">
                <!--未传值-->
                AND (q.a56  IS NULL or q.a56 ='')
            </if>
        </if>
        <include refid="Convert"></include>
        <include
                refid="com.my.som.mapper.newBusiness.common.NewBusinessCommonAnalysisMapper.queryBusCommon"></include>
        GROUP BY
        <choose>
            <when test="feeStas == 0">
                c.insu_type, c.price,
            </when>
            <when test="feeStas == 1">
                e.INSURED_TYPE,
            </when>
        </choose>
        SUBSTR(a.${busKeyField},1,7)
        ) a
        ) c
        <![CDATA[
        where c.month >= #{begnDate,jdbcType=VARCHAR}
          and c.month <= #{expiDate,jdbcType=VARCHAR}
        ]]>
        group by c.month
        order by c.month asc
    </select>

    <sql id="Convert">
        <if test="drgGroup != null and drgGroup != ''">
            and (
            instr(a.drg_codg, #{drgGroup,jdbcType=VARCHAR}) > 0
            or
            instr(a.DRG_NAME, #{drgGroup,jdbcType=VARCHAR}) > 0
            )
        </if>

        <if test="recordType != null and recordType != ''">
            and a.medcas_type = #{recordType}
        </if>
        <if test="dept != null and dept !=''">
            and a.dscg_caty_codg_inhosp = #{dept}
        </if>
    </sql>
</mapper>
