<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.som.dao.hospitalAnalysis.DrgsKnowledgeBaseDao">
    <select id="getMdcAdrg" resultType="com.my.som.vo.hospitalAnalysis.MdcContainAdrgVo">
        select
            a.mdc_codg AS mdcCodg,
            b.MDC_NAME AS mdcName,
            group_concat(distinct a.adrg_codg) AS adrgCodes
        from som_drg_name a left join som_mdc_cfg b on a.mdc_codg = b.mdc_codg
        where 1 = 1
        <if test="queryParam.drgGroupType!=null and queryParam.drgGroupType!=''">
            AND  a.grper_type = #{queryParam.drgGroupType}
        </if>
        group by a.mdc_codg,b.MDC_NAME
        order by a.mdc_codg
    </select>

    <select id="getAdrgAndDrgs" resultType="com.my.som.vo.hospitalAnalysis.AdrgContainDrgsVo">
        select
            a.adrg_codg AS adrgCodg,
            a.ADRG_NAME AS adrgName,
            group_concat(distinct a.drg_codg) AS drgsCodes
            from som_drg_name a
        where 1 = 1
        <if test="queryParam.drgGroupType!=null and queryParam.drgGroupType!=''">
            AND  a.grper_type = #{queryParam.drgGroupType}
        </if>
        group by a.adrg_codg,a.ADRG_NAME
        order by a.adrg_codg
    </select>

    <select id="getDrgsIndex" resultType="com.my.som.vo.hospitalAnalysis.DrgsKnowledgeVo">
        select
            y.*
        from (
            SELECT DISTINCT drg_codg, a.HOSPITAL_ID
            from som_drg_grp_info a
            <if test="queryParam.enableSeAns != null and queryParam.enableSeAns != '' and queryParam.enableSeAns == 1">
                INNER JOIN som_hi_invy_bas_info b
                ON a.SETTLE_LIST_ID = b.ID
                INNER JOIN som_setl_cas_crsp p
                ON b.K00 = p.K00
            </if>
            where grp_stas='1'
            <if test="queryParam.year!=null and queryParam.year!=''">
                AND SUBSTR(dscg_time,1,4) = #{queryParam.year}
            </if>
            <include refid="com.my.som.common.mapper.CommonMapper.fbAuthQueryParam" />
        ) x  inner join
        (
            select
                b.drg_codg AS drgsCode,
                b.DRG_NAME AS drgsName,
                b.medcas_val AS medcasVal,
                b.HOSPITAL_ID,
                ROUND(convert(AES_DECRYPT(UNHEX(b.standard_avg_fee),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) using utf8),2) AS avgCost,
                ROUND(convert(AES_DECRYPT(UNHEX(b.standard_ave_hosp_day),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) using utf8),2) AS avgDays,
                ROUND(b.avg_drug_fee,2) AS avgDrugFee,
                ROUND(b.avg_mcs_fee,2) AS avgMaterailCost,
                ROUND(b.avg_abt_fee,2) AS avgAbtFee,
                ROUND(b.inspect_fee_standard_val,2) inspectFeeStandardVal,
                null AS children
            from som_drg_standard b
            <where>
                <if test="queryParam.year!=null and queryParam.year!=''">
                    AND  b.STANDARD_YEAR = #{queryParam.year}
                </if>
                <if test="queryParam.queryDrg!=null and queryParam.queryDrg!=''">
                    AND  b.drg_codg =#{queryParam.queryDrg}
                </if>
                <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
                    AND b.HOSPITAL_ID = #{queryParam.hospitalId,jdbcType=VARCHAR}
                </if>
                <!--<if test="queryParam.drgGroupType!=null and queryParam.drgGroupType!=''">-->
                    <!--AND  a.grper_type = #{queryParam.drgGroupType}-->
                <!--</if>-->
            </where>
            ORDER BY b.drg_codg, b.HOSPITAL_ID
        ) y on x.drg_codg = y.drgsCode
        AND x.HOSPITAL_ID = y.HOSPITAL_ID
    </select>

    <select id="getCountByCoverRate" resultType="com.my.som.vo.hospitalAnalysis.DrgsCoverRateVo">
        select
          a.*,
          b.mdcNum-a.mdcCoverNum AS mdcNotCoverNum
        from (
            select
                count(DISTINCT(b.mdc_codg)) AS mdcCoverNum,
                count(case when b.drg_codg is not null then 1 else null end) AS drgsCoverNum,
                count(case when b.drg_codg is null then 1 else null end) AS drgsNotCoverNum,
                count(case when SUBSTR(b.drg_codg,2,1) in ('R','S','T','U','V','W','X','Y','Z') then 1 else null end) AS medicalDeptNum,
                count(case when SUBSTR(b.drg_codg,2,1) in ('A','B','C','D','E','F','G','H','J') then 1 else null end) AS surgeryDeptNum,
                count(case when SUBSTR(b.drg_codg,2,1) in ('K','L','M','N','P','Q') then 1 else null end) AS notOperationNum,
                count(CASE WHEN right(b.drg_codg,1) = '1' THEN 1 ELSE NULL END) AS seriousComplication,
                count(CASE WHEN right(b.drg_codg,1) = '3' THEN 1 ELSE NULL END) AS normalComplication,
                count(CASE WHEN right(b.drg_codg,1) = '5' THEN 1 ELSE NULL END) AS noComplication
            from som_drg_standard a
            left join
            (
                SELECT
                    distinct drg_codg,mdc_codg, a.HOSPITAL_ID
                from som_drg_grp_info a
                <if test="queryParam.enableSeAns != null and queryParam.enableSeAns != '' and queryParam.enableSeAns == 1">
                    INNER JOIN som_hi_invy_bas_info b
                    ON a.SETTLE_LIST_ID = b.ID
                    INNER JOIN som_setl_cas_crsp p
                    ON b.K00 = p.K00
                </if>
                where grp_stas='1'
                <if test="queryParam.year!=null and queryParam.year!=''">
                    AND SUBSTR(dscg_time,1,4) = #{queryParam.year}
                </if>
                <include refid="com.my.som.common.mapper.CommonMapper.fbAuthQueryParam" />
            ) b on a.drg_codg = b.drg_codg
            AND a.HOSPITAL_ID = b.HOSPITAL_ID
            <where>
                <if test="queryParam.year!=null and queryParam.year!=''">
                    AND  a.STANDARD_YEAR = #{queryParam.year}
                </if>
                <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
                    AND a.HOSPITAL_ID = #{queryParam.hospitalId,jdbcType=VARCHAR}
                </if>
            </where>
        )a cross join (
            select count(distinct mdc_codg) as mdcNum from som_mdc_cfg where 1=1
            <if test="queryParam.drgGroupType!=null and queryParam.drgGroupType!=''">
                AND  a.grper_type = #{queryParam.drgGroupType}
            </if>
        ) b
    </select>

</mapper>
