<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.som.dao.hospitalAnalysis.InGroupAnalysisDao">
    <select id="list" resultType="com.my.som.vo.hospitalAnalysis.InGroupAnalysisInfo">
    select a.*,
        b.NAME AS priOutHosDeptName
    from
        (
            select a.dscg_caty_codg_inhosp as priOutHosDeptCode,
                    a.HOSPITAL_ID,
                    IFNULL(ROUND(NULLIF(sum(case when a.grp_stas = '1' then 1 else 0 end),0) /
                    count(1) * 100,2),0) AS inGrpupRate,
                    IFNULL(count(distinct (case when a.grp_stas = '1' then a.drg_codg else null end)),0) AS drgsNum,
                    COUNT(1) AS medicalTotalNum,
                    COUNT(case when a.grp_stas = '1' then 1 else null end) AS groupNum,
                    COUNT(case when a.grp_stas != '1' then 1 else null end) AS noGroupNum,
                    COUNT(case when a.chk_flag = '0' then 1 else null end) AS eliminateNum,
                    COUNT(case when a.drg_codg = '0000' then 1 else null end) as mainCodeErrorNum,
                    COUNT(case when a.drg_codg = '0001' then 1 else null end) as noGroupPlanNum,
                    COUNT(case when a.drg_codg = '0002' then 1 else null end) as mainCodeAndOperateErrorNum,
                    COUNT(case when a.drg_codg = '0003' then 1 else null end) as codeAndSexErrorNum,
                    COUNT(case when a.drg_codg = '0004' then 1 else null end) as ageErrorNum,
                    COUNT(case when a.drg_codg = '0005' then 1 else null end) as xseWeightErrorNum,
                    COUNT(case when a.drg_codg = '0006' then 1 else null end) as xseAgeErrorNum,
                    COUNT(CASE WHEN a.drg_codg IS NULL THEN 1 ELSE NULL END) AS groupFailNum
            from som_drg_grp_info a
                <if test='queryParam.enableSeAns != null and queryParam.enableSeAns != "" and queryParam.enableSeAns == "1"'>
                    LEFT JOIN som_hi_invy_bas_info q
                    ON a.SETTLE_LIST_ID = q.ID
                    INNER JOIN som_setl_cas_crsp p
                    ON q.k00 = p.k00
                </if>
                    LEFT JOIN som_drg_grp_rcd b
                              ON a.SETTLE_LIST_ID = b.SETTLE_LIST_ID
            where a.dscg_caty_codg_inhosp IS NOT NULL
            AND a.dscg_caty_codg_inhosp != ''
            <if test="queryParam.drCodg != null and queryParam.drCodg != ''">
                AND a.ipdr_code = #{queryParam.drCodg,jdbcType=VARCHAR}
            </if>
            <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
                AND a.HOSPITAL_ID = #{queryParam.hospitalId}
            </if>
            <if test="queryParam.b16c!=null and queryParam.b16c!=''">
                AND a.dscg_caty_codg_inhosp = #{queryParam.b16c}
            </if>
            <if test="queryParam.inStartTime != null and
                      queryParam.inStartTime != '' and
                      queryParam.inEndTime != null and
                      queryParam.inEndTime != ''">
                AND a.adm_time BETWEEN #{queryParam.inStartTime} AND CONCAT(#{queryParam.inEndTime},' 23:59:59')
            </if>
            <if test="queryParam.cy_start_date!=null and
                       queryParam.cy_start_date!='' and
                       queryParam.cy_end_date!=null and
                       queryParam.cy_end_date!=''">
                AND a.dscg_time BETWEEN #{queryParam.cy_start_date} and CONCAT(#{queryParam.cy_end_date},' 23:59:59')
            </if>
            <if test="queryParam.seStartTime!=null and
                       queryParam.seStartTime!='' and
                       queryParam.seEndTime!=null and
                       queryParam.seEndTime!=''">
                AND a.setl_end_time BETWEEN #{queryParam.seStartTime,jdbcType=VARCHAR} and CONCAT(#{queryParam.seEndTime,jdbcType=VARCHAR},' 23:59:59')
            </if>
                GROUP BY a.dscg_caty_codg_inhosp, a.HOSPITAL_ID
                ORDER BY inGrpupRate desc
        )a
        LEFT join som_dept b
       on a.priOutHosDeptCode = b.CODE
        AND a.HOSPITAL_ID = b.HOSPITAL_ID
    </select>

    <select id="getTopCountInfo" resultType="com.my.som.vo.hospitalAnalysis.InGroupTopCountInfo">
        select a.*,
               ifnull(round(ifnull(ifnull(a.medicalRecordNum - a.lastMonthMedicalRecordNum, 0) /nullif(a.lastMonthMedicalRecordNum, 0), 0) * 100, 2),0)as medicalRecordNumRingRatio,
               ifnull(round(ifnull(ifnull(a.medicalRecordNum - lastYearMedicalRecordNum, 0) /nullif(a.lastYearMedicalRecordNum, 0), 0) * 100, 2), 0) as medicalRecordNumYOY,

               ifnull(round(ifnull(ifnull(a.drgNum - lastMonthDrgNum, 0) / nullif(a.lastMonthDrgNum, 0), 0) * 100, 2),0)                                                                   as drgNumRingRatio,
               ifnull(round(ifnull(ifnull(a.drgNum - lastYearDrgNum, 0) / nullif(a.lastYearDrgNum, 0), 0) * 100, 2),0)                                                                   as drgNumYOY,

               ifnull(round(ifnull(ifnull(a.drgInGroupMedcasVal - lastMonthInGroupNum, 0) / nullif(a.lastMonthInGroupNum, 0),0) * 100, 2), 0)                                        as inGroupNumRatio,
               ifnull(round(ifnull(ifnull(a.drgInGroupMedcasVal - lastYearInGroupNum, 0) / nullif(a.lastYearInGroupNum, 0), 0) *100, 2), 0)                                                    as inGroupNumYOY,

               ifnull(round(ifnull(ifnull(a.notInGroupNum - lastMonthNotInGroupNum, 0) /nullif(a.lastMonthNotInGroupNum, 0), 0) * 100, 2), 0)   as notInGroupNumRatio,
               ifnull(round(ifnull(ifnull(a.notInGroupNum - lastYearNotInGroupNum, 0) /nullif(a.lastYearNotInGroupNum, 0), 0) * 100, 2), 0)    as notInGroupNumYOY
        from (
                 select ifnull(COUNT(CASE
                                         WHEN a.${queryParam.busKeyField} BETWEEN #{queryParam.cy_start_date} and CONCAT(#{queryParam.cy_end_date}, ' 23:59:59')
                                             THEN 1
                                         ELSE NULL END), 0) AS medicalRecordNum,
                        ifnull(COUNT(CASE
                                         WHEN a.${queryParam.busKeyField} BETWEEN #{queryParam.lastMonth_cy_start_date} and CONCAT(#{queryParam.lastMonth_cy_end_date}, ' 23:59:59')
                                             THEN 1
                                         ELSE NULL END), 0) AS lastMonthMedicalRecordNum,
                        ifnull(COUNT(CASE
                                         WHEN a.${queryParam.busKeyField} BETWEEN #{queryParam.lastYear_cy_start_date} and CONCAT(#{queryParam.lastYear_cy_end_date}, ' 23:59:59')
                                             THEN 1
                                         ELSE NULL END), 0) AS lastYearMedicalRecordNum,

                        ifnull(COUNT(distinct (CASE
                                                   WHEN a.${queryParam.busKeyField} BETWEEN #{queryParam.cy_start_date} and CONCAT(#{queryParam.cy_end_date}, ' 23:59:59')
                                                       AND a.grp_stas = '1'
                                                       THEN a.drg_codg
                                                   ELSE NULL END)), 0) AS drgNum,
                        ifnull(COUNT(distinct (CASE
                                                   WHEN a.${queryParam.busKeyField} BETWEEN #{queryParam.lastMonth_cy_start_date} and CONCAT(#{queryParam.lastMonth_cy_end_date}, ' 23:59:59')
                                                       AND a.grp_stas = '1'
                                                       THEN a.drg_codg
                                                   ELSE NULL END)), 0) AS lastMonthDrgNum,
                        ifnull(COUNT(distinct (CASE
                                                   WHEN a.${queryParam.busKeyField} BETWEEN #{queryParam.lastYear_cy_start_date} and CONCAT(#{queryParam.lastYear_cy_end_date}, ' 23:59:59')
                                                       AND a.grp_stas = '1'
                                                       THEN a.drg_codg
                                                   ELSE NULL END)), 0) AS lastYearDrgNum,

                        ifnull(COUNT(CASE
                                         WHEN a.${queryParam.busKeyField} BETWEEN #{queryParam.cy_start_date} and CONCAT(#{queryParam.cy_end_date}, ' 23:59:59')
                                             AND a.grp_stas = '1' then 1
                                         ELSE NULL END), 0)            AS drgInGroupMedcasVal,
                        ifnull(COUNT(CASE
                                         WHEN a.${queryParam.busKeyField} BETWEEN #{queryParam.lastMonth_cy_start_date} and CONCAT(#{queryParam.lastMonth_cy_end_date}, ' 23:59:59')
                                             AND a.grp_stas = '1' then 1
                                         ELSE NULL END), 0)            AS lastMonthInGroupNum,
                        ifnull(COUNT(CASE
                                         WHEN a.${queryParam.busKeyField} BETWEEN #{queryParam.lastYear_cy_start_date} and CONCAT(#{queryParam.lastYear_cy_end_date}, ' 23:59:59')
                                             AND a.grp_stas = '1' then 1
                                         ELSE NULL END), 0)            AS lastYearInGroupNum,

                        ifnull(COUNT(CASE
                                         WHEN a.${queryParam.busKeyField} BETWEEN #{queryParam.cy_start_date} and CONCAT(#{queryParam.cy_end_date}, ' 23:59:59')
                                             AND a.grp_stas != '1' then 1
                                         ELSE NULL END), 0)            AS notInGroupNum,
                        ifnull(COUNT(CASE
                                         WHEN a.${queryParam.busKeyField} BETWEEN #{queryParam.lastMonth_cy_start_date} and CONCAT(#{queryParam.lastMonth_cy_end_date}, ' 23:59:59')
                                             AND a.grp_stas != '1' then 1
                                         ELSE NULL END), 0)            AS lastMonthNotInGroupNum,
                        ifnull(COUNT(CASE
                                         WHEN a.${queryParam.busKeyField} BETWEEN #{queryParam.lastYear_cy_start_date} and CONCAT(#{queryParam.lastYear_cy_end_date}, ' 23:59:59')
                                             AND a.grp_stas != '1' then 1
                                         ELSE NULL END), 0)            AS lastYearNotInGroupNum
                 from som_drg_grp_info a
                        <if test='queryParam.enableSeAns != null and queryParam.enableSeAns != "" and queryParam.enableSeAns == "1"'>
                            LEFT JOIN som_hi_invy_bas_info q
                            ON a.SETTLE_LIST_ID = q.ID
                            INNER JOIN som_setl_cas_crsp p
                            ON q.k00 = p.k00
                        </if>

                          left join som_drg_grp_rcd b
                                    on a.SETTLE_LIST_ID = b.SETTLE_LIST_ID

            <where>
                <include refid="Common_sql"></include>
                 <include refid="com.my.som.common.mapper.CommonMapper.fbAuthQueryParam" />
            </where>

             ) a
    </select>

    <select id="getNoGroupResonCountInfo" resultType="com.my.som.vo.dipBusiness.DipGroupErrorMsgVo">
        SELECT b.*,
        COUNT(1)OVER() AS medcasVal
        FROM(
        SELECT a.grp_fale_rea AS notInGroupReason,
        a.grp_stas AS grpStas,
        COUNT(1)OVER() AS totalNum
        FROM som_drg_grp_rcd a
        WHERE SETTLE_LIST_ID IN (
        SELECT settle_list_id
        FROM som_drg_grp_info c
        <if test='enableSeAns != null and enableSeAns != "" and enableSeAns == "1"'>
            LEFT JOIN som_hi_invy_bas_info q
            ON c.SETTLE_LIST_ID = q.ID
            INNER JOIN som_setl_cas_crsp p
            ON q.k00 = p.k00
        </if>
        WHERE 1=1
        <if test="inStartTime != null and inStartTime != ''
                                                     and inEndTime != null and inEndTime != ''">
            AND c.adm_time BETWEEN #{inStartTime} AND CONCAT(#{inEndTime},' 23:59:59')
        </if>
        <if test="cy_start_date != null and cy_start_date != ''
                                                     and cy_end_date!=null and cy_end_date!=''">
            AND c.dscg_time BETWEEN #{cy_start_date} AND CONCAT(#{cy_end_date},' 23:59:59')
        </if>
        <if test="seStartTime != null and seStartTime != ''
                                                     and seEndTime != null and seEndTime != ''">
            AND c.setl_end_time BETWEEN #{seStartTime} and CONCAT(#{seEndTime},' 23:59:59')
        </if>
        )
        ) b
        WHERE grpStas = '0'
<!--        SELECT a.*,-->
<!--               IFNULL(CONCAT(ROUND(IFNULL(IFNULL(a.medcasVal,0) / IFNULL(b.totals,0),0) * 100,2),'%'),0) AS allRate-->
<!--        FROM-->
<!--             (-->
<!--                 SELECT a.*,-->
<!--                        IFNULL(CONCAT(ROUND(IFNULL(IFNULL(a.medcasVal,0) / IFNULL((IFNULL(b.notInGroupTotals,0) + IFNULL(c.eliminateTotals,0)),0),0) * 100,2),'%'),0) AS notInGroupRate-->
<!--                 FROM-->
<!--                      (-->
<!--                          SELECT '未入组原因' AS notInGroupName,-->
<!--                                 '主要诊断不在分组方案中' AS notInGroupReason,-->
<!--                                 COUNT(1) AS medcasVal-->
<!--                          FROM som_drg_grp_info-->
<!--                          WHERE grp_stas = '0'-->
<!--                            AND drg_codg = '0000'-->
<!--                            <if test="queryParam.hospitalId != null and queryParam.hospitalId != ''">-->
<!--                                AND HOSPITAL_ID = #{queryParam.hospitalId,jdbcType=VARCHAR}-->
<!--                            </if>-->
<!--                            <if test="queryParam.b16c != null and queryParam.b16c != ''">-->
<!--                                AND dscg_caty_codg_inhosp = #{queryParam.b16c,jdbcType=VARCHAR}-->
<!--                            </if>-->
<!--                            <if test="queryParam.drCodg != null and queryParam.drCodg != ''">-->
<!--                                AND ipdr_code = #{queryParam.drCodg,jdbcType=VARCHAR}-->
<!--                            </if>-->
<!--                            <if test="queryParam.inStartTime != null and queryParam.inStartTime != '' and queryParam.inEndTime != null and queryParam.inEndTime != ''">-->
<!--                                AND adm_time BETWEEN #{queryParam.inStartTime,jdbcType=VARCHAR} AND CONCAT(#{queryParam.inEndTime,jdbcType=VARCHAR},' 23:59:59')-->
<!--                            </if>-->
<!--                            <if test="queryParam.cy_start_date != null and queryParam.cy_start_date != '' and queryParam.cy_end_date != null and queryParam.cy_end_date != ''">-->
<!--                                AND dscg_time BETWEEN #{queryParam.cy_start_date,jdbcType=VARCHAR} AND CONCAT(#{queryParam.cy_end_date,jdbcType=VARCHAR},' 23:59:59')-->
<!--                            </if>-->
<!--                            <if test="queryParam.seStartTime != null and queryParam.seStartTime != '' and queryParam.seEndTime != null and queryParam.seEndTime != ''">-->
<!--                                AND setl_end_time BETWEEN #{queryParam.seStartTime,jdbcType=VARCHAR} and CONCAT(#{queryParam.seEndTime,jdbcType=VARCHAR},' 23:59:59')-->
<!--                            </if>-->
<!--                          UNION ALL-->
<!--                          SELECT '未入组原因' AS notInGroupName,-->
<!--                                 '新生儿编码入组,年龄不为0' AS notInGroupReason,-->
<!--                                 COUNT(1) AS medcasVal-->
<!--                          FROM som_drg_grp_info-->
<!--                          WHERE grp_stas = '0'-->
<!--                            AND drg_codg = '0001'-->
<!--                            <if test="queryParam.hospitalId != null and queryParam.hospitalId != ''">-->
<!--                                AND HOSPITAL_ID = #{queryParam.hospitalId,jdbcType=VARCHAR}-->
<!--                            </if>-->
<!--                            <if test="queryParam.b16c != null and queryParam.b16c != ''">-->
<!--                                AND dscg_caty_codg_inhosp = #{queryParam.b16c,jdbcType=VARCHAR}-->
<!--                            </if>-->
<!--                            <if test="queryParam.drCodg != null and queryParam.drCodg != ''">-->
<!--                                AND ipdr_code = #{queryParam.drCodg,jdbcType=VARCHAR}-->
<!--                            </if>-->
<!--                            <if test="queryParam.inStartTime != null and queryParam.inStartTime != '' and queryParam.inEndTime != null and queryParam.inEndTime != ''">-->
<!--                                AND adm_time BETWEEN #{queryParam.inStartTime,jdbcType=VARCHAR} AND CONCAT(#{queryParam.inEndTime,jdbcType=VARCHAR},' 23:59:59')-->
<!--                            </if>-->
<!--                            <if test="queryParam.cy_start_date != null and queryParam.cy_start_date != '' and queryParam.cy_end_date != null and queryParam.cy_end_date != ''">-->
<!--                                AND dscg_time BETWEEN #{queryParam.cy_start_date,jdbcType=VARCHAR} AND CONCAT(#{queryParam.cy_end_date,jdbcType=VARCHAR},' 23:59:59')-->
<!--                            </if>-->
<!--                            <if test="queryParam.seStartTime != null and queryParam.seStartTime != '' and queryParam.seEndTime != null and queryParam.seEndTime != ''">-->
<!--                                AND setl_end_time BETWEEN #{queryParam.seStartTime,jdbcType=VARCHAR} and CONCAT(#{queryParam.seEndTime,jdbcType=VARCHAR},' 23:59:59')-->
<!--                            </if>-->
<!--                          UNION ALL-->
<!--                          SELECT '未入组原因' AS notInGroupName,-->
<!--                                 '当前诊断可以入MDCP但是次要诊断或者手术没有找到相应的ADRG或者填写为空' AS notInGroupReason,-->
<!--                                 COUNT(1) AS medcasVal-->
<!--                          FROM som_drg_grp_info-->
<!--                          WHERE grp_stas = '0'-->
<!--                            AND drg_codg = '0002'-->
<!--                            <if test="queryParam.hospitalId != null and queryParam.hospitalId != ''">-->
<!--                                AND HOSPITAL_ID = #{queryParam.hospitalId,jdbcType=VARCHAR}-->
<!--                            </if>-->
<!--                            <if test="queryParam.b16c != null and queryParam.b16c != ''">-->
<!--                                AND dscg_caty_codg_inhosp = #{queryParam.b16c,jdbcType=VARCHAR}-->
<!--                            </if>-->
<!--                            <if test="queryParam.drCodg != null and queryParam.drCodg != ''">-->
<!--                                AND ipdr_code = #{queryParam.drCodg,jdbcType=VARCHAR}-->
<!--                            </if>-->
<!--                            <if test="queryParam.inStartTime != null and queryParam.inStartTime != '' and queryParam.inEndTime != null and queryParam.inEndTime != ''">-->
<!--                                AND adm_time BETWEEN #{queryParam.inStartTime,jdbcType=VARCHAR} AND-->
<!--                                CONCAT(#{queryParam.inEndTime,jdbcType=VARCHAR},' 23:59:59')-->
<!--                            </if>-->
<!--                            <if test="queryParam.cy_start_date != null and queryParam.cy_start_date != '' and queryParam.cy_end_date != null and queryParam.cy_end_date != ''">-->
<!--                                AND dscg_time BETWEEN #{queryParam.cy_start_date,jdbcType=VARCHAR} AND-->
<!--                                CONCAT(#{queryParam.cy_end_date,jdbcType=VARCHAR},' 23:59:59')-->
<!--                            </if>-->
<!--                            <if test="queryParam.seStartTime != null and queryParam.seStartTime != '' and queryParam.seEndTime != null and queryParam.seEndTime != ''">-->
<!--                                AND setl_end_time BETWEEN #{queryParam.seStartTime,jdbcType=VARCHAR} and CONCAT(#{queryParam.seEndTime,jdbcType=VARCHAR},' 23:59:59')-->
<!--                            </if>-->
<!--                          UNION ALL-->
<!--                          SELECT '未入组原因' AS notInGroupName,-->
<!--                                 '主要诊断不可为null' AS notInGroupReason,-->
<!--                                 COUNT(1) AS medcasVal-->
<!--                          FROM som_drg_grp_info-->
<!--                          WHERE grp_stas = '0'-->
<!--                            AND drg_codg = '0003'-->
<!--                            <if test="queryParam.hospitalId != null and queryParam.hospitalId != ''">-->
<!--                                AND HOSPITAL_ID = #{queryParam.hospitalId,jdbcType=VARCHAR}-->
<!--                            </if>-->
<!--                            <if test="queryParam.b16c != null and queryParam.b16c != ''">-->
<!--                                AND dscg_caty_codg_inhosp = #{queryParam.b16c,jdbcType=VARCHAR}-->
<!--                            </if>-->
<!--                            <if test="queryParam.drCodg != null and queryParam.drCodg != ''">-->
<!--                                AND ipdr_code = #{queryParam.drCodg,jdbcType=VARCHAR}-->
<!--                            </if>-->
<!--                            <if test="queryParam.inStartTime != null and queryParam.inStartTime != '' and queryParam.inEndTime != null and queryParam.inEndTime != ''">-->
<!--                                AND adm_time BETWEEN #{queryParam.inStartTime,jdbcType=VARCHAR} AND-->
<!--                                CONCAT(#{queryParam.inEndTime,jdbcType=VARCHAR},' 23:59:59')-->
<!--                            </if>-->
<!--                            <if test="queryParam.cy_start_date != null and queryParam.cy_start_date != '' and queryParam.cy_end_date != null and queryParam.cy_end_date != ''">-->
<!--                                AND dscg_time BETWEEN #{queryParam.cy_start_date,jdbcType=VARCHAR} AND-->
<!--                                CONCAT(#{queryParam.cy_end_date,jdbcType=VARCHAR},' 23:59:59')-->
<!--                            </if>-->
<!--                            <if test="queryParam.seStartTime != null and queryParam.seStartTime != '' and queryParam.seEndTime != null and queryParam.seEndTime != ''">-->
<!--                                AND setl_end_time BETWEEN #{queryParam.seStartTime,jdbcType=VARCHAR} and CONCAT(#{queryParam.seEndTime,jdbcType=VARCHAR},' 23:59:59')-->
<!--                            </if>-->
<!--                          UNION ALL-->
<!--                          SELECT '未入组原因' AS notInGroupName,-->
<!--                                 '年龄参数异常' AS notInGroupReason,-->
<!--                                 COUNT(1) AS medcasVal-->
<!--                          FROM som_drg_grp_info-->
<!--                          WHERE grp_stas = '0'-->
<!--                            AND drg_codg = '0004'-->
<!--                            <if test="queryParam.hospitalId != null and queryParam.hospitalId != ''">-->
<!--                                AND HOSPITAL_ID = #{queryParam.hospitalId,jdbcType=VARCHAR}-->
<!--                            </if>-->
<!--                            <if test="queryParam.b16c != null and queryParam.b16c != ''">-->
<!--                                AND dscg_caty_codg_inhosp = #{queryParam.b16c,jdbcType=VARCHAR}-->
<!--                            </if>-->
<!--                            <if test="queryParam.drCodg != null and queryParam.drCodg != ''">-->
<!--                                AND ipdr_code = #{queryParam.drCodg,jdbcType=VARCHAR}-->
<!--                            </if>-->
<!--                            <if test="queryParam.inStartTime != null and queryParam.inStartTime != '' and queryParam.inEndTime != null and queryParam.inEndTime != ''">-->
<!--                                AND adm_time BETWEEN #{queryParam.inStartTime,jdbcType=VARCHAR} AND CONCAT(#{queryParam.inEndTime,jdbcType=VARCHAR},' 23:59:59')-->
<!--                            </if>-->
<!--                            <if test="queryParam.cy_start_date != null and queryParam.cy_start_date != '' and queryParam.cy_end_date != null and queryParam.cy_end_date != ''">-->
<!--                                AND dscg_time BETWEEN #{queryParam.cy_start_date,jdbcType=VARCHAR} AND CONCAT(#{queryParam.cy_end_date,jdbcType=VARCHAR},' 23:59:59')-->
<!--                            </if>-->
<!--                            <if test="queryParam.seStartTime != null and queryParam.seStartTime != '' and queryParam.seEndTime != null and queryParam.seEndTime != ''">-->
<!--                                AND setl_end_time BETWEEN #{queryParam.seStartTime,jdbcType=VARCHAR} and CONCAT(#{queryParam.seEndTime,jdbcType=VARCHAR},' 23:59:59')-->
<!--                            </if>-->
<!--                          UNION ALL-->
<!--                          SELECT '未入组原因' AS notInGroupName,-->
<!--                                 '新生儿体重参数异常' AS notInGroupReason,-->
<!--                                 COUNT(1) AS medcasVal-->
<!--                          FROM som_drg_grp_info-->
<!--                          WHERE grp_stas = '0'-->
<!--                            AND drg_codg = '0005'-->
<!--                            <if test="queryParam.hospitalId != null and queryParam.hospitalId != ''">-->
<!--                                AND HOSPITAL_ID = #{queryParam.hospitalId,jdbcType=VARCHAR}-->
<!--                            </if>-->
<!--                            <if test="queryParam.b16c != null and queryParam.b16c != ''">-->
<!--                                AND dscg_caty_codg_inhosp = #{queryParam.b16c,jdbcType=VARCHAR}-->
<!--                            </if>-->
<!--                            <if test="queryParam.drCodg != null and queryParam.drCodg != ''">-->
<!--                                AND ipdr_code = #{queryParam.drCodg,jdbcType=VARCHAR}-->
<!--                            </if>-->
<!--                            <if test="queryParam.inStartTime != null and queryParam.inStartTime != '' and queryParam.inEndTime != null and queryParam.inEndTime != ''">-->
<!--                                AND adm_time BETWEEN #{queryParam.inStartTime,jdbcType=VARCHAR} AND CONCAT(#{queryParam.inEndTime,jdbcType=VARCHAR},' 23:59:59')-->
<!--                            </if>-->
<!--                            <if test="queryParam.cy_start_date != null and queryParam.cy_start_date != '' and queryParam.cy_end_date != null and queryParam.cy_end_date != ''">-->
<!--                                AND dscg_time BETWEEN #{queryParam.cy_start_date,jdbcType=VARCHAR} AND CONCAT(#{queryParam.cy_end_date,jdbcType=VARCHAR},' 23:59:59')-->
<!--                            </if>-->
<!--                            <if test="queryParam.seStartTime != null and queryParam.seStartTime != '' and queryParam.seEndTime != null and queryParam.seEndTime != ''">-->
<!--                                AND setl_end_time BETWEEN #{queryParam.seStartTime,jdbcType=VARCHAR} and CONCAT(#{queryParam.seEndTime,jdbcType=VARCHAR},' 23:59:59')-->
<!--                            </if>-->
<!--                          UNION ALL-->
<!--                          SELECT '未入组原因' AS notInGroupName,-->
<!--                                 '新生儿年龄参数异常' AS notInGroupReason,-->
<!--                                 COUNT(1) AS medcasVal-->
<!--                          FROM som_drg_grp_info-->
<!--                          WHERE grp_stas = '0'-->
<!--                            AND drg_codg = '0006'-->
<!--                            <if test="queryParam.hospitalId != null and queryParam.hospitalId != ''">-->
<!--                                AND HOSPITAL_ID = #{queryParam.hospitalId,jdbcType=VARCHAR}-->
<!--                            </if>-->
<!--                            <if test="queryParam.b16c != null and queryParam.b16c != ''">-->
<!--                                AND dscg_caty_codg_inhosp = #{queryParam.b16c,jdbcType=VARCHAR}-->
<!--                            </if>-->
<!--                            <if test="queryParam.drCodg != null and queryParam.drCodg != ''">-->
<!--                                AND ipdr_code = #{queryParam.drCodg,jdbcType=VARCHAR}-->
<!--                            </if>-->
<!--                            <if test="queryParam.inStartTime != null and queryParam.inStartTime != '' and queryParam.inEndTime != null and queryParam.inEndTime != ''">-->
<!--                                AND adm_time BETWEEN #{queryParam.inStartTime,jdbcType=VARCHAR} AND CONCAT(#{queryParam.inEndTime,jdbcType=VARCHAR},' 23:59:59')-->
<!--                            </if>-->
<!--                            <if test="queryParam.cy_start_date != null and queryParam.cy_start_date != '' and queryParam.cy_end_date != null and queryParam.cy_end_date != ''">-->
<!--                                AND dscg_time BETWEEN #{queryParam.cy_start_date,jdbcType=VARCHAR} AND CONCAT(#{queryParam.cy_end_date,jdbcType=VARCHAR},' 23:59:59')-->
<!--                            </if>-->
<!--                            <if test="queryParam.seStartTime != null and queryParam.seStartTime != '' and queryParam.seEndTime != null and queryParam.seEndTime != ''">-->
<!--                                AND setl_end_time BETWEEN #{queryParam.seStartTime,jdbcType=VARCHAR} and CONCAT(#{queryParam.seEndTime,jdbcType=VARCHAR},' 23:59:59')-->
<!--                            </if>-->
<!--                          UNION ALL-->
<!--                          SELECT '未入组原因' AS notInGroupName,-->
<!--                                 '分组失败' AS notInGroupReason,-->
<!--                                 COUNT(1) AS medcasVal-->
<!--                          FROM som_drg_grp_info-->
<!--                          WHERE grp_stas = '0'-->
<!--                            AND drg_codg IS NULL-->
<!--                            <if test="queryParam.hospitalId != null and queryParam.hospitalId != ''">-->
<!--                                AND HOSPITAL_ID = #{queryParam.hospitalId,jdbcType=VARCHAR}-->
<!--                            </if>-->
<!--                            <if test="queryParam.b16c != null and queryParam.b16c != ''">-->
<!--                                AND dscg_caty_codg_inhosp = #{queryParam.b16c,jdbcType=VARCHAR}-->
<!--                            </if>-->
<!--                            <if test="queryParam.drCodg != null and queryParam.drCodg != ''">-->
<!--                                AND ipdr_code = #{queryParam.drCodg,jdbcType=VARCHAR}-->
<!--                            </if>-->
<!--                            <if test="queryParam.inStartTime != null and queryParam.inStartTime != '' and queryParam.inEndTime != null and queryParam.inEndTime != ''">-->
<!--                                AND adm_time BETWEEN #{queryParam.inStartTime,jdbcType=VARCHAR} AND CONCAT(#{queryParam.inEndTime,jdbcType=VARCHAR},' 23:59:59')-->
<!--                            </if>-->
<!--                            <if test="queryParam.cy_start_date != null and queryParam.cy_start_date != '' and queryParam.cy_end_date != null and queryParam.cy_end_date != ''">-->
<!--                                AND dscg_time BETWEEN #{queryParam.cy_start_date,jdbcType=VARCHAR} AND CONCAT(#{queryParam.cy_end_date,jdbcType=VARCHAR},' 23:59:59')-->
<!--                            </if>-->
<!--                            <if test="queryParam.seStartTime != null and queryParam.seStartTime != '' and queryParam.seEndTime != null and queryParam.seEndTime != ''">-->
<!--                                AND setl_end_time BETWEEN #{queryParam.seStartTime,jdbcType=VARCHAR} and CONCAT(#{queryParam.seEndTime,jdbcType=VARCHAR},' 23:59:59')-->
<!--                            </if>-->
<!--                      ) a-->
<!--                 INNER JOIN-->
<!--                            (-->
<!--                                SELECT COUNT(1) AS notInGroupTotals-->
<!--                                FROM som_drg_grp_info-->
<!--                                WHERE grp_stas = '0'-->
<!--                                <if test="queryParam.hospitalId != null and queryParam.hospitalId != ''">-->
<!--                                    AND HOSPITAL_ID = #{queryParam.hospitalId,jdbcType=VARCHAR}-->
<!--                                </if>-->
<!--                                <if test="queryParam.b16c != null and queryParam.b16c != ''">-->
<!--                                    AND dscg_caty_codg_inhosp = #{queryParam.b16c,jdbcType=VARCHAR}-->
<!--                                </if>-->
<!--                                <if test="queryParam.drCodg != null and queryParam.drCodg != ''">-->
<!--                                    AND ipdr_code = #{queryParam.drCodg,jdbcType=VARCHAR}-->
<!--                                </if>-->
<!--                                <if test="queryParam.inStartTime != null and queryParam.inStartTime != '' and queryParam.inEndTime != null and queryParam.inEndTime != ''">-->
<!--                                    AND adm_time BETWEEN #{queryParam.inStartTime,jdbcType=VARCHAR} AND CONCAT(#{queryParam.inEndTime,jdbcType=VARCHAR},' 23:59:59')-->
<!--                                </if>-->
<!--                                <if test="queryParam.cy_start_date != null and queryParam.cy_start_date != '' and queryParam.cy_end_date != null and queryParam.cy_end_date != ''">-->
<!--                                    AND dscg_time BETWEEN #{queryParam.cy_start_date,jdbcType=VARCHAR} AND CONCAT(#{queryParam.cy_end_date,jdbcType=VARCHAR},' 23:59:59')-->
<!--                                </if>-->
<!--                                <if test="queryParam.seStartTime != null and queryParam.seStartTime != '' and queryParam.seEndTime != null and queryParam.seEndTime != ''">-->
<!--                                    AND setl_end_time BETWEEN #{queryParam.seStartTime,jdbcType=VARCHAR} and CONCAT(#{queryParam.seEndTime,jdbcType=VARCHAR},' 23:59:59')-->
<!--                                </if>-->
<!--                            ) b-->
<!--                 INNER JOIN-->
<!--                            (-->
<!--                                SELECT COUNT(CASE WHEN chk_flag = '0' THEN 1 ELSE NULL END) AS eliminateTotals-->
<!--                                FROM som_drg_grp_info-->
<!--                                <where>-->
<!--                                    <if test="queryParam.hospitalId != null and queryParam.hospitalId != ''">-->
<!--                                        AND HOSPITAL_ID = #{queryParam.hospitalId,jdbcType=VARCHAR}-->
<!--                                    </if>-->
<!--                                    <if test="queryParam.b16c != null and queryParam.b16c != ''">-->
<!--                                        AND dscg_caty_codg_inhosp = #{queryParam.b16c,jdbcType=VARCHAR}-->
<!--                                    </if>-->
<!--                                    <if test="queryParam.drCodg != null and queryParam.drCodg != ''">-->
<!--                                        AND ipdr_code = #{queryParam.drCodg,jdbcType=VARCHAR}-->
<!--                                    </if>-->
<!--                                    <if test="queryParam.inStartTime != null and queryParam.inStartTime != '' and queryParam.inEndTime != null and queryParam.inEndTime != ''">-->
<!--                                        AND adm_time BETWEEN #{queryParam.inStartTime,jdbcType=VARCHAR} AND CONCAT(#{queryParam.inEndTime,jdbcType=VARCHAR},' 23:59:59')-->
<!--                                    </if>-->
<!--                                    <if test="queryParam.cy_start_date != null and queryParam.cy_start_date != '' and queryParam.cy_end_date != null and queryParam.cy_end_date != ''">-->
<!--                                        AND dscg_time BETWEEN #{queryParam.cy_start_date,jdbcType=VARCHAR} AND CONCAT(#{queryParam.cy_end_date,jdbcType=VARCHAR},' 23:59:59')-->
<!--                                    </if>-->
<!--                                    <if test="queryParam.seStartTime != null and queryParam.seStartTime != '' and queryParam.seEndTime != null and queryParam.seEndTime != ''">-->
<!--                                        AND setl_end_time BETWEEN #{queryParam.seStartTime,jdbcType=VARCHAR} and CONCAT(#{queryParam.seEndTime,jdbcType=VARCHAR},' 23:59:59')-->
<!--                                    </if>-->
<!--                                </where>-->
<!--                            ) c-->
<!--             ) a-->
<!--        INNER JOIN-->
<!--                   (-->
<!--                       SELECT COUNT(1) AS totals-->
<!--                       FROM som_drg_grp_info-->
<!--                       <where>-->
<!--                           <if test="queryParam.hospitalId != null and queryParam.hospitalId != ''">-->
<!--                               AND HOSPITAL_ID = #{queryParam.hospitalId,jdbcType=VARCHAR}-->
<!--                           </if>-->
<!--                           <if test="queryParam.b16c != null and queryParam.b16c != ''">-->
<!--                               AND dscg_caty_codg_inhosp = #{queryParam.b16c,jdbcType=VARCHAR}-->
<!--                           </if>-->
<!--                           <if test="queryParam.drCodg != null and queryParam.drCodg != ''">-->
<!--                               AND ipdr_code = #{queryParam.drCodg,jdbcType=VARCHAR}-->
<!--                           </if>-->
<!--                           <if test="queryParam.inStartTime != null and queryParam.inStartTime != '' and queryParam.inEndTime != null and queryParam.inEndTime != ''">-->
<!--                               AND adm_time BETWEEN #{queryParam.inStartTime,jdbcType=VARCHAR} AND CONCAT(#{queryParam.inEndTime,jdbcType=VARCHAR},' 23:59:59')-->
<!--                           </if>-->
<!--                           <if test="queryParam.cy_start_date != null and queryParam.cy_start_date != '' and queryParam.cy_end_date != null and queryParam.cy_end_date != ''">-->
<!--                               AND dscg_time BETWEEN #{queryParam.cy_start_date,jdbcType=VARCHAR} AND CONCAT(#{queryParam.cy_end_date,jdbcType=VARCHAR},' 23:59:59')-->
<!--                           </if>-->
<!--                           <if test="queryParam.seStartTime != null and queryParam.seStartTime != '' and queryParam.seEndTime != null and queryParam.seEndTime != ''">-->
<!--                               AND setl_end_time BETWEEN #{queryParam.seStartTime,jdbcType=VARCHAR} and CONCAT(#{queryParam.seEndTime,jdbcType=VARCHAR},' 23:59:59')-->
<!--                           </if>-->
<!--                       </where>-->
<!--                   ) b-->
    </select>

    <sql id="Common_sql">
        <!-- 科室编码 -->
        <if test="queryParam.deptCode != null and queryParam.deptCode != ''">
            and a.dscg_caty_codg_inhosp = #{queryParam.deptCode,jdbcType=VARCHAR}
        </if>
        <if test="queryParam.b16c != null and queryParam.b16c != ''">
            and a.dscg_caty_codg_inhosp = #{queryParam.b16c,jdbcType=VARCHAR}
        </if>
        <if test="queryParam.drCodg != null and queryParam.drCodg != ''">
            and a.ipdr_code = #{queryParam.drCodg,jdbcType=VARCHAR}
        </if>
    </sql>

</mapper>