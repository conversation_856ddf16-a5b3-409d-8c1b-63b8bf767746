<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.som.dao.medicalQuality.SettleListMarkMapper">

    <!-- 插入 -->
    <insert id="insert">
        INSERT INTO som_invy_label(K00, SETTLE_LIST_ID, HOSPITAL_ID)
        VALUES(#{k00,jdbcType=VARCHAR}, #{settleListId,jdbcType=VARCHAR}, #{hospitalId,jdbcType=VARCHAR})
    </insert>

    <!-- 删除 -->
    <delete id="delete">
        DELETE FROM som_invy_label
        WHERE k00 = #{k00,jdbcType=VARCHAR}
    </delete>

    <!-- 查询数据 -->
    <select id="query" resultType="com.my.som.vo.medicalQuality.SettleListMarkVo">
        SELECT a.K00 AS k00,
               a.SETTLE_LIST_ID AS settleListId,
               a.HOSPITAL_ID AS hospitalId
        FROM som_invy_label a
        <where>
            <if test="k00 != null and k00 != ''">
                a.K00 = #{k00,jdbcType=VARCHAR}
            </if>
        </where>
    </select>

    <select id="queryErrorNum" resultType="int">
        SELECT COUNT(1) FROM som_invy_chk_detl WHERE SETTLE_LIST_ID = #{id}
    </select>

</mapper>
