<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.som.dao.medicalQuality.PPLCompareAnalysisControllerMapper">

    <!-- 获取人员信息 -->
    <select id="getList" resultType="com.my.som.vo.medicalQuality.PPLCompareAnalysisVo">
        select a.id,
               a.a11 AS name,
               a.a48 AS patientId,
               SUBSTR(a.b12,1,10) AS inHosTime,
               SUBSTR(a.b15,1,10) AS outHosTime,
               b.name AS deptName
        from som_hi_invy_bas_info a
        left join som_dept b
        on a.b16c = b.code
        <if test="group != null and group != ''">
            <if test="group == 1">
                LEFT JOIN som_dip_grp_info c
            </if>
            <if test="group == 3">
                LEFT JOIN som_drg_grp_info c
            </if>
            ON a.ID = c.SETTLE_LIST_ID
        </if>
        <where>
            <if test="group != null and group != ''">
                <if test="group == 1">
                    AND c.dip_codg = #{dipGroup}
                </if>
                <if test="group == 3">
                    AND c.drg_codg = #{queryDrg}
                </if>
            </if>
            <if test="diagnosis != null and diagnosis != ''">
                AND a.C03C = #{diagnosis}
            </if>
            <if test="begnDate != null and begnDate != '' and expiDate != null and expiDate != ''">
                AND a.b15 between #{begnDate,jdbcType=VARCHAR} and concat(#{expiDate,jdbcType=VARCHAR}, ' 23:59:59')
            </if>
            <if test="deptCode != null and deptCode != ''">
                AND a.b16c = #{deptCode,jdbcType=VARCHAR}
            </if>
            <if test="a48 != null and a48 != ''">
                AND a.a48 = #{a48,jdbcType=VARCHAR}
            </if>
        </where>
    </select>

    <select id="getInfo" resultType="com.my.som.vo.medicalQuality.PPLCompareAnalysisVo">
        SELECT
            distinct a.A11 AS name,
                     a.ID,
                     a.A12C AS gend,
                     a.A14 AS age,
                     a.A48 AS patientId,
                     SUBSTR(a.b12,1,10) AS inHosTime,
                     SUBSTR(a.b15,1,10) AS outHosTime,
                     b.name AS indeptname,
                     b.name AS outdeptname,
                     a.B20 AS inHosdays,
                     a.A54 AS insuredType
        FROM
            som_hi_invy_bas_info a
        left join som_dept b on a.B13C = b.code and a.B16C = b.code
        <where>
            <if test="ids!= null and ids.size() > 0">
                <foreach collection="ids" item="id" open="and a.id in (" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
        </where>
    </select>

    <select id="getIngroup" resultType="com.my.som.vo.medicalQuality.PPLCompareAnalysisVo">
        SELECT
            a.ID,
            a.B20 AS inHosdays,
            a.D01 AS zong,
            <if test="group != null and group != ''">
                <if test="group == 1">
                    g.dip_codg AS dipcode,
                    g.DIP_NAME AS dipname,
                    g.act_ipt,
                    ROUND(((IFNULL(ROUND(convert(AES_DECRYPT(UNHEX(g.dip_standard_inpf),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) using utf8),2),0)) - a.D01),2) AS dipqyfycy,

                    ROUND(((IFNULL(ROUND(convert(AES_DECRYPT(UNHEX(g.dip_standard_avg_fee_same_lv),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) using utf8),2),0)) - a.D01),2) AS dipjbfycy,

                    ROUND(((IFNULL(ROUND(convert(AES_DECRYPT(UNHEX(g.dip_standard_ipt_days),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) using utf8),2),0)) - g.act_ipt),2) AS dipindayqycy,

                    ROUND(((IFNULL(ROUND(convert(AES_DECRYPT(UNHEX(g.dip_standard_ipt_days_same_lv),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) using utf8),2),0)) - g.act_ipt),2) AS dipindayjbcy,

                    IFNULL(ROUND(convert(AES_DECRYPT(UNHEX(g.dip_standard_inpf),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) using utf8),2),0) AS dipareaStandardCost,

                    IFNULL(ROUND(convert(AES_DECRYPT(UNHEX(g.dip_standard_avg_fee_same_lv),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) using utf8),2),0) AS diplevelStandardCost,

                    IFNULL(ROUND(convert(AES_DECRYPT(UNHEX(g.dip_standard_ipt_days),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) using utf8),2),0) AS dipareaStandardIndays,

                    IFNULL(ROUND(convert(AES_DECRYPT(UNHEX(g.dip_standard_ipt_days_same_lv),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) using utf8),2),0) AS diplevelStandardIndays
                </if>
                <if test="group == 3">
                    b.drg_codg AS drgcode,
                    b.DRG_NAME AS drgname,
                    b.act_ipt,
                    IFNULL(ROUND(convert(AES_DECRYPT(UNHEX(b.standard_ave_hosp_day),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) using utf8),2),0)as drgindayavg,

                    IFNULL(ROUND(convert(AES_DECRYPT(UNHEX(b.standard_avg_fee),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) using utf8),2),0)as drgavgcost,

                    ROUND(((IFNULL(ROUND(convert(AES_DECRYPT(UNHEX(b.standard_avg_fee),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) using utf8),2),0)) - a.D01),2) AS drgfycy,

                    ROUND(((IFNULL(ROUND(convert(AES_DECRYPT(UNHEX(b.standard_ave_hosp_day),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) using utf8),2),0))-b.act_ipt),2)AS drgindaycy
                </if>
            </if>
        from som_hi_invy_bas_info a
            LEFT JOIN (
                select
                b.HOSPITAL_ID,
                b.SETTLE_LIST_ID,
                b.dscg_time,
                b.act_ipt,
                b.drg_codg,
                b.DRG_NAME,
                f.STANDARD_YEAR,
                f.standard_ave_hosp_day,
                f.standard_avg_fee
                from som_drg_grp_info b
                left join som_drg_standard f
                on b.drg_codg = f.drg_codg
                and substr(b.dscg_time,1,4) = f.STANDARD_YEAR
                AND b.HOSPITAL_ID = f.HOSPITAL_ID
                )b
            ON b.SETTLE_LIST_ID = a.ID
            AND b.HOSPITAL_ID = a.HOSPITAL_ID
                LEFT JOIN (
                select
                    c.dip_codg,
                    c.DIP_NAME,
                    c.SETTLE_LIST_ID,
                    c.act_ipt,
                    e.dip_wt,
                    e.dip_standard_inpf,
                    e.dip_standard_avg_fee_same_lv,
                    e.dip_standard_ipt_days,
                    e.dip_standard_ipt_days_same_lv
                from som_dip_grp_info c
                    left join som_dip_standard e
                    on c.dip_codg = e.dip_codg
                    and substr(c.dscg_time,1,4) = e.STANDARD_YEAR
                ) g
                    ON g.SETTLE_LIST_ID = a.ID
                <where>
                    <if test="ids!= null and ids.size() > 0">
                        and a.id
                        <foreach collection="ids" item="id" open="in (" close=")" separator=",">
                            #{id}
                        </foreach>
                    </if>
                </where>
    </select>

    <select id="getCost" resultType="com.my.som.vo.medicalQuality.PPLCompareAnalysisVo">
        select
                a.ID,
                round(case when a.A54 = 1 then f.drg_wt * 100 * h.czPrice else f.drg_wt * 100 * h.cxPrice end,2) as predictCost,
                f.ipt_sumfee as zong,
                CONCAT((ROUND((((case when a.A54 = 1 then f.drg_wt * 100 * h.czPrice else f.drg_wt * 100 * h.cxPrice end)-(f.ipt_sumfee))/(case when a.A54 = 1 then f.drg_wt * 100 * h.czPrice else f.drg_wt * 100 * h.cxPrice end))* 100 ,2)) ,'%') as drgcybl,
                ROUND((((case when a.A54 = 1 then f.drg_wt * 100 * h.czPrice else f.drg_wt * 100 * h.cxPrice end) - (f.ipt_sumfee))/(f.ipt_sumfee)),2) as srcy,
                case when a.A54 = 1 then round((f.drg_wt * 100 * h.czPrice) - f.ipt_sumfee,2) else round((f.drg_wt * 100 * h.cxPrice) - f.ipt_sumfee,2) end as drgcy,
                ROUND(f.standard_avg_fee,2) AS drgareaStandardCost,
                round(case when a.A54 = 1 then g.dip_wt * 100 * h.czPrice else g.dip_wt * 100 * h.cxPrice end,2) as dippredictCost,
                g.ipt_sumfee as dipzong,
                CONCAT((ROUND((((case when a.A54 = 1 then g.dip_wt * 100 * h.czPrice else g.dip_wt * 100 * h.cxPrice end)-(g.ipt_sumfee))/(case when a.A54 = 1 then g.dip_wt * 100 * h.czPrice else g.dip_wt * 100 * h.cxPrice end)) * 100,2)),'%') as dipcybl,
                case when a.A54 = 1 then round((g.dip_wt * 100 * h.czPrice) - g.ipt_sumfee,2) else round((g.dip_wt * 100 * h.cxPrice) - g.ipt_sumfee,2) end as dipcy
        from som_hi_invy_bas_info a
        left join
            (
            select
                    b.SETTLE_LIST_ID,
                    b.ipt_sumfee,
                    c.drg_wt,
                    c.standard_avg_fee
            from som_drg_grp_info b left join som_drg_standard c
            on b.drg_codg = c.drg_codg
            and substr(b.dscg_time,1,4) = c.STANDARD_YEAR
            AND b.HOSPITAL_ID = c.HOSPITAL_ID
            ) f
        on a.ID = f.SETTLE_LIST_ID
        left join
            (
            select
                    d.SETTLE_LIST_ID,
                    d.ipt_sumfee,
                    e.dip_wt
            from som_dip_grp_info d
            left join som_dip_standard e
            on d.dip_codg = e.dip_codg
            and substr(d.dscg_time,1,4) = e.STANDARD_YEAR
            ) g
        on a.ID = g.SETTLE_LIST_ID
        CROSS JOIN
            (
            SELECT
                    MAX( CASE WHEN `key` = 'CX_PRICE' THEN `VALUE` ELSE NULL END ) AS cxPrice,
                    MAX( CASE WHEN `key` = 'CZ_PRICE' THEN `VALUE` ELSE NULL END ) AS czPrice,
                    MAX( CASE WHEN `key` = 'PRICE' THEN `VALUE` ELSE NULL END ) AS price
            FROM som_dip_gen_cfg
            WHERE type = 'PREDICTED_PRICE'
            GROUP BY type
            ) h
        <where>
            <if test="ids!= null and ids.size() > 0">
                and a.id
                <foreach collection="ids" item="id" open="in (" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
        </where>
    </select>

    <select id="getCostPay" resultType="com.my.som.vo.medicalQuality.PPLCompareAnalysisVo">
        SELECT
            a.ID,
            sum( CASE WHEN b.cwf IS NOT NULL OR b.cwf != '' THEN b.cwf ELSE 0 END ) AS cwf,
            sum( CASE WHEN b.zcf IS NOT NULL OR b.zcf != '' THEN b.zcf ELSE 0 END ) AS zcf,
            sum( CASE WHEN b.jcf IS NOT NULL OR b.jcf != '' THEN b.jcf ELSE 0 END ) AS jcf,
            sum( CASE WHEN b.hyf IS NOT NULL OR b.hyf != '' THEN b.hyf ELSE 0 END ) AS hyf,
            sum( CASE WHEN b.treat_fee IS NOT NULL OR b.treat_fee != '' THEN b.treat_fee ELSE 0 END ) AS treat_fee,
            sum( CASE WHEN b.ssf IS NOT NULL OR b.ssf != '' THEN b.ssf ELSE 0 END ) AS ssf,
            sum( CASE WHEN b.nursfee IS NOT NULL OR b.nursfee != '' THEN b.nursfee ELSE 0 END ) AS nursfee,
            sum( CASE WHEN b.wsclf IS NOT NULL OR b.wsclf != '' THEN b.wsclf ELSE 0 END ) AS wsclf,
            sum( CASE WHEN b.west_fee IS NOT NULL OR b.west_fee != '' THEN b.west_fee ELSE 0 END ) AS west_fee,
            sum( CASE WHEN b.zyypf IS NOT NULL OR b.zyypf != '' THEN b.zyypf ELSE 0 END ) AS zyypf,
            sum( CASE WHEN b.zcy IS NOT NULL OR b.zcy != '' THEN b.zcy ELSE 0 END ) AS zcy,
            sum( CASE WHEN b.ybzlf IS NOT NULL OR b.ybzlf != '' THEN b.ybzlf ELSE 0 END ) AS ybzlf,
            sum( CASE WHEN b.ghf IS NOT NULL OR b.ghf != '' THEN b.ghf ELSE 0 END ) AS ghf,
            sum( CASE WHEN b.qt IS NOT NULL OR b.qt != '' THEN b.qt ELSE 0 END ) AS qt
        FROM
            som_hi_invy_bas_info a
                LEFT JOIN (
                SELECT
                    hi_setl_invy_id,
                    MAX( CASE WHEN med_chrg_itemname = '床位费' THEN amt ELSE NULL END ) AS cwf,
                    MAX( CASE WHEN med_chrg_itemname = '诊查费' THEN amt ELSE NULL END ) AS zcf,
                    MAX( CASE WHEN med_chrg_itemname = '检查费' THEN amt ELSE NULL END ) AS jcf,
                    MAX( CASE WHEN med_chrg_itemname = '化验费' THEN amt ELSE NULL END ) AS hyf,
                    MAX( CASE WHEN med_chrg_itemname = '治疗费' THEN amt ELSE NULL END ) AS treat_fee,
                    MAX( CASE WHEN med_chrg_itemname = '手术费' THEN amt ELSE NULL END ) AS ssf,
                    MAX( CASE WHEN med_chrg_itemname = '护理费' THEN amt ELSE NULL END ) AS nursfee,
                    MAX( CASE WHEN med_chrg_itemname = '卫生材料费' THEN amt ELSE NULL END ) AS wsclf,
                    MAX( CASE WHEN med_chrg_itemname = '西药费' THEN amt ELSE NULL END ) AS west_fee,
                    MAX( CASE WHEN med_chrg_itemname = '中药饮片费' THEN amt ELSE NULL END ) AS zyypf,
                    MAX( CASE WHEN med_chrg_itemname = '中成药' THEN amt ELSE NULL END ) AS zcy,
                    MAX( CASE WHEN med_chrg_itemname = '一般治疗费' THEN amt ELSE NULL END ) AS ybzlf,
                    MAX( CASE WHEN med_chrg_itemname = '挂号费' THEN amt ELSE NULL END ) AS ghf,
                    MAX( CASE WHEN med_chrg_itemname = '其他费' THEN amt ELSE NULL END ) AS qt
                FROM
                    som_hi_invy_bas_info a
                        LEFT JOIN som_hi_setl_invy_med_fee_info b ON a.ID = b.hi_setl_invy_id
                <where>
                    <if test="ids!= null and ids.size() > 0">
                        and a.id
                        <foreach collection="ids" item="id" open="in (" close=")" separator=",">
                            #{id}
                        </foreach>
                    </if>
                </where>
                GROUP BY
                    hi_setl_invy_id
            ) b ON a.ID = b.hi_setl_invy_id
                <where>
                    <if test="ids!= null and ids.size() > 0">
                        and a.id
                        <foreach collection="ids" item="id" open="in (" close=")" separator=",">
                            #{id}
                        </foreach>
                    </if>
                </where>
        group by a.ID
    </select>
</mapper>
