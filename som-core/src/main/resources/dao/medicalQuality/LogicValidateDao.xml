<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.som.dao.medicalQuality.LogicValidateDao">
    <select id="getValidateMainInfo" resultType="com.my.som.dto.medicalQuality.ValidateMainInfo">
    select
        h.*,
        g.NAME as b13n,
        g.NAME as b16n
    from(
        SELECT
            b.err_dscr as validateResult, <!--完整性校验结果 -->
               c.dip_codg AS dipCodg,
               c.DIP_NAME AS dipName,
            a.K00 AS K00,
            a.id as  id,  <!--医保结算清单ID -->
            a.a48 as  a48,  <!--病案号 -->
            a.a11 as  a11,  <!--姓名 -->
            a.a12c as  a12c,  <!--性别 -->
            a.a14 as  a14,  <!--年龄 -->
            a.b12 as  b12,  <!--入院时间 -->
            a.b13c,  <!--入院科别 -->
            a.b15 as  b15,  <!--出院时间 -->
            a.b16c,  <!--出院科别 -->
            a.c04n as  c04n,  <!--主要诊断 -->
            a.c15x01n as c15x01n,  <!--手术及操作名称 -->
            a.HOSPITAL_ID
        FROM
            som_init_hi_setl_invy_med_fee_info a
        <if test="queryParam.errType!=null and queryParam.errType!=''">
            inner join
        </if>
        <if test="queryParam.errType==null or queryParam.errType==''">
            left join
        </if>
        (
            select
                settle_list_id,
                group_concat(err_dscr)  AS err_dscr
            from som_setl_invy_chk_err_rcd
            where err_type in ('LE01','LE02','LE03','LE04','LE05','LE06','LE07')
            <if test="queryParam.errType!=null and queryParam.errType!=''">
                AND  err_type = #{queryParam.errType}
            </if>
            GROUP BY settle_list_id
        )b on a.id = b.settle_list_id
        left join som_dip_grp_info c
        on a.ID = c.SETTLE_LIST_ID
        WHERE a.active_flag = 1
          <if test="queryParam.dipCodg != null and queryParam.dipCodg != ''">
              AND c.dip_codg = #{queryParam.dipCodg}
          </if>
        <if test="queryParam.drCodg != null and queryParam.drCodg != ''">
            AND a.B25C = #{queryParam.drCodg}
        </if>
          <if test="queryParam.resultType != null and queryParam.resultType != ''">
          <choose>
              <when test="queryParam.resultType == 1">
                  AND b.err_dscr IS NOT NULL AND b.err_dscr != ''
              </when>
          </choose>
          </if>
        <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
            AND  a.HOSPITAL_ID = #{queryParam.hospitalId}
        </if>
        <if test="queryParam.a48!=null and queryParam.a48!=''">
            AND instr(`a48`,#{queryParam.a48}) > 0
        </if>
        <if test="queryParam.a11!=null and queryParam.a11!=''">
            AND `a11` = #{queryParam.a11}
        </if>
        <if test="queryParam.b16c!=null and queryParam.b16c!=''">
            AND b16c = #{queryParam.b16c}
        </if>
        <if test="queryParam.b34c!=null and queryParam.b34c!=''">
            AND `b34c` = #{queryParam.b34c}
        </if>
        <if test="queryParam.settle_start_date!=null and queryParam.settle_start_date!='' and
        queryParam.settle_end_date!=null and queryParam.settle_end_date!=''">
            AND d37 BETWEEN #{queryParam.settle_start_date} and CONCAT(#{queryParam.settle_end_date},' 23:59:59')
        </if>
        <if test="queryParam.ry_start_date!=null and queryParam.ry_start_date!='' and
        queryParam.ry_end_date!=null and queryParam.ry_end_date!=''">
            AND b12 BETWEEN #{queryParam.ry_start_date} and CONCAT(#{queryParam.ry_end_date},' 23:59:59')
        </if>
        <if test="(queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and
        queryParam.cy_end_date!=null and queryParam.cy_end_date!='') or queryParam.inHosFlag == 2">
            AND b15 BETWEEN #{queryParam.cy_start_date} and CONCAT(#{queryParam.cy_end_date},' 23:59:59')
        </if>
        <if test="queryParam.doctorIdList!=null">
            AND (
            a.B22C in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.B23C in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.B24C in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.B25C in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.B26C in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.B27C in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.B28 in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.B29C in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.B31C in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.B32C in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            )
        </if>
        order by b.err_dscr desc
        )h left join som_dept g on h.b13c = g.CODE and h.b16c =g.CODE
        AND h.HOSPITAL_ID = g.HOSPITAL_ID
    </select>

    <select id="getCountInfo" resultType="com.my.som.dto.medicalQuality.LogicValidateCountInfo">
        SELECT
            b.totalNum as totalNum, <!--总病案数 -->
            a.errorNum as errorNum,<!--逻辑错误病案数 -->
            a.codeError as codeError,<!--编码信息错误病案数 -->
            a.timeError as timeError, <!--时间信息病案数 -->
            a.costError as costError, <!--费用信息错误案数 -->
            a.sexError as sexError,  <!--性别信息错误病案数 -->
            a.ageError as ageError, <!--年龄信息错误病案数 -->
            a.babyInfoError as babyInfoError, <!--新生儿信息错误病案数 -->
            a.existenceError as existenceError  <!--生存矛盾错误病案数 -->
        FROM
        (
            SELECT
                count(distinct(case when err_type in ('LE01','LE02','LE03','LE04','LE05','LE06','LE07') then a.settle_list_id else null end)) as errorNum,
                count(distinct(case when err_type = 'LE01' then a.settle_list_id else null end)) as codeError,
                count(distinct(case when err_type = 'LE02' then a.settle_list_id else null end)) as timeError,
                count(distinct(case when err_type = 'LE03' then a.settle_list_id else null end)) as costError,
                count(distinct(case when err_type = 'LE04' then a.settle_list_id else null end)) as sexError,
                count(distinct(case when err_type = 'LE05' then a.settle_list_id else null end)) as ageError,
                count(distinct(case when err_type = 'LE06' then a.settle_list_id else null end)) as babyInfoError,
                count(distinct(case when err_type = 'LE07' then a.settle_list_id else null end)) as existenceError
        FROM
            som_setl_invy_chk_err_rcd a
            where a.settle_list_id in
            (
                select
                    id
                from som_init_hi_setl_invy_med_fee_info
                WHERE active_flag = 1
                <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
                    AND  HOSPITAL_ID = #{queryParam.hospitalId}
                </if>
                <if test="queryParam.a48!=null and queryParam.a48!=''">
                    AND `a48` = #{queryParam.a48}
                </if>
                <if test="queryParam.a11!=null and queryParam.a11!=''">
                    AND `a11` = #{queryParam.a11}
                </if>
                <if test="queryParam.b16c!=null and queryParam.b16c!=''">
                    AND b16c = #{queryParam.b16c}
                </if>
                <if test="queryParam.drCodg != null and queryParam.drCodg != ''">
                    AND B25C = #{queryParam.drCodg}
                </if>
                <if test="queryParam.b34c!=null and queryParam.b34c!=''">
                    AND `b34c` = #{queryParam.b34c}
                </if>
                <if test="queryParam.settle_start_date!=null and queryParam.settle_start_date!='' and
                queryParam.settle_end_date!=null and queryParam.settle_end_date!=''">
                    AND d37 BETWEEN #{queryParam.settle_start_date} and CONCAT(#{queryParam.settle_end_date},' 23:59:59')
                </if>
                <if test="queryParam.ry_start_date!=null and queryParam.ry_start_date!='' and
                queryParam.ry_end_date!=null and queryParam.ry_end_date!=''">
                    AND b12 BETWEEN #{queryParam.ry_start_date} and CONCAT(#{queryParam.ry_end_date},' 23:59:59')
                </if>
                <if test="queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and
                queryParam.cy_end_date!=null and queryParam.cy_end_date!=''">
                    AND b15 BETWEEN #{queryParam.cy_start_date} and CONCAT(#{queryParam.cy_end_date},' 23:59:59')
                </if>
                <if test="queryParam.doctorIdList!=null">
                    AND (
                    B22C in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    B23C in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    B24C in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    B25C in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    B26C in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    B27C in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    B28 in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    B29C in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    B31C in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    B32C in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    )
                </if>
            )
        )a
        cross join
        (
            select
                count(1) as totalNum
            from som_init_hi_setl_invy_med_fee_info a
            WHERE a.active_flag = 1
            <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
                AND  a.HOSPITAL_ID = #{queryParam.hospitalId}
            </if>
            <if test="queryParam.a48!=null and queryParam.a48!=''">
                AND `a48` = #{queryParam.a48}
            </if>
            <if test="queryParam.a11!=null and queryParam.a11!=''">
                AND `a11` = #{queryParam.a11}
            </if>
            <if test="queryParam.b16c!=null and queryParam.b16c!=''">
                AND b16c = #{queryParam.b16c}
            </if>
            <if test="queryParam.b34c!=null and queryParam.b34c!=''">
                AND `b34c` = #{queryParam.b34c}
            </if>
            <if test="queryParam.settle_start_date!=null and queryParam.settle_start_date!='' and
            queryParam.settle_end_date!=null and queryParam.settle_end_date!=''">
                AND d37 BETWEEN #{queryParam.settle_start_date} and CONCAT(#{queryParam.settle_end_date},' 23:59:59')
            </if>
            <if test="queryParam.ry_start_date!=null and queryParam.ry_start_date!='' and
            queryParam.ry_end_date!=null and queryParam.ry_end_date!=''">
                AND b12 BETWEEN #{queryParam.ry_start_date} and CONCAT(#{queryParam.ry_end_date},' 23:59:59')
            </if>
            <if test="queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and
            queryParam.cy_end_date!=null and queryParam.cy_end_date!=''">
                AND b15 BETWEEN #{queryParam.cy_start_date} and CONCAT(#{queryParam.cy_end_date},' 23:59:59')
            </if>
            <if test="queryParam.doctorIdList!=null">
                AND (
                B22C in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                B23C in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                B24C in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                B25C in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                B26C in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                B27C in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                B28 in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                B29C in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                B31C in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                B32C in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                )
            </if>
        )b
    </select>

</mapper>
