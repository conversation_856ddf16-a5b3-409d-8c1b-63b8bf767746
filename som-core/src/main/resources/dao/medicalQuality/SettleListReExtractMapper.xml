<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.som.dao.medicalQuality.SettleListReExtractMapper">
    <!-- 查询要同步的数据 -->
    <select id="queryReExtractK00" resultType="java.lang.String">
        select k00 from
        som_hi_invy_bas_info
        where
            <choose>
                <when test='type == "1"'>
                 <![CDATA[
                    b12 >= #{begnDate,jdbcType=VARCHAR}
                    and b12 <= CONCAT(#{expiDate,jdbcType=VARCHAR},' 23:59:59')
                ]]>
                </when>
                <when test='type == "2"'>
                    <![CDATA[
                    b15 >= #{begnDate,jdbcType=VARCHAR}
                    and b15 <= CONCAT(#{expiDate,jdbcType=VARCHAR},' 23:59:59')
                    ]]>
                </when>
                <when test='type == "3"'>
                    <![CDATA[
                    d37 >= #{begnDate,jdbcType=VARCHAR}
                    and d37 <= CONCAT(#{expiDate,jdbcType=VARCHAR},' 23:59:59')
                    ]]>
                </when>
            </choose>
    </select>
</mapper>
