<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.som.dao.medicalQuality.GroupCompQueryMapper">

    <select id="selectGroupData" resultType="com.my.som.model.common.SomDipStandard">
        select *
        from (
        select DISTINCT convert(AES_DECRYPT(UNHEX(a.dip_standard_inpf),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) using utf8) as dipStandardInpf,
        a.dip_codg AS dipCodg,
        a.DIP_NAME AS dipName,
        a.refer_sco as refer_sco,
        a.adjm_cof as adjm_cof,
        a.asst_list_age_grp AS asstListAgeGrp,
        a.asst_list_dise_sev_deg AS asstListDiseSevDeg,
        a.asst_list_tmor_sev_deg AS asstListTmorSevDeg
        from som_dip_standard a
        left join som_dip_supe_ultra_low_bind b
        on a.dip_codg = b.code
        and a.STANDARD_YEAR = b.`YEAR`
        and a.is_used_asst_list = b.is_used_asst_list
        and a.asst_list_age_grp = b.asst_list_age_grp
        and a.asst_list_dise_sev_deg = b.asst_list_dise_sev_deg
        and a.asst_list_tmor_sev_deg = b.asst_list_tmor_sev_deg
        and a.HOSPITAL_ID = b.HOSPITAL_ID
        <where>
            <if test="hospitalId != null and hospitalId != ''">
                AND a.HOSPITAL_ID = #{hospitalId,jdbcType=VARCHAR}
            </if>
            <if test="year != null and year != ''">
                AND a.STANDARD_YEAR = #{year,jdbcType=VARCHAR}
            </if>
        </where>
        ) b
    </select>
    <select id="selectNew" resultType="java.lang.String">
        select max(STANDARD_YEAR) as year from som_dip_standard a
        <where>
            <if test="hospitalId != null and hospitalId != ''">
                AND a.HOSPITAL_ID = #{hospitalId,jdbcType=VARCHAR}
            </if>
        </where>
    </select>

    <select id="queryRecords"  resultType="java.util.Map">
        select m.* from
        (
        SELECT count(1) ct,
        x.mainDiagDiseCodg,
        x.mainDiagDiseName,
        x.oper,
        x.mainOperCode,
        x.mainOperName
        FROM (SELECT max(b.main_diag_dise_codg) AS mainDiagDiseCodg,
        max(b.main_diag_dise_name) AS mainDiagDiseName,
        GROUP_CONCAT(case when c.seq != 0 then concat(concat(c.C35C, ' | '), c.C36N) else null end order by c.id, ',') as oper,
        max(case when c.seq = 0 then c.C35C else null end) as mainOperCode,
        max(case when c.seq = 0 then c.C36N else null end) as mainOperName
        FROM som_hi_invy_bas_info a
        LEFT JOIN som_dip_grp_rcd b ON a.id = b.SETTLE_LIST_ID
        left join som_oprn_oprt_info c on a.id = c.SETTLE_LIST_ID
        where b.dip_codg = #{dipCodg,jdbcType=VARCHAR}
        and substr(a.D37, 1, 4) = #{year,jdbcType=VARCHAR}
        <if test="hospitalId != null and hospitalId != ''">
            AND a.HOSPITAL_ID = #{hospitalId,jdbcType=VARCHAR}
        </if>
        GROUP BY a.id) x
        group by x.mainDiagDiseCodg,
        x.mainDiagDiseName,
        x.oper,
        x.mainOperCode,
        x.mainOperName ) m
        where m.ct > 1 order by m.ct desc
    </select>

</mapper>
