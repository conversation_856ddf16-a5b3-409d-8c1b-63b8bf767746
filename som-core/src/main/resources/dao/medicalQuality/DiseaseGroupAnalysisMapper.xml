<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.som.dao.medicalQuality.DiseaseGroupAnalysisMapper">
    <!-- 获取信息 -->
    <select id="getList" resultType="com.my.som.vo.medicalQuality.DiseaseGroupAnalysisVo">
        SELECT
                <choose>
                    <when test="dto.type == 1">
                        a.dip_codg AS dipCodg,
                        a.DIP_NAME AS dipName,
                    </when>
                    <when test="dto.type == 3">
                        a.drg_codg AS drgCodg,
                        a.DRG_NAME AS drgName,
                    </when>
                </choose>
        <if test="dto.queryType == 1">
            c.NAME AS deptName,
        </if>
        COUNT(1) AS inGroupNumber
        <if test="dto.queryType == 1">
            <trim prefix=",">
                a.dscg_caty_codg_inhosp as deptCode
            </trim>
        </if>
        from
                <choose>
                    <when test="dto.type == 1">
                        som_dip_grp_info a
                    </when>
                    <when test="dto.type == 3">
                        som_drg_grp_info a
                    </when>
                </choose>
        LEFT JOIN som_dept c ON a.dscg_caty_codg_inhosp = c.CODE
        AND a.HOSPITAL_ID = c.HOSPITAL_ID
        <where>
         a.grp_stas = '1'
         <if test="dto.begnDate != null and dto.begnDate != '' and dto.expiDate != null and dto.expiDate != ''">
            and a.dscg_time between #{dto.begnDate} and #{dto.expiDate}
        </if>
        <if test="dto.deptCode != null and dto.deptCode != ''">
            and a.dscg_caty_codg_inhosp = #{dto.deptCode}
        </if>
        <if test="dto.deptName != null and dto.deptName !=''">
            and c.NAME =#{dto.deptName}
        </if>
        <if test="dto.drgGroup != null and dto.drgGroup != '' and dto.drgGroup != null and dto.drgGroup !=''">
            and a.drg_codg = #{dto.drgGroup}
        </if>
        <if test="dto.dipGroup != null and dto.dipGroup != '' and dto.dipGroup != null and dto.dipGroup !=''">
            and a.dip_codg = #{dto.dipGroup}
        </if>
        <include refid="com.my.som.common.mapper.CommonMapper.fbAuthDto" />
        </where>
        GROUP BY
         <choose>
            <when test="dto.type == 1">
                dip_codg,DIP_NAME
                <if test="dto.queryType == 1">
                    <trim prefix=",">
                        dscg_caty_codg_inhosp,c.NAME
                    </trim>
                </if>
            </when>
            <when test="dto.type == 3">
                drg_codg,DRG_NAME
                <if test="dto.queryType == 1">
                    <trim prefix=",">
                        dscg_caty_codg_inhosp,c.NAME
                    </trim>
                </if>
            </when>
         </choose>
    </select>

    <select id="getInfo" resultType="com.my.som.vo.medicalQuality.DiseaseGroupAnalysisVo">
        SELECT
        <choose>
            <when test="dto.type == 1">
                a.dip_codg AS dipCodg,
                a.DIP_NAME AS dipName,
            </when>
            <when test="dto.type == 3">
                a.drg_codg AS drgCodg,
                a.DRG_NAME AS drgName,
            </when>
        </choose>
        <if test="dto.queryType == 1">
            c.NAME AS deptName,
        </if>
        COUNT(1) AS inGroupNumber
        <if test="dto.queryType == 1">
            <trim prefix=",">
                a.dscg_caty_codg_inhosp as deptCode
            </trim>
        </if>
        from
        <choose>
            <when test="dto.type == 1">
                som_dip_grp_info a
            </when>
            <when test="dto.type == 3">
                som_drg_grp_info a
            </when>
        </choose>
        LEFT JOIN som_dept c ON a.dscg_caty_codg_inhosp = c.CODE
        AND a.HOSPITAL_ID = c.HOSPITAL_ID
        <where>
            a.grp_stas = '1'
            <if test="dto.begnDate != null and dto.begnDate != '' and dto.expiDate != null and dto.expiDate != ''">
                and a.dscg_time between #{dto.begnDate} and #{dto.expiDate}
            </if>
            <if test="dto.deptCode != null and dto.deptCode != ''">
                and a.dscg_caty_codg_inhosp = #{dto.deptCode}
            </if>
            <if test="dto.deptName != null and dto.deptName !=''">
                and c.NAME =#{dto.deptName}
            </if>
            <if test="dto.drgGroup != null and dto.drgGroup != '' and dto.drgGroup != null and dto.drgGroup !=''">
                and a.drg_codg = #{dto.drgGroup}
            </if>
            <if test="dto.dipGroup != null and dto.dipGroup != '' and dto.dipGroup != null and dto.dipGroup !=''">
                and a.dip_codg = #{dto.dipGroup}
            </if>
            <include refid="com.my.som.common.mapper.CommonMapper.fbAuthDto" />
        </where>
        GROUP BY
        <choose>
            <when test="dto.type == 1">
                dip_codg,DIP_NAME
                <if test="dto.queryType == 1">
                    <trim prefix=",">
                        dscg_caty_codg_inhosp,c.NAME
                    </trim>
                </if>
            </when>
            <when test="dto.type == 3">
                drg_codg,DRG_NAME
                <if test="dto.queryType == 1">
                    <trim prefix=",">
                        dscg_caty_codg_inhosp,c.NAME
                    </trim>
                </if>
            </when>
        </choose>
    </select>

    <select id="getInGroup" resultType="com.my.som.vo.medicalQuality.DiseaseGroupAnalysisVo">
      SELECT
            d.*,
            <choose>
            <when test="dto.type == 1">
                ROUND((d.dipareaStandardCost-d.avgCost),2)as dipqyfycy,
                ROUND((d.diplevelStandardCost-d.avgCost),2)as dipjbfycy,
                ROUND((d.dipareaStandardIndays-d.actIpt),2)as dipindayqycy,
                ROUND((d.diplevelStandardIndays-d.actIpt),2)as dipindayjbcy
            </when>
            <when test="dto.type == 3">
                ROUND((d.drgareaStandardCost-d.avgCost),2)as drgfycy,
                ROUND((d.drgindayavg-d.actIpt),2)as drgindaycy
            </when>
            </choose>
            FROM
            (
                SELECT b.*,
                   round( b.sumfee / b.inGroupNumber, 2 ) AS avgCost,
                    <choose>
                        <when test="dto.type == 1">
                            IFNULL(ROUND(convert(AES_DECRYPT(UNHEX(g.dip_standard_inpf),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) using utf8),2),0) AS dipareaStandardCost,
                            IFNULL(ROUND(convert(AES_DECRYPT(UNHEX(g.dip_standard_avg_fee_same_lv),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) using utf8),2),0) AS diplevelStandardCost,
                            IFNULL(ROUND(convert(AES_DECRYPT(UNHEX(g.dip_standard_ipt_days),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) using utf8),2),0) AS dipareaStandardIndays,
                            IFNULL(ROUND(convert(AES_DECRYPT(UNHEX(g.dip_standard_ipt_days_same_lv),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) using utf8),2),0) AS diplevelStandardIndays
                        </when>
                        <when test="dto.type == 3">
                            IFNULL(ROUND(convert(AES_DECRYPT(UNHEX(g.standard_ave_hosp_day),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) using utf8),2),0) AS drgindayavg,
                            IFNULL(ROUND(convert(AES_DECRYPT(UNHEX(g.standard_avg_fee),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) using utf8),2),0) AS drgareaStandardCost
                        </when>
                    </choose>
            FROM
                (
                    SELECT
                        <choose>
                            <when test="dto.type == 1">
                                   a.dip_codg AS dipCodg,
                            </when>
                            <when test="dto.type == 3">
                                 a.drg_codg AS drgCodg,
                            </when>
                        </choose>
                        c.NAME AS deptName,
                        a.dscg_caty_codg_inhosp AS deptCode,
                        count( CASE WHEN a.grp_stas = '1' THEN 1 ELSE NULL END ) AS inGroupNumber,
                        round( sum( ifnull( a.act_ipt, 0 ))/ count( 1 ), 1 ) AS actIpt,
                        round( sum(CASE WHEN a.ipt_sumfee IS NOT NULL AND a.ipt_sumfee != '' THEN a.ipt_sumfee ELSE 0 END),
                        2 ) AS sumfee
                    FROM
                        <choose>
                            <when test="dto.type == 1">
                                som_dip_grp_info a
                            </when>
                            <when test="dto.type == 3">
                                som_drg_grp_info a
                            </when>
                        </choose>
                    LEFT JOIN som_dept c ON a.dscg_caty_codg_inhosp = c.CODE
                    AND a.HOSPITAL_ID = c.HOSPITAL_ID
                    <where>
                    a.grp_stas = '1'
                    <if test="dto.begnDate != null and dto.begnDate != '' and dto.expiDate != null and dto.expiDate != ''">
                        and a.dscg_time between #{dto.begnDate} and #{dto.expiDate}
                    </if>
                    <if test="dto.deptCode != null and dto.deptCode != ''">
                        and a.dscg_caty_codg_inhosp = #{dto.deptCode}
                    </if>
                    <if test="dto.type == 1">
                        and a.dip_codg
                        <foreach collection="dto.ids" item="Code" open="in (" close=")" separator=",">
                            #{Code}
                        </foreach>
                    </if>
                    <if test="dto.type == 3">
                        and a.drg_codg
                        <foreach collection="dto.ids" item="Code" open="in (" close=")" separator=",">
                            #{Code}
                        </foreach>
                    </if>
                    <if test="dto.queryType==1">
                        and c.CODE
                        <foreach collection="dto.depts" item="Dept" open="in(" close=")" separator=",">
                            #{Dept}
                        </foreach>
                    </if>
                     <include refid="com.my.som.common.mapper.CommonMapper.fbAuthDto" />
                    </where>
                    GROUP BY
                    <choose>
                        <when test="dto.type == 1">
                            dip_codg,dscg_caty_codg_inhosp,c.NAME
                        </when>
                        <when test="dto.type == 3">
                            drg_codg,dscg_caty_codg_inhosp,C.NAME
                        </when>
                    </choose>
                ) b
                <choose>
                    <when test="dto.type == 1">
                        left join som_dip_standard g on b.dipCodg = g.dip_codg
                    </when>
                    <when test="dto.type == 3">
                        left join som_drg_standard g on b.drgCodg = g.drg_codg
                    </when>
                </choose>
            <where>
                <if test="dto.deptCode != null and dto.deptCode != ''">
                    and b.deptCode = #{dto.deptCode}
                </if>
                <if test="dto.type == 1">
                    and b.dipCodg
                    <foreach collection="dto.ids" item="Code" open="in (" close=")" separator=",">
                        #{Code}
                    </foreach>
                </if>
                <if test="dto.type == 3">
                    and b.drgCodg
                    <foreach collection="dto.ids" item="Code" open="in (" close=")" separator=",">
                        #{Code}
                    </foreach>
                </if>
            </where>
        )d
    </select>
    <select id="getCostPay" resultType="com.my.som.vo.medicalQuality.DiseaseGroupAnalysisVo">
        select
        <choose>
            <when test="dto.type == 1">
                d.dipCodg,
            </when>
            <when test="dto.type == 3">
                d.drgCodg,
            </when>
        </choose>
        d.deptCode,
        d.cwf,
        d.zcf,
        d.jcf,
        d.hyf,
        d.treat_fee,
        d.ssf,
        d.nursfee,
        d.wsclf,
        d.west_fee,
        d.zyypf,
        d.zcy,
        d.ybzlf,
        d.ghf,
        d.qt,
        e.deptName
        from (
        SELECT DISTINCT
        <choose>
            <when test="dto.type == 1">
                 a.dip_codg AS dipCodg,
            </when>
            <when test="dto.type == 3">
                 a.drg_codg AS drgCodg,
            </when>
        </choose>
        a.dscg_caty_codg_inhosp AS deptCode,
        SUM( CASE WHEN med_chrg_itemname = '床位费' THEN amt ELSE 0 END ) AS cwf,
        SUM( CASE WHEN med_chrg_itemname = '诊查费' THEN amt ELSE 0 END ) AS zcf,
        SUM( CASE WHEN med_chrg_itemname = '检查费' THEN amt ELSE 0 END ) AS jcf,
        SUM( CASE WHEN med_chrg_itemname = '化验费' THEN amt ELSE 0 END ) AS hyf,
        SUM( CASE WHEN med_chrg_itemname = '治疗费' THEN amt ELSE 0 END ) AS treat_fee,
        SUM( CASE WHEN med_chrg_itemname = '手术费' THEN amt ELSE 0 END ) AS ssf,
        SUM( CASE WHEN med_chrg_itemname = '护理费' THEN amt ELSE 0 END ) AS nursfee,
        SUM( CASE WHEN med_chrg_itemname = '卫生材料费' THEN amt ELSE 0 END ) AS wsclf,
        SUM( CASE WHEN med_chrg_itemname = '西药费' THEN amt ELSE 0 END ) AS west_fee,
        SUM( CASE WHEN med_chrg_itemname = '中药饮片费' THEN amt ELSE 0 END ) AS zyypf,
        SUM( CASE WHEN med_chrg_itemname = '中成药' THEN amt ELSE 0 END ) AS zcy,
        SUM( CASE WHEN med_chrg_itemname = '一般治疗费' THEN amt ELSE 0 END ) AS ybzlf,
        SUM( CASE WHEN med_chrg_itemname = '挂号费' THEN amt ELSE 0 END ) AS ghf,
        SUM( CASE WHEN med_chrg_itemname = '其他费' THEN amt ELSE 0 END ) AS qt
        FROM
        <choose>
            <when test="dto.type == 1">
                som_dip_grp_info a
            </when>
            <when test="dto.type == 3">
                som_drg_grp_info a
            </when>
        </choose>
        LEFT JOIN som_hi_setl_invy_med_fee_info b
        ON a.SETTLE_LIST_ID = b.hi_setl_invy_id
        <where>
            a.grp_stas = '1'
            <if test="dto.begnDate != null and dto.begnDate != '' and dto.expiDate != null and dto.expiDate != ''">
                and a.dscg_time between #{dto.begnDate} and #{dto.expiDate}
            </if>
            <include refid="com.my.som.common.mapper.CommonMapper.fbAuthDto" />
        </where>
        GROUP BY
        <choose>
            <when test="dto.type == 1">
                a.dip_codg,
            </when>
            <when test="dto.type == 3">
                a.drg_codg,
            </when>
        </choose>
         a.dscg_caty_codg_inhosp
        ) d
        left join
        (
        SELECT DISTINCT
        <choose>
            <when test="dto.type == 1">
                 a.dip_codg AS dipCodg,
            </when>
            <when test="dto.type == 3">
                 a.drg_codg AS drgCodg,
            </when>
        </choose>
        c.CODE,
        c.NAME AS deptName
        FROM
        <choose>
            <when test="dto.type == 1">
                som_dip_grp_info a
            </when>
            <when test="dto.type == 3">
                som_drg_grp_info a
            </when>
        </choose>
        LEFT JOIN som_dept c
        ON a.dscg_caty_codg_inhosp = c.CODE
        AND a.HOSPITAL_ID = c.HOSPITAL_ID
        <where>
            <include refid="com.my.som.common.mapper.CommonMapper.fbAuthDto" />
        </where>
        ) e on
        <choose>
            <when test="dto.type == 1">
                d.dipCodg = e.dipCodg
            </when>
            <when test="dto.type == 3">
                d.drgCodg = e.drgCodg
            </when>
        </choose>
        <where>
            <if test="dto.type == 1">
                and d.dipCodg
                <foreach collection="dto.ids" item="Code" open="in (" close=")" separator=",">
                    #{Code}
                </foreach>
            </if>
            <if test="dto.type == 3">
                and d.drgCodg
                <foreach collection="dto.ids" item="Code" open="in (" close=")" separator=",">
                    #{Code}
                </foreach>
            </if>
            <if test="dto.queryType==1">
                and e.CODE
                <foreach collection="dto.depts" item="Dept" open="in(" close=")" separator=",">
                    #{Dept}
                </foreach>
            </if>
        </where>
    </select>

    <select id="getCost" resultType="com.my.som.vo.medicalQuality.DiseaseGroupAnalysisVo">
    select
        b.*,
        Round(((SUM(b.areaStandardCost)) - (SUM(b.zong))),2) AS cy,
        CONCAT(ROUND((((SUM(b.areaStandardCost)) - (SUM(b.zong)) )/ (SUM(b.areaStandardCost)))*100,2),'%') AS cyb
    from(
        select
        <choose>
            <when test="dto.type == 1">
                 a.dip_codg AS dipCodg,
            </when>
            <when test="dto.type == 3">
                 a.drg_codg AS drgCodg,
            </when>
        </choose>
        c.NAME AS deptName,
        a.dscg_caty_codg_inhosp as deptCode,
        SUM(CASE WHEN a.standard_avg_fee IS NOT NULL AND a.standard_avg_fee != '' THEN a.standard_avg_fee ELSE 0 END)
        as areaStandardCost,
        SUM(CASE WHEN a.ipt_sumfee IS NOT NULL AND a.ipt_sumfee != '' THEN a.ipt_sumfee ELSE 0 END) as
        zong
        FROM
        <choose>
            <when test="dto.type == 1">
                som_dip_grp_info a
            </when>
            <when test="dto.type == 3">
                som_drg_grp_info a
            </when>
        </choose>
        LEFT JOIN som_dept c ON a.dscg_caty_codg_inhosp = c.CODE
        AND a.HOSPITAL_ID = c.HOSPITAL_ID
        <where>
            a.grp_stas = '1'
            <if test="dto.begnDate != null and dto.begnDate != '' and dto.expiDate != null and dto.expiDate != ''">
                and a.dscg_time between #{dto.begnDate} and #{dto.expiDate}
            </if>
            <if test="dto.deptCode != null and dto.deptCode != ''">
                and a.dscg_caty_codg_inhosp = #{dto.deptCode}
            </if>
            <if test="dto.type == 1">
                and a.dip_codg
                <foreach collection="dto.ids" item="Code" open="in (" close=")" separator=",">
                    #{Code}
                </foreach>
            </if>
            <if test="dto.type == 3">
                and a.drg_codg
                <foreach collection="dto.ids" item="Code" open="in (" close=")" separator=",">
                    #{Code}
                </foreach>
            </if>
            <if test="dto.queryType==1">
                and c.CODE
                <foreach collection="dto.depts" item="Dept" open="in(" close=")" separator=",">
                    #{Dept}
                </foreach>
            </if>
            <include refid="com.my.som.common.mapper.CommonMapper.fbAuthDto" />
        </where>
        GROUP BY
        <choose>
            <when test="dto.type == 1">
                dip_codg,dscg_caty_codg_inhosp,c.NAME
            </when>
            <when test="dto.type == 3">
                drg_codg,dscg_caty_codg_inhosp,c.NAME
            </when>
        </choose>
        )b
        GROUP BY
        <choose>
            <when test="dto.type == 1">
                b.dipCodg,b.deptCode,b.deptName
            </when>
            <when test="dto.type == 3">
                b.drgCodg,b.deptCode,b.deptName
            </when>
        </choose>
    </select>
</mapper>
