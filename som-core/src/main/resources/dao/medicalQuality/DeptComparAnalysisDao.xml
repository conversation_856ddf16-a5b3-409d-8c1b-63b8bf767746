<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.som.dao.medicalQuality.DeptComparAnalysisDao">
    <select id="getData" resultType="com.my.som.vo.medicalQuality.DeptComparAnalysisVo">
        select
               b.*,
               round((b.drgInGroupMedcasVal/b.medcasVal)*100,2) as inGroupRate,
               c.`NAME` as deptName,
               round(b.dipWt/b.medcasVal,2) as cmi,
               round(b.sumfee/b.medcasVal,2) as avgCost
        from
            (
                select
                    distinct a.dscg_caty_codg_inhosp as deptCode,
                    <!-- 病案数 -->
                    count(1) as medcasVal,
                    count(distinct(case when a.grp_stas = '1' then a.dip_codg else null end)) as dipNum,
                    <!-- 入组病案数 -->
                    count(case when a.grp_stas = '1' then 1 else null end) as drgInGroupMedcasVal,
                    <!-- 未入组病案数 -->
                    count(case when a.grp_stas != '1' then 1 else null end) as notInGroupNum,
                    round(sum(ifnull(a.act_ipt,0))/count(1),1) as actIpt,
                    sum(case when a.dip_wt is not null and a.dip_wt != '' then a.dip_wt else 0 end) as dipWt,
                    round(sum(case when a.ipt_sumfee is not null and a.ipt_sumfee != '' then a.ipt_sumfee else 0 end),2) as sumfee,
                    round(sum(a.standard_avg_fee)/count(1),2) as standardFee
                from som_dip_grp_info a
                where a.dscg_caty_codg_inhosp = #{deptCode}
                  <if test="dipGroup != null and dipGroup != ''">
                      and a.dip_codg = #{dipGroup}
                  </if>
                <if test="expiDate != null and expiDate != ''">
                    and a.dscg_time between #{begnDate} and #{expiDate}
                </if>
                group by a.dscg_caty_codg_inhosp
            ) b
        left join som_dept c
        on b.deptCode = c.`CODE`
    </select>
    <select id="getCost" resultType="com.my.som.vo.medicalQuality.DeptComparAnalysisVo">
        select
                a.dscg_caty_codg_inhosp,
                sum(case when b.cwf is not null or b.cwf != '' then b.cwf else 0 end) as cwf,
                sum(case when b.zcf is not null or b.zcf != '' then b.zcf else 0 end) as zcf,
                sum(case when b.jcf is not null or b.jcf != '' then b.jcf else 0 end) as jcf,
                sum(case when b.hyf is not null or b.hyf != '' then b.hyf else 0 end) as hyf,
                sum(case when b.treat_fee is not null or b.treat_fee != '' then b.treat_fee else 0 end) as treat_fee,
                sum(case when b.ssf is not null or b.ssf != '' then b.ssf else 0 end) as ssf,
                sum(case when b.nursfee is not null or b.nursfee != '' then b.nursfee else 0 end) as nursfee,
                sum(case when b.wsclf is not null or b.wsclf != '' then b.wsclf else 0 end) as wsclf,
                sum(case when b.west_fee is not null or b.west_fee != '' then b.west_fee else 0 end) as west_fee,
                sum(case when b.zyypf is not null or b.zyypf != '' then b.zyypf else 0 end) as zyypf,
                sum(case when b.zcy is not null or b.zcy != '' then b.zcy else 0 end) as zcy,
                sum(case when b.ybzlf is not null or b.ybzlf != '' then b.ybzlf else 0 end) as ybzlf,
                sum(case when b.ghf is not null or b.ghf != '' then b.ghf else 0 end) as ghf,
                sum(case when b.qt is not null or b.qt != '' then b.qt else 0 end) as qt
        from
        <if test="group != null and group != ''">
            <if test="group == 1">
                som_dip_grp_info a
            </if>
            <if test="group == 3">
                som_drg_grp_info a
            </if>
        </if>
        left join
            (
                select
                       hi_setl_invy_id,
                       MAX( CASE WHEN med_chrg_itemname = '床位费' THEN amt ELSE NULL END ) AS cwf,
                       MAX( CASE WHEN med_chrg_itemname = '诊查费' THEN amt ELSE NULL END ) AS zcf,
                       MAX( CASE WHEN med_chrg_itemname = '检查费' THEN amt ELSE NULL END ) AS jcf,
                       MAX( CASE WHEN med_chrg_itemname = '化验费' THEN amt ELSE NULL END ) AS hyf,
                       MAX( CASE WHEN med_chrg_itemname = '治疗费' THEN amt ELSE NULL END ) AS treat_fee,
                       MAX( CASE WHEN med_chrg_itemname = '手术费' THEN amt ELSE NULL END ) AS ssf,
                       MAX( CASE WHEN med_chrg_itemname = '护理费' THEN amt ELSE NULL END ) AS nursfee,
                       MAX( CASE WHEN med_chrg_itemname = '卫生材料费' THEN amt ELSE NULL END ) AS wsclf,
                       MAX( CASE WHEN med_chrg_itemname = '西药费' THEN amt ELSE NULL END ) AS west_fee,
                       MAX( CASE WHEN med_chrg_itemname = '中药饮片费' THEN amt ELSE NULL END ) AS zyypf,
                       MAX( CASE WHEN med_chrg_itemname = '中成药' THEN amt ELSE NULL END ) AS zcy,
                       MAX( CASE WHEN med_chrg_itemname = '一般治疗费' THEN amt ELSE NULL END ) AS ybzlf,
                       MAX( CASE WHEN med_chrg_itemname = '挂号费' THEN amt ELSE NULL END ) AS ghf,
                       MAX( CASE WHEN med_chrg_itemname = '其他费' THEN amt ELSE NULL END ) AS qt
                from
                     <if test="group != null and group != ''">
                         <if test="group == 1">
                             som_dip_grp_info a
                         </if>
                         <if test="group == 3">
                             som_drg_grp_info a
                         </if>
                     </if>
                left join
                    som_hi_setl_invy_med_fee_info b on a.SETTLE_LIST_ID = b.hi_setl_invy_id
                where
                    a.dscg_caty_codg_inhosp = #{deptCode}
                <if test="group != null and group != ''">
                    <if test="group == 1">
                        <if test="dipGroup != null and dipGroup != ''">
                            and a.dip_codg = #{dipGroup}
                        </if>
                    </if>
                    <if test="group == 3">
                        <if test="dipGroup != null and dipGroup != ''">
                            and a.drg_codg = #{queryDrg}
                        </if>
                    </if>
                </if>
                <if test="expiDate != null and expiDate != ''">
                and a.dscg_time between #{begnDate} and #{expiDate}
                </if>
                GROUP BY
                    hi_setl_invy_id
            ) b
        on
            a.SETTLE_LIST_ID = b.hi_setl_invy_id
        where
              a.dscg_caty_codg_inhosp = #{deptCode}
        <if test="group != null and group != ''">
            <if test="group == 1">
                <if test="dipGroup != null and dipGroup != ''">
                    and a.dip_codg = #{dipGroup}
                </if>
            </if>
            <if test="group == 3">
                <if test="dipGroup != null and dipGroup != ''">
                    and a.drg_codg = #{queryDrg}
                </if>
            </if>
        </if>
        and a.dscg_time between #{begnDate} and #{expiDate}
        group by
                 a.dscg_caty_codg_inhosp
    </select>
    <select id="getDrgData" resultType="com.my.som.vo.medicalQuality.DrgDeptComparAnalysisVo">
        select
                b.*,
                round((b.drgInGroupMedcasVal/b.medcasVal)*100,2) as inGroupRate,
                c.`NAME` as deptName,
                round(b.dipWt/b.medcasVal,2) as cmi,
                round(b.sumfee/b.medcasVal,2) as avgCost
        from
            (
            select
                    distinct a.dscg_caty_codg_inhosp as deptCode,
                    <!-- 病案数 -->
                    count(1) as medcasVal,
                    count(distinct(case when a.grp_stas = '1' then a.drg_codg else null end)) as drgNum,
                    <!-- 入组病案数 -->
                    count(case when a.grp_stas = '1' then 1 else null end) as drgInGroupMedcasVal,
                    <!-- 未入组病案数 -->
                    count(case when a.grp_stas != '1' then 1 else null end) as notInGroupNum,
                    round(sum(ifnull(a.act_ipt,0))/count(1),1) as actIpt,
                    sum(case when a.drg_wt is not null and a.drg_wt != '' then a.drg_wt else 0 end) as dipWt,
                    round(sum(case when a.ipt_sumfee is not null and a.ipt_sumfee != '' then a.ipt_sumfee else 0 end),2) as sumfee,
                    round(sum(a.standard_avg_fee)/count(1),2) as standardFee
            from som_drg_grp_info a
            where a.dscg_caty_codg_inhosp = #{deptCode}
            <if test="dipGroup != null and dipGroup != ''">
                and a.drg_codg = #{queryDrg}
            </if>
            <if test="expiDate != null and expiDate != ''">
                and a.dscg_time between #{begnDate} and #{expiDate}
            </if>
            group by a.dscg_caty_codg_inhosp
            ) b
        left join som_dept c
        on b.deptCode = c.`CODE`
    </select>
    <select id="getPayCostData" resultType="com.my.som.vo.medicalQuality.DeptComparAnalysisVo">
        select
            ifnull(max(case when f.insuType = 1 then f.totalCount else null end),0) as cztotalCount,
            ifnull(max(case when f.insuType = 1 then f.sumfee else null end),0) as cztotalCost,
            ifnull(max(case when f.insuType = 1 then f.czPrice else null end),0) as czpoint,
            ifnull(max(case when f.insuType = 1 then f.predictCost else null end),0) as czpredictCost,
            ifnull(max(case when f.insuType = 2 then f.totalCount else null end),0) as cxtotalCount,
            ifnull(max(case when f.insuType = 2 then f.sumfee else null end),0) as cxtotalCost,
            ifnull(max(case when f.insuType = 2 then f.cxPrice else null end),0) as cxpoint,
            ifnull(max(case when f.insuType = 2 then f.predictCost else null end),0) as cxpredictCost
        from
            (
                select d.*,
                       e.cxPrice,
                       e.czPrice,
                       case when d.insuType = 1 then d.point * e.czPrice
                            when d.insuType = 2 then d.point * e.cxPrice
                            else 0 end as predictCost
                from
                    (
                        select
                            b.A54 as insuType,
                            ifnull(count(1),0) as totalCount,
                            sum(ifnull(a.ipt_sumfee,0)) as sumfee,
                            sum(ifnull(c.dip_wt * 100,0)) as point
                        from som_dip_grp_info a
                                 left join som_hi_invy_bas_info b
                                           on a.SETTLE_LIST_ID = b.ID
                                               and a.HOSPITAL_ID = b.HOSPITAL_ID
                                 left join som_dip_standard c
                                           on a.dip_codg = c.dip_codg
                                               and substr(a.dscg_time,1,4) = c.STANDARD_YEAR
                        where a.dscg_caty_codg_inhosp = #{deptCode}
                        <if test="dipGroup != null and dipGroup != ''">
                            and a.dip_codg = #{dipGroup}
                        </if>
                        <if test="expiDate != null and expiDate != ''">
                            and a.dscg_time between #{begnDate} and #{expiDate}
                        </if>
                          and (b.A54 = '1' or b.A54 = '2')
                        group by b.A54
                    ) d
                        CROSS JOIN
                    (
                        SELECT MAX(CASE WHEN `key` = 'CX_PRICE' THEN `VALUE` ELSE NULL END) AS cxPrice,
                               MAX(case when `key` = 'CZ_PRICE' THEN `VALUE` ELSE NULL END) AS czPrice,
                               MAX(case when `key` = 'PRICE' THEN `VALUE` ELSE NULL END) AS price
                        FROM som_dip_gen_cfg
                        WHERE type = 'PREDICTED_PRICE'
                        GROUP BY type
                    ) e
            ) f
    </select>
    <select id="getDrgPayCostData" resultType="com.my.som.vo.medicalQuality.DrgDeptComparAnalysisVo">
        select
            ifnull(max(case when f.insuType = 1 then f.totalCount else null end),0) as cztotalCount,
            ifnull(max(case when f.insuType = 1 then f.sumfee else null end),0) as cztotalCost,
            ifnull(max(case when f.insuType = 1 then f.point else null end),0) as czpoint,
            ifnull(max(case when f.insuType = 1 then f.predictCost else null end),0) as czpredictCost,
            ifnull(max(case when f.insuType = 2 then f.totalCount else null end),0) as cxtotalCount,
            ifnull(max(case when f.insuType = 2 then f.sumfee else null end),0) as cxtotalCost,
            ifnull(max(case when f.insuType = 2 then f.point else null end),0) as cxpoint,
            ifnull(max(case when f.insuType = 2 then f.predictCost else null end),0) as cxpredictCost
        from
            (
                select d.*,
                       case when d.insuType = 1 then d.point * e.czPrice
                            when d.insuType = 2 then d.point * e.cxPrice
                            else 0 end as predictCost
                from
                    (
                        select
                            b.A54 as insuType,
                            ifnull(count(1),0) as totalCount,
                            sum(ifnull(a.ipt_sumfee,0)) as sumfee,
                            sum(ifnull(c.drg_wt * 100,0)) as point
                        from som_drg_grp_info a
                                 left join som_hi_invy_bas_info b
                                           on a.SETTLE_LIST_ID = b.ID
                                               and a.HOSPITAL_ID = b.HOSPITAL_ID
                                 left join som_drg_standard c
                                           on a.drg_codg = c.drg_codg
                                               and substr(a.dscg_time,1,4) = c.STANDARD_YEAR
                                 AND a.HOSPITAL_ID = c.HOSPITAL_ID
                        where a.dscg_caty_codg_inhosp = #{deptCode}
                        <if test="dipGroup != null and dipGroup != ''">
                            and a.drg_codg = #{queryDrg}
                        </if>
                        <if test="expiDate != null and expiDate != ''">
                            and a.dscg_time between #{begnDate} and #{expiDate}
                        </if>
                          and (b.A54 = '1' or b.A54 = '2')
                        group by b.A54
                    ) d
                        CROSS JOIN
                    (
                        SELECT MAX(CASE WHEN `key` = 'CX_PRICE' THEN `VALUE` ELSE NULL END) AS cxPrice,
                               MAX(case when `key` = 'CZ_PRICE' THEN `VALUE` ELSE NULL END) AS czPrice,
                               MAX(case when `key` = 'PRICE' THEN `VALUE` ELSE NULL END) AS price
                        FROM som_dip_gen_cfg
                        WHERE type = 'PREDICTED_PRICE'
                        GROUP BY type
                    ) e
            ) f
    </select>

</mapper>
