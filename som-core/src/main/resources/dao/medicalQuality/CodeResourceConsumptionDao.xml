<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.som.dao.medicalQuality.CodeResourceConsumptionDao">
    <select id="getCodeResourceConsumptionInfo" resultType="com.my.som.dto.medicalQuality.CodeResourceConsumptionVo">
        select a.*,b.name as priOutHosDeptName from (
        SELECT
            a.SETTLE_LIST_ID AS  id,
            hi.K00 AS k00,
            a.PATIENT_ID AS  patientId,
            a.HOSPITAL_ID,
            a.NAME AS  name,
            a.dscg_time as outhostime,
            a.dscg_caty_codg_inhosp AS  priOutHosDeptCode,
            CONCAT(a.main_diag_dise_codg,a.main_diag_dise_name) AS mainDiagnoseCodeAndName,
            CONCAT(a.main_oprn_oprt_codg,a.main_oprn_oprt_name) AS mainOperativeCodeAndName,
            b.main_diag_is_cho_err AS  mainDiagIsChoErr,
            b.main_oprn_is_cho_err AS  mainOprnIsChoErr,
            CONCAT(b.resu_cosm_adjm_new_main_diag_codg,b.resu_cosm_adjm_new_main_diag_name) AS newMainDiagnoseCodeAndName,
            CONCAT(b.resu_cosm_adjm_new_main_oprn_codg,b.resu_cosm_adjm_new_main_oprn_name) AS newMainOperativeCodeAndName,
            c.drg_codg AS drgCodg,
            CONCAT(c.drg_codg,c.DRG_NAME) AS drgCodeAndName,
            b.resu_cosm_adjm_new_drg_codg AS resuCosmAdjmNewDrgCodg,
            IFNULL(CONCAT(b.resu_cosm_adjm_new_drg_codg,b.resu_cosm_adjm_new_drg_name),CONCAT(c.drg_codg,c.DRG_NAME)) AS newDrgCodeAndName,
            d.dip_codg AS dipCodg,
            CONCAT(d.dip_codg,d.DIP_NAME) AS dipCodeAndName,
            b.resu_cosm_adjm_new_dip_codg AS resuCosmAdjmNewDipCodg,
            IFNULL(CONCAT(b.resu_cosm_adjm_new_dip_codg,b.resu_cosm_adjm_new_dip_name),CONCAT(d.dip_codg,d.DIP_NAME)) AS newDipCodeAndName
        FROM som_drg_grp_info a
        LEFT JOIN som_hi_invy_bas_info hi on a.SETTLE_LIST_ID = hi.id
        LEFT JOIN som_codg_resu_adjm_rcd b on a.SETTLE_LIST_ID=b.SETTLE_LIST_ID
        LEFT JOIN som_drg_grp_rcd c on a.SETTLE_LIST_ID=c.SETTLE_LIST_ID
        LEFT JOIN som_dip_grp_rcd d on a.SETTLE_LIST_ID=d.SETTLE_LIST_ID
        WHERE 1=1
        <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
            AND a.HOSPITAL_ID = #{queryParam.hospitalId}
        </if>
        <if test="queryParam.b16c!=null and queryParam.b16c!=''">
            AND a.dscg_caty_codg_inhosp = #{queryParam.b16c}
        </if>
        <if test="queryParam.a48!=null and queryParam.a48!=''">
            AND a.PATIENT_ID = #{queryParam.a48}
        </if>
        <if test="queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and
        queryParam.cy_end_date!=null and queryParam.cy_end_date!=''">
            AND a.dscg_time BETWEEN #{queryParam.cy_start_date} and CONCAT(#{queryParam.cy_end_date},' 23:59:59')
        </if>
        <if test="queryParam.doctorIdList!=null">
            AND (
            a.deptdrt_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.chfdr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.atddr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.ipdr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.resp_nurs_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.train_dr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.intn_dr in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.codr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.qltctrl_dr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.qltctrl_nurs_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            )
        </if>
        ORDER BY b.main_diag_is_cho_err desc,b.main_oprn_is_cho_err desc
        ) a
        left join som_dept b
        on a.priOutHosDeptCode = b.code and b.HOSPITAL_ID=a.HOSPITAL_ID
    </select>

    <select id="getCount" resultType="com.my.som.dto.medicalQuality.CodeResourceConsumptionCountVo">
        select
            COUNT(1) as totalMedicalNum,
            0 as diagnoseErrorNum,
            0 as operativeErrorNum,
            IFNULL(count(distinct (case when a.grp_stas = '1' then a.drg_codg else null end)),0) as totalDrgNum,
            IFNULL(count(distinct (case when d.grp_stas = '1' then d.dip_codg else null end)),0) as totalDipNum,
            0 as drgErrorNum,
            0 as dipErrorNum
        from som_drg_grp_info a
        LEFT JOIN som_drg_grp_rcd c on a.SETTLE_LIST_ID=c.SETTLE_LIST_ID
        LEFT JOIN som_dip_grp_rcd d on a.SETTLE_LIST_ID=d.SETTLE_LIST_ID
        where 1=1
        <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
            AND a.HOSPITAL_ID = #{queryParam.hospitalId}
        </if>
        <if test="queryParam.b16c!=null and queryParam.b16c!=''">
            AND a.dscg_caty_codg_inhosp = #{queryParam.b16c}
        </if>
        <if test="queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and
                queryParam.cy_end_date!=null and queryParam.cy_end_date!=''">
            AND a.dscg_time BETWEEN #{queryParam.cy_start_date} and CONCAT(#{queryParam.cy_end_date},' 23:59:59')
        </if>
        <if test="queryParam.doctorIdList!=null">
            AND (
            a.deptdrt_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.chfdr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.atddr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.ipdr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.resp_nurs_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.train_dr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.intn_dr in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.codr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.qltctrl_dr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.qltctrl_nurs_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            )
        </if>
    </select>

    <select id="getCountInfo" resultType="com.my.som.dto.medicalQuality.CodeResourceConsumptionCountVo">
            SELECT
                COUNT(case when b.main_diag_is_cho_err = '1' then 1 else null end) as diagnoseErrorNum,
                COUNT(case when b.main_oprn_is_cho_err = '1' then 1 else null end) as operativeErrorNum,
                IFNULL(count(distinct (case when a.grp_stas = '1' and a.drg_codg!=b.resu_cosm_adjm_new_drg_codg then a.drg_codg else null end)),0) as drgErrorNum,
                IFNULL(count(distinct (case when a.grp_stas = '1' and d.dip_codg!=b.resu_cosm_adjm_new_dip_codg then d.dip_codg else null end)),0) as dipErrorNum
            FROM som_drg_grp_info a
            LEFT JOIN som_codg_resu_adjm_rcd b on a.SETTLE_LIST_ID=b.SETTLE_LIST_ID
            LEFT JOIN som_drg_grp_rcd c on a.SETTLE_LIST_ID=c.SETTLE_LIST_ID
            LEFT JOIN som_dip_grp_rcd d on a.SETTLE_LIST_ID=d.SETTLE_LIST_ID
            WHERE 1=1
            <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
                AND a.HOSPITAL_ID = #{queryParam.hospitalId}
            </if>
            <if test="queryParam.b16c!=null and queryParam.b16c!=''">
                AND a.dscg_caty_codg_inhosp = #{queryParam.b16c}
            </if>
            <if test="queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and
                queryParam.cy_end_date!=null and queryParam.cy_end_date!=''">
                AND a.dscg_time BETWEEN #{queryParam.cy_start_date} and CONCAT(#{queryParam.cy_end_date},' 23:59:59')
            </if>
            <if test="queryParam.ids!=null">
                AND a.SETTLE_LIST_ID in
                <foreach collection="queryParam.ids" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="queryParam.doctorIdList!=null">
                AND (
                a.deptdrt_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                a.chfdr_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                a.atddr_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                a.ipdr_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                a.resp_nurs_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                a.train_dr_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                a.intn_dr in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                a.codr_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                a.qltctrl_dr_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                a.qltctrl_nurs_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                )
            </if>
    </select>

</mapper>
