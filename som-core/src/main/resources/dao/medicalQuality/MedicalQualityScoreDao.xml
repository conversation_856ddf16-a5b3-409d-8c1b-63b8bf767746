<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.som.dao.medicalQuality.MedicalQualityScoreDao">
    <select id="getList" resultType="com.my.som.dto.medicalQuality.MedicalAndScoreInfo">
        select a.*,b.name as b13n,b.name as b16n from (
        SELECT
            round(IFNULL(b.refer_sco,100),2) as refer_sco, <!--病案质量得分 -->
            b.dedu_point_rea as deduPointRea, <!--病案质量扣分原因 -->
            a.id as  id,  <!--医保结算清单ID -->
            a.a48 as  a48,  <!--病案号 -->
            a.a11 as  a11,  <!--姓名 -->
            a.a12c as  a12c,  <!--性别 -->
            a.a14 as  a14,  <!--年龄 -->
            a.b12 as  b12,  <!--入院时间 -->
            a.b13c as  b13c,  <!--入院科别 -->
            a.b15 as  b15,  <!--出院时间 -->
            a.b16c as  b16c,  <!--出院科别 -->
            a.c04n as  c04n,  <!--主要诊断 -->
            a.c15x01n as c15x01n,  <!--手术及操作名称 -->
            a.HOSPITAL_ID
        FROM
            som_init_hi_setl_invy_med_fee_info a
        left join som_setl_invy_qlt_dedu_point_detl b on a.id = b.settle_list_id
        WHERE a.active_flag = 1
        <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
            AND  a.HOSPITAL_ID = #{queryParam.hospitalId}
        </if>
        <if test="queryParam.a48!=null and queryParam.a48!=''">
            AND instr(`a48`,#{queryParam.a48}) > 0
        </if>
        <if test="queryParam.a11!=null and queryParam.a11!=''">
            AND `a11` LIKE CONCAT('%',#{queryParam.a11},'%')
        </if>
        <if test="queryParam.b16c!=null and queryParam.b16c!=''">
            AND b16c = #{queryParam.b16c}
        </if>
        <if test="queryParam.drCodg != null and queryParam.drCodg != ''">
            AND a.B25C = #{queryParam.drCodg}
        </if>
        <if test="queryParam.b34c!=null and queryParam.b34c!=''">
            AND `b34c` = #{queryParam.b34c}
        </if>
        <if test="queryParam.settle_start_date!=null and queryParam.settle_start_date!='' and
        queryParam.settle_end_date!=null and queryParam.settle_end_date!=''">
            AND d37 BETWEEN #{queryParam.settle_start_date} and CONCAT(#{queryParam.settle_end_date},' 23:59:59')
        </if>
        <if test="queryParam.ry_start_date!=null and queryParam.ry_start_date!='' and
        queryParam.ry_end_date!=null and queryParam.ry_end_date!=''">
            AND b12 BETWEEN #{queryParam.ry_start_date} and CONCAT(#{queryParam.ry_end_date},' 23:59:59')
        </if>
        <if test="(queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and
        queryParam.cy_end_date!=null and queryParam.cy_end_date!='') or queryParam.inHosFlag == 2">
            AND b15 BETWEEN #{queryParam.cy_start_date} and CONCAT(#{queryParam.cy_end_date},' 23:59:59')
        </if>
        <if test="queryParam.doctorIdList!=null">
            AND (
            a.B22C in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.B23C in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.B24C in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.B25C in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.B26C in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.B27C in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.B28 in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.B29C in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.B31C in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.B32C in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            )
        </if>
        order by round(IFNULL(b.refer_sco,100),2) asc
        ) a left join som_dept b on a.b13c = b.code and a.b16c = b.code
        AND a.HOSPITAL_ID = b.HOSPITAL_ID
    </select>

    <select id="getScoreCountInfo" resultType="com.my.som.dto.medicalQuality.MedicalQualityScoreCountInfo">
        SELECT
            round(((count(1)*18 - sum(a.nwb_adm_wt_dedu_point+a.nwb_bir_wt_dedu_point+a.medcas_no_dedu_point+a.gend_dedu_point+a.brdy_dedu_point+
                a.age_dedu_point+a.med_pay_way_dedu_point+a.oth_patn_bas_info_dedu_point))/count(1))/18 *100,0) as baseInfoScore, <!--基本信息得分 -->
            round(((count(1)*26 - sum(a.dscg_way_dedu_point+a.adm_time_dedu_point+a.dscg_time_dedu_point+a.act_ipt_days_dedu_point+a.dscg_caty_dedu_point+
                a.is_31_day_in_ipt_plan_dedu_point+a.adm_way_dedu_point+a.adm_caty_dedu_point+a.refldept_caty_dedu_point))/count(1))/26 *100,0) as inHosScore,<!--住院信息得分 -->
            round(sum(refer_sco)/count(1),0) as avgScore,<!--综合得分 -->
            round(((count(1)*50 - sum(a.dscg_main_diag_dedu_point+a.main_diag_codg_dedu_point+a.oth_diag_dedu_point+a.oth_diag_codg_dedu_point+a.main_oprn_oprt_name_dedu_point+
                a.main_oprn_oprt_name_dedu_point+a.adm_cond_dedu_point+a.adm_cond_dedu_point+a.palg_diag_codg_dedu_point+a.incs_heal_lv_dedu_point+
                a.brn_damg_patn_coma_time_dedu_point+a.oth_oprn_oprt_name_dedu_point+a.oth_oprn_oprt_codg_dedu_point+a.oprn_oprt_date_dedu_point+
                a.otp_diag_dedu_point+a.otp_diag_dise_codg_dedu_point+a.anst_way_dedu_point+a.oth_trt_info_dedu_point))/count(1))/50 *100,0) as diagnosisScore, <!--诊疗信息得分 -->
            round(((count(1)*6 - sum(a.sumfee_dedu_point+a.oth_ast_info_dedu_point))/count(1))/6 *100,0) as costScore <!--费用信息得分 -->
        FROM som_setl_invy_qlt_dedu_point_detl a
        where a.settle_list_id in
        (
            select
                id
            from som_init_hi_setl_invy_med_fee_info
            WHERE active_flag = 1
            <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
                AND  HOSPITAL_ID = #{queryParam.hospitalId}
            </if>
            <if test="queryParam.a48!=null and queryParam.a48!=''">
                AND `a48` = #{queryParam.a48}
            </if>
            <if test="queryParam.a11!=null and queryParam.a11!=''">
                AND `a11` = #{queryParam.a11}
            </if>
            <if test="queryParam.b16c!=null and queryParam.b16c!=''">
                AND b16c = #{queryParam.b16c}
            </if>
            <if test="queryParam.drCodg != null and queryParam.drCodg != ''">
                AND B25C = #{queryParam.drCodg}
            </if>
            <if test="queryParam.b34c!=null and queryParam.b34c!=''">
                AND `b34c` = #{queryParam.b34c}
            </if>
            <if test="queryParam.settle_start_date!=null and queryParam.settle_start_date!='' and
            queryParam.settle_end_date!=null and queryParam.settle_end_date!=''">
                AND d37 BETWEEN #{queryParam.settle_start_date} and CONCAT(#{queryParam.settle_end_date},' 23:59:59')
            </if>
            <if test="queryParam.ry_start_date!=null and queryParam.ry_start_date!='' and
            queryParam.ry_end_date!=null and queryParam.ry_end_date!=''">
                AND b12 BETWEEN #{queryParam.ry_start_date} and CONCAT(#{queryParam.ry_end_date},' 23:59:59')
            </if>
            <if test="queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and
            queryParam.cy_end_date!=null and queryParam.cy_end_date!=''">
                AND b15 BETWEEN #{queryParam.cy_start_date} and CONCAT(#{queryParam.cy_end_date},' 23:59:59')
            </if>
            <if test="queryParam.doctorIdList!=null">
                AND (
                B22C in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                B23C in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                B24C in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                B25C in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                B26C in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                B27C in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                B28 in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                B29C in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                B31C in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                B32C in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                )
            </if>
        )
    </select>

    <select id="getScoreLowTopInfo" resultType="com.my.som.dto.medicalQuality.ScoreLowTopInfo">
        SELECT
            a.id as settle_list_id, <!--结算清单ID -->
            a.a11 as  a11,   <!--姓名 -->
            round(IFNULL(b.refer_sco,0),2) as refer_sco <!--病案质量得分 -->
        FROM
        som_init_hi_setl_invy_med_fee_info a
        left join som_setl_invy_qlt_dedu_point_detl b on a.id = b.settle_list_id
        WHERE a.active_flag = 1 and b.dedu_point_rea is not null
        <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
            AND  a.HOSPITAL_ID = #{queryParam.hospitalId}
        </if>
        <if test="queryParam.a48!=null and queryParam.a48!=''">
            AND `a48` = #{queryParam.a48}
        </if>
        <if test="queryParam.a11!=null and queryParam.a11!=''">
            AND `a11` = #{queryParam.a11}
        </if>
        <if test="queryParam.b16c!=null and queryParam.b16c!=''">
            AND b16c = #{queryParam.b16c}
        </if>
        <if test="queryParam.drCodg != null and queryParam.drCodg != ''">
            AND B25C = #{queryParam.drCodg}
        </if>
        <if test="queryParam.b34c!=null and queryParam.b34c!=''">
            AND `b34c` = #{queryParam.b34c}
        </if>
        <if test="queryParam.settle_start_date!=null and queryParam.settle_start_date!='' and
        queryParam.settle_end_date!=null and queryParam.settle_end_date!=''">
            AND d37 BETWEEN #{queryParam.settle_start_date} and CONCAT(#{queryParam.settle_end_date},' 23:59:59')
        </if>
        <if test="queryParam.ry_start_date!=null and queryParam.ry_start_date!='' and
        queryParam.ry_end_date!=null and queryParam.ry_end_date!=''">
            AND b12 BETWEEN #{queryParam.ry_start_date} and CONCAT(#{queryParam.ry_end_date},' 23:59:59')
        </if>
        <if test="queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and
        queryParam.cy_end_date!=null and queryParam.cy_end_date!=''">
            AND b15 BETWEEN #{queryParam.cy_start_date} and CONCAT(#{queryParam.cy_end_date},' 23:59:59')
        </if>
        <if test="queryParam.doctorIdList!=null">
            AND (
            a.B22C in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.B23C in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.B24C in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.B25C in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.B26C in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.B27C in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.B28 in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.B29C in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.B31C in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.B32C in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            )
        </if>
        order by round(IFNULL(b.refer_sco,0),2) asc
        limit 0,10
    </select>

</mapper>
