<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.som.dao.medicalQuality.SettleListManageDao">
    <select id="getMainInfo" resultType="com.my.som.dto.medicalQuality.BusSettleListMainInfo">
        select
        h.*,
        g.NAME as b13n,
        j.<PERSON>AM<PERSON> as b16n
    from(
        SELECT
        c.id,<!--医保结算清单ID -->
       c.D35 AS listSerialNumFlag  ,
        CASE
        WHEN c.a56  IS NULL or c.a56 =''  THEN '未传值'
        WHEN SUBSTRING(c.a56, 1, 4) = SUBSTRING(#{queryParam.provLevelInsuplcAdmdvs}, 1, 4)THEN '省本级'
        WHEN SUBSTRING(c.a56, 1, 4) = SUBSTRING(#{queryParam.insuplcAdmdvs}, 1, 4) THEN '市医保'
        WHEN SUBSTRING(c.a56, 1, 2) = SUBSTRING(#{queryParam.insuplcAdmdvs}, 1, 2) AND SUBSTRING(c.a56, 1, 4) != SUBSTRING(#{queryParam.insuplcAdmdvs}, 1, 4) THEN CONCAT('省内异地','-',t2.cityName)
        ELSE '省外异地'
        END AS isRemote,
        c.a48,  <!--病案号 -->
        c.a11,  <!--姓名 -->
        c.k00,<!--病案唯一标识-->
            c.a12c, <!--性别 -->
            c.a14,  <!--年龄 -->
            c.b12,  <!--入院时间 -->
            c.B13C, <!--入院科别 -->
            c.b15,  <!--出院时间 -->
            c.B16C, <!--出院科别 -->
            c.c04n, <!--主要诊断 -->
            c.c15x01n,  <!--手术及操作名称 -->
            CONCAT(a.drg_codg,a.DRG_NAME) as drgsCodeAndName,
            CONCAT(b.dip_codg,b.DIP_NAME) as dipCodeAndName,
            b.asst_list_age_grp AS asstListAgeGrp,
            b.asst_list_dise_sev_deg AS asstListDiseSevDeg,
            b.asst_list_tmor_sev_deg AS asstListTmorSevDeg,

            CASE WHEN z.SETTLE_LIST_ID THEN 1 ELSE 0 END AS deleteFlag,
            c.HOSPITAL_ID

        FROM som_hi_invy_bas_info c
        LEFT JOIN (
        SELECT SUBSTR(data_val, 1, 4) AS cbd, labl_name  as  cityName
        FROM `som_sys_code`
        WHERE code_type = 'XZQHDM'
        AND data_val LIKE '%00'
        AND SUBSTRING(data_val, 1, 2) = SUBSTRING(#{queryParam.insuplcAdmdvs}, 1, 2)
        union
        SELECT
        CONCAT(SUBSTR(data_val, 1, 2), '99') AS cbd,
        labl_name AS cityName
        FROM
        `som_sys_code`
        WHERE
        code_type = 'XZQHDM'
        AND data_val LIKE '%0000'
        AND SUBSTRING(data_val, 1, 2) = SUBSTRING(#{queryParam.insuplcAdmdvs}, 1, 2)
        ) t2 ON t2.cbd = SUBSTRING(c.a56, 1, 4)
        <if test='queryParam.enableSeAns != null and queryParam.enableSeAns != "" and queryParam.enableSeAns == "1"'>
            INNER JOIN som_setl_cas_crsp p
            ON c.k00 = p.k00
        </if>
        left join (
            SELECT  drg_codg,DRG_NAME,SETTLE_LIST_ID,PATIENT_ID,NAME,deptdrt_code,chfdr_code,atddr_code,ipdr_code,resp_nurs_code,train_dr_code,intn_dr,codr_code,qltctrl_dr_code,qltctrl_nurs_code
            FROM  som_drg_grp_info e
            where 1=1
            <if test="queryParam.ry_start_date!=null and queryParam.ry_start_date!='' and
                queryParam.ry_end_date!=null and queryParam.ry_end_date!=''">
                AND e.adm_time BETWEEN #{queryParam.ry_start_date} and CONCAT(#{queryParam.ry_end_date},' 23:59:59')
            </if>
            <if test="queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and
            queryParam.cy_end_date!=null and queryParam.cy_end_date!=''">
                AND e.dscg_time BETWEEN #{queryParam.cy_start_date} and CONCAT(#{queryParam.cy_end_date},' 23:59:59')
            </if>
            <if test="queryParam.seStartTime!=null and queryParam.seStartTime!='' and
            queryParam.seEndTime!=null and queryParam.seEndTime!=''">
                AND e.setl_end_time BETWEEN #{queryParam.seStartTime} and CONCAT(#{queryParam.seEndTime},' 23:59:59')
            </if>
        )a
        on c.id = a.SETTLE_LIST_ID
        left join (
            SELECT dip_codg,DIP_NAME,SETTLE_LIST_ID,asst_list_age_grp,asst_list_dise_sev_deg,asst_list_tmor_sev_deg
            FROM    som_dip_grp_info d
            where 1=1
            <if test="queryParam.ry_start_date!=null and queryParam.ry_start_date!='' and
            queryParam.ry_end_date!=null and queryParam.ry_end_date!=''">
                AND d.adm_time BETWEEN #{queryParam.ry_start_date} and CONCAT(#{queryParam.ry_end_date},' 23:59:59')
            </if>
            <if test="queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and
            queryParam.cy_end_date!=null and queryParam.cy_end_date!=''">
                AND d.dscg_time BETWEEN #{queryParam.cy_start_date} and CONCAT(#{queryParam.cy_end_date},' 23:59:59')
            </if>
            <if test="queryParam.seStartTime!=null and queryParam.seStartTime!='' and
            queryParam.seEndTime!=null and queryParam.seEndTime!=''">
                AND d.setl_end_time BETWEEN #{queryParam.seStartTime} and CONCAT(#{queryParam.seEndTime},' 23:59:59')
            </if>
        ) b
        on c.id = b.SETTLE_LIST_ID
        left join som_dip_grp_bac z
        on c.ID = z.SETTLE_LIST_ID
        left join
            (SELECT SETTLE_LIST_ID FROM som_can_opt_medcas_info WHERE grper_type = #{queryParam.grperType} ) x
        on c.ID = x.SETTLE_LIST_ID
        WHERE c.ACTIVE_FLAG = 1
          <if test="queryParam.lookOver != null and queryParam.lookOver != ''">
              AND c.LOOK_OVER = #{queryParam.lookOver}
          </if>
          <if test="queryParam.isAdjustable != null and queryParam.isAdjustable != ''">
          <choose>
              <when test="queryParam.isAdjustable == 1">
                  AND x.SETTLE_LIST_ID IS NOT NULL
              </when>
              <when test="queryParam.isAdjustable == 0">
                  AND x.SETTLE_LIST_ID IS NULL
              </when>
          </choose>
          </if>
        <if test="queryParam.isNullPreHosCost != null and queryParam.isNullPreHosCost != ''">
            <choose>
                <when test="queryParam.isNullPreHosCost == 1">
                    AND c.D02 = 0
                </when>
                <when test="queryParam.isNullPreHosCost == 0">
                    AND c.D02 != 0
                </when>
            </choose>
        </if>

        <if test="queryParam.listSerialNumFlag != null and queryParam.listSerialNumFlag != ''">
            <choose>
                <when test="queryParam.listSerialNumFlag == 1">

                    AND c.D35 != '' AND c.D35 IS NOT NULL
                </when>
                <when test="queryParam.listSerialNumFlag == 0">
                    AND  (c.D35 = '' OR c.D35 IS NULL)
                </when>
            </choose>
        </if>
        <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
            AND  c.HOSPITAL_ID = #{queryParam.hospitalId}
        </if>
        <if test="queryParam.deptCode!=null and queryParam.deptCode!=''">
            AND  c.B16C = #{queryParam.deptCode}
        </if>
        <if test="queryParam.b16c!=null and queryParam.b16c!=''">
            AND c.b16c = #{queryParam.b16c}
        </if>
        <if test="queryParam.drCodg!=null and queryParam.drCodg!=''">
            AND c.B25C = #{queryParam.drCodg}
        </if>
        <if test="queryParam.queryDrg!=null and queryParam.queryDrg!=''">
            AND a.drg_codg = #{queryParam.queryDrg}
        </if>
        <if test="queryParam.queryDipGroup!=null and queryParam.queryDipGroup!=''">
            AND b.dip_codg = #{queryParam.queryDipGroup}
        </if>
        <if test="queryParam.a48!=null and queryParam.a48!=''">
            AND c.a48 LIKE CONCAT('%',#{queryParam.a48},'%')
        </if>
        <if test="queryParam.a11!=null and queryParam.a11!=''">
            AND c.a11 LIKE CONCAT('%',#{queryParam.a11},'%')
        </if>
        <if test="queryParam.b34c!=null and queryParam.b34c!=''">
            AND c.b34c = #{queryParam.b34c}
        </if>
        <if test="queryParam.ry_start_date!=null and queryParam.ry_start_date!='' and
        queryParam.ry_end_date!=null and queryParam.ry_end_date!=''">
            AND c.B12 BETWEEN #{queryParam.ry_start_date} and CONCAT(#{queryParam.ry_end_date},' 23:59:59')
        </if>
        <if test="queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and
        queryParam.cy_end_date!=null and queryParam.cy_end_date!=''">
            AND c.B15 BETWEEN #{queryParam.cy_start_date} and CONCAT(#{queryParam.cy_end_date},' 23:59:59')
        </if>
        <if test="queryParam.seStartTime!=null and queryParam.seStartTime!='' and
            queryParam.seEndTime!=null and queryParam.seEndTime!=''">
            AND c.d37 BETWEEN #{queryParam.seStartTime} and CONCAT(#{queryParam.seEndTime},' 23:59:59')
        </if>
        <if test="queryParam.isRemote != null and queryParam.isRemote != ''">
            <if test="queryParam.isRemote == 0">
                AND SUBSTRING(c.a56,1,4)
                = SUBSTRING(#{queryParam.insuplcAdmdvs},1,4)
                <!--                AND a.YDJY = '否'-->
            </if>
            <if test="queryParam.isRemote == 1">
                <!--省内异地-->
                AND  SUBSTRING(c.a56,1,4)
                != SUBSTRING(#{queryParam.insuplcAdmdvs},1,4)
                AND  SUBSTRING(c.a56,1,4)
               != #{queryParam.provLevelInsuplcAdmdvs}
                AND  SUBSTRING(c.a56,1,2)
                = SUBSTRING(#{queryParam.insuplcAdmdvs},1,2)
                <!--                AND a.YDJY = '是'-->
            </if>
            <if test="queryParam.isRemote == 2">
                <!--省外异地-->
                AND  SUBSTRING(c.a56,1,2)
                != SUBSTRING(#{queryParam.insuplcAdmdvs},1,2)
                <!--                AND a.YDJY = '是'-->
            </if>
            <if test="queryParam.isRemote == 3">
                <!--省本级-->
                AND  SUBSTRING(c.a56,1,4)
                = #{queryParam.provLevelInsuplcAdmdvs}
                <!--                AND a.YDJY = '是'-->
            </if>
            <if test="queryParam.isRemote == 4">
                <!--未传值-->
                AND (c.a56  IS NULL or c.a56 ='')
            </if>
        </if>
        <if test="queryParam.doctorIdList!=null">
            AND (
            a.deptdrt_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.chfdr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.atddr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.ipdr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.resp_nurs_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.train_dr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.intn_dr in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.codr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.qltctrl_dr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.qltctrl_nurs_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
                )
        </if>
        )h
        left join som_dept g on h.B13C = g.CODE
        AND h.HOSPITAL_ID = g.HOSPITAL_ID
        left join som_dept j on h.B16C = j.CODE
        AND h.HOSPITAL_ID = j.HOSPITAL_ID

    </select>

    <select id="getListForDoctor" resultType="com.my.som.dto.medicalQuality.BusSettleListMainInfoForDoctor">
        SELECT
            a.id as  id,  <!--医保结算清单ID -->
            a.a48 as  a48,  <!--病案号 -->
            a.a11 as  a11,  <!--姓名 -->
            a.b12 as  b12,  <!--入院时间 -->
            a.b13n as  b13n,  <!--入院科别 -->
            a.b15 as  b15,  <!--出院时间 -->
            a.b16n as  b16n,  <!--出院科别 -->
            a.c04n as  c04n,  <!--主要诊断 -->
            a.c15x01n as  c15x01n <!--手术及操作名称 -->
        FROM som_hi_invy_bas_info a
        WHERE 1 = 1
        <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
            AND  a.HOSPITAL_ID = #{queryParam.hospitalId,jdbcType=VARCHAR}
        </if>
        <if test="queryParam.b16c!=null and queryParam.b16c!=''">
            AND a.b16c = #{queryParam.b16c}
        </if>
        <if test="queryParam.a48!=null and queryParam.a48!=''">
            AND a.a48 = #{queryParam.a48}
        </if>
        <if test="queryParam.a11!=null and queryParam.a11!=''">
            AND a.a11 = #{queryParam.a11}
        </if>
        <if test="queryParam.ry_start_date!=null and queryParam.ry_start_date!='' and
        queryParam.ry_end_date!=null and queryParam.ry_end_date!=''">
            AND a.b12 BETWEEN #{queryParam.ry_start_date} and CONCAT(#{queryParam.ry_end_date},' 23:59:59')
        </if>
        <if test="queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and
        queryParam.cy_end_date!=null and queryParam.cy_end_date!=''">
            AND a.b15 BETWEEN #{queryParam.cy_start_date} and CONCAT(#{queryParam.cy_end_date},' 23:59:59')
        </if>
        <if test="queryParam.doctorIdList!=null">
            AND (
                a.B22C in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                a.B23C in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                a.B24C in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                a.B25C in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                a.B26C in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                a.B27C in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                a.B28 in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                a.B29C in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                a.B31C in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                a.B32C in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            )
        </if>
    </select>

    <!-- 查询DIP和DRG分组信息 -->
    <select id="queryGroupInfo" resultType="com.my.som.dto.medicalQuality.DrgsCostDayGroupInfo">
        select b.dip_codg AS dipCodg,
               b.DIP_NAME AS dipName,
               IFNULL(b.dip_avg_cost,0) AS dipAvgInHosCost,
               IFNULL(b.dip_avg_days,0) AS dipAvgDays,
               c.drg_codg AS drgCodg,
               c.DRG_NAME AS drgName,
               IFNULL(c.drg_avg_cost,0) AS avgInHosCost,
               IFNULL(c.drg_avg_days,0) AS avgDays
        from som_hi_invy_bas_info a
        left join (
            select a.dip_codg,
                   a.DIP_NAME,
                   a.SETTLE_LIST_ID,
                   ROUND(AES_DECRYPT(UNHEX(b.dip_standard_avg_fee_same_lv),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}),2) as dip_avg_cost,
                   ROUND(AES_DECRYPT(UNHEX(b.dip_standard_ipt_days_same_lv),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}),2) as dip_avg_days
            from som_dip_grp_info a
            left join som_dip_standard b
            on a.dip_codg = b.dip_codg
            and a.HOSPITAL_ID = b.HOSPITAL_ID
            and SUBSTRING(a.dscg_time,1,4) = b.STANDARD_YEAR
            where a.SETTLE_LIST_ID = #{id,jdbcType=INTEGER}
        ) b
       on a.id = b.SETTLE_LIST_ID
       left join (
            select a.drg_codg,
                   a.DRG_NAME,
                   a.SETTLE_LIST_ID,
                   ROUND(AES_DECRYPT(UNHEX(b.standard_avg_fee),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}),2) as drg_avg_cost,
                   ROUND(AES_DECRYPT(UNHEX(b.standard_ave_hosp_day),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}),2) as drg_avg_days
            from som_drg_grp_info a
            left join som_drg_standard b
            on a.drg_codg = b.drg_codg
            and a.HOSPITAL_ID = b.HOSPITAL_ID
            and SUBSTRING(a.dscg_time,1,4) = b.STANDARD_YEAR
            where a.SETTLE_LIST_ID = #{id,jdbcType=INTEGER}
       ) c
       on a.id = c.SETTLE_LIST_ID
       where a.id = #{id,jdbcType=INTEGER}
    </select>

    <select id="selectMedicalDeleteData" resultType="com.my.som.dto.medicalQuality.BusSettleListMainInfo">
        SELECT
        ID AS id
        FROM som_hi_invy_bas_info
        WHERE A48 = #{bah}
          AND SUBSTR(B12,1,10) = #{inHosTime}
          AND SUBSTR(B15,1,10) = #{outHosTime}
    </select>

    <insert id="insertBackDataById">
        INSERT INTO som_dip_grp_bac SELECT * FROM som_dip_grp_info WHERE SETTLE_LIST_ID IN
        <foreach collection="ids" index="index" close=")" separator="," open="(" item="id">
            #{id}
        </foreach>
    </insert>

    <delete id="deleteBusKeyDipById">
        DELETE FROM som_dip_grp_info WHERE SETTLE_LIST_ID IN
        <foreach collection="ids" index="index" close=")" separator="," open="(" item="id">
            #{id}
        </foreach>
    </delete>

    <insert id="insertBusKeyById">
        INSERT INTO som_dip_grp_info SELECT * FROM som_dip_grp_bac WHERE SETTLE_LIST_ID IN
        <foreach collection="ids" index="index" close=")" separator="," open="(" item="id">
            #{id}
        </foreach>
    </insert>

    <delete id="deleteBackDipById">
        DELETE FROM som_dip_grp_bac WHERE SETTLE_LIST_ID IN
        <foreach collection="ids" index="index" close=")" separator="," open="(" item="id">
            #{id}
        </foreach>
    </delete>

    <select id="selectBusSettleLIstError" resultType="com.my.som.vo.medicalQuality.BusSettleListResult">
        SELECT
            err_fld as errorFields,
            err_dscr as errDscr,
            err_type as errType
        from som_invy_chk_detl
        WHERE SETTLE_LIST_ID=#{dto.id}
    </select>

    <select id="selectBusSettleErrorLIst" resultType="java.lang.String">
        select chk_stas as chkStas
        from som_setl_invy_chk
        WHERE SETTLE_LIST_ID=#{dto.id}
    </select>

    <update id="updateSettleList">
        update som_hi_invy_bas_info
        <set>
            <choose>
                <when test='dto.type == "1"'>
                <if test='dto.state=="1"'>
                    <foreach collection="dto.exchange" item="list" index="index" separator=",">
                        ${list.zd}=#{list.after}
                    </foreach>
                </if>
                <if test='dto.state=="2"'>
                    <foreach collection="dto.exchange" item="list" index="index" separator=",">
                        ${list.fld}=#{list.hisVal}
                    </foreach>
                </if>
                </when>
                <when  test='dto.type == "2"'>
                    DATA_LOG_ID=NUll
                </when>
            </choose>
        </set>
        where id=#{dto.id}
    </update>

    <insert id="copyId" useGeneratedKeys="true" keyProperty="id">
        insert into som_hi_invy_bas_info(
        <include refid="com.my.som.dao.dataHandle.BusSettleListDao.busSettleListFields" />
        )
        select <include refid="com.my.som.dao.dataHandle.BusSettleListDao.busSettleListFields" />
        from som_hi_invy_bas_info
        where id=#{dto.id}
    </insert>
    <delete id="deleteDisease">
        delete from som_diag where SETTLE_LIST_ID=#{dto.id}
    </delete>
    <insert id="insertDisease">
        insert into som_diag(
            <include refid="com.my.som.dao.dataHandle.BusDiseaseDiagnosisDao.busDiseaseDiagnosisFields" />
        )VALUES
            <foreach collection="dto.busDiseaseDiagnosisTrimsList" item="list"  separator=",">
                <choose>
                    <when test='list.c06c1 !="" and list.c06c1 !=null and list.c06c1 !="-"'>
                        (#{dto.id},#{list.seq},"1",#{list.c06c1},#{list.c07n1},#{list.c08c1},"")
                    </when>
                    <when test='list.c06c2 !="" and list.c06c2 !=null and list.c06c1 !="-"'>
                        (#{dto.id},#{list.seq},"2",#{list.c06c2},#{list.c07n2},#{list.c08c2},"")
                    </when>
                </choose>
            </foreach>
    </insert>
    <delete id="deleteOperate">
        delete from som_oprn_oprt_info where SETTLE_LIST_ID=#{dto.id}
    </delete>
    <insert id="insertOperate">
        insert into som_oprn_oprt_info(
            <include refid="com.my.som.dao.dataHandle.BusOperateDiagnosisDao.somOprnOprtInfo" />
        )VALUES
        <foreach collection="dto.busOperateDiagnosisList" item="list"  separator=",">
            <choose>
            <when test='list.C35C !="" and list.C35C !=null and list.C35C !="-"'>
            (
             #{dto.id},#{list.seq},#{list.c35c},#{list.c36n},#{list.oprn_oprt_date},#{list.oprn_oprt_lv},#{list.c39c},
             #{list.oprn_oprt_oper_name},#{list.oprn_oprt_1_asit},#{list.oprn_oprt_2_asit},#{list.c42},#{list.c43},#{list.oprn_oprt_anst_dr_code},#{list.oprn_oprt_anst_dr_name},
             #{list.oprnOprtBegntime},#{list.oprnOprtEndtime},#{list.anstBegntime},#{list.anstEndtime}
             )
            </when>
            </choose>
        </foreach>
    </insert>
    <!-- 查询id -->
    <select id="selectBusSettleListId" resultType="java.lang.Long">
        select MAX(ID) from som_hi_invy_bas_info where k00=#{k00}
    </select>
    <!-- 根据settleListID删除数据 -->
    <delete id="deleteBusSettleListById" >
        delete from som_hi_invy_bas_info where ID=#{id}
    </delete>
    <!-- 插入历史数据 -->
    <insert id="insertHistory">
        insert into som_hi_setl_invy_modi_rcd(
            K00,fld,his_val,new_val,STATE,modi_time
        )VALUES
        <foreach collection="dto.exchange" item="list" index="index" separator=",">
            (#{dto.k00},#{list.zd},#{list.old},#{list.after},"1",#{dto.modi_time})
        </foreach>
    </insert>

    <select id="selectHistoryModify" resultType="com.my.som.model.medicalQuality.BusHistoryModify">
        SELECT
            a.K00 AS k00,
            a.fld as fld,
            a.his_val as hisVal,
            a.new_val as newVal,
            a.STATE as state,
            a.modi_time as modi_time
        from som_hi_setl_invy_modi_rcd a
        where a.K00=#{k00}
        ORDER BY modi_time
    </select>

    <update id="updateSettleListByHisState">
        update som_hi_invy_bas_info set HISTORY_TIP_STATE=0,LOOK_OVER=1
        where ID=#{dto.id}
    </update>

    <update id="updateSettleListByHistoryState">
        update som_hi_invy_bas_info set HISTORY_TIP_STATE=1
        where K00=#{k00}
    </update>

    <update id="updateSettleListLookOver">
        update som_hi_invy_bas_info set LOOK_OVER=#{lookOver,jdbcType=VARCHAR}
        where K00=#{k00,jdbcType=VARCHAR}
    </update>

    <delete id="deleteHistoryByK00">
        delete from som_hi_setl_invy_modi_rcd where K00=#{dto.k00} and modi_time=#{dto.modi_time}
    </delete>

    <insert id="insertDiseaseHistory">
        INSERT INTO som_his_diag
            ( SETTLE_LIST_ID, seq, TYPE, dscg_diag_codg, dscg_diag_name, dscg_diag_adm_cond, dscg_diag_dscg_cond, K00, modi_time )
        SELECT
            SETTLE_LIST_ID,
            seq,
            TYPE,
            dscg_diag_codg,
            dscg_diag_name,
            dscg_diag_adm_cond,
            dscg_diag_dscg_cond,
            #{dto.k00},
            #{dto.modi_time}
        FROM som_diag
        where SETTLE_LIST_ID=#{dto.id}
    </insert>

    <select id="selectBusDiseaseHistoryList" resultType="com.my.som.model.dataHandle.SomDiag">
        select
            SETTLE_LIST_ID as settleListId,
            seq as seq,
            TYPE as type,
            dscg_diag_codg as dscg_diag_codg,
            dscg_diag_name as dscg_diag_name,
            dscg_diag_adm_cond as dscg_diag_adm_cond,
            dscg_diag_dscg_cond as dscg_diag_dscg_cond,
            K00 as k00,
            modi_time as modi_time
        from som_his_diag
        where K00=#{k00}
    </select>
    <insert id="insertHistoryDisease">
        INSERT INTO som_diag (
            SETTLE_LIST_ID, seq, TYPE, dscg_diag_codg, dscg_diag_name, dscg_diag_adm_cond, dscg_diag_dscg_cond
        )SELECT
                #{dto.id},seq,TYPE,dscg_diag_codg,dscg_diag_name,dscg_diag_adm_cond,dscg_diag_dscg_cond
        from som_his_diag
        where K00=#{dto.k00} and modi_time=#{dto.modi_time}
    </insert>
    <insert id="insertOperateHistory">
        INSERT INTO som_his_oprn_oprt_info (
            SETTLE_LIST_ID, seq, C35C, C36N, oprn_oprt_date, oprn_oprt_lv, C39C,oprn_oprt_oper_name,oprn_oprt_1_asit,oprn_oprt_2_asit,C42,C43,oprn_oprt_anst_dr_code,oprn_oprt_anst_dr_name, K00, modi_time
        )SELECT
            SETTLE_LIST_ID,seq,C35C,C36N,oprn_oprt_date,oprn_oprt_lv,C39C,oprn_oprt_oper_name,oprn_oprt_1_asit,oprn_oprt_2_asit,C42,C43,oprn_oprt_anst_dr_code,oprn_oprt_anst_dr_name,#{dto.k00},#{dto.modi_time}
        from som_oprn_oprt_info
        where SETTLE_LIST_ID =#{dto.id}
    </insert>
    <insert id="insertHistoryOperate">
        INSERT INTO som_oprn_oprt_info(
            SETTLE_LIST_ID,seq,C35C,C36N,oprn_oprt_date,oprn_oprt_lv,C39C,oprn_oprt_oper_name,oprn_oprt_1_asit,oprn_oprt_2_asit,C42,C43,oprn_oprt_anst_dr_code,oprn_oprt_anst_dr_name
        )SELECT
            #{dto.id},seq,C35C,C36N,oprn_oprt_date,oprn_oprt_lv,C39C,oprn_oprt_oper_name,oprn_oprt_1_asit,oprn_oprt_2_asit,C42,C43,oprn_oprt_anst_dr_code,oprn_oprt_anst_dr_name
        from som_his_oprn_oprt_info
        where K00=#{dto.k00} and modi_time=#{dto.modi_time}
    </insert>

    <update id="updateAllBySettleListId">
        update ${tabName}
        set
        <if test='tabName=="som_diag" or tabName=="som_oprn_oprt_info" or tabName=="som_setl_invy_bld_info"'>
            SETTLE_LIST_ID=#{newId}
        </if>
        <if test='tabName!="som_diag" and tabName!="som_oprn_oprt_info" and tabName!="som_setl_invy_bld_info"'>
            hi_setl_invy_id=#{newId}
        </if>
        where
        <if test='tabName=="som_diag" or tabName=="som_oprn_oprt_info" or tabName=="som_setl_invy_bld_info"'>
            SETTLE_LIST_ID=#{id}
        </if>
        <if test='tabName!="som_diag" and tabName!="som_oprn_oprt_info" and tabName!="som_setl_invy_bld_info"'>
            hi_setl_invy_id=#{id}
        </if>
    </update>
    <update id="updateODBySettleListId">
        update ${tabName}
        set
        <choose>
            <when test='tabName=="som_hi_setl_invy_med_fee_info" or tabName=="som_fund_pay" '>
                hi_setl_invy_id=#{newId}
            </when>
            <when test='tabName!="som_hi_setl_invy_med_fee_info" or tabName!="som_fund_pay"'>
                SETTLE_LIST_ID=#{newId}
            </when>
        </choose>
        where
        <choose>
            <when test='tabName=="som_hi_setl_invy_med_fee_info" or tabName=="som_fund_pay" '>
                hi_setl_invy_id=#{id}
            </when>
            <when test='tabName!="som_hi_setl_invy_med_fee_info" or tabName!="som_fund_pay"'>
                SETTLE_LIST_ID=#{id}
            </when>
        </choose>
    </update>
    <!-- 查询bus_key_dip是否有结果判断流程是否结束 -->
    <select id="selectProcessResult" resultType="com.my.som.vo.medicalQuality.AllErrorVo">
        select COUNT(1) as resNumber from som_dip_grp_info where SETTLE_LIST_ID=#{id}
    </select>

    <!-- 查询输血信息 -->
    <select id="queryBusTransfusionList" resultType="com.my.som.model.medicalQuality.SomSetlInvyBldInfo">
        SELECT  id,
                SETTLE_LIST_ID AS settleListId,
                BLD_CAT AS bldCat,
                BLD_AMT AS bldAmt,
                BLD_UNT AS bldUnt
        FROM som_setl_invy_bld_info
        WHERE SETTLE_LIST_ID = #{id}
    </select>
    <select id="querySettleListOpeLogInfo" resultType="com.my.som.dto.medicalQuality.SettleListOpeLogInfo">
        select USER_NAME as userName, nknm as nknm, crte_time as crteTime,HOSPITAL_ID as hospitalId,K00 as k00,SETTLE_LIST_ID as settleListId,oprt as oprt
        from som_setl_invy_flag_rcd
        where K00 = #{k00}
        order by crteTime desc
    </select>

    <select id="querySettleListOpeLog" resultType="com.my.som.dto.medicalQuality.SettleListOpeLogInfo">
        select USER_NAME as userName, nknm as nknm, crte_time as crteTime,HOSPITAL_ID as hospitalId,K00 as k00,SETTLE_LIST_ID as settleListId,oprt as oprt
        from som_setl_invy_flag_rcd
        where K00 = #{k00,jdbcType=VARCHAR}
    </select>
    <select id="queryAllCountByB16c" resultType="int">
        select count(1)
        from som_hi_invy_bas_info
        where b16c = #{b16c,jdbcType=VARCHAR} and left(B15, 7) = #{b15} and UPLOAD_FLAG = '0' and ACTIVE_FLAG = '1' and HOSPITAL_ID = #{hospitalId}
    </select>
    <select id="queryCaseByB16c" resultType="com.my.som.vo.settleListInfo2.SettleListLookOverVo">
        select a.*, b.NAME as b16n
        from (select count(B16C) as count, B16C as b16c
              from som_hi_invy_bas_info
              where B16C = #{b16c,jdbcType=VARCHAR} and LOOK_OVER = '1' and left(B15, 7) = #{b15} and UPLOAD_FLAG = '0' and ACTIVE_FLAG = '1' and HOSPITAL_ID = #{hospitalId}
              group by B16C
            )a
        left join som_dept b on b.`CODE` = a.B16C
    </select>
    <select id="queryAllHospitalCount" resultType="java.lang.Integer">
        select count(1) from som_hi_invy_bas_info where left(B15, 7) = #{b15} and UPLOAD_FLAG = '0' and ACTIVE_FLAG = '1' and HOSPITAL_ID = #{hospitalId}
    </select>
    <select id="queryLookOverHospitalCount" resultType="java.lang.Integer">
        select count(1) from som_hi_invy_bas_info where left(B15, 7) = #{b15} and LOOK_OVER = '1' and UPLOAD_FLAG = '0' and ACTIVE_FLAG = '1' and HOSPITAL_ID = #{hospitalId}
    </select>
    <select id="lookOverMonthInventoryCount" resultType="java.lang.Integer">
        select count(1) from som_hi_invy_bas_info where left(B15, 7) = #{begnDate} and UPLOAD_FLAG = '1' and ACTIVE_FLAG = '1' and HOSPITAL_ID = #{hospitalId}
    </select>

    <delete id="deletePatientAllTableByIdAndHospitalId">
        <foreach collection="tableNames" item="tabName">
            DELETE FROM ${ tabName } WHERE
                <if test='tabName == "som_otp_slow_special_trt_info" or
                            tabName == "som_hi_setl_invy_med_fee_info" or
                            tabName == "som_fund_pay" or
                            tabName == "som_setl_invy_scs_cutd_info"'>
                    hi_setl_invy_id
                </if>
                <if test='tabName == "som_hi_invy_bas_info"'>
                    ID
                </if>
                <if test='tabName != "som_otp_slow_special_trt_info" and
                            tabName != "som_hi_setl_invy_med_fee_info" and
                            tabName != "som_fund_pay" and
                            tabName != "som_setl_invy_scs_cutd_info" and
                            tabName != "som_hi_invy_bas_info"'>
                    SETTLE_LIST_ID
                </if>
            IN
            <foreach collection="ids" index="index" open="(" separator="," close=")" item="id">
                #{ id }
            </foreach>
            ;
        </foreach>
    </delete>

    <update id="updateDataById">
    <foreach collection="listDtos" item="list" index="index" separator=";">
        update ${tabName}
        <set>
            <foreach collection="list.data" item="listData" index="index" separator=",">
                ${listData.zd}=nullif(#{listData.value},'')
<!--                <if test='tabName=="som_diag"'>-->
<!--                    ,type=#{listData.type}-->
<!--                </if>-->
            </foreach>
        </set>
<!--&#45;&#45;         <if test='tabName!="som_diag"'>-->
            where id=#{list.id}
<!--        </if>-->
<!--        <if test='tabName=="som_diag"'>-->
<!--            where SETTLE_LIST_ID=#{list.id}-->
<!--        </if>-->
    </foreach>
    </update>
<!--  通过settle_listID删除信息  -->
    <delete id="deleteDataBySettleList">
        delete from ${tabName}
        where
      <if test='tabName!="som_otp_slow_special_trt_info"'>
          SETTLE_LIST_ID=#{dto.id}
      </if>
      <if test='tabName=="som_otp_slow_special_trt_info"'>
          hi_setl_invy_id=#{dto.id}
      </if>
    </delete>

    <!-- 删除临时锁数据 -->
    <delete id="deleteLockData">
        delete from som_invy_temp
        where k00 = #{k00,jdbcType=VARCHAR}
    </delete>

    <insert id="insertOutPatient">
        insert into som_otp_slow_special_trt_info(
        hi_setl_invy_id,diag_code,diag_name,oprn_oprt_code,oprn_oprt_name,DEPT_CODE,DEPT_NAME,mdtrt_date
        )VALUES
        <foreach collection="dto.busOutpatientClinicDiagnosisList" item="list" index="index" separator=",">
            (
             #{dto.id},#{list.diagCode},#{list.diagName},#{list.oprnOprtCode},
             #{list.oprnOprtName},#{list.deptCode},#{list.deptName},#{list.mdtrtDate}
             )
        </foreach>
    </insert>
<!--  新增诊断信息  -->
    <insert id="insertBusDisease">
        insert into som_diag(
        SETTLE_LIST_ID,seq,TYPE,dscg_diag_codg,dscg_diag_name,dscg_diag_adm_cond,dscg_diag_dscg_cond,maindiag_flag
        )VALUES
        <foreach collection="busDiseaseDiagnosisTrimList" item="list"  separator=",">
            <choose>
                <when test='list.c06c1 !="" and list.c06c1 !=null and list.c06c1 !="-"'>
                    (#{newId},#{list.seq},#{list.type},#{list.c06c1},#{list.c07n1},#{list.c08c1},#{list.c50c1},#{list.mainDiagFlag})
                </when>
                <when test='list.c06c2 !="" and list.c06c2 !=null and list.c06c1 !="-"'>
                    (#{newId},#{list.seq},#{list.type},#{list.c06c2},#{list.c07n2},#{list.c08c2},#{list.c50c2},#{list.mainDiagFlag})
                </when>
            </choose>
        </foreach>
    </insert>

    <insert id="copyBusSettleList" useGeneratedKeys="true" keyProperty="id">
        insert into som_hi_invy_bas_info(
        <include refid="com.my.som.dao.dataHandle.BusSettleListDao.busSettleListFields" />
        )
        select <include refid="com.my.som.dao.dataHandle.BusSettleListDao.busSettleListFields" />
        from som_hi_invy_bas_info
        where id=#{dto.id}
    </insert>

    <insert id="insertBusOperate">
        insert into som_oprn_oprt_info(
        SETTLE_LIST_ID,seq,C35C,C36N,oprn_oprt_date,oprn_oprt_lv,C39C,oprn_oprt_oper_name,oprn_oprt_1_asit,oprn_oprt_2_asit,C42,C43,oprn_oprt_anst_dr_code,oprn_oprt_anst_dr_name,OPRN_OPRT_BEGNTIME,
        OPRN_OPRT_ENDTIME,ANST_BEGNTIME,ANST_ENDTIME
        )VALUES
        <foreach collection="busOperateDiagnosisList" item="list"  separator=",">
            <choose>
                <when test='list.C35C !="" and list.C35C !=null and list.C35C !="-"'>
                    (
                    #{newId},#{list.seq},#{list.c35c},#{list.c36n},#{list.oprn_oprt_date},#{list.oprn_oprt_lv},#{list.c39c},
                    #{list.oprn_oprt_oper_name},#{list.oprn_oprt_1_asit},#{list.oprn_oprt_2_asit},#{list.c42},#{list.c43},#{list.oprn_oprt_anst_dr_code},#{list.oprn_oprt_anst_dr_name},
                    #{list.oprnOprtBegntime},#{list.oprnOprtEndtime},#{list.anstBegntime},#{list.anstEndtime}
                    )
                </when>
            </choose>
        </foreach>
    </insert>
    <insert id="insertSettleListOpeLog">
        insert into som_setl_invy_flag_rcd
        (USER_NAME, nknm, crte_time, HOSPITAL_ID, K00, SETTLE_LIST_ID, oprt) values (#{userName},#{nknm},now(),#{hospitalId},#{k00},#{id},#{lookOver})
    </insert>
    <insert id="addUpdateLog">
        insert into som_invy_upld_modi_rcd (K00,USER_NAME,updt_time,refer_sco) values (#{k00,jdbcType=VARCHAR},#{userName,jdbcType=VARCHAR},#{updtTime,jdbcType=VARCHAR},#{refer_sco,jdbcType=VARCHAR})
    </insert>

    <!-- 新增临时锁数据 -->
    <insert id="insertLockData">
        INSERT INTO som_invy_temp(k00, USERNAME, IP)
        VALUES(#{k00,jdbcType=VARCHAR}, #{userName,jdbcType=VARCHAR}, #{ip,jdbcType=VARCHAR})
    </insert>

    <update id="updateBusDataLogId">
        update som_hi_invy_bas_info
        set DATA_LOG_ID=NUll
        where id=#{newId}
    </update>
    <update id="UpdateLog">
        UPDATE som_invy_upld_modi_rcd
        <set>
            <if test="userName != null and userName != ''">
                USER_NAME = #{userName,jdbcType=VARCHAR},
            </if>
            <if test="updtTime != null and updtTime != ''">
                updt_time = #{updtTime,jdbcType=VARCHAR},
            </if>
            <if test="refer_sco != null and refer_sco != ''">
                refer_sco = #{refer_sco,jdbcType=VARCHAR}
            </if>
        </set>
        WHERE ID = #{id,jdbcType=VARCHAR}
    </update>
    <select id="MonthInventoryCount" resultType="java.lang.Integer">
        select count(1) from som_hi_invy_bas_info where left(B15, 7) = #{begnDate} and ACTIVE_FLAG = '1' and HOSPITAL_ID = #{hospitalId}
    </select>
    <select id="noUploadCount" resultType="java.lang.Integer">
        select count(1) from som_hi_invy_bas_info where left(B15, 7) = #{begnDate} and ACTIVE_FLAG = '1' and UPLOAD_FLAG = '0' and HOSPITAL_ID = #{hospitalId}
    </select>
    <select id="queryUploadFalseData" resultType="com.my.som.vo.listManagement.SettleListUploadLogVo">
        select
--                substring_index(substring_index(b.err_msg,"：", -1), "-异常流水号", 1) as errMsg,
               b.err_msg as errMsg,
               b.K00 as k00
        from som_hi_invy_bas_info a
        left join som_invy_upld_log b on a.K00 = b.K00
        where b.upld_stas = "0"
          and a.B15 BETWEEN #{begnDate,jdbcType=VARCHAR} AND CONCAT(#{expiDate,jdbcType=VARCHAR},' 23:59:59')
        <if test="hospitalId != null and hospitalId != ''">
            AND a.HOSPITAL_ID = #{hospitalId}
        </if>
    </select>
    <select id="queryUpdateLogByK00" resultType="com.my.som.model.medicalQuality.settleListUpdateVo">
        select ID as id, K00 as k00
        from som_invy_upld_modi_rcd where k00 = #{k00,jdbcType=VARCHAR}
    </select>

    <!-- 查询是否锁住 -->
    <select id="queryLockState" resultType="com.my.som.vo.medicalQuality.SettleListTempLockVo">
        select  K00 AS k00,
                USERNAME AS username,
                IP AS ip
        from som_invy_temp
        where k00 = #{k00,jdbcType=VARCHAR}
    </select>

    <!-- 查询结算清单标识记录 -->
    <select id="querySkipData" resultType="com.my.som.model.dataHandle.SomHiInvyBasInfo">
        select *
        from(
            select id,k00
            from som_hi_invy_bas_info
            where k00 = #{k00,jdbcType=VARCHAR}
            order by id desc
        ) b limit 1
    </select>

    <!-- 通过IP和用户名查询是否锁住 -->
    <select id="queryLockStateByIpUsername" resultType="java.lang.Integer">
        SELECT count(1)
        FROM som_invy_temp
        WHERE k00 = #{k00,jdbcType=VARCHAR}
        AND username = #{userName,jdbcType=VARCHAR}
        AND ip = #{ip,jdbcType=VARCHAR}
    </select>

    <!-- 通过就诊ID查询唯一ID -->
    <select id="queryUniqueIdByMdtrtID" resultType="java.lang.String">
        SELECT UNIQUE_ID
        FROM som_medcas_intf_bas_info
        WHERE MDTRT_ID = #{mdtrtId,jdbcType=VARCHAR}
    </select>

    <select id="selectScore" resultType="java.lang.String">
        SELECT refer_sco FROM ${tabName} WHERE SETTLE_LIST_ID = #{settleListId,jdbcType=VARCHAR}
    </select>

    <!-- 查询上传状态 -->
    <select id="queryUploadState" resultType="java.lang.Integer">
        SELECT count(1)
        FROM som_invy_upld_log
        WHERE k00 = #{k00,jdbcType=VARCHAR}
        AND upld_stas = '1'
    </select>

    <select id="queryNonGroupsList" parameterType="String" resultType="com.my.som.vo.medicalQuality.NonGroupVo">
        SELECT code ,name , type
        FROM som_non_group
        WHERE active_flag ='1'
        <if test="type != null and type != ''">
            and type = #{type,jdbcType=VARCHAR}
        </if>
    </select>

    <select id="codingControlsOnWm"  resultType="com.my.som.dto.listManagement.MainCode">
        SELECT
        *
        FROM
        (
        SELECT
        t1.*,
        CASE
        WHEN t1.crspIcdCodg = t1.middleCode THEN '转码'
        WHEN t1.oprt_rcd IS NOT NULL THEN t1.oprt_rcd
        WHEN t1.originalCode = t1.icdGreyCode THEN '医保停用码或灰码转码'
        WHEN t1.crspIcdCodg = t1.icdGreyCode THEN '医保停用码或灰码转码'
        ELSE '未找到具体原因'
        END AS middleResonance
        FROM
        (
        SELECT
        b.a48 as medcasno,
        b.a11 as patientName,
        c.diag_code as originalCode,
        c.diag_name as originalName,
        a.dscg_diag_codg as middleCode,
        a.dscg_diag_name as middleName,
        d.modi_psn as modiPsn,
        d.oprt_rcd ,
        e.crsp_icd_codg AS crspIcdCodg,
        e.crsp_icd_name,
        f.icd_grey_code AS icdGreyCode,
        g.diag_code as uploadCode,
        g.diag_name as uploadName,
        CASE
        WHEN   g.diag_code = c.diag_code THEN '是'
        WHEN   g.diag_code is null or g.diag_code ='' THEN '未从医保抽取到数据'
        ELSE '否'
        END AS uploadResonance
        FROM
        ( SELECT dscg_diag_codg, dscg_diag_name, settle_list_id FROM som_diag WHERE maindiag_flag = 1 AND seq = 0 ) a
        INNER JOIN som_hi_invy_bas_info b ON a.settle_list_id = b.id
        INNER JOIN ( SELECT diag_code, diag_name, unique_id FROM som_diag_info WHERE maindiag_flag = 1 ) c ON c.unique_id = b.k00
        LEFT JOIN som_hi_invy_supn_info d ON d.k00 = c.unique_id
        LEFT JOIN ( SELECT * FROM som_codg_crsp WHERE icd_type = 'ICD-10' AND crsp_icd_codg_ver = 10 AND icd_codg != crsp_icd_codg ) e ON e.icd_codg = c.diag_code
        LEFT JOIN som_chk_grey_code f ON f.icd_exp_code = a.dscg_diag_codg AND f.type = 2
        LEFT JOIN som_settlement_diseinfo g on b.id=g.setl_id and g.maindiag_flag = 1
        WHERE
        (a.dscg_diag_codg != c.diag_code OR
        (g.diag_code IS NOT NULL AND g.diag_code != c.diag_code))
        AND b.D37 BETWEEN #{begnDate} AND CONCAT(#{expiDate}, ' 23:59:59')
        <if test="medcasCodg != null and medcasCodg != ''">
            AND b.a48 = #{medcasCodg,jdbcType=VARCHAR}
        </if>
        limit #{pageNum} , #{pageSize}
        ) t1
        ) t2
    </select>

    <select id="codingControlsOnOprn"  resultType="com.my.som.dto.listManagement.MainCode">
        SELECT
        *
        FROM
        (
        SELECT
        t1.*,
        CASE
        WHEN t1.crspIcdCodg IS NOT NULL
        AND t1.crspIcdCodg = t1.middleCode THEN
        '转码'
        WHEN t1.oprt_rcd IS NOT NULL THEN
        t1.oprt_rcd
        WHEN t1.icdGreyCode = t1.originalCode THEN
        '医保停用码或灰码转码' ELSE '1'
        END AS middleResonance
        FROM
        (
        SELECT
        b.a48 as medcasno,
        b.a11 as patientName,
        a.c35c as middleCode,
        a.c36n as middleName,
        c.oprn_oprt_code as originalCode,
        c.oprn_oprt_name as originalName,
        d.modi_psn as modiPsn,
        d.oprt_rcd,
        e.crsp_icd_codg  AS crspIcdCodg,
        f.icd_grey_code AS icdGreyCode,
        g.oprn_oprt_code as uploadCode,
        g.oprn_oprt_name as uploadName,
        CASE
        WHEN   g.oprn_oprt_code = c.oprn_oprt_code THEN '是'
        WHEN   g.oprn_oprt_code is null or g.oprn_oprt_code ='' THEN '未从医保抽取到数据'
        ELSE '否'
        END AS uploadResonance
        FROM
        ( SELECT settle_list_id, c35c, c36n FROM som_oprn_oprt_info WHERE seq = 0 ) a
        INNER JOIN som_hi_invy_bas_info b ON a.settle_list_id = b.id
        INNER JOIN ( SELECT unique_id, oprn_oprt_code, oprn_oprt_name FROM som_oprn_rcd WHERE oprn_oprt_sn = 1 ) c ON c.unique_id = b.k00
        LEFT JOIN som_hi_invy_supn_info d ON d.k00 = c.unique_id
        LEFT JOIN ( SELECT * FROM som_codg_crsp WHERE icd_type = 'ICD-9' AND crsp_icd_codg_ver = 10 AND icd_codg != crsp_icd_codg ) e ON e.icd_codg = c.oprn_oprt_code
        LEFT JOIN som_chk_grey_code f ON f.icd_exp_code = a.c35c AND f.type = 3
        LEFT JOIN som_settlement_oprninfo g on b.id=g.setl_id and g.main_oprn_flag = 1
        WHERE
        ( a.c35c != c.oprn_oprt_code OR
        (g.oprn_oprt_code IS NOT NULL AND g.oprn_oprt_code != c.oprn_oprt_code))
        AND b.D37 BETWEEN #{begnDate} AND CONCAT(#{expiDate}, ' 23:59:59')
        <if test="medcasCodg != null and medcasCodg != ''">
            AND b.a48 = #{medcasCodg,jdbcType=VARCHAR}
        </if>

        limit #{pageNum} , #{pageSize}
        ) t1
        ) t1
    </select>

    <select id="codingControlsOnTcm"  resultType="com.my.som.dto.listManagement.MainCode">
        SELECT
        *
        FROM
        (
        SELECT
        t1.*,
        CASE
        WHEN t1.oprt_rcd IS NOT NULL THEN t1.oprt_rcd
        ELSE '未找到具体原因'
        END AS middleResonance
        FROM
        (
        SELECT
        b.a48 as medcasno,
        b.a11 as patientName,
        c.diag_code as originalCode,
        c.diag_name as originalName,
        a.dscg_diag_codg as middleCode,
        a.dscg_diag_name as middleName,
        d.modi_psn as modiPsn,
        d.oprt_rcd,
        g.diag_code as uploadCode,
        g.diag_name as uploadName,
        CASE
        WHEN   g.diag_code = c.diag_code THEN '是'
        WHEN   g.diag_code is null or g.diag_code ='' THEN '未从医保抽取到数据'
        ELSE '否'
        END AS uploadResonance
        FROM
        ( SELECT dscg_diag_codg, dscg_diag_name, settle_list_id FROM som_diag WHERE type = 2 ) a
        INNER JOIN som_hi_invy_bas_info b ON a.settle_list_id = b.id
        INNER JOIN ( SELECT diag_code, diag_name, unique_id FROM som_diag_info WHERE ipt_patn_disediag_type_code = 141 ) c ON c.unique_id = b.k00
        LEFT JOIN som_hi_invy_supn_info d ON d.k00 = c.unique_id
        LEFT JOIN som_settlement_diseinfo g on b.id=g.setl_id and g.diag_type = 2

        WHERE
        (a.dscg_diag_codg != c.diag_code OR
        (g.diag_code IS NOT NULL AND g.diag_code != c.diag_code))

        AND b.D37 BETWEEN #{begnDate} AND CONCAT(#{expiDate}, ' 23:59:59')
        <if test="medcasCodg != null and medcasCodg != ''">
            and b.a48 = #{type,jdbcType=VARCHAR}
        </if>
        limit #{pageNum} , #{pageSize}
        ) t1
        ) t2
    </select>

    <select id="queryCountK00Count" resultType="int">
        select count(*) from som_hi_invy_bas_info where k00 = #{k00}
    </select>

    <select id="queryDiagInfo"  resultType="com.my.som.dto.somDiagInfo.SomDiagInfo">
        SELECT
        CONCAT(code, '【', name, '】') AS diagCodgAndName,
        code AS dscgDiagCodg,
        name AS dscgDiagName
        FROM som_icd_codg
        WHERE
        icd_codg_ver = '10'
        AND icd_type = 'icd-10'
        <if test="query != null">
            AND code LIKE CONCAT(#{query}, '%')
        </if>
    </select>

    <select id="queryDiagNameInfo"  resultType="com.my.som.dto.somDiagInfo.SomDiagInfo">
        SELECT
        CONCAT(code, '【', name, '】') AS diagCodgAndName,
        code AS dscgDiagCodg,
        name AS dscgDiagName
        FROM som_icd_codg
        WHERE
        icd_codg_ver = '10'
        AND icd_type = 'icd-10'
        <if test="query != null">
            AND name LIKE CONCAT(#{query}, '%')
        </if>
    </select>

    <select id="queryOprnInfo"  resultType="com.my.som.dto.somOprnInfo.SomOprnInfo">
        SELECT
        CONCAT(code, '【', name, '】') AS oprnCodeAndName,
        code AS oprnCode,
        name AS oprnName
        FROM som_icd_codg
        WHERE
        icd_codg_ver = '10'
        AND icd_type = 'icd-9'
        <if test="query != null">
            AND code LIKE CONCAT(#{query}, '%')
        </if>
    </select>

    <select id="queryOprnNameInfo"  resultType="com.my.som.dto.somOprnInfo.SomOprnInfo">
        SELECT
        CONCAT(code, '【', name, '】') AS oprnCodeAndName,
        code AS oprnCode,
        name as oprnName
        FROM som_icd_codg
        WHERE
        icd_codg_ver = '10'
        AND icd_type = 'icd-9'
        <if test="query != null">
            AND name LIKE CONCAT(#{query}, '%')
        </if>
    </select>

    <select id="queryHosInfo"  resultType="String">
        select `value` from som_sys_gen_cfg WHERE `key` = 'HOSPITAL_ID'
    </select>


    <select id="querySpecialDisease"  resultType="com.my.som.dto.medicalQuality.BusSettleListMainInfo">

        SELECT
        a.a48, a.a11 , a.a14,
        b.sumfee,b.dise_type as diseType,
        ifnull(a.b20, 0) AS b20,
        ROUND( ifnull(e.total_days,0),2) as totalDays,
        a.SETTLEMENT_ID as settleMentID,
        f.`name` as deptName,
        g.`name` as doctorName,
        h.diagCodeAndName,
        i.oprnCodeAndName,
        <choose>
            <when test="groupType == 1">
                CONCAT(c.dip_codg,'【',c.DIP_NAME,'】') as dipCodeAndName,
                ROUND(IFNULL(AES_DECRYPT(UNHEX(d.dip_standard_ipt_days_same_lv),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}),0),2) as avgDays ,
                ROUND((IFNULL(ROUND(convert(AES_DECRYPT(UNHEX(d.dip_standard_inpf),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) using utf8),2),0)),2) AS avgFee,
                ROUND((IFNULL(ROUND(convert(AES_DECRYPT(UNHEX(d.dip_standard_avg_fee_same_lv),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) using utf8),2),0)),2) AS levelAvgFee,
                ROUND((IFNULL(ROUND(convert(AES_DECRYPT(UNHEX(d.last_year_avg_fee),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) using utf8),2),0)),2) AS lastAvgFee
            </when>
            <when test="groupType == 3">
                CASE
                WHEN a.a54 = 310 OR a.a54 = 1 OR a.a54 = '01'  THEN ifnull(d.adjm_cof,0)
                WHEN a.a54 = 390 OR a.a54 = 2 OR a.a54 = '02'  THEN ifnull(d.adjm_resid_cof,0)
                ELSE ifnull(d.adjm_resid_cof,0) END AS adjm_cof,
                CONCAT(d.drg_codg,'【',d.Drg_NAME,'】') as drgCodeAndName,
                ROUND(AES_DECRYPT(UNHEX(d.standard_ave_hosp_day),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}),2) as avgDays ,
                ROUND((IFNULL(ROUND(convert(AES_DECRYPT(UNHEX(d.standard_avg_fee),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) using utf8),2),0)),2) AS AvgFee
            </when>
        </choose>
        FROM
        som_hi_invy_bas_info a
        <choose>
        <when test="groupType == 1">
        LEFT JOIN som_dip_sco b ON a.id = b.settle_list_id
        LEFT JOIN som_dip_grp_info c ON a.id = c.settle_list_id
        LEFT JOIN som_dip_standard d ON c.dip_codg = d.dip_codg
        AND c.`asst_list_age_grp` = d.`asst_list_age_grp`
        AND c.`asst_list_dise_sev_deg` = d.`asst_list_dise_sev_deg`
        AND c.`asst_list_tmor_sev_deg` = d.`asst_list_tmor_sev_deg`
        AND c.`auxiliary_burn` = d.`auxiliary_burn`
        and a.a01 = d.hospital_id
            <if test="ry_start_date != null and ry_start_date != '' and ry_end_date !=null and ry_end_date != ''">
                <![CDATA[
                                            AND d.STANDARD_YEAR >= SUBSTR(#{ry_start_date,jdbcType=VARCHAR},1,4)
                                              AND d.STANDARD_YEAR <= SUBSTR(#{ry_end_date,jdbcType=VARCHAR},1,4)
                                        ]]>
            </if>
            <if test="cy_start_date != null and cy_start_date != '' and cy_end_date !=null and cy_end_date != ''">
                <![CDATA[
                                            AND d.STANDARD_YEAR >= SUBSTR(#{cy_start_date,jdbcType=VARCHAR},1,4)
                                              AND d.STANDARD_YEAR <= SUBSTR(#{cy_end_date,jdbcType=VARCHAR},1,4)
                                        ]]>
            </if>
            <if test="seStartTime != null and seStartTime != '' and seEndTime !=null and seEndTime != ''">
                <![CDATA[
                                            AND d.STANDARD_YEAR >= SUBSTR(#{seStartTime,jdbcType=VARCHAR},1,4)
                                              AND d.STANDARD_YEAR <= SUBSTR(#{seEndTime,jdbcType=VARCHAR},1,4)
                                        ]]>
            </if>

        </when>
            <when test="groupType == 3">
                LEFT JOIN som_drg_sco b ON a.id = b.settle_list_id
                LEFT JOIN som_drg_standard d ON b.drg_codg = d.drg_codg
                and d.insuplc_admdvs = a.insuplc_admdvs
                <if test="ry_start_date != null and ry_start_date != '' and ry_end_date !=null and ry_end_date != ''">
                    <![CDATA[
                                            AND d.STANDARD_YEAR >= SUBSTR(#{ry_start_date,jdbcType=VARCHAR},1,4)
                                              AND d.STANDARD_YEAR <= SUBSTR(#{ry_end_date,jdbcType=VARCHAR},1,4)
                                        ]]>
                </if>
                <if test="cy_start_date != null and cy_start_date != '' and cy_end_date !=null and cy_end_date != ''">
                    <![CDATA[
                                            AND d.STANDARD_YEAR >= SUBSTR(#{cy_start_date,jdbcType=VARCHAR},1,4)
                                              AND d.STANDARD_YEAR <= SUBSTR(#{cy_end_date,jdbcType=VARCHAR},1,4)
                                        ]]>
                </if>
                <if test="seStartTime != null and seStartTime != '' and seEndTime !=null and seEndTime != ''">
                    <![CDATA[
                                            AND d.STANDARD_YEAR >= SUBSTR(#{seStartTime,jdbcType=VARCHAR},1,4)
                                              AND d.STANDARD_YEAR <= SUBSTR(#{seEndTime,jdbcType=VARCHAR},1,4)
                                        ]]>
                </if>
            </when>
        </choose>
        LEFT JOIN (
        SELECT
        hi_setl_invy_id,
        CAST( SUBSTRING_INDEX( scs_cutd_sum_dura, '/', 1 ) AS DECIMAL ( 10, 2 ) ) + CAST( SUBSTRING_INDEX( SUBSTRING_INDEX( scs_cutd_sum_dura, '/', 2 ), '/', - 1 ) AS DECIMAL ( 10, 2 ) ) / 24 + CAST( SUBSTRING_INDEX( scs_cutd_sum_dura, '/', - 1 ) AS DECIMAL ( 10, 2 ) ) / ( 24 * 60 ) AS total_days
        FROM
        som_setl_invy_scs_cutd_info
        WHERE scs_cutd_sum_dura IS NOT NULL AND scs_cutd_sum_dura != ''
        ) e ON e.hi_setl_invy_id = a.id
        left join som_dept f on a.B16C = f.CODE
        AND a.HOSPITAL_ID = f.HOSPITAL_ID

        left join som_hi_dr_crsp g on a.B51C = g.`oper_dr_code`
        AND a.HOSPITAL_ID = g.HOSPITAL_ID

        LEFT JOIN (
        SELECT c.settle_list_id, GROUP_CONCAT( c.codeAndName ORDER BY c.seq SEPARATOR ';' ) AS diagCodeAndName
        FROM
        ( SELECT c.settle_list_id, CONCAT( c.dscg_diag_codg, '【', c.dscg_diag_name, '】' ) AS codeAndName,c.seq FROM som_diag c) c
        GROUP BY c.settle_list_id
        ) h ON h.settle_list_id = a.id
        LEFT JOIN (
        SELECT d.settle_list_id, GROUP_CONCAT( d.codeAndName ORDER BY d.seq SEPARATOR ';' ) AS oprnCodeAndName
        FROM
        (SELECT d.settle_list_id, CONCAT( d.c35c, '【', d.c36n, '】' ) AS codeAndName, d.seq FROM som_oprn_oprt_info d ) d
        GROUP BY d.settle_list_id
        ) i ON i.settle_list_id = a.id
       <where>
            <if test="ry_start_date!=null and ry_start_date!='' and
                ry_end_date!=null and ry_end_date!=''">
                AND a.b12 BETWEEN #{ry_start_date} and CONCAT(#{ry_end_date},' 23:59:59')
            </if>
            <if test="cy_start_date!=null and cy_start_date!='' and
            cy_end_date!=null and cy_end_date!=''">
                AND a.b15 BETWEEN #{cy_start_date} and CONCAT(#{cy_end_date},' 23:59:59')
            </if>
            <if test="seStartTime!=null and seStartTime!='' and
            seEndTime!=null and  seEndTime!=''">
                AND a.d37 BETWEEN #{seStartTime} and CONCAT(#{seEndTime},' 23:59:59')
            </if>
            <if test="a48!=null and a48!=''">
                AND a.a48 LIKE CONCAT('%',#{a48},'%')
            </if>

       </where>
        </select>
</mapper>
