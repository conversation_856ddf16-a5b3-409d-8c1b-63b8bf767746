<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.som.dao.dictmenagement.DictMenagementDao">
    <resultMap id="BaseResultMap" type="com.my.som.vo.SomSysCode">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="data_val" jdbcType="VARCHAR" property="dataVal" />
        <result column="labl_name" jdbcType="VARCHAR" property="lablName" />
        <result column="code_type" jdbcType="VARCHAR" property="codeType" />
        <result column="dscr" jdbcType="VARCHAR" property="dscr" />
        <result column="srt" jdbcType="DECIMAL" property="srt" />
        <result column="crter" jdbcType="BIGINT" property="crter" />
        <result column="crte_time" jdbcType="TIMESTAMP" property="crteTime" />
        <result column="updt_psn" jdbcType="BIGINT" property="updtPsn" />
        <result column="updt_time" jdbcType="TIMESTAMP" property="updtTime" />
        <result column="memo_info" jdbcType="VARCHAR" property="memo_info" />
        <result column="is_del" jdbcType="TINYINT" property="isDel" />
    </resultMap>
    <sql id="Base_Column_List">
        id, data_val, labl_name, code_type, dscr, srt, crter, crte_time, updt_psn,
        updt_time, memo_info, is_del
    </sql>
    <select id="getList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from som_sys_code
        where is_del = 0
        order by code_type,srt
    </select>

    <select id="getSettleListDictList" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM som_sys_dic
        WHERE is_del = 0
        ORDER BY code_type,srt
    </select>


    <select id="getChargeDetail" resultType="com.my.som.model.medicalQuality.BusFeeBreakDown">
        SELECT
        a.k00 as uniqueId,
        GROUP_CONCAT(DISTINCT substr(b.med_list_codg, 1, 15) SEPARATOR ';') AS medListCodg

        FROM
        som_hi_invy_bas_info a
        LEFT JOIN
        som_chrg_detl_intf b ON a.k00 = b.unique_id
        WHERE
        LEFT(b.med_list_codg, 2) = '00'
        AND a.d37 BETWEEN #{seStartTime} AND CONCAT(#{seEndTime}, ' 23:59:59')
        GROUP BY
        a.id
    </select>

    <select id="getChargeUniqueId" resultType="String">
        SELECT
        a.k00 as uniqueId
        FROM
        som_hi_invy_bas_info a
        LEFT JOIN
        som_chrg_detl_intf b ON a.k00 = b.unique_id
        WHERE
        LEFT(b.med_list_codg, 2) = '00'
        AND a.d37 BETWEEN #{seStartTime} AND CONCAT(#{seEndTime}, ' 23:59:59')
        GROUP BY
        a.id
    </select>
</mapper>