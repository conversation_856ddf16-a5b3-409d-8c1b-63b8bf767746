<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.my.som.dao.dipBusiness.DipDeptCostEarlyWarningDao">

    <!-- 获取数据 -->
    <select id="getList" resultType="com.my.som.vo.dipBusiness.DipDeptCostEarlyWarningVo">
        SELECT e.*
        FROM
             (
                 SELECT b.*,
                        CASE WHEN c.`NAME` IS NULL THEN '未填写科室' ELSE c.`NAME` END AS deptName,
                        <![CDATA[
                        CASE WHEN b.balanceRate <= -10 THEN 1
                             WHEN b.balanceRate <= -1 THEN 2
                             WHEN b.balanceRate > -1 AND b.balanceRate <= 1 THEN 3
                             WHEN b.balanceRate > 1 THEN 4
                             ELSE 3 END AS rate
                        ]]> <!-- 盈亏比区间 默认3可能情况为 -0.0 -->
                 FROM
                      (
                          SELECT d.deptCode,
                                 d.hospitalId,
                                 COUNT(1) AS groupCount,
                                 SUM(d.pelCount) AS allCount,
                                 SUM(d.lowNum) AS lowNum,
                                 SUM(d.upNum) AS upNum,
                                 ROUND((SUM(d.forecastCost)-SUM(d.sumfee)-SUM(d.preHospExamfee)),2) AS balanceCost,
                                 ROUND(((SUM(d.forecastCost)-SUM(d.sumfee)-SUM(d.preHospExamfee))/SUM(d.sumfee) * 100),2) AS balanceRate
                          FROM
                               (
                                   SELECT c.dipCodg,
                                          c.deptCode,
                                          c.hospitalId,
                                          COUNT(1) AS pelCount,
                                          COUNT(c.lowNum) AS lowNum,
                                          COUNT(c.upNum) AS upNum,
                                          SUM(c.sumfee) AS sumfee,
                                          SUM(c.forecastCost) as forecastCost,
                                          SUM(c.preHospExamfee) as preHospExamfee
                                   FROM
                                        (
                                            SELECT a.dipCodg,
                                                   a.deptCode,
                                                   a.sumfee,
                                                   a.hospitalId,
                                                 a.ycCost AS forecastCost,
                                                a.preHospExamfee as preHospExamfee,
                                                    a.lowNum,
                                                   a.upNum
                                            FROM
                                                 (
                                                     SELECT
                                                       a.dscg_caty_codg_inhosp AS deptCode,
                                                            a.HOSPITAL_ID AS hospitalId,
                                                    IFNULL(a.pre_hosp_examfee,0) AS preHospExamfee,
                                                     <choose>
                                                         <when test="feeStas == 0">
                                                             a.dip_codg AS dipCodg,
                                                             IFNULL(a.ipt_sumfee,0) AS sumfee,
                                                             c.SETTLE_LIST_ID,
                                                             c.dise_type,
                                                             c.insu_type AS insuType,
                                                             IFNULL(c.totl_sco,0) AS totlSco,

                                                             c.price As price,
                                                             c.forecast_fee AS ycCost,
                                                             c.profitloss AS profitloss,
                                                             CASE WHEN c.dise_type = 1 THEN 1 ELSE NULL END AS upNum,
                                                             CASE WHEN c.dise_type = 2 THEN 2 ELSE NULL END AS lowNum
                                                         </when>
                                                         <when test="feeStas == 1">
                                                             y.dip_codg AS dipCodg,
                                                             y.INSURED_TYPE AS insuType,
                                                             y.ycCost,
                                                             IFNULL(y.ipt_sumfee,0) AS sumfee,
                                                             CASE WHEN y.dise_type = 1 THEN 1 ELSE NULL END AS upNum,
                                                             CASE WHEN y.dise_type = 2 THEN 2 ELSE NULL END AS lowNum
                                                         </when>
                                                     </choose>
                                                     FROM som_dip_grp_info a
                                                     <if test='enableSeAns != null and enableSeAns != "" and enableSeAns == "1"'>
                                                         LEFT JOIN som_hi_invy_bas_info q
                                                         ON q.ID = a.SETTLE_LIST_ID
                                                         INNER JOIN som_setl_cas_crsp p
                                                         ON q.k00 = p.k00
                                                     </if>
                                                     LEFT JOIN som_dip_sco c
                                                         ON a.SETTLE_LIST_ID = c.SETTLE_LIST_ID
                                                     LEFT JOIN som_dip_grp_rcd e
                                                         ON a.SETTLE_LIST_ID = e.SETTLE_LIST_ID
                                                        AND a.is_used_asst_list = e.used_asst_list
                                                        AND a.asst_list_age_grp = e.asst_list_age_grp
                                                        AND a.asst_list_dise_sev_deg = e.asst_list_dise
                                                        AND a.asst_list_tmor_sev_deg = e.asst_list_tmor_sev_deg
                                                         AND a.HOSPITAL_ID = e.HOSPITAL_ID


                                                     <if test="feeStas == 1">
                                                         INNER JOIN
                                                         (
                                                         SELECT
                                                         a.dip_codg,
                                                         a.DIP_NAME,
                                                         a.rid_idt_codg,
                                                         a.medcas_codg,
                                                         a.adm_time,
                                                         a.dscg_time,
                                                         a.is_in_group,
                                                         b.sumfee AS ipt_sumfee,
                                                         b.INSURED_TYPE,
                                                         b.MED_TYPE AS dise_type,
                                                         b.setl_pt_val,
                                                         b.dfr_fee AS ycCost,
                                                         b.MED_TYPE,
                                                         b.setl_sco,
                                                         a.used_asst_list,
                                                         a.asst_list_age_grp,
                                                         a.asst_list_dise_sev_deg,
                                                         a.asst_list_tmor_sev_deg
                                                         FROM som_dip_grp_fbck a
                                                         LEFT JOIN som_fund_dfr_fbck b
                                                         ON a.rid_idt_codg = b.rid_idt_codg
                                                         WHERE 1=1
                                                         <if test="begnDate != null and begnDate != '' and expiDate != null and expiDate != ''">
                                                             AND a.dscg_time BETWEEN #{begnDate,jdbcType=VARCHAR} AND concat(#{expiDate,jdbcType=VARCHAR},' 23:59:59')
                                                         </if>
                                                         <if test="inStartTime != null and inStartTime != '' and inEndTime != null and inEndTime != ''">
                                                             AND a.adm_time BETWEEN #{inStartTime} AND CONCAT(#{inEndTime},' 23:59:59')
                                                         </if>
                                                         <if test="seStartTime!=null and seStartTime!='' and seEndTime!=null and seEndTime!=''">
                                                             AND a.setl_end_time BETWEEN #{seStartTime} and CONCAT(#{seEndTime},' 23:59:59')
                                                         </if>
                                                         ) y
                                                     ON a.PATIENT_ID = y.medcas_codg
                                                     AND SUBSTR(a.adm_time,1,10) = y.adm_time
                                                     AND SUBSTR(a.dscg_time,1,10) = y.dscg_time
                                                     </if>
                                                     WHERE 1=1
                                                       <include refid="com.my.som.mapper.newBusiness.common.NewBusinessCommonAnalysisMapper.queryBusCommon"></include>
                                                     <if test="inStartTime != null and inStartTime != '' and
                                                               inEndTime != null and inEndTime != ''">
                                                         AND a.adm_time BETWEEN #{inStartTime} AND CONCAT(#{inEndTime},' 23:59:59')
                                                     </if>
                                                     <if test="(begnDate != null and begnDate != '' and expiDate != null and expiDate != '') or inHosFlag == 1">
                                                       <![CDATA[
                                                       AND a.dscg_time BETWEEN #{begnDate} AND CONCAT(#{expiDate},' 23:59:59')
                                                       ]]>
                                                     </if>
                                                     <if test="seStartTime!=null and seStartTime!='' and seEndTime!=null and seEndTime!=''">
                                                         AND a.setl_end_time BETWEEN #{seStartTime} and CONCAT(#{seEndTime},' 23:59:59')
                                                     </if>
                                                 ) a

                                        ) c
                                    WHERE c.dipCodg IS NOT NULL AND c.dipCodg != ''
                                   GROUP BY c.dipCodg,
                                            c.deptCode,
                                            c.hospitalId
                               ) d
                          GROUP BY d.deptCode,d.hospitalId

                      ) b
                 LEFT JOIN som_dept c
                     ON b.deptCode = c.`CODE`
                        AND b.hospitalId = c.HOSPITAL_ID
                 ORDER BY balanceRate
             ) e
        <where>
        <if test="rate != null and rate != '' and rate != 0">
              e.rate = #{rate,jdbcType=VARCHAR}
        </if>
            and e.deptName != '未填写科室'
        </where>
    </select>
</mapper>
