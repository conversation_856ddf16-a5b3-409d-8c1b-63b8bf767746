<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.som.dao.dipBusiness.DipDeptAnalysisDao">
    <select id="selectDeptData" resultType="com.my.som.vo.dipBusiness.DipDeptAnalysisVo">
        SELECT a.*,
               IFNULL(b.dominantDiseaseNum,0) AS dominantDiseaseNum,
               IFNULL(c.baseDiseaseNum,0) AS baseDiseaseNum,
               IFNULL(d.youngerDiseaseNum,0) AS youngerDiseaseNum,
               IFNULL(professionalDiseaseNum,0) AS professionalDiseaseNum
        FROM
             (
                 SELECT a.dscg_caty_codg_inhosp AS deptCode,
                        IFNULL(b.`NAME`,'未填写科室') AS deptName,
                        COUNT(1) AS totalNum
                 FROM som_dip_grp_info a
                 LEFT JOIN som_dept b
                     ON a.dscg_caty_codg_inhosp = b.`CODE`
                 AND a.HOSPITAL_ID = b.HOSPITAL_ID
                 WHERE a.dip_codg IS NOT NULL
        <if  test="hospitalId != null and hospitalId != ''">
                   AND a.HOSPITAL_ID = #{hospitalId}
        </if>
        AND a.dscg_time BETWEEN #{begnDate} AND #{expiDate}
                 GROUP BY a.dscg_caty_codg_inhosp,
                          b.`NAME`
             ) a
        LEFT JOIN
                 (
                     SELECT a.dscg_caty_codg_inhosp,
                            COUNT(1) AS dominantDiseaseNum
                     FROM som_dip_grp_info a
                     LEFT JOIN som_hi_invy_bas_info b
                         ON a.SETTLE_LIST_ID = b.ID
                     RIGHT JOIN
                         (
                             select a.ID AS id,
                                    a.tcm_dise_codg AS chineseDiseaseCode,
                                    a.tcm_dise_name AS chineseDiseaseName,
                                    a.hi_codg AS medicalCode,
                                    a.hi_name AS medicalName
                             from som_hi_tcm_codg_crsp a
                             where a.active_flag = '1'
                         ) c
        ON b.C03C = c.medicalCode
        AND b.C37C = c.chineseDiseaseCode
        WHERE a.dip_codg IS NOT NULL
          <include
                  refid="com.my.som.mapper.newBusiness.common.NewBusinessCommonAnalysisMapper.queryBusCommon"></include>
        AND a.dscg_time BETWEEN #{begnDate} AND #{expiDate}
        AND (b.D22/b.D01) >= 0.5
        GROUP BY a.dscg_caty_codg_inhosp
                 ) b
        ON a.deptCode = b.dscg_caty_codg_inhosp
        LEFT JOIN
        (
        SELECT a.dscg_caty_codg_inhosp,
        COUNT(1) AS baseDiseaseNum
        FROM som_dip_grp_info a
        LEFT JOIN som_hosp_info b
        ON a.HOSPITAL_ID = b.HOSPITAL_ID
        RIGHT JOIN
        (
        select a.ID AS id,
        a.dip_codg AS dipCodg,
        a.dip_diag_codg_dis_gp AS dipDiagCodg,
        a.dip_diag_name_dis_gp AS dipDiagName,
        a.dip_oprt_codg AS dipOprtCodg,
        a.dip_oprt_name AS dipOprtName,
        a.is_used_asst_list_grp_rcd AS usedAsstList,
        a.is_sd_dise_grp_list AS isStabilizeDisease,
        a.dise_type AS diseType
        from som_dip_grp_detl a
        where a.active_flag = '1'
        ) c
        ON a.dip_codg = c.dipCodg
        WHERE a.dip_codg IS NOT NULL
        <if  test="hospitalId != null and hospitalId != ''">
        AND a.HOSPITAL_ID = #{hospitalId}
        </if>
        <![CDATA[
          AND b.hosp_lv >= 4
                    ]]>
        AND a.dscg_time BETWEEN #{begnDate} AND #{expiDate}
        GROUP BY a.dscg_caty_codg_inhosp
            ) c
            ON a.deptCode = c.dscg_caty_codg_inhosp
            LEFT JOIN
            (
            SELECT dscg_caty_codg_inhosp,
            COUNT(1) AS youngerDiseaseNum
            from som_dip_grp_info
            WHERE dip_codg IS NOT NULL
        <if  test="hospitalId != null and hospitalId != ''">
          AND HOSPITAL_ID = #{hospitalId}
        </if>
        <![CDATA[
                   AND AGE < '14'
                    ]]>
          AND dscg_time BETWEEN #{begnDate} AND #{expiDate}
        GROUP BY dscg_caty_codg_inhosp
        ) d
        ON a.deptCode = d.dscg_caty_codg_inhosp
        LEFT JOIN
        (
        SELECT a.dscg_caty_codg_inhosp,
        COUNT(1) AS professionalDiseaseNum
        FROM som_dip_grp_info a
        RIGHT JOIN
        (
        select a.ID AS id,
        a.inhosp_dept_codg AS inhospDeptCodg,
        a.inhosp_dept_name AS inhospDeptName,
        a.key_spcy_name AS keySpcyName,
        a.key_spcy_type AS keySpcyType
        from som_dip_key_spcy_info a
        where a.active_flag = '1'
        ) b
        ON a.dscg_caty_codg_inhosp = b.inhospDeptCodg
        WHERE a.dip_codg IS NOT NULL
        <if  test="hospitalId != null and hospitalId != ''">
        AND a.HOSPITAL_ID = #{hospitalId}
        </if>
          AND a.dscg_time BETWEEN #{begnDate} AND #{expiDate}
        GROUP BY a.dscg_caty_codg_inhosp
        ) e
        ON a.deptCode = e.dscg_caty_codg_inhosp
        <where>
        <if test="deptCode != null and deptCode != ''">
          AND a.deptCode = #{deptCode}
        </if>
        </where>
    </select>
</mapper>