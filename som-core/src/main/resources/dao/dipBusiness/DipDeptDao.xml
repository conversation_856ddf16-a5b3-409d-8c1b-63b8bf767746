<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.som.dao.dipBusiness.DipDeptDao">
    <select id="list" resultType="com.my.som.vo.dipBusiness.DipDeptIndexVo">
        select a.*,
               b.NAME as priOutHosDeptName
        from
        (select
            x.dscg_caty_codg_inhosp AS priOutHosDeptCode,
            x.HOSPITAL_ID,
            COUNT(1) AS medicalRecordNum,
            COUNT(case when x.grp_stas = '1' then 1 else null end) AS groupNum,
            COUNT(case when x.grp_stas != '1' then 1 else null end) AS noGroupNum,
            IFNULL(count(distinct (case when x.grp_stas = '1' then x.dip_codg else null end)),0) AS dipGroupNum,
            IFNULL(ROUND(SUM(x.dip_wt), 4),0) AS totalAreaWeight,
            IFNULL(ROUND(SUM(x.hosWeight), 4),0) AS totalHosWeight,
            ROUND(AVG(x.act_ipt), 2) AS avgDays, <!--平均住院日-->
            ROUND(AVG(x.ipt_sumfee), 2) AS avgCost, <!--平均住院费用-->
            ROUND(sum(x.profitloss), 2) AS profitlossTotal, <!--预测金额差异-->
            <!--  时间消耗指数   -->
            ROUND(IFNULL(sum(IFNULL(CASE WHEN x.grp_stas = '1' then x.act_ipt else 0 end,0)/NULLIF(CASE WHEN x.grp_stas = '1' then x.dip_standard_ipt_days_same_lv else 0 end, 0))/COUNT(1),0),2) as timeIndex,
            <!--  费用消耗指数   -->
            ROUND(IFNULL(sum(IFNULL(CASE WHEN x.grp_stas = '1' then x.ipt_sumfee else 0 end,0)/NULLIF(CASE WHEN x.grp_stas = '1' then x.dip_standard_avg_fee_same_lv else 0 end, 0))/COUNT(1),0),2) as costIndex,
            IFNULL(CONCAT(ROUND(SUM(x.drugfee)/NULLIF(SUM(x.ipt_sumfee),0)*100,2),'%'),0) AS medicalCostRate,<!--  药品费占比   -->
            IFNULL(CONCAT(ROUND(SUM(x.mcs_fee)/NULLIF(SUM(x.ipt_sumfee),0)*100,2),'%'),0) AS materialCostRate <!--  耗材费占比   -->
        from(
            select
                m.*,
                n.hosWeight AS hosWeight
            from(
                select
                    SUBSTR(b.dscg_time,1,4) as year,
                    b.dscg_caty_codg_inhosp AS dscg_caty_codg_inhosp,
                    IFNULL(b.dscg_caty_name_inhosp,'未填写科室') AS dscg_caty_name_inhosp,
                    b.HOSPITAL_ID,
                    a.grp_stas as grp_stas,
                    a.dip_codg as dip_codg,
                    c.dip_wt as dip_wt,
                    b.act_ipt as act_ipt,
                    b.ipt_sumfee as ipt_sumfee,
                    convert(AES_DECRYPT(UNHEX(c.dip_standard_ipt_days_same_lv),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) using utf8) as dip_standard_ipt_days_same_lv,
                    convert(AES_DECRYPT(UNHEX(c.dip_standard_avg_fee_same_lv),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) using utf8) as dip_standard_avg_fee_same_lv,
                    convert(AES_DECRYPT(UNHEX(c.last_year_avg_fee),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) using utf8) as last_year_avg_fee,
                    b.drugfee as drugfee,
                    b.mcs_fee as mcs_fee,
                    d.profitloss
                from som_dip_grp_info b
                <if test='queryParam.enableSeAns != null and queryParam.enableSeAns != "" and queryParam.enableSeAns == "1"'>
                    LEFT JOIN som_hi_invy_bas_info q
                    ON q.ID = b.SETTLE_LIST_ID
                    INNER JOIN som_setl_cas_crsp p
                    ON q.k00 = p.k00
                </if>
                inner join som_dip_grp_rcd a on a.SETTLE_LIST_ID = b.SETTLE_LIST_ID
                left join som_dip_sco d on a.SETTLE_LIST_ID = d.SETTLE_LIST_ID
                left join som_dip_standard c on a.dip_codg=c.dip_codg AND SUBSTR(ifnull(b.setl_end_time,b.dscg_time),1,4) = c.STANDARD_YEAR
                AND ifnull(c.asst_list_age_grp,'未使用') = ifnull(b.asst_list_age_grp,'未使用')
                AND ifnull(c.asst_list_dise_sev_deg,'未使用') = ifnull(b.asst_list_dise_sev_deg,'未使用')
                AND ifnull(c.asst_list_tmor_sev_deg,'未使用') = ifnull(b.asst_list_tmor_sev_deg,'未使用')
                AND ifnull(c.auxiliary_burn,'未使用') = ifnull(b.auxiliary_burn,'未使用')
                AND b.HOSPITAL_ID = c.HOSPITAL_ID
                where 1 = 1
                <if test="queryParam.dipGroup != null and queryParam.dipGroup != ''">
                    and b.dip_codg = #{queryParam.dipGroup}
                </if>
                <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
                    AND  a.HOSPITAL_ID = #{queryParam.hospitalId}
                </if>
                <if test="queryParam.b16c!=null and queryParam.b16c!=''">
                    AND b.dscg_caty_codg_inhosp = #{queryParam.b16c}
                </if>
                <if test="queryParam.inStartTime!=null and queryParam.inStartTime!='' and
                        queryParam.inEndTime!=null and queryParam.inEndTime != ''">
                    AND b.adm_time BETWEEN #{queryParam.inStartTime} and CONCAT(#{queryParam.inEndTime},' 23:59:59')
                </if>
                <if test="(queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and
                 queryParam.cy_end_date!=null and queryParam.cy_end_date!='') or queryParam.inHosFlag == 2">
                    AND b.dscg_time BETWEEN #{queryParam.cy_start_date} and CONCAT(#{queryParam.cy_end_date},' 23:59:59')
                </if>
                <if test="(queryParam.seStartTime!=null and queryParam.seStartTime!='' and
                         queryParam.seEndTime!=null and queryParam.seEndTime!='') or queryParam.inHosFlag == 2">
                    AND b.setl_end_time BETWEEN #{queryParam.seStartTime} and CONCAT(#{queryParam.seEndTime},' 23:59:59')
                </if>
                <if test="queryParam.doctorIdList!=null">
                    AND (
                    b.deptdrt_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    b.chfdr_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    b.atddr_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    b.ipdr_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    b.resp_nurs_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    b.train_dr_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    b.intn_dr in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    b.codr_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    b.qltctrl_dr_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    b.qltctrl_nurs_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    )
                </if>
            )m left join (
                select a.dip_codg,a.HOSPITAL_ID,
                       IFNULL( AVG( a.ipt_sumfee )/ NULLIF( convert ( AES_DECRYPT( UNHEX( MAX(c.AREA_AVG_COST) ), ${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) using utf8 ), 0 ), 0 ) AS hosWeight
				from som_dip_grp_info a
				left join som_dip_grp_rcd b on a.SETTLE_LIST_ID = b.SETTLE_LIST_ID
				left JOIN som_regn_year_sum_data c on SUBSTR( a.dscg_time, 1, 4 ) = c.STANDARD_YEAR
				where b.grp_stas = '1'
                <if test="queryParam.hospitalId != null and queryParam.hospitalId != ''">
				  AND a.HOSPITAL_ID = #{queryParam.hospitalId}
                </if>
                <if test="queryParam.dipGroup != null and queryParam.dipGroup != ''">
                    and a.dip_codg = #{queryParam.dipGroup}
                </if>
                <if test="queryParam.inStartTime!=null and queryParam.inStartTime!='' and
                        queryParam.inEndTime!=null and queryParam.inEndTime != ''">
                    AND a.adm_time BETWEEN #{queryParam.inStartTime} and CONCAT(#{queryParam.inEndTime},' 23:59:59')
                </if>
				<if test="(queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and
                 queryParam.cy_end_date!=null and queryParam.cy_end_date!='') or queryParam.inHosFlag == 2">
                    AND a.dscg_time BETWEEN #{queryParam.cy_start_date} and CONCAT(#{queryParam.cy_end_date},' 23:59:59')
                </if>
                <if test="(queryParam.seStartTime!=null and queryParam.seStartTime!='' and
                         queryParam.seEndTime!=null and queryParam.seEndTime!='') or queryParam.inHosFlag == 2">
                    AND a.setl_end_time BETWEEN #{queryParam.seStartTime} and CONCAT(#{queryParam.seEndTime},' 23:59:59')
                </if>
				group by a.dip_codg,a.HOSPITAL_ID
            ) n on m.dip_codg = n.dip_codg AND m.HOSPITAL_ID = n.HOSPITAL_ID
        )x
        GROUP BY x.dscg_caty_codg_inhosp,x.HOSPITAL_ID
        ORDER BY totalHosWeight desc
        ) a
            left join som_dept b
                on a.priOutHosDeptCode = b.CODE
                and a.HOSPITAL_ID = b.HOSPITAL_ID
            where b.NAME is not null and b.NAME != ''
    </select>

    <select id="getCountInfo" resultType="com.my.som.vo.dipBusiness.DipDeptCountVo">
        select
            x.dscg_caty_codg_inhosp AS priOutHosDeptCode,
            IFNULL(x.dscg_caty_name_inhosp,'未填写科室') AS priOutHosDeptName,
            IFNULL(count(1),0) AS totalMedicalRecordNum,
            IFNULL(ROUND(count(1) / NULLIF(y.totalMedicalNum,0)*100,2),0) AS totalMedicalRecordNumRate,
            IFNULL(count(case when x.grp_stas = '1' then 1 else null end),0)   AS inGroupMedicalNum,
            IFNULL(ROUND(count(case when x.grp_stas = '1' then 1 else null end) /
              NULLIF(y.inGroupMedicalNum,0)*100,2),0) AS  inGroupMedicalNumRate,
            IFNULL(count(case when x.grp_stas != '1' then 1 else null end),0)  AS notGroupMedicalNum,
            IFNULL(ROUND(count(case when x.grp_stas != '1' then 1 else null end) /
              NULLIF(y.inGroupMedicalNum,0)*100,2),0) AS  notGroupMedicalNumRate,
            IFNULL(count(distinct (case when x.grp_stas = '1' then x.dip_codg else null end)),0)   AS dipGroupNum,
            IFNULL(ROUND(count(distinct (case when x.grp_stas = '1' then x.dip_codg else null end)) /
              NULLIF(y.dipGroupNum,0)*100,2),0) AS  dipGroupNumRate,
            IFNULL(ROUND(AVG(x.act_ipt), 2),0)   AS avgDays,
            y.hosAvgDays as hosAvgDays,
            IFNULL(ROUND(AVG(CASE WHEN x.grp_stas = '1' THEN x.act_ipt ELSE NULL END), 2),0)   AS inGroupAvgDays,
            y.hosInGroupAvgDays as hosInGroupAvgDays,
            IFNULL(ROUND(AVG(x.ipt_sumfee), 2),0)  AS avgCost,
            y.hosAvgCost as hosAvgCost,
            IFNULL(ROUND(AVG(CASE WHEN x.grp_stas = '1' THEN x.ipt_sumfee ELSE NULL END), 2),0)    AS inGroupAvgCost,
            y.hosInGroupAvgCost as hosInGroupAvgCost,
            IFNULL(ROUND(SUM(CASE WHEN x.grp_stas = '1' THEN x.act_ipt ELSE NULL END / NULLIF(x.dip_standard_ipt_days,0)) /
            NULLIF(COUNT(CASE WHEN x.grp_stas = '1' THEN 1 ELSE NULL END),0), 2),0)  AS timeIndex,
            y.hosTimeIndex as hosTimeIndex,
            IFNULL(ROUND(SUM(CASE WHEN x.grp_stas = '1' THEN x.ipt_sumfee ELSE NULL END / NULLIF(x.dip_standard_inpf,0)) /
            NULLIF(COUNT(CASE WHEN x.grp_stas = '1' THEN 1 ELSE NULL END),0), 2),0)   AS costIndex,
            y.hosCostIndex as hosCostIndex,
            IFNULL(ROUND(AVG(x.drugfee), 2),0)   AS avgDrugFee,
            y.hosAvgMedicalCost as hosAvgMedicalCost,
            IFNULL(ROUND(AVG(x.mcs_fee), 2),0)   AS avgMcsFee,
            y.hosAvgMaterialCost as hosAvgMaterialCost
        from (
            select
                b.dscg_caty_codg_inhosp as dscg_caty_codg_inhosp,
                d.`NAME` AS dscg_caty_name_inhosp,
                a.grp_stas as grp_stas,
                a.dip_codg as dip_codg,
                b.act_ipt as act_ipt,
                b.ipt_sumfee as ipt_sumfee,
                convert(AES_DECRYPT(UNHEX(c.dip_standard_ipt_days),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) using utf8) as dip_standard_ipt_days,
                convert(AES_DECRYPT(UNHEX(c.dip_standard_inpf),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) using utf8) as dip_standard_inpf,
                b.drugfee as drugfee,
                b.mcs_fee as mcs_fee
            from som_dip_grp_rcd a
            inner join som_dip_grp_info b on a.SETTLE_LIST_ID = b.SETTLE_LIST_ID
            <if test='queryParam.enableSeAns != null and queryParam.enableSeAns != "" and queryParam.enableSeAns == "1"'>
                LEFT JOIN som_hi_invy_bas_info q
                ON q.ID = b.SETTLE_LIST_ID
                INNER JOIN som_setl_cas_crsp p
                ON q.k00 = p.k00
            </if>
             AND a.used_asst_list = b.is_used_asst_list
             AND a.asst_list_age_grp = b.asst_list_age_grp
             AND a.asst_list_dise = b.asst_list_dise_sev_deg
             AND a.asst_list_tmor_sev_deg = b.asst_list_tmor_sev_deg
            left join som_dip_standard c on a.dip_codg=c.dip_codg AND SUBSTR(b.dscg_time,1,4) = c.STANDARD_YEAR
             AND a.used_asst_list = c.is_used_asst_list
             AND a.asst_list_age_grp = c.asst_list_age_grp
             AND a.asst_list_dise = c.asst_list_dise_sev_deg
             AND a.asst_list_tmor_sev_deg = c.asst_list_tmor_sev_deg
             AND a.auxiliary_burn = c.auxiliary_burn
                AND a.HOSPITAL_ID = c.HOSPITAL_ID
            LEFT JOIN som_dept d
            ON b.dscg_caty_codg_inhosp = d.`CODE`
            AND b.HOSPITAL_ID = d.HOSPITAL_ID
            where 1 = 1
            <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
                AND  a.HOSPITAL_ID = #{queryParam.hospitalId}
            </if>
            <if test="queryParam.b16c!=null and queryParam.b16c!=''">
                AND b.dscg_caty_codg_inhosp = #{queryParam.b16c}
            </if>
            <if test="queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and
            queryParam.cy_end_date!=null and queryParam.cy_end_date!=''">
                AND b.dscg_time BETWEEN #{queryParam.cy_start_date} and CONCAT(#{queryParam.cy_end_date},' 23:59:59')
            </if>
            <if test="queryParam.seStartTime!=null and queryParam.seStartTime!='' and
                queryParam.seEndTime!=null and queryParam.seEndTime!=''">
                AND b.setl_end_time BETWEEN #{queryParam.seStartTime} and CONCAT(#{queryParam.seEndTime},' 23:59:59')
            </if>
            <if test="queryParam.doctorIdList!=null">
                AND (
                b.deptdrt_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                b.chfdr_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                b.atddr_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                b.ipdr_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                b.resp_nurs_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                b.train_dr_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                b.intn_dr in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                b.codr_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                b.qltctrl_dr_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                b.qltctrl_nurs_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                )
            </if>
        )x
        cross join (
            select
                IFNULL(count(1),0) AS totalMedicalNum,
                IFNULL(count(case when a.grp_stas = '1' then 1 else null end),0) AS inGroupMedicalNum,
                IFNULL(count(case when a.grp_stas != '1' then 1 else null end),0) AS notGroupMedicalNum,
                IFNULL(count(distinct (case when a.grp_stas = '1' then a.dip_codg else null end)),0) AS dipGroupNum,
                IFNULL(ROUND(AVG(b.act_ipt), 2),0) AS hosAvgDays,
                IFNULL(ROUND(AVG(CASE WHEN a.grp_stas = '1' THEN b.act_ipt ELSE NULL END), 2),0) AS hosInGroupAvgDays,
                IFNULL(ROUND(AVG(b.ipt_sumfee), 2),0) AS hosAvgCost,
                IFNULL(ROUND(AVG(CASE WHEN a.grp_stas = '1' THEN b.ipt_sumfee ELSE NULL END), 2),0) AS hosInGroupAvgCost,
                IFNULL(ROUND(SUM(CASE WHEN a.grp_stas = '1' THEN b.act_ipt ELSE NULL END / NULLIF(convert(AES_DECRYPT(UNHEX(c.dip_standard_ipt_days),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) using utf8),0))
                /NULLIF(COUNT(CASE WHEN a.grp_stas = '1' THEN 1 ELSE NULL END),0), 2),0) AS hosTimeIndex,
                IFNULL(ROUND(SUM(CASE WHEN a.grp_stas = '1' THEN b.ipt_sumfee ELSE NULL END /
                NULLIF(convert(AES_DECRYPT(UNHEX(c.dip_standard_inpf),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) using utf8),0)) /NULLIF(COUNT(CASE WHEN a.grp_stas = '1' THEN 1 ELSE NULL END),0), 2),0) AS hosCostIndex,
                IFNULL(ROUND(AVG(b.drugfee), 2),0) AS hosAvgMedicalCost,
                IFNULL(ROUND(AVG(b.mcs_fee), 2),0)   AS hosAvgMaterialCost
            from som_dip_grp_rcd a
            inner join som_dip_grp_info b on a.SETTLE_LIST_ID = b.SETTLE_LIST_ID
            left join som_dip_standard c on a.dip_codg=c.dip_codg AND SUBSTR(b.dscg_time,1,4) = c.STANDARD_YEAR AND b.HOSPITAL_ID = c.HOSPITAL_ID
            where 1 = 1
            <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
                AND  a.HOSPITAL_ID = #{queryParam.hospitalId}
            </if>
            <if test="queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and queryParam.cy_end_date!=null and queryParam.cy_end_date!=''">
                AND b.dscg_time BETWEEN #{queryParam.cy_start_date} and CONCAT(#{queryParam.cy_end_date},' 23:59:59')
            </if>
            <if test="queryParam.seStartTime!=null and queryParam.seStartTime!='' and queryParam.seEndTime!=null and queryParam.seEndTime!=''">
                AND b.setl_end_time BETWEEN #{queryParam.seStartTime} and CONCAT(#{queryParam.seEndTime},' 23:59:59')
            </if>
        ) y
        GROUP BY x.dscg_caty_codg_inhosp,x.dscg_caty_name_inhosp, y.totalMedicalNum
        ORDER BY dipGroupNum desc
    </select>

    <select id="queryConsumptionIndex" resultType="com.my.som.vo.dipBusiness.DipDeptIndexVo">
        SELECT a.dscg_caty_codg_inhosp AS deptCode,
        a.dip_codg AS dipCodg,
        ROUND(IFNULL(AVG(a.act_ipt),0),2) AS avgInHosDays,
        ROUND(IFNULL(CONVERT(AES_DECRYPT(UNHEX(b.dip_standard_ipt_days_same_lv),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) using utf8),0),2) AS dipInHosDays,
        ROUND(IFNULL(CONVERT(AES_DECRYPT(UNHEX(b.last_year_avg_fee),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) using utf8),0),2) AS dipInHosCost,
        ROUND(IFNULL(AVG(a.ipt_sumfee),0),2) AS avgInHosCost,
        ROUND(IFNULL(IFNULL(AVG(a.act_ipt),0)/NULLIF(CONVERT(AES_DECRYPT(UNHEX(b.dip_standard_ipt_days_same_lv),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) using utf8),0),0) - 1,2) AS timeIndex,
        ROUND(IFNULL(IFNULL(AVG(a.ipt_sumfee),0)/NULLIF(CONVERT(AES_DECRYPT(UNHEX(b.last_year_avg_fee),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) using utf8),0),0) - 1,2) AS costIndex
        FROM som_dip_grp_info a
        <if test='queryParam.enableSeAns != null and queryParam.enableSeAns != "" and queryParam.enableSeAns == "1"'>
            LEFT JOIN som_hi_invy_bas_info q
            ON q.ID = a.SETTLE_LIST_ID
            INNER JOIN som_setl_cas_crsp p
            ON q.k00 = p.k00
        </if>
        LEFT JOIN som_dip_standard b
        ON a.dip_codg = b.dip_codg
        AND SUBSTR(a.dscg_time,1,4) = b.STANDARD_YEAR
        AND a.is_used_asst_list = b.is_used_asst_list
        AND a.asst_list_age_grp = b.asst_list_age_grp
        AND a.asst_list_dise_sev_deg = b.asst_list_dise_sev_deg
        AND a.asst_list_tmor_sev_deg = b.asst_list_tmor_sev_deg
        AND a.HOSPITAL_ID = b.HOSPITAL_ID
        WHERE a.dip_codg IS NOT NULL
          AND a.HOSPITAL_ID = #{queryParam.hospitalId}
            <if test="queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and
                   queryParam.cy_end_date!=null and queryParam.cy_end_date!=''">
                <![CDATA[
           AND a.dscg_time >= #{queryParam.cy_start_date}
           AND a.dscg_time <= CONCAT(#{queryParam.cy_end_date},' 23:59:59')
           ]]>
            </if>
            <if test="queryParam.seStartTime!=null and queryParam.seStartTime!='' and queryParam.seEndTime!=null and queryParam.seEndTime!=''">
                AND a.setl_end_time BETWEEN #{queryParam.seStartTime} and CONCAT(#{queryParam.seEndTime},' 23:59:59')
            </if>
            <if test="queryParam.b16c!=null and queryParam.b16c!=''">
                AND a.dscg_caty_codg_inhosp = #{queryParam.b16c}
            </if>
        GROUP BY a.dscg_caty_codg_inhosp,
        a.dip_codg,
        b.dip_standard_ipt_days_same_lv,
        b.last_year_avg_fee
    </select>
</mapper>
