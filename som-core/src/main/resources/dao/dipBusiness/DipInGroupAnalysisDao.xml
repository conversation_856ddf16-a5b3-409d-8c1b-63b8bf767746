<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.som.dao.dipBusiness.DipInGroupAnalysisDao">
    <select id="list" resultType="com.my.som.vo.dipBusiness.DipInGroupAnalysisVo">
        select a.*,
               b.NAME as priOutHosDeptName
        from
            (
                select a.dscg_caty_codg_inhosp as priOutHosDeptCode,
                       a.HOSPITAL_ID,
                       IFNULL(ROUND(NULLIF(sum(case when a.grp_stas = '1' then 1 else 0 end),0)/count(1) * 100,2),0) as inGroupRate,
                       NULLIF(count(DISTINCT(case when a.grp_stas = '1' then a.dip_codg else null end)),0) as dipNum,
                       count(1) as medicalTotalNum,
                       count(case when a.grp_stas = '1' then 1 else null end) as groupNum,
                       count(case when a.grp_stas != '1' then 1 else null end) as noGroupNum,
                       count(case when b.chk_flag = '0' then 1 else null end) as paiChuNum,
                       count(case when b.grp_fale_rea = '未找到主要诊断节代码' then 1 else null end) as error1,
                       count(case when b.grp_fale_rea like '%未找到级别%' then 1 else null end) as error2,
                       count(case when b.grp_fale_rea = '住院天数大于60天或小于0' then 1 else null end) as error3,
                       count(case when substr(b.grp_fale_rea,1,6)  in ('%主要诊断编码%','主要诊断填写') then 1 else null end) as error4,
                       count(case when b.grp_fale_rea like '%手术及操作编码%' then 1 else null end) as error5
                from som_dip_grp_info a
                         left join som_dip_grp_rcd b
                                   on a.SETTLE_LIST_ID = b.SETTLE_LIST_ID
                <where>
                    a.dscg_caty_codg_inhosp IS NOT NULL
                    AND a.dscg_caty_codg_inhosp != ''
                    <include
                            refid="com.my.som.mapper.newBusiness.common.NewBusinessCommonAnalysisMapper.queryBusCommon"></include>
                    <include refid="Common_sql"></include>
                    <!-- 出院时间范围 -->
                    <if test="dto.cy_start_date!=null and dto.cy_start_date!='' and dto.cy_end_date!=null and dto.cy_end_date!=''">
                        and a.dscg_time BETWEEN #{dto.cy_start_date} and CONCAT(#{dto.cy_end_date},' 23:59:59')
                    </if>
                </where>
                group by a.dscg_caty_codg_inhosp,a.HOSPITAL_ID
                order by inGroupRate desc
            ) a
                left join som_dept b
                          on a.priOutHosDeptCode = b.CODE
                            AND a.HOSPITAL_ID = b.HOSPITAL_ID
    </select>

    <select id="getTopCountInfo" resultType="com.my.som.vo.dipBusiness.DipInGroupTopCountVo">
        select a.*,
               ifnull(round(ifnull(ifnull(a.medicalRecordNum-a.lastMonthMedicalRecordNum,0)/nullif(a.lastMonthMedicalRecordNum,0),0) * 100,2),0) as medicalRecordNumRingRatio,
               ifnull(round(ifnull(ifnull(a.medicalRecordNum-lastYearMedicalRecordNum,0)/nullif(a.lastYearMedicalRecordNum,0),0) * 100,2),0) as medicalRecordNumYOY,

               ifnull(round(ifnull(ifnull(a.dipNum-lastMonthDipNum,0)/nullif(a.lastMonthDipNum,0),0) * 100,2),0) as dipNumRingRatio,
               ifnull(round(ifnull(ifnull(a.dipNum-lastYearDipNum,0)/nullif(a.lastYearDipNum,0),0) * 100,2),0) as dipNumYOY,

               ifnull(round(ifnull(ifnull(a.drgInGroupMedcasVal-lastMonthInGroupNum,0)/nullif(a.lastMonthInGroupNum,0),0) * 100,2),0) as inGroupNumRatio,
               ifnull(round(ifnull(ifnull(a.drgInGroupMedcasVal-lastYearInGroupNum,0)/nullif(a.lastYearInGroupNum,0),0) * 100,2),0) as inGroupNumYOY,

               case when a.lastMonthNotInGroupNum = 0 and a.notInGroupNum != 0 then '100.00' else ifnull(round(ifnull(ifnull(a.notInGroupNum-lastMonthNotInGroupNum,0)/nullif(a.lastMonthNotInGroupNum,0),0) * 100,2),0) end as notInGroupNumRatio,
               case when a.lastYearNotInGroupNum = 0 and a.notInGroupNum != 0 then '100.00' else ifnull(round(ifnull(ifnull(a.notInGroupNum-lastYearNotInGroupNum,0)/nullif(a.lastYearNotInGroupNum,0),0) * 100,2),0) end as notInGroupNumYOY
        from
            (
                select
                       ifnull(count(case when a.dscg_time between #{dto.cy_start_date} and CONCAT(#{dto.cy_end_date},' 23:59:59') then 1 else null end),0) as medicalRecordNum,
                       ifnull(count(case when a.dscg_time between #{dto.lastMonth_cy_start_date} and CONCAT(#{dto.lastMonth_cy_end_date},' 23:59:59') then 1 else null end),0) as lastMonthMedicalRecordNum,
                       ifnull(count(case when a.dscg_time between #{dto.lastYear_cy_start_date} and CONCAT(#{dto.lastYear_cy_end_date},' 23:59:59') then 1 else null end),0) as lastYearMedicalRecordNum,

                       ifnull(count(distinct(case when a.dscg_time between #{dto.cy_start_date} and CONCAT(#{dto.cy_end_date},' 23:59:59') then b.dip_codg else null end)),0) as dipNum,
                       ifnull(count(distinct(case when a.dscg_time between #{dto.lastMonth_cy_start_date} and CONCAT(#{dto.lastMonth_cy_end_date},' 23:59:59') then b.dip_codg else null end)),0) as lastMonthDipNum,
                       ifnull(count(distinct(case when a.dscg_time between #{dto.lastYear_cy_start_date} and CONCAT(#{dto.lastYear_cy_end_date},' 23:59:59') then b.dip_codg else null end)),0) as lastYearDipNum,

                       ifnull(count(case when a.dscg_time between #{dto.cy_start_date} and CONCAT(#{dto.cy_end_date},' 23:59:59') and b.grp_stas = '1' then 1 else null end),0) as drgInGroupMedcasVal,
                       ifnull(count(case when a.dscg_time between #{dto.lastMonth_cy_start_date} and CONCAT(#{dto.lastMonth_cy_end_date},' 23:59:59') and b.grp_stas = '1' then 1 else null end),0) as lastMonthInGroupNum,
                       ifnull(count(case when a.dscg_time between #{dto.lastYear_cy_start_date} and CONCAT(#{dto.lastYear_cy_end_date},' 23:59:59') and b.grp_stas = '1' then 1 else null end),0) as lastYearInGroupNum,

                       ifnull(count(case when a.dscg_time between #{dto.cy_start_date} and CONCAT(#{dto.cy_end_date},' 23:59:59') and b.grp_stas != '1' then 1 else null end),0) as notInGroupNum,
                       ifnull(count(case when a.dscg_time between #{dto.lastMonth_cy_start_date} and CONCAT(#{dto.lastMonth_cy_end_date},' 23:59:59') and b.grp_stas != '1' then 1 else null end),0) as lastMonthNotInGroupNum,
                       ifnull(count(case when a.dscg_time between #{dto.lastYear_cy_start_date} and CONCAT(#{dto.lastYear_cy_end_date},' 23:59:59') and b.grp_stas != '1' then 1 else null end),0) as lastYearNotInGroupNum
                from som_dip_grp_info a
                         left join som_dip_grp_rcd b
                                   on a.SETTLE_LIST_ID = b.SETTLE_LIST_ID
                <where>
                    <if test="hospitalId != null and hospitalId != ''">
                        AND a.HOSPITAL_ID = #{hospitalId}
                    </if>
                    <include refid="Common_sql"></include>
                </where>

            ) a
    </select>

    <select id="getNoGroupResonCountInfo" resultType="com.my.som.vo.dipBusiness.DipInGroupAnalysisCountVo">
        select a.*,
               ifnull(concat(round(ifnull(a.medcasVal/nullif(b.totals,0),0) * 100 ,2),'%'),0) as allRate
        from
            (
                select a.*,
                       ifnull(concat(round(ifnull(a.medcasVal/nullif(b.notInGroupTotals,0),0) * 100,2),'%'),0) as notInGroupRate
                from
                    (
                        select '未入组原因' as notInGroupName,
                               '排除病案数' as notInGroupReason,
                               count(case when b.chk_flag = '0' then 1 else null end) as medcasVal
                        from som_dip_grp_info a
                                 left join som_dip_grp_rcd b
                                           on a.SETTLE_LIST_ID = b.SETTLE_LIST_ID
                         <where>
                            <include refid="Common_sql"></include>
                             <!-- 出院时间范围 -->
                             <if test="dto.cy_start_date!=null and dto.cy_start_date!='' and dto.cy_end_date!=null and dto.cy_end_date!=''">
                                and a.dscg_time BETWEEN #{dto.cy_start_date} and CONCAT(#{dto.cy_end_date},' 23:59:59')
                            </if>
                         </where>
                        union all
                        select '未入组原因' as notInGroupName,
                               '未找到主要诊断节代码' as notInGroupReason,
                               count(1) as medcasVal
                        from som_dip_grp_info a
                                 left join som_dip_grp_rcd b
                                           on a.SETTLE_LIST_ID = b.SETTLE_LIST_ID
                        where b.grp_fale_rea = '未找到主要诊断节代码'
                        <include refid="Common_sql"></include>
                        <!-- 出院时间范围 -->
                        <if test="dto.cy_start_date!=null and dto.cy_start_date!='' and dto.cy_end_date!=null and dto.cy_end_date!=''">
                            and a.dscg_time BETWEEN #{dto.cy_start_date} and CONCAT(#{dto.cy_end_date},' 23:59:59')
                        </if>
                        union all
                        select '未入组原因' as notInGroupName,
                               '未找到手术级别' as notInGroupReason,
                               count(1) as medcasVal
                        from som_dip_grp_info a
                                 left join som_dip_grp_rcd b
                                           on a.SETTLE_LIST_ID = b.SETTLE_LIST_ID
                        where b.grp_fale_rea like '%未找到级别%'
                        <include refid="Common_sql"></include>
                        <!-- 出院时间范围 -->
                        <if test="dto.cy_start_date!=null and dto.cy_start_date!='' and dto.cy_end_date!=null and dto.cy_end_date!=''">
                            and a.dscg_time BETWEEN #{dto.cy_start_date} and CONCAT(#{dto.cy_end_date},' 23:59:59')
                        </if>
                        union all
                        select '未入组原因' as notInGroupName,
                               '主要诊断编码错误' as notInGroupReason,
                               count(1) as medcasVal
                        from som_dip_grp_info a
                                left join som_dip_grp_rcd b
                                    on a.SETTLE_LIST_ID = b.SETTLE_LIST_ID
                        where substr(b.grp_fale_rea,1,6)  in ('%主要诊断编码%','主要诊断填写')
                        <include refid="Common_sql"></include>
                        <!-- 出院时间范围 -->
                        <if test="dto.cy_start_date!=null and dto.cy_start_date!='' and dto.cy_end_date!=null and dto.cy_end_date!=''">
                            and a.dscg_time BETWEEN #{dto.cy_start_date} and CONCAT(#{dto.cy_end_date},' 23:59:59')
                        </if>
                        union all
                        select '未入组原因' as notInGroupName,
                               '手术及操作编码错误' as notInGroupReason,
                               count(1) as medcasVal
                        from som_dip_grp_info a
                                left join som_dip_grp_rcd b
                                    on a.SETTLE_LIST_ID = b.SETTLE_LIST_ID
                        where b.grp_fale_rea like '%手术及操作编码%'
                        <include refid="Common_sql"></include>
                        <!-- 出院时间范围 -->
                        <if test="dto.cy_start_date!=null and dto.cy_start_date!='' and dto.cy_end_date!=null and dto.cy_end_date!=''">
                            and a.dscg_time BETWEEN #{dto.cy_start_date} and CONCAT(#{dto.cy_end_date},' 23:59:59')
                        </if>
                        union all
                        select '未入组原因' as notInGroupName,
                        '主要诊断不参与分组' as notInGroupReason,
                        count(1) as medcasVal
                        from som_dip_grp_info a
                        left join som_dip_grp_rcd b
                        on a.SETTLE_LIST_ID = b.SETTLE_LIST_ID
                        where b.grp_fale_rea like '%不参与分组%'
                        <include refid="Common_sql"></include>
                        <!-- 出院时间范围 -->
                        <if test="dto.cy_start_date!=null and dto.cy_start_date!='' and dto.cy_end_date!=null and dto.cy_end_date!=''">
                            and a.dscg_time BETWEEN #{dto.cy_start_date} and CONCAT(#{dto.cy_end_date},' 23:59:59')
                        </if>
                    ) a

                        inner join
                    (
                        select count(1) as notInGroupTotals
                        from som_dip_grp_info a left join som_dip_grp_rcd b on a.SETTLE_LIST_ID = b.SETTLE_LIST_ID
                        where b.grp_fale_rea is not null
                        <include refid="Common_sql"></include>
                        <!-- 出院时间范围 -->
                        <if test="dto.cy_start_date!=null and dto.cy_start_date!='' and dto.cy_end_date!=null and dto.cy_end_date!=''">
                            and a.dscg_time BETWEEN #{dto.cy_start_date} and CONCAT(#{dto.cy_end_date},' 23:59:59')
                        </if>
                    ) b

            ) a
                inner join
            (
                select count(1) as totals
                from som_dip_grp_info a
                         left join som_dip_grp_rcd b
                                   on a.SETTLE_LIST_ID = b.SETTLE_LIST_ID
                <where>
                    <include refid="Common_sql"></include>
                    <!-- 出院时间范围 -->
                    <if test="dto.cy_start_date!=null and dto.cy_start_date!='' and dto.cy_end_date!=null and dto.cy_end_date!=''">
                        and a.dscg_time BETWEEN #{dto.cy_start_date} and CONCAT(#{dto.cy_end_date},' 23:59:59')
                    </if>
                </where>
            ) b
    </select>


    <sql id="Common_sql">
        <!-- 科室编码 -->
        <if test="dto.deptCode != null and dto.deptCode != ''">
            and a.dscg_caty_codg_inhosp = #{dto.deptCode,jdbcType=VARCHAR}
        </if>
        <if test="dto.b16c != null and dto.b16c != ''">
            and a.dscg_caty_codg_inhosp = #{dto.b16c,jdbcType=VARCHAR}
        </if>
    </sql>
</mapper>