<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.som.dao.dipBusiness.DipPatientAnalysisDao">
    <select id="selectPatientData" resultType="com.my.som.vo.dipBusiness.DipPatientAnalysisVo">
        SELECT a.bah,
               a.jzFlag,
               a.jsid,
               a.name,
               a.age,
               a.deptCode,
               a.deptName,
               a.outHosTime,
               a.dipCodg,
        CASE
        WHEN a.dipCodg ='' or a.dipCodg is null
        THEN
        '否' ELSE '是' END as inDipGroup,
               a.dipName,
        CASE
        WHEN a.isUsedAsstList = 1
        THEN
        '是' ELSE '否' END as isUsedAsstList,

               a.asstList<PERSON>geGrp,
               a.asstListDiseSevDeg,
               a.asstListTmorSevDeg,
               a.<PERSON>,
               a.hospLv,
        a.keySpcyType,
         a.dominantDisease,
        a.baseDisease,
         a.youngerDisease ,
         a.professionalDisease ,
        a.sumfee,
        a.forecast_fee as forecastFee,
        a.profitloss,
        a.isReAdm,
        a.isDead,
        a.avgCost,
        a.avgDays
        FROM
             (
                 SELECT a.PATIENT_ID AS bah,
                        b.k00 AS jzFlag,
                        b.SETTLEMENT_ID AS jsid,
                        a.`NAME` AS name,
                        a.AGE AS age,
                        a.dscg_caty_codg_inhosp AS deptCode,
                        c.`NAME` AS deptName,
                        a.dscg_time AS outHosTime,
                        a.dip_codg AS dipCodg,
                        a.DIP_NAME AS dipName,
                        a.is_used_asst_list AS isUsedAsstList,
                        a.asst_list_age_grp AS asstListAgeGrp,
                        a.asst_list_dise_sev_deg AS asstListDiseSevDeg,
                        a.asst_list_tmor_sev_deg AS asstListTmorSevDeg,
                        a.auxiliary_burn AS auxiliaryBurn,
                        d.hosp_lv AS hospLv,
                        g.key_spcy_type AS keySpcyType,
                        CASE WHEN e.hi_codg IS NOT NULL
                               OR e.tcm_dise_codg IS NOT NULL THEN 1 ELSE 0 END AS dominantDisease,
                        CASE WHEN f.dip_codg IS NOT NULL
                              AND d.hosp_lv != 3 THEN 1 ELSE 0 END AS baseDisease,
                        <![CDATA[
                            CASE WHEN a.AGE < 14 THEN 1 ELSE 0 END AS youngerDisease,
                        ]]>
                        CASE WHEN g.inhosp_dept_codg IS NOT NULL THEN 1 ELSE 0 END AS professionalDisease ,
        h.sumfee,
        h.forecast_fee,
        h.profitloss,

        CASE
        WHEN b.B36C =2
        THEN
        '是' ELSE '否' END as isReAdm,
        CASE
        WHEN b.b34c =5
        THEN
        '是' ELSE '否' END as isDead,
        i.avgCost,
        i.avgDays
        FROM som_dip_grp_info a
        LEFT JOIN som_hi_invy_bas_info b
            ON a.SETTLE_LIST_ID = b.ID
        <if test='enableSeAns != null and enableSeAns != "" and enableSeAns == "1"'>
            INNER JOIN som_setl_cas_crsp p
            ON b.k00 = p.k00
        </if>
        LEFT JOIN som_dept c
            ON a.dscg_caty_codg_inhosp = c.`CODE`
            AND a.HOSPITAL_ID = c.HOSPITAL_ID
        LEFT JOIN som_hosp_info d
            ON a.HOSPITAL_ID = d.HOSPITAL_ID
        LEFT JOIN som_hi_tcm_codg_crsp e
            ON b.C03C = e.hi_codg
           AND b.C37C = e.tcm_dise_codg
        LEFT JOIN som_dip_grp_detl f
            ON a.dip_codg = f.dip_codg
           AND f.ACTIVE_FLAG = '1'
        LEFT JOIN som_dip_key_spcy_info g
            ON a.dscg_caty_codg_inhosp = g.inhosp_dept_codg
           AND g.ACTIVE_FLAG = '1'
        AND g.ACTIVE_FLAG = '1'
        Left join som_dip_sco h on h.settle_list_id = b.ID
        LEFT JOIN (
        SELECT

        ROUND( IFNULL( AVG( a.ipt_sumfee ), 0 ), 2 ) AS avgCost,
        ROUND( IFNULL( AVG( a.act_ipt ), 0 ), 2 ) AS avgDays,
        a.dip_codg,
        a.DIP_NAME,
        a.is_used_asst_list,
        a.asst_list_age_grp,
        a.asst_list_dise_sev_deg,
        a.asst_list_tmor_sev_deg,
        a.auxiliary_burn
        FROM
        som_dip_grp_info a
        LEFT JOIN som_dip_standard i ON a.dip_codg = i.dip_codg
        AND SUBSTR( a.dscg_time, 1, 4 ) = i.STANDARD_YEAR
        AND IFNULL( i.asst_list_age_grp, 0 ) = IFNULL( a.asst_list_age_grp, 0 )
        AND IFNULL( i.asst_list_dise_sev_deg, 0 ) = IFNULL( a.asst_list_dise_sev_deg, 0 )
        AND IFNULL( i.asst_list_tmor_sev_deg, 0 ) = IFNULL( a.asst_list_tmor_sev_deg, 0 )
        AND IFNULL( i.auxiliary_burn, 0 ) = IFNULL( a.auxiliary_burn, 0 )
        AND a.HOSPITAL_ID = i.HOSPITAL_ID
        GROUP BY

        a.dip_codg,
        a.DIP_NAME,
        a.is_used_asst_list,
        a.asst_list_age_grp,
        a.asst_list_dise_sev_deg,
        a.asst_list_tmor_sev_deg,
        a.auxiliary_burn
        ) i ON a.dip_codg = i.dip_codg
        WHERE a.dip_codg IS NOT NULL
         <if test="begnDate != null and begnDate != '' and expiDate != null and expiDate != ''">
          AND a.dscg_time
              BETWEEN #{begnDate} AND concat(#{expiDate},' 23:59:59')
          AND b.B15
              BETWEEN #{begnDate} AND concat(#{expiDate},' 23:59:59')
         </if>
         <if test="seStartTime != null and seStartTime != '' and seEndTime != null and seEndTime != ''">
             AND a.setl_end_time
             BETWEEN #{seStartTime} AND concat(#{seEndTime},' 23:59:59')
             AND b.D37
             BETWEEN #{seStartTime} AND concat(#{seEndTime},' 23:59:59')
         </if>
         <if test="medcasCodg != null and medcasCodg != ''">
          AND a.PATIENT_ID LIKE CONCAT('%',#{medcasCodg},'%')
         </if>
         <if test="dipCodg != null and dipCodg != ''">
          AND a.dip_codg = #{dipCodg}
         </if>
         <if test="name != null and name != ''">
          AND a.`NAME` LIKE CONCAT('%',#{name},'%')
         </if>

                 <include
                         refid="com.my.som.mapper.newBusiness.common.NewBusinessCommonAnalysisMapper.queryBusCommon"></include>
        ) a
        <where>
            <if test="diseType != null  and diseType.size() > 0 ">
                <foreach collection="diseType" index="index" item="item">
                    <if test='item == "1"'>
                        AND a.dominantDisease = 1
                    </if>
                    <if test='item == "2"'>
                        AND a.baseDisease = 1
                    </if>
                    <if test='item == "3"'>
                        AND a.youngerDisease = 1
                    </if>
                    <if test='item == "4"'>
                        AND a.professionalDisease = 1
                    </if>
                </foreach>
            </if>
            <if test="isDead != null and isDead != ''">
                <if test="isDead ==1">
                    AND a.`isDead` = '是'
                </if>
                <if test="isDead ==0">
                    AND a.`isDead` = '否'
                </if>
            </if>
            <if test="isReAdm != null and isReAdm != ''">
                <if test="isReAdm ==1">
                    AND a.`isReAdm` = '是'
                </if>
                <if test="isReAdm ==0">
                    AND a.`isReAdm` = '否'
                </if>
            </if>
        </where>

    </select>

</mapper>