<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.my.som.dao.dipBusiness.DipPayToPredictDao">

    <!-- 查询数据 -->
    <select id="getList" resultType="com.my.som.vo.dipBusiness.DipPayToPredictVo">
        SELECT a.*
        FROM
        (
        SELECT a.*,
        <choose>
            <when test="feeStas == 0">
                ROUND(IFNULL(a.profitloss,0),2) AS profitAndLoss,
            </when>
            <when test="feeStas == 1">
                ROUND(IFNULL(a.preCost,0),2) AS profitAndLoss,
            </when>
        </choose>
        b.name AS dept
        FROM
        (
        SELECT a.patientId,
        a.jzid,
        a.jsid,

        a.beforeHospTime,
        a.afterHospTime,
        a.id,
        a.name,
        a.dipCodg,
        a.dipName,
        a.isUsedAsstList,
        a.asstListAgeGrp,
        a.asstListDiseSevDeg,
        a.asstListTmorSevDeg,
        a.auxiliaryBurn,
        a.standardFee,
        a.standardDays,
        a.inHosDays,
        a.hospitalId,
        a.recodeType,
        a.deptCode,
        ifnull( a.sumfee,0) AS sumfee ,

        a.stsbFee,
        a.insuredType,
        a.refer_sco,
        a.adjm_cof,
        a.uplmtMag,
        a.dominantDiseaseRate,
        a.baseDiseaseRate,
        a.youngerDiseaseRate,
        a.professionalDiseaseRate,
        a.hospCof,
        a.preHospExamfee AS preHospExamfee,
        case
         when a.listSerialNumFlag is  not null and a.listSerialNumFlag != '' then
            '是'
        else
            '否'
        end
        as listSerialNumFlag,
        round(a.calculateScore,2) AS calculateScore,
        round(a.addScore,2) AS addScore,
        round(a.totlSco,2) AS totlSco,
        a.inHospCost ,
        a.checkTotalFee,    a.fundAmtSum,a.presonBearCost,a.preFundFee,a.fundRatio,a.fundSourceType,a.fundRatioType
        <choose>
            <when test="feeStas == 0">
                ,a.ycCost as preCost,
                a.profitloss AS profitloss
            </when>
            <when test="feeStas == 1">
                , ROUND(a.ycCost,2) AS preCost,
                a.fbTotalCost
            </when>
        </choose>
        FROM
        (
        SELECT a.PATIENT_ID AS patientId,
        q.CLINIC_ID AS jzid,
        q.SETTLEMENT_ID AS jsid,
        a.SETTLE_LIST_ID as id,
        a.`NAME` AS name,
        a.is_used_asst_list AS isUsedAsstList,
        a.asst_list_age_grp AS asstListAgeGrp,
        a.asst_list_dise_sev_deg AS asstListDiseSevDeg,
        a.asst_list_tmor_sev_deg AS asstListTmorSevDeg,
        a.auxiliary_burn AS auxiliaryBurn,
        ROUND(IFNULL(CONVERT(AES_DECRYPT(UNHEX(b.dip_standard_inpf),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) USING utf8),0), 4) AS standardFee,
        ROUND(IFNULL(CONVERT(AES_DECRYPT(UNHEX(b.dip_standard_ipt_days),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) USING utf8),0), 4) AS standardDays,

        a.HOSPITAL_ID AS hospitalId,
        a.medcas_type AS recodeType,
        a.dscg_caty_codg_inhosp AS deptCode,
        c.sumfee AS sumfee,
        c.forecast_fee AS ycCost,
        c.profitloss AS profitloss,
        q.B20  as inHosDays,
        q.b12 as beforeHospTime,
        q.b15 as afterHospTime,
        q.D35 as    listSerialNumFlag,
        ifnull(c.pre_hosp_examfee,0) AS preHospExamfee,
        <!--                                   case-->
        <!--                                       when c.dise_type = '1' then '3'-->
        <!--                                       when c.dise_type = '2' then '2'-->
        <!--                                       when c.dise_type = '3' then '1'-->
        <!--                                       else c.dise_type-->
        <!--                                    end -->
        c.dise_type AS stsbFee,
        <choose>
            <when test="feeStas == 0">
                a.dip_codg AS dipCodg,
                a.DIP_NAME AS dipName,
                CASE
                WHEN ifnull(t1.hi_type,c.insu_type) IN ('1', '01', '310') THEN '1'
                WHEN ifnull(t1.hi_type,c.insu_type)  IN ('2', '02', '390') THEN '2'
                ELSE '99'
                END AS insuredType,
                c.setl_sco AS calculateScore,
                IFNULL(c.incr_sco,0) AS addScore,
                c.totl_sco AS totlSco,
                c.refer_sco AS refer_sco,
            </when>
            <when test="feeStas == 1">
                e.dip_codg AS dipCodg,
                e.DIP_NAME AS dipName,
                e.INSURED_TYPE AS insuredType,
                e.incr_sco AS addScore,
                e.setl_sco AS calculateScore,
                e.setl_sco AS totlSco,
                e.bas_sco AS refer_sco,
                e.ipt_sumfee AS fbTotalCost,
                e.ycCost,
            </when>
        </choose>
        c.adjm_cof AS adjm_cof,
        c.uplmt_mag AS uplmtMag,
        c.tcm_adt_cof AS dominantDiseaseRate,
        c.grst_cof AS baseDiseaseRate,
        c.young_cof AS youngerDiseaseRate,
        c.key_disc_cof AS professionalDiseaseRate,
        c.hosp_cof AS hospCof,
        c.in_hosp_cost as inHospCost,
        c.medicare_settlement_cost as checkTotalFee,
        c.fundAmtSum ,c.presonBearCost,c.preFundFee,c.fundRatio,c.fundSourceType,c.fundRatioType
        FROM som_dip_grp_info a
        left join som_dip_standard b on
        a.dip_codg = b.dip_codg and
        a.asst_list_age_grp = b.asst_list_age_grp
        and   a.asst_list_dise_sev_deg = b.asst_list_dise_sev_deg
        and    a.asst_list_tmor_sev_deg = b.asst_list_tmor_sev_deg
        and   a.asst_list_tmor_sev_deg = b.asst_list_tmor_sev_deg
        and    a.auxiliary_burn = b.auxiliary_burn
        and    a.hospital_id = b.hospital_id
        and     b.standard_year = ifnull(SUBSTRING(a.setl_end_time,1,4),SUBSTRING(a.dscg_time,1,4) )
        LEFT JOIN som_hi_invy_bas_info q
        ON q.ID = a.SETTLE_LIST_ID
        LEFT JOIN som_setl_info t1
        on t1.unique_id = q.k00
        <if test='enableSeAns != null and enableSeAns != "" and enableSeAns == "1"'>
            INNER JOIN som_setl_cas_crsp p
            ON q.k00 = p.k00
        </if>
        LEFT JOIN som_dip_sco c
        ON a.SETTLE_LIST_ID = c.SETTLE_LIST_ID

        <if test="feeStas == 1">
            INNER JOIN
            (
            SELECT
            a.dip_codg,
            a.DIP_NAME,
            a.rid_idt_codg,
            a.medcas_codg,
            a.adm_time,
            a.dscg_time,
            a.is_in_group,
            b.bas_sco,
            b.incr_sco,
            b.sumfee AS ipt_sumfee,
            b.INSURED_TYPE,
            b.MED_TYPE AS dise_type,
            b.setl_pt_val,
            b.dfr_fee AS ycCost,
            b.MED_TYPE,
            b.setl_sco,
            a.used_asst_list,
            a.asst_list_age_grp,
            a.asst_list_dise_sev_deg,
            a.asst_list_tmor_sev_deg
            FROM som_dip_grp_fbck a
            LEFT JOIN som_fund_dfr_fbck b
            ON a.rid_idt_codg = b.rid_idt_codg
            WHERE 1=1
            <if test="cy_start_date != null and cy_start_date != '' and cy_end_date != null and cy_end_date != ''">
                AND a.dscg_time BETWEEN #{cy_start_date,jdbcType=VARCHAR} AND concat(#{cy_end_date,jdbcType=VARCHAR},' 23:59:59')
            </if>
            <if test="inStartTime != null and inStartTime != '' and inEndTime != null and inEndTime != ''">
                AND a.adm_time BETWEEN #{inStartTime} AND CONCAT(#{inEndTime},' 23:59:59')
            </if>
            <if test="seStartTime!=null and seStartTime!='' and seEndTime!=null and seEndTime!=''">
                AND a.setl_time BETWEEN #{seStartTime} and CONCAT(#{seEndTime},' 23:59:59')
            </if>
            <if test="dipCodg!=null and dipCodg!=''">
                AND a.dip_codg = #{dipCodg,jdbcType=VARCHAR}
            </if>
            ) e
            ON a.PATIENT_ID = e.medcas_codg
            AND SUBSTR(a.adm_time,1,10) = e.adm_time
            AND SUBSTR(a.dscg_time,1,10) = e.dscg_time
        </if>

        WHERE 1=1
        <if test='setlway != null and setlway != "" and setlway == "1"'>
            AND q.setlway = #{setlway,jdbcType=VARCHAR}
        </if>
        <if test="cy_start_date != null and cy_start_date != '' and cy_end_date != null and cy_end_date != ''">
            AND a.dscg_time BETWEEN #{cy_start_date,jdbcType=VARCHAR} AND concat(#{cy_end_date,jdbcType=VARCHAR},' 23:59:59')
        </if>
        <if test="inStartTime != null and inStartTime != '' and inEndTime != null and inEndTime != ''">
            AND a.adm_time BETWEEN #{inStartTime} AND CONCAT(#{inEndTime},' 23:59:59')
        </if>
        <if test="seStartTime!=null and seStartTime!='' and seEndTime!=null and seEndTime!=''">
            AND a.setl_end_time BETWEEN #{seStartTime} and CONCAT(#{seEndTime},' 23:59:59')
        </if>
        <include
                refid="com.my.som.mapper.newBusiness.common.NewBusinessCommonAnalysisMapper.queryBusCommon"></include>
        <if test="bah != null and bah !=''">
            AND INSTR(a.PATIENT_ID,#{bah,jdbcType=VARCHAR}) > 0
        </if>
        <if test="fundRatioType != null and fundRatioType !=''">
            AND INSTR(c.fundRatioType,#{fundRatioType,jdbcType=VARCHAR}) > 0
        </if>
        <if test="costSection != null and costSection != ''">
                AND c.dise_type = #{costSection,jdbcType=VARCHAR}
        </if>
        <include refid="Convert_Cort"></include>
        <if test="dipCodg!=null and dipCodg!=''">
            AND a.dip_codg = #{dipCodg,jdbcType=VARCHAR}
        </if>
        ) a

        ) a
        left join som_dept b
        on a.deptCode = b.code AND a.hospitalId = b.HOSPITAL_ID) a
        <where>
            <if test="isLoss != null and isLoss != ''">
                <if test="isLoss == 1">
                    <![CDATA[
                    AND a.profitAndLoss < 0
                ]]>
                </if>
                <if test="isLoss == 0">
                    <![CDATA[
                    AND a.profitAndLoss >= 0
                ]]>
                </if>
            </if>
            <if test="categories != null and categories != ''">
                AND a.insuredType = #{categories}
            </if>
        </where>

    </select>

    <!-- 查询城职城乡结算信息 -->
    <select id="getListPoint" resultType="com.my.som.vo.dipBusiness.DipPayToPredictVo">
        SELECT a.*,
        b.*
        FROM
        (
        SELECT COUNT(1) AS totalCount
        <choose>
            <when test="feeStas == 0">
                , CASE
                WHEN ifnull(t1.hi_type, d.insu_type) IN ('1', '01', '310') THEN '1'
                WHEN ifnull(t1.hi_type, d.insu_type) IN ('2', '02', '390') THEN '2'
                ELSE '99'
                END AS insuType,
                <!--                      d.insu_type AS insuType,-->
                <!--                        IFNULL(SUM(CASE WHEN d.dise_type = 0 THEN IFNULL(a.pre_hosp_examfee,0) ELSE NULL END),0) AS nogroupPreHosCost,-->
                <!--                            IFNULL(SUM(CASE WHEN d.dise_type = 1 THEN IFNULL(a.pre_hosp_examfee,0) ELSE NULL END),0) AS upPreHosCost,-->
                <!--                            IFNULL(SUM(CASE WHEN d.dise_type = 2 THEN IFNULL(a.pre_hosp_examfee,0) ELSE NULL END),0) AS lowPreHosCost,-->
                <!--                            IFNULL(SUM(CASE WHEN d.dise_type = 3 THEN IFNULL(a.pre_hosp_examfee,0) ELSE NULL END),0) AS ncPreHosCost,-->
                <!--                            IFNULL(SUM(CASE WHEN d.dise_type = 4 THEN IFNULL(a.pre_hosp_examfee,0) ELSE NULL END),0) AS nobkPreHosCost,-->
                <!--                            IFNULL(SUM(CASE WHEN d.dise_type = 5 THEN IFNULL(a.pre_hosp_examfee,0) ELSE NULL END),0) AS instabilityPreHosCost,-->
                <!--                            IFNULL(SUM(CASE WHEN d.dise_type = 6 THEN IFNULL(a.pre_hosp_examfee,0) ELSE NULL END),0) AS exhPreHosCost,-->
                <!--                            IFNULL( SUM( CASE WHEN d.dise_type not in (0,1,2,3,4,5,6) THEN IFNULL( a.pre_hosp_examfee, 0 ) ELSE NULL END ), 0 ) AS otherPreHosCost,-->

                <![CDATA[
                            IFNULL(sum(a.pre_hosp_examfee),0) as preHospExamfee,

                            IFNULL(SUM(CASE WHEN d.dise_type = 0 THEN IFNULL(d.totl_sco,0) ELSE NULL END),0) AS nogroupPoint,
                            IFNULL(SUM(CASE WHEN d.dise_type = 1 THEN IFNULL(d.totl_sco,0) ELSE NULL END),0) AS upPoint,
                            IFNULL(SUM(CASE WHEN d.dise_type = 2 THEN IFNULL(d.totl_sco,0) ELSE NULL END),0) AS lowPoint,
                            IFNULL(SUM(CASE WHEN d.dise_type = 3 THEN IFNULL(d.totl_sco,0) ELSE NULL END),0) AS NCPoint,
                            IFNULL(SUM(CASE WHEN d.dise_type = 4 THEN IFNULL(d.totl_sco,0) ELSE NULL END),0) AS nobkPoint,
                            IFNULL(SUM(CASE WHEN d.dise_type = 5 THEN IFNULL(d.totl_sco,0) ELSE NULL END),0) AS instabilityPoint,
                            IFNULL(SUM(CASE WHEN d.dise_type = 6 THEN IFNULL(d.totl_sco,0) ELSE NULL END),0) AS exhPoint,
                            IFNULL(SUM(CASE WHEN d.dise_type not in (0,1,2,3,4,5,6) THEN IFNULL(d.totl_sco,0) ELSE NULL END),0) AS otherPoint,

                            IFNULL(SUM(CASE WHEN d.dise_type = 0 THEN IFNULL(d.sumfee,0) ELSE NULL END),0) AS nogroupCost,
                            IFNULL(SUM(CASE WHEN d.dise_type = 1 THEN IFNULL(d.sumfee,0) ELSE NULL END),0) AS upCost,
                            IFNULL(SUM(CASE WHEN d.dise_type = 2 THEN IFNULL(d.sumfee,0) ELSE NULL END),0) AS lowCost,
                            IFNULL(SUM(CASE WHEN d.dise_type = 3 THEN IFNULL(d.sumfee,0) ELSE NULL END),0) AS NCCost,
                            IFNULL(SUM(CASE WHEN d.dise_type = 4 THEN IFNULL(d.sumfee,0) ELSE NULL END),0) AS nobkCost,
                            IFNULL(SUM(CASE WHEN d.dise_type = 5 THEN IFNULL(d.sumfee,0) ELSE NULL END),0) AS instabilityCost,
                            IFNULL(SUM(CASE WHEN d.dise_type = 6 THEN IFNULL(d.sumfee,0) ELSE NULL END),0) AS exhCost,
                            IFNULL( SUM( CASE WHEN d.dise_type not in (0,1,2,3,4,5,6) THEN IFNULL( d.sumfee, 0 ) ELSE NULL END ), 0 ) AS othFee,


                            IFNULL( SUM( CASE WHEN d.dise_type = 0 THEN IFNULL( d.preFundFee, 0 ) ELSE NULL END ), 0 ) AS nogroupPreFund,
                            IFNULL( SUM( CASE WHEN d.dise_type = 1 THEN IFNULL( d.preFundFee, 0 ) ELSE NULL END ), 0 ) AS upPreFund,
                            IFNULL( SUM( CASE WHEN d.dise_type = 2 THEN IFNULL( d.preFundFee, 0 ) ELSE NULL END ), 0 ) AS lowPreFund,
                            IFNULL( SUM( CASE WHEN d.dise_type = 3 THEN IFNULL( d.preFundFee, 0 ) ELSE NULL END ), 0 ) AS ncPreFund,
                            IFNULL( SUM( CASE WHEN d.dise_type = 4 THEN IFNULL( d.preFundFee, 0 ) ELSE NULL END ), 0 ) AS nobkPreFund,
                            IFNULL( SUM( CASE WHEN d.dise_type = 5 THEN IFNULL( d.preFundFee, 0 ) ELSE NULL END ), 0 ) AS instabilityPreFund,
                            IFNULL( SUM( CASE WHEN d.dise_type = 6 THEN IFNULL( d.preFundFee, 0 ) ELSE NULL END ), 0 ) AS exhPreFund,
                            IFNULL(SUM( CASE WHEN d.dise_type NOT IN ( 0, 1, 2, 3, 4, 5, 6 ) THEN IFNULL( d.preFundFee, 0 ) ELSE NULL END ),0 ) AS otherPreFund,

                            IFNULL( SUM( CASE WHEN d.dise_type = 0 THEN IFNULL( d.fundAmtSum, 0 ) ELSE NULL END ), 0 ) AS nogroupFundAmt,
                            IFNULL( SUM( CASE WHEN d.dise_type = 1 THEN IFNULL( d.fundAmtSum, 0 ) ELSE NULL END ), 0 ) AS upFundAmt,
                            IFNULL( SUM( CASE WHEN d.dise_type = 2 THEN IFNULL( d.fundAmtSum, 0 ) ELSE NULL END ), 0 ) AS lowFundAmt,
                            IFNULL( SUM( CASE WHEN d.dise_type = 3 THEN IFNULL( d.fundAmtSum, 0 ) ELSE NULL END ), 0 ) AS ncFundAmt,
                            IFNULL( SUM( CASE WHEN d.dise_type = 4 THEN IFNULL( d.fundAmtSum, 0 ) ELSE NULL END ), 0 ) AS nobkFundAmt,
                            IFNULL( SUM( CASE WHEN d.dise_type = 5 THEN IFNULL( d.fundAmtSum, 0 ) ELSE NULL END ), 0 ) AS instabilityFundAmt,
                            IFNULL( SUM( CASE WHEN d.dise_type = 6 THEN IFNULL( d.fundAmtSum, 0 ) ELSE NULL END ), 0 ) AS exhFundAmt,
                            IFNULL( SUM( CASE WHEN d.dise_type NOT IN ( 0, 1, 2, 3, 4, 5, 6 ) THEN IFNULL( d.fundAmtSum, 0 ) ELSE NULL END ),0 ) AS othFundAmt,
 IFNULL(SUM(CASE WHEN d.dise_type = 0 THEN IFNULL(a.pre_hosp_examfee,0) ELSE NULL END),0) AS nogroupPreHosCost,
                                                    IFNULL(SUM(CASE WHEN d.dise_type = 1 THEN IFNULL(a.pre_hosp_examfee,0) ELSE NULL END),0) AS upPreHosCost,
                                                    IFNULL(SUM(CASE WHEN d.dise_type = 2 THEN IFNULL(a.pre_hosp_examfee,0) ELSE NULL END),0) AS lowPreHosCost,
                                                    IFNULL(SUM(CASE WHEN d.dise_type = 3 THEN IFNULL(a.pre_hosp_examfee,0) ELSE NULL END),0) AS ncPreHosCost,
                                                    IFNULL(SUM(CASE WHEN d.dise_type = 4 THEN IFNULL(a.pre_hosp_examfee,0) ELSE NULL END),0) AS nobkPreHosCost,
                                                    IFNULL(SUM(CASE WHEN d.dise_type = 5 THEN IFNULL(a.pre_hosp_examfee,0) ELSE NULL END),0) AS instabilityPreHosCost,
                                                    IFNULL(SUM(CASE WHEN d.dise_type = 6 THEN IFNULL(a.pre_hosp_examfee,0) ELSE NULL END),0) AS exhPreHosCost,
                                                    IFNULL( SUM( CASE WHEN d.dise_type not in (0,1,2,3,4,5,6) THEN IFNULL( a.pre_hosp_examfee, 0 ) ELSE NULL END ), 0 ) AS otherPreHosCost



                        ]]>
            </when>
            <when test="feeStas == 1">
                ,
                CASE
                WHEN ifnull(t1.hi_type, e.insu_type)  IN ('1', '01', '310') THEN '1'
                WHEN ifnull(t1.hi_type, e.insu_type)  IN ('2', '02', '390') THEN '2'
                ELSE '99'
                END AS insuTypeGroup
                <!--                        e.INSURED_TYPE AS insuType,-->
                IFNULL(SUM(CASE WHEN e.dise_type = 1 THEN e.setl_sco ELSE NULL END),0) AS upPoint,
                IFNULL(SUM(CASE WHEN e.dise_type = 2 THEN e.setl_sco ELSE NULL END),0) AS lowPoint,
                IFNULL(SUM(CASE WHEN e.dise_type = 3 THEN e.setl_sco ELSE NULL END),0) AS NCPoint,
                IFNULL( SUM( CASE WHEN e.dise_type not in (1,2,3) THEN IFNULL( e.setl_sco, 0 ) ELSE NULL END ), 0 ) AS otherPoint,
                IFNULL(SUM(CASE WHEN e.dise_type = 1 THEN e.ipt_sumfee ELSE NULL END),0) AS upCost,
                IFNULL(SUM(CASE WHEN e.dise_type = 2 THEN e.ipt_sumfee ELSE NULL END),0) AS lowCost,
                IFNULL(SUM(CASE WHEN e.dise_type = 3 THEN e.ipt_sumfee ELSE NULL END),0) AS NCCost,
                IFNULL( SUM( CASE WHEN e.dise_type not in (1,2,3) THEN IFNULL( e.ipt_sumfee, 0 ) ELSE NULL END ), 0 ) AS othFee
            </when>
        </choose>
        FROM som_dip_grp_info a
        LEFT JOIN som_hi_invy_bas_info q
        ON q.ID = a.SETTLE_LIST_ID
        LEFT JOIN som_setl_info t1
        on t1.unique_id = q.k00
        <if test='enableSeAns != null and enableSeAns != "" and enableSeAns == "1"'>
            INNER JOIN som_setl_cas_crsp p
            ON q.k00 = p.k00
        </if>
        LEFT JOIN som_dip_sco d
        ON a.SETTLE_LIST_ID = d.SETTLE_LIST_ID

        <if test="feeStas == 1">
            INNER JOIN
            (
            SELECT a.dip_codg,
            a.DIP_NAME,
            a.rid_idt_codg,
            a.medcas_codg,
            a.adm_time,
            a.dscg_time,
            a.is_in_group,
            b.bas_sco,
            b.incr_sco,
            b.sumfee AS ipt_sumfee,
            b.INSURED_TYPE,
            b.MED_TYPE AS dise_type,
            b.setl_pt_val,
            b.dfr_fee AS ycCost,
            b.MED_TYPE,
            b.setl_sco,
            a.used_asst_list,
            a.asst_list_age_grp,
            a.asst_list_dise_sev_deg,
            a.asst_list_tmor_sev_deg
            FROM som_dip_grp_fbck a
            LEFT JOIN som_fund_dfr_fbck b
            ON a.rid_idt_codg = b.rid_idt_codg
            WHERE a.dscg_time BETWEEN #{cy_start_date,jdbcType=VARCHAR} AND #{cy_end_date,jdbcType=VARCHAR}
            ) e
            ON a.PATIENT_ID = e.medcas_codg
            AND SUBSTR(a.adm_time,1,10) = e.adm_time
            AND SUBSTR(a.dscg_time,1,10) = e.dscg_time
        </if>
        WHERE 1=1
        <include refid="Convert_Cort"></include>
        <include refid="com.my.som.mapper.newBusiness.common.NewBusinessCommonAnalysisMapper.queryBusCommon"></include>
        <if test="inStartTime != null and inStartTime != '' and inEndTime != null and inEndTime != ''">
            AND a.adm_time BETWEEN #{inStartTime} AND CONCAT(#{inEndTime},' 23:59:59')
        </if>
        <if test="(cy_start_date != null and cy_start_date !='') or inHosFlag == 1">
            <![CDATA[
                        AND a.dscg_time BETWEEN #{cy_start_date,jdbcType=VARCHAR} AND #{cy_end_date,jdbcType=VARCHAR}
                    ]]>
        </if>
        <if test="seStartTime != null and seStartTime != '' and seEndTime != null and seEndTime != ''">
            AND a.setl_end_time BETWEEN #{seStartTime} AND CONCAT(#{seEndTime},' 23:59:59')
        </if>
        GROUP BY insuType
        ) a
        CROSS JOIN
        (
        SELECT MAX(CASE WHEN `key` = 'CX_PRICE' THEN `VALUE` ELSE NULL END) AS cxPrice,
        MAX(case when `key` = 'CZ_PRICE' THEN `VALUE` ELSE NULL END) AS czPrice,
        MAX(case when `key` = 'PRICE' THEN `VALUE` ELSE NULL END) AS price
        FROM som_dip_gen_cfg
        WHERE `type` = 'PREDICTED_PRICE'
        GROUP BY `type`
        ) b
    </select>

    <!-- 查询前几月数据 -->
    <select id="getMonthData" resultType="com.my.som.vo.dipBusiness.DipPayToPredictVo">
        SELECT c.month,
        ROUND(SUM(c.payCost)/10000,2) AS sumfee,
        ROUND(SUM(c.ycCost)/10000,2) AS predictCost,
        <choose>
            <when test="feeStas == 0">
                ROUND(SUM(c.profitloss),2) AS cyb
            </when>
            <when test="feeStas == 1">
                ROUND((SUM(c.ycCost)-SUM(c.payCost))/SUM(c.ycCost) * 100,2) AS cyb
            </when>
        </choose>

        FROM
        (
        SELECT a.month,
        a.payCost,
        <choose>
            <when test="feeStas == 0">
                a.profitloss AS profitloss,
                a.ycCost AS ycCost
            </when>
            <when test="feeStas == 1">
                a.cost as ycCost
            </when>
        </choose>

        FROM
        (
        SELECT
        SUBSTR(a.${busKeyField},1,7) AS month,
        <choose>
            <when test="feeStas == 0">
                SUM(a.ipt_sumfee) AS payCost,
                c.insu_type AS insuType,
                SUM(c.profitloss) AS profitloss,
                SUM(c.forecast_fee) AS ycCost
            </when>
            <when test="feeStas == 1">
                SUM(e.ipt_sumfee) AS payCost,
                e.INSURED_TYPE AS insuType,
                SUM(e.ycCost) cost
            </when>
        </choose>
        FROM som_dip_grp_info a
        <if test='enableSeAns != null and enableSeAns != "" and enableSeAns == "1"'>
            LEFT JOIN som_hi_invy_bas_info q
            ON q.ID = a.SETTLE_LIST_ID
            INNER JOIN som_setl_cas_crsp p
            ON q.k00 = p.k00
        </if>
        LEFT JOIN som_dip_sco c
        ON a.SETTLE_LIST_ID = c.SETTLE_LIST_ID
        <if test="feeStas == 1">
            INNER JOIN
            (
            SELECT
            a.dip_codg,
            a.DIP_NAME,
            a.rid_idt_codg,
            a.medcas_codg,
            a.adm_time,
            a.dscg_time,
            a.is_in_group,
            b.bas_sco,
            b.incr_sco,
            b.sumfee AS ipt_sumfee,
            b.INSURED_TYPE,
            b.MED_TYPE AS dise_type,
            b.setl_pt_val,
            b.dfr_fee AS ycCost,
            b.MED_TYPE,
            b.setl_sco,
            a.used_asst_list,
            a.asst_list_age_grp,
            a.asst_list_dise_sev_deg,
            a.asst_list_tmor_sev_deg
            FROM som_dip_grp_fbck a
            LEFT JOIN som_fund_dfr_fbck b
            ON a.rid_idt_codg = b.rid_idt_codg
            <![CDATA[
                            WHERE SUBSTR(a.${busKeyField},1,7) >= #{begnDate,jdbcType=VARCHAR} AND SUBSTR(a.${busKeyField},1,7) <= #{expiDate,jdbcType=VARCHAR}
                            ]]>
            ) e
            ON a.PATIENT_ID = e.medcas_codg
            AND SUBSTR(a.adm_time,1,10) = e.adm_time
            AND SUBSTR(a.dscg_time,1,10) = e.dscg_time
        </if>
        <where>
            <include refid="Convert_Cort"></include>
            <include
                    refid="com.my.som.mapper.newBusiness.common.NewBusinessCommonAnalysisMapper.queryBusCommon"></include>
        </where>
        GROUP BY
        <choose>
            <when test="feeStas == 0">
                c.insu_type,

            </when>
            <when test="feeStas == 1">
                e.INSURED_TYPE,
            </when>
        </choose>
        SUBSTR(a.${busKeyField},1,7)
        ) a

        ) c
        <![CDATA[
        WHERE c.month >= #{begnDate,jdbcType=VARCHAR}
          AND c.month <= #{expiDate,jdbcType=VARCHAR}
        ]]>
        GROUP BY c.month
        ORDER BY c.month ASC
    </select>

    <!-- 查询基础信息 -->
    <select id="getPatientScoreData" resultType="com.my.som.vo.dipBusiness.DipPayToPredictVo">
        SELECT SUBSTR(a.dscg_time,1,7) AS ym,
        a.SETTLE_LIST_ID AS settleListId,
        a.PATIENT_ID AS patientId,
        a.`NAME` AS name,
        a.AGE AS age,
        a.dscg_caty_codg_inhosp AS deptCode,
        a.HOSPITAL_ID AS hospitalId,
        IFNULL(b.refer_sco,0) AS refer_sco,
        IFNULL(b.adjm_cof,0) AS adjm_cof,
        IFNULL(b.uplmt_mag,0) AS uplmtMag,
        IFNULL(b.lowlmt_mag,0) AS lowlmtMag,
        a.ipt_sumfee AS inHosTotalCost,
        d.hosp_lv AS hospLv,
        IFNULL(a.pre_hosp_examfee,0) AS preHospExamfee,

        IFNULL(c.D22,0)+IFNULL(c.D64,0)+IFNULL(c.D24,0)+IFNULL(c.D25,0) AS TCMTreatmentCost,
        IFNULL(c.D22,0)+IFNULL(c.D64,0)+IFNULL(c.D24,0)+IFNULL(c.D25,0)+IFNULL(c.D19,0)+IFNULL(c.D20,0)+IFNULL(c.D23,0)+IFNULL(c.D26,0)+IFNULL(c.D27,0)+IFNULL(c.D28,0)+IFNULL(c.D29,0)+IFNULL(c.D30,0)+IFNULL(c.D32,0)+IFNULL(c.D33,0) AS hospitalizationExpenses,

        <choose>
            <when test="queryType == 1">
                a.dip_codg AS dipCodg,
                a.DIP_NAME AS dipName,
                IFNULL(CONVERT(AES_DECRYPT(UNHEX(b.dip_standard_avg_fee_same_lv),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) USING utf8),0) AS levelStandardCost,
                IFNULL(CONVERT(AES_DECRYPT(UNHEX(b.last_year_avg_fee),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) USING utf8),0) AS lastYearLevelStandardCost,
                c.D22 AS TCMCost,
                c.C03C AS WMCode,
                c.C37C AS TCMCode,
                c.a46c AS insuredType,
                e.high_fee AS max,
                e.min_fee AS min
            </when>
            <when test="queryType == 3">
                a.drg_codg AS drgCodg,
                a.DRG_NAME AS drgName,
                CONVERT(AES_DECRYPT(UNHEX(b.standard_avg_fee),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) USING utf8) AS levelStandardCost,
                CONVERT(AES_DECRYPT(UNHEX(b.standard_avg_fee),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) USING utf8) AS lastYearLevelStandardCost,
                CASE WHEN c.A46C = 01 OR c.A46C = 1 THEN 1
                WHEN c.A46C = 02 OR c.A46C = 2 THEN 2
                WHEN c.A46C = 03 OR c.A46C = 3 THEN 3
                WHEN c.A46C = 04 OR c.A46C = 4 THEN 4
                WHEN c.A46C = 05 OR c.A46C = 5 THEN 5
                WHEN c.A46C = 06 OR c.A46C = 6 THEN 6
                WHEN c.A46C = 07 OR c.A46C = 7 THEN 7
                WHEN c.A46C = 08 OR c.A46C = 8 THEN 8
                WHEN c.A46C = 99 OR c.A46C = 9 THEN 9
                ELSE 9 END AS insuredType,
                CASE WHEN e.high_fee IS NOT NULL AND e.high_fee != '' THEN e.high_fee ELSE CONVERT(AES_DECRYPT(UNHEX(b.standard_avg_fee),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) USING utf8) * b.uplmt_mag END AS max,
                CASE WHEN e.min_fee IS NOT NULL AND e.min_fee != '' THEN e.min_fee ELSE CONVERT(AES_DECRYPT(UNHEX(b.standard_avg_fee),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) USING utf8) * b.lowlmt_mag END min
            </when>
        </choose>
        <choose>
            <when test="queryType == 1">
                FROM som_dip_grp_info a
                LEFT JOIN som_dip_standard b
                ON SUBSTR(a.dscg_time,1,4) = b.STANDARD_YEAR
                AND a.dip_codg = b.dip_codg
                AND a.is_used_asst_list = b.is_used_asst_list
                AND a.asst_list_age_grp = b.asst_list_age_grp
                AND a.asst_list_dise_sev_deg = b.asst_list_dise_sev_deg
                AND a.asst_list_tmor_sev_deg = b.asst_list_tmor_sev_deg
                AND a.HOSPITAL_ID = b.HOSPITAL_ID
            </when>
            <when test="queryType == 3">
                FROM som_drg_grp_info a
                LEFT JOIN som_drg_standard b
                ON SUBSTR(a.dscg_time,1,4) = b.STANDARD_YEAR
                AND a.drg_codg = b.drg_codg
                AND a.HOSPITAL_ID = b.HOSPITAL_ID
            </when>
        </choose>
        LEFT JOIN som_hi_invy_bas_info c
        ON a.SETTLE_LIST_ID = c.ID
        LEFT JOIN som_hosp_info d
        ON a.HOSPITAL_ID = d.HOSPITAL_ID
        <choose>
            <when test="queryType == 1">
                LEFT JOIN som_dip_supe_ultra_low_bind e
                ON SUBSTR(a.dscg_time,1,4) = e.`YEAR`
                AND a.HOSPITAL_ID = e.HOSPITAL_ID
                AND a.dip_codg = e.`CODE`
                AND a.is_used_asst_list = e.is_used_asst_list
                AND a.asst_list_age_grp = e.asst_list_age_grp
                AND a.asst_list_dise_sev_deg = e.asst_list_dise_sev_deg
                AND a.asst_list_tmor_sev_deg = e.asst_list_tmor_sev_deg
                AND e.TYPE = 1
            </when>
            <when test="queryType == 3">
                LEFT JOIN som_drg_supe_ultra_low_bind e
                ON SUBSTR(a.dscg_time,1,4) = e.`YEAR`
                AND a.HOSPITAL_ID = e.HOSPITAL_ID
                AND a.drg_codg = e.CODE
            </when>
        </choose>
        WHERE SUBSTR(a.dscg_time,1,7) = #{ym}
        <include refid="com.my.som.mapper.newBusiness.common.NewBusinessCommonAnalysisMapper.queryBusCommon"></include>
    </select>

    <!-- 修改分值 -->
    <insert id="insertPatientScore">
        INSERT INTO som_dip_sco
        (
        ym,
        SETTLE_LIST_ID,
        `NAME`,
        insu_type,
        dip_codg,
        DIP_NAME,
        setl_sco,
        incr_sco,
        totl_sco,
        refer_sco,
        adjm_cof,
        uplmt_mag,
        tcm_adt_cof,
        grst_cof,
        young_cof,
        key_disc_cof,
        hosp_cof,
        dise_type,
        HOSPITAL_ID
        )
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (
            #{item.ym},
            #{item.settleListId},
            #{item.name},
            #{item.insuType},
            #{item.dipCodg},
            #{item.dipName},
            #{item.calculateScore},
            #{item.addScore},
            #{item.totlSco},
            #{item.refer_sco},
            #{item.adjm_cof},
            #{item.uplmtMag},
            #{item.dominantDiseaseRate},
            #{item.baseDiseaseRate},
            #{item.youngerDiseaseRate},
            #{item.professionalDiseaseRate},
            #{item.hospCof},
            #{item.diseType},
            #{item.hospitalId,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>

    <insert id="insertDrgPatientScore">
        INSERT INTO som_drg_sco
        (
        ym,
        SETTLE_LIST_ID,
        `NAME`,
        insu_type,
        drg_codg,
        DRG_NAME,
        setl_sco,
        incr_sco,
        totl_sco,
        refer_sco,
        adjm_cof,
        uplmt_mag,
        tcm_adt_cof,
        grst_cof,
        young_cof,
        key_disc_cof,
        hosp_cof,
        dise_type,
        price,
        sumfee,
        forecast_fee,
        profitloss,
        HOSPITAL_ID,
        medcasno,
        insuplc_admdvs
        )
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (
            #{item.ym},
            #{item.settleListId},
            #{item.name},
            <choose>
                <when test="item.insuType == 310 or item.insuType == 1 or item.insuType == 01">1</when>
                <when test="item.insuType == 390 or item.insuType == 2 or item.insuType == 02">2</when>
                <otherwise>
                    #{item.insuType}
                </otherwise>
            </choose>,
            #{item.drgCodg},
            #{item.drgName},
            #{item.calculateScore},
            #{item.addScore},
            #{item.totlSco},
            #{item.refer_sco},
            #{item.adjm_cof},
            #{item.uplmtMag},
            #{item.dominantDiseaseRate},
            #{item.baseDiseaseRate},
            #{item.youngerDiseaseRate},
            #{item.professionalDiseaseRate},
            #{item.hospCof},
            #{item.diseType},
            #{item.price},
            #{item.sumfee},
            #{item.forecast_fee},
            #{item.profitloss},
            #{item.hospitalId,jdbcType=VARCHAR},
            #{item.patientId,jdbcType=VARCHAR},
            #{item.insuplcAdmdvs,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>

    <insert id="addPatientScore">
        INSERT INTO som_dip_sco
        (
        ym,
        SETTLE_LIST_ID,
        `NAME`,
        insu_type,
        dip_codg,
        DIP_NAME,
        setl_sco,
        incr_sco,
        totl_sco,
        refer_sco,
        adjm_cof,
        uplmt_mag,
        tcm_adt_cof,
        grst_cof,
        young_cof,
        key_disc_cof,
        hosp_cof,
        dise_type,
        price,
        sumfee,  <!-- 最终结算费用  -->
        forecast_fee,
        profitloss,
        HOSPITAL_ID,
        tcm_org_cof,
        medcasno,pre_hosp_examfee,in_hosp_cost,medicare_settlement_cost,fundAmtSum,presonBearCost,preFundFee,fundRatio,fundSourceType,fundRatioType
        )
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (
            #{item.ym},
            #{item.settleListId},
            #{item.name},
            #{item.insuType},
            #{item.dipCodg},
            #{item.dipName},
            #{item.settleMentScore},
            #{item.addItiveValue},
            #{item.totlSco},
            #{item.refer_sco},
            #{item.adjm_cof},
            #{item.uplmtMag},
            #{item.tcmAdtCof},
            #{item.grstCof},
            #{item.youngCof},
            #{item.professionalRate},
            #{item.hospCof},
            #{item.diseType},
            #{item.price},
            <!--            #{item.sumfee},-->
            <choose>
                <when test="item.businessSerial != null and item.businessSerial != ''
                        and item.checkTotalFee != null and item.checkTotalFee != ''
                        and item.checkTotalFee != 0">
                    #{item.checkTotalFee} + #{item.preHospExamfee},
                </when>
                <otherwise>
                    #{item.inHospCost} + #{item.preHospExamfee},
                </otherwise>
            </choose>
            #{item.forecast_fee},
            #{item.profitloss},
            #{item.hospitalId,jdbcType=VARCHAR},
            #{item.tcmOrgCof,},
            #{item.patientId,jdbcType=VARCHAR},
            #{item.preHospExamfee,jdbcType=VARCHAR},
            #{item.inHospCost,jdbcType=VARCHAR},
            #{item.checkTotalFee,jdbcType=VARCHAR},
            #{item.fundAmtSum},
            #{item.presonBearCost},
            #{item.preFundFee},
            #{item.fundRatio},
            #{item.fundSourceType},
            #{item.fundRatioType}
            )
        </foreach>
    </insert>

    <!-- 删除分值 -->
    <delete id="detelePatientScore">
        DELETE FROM som_dip_sco WHERE ym = #{ym}
        <if test="hospitalId != null and hospitalId != ''">
            AND HOSPITAL_ID = #{hospitalId,jdbcType=VARCHAR}
        </if>
    </delete>

    <!-- 查询分值 -->
    <select id="selectPatientScore" resultType="com.my.som.vo.dipBusiness.DipPayToPredictVo">
        SELECT ym AS ym,
        SETTLE_LIST_ID AS patientId,
        `NAME` AS name,
        dip_codg AS dipCodg,
        DIP_NAME AS dipName,
        setl_sco AS calculateScore,
        incr_sco AS addScore,
        totl_sco AS totlSco
        FROM som_dip_sco
        WHERE ym = #{ym}
        <if test="hospitalId != null and hospitalId != ''">
            AND HOSPITAL_ID = #{hospitalId,jdbcType=VARCHAR}
        </if>
    </select>

    <!-- 查询指定月份的病人基础信息 -->
    <select id="getPatientScore" resultType="com.my.som.vo.dipBusiness.DipPayToPredictVo">
        SELECT SUBSTR(a.dscg_time,1,7) AS ym,
        a.SETTLE_LIST_ID AS settleListID,
        a.PATIENT_ID AS patientId,
        a.`NAME` AS name,
        a.AGE AS age,
        a.dscg_caty_codg_inhosp AS deptCode,
        a.HOSPITAL_ID AS hospitalId,
        IFNULL(b.refer_sco,0) AS refer_sco,
        IFNULL(b.adjm_cof,0) AS adjm_cof,
        IFNULL(b.uplmt_mag,0) AS uplmtMag,
        IFNULL(b.lowlmt_mag,0) AS lowlmtMag,
        a.ipt_sumfee AS inHosTotalCost,
        d.hosp_lv AS hospLv,
        d.hosp_cof AS hospCof,
        IFNULL(a.pre_hosp_examfee,0) AS preHospExamfee,
        b.is_sd_dise AS stableFlag,

        IFNULL(c.D22,0)+IFNULL(c.D64,0)+IFNULL(c.D24,0)+IFNULL(c.D25,0) AS TCMTreatmentCost,
        IFNULL(c.D22,0)+IFNULL(c.D64,0)+IFNULL(c.D24,0)+IFNULL(c.D25,0)+IFNULL(c.D19,0)+IFNULL(c.D20,0)+IFNULL(c.D23,0)+IFNULL(c.D26,0)+IFNULL(c.D27,0)+IFNULL(c.D28,0)+IFNULL(c.D29,0)+IFNULL(c.D30,0)+IFNULL(c.D32,0)+IFNULL(c.D33,0) AS hospitalizationExpenses,

        <choose>
            <when test="queryType == 1">
                a.dip_codg AS dipCodg,
                a.DIP_NAME AS dipName,
                a.grp_stas AS grpStas,
                IFNULL(CONVERT(AES_DECRYPT(UNHEX(b.dip_standard_inpf),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) USING utf8),0) AS areaStandardCost,
                IFNULL(CONVERT(AES_DECRYPT(UNHEX(b.last_year_avg_fee),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) USING utf8),0) AS levelCost,
                IFNULL(CONVERT(AES_DECRYPT(UNHEX(b.dip_standard_avg_fee_same_lv),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) USING utf8),0) AS lastYearLevelStandardCost,
                IFNULL(CAST(CONVERT(AES_DECRYPT(UNHEX(b.dip_standard_inpf),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) USING utf8)/b.refer_sco * 100 as decimal(8,4)),0) AS diseaseAverageCost,
                c.D22 AS TCMCost,
                c.C03C AS WMCode,
                c.C37C AS TCMCode,
                c.a46c AS insuredType,
                e.high_fee AS max,
                e.min_fee AS min,
                #{queryType,jdbcType=VARCHAR} AS queryType
            </when>
            <when test="queryType == 3">
                a.drg_codg AS drgCodg,
                a.DRG_NAME AS drgName,
                CONVERT(AES_DECRYPT(UNHEX(b.standard_avg_fee),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) USING utf8) AS levelStandardCost,
                CONVERT(AES_DECRYPT(UNHEX(b.standard_avg_fee),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) USING utf8) AS lastYearLevelStandardCost,
                CASE WHEN c.A46C = 01 OR c.A46C = 1 THEN 1
                WHEN c.A46C = 02 OR c.A46C = 2 THEN 2
                WHEN c.A46C = 03 OR c.A46C = 3 THEN 3
                WHEN c.A46C = 04 OR c.A46C = 4 THEN 4
                WHEN c.A46C = 05 OR c.A46C = 5 THEN 5
                WHEN c.A46C = 06 OR c.A46C = 6 THEN 6
                WHEN c.A46C = 07 OR c.A46C = 7 THEN 7
                WHEN c.A46C = 08 OR c.A46C = 8 THEN 8
                WHEN c.A46C = 99 OR c.A46C = 9 THEN 9
                ELSE 9 END AS insuredType,
                CASE WHEN e.high_fee IS NOT NULL AND e.high_fee != '' THEN e.high_fee ELSE CONVERT(AES_DECRYPT(UNHEX(b.standard_avg_fee),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) USING utf8) * b.uplmt_mag END AS max,
                CASE WHEN e.min_fee IS NOT NULL AND e.min_fee != '' THEN e.min_fee ELSE CONVERT(AES_DECRYPT(UNHEX(b.standard_avg_fee),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) USING utf8) * b.lowlmt_mag END min,
                #{queryType,jdbcType=VARCHAR} AS queryType
            </when>
        </choose>
        <choose>
            <when test="queryType == 1">
                FROM som_dip_grp_info a
                LEFT JOIN som_dip_standard b
                ON SUBSTR(a.dscg_time,1,4) = b.STANDARD_YEAR
                AND a.dip_codg = b.dip_codg
                AND a.is_used_asst_list = b.is_used_asst_list
                AND a.asst_list_age_grp = b.asst_list_age_grp
                AND a.asst_list_dise_sev_deg = b.asst_list_dise_sev_deg
                AND a.asst_list_tmor_sev_deg = b.asst_list_tmor_sev_deg
                AND a.HOSPITAL_ID = b.HOSPITAL_ID
            </when>
            <when test="queryType == 3">
                FROM som_drg_grp_info a
                LEFT JOIN som_drg_standard b
                ON SUBSTR(a.dscg_time,1,4) = b.STANDARD_YEAR
                AND a.drg_codg = b.drg_codg
                AND a.HOSPITAL_ID = b.HOSPITAL_ID
            </when>
        </choose>
        LEFT JOIN som_hi_invy_bas_info c
        ON a.SETTLE_LIST_ID = c.ID
        LEFT JOIN som_hosp_info d
        ON a.HOSPITAL_ID = d.HOSPITAL_ID
        <choose>
            <when test="queryType == 1">
                LEFT JOIN som_dip_supe_ultra_low_bind e
                ON SUBSTR(a.dscg_time,1,4) = e.`YEAR`
                AND a.HOSPITAL_ID = e.HOSPITAL_ID
                AND a.dip_codg = e.`CODE`
                AND a.is_used_asst_list = e.is_used_asst_list
                AND a.asst_list_age_grp = e.asst_list_age_grp
                AND a.asst_list_dise_sev_deg = e.asst_list_dise_sev_deg
                AND a.asst_list_tmor_sev_deg = e.asst_list_tmor_sev_deg
                AND e.TYPE = 1
            </when>
            <when test="queryType == 3">
                LEFT JOIN som_drg_supe_ultra_low_bind e
                ON SUBSTR(a.dscg_time,1,4) = e.`YEAR`
                AND a.HOSPITAL_ID = e.HOSPITAL_ID
                AND a.drg_codg = e.CODE
            </when>
        </choose>
        WHERE SUBSTR(a.dscg_time,1,7) = #{ym}
        <include refid="com.my.som.mapper.newBusiness.common.NewBusinessCommonAnalysisMapper.queryBusCommon"></include>
    </select>

    <sql id="Convert_Cort">
        <if test="dipGroup != null and dipGroup != ''">
            <!--and (-->
            <!--       instr(a.dip_codg, #{dipGroup,jdbcType=VARCHAR}) > 0-->
            <!--       or-->
            <!--        instr(a.DIP_NAME, #{dipGroup,jdbcType=VARCHAR}) > 0-->
            <!--     )-->
            and a.dip_codg = #{dipGroup,jdbcType=VARCHAR}
        </if>

        <if test="recordType != null and recordType != ''">
            and a.medcas_type = #{recordType}
        </if>

        <if test="dept != null and dept !=''">
            and a.dscg_caty_codg_inhosp = #{dept}
        </if>
    </sql>

</mapper>
