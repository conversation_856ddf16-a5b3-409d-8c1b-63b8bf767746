<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.som.dao.dipBusiness.DipAnalysisDao">
    <select id="list" resultType="com.my.som.vo.dipBusiness.DipAnalysisVo">
        SELECT
               a.dip_codg AS dipCodg,
               a.DIP_NAME AS dipName,
               a.is_used_asst_list AS isUsedAsstList,
               a.asst_list_age_grp AS asstListAgeGrp,
               a.asst_list_dise_sev_deg AS asstListDiseSevDeg,
               a.asst_list_tmor_sev_deg AS asstListTmorSevDeg,
               COUNT(1) AS inGroupMedicalNum,
               COUNT(DISTINCT(a.dscg_caty_codg_inhosp)) AS deptNum,
               ROUND(IFNULL(AVG(a.ipt_sumfee),0),2) AS avgCost,
               ROUND( IFNULL( AVG( a.act_ipt ), 0 ), 2 ) AS avgDays,
               CONCAT(a.STANDARD_YEAR,'年') AS standardYear,
               ROUND(IFNULL(c.dip_wt,0),2) AS dipWt,
               ROUND(SUM(IFNULL(c.dip_wt,0)),2) AS totalDipWeight,
               ROUND(MAX(IFNULL(AES_DECRYPT(UNHEX(c.dip_standard_avg_fee_same_lv),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}),0)),2) AS peerBenchmarkCost,
               ROUND(IFNULL(SUM(IFNULL(IFNULL(a.ipt_sumfee,0)/NULLIF(AES_DECRYPT(UNHEX(c.dip_standard_avg_fee_same_lv),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}),0),0))/NULLIF(COUNT(1),0),0),2) AS peerBenchmarkCostIndex,
               ROUND(MAX(IFNULL(AES_DECRYPT(UNHEX(c.dip_standard_inpf),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}),0)),2) AS areaCost,
               ROUND(IFNULL(SUM(IFNULL(IFNULL(a.ipt_sumfee,0)/NULLIF(AES_DECRYPT(UNHEX(c.dip_standard_inpf),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}),0),0))/NULLIF(COUNT(1),0),0),2) AS areaCostIndex,
               ROUND(MAX(IFNULL(AES_DECRYPT(UNHEX(c.dip_standard_ipt_days_same_lv),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}),0)),2) AS peerBenchmarkDays,
               ROUND(IFNULL(SUM(IFNULL(IFNULL(a.act_ipt,0)/NULLIF(AES_DECRYPT(UNHEX(c.dip_standard_ipt_days_same_lv),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}),0),0))/NULLIF(COUNT(1),0),0),2) AS peerBenchmarkTimeIndex,
               ROUND(MAX(IFNULL(AES_DECRYPT(UNHEX(c.dip_standard_ipt_days),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}),0)),2) AS areaDays,
               ROUND(IFNULL(SUM(IFNULL(IFNULL(a.act_ipt,0)/NULLIF(AES_DECRYPT(UNHEX(c.dip_standard_ipt_days),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}),0),0))/NULLIF(COUNT(1),0),0),2) AS areaTimeIndex
        FROM som_dip_grp_info a
        <if test='queryParam.enableSeAns != null and queryParam.enableSeAns != "" and queryParam.enableSeAns == "1"'>
            LEFT JOIN som_hi_invy_bas_info q
            ON q.ID = a.SETTLE_LIST_ID
            INNER JOIN som_setl_cas_crsp p
            ON q.k00 = p.k00
        </if>
            LEFT JOIN som_hi_invy_bas_info b
            ON a.SETTLE_LIST_ID = b.ID
                LEFT JOIN som_dip_standard c
                ON a.dip_codg = c.dip_codg
                AND SUBSTR(a.dscg_time,1,4) = c.STANDARD_YEAR
                AND ifnull(c.is_used_asst_list,0) = ifnull(a.is_used_asst_list,0)
                AND ifnull(c.asst_list_age_grp,0) = ifnull(a.asst_list_age_grp,0)
                AND ifnull(c.asst_list_dise_sev_deg,0) = ifnull(a.asst_list_dise_sev_deg,0)
                AND ifnull(c.asst_list_tmor_sev_deg,0) = ifnull(a.asst_list_tmor_sev_deg,0)
                AND a.HOSPITAL_ID = c.HOSPITAL_ID
        WHERE a.grp_stas = '1'
        <if test="queryParam.b16c!=null and queryParam.b16c!=''">
            AND a.dip_codg in (
            select
            distinct dip_codg
            from som_dip_grp_info
            where grp_stas = '1' AND dscg_caty_codg_inhosp = #{queryParam.b16c}
            <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
                AND  HOSPITAL_ID = #{queryParam.hospitalId}
            </if>
            <if test="queryParam.inStartTime != null and queryParam.inStartTime != '' and
                      queryParam.inEndTime != null and queryParam.inEndTime != ''">
                AND adm_time BETWEEN #{queryParam.inStartTime} AND CONCAT(#{queryParam.inEndTime},' 23:59:59')
            </if>
            <if test="(queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and
                        queryParam.cy_end_date!=null and queryParam.cy_end_date!='') or queryParam.inHosFlag == 2">
                AND dscg_time BETWEEN #{queryParam.cy_start_date} and CONCAT(#{queryParam.cy_end_date},' 23:59:59')
            </if>
            <if test="(queryParam.seStartTime!=null and queryParam.seStartTime!='' and
                        queryParam.seEndTime!=null and queryParam.seEndTime!='') or queryParam.inHosFlag == 2">
                AND setl_end_time BETWEEN #{queryParam.seStartTime} and CONCAT(#{queryParam.seEndTime},' 23:59:59')
            </if>
            )
        </if>
        <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
            AND  a.HOSPITAL_ID = #{queryParam.hospitalId}
        </if>
        <if test="queryParam.queryDrg!=null and queryParam.queryDrg!=''">
            AND a.dip_codg = #{queryParam.queryDrg}
        </if>
        <if test="queryParam.inStartTime!=null and queryParam.inStartTime!='' and
        queryParam.inEndTime!=null and queryParam.inEndTime!=''">
            AND a.adm_time BETWEEN #{queryParam.inStartTime} and CONCAT(#{queryParam.inEndTime},' 23:59:59')
        </if>
        <if test="(queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and
        queryParam.cy_end_date!=null and queryParam.cy_end_date!='') or queryParam.inHosFlag == 2">
            AND a.dscg_time BETWEEN #{queryParam.cy_start_date} and CONCAT(#{queryParam.cy_end_date},' 23:59:59')
        </if>
        <if test="(queryParam.seStartTime!=null and queryParam.seStartTime!='' and
        queryParam.seEndTime!=null and queryParam.seEndTime!='') or queryParam.inHosFlag == 2">
            AND a.setl_end_time BETWEEN #{queryParam.seStartTime} and CONCAT(#{queryParam.seEndTime},' 23:59:59')
        </if>
        <if test="queryParam.deptCode != null and queryParam.deptCode != ''">
            AND a.dscg_caty_codg_inhosp = #{queryParam.deptCode}
        </if>
        <if test="queryParam.searchVal != null and queryParam.searchVal != ''">
            and (a.dip_codg like concat('%',#{queryParam.searchVal},'%') or
            a.DIP_NAME like concat('%',#{queryParam.searchVal},'%'))
        </if>
        <if test="queryParam.dateType != null and queryParam.dateType != ''">
            <choose>
                <when test="queryParam.dateType == 1">
                    and substr(a.dscg_time,1,4) = #{queryParam.curYear}
                </when>
                <when test="queryParam.dateType == 2">
                    and substr(a.dscg_time,1,7) = #{queryParam.curMonth}
                </when>
            </choose>
        </if>
        GROUP BY a.dip_codg,
                 a.DIP_NAME,
                 a.STANDARD_YEAR,
                 ROUND(IFNULL(c.dip_wt,0),2),
                 a.is_used_asst_list,
                 a.asst_list_age_grp,
                 a.asst_list_dise_sev_deg,
                 a.asst_list_tmor_sev_deg
        ORDER BY totalDipWeight DESC
    </select>

    <select id="getCountInfo" resultType="java.util.Map">
        select

        <!-- 核心病种 -->
        count(case when not regexp_like(a.DIP_NAME, '(治疗性操作组|诊断性操作组|相关手术组|保守治疗组|治疗性操作|诊断性操作|相关手术|保守治疗)') or a.DIP_NAME IS NULL then 1 else null end) as coreDiseaseNum,
        IFNULL(COUNT(DISTINCT(CASE WHEN not regexp_like(a.DIP_NAME, '(治疗性操作组|诊断性操作组|相关手术组|保守治疗组|治疗性操作|诊断性操作|相关手术|保守治疗)')  or a.DIP_NAME IS NULL THEN a.dip_codg ELSE NULL END)),0) AS coreDiseaseGroupNum,
        IFNULL(CONCAT(ROUND((IFNULL(COUNT(DISTINCT(CASE WHEN not regexp_like(a.DIP_NAME, '(治疗性操作组|诊断性操作组|相关手术组|保守治疗组|治疗性操作|诊断性操作|相关手术|保守治疗)')  or a.DIP_NAME IS NULL THEN a.dip_codg ELSE NULL END)),0)/NULLIF(COUNT(DISTINCT(a.dip_codg)),0))*100,2),'%'),0) AS coreDiseaseGroupNumRate,
        IFNULL(ROUND(SUM(CASE WHEN not regexp_like(a.DIP_NAME, '(治疗性操作组|诊断性操作组|相关手术组|保守治疗组|治疗性操作|诊断性操作|相关手术|保守治疗)')  or a.DIP_NAME IS NULL THEN b.dip_wt ELSE 0 END),2),0) AS coreDiseaseTotalWeight,
        IFNULL(ROUND(SUM(CASE WHEN not regexp_like(a.DIP_NAME, '(治疗性操作组|诊断性操作组|相关手术组|保守治疗组|治疗性操作|诊断性操作|相关手术|保守治疗)')  or a.DIP_NAME IS NULL THEN b.dip_wt ELSE 0 END) /
        NULLIF(COUNT(CASE WHEN not regexp_like(a.DIP_NAME, '(治疗性操作组|诊断性操作组|相关手术组|保守治疗组|治疗性操作|诊断性操作|相关手术|保守治疗)')  or a.DIP_NAME IS NULL THEN 1 ELSE NULL END),0), 2),0) AS coreDiseaseCmi,

        <!-- 综合病种 -->
        count(case when regexp_like(a.DIP_NAME, '(治疗性操作组|诊断性操作组|相关手术组|保守治疗组|治疗性操作|诊断性操作|相关手术|保守治疗)') then 1 else null end) as compDiseaseNum,
        IFNULL(COUNT(DISTINCT(CASE WHEN regexp_like(a.DIP_NAME, '(治疗性操作组|诊断性操作组|相关手术组|保守治疗组|治疗性操作|诊断性操作|相关手术|保守治疗)') THEN a.dip_codg ELSE NULL END)),0) AS compDiseaseGroupNum,
        IFNULL(CONCAT(ROUND((IFNULL(COUNT(DISTINCT(CASE WHEN regexp_like(a.DIP_NAME, '(治疗性操作组|诊断性操作组|相关手术组|保守治疗组|治疗性操作|诊断性操作|相关手术|保守治疗)') THEN a.dip_codg ELSE NULL END)),0)/NULLIF(COUNT(DISTINCT(a.dip_codg)),0))*100,2),'%'),0) AS compDiseaseGroupNumRate,
        IFNULL(ROUND(SUM(CASE WHEN regexp_like(a.DIP_NAME, '(治疗性操作组|诊断性操作组|相关手术组|保守治疗组|治疗性操作|诊断性操作|相关手术|保守治疗)') THEN b.dip_wt ELSE 0 END),2),0) AS compDiseaseTotalWeight,
        IFNULL(ROUND(SUM(CASE WHEN regexp_like(a.DIP_NAME, '(治疗性操作组|诊断性操作组|相关手术组|保守治疗组|治疗性操作|诊断性操作|相关手术|保守治疗)') THEN b.dip_wt ELSE 0 END) /
        NULLIF(COUNT(CASE WHEN regexp_like(a.DIP_NAME, '(治疗性操作组|诊断性操作组|相关手术组|保守治疗组|治疗性操作|诊断性操作|相关手术|保守治疗)') THEN 1 ELSE NULL END),0), 2),0) AS compDiseaseCmi,

        <!-- 诊断性操作 -->
        count(case when (regexp_like(a.DIP_NAME, '诊断性操作组') or regexp_like(a.DIP_NAME, '诊断性操作'))  then 1 else null end) as compC2DiseaseNum,
        IFNULL(COUNT(DISTINCT(CASE WHEN (regexp_like(a.DIP_NAME, '诊断性操作组') or regexp_like(a.DIP_NAME, '诊断性操作')) THEN a.dip_codg ELSE NULL END)),0) AS compC2DiseaseGroupNum,
        IFNULL(CONCAT(ROUND((IFNULL(COUNT(DISTINCT(CASE WHEN (regexp_like(a.DIP_NAME, '诊断性操作组') or regexp_like(a.DIP_NAME, '诊断性操作'))  THEN a.dip_codg ELSE NULL END)),0)/NULLIF(COUNT(DISTINCT(a.dip_codg)),0))*100,2),'%'),0) AS compC2DiseaseGroupNumRate,
        IFNULL(ROUND(SUM(CASE WHEN (regexp_like(a.DIP_NAME, '诊断性操作组') or regexp_like(a.DIP_NAME, '诊断性操作')) THEN b.dip_wt ELSE 0 END),2),0) AS compC2DiseaseTotalWeight,
        IFNULL(ROUND(SUM(CASE WHEN (regexp_like(a.DIP_NAME, '诊断性操作组') or regexp_like(a.DIP_NAME, '诊断性操作'))  THEN b.dip_wt ELSE 0 END) /
        NULLIF(COUNT(CASE WHEN (regexp_like(a.DIP_NAME, '诊断性操作组') or regexp_like(a.DIP_NAME, '诊断性操作')) THEN 1 ELSE NULL END),0), 2),0) AS compC2DiseaseCmi,

        <!-- 治疗性操作 -->
        count(case when (regexp_like(a.DIP_NAME, '治疗性操作组') or regexp_like(a.DIP_NAME, '治疗性操作'))  then 1 else null end) as compC1DiseaseNum,
        IFNULL(COUNT(DISTINCT(CASE WHEN (regexp_like(a.DIP_NAME, '治疗性操作组') or regexp_like(a.DIP_NAME, '治疗性操作'))  THEN a.dip_codg ELSE NULL END)),0) AS compC1DiseaseGroupNum,
        IFNULL(CONCAT(ROUND((IFNULL(COUNT(DISTINCT(CASE WHEN (regexp_like(a.DIP_NAME, '治疗性操作组') or regexp_like(a.DIP_NAME, '治疗性操作'))  THEN a.dip_codg ELSE NULL END)),0)/NULLIF(COUNT(DISTINCT(a.dip_codg)),0))*100,2),'%'),0) AS compC1DiseaseGroupNumRate,
        IFNULL(ROUND(SUM(CASE WHEN (regexp_like(a.DIP_NAME, '治疗性操作组') or regexp_like(a.DIP_NAME, '治疗性操作'))  THEN b.dip_wt ELSE 0 END),2),0) AS compC1DiseaseTotalWeight,
        IFNULL(ROUND(SUM(CASE WHEN (regexp_like(a.DIP_NAME, '治疗性操作组') or regexp_like(a.DIP_NAME, '治疗性操作'))  THEN b.dip_wt ELSE 0 END) /
        NULLIF(COUNT(CASE WHEN (regexp_like(a.DIP_NAME, '治疗性操作组') or regexp_like(a.DIP_NAME, '治疗性操作'))  THEN 1 ELSE NULL END),0), 2),0) AS compC1DiseaseCmi,

        <!-- 相关手术 -->
        count(case when (regexp_like(a.DIP_NAME, '相关手术组') or regexp_like(a.DIP_NAME, '相关手术'))  then 1 else null end) as compC3DiseaseNum,
        IFNULL(COUNT(DISTINCT(CASE WHEN (regexp_like(a.DIP_NAME, '相关手术组') or regexp_like(a.DIP_NAME, '相关手术'))  THEN a.dip_codg ELSE NULL END)),0) AS compC3DiseaseGroupNum,
        IFNULL(CONCAT(ROUND((IFNULL(COUNT(DISTINCT(CASE WHEN (regexp_like(a.DIP_NAME, '相关手术组') or regexp_like(a.DIP_NAME, '相关手术'))  THEN a.dip_codg ELSE NULL END)),0)/NULLIF(COUNT(DISTINCT(a.dip_codg)),0))*100,2),'%'),0) AS compC3DiseaseGroupNumRate,
        IFNULL(ROUND(SUM(CASE WHEN (regexp_like(a.DIP_NAME, '相关手术组') or regexp_like(a.DIP_NAME, '相关手术'))  THEN b.dip_wt ELSE 0 END),2),0) AS compC3DiseaseTotalWeight,
        IFNULL(ROUND(SUM(CASE WHEN (regexp_like(a.DIP_NAME, '相关手术组') or regexp_like(a.DIP_NAME, '相关手术'))  THEN b.dip_wt ELSE 0 END) /
        NULLIF(COUNT(CASE WHEN (regexp_like(a.DIP_NAME, '相关手术组') or regexp_like(a.DIP_NAME, '相关手术'))  THEN 1 ELSE NULL END),0), 2),0) AS compC3DiseaseCmi,

        <!-- 保守治疗 -->
        count(case when (regexp_like(a.DIP_NAME, '保守治疗组') or regexp_like(a.DIP_NAME, '保守治疗')) then 1 else null end) as compC0DiseaseNum,
        IFNULL(COUNT(DISTINCT(CASE WHEN (regexp_like(a.DIP_NAME, '保守治疗组') or regexp_like(a.DIP_NAME, '保守治疗')) THEN a.dip_codg ELSE NULL END)),0) AS compC0DiseaseGroupNum,
        IFNULL(CONCAT(ROUND((IFNULL(COUNT(DISTINCT(CASE WHEN (regexp_like(a.DIP_NAME, '保守治疗组') or regexp_like(a.DIP_NAME, '保守治疗')) THEN a.dip_codg ELSE NULL END)),0)/NULLIF(COUNT(DISTINCT(a.dip_codg)),0))*100,2),'%'),0) AS compC0DiseaseGroupNumRate,
        IFNULL(ROUND(SUM(CASE WHEN (regexp_like(a.DIP_NAME, '保守治疗组') or regexp_like(a.DIP_NAME, '保守治疗')) THEN b.dip_wt ELSE 0 END),2),0) AS compC0DiseaseTotalWeight,
        IFNULL(ROUND(SUM(CASE WHEN (regexp_like(a.DIP_NAME, '保守治疗组') or regexp_like(a.DIP_NAME, '保守治疗'))THEN b.dip_wt ELSE 0 END) /
        NULLIF(COUNT(CASE WHEN (regexp_like(a.DIP_NAME, '保守治疗组') or regexp_like(a.DIP_NAME, '保守治疗')) THEN 1 ELSE NULL END),0), 2),0) AS compC0DiseaseCmi,


        COUNT(CASE WHEN SUBSTR(d.drg_codg,2,1) in ('R','S','T','U','V','W','X','Y','Z')  THEN 1 ELSE NULL END) AS medicalDeptMedicalNum,<!--内科组的DRGs病案数-->
        IFNULL(COUNT(DISTINCT(CASE WHEN SUBSTR(d.drg_codg,2,1) in ('R','S','T','U','V','W','X','Y','Z') AND a.grp_stas = '1' THEN a.dip_codg ELSE NULL END)),0) AS medicalDeptDrgsNum,<!--内科组的DRGs组DRGs组数-->
        IFNULL(CONCAT(ROUND((IFNULL(COUNT(DISTINCT(CASE WHEN SUBSTR(d.drg_codg,2,1) in ('R','S','T','U','V','W','X','Y','Z') THEN d.drg_codg ELSE NULL END)),0)/NULLIF(COUNT(DISTINCT(d.drg_codg)),0))*100,2),'%'),0)
        AS medicalDeptDrgsRate,<!--内科组DRGs组数占比-->
        IFNULL(ROUND(SUM(CASE WHEN SUBSTR(d.drg_codg,2,1) in ('R','S','T','U','V','W','X','Y','Z') THEN b.dip_wt ELSE 0 END),2),0) AS medicalDeptTotalDrgWeight, <!--内科组的DRGs组总权重-->
        IFNULL(ROUND(SUM(CASE WHEN SUBSTR(d.drg_codg,2,1) in ('R','S','T','U','V','W','X','Y','Z') THEN b.dip_wt ELSE 0 END) /
        NULLIF(COUNT(CASE WHEN SUBSTR(d.drg_codg,2,1) in ('R','S','T','U','V','W','X','Y','Z') THEN 1 ELSE NULL END),0), 2),0) AS medicalDeptCmi,  <!--内科组的DRGs组CMI指数-->

        COUNT(CASE WHEN SUBSTR(d.drg_codg,2,1) in ('K','L','M','N','P','Q')  THEN 1 ELSE NULL END) AS notOperationMedicalNum,<!--非手术室操作组的DRGs病案数-->
        IFNULL(COUNT(DISTINCT(CASE WHEN SUBSTR(d.drg_codg,2,1) in ('K','L','M','N','P','Q') AND a.grp_stas = '1' THEN a.dip_codg ELSE NULL END)),0) AS notOperationDrgsNum,<!--非手术室操作组的DRGs组DRGs组数-->
        IFNULL(CONCAT(ROUND((IFNULL(COUNT(DISTINCT(CASE WHEN SUBSTR(d.drg_codg,2,1) in ('K','L','M','N','P','Q') THEN d.drg_codg ELSE NULL END)),0)/NULLIF(COUNT(DISTINCT(d.drg_codg)),0))*100,2),'%'),0)
        AS notOperationDrgsRate,<!--非手术室操作组DRGs组数占比-->
        IFNULL(ROUND(SUM(CASE WHEN SUBSTR(d.drg_codg,2,1) in ('K','L','M','N','P','Q') THEN b.dip_wt ELSE 0 END),2),0) AS notOperationTotalDrgWeight, <!--非手术室操作组的DRGs组总权重-->
        IFNULL(ROUND(SUM(CASE WHEN SUBSTR(d.drg_codg,2,1) in ('K','L','M','N','P','Q') THEN b.dip_wt ELSE 0 END) /
        NULLIF(COUNT(CASE WHEN SUBSTR(d.drg_codg,2,1) in ('K','L','M','N','P','Q') THEN 1 ELSE NULL END),0), 2),0) AS notOperationCmi,  <!--非手术室操作组的DRGs组CMI指数-->

        COUNT(CASE WHEN SUBSTR(d.drg_codg,2,1) in ('A','B','C','D','E','F','G','H','J')  THEN 1 ELSE NULL END) AS surgeryDeptMedicalNum,<!--外科组的DRGs病案数-->
        IFNULL(COUNT(DISTINCT(CASE WHEN SUBSTR(d.drg_codg,2,1) in ('A','B','C','D','E','F','G','H','J') AND a.grp_stas = '1' THEN a.dip_codg ELSE NULL END)),0) AS surgeryDeptDrgsNum,<!--外科组的DRGs组DRGs组数-->
        IFNULL(CONCAT(ROUND((IFNULL(COUNT(DISTINCT(CASE WHEN SUBSTR(d.drg_codg,2,1) in ('A','B','C','D','E','F','G','H','J') THEN d.drg_codg ELSE NULL END)),0)/NULLIF(COUNT(DISTINCT(d.drg_codg)),0))*100,2),'%'),0)
        AS surgeryDeptDrgsRate,<!--外科组DRGs组数占比-->
        IFNULL(ROUND(SUM(CASE WHEN SUBSTR(d.drg_codg,2,1) in ('A','B','C','D','E','F','G','H','J') THEN b.dip_wt ELSE 0 END),2),0) AS surgeryDeptTotalDrgWeight, <!--外科组的DRGs组总权重-->
        IFNULL(ROUND(SUM(CASE WHEN SUBSTR(d.drg_codg,2,1) in ('A','B','C','D','E','F','G','H','J') THEN b.dip_wt ELSE 0 END) /
        NULLIF(COUNT(CASE WHEN SUBSTR(d.drg_codg,2,1) in ('A','B','C','D','E','F','G','H','J') THEN 1 ELSE NULL END),0), 2),0) AS surgeryDeptCmi  <!--外科组的DRGs组CMI指数-->
        from som_dip_grp_info a
        <if test='queryParam.enableSeAns != null and queryParam.enableSeAns != "" and queryParam.enableSeAns == "1"'>
            LEFT JOIN som_hi_invy_bas_info q
            ON q.ID = a.SETTLE_LIST_ID
            INNER JOIN som_setl_cas_crsp p
            ON q.k00 = p.k00
        </if>
        left join som_dip_standard b
        on a.HOSPITAL_ID = b.HOSPITAL_ID
        and a.STANDARD_YEAR = b.STANDARD_YEAR
        and a.dip_codg = b.dip_codg
        and ifnull(a.is_used_asst_list,0) = ifnull(b.is_used_asst_list,0)
        and ifnull(a.asst_list_age_grp,0) = ifnull(b.asst_list_age_grp,0)
        and ifnull(a.asst_list_dise_sev_deg,0) = ifnull(b.asst_list_dise_sev_deg,0)
        and ifnull(a.asst_list_tmor_sev_deg,0) = ifnull(b.asst_list_tmor_sev_deg,0)
        AND a.HOSPITAL_ID = b.HOSPITAL_ID
        left join som_dip_grp_rcd c
        on a.SETTLE_LIST_ID = c.SETTLE_LIST_ID
        and ifnull(a.is_used_asst_list,0) = ifnull(c.used_asst_list,0)
        and ifnull(a.asst_list_age_grp,0) = ifnull(c.asst_list_age_grp,0)
        and ifnull(a.asst_list_dise_sev_deg,0) = ifnull(c.asst_list_dise,0)
        and ifnull(a.asst_list_tmor_sev_deg,0) = ifnull(c.asst_list_tmor_sev_deg,0)
        AND a.HOSPITAL_ID = c.HOSPITAL_ID
        left join som_drg_grp_rcd d
        on a.SETTLE_LIST_ID = d.SETTLE_LIST_ID
        where 1 = 1
        AND a.dip_codg IS NOT NULL
        <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
            AND  a.HOSPITAL_ID = #{queryParam.hospitalId}
        </if>
        <if test="queryParam.b16c!=null and queryParam.b16c!=''">
            AND a.dscg_caty_codg_inhosp = #{queryParam.b16c}
        </if>
        <if test="queryParam.queryDrg!=null and queryParam.queryDrg!=''">
            AND a.dip_codg = #{queryParam.queryDrg}
        </if>
        <if test="queryParam.inStartTime!=null and queryParam.inStartTime!='' and
        queryParam.inEndTime!=null and queryParam.inEndTime!=''">
            AND a.adm_time BETWEEN #{queryParam.inStartTime} and CONCAT(#{queryParam.inEndTime},' 23:59:59')
        </if>
        <if test="(queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and
        queryParam.cy_end_date!=null and queryParam.cy_end_date!='') or queryParam.inHosFlag == 2">
            AND a.dscg_time BETWEEN #{queryParam.cy_start_date} and CONCAT(#{queryParam.cy_end_date},' 23:59:59')
        </if>
        <if test="(queryParam.seStartTime!=null and queryParam.seStartTime!='' and
        queryParam.seEndTime!=null and queryParam.seEndTime!='') or queryParam.inHosFlag == 2">
            AND a.setl_end_time BETWEEN #{queryParam.seStartTime} and CONCAT(#{queryParam.seEndTime},' 23:59:59')
        </if>
        <if test="queryParam.doctorIdList!=null">
            AND (
            a.deptdrt_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.chfdr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.atddr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.ipdr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.resp_nurs_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.train_dr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.intn_dr in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.codr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.qltctrl_dr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.qltctrl_nurs_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            )
        </if>
    </select>


<!--        <select id="getCountInfo" resultType="java.util.Map">-->
<!--            select-->

<!--            ifnull(count(case when c.grp_stas = '1' then 1 else null end),0) as coreDiseaseNum,-->
<!--            concat(ifnull(round(count(case when c.grp_stas = '1' then 1 else null end)/nullif(count(1),0) * 100,2),0),"%") as coreDiseaseGroupNumRate,-->

<!--            ifnull(count(case when c.grp_stas = '0' then 1 else null end),0) as compDiseaseNum,-->
<!--            concat(ifnull(round(count(case when c.grp_stas = '0' then 1 else null end)/nullif(count(1),0) * 100,2),0),"%") as compDiseaseGroupNumRate,-->

<!--            ifnull(count(case when c.grp_stas = '0' then-->
<!--            case when e.trt_type not in ('1','2','3') || e.trt_type is null then 1 else null end-->
<!--            else null end),0) as compC0DiseaseNum,-->
<!--            concat(ifnull(round(count(case when c.grp_stas = '0' then-->
<!--            case when e.trt_type not in ('1','2','3') || e.trt_type is null then 1 else null end-->
<!--            else null end)/nullif(count(1),0) * 100,2),0),"%") as compC0DiseaseGroupNumRate,-->

<!--            ifnull(count(case when c.grp_stas = '0' then-->
<!--            case when e.trt_type = '1' then 1 else null end-->
<!--            else null end),0) as compC1DiseaseNum,-->
<!--            concat(ifnull(round(count(case when c.grp_stas = '0' then-->
<!--            case when e.trt_type = '1' then 1 else null end-->
<!--            else null end)/nullif(count(1),0) * 100,2),0),"%") as compC1DiseaseGroupNumRate,-->

<!--            ifnull(count(case when c.grp_stas = '0' then-->
<!--            case when e.trt_type = '2' then 1 else null end-->
<!--            else null end),0) as compC2DiseaseNum,-->
<!--            concat(ifnull(round(count(case when c.grp_stas = '0' then-->
<!--            case when e.trt_type = '2' then 1 else null end-->
<!--            else null end)/nullif(count(1),0) * 100,2),0),"%") as compC2DiseaseGroupNumRate,-->

<!--            ifnull(count(case when c.grp_stas = '0' then-->
<!--            case when e.trt_type = '3' then 1 else null end-->
<!--            else null end),0) as compC3DiseaseNum,-->
<!--            concat(ifnull(round(count(case when c.grp_stas = '0' then-->
<!--            case when e.trt_type = '3' then 1 else null end-->
<!--            else null end)/nullif(count(1),0) * 100,2),0),"%") as compC3DiseaseGroupNumRate,-->


<!--            COUNT(CASE WHEN SUBSTR(d.drg_codg,2,1) in ('R','S','T','U','V','W','X','Y','Z')  THEN 1 ELSE NULL END) AS medicalDeptMedicalNum,&lt;!&ndash;内科组的DRGs病案数&ndash;&gt;-->
<!--            IFNULL(COUNT(DISTINCT(CASE WHEN SUBSTR(d.drg_codg,2,1) in ('R','S','T','U','V','W','X','Y','Z') AND a.grp_stas = '1' THEN a.dip_codg ELSE NULL END)),0) AS medicalDeptDrgsNum,&lt;!&ndash;内科组的DRGs组DRGs组数&ndash;&gt;-->
<!--            IFNULL(CONCAT(ROUND((IFNULL(COUNT(DISTINCT(CASE WHEN SUBSTR(d.drg_codg,2,1) in ('R','S','T','U','V','W','X','Y','Z') THEN d.drg_codg ELSE NULL END)),0)/NULLIF(COUNT(DISTINCT(d.drg_codg)),0))*100,2),'%'),0)-->
<!--            AS medicalDeptDrgsRate,&lt;!&ndash;内科组DRGs组数占比&ndash;&gt;-->
<!--            IFNULL(ROUND(SUM(CASE WHEN SUBSTR(d.drg_codg,2,1) in ('R','S','T','U','V','W','X','Y','Z') THEN b.dip_wt ELSE 0 END),2),0) AS medicalDeptTotalDrgWeight, &lt;!&ndash;内科组的DRGs组总权重&ndash;&gt;-->
<!--            IFNULL(ROUND(SUM(CASE WHEN SUBSTR(d.drg_codg,2,1) in ('R','S','T','U','V','W','X','Y','Z') THEN b.dip_wt ELSE 0 END) /-->
<!--            NULLIF(COUNT(CASE WHEN SUBSTR(d.drg_codg,2,1) in ('R','S','T','U','V','W','X','Y','Z') THEN 1 ELSE NULL END),0), 2),0) AS medicalDeptCmi,  &lt;!&ndash;内科组的DRGs组CMI指数&ndash;&gt;-->

<!--            COUNT(CASE WHEN SUBSTR(d.drg_codg,2,1) in ('K','L','M','N','P','Q')  THEN 1 ELSE NULL END) AS notOperationMedicalNum,&lt;!&ndash;非手术室操作组的DRGs病案数&ndash;&gt;-->
<!--            IFNULL(COUNT(DISTINCT(CASE WHEN SUBSTR(d.drg_codg,2,1) in ('K','L','M','N','P','Q') AND a.grp_stas = '1' THEN a.dip_codg ELSE NULL END)),0) AS notOperationDrgsNum,&lt;!&ndash;非手术室操作组的DRGs组DRGs组数&ndash;&gt;-->
<!--            IFNULL(CONCAT(ROUND((IFNULL(COUNT(DISTINCT(CASE WHEN SUBSTR(d.drg_codg,2,1) in ('K','L','M','N','P','Q') THEN d.drg_codg ELSE NULL END)),0)/NULLIF(COUNT(DISTINCT(d.drg_codg)),0))*100,2),'%'),0)-->
<!--            AS notOperationDrgsRate,&lt;!&ndash;非手术室操作组DRGs组数占比&ndash;&gt;-->
<!--            IFNULL(ROUND(SUM(CASE WHEN SUBSTR(d.drg_codg,2,1) in ('K','L','M','N','P','Q') THEN b.dip_wt ELSE 0 END),2),0) AS notOperationTotalDrgWeight, &lt;!&ndash;非手术室操作组的DRGs组总权重&ndash;&gt;-->
<!--            IFNULL(ROUND(SUM(CASE WHEN SUBSTR(d.drg_codg,2,1) in ('K','L','M','N','P','Q') THEN b.dip_wt ELSE 0 END) /-->
<!--            NULLIF(COUNT(CASE WHEN SUBSTR(d.drg_codg,2,1) in ('K','L','M','N','P','Q') THEN 1 ELSE NULL END),0), 2),0) AS notOperationCmi,  &lt;!&ndash;非手术室操作组的DRGs组CMI指数&ndash;&gt;-->

<!--            COUNT(CASE WHEN SUBSTR(d.drg_codg,2,1) in ('A','B','C','D','E','F','G','H','J')  THEN 1 ELSE NULL END) AS surgeryDeptMedicalNum,&lt;!&ndash;外科组的DRGs病案数&ndash;&gt;-->
<!--            IFNULL(COUNT(DISTINCT(CASE WHEN SUBSTR(d.drg_codg,2,1) in ('A','B','C','D','E','F','G','H','J') AND a.grp_stas = '1' THEN a.dip_codg ELSE NULL END)),0) AS surgeryDeptDrgsNum,&lt;!&ndash;外科组的DRGs组DRGs组数&ndash;&gt;-->
<!--            IFNULL(CONCAT(ROUND((IFNULL(COUNT(DISTINCT(CASE WHEN SUBSTR(d.drg_codg,2,1) in ('A','B','C','D','E','F','G','H','J') THEN d.drg_codg ELSE NULL END)),0)/NULLIF(COUNT(DISTINCT(d.drg_codg)),0))*100,2),'%'),0)-->
<!--            AS surgeryDeptDrgsRate,&lt;!&ndash;外科组DRGs组数占比&ndash;&gt;-->
<!--            IFNULL(ROUND(SUM(CASE WHEN SUBSTR(d.drg_codg,2,1) in ('A','B','C','D','E','F','G','H','J') THEN b.dip_wt ELSE 0 END),2),0) AS surgeryDeptTotalDrgWeight, &lt;!&ndash;外科组的DRGs组总权重&ndash;&gt;-->
<!--            IFNULL(ROUND(SUM(CASE WHEN SUBSTR(d.drg_codg,2,1) in ('A','B','C','D','E','F','G','H','J') THEN b.dip_wt ELSE 0 END) /-->
<!--            NULLIF(COUNT(CASE WHEN SUBSTR(d.drg_codg,2,1) in ('A','B','C','D','E','F','G','H','J') THEN 1 ELSE NULL END),0), 2),0) AS surgeryDeptCmi  &lt;!&ndash;外科组的DRGs组CMI指数&ndash;&gt;-->
<!--            from som_dip_grp_info a-->
<!--            left join som_dip_standard b-->
<!--            on a.HOSPITAL_ID = b.HOSPITAL_ID-->
<!--            and a.STANDARD_YEAR = b.STANDARD_YEAR-->
<!--            and a.dip_codg = b.dip_codg-->
<!--            left join som_dip_grp_rcd c-->
<!--            on a.SETTLE_LIST_ID = c.SETTLE_LIST_ID-->
<!--            left join som_drg_grp_rcd d-->
<!--            on a.SETTLE_LIST_ID = d.SETTLE_LIST_ID-->
<!--            left join som_icd_codg e-->
<!--            on a.main_oprn_oprt_codg = e.code-->
<!--            and e.ICD_TYPE = 'ICD-9' and e.icd_codg_ver = '10'-->
<!--            where 1 = 1-->
<!--            <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">-->
<!--                AND  a.HOSPITAL_ID = #{queryParam.hospitalId}-->
<!--            </if>-->
<!--            <if test="queryParam.b16c!=null and queryParam.b16c!=''">-->
<!--                AND a.dscg_caty_codg_inhosp = #{queryParam.b16c}-->
<!--            </if>-->
<!--            <if test="queryParam.queryDrg!=null and queryParam.queryDrg!=''">-->
<!--                AND a.dip_codg = #{queryParam.queryDrg}-->
<!--            </if>-->
<!--            <if test="queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and-->
<!--            queryParam.cy_end_date!=null and queryParam.cy_end_date!=''">-->
<!--                AND a.dscg_time BETWEEN #{queryParam.cy_start_date} and CONCAT(#{queryParam.cy_end_date},' 23:59:59')-->
<!--            </if>-->
<!--            <if test="queryParam.doctorIdList!=null">-->
<!--                AND (-->
<!--                a.deptdrt_code in-->
<!--                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">-->
<!--                    #{item}-->
<!--                </foreach>-->
<!--                OR-->
<!--                a.chfdr_code in-->
<!--                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">-->
<!--                    #{item}-->
<!--                </foreach>-->
<!--                OR-->
<!--                a.atddr_code in-->
<!--                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">-->
<!--                    #{item}-->
<!--                </foreach>-->
<!--                OR-->
<!--                a.ipdr_code in-->
<!--                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">-->
<!--                    #{item}-->
<!--                </foreach>-->
<!--                OR-->
<!--                a.resp_nurs_code in-->
<!--                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">-->
<!--                    #{item}-->
<!--                </foreach>-->
<!--                OR-->
<!--                a.train_dr_code in-->
<!--                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">-->
<!--                    #{item}-->
<!--                </foreach>-->
<!--                OR-->
<!--                a.intn_dr in-->
<!--                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">-->
<!--                    #{item}-->
<!--                </foreach>-->
<!--                OR-->
<!--                a.codr_code in-->
<!--                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">-->
<!--                    #{item}-->
<!--                </foreach>-->
<!--                OR-->
<!--                a.qltctrl_dr_code in-->
<!--                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">-->
<!--                    #{item}-->
<!--                </foreach>-->
<!--                OR-->
<!--                a.qltctrl_nurs_code in-->
<!--                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">-->
<!--                    #{item}-->
<!--                </foreach>-->
<!--                )-->
<!--            </if>-->
<!--        </select>-->
</mapper>