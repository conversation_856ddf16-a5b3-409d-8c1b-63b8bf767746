<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.som.dao.dipBusiness.DipCostControlDao">
    <select id="getDipGroupAndCostControl" resultType="java.util.HashMap">
        SELECT a.*,
               ROUND(IFNULL(a.levelCityAvgCost,0)-IFNULL(a.inHosTotalCost,0),2) AS profitAndLoss,
               b.name AS priOutHosDeptName
        FROM (
            select
                m.*,
                n.hosWeight AS hosWeight
            from(
                select
                    x.*
                from(
                    select
                        SUBSTR(a.dscg_time,1,4) as year,
                        a.PATIENT_ID as patientId,
                        a.SETTLE_LIST_ID AS id,
                        a.NAME as name,
                        a.ipdr_name AS inHosDoctorName,
                        a.dscg_caty_codg_inhosp,
                        b.dip_codg AS dipCodg,
                        b.DIP_NAME AS dipName,
                        a.HOSPITAL_ID,
                        a.is_used_asst_list AS isUsedAsstList,
                        a.asst_list_age_grp AS asstListAgeGrp,
                        a.asst_list_dise_sev_deg AS asstListDiseSevDeg,
                        a.asst_list_tmor_sev_deg AS asstListTmorSevDeg,
                        IFNULL(ROUND(a.ipt_sumfee, 2),0) AS inHosTotalCost,
                        IFNULL(convert(AES_DECRYPT(UNHEX(c.dip_standard_inpf),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) using utf8),0) as cityAvgCost,
                        IFNULL(convert(AES_DECRYPT(UNHEX(c.dip_standard_avg_fee_same_lv),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) using utf8),0) as levelCityAvgCost,
                        IFNULL(ROUND(a.act_ipt, 2),0) AS inHosDays,
                        IFNULL(convert(AES_DECRYPT(UNHEX(c.dip_standard_ipt_days),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) using utf8),0) as cityAvgDays,
                        IFNULL(convert(AES_DECRYPT(UNHEX(c.dip_standard_ipt_days_same_lv),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) using utf8),0) as levelCityAvgDays,
                        <![CDATA[
                        CASE WHEN d.dise_type = 4 IS NULL THEN
						'暂无该病种数据' ELSE d.dise_type END  AS costControlRange,
				        CASE WHEN d.dise_type = 4 IS NULL THEN '暂无参考标准'
						WHEN d.dise_type = 2 THEN '费用偏低'
						WHEN d.dise_type = 1 THEN '费用偏高'
						WHEN d.dise_type = 3 THEN '平稳区间' ELSE '无法预警' END AS costWarn,
                        ]]>
                        IFNULL(ROUND(c.dip_wt, 2),0) AS areaWeight,
                        CONCAT(b.main_diag_dise_codg,b.main_diag_dise_name) as mainCodeAndName,
                        IFNULL(ROUND(a.com_med_servfee,2),0) AS comMedServfee,
                        IFNULL(CONCAT(ROUND(a.com_med_servfee/NULLIF(a.ipt_sumfee,0)*100,2),'%'),0) AS serviceCostRate,
                        IFNULL(ROUND(a.rhab_fee,2),0) AS rhabFee,
                        IFNULL(CONCAT(ROUND(a.rhab_fee/NULLIF(a.ipt_sumfee,0)*100,2),'%'),0) AS recoverCostRate,
                        IFNULL(ROUND(a.diag_fee,2),0) AS diagFee,
                        IFNULL(CONCAT(ROUND(a.diag_fee/NULLIF(a.ipt_sumfee,0)*100,2),'%'),0) AS diagnoseCostRate,
                        IFNULL(ROUND(a.treat_fee,2),0) AS treatFee,
                        IFNULL(CONCAT(ROUND(a.treat_fee/NULLIF(a.ipt_sumfee,0)*100,2),'%'),0) AS treatmentCostRate,
                        IFNULL(ROUND(a.drugfee,2),0) AS drugfee,
                        IFNULL(CONCAT(ROUND(a.drugfee/NULLIF(a.ipt_sumfee,0)*100,2),'%'),0) AS medicalCostRate,
                        IFNULL(ROUND(a.blood_blo_pro,2),0) AS bloodBloPro,
                        IFNULL(CONCAT(ROUND(a.blood_blo_pro/NULLIF(a.ipt_sumfee,0)*100,2),'%'),0) AS bloodCostRate,
                        IFNULL(ROUND(a.mcs_fee,2),0) AS mcsFee,
                        IFNULL(CONCAT(ROUND(a.mcs_fee/NULLIF(a.ipt_sumfee,0)*100,2),'%'),0) AS materialCostRate,
                        IFNULL(ROUND(a.tcm_oth,2),0) AS tcmOth,
                        IFNULL(CONCAT(ROUND(a.tcm_oth/NULLIF(a.ipt_sumfee,0)*100,2),'%'),0) AS chineseOtherRate,
                        IFNULL(ROUND(a.oth_fee,2),0) AS othFee,
                        IFNULL(CONCAT(ROUND(a.oth_fee/NULLIF(a.ipt_sumfee,0)*100,2),'%'),0) AS otherCostRate,
                        IFNULL(ROUND(a.abt_fee,2),0) AS abtFee,
                        IFNULL(CONCAT(ROUND(a.abt_fee/NULLIF(a.ipt_sumfee,0)*100,2),'%'),0) AS antibioticCostRate,
                        IFNULL(ROUND(a.inspect_fee,2),0) AS inspectFee,
                        IFNULL(CONCAT(ROUND(a.inspect_fee/NULLIF(a.ipt_sumfee,0)*100,2),'%'),0) AS inspectionCostRate,
                        IFNULL(ROUND(a.psn_ownpay,2),0) AS psnOwnpay,
                        IFNULL(CONCAT(ROUND(a.psn_ownpay/NULLIF(a.ipt_sumfee,0)*100,2),'%'),0) AS psnOwnpayRate
                    from som_dip_grp_info a
                    <if test='queryParam.enableSeAns != null and queryParam.enableSeAns != "" and queryParam.enableSeAns == "1"'>
                        LEFT JOIN som_hi_invy_bas_info q
                        ON q.ID = a.SETTLE_LIST_ID
                        INNER JOIN som_setl_cas_crsp p
                        ON q.k00 = p.k00
                    </if>
                    LEFT JOIN som_dip_grp_rcd b on a.SETTLE_LIST_ID = b.SETTLE_LIST_ID
                    LEFT JOIN som_dip_sco d
                        ON a.SETTLE_LIST_ID = d.SETTLE_LIST_ID
                    LEFT JOIN som_dip_standard c on b.dip_codg = c.dip_codg AND SUBSTR(a.dscg_time,1,4) = c.STANDARD_YEAR AND c.ACTIVE_FLAG = '1'
                         AND a.asst_list_age_grp = c. asst_list_age_grp
                         AND a.asst_list_dise_sev_deg = c.asst_list_dise_sev_deg
                         AND a.asst_list_tmor_sev_deg = c.asst_list_tmor_sev_deg
                        AND a.HOSPITAL_ID = c.HOSPITAL_ID
                    cross join som_cd_control_fee_stsb_cfg e
                    where 1=1
                    <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
                        AND  a.HOSPITAL_ID = #{queryParam.hospitalId}
                    </if>
                    <if test="queryParam.patientId!=null and queryParam.patientId!=''">
                        AND instr(a.PATIENT_ID,#{queryParam.patientId}) > 0
                    </if>
                    <if test="queryParam.b16c!=null and queryParam.b16c!=''">
                        AND a.dscg_caty_codg_inhosp = #{queryParam.b16c}
                    </if>
                    <if test="queryParam.queryDipGroup!=null and queryParam.queryDipGroup!=''">
                        AND b.dip_codg = #{queryParam.queryDipGroup}
                    </if>
                    <if test="queryParam.grpStas!=null and queryParam.grpStas!=''">
                        AND b.grp_stas = #{queryParam.grpStas}
                    </if>
                    <if test="queryParam.a11!=null and queryParam.a11!=''">
                        AND a.NAME LIKE concat("%",#{queryParam.a11},"%")
                    </if>
                    <if test="queryParam.drCodg!=null and queryParam.drCodg!=''">
                        AND a.ipdr_code = #{queryParam.drCodg}
                    </if>
                    <if test="queryParam.drName!=null and queryParam.drName!=''">
                        AND a.ipdr_name = #{queryParam.drName}
                    </if>
                    <if test="queryParam.inStartTime!=null and queryParam.inStartTime!='' and
                                      queryParam.inEndTime!=null and queryParam.inEndTime!=''">
                        AND a.adm_time BETWEEN #{queryParam.inStartTime} and CONCAT(#{queryParam.inEndTime},' 23:59:59')
                    </if>
                    <if test="(queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and
                    queryParam.cy_end_date!=null and queryParam.cy_end_date!='') ">
                        AND a.dscg_time BETWEEN #{queryParam.cy_start_date} and CONCAT(#{queryParam.cy_end_date},' 23:59:59')
                    </if>
                    <if test="(queryParam.seStartTime!=null and queryParam.seStartTime!='' and
                                queryParam.seEndTime!=null and queryParam.seEndTime!='') ">
                        AND a.setl_end_time BETWEEN #{queryParam.seStartTime} and CONCAT(#{queryParam.seEndTime},' 23:59:59')
                    </if>
                    <if test="queryParam.doctorIdList!=null">
                        AND (
                        a.deptdrt_code in
                        <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator=","
                                 close=")">
                            #{item}
                        </foreach>
                        OR
                        a.chfdr_code in
                        <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator=","
                                 close=")">
                            #{item}
                        </foreach>
                        OR
                        a.atddr_code in
                        <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator=","
                                 close=")">
                            #{item}
                        </foreach>
                        OR
                        a.ipdr_code in
                        <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator=","
                                 close=")">
                            #{item}
                        </foreach>
                        OR
                        a.resp_nurs_code in
                        <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator=","
                                 close=")">
                            #{item}
                        </foreach>
                        OR
                        a.train_dr_code in
                        <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator=","
                                 close=")">
                            #{item}
                        </foreach>
                        OR
                        a.intn_dr in
                        <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator=","
                                 close=")">
                            #{item}
                        </foreach>
                        OR
                        a.codr_code in
                        <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator=","
                                 close=")">
                            #{item}
                        </foreach>
                        OR
                        a.qltctrl_dr_code in
                        <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator=","
                                 close=")">
                            #{item}
                        </foreach>
                        OR
                        a.qltctrl_nurs_code in
                        <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator=","
                                 close=")">
                            #{item}
                        </foreach>
                        )
                    </if>
                )x
                where 1 = 1
                  <if test="queryParam.hospitalId != null and queryParam.hospitalId != ''">
                      AND x.HOSPITAL_ID = #{queryParam.hospitalId}
                  </if>
                <if test="queryParam.costWarnType!=null and queryParam.costWarnType!=''">
                    AND  x.costWarn in
                    (select
                    case when #{queryParam.costWarnType}='0' then '费用偏低'
                    when #{queryParam.costWarnType}='1' then '费用偏高'
                    when #{queryParam.costWarnType}='2' then '平稳区间'
                    when #{queryParam.costWarnType}='9' then '暂无参考标准'
                    else null end
                    from dual
                    )
                </if>
            )m left join (
                select
                    SUBSTR(a.dscg_time,1,4) as year,
                    b.dip_codg AS dipCodg,
                    IFNULL(ROUND(AVG(a.ipt_sumfee)/NULLIF(convert(AES_DECRYPT(UNHEX(c.AREA_AVG_COST),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) using utf8),0), 4),0) AS hosWeight
                from som_dip_grp_info a
                INNER JOIN som_dip_grp_rcd b on a.SETTLE_LIST_ID = b.SETTLE_LIST_ID
                INNER JOIN som_regn_year_sum_data c on SUBSTR(a.dscg_time,1,4) = c.STANDARD_YEAR
                where b.grp_stas = '1'
                  <if test="queryParam.hospitalId != null and queryParam.hospitalId != ''">
                      AND a.HOSPITAL_ID = #{queryParam.hospitalId}
                  </if>
                <if test="queryParam.inStartTime!=null and queryParam.inStartTime!='' and
                          queryParam.inEndTime!=null and queryParam.inEndTime!=''">
                    AND a.adm_time BETWEEN #{queryParam.inStartTime} and CONCAT(#{queryParam.inEndTime},' 23:59:59')
                </if>
                <if test="(queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and
                            queryParam.cy_end_date!=null and queryParam.cy_end_date!='') ">
                    AND a.dscg_time BETWEEN #{queryParam.cy_start_date} and CONCAT(#{queryParam.cy_end_date},' 23:59:59')
                </if>
                <if test="(queryParam.seStartTime!=null and queryParam.seStartTime!='' and
                                    queryParam.seEndTime!=null and queryParam.seEndTime!='') ">
                    AND a.dscg_time BETWEEN #{queryParam.seStartTime} and CONCAT(#{queryParam.seEndTime},' 23:59:59')
                </if>
                GROUP BY b.dip_codg, SUBSTR(a.dscg_time,1,4), c.AREA_AVG_COST
            ) n on m.dipCodg = n.dipCodg and m.year=n.year
        order by n.hosWeight desc
    ) a left join som_dept b on a.dscg_caty_codg_inhosp = b.code AND a.HOSPITAL_ID = b.HOSPITAL_ID
    </select>

    <select id="getDipGroupNumTop10" resultType="com.my.som.vo.dipBusiness.DipInGroupNumTop10Vo">
        select
            CONCAT(y.dip_codg,y.DIP_NAME) AS dipGroupCodeAndName,
            COUNT(1) AS medicalRecordNum
        from (
            select
              x.*
            from(
                select
                    b.dip_codg AS dip_codg,
                    b.DIP_NAME AS DIP_NAME,
                    <![CDATA[
                                case when convert(AES_DECRYPT(UNHEX(c.dip_setl_fee),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) using utf8)=0
                                or convert(AES_DECRYPT(UNHEX(c.dip_setl_fee),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) using utf8) is null then '暂无参考标准'
                                     when a.ipt_sumfee < convert(AES_DECRYPT(UNHEX(c.dip_setl_fee),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) using utf8)*e.control_fee_stsb_min_prop then '费用偏低'
                                     when a.ipt_sumfee > convert(AES_DECRYPT(UNHEX(c.dip_setl_fee),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) using utf8)*e.control_fee_stsb_max_prop then '费用偏高'
                                     when convert(AES_DECRYPT(UNHEX(c.dip_setl_fee),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) using utf8)*e.control_fee_stsb_min_prop <= a.ipt_sumfee <= convert(AES_DECRYPT(UNHEX(c.dip_setl_fee),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) using utf8)*e.control_fee_stsb_max_prop then '平稳区间'
                                     else '无法预警'
                                end
                               ]]> AS costWarn
                from som_dip_grp_info a
                <if test='queryParam.enableSeAns != null and queryParam.enableSeAns != "" and queryParam.enableSeAns == "1"'>
                    LEFT JOIN som_hi_invy_bas_info q
                    ON q.ID = a.SETTLE_LIST_ID
                    INNER JOIN som_setl_cas_crsp p
                    ON q.k00 = p.k00
                </if>
                INNER JOIN som_dip_grp_rcd b on a.SETTLE_LIST_ID = b.SETTLE_LIST_ID
                LEFT JOIN som_dip_standard c on b.dip_codg = c.dip_codg AND SUBSTR(a.dscg_time,1,4) = c.STANDARD_YEAR AND c.ACTIVE_FLAG = '1'
                    AND a.HOSPITAL_ID = c.HOSPITAL_ID
                cross join som_cd_control_fee_stsb_cfg e
                where b.grp_stas ='1'
                <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
                    AND  a.HOSPITAL_ID = #{queryParam.hospitalId}
                </if>
                <if test="queryParam.b16c!=null and queryParam.b16c!=''">
                    AND ( a.dscg_caty_codg_inhosp = #{queryParam.b16c} or
                    a.dscg_caty_name_inhosp  = (select NAME from som_dept where TYPE='2' and CODE=#{queryParam.b16c} AND HOSPITAL_ID = #{queryParam.hospitalId} AND ACTIVE_FLAG='1')
                    )
                </if>
                <if test="queryParam.queryDipGroup!=null and queryParam.queryDipGroup!=''">
                    AND b.dip_codg = #{queryParam.queryDipGroup}
                </if>
                <if test="queryParam.a11!=null and queryParam.a11!=''">
                    AND a.NAME LIKE concat("%",#{queryParam.a11},"%")
                </if>
                <if test="queryParam.drCodg!=null and queryParam.drCodg!=''">
                    AND (
                    a.deptdrt_code = #{queryParam.drCodg} OR
                    a.chfdr_code = #{queryParam.drCodg} OR
                    a.atddr_code = #{queryParam.drCodg} OR
                    a.ipdr_code = #{queryParam.drCodg} OR
                    a.train_dr_code = #{queryParam.drCodg} OR
                    a.intn_dr = #{queryParam.drCodg} OR
                    a.qltctrl_dr_code = #{queryParam.drCodg}
                    )
                </if>
                <if test="queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and
                        queryParam.cy_end_date!=null and queryParam.cy_end_date!=''">
                    AND a.dscg_time BETWEEN #{queryParam.cy_start_date} and CONCAT(#{queryParam.cy_end_date},' 23:59:59')
                </if>
                <if test="queryParam.doctorIdList!=null">
                    AND (
                    a.deptdrt_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    a.chfdr_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    a.atddr_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    a.ipdr_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    a.resp_nurs_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    a.train_dr_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    a.intn_dr in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    a.codr_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    a.qltctrl_dr_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    a.qltctrl_nurs_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    )
                </if>
            )x
            where 1 = 1
            <if test="queryParam.costWarnType!=null and queryParam.costWarnType!=''">
                AND  x.costWarn in
                (select
                case when #{queryParam.costWarnType}='0' then '费用偏低'
                when #{queryParam.costWarnType}='1' then '费用偏高'
                when #{queryParam.costWarnType}='2' then '平稳区间'
                when #{queryParam.costWarnType}='9' then '暂无参考标准'
                else null end
                from dual
                )
            </if>
        ) y
        group by CONCAT(y.dip_codg,y.DIP_NAME)
    </select>

    <select id="getDipGroupCostTop10" resultType="com.my.som.vo.dipBusiness.DipInGroupCostTop10Vo">
        select
            CONCAT(y.dip_codg,y.DIP_NAME) AS dipGroupCodeAndName,
            IFNULL(ROUND(sum(y.ipt_sumfee),2),0) AS inHosCost
        from (
            select
                x.*
            from(
                select
                    b.dip_codg AS dip_codg,
                    b.DIP_NAME AS DIP_NAME,
                    a.ipt_sumfee as ipt_sumfee,
                    <![CDATA[
                    case when convert(AES_DECRYPT(UNHEX(c.dip_setl_fee),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) using utf8)=0 or convert(AES_DECRYPT(UNHEX(c.dip_setl_fee),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) using utf8) is null then '暂无参考标准'
                         when a.ipt_sumfee < convert(AES_DECRYPT(UNHEX(c.dip_setl_fee),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) using utf8)*e.control_fee_stsb_min_prop then '费用偏低'
                         when a.ipt_sumfee > convert(AES_DECRYPT(UNHEX(c.dip_setl_fee),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) using utf8)*e.control_fee_stsb_max_prop then '费用偏高'
                         when convert(AES_DECRYPT(UNHEX(c.dip_setl_fee),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) using utf8)*e.control_fee_stsb_min_prop <= a.ipt_sumfee <= convert(AES_DECRYPT(UNHEX(c.dip_setl_fee),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) using utf8)*e.control_fee_stsb_max_prop then '平稳区间'
                         else '无法预警'
                    end
                   ]]> AS costWarn
                from som_dip_grp_info a
                <if test='queryParam.enableSeAns != null and queryParam.enableSeAns != "" and queryParam.enableSeAns == "1"'>
                    LEFT JOIN som_hi_invy_bas_info q
                    ON q.ID = a.SETTLE_LIST_ID
                    INNER JOIN som_setl_cas_crsp p
                    ON q.k00 = p.k00
                </if>
                INNER JOIN som_dip_grp_rcd b on a.SETTLE_LIST_ID = b.SETTLE_LIST_ID
                LEFT JOIN som_dip_standard c on b.dip_codg = c.dip_codg AND SUBSTR(a.dscg_time,1,4) = c.STANDARD_YEAR AND c.ACTIVE_FLAG = '1'
                    AND a.HOSPITAL_ID = c.HOSPITAL_ID
                cross join som_cd_control_fee_stsb_cfg e
                where b.grp_stas ='1'
                <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
                    AND  a.HOSPITAL_ID = #{queryParam.hospitalId}
                </if>
                <if test="queryParam.b16c!=null and queryParam.b16c!=''">
                    AND ( a.dscg_caty_codg_inhosp = #{queryParam.b16c} or
                    a.dscg_caty_name_inhosp  = (select NAME from som_dept where TYPE='2' and CODE=#{queryParam.b16c} AND HOSPITAL_ID = #{queryParam.hospitalId} AND ACTIVE_FLAG='1')
                    )
                </if>
                <if test="queryParam.queryDipGroup!=null and queryParam.queryDipGroup!=''">
                    AND b.dip_codg = #{queryParam.queryDipGroup}
                </if>
                <if test="queryParam.a11!=null and queryParam.a11!=''">
                    AND a.NAME LIKE concat("%",#{queryParam.a11},"%")
                </if>
                <if test="queryParam.drCodg!=null and queryParam.drCodg!=''">
                    AND (
                    a.deptdrt_code = #{queryParam.drCodg} OR
                    a.chfdr_code = #{queryParam.drCodg} OR
                    a.atddr_code = #{queryParam.drCodg} OR
                    a.ipdr_code = #{queryParam.drCodg} OR
                    a.train_dr_code = #{queryParam.drCodg} OR
                    a.intn_dr = #{queryParam.drCodg} OR
                    a.qltctrl_dr_code = #{queryParam.drCodg}
                    )
                </if>
                <if test="queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and
                                queryParam.cy_end_date!=null and queryParam.cy_end_date!=''">
                    AND a.dscg_time BETWEEN #{queryParam.cy_start_date} and CONCAT(#{queryParam.cy_end_date},' 23:59:59')
                </if>
                <if test="queryParam.doctorIdList!=null">
                    AND (
                    a.deptdrt_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    a.chfdr_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    a.atddr_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    a.ipdr_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    a.resp_nurs_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    a.train_dr_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    a.intn_dr in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    a.codr_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    a.qltctrl_dr_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    a.qltctrl_nurs_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    )
                </if>
            )x
            where 1 = 1
            <if test="queryParam.costWarnType!=null and queryParam.costWarnType!=''">
                AND  x.costWarn in
                (select
                case when #{queryParam.costWarnType}='0' then '费用偏低'
                when #{queryParam.costWarnType}='1' then '费用偏高'
                when #{queryParam.costWarnType}='2' then '平稳区间'
                when #{queryParam.costWarnType}='9' then '暂无参考标准'
                else null end
                from dual
                )
            </if>
        )y
        group by CONCAT(y.dip_codg,y.DIP_NAME)
    </select>

    <select id="getCostCountInfo" resultType="com.my.som.vo.firstPage.MedicalTreatmentCostVo">
        select
            IFNULL(SUM(a.com_med_servfee),0)+IFNULL(SUM(a.rhab_fee),0)+
            IFNULL(SUM(a.diag_fee),0)+IFNULL(SUM(a.treat_fee),0)+IFNULL(SUM(a.drugfee),0)+
            IFNULL(SUM(a.blood_blo_pro),0)+IFNULL(SUM(a.mcs_fee),0)+IFNULL(SUM(a.tcm_oth),0)+
            IFNULL(SUM(a.oth_fee),0) AS iptSumfee,
            IFNULL(SUM(a.com_med_servfee),0) AS comMedServfee,
            IFNULL(SUM(a.rhab_fee),0) AS rhabFee,
            IFNULL(SUM(a.diag_fee),0) AS diagFee,
            IFNULL(SUM(a.treat_fee),0) AS treatFee,
            IFNULL(SUM(a.drugfee),0) AS drugfee,
            IFNULL(SUM(a.blood_blo_pro),0) AS bloodBloPro,
            IFNULL(SUM(a.mcs_fee),0) AS mcsFee,
            IFNULL(SUM(a.tcm_oth),0) AS chineseOtherCost,
            IFNULL(SUM(a.oth_fee),0) AS othFee,
            IFNULL(SUM(a.abt_fee),0) AS abtFee,
            IFNULL(SUM(a.inspect_fee),0) AS inspectFee
        from (
            select
              x.*
            from(
                select
                    <![CDATA[
                    case when convert(AES_DECRYPT(UNHEX(c.dip_setl_fee),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) using utf8)=0 or convert(AES_DECRYPT(UNHEX(c.dip_setl_fee),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) using utf8) is null then '暂无参考标准'
                         when a.ipt_sumfee < convert(AES_DECRYPT(UNHEX(c.dip_setl_fee),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) using utf8)*e.control_fee_stsb_min_prop then '费用偏低'
                         when a.ipt_sumfee > convert(AES_DECRYPT(UNHEX(c.dip_setl_fee),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) using utf8)*e.control_fee_stsb_max_prop then '费用偏高'
                         when convert(AES_DECRYPT(UNHEX(c.dip_setl_fee),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) using utf8)*e.control_fee_stsb_min_prop <= a.ipt_sumfee <= convert(AES_DECRYPT(UNHEX(c.dip_setl_fee),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) using utf8)*e.control_fee_stsb_max_prop then '平稳区间'
                         else '无法预警'
                    end
                   ]]> AS costWarn,
                    IFNULL(ROUND(a.com_med_servfee,2),0) AS com_med_servfee,
                    IFNULL(ROUND(a.rhab_fee,2),0) AS rhab_fee,
                    IFNULL(ROUND(a.diag_fee,2),0) AS diag_fee,
                    IFNULL(ROUND(a.treat_fee,2),0) AS treat_fee,
                    IFNULL(ROUND(a.drugfee,2),0) AS drugfee,
                    IFNULL(ROUND(a.blood_blo_pro,2),0) AS blood_blo_pro,
                    IFNULL(ROUND(a.mcs_fee,2),0) AS mcs_fee,
                    IFNULL(ROUND(a.tcm_oth,2),0) AS tcm_oth,
                    IFNULL(ROUND(a.oth_fee,2),0) AS oth_fee,
                    IFNULL(ROUND(a.abt_fee,2),0) AS abt_fee,
                    IFNULL(ROUND(a.inspect_fee,2),0) AS inspect_fee
                from som_dip_grp_info a
                <if test='queryParam.enableSeAns != null and queryParam.enableSeAns != "" and queryParam.enableSeAns == "1"'>
                    LEFT JOIN som_hi_invy_bas_info q
                    ON q.ID = a.SETTLE_LIST_ID
                    INNER JOIN som_setl_cas_crsp p
                    ON q.k00 = p.k00
                </if>
                LEFT JOIN som_dip_grp_rcd b on a.SETTLE_LIST_ID = b.SETTLE_LIST_ID
                LEFT JOIN som_dip_standard c on b.dip_codg = c.dip_codg AND SUBSTR(a.dscg_time,1,4) = c.STANDARD_YEAR AND c.ACTIVE_FLAG = '1'
                    AND a.HOSPITAL_ID = c.HOSPITAL_ID
                cross join som_cd_control_fee_stsb_cfg e
                where 1=1
                <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
                    AND  a.HOSPITAL_ID = #{queryParam.hospitalId}
                </if>
                <if test="queryParam.b16c!=null and queryParam.b16c!=''">
                    AND ( a.dscg_caty_codg_inhosp = #{queryParam.b16c} or
                    a.dscg_caty_name_inhosp  = (select NAME from som_dept where TYPE='2' and CODE=#{queryParam.b16c} AND HOSPITAL_ID = #{queryParam.hospitalId} AND ACTIVE_FLAG='1')
                    )
                </if>
                <if test="queryParam.queryDipGroup!=null and queryParam.queryDipGroup!=''">
                    AND b.dip_codg = #{queryParam.queryDipGroup}
                </if>
                <if test="queryParam.grpStas!=null and queryParam.grpStas!=''">
                    AND b.grp_stas = #{queryParam.grpStas}
                </if>
                <if test="queryParam.a11!=null and queryParam.a11!=''">
                    AND a.NAME LIKE concat("%",#{queryParam.a11},"%")
                </if>
                <if test="queryParam.drCodg!=null and queryParam.drCodg!=''">
                    AND (
                    a.deptdrt_code = #{queryParam.drCodg} OR
                    a.chfdr_code = #{queryParam.drCodg} OR
                    a.atddr_code = #{queryParam.drCodg} OR
                    a.ipdr_code = #{queryParam.drCodg} OR
                    a.train_dr_code = #{queryParam.drCodg} OR
                    a.intn_dr = #{queryParam.drCodg} OR
                    a.qltctrl_dr_code = #{queryParam.drCodg}
                    )
                </if>
                <if test="queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and
                                        queryParam.cy_end_date!=null and queryParam.cy_end_date!=''">
                    AND a.dscg_time BETWEEN #{queryParam.cy_start_date} and CONCAT(#{queryParam.cy_end_date},' 23:59:59')
                </if>
                <if test="queryParam.doctorIdList!=null">
                    AND (
                    a.deptdrt_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    a.chfdr_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    a.atddr_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    a.ipdr_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    a.resp_nurs_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    a.train_dr_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    a.intn_dr in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    a.codr_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    a.qltctrl_dr_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    a.qltctrl_nurs_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    )
                </if>
            )x
            where 1 = 1
            <if test="queryParam.costWarnType!=null and queryParam.costWarnType!=''">
                AND  x.costWarn in
                (select
                case when #{queryParam.costWarnType}='0' then '费用偏低'
                when #{queryParam.costWarnType}='1' then '费用偏高'
                when #{queryParam.costWarnType}='2' then '平稳区间'
                when #{queryParam.costWarnType}='9' then '暂无参考标准'
                else null end
                from dual
                )
            </if>
        )a
    </select>
</mapper>
