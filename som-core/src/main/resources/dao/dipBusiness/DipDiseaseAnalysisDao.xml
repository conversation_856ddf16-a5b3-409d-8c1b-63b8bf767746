<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.som.dao.dipBusiness.DipDiseaseAnalysisDao">
    <select id="list" resultType="com.my.som.vo.dipBusiness.DipDiseaseAnalysisInfo">
        SELECT x.*
        FROM
             (
                 SELECT m.*,
                        concat(m.icdCodg, ifnull(p.name,'')) as icdName
                 <if test="queryParam.queryType==1">
                 <trim prefix=",">
                        n.name AS priOutHosDeptName
                 </trim>
                 </if>
                 FROM
                      (
                          SELECT
        <if test="queryParam.queryType==1">
            a.dscg_caty_codg_inhosp AS priOutHosDeptCode,
        </if>
        <if test="queryParam.queryType==2">
            group_concat(distinct a.dscg_caty_codg_inhosp) AS priOutHosDeptCode,
            group_concat(distinct a.dscg_caty_name_inhosp) AS priOutHosDeptName,
        </if>
        b.dscg_diag_codg AS icdCodg,
               a.HOSPITAL_ID,
               a.is_used_asst_list AS isUsedAsstList,
               a.asst_list_age_grp AS asstListAgeGrp,
               a.asst_list_dise_sev_deg AS asstListDiseSevDeg,
               a.asst_list_tmor_sev_deg AS asstListTmorSevDeg,
        <![CDATA[
                        case
                            when SUBSTR(b.dscg_diag_codg,1,1)='A' or SUBSTR(b.dscg_diag_codg,1,1)='B' then '某些传染病和寄生虫病'
                            when (SUBSTR(b.dscg_diag_codg,1,1)='C') or
                                 (SUBSTR(b.dscg_diag_codg,1,1)='D' and CONVERT(substr(b.dscg_diag_codg,2,2),SIGNED)>=0 and CONVERT(SUBSTR(b.dscg_diag_codg,2,2),SIGNED)<=48) then '肿瘤'
                            when (SUBSTR(b.dscg_diag_codg,1,1)='D' and CONVERT(substr(b.dscg_diag_codg,2,2),SIGNED)>=50 and CONVERT(SUBSTR(b.dscg_diag_codg,2,2),SIGNED)<=89) then '血液及造血器官疾病和涉及免疫机制的某些疾患'
                            when (SUBSTR(b.dscg_diag_codg,1,1)='E' and CONVERT(substr(b.dscg_diag_codg,2,2),SIGNED)>=0 and CONVERT(SUBSTR(b.dscg_diag_codg,2,2),SIGNED)<=90) then '内分泌、营养和代谢疾病'
                            when SUBSTR(b.dscg_diag_codg,1,1)='F'  then '精神和行为障碍'
                            when SUBSTR(b.dscg_diag_codg,1,1)='G' then '神经系统疾病'
                            when (SUBSTR(b.dscg_diag_codg,1,1)='H' and CONVERT(substr(b.dscg_diag_codg,2,2),SIGNED)>=0 and CONVERT(SUBSTR(b.dscg_diag_codg,2,2),SIGNED)<=59) then '眼和附器疾病及耳和乳突疾病'
                            when (SUBSTR(b.dscg_diag_codg,1,1)='H' and CONVERT(substr(b.dscg_diag_codg,2,2),SIGNED)>=60 and CONVERT(SUBSTR(b.dscg_diag_codg,2,2),SIGNED)<=95) then '耳和乳突疾病'
                            when SUBSTR(b.dscg_diag_codg,1,1)='I' then '循环系统疾病'
                            when SUBSTR(b.dscg_diag_codg,1,1)='J' then '呼吸系统疾病'
                            when (SUBSTR(b.dscg_diag_codg,1,1)='K' and CONVERT(substr(b.dscg_diag_codg,2,2),SIGNED)>=0 and CONVERT(SUBSTR(b.dscg_diag_codg,2,2),SIGNED)<=93) then '消化系统疾病'
                            when SUBSTR(b.dscg_diag_codg,1,1)='L' then '皮肤和皮下组织疾病'
                            when SUBSTR(b.dscg_diag_codg,1,1)='M' then '肌肉骨骼系统和结缔组织疾病'
                            when SUBSTR(b.dscg_diag_codg,1,1)='N' then '泌尿生殖系统疾病'
                            when SUBSTR(b.dscg_diag_codg,1,1)='O' then '妊娠、分娩和产褥期'
                            when (SUBSTR(b.dscg_diag_codg,1,1)='P' and CONVERT(substr(b.dscg_diag_codg,2,2),SIGNED)>=0 and CONVERT(SUBSTR(b.dscg_diag_codg,2,2),SIGNED)<=96) then '起源于围生期的某些情况'
                            when SUBSTR(b.dscg_diag_codg,1,1)='Q' then '先天性畸形、变形和染色体异常'
                            when SUBSTR(b.dscg_diag_codg,1,1)='R' then '症状、体征和临床与实验室异常所见，不可归类在他处者'
                            when (SUBSTR(b.dscg_diag_codg,1,1)='S') or
                                 (SUBSTR(b.dscg_diag_codg,1,1)='T' and CONVERT(substr(b.dscg_diag_codg,2,2),SIGNED)>=0 and CONVERT(SUBSTR(b.dscg_diag_codg,2,2),SIGNED)<=98) then '损伤、中毒和外因的某些其他后果'
                            when SUBSTR(b.dscg_diag_codg,1,1)='U' then '疾病和死亡的外因'
                            when (SUBSTR(b.dscg_diag_codg,1,1)='V') or (SUBSTR(b.dscg_diag_codg,1,1)='W') or (SUBSTR(b.dscg_diag_codg,1,1)='X') or
                                 (SUBSTR(b.dscg_diag_codg,1,1)='Y' and CONVERT(substr(b.dscg_diag_codg,2,2),SIGNED)>=01 and CONVERT(SUBSTR(b.dscg_diag_codg,2,2),SIGNED)<=98) then '用于特殊目的的编码'
                            when SUBSTR(b.dscg_diag_codg,1,1)='Z' then '影响健康状态和与保健机构接触的因素'
                            when (SUBSTR(b.dscg_diag_codg,1,3)='D49') or
                                 (SUBSTR(b.dscg_diag_codg,1,1)='D' and CONVERT(substr(b.dscg_diag_codg,2,2),SIGNED)>=90 and CONVERT(SUBSTR(b.dscg_diag_codg,2,2),SIGNED)<=99) or
                                 (SUBSTR(b.dscg_diag_codg,1,1)='E' and CONVERT(substr(b.dscg_diag_codg,2,2),SIGNED)>=91 and CONVERT(SUBSTR(b.dscg_diag_codg,2,2),SIGNED)<=99) or
                                 (SUBSTR(b.dscg_diag_codg,1,1)='K' and CONVERT(substr(b.dscg_diag_codg,2,2),SIGNED)>=94 and CONVERT(SUBSTR(b.dscg_diag_codg,2,2),SIGNED)<=99) or
                                 (SUBSTR(b.dscg_diag_codg,1,1)='P' and CONVERT(substr(b.dscg_diag_codg,2,2),SIGNED)>=97 and CONVERT(SUBSTR(b.dscg_diag_codg,2,2),SIGNED)<=99)
                                 then '其他'
                        else null end
                    ]]>  AS diagnosisType, <!--疾病大类-->
        IFNULL(COUNT(distinct a.SETTLE_LIST_ID), 0) AS totalPatients,
        ROUND(IFNULL(SUM(ipt_sumfee),0),2) AS totalInHosCost,
        IFNULL(ROUND(AVG(ipt_sumfee), 2),0) AS avgCost,
        IFNULL(SUM(act_ipt),0) AS inHosDays,
        IFNULL(ROUND(AVG(act_ipt), 2),0) AS avgInHosDays,
        z.totalNum
        from som_dip_grp_info a
        <if test='queryParam.enableSeAns != null and queryParam.enableSeAns != "" and queryParam.enableSeAns == "1"'>
            LEFT JOIN som_hi_invy_bas_info q
            ON q.ID = a.SETTLE_LIST_ID
            INNER JOIN som_setl_cas_crsp p
            ON q.k00 = p.k00
        </if>
        inner join som_diag b on a.SETTLE_LIST_ID = b.SETTLE_LIST_ID
        inner join (
            select count(1) as totalNum,1 from som_dip_grp_info
            <where>
                <if test="queryParam.inStartTime!=null and queryParam.inStartTime!='' and
                    queryParam.inEndTime!=null and queryParam.inEndTime!=''">
                    AND adm_time BETWEEN #{queryParam.inStartTime} and CONCAT(#{queryParam.inEndTime},' 23:59:59')
                </if>
                <if test="(queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and
                    queryParam.cy_end_date!=null and queryParam.cy_end_date!='') or queryParam.inHosFlag == 2">
                    AND dscg_time BETWEEN #{queryParam.cy_start_date} and CONCAT(#{queryParam.cy_end_date},' 23:59:59')
                </if>
                <if test="(queryParam.seStartTime!=null and queryParam.seStartTime!='' and
                    queryParam.seEndTime!=null and queryParam.seEndTime!='') or queryParam.inHosFlag == 2">
                    AND setl_end_time BETWEEN #{queryParam.seStartTime} and CONCAT(#{queryParam.seEndTime},' 23:59:59')
                </if>
                <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
                    AND  HOSPITAL_ID = #{queryParam.hospitalId}
                </if>
            </where>

            ) z on 1=1
        where b.dscg_diag_codg is not null
        AND b.dscg_diag_codg != '' and b.dscg_diag_codg!='-' and  b.dscg_diag_codg!='--'
        <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
            AND  a.HOSPITAL_ID = #{queryParam.hospitalId}
        </if>
        <if test="queryParam.b16c!=null and queryParam.b16c!=''">
            AND a.dscg_caty_codg_inhosp = #{queryParam.b16c}
        </if>
        <if test="queryParam.queryIcd!=null and queryParam.queryIcd!=''">
            AND b.dscg_diag_codg = #{queryParam.queryIcd}
        </if>
        <if test="queryParam.inStartTime!=null and queryParam.inStartTime!='' and
                    queryParam.inEndTime!=null and queryParam.inEndTime!=''">
            AND a.adm_time BETWEEN #{queryParam.inStartTime} and CONCAT(#{queryParam.inEndTime},' 23:59:59')
        </if>
        <if test="(queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and
                queryParam.cy_end_date!=null and queryParam.cy_end_date!='') or queryParam.inHosFlag == 2">
            AND a.dscg_time BETWEEN #{queryParam.cy_start_date} and CONCAT(#{queryParam.cy_end_date},' 23:59:59')
        </if>
        <if test="(queryParam.seStartTime!=null and queryParam.seStartTime!='' and
                    queryParam.seEndTime!=null and queryParam.seEndTime!='') or queryParam.inHosFlag == 2">
            AND a.setl_end_time BETWEEN #{queryParam.seStartTime} and CONCAT(#{queryParam.seEndTime},' 23:59:59')
        </if>
        <if test="queryParam.doctorIdList!=null">
            AND (
            a.deptdrt_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.chfdr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.atddr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.ipdr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.resp_nurs_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.train_dr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.intn_dr in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.codr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.qltctrl_dr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.qltctrl_nurs_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            )
        </if>
        GROUP BY b.dscg_diag_codg,
                 a.HOSPITAL_ID,
                 z.totalNum,
                 a.is_used_asst_list,
                 a.asst_list_age_grp,
                 a.asst_list_dise_sev_deg,
                 a.asst_list_tmor_sev_deg
        <if test="queryParam.queryType==1">
            ,a.dscg_caty_codg_inhosp
        </if>
                      ) m
                          <if test="queryParam.queryType==1">
                          left join som_dept n
                              on m.priOutHosDeptCode = n.code
                          AND m.HOSPITAL_ID = n.HOSPITAL_ID
                          </if>
        left join (
        select code,name from som_icd_codg
        where icd_TYPE = 'ICD-10' and icd_codg_ver = '10'
        ) p on m.icdCodg = p.code
                 ) x
        ORDER BY x.totalPatients DESC
    </select>

    <select id="getCountInfo" resultType="java.util.Map">
        select
        IFNULL((x.a00b99+x.c00d48+x.d50d89+x.e00e90+x.f00f99+x.g00g99+x.h00h59+x.h60h95+x.i00i99+x.j00j99+x.k00k93+x.l00l99+x.m00m99+x.n00n99+x.o00o99+x.p00p96
        +x.q00q99+x.r00r99+x.s00t98+x.u00u99+x.v01y98+x.z00z99+x.other),0) AS totalPatients,
        x.*
        from(
        select
        IFNULL(COUNT(DISTINCT(case when SUBSTR(b.dscg_diag_codg,1,1)='A' or SUBSTR(b.dscg_diag_codg,1,1)='B' then a.SETTLE_LIST_ID else null end)),0) AS a00b99,
        <![CDATA[ IFNULL(COUNT(DISTINCT(case when (SUBSTR(b.dscg_diag_codg,1,1)='C') or
                   (SUBSTR(b.dscg_diag_codg,1,1)='D' and CONVERT(substr(b.dscg_diag_codg,2,2),SIGNED)>=0 and CONVERT(SUBSTR(b.dscg_diag_codg,2,2),SIGNED)<=48) then a.SETTLE_LIST_ID else null end)),0) ]]> AS c00d48,
        <![CDATA[ IFNULL(COUNT(DISTINCT(case when (SUBSTR(b.dscg_diag_codg,1,1)='D' and CONVERT(substr(b.dscg_diag_codg,2,2),SIGNED)>=50 and CONVERT(SUBSTR(b.dscg_diag_codg,2,2),SIGNED)<=89)
                    then a.SETTLE_LIST_ID else null end)),0) ]]> AS d50d89,
        <![CDATA[ IFNULL(COUNT(DISTINCT(case when (SUBSTR(b.dscg_diag_codg,1,1)='E' and CONVERT(substr(b.dscg_diag_codg,2,2),SIGNED)>=0 and CONVERT(SUBSTR(b.dscg_diag_codg,2,2),SIGNED)<=90)
                    then a.SETTLE_LIST_ID else null end)),0) ]]> AS e00e90,
        IFNULL(COUNT(DISTINCT(case when SUBSTR(b.dscg_diag_codg,1,1)='F' then a.SETTLE_LIST_ID else null end)),0) AS f00f99,
        IFNULL(COUNT(DISTINCT(case when SUBSTR(b.dscg_diag_codg,1,1)='G' then a.SETTLE_LIST_ID else null end)),0) AS g00g99,
        <![CDATA[ IFNULL(COUNT(DISTINCT(case when (SUBSTR(b.dscg_diag_codg,1,1)='H' and CONVERT(substr(b.dscg_diag_codg,2,2),SIGNED)>=0 and CONVERT(SUBSTR(b.dscg_diag_codg,2,2),SIGNED)<=59)
                    then a.SETTLE_LIST_ID else null end)),0) ]]> AS h00h59,
        <![CDATA[ IFNULL(COUNT(DISTINCT(case when (SUBSTR(b.dscg_diag_codg,1,1)='H' and CONVERT(substr(b.dscg_diag_codg,2,2),SIGNED)>=60 and CONVERT(SUBSTR(b.dscg_diag_codg,2,2),SIGNED)<=95)
                    then a.SETTLE_LIST_ID else null end)),0) ]]> AS h60h95,
        IFNULL(COUNT(DISTINCT(case when SUBSTR(b.dscg_diag_codg,1,1)='I' then a.SETTLE_LIST_ID else null end)),0) AS i00i99,
        IFNULL(COUNT(DISTINCT(case when SUBSTR(b.dscg_diag_codg,1,1)='J' then a.SETTLE_LIST_ID else null end)),0) AS j00j99,
        <![CDATA[ IFNULL(COUNT(DISTINCT(case when (SUBSTR(b.dscg_diag_codg,1,1)='K' and CONVERT(substr(b.dscg_diag_codg,2,2),SIGNED)>=0 and CONVERT(SUBSTR(b.dscg_diag_codg,2,2),SIGNED)<=93)
                    then a.SETTLE_LIST_ID else null end)),0) ]]> AS k00k93,
        IFNULL(COUNT(DISTINCT(case when SUBSTR(b.dscg_diag_codg,1,1)='L' then a.SETTLE_LIST_ID else null end)),0) AS l00l99,
        IFNULL(COUNT(DISTINCT(case when SUBSTR(b.dscg_diag_codg,1,1)='M' then a.SETTLE_LIST_ID else null end)),0) AS m00m99,
        IFNULL(COUNT(DISTINCT(case when SUBSTR(b.dscg_diag_codg,1,1)='N' then a.SETTLE_LIST_ID else null end)),0) AS n00n99,
        IFNULL(COUNT(DISTINCT(case when SUBSTR(b.dscg_diag_codg,1,1)='O' then a.SETTLE_LIST_ID else null end)),0) AS o00o99,
        <![CDATA[ IFNULL(COUNT(DISTINCT(case when (SUBSTR(b.dscg_diag_codg,1,1)='P' and CONVERT(substr(b.dscg_diag_codg,2,2),SIGNED)>=0 and CONVERT(SUBSTR(b.dscg_diag_codg,2,2),SIGNED)<=96)
                    then a.SETTLE_LIST_ID else null end)),0) ]]> AS p00p96,
        IFNULL(COUNT(DISTINCT(case when SUBSTR(b.dscg_diag_codg,1,1)='Q' then a.SETTLE_LIST_ID else null end)),0) AS q00q99,
        IFNULL(COUNT(DISTINCT(case when SUBSTR(b.dscg_diag_codg,1,1)='R' then a.SETTLE_LIST_ID else null end)),0) AS r00r99,
        <![CDATA[ IFNULL(COUNT(DISTINCT(case when (SUBSTR(b.dscg_diag_codg,1,1)='S') or
                    (SUBSTR(b.dscg_diag_codg,1,1)='T' and CONVERT(substr(b.dscg_diag_codg,2,2),SIGNED)>=0 and CONVERT(SUBSTR(b.dscg_diag_codg,2,2),SIGNED)<=98) then a.SETTLE_LIST_ID else null end)),0) ]]> AS s00t98,
        IFNULL(COUNT(DISTINCT(case when SUBSTR(b.dscg_diag_codg,1,1)='U' then a.SETTLE_LIST_ID else null end)),0) AS u00u99,
        <![CDATA[ IFNULL(COUNT(DISTINCT(case when (SUBSTR(b.dscg_diag_codg,1,1)='V') or (SUBSTR(b.dscg_diag_codg,1,1)='W') or (SUBSTR(b.dscg_diag_codg,1,1)='X') or
                    (SUBSTR(b.dscg_diag_codg,1,1)='Y' and CONVERT(substr(b.dscg_diag_codg,2,2),SIGNED)>=01 and CONVERT(SUBSTR(b.dscg_diag_codg,2,2),SIGNED)<=98) then a.SETTLE_LIST_ID else null end)),0) ]]> AS v01y98,
        IFNULL(COUNT(DISTINCT(case when SUBSTR(b.dscg_diag_codg,1,1)='Z' then a.SETTLE_LIST_ID else null end)),0) AS z00z99,
        <![CDATA[ IFNULL(COUNT(DISTINCT(case when (SUBSTR(b.dscg_diag_codg,1,3)='D49') or
                    (SUBSTR(b.dscg_diag_codg,1,1)='D' and CONVERT(substr(b.dscg_diag_codg,2,2),SIGNED)>=90 and CONVERT(SUBSTR(b.dscg_diag_codg,2,2),SIGNED)<=99) or
                    (SUBSTR(b.dscg_diag_codg,1,1)='E' and CONVERT(substr(b.dscg_diag_codg,2,2),SIGNED)>=91 and CONVERT(SUBSTR(b.dscg_diag_codg,2,2),SIGNED)<=99) or
                    (SUBSTR(b.dscg_diag_codg,1,1)='K' and CONVERT(substr(b.dscg_diag_codg,2,2),SIGNED)>=94 and CONVERT(SUBSTR(b.dscg_diag_codg,2,2),SIGNED)<=99) or
                    (SUBSTR(b.dscg_diag_codg,1,1)='P' and CONVERT(substr(b.dscg_diag_codg,2,2),SIGNED)>=97 and CONVERT(SUBSTR(b.dscg_diag_codg,2,2),SIGNED)<=99)
                    then a.SETTLE_LIST_ID else null end)),0)  ]]> AS other
        from som_dip_grp_info a
        <if test='queryParam.enableSeAns != null and queryParam.enableSeAns != "" and queryParam.enableSeAns == "1"'>
            LEFT JOIN som_hi_invy_bas_info q
            ON q.ID = a.SETTLE_LIST_ID
            INNER JOIN som_setl_cas_crsp p
            ON q.k00 = p.k00
        </if>
        inner join som_diag b on a.SETTLE_LIST_ID = b.SETTLE_LIST_ID
        where b.dscg_diag_codg is not null and b.dscg_diag_codg!='-' and  b.dscg_diag_codg!='--'
        <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
            AND  a.HOSPITAL_ID = #{queryParam.hospitalId}
        </if>
        <if test="queryParam.b16c!=null and queryParam.b16c!=''">
            AND a.dscg_caty_codg_inhosp = #{queryParam.b16c}
        </if>
        <if test="queryParam.queryIcd!=null and queryParam.queryIcd!=''">
            AND b.dscg_diag_codg = #{queryParam.queryIcd}
        </if>
        <if test="queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and
                queryParam.cy_end_date!=null and queryParam.cy_end_date!=''">
            AND a.dscg_time BETWEEN #{queryParam.cy_start_date} and CONCAT(#{queryParam.cy_end_date},' 23:59:59')
        </if>
        <if test="queryParam.seStartTime != null and queryParam.seStartTime != '' and
                queryParam.seEndTime != null and queryParam.seEndTime != ''">
            AND a.setl_end_time BETWEEN #{queryParam.seStartTime} and CONCAT(#{queryParam.seEndTime},' 23:59:59')
        </if>
        <if test="queryParam.doctorIdList!=null">
            AND (
            a.deptdrt_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.chfdr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.atddr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.ipdr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.resp_nurs_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.train_dr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.intn_dr in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.codr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.qltctrl_dr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.qltctrl_nurs_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            )
        </if>
        )x
    </select>
</mapper>