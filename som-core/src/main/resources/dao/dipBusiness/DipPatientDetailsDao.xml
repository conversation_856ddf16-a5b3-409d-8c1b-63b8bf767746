<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.som.dao.dipBusiness.DipPatientDetailsDao">
    <select id="selectPatientInfo" resultType="com.my.som.vo.dipBusiness.DipSTDHandlerVo">
        SELECT
        distinct
        case when a.A12C=1 then '男' else '女' end as gend,<!-- 性别 -->
        a.A14 as age,<!-- 年龄 -->
        IFNULL(IFNULL(D11,0)+IFNULL(D12,0)+IFNULL(D13,0)+IFNULL(D14,0),0) AS com_med_servfee, <!-- 综合医疗服务费 -->
        <!--        (IFNULL(IFNULL(D11,0)+IFNULL(D12,0)+IFNULL(D13,0)+IFNULL(D14,0),0) - IFNULL(d.com_med_servfee,0)) AS zhylfwfcy,&lt;!&ndash; 综合医疗服务费差异 &ndash;&gt;-->
        IFNULL(D11,0) AS ordn_med_servfee, <!-- 一般医疗服务费 -->
        IFNULL(D12,0) AS ordn_trt_oprt_fee, <!-- 一般治疗操作费 -->
        IFNULL(D13,0) AS nursfee, <!-- 护理费 -->
        IFNULL(D14,0) AS zhylfwqtf, <!-- 综合医疗服务其他费 -->
        IFNULL(IFNULL(D15,0)+IFNULL(D16,0)+IFNULL(D17,0)+IFNULL(D18,0),0) AS diag_fee, <!-- 诊断费 -->
        IFNULL(D15,0) AS cas_diag_fee, <!-- 病理诊断费 -->
        IFNULL(D16,0) AS lab_diag_fee, <!-- 实验室诊断费 -->
        IFNULL(D17,0) AS rdhy_diag_fee, <!-- 影像学诊断费 -->
        IFNULL(D18,0) AS clnc_diag_item_fee, <!-- 临床诊断项目费 -->
        IFNULL(IFNULL(D19,0)+IFNULL(D20,0),0) AS treat_fee, <!-- 治疗费 -->
        IFNULL(D19,0) AS nsrgtrt_item_fee, <!-- 非手术治疗项目费 -->
        IFNULL(D20,0) AS oprn_treat_fee, <!-- 手术治疗费 -->
        IFNULL(D21,0) AS rhab_fee, <!-- 康复费 -->
        IFNULL(D22,0) AS tcmdrug_fee, <!-- 中医费 -->
        IFNULL(D23,0) AS west_fee, <!-- 西药费 -->
        IFNULL(IFNULL(D24,0)+IFNULL(D25,0),0) AS zyf1, <!-- 中药费 -->
        IFNULL(D24,0) AS tcmpat_fee, <!-- 中成药费 -->
        IFNULL(D25,0) AS tcmherb, <!-- 中草药费-->
        IFNULL(IFNULL(D26,0)+IFNULL(D27,0)+IFNULL(D28,0)+IFNULL(D29,0)+IFNULL(D30,0),0) AS blood_blo_pro_fee, <!-- 血液和血液制品费 -->
        IFNULL(D26,0) AS blo_fee, <!-- 血费 -->
        IFNULL(D27,0) AS bdblzpf, <!-- 白蛋白类制品费 -->
        IFNULL(D28,0) AS qdblzpf, <!-- 球蛋白类制品费 -->
        IFNULL(D29,0) AS nxyzlzpf, <!-- 凝血因子类制品费 -->
        IFNULL(D30,0) AS xbyzlzpf, <!-- 细胞因子类制品费 -->
        IFNULL(IFNULL(D31,0)+IFNULL(D32,0)+IFNULL(D33,0),0) AS mcs_fee, <!-- 耗材费 -->
        IFNULL(D31,0) AS jcyycxyyclf, <!-- 检查用一次性医用材料费 -->
        IFNULL(D32,0) AS trt_use_dspo_med_matlfee, <!-- 治疗用一次性医用材料费 -->
        IFNULL(D33,0) AS oprn_use_dspo_med_matlfee, <!-- 手术用一次性医用材料费 -->
        IFNULL(D34,0) AS oth_fee, <!-- 其他费 -->
        D01 AS inHosTotalCost, <!-- 住院总费用 -->
        IFNULL(ROUND((IFNULL(D01,0) - IFNULL(CONVERT(AES_DECRYPT(UNHEX(c.dip_standard_avg_fee_same_lv ),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) USING utf8),0)),2),0) AS zyfycy,<!-- 住院费用与标杆差异 -->
        ROUND((IFNULL(B20,0) - IFNULL(CONVERT(AES_DECRYPT(UNHEX(c.dip_standard_ipt_days_same_lv ),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) USING utf8),0)),2) as zytscy,<!-- 住院天数与标杆差异 -->
        ROUND(((IFNULL(b.drugfee/a.D01,0))*100 - IFNULL(d.drug_ratio,0)*100),2) AS yzbcy,
        ROUND(((IFNULL(b.mcs_fee/(a.D01-b.drugfee),0))*100 - IFNULL(d.mcs_fee_rat,0)*100),2) AS hzbcy,
        SUBSTRING(B15,1,10) AS outHosTime, <!-- 出院时间 -->
        SUBSTRING(B12,1,10) AS inHosTime, <!-- 入院时间 -->
        B16C AS deptCode, <!-- 出院科室编码 -->
        e.`NAME` AS deptName, <!-- 出院科室编码 -->
        A11 AS name, <!-- 患者姓名 -->
        a.ID AS id, <!-- settle_list_id-->
        a.A48 AS patientId, <!-- 病案号 -->
        TRIM(B25C) AS residentCode, <!-- 住院医师编码 -->
        TRIM(B25N) AS residentName, <!-- 住院医师姓名 -->
        IFNULL(B20,0) AS inHosDays, <!-- 住院天数 -->
        b.dip_codg AS dipCodg, <!-- DIP编码 -->
        b.DIP_NAME AS dipName, <!-- DIP名称 -->
        IFNULL(b.drugfee,0) AS ypf, <!-- 药品费 -->
        ROUND((IFNULL(b.drugfee/a.D01,0))*100,2) AS yzb, <!-- 药占比 -->
        IFNULL(b.mcs_fee,0) AS clf, <!-- 材料费 -->
        ROUND((IFNULL(b.mcs_fee/(a.D01-b.drugfee),0))*100,2) AS hzb, <!-- 耗占比 -->
        IFNULL(CONVERT(AES_DECRYPT(UNHEX(c.dip_standard_avg_fee_same_lv ),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) USING utf8),0) AS bgzyfyjb, <!-- 标杆住院费用（级别） -->
        IFNULL(CONVERT(AES_DECRYPT(UNHEX(c.dip_standard_ipt_days_same_lv ),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) USING utf8),0) AS bgzytsjb, <!-- 标杆住院天数（级别） -->
        ROUND(IFNULL(d.drug_ratio,0)*100,2) AS bgyzb, <!-- 标杆药占比 -->
        ROUND(IFNULL(d.mcs_fee_rat,0)*100,2) AS bghzb, <!-- 标杆耗占比 -->
        IFNULL(d.avg_fee,0) AS ljfy,<!-- 例均费用 -->
        IFNULL(d.com_med_servfee,0) AS bgzhylfwf, <!-- 标杆综合医疗服务费 -->
        IFNULL(d.ordn_med_servfee,0) AS bgybylfwf, <!-- 标杆一般医疗服务费 -->
        IFNULL(d.ordn_trt_oprt_fee,0) AS bgybzlczf, <!-- 标杆一般治疗操作费 -->
        IFNULL(d.nursfee,0) AS bghlf, <!-- 标杆护理费 -->
        IFNULL(d.oth_fee_com,0) AS bgzhylfwqtf, <!-- 标杆综合医疗服务其他费 -->
        IFNULL(d.diag_fee,0) AS bgzdf, <!-- 标杆诊断费 -->
        IFNULL(d.cas_diag_fee,0) AS bgblzdf, <!-- 标杆病理诊断费 -->
        IFNULL(d.lab_diag_fee,0) AS bgsyszdf, <!-- 标杆实验室诊断费 -->
        IFNULL(d.rdhy_diag_fee,0) AS bgyxxzdf, <!-- 标杆影像学诊断费 -->
        IFNULL(d.clnc_diag_item_fee,0) AS bglczdxmf, <!-- 标杆临床诊断项目费 -->
        IFNULL(d.treat_fee,0) AS bgzlf, <!-- 标杆治疗费 -->
        IFNULL(d.nsrgtrt_item_fee,0) AS bgfsszlxmf, <!-- 标杆非手术治疗项目费 -->
        IFNULL(d.oprn_treat_fee,0) AS bgsszlf, <!-- 标杆手术治疗费 -->
        IFNULL(d.rhab_fee,0) AS bgkff, <!-- 标杆康复费 -->
        IFNULL(d.tcm_treat_fee,0) AS bgzyf, <!-- 标杆中医费 -->
        IFNULL(d.west_fee,0) AS bgxyf, <!-- 标杆西药费 -->
        IFNULL(d.tcmdrug_fee,0) AS bgzyf1, <!-- 标杆中药费 -->
        IFNULL(d.tcmpat_fee,0) AS bgzcyf, <!-- 标杆中成药费 -->
        IFNULL(d.tcmherb,0) AS bgzcyf1, <!-- 标杆中草药费 -->
        IFNULL(d.blood_blo_pro_fee,0) AS bgxyhxyzpf, <!-- 标杆血液和血液制品费 -->
        IFNULL(d.blo_fee,0) AS bgxf, <!-- 标杆血费 -->
        IFNULL(d.albu_clss_prod_fee,0) AS bgbdblzpf, <!-- 标杆白蛋白类制品费 -->
        IFNULL(d.glon_clss_prod_fee,0) AS bgqdblzpf, <!-- 标杆球蛋白类制品费 -->
        IFNULL(d.clotfac_clss_prod_fee,0) AS bgnxyzlzpf, <!-- 标杆凝血因子类制品费 -->
        IFNULL(d.cyki_clss_prod_fee,0) AS bgxbyzlzpf, <!-- 标杆细胞因子类制品费 -->
        IFNULL(d.mcs_fee,0) AS bghcf, <!-- 标杆耗材费 -->
        IFNULL(d.exam_use_dspo_med_matlfee,0) AS bgjcyycxyyclf, <!-- 标杆检查用一次性医用材料费 -->
        IFNULL(d.trt_use_dspo_med_matlfee,0) AS bgzlyycxyyclf, <!-- 标杆治疗用一次性医用材料费 -->
        IFNULL(d.oprn_use_dspo_med_matlfee,0) AS bgssyycxyyclf, <!-- 标杆手术用一次性医用材料费 -->
        IFNULL(d.oth_fee,0) AS bgqtf <!-- 标杆其他费 -->
        FROM som_hi_invy_bas_info a
        LEFT JOIN som_dip_grp_info b
        ON a.ID = b.SETTLE_LIST_ID
        LEFT JOIN som_dip_standard c
        ON b.dip_codg = c.dip_codg
        AND SUBSTR(b.dscg_time,1,4) = c.STANDARD_YEAR
        AND b.is_used_asst_list = c.is_used_asst_list
        AND b.asst_list_age_grp = c.asst_list_age_grp
        AND b.asst_list_dise_sev_deg = c.asst_list_dise_sev_deg
        AND b.asst_list_tmor_sev_deg = c.asst_list_tmor_sev_deg
        AND c.ACTIVE_FLAG = 1
        LEFT JOIN som_std_fee d
        ON b.dip_codg = d.`CODE`
        AND SUBSTR(b.dscg_time,1,4) = d.STANDARD_YEAR
        AND b.asst_list_age_grp = d.asst_list_age_grp
        AND b.asst_list_dise_sev_deg = d.asst_list_dise_sev_deg
        AND b.asst_list_tmor_sev_deg = d.asst_list_tmor_sev_deg
        AND d.TYPE = 1
        LEFT JOIN som_dept e
        ON a.B16C = e.`CODE`
        WHERE b.grp_stas = 1
        <if test="diseaseCodes != null and diseaseCodes.size() > 0">
            <foreach collection="diseaseCodes" item="diseaseCode" open="AND b.dip_codg IN (" separator="," close=")">
                #{diseaseCode}
            </foreach>
        </if>
        <if test="doctorCodes != null and doctorCodes.size() > 0">
            <foreach collection="doctorCodes" item="drCodg" open="AND a.B25C IN (" separator="," close=")">
                #{drCodg}
            </foreach>
        </if>
        <if test="searchType != null and searchType != ''">
            <choose>
                <when test="searchType == 1">
                    <if test="searchVal != null and searchVal != ''">
                        AND (a.B16C like concat('%',#{searchVal},'%') or e.`NAME` like concat('%',#{searchVal},'%'))
                    </if>
                </when>
                <when test="searchType == 4">
                    <if test="diseType == 1 and searchVal != null and searchVal != ''">
                        AND b.dip_codg = #{searchVal}
                    </if>
                    <if test="diseType == 2 and searchVal != null and searchVal != ''">
                        AND (b.dip_codg like concat('%',#{searchVal},'%') or b.DIP_NAME like concat('%',#{searchVal},'%'))
                    </if>
                </when>
                <when test="searchType == 3">
                    <if test="searchVal != null and searchVal != ''">
                        AND (a.B25C like concat('%',#{searchVal},'%') or a.B25N like concat('%',#{searchVal},'%'))
                    </if>
                </when>
                <when test="searchType == 2">
                    <if test="searchVal != null and searchVal != ''">
                        AND (a.A11 like concat('%',#{searchVal},'%') or a.A48 like concat('%',#{searchVal},'%'))
                    </if>
                </when>
            </choose>
        </if>
        <include refid="com.my.som.mapper.newBusiness.common.NewBusinessCommonAnalysisMapper.queryCommon"></include>
        <if test="residentCode != null and residentCode != ''">
            and a.B25C = #{residentCode}
        </if>
        <!--        <if test="residentCode != null and residentCode != ''">-->
        <!--            AND a.B25C like concat('%',#{residentCode},'%')-->
        <!--        </if>-->
        <if test="dateType != null and dateType != ''">
            <choose>
                <when test="dateType == 1">
                    <if test="curYear != null and curYear != ''">
                        AND SUBSTR(a.B15,1,4) = #{curYear}
                    </if>
                </when>
                <when test="dateType == 2">
                    <if test="curMonth != null and curMonth != ''">
                        AND SUBSTR(a.B15,1,7) = #{curMonth}
                    </if>
                </when>
            </choose>
        </if>
        <if test="ids != null and ids.size() > 0">
            <foreach collection="ids" item="id" open="AND a.ID IN (" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="id != null and id !='' ">
            AND a.ID=#{id}
        </if>
    </select>
</mapper>