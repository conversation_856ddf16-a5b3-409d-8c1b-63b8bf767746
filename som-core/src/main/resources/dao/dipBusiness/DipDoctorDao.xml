<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.som.dao.dipBusiness.DipDoctorDao">
    <select id="list" resultType="com.my.som.vo.dipBusiness.DipDoctorIndexVo">
        select p.*
        <if test="queryParam.queryType==1">
            <trim prefix=",">
                m.name AS priOutHosDeptName
            </trim>
        </if>
        from (
        select
        <if test="queryParam.queryType==1">
            x.dscg_caty_codg_inhosp AS priOutHosDeptCode,
        </if>
        x.drCodg AS drCodg,
        x.drName AS drName,
        <if test="queryParam.queryType==2">
            group_concat(distinct x.dscg_caty_name_inhosp) AS doctorDepts,
        </if>
        IFNULL(COUNT(1),0) AS medicalRecordNum,
        IFNULL(COUNT(case when x.grp_stas = '1' then 1 else null end),0) AS groupNum,
        IFNULL(COUNT(case when x.grp_stas != '1' then 1 else null end),0) AS noGroupNum,
        IFNULL(count(distinct (case when x.grp_stas = '1' then x.dip_codg else null end)),0) AS dipGroupNum,
        IFNULL(ROUND(SUM(x.dip_wt), 4),0) AS totalAreaWeight,
        IFNULL(ROUND(SUM(x.hosWeight), 4),0) AS totalHosWeight,
        IFNULL(ROUND(AVG(x.act_ipt), 2),0) AS avgDays, <!--平均住院日-->
        IFNULL(ROUND(AVG(x.ipt_sumfee), 2),0) AS avgCost, <!--平均住院费用-->
        IFNULL(ROUND(IFNULL(sum(IFNULL(x.act_ipt,0)/NULLIF(x.dip_standard_ipt_days, 0))/COUNT(1),0),2),0) as timeIndex, <!--  时间消耗指数   -->
        IFNULL(ROUND(IFNULL(sum(IFNULL(x.ipt_sumfee,0)/NULLIF(x.dip_standard_inpf, 0))/COUNT(1),0),2),0) as costIndex,  <!--  费用消耗指数   -->
        IFNULL(CONCAT(ROUND(SUM(x.drugfee)/NULLIF(SUM(x.ipt_sumfee),0)*100,2),'%'),0) AS medicalCostRate,<!--  药品费占比   -->
        IFNULL(CONCAT(ROUND(SUM(x.mcs_fee)/NULLIF(SUM(x.ipt_sumfee),0)*100,2),'%'),0) AS materialCostRate <!--  耗材费占比   -->
        from
        (
        select
        m.*,
        n.hosWeight AS hosWeight
        from(
        select
        <if test="queryParam.doctorType!=null and queryParam.doctorType!=''">
            (CASE #{queryParam.doctorType}#
            WHEN 'dct0' THEN e.ipdr_code
            WHEN 'dct1' THEN e.atddr_code
            WHEN 'dct2' THEN e.deptdrt_code
            WHEN 'dct3' THEN e.chfdr_code
            WHEN 'dct4' THEN e.train_dr_code
            WHEN 'dct5' THEN e.qltctrl_dr_code
            WHEN 'dct6' THEN e.intn_dr
            ELSE  e.ipdr_name
            END) AS drCodg,
            (CASE #{queryParam.doctorType}#
            WHEN 'dct0' THEN e.ipdr_name
            WHEN 'dct1' THEN e.atddr_name
            WHEN 'dct2' THEN e.deptdrt_name
            WHEN 'dct3' THEN e.chfdr_name
            WHEN 'dct4' THEN e.train_dr_name
            WHEN 'dct5' THEN e.qltctrl_dr_name
            WHEN 'dct6' THEN e.intn_dr
            ELSE  e.ipdr_name
            END) AS drName,
        </if>
        <if test="queryParam.doctorType==null or queryParam.doctorType==''">
            c.drCodg as drCodg,
            c.drName as drName,
        </if>
        SUBSTR(e.dscg_time,1,4) as year,
        e.dscg_caty_codg_inhosp as dscg_caty_codg_inhosp,
        e.dscg_caty_name_inhosp AS dscg_caty_name_inhosp,
        a.grp_stas as grp_stas,
        a.dip_codg as dip_codg,
        e.act_ipt as act_ipt,
        e.ipt_sumfee as ipt_sumfee,
        convert(AES_DECRYPT(UNHEX(f.dip_standard_ipt_days),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) using utf8) as dip_standard_ipt_days,
        convert(AES_DECRYPT(UNHEX(f.dip_standard_inpf),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) using utf8) as dip_standard_inpf,
        e.drugfee as drugfee,
        e.mcs_fee as mcs_fee,
        f.dip_wt as dip_wt
        from som_dip_grp_rcd a
        inner join som_dip_grp_info e on a.SETTLE_LIST_ID = e.SETTLE_LIST_ID
        <if test='queryParam.enableSeAns != null and queryParam.enableSeAns != "" and queryParam.enableSeAns == "1"'>
            LEFT JOIN som_hi_invy_bas_info q
            ON q.ID = e.SETTLE_LIST_ID
            INNER JOIN som_setl_cas_crsp p
            ON q.k00 = p.k00
        </if>

        AND ifnull( a.asst_list_age_grp,  '未使用' )= ifnull( e.asst_list_age_grp,  '未使用' )
        AND ifnull( a.asst_list_dise,  '未使用' )= ifnull( e.asst_list_dise_sev_deg,  '未使用' )
        AND ifnull( a.asst_list_tmor_sev_deg, '未使用' )= ifnull(e.asst_list_tmor_sev_deg, '未使用' )
        AND ifnull( a.auxiliary_burn, '未使用' )= ifnull(e.auxiliary_burn, '未使用' )

        left join som_dip_standard f on a.dip_codg=f.dip_codg AND SUBSTR(e.dscg_time,1,4) = f.STANDARD_YEAR AND f.ACTIVE_FLAG = '1'

        AND ifnull( a.asst_list_age_grp,  '未使用' )= ifnull( f.asst_list_age_grp,  '未使用' )
        AND ifnull( a.asst_list_dise,  '未使用' )= ifnull( f.asst_list_dise_sev_deg,  '未使用' )
        AND ifnull( a.asst_list_tmor_sev_deg,  '未使用' )= ifnull(f.asst_list_tmor_sev_deg, '未使用' )
        AND ifnull( a.auxiliary_burn,  '未使用' )= ifnull(f.auxiliary_burn, '未使用' )
            AND a.HOSPITAL_ID = f.HOSPITAL_ID
        <if test="queryParam.doctorType==null or queryParam.doctorType==''">
            inner join (
                select
                SETTLE_LIST_ID as settle_list_id,
                ipdr_code as drCodg,
                ipdr_name as drName
                FROM som_dip_grp_info
                where ipdr_name is not null and ipdr_name !='-' and ipdr_name !='未填写'
                <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
                    AND  HOSPITAL_ID = #{queryParam.hospitalId}
                </if>
                <if test="queryParam.b16c!=null and queryParam.b16c!=''">
                    AND dscg_caty_codg_inhosp = #{queryParam.b16c}
                </if>
                <if test="queryParam.inStartTime!=null and queryParam.inStartTime!='' and
                    queryParam.inEndTime!=null and queryParam.inEndTime != ''">
                    AND adm_time BETWEEN #{queryParam.inStartTime} and CONCAT(#{queryParam.inEndTime},' 23:59:59')
                </if>
                <if test="(queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and
                 queryParam.cy_end_date!=null and queryParam.cy_end_date!='') or queryParam.inHosFlag == 2">
                    AND dscg_time BETWEEN #{queryParam.cy_start_date} and CONCAT(#{queryParam.cy_end_date},' 23:59:59')
                </if>
                <if test="(queryParam.seStartTime!=null and queryParam.seStartTime!='' and
                     queryParam.seEndTime!=null and queryParam.seEndTime!='') or queryParam.inHosFlag == 2">
                    AND setl_end_time BETWEEN #{queryParam.seStartTime} and CONCAT(#{queryParam.seEndTime},' 23:59:59')
                </if>
                <if test="queryParam.drCodg!=null and queryParam.drCodg!=''">
                    AND `INHOS_DOCTOR_CODE` = #{queryParam.drCodg}
                </if>
                group by settle_list_id,drCodg,drName
            ) c on e.settle_list_id = c.settle_list_id
        </if>
        where 1=1
        <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
            AND  e.HOSPITAL_ID = #{queryParam.hospitalId}
        </if>
        <if test="queryParam.b16c!=null and queryParam.b16c!=''">
            AND e.dscg_caty_codg_inhosp = #{queryParam.b16c}
        </if>
        <if test="queryParam.inStartTime!=null and queryParam.inStartTime!='' and
                queryParam.inEndTime!=null and queryParam.inEndTime != ''">
            AND e.adm_time BETWEEN #{queryParam.inStartTime} and CONCAT(#{queryParam.inEndTime},' 23:59:59')
        </if>
        <if test="(queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and
                 queryParam.cy_end_date!=null and queryParam.cy_end_date!='') or queryParam.inHosFlag == 2">
            AND e.dscg_time BETWEEN #{queryParam.cy_start_date} and CONCAT(#{queryParam.cy_end_date},' 23:59:59')
        </if>
        <if test="(queryParam.seStartTime!=null and queryParam.seStartTime!='' and
                 queryParam.seEndTime!=null and queryParam.seEndTime!='') or queryParam.inHosFlag == 2">
            AND e.setl_end_time BETWEEN #{queryParam.seStartTime} and CONCAT(#{queryParam.seEndTime},' 23:59:59')
        </if>
        <if test="queryParam.doctorIdList!=null">
            AND (
            e.deptdrt_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            e.chfdr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            e.atddr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            e.ipdr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            e.resp_nurs_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            e.train_dr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            e.intn_dr in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            e.codr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            e.qltctrl_dr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            e.qltctrl_nurs_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            )
        </if>
        ) m left join (
        select a.dip_codg,
        IFNULL( AVG( a.ipt_sumfee )/ NULLIF( convert ( AES_DECRYPT( UNHEX( MAX(c.AREA_AVG_COST) ), ${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) using utf8 ), 0 ), 0 ) AS hosWeight
        from som_dip_grp_info a
        left join som_dip_grp_rcd b on a.SETTLE_LIST_ID = b.SETTLE_LIST_ID
        AND b.used_asst_list = a.is_used_asst_list
        AND b.asst_list_age_grp = a.asst_list_age_grp
        AND b.asst_list_dise = a.asst_list_dise_sev_deg
        AND b.asst_list_tmor_sev_deg = a.asst_list_tmor_sev_deg
        left JOIN som_regn_year_sum_data c on SUBSTR( a.dscg_time, 1, 4 ) = c.STANDARD_YEAR
        where b.grp_stas = '1'
          AND a.HOSPITAL_ID = #{queryParam.hospitalId}
        <if test="queryParam.inStartTime!=null and queryParam.inStartTime!='' and
                queryParam.inEndTime!=null and queryParam.inEndTime != ''">
            AND a.adm_time BETWEEN #{queryParam.inStartTime} and CONCAT(#{queryParam.inEndTime},' 23:59:59')
        </if>
        <if test="(queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and
                 queryParam.cy_end_date!=null and queryParam.cy_end_date!='') or queryParam.inHosFlag == 2">
            AND a.dscg_time BETWEEN #{queryParam.cy_start_date} and CONCAT(#{queryParam.cy_end_date},' 23:59:59')
        </if>
        group by a.dip_codg
        ) n on m.dip_codg = n.dip_codg
        ) x
        where x.drName is not null and x.drName !='-' and x.drName !='未填写'
        <if test="queryParam.drCodg!=null and queryParam.drCodg!=''">
            AND x.drCodg= #{queryParam.drCodg}
        </if>
        <if test="queryParam.doctorIdList!=null">
            AND x.drCodg in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="queryParam.queryType==1">
            GROUP BY x.dscg_caty_codg_inhosp,x.drCodg,x.drName
        </if>
        <if test="queryParam.queryType==2">
            GROUP BY x.drCodg,x.drName
        </if>
        ORDER BY totalHosWeight desc
        )  p
        <if test="queryParam.queryType==1">
            left join som_dept m
            on p.priOutHosDeptCode = m.code
        </if>
    </select>

    <select id="getCountInfo" resultType="com.my.som.vo.dipBusiness.DipDoctorCountVo">
        select
            x.drCodg AS drCodg,
            x.drName AS drName,
            IFNULL(count(1),0) AS totalMedicalRecordNum,
            IFNULL(ROUND(count(1) / NULLIF(y.totalMedicalNum,0)*100,2),0) AS totalMedicalRecordNumRate,
            IFNULL(count(case when x.grp_stas = '1' then 1 else null end),0)   AS inGroupMedicalNum,
            IFNULL(ROUND(count(case when x.grp_stas = '1' then 1 else null end) /
              NULLIF(y.inGroupMedicalNum,0)*100,2),0) AS  inGroupMedicalNumRate,
            IFNULL(count(case when x.grp_stas != '1' then 1 else null end),0)  AS notGroupMedicalNum,
            IFNULL(ROUND(count(case when x.grp_stas != '1' then 1 else null end) /
              NULLIF(y.inGroupMedicalNum,0)*100,2),0) AS  notGroupMedicalNumRate,
            IFNULL(count(distinct (case when x.grp_stas = '1' then x.dip_codg else null end)),0)   AS dipGroupNum,
            IFNULL(ROUND(count(distinct (case when x.grp_stas = '1' then x.dip_codg else null end)) /
              NULLIF(y.dipGroupNum,0)*100,2),0) AS  dipGroupNumRate,
            IFNULL(ROUND(AVG(x.act_ipt), 2),0)   AS avgDays,
            y.hosAvgDays as hosAvgDays,
            IFNULL(ROUND(AVG(CASE WHEN x.grp_stas = '1' THEN x.act_ipt ELSE NULL END), 2),0)   AS inGroupAvgDays,
            y.hosInGroupAvgDays as hosInGroupAvgDays,
            IFNULL(ROUND(AVG(x.ipt_sumfee), 2),0)  AS avgCost,
            y.hosAvgCost as hosAvgCost,
            IFNULL(ROUND(AVG(CASE WHEN x.grp_stas = '1' THEN x.ipt_sumfee ELSE NULL END), 2),0)    AS inGroupAvgCost,
            y.hosInGroupAvgCost as hosInGroupAvgCost,
            IFNULL(ROUND(SUM(CASE WHEN x.grp_stas = '1' THEN x.act_ipt ELSE NULL END / NULLIF(x.dip_standard_ipt_days,0)) /
            NULLIF(COUNT(CASE WHEN x.grp_stas = '1' THEN 1 ELSE NULL END),0), 2),0)  AS timeIndex,
            y.hosTimeIndex as hosTimeIndex,
            IFNULL(ROUND(SUM(CASE WHEN x.grp_stas = '1' THEN x.ipt_sumfee ELSE NULL END / NULLIF(x.dip_standard_inpf,0)) /
            NULLIF(COUNT(CASE WHEN x.grp_stas = '1' THEN 1 ELSE NULL END),0), 2),0)   AS costIndex,
            y.hosCostIndex as hosCostIndex,
            IFNULL(ROUND(AVG(x.drugfee), 2),0)   AS avgDrugFee,
            y.hosAvgMedicalCost as hosAvgMedicalCost,
            IFNULL(ROUND(AVG(x.mcs_fee), 2),0)   AS avgMcsFee,
            y.hosAvgMaterialCost as hosAvgMaterialCost
        from (
        select
        <if test="queryParam.doctorType!=null and queryParam.doctorType!=''">
            (CASE #{queryParam.doctorType}#
            WHEN 'dct0' THEN e.ipdr_code
            WHEN 'dct1' THEN e.atddr_code
            WHEN 'dct2' THEN e.deptdrt_code
            WHEN 'dct3' THEN e.chfdr_code
            WHEN 'dct4' THEN e.train_dr_code
            WHEN 'dct5' THEN e.qltctrl_dr_code
            WHEN 'dct6' THEN e.intn_dr
            ELSE  e.ipdr_name
            END) AS drCodg,
            (CASE #{queryParam.doctorType}#
            WHEN 'dct0' THEN e.ipdr_name
            WHEN 'dct1' THEN e.atddr_name
            WHEN 'dct2' THEN e.deptdrt_name
            WHEN 'dct3' THEN e.chfdr_name
            WHEN 'dct4' THEN e.train_dr_name
            WHEN 'dct5' THEN e.qltctrl_dr_name
            WHEN 'dct6' THEN e.intn_dr
            ELSE  e.ipdr_name
            END) AS drName,
        </if>
        <if test="queryParam.doctorType==null or queryParam.doctorType==''">
            c.drCodg as drCodg,
            c.drName as drName,
        </if>
            e.dscg_caty_codg_inhosp as dscg_caty_codg_inhosp,
            e.dscg_caty_name_inhosp AS dscg_caty_name_inhosp,
            a.grp_stas as grp_stas,
            a.dip_codg as dip_codg,
            e.act_ipt as act_ipt,
            e.ipt_sumfee as ipt_sumfee,
            convert(AES_DECRYPT(UNHEX(f.dip_standard_ipt_days),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) using utf8) as dip_standard_ipt_days,
            convert(AES_DECRYPT(UNHEX(f.dip_standard_inpf),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) using utf8) as dip_standard_inpf,
            e.drugfee as drugfee,
            e.mcs_fee as mcs_fee
        from som_dip_grp_rcd a
        left join som_dip_grp_info e on a.SETTLE_LIST_ID = e.SETTLE_LIST_ID
        <if test='queryParam.enableSeAns != null and queryParam.enableSeAns != "" and queryParam.enableSeAns == "1"'>
            LEFT JOIN som_hi_invy_bas_info q
            ON q.ID = e.SETTLE_LIST_ID
            INNER JOIN som_setl_cas_crsp p
            ON q.k00 = p.k00
        </if>
        left join som_dip_standard f on a.dip_codg=f.dip_codg AND SUBSTR(e.dscg_time,1,4) = f.STANDARD_YEAR AND f.ACTIVE_FLAG = '1' AND a.HOSPITAL_ID = f.HOSPITAL_ID
        <if test="queryParam.doctorType==null or queryParam.doctorType==''">
            inner join (
            select
            b.settle_list_id as settle_list_id,
            b.drCodg as drCodg,
            b.drName as drName
            from (
            select
            SETTLE_LIST_ID as settle_list_id,
            ipdr_code as drCodg,
            ipdr_name as drName
            FROM som_dip_grp_info
            where ipdr_name is not null and ipdr_name !='-' and ipdr_name !='未填写'
            <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
                AND  HOSPITAL_ID = #{queryParam.hospitalId}
            </if>
            <if test="queryParam.b16c!=null and queryParam.b16c!=''">
                AND dscg_caty_codg_inhosp = #{queryParam.b16c}
            </if>
            <if test="queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and queryParam.cy_end_date!=null and queryParam.cy_end_date!=''">
                AND  dscg_time BETWEEN #{queryParam.cy_start_date} and CONCAT(#{queryParam.cy_end_date},' 23:59:59')
            </if>
            <if test="queryParam.seStartTime!=null and queryParam.seStartTime!='' and queryParam.seEndTime!=null and queryParam.seEndTime!=''">
                AND  setl_end_time BETWEEN #{queryParam.seStartTime} and CONCAT(#{queryParam.seEndTime},' 23:59:59')
            </if>
            <if test="queryParam.drCodg!=null and queryParam.drCodg!=''">
                AND `INHOS_DOCTOR_CODE` = #{queryParam.drCodg}
            </if>
            ) b group by settle_list_id,drCodg,drName
            ) c on e.settle_list_id = c.settle_list_id
        </if>
        where 1=1
        <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
            AND  e.HOSPITAL_ID = #{queryParam.hospitalId}
        </if>
        <if test="queryParam.b16c!=null and queryParam.b16c!=''">
            AND e.dscg_caty_codg_inhosp = #{queryParam.b16c}
        </if>
        <if test="queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and queryParam.cy_end_date!=null and queryParam.cy_end_date!=''">
            AND e.dscg_time BETWEEN #{queryParam.cy_start_date} and CONCAT(#{queryParam.cy_end_date},' 23:59:59')
        </if>
        <if test="queryParam.seStartTime!=null and queryParam.seStartTime!='' and queryParam.seEndTime!=null and queryParam.seEndTime!=''">
            AND e.setl_end_time BETWEEN #{queryParam.seStartTime} and CONCAT(#{queryParam.seEndTime},' 23:59:59')
        </if>
        <if test="queryParam.doctorIdList!=null">
            AND (
            e.deptdrt_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            e.chfdr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            e.atddr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            e.ipdr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            e.resp_nurs_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            e.train_dr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            e.intn_dr in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            e.codr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            e.qltctrl_dr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            e.qltctrl_nurs_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            )
        </if>
        ) x
        cross join (
            select
                IFNULL(count(1),0) AS totalMedicalNum,
                IFNULL(count(case when a.grp_stas = '1' then 1 else null end),0) AS inGroupMedicalNum,
                IFNULL(count(case when a.grp_stas != '1' then 1 else null end),0) AS notGroupMedicalNum,
                IFNULL(count(distinct (case when a.grp_stas = '1' then a.dip_codg else null end)),0) AS dipGroupNum,
                IFNULL(ROUND(AVG(b.act_ipt), 2),0) AS hosAvgDays,
                IFNULL(ROUND(AVG(CASE WHEN a.grp_stas = '1' THEN b.act_ipt ELSE NULL END), 2),0) AS hosInGroupAvgDays,
                IFNULL(ROUND(AVG(b.ipt_sumfee), 2),0) AS hosAvgCost,
                IFNULL(ROUND(AVG(CASE WHEN a.grp_stas = '1' THEN b.ipt_sumfee ELSE NULL END), 2),0) AS hosInGroupAvgCost,
                IFNULL(ROUND(SUM(CASE WHEN a.grp_stas = '1' THEN b.act_ipt ELSE NULL END / NULLIF(convert(AES_DECRYPT(UNHEX(c.dip_standard_ipt_days),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) using utf8),0))
                /NULLIF(COUNT(CASE WHEN a.grp_stas = '1' THEN 1 ELSE NULL END),0), 2),0) AS hosTimeIndex,
                IFNULL(ROUND(SUM(CASE WHEN a.grp_stas = '1' THEN b.ipt_sumfee ELSE NULL END /
                NULLIF(convert(AES_DECRYPT(UNHEX(c.dip_standard_inpf),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) using utf8),0)) /NULLIF(COUNT(CASE WHEN a.grp_stas = '1' THEN 1 ELSE NULL END),0), 2),0) AS hosCostIndex,
                IFNULL(ROUND(AVG(b.drugfee), 2),0) AS hosAvgMedicalCost,
                IFNULL(ROUND(AVG(b.mcs_fee), 2),0)   AS hosAvgMaterialCost
            from som_dip_grp_rcd a
            left join som_dip_grp_info b on a.SETTLE_LIST_ID = b.SETTLE_LIST_ID
            left join som_dip_standard c on a.dip_codg=c.dip_codg AND SUBSTR(b.dscg_time,1,4) = c.STANDARD_YEAR AND c.ACTIVE_FLAG = '1'
            where 1 = 1
            <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
                AND b.HOSPITAL_ID = #{queryParam.hospitalId}
            </if>
            <if test="queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and queryParam.cy_end_date!=null and queryParam.cy_end_date!=''">
                AND b.dscg_time BETWEEN #{queryParam.cy_start_date} and CONCAT(#{queryParam.cy_end_date},' 23:59:59')
            </if>
            <if test="queryParam.seStartTime!=null and queryParam.seStartTime!='' and queryParam.seEndTime!=null and queryParam.seEndTime!=''">
                AND b.setl_end_time BETWEEN #{queryParam.seStartTime} and CONCAT(#{queryParam.seEndTime},' 23:59:59')
            </if>
        ) y
        where x.drName is not null and x.drName !='-' and x.drName !='未填写'
        <if test="queryParam.drCodg!=null and queryParam.drCodg!=''">
            AND x.drCodg= #{queryParam.drCodg}
        </if>
        <if test="queryParam.doctorIdList!=null">
            AND x.drCodg in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="queryParam.queryType==1">
            GROUP BY x.dscg_caty_codg_inhosp,x.dscg_caty_name_inhosp,x.drCodg,x.drName, y.totalMedicalNum
        </if>
        <if test="queryParam.queryType==2">
            GROUP BY x.drCodg,x.drName, y.totalMedicalNum
        </if>
        ORDER BY dipGroupNum desc
    </select>
</mapper>