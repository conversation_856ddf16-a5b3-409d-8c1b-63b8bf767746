<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.som.dao.dipBusiness.DipHospitalCoefficientDao">

    <!-- 查询医院系数 -->
    <select id="queryHospitalCoefficient" resultType="com.my.som.vo.dipBusiness.DipDeptIndexVo">
        SELECT a.year AS year,
               CASE WHEN a.unstableDisease = 1 THEN ROUND(IFNULL(IFNULL(SUM(a.refer_sco),0)/NULLIF(SUM(a.number),0),0)/100,2) ELSE NULL END AS cmi,
               SUM(a.timeIndex) AS timeIndex,
               SUM(a.costIndex) AS costIndex
        FROM
             (
                 SELECT b.STANDARD_YEAR AS year,
                        COUNT(1) AS number,
                        IFNULL(SUM(b.refer_sco),0) AS refer_sco,
                        IFNULL(IFNULL(AVG(a.act_ipt),0)/NULLIF(CONVERT(AES_DECRYPT(UNHEX(b.dip_standard_ipt_days_same_lv),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) USING utf8),0) - 1,0) AS timeIndex,
                        IFNULL(IFNULL(AVG(a.ipt_sumfee),0)/NULLIF(CONVERT (AES_DECRYPT(UNHEX( b.last_year_avg_fee ),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) USING utf8),0) - 1,0) AS costIndex,
                        CASE WHEN c.dip_codg IS NULL THEN 1 ELSE 0 END AS unstableDisease
                 FROM som_dip_grp_info a
                 LEFT JOIN som_dip_standard b
                     ON SUBSTR(a.dscg_time,1,4) = b.STANDARD_YEAR
                    AND a.dip_codg = b.dip_codg
        AND ifnull(a.is_used_asst_list,0) = ifnull(b.is_used_asst_list,0)
        AND ifnull(a.asst_list_age_grp,0) = ifnull(b.asst_list_age_grp,0)
        AND ifnull(a.asst_list_tmor_sev_deg,0) = ifnull(b.asst_list_tmor_sev_deg,0)
        AND ifnull(a.asst_list_dise_sev_deg,0) = ifnull(b.asst_list_dise_sev_deg,0)
                    AND a.HOSPITAL_ID = b.HOSPITAL_ID
                 LEFT JOIN som_dip_grp c
                     ON a.dip_codg = c.dip_codg
                 WHERE a.dip_codg IS NOT NULL
                 AND a.HOSPITAL_ID = #{hospitalId}
                   AND SUBSTR(a.dscg_time,1,4) = #{year}
                 GROUP BY b.STANDARD_YEAR,
                          a.dip_codg,
                          b.dip_standard_ipt_days_same_lv,
                          b.last_year_avg_fee
             ) a
        WHERE a.year IS NOT NULL
        GROUP BY a.year,
                 a.unstableDisease
    </select>
</mapper>