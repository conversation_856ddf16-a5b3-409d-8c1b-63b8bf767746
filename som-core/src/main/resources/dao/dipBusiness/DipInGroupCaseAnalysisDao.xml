<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.som.dao.dipBusiness.DipInGroupCaseAnalysisDao">
    <select id="getData" resultType="com.my.som.vo.dipBusiness.DipInGroupAnalysisVo">
        SELECT a.*,
               <!-- 出院科室名称 -->
               b.NAME AS priOutHosDeptName
        FROM
             (
                 SELECT a.dscg_caty_codg_inhosp AS priOutHosDeptCode,a.HOSPITAL_ID,
                        IFNULL(ROUND(NULLIF(SUM(CASE WHEN a.grp_stas = '1' THEN 1 ELSE 0 END),0) /NULLIF(COUNT(1),0) * 100,2),0) AS inGroupRate,
                        IFNULL(COUNT(DISTINCT CASE WHEN a.grp_stas = '1' THEN a.dip_codg ELSE NULL END),0) AS dipNum,
                        IFNULL(COUNT(1),0) AS medicalTotalNum,
                        IFNULL(COUNT(CASE WHEN a.grp_stas = '1' THEN 1 ELSE NULL END),0) AS groupNum,
                        IFNULL(COUNT(CASE WHEN a.grp_stas != '1' THEN 1 ELSE NULL END),0) AS noGroupNum,
                        IFNULL(COUNT(CASE WHEN b.grp_fale_rea LIKE '%未找到手术级别%' THEN '1' ELSE null END),0) AS error1,
                        IFNULL(COUNT(CASE WHEN b.grp_fale_rea LIKE '%未找到主要诊断节代码%' THEN '1' ELSE null END),0) AS error2,
                        IFNULL(COUNT(CASE WHEN b.grp_fale_rea LIKE '%不在支付体系%' THEN '1' ELSE null END),0) AS error3,
                        IFNULL(COUNT(CASE WHEN b.grp_fale_rea LIKE '%诊断编码不是医保版编码%' THEN '1' ELSE null END),0) AS error4,
                        IFNULL(COUNT(CASE WHEN b.grp_fale_rea LIKE '%手术及操作编码%' THEN '1' ELSE null END),0) AS error5,
                        IFNULL(COUNT(CASE WHEN b.grp_fale_rea LIKE '%主要诊断编码为空%' THEN '1' ELSE null END),0) AS error6,
                        IFNULL(COUNT(CASE WHEN b.grp_fale_rea LIKE '%主要诊断编码不参与分组%' THEN '1' ELSE null END),0) AS error7,
                        IFNULL(COUNT(CASE WHEN b.grp_fale_rea LIKE '%主要诊断填写不规范%' THEN '1' ELSE null END),0) AS error8
                 FROM som_dip_grp_info a
                 <if test='enableSeAns != null and enableSeAns != "" and enableSeAns == "1"'>
                     LEFT JOIN som_hi_invy_bas_info q
                     ON q.ID = a.SETTLE_LIST_ID
                     INNER JOIN som_setl_cas_crsp p
                     ON q.k00 = p.k00
                 </if>
                 LEFT JOIN som_dip_grp_rcd b
                     ON a.SETTLE_LIST_ID = b.SETTLE_LIST_ID
                    AND
                        (( a.is_used_asst_list = b.used_asst_list
                               AND a.asst_list_age_grp = b.asst_list_age_grp
                               AND a.asst_list_dise_sev_deg = b.asst_list_dise
                               AND a.asst_list_tmor_sev_deg = b.asst_list_tmor_sev_deg)
                        or ( a.is_used_asst_list  is null and  b.used_asst_list  is null
                                 and  a.asst_list_age_grp  is null and  b.asst_list_age_grp  is null
                                 and  a.asst_list_dise_sev_deg  is null and  b.asst_list_dise  is null
                                 and  a.asst_list_tmor_sev_deg  is null and  b.asst_list_tmor_sev_deg is null)
                            )
                    AND a.HOSPITAL_ID = b.HOSPITAL_ID
                 <where>
                       a.dscg_caty_codg_inhosp IS NOT NULL
                   AND a.dscg_caty_codg_inhosp != ''
                   <include
                           refid="com.my.som.mapper.newBusiness.common.NewBusinessCommonAnalysisMapper.queryBusCommon"></include>
                       <include refid="Common_sql"></include>
                     <if test="dipGroup != null and dipGroup != ''">
                         AND a.dip_codg = #{dipGroup}
                     </if>
                 <if test="inStartTime != null and inStartTime != '' and
                           inEndTime != null and inEndTime != ''">
                     AND a.adm_time BETWEEN #{inStartTime} AND CONCAT(#{inEndTime},' 23:59:59')
                 </if>
                 <if test="(cy_start_date!=null and cy_start_date!='' and cy_end_date!=null and cy_end_date!='') or inHosFlag == 1">
                   AND a.dscg_time BETWEEN #{cy_start_date} AND CONCAT(#{cy_end_date},' 23:59:59')
                 </if>
                 <if test="seStartTime!=null and seStartTime!='' and seEndTime!=null and seEndTime!=''">
                     AND a.setl_end_time BETWEEN #{seStartTime} and CONCAT(#{seEndTime},' 23:59:59')
                 </if>
                 </where>
                 GROUP BY a.dscg_caty_codg_inhosp,a.HOSPITAL_ID
                 ORDER BY inGroupRate DESC
             ) a
        LEFT JOIN som_dept b
            ON a.priOutHosDeptCode = b.CODE
            AND a.HOSPITAL_ID = b.HOSPITAL_ID
        order by a.inGroupRate desc
    </select>

    <select id="getTopCountInfo" resultType="com.my.som.vo.dipBusiness.DipInGroupTopCountVo">
        select a.*,
        ifnull(round(ifnull(ifnull(a.medicalRecordNum-a.lastMonthMedicalRecordNum,0)/nullif(a.lastMonthMedicalRecordNum,0),0) * 100,2),0) as medicalRecordNumRingRatio,
        ifnull(round(ifnull(ifnull(a.medicalRecordNum-lastYearMedicalRecordNum,0)/nullif(a.lastYearMedicalRecordNum,0),0) * 100,2),0) as medicalRecordNumYOY,

        ifnull(round(ifnull(ifnull(a.dipNum-lastMonthDipNum,0)/nullif(a.lastMonthDipNum,0),0) * 100,2),0) as dipNumRingRatio,
        ifnull(round(ifnull(ifnull(a.dipNum-lastYearDipNum,0)/nullif(a.lastYearDipNum,0),0) * 100,2),0) as dipNumYOY,

        ifnull(round(ifnull(ifnull(a.drgInGroupMedcasVal-lastMonthInGroupNum,0)/nullif(a.lastMonthInGroupNum,0),0) * 100,2),0) as inGroupNumRatio,
        ifnull(round(ifnull(ifnull(a.drgInGroupMedcasVal-lastYearInGroupNum,0)/nullif(a.lastYearInGroupNum,0),0) * 100,2),0) as inGroupNumYOY,


        case when a.lastMonthNotInGroupNum = 0 and a.notInGroupNum != 0 then '100' else ifnull(round(ifnull(ifnull(a.notInGroupNum-lastMonthNotInGroupNum,0)/nullif(a.lastMonthNotInGroupNum,0),0) * 100,2),0) end as notInGroupNumRatio,
        case when a.lastYearNotInGroupNum = 0 and a.notInGroupNum != 0 then '100' else ifnull(round(ifnull(ifnull(a.notInGroupNum-lastYearNotInGroupNum,0)/nullif(a.lastYearNotInGroupNum,0),0) * 100,2),0) end as notInGroupNumYOY
--         ifnull(round(ifnull(ifnull(a.notInGroupNum-lastMonthNotInGroupNum,0)/nullif(a.lastMonthNotInGroupNum,0),0) * 100,2),0) as notInGroupNumRatio,
--         ifnull(round(ifnull(ifnull(a.notInGroupNum-lastYearNotInGroupNum,0)/nullif(a.lastYearNotInGroupNum,0),0) * 100,2),0) as notInGroupNumYOY
        from
        (
        select
        <if test="cy_start_date != null and cy_start_date != '' and cy_end_date != null and cy_end_date != ''">
            ifnull(count(case when a.dscg_time between #{cy_start_date} and CONCAT(#{cy_end_date},' 23:59:59') then 1 else null end),0) as medicalRecordNum,
            ifnull(count(case when a.dscg_time between #{lastMonth_cy_start_date} and CONCAT(#{lastMonth_cy_end_date},' 23:59:59') then 1 else null end),0) as lastMonthMedicalRecordNum,
            ifnull(count(case when a.dscg_time between #{lastYear_cy_start_date} and CONCAT(#{lastYear_cy_end_date},' 23:59:59') then 1 else null end),0) as lastYearMedicalRecordNum,

            ifnull(count(distinct(case when a.dscg_time between #{cy_start_date} and CONCAT(#{cy_end_date},' 23:59:59') then b.dip_codg else null end)),0) as dipNum,
            ifnull(count(distinct(case when a.dscg_time between #{lastMonth_cy_start_date} and CONCAT(#{lastMonth_cy_end_date},' 23:59:59') then b.dip_codg else null end)),0) as lastMonthDipNum,
            ifnull(count(distinct(case when a.dscg_time between #{lastYear_cy_start_date} and CONCAT(#{lastYear_cy_end_date},' 23:59:59') then b.dip_codg else null end)),0) as lastYearDipNum,

            ifnull(count(case when a.dscg_time between #{cy_start_date} and CONCAT(#{cy_end_date},' 23:59:59') and b.grp_stas = '1' then 1 else null end),0) as drgInGroupMedcasVal,
            ifnull(count(case when a.dscg_time between #{lastMonth_cy_start_date} and CONCAT(#{lastMonth_cy_end_date},' 23:59:59') and b.grp_stas = '1' then 1 else null end),0) as lastMonthInGroupNum,
            ifnull(count(case when a.dscg_time between #{lastYear_cy_start_date} and CONCAT(#{lastYear_cy_end_date},' 23:59:59') and b.grp_stas = '1' then 1 else null end),0) as lastYearInGroupNum,

            ifnull(count(case when a.dscg_time between #{cy_start_date} and CONCAT(#{cy_end_date},' 23:59:59') and b.grp_stas != '1' then 1 else null end),0) as notInGroupNum,
            ifnull(count(case when a.dscg_time between #{lastMonth_cy_start_date} and CONCAT(#{lastMonth_cy_end_date},' 23:59:59') and b.grp_stas != '1' then 1 else null end),0) as lastMonthNotInGroupNum,
            ifnull(count(case when a.dscg_time between #{lastYear_cy_start_date} and CONCAT(#{lastYear_cy_end_date},' 23:59:59') and b.grp_stas != '1' then 1 else null end),0) as lastYearNotInGroupNum
        </if>

        <if test="inStartTime != null and inStartTime != '' and inEndTime != null and inEndTime != ''">
            ifnull(count(case when a.adm_time between #{inStartTime} and CONCAT(#{inEndTime},' 23:59:59') then 1 else null end),0) as medicalRecordNum,
            ifnull(count(case when a.adm_time between #{lastMonth_cy_start_date} and CONCAT(#{lastMonth_cy_end_date},' 23:59:59') then 1 else null end),0) as lastMonthMedicalRecordNum,
            ifnull(count(case when a.adm_time between #{lastYear_cy_start_date} and CONCAT(#{lastYear_cy_end_date},' 23:59:59') then 1 else null end),0) as lastYearMedicalRecordNum,

            ifnull(count(distinct(case when a.adm_time between #{inStartTime} and CONCAT(#{inEndTime},' 23:59:59') then b.dip_codg else null end)),0) as dipNum,
            ifnull(count(distinct(case when a.adm_time between #{lastMonth_cy_start_date} and CONCAT(#{lastMonth_cy_end_date},' 23:59:59') then b.dip_codg else null end)),0) as lastMonthDipNum,
            ifnull(count(distinct(case when a.adm_time between #{lastYear_cy_start_date} and CONCAT(#{lastYear_cy_end_date},' 23:59:59') then b.dip_codg else null end)),0) as lastYearDipNum,

            ifnull(count(case when a.adm_time between #{inStartTime} and CONCAT(#{inEndTime},' 23:59:59') and b.grp_stas = '1' then 1 else null end),0) as drgInGroupMedcasVal,
            ifnull(count(case when a.adm_time between #{lastMonth_cy_start_date} and CONCAT(#{lastMonth_cy_end_date},' 23:59:59') and b.grp_stas = '1' then 1 else null end),0) as lastMonthInGroupNum,
            ifnull(count(case when a.adm_time between #{lastYear_cy_start_date} and CONCAT(#{lastYear_cy_end_date},' 23:59:59') and b.grp_stas = '1' then 1 else null end),0) as lastYearInGroupNum,

            ifnull(count(case when a.adm_time between #{inStartTime} and CONCAT(#{inEndTime},' 23:59:59') and b.grp_stas != '1' then 1 else null end),0) as notInGroupNum,
            ifnull(count(case when a.adm_time between #{lastMonth_cy_start_date} and CONCAT(#{lastMonth_cy_end_date},' 23:59:59') and b.grp_stas != '1' then 1 else null end),0) as lastMonthNotInGroupNum,
            ifnull(count(case when a.adm_time between #{lastYear_cy_start_date} and CONCAT(#{lastYear_cy_end_date},' 23:59:59') and b.grp_stas != '1' then 1 else null end),0) as lastYearNotInGroupNum
        </if>

        <if test="seStartTime != null and seStartTime != '' and seEndTime != null and seEndTime != ''">
            ifnull(count(case when a.setl_end_time between #{seStartTime} and CONCAT(#{seEndTime},' 23:59:59') then 1 else null end),0) as medicalRecordNum,
            ifnull(count(case when a.setl_end_time between #{lastMonth_cy_start_date} and CONCAT(#{lastMonth_cy_end_date},' 23:59:59') then 1 else null end),0) as lastMonthMedicalRecordNum,
            ifnull(count(case when a.setl_end_time between #{lastYear_cy_start_date} and CONCAT(#{lastYear_cy_end_date},' 23:59:59') then 1 else null end),0) as lastYearMedicalRecordNum,

            ifnull(count(distinct(case when a.setl_end_time between #{seStartTime} and CONCAT(#{seEndTime},' 23:59:59') then b.dip_codg else null end)),0) as dipNum,
            ifnull(count(distinct(case when a.setl_end_time between #{lastMonth_cy_start_date} and CONCAT(#{lastMonth_cy_end_date},' 23:59:59') then b.dip_codg else null end)),0) as lastMonthDipNum,
            ifnull(count(distinct(case when a.setl_end_time between #{lastYear_cy_start_date} and CONCAT(#{lastYear_cy_end_date},' 23:59:59') then b.dip_codg else null end)),0) as lastYearDipNum,

            ifnull(count(case when a.setl_end_time between #{seStartTime} and CONCAT(#{seEndTime},' 23:59:59') and b.grp_stas = '1' then 1 else null end),0) as drgInGroupMedcasVal,
            ifnull(count(case when a.setl_end_time between #{lastMonth_cy_start_date} and CONCAT(#{lastMonth_cy_end_date},' 23:59:59') and b.grp_stas = '1' then 1 else null end),0) as lastMonthInGroupNum,
            ifnull(count(case when a.setl_end_time between #{lastYear_cy_start_date} and CONCAT(#{lastYear_cy_end_date},' 23:59:59') and b.grp_stas = '1' then 1 else null end),0) as lastYearInGroupNum,

            ifnull(count(case when a.setl_end_time between #{seStartTime} and CONCAT(#{seEndTime},' 23:59:59') and b.grp_stas != '1' then 1 else null end),0) as notInGroupNum,
            ifnull(count(case when a.setl_end_time between #{lastMonth_cy_start_date} and CONCAT(#{lastMonth_cy_end_date},' 23:59:59') and b.grp_stas != '1' then 1 else null end),0) as lastMonthNotInGroupNum,
            ifnull(count(case when a.setl_end_time between #{lastYear_cy_start_date} and CONCAT(#{lastYear_cy_end_date},' 23:59:59') and b.grp_stas != '1' then 1 else null end),0) as lastYearNotInGroupNum
        </if>
        from som_dip_grp_info a
        <if test='enableSeAns != null and enableSeAns != "" and enableSeAns == "1"'>
            LEFT JOIN som_hi_invy_bas_info q
            ON q.ID = a.SETTLE_LIST_ID
            INNER JOIN som_setl_cas_crsp p
            ON q.k00 = p.k00
        </if>
        left join som_dip_grp_rcd b
        on a.SETTLE_LIST_ID = b.SETTLE_LIST_ID
        <where>
            <include
                    refid="com.my.som.mapper.newBusiness.common.NewBusinessCommonAnalysisMapper.queryBusCommon"></include>
            <include refid="Common_sql"></include>
        </where>

        ) a
    </select>

    <select id="getNoGroupResonCountInfo" resultType="com.my.som.vo.dipBusiness.DipGroupErrorMsgVo">
<!--        SELECT a.notInGroupName,-->
<!--               a.notInGroupReason,-->
<!--               a.medcasVal,-->
<!--               a.notInGroupRate,-->
<!--               CONCAT(ROUND(IFNULL(IFNULL(a.medcasVal,0)/NULLIF(b.totals,0),0) * 100,2),'%') AS allRate-->
<!--        FROM-->
<!--             (-->
<!--                 SELECT a.notInGroupName,-->
<!--                        a.notInGroupReason,-->
<!--                        a.medcasVal,-->
<!--                        CONCAT(ROUND(IFNULL(IFNULL(a.medcasVal,0)/NULLIF(b.notInGroupTotals,0),0) * 100,2),'%') AS notInGroupRate-->
<!--                 FROM-->
<!--                      (-->
<!--                          SELECT '未入组原因' AS notInGroupName,-->
<!--                                 '未找到手术级别' AS notInGroupReason,-->
<!--                                 COUNT(CASE WHEN b.grp_fale_rea LIKE '%未找到级别%' THEN '1' ELSE null END) AS medcasVal-->
<!--                          FROM som_dip_grp_info a-->
<!--                          LEFT JOIN som_dip_grp_rcd b-->
<!--                              ON a.SETTLE_LIST_ID = b.SETTLE_LIST_ID-->
<!--                             AND a.is_used_asst_list = b.used_asst_list-->
<!--                             AND a.asst_list_age_grp = b.asst_list_age_grp-->
<!--                             AND a.asst_list_dise_sev_deg = b.asst_list_dise-->
<!--                             AND a.asst_list_tmor_sev_deg = b.asst_list_tmor_sev_deg-->
<!--                                     AND a.HOSPITAL_ID = b.HOSPITAL_ID-->
<!--                          <where>-->
<!--                              <if test="hospitalId != null and hospitalId != ''">-->
<!--                                  AND a.HOSPITAL_ID = #{hospitalId}-->
<!--                              </if>-->
<!--                              <include refid="Common_sql"></include>-->
<!--                              <if test="inStartTime != null and inStartTime != '' and inEndTime != null and inEndTime != null">-->
<!--                                  AND a.adm_time BETWEEN #{inStartTime} AND CONCAT(#{inEndTime},' 23:59:59')-->
<!--                              </if>-->
<!--                              &lt;!&ndash; 出院时间范围 &ndash;&gt;-->
<!--                              <if test="(cy_start_date!=null and cy_start_date!='' and cy_end_date!=null and cy_end_date!='') or inHosFlag == 2">-->
<!--                                AND a.dscg_time BETWEEN #{cy_start_date} AND CONCAT(#{cy_end_date},' 23:59:59')-->
<!--                              </if>-->
<!--                              <if test="seStartTime!=null and seStartTime!='' and seEndTime!=null and seEndTime!=''">-->
<!--                                  AND a.setl_end_time BETWEEN #{seStartTime} and CONCAT(#{seEndTime},' 23:59:59')-->
<!--                              </if>-->
<!--                          </where>-->
<!--                          UNION ALL-->
<!--                          SELECT '未入组原因' AS notInGroupName,-->
<!--                                 '未找到主要诊断节代码' AS notInGroupReason,-->
<!--                                 COUNT(CASE WHEN b.grp_fale_rea LIKE '%未找到主要诊断节代码%' THEN '1' ELSE null END) AS medcasVal-->
<!--                          FROM som_dip_grp_info a-->
<!--                          LEFT JOIN som_dip_grp_rcd b-->
<!--                              ON a.SETTLE_LIST_ID = b.SETTLE_LIST_ID-->
<!--                             AND a.is_used_asst_list = b.used_asst_list-->
<!--                             AND a.asst_list_age_grp = b.asst_list_age_grp-->
<!--                             AND a.asst_list_dise_sev_deg = b.asst_list_dise-->
<!--                             AND a.asst_list_tmor_sev_deg = b.asst_list_tmor_sev_deg-->
<!--                                     AND a.HOSPITAL_ID = b.HOSPITAL_ID-->
<!--                          <where>-->
<!--                              <if test="hospitalId != null and hospitalId != ''">-->
<!--                                  AND a.HOSPITAL_ID = #{hospitalId}-->
<!--                              </if>-->
<!--                                <include refid="Common_sql"></include>-->
<!--                              <if test="inStartTime != null and inStartTime != '' and inEndTime != null and inEndTime != null">-->
<!--                                  AND a.adm_time BETWEEN #{inStartTime} AND CONCAT(#{inEndTime},' 23:59:59')-->
<!--                              </if>-->
<!--                              &lt;!&ndash; 出院时间范围 &ndash;&gt;-->
<!--                              <if test="(cy_start_date!=null and cy_start_date!='' and cy_end_date!=null and cy_end_date!='') or inHosFlag == 2">-->
<!--                                  AND a.dscg_time BETWEEN #{cy_start_date} AND CONCAT(#{cy_end_date},' 23:59:59')-->
<!--                              </if>-->
<!--                              <if test="seStartTime!=null and seStartTime!='' and seEndTime!=null and seEndTime!=''">-->
<!--                                  AND a.setl_end_time BETWEEN #{seStartTime} and CONCAT(#{seEndTime},' 23:59:59')-->
<!--                              </if>-->
<!--                          </where>-->
<!--                          UNION ALL-->
<!--                          SELECT '未入组原因' AS notInGroupName,-->
<!--                                 '不在支付体系' AS notInGroupReason,-->
<!--                                 COUNT(CASE WHEN b.grp_fale_rea LIKE '%不在支付体系中%' THEN '1' ELSE null END) AS medcasVal-->
<!--                          FROM som_dip_grp_info a-->
<!--                          LEFT JOIN som_dip_grp_rcd b-->
<!--                              ON a.SETTLE_LIST_ID = b.SETTLE_LIST_ID-->
<!--                             AND a.is_used_asst_list = b.used_asst_list-->
<!--                             AND a.asst_list_age_grp = b.asst_list_age_grp-->
<!--                             AND a.asst_list_dise_sev_deg = b.asst_list_dise-->
<!--                             AND a.asst_list_tmor_sev_deg = b.asst_list_tmor_sev_deg-->
<!--                                AND a.HOSPITAL_ID = b.HOSPITAL_ID-->
<!--                          <where>-->
<!--                              <if test="hospitalId != null and hospitalId != ''">-->
<!--                                  AND a.HOSPITAL_ID = #{hospitalId}-->
<!--                              </if>-->
<!--                                <include refid="Common_sql"></include>-->
<!--                              <if test="inStartTime != null and inStartTime != '' and inEndTime != null and inEndTime != null">-->
<!--                                  AND a.adm_time BETWEEN #{inStartTime} AND CONCAT(#{inEndTime},' 23:59:59')-->
<!--                              </if>-->
<!--                              &lt;!&ndash; 出院时间范围 &ndash;&gt;-->
<!--                              <if test="(cy_start_date!=null and cy_start_date!='' and cy_end_date!=null and cy_end_date!='') or inHosFlag == 2">-->
<!--                                  AND a.dscg_time BETWEEN #{cy_start_date} AND CONCAT(#{cy_end_date},' 23:59:59')-->
<!--                              </if>-->
<!--                              &lt;!&ndash; 结算时间范围 &ndash;&gt;-->
<!--                              <if test="seStartTime!=null and seStartTime!='' and seEndTime!=null and seEndTime!=''">-->
<!--                                  AND a.setl_end_time BETWEEN #{seStartTime} and CONCAT(#{seEndTime},' 23:59:59')-->
<!--                              </if>-->
<!--                          </where>-->
<!--                          UNION ALL-->
<!--                          SELECT '未入组原因' AS notInGroupName,-->
<!--                                 '诊断编码不是医保版编码' AS notInGroupReason,-->
<!--                                 COUNT(CASE WHEN b.grp_fale_rea LIKE '%诊断编码不是医保版编码%' THEN '1' ELSE null END) AS medcasVal-->
<!--                          FROM som_dip_grp_info a-->
<!--                          LEFT JOIN som_dip_grp_rcd b-->
<!--                              ON a.SETTLE_LIST_ID = b.SETTLE_LIST_ID-->
<!--                             AND a.is_used_asst_list = b.used_asst_list-->
<!--                             AND a.asst_list_age_grp = b.asst_list_age_grp-->
<!--                             AND a.asst_list_dise_sev_deg = b.asst_list_dise-->
<!--                             AND a.asst_list_tmor_sev_deg = b.asst_list_tmor_sev_deg-->
<!--                                AND a.HOSPITAL_ID = b.HOSPITAL_ID-->
<!--                          <where>-->
<!--                              <if test="hospitalId != null and hospitalId != ''">-->
<!--                                  AND a.HOSPITAL_ID = #{hospitalId}-->
<!--                              </if>-->
<!--                                <include refid="Common_sql"></include>-->
<!--                              <if test="inStartTime != null and inStartTime != '' and inEndTime != null and inEndTime != null">-->
<!--                                  AND a.adm_time BETWEEN #{inStartTime} AND CONCAT(#{inEndTime},' 23:59:59')-->
<!--                              </if>-->
<!--                              &lt;!&ndash; 出院时间范围 &ndash;&gt;-->
<!--                              <if test="(cy_start_date!=null and cy_start_date!='' and cy_end_date!=null and cy_end_date!='') or inHosFlag == 2">-->
<!--                                  AND a.dscg_time BETWEEN #{cy_start_date} AND CONCAT(#{cy_end_date},' 23:59:59')-->
<!--                              </if>-->
<!--                              &lt;!&ndash; 结算时间范围 &ndash;&gt;-->
<!--                              <if test="seStartTime!=null and seStartTime!='' and seEndTime!=null and seEndTime!=''">-->
<!--                                  AND a.setl_end_time BETWEEN #{seStartTime} and CONCAT(#{seEndTime},' 23:59:59')-->
<!--                              </if>-->
<!--                          </where>-->
<!--                          UNION ALL-->
<!--                          SELECT '未入组原因' AS notInGroupName,-->
<!--                                 '手术及操作编码不是医保版编码' AS notInGroupReason,-->
<!--                                 COUNT(CASE WHEN b.grp_fale_rea LIKE '%手术及操作编码%' THEN '1' ELSE null END) AS medcasVal-->
<!--                          FROM som_dip_grp_info a-->
<!--                          LEFT JOIN som_dip_grp_rcd b-->
<!--                              ON a.SETTLE_LIST_ID = b.SETTLE_LIST_ID-->
<!--                             AND a.is_used_asst_list = b.used_asst_list-->
<!--                             AND a.asst_list_age_grp = b.asst_list_age_grp-->
<!--                             AND a.asst_list_dise_sev_deg = b.asst_list_dise-->
<!--                             AND a.asst_list_tmor_sev_deg = b.asst_list_tmor_sev_deg-->
<!--                                AND a.HOSPITAL_ID = b.HOSPITAL_ID-->
<!--                          <where>-->
<!--                              <if test="hospitalId != null and hospitalId != ''">-->
<!--                                  AND a.HOSPITAL_ID = #{hospitalId}-->
<!--                              </if>-->
<!--                                <include refid="Common_sql"></include>-->
<!--                              <if test="inStartTime != null and inStartTime != '' and inEndTime != null and inEndTime != null">-->
<!--                                  AND a.adm_time BETWEEN #{inStartTime} AND CONCAT(#{inEndTime},' 23:59:59')-->
<!--                              </if>-->
<!--                              &lt;!&ndash; 出院时间范围 &ndash;&gt;-->
<!--                              <if test="(cy_start_date!=null and cy_start_date!='' and cy_end_date!=null and cy_end_date!='') or inHosFlag == 2">-->
<!--                                  AND a.dscg_time BETWEEN #{cy_start_date} AND CONCAT(#{cy_end_date},' 23:59:59')-->
<!--                              </if>-->
<!--                              &lt;!&ndash; 结算时间范围 &ndash;&gt;-->
<!--                              <if test="seStartTime!=null and seStartTime!='' and seEndTime!=null and seEndTime!=''">-->
<!--                                  AND a.setl_end_time BETWEEN #{seStartTime} and CONCAT(#{seEndTime},' 23:59:59')-->
<!--                              </if>-->
<!--                          </where>-->
<!--                          UNION ALL-->
<!--                          SELECT '未入组原因' AS notInGroupName,-->
<!--                                 '未找到主要诊断编码' AS notInGroupReason,-->
<!--                                 COUNT(CASE WHEN b.grp_fale_rea LIKE '%主要诊断编码为空%' THEN '1' ELSE null END) AS medcasVal-->
<!--                          FROM som_dip_grp_info a-->
<!--                          LEFT JOIN som_dip_grp_rcd b-->
<!--                              ON a.SETTLE_LIST_ID = b.SETTLE_LIST_ID-->
<!--                             AND a.is_used_asst_list = b.used_asst_list-->
<!--                             AND a.asst_list_age_grp = b.asst_list_age_grp-->
<!--                             AND a.asst_list_dise_sev_deg = b.asst_list_dise-->
<!--                             AND a.asst_list_tmor_sev_deg = b.asst_list_tmor_sev_deg-->
<!--                                     AND a.HOSPITAL_ID = b.HOSPITAL_ID-->
<!--                          <where>-->
<!--                              <if test="hospitalId != null and hospitalId != ''">-->
<!--                                  AND a.HOSPITAL_ID = #{hospitalId}-->
<!--                              </if>-->
<!--                                <include refid="Common_sql"></include>-->
<!--                              <if test="inStartTime != null and inStartTime != '' and inEndTime != null and inEndTime != null">-->
<!--                                  AND a.adm_time BETWEEN #{inStartTime} AND CONCAT(#{inEndTime},' 23:59:59')-->
<!--                              </if>-->
<!--                              &lt;!&ndash; 出院时间范围 &ndash;&gt;-->
<!--                              <if test="(cy_start_date!=null and cy_start_date!='' and cy_end_date!=null and cy_end_date!='') or inHosFlag == 2">-->
<!--                                  AND a.dscg_time BETWEEN #{cy_start_date} AND CONCAT(#{cy_end_date},' 23:59:59')-->
<!--                              </if>-->
<!--                              &lt;!&ndash; 结算时间范围 &ndash;&gt;-->
<!--                              <if test="seStartTime!=null and seStartTime!='' and seEndTime!=null and seEndTime!=''">-->
<!--                                  AND a.setl_end_time BETWEEN #{seStartTime} and CONCAT(#{seEndTime},' 23:59:59')-->
<!--                              </if>-->
<!--                          </where>-->
<!--                            UNION ALL-->
<!--                            SELECT '未入组原因' AS notInGroupName,-->
<!--                            '主要诊断不参与分组' AS notInGroupReason,-->
<!--                            COUNT(CASE WHEN b.grp_fale_rea LIKE '%不参与分组%' THEN '1' ELSE null END) AS medcasVal-->
<!--                            FROM som_dip_grp_info a-->
<!--                            LEFT JOIN som_dip_grp_rcd b-->
<!--                            ON a.SETTLE_LIST_ID = b.SETTLE_LIST_ID-->
<!--                            AND a.is_used_asst_list = b.used_asst_list-->
<!--                            AND a.asst_list_age_grp = b.asst_list_age_grp-->
<!--                            AND a.asst_list_dise_sev_deg = b.asst_list_dise-->
<!--                            AND a.asst_list_tmor_sev_deg = b.asst_list_tmor_sev_deg-->
<!--                                   AND a.HOSPITAL_ID = b.HOSPITAL_ID-->
<!--                            <where>-->
<!--                                   <if test="hospitalId != null and hospitalId != ''">-->
<!--                                       AND a.HOSPITAL_ID = #{hospitalId}-->
<!--                                   </if>-->
<!--                                <include refid="Common_sql"></include>-->
<!--                                <if test="inStartTime != null and inStartTime != '' and inEndTime != null and inEndTime != null">-->
<!--                                    AND a.adm_time BETWEEN #{inStartTime} AND CONCAT(#{inEndTime},' 23:59:59')-->
<!--                                </if>-->
<!--                                &lt;!&ndash; 出院时间范围 &ndash;&gt;-->
<!--                                <if test="(cy_start_date!=null and cy_start_date!='' and cy_end_date!=null and cy_end_date!='') or inHosFlag == 2">-->
<!--                                    AND a.dscg_time BETWEEN #{cy_start_date} AND CONCAT(#{cy_end_date},' 23:59:59')-->
<!--                                </if>-->
<!--                                &lt;!&ndash; 结算时间范围 &ndash;&gt;-->
<!--                                <if test="seStartTime!=null and seStartTime!='' and seEndTime!=null and seEndTime!=''">-->
<!--                                    AND a.setl_end_time BETWEEN #{seStartTime} and CONCAT(#{seEndTime},' 23:59:59')-->
<!--                                </if>-->
<!--                            </where>-->
<!--                      ) a-->
<!--                 INNER JOIN-->
<!--                          (-->
<!--                              SELECT COUNT(1) AS notInGroupTotals-->
<!--                              FROM som_dip_grp_info a-->
<!--                              LEFT JOIN som_dip_grp_rcd b-->
<!--                                  ON a.SETTLE_LIST_ID = b.SETTLE_LIST_ID-->
<!--                                         AND a.is_used_asst_list = b.used_asst_list-->
<!--                                         AND a.asst_list_age_grp = b.asst_list_age_grp-->
<!--                                         AND a.asst_list_dise_sev_deg = b.asst_list_dise-->
<!--                                         AND a.asst_list_tmor_sev_deg = b.asst_list_tmor_sev_deg-->
<!--                              AND a.HOSPITAL_ID = b.HOSPITAL_ID-->
<!--                              WHERE b.grp_fale_rea IS NOT NULL-->
<!--                                AND a.HOSPITAL_ID = #{hospitalId}-->
<!--                                    <include refid="Common_sql"></include>-->
<!--        <if test="inStartTime != null and inStartTime != '' and inEndTime != null and inEndTime != null">-->
<!--            AND a.adm_time BETWEEN #{inStartTime} AND CONCAT(#{inEndTime},' 23:59:59')-->
<!--        </if>-->
<!--        &lt;!&ndash; 出院时间范围 &ndash;&gt;-->
<!--        <if test="(cy_start_date!=null and cy_start_date!='' and cy_end_date!=null and cy_end_date!='') or inHosFlag == 2">-->
<!--            AND a.dscg_time BETWEEN #{cy_start_date} AND CONCAT(#{cy_end_date},' 23:59:59')-->
<!--        </if>-->
<!--        &lt;!&ndash; 结算时间范围 &ndash;&gt;-->
<!--        <if test="seStartTime!=null and seStartTime!='' and seEndTime!=null and seEndTime!=''">-->
<!--            AND a.setl_end_time BETWEEN #{seStartTime} and CONCAT(#{seEndTime},' 23:59:59')-->
<!--        </if>-->
<!--                          ) b-->
<!--             ) a-->
<!--        INNER JOIN-->
<!--                 (-->
<!--                     SELECT COUNT(1) AS totals-->
<!--                     FROM som_dip_grp_info a-->
<!--                     LEFT JOIN som_dip_grp_rcd b-->
<!--                         ON a.SETTLE_LIST_ID = b.SETTLE_LIST_ID-->
<!--                        AND a.is_used_asst_list = b.used_asst_list-->
<!--                        AND a.asst_list_age_grp = b.asst_list_age_grp-->
<!--                        AND a.asst_list_dise_sev_deg = b.asst_list_dise-->
<!--                        AND a.asst_list_tmor_sev_deg = b.asst_list_tmor_sev_deg-->
<!--                                AND a.HOSPITAL_ID = b.HOSPITAL_ID-->
<!--                     <where>-->
<!--                                <if test="hospitalId != null and hospitalId != ''">-->
<!--                                    AND a.HOSPITAL_ID = #{hospitalId}-->
<!--                                </if>-->
<!--                           <include refid="Common_sql"></include>-->
<!--                         <if test="inStartTime != null and inStartTime != '' and inEndTime != null and inEndTime != null">-->
<!--                             AND a.adm_time BETWEEN #{inStartTime} AND CONCAT(#{inEndTime},' 23:59:59')-->
<!--                         </if>-->
<!--                         &lt;!&ndash; 出院时间范围 &ndash;&gt;-->
<!--                         <if test="(cy_start_date!=null and cy_start_date!='' and cy_end_date!=null and cy_end_date!='') or inHosFlag == 2">-->
<!--                             AND a.dscg_time BETWEEN #{cy_start_date} AND CONCAT(#{cy_end_date},' 23:59:59')-->
<!--                         </if>-->
<!--                         &lt;!&ndash; 结算时间范围 &ndash;&gt;-->
<!--                         <if test="seStartTime!=null and seStartTime!='' and seEndTime!=null and seEndTime!=''">-->
<!--                             AND a.setl_end_time BETWEEN #{seStartTime} and CONCAT(#{seEndTime},' 23:59:59')-->
<!--                         </if>-->
<!--                     </where>-->
<!--                 ) b-->
        SELECT b.*,
               COUNT(1)OVER() AS medcasVal
            FROM(
                    SELECT a.grp_fale_rea AS notInGroupReason,
                           a.grp_stas AS grpStas,
                           COUNT(1)OVER() AS totalNum
                    FROM som_dip_grp_rcd a
                    WHERE SETTLE_LIST_ID IN (
                                SELECT c.settle_list_id
                                FROM som_dip_grp_info c
                                <if test='enableSeAns != null and enableSeAns != "" and enableSeAns == "1"'>
                                    LEFT JOIN som_hi_invy_bas_info d
                                    ON d.ID = c.SETTLE_LIST_ID
                                    INNER JOIN som_setl_cas_crsp p
                                    ON d.k00 = p.k00
                                </if>
                                WHERE 1=1
                                <if test="inStartTime != null and inStartTime != ''
                                                     and inEndTime != null and inEndTime != ''">
                                    AND c.adm_time BETWEEN #{inStartTime} AND CONCAT(#{inEndTime},' 23:59:59')
                                </if>
                                <if test="cy_start_date != null and cy_start_date != ''
                                                     and cy_end_date!=null and cy_end_date!=''">
                                    AND c.dscg_time BETWEEN #{cy_start_date} AND CONCAT(#{cy_end_date},' 23:59:59')
                                </if>
                                <if test="seStartTime != null and seStartTime != ''
                                                     and seEndTime != null and seEndTime != ''">
                                    AND c.setl_end_time BETWEEN #{seStartTime} and CONCAT(#{seEndTime},' 23:59:59')
                                </if>
                                            )
                ) b
            WHERE grpStas = '0'
    </select>

    <sql id="Common_sql">
        <!-- 科室编码 -->
        <if test="deptCode != null and deptCode != ''">
            and a.dscg_caty_codg_inhosp = #{deptCode,jdbcType=VARCHAR}
        </if>
        <if test="b16c != null and b16c != ''">
            and a.dscg_caty_codg_inhosp = #{b16c,jdbcType=VARCHAR}
        </if>
        <if test="searchVal != null and searchVal != ''">
            and (a.dscg_caty_codg_inhosp like concat('%', #{searchVal}, '%')  or a.dscg_caty_name_inhosp like concat('%', #{searchVal}, '%'))
        </if>
        <if test="dateType != null and dateType != ''">
            <choose>
                <when test="dateType == 1">
                    <if test="curYear != null and curYear != ''">
                        and substr(a.dscg_time,1,4) = #{curYear}
                    </if>
                </when>
                <when test="dateType == 2">
                    <if test="curMonth != null and curMonth != ''">
                        and substr(a.dscg_time,1,7) = #{curMonth}
                    </if>
                </when>
            </choose>
        </if>
    </sql>

</mapper>