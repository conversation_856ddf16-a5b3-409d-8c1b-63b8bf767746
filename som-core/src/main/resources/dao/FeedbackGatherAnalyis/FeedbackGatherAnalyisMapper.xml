<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.som.mapper.newBusiness.FeedbackGatherAnalyis.FeedbackGatherAnalyisMapper">

    <select id="queryFeedbackGatherData" resultType="com.my.som.vo.newBusiness.FeedbackGatherAnalyis.FeedbackGatherAnalyisVo">
        SELECT
            <include refid="queryDiff"></include>
            ROUND(sum(IFNULL(a.sumfee,0)),2) as sumfee,
            ROUND(sum(IFNULL(a.dis_gp_sumfee,0)),2) as disGpSumfee,
            ROUND(sum(IFNULL(a.dis_gp_pool_fee,0)),2) as disGpPoolFee,
            ROUND(sum(IFNULL(a.pool_fee,0)),2) as poolFee,
            ROUND(sum(IFNULL(a.dis_gp_pool_fee,0)-IFNULL(a.pool_fee,0)),2) as diff
        from som_fbck_ana a
        <where>
            <if test="seStartTime!=null and seStartTime !='' and seEndTime!=null and seEndTime !=''">
               a.SETL_TIME BETWEEN  #{seStartTime,jdbcType=VARCHAR} and CONCAT(#{seEndTime,jdbcType=VARCHAR},' 23:59:59')
            </if>
            <if test="drName!=null and drName !=''">
                AND a.dr_name LIKE  CONCAT('%',#{drName,jdbcType=VARCHAR},'%')
            </if>
            <if test="disGpName!=null and disGpName !=''">
                AND a.dis_gp_name LIKE  CONCAT('%',#{disGpName,jdbcType=VARCHAR},'%')
            </if>
            <if test="deptName!=null and deptName !=''">
                AND a.DEPT_NAME LIKE  CONCAT('%',#{deptName,jdbcType=VARCHAR},'%')
            </if>
        </where>
        <include refid="queryType"></include>
        order by diff
    </select>
    <sql id="queryDiff">
        <if test="type!=null and type !='' ">
            <if test="type== 'dept'">
                distinct a.DEPT_NAME as deptName,
            </if>
            <if test="type== 'disease'">
                a.dis_gp_codg as disGpCodg,
                a.dis_gp_name as disGpName,
            </if>
            <if test="type== 'doctor'">
                a.dr_name as drName,
            </if>
        </if>
    </sql>
    <sql id="queryType">
        <if test="type!=null and type !='' ">
            <if test="type== 'dept'">
                group by a.DEPT_NAME
            </if>
            <if test="type== 'disease'">
                group by a.dis_gp_codg ,a.dis_gp_name
            </if>
            <if test="type== 'doctor'">
                group by a.dr_name
            </if>
        </if>
    </sql>
</mapper>