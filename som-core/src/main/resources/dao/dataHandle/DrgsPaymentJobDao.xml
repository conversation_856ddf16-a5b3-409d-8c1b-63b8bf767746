<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.som.dao.dataHandle.DrgsPaymentJobDao">
    <select id="getDrgsPaymentMedicalList" parameterType="com.my.som.dto.dataHandle.DataHandleCommonDto" resultType="com.my.som.vo.dataHandle.DrgsPaymentMedicalVo">
        SELECT
            a.SETTLE_LIST_ID as settleListId,
            a.mdtrt_no as mdtrtNo,
            a.PATIENT_ID as patientId,
            a.psn_no as psnNo,
            a.NAME as name,
            a.gend as gend,
            a.AGE as age,
            a.citi_idet_no as citiIdetNo,
            a.HOSPITAL_ID as hospitalId,
            a.medins_name as medinsName,
            a.hosp_lv as hospLv,
            a.adm_time as admTime,
            a.dscg_time as dscgTime,
            a.setl_time as setlTime,
            IFNULL(a.act_ipt,0) as actIpt,
            IFNULL(a.ipt_bedday,0) as iptBedday,
            a.psn_insu_type as psnInsuType,
            a.insu_idet as insuIdet,
            a.med_type as medType,
            a.setl_type as setlType,
            a.adm_diag_codg as admDiagCodg,
            a.adm_diag_name as admDiagName,
            a.main_diag_dise_codg as mainDiagDiseCodg,
            a.main_diag_dise_name as mainDiagDiseName,
            a.dscg_caty_codg_inhosp as dscgCatyCodgInhosp,
            a.dscg_caty_name_inhosp as dscgCatyNameInhosp,
            IFNULL(a.act_pay_amt,0) as actPayAmt,
            IFNULL(a.ipt_sumfee,0) as iptSumfee,
            IFNULL(a.ipt_sumfee_in_selfpay_amt,0) as iptSumfeeInSelfpayAmt,
            IFNULL(a.all_ownpay_amt,0) as allOwnpayAmt,
            IFNULL(a.coup_selfpay_amt,0) as coupSelfpayAmt,
            IFNULL(a.sp_coup_selfpay_amt,0) as spCoupSelfpayAmt,
            IFNULL(a.fit_reim_scp_amt,0) as fitReimScpAmt,
            IFNULL(a.dedc_selfpay_amt,0) as dedcSelfpayAmt,
            IFNULL(a.acct_pay_hi,0) as acctPayHi,
            IFNULL(a.bas_med_pool_pay_prop,0) as basMedPoolPayProp,
            IFNULL(a.bas_med_pool_selfpay_amt,0) as basMedPoolSelfpayAmt,
            IFNULL(a.bas_med_fund_pay,0) as basMedFundPay,
            IFNULL(a.hifob_med_pool_pay_prop,0) as hifobMedPoolPayProp,
            IFNULL(a.hifob_med_pool_selfpay_amt,0) as hifobMedPoolSelfpayAmt,
            IFNULL(a.hifob_med_fund_pay,0) as hifobMedFundPay,
            IFNULL(a.over_selfpay_amt,0) as overSelfpayAmt,
            IFNULL(a.cvlserv_subs_fund_pay,0) as cvlservSubsFundPay,
            IFNULL(a.med_care_psn_subs_fund_pay,0) as medCarePsnSubsFundPay,
            IFNULL(a.retire_subs_fund_pay,0) as retireSubsFundPay,
            IFNULL(a.retr_subs_fund_pay,0) as retrSubsFundPay,
            IFNULL(a.entp_splm_med_fund_pay,0) as entpSplmMedFundPay,
            IFNULL(a.bas_med_fund_major_dise_subs_pay,0) as basMedFundMajorDiseSubsPay,
            IFNULL(a.fili_register_psn_subs_pay,0) as filiRegisterPsnSubsPay,
            IFNULL(a.old_subs_pay,0) as oldSubsPay,
            IFNULL(a.oth_subs_fund_pay,0) as othSubsFundPay,
            IFNULL(a.medins_part_amt,0) as medinsPartAmt,
            a.sin_dise_codg as sinDiseCodg,
            a.out_flag as outFlag,
            a.fili_register_poor_flag as filiRegisterPoorFlag,
            a.day_surgery_flag as daySurgeryFlag,
            a.impe_clnc_path_mgt_flag as impeClncPathMgtFlag,
            a.exe_clnc_path_stas as exeClncPathStas,
            a.medcas_stas as medcasStas,
            a.psn_med_setl_evt_id as psnMedSetlEvtId,
            a.setl_doc_no as setlDocNo,
            a.memo_info as memo_info,
            a.DATA_LOG_ID as dataLogId,
            a.ACTIVE_FLAG as activeFlag,
            b.drg_codg as drgCodg,
            b.DRG_NAME as drgName,
            b.grper_info_id as drgGroupID,
            c.ID as payStdId,
            c.drg_pay_std as drgPayStd,
            c.drg_wt as drgWt,
            c.rate as rate
        FROM som_hi_fee_setl a
        LEFT JOIN som_drg_grp_rcd b on a.SETTLE_LIST_ID = b.SETTLE_LIST_ID
        LEFT JOIN som_drg_grps_hi_kpi_std c on b.drg_codg = c.drg_codg AND a.psn_insu_type=c.psn_insu_type and a.hosp_lv=c.hosp_lv
        and c.data_souc='昆明市医保' and c.ACTIVE_FLAG='1'
        <![CDATA[  AND a.setl_time >= STR_TO_DATE(c.begn_date,"%Y%m%d")
            AND a.setl_time <= STR_TO_DATE(c.expi_date,"%Y%m%d") ]]>
        WHERE 1=1
        <if test="queryParam.logId!=null and queryParam.logId!=''">
            AND a.DATA_LOG_ID = #{queryParam.logId}
        </if>
        <if test="queryParam.activeFlag!=null and queryParam.activeFlag!=''">
            AND a.ACTIVE_FLAG = #{queryParam.activeFlag}
        </if>
        LIMIT #{queryParam.start} , #{queryParam.limit}
    </select>

    <!--批量插入DRGs支付记录-->
    <insert id="batchInsertBusDrgPayment">
        insert into som_drg_pay_detl (SETTLE_LIST_ID, mdtrt_no, PATIENT_ID,
            psn_no, NAME, gend,
            AGE, citi_idet_no, HOSPITAL_ID,
            medins_name, hosp_lv, adm_time,
            dscg_time, setl_time, ipt_sumfee,
            act_ipt, psn_insu_type, drg_codg,
            DRG_NAME, drg_pay_std, drg_pay_amt,
            drg_wt, rate, grper_id,
            pay_std_id, PAY_TYPE, item_pay_type,
            DRG_PAY_TYPE, medins_setl_way, sp_subs_amt,
            init_item_pay_amt, act_pay_amt, over_exp_blnc_amt,
            DATA_LOG_ID, ACTIVE_FLAG)
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.settleListId,jdbcType=BIGINT}, #{item.mdtrtNo,jdbcType=VARCHAR}, #{item.patientId,jdbcType=VARCHAR},
            #{item.psnNo,jdbcType=VARCHAR}, #{item.name,jdbcType=VARCHAR}, #{item.gend,jdbcType=VARCHAR},
            #{item.age,jdbcType=VARCHAR}, #{item.citiIdetNo,jdbcType=VARCHAR}, #{item.hospitalId,jdbcType=VARCHAR},
            #{item.medinsName,jdbcType=VARCHAR}, #{item.hospLv,jdbcType=VARCHAR}, #{item.admTime,jdbcType=VARCHAR},
            #{item.dscgTime,jdbcType=VARCHAR}, #{item.setlTime,jdbcType=TIMESTAMP}, #{item.iptSumfee,jdbcType=DECIMAL},
            #{item.actIpt,jdbcType=VARCHAR}, #{item.psnInsuType,jdbcType=VARCHAR}, #{item.drgCodg,jdbcType=VARCHAR},
            #{item.drgName,jdbcType=VARCHAR}, #{item.drgPayStd,jdbcType=DECIMAL}, #{item.drgPayAmt,jdbcType=DECIMAL},
            #{item.drgWt,jdbcType=DECIMAL}, #{item.rate,jdbcType=DECIMAL}, #{item.grperId,jdbcType=BIGINT},
            #{item.payStdId,jdbcType=BIGINT}, #{item.payType,jdbcType=VARCHAR}, #{item.itemPayType,jdbcType=VARCHAR},
            #{item.drgPayType,jdbcType=VARCHAR}, #{item.medinsSetlWay,jdbcType=VARCHAR}, #{item.spSubsAmt,jdbcType=DECIMAL},
            #{item.initItemPayAmt,jdbcType=DECIMAL}, #{item.actPayAmt,jdbcType=DECIMAL}, #{item.overExpBlncAmt,jdbcType=DECIMAL},
            #{item.dataLogId,jdbcType=BIGINT}, #{item.activeFlag,jdbcType=VARCHAR})
        </foreach>
    </insert>
</mapper>