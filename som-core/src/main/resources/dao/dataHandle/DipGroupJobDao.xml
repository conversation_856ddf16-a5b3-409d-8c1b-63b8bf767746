<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.som.dao.dataHandle.DipGroupJobDao">

    <!--批量插入分组记录-->
    <insert id="batchInsertStsDipGroupRecord">
        INSERT INTO som_dip_grp_rcd (
                SETTLE_LIST_ID, medcas_type, HOSPITAL_ID,
                main_diag_dise_codg, main_diag_dise_name,
                dip_diag_codg_part, dip_diag_name_part, oprn_oprt_code, oprn_oprt_name,
                dip_codg, DIP_NAME, used_asst_list, asst_list_age_grp,
                asst_list_dise, asst_list_tmor_sev_deg, auxiliary_burn,grp_stas,
                grp_fale_rea, DATA_LOG_ID, OPR_DATE, ACTIVE_FLAG,
                grper_oupt_log, grp_err_log, grper_type
        ) VALUES
        <foreach collection="sdgrl" item="item" index="index" separator=",">
            (
            #{item.settleListId,jdbcType=BIGINT}, #{item.medcasType,jdbcType=VARCHAR}, #{item.hospitalId,jdbcType=VARCHAR},
            #{item.mainDiagDiseCodg,jdbcType=VARCHAR}, #{item.mainDiagDiseName,jdbcType=VARCHAR}, #{item.dipDiagCodgPart,jdbcType=VARCHAR},
            #{item.dipDiagNamePart,jdbcType=VARCHAR}, #{item.dipOprtCodg,jdbcType=VARCHAR}, #{item.dipOprtName,jdbcType=VARCHAR},
            #{item.dipCodg,jdbcType=VARCHAR},#{item.dipName,jdbcType=VARCHAR}, #{item.usedAsstList,jdbcType=VARCHAR}, #{item.asstListAgeGrp,jdbcType=VARCHAR},
            #{item.asstListDiseSevDeg,jdbcType=VARCHAR}, #{item.asstListTmorSevDeg,jdbcType=VARCHAR},#{item.auxiliaryBurn,jdbcType=VARCHAR},
            #{item.grpStas,jdbcType=VARCHAR}, #{item.grpFaleRea,jdbcType=VARCHAR},
            #{item.dataLogId,jdbcType=BIGINT}, now(), #{item.activeFlag,jdbcType=VARCHAR},
            #{item.grperOuptLog,jdbcType=LONGVARCHAR}, #{item.grpErrLog,jdbcType=LONGVARCHAR},
            #{item.grper_type,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>

    <!-- 批量插入分组日志 -->
    <insert id="batchInsertStsDipGroupLog">
        INSERT INTO som_dip_grper_intf_trns_log(
            HOSPITAL_ID,grp_para,grper_oupt_log,
            SETTLE_LIST_ID,opter,OPR_DATE,
            ACTIVE_FLAG,grper_info_id
        )
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (
            #{item.hospitalId,jdbcType=VARCHAR}, #{item.grpPara,jdbcType=LONGVARCHAR}, #{item.grperOuptLog,jdbcType=LONGVARCHAR},
            #{item.settleListId,jdbcType=BIGINT}, #{item.opter,jdbcType=VARCHAR}, now(), #{item.activeFlag,jdbcType=VARCHAR},
            #{item.grperInfoId,jdbcType=BIGINT}
            )
        </foreach>
    </insert>

    <!-- 抽取中间表 -->
    <insert id="extractDipData">
        INSERT INTO som_dip_grp_info (
            SETTLE_LIST_ID, medcas_type, HOSPITAL_ID,
            medins_name, setl_begn_time,
            setl_end_time, med_pay_way,
            PATIENT_ID, NAME, gend,
            brdy, AGE, age_insuff_1_age_age,
            nwb_bir_wt, nwb_adm_wt, adm_way,
            adm_time,
            adm_caty_codg_inhosp, adm_caty_name_inhosp,
            dscg_caty_codg_inhosp, dscg_caty_name_inhosp,
            dscg_time, act_ipt,
            dscg_way, ipt_sumfee,
            ipt_sumfee_in_selfpay_amt, com_med_servfee, rhab_fee,
            diag_fee, treat_fee, drugfee,
            blood_blo_pro, mcs_fee, tcm_oth,
            oth_fee, abt_fee, inspect_fee,
            psn_selfpay,psn_ownpay,acct_pay,psn_cashpay,
            dip_codg, DIP_NAME,
            is_used_asst_list, asst_list_age_grp, asst_list_dise_sev_deg, asst_list_tmor_sev_deg,auxiliary_burn,
            STANDARD_YEAR, dip_wt, standard_ave_hosp_day,
            standard_avg_fee, AVG_AGE, avg_drug_fee, avg_mcs_fee,
            avg_abt_fee, inspect_fee_standard_val, main_diag_dise_codg,
            main_diag_dise_name, main_oprn_oprt_codg, main_oprn_oprt_name,
            deptdrt_code, deptdrt_name, chfdr_code,
            chfdr_name, atddr_code,
            atddr_name, ipdr_code, ipdr_name,
            resp_nurs_code, resp_nurs_name, train_dr_code,
            train_dr_name, intn_dr,
            codr_code, codr_name, qltctrl_dr_code,
            qltctrl_dr_name, qltctrl_nurs_code, qltctrl_nurs_name,
            qltctrl_date, grp_stas,
            grper_type, DATA_LOG_ID, pre_hosp_examfee
        )
        SELECT
            <!--基本信息-->
            a.ID AS SETTLE_LIST_ID,
            a.A03 AS medcas_type,
            a.HOSPITAL_ID AS HOSPITAL_ID,
            a.A02 AS medins_name,
            a.D36 AS setl_begn_time,
            a.D37 AS setl_end_time,
            a.A46C AS med_pay_way,
            a.A48 AS PATIENT_ID,
            a.A11 AS NAME,
            a.A12C AS gend,
            a.A13 AS brdy,
            a.A14 AS AGE,
            a.A16 AS age_insuff_1_age_age,
            a.A18 AS nwb_bir_wt,
            a.A17 AS nwb_adm_wt,
            a.B11C AS adm_way,
            a.B12 AS adm_time,
            a.B13C AS adm_caty_codg_inhosp,
            a.B13N AS adm_caty_name_inhosp,
            B16C AS dscg_caty_codg_inhosp,
            B16N AS dscg_caty_name_inhosp,
            a.B15 AS dscg_time,
            a.B20 AS act_ipt,
            a.B34C AS dscg_way,
            <!--费用信息-->
            IFNULL(a.D01,0) AS ipt_sumfee,
                IFNULL(a.D09,0) AS ipt_sumfee_in_selfpay_amt,
            <!--综合医疗服务费：1.一般医疗服务费、2.一般治疗操作费、3.护理费综合医疗服务类、4.其他费用-->
            IFNULL(a.D11,0)+IFNULL(a.D12,0)+IFNULL(a.D13,0)+IFNULL(a.D14,0)  AS com_med_servfee,
            <!--康复费：11.康复费-->
            IFNULL(a.D21,0) AS rhab_fee,
            <!--诊断费（西医病案）：5.病理诊断费、6.实验室诊断费、7.影像学诊断费、8.临床诊断项目费-->
            <!--诊断费（中医病案）：5.病理诊断费、6.实验室诊断费、7.影像学诊断费、8.临床诊断项目费、12.中医类(中医和名族医医疗服务)中医诊断-->
            case when a.A03 = '1' then IFNULL(a.D15,0)+IFNULL(a.D16,0)+IFNULL(a.D17,0)+IFNULL(a.D18,0)
              else IFNULL(a.D15,0)+IFNULL(a.D16,0)+IFNULL(a.D17,0)+IFNULL(a.D18,0)+IFNULL(a.D63,0)
            end AS diag_fee,
            <!--治疗类：9.非手术治疗项目费、10.手术治疗费、13.中医治疗费-->
            IFNULL(a.D19,0)+IFNULL(a.D20,0)+IFNULL(a.d22,0) AS treat_fee,
            <!--药品费：15.西药费、16.中成药费、17.中草药费-->
            IFNULL(a.D23,0)+IFNULL(a.D24,0)+IFNULL(a.D25,0)  AS drugfee,
            <!--血液和血液制品类费：18.血费、19白蛋白类制品费、20球蛋白类制品费、21凝血因子类制品费、22细胞因子类制品费-->
            IFNULL(a.D26,0)+IFNULL(a.D27,0)+IFNULL(a.D28,0)+IFNULL(a.D29,0)+IFNULL(a.D30,0) AS blood_blo_pro,
            <!--耗材费：23.检查用一次性医用材料费、24.治疗用一次性医用材料费、25.手术用一次性医用材料费-->
            IFNULL(a.D31,0)+IFNULL(a.D32,0)+IFNULL(a.D33,0) AS mcs_fee,
            <!--中医其他：(中医病案首页：14.中医其他）-->
            case when a.A03 = '1' then 0 else IFNULL(a.D64,0) end AS tcm_oth,
            <!--其他费：26其他费-->
            IFNULL(a.D34,0) AS oth_fee,
            <!--抗生素费用（另外：抗菌药物费）-->
            IFNULL(a.D23x01,0) AS abt_fee,
            <!--检验费（另外：实验室诊断费+影像学诊断费）-->
            IFNULL(a.D16,0)+IFNULL(a.D17,0) AS inspect_fee,
            <!--结算清单个人自付部分金额-->
            IFNULL(a.D54,0) AS psn_selfpay,
            IFNULL(a.D55,0) AS psn_ownpay,
            IFNULL(a.D56,0) AS acct_pay,
            IFNULL(a.D57,0) AS psn_cashpay,
            <!--分组信息-->
            e.dip_codg AS dip_codg,
            e.DIP_NAME AS dip_name,
            <!-- 辅助目录 -->
            d.used_asst_list AS is_used_asst_list,
            d.asst_list_age_grp,
            d.asst_list_dise,
            d.asst_list_tmor_sev_deg,
            d.auxiliary_burn,
            <!--  标杆信息-->
            e.STANDARD_YEAR AS STANDARD_YEAR,
            e.dip_wt as dip_wt,
            convert(AES_DECRYPT(UNHEX(e.dip_standard_ipt_days),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) using utf8) as standard_ave_hosp_day,
            convert(AES_DECRYPT(UNHEX(e.dip_standard_inpf),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) using utf8) as standard_avg_fee,
            null as AVG_AGE,
            null as avg_drug_fee,
            null as avg_mcs_fee,
            null as avg_abt_fee,
            null as inspect_fee_standard_val,
            <!--诊疗信息-->
            a.C03C AS main_diag_dise_codg,
            a.C04N AS main_diag_dise_name,
            a.C14x01C AS main_oprn_oprt_codg,
            a.C15x01N AS main_oprn_oprt_name,
            a.B22C AS deptdrt_code,
            a.B22N AS deptdrt_name,
            a.B23C AS chfdr_code,
            a.B23N AS chfdr_name,
            a.B24C AS atddr_code,
            a.B24N AS atddr_name,
            a.B25C AS ipdr_code,
            a.B25N AS ipdr_name,
            a.B26C AS resp_nurs_code,
            a.B26N AS resp_nurs_name,
            a.B27C AS train_dr_code,
            a.B27N AS train_dr_name,
            a.B28 AS intn_dr,
            a.B29C AS codr_code,
            a.B29N codr_name,
            a.B31C AS qltctrl_dr_code,
            a.B31N AS qltctrl_dr_name,
            a.B32C AS qltctrl_nurs_code,
            a.B32N AS qltctrl_nurs_name,
            a.B33 AS qltctrl_date,
        CASE
        WHEN TRIM(e.refer_sco) != '' AND e.refer_sco IS NOT NULL THEN 1
        ELSE 0
        END AS grp_stas,
        <!--数据处理信息-->
        d.grper_type AS grper_type,
            b.ID AS DATA_LOG_ID,
            IFNULL(a.D02,0) AS pre_hosp_examfee
          FROM som_hi_invy_bas_info a
          INNER JOIN som_datapros_log b ON a.DATA_LOG_ID = b.ID
          INNER JOIN som_dip_grp_rcd d ON a.ID = d.SETTLE_LIST_ID
          LEFT JOIN som_dip_standard e ON d.dip_codg = e.dip_codg AND SUBSTR(IFNULL(a.d37,a.b15),1,4)= e.STANDARD_YEAR
          and a.HOSPITAL_ID = e.HOSPITAL_ID
        AND d.asst_list_age_grp = e.asst_list_age_grp
        AND d.asst_list_dise = e.asst_list_dise_sev_deg
        AND d.asst_list_tmor_sev_deg = e.asst_list_tmor_sev_deg
        AND d.auxiliary_burn = e.auxiliary_burn
          WHERE a.ACTIVE_FLAG = '1'


          <if test="queryParam.logId != null and queryParam.logId != ''">
            AND a.DATA_LOG_ID = #{queryParam.logId}
          </if>
    </insert>

    <insert id="batchInsertStsDipGroupRecord2">
        INSERT INTO som_dip_grp_rcd (
            SETTLE_LIST_ID, medcas_type, HOSPITAL_ID,
            main_diag_dise_codg, main_diag_dise_name,
            dip_diag_codg_part, dip_diag_name_part, oprn_oprt_code, oprn_oprt_name,
            dip_codg, DIP_NAME, used_asst_list, asst_list_age_grp,
            asst_list_dise, asst_list_tmor_sev_deg, auxiliary_burn, grp_stas,
            grp_fale_rea, DATA_LOG_ID, OPR_DATE, ACTIVE_FLAG,
            grper_oupt_log, grp_err_log, grper_type
        ) VALUES
        (
            #{settleListId,jdbcType=BIGINT}, #{medcasType,jdbcType=VARCHAR}, #{hospitalId,jdbcType=VARCHAR},
            #{mainDiagDiseCodg,jdbcType=VARCHAR}, #{mainDiagDiseName,jdbcType=VARCHAR}, #{dipDiagCodgPart,jdbcType=VARCHAR},
            #{dipDiagNamePart,jdbcType=VARCHAR}, #{dipOprtCodg,jdbcType=VARCHAR}, #{dipOprtName,jdbcType=VARCHAR},
            #{dipCodg,jdbcType=VARCHAR},#{dipName,jdbcType=VARCHAR}, #{usedAsstList,jdbcType=VARCHAR}, #{asstListAgeGrp,jdbcType=VARCHAR},
            #{asstListDiseSevDeg,jdbcType=VARCHAR}, #{asstListTmorSevDeg,jdbcType=VARCHAR},#{auxiliaryBurn,jdbcType=VARCHAR},
            #{grpStas,jdbcType=VARCHAR}, #{grpFaleRea,jdbcType=VARCHAR},
            #{dataLogId,jdbcType=BIGINT}, now(), #{activeFlag,jdbcType=VARCHAR},
            #{grperOuptLog,jdbcType=LONGVARCHAR}, #{grpErrLog,jdbcType=LONGVARCHAR},
            #{grper_type,jdbcType=VARCHAR}
        )
    </insert>

    <insert id="batchInsertStsDipGroupLog2">
        INSERT INTO som_dip_grper_intf_trns_log(
            HOSPITAL_ID,grp_para,grper_oupt_log,
            SETTLE_LIST_ID,opter,OPR_DATE,
            ACTIVE_FLAG,grper_info_id
        )
        VALUES
        (
            #{hospitalId,jdbcType=VARCHAR}, #{grpPara,jdbcType=LONGVARCHAR}, #{grperOuptLog,jdbcType=LONGVARCHAR},
            #{settleListId,jdbcType=BIGINT}, #{opter,jdbcType=VARCHAR}, now(), #{activeFlag,jdbcType=VARCHAR},
            #{grperInfoId,jdbcType=BIGINT}
        )
    </insert>

    <!-- 清洗未通过清洗的病案 -->
    <select id="queryNotPassCleanDataStsDipGroupRecordList"
            resultType="com.my.som.model.dipBusiness.SomDipGrpRcd">
        SELECT
            a.id as settleListId,
            a.a03 as medcasType,
            a.hospital_id as hospitalId,
            '校验未通过' as grpFaleRea,
            a.active_flag as activeFlag
        from som_hi_invy_bas_info a
        WHERE a.id in
        (
            <include refid="com.my.som.dao.dataHandle.DrgGroupJobDao.settleListMedicalPageChoose"/>
        )
        <if test="queryParam.logId!=null and queryParam.logId!=''">
            AND a.DATA_LOG_ID = #{queryParam.logId}
        </if>
        <if test="queryParam.activeFlag!=null and queryParam.activeFlag!=''">
            AND a.ACTIVE_FLAG = #{queryParam.activeFlag}
        </if>
    </select>

</mapper>
