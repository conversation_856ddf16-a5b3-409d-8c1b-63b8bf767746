<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.som.dao.dataHandle.StsValidateLoseScoreDao">
    <!--批量新增回写主键支持-->
    <insert id="savePreValidateLoseScoreBatch">
        INSERT INTO som_setl_invy_qlt_dedu_point_detl (
           SETTLE_LIST_ID,HOSPITAL_ID,refer_sco,
           nwb_adm_wt_dedu_point, nwb_bir_wt_dedu_point, medcas_no_dedu_point,
           gend_dedu_point, brdy_dedu_point, age_dedu_point,
           med_pay_way_dedu_point, oth_patn_bas_info_dedu_point, dscg_way_dedu_point,
           adm_time_dedu_point, dscg_time_dedu_point, act_ipt_days_dedu_point,
           dscg_caty_dedu_point, is_31_day_in_ipt_plan_dedu_point, adm_way_dedu_point,
           adm_caty_dedu_point, refldept_caty_dedu_point, dscg_main_diag_dedu_point,
           main_diag_codg_dedu_point, oth_diag_dedu_point, oth_diag_codg_dedu_point,
           main_oprn_oprt_name_dedu_point, main_oprn_oprt_codg_dedu_point,
           adm_cond_dedu_point, palg_diag_dedu_point, palg_diag_codg_dedu_point,
           incs_heal_lv_dedu_point, brn_damg_patn_coma_time_dedu_point, oth_oprn_oprt_name_dedu_point,
           oth_oprn_oprt_codg_dedu_point, oprn_oprt_date_dedu_point, otp_diag_dedu_point,
           otp_diag_dise_codg_dedu_point, anst_way_dedu_point, oth_trt_info_dedu_point,
           sumfee_dedu_point, oth_ast_info_dedu_point, DATA_LOG_ID,
           OPR_DATE, dedu_point_rea
        ) VALUES
        <foreach collection="svlss" item="item" index="index" separator=",">
         (#{item.settleListId,jdbcType=VARCHAR},#{item.hospitalId,jdbcType=VARCHAR}, #{item.refer_sco,jdbcType=DECIMAL},
          #{item.nwbAdmWtDeduPoint,jdbcType=DECIMAL}, #{item.nwbBirWtDeduPoint,jdbcType=DECIMAL}, #{item.medcasNoDeduPoint,jdbcType=DECIMAL},
          #{item.gendDeduPoint,jdbcType=DECIMAL}, #{item.brdyDeduPoint,jdbcType=DECIMAL}, #{item.ageDeduPoint,jdbcType=DECIMAL},
          #{item.medPayWayDeduPoint,jdbcType=DECIMAL}, #{item.othPatnBasInfoDeduPoint,jdbcType=DECIMAL}, #{item.dscgWayDeduPoint,jdbcType=DECIMAL},
          #{item.admTimeDeduPoint,jdbcType=DECIMAL}, #{item.dscgTimeDeduPoint,jdbcType=DECIMAL}, #{item.actIptDaysDeduPoint,jdbcType=DECIMAL},
          #{item.dscgCatyDeduPoint,jdbcType=DECIMAL}, #{item.is31DayInIptPlanDeduPoint,jdbcType=DECIMAL}, #{item.admWayDeduPoint,jdbcType=DECIMAL},
          #{item.admCatyDeduPoint,jdbcType=DECIMAL}, #{item.refldeptCatyDeduPoint,jdbcType=DECIMAL}, #{item.dscgMainDiagDeduPoint,jdbcType=DECIMAL},
          #{item.mainDiagCodgDeduPoint,jdbcType=DECIMAL}, #{item.othDiagDeduPoint,jdbcType=DECIMAL}, #{item.othDiagCodgDeduPoint,jdbcType=DECIMAL},
          #{item.mainOprnOprtNameDeduPoint,jdbcType=DECIMAL}, #{item.mainOprnOprtCodgDeduPoint,jdbcType=DECIMAL},
          #{item.admCondDeduPoint,jdbcType=DECIMAL}, #{item.palgDiagDeduPoint,jdbcType=DECIMAL}, #{item.palgDiagCodgDeduPoint,jdbcType=DECIMAL},
          #{item.incsHealLvDeduPoint,jdbcType=DECIMAL}, #{item.brnDamgPatnComaTimeDeduPoint,jdbcType=DECIMAL}, #{item.othOprnOprtNameDeduPoint,jdbcType=DECIMAL},
          #{item.othOprnOprtCodgDeduPoint,jdbcType=DECIMAL}, #{item.oprnOprtDateDeduPoint,jdbcType=DECIMAL}, #{item.otpDiagDeduPoint,jdbcType=DECIMAL},
          #{item.otpDiagDiseCodgDeduPoint,jdbcType=DECIMAL}, #{item.anstWayDeduPoint,jdbcType=DECIMAL}, #{item.othTrtInfoDeduPoint,jdbcType=DECIMAL},
          #{item.sumfeeDeduPoint,jdbcType=DECIMAL}, #{item.othAstInfoDeduPoint,jdbcType=DECIMAL}, #{item.dataLogId,jdbcType=VARCHAR},
          #{item.oprDate,jdbcType=TIMESTAMP}, #{item.deduPointRea,jdbcType=LONGVARCHAR})
      </foreach>
    </insert>

    <insert id="savePreValidateLoseScoreBatch2">
        INSERT INTO som_setl_invy_qlt_dedu_point_detl (
           SETTLE_LIST_ID,HOSPITAL_ID,refer_sco,
           nwb_adm_wt_dedu_point, nwb_bir_wt_dedu_point, medcas_no_dedu_point,
           gend_dedu_point, brdy_dedu_point, age_dedu_point,
           med_pay_way_dedu_point, oth_patn_bas_info_dedu_point, dscg_way_dedu_point,
           adm_time_dedu_point, dscg_time_dedu_point, act_ipt_days_dedu_point,
           dscg_caty_dedu_point, is_31_day_in_ipt_plan_dedu_point, adm_way_dedu_point,
           adm_caty_dedu_point, refldept_caty_dedu_point, dscg_main_diag_dedu_point,
           main_diag_codg_dedu_point, oth_diag_dedu_point, oth_diag_codg_dedu_point,
           main_oprn_oprt_name_dedu_point, main_oprn_oprt_codg_dedu_point,
           adm_cond_dedu_point, palg_diag_dedu_point, palg_diag_codg_dedu_point,
           incs_heal_lv_dedu_point, brn_damg_patn_coma_time_dedu_point, oth_oprn_oprt_name_dedu_point,
           oth_oprn_oprt_codg_dedu_point, oprn_oprt_date_dedu_point, otp_diag_dedu_point,
           otp_diag_dise_codg_dedu_point, anst_way_dedu_point, oth_trt_info_dedu_point,
           sumfee_dedu_point, oth_ast_info_dedu_point, DATA_LOG_ID,
           OPR_DATE, dedu_point_rea
        ) VALUES
         (#{settleListId,jdbcType=VARCHAR},#{hospitalId,jdbcType=VARCHAR}, #{refer_sco,jdbcType=DECIMAL},
          #{nwbAdmWtDeduPoint,jdbcType=DECIMAL}, #{nwbBirWtDeduPoint,jdbcType=DECIMAL}, #{medcasNoDeduPoint,jdbcType=DECIMAL},
          #{gendDeduPoint,jdbcType=DECIMAL}, #{brdyDeduPoint,jdbcType=DECIMAL}, #{ageDeduPoint,jdbcType=DECIMAL},
          #{medPayWayDeduPoint,jdbcType=DECIMAL}, #{othPatnBasInfoDeduPoint,jdbcType=DECIMAL}, #{dscgWayDeduPoint,jdbcType=DECIMAL},
          #{admTimeDeduPoint,jdbcType=DECIMAL}, #{dscgTimeDeduPoint,jdbcType=DECIMAL}, #{actIptDaysDeduPoint,jdbcType=DECIMAL},
          #{dscgCatyDeduPoint,jdbcType=DECIMAL}, #{is31DayInIptPlanDeduPoint,jdbcType=DECIMAL}, #{admWayDeduPoint,jdbcType=DECIMAL},
          #{admCatyDeduPoint,jdbcType=DECIMAL}, #{refldeptCatyDeduPoint,jdbcType=DECIMAL}, #{dscgMainDiagDeduPoint,jdbcType=DECIMAL},
          #{mainDiagCodgDeduPoint,jdbcType=DECIMAL}, #{othDiagDeduPoint,jdbcType=DECIMAL}, #{othDiagCodgDeduPoint,jdbcType=DECIMAL},
          #{mainOprnOprtNameDeduPoint,jdbcType=DECIMAL}, #{mainOprnOprtCodgDeduPoint,jdbcType=DECIMAL},
          #{admCondDeduPoint,jdbcType=DECIMAL}, #{palgDiagDeduPoint,jdbcType=DECIMAL}, #{palgDiagCodgDeduPoint,jdbcType=DECIMAL},
          #{incsHealLvDeduPoint,jdbcType=DECIMAL}, #{brnDamgPatnComaTimeDeduPoint,jdbcType=DECIMAL}, #{othOprnOprtNameDeduPoint,jdbcType=DECIMAL},
          #{othOprnOprtCodgDeduPoint,jdbcType=DECIMAL}, #{oprnOprtDateDeduPoint,jdbcType=DECIMAL}, #{otpDiagDeduPoint,jdbcType=DECIMAL},
          #{otpDiagDiseCodgDeduPoint,jdbcType=DECIMAL}, #{anstWayDeduPoint,jdbcType=DECIMAL}, #{othTrtInfoDeduPoint,jdbcType=DECIMAL},
          #{sumfeeDeduPoint,jdbcType=DECIMAL}, #{othAstInfoDeduPoint,jdbcType=DECIMAL}, #{dataLogId,jdbcType=VARCHAR},
          #{oprDate,jdbcType=TIMESTAMP}, #{deduPointRea,jdbcType=LONGVARCHAR})
    </insert>
</mapper>