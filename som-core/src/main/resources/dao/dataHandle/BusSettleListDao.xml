<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.som.dao.dataHandle.BusSettleListDao">
    <resultMap id="BaseResultMap" type="java.util.Map">
        <id column="ID" jdbcType="BIGINT" property="id" />
        <result column="K00" jdbcType="VARCHAR" property="k00" />
        <result column="A01" jdbcType="VARCHAR" property="a01" />
        <result column="A02" jdbcType="VARCHAR" property="a02" />
        <result column="A03" jdbcType="VARCHAR" property="a03" />
        <result column="A11" jdbcType="VARCHAR" property="a11" />
        <result column="A12C" jdbcType="VARCHAR" property="a12c" />
        <result column="A13" jdbcType="VARCHAR" property="a13" />
        <result column="A14" jdbcType="INTEGER" property="a14" />
        <result column="A15C" jdbcType="VARCHAR" property="a15c" />
        <result column="A16" jdbcType="INTEGER" property="a16" />
        <result column="A17" jdbcType="DOUBLE" property="a17" />
        <result column="A18" jdbcType="DOUBLE" property="a18" />
        <result column="A19C" jdbcType="VARCHAR" property="a19c" />
        <result column="A20" jdbcType="VARCHAR" property="a20" />
        <result column="A21C" jdbcType="VARCHAR" property="a21c" />
        <result column="A22" jdbcType="VARCHAR" property="a22" />
        <result column="A23C" jdbcType="VARCHAR" property="a23c" />
        <result column="A24" jdbcType="VARCHAR" property="a24" />
        <result column="A25C" jdbcType="VARCHAR" property="a25c" />
        <result column="A26" jdbcType="VARCHAR" property="a26" />
        <result column="A27" jdbcType="VARCHAR" property="a27" />
        <result column="A28C" jdbcType="VARCHAR" property="a28c" />
        <result column="A29N" jdbcType="VARCHAR" property="a29n" />
        <result column="A29" jdbcType="VARCHAR" property="a29" />
        <result column="A30" jdbcType="VARCHAR" property="a30" />
        <result column="A31C" jdbcType="VARCHAR" property="a31c" />
        <result column="A32" jdbcType="VARCHAR" property="a32" />
        <result column="A33C" jdbcType="VARCHAR" property="a33c" />
        <result column="A34" jdbcType="VARCHAR" property="a34" />
        <result column="A35" jdbcType="VARCHAR" property="a35" />
        <result column="A38C" jdbcType="VARCHAR" property="a38c" />
        <result column="A46C" jdbcType="VARCHAR" property="a46c" />
        <result column="A47" jdbcType="VARCHAR" property="a47" />
        <result column="A48" jdbcType="VARCHAR" property="a48" />
        <result column="A49" jdbcType="VARCHAR" property="a49" />
        <result column="A50" jdbcType="VARCHAR" property="a50" />
        <result column="A51" jdbcType="VARCHAR" property="a51" />
        <result column="A52" jdbcType="VARCHAR" property="a52" />
        <result column="A53" jdbcType="VARCHAR" property="a53" />
        <result column="A54" jdbcType="VARCHAR" property="a54" />
        <result column="A55" jdbcType="VARCHAR" property="a55" />
        <result column="A56" jdbcType="VARCHAR" property="a56" />
        <result column="A57" jdbcType="VARCHAR" property="a57" />
        <result column="A58" jdbcType="VARCHAR" property="a58" />
        <result column="B11C" jdbcType="VARCHAR" property="b11c" />
        <result column="B12" jdbcType="VARCHAR" property="b12" />
        <result column="B12S" jdbcType="DECIMAL" property="b12s" />
        <result column="B13C" jdbcType="VARCHAR" property="b13c" />
        <result column="B13N" jdbcType="VARCHAR" property="b13n" />
        <result column="B14C" jdbcType="VARCHAR" property="b14c" />
        <result column="B14N" jdbcType="VARCHAR" property="b14n" />
        <result column="B15" jdbcType="VARCHAR" property="b15" />
        <result column="B15S" jdbcType="DECIMAL" property="b15s" />
        <result column="B16C" jdbcType="VARCHAR" property="b16c" />
        <result column="B16N" jdbcType="VARCHAR" property="b16n" />
        <result column="B17C" jdbcType="VARCHAR" property="b17c" />
        <result column="B17N" jdbcType="VARCHAR" property="b17n" />
        <result column="B20" jdbcType="VARCHAR" property="b20" />
        <result column="B21C" jdbcType="VARCHAR" property="b21c" />
        <result column="B22C" jdbcType="VARCHAR" property="b22c" />
        <result column="B22N" jdbcType="VARCHAR" property="b22n" />
        <result column="B23C" jdbcType="VARCHAR" property="b23c" />
        <result column="B23N" jdbcType="VARCHAR" property="b23n" />
        <result column="B24C" jdbcType="VARCHAR" property="b24c" />
        <result column="B24N" jdbcType="VARCHAR" property="b24n" />
        <result column="B25C" jdbcType="VARCHAR" property="b25c" />
        <result column="B25N" jdbcType="VARCHAR" property="b25n" />
        <result column="B26C" jdbcType="VARCHAR" property="b26c" />
        <result column="B26N" jdbcType="VARCHAR" property="b26n" />
        <result column="B27C" jdbcType="VARCHAR" property="b27c" />
        <result column="B27N" jdbcType="VARCHAR" property="b27n" />
        <result column="B28" jdbcType="VARCHAR" property="b28" />
        <result column="B29C" jdbcType="VARCHAR" property="b29c" />
        <result column="B29N" jdbcType="VARCHAR" property="b29n" />
        <result column="B30C" jdbcType="VARCHAR" property="b30c" />
        <result column="B31C" jdbcType="VARCHAR" property="b31c" />
        <result column="B31N" jdbcType="VARCHAR" property="b31n" />
        <result column="B32C" jdbcType="VARCHAR" property="b32c" />
        <result column="B32N" jdbcType="VARCHAR" property="b32n" />
        <result column="B33" jdbcType="VARCHAR" property="b33" />
        <result column="B34C" jdbcType="VARCHAR" property="b34c" />
        <result column="B36C" jdbcType="VARCHAR" property="b36c" />
        <result column="B37" jdbcType="VARCHAR" property="b37" />
        <result column="B38" jdbcType="VARCHAR" property="b38" />
        <result column="B39" jdbcType="VARCHAR" property="b39" />
        <result column="B44" jdbcType="INTEGER" property="b44" />
        <result column="B45" jdbcType="INTEGER" property="b45" />
        <result column="B46" jdbcType="INTEGER" property="b46" />
        <result column="B47" jdbcType="INTEGER" property="b47" />
        <result column="B48" jdbcType="VARCHAR" property="b48" />
        <result column="B49" jdbcType="VARCHAR" property="b49" />
        <result column="B51C" jdbcType="VARCHAR" property="b51c" />
        <result column="B52N" jdbcType="VARCHAR" property="b52n" />
        <result column="C01C" jdbcType="VARCHAR" property="c01c" />
        <result column="C02N" jdbcType="VARCHAR" property="c02n" />
        <result column="C03C" jdbcType="VARCHAR" property="c03c" />
        <result column="C04N" jdbcType="VARCHAR" property="c04n" />
        <result column="C05C" jdbcType="VARCHAR" property="c05c" />
        <result column="C09C" jdbcType="VARCHAR" property="c09c" />
        <result column="C10N" jdbcType="VARCHAR" property="c10n" />
        <result column="C11" jdbcType="VARCHAR" property="c11" />
        <result column="C12C" jdbcType="VARCHAR" property="c12c" />
        <result column="C13N" jdbcType="VARCHAR" property="c13n" />
        <result column="C14x01C" jdbcType="VARCHAR" property="c14x01c" />
        <result column="C15x01N" jdbcType="VARCHAR" property="c15x01n" />
        <result column="C16x01" jdbcType="VARCHAR" property="c16x01" />
        <result column="C17x01" jdbcType="VARCHAR" property="c17x01" />
        <result column="C18x01" jdbcType="VARCHAR" property="c18x01" />
        <result column="C19x01" jdbcType="VARCHAR" property="c19x01" />
        <result column="C20x01" jdbcType="VARCHAR" property="c20x01" />
        <result column="C21x01C" jdbcType="VARCHAR" property="c21x01c" />
        <result column="C22x01C" jdbcType="VARCHAR" property="c22x01c" />
        <result column="C23x01" jdbcType="VARCHAR" property="c23x01" />
        <result column="C24C" jdbcType="VARCHAR" property="c24c" />
        <result column="C25" jdbcType="VARCHAR" property="c25" />
        <result column="C26C" jdbcType="VARCHAR" property="c26c" />
        <result column="C27C" jdbcType="VARCHAR" property="c27c" />
        <result column="C28" jdbcType="INTEGER" property="c28" />
        <result column="C29" jdbcType="INTEGER" property="c29" />
        <result column="C30" jdbcType="INTEGER" property="c30" />
        <result column="C31" jdbcType="INTEGER" property="c31" />
        <result column="C32" jdbcType="INTEGER" property="c32" />
        <result column="C33" jdbcType="INTEGER" property="c33" />
        <result column="C34C" jdbcType="VARCHAR" property="c34c" />
        <result column="C35C" jdbcType="VARCHAR" property="c35c" />
        <result column="C36N" jdbcType="VARCHAR" property="c36n" />
        <result column="C37C" jdbcType="VARCHAR" property="c37c" />
        <result column="C38N" jdbcType="VARCHAR" property="c38n" />
        <result column="C39C" jdbcType="VARCHAR" property="c39c" />
        <result column="C42" jdbcType="DECIMAL" property="c42" />
        <result column="C43" jdbcType="DECIMAL" property="c43" />
        <result column="C44" jdbcType="DECIMAL" property="c44" />
        <result column="C45" jdbcType="VARCHAR" property="c45" />
        <result column="C46" jdbcType="DOUBLE" property="c46" />
        <result column="C47" jdbcType="VARCHAR" property="c47" />
        <result column="C48C" jdbcType="VARCHAR" property="c48c" />
        <result column="C49C" jdbcType="VARCHAR" property="c49c" />
        <result column="D01" jdbcType="DOUBLE" property="d01" />
        <result column="D09" jdbcType="DOUBLE" property="d09" />
        <result column="D11" jdbcType="DOUBLE" property="d11" />
        <result column="D12" jdbcType="DOUBLE" property="d12" />
        <result column="D13" jdbcType="DOUBLE" property="d13" />
        <result column="D14" jdbcType="DOUBLE" property="d14" />
        <result column="D15" jdbcType="DOUBLE" property="d15" />
        <result column="D16" jdbcType="DOUBLE" property="d16" />
        <result column="D17" jdbcType="DOUBLE" property="d17" />
        <result column="D18" jdbcType="DOUBLE" property="d18" />
        <result column="D19" jdbcType="DOUBLE" property="d19" />
        <result column="D19x01" jdbcType="DOUBLE" property="d19x01" />
        <result column="D20" jdbcType="DOUBLE" property="d20" />
        <result column="D20x01" jdbcType="DOUBLE" property="d20x01" />
        <result column="D20x02" jdbcType="DOUBLE" property="d20x02" />
        <result column="D21" jdbcType="DOUBLE" property="d21" />
        <result column="D22" jdbcType="DOUBLE" property="d22" />
        <result column="D23" jdbcType="DOUBLE" property="d23" />
        <result column="D23x01" jdbcType="DOUBLE" property="d23x01" />
        <result column="D24" jdbcType="DOUBLE" property="d24" />
        <result column="D25" jdbcType="DOUBLE" property="d25" />
        <result column="D26" jdbcType="DOUBLE" property="d26" />
        <result column="D27" jdbcType="DOUBLE" property="d27" />
        <result column="D28" jdbcType="DOUBLE" property="d28" />
        <result column="D29" jdbcType="DOUBLE" property="d29" />
        <result column="D30" jdbcType="DOUBLE" property="d30" />
        <result column="D31" jdbcType="DOUBLE" property="d31" />
        <result column="D32" jdbcType="DOUBLE" property="d32" />
        <result column="D33" jdbcType="DOUBLE" property="d33" />
        <result column="D34" jdbcType="DOUBLE" property="d34" />
        <result column="D35" jdbcType="VARCHAR" property="d35" />
        <result column="D36" jdbcType="VARCHAR" property="d36" />
        <result column="D37" jdbcType="VARCHAR" property="d37" />
        <result column="D38" jdbcType="VARCHAR" property="d38" />
        <result column="D39" jdbcType="VARCHAR" property="d39" />
        <result column="D54" jdbcType="DOUBLE" property="d54" />
        <result column="D55" jdbcType="DOUBLE" property="d55" />
        <result column="D56" jdbcType="DOUBLE" property="d56" />
        <result column="D57" jdbcType="DOUBLE" property="d57" />
        <result column="D58" jdbcType="VARCHAR" property="d58" />
        <result column="D59" jdbcType="VARCHAR" property="d59" />
        <result column="D60" jdbcType="VARCHAR" property="d60" />
        <result column="D61" jdbcType="DOUBLE" property="d61" />
        <result column="D62" jdbcType="DOUBLE" property="d62" />
        <result column="D63" jdbcType="DOUBLE" property="d63" />
        <result column="D64" jdbcType="DOUBLE" property="d64" />
        <result column="D65" jdbcType="DOUBLE" property="d65" />
        <result column="D66" jdbcType="DOUBLE" property="d66" />
        <result column="D67" jdbcType="DOUBLE" property="d67" />
        <result column="D68" jdbcType="DOUBLE" property="d68" />
        <result column="D69" jdbcType="DOUBLE" property="d69" />
        <result column="D70" jdbcType="DOUBLE" property="d70" />
        <result column="D71" jdbcType="DOUBLE" property="d71" />
        <result column="D72" jdbcType="DOUBLE" property="d72" />
        <result column="D73" jdbcType="DOUBLE" property="d73" />
        <result column="HOSPITAL_ID" jdbcType="VARCHAR" property="hospitalId" />
        <result column="DATA_LOG_ID" jdbcType="BIGINT" property="dataLogId" />
        <result column="ACTIVE_FLAG" jdbcType="VARCHAR" property="activeFlag" />
        <result column="OPR_DATE" jdbcType="TIMESTAMP" property="oprDate" />
        <result column="UPLOAD_FLAG" jdbcType="VARCHAR" property="uploadFlag"/>
        <result column="HISTORY_TIP_STATE" jdbcType="VARCHAR" property="hisTipState"/>
        <result column="LOOK_OVER" jdbcType="VARCHAR" property="lookOver"/>
        <result column="INSUPLC_ADMDVS" jdbcType="VARCHAR" property="insuplcAdmdvs"/>

    </resultMap>

    <sql id="busSettleListFields">
        K00,    A01,    A02,    A03,    A11,    A12C,   A13,    A14,    A15C,   A16,
        A17,    A18,    A19C,   A20,    A21C,   A22,    A23C,   A24,    A25C,   A26,
        A27,    A28C,   A29N,   A29,    A30,    A31C,   A32,    A33C,   A34,    A35,
        A38C,   A46C,   A47,    A48,    A49,    A50,    A51,    A52,    A53,    A54,
        A55,    A56,    A57,    A58,    B11C,   B12,    B12S,   B13C,   B13N,   B14C,
        B14N,   B15,    B15S,   B16C,   B16N,   B17C,   B17N,   B20,    B21C,   B22C,
        B22N,   B23C,   B23N,   B24C,   B24N,   B25C,   B25N,   B26C,   B26N,   B27C,
        B27N,   B28,    B29C,   B29N,   B30C,   B31C,   B31N,   B32C,   B32N,   B33,
        B34C,   B36C,   B37,    B38,    B39,    B44,    B45,    B46,    B47,    B48,
        B49,    B51C,   B52N,   C01C,   C02N,   C03C,   C04N,   C05C,   C09C,   C10N,
        C11,    C12C,   C13N,   C14x01C,    C15x01N,    C16x01, C17x01, C18x01, C19x01, C20x01,
        C21x01C,    C22x01C,    C23x01, C24C,   C25,    C26C,   C27C,   C28,    C29,    C30,
        C31,    C32,    C33,    C34C,   C35C,   C36N,   C37C,   C38N,   C39C,   C42,
        C43,    C44,    C45,    C46,    C47,    C48C,   C49C,   D01,    D02,    D09,
        D11, D12, D13, D14, D15,  D16,    D17,    D18,    D19,    D19x01,
        D20,    D20x01, D20x02, D21,    D22,    D23,    D23x01, D24,    D25,    D26,
        D27,    D28,    D29,    D30,    D31,    D32,    D33,    D34,    D35,    D36,
        D37,    D38,    D39,    D54,    D55,    D56,    D57,    D58,    D59,    D60,
        D61,    D62,    D63,    D64,    D65,    D66,    D67,    D68,    D69,    D70,
        D71,    D72,    D73,   ADM_DIAG, HOSPITAL_ID, DATA_LOG_ID, ACTIVE_FLAG, OPR_DATE, BATCH_NUM, MTMM_IMP_DEPT_NAME, MTMM_INP_DATE,
        INHOS_MEDICAL_TYPE, MED_INS_ORGAN, MED_INS_ORGAN_OPERATOR, UPLOAD_FLAG,  CLINIC_ID,SETTLEMENT_ID,HISTORY_TIP_STATE,LOOK_OVER,PSN_NO,MUL_NWB_BIR_WT,
        MUL_NWB_ADM_WT,   RESP_NURS_CODE,STAS_TYPE, YDJY, SETTLE_LIST_BEGIN_TIME,INSUPLC_ADMDVS
    </sql>
    <!--批量新增回写主键支持-->
    <insert id="insertBusSettleList">
        INSERT INTO som_hi_invy_bas_info (
            <include refid="busSettleListFields" />
        ) VALUES
        <foreach collection="sl" item="item" index="index" separator=",">
            (
             #{item.k00,jdbcType=VARCHAR}, #{item.a01,jdbcType=VARCHAR}, #{item.a02,jdbcType=VARCHAR}, #{item.a03,jdbcType=VARCHAR}, #{item.a11,jdbcType=VARCHAR},
             #{item.a12c,jdbcType=VARCHAR}, #{item.a13,jdbcType=VARCHAR}, #{item.a14,jdbcType=INTEGER}, #{item.a15c,jdbcType=VARCHAR}, #{item.a16,jdbcType=INTEGER},
             #{item.a17,jdbcType=DOUBLE}, #{item.a18,jdbcType=DOUBLE}, #{item.a19c,jdbcType=VARCHAR}, #{item.a20,jdbcType=VARCHAR}, #{item.a21c,jdbcType=VARCHAR},
             #{item.a22,jdbcType=VARCHAR}, #{item.a23c,jdbcType=VARCHAR}, #{item.a24,jdbcType=VARCHAR}, #{item.a25c,jdbcType=VARCHAR}, #{item.a26,jdbcType=VARCHAR},
             #{item.a27,jdbcType=VARCHAR}, #{item.a28c,jdbcType=VARCHAR}, #{item.a29n,jdbcType=VARCHAR}, #{item.a29,jdbcType=VARCHAR}, #{item.a30,jdbcType=VARCHAR},
             #{item.a31c,jdbcType=VARCHAR}, #{item.a32,jdbcType=VARCHAR}, #{item.a33c,jdbcType=VARCHAR}, #{item.a34,jdbcType=VARCHAR}, #{item.a35,jdbcType=VARCHAR},
             #{item.a38c,jdbcType=VARCHAR}, #{item.a46c,jdbcType=VARCHAR}, #{item.a47,jdbcType=VARCHAR}, #{item.a48,jdbcType=VARCHAR}, #{item.a49,jdbcType=VARCHAR},
             #{item.a50,jdbcType=VARCHAR}, #{item.a51,jdbcType=VARCHAR}, #{item.a52,jdbcType=VARCHAR}, #{item.a53,jdbcType=VARCHAR}, #{item.a54,jdbcType=VARCHAR},
             #{item.a55,jdbcType=VARCHAR}, #{item.a56,jdbcType=VARCHAR}, #{item.a57,jdbcType=VARCHAR}, #{item.a58,jdbcType=VARCHAR}, #{item.b11c,jdbcType=VARCHAR},
             #{item.b12,jdbcType=VARCHAR}, #{item.b12s,jdbcType=DECIMAL}, #{item.b13c,jdbcType=VARCHAR}, #{item.b13n,jdbcType=VARCHAR}, #{item.b14c,jdbcType=VARCHAR},
             #{item.b14n,jdbcType=VARCHAR}, #{item.b15,jdbcType=VARCHAR}, #{item.b15s,jdbcType=DECIMAL}, #{item.b16c,jdbcType=VARCHAR}, #{item.b16n,jdbcType=VARCHAR},
             #{item.b17c,jdbcType=VARCHAR}, #{item.b17n,jdbcType=VARCHAR}, #{item.b20,jdbcType=VARCHAR}, #{item.b21c,jdbcType=VARCHAR}, #{item.b22c,jdbcType=VARCHAR},
             #{item.b22n,jdbcType=VARCHAR}, #{item.b23c,jdbcType=VARCHAR}, #{item.b23n,jdbcType=VARCHAR}, #{item.b24c,jdbcType=VARCHAR}, #{item.b24n,jdbcType=VARCHAR},
             #{item.b25c,jdbcType=VARCHAR}, #{item.b25n,jdbcType=VARCHAR}, #{item.b26c,jdbcType=VARCHAR}, #{item.b26n,jdbcType=VARCHAR}, #{item.b27c,jdbcType=VARCHAR},
             #{item.b27n,jdbcType=VARCHAR}, #{item.b28,jdbcType=VARCHAR}, #{item.b29c,jdbcType=VARCHAR}, #{item.b29n,jdbcType=VARCHAR}, #{item.b30c,jdbcType=VARCHAR},
             #{item.b31c,jdbcType=VARCHAR}, #{item.b31n,jdbcType=VARCHAR}, #{item.b32c,jdbcType=VARCHAR}, #{item.b32n,jdbcType=VARCHAR}, #{item.b33,jdbcType=VARCHAR},
             #{item.b34c,jdbcType=VARCHAR}, #{item.b36c,jdbcType=VARCHAR}, #{item.b37,jdbcType=VARCHAR}, #{item.b38,jdbcType=VARCHAR}, #{item.b39,jdbcType=VARCHAR},
             #{item.b44,jdbcType=INTEGER}, #{item.b45,jdbcType=INTEGER}, #{item.b46,jdbcType=INTEGER}, #{item.b47,jdbcType=INTEGER}, #{item.b48,jdbcType=VARCHAR},
             #{item.b49,jdbcType=VARCHAR}, #{item.b51c,jdbcType=VARCHAR}, #{item.b52n,jdbcType=VARCHAR}, #{item.c01c,jdbcType=VARCHAR}, #{item.c02n,jdbcType=VARCHAR},
             #{item.c03c,jdbcType=VARCHAR}, #{item.c04n,jdbcType=VARCHAR}, #{item.c05c,jdbcType=VARCHAR}, #{item.c09c,jdbcType=VARCHAR}, #{item.c10n,jdbcType=VARCHAR},
             #{item.c11,jdbcType=VARCHAR}, #{item.c12c,jdbcType=VARCHAR}, #{item.c13n,jdbcType=VARCHAR}, #{item.c14x01c,jdbcType=VARCHAR}, #{item.c15x01n,jdbcType=VARCHAR},
             #{item.c16x01,jdbcType=VARCHAR}, #{item.c17x01,jdbcType=VARCHAR}, #{item.c18x01,jdbcType=VARCHAR}, #{item.c19x01,jdbcType=VARCHAR}, #{item.c20x01,jdbcType=VARCHAR},
             #{item.c21x01c,jdbcType=VARCHAR}, #{item.c22x01c,jdbcType=VARCHAR}, #{item.c23x01,jdbcType=VARCHAR}, #{item.c24c,jdbcType=VARCHAR}, #{item.c25,jdbcType=VARCHAR},
             #{item.c26c,jdbcType=VARCHAR}, #{item.c27c,jdbcType=VARCHAR}, #{item.c28,jdbcType=INTEGER}, #{item.c29,jdbcType=INTEGER}, #{item.c30,jdbcType=INTEGER},
             #{item.c31,jdbcType=INTEGER}, #{item.c32,jdbcType=INTEGER}, #{item.c33,jdbcType=INTEGER}, #{item.c34c,jdbcType=VARCHAR}, #{item.c35c,jdbcType=VARCHAR},
             #{item.c36n,jdbcType=VARCHAR}, #{item.c37c,jdbcType=VARCHAR}, #{item.c38n,jdbcType=VARCHAR}, #{item.c39c,jdbcType=VARCHAR}, #{item.c42,jdbcType=DECIMAL},
             #{item.c43,jdbcType=DECIMAL}, #{item.c44,jdbcType=DECIMAL}, #{item.c45,jdbcType=VARCHAR}, #{item.c46,jdbcType=DOUBLE}, #{item.c47,jdbcType=VARCHAR},
             #{item.c48c,jdbcType=VARCHAR}, #{item.c49c,jdbcType=VARCHAR}, #{item.d01,jdbcType=DOUBLE},#{item.yqjcf,jdbcType=DOUBLE}, #{item.d09,jdbcType=DOUBLE},
             #{item.d11,jdbcType=DOUBLE}, #{item.d12,jdbcType=DOUBLE}, #{item.d13,jdbcType=DOUBLE}, #{item.d14,jdbcType=DOUBLE}, #{item.d15,jdbcType=DOUBLE},
             #{item.d16,jdbcType=DOUBLE}, #{item.d17,jdbcType=DOUBLE}, #{item.d18,jdbcType=DOUBLE}, #{item.d19,jdbcType=DOUBLE}, #{item.d19x01,jdbcType=DOUBLE},
             #{item.d20,jdbcType=DOUBLE}, #{item.d20x01,jdbcType=DOUBLE}, #{item.d20x02,jdbcType=DOUBLE}, #{item.d21,jdbcType=DOUBLE}, #{item.d22,jdbcType=DOUBLE},
             #{item.d23,jdbcType=DOUBLE}, #{item.d23x01,jdbcType=DOUBLE}, #{item.d24,jdbcType=DOUBLE}, #{item.d25,jdbcType=DOUBLE}, #{item.d26,jdbcType=DOUBLE},
             #{item.d27,jdbcType=DOUBLE}, #{item.d28,jdbcType=DOUBLE}, #{item.d29,jdbcType=DOUBLE}, #{item.d30,jdbcType=DOUBLE}, #{item.d31,jdbcType=DOUBLE},
             #{item.d32,jdbcType=DOUBLE}, #{item.d33,jdbcType=DOUBLE}, #{item.d34,jdbcType=DOUBLE}, #{item.d35,jdbcType=VARCHAR}, #{item.d36,jdbcType=VARCHAR},
             #{item.d37,jdbcType=VARCHAR}, #{item.d38,jdbcType=VARCHAR}, #{item.d39,jdbcType=VARCHAR}, #{item.d54,jdbcType=DOUBLE}, #{item.d55,jdbcType=DOUBLE},
             #{item.d56,jdbcType=DOUBLE}, #{item.d57,jdbcType=DOUBLE}, #{item.d58,jdbcType=VARCHAR}, #{item.d59,jdbcType=VARCHAR}, #{item.d60,jdbcType=VARCHAR},
             #{item.d61,jdbcType=DOUBLE}, #{item.d62,jdbcType=DOUBLE}, #{item.d63,jdbcType=DOUBLE}, #{item.d64,jdbcType=DOUBLE}, #{item.d65,jdbcType=DOUBLE},
             #{item.d66,jdbcType=DOUBLE}, #{item.d67,jdbcType=DOUBLE}, #{item.d68,jdbcType=DOUBLE}, #{item.d69,jdbcType=DOUBLE}, #{item.d70,jdbcType=DOUBLE},
             #{item.d71,jdbcType=DOUBLE}, #{item.d72,jdbcType=DOUBLE}, #{item.d73,jdbcType=DOUBLE},
            #{item.adm_diag,jdbcType=VARCHAR},
            #{item.hospital_id,jdbcType=VARCHAR},#{item.data_log_id,jdbcType=BIGINT},
             #{item.active_flag,jdbcType=VARCHAR},now(),#{item.batchNum,jdbcType=VARCHAR}, #{item.mtmm_imp_dept_name,jdbcType=VARCHAR}, #{item.mtmm_imp_date,jdbcType=VARCHAR},
             #{item.inhos_medical_type,jdbcType=VARCHAR}, #{item.med_ins_organ,jdbcType=VARCHAR}, #{item.med_ins_organ_operator,jdbcType=VARCHAR}, #{item.upload_flag,jdbcType=VARCHAR}, #{item.clinic_id,jdbcType=VARCHAR},
             #{item.settlement_id,jdbcType=VARCHAR}, #{item.history_tip_state,jdbcType=VARCHAR},#{item.look_over,jdbcType=VARCHAR},#{item.psn_no,jdbcType=VARCHAR}, #{item.mul_nwb_bir_wt,jdbcType=VARCHAR},
             #{item.mul_nwb_adm_wt,jdbcType=VARCHAR}, #{item.resp_nurs_code,jdbcType=VARCHAR}, #{item.stas_type,jdbcType=VARCHAR}, #{item.ydjy,jdbcType=VARCHAR}, #{item.settle_list_begin_time,jdbcType=VARCHAR}, #{item.insuplc_admdvs,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>

    <!--批量新增回写主键支持-->
    <insert id="insertVDrgSettleList">
        INSERT INTO som_drg_setl_invy_view (
            K00, A01, A02, A03, A11, A12C, A13, A14, A15C, A16, A17, A18, A19C, A20, A21C,
            A22, A23C, A24, A25C, A26, A27, A28C, A29N, A29, A30, A31C, A32, A33C, A34, A35,
            A38C, A46C, A47, A48, A49, A50, A51, A52, A53, A54, A55, A56, A57, A58, B11C, B12,
            B12S, B13C, B13N, B14C, B14N, B15, B15S, B16C, B16N, B17C, B17N, B20, B21C, B22C,
            B22N, B23C, B23N, B24C, B24N, B25C, B25N, B26C, B26N, B27C, B27N, B28, B29C, B29N,
            B30C, B31C, B31N, B32C, B32N, B33, B34C, B36C, B37, B38, B39, B44, B45, B46, B47,
            B48, B49, B51C, B52N, C01C, C02N, C03C, C04N, C05C, C09C, C10N, C11, C12C, C13N,
            C14x01C, C15x01N, C16x01, C17x01, C18x01, C19x01, C20x01, C21x01C, C22x01C, C23x01,
            C24C, C25, C26C, C27C, C28, C29, C30, C31, C32, C33, C34C, C35C, C36N, C37C, C38N,
            C39C, C42, C43, C44, C45, C46, C47, C48C, C49C, D01, D09, D11, D12, D13, D14, D15,
            D16, D17, D18, D19, D19x01, D20, D20x01, D20x02, D21, D22, D23, D23x01, D24, D25,
            D26, D27, D28, D29, D30, D31, D32, D33, D34, D35, D36, D37, D38, D39, D54, D55, D56,
            D57, D58, D59, D60, D61, D62, D63, D64, D65, D66, D67, D68, D69, D70, D71, D72, D73
        ) VALUES
        <foreach collection="sl" item="item" index="index" separator=",">
            (
            #{item.k00,jdbcType=VARCHAR}, #{item.a01,jdbcType=VARCHAR}, #{item.a02,jdbcType=VARCHAR},
            #{item.a03,jdbcType=VARCHAR}, #{item.a11,jdbcType=VARCHAR}, #{item.a12c,jdbcType=VARCHAR}, #{item.a13,jdbcType=VARCHAR},
            #{item.a14,jdbcType=INTEGER}, #{item.a15c,jdbcType=VARCHAR}, #{item.a16,jdbcType=INTEGER}, #{item.a17,jdbcType=DOUBLE},
            #{item.a18,jdbcType=DOUBLE}, #{item.a19c,jdbcType=VARCHAR}, #{item.a20,jdbcType=VARCHAR}, #{item.a21c,jdbcType=VARCHAR},
            #{item.a22,jdbcType=VARCHAR}, #{item.a23c,jdbcType=VARCHAR}, #{item.a24,jdbcType=VARCHAR}, #{item.a25c,jdbcType=VARCHAR},
            #{item.a26,jdbcType=VARCHAR}, #{item.a27,jdbcType=VARCHAR}, #{item.a28c,jdbcType=VARCHAR}, #{item.a29n,jdbcType=VARCHAR},
            #{item.a29,jdbcType=VARCHAR}, #{item.a30,jdbcType=VARCHAR}, #{item.a31c,jdbcType=VARCHAR}, #{item.a32,jdbcType=VARCHAR},
            #{item.a33c,jdbcType=VARCHAR}, #{item.a34,jdbcType=VARCHAR}, #{item.a35,jdbcType=VARCHAR}, #{item.a38c,jdbcType=VARCHAR},
            #{item.a46c,jdbcType=VARCHAR}, #{item.a47,jdbcType=VARCHAR}, #{item.a48,jdbcType=VARCHAR}, #{item.a49,jdbcType=VARCHAR},
            #{item.a50,jdbcType=VARCHAR}, #{item.a51,jdbcType=VARCHAR}, #{item.a52,jdbcType=VARCHAR}, #{item.a53,jdbcType=VARCHAR},
            #{item.a54,jdbcType=VARCHAR}, #{item.a55,jdbcType=VARCHAR}, #{item.a56,jdbcType=VARCHAR}, #{item.a57,jdbcType=VARCHAR},
            #{item.a58,jdbcType=VARCHAR}, #{item.b11c,jdbcType=VARCHAR}, #{item.b12,jdbcType=VARCHAR}, #{item.b12s,jdbcType=DECIMAL},
            #{item.b13c,jdbcType=VARCHAR}, #{item.b13n,jdbcType=VARCHAR}, #{item.b14c,jdbcType=VARCHAR}, #{item.b14n,jdbcType=VARCHAR},
            #{item.b15,jdbcType=VARCHAR}, #{item.b15s,jdbcType=DECIMAL}, #{item.b16c,jdbcType=VARCHAR}, #{item.b16n,jdbcType=VARCHAR},
            #{item.b17c,jdbcType=VARCHAR}, #{item.b17n,jdbcType=VARCHAR}, #{item.b20,jdbcType=VARCHAR}, #{item.b21c,jdbcType=VARCHAR},
            #{item.b22c,jdbcType=VARCHAR}, #{item.b22n,jdbcType=VARCHAR}, #{item.b23c,jdbcType=VARCHAR}, #{item.b23n,jdbcType=VARCHAR},
            #{item.b24c,jdbcType=VARCHAR}, #{item.b24n,jdbcType=VARCHAR}, #{item.b25c,jdbcType=VARCHAR}, #{item.b25n,jdbcType=VARCHAR},
            #{item.b26c,jdbcType=VARCHAR}, #{item.b26n,jdbcType=VARCHAR}, #{item.b27c,jdbcType=VARCHAR}, #{item.b27n,jdbcType=VARCHAR},
            #{item.b28,jdbcType=VARCHAR}, #{item.b29c,jdbcType=VARCHAR}, #{item.b29n,jdbcType=VARCHAR}, #{item.b30c,jdbcType=VARCHAR},
            #{item.b31c,jdbcType=VARCHAR}, #{item.b31n,jdbcType=VARCHAR}, #{item.b32c,jdbcType=VARCHAR}, #{item.b32n,jdbcType=VARCHAR},
            #{item.b33,jdbcType=VARCHAR}, #{item.b34c,jdbcType=VARCHAR}, #{item.b36c,jdbcType=VARCHAR}, #{item.b37,jdbcType=VARCHAR},
            #{item.b38,jdbcType=VARCHAR}, #{item.b39,jdbcType=VARCHAR}, #{item.b44,jdbcType=INTEGER}, #{item.b45,jdbcType=INTEGER},
            #{item.b46,jdbcType=INTEGER}, #{item.b47,jdbcType=INTEGER}, #{item.b48,jdbcType=VARCHAR}, #{item.b49,jdbcType=VARCHAR},
            #{item.b51c,jdbcType=VARCHAR}, #{item.b52n,jdbcType=VARCHAR}, #{item.c01c,jdbcType=VARCHAR}, #{item.c02n,jdbcType=VARCHAR},
            #{item.c03c,jdbcType=VARCHAR}, #{item.c04n,jdbcType=VARCHAR}, #{item.c05c,jdbcType=VARCHAR}, #{item.c09c,jdbcType=VARCHAR},
            #{item.c10n,jdbcType=VARCHAR}, #{item.c11,jdbcType=VARCHAR}, #{item.c12c,jdbcType=VARCHAR}, #{item.c13n,jdbcType=VARCHAR},
            #{item.c14x01c,jdbcType=VARCHAR}, #{item.c15x01n,jdbcType=VARCHAR}, #{item.c16x01,jdbcType=VARCHAR},
            #{item.c17x01,jdbcType=VARCHAR}, #{item.c18x01,jdbcType=VARCHAR}, #{item.c19x01,jdbcType=VARCHAR},
            #{item.c20x01,jdbcType=VARCHAR}, #{item.c21x01c,jdbcType=VARCHAR}, #{item.c22x01c,jdbcType=VARCHAR},
            #{item.c23x01,jdbcType=VARCHAR}, #{item.c24c,jdbcType=VARCHAR}, #{item.c25,jdbcType=VARCHAR},
            #{item.c26c,jdbcType=VARCHAR}, #{item.c27c,jdbcType=VARCHAR}, #{item.c28,jdbcType=INTEGER}, #{item.c29,jdbcType=INTEGER},
            #{item.c30,jdbcType=INTEGER}, #{item.c31,jdbcType=INTEGER}, #{item.c32,jdbcType=INTEGER}, #{item.c33,jdbcType=INTEGER},
            #{item.c34c,jdbcType=VARCHAR}, #{item.c35c,jdbcType=VARCHAR}, #{item.c36n,jdbcType=VARCHAR}, #{item.c37c,jdbcType=VARCHAR},
            #{item.c38n,jdbcType=VARCHAR}, #{item.c39c,jdbcType=VARCHAR}, #{item.c42,jdbcType=DECIMAL}, #{item.c43,jdbcType=DECIMAL},
            #{item.c44,jdbcType=DECIMAL}, #{item.c45,jdbcType=VARCHAR}, #{item.c46,jdbcType=DOUBLE}, #{item.c47,jdbcType=VARCHAR},
            #{item.c48c,jdbcType=VARCHAR}, #{item.c49c,jdbcType=VARCHAR}, #{item.d01,jdbcType=DOUBLE}, #{item.d09,jdbcType=DOUBLE},
            #{item.d11,jdbcType=DOUBLE}, #{item.d12,jdbcType=DOUBLE}, #{item.d13,jdbcType=DOUBLE}, #{item.d14,jdbcType=DOUBLE},
            #{item.d15,jdbcType=DOUBLE}, #{item.d16,jdbcType=DOUBLE}, #{item.d17,jdbcType=DOUBLE}, #{item.d18,jdbcType=DOUBLE},
            #{item.d19,jdbcType=DOUBLE}, #{item.d19x01,jdbcType=DOUBLE}, #{item.d20,jdbcType=DOUBLE}, #{item.d20x01,jdbcType=DOUBLE},
            #{item.d20x02,jdbcType=DOUBLE}, #{item.d21,jdbcType=DOUBLE}, #{item.d22,jdbcType=DOUBLE}, #{item.d23,jdbcType=DOUBLE},
            #{item.d23x01,jdbcType=DOUBLE}, #{item.d24,jdbcType=DOUBLE}, #{item.d25,jdbcType=DOUBLE}, #{item.d26,jdbcType=DOUBLE},
            #{item.d27,jdbcType=DOUBLE}, #{item.d28,jdbcType=DOUBLE}, #{item.d29,jdbcType=DOUBLE}, #{item.d30,jdbcType=DOUBLE},
            #{item.d31,jdbcType=DOUBLE}, #{item.d32,jdbcType=DOUBLE}, #{item.d33,jdbcType=DOUBLE}, #{item.d34,jdbcType=DOUBLE},
            #{item.d35,jdbcType=VARCHAR}, #{item.d36,jdbcType=VARCHAR}, #{item.d37,jdbcType=VARCHAR}, #{item.d38,jdbcType=VARCHAR},
            #{item.d39,jdbcType=VARCHAR}, #{item.d54,jdbcType=DOUBLE}, #{item.d55,jdbcType=DOUBLE}, #{item.d56,jdbcType=DOUBLE},
            #{item.d57,jdbcType=DOUBLE}, #{item.d58,jdbcType=VARCHAR}, #{item.d59,jdbcType=VARCHAR}, #{item.d60,jdbcType=VARCHAR},
            #{item.d61,jdbcType=DOUBLE}, #{item.d62,jdbcType=DOUBLE}, #{item.d63,jdbcType=DOUBLE}, #{item.d64,jdbcType=DOUBLE},
            #{item.d65,jdbcType=DOUBLE}, #{item.d66,jdbcType=DOUBLE}, #{item.d67,jdbcType=DOUBLE}, #{item.d68,jdbcType=DOUBLE},
            #{item.d69,jdbcType=DOUBLE}, #{item.d70,jdbcType=DOUBLE}, #{item.d71,jdbcType=DOUBLE}, #{item.d72,jdbcType=DOUBLE},
            #{item.d73,jdbcType=DOUBLE}
            )
        </foreach>
    </insert>

    <!-- di表去重 -->
    <delete id="diTableRepeat">
        DELETE FROM ${tabName} WHERE ${relationIdName} IN
        <foreach collection="ids" open="(" close=")" separator="," item="id">
            #{id}
        </foreach>
    </delete>

    <!-- 主表去重 -->
    <delete id="diPrimaryTableRepeat">
        DELETE FROM ${tabName} WHERE id IN
        <foreach collection="ids" open="(" close=")" separator="," item="id">
            #{id}
        </foreach>
    </delete>

    <sql id="Base_Column_List">
        ID, K00, A01, A02, A03, A11, A12C, A13, A14, A15C, A16, A17, A18, A19C, A20, A21C,
        A22, A23C, A24, A25C, A26, A27, A28C, A29N, A29, A30, A31C, A32, A33C, A34, A35,
        A38C, A46C, A47, A48, A49, A50, A51, A52, A53, A54, A55, A56, A57, A58, B11C, B12,
        B12S, B13C, B13N, B14C, B14N, B15, B15S, B16C, B16N, B17C, B17N, B20, B21C, B22C,
        B22N, B23C, B23N, B24C, B24N, B25C, B25N, B26C, B26N, B27C, B27N, B28, B29C, B29N,
        B30C, B31C, B31N, B32C, B32N, B33, B34C, B36C, B37, B38, B39, B44, B45, B46, B47,
        B48, B49, B51C, B52N, C01C, C02N, C03C, C04N, C05C, C09C, C10N, C11, C12C, C13N,
        C14x01C, C15x01N, C16x01, C17x01, C18x01, C19x01, C20x01, C21x01C, C22x01C, C23x01,
        C24C, C25, C26C, C27C, C28, C29, C30, C31, C32, C33, C34C, C35C, C36N, C37C, C38N,
        C39C, C42, C43, C44, C45, C46, C47, C48C, C49C, D01, D09, D11, D12, D13, D14, D15,
        D16, D17, D18, D19, D19x01, D20, D20x01, D20x02, D21, D22, D23, D23x01, D24, D25,
        D26, D27, D28, D29, D30, D31, D32, D33, D34, D35, D36, D37, D38, D39, D54, D55, D56,
        D57, D58, D59, D60, D61, D62, D63, D64, D65, D66, D67, D68, D69, D70, D71, D72, D73,
        HOSPITAL_ID, DATA_LOG_ID, ACTIVE_FLAG, OPR_DATE
    </sql>

    <select id="getSettleList" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        from som_hi_invy_bas_info
        <where>
            <if test="queryParam.logId!=null and queryParam.logId!=''">
                AND `DATA_LOG_ID` = #{queryParam.logId}
            </if>
            <if test="queryParam.active_flag!=null and queryParam.active_flag!=''">
                AND `ACTIVE_FLAG` = #{queryParam.active_flag}
            </if>
        </where>
        LIMIT #{queryParam.start} , #{queryParam.limit}
    </select>

    <!-- 通过 k00 获取数据 -->
    <select id="getSettleListByK00" resultType="com.my.som.model.dataHandle.SomHiInvyBasInfo">
        select id,k00 from som_hi_invy_bas_info
        where k00 in
        <foreach collection="k00s" item="k00" separator="," open="(" close=")">
            #{k00}
        </foreach>
        and hospital_id = #{hospitalId,jdbcType=VARCHAR}
        and active_flag = #{enabFlag,jdbcType=VARCHAR}
    </select>

    <!-- 获取 som_hi_invy_bas_info 表重复记录 -->
    <select id="querySettleListRepaetRecord" resultType="java.lang.String">
        SELECT a.ID AS id FROM (
             SELECT ID FROM som_hi_invy_bas_info
             WHERE K00 IN (SELECT K00 FROM som_hi_invy_bas_info WHERE ACTIVE_FLAG='1' GROUP BY K00 HAVING COUNT(1) > 1 )
               AND ID NOT IN (SELECT MAX(ID) FROM som_hi_invy_bas_info WHERE ACTIVE_FLAG='1' GROUP BY K00 HAVING COUNT(1) > 1 )
               AND ACTIVE_FLAG='1'
         ) a
    </select>

    <update id="updateDeptName" parameterType="java.lang.Long">
        update som_hi_invy_bas_info a
        set a.B13N = (select case when TYPE='2' and NAME is not null then NAME else NAME end from som_dept where CODE=a.B13C AND ACTIVE_FLAG='1'),
            a.B16N = (select case when TYPE='2' and NAME is not null then NAME else NAME end from som_dept where CODE=a.B16C AND ACTIVE_FLAG='1')
        where (a.B13N is null or a.B16N is null) AND DATA_LOG_ID = #{data_log_id,jdbcType=BIGINT}
    </update>

    <update id="updateA48IsNULL" parameterType="java.lang.Long">
        update som_hi_invy_bas_info
        set A48 = CONCAT('ne_bah_',ID)
        where A48 is null AND DATA_LOG_ID = #{data_log_id,jdbcType=BIGINT}
    </update>

    <insert id="insertBusSettleListData">
        INSERT INTO som_hi_invy_bas_info (
        K00, A01, A02, A03, A11, A12C, A13, A14, A15C, A16, A17, A18, A19C, A20, A21C,
        A22, A23C, A24, A25C, A26, A27, A28C, A29N, A29, A30, A31C, A32, A33C, A34, A35,
        A38C, A46C, A47, A48, A49, A50, A51, A52, A53, A54, A55, A56, A57, A58, B11C, B12,
        B12S, B13C, B13N, B14C, B14N, B15, B15S, B16C, B16N, B17C, B17N, B20, B21C, B22C,
        B22N, B23C, B23N, B24C, B24N, B25C, B25N, B26C, B26N, B27C, B27N, B28, B29C, B29N,
        B30C, B31C, B31N, B32C, B32N, B33, B34C, B36C, B37, B38, B39, B44, B45, B46, B47,
        B48, B49, B51C, B52N, C01C, C02N, C03C, C04N, C05C, C09C, C10N, C11, C12C, C13N,
        C14x01C, C15x01N, C16x01, C17x01, C18x01, C19x01, C20x01, C21x01C, C22x01C, C23x01,
        C24C, C25, C26C, C27C, C28, C29, C30, C31, C32, C33, C34C, C35C, C36N, C37C, C38N,
        C39C, C42, C43, C44, C45, C46, C47, C48C, C49C, D01, D09, D11, D12, D13, D14, D15,
        D16, D17, D18, D19, D19x01, D20, D20x01, D20x02, D21, D22, D23, D23x01, D24, D25,
        D26, D27, D28, D29, D30, D31, D32, D33, D34, D35, D36, D37, D38, D39, D54, D55, D56,
        D57, D58, D59, D60, D61, D62, D63, D64, D65, D66, D67, D68, D69, D70, D71, D72, D73,
        HOSPITAL_ID, DATA_LOG_ID, ACTIVE_FLAG, OPR_DATE, BATCH_NUM,ORIGINAL_OVERALL_COST
        ) VALUES
        <foreach collection="sl" item="item" index="index" separator=",">
            (
            #{item.k00,jdbcType=VARCHAR}, #{item.ddyljgdm,jdbcType=VARCHAR}, #{item.username,jdbcType=VARCHAR},
            #{item.basylx,jdbcType=VARCHAR}, #{item.xm,jdbcType=VARCHAR}, #{item.xb,jdbcType=VARCHAR},
            #{item.csrq,jdbcType=VARCHAR}, #{item.nl,jdbcType=INTEGER}, #{item.gj,jdbcType=VARCHAR},
            #{item.bzyzsnl,jdbcType=INTEGER}, #{item.xserytz,jdbcType=VARCHAR}, #{item.xsecstz,jdbcType=DOUBLE},
            #{item.mz,jdbcType=VARCHAR}, #{item.sfzh,jdbcType=VARCHAR}, #{item.hy,jdbcType=VARCHAR},
            #{item.csd,jdbcType=VARCHAR}, #{item.gg,jdbcType=VARCHAR}, #{item.hkdz,jdbcType=VARCHAR},
            #{item.yb2,jdbcType=VARCHAR}, #{item.xzz,jdbcType=VARCHAR}, #{item.dh,jdbcType=VARCHAR},
            #{item.yb1,jdbcType=VARCHAR}, #{item.gzdwjdz,jdbcType=VARCHAR}, #{item.gzdwjdz,jdbcType=VARCHAR},
            #{item.dwdh,jdbcType=VARCHAR}, #{item.yb3,jdbcType=VARCHAR}, #{item.lxrxm,jdbcType=VARCHAR},
            #{item.gx,jdbcType=VARCHAR}, #{item.dz,jdbcType=VARCHAR}, #{item.dh2,jdbcType=VARCHAR},
            #{item.zy,jdbcType=VARCHAR}, #{item.ylfkfs,jdbcType=VARCHAR}, #{item.jkkh,jdbcType=VARCHAR},
            #{item.bah,jdbcType=VARCHAR}, #{item.zycs,jdbcType=VARCHAR}, #{item.a50,jdbcType=VARCHAR},
            #{item.a51,jdbcType=VARCHAR}, #{item.a52,jdbcType=VARCHAR}, #{item.a53,jdbcType=VARCHAR},
            #{item.a54,jdbcType=VARCHAR}, #{item.a55,jdbcType=VARCHAR}, #{item.a56,jdbcType=VARCHAR},
            #{item.a57,jdbcType=VARCHAR}, #{item.a58,jdbcType=VARCHAR}, #{item.rytj,jdbcType=VARCHAR},
            #{item.rysj,jdbcType=VARCHAR}, #{item.rysjs,jdbcType=DECIMAL}, #{item.rykb,jdbcType=VARCHAR},
            #{item.rykb,jdbcType=VARCHAR}, #{item.rybf,jdbcType=VARCHAR}, #{item.rybf,jdbcType=VARCHAR},
            #{item.cysj,jdbcType=VARCHAR}, #{item.cysjs,jdbcType=DECIMAL}, #{item.cykb,jdbcType=VARCHAR}, #{item.cykb,jdbcType=VARCHAR},
            #{item.cybf,jdbcType=VARCHAR}, #{item.cybf,jdbcType=VARCHAR}, #{item.sjzyts,jdbcType=VARCHAR}, #{item.zkkb,jdbcType=VARCHAR},
            #{item.b22c,jdbcType=VARCHAR}, #{item.kzr,jdbcType=VARCHAR}, #{item.b23c,jdbcType=VARCHAR}, #{item.zrys,jdbcType=VARCHAR},
            #{item.b24c,jdbcType=VARCHAR}, #{item.zzys,jdbcType=VARCHAR}, #{item.zyysdm,jdbcType=VARCHAR}, #{item.zyys,jdbcType=VARCHAR},
            #{item.b26c,jdbcType=VARCHAR}, #{item.zrhs,jdbcType=VARCHAR}, #{item.b27c,jdbcType=VARCHAR}, #{item.jxys,jdbcType=VARCHAR},
            #{item.sxys,jdbcType=VARCHAR}, #{item.b29c,jdbcType=VARCHAR}, #{item.bmy,jdbcType=VARCHAR}, #{item.bazl,jdbcType=VARCHAR},
            #{item.b31c,jdbcType=VARCHAR}, #{item.zkys,jdbcType=VARCHAR}, #{item.b32c,jdbcType=VARCHAR}, #{item.zkhs,jdbcType=VARCHAR},
            #{item.zkrq,jdbcType=VARCHAR}, #{item.lyfs,jdbcType=VARCHAR}, #{item.sfzzyjh,jdbcType=VARCHAR}, #{item.md,jdbcType=VARCHAR},
            #{item.b38,jdbcType=VARCHAR}, #{item.b39,jdbcType=VARCHAR}, #{item.b44,jdbcType=INTEGER}, #{item.b45,jdbcType=INTEGER},
            #{item.b46,jdbcType=INTEGER}, #{item.b47,jdbcType=INTEGER}, #{item.b48,jdbcType=VARCHAR}, #{item.b49,jdbcType=VARCHAR},
            #{item.b51c,jdbcType=VARCHAR}, #{item.b52n,jdbcType=VARCHAR}, #{item.jbbm,jdbcType=VARCHAR}, #{item.mzzd,jdbcType=VARCHAR},
            #{item.jbdm,jdbcType=VARCHAR}, #{item.zyzd,jdbcType=VARCHAR},
            #{item.rybq,jdbcType=VARCHAR}, #{item.jbmm,jdbcType=VARCHAR},
            #{item.blzd,jdbcType=VARCHAR}, #{item.blh,jdbcType=VARCHAR},
            #{item.c12c,jdbcType=VARCHAR}, #{item.c13n,jdbcType=VARCHAR},
            #{item.ssjczbm1,jdbcType=VARCHAR}, #{item.ssjczmc1,jdbcType=VARCHAR},
            #{item.ssjczrq1,jdbcType=VARCHAR}, #{item.ssjb1,jdbcType=VARCHAR},
            #{item.sz1,jdbcType=VARCHAR}, #{item.yz1,jdbcType=VARCHAR},
            #{item.ez1,jdbcType=VARCHAR}, #{item.qkdj1,jdbcType=VARCHAR},
            #{item.mzfs1,jdbcType=VARCHAR}, #{item.mzys1,jdbcType=VARCHAR}, #{item.ywgm,jdbcType=VARCHAR},
            #{item.gmyw,jdbcType=VARCHAR},
            #{item.xx,jdbcType=VARCHAR}, #{item.rh,jdbcType=VARCHAR}, #{item.ryq_t,jdbcType=INTEGER},
            #{item.ryq_xs,jdbcType=INTEGER},
            #{item.ryq_f,jdbcType=INTEGER}, #{item.ryh_t,jdbcType=INTEGER}, #{item.ryh_xs,jdbcType=INTEGER},
            #{item.ryh_f,jdbcType=INTEGER},
            #{item.c34c,jdbcType=VARCHAR}, #{item.c35c,jdbcType=VARCHAR}, #{item.c36n,jdbcType=VARCHAR}, #{item.c37c,jdbcType=VARCHAR},
            #{item.c38n,jdbcType=VARCHAR}, #{item.c39c,jdbcType=VARCHAR}, #{item.c42,jdbcType=DECIMAL}, #{item.c43,jdbcType=DECIMAL},
            #{item.c44,jdbcType=DECIMAL}, #{item.c45,jdbcType=VARCHAR}, #{item.c46,jdbcType=DOUBLE},
            #{item.c47,jdbcType=VARCHAR},
            #{item.c48c,jdbcType=VARCHAR}, #{item.c49c,jdbcType=VARCHAR}, #{item.zfy,jdbcType=DOUBLE}, #{item.zfje,jdbcType=DOUBLE},
            #{item.ylfuf,jdbcType=DOUBLE}, #{item.zlczf,jdbcType=DOUBLE}, #{item.nursfee,jdbcType=DOUBLE}, #{item.oth_fee_com,jdbcType=DOUBLE},
            #{item.cas_diag_fee,jdbcType=DOUBLE}, #{item.lab_diag_fee,jdbcType=DOUBLE}, #{item.rdhy_diag_fee,jdbcType=DOUBLE}, #{item.clnc_diag_item_fee,jdbcType=DOUBLE},
            #{item.nsrgtrt_item_fee,jdbcType=DOUBLE}, #{item.wlzlf,jdbcType=DOUBLE}, #{item.oprn_treat_fee,jdbcType=DOUBLE}, #{item.maf,jdbcType=DOUBLE},
            #{item.ssf,jdbcType=DOUBLE}, #{item.rhab_fee,jdbcType=DOUBLE}, #{item.tcm_treat_fee,jdbcType=DOUBLE}, #{item.west_fee,jdbcType=DOUBLE},
            #{item.kjywf,jdbcType=DOUBLE}, #{item.tcmpat_fee,jdbcType=DOUBLE}, #{item.tcmherb,jdbcType=DOUBLE}, #{item.blo_fee,jdbcType=DOUBLE},
            #{item.bdblzpf,jdbcType=DOUBLE}, #{item.qdblzpf,jdbcType=DOUBLE}, #{item.nxyzlzpf,jdbcType=DOUBLE}, #{item.xbyzlzpf,jdbcType=DOUBLE},
            #{item.hcyyclf,jdbcType=DOUBLE}, #{item.yyclf,jdbcType=DOUBLE}, #{item.ycxyyclf,jdbcType=DOUBLE},
            #{item.oth_fee,jdbcType=DOUBLE},
            #{item.d35,jdbcType=VARCHAR}, #{item.d36,jdbcType=VARCHAR}, #{item.jssj,jdbcType=VARCHAR}, #{item.d38,jdbcType=VARCHAR},
            #{item.d39,jdbcType=VARCHAR}, #{item.d54,jdbcType=DOUBLE}, #{item.d55,jdbcType=DOUBLE}, #{item.d56,jdbcType=DOUBLE},
            #{item.d57,jdbcType=DOUBLE}, #{item.d58,jdbcType=VARCHAR}, #{item.d59,jdbcType=VARCHAR}, #{item.d60,jdbcType=VARCHAR},
            #{item.d61,jdbcType=DOUBLE}, #{item.d62,jdbcType=DOUBLE}, #{item.d63,jdbcType=DOUBLE}, #{item.d64,jdbcType=DOUBLE},
            #{item.d65,jdbcType=DOUBLE}, #{item.d66,jdbcType=DOUBLE}, #{item.d67,jdbcType=DOUBLE}, #{item.d68,jdbcType=DOUBLE},
            #{item.d69,jdbcType=DOUBLE}, #{item.d70,jdbcType=DOUBLE}, #{item.d71,jdbcType=DOUBLE}, #{item.d72,jdbcType=DOUBLE},
            #{item.d73,jdbcType=DOUBLE}, #{item.ddyljgdm,jdbcType=VARCHAR},#{item.data_log_id,jdbcType=BIGINT},
            #{item.active_flag,jdbcType=VARCHAR},now(),#{item.batchNum,jdbcType=VARCHAR},#{item.originalOverallCost,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>

    <insert id="insertDi01">
<!--        INSERT INTO som_setl_intf_bas_info-->
<!--            (-->
<!--             unique_id,-->

<!--             psn_no,            mdtrt_id,           setl_id,            fixmedins_name,     fixmedins_code,         hi_setl_lv,             hi_no,          medcasno,           dcla_time,      psn_name,-->
<!--             gend,              brdy,               age,                ntly,               nwb_age,                naty,                   patn_cert_type, certno,             prfs,           curr_addr,-->
<!--             emp_name,          emp_addr,           emp_tel,            poscode,            coner_name,             patn_rlts,              coner_addr,     coner_tel,          hi_type,        insuplc,-->
<!--             sp_psn_type,       nwb_adm_type,       nwb_bir_wt,         nwb_adm_wt,         mul_nwb_bir_wt,         mul_nwb_adm_wt,         opsp_diag_caty, opsp_mdtrt_date,    ipt_med_type,   adm_way,-->
<!--             trt_type,          adm_time,           adm_caty,           refldept_dept,      dscg_time,              dscg_caty,              act_ipt_days,   otp_wm_dise,        wm_dise_code,   otp_tcm_dise,-->
<!--             tcm_dise_code,     diag_code_cnt,      oprn_oprt_code_cnt, vent_used_dura,     pwcry_bfadm_coma_dura,  pwcry_afadm_coma_dura,  bld_cat,        bld_amt,            bld_unt,        spga_nurscare_days,-->
<!--             lv1_nurscare_days, scd_nurscare_days,  lv3_nurscare_days,  dscg_way,           acp_medins_name,        acp_optins_code,        bill_code,      bill_no,            biz_sn,         days_rinp_flag_31,-->
<!--             days_rinp_pup_31,  chfpdr_name,        chfpdr_code,        setl_begn_date,     setl_end_date,          psn_selfpay,            psn_ownpay,     acct_pay,           psn_cashpay,    hi_paymtd,-->
<!--             hsorg,             hsorg_opter,        medins_fill_dept,   medins_fill_psn,    resp_nurs_name,         resp_nurs_code,         stas_type,-->

<!--             extract_flag,      batch_num-->
<!--            ) VALUES-->
<!--                  <foreach collection="" item="item" index="index" separator=",">-->
<!--                      (-->
<!--                       #{item.unique_id},-->

<!--                       #{item.psn_no},            #{item.mdtrt_id},           #{item.setl_id},            #{item.fixmedins_name},     #{item.fixmedins_code},         #{item.hi_setl_lv},             #{item.hi_no},          #{item.medcasno},           #{item.dcla_time},      #{item.psn_name},-->
<!--                       #{item.gend},              #{item.brdy},               #{item.age},                #{item.ntly},               #{item.nwb_age},                #{item.naty},                   #{item.patn_cert_type}, #{item.certno},             #{item.prfs},           #{item.curr_addr},-->
<!--                       #{item.emp_name},          #{item.emp_addr},           #{item.emp_tel},            #{item.poscode},            #{item.coner_name},             #{item.patn_rlts},              #{item.coner_addr},     #{item.coner_tel},          #{item.hi_type},        #{item.insuplc},-->
<!--                       #{item.sp_psn_type},       #{item.nwb_adm_type},       #{item.nwb_bir_wt},         #{item.nwb_adm_wt},         #{item.mul_nwb_bir_wt},         #{item.mul_nwb_adm_wt},         #{item.opsp_diag_caty}, #{item.opsp_mdtrt_date},    #{item.ipt_med_type},   #{item.adm_way},-->
<!--                       #{item.trt_type},          #{item.adm_time},           #{item.adm_caty},           #{item.refldept_dept},      #{item.dscg_time},              #{item.dscg_caty},              #{item.act_ipt_days},   #{item.otp_wm_dise},        #{item.wm_dise_code},   #{item.otp_tcm_dise},-->
<!--                       #{item.tcm_dise_code},     #{item.diag_code_cnt},      #{item.oprn_oprt_code_cnt}, #{item.vent_used_dura},     #{item.pwcry_bfadm_coma_dura},  #{item.pwcry_afadm_coma_dura},  #{item.bld_cat},        #{item.bld_amt},            #{item.bld_unt},        #{item.spga_nurscare_days},-->
<!--                       #{item.lv1_nurscare_days}, #{item.scd_nurscare_days},  #{item.lv3_nurscare_days},  #{item.dscg_way},           #{item.acp_medins_name},        #{item.acp_optins_code},        #{item.bill_code},      #{item.bill_no},            #{item.biz_sn},         #{item.days_rinp_flag_31},-->
<!--                       #{item.days_rinp_pup_31},  #{item.chfpdr_name},        #{item.chfpdr_code},        #{item.setl_begn_date},     #{item.setl_end_date},          #{item.psn_selfpay},            #{item.psn_ownpay},     #{item.acct_pay},           #{item.psn_cashpay},    #{item.hi_paymtd},-->
<!--                       #{item.hsorg},             #{item.hsorg_opter},        #{item.medins_fill_dept},   #{item.medins_fill_psn},    #{item.resp_nurs_name},         #{item.resp_nurs_code},         #{item.stas_type},-->

<!--                       #{item.extract_flag},      #{item.batch_num}-->
<!--                      )-->
<!--                  </foreach>-->
        INSERT INTO som_setl_intf_bas_info
            (
             psn_no,unique_id,                    mdtrt_id,           setl_id,            fixmedins_name,         fixmedins_code,
             hi_setl_lv,                hi_no,              medcasno,           dcla_time,              psn_name,
             gend,                      brdy,               age,                ntly,                   nwb_age,
             naty,                      patn_cert_type,     certno,             prfs,                   curr_addr,
             emp_name,                  emp_addr,           emp_tel,            poscode,                coner_name,
             patn_rlts,                 coner_addr,         coner_tel,          hi_type,                insuplc,
             sp_psn_type,               nwb_adm_type,       nwb_bir_wt,         nwb_adm_wt,             mul_nwb_bir_wt,
             mul_nwb_adm_wt,            opsp_diag_caty,     opsp_mdtrt_date,    ipt_med_type,           adm_way,
             trt_type,                  adm_time,           adm_caty,           refldept_dept,          dscg_time,
             dscg_caty,                 act_ipt_days,       otp_wm_diag,        otp_wm_diag_dise_code,  otp_tcm_diag,
             otp_tcm_diag_dise_code,    diag_code_cnt,      oprn_oprt_code_cnt, vent_used_dura,         pwcry_bfadm_coma_dura,
             pwcry_afadm_coma_dura,     bld_cat,            bld_amt,            bld_unt,                spga_nurscare_days,
             lv1_nurscare_days,         scd_nurscare_days,  lv3_nurscare_days,  dscg_way,               acp_medins_name,
             acp_optins_code,           bill_code,          bill_no,            biz_sn,                 days_rinp_flag_31,
             rinp_pup,                  chfpdr_name,        chfpdr_code,        setl_begn_date,         setl_end_date,
             psn_selfpay,               psn_ownpay,         acct_pay,           psn_cashpay,            hi_paymtd,
             hsorg,                     hsorg_opter,        medins_fill_dept,   medins_fill_psn,        resp_nurs_code,
             stas_type,                 extract_flag,       batch_num
            ) VALUES
                <foreach collection="di" item="item" index="index" separator=",">
                    (
                        #{item.psn_no,jdbcType=VARCHAR},#{item.unique_id,jdbcType=VARCHAR},                    #{item.mdtrt_id,jdbcType=VARCHAR},          #{item.setl_id,jdbcType=VARCHAR},               #{item.fixmedins_name,jdbcType=VARCHAR},        #{item.fixmedins_code,jdbcType=VARCHAR},
                        #{item.hi_setl_lv,jdbcType=VARCHAR},                #{item.hi_no,jdbcType=VARCHAR},             #{item.medcasno,jdbcType=VARCHAR},              #{item.dcla_time,jdbcType=VARCHAR},             #{item.psn_name,jdbcType=VARCHAR},
                        #{item.gend,jdbcType=VARCHAR},                      #{item.brdy,jdbcType=VARCHAR},              #{item.age,jdbcType=VARCHAR},                   #{item.ntly,jdbcType=VARCHAR},                  #{item.nwb_age,jdbcType=VARCHAR},
                        #{item.naty,jdbcType=VARCHAR},                      #{item.patn_cert_type,jdbcType=VARCHAR},    #{item.certno,jdbcType=VARCHAR},                #{item.prfs,jdbcType=VARCHAR},                  #{item.curr_addr,jdbcType=VARCHAR},
                        #{item.emp_name,jdbcType=VARCHAR},                  #{item.emp_addr,jdbcType=VARCHAR},          #{item.emp_tel,jdbcType=VARCHAR},               #{item.poscode,jdbcType=VARCHAR},               #{item.coner_name,jdbcType=VARCHAR},
                        #{item.patn_rlts,jdbcType=VARCHAR},                 #{item.coner_addr,jdbcType=VARCHAR},        #{item.coner_tel,jdbcType=VARCHAR},             #{item.hi_type,jdbcType=VARCHAR},               #{item.insuplc,jdbcType=VARCHAR},
                        #{item.sp_psn_type,jdbcType=VARCHAR},               #{item.nwb_adm_type,jdbcType=VARCHAR},      #{item.nwb_bir_wt,jdbcType=VARCHAR},            #{item.nwb_adm_wt,jdbcType=VARCHAR},            #{item.mul_nwb_bir_wt,jdbcType=VARCHAR},
                        #{item.mul_nwb_adm_wt,jdbcType=VARCHAR},            #{item.opsp_diag_caty,jdbcType=VARCHAR},    #{item.opsp_mdtrt_date,jdbcType=VARCHAR},       #{item.ipt_med_type,jdbcType=VARCHAR},          #{item.adm_way,jdbcType=VARCHAR},
                        #{item.trt_type,jdbcType=VARCHAR},                  #{item.adm_time,jdbcType=VARCHAR},          #{item.adm_caty,jdbcType=VARCHAR},              #{item.refldept_dept,jdbcType=VARCHAR},         #{item.dscg_time,jdbcType=VARCHAR},
                        #{item.dscg_caty,jdbcType=VARCHAR},                 #{item.act_ipt_days,jdbcType=VARCHAR},      #{item.otp_wm_diag,jdbcType=VARCHAR},           #{item.otp_wm_diag_dise_code,jdbcType=VARCHAR}, #{item.otp_tcm_diag,jdbcType=VARCHAR},
                        #{item.otp_tcm_diag_dise_code,jdbcType=VARCHAR},    #{item.diag_code_cnt,jdbcType=VARCHAR},     #{item.oprn_oprt_code_cnt,jdbcType=VARCHAR},    #{item.vent_used_dura,jdbcType=VARCHAR},        #{item.pwcry_bfadm_coma_dura,jdbcType=VARCHAR},
                        #{item.pwcry_afadm_coma_dura,jdbcType=VARCHAR},     #{item.bld_cat,jdbcType=VARCHAR},           #{item.bld_amt,jdbcType=VARCHAR},               #{item.bld_unt,jdbcType=VARCHAR},               #{item.spga_nurscare_days,jdbcType=VARCHAR},
                        #{item.lv1_nurscare_days,jdbcType=VARCHAR},         #{item.scd_nurscare_days,jdbcType=VARCHAR}, #{item.lv3_nurscare_days,jdbcType=VARCHAR},     #{item.dscg_way,jdbcType=VARCHAR},              #{item.acp_medins_name,jdbcType=VARCHAR},
                        #{item.acp_optins_code,jdbcType=VARCHAR},           #{item.bill_code,jdbcType=VARCHAR},         #{item.bill_no,jdbcType=VARCHAR},               #{item.biz_sn,jdbcType=VARCHAR},                #{item.days_rinp_flag_31,jdbcType=VARCHAR},
                        #{item.rinp_pup,jdbcType=VARCHAR},                  #{item.chfpdr_name,jdbcType=VARCHAR},       #{item.chfpdr_code,jdbcType=VARCHAR},           #{item.setl_begn_date,jdbcType=VARCHAR},        #{item.setl_end_date,jdbcType=VARCHAR},
                        #{item.psn_selfpay,jdbcType=VARCHAR},               #{item.psn_ownpay,jdbcType=VARCHAR},        #{item.acct_pay,jdbcType=VARCHAR},              #{item.psn_cashpay,jdbcType=VARCHAR},           #{item.hi_paymtd,jdbcType=VARCHAR},
                        #{item.hsorg,jdbcType=VARCHAR},                     #{item.hsorg_opter,jdbcType=VARCHAR},       #{item.medins_fill_dept,jdbcType=VARCHAR},      #{item.medins_fill_psn,jdbcType=VARCHAR},       #{item.resp_nurs_code,jdbcType=VARCHAR},
                        #{item.stas_type,jdbcType=VARCHAR},                 #{item.extract_flag,jdbcType=VARCHAR},      #{item.batchNum,jdbcType=VARCHAR}
                    )
                </foreach>
    </insert>

    <insert id="insertDi01PayInfo">
        INSERT INTO som_setl_intf_fund_pay
            (
             fund_pay_type,
             fund_payamt,
             mdtrt_id,
             di01_id
            ) VALUES
                <foreach collection="di" item="item" index="index" separator=",">
                    (
                     #{item.fund_pay_type},
                     #{item.fund_payamt},
                     #{item.mdtrt_id},
                    #{item.di01_id}
                    )
                </foreach>
    </insert>

    <insert id="insertDi01OpspdiseInfo">
        INSERT INTO som_setl_intf_slow_special
            (
             diag_name,
             diag_code,
             oprn_oprt_name,
             oprn_oprt_code,
             maindiag_flag,
             mdtrt_id,
        di01_id
            ) VALUES
                <foreach collection="di" item="item" index="index" separator=",">
                    (
                     #{item.diag_name},
                     #{item.diag_code},
                     #{item.oprn_oprt_name},
                     #{item.oprn_oprt_code},
                     #{item.maindiag_flag},
                     #{item.mdtrt_id},
                    #{item.di01_id}
                    )
                </foreach>
    </insert>

    <insert id="insertDi01DiseInfo">
        INSERT INTO som_setl_intf_diag
            (
             diag_type,
             diag_code,
             diag_name,
             adm_cond_type,
             maindiag_flag,
             mdtrt_id,
             di01_id
            ) VALUES
                <foreach collection="di" item="item" index="index" separator=",">
                    (
                     #{item.diag_type},
                     #{item.diag_code},
                     #{item.diag_name},
                     #{item.adm_cond_type},
                     #{item.maindiag_flag},
                     #{item.mdtrt_id},
                    #{item.di01_id}
                    )
                </foreach>
    </insert>

    <insert id="insertDi01ItemInfo">
        INSERT INTO som_setl_intf_chrg_item
            (
             med_chrgitm,
             amt,
             claa_sumfee,
             clab_amt,
             fulamt_ownpay_amt,
             oth_amt,
             mdtrt_id,
             di01_id
            ) VALUES
                <foreach collection="di" item="item" index="index" separator=",">
                    (
                     #{item.med_chrgitm},
                     #{item.amt},
                     #{item.claa_sumfee},
                     #{item.clab_amt},
                     #{item.fulamt_ownpay_amt},
                     #{item.oth_amt},
                     #{item.mdtrt_id},
                    #{item.di01_id}
                    )
                </foreach>
    </insert>

    <insert id="insertDi01OprnInfo">
        INSERT INTO som_setl_intf_oprn
            (
                oprn_oprt_type,
                oprn_oprt_name,
                oprn_oprt_code,
                oprn_oprt_date,
                anst_way,
                oper_dr_name,
                oper_dr_code,
                anst_dr_name,
                anst_dr_code,
                oprn_oprt_begntime,
                oprn_oprt_endtime,
                anst_begntime,
                anst_endtime,
                mdtrt_id,
                di01_id
            ) VALUES
                <foreach collection="di" item="item" index="index" separator=",">
                    (
                     #{item.oprn_oprt_type},
                     #{item.oprn_oprt_name},
                     #{item.oprn_oprt_code},
                     #{item.oprn_oprt_date},
                     #{item.anst_way},
                     #{item.oper_dr_name},
                     #{item.oper_dr_code},
                     #{item.anst_dr_name},
                     #{item.anst_dr_code},
                     #{item.oprn_oprt_begntime},
                     #{item.oprn_oprt_endtime},
                     #{item.anst_begntime},
                     #{item.anst_endtime},
                     #{item.mdtrt_id},
                    #{item.di01_id}
                    )
                </foreach>
    </insert>

    <insert id="insertDi01IcuInfo">
        INSERT INTO som_setl_intf_scs_cutd
            (
             scs_cutd_ward_type,
             scs_cutd_inpool_time,
             scs_cutd_exit_time,
             scs_cutd_sum_dura,
             mdtrt_id,
        di01_id
            ) VALUES
                <foreach collection="di" item="item" index="index" separator=",">
                    (
                     #{item.scs_cutd_ward_type},
                     #{item.scs_cutd_inpool_time},
                     #{item.scs_cutd_exit_time},
                     #{item.scs_cutd_sum_dura},
                     #{item.mdtrt_id},
                     #{item.di01_id}
                    )
                </foreach>
    </insert>

    <insert id="insertDi01BldInfo">
        INSERT INTO som_setl_intf_bld
        (
        bld_cat,
        bld_amt,
        bld_unt,
        mdtrt_id,
        di01_id
        ) VALUES
        <foreach collection="di" item="item" index="index" separator=",">
            (
            #{item.bld_cat},
            #{item.bld_amt},
            #{item.bld_unt},
            #{item.mdtrt_id},
            #{item.di01_id}
            )
        </foreach>
    </insert>

    <insert id="insertDi02">
        INSERT INTO som_medcas_intf_bas_info
        (
            mdtrt_sn,           mdtrt_id,                   psn_no,                     patn_ipt_cnt,               ipt_no,
            medcasno,           psn_name,                   gend,                       brdy,                       ntly,
            ntly_name,          nwb_bir_wt,                 nwb_adm_wt,                 birplc,                     napl,
            naty_name,          naty,                       certno,                     prfs,                       mrg_stas,
            curr_addr_poscode,  curr_addr,                  psn_tel,                    resd_addr_prov,             resd_addr_city,
            resd_addr_coty,     resd_addr_subd,             resd_addr_vil,              resd_addr_housnum,          resd_addr_poscode,
            resd_addr,          empr_tel,                   empr_poscode,               empr_addr,                  coner_tel,
            coner_name,         coner_addr,                 coner_rlts_code,            adm_way_name,               adm_way_code,
            trt_type_name,      trt_type,                   adm_caty,                   adm_ward,                   adm_date,
            dscg_date,          dscg_caty,                  refldeptCatyName,           dscg_ward,                  ipt_days,
            drug_dicm_flag,     dicm_drug_name,             die_autp_flag,              abo_code,                   abo_name,
            rh_code,            rh_name,                    die_flag,                   deptdrt_name,               chfdr_name,
            atddr_name,         chfpdr_code,chfpdr_name,                ipt_dr_name,                ipt_dr_code,                resp_nurs_code,
            resp_nurs_name,     train_dr_name,              intn_dr_name,               codr_name,                  qltctrl_dr_name,
            qltctrl_nurs_name,  medcas_qlt_name,            medcas_qlt_code,            qltctrl_date,               dscg_way_name,
            dscg_way,           acp_medins_code,            acp_medins_name,            dscg_31days_rinp_flag,      dscg_31days_rinp_pup,
            damg_intx_ext_rea,  damg_intx_ext_rea_disecode, brn_damg_bfadm_coma_dura,   brn_damg_afadm_coma_dura,   vent_used_dura,
            cnfm_date,          patn_dise_diag_crsp,        patn_dise_diag_crsp_code,   ipt_patn_diag_inscp,        ipt_patn_diag_inscp_code,
            dscg_trt_rslt,      dscg_trt_rslt_code,         medins_orgcode,             age,                        aise,
            pote_intn_dr_name,  hbsag,                      hcvab,                      hivab,                      resc_cnt,
            resc_succ_cnt,      hosp_dise_fsttime,          hif_pay_way_name,           hif_pay_way_code,           med_fee_paymtd_name,
            medfee_paymtd_code, selfpay_amt,                medfee_sumamt,              ordn_med_servfee,           ordn_trt_oprt_fee,
            nurs_fee,           com_med_serv_oth_fee,       palg_diag_fee,              lab_diag_fee,               rdhy_diag_fee,
            clnc_dise_fee,      nsrgtrt_item_fee,           clnc_phys_trt_fee,          rgtrt_trt_fee,              anst_fee,
            oprn_fee,           rhab_fee,                   tcm_trt_fee,                wmfee,                      abtl_medn_fee,
            tcmpat_fee,         tcmherb_fee,                blo_fee,                    albu_fee,                   glon_fee,
            clotfac_fee,        cyki_fee,                   exam_dspo_matl_fee,         trt_dspo_matl_fee,          oprn_dspo_matl_fee,
            oth_fee,            vali_flag,                  fixmedins_code,             extract_flag,               batch_num,
            offsite_med_treat,  pre_exam,                    unique_id
        ) VALUES
        <foreach collection="di" item="item" index="index" separator=",">
            (

            #{item.mdtrt_sn,jdbcType=VARCHAR},#{item.mdtrt_id,jdbcType=VARCHAR},#{item.psn_no,jdbcType=VARCHAR},#{item.patn_ipt_cnt,jdbcType=VARCHAR},#{item.ipt_no,jdbcType=VARCHAR},
            #{item.medcasno,jdbcType=VARCHAR},#{item.psn_name,jdbcType=VARCHAR},#{item.gend,jdbcType=VARCHAR},#{item.brdy,jdbcType=VARCHAR},#{item.ntly,jdbcType=VARCHAR},
            #{item.ntly_name,jdbcType=VARCHAR},#{item.nwb_bir_wt,jdbcType=VARCHAR},#{item.nwb_adm_wt,jdbcType=VARCHAR},#{item.birplc,jdbcType=VARCHAR},#{item.napl,jdbcType=VARCHAR},
            #{item.naty_name,jdbcType=VARCHAR},#{item.naty,jdbcType=VARCHAR},#{item.certno,jdbcType=VARCHAR},#{item.prfs,jdbcType=VARCHAR},#{item.mrg_stas,jdbcType=VARCHAR},
            #{item.curr_addr_poscode,jdbcType=VARCHAR},#{item.curr_addr,jdbcType=VARCHAR},#{item.psn_tel,jdbcType=VARCHAR},#{item.resd_addr_prov,jdbcType=VARCHAR},#{item.resd_addr_city,jdbcType=VARCHAR},
            #{item.resd_addr_coty,jdbcType=VARCHAR},#{item.resd_addr_subd,jdbcType=VARCHAR},#{item.resd_addr_vil,jdbcType=VARCHAR},#{item.resd_addr_housnum,jdbcType=VARCHAR},#{item.resd_addr_poscode,jdbcType=VARCHAR},
            #{item.resd_addr,jdbcType=VARCHAR},#{item.empr_tel,jdbcType=VARCHAR},#{item.empr_poscode,jdbcType=VARCHAR},#{item.empr_addr,jdbcType=VARCHAR},#{item.coner_tel,jdbcType=VARCHAR},
            #{item.coner_name,jdbcType=VARCHAR},#{item.coner_addr,jdbcType=VARCHAR},#{item.coner_rlts_code,jdbcType=VARCHAR},#{item.adm_way_name,jdbcType=VARCHAR},#{item.adm_way_code,jdbcType=VARCHAR},
            #{item.trt_type_name,jdbcType=VARCHAR},#{item.trt_type,jdbcType=VARCHAR},#{item.adm_caty,jdbcType=VARCHAR},#{item.adm_ward,jdbcType=VARCHAR},#{item.adm_date,jdbcType=VARCHAR},
            #{item.dscg_date,jdbcType=VARCHAR},#{item.dscg_caty,jdbcType=VARCHAR},#{item.refldeptCatyName,jdbcType=VARCHAR},#{item.dscg_ward,jdbcType=VARCHAR},#{item.ipt_days,jdbcType=VARCHAR},
            #{item.drug_dicm_flag,jdbcType=VARCHAR},#{item.dicm_drug_name,jdbcType=VARCHAR},#{item.die_autp_flag,jdbcType=VARCHAR},#{item.abo_code,jdbcType=VARCHAR},#{item.abo_name,jdbcType=VARCHAR},
            #{item.rh_code,jdbcType=VARCHAR},#{item.rh_name,jdbcType=VARCHAR},#{item.die_flag,jdbcType=VARCHAR},#{item.deptdrt_name,jdbcType=VARCHAR},#{item.chfdr_name,jdbcType=VARCHAR},
            #{item.atddr_name,jdbcType=VARCHAR},#{item.chfpdr_code,jdbcType=VARCHAR},#{item.chfpdr_name,jdbcType=VARCHAR},#{item.ipt_dr_name,jdbcType=VARCHAR},#{item.ipt_dr_code,jdbcType=VARCHAR},#{item.resp_nurs_code,jdbcType=VARCHAR},#{item.resp_nurs_name,jdbcType=VARCHAR},#{item.train_dr_name,jdbcType=VARCHAR},
            #{item.intn_dr_name,jdbcType=VARCHAR},#{item.codr_name,jdbcType=VARCHAR},#{item.qltctrl_dr_name,jdbcType=VARCHAR},#{item.qltctrl_nurs_name,jdbcType=VARCHAR},#{item.medcas_qlt_name,jdbcType=VARCHAR},
            #{item.medcas_qlt_code,jdbcType=VARCHAR},#{item.qltctrl_date,jdbcType=VARCHAR},#{item.dscg_way_name,jdbcType=VARCHAR},#{item.dscg_way,jdbcType=VARCHAR},#{item.acp_medins_code,jdbcType=VARCHAR},
            #{item.acp_medins_name,jdbcType=VARCHAR},#{item.dscg_31days_rinp_flag,jdbcType=VARCHAR},#{item.dscg_31days_rinp_pup,jdbcType=VARCHAR},#{item.damg_intx_ext_rea,jdbcType=VARCHAR},#{item.damg_intx_ext_rea_disecode,jdbcType=VARCHAR},
            #{item.brn_damg_bfadm_coma_dura,jdbcType=VARCHAR},#{item.brn_damg_afadm_coma_dura,jdbcType=VARCHAR},#{item.vent_used_dura,jdbcType=VARCHAR},#{item.cnfm_date,jdbcType=VARCHAR},#{item.patn_dise_diag_crsp,jdbcType=VARCHAR},
            #{item.patn_dise_diag_crsp_code,jdbcType=VARCHAR},#{item.ipt_patn_diag_inscp,jdbcType=VARCHAR},#{item.ipt_patn_diag_inscp_code,jdbcType=VARCHAR},#{item.dscg_trt_rslt,jdbcType=VARCHAR},#{item.dscg_trt_rslt_code,jdbcType=VARCHAR},
            #{item.medins_orgcode,jdbcType=VARCHAR},#{item.age,jdbcType=VARCHAR},#{item.aise,jdbcType=VARCHAR},#{item.pote_intn_dr_name,jdbcType=VARCHAR},#{item.hbsag,jdbcType=VARCHAR},
            #{item.hcvab,jdbcType=VARCHAR},#{item.hivab,jdbcType=VARCHAR},#{item.resc_cnt,jdbcType=VARCHAR},#{item.resc_succ_cnt,jdbcType=VARCHAR},#{item.hosp_dise_fsttime,jdbcType=VARCHAR},
            #{item.hif_pay_way_name,jdbcType=VARCHAR},#{item.hif_pay_way_code,jdbcType=VARCHAR},#{item.med_fee_paymtd_name,jdbcType=VARCHAR},#{item.medfee_paymtd_code,jdbcType=VARCHAR},#{item.selfpay_amt,jdbcType=VARCHAR},
            #{item.medfee_sumamt,jdbcType=VARCHAR},#{item.ordn_med_servfee,jdbcType=VARCHAR},#{item.ordn_trt_oprt_fee,jdbcType=VARCHAR},#{item.nurs_fee,jdbcType=VARCHAR},#{item.com_med_serv_oth_fee,jdbcType=VARCHAR},
            #{item.palg_diag_fee,jdbcType=VARCHAR},#{item.lab_diag_fee,jdbcType=VARCHAR},#{item.rdhy_diag_fee,jdbcType=VARCHAR},#{item.clnc_dise_fee,jdbcType=VARCHAR},#{item.nsrgtrt_item_fee,jdbcType=VARCHAR},
            #{item.clnc_phys_trt_fee,jdbcType=VARCHAR},#{item.rgtrt_trt_fee,jdbcType=VARCHAR},#{item.anst_fee,jdbcType=VARCHAR},#{item.oprn_fee,jdbcType=VARCHAR},#{item.rhab_fee,jdbcType=VARCHAR},
            #{item.tcm_trt_fee,jdbcType=VARCHAR},#{item.wmfee,jdbcType=VARCHAR},#{item.abtl_medn_fee,jdbcType=VARCHAR},#{item.tcmpat_fee,jdbcType=VARCHAR},#{item.tcmherb_fee,jdbcType=VARCHAR},
            #{item.blo_fee,jdbcType=VARCHAR},#{item.albu_fee,jdbcType=VARCHAR},#{item.glon_fee,jdbcType=VARCHAR},#{item.clotfac_fee,jdbcType=VARCHAR},#{item.cyki_fee,jdbcType=VARCHAR},
            #{item.exam_dspo_matl_fee,jdbcType=VARCHAR},#{item.trt_dspo_matl_fee,jdbcType=VARCHAR},#{item.oprn_dspo_matl_fee,jdbcType=VARCHAR},#{item.oth_fee,jdbcType=VARCHAR},#{item.vali_flag,jdbcType=VARCHAR},
            #{item.fixmedins_code,jdbcType=VARCHAR},#{item.extract_flag,jdbcType=VARCHAR},#{item.batchNum,jdbcType=VARCHAR},#{item.offsite_med_treat,jdbcType=VARCHAR},
            #{item.pre_exam,jdbcType=DOUBLE}, #{item.unique_id,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>

    <insert id="insertDi02DiseInfo">
        INSERT INTO som_medcas_intf_diag_info
            (
             palg_no,ipt_patn_disediag_type_code,disediag_type,maindiag_flag,diag_code,
             diag_name,inhosp_diag_code,inhosp_diag_name,adm_dise_cond_name,adm_dise_cond_code,
             adm_cond,adm_cond_code,high_diag_evid,bkup_deg,bkup_deg_code,
             vali_flag,ipt_medcas_hmpg_sn,fixmedins_code,mdtrt_sn,mdtrt_id,di02_id
            )
        VALUES
            <foreach collection="di" index="index" item="item" separator=",">
               (
                #{item.palg_no},#{item.ipt_patn_disediag_type_code},#{item.disediag_type},#{item.maindiag_flag},#{item.diag_code},
                #{item.diag_name},#{item.inhosp_diag_code},#{item.inhosp_diag_name},#{item.adm_dise_cond_name},#{item.adm_dise_cond_code},
                #{item.adm_cond},#{item.adm_cond_code},#{item.high_diag_evid},#{item.bkup_deg},#{item.bkup_deg_code},
                #{item.vali_flag},#{item.ipt_medcas_hmpg_sn},#{item.fixmedins_code},#{item.mdtrt_sn},#{item.mdtrt_id},#{item.di02_id}
               )
            </foreach>
    </insert>

    <insert id="insertDi02OprnInfo">
        INSERT INTO som_medcas_intf_oprn_oprt_info
            (
             oprn_oprt_date,
             oprn_oprt_name,
             oprn_oprt_code,
             oprn_oprt_sn,
             oprn_lv_code,
             oprn_lv_name,
             oper_code,oper_name,
             asit1_name,
             asit2_name,
             sinc_heal_lv,
             sinc_heal_lv_code,
             anst_mtd_name,
             anst_mtd_code,
             anst_dr_code,anst_dr_name,
             oprn_oper_part,
             oprn_oper_part_code,
             oprn_con_time,
             anst_lv_name,
             anst_lv_code,
             oprn_patn_type,
             oprn_patn_type_code,
             main_oprn_flag,
             anst_asa_lv_code,
             anst_asa_lv_name,
             anst_medn_code,
             anst_medn_name,
             anst_medn_dos,
             unt,
             anst_begntime,
             anst_endtime,
             anst_copn_code,
             anst_copn_name,
             anst_copn_dscr,
             pacu_begntime,
             pacu_endtime,
             canc_oprn_flag,
             vali_flag,
             ipt_medcas_hmpg_sn,
             mdtrt_sn,
             oprn_oprt_begntime,
             oprn_oprt_endtime,
             mdtrt_id,
             di02_id
        )
        VALUES
            <foreach collection="di" index="index" item="item" separator=",">
               (
                #{item.oprn_oprt_date,jdbcType=VARCHAR},#{item.oprn_oprt_name,jdbcType=VARCHAR},#{item.oprn_oprt_code,jdbcType=VARCHAR},#{item.oprn_oprt_sn,jdbcType=VARCHAR},#{item.oprn_lv_code,jdbcType=VARCHAR},
                #{item.oprn_lv_name,jdbcType=VARCHAR},#{item.oper_code,jdbcType=VARCHAR},#{item.oper_name,jdbcType=VARCHAR},#{item.asit1_name,jdbcType=VARCHAR},#{item.asit2_name,jdbcType=VARCHAR},#{item.sinc_heal_lv,jdbcType=VARCHAR},
                #{item.sinc_heal_lv_code,jdbcType=VARCHAR},#{item.anst_mtd_name,jdbcType=VARCHAR},#{item.anst_mtd_code,jdbcType=VARCHAR},#{item.anst_dr_code,jdbcType=VARCHAR},#{item.anst_dr_name,jdbcType=VARCHAR},#{item.oprn_oper_part,jdbcType=VARCHAR},
                #{item.oprn_oper_part_code,jdbcType=VARCHAR},#{item.oprn_con_time,jdbcType=VARCHAR},#{item.anst_lv_name,jdbcType=VARCHAR},#{item.anst_lv_code,jdbcType=VARCHAR},#{item.oprn_patn_type,jdbcType=VARCHAR},
                #{item.oprn_patn_type_code,jdbcType=VARCHAR},#{item.main_oprn_flag,jdbcType=VARCHAR},#{item.anst_asa_lv_code,jdbcType=VARCHAR},#{item.anst_asa_lv_name,jdbcType=VARCHAR},#{item.anst_medn_code,jdbcType=VARCHAR},
                #{item.anst_medn_name,jdbcType=VARCHAR},#{item.anst_medn_dos,jdbcType=VARCHAR},#{item.unt,jdbcType=VARCHAR},#{item.anst_begntime,jdbcType=VARCHAR},#{item.anst_endtime,jdbcType=VARCHAR},
                #{item.anst_copn_code,jdbcType=VARCHAR},#{item.anst_copn_name,jdbcType=VARCHAR},#{item.anst_copn_dscr,jdbcType=VARCHAR},#{item.pacu_begntime,jdbcType=VARCHAR},#{item.pacu_endtime,jdbcType=VARCHAR},
                #{item.canc_oprn_flag,jdbcType=VARCHAR},#{item.vali_flag,jdbcType=VARCHAR},#{item.ipt_medcas_hmpg_sn,jdbcType=VARCHAR},#{item.mdtrt_sn,jdbcType=VARCHAR},#{item.oprn_oprt_begntime,jdbcType=VARCHAR},#{item.oprn_oprt_endtime,jdbcType=VARCHAR},#{item.mdtrt_id,jdbcType=VARCHAR},#{item.di02_id,jdbcType=VARCHAR}
               )
            </foreach>
    </insert>

    <insert id="insertDi02IcuInfo">
        INSERT INTO som_medcas_intf_scs_cutd_info
            (
             icu_code,inpool_icu_time,out_icu_time,medins_orgcode,nurscare_lv_code,
             nurscare_lv_name,nurscare_days,back_icu,vali_flag,ipt_medcas_hmpg_sn,
             mdtrt_sn,fixmedins_code,mdtrt_id,di02_id
            )
        VALUES
            <foreach collection="di" index="index" item="item" separator=",">
               (
                #{item.icu_code,jdbcType=VARCHAR},#{item.inpool_icu_time,jdbcType=VARCHAR},#{item.out_icu_time,jdbcType=VARCHAR},#{item.medins_orgcode,jdbcType=VARCHAR},#{item.nurscare_lv_code,jdbcType=VARCHAR},
                #{item.nurscare_lv_name,jdbcType=VARCHAR},#{item.nurscare_days,jdbcType=VARCHAR},#{item.back_icu,jdbcType=VARCHAR},#{item.vali_flag,jdbcType=VARCHAR},#{item.ipt_medcas_hmpg_sn,jdbcType=VARCHAR},
                #{item.mdtrt_sn,jdbcType=VARCHAR},#{item.fixmedins_code,jdbcType=VARCHAR},#{item.mdtrt_id,jdbcType=VARCHAR},#{item.di02_id,jdbcType=VARCHAR}
               )
            </foreach>
    </insert>

    <update id="updateDi02">
        UPDATE som_medcas_intf_bas_info SET extract_flag = 1
        <if test="mdtrt_sn != null and mdtrt_sn != ''">
        where mdtrt_sn = #{mdtrt_sn}
        </if>
    </update>

    <update id="updateDi01">
        UPDATE som_setl_intf_bas_info SET extract_flag = 1
        <if test="mdtrt_id != null and mdtrt_id != ''">
        where mdtrt_id = #{mdtrt_id}
        </if>
    </update>

    <sql id="selectInterfaceDataDi02Ids">
        <foreach collection="pageIds" open="(" close=")" separator="," item="id">
            #{id}
        </foreach>
    </sql>

    <sql id="selectInterfaceDataDi01Ids">
        <foreach collection="settleIds" open="(" close=")" separator="," item="id">
            #{id}
        </foreach>
    </sql>

    <select id="selectInterFaceData" resultType="java.util.Map">
        SELECT a.mdtrt_sn,
               <choose>
                   <when test="useVisit == 1">
                       a.mdtrt_id AS uniqueid,
                   </when>
                   <when test="useVisit == 0">
                       a.unique_id AS uniqueid,
                   </when>
               </choose>
               a.fixmedins_code AS usercode,
               d.fixmedins_name AS username,
               d.psn_no AS rybh,
               d.mdtrt_id AS jzid,
               a.psn_name AS xm,
               a.gend AS xb,
               a.brdy AS csrq,
               a.age AS nl,
               a.ntly AS gj,
               a.nwb_adm_wt AS xserytz,
               a.nwb_bir_wt AS xsecstz,
               a.naty AS mz,
               a.certno AS sfzh,
               a.mrg_stas AS hy,
               a.birplc AS csd,
               a.napl AS gg,
               a.resd_addr AS hkdz,
               a.resd_addr_poscode AS yb2,
               a.curr_addr AS xzz,
               a.psn_tel AS dh,
               a.curr_addr_poscode AS yb1,
               a.empr_addr AS gzdwjdz,
               a.empr_tel AS dwdh,
               a.empr_poscode AS yb3,
               a.coner_name AS lxrxm,
               a.coner_rlts_code AS gx,
               a.coner_addr AS dz,
               a.coner_tel AS dh2,
               a.prfs AS zy,
               a.medfee_paymtd_code AS ylfkfs,
               a.medfee_paymtd_code AS yblx,
                <choose>
                    <when test="convert == 1">
                        a.ipt_no AS bah,
                    </when>
                    <when test="convert == 0">
                        a.medcasno AS bah,
                    </when>
                </choose>
               a.patn_ipt_cnt AS zycs,
               a.adm_way_code AS rytj,
               a.adm_date AS rysj,
               a.adm_caty AS rykbbm,
               a.adm_ward AS rybf,
               a.dscg_date AS cysj,
               a.dscg_caty AS cykbbm,
               a.dscg_ward AS cybf,
               a.ipt_days AS sjzyts,
               a.refldeptCatyName AS zkkbbm,
               a.deptdrt_name AS kzrdm,
               a.chfdr_name AS zrysdm,
               a.atddr_name AS zzysdm1,
               a.ipt_dr_code AS zyysdm,
               a.ipt_dr_name AS zyys,
               a.resp_nurs_code AS zrhsdm,
               a.train_dr_name AS jxysdm,
               a.intn_dr_name AS sxys,
               a.codr_name AS bmydm,
               a.medcas_qlt_code AS bazl,
               a.qltctrl_dr_name AS zkysdm,
               a.qltctrl_nurs_name AS zkhsdm,
               a.qltctrl_date AS zkrq,
               a.dscg_way AS lyfs,
               a.dscg_31days_rinp_flag AS sfzzyjh,
               a.dscg_31days_rinp_pup AS md,
               a.trt_type AS zllb,
               a.acp_medins_code AS zynjsjgdm,
               a.acp_medins_name AS zynjsjg,
               a.chfpdr_code AS zzysdm,
               a.chfpdr_name AS zzysxm,
               a.damg_intx_ext_rea_disecode AS h23,
               a.damg_intx_ext_rea AS wbyy,
               a.drug_dicm_flag AS ywgm,
               a.dicm_drug_name AS gmyw,
               a.abo_code AS xx,
               a.rh_code rh,
               a.brn_damg_bfadm_coma_dura,
               a.brn_damg_afadm_coma_dura,
               a.die_autp_flag AS swhzsj,
               a.vent_used_dura,
               a.medfee_sumamt AS zfy,
               a.selfpay_amt AS zfje,
               a.ordn_med_servfee AS ylfuf,
               a.ordn_trt_oprt_fee AS zlczf,
               a.nurs_fee AS nursfee,
               a.com_med_serv_oth_fee AS oth_fee_com,
               a.palg_diag_fee AS cas_diag_fee,
               a.lab_diag_fee AS lab_diag_fee,
               a.rdhy_diag_fee AS rdhy_diag_fee,
               a.clnc_dise_fee AS clnc_diag_item_fee,
               a.nsrgtrt_item_fee AS nsrgtrt_item_fee,
               a.clnc_phys_trt_fee AS wlzlf,
               a.rgtrt_trt_fee AS oprn_treat_fee,
               a.anst_fee AS maf,
               a.oprn_fee AS ssf,
               a.rhab_fee AS rhab_fee,
               a.tcm_trt_fee AS tcm_treat_fee,
               a.wmfee AS west_fee,
               a.abtl_medn_fee AS kjywf,
               a.tcmpat_fee AS tcmpat_fee,
               a.tcmherb_fee AS tcmherb,
               a.blo_fee AS blo_fee,
               a.albu_fee AS bdblzpf,
               a.glon_fee AS qdblzpf,
               a.clotfac_fee AS nxyzlzpf,
               a.cyki_fee AS xbyzlzpf,
               a.exam_dspo_matl_fee AS hcyyclf,
               a.trt_dspo_matl_fee AS yyclf,
               a.oprn_dspo_matl_fee AS ycxyyclf,
               a.oth_fee AS oth_fee,
               a.offsite_med_treat AS ydjy,
               a.pre_exam as yqjcf,<!--院前检查费-->

               b.ipt_patn_disediag_type_code,
               b.diag_code,
               b.diag_name,
               b.adm_cond_code,
               b.zd_ids,
               c.oprn_oprt_code,
               c.oprn_oprt_name,
               c.oprn_oprt_date,
               c.oprn_lv_code,
               c.oper_code,
               c.oper_name,
               c.asit_1_name,
               c.asit_name2,
               c.sinc_heal_lv_code,
               c.anst_mtd_code,
               c.anst_dr_code,
               c.anst_dr_name,
               c.anst_begntime,
               c.anst_endtime,
               c.oprn_oprt_begntime,
               c.oprn_oprt_endtime,
               c.ss_ids,
               d.*
        FROM som_medcas_intf_bas_info a
        LEFT JOIN
            (
                SELECT di02_id,
                       GROUP_CONCAT(ipt_patn_disediag_type_code ORDER BY main_flag DESC,id) AS ipt_patn_disediag_type_code,
                       GROUP_CONCAT(diag_code ORDER BY main_flag DESC,id) AS diag_code,
                       GROUP_CONCAT(diag_name ORDER BY main_flag DESC,id) AS diag_name,
                       GROUP_CONCAT(adm_dise_cond_code ORDER BY main_flag DESC,id) AS adm_cond_code,
                       GROUP_CONCAT(di02_id ORDER BY main_flag DESC,id) AS zd_ids
                FROM (
                    select *,
                         case when maindiag_flag = '1' then '1' else '0' end as main_flag
                    FROM som_medcas_intf_diag_info
                    WHERE di02_id in <include refid="selectInterfaceDataDi02Ids" />
                ) a
                GROUP BY di02_id
            ) b
            ON a.id = b.di02_id
        LEFT JOIN
            (
                SELECT di02_id,
                       GROUP_CONCAT(oprn_oprt_code ORDER BY main_flag DESC,id) oprn_oprt_code,
                       GROUP_CONCAT(oprn_oprt_name ORDER BY main_flag DESC,id) oprn_oprt_name,
                       GROUP_CONCAT(oprn_oprt_date ORDER BY main_flag DESC,id) oprn_oprt_date,
                       GROUP_CONCAT(oprn_lv_code ORDER BY main_flag DESC,id) oprn_lv_code,
                       GROUP_CONCAT(oper_code ORDER BY main_flag DESC,id) oper_code,
                       GROUP_CONCAT(oper_name ORDER BY main_flag DESC,id) oper_name,
                       GROUP_CONCAT(asit1_name ORDER BY main_flag DESC,id) asit_1_name,
                       GROUP_CONCAT(asit2_name ORDER BY main_flag DESC,id) asit_name2,
                       GROUP_CONCAT(sinc_heal_lv_code ORDER BY main_flag DESC,id) sinc_heal_lv_code,
                       GROUP_CONCAT(anst_mtd_code ORDER BY main_flag DESC,id) anst_mtd_code,
                       GROUP_CONCAT(anst_dr_code ORDER BY main_flag DESC,id) anst_dr_code,
                       GROUP_CONCAT(anst_dr_name ORDER BY main_flag DESC,id) anst_dr_name,
                       GROUP_CONCAT(anst_begntime ORDER BY main_flag DESC,id) anst_begntime,
                       GROUP_CONCAT(anst_endtime ORDER BY main_flag DESC,id) anst_endtime,
                       GROUP_CONCAT(oprn_oprt_begntime ORDER BY main_flag DESC,id) oprn_oprt_begntime,
                       GROUP_CONCAT(oprn_oprt_endtime ORDER BY main_flag DESC,id) oprn_oprt_endtime,
                       GROUP_CONCAT(di02_id ORDER BY main_flag DESC,id) AS ss_ids
                FROM (
                    select *,
                                 case when main_oprn_flag = '1' then '1' else '0' end as main_flag
                    FROM som_medcas_intf_oprn_oprt_info
                    WHERE di02_id in <include refid="selectInterfaceDataDi02Ids" />
                ) a
                GROUP BY di02_id
            ) c
            ON a.id = c.di02_id
        LEFT JOIN
            (
                SELECT a.psn_no,
                       a.mdtrt_id,
                       a.mul_nwb_bir_wt as dxsecstz,
                       a.mul_nwb_adm_wt as dxserytz,
                       a.stas_type as ztfl,
                       a.mdtrt_id AS mi,
                       a.setl_id AS jsid,
                       a.fixmedins_name,
                       a.nwb_age AS bzyzsnl,
                       a.emp_name AS gzdwjdz,
                       a.hi_setl_lv AS ybjsdj,
                       a.hi_no AS ybbh,
                       substr(a.dcla_time,1,10) AS sbsj,
                       a.patn_cert_type,
                       a.hi_type AS yblxold,
                       a.sp_psn_type AS tsrylx,
                       a.insuplc AS cbd,
                       a.nwb_adm_type AS xserylx,
                       a.biz_sn AS ywlsh,
                       a.ipt_med_type AS zyyllx,
                       a.spga_nurscare_days AS tjhlts,
                       a.lv1_nurscare_days AS yjhlts,
                       a.scd_nurscare_days AS ejhlts,
                       a.lv3_nurscare_days AS sjhlts,
                       a.chfpdr_code,
                       a.chfpdr_name,
                       a.otp_wm_diag_dise_code AS jbbm,
                       a.otp_wm_diag AS mzzd,
                       a.otp_tcm_diag_dise_code AS jbbm_zyzd,
                       a.otp_tcm_diag AS mzzd_zyzd,
                       a.bld_cat AS sxpz,               a.bld_amt AS sxl,               a.bld_unt AS sxjldw,            a.setl_begn_date AS jskssj,     a.setl_end_date AS jsjssj,
                       a.bill_code AS pjdm,             a.bill_no AS pjhm,              a.psn_selfpay AS grzf,          a.psn_ownpay AS grzf1,          a.acct_pay AS grzhzf,
                       a.psn_cashpay AS grxjzf,         a.hi_paymtd AS ybzffs,          a.medins_fill_dept AS yljgtbbm, a.medins_fill_psn AS yljgtbr,   a.opsp_diag_caty AS mtmmzdkb,  a.opsp_mdtrt_date AS mtmmjzrq,  a.hsorg AS ybjg,   a.hsorg_opter AS ybjgjbr,     b.fund_pay_type,
                       b.fund_payamt,                   b.pay_ids,                      c.mtmm_oprn_oprt_name,          c.mtmm_oprn_oprt_code,          c.mtmm_diag_code,               c.mtmm_diag_name,c.opsp_ids,
                       d.med_chrgitm,                   d.amt,                          d.claa_sumfee,                  d.clab_amt,                     d.fulamt_ownpay_amt,
                       d.oth_amt,                       d.item_ids,                     e.scs_cutd_ward_type,           e.scs_cutd_inpool_time,         e.scs_cutd_exit_time,           e.scs_cutd_sum_dura,e.icu_ids,
                       f.bld_cat,                       f.bld_amt,                      f.bld_unt,f.bld_ids
                FROM som_setl_intf_bas_info a
                LEFT JOIN
                    (
                        SELECT di01_id,
                             GROUP_CONCAT(fund_pay_type) fund_pay_type,
                             GROUP_CONCAT(fund_payamt) fund_payamt,
                             GROUP_CONCAT(di01_id) AS pay_ids
                        FROM som_setl_intf_fund_pay
                        WHERE di01_id in <include refid="selectInterfaceDataDi01Ids" />
                        GROUP BY di01_id
                    ) b
                    ON a.id = b.di01_id
                LEFT JOIN
                    (
                        SELECT di01_id,
                             GROUP_CONCAT(diag_code) mtmm_diag_code,
                             GROUP_CONCAT(diag_name) mtmm_diag_name,
                             GROUP_CONCAT(oprn_oprt_name) mtmm_oprn_oprt_name,
                             GROUP_CONCAT(oprn_oprt_code) mtmm_oprn_oprt_code,
                             GROUP_CONCAT(di01_id) AS opsp_ids
                        FROM som_setl_intf_slow_special
                        WHERE di01_id in <include refid="selectInterfaceDataDi01Ids" />
                        GROUP BY di01_id
                    ) c
                    ON a.id = c.di01_id
                LEFT JOIN
                    (
                        SELECT di01_id,
                                GROUP_CONCAT(med_chrgitm) med_chrgitm,
                                GROUP_CONCAT(amt) amt,
                                GROUP_CONCAT(claa_sumfee) claa_sumfee,
                                GROUP_CONCAT(clab_amt) clab_amt,
                                GROUP_CONCAT(fulamt_ownpay_amt) fulamt_ownpay_amt,
                                GROUP_CONCAT(oth_amt) oth_amt,
                                GROUP_CONCAT(di01_id) AS item_ids
                        FROM som_setl_intf_chrg_item
                        WHERE di01_id in <include refid="selectInterfaceDataDi01Ids" />
                        GROUP BY di01_id
                    ) d
                    ON a.id = d.di01_id
                LEFT JOIN
                    (
                        SELECT di01_id,
                               GROUP_CONCAT(scs_cutd_ward_type) scs_cutd_ward_type,
                               GROUP_CONCAT(scs_cutd_inpool_time) scs_cutd_inpool_time,
                               GROUP_CONCAT(scs_cutd_exit_time) scs_cutd_exit_time,
                               GROUP_CONCAT(scs_cutd_sum_dura) scs_cutd_sum_dura,
                               GROUP_CONCAT(di01_id) AS icu_ids
                         FROM som_setl_intf_scs_cutd
                        WHERE di01_id in <include refid="selectInterfaceDataDi01Ids" />
                         GROUP BY di01_id
                    ) e
                    ON a.id = e.di01_id
                LEFT JOIN
                    (
                        SELECT di01_id,
                               GROUP_CONCAT(bld_cat) bld_cat,
                               GROUP_CONCAT(bld_amt) bld_amt,
                               GROUP_CONCAT(bld_unt) bld_unt,
                               GROUP_CONCAT(di01_id) AS bld_ids
                        FROM som_setl_intf_bld
                        WHERE di01_id IN <include refid="selectInterfaceDataDi01Ids" />
                        GROUP BY di01_id
                    ) f
                ON a.id = f.di01_id
                WHERE a.id IN <include refid="selectInterfaceDataDi01Ids" />
            ) d
            ON a.mdtrt_id = d.mi
            WHERE a.id IN <include refid="selectInterfaceDataDi02Ids" />
    </select>

    <select id="selectSettleListData" resultType="java.util.Map">
        SELECT a.id, a.K00 AS uniqueid,   a.a01 AS usercode,  d.fixmedins_name AS username,
               a.a11 AS xm, a.a12c AS xb,   a.a13 AS csrq,  a.a14 AS nl,
               a.a15c AS gj,    a.a17 AS xserytz,   a.a18 AS xsecstz,
               a.a19c AS mz,    a.a20 AS sfzh,  a.a21c AS hy,
               a.a22 AS csd,    a.a23c AS gg,   a.a24 AS hkdz,
               a.a25c AS yb2,   a.a26 AS xzz,   a.a27 AS dh,
               a.a28c AS yb1,   a.a29n AS gzdwjdz,  a.a30 AS dwdh,
               a.a31c AS yb3,   a.a32 AS lxrxm, a.a33c AS gx,
               a.a34 AS dz, a.a35 AS dh2,   a.a38c AS zy,
               a.a46c AS ylfkfs,    a.a48 AS bah,   a.a49 AS zycs,
               a.a54 AS yblx,   a.b11c AS rytj, a.b12 AS rysj,
               a.b13c AS rykbbm,    a.b14c AS rybf, a.b15 AS cysj,
               a.b16c AS cykbbm,    a.b17c AS cybf, a.b20 AS sjzyts,
               a.b21c AS zkkbbm,    a.b22c AS kzrdm,    a.b23c AS zrysdm,
               a.b24c AS zzysdm1,   a.b25c AS zyysdm,   a.b25n AS zyys,
               a.b26c AS zrhsdm,    a.b26n AS zrhsxm, a.b27c AS jxysdm,   a.b28 AS sxys,
               a.b29c AS bmydm, a.b30c AS bazl, a.b31c AS zkysdm,
               a.b32c AS zkhsdm,    a.b33 AS zkrq,  a.b34c AS lyfs,
               a.b36c AS sfzzyjh,   a.b37 AS md,    a.b39 AS zllb,
               a.b48 AS zynjsjgdm,  a.b49 AS zynjsjg,   a.b51c AS zzysdm,
               a.b52n AS zzysxm,    a.c12c AS h23,  a.c13n AS wbyy,
               a.c24c AS ywgm,  a.c25 AS gmyw,  a.c26c AS xx,
               a.c27c rh,   a.c34c AS swhzsj,   a.d01 AS zfy,
               a.D02 as yqjcf,<!--院前检查费-->    a.d09 AS zfje,
               a.d11 AS ylfuf,  a.d12 AS zlczf, a.d13 AS nursfee,
               a.d14 AS oth_fee_com,   a.d15 AS cas_diag_fee, a.d16 AS lab_diag_fee,
               a.d17 AS rdhy_diag_fee, a.d18 AS clnc_diag_item_fee,   a.d19 AS nsrgtrt_item_fee,
               a.d19x01 AS wlzlf,   a.d20 AS oprn_treat_fee, a.d20x01 AS maf,
               a.d20x02 AS ssf, a.d21 AS rhab_fee,   a.d22 AS tcm_treat_fee,
               a.d23 AS west_fee,    a.d23x01 AS kjywf,  a.d24 AS tcmpat_fee,
               a.d25 AS tcmherb,  a.d26 AS blo_fee,    a.d27 AS bdblzpf,
               a.d28 AS qdblzpf,    a.d29 AS nxyzlzpf,  a.d30 AS xbyzlzpf,
               a.d31 AS hcyyclf,    a.d32 AS yyclf, a.d33 AS ycxyyclf,
               a.d34 AS oth_fee,
               a.ydjy AS ydjy,
               b.ipt_patn_disediag_type_code,
               b.diag_code, b.diag_name,    b.adm_cond_code,    b.zd_ids,
               c.oprn_oprt_code,    c.oprn_oprt_name,   c.oprn_oprt_date,
               c.oprn_lv_code,  c.oper_code,    c.oper_name,
               c.asit_1_name,   c.asit_name2,   c.sinc_heal_lv_code,
               c.anst_mtd_code, c.anst_dr_code, c.anst_dr_name,
               c.anst_begntime, c.anst_endtime, c.oprn_oprt_begntime,
               c.oprn_oprt_endtime, c.ss_ids,
               d.*
        FROM som_hi_invy_bas_info a
        LEFT JOIN
            (
                SELECT SETTLE_LIST_ID,
                       GROUP_CONCAT(TYPE) AS ipt_patn_disediag_type_code,
                       GROUP_CONCAT(dscg_diag_codg) AS diag_code,
                       GROUP_CONCAT(dscg_diag_name) AS diag_name,
                       GROUP_CONCAT(dscg_diag_adm_cond) AS adm_cond_code,
                       GROUP_CONCAT(SETTLE_LIST_ID) AS zd_ids
                FROM som_diag
                WHERE SETTLE_LIST_ID IN
                      (
                          SELECT ID FROM som_hi_invy_bas_info WHERE K00 IN
                            <foreach collection="uniqueIds" open="(" close=")" separator="," item="id">
                                #{id}
                            </foreach>
                      )
                GROUP BY SETTLE_LIST_ID
            ) b ON a.ID = b.SETTLE_LIST_ID
        LEFT JOIN
        (
            SELECT SETTLE_LIST_ID,
            GROUP_CONCAT(C35C) AS oprn_oprt_code,
            GROUP_CONCAT(C36N) AS oprn_oprt_name,
            GROUP_CONCAT(oprn_oprt_date) AS oprn_oprt_date,
            GROUP_CONCAT(oprn_oprt_lv) AS oprn_lv_code,
            GROUP_CONCAT(C39C) AS oper_code,
            GROUP_CONCAT(oprn_oprt_oper_name) AS oper_name,
            GROUP_CONCAT(oprn_oprt_1_asit) AS asit_1_name,
            GROUP_CONCAT(oprn_oprt_2_asit) AS asit_name2,
            GROUP_CONCAT(C42) AS sinc_heal_lv_code,
            GROUP_CONCAT(C43) AS anst_mtd_code,
            GROUP_CONCAT(oprn_oprt_anst_dr_code) AS anst_dr_code,
            GROUP_CONCAT(oprn_oprt_anst_dr_name) AS anst_dr_name,
            GROUP_CONCAT(OPRN_OPRT_BEGNTIME) AS oprn_oprt_begntime,
            GROUP_CONCAT(OPRN_OPRT_ENDTIME) AS oprn_oprt_endtime,
            GROUP_CONCAT(ANST_BEGNTIME) AS anst_begntime,
            GROUP_CONCAT(ANST_ENDTIME) AS anst_endtime,
            GROUP_CONCAT(SETTLE_LIST_ID) AS ss_ids
            FROM som_oprn_oprt_info
            WHERE SETTLE_LIST_ID IN
            (
            SELECT ID FROM som_hi_invy_bas_info WHERE K00 IN
            <foreach collection="uniqueIds" open="(" close=")" separator="," item="id">
                #{id}
            </foreach>
            )
            GROUP BY SETTLE_LIST_ID
        ) c ON a.ID = c.SETTLE_LIST_ID
        LEFT JOIN
            (
                SELECT a.psn_no AS rybh,
                       a.mdtrt_id AS jzid,
                       a.mul_nwb_bir_wt as dxsecstz,
                       a.mul_nwb_adm_wt as dxserytz,
                       a.stas_type as ztfl,
                       a.mdtrt_id AS mi,
                       a.setl_id AS jsid,
                       a.fixmedins_name,
                       a.nwb_age AS bzyzsnl,
                       a.emp_name AS gzdwjdz,
                       a.hi_setl_lv AS ybjsdj,          a.hi_no AS ybbh,                substr(a.dcla_time,1,10) AS sbsj,a.patn_cert_type,              a.hi_type AS yblxold,
                       a.sp_psn_type AS tsrylx,         a.insuplc AS cbd,               a.nwb_adm_type AS xserylx,      a.biz_sn AS ywlsh,              a.ipt_med_type AS zyyllx,
                       a.spga_nurscare_days AS tjhlts,  a.lv1_nurscare_days AS yjhlts,  a.scd_nurscare_days AS ejhlts,  a.lv3_nurscare_days AS sjhlts,  a.chfpdr_code,
                       a.chfpdr_name,         a.otp_wm_diag_dise_code AS jbbm,a.otp_wm_diag AS mzzd,          a.otp_tcm_diag_dise_code AS jbbm_zyzd,a.otp_tcm_diag AS mzzd_zyzd,
                       a.bld_cat AS sxpz,               a.bld_amt AS sxl,               a.bld_unt AS sxjldw,            a.setl_begn_date AS jskssj,     a.setl_end_date AS jsjssj,
                       a.bill_code AS pjdm,             a.bill_no AS pjhm,              a.psn_selfpay AS grzf,          a.psn_ownpay AS grzf1,          a.acct_pay AS grzhzf,
                       a.psn_cashpay AS grxjzf,
                       a.hi_paymtd AS ybzffs,
                       a.medins_fill_dept AS yljgtbbm,
                       a.medins_fill_psn AS yljgtbr,
                       a.opsp_diag_caty AS mtmmzdkb,
                       a.opsp_mdtrt_date AS mtmmjzrq,
                       a.hsorg AS ybjg,
                       a.hsorg_opter AS ybjgjbr,
                       b.fund_pay_type,
                       b.fund_payamt,
                       b.pay_ids,
                       c.mtmm_oprn_oprt_name,
                       c.mtmm_oprn_oprt_code,
                       c.mtmm_diag_code,
                       c.mtmm_diag_name,
                       c.opsp_ids,
                       d.med_chrgitm,
                       d.amt,
                       d.claa_sumfee,
                       d.clab_amt,
                       d.fulamt_ownpay_amt,
                       d.oth_amt,
                       d.item_ids,
                       e.scs_cutd_ward_type,
                       e.scs_cutd_inpool_time,
                       e.scs_cutd_exit_time,
                       e.scs_cutd_sum_dura,
                       e.icu_ids,
                       f.bld_cat,
                       f.bld_amt,
                       f.bld_unt,
                       f.bld_ids
                FROM som_setl_intf_bas_info a
                LEFT JOIN
                    (
                    SELECT di01_id,
                    GROUP_CONCAT(fund_pay_type) fund_pay_type,
                    GROUP_CONCAT(fund_payamt) fund_payamt,
                    GROUP_CONCAT(di01_id) AS pay_ids
                    FROM som_setl_intf_fund_pay
                    WHERE di01_id in <include refid="selectInterfaceDataDi01Ids" />
                    GROUP BY di01_id
                    ) b
                ON a.id = b.di01_id
                LEFT JOIN
                    (
                    SELECT di01_id,
                    GROUP_CONCAT(diag_code) mtmm_diag_code,
                    GROUP_CONCAT(diag_name) mtmm_diag_name,
                    GROUP_CONCAT(oprn_oprt_name) mtmm_oprn_oprt_name,
                    GROUP_CONCAT(oprn_oprt_code) mtmm_oprn_oprt_code,
                    GROUP_CONCAT(di01_id) AS opsp_ids
                    FROM som_setl_intf_slow_special
                    WHERE di01_id in <include refid="selectInterfaceDataDi01Ids" />
                    GROUP BY di01_id
                    ) c
                ON a.id = c.di01_id
                LEFT JOIN
                    (
                    SELECT di01_id,
                    GROUP_CONCAT(med_chrgitm) med_chrgitm,
                    GROUP_CONCAT(amt) amt,
                    GROUP_CONCAT(claa_sumfee) claa_sumfee,
                    GROUP_CONCAT(clab_amt) clab_amt,
                    GROUP_CONCAT(fulamt_ownpay_amt) fulamt_ownpay_amt,
                    GROUP_CONCAT(oth_amt) oth_amt,
                    GROUP_CONCAT(di01_id) AS item_ids
                    FROM som_setl_intf_chrg_item
                    WHERE di01_id in <include refid="selectInterfaceDataDi01Ids" />
                    GROUP BY di01_id
                    ) d
                ON a.id = d.di01_id
                LEFT JOIN
                    (
                    SELECT di01_id,
                    GROUP_CONCAT(scs_cutd_ward_type) scs_cutd_ward_type,
                    GROUP_CONCAT(scs_cutd_inpool_time) scs_cutd_inpool_time,
                    GROUP_CONCAT(scs_cutd_exit_time) scs_cutd_exit_time,
                    GROUP_CONCAT(scs_cutd_sum_dura) scs_cutd_sum_dura,
                    GROUP_CONCAT(di01_id) AS icu_ids
                    FROM som_setl_intf_scs_cutd
                    WHERE di01_id in <include refid="selectInterfaceDataDi01Ids" />
                    GROUP BY di01_id
                    ) e
                ON a.id = e.di01_id
                LEFT JOIN
                    (
                    SELECT di01_id,
                    GROUP_CONCAT(bld_cat) bld_cat,
                    GROUP_CONCAT(bld_amt) bld_amt,
                    GROUP_CONCAT(bld_unt) bld_unt,
                    GROUP_CONCAT(di01_id) AS bld_ids
                    FROM som_setl_intf_bld
                    WHERE di01_id IN <include refid="selectInterfaceDataDi01Ids" />
                    GROUP BY di01_id
                    ) f
                ON a.id = f.di01_id
                WHERE a.id IN <include refid="selectInterfaceDataDi01Ids" />
            ) d ON a.CLINIC_ID = d.mi
        WHERE a.K00 IN
        <foreach collection="uniqueIds" open="(" close=")" separator="," item="id">
            #{id}
        </foreach>
    </select>

    <select id="selectDi02DuplicateData" resultType="com.my.som.model.dataHandle.SomMedcasIntfBasInfo">
        SELECT id,mdtrt_sn FROM som_medcas_intf_bas_info
        WHERE mdtrt_id IN ( SELECT mdtrt_id FROM som_medcas_intf_bas_info GROUP BY mdtrt_id HAVING COUNT(1) > 1 )
          AND id NOT IN ( SELECT MAX(id) FROM som_medcas_intf_bas_info GROUP BY mdtrt_id HAVING COUNT(1) > 1 )
    </select>

    <select id="selectDi01DuplicateData" resultType="com.my.som.model.dataHandle.SomMedcasIntfBasInfo">
        SELECT id,mdtrt_id FROM som_setl_intf_bas_info
        WHERE mdtrt_id IN ( SELECT mdtrt_id FROM som_setl_intf_bas_info GROUP BY mdtrt_id HAVING COUNT(1) > 1 )
          AND id NOT IN ( SELECT MAX(id) FROM som_setl_intf_bas_info GROUP BY mdtrt_id HAVING COUNT(1) > 1 )
    </select>

    <select id="selectDi03DuplicateData" resultType="com.my.som.model.dataHandle.SomMedcasIntfBasInfo">
        SELECT id,mdtrt_id FROM som_chrg_item_intf
        WHERE mdtrt_id IN ( SELECT mdtrt_id FROM som_chrg_item_intf GROUP BY mdtrt_id HAVING COUNT(1) > 1 )
          AND id NOT IN ( SELECT MAX(id) FROM som_chrg_item_intf GROUP BY mdtrt_id HAVING COUNT(1) > 1 )
    </select>

    <select id="selectInfo" resultType="com.my.som.model.dataHandle.SomMedcasIntfBasInfo">
        select id,mdtrt_sn from som_medcas_intf_bas_info where extract_flag = 0
    </select>

    <select id="selectDi05Info" resultType="com.my.som.model.dataHandle.SomMedcasIntfBasInfo">
        select id,mdtrt_sn from som_clnc_examrpt_main
    </select>

    <select id="selectDi06Info" resultType="com.my.som.model.dataHandle.SomMedcasIntfBasInfo">
        select id,mdtrt_sn from som_clnc_test_rpot_main
    </select>

    <select id="selectDi07Info" resultType="com.my.som.model.dataHandle.SomMedcasIntfBasInfo">
        select id,mdtrt_sn from som_elec_medrcd_info
    </select>

    <select id="selectDi08Info" resultType="com.my.som.model.dataHandle.SomMedcasIntfBasInfo">
        select id,mdtrt_id from som_otp_mdtrt_info
    </select>

    <select id="selectDi01Info" resultType="com.my.som.model.dataHandle.SomMedcasIntfBasInfo">
        select id,mdtrt_id from som_setl_intf_bas_info where extract_flag = 0
    </select>

    <select id="selectDi03Info" resultType="com.my.som.model.dataHandle.SomMedcasIntfBasInfo">
        select id,mdtrt_id from som_chrg_item_intf
    </select>

    <insert id="insertDi03">
        INSERT INTO som_chrg_item_intf
        (
        mdtrt_id,
        psn_no,
        psn_name,
        psn_cert_type,
        certno,
        gend,
        naty,
        brdy,
        age,
        insutype,
        psn_type,
        cvlserv_flag,
        setl_time,
        dtrt_cert_type,
        med_type,
        medfee_sumamt,
        fulamt_ownpay_amt,
        overlmt_selfpay,
        preselfpay_amt,
        inscp_scp_amt,
        act_pay_dedc,
        hifp_pay,
        pool_prop_selfpay,
        cvlserv_pay,
        hifes_pay,
        hifmi_pay,
        hifob_pay,
        maf_pay,
        oth_pay,
        fund_pay_sumamt,
        psn_part_amt,
        acct_pay,
        psn_cash_pay,
        hosp_part_amt,
        balc,
        acct_mulaid_pay,
        medins_setl_id,
        clr_optins,
        clr_way,
        clr_type,
        hifdm_pay,extract_flag
        ) VALUES
        <foreach collection="di" item="item" index="index" separator=",">
            (
            #{item.mdtrt_id,jdbcType=VARCHAR},#{item.psn_no,jdbcType=VARCHAR},#{item.psn_name,jdbcType=VARCHAR},#{item.psn_cert_type,jdbcType=VARCHAR},#{item.certno,jdbcType=VARCHAR},
            #{item.gend,jdbcType=VARCHAR},#{item.naty,jdbcType=VARCHAR},#{item.brdy,jdbcType=VARCHAR},#{item.age,jdbcType=VARCHAR},#{item.insutype,jdbcType=VARCHAR},
            #{item.psn_type,jdbcType=VARCHAR},#{item.cvlserv_flag,jdbcType=VARCHAR},#{item.setl_time,jdbcType=VARCHAR},#{item.dtrt_cert_type,jdbcType=VARCHAR},#{item.med_type,jdbcType=VARCHAR},
            #{item.medfee_sumamt,jdbcType=VARCHAR},#{item.fulamt_ownpay_amt,jdbcType=VARCHAR},#{item.overlmt_selfpay,jdbcType=VARCHAR},#{item.preselfpay_amt,jdbcType=VARCHAR},#{item.inscp_scp_amt,jdbcType=VARCHAR},
            #{item.act_pay_dedc,jdbcType=VARCHAR},#{item.hifp_pay,jdbcType=VARCHAR},#{item.pool_prop_selfpay,jdbcType=DOUBLE},#{item.cvlserv_pay,jdbcType=VARCHAR},#{item.hifes_pay,jdbcType=VARCHAR},
            #{item.hifmi_pay,jdbcType=VARCHAR},#{item.hifob_pay,jdbcType=VARCHAR},#{item.maf_pay,jdbcType=VARCHAR},#{item.oth_pay,jdbcType=VARCHAR},#{item.fund_pay_sumamt,jdbcType=VARCHAR},
            #{item.psn_part_amt,jdbcType=VARCHAR},#{item.acct_pay,jdbcType=VARCHAR},#{item.psn_cash_pay,jdbcType=VARCHAR},#{item.hosp_part_amt,jdbcType=VARCHAR},#{item.balc,jdbcType=VARCHAR},
            #{item.acct_mulaid_pay,jdbcType=VARCHAR},#{item.medins_setl_id,jdbcType=VARCHAR},#{item.clr_optins,jdbcType=VARCHAR},#{item.clr_way,jdbcType=VARCHAR},#{item.clr_type,jdbcType=VARCHAR},
            #{item.hifdm_pay,jdbcType=VARCHAR},#{item.extract_flag,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>

    <insert id="insertDi11Detail">
        INSERT INTO som_chrg_detl_intf
            (
                feedetl_sn,
                init_feedetl_sn,
                unique_id,
                mdtrt_id,
                drord_no,
                psn_no,
                med_type,
                fee_ocur_time,
                med_list_codg,
                medins_list_codg,
                det_item_fee_sumamt,
                cnt,
                pric,
                bilg_dept_codg,
                bilg_dept_name,
                bilg_dr_codg,
                bilg_dr_name,
                acord_dept_codg,
                acord_dept_name,
                orders_dr_code,
                orders_dr_name,
                hosp_appr_flag,
                tcmdrug_used_way,
                etip_flag,
                etip_hosp_code,
                dscg_tkdrug_flag,
                matn_fee_flag,
                memo,
                comb_no,
                exp_content,
                dosform_code,
                mcs_prov_code,
                batch_num
            )
        VALUES
        <foreach collection="di" item="item" index="index" separator=",">
            (
            #{item.feedetl_sn,jdbcType=VARCHAR},#{item.init_feedetl_sn,jdbcType=VARCHAR},#{item.unique_id,jdbcType=VARCHAR},#{item.mdtrt_id,jdbcType=VARCHAR},#{item.drord_no,jdbcType=VARCHAR},#{item.psn_no,jdbcType=VARCHAR},
            #{item.med_type,jdbcType=VARCHAR},#{item.fee_ocur_time,jdbcType=VARCHAR},#{item.med_list_codg,jdbcType=VARCHAR},#{item.medins_list_codg,jdbcType=VARCHAR},#{item.det_item_fee_sumamt,jdbcType=VARCHAR},
            #{item.cnt,jdbcType=VARCHAR},#{item.pric,jdbcType=VARCHAR},#{item.bilg_dept_codg,jdbcType=VARCHAR},#{item.bilg_dept_name,jdbcType=VARCHAR},#{item.bilg_dr_codg,jdbcType=VARCHAR},
            #{item.bilg_dr_name,jdbcType=VARCHAR},#{item.acord_dept_codg,jdbcType=VARCHAR},#{item.acord_dept_name,jdbcType=VARCHAR},#{item.orders_dr_code,jdbcType=VARCHAR},#{item.orders_dr_name,jdbcType=VARCHAR},
            #{item.hosp_appr_flag,jdbcType=VARCHAR},#{item.tcmdrug_used_way,jdbcType=VARCHAR},#{item.etip_flag,jdbcType=VARCHAR},#{item.etip_hosp_code,jdbcType=VARCHAR},#{item.dscg_tkdrug_flag,jdbcType=VARCHAR},
            #{item.matn_fee_flag,jdbcType=VARCHAR},#{item.memo,jdbcType=VARCHAR},#{item.comb_no,jdbcType=VARCHAR},#{item.exp_content,jdbcType=VARCHAR},#{item.dosform_code,jdbcType=VARCHAR},
            #{item.mcs_prov_code,jdbcType=VARCHAR},#{item.batchNum,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>

    <select id="selectDi03DetailInfo" resultType="com.my.som.model.dataHandle.SomMedcasIntfBasInfo">
        SELECT id FROM som_chrg_detl_intf WHERE mdtrt_id in
        <foreach collection="di" index="index" item="item" open="(" separator="," close=")">
            #{item.mdtrt_id}
        </foreach>
    </select>

    <select id="selectDi04Info" resultType="com.my.som.model.dataHandle.SomMedcasIntfBasInfo">
        SELECT id FROM som_drord_info_intf WHERE mdtrt_id in
        <foreach collection="di" index="index" item="item" open="(" separator="," close=")">
            #{item.mdtrt_id}
        </foreach>
    </select>

    <select id="selectDi05DataInfo" resultType="com.my.som.model.dataHandle.SomMedcasIntfBasInfo">
        SELECT id FROM som_clnc_examrpt_main WHERE mdtrt_sn in
        <foreach collection="di" index="index" item="item" open="(" separator="," close=")">
            #{item.mdtrt_sn}
        </foreach>
    </select>

    <select id="selectDi06DataInfo" resultType="com.my.som.model.dataHandle.SomMedcasIntfBasInfo">
        SELECT id FROM som_clnc_test_rpot_main WHERE mdtrt_sn in
        <foreach collection="di" index="index" item="item" open="(" separator="," close=")">
            #{item.mdtrt_sn}
        </foreach>
    </select>

    <select id="selectDi07DataInfo" resultType="com.my.som.model.dataHandle.SomMedcasIntfBasInfo">
        SELECT id FROM som_elec_medrcd_info WHERE mdtrt_sn in
        <foreach collection="di" index="index" item="item" open="(" separator="," close=")">
            #{item.mdtrt_sn}
        </foreach>
    </select>

    <select id="selectDi08DataInfo" resultType="com.my.som.model.dataHandle.SomMedcasIntfBasInfo">
        SELECT id FROM som_otp_mdtrt_info WHERE mdtrt_id in
        <foreach collection="di" index="index" item="item" open="(" separator="," close=")">
            #{item.mdtrt_id}
        </foreach>
    </select>

    <select id="selectDi09DataInfo" resultType="com.my.som.model.dataHandle.SomMedcasIntfBasInfo">
        SELECT id FROM som_fee_detl_info WHERE mdtrt_id in
        <foreach collection="di" index="index" item="item" open="(" separator="," close=")">
            #{item.mdtrt_id}
        </foreach>
    </select>

    <select id="selectDi10DataInfo" resultType="com.my.som.model.dataHandle.SomMedcasIntfBasInfo">
        SELECT id FROM som_hosp_dept_info WHERE hosp_dept_codg in
        <foreach collection="di" index="index" item="item" open="(" separator="," close=")">
            #{item.hosp_dept_codg}
        </foreach>
    </select>

    <!-- 获取病案首页数据 -->
    <select id="getMedicalRecord" resultMap="BaseResultMap">
        select
            <include refid="com.my.som.mapper.common.SomHospInfoMapper.medicalRecordFields" />,
            id
        FROM som_init_hi_setl_invy_med_fee_info
        WHERE SETTLE_LIST_ID IN (
            select id
            from (
                SELECT id
                from som_hi_invy_bas_info
                WHERE 1 = 1
                <if test="queryParam.logId!=null and queryParam.logId!=''">
                    AND `DATA_LOG_ID` = #{queryParam.logId}
                </if>
                <if test="queryParam.active_flag!=null and queryParam.active_flag!=''">
                    AND `ACTIVE_FLAG` = #{queryParam.active_flag}
                </if>
                LIMIT #{queryParam.start} , #{queryParam.limit}
            ) a
        )
    </select>

    <!-- 查询重复id -->
    <select id="queryRepeatIds" resultType="java.lang.String">
        <choose>
            <when test="isMulti == true">
                <choose>
                    <when test="useVisit == 1">
                        SELECT id FROM ${tabName}
                        WHERE mdtrt_id IN (
                        select mdtrt_id from (
                        SELECT mdtrt_id,batch_num FROM ${tabName} GROUP BY mdtrt_id,batch_num HAVING COUNT(1) > 1
                        ) a group by mdtrt_id having count(1) > 1
                        )
                        and batch_num not in (
                        select batch_num from (
                        SELECT id,batch_num,max(id)over() as max_id FROM ${tabName}
                        WHERE mdtrt_id IN ( SELECT mdtrt_id FROM ${tabName} GROUP BY mdtrt_id HAVING COUNT(1) > 1 )
                        ) a where id = max_id
                        )
                    </when>
                    <when test="useVisit == 0">
                        SELECT id FROM ${tabName}
                        WHERE unique_id IN (
                        select unique_id from (
                        SELECT unique_id,batch_num FROM ${tabName} GROUP BY unique_id,batch_num HAVING COUNT(1) > 1
                        ) a group by unique_id having count(1) > 1
                        )
                        and batch_num not in (
                        select batch_num from (
                        SELECT id,batch_num,max(id)over() as max_id FROM ${tabName}
                        WHERE unique_id IN ( SELECT unique_id FROM ${tabName} GROUP BY unique_id HAVING COUNT(1) > 1 )
                        ) a where id = max_id
                        )
                    </when>
                </choose>

            </when>
            <when test="isMulti == false">
                <if test='tabName == "som_medcas_intf_bas_info"'>
                    <choose>
                        <when test="useVisit == 1">
                            SELECT id FROM ${tabName}
                            WHERE mdtrt_id IN ( SELECT mdtrt_id FROM ${tabName} GROUP BY mdtrt_id HAVING COUNT(1) > 1 )
                            AND id NOT IN ( SELECT MAX(id) FROM ${tabName} GROUP BY mdtrt_id HAVING COUNT(1) > 1 )
                        </when>
                        <when test="useVisit == 0">
                            SELECT id FROM ${tabName}
                            WHERE unique_id IN ( SELECT unique_id FROM ${tabName} GROUP BY unique_id HAVING COUNT(1) > 1 )
                            AND id NOT IN ( SELECT MAX(id) FROM ${tabName} GROUP BY unique_id HAVING COUNT(1) > 1 )
                        </when>
                    </choose>

                </if>
                <if test='tabName != "som_medcas_intf_bas_info"'>
                    <choose>
                        <when test="useVisit == 1">
                            SELECT id FROM ${tabName}
                            WHERE mdtrt_id IN ( SELECT mdtrt_id FROM ${tabName} GROUP BY mdtrt_id HAVING COUNT(1) > 1 )
                            AND id NOT IN ( SELECT MAX(id) FROM ${tabName} GROUP BY mdtrt_id HAVING COUNT(1) > 1 )
                        </when>
                        <when test="useVisit == 0">
                            SELECT id FROM ${tabName}
                            WHERE unique_id IN ( SELECT unique_id FROM ${tabName} GROUP BY unique_id HAVING COUNT(1) > 1 )
                            AND id NOT IN ( SELECT MAX(id) FROM ${tabName} GROUP BY unique_id HAVING COUNT(1) > 1 )
                        </when>
                    </choose>
                </if>
            </when>
        </choose>
    </select>

    <!-- 获取id01就诊id和id对应 -->
    <select id="getDiIdMapping" resultType="com.my.som.model.dataHandle.SomMedcasIntfBasInfo">
        <if test='tabName == "som_medcas_intf_bas_info"'>
            <choose>
                <when test="useVisit == 1">
                    select id,mdtrt_id from ${tabName} where batch_num = #{batchNum,jdbcType=VARCHAR}
                </when>
                <when test="useVisit == 0">
                    select id,unique_id AS mdtrt_id from ${tabName} where batch_num = #{batchNum,jdbcType=VARCHAR}
                </when>
            </choose>
        </if>
        <if test='tabName != "som_medcas_intf_bas_info"'>
            select id,mdtrt_id from ${tabName} where batch_num = #{batchNum,jdbcType=VARCHAR}
        </if>
    </select>

    <insert id="insertDi04">
        INSERT INTO som_drord_info_intf
        (
        mdtrt_sn,unique_id,mdtrt_id,psn_no,ipt_bed_no,drord_no,
         isu_dept_code,drord_isu_time,exe_dept_code,exe_dept_name,drord_chker_name,
        drord_ptr_name,drord_grp_no,drord_type,drord_item_type_code,
        drord_item_type_name,
        drord_detl_code,
        drord_detl_name,
        medn_type_code,
        medn_type_name,
        drug_dosform,
        drug_dosform_name,
        drug_spec,
        dismed_cnt,
        dismed_cnt_unt,
        medn_use_frqu,
        medn_used_dosunt,
        drug_used_sdose,
        drug_used_idose,
        drug_used_way_code,
        drug_used_way,
        medc_days,
        medc_begntime,
        medc_endtime,
        skintst_dicm,
        tcmherb_foote,
        drord_endtime,
        ipt_dept_code,
        medins_orgcode,
        unif_purc_drug_flag,
        drug_mgt_plaf_code,
        drug_purc_code,
        bas_medn_flag,
        vali_flag,
        medcas_drord_detl_id,
         batch_num
        ) VALUES
        <foreach collection="di" item="item" index="index" separator=",">
            (
            #{item.mdtrt_sn,jdbcType=VARCHAR},#{item.unique_id,jdbcType=VARCHAR},#{item.mdtrt_id,jdbcType=VARCHAR},#{item.psn_no,jdbcType=VARCHAR},#{item.ipt_bed_no,jdbcType=VARCHAR},#{item.drord_no,jdbcType=VARCHAR},
            #{item.isu_dept_code,jdbcType=VARCHAR},#{item.drord_isu_time,jdbcType=VARCHAR},#{item.exe_dept_code,jdbcType=VARCHAR},#{item.exe_dept_name,jdbcType=VARCHAR},#{item.drord_chker_name,jdbcType=VARCHAR},
            #{item.drord_ptr_name,jdbcType=VARCHAR},#{item.drord_grp_no,jdbcType=VARCHAR},#{item.drord_type,jdbcType=VARCHAR},#{item.drord_item_type_code,jdbcType=VARCHAR},#{item.drord_item_type_name,jdbcType=VARCHAR},
            #{item.drord_detl_code,jdbcType=VARCHAR},#{item.drord_detl_name,jdbcType=VARCHAR},#{item.medn_type_code,jdbcType=VARCHAR},#{item.medn_type_name,jdbcType=VARCHAR},#{item.drug_dosform,jdbcType=VARCHAR},
            #{item.drug_dosform_name,jdbcType=VARCHAR},#{item.drug_spec,jdbcType=VARCHAR},#{item.dismed_cnt,jdbcType=VARCHAR},#{item.dismed_cnt_unt,jdbcType=VARCHAR},#{item.medn_use_frqu,jdbcType=VARCHAR},
            #{item.medn_used_dosunt,jdbcType=VARCHAR},#{item.drug_used_sdose,jdbcType=VARCHAR},#{item.drug_used_idose,jdbcType=VARCHAR},#{item.drug_used_way_code,jdbcType=VARCHAR},#{item.drug_used_way,jdbcType=VARCHAR},
            #{item.medc_days,jdbcType=VARCHAR},#{item.medc_begntime,jdbcType=VARCHAR},#{item.medc_endtime,jdbcType=VARCHAR},#{item.skintst_dicm,jdbcType=VARCHAR},#{item.tcmherb_foote,jdbcType=VARCHAR},
            #{item.drord_endtime,jdbcType=VARCHAR},#{item.ipt_dept_code,jdbcType=VARCHAR},#{item.medins_orgcode,jdbcType=VARCHAR},#{item.unif_purc_drug_flag,jdbcType=VARCHAR},#{item.drug_mgt_plaf_code,jdbcType=VARCHAR},
            #{item.drug_purc_code,jdbcType=VARCHAR},#{item.bas_medn_flag,jdbcType=VARCHAR},#{item.vali_flag,jdbcType=VARCHAR},#{item.medcas_drord_detl_id,jdbcType=VARCHAR},#{item.batchNum,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>

    <insert id="insertDi05">
        INSERT INTO som_clnc_examrpt_main
        (
         mdtrt_sn,mdtrt_id,psn_no,appy_no,appy_doc_name,
         rpotc_no,rpotc_type_code,exam_rpotc_name,exam_date,rpt_date,
         cma_date,sapl_date,spcm_no,spcm_name,exam_type_code,
         exam_item_code,exam_type_name,exam_item_name,inhosp_exam_item_code,inhosp_exam_item_name,
         exam_part,exam_rslt_poit_flag,exam_rslt_abn,exam_ccls,appy_org_name,
         appy_dept_code,exam_dept_code,ipt_dept_code,ipt_dept_name,bilg_dr_codg,
         bilg_dr_name,exe_org_name,vali_flag, batch_num
        ) VALUES
        <foreach collection="di" item="item" index="index" separator=",">
            (
             #{item.mdtrt_sn},#{item.mdtrt_id},#{item.psn_no},#{item.appy_no},#{item.appy_doc_name},
             #{item.rpotc_no},#{item.rpotc_type_code},#{item.exam_rpotc_name},#{item.exam_date},#{item.rpt_date},
             #{item.cma_date},#{item.sapl_date},#{item.spcm_no},#{item.spcm_name},#{item.exam_type_code},
             #{item.exam_item_code},#{item.exam_type_name},#{item.exam_item_name},#{item.inhosp_exam_item_code},#{item.inhosp_exam_item_name},
             #{item.exam_part},#{item.exam_rslt_poit_flag},#{item.exam_rslt_abn},#{item.exam_ccls},#{item.appy_org_name},
             #{item.appy_dept_code},#{item.exam_dept_code},#{item.ipt_dept_code},#{item.ipt_dept_name},#{item.bilg_dr_codg},
             #{item.bilg_dr_name},#{item.exe_org_name},#{item.vali_flag}, #{item.batchNum,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>

    <insert id="insertDi05CheckItem">
        INSERT INTO som_exam_item_info
        (
         appy_no,
         rpotc_no,
         exam_item_code,
         exam_item_name,
         inhosp_exam_item_code,
         inhosp_exam_item_name,
         exam_charge,
         mdtrt_id,
         di05_id
        ) VALUES
        <foreach collection="di" item="item" index="index" separator=",">
            (
             #{item.appy_no},
             #{item.rpotc_no},
             #{item.exam_item_code},
             #{item.exam_item_name},
             #{item.inhosp_exam_item_code},
             #{item.inhosp_exam_item_name},
             #{item.exam_charge},
             #{item.mdtrt_id},
             #{item.di05_id}
            )
        </foreach>
    </insert>

    <insert id="insertDi05CheckSpecimen">
        INSERT INTO som_exam_spcm_info
        (
         rpotc_no,
         appy_no,
         sapl_date,
         spcm_no,
         spcm_name,
         mdtrt_id,
        di05_id
        ) VALUES
        <foreach collection="di" item="item" index="index" separator=",">
            (
             #{item.rpotc_no},
             #{item.appy_no},
             #{item.sapl_date},
             #{item.spcm_no},
             #{item.spcm_name},
             #{item.mdtrt_id},
            #{item.di05_id}
            )
        </foreach>
    </insert>

    <insert id="insertDi05ImageInspection">
        INSERT INTO som_exam_img_info
        (
         rpotc_no,
         study_uid,
         patient_id,
         patient_name,
         acession_no,
         study_time,
         modality,
         store_path,
         series_count,
         image_count,
         mdtrt_id,
         di05_id
        ) VALUES
        <foreach collection="di" item="item" index="index" separator=",">
            (
             #{item.rpotc_no},
             #{item.study_uid},
             #{item.patient_id},
             #{item.patient_name},
             #{item.acession_no},
             #{item.study_time},
             #{item.modality},
             #{item.store_path},
             #{item.series_count},
             #{item.image_count},
             #{item.mdtrt_id},
             #{item.di05_id}
            )
        </foreach>
    </insert>

    <insert id="insertDi06">
        INSERT INTO som_clnc_test_rpot_main
        (
         mdtrt_sn,
         mdtrt_id,
         psn_no,
         appy_no,
         appy_org_code,
         appy_org_name,
         bilg_dr_codg,
         bilg_dr_name,
         exam_org_code,
         exam_org_name,
         appy_dept_code,
         exam_dept_code,
         exam_mtd,
         rpotc_no,
         exam_item_code,
         exam_item_name,
         inhosp_exam_item_code,
         inhosp_exam_item_name,
         rpt_date,
         rpot_doc,
         exam_charge,
         vali_flag,
         batch_num
        ) VALUES
        <foreach collection="di" item="item" index="index" separator=",">
            (
             #{item.mdtrt_sn},
             #{item.mdtrt_id},
             #{item.psn_no},
             #{item.appy_no},
             #{item.appy_org_code},
             #{item.appy_org_name},
             #{item.bilg_dr_codg},
             #{item.bilg_dr_name},
             #{item.exam_org_code},
             #{item.exam_org_name},
             #{item.appy_dept_code},
             #{item.exam_dept_code},
             #{item.exam_mtd},
             #{item.rpotc_no},
             #{item.exam_item_code},
             #{item.exam_item_name},
             #{item.inhosp_exam_item_code},
             #{item.inhosp_exam_item_name},
             #{item.rpt_date},
             #{item.rpot_doc},
             #{item.exam_charge},
             #{item.vali_flag},
             #{item.batchNum,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>

    <insert id="insertDi06CheckItem">
        INSERT INTO som_test_detl_info
        (
         rpotc_no,appy_no,exam_mtd,ref_val,exam_unt,
         exam_rslt_val,exam_rslt_dicm,exam_item_detl_code,exam_item_detl_name,exam_rslt_abn,
         mdtrt_id,di06_id
        ) VALUES
        <foreach collection="di" item="item" index="index" separator=",">
            (
             #{item.rpotc_no},#{item.appy_no},#{item.exam_mtd},#{item.ref_val},#{item.exam_unt},
             #{item.exam_rslt_val},#{item.exam_rslt_dicm},#{item.exam_item_detl_code},#{item.exam_item_detl_name},#{item.exam_rslt_abn},
             #{item.mdtrt_id},
             #{item.di06_id}
            )
        </foreach>
    </insert>

    <insert id="insertDi06CheckSpecimen">
        INSERT INTO som_test_spcm_info
        (
         rpotc_no,
         appy_no,
         sapl_date,
         spcm_no,
         spcm_name,
         mdtrt_id,
         di06_id
        ) VALUES
        <foreach collection="di" item="item" index="index" separator=",">
            (
             #{item.rpotc_no},
             #{item.appy_no},
             #{item.sapl_date},
             #{item.spcm_no},
             #{item.spcm_name},
             #{item.mdtrt_id},
             #{item.di06_id}
            )
        </foreach>
    </insert>

    <insert id="insertDi07">
        INSERT INTO som_elec_medrcd_info
        (
         mdtrt_sn,mdtrt_id,psn_no,mdtrtsn,name,
         gend,age,adm_rec_no,wardarea_name,dept_code,
         dept_name,bedno,adm_time,illhis_stte_name,illhis_stte_rltl,
         stte_rele,chfcomp,dise_now,hlcon,dise_his,
         ifet,ifet_his,prev_vcnt,oprn_his,bld_his,
         algs_his,psn_his,mrg_his,mena_his,fmhis,
         physexm_tprt,physexm_pule,physexm_vent_frqu,physexm_systolic_pre,physexm_dstl_pre,
         physexm_height,physexm_wt,physexm_ordn_stas,physexm_skin_musl,physexm_spef_lymph,
         physexm_head,physexm_neck,physexm_chst,physexm_abd,physexm_finger_exam,
         physexm_genital_area,physexm_spin,physexm_all_fors,nersys,spcy_info,
         asst_exam_rslt,tcm4d_rslt,syddclft,syddclft_name,prnp_trt,
         rec_doc_code,rec_doc_name,ipdr_code,ipdr_name,chfdr_code,
         chfdr_name,chfpdr_code,chfpdr_name,main_symp,adm_rea,
         adm_way,apgr,diet_info,growth_deg,mtl_stas_norm,
         slep_info,sp_info,mind_info,nurt,self_ablt,
         nurscare_obsv_item_name,nurscare_obsv_rslt,smoke,stop_smok_days,smok_info,
         smok_day,drnk,drnk_frqu,drnk_day,eval_time,
         resp_nurs_name,vali_flag, batch_num
        ) VALUES
        <foreach collection="di" item="item" index="index" separator=",">
            (
             #{item.mdtrt_sn},#{item.mdtrt_id},#{item.psn_no},#{item.mdtrtsn},#{item.name},
             #{item.gend},#{item.age},#{item.adm_rec_no},#{item.wardarea_name},#{item.dept_code},
             #{item.dept_name},#{item.bedno},#{item.adm_time},#{item.illhis_stte_name},#{item.illhis_stte_rltl},
             #{item.stte_rele},#{item.chfcomp},#{item.dise_now},#{item.hlcon},#{item.dise_his},
             #{item.ifet},#{item.ifet_his},#{item.prev_vcnt},#{item.oprn_his},#{item.bld_his},
             #{item.algs_his},#{item.psn_his},#{item.mrg_his},#{item.mena_his},#{item.fmhis},
             #{item.physexm_tprt},#{item.physexm_pule},#{item.physexm_vent_frqu},#{item.physexm_systolic_pre},#{item.physexm_dstl_pre},
             #{item.physexm_height},#{item.physexm_wt},#{item.physexm_ordn_stas},#{item.physexm_skin_musl},#{item.physexm_spef_lymph},
             #{item.physexm_head},#{item.physexm_neck},#{item.physexm_chst},#{item.physexm_abd},#{item.physexm_finger_exam},
             #{item.physexm_genital_area},#{item.physexm_spin},#{item.physexm_all_fors},#{item.nersys},#{item.spcy_info},
             #{item.asst_exam_rslt},#{item.tcm4d_rslt},#{item.syddclft},#{item.syddclft_name},#{item.prnp_trt},
             #{item.rec_doc_code},#{item.rec_doc_name},#{item.ipdr_code},#{item.ipdr_name},#{item.chfdr_code},
             #{item.chfdr_name},#{item.chfpdr_code},#{item.chfpdr_name},#{item.main_symp},#{item.adm_rea},
             #{item.adm_way},#{item.apgr},#{item.diet_info},#{item.growth_deg},#{item.mtl_stas_norm},
             #{item.slep_info},#{item.sp_info},#{item.mind_info},#{item.nurt},#{item.self_ablt},
             #{item.nurscare_obsv_item_name},#{item.nurscare_obsv_rslt},#{item.smoke},#{item.stop_smok_days},#{item.smok_info},
             #{item.smok_day},#{item.drnk},#{item.drnk_frqu},#{item.drnk_day},#{item.eval_time},
             #{item.resp_nurs_name},#{item.vali_flag}, #{item.batchNum,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>

    <insert id="insertDi07Diagnosis">
        INSERT INTO som_elec_medrcd_diag_info
        (
         inout_diag_type,
         maindiag_flag,
         diag_seq,
         diag_time,
         wm_diag_code,
         wm_diag_name,
         tcm_dise_code,
         tcm_dise_name,
         tcmsymp_code,
         tcmsymp,
         vali_flag,
         mdtrt_id,
         di07_id
        ) VALUES
        <foreach collection="di" item="item" index="index" separator=",">
            (
            #{item.inout_diag_type},
            #{item.maindiag_flag},
            #{item.diag_seq},
            #{item.diag_time},
            #{item.wm_diag_code},
            #{item.wm_diag_name},
            #{item.tcm_dise_code},
            #{item.tcm_dise_name},
            #{item.tcmsymp_code},
            #{item.tcmsymp},
            #{item.vali_flag},
            #{item.mdtrt_id},
            #{item.di07_id}
            )
        </foreach>
    </insert>

    <insert id="insertDi07CourseRecord">
        INSERT INTO som_elec_medrcd_codse_rcd
        (
         dept_code,
         dept_name,
         wardarea_name,
         bedno,
         rcd_time,
         chfcomp,
         cas_ftur,
         tcm4d_rslt,
         dise_evid,
         prel_wm_diag_code,
         prel_wm_dise_name,
         prel_tcm_diag_code,
         prel_tcm_dise_name,
         prel_tcmsymp_code,
         prel_tcmsymp,
         finl_wm_diag_code,
         finl_wm_diag_name,
         finl_tcm_dise_code,
         finl_tcm_dise_name,
         finl_tcmsymp_code,
         finl_tcmsymp,
         dise_plan,
         prnp_trt,
         ipdr_code,
         ipdr_name,
         prnt_doc_name,
         vali_flag,
         mdtrt_id,
        di07_id
        ) VALUES
        <foreach collection="di" item="item" index="index" separator=",">
            (
             #{item.dept_code},
             #{item.dept_name},
             #{item.wardarea_name},
             #{item.bedno},
             #{item.rcd_time},
             #{item.chfcomp},
             #{item.cas_ftur},
             #{item.tcm4d_rslt},
             #{item.dise_evid},
             #{item.prel_wm_diag_code},
             #{item.prel_wm_dise_name},
             #{item.prel_tcm_diag_code},
             #{item.prel_tcm_dise_name},
             #{item.prel_tcmsymp_code},
             #{item.prel_tcmsymp},
             #{item.finl_wm_diag_code},
             #{item.finl_wm_diag_name},
             #{item.finl_tcm_dise_code},
             #{item.finl_tcm_dise_name},
             #{item.finl_tcmsymp_code},
             #{item.finl_tcmsymp},
             #{item.dise_plan},
             #{item.prnp_trt},
             #{item.ipdr_code},
             #{item.ipdr_name},
             #{item.prnt_doc_name},
             #{item.vali_flag},
             #{item.mdtrt_id},
            #{item.di07_id}
            )
        </foreach>
    </insert>

    <insert id="insertDi07Operation">
        INSERT INTO som_elec_medrcd_oprn_rcd
        (
         oprn_appy_id,
         oprn_seq,
         blotype_abo,
         oprn_time,
         oprn_type_code,
         oprn_type_name,
         bfpn_diag_code,
         bfpn_oprn_diag_name,
         bfpn_inhosp_ifet,
         afpn_diag_code,
         afpn_diag_name,
         sinc_heal_lv,
         sinc_heal_lv_code,
         back_oprn,
         selv,
         prev_abtl_medn,
         abtl_medn_days,
         oprn_oprt_code,
         oprn_oprt_name,
         oprn_lv_code,
         oprn_lv_name,
         anst_mtd_code,
         anst_mtd_name,
         anst_lv_code,
         anst_lv_name,
         exe_anst_dept_code,
         exe_anst_dept_name,
         anst_efft,
         oprn_begntime,
         oprn_endtime,
         oprn_asps,
         oprn_asps_ifet,
         afpn_info,
         oprn_merg,
         oprn_conc,
         oprn_anst_dept_code,
         oprn_anst_dept_name,
         palg_dise,
         oth_med_dspo,
         out_std_oprn_time,
         oprn_oper_name,
         oprn_asit_name1,
         oprn_asit_name2,
         anst_dr_name,
         anst_asa_lv_code,
         anst_asa_lv_name,
         anst_medn_code,
         anst_medn_name,
         anst_medn_dos,
         anst_dosunt,
         anst_begntime,
         anst_endtime,
         anst_merg_symp_code,
         anst_merg_symp,
         anst_merg_symp_dscr,
         pacu_begntime,
         pacu_endtime,
         oprn_selv,
         canc_oprn,
         vali_flag,
         mdtrt_id,
        di07_id
        ) VALUES
        <foreach collection="di" item="item" index="index" separator=",">
            (
             #{item.oprn_appy_id},
             #{item.oprn_seq},
             #{item.blotype_abo},
             #{item.oprn_time},
             #{item.oprn_type_code},
             #{item.oprn_type_name},
             #{item.bfpn_diag_code},
             #{item.bfpn_oprn_diag_name},
             #{item.bfpn_inhosp_ifet},
             #{item.afpn_diag_code},
             #{item.afpn_diag_name},
             #{item.sinc_heal_lv},
             #{item.sinc_heal_lv_code},
             #{item.back_oprn},
             #{item.selv},
             #{item.prev_abtl_medn},
             #{item.abtl_medn_days},
             #{item.oprn_oprt_code},
             #{item.oprn_oprt_name},
             #{item.oprn_lv_code},
             #{item.oprn_lv_name},
             #{item.anst_mtd_code},
             #{item.anst_mtd_name},
             #{item.anst_lv_code},
             #{item.anst_lv_name},
             #{item.exe_anst_dept_code},
             #{item.exe_anst_dept_name},
             #{item.anst_efft},
             #{item.oprn_begntime},
             #{item.oprn_endtime},
             #{item.oprn_asps},
             #{item.oprn_asps_ifet},
             #{item.afpn_info},
             #{item.oprn_merg},
             #{item.oprn_conc},
             #{item.oprn_anst_dept_code},
             #{item.oprn_anst_dept_name},
             #{item.palg_dise},
             #{item.oth_med_dspo},
             #{item.out_std_oprn_time},
             #{item.oprn_oper_name},
             #{item.oprn_asit_name1},
             #{item.oprn_asit_name2},
             #{item.anst_dr_name},
             #{item.anst_asa_lv_code},
             #{item.anst_asa_lv_name},
             #{item.anst_medn_code},
             #{item.anst_medn_name},
             #{item.anst_medn_dos},
             #{item.anst_dosunt},
             #{item.anst_begntime},
             #{item.anst_endtime},
             #{item.anst_merg_symp_code},
             #{item.anst_merg_symp},
             #{item.anst_merg_symp_dscr},
             #{item.pacu_begntime},
             #{item.pacu_endtime},
             #{item.oprn_selv},
             #{item.canc_oprn},
             #{item.vali_flag},
             #{item.mdtrt_id},
            #{item.di07_id}
            )
        </foreach>
    </insert>

    <insert id="insertDi07Resuce">
        INSERT INTO som_elec_medrcd_cond_resc_rcd
        (
         dept,
         dept_name,
         wardarea_name,
         bedno,
         diag_name,
         diag_code,
         cond_chg,
         resc_mes,
         oprn_oprt_code,
         oprn_oprt_name,
         oprn_oper_part,
         itvt_name,
         oprt_mtd,
         oprt_cnt,
         resc_begntime,
         resc_endtime,
         dise_item_name,
         dise_ccls,
         dise_ccls_qunt,
         dise_ccls_code,
         mnan,
         resc_psn_list,
         proftechttl_code,
         doc_code,
         dr_name,
         vali_flag,
         mdtrt_id,
        di07_id
        ) VALUES
        <foreach collection="di" item="item" index="index" separator=",">
            (
             #{item.dept},
             #{item.dept_name},
             #{item.wardarea_name},
             #{item.bedno},
             #{item.diag_name},
             #{item.diag_code},
             #{item.cond_chg},
             #{item.resc_mes},
             #{item.oprn_oprt_code},
             #{item.oprn_oprt_name},
             #{item.oprn_oper_part},
             #{item.itvt_name},
             #{item.oprt_mtd},
             #{item.oprt_cnt},
             #{item.resc_begntime},
             #{item.resc_endtime},
             #{item.dise_item_name},
             #{item.dise_ccls},
             #{item.dise_ccls_qunt},
             #{item.dise_ccls_code},
             #{item.mnan},
             #{item.resc_psn_list},
             #{item.proftechttl_code},
             #{item.doc_code},
             #{item.dr_name},
             #{item.vali_flag},
             #{item.mdtrt_id},
            #{item.di07_id}
            )
        </foreach>
    </insert>

    <insert id="insertDi07Obituary">
        INSERT INTO som_elec_medrcd_die_rcd
        (
         dept,
         dept_name,
         wardarea_name,
         bedno,
         adm_time,
         adm_dise,
         adm_info,
         trt_proc_dscr,
         die_time,
         die_drt_rea,
         die_drt_rea_code,
         die_dise_name,
         die_diag_code,
         agre_corp_dset,
         ipdr_name,
         chfpdr_code,
         chfpdr_name,
         chfdr_name,
         sign_time,
         vali_flag,
         mdtrt_id,
        di07_id
        ) VALUES
        <foreach collection="di" item="item" index="index" separator=",">
            (
             #{item.dept},
             #{item.dept_name},
             #{item.wardarea_name},
             #{item.bedno},
             #{item.adm_time},
             #{item.adm_dise},
             #{item.adm_info},
             #{item.trt_proc_dscr},
             #{item.die_time},
             #{item.die_drt_rea},
             #{item.die_drt_rea_code},
             #{item.die_dise_name},
             #{item.die_diag_code},
             #{item.agre_corp_dset},
             #{item.ipdr_name},
             #{item.chfpdr_code},
             #{item.chfpdr_name},
             #{item.chfdr_name},
             #{item.sign_time},
             #{item.vali_flag},
             #{item.mdtrt_id},
            #{item.di07_id}
            )
        </foreach>
    </insert>

    <insert id="insertDi07OutHos">
        INSERT INTO som_elec_medrcd_dscg_sumy
        (
         dscg_date,
         adm_diag_dscr,
         dscg_dise_dscr,
         adm_info,
         trt_proc_rslt_dscr,
         dscg_info,
         dscg_drord,
         caty,
         rec_doc,
         main_drug_name,
         oth_imp_info,
         vali_flag,
         mdtrt_id,
        di07_id
        ) VALUES
        <foreach collection="di" item="item" index="index" separator=",">
            (
             #{item.dscg_date},
             #{item.adm_diag_dscr},
             #{item.dscg_dise_dscr},
             #{item.adm_info},
             #{item.trt_proc_rslt_dscr},
             #{item.dscg_info},
             #{item.dscg_drord},
             #{item.caty},
             #{item.rec_doc},
             #{item.main_drug_name},
             #{item.oth_imp_info},
             #{item.vali_flag},
             #{item.mdtrt_id},
            #{item.di07_id}
            )
        </foreach>
    </insert>

    <insert id="insertDi08">
        INSERT INTO som_otp_mdtrt_info
        (
         mdtrt_id,
         psn_no,
         med_type,
         begntime,
         main_cond_dscr,
         dise_codg,
         dise_name,
         birctrl_type,
         birctrl_matn_date,
         matn_type,
         geso_val
        ) VALUES
        <foreach collection="di" item="item" index="index" separator=",">
            (
             #{item.mdtrt_id},
             #{item.psn_no},
             #{item.med_type},
             #{item.begntime},
             #{item.main_cond_dscr},
             #{item.dise_codg},
             #{item.dise_name},
             #{item.birctrl_type},
             #{item.birctrl_matn_date},
             #{item.matn_type},
             #{item.geso_val}
            )
        </foreach>
    </insert>

    <insert id="insertDi08Diagnosis">
        INSERT INTO som_otp_mdtrt_diag_info
        (
         diag_type,
         diag_srt_no,
         diag_code,
         diag_name,
         diag_dept,
         dise_dor_no,
         dise_dor_name,
         diag_time,
         vali_flag,
         mdtrt_id,
         di08_id
        ) VALUES
        <foreach collection="di" item="item" index="index" separator=",">
            (
             #{item.diag_type},
             #{item.diag_srt_no},
             #{item.diag_code},
             #{item.diag_name},
             #{item.diag_dept},
             #{item.dise_dor_no},
             #{item.dise_dor_name},
             #{item.diag_time},
             #{item.vali_flag},
             #{item.mdtrt_id},
             #{item.di08_id}
            )
        </foreach>
    </insert>

    <insert id="insertDi09">
        INSERT INTO som_fee_detl_info
        (
         feedetl_sn,
         mdtrt_id,
         psn_no,
         chrg_bchno,
         dise_codg,
         rxno,
         rx_circ_flag,
         fee_ocur_time,
         med_list_codg,
         medins_list_codg,
         det_item_fee_sumamt,
         cnt,
         pric,
         sin_dos_dscr,
         used_frqu_dscr,
         prd_days,
         medc_way_dscr,
         bilg_dept_codg,
         bilg_dept_name,
         bilg_dr_codg,
         bilg_dr_name,
         acord_dept_codg,
         acord_dept_name,
         orders_dr_code,
         orders_dr_name,
         hosp_appr_flag,
         tcmdrug_used_way,
         etip_flag,
         etip_hosp_code,
         dscg_tkdrug_flag,
         matn_fee_flag,
         dosform_code,
         mcs_prov_code
        ) VALUES
        <foreach collection="di" item="item" index="index" separator=",">
            (
            #{item.feedetl_sn},
            #{item.mdtrt_id},
            #{item.psn_no},
            #{item.chrg_bchno},
            #{item.dise_codg},
            #{item.rxno},
            #{item.rx_circ_flag},
            #{item.fee_ocur_time},
            #{item.med_list_codg},
            #{item.medins_list_codg},
            #{item.det_item_fee_sumamt},
            #{item.cnt},
            #{item.pric},
            #{item.sin_dos_dscr},
            #{item.used_frqu_dscr},
            #{item.prd_days},
            #{item.medc_way_dscr},
            #{item.bilg_dept_codg},
            #{item.bilg_dept_name},
            #{item.bilg_dr_codg},
            #{item.bilg_dr_name},
            #{item.acord_dept_codg},
            #{item.acord_dept_name},
            #{item.orders_dr_code},
            #{item.orders_dr_name},
            #{item.hosp_appr_flag},
            #{item.tcmdrug_used_way},
            #{item.etip_flag},
            #{item.etip_hosp_code},
            #{item.dscg_tkdrug_flag},
            #{item.matn_fee_flag},
            #{item.dosform_code},
            #{item.mcs_prov_code}
            )
        </foreach>
    </insert>

    <insert id="insertDi10">
        INSERT INTO som_hosp_dept_info
        (
         hosp_dept_codg,
         caty,
         hosp_dept_name,
         begntime,
         endtime,
         itro,
         dept_resper_name,
         dept_resper_tel,
         dept_med_serv_scp,
         dept_estbdat,
         aprv_bed_cnt,
         hi_crtf_bed_cnt,
         poolarea_no,
         dr_psncnt,
         phar_psncnt,
         nurs_psncnt,
         tecn_psncnt,
         memo
        ) VALUES
        <foreach collection="di" item="item" index="index" separator=",">
            (
             #{item.hosp_dept_codg},
             #{item.caty},
             #{item.hosp_dept_name},
             #{item.begntime},
             #{item.endtime},
             #{item.itro},
             #{item.dept_resper_name},
             #{item.dept_resper_tel},
             #{item.dept_med_serv_scp},
             #{item.dept_estbdat},
             #{item.aprv_bed_cnt},
             #{item.hi_crtf_bed_cnt},
             #{item.poolarea_no},
             #{item.dr_psncnt},
             #{item.phar_psncnt},
             #{item.nurs_psncnt},
             #{item.tecn_psncnt},
             #{item.memo}
            )
        </foreach>
    </insert>
    <insert id="insertDi11DetailJson">
        INSERT INTO som_hosp_dept_info
        (
        hosp_dept_codg,
        caty,
        hosp_dept_name,
        begntime,
        endtime,
        itro,
        dept_resper_name,
        dept_resper_tel,
        dept_med_serv_scp,
        dept_estbdat,
        aprv_bed_cnt,
        hi_crtf_bed_cnt,
        poolarea_no,
        dr_psncnt,
        phar_psncnt,
        nurs_psncnt,
        tecn_psncnt,
        memo
        ) VALUES
        <foreach collection="di" item="item" index="index" separator=",">
        (
        #{item.hosp_dept_codg},
        #{item.caty},
        #{item.hosp_dept_name},
        #{item.begntime},
        #{item.endtime},
        #{item.itro},
        #{item.dept_resper_name},
        #{item.dept_resper_tel},
        #{item.dept_med_serv_scp},
        #{item.dept_estbdat},
        #{item.aprv_bed_cnt},
        #{item.hi_crtf_bed_cnt},
        #{item.poolarea_no},
        #{item.dr_psncnt},
        #{item.phar_psncnt},
        #{item.nurs_psncnt},
        #{item.tecn_psncnt},
        #{item.memo}
        )
        </foreach>
    </insert>

    <select id="queryIdsToK00s" resultType="com.my.som.vo.common.SettleListVo">
        SELECT ID AS id,
               K00 AS k00
        FROM som_hi_invy_bas_info
        WHERE K00 IN
        (
            <foreach collection="list" item="k00" separator=",">
                #{k00}
            </foreach>
        )
    </select>

    <delete id="deleteDi03RepeatData">
        DELETE FROM som_chrg_item_intf
        WHERE id NOT IN
              (
                  SELECT a.id
                  FROM
                       (
                           SELECT MAX(id) AS id
                           FROM som_chrg_item_intf
                           WHERE mdtrt_id IN
                                 (
                                     <foreach collection="list" item="item" separator=",">
                                        #{item.k00}
                                     </foreach>
                                 )
                           GROUP BY mdtrt_id
                       ) a
              )
          AND mdtrt_id IN
              (
                  <foreach collection="list" item="item" separator=",">
                    #{item.k00}
                  </foreach>
              )
    </delete>

    <delete id="deleteBusFundPayData">
        DELETE FROM som_fund_pay
        WHERE hi_setl_invy_id IN
              (
                  <foreach collection="list" item="item" separator=",">
                      #{item.id}
                  </foreach>
              )
    </delete>

    <select id="queryDi03Data" resultType="java.util.HashMap">
        SELECT distinct mdtrt_id FROM som_chrg_item_intf
        WHERE extract_flag = '0'
    </select>

    <update id="updateDi03">
        UPDATE som_chrg_item_intf
        SET extract_flag = '1'
        WHERE mdtrt_id IN
              (
                  <foreach collection="list" item="item" separator=",">
                      #{item.k00}
                  </foreach>
              )
    </update>

    <select id="queryDi03DetailData" resultType="java.util.HashMap">
        SELECT * FROM som_chrg_item_intf
        WHERE mdtrt_id IN
              (
                  <foreach collection="list" item="item" separator=",">
                      #{item.k00,jdbcType=VARCHAR}
                  </foreach>
              )
    </select>
    <select id="querySettleListRepeatIds" resultType="java.lang.String">
        SELECT GROUP_CONCAT(id) as ids FROM som_hi_invy_bas_info WHERE ACTIVE_FLAG='1' GROUP BY K00 HAVING COUNT(1) > 1
    </select>

    <select id="queryBasicInfo" resultType="java.util.HashMap">
        SELECT id,
        <include refid="busSettleListFields" />
        FROM som_hi_invy_bas_info
        WHERE K00 = #{s}
        ORDER BY id
        LIMIT 1
    </select>

    <select id="queryDiseaseInfo" resultType="java.util.Map">
        SELECT #{k00} AS k00, maindiag_flag,
               <include refid="com.my.som.app.mapper.appMedicalQuality.AppMedicalFirstPageInfoMapper.Base_Disease_List" />
        FROM som_diag WHERE SETTLE_LIST_ID = ${settleListId}
    </select>

    <select id="queryOperationInfo" resultType="java.util.Map">
        SELECT #{k00}  AS k00,oprn_oprt_begntime,oprn_oprt_endtime,anst_begntime,anst_endtime,
               <include refid="com.my.som.app.mapper.appMedicalQuality.AppMedicalFirstPageInfoMapper.Base_Operate_List" />
        FROM som_oprn_oprt_info WHERE SETTLE_LIST_ID = ${settleListId}
    </select>

    <select id="queryNoExtract" resultType="int">
        SELECT COUNT(1) FROM som_medcas_intf_bas_info WHERE extract_flag = '0'
    </select>

    <select id="queryNoExtractList" resultType="int">
        SELECT COUNT(1) FROM som_setl_intf_bas_info WHERE extract_flag = '0'
    </select>

    <select id="selectYb14FeeByBah" resultType="java.util.Map">
       select a.d35 ,b.yb14Fee
        from som_hi_invy_bas_info a
        left join
        ( SELECT  sum( amt ) AS yb14Fee, hi_setl_invy_id FROM `som_hi_setl_invy_med_fee_info` GROUP BY  hi_setl_invy_id ) b
        on a.id = b.hi_setl_invy_id
        where a.a48 = #{bah}
    </select>

    <delete id="deleteDi11Detail">
        delete from som_chrg_detl_intf
        where unique_id IN
        <foreach collection="uniqueIds" item="uniqueId" separator="," open="(" close=")">
            #{uniqueId,jdbcType=VARCHAR}
        </foreach>
    </delete>

</mapper>
