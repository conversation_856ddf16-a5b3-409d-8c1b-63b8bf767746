<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.som.dao.dataHandle.BusMedicalCostDao">
    <!--批量新增-->
    <insert id="insertMedicalCostData">
        INSERT INTO som_hi_setl_invy_med_fee_info (
          hi_setl_invy_id, med_chrg_itemname, amt, claa,
          clab,ownpay,oth
        ) VALUES
        <foreach collection="mcd" item="item" index="index" separator=",">
            (#{item.settle_list_id,jdbcType=BIGINT},
            #{item.med_chrg_itemname,jdbcType=VARCHAR},#{item.amt,jdbcType=DECIMAL},
            #{item.claa,jdbcType=DECIMAL},#{item.clab,jdbcType=DECIMAL},
            #{item.ownpay,jdbcType=DECIMAL},#{item.oth,jdbcType=DECIMAL}
            )
        </foreach>
    </insert>

    <insert id="insertVDrgMedicalCost">
        INSERT INTO som_drg_fee_med_chrg_type (
            K00, med_chrg_itemname, amt, claa,
            clab,ownpay,oth
        ) VALUES
        <foreach collection="mcd" item="item" index="index" separator=",">
            (#{item.k00,jdbcType=VARCHAR},
            #{item.med_chrg_itemname,jdbcType=VARCHAR},#{item.amt,jdbcType=DECIMAL},
            #{item.claa,jdbcType=DECIMAL},#{item.clab,jdbcType=DECIMAL},
            #{item.ownpay,jdbcType=DECIMAL},#{item.oth,jdbcType=DECIMAL}
            )
        </foreach>
    </insert>

    <insert id="insertMedicalType">
        INSERT INTO med_type(
            a48,b15,setlway,a46c
        )values
             (
              #{a48,jdbcType=VARCHAR},
              #{b15,jdbcType=VARCHAR},
              #{setlway,jdbcType=VARCHAR},
              #{a46c,jdbcType=VARCHAR}
             )
    </insert>

    <update id="updateSeltleList">
        update som_hi_invy_bas_info
        set
          setlway = #{setlway,jdbcType=VARCHAR}
        <if test="a46c != '' and a46c != null">
            ,a46c = #{a46c,jdbcType=VARCHAR}
        </if>
        where id = #{id}
    </update>

    <delete id="deleteMedicalType">
        delete from med_type
    </delete>

    <select id="querySeltleList" resultType="com.my.som.vo.upload.MedTypeVo">
        select b.id,a.* from
            med_type a
        inner join som_hi_invy_bas_info b
        on a.a48 = b.a48 and SUBSTR(a.b15 ,1, 10) = SUBSTR(b.b15 ,1, 10)
    </select>
</mapper>