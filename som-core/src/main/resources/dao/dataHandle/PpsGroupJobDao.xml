<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.som.dao.dataHandle.PpsGroupJobDao">
    <resultMap id="BaseResultMap" type="com.my.som.model.dataHandle.SomSetlMedcas">
        <id column="ID" jdbcType="BIGINT" property="id" />
        <result column="SETTLE_LIST_ID" jdbcType="BIGINT" property="settleListId" />
        <result column="mdtrt_no" jdbcType="VARCHAR" property="mdtrtNo" />
        <result column="PATIENT_ID" jdbcType="VARCHAR" property="patientId" />
        <result column="psn_no" jdbcType="VARCHAR" property="psnNo" />
        <result column="NAME" jdbcType="VARCHAR" property="name" />
        <result column="gend" jdbcType="VARCHAR" property="gend" />
        <result column="AGE" jdbcType="INTEGER" property="age" />
        <result column="HOSPITAL_ID" jdbcType="VARCHAR" property="hospitalId" />
        <result column="medins_name" jdbcType="VARCHAR" property="medinsName" />
        <result column="adm_time" jdbcType="VARCHAR" property="admTime" />
        <result column="dscg_time" jdbcType="VARCHAR" property="dscgTime" />
        <result column="setl_time" jdbcType="TIMESTAMP" property="setlTime" />
        <result column="main_diag_dise_codg" jdbcType="VARCHAR" property="mainDiagDiseCodg" />
        <result column="main_diag_dise_name" jdbcType="VARCHAR" property="mainDiagDiseName" />
        <result column="dscg_caty_codg_inhosp" jdbcType="VARCHAR" property="dscgCatyCodgInhosp" />
        <result column="dscg_caty_name_inhosp" jdbcType="VARCHAR" property="dscgCatyNameInhosp" />
        <result column="bkkp_sn" jdbcType="VARCHAR" property="bkkpSn" />
        <result column="hi_gen_item_codg" jdbcType="VARCHAR" property="hiGenItemCodg" />
        <result column="hi_gen_itemname" jdbcType="VARCHAR" property="hiGenItemname" />
        <result column="prnt_type_codg" jdbcType="VARCHAR" property="prntTypeCodg" />
        <result column="hi_item_codg" jdbcType="VARCHAR" property="hiItemCodg" />
        <result column="hosp_itemname" jdbcType="VARCHAR" property="hospItemname" />
        <result column="cnt" jdbcType="INTEGER" property="cnt" />
        <result column="act_pric" jdbcType="DECIMAL" property="actPric" />
        <result column="DATA_LOG_ID" jdbcType="BIGINT" property="dataLogId" />
        <result column="ACTIVE_FLAG" jdbcType="VARCHAR" property="activeFlag" />
    </resultMap>

    <sql id="Base_Column_List">
     ID, SETTLE_LIST_ID, mdtrt_no, PATIENT_ID, psn_no, NAME, gend, AGE, HOSPITAL_ID,
    medins_name, adm_time, dscg_time, setl_time, main_diag_dise_codg, main_diag_dise_name,
    dscg_caty_codg_inhosp, dscg_caty_name_inhosp, bkkp_sn, hi_gen_item_codg, hi_gen_itemname,
    prnt_type_codg, hi_item_codg, hosp_itemname, cnt, act_pric, DATA_LOG_ID, ACTIVE_FLAG
  </sql>

    <select id="getBalanceProjectList" parameterType="com.my.som.dto.dataHandle.DataHandleCommonDto" resultMap="BaseResultMap">
        SELECT
            distinct
        <include refid="Base_Column_List" />
        from som_setl_medcas
        WHERE 1=1
        <if test="queryParam.logId!=null and queryParam.logId!=''">
            AND DATA_LOG_ID = #{queryParam.logId}
        </if>
        <if test="queryParam.activeFlag!=null and queryParam.activeFlag!=''">
            AND ACTIVE_FLAG = #{queryParam.activeFlag}
        </if>
        LIMIT #{queryParam.start} , #{queryParam.limit}
    </select>

    <!-- 清洗未通过清洗的病案 -->
    <select id="queryNotPassCleanDataStsCdGroupRecordList"
            resultType="com.my.som.model.dataHandle.StsCDGroupRecord">
        SELECT
            a.id as settleListId,
            a.a03 as medcasType,
            a.hospital_id as hospitalId,
            '校验未通过' as grpFaleRea,
            a.active_flag as activeFlag
        from som_hi_invy_bas_info a
        WHERE a.id in
        (
            <include refid="com.my.som.dao.dataHandle.DrgGroupJobDao.settleListMedicalPageChoose"/>
        )
        <if test="queryParam.logId!=null and queryParam.logId!=''">
            AND a.DATA_LOG_ID = #{queryParam.logId}
        </if>
        <if test="queryParam.activeFlag!=null and queryParam.activeFlag!=''">
            AND a.ACTIVE_FLAG = #{queryParam.activeFlag}
        </if>
    </select>

    <!--批量插入分组记录-->
    <insert id="batchInsertStsPpsGroupRecord">
        insert into sts_pps_group_record (
            SETTLE_LIST_ID,mdtrt_no, PATIENT_ID,psn_no, NAME,
            gend, AGE, HOSPITAL_ID,
            medins_name, adm_time, dscg_time,setl_time,
            main_diag_dise_codg, main_diag_dise_name, dscg_caty_codg_inhosp,
            dscg_caty_name_inhosp, dis_gp_codg, dis_gp_name,
            grp_stas, grp_fale_rea, grp_flag, GROUP_CLASS, DATA_LOG_ID, ACTIVE_FLAG
        )
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.settleListId,jdbcType=VARCHAR},
            #{item.mdtrtNo,jdbcType=VARCHAR}, #{item.patientId,jdbcType=VARCHAR}, #{item.psnNo,jdbcType=VARCHAR}, #{item.name,jdbcType=VARCHAR},
            #{item.gend,jdbcType=VARCHAR}, #{item.age,jdbcType=INTEGER}, #{item.hospitalId,jdbcType=VARCHAR},
            #{item.medinsName,jdbcType=VARCHAR}, #{item.admTime,jdbcType=VARCHAR}, #{item.dscgTime,jdbcType=VARCHAR},
            #{item.setlTime,jdbcType=TIMESTAMP},
            #{item.mainDiagDiseCodg,jdbcType=VARCHAR}, #{item.mainDiagDiseName,jdbcType=VARCHAR}, #{item.dscgCatyCodgInhosp,jdbcType=VARCHAR},
            #{item.dscgCatyNameInhosp,jdbcType=VARCHAR}, #{item.disGpCodg,jdbcType=VARCHAR}, #{item.disGpName,jdbcType=VARCHAR},
            #{item.grpStas,jdbcType=VARCHAR}, #{item.grpFaleRea,jdbcType=VARCHAR}, #{item.grpFlag,jdbcType=VARCHAR},#{item.groupClass,jdbcType=VARCHAR},#{item.dataLogId,jdbcType=BIGINT}, #{item.activeFlag,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>

    <!-- 批量新增成都分组记录 -->
    <insert id="batchInsertStsCDGroupRecord">
        INSERT INTO som_grp_rcd (
                SETTLE_LIST_ID, medcas_type, HOSPITAL_ID,
                cd_codg,CD_NAME, grp_stas, grp_fale_rea,
                DATA_LOG_ID, OPR_DATE, ACTIVE_FLAG,
                grper_oupt_log, grp_err_log, grper_type, chk_flag
        ) VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (
            #{item.settleListId,jdbcType=BIGINT}, #{item.medcasType,jdbcType=VARCHAR}, #{item.hospitalId,jdbcType=VARCHAR},
            #{item.cdCodg,jdbcType=VARCHAR},#{item.cdName,jdbcType=VARCHAR}, #{item.grpStas,jdbcType=VARCHAR}, #{item.grpFaleRea,jdbcType=VARCHAR},
            #{item.dataLogId,jdbcType=BIGINT}, now(), #{item.activeFlag,jdbcType=VARCHAR},
            #{item.grperOuptLog,jdbcType=LONGVARCHAR}, #{item.grpErrLog,jdbcType=LONGVARCHAR},
            #{item.grper_type,jdbcType=VARCHAR}, , #{item.chkFlag,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>

    <!-- 批量新增成都分组日志 -->
    <insert id="batchInsertStsCDGroupLog">
        INSERT INTO som_cd_grper_intf_trns_log(
            HOSPITAL_ID,grp_para,grper_oupt_log,
            SETTLE_LIST_ID,opter,OPR_DATE,
            ACTIVE_FLAG,grper_info_id
        )
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (
            #{item.hospitalId,jdbcType=VARCHAR}, #{item.grpPara,jdbcType=LONGVARCHAR}, #{item.grperOuptLog,jdbcType=LONGVARCHAR},
            #{item.settleListId,jdbcType=BIGINT}, #{item.opter,jdbcType=VARCHAR}, now(), #{item.activeFlag,jdbcType=VARCHAR},
            #{item.grperInfoId,jdbcType=BIGINT}
            )
        </foreach>
    </insert>

    <!-- 成都分组生成中间表 -->
    <insert id="extractCDData">
        INSERT INTO som_cd_grp_info (
            SETTLE_LIST_ID, medcas_type, HOSPITAL_ID,
            medins_name, setl_begn_time,
            setl_end_time, med_pay_way,
            PATIENT_ID, NAME, gend,
            brdy, AGE, age_insuff_1_age_age,
            nwb_bir_wt, nwb_adm_wt, adm_way,
            adm_time,
            adm_caty_codg_inhosp, adm_caty_name_inhosp,
            dscg_caty_codg_inhosp, dscg_caty_name_inhosp,
            dscg_time, act_ipt,
            dscg_way, ipt_sumfee,
            ipt_sumfee_in_selfpay_amt, com_med_servfee, rhab_fee,
            diag_fee, treat_fee, drugfee,
            blood_blo_pro, mcs_fee, tcm_oth,
            oth_fee, abt_fee, inspect_fee,
            psn_selfpay,psn_ownpay,acct_pay,psn_cashpay,
            cd_codg, CD_NAME,
            STANDARD_YEAR, cd_wt, standard_ave_hosp_day,
            standard_avg_fee, AVG_AGE, avg_drug_fee, avg_mcs_fee,
            avg_abt_fee, inspect_fee_standard_val, main_diag_dise_codg,
            main_diag_dise_name, main_oprn_oprt_codg, main_oprn_oprt_name,
            deptdrt_code, deptdrt_name, chfdr_code,
            chfdr_name, atddr_code,
            atddr_name, ipdr_code, ipdr_name,
            resp_nurs_code, resp_nurs_name, train_dr_code,
            train_dr_name, intn_dr,
            codr_code, codr_name, qltctrl_dr_code,
            qltctrl_dr_name, qltctrl_nurs_code, qltctrl_nurs_name,
            qltctrl_date, grp_stas,
            grper_type, DATA_LOG_ID
        )SELECT
            <!--基本信息-->
            a.ID AS SETTLE_LIST_ID,
            a.A03 AS medcas_type,
            a.HOSPITAL_ID AS HOSPITAL_ID,
            a.A02 AS medins_name,
            a.D36 AS setl_begn_time,
            a.D37 AS setl_end_time,
            a.A46C AS med_pay_way,
            a.A48 AS PATIENT_ID,
            a.A11 AS NAME,
            a.A12C AS gend,
            a.A13 AS brdy,
            a.A14 AS AGE,
            a.A16 AS age_insuff_1_age_age,
            a.A18 AS nwb_bir_wt,
            a.A17 AS nwb_adm_wt,
            a.B11C AS adm_way,
            a.B12 AS adm_time,
            a.B13C AS adm_caty_codg_inhosp,
            a.B13N AS adm_caty_name_inhosp,
            B16C AS dscg_caty_codg_inhosp,
            B16N AS dscg_caty_name_inhosp,
            a.B15 AS dscg_time,
            a.B20 AS act_ipt,
            a.B34C AS dscg_way,
            <!--费用信息-->
            IFNULL(a.D01,0) AS ipt_sumfee,
            IFNULL(a.D09,0) AS ipt_sumfee_in_selfpay_amt,
            <!--综合医疗服务费：1.一般医疗服务费、2.一般治疗操作费、3.护理费综合医疗服务类、4.其他费用-->
            IFNULL(a.D11,0)+IFNULL(a.D12,0)+IFNULL(a.D13,0)+IFNULL(a.D14,0)  AS com_med_servfee,
            <!--康复费：11.康复费-->
            IFNULL(a.D21,0) AS rhab_fee,
            <!--诊断费（西医病案）：5.病理诊断费、6.实验室诊断费、7.影像学诊断费、8.临床诊断项目费-->
            <!--诊断费（中医病案）：5.病理诊断费、6.实验室诊断费、7.影像学诊断费、8.临床诊断项目费、12.中医类(中医和名族医医疗服务)中医诊断-->
            case when a.A03 = '1' then IFNULL(a.D15,0)+IFNULL(a.D16,0)+IFNULL(a.D17,0)+IFNULL(a.D18,0)
              else IFNULL(a.D15,0)+IFNULL(a.D16,0)+IFNULL(a.D17,0)+IFNULL(a.D18,0)+IFNULL(a.D63,0)
            end AS diag_fee,
            <!--治疗类：9.非手术治疗项目费、10.手术治疗费、13.中医治疗费-->
            IFNULL(a.D19,0)+IFNULL(a.D20,0)+IFNULL(a.d22,0) AS treat_fee,
            <!--药品费：15.西药费、16.中成药费、17.中草药费-->
            IFNULL(a.D23,0)+IFNULL(a.D24,0)+IFNULL(a.D25,0)  AS drugfee,
            <!--血液和血液制品类费：18.血费、19白蛋白类制品费、20球蛋白类制品费、21凝血因子类制品费、22细胞因子类制品费-->
            IFNULL(a.D26,0)+IFNULL(a.D27,0)+IFNULL(a.D28,0)+IFNULL(a.D29,0)+IFNULL(a.D30,0) AS blood_blo_pro,
            <!--耗材费：23.检查用一次性医用材料费、24.治疗用一次性医用材料费、25.手术用一次性医用材料费-->
            IFNULL(a.D31,0)+IFNULL(a.D32,0)+IFNULL(a.D33,0) AS mcs_fee,
            <!--中医其他：(中医病案首页：14.中医其他）-->
            case when a.A03 = '1' then 0 else IFNULL(a.D64,0) end AS tcm_oth,
            <!--其他费：26其他费-->
            IFNULL(a.D34,0) AS oth_fee,
            <!--抗生素费用（另外：抗菌药物费）-->
            IFNULL(a.D23x01,0) AS abt_fee,
            <!--检验费（另外：实验室诊断费+影像学诊断费）-->
            IFNULL(a.D16,0)+IFNULL(a.D17,0) AS inspect_fee,
            <!--结算清单个人自付部分金额-->
            IFNULL(a.D54,0) AS psn_selfpay,
            IFNULL(a.D55,0) AS psn_ownpay,
            IFNULL(a.D56,0) AS acct_pay,
            IFNULL(a.D57,0) AS psn_cashpay,
            <!--分组信息-->
            d.cd_codg AS dip_codg,
            d.CD_NAME AS dip_codg,
            <!--  标杆信息 -->
            e.STANDARD_YEAR AS STANDARD_YEAR,
            e.STANDARD_YEAR as cd_wt,
            convert(AES_DECRYPT(UNHEX(e.standard_ave_hosp_day),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) using utf8) as standard_ave_hosp_day,
            convert(AES_DECRYPT(UNHEX(e.standard_avg_fee),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) using utf8) as standard_avg_fee,
            e.AVG_AGE as AVG_AGE,
            e.avg_drug_fee as avg_drug_fee,
            e.avg_mcs_fee as avg_mcs_fee,
            e.avg_abt_fee as avg_abt_fee,
            e.inspect_fee_standard_val as inspect_fee_standard_val,
            <!--诊疗信息-->
            a.C03C AS main_diag_dise_codg,
            a.C04N AS main_diag_dise_name,
            a.C14x01C AS main_oprn_oprt_codg,
            a.C15x01N AS main_oprn_oprt_name,
            a.B22C AS deptdrt_code,
            a.B22N AS deptdrt_name,
            a.B23C AS chfdr_code,
            a.B23N AS chfdr_name,
            a.B24C AS atddr_code,
            a.B24N AS atddr_name,
            a.B25C AS ipdr_code,
            a.B25N AS ipdr_name,
            a.B26C AS resp_nurs_code,
            a.B26N AS resp_nurs_name,
            a.B27C AS train_dr_code,
            a.B27N AS train_dr_name,
            a.B28 AS intn_dr,
            a.B29C AS codr_code,
            a.B29N codr_name,
            a.B31C AS qltctrl_dr_code,
            a.B31N AS qltctrl_dr_name,
            a.B32C AS qltctrl_nurs_code,
            a.B32N AS qltctrl_nurs_name,
            a.B33 AS qltctrl_date,
            d.grp_stas AS grp_stas,
            <!--数据处理信息-->
            d.grper_type AS grper_type,
            b.ID AS DATA_LOG_ID
          FROM som_hi_invy_bas_info a
          INNER JOIN som_datapros_log b ON a.DATA_LOG_ID = b.ID
          INNER JOIN som_grp_rcd d ON a.ID = d.SETTLE_LIST_ID
        <!-- LEFT JOIN som_drg_standard e ON d.drg_codg = e.drg_codg AND SUBSTR(a.B15,1,4) = e.STANDARD_YEAR -->
        WHERE a.ACTIVE_FLAG = '1'
          <if test="queryParam.logId != null and queryParam.logId != ''">
            AND a.DATA_LOG_ID = #{queryParam.logId}
          </if>
    </insert>
</mapper>
