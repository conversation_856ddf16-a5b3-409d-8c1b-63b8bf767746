<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.som.dao.dataHandle.BusBalanceProjectDao">
    <!--批量新增-->
    <insert id="insertBalanceProjectData">
        insert into som_setl_medcas (
            SETTLE_LIST_ID,mdtrt_no,PATIENT_ID, psn_no, NAME,
            gend, AGE, HOSPITAL_ID,
            medins_name, adm_time, dscg_time,setl_time,
            main_diag_dise_codg, main_diag_dise_name, dscg_caty_codg_inhosp,
            dscg_caty_name_inhosp, bkkp_sn, hi_gen_item_codg,
            hi_gen_itemname, prnt_type_codg, hi_item_codg,
            hosp_itemname, cnt, act_pric,
            DATA_LOG_ID, ACTIVE_FLAG
        )values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.settle_list_id,jdbcType=VARCHAR},
            #{item.mdtrt_no,jdbcType=VARCHAR},#{item.patient_id,jdbcType=VARCHAR}, #{item.psn_no,jdbcType=VARCHAR}, #{item.name,jdbcType=VARCHAR},
            #{item.gend,jdbcType=VARCHAR}, #{item.age,jdbcType=INTEGER}, #{item.hospital_id,jdbcType=VARCHAR},
            #{item.medins_name,jdbcType=VARCHAR}, #{item.adm_time,jdbcType=VARCHAR}, #{item.dscg_time,jdbcType=VARCHAR},
            #{item.setl_time,jdbcType=TIMESTAMP},
            #{item.main_diag_dise_codg,jdbcType=VARCHAR}, #{item.main_diag_dise_name,jdbcType=VARCHAR}, #{item.dscg_caty_codg_inhosp,jdbcType=VARCHAR},
            #{item.dscg_caty_name_inhosp,jdbcType=VARCHAR}, #{item.bkkp_sn,jdbcType=VARCHAR}, #{item.hi_gen_item_codg,jdbcType=VARCHAR},
            #{item.hi_gen_itemname,jdbcType=VARCHAR}, #{item.prnt_type_codg,jdbcType=VARCHAR}, #{item.hi_item_codg,jdbcType=VARCHAR},
            #{item.hosp_itemname,jdbcType=VARCHAR}, #{item.cnt,jdbcType=INTEGER}, #{item.act_pric,jdbcType=DECIMAL},
            #{item.data_log_id,jdbcType=BIGINT}, #{item.active_flag,jdbcType=VARCHAR})
        </foreach>
    </insert>

    <delete id="deleteBalanceProjectDataByOutHosTime" parameterType="java.util.HashMap">
        delete from som_setl_medcas where 1=1
        <if test="queryParam.begn_date!=null and queryParam.begn_date!='' and
                queryParam.expi_date!=null and queryParam.expi_date!=''">
            AND dscg_time BETWEEN #{queryParam.begn_date} and #{queryParam.expi_date}
        </if>
    </delete>

    <delete id="deleteStsPpsProjectRecordByOutHosTime" parameterType="java.util.HashMap">
        delete from sts_pps_group_record where 1=1
        <if test="queryParam.begn_date!=null and queryParam.begn_date!='' and
                queryParam.expi_date!=null and queryParam.expi_date!=''">
            AND dscg_time BETWEEN #{queryParam.begn_date} and #{queryParam.expi_date}
        </if>
    </delete>

</mapper>