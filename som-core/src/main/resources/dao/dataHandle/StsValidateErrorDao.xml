<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.som.dao.dataHandle.StsValidateErrorDao">
    <!--批量新增回写主键支持-->
    <insert id="savePreValidateErrorBatch">
        INSERT INTO som_setl_invy_chk_err_rcd (
            SETTLE_LIST_ID, HOSPITAL_ID, err_type,
            err_dscr,DATA_LOG_ID, OPR_DATE
        ) VALUES
        <foreach collection="sves" item="item" index="index" separator=",">
            (#{item.settleListId,jdbcType=BIGINT}, #{item.hospitalId,jdbcType=VARCHAR}, #{item.errType,jdbcType=VARCHAR},
            #{item.errDscr,jdbcType=LONGVARCHAR}, #{item.dataLogId,jdbcType=BIGINT},#{item.oprDate,jdbcType=TIMESTAMP}
            )
        </foreach>
    </insert>
    
    <!-- 批量插入校验错误日志信息 -->
    <insert id="savePreValidateErrorBatch2">
        INSERT INTO som_setl_invy_chk_err_rcd (
            SETTLE_LIST_ID, HOSPITAL_ID, err_type,
            err_dscr,DATA_LOG_ID, OPR_DATE
        ) VALUES
        (#{settleListId,jdbcType=BIGINT}, #{hospitalId,jdbcType=VARCHAR}, #{errType,jdbcType=VARCHAR},
        #{errDscr,jdbcType=LONGVARCHAR}, #{dataLogId,jdbcType=BIGINT},#{oprDate,jdbcType=TIMESTAMP}
        )
    </insert>
</mapper>