<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.som.dao.dataHandle.StsDrgGroupRecordDao">
    <delete id="deleteStsDrgGroupRecordByLogId" parameterType="java.lang.Long">
        DELETE FROM som_drg_grp_rcd
        WHERE SETTLE_LIST_ID IN (
            SELECT a.ID FROM (
                SELECT ID FROM som_hi_invy_bas_info
                WHERE DATA_LOG_ID = #{logId,jdbcType=BIGINT}
                AND ACTIVE_FLAG='1'
            )a
        )
    </delete>
</mapper>