<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.som.dao.dataHandle.BusDiseaseDiagnosisDao">
    <resultMap id="BaseResultMap" type="com.my.som.model.dataHandle.SomDiag">
        <id column="ID" jdbcType="BIGINT" property="id" />
        <result column="SETTLE_LIST_ID" jdbcType="BIGINT" property="settleListId" />
        <result column="seq" jdbcType="INTEGER" property="seq" />
        <result column="TYPE" jdbcType="VARCHAR" property="type" />
        <result column="dscg_diag_codg" jdbcType="VARCHAR" property="dscg_diag_codg" />
        <result column="dscg_diag_name" jdbcType="VARCHAR" property="dscg_diag_name" />
        <result column="dscg_diag_adm_cond" jdbcType="VARCHAR" property="dscg_diag_adm_cond" />
        <result column="dscg_diag_dscg_cond" jdbcType="VARCHAR" property="dscg_diag_dscg_cond" />
    </resultMap>
    <select id="getDiseaseInfoById" resultType="java.util.Map">
        SELECT
            seq as seq,
            TYPE as type,
            dscg_diag_codg as dscg_diag_codg,
            dscg_diag_name as dscg_diag_name,
            dscg_diag_adm_cond as dscg_diag_adm_cond,
            dscg_diag_dscg_cond as dscg_diag_dscg_cond
        FROM
          som_diag
        WHERE 1 = 1
        <if test="queryParam.id!=null and queryParam.id!=''">
            AND `SETTLE_LIST_ID` = #{queryParam.id}
        </if>
    </select>

    <sql id="Base_Column_List">
      ID, SETTLE_LIST_ID, seq, TYPE, dscg_diag_codg, dscg_diag_name, dscg_diag_adm_cond, dscg_diag_dscg_cond
    </sql>
    <select id="getDiseaseListForValidate" resultType="com.my.som.model.dataHandle.BusDiseaseDiagnosisCost">
        SELECT
            x.ID as id,
            x.SETTLE_LIST_ID as settleListId,
            x.seq as seq,
            x.TYPE as type,
            UPPER(x.dscg_diag_codg) as dscg_diag_codg,
            x.dscg_diag_name as dscg_diag_name,
            x.dscg_diag_adm_cond as dscg_diag_adm_cond,
            x.dscg_diag_dscg_cond as dscg_diag_dscg_cond,
            ROUND(IFNULL(convert(AES_DECRYPT(UNHEX(y.standard_fee),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) using utf8),0),4) as standardFee
        FROM som_diag x left join
        som_code_stand_fee y on x.dscg_diag_codg=y.icd_codg and y.ICD_TYPE='ICD-10'
        <if test="queryParam.diagnostic_type!=null and queryParam.diagnostic_type!=''">
            AND x.TYPE = #{queryParam.diagnostic_type}
        </if>
<!--        <if test="queryParam.hospLv!=null and queryParam.hospLv!=''">-->
<!--            AND y.hosp_lv = #{queryParam.hospLv}-->
<!--        </if>-->
        <if test="queryParam.active_flag!=null and queryParam.active_flag!=''">
            AND y.ACTIVE_FLAG = #{queryParam.active_flag}
        </if>
        WHERE x.dscg_diag_codg!='-' and x.dscg_diag_codg!='--'
        AND x.SETTLE_LIST_ID IN (
            SELECT a.ID
            FROM (
                SELECT
                  ID
                FROM som_hi_invy_bas_info
                WHERE 1 = 1
                <if test="queryParam.logId!=null and queryParam.logId!=''">
                    AND `DATA_LOG_ID` = #{queryParam.logId}
                </if>
                <if test="queryParam.active_flag!=null and queryParam.active_flag!=''">
                    AND `ACTIVE_FLAG` = #{queryParam.active_flag}
                </if>
                LIMIT #{queryParam.start} , #{queryParam.limit}
            ) a
        )
    </select>

    <!-- 是否校验通过再分组 -->
    <sql id="groupLimit">
        SELECT a.id
        from som_hi_invy_bas_info a
        <where>
            <if test="queryParam.validateEnableType != null and queryParam.validateEnableType != '' and queryParam.validateEnableType == 1">
                AND a.id not in
                (
                    <include refid="com.my.som.dao.dataHandle.DrgGroupJobDao.settleListMedicalPageChoose"/>
                )
            </if>
            <if test="queryParam.logId!=null and queryParam.logId!=''">
                AND a.DATA_LOG_ID = #{queryParam.logId}
            </if>
            <if test="queryParam.activeFlag!=null and queryParam.activeFlag!=''">
                AND a.ACTIVE_FLAG = #{queryParam.activeFlag}
            </if>
        </where>

        LIMIT #{queryParam.start} , #{queryParam.limit}
    </sql>

    <select id="getDiseaseListForGroup" resultMap="BaseResultMap">
        SELECT
            distinct
        <include refid="Base_Column_List" />
        FROM som_diag
        WHERE dscg_diag_codg!='-' and dscg_diag_codg!='--'
        AND SETTLE_LIST_ID IN (
            SELECT t.ID
            FROM (
                <include refid="groupLimit" />
            ) t
        )
        <if test="queryParam.diagType!=null and queryParam.diagType!=''">
            AND `TYPE` = #{queryParam.diagType}
        </if>
    </select>

    <select id="getDiseaseListForDipGroup" resultMap="BaseResultMap">
        SELECT
            distinct
            <include refid="Base_Column_List" />
        FROM som_diag
        WHERE dscg_diag_codg!='-' and dscg_diag_codg!='--'
        AND SETTLE_LIST_ID IN (
            SELECT t.ID
            FROM (
                SELECT
                  ID
                FROM som_hi_invy_bas_info a
                WHERE 1=1
                <if test="queryParam.logId!=null and queryParam.logId!=''">
                    AND a.DATA_LOG_ID = #{queryParam.logId}
                </if>
                <if test="queryParam.activeFlag!=null and queryParam.activeFlag!=''">
                    AND a.ACTIVE_FLAG = #{queryParam.activeFlag}
                </if>
                LIMIT #{queryParam.start} , #{queryParam.limit}
            ) t
        )
        <if test="queryParam.diagType!=null and queryParam.diagType!=''">
            AND `TYPE` = #{queryParam.diagType}
        </if>
    </select>

    <select id="getDiseaseListForGroupGt60" resultMap="BaseResultMap">
        SELECT
            distinct
            <include refid="Base_Column_List" />
        FROM som_diag
        WHERE SETTLE_LIST_ID IN (
            SELECT t.ID
            FROM (
            SELECT
            ID
            FROM som_hi_invy_bas_info a
            WHERE <![CDATA[ a.B20>60 ]]>
            <if test="queryParam.logId!=null and queryParam.logId!=''">
                AND a.DATA_LOG_ID = #{queryParam.logId}
            </if>
            <if test="queryParam.activeFlag!=null and queryParam.activeFlag!=''">
                AND a.ACTIVE_FLAG = #{queryParam.activeFlag}
            </if>
            LIMIT #{queryParam.start} , #{queryParam.limit}
            ) t
            )
        <if test="queryParam.diagType!=null and queryParam.diagType!=''">
            AND `TYPE` = #{queryParam.diagType}
        </if>
    </select>

    <!-- 获取病案首页诊断信息 -->
    <select id="getMedicalRecordDisease"
            resultType="com.my.som.model.dataHandle.BusDiseaseDiagnosisCost">
        SELECT
            x.ID as id,
            x.medcas_hmpg_id as settleListId,
            x.seq as seq,
            x.TYPE as type,
            UPPER(x.dscg_diag_codg) as dscg_diag_codg,
            x.dscg_diag_name as dscg_diag_name,
            x.dscg_diag_adm_cond as dscg_diag_adm_cond,
            x.dscg_diag_dscg_cond as dscg_diag_dscg_cond,
            ROUND(IFNULL(convert(AES_DECRYPT(UNHEX(y.standard_fee),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) using utf8),0),4) as standardFee
        FROM som_init_diag_info x left join
        som_code_stand_fee y on x.dscg_diag_codg=y.icd_codg and y.ICD_TYPE='ICD-10'
        <if test="queryParam.diagnostic_type!=null and queryParam.diagnostic_type!=''">
            AND x.TYPE = #{queryParam.diagnostic_type}
        </if>
        <if test="queryParam.active_flag!=null and queryParam.active_flag!=''">
            AND y.ACTIVE_FLAG = #{queryParam.active_flag}
        </if>
        WHERE x.dscg_diag_codg!='-' and x.dscg_diag_codg!='--'
        AND x.medcas_hmpg_id IN (
            SELECT id
            FROM som_init_hi_setl_invy_med_fee_info
            WHERE settle_list_id IN(
                SELECT a.ID
                FROM (
                    SELECT
                      ID
                    FROM som_hi_invy_bas_info
                    WHERE 1 = 1
                    <if test="queryParam.logId!=null and queryParam.logId!=''">
                        AND `DATA_LOG_ID` = #{queryParam.logId}
                    </if>
                    <if test="queryParam.active_flag!=null and queryParam.active_flag!=''">
                        AND `ACTIVE_FLAG` = #{queryParam.active_flag}
                    </if>
                    LIMIT #{queryParam.start} , #{queryParam.limit}
                ) a
            )
        )
    </select>

    <!--批量新增-->
    <insert id="insertDiseaseData">
        INSERT INTO som_diag (
           SETTLE_LIST_ID, seq, TYPE, dscg_diag_codg, dscg_diag_name, dscg_diag_adm_cond, dscg_diag_dscg_cond,maindiag_flag
        ) VALUES
        <foreach collection="dd" item="item" index="index" separator=",">
            (#{item.settle_list_id,jdbcType=BIGINT},#{item.seq,jdbcType=INTEGER},
            #{item.type,jdbcType=VARCHAR},#{item.dscg_diag_codg,jdbcType=VARCHAR},#{item.dscg_diag_name,jdbcType=VARCHAR},
            #{item.dscg_diag_adm_cond,jdbcType=VARCHAR},#{item.dscg_diag_dscg_cond,jdbcType=VARCHAR},#{item.maindiag_flag,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>

    <!--批量新增-->
    <insert id="insertDiseaseRecordData">
        INSERT INTO som_init_diag (
        K00, seq, TYPE, dscg_diag_codg, dscg_diag_name, dscg_diag_adm_cond, dscg_diag_dscg_cond
        ) VALUES
        <foreach collection="dd" item="item" index="index" separator=",">
            (#{item.k00,jdbcType=VARCHAR},#{item.seq,jdbcType=INTEGER},
            #{item.type,jdbcType=VARCHAR},#{item.dscg_diag_codg,jdbcType=VARCHAR},#{item.dscg_diag_name,jdbcType=VARCHAR},
            #{item.dscg_diag_adm_cond,jdbcType=VARCHAR},#{item.dscg_diag_dscg_cond,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>

    <!--批量新增-->
    <insert id="insertVDrgDisease">
        INSERT INTO som_drg_fee_setl_diag (
        K00, seq, TYPE, dscg_diag_codg, dscg_diag_name, dscg_diag_adm_cond, dscg_diag_dscg_cond
        ) VALUES
        <foreach collection="dd" item="item" index="index" separator=",">
            (#{item.k00,jdbcType=VARCHAR},#{item.seq,jdbcType=INTEGER},
            #{item.type,jdbcType=VARCHAR},#{item.dscg_diag_codg,jdbcType=VARCHAR},#{item.dscg_diag_name,jdbcType=VARCHAR},
            #{item.dscg_diag_adm_cond,jdbcType=VARCHAR},#{item.dscg_diag_dscg_cond,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>

    <!--批量新增-->
    <insert id="bacthInsertBusDiseaseDiagnosis">
        INSERT INTO som_diag (
        <include refid="busDiseaseDiagnosisFields" />
        ) VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.settleListId,jdbcType=BIGINT},#{item.seq,jdbcType=INTEGER},
            #{item.type,jdbcType=VARCHAR},#{item.dscg_diag_codg,jdbcType=VARCHAR},#{item.dscg_diag_name,jdbcType=VARCHAR},
            #{item.dscg_diag_adm_cond,jdbcType=VARCHAR},#{item.dscg_diag_dscg_cond,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>

    <sql id="busDiseaseDiagnosisFields">
        SETTLE_LIST_ID, seq,    TYPE,   dscg_diag_codg,   dscg_diag_name,   dscg_diag_adm_cond,   dscg_diag_dscg_cond
    </sql>
</mapper>
