<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.som.dao.dataHandle.StsCodeResourceConsumptionDao">
    <!--批量新增回写主键支持-->
    <insert id="savePreValidateCodeResourceConsumptionBatch">
        INSERT INTO som_codg_resu_adjm_rcd (
            SETTLE_LIST_ID, HOSPITAL_ID, main_diag_dise_codg,
            main_oprn_codg, main_diag_is_cho_err,
            main_oprn_is_cho_err, resu_cosm_adjm_new_main_diag_codg,
            resu_cosm_adjm_new_main_diag_name, resu_cosm_adjm_new_main_oprn_codg,
            resu_cosm_adjm_new_main_oprn_name, resu_cosm_adjm_new_drg_codg, resu_cosm_adjm_new_drg_name,
            resu_cosm_adjm_new_dip_codg, resu_cosm_adjm_new_dip_name, DATA_LOG_ID,
            OPR_DATE
        )VALUES
        <foreach collection="scrc" item="item" index="index" separator=",">
            (#{item.settleListId,jdbcType=BIGINT}, #{item.hospitalId,jdbcType=VARCHAR}, #{item.mainDiagDiseCodg,jdbcType=VARCHAR},
            #{item.mainOprnCodg,jdbcType=VARCHAR}, #{item.mainDiagIsChoErr,jdbcType=VARCHAR},
            #{item.mainOprnIsChoErr,jdbcType=VARCHAR}, #{item.resuCosmAdjmNewMainDiagCodg,jdbcType=VARCHAR},
            #{item.resuCosmAdjmNewMainDiagName,jdbcType=VARCHAR},#{item.resuCosmAdjmNewMainOprnCodg,jdbcType=VARCHAR},
            #{item.resuCosmAdjmNewMainOprnName,jdbcType=VARCHAR},#{item.resuCosmAdjmNewDrgCodg,jdbcType=VARCHAR}, #{item.resuCosmAdjmNewDrgName,jdbcType=VARCHAR},
            #{item.resuCosmAdjmNewDipCodg,jdbcType=VARCHAR}, #{item.resuCosmAdjmNewDipName,jdbcType=VARCHAR},
            #{item.dataLogId,jdbcType=BIGINT}, now())
        </foreach>
    </insert>

    <insert id="savePreValidateCodeResourceConsumptionBatch2">
        INSERT INTO som_codg_resu_adjm_rcd (
            SETTLE_LIST_ID, HOSPITAL_ID, main_diag_dise_codg,
            main_oprn_codg, main_diag_is_cho_err,
            main_oprn_is_cho_err, resu_cosm_adjm_new_main_diag_codg,
            resu_cosm_adjm_new_main_diag_name, resu_cosm_adjm_new_main_oprn_codg,
            resu_cosm_adjm_new_main_oprn_name, resu_cosm_adjm_new_drg_codg, resu_cosm_adjm_new_drg_name,
            resu_cosm_adjm_new_dip_codg, resu_cosm_adjm_new_dip_name, DATA_LOG_ID,
            OPR_DATE
        )VALUES
        (#{settleListId,jdbcType=BIGINT}, #{hospitalId,jdbcType=VARCHAR}, #{mainDiagDiseCodg,jdbcType=VARCHAR},
        #{mainOprnCodg,jdbcType=VARCHAR}, #{mainDiagIsChoErr,jdbcType=VARCHAR},
        #{mainOprnIsChoErr,jdbcType=VARCHAR}, #{resuCosmAdjmNewMainDiagCodg,jdbcType=VARCHAR},
        #{resuCosmAdjmNewMainDiagName,jdbcType=VARCHAR},#{resuCosmAdjmNewMainOprnCodg,jdbcType=VARCHAR},
        #{resuCosmAdjmNewMainOprnName,jdbcType=VARCHAR},#{resuCosmAdjmNewDrgCodg,jdbcType=VARCHAR}, #{resuCosmAdjmNewDrgName,jdbcType=VARCHAR},
        #{resuCosmAdjmNewDipCodg,jdbcType=VARCHAR}, #{resuCosmAdjmNewDipName,jdbcType=VARCHAR},
        #{dataLogId,jdbcType=BIGINT}, now())
    </insert>

    <update id="updateStsCodeResourceConsumptionNewDrgName" parameterType="java.util.HashMap">
        update som_codg_resu_adjm_rcd a
        set a.resu_cosm_adjm_new_drg_name = (select DRG_NAME from som_drg_name b where b.drg_codg=a.resu_cosm_adjm_new_drg_codg and b.ACTIVE_FLAG='1' LIMIT 0,1)
        where a.resu_cosm_adjm_new_drg_codg is not null
        <if test="queryParam.logId!=null and queryParam.logId!=''">
            AND a.DATA_LOG_ID = #{queryParam.logId}
        </if>
    </update>
</mapper>