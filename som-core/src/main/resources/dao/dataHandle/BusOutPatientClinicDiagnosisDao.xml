<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.som.dao.dataHandle.BusOutPatientClinicDiagnosisDao">
    <!--批量新增-->
    <insert id="insertOutClinicData">
        INSERT INTO som_otp_slow_special_trt_info (
            hi_setl_invy_id, diag_code, diag_name, oprn_oprt_code, oprn_oprt_name,
            DEPT_CODE, DEPT_NAME, mdtrt_date
        ) VALUES
        <foreach collection="ocd" item="item" index="index" separator=",">
            (#{item.settle_list_id,jdbcType=BIGINT},#{item.diag_code,jdbcType=VARCHAR},
            #{item.diag_name,jdbcType=VARCHAR},#{item.oprn_oprt_code,jdbcType=VARCHAR},#{item.oprn_oprt_name,jdbcType=VARCHAR},
            #{item.dept_code,jdbcType=VARCHAR}, #{item.dept_name,jdbcType=VARCHAR}, #{item.mdtrt_date,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>

    <!--批量新增-->
    <insert id="insertVDrgOutClinic">
        INSERT INTO som_medcas_diag_oprn_info (
            K00, diag_code, diag_name, oprn_oprt_code, oprn_oprt_name,
            DEPT_CODE, DEPT_NAME, mdtrt_date
        ) VALUES
        <foreach collection="ocd" item="item" index="index" separator=",">
            (#{item.k00,jdbcType=VARCHAR},#{item.diag_code,jdbcType=VARCHAR},
            #{item.diag_name,jdbcType=VARCHAR},#{item.oprn_oprt_code,jdbcType=VARCHAR},#{item.oprn_oprt_name,jdbcType=VARCHAR},
            #{item.dept_code,jdbcType=VARCHAR}, #{item.dept_name,jdbcType=VARCHAR}, #{item.mdtrt_date,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>

</mapper>