<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.som.dao.dataHandle.DataHandleLogDao">
    <select id="getList" resultMap="com.my.som.mapper.dataHandle.SomDataprosLogMapper.BaseResultMap">
        SELECT
            ID, IFNULL(medcas_val,0) as medcas_val, IFNULL(integrity_chk_pass_val,0) as integrity_chk_pass_val,
            IFNULL(logic_chk_pass_val,0) as logic_chk_pass_val, IFNULL(data_clean_pass_val,0) as data_clean_pass_val,
            IFNULL(drg_in_group_medcas_val,0) as drg_in_group_medcas_val, IFNULL(bus_key_tab_selc_medcas_val,0) as bus_key_tab_selc_medcas_val,
            IFNULL(dip_in_group_medcas_val,0) as cd_in_group_medcas_val, data_upld_time, data_dscg_time_scp, datapros_dura,RESULT,
            prcs_prgs, oprt_psn, HOSPITAL_ID, ACTIVE_FLAG,
            IFNULL(dip_in_group_medcas_val,0) as dip_in_group_medcas_val
        FROM
        som_datapros_log
        WHERE 1 = 1
        <if test="queryParam.id!=null and queryParam.id!=''">
            AND `id` = #{queryParam.id}
        </if>
        <if test="queryParam.data_upld_time!=null and queryParam.data_upld_time!=''">
            AND `upload_date` LIKE concat(#{queryParam.data_upld_time},"%")
        </if>
        <if test="queryParam.result!=null and queryParam.result=='exp'">  <!-- 存在异常的流程-->
            AND substr(result,1,3) = #{queryParam.result}
        </if>
        <if test="queryParam.result!=null and queryParam.result=='success'">
            AND substr(result,1,7) = #{queryParam.result}
        </if>
        <if test="queryParam.result!=null and queryParam.result=='run'">  <!-- 正在执行的流程-->
            AND substr(result,1,3) != 'exp'
            AND substr(result,1,7) != 'success'
        </if>
        <if test="queryParam.hospital_id!=null">
            AND `hospital_id` = #{queryParam.hospital_id}
        </if>
        <if test="queryParam.active_flag!=null">
            AND `active_flag` = #{queryParam.active_flag}
        </if>
        order by data_upld_time desc
    </select>
</mapper>