<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.som.dao.dataHandle.BusIcuDao">
    <!--批量新增-->
    <insert id="insertIcuData">
        INSERT INTO som_setl_invy_scs_cutd_info (
            hi_setl_invy_id, scs_cutd_ward_type, scs_cutd_inpool_time, scs_cutd_exit_time, scs_cutd_sum_dura
        ) VALUES
        <foreach collection="id" item="item" index="index" separator=",">
            (#{item.settle_list_id,jdbcType=BIGINT},
            #{item.scs_cutd_ward_type,jdbcType=VARCHAR},#{item.scs_cutd_inpool_time,jdbcType=VARCHAR},
            #{item.scs_cutd_exit_time,jdbcType=VARCHAR},#{item.scs_cutd_sum_dura,jdbcType=DECIMAL}
            )
        </foreach>
    </insert>

    <!--批量新增-->
    <insert id="insertVDrgIcu">
        INSERT INTO som_drg_fee_icu_diag_info (
          K00, scs_cutd_ward_type, scs_cutd_inpool_time, scs_cutd_exit_time, scs_cutd_sum_dura
        ) VALUES
        <foreach collection="id" item="item" index="index" separator=",">
            (#{item.k00,jdbcType=VARCHAR},
            #{item.scs_cutd_ward_type,jdbcType=VARCHAR},#{item.scs_cutd_inpool_time,jdbcType=VARCHAR},
            #{item.scs_cutd_exit_time,jdbcType=VARCHAR},#{item.scs_cutd_sum_dura,jdbcType=DECIMAL}
            )
        </foreach>
    </insert>
</mapper>