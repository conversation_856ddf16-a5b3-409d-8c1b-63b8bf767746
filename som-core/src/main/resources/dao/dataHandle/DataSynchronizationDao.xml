<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.som.dao.dataHandle.DataSynchronizationDao">

    <!-- 保存同步日志 -->
    <insert id="saveSyncLog">
        INSERT INTO bus_sync_log(
            sync_data,sync_res,err_msg,crter,crte_time
        )
        VALUES(
               #{syncData,jdbcType=VARCHAR},
               #{syncRes,jdbcType=VARCHAR},
               #{errMsg,jdbcType=VARCHAR},
               #{crter,jdbcType=VARCHAR},
               now()
       )
    </insert>

    <!-- 条件查询数据 -->
    <select id="queryData" resultType="com.my.som.vo.dataHandle.DataSynchronization.DataSyncBaseVo">
        SELECT ID as id,
               K00 as mdtrtId,
               A48 as medcasCodg,
               A11 as name,
               A12C as gend,
               A14 as age,
               D01 as inHosTotalCost,
               B16N as  deptName,
               B16C as deptCode,
               B15 as outHosTime,
               B25N as resident,
               B25C as residentCode
        FROM som_hi_invy_bas_info WHERE B15 BETWEEN #{begnDate} and CONCAT(#{expiDate},' 23:59:59')
        <if test="name != null and name != ''">
            and A11 like CONCAT('%',#{name},'%')
        </if>
        <if test="deptCode != null and deptCode != ''">
            and B16C = #{deptCode}
        </if>
        <if test="drCodg != null and drCodg != ''">
            and B25C = #{drCodg}
        </if>
        <if test="medcasCodg != null and medcasCodg != ''">
            and A48 like CONCAT('%',#{medcasCodg},'%')
        </if>
    </select>

    <!-- 查询住院医师 -->
    <select id="queryResident" resultType="com.my.som.vo.dataHandle.DataSynchronization.DataSyncResidentVo">
        select distinct
                        B25N as resident,
                        B25C as residentCode
        from som_hi_invy_bas_info
        WHERE B15 BETWEEN #{begnDate} and CONCAT(#{expiDate},' 23:59:59')
    </select>

    <!-- 查询手动名称 -->
    <select id="querySyncName" resultType="com.my.som.vo.dataHandle.DataSynchronization.SyncNameVo">
        select msn_id as id,
               manual_sync_name as syncName,
               manual_sync_code as syncCode
        from cfg_sync_data
    </select>

    <!-- 查询同步日志 -->
    <select id="querySyncData" resultType="com.my.som.entity.sync.SyncDataLog">
        select id,
               sync_data as syncData,
               sync_res as syncRes,
               err_msg as errMsg,
               crter,
               crte_time as crteTime
        from bus_sync_log
        <where>
            <if test="medcasCodg != null and medcasCodg != ''">
                AND sync_data LIKE CONCAT('%', #{medcasCodg,jdbcType=VARCHAR}, '%')
            </if>
        </where>
        ORDER BY crte_time DESC
    </select>

    <!-- 查询未在系统的数据 -->
    <select id="queryNotInSysData" resultType="java.lang.String">
        select a.id
        from (
            <foreach collection="uniqueIdArr" item="id" separator="union all">
                select #{id} as id
            </foreach>
        ) a
        where a.id not in (
            select k00
            from som_hi_invy_bas_info
            where B15 BETWEEN #{begnDate,jdbcType=VARCHAR} and CONCAT(#{expiDate,jdbcType=VARCHAR},' 23:59:59')
        )
    </select>

    <!-- 刪除费用明细 -->
    <delete id="deleteCostDetail" parameterType="java.util.List">
        delete from som_chrg_detl_intf where mdtrt_id in (
            <foreach collection="list" item="id" separator=",">
                #{id}
            </foreach>
            )
    </delete>

    <!-- 删除住院医嘱记录 -->
    <delete id="deleteAdvRecords" parameterType="java.util.List">
        delete from som_drord_info_intf where mdtrt_id in (
        <foreach collection="list" item="id" separator=",">
            #{id}
        </foreach>
        )
    </delete>

    <!-- 删除多张表的数据 -->
    <delete id="deleteMoreData">
        <foreach collection="tableList" item="tabName">
            delete from ${tabName} where mdtrt_id in(
                <foreach collection="list" item="id" separator=",">
                    #{id}
                </foreach>
            );
        </foreach>
    </delete>

</mapper>
