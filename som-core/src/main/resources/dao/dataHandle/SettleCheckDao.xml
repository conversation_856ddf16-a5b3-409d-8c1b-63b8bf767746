<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.som.dao.dataHandle.SettleCheckDao">

    <select id="QuerySettle" resultType="com.my.som.vo.dataHandle.SettleCheckVo">
        SELECT
            script_id AS scriptId,
            script_name AS checkRule,
            mean AS checkFiled,
            action_type AS checkType,
            script_enable AS enable,
            time_type AS checkTime
        FROM som_chain_script
        <where>
            <if test="checkFiled != null and checkFiled != ''">
                AND mean LIKE CONCAT('%', #{ checkFiled, jdbcType=VARCHAR }, '%')
            </if>
            <if test="checkRule != null and checkRule != ''">
                AND script_name LIKE CONCAT ('%', #{ script_name, jdbcType=VARCHAR }, '%')
            </if>
            <if test="enable != null and enable != ''">
                AND script_enable = #{ enable, jdbcType=VARCHAR }
            </if>
        </where>
        order by id desc
    </select>
    <select id="querySomChain" resultType="com.my.som.vo.dataHandle.SomChainVo">
        SELECT
            application_name  AS applicationName,
            chain_name AS chainName,
            chain_desc AS chainDesc,
            el_data AS elData,
            chain_enable AS chainEnable
        FROM som_chain
    </select>

    <update id="updateSomChainScript" parameterType="com.my.som.dto.dataHandle.SettleCheckDto">
        UPDATE som_chain_script
        <set>

            <if test="checkFiled != null">
                mean = #{checkFiled},
            </if>
            <if test="checkType != null">
                action_type = #{checkType},
            </if>
            <if test="enable != null">
                script_enable = #{enable},
            </if>
            <if test="checkTime != null">
                time_type = #{checkTime},
            </if>
        </set>
        WHERE script_id = #{scriptId}
    </update>

    <update id="updateSomChain">
        UPDATE som_chain SET el_data = #{elData} WHERE chain_name = #{chainName}
    </update>
</mapper>
