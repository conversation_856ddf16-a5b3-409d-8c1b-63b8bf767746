<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.som.dao.dataHandle.BusFundPayDao">
    <!--批量新增-->
    <insert id="insertFundPayData">
        INSERT INTO som_fund_pay (
            hi_setl_invy_id, FUND_PAY_TYPE, pool_coty_fund_pay_type, pool_coty_fund_pay_type_name, FUND_PAYAMT
        ) VALUES
        <foreach collection="fpd" item="item" index="index" separator=",">
            (#{item.settle_list_id,jdbcType=BIGINT},
            #{item.fund_pay_type,jdbcType=VARCHAR},#{item.pool_coty_fund_pay_type,jdbcType=VARCHAR},
            #{item.pool_coty_fund_pay_type_name,jdbcType=VARCHAR},#{item.fund_payamt,jdbcType=DECIMAL}
            )
        </foreach>
    </insert>

    <insert id="insertVDrgFundPay">
        INSERT INTO som_drg_fee_setl_fund_pay (
            K00, FUND_PAY_TYPE, pool_coty_fund_pay_type, pool_coty_fund_pay_type_name, FUND_PAYAMT
        ) VALUES
        <foreach collection="fpd" item="item" index="index" separator=",">
            (#{item.k00,jdbcType=VARCHAR},
            #{item.fund_pay_type,jdbcType=VARCHAR},#{item.pool_coty_fund_pay_type,jdbcType=VARCHAR},
            #{item.pool_coty_fund_pay_type_name,jdbcType=VARCHAR},#{item.fund_payamt,jdbcType=DECIMAL}
            )
        </foreach>
    </insert>

</mapper>