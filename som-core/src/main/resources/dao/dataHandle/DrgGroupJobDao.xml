<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.som.dao.dataHandle.DrgGroupJobDao">

    <!-- 清单或首页 -->
    <sql id="settleListMedicalPageChoose">
        <choose>
            <!-- 清单 -->
            <when test='queryParam.sysMedicalType == "1"'>
                SELECT SETTLE_LIST_ID
                FROM som_setl_invy_chk
                where SETTLE_LIST_ID in (
                    select id
                    from som_hi_invy_bas_info
                  <where>
                      <if test="queryParam.logId!=null and queryParam.logId!=''">
                          AND  data_log_id = #{queryParam.logId}
                      </if>
                  </where>
                )
                AND chk_stas = '0'
            </when>
            <!-- 首页，表sts_validate_error后修改SETTLE_LIST_ID为bus_medical_record的id，所以不能直接使用bus_settle_list表id  -->
            <when test='queryParam.sysMedicalType == "2"'>
                select SETTLE_LIST_ID from som_setl_invy_chk_err_rcd
                where SETTLE_LIST_ID in (
                    select id from som_init_hi_setl_invy_med_fee_info where SETTLE_LIST_ID in (
                        select id from som_hi_invy_bas_info
                        <where>
                            <if test="queryParam.logId!=null and queryParam.logId!=''">
                                AND  data_log_id = #{queryParam.logId}
                            </if>
                        </where>
                    )
                )
                group by SETTLE_LIST_ID
            </when>
        </choose>
    </sql>

    <!-- 查询重症监护时长 -->
    <select id="queryIcuInfoData" resultType="com.my.som.vo.listManagement.IcuInfoVo">
        SELECT a.hi_setl_invy_id    AS settleListId,
        a.scs_cutd_ward_type AS scs_cutd_ward_type,<!-- 重症监护病房类型 -->
        a.scs_cutd_inpool_time AS scs_cutd_inpool_time,<!-- 重症监护进入时间 -->
        a.scs_cutd_exit_time AS scs_cutd_exit_time,<!-- 重症监护退出时间 -->
        a.scs_cutd_sum_dura AS scs_cutd_sum_dura<!-- 重症监护合计时长 -->
        FROM som_setl_invy_scs_cutd_info a
        WHERE a.hi_setl_invy_id IN (
            SELECT t.ID
            FROM (
                <include refid="com.my.som.dao.dataHandle.BusDiseaseDiagnosisDao.groupLimit" />
            ) t
        )
    </select>

    <select id="getCaseYB2020Vo" resultType="com.my.som.vo.dataHandle.dataGroup.CaseYB2020Vo">
        SELECT
            a.b20 as b20,
            a.a46c as a46c,
            a.a01 as a01,
            a.d01 as d01,
            a.a14 as a14,
            a.a16 as a16,
            a.a17 as a17,
            a.b34c as b34c,
            a.a12c as a12c,
            a.a48 as a48,
            IFNULL(to_days(str_to_date(SUBSTR(replace(replace(a.b12, '-', ''),'/',''),1,8), '%Y%m%d'))  -
              to_days(str_to_date(SUBSTR(replace(replace(a.a13, '-', ''),'/',''),1,8), '%Y%m%d')),0)  as sf0100,<!--根据出生日期和入院时间计算出生天数-->
            a.data_log_id as dataLogId,
            a.id as settleListId,
            a.a03 as a03,
            a.d09 as d09,
            a.opr_date as oprDate,
            a.insuplc_admdvs as insuplcAdmdvs
        from som_hi_invy_bas_info a
        <where>
            <if test='queryParam.validateEnableType != null and queryParam.validateEnableType != "" and queryParam.validateEnableType == "1"'>
                AND a.id not in
                (
                    <include refid="settleListMedicalPageChoose" />
                )
            </if>
            <if test="queryParam.logId!=null and queryParam.logId!=''">
                AND a.DATA_LOG_ID = #{queryParam.logId}
            </if>
            <if test="queryParam.activeFlag!=null and queryParam.activeFlag!=''">
                AND a.ACTIVE_FLAG = #{queryParam.activeFlag}
            </if>
        </where>

        LIMIT #{queryParam.start} , #{queryParam.limit}

    </select>

    <select id="getCaseYBDip2020Vo" resultType="com.my.som.vo.dataHandle.dataGroup.CaseYBDip2020Vo">
        SELECT
            a.a14 as a14,
            a.a12c as a12c,
            a.b20 as b20,
            a.d01 as d01,
            a.b34c as b34c,
            a.a48 as a48,
            a.data_log_id as dataLogId,
            a.id as settleListId,
            a.a03 as a03,
            a.d09 as d09,
            a.opr_date as oprDate
        from som_hi_invy_bas_info a
        WHERE 1=1
        <if test="queryParam.logId!=null and queryParam.logId!=''">
            AND a.DATA_LOG_ID = #{queryParam.logId}
        </if>
        <if test="queryParam.activeFlag!=null and queryParam.activeFlag!=''">
            AND a.ACTIVE_FLAG = #{queryParam.activeFlag}
        </if>
        LIMIT #{queryParam.start} , #{queryParam.limit}
    </select>

    <select id="getCaseYB2020Gt60Vo" resultType="com.my.som.vo.dataHandle.dataGroup.CaseYB2020Vo">
        SELECT
        a.b20 as b20,
        a.a46c as a46c,
        a.d01 as d01,
        a.a14 as a16,
        a.a16 as a16,
        a.a17 as a17,
        a.b34c as b34c,
        a.a12c as a12c,
        a.a48 as a48,
        IFNULL(to_days(str_to_date(SUBSTR(replace(replace(a.b12, '-', ''),'/',''),1,8), '%Y%m%d'))  -
        to_days(str_to_date(SUBSTR(replace(replace(a.a13, '-', ''),'/',''),1,8), '%Y%m%d')),0)  as sf0100,<!--根据出生日期和入院时间计算出生天数-->
        a.data_log_id as dataLogId,
        a.id as settleListId,
        a.a03 as a03,
        a.d09 as d09,
        a.opr_date as oprDate
        from som_hi_invy_bas_info a
        WHERE  <![CDATA[ a.B20>60 ]]>
        <if test="queryParam.logId!=null and queryParam.logId!=''">
            AND a.DATA_LOG_ID = #{queryParam.logId}
        </if>
        <if test="queryParam.activeFlag!=null and queryParam.activeFlag!=''">
            AND a.ACTIVE_FLAG = #{queryParam.activeFlag}
        </if>
        LIMIT #{queryParam.start} , #{queryParam.limit}
    </select>

    <select id="getBusDiseaseDiagnosisList" resultType="com.my.som.model.dataHandle.SomDiag">
        SELECT
            id,
            settle_list_id as settleListId,
            seq,
            type,
            dscg_diag_codg,
            dscg_diag_name,
            dscg_diag_adm_cond,
            dscg_diag_dscg_cond
        FROM som_diag
        WHERE type = #{queryParam.diagType}
        AND settle_list_id in (
            SELECT
                a.id as settleListId
            from som_hi_invy_bas_info a
            WHERE a.id not in
            (
                select
                  settle_list_id
                from som_setl_invy_chk_err_rcd
                where 1=1
                <if test="queryParam.logId!=null and queryParam.logId!=''">
                    AND  data_log_id = #{queryParam.logId}
                </if>
                AND err_type in (
                select error_code from som_medcas_in_group_cfg
                where 1=1
                <if test="queryParam.enabFlag!=null and queryParam.enabFlag!=''">
                    AND  enab_flag=#{queryParam.enabFlag}
                </if>
                )
            )
            <if test="queryParam.logId!=null and queryParam.logId!=''">
                AND a.DATA_LOG_ID = #{queryParam.logId}
            </if>
            <if test="queryParam.activeFlag!=null and queryParam.activeFlag!=''">
                AND a.ACTIVE_FLAG = #{queryParam.activeFlag}
            </if>
            LIMIT #{queryParam.start} , #{queryParam.limit}
        )
    </select>

    <select id="getBusOperateDiagnosisList" resultType="com.my.som.model.dataHandle.SomOprnOprtInfo">
        SELECT
            id,
            settle_list_id as settleListId,
            seq,
            type,
            dscg_diag_codg,
            dscg_diag_name,
            dscg_diag_adm_cond,
            dscg_diag_dscg_cond
        FROM som_oprn_oprt_info
        WHERE settleListId in (
            SELECT
              a.id as settleListId
            from som_hi_invy_bas_info a
            WHERE a.id not in
            (
                select
                  settle_list_id
                from som_setl_invy_chk_err_rcd
                where 1=1
                <if test="queryParam.logId!=null and queryParam.logId!=''">
                    AND  data_log_id = #{queryParam.logId}
                </if>
                AND err_type in (
                select error_code from som_medcas_in_group_cfg
                where 1=1
                <if test="queryParam.enabFlag!=null and queryParam.enabFlag!=''">
                    AND  enab_flag=#{queryParam.enabFlag}
                </if>
                )
            )
            <if test="queryParam.logId!=null and queryParam.logId!=''">
                AND a.DATA_LOG_ID = #{queryParam.logId}
            </if>
            <if test="queryParam.activeFlag!=null and queryParam.activeFlag!=''">
                AND a.ACTIVE_FLAG = #{queryParam.activeFlag}
            </if>
            LIMIT #{queryParam.start} , #{queryParam.limit}
        )
    </select>
    <select id="queryTpdDrgBrustIcd" resultType="java.lang.String">
        SELECT
          grper_icd_codg
        FROM som_grper_icd_codg
        WHERE 1=1
        <if test="queryParam.grperIcdType!=null and queryParam.grperIcdType!=''">
            AND grper_icd_type = #{queryParam.grperIcdType}
        </if>
        <if test="queryParam.drgGroupType!=null and queryParam.drgGroupType!=''">
            AND grper_type = #{queryParam.drgGroupType}
        </if>
        <if test="queryParam.activeFlag!=null and queryParam.activeFlag!=''">
            AND ACTIVE_FLAG = #{queryParam.activeFlag}
        </if>
    </select>

    <!--批量插入分组记录-->
    <insert id="batchInsertStsDrgGroupRecord">
        INSERT INTO som_drg_grp_rcd (
            SETTLE_LIST_ID, medcas_type, HOSPITAL_ID, chk_flag,
            grp_stas,grper_oupt_log, grp_fale_rea,grper_type,grper_info_id,
            mdc_codg,MDC_NAME,adrg_codg,ADRG_NAME,drg_codg,DRG_NAME,grp_type,
            ipt_sumfee,ipt_sumfee_in_selfpay_amt,OPR_DATE,ACTIVE_FLAG
        ) VALUES
        <foreach collection="sdgrl" item="item" index="index" separator=",">
            (#{item.settleListId,jdbcType=BIGINT}, #{item.medcasType,jdbcType=VARCHAR}, #{item.hospitalId,jdbcType=VARCHAR},
            #{item.chkFlag,jdbcType=VARCHAR}, #{item.grpStas,jdbcType=VARCHAR}, #{item.grperOuptLog,jdbcType=LONGVARCHAR},
            #{item.grpFaleRea,jdbcType=VARCHAR}, #{item.grperType,jdbcType=VARCHAR}, #{item.grperInfoId,jdbcType=BIGINT}, #{item.mdcCodg,jdbcType=VARCHAR},
            #{item.mdcName,jdbcType=VARCHAR}, #{item.adrgCodg,jdbcType=VARCHAR}, #{item.adrgName,jdbcType=VARCHAR},
            #{item.drgCodg,jdbcType=VARCHAR}, #{item.drgName,jdbcType=VARCHAR},#{item.grpType,jdbcType=VARCHAR}, #{item.iptSumfee,jdbcType=DECIMAL},
            #{item.iptSumfeeInSelfpayAmt,jdbcType=DECIMAL},now(), #{item.activeFlag,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>

    <!--批量插入分组记录副表-->
    <insert id="batchInsertStsDrgGroupRecordAdded">
        INSERT INTO som_drg_grp_exe_rcd (
            SETTLE_LIST_ID, medcas_type, HOSPITAL_ID,
            grp_stas, grp_fale_rea, grper_type,
            grper_info_id, mdc_codg, MDC_NAME,
            adrg_codg, ADRG_NAME, drg_codg,
            DRG_NAME, err_type, bedday_pay_type,
            OPR_DATE, ACTIVE_FLAG, grper_oupt_log
        )
        VALUES
        <foreach collection="sdgral" item="item" index="index" separator=",">
            (#{item.settleListId,jdbcType=BIGINT}, #{item.medcasType,jdbcType=VARCHAR}, #{item.hospitalId,jdbcType=VARCHAR},
            #{item.grpStas,jdbcType=VARCHAR}, #{item.grpFaleRea,jdbcType=VARCHAR}, #{item.grperType,jdbcType=VARCHAR},
            #{item.grperInfoId,jdbcType=BIGINT}, #{item.mdcCodg,jdbcType=VARCHAR}, #{item.mdcName,jdbcType=VARCHAR},
            #{item.adrgCodg,jdbcType=VARCHAR}, #{item.adrgName,jdbcType=VARCHAR}, #{item.drgCodg,jdbcType=VARCHAR},
            #{item.drgName,jdbcType=VARCHAR}, #{item.errType,jdbcType=VARCHAR}, #{item.beddayPayType,jdbcType=VARCHAR},
            now(), #{item.activeFlag,jdbcType=VARCHAR}, #{item.grperOuptLog,jdbcType=LONGVARCHAR}
            )
        </foreach>
    </insert>

    <!--批量插入分组日志-->
    <insert id="batchInsertStsDrgGroupLog">
        INSERT INTO som_drg_grper_intf_trns_log (
            HOSPITAL_ID, SETTLE_LIST_ID, trns_dspo_time,
            opter, OPR_DATE, ACTIVE_FLAG,
            grper_info_id, grp_para,
            grper_oupt_log
        ) VALUES
        <foreach collection="sdgll" item="item" index="index" separator=",">
            (#{item.hospitalId,jdbcType=VARCHAR}, #{item.settleListId,jdbcType=BIGINT}, #{item.trnsDspoTime,jdbcType=DOUBLE},
            #{item.opter,jdbcType=VARCHAR}, now(), #{item.activeFlag,jdbcType=VARCHAR},
            #{item.grperInfoId,jdbcType=BIGINT}, #{item.grpPara,jdbcType=LONGVARCHAR},
            #{item.grperOuptLog,jdbcType=LONGVARCHAR})
        </foreach>
    </insert>

    <select id="queryNotPassCleanDataStsDrgGroupRecordList" resultType="com.my.som.model.dataHandle.SomDrgGrpRcd">
        SELECT
            a.id as settleListId,
            a.a03 as medcasType,
            a.hospital_id as hospitalId,
            '0' as chkFlag,      <!--默认：0为未通过-->
            '0' as grpStas,        <!--默认：0为未分组-->
            null as grperOuptLog,
            '校验未通过' as grpFaleRea,
            null as grperType,
            null as grperInfoId,
            null as mdcCodg,
            null as mdcName,
            null as adrgCodg,
            null as adrgName,
            null as drgCodg,
            null as drgName,
            a.d01 as iptSumfee,
            a.d09 as iptSumfeeInSelfpayAmt,
            a.active_flag as activeFlag
        from som_hi_invy_bas_info a
        WHERE a.id in
        (
            <include refid="settleListMedicalPageChoose"/>
        )
        <if test="queryParam.logId!=null and queryParam.logId!=''">
            AND a.DATA_LOG_ID = #{queryParam.logId}
        </if>
        <if test="queryParam.activeFlag!=null and queryParam.activeFlag!=''">
            AND a.ACTIVE_FLAG = #{queryParam.activeFlag}
        </if>
    </select>

    <select id="getMinInHosTimeAndMaxOutHosTime" resultType="java.util.Map">
        select
          min(adm_time) as mininhostime,
          max(dscg_time) as maxouthostime
        FROM som_drg_grp_info where 1=1
        <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
            AND  HOSPITAL_ID = #{queryParam.hospitalId}
        </if>
        <if test="queryParam.logId!=null and queryParam.logId!=''">
            AND  DATA_LOG_ID = #{queryParam.logId}
        </if>
    </select>

    <select id="getCombineInHosDaysGt60MedicalVo" resultType="com.my.som.vo.dataHandle.CombineInHosDaysGt60MedicalVo">
        select
          SETTLE_LIST_ID as hiSetlInvyId,
          NAME as name,
          adm_time as admTime,
          dscg_time as dscgTime,
          act_ipt as inHosDays
        from som_drg_grp_info
        where CONCAT(NAME,brdy,gend) in(
            SELECT
              CONCAT(NAME,brdy,gend)
            FROM som_drg_grp_info where drg_codg is not null
            <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
                AND  HOSPITAL_ID = #{queryParam.hospitalId}
            </if>
            <if test="queryParam.new_cy_date!=null and queryParam.new_cy_date!='' and
                      queryParam.new_ry_date!=null and queryParam.new_ry_date!=''">
                <![CDATA[  AND STR_TO_DATE(adm_time,"%Y-%m-%d") <= STR_TO_DATE(#{queryParam.new_cy_date},"%Y-%m-%d")
                           AND STR_TO_DATE(dscg_time,"%Y-%m-%d") >= STR_TO_DATE(#{queryParam.new_ry_date},"%Y-%m-%d") ]]>
            </if>
            GROUP BY NAME,brdy,gend,drg_codg
            HAVING COUNT(1)>1
        )
        <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
            AND  HOSPITAL_ID = #{queryParam.hospitalId}
        </if>
        <if test="queryParam.new_cy_date!=null and queryParam.new_cy_date!='' and
                  queryParam.new_ry_date!=null and queryParam.new_ry_date!=''">
            <![CDATA[  AND STR_TO_DATE(adm_time,"%Y-%m-%d") <= STR_TO_DATE(#{queryParam.new_cy_date},"%Y-%m-%d")
                       AND STR_TO_DATE(dscg_time,"%Y-%m-%d") >= STR_TO_DATE(#{queryParam.new_ry_date},"%Y-%m-%d") ]]>
        </if>
        ORDER BY NAME,adm_time desc
    </select>

    <!--插入分组记录副表(满足合并住院天数大于60天的病组)-->
    <insert id="insertStsDrgGroupRecordAddedForCombineInHosDaysGt60">
        INSERT INTO som_drg_grp_exe_rcd(
            SETTLE_LIST_ID, medcas_type, HOSPITAL_ID,
            grp_stas, grp_fale_rea, grper_type,
            grper_info_id, mdc_codg, MDC_NAME,
            adrg_codg, ADRG_NAME, drg_codg,
            DRG_NAME, err_type, bedday_pay_type,
            OPR_DATE, ACTIVE_FLAG, grper_oupt_log
        )SELECT
            SETTLE_LIST_ID,
            medcas_type,
            HOSPITAL_ID,
            grp_stas,
            grp_fale_rea,
            grper_type,
            grper_info_id,
            mdc_codg,
            MDC_NAME,
            adrg_codg,
            ADRG_NAME,
            drg_codg,
            DRG_NAME,
            null as err_type,
            #{queryParam.beddayPayType} as bedday_pay_type,
            now() as OPR_DATE,
            #{queryParam.activeFlag} as ACTIVE_FLAG,
            grper_oupt_log
        FROM som_drg_grp_rcd
        WHERE grp_stas = '1'
        <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
            AND  HOSPITAL_ID = #{queryParam.hospitalId}
        </if>
        <if test="queryParam.gt60MedicalSettleListIds!=null">
            AND SETTLE_LIST_ID in
            <foreach collection="queryParam.gt60MedicalSettleListIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            AND SETTLE_LIST_ID not in (
                select SETTLE_LIST_ID from som_drg_grp_exe_rcd where 1=1
                AND SETTLE_LIST_ID in
                <foreach collection="queryParam.gt60MedicalSettleListIds" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            )
        </if>
    </insert>
</mapper>
