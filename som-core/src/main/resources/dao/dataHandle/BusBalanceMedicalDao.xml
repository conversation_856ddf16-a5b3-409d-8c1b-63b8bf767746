<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.som.dao.dataHandle.BusBalanceMedicalDao">
    <!--批量新增-->
    <insert id="insertBalanceMedicalData">
        insert into som_hi_fee_setl (SETTLE_LIST_ID, mdtrt_no, PATIENT_ID,
            psn_no, NAME, gend,
            AGE, citi_idet_no, HOSPITAL_ID,
            medins_name, hosp_lv, adm_time,
            dscg_time, setl_time, act_ipt,
            ipt_bedday, psn_insu_type, insu_idet,
            med_type, setl_type, adm_diag_codg,
            adm_diag_name, main_diag_dise_codg, main_diag_dise_name,
            dscg_caty_codg_inhosp, dscg_caty_name_inhosp, act_pay_amt,
            ipt_sumfee, ipt_sumfee_in_selfpay_amt, all_ownpay_amt,
            coup_selfpay_amt, sp_coup_selfpay_amt, fit_reim_scp_amt,
            dedc_selfpay_amt, acct_pay_hi, bas_med_pool_pay_prop,
            bas_med_pool_selfpay_amt, bas_med_fund_pay,
            hifob_med_pool_pay_prop, hifob_med_pool_selfpay_amt,
            hifob_med_fund_pay, over_selfpay_amt,
            cvlserv_subs_fund_pay, med_care_psn_subs_fund_pay,
            retire_subs_fund_pay, retr_subs_fund_pay,
            entp_splm_med_fund_pay, bas_med_fund_major_dise_subs_pay,
            fili_register_psn_subs_pay, old_subs_pay,
            oth_subs_fund_pay, medins_part_amt,
            sin_dise_codg, out_flag, fili_register_poor_flag,
            day_surgery_flag, impe_clnc_path_mgt_flag,
            exe_clnc_path_stas, medcas_stas, psn_med_setl_evt_id,
            setl_doc_no, memo_info, DATA_LOG_ID,
            ACTIVE_FLAG)
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.settle_list_id,jdbcType=BIGINT}, #{item.mdtrt_no,jdbcType=VARCHAR}, #{item.patient_id,jdbcType=VARCHAR},
            #{item.psn_no,jdbcType=VARCHAR}, #{item.name,jdbcType=VARCHAR}, #{item.gend,jdbcType=VARCHAR},
            #{item.age,jdbcType=VARCHAR}, #{item.citi_idet_no,jdbcType=VARCHAR}, #{item.hospital_id,jdbcType=VARCHAR},
            #{item.medins_name,jdbcType=VARCHAR}, #{item.hosp_lv,jdbcType=VARCHAR}, #{item.adm_time,jdbcType=VARCHAR},
            #{item.dscg_time,jdbcType=VARCHAR}, #{item.setl_time,jdbcType=TIMESTAMP}, #{item.act_ipt,jdbcType=VARCHAR},
            #{item.ipt_bedday,jdbcType=VARCHAR}, #{item.psn_insu_type,jdbcType=VARCHAR}, #{item.insu_idet,jdbcType=VARCHAR},
            #{item.med_type,jdbcType=VARCHAR}, #{item.setl_type,jdbcType=VARCHAR}, #{item.adm_diag_codg,jdbcType=VARCHAR},
            #{item.adm_diag_name,jdbcType=VARCHAR}, #{item.main_diag_dise_codg,jdbcType=VARCHAR}, #{item.main_diag_dise_name,jdbcType=VARCHAR},
            #{item.dscg_caty_codg_inhosp,jdbcType=VARCHAR}, #{item.dscg_caty_name_inhosp,jdbcType=VARCHAR}, #{item.act_pay_amt,jdbcType=DECIMAL},
            #{item.ipt_sumfee,jdbcType=DECIMAL}, #{item.ipt_sumfee_in_selfpay_amt,jdbcType=DECIMAL}, #{item.all_ownpay_amt,jdbcType=DECIMAL},
            #{item.coup_selfpay_amt,jdbcType=DECIMAL}, #{item.sp_coup_selfpay_amt,jdbcType=DECIMAL}, #{item.fit_reim_scp_amt,jdbcType=DECIMAL},
            #{item.dedc_selfpay_amt,jdbcType=DECIMAL}, #{item.acct_pay_hi,jdbcType=DECIMAL}, #{item.bas_med_pool_pay_prop,jdbcType=DECIMAL},
            #{item.bas_med_pool_selfpay_amt,jdbcType=DECIMAL}, #{item.bas_med_fund_pay,jdbcType=DECIMAL},
            #{item.hifob_med_pool_pay_prop,jdbcType=DECIMAL}, #{item.hifob_med_pool_selfpay_amt,jdbcType=DECIMAL},
            #{item.hifob_med_fund_pay,jdbcType=DECIMAL}, #{item.over_selfpay_amt,jdbcType=DECIMAL},
            #{item.cvlserv_subs_fund_pay,jdbcType=DECIMAL}, #{item.med_care_psn_subs_fund_pay,jdbcType=DECIMAL},
            #{item.retire_subs_fund_pay,jdbcType=DECIMAL}, #{item.retr_subs_fund_pay,jdbcType=DECIMAL},
            #{item.entp_splm_med_fund_pay,jdbcType=DECIMAL}, #{item.bas_med_fund_major_dise_subs_pay,jdbcType=DECIMAL},
            #{item.fili_register_psn_subs_pay,jdbcType=DECIMAL}, #{item.old_subs_pay,jdbcType=DECIMAL},
            #{item.oth_subs_fund_pay,jdbcType=DECIMAL}, #{item.medins_part_amt,jdbcType=DECIMAL},
            #{item.sin_dise_codg,jdbcType=VARCHAR}, #{item.out_flag,jdbcType=VARCHAR}, #{item.fili_register_poor_flag,jdbcType=VARCHAR},
            #{item.day_surgery_flag,jdbcType=VARCHAR}, #{item.impe_clnc_path_mgt_flag,jdbcType=VARCHAR},
            #{item.exe_clnc_path_stas,jdbcType=VARCHAR}, #{item.medcas_stas,jdbcType=VARCHAR}, #{item.psn_med_setl_evt_id,jdbcType=VARCHAR},
            #{item.setl_doc_no,jdbcType=VARCHAR}, #{item.memo_info,jdbcType=VARCHAR}, #{item.data_log_id,jdbcType=BIGINT},
            #{item.active_flag,jdbcType=VARCHAR})
        </foreach>
    </insert>

    <delete id="deleteBalanceMedicalDataByOutHosTime" parameterType="java.util.HashMap">
        delete from som_hi_fee_setl where 1=1
        <if test="queryParam.begn_date!=null and queryParam.begn_date!='' and
                queryParam.expi_date!=null and queryParam.expi_date!=''">
            AND dscg_time BETWEEN #{queryParam.begn_date} and #{queryParam.expi_date}
        </if>
    </delete>

    <delete id="deleteBusDrgPaymentByOutHosTime" parameterType="java.util.HashMap">
        delete from som_drg_pay_detl where 1=1
        <if test="queryParam.begn_date!=null and queryParam.begn_date!='' and
                queryParam.expi_date!=null and queryParam.expi_date!=''">
            AND dscg_time BETWEEN #{queryParam.begn_date} and #{queryParam.expi_date}
        </if>
    </delete>

</mapper>