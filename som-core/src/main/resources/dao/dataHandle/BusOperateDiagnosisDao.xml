<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.som.dao.dataHandle.BusOperateDiagnosisDao">
    <resultMap id="BaseResultMap" type="com.my.som.model.dataHandle.SomOprnOprtInfo">
        <id column="ID" jdbcType="BIGINT" property="id" />
        <result column="SETTLE_LIST_ID" jdbcType="BIGINT" property="settleListId" />
        <result column="seq" jdbcType="INTEGER" property="seq" />
        <result column="C35C" jdbcType="VARCHAR" property="c35c" />
        <result column="C36N" jdbcType="VARCHAR" property="c36n" />
        <result column="oprn_oprt_date" jdbcType="VARCHAR" property="oprn_oprt_date" />
        <result column="oprn_oprt_lv" jdbcType="VARCHAR" property="oprn_oprt_lv" />
        <result column="C39C" jdbcType="VARCHAR" property="c39c" />
        <result column="oprn_oprt_oper_name" jdbcType="VARCHAR" property="oprn_oprt_oper_name" />
        <result column="oprn_oprt_1_asit" jdbcType="VARCHAR" property="oprn_oprt_1_asit" />
        <result column="oprn_oprt_2_asit" jdbcType="VARCHAR" property="oprn_oprt_2_asit" />
        <result column="C42" jdbcType="VARCHAR" property="c42" />
        <result column="C43" jdbcType="VARCHAR" property="c43" />
        <result column="oprn_oprt_anst_dr_code" jdbcType="VARCHAR" property="oprn_oprt_anst_dr_code" />
        <result column="oprn_oprt_anst_dr_name" jdbcType="VARCHAR" property="oprn_oprt_anst_dr_name" />
    </resultMap>
    <select id="getOperateInfoById" resultType="java.util.Map">
        SELECT
            seq as seq,
            C35C as c35c,
            C36N as c36n,
            oprn_oprt_date as oprn_oprt_date,
            oprn_oprt_lv as oprn_oprt_lv,
            C39 as c39,
            oprn_oprt_1_asit as oprn_oprt_1_asit,
            oprn_oprt_2_asit as oprn_oprt_2_asit,
            C42 as c42,
            C43 as c43,
            C44 as c44
        FROM
          som_oprn_oprt_info
        WHERE 1 = 1
        <if test="queryParam.id!=null and queryParam.id!=''">
            AND `SETTLE_LIST_ID` = #{queryParam.id}
        </if>
    </select>

    <sql id="Base_Column_List">
      ID, SETTLE_LIST_ID, seq, C35C, C36N, oprn_oprt_date, oprn_oprt_lv, C39C, oprn_oprt_oper_name, oprn_oprt_1_asit, oprn_oprt_2_asit, C42, C43, oprn_oprt_anst_dr_code,
      oprn_oprt_anst_dr_name
    </sql>
    <select id="getOperateListForValidate" resultType="com.my.som.model.dataHandle.BusOperateDiagnosisCost">
        SELECT
            x.ID as id,
            x.SETTLE_LIST_ID as settleListId,
            x.seq as seq,
            x.C35C as c35c,
            x.C36N as c36n,
            x.oprn_oprt_date as oprn_oprt_date,
            x.oprn_oprt_lv as oprn_oprt_lv,
            x.C39C as c39c,
            x.oprn_oprt_oper_name as oprn_oprt_oper_name,
            x.oprn_oprt_1_asit as oprn_oprt_1_asit,
            x.oprn_oprt_2_asit as oprn_oprt_2_asit,
            x.C42 as c42,
            x.C43 as c43,
            x.oprn_oprt_anst_dr_code as oprn_oprt_anst_dr_code,
            x.oprn_oprt_anst_dr_name as oprn_oprt_anst_dr_name,
            ROUND(IFNULL(convert(AES_DECRYPT(UNHEX(y.standard_fee),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) using utf8),0),4) as standardFee
        FROM som_oprn_oprt_info x left join
        som_code_stand_fee y on x.C35C=y.icd_codg and y.ICD_TYPE='ICD-9'
<!--        <if test="queryParam.hospLv!=null and queryParam.hospLv!=''">-->
<!--            AND y.hosp_lv = #{queryParam.hospLv}-->
<!--        </if>-->
        <if test="queryParam.active_flag!=null and queryParam.active_flag!=''">
            AND y.ACTIVE_FLAG = #{queryParam.active_flag}
        </if>
        WHERE x.C35C!='-' AND x.C35C!='--'
        AND x.SETTLE_LIST_ID IN (
            SELECT a.ID
            FROM (
                SELECT
                  ID
                FROM som_hi_invy_bas_info
                WHERE 1 = 1
                <if test="queryParam.logId!=null and queryParam.logId!=''">
                    AND `DATA_LOG_ID` = #{queryParam.logId}
                </if>
                <if test="queryParam.active_flag!=null and queryParam.active_flag!=''">
                    AND `ACTIVE_FLAG` = #{queryParam.active_flag}
                </if>
                LIMIT #{queryParam.start} , #{queryParam.limit}
            ) a
        )
    </select>

    <select id="getOperateListForGroup" resultMap="BaseResultMap">
        SELECT
            distinct
        <include refid="Base_Column_List" />
        FROM som_oprn_oprt_info
        WHERE C35C!='-' AND C35C!='--'
        AND SETTLE_LIST_ID IN (
            SELECT t.ID
            FROM (
                <include refid="com.my.som.dao.dataHandle.BusDiseaseDiagnosisDao.groupLimit" />
            ) t
        )
    </select>

    <select id="getOperateListForDipGroup" resultMap="BaseResultMap">
        SELECT
            distinct
            <include refid="Base_Column_List" />
        FROM som_oprn_oprt_info
        WHERE C35C!='-' AND C35C!='--'
        AND SETTLE_LIST_ID IN (
            SELECT t.ID
            FROM (
                SELECT
                  ID
                FROM som_hi_invy_bas_info a
                WHERE 1=1
                <if test="queryParam.logId!=null and queryParam.logId!=''">
                    AND a.DATA_LOG_ID = #{queryParam.logId}
                </if>
                <if test="queryParam.activeFlag!=null and queryParam.activeFlag!=''">
                    AND a.ACTIVE_FLAG = #{queryParam.activeFlag}
                </if>
                LIMIT #{queryParam.start} , #{queryParam.limit}
            ) t
        )
    </select>

    <select id="getOperateListForGroupGt60" resultMap="BaseResultMap">
        SELECT
            distinct
            <include refid="Base_Column_List" />
        FROM som_oprn_oprt_info
        WHERE SETTLE_LIST_ID IN (
            SELECT t.ID
            FROM (
                SELECT
                  ID
                FROM som_hi_invy_bas_info a
                WHERE <![CDATA[ a.B20>60 ]]>
                <if test="queryParam.logId!=null and queryParam.logId!=''">
                    AND a.DATA_LOG_ID = #{queryParam.logId}
                </if>
                <if test="queryParam.activeFlag!=null and queryParam.activeFlag!=''">
                    AND a.ACTIVE_FLAG = #{queryParam.activeFlag}
                </if>
                LIMIT #{queryParam.start} , #{queryParam.limit}
            ) t
        )
    </select>

    <!-- 获取病案首页手术信息 -->
    <select id="getMedicalRecordOperation"
            resultType="com.my.som.model.dataHandle.BusOperateDiagnosisCost">
        SELECT
            x.ID as id,
            x.medcas_hmpg_id as settleListId,
            x.seq as seq,
            x.C35C as c35c,
            x.C36N as c36n,
            x.oprn_oprt_date as oprn_oprt_date,
            x.oprn_oprt_lv as oprn_oprt_lv,
            x.C39C as c39c,
            x.oprn_oprt_oper_name as oprn_oprt_oper_name,
            x.oprn_oprt_1_asit as oprn_oprt_1_asit,
            x.oprn_oprt_2_asit as oprn_oprt_2_asit,
            x.C42 as c42,
            x.C43 as c43,
            x.oprn_oprt_anst_dr_code as oprn_oprt_anst_dr_code,
            x.oprn_oprt_anst_dr_name as oprn_oprt_anst_dr_name,
            ROUND(IFNULL(convert(AES_DECRYPT(UNHEX(y.standard_fee),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) using utf8),0),4) as standardFee
        FROM som_medcas_hmpg_oprn_info x left join
        som_code_stand_fee y on x.C35C=y.icd_codg and y.ICD_TYPE='ICD-9'
        <if test="queryParam.active_flag!=null and queryParam.active_flag!=''">
            AND y.ACTIVE_FLAG = #{queryParam.active_flag}
        </if>
        WHERE x.C35C!='-' AND x.C35C!='--'
        AND x.medcas_hmpg_id IN (
            SELECT id
            FROM som_init_hi_setl_invy_med_fee_info
            WHERE settle_list_id IN(
                SELECT a.ID
                FROM (
                    SELECT
                      ID
                    FROM som_hi_invy_bas_info
                    WHERE 1 = 1
                    <if test="queryParam.logId!=null and queryParam.logId!=''">
                        AND `DATA_LOG_ID` = #{queryParam.logId}
                    </if>
                    <if test="queryParam.active_flag!=null and queryParam.active_flag!=''">
                        AND `ACTIVE_FLAG` = #{queryParam.active_flag}
                    </if>
                    LIMIT #{queryParam.start} , #{queryParam.limit}
                ) a
            )
        )
    </select>

    <!--批量新增-->
    <insert id="insertOperateData">
        INSERT INTO som_oprn_oprt_info (
          <include refid="somOprnOprtInfo"></include>
        ) VALUES
        <foreach collection="od" item="item" index="index" separator=",">
            (#{item.settle_list_id,jdbcType=BIGINT},#{item.seq,jdbcType=INTEGER},
            #{item.c35c,jdbcType=VARCHAR},#{item.c36n,jdbcType=VARCHAR},#{item.oprn_oprt_date,jdbcType=VARCHAR},
            #{item.oprn_oprt_lv,jdbcType=VARCHAR},#{item.c39c,jdbcType=VARCHAR},#{item.oprn_oprt_oper_name,jdbcType=VARCHAR},
            #{item.oprn_oprt_1_asit,jdbcType=VARCHAR},#{item.oprn_oprt_2_asit,jdbcType=VARCHAR},#{item.c42,jdbcType=VARCHAR},
            #{item.c43,jdbcType=VARCHAR},#{item.oprn_oprt_anst_dr_code,jdbcType=VARCHAR},#{item.oprn_oprt_anst_dr_name,jdbcType=VARCHAR},
            #{item.oprn_oprt_begntime,jdbcType=VARCHAR}, #{item.oprn_oprt_endtime,jdbcType=VARCHAR},
            #{item.anst_begntime,jdbcType=VARCHAR}, #{item.anst_endtime,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>

    <!--批量新增-->
    <insert id="insertOperateRecordData">
        INSERT INTO som_init_oprn_oprt_info (
        K00, seq,    C35C,   C36N,   oprn_oprt_date,    oprn_oprt_lv,    C39C,   oprn_oprt_oper_name,   oprn_oprt_1_asit,    oprn_oprt_2_asit,
        C42,    C43,    oprn_oprt_anst_dr_code,   oprn_oprt_anst_dr_name,   OPRN_OPRT_BEGNTIME, OPRN_OPRT_ENDTIME,  ANST_BEGNTIME,
        ANST_ENDTIME
        ) VALUES
        <foreach collection="od" item="item" index="index" separator=",">
            (#{item.k00,jdbcType=VARCHAR},#{item.seq,jdbcType=INTEGER},
            #{item.c35c,jdbcType=VARCHAR},#{item.c36n,jdbcType=VARCHAR},#{item.oprn_oprt_date,jdbcType=VARCHAR},
            #{item.oprn_oprt_lv,jdbcType=VARCHAR},#{item.c39c,jdbcType=VARCHAR},#{item.oprn_oprt_oper_name,jdbcType=VARCHAR},
            #{item.oprn_oprt_1_asit,jdbcType=VARCHAR},#{item.oprn_oprt_2_asit,jdbcType=VARCHAR},#{item.c42,jdbcType=VARCHAR},
            #{item.c43,jdbcType=VARCHAR},#{item.oprn_oprt_anst_dr_code,jdbcType=VARCHAR},#{item.oprn_oprt_anst_dr_name,jdbcType=VARCHAR},
            #{item.oprn_oprt_begntime,jdbcType=VARCHAR}, #{item.oprn_oprt_endtime,jdbcType=VARCHAR},
            #{item.anst_begntime,jdbcType=VARCHAR}, #{item.anst_endtime,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>

    <!--批量新增-->
    <insert id="insertVDrgOperate">
        INSERT INTO som_drg_fee_setl_oprn (
          K00, seq, C35C, C36N, oprn_oprt_date, oprn_oprt_lv, C39C, oprn_oprt_oper_name, oprn_oprt_1_asit, oprn_oprt_2_asit, C42, C43, oprn_oprt_anst_dr_code,oprn_oprt_anst_dr_name
        ) VALUES
        <foreach collection="od" item="item" index="index" separator=",">
            (#{item.k00,jdbcType=VARCHAR},#{item.seq,jdbcType=INTEGER},
            #{item.c35c,jdbcType=VARCHAR},#{item.c36n,jdbcType=VARCHAR},#{item.oprn_oprt_date,jdbcType=VARCHAR},
            #{item.oprn_oprt_lv,jdbcType=VARCHAR},#{item.c39c,jdbcType=VARCHAR},#{item.oprn_oprt_oper_name,jdbcType=VARCHAR},
            #{item.oprn_oprt_1_asit,jdbcType=VARCHAR},#{item.oprn_oprt_2_asit,jdbcType=VARCHAR},#{item.c42,jdbcType=VARCHAR},
            #{item.c43,jdbcType=VARCHAR},#{item.oprn_oprt_anst_dr_code,jdbcType=VARCHAR},#{item.oprn_oprt_anst_dr_name,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>

    <insert id="bacthInsertBusOperateDiagnosis">
        INSERT INTO som_oprn_oprt_info (
        SETTLE_LIST_ID, seq, C35C, C36N, oprn_oprt_date, oprn_oprt_lv, C39C, oprn_oprt_oper_name, oprn_oprt_1_asit, oprn_oprt_2_asit, C42, C43, oprn_oprt_anst_dr_code,oprn_oprt_anst_dr_name
        ) VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.settleListId,jdbcType=BIGINT},#{item.seq,jdbcType=INTEGER},
            #{item.c35c,jdbcType=VARCHAR},#{item.c36n,jdbcType=VARCHAR},#{item.oprn_oprt_date,jdbcType=VARCHAR},
            #{item.oprn_oprt_lv,jdbcType=VARCHAR},#{item.c39c,jdbcType=VARCHAR},#{item.oprn_oprt_oper_name,jdbcType=VARCHAR},
            #{item.oprn_oprt_1_asit,jdbcType=VARCHAR},#{item.oprn_oprt_2_asit,jdbcType=VARCHAR},#{item.c42,jdbcType=VARCHAR},
            #{item.c43,jdbcType=VARCHAR},#{item.oprn_oprt_anst_dr_code,jdbcType=VARCHAR},#{item.oprn_oprt_anst_dr_name,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>

    <sql id="somOprnOprtInfo">
        SETTLE_LIST_ID, seq,    C35C,   C36N,   oprn_oprt_date,    oprn_oprt_lv,    C39C,   oprn_oprt_oper_name,   oprn_oprt_1_asit,    oprn_oprt_2_asit,
        C42,    C43,    oprn_oprt_anst_dr_code,   oprn_oprt_anst_dr_name,   OPRN_OPRT_BEGNTIME, OPRN_OPRT_ENDTIME,  ANST_BEGNTIME,
        ANST_ENDTIME
    </sql>
</mapper>
