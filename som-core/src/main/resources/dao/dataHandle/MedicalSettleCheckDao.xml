<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.som.dao.dataHandle.MedicalSettleCheckDao">
    <sql id="settleListValidateQuery">
        SELECT ID AS id,
               chk_name AS chkName,
               fld_code AS fldCode,
               verf_rule AS verfRule,
               err_dscr AS errDscr,
               chk_type_type AS chkTypeType,
               codes_code AS dictCode,
               enab_flag AS enabFlag,
               chk_regl AS chkRegl,
               CONVERTER AS converter,
               `LENGTH` AS length,
               `TYPE` AS type,
               skip_chk_flag AS skipChkFlag,
               invy_conv AS transformation
        FROM som_medcas_in_group_rule_cfg
    </sql>

    <select id="QueryMedical" resultType="com.my.som.vo.dataHandle.MedicalSettleCheckVo">
        <include refid="settleListValidateQuery" />
        <where>
            <if test="fldCode != null and fldCode != ''">
              AND fld_code LIKE CONCAT('%', #{ fldCode, jdbcType=VARCHAR }, '%')
            </if>
            <if test="chkName != null and chkName != ''">
             AND chk_name LIKE CONCAT ('%', #{ chkName, jdbcType=VARCHAR }, '%')
            </if>
            <if test="enabFlag != null and enabFlag != ''">
             AND enab_flag = #{ enabFlag, jdbcType=VARCHAR }
            </if>
        </where>
    </select>

    <update id="updateMedical">
        UPDATE som_medcas_in_group_rule_cfg
        SET enab_flag = #{enabFlag,jdbcType=VARCHAR}
        WHERE ID = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateAllMedical">
        UPDATE som_medcas_in_group_rule_cfg
        SET
            fld_code = #{fldCode,jdbcType=VARCHAR},
            chk_name = #{chkName,jdbcType=VARCHAR},
            verf_rule = #{verfRule,jdbcType=VARCHAR},
            err_dscr = #{errDscr,jdbcType=VARCHAR},
            chk_type_type = #{chkTypeType,jdbcType=VARCHAR},
            codes_code = #{dictCode,jdbcType=VARCHAR},
            chk_regl = #{chkRegl,jdbcType=VARCHAR},
            LENGTH = #{length,jdbcType=VARCHAR},
            CONVERTER = #{converter,jdbcType=VARCHAR},
            TYPE = #{type,jdbcType=VARCHAR},
            invy_conv = #{transformation,jdbcType=VARCHAR}
        WHERE ID = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateStart">
        <foreach collection="list" item="item" separator=";">
            UPDATE som_medcas_in_group_rule_cfg
            SET enab_flag = #{item.enabFlag,jdbcType=VARCHAR}
            WHERE ID = #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <update id="updateCode">
        UPDATE som_medcas_in_group_rule_cfg
        SET
            codes_code = #{dictCode,jdbcType=VARCHAR},
            LENGTH = #{length,jdbcType=VARCHAR},
            chk_regl = #{chkRegl,jdbcType=VARCHAR},
            CONVERTER = #{converter,jdbcType=VARCHAR},
            enab_flag = #{enabFlag,jdbcType=VARCHAR}
        WHERE
            ID = #{id,jdbcType=BIGINT}
    </update>
    <update id="update">
        UPDATE som_medcas_in_group_rule_cfg
        SET
        <trim suffixOverrides=",">
            <if test="dictCode != null and dictCode != ''">
                codes_code = CONCAT(SUBSTR(codes_code,1,LENGTH(codes_code)-1),#{dictCode,jdbcType=VARCHAR}),
            </if>
            <if test="chkRegl != null and chkRegl != ''">
               chk_regl = CONCAT(SUBSTR(chk_regl,1,LENGTH(chk_regl)-1),#{chkRegl,jdbcType=VARCHAR}),
            </if>
            <if test="length != null and length != ''">
                `LENGTH` = CONCAT(SUBSTR(`LENGTH`,1,LENGTH(`LENGTH`)-1),#{length,jdbcType=VARCHAR}),
            </if>
            <if test="converter != null and converter != ''">
                CONVERTER = CONCAT(SUBSTR(CONVERTER,1,LENGTH(CONVERTER)-1),#{converter,jdbcType=VARCHAR}),
            </if>
            <if test="transformation != null and transformation != ''">
                invy_conv = CONCAT(SUBSTR(invy_conv,1,LENGTH(invy_conv)-1),#{transformation,jdbcType=VARCHAR}),
            </if>
                enab_flag = #{enabFlag,jdbcType=VARCHAR}
        </trim>
        WHERE
        ID = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateStartFlag">
        UPDATE som_medcas_in_group_rule_cfg
        SET
            <trim suffixOverrides=",">
                <if test="dictCode != null and dictCode != ''">
                    codes_code = CONCAT(SUBSTR(codes_code,1,LENGTH(codes_code)-1),#{dictCode,jdbcType=VARCHAR}),
                </if>
                <if test="chkRegl != null and chkRegl != ''">
                    chk_regl = CONCAT(SUBSTR(chk_regl,1,LENGTH(chk_regl)-1),#{chkRegl,jdbcType=VARCHAR}),
                </if>
                <if test="length != null and length != ''">
                    `LENGTH` = CONCAT(SUBSTR(`LENGTH`,1,LENGTH(`LENGTH`)-1),#{length,jdbcType=VARCHAR}),
                </if>
                <if test="converter != null and converter != ''">
                    CONVERTER = CONCAT(SUBSTR(CONVERTER,1,LENGTH(CONVERTER)-1),#{converter,jdbcType=VARCHAR}),
                </if>
                enab_flag = #{enabFlag,jdbcType=VARCHAR}
            </trim>

        WHERE ID = #{id,jdbcType=BIGINT}
    </update>
    <insert id="insertMedical">
        INSERT INTO som_medcas_in_group_rule_cfg
        (
         TYPE,
         chk_name,
         fld_code,
         verf_rule,
         err_dscr,
         chk_type_type,
         codes_code,
         enab_flag,
         chk_regl,
         LENGTH,
         CONVERTER,
         invy_conv
         )
        VALUES
        (
         #{type,jdbcType=VARCHAR},
         #{chkName,jdbcType=VARCHAR},
         #{fldCode,jdbcType=VARCHAR},
         #{verfRule,jdbcType=VARCHAR},
         #{errDscr,jdbcType=VARCHAR},
         #{chkTypeType,jdbcType=VARCHAR},
         #{dictCode,jdbcType=VARCHAR},
         #{enabFlag,jdbcType=VARCHAR},
         #{chkRegl,jdbcType=VARCHAR},
         #{length,jdbcType=VARCHAR},
         #{converter,jdbcType=VARCHAR},
         #{transformation,jdbcType=VARCHAR},
         )
    </insert>
    <insert id="insertReduc">
<!--        INSERT INTO som_medcas_in_group_rule_cfg SELECT * FROM som_medcas_in_group_rule_cfg_bac;-->
        INSERT INTO som_medcas_in_group_rule_cfg (id,chk_name,fld_code,verf_rule,err_dscr,chk_type_type,codes_code,chk_regl,length,type,enab_flag,converter,skip_chk_flag,invy_conv)
        SELECT id,chk_name,fld_code,verf_rule,err_dscr,chk_type_type,codes_code,chk_regl,length,type,enab_flag,converter,skip_chk_flag,invy_conv
        FROM som_medcas_in_group_rule_cfg_bac
    </insert>
    <delete id="deleteMedical">
        DELETE FROM som_medcas_in_group_rule_cfg
        WHERE ID = #{id,jdbcType=BIGINT}
    </delete>
    <delete id="reduction">
        TRUNCATE TABLE som_medcas_in_group_rule_cfg;
    </delete>
    <select id="queryDataByType" resultType="com.my.som.vo.dataHandle.MedicalSettleCheckVo">
        SELECT ID AS id,
               SUBSTR(codes_code,-1,1) AS dictCode,
               SUBSTR(`LENGTH`,-1,1) AS length,
               SUBSTR(chk_regl,-1,1) AS chkRegl,
               SUBSTR(chk_regl,-1,1) AS converter
        FROM som_medcas_in_group_rule_cfg
        WHERE `TYPE` = #{type,jdbcType=VARCHAR}
    </select>
    <select id="queryDict" resultType="com.my.som.vo.dataHandle.MedicalSettleCheckVo">
        SELECT
               labl_name AS type,
               data_val AS dataVal
        FROM  som_sys_dic WHERE code_type = "SETTLE_LIST_VALIDATE_TYPE"
    </select>
</mapper>
