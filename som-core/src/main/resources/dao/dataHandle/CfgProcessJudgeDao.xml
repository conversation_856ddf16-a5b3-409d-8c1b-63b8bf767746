<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.som.dao.dataHandle.CfgProcessJudgeDao">
    <!--根据开启的流程步骤，进行动态计算每一步的进度值-->
    <select id="queryCurProgress" resultType="com.my.som.vo.dataHandle.DataHandleProgressVo">
        select
            a.taskName as taskName,
            case when b.total=1 then 0 else cast(100/b.total*(a.rownum-1) as signed) end as startProgress,
            case when a.rownum=b.total then 100 else cast(100/b.total*a.rownum as signed) end as endProgress

        from (
            SELECT
              @rownum:=@rownum+1 as rownum,
              task_name as taskName
            from  (SELECT @rownum:=0) r,som_prcs_seq
            where 1=1
            <if test="queryParam.enab_flag!=null and queryParam.enab_flag!=''">
                and `ENAB_FLAG` = #{queryParam.enab_flag}
            </if>
            <!--<if test="queryParam.hospital_id!=null and queryParam.hospital_id!=''">
                AND `HOSPITAL_ID` = #{queryParam.hospital_id}
            </if> -->
            order by exe_seq
        )a
        <!-- 子查询 a 的作用更任务生成一个序号，并选择任务名-->
        cross join (
            SELECT
              count(1) as total
            from  som_prcs_seq
            where 1=1
            <if test="queryParam.enab_flag!=null and queryParam.enab_flag!=''">
                AND `ENAB_FLAG` = #{queryParam.enab_flag}
            </if>
            <!--<if test="queryParam.hospital_id!=null and queryParam.hospital_id!=''">
                AND `HOSPITAL_ID` = #{queryParam.hospital_id}
            </if> -->
        )b
        <!-- 子查询b 用与统计满足条件的任务总数-->
</select>
</mapper>
