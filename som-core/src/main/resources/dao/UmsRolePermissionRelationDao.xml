<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.som.dao.UmsRolePermissionRelationDao">
    <!--批量新增回写主键支持-->
    <insert id="insertList">
        INSERT INTO som_back_user_role_perm_rlts (role_id, permission_id) VALUES
        <foreach collection="list" separator="," item="item" index="index">
            (#{item.roleId,jdbcType=BIGINT},
            #{item.permissionId,jdbcType=BIGINT})
        </foreach>
    </insert>
    <select id="getPermissionList" resultMap="com.my.som.mapper.SomBackUserPermMapper.BaseResultMap">
        SELECT
            p.*
        FROM
            som_back_user_role_perm_rlts r
            LEFT JOIN som_back_user_perm p ON r.permission_id = p.id
        WHERE
            r.role_id = #{roleId};
    </select>
</mapper>