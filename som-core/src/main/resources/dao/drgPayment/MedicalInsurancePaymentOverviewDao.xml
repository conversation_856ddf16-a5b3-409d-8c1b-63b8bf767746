<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.som.dao.drgPayment.MedicalInsurancePaymentOverviewDao">
    <select id="getTopCountInfo" resultType="com.my.som.vo.drgPayment.PaymentOverviewTopCountInfo">
        select
            SUM(CASE WHEN
                <![CDATA[ a.setl_time >= STR_TO_DATE(#{queryParam.js_start_date},"%Y-%m-%d %H:%i:%s") ]]>
                AND <![CDATA[ a.setl_time <= STR_TO_DATE(#{queryParam.js_end_date},"%Y-%m-%d %H:%i:%s") ]]>
                THEN a.act_pay_amt ELSE 0 END) AS totalActualPayCost,
            SUM(CASE WHEN
                <![CDATA[ a.setl_time >= STR_TO_DATE(#{queryParam.js_start_date},"%Y-%m-%d %H:%i:%s") ]]>
                AND <![CDATA[ a.setl_time <= STR_TO_DATE(#{queryParam.js_end_date},"%Y-%m-%d %H:%i:%s") ]]>
                AND a.psn_insu_type='1'
                THEN a.act_pay_amt ELSE 0 END) AS totalActualPayCost1,
            SUM(CASE WHEN
                <![CDATA[ a.setl_time >= STR_TO_DATE(#{queryParam.js_start_date},"%Y-%m-%d %H:%i:%s") ]]>
                AND <![CDATA[ a.setl_time <= STR_TO_DATE(#{queryParam.js_end_date},"%Y-%m-%d %H:%i:%s") ]]>
                AND a.psn_insu_type='2'
                THEN a.act_pay_amt ELSE 0 END) AS totalActualPayCost2,
            SUM(CASE WHEN
                <![CDATA[ a.setl_time >= STR_TO_DATE(#{queryParam.lastMonth_js_start_date},"%Y-%m-%d %H:%i:%s") ]]>
                AND <![CDATA[ a.setl_time <= STR_TO_DATE(#{queryParam.lastMonth_js_end_date},"%Y-%m-%d %H:%i:%s") ]]>
                THEN a.act_pay_amt ELSE 0 END) AS lastMonthTotalActualPayCost,
            SUM(CASE WHEN
                <![CDATA[ a.setl_time >= STR_TO_DATE(#{queryParam.lastYear_js_start_date},"%Y-%m-%d %H:%i:%s") ]]>
                AND <![CDATA[ a.setl_time <= STR_TO_DATE(#{queryParam.lastYear_js_end_date},"%Y-%m-%d %H:%i:%s") ]]>
                THEN a.act_pay_amt ELSE 0 END) AS lastYearTotalActualPayCost,
            SUM(CASE WHEN
                <![CDATA[ a.setl_time >= STR_TO_DATE(#{queryParam.js_start_date},"%Y-%m-%d %H:%i:%s") ]]>
                AND <![CDATA[ a.setl_time <= STR_TO_DATE(#{queryParam.js_end_date},"%Y-%m-%d %H:%i:%s") ]]>
                THEN a.drg_pay_amt ELSE 0 END) AS totalDrgPayCost,
            SUM(CASE WHEN
                <![CDATA[ a.setl_time >= STR_TO_DATE(#{queryParam.js_start_date},"%Y-%m-%d %H:%i:%s") ]]>
                AND <![CDATA[ a.setl_time <= STR_TO_DATE(#{queryParam.js_end_date},"%Y-%m-%d %H:%i:%s") ]]>
                AND a.psn_insu_type='1'
                THEN a.drg_pay_amt ELSE 0 END) AS totalDrgPayCost1,
            SUM(CASE WHEN
                <![CDATA[ a.setl_time >= STR_TO_DATE(#{queryParam.js_start_date},"%Y-%m-%d %H:%i:%s") ]]>
                AND <![CDATA[ a.setl_time <= STR_TO_DATE(#{queryParam.js_end_date},"%Y-%m-%d %H:%i:%s") ]]>
                AND a.psn_insu_type='2'
                THEN a.drg_pay_amt ELSE 0 END) AS totalDrgPayCost2,
            SUM(CASE WHEN
                <![CDATA[ a.setl_time >= STR_TO_DATE(#{queryParam.lastMonth_js_start_date},"%Y-%m-%d %H:%i:%s") ]]>
                AND <![CDATA[ a.setl_time <= STR_TO_DATE(#{queryParam.lastMonth_js_end_date},"%Y-%m-%d %H:%i:%s") ]]>
                THEN a.drg_pay_amt ELSE 0 END) AS lastMonthTotalDrgPayCost,
            SUM(CASE WHEN
                <![CDATA[ a.setl_time >= STR_TO_DATE(#{queryParam.lastYear_js_start_date},"%Y-%m-%d %H:%i:%s") ]]>
                AND <![CDATA[ a.setl_time <= STR_TO_DATE(#{queryParam.lastYear_js_end_date},"%Y-%m-%d %H:%i:%s") ]]>
                THEN a.drg_pay_amt ELSE 0 END) AS lastYearTotalDrgPayCost,
            SUM(CASE WHEN
                <![CDATA[ a.setl_time >= STR_TO_DATE(#{queryParam.js_start_date},"%Y-%m-%d %H:%i:%s") ]]>
                AND <![CDATA[ a.setl_time <= STR_TO_DATE(#{queryParam.js_end_date},"%Y-%m-%d %H:%i:%s") ]]>
                THEN a.init_item_pay_amt ELSE 0 END) AS totalProjectPayCost,
            SUM(CASE WHEN
                <![CDATA[ a.setl_time >= STR_TO_DATE(#{queryParam.js_start_date},"%Y-%m-%d %H:%i:%s") ]]>
                AND <![CDATA[ a.setl_time <= STR_TO_DATE(#{queryParam.js_end_date},"%Y-%m-%d %H:%i:%s") ]]>
                AND a.psn_insu_type='1'
                THEN a.init_item_pay_amt ELSE 0 END) AS totalProjectPayCost1,
            SUM(CASE WHEN
                <![CDATA[ a.setl_time >= STR_TO_DATE(#{queryParam.js_start_date},"%Y-%m-%d %H:%i:%s") ]]>
                AND <![CDATA[ a.setl_time <= STR_TO_DATE(#{queryParam.js_end_date},"%Y-%m-%d %H:%i:%s") ]]>
                AND a.psn_insu_type='2'
                THEN a.init_item_pay_amt ELSE 0 END) AS totalProjectPayCost2,
            SUM(CASE WHEN
                <![CDATA[ a.setl_time >= STR_TO_DATE(#{queryParam.lastMonth_js_start_date},"%Y-%m-%d %H:%i:%s") ]]>
                AND <![CDATA[ a.setl_time <= STR_TO_DATE(#{queryParam.lastMonth_js_end_date},"%Y-%m-%d %H:%i:%s") ]]>
                THEN a.init_item_pay_amt ELSE 0 END) AS lastMonthTotalProjectPayCost,
            SUM(CASE WHEN
                <![CDATA[ a.setl_time >= STR_TO_DATE(#{queryParam.lastYear_js_start_date},"%Y-%m-%d %H:%i:%s") ]]>
                AND <![CDATA[ a.setl_time <= STR_TO_DATE(#{queryParam.lastYear_js_end_date},"%Y-%m-%d %H:%i:%s") ]]>
                THEN a.init_item_pay_amt ELSE 0 END) AS lastYearTotalProjectPayCost,
            SUM(CASE WHEN
                <![CDATA[ a.setl_time >= STR_TO_DATE(#{queryParam.js_start_date},"%Y-%m-%d %H:%i:%s") ]]>
                AND <![CDATA[ a.setl_time <= STR_TO_DATE(#{queryParam.js_end_date},"%Y-%m-%d %H:%i:%s") ]]>
                AND a.PAY_TYPE='0'
                THEN a.ipt_sumfee ELSE 0 END) AS notPayCost,
            SUM(CASE WHEN
                <![CDATA[ a.setl_time >= STR_TO_DATE(#{queryParam.lastMonth_js_start_date},"%Y-%m-%d %H:%i:%s") ]]>
                AND <![CDATA[ a.setl_time <= STR_TO_DATE(#{queryParam.lastMonth_js_end_date},"%Y-%m-%d %H:%i:%s") ]]>
                AND a.PAY_TYPE='0'
                THEN a.ipt_sumfee ELSE 0 END) AS lastMonthNotPayCost,
            SUM(CASE WHEN
                <![CDATA[ a.setl_time >= STR_TO_DATE(#{queryParam.lastYear_js_start_date},"%Y-%m-%d %H:%i:%s") ]]>
                AND <![CDATA[ a.setl_time <= STR_TO_DATE(#{queryParam.lastYear_js_end_date},"%Y-%m-%d %H:%i:%s") ]]>
                AND a.PAY_TYPE='0'
                THEN a.ipt_sumfee ELSE 0 END) AS lastYearNotPayCost,

            COUNT(CASE WHEN
                <![CDATA[ a.setl_time >= STR_TO_DATE(#{queryParam.js_start_date},"%Y-%m-%d %H:%i:%s") ]]>
                AND <![CDATA[ a.setl_time <= STR_TO_DATE(#{queryParam.js_end_date},"%Y-%m-%d %H:%i:%s") ]]>
                THEN 1 ELSE NULL END) AS totalPayNum,
            COUNT(CASE WHEN
                <![CDATA[ a.setl_time >= STR_TO_DATE(#{queryParam.js_start_date},"%Y-%m-%d %H:%i:%s") ]]>
                AND <![CDATA[ a.setl_time <= STR_TO_DATE(#{queryParam.js_end_date},"%Y-%m-%d %H:%i:%s") ]]>
                AND a.psn_insu_type='1'
                THEN 1 ELSE NULL END) AS totalPayNum1,
            COUNT(CASE WHEN
                <![CDATA[ a.setl_time >= STR_TO_DATE(#{queryParam.js_start_date},"%Y-%m-%d %H:%i:%s") ]]>
                AND <![CDATA[ a.setl_time <= STR_TO_DATE(#{queryParam.js_end_date},"%Y-%m-%d %H:%i:%s") ]]>
                AND a.psn_insu_type='2'
                THEN 1 ELSE NULL END) AS totalPayNum2,
            COUNT(CASE WHEN
                <![CDATA[ a.setl_time >= STR_TO_DATE(#{queryParam.lastMonth_js_start_date},"%Y-%m-%d %H:%i:%s") ]]>
                AND <![CDATA[ a.setl_time <= STR_TO_DATE(#{queryParam.lastMonth_js_end_date},"%Y-%m-%d %H:%i:%s") ]]>
                THEN 1 ELSE NULL END) AS lastMonthTotalPayNum,
            COUNT(CASE WHEN
                <![CDATA[ a.setl_time >= STR_TO_DATE(#{queryParam.lastYear_js_start_date},"%Y-%m-%d %H:%i:%s") ]]>
                AND <![CDATA[ a.setl_time <= STR_TO_DATE(#{queryParam.lastYear_js_end_date},"%Y-%m-%d %H:%i:%s") ]]>
                THEN 1 ELSE NULL END) AS lastYearTotalPayNum,
            COUNT(CASE WHEN
                <![CDATA[ a.setl_time >= STR_TO_DATE(#{queryParam.js_start_date},"%Y-%m-%d %H:%i:%s") ]]>
                AND <![CDATA[ a.setl_time <= STR_TO_DATE(#{queryParam.js_end_date},"%Y-%m-%d %H:%i:%s") ]]>
                AND PAY_TYPE='2'
                THEN 1 ELSE NULL END) AS totalDrgPayNum,
            COUNT(CASE WHEN
                <![CDATA[ a.setl_time >= STR_TO_DATE(#{queryParam.js_start_date},"%Y-%m-%d %H:%i:%s") ]]>
                AND <![CDATA[ a.setl_time <= STR_TO_DATE(#{queryParam.js_end_date},"%Y-%m-%d %H:%i:%s") ]]>
                AND PAY_TYPE='2' AND a.psn_insu_type='1'
                THEN 1 ELSE NULL END) AS totalDrgPayNum1,
            COUNT(CASE WHEN
                <![CDATA[ a.setl_time >= STR_TO_DATE(#{queryParam.js_start_date},"%Y-%m-%d %H:%i:%s") ]]>
                AND <![CDATA[ a.setl_time <= STR_TO_DATE(#{queryParam.js_end_date},"%Y-%m-%d %H:%i:%s") ]]>
                AND PAY_TYPE='2' AND a.psn_insu_type='2'
                THEN 1 ELSE NULL END) AS totalDrgPayNum2,
            COUNT(CASE WHEN
                <![CDATA[ a.setl_time >= STR_TO_DATE(#{queryParam.lastMonth_js_start_date},"%Y-%m-%d %H:%i:%s") ]]>
                AND <![CDATA[ a.setl_time <= STR_TO_DATE(#{queryParam.lastMonth_js_end_date},"%Y-%m-%d %H:%i:%s") ]]>
                AND PAY_TYPE='2'
                THEN 1 ELSE NULL END) AS lastMonthTotalDrgPayNum,
            COUNT(CASE WHEN
                <![CDATA[ a.setl_time >= STR_TO_DATE(#{queryParam.lastYear_js_start_date},"%Y-%m-%d %H:%i:%s") ]]>
                AND <![CDATA[ a.setl_time <= STR_TO_DATE(#{queryParam.lastYear_js_end_date},"%Y-%m-%d %H:%i:%s") ]]>
                AND PAY_TYPE='2'
                THEN 1 ELSE NULL END) AS lastYearTotalDrgPayNum,
            COUNT(CASE WHEN
                <![CDATA[ a.setl_time >= STR_TO_DATE(#{queryParam.js_start_date},"%Y-%m-%d %H:%i:%s") ]]>
                AND <![CDATA[ a.setl_time <= STR_TO_DATE(#{queryParam.js_end_date},"%Y-%m-%d %H:%i:%s") ]]>
                AND PAY_TYPE='1'
                THEN 1 ELSE NULL END) AS totalProjectPayNum,
            COUNT(CASE WHEN
                <![CDATA[ a.setl_time >= STR_TO_DATE(#{queryParam.js_start_date},"%Y-%m-%d %H:%i:%s") ]]>
                AND <![CDATA[ a.setl_time <= STR_TO_DATE(#{queryParam.js_end_date},"%Y-%m-%d %H:%i:%s") ]]>
                AND PAY_TYPE='1' AND a.psn_insu_type='1'
                THEN 1 ELSE NULL END) AS totalProjectPayNum1,
            COUNT(CASE WHEN
                <![CDATA[ a.setl_time >= STR_TO_DATE(#{queryParam.js_start_date},"%Y-%m-%d %H:%i:%s") ]]>
                AND <![CDATA[ a.setl_time <= STR_TO_DATE(#{queryParam.js_end_date},"%Y-%m-%d %H:%i:%s") ]]>
                AND PAY_TYPE='1' AND a.psn_insu_type='2'
                THEN 1 ELSE NULL END) AS totalProjectPayNum2,
            COUNT(CASE WHEN
                <![CDATA[ a.setl_time >= STR_TO_DATE(#{queryParam.lastMonth_js_start_date},"%Y-%m-%d %H:%i:%s") ]]>
                AND <![CDATA[ a.setl_time <= STR_TO_DATE(#{queryParam.lastMonth_js_end_date},"%Y-%m-%d %H:%i:%s") ]]>
                AND PAY_TYPE='1'
                THEN 1 ELSE NULL END) AS lastMonthTotalProjectPayNum,
            COUNT(CASE WHEN
                <![CDATA[ a.setl_time >= STR_TO_DATE(#{queryParam.lastYear_js_start_date},"%Y-%m-%d %H:%i:%s") ]]>
                AND <![CDATA[ a.setl_time <= STR_TO_DATE(#{queryParam.lastYear_js_end_date},"%Y-%m-%d %H:%i:%s") ]]>
                AND PAY_TYPE='1'
                THEN 1 ELSE NULL END) AS lastYearTotalProjectPayNum,
            COUNT(CASE WHEN
                <![CDATA[ a.setl_time >= STR_TO_DATE(#{queryParam.js_start_date},"%Y-%m-%d %H:%i:%s") ]]>
                AND <![CDATA[ a.setl_time <= STR_TO_DATE(#{queryParam.js_end_date},"%Y-%m-%d %H:%i:%s") ]]>
                AND a.PAY_TYPE='0'
                THEN 1 ELSE NULL END) AS notPayNum,
            COUNT(CASE WHEN
                <![CDATA[ a.setl_time >= STR_TO_DATE(#{queryParam.lastMonth_js_start_date},"%Y-%m-%d %H:%i:%s") ]]>
                AND <![CDATA[ a.setl_time <= STR_TO_DATE(#{queryParam.lastMonth_js_end_date},"%Y-%m-%d %H:%i:%s") ]]>
                AND a.PAY_TYPE='0'
                THEN 1 ELSE NULL END) AS lastMonthNotPayNum,
            COUNT(CASE WHEN
                <![CDATA[ a.setl_time >= STR_TO_DATE(#{queryParam.lastYear_js_start_date},"%Y-%m-%d %H:%i:%s") ]]>
                AND <![CDATA[ a.setl_time <= STR_TO_DATE(#{queryParam.lastYear_js_end_date},"%Y-%m-%d %H:%i:%s") ]]>
                AND a.PAY_TYPE='0'
                THEN 1 ELSE NULL END) AS lastYearNotPayNum
        from som_drg_pay_detl a
        where 1 = 1
        <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
            AND  a.HOSPITAL_ID = #{queryParam.hospitalId}
        </if>
        <if test="queryParam.b16c!=null and queryParam.b16c!=''">
            AND  a.dscg_caty_codg_inhosp = #{queryParam.b16c}
        </if>
        <if test="queryParam.insuranceTypeList!=null">
            AND  a.psn_insu_type in
            <foreach collection="queryParam.insuranceTypeList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="queryParam.js_start_date!=null and queryParam.js_start_date!='' and
        queryParam.js_end_date!=null and queryParam.js_end_date!=''">
            <![CDATA[
            AND (
                (a.setl_time >= STR_TO_DATE(#{queryParam.js_start_date},"%Y-%m-%d %H:%i:%s")
                AND a.setl_time <= STR_TO_DATE(#{queryParam.js_end_date},"%Y-%m-%d %H:%i:%s"))
                OR
                (a.setl_time >= STR_TO_DATE(#{queryParam.lastMonth_js_start_date},"%Y-%m-%d %H:%i:%s")
                AND a.setl_time <= STR_TO_DATE(#{queryParam.lastMonth_js_end_date},"%Y-%m-%d %H:%i:%s"))
                OR
                (a.setl_time >= STR_TO_DATE(#{queryParam.lastYear_js_start_date},"%Y-%m-%d %H:%i:%s")
                AND a.setl_time <= STR_TO_DATE(#{queryParam.lastYear_js_end_date},"%Y-%m-%d %H:%i:%s"))
            )]]>
        </if>
    </select>

    <select id="getIssueInfo" resultType="com.my.som.vo.drgPayment.PaymentOverviewIssueCountInfo">
        select
            a.ym as ym,
            IFNULL(b.drgPayAmt,0) as drgPayAmt,
            IFNULL(b.initItemPayAmt,0) as initItemPayAmt,
            IFNULL(b.allOwnpayAmt,0) as allOwnpayAmt,
            IFNULL(b.drgPayNum,0) as drgPayNum,
            IFNULL(b.projectPayNum,0) as projectPayNum,
            IFNULL(b.fullSelfPayNum,0) as fullSelfPayNum
        from (
            select CONCAT(#{queryParam.year},'01') as ym from dual
            union all
            select CONCAT(#{queryParam.year},'02') as ym from dual
            union all
            select CONCAT(#{queryParam.year},'03') as ym from dual
            union all
            select CONCAT(#{queryParam.year},'04') as ym from dual
            union all
            select CONCAT(#{queryParam.year},'05') as ym from dual
            union all
            select CONCAT(#{queryParam.year},'06') as ym from dual
            union all
            select CONCAT(#{queryParam.year},'07') as ym from dual
            union all
            select CONCAT(#{queryParam.year},'08') as ym from dual
            union all
            select CONCAT(#{queryParam.year},'09') as ym from dual
            union all
            select CONCAT(#{queryParam.year},'10') as ym from dual
            union all
            select CONCAT(#{queryParam.year},'11') as ym from dual
            union all
            select CONCAT(#{queryParam.year},'12') as ym from dual
        ) a
        left join(
            SELECT
                DATE_FORMAT(a.setl_time,"%Y%m") as ym,
                SUM(a.drg_pay_amt) AS drgPayAmt,
                SUM(a.init_item_pay_amt) AS initItemPayAmt,
                SUM(case when PAY_TYPE ='0' then a.ipt_sumfee else 0 end) AS allOwnpayAmt,
                COUNT(case when PAY_TYPE ='2' then 1 else null end) drgPayNum,
                COUNT(case when PAY_TYPE ='1' then 1 else null end) projectPayNum,
                COUNT(case when PAY_TYPE ='0' then 1 else null end) fullSelfPayNum
            from som_drg_pay_detl a
            where 1=1
            <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
                AND  a.HOSPITAL_ID = #{queryParam.hospitalId}
            </if>
            <if test="queryParam.b16c!=null and queryParam.b16c!=''">
                AND  a.dscg_caty_codg_inhosp = #{queryParam.b16c}
            </if>
            <if test="queryParam.insuranceTypeList!=null">
                AND  a.psn_insu_type in
                <foreach collection="queryParam.insuranceTypeList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="queryParam.year!=null and queryParam.year!='' ">
                <![CDATA[
                AND DATE_FORMAT(a.setl_time,"%Y") = #{queryParam.year}
                ]]>
            </if>
            group by DATE_FORMAT(a.setl_time,"%Y%m")
        ) b on a.ym=b.ym
        order by a.ym asc
    </select>

</mapper>