<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.som.dao.common.CommonDao">
    <select id="getAutoIncrement" resultType="java.lang.Long">
        select AUTO_INCREMENT from INFORMATION_SCHEMA.TABLES
        where tab_name=#{0}
    </select>

    <select id="getStsDDataHandleLogNextId" resultType="java.lang.Long">
        select case when id is null then 1 else max(id)+1 end from som_datapros_log
    </select>

    <resultMap id="BaseResultMap" type="com.my.som.vo.common.DrgsSuggestInfo">
        <result column="drg_codg" jdbcType="VARCHAR" property="drgsCode" />
        <result column="DRG_NAME" jdbcType="VARCHAR" property="drgsName" />
    </resultMap>

    <sql id="Base_Column_List">
          drg_codg, DRG_NAME
      </sql>
    <select id="queryLikeDrgsByPram" resultMap="BaseResultMap">
        select
            distinct
            drg_codg as drg_codg,
            DRG_NAME as DRG_NAME
        from som_drg_standard
        where 1 = 1
        <if test="drgsQueryParam.active_flag!=null and drgsQueryParam.active_flag!=''">
            AND active_flag = #{drgsQueryParam.active_flag}
        </if>
        <if test="drgsQueryParam.drg_codg!=null and drgsQueryParam.drg_codg!=''">
            AND drg_codg = #{drgsQueryParam.drg_codg}
        </if>
        <if test="drgsQueryParam.drg_name!=null and drgsQueryParam.drg_name!=''">
            AND drg_name = #{drgsQueryParam.drg_name}
        </if>
        <if test="drgsQueryParam.hospital_id!=null and drgsQueryParam.hospital_id!=''">
            AND hospital_id = #{drgsQueryParam.hospital_id}
        </if>
        <if test="drgsQueryParam.likeQueryString!=null and drgsQueryParam.likeQueryString!=''">
            AND (
            drg_codg LIKE concat("%",#{drgsQueryParam.likeQueryString},"%")
            OR drg_name LIKE concat("%",#{drgsQueryParam.likeQueryString},"%")
            )
        </if>
        ORDER BY drg_codg,DRG_NAME
        limit 0,100
    </select>

    <select id="queryLikePpsGroupByPram" resultType="com.my.som.vo.common.PpsGroupSuggestInfo">
        select
            cd_codg as cdCodg,
            CD_NAME as cdName
        from som_grp_rcd
        where 1 = 1
        <if test="drgsQueryParam.active_flag!=null and drgsQueryParam.active_flag!=''">
            AND active_flag = #{drgsQueryParam.active_flag}
        </if>
        <if test="drgsQueryParam.hospital_id!=null and drgsQueryParam.hospital_id!=''">
            AND hospital_id = #{drgsQueryParam.hospital_id}
        </if>
        <if test="drgsQueryParam.likeQueryString!=null and drgsQueryParam.likeQueryString!=''">
            AND (
            cd_codg LIKE concat("%",#{drgsQueryParam.likeQueryString},"%")
            OR CD_NAME LIKE concat("%",#{drgsQueryParam.likeQueryString},"%")
            )
        </if>
        GROUP BY cd_codg,CD_NAME
        ORDER BY cd_codg
        limit 0,100
    </select>

    <select id="queryLikeDipGroupByPram" resultType="com.my.som.vo.common.DipGroupSuggestInfo">
        select DISTINCT A.* FROM(
            select
                distinct
                a.dip_codg as dipCodg,
                a.DIP_NAME as dipName
            from som_dip_grp_rcd a
        WHERE 1=1
        <if test="drgsQueryParam.active_flag!=null and drgsQueryParam.active_flag!=''">
            AND a.ACTIVE_FLAG = #{drgsQueryParam.active_flag}
        </if>
        <if test="drgsQueryParam.hospital_id!=null and drgsQueryParam.hospital_id!=''">
            AND a.HOSPITAL_ID = #{drgsQueryParam.hospital_id}
        </if>
        )A
        where
          A.dipCodg !=''
        AND (
         <if test="drgsQueryParam.likeQueryString!=null and drgsQueryParam.likeQueryString!=''">
             A.dipCodg LIKE concat("%",#{drgsQueryParam.likeQueryString},"%")
             OR A.dipName LIKE concat("%",#{drgsQueryParam.likeQueryString},"%")
         </if>
        )
        ORDER BY A.dipCodg,A.dipName
        limit 0,100
    </select>

<!--    <select id="queryMedicalDoctorSelectInput" resultType="com.my.som.vo.common.SeletInputInfo">-->
<!--        select-->
<!--            a.value as value,-->
<!--            a.label as label-->
<!--            from(-->
<!--                select-->
<!--                CODE as value,-->
<!--                NAME as label,-->
<!--                DOCTOR_TYPE-->
<!--                from-->
<!--                som_medstff_info-->
<!--                where 1=1-->
<!--                <if test="doctorQueryParam.doctorType != null and doctorQueryParam.doctorType != ''">-->
<!--                    AND  DOCTOR_TYPE = #{doctorQueryParam.doctorType}-->
<!--                </if>-->
<!--            ) a-->

<!--    </select>-->

    <select id="queryMedicalDoctorSelectInput" resultType="com.my.som.vo.common.SeletInputInfo">
        select
            a.value as value,
            case when a.label=a.value then CONCAT(a.label,'-(历史病案)')
              else a.label end as label
            from(
                  select
                    ipdr_code as value,
                    ipdr_name as label
                from som_drg_grp_info
                where 1=1
                <if test="doctorQueryParam.hospitalId!=null and doctorQueryParam.hospitalId!=''">
                    AND  HOSPITAL_ID = #{doctorQueryParam.hospitalId}
                </if>
                <if test="doctorQueryParam.b16c!=null and doctorQueryParam.b16c!=''">
                    AND  dscg_caty_codg_inhosp  =#{doctorQueryParam.b16c}
                </if>
                <if test="doctorQueryParam.begnDate!=null and doctorQueryParam.begnDate!=''">
                    AND dscg_time BETWEEN #{doctorQueryParam.begnDate,jdbcType=VARCHAR}
                    AND CONCAT(#{doctorQueryParam.expiDate,jdbcType=VARCHAR},' 23:59:59')
                </if>
            ) a
            where a.label not in ('-','--','未填写') and a.value not in ('-','--','未填写')
            and a.label is not null and a.label!='' and a.label!='----' and a.label!='-='
            and a.label!='?' and a.label!='*' and a.label!='=' and a.label!='=-' and a.label!='0'
            GROUP BY a.value, a.label
            ORDER BY a.label

<!--        <if test="doctorQueryParam.doctorType==null or doctorQueryParam.doctorType==''">-->
<!--            a.value as value,-->
<!--            case when a.label=a.value then CONCAT(a.label,'-(历史病案)')-->
<!--              else a.label end as label-->
<!--            from(-->
<!--                select-->
<!--                    ipdr_code as value,-->
<!--                    ipdr_name as label-->
<!--                FROM som_drg_grp_info-->
<!--                where 1=1-->
<!--                <if test="doctorQueryParam.hospitalId!=null and doctorQueryParam.hospitalId!=''">-->
<!--                    AND  HOSPITAL_ID = #{doctorQueryParam.hospitalId}-->
<!--                </if>-->
<!--                <if test="doctorQueryParam.b16c!=null and doctorQueryParam.b16c!=''">-->
<!--                    AND dscg_caty_codg_inhosp  =#{doctorQueryParam.b16c}-->
<!--                </if>-->
<!--                union all-->
<!--                select-->
<!--                    atddr_code as value,-->
<!--                    atddr_name as label-->
<!--                FROM som_drg_grp_info-->
<!--                where 1=1-->
<!--                <if test="doctorQueryParam.hospitalId!=null and doctorQueryParam.hospitalId!=''">-->
<!--                    AND  HOSPITAL_ID = #{doctorQueryParam.hospitalId}-->
<!--                </if>-->
<!--                <if test="doctorQueryParam.b16c!=null and doctorQueryParam.b16c!=''">-->
<!--                    AND dscg_caty_codg_inhosp  =#{doctorQueryParam.b16c}-->
<!--                </if>-->
<!--                union all-->
<!--                select-->
<!--                    deptdrt_code as value,-->
<!--                    deptdrt_name as label-->
<!--                FROM som_drg_grp_info-->
<!--                where 1=1-->
<!--                <if test="doctorQueryParam.hospitalId!=null and doctorQueryParam.hospitalId!=''">-->
<!--                    AND  HOSPITAL_ID = #{doctorQueryParam.hospitalId}-->
<!--                </if>-->
<!--                <if test="doctorQueryParam.b16c!=null and doctorQueryParam.b16c!=''">-->
<!--                    AND dscg_caty_codg_inhosp  =#{doctorQueryParam.b16c}-->
<!--                </if>-->
<!--                union all-->
<!--                select-->
<!--                    chfdr_code as value,-->
<!--                    chfdr_name as label-->
<!--                FROM som_drg_grp_info-->
<!--                where 1=1-->
<!--                <if test="doctorQueryParam.hospitalId!=null and doctorQueryParam.hospitalId!=''">-->
<!--                    AND  HOSPITAL_ID = #{doctorQueryParam.hospitalId}-->
<!--                </if>-->
<!--                <if test="doctorQueryParam.b16c!=null and doctorQueryParam.b16c!=''">-->
<!--                    AND dscg_caty_codg_inhosp  =#{doctorQueryParam.b16c}-->
<!--                </if>-->
<!--                union all-->
<!--                select-->
<!--                    train_dr_code as value,-->
<!--                    train_dr_name as label-->
<!--                FROM som_drg_grp_info-->
<!--                where 1=1-->
<!--                <if test="doctorQueryParam.hospitalId!=null and doctorQueryParam.hospitalId!=''">-->
<!--                    AND  HOSPITAL_ID = #{doctorQueryParam.hospitalId}-->
<!--                </if>-->
<!--                <if test="doctorQueryParam.b16c!=null and doctorQueryParam.b16c!=''">-->
<!--                    AND dscg_caty_codg_inhosp  =#{doctorQueryParam.b16c}-->
<!--                </if>-->
<!--                union all-->
<!--                select-->
<!--                    qltctrl_dr_code as value,-->
<!--                    qltctrl_dr_name as label-->
<!--                FROM som_drg_grp_info-->
<!--                where 1=1-->
<!--                <if test="doctorQueryParam.hospitalId!=null and doctorQueryParam.hospitalId!=''">-->
<!--                    AND  HOSPITAL_ID = #{doctorQueryParam.hospitalId}-->
<!--                </if>-->
<!--                <if test="doctorQueryParam.b16c!=null and doctorQueryParam.b16c!=''">-->
<!--                    AND dscg_caty_codg_inhosp  =#{doctorQueryParam.b16c}-->
<!--                </if>-->
<!--                union all-->
<!--                select-->
<!--                    intn_dr as value,-->
<!--                    intn_dr as label-->
<!--                FROM som_drg_grp_info-->
<!--                where 1=1-->
<!--                <if test="doctorQueryParam.hospitalId!=null and doctorQueryParam.hospitalId!=''">-->
<!--                    AND  HOSPITAL_ID = #{doctorQueryParam.hospitalId}-->
<!--                </if>-->
<!--                <if test="doctorQueryParam.b16c!=null and doctorQueryParam.b16c!=''">-->
<!--                    AND dscg_caty_codg_inhosp  =#{doctorQueryParam.b16c}-->
<!--                </if>-->
<!--            ) a-->
<!--            where a.label not in ('-','&#45;&#45;','未填写') and a.value not in ('-','&#45;&#45;','未填写')-->
<!--            and a.label is not null and a.label!='' and a.label!='&#45;&#45;&#45;&#45;' and a.label!='-='-->
<!--            and a.label!='?' and a.label!='*' and a.label!='=' and a.label!='=-' and a.label!='0'-->
<!--            GROUP BY a.value, a.label-->
<!--            ORDER BY a.label-->
<!--        </if>-->
    </select>

    <select id="queryDrgDetailList" resultType="com.my.som.vo.hospitalAnalysis.DrgsAnalysisInfo">
        SELECT x.*,
        IFNULL(y.deptNum,0) AS deptNum  <!--科室数-->
        FROM
        (
        SELECT
        <choose>
            <when test="queryParam.type == 1">
                a.dip_codg AS drgsCode,
                a.DIP_NAME AS drgsName,
                ROUND(IFNULL(a.dip_wt,0), 2) AS drgWt,  <!--权重-->
                ROUND(IFNULL(SUM(a.dip_wt),0),2) AS totalDrgWeight, <!--总权重-->
            </when>
            <when test="queryParam.type == 2">
                a.drg_codg AS drgsCode,
                a.DRG_NAME AS drgsName,
                ROUND(IFNULL(a.drg_wt,0), 2) AS drgWt,  <!--权重-->
                ROUND(IFNULL(SUM(a.drg_wt),0),2) AS totalDrgWeight, <!--总权重-->
            </when>
            <when test="queryParam.type == 3">
                a.cd_codg AS drgsCode,
                a.CD_NAME AS drgsName,
                ROUND(IFNULL(a.cd_wt,0), 2) AS drgWt,  <!--权重-->
                ROUND(IFNULL(SUM(a.cd_wt),0),2) AS totalDrgWeight, <!--总权重-->
            </when>
        </choose>
        COUNT(1) AS medcasVal, <!--病案数-->
        ROUND(IFNULL(avg(a.ipt_sumfee),0), 2) AS avgCost, <!--平均住院费用-->
        ROUND(IFNULL(avg(a.act_ipt),0), 2) AS avgDays, <!--平均住院日-->
        ROUND(IFNULL(sum(IFNULL(a.act_ipt,0)/NULLIF(a.standard_ave_hosp_day, 0))/COUNT(1),0),2) as timeIndex, <!--  时间消耗指数   -->
        ROUND(IFNULL(sum(IFNULL(a.ipt_sumfee,0)/NULLIF(a.standard_avg_fee, 0))/COUNT(1),0),2) as costIndex  <!--  费用消耗指数   -->
        FROM
        <choose>
            <when test="queryParam.type == 1">
                som_dip_grp_info a
                <if test='queryParam.enableSeAns != null and queryParam.enableSeAns != "" and queryParam.enableSeAns == "1"'>
                    INNER JOIN som_hi_invy_bas_info b ON a.SETTLE_LIST_ID=b.id
                    INNER JOIN som_setl_cas_crsp p on b.k00=p.k00
                </if>
                <if test="queryParam.queryType!=null and queryParam.queryType!=''">
                    LEFT JOIN som_dip_grp_rcd b
                    ON a.SETTLE_LIST_ID = b.SETTLE_LIST_ID
                    LEFT JOIN som_drg_grp_rcd m
                    ON a.SETTLE_LIST_ID = m.SETTLE_LIST_ID
                </if>
            </when>
            <when test="queryParam.type == 2">
                som_drg_grp_info a
                <if test='queryParam.enableSeAns != null and queryParam.enableSeAns != "" and queryParam.enableSeAns == "1"'>
                    INNER JOIN som_hi_invy_bas_info b ON a.SETTLE_LIST_ID=b.id
                    INNER JOIN som_setl_cas_crsp p on b.k00=p.k00
                </if>

            </when>
            <when test="queryParam.type == 3">
                som_cd_grp_info a
                <if test='queryParam.enableSeAns != null and queryParam.enableSeAns != "" and queryParam.enableSeAns == "1"'>
                    INNER JOIN som_hi_invy_bas_info b ON a.SETTLE_LIST_ID=b.id
                    INNER JOIN som_setl_cas_crsp p on b.k00=p.k00
                </if>
            </when>
        </choose>
        WHERE a.grp_stas = '1'
        <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
            AND  a.HOSPITAL_ID = #{queryParam.hospitalId}
        </if>
        <if test="queryParam.priOutHosDeptCode!=null and queryParam.priOutHosDeptCode!=''">
            AND a.dscg_caty_codg_inhosp  =#{queryParam.priOutHosDeptCode}
        </if>
        <if test="queryParam.inStartTime != null and queryParam.inStartTime != '' and
                            queryParam.inEndTime != null and queryParam.inEndTime != ''">
            AND a.adm_time BETWEEN #{queryParam.inStartTime} AND CONCAT(#{queryParam.inEndTime},' 23:59:59')
        </if>
        <if test="(queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and
                            queryParam.cy_end_date!=null and queryParam.cy_end_date!='') or
                           queryParam.inHosFlag == 1">
            AND a.dscg_time BETWEEN #{queryParam.cy_start_date} and CONCAT(#{queryParam.cy_end_date},' 23:59:59')
        </if>
        <if test="queryParam.queryDrgsCode != null and queryParam.queryDrgsCode != ''">
            AND
            <choose>
                <when test="queryParam.type == 1">
                    a.dip_codg
                </when>
                <when test="queryParam.type == 2">
                    a.drg_codg
                </when>
                <when test="queryParam.type == 3">
                    a.cd_codg
                </when>
            </choose>
            = #{queryParam.queryDrgsCode}
        </if>
        <if test="queryParam.seStartTime != null and queryParam.seStartTime != '' and
                           queryParam.seEndTime != null and queryParam.seEndTime != ''">
            AND a.setl_end_time BETWEEN #{queryParam.seStartTime,jdbcType=VARCHAR} AND CONCAT(#{queryParam.seEndTime,jdbcType=VARCHAR},' 23:59:59')
        </if>
        <if test="queryParam.queryType!=null and queryParam.queryType!='' and  queryParam.queryType=='hxbz'">   <!--hxbz-->
            AND (not regexp_like(b.DIP_NAME, '(治疗性操作组|诊断性操作组|相关手术组|保守治疗组|治疗性操作|诊断性操作|相关手术|保守治疗)') or b.DIP_NAME IS NULL)
        </if>
        <if test="queryParam.queryType!=null and queryParam.queryType!='' and  queryParam.queryType=='zlxcz'">   <!--zlxcz-->
            AND (regexp_like(b.DIP_NAME, '治疗性操作组') or regexp_like(b.DIP_NAME, '治疗性操作'))
        </if>
        <if test="queryParam.queryType!=null and queryParam.queryType!='' and  queryParam.queryType=='zdxcz'">   <!--zdxcz-->
            AND (regexp_like(b.DIP_NAME, '诊断性操作组') or regexp_like(b.DIP_NAME, '诊断性操作'))
        </if>
        <if test="queryParam.queryType!=null and queryParam.queryType!='' and  queryParam.queryType=='xgss'">   <!--xgss-->
            AND (regexp_like(b.DIP_NAME, '相关手术组') or regexp_like(b.DIP_NAME, '相关手术'))
        </if>
        <if test="queryParam.queryType!=null and queryParam.queryType!='' and  queryParam.queryType=='bszl'">   <!--bszl-->
            AND (regexp_like(b.DIP_NAME, '保守治疗组') or regexp_like(b.DIP_NAME, '保守治疗'))
        </if>
        <if test="queryParam.queryType!=null and queryParam.queryType!='' and  queryParam.queryType=='hj'">   <!--hj-->
            AND regexp_like(b.DIP_NAME, '(治疗性操作组|诊断性操作组|相关手术组|保守治疗组|治疗性操作|诊断性操作|相关手术|保守治疗)')
        </if>

        <if test="queryParam.type == 2 and queryParam.queryType!=null and queryParam.queryType!='' and  queryParam.queryType=='seriousComplicationDrgsDrgNum'">   <!--查询伴严重合并症及伴随病的DRGs组DRG详情-->
            AND right(drg_codg,1) = '1'
        </if>
        <if test="queryParam.type == 2 and queryParam.queryType!=null and queryParam.queryType!='' and  queryParam.queryType=='comorbiditiesComplicationDrgsMedicalNum'">   <!--查询伴合并症或并发症的DRGs组DRG详情-->
            AND right(drg_codg,1) = '2'
        </if>
        <if test="queryParam.type == 2 and queryParam.queryType!=null and queryParam.queryType!='' and  queryParam.queryType=='normalComplicationDrgsDrgNum'">   <!--查询伴一般合并症及伴随病的DRGs组DRG详情-->
            AND right(drg_codg,1) = '3'
        </if>
        <if test="queryParam.type == 2 and queryParam.queryType!=null and queryParam.queryType!='' and  queryParam.queryType=='noSeriousComorbiditiesComplicationDrgsMedicalNum'">   <!--查询不伴严重合并症或并发症的DRGs组DRG详情-->
            AND right(drg_codg,1) = '4'
        </if>
        <if test="queryParam.type == 2 and queryParam.queryType!=null and queryParam.queryType!='' and  queryParam.queryType=='noComplicationDrgsDrgNum'">   <!--查询伴不伴合并症及伴随病的DRGs组DRG详情-->
            AND right(drg_codg,1) = '5'
        </if>
        <if test="queryParam.type == 2 and queryParam.queryType!=null and queryParam.queryType!='' and  queryParam.queryType=='notDifferentiatedDrgsMedicalNum'">   <!--查询未作区分的DRGs组DRG详情-->
            AND right(drg_codg,1) = '9'
        </if>

        <if test="queryParam.type == 3 and queryParam.queryType!=null and queryParam.queryType!='' and  queryParam.queryType=='seriousComplicationDrgsDrgNum'">   <!--查询伴严重合并症及伴随病的DRGs组DRG详情-->
            AND right(cd_codg,1) = '1'
        </if>
        <if test="queryParam.type == 3 and queryParam.queryType!=null and queryParam.queryType!='' and  queryParam.queryType=='comorbiditiesComplicationDrgsMedicalNum'">   <!--查询伴合并症或并发症的DRGs组DRG详情-->
            AND right(cd_codg,1) = '2'
        </if>

        <if test="queryParam.type == 3 and queryParam.queryType!=null and queryParam.queryType!='' and  queryParam.queryType=='normalComplicationDrgsDrgNum'">   <!--查询伴一般合并症及伴随病的DRGs组DRG详情-->
            AND right(cd_codg,1) = '3'
        </if>
        <if test="queryParam.type == 3 and queryParam.queryType!=null and queryParam.queryType!='' and  queryParam.queryType=='noSeriousComorbiditiesComplicationDrgsMedicalNum'">   <!--查询不伴严重合并症或并发症的DRGs组DRG详情-->
            AND right(cd_codg,1) = '4'
        </if>
        <if test="queryParam.type == 3 and queryParam.queryType!=null and queryParam.queryType!='' and  queryParam.queryType=='noComplicationDrgsDrgNum'">   <!--查询伴不伴合并症及伴随病的DRGs组DRG详情-->
            AND right(cd_codg,1) = '5'
        </if>
        <if test="queryParam.type == 3 and queryParam.queryType!=null and queryParam.queryType!='' and  queryParam.queryType=='notDifferentiatedDrgsMedicalNum'">   <!--查询未作区分的DRGs组DRG详情-->
            AND right(cd_codg,1) = '9'
        </if>

        <if test="queryParam.type == 2 and queryParam.queryType!=null and queryParam.queryType!='' and  queryParam.queryType=='medicalDeptDrgsDrgNum'">   <!--查询内科组DRG详情-->
            AND SUBSTR(drg_codg,2,1) in ('R','S','T','U','V','W','X','Y','Z')
        </if>
        <if test="queryParam.type == 2 and queryParam.queryType!=null and queryParam.queryType!='' and  queryParam.queryType=='notOperationDrgsDrgNum'">   <!--查询非手术室操作组DRG详情-->
            AND SUBSTR(drg_codg,2,1) in ('K','L','M','N','P','Q')
        </if>
        <if test="queryParam.type == 2 and queryParam.queryType!=null and queryParam.queryType!='' and  queryParam.queryType=='surgeryDeptDrgsDrgsDrgNum'">   <!--查询外科组DRG详情-->
            AND SUBSTR(drg_codg,2,1) in ('A','B','C','D','E','F','G','H','J')
        </if>

        <if test="queryParam.type == 3 and queryParam.queryType!=null and queryParam.queryType!='' and  queryParam.queryType=='medicalDeptDrgsDrgNum'">   <!--查询内科组DRG详情-->
            AND SUBSTR(cd_codg,2,1) in ('R','S','T','U','V','W','X','Y','Z')
        </if>
        <if test="queryParam.type == 3 and queryParam.queryType!=null and queryParam.queryType!='' and  queryParam.queryType=='notOperationDrgsDrgNum'">   <!--查询非手术室操作组DRG详情-->
            AND SUBSTR(cd_codg,2,1) in ('K','L','M','N','P','Q')
        </if>
        <if test="queryParam.type == 3 and queryParam.queryType!=null and queryParam.queryType!='' and  queryParam.queryType=='surgeryDeptDrgsDrgsDrgNum'">   <!--查询外科组DRG详情-->
            AND SUBSTR(cd_codg,2,1) in ('A','B','C','D','E','F','G','H','J')
        </if>
        <if test=" queryParam.drCodg!=null and queryParam.drCodg!=''">
            AND ipdr_code = #{queryParam.drCodg}
        </if>
        <if test=" queryParam.drName!=null and queryParam.drName!=''">
            AND (
            ipdr_name = #{queryParam.drName} OR
            atddr_name = #{queryParam.drName} OR
            deptdrt_name = #{queryParam.drName} OR
            chfdr_name = #{queryParam.drName} OR
            train_dr_name = #{queryParam.drName} OR
            qltctrl_dr_name = #{queryParam.drName} OR
            intn_dr = #{queryParam.drName}
            )
        </if>
        <if test="queryParam.doctorIdList!=null">
            AND (
            deptdrt_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            chfdr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            atddr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            ipdr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            resp_nurs_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            train_dr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            intn_dr in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            codr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            qltctrl_dr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            qltctrl_nurs_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            )
        </if>
        <choose>
            <when test="queryParam.type == 1">
                GROUP BY a.dip_codg,a.DIP_NAME,a.dip_wt
            </when>
            <when test="queryParam.type == 2">
                GROUP BY a.drg_codg,a.DRG_NAME,a.drg_wt
            </when>
            <when test="queryParam.type == 3">
                GROUP BY a.cd_codg,a.CD_NAME,a.cd_wt
            </when>
        </choose>
        )x
        inner join
        (
        select
        <choose>
            <when test="queryParam.type == 1">
                dip_codg AS drgsCode,
            </when>
            <when test="queryParam.type == 2">
                drg_codg AS drgsCode,
            </when>
            <when test="queryParam.type == 3">
                cd_codg AS drgsCode,
            </when>
        </choose>
        COUNT(DISTINCT(dscg_caty_codg_inhosp)) AS deptNum
        from
        <choose>
            <when test="queryParam.type == 1">
                som_dip_grp_info a
                <if test="queryParam.queryType!=null and queryParam.queryType!=''">
                    left join som_drg_grp_rcd m
                    on a.SETTLE_LIST_ID = m.SETTLE_LIST_ID
                </if>
            </when>
            <when test="queryParam.type == 2">
                som_drg_grp_info a
            </when>
            <when test="queryParam.type == 3">
                som_cd_grp_info a
            </when>
        </choose>
        where a.grp_stas = '1'
        <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
            AND a.HOSPITAL_ID = #{queryParam.hospitalId}
        </if>
        <if test="queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and
            queryParam.cy_end_date!=null and queryParam.cy_end_date!=''">
            AND a.dscg_time BETWEEN #{queryParam.cy_start_date} and CONCAT(CONCAT(#{queryParam.cy_end_date},' 23:59:59'),' 23:59:59')
        </if>
        <if test="queryParam.seStartTime != null and queryParam.seStartTime != '' and
                           queryParam.seEndTime != null and queryParam.seEndTime != ''">
            AND a.setl_end_time BETWEEN #{queryParam.seStartTime,jdbcType=VARCHAR} AND CONCAT(#{queryParam.seEndTime,jdbcType=VARCHAR},' 23:59:59')
        </if>
        <if test="queryParam.type == 2 and queryParam.queryType!=null and queryParam.queryType!='' and  queryParam.queryType=='seriousComplicationDrgsDrgNum'">   <!--查询伴严重合并症及伴随病的DRGs组DRG详情-->
            AND right(drg_codg,1) = '1'
        </if>
        <if test="queryParam.type == 2 and queryParam.queryType!=null and queryParam.queryType!='' and  queryParam.queryType=='comorbiditiesComplicationDrgsMedicalNum'">   <!--查询伴合并症或并发症的DRGs组DRG详情-->
            AND right(drg_codg,1) = '2'
        </if>
        <if test="queryParam.type == 2 and queryParam.queryType!=null and queryParam.queryType!='' and  queryParam.queryType=='normalComplicationDrgsDrgNum'">   <!--查询伴一般合并症及伴随病的DRGs组DRG详情-->
            AND right(drg_codg,1) = '3'
        </if>
        <if test="queryParam.type == 2 and queryParam.queryType!=null and queryParam.queryType!='' and  queryParam.queryType=='noSeriousComorbiditiesComplicationDrgsMedicalNum'">   <!--查询不伴严重合并症或并发症的DRGs组DRG详情-->
            AND right(drg_codg,1) = '4'
        </if>
        <if test="queryParam.type == 2 and queryParam.queryType!=null and queryParam.queryType!='' and  queryParam.queryType=='noComplicationDrgsDrgNum'">   <!--查询伴不伴合并症及伴随病的DRGs组DRG详情-->
            AND right(drg_codg,1) = '5'
        </if>
        <if test="queryParam.type == 2 and queryParam.queryType!=null and queryParam.queryType!='' and  queryParam.queryType=='notDifferentiatedDrgsMedicalNum'">   <!--查询未作区分的DRGs组DRG详情-->
            AND right(drg_codg,1) = '9'
        </if>

        <if test="queryParam.type == 3 and queryParam.queryType!=null and queryParam.queryType!='' and  queryParam.queryType=='seriousComplicationDrgsDrgNum'">   <!--查询伴严重合并症及伴随病的DRGs组DRG详情-->
            AND right(cd_codg,1) = '1'
        </if>
        <if test="queryParam.type == 3 and queryParam.queryType!=null and queryParam.queryType!='' and  queryParam.queryType=='comorbiditiesComplicationDrgsMedicalNum'">   <!--查询伴严重合并症及伴随病的DRGs组DRG详情-->
            AND right(cd_codg,1) = '2'
        </if>
        <if test="queryParam.type == 3 and queryParam.queryType!=null and queryParam.queryType!='' and  queryParam.queryType=='normalComplicationDrgsDrgNum'">   <!--查询伴一般合并症及伴随病的DRGs组DRG详情-->
            AND right(cd_codg,1) = '3'
        </if>
        <if test="queryParam.type == 3 and queryParam.queryType!=null and queryParam.queryType!='' and  queryParam.queryType=='noSeriousComorbiditiesComplicationDrgsMedicalNum'">   <!--查询伴严重合并症及伴随病的DRGs组DRG详情-->
            AND right(cd_codg,1) = '4'
        </if>
        <if test="queryParam.type == 3 and queryParam.queryType!=null and queryParam.queryType!='' and  queryParam.queryType=='noComplicationDrgsDrgNum'">   <!--查询伴不伴合并症及伴随病的DRGs组DRG详情-->
            AND right(cd_codg,1) = '5'
        </if>
        <if test="queryParam.type == 3 and queryParam.queryType!=null and queryParam.queryType!='' and  queryParam.queryType=='notDifferentiatedDrgsMedicalNum'">   <!--查询伴严重合并症及伴随病的DRGs组DRG详情-->
            AND right(cd_codg,1) = '9'
        </if>

        <if test="queryParam.type == 2 and queryParam.queryType!=null and queryParam.queryType!='' and  queryParam.queryType=='medicalDeptDrgsDrgNum'">   <!--查询内科组DRG详情-->
            AND SUBSTR(drg_codg,2,1) in ('R','S','T','U','V','W','X','Y','Z')
        </if>
        <if test="queryParam.type == 2 and queryParam.queryType!=null and queryParam.queryType!='' and  queryParam.queryType=='notOperationDrgsDrgNum'">   <!--查询非手术室操作组DRG详情-->
            AND SUBSTR(drg_codg,2,1) in ('K','L','M','N','P','Q')
        </if>
        <if test="queryParam.type == 2 and queryParam.queryType!=null and queryParam.queryType!='' and  queryParam.queryType=='surgeryDeptDrgsDrgsDrgNum'">   <!--查询外科组DRG详情-->
            AND SUBSTR(drg_codg,2,1) in ('A','B','C','D','E','F','G','H','J')
        </if>
        <if test="queryParam.type == 3 and queryParam.queryType!=null and queryParam.queryType!='' and  queryParam.queryType=='medicalDeptDrgsDrgNum'">   <!--查询内科组DRG详情-->
            AND SUBSTR(cd_codg,2,1) in ('R','S','T','U','V','W','X','Y','Z')
        </if>
        <if test="queryParam.type == 3 and queryParam.queryType!=null and queryParam.queryType!='' and  queryParam.queryType=='notOperationDrgsDrgNum'">   <!--查询非手术室操作组DRG详情-->
            AND SUBSTR(cd_codg,2,1) in ('K','L','M','N','P','Q')
        </if>
        <if test="queryParam.type == 3 and queryParam.queryType!=null and queryParam.queryType!='' and  queryParam.queryType=='surgeryDeptDrgsDrgsDrgNum'">   <!--查询外科组DRG详情-->
            AND SUBSTR(cd_codg,2,1) in ('A','B','C','D','E','F','G','H','J')
        </if>
        <choose>
            <when test="queryParam.type == 1">
                GROUP BY dip_codg
            </when>
            <when test="queryParam.type == 2">
                GROUP BY drg_codg
            </when>
            <when test="queryParam.type == 3">
                GROUP BY cd_codg
            </when>
        </choose>
        ) y on x.drgsCode = y.drgsCode
    </select>

    <select id="queryMedicalDetailList" resultType="com.my.som.vo.common.MedicalMainDetailInfo">
        SELECT x.*,
        CASE
        WHEN (x.drgsCode IS NULL or x.drgsCode = '')
                AND ( x.drgsName IS NULL  or x.drgsName = '')   THEN '主要诊断不在入组方案'
        ELSE x.grp_fale_rea
        END AS grpFaleRea,
               ROUND(IFNULL(x.standardInHosDays,0)-IFNULL(x.inHosDays,0),2) AS dayDifference,
               ROUND(IFNULL(x.standardInHosCost,0)-IFNULL(x.inHosTotalCost,0),2) AS costDifference,
               IFNULL(y.digsNum,0) AS digsNum,
               y.otherDiagnoseCodeAndName AS otherDiagnoseCodeAndName,
               IFNULL(z.oprNum,0) AS oprNum,
               z.oneLevelOprCodeAndName AS oneLevelOprCodeAndName,
               z.oneLevelStanOprCodeAndName AS oneLevelStanOprCodeAndName,
               z.twoLevelOprCodeAndName AS twoLevelOprCodeAndName,
               z.twoLevelStanOprCodeAndName AS twoLevelStanOprCodeAndName,
               z.threeLevelOprCodeAndName AS threeLevelOprCodeAndName,
               z.threeLevelStanOprCodeAndName AS threeLevelStanOprCodeAndName,
               z.fourLevelOprCodeAndName AS fourLevelOprCodeAndName,
               z.fourLevelStanOprCodeAndName AS fourLevelStanOprCodeAndName
        FROM
             (
                 SELECT DISTINCT
                                sco.settle_list_id,
                                sco.dise_type,
                                 a.SETTLE_LIST_ID AS hiSetlInvyId,
                                 a.PATIENT_ID AS patientId,
                                 a.NAME AS name,
                                 a.adm_time AS inHosTime,
                                 a.dscg_time AS outHosTime,
        CASE
        WHEN b.D35 IS NOT NULL and b.D35  != '' THEN '是'
        ELSE '否'
        END AS listSerialNumFlag,
                                 <choose>
                                     <when test="queryParam.type == 1">
                                        a.dip_codg AS drgsCode,
                                        a.DIP_NAME AS drgsName,
                                        <if test="queryParam.queryType!=null and queryParam.queryType!=''">
                                            e.oprn_oprt_code,

                                        </if>
                                     </when>
                                     <when test="queryParam.type == 2">
                                        a.drg_codg AS drgsCode,
                                        a.DRG_NAME AS drgsName,
                                         b.adm_diag as admDiag,
                                        w.conditionDiagnosis as conditionDiagnosis,
                                         IFNULL(ROUND(IFNULL(a.drugfee,0),2),0) AS medicalCost,  /*药物费用*/
                                         IFNULL(ROUND(IFNULL(a.drugfee,0)/NULLIF(a.ipt_sumfee,0)*100,2),0) AS medicalCostRate, /*药物占比*/
                                         IFNULL(ROUND(IFNULL(a.mcs_fee,0),2),0) AS materialCost,  /*耗材费*/
                                         IFNULL(ROUND(IFNULL(a.mcs_fee,0)/NULLIF(a.ipt_sumfee,0)*100,2),0) AS materialCostRate , /*耗材费*/
                                     </when>
                                     <when test="queryParam.type == 3">
                                        a.cd_codg AS drgsCode,
                                        a.CD_NAME AS drgsName,
                                     </when>
                                 </choose>
                                 z.`NAME` AS priOutHosDeptName,
        CASE
        WHEN b.a56  IS NULL or b.a56 =''  THEN '未传值'
        WHEN SUBSTRING(b.a56, 1, 4) = SUBSTRING(#{queryParam.provLevelInsuplcAdmdvs}, 1, 4)THEN '省本级'
        WHEN SUBSTRING(b.a56, 1, 4) = SUBSTRING(#{queryParam.insuplcAdmdvs}, 1, 4) THEN '市医保'
        WHEN SUBSTRING(b.a56, 1, 2) = SUBSTRING(#{queryParam.insuplcAdmdvs}, 1, 2) AND SUBSTRING(b.a56, 1, 4) != SUBSTRING(#{queryParam.insuplcAdmdvs}, 1, 4) THEN CONCAT('省内异地-', t2.cityName)
        ELSE '省外异地'
        END AS isRemote,

        <if test="queryParam.type == 1">
                                     ROUND(IFNULL(AES_DECRYPT(UNHEX(y.dip_standard_ipt_days_same_lv),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}),0), 2) as standardInHosDays ,
                                 </if>
                                 <if test="queryParam.type == 2">
                                     ROUND(IFNULL(AES_DECRYPT(UNHEX(y.standard_ave_hosp_day),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}),0), 2) as standardInHosDays,
                                 </if>
                                 <if test="queryParam.type == 3">
                                     ROUND(IFNULL(AES_DECRYPT(UNHEX(y.standard_ave_hosp_day),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}),0), 2) as standardInHosDays,
                                 </if>
                                 IFNULL(a.act_ipt,0) AS inHosDays,
                                 <if test="queryParam.type == 1">
                                     ROUND(IFNULL(AES_DECRYPT(UNHEX(y.dip_standard_avg_fee_same_lv),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}),0), 2) as standardInHosCost,
                                 </if>
                                 <if test="queryParam.type == 2">
                                     IFNULL(fp.370100_amount,0) AS Amount370100,
                                     IFNULL(psn_selfpay,0) AS psnSelfpay,
                                     IFNULL(psn_ownpay,0) AS psnOwnpay,
                                     IFNULL(acct_pay,0) AS acctPay,
                                     IFNULL(psn_cashpay,0) AS psnCashpay,

                                     <choose>
                                         <when test="queryParam.feeMulAdjmConf != null and queryParam.feeMulAdjmConf != '' and queryParam.feeMulAdjmConf == 'true' ">
                                             case when
                                             b.a54 in ('1','01','310') then '职工'
                                             else '居民' end as categories,

                                             case when
                                             b.a54 in ('1','01','310') then ROUND( IFNULL( AES_DECRYPT( UNHEX( y.standard_avg_fee ), ${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY} ), 0 )* y.adjm_cof, 2 )
                                             else ROUND(  IFNULL( AES_DECRYPT( UNHEX( y.standard_avg_fee ), ${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY} ), 0 )* y.adjm_resid_cof, 2 )
                                             end as standardInHosCost,
                                         </when>
                                         <otherwise>
                                             ROUND(IFNULL(AES_DECRYPT(UNHEX(y.standard_avg_fee),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}),0),2) AS standardInHosCost,
                                         </otherwise>
                                     </choose>
                                          </if>
                                 <if test="queryParam.type == 3">
                                     ROUND(IFNULL(AES_DECRYPT(UNHEX(y.standard_avg_fee),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}),0), 2) as standardInHosCost,
                                 </if>
                                 IFNULL(a.ipt_sumfee,0)  AS inHosTotalCost,
                                 a.dscg_way AS dscgWay,
                                 CONCAT(a.main_diag_dise_codg,a.main_diag_dise_name) AS mainDiagnoseCodeAndName,
                                 CONCAT(a.main_oprn_oprt_codg,a.main_oprn_oprt_name) AS mainOperativeCodeAndName,
                                 a.ipdr_name AS inHosDoctorName,
                                 a.atddr_name AS atddrName,
                                 a.deptdrt_name AS deptdrtName,
                                 a.chfdr_name AS chfdrName,
                                 a.train_dr_name AS trainDrName,
                                 a.qltctrl_dr_name AS qltctrlDrName,
                                 <if test="queryParam.type == 1">
                                     <if test="queryParam.queryType!=null and queryParam.queryType!=''">
                                         a.is_used_asst_list AS isUsedAsstList,
                                         a.asst_list_age_grp AS asstListAgeGrp,
                                         a.asst_list_dise_sev_deg AS asstListDiseSevDeg,
                                         a.asst_list_tmor_sev_deg AS asstListTmorSevDeg,
                                     </if>
                                 </if>
                                 a.intn_dr AS intnDr,

                                 e.grp_fale_rea
                 FROM
                      <choose>
                        <when test="queryParam.type == 1">
                            som_dip_grp_info a
                            INNER JOIN som_hi_invy_bas_info b ON a.SETTLE_LIST_ID=b.id

                            <if test='queryParam.enableSeAns != null and queryParam.enableSeAns != "" and queryParam.enableSeAns == "1"'>

                                INNER JOIN som_setl_cas_crsp p on b.k00=p.k00
                            </if>
                            left join som_drg_sco sco on sco.settle_list_id = a.settle_list_id

                            LEFT JOIN som_dept z
                                ON a.dscg_caty_codg_inhosp = z.`CODE`
                               AND a.HOSPITAL_ID = z.HOSPITAL_ID
                            INNER JOIN som_dip_grp_rcd e
                                ON a.SETTLE_LIST_ID = e.SETTLE_LIST_ID
                               AND e.active_flag = '1'

                            LEFT JOIN (
                            SELECT SUBSTR(data_val, 1, 4) AS cbd, labl_name  as  cityName
                            FROM `som_sys_code`
                            WHERE code_type = 'XZQHDM'
                            AND data_val LIKE '%00'
                            AND SUBSTRING(data_val, 1, 2) = SUBSTRING(#{queryParam.insuplcAdmdvs}, 1, 2)
                            union
                            SELECT
                            CONCAT(SUBSTR(data_val, 1, 2), '99') AS cbd,
                            labl_name AS cityName
                            FROM
                            `som_sys_code`
                            WHERE
                            code_type = 'XZQHDM'
                            AND data_val LIKE '%0000'
                            AND SUBSTRING(data_val, 1, 2) = SUBSTRING(#{queryParam.insuplcAdmdvs}, 1, 2)
                            ) t2 ON t2.cbd = SUBSTRING(b.a56, 1, 4)
                            LEFT JOIN som_dip_standard y
                                ON a.dip_codg = y.dip_codg
                               AND substr(a.dscg_time,1,4) = y.STANDARD_YEAR
                               AND a.is_used_asst_list = y.is_used_asst_list
                               AND a.asst_list_age_grp = y.asst_list_age_grp
                               AND a.asst_list_dise_sev_deg = y.asst_list_dise_sev_deg
                               AND a.asst_list_tmor_sev_deg = y.asst_list_tmor_sev_deg
                            <if test="queryParam.queryType!=null and queryParam.queryType!=''">
                                LEFT JOIN som_dip_grp_rcd dip_rcd
                                    ON a.SETTLE_LIST_ID = dip_rcd.SETTLE_LIST_ID
                                LEFT JOIN som_drg_grp_rcd m
                                    ON a.SETTLE_LIST_ID = m.SETTLE_LIST_ID
                            </if>
                        </when>
                        <when test="queryParam.type == 2">
                            som_drg_grp_info a
                            LEFT JOIN (
                            SELECT
                            hi_setl_invy_id,
                            SUM(CASE WHEN fund_pay_type  = '370100' THEN fund_payamt  ELSE 0 END) AS `370100_amount`
                            FROM som_fund_pay
                            GROUP BY hi_setl_invy_id
                            ) fp ON a.SETTLE_LIST_ID = fp.hi_setl_invy_id
                            left join som_drg_sco sco on sco.settle_list_id = a.settle_list_id
                            left JOIN som_hi_invy_bas_info b ON a.SETTLE_LIST_ID=b.id
                            LEFT JOIN (
                            SELECT SUBSTR(data_val, 1, 4) AS cbd, labl_name  as  cityName
                            FROM `som_sys_code`
                            WHERE code_type = 'XZQHDM'
                            AND data_val LIKE '%00'
                            AND SUBSTRING(data_val, 1, 2) = SUBSTRING(#{queryParam.insuplcAdmdvs}, 1, 2)
                            union
                            SELECT
                            CONCAT(SUBSTR(data_val, 1, 2), '99') AS cbd,
                            labl_name AS cityName
                            FROM
                            `som_sys_code`
                            WHERE
                            code_type = 'XZQHDM'
                            AND data_val LIKE '%0000'
                            AND SUBSTRING(data_val, 1, 2) = SUBSTRING(#{queryParam.insuplcAdmdvs}, 1, 2)
                            ) t2 ON t2.cbd = SUBSTRING(b.a56, 1, 4)
                            left JOIN som_setl_cas_crsp p on b.k00=p.k00

                            LEFT JOIN (
                            SELECT
                            SETTLE_LIST_ID,
                            GROUP_CONCAT(dscg_diag_name ORDER BY seq ASC) AS conditionDiagnosis
                            FROM
                            som_diag
                            GROUP BY
                            SETTLE_LIST_ID
                            ) w
                            ON w.SETTLE_LIST_ID = a.SETTLE_LIST_ID
                            LEFT JOIN som_dept z
                                ON a.dscg_caty_codg_inhosp = z.`CODE`
                            INNER JOIN som_drg_grp_rcd e
                                ON a.SETTLE_LIST_ID = e.SETTLE_LIST_ID
                               AND e.active_flag = '1'
                            LEFT JOIN som_drg_standard y
                                ON a.drg_codg = y.drg_codg
                            AND y.insuplc_admdvs =b.insuplc_admdvs
                            <choose>
                                <when test="queryParam.seEndTime != null and queryParam.seEndTime != '' ">
                                    AND substr(a.setl_end_time,1,4) = y.STANDARD_YEAR
                                </when>
                                <when test="queryParam.cy_end_date != null and queryParam.cy_end_date != '' ">
                                    AND substr(a.dscg_time,1,4) = y.STANDARD_YEAR
                                </when>
                            </choose>
                        </when>
                        <when test="queryParam.type == 3">
                            som_cd_grp_info a
                            <if test='queryParam.enableSeAns != null and queryParam.enableSeAns != "" and queryParam.enableSeAns == "1"'>
                                INNER JOIN som_hi_invy_bas_info b ON a.SETTLE_LIST_ID=b.id
                                INNER JOIN som_setl_cas_crsp p on b.k00=p.k00
                            </if>
                            left join som_drg_sco sco on sco.settle_list_id = a.settle_list_id
                            LEFT JOIN som_dept z
                                ON a.dscg_caty_codg_inhosp = z.`CODE`
                            INNER JOIN som_grp_rcd e
                                ON a.SETTLE_LIST_ID = e.SETTLE_LIST_ID
                               AND e.active_flag = '1'
                            LEFT JOIN som_cd_standard_info y
                                ON a.cd_codg = y.cd_codg
                            <choose>
                                <when test="queryParam.seEndTime != null and queryParam.seEndTime != '' ">
                                    AND substr(a.setl_end_time,1,4) = y.STANDARD_YEAR
                                </when>
                                <when test="queryParam.cy_end_date != null and queryParam.cy_end_date != '' ">
                                    AND substr(a.dscg_time,1,4) = y.STANDARD_YEAR
                                </when>
                            </choose>
                        </when>
                      </choose>
                 WHERE 1=1
                 <if test="queryParam.queryType=='hxbz' or queryParam.queryType=='zlxcz' or queryParam.queryType=='zdxcz' or queryParam.queryType=='xgss' or queryParam.queryType=='bszl' or queryParam.queryType=='hj'">
                     AND a.dip_codg IS NOT NULL
                 </if>
                 <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
                     AND a.HOSPITAL_ID = #{queryParam.hospitalId}
                 </if>
                 <if test="queryParam.queryType!=null and queryParam.queryType!='' and queryParam.queryType=='groupNum'">
                     AND a.grp_stas = '1'
                 </if>
                 <if test="queryParam.queryType!=null and queryParam.queryType!='' and queryParam.queryType=='noGroupNum'">
                     AND a.grp_stas = '0'
                 </if>
                 <choose>
                     <when test="queryParam.type == 1">
                         <if test="queryParam.queryType!=null and queryParam.queryType!='' and queryParam.queryType=='paiChuNum'">
                             AND e.chk_flag = '0'
                         </if>
                         <if test="queryParam.queryType!=null and queryParam.queryType!='' and queryParam.queryType=='error1'">
                             AND b.grp_fale_rea LIKE '%未找到手术级别%'
                         </if>
                         <if test="queryParam.queryType!=null and queryParam.queryType!='' and queryParam.queryType=='error2'">
                             AND b.grp_fale_rea LIKE '%未找到主要诊断节代码%'
                         </if>
                         <if test="queryParam.queryType!=null and queryParam.queryType!='' and queryParam.queryType=='error3'">
                             AND b.grp_fale_rea LIKE '%不在支付体系%'
                         </if>
                         <if test="queryParam.queryType!=null and queryParam.queryType!='' and queryParam.queryType=='error4'">
                             AND b.grp_fale_rea LIKE '%诊断编码不是医保版编码%'
                         </if>
                         <if test="queryParam.queryType!=null and queryParam.queryType!='' and queryParam.queryType=='error5'">
                             AND b.grp_fale_rea LIKE '%手术及操作编码%'
                         </if>
                         <if test="queryParam.queryType!=null and queryParam.queryType!='' and queryParam.queryType=='error6'">
                             AND b.grp_fale_rea LIKE '%主要诊断编码为空%'
                         </if>
                         <if test="queryParam.queryType!=null and queryParam.queryType!='' and queryParam.queryType=='error7'">
                             AND b.grp_fale_rea LIKE '%主要诊断编码不参与分组%'
                         </if>
                         <if test="queryParam.queryType!=null and queryParam.queryType!='' and queryParam.queryType=='error8'">
                             AND b.grp_fale_rea LIKE '%主要诊断填写不规范%'
                         </if>
                     </when>
                     <when test="queryParam.type == 2">
                         <if test="queryParam.queryType!=null and queryParam.queryType!='' and queryParam.queryType=='eliminateNum'">
                             AND a.chk_flag = '0'
                         </if>
                         <if test="queryParam.queryType!=null and queryParam.queryType!='' and queryParam.queryType=='mainCodeErrorNum'">
                             AND a.drg_codg = '0000'
                         </if>
                         <if test="queryParam.queryType!=null and queryParam.queryType!='' and queryParam.queryType=='noGroupPlanNum'">
                             AND a.drg_codg = '0001'
                         </if>
                         <if test="queryParam.queryType!=null and queryParam.queryType!='' and queryParam.queryType=='mainCodeAndOperateErrorNum'">
                             AND a.drg_codg = '0002'
                         </if>
                         <if test="queryParam.queryType!=null and queryParam.queryType!='' and queryParam.queryType=='codeAndSexErrorNum'">
                             AND a.drg_codg = '0003'
                         </if>
                         <if test="queryParam.queryType!=null and queryParam.queryType!='' and queryParam.queryType=='ageErrorNum'">
                             AND a.drg_codg = '0004'
                         </if>
                         <if test="queryParam.queryType!=null and queryParam.queryType!='' and queryParam.queryType=='xseWeightErrorNum'">
                             AND a.drg_codg = '0005'
                         </if>
                         <if test="queryParam.queryType!=null and queryParam.queryType!='' and queryParam.queryType=='xseAgeErrorNum'">
                             AND a.drg_codg = '0006'
                         </if>
                         <if test="queryParam.queryType!=null and queryParam.queryType!='' and queryParam.queryType=='groupFailNum'">
                             AND a.drg_codg IS NULL
                         </if>
                         <if test="queryParam.standType != null and queryParam.standType != ''  ">
                             <choose>
                                 <when test="queryParam.standType == 1">
                                     and  b.insuplc_admdvs = SUBSTRING(#{queryParam.insuplcAdmdvs}, 1, 4)
                                 </when>
                                 <otherwise>
                                     <!-- 这里是 else 部分的逻辑 -->
                                     and  b.insuplc_admdvs != SUBSTRING(#{queryParam.insuplcAdmdvs}, 1, 4) <!-- 你可以在这里设置默认值 -->
                                 </otherwise>
                             </choose>
                         </if>
                         <if test="queryParam.categories != null and queryParam.categories != ''">
                             <choose>
                                 <when test="queryParam.categories == 1">
                                     and   b.a54 in  ('1','01','310')
                                 </when>
                                 <otherwise>
                                     <!-- 这里是 else 部分的逻辑 -->
                                     and  b.a54 not in  ('1','01','310') <!-- 你可以在这里设置默认值 -->
                                 </otherwise>
                             </choose>
                         </if>
                     </when>
                     <when test="queryParam.type == 3">
                         <if test="queryParam.queryType!=null and queryParam.queryType!='' and queryParam.queryType=='eliminateNum'">
                             AND e.chk_flag = '0'
                         </if>
                         <if test="queryParam.queryType!=null and queryParam.queryType!='' and queryParam.queryType=='mainCodeErrorNum'">
                             AND e.grp_fale_rea = '未找到主要节代码'
                         </if>
                         <if test="queryParam.queryType!=null and queryParam.queryType!='' and queryParam.queryType=='noGroupPlanNum'">
                             AND e.grp_fale_rea = '未找到级别'
                         </if>
                         <if test="queryParam.queryType!=null and queryParam.queryType!='' and queryParam.queryType=='mainCodeAndOperateErrorNum'">
                             AND e.grp_fale_rea = '住院天数大于60天或小于0天'
                         </if>
                         <if test="queryParam.queryType!=null and queryParam.queryType!='' and queryParam.queryType=='codeAndSexErrorNum'">
                             AND e.grp_fale_rea = '编码和性别不匹配'
                         </if>
                     </when>
                 </choose>
                 <if test="queryParam.priOutHosDeptCode!=null and queryParam.priOutHosDeptCode!=''">
                     AND a.dscg_caty_codg_inhosp  =#{queryParam.priOutHosDeptCode}
                 </if>
                 <if test="queryParam.inStartTime != null and queryParam.inStartTime != '' and
                     queryParam.inEndTime != null and queryParam.inEndTime != ''">
                     AND a.adm_time BETWEEN #{queryParam.inStartTime} AND CONCAT(#{queryParam.inEndTime},' 23:59:59')
                 </if>
                 <if test="(queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and
                            queryParam.cy_end_date!=null and queryParam.cy_end_date!='') or
                           queryParam.inHosFlag == 1">
                     AND a.dscg_time BETWEEN #{queryParam.cy_start_date} and CONCAT(#{queryParam.cy_end_date},' 23:59:59')
                 </if>
                 <if test="queryParam.seStartTime != null and queryParam.seStartTime != '' and queryParam.seEndTime != null and queryParam.seEndTime != ''">
                     AND a.setl_end_time BETWEEN #{queryParam.seStartTime} AND CONCAT(#{queryParam.seEndTime},' 23:59:59')
                 </if>
                 <if test="queryParam.queryDrgsCode!=null and queryParam.queryDrgsCode!=''">
                     <choose>
                         <when test="queryParam.type == 1">
                             AND a.dip_codg = #{queryParam.queryDrgsCode}
                         </when>
                         <when test="queryParam.type == 2">
                             AND a.drg_codg = #{queryParam.queryDrgsCode}
                         </when>
                         <when test="queryParam.type == 3">
                             AND a.cd_codg = #{queryParam.queryDrgsCode}
                         </when>
                     </choose>
                 </if>
                 <if test="queryParam.drCodg!=null and queryParam.drCodg!=''">
                     AND
                        (
                         a.deptdrt_code = #{queryParam.drCodg} OR
                         a.chfdr_code = #{queryParam.drCodg} OR
                         a.atddr_code = #{queryParam.drCodg} OR
                         a.ipdr_code = #{queryParam.drCodg} OR
                         a.train_dr_code = #{queryParam.drCodg} OR
                         a.intn_dr = #{queryParam.drCodg} OR
                         a.qltctrl_dr_code = #{queryParam.drCodg}
                        )
                 </if>
                 <if test="queryParam.drName!=null and queryParam.drName!=''">
                     AND
                        (
                         ipdr_name = #{queryParam.drName} OR
                         atddr_name = #{queryParam.drName} OR
                         deptdrt_name = #{queryParam.drName} OR
                         chfdr_name = #{queryParam.drName} OR
                         train_dr_name = #{queryParam.drName} OR
                         qltctrl_dr_name = #{queryParam.drName} OR
                         intn_dr = #{queryParam.drName}
                        )
                 </if>
                 <if test="queryParam.doctorIdList!=null">
                    AND
                        (
                            a.deptdrt_code IN
                            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                               #{item}
                            </foreach>
                            OR a.chfdr_code IN
                            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                               #{item}
                            </foreach>
                            OR a.atddr_code IN
                            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                               #{item}
                            </foreach>
                            OR a.ipdr_code IN
                            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                               #{item}
                            </foreach>
                            OR a.resp_nurs_code IN
                            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                               #{item}
                            </foreach>
                            OR a.train_dr_code IN
                            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                               #{item}
                            </foreach>
                            OR a.intn_dr IN
                            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                               #{item}
                            </foreach>
                            OR a.codr_code IN
                            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                               #{item}
                            </foreach>
                            OR a.qltctrl_dr_code IN
                            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                               #{item}
                            </foreach>
                            OR a.qltctrl_nurs_code IN
                            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                               #{item}
                            </foreach>
                        )
                 </if>
             ) x
        LEFT JOIN
                 (
                     SELECT a.SETTLE_LIST_ID AS hiSetlInvyId,
                            count(1) AS digsNum,
                            group_concat(if(m.seq!='0',CONCAT(m.dscg_diag_codg,m.dscg_diag_name),null)) AS otherDiagnoseCodeAndName
                     FROM
                          <choose>
                              <when test="queryParam.type == 1">
                                  som_dip_grp_info a
                              </when>
                              <when test="queryParam.type == 2">
                                  som_drg_grp_info a
                              </when>
                              <when test="queryParam.type == 3">
                                  som_cd_grp_info a
                              </when>
                          </choose>
                     INNER JOIN som_diag m
                        ON a.SETTLE_LIST_ID = m.SETTLE_LIST_ID
                     WHERE m.TYPE='1'
                       AND m.dscg_diag_codg!='-'
                       AND m.dscg_diag_codg!='--'
                     <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
                         AND a.HOSPITAL_ID = #{queryParam.hospitalId}
                     </if>
                     <if test="queryParam.priOutHosDeptCode!=null and queryParam.priOutHosDeptCode!=''">
                         AND a.dscg_caty_codg_inhosp  =#{queryParam.priOutHosDeptCode}
                     </if>
                     <if test="queryParam.inStartTime != null and queryParam.inStartTime != '' and
                                  queryParam.inEndTime != null and queryParam.inEndTime != ''">
                         AND a.adm_time BETWEEN #{queryParam.inStartTime} AND CONCAT(#{queryParam.inEndTime},' 23:59:59')
                     </if>
                     <if test="(queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and
                                queryParam.cy_end_date!=null and queryParam.cy_end_date!='') or
                               queryParam.inHosFlag == 2">
                         AND a.dscg_time BETWEEN #{queryParam.cy_start_date} and CONCAT(#{queryParam.cy_end_date},' 23:59:59')
                     </if>
                     <if test="queryParam.doctorIdList!=null">
                         AND
                             (
                                a.deptdrt_code IN
                                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                                   #{item}
                                </foreach>
                                OR a.chfdr_code in
                                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                                   #{item}
                                </foreach>
                                OR a.atddr_code in
                                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                                   #{item}
                                </foreach>
                                OR a.ipdr_code in
                                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                                   #{item}
                                </foreach>
                                OR a.resp_nurs_code in
                                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                                   #{item}
                                </foreach>
                                OR a.train_dr_code in
                                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                                   #{item}
                                </foreach>
                                OR a.intn_dr in
                                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                                   #{item}
                                </foreach>
                                OR a.codr_code in
                                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                                   #{item}
                                </foreach>
                                OR a.qltctrl_dr_code in
                                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                                   #{item}
                                </foreach>
                                OR a.qltctrl_nurs_code in
                                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                                   #{item}
                                </foreach>
                             )
                     </if>
                     GROUP BY a.SETTLE_LIST_ID
                 ) y
            ON x.hiSetlInvyId = y.hiSetlInvyId
        LEFT JOIN
                 (
                     SELECT a.SETTLE_LIST_ID AS hiSetlInvyId,
                            count(1) AS oprNum,
                            group_concat(if(m.oprn_oprt_lv='1',CONCAT(m.C35C,m.C36N),null))   AS oneLevelOprCodeAndName,
                            group_concat(if(n.oprn_lv='1',CONCAT(m.C35C,m.C36N),null))   AS oneLevelStanOprCodeAndName,
                            group_concat(if(m.oprn_oprt_lv='2',CONCAT(m.C35C,m.C36N),null))   AS twoLevelOprCodeAndName,
                            group_concat(if(n.oprn_lv='2',CONCAT(m.C35C,m.C36N),null))   AS twoLevelStanOprCodeAndName,
                            group_concat(if(m.oprn_oprt_lv='3',CONCAT(m.C35C,m.C36N),null))   AS threeLevelOprCodeAndName,
                            group_concat(if(n.oprn_lv='3',CONCAT(m.C35C,m.C36N),null))   AS threeLevelStanOprCodeAndName,
                            group_concat(if(m.oprn_oprt_lv='4',CONCAT(m.C35C,m.C36N),null))   AS fourLevelOprCodeAndName,
                            group_concat(if(n.oprn_lv='4',CONCAT(m.C35C,m.C36N),null))   AS fourLevelStanOprCodeAndName
                     FROM
                          <choose>
                              <when test="queryParam.type == 1">
                                  som_dip_grp_info a
                              </when>
                              <when test="queryParam.type == 2">
                                  som_drg_grp_info a
                              </when>
                              <when test="queryParam.type == 3">
                                  som_cd_grp_info a
                              </when>
                          </choose>
                     INNER JOIN som_oprn_oprt_info m
                        ON a.SETTLE_LIST_ID = m.SETTLE_LIST_ID
                     LEFT JOIN som_oprn_lv n
                        ON m.c35c = n.oprn_oprt_codg
                     WHERE m.C35C IS NOT NULL
                       AND m.C35C!='-'
                       AND m.C35C!='--'
                     <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
                         AND a.HOSPITAL_ID = #{queryParam.hospitalId}
                     </if>
                     <if test="queryParam.priOutHosDeptCode!=null and queryParam.priOutHosDeptCode!=''">
                         AND a.dscg_caty_codg_inhosp  =#{queryParam.priOutHosDeptCode}
                     </if>
                     <if test="queryParam.inStartTime != null and queryParam.inStartTime != '' and
                                               queryParam.inEndTime != null and queryParam.inEndTime != ''">
                         AND a.adm_time BETWEEN #{queryParam.inStartTime} AND CONCAT(#{queryParam.inEndTime},' 23:59:59')
                     </if>
                     <if test="(queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and
                                             queryParam.cy_end_date!=null and queryParam.cy_end_date!='') or
                                            queryParam.inHosFlag == 2">
                         AND a.dscg_time BETWEEN #{queryParam.cy_start_date} and CONCAT(#{queryParam.cy_end_date},' 23:59:59')
                     </if>
                     <if test="queryParam.doctorIdList!=null">
                         AND
                             (
                                 a.deptdrt_code IN
                                 <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                                     #{item}
                                 </foreach>
                                 OR a.chfdr_code IN
                                 <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                                     #{item}
                                 </foreach>
                                 OR a.atddr_code IN
                                 <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                                     #{item}
                                 </foreach>
                                 OR a.ipdr_code IN
                                 <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                                     #{item}
                                 </foreach>
                                 OR a.resp_nurs_code IN
                                 <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                                     #{item}
                                 </foreach>
                                 OR a.train_dr_code IN
                                 <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                                     #{item}
                                 </foreach>
                                 OR a.intn_dr IN
                                 <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                                     #{item}
                                 </foreach>
                                 OR a.codr_code IN
                                 <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                                     #{item}
                                 </foreach>
                                 OR a.qltctrl_dr_code IN
                                 <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                                     #{item}
                                 </foreach>
                                 OR a.qltctrl_nurs_code IN
                                 <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                                     #{item}
                                 </foreach>
                             )
                     </if>
                     GROUP BY a.SETTLE_LIST_ID
                 ) z
            ON x.hiSetlInvyId = z.hiSetlInvyId
        <if test="queryParam.queryIcdCode!=null and queryParam.queryIcdCode!=''">   <!--查询某单个疾病下病案详情-->
            where x.hiSetlInvyId in (
                select
                  a.SETTLE_LIST_ID
                from
                <choose>
                    <when test="queryParam.type == 1">
                        som_dip_grp_info a
                    </when>
                    <when test="queryParam.type == 2">
                        som_drg_grp_info a
                    </when>
                    <when test="queryParam.type == 3">
                        som_cd_grp_info a
                    </when>
                </choose>
                inner join som_diag m on a.SETTLE_LIST_ID = m.SETTLE_LIST_ID
                where m.TYPE='1' AND  m.dscg_diag_codg = #{queryParam.queryIcdCode}
                <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
                    AND  a.HOSPITAL_ID = #{queryParam.hospitalId}
                </if>
                <if test="queryParam.priOutHosDeptCode!=null and queryParam.priOutHosDeptCode!=''">
                    AND a.dscg_caty_codg_inhosp  =#{queryParam.priOutHosDeptCode}
                </if>
                <if test="queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and
                        queryParam.cy_end_date!=null and queryParam.cy_end_date!=''">
                    AND a.dscg_time BETWEEN #{queryParam.cy_start_date} and CONCAT(CONCAT(#{queryParam.cy_end_date},' 23:59:59'),' 23:59:59')
                </if>
                <if test="queryParam.doctorIdList!=null">
                    AND (
                    a.deptdrt_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    a.chfdr_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    a.atddr_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    a.ipdr_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    a.resp_nurs_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    a.train_dr_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    a.intn_dr in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    a.codr_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    a.qltctrl_dr_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    a.qltctrl_nurs_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    )
                </if>
            )
        </if>

        <if test="queryParam.queryType!=null and queryParam.queryType!='' and  queryParam.queryType=='hxbz'">   <!--hxbz-->
            where (not regexp_like(x.drgsName, '(治疗性操作组|诊断性操作组|相关手术组|保守治疗组|治疗性操作|诊断性操作|相关手术|保守治疗)') or x.drgsName IS NULL)
        </if>
        <if test="queryParam.queryType!=null and queryParam.queryType!='' and  queryParam.queryType=='zlxcz'">   <!--zlxcz-->
            where (regexp_like(x.drgsName, '治疗性操作组') or regexp_like(x.drgsName, '治疗性操作'))
        </if>
        <if test="queryParam.queryType!=null and queryParam.queryType!='' and  queryParam.queryType=='zdxcz'">   <!--zdxcz-->
            where (regexp_like(x.drgsName, '诊断性操作组') or regexp_like(x.drgsName, '诊断性操作'))
        </if>
        <if test="queryParam.queryType!=null and queryParam.queryType!='' and  queryParam.queryType=='xgss'">   <!--xgss-->
            where (regexp_like(x.drgsName, '相关手术组') or regexp_like(x.drgsName, '相关手术'))
        </if>
        <if test="queryParam.queryType!=null and queryParam.queryType!='' and  queryParam.queryType=='bszl'">   <!--bszl-->
            where (regexp_like(x.drgsName, '保守治疗组') or regexp_like(x.drgsName, '保守治疗'))
        </if>
        <if test="queryParam.queryType!=null and queryParam.queryType!='' and  queryParam.queryType=='hj'">   <!--hj-->
            where regexp_like(x.drgsName, '(治疗性操作组|诊断性操作组|相关手术组|保守治疗组|治疗性操作|诊断性操作|相关手术|保守治疗)')
        </if>

        <if test="queryParam.queryType!=null and queryParam.queryType!='' and queryParam.queryType=='seriousComplicationDrgsMedicalNum'">   <!--查询伴严重合并症及伴随病的DRGs组DRG详情-->
            where right(x.drgsCode,1) = '1'
        </if>
        <if test="queryParam.queryType!=null and queryParam.queryType!='' and queryParam.queryType=='comorbiditiesComplicationDrgsMedicalNum'">   <!--查询伴合并症或并发症的DRGs组DRG详情-->
            where right(x.drgsCode,1) = '2'
        </if>
        <if test="queryParam.queryType!=null and queryParam.queryType!='' and queryParam.queryType=='normalComplicationDrgsMedicalNum'">   <!--查询伴一般合并症及伴随病的DRGs组DRG详情-->
            where right(x.drgsCode,1) = '3'
        </if>
        <if test="queryParam.queryType!=null and queryParam.queryType!='' and queryParam.queryType=='noSeriousComorbiditiesComplicationDrgsMedicalNum'">   <!--查询不伴严重合并症或并发症的DRGs组DRG详情-->
            where right(x.drgsCode,1) = '4'
        </if>
        <if test="queryParam.queryType!=null and queryParam.queryType!='' and queryParam.queryType=='noComplicationDrgsMedicalNum'">   <!--查询伴不伴合并症及伴随病的DRGs组DRG详情-->
            where right(x.drgsCode,1) = '5'
        </if>
        <if test="queryParam.queryType!=null and queryParam.queryType!='' and queryParam.queryType=='notDifferentiatedDrgsMedicalNum'">   <!--查询未作区分的DRGs组DRG详情-->
            where right(x.drgsCode,1) = '9'
        </if>

        <if test="queryParam.queryType!=null and queryParam.queryType!='' and queryParam.queryType=='medicalDeptDrgsMedicalNum'">   <!--查询内科组病案详情-->
            <choose>
                <when test="queryParam.type == 1">
                    where SUBSTR(x.dip_drg_code,2,1) in ('R','S','T','U','V','W','X','Y','Z')
                </when>
                <when test="queryParam.type == 2">
                    where SUBSTR(x.drgsCode,2,1) in ('R','S','T','U','V','W','X','Y','Z')
                </when>
                <when test="queryParam.type == 3">
                    where SUBSTR(x.drgsCode,2,1) in ('R','S','T','U','V','W','X','Y','Z')
                </when>
            </choose>
        </if>
        <if test="queryParam.queryType!=null and queryParam.queryType!='' and queryParam.queryType=='notOperationDrgsMedicalNum'">   <!--查询非手术室操作组病案详情-->
            <choose>
                <when test="queryParam.type == 1">
                    where SUBSTR(x.dip_drg_code,2,1) in ('K','L','M','N','P','Q')
                </when>
                <when test="queryParam.type == 2">
                    where SUBSTR(x.drgsCode,2,1) in ('K','L','M','N','P','Q')
                </when>
                <when test="queryParam.type == 3">
                    where SUBSTR(x.drgsCode,2,1) in ('K','L','M','N','P','Q')
                </when>
            </choose>
        </if>
        <if test="queryParam.queryType!=null and queryParam.queryType!='' and queryParam.queryType=='surgeryDeptDrgsMedicalNum'">   <!--查询外科组病案详情-->
            <choose>
                <when test="queryParam.type == 1">
                    where SUBSTR(x.dip_drg_code,2,1) in ('A','B','C','D','E','F','G','H','J')
                </when>
                <when test="queryParam.type == 2">
                    where SUBSTR(x.drgsCode,2,1) in ('A','B','C','D','E','F','G','H','J')
                </when>
                <when test="queryParam.type == 3">
                    where SUBSTR(x.drgsCode,2,1) in ('A','B','C','D','E','F','G','H','J')
                </when>
            </choose>
        </if>
        <if test="queryParam.allOprLevelCode!=null and queryParam.allOprLevelCode!=''">   <!--查询总手术例数下病案详情-->
            where x.hiSetlInvyId in (
                select
                  a.SETTLE_LIST_ID
                from
                <choose>
                    <when test="queryParam.type == 1">
                        som_dip_grp_info a
                    </when>
                    <when test="queryParam.type == 2">
                        som_drg_grp_info a
                    </when>
                    <when test="queryParam.type == 3">
                        som_cd_grp_info a
                    </when>
                </choose>
                inner join som_oprn_oprt_info m on a.SETTLE_LIST_ID = m.SETTLE_LIST_ID
                left join som_oprn_lv n on m.c35c = n.oprn_oprt_codg
                where m.C35C is not null AND m.C35C!='-' and  m.C35C!='--'
                <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
                    AND  a.HOSPITAL_ID = #{queryParam.hospitalId}
                </if>
                <if test="queryParam.priOutHosDeptCode!=null and queryParam.priOutHosDeptCode!=''">
                    AND a.dscg_caty_codg_inhosp  =#{queryParam.priOutHosDeptCode}
                </if>
                <if test="queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and
                                queryParam.cy_end_date!=null and queryParam.cy_end_date!=''">
                    AND a.dscg_time BETWEEN #{queryParam.cy_start_date} and CONCAT(CONCAT(#{queryParam.cy_end_date},' 23:59:59'),' 23:59:59')
                </if>
                <if test="queryParam.doctorIdList!=null">
                    AND (
                    a.deptdrt_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    a.chfdr_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    a.atddr_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    a.ipdr_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    a.resp_nurs_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    a.train_dr_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    a.intn_dr in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    a.codr_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    a.qltctrl_dr_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    a.qltctrl_nurs_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    )
                </if>
            )
        </if>
        <if test="queryParam.oprLevelCode!=null and queryParam.oprLevelCode!=''">   <!--查询某院内手术级别下病案详情-->
            where x.hiSetlInvyId in (
                select
                  a.SETTLE_LIST_ID
                from
                <choose>
                    <when test="queryParam.type == 1">
                        som_dip_grp_info a
                    </when>
                    <when test="queryParam.type == 2">
                        som_drg_grp_info a
                    </when>
                    <when test="queryParam.type == 3">
                        som_cd_grp_info a
                    </when>
                </choose>
                inner join som_oprn_oprt_info m on a.SETTLE_LIST_ID = m.SETTLE_LIST_ID
                left join som_oprn_lv n on m.c35c = n.oprn_oprt_codg
                where m.C35C is not null AND m.C35C!='-' and  m.C35C!='--'
                <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
                    AND  a.HOSPITAL_ID = #{queryParam.hospitalId}
                </if>
                <if test="queryParam.priOutHosDeptCode!=null and queryParam.priOutHosDeptCode!=''">
                    AND a.dscg_caty_codg_inhosp  =#{queryParam.priOutHosDeptCode}
                </if>
                <if test="queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and
                            queryParam.cy_end_date!=null and queryParam.cy_end_date!=''">
                    AND a.dscg_time BETWEEN #{queryParam.cy_start_date} and CONCAT(CONCAT(#{queryParam.cy_end_date},' 23:59:59'),' 23:59:59')
                </if>
                <if test="queryParam.oprLevelCode!='other'">
                    AND m.oprn_oprt_lv = #{queryParam.oprLevelCode}
                </if>
                <if test="queryParam.oprLevelCode=='other'">
                    AND (m.oprn_oprt_lv not in ('1' ,'2' , '3' , '4') or m.oprn_oprt_lv is null)
                </if>
                <if test="queryParam.doctorIdList!=null">
                    AND (
                    a.deptdrt_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    a.chfdr_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    a.atddr_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    a.ipdr_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    a.resp_nurs_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    a.train_dr_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    a.intn_dr in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    a.codr_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    a.qltctrl_dr_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    a.qltctrl_nurs_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    )
                </if>
            )
        </if>
        <if test="queryParam.stanOprLevelCode!=null and queryParam.stanOprLevelCode!=''">   <!--查询某标准手术级别下病案详情-->
            where x.hiSetlInvyId in (
                select
                  a.SETTLE_LIST_ID
                from
                <choose>
                    <when test="queryParam.type == 1">
                        som_dip_grp_info a
                    </when>
                    <when test="queryParam.type == 2">
                        som_drg_grp_info a
                    </when>
                    <when test="queryParam.type == 3">
                        som_cd_grp_info a
                    </when>
                </choose>
                inner join som_oprn_oprt_info m on a.SETTLE_LIST_ID = m.SETTLE_LIST_ID
                left join som_oprn_lv n on m.c35c = n.oprn_oprt_codg
                where m.C35C is not null AND m.C35C!='-' and  m.C35C!='--'
                <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
                    AND  a.HOSPITAL_ID = #{queryParam.hospitalId}
                </if>
                <if test="queryParam.priOutHosDeptCode!=null and queryParam.priOutHosDeptCode!=''">
                    AND a.dscg_caty_codg_inhosp  =#{queryParam.priOutHosDeptCode}
                </if>
                <if test="queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and
                                queryParam.cy_end_date!=null and queryParam.cy_end_date!=''">
                    AND a.dscg_time BETWEEN #{queryParam.cy_start_date} and CONCAT(#{queryParam.cy_end_date},' 23:59:59')
                </if>
                <if test="queryParam.stanOprLevelCode!='other'">
                    AND n.oprn_lv = #{queryParam.stanOprLevelCode}
                </if>
                <if test="queryParam.stanOprLevelCode=='other'">
                    AND (n.oprn_lv not in ('1' ,'2' , '3' , '4') or n.oprn_lv is null)
                </if>
                <if test="queryParam.doctorIdList!=null">
                    AND (
                    a.deptdrt_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    a.chfdr_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    a.atddr_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    a.ipdr_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    a.resp_nurs_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    a.train_dr_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    a.intn_dr in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    a.codr_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    a.qltctrl_dr_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    a.qltctrl_nurs_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    )
                </if>
            )
        </if>
        <if test="queryParam.queryDiagnosisType!=null and queryParam.queryDiagnosisType!=''">  <!--查询疾病大类病案详情-->
            where x.hiSetlInvyId in (
                select
                  a.SETTLE_LIST_ID
                from
                <choose>
                    <when test="queryParam.type == 1">
                        som_dip_grp_info a
                    </when>
                    <when test="queryParam.type == 2">
                        som_drg_grp_info a
                    </when>
                    <when test="queryParam.type == 3">
                        som_cd_grp_info a
                    </when>
                </choose>
                inner join som_diag b on a.SETTLE_LIST_ID = b.SETTLE_LIST_ID
                where b.TYPE='1'
                <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
                    AND  a.HOSPITAL_ID = #{queryParam.hospitalId}
                </if>
                <if test="queryParam.priOutHosDeptCode!=null and queryParam.priOutHosDeptCode!=''">
                    AND a.dscg_caty_codg_inhosp  =#{queryParam.priOutHosDeptCode}
                </if>
                <if test="queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and
                            queryParam.cy_end_date!=null and queryParam.cy_end_date!=''">
                    AND a.dscg_time BETWEEN #{queryParam.cy_start_date} and CONCAT(#{queryParam.cy_end_date},' 23:59:59')
                </if>
                <if test="queryParam.queryDiagnosisType=='a00b99'">
                    AND (SUBSTR(b.dscg_diag_codg,1,1)='A' or SUBSTR(b.dscg_diag_codg,1,1)='B')
                </if>
                <if test="queryParam.queryDiagnosisType=='c00d48'">
                    <![CDATA[  AND ((SUBSTR(b.dscg_diag_codg,1,1)='C') or
                    (SUBSTR(b.dscg_diag_codg,1,1)='D' and CONVERT(substr(b.dscg_diag_codg,2,2),SIGNED)>=0 and CONVERT(SUBSTR(b.dscg_diag_codg,2,2),SIGNED)<=48))  ]]>
                </if>
                <if test="queryParam.queryDiagnosisType=='d50d89'">
                    <![CDATA[ AND (SUBSTR(b.dscg_diag_codg,1,1)='D' and CONVERT(substr(b.dscg_diag_codg,2,2),SIGNED)>=50 and CONVERT(SUBSTR(b.dscg_diag_codg,2,2),SIGNED)<=89)  ]]>
                </if>
                <if test="queryParam.queryDiagnosisType=='e00e90'">
                    <![CDATA[ AND (SUBSTR(b.dscg_diag_codg,1,1)='E' and CONVERT(substr(b.dscg_diag_codg,2,2),SIGNED)>=0 and CONVERT(SUBSTR(b.dscg_diag_codg,2,2),SIGNED)<=90)  ]]>
                </if>
                <if test="queryParam.queryDiagnosisType=='f00f99'">
                    AND SUBSTR(b.dscg_diag_codg,1,1)='F'
                </if>
                <if test="queryParam.queryDiagnosisType=='g00g99'">
                    AND SUBSTR(b.dscg_diag_codg,1,1)='G'
                </if>
                <if test="queryParam.queryDiagnosisType=='h00h59'">
                    <![CDATA[ AND (SUBSTR(b.dscg_diag_codg,1,1)='H' and CONVERT(substr(b.dscg_diag_codg,2,2),SIGNED)>=0 and CONVERT(SUBSTR(b.dscg_diag_codg,2,2),SIGNED)<=59) ]]>
                </if>
                <if test="queryParam.queryDiagnosisType=='h60h95'">
                    <![CDATA[ AND (SUBSTR(b.dscg_diag_codg,1,1)='H' and CONVERT(substr(b.dscg_diag_codg,2,2),SIGNED)>=60 and CONVERT(SUBSTR(b.dscg_diag_codg,2,2),SIGNED)<=95) ]]>
                </if>
                <if test="queryParam.queryDiagnosisType=='i00i99'">
                    AND SUBSTR(b.dscg_diag_codg,1,1)='I'
                </if>
                <if test="queryParam.queryDiagnosisType=='j00j99'">
                    AND SUBSTR(b.dscg_diag_codg,1,1)='J'
                </if>
                <if test="queryParam.queryDiagnosisType=='k00k93'">
                    <![CDATA[ AND (SUBSTR(b.dscg_diag_codg,1,1)='K' and CONVERT(substr(b.dscg_diag_codg,2,2),SIGNED)>=0 and CONVERT(SUBSTR(b.dscg_diag_codg,2,2),SIGNED)<=93) ]]>
                </if>
                <if test="queryParam.queryDiagnosisType=='l00l99'">
                    AND SUBSTR(b.dscg_diag_codg,1,1)='L'
                </if>
                <if test="queryParam.queryDiagnosisType=='m00m99'">
                    AND SUBSTR(b.dscg_diag_codg,1,1)='M'
                </if>
                <if test="queryParam.queryDiagnosisType=='n00n99'">
                    AND SUBSTR(b.dscg_diag_codg,1,1)='N'
                </if>
                <if test="queryParam.queryDiagnosisType=='o00o99'">
                    AND SUBSTR(b.dscg_diag_codg,1,1)='O'
                </if>
                <if test="queryParam.queryDiagnosisType=='p00p96'">
                    <![CDATA[ AND (SUBSTR(b.dscg_diag_codg,1,1)='P' and CONVERT(substr(b.dscg_diag_codg,2,2),SIGNED)>=0 and CONVERT(SUBSTR(b.dscg_diag_codg,2,2),SIGNED)<=96) ]]>
                </if>
                <if test="queryParam.queryDiagnosisType=='q00q99'">
                    AND SUBSTR(b.dscg_diag_codg,1,1)='Q'
                </if>
                <if test="queryParam.queryDiagnosisType=='r00r99'">
                    AND SUBSTR(b.dscg_diag_codg,1,1)='R'
                </if>
                <if test="queryParam.queryDiagnosisType=='s00t98'">
                    <![CDATA[ AND ((SUBSTR(b.dscg_diag_codg,1,1)='S') or
                    (SUBSTR(b.dscg_diag_codg,1,1)='T' and CONVERT(substr(b.dscg_diag_codg,2,2),SIGNED)>=0 and CONVERT(SUBSTR(b.dscg_diag_codg,2,2),SIGNED)<=98))  ]]>
                </if>
                <if test="queryParam.queryDiagnosisType=='u00u99'">
                    AND SUBSTR(b.dscg_diag_codg,1,1)='U'
                </if>
                <if test="queryParam.queryDiagnosisType=='v01y98'">
                    <![CDATA[  AND ((SUBSTR(b.dscg_diag_codg,1,1)='V') or (SUBSTR(b.dscg_diag_codg,1,1)='W') or (SUBSTR(b.dscg_diag_codg,1,1)='X') or
                    (SUBSTR(b.dscg_diag_codg,1,1)='Y' and CONVERT(substr(b.dscg_diag_codg,2,2),SIGNED)>=01 and CONVERT(SUBSTR(b.dscg_diag_codg,2,2),SIGNED)<=98))   ]]>
                </if>
                <if test="queryParam.queryDiagnosisType=='z00z99'">
                    AND SUBSTR(b.dscg_diag_codg,1,1)='Z'
                </if>
                <if test="queryParam.queryDiagnosisType=='other'">
                    <![CDATA[  AND ((SUBSTR(b.dscg_diag_codg,1,3)='D49') or
                    (SUBSTR(b.dscg_diag_codg,1,1)='D' and CONVERT(substr(b.dscg_diag_codg,2,2),SIGNED)>=90 and CONVERT(SUBSTR(b.dscg_diag_codg,2,2),SIGNED)<=99) or
                    (SUBSTR(b.dscg_diag_codg,1,1)='E' and CONVERT(substr(b.dscg_diag_codg,2,2),SIGNED)>=91 and CONVERT(SUBSTR(b.dscg_diag_codg,2,2),SIGNED)<=99) or
                    (SUBSTR(b.dscg_diag_codg,1,1)='K' and CONVERT(substr(b.dscg_diag_codg,2,2),SIGNED)>=94 and CONVERT(SUBSTR(b.dscg_diag_codg,2,2),SIGNED)<=99) or
                    (SUBSTR(b.dscg_diag_codg,1,1)='P' and CONVERT(substr(b.dscg_diag_codg,2,2),SIGNED)>=97 and CONVERT(SUBSTR(b.dscg_diag_codg,2,2),SIGNED)<=99))   ]]>
                </if>
                <if test="queryParam.doctorIdList!=null">
                    AND (
                    a.deptdrt_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    a.chfdr_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    a.atddr_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    a.ipdr_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    a.resp_nurs_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    a.train_dr_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    a.intn_dr in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    a.codr_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    a.qltctrl_dr_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    a.qltctrl_nurs_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    )
                </if>
            )
        </if>
    </select>

    <select id="queryDeptDetailList" resultType="com.my.som.vo.common.DeptMainDetailInfo">
        select
            x.*,
            IFNULL(CONCAT(ROUND(x.medicalTotalNum/NULLIF(y.medicalTotalNum,0)*100,2),'%'),0) AS medicalTotalNumRate,
            IFNULL(CONCAT(ROUND(x.sumfee/NULLIF(y.sumfee,0)*100,2),'%'),0) AS totalCostRate,
        <!--IFNULL(CONCAT(ROUND(x.totalBaseCost/NULLIF(y.totalBaseCost,0)*100,2),'%'),0) AS totalBaseCostRate,-->
        IFNULL(CONCAT(ROUND(x.totalDays/NULLIF(y.totalDays,0)*100,2),'%'),0) AS totalDaysRate
        from(
            select p.*,
                   k.name AS priOutHosDeptName
            from (
                select
                    <choose>
                        <when test="queryParam.type == 1">
                            dip_codg AS drgsCode,
                            DIP_NAME AS drgsName,
                            ROUND(IFNULL(SUM(dip_wt),0),2) AS totalDrgWeight,
                        </when>
                        <when test="queryParam.type == 2">
                            drg_codg AS drgsCode,
                            DRG_NAME AS drgsName,
                            ROUND(IFNULL(SUM(drg_wt),0),2) AS totalDrgWeight,
                        </when>
                        <when test="queryParam.type == 3">
                            cd_codg AS drgsCode,
                            CD_NAME AS drgsName,
                            ROUND(IFNULL(SUM(cd_wt),0),2) AS totalDrgWeight,
                        </when>
                    </choose>
                    dscg_caty_codg_inhosp AS priOutHosDeptCode,
                    COUNT(1) AS medicalTotalNum,
                    IFNULL(SUM(ipt_sumfee),0) AS sumfee,
                    ROUND(IFNULL(AVG(ipt_sumfee),0), 2) AS avgCost,
                    IFNULL(SUM(act_ipt),0) AS totalDays,
                    ROUND(IFNULL(AVG(act_ipt),0), 2) AS avgDays
                from
                <choose>
                    <when test="queryParam.type == 1">
                        som_dip_grp_info a
                        <if test='queryParam.enableSeAns != null and queryParam.enableSeAns != "" and queryParam.enableSeAns == "1"'>
                            INNER JOIN som_hi_invy_bas_info b on  a.SETTLE_LIST_ID=b.id
                            INNER JOIN som_setl_cas_crsp p on p.K00=b.K00
                        </if>
                    </when>
                    <when test="queryParam.type == 2">
                        som_drg_grp_info a
                        <if test='queryParam.enableSeAns != null and queryParam.enableSeAns != "" and queryParam.enableSeAns == "1"'>
                            INNER JOIN som_hi_invy_bas_info b on  a.SETTLE_LIST_ID=b.id
                            INNER JOIN som_setl_cas_crsp p on p.K00=b.K00
                        </if>
                    </when>
                    <when test="queryParam.type == 3">
                        som_cd_grp_info a
                        <if test='queryParam.enableSeAns != null and queryParam.enableSeAns != "" and queryParam.enableSeAns == "1"'>
                            INNER JOIN som_hi_invy_bas_info b on  a.SETTLE_LIST_ID=b.id
                            INNER JOIN som_setl_cas_crsp p on p.K00=b.K00
                        </if>
                    </when>
                </choose>
                where grp_stas = '1'
                <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
                    AND  a.HOSPITAL_ID = #{queryParam.hospitalId}
                </if>
                <if test="queryParam.priOutHosDeptCode!=null and queryParam.priOutHosDeptCode!=''">
                    AND dscg_caty_codg_inhosp  =#{queryParam.priOutHosDeptCode}
                </if>
                <if test="queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and
                queryParam.cy_end_date!=null and queryParam.cy_end_date!=''">
                    AND dscg_time BETWEEN #{queryParam.cy_start_date} and CONCAT(#{queryParam.cy_end_date},' 23:59:59')
                </if>
                <if test="queryParam.seEndTime!=null and queryParam.seEndTime != '' and queryParam.seStartTime != null and queryParam.seStartTime != ''">
                    AND a.setl_end_time BETWEEN #{queryParam.seStartTime} and CONCAT(#{queryParam.seEndTime},' 23:59:59')
                </if>
                <if test="queryParam.inStartTime != null and queryParam.inStartTime != '' and queryParam.inEndTime != null and queryParam.inEndTime != ''">
                    AND a.adm_time BETWEEN #{queryParam.inStartTime} AND CONCAT(#{queryParam.inEndTime},' 23:59:59')
                </if>
                <if test="queryParam.queryDrgsCode!=null and queryParam.queryDrgsCode!=''">
                    <choose>
                        <when test="queryParam.type == 1">
                            AND dip_codg = #{queryParam.queryDrgsCode}
                        </when>
                        <when test="queryParam.type == 2">
                            AND drg_codg = #{queryParam.queryDrgsCode}
                        </when>
                        <when test="queryParam.type == 3">
                            AND cd_codg = #{queryParam.queryDrgsCode}
                        </when>
                    </choose>
                </if>
                <if test="queryParam.doctorIdList!=null">
                    AND (
                    deptdrt_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    chfdr_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    atddr_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    ipdr_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    resp_nurs_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    train_dr_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    intn_dr in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    codr_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    qltctrl_dr_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR
                    qltctrl_nurs_code in
                    <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    )
                </if>
                GROUP BY dscg_caty_codg_inhosp
                 <trim prefix=",">
                    <choose>
                        <when test="queryParam.type == 1">
                            dip_codg,DIP_NAME
                        </when>
                        <when test="queryParam.type == 2">
                            drg_codg,DRG_NAME
                        </when>
                        <when test="queryParam.type == 3">
                            cd_codg,CD_NAME
                        </when>
                    </choose>
                </trim>
            ) p
            left join som_dept k on p.priOutHosDeptCode = k.code and k.active_flag = '1'
        ) x cross join
        (
            select
                COUNT(1) AS medicalTotalNum,
                IFNULL(SUM(ipt_sumfee),0) AS sumfee,
                IFNULL(SUM(act_ipt),0) AS totalDays
            from
            <choose>
                <when test="queryParam.type == 1">
                    som_dip_grp_info a
                    <if test='queryParam.enableSeAns != null and queryParam.enableSeAns != "" and queryParam.enableSeAns == "1"'>
                        INNER JOIN som_hi_invy_bas_info b on  a.SETTLE_LIST_ID=b.id
                        INNER JOIN som_setl_cas_crsp p on p.K00=b.K00
                    </if>
                </when>
                <when test="queryParam.type == 2">
                    som_drg_grp_info a
                    <if test='queryParam.enableSeAns != null and queryParam.enableSeAns != "" and queryParam.enableSeAns == "1"'>
                        INNER JOIN som_hi_invy_bas_info b on  a.SETTLE_LIST_ID=b.id
                        INNER JOIN som_setl_cas_crsp p on p.K00=b.K00
                    </if>
                </when>
                <when test="queryParam.type == 3">
                    som_cd_grp_info a
                    <if test='queryParam.enableSeAns != null and queryParam.enableSeAns != "" and queryParam.enableSeAns == "1"'>
                        INNER JOIN som_hi_invy_bas_info b on  a.SETTLE_LIST_ID=b.id
                        INNER JOIN som_setl_cas_crsp p on p.K00=b.K00
                    </if>
                </when>
            </choose>
            where grp_stas = '1'
            <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
                AND  a.HOSPITAL_ID = #{queryParam.hospitalId}
            </if>
            <if test="queryParam.priOutHosDeptCode!=null and queryParam.priOutHosDeptCode!=''">
                AND dscg_caty_codg_inhosp  =#{queryParam.priOutHosDeptCode}
            </if>
            <if test="queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and
                queryParam.cy_end_date!=null and queryParam.cy_end_date!=''">
                AND dscg_time BETWEEN #{queryParam.cy_start_date} and CONCAT(#{queryParam.cy_end_date},' 23:59:59')
            </if>
            <if test="queryParam.queryDrgsCode!=null and queryParam.queryDrgsCode!=''">
                <choose>
                    <when test="queryParam.type == 1">
                        AND dip_codg = #{queryParam.queryDrgsCode}
                    </when>
                    <when test="queryParam.type == 2">
                        AND drg_codg = #{queryParam.queryDrgsCode}
                    </when>
                    <when test="queryParam.type == 3">
                        AND cd_codg = #{queryParam.queryDrgsCode}
                    </when>
                </choose>
            </if>
            <if test="queryParam.doctorIdList!=null">
                AND (
                deptdrt_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                chfdr_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                atddr_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                ipdr_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                resp_nurs_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                train_dr_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                intn_dr in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                codr_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                qltctrl_dr_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                qltctrl_nurs_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                )
            </if>
        )y
    </select>

    <select id="queryPpsDeptDetailList" resultType="com.my.som.vo.common.PpsDeptMainDetailInfo">
        select
            x.*,
            IFNULL(CONCAT(ROUND(x.medicalTotalNum/NULLIF(y.medicalTotalNum,0)*100,2),'%'),0) AS medicalTotalNumRate,
            IFNULL(CONCAT(ROUND(x.sumfee/NULLIF(y.sumfee,0)*100,2),'%'),0) AS totalCostRate,
--             IFNULL(CONCAT(ROUND(x.totalBaseCost/NULLIF(y.totalBaseCost,0)*100,2),'%'),0) AS totalBaseCostRate,
            IFNULL(CONCAT(ROUND(x.totalDays/NULLIF(y.totalDays,0)*100,2),'%'),0) AS totalDaysRate
        from(
            select
                a.dscg_caty_codg_inhosp AS priOutHosDeptCode,
                a.dscg_caty_name_inhosp AS priOutHosDeptName,
                a.cd_codg AS ppsGroupCode,
                a.CD_NAME AS ppsGroupName,
                COUNT(1) AS medicalTotalNum,
                IFNULL(SUM(b.ipt_sumfee),0) AS sumfee,
--                 IFNULL(SUM(b.TOTAL_BASE_COST),0) AS totalBaseCost,
                ROUND(IFNULL(AVG(b.ipt_sumfee),0), 2) AS avgCost,
                IFNULL(SUM(b.act_ipt),0) AS totalDays,
                ROUND(IFNULL(AVG(b.act_ipt),0), 2) AS avgDays
            from som_grp_rcd a
            inner join som_drg_grp_info b on a.SETTLE_LIST_ID = b.SETTLE_LIST_ID
            where a.grp_stas = '1'
            <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
                AND  b.HOSPITAL_ID = #{queryParam.hospitalId}
            </if>
            <if test="queryParam.priOutHosDeptCode!=null and queryParam.priOutHosDeptCode!=''">
                AND a.dscg_caty_codg_inhosp  = #{queryParam.priOutHosDeptCode}
            </if>
            <if test="queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and
                queryParam.cy_end_date!=null and queryParam.cy_end_date!=''">
                AND a.dscg_time BETWEEN #{queryParam.cy_start_date} and CONCAT(#{queryParam.cy_end_date},' 23:59:59')
            </if>
            <if test="queryParam.queryPpsGroupCode!=null and queryParam.queryPpsGroupCode!=''">
                AND a.cd_codg = #{queryParam.queryPpsGroupCode}
            </if>
            <if test="queryParam.doctorIdList!=null">
                AND (
                b.deptdrt_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                b.chfdr_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                b.atddr_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                b.ipdr_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                b.resp_nurs_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                b.train_dr_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                b.intn_dr in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                b.codr_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                b.qltctrl_dr_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                b.qltctrl_nurs_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                )
            </if>
            GROUP BY a.dscg_caty_codg_inhosp,a.dscg_caty_name_inhosp
        ) x cross join
        (
            select
                COUNT(1) AS medicalTotalNum,
                IFNULL(SUM(b.ipt_sumfee),0) AS sumfee,
--                 IFNULL(SUM(b.TOTAL_BASE_COST),0) AS totalBaseCost,
                IFNULL(SUM(b.act_ipt),0) AS totalDays
            from som_grp_rcd a
            inner join som_drg_grp_info b on a.SETTLE_LIST_ID = b.SETTLE_LIST_ID
            where a.grp_stas = '1'
            <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
                AND  b.HOSPITAL_ID = #{queryParam.hospitalId}
            </if>
            <if test="queryParam.priOutHosDeptCode!=null and queryParam.priOutHosDeptCode!=''">
                AND a.dscg_caty_codg_inhosp  = #{queryParam.priOutHosDeptCode}
            </if>
            <if test="queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and
                    queryParam.cy_end_date!=null and queryParam.cy_end_date!=''">
                AND a.dscg_time BETWEEN #{queryParam.cy_start_date} and CONCAT(#{queryParam.cy_end_date},' 23:59:59')
            </if>
            <if test="queryParam.queryPpsGroupCode!=null and queryParam.queryPpsGroupCode!=''">
                AND a.cd_codg = #{queryParam.queryPpsGroupCode}
            </if>
            <if test="queryParam.doctorIdList!=null">
                AND (
                b.deptdrt_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                b.chfdr_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                b.atddr_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                b.ipdr_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                b.resp_nurs_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                b.train_dr_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                b.intn_dr in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                b.codr_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                b.qltctrl_dr_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                b.qltctrl_nurs_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                )
            </if>
        )y
    </select>

    <select id="getHospitalDrgByHospitalId" resultType="com.my.som.vo.dataHandle.dataGroup.HospitalDrgVo">
        select
            a.id as grperInfoId,
            a.grper_type as drgGroupType,
            a.grper_ver as drgGroupVersion,
            a.grper_url_dip as grperUrlDrg,
            a.hospital_id as hospitalId,
            b.medins_name as medinsName,
            b.hosp_lv as hospLv
        from som_grp_reqt_addr_info a inner join som_hosp_info b on a.hospital_id = b.hospital_id
        where 1=1
        <if test="queryParam.active_flag!=null and queryParam.active_flag!=''">
            AND a.active_flag = #{queryParam.active_flag}
        </if>
        <if test="queryParam.hospital_id!=null and queryParam.hospital_id!=''">
            AND a.hospital_id = #{queryParam.hospital_id}
        </if>
        <if test="queryParam.grper_type!=null and queryParam.grper_type!=''">
            AND a.grper_type = #{queryParam.grper_type,jdbcType=VARCHAR}
        </if>
    </select>

    <select id="getHospitalDipByHospitalId" resultType="com.my.som.vo.dataHandle.dataGroup.HospitalDipVo">
        select
            a.id as grperInfoId,
            a.grper_type as drgGroupType,
            a.grper_ver as drgGroupVersion,
            a.grper_url_dip as grperUrlDip,
            a.hospital_id as hospitalId,
            b.medins_name as medinsName,
            b.hosp_lv as hospLv
        from som_grp_reqt_addr_info a inner join som_hosp_info b on a.hospital_id = b.hospital_id
        where 1=1
        <if test="queryParam.active_flag!=null and queryParam.active_flag!=''">
            AND a.active_flag = #{queryParam.active_flag}
        </if>
        <if test="queryParam.hospital_id!=null and queryParam.hospital_id!=''">
            AND a.hospital_id = #{queryParam.hospital_id}
        </if>
    </select>

    <select id="queryTpdDrgsVo" resultType="com.my.som.vo.dataHandle.dataGroup.SomDrgNameVo">
        select
            DISTINCT
            a.id as id,
            a.grper_type as grperType,
            a.drg_codg as drgCodg,
            a.drg_name as drgName,
            a.mdc_codg as mdcCodg,
            b.mdc_name as mdcName,
            a.adrg_codg as adrgCodg,
            a.adrg_name as adrgName,
            a.begn_date as begnDate,
            a.expi_date as expiDate,
            a.active_flag as activeFlag
        from som_drg_name a left join som_mdc_cfg b on a.mdc_codg = b.mdc_codg
        where 1=1
        <if test="queryParam.id!=null and queryParam.id!=''">
            AND a.id = #{queryParam.id}
        </if>
        <if test="queryParam.grper_type!=null and queryParam.grper_type!=''">
            AND a.grper_type = #{queryParam.grper_type}
        </if>
        <if test="queryParam.drg_codg!=null and queryParam.drg_codg!=''">
            AND a.drg_codg = #{queryParam.drg_codg}
        </if>
        <if test="queryParam.drg_name!=null and queryParam.drg_name!=''">
            AND a.drg_name = #{queryParam.drg_name}
        </if>
        <if test="queryParam.mdc_codg!=null and queryParam.mdc_codg!=''">
            AND a.mdc_codg = #{queryParam.mdc_codg}
        </if>
        <if test="queryParam.mdc_name!=null and queryParam.mdc_name!=''">
            AND a.mdc_name = #{queryParam.mdc_name}
        </if>
        <if test="queryParam.adrg_codg!=null and queryParam.adrg_codg!=''">
            AND a.adrg_codg = #{queryParam.adrg_codg}
        </if>
        <if test="queryParam.adrg_name!=null and queryParam.adrg_name!=''">
            AND a.adrg_name = #{queryParam.adrg_name}
        </if>
        <if test="queryParam.active_flag!=null and queryParam.active_flag!=''">
            AND a.active_flag = #{queryParam.active_flag}
        </if>
        <if test="queryParam.insuplcAdmdvs!=null and queryParam.insuplcAdmdvs!=''">
            AND a.insuplc_admdvs = #{queryParam.insuplcAdmdvs}
        </if>
    </select>

    <delete id="removeRepeatStsValidateErrorData">
        DELETE FROM som_setl_invy_chk_err_rcd
        WHERE SETTLE_LIST_ID IN (
            SELECT a.ID FROM (
                                 SELECT ID FROM som_hi_invy_bas_info
                                 WHERE K00 IN (SELECT K00 FROM som_hi_invy_bas_info WHERE ACTIVE_FLAG='1' GROUP BY K00 HAVING COUNT(1) > 1 )
                                   AND ID NOT IN (SELECT MAX(ID) FROM som_hi_invy_bas_info WHERE ACTIVE_FLAG='1' GROUP BY K00 HAVING COUNT(1) > 1 )
                                   AND ACTIVE_FLAG='1'
                             )a
        )
    </delete>

    <delete id="removeRepeatStsValidateLoseScoreData">
        DELETE FROM som_setl_invy_qlt_dedu_point_detl
        WHERE SETTLE_LIST_ID IN (
            SELECT a.ID FROM (
                                 SELECT ID FROM som_hi_invy_bas_info
                                 WHERE K00 IN (SELECT K00 FROM som_hi_invy_bas_info WHERE ACTIVE_FLAG='1' GROUP BY K00 HAVING COUNT(1) > 1 )
                                   AND ID NOT IN (SELECT MAX(ID) FROM som_hi_invy_bas_info WHERE ACTIVE_FLAG='1' GROUP BY K00 HAVING COUNT(1) > 1 )
                                   AND ACTIVE_FLAG='1'
                             )a
        )
    </delete>

    <delete id="removeRepeatStsCodeResourceConsumptionData">
        DELETE FROM som_codg_resu_adjm_rcd
        WHERE SETTLE_LIST_ID IN (
            SELECT a.ID FROM (
                                 SELECT ID FROM som_hi_invy_bas_info
                                 WHERE K00 IN (SELECT K00 FROM som_hi_invy_bas_info WHERE ACTIVE_FLAG='1' GROUP BY K00 HAVING COUNT(1) > 1 )
                                   AND ID NOT IN (SELECT MAX(ID) FROM som_hi_invy_bas_info WHERE ACTIVE_FLAG='1' GROUP BY K00 HAVING COUNT(1) > 1 )
                                   AND ACTIVE_FLAG='1'
                             )a
        )
    </delete>

    <delete id="removeRepeatStsDrgGroupLogData">
        DELETE FROM som_drg_grper_intf_trns_log
        WHERE SETTLE_LIST_ID IN (
            SELECT a.ID FROM (
                                 SELECT ID FROM som_hi_invy_bas_info
                                 WHERE K00 IN (SELECT K00 FROM som_hi_invy_bas_info WHERE ACTIVE_FLAG='1' GROUP BY K00 HAVING COUNT(1) > 1 )
                                   AND ID NOT IN (SELECT MAX(ID) FROM som_hi_invy_bas_info WHERE ACTIVE_FLAG='1' GROUP BY K00 HAVING COUNT(1) > 1 )
                                   AND ACTIVE_FLAG='1'
                             )a
        )
    </delete>

    <delete id="removeRepeatStsDrgGroupRecordData">
        DELETE FROM som_drg_grp_rcd
        WHERE SETTLE_LIST_ID IN (
            SELECT a.ID FROM (
                                 SELECT ID FROM som_hi_invy_bas_info
                                 WHERE K00 IN (SELECT K00 FROM som_hi_invy_bas_info WHERE ACTIVE_FLAG='1' GROUP BY K00 HAVING COUNT(1) > 1 )
                                   AND ID NOT IN (SELECT MAX(ID) FROM som_hi_invy_bas_info WHERE ACTIVE_FLAG='1' GROUP BY K00 HAVING COUNT(1) > 1 )
                                   AND ACTIVE_FLAG='1'
                             )a
        )
    </delete>

    <delete id="removeRepeatStsDipGroupRecordData">
        DELETE FROM som_dip_grp_rcd
        WHERE SETTLE_LIST_ID IN (
            SELECT a.ID FROM (
                                 SELECT ID FROM som_hi_invy_bas_info
                                 WHERE K00 IN (SELECT K00 FROM som_hi_invy_bas_info WHERE ACTIVE_FLAG='1' GROUP BY K00 HAVING COUNT(1) > 1 )
                                   AND ID NOT IN (SELECT MAX(ID) FROM som_hi_invy_bas_info WHERE ACTIVE_FLAG='1' GROUP BY K00 HAVING COUNT(1) > 1 )
                                   AND ACTIVE_FLAG='1'
                             )a
        )
    </delete>

    <delete id="removeRepeatStsDrgGroupRecordAddedData">
        DELETE FROM som_drg_grp_exe_rcd
        WHERE SETTLE_LIST_ID IN (
            SELECT a.ID FROM (
                                 SELECT ID FROM som_hi_invy_bas_info
                                 WHERE K00 IN (SELECT K00 FROM som_hi_invy_bas_info WHERE ACTIVE_FLAG='1' GROUP BY K00 HAVING COUNT(1) > 1 )
                                   AND ID NOT IN (SELECT MAX(ID) FROM som_hi_invy_bas_info WHERE ACTIVE_FLAG='1' GROUP BY K00 HAVING COUNT(1) > 1 )
                                   AND ACTIVE_FLAG='1'
                             )a
        )
    </delete>

    <delete id="removeRepeatBusKeyData">
       DELETE FROM som_drg_grp_info
       WHERE SETTLE_LIST_ID IN (
           SELECT a.ID FROM (
                                SELECT ID FROM som_hi_invy_bas_info
                                WHERE K00 IN (SELECT K00 FROM som_hi_invy_bas_info WHERE ACTIVE_FLAG='1' GROUP BY K00 HAVING COUNT(1) > 1 )
                                  AND ID NOT IN (SELECT MAX(ID) FROM som_hi_invy_bas_info WHERE ACTIVE_FLAG='1' GROUP BY K00 HAVING COUNT(1) > 1 )
                                  AND ACTIVE_FLAG='1'
                            )a
       )
    </delete>

    <delete id="removeRepeatBusDiseaseDiagnosisData">
       DELETE FROM som_diag
       WHERE SETTLE_LIST_ID IN (
           SELECT a.ID FROM (
                                SELECT ID FROM som_hi_invy_bas_info
                                WHERE K00 IN (SELECT K00 FROM som_hi_invy_bas_info WHERE ACTIVE_FLAG='1' GROUP BY K00 HAVING COUNT(1) > 1 )
                                  AND ID NOT IN (SELECT MAX(ID) FROM som_hi_invy_bas_info WHERE ACTIVE_FLAG='1' GROUP BY K00 HAVING COUNT(1) > 1 )
                                  AND ACTIVE_FLAG='1'
                            )a
       )
    </delete>

    <delete id="removeRepeatBusOperateDiagnosisData">
       DELETE FROM som_oprn_oprt_info
       WHERE SETTLE_LIST_ID IN (
           SELECT a.ID FROM (
                                SELECT ID FROM som_hi_invy_bas_info
                                WHERE K00 IN (SELECT K00 FROM som_hi_invy_bas_info WHERE ACTIVE_FLAG='1' GROUP BY K00 HAVING COUNT(1) > 1 )
                                  AND ID NOT IN (SELECT MAX(ID) FROM som_hi_invy_bas_info WHERE ACTIVE_FLAG='1' GROUP BY K00 HAVING COUNT(1) > 1 )
                                  AND ACTIVE_FLAG='1'
                            )a
       )
    </delete>

    <delete id="removeRepeatBusOutClicData">
       DELETE FROM som_otp_slow_special_trt_info
       WHERE hi_setl_invy_id IN (
           SELECT a.ID FROM (
                                SELECT ID FROM som_hi_invy_bas_info
                                WHERE K00 IN (SELECT K00 FROM som_hi_invy_bas_info WHERE ACTIVE_FLAG='1' GROUP BY K00 HAVING COUNT(1) > 1 )
                                  AND ID NOT IN (SELECT MAX(ID) FROM som_hi_invy_bas_info WHERE ACTIVE_FLAG='1' GROUP BY K00 HAVING COUNT(1) > 1 )
                                  AND ACTIVE_FLAG='1'
                            )a
       )
    </delete>

    <delete id="removeRepeatBusIcuData">
       DELETE FROM som_setl_invy_scs_cutd_info
       WHERE hi_setl_invy_id IN (
           SELECT a.ID FROM (
                                SELECT ID FROM som_hi_invy_bas_info
                                WHERE K00 IN (SELECT K00 FROM som_hi_invy_bas_info WHERE ACTIVE_FLAG='1' GROUP BY K00 HAVING COUNT(1) > 1 )
                                  AND ID NOT IN (SELECT MAX(ID) FROM som_hi_invy_bas_info WHERE ACTIVE_FLAG='1' GROUP BY K00 HAVING COUNT(1) > 1 )
                                  AND ACTIVE_FLAG='1'
                            )a
       )
    </delete>

    <delete id="removeRepeatBusMedicalCostData">
       DELETE FROM som_hi_setl_invy_med_fee_info
       WHERE hi_setl_invy_id IN (
           SELECT a.ID FROM (
                                SELECT ID FROM som_hi_invy_bas_info
                                WHERE K00 IN (SELECT K00 FROM som_hi_invy_bas_info WHERE ACTIVE_FLAG='1' GROUP BY K00 HAVING COUNT(1) > 1 )
                                  AND ID NOT IN (SELECT MAX(ID) FROM som_hi_invy_bas_info WHERE ACTIVE_FLAG='1' GROUP BY K00 HAVING COUNT(1) > 1 )
                                  AND ACTIVE_FLAG='1'
                            )a
       )
    </delete>

    <delete id="removeRepeatBusFundPayData">
       DELETE FROM som_fund_pay
       WHERE hi_setl_invy_id IN (
           SELECT a.ID FROM (
                                SELECT ID FROM som_hi_invy_bas_info
                                WHERE K00 IN (SELECT K00 FROM som_hi_invy_bas_info WHERE ACTIVE_FLAG='1' GROUP BY K00 HAVING COUNT(1) > 1 )
                                  AND ID NOT IN (SELECT MAX(ID) FROM som_hi_invy_bas_info WHERE ACTIVE_FLAG='1' GROUP BY K00 HAVING COUNT(1) > 1 )
                                  AND ACTIVE_FLAG='1'
                            )a
       )
    </delete>

    <delete id="removeRepeatBusSettleListData">
        DELETE FROM  som_hi_invy_bas_info
        WHERE ID IN (
            SELECT a.ID FROM (
                                 SELECT ID FROM som_hi_invy_bas_info
                                 WHERE K00 IN (SELECT K00 FROM som_hi_invy_bas_info WHERE ACTIVE_FLAG='1' GROUP BY K00 HAVING COUNT(1) > 1 )
                                   AND ID NOT IN (SELECT MAX(ID) FROM som_hi_invy_bas_info WHERE ACTIVE_FLAG='1' GROUP BY K00 HAVING COUNT(1) > 1 )
                                   AND ACTIVE_FLAG='1'
                             )a
        )
    </delete>

    <!-- 去除DIP中间表 -->
    <delete id="removeRepeatBusKeyDIPData">
        DELETE FROM som_dip_grp_info
        WHERE SETTLE_LIST_ID IN (
            SELECT a.ID FROM (
                                 SELECT ID FROM som_hi_invy_bas_info
                                 WHERE K00 IN (SELECT K00 FROM som_hi_invy_bas_info WHERE ACTIVE_FLAG='1' GROUP BY K00 HAVING COUNT(1) > 1 )
                                   AND ID NOT IN (SELECT MAX(ID) FROM som_hi_invy_bas_info WHERE ACTIVE_FLAG='1' GROUP BY K00 HAVING COUNT(1) > 1 )
                                   AND ACTIVE_FLAG='1'
                             )a
        )
    </delete>

    <!-- 去除CD中间表 -->
    <delete id="removeRepeatBusKeyCDData">
        DELETE FROM som_cd_grp_info
        WHERE SETTLE_LIST_ID IN (
            SELECT a.ID FROM (
                                 SELECT ID FROM som_hi_invy_bas_info
                                 WHERE K00 IN (SELECT K00 FROM som_hi_invy_bas_info WHERE ACTIVE_FLAG='1' GROUP BY K00 HAVING COUNT(1) > 1 )
                                   AND ID NOT IN (SELECT MAX(ID) FROM som_hi_invy_bas_info WHERE ACTIVE_FLAG='1' GROUP BY K00 HAVING COUNT(1) > 1 )
                                   AND ACTIVE_FLAG='1'
                             )a
        )
    </delete>

    <!-- 去重dip_log -->
    <delete id="removeRepeatStsDipGroupLogData">
        DELETE FROM som_dip_grper_intf_trns_log
        WHERE SETTLE_LIST_ID IN (
            SELECT a.ID FROM (
                                 SELECT ID FROM som_hi_invy_bas_info
                                 WHERE K00 IN (SELECT K00 FROM som_hi_invy_bas_info WHERE ACTIVE_FLAG='1' GROUP BY K00 HAVING COUNT(1) > 1 )
                                   AND ID NOT IN (SELECT MAX(ID) FROM som_hi_invy_bas_info WHERE ACTIVE_FLAG='1' GROUP BY K00 HAVING COUNT(1) > 1 )
                                   AND ACTIVE_FLAG='1'
                             )a
        )
    </delete>


    <sql id="repeatIds">
        <foreach collection="ids" item="id" open="in (" close=")" separator=",">
            #{id,jdbcType=VARCHAR}
        </foreach>
    </sql>

    <!-- 去重 -->
    <delete id="repeat">
        delete from ${tabName}
        where
        <!-- settle_list_id -->
        <if test='tabName == "som_dip_grp_info" or
                  tabName == "som_dip_grp_rcd" or
                  tabName == "som_dip_grper_intf_trns_log" or
                  tabName == "som_drg_grp_info" or
                  tabName == "som_drg_grp_rcd" or
                  tabName == "som_drg_grper_intf_trns_log" or
                  tabName == "som_cd_grp_info" or
                  tabName == "som_grp_rcd" or
                  tabName == "som_cd_grper_intf_trns_log" or
                  tabName == "som_codg_resu_adjm_rcd" or
                  tabName == "som_setl_invy_chk_err_rcd" or
                  tabName == "som_setl_invy_qlt_dedu_point_detl" or
                  tabName == "som_diag" or
                  tabName == "som_oprn_oprt_info" or
                  tabName == "som_dip_sco" or
                  tabName == "som_drg_sco" or
                  tabName == "som_setl_invy_chk" or
                  tabName == "som_invy_chk_detl" or
                  tabName == "som_init_hi_setl_invy_med_fee_info" or
                  tabName == "som_setl_invy_bld_info" or
                  tabName == "som_in_group_rcd" or
                  tabName == "som_can_opt_medcas_info"
                '>
            settle_list_id
        </if>

        <!-- hi_setl_invy_id -->
        <if test='tabName == "som_otp_slow_special_trt_info" or
                  tabName == "som_setl_invy_scs_cutd_info" or
                  tabName == "som_hi_setl_invy_med_fee_info" or
                  tabName == "som_fund_pay"
                 '>
            hi_setl_invy_id
        </if>

        <!-- id -->
        <if test='tabName == "som_hi_invy_bas_info"'>
            id
        </if>

        <!-- medcas_hmpg_id -->
        <if test='tabName == "som_init_diag_info" or
                  tabName == "som_medcas_hmpg_oprn_info"
                 '>
            medcas_hmpg_id
        </if>

        <choose>
            <when test='type == "1"'>
                <include refid="repeatIds"/>
            </when>
            <when test='type == "2"'>
                <trim prefix="IN (" suffix=")">
                    SELECT id from(
                        SELECT id
                        FROM som_init_hi_setl_invy_med_fee_info
                        WHERE settle_list_id <include refid="repeatIds"/>
                    ) a
                </trim>
            </when>
        </choose>

    </delete>

    <!-- 上传去重 -->
    <delete id="uploadRepeat">
        delete from ${tabName}
        where
        <!-- settle_list_id -->
        <if test='tabName == "som_dip_grp_info" or
                  tabName == "som_dip_grp_rcd" or
                  tabName == "som_dip_grper_intf_trns_log" or
                  tabName == "som_drg_grp_info" or
                  tabName == "som_drg_grp_rcd" or
                  tabName == "som_drg_grper_intf_trns_log" or
                  tabName == "som_cd_grp_info" or
                  tabName == "som_grp_rcd" or
                  tabName == "som_cd_grper_intf_trns_log" or
                  tabName == "som_codg_resu_adjm_rcd" or
                  tabName == "som_setl_invy_chk_err_rcd" or
                  tabName == "som_setl_invy_qlt_dedu_point_detl" or
                  tabName == "som_diag" or
                  tabName == "som_oprn_oprt_info"
                '>
            settle_list_id
        </if>

        <!-- hi_setl_invy_id -->
        <if test='tabName == "som_otp_slow_special_trt_info" or
                  tabName == "som_setl_invy_scs_cutd_info" or
                  tabName == "som_hi_setl_invy_med_fee_info" or
                  tabName == "som_fund_pay"
                 '>
            hi_setl_invy_id
        </if>

        <!-- id -->
        <if test='tabName == "som_hi_invy_bas_info"'>
            id
        </if>
        IN
        <foreach collection="ids" open="(" separator="," close=")" item="id">
            #{id,jdbcType=VARCHAR}
        </foreach>
    </delete>

    <select id="queryMedicalDetailForBedDayCostList" resultType="com.my.som.vo.common.MedicalMainDetailForBedDayCostInfo">
        select
            a.SETTLE_LIST_ID AS hiSetlInvyId,
            a.PATIENT_ID AS patientId,
            a.NAME AS name,
            a.adm_time AS inHosTime,
            a.dscg_time AS outHosTime,
            b.drg_codg AS drgsCode,
            b.DRG_NAME AS drgsName,
            IFNULL(b.grp_fale_rea,b.grper_oupt_log) as grperOuptLog,
            a.dscg_caty_name_inhosp AS priOutHosDeptName,
            e.DRG_STANDARD_DAYS as drgStandardDays,
            a.act_ipt AS inHosDays,
            e.DRG_STANDARD_BED_COST as drgStandardBedCost,
            ROUND(IFNULL(a.ipt_sumfee/NULLIF(a.act_ipt,0),0), 2) AS bedDayCost
        from som_drg_grp_info a
        inner join som_drg_grp_exe_rcd b ON a.SETTLE_LIST_ID = b.SETTLE_LIST_ID and b.ACTIVE_FLAG = #{queryParam.activeFlag}
        LEFT JOIN tpd_zs_bed_drg_benchmark e ON b.drg_codg = e.drg_codg AND e.hosp_lv = #{queryParam.hospLv} AND SUBSTR(a.dscg_time,1,4) = e.year
        where 1=1
        <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
            AND  a.HOSPITAL_ID = #{queryParam.hospitalId}
        </if>
        <if test="queryParam.priOutHosDeptCode!=null and queryParam.priOutHosDeptCode!=''">
            AND a.dscg_caty_codg_inhosp = #{queryParam.priOutHosDeptCode}
        </if>
        <if test="queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and
                queryParam.cy_end_date!=null and queryParam.cy_end_date!=''">
            AND a.dscg_time BETWEEN #{queryParam.cy_start_date} and CONCAT(#{queryParam.cy_end_date},' 23:59:59')
        </if>
        <if test="queryParam.queryDrgsCode!=null and queryParam.queryDrgsCode!=''">
            AND b.drg_codg = #{queryParam.queryDrgsCode}
        </if>
        <if test="queryParam.beddayPayType!=null and queryParam.beddayPayType!=''">
            AND b.bedday_pay_type = #{queryParam.beddayPayType}
        </if>
        order by a.NAME,a.adm_time asc
    </select>

    <select id="getMedicalCostItem" resultType="java.lang.String">
        select
        ifnull(t2.labl_name,'其他医疗费') AS medChrgItemname
        from som_hi_setl_invy_med_fee_info t1
        left join (SELECT data_val ,labl_name from  som_dic_code where code_type = 'MED_CHRGITM' ) t2 ON
        RIGHT('00' + t1.med_chrg_itemname, 2) = t2.data_val
        where hi_setl_invy_id in (
            select
              SETTLE_LIST_ID
            from som_drg_grp_info
            <if test='queryParam.enableSeAns != null and queryParam.enableSeAns != "" and queryParam.enableSeAns == "1"'>
                INNER JOIN som_hi_invy_bas_info a on a.id=som_drg_grp_info.SETTLE_LIST_ID
                INNER JOIN som_setl_cas_crsp p on a.k00=p.k00
            </if>
            where 1=1
            <if test="queryParam.tb16cList!=null and queryParam.tb16cList.size()>0">
                AND dscg_caty_codg_inhosp IN
                <foreach collection="queryParam.tb16cList" item="tb16c" open="(" separator="," close=")">
                    #{tb16c}
                </foreach>
            </if>
            <if test="queryParam.drgCodgList!=null and queryParam.drgCodgList.size()>0">
                AND `drg_codg` IN
                <foreach collection="queryParam.drgCodgList" item="drgCodg" open="(" separator="," close=")">
                    #{drgCodg}
                </foreach>
            </if>

            <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
                AND  HOSPITAL_ID = #{queryParam.hospitalId}
            </if>
            <if test="queryParam.b16c!=null and queryParam.b16c!=''">
                AND dscg_caty_codg_inhosp  =#{queryParam.b16c}
            </if>
            <if test="queryParam.queryDrg!=null and queryParam.queryDrg!=''">
                AND `drg_codg` = #{queryParam.queryDrg}
            </if>
            <if test="queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and
            queryParam.cy_end_date!=null and queryParam.cy_end_date!=''">
                AND dscg_time BETWEEN #{queryParam.cy_start_date} and CONCAT(#{queryParam.cy_end_date},' 23:59:59')
            </if>
            <if test="queryParam.seStartTime!=null and queryParam.seStartTime!='' and
                queryParam.seEndTime!=null and queryParam.seEndTime!=''">
                AND setl_end_time BETWEEN #{queryParam.seStartTime} and CONCAT(#{queryParam.seEndTime},' 23:59:59')
            </if>
            <if test="queryParam.inStartTime!=null and queryParam.inStartTime!='' and
                            queryParam.inEndTime!=null and queryParam.inEndTime!=''">
                AND adm_time BETWEEN #{queryParam.inStartTime} and CONCAT(#{queryParam.inEndTime},' 23:59:59')
            </if>
            <if test="queryParam.doctorIdList!=null">
                AND (
                deptdrt_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                chfdr_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                atddr_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                ipdr_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                resp_nurs_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                train_dr_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                intn_dr in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                codr_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                qltctrl_dr_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                qltctrl_nurs_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                )
            </if>
        )
        and t1.med_chrg_itemname!=''
        group by t2.labl_name order by t2.labl_name asc
    </select>

    <select id="getFundPayItem" resultType="java.lang.String">
        select
        ifnull(t2.labl_name,'未传基金') as FUND_PAY_TYPE
        from som_fund_pay t1
        left join (SELECT data_val ,labl_name from  som_dic_code where code_type = 'FUND_PAY_TYPE' ) t2
        on t1.FUND_PAY_TYPE =  t2.data_val
        where hi_setl_invy_id in (
            select
              SETTLE_LIST_ID
            from som_drg_grp_info
            <if test='queryParam.enableSeAns != null and queryParam.enableSeAns != "" and queryParam.enableSeAns == "1"'>
                INNER JOIN som_hi_invy_bas_info a on a.id=som_drg_grp_info.SETTLE_LIST_ID
                INNER JOIN som_setl_cas_crsp p on a.k00=p.k00
            </if>
            where 1=1
            <if test="queryParam.tb16cList!=null and queryParam.tb16cList.size()>0">
                AND dscg_caty_codg_inhosp IN
                <foreach collection="queryParam.tb16cList" item="tb16c" open="(" separator="," close=")">
                    #{tb16c}
                </foreach>
            </if>
            <if test="queryParam.drgCodgList!=null and queryParam.drgCodgList.size()>0">
                AND `drg_codg` IN
                <foreach collection="queryParam.drgCodgList" item="drgCodg" open="(" separator="," close=")">
                    #{drgCodg}
                </foreach>
            </if>

            <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
                AND  HOSPITAL_ID = #{queryParam.hospitalId}
            </if>
            <if test="queryParam.b16c!=null and queryParam.b16c!=''">
                AND dscg_caty_codg_inhosp  =#{queryParam.b16c}
            </if>
            <if test="queryParam.queryDrg!=null and queryParam.queryDrg!=''">
                AND `drg_codg` = #{queryParam.queryDrg}
            </if>
            <if test="queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and
                queryParam.cy_end_date!=null and queryParam.cy_end_date!=''">
                AND dscg_time BETWEEN #{queryParam.cy_start_date} and CONCAT(#{queryParam.cy_end_date},' 23:59:59')
            </if>
            <if test="queryParam.seStartTime!=null and queryParam.seStartTime!='' and
                    queryParam.seEndTime!=null and queryParam.seEndTime!=''">
                AND setl_end_time BETWEEN #{queryParam.seStartTime} and CONCAT(#{queryParam.seEndTime},' 23:59:59')
            </if>
            <if test="queryParam.inStartTime!=null and queryParam.inStartTime!='' and
                        queryParam.inEndTime!=null and queryParam.inEndTime!=''">
                AND adm_time BETWEEN #{queryParam.inStartTime} and CONCAT(#{queryParam.inEndTime},' 23:59:59')
            </if>
            <if test="queryParam.doctorIdList!=null">
                AND (
                deptdrt_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                chfdr_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                atddr_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                ipdr_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                resp_nurs_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                train_dr_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                intn_dr in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                codr_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                qltctrl_dr_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                qltctrl_nurs_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                )
            </if>
        )
        and t1.FUND_PAY_TYPE!=''
        group by  t2.labl_name order by  t2.labl_name asc
    </select>

    <!--批量新增-->
    <insert id="batchInsertBusDept">
        INSERT INTO som_dept (CODE, NAME, dept_lv,
        prnt_dept_codg, TYPE, std_dept_codg,
        dept_area, is_oprn_dept, HOSPITAL_ID,
        ACTIVE_FLAG) VALUES
        <foreach collection="lbdl" item="item" index="index" separator=",">
            (#{item.code,jdbcType=VARCHAR}, #{item.name,jdbcType=VARCHAR}, #{item.dept_lv,jdbcType=INTEGER},
            #{item.prntDeptCodg,jdbcType=VARCHAR}, #{item.type,jdbcType=INTEGER}, #{item.stdDeptCodg,jdbcType=VARCHAR},
            #{item.dept_area,jdbcType=DECIMAL}, #{item.isOprnDept,jdbcType=VARCHAR}, #{item.hospitalId,jdbcType=VARCHAR},
            #{item.activeFlag,jdbcType=VARCHAR})
        </foreach>
    </insert>

    <!--批量新增-->
    <insert id="batchInsertBusMedicalWorkers">
        insert into som_medstff_info (CODE, NAME, citi_idet_no,
        brdy, gend, profttl,
        TYPE)
        values
        <foreach collection="lbmwl" item="item" index="index" separator=",">
            (#{item.code,jdbcType=VARCHAR}, #{item.name,jdbcType=VARCHAR}, #{item.citiIdetNo,jdbcType=VARCHAR},
            #{item.brdy,jdbcType=VARCHAR}, #{item.gend,jdbcType=VARCHAR}, #{item.profttl,jdbcType=VARCHAR},
            #{item.type,jdbcType=VARCHAR})
        </foreach>
    </insert>

    <!--批量新增-->
    <insert id="batchInsertBusDeptWorkersRelationship">
        insert into som_dept_psn_rlts (DEPT_CODE, medstff_codg)
        values
        <foreach collection="lbdwrl" item="item" index="index" separator=",">
            (#{item.deptCode,jdbcType=VARCHAR}, #{item.medstffCodg,jdbcType=VARCHAR})
        </foreach>
    </insert>


    <resultMap id="StsDataHandleLogBaseResultMap" type="com.my.som.model.dataHandle.SomDataprosLog">
        <id column="ID" jdbcType="BIGINT" property="id" />
        <result column="medcas_val" jdbcType="INTEGER" property="medcasVal" />
        <result column="integrity_chk_pass_val" jdbcType="INTEGER" property="integrityChkPassVal" />
        <result column="logic_chk_pass_val" jdbcType="INTEGER" property="logicChkPassVal" />
        <result column="data_clean_pass_val" jdbcType="INTEGER" property="dataCleanPassVal" />
        <result column="drg_in_group_medcas_val" jdbcType="INTEGER" property="drgInGroupMedcasVal" />
        <result column="bus_key_tab_selc_medcas_val" jdbcType="INTEGER" property="busKeyTabSelcMedcasVal" />
        <result column="cd_in_group_medcas_val" jdbcType="INTEGER" property="cdInGroupMedcasVal" />
        <result column="data_upld_time" jdbcType="TIMESTAMP" property="dataUpldTime" />
        <result column="data_dscg_time_scp" jdbcType="VARCHAR" property="dataDscgTimeScp" />
        <result column="datapros_dura" jdbcType="DECIMAL" property="dataprosDura" />
        <result column="prcs_prgs" jdbcType="INTEGER" property="prcs_prgs" />
        <result column="oprt_psn" jdbcType="VARCHAR" property="oprt_psn" />
        <result column="HOSPITAL_ID" jdbcType="VARCHAR" property="hospitalId" />
        <result column="ACTIVE_FLAG" jdbcType="VARCHAR" property="activeFlag" />
    </resultMap>
    <resultMap extends="StsDataHandleLogBaseResultMap" id="ResultMapWithBLOBs" type="com.my.som.model.dataHandle.SomDataprosLog">
        <result column="RESULT" jdbcType="LONGVARCHAR" property="result" />
    </resultMap>

    <sql id="StsDataHandleLogBase_Column_List">
    ID, medcas_val, integrity_chk_pass_val, logic_chk_pass_val, data_clean_pass_val,
    drg_in_group_medcas_val, bus_key_tab_selc_medcas_val, cd_in_group_medcas_val, data_upld_time, data_dscg_time_scp, datapros_dura,
    prcs_prgs, oprt_psn, HOSPITAL_ID, ACTIVE_FLAG
  </sql>
    <sql id="StsDataHandleLogBlob_Column_List">
    RESULT
  </sql>
    <select id="getMinIdByStsDataHandleLogForInterface" resultMap="ResultMapWithBLOBs">
        select
        <if test="distinct">
            distinct
        </if>
        <include refid="StsDataHandleLogBase_Column_List" />
        ,
        <include refid="StsDataHandleLogBlob_Column_List" />
        from som_datapros_log
        where result='waited' AND prcs_prgs = 0
        AND ACTIVE_FLAG ='1'
        ORDER BY ID
        limit 1
    </select>

    <select id="queryDataIsuue" resultType="java.lang.String">
        select
            SUBSTR(max(B15),1,7) as  maxTime
        from som_hi_invy_bas_info where B15 is not null
    </select>

    <select id="getMainIcd4StandardCost" parameterType="java.lang.String" resultType="java.lang.String">
        select
            ROUND(avg(convert(AES_DECRYPT(UNHEX(standard_fee),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) using utf8)),4) as standardFee
        from som_code_stand_fee
        WHERE  SUBSTR(icd_codg ,1,5)=#{mc} and ICD_TYPE='ICD-10'
    </select>

    <select id="hasRepeatData" resultType="java.lang.String">
            SELECT count(1) as cnt FROM som_hi_invy_bas_info
            WHERE K00 IN (SELECT K00 FROM som_hi_invy_bas_info WHERE ACTIVE_FLAG='1' GROUP BY K00 HAVING COUNT(1) > 1 )
              AND ID NOT IN (SELECT MAX(ID) FROM som_hi_invy_bas_info WHERE ACTIVE_FLAG='1' GROUP BY K00 HAVING COUNT(1) > 1 )
              AND ACTIVE_FLAG='1'
    </select>

    <select id="hasDataLogIdIsNullData" resultType="java.lang.String">
            SELECT count(1) as cnt FROM som_hi_invy_bas_info
            WHERE DATA_LOG_ID is null
              AND ACTIVE_FLAG='1'
        <if test="k00 != null and k00 !='' ">
            AND k00 =#{k00}
        </if>
    </select>



    <!-- 获取所有码表类型 -->
    <select id="queryAllDictType" resultType="java.util.Map">
        select code_type
        from som_sys_code
        WHERE is_del = 0
        group by code_type
    </select>

    <update id="updateDataLogIdIsNull" parameterType="java.lang.Long">
        UPDATE som_hi_invy_bas_info SET DATA_LOG_ID = #{id}
        <where>
            <if test="ids != null and ids.size() > 0">
                <foreach collection="ids" item="settleListId" open="ID IN (" separator="," close=")">
                    #{settleListId}
                </foreach>
            </if>
            AND ACTIVE_FLAG='1'
        </where>
    </update>

    <select id="selectIdByDataLogId" resultType="java.lang.String">
        SELECT ID FROM som_hi_invy_bas_info WHERE DATA_LOG_ID IS NULL
    </select>

    <select id="selectK00ByDataLogId" resultType="java.lang.String">
        SELECT K00 FROM som_hi_invy_bas_info WHERE DATA_LOG_ID IS NULL
    </select>
    <!--  根据数据批次号查询对应结算清单id  -->
    <select id="selectSettleIdByDataLogId" resultType="java.lang.String">
        SELECT ID FROM som_hi_invy_bas_info WHERE DATA_LOG_ID = #{data_log_id}
    </select>

    <!--  通用科室查询条件  -->
    <sql id="commonDeptCondition">
        (
            with RECURSIVE temp AS (
                select * from som_dept r where code = #{b16c,jdbcType=VARCHAR}
                UNION ALL
                SELECT b.* from som_dept b, temp t where b.prnt_dept_codg = t.code
            )
            select code from temp
        )
    </sql>

    <select id="queryDiagnosis" resultType="com.my.som.vo.common.DipGroupSuggestInfo">
        SELECT DISTINCT a.*
        FROM
             (
                 SELECT DISTINCT C03C AS diagCode,
                                 C04N AS diagName
                 FROM som_hi_invy_bas_info
             ) a
        WHERE a.diagCode IS NOT NULL
          AND a.diagCode != ''
          AND
          (
            <if test="likeQueryString != null and likeQueryString != ''">
              a.diagCode LIKE CONCAT("%",#{likeQueryString},"%")
           OR a.diagName LIKE CONCAT("%",#{likeQueryString},"%")
            </if>
          )
        ORDER BY a.diagCode,
                 a.diagName
    </select>

    <select id="queryDIPGroup" resultType="com.my.som.vo.common.SeletInputInfo">
        SELECT DISTINCT A.*
        FROM
             (
                 SELECT DISTINCT a.dip_codg as value,
                        a.DIP_NAME as label
                 FROM som_dip_grp_rcd a
                 WHERE 1=1
                    <if test="dipCodg != null and dipCodg != ''">
                        AND a.dip_codg LIKE CONCAT("%",#{dipCodg},"%")
                    </if>
             )A
        WHERE A.value !=''
        <if test="dipCodg != null and dipCodg != ''">
            AND A.value LIKE CONCAT("%",#{dipCodg},"%")
        </if>
        ORDER BY A.value,A.label
        limit 0,100
    </select>

    <select id="dataIsNotSplit" resultType="com.my.som.vo.task.HandlerTask">
        SELECT a.id as pageId,
               b.id as settleId,
               <choose>
                   <when test="useVisit == 1">
                       a.mdtrt_id AS uniqueid,
                   </when>
                   <when test="useVisit == 0">
                       a.unique_id AS uniqueid,
                   </when>
               </choose>
               a.extract_flag AS firstPageExtract,
               b.extract_flag AS settleListExtract
        FROM som_medcas_intf_bas_info a LEFT JOIN som_setl_intf_bas_info b
        ON a.mdtrt_id = b.mdtrt_id
        WHERE (a.extract_flag = '0' OR b.extract_flag = '0')
        <if test="pageIds != null and pageIds.size()>0">
            AND a.id in <include refid="com.my.som.dao.dataHandle.BusSettleListDao.selectInterfaceDataDi02Ids" />
        </if>
    </select>

    <select id="unExtractedData" resultType="com.my.som.vo.task.HandlerTask">
        SELECT
            a.id,
            a.unique_id as uniqueId,
            a.extract_flag AS settleListExtract
        FROM som_setl_invy_bas_info a
        where a.extract_flag = '0'
        <if test="pageIds != null and pageIds.size()>0">
            AND a.id in <include refid="com.my.som.dao.dataHandle.BusSettleListDao.selectInterfaceDataDi02Ids" />
        </if>
    </select>

    <delete id="deleteDuplicateData">
        DELETE FROM ${tabName}
        <if test='tabName == "som_medcas_intf_bas_info"'>
            WHERE id IN
            <foreach collection="ids" open="(" separator="," close=")" item="id">
                #{id}
            </foreach>
        </if>
        <if test='tabName  == "som_medcas_intf_diag_info" or tabName  == "som_medcas_intf_scs_cutd_info" or tabName  == "som_medcas_intf_oprn_oprt_info"'>
            WHERE di02_id IN
            <foreach collection="ids" open="(" separator="," close=")" item="id">
                #{id}
            </foreach>
        </if>
    </delete>

    <delete id="deleteDi01DuplicateData">
        DELETE FROM ${tabName}
        <if test='tabName == "som_setl_intf_bas_info"'>
            WHERE id IN
            <foreach collection="ids" open="(" separator="," close=")" item="id">
                #{id}
            </foreach>
        </if>
        <if test='tabName  == "som_setl_intf_fund_pay" or tabName  == "som_setl_intf_slow_special" or tabName  == "som_setl_intf_diag" or tabName  == "som_setl_intf_chrg_item" or tabName  == "som_setl_intf_oprn" or tabName  == "som_setl_intf_scs_cutd"'>
            WHERE di01_id IN
            <foreach collection="ids" open="(" separator="," close=")" item="id">
                #{id}
            </foreach>
        </if>
    </delete>

    <delete id="deleteDi03DuplicateData">
        DELETE FROM ${tabName}
        <if test='tabName == "som_chrg_item_intf"'>
            WHERE id IN
            <foreach collection="ids" open="(" separator="," close=")" item="id">
                #{id}
            </foreach>
        </if>
<!--        <if test='tabName  == "som_setl_intf_fund_pay" or tabName  == "som_setl_intf_slow_special" or tabName  == "som_setl_intf_diag" or tabName  == "som_setl_intf_chrg_item" or tabName  == "som_setl_intf_oprn" or tabName  == "som_setl_intf_scs_cutd"'>-->
<!--            WHERE di01_id IN-->
<!--            <foreach collection="ids" open="(" separator="," close=")" item="id">-->
<!--                #{id}-->
<!--            </foreach>-->
<!--        </if>-->
    </delete>

    <delete id="deleteDi03DetailData">
        DELETE FROM som_chrg_detl_intf WHERE id IN
            <foreach collection="list" open="(" separator="," close=")" item="id">
                #{id}
            </foreach>
    </delete>

    <delete id="deleteDi04Data">
        DELETE FROM som_drord_info_intf WHERE id IN
        <foreach collection="list" open="(" separator="," close=")" item="id">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteDi05Data">
        DELETE FROM som_clnc_examrpt_main WHERE id IN
        <foreach collection="list" open="(" separator="," close=")" item="id">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteDi05CheckItemData">
        DELETE FROM som_exam_item_info WHERE di05_id IN
        <foreach collection="list" open="(" separator="," close=")" item="id">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteDi05CheckSpecimenData">
        DELETE FROM som_exam_spcm_info WHERE di05_id IN
        <foreach collection="list" open="(" separator="," close=")" item="id">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteDi05ImageInspectionData">
        DELETE FROM som_exam_img_info WHERE di05_id IN
        <foreach collection="list" open="(" separator="," close=")" item="id">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteDi06Data">
        DELETE FROM som_clnc_test_rpot_main WHERE id IN
        <foreach collection="list" open="(" separator="," close=")" item="id">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteDi06CheckItemData">
        DELETE FROM som_test_detl_info WHERE di06_id IN
        <foreach collection="list" open="(" separator="," close=")" item="id">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteDi06CheckSpecimenData">
        DELETE FROM som_test_spcm_info WHERE di06_id IN
        <foreach collection="list" open="(" separator="," close=")" item="id">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteDi07Data">
        DELETE FROM som_elec_medrcd_info WHERE id IN
        <foreach collection="list" open="(" separator="," close=")" item="id">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteDi07DiagnosisData">
        DELETE FROM som_elec_medrcd_diag_info WHERE di07_id IN
        <foreach collection="list" open="(" separator="," close=")" item="id">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteDi07CourseRecordData">
        DELETE FROM som_elec_medrcd_codse_rcd WHERE di07_id IN
        <foreach collection="list" open="(" separator="," close=")" item="id">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteDi07OperationData">
        DELETE FROM som_elec_medrcd_oprn_rcd WHERE di07_id IN
        <foreach collection="list" open="(" separator="," close=")" item="id">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteDi07ResuceData">
        DELETE FROM som_elec_medrcd_cond_resc_rcd WHERE di07_id IN
        <foreach collection="list" open="(" separator="," close=")" item="id">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteDi07ObituaryData">
        DELETE FROM som_elec_medrcd_die_rcd WHERE di07_id IN
        <foreach collection="list" open="(" separator="," close=")" item="id">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteDi07OutHosData">
        DELETE FROM som_elec_medrcd_dscg_sumy WHERE di07_id IN
        <foreach collection="list" open="(" separator="," close=")" item="id">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteDi08Data">
        DELETE FROM som_otp_mdtrt_info WHERE id IN
        <foreach collection="list" open="(" separator="," close=")" item="id">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteDi08DiagnosisData">
        DELETE FROM som_otp_mdtrt_diag_info WHERE di08_id IN
        <foreach collection="list" open="(" separator="," close=")" item="id">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteDi09Data">
        DELETE FROM som_fee_detl_info WHERE id IN
        <foreach collection="list" open="(" separator="," close=")" item="id">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteDi10Data">
        DELETE FROM som_hosp_dept_info WHERE id IN
        <foreach collection="list" open="(" separator="," close=")" item="id">
            #{id}
        </foreach>
    </delete>

    <!-- 删除死锁导致空数据 -->
    <delete id="deleteNonDataLog">
        DELETE FROM som_datapros_log where prcs_prgs = '0' and RESULT = 'waited'
        AND ID IN
        <foreach collection="list" item="id" open="(" close=")" separator=",">
            #{id,jdbcType=VARCHAR}
        </foreach>
    </delete>

    <select id="getcompleteError" resultType="com.my.som.vo.medicalQuality.AllErrorVo">
        select SETTLE_LIST_ID as id,
               err_dscr as completeError
        from som_setl_invy_chk_err_rcd
        where err_type in ('CE01','CE02','CE03','CE04')
          AND  SETTLE_LIST_ID = #{id,jdbcType=BIGINT}
    </select>

    <select id="getlogicError" resultType="com.my.som.vo.medicalQuality.AllErrorVo">
        select SETTLE_LIST_ID as id,
               err_dscr as logicError
        from som_setl_invy_chk_err_rcd
        where err_type in ('LE01','LE02','LE03','LE04','LE05','LE06','LE07')
          AND  SETTLE_LIST_ID = #{id,jdbcType=BIGINT}
    </select>

    <!-- 查询清单字典 -->
    <select id="querySettleListAllDictType" resultType="java.util.Map">
        select code_type
        from som_sys_dic
        WHERE is_del = 0
        group by code_type
    </select>

    <!-- 查询 som_datapros_log 是否在执行 -->
    <select id="queryDataLogProcessNotExistsNum" resultType="java.lang.Integer">
        select count(1) from som_datapros_log where prcs_prgs != 100
    </select>

    <select id="queryHospitalId" resultType="com.my.som.vo.common.SeletInputInfo">
        SELECT HOSPITAL_ID AS value,medins_name AS label FROM som_hosp_info
    </select>
    <select id="queryHospital" resultType="com.my.som.vo.common.MedicalInstitutionVo">
        SELECT HOSPITAL_ID AS hospitalId,
               medins_name AS medinsName
        FROM som_hosp_info
        WHERE HOSPITAL_ID = #{hospitalId,jdbcType=VARCHAR}
    </select>

    <!-- 查询无数据日志数量 -->
    <select id="queryNonDataLogNum" resultType="java.lang.Integer">
        select id from som_datapros_log where prcs_prgs = '0' and RESULT = 'waited'
    </select>

    <!-- 查询无数据数量 -->
    <select id="queryNonDataNum" resultType="java.lang.Integer">
        select count(1) from som_hi_invy_bas_info where DATA_LOG_ID in (
            select id from som_datapros_log where prcs_prgs = '0' and RESULT = 'waited'
        )
    </select>

    <!-- 查询正在执行任务数量 -->
    <select id="queryExeProcessNum" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM som_datapros_log
        where prcs_prgs != 100 and
        INSTR(RESULT,'exp') = 0
    </select>

    <!-- 获取医疗机构配置的医保统筹区 -->
    <select id="getMedicalInsuranceArea" resultType="java.lang.String">
        SELECT insuplc_admdvs
        FROM som_grp_reqt_addr_info
        where active_flag = '1'
          and hospital_id = #{hospitalId,jdbcType=VARCHAR}
          and insuplc_admdvs is not null
        group by insuplc_admdvs
    </select>
</mapper>
