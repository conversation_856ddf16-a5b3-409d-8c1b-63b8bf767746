<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.som.dao.UmsAdminRoleRelationDao">
    <!--批量新增回写主键支持-->
    <insert id="insertList">
        INSERT INTO som_back_user_role_rlts (admin_id, role_id) VALUES
        <foreach collection="list" separator="," item="item" index="index">
            (#{item.adminId,jdbcType=BIGINT},
            #{item.roleId,jdbcType=BIGINT})
        </foreach>
    </insert>
    <select id="getRoleList" resultMap="com.my.som.mapper.SomBackUserRoleMapper.BaseResultMap">
        select r.*
        from som_back_user_role_rlts ar left join som_back_user_role r on ar.role_id = r.id
        where ar.admin_id = #{adminId}
    </select>
    <select id="getRolePermissionList" resultMap="com.my.som.mapper.SomBackUserPermMapper.BaseResultMap">
        select p.*
        from som_back_user_role_rlts ar left join som_back_user_role r on ar.role_id = r.id
            left join som_back_user_role_perm_rlts rp on r.id = rp.role_id
            left join som_back_user_perm p on rp.permission_id=p.id
            where ar.admin_id = #{adminId} and p.id is not null
    </select>
    <select id="getPermissionList" resultMap="com.my.som.mapper.SomBackUserPermMapper.BaseResultMap">
        SELECT
            p.*
        FROM
            som_back_user_role_rlts ar
            LEFT JOIN som_back_user_role r ON ar.role_id = r.id
            LEFT JOIN som_back_user_role_perm_rlts rp ON r.id = rp.role_id
            LEFT JOIN som_back_user_perm p ON rp.permission_id = p.id
        WHERE
            ar.admin_id = #{adminId}
            AND p.id IS NOT NULL
            AND p.id NOT IN (
                SELECT
                    p.id
                FROM
                    som_user_mgt_back_user_perm_rlts pr
                    LEFT JOIN som_back_user_perm p ON pr.permission_id = p.id
                WHERE
                    pr.type = - 1
                    AND pr.admin_id = #{adminId}
            )
        UNION
        SELECT
            p.*
        FROM
            som_user_mgt_back_user_perm_rlts pr
            LEFT JOIN som_back_user_perm p ON pr.permission_id = p.id
        WHERE
            pr.type = 1
            AND pr.admin_id = #{adminId}
    </select>
</mapper>