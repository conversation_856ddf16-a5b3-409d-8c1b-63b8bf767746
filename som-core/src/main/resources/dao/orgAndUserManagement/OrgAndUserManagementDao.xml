<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.som.dao.orgAndUserManagement.OrgAndUserManagementDao">
    <resultMap id="BaseResultMap" type="com.my.som.model.orgAndUserManagement.SomBasDept">
        <id column="ORG_ID" jdbcType="VARCHAR" property="orgId" />
        <result column="prnt_dept_id" jdbcType="VARCHAR" property="prntDeptId" />
        <result column="ORG_NAME" jdbcType="VARCHAR" property="orgName" />
        <result column="teamlead_type" jdbcType="CHAR" property="teamleadType" />
        <result column="biz_org_type" jdbcType="CHAR" property="bizOrgType" />
        <result column="argt_seq" jdbcType="INTEGER" property="argtSeq" />
        <result column="dept_hery" jdbcType="INTEGER" property="deptHery" />
        <result column="crter_id" jdbcType="INTEGER" property="crterId" />
        <result column="crte_time" jdbcType="TIMESTAMP" property="crteTime" />
        <result column="is_vali" jdbcType="CHAR" property="is_vali" />
        <result column="dept_resper" jdbcType="VARCHAR" property="deptResper" />
        <result column="dept_id_path" jdbcType="LONGVARCHAR" property="deptIdPath" />
        <result column="dept_name_path" jdbcType="LONGVARCHAR" property="deptNamePath" />
    </resultMap>
    <sql id="Base_Column_List">
        ORG_ID,
        prnt_dept_id,
        ORG_NAME,
        teamlead_type,
        biz_org_type,
        argt_seq,
        dept_hery,
        crter_id,
        crte_time,
        is_vali,
        dept_resper,
        dept_id_path,
        dept_name_path
    </sql>

    <select id="getOrgNodeList" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM som_bas_dept
        WHERE is_vali = '1'
        AND ORG_ID IS NOT NULL
        AND ORG_NAME is not null AND ORG_ID!='' AND ORG_NAME!=''
        <if test="queryParam.hospitalId != null and queryParam.hospitalId != ''">
        AND ORG_ID IN (
            select * from (
            with RECURSIVE temp AS (
                select * from som_bas_dept r where ORG_ID = #{queryParam.hospitalId}
                UNION ALL
                SELECT b.* from som_bas_dept b, temp t where b.prnt_dept_id = t.ORG_ID
            )
            select ORG_ID from temp
        ) a
        union all
        select * from (
            with RECURSIVE temp AS (
                select * from som_bas_dept r where ORG_ID = #{queryParam.hospitalId}
                UNION ALL
                SELECT b.* from som_bas_dept b, temp t where b.ORG_ID = t.prnt_dept_id
            )
            select ORG_ID from temp
        ) b
        )
        </if>
        <if test="queryParam.orgId !=null">
           AND ORG_ID = #{orgId,jdbcType=VARCHAR}
        </if>
        order by argt_seq
    </select>

    <!-- 查询 组织基于orgName-->
    <select id="queryBaseOrgByName" parameterType="java.lang.String" resultType="java.lang.Integer">
      select count(1) as ogrs from som_bas_dept where ORG_NAME = #{org_name,jdbcType=VARCHAR} AND is_vali = '1'
    </select>

    <!-- 根据组织id 查询子级组织 -->
    <select id="queryListOrgByOrgId" parameterType="java.lang.String" resultMap="BaseResultMap">
      select ORG_ID from som_bas_dept where instr(dept_id_path,#{orgId,jdbcType=VARCHAR}) > 1 and is_vali = '1';
    </select>

    <!--删除组织及其子组织-->
    <update id="removeListOrgByOrgId">
        delete from som_bas_dept
          where org_id in
          <foreach collection="list" item="item" separator="," open="(" close=")">
              #{item.orgId}
          </foreach>
        and is_vali = '1'
    </update>

    <insert id="addOrg" parameterType="com.my.som.model.orgAndUserManagement.SomBasDept">
        insert into som_bas_dept (ORG_ID, prnt_dept_id, ORG_NAME, teamlead_type, biz_org_type,
                                  argt_seq, dept_hery, crter_id,
                                  crte_time, is_vali, dept_resper,
                                  dept_id_path, dept_name_path)
        values (#{orgId,jdbcType=VARCHAR}, #{prntDeptId,jdbcType=VARCHAR}, #{orgName,jdbcType=VARCHAR},
                #{teamleadType,jdbcType=CHAR},
                #{bizOrgType,jdbcType=CHAR},
                #{argtSeq,jdbcType=INTEGER}, #{deptHery,jdbcType=INTEGER}, #{crterId,jdbcType=INTEGER},
                now(), #{is_vali,jdbcType=CHAR}, #{deptResper,jdbcType=VARCHAR},
                concat(#{deptIdPath,jdbcType=LONGVARCHAR}), #{deptNamePath,jdbcType=LONGVARCHAR})
    </insert>

    <!-- 批量新增组织机构 -->
    <insert id="batchInsertOrg">
        insert into som_bas_dept (ORG_ID, prnt_dept_id, ORG_NAME, teamlead_type,biz_org_type,
                              argt_seq, dept_hery, crter_id,
                              crte_time, is_vali, dept_resper,
                              dept_id_path, dept_name_path)
        values
        <foreach collection="list" item="item" separator=",">
            (
                #{item.orgId,jdbcType=VARCHAR}, #{item.prntDeptId,jdbcType=VARCHAR}, #{item.orgName,jdbcType=VARCHAR}, #{item.teamleadType,jdbcType=CHAR},
                #{item.bizOrgType,jdbcType=CHAR},
                #{item.argtSeq,jdbcType=INTEGER}, #{item.deptHery,jdbcType=INTEGER}, #{item.crterId,jdbcType=INTEGER},
                now(), #{item.is_vali,jdbcType=CHAR}, #{item.deptResper,jdbcType=VARCHAR},
                #{item.deptIdPath,jdbcType=LONGVARCHAR}, #{item.deptNamePath,jdbcType=LONGVARCHAR}
            )
        </foreach>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.my.som.model.orgAndUserManagement.SomBasDept">
        update som_bas_dept
        <set>
            <if test="prntDeptId != null">
                prnt_dept_id = #{prntDeptId,jdbcType=VARCHAR},
            </if>
            <if test="orgName != null">
                ORG_NAME = #{orgName,jdbcType=VARCHAR},
            </if>
            <if test="teamleadType != null">
                teamlead_type = #{teamleadType,jdbcType=CHAR},
            </if>
            <if test="bizOrgType != null">
                biz_org_type = #{bizOrgType,jdbcType=CHAR},
            </if>
            <if test="argtSeq != null">
                argt_seq = #{argtSeq,jdbcType=INTEGER},
            </if>
            <if test="deptHery != null">
                dept_hery = #{deptHery,jdbcType=INTEGER},
            </if>
            <if test="crterId != null">
                crter_id = #{crterId,jdbcType=INTEGER},
            </if>
            <if test="crteTime != null">
                crte_time = #{crteTime,jdbcType=TIMESTAMP},
            </if>
            <if test="is_vali != null">
                is_vali = #{is_vali,jdbcType=CHAR},
            </if>
            <if test="deptResper != null">
                dept_resper = #{deptResper,jdbcType=VARCHAR},
            </if>
            <if test="deptIdPath != null">
                dept_id_path = #{deptIdPath,jdbcType=LONGVARCHAR},
            </if>
            <if test="deptNamePath != null">
                dept_name_path = #{deptNamePath,jdbcType=LONGVARCHAR},
            </if>
        </set>
        where ORG_ID = #{orgId,jdbcType=VARCHAR}
    </update>

    <select id="extractDept" resultType="com.my.som.model.common.SomDept">
        select code,name,HOSPITAL_ID AS hospitalId,type
        from som_dept
        where HOSPITAL_ID = #{orgId}
    </select>

    <!-- 查询当前组织详情 -->
    <select id="selectDetail" resultType="com.my.som.model.orgAndUserManagement.SomBasDept">
        select ORG_ID AS orgId,
               argt_seq AS argtSeq,
               dept_hery AS deptHery,
               dept_id_path AS deptIdPath,
               dept_name_path AS deptNamePath
        from som_bas_dept
        where ORG_ID = #{orgId}
    </select>

    <!-- 查询当前组织详情下的医生情况 -->
    <select id="selectWorkers" resultType="com.my.som.model.common.SomDept">
        SELECT a.HOSPITAL_ID AS hospitalId,
            TRIM(a.CODE) as workerCode,
            TRIM(a.NAME)  as workerName,
            TRIM(a.DEPT_CODE) as code,
            TRIM(b.NAME) as name
        FROM
            som_medstff_info a left join som_dept b on b.code=a.DEPT_CODE AND a.HOSPITAL_ID = b.HOSPITAL_ID
        where a.ACTIVE_FLAG = 1
        <if test="orgId != null and orgId != ''">
            AND a.HOSPITAL_ID = #{orgId}
        </if>
    </select>

    <!-- 查询组织机构是否存在 -->
    <select id="queryOrgExtsis" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM som_bas_dept
        WHERE org_id in
        <foreach collection="list" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </select>
</mapper>
