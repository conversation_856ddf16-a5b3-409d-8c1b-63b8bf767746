<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.som.dao.drgReasonableCost.DrgPayAdverseDao">
    <!--按照科室查询-->
    <select id="list1" resultType="com.my.som.vo.drgReasonableCost.DrgPayAdverseVo">
            select
                a.dscg_caty_codg_inhosp as dscg_caty_codg_inhosp,
                a.dscg_caty_name_inhosp as deptName,
                count(1) as totalSettleMedicalNum,
                ROUND(IFNULL(SUM(b.ipt_sumfee),0), 2) AS totalInHosCost,
                ROUND(IFNULL(SUM(a.bas_med_pool_selfpay_amt),0), 2) totalFundCost,
                ROUND(IFNULL(SUM(c.POINT*c.setl_pt_val-a.ipt_sumfee_in_selfpay_amt),0), 2) as totalDrgPayCost,
                ROUND(IFNULL(SUM(c.POINT*c.setl_pt_val-a.ipt_sumfee_in_selfpay_amt),0)-IFNULL(SUM(a.bas_med_pool_selfpay_amt),0), 2) as totalProfitOrLossCost,
                CONCAT(ROUND((IFNULL(SUM(c.POINT*c.setl_pt_val-a.ipt_sumfee_in_selfpay_amt),0)-IFNULL(SUM(a.bas_med_pool_selfpay_amt),0))/
                  NULLIF(SUM(c.POINT*c.setl_pt_val-a.ipt_sumfee_in_selfpay_amt),0)*100, 2),'%') as profitOrLossRate
            from som_hi_fee_setl a
            inner join som_drg_grp_info b on a.SETTLE_LIST_ID=b.SETTLE_LIST_ID
            left join tpd_zs_drg_pay_params c on b.PATIENT_ID = c.PATIENT_ID and b.adm_time = c.adm_time and
            b.HOSPITAL_ID = c.HOSPITAL_ID
            where 1=1
            <if test="queryParam.b16c!=null and queryParam.b16c!=''">
                AND b.dscg_caty_codg_inhosp = #{queryParam.b16c}
            </if>
            <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
                AND a.HOSPITAL_ID = #{queryParam.hospitalId}
            </if>
            <if test="queryParam.insuranceTypeList!=null">
                AND a.psn_insu_type in
                <foreach collection="queryParam.insuranceTypeList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="queryParam.timeRangType=='cysj'">
                <if test="queryParam.time_range_start_date!=null and queryParam.time_range_start_date!='' and
                    queryParam.time_range_end_date!=null and queryParam.time_range_end_date!=''">
                    <![CDATA[  AND STR_TO_DATE(a.dscg_time,"%Y-%m-%d") >= STR_TO_DATE(#{queryParam.time_range_start_date},"%Y-%m-%d")
                            AND STR_TO_DATE(a.dscg_time,"%Y-%m-%d") <= STR_TO_DATE(#{queryParam.time_range_end_date},"%Y-%m-%d") ]]>
                </if>
            </if>
            <if test="queryParam.timeRangType=='rysj'">
                <if test="queryParam.time_range_start_date!=null and queryParam.time_range_start_date!='' and
                    queryParam.time_range_end_date!=null and queryParam.time_range_end_date!=''">
                    <![CDATA[  AND STR_TO_DATE(a.adm_time,"%Y-%m-%d") >= STR_TO_DATE(#{queryParam.time_range_start_date},"%Y-%m-%d")
                            AND STR_TO_DATE(a.adm_time,"%Y-%m-%d") <= STR_TO_DATE(#{queryParam.time_range_end_date},"%Y-%m-%d") ]]>
                </if>
            </if>
            <if test="queryParam.timeRangType=='jssj'">
                <if test="queryParam.time_range_start_date!=null and queryParam.time_range_start_date!='' and
                    queryParam.time_range_end_date!=null and queryParam.time_range_end_date!=''">
                    <![CDATA[  AND STR_TO_DATE(a.setl_time,"%Y-%m-%d") >= STR_TO_DATE(#{queryParam.time_range_start_date},"%Y-%m-%d")
                            AND STR_TO_DATE(a.setl_time,"%Y-%m-%d") <= STR_TO_DATE(#{queryParam.time_range_end_date},"%Y-%m-%d") ]]>
                </if>
            </if>
            GROUP BY a.dscg_caty_codg_inhosp,a.dscg_caty_name_inhosp
            ORDER BY a.dscg_caty_name_inhosp
    </select>

    <!--按照医生查询-->
    <select id="list2" resultType="com.my.som.vo.drgReasonableCost.DrgPayAdverseVo">
            select
                x.drCodg AS drCodg,
                x.drName AS drName,
                group_concat(distinct x.dscg_caty_name_inhosp) AS doctorDepts,
                IFNULL(COUNT(1),0) AS totalSettleMedicalNum,
                ROUND(IFNULL(SUM(x.ipt_sumfee),0), 2) AS totalInHosCost,
                ROUND(IFNULL(SUM(x.fundCost),0), 2) totalFundCost,
                ROUND(IFNULL(SUM(x.drgPayAmt),0), 2) as totalDrgPayCost,
                ROUND(IFNULL(SUM(x.drgPayAmt),0)-IFNULL(SUM(x.fundCost),0), 2) as totalProfitOrLossCost,
                CONCAT(ROUND((IFNULL(SUM(x.drgPayAmt),0)-IFNULL(SUM(x.fundCost),0))/
                  NULLIF(SUM(x.drgPayAmt),0)*100, 2),'%') as profitOrLossRate
            from
            (
            select
            <if test="queryParam.doctorType!=null and queryParam.doctorType!=''">
                (CASE #{queryParam.doctorType}#
                WHEN 'dct0' THEN b.ipdr_code
                WHEN 'dct1' THEN b.atddr_code
                WHEN 'dct2' THEN b.deptdrt_code
                WHEN 'dct3' THEN b.chfdr_code
                WHEN 'dct4' THEN b.train_dr_code
                WHEN 'dct5' THEN b.qltctrl_dr_code
                WHEN 'dct6' THEN b.intn_dr ELSE b.ipdr_name
                END) AS drCodg,
                (CASE #{queryParam.doctorType}#
                WHEN 'dct0' THEN b.ipdr_name
                WHEN 'dct1' THEN b.atddr_name
                WHEN 'dct2' THEN b.deptdrt_name
                WHEN 'dct3' THEN b.chfdr_name
                WHEN 'dct4' THEN b.train_dr_name
                WHEN 'dct5' THEN b.qltctrl_dr_name
                WHEN 'dct6' THEN b.intn_dr ELSE b.ipdr_name
                END) AS
                drName,
            </if>
            <if test="queryParam.doctorType==null or queryParam.doctorType==''">
                c.drCodg as drCodg,
                c.drName as drName,
            </if>
            b.dscg_caty_codg_inhosp as dscg_caty_codg_inhosp,
            b.dscg_caty_name_inhosp AS dscg_caty_name_inhosp,
            b.grp_stas as grp_stas,
            b.ipt_sumfee as ipt_sumfee,
            a.bas_med_pool_selfpay_amt as fundCost,
            t.POINT as POINT,
            t.POINT*t.setl_pt_val-a.ipt_sumfee_in_selfpay_amt as drgPayAmt
            from som_hi_fee_setl a
            inner join som_drg_grp_info b on a.SETTLE_LIST_ID=b.SETTLE_LIST_ID
            inner join tpd_zs_drg_pay_params t on b.PATIENT_ID = t.PATIENT_ID and b.adm_time = t.adm_time and
             b.HOSPITAL_ID = t.HOSPITAL_ID
            <if test="queryParam.doctorType==null or queryParam.doctorType==''">
                inner join (
                select
                b.settle_list_id as settle_list_id,
                b.drCodg as drCodg,
                b.drName as drName
                from (
                select
                SETTLE_LIST_ID as settle_list_id,
                ipdr_code as drCodg,
                ipdr_name as drName
                FROM som_drg_grp_info
                where ipdr_name is not null and ipdr_name !='-' and ipdr_name !='--'
                <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
                    AND HOSPITAL_ID = #{queryParam.hospitalId}
                </if>
                <if test="queryParam.b16c!=null and queryParam.b16c!=''">
                    AND `PRI_OUTHOS_DEPT_CODE` = #{queryParam.b16c}
                </if>
                <if test="queryParam.drCodg!=null and queryParam.drCodg!=''">
                    AND `INHOS_DOCTOR_CODE` = #{queryParam.drCodg}
                </if>
                <if test="queryParam.timeRangType=='cysj'">
                    AND SETTLE_LIST_ID IN (
                    SELECT SETTLE_LIST_ID FROM som_hi_fee_setl
                    WHERE 1=1
                    <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
                        AND  HOSPITAL_ID = #{queryParam.hospitalId}
                    </if>
                    <if test="queryParam.insuranceTypeList!=null">
                        AND  psn_insu_type in
                        <foreach collection="queryParam.insuranceTypeList" index="index" item="item" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                    </if>
                    <if test="queryParam.time_range_start_date!=null and queryParam.time_range_start_date!='' and
                                queryParam.time_range_end_date!=null and queryParam.time_range_end_date!=''">
                        <![CDATA[  AND STR_TO_DATE(dscg_time,"%Y-%m-%d") >= STR_TO_DATE(#{queryParam.time_range_start_date},"%Y-%m-%d")
                                        AND STR_TO_DATE(dscg_time,"%Y-%m-%d") <= STR_TO_DATE(#{queryParam.time_range_end_date},"%Y-%m-%d") ]]>
                    </if>
                    )
                </if>
                <if test="queryParam.timeRangType=='rysj'">
                    AND SETTLE_LIST_ID IN (
                    SELECT SETTLE_LIST_ID FROM som_hi_fee_setl
                    WHERE 1=1
                    <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
                        AND  HOSPITAL_ID = #{queryParam.hospitalId}
                    </if>
                    <if test="queryParam.insuranceTypeList!=null">
                        AND  psn_insu_type in
                        <foreach collection="queryParam.insuranceTypeList" index="index" item="item" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                    </if>
                    <if test="queryParam.time_range_start_date!=null and queryParam.time_range_start_date!='' and
                                queryParam.time_range_end_date!=null and queryParam.time_range_end_date!=''">
                        <![CDATA[  AND STR_TO_DATE(adm_time,"%Y-%m-%d") >= STR_TO_DATE(#{queryParam.time_range_start_date},"%Y-%m-%d")
                                        AND STR_TO_DATE(adm_time,"%Y-%m-%d") <= STR_TO_DATE(#{queryParam.time_range_end_date},"%Y-%m-%d") ]]>
                    </if>
                    )
                </if>
                <if test="queryParam.timeRangType=='jssj'">
                    AND SETTLE_LIST_ID IN (
                    SELECT SETTLE_LIST_ID FROM som_hi_fee_setl
                    WHERE 1=1
                    <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
                        AND  HOSPITAL_ID = #{queryParam.hospitalId}
                    </if>
                    <if test="queryParam.insuranceTypeList!=null">
                        AND  psn_insu_type in
                        <foreach collection="queryParam.insuranceTypeList" index="index" item="item" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                    </if>
                    <if test="queryParam.time_range_start_date!=null and queryParam.time_range_start_date!='' and
                                queryParam.time_range_end_date!=null and queryParam.time_range_end_date!=''">
                        <![CDATA[  AND STR_TO_DATE(setl_time,"%Y-%m-%d") >= STR_TO_DATE(#{queryParam.time_range_start_date},"%Y-%m-%d")
                                        AND STR_TO_DATE(setl_time,"%Y-%m-%d") <= STR_TO_DATE(#{queryParam.time_range_end_date},"%Y-%m-%d") ]]>
                    </if>
                    )
                </if>
                union all
                select
                SETTLE_LIST_ID as settle_list_id,
                atddr_code as drCodg,
                atddr_name as drName
                FROM som_drg_grp_info
                where atddr_name is not null and atddr_name !='-' and atddr_name !='--'
                <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
                    AND  HOSPITAL_ID = #{queryParam.hospitalId}
                </if>
                <if test="queryParam.b16c!=null and queryParam.b16c!=''">
                    AND `PRI_OUTHOS_DEPT_CODE` = #{queryParam.b16c}
                </if>
                <if test="queryParam.drCodg!=null and queryParam.drCodg!=''">
                    AND `ATTENDING_DOCTOR_CODE` = #{queryParam.drCodg}
                </if>
                <if test="queryParam.timeRangType=='cysj'">
                    AND SETTLE_LIST_ID IN (
                    SELECT SETTLE_LIST_ID FROM som_hi_fee_setl
                    WHERE 1=1
                    <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
                        AND  HOSPITAL_ID = #{queryParam.hospitalId}
                    </if>
                    <if test="queryParam.insuranceTypeList!=null">
                        AND  psn_insu_type in
                        <foreach collection="queryParam.insuranceTypeList" index="index" item="item" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                    </if>
                    <if test="queryParam.time_range_start_date!=null and queryParam.time_range_start_date!='' and
                                            queryParam.time_range_end_date!=null and queryParam.time_range_end_date!=''">
                        <![CDATA[  AND STR_TO_DATE(dscg_time,"%Y-%m-%d") >= STR_TO_DATE(#{queryParam.time_range_start_date},"%Y-%m-%d")
                                                    AND STR_TO_DATE(dscg_time,"%Y-%m-%d") <= STR_TO_DATE(#{queryParam.time_range_end_date},"%Y-%m-%d") ]]>
                    </if>
                    )
                </if>
                <if test="queryParam.timeRangType=='rysj'">
                    AND SETTLE_LIST_ID IN (
                    SELECT SETTLE_LIST_ID FROM som_hi_fee_setl
                    WHERE 1=1
                    <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
                        AND  HOSPITAL_ID = #{queryParam.hospitalId}
                    </if>
                    <if test="queryParam.insuranceTypeList!=null">
                        AND  psn_insu_type in
                        <foreach collection="queryParam.insuranceTypeList" index="index" item="item" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                    </if>
                    <if test="queryParam.time_range_start_date!=null and queryParam.time_range_start_date!='' and
                                            queryParam.time_range_end_date!=null and queryParam.time_range_end_date!=''">
                        <![CDATA[  AND STR_TO_DATE(adm_time,"%Y-%m-%d") >= STR_TO_DATE(#{queryParam.time_range_start_date},"%Y-%m-%d")
                                                    AND STR_TO_DATE(adm_time,"%Y-%m-%d") <= STR_TO_DATE(#{queryParam.time_range_end_date},"%Y-%m-%d") ]]>
                    </if>
                    )
                </if>
                <if test="queryParam.timeRangType=='jssj'">
                    AND SETTLE_LIST_ID IN (
                    SELECT SETTLE_LIST_ID FROM som_hi_fee_setl
                    WHERE 1=1
                    <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
                        AND  HOSPITAL_ID = #{queryParam.hospitalId}
                    </if>
                    <if test="queryParam.insuranceTypeList!=null">
                        AND  psn_insu_type in
                        <foreach collection="queryParam.insuranceTypeList" index="index" item="item" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                    </if>
                    <if test="queryParam.time_range_start_date!=null and queryParam.time_range_start_date!='' and
                                            queryParam.time_range_end_date!=null and queryParam.time_range_end_date!=''">
                        <![CDATA[  AND STR_TO_DATE(setl_time,"%Y-%m-%d") >= STR_TO_DATE(#{queryParam.time_range_start_date},"%Y-%m-%d")
                                                    AND STR_TO_DATE(setl_time,"%Y-%m-%d") <= STR_TO_DATE(#{queryParam.time_range_end_date},"%Y-%m-%d") ]]>
                    </if>
                    )
                </if>
                union all
                select
                SETTLE_LIST_ID as settle_list_id,
                deptdrt_code as drCodg,
                deptdrt_name as drName
                FROM som_drg_grp_info
                where deptdrt_name is not null and deptdrt_name !='-' and deptdrt_name !='--'
                <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
                    AND  HOSPITAL_ID = #{queryParam.hospitalId}
                </if>
                <if test="queryParam.b16c!=null and queryParam.b16c!=''">
                    AND `PRI_OUTHOS_DEPT_CODE` = #{queryParam.b16c}
                </if>
                <if test="queryParam.drCodg!=null and queryParam.drCodg!=''">
                    AND `DEPT_DIRECTOR_CODE` = #{queryParam.drCodg}
                </if>
                <if test="queryParam.timeRangType=='cysj'">
                    AND SETTLE_LIST_ID IN (
                    SELECT SETTLE_LIST_ID FROM som_hi_fee_setl
                    WHERE 1=1
                    <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
                        AND  HOSPITAL_ID = #{queryParam.hospitalId}
                    </if>
                    <if test="queryParam.insuranceTypeList!=null">
                        AND  psn_insu_type in
                        <foreach collection="queryParam.insuranceTypeList" index="index" item="item" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                    </if>
                    <if test="queryParam.time_range_start_date!=null and queryParam.time_range_start_date!='' and
                                            queryParam.time_range_end_date!=null and queryParam.time_range_end_date!=''">
                        <![CDATA[  AND STR_TO_DATE(dscg_time,"%Y-%m-%d") >= STR_TO_DATE(#{queryParam.time_range_start_date},"%Y-%m-%d")
                                                    AND STR_TO_DATE(dscg_time,"%Y-%m-%d") <= STR_TO_DATE(#{queryParam.time_range_end_date},"%Y-%m-%d") ]]>
                    </if>
                    )
                </if>
                <if test="queryParam.timeRangType=='rysj'">
                    AND SETTLE_LIST_ID IN (
                    SELECT SETTLE_LIST_ID FROM som_hi_fee_setl
                    WHERE 1=1
                    <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
                        AND  HOSPITAL_ID = #{queryParam.hospitalId}
                    </if>
                    <if test="queryParam.insuranceTypeList!=null">
                        AND  psn_insu_type in
                        <foreach collection="queryParam.insuranceTypeList" index="index" item="item" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                    </if>
                    <if test="queryParam.time_range_start_date!=null and queryParam.time_range_start_date!='' and
                                            queryParam.time_range_end_date!=null and queryParam.time_range_end_date!=''">
                        <![CDATA[  AND STR_TO_DATE(adm_time,"%Y-%m-%d") >= STR_TO_DATE(#{queryParam.time_range_start_date},"%Y-%m-%d")
                                                    AND STR_TO_DATE(adm_time,"%Y-%m-%d") <= STR_TO_DATE(#{queryParam.time_range_end_date},"%Y-%m-%d") ]]>
                    </if>
                    )
                </if>
                <if test="queryParam.timeRangType=='jssj'">
                    AND SETTLE_LIST_ID IN (
                    SELECT SETTLE_LIST_ID FROM som_hi_fee_setl
                    WHERE 1=1
                    <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
                        AND  HOSPITAL_ID = #{queryParam.hospitalId}
                    </if>
                    <if test="queryParam.insuranceTypeList!=null">
                        AND  psn_insu_type in
                        <foreach collection="queryParam.insuranceTypeList" index="index" item="item" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                    </if>
                    <if test="queryParam.time_range_start_date!=null and queryParam.time_range_start_date!='' and
                                            queryParam.time_range_end_date!=null and queryParam.time_range_end_date!=''">
                        <![CDATA[  AND STR_TO_DATE(setl_time,"%Y-%m-%d") >= STR_TO_DATE(#{queryParam.time_range_start_date},"%Y-%m-%d")
                                                    AND STR_TO_DATE(setl_time,"%Y-%m-%d") <= STR_TO_DATE(#{queryParam.time_range_end_date},"%Y-%m-%d") ]]>
                    </if>
                    )
                </if>
                union all
                select
                SETTLE_LIST_ID as settle_list_id,
                chfdr_code as drCodg,
                chfdr_name as drName
                FROM
                som_drg_grp_info
                where chfdr_name is not null and chfdr_name !='-' and chfdr_name !='--'
                <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
                    AND  HOSPITAL_ID = #{queryParam.hospitalId}
                </if>
                <if test="queryParam.b16c!=null and queryParam.b16c!=''">
                    AND `PRI_OUTHOS_DEPT_CODE` = #{queryParam.b16c}
                </if>
                <if test="queryParam.drCodg!=null and queryParam.drCodg!=''">
                    AND `MEDICAL_DIRECTOR_CODE` = #{queryParam.drCodg}
                </if>
                <if test="queryParam.timeRangType=='cysj'">
                    AND SETTLE_LIST_ID IN (
                    SELECT SETTLE_LIST_ID FROM som_hi_fee_setl
                    WHERE 1=1
                    <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
                        AND  HOSPITAL_ID = #{queryParam.hospitalId}
                    </if>
                    <if test="queryParam.insuranceTypeList!=null">
                        AND  psn_insu_type in
                        <foreach collection="queryParam.insuranceTypeList" index="index" item="item" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                    </if>
                    <if test="queryParam.time_range_start_date!=null and queryParam.time_range_start_date!='' and
                                        queryParam.time_range_end_date!=null and queryParam.time_range_end_date!=''">
                        <![CDATA[  AND STR_TO_DATE(dscg_time,"%Y-%m-%d") >= STR_TO_DATE(#{queryParam.time_range_start_date},"%Y-%m-%d")
                                                AND STR_TO_DATE(dscg_time,"%Y-%m-%d") <= STR_TO_DATE(#{queryParam.time_range_end_date},"%Y-%m-%d") ]]>
                    </if>
                    )
                </if>
                <if test="queryParam.timeRangType=='rysj'">
                    AND SETTLE_LIST_ID IN (
                    SELECT SETTLE_LIST_ID FROM som_hi_fee_setl
                    WHERE 1=1
                    <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
                        AND  HOSPITAL_ID = #{queryParam.hospitalId}
                    </if>
                    <if test="queryParam.insuranceTypeList!=null">
                        AND  psn_insu_type in
                        <foreach collection="queryParam.insuranceTypeList" index="index" item="item" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                    </if>
                    <if test="queryParam.time_range_start_date!=null and queryParam.time_range_start_date!='' and
                                        queryParam.time_range_end_date!=null and queryParam.time_range_end_date!=''">
                        <![CDATA[  AND STR_TO_DATE(adm_time,"%Y-%m-%d") >= STR_TO_DATE(#{queryParam.time_range_start_date},"%Y-%m-%d")
                                                AND STR_TO_DATE(adm_time,"%Y-%m-%d") <= STR_TO_DATE(#{queryParam.time_range_end_date},"%Y-%m-%d") ]]>
                    </if>
                    )
                </if>
                <if test="queryParam.timeRangType=='jssj'">
                    AND SETTLE_LIST_ID IN (
                    SELECT SETTLE_LIST_ID FROM som_hi_fee_setl
                    WHERE 1=1
                    <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
                        AND  HOSPITAL_ID = #{queryParam.hospitalId}
                    </if>
                    <if test="queryParam.insuranceTypeList!=null">
                        AND  psn_insu_type in
                        <foreach collection="queryParam.insuranceTypeList" index="index" item="item" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                    </if>
                    <if test="queryParam.time_range_start_date!=null and queryParam.time_range_start_date!='' and
                                        queryParam.time_range_end_date!=null and queryParam.time_range_end_date!=''">
                        <![CDATA[  AND STR_TO_DATE(setl_time,"%Y-%m-%d") >= STR_TO_DATE(#{queryParam.time_range_start_date},"%Y-%m-%d")
                                                AND STR_TO_DATE(setl_time,"%Y-%m-%d") <= STR_TO_DATE(#{queryParam.time_range_end_date},"%Y-%m-%d") ]]>
                    </if>
                    )
                </if>
                union all
                select
                SETTLE_LIST_ID as settle_list_id,
                train_dr_code as drCodg,
                train_dr_name as drName
                FROM som_drg_grp_info
                where train_dr_name is not null and train_dr_name !='-' and train_dr_name !='--'
                <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
                    AND  HOSPITAL_ID = #{queryParam.hospitalId}
                </if>
                <if test="queryParam.b16c!=null and queryParam.b16c!=''">
                    AND `PRI_OUTHOS_DEPT_CODE` = #{queryParam.b16c}
                </if>
                <if test="queryParam.drCodg!=null and queryParam.drCodg!=''">
                    AND `REFRESHER_DOCTOR_CODE` = #{queryParam.drCodg}
                </if>
                <if test="queryParam.timeRangType=='cysj'">
                    AND SETTLE_LIST_ID IN (
                    SELECT SETTLE_LIST_ID FROM som_hi_fee_setl
                    WHERE 1=1
                    <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
                        AND  HOSPITAL_ID = #{queryParam.hospitalId}
                    </if>
                    <if test="queryParam.insuranceTypeList!=null">
                        AND  psn_insu_type in
                        <foreach collection="queryParam.insuranceTypeList" index="index" item="item" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                    </if>
                    <if test="queryParam.time_range_start_date!=null and queryParam.time_range_start_date!='' and
                                        queryParam.time_range_end_date!=null and queryParam.time_range_end_date!=''">
                        <![CDATA[  AND STR_TO_DATE(dscg_time,"%Y-%m-%d") >= STR_TO_DATE(#{queryParam.time_range_start_date},"%Y-%m-%d")
                                                AND STR_TO_DATE(dscg_time,"%Y-%m-%d") <= STR_TO_DATE(#{queryParam.time_range_end_date},"%Y-%m-%d") ]]>
                    </if>
                    )
                </if>
                <if test="queryParam.timeRangType=='rysj'">
                    AND SETTLE_LIST_ID IN (
                    SELECT SETTLE_LIST_ID FROM som_hi_fee_setl
                    WHERE 1=1
                    <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
                        AND  HOSPITAL_ID = #{queryParam.hospitalId}
                    </if>
                    <if test="queryParam.insuranceTypeList!=null">
                        AND  psn_insu_type in
                        <foreach collection="queryParam.insuranceTypeList" index="index" item="item" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                    </if>
                    <if test="queryParam.time_range_start_date!=null and queryParam.time_range_start_date!='' and
                                        queryParam.time_range_end_date!=null and queryParam.time_range_end_date!=''">
                        <![CDATA[  AND STR_TO_DATE(adm_time,"%Y-%m-%d") >= STR_TO_DATE(#{queryParam.time_range_start_date},"%Y-%m-%d")
                                                AND STR_TO_DATE(adm_time,"%Y-%m-%d") <= STR_TO_DATE(#{queryParam.time_range_end_date},"%Y-%m-%d") ]]>
                    </if>
                    )
                </if>
                <if test="queryParam.timeRangType=='jssj'">
                    AND SETTLE_LIST_ID IN (
                    SELECT SETTLE_LIST_ID FROM som_hi_fee_setl
                    WHERE 1=1
                    <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
                        AND  HOSPITAL_ID = #{queryParam.hospitalId}
                    </if>
                    <if test="queryParam.insuranceTypeList!=null">
                        AND  psn_insu_type in
                        <foreach collection="queryParam.insuranceTypeList" index="index" item="item" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                    </if>
                    <if test="queryParam.time_range_start_date!=null and queryParam.time_range_start_date!='' and
                                        queryParam.time_range_end_date!=null and queryParam.time_range_end_date!=''">
                        <![CDATA[  AND STR_TO_DATE(setl_time,"%Y-%m-%d") >= STR_TO_DATE(#{queryParam.time_range_start_date},"%Y-%m-%d")
                                                AND STR_TO_DATE(setl_time,"%Y-%m-%d") <= STR_TO_DATE(#{queryParam.time_range_end_date},"%Y-%m-%d") ]]>
                    </if>
                    )
                </if>
                union all
                select
                SETTLE_LIST_ID as settle_list_id,
                qltctrl_dr_code as drCodg,
                qltctrl_dr_name as drName
                FROM som_drg_grp_info
                where qltctrl_dr_name is not null and qltctrl_dr_name !='-' and qltctrl_dr_name !='--'
                <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
                    AND  HOSPITAL_ID = #{queryParam.hospitalId}
                </if>
                <if test="queryParam.b16c!=null and queryParam.b16c!=''">
                    AND `PRI_OUTHOS_DEPT_CODE` = #{queryParam.b16c}
                </if>
                <if test="queryParam.drCodg!=null and queryParam.drCodg!=''">
                    AND `QUALITY_DOCTOR_CODE` = #{queryParam.drCodg}
                </if>
                <if test="queryParam.timeRangType=='cysj'">
                    AND SETTLE_LIST_ID IN (
                    SELECT SETTLE_LIST_ID FROM som_hi_fee_setl
                    WHERE 1=1
                    <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
                        AND  HOSPITAL_ID = #{queryParam.hospitalId}
                    </if>
                    <if test="queryParam.insuranceTypeList!=null">
                        AND  psn_insu_type in
                        <foreach collection="queryParam.insuranceTypeList" index="index" item="item" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                    </if>
                    <if test="queryParam.time_range_start_date!=null and queryParam.time_range_start_date!='' and
                                        queryParam.time_range_end_date!=null and queryParam.time_range_end_date!=''">
                        <![CDATA[  AND STR_TO_DATE(dscg_time,"%Y-%m-%d") >= STR_TO_DATE(#{queryParam.time_range_start_date},"%Y-%m-%d")
                                                AND STR_TO_DATE(dscg_time,"%Y-%m-%d") <= STR_TO_DATE(#{queryParam.time_range_end_date},"%Y-%m-%d") ]]>
                    </if>
                    )
                </if>
                <if test="queryParam.timeRangType=='rysj'">
                    AND SETTLE_LIST_ID IN (
                    SELECT SETTLE_LIST_ID FROM som_hi_fee_setl
                    WHERE 1=1
                    <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
                        AND  HOSPITAL_ID = #{queryParam.hospitalId}
                    </if>
                    <if test="queryParam.insuranceTypeList!=null">
                        AND  psn_insu_type in
                        <foreach collection="queryParam.insuranceTypeList" index="index" item="item" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                    </if>
                    <if test="queryParam.time_range_start_date!=null and queryParam.time_range_start_date!='' and
                                        queryParam.time_range_end_date!=null and queryParam.time_range_end_date!=''">
                        <![CDATA[  AND STR_TO_DATE(adm_time,"%Y-%m-%d") >= STR_TO_DATE(#{queryParam.time_range_start_date},"%Y-%m-%d")
                                                AND STR_TO_DATE(adm_time,"%Y-%m-%d") <= STR_TO_DATE(#{queryParam.time_range_end_date},"%Y-%m-%d") ]]>
                    </if>
                    )
                </if>
                <if test="queryParam.timeRangType=='jssj'">
                    AND SETTLE_LIST_ID IN (
                    SELECT SETTLE_LIST_ID FROM som_hi_fee_setl
                    WHERE 1=1
                    <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
                        AND  HOSPITAL_ID = #{queryParam.hospitalId}
                    </if>
                    <if test="queryParam.insuranceTypeList!=null">
                        AND  psn_insu_type in
                        <foreach collection="queryParam.insuranceTypeList" index="index" item="item" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                    </if>
                    <if test="queryParam.time_range_start_date!=null and queryParam.time_range_start_date!='' and
                                        queryParam.time_range_end_date!=null and queryParam.time_range_end_date!=''">
                        <![CDATA[  AND STR_TO_DATE(setl_time,"%Y-%m-%d") >= STR_TO_DATE(#{queryParam.time_range_start_date},"%Y-%m-%d")
                                                AND STR_TO_DATE(setl_time,"%Y-%m-%d") <= STR_TO_DATE(#{queryParam.time_range_end_date},"%Y-%m-%d") ]]>
                    </if>
                    )
                </if>
                union all
                select
                SETTLE_LIST_ID as settle_list_id,
                intn_dr as drCodg,
                intn_dr as drName
                FROM som_drg_grp_info
                where intn_dr is not null and intn_dr !='-' and intn_dr !='--'
                <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
                    AND  HOSPITAL_ID = #{queryParam.hospitalId}
                </if>
                <if test="queryParam.b16c!=null and queryParam.b16c!=''">
                    AND `PRI_OUTHOS_DEPT_CODE` = #{queryParam.b16c}
                </if>
                <if test="queryParam.drCodg!=null and queryParam.drCodg!=''">
                    AND `INTERN_DOCTOR_NAME` = #{queryParam.drCodg}
                </if>
                <if test="queryParam.timeRangType=='cysj'">
                    AND SETTLE_LIST_ID IN (
                    SELECT SETTLE_LIST_ID FROM som_hi_fee_setl
                    WHERE 1=1
                    <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
                        AND  HOSPITAL_ID = #{queryParam.hospitalId}
                    </if>
                    <if test="queryParam.insuranceTypeList!=null">
                        AND  psn_insu_type in
                        <foreach collection="queryParam.insuranceTypeList" index="index" item="item" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                    </if>
                    <if test="queryParam.time_range_start_date!=null and queryParam.time_range_start_date!='' and
                                                queryParam.time_range_end_date!=null and queryParam.time_range_end_date!=''">
                        <![CDATA[  AND STR_TO_DATE(dscg_time,"%Y-%m-%d") >= STR_TO_DATE(#{queryParam.time_range_start_date},"%Y-%m-%d")
                                                        AND STR_TO_DATE(dscg_time,"%Y-%m-%d") <= STR_TO_DATE(#{queryParam.time_range_end_date},"%Y-%m-%d") ]]>
                    </if>
                    )
                </if>
                <if test="queryParam.timeRangType=='rysj'">
                    AND SETTLE_LIST_ID IN (
                    SELECT SETTLE_LIST_ID FROM som_hi_fee_setl
                    WHERE 1=1
                    <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
                        AND  HOSPITAL_ID = #{queryParam.hospitalId}
                    </if>
                    <if test="queryParam.insuranceTypeList!=null">
                        AND  psn_insu_type in
                        <foreach collection="queryParam.insuranceTypeList" index="index" item="item" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                    </if>
                    <if test="queryParam.time_range_start_date!=null and queryParam.time_range_start_date!='' and
                                                queryParam.time_range_end_date!=null and queryParam.time_range_end_date!=''">
                        <![CDATA[  AND STR_TO_DATE(adm_time,"%Y-%m-%d") >= STR_TO_DATE(#{queryParam.time_range_start_date},"%Y-%m-%d")
                                                        AND STR_TO_DATE(adm_time,"%Y-%m-%d") <= STR_TO_DATE(#{queryParam.time_range_end_date},"%Y-%m-%d") ]]>
                    </if>
                    )
                </if>
                <if test="queryParam.timeRangType=='jssj'">
                    AND SETTLE_LIST_ID IN (
                    SELECT SETTLE_LIST_ID FROM som_hi_fee_setl
                    WHERE 1=1
                    <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
                        AND  HOSPITAL_ID = #{queryParam.hospitalId}
                    </if>
                    <if test="queryParam.insuranceTypeList!=null">
                        AND  psn_insu_type in
                        <foreach collection="queryParam.insuranceTypeList" index="index" item="item" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                    </if>
                    <if test="queryParam.time_range_start_date!=null and queryParam.time_range_start_date!='' and
                                                queryParam.time_range_end_date!=null and queryParam.time_range_end_date!=''">
                        <![CDATA[  AND STR_TO_DATE(setl_time,"%Y-%m-%d") >= STR_TO_DATE(#{queryParam.time_range_start_date},"%Y-%m-%d")
                                                        AND STR_TO_DATE(setl_time,"%Y-%m-%d") <= STR_TO_DATE(#{queryParam.time_range_end_date},"%Y-%m-%d") ]]>
                    </if>
                    )
                </if>
                ) b group by settle_list_id,drCodg,drName
                ) c on b.settle_list_id = c.settle_list_id
            </if>
            where 1=1
            <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
                AND a.HOSPITAL_ID = #{queryParam.hospitalId}
            </if>
            <if test="queryParam.b16c!=null and queryParam.b16c!=''">
                AND b.dscg_caty_codg_inhosp = #{queryParam.b16c}
            </if>
            <if test="queryParam.insuranceTypeList!=null">
                AND  a.psn_insu_type in
                <foreach collection="queryParam.insuranceTypeList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="queryParam.timeRangType=='cysj'">
                <if test="queryParam.time_range_start_date!=null and queryParam.time_range_start_date!='' and
                    queryParam.time_range_end_date!=null and queryParam.time_range_end_date!=''">
                    <![CDATA[  AND STR_TO_DATE(a.dscg_time,"%Y-%m-%d") >= STR_TO_DATE(#{queryParam.time_range_start_date},"%Y-%m-%d")
                            AND STR_TO_DATE(a.dscg_time,"%Y-%m-%d") <= STR_TO_DATE(#{queryParam.time_range_end_date},"%Y-%m-%d") ]]>
                </if>
            </if>
            <if test="queryParam.timeRangType=='rysj'">
                <if test="queryParam.time_range_start_date!=null and queryParam.time_range_start_date!='' and
                    queryParam.time_range_end_date!=null and queryParam.time_range_end_date!=''">
                    <![CDATA[  AND STR_TO_DATE(a.adm_time,"%Y-%m-%d") >= STR_TO_DATE(#{queryParam.time_range_start_date},"%Y-%m-%d")
                            AND STR_TO_DATE(a.adm_time,"%Y-%m-%d") <= STR_TO_DATE(#{queryParam.time_range_end_date},"%Y-%m-%d") ]]>
                </if>
            </if>
            <if test="queryParam.timeRangType=='jssj'">
                <if test="queryParam.time_range_start_date!=null and queryParam.time_range_start_date!='' and
                    queryParam.time_range_end_date!=null and queryParam.time_range_end_date!=''">
                    <![CDATA[  AND STR_TO_DATE(a.setl_time,"%Y-%m-%d") >= STR_TO_DATE(#{queryParam.time_range_start_date},"%Y-%m-%d")
                            AND STR_TO_DATE(a.setl_time,"%Y-%m-%d") <= STR_TO_DATE(#{queryParam.time_range_end_date},"%Y-%m-%d") ]]>
                </if>
            </if>
            ) x
            where 1 = 1
            <if test="queryParam.drCodg!=null and queryParam.drCodg!=''">
                AND x.drCodg= #{queryParam.drCodg}
            </if>
            GROUP BY x.drCodg,x.drName
            ORDER BY x.drName
    </select>
    <!--按照病组查询-->
    <select id="list3" resultType="com.my.som.vo.drgReasonableCost.DrgPayAdverseVo">
            select
            b.drg_codg as drgsCode,
            case
                when b.drg_codg is null then '排除病案'
                when b.drg_codg='0001' then '未入组病案,未入组原因：主要诊断为空'
                when b.drg_codg='0004' then '未入组病案,未入组原因：总费用小于5元'
                when b.drg_codg='0007' then '未入组病案,未入组原因：分组方案无此编码'
                when b.drg_codg='0008' then '未入组病案,未入组原因：主要诊断编码不规范'
            else b.DRG_NAME
            end AS drgsName,
            count(1) as totalSettleMedicalNum,
            ROUND(IFNULL(SUM(b.ipt_sumfee),0), 2) AS totalInHosCost,
            ROUND(IFNULL(SUM(a.bas_med_pool_selfpay_amt),0), 2) totalFundCost,
            ROUND(IFNULL(SUM(c.POINT*c.setl_pt_val-a.ipt_sumfee_in_selfpay_amt),0), 2) as totalDrgPayCost,
            ROUND(IFNULL(SUM(c.POINT*c.setl_pt_val-a.ipt_sumfee_in_selfpay_amt),0)-IFNULL(SUM(a.bas_med_pool_selfpay_amt),0), 2) as totalProfitOrLossCost,
            CONCAT(ROUND((IFNULL(SUM(c.POINT*c.setl_pt_val-a.ipt_sumfee_in_selfpay_amt),0)-IFNULL(SUM(a.bas_med_pool_selfpay_amt),0))/
              NULLIF(SUM(c.POINT*c.setl_pt_val-a.ipt_sumfee_in_selfpay_amt),0)*100, 2),'%') as profitOrLossRate
            from som_hi_fee_setl a
            inner join som_drg_grp_info b on a.SETTLE_LIST_ID=b.SETTLE_LIST_ID
            left join tpd_zs_drg_pay_params c on b.PATIENT_ID = c.PATIENT_ID and b.adm_time = c.
            adm_time and b.HOSPITAL_ID = c.HOSPITAL_ID
            where 1=1
            <if test="queryParam.b16c!=null and queryParam.b16c!=''">
                AND  b.dscg_caty_codg_inhosp = #{queryParam.b16c}
            </if>
            <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
                AND  a.HOSPITAL_ID = #{queryParam.hospitalId}
            </if>
            <if test="queryParam.queryDrg!=null and queryParam.queryDrg!=''">
                AND  a.drg_codg = #{queryParam.queryDrg}
            </if>
            <if test="queryParam.insuranceTypeList!=null">
                AND  a.psn_insu_type in
                <foreach collection="queryParam.insuranceTypeList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="queryParam.timeRangType=='cysj'">
                <if test="queryParam.time_range_start_date!=null and queryParam.time_range_start_date!='' and
                    queryParam.time_range_end_date!=null and queryParam.time_range_end_date!=''">
                    <![CDATA[  AND STR_TO_DATE(a.dscg_time,"%Y-%m-%d") >= STR_TO_DATE(#{queryParam.time_range_start_date},"%Y-%m-%d")
                            AND STR_TO_DATE(a.dscg_time,"%Y-%m-%d") <= STR_TO_DATE(#{queryParam.time_range_end_date},"%Y-%m-%d") ]]>
                </if>
            </if>
            <if test="queryParam.timeRangType=='rysj'">
                <if test="queryParam.time_range_start_date!=null and queryParam.time_range_start_date!='' and
                    queryParam.time_range_end_date!=null and queryParam.time_range_end_date!=''">
                    <![CDATA[  AND STR_TO_DATE(a.adm_time,"%Y-%m-%d") >= STR_TO_DATE(#{queryParam.time_range_start_date},"%Y-%m-%d")
                            AND STR_TO_DATE(a.adm_time,"%Y-%m-%d") <= STR_TO_DATE(#{queryParam.time_range_end_date},"%Y-%m-%d") ]]>
                </if>
            </if>
            <if test="queryParam.timeRangType=='jssj'">
                <if test="queryParam.time_range_start_date!=null and queryParam.time_range_start_date!='' and
                    queryParam.time_range_end_date!=null and queryParam.time_range_end_date!=''">
                    <![CDATA[  AND STR_TO_DATE(a.setl_time,"%Y-%m-%d") >= STR_TO_DATE(#{queryParam.time_range_start_date},"%Y-%m-%d")
                            AND STR_TO_DATE(a.setl_time,"%Y-%m-%d") <= STR_TO_DATE(#{queryParam.time_range_end_date},"%Y-%m-%d") ]]>
                </if>
            </if>
            GROUP BY b.drg_codg,b.DRG_NAME
            ORDER BY b.drg_codg
    </select>

    <!--按照病人查询-->
    <select id="list4" resultType="com.my.som.vo.drgReasonableCost.DrgPayAdverseVo">
            select
            b.PATIENT_ID as patientId,
            b.NAME as patientName,
            b.gend as gend,
            b.AGE as age,
            b.dscg_caty_name_inhosp as priOutHosDeptName,
            b.adm_time as inHosTime,
            b.dscg_time as outHosTime,
            a.setl_time as setlTime,
            a.psn_insu_type as psnInsuType,
            b.ipt_sumfee as totalInHosCost,
            ROUND(IFNULL(a.bas_med_pool_selfpay_amt,0), 2) totalFundCost,
            ROUND(IFNULL(c.POINT*c.setl_pt_val-a.ipt_sumfee_in_selfpay_amt,0), 2) as totalDrgPayCost,
            ROUND(IFNULL(c.POINT*c.setl_pt_val-a.ipt_sumfee_in_selfpay_amt,0)-IFNULL(a.bas_med_pool_selfpay_amt,0), 2) as totalProfitOrLossCost,
            CONCAT(ROUND((IFNULL(c.POINT*c.setl_pt_val-a.ipt_sumfee_in_selfpay_amt,0)-IFNULL(a.bas_med_pool_selfpay_amt,0))/
              NULLIF(c.POINT*c.setl_pt_val-a.ipt_sumfee_in_selfpay_amt,0)*100, 2),'%') as profitOrLossRate
            from som_hi_fee_setl a
            inner join som_drg_grp_info b on a.SETTLE_LIST_ID=b.SETTLE_LIST_ID
            left join tpd_zs_drg_pay_params c on b.PATIENT_ID = c.PATIENT_ID and b.adm_time = c.
            adm_time and b.HOSPITAL_ID = c.HOSPITAL_ID
            where 1=1
            <if test="queryParam.b16c!=null and queryParam.b16c!=''">
                AND  b.dscg_caty_codg_inhosp = #{queryParam.b16c}
            </if>
            <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
                AND  a.HOSPITAL_ID = #{queryParam.hospitalId}
            </if>
            <if test="queryParam.patientName!=null and queryParam.patientName!=''">
                AND  b.NAME LIKE concat("%",#{queryParam.patientName},"%")
            </if>
            <if test="queryParam.insuranceTypeList!=null">
                AND  a.psn_insu_type in
                <foreach collection="queryParam.insuranceTypeList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="queryParam.timeRangType=='cysj'">
                <if test="queryParam.time_range_start_date!=null and queryParam.time_range_start_date!='' and
                    queryParam.time_range_end_date!=null and queryParam.time_range_end_date!=''">
                    <![CDATA[  AND STR_TO_DATE(a.dscg_time,"%Y-%m-%d") >= STR_TO_DATE(#{queryParam.time_range_start_date},"%Y-%m-%d")
                            AND STR_TO_DATE(a.dscg_time,"%Y-%m-%d") <= STR_TO_DATE(#{queryParam.time_range_end_date},"%Y-%m-%d") ]]>
                </if>
            </if>
            <if test="queryParam.timeRangType=='rysj'">
                <if test="queryParam.time_range_start_date!=null and queryParam.time_range_start_date!='' and
                    queryParam.time_range_end_date!=null and queryParam.time_range_end_date!=''">
                    <![CDATA[  AND STR_TO_DATE(a.adm_time,"%Y-%m-%d") >= STR_TO_DATE(#{queryParam.time_range_start_date},"%Y-%m-%d")
                            AND STR_TO_DATE(a.adm_time,"%Y-%m-%d") <= STR_TO_DATE(#{queryParam.time_range_end_date},"%Y-%m-%d") ]]>
                </if>
            </if>
            <if test="queryParam.timeRangType=='jssj'">
                <if test="queryParam.time_range_start_date!=null and queryParam.time_range_start_date!='' and
                    queryParam.time_range_end_date!=null and queryParam.time_range_end_date!=''">
                    <![CDATA[  AND STR_TO_DATE(a.setl_time,"%Y-%m-%d") >= STR_TO_DATE(#{queryParam.time_range_start_date},"%Y-%m-%d")
                            AND STR_TO_DATE(a.setl_time,"%Y-%m-%d") <= STR_TO_DATE(#{queryParam.time_range_end_date},"%Y-%m-%d") ]]>
                </if>
            </if>
            ORDER BY b.NAME
    </select>



</mapper>