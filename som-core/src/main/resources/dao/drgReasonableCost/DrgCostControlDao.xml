<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.som.dao.drgReasonableCost.DrgCostControlDao">
    <select id="list" resultType="com.my.som.vo.drgReasonableCost.DrgCostControlVo">
        select
            x.*
        from (
            select
                a.PATIENT_ID as patientId,
                a.NAME as patientName,
                a.gend as gend,
                a.AGE as age,
                a.dscg_caty_name_inhosp as priOutHosDeptName,
                a.drg_codg AS drgsCode,
                case when a.drg_codg is null then '排除病案'
                    when a.drg_codg='0001' then '未入组病案,未入组原因：主要诊断为空'
                    when a.drg_codg='0004' then '未入组病案,未入组原因：总费用小于5元'
                    when a.drg_codg='0007' then '未入组病案,未入组原因：分组方案无此编码'
                    when a.drg_codg='0008' then '未入组病案,未入组原因：主要诊断编码不规范'
                    else b.DRG_NAME
                end AS drgsName,
               ROUND(IFNULL(a.ipt_sumfee,0), 2) AS inHosCost,
               ROUND(IFNULL(a.act_ipt,0), 2) AS inHosDays,
               <![CDATA[
                case when b.CUT_AVG_COST=0 or b.CUT_AVG_COST is null then '暂无参考标准'
                     when a.ipt_sumfee < b.CUT_AVG_COST*c.control_fee_stsb_min_prop then '费用偏低'
                     when a.ipt_sumfee > b.CUT_AVG_COST*c.control_fee_stsb_max_prop then '费用偏高'
                     when b.CUT_AVG_COST*c.control_fee_stsb_min_prop <= a.ipt_sumfee <= b.CUT_AVG_COST*c.control_fee_stsb_max_prop then '平稳区间'
                     else '无法预警'
                end
               ]]> AS costWarn
            from som_drg_grp_info a
            left join tpd_zs_drg_pay_params b on a.PATIENT_ID = b.PATIENT_ID and a.adm_time = b.adm_time and a.HOSPITAL_ID = b.HOSPITAL_ID
            cross join som_cd_control_fee_stsb_cfg c
            where 1=1
            <if test="queryParam.b16c!=null and queryParam.b16c!=''">
                AND  a.dscg_caty_codg_inhosp = #{queryParam.b16c}
            </if>
            <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
                AND  a.HOSPITAL_ID = #{queryParam.hospitalId}
            </if>
            <if test="queryParam.queryDrg!=null and queryParam.queryDrg!=''">
                AND  a.drg_codg = #{queryParam.queryDrg}
            </if>
            <if test="queryParam.patientName!=null and queryParam.patientName!=''">
                AND  a.NAME LIKE concat("%",#{queryParam.patientName},"%")
            </if>
            <if test="queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and
                queryParam.cy_end_date!=null and queryParam.cy_end_date!=''">
                <![CDATA[  AND STR_TO_DATE(a.dscg_time,"%Y-%m-%d") >= STR_TO_DATE(#{queryParam.cy_start_date},"%Y-%m-%d")
                        AND STR_TO_DATE(a.dscg_time,"%Y-%m-%d") <= STR_TO_DATE(#{queryParam.cy_end_date},"%Y-%m-%d") ]]>
            </if>
            ORDER BY a.NAME
        )x
        where 1 = 1
        <if test="queryParam.costWarnType!=null and queryParam.costWarnType!=''">
            AND  x.costWarn in
            (select
                case when #{queryParam.costWarnType}='0' then '费用偏低'
                    when #{queryParam.costWarnType}='1' then '费用偏高'
                    when #{queryParam.costWarnType}='2' then '平稳区间'
                    when #{queryParam.costWarnType}='9' then '暂无参考标准'
                else null end
            from dual
            )
        </if>
    </select>

</mapper>