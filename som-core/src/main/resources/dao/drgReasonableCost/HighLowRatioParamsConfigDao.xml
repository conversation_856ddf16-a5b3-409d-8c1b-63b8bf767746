<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.som.dao.drgReasonableCost.HighLowRatioParamsConfigDao">
    <select id="getAllParams" resultType="com.my.som.vo.drgReasonableCost.HighLowRatioParamsConfigVo">
        select
          IFNULL(control_fee_stsb_min_prop,0) as lowRatioDrgPayCost,
          IFNULL(control_fee_stsb_max_prop,0) as highRatioDrgPayCost,
          IFNULL(std_wt_low_mag_lowlmt,0) as lowRatioDrgWeight,
          IFNULL(std_wt_highmag_uplmt,0) as highRatioDrgWeight,
          IFNULL(std_time_low_mag_lowlmt,0) as lowRatioInHosDays,
          IFNULL(std_time_highmag_uplmt,0) as highRatioInHosDays,
          IFNULL(std_fee_mag_lmt,0) as lowRatioInHosCost,
          IFNULL(std_fee_highmag_uplmt,0) as highRatioInHosCost
        from som_cd_control_fee_stsb_cfg
    </select>

</mapper>