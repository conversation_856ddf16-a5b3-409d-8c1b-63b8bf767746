<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.som.dao.drgReasonableCost.BedDayCostDao">
    <select id="list" resultType="com.my.som.vo.drgReasonableCost.BedCostDayVo">
        select
            x.drg_codg AS drgsCode,
            case when x.drg_codg='0001' then '床日未入组-主要诊断为空'
                 when x.drg_codg='0004' then '床日未入组-总费用小于5元'
                 when x.drg_codg='0007' then '床日未入组-分组方案无此编码'
                 when x.drg_codg='0008' then '床日未入组-主要诊断编码不规范'
                 else x.DRG_NAME
            end AS drgsName,
            COUNT(1) AS medcasVal, <!--病案数-->
            ROUND(IFNULL(AVG(x.ipt_sumfee),0), 2) AS avgCost, <!--平均住院费用-->
            ROUND(IFNULL(SUM(x.ipt_sumfee)/NULLIF(SUM(x.act_ipt),0),0), 2) AS avgBedDayCost,  <!--平均床日费用-->
            ROUND(IFNULL(AVG(x.act_ipt),0), 2) AS avgDays, <!--平均住院日-->
            ROUND(IFNULL(y.DRG_STANDARD_BED_COST,0), 2) AS drgStandardBedCost, <!--床日付费参照标准床日费用-->
            ROUND(IFNULL(y.DRG_STANDARD_DAYS,0), 2) AS drgStandardDays, <!--床日付费参照标准住院日-->
            ROUND(IFNULL(x.standard_avg_fee,0), 2) AS standardAvgFee, <!--DRG付费参照标准住院费用-->
            ROUND(IFNULL(x.standard_ave_hosp_day,0), 2) AS standardAveHospDay, <!--DRG付费参照标准住院日-->
            case when #{queryParam.beddayPayType}='1' then '住院天数大于60天'
                 when #{queryParam.beddayPayType}='2' then '年度住院天数大于60天'
                else null end AS beddayPayType
        from (
            select
                a.drg_codg,
                a.DRG_NAME,
                c.ipt_sumfee,
                c.act_ipt,
                convert(AES_DECRYPT(UNHEX(e.standard_avg_fee),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) using utf8),
                convert(AES_DECRYPT(UNHEX(e.standard_ave_hosp_day),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) using utf8),
                SUBSTR(c.dscg_time,1,4) as year
            from som_drg_grp_exe_rcd a
            inner join som_drg_grp_info c on a.SETTLE_LIST_ID = c.SETTLE_LIST_ID
            LEFT JOIN som_drg_standard e ON a.drg_codg = e.drg_codg AND SUBSTR(c.dscg_time,1,4) = e.STANDARD_YEAR
            where a.ACTIVE_FLAG = #{queryParam.activeFlag}
            <if test="queryParam.b16c!=null and queryParam.b16c!=''">
                AND  c.dscg_caty_codg_inhosp = #{queryParam.b16c}
            </if>
            <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
                AND  a.HOSPITAL_ID = #{queryParam.hospitalId}
            </if>
            <if test="queryParam.queryDrg!=null and queryParam.queryDrg!=''">
                AND a.drg_codg = #{queryParam.queryDrg}
            </if>
            <if test="queryParam.beddayPayType!=null and queryParam.beddayPayType!=''">
                AND a.bedday_pay_type = #{queryParam.beddayPayType}
            </if>
            <if test="queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and
                queryParam.cy_end_date!=null and queryParam.cy_end_date!=''">
                <![CDATA[  AND STR_TO_DATE(c.dscg_time,"%Y-%m-%d") >= STR_TO_DATE(#{queryParam.cy_start_date},"%Y-%m-%d")
                        AND STR_TO_DATE(c.dscg_time,"%Y-%m-%d") <= STR_TO_DATE(#{queryParam.cy_end_date},"%Y-%m-%d") ]]>
            </if>
        )x left join tpd_zs_bed_drg_benchmark y on x.drg_codg = y.drg_codg and x.year = y.year
        and y.hosp_lv = #{queryParam.hospLv} and y.ACTIVE_FLAG = #{queryParam.activeFlag}
        GROUP BY x.drg_codg,x.DRG_NAME
        ORDER BY x.drg_codg
    </select>

</mapper>