<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <title>Document</title>
    <style>
        body {
            margin: 0;
            padding: 0;
        }
        div, h3, h4{
            margin: 0;
            padding: 0;
        }
        .container{
            height: 800px;
            width: 600px;
            color: black;
            margin: 0 auto;
        }
        .border {
            height: 100%;
            width: 100%;
            position: relative;
            border: 2px solid #2983b1;
        }
        .header{
            height: 6%;
            width: 100%;
            font-weight: 600;
            position: relative;
            border-bottom: 2px solid #2983b1;
        }
        .logo{
            width: 49%;
            height: 100%;
            float: left;
        }
        .header_title{
            width: 50%;
            height: 100%;
            float: left;
        }
        .logo_content {
            position: absolute;
            top: 25%;
        }
        .header_content {
            position: absolute;
            top: 25%;
            right: 0
        }


        .content{
            height: 91%;
            width: 100%;
        }


        /**
            组信息
        */
        .content_group_info{
            height: 25%;
            width: 100%;
        }
        .cgi_code_name_info{
            width: 100%;
            height: 30%;
            text-align: center;
            padding-top: 8px;
        }
        .cgi_code_name_info h4 {
            height: 45%;
            width: 100%;
            font-weight: 900;
        }
        .cgi_person_info{
            width: 100%;
            height: 20%;
            margin-left: 10%;
        }
        .cgi_person_info div {
            display: inline-block;
            margin-right: 15px;
        }
        .cgi_diagnose_info {
            width: 100%;
            height: 20%;
            margin-left: 10%;
        }
        .cgi_diagnose_info div{
            margin-bottom: 5px;
        }


        /**
            付费预测
        */
        .content_pre_info {
            height: 50%;
            width: 100%;
        }
        .cpi_pre_info {
            margin-left: 10px;
            width: 98%;
            height: 20%;
        }
        .cpi_pre_info div{
            float: left;
        }
        .cpi_pre_content_img{
            height: 100%;
            width: 10%;
            position: relative;
        }
        .cpi_pre_content_img img {
            position: absolute;
            top: 25%;
        }
        .cpi_pre_content_range{
            height: 100%;
            width: 15%;
            position: relative;
        }
        .cpi_pre_content_waring{
            height: 100%;
            width: 20%;
            position: relative;
        }
        .cpi_pre_content_waring img {
            position: absolute;
            left: 0;
            top: 20%;
        }
        .cpi_pre_content_range span{
            position: absolute;
            top: 33%;
            <#if rateName == "高倍率病例" || rateName == "低倍率病例">
                color: red;
            <#else>
                color: green;
            </#if>
        }
        .cpi_pre_content_difference{
            height: 100%;
            width: 53%;
            text-align: right;
            position: relative;
            font-weight: 900;
        }
        .cpi_pre_content_difference_wapper{
            position: absolute;
            top: 16%;
            right: 0;
        }
        .cpi_range{
            margin-left: 10px;
            width: 98%;
            height: 10%;
            position: relative;
        }
        .cpi_range div{
            width: 100%;
            height: 50%;
        }
        .cpi_range_ruler{
            z-index: 999;
        }
        .cpi_range_ruler div{
            width: 24%;
            height: 100%;
            float: left;
            border-right: 1px solid red;
            border-bottom: 1px solid red;
            border-left: 1px solid red;
            position: relative;
        }
        .cpi_scale_value{
            position: absolute;
            top: 20px;
            left: -5px;
        }
        .cpi_progress_bar{
            position: absolute;
            left: 1px;
            width: ${rulerWidth}px;
            height: 18px;
            background-color: #C7EDCC;
            font-size: 12px;
        }
        .cpi_progress_bar_cost{
            position: absolute;
            right: ${totalCostPositionRight}px;
            top: -20px;
        }
        .cpi_progress_bar_rate{
            position: absolute;
            <#if rateType == "1">
                right: -28px;
            <#else>
                right: 10px;
            </#if>
            bottom: -22px;
        }
        .cpi_progress_bar_line{
            display: block;
            width: 1px;
            height: 62px;
            background-color: red;
            position: absolute;
            right: 0;
            top: -25px;
        }

        .cpi_other_pre_info{
            margin: 50px 0px 0px 10px;
            width: 98%;
            height: 50%;
            position: relative;
        }
        .cpi_other_pre_info div{
            width: 100%;
            height: 20%;
        }
        .cpi_other_pre_info div span {
            display: inline-block;
            width: 30%;
            height: 100%;
        }
        .cpi_other_pre_info div span img {
            position: relative;
            top: 3px;
        }

        .in_hos_waring{
        <#if inHosDaysWaring == "1">
            color: red;
        <#else>
            color: #2983b1;
        </#if>
        }

        .consumable_waring{
        <#if consumableWaring == "1">
            color: red;
        <#else>
            color: #2983b1;
        </#if>
        }

        .drug_waring{
        <#if drugWaring == "1">
            color: red;
        <#else>
            color: #2983b1;
        </#if>
        }



        /**
            标题
        */
        .title{
            width: 100%;
            height: 40px;
            color: white;
            line-height: 40px;
            background-color: #2983b1;
        }

        /**
            颜色
        */
        .color_blue_title{
            color: #2983b1;
            font-weight: 600;
        }
        .color_blue_label{
            color: #2983b1;
        }
        .color_green_label{
            color: green;
        }
        .color_red_label{
            color: red;
        }
    </style>


</head>
<body>
<div class="container">

    <#if code == "1">
        <div class="border">
            <!-- header，包括 log 和功能标题 -->
            <div class="header">
                <div class="logo">
                    <div class="logo_content">
                        <img src="${url}/preGroupImages/fb_data.png" height="30px" width="30px"/>
                        <!-- <span>梵本数据</span> -->
                    </div>
                </div>
                <div class="header_title">
                    <div class="header_content">
                        智能助手
                    </div>
                </div>
            </div>

            <!-- content，包括组信息，付费信息，诊断编码顺序推荐 -->
            <div class="content">

                <!-- 组信息 -->
                <div class="content_group_info">
                    <div class="cgi_code_name_info">
                        <h4 class="color_blue_title">${dipCode}</h4>
                        <h4 class="color_blue_title">${dipName}</h4>
                    </div>

                    <!-- 病案信息 -->
                    <div class="cgi_person_info">
                        <div>
                            病案号： ${patientNum}
                        </div>

                        <div>
                            姓名：${username}
                        </div>

                        <div class="color_blue_label">
                            权重：${weight}
                        </div>

                        <div class="color_blue_label">
                            标杆：${benchmark}元
                        </div>
                    </div>

                    <!-- 诊断信息 -->
                    <div class="cgi_diagnose_info">
                        <div>主要诊断：${primaryDiagnoseCode}  ${primaryDiagnoseName}</div>
                        <div>主要手术：${primaryOperationCode}  ${primaryOperationName}</div>
                    </div>
                </div>

                <!-- 标题 -->
                <div class="title">
                    <span>付费预测</span>
                </div>

                <!-- 付费预测信息 -->
                <div class="content_pre_info">

                    <!-- 预付信息 -->
                    <div class="cpi_pre_info">
                        <div class="cpi_pre_content_img">
                            <img src="${url}/preGroupImages/advance.png" width="35px" height="35px"/>
                        </div>
                        <div class="cpi_pre_content_range">
                            <span>${rateName}</span>
                        </div>
                        <div class="cpi_pre_content_waring">
                            <#if rateName == "高倍率病例" || rateName == "低倍率病例">
                                <img src="${url}/preGroupImages/waring.png" width="35px" height="35px"/>
                            <#else>
                                <img src="${url}/preGroupImages/normal.png" width="35px" height="35px"/>
                            </#if>
                        </div>
                        <div class="cpi_pre_content_difference color_blue_label">
                        <span class="cpi_pre_content_difference_wapper">
                            <#if balance gt 0 >
                                <img src="${url}/preGroupImages/profit.png" width="35px" height="35px"/>
                            <#else>
                                <img src="${url}/preGroupImages/loss.png" width="35px" height="35px"/>
                            </#if>
                            <span style="position: relative; top: -10px">${balance}元</span>
                        </span>
                        </div>
                    </div>

                    <!-- range 区间范围 -->
                    <div class="cpi_range">
                        <div></div>
                        <i class="cpi_progress_bar">
                            <span class="cpi_progress_bar_line"></span>
                            <span class="cpi_progress_bar_cost">总费用：${totalCost}元</span>
                            <span class="cpi_progress_bar_rate">${rate}</span>
                        </i>
                        <div class="cpi_range_ruler">
                            <div>
                                <span class="cpi_scale_value">0</span>
                            </div>
                            <div>
                                <span style="position: absolute;top: 20px;left: -10px;">0.5</span>
                                <span style="position: absolute;top: 40px;left: -120px;font-size: 12px;"  class="color_red_label">低倍率：${downRateCost}</span>
                            </div>
                            <div>
                                <span class="cpi_scale_value">1</span>
                                <span style="position: absolute;top: 40px;left: -50px;font-size: 12px;"  class="color_green_label">标杆：${benchmark}</span>
                            </div>
                            <div>
                                <span class="cpi_scale_value">2</span>
                                <span style="position: absolute;top: 20px;right: -8px;">∞</span>
                                <span style="position: absolute;top: 40px;right: 26px;font-size: 12px;"  class="color_red_label">高倍率：${upRateCost}</span>
                            </div>
                        </div>
                    </div>

                    <!-- 其他预测信息 -->
                    <div class="cpi_other_pre_info">

                        <!-- 总费用 -->
                        <div>
                        <span>
                            住院总费用：${totalCost}元
                        </span>
                            <span>
                            预测结算费用：${benchmark}元
                        </span>
                        </div>

                        <!-- 住院天数 -->
                        <div>
                        <span>
                            住院天数：${inHosDays}天
                        </span>
                            <span class="color_blue_label">
                            标杆：${standardInHosDays}天
                        </span>
                            <span class="in_hos_waring">
                            住院天数差异：${inHosDaysDiff}天
                            <#if inHosDaysWaring == "1">
                                <img src="${url}/preGroupImages/waring.png" width="20px" height="20px"/>
                            </#if>
                        </span>
                        </div>

                        <!-- 药占比 -->
                        <div>
                        <span>
                            药占比：${drugRate}%
                        </span>
                            <span class="color_blue_label">
                            标杆：${standardDrugRate}%
                        </span>
                            <span class="drug_waring">
                            耗占比差异：${drugRateDiff}%
                            <#if drugWaring == "1">
                                <img src="${url}/preGroupImages/waring.png" width="20px" height="20px"/>
                            </#if>
                        </span>
                        </div>

                        <!-- 耗占比 -->
                        <div>
                        <span>
                            药占比：${consumableRate}%
                        </span>
                            <span class="color_blue_label">
                            标杆：${standardConsumableRate}%
                        </span>
                            <span class="consumable_waring">
                            药占比差异：${consumableRateDiff}%
                            <#if consumableWaring == "1">
                                <img src="${url}/preGroupImages/waring.png" width="20px" height="20px"/>
                            </#if>
                        </span>
                        </div>

                        <div>
                            <div style="color: red">
                                <#if remark != "">
                                    <img src="${url}/preGroupImages/remark.png" width="20px" height="20px" style="position: relative; top: 3px" />
                                </#if>
                                ${remark}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    <#else>
        ${errorMsg}
    </#if>
</div>
</body>
</html>
