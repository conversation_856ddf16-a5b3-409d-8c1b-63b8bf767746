<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8"/>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <style>
        .header{
            height: 5%;
            width: 98.7%;
            font-weight: bold;
            position: relative;
            border: 1px solid blue;
            line-height:250%;
            padding-left: 1%;
        }
        .container{
            height: 920px;
            width: 700px;
            color: black;
            margin: 0 auto;
            overflow: auto;
        }
        table1{
            width: 100%;
            height: 100%;
            border-collapse : collapse;
            border-spacing : 0;
            border: 1px;
        }
        tr,td{
            border: 1px solid blue;
            text-align: center;
            padding: 0.5%;
        }
        .divT1{
            height:20%;
            width: 100%;
            display: table;
            border: 1px;
        }
        .divT2{
            height:20%;
            width: 100%;
            display: table;
            border: 1px;
        }
        .divT3{
            height:20%;
            width: 100%;
            display: table;
            border: 1px;
        }
        .divT4{
            height: 20%;
            width: 100%;
            border: 1px;
            display: table;
        }
        .divT5{
            height:20%;
            width: 100%;
            display: table;
            border: 1px;
        }
        table{
            width: 100%;
            height: 100%;
            border-collapse : collapse;
            border-spacing : 0;
            border: 1px;
        }
        .fontColor{
            color: rgba(255, 0, 0, 0.93);
            font-weight: bold;
        }
        .fontColor2{
            color: green;
            font-weight: bold;
        }
        .fontBold{
            font-weight: bold;
        }
        .textLeft{
            text-align: left;
        }
        .textRight{
            text-align: right;
        }
        .textMarginRight{
            margin-right: 13%;
        }
        .title {
            background: linear-gradient(to right, #e03232, #2121cc);
            -webkit-background-clip: text;
            color: transparent;
        }
        .divFont{
            background: linear-gradient(to right, #deb223, #0daf5d);
            -webkit-background-clip: text;
            color: transparent;
            font-weight: bold;
        }
        .divFont1{
            background: linear-gradient(to right, #0cde2f, #0d33af);
            -webkit-background-clip: text;
            color: transparent;
            font-weight: bold;
        }
        .divFont2{
            background: linear-gradient(to right, #0c4fde, rgba(255, 0, 0, 0.93));
            -webkit-background-clip: text;
            color: transparent;
            font-weight: bold;
        }
    </style>
</head>
<body>
<div class="container">
    <div class="divT1">
        <table>
            <tr>
                <td class="textLeft" style="font-size: 22px">
                    <span class="title">
                        <b>金领云</b>
                    </span>
                </td>
                <td class="textRight" style="text-align: center;color: #0808ad">
                    <b>医生AI小助手</b>
                </td>
            </tr>
            <tr>
                <td class="textLeft" colspan="2">
                    <div style="width: 35%;float: left">
                        <span>姓名：</span>
                        <span>${xm}</span>
                    </div>
                    <div style="width: 35%;float: left">
                        <span>性别：${xb}</span>
                    </div>
                    <div style="width: 30%;float: left">
                        <span>年龄：${nl}</span>
                    </div>
                </td>
            </tr>
            <tr>
                <td class="textLeft" colspan="2">
                    <div style="width: 35%;float: left">
                        <span>不足一周岁(天)：</span>
                        <#if bzyzs??>
                            <span>${bzyzs}</span>
                        </#if>
                    </div>
                    <div style="width: 35%;float: left">
                        <span>病案号：${patientNum}</span>
                    </div>
                    <div style="width: 30%;float: left">
                        <span >参保类别：</span>
                        <span class="fontColor">${cblx}</span>
                    </div>
                </td>
            </tr>
            <tr>
                <td class="textLeft" colspan="2">
                    <div style="width: 35%;float: left">
                        <span>住院总费用：</span>
                        <span class="fontColor" style="margin-right: 7%">${inHosCost}</span>
                    </div>
                    <div style="width: 35%;float: left">
                        <span >同级例均：</span>
                        <#if benchmarkLevel??>
                            <span class="fontBold">${benchmarkLevel}</span>
                        </#if>
                    </div>
                    <div style="width: 30%;float: left">
                        <span>同区例均： </span>
                        <#if dipAvgCost??>
                            <span class="fontBold">${dipAvgCost}</span>
                        </#if>
                    </div>
                </td>
            </tr>
            <#if dipCode?? && dipName??>
            <tr>
                <td class="textLeft" colspan="2">
                        <span >DIP编码:</span>
                        <span class="fontBold">${dipCode}</span>
                </td>
            </tr>
            <tr>
                <td class="textLeft" colspan="2">
                    <span >DIP名称:</span>
                    <span class="fontBold">${dipName}</span>
                </td>
            </tr>
            <#else>
                    <tr>
                        <td class="textLeft" colspan="2" style="color: red;text-align: center;font-weight: bold">${rzqk} </td>
                    </tr>
            </#if>

            <tr>
                <#if zyzd?? && (zyzd?size > 0)>
                <#list zyzd as zyzdbm>
                    <#if zyzdbm_index=0 >
                        <td class="textLeft" colspan="2">
                            <span>主要诊断:</span>
                            <span class="fontBold">${zyzdbm}</span>
                        </td>
                    </#if>
                </#list>
                </#if>
            </tr>
            <tr>
                <td class="textLeft" colspan="2">
                <span class="textLeft">主要手术：</span>
                <#if zyss?? && (zyss?size > 0)>
                <#list zyss as zyssbm>
                    <#if zyssbm_index=0 >
                       <span class="fontBold">${zyssbm}</span>
                    </#if>
                </#list>
                </#if>
                </td>
            </tr>
        </table>
    </div>
    <#if dipCode??>
    <div class="header">
        <span class="divFont">DIP付费预测参考</span>
    </div>
    <div class="content" >
        <!--  高低倍率-->
        <div class="divT2">
            <table>
                <tr>
                    <td class="textLeft" colspan="5">
                        <div style="width: 35%;float: left">
                            <span>基准分值:</span>
                            <span class="textMarginRight">${jzfz}</span>
                        </div>
                        <div style="width: 35%;float: left">
                            <span>级别基本系数:</span>
                            <span class="textMarginRight">${jbjbxs}</span>
                        </div>
                        <div style="width: 30%;float: left">
                            <span>预测分值:</span>
                            <span class="textMarginRight">${ycfzFirst}</span>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td class="textLeft" colspan="5">
                        <div style="width: 35%;float: left">
                            <span>病案类型:</span>
                            <span >${balx}</span>
                        </div>
                        <div style="width: 35%;float: left">
                            <span>中医优势病例系数:</span>
                            <span >${zyysjcxs}</span>
                        </div>
                        <div style="width: 30%;float: left">
                            <span>基层病种系数:</span>
                            <span >${jcbzjcxs}</span>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td class="textLeft" colspan="5">
                        <div style="width: 35%;float: left">
                            <span>低龄病种系数:</span>
                            <span >${dlbzjcxs}</span>
                        </div>
                        <div style="width: 35%;float: left">
                            <span>重点学科系数:</span>
                            <span >${zdxkjcxs}</span>
                        </div>
                        <div style="width: 30%;float: left">
                            <span>加成分值:</span>
                            <span >${jcfz}</span>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td class="textLeft" colspan="5">
                        <div style="width: 35%;float: left">
                            <span>总分值:</span>
                            <span class="fontColor">${zfz}</span>
                        </div>
                        <div style="width: 35%;float: left">
                            <span>分值单价:</span>
                            <span class="fontColor" >${fzdj}</span>
                        </div>
                        <div style="width: 30%;float: left">
                            <span>预测费用:</span>
                            <span class="fontColor" >${ycfy}</span>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td rowspan="4">
                        <#if inHosCost gt costMax>
                            <img src="${url}/preGroupImages/waringF.png" height="60px" width="60px"/>
                            <br/>
                            <span class="fontColor" style="align-items: center">高倍率病例</span>
                        <#elseif inHosCost lt costMin>
                            <img src="${url}/preGroupImages/waringF.png" height="60px" width="60px"/>
                            <br/>
                            <span class="fontColor" style="align-items: center">低倍率病例</span>
                        <#else>
                            <img src="${url}/preGroupImages/goodCost.png" height="60px" width="60px"/>
                            <br/>
                            <span class="fontColor2" style="align-items: center">正常倍率病例</span>
                        </#if>
                    </td>
                    <td class="textRight" colspan="5"><img src="${url}/preGroupImages/money.png" width="20px" height="20px"/>
<#--                        <span class="fontBold" >预估费用：</span>-->
<#--                        <span class="fontColor" style=" margin-right: 13%;">${ycfy}元</span>-->
                        <span class="fontBold">预估结算差参考：</span>
                        <span class="fontColor">${fycy}元</span>
                    </td>
                </tr>
                <tr>
                    <td>${costMin}</td>
                    <td >${costAvg}</td>
                    <td >${costMax}</td>
                    <td >${costHightMax}</td>
                </tr>
                <tr>
                    <td style="background-color: rgba(110,108,108,0.93);color: aliceblue">低倍率</td>
                    <td style="background-color: rgba(44,175,49,0.79);color: aliceblue">正常倍率区间</td>
                    <#--                    <td style="background-color: #4caf50c9;color: aliceblue">盈利</td>-->
                    <#--                    <td style="background-color: #3f51b5e0;color: aliceblue">略亏</td>-->
                    <td style="background-color: rgba(255,0,0,0.7);color: aliceblue" >高倍率</td>
                    <td style="background-color: rgba(206,94,14,0.85);color: aliceblue" >持平</td>
                </tr>
                <tr>
                    <td>低倍（&lt;0.5）</td>
                    <td >正常区间（0.5-1.8/2）</td>
                    <td >高倍（&gt;1.8/2）</td>
                    <td >极高倍（&gt;5）</td>
                </tr>
            </table>
        </div>
        <div class="divT3">
            <table>
                <tr>
                    <td class="textLeft">
                        <div style="width: 35%;float: left">
                            <span class="textMarginRight" >本病例住院天数：${inHosDays}天</span>
                        </div>
                        <div style="width: 30%;float: left">
                            <span class="textMarginRight" >标杆天数：${inHosAvgDays}天</span>
                        </div>
                        <div style="width: 30%;float: left">
                            <span >差距：${zytscy}天</span>
                        </div>
                        <#if zytscy gt 0>
                        <img src="${url}/preGroupImages/good.png" style="float:right" width="28px" height="27px" /></td>
                    <#else>
                        <img src="${url}/preGroupImages/bad.png" style="float:right" width="28px" height="27px" /></td>
                    </#if>

                </tr>
                <tr>
                    <td class="textLeft">
                        <div style="width: 35%;float: left">
                            <span class="textMarginRight">本病例药费占比：${ybfyzb}%</span>
                        </div>
                        <div style="width: 30%;float: left">
                            <span class="textMarginRight">标杆占比：${ypfbgzb}%</span>
                        </div>
                        <div style="width: 30%;float: left">
                            <span>差距：${ypfycy}%</span>
                        </div>
                        <#if ypfycy gt 0>
                        <img src="${url}/preGroupImages/good.png" style="float:right" width="28px" height="27px" /></td>
                    <#else>
                        <img src="${url}/preGroupImages/bad.png" style="float:right" width="28px" height="27px" /></td>
                    </#if>
                </tr>
                <tr>
                    <td class="textLeft">
                        <div style="width: 35%;float: left">
                            <span class="textMarginRight">本病例耗材占比：${hcfyzb}%</span>
                        </div>
                        <div style="width: 30%;float: left">
                            <span class="textMarginRight">标杆占比：${hcfybgzb}%</span>
                        </div>
                        <div style="width: 30%;float: left">
                            <span>差距：${hcfycy}%</span>
                        </div>
                        <#if hcfycy gt 0>
                        <img src="${url}/preGroupImages/good.png" style="float:right" width="28px" height="27px" /></td>
                    <#else>
                        <img src="${url}/preGroupImages/bad.png" style="float:right" width="28px" height="27px" /></td>
                    </#if>
                </tr>
                <#--                <tr>-->
                <#--                    <td class="textLeft">-->
                <#--                        <span class="textMarginRight">本病例检验检查：${jcfyzb}%</span>-->
                <#--                        <span class="textMarginRight">标杆占比：20%</span>-->
                <#--                        <span>差距：+2%</span>-->
                <#--                        <img src="${url}/preGroupImages/good.png"  style="float:right" /></td>-->
                <#--                </tr>-->
            </table>
        </div>
        <div class="header">
            <span class="divFont1" >AI智能推荐参考</span>
        </div>
        <div class="divT4">
            <table>
                <#if zntj?? && (zntj ?size > 0)>
                    <tr>
                        <td>主要诊断及手术</td>
                        <td>病组名称</td>
                        <td>参考费用</td>
                        <td>推荐排序</td>
                    </tr>
                    <#list zntj as dip>
                        <tr class="space-between">
                            <td class="textLeft">${dip.dipCode}</td>
                            <td class="textLeft">${dip.dipName}</td>
                            <td class="textRight">${dip.dipAvgCostLevel}</td>
                            <td class="textRight">${dip_index+1}</td>
                        </tr>
                    </#list>
                <#else>
                    <tr>
                        <td><h2 style="text-align: center">暂无推荐</h2></td>
                    </tr>
                </#if>
                <#--                <tr>-->
                <#--                    <td class="textLeft">N31.8膀胱未特指的神经肌肉......</td>-->
                <#--                    <td class="textLeft">膀胱未特·· </td>-->
                <#--                    <td class="textRight"> 8774.00</td>-->
                <#--                    <td class="textRight">1</td>-->
                <#--                </tr>-->
                <#--                <tr>-->
                <#--                    <td class="textLeft">N31.8膀胱未特指的神经肌肉功..</td>-->
                <#--                    <td class="textLeft">膀胱未特··</td>-->
                <#--                    <td class="textRight">8774.00</td>-->
                <#--                    <td class="textRight">2</td>-->
                <#--                </tr>-->
                <#--                <tr>-->
                <#--                    <td class="textLeft">N31.8膀胱未特指的神经肌肉功..</td>-->
                <#--                    <td class="textLeft">膀胱未特··</td>-->
                <#--                    <td class="textRight">8774.00</td>-->
                <#--                    <td class="textRight">3</td>-->
                <#--                </tr>-->
                <#--                <tr>-->
                <#--                    <td class="textLeft">N31.8膀胱未特指的神经肌肉功..</td>-->
                <#--                    <td class="textLeft">膀胱未特··</td>-->
                <#--                    <td class="textRight">8774.00</td>-->
                <#--                    <td class="textRight">4</td>-->
                <#--                </tr>-->
            </table>
        </div>
        </#if>
        <div class="header">
            <span class="divFont2">AI病案内涵质控</span>
        </div>
        <div class="divT5">
            <table>
                <tr>
                    <td class="textLeft">
                        <span class="textMarginRight fontBold">本病例总得分：${medicalRecordScore}分</span>
                        <img src="${url}/preGroupImages/waringF2.png" width="20px" height="20px"/>
                        <span class="textMarginRight fontBold">逻辑性${countLogicalCheck}处</span>
                        <img src="${url}/preGroupImages/waringF2.png" width="20px" height="20px"/>
                        <span class="textMarginRight fontBold">完整性${countIntegrity}处</span>
                        <#--                        <img src="${url}/preGroupImages/waringF2.png" width="20px" height="20px"/>-->
                        <#--                        <span class="fontBold">疑似疾病与诊疗不符</span>-->
                    </td>
                </tr>
                <#--                <tr>-->
                <#--                    <td class="textLeft">-->
                <#--                        <span class="fontBold">疑似疾病与诊疗不符：</span>-->
                <#--                        <span>主要诊断与检验检验项目不符，且X光检查频率过高；</span>-->
                <#--                    </td>-->
                <#--                </tr>-->
                <tr>
                    <td class="textLeft">
                        <span class="fontBold">逻辑性问题：</span>
                    </td>
                </tr>
                <#if logicalCheckList?? && (logicalCheckList?size > 0)>
                    <#list logicalCheckList as item>
                        <tr>
                            <td class="textLeft">
                                <span>${item_index+1}、${item}</span>
                            </td>
                        </tr>
                    </#list>
                </#if>
                <tr>
                    <td class="textLeft">
                        <span class="fontBold">完整性问题：</span>
                    </td>
                </tr>
                <#if integrityCheckList?? && (integrityCheckList?size > 0)>
                    <#list integrityCheckList as item>
                        <tr>
                            <td class="textLeft" style="font-size: 15px">
                                <span style="text-align: left">${item_index+1}、${item.content}</span>
                                <span class="fontColor" style="float: right">扣${item.score}分</span>
                            </td>
                        </tr>
                    </#list>
                </#if>
            </table>
        </div>
    </div>
<#--    <#else>-->
<#--    <div class="divT2-1">-->
<#--        <table>-->
<#--            <tr>-->
<#--                <td>-->
<#--                    <h2 style="text-align: center;color: red">DIP入组失败</h2>-->
<#--                </td>-->
<#--            </tr>-->
<#--        </table>-->
<#--    </div>-->
</div>
</body>
</html>
