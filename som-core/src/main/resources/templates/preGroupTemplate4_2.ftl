<!DOCTYPE html>
<html lang="en" xmlns="http://www.w3.org/1999/html">
<head>
    <meta charset="UTF-8">
    <title>预分组/预校验</title>

    <!-- styles -->

    <style>
        @charset "utf-8";

        #tabs {
        	overflow: hidden;
        	width: 96%;
        	margin: 0;
        	padding: 0 2%;
        	list-style: none;
        	/*background: #fff;*/
        	position: relative;
        }
        #tabs:after{
        	content: "";
        	position: absolute;
        	left: 2%;
        	bottom: 0;
        	width: 96%;
        	height: 2px;
        	background-color: #E4E7ED;
        	z-index: 1;
        }
        #tabs li {
        	float: left;
        }
        #tabs a {
        	position: relative;
        	/*background: #fff;*/
        	padding: .7em 0;
        	width: 120px;
        	text-align: center;
        	float: left;
        	text-decoration: none;
        	color: #444;
        }
         #tabs a:hover, #tabs a:hover::after, #tabs a:focus, #tabs a:focus::after {
        	 color: rgba(18, 150, 219, 0.9);
        }
         .active{
        	 color: rgba(18, 150, 219, 0.9)!important;
        	 border-bottom: 2px solid rgba(18, 150, 219, 0.9)!important;
        	 z-index: 10;
         }
        #tabs a:focus {
        	outline: 0;
        }

        #content {
        	/*background: #fff;*/
        	/*padding: 2em;*/
        	height: 400px;
        	position: relative;
        	z-index: 2;
        	font-size: 13px;
        }
        #content h2, #content h3, #content p {
        	margin: 0 0 15px 0;
        }
        /* ------------------------------------------------- */

        #about {
        	color: #999;
        }
        #about a {
        	color: #eee;
        }

        .sl-content-padding1{
        	padding: 0 2%;
        }

        .sl-content-padding2{
        	padding: 2% 2% 0 2%;
        }

        .sl-qi-item-wrap{
        	padding: 2% 0 2% 2%;
        	border: 1px solid #EBEEF5;
        	width: 98%;
        	margin-bottom: 1%;
        }

        .sl-qi-item-wrap-title{
        	color: #e6a23c;
        	border-bottom: 1px solid #EBEEF5;
        	width: 98%;
        	padding: 1% 0;
        	font-size: 15px;
        }
        .sl-qi-complete{
        	padding: 25% 0% 0% 30%;
        	width: 98%;
        	font-size: 30px;
        	font-weight:bold;
        }

        @charset "utf-8";

        #tabs2 {
        	overflow: hidden;
        	width: 100%;
        	margin: 0;
        	list-style: none;
        	position: relative;
        	background: #fff;
        }
        #tabs2:after{
        	content: "";
        	position: absolute;
        	left: 2%;
        	bottom: 0;
        	width: 96%;
        	height: 2px;
        	background-color: #E4E7ED;
        	z-index: 1;
        }
        #tabs2 li {
        	float: left;
        }
        #tabs2 a {
        	position: relative;
        	background: #fff;
        	padding: .7em 0;
        	text-align: center;
        	width: 120px;
        	float: left;
        	text-decoration: none;
        	color: #444;
        	z-index: 3;
        }
         #tabs2 a:hover, #tabs2 a:hover::after, #tabs2 a:focus, #tabs2 a:focus::after {
        	 color: rgba(91,174,99,0.53);
        }
        #tabs2 a:focus {
        	outline: 0;
        }

        #content2 {
        	background: #fff;
        	height: calc(300px - 4%);
        	position: relative;
        	padding: 2%;
        	z-index: 2;
        }
        #content2 h2, #content2 h3, #content2 p {
        	margin: 0 0 15px 0;
        }

        * {
            margin: 0;
            padding: 0;
        }

        .container {
            height: 920px;
            width: 700px;
            color: black;
            margin: 0 auto;
            background-color: #deebf7;
            border-radius: 25px 25px 5px 5px;
        }

        body {
            font-family: 微软雅黑;
        }

        .header {
            height: 1%;
            width: 100%;
            position: relative;
            text-align: center;
            top: 10px
        }

        .header-title {
            font-size: 22px;
            font-weight: bold
        }

        .content {
            height: 96%;
            width: 100%;
            position: relative;
            overflow: auto;
        }

        .content-inner {
            height: 96%;
            padding-right: 5px;
        }

        .content::-webkit-scrollbar {
            width: 6px;
            height: 6px;
            background: transparent;
        }

        .content::-webkit-scrollbar-thumb {
            background: transparent;
            border-radius: 4px;
        }

        .content:hover::-webkit-scrollbar-thumb {
            background: hsla(0, 0%, 53%, 0.4);
        }

        .content:hover::-webkit-scrollbar-track {
            background: hsla(0, 0%, 53%, 0.1);
        }

        .in-group-circumstance {
            width: 100%;
        }

        .title {
            height: 5%;
            width: 100%;
            font-size: 18px;
            font-weight: bold;
            position: relative;
            margin-top: 0.5rem;
        }

        .hop-title {
            width: 100%;
            font-size: 18px;
            font-weight: bold;
            margin-top: 0.5rem;
        }

        .title img {
            padding: 0 2%;
        }

        .divider {
            background-color: #dcdfe6;
            position: relative;
            display: block;
            height: 1px;
            width: 96%;
            margin: 1.5%
        }

        /**
            统一flex布局
        */
        .in-group-base,
        .in-group-diagnose,
        .in-group-diagnose .in-group-diagnose-details,
        .title,
        .in-group-dip-group {
            display: flex;
            justify-content: left;
            align-items: center;
        }

        .hop-title,
        .in-group-base div {
            display: flex;
            justify-content: center;
            align-items: center;
        }

        /**
            DIP入组信息
        */
        .in-group-content {
            width: 98%;
            padding-left: 2%;
            color: #797979;
        }

        .in-group-dip-group {
            height: 25%;
            width: 100%;
            font-weight: 600;
            font-size: 16px;
            padding-top: 1%;
            display: flex;
            align-items: center;

        }

        .in-group-dip-group div {
            margin-left: 2%;
        }

        .in-group-diagnose {
            height: 100%;
            padding-top: 2%;
        }

        .in-group-diagnose .in-group-diagnose-details {
            width: 50%;
        }

        .in-group-base-info {
            font-size: 12px;
            height: 80%;
            width: 88%;
            display: flex;
            flex-direction: column;
        }

        .in-group-base-info div img {
            margin-right: 0.5rem;
        }

        .in-group-base-info .in-group-base {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding-top: 2%;
        }

        .operation {
            min-height: 4rem;
            display: flex;
            align-items: center;
        }

        .operation::-webkit-scrollbar {
            width: 6px;
            height: 6px;
            background: transparent;
        }

        .operation::-webkit-scrollbar-thumb {
            background: transparent;
            border-radius: 4px;
        }

        .operation:hover::-webkit-scrollbar-thumb {
            background: hsla(0, 0%, 53%, 0.4);
        }

        .operation:hover::-webkit-scrollbar-track {
            background: hsla(0, 0%, 53%, 0.1);
        }

        .operation p {
            display: block;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
        }


        /**
            DIP支付预测
        */
        .pre-pay {
            width: 100%;
            /*margin-top: 2.5rem;*/
        }

        .pre-pay-content {
            width: 96%;
            padding: 2% 2% 0 2%;
            position: relative;
        }

        .pre-pay-content-range {
            width: 100%;
            height: 25px;
            display: flex;
            flex-wrap: nowrap;
        }

        .range-item {
            text-align: center;
            line-height: 25px;
            font-size: 12px;
            position: relative;
        }

        /*  */

        .low {
            width: 20%;
            height: 100%;
            border-right: 1px solid #1296dbab;
            /*border-right: none;*/
            border-radius: 25px 0 0 25px;
            background-image: linear-gradient(to right, rgba(158,173,189, 0.63), rgba(0,205,209, 0.53));

        }

        .normal {
            width: 20%;
            height: 100%;
            border-right: 1px solid #1296dbab;
            /*border-right: none;*/
            background-color: rgba(0,205,209, 0.53);
        }

        .normal-to-high {
            width: 40%;
            height: 100%;
            border-right: 1px solid #00A584ab;
            /*border-right: none;*/
            background-image:linear-gradient(to right, rgba(0,205,209, 0.53), rgba(224, 107, 85, 0.7));
        }

        .high {
            position: relative;
            width: 20%;
            height: 100%;
            border-radius: 0 25px 25px 0;
            background-color: rgba(224, 107, 85, 0.7);
        }

        .range-money {
            position: absolute;
            right: -18%;
        }

        .range-money-normal {
            position: absolute;
            right: -20%;
        }

        .tooltip__popper.is-dark {
            display: inline-block;
            background: #3BC08E99;
            color: #ffffff;
        }

        .tooltip__popper {
            height: 15px;
            border-radius: 15px;
            padding: 10px;
            z-index: 2000;
            font-size: 12px;
            line-height: 1.2;
            min-width: 10px;
            word-wrap: break-word;
            position: relative;
        }

        .tooltip__popper .popper__arrow {
            border-width: 6px;
        }

        .tooltip__popper .popper__arrow, .tooltip__popper .popper__arrow:after {
            display: block;
            width: 0;
            height: 0;
            border-color: transparent;
            border-style: solid;
        }

        .tooltip__popper .popper__arrow:after {
            position: absolute;
            bottom: -7px;
            left: 26px;
            margin: -5px;
            border-top-color: #3BC08E99;
            border-bottom-width: 1px;
            content: " ";
            border-width: 6px;
        }


        /**
            病组智能推荐
        */
        .group-recommend {
            width: 100%;
        }

        .group-recommend-content {
            width: 96%;
            padding: 0 2%;
            position: relative;
        }

        .core-group {
            width: 100%;
        }

        /**
            病案质控
        */
        .record-qc {
            width: 100%;
        }

        .record-content {
            width: 96%;
            padding: 0 2%;
            position: relative;
        }

        /**
            清单质控
         */
        .sl-content {
            width: 100%;
            /*padding: 0 2%;*/
        }

        .sl-content-item {
            display: block;
            height: 100%;
        }

        .sl-content-item::-webkit-scrollbar {
            width: 6px;
            height: 6px;
            background: transparent;
        }

        .sl-content-item::-webkit-scrollbar-thumb {
            background: transparent;
            border-radius: 4px;
        }

        .sl-content-item:hover::-webkit-scrollbar-thumb {
            background: hsla(0, 0%, 53%, 0.4);
        }

        .sl-content-item:hover::-webkit-scrollbar-track {
            background: hsla(0, 0%, 53%, 0.1);
        }

        /*!***/
        /*    德阳新增*/
        /* *!*/
        /*.group-dy-content{*/
        /*    width: 96%;*/
        /*    padding: 0 2%;*/
        /*    position: relative;*/
        /*}*/

        /*tab实现*/

        /* Style the tab */

        .top_tabs {
            display: flex;
            width: 96%;
            margin: 0 2%;
            margin-top: 1.5rem;
            border-bottom: 1px solid #ddd;
        }

        .top_tabs .tab {
            height: 5%;
            font-size: 16px;
            /*font-weight: bold;*/
            margin-top: 0.5rem;
            padding: 10px;
            border: none;
            outline: none;
            cursor: pointer;
            color: #666666;
            background-color: #fafafa;
            display: flex;
            margin-right: 4px;
            border-radius: 4px 4px 0px 0px;

        }

        .top_tabs .tab.active {
            color: rgba(18, 150, 219, 0.9) !important;
            border-bottom: 2px solid rgba(18, 150, 219, 0.9) !important;
        }

        /* Style the buttons that are used to open the tab content */
        .top_tabs .tab:hover {
            color: rgba(18, 150, 219, 0.9);
        }

        /* Style the tab content */
        .tabcontent {
            width: 100%;
            /*margin: 0 2%;*/
            /*padding-top: 1.5rem;*/
        }
        .top_tabs .tab_icon{
            height: 25px;
            width: 25px;
            padding-right: 2px;
        }

        .top_tabs .tab span{
           display: flex;
            line-height: 25px;
        }
         span.warn-tips{
             color: #3BC08E99;font-size: 12px;
         }

         .in-ccmcc-base{
             display: flex;
             padding-top: 1%;
         }

        .in-ccmcc-base div {
            display: flex;
            /* justify-content: center; */
            align-items: center;
            flex: 1;
            justify-content: flex-start;
        }

        #content2 .none_item_content{
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 22px;
            font-weight: 600;
        }

        ul {
        	list-style-type: none;
        }

        a {
        	color: #b63b4d;
        	text-decoration: none;
        }


        h1 {
         	color: #FFF;
         	font-size: 24px;
         	font-weight: 400;
         	text-align: center;
         	margin-top: 80px;
         }

        h1 a {
         	color: #c12c42;
         	font-size: 16px;
         }

         .accordion {
         	width: 100%;
        	 margin-top: 2rem;
         	background: #FFF;
         	-webkit-border-radius: 4px;
         	-moz-border-radius: 4px;
         	border-radius: 4px;
         }

        .accordion .link {
        	cursor: pointer;
        	display: block;
        	padding: 15px 15px 15px 42px;
        	color: #4D4D4D;
        	font-size: 14px;
        	font-weight: 700;
        	border-bottom: 1px solid #CCC;
        	position: relative;
        	-webkit-transition: all 0.4s ease;
        	-o-transition: all 0.4s ease;
        	transition: all 0.4s ease;
        }
        .secondary-menu{
        	cursor: pointer !important;
        	display: block;
        }

        .accordion li:last-child .link {
        	border-bottom: 0;
        }

        .accordion li i {
        	position: absolute;
        	top: 16px;
        	left: 12px;
        	font-size: 18px;
        	color: #595959;
        	-webkit-transition: all 0.4s ease;
        	-o-transition: all 0.4s ease;
        	transition: all 0.4s ease;
        }

        .accordion li i.fa-chevron-down {
        	right: 12px;
        	left: auto;
        	font-size: 16px;
        }

        .accordion li.open .link {
        	color: #b63b4d;
        }

        .accordion li.open i {
        	color: #b63b4d;
        }
        .accordion li.open i.fa-chevron-down {
        	-webkit-transform: rotate(180deg);
        	-ms-transform: rotate(180deg);
        	-o-transform: rotate(180deg);
        	transform: rotate(180deg);
        }

        /**
         * Submenu
         -----------------------------*/
         .submenu, .secondary-submenu {
         	display: none;
         	background: rgba(74,165,211, 0.29);
         	font-size: 14px;
         }

         .submenu li {
         	/*border-bottom: 1px solid #4b4a5e;*/
        	 position: relative;
         }
         .secondary-submenu li {
         	/*border-bottom: 1px solid #4b4a5e;*/
        	 position: relative;
         }

         .submenu a {
         	display: block;
         	text-decoration: none;
        	 color: black;
        	 cursor: default;
         	padding: 12px;
         	padding-left: 42px;
         	-webkit-transition: all 0.25s ease;
         	-o-transition: all 0.25s ease;
         	transition: all 0.25s ease;
         }

         .submenu a:hover {
         	background: rgba(18, 150, 219, 0.4);
         	color: #FFF;
         }

         .submenu .a-hover{
        	background: rgba(18, 150, 219, 0.4);
         }

         .secondary-submenu a:hover{
        	color: black;
        	background: none;
         }


        .sfq-header {
        	width: 95%;
        }
        .sfq-header p {
        	display: inline-block;
        	font-size: 12px;
        	overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
        }
        .des-icon{
        	position: absolute;
        	left: 12px;
        	top: 10px;
        }

        /*!
         *  Font Awesome 4.7.0 by @davegandy - http://fontawesome.io - @fontawesome
         *  License - http://fontawesome.io/license (Font: SIL OFL 1.1, CSS: MIT License)
         */
        /* FONT PATH
         * -------------------------- */
        @font-face {
          font-family: 'FontAwesome';
          src: url('../fonts/fontawesome-webfont.eot?v=4.7.0');
          src: url('../fonts/fontawesome-webfont.eot?#iefix&v=4.7.0') format('embedded-opentype'), url('../fonts/fontawesome-webfont.woff2?v=4.7.0') format('woff2'), url('../fonts/fontawesome-webfont.woff?v=4.7.0') format('woff'), url('../fonts/fontawesome-webfont.ttf?v=4.7.0') format('truetype'), url('../fonts/fontawesome-webfont.svg?v=4.7.0#fontawesomeregular') format('svg');
          font-weight: normal;
          font-style: normal;
        }
        .fa {
          display: inline-block;
          font: normal normal normal 14px/1 FontAwesome;
          font-size: inherit;
          text-rendering: auto;
          -webkit-font-smoothing: antialiased;
          -moz-osx-font-smoothing: grayscale;
        }
        /* makes the font 33% larger relative to the icon container */
        .fa-lg {
          font-size: 1.33333333em;
          line-height: 0.75em;
          vertical-align: -15%;
        }
        .fa-2x {
          font-size: 2em;
        }
        .fa-3x {
          font-size: 3em;
        }
        .fa-4x {
          font-size: 4em;
        }
        .fa-5x {
          font-size: 5em;
        }
        .fa-fw {
          width: 1.28571429em;
          text-align: center;
        }
        .fa-ul {
          padding-left: 0;
          margin-left: 2.14285714em;
          list-style-type: none;
        }
        .fa-ul > li {
          position: relative;
        }
        .fa-li {
          position: absolute;
          left: -2.14285714em;
          width: 2.14285714em;
          top: 0.14285714em;
          text-align: center;
        }
        .fa-li.fa-lg {
          left: -1.85714286em;
        }
        .fa-border {
          padding: .2em .25em .15em;
          border: solid 0.08em #eeeeee;
          border-radius: .1em;
        }
        .fa-pull-left {
          float: left;
        }
        .fa-pull-right {
          float: right;
        }
        .fa.fa-pull-left {
          margin-right: .3em;
        }
        .fa.fa-pull-right {
          margin-left: .3em;
        }
        /* Deprecated as of 4.4.0 */
        .pull-right {
          float: right;
        }
        .pull-left {
          float: left;
        }
        .fa.pull-left {
          margin-right: .3em;
        }
        .fa.pull-right {
          margin-left: .3em;
        }
        .fa-spin {
          -webkit-animation: fa-spin 2s infinite linear;
          animation: fa-spin 2s infinite linear;
        }
        .fa-pulse {
          -webkit-animation: fa-spin 1s infinite steps(8);
          animation: fa-spin 1s infinite steps(8);
        }
        @-webkit-keyframes fa-spin {
          0% {
            -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
          }
          100% {
            -webkit-transform: rotate(359deg);
            transform: rotate(359deg);
          }
        }
        @keyframes fa-spin {
          0% {
            -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
          }
          100% {
            -webkit-transform: rotate(359deg);
            transform: rotate(359deg);
          }
        }
        .fa-rotate-90 {
          -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=1)";
          -webkit-transform: rotate(90deg);
          -ms-transform: rotate(90deg);
          transform: rotate(90deg);
        }
        .fa-rotate-180 {
          -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=2)";
          -webkit-transform: rotate(180deg);
          -ms-transform: rotate(180deg);
          transform: rotate(180deg);
        }
        .fa-rotate-270 {
          -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=3)";
          -webkit-transform: rotate(270deg);
          -ms-transform: rotate(270deg);
          transform: rotate(270deg);
        }
        .fa-flip-horizontal {
          -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=0, mirror=1)";
          -webkit-transform: scale(-1, 1);
          -ms-transform: scale(-1, 1);
          transform: scale(-1, 1);
        }
        .fa-flip-vertical {
          -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=2, mirror=1)";
          -webkit-transform: scale(1, -1);
          -ms-transform: scale(1, -1);
          transform: scale(1, -1);
        }
        :root .fa-rotate-90,
        :root .fa-rotate-180,
        :root .fa-rotate-270,
        :root .fa-flip-horizontal,
        :root .fa-flip-vertical {
          filter: none;
        }
        .fa-stack {
          position: relative;
          display: inline-block;
          width: 2em;
          height: 2em;
          line-height: 2em;
          vertical-align: middle;
        }
        .fa-stack-1x,
        .fa-stack-2x {
          position: absolute;
          left: 0;
          width: 100%;
          text-align: center;
        }
        .fa-stack-1x {
          line-height: inherit;
        }
        .fa-stack-2x {
          font-size: 2em;
        }
        .fa-inverse {
          color: #ffffff;
        }
        /* Font Awesome uses the Unicode Private Use Area (PUA) to ensure screen
           readers do not read off random characters that represent icons */
        .fa-glass:before {
          content: "\f000";
        }
        .fa-music:before {
          content: "\f001";
        }
        .fa-search:before {
          content: "\f002";
        }
        .fa-envelope-o:before {
          content: "\f003";
        }
        .fa-heart:before {
          content: "\f004";
        }
        .fa-star:before {
          content: "\f005";
        }
        .fa-star-o:before {
          content: "\f006";
        }
        .fa-user:before {
          content: "\f007";
        }
        .fa-film:before {
          content: "\f008";
        }
        .fa-th-large:before {
          content: "\f009";
        }
        .fa-th:before {
          content: "\f00a";
        }
        .fa-th-list:before {
          content: "\f00b";
        }
        .fa-check:before {
          content: "\f00c";
        }
        .fa-remove:before,
        .fa-close:before,
        .fa-times:before {
          content: "\f00d";
        }
        .fa-search-plus:before {
          content: "\f00e";
        }
        .fa-search-minus:before {
          content: "\f010";
        }
        .fa-power-off:before {
          content: "\f011";
        }
        .fa-signal:before {
          content: "\f012";
        }
        .fa-gear:before,
        .fa-cog:before {
          content: "\f013";
        }
        .fa-trash-o:before {
          content: "\f014";
        }
        .fa-home:before {
          content: "\f015";
        }
        .fa-file-o:before {
          content: "\f016";
        }
        .fa-clock-o:before {
          content: "\f017";
        }
        .fa-road:before {
          content: "\f018";
        }
        .fa-download:before {
          content: "\f019";
        }
        .fa-arrow-circle-o-down:before {
          content: "\f01a";
        }
        .fa-arrow-circle-o-up:before {
          content: "\f01b";
        }
        .fa-inbox:before {
          content: "\f01c";
        }
        .fa-play-circle-o:before {
          content: "\f01d";
        }
        .fa-rotate-right:before,
        .fa-repeat:before {
          content: "\f01e";
        }
        .fa-refresh:before {
          content: "\f021";
        }
        .fa-list-alt:before {
          content: "\f022";
        }
        .fa-lock:before {
          content: "\f023";
        }
        .fa-flag:before {
          content: "\f024";
        }
        .fa-headphones:before {
          content: "\f025";
        }
        .fa-volume-off:before {
          content: "\f026";
        }
        .fa-volume-down:before {
          content: "\f027";
        }
        .fa-volume-up:before {
          content: "\f028";
        }
        .fa-qrcode:before {
          content: "\f029";
        }
        .fa-barcode:before {
          content: "\f02a";
        }
        .fa-tag:before {
          content: "\f02b";
        }
        .fa-tags:before {
          content: "\f02c";
        }
        .fa-book:before {
          content: "\f02d";
        }
        .fa-bookmark:before {
          content: "\f02e";
        }
        .fa-print:before {
          content: "\f02f";
        }
        .fa-camera:before {
          content: "\f030";
        }
        .fa-font:before {
          content: "\f031";
        }
        .fa-bold:before {
          content: "\f032";
        }
        .fa-italic:before {
          content: "\f033";
        }
        .fa-text-height:before {
          content: "\f034";
        }
        .fa-text-width:before {
          content: "\f035";
        }
        .fa-align-left:before {
          content: "\f036";
        }
        .fa-align-center:before {
          content: "\f037";
        }
        .fa-align-right:before {
          content: "\f038";
        }
        .fa-align-justify:before {
          content: "\f039";
        }
        .fa-list:before {
          content: "\f03a";
        }
        .fa-dedent:before,
        .fa-outdent:before {
          content: "\f03b";
        }
        .fa-indent:before {
          content: "\f03c";
        }
        .fa-video-camera:before {
          content: "\f03d";
        }
        .fa-photo:before,
        .fa-image:before,
        .fa-picture-o:before {
          content: "\f03e";
        }
        .fa-pencil:before {
          content: "\f040";
        }
        .fa-map-marker:before {
          content: "\f041";
        }
        .fa-adjust:before {
          content: "\f042";
        }
        .fa-tint:before {
          content: "\f043";
        }
        .fa-edit:before,
        .fa-pencil-square-o:before {
          content: "\f044";
        }
        .fa-share-square-o:before {
          content: "\f045";
        }
        .fa-check-square-o:before {
          content: "\f046";
        }
        .fa-arrows:before {
          content: "\f047";
        }
        .fa-step-backward:before {
          content: "\f048";
        }
        .fa-fast-backward:before {
          content: "\f049";
        }
        .fa-backward:before {
          content: "\f04a";
        }
        .fa-play:before {
          content: "\f04b";
        }
        .fa-pause:before {
          content: "\f04c";
        }
        .fa-stop:before {
          content: "\f04d";
        }
        .fa-forward:before {
          content: "\f04e";
        }
        .fa-fast-forward:before {
          content: "\f050";
        }
        .fa-step-forward:before {
          content: "\f051";
        }
        .fa-eject:before {
          content: "\f052";
        }
        .fa-chevron-left:before {
          content: "\f053";
        }
        .fa-chevron-right:before {
          content: "\f054";
        }
        .fa-plus-circle:before {
          content: "\f055";
        }
        .fa-minus-circle:before {
          content: "\f056";
        }
        .fa-times-circle:before {
          content: "\f057";
        }
        .fa-check-circle:before {
          content: "\f058";
        }
        .fa-question-circle:before {
          content: "\f059";
        }
        .fa-info-circle:before {
          content: "\f05a";
        }
        .fa-crosshairs:before {
          content: "\f05b";
        }
        .fa-times-circle-o:before {
          content: "\f05c";
        }
        .fa-check-circle-o:before {
          content: "\f05d";
        }
        .fa-ban:before {
          content: "\f05e";
        }
        .fa-arrow-left:before {
          content: "\f060";
        }
        .fa-arrow-right:before {
          content: "\f061";
        }
        .fa-arrow-up:before {
          content: "\f062";
        }
        .fa-arrow-down:before {
          content: "\f063";
        }
        .fa-mail-forward:before,
        .fa-share:before {
          content: "\f064";
        }
        .fa-expand:before {
          content: "\f065";
        }
        .fa-compress:before {
          content: "\f066";
        }
        .fa-plus:before {
          content: "\f067";
        }
        .fa-minus:before {
          content: "\f068";
        }
        .fa-asterisk:before {
          content: "\f069";
        }
        .fa-exclamation-circle:before {
          content: "\f06a";
        }
        .fa-gift:before {
          content: "\f06b";
        }
        .fa-leaf:before {
          content: "\f06c";
        }
        .fa-fire:before {
          content: "\f06d";
        }
        .fa-eye:before {
          content: "\f06e";
        }
        .fa-eye-slash:before {
          content: "\f070";
        }
        .fa-warning:before,
        .fa-exclamation-triangle:before {
          content: "\f071";
        }
        .fa-plane:before {
          content: "\f072";
        }
        .fa-calendar:before {
          content: "\f073";
        }
        .fa-random:before {
          content: "\f074";
        }
        .fa-comment:before {
          content: "\f075";
        }
        .fa-magnet:before {
          content: "\f076";
        }
        .fa-chevron-up:before {
          content: "\f077";
        }
        .fa-chevron-down:before {
          content: "\f078";
        }
        .fa-retweet:before {
          content: "\f079";
        }
        .fa-shopping-cart:before {
          content: "\f07a";
        }
        .fa-folder:before {
          content: "\f07b";
        }
        .fa-folder-open:before {
          content: "\f07c";
        }
        .fa-arrows-v:before {
          content: "\f07d";
        }
        .fa-arrows-h:before {
          content: "\f07e";
        }
        .fa-bar-chart-o:before,
        .fa-bar-chart:before {
          content: "\f080";
        }
        .fa-twitter-square:before {
          content: "\f081";
        }
        .fa-facebook-square:before {
          content: "\f082";
        }
        .fa-camera-retro:before {
          content: "\f083";
        }
        .fa-key:before {
          content: "\f084";
        }
        .fa-gears:before,
        .fa-cogs:before {
          content: "\f085";
        }
        .fa-comments:before {
          content: "\f086";
        }
        .fa-thumbs-o-up:before {
          content: "\f087";
        }
        .fa-thumbs-o-down:before {
          content: "\f088";
        }
        .fa-star-half:before {
          content: "\f089";
        }
        .fa-heart-o:before {
          content: "\f08a";
        }
        .fa-sign-out:before {
          content: "\f08b";
        }
        .fa-linkedin-square:before {
          content: "\f08c";
        }
        .fa-thumb-tack:before {
          content: "\f08d";
        }
        .fa-external-link:before {
          content: "\f08e";
        }
        .fa-sign-in:before {
          content: "\f090";
        }
        .fa-trophy:before {
          content: "\f091";
        }
        .fa-github-square:before {
          content: "\f092";
        }
        .fa-upload:before {
          content: "\f093";
        }
        .fa-lemon-o:before {
          content: "\f094";
        }
        .fa-phone:before {
          content: "\f095";
        }
        .fa-square-o:before {
          content: "\f096";
        }
        .fa-bookmark-o:before {
          content: "\f097";
        }
        .fa-phone-square:before {
          content: "\f098";
        }
        .fa-twitter:before {
          content: "\f099";
        }
        .fa-facebook-f:before,
        .fa-facebook:before {
          content: "\f09a";
        }
        .fa-github:before {
          content: "\f09b";
        }
        .fa-unlock:before {
          content: "\f09c";
        }
        .fa-credit-card:before {
          content: "\f09d";
        }
        .fa-feed:before,
        .fa-rss:before {
          content: "\f09e";
        }
        .fa-hdd-o:before {
          content: "\f0a0";
        }
        .fa-bullhorn:before {
          content: "\f0a1";
        }
        .fa-bell:before {
          content: "\f0f3";
        }
        .fa-certificate:before {
          content: "\f0a3";
        }
        .fa-hand-o-right:before {
          content: "\f0a4";
        }
        .fa-hand-o-left:before {
          content: "\f0a5";
        }
        .fa-hand-o-up:before {
          content: "\f0a6";
        }
        .fa-hand-o-down:before {
          content: "\f0a7";
        }
        .fa-arrow-circle-left:before {
          content: "\f0a8";
        }
        .fa-arrow-circle-right:before {
          content: "\f0a9";
        }
        .fa-arrow-circle-up:before {
          content: "\f0aa";
        }
        .fa-arrow-circle-down:before {
          content: "\f0ab";
        }
        .fa-globe:before {
          content: "\f0ac";
        }
        .fa-wrench:before {
          content: "\f0ad";
        }
        .fa-tasks:before {
          content: "\f0ae";
        }
        .fa-filter:before {
          content: "\f0b0";
        }
        .fa-briefcase:before {
          content: "\f0b1";
        }
        .fa-arrows-alt:before {
          content: "\f0b2";
        }
        .fa-group:before,
        .fa-users:before {
          content: "\f0c0";
        }
        .fa-chain:before,
        .fa-link:before {
          content: "\f0c1";
        }
        .fa-cloud:before {
          content: "\f0c2";
        }
        .fa-flask:before {
          content: "\f0c3";
        }
        .fa-cut:before,
        .fa-scissors:before {
          content: "\f0c4";
        }
        .fa-copy:before,
        .fa-files-o:before {
          content: "\f0c5";
        }
        .fa-paperclip:before {
          content: "\f0c6";
        }
        .fa-save:before,
        .fa-floppy-o:before {
          content: "\f0c7";
        }
        .fa-square:before {
          content: "\f0c8";
        }
        .fa-navicon:before,
        .fa-reorder:before,
        .fa-bars:before {
          content: "\f0c9";
        }
        .fa-list-ul:before {
          content: "\f0ca";
        }
        .fa-list-ol:before {
          content: "\f0cb";
        }
        .fa-strikethrough:before {
          content: "\f0cc";
        }
        .fa-underline:before {
          content: "\f0cd";
        }
        .fa-table:before {
          content: "\f0ce";
        }
        .fa-magic:before {
          content: "\f0d0";
        }
        .fa-truck:before {
          content: "\f0d1";
        }
        .fa-pinterest:before {
          content: "\f0d2";
        }
        .fa-pinterest-square:before {
          content: "\f0d3";
        }
        .fa-google-plus-square:before {
          content: "\f0d4";
        }
        .fa-google-plus:before {
          content: "\f0d5";
        }
        .fa-money:before {
          content: "\f0d6";
        }
        .fa-caret-down:before {
          content: "\f0d7";
        }
        .fa-caret-up:before {
          content: "\f0d8";
        }
        .fa-caret-left:before {
          content: "\f0d9";
        }
        .fa-caret-right:before {
          content: "\f0da";
        }
        .fa-columns:before {
          content: "\f0db";
        }
        .fa-unsorted:before,
        .fa-sort:before {
          content: "\f0dc";
        }
        .fa-sort-down:before,
        .fa-sort-desc:before {
          content: "\f0dd";
        }
        .fa-sort-up:before,
        .fa-sort-asc:before {
          content: "\f0de";
        }
        .fa-envelope:before {
          content: "\f0e0";
        }
        .fa-linkedin:before {
          content: "\f0e1";
        }
        .fa-rotate-left:before,
        .fa-undo:before {
          content: "\f0e2";
        }
        .fa-legal:before,
        .fa-gavel:before {
          content: "\f0e3";
        }
        .fa-dashboard:before,
        .fa-tachometer:before {
          content: "\f0e4";
        }
        .fa-comment-o:before {
          content: "\f0e5";
        }
        .fa-comments-o:before {
          content: "\f0e6";
        }
        .fa-flash:before,
        .fa-bolt:before {
          content: "\f0e7";
        }
        .fa-sitemap:before {
          content: "\f0e8";
        }
        .fa-umbrella:before {
          content: "\f0e9";
        }
        .fa-paste:before,
        .fa-clipboard:before {
          content: "\f0ea";
        }
        .fa-lightbulb-o:before {
          content: "\f0eb";
        }
        .fa-exchange:before {
          content: "\f0ec";
        }
        .fa-cloud-download:before {
          content: "\f0ed";
        }
        .fa-cloud-upload:before {
          content: "\f0ee";
        }
        .fa-user-md:before {
          content: "\f0f0";
        }
        .fa-stethoscope:before {
          content: "\f0f1";
        }
        .fa-suitcase:before {
          content: "\f0f2";
        }
        .fa-bell-o:before {
          content: "\f0a2";
        }
        .fa-coffee:before {
          content: "\f0f4";
        }
        .fa-cutlery:before {
          content: "\f0f5";
        }
        .fa-file-text-o:before {
          content: "\f0f6";
        }
        .fa-building-o:before {
          content: "\f0f7";
        }
        .fa-hospital-o:before {
          content: "\f0f8";
        }
        .fa-ambulance:before {
          content: "\f0f9";
        }
        .fa-medkit:before {
          content: "\f0fa";
        }
        .fa-fighter-jet:before {
          content: "\f0fb";
        }
        .fa-beer:before {
          content: "\f0fc";
        }
        .fa-h-square:before {
          content: "\f0fd";
        }
        .fa-plus-square:before {
          content: "\f0fe";
        }
        .fa-angle-double-left:before {
          content: "\f100";
        }
        .fa-angle-double-right:before {
          content: "\f101";
        }
        .fa-angle-double-up:before {
          content: "\f102";
        }
        .fa-angle-double-down:before {
          content: "\f103";
        }
        .fa-angle-left:before {
          content: "\f104";
        }
        .fa-angle-right:before {
          content: "\f105";
        }
        .fa-angle-up:before {
          content: "\f106";
        }
        .fa-angle-down:before {
          content: "\f107";
        }
        .fa-desktop:before {
          content: "\f108";
        }
        .fa-laptop:before {
          content: "\f109";
        }
        .fa-tablet:before {
          content: "\f10a";
        }
        .fa-mobile-phone:before,
        .fa-mobile:before {
          content: "\f10b";
        }
        .fa-circle-o:before {
          content: "\f10c";
        }
        .fa-quote-left:before {
          content: "\f10d";
        }
        .fa-quote-right:before {
          content: "\f10e";
        }
        .fa-spinner:before {
          content: "\f110";
        }
        .fa-circle:before {
          content: "\f111";
        }
        .fa-mail-reply:before,
        .fa-reply:before {
          content: "\f112";
        }
        .fa-github-alt:before {
          content: "\f113";
        }
        .fa-folder-o:before {
          content: "\f114";
        }
        .fa-folder-open-o:before {
          content: "\f115";
        }
        .fa-smile-o:before {
          content: "\f118";
        }
        .fa-frown-o:before {
          content: "\f119";
        }
        .fa-meh-o:before {
          content: "\f11a";
        }
        .fa-gamepad:before {
          content: "\f11b";
        }
        .fa-keyboard-o:before {
          content: "\f11c";
        }
        .fa-flag-o:before {
          content: "\f11d";
        }
        .fa-flag-checkered:before {
          content: "\f11e";
        }
        .fa-terminal:before {
          content: "\f120";
        }
        .fa-code:before {
          content: "\f121";
        }
        .fa-mail-reply-all:before,
        .fa-reply-all:before {
          content: "\f122";
        }
        .fa-star-half-empty:before,
        .fa-star-half-full:before,
        .fa-star-half-o:before {
          content: "\f123";
        }
        .fa-location-arrow:before {
          content: "\f124";
        }
        .fa-crop:before {
          content: "\f125";
        }
        .fa-code-fork:before {
          content: "\f126";
        }
        .fa-unlink:before,
        .fa-chain-broken:before {
          content: "\f127";
        }
        .fa-question:before {
          content: "\f128";
        }
        .fa-info:before {
          content: "\f129";
        }
        .fa-exclamation:before {
          content: "\f12a";
        }
        .fa-superscript:before {
          content: "\f12b";
        }
        .fa-subscript:before {
          content: "\f12c";
        }
        .fa-eraser:before {
          content: "\f12d";
        }
        .fa-puzzle-piece:before {
          content: "\f12e";
        }
        .fa-microphone:before {
          content: "\f130";
        }
        .fa-microphone-slash:before {
          content: "\f131";
        }
        .fa-shield:before {
          content: "\f132";
        }
        .fa-calendar-o:before {
          content: "\f133";
        }
        .fa-fire-extinguisher:before {
          content: "\f134";
        }
        .fa-rocket:before {
          content: "\f135";
        }
        .fa-maxcdn:before {
          content: "\f136";
        }
        .fa-chevron-circle-left:before {
          content: "\f137";
        }
        .fa-chevron-circle-right:before {
          content: "\f138";
        }
        .fa-chevron-circle-up:before {
          content: "\f139";
        }
        .fa-chevron-circle-down:before {
          content: "\f13a";
        }
        .fa-html5:before {
          content: "\f13b";
        }
        .fa-css3:before {
          content: "\f13c";
        }
        .fa-anchor:before {
          content: "\f13d";
        }
        .fa-unlock-alt:before {
          content: "\f13e";
        }
        .fa-bullseye:before {
          content: "\f140";
        }
        .fa-ellipsis-h:before {
          content: "\f141";
        }
        .fa-ellipsis-v:before {
          content: "\f142";
        }
        .fa-rss-square:before {
          content: "\f143";
        }
        .fa-play-circle:before {
          content: "\f144";
        }
        .fa-ticket:before {
          content: "\f145";
        }
        .fa-minus-square:before {
          content: "\f146";
        }
        .fa-minus-square-o:before {
          content: "\f147";
        }
        .fa-level-up:before {
          content: "\f148";
        }
        .fa-level-down:before {
          content: "\f149";
        }
        .fa-check-square:before {
          content: "\f14a";
        }
        .fa-pencil-square:before {
          content: "\f14b";
        }
        .fa-external-link-square:before {
          content: "\f14c";
        }
        .fa-share-square:before {
          content: "\f14d";
        }
        .fa-compass:before {
          content: "\f14e";
        }
        .fa-toggle-down:before,
        .fa-caret-square-o-down:before {
          content: "\f150";
        }
        .fa-toggle-up:before,
        .fa-caret-square-o-up:before {
          content: "\f151";
        }
        .fa-toggle-right:before,
        .fa-caret-square-o-right:before {
          content: "\f152";
        }
        .fa-euro:before,
        .fa-eur:before {
          content: "\f153";
        }
        .fa-gbp:before {
          content: "\f154";
        }
        .fa-dollar:before,
        .fa-usd:before {
          content: "\f155";
        }
        .fa-rupee:before,
        .fa-inr:before {
          content: "\f156";
        }
        .fa-cny:before,
        .fa-rmb:before,
        .fa-yen:before,
        .fa-jpy:before {
          content: "\f157";
        }
        .fa-ruble:before,
        .fa-rouble:before,
        .fa-rub:before {
          content: "\f158";
        }
        .fa-won:before,
        .fa-krw:before {
          content: "\f159";
        }
        .fa-bitcoin:before,
        .fa-btc:before {
          content: "\f15a";
        }
        .fa-file:before {
          content: "\f15b";
        }
        .fa-file-text:before {
          content: "\f15c";
        }
        .fa-sort-alpha-asc:before {
          content: "\f15d";
        }
        .fa-sort-alpha-desc:before {
          content: "\f15e";
        }
        .fa-sort-amount-asc:before {
          content: "\f160";
        }
        .fa-sort-amount-desc:before {
          content: "\f161";
        }
        .fa-sort-numeric-asc:before {
          content: "\f162";
        }
        .fa-sort-numeric-desc:before {
          content: "\f163";
        }
        .fa-thumbs-up:before {
          content: "\f164";
        }
        .fa-thumbs-down:before {
          content: "\f165";
        }
        .fa-youtube-square:before {
          content: "\f166";
        }
        .fa-youtube:before {
          content: "\f167";
        }
        .fa-xing:before {
          content: "\f168";
        }
        .fa-xing-square:before {
          content: "\f169";
        }
        .fa-youtube-play:before {
          content: "\f16a";
        }
        .fa-dropbox:before {
          content: "\f16b";
        }
        .fa-stack-overflow:before {
          content: "\f16c";
        }
        .fa-instagram:before {
          content: "\f16d";
        }
        .fa-flickr:before {
          content: "\f16e";
        }
        .fa-adn:before {
          content: "\f170";
        }
        .fa-bitbucket:before {
          content: "\f171";
        }
        .fa-bitbucket-square:before {
          content: "\f172";
        }
        .fa-tumblr:before {
          content: "\f173";
        }
        .fa-tumblr-square:before {
          content: "\f174";
        }
        .fa-long-arrow-down:before {
          content: "\f175";
        }
        .fa-long-arrow-up:before {
          content: "\f176";
        }
        .fa-long-arrow-left:before {
          content: "\f177";
        }
        .fa-long-arrow-right:before {
          content: "\f178";
        }
        .fa-apple:before {
          content: "\f179";
        }
        .fa-windows:before {
          content: "\f17a";
        }
        .fa-android:before {
          content: "\f17b";
        }
        .fa-linux:before {
          content: "\f17c";
        }
        .fa-dribbble:before {
          content: "\f17d";
        }
        .fa-skype:before {
          content: "\f17e";
        }
        .fa-foursquare:before {
          content: "\f180";
        }
        .fa-trello:before {
          content: "\f181";
        }
        .fa-female:before {
          content: "\f182";
        }
        .fa-male:before {
          content: "\f183";
        }
        .fa-gittip:before,
        .fa-gratipay:before {
          content: "\f184";
        }
        .fa-sun-o:before {
          content: "\f185";
        }
        .fa-moon-o:before {
          content: "\f186";
        }
        .fa-archive:before {
          content: "\f187";
        }
        .fa-bug:before {
          content: "\f188";
        }
        .fa-vk:before {
          content: "\f189";
        }
        .fa-weibo:before {
          content: "\f18a";
        }
        .fa-renren:before {
          content: "\f18b";
        }
        .fa-pagelines:before {
          content: "\f18c";
        }
        .fa-stack-exchange:before {
          content: "\f18d";
        }
        .fa-arrow-circle-o-right:before {
          content: "\f18e";
        }
        .fa-arrow-circle-o-left:before {
          content: "\f190";
        }
        .fa-toggle-left:before,
        .fa-caret-square-o-left:before {
          content: "\f191";
        }
        .fa-dot-circle-o:before {
          content: "\f192";
        }
        .fa-wheelchair:before {
          content: "\f193";
        }
        .fa-vimeo-square:before {
          content: "\f194";
        }
        .fa-turkish-lira:before,
        .fa-try:before {
          content: "\f195";
        }
        .fa-plus-square-o:before {
          content: "\f196";
        }
        .fa-space-shuttle:before {
          content: "\f197";
        }
        .fa-slack:before {
          content: "\f198";
        }
        .fa-envelope-square:before {
          content: "\f199";
        }
        .fa-wordpress:before {
          content: "\f19a";
        }
        .fa-openid:before {
          content: "\f19b";
        }
        .fa-institution:before,
        .fa-bank:before,
        .fa-university:before {
          content: "\f19c";
        }
        .fa-mortar-board:before,
        .fa-graduation-cap:before {
          content: "\f19d";
        }
        .fa-yahoo:before {
          content: "\f19e";
        }
        .fa-google:before {
          content: "\f1a0";
        }
        .fa-reddit:before {
          content: "\f1a1";
        }
        .fa-reddit-square:before {
          content: "\f1a2";
        }
        .fa-stumbleupon-circle:before {
          content: "\f1a3";
        }
        .fa-stumbleupon:before {
          content: "\f1a4";
        }
        .fa-delicious:before {
          content: "\f1a5";
        }
        .fa-digg:before {
          content: "\f1a6";
        }
        .fa-pied-piper-pp:before {
          content: "\f1a7";
        }
        .fa-pied-piper-alt:before {
          content: "\f1a8";
        }
        .fa-drupal:before {
          content: "\f1a9";
        }
        .fa-joomla:before {
          content: "\f1aa";
        }
        .fa-language:before {
          content: "\f1ab";
        }
        .fa-fax:before {
          content: "\f1ac";
        }
        .fa-building:before {
          content: "\f1ad";
        }
        .fa-child:before {
          content: "\f1ae";
        }
        .fa-paw:before {
          content: "\f1b0";
        }
        .fa-spoon:before {
          content: "\f1b1";
        }
        .fa-cube:before {
          content: "\f1b2";
        }
        .fa-cubes:before {
          content: "\f1b3";
        }
        .fa-behance:before {
          content: "\f1b4";
        }
        .fa-behance-square:before {
          content: "\f1b5";
        }
        .fa-steam:before {
          content: "\f1b6";
        }
        .fa-steam-square:before {
          content: "\f1b7";
        }
        .fa-recycle:before {
          content: "\f1b8";
        }
        .fa-automobile:before,
        .fa-car:before {
          content: "\f1b9";
        }
        .fa-cab:before,
        .fa-taxi:before {
          content: "\f1ba";
        }
        .fa-tree:before {
          content: "\f1bb";
        }
        .fa-spotify:before {
          content: "\f1bc";
        }
        .fa-deviantart:before {
          content: "\f1bd";
        }
        .fa-soundcloud:before {
          content: "\f1be";
        }
        .fa-database:before {
          content: "\f1c0";
        }
        .fa-file-pdf-o:before {
          content: "\f1c1";
        }
        .fa-file-word-o:before {
          content: "\f1c2";
        }
        .fa-file-excel-o:before {
          content: "\f1c3";
        }
        .fa-file-powerpoint-o:before {
          content: "\f1c4";
        }
        .fa-file-photo-o:before,
        .fa-file-picture-o:before,
        .fa-file-image-o:before {
          content: "\f1c5";
        }
        .fa-file-zip-o:before,
        .fa-file-archive-o:before {
          content: "\f1c6";
        }
        .fa-file-sound-o:before,
        .fa-file-audio-o:before {
          content: "\f1c7";
        }
        .fa-file-movie-o:before,
        .fa-file-video-o:before {
          content: "\f1c8";
        }
        .fa-file-code-o:before {
          content: "\f1c9";
        }
        .fa-vine:before {
          content: "\f1ca";
        }
        .fa-codepen:before {
          content: "\f1cb";
        }
        .fa-jsfiddle:before {
          content: "\f1cc";
        }
        .fa-life-bouy:before,
        .fa-life-buoy:before,
        .fa-life-saver:before,
        .fa-support:before,
        .fa-life-ring:before {
          content: "\f1cd";
        }
        .fa-circle-o-notch:before {
          content: "\f1ce";
        }
        .fa-ra:before,
        .fa-resistance:before,
        .fa-rebel:before {
          content: "\f1d0";
        }
        .fa-ge:before,
        .fa-empire:before {
          content: "\f1d1";
        }
        .fa-git-square:before {
          content: "\f1d2";
        }
        .fa-git:before {
          content: "\f1d3";
        }
        .fa-y-combinator-square:before,
        .fa-yc-square:before,
        .fa-hacker-news:before {
          content: "\f1d4";
        }
        .fa-tencent-weibo:before {
          content: "\f1d5";
        }
        .fa-qq:before {
          content: "\f1d6";
        }
        .fa-wechat:before,
        .fa-weixin:before {
          content: "\f1d7";
        }
        .fa-send:before,
        .fa-paper-plane:before {
          content: "\f1d8";
        }
        .fa-send-o:before,
        .fa-paper-plane-o:before {
          content: "\f1d9";
        }
        .fa-history:before {
          content: "\f1da";
        }
        .fa-circle-thin:before {
          content: "\f1db";
        }
        .fa-header:before {
          content: "\f1dc";
        }
        .fa-paragraph:before {
          content: "\f1dd";
        }
        .fa-sliders:before {
          content: "\f1de";
        }
        .fa-share-alt:before {
          content: "\f1e0";
        }
        .fa-share-alt-square:before {
          content: "\f1e1";
        }
        .fa-bomb:before {
          content: "\f1e2";
        }
        .fa-soccer-ball-o:before,
        .fa-futbol-o:before {
          content: "\f1e3";
        }
        .fa-tty:before {
          content: "\f1e4";
        }
        .fa-binoculars:before {
          content: "\f1e5";
        }
        .fa-plug:before {
          content: "\f1e6";
        }
        .fa-slideshare:before {
          content: "\f1e7";
        }
        .fa-twitch:before {
          content: "\f1e8";
        }
        .fa-yelp:before {
          content: "\f1e9";
        }
        .fa-newspaper-o:before {
          content: "\f1ea";
        }
        .fa-wifi:before {
          content: "\f1eb";
        }
        .fa-calculator:before {
          content: "\f1ec";
        }
        .fa-paypal:before {
          content: "\f1ed";
        }
        .fa-google-wallet:before {
          content: "\f1ee";
        }
        .fa-cc-visa:before {
          content: "\f1f0";
        }
        .fa-cc-mastercard:before {
          content: "\f1f1";
        }
        .fa-cc-discover:before {
          content: "\f1f2";
        }
        .fa-cc-amex:before {
          content: "\f1f3";
        }
        .fa-cc-paypal:before {
          content: "\f1f4";
        }
        .fa-cc-stripe:before {
          content: "\f1f5";
        }
        .fa-bell-slash:before {
          content: "\f1f6";
        }
        .fa-bell-slash-o:before {
          content: "\f1f7";
        }
        .fa-trash:before {
          content: "\f1f8";
        }
        .fa-copyright:before {
          content: "\f1f9";
        }
        .fa-at:before {
          content: "\f1fa";
        }
        .fa-eyedropper:before {
          content: "\f1fb";
        }
        .fa-paint-brush:before {
          content: "\f1fc";
        }
        .fa-birthday-cake:before {
          content: "\f1fd";
        }
        .fa-area-chart:before {
          content: "\f1fe";
        }
        .fa-pie-chart:before {
          content: "\f200";
        }
        .fa-line-chart:before {
          content: "\f201";
        }
        .fa-lastfm:before {
          content: "\f202";
        }
        .fa-lastfm-square:before {
          content: "\f203";
        }
        .fa-toggle-off:before {
          content: "\f204";
        }
        .fa-toggle-on:before {
          content: "\f205";
        }
        .fa-bicycle:before {
          content: "\f206";
        }
        .fa-bus:before {
          content: "\f207";
        }
        .fa-ioxhost:before {
          content: "\f208";
        }
        .fa-angellist:before {
          content: "\f209";
        }
        .fa-cc:before {
          content: "\f20a";
        }
        .fa-shekel:before,
        .fa-sheqel:before,
        .fa-ils:before {
          content: "\f20b";
        }
        .fa-meanpath:before {
          content: "\f20c";
        }
        .fa-buysellads:before {
          content: "\f20d";
        }
        .fa-connectdevelop:before {
          content: "\f20e";
        }
        .fa-dashcube:before {
          content: "\f210";
        }
        .fa-forumbee:before {
          content: "\f211";
        }
        .fa-leanpub:before {
          content: "\f212";
        }
        .fa-sellsy:before {
          content: "\f213";
        }
        .fa-shirtsinbulk:before {
          content: "\f214";
        }
        .fa-simplybuilt:before {
          content: "\f215";
        }
        .fa-skyatlas:before {
          content: "\f216";
        }
        .fa-cart-plus:before {
          content: "\f217";
        }
        .fa-cart-arrow-down:before {
          content: "\f218";
        }
        .fa-diamond:before {
          content: "\f219";
        }
        .fa-ship:before {
          content: "\f21a";
        }
        .fa-user-secret:before {
          content: "\f21b";
        }
        .fa-motorcycle:before {
          content: "\f21c";
        }
        .fa-street-view:before {
          content: "\f21d";
        }
        .fa-heartbeat:before {
          content: "\f21e";
        }
        .fa-venus:before {
          content: "\f221";
        }
        .fa-mars:before {
          content: "\f222";
        }
        .fa-mercury:before {
          content: "\f223";
        }
        .fa-intersex:before,
        .fa-transgender:before {
          content: "\f224";
        }
        .fa-transgender-alt:before {
          content: "\f225";
        }
        .fa-venus-double:before {
          content: "\f226";
        }
        .fa-mars-double:before {
          content: "\f227";
        }
        .fa-venus-mars:before {
          content: "\f228";
        }
        .fa-mars-stroke:before {
          content: "\f229";
        }
        .fa-mars-stroke-v:before {
          content: "\f22a";
        }
        .fa-mars-stroke-h:before {
          content: "\f22b";
        }
        .fa-neuter:before {
          content: "\f22c";
        }
        .fa-genderless:before {
          content: "\f22d";
        }
        .fa-facebook-official:before {
          content: "\f230";
        }
        .fa-pinterest-p:before {
          content: "\f231";
        }
        .fa-whatsapp:before {
          content: "\f232";
        }
        .fa-server:before {
          content: "\f233";
        }
        .fa-user-plus:before {
          content: "\f234";
        }
        .fa-user-times:before {
          content: "\f235";
        }
        .fa-hotel:before,
        .fa-bed:before {
          content: "\f236";
        }
        .fa-viacoin:before {
          content: "\f237";
        }
        .fa-train:before {
          content: "\f238";
        }
        .fa-subway:before {
          content: "\f239";
        }
        .fa-medium:before {
          content: "\f23a";
        }
        .fa-yc:before,
        .fa-y-combinator:before {
          content: "\f23b";
        }
        .fa-optin-monster:before {
          content: "\f23c";
        }
        .fa-opencart:before {
          content: "\f23d";
        }
        .fa-expeditedssl:before {
          content: "\f23e";
        }
        .fa-battery-4:before,
        .fa-battery:before,
        .fa-battery-full:before {
          content: "\f240";
        }
        .fa-battery-3:before,
        .fa-battery-three-quarters:before {
          content: "\f241";
        }
        .fa-battery-2:before,
        .fa-battery-half:before {
          content: "\f242";
        }
        .fa-battery-1:before,
        .fa-battery-quarter:before {
          content: "\f243";
        }
        .fa-battery-0:before,
        .fa-battery-empty:before {
          content: "\f244";
        }
        .fa-mouse-pointer:before {
          content: "\f245";
        }
        .fa-i-cursor:before {
          content: "\f246";
        }
        .fa-object-group:before {
          content: "\f247";
        }
        .fa-object-ungroup:before {
          content: "\f248";
        }
        .fa-sticky-note:before {
          content: "\f249";
        }
        .fa-sticky-note-o:before {
          content: "\f24a";
        }
        .fa-cc-jcb:before {
          content: "\f24b";
        }
        .fa-cc-diners-club:before {
          content: "\f24c";
        }
        .fa-clone:before {
          content: "\f24d";
        }
        .fa-balance-scale:before {
          content: "\f24e";
        }
        .fa-hourglass-o:before {
          content: "\f250";
        }
        .fa-hourglass-1:before,
        .fa-hourglass-start:before {
          content: "\f251";
        }
        .fa-hourglass-2:before,
        .fa-hourglass-half:before {
          content: "\f252";
        }
        .fa-hourglass-3:before,
        .fa-hourglass-end:before {
          content: "\f253";
        }
        .fa-hourglass:before {
          content: "\f254";
        }
        .fa-hand-grab-o:before,
        .fa-hand-rock-o:before {
          content: "\f255";
        }
        .fa-hand-stop-o:before,
        .fa-hand-paper-o:before {
          content: "\f256";
        }
        .fa-hand-scissors-o:before {
          content: "\f257";
        }
        .fa-hand-lizard-o:before {
          content: "\f258";
        }
        .fa-hand-spock-o:before {
          content: "\f259";
        }
        .fa-hand-pointer-o:before {
          content: "\f25a";
        }
        .fa-hand-peace-o:before {
          content: "\f25b";
        }
        .fa-trademark:before {
          content: "\f25c";
        }
        .fa-registered:before {
          content: "\f25d";
        }
        .fa-creative-commons:before {
          content: "\f25e";
        }
        .fa-gg:before {
          content: "\f260";
        }
        .fa-gg-circle:before {
          content: "\f261";
        }
        .fa-tripadvisor:before {
          content: "\f262";
        }
        .fa-odnoklassniki:before {
          content: "\f263";
        }
        .fa-odnoklassniki-square:before {
          content: "\f264";
        }
        .fa-get-pocket:before {
          content: "\f265";
        }
        .fa-wikipedia-w:before {
          content: "\f266";
        }
        .fa-safari:before {
          content: "\f267";
        }
        .fa-chrome:before {
          content: "\f268";
        }
        .fa-firefox:before {
          content: "\f269";
        }
        .fa-opera:before {
          content: "\f26a";
        }
        .fa-internet-explorer:before {
          content: "\f26b";
        }
        .fa-tv:before,
        .fa-television:before {
          content: "\f26c";
        }
        .fa-contao:before {
          content: "\f26d";
        }
        .fa-500px:before {
          content: "\f26e";
        }
        .fa-amazon:before {
          content: "\f270";
        }
        .fa-calendar-plus-o:before {
          content: "\f271";
        }
        .fa-calendar-minus-o:before {
          content: "\f272";
        }
        .fa-calendar-times-o:before {
          content: "\f273";
        }
        .fa-calendar-check-o:before {
          content: "\f274";
        }
        .fa-industry:before {
          content: "\f275";
        }
        .fa-map-pin:before {
          content: "\f276";
        }
        .fa-map-signs:before {
          content: "\f277";
        }
        .fa-map-o:before {
          content: "\f278";
        }
        .fa-map:before {
          content: "\f279";
        }
        .fa-commenting:before {
          content: "\f27a";
        }
        .fa-commenting-o:before {
          content: "\f27b";
        }
        .fa-houzz:before {
          content: "\f27c";
        }
        .fa-vimeo:before {
          content: "\f27d";
        }
        .fa-black-tie:before {
          content: "\f27e";
        }
        .fa-fonticons:before {
          content: "\f280";
        }
        .fa-reddit-alien:before {
          content: "\f281";
        }
        .fa-edge:before {
          content: "\f282";
        }
        .fa-credit-card-alt:before {
          content: "\f283";
        }
        .fa-codiepie:before {
          content: "\f284";
        }
        .fa-modx:before {
          content: "\f285";
        }
        .fa-fort-awesome:before {
          content: "\f286";
        }
        .fa-usb:before {
          content: "\f287";
        }
        .fa-product-hunt:before {
          content: "\f288";
        }
        .fa-mixcloud:before {
          content: "\f289";
        }
        .fa-scribd:before {
          content: "\f28a";
        }
        .fa-pause-circle:before {
          content: "\f28b";
        }
        .fa-pause-circle-o:before {
          content: "\f28c";
        }
        .fa-stop-circle:before {
          content: "\f28d";
        }
        .fa-stop-circle-o:before {
          content: "\f28e";
        }
        .fa-shopping-bag:before {
          content: "\f290";
        }
        .fa-shopping-basket:before {
          content: "\f291";
        }
        .fa-hashtag:before {
          content: "\f292";
        }
        .fa-bluetooth:before {
          content: "\f293";
        }
        .fa-bluetooth-b:before {
          content: "\f294";
        }
        .fa-percent:before {
          content: "\f295";
        }
        .fa-gitlab:before {
          content: "\f296";
        }
        .fa-wpbeginner:before {
          content: "\f297";
        }
        .fa-wpforms:before {
          content: "\f298";
        }
        .fa-envira:before {
          content: "\f299";
        }
        .fa-universal-access:before {
          content: "\f29a";
        }
        .fa-wheelchair-alt:before {
          content: "\f29b";
        }
        .fa-question-circle-o:before {
          content: "\f29c";
        }
        .fa-blind:before {
          content: "\f29d";
        }
        .fa-audio-description:before {
          content: "\f29e";
        }
        .fa-volume-control-phone:before {
          content: "\f2a0";
        }
        .fa-braille:before {
          content: "\f2a1";
        }
        .fa-assistive-listening-systems:before {
          content: "\f2a2";
        }
        .fa-asl-interpreting:before,
        .fa-american-sign-language-interpreting:before {
          content: "\f2a3";
        }
        .fa-deafness:before,
        .fa-hard-of-hearing:before,
        .fa-deaf:before {
          content: "\f2a4";
        }
        .fa-glide:before {
          content: "\f2a5";
        }
        .fa-glide-g:before {
          content: "\f2a6";
        }
        .fa-signing:before,
        .fa-sign-language:before {
          content: "\f2a7";
        }
        .fa-low-vision:before {
          content: "\f2a8";
        }
        .fa-viadeo:before {
          content: "\f2a9";
        }
        .fa-viadeo-square:before {
          content: "\f2aa";
        }
        .fa-snapchat:before {
          content: "\f2ab";
        }
        .fa-snapchat-ghost:before {
          content: "\f2ac";
        }
        .fa-snapchat-square:before {
          content: "\f2ad";
        }
        .fa-pied-piper:before {
          content: "\f2ae";
        }
        .fa-first-order:before {
          content: "\f2b0";
        }
        .fa-yoast:before {
          content: "\f2b1";
        }
        .fa-themeisle:before {
          content: "\f2b2";
        }
        .fa-google-plus-circle:before,
        .fa-google-plus-official:before {
          content: "\f2b3";
        }
        .fa-fa:before,
        .fa-font-awesome:before {
          content: "\f2b4";
        }
        .fa-handshake-o:before {
          content: "\f2b5";
        }
        .fa-envelope-open:before {
          content: "\f2b6";
        }
        .fa-envelope-open-o:before {
          content: "\f2b7";
        }
        .fa-linode:before {
          content: "\f2b8";
        }
        .fa-address-book:before {
          content: "\f2b9";
        }
        .fa-address-book-o:before {
          content: "\f2ba";
        }
        .fa-vcard:before,
        .fa-address-card:before {
          content: "\f2bb";
        }
        .fa-vcard-o:before,
        .fa-address-card-o:before {
          content: "\f2bc";
        }
        .fa-user-circle:before {
          content: "\f2bd";
        }
        .fa-user-circle-o:before {
          content: "\f2be";
        }
        .fa-user-o:before {
          content: "\f2c0";
        }
        .fa-id-badge:before {
          content: "\f2c1";
        }
        .fa-drivers-license:before,
        .fa-id-card:before {
          content: "\f2c2";
        }
        .fa-drivers-license-o:before,
        .fa-id-card-o:before {
          content: "\f2c3";
        }
        .fa-quora:before {
          content: "\f2c4";
        }
        .fa-free-code-camp:before {
          content: "\f2c5";
        }
        .fa-telegram:before {
          content: "\f2c6";
        }
        .fa-thermometer-4:before,
        .fa-thermometer:before,
        .fa-thermometer-full:before {
          content: "\f2c7";
        }
        .fa-thermometer-3:before,
        .fa-thermometer-three-quarters:before {
          content: "\f2c8";
        }
        .fa-thermometer-2:before,
        .fa-thermometer-half:before {
          content: "\f2c9";
        }
        .fa-thermometer-1:before,
        .fa-thermometer-quarter:before {
          content: "\f2ca";
        }
        .fa-thermometer-0:before,
        .fa-thermometer-empty:before {
          content: "\f2cb";
        }
        .fa-shower:before {
          content: "\f2cc";
        }
        .fa-bathtub:before,
        .fa-s15:before,
        .fa-bath:before {
          content: "\f2cd";
        }
        .fa-podcast:before {
          content: "\f2ce";
        }
        .fa-window-maximize:before {
          content: "\f2d0";
        }
        .fa-window-minimize:before {
          content: "\f2d1";
        }
        .fa-window-restore:before {
          content: "\f2d2";
        }
        .fa-times-rectangle:before,
        .fa-window-close:before {
          content: "\f2d3";
        }
        .fa-times-rectangle-o:before,
        .fa-window-close-o:before {
          content: "\f2d4";
        }
        .fa-bandcamp:before {
          content: "\f2d5";
        }
        .fa-grav:before {
          content: "\f2d6";
        }
        .fa-etsy:before {
          content: "\f2d7";
        }
        .fa-imdb:before {
          content: "\f2d8";
        }
        .fa-ravelry:before {
          content: "\f2d9";
        }
        .fa-eercast:before {
          content: "\f2da";
        }
        .fa-microchip:before {
          content: "\f2db";
        }
        .fa-snowflake-o:before {
          content: "\f2dc";
        }
        .fa-superpowers:before {
          content: "\f2dd";
        }
        .fa-wpexplorer:before {
          content: "\f2de";
        }
        .fa-meetup:before {
          content: "\f2e0";
        }
        .sr-only {
          position: absolute;
          width: 1px;
          height: 1px;
          padding: 0;
          margin: -1px;
          overflow: hidden;
          clip: rect(0, 0, 0, 0);
          border: 0;
        }
        .sr-only-focusable:active,
        .sr-only-focusable:focus {
          position: static;
          width: auto;
          height: auto;
          margin: 0;
          overflow: visible;
          clip: auto;
        }


        @font-face {
            font-family: 'FontAwesome';
            src: url('BOOT-INF/classes/static/preMedia/fonts/fontawesome-webfont.eot?v=4.7.0');
            src: url('BOOT-INF/classes/static/preMedia/fonts/fontawesome-webfont.eot?#iefix&v=4.7.0') format('embedded-opentype'), url('BOOT-INF/classes/static/preMedia/fonts/fontawesome-webfont.woff2?v=4.7.0') format('woff2'), url('BOOT-INF/classes/static/preMedia/fonts/fontawesome-webfont.woff?v=4.7.0') format('woff'), url('BOOT-INF/classes/static/preMedia/fonts/fontawesome-webfont.ttf?v=4.7.0') format('truetype'), url('BOOT-INF/classes/static/preMedia/fonts/fontawesome-webfont.svg?v=4.7.0#fontawesomeregular') format('svg');
            font-weight: normal;
            font-style: normal;
        }

        .in-group-base div {
            padding: 5px;
            margin-bottom: 10px;
            margin-right: 10px;
        }
    </style>


    <!-- scroll-js -->
    <script type="text/javascript" >
    /*! jQuery v2.2.4 | (c) jQuery Foundation | jquery.org/license */
    !function(a,b){"object"==typeof module&&"object"==typeof module.exports?module.exports=a.document?b(a,!0):function(a){if(!a.document)throw new Error("jQuery requires a window with a document");return b(a)}:b(a)}("undefined"!=typeof window?window:this,function(a,b){var c=[],d=a.document,e=c.slice,f=c.concat,g=c.push,h=c.indexOf,i={},j=i.toString,k=i.hasOwnProperty,l={},m="2.2.4",n=function(a,b){return new n.fn.init(a,b)},o=/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,p=/^-ms-/,q=/-([\da-z])/gi,r=function(a,b){return b.toUpperCase()};n.fn=n.prototype={jquery:m,constructor:n,selector:"",length:0,toArray:function(){return e.call(this)},get:function(a){return null!=a?0>a?this[a+this.length]:this[a]:e.call(this)},pushStack:function(a){var b=n.merge(this.constructor(),a);return b.prevObject=this,b.context=this.context,b},each:function(a){return n.each(this,a)},map:function(a){return this.pushStack(n.map(this,function(b,c){return a.call(b,c,b)}))},slice:function(){return this.pushStack(e.apply(this,arguments))},first:function(){return this.eq(0)},last:function(){return this.eq(-1)},eq:function(a){var b=this.length,c=+a+(0>a?b:0);return this.pushStack(c>=0&&b>c?[this[c]]:[])},end:function(){return this.prevObject||this.constructor()},push:g,sort:c.sort,splice:c.splice},n.extend=n.fn.extend=function(){var a,b,c,d,e,f,g=arguments[0]||{},h=1,i=arguments.length,j=!1;for("boolean"==typeof g&&(j=g,g=arguments[h]||{},h++),"object"==typeof g||n.isFunction(g)||(g={}),h===i&&(g=this,h--);i>h;h++)if(null!=(a=arguments[h]))for(b in a)c=g[b],d=a[b],g!==d&&(j&&d&&(n.isPlainObject(d)||(e=n.isArray(d)))?(e?(e=!1,f=c&&n.isArray(c)?c:[]):f=c&&n.isPlainObject(c)?c:{},g[b]=n.extend(j,f,d)):void 0!==d&&(g[b]=d));return g},n.extend({expando:"jQuery"+(m+Math.random()).replace(/\D/g,""),isReady:!0,error:function(a){throw new Error(a)},noop:function(){},isFunction:function(a){return"function"===n.type(a)},isArray:Array.isArray,isWindow:function(a){return null!=a&&a===a.window},isNumeric:function(a){var b=a&&a.toString();return!n.isArray(a)&&b-parseFloat(b)+1>=0},isPlainObject:function(a){var b;if("object"!==n.type(a)||a.nodeType||n.isWindow(a))return!1;if(a.constructor&&!k.call(a,"constructor")&&!k.call(a.constructor.prototype||{},"isPrototypeOf"))return!1;for(b in a);return void 0===b||k.call(a,b)},isEmptyObject:function(a){var b;for(b in a)return!1;return!0},type:function(a){return null==a?a+"":"object"==typeof a||"function"==typeof a?i[j.call(a)]||"object":typeof a},globalEval:function(a){var b,c=eval;a=n.trim(a),a&&(1===a.indexOf("use strict")?(b=d.createElement("script"),b.text=a,d.head.appendChild(b).parentNode.removeChild(b)):c(a))},camelCase:function(a){return a.replace(p,"ms-").replace(q,r)},nodeName:function(a,b){return a.nodeName&&a.nodeName.toLowerCase()===b.toLowerCase()},each:function(a,b){var c,d=0;if(s(a)){for(c=a.length;c>d;d++)if(b.call(a[d],d,a[d])===!1)break}else for(d in a)if(b.call(a[d],d,a[d])===!1)break;return a},trim:function(a){return null==a?"":(a+"").replace(o,"")},makeArray:function(a,b){var c=b||[];return null!=a&&(s(Object(a))?n.merge(c,"string"==typeof a?[a]:a):g.call(c,a)),c},inArray:function(a,b,c){return null==b?-1:h.call(b,a,c)},merge:function(a,b){for(var c=+b.length,d=0,e=a.length;c>d;d++)a[e++]=b[d];return a.length=e,a},grep:function(a,b,c){for(var d,e=[],f=0,g=a.length,h=!c;g>f;f++)d=!b(a[f],f),d!==h&&e.push(a[f]);return e},map:function(a,b,c){var d,e,g=0,h=[];if(s(a))for(d=a.length;d>g;g++)e=b(a[g],g,c),null!=e&&h.push(e);else for(g in a)e=b(a[g],g,c),null!=e&&h.push(e);return f.apply([],h)},guid:1,proxy:function(a,b){var c,d,f;return"string"==typeof b&&(c=a[b],b=a,a=c),n.isFunction(a)?(d=e.call(arguments,2),f=function(){return a.apply(b||this,d.concat(e.call(arguments)))},f.guid=a.guid=a.guid||n.guid++,f):void 0},now:Date.now,support:l}),"function"==typeof Symbol&&(n.fn[Symbol.iterator]=c[Symbol.iterator]),n.each("Boolean Number String Function Array Date RegExp Object Error Symbol".split(" "),function(a,b){i["[object "+b+"]"]=b.toLowerCase()});function s(a){var b=!!a&&"length"in a&&a.length,c=n.type(a);return"function"===c||n.isWindow(a)?!1:"array"===c||0===b||"number"==typeof b&&b>0&&b-1 in a}var t=function(a){var b,c,d,e,f,g,h,i,j,k,l,m,n,o,p,q,r,s,t,u="sizzle"+1*new Date,v=a.document,w=0,x=0,y=ga(),z=ga(),A=ga(),B=function(a,b){return a===b&&(l=!0),0},C=1<<31,D={}.hasOwnProperty,E=[],F=E.pop,G=E.push,H=E.push,I=E.slice,J=function(a,b){for(var c=0,d=a.length;d>c;c++)if(a[c]===b)return c;return-1},K="checked|selected|async|autofocus|autoplay|controls|defer|disabled|hidden|ismap|loop|multiple|open|readonly|required|scoped",L="[\\x20\\t\\r\\n\\f]",M="(?:\\\\.|[\\w-]|[^\\x00-\\xa0])+",N="\\["+L+"*("+M+")(?:"+L+"*([*^$|!~]?=)"+L+"*(?:'((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\"|("+M+"))|)"+L+"*\\]",O=":("+M+")(?:\\((('((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\")|((?:\\\\.|[^\\\\()[\\]]|"+N+")*)|.*)\\)|)",P=new RegExp(L+"+","g"),Q=new RegExp("^"+L+"+|((?:^|[^\\\\])(?:\\\\.)*)"+L+"+$","g"),R=new RegExp("^"+L+"*,"+L+"*"),S=new RegExp("^"+L+"*([>+~]|"+L+")"+L+"*"),T=new RegExp("="+L+"*([^\\]'\"]*?)"+L+"*\\]","g"),U=new RegExp(O),V=new RegExp("^"+M+"$"),W={ID:new RegExp("^#("+M+")"),CLASS:new RegExp("^\\.("+M+")"),TAG:new RegExp("^("+M+"|[*])"),ATTR:new RegExp("^"+N),PSEUDO:new RegExp("^"+O),CHILD:new RegExp("^:(only|first|last|nth|nth-last)-(child|of-type)(?:\\("+L+"*(even|odd|(([+-]|)(\\d*)n|)"+L+"*(?:([+-]|)"+L+"*(\\d+)|))"+L+"*\\)|)","i"),bool:new RegExp("^(?:"+K+")$","i"),needsContext:new RegExp("^"+L+"*[>+~]|:(even|odd|eq|gt|lt|nth|first|last)(?:\\("+L+"*((?:-\\d)?\\d*)"+L+"*\\)|)(?=[^-]|$)","i")},X=/^(?:input|select|textarea|button)$/i,Y=/^h\d$/i,Z=/^[^{]+\{\s*\[native \w/,$=/^(?:#([\w-]+)|(\w+)|\.([\w-]+))$/,_=/[+~]/,aa=/'|\\/g,ba=new RegExp("\\\\([\\da-f]{1,6}"+L+"?|("+L+")|.)","ig"),ca=function(a,b,c){var d="0x"+b-65536;return d!==d||c?b:0>d?String.fromCharCode(d+65536):String.fromCharCode(d>>10|55296,1023&d|56320)},da=function(){m()};try{H.apply(E=I.call(v.childNodes),v.childNodes),E[v.childNodes.length].nodeType}catch(ea){H={apply:E.length?function(a,b){G.apply(a,I.call(b))}:function(a,b){var c=a.length,d=0;while(a[c++]=b[d++]);a.length=c-1}}}function fa(a,b,d,e){var f,h,j,k,l,o,r,s,w=b&&b.ownerDocument,x=b?b.nodeType:9;if(d=d||[],"string"!=typeof a||!a||1!==x&&9!==x&&11!==x)return d;if(!e&&((b?b.ownerDocument||b:v)!==n&&m(b),b=b||n,p)){if(11!==x&&(o=$.exec(a)))if(f=o[1]){if(9===x){if(!(j=b.getElementById(f)))return d;if(j.id===f)return d.push(j),d}else if(w&&(j=w.getElementById(f))&&t(b,j)&&j.id===f)return d.push(j),d}else{if(o[2])return H.apply(d,b.getElementsByTagName(a)),d;if((f=o[3])&&c.getElementsByClassName&&b.getElementsByClassName)return H.apply(d,b.getElementsByClassName(f)),d}if(c.qsa&&!A[a+" "]&&(!q||!q.test(a))){if(1!==x)w=b,s=a;else if("object"!==b.nodeName.toLowerCase()){(k=b.getAttribute("id"))?k=k.replace(aa,"\\$&"):b.setAttribute("id",k=u),r=g(a),h=r.length,l=V.test(k)?"#"+k:"[id='"+k+"']";while(h--)r[h]=l+" "+qa(r[h]);s=r.join(","),w=_.test(a)&&oa(b.parentNode)||b}if(s)try{return H.apply(d,w.querySelectorAll(s)),d}catch(y){}finally{k===u&&b.removeAttribute("id")}}}return i(a.replace(Q,"$1"),b,d,e)}function ga(){var a=[];function b(c,e){return a.push(c+" ")>d.cacheLength&&delete b[a.shift()],b[c+" "]=e}return b}function ha(a){return a[u]=!0,a}function ia(a){var b=n.createElement("div");try{return!!a(b)}catch(c){return!1}finally{b.parentNode&&b.parentNode.removeChild(b),b=null}}function ja(a,b){var c=a.split("|"),e=c.length;while(e--)d.attrHandle[c[e]]=b}function ka(a,b){var c=b&&a,d=c&&1===a.nodeType&&1===b.nodeType&&(~b.sourceIndex||C)-(~a.sourceIndex||C);if(d)return d;if(c)while(c=c.nextSibling)if(c===b)return-1;return a?1:-1}function la(a){return function(b){var c=b.nodeName.toLowerCase();return"input"===c&&b.type===a}}function ma(a){return function(b){var c=b.nodeName.toLowerCase();return("input"===c||"button"===c)&&b.type===a}}function na(a){return ha(function(b){return b=+b,ha(function(c,d){var e,f=a([],c.length,b),g=f.length;while(g--)c[e=f[g]]&&(c[e]=!(d[e]=c[e]))})})}function oa(a){return a&&"undefined"!=typeof a.getElementsByTagName&&a}c=fa.support={},f=fa.isXML=function(a){var b=a&&(a.ownerDocument||a).documentElement;return b?"HTML"!==b.nodeName:!1},m=fa.setDocument=function(a){var b,e,g=a?a.ownerDocument||a:v;return g!==n&&9===g.nodeType&&g.documentElement?(n=g,o=n.documentElement,p=!f(n),(e=n.defaultView)&&e.top!==e&&(e.addEventListener?e.addEventListener("unload",da,!1):e.attachEvent&&e.attachEvent("onunload",da)),c.attributes=ia(function(a){return a.className="i",!a.getAttribute("className")}),c.getElementsByTagName=ia(function(a){return a.appendChild(n.createComment("")),!a.getElementsByTagName("*").length}),c.getElementsByClassName=Z.test(n.getElementsByClassName),c.getById=ia(function(a){return o.appendChild(a).id=u,!n.getElementsByName||!n.getElementsByName(u).length}),c.getById?(d.find.ID=function(a,b){if("undefined"!=typeof b.getElementById&&p){var c=b.getElementById(a);return c?[c]:[]}},d.filter.ID=function(a){var b=a.replace(ba,ca);return function(a){return a.getAttribute("id")===b}}):(delete d.find.ID,d.filter.ID=function(a){var b=a.replace(ba,ca);return function(a){var c="undefined"!=typeof a.getAttributeNode&&a.getAttributeNode("id");return c&&c.value===b}}),d.find.TAG=c.getElementsByTagName?function(a,b){return"undefined"!=typeof b.getElementsByTagName?b.getElementsByTagName(a):c.qsa?b.querySelectorAll(a):void 0}:function(a,b){var c,d=[],e=0,f=b.getElementsByTagName(a);if("*"===a){while(c=f[e++])1===c.nodeType&&d.push(c);return d}return f},d.find.CLASS=c.getElementsByClassName&&function(a,b){return"undefined"!=typeof b.getElementsByClassName&&p?b.getElementsByClassName(a):void 0},r=[],q=[],(c.qsa=Z.test(n.querySelectorAll))&&(ia(function(a){o.appendChild(a).innerHTML="<a id='"+u+"'></a><select id='"+u+"-\r\\' msallowcapture=''><option selected=''></option></select>",a.querySelectorAll("[msallowcapture^='']").length&&q.push("[*^$]="+L+"*(?:''|\"\")"),a.querySelectorAll("[selected]").length||q.push("\\["+L+"*(?:value|"+K+")"),a.querySelectorAll("[id~="+u+"-]").length||q.push("~="),a.querySelectorAll(":checked").length||q.push(":checked"),a.querySelectorAll("a#"+u+"+*").length||q.push(".#.+[+~]")}),ia(function(a){var b=n.createElement("input");b.setAttribute("type","hidden"),a.appendChild(b).setAttribute("name","D"),a.querySelectorAll("[name=d]").length&&q.push("name"+L+"*[*^$|!~]?="),a.querySelectorAll(":enabled").length||q.push(":enabled",":disabled"),a.querySelectorAll("*,:x"),q.push(",.*:")})),(c.matchesSelector=Z.test(s=o.matches||o.webkitMatchesSelector||o.mozMatchesSelector||o.oMatchesSelector||o.msMatchesSelector))&&ia(function(a){c.disconnectedMatch=s.call(a,"div"),s.call(a,"[s!='']:x"),r.push("!=",O)}),q=q.length&&new RegExp(q.join("|")),r=r.length&&new RegExp(r.join("|")),b=Z.test(o.compareDocumentPosition),t=b||Z.test(o.contains)?function(a,b){var c=9===a.nodeType?a.documentElement:a,d=b&&b.parentNode;return a===d||!(!d||1!==d.nodeType||!(c.contains?c.contains(d):a.compareDocumentPosition&&16&a.compareDocumentPosition(d)))}:function(a,b){if(b)while(b=b.parentNode)if(b===a)return!0;return!1},B=b?function(a,b){if(a===b)return l=!0,0;var d=!a.compareDocumentPosition-!b.compareDocumentPosition;return d?d:(d=(a.ownerDocument||a)===(b.ownerDocument||b)?a.compareDocumentPosition(b):1,1&d||!c.sortDetached&&b.compareDocumentPosition(a)===d?a===n||a.ownerDocument===v&&t(v,a)?-1:b===n||b.ownerDocument===v&&t(v,b)?1:k?J(k,a)-J(k,b):0:4&d?-1:1)}:function(a,b){if(a===b)return l=!0,0;var c,d=0,e=a.parentNode,f=b.parentNode,g=[a],h=[b];if(!e||!f)return a===n?-1:b===n?1:e?-1:f?1:k?J(k,a)-J(k,b):0;if(e===f)return ka(a,b);c=a;while(c=c.parentNode)g.unshift(c);c=b;while(c=c.parentNode)h.unshift(c);while(g[d]===h[d])d++;return d?ka(g[d],h[d]):g[d]===v?-1:h[d]===v?1:0},n):n},fa.matches=function(a,b){return fa(a,null,null,b)},fa.matchesSelector=function(a,b){if((a.ownerDocument||a)!==n&&m(a),b=b.replace(T,"='$1']"),c.matchesSelector&&p&&!A[b+" "]&&(!r||!r.test(b))&&(!q||!q.test(b)))try{var d=s.call(a,b);if(d||c.disconnectedMatch||a.document&&11!==a.document.nodeType)return d}catch(e){}return fa(b,n,null,[a]).length>0},fa.contains=function(a,b){return(a.ownerDocument||a)!==n&&m(a),t(a,b)},fa.attr=function(a,b){(a.ownerDocument||a)!==n&&m(a);var e=d.attrHandle[b.toLowerCase()],f=e&&D.call(d.attrHandle,b.toLowerCase())?e(a,b,!p):void 0;return void 0!==f?f:c.attributes||!p?a.getAttribute(b):(f=a.getAttributeNode(b))&&f.specified?f.value:null},fa.error=function(a){throw new Error("Syntax error, unrecognized expression: "+a)},fa.uniqueSort=function(a){var b,d=[],e=0,f=0;if(l=!c.detectDuplicates,k=!c.sortStable&&a.slice(0),a.sort(B),l){while(b=a[f++])b===a[f]&&(e=d.push(f));while(e--)a.splice(d[e],1)}return k=null,a},e=fa.getText=function(a){var b,c="",d=0,f=a.nodeType;if(f){if(1===f||9===f||11===f){if("string"==typeof a.textContent)return a.textContent;for(a=a.firstChild;a;a=a.nextSibling)c+=e(a)}else if(3===f||4===f)return a.nodeValue}else while(b=a[d++])c+=e(b);return c},d=fa.selectors={cacheLength:50,createPseudo:ha,match:W,attrHandle:{},find:{},relative:{">":{dir:"parentNode",first:!0}," ":{dir:"parentNode"},"+":{dir:"previousSibling",first:!0},"~":{dir:"previousSibling"}},preFilter:{ATTR:function(a){return a[1]=a[1].replace(ba,ca),a[3]=(a[3]||a[4]||a[5]||"").replace(ba,ca),"~="===a[2]&&(a[3]=" "+a[3]+" "),a.slice(0,4)},CHILD:function(a){return a[1]=a[1].toLowerCase(),"nth"===a[1].slice(0,3)?(a[3]||fa.error(a[0]),a[4]=+(a[4]?a[5]+(a[6]||1):2*("even"===a[3]||"odd"===a[3])),a[5]=+(a[7]+a[8]||"odd"===a[3])):a[3]&&fa.error(a[0]),a},PSEUDO:function(a){var b,c=!a[6]&&a[2];return W.CHILD.test(a[0])?null:(a[3]?a[2]=a[4]||a[5]||"":c&&U.test(c)&&(b=g(c,!0))&&(b=c.indexOf(")",c.length-b)-c.length)&&(a[0]=a[0].slice(0,b),a[2]=c.slice(0,b)),a.slice(0,3))}},filter:{TAG:function(a){var b=a.replace(ba,ca).toLowerCase();return"*"===a?function(){return!0}:function(a){return a.nodeName&&a.nodeName.toLowerCase()===b}},CLASS:function(a){var b=y[a+" "];return b||(b=new RegExp("(^|"+L+")"+a+"("+L+"|$)"))&&y(a,function(a){return b.test("string"==typeof a.className&&a.className||"undefined"!=typeof a.getAttribute&&a.getAttribute("class")||"")})},ATTR:function(a,b,c){return function(d){var e=fa.attr(d,a);return null==e?"!="===b:b?(e+="","="===b?e===c:"!="===b?e!==c:"^="===b?c&&0===e.indexOf(c):"*="===b?c&&e.indexOf(c)>-1:"$="===b?c&&e.slice(-c.length)===c:"~="===b?(" "+e.replace(P," ")+" ").indexOf(c)>-1:"|="===b?e===c||e.slice(0,c.length+1)===c+"-":!1):!0}},CHILD:function(a,b,c,d,e){var f="nth"!==a.slice(0,3),g="last"!==a.slice(-4),h="of-type"===b;return 1===d&&0===e?function(a){return!!a.parentNode}:function(b,c,i){var j,k,l,m,n,o,p=f!==g?"nextSibling":"previousSibling",q=b.parentNode,r=h&&b.nodeName.toLowerCase(),s=!i&&!h,t=!1;if(q){if(f){while(p){m=b;while(m=m[p])if(h?m.nodeName.toLowerCase()===r:1===m.nodeType)return!1;o=p="only"===a&&!o&&"nextSibling"}return!0}if(o=[g?q.firstChild:q.lastChild],g&&s){m=q,l=m[u]||(m[u]={}),k=l[m.uniqueID]||(l[m.uniqueID]={}),j=k[a]||[],n=j[0]===w&&j[1],t=n&&j[2],m=n&&q.childNodes[n];while(m=++n&&m&&m[p]||(t=n=0)||o.pop())if(1===m.nodeType&&++t&&m===b){k[a]=[w,n,t];break}}else if(s&&(m=b,l=m[u]||(m[u]={}),k=l[m.uniqueID]||(l[m.uniqueID]={}),j=k[a]||[],n=j[0]===w&&j[1],t=n),t===!1)while(m=++n&&m&&m[p]||(t=n=0)||o.pop())if((h?m.nodeName.toLowerCase()===r:1===m.nodeType)&&++t&&(s&&(l=m[u]||(m[u]={}),k=l[m.uniqueID]||(l[m.uniqueID]={}),k[a]=[w,t]),m===b))break;return t-=e,t===d||t%d===0&&t/d>=0}}},PSEUDO:function(a,b){var c,e=d.pseudos[a]||d.setFilters[a.toLowerCase()]||fa.error("unsupported pseudo: "+a);return e[u]?e(b):e.length>1?(c=[a,a,"",b],d.setFilters.hasOwnProperty(a.toLowerCase())?ha(function(a,c){var d,f=e(a,b),g=f.length;while(g--)d=J(a,f[g]),a[d]=!(c[d]=f[g])}):function(a){return e(a,0,c)}):e}},pseudos:{not:ha(function(a){var b=[],c=[],d=h(a.replace(Q,"$1"));return d[u]?ha(function(a,b,c,e){var f,g=d(a,null,e,[]),h=a.length;while(h--)(f=g[h])&&(a[h]=!(b[h]=f))}):function(a,e,f){return b[0]=a,d(b,null,f,c),b[0]=null,!c.pop()}}),has:ha(function(a){return function(b){return fa(a,b).length>0}}),contains:ha(function(a){return a=a.replace(ba,ca),function(b){return(b.textContent||b.innerText||e(b)).indexOf(a)>-1}}),lang:ha(function(a){return V.test(a||"")||fa.error("unsupported lang: "+a),a=a.replace(ba,ca).toLowerCase(),function(b){var c;do if(c=p?b.lang:b.getAttribute("xml:lang")||b.getAttribute("lang"))return c=c.toLowerCase(),c===a||0===c.indexOf(a+"-");while((b=b.parentNode)&&1===b.nodeType);return!1}}),target:function(b){var c=a.location&&a.location.hash;return c&&c.slice(1)===b.id},root:function(a){return a===o},focus:function(a){return a===n.activeElement&&(!n.hasFocus||n.hasFocus())&&!!(a.type||a.href||~a.tabIndex)},enabled:function(a){return a.disabled===!1},disabled:function(a){return a.disabled===!0},checked:function(a){var b=a.nodeName.toLowerCase();return"input"===b&&!!a.checked||"option"===b&&!!a.selected},selected:function(a){return a.parentNode&&a.parentNode.selectedIndex,a.selected===!0},empty:function(a){for(a=a.firstChild;a;a=a.nextSibling)if(a.nodeType<6)return!1;return!0},parent:function(a){return!d.pseudos.empty(a)},header:function(a){return Y.test(a.nodeName)},input:function(a){return X.test(a.nodeName)},button:function(a){var b=a.nodeName.toLowerCase();return"input"===b&&"button"===a.type||"button"===b},text:function(a){var b;return"input"===a.nodeName.toLowerCase()&&"text"===a.type&&(null==(b=a.getAttribute("type"))||"text"===b.toLowerCase())},first:na(function(){return[0]}),last:na(function(a,b){return[b-1]}),eq:na(function(a,b,c){return[0>c?c+b:c]}),even:na(function(a,b){for(var c=0;b>c;c+=2)a.push(c);return a}),odd:na(function(a,b){for(var c=1;b>c;c+=2)a.push(c);return a}),lt:na(function(a,b,c){for(var d=0>c?c+b:c;--d>=0;)a.push(d);return a}),gt:na(function(a,b,c){for(var d=0>c?c+b:c;++d<b;)a.push(d);return a})}},d.pseudos.nth=d.pseudos.eq;for(b in{radio:!0,checkbox:!0,file:!0,password:!0,image:!0})d.pseudos[b]=la(b);for(b in{submit:!0,reset:!0})d.pseudos[b]=ma(b);function pa(){}pa.prototype=d.filters=d.pseudos,d.setFilters=new pa,g=fa.tokenize=function(a,b){var c,e,f,g,h,i,j,k=z[a+" "];if(k)return b?0:k.slice(0);h=a,i=[],j=d.preFilter;while(h){c&&!(e=R.exec(h))||(e&&(h=h.slice(e[0].length)||h),i.push(f=[])),c=!1,(e=S.exec(h))&&(c=e.shift(),f.push({value:c,type:e[0].replace(Q," ")}),h=h.slice(c.length));for(g in d.filter)!(e=W[g].exec(h))||j[g]&&!(e=j[g](e))||(c=e.shift(),f.push({value:c,type:g,matches:e}),h=h.slice(c.length));if(!c)break}return b?h.length:h?fa.error(a):z(a,i).slice(0)};function qa(a){for(var b=0,c=a.length,d="";c>b;b++)d+=a[b].value;return d}function ra(a,b,c){var d=b.dir,e=c&&"parentNode"===d,f=x++;return b.first?function(b,c,f){while(b=b[d])if(1===b.nodeType||e)return a(b,c,f)}:function(b,c,g){var h,i,j,k=[w,f];if(g){while(b=b[d])if((1===b.nodeType||e)&&a(b,c,g))return!0}else while(b=b[d])if(1===b.nodeType||e){if(j=b[u]||(b[u]={}),i=j[b.uniqueID]||(j[b.uniqueID]={}),(h=i[d])&&h[0]===w&&h[1]===f)return k[2]=h[2];if(i[d]=k,k[2]=a(b,c,g))return!0}}}function sa(a){return a.length>1?function(b,c,d){var e=a.length;while(e--)if(!a[e](b,c,d))return!1;return!0}:a[0]}function ta(a,b,c){for(var d=0,e=b.length;e>d;d++)fa(a,b[d],c);return c}function ua(a,b,c,d,e){for(var f,g=[],h=0,i=a.length,j=null!=b;i>h;h++)(f=a[h])&&(c&&!c(f,d,e)||(g.push(f),j&&b.push(h)));return g}function va(a,b,c,d,e,f){return d&&!d[u]&&(d=va(d)),e&&!e[u]&&(e=va(e,f)),ha(function(f,g,h,i){var j,k,l,m=[],n=[],o=g.length,p=f||ta(b||"*",h.nodeType?[h]:h,[]),q=!a||!f&&b?p:ua(p,m,a,h,i),r=c?e||(f?a:o||d)?[]:g:q;if(c&&c(q,r,h,i),d){j=ua(r,n),d(j,[],h,i),k=j.length;while(k--)(l=j[k])&&(r[n[k]]=!(q[n[k]]=l))}if(f){if(e||a){if(e){j=[],k=r.length;while(k--)(l=r[k])&&j.push(q[k]=l);e(null,r=[],j,i)}k=r.length;while(k--)(l=r[k])&&(j=e?J(f,l):m[k])>-1&&(f[j]=!(g[j]=l))}}else r=ua(r===g?r.splice(o,r.length):r),e?e(null,g,r,i):H.apply(g,r)})}function wa(a){for(var b,c,e,f=a.length,g=d.relative[a[0].type],h=g||d.relative[" "],i=g?1:0,k=ra(function(a){return a===b},h,!0),l=ra(function(a){return J(b,a)>-1},h,!0),m=[function(a,c,d){var e=!g&&(d||c!==j)||((b=c).nodeType?k(a,c,d):l(a,c,d));return b=null,e}];f>i;i++)if(c=d.relative[a[i].type])m=[ra(sa(m),c)];else{if(c=d.filter[a[i].type].apply(null,a[i].matches),c[u]){for(e=++i;f>e;e++)if(d.relative[a[e].type])break;return va(i>1&&sa(m),i>1&&qa(a.slice(0,i-1).concat({value:" "===a[i-2].type?"*":""})).replace(Q,"$1"),c,e>i&&wa(a.slice(i,e)),f>e&&wa(a=a.slice(e)),f>e&&qa(a))}m.push(c)}return sa(m)}function xa(a,b){var c=b.length>0,e=a.length>0,f=function(f,g,h,i,k){var l,o,q,r=0,s="0",t=f&&[],u=[],v=j,x=f||e&&d.find.TAG("*",k),y=w+=null==v?1:Math.random()||.1,z=x.length;for(k&&(j=g===n||g||k);s!==z&&null!=(l=x[s]);s++){if(e&&l){o=0,g||l.ownerDocument===n||(m(l),h=!p);while(q=a[o++])if(q(l,g||n,h)){i.push(l);break}k&&(w=y)}c&&((l=!q&&l)&&r--,f&&t.push(l))}if(r+=s,c&&s!==r){o=0;while(q=b[o++])q(t,u,g,h);if(f){if(r>0)while(s--)t[s]||u[s]||(u[s]=F.call(i));u=ua(u)}H.apply(i,u),k&&!f&&u.length>0&&r+b.length>1&&fa.uniqueSort(i)}return k&&(w=y,j=v),t};return c?ha(f):f}return h=fa.compile=function(a,b){var c,d=[],e=[],f=A[a+" "];if(!f){b||(b=g(a)),c=b.length;while(c--)f=wa(b[c]),f[u]?d.push(f):e.push(f);f=A(a,xa(e,d)),f.selector=a}return f},i=fa.select=function(a,b,e,f){var i,j,k,l,m,n="function"==typeof a&&a,o=!f&&g(a=n.selector||a);if(e=e||[],1===o.length){if(j=o[0]=o[0].slice(0),j.length>2&&"ID"===(k=j[0]).type&&c.getById&&9===b.nodeType&&p&&d.relative[j[1].type]){if(b=(d.find.ID(k.matches[0].replace(ba,ca),b)||[])[0],!b)return e;n&&(b=b.parentNode),a=a.slice(j.shift().value.length)}i=W.needsContext.test(a)?0:j.length;while(i--){if(k=j[i],d.relative[l=k.type])break;if((m=d.find[l])&&(f=m(k.matches[0].replace(ba,ca),_.test(j[0].type)&&oa(b.parentNode)||b))){if(j.splice(i,1),a=f.length&&qa(j),!a)return H.apply(e,f),e;break}}}return(n||h(a,o))(f,b,!p,e,!b||_.test(a)&&oa(b.parentNode)||b),e},c.sortStable=u.split("").sort(B).join("")===u,c.detectDuplicates=!!l,m(),c.sortDetached=ia(function(a){return 1&a.compareDocumentPosition(n.createElement("div"))}),ia(function(a){return a.innerHTML="<a href='#'></a>","#"===a.firstChild.getAttribute("href")})||ja("type|href|height|width",function(a,b,c){return c?void 0:a.getAttribute(b,"type"===b.toLowerCase()?1:2)}),c.attributes&&ia(function(a){return a.innerHTML="<input/>",a.firstChild.setAttribute("value",""),""===a.firstChild.getAttribute("value")})||ja("value",function(a,b,c){return c||"input"!==a.nodeName.toLowerCase()?void 0:a.defaultValue}),ia(function(a){return null==a.getAttribute("disabled")})||ja(K,function(a,b,c){var d;return c?void 0:a[b]===!0?b.toLowerCase():(d=a.getAttributeNode(b))&&d.specified?d.value:null}),fa}(a);n.find=t,n.expr=t.selectors,n.expr[":"]=n.expr.pseudos,n.uniqueSort=n.unique=t.uniqueSort,n.text=t.getText,n.isXMLDoc=t.isXML,n.contains=t.contains;var u=function(a,b,c){var d=[],e=void 0!==c;while((a=a[b])&&9!==a.nodeType)if(1===a.nodeType){if(e&&n(a).is(c))break;d.push(a)}return d},v=function(a,b){for(var c=[];a;a=a.nextSibling)1===a.nodeType&&a!==b&&c.push(a);return c},w=n.expr.match.needsContext,x=/^<([\w-]+)\s*\/?>(?:<\/\1>|)$/,y=/^.[^:#\[\.,]*$/;function z(a,b,c){if(n.isFunction(b))return n.grep(a,function(a,d){return!!b.call(a,d,a)!==c});if(b.nodeType)return n.grep(a,function(a){return a===b!==c});if("string"==typeof b){if(y.test(b))return n.filter(b,a,c);b=n.filter(b,a)}return n.grep(a,function(a){return h.call(b,a)>-1!==c})}n.filter=function(a,b,c){var d=b[0];return c&&(a=":not("+a+")"),1===b.length&&1===d.nodeType?n.find.matchesSelector(d,a)?[d]:[]:n.find.matches(a,n.grep(b,function(a){return 1===a.nodeType}))},n.fn.extend({find:function(a){var b,c=this.length,d=[],e=this;if("string"!=typeof a)return this.pushStack(n(a).filter(function(){for(b=0;c>b;b++)if(n.contains(e[b],this))return!0}));for(b=0;c>b;b++)n.find(a,e[b],d);return d=this.pushStack(c>1?n.unique(d):d),d.selector=this.selector?this.selector+" "+a:a,d},filter:function(a){return this.pushStack(z(this,a||[],!1))},not:function(a){return this.pushStack(z(this,a||[],!0))},is:function(a){return!!z(this,"string"==typeof a&&w.test(a)?n(a):a||[],!1).length}});var A,B=/^(?:\s*(<[\w\W]+>)[^>]*|#([\w-]*))$/,C=n.fn.init=function(a,b,c){var e,f;if(!a)return this;if(c=c||A,"string"==typeof a){if(e="<"===a[0]&&">"===a[a.length-1]&&a.length>=3?[null,a,null]:B.exec(a),!e||!e[1]&&b)return!b||b.jquery?(b||c).find(a):this.constructor(b).find(a);if(e[1]){if(b=b instanceof n?b[0]:b,n.merge(this,n.parseHTML(e[1],b&&b.nodeType?b.ownerDocument||b:d,!0)),x.test(e[1])&&n.isPlainObject(b))for(e in b)n.isFunction(this[e])?this[e](b[e]):this.attr(e,b[e]);return this}return f=d.getElementById(e[2]),f&&f.parentNode&&(this.length=1,this[0]=f),this.context=d,this.selector=a,this}return a.nodeType?(this.context=this[0]=a,this.length=1,this):n.isFunction(a)?void 0!==c.ready?c.ready(a):a(n):(void 0!==a.selector&&(this.selector=a.selector,this.context=a.context),n.makeArray(a,this))};C.prototype=n.fn,A=n(d);var D=/^(?:parents|prev(?:Until|All))/,E={children:!0,contents:!0,next:!0,prev:!0};n.fn.extend({has:function(a){var b=n(a,this),c=b.length;return this.filter(function(){for(var a=0;c>a;a++)if(n.contains(this,b[a]))return!0})},closest:function(a,b){for(var c,d=0,e=this.length,f=[],g=w.test(a)||"string"!=typeof a?n(a,b||this.context):0;e>d;d++)for(c=this[d];c&&c!==b;c=c.parentNode)if(c.nodeType<11&&(g?g.index(c)>-1:1===c.nodeType&&n.find.matchesSelector(c,a))){f.push(c);break}return this.pushStack(f.length>1?n.uniqueSort(f):f)},index:function(a){return a?"string"==typeof a?h.call(n(a),this[0]):h.call(this,a.jquery?a[0]:a):this[0]&&this[0].parentNode?this.first().prevAll().length:-1},add:function(a,b){return this.pushStack(n.uniqueSort(n.merge(this.get(),n(a,b))))},addBack:function(a){return this.add(null==a?this.prevObject:this.prevObject.filter(a))}});function F(a,b){while((a=a[b])&&1!==a.nodeType);return a}n.each({parent:function(a){var b=a.parentNode;return b&&11!==b.nodeType?b:null},parents:function(a){return u(a,"parentNode")},parentsUntil:function(a,b,c){return u(a,"parentNode",c)},next:function(a){return F(a,"nextSibling")},prev:function(a){return F(a,"previousSibling")},nextAll:function(a){return u(a,"nextSibling")},prevAll:function(a){return u(a,"previousSibling")},nextUntil:function(a,b,c){return u(a,"nextSibling",c)},prevUntil:function(a,b,c){return u(a,"previousSibling",c)},siblings:function(a){return v((a.parentNode||{}).firstChild,a)},children:function(a){return v(a.firstChild)},contents:function(a){return a.contentDocument||n.merge([],a.childNodes)}},function(a,b){n.fn[a]=function(c,d){var e=n.map(this,b,c);return"Until"!==a.slice(-5)&&(d=c),d&&"string"==typeof d&&(e=n.filter(d,e)),this.length>1&&(E[a]||n.uniqueSort(e),D.test(a)&&e.reverse()),this.pushStack(e)}});var G=/\S+/g;function H(a){var b={};return n.each(a.match(G)||[],function(a,c){b[c]=!0}),b}n.Callbacks=function(a){a="string"==typeof a?H(a):n.extend({},a);var b,c,d,e,f=[],g=[],h=-1,i=function(){for(e=a.once,d=b=!0;g.length;h=-1){c=g.shift();while(++h<f.length)f[h].apply(c[0],c[1])===!1&&a.stopOnFalse&&(h=f.length,c=!1)}a.memory||(c=!1),b=!1,e&&(f=c?[]:"")},j={add:function(){return f&&(c&&!b&&(h=f.length-1,g.push(c)),function d(b){n.each(b,function(b,c){n.isFunction(c)?a.unique&&j.has(c)||f.push(c):c&&c.length&&"string"!==n.type(c)&&d(c)})}(arguments),c&&!b&&i()),this},remove:function(){return n.each(arguments,function(a,b){var c;while((c=n.inArray(b,f,c))>-1)f.splice(c,1),h>=c&&h--}),this},has:function(a){return a?n.inArray(a,f)>-1:f.length>0},empty:function(){return f&&(f=[]),this},disable:function(){return e=g=[],f=c="",this},disabled:function(){return!f},lock:function(){return e=g=[],c||(f=c=""),this},locked:function(){return!!e},fireWith:function(a,c){return e||(c=c||[],c=[a,c.slice?c.slice():c],g.push(c),b||i()),this},fire:function(){return j.fireWith(this,arguments),this},fired:function(){return!!d}};return j},n.extend({Deferred:function(a){var b=[["resolve","done",n.Callbacks("once memory"),"resolved"],["reject","fail",n.Callbacks("once memory"),"rejected"],["notify","progress",n.Callbacks("memory")]],c="pending",d={state:function(){return c},always:function(){return e.done(arguments).fail(arguments),this},then:function(){var a=arguments;return n.Deferred(function(c){n.each(b,function(b,f){var g=n.isFunction(a[b])&&a[b];e[f[1]](function(){var a=g&&g.apply(this,arguments);a&&n.isFunction(a.promise)?a.promise().progress(c.notify).done(c.resolve).fail(c.reject):c[f[0]+"With"](this===d?c.promise():this,g?[a]:arguments)})}),a=null}).promise()},promise:function(a){return null!=a?n.extend(a,d):d}},e={};return d.pipe=d.then,n.each(b,function(a,f){var g=f[2],h=f[3];d[f[1]]=g.add,h&&g.add(function(){c=h},b[1^a][2].disable,b[2][2].lock),e[f[0]]=function(){return e[f[0]+"With"](this===e?d:this,arguments),this},e[f[0]+"With"]=g.fireWith}),d.promise(e),a&&a.call(e,e),e},when:function(a){var b=0,c=e.call(arguments),d=c.length,f=1!==d||a&&n.isFunction(a.promise)?d:0,g=1===f?a:n.Deferred(),h=function(a,b,c){return function(d){b[a]=this,c[a]=arguments.length>1?e.call(arguments):d,c===i?g.notifyWith(b,c):--f||g.resolveWith(b,c)}},i,j,k;if(d>1)for(i=new Array(d),j=new Array(d),k=new Array(d);d>b;b++)c[b]&&n.isFunction(c[b].promise)?c[b].promise().progress(h(b,j,i)).done(h(b,k,c)).fail(g.reject):--f;return f||g.resolveWith(k,c),g.promise()}});var I;n.fn.ready=function(a){return n.ready.promise().done(a),this},n.extend({isReady:!1,readyWait:1,holdReady:function(a){a?n.readyWait++:n.ready(!0)},ready:function(a){(a===!0?--n.readyWait:n.isReady)||(n.isReady=!0,a!==!0&&--n.readyWait>0||(I.resolveWith(d,[n]),n.fn.triggerHandler&&(n(d).triggerHandler("ready"),n(d).off("ready"))))}});function J(){d.removeEventListener("DOMContentLoaded",J),a.removeEventListener("load",J),n.ready()}n.ready.promise=function(b){return I||(I=n.Deferred(),"complete"===d.readyState||"loading"!==d.readyState&&!d.documentElement.doScroll?a.setTimeout(n.ready):(d.addEventListener("DOMContentLoaded",J),a.addEventListener("load",J))),I.promise(b)},n.ready.promise();var K=function(a,b,c,d,e,f,g){var h=0,i=a.length,j=null==c;if("object"===n.type(c)){e=!0;for(h in c)K(a,b,h,c[h],!0,f,g)}else if(void 0!==d&&(e=!0,n.isFunction(d)||(g=!0),j&&(g?(b.call(a,d),b=null):(j=b,b=function(a,b,c){return j.call(n(a),c)})),b))for(;i>h;h++)b(a[h],c,g?d:d.call(a[h],h,b(a[h],c)));return e?a:j?b.call(a):i?b(a[0],c):f},L=function(a){return 1===a.nodeType||9===a.nodeType||!+a.nodeType};function M(){this.expando=n.expando+M.uid++}M.uid=1,M.prototype={register:function(a,b){var c=b||{};return a.nodeType?a[this.expando]=c:Object.defineProperty(a,this.expando,{value:c,writable:!0,configurable:!0}),a[this.expando]},cache:function(a){if(!L(a))return{};var b=a[this.expando];return b||(b={},L(a)&&(a.nodeType?a[this.expando]=b:Object.defineProperty(a,this.expando,{value:b,configurable:!0}))),b},set:function(a,b,c){var d,e=this.cache(a);if("string"==typeof b)e[b]=c;else for(d in b)e[d]=b[d];return e},get:function(a,b){return void 0===b?this.cache(a):a[this.expando]&&a[this.expando][b]},access:function(a,b,c){var d;return void 0===b||b&&"string"==typeof b&&void 0===c?(d=this.get(a,b),void 0!==d?d:this.get(a,n.camelCase(b))):(this.set(a,b,c),void 0!==c?c:b)},remove:function(a,b){var c,d,e,f=a[this.expando];if(void 0!==f){if(void 0===b)this.register(a);else{n.isArray(b)?d=b.concat(b.map(n.camelCase)):(e=n.camelCase(b),b in f?d=[b,e]:(d=e,d=d in f?[d]:d.match(G)||[])),c=d.length;while(c--)delete f[d[c]]}(void 0===b||n.isEmptyObject(f))&&(a.nodeType?a[this.expando]=void 0:delete a[this.expando])}},hasData:function(a){var b=a[this.expando];return void 0!==b&&!n.isEmptyObject(b)}};var N=new M,O=new M,P=/^(?:\{[\w\W]*\}|\[[\w\W]*\])$/,Q=/[A-Z]/g;function R(a,b,c){var d;if(void 0===c&&1===a.nodeType)if(d="data-"+b.replace(Q,"-$&").toLowerCase(),c=a.getAttribute(d),"string"==typeof c){try{c="true"===c?!0:"false"===c?!1:"null"===c?null:+c+""===c?+c:P.test(c)?n.parseJSON(c):c;
    }catch(e){}O.set(a,b,c)}else c=void 0;return c}n.extend({hasData:function(a){return O.hasData(a)||N.hasData(a)},data:function(a,b,c){return O.access(a,b,c)},removeData:function(a,b){O.remove(a,b)},_data:function(a,b,c){return N.access(a,b,c)},_removeData:function(a,b){N.remove(a,b)}}),n.fn.extend({data:function(a,b){var c,d,e,f=this[0],g=f&&f.attributes;if(void 0===a){if(this.length&&(e=O.get(f),1===f.nodeType&&!N.get(f,"hasDataAttrs"))){c=g.length;while(c--)g[c]&&(d=g[c].name,0===d.indexOf("data-")&&(d=n.camelCase(d.slice(5)),R(f,d,e[d])));N.set(f,"hasDataAttrs",!0)}return e}return"object"==typeof a?this.each(function(){O.set(this,a)}):K(this,function(b){var c,d;if(f&&void 0===b){if(c=O.get(f,a)||O.get(f,a.replace(Q,"-$&").toLowerCase()),void 0!==c)return c;if(d=n.camelCase(a),c=O.get(f,d),void 0!==c)return c;if(c=R(f,d,void 0),void 0!==c)return c}else d=n.camelCase(a),this.each(function(){var c=O.get(this,d);O.set(this,d,b),a.indexOf("-")>-1&&void 0!==c&&O.set(this,a,b)})},null,b,arguments.length>1,null,!0)},removeData:function(a){return this.each(function(){O.remove(this,a)})}}),n.extend({queue:function(a,b,c){var d;return a?(b=(b||"fx")+"queue",d=N.get(a,b),c&&(!d||n.isArray(c)?d=N.access(a,b,n.makeArray(c)):d.push(c)),d||[]):void 0},dequeue:function(a,b){b=b||"fx";var c=n.queue(a,b),d=c.length,e=c.shift(),f=n._queueHooks(a,b),g=function(){n.dequeue(a,b)};"inprogress"===e&&(e=c.shift(),d--),e&&("fx"===b&&c.unshift("inprogress"),delete f.stop,e.call(a,g,f)),!d&&f&&f.empty.fire()},_queueHooks:function(a,b){var c=b+"queueHooks";return N.get(a,c)||N.access(a,c,{empty:n.Callbacks("once memory").add(function(){N.remove(a,[b+"queue",c])})})}}),n.fn.extend({queue:function(a,b){var c=2;return"string"!=typeof a&&(b=a,a="fx",c--),arguments.length<c?n.queue(this[0],a):void 0===b?this:this.each(function(){var c=n.queue(this,a,b);n._queueHooks(this,a),"fx"===a&&"inprogress"!==c[0]&&n.dequeue(this,a)})},dequeue:function(a){return this.each(function(){n.dequeue(this,a)})},clearQueue:function(a){return this.queue(a||"fx",[])},promise:function(a,b){var c,d=1,e=n.Deferred(),f=this,g=this.length,h=function(){--d||e.resolveWith(f,[f])};"string"!=typeof a&&(b=a,a=void 0),a=a||"fx";while(g--)c=N.get(f[g],a+"queueHooks"),c&&c.empty&&(d++,c.empty.add(h));return h(),e.promise(b)}});var S=/[+-]?(?:\d*\.|)\d+(?:[eE][+-]?\d+|)/.source,T=new RegExp("^(?:([+-])=|)("+S+")([a-z%]*)$","i"),U=["Top","Right","Bottom","Left"],V=function(a,b){return a=b||a,"none"===n.css(a,"display")||!n.contains(a.ownerDocument,a)};function W(a,b,c,d){var e,f=1,g=20,h=d?function(){return d.cur()}:function(){return n.css(a,b,"")},i=h(),j=c&&c[3]||(n.cssNumber[b]?"":"px"),k=(n.cssNumber[b]||"px"!==j&&+i)&&T.exec(n.css(a,b));if(k&&k[3]!==j){j=j||k[3],c=c||[],k=+i||1;do f=f||".5",k/=f,n.style(a,b,k+j);while(f!==(f=h()/i)&&1!==f&&--g)}return c&&(k=+k||+i||0,e=c[1]?k+(c[1]+1)*c[2]:+c[2],d&&(d.unit=j,d.start=k,d.end=e)),e}var X=/^(?:checkbox|radio)$/i,Y=/<([\w:-]+)/,Z=/^$|\/(?:java|ecma)script/i,$={option:[1,"<select multiple='multiple'>","</select>"],thead:[1,"<table>","</table>"],col:[2,"<table><colgroup>","</colgroup></table>"],tr:[2,"<table><tbody>","</tbody></table>"],td:[3,"<table><tbody><tr>","</tr></tbody></table>"],_default:[0,"",""]};$.optgroup=$.option,$.tbody=$.tfoot=$.colgroup=$.caption=$.thead,$.th=$.td;function _(a,b){var c="undefined"!=typeof a.getElementsByTagName?a.getElementsByTagName(b||"*"):"undefined"!=typeof a.querySelectorAll?a.querySelectorAll(b||"*"):[];return void 0===b||b&&n.nodeName(a,b)?n.merge([a],c):c}function aa(a,b){for(var c=0,d=a.length;d>c;c++)N.set(a[c],"globalEval",!b||N.get(b[c],"globalEval"))}var ba=/<|&#?\w+;/;function ca(a,b,c,d,e){for(var f,g,h,i,j,k,l=b.createDocumentFragment(),m=[],o=0,p=a.length;p>o;o++)if(f=a[o],f||0===f)if("object"===n.type(f))n.merge(m,f.nodeType?[f]:f);else if(ba.test(f)){g=g||l.appendChild(b.createElement("div")),h=(Y.exec(f)||["",""])[1].toLowerCase(),i=$[h]||$._default,g.innerHTML=i[1]+n.htmlPrefilter(f)+i[2],k=i[0];while(k--)g=g.lastChild;n.merge(m,g.childNodes),g=l.firstChild,g.textContent=""}else m.push(b.createTextNode(f));l.textContent="",o=0;while(f=m[o++])if(d&&n.inArray(f,d)>-1)e&&e.push(f);else if(j=n.contains(f.ownerDocument,f),g=_(l.appendChild(f),"script"),j&&aa(g),c){k=0;while(f=g[k++])Z.test(f.type||"")&&c.push(f)}return l}!function(){var a=d.createDocumentFragment(),b=a.appendChild(d.createElement("div")),c=d.createElement("input");c.setAttribute("type","radio"),c.setAttribute("checked","checked"),c.setAttribute("name","t"),b.appendChild(c),l.checkClone=b.cloneNode(!0).cloneNode(!0).lastChild.checked,b.innerHTML="<textarea>x</textarea>",l.noCloneChecked=!!b.cloneNode(!0).lastChild.defaultValue}();var da=/^key/,ea=/^(?:mouse|pointer|contextmenu|drag|drop)|click/,fa=/^([^.]*)(?:\.(.+)|)/;function ga(){return!0}function ha(){return!1}function ia(){try{return d.activeElement}catch(a){}}function ja(a,b,c,d,e,f){var g,h;if("object"==typeof b){"string"!=typeof c&&(d=d||c,c=void 0);for(h in b)ja(a,h,c,d,b[h],f);return a}if(null==d&&null==e?(e=c,d=c=void 0):null==e&&("string"==typeof c?(e=d,d=void 0):(e=d,d=c,c=void 0)),e===!1)e=ha;else if(!e)return a;return 1===f&&(g=e,e=function(a){return n().off(a),g.apply(this,arguments)},e.guid=g.guid||(g.guid=n.guid++)),a.each(function(){n.event.add(this,b,e,d,c)})}n.event={global:{},add:function(a,b,c,d,e){var f,g,h,i,j,k,l,m,o,p,q,r=N.get(a);if(r){c.handler&&(f=c,c=f.handler,e=f.selector),c.guid||(c.guid=n.guid++),(i=r.events)||(i=r.events={}),(g=r.handle)||(g=r.handle=function(b){return"undefined"!=typeof n&&n.event.triggered!==b.type?n.event.dispatch.apply(a,arguments):void 0}),b=(b||"").match(G)||[""],j=b.length;while(j--)h=fa.exec(b[j])||[],o=q=h[1],p=(h[2]||"").split(".").sort(),o&&(l=n.event.special[o]||{},o=(e?l.delegateType:l.bindType)||o,l=n.event.special[o]||{},k=n.extend({type:o,origType:q,data:d,handler:c,guid:c.guid,selector:e,needsContext:e&&n.expr.match.needsContext.test(e),namespace:p.join(".")},f),(m=i[o])||(m=i[o]=[],m.delegateCount=0,l.setup&&l.setup.call(a,d,p,g)!==!1||a.addEventListener&&a.addEventListener(o,g)),l.add&&(l.add.call(a,k),k.handler.guid||(k.handler.guid=c.guid)),e?m.splice(m.delegateCount++,0,k):m.push(k),n.event.global[o]=!0)}},remove:function(a,b,c,d,e){var f,g,h,i,j,k,l,m,o,p,q,r=N.hasData(a)&&N.get(a);if(r&&(i=r.events)){b=(b||"").match(G)||[""],j=b.length;while(j--)if(h=fa.exec(b[j])||[],o=q=h[1],p=(h[2]||"").split(".").sort(),o){l=n.event.special[o]||{},o=(d?l.delegateType:l.bindType)||o,m=i[o]||[],h=h[2]&&new RegExp("(^|\\.)"+p.join("\\.(?:.*\\.|)")+"(\\.|$)"),g=f=m.length;while(f--)k=m[f],!e&&q!==k.origType||c&&c.guid!==k.guid||h&&!h.test(k.namespace)||d&&d!==k.selector&&("**"!==d||!k.selector)||(m.splice(f,1),k.selector&&m.delegateCount--,l.remove&&l.remove.call(a,k));g&&!m.length&&(l.teardown&&l.teardown.call(a,p,r.handle)!==!1||n.removeEvent(a,o,r.handle),delete i[o])}else for(o in i)n.event.remove(a,o+b[j],c,d,!0);n.isEmptyObject(i)&&N.remove(a,"handle events")}},dispatch:function(a){a=n.event.fix(a);var b,c,d,f,g,h=[],i=e.call(arguments),j=(N.get(this,"events")||{})[a.type]||[],k=n.event.special[a.type]||{};if(i[0]=a,a.delegateTarget=this,!k.preDispatch||k.preDispatch.call(this,a)!==!1){h=n.event.handlers.call(this,a,j),b=0;while((f=h[b++])&&!a.isPropagationStopped()){a.currentTarget=f.elem,c=0;while((g=f.handlers[c++])&&!a.isImmediatePropagationStopped())a.rnamespace&&!a.rnamespace.test(g.namespace)||(a.handleObj=g,a.data=g.data,d=((n.event.special[g.origType]||{}).handle||g.handler).apply(f.elem,i),void 0!==d&&(a.result=d)===!1&&(a.preventDefault(),a.stopPropagation()))}return k.postDispatch&&k.postDispatch.call(this,a),a.result}},handlers:function(a,b){var c,d,e,f,g=[],h=b.delegateCount,i=a.target;if(h&&i.nodeType&&("click"!==a.type||isNaN(a.button)||a.button<1))for(;i!==this;i=i.parentNode||this)if(1===i.nodeType&&(i.disabled!==!0||"click"!==a.type)){for(d=[],c=0;h>c;c++)f=b[c],e=f.selector+" ",void 0===d[e]&&(d[e]=f.needsContext?n(e,this).index(i)>-1:n.find(e,this,null,[i]).length),d[e]&&d.push(f);d.length&&g.push({elem:i,handlers:d})}return h<b.length&&g.push({elem:this,handlers:b.slice(h)}),g},props:"altKey bubbles cancelable ctrlKey currentTarget detail eventPhase metaKey relatedTarget shiftKey target timeStamp view which".split(" "),fixHooks:{},keyHooks:{props:"char charCode key keyCode".split(" "),filter:function(a,b){return null==a.which&&(a.which=null!=b.charCode?b.charCode:b.keyCode),a}},mouseHooks:{props:"button buttons clientX clientY offsetX offsetY pageX pageY screenX screenY toElement".split(" "),filter:function(a,b){var c,e,f,g=b.button;return null==a.pageX&&null!=b.clientX&&(c=a.target.ownerDocument||d,e=c.documentElement,f=c.body,a.pageX=b.clientX+(e&&e.scrollLeft||f&&f.scrollLeft||0)-(e&&e.clientLeft||f&&f.clientLeft||0),a.pageY=b.clientY+(e&&e.scrollTop||f&&f.scrollTop||0)-(e&&e.clientTop||f&&f.clientTop||0)),a.which||void 0===g||(a.which=1&g?1:2&g?3:4&g?2:0),a}},fix:function(a){if(a[n.expando])return a;var b,c,e,f=a.type,g=a,h=this.fixHooks[f];h||(this.fixHooks[f]=h=ea.test(f)?this.mouseHooks:da.test(f)?this.keyHooks:{}),e=h.props?this.props.concat(h.props):this.props,a=new n.Event(g),b=e.length;while(b--)c=e[b],a[c]=g[c];return a.target||(a.target=d),3===a.target.nodeType&&(a.target=a.target.parentNode),h.filter?h.filter(a,g):a},special:{load:{noBubble:!0},focus:{trigger:function(){return this!==ia()&&this.focus?(this.focus(),!1):void 0},delegateType:"focusin"},blur:{trigger:function(){return this===ia()&&this.blur?(this.blur(),!1):void 0},delegateType:"focusout"},click:{trigger:function(){return"checkbox"===this.type&&this.click&&n.nodeName(this,"input")?(this.click(),!1):void 0},_default:function(a){return n.nodeName(a.target,"a")}},beforeunload:{postDispatch:function(a){void 0!==a.result&&a.originalEvent&&(a.originalEvent.returnValue=a.result)}}}},n.removeEvent=function(a,b,c){a.removeEventListener&&a.removeEventListener(b,c)},n.Event=function(a,b){return this instanceof n.Event?(a&&a.type?(this.originalEvent=a,this.type=a.type,this.isDefaultPrevented=a.defaultPrevented||void 0===a.defaultPrevented&&a.returnValue===!1?ga:ha):this.type=a,b&&n.extend(this,b),this.timeStamp=a&&a.timeStamp||n.now(),void(this[n.expando]=!0)):new n.Event(a,b)},n.Event.prototype={constructor:n.Event,isDefaultPrevented:ha,isPropagationStopped:ha,isImmediatePropagationStopped:ha,isSimulated:!1,preventDefault:function(){var a=this.originalEvent;this.isDefaultPrevented=ga,a&&!this.isSimulated&&a.preventDefault()},stopPropagation:function(){var a=this.originalEvent;this.isPropagationStopped=ga,a&&!this.isSimulated&&a.stopPropagation()},stopImmediatePropagation:function(){var a=this.originalEvent;this.isImmediatePropagationStopped=ga,a&&!this.isSimulated&&a.stopImmediatePropagation(),this.stopPropagation()}},n.each({mouseenter:"mouseover",mouseleave:"mouseout",pointerenter:"pointerover",pointerleave:"pointerout"},function(a,b){n.event.special[a]={delegateType:b,bindType:b,handle:function(a){var c,d=this,e=a.relatedTarget,f=a.handleObj;return e&&(e===d||n.contains(d,e))||(a.type=f.origType,c=f.handler.apply(this,arguments),a.type=b),c}}}),n.fn.extend({on:function(a,b,c,d){return ja(this,a,b,c,d)},one:function(a,b,c,d){return ja(this,a,b,c,d,1)},off:function(a,b,c){var d,e;if(a&&a.preventDefault&&a.handleObj)return d=a.handleObj,n(a.delegateTarget).off(d.namespace?d.origType+"."+d.namespace:d.origType,d.selector,d.handler),this;if("object"==typeof a){for(e in a)this.off(e,b,a[e]);return this}return b!==!1&&"function"!=typeof b||(c=b,b=void 0),c===!1&&(c=ha),this.each(function(){n.event.remove(this,a,c,b)})}});var ka=/<(?!area|br|col|embed|hr|img|input|link|meta|param)(([\w:-]+)[^>]*)\/>/gi,la=/<script|<style|<link/i,ma=/checked\s*(?:[^=]|=\s*.checked.)/i,na=/^true\/(.*)/,oa=/^\s*<!(?:\[CDATA\[|--)|(?:\]\]|--)>\s*$/g;function pa(a,b){return n.nodeName(a,"table")&&n.nodeName(11!==b.nodeType?b:b.firstChild,"tr")?a.getElementsByTagName("tbody")[0]||a.appendChild(a.ownerDocument.createElement("tbody")):a}function qa(a){return a.type=(null!==a.getAttribute("type"))+"/"+a.type,a}function ra(a){var b=na.exec(a.type);return b?a.type=b[1]:a.removeAttribute("type"),a}function sa(a,b){var c,d,e,f,g,h,i,j;if(1===b.nodeType){if(N.hasData(a)&&(f=N.access(a),g=N.set(b,f),j=f.events)){delete g.handle,g.events={};for(e in j)for(c=0,d=j[e].length;d>c;c++)n.event.add(b,e,j[e][c])}O.hasData(a)&&(h=O.access(a),i=n.extend({},h),O.set(b,i))}}function ta(a,b){var c=b.nodeName.toLowerCase();"input"===c&&X.test(a.type)?b.checked=a.checked:"input"!==c&&"textarea"!==c||(b.defaultValue=a.defaultValue)}function ua(a,b,c,d){b=f.apply([],b);var e,g,h,i,j,k,m=0,o=a.length,p=o-1,q=b[0],r=n.isFunction(q);if(r||o>1&&"string"==typeof q&&!l.checkClone&&ma.test(q))return a.each(function(e){var f=a.eq(e);r&&(b[0]=q.call(this,e,f.html())),ua(f,b,c,d)});if(o&&(e=ca(b,a[0].ownerDocument,!1,a,d),g=e.firstChild,1===e.childNodes.length&&(e=g),g||d)){for(h=n.map(_(e,"script"),qa),i=h.length;o>m;m++)j=e,m!==p&&(j=n.clone(j,!0,!0),i&&n.merge(h,_(j,"script"))),c.call(a[m],j,m);if(i)for(k=h[h.length-1].ownerDocument,n.map(h,ra),m=0;i>m;m++)j=h[m],Z.test(j.type||"")&&!N.access(j,"globalEval")&&n.contains(k,j)&&(j.src?n._evalUrl&&n._evalUrl(j.src):n.globalEval(j.textContent.replace(oa,"")))}return a}function va(a,b,c){for(var d,e=b?n.filter(b,a):a,f=0;null!=(d=e[f]);f++)c||1!==d.nodeType||n.cleanData(_(d)),d.parentNode&&(c&&n.contains(d.ownerDocument,d)&&aa(_(d,"script")),d.parentNode.removeChild(d));return a}n.extend({htmlPrefilter:function(a){return a.replace(ka,"<$1></$2>")},clone:function(a,b,c){var d,e,f,g,h=a.cloneNode(!0),i=n.contains(a.ownerDocument,a);if(!(l.noCloneChecked||1!==a.nodeType&&11!==a.nodeType||n.isXMLDoc(a)))for(g=_(h),f=_(a),d=0,e=f.length;e>d;d++)ta(f[d],g[d]);if(b)if(c)for(f=f||_(a),g=g||_(h),d=0,e=f.length;e>d;d++)sa(f[d],g[d]);else sa(a,h);return g=_(h,"script"),g.length>0&&aa(g,!i&&_(a,"script")),h},cleanData:function(a){for(var b,c,d,e=n.event.special,f=0;void 0!==(c=a[f]);f++)if(L(c)){if(b=c[N.expando]){if(b.events)for(d in b.events)e[d]?n.event.remove(c,d):n.removeEvent(c,d,b.handle);c[N.expando]=void 0}c[O.expando]&&(c[O.expando]=void 0)}}}),n.fn.extend({domManip:ua,detach:function(a){return va(this,a,!0)},remove:function(a){return va(this,a)},text:function(a){return K(this,function(a){return void 0===a?n.text(this):this.empty().each(function(){1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||(this.textContent=a)})},null,a,arguments.length)},append:function(){return ua(this,arguments,function(a){if(1===this.nodeType||11===this.nodeType||9===this.nodeType){var b=pa(this,a);b.appendChild(a)}})},prepend:function(){return ua(this,arguments,function(a){if(1===this.nodeType||11===this.nodeType||9===this.nodeType){var b=pa(this,a);b.insertBefore(a,b.firstChild)}})},before:function(){return ua(this,arguments,function(a){this.parentNode&&this.parentNode.insertBefore(a,this)})},after:function(){return ua(this,arguments,function(a){this.parentNode&&this.parentNode.insertBefore(a,this.nextSibling)})},empty:function(){for(var a,b=0;null!=(a=this[b]);b++)1===a.nodeType&&(n.cleanData(_(a,!1)),a.textContent="");return this},clone:function(a,b){return a=null==a?!1:a,b=null==b?a:b,this.map(function(){return n.clone(this,a,b)})},html:function(a){return K(this,function(a){var b=this[0]||{},c=0,d=this.length;if(void 0===a&&1===b.nodeType)return b.innerHTML;if("string"==typeof a&&!la.test(a)&&!$[(Y.exec(a)||["",""])[1].toLowerCase()]){a=n.htmlPrefilter(a);try{for(;d>c;c++)b=this[c]||{},1===b.nodeType&&(n.cleanData(_(b,!1)),b.innerHTML=a);b=0}catch(e){}}b&&this.empty().append(a)},null,a,arguments.length)},replaceWith:function(){var a=[];return ua(this,arguments,function(b){var c=this.parentNode;n.inArray(this,a)<0&&(n.cleanData(_(this)),c&&c.replaceChild(b,this))},a)}}),n.each({appendTo:"append",prependTo:"prepend",insertBefore:"before",insertAfter:"after",replaceAll:"replaceWith"},function(a,b){n.fn[a]=function(a){for(var c,d=[],e=n(a),f=e.length-1,h=0;f>=h;h++)c=h===f?this:this.clone(!0),n(e[h])[b](c),g.apply(d,c.get());return this.pushStack(d)}});var wa,xa={HTML:"block",BODY:"block"};function ya(a,b){var c=n(b.createElement(a)).appendTo(b.body),d=n.css(c[0],"display");return c.detach(),d}function za(a){var b=d,c=xa[a];return c||(c=ya(a,b),"none"!==c&&c||(wa=(wa||n("<iframe frameborder='0' width='0' height='0'/>")).appendTo(b.documentElement),b=wa[0].contentDocument,b.write(),b.close(),c=ya(a,b),wa.detach()),xa[a]=c),c}var Aa=/^margin/,Ba=new RegExp("^("+S+")(?!px)[a-z%]+$","i"),Ca=function(b){var c=b.ownerDocument.defaultView;return c&&c.opener||(c=a),c.getComputedStyle(b)},Da=function(a,b,c,d){var e,f,g={};for(f in b)g[f]=a.style[f],a.style[f]=b[f];e=c.apply(a,d||[]);for(f in b)a.style[f]=g[f];return e},Ea=d.documentElement;!function(){var b,c,e,f,g=d.createElement("div"),h=d.createElement("div");if(h.style){h.style.backgroundClip="content-box",h.cloneNode(!0).style.backgroundClip="",l.clearCloneStyle="content-box"===h.style.backgroundClip,g.style.cssText="border:0;width:8px;height:0;top:0;left:-9999px;padding:0;margin-top:1px;position:absolute",g.appendChild(h);function i(){h.style.cssText="-webkit-box-sizing:border-box;-moz-box-sizing:border-box;box-sizing:border-box;position:relative;display:block;margin:auto;border:1px;padding:1px;top:1%;width:50%",h.innerHTML="",Ea.appendChild(g);var d=a.getComputedStyle(h);b="1%"!==d.top,f="2px"===d.marginLeft,c="4px"===d.width,h.style.marginRight="50%",e="4px"===d.marginRight,Ea.removeChild(g)}n.extend(l,{pixelPosition:function(){return i(),b},boxSizingReliable:function(){return null==c&&i(),c},pixelMarginRight:function(){return null==c&&i(),e},reliableMarginLeft:function(){return null==c&&i(),f},reliableMarginRight:function(){var b,c=h.appendChild(d.createElement("div"));return c.style.cssText=h.style.cssText="-webkit-box-sizing:content-box;box-sizing:content-box;display:block;margin:0;border:0;padding:0",c.style.marginRight=c.style.width="0",h.style.width="1px",Ea.appendChild(g),b=!parseFloat(a.getComputedStyle(c).marginRight),Ea.removeChild(g),h.removeChild(c),b}})}}();function Fa(a,b,c){var d,e,f,g,h=a.style;return c=c||Ca(a),g=c?c.getPropertyValue(b)||c[b]:void 0,""!==g&&void 0!==g||n.contains(a.ownerDocument,a)||(g=n.style(a,b)),c&&!l.pixelMarginRight()&&Ba.test(g)&&Aa.test(b)&&(d=h.width,e=h.minWidth,f=h.maxWidth,h.minWidth=h.maxWidth=h.width=g,g=c.width,h.width=d,h.minWidth=e,h.maxWidth=f),void 0!==g?g+"":g}function Ga(a,b){return{get:function(){return a()?void delete this.get:(this.get=b).apply(this,arguments)}}}var Ha=/^(none|table(?!-c[ea]).+)/,Ia={position:"absolute",visibility:"hidden",display:"block"},Ja={letterSpacing:"0",fontWeight:"400"},Ka=["Webkit","O","Moz","ms"],La=d.createElement("div").style;function Ma(a){if(a in La)return a;var b=a[0].toUpperCase()+a.slice(1),c=Ka.length;while(c--)if(a=Ka[c]+b,a in La)return a}function Na(a,b,c){var d=T.exec(b);return d?Math.max(0,d[2]-(c||0))+(d[3]||"px"):b}function Oa(a,b,c,d,e){for(var f=c===(d?"border":"content")?4:"width"===b?1:0,g=0;4>f;f+=2)"margin"===c&&(g+=n.css(a,c+U[f],!0,e)),d?("content"===c&&(g-=n.css(a,"padding"+U[f],!0,e)),"margin"!==c&&(g-=n.css(a,"border"+U[f]+"Width",!0,e))):(g+=n.css(a,"padding"+U[f],!0,e),"padding"!==c&&(g+=n.css(a,"border"+U[f]+"Width",!0,e)));return g}function Pa(a,b,c){var d=!0,e="width"===b?a.offsetWidth:a.offsetHeight,f=Ca(a),g="border-box"===n.css(a,"boxSizing",!1,f);if(0>=e||null==e){if(e=Fa(a,b,f),(0>e||null==e)&&(e=a.style[b]),Ba.test(e))return e;d=g&&(l.boxSizingReliable()||e===a.style[b]),e=parseFloat(e)||0}return e+Oa(a,b,c||(g?"border":"content"),d,f)+"px"}function Qa(a,b){for(var c,d,e,f=[],g=0,h=a.length;h>g;g++)d=a[g],d.style&&(f[g]=N.get(d,"olddisplay"),c=d.style.display,b?(f[g]||"none"!==c||(d.style.display=""),""===d.style.display&&V(d)&&(f[g]=N.access(d,"olddisplay",za(d.nodeName)))):(e=V(d),"none"===c&&e||N.set(d,"olddisplay",e?c:n.css(d,"display"))));for(g=0;h>g;g++)d=a[g],d.style&&(b&&"none"!==d.style.display&&""!==d.style.display||(d.style.display=b?f[g]||"":"none"));return a}n.extend({cssHooks:{opacity:{get:function(a,b){if(b){var c=Fa(a,"opacity");return""===c?"1":c}}}},cssNumber:{animationIterationCount:!0,columnCount:!0,fillOpacity:!0,flexGrow:!0,flexShrink:!0,fontWeight:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,widows:!0,zIndex:!0,zoom:!0},cssProps:{"float":"cssFloat"},style:function(a,b,c,d){if(a&&3!==a.nodeType&&8!==a.nodeType&&a.style){var e,f,g,h=n.camelCase(b),i=a.style;return b=n.cssProps[h]||(n.cssProps[h]=Ma(h)||h),g=n.cssHooks[b]||n.cssHooks[h],void 0===c?g&&"get"in g&&void 0!==(e=g.get(a,!1,d))?e:i[b]:(f=typeof c,"string"===f&&(e=T.exec(c))&&e[1]&&(c=W(a,b,e),f="number"),null!=c&&c===c&&("number"===f&&(c+=e&&e[3]||(n.cssNumber[h]?"":"px")),l.clearCloneStyle||""!==c||0!==b.indexOf("background")||(i[b]="inherit"),g&&"set"in g&&void 0===(c=g.set(a,c,d))||(i[b]=c)),void 0)}},css:function(a,b,c,d){var e,f,g,h=n.camelCase(b);return b=n.cssProps[h]||(n.cssProps[h]=Ma(h)||h),g=n.cssHooks[b]||n.cssHooks[h],g&&"get"in g&&(e=g.get(a,!0,c)),void 0===e&&(e=Fa(a,b,d)),"normal"===e&&b in Ja&&(e=Ja[b]),""===c||c?(f=parseFloat(e),c===!0||isFinite(f)?f||0:e):e}}),n.each(["height","width"],function(a,b){n.cssHooks[b]={get:function(a,c,d){return c?Ha.test(n.css(a,"display"))&&0===a.offsetWidth?Da(a,Ia,function(){return Pa(a,b,d)}):Pa(a,b,d):void 0},set:function(a,c,d){var e,f=d&&Ca(a),g=d&&Oa(a,b,d,"border-box"===n.css(a,"boxSizing",!1,f),f);return g&&(e=T.exec(c))&&"px"!==(e[3]||"px")&&(a.style[b]=c,c=n.css(a,b)),Na(a,c,g)}}}),n.cssHooks.marginLeft=Ga(l.reliableMarginLeft,function(a,b){return b?(parseFloat(Fa(a,"marginLeft"))||a.getBoundingClientRect().left-Da(a,{marginLeft:0},function(){return a.getBoundingClientRect().left}))+"px":void 0}),n.cssHooks.marginRight=Ga(l.reliableMarginRight,function(a,b){return b?Da(a,{display:"inline-block"},Fa,[a,"marginRight"]):void 0}),n.each({margin:"",padding:"",border:"Width"},function(a,b){n.cssHooks[a+b]={expand:function(c){for(var d=0,e={},f="string"==typeof c?c.split(" "):[c];4>d;d++)e[a+U[d]+b]=f[d]||f[d-2]||f[0];return e}},Aa.test(a)||(n.cssHooks[a+b].set=Na)}),n.fn.extend({css:function(a,b){return K(this,function(a,b,c){var d,e,f={},g=0;if(n.isArray(b)){for(d=Ca(a),e=b.length;e>g;g++)f[b[g]]=n.css(a,b[g],!1,d);return f}return void 0!==c?n.style(a,b,c):n.css(a,b)},a,b,arguments.length>1)},show:function(){return Qa(this,!0)},hide:function(){return Qa(this)},toggle:function(a){return"boolean"==typeof a?a?this.show():this.hide():this.each(function(){V(this)?n(this).show():n(this).hide()})}});function Ra(a,b,c,d,e){return new Ra.prototype.init(a,b,c,d,e)}n.Tween=Ra,Ra.prototype={constructor:Ra,init:function(a,b,c,d,e,f){this.elem=a,this.prop=c,this.easing=e||n.easing._default,this.options=b,this.start=this.now=this.cur(),this.end=d,this.unit=f||(n.cssNumber[c]?"":"px")},cur:function(){var a=Ra.propHooks[this.prop];return a&&a.get?a.get(this):Ra.propHooks._default.get(this)},run:function(a){var b,c=Ra.propHooks[this.prop];return this.options.duration?this.pos=b=n.easing[this.easing](a,this.options.duration*a,0,1,this.options.duration):this.pos=b=a,this.now=(this.end-this.start)*b+this.start,this.options.step&&this.options.step.call(this.elem,this.now,this),c&&c.set?c.set(this):Ra.propHooks._default.set(this),this}},Ra.prototype.init.prototype=Ra.prototype,Ra.propHooks={_default:{get:function(a){var b;return 1!==a.elem.nodeType||null!=a.elem[a.prop]&&null==a.elem.style[a.prop]?a.elem[a.prop]:(b=n.css(a.elem,a.prop,""),b&&"auto"!==b?b:0)},set:function(a){n.fx.step[a.prop]?n.fx.step[a.prop](a):1!==a.elem.nodeType||null==a.elem.style[n.cssProps[a.prop]]&&!n.cssHooks[a.prop]?a.elem[a.prop]=a.now:n.style(a.elem,a.prop,a.now+a.unit)}}},Ra.propHooks.scrollTop=Ra.propHooks.scrollLeft={set:function(a){a.elem.nodeType&&a.elem.parentNode&&(a.elem[a.prop]=a.now)}},n.easing={linear:function(a){return a},swing:function(a){return.5-Math.cos(a*Math.PI)/2},_default:"swing"},n.fx=Ra.prototype.init,n.fx.step={};var Sa,Ta,Ua=/^(?:toggle|show|hide)$/,Va=/queueHooks$/;function Wa(){return a.setTimeout(function(){Sa=void 0}),Sa=n.now()}function Xa(a,b){var c,d=0,e={height:a};for(b=b?1:0;4>d;d+=2-b)c=U[d],e["margin"+c]=e["padding"+c]=a;return b&&(e.opacity=e.width=a),e}function Ya(a,b,c){for(var d,e=(_a.tweeners[b]||[]).concat(_a.tweeners["*"]),f=0,g=e.length;g>f;f++)if(d=e[f].call(c,b,a))return d}function Za(a,b,c){var d,e,f,g,h,i,j,k,l=this,m={},o=a.style,p=a.nodeType&&V(a),q=N.get(a,"fxshow");c.queue||(h=n._queueHooks(a,"fx"),null==h.unqueued&&(h.unqueued=0,i=h.empty.fire,h.empty.fire=function(){h.unqueued||i()}),h.unqueued++,l.always(function(){l.always(function(){h.unqueued--,n.queue(a,"fx").length||h.empty.fire()})})),1===a.nodeType&&("height"in b||"width"in b)&&(c.overflow=[o.overflow,o.overflowX,o.overflowY],j=n.css(a,"display"),k="none"===j?N.get(a,"olddisplay")||za(a.nodeName):j,"inline"===k&&"none"===n.css(a,"float")&&(o.display="inline-block")),c.overflow&&(o.overflow="hidden",l.always(function(){o.overflow=c.overflow[0],o.overflowX=c.overflow[1],o.overflowY=c.overflow[2]}));for(d in b)if(e=b[d],Ua.exec(e)){if(delete b[d],f=f||"toggle"===e,e===(p?"hide":"show")){if("show"!==e||!q||void 0===q[d])continue;p=!0}m[d]=q&&q[d]||n.style(a,d)}else j=void 0;if(n.isEmptyObject(m))"inline"===("none"===j?za(a.nodeName):j)&&(o.display=j);else{q?"hidden"in q&&(p=q.hidden):q=N.access(a,"fxshow",{}),f&&(q.hidden=!p),p?n(a).show():l.done(function(){n(a).hide()}),l.done(function(){var b;N.remove(a,"fxshow");for(b in m)n.style(a,b,m[b])});for(d in m)g=Ya(p?q[d]:0,d,l),d in q||(q[d]=g.start,p&&(g.end=g.start,g.start="width"===d||"height"===d?1:0))}}function $a(a,b){var c,d,e,f,g;for(c in a)if(d=n.camelCase(c),e=b[d],f=a[c],n.isArray(f)&&(e=f[1],f=a[c]=f[0]),c!==d&&(a[d]=f,delete a[c]),g=n.cssHooks[d],g&&"expand"in g){f=g.expand(f),delete a[d];for(c in f)c in a||(a[c]=f[c],b[c]=e)}else b[d]=e}function _a(a,b,c){var d,e,f=0,g=_a.prefilters.length,h=n.Deferred().always(function(){delete i.elem}),i=function(){if(e)return!1;for(var b=Sa||Wa(),c=Math.max(0,j.startTime+j.duration-b),d=c/j.duration||0,f=1-d,g=0,i=j.tweens.length;i>g;g++)j.tweens[g].run(f);return h.notifyWith(a,[j,f,c]),1>f&&i?c:(h.resolveWith(a,[j]),!1)},j=h.promise({elem:a,props:n.extend({},b),opts:n.extend(!0,{specialEasing:{},easing:n.easing._default},c),originalProperties:b,originalOptions:c,startTime:Sa||Wa(),duration:c.duration,tweens:[],createTween:function(b,c){var d=n.Tween(a,j.opts,b,c,j.opts.specialEasing[b]||j.opts.easing);return j.tweens.push(d),d},stop:function(b){var c=0,d=b?j.tweens.length:0;if(e)return this;for(e=!0;d>c;c++)j.tweens[c].run(1);return b?(h.notifyWith(a,[j,1,0]),h.resolveWith(a,[j,b])):h.rejectWith(a,[j,b]),this}}),k=j.props;for($a(k,j.opts.specialEasing);g>f;f++)if(d=_a.prefilters[f].call(j,a,k,j.opts))return n.isFunction(d.stop)&&(n._queueHooks(j.elem,j.opts.queue).stop=n.proxy(d.stop,d)),d;return n.map(k,Ya,j),n.isFunction(j.opts.start)&&j.opts.start.call(a,j),n.fx.timer(n.extend(i,{elem:a,anim:j,queue:j.opts.queue})),j.progress(j.opts.progress).done(j.opts.done,j.opts.complete).fail(j.opts.fail).always(j.opts.always)}n.Animation=n.extend(_a,{tweeners:{"*":[function(a,b){var c=this.createTween(a,b);return W(c.elem,a,T.exec(b),c),c}]},tweener:function(a,b){n.isFunction(a)?(b=a,a=["*"]):a=a.match(G);for(var c,d=0,e=a.length;e>d;d++)c=a[d],_a.tweeners[c]=_a.tweeners[c]||[],_a.tweeners[c].unshift(b)},prefilters:[Za],prefilter:function(a,b){b?_a.prefilters.unshift(a):_a.prefilters.push(a)}}),n.speed=function(a,b,c){var d=a&&"object"==typeof a?n.extend({},a):{complete:c||!c&&b||n.isFunction(a)&&a,duration:a,easing:c&&b||b&&!n.isFunction(b)&&b};return d.duration=n.fx.off?0:"number"==typeof d.duration?d.duration:d.duration in n.fx.speeds?n.fx.speeds[d.duration]:n.fx.speeds._default,null!=d.queue&&d.queue!==!0||(d.queue="fx"),d.old=d.complete,d.complete=function(){n.isFunction(d.old)&&d.old.call(this),d.queue&&n.dequeue(this,d.queue)},d},n.fn.extend({fadeTo:function(a,b,c,d){return this.filter(V).css("opacity",0).show().end().animate({opacity:b},a,c,d)},animate:function(a,b,c,d){var e=n.isEmptyObject(a),f=n.speed(b,c,d),g=function(){var b=_a(this,n.extend({},a),f);(e||N.get(this,"finish"))&&b.stop(!0)};return g.finish=g,e||f.queue===!1?this.each(g):this.queue(f.queue,g)},stop:function(a,b,c){var d=function(a){var b=a.stop;delete a.stop,b(c)};return"string"!=typeof a&&(c=b,b=a,a=void 0),b&&a!==!1&&this.queue(a||"fx",[]),this.each(function(){var b=!0,e=null!=a&&a+"queueHooks",f=n.timers,g=N.get(this);if(e)g[e]&&g[e].stop&&d(g[e]);else for(e in g)g[e]&&g[e].stop&&Va.test(e)&&d(g[e]);for(e=f.length;e--;)f[e].elem!==this||null!=a&&f[e].queue!==a||(f[e].anim.stop(c),b=!1,f.splice(e,1));!b&&c||n.dequeue(this,a)})},finish:function(a){return a!==!1&&(a=a||"fx"),this.each(function(){var b,c=N.get(this),d=c[a+"queue"],e=c[a+"queueHooks"],f=n.timers,g=d?d.length:0;for(c.finish=!0,n.queue(this,a,[]),e&&e.stop&&e.stop.call(this,!0),b=f.length;b--;)f[b].elem===this&&f[b].queue===a&&(f[b].anim.stop(!0),f.splice(b,1));for(b=0;g>b;b++)d[b]&&d[b].finish&&d[b].finish.call(this);delete c.finish})}}),n.each(["toggle","show","hide"],function(a,b){var c=n.fn[b];n.fn[b]=function(a,d,e){return null==a||"boolean"==typeof a?c.apply(this,arguments):this.animate(Xa(b,!0),a,d,e)}}),n.each({slideDown:Xa("show"),slideUp:Xa("hide"),slideToggle:Xa("toggle"),fadeIn:{opacity:"show"},fadeOut:{opacity:"hide"},fadeToggle:{opacity:"toggle"}},function(a,b){n.fn[a]=function(a,c,d){return this.animate(b,a,c,d)}}),n.timers=[],n.fx.tick=function(){var a,b=0,c=n.timers;for(Sa=n.now();b<c.length;b++)a=c[b],a()||c[b]!==a||c.splice(b--,1);c.length||n.fx.stop(),Sa=void 0},n.fx.timer=function(a){n.timers.push(a),a()?n.fx.start():n.timers.pop()},n.fx.interval=13,n.fx.start=function(){Ta||(Ta=a.setInterval(n.fx.tick,n.fx.interval))},n.fx.stop=function(){a.clearInterval(Ta),Ta=null},n.fx.speeds={slow:600,fast:200,_default:400},n.fn.delay=function(b,c){return b=n.fx?n.fx.speeds[b]||b:b,c=c||"fx",this.queue(c,function(c,d){var e=a.setTimeout(c,b);d.stop=function(){a.clearTimeout(e)}})},function(){var a=d.createElement("input"),b=d.createElement("select"),c=b.appendChild(d.createElement("option"));a.type="checkbox",l.checkOn=""!==a.value,l.optSelected=c.selected,b.disabled=!0,l.optDisabled=!c.disabled,a=d.createElement("input"),a.value="t",a.type="radio",l.radioValue="t"===a.value}();var ab,bb=n.expr.attrHandle;n.fn.extend({attr:function(a,b){return K(this,n.attr,a,b,arguments.length>1)},removeAttr:function(a){return this.each(function(){n.removeAttr(this,a)})}}),n.extend({attr:function(a,b,c){var d,e,f=a.nodeType;if(3!==f&&8!==f&&2!==f)return"undefined"==typeof a.getAttribute?n.prop(a,b,c):(1===f&&n.isXMLDoc(a)||(b=b.toLowerCase(),e=n.attrHooks[b]||(n.expr.match.bool.test(b)?ab:void 0)),void 0!==c?null===c?void n.removeAttr(a,b):e&&"set"in e&&void 0!==(d=e.set(a,c,b))?d:(a.setAttribute(b,c+""),c):e&&"get"in e&&null!==(d=e.get(a,b))?d:(d=n.find.attr(a,b),null==d?void 0:d))},attrHooks:{type:{set:function(a,b){if(!l.radioValue&&"radio"===b&&n.nodeName(a,"input")){var c=a.value;return a.setAttribute("type",b),c&&(a.value=c),b}}}},removeAttr:function(a,b){var c,d,e=0,f=b&&b.match(G);if(f&&1===a.nodeType)while(c=f[e++])d=n.propFix[c]||c,n.expr.match.bool.test(c)&&(a[d]=!1),a.removeAttribute(c)}}),ab={set:function(a,b,c){return b===!1?n.removeAttr(a,c):a.setAttribute(c,c),c}},n.each(n.expr.match.bool.source.match(/\w+/g),function(a,b){var c=bb[b]||n.find.attr;bb[b]=function(a,b,d){var e,f;return d||(f=bb[b],bb[b]=e,e=null!=c(a,b,d)?b.toLowerCase():null,bb[b]=f),e}});var cb=/^(?:input|select|textarea|button)$/i,db=/^(?:a|area)$/i;n.fn.extend({prop:function(a,b){return K(this,n.prop,a,b,arguments.length>1)},removeProp:function(a){return this.each(function(){delete this[n.propFix[a]||a]})}}),n.extend({prop:function(a,b,c){var d,e,f=a.nodeType;if(3!==f&&8!==f&&2!==f)return 1===f&&n.isXMLDoc(a)||(b=n.propFix[b]||b,e=n.propHooks[b]),
    void 0!==c?e&&"set"in e&&void 0!==(d=e.set(a,c,b))?d:a[b]=c:e&&"get"in e&&null!==(d=e.get(a,b))?d:a[b]},propHooks:{tabIndex:{get:function(a){var b=n.find.attr(a,"tabindex");return b?parseInt(b,10):cb.test(a.nodeName)||db.test(a.nodeName)&&a.href?0:-1}}},propFix:{"for":"htmlFor","class":"className"}}),l.optSelected||(n.propHooks.selected={get:function(a){var b=a.parentNode;return b&&b.parentNode&&b.parentNode.selectedIndex,null},set:function(a){var b=a.parentNode;b&&(b.selectedIndex,b.parentNode&&b.parentNode.selectedIndex)}}),n.each(["tabIndex","readOnly","maxLength","cellSpacing","cellPadding","rowSpan","colSpan","useMap","frameBorder","contentEditable"],function(){n.propFix[this.toLowerCase()]=this});var eb=/[\t\r\n\f]/g;function fb(a){return a.getAttribute&&a.getAttribute("class")||""}n.fn.extend({addClass:function(a){var b,c,d,e,f,g,h,i=0;if(n.isFunction(a))return this.each(function(b){n(this).addClass(a.call(this,b,fb(this)))});if("string"==typeof a&&a){b=a.match(G)||[];while(c=this[i++])if(e=fb(c),d=1===c.nodeType&&(" "+e+" ").replace(eb," ")){g=0;while(f=b[g++])d.indexOf(" "+f+" ")<0&&(d+=f+" ");h=n.trim(d),e!==h&&c.setAttribute("class",h)}}return this},removeClass:function(a){var b,c,d,e,f,g,h,i=0;if(n.isFunction(a))return this.each(function(b){n(this).removeClass(a.call(this,b,fb(this)))});if(!arguments.length)return this.attr("class","");if("string"==typeof a&&a){b=a.match(G)||[];while(c=this[i++])if(e=fb(c),d=1===c.nodeType&&(" "+e+" ").replace(eb," ")){g=0;while(f=b[g++])while(d.indexOf(" "+f+" ")>-1)d=d.replace(" "+f+" "," ");h=n.trim(d),e!==h&&c.setAttribute("class",h)}}return this},toggleClass:function(a,b){var c=typeof a;return"boolean"==typeof b&&"string"===c?b?this.addClass(a):this.removeClass(a):n.isFunction(a)?this.each(function(c){n(this).toggleClass(a.call(this,c,fb(this),b),b)}):this.each(function(){var b,d,e,f;if("string"===c){d=0,e=n(this),f=a.match(G)||[];while(b=f[d++])e.hasClass(b)?e.removeClass(b):e.addClass(b)}else void 0!==a&&"boolean"!==c||(b=fb(this),b&&N.set(this,"__className__",b),this.setAttribute&&this.setAttribute("class",b||a===!1?"":N.get(this,"__className__")||""))})},hasClass:function(a){var b,c,d=0;b=" "+a+" ";while(c=this[d++])if(1===c.nodeType&&(" "+fb(c)+" ").replace(eb," ").indexOf(b)>-1)return!0;return!1}});var gb=/\r/g,hb=/[\x20\t\r\n\f]+/g;n.fn.extend({val:function(a){var b,c,d,e=this[0];{if(arguments.length)return d=n.isFunction(a),this.each(function(c){var e;1===this.nodeType&&(e=d?a.call(this,c,n(this).val()):a,null==e?e="":"number"==typeof e?e+="":n.isArray(e)&&(e=n.map(e,function(a){return null==a?"":a+""})),b=n.valHooks[this.type]||n.valHooks[this.nodeName.toLowerCase()],b&&"set"in b&&void 0!==b.set(this,e,"value")||(this.value=e))});if(e)return b=n.valHooks[e.type]||n.valHooks[e.nodeName.toLowerCase()],b&&"get"in b&&void 0!==(c=b.get(e,"value"))?c:(c=e.value,"string"==typeof c?c.replace(gb,""):null==c?"":c)}}}),n.extend({valHooks:{option:{get:function(a){var b=n.find.attr(a,"value");return null!=b?b:n.trim(n.text(a)).replace(hb," ")}},select:{get:function(a){for(var b,c,d=a.options,e=a.selectedIndex,f="select-one"===a.type||0>e,g=f?null:[],h=f?e+1:d.length,i=0>e?h:f?e:0;h>i;i++)if(c=d[i],(c.selected||i===e)&&(l.optDisabled?!c.disabled:null===c.getAttribute("disabled"))&&(!c.parentNode.disabled||!n.nodeName(c.parentNode,"optgroup"))){if(b=n(c).val(),f)return b;g.push(b)}return g},set:function(a,b){var c,d,e=a.options,f=n.makeArray(b),g=e.length;while(g--)d=e[g],(d.selected=n.inArray(n.valHooks.option.get(d),f)>-1)&&(c=!0);return c||(a.selectedIndex=-1),f}}}}),n.each(["radio","checkbox"],function(){n.valHooks[this]={set:function(a,b){return n.isArray(b)?a.checked=n.inArray(n(a).val(),b)>-1:void 0}},l.checkOn||(n.valHooks[this].get=function(a){return null===a.getAttribute("value")?"on":a.value})});var ib=/^(?:focusinfocus|focusoutblur)$/;n.extend(n.event,{trigger:function(b,c,e,f){var g,h,i,j,l,m,o,p=[e||d],q=k.call(b,"type")?b.type:b,r=k.call(b,"namespace")?b.namespace.split("."):[];if(h=i=e=e||d,3!==e.nodeType&&8!==e.nodeType&&!ib.test(q+n.event.triggered)&&(q.indexOf(".")>-1&&(r=q.split("."),q=r.shift(),r.sort()),l=q.indexOf(":")<0&&"on"+q,b=b[n.expando]?b:new n.Event(q,"object"==typeof b&&b),b.isTrigger=f?2:3,b.namespace=r.join("."),b.rnamespace=b.namespace?new RegExp("(^|\\.)"+r.join("\\.(?:.*\\.|)")+"(\\.|$)"):null,b.result=void 0,b.target||(b.target=e),c=null==c?[b]:n.makeArray(c,[b]),o=n.event.special[q]||{},f||!o.trigger||o.trigger.apply(e,c)!==!1)){if(!f&&!o.noBubble&&!n.isWindow(e)){for(j=o.delegateType||q,ib.test(j+q)||(h=h.parentNode);h;h=h.parentNode)p.push(h),i=h;i===(e.ownerDocument||d)&&p.push(i.defaultView||i.parentWindow||a)}g=0;while((h=p[g++])&&!b.isPropagationStopped())b.type=g>1?j:o.bindType||q,m=(N.get(h,"events")||{})[b.type]&&N.get(h,"handle"),m&&m.apply(h,c),m=l&&h[l],m&&m.apply&&L(h)&&(b.result=m.apply(h,c),b.result===!1&&b.preventDefault());return b.type=q,f||b.isDefaultPrevented()||o._default&&o._default.apply(p.pop(),c)!==!1||!L(e)||l&&n.isFunction(e[q])&&!n.isWindow(e)&&(i=e[l],i&&(e[l]=null),n.event.triggered=q,e[q](),n.event.triggered=void 0,i&&(e[l]=i)),b.result}},simulate:function(a,b,c){var d=n.extend(new n.Event,c,{type:a,isSimulated:!0});n.event.trigger(d,null,b)}}),n.fn.extend({trigger:function(a,b){return this.each(function(){n.event.trigger(a,b,this)})},triggerHandler:function(a,b){var c=this[0];return c?n.event.trigger(a,b,c,!0):void 0}}),n.each("blur focus focusin focusout load resize scroll unload click dblclick mousedown mouseup mousemove mouseover mouseout mouseenter mouseleave change select submit keydown keypress keyup error contextmenu".split(" "),function(a,b){n.fn[b]=function(a,c){return arguments.length>0?this.on(b,null,a,c):this.trigger(b)}}),n.fn.extend({hover:function(a,b){return this.mouseenter(a).mouseleave(b||a)}}),l.focusin="onfocusin"in a,l.focusin||n.each({focus:"focusin",blur:"focusout"},function(a,b){var c=function(a){n.event.simulate(b,a.target,n.event.fix(a))};n.event.special[b]={setup:function(){var d=this.ownerDocument||this,e=N.access(d,b);e||d.addEventListener(a,c,!0),N.access(d,b,(e||0)+1)},teardown:function(){var d=this.ownerDocument||this,e=N.access(d,b)-1;e?N.access(d,b,e):(d.removeEventListener(a,c,!0),N.remove(d,b))}}});var jb=a.location,kb=n.now(),lb=/\?/;n.parseJSON=function(a){return JSON.parse(a+"")},n.parseXML=function(b){var c;if(!b||"string"!=typeof b)return null;try{c=(new a.DOMParser).parseFromString(b,"text/xml")}catch(d){c=void 0}return c&&!c.getElementsByTagName("parsererror").length||n.error("Invalid XML: "+b),c};var mb=/#.*$/,nb=/([?&])_=[^&]*/,ob=/^(.*?):[ \t]*([^\r\n]*)$/gm,pb=/^(?:about|app|app-storage|.+-extension|file|res|widget):$/,qb=/^(?:GET|HEAD)$/,rb=/^\/\//,sb={},tb={},ub="*/".concat("*"),vb=d.createElement("a");vb.href=jb.href;function wb(a){return function(b,c){"string"!=typeof b&&(c=b,b="*");var d,e=0,f=b.toLowerCase().match(G)||[];if(n.isFunction(c))while(d=f[e++])"+"===d[0]?(d=d.slice(1)||"*",(a[d]=a[d]||[]).unshift(c)):(a[d]=a[d]||[]).push(c)}}function xb(a,b,c,d){var e={},f=a===tb;function g(h){var i;return e[h]=!0,n.each(a[h]||[],function(a,h){var j=h(b,c,d);return"string"!=typeof j||f||e[j]?f?!(i=j):void 0:(b.dataTypes.unshift(j),g(j),!1)}),i}return g(b.dataTypes[0])||!e["*"]&&g("*")}function yb(a,b){var c,d,e=n.ajaxSettings.flatOptions||{};for(c in b)void 0!==b[c]&&((e[c]?a:d||(d={}))[c]=b[c]);return d&&n.extend(!0,a,d),a}function zb(a,b,c){var d,e,f,g,h=a.contents,i=a.dataTypes;while("*"===i[0])i.shift(),void 0===d&&(d=a.mimeType||b.getResponseHeader("Content-Type"));if(d)for(e in h)if(h[e]&&h[e].test(d)){i.unshift(e);break}if(i[0]in c)f=i[0];else{for(e in c){if(!i[0]||a.converters[e+" "+i[0]]){f=e;break}g||(g=e)}f=f||g}return f?(f!==i[0]&&i.unshift(f),c[f]):void 0}function Ab(a,b,c,d){var e,f,g,h,i,j={},k=a.dataTypes.slice();if(k[1])for(g in a.converters)j[g.toLowerCase()]=a.converters[g];f=k.shift();while(f)if(a.responseFields[f]&&(c[a.responseFields[f]]=b),!i&&d&&a.dataFilter&&(b=a.dataFilter(b,a.dataType)),i=f,f=k.shift())if("*"===f)f=i;else if("*"!==i&&i!==f){if(g=j[i+" "+f]||j["* "+f],!g)for(e in j)if(h=e.split(" "),h[1]===f&&(g=j[i+" "+h[0]]||j["* "+h[0]])){g===!0?g=j[e]:j[e]!==!0&&(f=h[0],k.unshift(h[1]));break}if(g!==!0)if(g&&a["throws"])b=g(b);else try{b=g(b)}catch(l){return{state:"parsererror",error:g?l:"No conversion from "+i+" to "+f}}}return{state:"success",data:b}}n.extend({active:0,lastModified:{},etag:{},ajaxSettings:{url:jb.href,type:"GET",isLocal:pb.test(jb.protocol),global:!0,processData:!0,async:!0,contentType:"application/x-www-form-urlencoded; charset=UTF-8",accepts:{"*":ub,text:"text/plain",html:"text/html",xml:"application/xml, text/xml",json:"application/json, text/javascript"},contents:{xml:/\bxml\b/,html:/\bhtml/,json:/\bjson\b/},responseFields:{xml:"responseXML",text:"responseText",json:"responseJSON"},converters:{"* text":String,"text html":!0,"text json":n.parseJSON,"text xml":n.parseXML},flatOptions:{url:!0,context:!0}},ajaxSetup:function(a,b){return b?yb(yb(a,n.ajaxSettings),b):yb(n.ajaxSettings,a)},ajaxPrefilter:wb(sb),ajaxTransport:wb(tb),ajax:function(b,c){"object"==typeof b&&(c=b,b=void 0),c=c||{};var e,f,g,h,i,j,k,l,m=n.ajaxSetup({},c),o=m.context||m,p=m.context&&(o.nodeType||o.jquery)?n(o):n.event,q=n.Deferred(),r=n.Callbacks("once memory"),s=m.statusCode||{},t={},u={},v=0,w="canceled",x={readyState:0,getResponseHeader:function(a){var b;if(2===v){if(!h){h={};while(b=ob.exec(g))h[b[1].toLowerCase()]=b[2]}b=h[a.toLowerCase()]}return null==b?null:b},getAllResponseHeaders:function(){return 2===v?g:null},setRequestHeader:function(a,b){var c=a.toLowerCase();return v||(a=u[c]=u[c]||a,t[a]=b),this},overrideMimeType:function(a){return v||(m.mimeType=a),this},statusCode:function(a){var b;if(a)if(2>v)for(b in a)s[b]=[s[b],a[b]];else x.always(a[x.status]);return this},abort:function(a){var b=a||w;return e&&e.abort(b),z(0,b),this}};if(q.promise(x).complete=r.add,x.success=x.done,x.error=x.fail,m.url=((b||m.url||jb.href)+"").replace(mb,"").replace(rb,jb.protocol+"//"),m.type=c.method||c.type||m.method||m.type,m.dataTypes=n.trim(m.dataType||"*").toLowerCase().match(G)||[""],null==m.crossDomain){j=d.createElement("a");try{j.href=m.url,j.href=j.href,m.crossDomain=vb.protocol+"//"+vb.host!=j.protocol+"//"+j.host}catch(y){m.crossDomain=!0}}if(m.data&&m.processData&&"string"!=typeof m.data&&(m.data=n.param(m.data,m.traditional)),xb(sb,m,c,x),2===v)return x;k=n.event&&m.global,k&&0===n.active++&&n.event.trigger("ajaxStart"),m.type=m.type.toUpperCase(),m.hasContent=!qb.test(m.type),f=m.url,m.hasContent||(m.data&&(f=m.url+=(lb.test(f)?"&":"?")+m.data,delete m.data),m.cache===!1&&(m.url=nb.test(f)?f.replace(nb,"$1_="+kb++):f+(lb.test(f)?"&":"?")+"_="+kb++)),m.ifModified&&(n.lastModified[f]&&x.setRequestHeader("If-Modified-Since",n.lastModified[f]),n.etag[f]&&x.setRequestHeader("If-None-Match",n.etag[f])),(m.data&&m.hasContent&&m.contentType!==!1||c.contentType)&&x.setRequestHeader("Content-Type",m.contentType),x.setRequestHeader("Accept",m.dataTypes[0]&&m.accepts[m.dataTypes[0]]?m.accepts[m.dataTypes[0]]+("*"!==m.dataTypes[0]?", "+ub+"; q=0.01":""):m.accepts["*"]);for(l in m.headers)x.setRequestHeader(l,m.headers[l]);if(m.beforeSend&&(m.beforeSend.call(o,x,m)===!1||2===v))return x.abort();w="abort";for(l in{success:1,error:1,complete:1})x[l](m[l]);if(e=xb(tb,m,c,x)){if(x.readyState=1,k&&p.trigger("ajaxSend",[x,m]),2===v)return x;m.async&&m.timeout>0&&(i=a.setTimeout(function(){x.abort("timeout")},m.timeout));try{v=1,e.send(t,z)}catch(y){if(!(2>v))throw y;z(-1,y)}}else z(-1,"No Transport");function z(b,c,d,h){var j,l,t,u,w,y=c;2!==v&&(v=2,i&&a.clearTimeout(i),e=void 0,g=h||"",x.readyState=b>0?4:0,j=b>=200&&300>b||304===b,d&&(u=zb(m,x,d)),u=Ab(m,u,x,j),j?(m.ifModified&&(w=x.getResponseHeader("Last-Modified"),w&&(n.lastModified[f]=w),w=x.getResponseHeader("etag"),w&&(n.etag[f]=w)),204===b||"HEAD"===m.type?y="nocontent":304===b?y="notmodified":(y=u.state,l=u.data,t=u.error,j=!t)):(t=y,!b&&y||(y="error",0>b&&(b=0))),x.status=b,x.statusText=(c||y)+"",j?q.resolveWith(o,[l,y,x]):q.rejectWith(o,[x,y,t]),x.statusCode(s),s=void 0,k&&p.trigger(j?"ajaxSuccess":"ajaxError",[x,m,j?l:t]),r.fireWith(o,[x,y]),k&&(p.trigger("ajaxComplete",[x,m]),--n.active||n.event.trigger("ajaxStop")))}return x},getJSON:function(a,b,c){return n.get(a,b,c,"json")},getScript:function(a,b){return n.get(a,void 0,b,"script")}}),n.each(["get","post"],function(a,b){n[b]=function(a,c,d,e){return n.isFunction(c)&&(e=e||d,d=c,c=void 0),n.ajax(n.extend({url:a,type:b,dataType:e,data:c,success:d},n.isPlainObject(a)&&a))}}),n._evalUrl=function(a){return n.ajax({url:a,type:"GET",dataType:"script",async:!1,global:!1,"throws":!0})},n.fn.extend({wrapAll:function(a){var b;return n.isFunction(a)?this.each(function(b){n(this).wrapAll(a.call(this,b))}):(this[0]&&(b=n(a,this[0].ownerDocument).eq(0).clone(!0),this[0].parentNode&&b.insertBefore(this[0]),b.map(function(){var a=this;while(a.firstElementChild)a=a.firstElementChild;return a}).append(this)),this)},wrapInner:function(a){return n.isFunction(a)?this.each(function(b){n(this).wrapInner(a.call(this,b))}):this.each(function(){var b=n(this),c=b.contents();c.length?c.wrapAll(a):b.append(a)})},wrap:function(a){var b=n.isFunction(a);return this.each(function(c){n(this).wrapAll(b?a.call(this,c):a)})},unwrap:function(){return this.parent().each(function(){n.nodeName(this,"body")||n(this).replaceWith(this.childNodes)}).end()}}),n.expr.filters.hidden=function(a){return!n.expr.filters.visible(a)},n.expr.filters.visible=function(a){return a.offsetWidth>0||a.offsetHeight>0||a.getClientRects().length>0};var Bb=/%20/g,Cb=/\[\]$/,Db=/\r?\n/g,Eb=/^(?:submit|button|image|reset|file)$/i,Fb=/^(?:input|select|textarea|keygen)/i;function Gb(a,b,c,d){var e;if(n.isArray(b))n.each(b,function(b,e){c||Cb.test(a)?d(a,e):Gb(a+"["+("object"==typeof e&&null!=e?b:"")+"]",e,c,d)});else if(c||"object"!==n.type(b))d(a,b);else for(e in b)Gb(a+"["+e+"]",b[e],c,d)}n.param=function(a,b){var c,d=[],e=function(a,b){b=n.isFunction(b)?b():null==b?"":b,d[d.length]=encodeURIComponent(a)+"="+encodeURIComponent(b)};if(void 0===b&&(b=n.ajaxSettings&&n.ajaxSettings.traditional),n.isArray(a)||a.jquery&&!n.isPlainObject(a))n.each(a,function(){e(this.name,this.value)});else for(c in a)Gb(c,a[c],b,e);return d.join("&").replace(Bb,"+")},n.fn.extend({serialize:function(){return n.param(this.serializeArray())},serializeArray:function(){return this.map(function(){var a=n.prop(this,"elements");return a?n.makeArray(a):this}).filter(function(){var a=this.type;return this.name&&!n(this).is(":disabled")&&Fb.test(this.nodeName)&&!Eb.test(a)&&(this.checked||!X.test(a))}).map(function(a,b){var c=n(this).val();return null==c?null:n.isArray(c)?n.map(c,function(a){return{name:b.name,value:a.replace(Db,"\r\n")}}):{name:b.name,value:c.replace(Db,"\r\n")}}).get()}}),n.ajaxSettings.xhr=function(){try{return new a.XMLHttpRequest}catch(b){}};var Hb={0:200,1223:204},Ib=n.ajaxSettings.xhr();l.cors=!!Ib&&"withCredentials"in Ib,l.ajax=Ib=!!Ib,n.ajaxTransport(function(b){var c,d;return l.cors||Ib&&!b.crossDomain?{send:function(e,f){var g,h=b.xhr();if(h.open(b.type,b.url,b.async,b.username,b.password),b.xhrFields)for(g in b.xhrFields)h[g]=b.xhrFields[g];b.mimeType&&h.overrideMimeType&&h.overrideMimeType(b.mimeType),b.crossDomain||e["X-Requested-With"]||(e["X-Requested-With"]="XMLHttpRequest");for(g in e)h.setRequestHeader(g,e[g]);c=function(a){return function(){c&&(c=d=h.onload=h.onerror=h.onabort=h.onreadystatechange=null,"abort"===a?h.abort():"error"===a?"number"!=typeof h.status?f(0,"error"):f(h.status,h.statusText):f(Hb[h.status]||h.status,h.statusText,"text"!==(h.responseType||"text")||"string"!=typeof h.responseText?{binary:h.response}:{text:h.responseText},h.getAllResponseHeaders()))}},h.onload=c(),d=h.onerror=c("error"),void 0!==h.onabort?h.onabort=d:h.onreadystatechange=function(){4===h.readyState&&a.setTimeout(function(){c&&d()})},c=c("abort");try{h.send(b.hasContent&&b.data||null)}catch(i){if(c)throw i}},abort:function(){c&&c()}}:void 0}),n.ajaxSetup({accepts:{script:"text/javascript, application/javascript, application/ecmascript, application/x-ecmascript"},contents:{script:/\b(?:java|ecma)script\b/},converters:{"text script":function(a){return n.globalEval(a),a}}}),n.ajaxPrefilter("script",function(a){void 0===a.cache&&(a.cache=!1),a.crossDomain&&(a.type="GET")}),n.ajaxTransport("script",function(a){if(a.crossDomain){var b,c;return{send:function(e,f){b=n("<script>").prop({charset:a.scriptCharset,src:a.url}).on("load error",c=function(a){b.remove(),c=null,a&&f("error"===a.type?404:200,a.type)}),d.head.appendChild(b[0])},abort:function(){c&&c()}}}});var Jb=[],Kb=/(=)\?(?=&|$)|\?\?/;n.ajaxSetup({jsonp:"callback",jsonpCallback:function(){var a=Jb.pop()||n.expando+"_"+kb++;return this[a]=!0,a}}),n.ajaxPrefilter("json jsonp",function(b,c,d){var e,f,g,h=b.jsonp!==!1&&(Kb.test(b.url)?"url":"string"==typeof b.data&&0===(b.contentType||"").indexOf("application/x-www-form-urlencoded")&&Kb.test(b.data)&&"data");return h||"jsonp"===b.dataTypes[0]?(e=b.jsonpCallback=n.isFunction(b.jsonpCallback)?b.jsonpCallback():b.jsonpCallback,h?b[h]=b[h].replace(Kb,"$1"+e):b.jsonp!==!1&&(b.url+=(lb.test(b.url)?"&":"?")+b.jsonp+"="+e),b.converters["script json"]=function(){return g||n.error(e+" was not called"),g[0]},b.dataTypes[0]="json",f=a[e],a[e]=function(){g=arguments},d.always(function(){void 0===f?n(a).removeProp(e):a[e]=f,b[e]&&(b.jsonpCallback=c.jsonpCallback,Jb.push(e)),g&&n.isFunction(f)&&f(g[0]),g=f=void 0}),"script"):void 0}),n.parseHTML=function(a,b,c){if(!a||"string"!=typeof a)return null;"boolean"==typeof b&&(c=b,b=!1),b=b||d;var e=x.exec(a),f=!c&&[];return e?[b.createElement(e[1])]:(e=ca([a],b,f),f&&f.length&&n(f).remove(),n.merge([],e.childNodes))};var Lb=n.fn.load;n.fn.load=function(a,b,c){if("string"!=typeof a&&Lb)return Lb.apply(this,arguments);var d,e,f,g=this,h=a.indexOf(" ");return h>-1&&(d=n.trim(a.slice(h)),a=a.slice(0,h)),n.isFunction(b)?(c=b,b=void 0):b&&"object"==typeof b&&(e="POST"),g.length>0&&n.ajax({url:a,type:e||"GET",dataType:"html",data:b}).done(function(a){f=arguments,g.html(d?n("<div>").append(n.parseHTML(a)).find(d):a)}).always(c&&function(a,b){g.each(function(){c.apply(this,f||[a.responseText,b,a])})}),this},n.each(["ajaxStart","ajaxStop","ajaxComplete","ajaxError","ajaxSuccess","ajaxSend"],function(a,b){n.fn[b]=function(a){return this.on(b,a)}}),n.expr.filters.animated=function(a){return n.grep(n.timers,function(b){return a===b.elem}).length};function Mb(a){return n.isWindow(a)?a:9===a.nodeType&&a.defaultView}n.offset={setOffset:function(a,b,c){var d,e,f,g,h,i,j,k=n.css(a,"position"),l=n(a),m={};"static"===k&&(a.style.position="relative"),h=l.offset(),f=n.css(a,"top"),i=n.css(a,"left"),j=("absolute"===k||"fixed"===k)&&(f+i).indexOf("auto")>-1,j?(d=l.position(),g=d.top,e=d.left):(g=parseFloat(f)||0,e=parseFloat(i)||0),n.isFunction(b)&&(b=b.call(a,c,n.extend({},h))),null!=b.top&&(m.top=b.top-h.top+g),null!=b.left&&(m.left=b.left-h.left+e),"using"in b?b.using.call(a,m):l.css(m)}},n.fn.extend({offset:function(a){if(arguments.length)return void 0===a?this:this.each(function(b){n.offset.setOffset(this,a,b)});var b,c,d=this[0],e={top:0,left:0},f=d&&d.ownerDocument;if(f)return b=f.documentElement,n.contains(b,d)?(e=d.getBoundingClientRect(),c=Mb(f),{top:e.top+c.pageYOffset-b.clientTop,left:e.left+c.pageXOffset-b.clientLeft}):e},position:function(){if(this[0]){var a,b,c=this[0],d={top:0,left:0};return"fixed"===n.css(c,"position")?b=c.getBoundingClientRect():(a=this.offsetParent(),b=this.offset(),n.nodeName(a[0],"html")||(d=a.offset()),d.top+=n.css(a[0],"borderTopWidth",!0),d.left+=n.css(a[0],"borderLeftWidth",!0)),{top:b.top-d.top-n.css(c,"marginTop",!0),left:b.left-d.left-n.css(c,"marginLeft",!0)}}},offsetParent:function(){return this.map(function(){var a=this.offsetParent;while(a&&"static"===n.css(a,"position"))a=a.offsetParent;return a||Ea})}}),n.each({scrollLeft:"pageXOffset",scrollTop:"pageYOffset"},function(a,b){var c="pageYOffset"===b;n.fn[a]=function(d){return K(this,function(a,d,e){var f=Mb(a);return void 0===e?f?f[b]:a[d]:void(f?f.scrollTo(c?f.pageXOffset:e,c?e:f.pageYOffset):a[d]=e)},a,d,arguments.length)}}),n.each(["top","left"],function(a,b){n.cssHooks[b]=Ga(l.pixelPosition,function(a,c){return c?(c=Fa(a,b),Ba.test(c)?n(a).position()[b]+"px":c):void 0})}),n.each({Height:"height",Width:"width"},function(a,b){n.each({padding:"inner"+a,content:b,"":"outer"+a},function(c,d){n.fn[d]=function(d,e){var f=arguments.length&&(c||"boolean"!=typeof d),g=c||(d===!0||e===!0?"margin":"border");return K(this,function(b,c,d){var e;return n.isWindow(b)?b.document.documentElement["client"+a]:9===b.nodeType?(e=b.documentElement,Math.max(b.body["scroll"+a],e["scroll"+a],b.body["offset"+a],e["offset"+a],e["client"+a])):void 0===d?n.css(b,c,g):n.style(b,c,d,g)},b,f?d:void 0,f,null)}})}),n.fn.extend({bind:function(a,b,c){return this.on(a,null,b,c)},unbind:function(a,b){return this.off(a,null,b)},delegate:function(a,b,c,d){return this.on(b,a,c,d)},undelegate:function(a,b,c){return 1===arguments.length?this.off(a,"**"):this.off(b,a||"**",c)},size:function(){return this.length}}),n.fn.andSelf=n.fn.addBack,"function"==typeof define&&define.amd&&define("jquery",[],function(){return n});var Nb=a.jQuery,Ob=a.$;return n.noConflict=function(b){return a.$===n&&(a.$=Ob),b&&a.jQuery===n&&(a.jQuery=Nb),n},b||(a.jQuery=a.$=n),n});

    </script>
    <script>if (typeof module === 'object') {
            window.jQuery = window.$ = module.exports;
        }
        ;</script>

    <!-- script -->
    <script type="text/javascript" >
    $(function() {

    	var Accordion = function(el, multiple) {
    		this.el = el || {};
    		this.multiple = multiple || false;

    		// Variables privadas
    		var links = this.el.find('.link');
    		// Evento
    		links.on('click', {el: this.el, multiple: this.multiple}, this.dropdown)
    	}

    	Accordion.prototype.dropdown = function(e) {
    		var $el = e.data.el;
    			$this = $(this),
    			$next = $this.next();

    		$next.slideToggle();
    		$this.parent().toggleClass('open');

    		if($this.parent().hasClass("open")){
    			$this.context.children[1].style.display = "none"
    			$this.context.children[2].style.display = "block"
    		} else {
    			$this.context.children[1].style.display = "block"
    			$this.context.children[2].style.display = "none"
    		}

    		if (!e.data.multiple) {
    			$el.find('.submenu').not($next).slideUp().parent().removeClass('open');
    		};
    	}

    	var accordion1 = new Accordion($('#accordion'), false);
    	var accordion2 = new Accordion($('#accordion_2'), false);
    	var accordion3 = new Accordion($('#accordion_3'), false);
    	var accordion4 = new Accordion($('#accordion_4'), false);
    	var accordion5 = new Accordion($('#accordion_5'), false);
    	var accordion6 = new Accordion($('#accordion_6'), false);
    	var accordion7 = new Accordion($('#accordion_7'), false);
    	var ac_1 = new Accordion($('#ac_1'), false);
    	var ac_2 = new Accordion($('#ac_2'), false);



    	/**
    	 *
    	 * 二级菜单
    	 */
    	 var SecondaryMenu = function(el, multiple) {
    		this.el = el || {};
    		this.multiple = multiple || false;

    		// Variables privadas
    		var links = this.el.find('.secondary-menu');
    		// Evento
    		links.on('click', {el: this.el, multiple: this.multiple}, this.dropdown)
    	}

    	SecondaryMenu.prototype.dropdown = function(e) {
    		var $el = e.data.el;
    			$this = $(this),
    			$next = $this.next();

    		$next.slideToggle();
    		$this.parent().toggleClass('open');

    		if (!e.data.multiple) {
    			$el.find('.submenu').not($next).slideUp().parent().removeClass('open');
    		};
    	}

    	var accordion3 = new SecondaryMenu($('#secondaryMenu'), false);
    	var accordion4 = new SecondaryMenu($('#secondaryMenu1'), false);
    });

    </script>
</head>
<body>
<#if code == "1">
<div class="container">
    <div class="header">
        <span class="header-title"></span>
    </div>
    <div class="content">
        <div class="content-inner">
            <!-- DIP入组情况 -->
            <div class="in-group-circumstance">
                <div class="hop-title">
                    <#if hospName??>
                        ${hospName}-AI小助手
                    <#else >
                        测试医院-AI小助手
                    </#if>
                </div>
                <div class="divider"></div>
                <div class="title">
                    <img src="data:image/png;base64,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" height="35px" width="35px"/>
                    <span>AI病例入组</span>
                </div>
                <div class="divider"></div>
                <div class="in-group-content">
                    <div class="in-group-base-info">
                        <div class="in-group-base">
                            <div>
                                <img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAAZRJREFUWEftlu1RwzAMhi13MCg9FqDHAJRB2g5CGYArC3ClYbDanEucUxTLktL04Af5G9t69PVK4H75g7H2X98+btPdp8f7Zuwb6Z4Z4GV/2IBza2o0Ord9Xi42Vhg1QPI4zmZrF+PZc+6zgqgBdvtD7BkFaGKMXwBwQ6EghLk2NSqAXtgBGjidttQAPbN6uJtr0iECnEPv/TE/tlou2Du7989jjoY2CiIA9kzKL4aVzmaHTACSV71oATSaNIgAlrBeBcDyqCVd6hTQIqylAbfqZDWQSKn60cepSGmNm6QY10LX3wA/c4CoY61VqTaIRYgvcHMAA5VEqiZIJoAuHVR+W1m+6jAqeZFyr9V8LgpiBPLcD95XpyA14ENoNHAsgHb8SqNZAikCiMWmGXPoTE07igCDlmtHsMZuThXdmrjWHACMneslOOwIJ05VAIuicV3S7RLMdBwAYGqLonHpkd4bAqDdb2qAUjHyAMqFQipMaZ/4B/iDEWhX60tbMNcG3qhKRV1UwimmHC7O2nviNJSq/NL/3zGvUzDSoe3MAAAAAElFTkSuQmCC" height="15px" width="15px"/>
                                <span>${xm}</span>
                            </div>

                            <div>
                                <img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAAe1JREFUWEftlm1OAkEMhtvlIhzBGwASLyDxt4oHAU7gCUT5bfACBllvwkXYmk7o2O3Ol4SEmLAJ2Sy7bZ/pxzuDcOYLzxwfLgD/IwOv75/Dx7ub+i/9UmoTzcDLejNHxAEQDX1gxBr3+0UOxtkCzNgOm2aU+j4IoB244Ii1BiGAxdNkPA9lpGObgegAaAc2UOodwwTAd0DUT2WiBcB1o6rapgx0kOlk7O1bwYlWgHjPmSOi71Q5wgCI9fT2ehRruuXH15ZLIhmymamapnYLOfiJQbuF6iDWcQxAHAqAZM4+C4AvD+LALqwFYB2XAtjvfCkzmexkoBRAMhUbsaMBmGi53hDfY6OWm3EXvNd7BqIr8cP32Nhmx5AbioWEHTdVNZSOtoCHwLOWcKnaxBZUJkSmyCFnkjluPFZLMdHQuinlfVSKQyviwJIRzZTrnZS+ZHfDnHPdN1qY7GT43jGT0RWiXs9tIurqOzlF3AEA//zFKudFBwBSALHJCOpAbP7t/1ISrXopW+kTDdopAZO2Von44HSdaIVEb/qdbLMhx0eVIERf1ANmbwj5ienLSZpQd3lSHwLSfBIAv9kcTkEuA3yIcVL4e6IKSXcWwO50qSaLqmFiU8oCcMDSA6aGk2bOnR+LAErH8pjvLgCXDPwACv+vMMR6jXMAAAAASUVORK5CYII=" height="15px" width="15px"/>
                                <span>${xb}</span>
                            </div>
                            <div>
                                <img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAAfVJREFUWEfFV21uwjAMrcvBVoZ2gaEdYHAQ4CBjB5jYBSZGdzCayRmOHOM0Tigaf1AhzXv28yc0BZ/3j69uaNsOAB78a851/hugp2vgfN69vjyF59z1kDuA/78djltomo3lbDgD0FvIjBJQgQF659wPArXDECwNniGvBJdAv3p+nKfIJwlIcNc0u/VysbV4AaVys9mGS5TyxhUB/3LbngioBFiSk0bAMMxlfFwR2H9+n4j5LeBERhq0Wi4izOiBg2tsLe7XzkQkII6JQIC7awrLJRFOgt8fCOwPR0c5PRa13LUl+S7TmaTwBLj1FtfTectZ6QkylLzgCQTthT4pzel8jVTcWPQCcG2sFt1CwBt8kRsNAMnIEum3EiBMT6DU/VyyGgmimMN+UWNNzTvcs0F2TgBbqiX9pvBAFHf/IcFkBOQwogWw1gUjCUIWVEhgyRgtUDlmVRrSaGYhgEOLLNlc9r9KyAqDdeiwgCcr6QUPC1/cCwpkqCVwVYpTXaoWIPee2ow4CWs/yAFp/2tlP8wDFFj3ioHUwGPaC2qsVUsv7jJiur47gWgyVoI8SwClQWtKxy8Z3KnOOUpATrOWVQstRnC+yo217awH+KgedL0so7Si0e9yf7TMC1kCeHlYtfBB7n5KhCKwVoLVZlUa4WN9oCaFfwGZB8F+xUtTwwAAAABJRU5ErkJggg==" height="15px" width="15px"/>
                                <span>${nl}</span>
                            </div>
                            <div style="margin-right: 1rem;">
                                <img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAAWZJREFUWEfdl2FuwjAMheNwsMEQFxjaAcYOAjsIcAAEF0CM7mLJ5KquHBTHSZWqUvmHGvo+Pzu2ATPxBybWN4MBzpf7EuGdtcvv7fowNJBBACjurX1yUXBu9fW5aUpBigECcYDGe/8HxuxR2BvzU+pGEcDp+vAUIRc73X6fxvs2JQag2X28r3KdSAK00S4W+/7lnQBGbZ1ryHI8h7UAAG/B2QxXRIDj9XEga4NoOttjEcYAtNSoAKLVkscsBX0QibSIAJTX1+qm6yfpv94Eqpvddh3VKgbgwhghfk9V/qgAkkscchSAWCOSesA8AcjiyVJAAHQjUjNglBTktlk8VxVA7I7BWAxnwbwASqzvC7WboNU6odaKUZgX5bxSQHNfSwWfDVUd0IRjz6sC5DjAN6X59YGcG1D9FuCWS0toaQ3QQpta15NbMV/DS8X5eakJtUu29mJau7Rz0nPtj4oKMFQ493eTA/wDuLi3MBRLzskAAAAASUVORK5CYII=" height="15px" width="15px"/>
                                <span>${medcasCodg}</span>
                            </div>

                            <div>
                                <img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAAbRJREFUWEfFlm1SwjAQhrPlYICMF5DxAOJBhIOIB3DwAg4SD0bjvCGLafqxSdpM86cUhn2fzX6SmvnQzPoqC+Dj83tVV9WKiJbGmF848brd7HOcEQEgBsNmsXizAsbY995DpFOgWgC+d6JYrMsOquuWGgDvp/OelLp5WugYpQ4+SAPgeDqbQrr/Zon07ulhzV/cAXD1pqouxQGUUlTX65fnRw2tdAAibXMDz74jJOoogN12Q8iVqq41exFySKHMBuAE8gHwmQEYqjgABNgLX4wBiwD45eMDdKVBEQDEnsUgACB+R11zA3OZPdhL0nMgqF0fgOPuA0gQ6QDW4q3Hw9vj18/FbyZhGKSekgeAOeTa6D3Gwc3EtvJpAJw4vEU/4GcMxGgAiCAUVoxoiXBwP+BBgzD1TdPRADCOfLCT090Ee87VMTRVOwGQSFL9ciK2jPNckJYVl61+STfH8cC1TTUlB/cBqXymgBgEgEBMFueChOKNfcA3WgKiS7wXgGEmASHSdL0e+nYHcS3n+k5eVgXh1k4oxTX6NiKFkwHEsCQKZwP4IOEqJt1i1+9iDuQYTfnP7AB/BP6SMI58cnwAAAAASUVORK5CYII=" height="15px" width="15px"/>
                                <span>${cblx}</span>
                            </div>
                            <div>
                                <img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAAbRJREFUWEfFlm1SwjAQhrPlYICMF5DxAOJBhIOIB3DwAg4SD0bjvCGLafqxSdpM86cUhn2fzX6SmvnQzPoqC+Dj83tVV9WKiJbGmF848brd7HOcEQEgBsNmsXizAsbY995DpFOgWgC+d6JYrMsOquuWGgDvp/OelLp5WugYpQ4+SAPgeDqbQrr/Zon07ulhzV/cAXD1pqouxQGUUlTX65fnRw2tdAAibXMDz74jJOoogN12Q8iVqq41exFySKHMBuAE8gHwmQEYqjgABNgLX4wBiwD45eMDdKVBEQDEnsUgACB+R11zA3OZPdhL0nMgqF0fgOPuA0gQ6QDW4q3Hw9vj18/FbyZhGKSekgeAOeTa6D3Gwc3EtvJpAJw4vEU/4GcMxGgAiCAUVoxoiXBwP+BBgzD1TdPRADCOfLCT090Ee87VMTRVOwGQSFL9ciK2jPNckJYVl61+STfH8cC1TTUlB/cBqXymgBgEgEBMFueChOKNfcA3WgKiS7wXgGEmASHSdL0e+nYHcS3n+k5eVgXh1k4oxTX6NiKFkwHEsCQKZwP4IOEqJt1i1+9iDuQYTfnP7AB/BP6SMI58cnwAAAAASUVORK5CYII=" height="15px" width="15px"/>
                                <span>${ybjs}</span>
                            </div>
                        </div>
                        <div class="in-group-content">
                            <div class="in-group-base-info">
                                <div class="in-group-base">
                                    <div>
                                        <span>结算总费用：${sumfee?default(0)}</span>
                                    </div>
                                    <div>
                                        <span>医保总费用：${ybzfy?default(0)}</span>
                                    </div>
                                    <div>
                                        <span>住院费用：${inHosCost?default(0)}</span>
                                    </div>
                                    <div>
                                        <span>院前检查费：${preHospExamfee?default(0)}</span>
                                    </div>
                                </div>

                            </div>
                            <dic>
                                <div class="in-group-diagnose">
                                    <div class="in-group-diagnose-details">
                                        <img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAAfFJREFUWEftV2tSwkAM3pSDSWW8gIwHEA4CHEQ8gIMXcJB6MHadrM1Omm72Uevwx/6B2Uf65cvjS8Gw5/XtY2mbZolLAHDnf6/Xw/PTQ8fPvZzOezBm54w50HpjbSfP8Tvaf8ANMhg9BNBtHu9bvnc8nZ12NgY4BQTQa9c0l3AI4Mdb5zwT6OV2vdrHGDB0lp3HNQk4CYB7D9a2nEYEp9Eq91J2kgCIzpinJTEchOb98+KZq2AB/gEce9rmDEGNLSAANXHTcmNKOIFn72a98n1h6jMJAO8DsgxrgEx1ZNCIbgIAvZwjD6bQ77VmAMAYMyUPOP01FRAA8DyoNSDFrNaBkPUhDIoAaQn5G+8DA2RcgsB11PmBWmLcrG3dYrHLqWZJFQ3q3ocCDfdSXGJAk+zSu9HG42nFiSgHBKCLDSA0WaEN59wXgpEzBQHMdr7RwJKpFB5GyUIswbMA0EhptxyNdgAdMoDzY/BYDD2zAciVstbsZgOQ0wJtf34AiXGM2jXXnNkAlOjJzQDEKokq4s8ZkMnZt19fFRiKIgBeMfuvIW1m0OSYf8ZRMwqgsJGVtsxUjFOCRPck8OBQKQAZR4yhFyquHZEK4N7SJxtnpZgBqfsj4IouyIGH38PZoQoAgeCJhOM8tltNbOiFQeD6BRKxb8RrE9v6IxUcAAAAAElFTkSuQmCC" height="15px" width="15px"/>
                                        <#if zyzd?? && (zyzd?size > 0)>
                                            <#list zyzd as zyzdbm>
                                                <#if zyzdbm_index=0 >
                                                    <span>${zyzdbm}</span>
                                                    <span>${zyzdmc}</span>
                                                </#if>
                                            </#list>
                                        </#if>
                                    </div>
                                    <div class="in-group-diagnose-details">
                                        <img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAYAAABXAvmHAAAAAXNSR0IArs4c6QAAAcJJREFUaEPtV21uwjAMTeBiMLQLDO0AYwcBDjJ2gIldYGL0YpBp1Vq1nZPYsZ2AlP5tPt7zs19sa+78s3eO31QCpRWsClQFmBGoKcQMIHt7VYAdwskBb8fTzhqzdcbsX9erXez8m1Lg8Pl9Ns4tetDWNpunh2WIxM0Q+Ae+Qx0hUZzA+8fXws1m51CUQ+lUlAAGfC/E9bp8eX5spkSLEeiKNVakw/8WIFGEQAr4lghQD9kJJIP/k2KzXo0wZyXgdRpkHkHFnI2ABvg2q5Dkk5e1TjOfb0cPFOU0axt7uewhB1InQLFJiBOmnVBTgFWskaiPrJWiJnYtBzzk9Vl7oWTwiMYNIiKaQkngCemiSiAFPKZIY2krpgDJ55lRVylirGVKRF2EQPdADSemw/HkvJInFqlKCo2iPQAG1oFguogUMQRy6N29CsrA+0EnJtHwv9dpJunxq5Cvd6Hch1mLdqGgTSrltxgBjEVOBw3M5RJrggpgW2Fpa6QQ8xLA+jo0p1IAcNeCBNBtQcHc97pQrFjbKSgwIXEjSt0frYFcdkgFnvQOpF6iuQ/9DmiC4JxdCXCiJ7G3KiARRc4ZVQFO9CT2/gBLIuMxHA8xCAAAAABJRU5ErkJggg==" height="15px" width="15px"/>
                                        <div class="operation" id="operation">
                                            <div>
                                                <#if zyss?? && (zyss?size > 0)>
                                                    <#list zyss as zyssbm>
                                                        <span>${zyssbm}</span>
                                                        <#list ssjczmc as zyssmc>
                                                            <#if zyssmc_index= zyssbm_index>
                                                                <span>${zyssmc}</span>
                                                                </br>
                                                            </#if>
                                                        </#list>
                                                    </#list>
                                                <#else>
                                                    <span>暂无手术</span>
                                                </#if>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                        </div>
                        <div class="in-group-dip-group">
                            <img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEAAAABACAYAAACqaXHeAAAAAXNSR0IArs4c6QAAAgFJREFUeF7tW9FRwzAMfc4E0GOF/LMB6TiULyagTMAXZZyWDfjPClxhAWLOHL1rUyeWHMfXtC9/uchy/Cw9SbFikPiarWqbWOWBuu2iNCn1J1XmXowA0ALoAuSAI5KyeC4KbDTk1TSoYPDUHjNJEiwM5p/3pQqAm7e6aizWBKCFAC1ginkAXYAcQBJkFGAYZB7ARIiZoKYWOKtUGJdeDGl2PiQ7yVogtCjNcwIwxWJIs8MhWVrAqVtAaAfd8+tVvTZAtS9rgc3XopxLxqeUSX4uIHk5AkALoAuQAyZJgq5A+bHHhxUS4tuXMRa3MLg6GGfxbQ0+tLpaet/d/fahXEr1iKOAj7mlk+SW04RUEQCz13rpO6bKvTDNfFIQggBMcfE7oCTnEWcNgMQKwgB4Gh6cYmPxRzinclmDu5jIEgWA+8KjYdocIMV+QosCQOJbORa9PwcBiOwnoAWETNXX9kYXiDjuDgE99Hk0B7iBfZP7+nQAPBY9ebv2IFS6+L53bVx9Aby0dTlr7dNvxujsHMNFunZYCl6XHAGgBYzQ20sXGCFKZOUAzQ7myhNiw9yO/LrGe0mQAChMmBbgIVGNBUnjOF0gstojB/wjQBLssCBGgaEsPnQ8SVCIQNYooOnr830vyBkGQ/X+Dt+un7BYDrMcZjmc/lfXnBwg5NBOsYvngF9TY57P0UFVcAAAAABJRU5ErkJggg==" height="30px" width="30px"/>
                            <#if dipCodg?? && dipName??>
                                <div>
                                    <p>
                                        ${dipCodg}
                                    </p>
                                    <p>
                                        ${dipName}
                                    </p>
                                </div>
                            <#else>
                                <div>
                                    <p style="color: red;text-align: center;font-weight: bold">${rzqk}${rzqkxx}</p>
                                </div>
                            </#if>
                        </div>
                        <!--辅助目录信息-->
                        <#if auxiliaryAge?? && auxiliaryIllness?? && auxiliaryTumour??>
                            <div class="in-group-base">
                                <div>
                                    烧伤:<span>${auxiliaryBurn}</span>
                                </div>
                                <div>
                                    年龄:<span>${auxiliaryAge}</span>
                                </div>
                                <div>
                                    疾病:<span>${auxiliaryIllness}</span>
                                </div>
                                <div>
                                    肿瘤:<span>${auxiliaryTumour}</span>
                                </div>

                            </div>
                        </#if>
                    </div>

                    <!-- 不参与分组手术 -->
                    <#if dyNotGroupOpe ?? && (dyNotGroupOpe ?size >0)>
                        <div class="group-recommend-content">
                            <ul id=accordion_4 class="accordion">
                                <li>
                                    <div class="link">
                                        <img src="data:image/png;base64,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" height="25px" width="25px"
                                             class="des-icon"/>
                                        <div class="sfq-header">
                                            <p style="width: 48%;">
                                                不参与分组手术
                                            </p>
                                        </div>
                                        <div class="sfq-header" style="display: none">
                                            <p style="width: 48%;">不参与分组手术</p>
                                        </div>
                                        <i class="fa fa-chevron-down"></i>
                                    </div>
                                    <ul class="submenu">
                                        <div class="sfq-header">
                                            <#if dyNotGroupOpe ?? && (dyNotGroupOpe ?size >0)>
                                                <#assign Oper_index = 0>

                                                <#list dyNotGroupOpe as dyfj>
                                                    <li>
                                                        <a href="#">
                                                            <div style="display: flex;">
                                                                 <#assign Oper_index = Oper_index + 1>
                                                            <span style="width: 50%;font-size: 12px;">
                                                                   ${Oper_index + "、"} ${dyfj}
                                                            </span>
                                                            </div>
                                                        </a>
                                                    </li>
                                                </#list>
                                            </#if>
                                        </div>
                                    </ul>
                                </li>
                            </ul>
                        </div>
                    </#if>

                    <!-- 德阳限定 -->
                    <#if dyLimited ?? && (dyLimited ?size >0)>
                        <div class="group-recommend-content">
                            <ul id=accordion_6 class="accordion">
                                <li>
                                    <div class="link">
                                        <img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAAf9JREFUWEfNV9FRwzAMtdLBoPRYgB4DUAahHYQyAFcW4ErDYHU4uZEr23KkNB8hf44T6elJfpLBzfzAzP7dKAAfn9/3CLhbLN4C8K4LawfQXpbdb+N9+/L8GNaWxwQAHQen5FCx3Dm3w09e16utBkIFsP/6OeWRwvkcHFCkCNA3TWADnLuwg4w4t9NAVAGEqJvmNMYYfft+OG45EPB+WUuLCCBxDtBunh6WGpXSPgdSAyEC2B+OnZVCDRgHsVmvCn/FC/rBkr/cOTInUR3rSGAzAaChJWYkOjW6a/+KAKTo86LkIPI9ieoIMGMhAuBGJANId1LdzBBFp9UNpYKDjwCsuc9BoPrRkdPqZhAAbWpGMMpEnEaITmSZsRcZkNDxKqc+EIWJiVRQQO+jVtREZxhAf/Zr+ed51s4+7mt2aP/KgAIAc88dA8Adb07UgPCboY5IgZQA+qYzpNscQN6kLHI9qQakwqulolbIkhaUOlBpPuFnpD1WYT+M0LofSmgpMSId9VFCNDUFef77+eFq9mYtMLTsWp9JeoFFjqWhwyRelRZftOOh1im1X3ynDaFDMv//BhKMiKfCQm/tOPKjO2oky0FobTYHMHko5QZzYyS5XG6TCwu/OxhOh3ovKAYRSycCaPHuoBVnoQMW24kiZlez/MJisWdiwGLo1m9mB/AHxPfeMKHprvQAAAAASUVORK5CYII=" height="25px" width="25px"
                                             class="des-icon"/>
                                        <div class="sfq-header">
                                            <p style="width: 48%;">
                                                ${dyGroupType}限定
                                            </p>
                                        </div>
                                        <div class="sfq-header" style="display: none">
                                            <p style="width: 48%;">${dyGroupType}限定</p>
                                        </div>
                                        <i class="fa fa-chevron-down"></i>
                                    </div>
                                    <ul class="submenu">
                                        <div class="sfq-header">
                                            <#if dyLimited ?? && (dyLimited ?size >0)>
                                                <#list dyLimited as dyfj>
                                                    <li>
                                                        <a href="#">
                                                            <div style="display: flex;">
                                                            <span style="width: 50%;font-size: 12px;">
                                                                    手术及操作编码: ${dyfj}
                                                            </span>
                                                            </div>
                                                        </a>
                                                    </li>
                                                </#list>
                                            </#if>
                                        </div>
                                    </ul>
                                </li>
                            </ul>
                        </div>
                    </#if>

                    <div class="divider"></div>

                    <!-- DIP付费预测 -->
                    <div class="pre-pay">
                        <div class="title">
                            <img src="data:image/png;base64,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" height="35px" width="35px"/>
                            <span>AI付费预测</span>
                        </div>
                        <div class="divider"></div>
                        <div class="pre-pay-content" id="pre-pay-content-id">
                            <div class="tooltip__popper is-dark" id="tooltip"
                                 style="transform-origin: center bottom;z-index: 2137;top: -10px;">
                                总费用:${sumfee?default(0)}<#if fee_percentage??>/${fee_percentage}%</#if>
                                <div class="popper__arrow"></div>
                            </div>
                            <div class="pre-pay-content-range">
                                <div class="low range-item" id="range-min">
                                    <div class="range-money" style="right: 50%"><${lowlmtMag}%</div>
                                    <div class="range-money" style="right: -20%;top: 100%">
                                        <#if lowLmtfee??>
                                            ${lowLmtfee?default(0)}
                                        </#if>
                                    </div>
                                </div>
                                <div class="normal range-item" id="range-min-normal">
                                    <div class="range-money-normal" style="right: 26%">${lowlmtMag}%~100%</div>
                                    <div class="range-money-normal" style="top: 100%">
                                        <#if normfee??>
                                            ${normfee}
                                        </#if>
                                    </div>
                                </div>
                                <div class="normal-to-high range-item" id="range-normal-max">
                                    <div class="range-money" style="right: 36%">100%~${uplmtMag}%</div>
                                    <div class="range-money" style="right: -11%;top: 100%">
                                        <#if upLmtfee??>
                                            ${upLmtfee?default(0)}
                                        </#if>
                                    </div>
                                </div>
                                <div class="high range-item" id="range-max">>${uplmtMag}%</div>
                            </div>

                            <ul id="${dipCodg???string('accordion','ac_1')}" class="accordion">
                                <li>
                                    <div class="link">
                                        <img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAYAAABXAvmHAAAAAXNSR0IArs4c6QAAAqZJREFUaEPtWlFOwzAMbbqDMZi4ABMHYByE7SCMA6BxATTWHWwNSlir1rUTN30ZQ2KfbeK+Zzt+cTJTBH5v759zO5u9+CHWzkNj4e+Mqay1R2f3eblYS/aN9GL78XW4OGgBjC2KjUSCJbDd7S3co1MNGlOtHu5uqZkBgdfdfm2K4idtml8nnFNxjJlPcXCRGBDoeV9gPQbE1LE0lU1d3z493letb7sfoN6ng6eCSZnvC0lZHlrAYwislgtxkaeASZ3TzQqaRj2AvQhcQfo0hLtpBCfgjXuZsMdQvU71vpuXjUAvPzNGLAuBVqUbhT6X2lgUeurOhIWLJJTAADgHQlBOVmMU86EEtCrNlWBaEqV1QRcqjIDWg1LNds+9DWNuRPBMMYAR4LzfeFragnD7l7EVCUKAC38o1IUxlTmdNl3ZHwscqgNs/jKl00WirOsKARxKwAuKsM0O7ddTvd6dB0khqogcsFxEYARSy2CXrLNRl2WwPaViCCPggGhJuEVMK5C2DFMNgRJovKkCQ0hoyWcTMi7vHRH3fNCGngfTniImZG4ajRw0As6L7iNcmeROMhBdHYTAAByT43+LgAs1aTk5nUC0pZAISAvQLTi/OWNO7lC6ACGgETK6yBH5D28ptf2A5P1fF7J2T09P79omQN6FqrTDlWRy9gNLIZoiY5v6qxOysQSi0fOKONyCXCQCqIrDqX02Aoi9vsbGPwGNl3KOgUXgItdOzGEAhIC2hkMiQSoRhIC2hiMIZGtomuYFAVKywR3JQCKQE3TMdhoBZr8f+1Cu9+orptiFWi6AIbsUU/CKabBQM966aJ1BSzft8FQX3Y619oOocVyXp7roTum8UKBjdrj+WrwHvqhwxZAHUjl4kd0eWglNe+y7k94b4/9OELtj+AZgqo5eRsdzfgAAAABJRU5ErkJggg==" height="25px" width="25px"
                                             class="des-icon"/>
                                        <#if dipCodg??>
                                            <div class="sfq-header">
                                                <p style="width: 22%;">
                                                    预测分值: ${zfz?default(0)}
                                                </p>
                                                <p style="width: 26%;">
                                                    预测费用: ${ycfy?default(0)}
                                                </p>
                                                <p style="width: 25%;">
                                                    <#if fycy gt 0>
                                                        <span style="color: rgba(91,174,99,0.53);font-size: 12px;">预测金额差异: ${fycy?default(0)}</span>
                                                    <#else>
                                                        <span style="color: rgba(192,0,23,0.53);font-size: 12px;">预测金额差异: ${fycy?default(0)}</span>
                                                    </#if>
                                                </p>
                                                <p style="position: relative;width: 24%;text-align: right">
                                                    <#if caseTypeT == "1">
                                                        <span style="color: rgba(91,174,99,0.53);font-size: 12px">${balx}</span>
                                                        <img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAYAAABXAvmHAAAAAXNSR0IArs4c6QAAAnRJREFUaEPdmtFRwzAMhqPuQXmlLEE7AmUg2oEoI5AuQXml7FFz8sU5J7EdWZYdX/PSHiSOPumXLNuFRuh6+vvcrm63rQJ4sYbcdt9b/ASlzuZ/349vB4lXQ8ogaDQo9d40jTE0ajhQ6ogPpMCwAJ5/Pw4KAA0XuxCGAxIF0Hn8S8xqx0CxIGSAzfWEhrOkwgBuL+v9jvLcLEAJr/sMVQC7n4dXXQB8VxAgh9YpXrXvmZOUF6AG4w1ICMIJsKRsYuXkBNhcTyo21CXuv6z3E3snfyhcbWK5J9VpAFCT7qlSGgDUKp0xjC2lHqAi72PdD06YdlXqAWrwvpm4KLaYKGiACrzfKoCjmXUphcTALg4wnqQi5iBdkTQAJWSx9Y5yf4LxeniUESwln1TjEQBltAiAq8vkqACdUBzAYzx3rdECJeMpeqbc4+oqEyVcDiCD8egzDZC983QZH1Eug8FNAdCTD44OSnkX+r7FiJR0uTkwaWt9Brl6+ETd2xHh54DLsDGEq+IIGt/nALuEubY+DERAOmI5JzEPOPdv0MuuXTZh72PuHUGgGpA2oQTeM6lGGkComZuFkKo6NoVu5joAbh7044X2bqSl05VuvRmsAaTCWyJxx5td4kvKMUQO75u1gI6EIZJ8kYGQHNPWvnNRL5TMg5zoFh2iByG29wcRkMwFSmvNvWcs0fvaWjReKdFicyJA2tytVUq+05r7PODIUVo5krFnXN/z933IZ1PnaMYCUZltDvuWIia0uWZW2wbK0epgVo4ByJkbc8ep7BwIAWJEEtuFwbY6x5mzSUwd1MB0QOYnN4Of23RV5Xxbrdq5E3jqe/8BXXPh4nwHdywAAAAASUVORK5CYII=" height="15px"
                                                             width="15px"
                                                             style="position: relative;left: -2px;top: 3px"/>
                                                    <#else>
                                                        <span style="color: rgba(192,0,23,0.53);font-size: 12px">${balx}</span>
                                                        <img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAYAAABXAvmHAAAAAXNSR0IArs4c6QAAAidJREFUaEPtmV1uwjAMxx12DfYE9xhcZWjnAM4xsavQ3QOe4BojkyPKqtZOnNhZVQleQFua+uePv/PhYOIfN3H74QkwdgTNI3D5XK4eUDP4+32DBv/++nEK31YfEwA02r34LfiOwRELPbh9gNmcdloQFcDlsNw58FuNEQijASkCsDC8D10Kkg1w/VocpamSG5kSCDFAbp7nGv8Y76DxP24vLXYRQDB+5o/FRhU86G9uLYEQAdRMG5bNQTN/P69T7EmAGgWbMqqbTimIKMCoxt8pUoUdBbgeFl7srf7AezHCDFbaXjHfnFk72X9ovd9/qcYZsSiwANYv1AoBFwUSQOt9ymPqORlZnQwAMLJKAmjSB2u5RgRwXiqNBgAWXbcWANWdhwCVlsjaGgiRJepgWgDE3uEJQHZqQjFMUui/ItDPVwth4NStigq1UUE1cs6/We3gKHWrClC8EGQeFKkQPqttZNaGt/OJGlkAsNi4d/a2tQoYbaXXQgZ74MFyWukUbkldB4CQUW1Us5bTFmk0+oZGq93t+SfcoMk5N6UEoGhLiRNZFJ9WkVSbeotUUgEIzoaS50Jj9oVY6rSOEQFo66EkCqZHi6EeDHqDFERqPNvIYi/S6nkUQpDzw/MzqVs642qoU0ptODNFNcA9bAKSeR9gEoH+JAEkZ93fnpsa3FqqIkBFJlyzdq5XEcx7940dGcdLLi1ystocIOflFmOfABZe1Mwx+Qj8AgIQg0B0gRy1AAAAAElFTkSuQmCC" height="15px"
                                                             width="15px"
                                                             style="position: relative;left: -2px;top: 3px"/>
                                                    </#if>
                                                </p>
                                            </div>
                                            <div class="sfq-header" style="display: none;">
                                                <p style="width: 22%;">费用信息</p>
                                            </div>
                                            <i class="fa fa-chevron-down"></i>
                                        <#else >
                                            <div class="sfq-header" style="display: flex;">
                                                <p style="width: 22%;">暂无费用信息</p>
                                            </div>
                                        </#if>
                                    </div>
                                    <ul class="submenu">
                                        <#if dipCodg??>
                                            <li>
                                                <a href="#">
                                                    <div class="sfq-header">
                                                        <p style="width: 22%;">
                                                            基准分值: ${jzfz}
                                                        </p>
                                                        <p style="width: 26%;">
                                                            级别基本系数: ${jbjbxs}
                                                        </p>
                                                        <p style="width: 25%;">
                                                            计算分值: ${ycfzFirst}
                                                        </p>
                                                        <p style="position: relative;width: 24%;">
                                                            病例类型: ${balx}
                                                        </p>
                                                    </div>
                                                </a>
                                            </li>
                                            <li>
                                                <a href="#">
                                                    <div class="sfq-header">
                                                        <p style="width: 22%;">
                                                            中医优势病例系数: ${zyysjcxs}
                                                        </p>
                                                        <p style="width: 26%;">
                                                            基层病种系数:${jcbzjcxs}
                                                        </p>
                                                        <p style="width: 25%;">
                                                            <#if dlbzjcxs??>
                                                                年龄病种系数: ${dlbzjcxs}
                                                            </#if>
                                                        </p>
                                                        <p style="position: relative;width: 24%;">
                                                            重点学科系数: ${zdxkjcxs}
                                                        </p>
                                                    </div>
                                                </a>
                                            </li>
                                            <li>
                                                <a href="#">
                                                    <div class="sfq-header">
                                                        <p style="width: 22%;">
                                                            加成分值: ${jcfz}
                                                        </p>
                                                        <p style="width: 26%;">
                                                            总分值: ${zfz}
                                                        </p>
                                                        <p style="width: 25%;">
                                                            分值单价: ${fzdj}
                                                        </p>
                                                        <p style="position: relative;width: 24%;">
                                                            预测费用: ${ycfy}
                                                        </p>
                                                    </div>
                                                </a>
                                            </li>
                                            <li>
                                                <a href="#">
                                                    <div class="sfq-header">
                                                        <p style="width: 48%;">
                                                            <#if fycy gt 0>
                                                                <span style="color: rgb(91,174,99);font-size: 12px;">预测金额差异: ${fycy}</span>
                                                            <#else>
                                                                <span style="color: red;font-size: 12px;">预测金额差异: ${fycy}</span>
                                                            </#if>
                                                        </p>
                                                        <p style="position: relative;width: 26%;">
                                                            <#if caseTypeT == "1">
                                                                <span style="color: rgba(91,174,99,0.53);font-size: 12px">${balx}</span>
                                                                <img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAYAAABXAvmHAAAAAXNSR0IArs4c6QAAAnRJREFUaEPdmtFRwzAMhqPuQXmlLEE7AmUg2oEoI5AuQXml7FFz8sU5J7EdWZYdX/PSHiSOPumXLNuFRuh6+vvcrm63rQJ4sYbcdt9b/ASlzuZ/349vB4lXQ8ogaDQo9d40jTE0ajhQ6ogPpMCwAJ5/Pw4KAA0XuxCGAxIF0Hn8S8xqx0CxIGSAzfWEhrOkwgBuL+v9jvLcLEAJr/sMVQC7n4dXXQB8VxAgh9YpXrXvmZOUF6AG4w1ICMIJsKRsYuXkBNhcTyo21CXuv6z3E3snfyhcbWK5J9VpAFCT7qlSGgDUKp0xjC2lHqAi72PdD06YdlXqAWrwvpm4KLaYKGiACrzfKoCjmXUphcTALg4wnqQi5iBdkTQAJWSx9Y5yf4LxeniUESwln1TjEQBltAiAq8vkqACdUBzAYzx3rdECJeMpeqbc4+oqEyVcDiCD8egzDZC983QZH1Eug8FNAdCTD44OSnkX+r7FiJR0uTkwaWt9Brl6+ETd2xHh54DLsDGEq+IIGt/nALuEubY+DERAOmI5JzEPOPdv0MuuXTZh72PuHUGgGpA2oQTeM6lGGkComZuFkKo6NoVu5joAbh7044X2bqSl05VuvRmsAaTCWyJxx5td4kvKMUQO75u1gI6EIZJ8kYGQHNPWvnNRL5TMg5zoFh2iByG29wcRkMwFSmvNvWcs0fvaWjReKdFicyJA2tytVUq+05r7PODIUVo5krFnXN/z933IZ1PnaMYCUZltDvuWIia0uWZW2wbK0epgVo4ByJkbc8ep7BwIAWJEEtuFwbY6x5mzSUwd1MB0QOYnN4Of23RV5Xxbrdq5E3jqe/8BXXPh4nwHdywAAAAASUVORK5CYII="
                                                                     height="15px" width="15px"
                                                                     style="position: relative;left: -2px;top: 3px"/>
                                                            <#else>
                                                                <span style="color: rgba(192,0,23,0.53);font-size: 12px">${balx}</span>
                                                                <img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAYAAABXAvmHAAAAAXNSR0IArs4c6QAAAidJREFUaEPtmV1uwjAMxx12DfYE9xhcZWjnAM4xsavQ3QOe4BojkyPKqtZOnNhZVQleQFua+uePv/PhYOIfN3H74QkwdgTNI3D5XK4eUDP4+32DBv/++nEK31YfEwA02r34LfiOwRELPbh9gNmcdloQFcDlsNw58FuNEQijASkCsDC8D10Kkg1w/VocpamSG5kSCDFAbp7nGv8Y76DxP24vLXYRQDB+5o/FRhU86G9uLYEQAdRMG5bNQTN/P69T7EmAGgWbMqqbTimIKMCoxt8pUoUdBbgeFl7srf7AezHCDFbaXjHfnFk72X9ovd9/qcYZsSiwANYv1AoBFwUSQOt9ymPqORlZnQwAMLJKAmjSB2u5RgRwXiqNBgAWXbcWANWdhwCVlsjaGgiRJepgWgDE3uEJQHZqQjFMUui/ItDPVwth4NStigq1UUE1cs6/We3gKHWrClC8EGQeFKkQPqttZNaGt/OJGlkAsNi4d/a2tQoYbaXXQgZ74MFyWukUbkldB4CQUW1Us5bTFmk0+oZGq93t+SfcoMk5N6UEoGhLiRNZFJ9WkVSbeotUUgEIzoaS50Jj9oVY6rSOEQFo66EkCqZHi6EeDHqDFERqPNvIYi/S6nkUQpDzw/MzqVs642qoU0ptODNFNcA9bAKSeR9gEoH+JAEkZ93fnpsa3FqqIkBFJlyzdq5XEcx7940dGcdLLi1ystocIOflFmOfABZe1Mwx+Qj8AgIQg0B0gRy1AAAAAElFTkSuQmCC"
                                                                     height="15px" width="15px"
                                                                     style="position: relative;left: -2px;top: 3px"/>
                                                            </#if>
                                                        </p>
                                                    </div>
                                                </a>
                                            </li>
                                        </#if>
                                    </ul>
                                </li>
                                <li>
                                                                    <div class="link">
                                                                        <img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAYAAABXAvmHAAAAAXNSR0IArs4c6QAAAqZJREFUaEPtWlFOwzAMbbqDMZi4ABMHYByE7SCMA6BxATTWHWwNSlir1rUTN30ZQ2KfbeK+Zzt+cTJTBH5v759zO5u9+CHWzkNj4e+Mqay1R2f3eblYS/aN9GL78XW4OGgBjC2KjUSCJbDd7S3co1MNGlOtHu5uqZkBgdfdfm2K4idtml8nnFNxjJlPcXCRGBDoeV9gPQbE1LE0lU1d3z493letb7sfoN6ng6eCSZnvC0lZHlrAYwislgtxkaeASZ3TzQqaRj2AvQhcQfo0hLtpBCfgjXuZsMdQvU71vpuXjUAvPzNGLAuBVqUbhT6X2lgUeurOhIWLJJTAADgHQlBOVmMU86EEtCrNlWBaEqV1QRcqjIDWg1LNds+9DWNuRPBMMYAR4LzfeFragnD7l7EVCUKAC38o1IUxlTmdNl3ZHwscqgNs/jKl00WirOsKARxKwAuKsM0O7ddTvd6dB0khqogcsFxEYARSy2CXrLNRl2WwPaViCCPggGhJuEVMK5C2DFMNgRJovKkCQ0hoyWcTMi7vHRH3fNCGngfTniImZG4ajRw0As6L7iNcmeROMhBdHYTAAByT43+LgAs1aTk5nUC0pZAISAvQLTi/OWNO7lC6ACGgETK6yBH5D28ptf2A5P1fF7J2T09P79omQN6FqrTDlWRy9gNLIZoiY5v6qxOysQSi0fOKONyCXCQCqIrDqX02Aoi9vsbGPwGNl3KOgUXgItdOzGEAhIC2hkMiQSoRhIC2hiMIZGtomuYFAVKywR3JQCKQE3TMdhoBZr8f+1Cu9+orptiFWi6AIbsUU/CKabBQM966aJ1BSzft8FQX3Y619oOocVyXp7roTum8UKBjdrj+WrwHvqhwxZAHUjl4kd0eWglNe+y7k94b4/9OELtj+AZgqo5eRsdzfgAAAABJRU5ErkJggg==" height="25px" width="25px"
                                                                             class="des-icon"/>
                                                                        <#if fundSourceType??>
                                                                            <div class="sfq-header">
                                                                                <p style="width: 22%;">
                                                                                    基金支付总额: ${fundAmtSum?default(0)}
                                                                                </p>
                                                                                <p style="width: 26%;">
                                                                                    个人负担: ${presonBearCost?default(0)}
                                                                                </p>
                                                                                <p style="width: 25%;">
                                                                                    <span>预测报销：${preFundFee?default(0)}</span>
                                                                                </p>
                                                                                <p style="width: 20%;">
                                                                                    <span>补偿比：${fundRatio?default(0)}</span>
                                                                                </p>
                                                                            </div>
                                                                            <div class="sfq-header" style="display: none;">
                                                                                <p style="width: 22%;">支付信息</p>
                                                                            </div>
                                                                            <i class="fa fa-chevron-down"></i>
                                                                        <#else >
                                                                            <div class="sfq-header" style="display: flex;">
                                                                                <p style="width: 22%;">暂无支付信息</p>
                                                                            </div>
                                                                        </#if>
                                                                    </div>
                                                                    <ul class="submenu">
                                                                        <#if fundSourceType??>
                                                                            <li>
                                                                                <a href="#">
                                                                                    <div class="sfq-header">

                                                                                        <p style="width: 32%;">
                                                                                            基金支付总额: ${fundAmtSum?default(0)}
                                                                                        </p>
                                                                                        <p style="width: 32%;">
                                                                                           个人负担：${presonBearCost?default(0)}
                                                                                        </p>
                                                                                        <p style="width: 32%;">
                                                                                           预测报销：${preFundFee?default(0)}
                                                                                        </p>

                                                                                    </div>
                                                                                </a>
                                                                            </li>
                                                                            <li>
                                                                                <a href="#">
                                                                                    <div class="sfq-header">
                                                                                                <p style="color: red; width: 32%;">
                                                                                               基金差异：${FundFeeDiff?default(0)}
                                                                                          </p>
                                                                                        <p style="width: 32%;">
                                                                                          <span>补偿比(85-100):${fundRatio?default(0)}</span>
                                                                                        </p>
                                                                                        <p style="width: 32%;">
                                                                                            基金来源:${fundSourceType?default(0)}
                                                                                        </p>
                                                                                    </div>
                                                                                </a>
                                                                            </li>
                                                                        </#if>
                                                                    </ul>
                                                                </li>
                                <li>
                                    <div class="link">
                                        <img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAYAAABXAvmHAAAAAXNSR0IArs4c6QAAAXZJREFUaEPtWe1twjAQvQuDEVp1gSIGAAaBDlI6QAULICCLEVdnkihy0zS+c0BIz3+QLM55X3aSC9OTD35y/AQCj3YQDnx9H3M3mWy8E87loSOO6EPm1vPX7RhuqR1ogHeA7gIqRMYg0Uvgc3/aMtGGmIvV+8tMgDVzSjmHEBFxyizLmXkqrvbV9BLY7U+uxsllOVsu3ord4XzpikosnzBaAtqnUOIYutoSMLzOYAK1CqkIRBEGAbG22oRwICo71Z8RIURIk5tWDSKECCFCzbMYHiVwJ9ZsBxyjOEY1ucGdmKj9Son3AUuK7nIKMRd8vfoekAzpKsiv72pYR0oCvh+UZRePqQIt3Yo+jGO2YqK7EgK0boH8BzwkFU1kgEAqApZECImbebem1a+1BoBu19ydQAi4dlPmYx2t9tjfenYdoxb1x6h9uANWUiBgVdBaDwesClrr4YBVQWt99Ccm6wVT16s/8qUGol0PBLTKpaqDA6mU1K7zA0WKyUA/Amp/AAAAAElFTkSuQmCC" height="25px" width="25px"
                                             class="des-icon"/>
                                        <#if dipCodg??>
                                            <div class="sfq-header">
                                                <p style="width: 22%;">
                                                    标杆费用: ${refeStandfee}
                                                </p>
                                                <p style="width: 30%;">
                                                    与标杆费用差异: ${bgfycy}
                                                </p>
                                                <p style="width: 22%;">
                                                    标杆住院天数: ${inHosAvgDays}
                                                </p>
                                                <p style="width: 22%;">
                                                    住院天数差异：${zytscy}
                                                </p>
                                            </div>
                                            <div class="sfq-header" style="display: none;">
                                                <p style="width: 22%;">标杆信息</p>
                                            </div>
                                            <i class="fa fa-chevron-down"></i>
                                        <#else >
                                            <div class="sfq-header" style="display: flex;">
                                                <p style="width: 22%;">暂无标杆信息</p>
                                            </div>
                                        </#if>
                                    </div>
                                    <ul class="submenu">
                                        <#if dipCodg??>
                                            <li>
                                                <a href="#">
                                                    <div class="sfq-header">
                                                        <p style="width: 22%;">
                                                            总费用: ${sumfee}
                                                        </p>
                                                        <p style="width: 26%;">
                                                            全市均费: ${refeStandfee}
                                                        </p>
                                                        <p style="width: 25%;">
                                                            标杆费用差异: ${bgfycy}
                                                        </p>
                                                        <p style="width: 24%;height: 22px;position: relative">
                                                            <#if bgfycy gt 0>
                                                                <img src="data:image/png;base64,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" height="20px"
                                                                     width="20px"
                                                                     style="position: absolute;left: 38%;top: 2px"/>
                                                            <#elseif sumfee lt lowLmtfee || bgfycy lt 0>
                                                                <img src="data:image/png;base64,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" height="18px"
                                                                     width="18px"
                                                                     style="position: absolute;left: 38%;top: 2px"/>
                                                            </#if>
                                                        </p>
                                                    </div>
                                                </a>
                                            </li>
                                            <li>
                                                <a href="#">
                                                    <div class="sfq-header">
                                                        <p style="width: 22%;">
                                                            住院天数: ${inHosDays}
                                                        </p>
                                                        <p style="width: 26%;">
                                                            全市平均天数: ${inHosAvgDays}
                                                        </p>
                                                        <p style="width: 25%;">
                                                            差距: ${zytscy}
                                                        </p>
                                                        <p style="width: 24%;height: 22px;position: relative">
                                                            <#if inHosAvgDays??>
                                                                <#if zytscy gt 0>
                                                                    <img src="data:image/png;base64,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"
                                                                         height="20px" width="20px"
                                                                         style="position: absolute;left: 38%;top: 2px"/>
                                                                <#else>
                                                                    <img src="data:image/png;base64,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"
                                                                         height="20px" width="20px"
                                                                         style="position: absolute;left: 38%;top: 2px"/>
                                                                </#if>
                                                            </#if>
                                                        </p>
                                                    </div>
                                                </a>
                                            </li>
                                            <li>
                                                <a href="#">
                                                    <div class="sfq-header">
                                                        <p style="width: 22%;">
                                                            药品费: ${ypf}
                                                        </p>
                                                        <p style="width: 26%;">
                                                            全市平均药品费: ${bgypf}
                                                        </p>
                                                        <p style="width: 25%;">
                                                            差距: ${bgypfcy}
                                                        </p>
                                                        <p style="width: 24%;height: 22px;position: relative">
                                                            <#if bgypf??>
                                                                <#if bgypfcy gt 0>
                                                                    <img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAIAAAACACAYAAADDPmHLAAAAAXNSR0IArs4c6QAADEdJREFUeF7tnQesdUURx/8kBkURrIAKFkRFRMUC2AuIQuwg9t4wKhZs2LsiVhR7x45YUKMoomJv2AsWLBEU7FETlRg1v++d+33n7Tfn3N09e8q7Zya5uS/37c7uzvzPnt3Z2Zlt5DRrCWwz69H74OUAmDkIHAAOgJlLYObD9xnAATBzCcx8+D4DOABmLoGZD99nAAfAzCUw8+H7DBAHgJ0l7Sfp5pKuIelXkr4r6WOSzo5jMc1SDoB2vewl6U2SbthS7EOSniDprGmquL1XDoBm+aD8E6snfpluUT4gAAwbihwAtrpSlF/ncFdJ799ICHAA2NriSb5TpiI3FAgcAFtr+aaSPm8o/8OSPirpF5IOrz6XNsr9RtIBkn6eCaBBqzkAthY3i74HBT9/T9KtJZ1X+31PSW9pWCC+T9LdB9VkZmMOgPWCu0j1hO8UyPMISW8wZHxxSe+QdFvjf4+S9KpMvQxWzQGwXtR3kHRyIP0zJF2/RSM7SDrNKMOrgsXkvwfTZkZDDoD1Qnu9pIcGcjxG0pOXyPY2kk4xyvAa4HUwWXIAbFHNZSSdKYknuk6HNCg3VOozJT0r+HHyawEHwBaNPUzSawMFMo1fOfLxvZGkLwVlmf6vLumXkTwGL+YA2CLyT0g6ONAArwSAEUs/MCyH960WirE8Bi3nAFgT97UlfceQ/F0kfSBBI9Zr4CWVmTiBzXBFHQBrsn6GpGcHYv+DpD0k/S1BHXeW9MGg/KcksUicJDkA1tTCVu+6gYbeJukBiVpjvRBaADEe7ZLIZ7DiDgDpVpJONSTOeuCTiZrY1tj3/0vSdol8BivuAFgz54ZPOqv5m2Ro4YrGiv9cSWwxJ0lzBwCGmvcYmsk14+I48uWAH7YFtoKTpDkD4BKSTpe0d6CZX1fuX7/P0NiTJGE5rNNXl3gUZTRTrsqcAfBSSUcZonywpDdnihhA3Syoe1J1dJzJst9qcwUAR7vWAo/XwT0zRb69pL8bde8l6d2ZPHuvNlcAWE8qU/4tJP04U+p4AlkHPzsm2hIym8+rNkcAvFzSYwxxHSnp+DwxbqqF3eB+QX1mmdC83KGJ8lXnBgDrwAep4gN4aEfxMoOELmK0x3nCZGlOAGB658DnQoE2ztGaMYjtWi7tK+nrRuXdp3wSSH/nAgBMsSh/H0NJTNsn5Gq+qmcdAmEPuHFHvr1XnwsAWN1bTpqvlvTIAlLm6WcWqNMTJb24AO9eWcwBANbTiVAx0OC+/c+OEua9bxmNAMQ3O/LuvfqqA6BJ+edXTyzu3l2JVwg7gDrBFx+DydMqA6BJ+SjlMOPcPldZ7P2xAdTpRZKOzmU4ZL1VBAC++tjjQ+/ehVyx1x9bUMhY/7AC1innKLlgl+JZrRoA2M6h/Os1iOAh1XXveAm1l8Tuj1WxTjiAcvo36fsAiw6vCgBYzOF8GVri6orp49ImYGNGqRNXywDahqA+AIBTBJ++6aqSrinpapIOWtJYH8qnya9IukHQNoc/v+1h8EQk+UtpviUAgC8dPvFMu1jbhlB+rBzw9MXZk5u9fdDvBvb3+2nlvfyj6q4i7XeirgB4oaTHSrpgp170U/kVlfL/2g/7TVytewA9NreONbMMF1b5ZAMhFwA85Z9OuDUzlFBo5xuSXtDjU18fy+2qmAFDji9sCyBw6ETsgmTKAcDF+ngXJfd86wpc4MDxIvTLL8C6lQWm5LGvgXN34fYNgS1aO58DAKJnEEWjiXhPtS2CWCfU6XOZGmLa4+4efvhMxWOaXdn3E0IuXBBmDs2sxhrroi0MCV1HWBsWi9GUCgCuSTO9WkSUrPcuiZtnWeduKSkXBNEDXZGCeB3fv8XIxWuAGAfRlAIAtlpccwrpawnIdwBEq6a1ILMBJ5n7G6V4FRDAMopSAMBq/2UG18smrEIdAFFqiSrEWuytRjSzpFkgBQC4Sj8w6BoBEcJLlW29dwBE6Ta6EOupzxqlD5T0mRguKQDg/DyccrgNm2JkcQDEaCWtDAAIF9Z3q6KcLuWUAoB/SCKKVp04eUsxtDgAlqokuYAl04cb0U5MxikA+J/BIaU+1R0AyfpdWsGS6dMkPX9pzUSnUAdAjESHL0NcozCMDYEuufW8lFKeYAfAUnGOUsCKbcTV9jBglb8CRlFP/43i1BredcBR9Y8xTfsMECOl6ZZhu8ehXJ0wxeMjEUUOgCgxTbYQhjkMdHXi7qN17d1fAZNVY37HuM4WPu3RRiCa9RkgX/hj18Ql7idBJ1gPXDilYw6AFGlNq+yjJeH1VCfS1YR3FFp77QCYllJTekPsASKd1Imj4renMHEApEhrOmWJOmKZ4C+X6pHsAJiOUlN6YoWkzYpG5gBIEft0yr5REtHM6vR0Sc9L7aIDIFVi0yhPVBMcceqEPyLeWUnkAEgS1yQKW4kpAMSuOb1zAORIbdw6TPXPCbqQmthic3UHwLjKzGmd2ENhMuvUxBYOgBzJT6COlY+Abl1K0p9y+uczQI7UxqtD0Isw7uDHGxJXRvXSARAlpskU4uINDp914jQwNAlHd9gBEC2q0QtyLYzVfng97DoNCa+iOuwAiBLTJArd0XDB7xyNzAEwCd1GdeI4SWQyqRMOIY+Lqt1QyAHQRXrD1rWCUXARNCsuwKLrDoBhlZjbGmZe4hHViXhBl5fEhZ1scgBki27Qik8xLnokO39YPXYADKrH7MZOq+Ia1xk8QtJrsjlWFR0AXSXYf/0rVZFQwpb26pDeZjMvB0D/CuzaAuf+nP/XKcv5w18BXVUxTn0CX90jaJowPU8t0R2fAUpIsT8eJLf8mSS+65Tk+9/WPQdAf8orwZmws+8MGJ1VpbUvwd8vhhSRYn9MUD4gqBMrf3YARchngCJi7IVJ0/RPLMCTS7XoACglyfJ8rOmfAJx7FMhztLm3DoDyiivF0Zr+CQsXRmrr1J4DoJP4equMjZ+Q8GFQLraDOIUUIwdAMVEWZcT9ftLb14nUdKSi+XPJlhwAJaVZjpfl+Ys/gJX0ulOrDoBO4uulMomvTjU44wqOCbgoOQCKirMIs9dJOiLglBT/N6UXKQD4tpF8mWtKoaNCW/seKLJdOztL+qGkSwbF2BJyJlCcUgDAFoQABHVisUJQolhyALRLCgvf8UERAHEtSf+NFXJKuRQAkA2blKh1Oi8xa5YDoFk7WP4I7rhnUCTr2ncsCFIAgAOiZYJMcU1yADRrhiNeMrLUiaBPPP2kxemFUgBAB1iMkCkrJMKVgtQzlvTSAWALaL/q6b9A8G+ygpCUqjdKBQCXEM9uyRPIRYVvSSKBURORZKJOnjNIOlHS4YFceL2yyCYxVm+UCgA6cqgkUrSVIpIt4/POYodvPkmZr0p1ZCQ+mHetFX7pLOfm8HIAAKP7SDqhR4ERAXMBBr6ZWfCMWTXavVpX7R0MjOSXPP3/6XvAuQCgX1xSJHVsMeeElsEiiAUgmCkABHaJ7JSpfQs2gv/21ZoqTPdC1eKHPk396QKABU/StrMwxFSZFacmQlhWEW7ELF4b368AQbJosmhuBDpJ0mFGR/k9XA/0Np4SAKh3Dl/1GCDsJImyrH6TYtsukQRRMhYzBTMEgOC7FyNKB61YWT4W7FKtqx26kRYsulNDLZV5/wGExYf77iWJO/WAYgEGvsMgyyXbW8bL2gov6txb0ruWMSj5/9IzQIm+sReuA4K/iY1Tktha1QHBLAFQ+iQueD5XEqd9FhH+JbwA0md/NvGeIgCsQXM4sgDFvtXfpEUpSSwsT5eEUYu7eOcXZP74KrTbdg08ifz9yoLtRbPaKACwBsSduXCm2DZ65O0F8b4BDCRlxPqJ8SuVdqkWc4dI4tNERxtnLKltZZffyACwBr2PpMUMATiwo5cgrJsfqbadeOay/Vx8sx3eodoW883enp1Rm9LpEwtTAj6mpN4tMZZ1PFYNAKGAyKYVzhJXKC7FdIZkYWc98MX0qmVrrDoALGmxBQ1BQQrcIQjbBYo/dojGYtqYIwAsuVylAsVBVSCG3WKEl1iGo3SmfF4nkyEHgK2KA6p0LHyzpsgl7A0cnOEzwbZzcuQAWK4SXhnk5mFByXE4H7agi2+2dhzdLj7nVn+fIukLy9mPW8IBMK78R2/dATC6CsbtgANgXPmP3roDYHQVjNsBB8C48h+9dQfA6CoYtwMOgHHlP3rrDoDRVTBuBxwA48p/9Nb/D8oIQJ+PF6HDAAAAAElFTkSuQmCC"
                                                                         height="20px" width="20px"
                                                                         style="position: absolute;left: 38%;top: 2px"/>
                                                                <#else>
                                                                    <img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAIAAAACACAYAAADDPmHLAAAAAXNSR0IArs4c6QAAC8lJREFUeF7tnQfMbUURx3+YIBqwRbBhwxCwS7GjYEFEVBSJJYqNBAsqaMRuEHusYAMLghiIndgLETVGrIgdiBoVExsaKyqWRPOHc987b7859+yes3vP3u/MJDfffe/uzs6Z+Z8ts7Oz2+E0aw1sN+un94fHATBzEDgAHAAz18DMH997AAfAzDUw88f3HsABMHMNzPzxvQdwAMxcAzN/fO8B4gFwL2BnYJfmb/v7ZcDvgN82f/X9IuDiePbTlHQAdOt9N2B/4AHAA4GdBpjom8CZwGeAnw6oX7yKA2BbFd+/MfoBwH6ZtS8QfAA4IzPfUezmDIA9gL2BvVqfG4zSZlzlU4Hjgd/EFS9bai4AuCFwx8DYtyir2qXcNTcQCD40oQxXNL0ZAbAjcGdgn9YbfpsMiv4V8J1moqe3VxM+ff4CXBO4VvNX3+8LaNLYR28Gju0rVPL3zQAAGXph8EV3vn0mpX0V+BTwWeCCRJ6aRB7WfO6xpO5ZwBGJvLMVXzcA7N4YWwaXsTWG643LSZqsyeDnZFzGHQm8DNi1Q9B3AU/K+RCxvGoGwPWAOwUGzz1J01j8XeB7zV99V7degvZsQPCIDuaTDAe1AGCHlqH1dmvClnuSdknLyAuj/7yEpXt4HgO8qaPMa4Dnr1KmEgC4KnD3iIe4ddONy+B3iCifUkSeOBl58fkhoE8t9ETgtA5hTgBeuipBcwHgcGBf4G6Rs9+cz6dZ+MLQ328MLWP/I2cjBXg9B3itwfdvzQu0EsCOBYDGaD1EzJInhw7/1RqvF2+1/v4+B/MJeLwOOM5o93RAE8fiNAYAMrxQXJL0Rv+g9Vbru8byzURyDT/OeCDtP3y69IMOBUAXcsfIq82SxVu9MPqFYxiuSV3tKp4HyDXdpnOBA0s/wxAAaMKmMbeLNIZ9e8nv1nChiaCMfnnpB66U/1HAOw3ZjgZOKSnzEAB8DjjIEEqOE81gv9Yj8P+M34fIUVIvU/CWx/GQoGH1ilpRFZvjpCr+JY2RQwXJuRG7seEAsOF1b+ALxk/yC8g/UIRSASAXqfbM25S6bnUAdJtSDiI5itqk4VYu7yKUCoDFztdCGIVCafKSsrftAOg25U2a+ZPCztqU0sMmASUFAHLyaHesTfp3auSMA2C5iU4GnhoUORuQsy07pQDgWcAbAwlOAvT/KeQAWK4txRJ83iii/ZFlq6sUG2wpmwIAawKYOv6rYQdAv6m+YvSsry/heHMA9BtjihLPBE4MGlZE0u2AP+UUyAGQU5v5eGky+CPgGgHL7I4hB0A+o+XmpO1ibRu3Sc6iB+VsyAGQU5t5eckrKIO36b+AwuKybYg5APIaLTe3nzQGb/NV7KBiCLOQAyCLGosxUZzgMwLuHwYenqtFB0AuTZbho3OJYUyAdls1DFyao0kHQA4tluVhDQOPbQ6djm7ZATBahcUZWMOAdl67wsuTBHIAJKlrksLWMCBBdNxtdMSUA2ASmyY3qjwDCsBt0xA3/IaGHQDJtpikghVC/mVAeQxGkQNglPpWVvmWTcqZsEHFYmiSOJgcAINVt/KKirW8a9DqU4B3jJHEATBGe6ut+2rj3KBSzjxqjBgOgDHaW23dg5tkU+1WFS18Y+DfQ0VxAAzV3OrrXQ3QaebwiPxDgI8PFccBMFRz09R7n9HlDwnL2yK9A2AaQw5tVZO+8KSQzk8OPl7vABhqimnqdS0HlS5HWU6SyQGQrLLJK1jLwed15BroFdYB0Kui6gpYy0Gd19QqIZkcAMkqm7yCtRz8D3BdQLECSeQASFJXFYW7loNyCMkxlEQOgCR1VVPYWg6+x4gi7hXYAdCroioLWMvBXwI3S5XWAZCqsTrKdy0H7wIodiCaHADRqqquoLUcVH5BBYpEkwMgWlXVFbSWg98wtoyXCu4AqM6u0QJZy0FVVo7l6JxCDoBofVdXsGs5+ISUa2kcANXZNUkgazn4QeCRsVwcALGaqrOctRz8a3N7SZTEDoAoNVVbqGs5qGhhRQ33kgOgV0XVF7CWg9H3DjgAqrdvr4DWclDOIDmFeskB0Kui6gtYy0Hlc7x2jOQOgBgt1V3m6sCfAd3U0ibdldh7/5EDoG7jxkr3RePSDuUe/lIfAwdAn4bW43eFi988EPVGMSl8UwCgPP9CWps+ARyaqCNPFJmosJ7itzcCQuUKlku4l1IAoJx1cjK06ddLLkPsatwB0GuWpALW7S3KNHrPGC4pABA/a6xJTWHqAIixTFwZXb2nOwauHxR/cscNJBu4pgJAxn62IVsKHwdAnHH7Ssn48vuHF2MrobQSS0dRiuHEUI3K82Td1xuLOgdAlGk6C2l5p1yB+miiF1KsHa6olwoA1em6Nka/6cZOnVAJ7xVoC2lFrCRFsTTM/gn8GPhF89FaeEpSsgalcVEKtxIkx44u19Kkb6eOBj4GPDSl8SEAEH9d7xZ2PSntliirzFmLTwn+y3hambxWLYM2f7QikxcwmoYCQA103R4W3XihgsqyLSCs6v5dZeiY5Or3lv505Z6SSCsyOInGAEANqbt5uzELTRKiUGFl2Hzh0EOTkTKpW86avz+y3UUxLcN136A+Kfc2bWlmLADEaNdmZaD8NbpXqCb6QwOCbMmVg4eznGOreH698R8ZY/iFkDkA0H5grQ4EgjCZUQml7NjMQ7QyCd2gYXupvopYeS0AqOdJis2PbOzvrVvSBewslBsAWYQawER3GeoC5kcvqXsmoBy7OUlLMnXDbdJBTUXq/CxnQ6V4bRYALPQjB8jLlxyV1m1c98usTCuLZ9JaPLM8Sew2GwAWD/8K4EUdmtAWqbZKc5HaUXttyprTP5egFp/NCgA9q7Jpdx2XfjHwykyK1U1eytPTJjmldmsCNTI1U4bNZgaANNZ1Lbt+Owz4aCa1aryXwduULad/JhlNNpsdAHpo6/KlhTK0Yvl6BgUrVduxAZ8zAJ3SqZrmAIBlw4H2LQ4Exi6r7gOcG1haFz0qi2fVNBcAyAjyClrjvpxEOVy5ys8TbtIoYlcu82ppTgCQEd4NHGlYI8dVbPIzPCbg/Tbg6dVaf+B2cM3P0yfbVZqopv2DghoCNBQMSrbY8LJWHdqu3rNPqCl/n1sPIF3LdXyOEct4NnD4CGPouLZiFELau3HhjmBdruocASBtPqzZTAk1+zTg5BHqtrbIj2+8kyPYlqs6VwBIo6cbyzSdpNHJWnXdQ0gAemtQ8XzjwqchvIvUmTMANBScZ5yhOws4YqC2de27FZQRdUhjYJujqs0ZAFKc5cfX/+vadiVeHEIXABr326QNKG1EVUdzB8AOTS+wb2CZMVeyWUGzuuxZG0TV0dwBIIMon877DcsMfWstAGhP4tTqrD9DP0CXDXSUar/gRxlMhkslCwC6+FFRSdWR9wBXmsS6mfMy4LbAJYlWk/fv6KDOccAbEvmspLgD4Eo161DHRYA8hW16LqDDlylknZ/UZlF4sjqFZ7GyDoCtqpUnUDECbfpWcxonxQCXArsEFaKydaQ0kqusA2CrJh/fsfRLiRnQyRwdz2pT1fsBDoCtptIhj4uNQy5vAY6JfOOs3cb3AgJXleQA2NYs1jGv2CBSXdYgt+/OgaVzhp5lB5EDYFuVWuFjCvC8ToTmdTwrXDaqR7lVRN3JijgANqpeCRb2Cf77wcAnl1hJ28iWp09JHBWJVC05ADaaxsq5sywZlsCiaKDwTf8joOXfmCCT4sBxAGxUcddFDIr40bHzNqmsNo3CHD0qMza2oLjx1YADwFazInz19oYkEMhtfNMm/k/p2rc3yo3ZUl6J4ReNOABsdeugqeL6h5ASVGjOoOSN1ZMDoNtEVpRvn0FlfPUSF/YVrOV3B8BySyi8S2N5DCkxlpaBa2N8nwPEmBVeBbygp6jS5GhHUTuIa0XeA8SZa68mG7cCRpUV5PImNZ12+LREVO7EtSQHwFqaLZ/QDoB8ulxLTg6AtTRbPqEdAPl0uZacHABrabZ8QjsA8ulyLTk5ANbSbPmEdgDk0+VacnIArKXZ8gn9f0yRL5+XHYGuAAAAAElFTkSuQmCC"
                                                                         height="20px" width="20px"
                                                                         style="position: absolute;left: 38%;top: 2px"/>
                                                                </#if>
                                                            </#if>
                                                        </p>
                                                    </div>
                                                </a>
                                            </li>
                                            <li>
                                                <a href="#">
                                                    <div class="sfq-header">
                                                        <p style="width: 22%;">
                                                            耗材费: ${hcfycy}
                                                        </p>
                                                        <p style="width: 26%;">
                                                            全市平均耗材费: ${bghcf}
                                                        </p>
                                                        <p style="width: 25%;">
                                                            差距: ${bghcfcy}
                                                        </p>
                                                        <p style="width: 24%;height: 22px;position: relative">
                                                            <#if bghcf??>
                                                                <#if bghcfcy gt 0>
                                                                    <img src="data:image/png;base64,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"
                                                                         height="20px" width="20px"
                                                                         style="position: absolute;left: 38%;top: 2px"/>
                                                                <#else>
                                                                    <img src="data:image/png;base64,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"
                                                                         height="20px" width="20px"
                                                                         style="position: absolute;left: 38%;top: 2px"/>
                                                                </#if>
                                                            </#if>
                                                        </p>
                                                    </div>
                                                </a>
                                            </li>
                                            <li>
                                                <a href="#">
                                                    <div class="sfq-header">
                                                        <p style="width: 22%;">
                                                            药占比: ${ybfyzb}%
                                                        </p>
                                                        <p style="width: 26%;">
                                                            全市平均药占比: ${ypfbgzb}
                                                        </p>
                                                        <p style="width: 25%;">
                                                            差距: ${ypfycy}%
                                                        </p>
                                                        <p style="width: 24%;height: 22px;position: relative">
                                                            <#if ypfbgzb??>
                                                                <#if ypfycy gt 0>
                                                                    <img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAIAAAACACAYAAADDPmHLAAAAAXNSR0IArs4c6QAADEdJREFUeF7tnQesdUURx/8kBkURrIAKFkRFRMUC2AuIQuwg9t4wKhZs2LsiVhR7x45YUKMoomJv2AsWLBEU7FETlRg1v++d+33n7Tfn3N09e8q7Zya5uS/37c7uzvzPnt3Z2Zlt5DRrCWwz69H74OUAmDkIHAAOgJlLYObD9xnAATBzCcx8+D4DOABmLoGZD99nAAfAzCUw8+H7DBAHgJ0l7Sfp5pKuIelXkr4r6WOSzo5jMc1SDoB2vewl6U2SbthS7EOSniDprGmquL1XDoBm+aD8E6snfpluUT4gAAwbihwAtrpSlF/ncFdJ799ICHAA2NriSb5TpiI3FAgcAFtr+aaSPm8o/8OSPirpF5IOrz6XNsr9RtIBkn6eCaBBqzkAthY3i74HBT9/T9KtJZ1X+31PSW9pWCC+T9LdB9VkZmMOgPWCu0j1hO8UyPMISW8wZHxxSe+QdFvjf4+S9KpMvQxWzQGwXtR3kHRyIP0zJF2/RSM7SDrNKMOrgsXkvwfTZkZDDoD1Qnu9pIcGcjxG0pOXyPY2kk4xyvAa4HUwWXIAbFHNZSSdKYknuk6HNCg3VOozJT0r+HHyawEHwBaNPUzSawMFMo1fOfLxvZGkLwVlmf6vLumXkTwGL+YA2CLyT0g6ONAArwSAEUs/MCyH960WirE8Bi3nAFgT97UlfceQ/F0kfSBBI9Zr4CWVmTiBzXBFHQBrsn6GpGcHYv+DpD0k/S1BHXeW9MGg/KcksUicJDkA1tTCVu+6gYbeJukBiVpjvRBaADEe7ZLIZ7DiDgDpVpJONSTOeuCTiZrY1tj3/0vSdol8BivuAFgz54ZPOqv5m2Ro4YrGiv9cSWwxJ0lzBwCGmvcYmsk14+I48uWAH7YFtoKTpDkD4BKSTpe0d6CZX1fuX7/P0NiTJGE5rNNXl3gUZTRTrsqcAfBSSUcZonywpDdnihhA3Syoe1J1dJzJst9qcwUAR7vWAo/XwT0zRb69pL8bde8l6d2ZPHuvNlcAWE8qU/4tJP04U+p4AlkHPzsm2hIym8+rNkcAvFzSYwxxHSnp+DwxbqqF3eB+QX1mmdC83KGJ8lXnBgDrwAep4gN4aEfxMoOELmK0x3nCZGlOAGB658DnQoE2ztGaMYjtWi7tK+nrRuXdp3wSSH/nAgBMsSh/H0NJTNsn5Gq+qmcdAmEPuHFHvr1XnwsAWN1bTpqvlvTIAlLm6WcWqNMTJb24AO9eWcwBANbTiVAx0OC+/c+OEua9bxmNAMQ3O/LuvfqqA6BJ+edXTyzu3l2JVwg7gDrBFx+DydMqA6BJ+SjlMOPcPldZ7P2xAdTpRZKOzmU4ZL1VBAC++tjjQ+/ehVyx1x9bUMhY/7AC1innKLlgl+JZrRoA2M6h/Os1iOAh1XXveAm1l8Tuj1WxTjiAcvo36fsAiw6vCgBYzOF8GVri6orp49ImYGNGqRNXywDahqA+AIBTBJ++6aqSrinpapIOWtJYH8qnya9IukHQNoc/v+1h8EQk+UtpviUAgC8dPvFMu1jbhlB+rBzw9MXZk5u9fdDvBvb3+2nlvfyj6q4i7XeirgB4oaTHSrpgp170U/kVlfL/2g/7TVytewA9NreONbMMF1b5ZAMhFwA85Z9OuDUzlFBo5xuSXtDjU18fy+2qmAFDji9sCyBw6ETsgmTKAcDF+ngXJfd86wpc4MDxIvTLL8C6lQWm5LGvgXN34fYNgS1aO58DAKJnEEWjiXhPtS2CWCfU6XOZGmLa4+4efvhMxWOaXdn3E0IuXBBmDs2sxhrroi0MCV1HWBsWi9GUCgCuSTO9WkSUrPcuiZtnWeduKSkXBNEDXZGCeB3fv8XIxWuAGAfRlAIAtlpccwrpawnIdwBEq6a1ILMBJ5n7G6V4FRDAMopSAMBq/2UG18smrEIdAFFqiSrEWuytRjSzpFkgBQC4Sj8w6BoBEcJLlW29dwBE6Ta6EOupzxqlD5T0mRguKQDg/DyccrgNm2JkcQDEaCWtDAAIF9Z3q6KcLuWUAoB/SCKKVp04eUsxtDgAlqokuYAl04cb0U5MxikA+J/BIaU+1R0AyfpdWsGS6dMkPX9pzUSnUAdAjESHL0NcozCMDYEuufW8lFKeYAfAUnGOUsCKbcTV9jBglb8CRlFP/43i1BredcBR9Y8xTfsMECOl6ZZhu8ehXJ0wxeMjEUUOgCgxTbYQhjkMdHXi7qN17d1fAZNVY37HuM4WPu3RRiCa9RkgX/hj18Ql7idBJ1gPXDilYw6AFGlNq+yjJeH1VCfS1YR3FFp77QCYllJTekPsASKd1Imj4renMHEApEhrOmWJOmKZ4C+X6pHsAJiOUlN6YoWkzYpG5gBIEft0yr5REtHM6vR0Sc9L7aIDIFVi0yhPVBMcceqEPyLeWUnkAEgS1yQKW4kpAMSuOb1zAORIbdw6TPXPCbqQmthic3UHwLjKzGmd2ENhMuvUxBYOgBzJT6COlY+Abl1K0p9y+uczQI7UxqtD0Isw7uDHGxJXRvXSARAlpskU4uINDp914jQwNAlHd9gBEC2q0QtyLYzVfng97DoNCa+iOuwAiBLTJArd0XDB7xyNzAEwCd1GdeI4SWQyqRMOIY+Lqt1QyAHQRXrD1rWCUXARNCsuwKLrDoBhlZjbGmZe4hHViXhBl5fEhZ1scgBki27Qik8xLnokO39YPXYADKrH7MZOq+Ia1xk8QtJrsjlWFR0AXSXYf/0rVZFQwpb26pDeZjMvB0D/CuzaAuf+nP/XKcv5w18BXVUxTn0CX90jaJowPU8t0R2fAUpIsT8eJLf8mSS+65Tk+9/WPQdAf8orwZmws+8MGJ1VpbUvwd8vhhSRYn9MUD4gqBMrf3YARchngCJi7IVJ0/RPLMCTS7XoACglyfJ8rOmfAJx7FMhztLm3DoDyiivF0Zr+CQsXRmrr1J4DoJP4equMjZ+Q8GFQLraDOIUUIwdAMVEWZcT9ftLb14nUdKSi+XPJlhwAJaVZjpfl+Ys/gJX0ulOrDoBO4uulMomvTjU44wqOCbgoOQCKirMIs9dJOiLglBT/N6UXKQD4tpF8mWtKoaNCW/seKLJdOztL+qGkSwbF2BJyJlCcUgDAFoQABHVisUJQolhyALRLCgvf8UERAHEtSf+NFXJKuRQAkA2blKh1Oi8xa5YDoFk7WP4I7rhnUCTr2ncsCFIAgAOiZYJMcU1yADRrhiNeMrLUiaBPPP2kxemFUgBAB1iMkCkrJMKVgtQzlvTSAWALaL/q6b9A8G+ygpCUqjdKBQCXEM9uyRPIRYVvSSKBURORZKJOnjNIOlHS4YFceL2yyCYxVm+UCgA6cqgkUrSVIpIt4/POYodvPkmZr0p1ZCQ+mHetFX7pLOfm8HIAAKP7SDqhR4ERAXMBBr6ZWfCMWTXavVpX7R0MjOSXPP3/6XvAuQCgX1xSJHVsMeeElsEiiAUgmCkABHaJ7JSpfQs2gv/21ZoqTPdC1eKHPk396QKABU/StrMwxFSZFacmQlhWEW7ELF4b368AQbJosmhuBDpJ0mFGR/k9XA/0Np4SAKh3Dl/1GCDsJImyrH6TYtsukQRRMhYzBTMEgOC7FyNKB61YWT4W7FKtqx26kRYsulNDLZV5/wGExYf77iWJO/WAYgEGvsMgyyXbW8bL2gov6txb0ruWMSj5/9IzQIm+sReuA4K/iY1Tktha1QHBLAFQ+iQueD5XEqd9FhH+JbwA0md/NvGeIgCsQXM4sgDFvtXfpEUpSSwsT5eEUYu7eOcXZP74KrTbdg08ifz9yoLtRbPaKACwBsSduXCm2DZ65O0F8b4BDCRlxPqJ8SuVdqkWc4dI4tNERxtnLKltZZffyACwBr2PpMUMATiwo5cgrJsfqbadeOay/Vx8sx3eodoW883enp1Rm9LpEwtTAj6mpN4tMZZ1PFYNAKGAyKYVzhJXKC7FdIZkYWc98MX0qmVrrDoALGmxBQ1BQQrcIQjbBYo/dojGYtqYIwAsuVylAsVBVSCG3WKEl1iGo3SmfF4nkyEHgK2KA6p0LHyzpsgl7A0cnOEzwbZzcuQAWK4SXhnk5mFByXE4H7agi2+2dhzdLj7nVn+fIukLy9mPW8IBMK78R2/dATC6CsbtgANgXPmP3roDYHQVjNsBB8C48h+9dQfA6CoYtwMOgHHlP3rrDoDRVTBuBxwA48p/9Nb/D8oIQJ+PF6HDAAAAAElFTkSuQmCC"
                                                                         height="20px" width="20px"
                                                                         style="position: absolute;left: 38%;top: 2px"/>
                                                                <#else>
                                                                    <img src="data:image/png;base64,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"
                                                                         height="20px" width="20px"
                                                                         style="position: absolute;left: 38%;top: 2px"/>
                                                                </#if>
                                                            </#if>
                                                        </p>
                                                    </div>
                                                </a>
                                            </li>
                                            <li>
                                                <a href="#">
                                                    <div class="sfq-header">
                                                        <p style="width: 22%;">
                                                            耗材比: ${hcfyzb}%
                                                        </p>
                                                        <p style="width: 26%;">
                                                            全市平均耗材比: ${hcfybgzb}
                                                        </p>
                                                        <p style="width: 25%;">
                                                            差距: ${hcfycy}%
                                                        </p>
                                                        <p style="width: 24%;height: 22px;position: relative">
                                                            <#if hcfybgzb??>
                                                                <#if hcfycy gt 0>
                                                                    <img src="data:image/png;base64,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"
                                                                         height="20px" width="20px"
                                                                         style="position: absolute;left: 38%;top: 2px"/>
                                                                <#else>
                                                                    <img src="data:image/png;base64,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"
                                                                         height="20px" width="20px"
                                                                         style="position: absolute;left: 38%;top: 2px"/>
                                                                </#if>
                                                            </#if>
                                                        </p>
                                                    </div>
                                                </a>
                                            </li>
                                        </#if>
                                    </ul>
                                </li>
                            </ul>
                        </div>
                    </div>

                    <!--2、AI分组推荐、AI病例质控、核心病种信息、AI清单质控-->
                    <div class="top_tabs">
                        <button class="tab active" onclick="openTab(event, 'group-recommend-tab')"><img
                                    src="data:image/png;base64,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" class="tab_icon"/>
                            <span style="font-size:12px;">AI分组推荐<#if zntj?? && (zntj ?size > 0)>${zntj ?size}</#if>
                        </button>
                        </span>

                        <button class="tab" onclick="openTab(event, 'med-vali-tab')"><img
                                    src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMgAAADICAYAAACtWK6eAAAAAXNSR0IArs4c6QAAFM9JREFUeF7tnet53UYOhnlUQnZbUB2xy/GmCEdFZFNO7DrcQuISdPYZSqOlKA7x4TYDkvCffTaaK4AXlxmS5zblP5UE/v3nj0/LAZ6fp/n/3x6mX1sD3+8vbfb+3W7Tt62/35+n78v//vDw0u7vL4+b7al58u+EHlJA+xKoABTDXxo9YuSjZFvhqjAlRHJN3ORdz9NzGQXu0/S17iwyBFLpb8GT0actzUsCUoCoEeGMEEjhme7TU+lbIk5C8yLF0wNSo0ONDFGAaNYYQH0iBoDZsaxxmaZdEZrTAdIrOiwNfFk413zfq3Be1kRLe6/1kbcDqNBcJcocHpBlhLA2jmW+fsRCtzqLClKByFpGNS37+dvj78wAdYjmhwSkKN4yZaog3KaXHNzL+0eyiCU8puC81jFnAeYwgFQotB5wDcMV8+oWqObQnACW0IBooVinSAkDP4a9S9Nu/z8CZ490UFjCAWIBRU2VEgi2GUMdfvnjx0u9IQXmQLCEAEQDxTJlSiAg+zZtpD41DA7LMEA0gi1QZJQwtXOzwVTR5T49RSvuuwMijRYViowSZrbsPpAYlkCgdAFEGi0SCncb7jaBCJYA6ZcrIJJokVB0s9lhE0lhGZF+uQDCBSOhGGarwyeeYeGchnVOv0wBSTAwe1u/ZLXudcU6ix1VOoFiAgjHC5wpWiwNvfUmofbm/90DiYu3DLcekDwLWBx7Ks+CeaZeYkCuEi3WEPR6ahaLRdutlo+plxZHffK2gAI/J+YEChsQLhjehGsMad13/Xqtpfe3XKdmrCU8np5Xs8Z1X1b6ZQwKCxA09B0ljZIeP1sqP8JYR3nHYwQopoBEB8P6MfkIxu2xhujAoKD8/M8jy763ZMkaYPa49+mv9UBRwfB8mcrDMMOOGfRd9bcnjTeOiYtN/vPl8bNWpmpArBai3Ujtz66RrCa+0jgBbriX4t5y3FZ2yQKkLOqX//64r48eLUjV2FdCoZGesm8AWDYzG6Ni/bCAJBRKw/bofp+eRhwpbx4ejQLkX3/++Gt9/GlRDCH6SigQKQVpY2SgyG4uD0iCgZhJ0DYdUrAtQB5u02eLJwvYKdZWBLFazLrwKl8uOeNlXVBT9l+WU1TxzGrYgHjSWjSU0cLfTofPYBxVLgEIeks/XLm5ADsJGIGyPlktC7Sqi9kRxPJILaOFna0deiQlKJ5XD0MASTAObc5+ixeA4nlJWDZqAgh6a5lg+NnWqUZmgGKZ0WzJkA2I5jZ9q5g6lWJzM6YSQE5HPe9ARBEkATG1gRxsRwKHBUR6rJYRJHngSAA5ifK+dhClWFJA8iiXYx7ZFgFEaouodM0AQcJh630SdLHZ7loSOCwg0rCWgFzLwDW7RU9GPS8J5UX6xse+MoJozCH7fpAA+NyW5yWhGBDN2fMW8WkeKYEE5FUCCUjCAEkAiCAaRw2tQXKTXgeWhrY86kVVs2r3+rbeVu/5q46c79sKl9CzG5Kye18SilOs0vGsgKwV8+HzoiMMEfCmZ3M8CUhPdwTOhZycjDBE5Lhzy2mB2w7ZDNmz9DSVs2HRPUiZQHpBE/myMCQgQPR4S3u5PyXAsZTObROQzgJHpgsHCAOOur8REQ6RLacNogeNk2athdN42VYaQSJfFiKK6WqAAkAiyxe1NUQPCQgqTcN2iGK6HVML4DhNFAH3LnXSHJPpXoOE9nCAYnoBguTgLUWHljFinYAeThtBQp+2AIrpAgiwDsrOuqaC1GK4fwf3f7gIgpxdJyCEtYDGQdnckaOIxo40kXdLpqYpFrqxsN4NME7vCGKp4MhH6nuAo3bk/SRvWaMYEM0lTQKybR4cw4DbHvBuBHUS0qc5qOi7/HsCspTGyAgCzD2np68Gj5y4HfVUKwHZ+EI8h2q3toSRuuX1TDje9g/2c1u3kyISkATkzbQ4kUCTcx+pHklAggJC5fUenhg2hlYtAUaRZXrm5PjNhoVl0uHXzobUIFG9WW9AqPmqxVHyQsdpXa6ZWbbRQAgg3p8crVsRA6J5m4tSuJGcWcOgqY7VCRzHqKmjZXTtRSAeUZAlaKIxuheN/XHWew1AGGkIR3ittiREjPXAzoQxZmRIEpDFUaWFMSJjICEbGQdts2vUDEOG4XhdGCcqRYUkAbkyIAw45qJ6VYRSgKLGVceJCAm6h0yxKGtg/L13BNlMsZhwkGlaa//MeaJBkoBcIIKsjZuT+lgcx3LniwRJAjLgJGVkBOEaK7fuaAUS7rxhIAEjYI9P/hTZmp5iqehnpEzcpiMAeRXuE+e3uq3gmOe+Td/++fL4mSOrEJAoAOE6BUQ2YkC2ikhUKT0Vga4JERbaphj6z98ef0fbW6RVW3NJ9t5TN5vyOQsgmpvMnkqQGAnHsC3aWkaOD+sBDW7dT3xIoBUIuF7N6xacJYojiOaYrScgVRgFlObBz/P0HfH4Zd33afraGoeb0nhFDitIXMFVnsIlIBzMtW1Br7XnVSX5b1cDBPe4FmXXNZbJwXUmIFqj5/QHlbIFiDSFGxFFUeMbCgmoC00GwzGNy6RYe0JBT7nWgEjh2Drg4ChN01YS6cp83YBOQDoKG7EkUCFlKEtAkKVFbONdvKMAZwTpZB1o9NgCZF4iA7BOW3Kdxj2SgPIMD4jmJtNdyKiJgMqow7UeHkS9HrqsyO3ci3ZQJwlIDysBlUEBQtUiRZmcG/UeW5/roPIa7zRNyBF3tzoE1MkhAUE9aZQIwkmvqkEVY+Iop+bs3Ll6QPIWDUCj9K4/OCmr5qKaI1vxKZbmHDoEIKBRtIRJ7b9eKt7v06fZS//nUSxrjkI5bZd7oKKge2pVFw7qJQHhaFrQ1sJgW5CU5Tzfp7+Wy7KYT7DN3S4U5G+pZc+vM4KAzBE9v2pibRKv4zGUQK0ATTsiAtJc+0I+vaM9FcmW+khAKOsU/t3aWCFIdn7GWbgNdbd1lHs34Ot6y7NnNU1UTwgMkIAMeKNwrXj01AbQ59ykt5dF13XEdqcBZMtroqdY3Qq+DQuxjh498/RiPD29+QjATg0IanzDADGsPbaMx3pf9RH92zS9vZmoiVYzYM/T93ntt/Zj+yPAqHMmIANTLBRgjYEsI+uWgSP1SonEZQ2ty0VkjM09rBxEvSiMBAsHkMP9BBtqgNaeFjJo5+jx7nTljx+/PzxM37YMnIoAiIFIAdnTT9HJ7WH6dXQKh+y/yvq0gEgVDIHQaITCq5kD7bv3QTjEQCjIkOix1UY0LrppRjtUVwkIQ6i7TY2ih9UzVZSDQA47uF9dRJ46HhLZFQcpCYgRIKhH2puupiB7753PHvh5+kQdI1OeGgGEgmy9F0QGCchHCxA/H6T5tSOuclWcANGjGEarZvhwjNsYb2n0iIFr0yyWMQMyKPtkRyWVYtqdEZhL79ARRHPN3xMQRNjId6yWBrkGYB0RkDqCkgG1bioKLc0PArbn81YEWNTe94p0ZK8crs0iCGIUexvjLBpuC3jOaviUUlpPvm55ckQWlIEjikY9PrW3OXocEBD0YUvYXjYaigDhvA+xtThUsZqNlb4cw6DaflDGfXraOxalDNwCECoKzfIDnESk9KqshZLdh7R3YShoX9S2zgsI0zDYgBASRhS1Z+AWUYjrJFCj8W6HyK6sQeuokX2IANG8j97FW6FwLNIKa0AQA6ciALUmKgqh0YNaB2JIlm0uDQipVANJU4b1FqIXL9xQfbg5OgIIJQvEUHbTVcBRUGswUAd/CGDdrQiCyJ2zILMIgiizGRY5K6bagsJdGzwFiMTLIjLZM3Ck/966qD1FK86rajlGrjlNpUyp/F0EiOb8meuJkU1YCJYyJi9A3OoQ1FEwfweRow9pWw0gaN2Fru1UgCAed8trIgqRAIKMSzkMCtxmigQAQs2NGpF1O0RudU6Ns0bWLQJEc4vuqRTKmFopBaIQL0CoGgCBXqoPT10gxrfXBtFl6a95cQ9ZowkgiIHtEY8sFGqDeM2NlAJZvwSQsmapgdf9Iv0/rA2Qw+wsAqZXdd8oIN6XhecC5FW6LaNqeczRgGjrEO6BQ9TifOkEEcfQ2gfaF3G6bEC0lzPdvNaGF23NjQAiXTcy9l6axe5/guiBRt7STmuPFCTnBaTsfGEse/k2YoSjAEFPZd7WBwASufbgpJYtQBB9UmDUv7MB0dyiUwUpumhuO/JrIIhRKfJ1JJ+2ug+B5gr0YGJTl4BOal/PuxATQNCcbxQgJFCAMqQRBE0Xdg8BgPXN/cEfI9XshZSlUQNOFDgNIGFDO2CAGqNCHIhFCnia6FFusG/TN/RXgz3vQtgRRLOYqwKCKJuKrlvGv/yCPDJH697AyOmbDoPup7UnxFkgC05AVsV8S2iaCIIqG6lD1j+r8LZeIApSECIG07MNauSel4VsQKS3tqG9F2BcGkCQkygkuu4dNiDGhMzREwBqLmRPZQzPy0I1IKh3PDIgFp63VYc0IwJlPcu/A4DPhqQ4ieMsx6otUrudCpCwCiIMzBqQMt5LZmfz0wKIpz1a9EBP/0o7z8tCVgTRLiQqIJSnsgBk9qrEe+wiz3vS6KEFhJPZ7Mk9AQEeKDQDREQA0QkA5IjRozoU6iN8VTpedyEsQDS36JFz4G4RxAGQs6ZXpwGEMq6lTVw+xbIGBIgekR0TKQ5wf60DIMR5UGtQR5ArALL3mPzbD9JQkjb+O/Wp1HeOqTx7Jfk3+kd2EhCJ1uz7UJC3ALHwUPa7sRtR+pKY2QqOBojmMZPIoZ4y9ATEzOR5AykBoRwfshhWiqUFZLhHakgkAdkWzHB9MQDxuk1XA8KhdLjADQGxOmdHvNioNqP1xbGtEIBoFxH1PF4SQRIQf2wpvSxXoM1uWrthRRAtIFEv3ChFbO07AUlAPkhA+6hJAuJvVJYzjEyxuA5I85T5nsxYEUQLSNSTLCqCbBkKV4GWhttrrKEXu4wCfcuurPSjBoS7kJFeqWVYEkDmse7TU7mw62WwPecpP0Y6jbwoPCIgFqRGS7MQwCNC3ROWEXNRTmu5JovMxqRIb6VInM1ES7MSkBHmT8zJjR5bnzFijmEGiMX7v+E88k6qNDzVCGi/3kvi3H/MDncDEO4YZoBshTPEC5Mh0VvqOf5hJGCRkXDHCAVItDTrMJZzhYUyUyPP+qOIm3WKVfVjcWsZLs26gvEdYI9cz699iY8SiRkgkpxv6Dk7JZn8e38JMKNHWaCFs97bqAgQizqkVVz110rOGEUC3OhhdapqDkhrYRlFopjaAdchiB7e6ZW4BmmFNu5pVkaRAxqy05IjRg8VIK0bcclGs2B3srqjDBs0eqgAaUURSZoV7fGTo9jVGdYpyTpaKf7yF8WsZCMq0uvkVsV6CzarTeY4cSUgyTh6fiNABYhlFElI4hqx28oEqVXP6KFOscoAllEkUy03U4w3sBSOxu8rSiIRIhR1BLEmOup764gwsw0uAalBb14uC2FDVmsDSINqScGeqRaitoO3ERp0z9qjStgEkJZRS08oMtU6OAB7yzeGw+Pkarl8M0BaRi2NIgnJCSERwmGdxnMkawaIdRSZhXKEH7znSPvKbTVwdC7MXSJI60RrnsxBOFe2taPtXZpqe9kUR36mEWTP60tTrYwkHHXGa6uBo5laTdMkPQXjSsgckL1TKA0k+bwWV7Ux2mt03kyxFRkJVyougLQKbLU3yZqEq9+h7Y8ORxGeCyC7aZGS/izch9o8NHlxhLdpevr7y6Poo3oRIkfdqBsgCQlkS6drpM0SItQdbqdYW9pu1Q6a8JuFe0yuLOBo1prKzEMqMdcIsntMB/w+ObWpTLcoCfX7uwUckVKrLilWnWTvV2L/+fL4WaPGhEQjPZu+Z4XDtUhfiz4hsTHGcKMYpD6etqGVl3uKtVygpyDy2S2tKQj6nxyOrhGkit+7CMsLRYGhC7poD1mog5ZeN+XU1rtGkF6QZF1CqV3+d4t6g4LDAj75Dt/3HAJIWYJ3JElIrExkMY5BSnUkOIakWFXcezWDlZcqc9yn6ev9Pn1yMJdLDWnl1XcdlxGAlooZFkHKJnpAQnksS2GecSwrZ0XqISAcQyPIMpK0vHw35ZzRsi32ZGi0e5HDKjpZbHk9xtAIslxMqybRPvi23nDWJrQZWct872QxMhwhIggCydymkzejzefkLQzlTNWA0eEIB8ju6ZYxJGROfHIOPqQSykfU1+NRF7dHgCMkIAlJfzKtjXUvjbVO37ylFaYG4dQKlsV7nfeKtUlvOXrMd1lAkBTI2vMhc3orpMf4Hl6cqjcsa8geMqpzhI0gsGc3LCrrnHP+/Dx9mm7T157K8J7LA4yyZqreOCocYWuQrYJv90bcAZLTRZNBMvKI8t6OZDl++AiyXCxZJwwygp4KY8/lJBMqpfKKVuz9KzscChDIqzsZRJ379jD9Gv3ZLm/jHOWolLYu6n44QBBIuhjIS4Iaqkbx3jcVNWYLdHRQIgtXdjokICML+KW8oxTz3mBEcEpKOxd3PzQgkRRHph1iFbU79gDjilHjsEV6y1TIY8aOod87qvSAAo7QBp9ucvAbpkMePoKwTrk6gvIW3QxqlZ5QlOUiUeOIt+ISck4FCJJyjSwk5zQMAKYCUZpKv28rMYbSB/noxdHvNjiyOR0gnPQgwolL8dYjQFgbCVJDXSVqnK4G2atNoHfST3Y0yfGQaDql+Vo7Zz3R2p42gkhqk9Ln52+PL2nQyf8hYIxMR6OI/xKAcNOuM4OCgtH7YCAKEOt1XAqQWsRDj4vcp6eHh+lb7yLZy1ASDJlkLwcIK5qUxgcHBQUj06ltgC4LiASUI6VeHDAynWpHl8sDwgblNapELeYTDFkq1eqVgKwkg9wHvHUJdDycYNiCUUdLQBpy5YIyIv3iQFHWl6kUH6IEhJAZC5TX9MsTlgrFPBX4Ue4Egw9GRhCmzNigGMIigSIjBlPBjeYZQZhyLKBA9yircYsXvz9P38t/3rtfqc9mla+qaOaJeojAFPfw5gmIUAVW730UcDjpUvO0xfjToUKxnK5bAmKgUvQxdoOp3g2RtYW1RD+Ol4AYy9gblpHvihiL6hDDJSCOarKCJaFwVBIxdALSUfZvdUu5k9j4vlatR+YTqGl6Kv97loclO4rZdKr/ARmrtPVk6KZuAAAAAElFTkSuQmCC" class="tab_icon"/>
                            <span style="font-size:12px;">AI病例质控
                                <#assign med_val_size= 0>
                                <#if logicalCheckList?? && (logicalCheckList ?size > 0)>
                                    <#assign med_val_size= med_val_size+logicalCheckList ?size>
                                </#if>
                                <#if integrityCheckList?? && (integrityCheckList ?size > 0)>
                                    <#assign med_val_size= med_val_size+integrityCheckList ?size>
                                </#if>
                                ${(med_val_size)!}
                            </span>
                        </button>

                        <button class="tab" onclick="openTab(event, 'med-dis-core-tab')"><img
                                    src="data:image/png;base64,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" class="tab_icon"/>
                            <span style="font-size:12px;">核心病种信息<#if hxbz?? && (hxbz ?size > 0)>${hxbz ?size}

                                </#if>
                            </span>
                        </button>
                        <#if isWeb?? && isWeb == '1'>
                            <button class="tab" onclick="openTab(event, 'setl-vali-tab')">
                                <img src="data:image/png;base64,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"
                                     class="tab_icon"/>
                                <span style="font-size:12px;">AI清单质控
                                    <#if qualityInfo??>
                                        <#assign setl_val_size= 0>
                                        <#list qualityInfo as qiItem>
                                            <#if qiItem.data?? && (qiItem.data?size>0)>
                                                <#list qiItem.data as midSubQiItem>
                                                    <#list midSubQiItem.data?keys as key>
                                                        <#if midSubQiItem.data[key]?? && (midSubQiItem.data[key]?size>0)>
                                                            <#assign setl_val_size=setl_val_size+midSubQiItem.data[key]?size>
                                                        </#if>
                                                    </#list>
                                                </#list>
                                            </#if>
                                        </#list>
                                        ${(setl_val_size)!}
                                    </#if>
                                </span>
                            </button>
                        </#if>
                        <button class="tab" onclick="openTab(event, 'setl-leak-tab')">
                            <img src="data:image/png;base64,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"
                                 class="tab_icon"/>
                            <span style="font-size:12px;">智能查漏
                                                           <#if oprnChrgDetl?? && (oprnChrgDetl ?size > 0)>${oprnChrgDetlTotal}

                                                           </#if>
                                                        </span>
                        </button>
                    </div>

                    <!-- 1、AI分组推荐 -->
                    <div id="group-recommend-tab" class="tabcontent">
                        <div class="group-recommend">
                            <#--                            <div class="title">-->
                            <#--                                <img src="data:image/png;base64,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" height="35px" width="35px"/>-->
                            <#--                                <span>AI分组推荐</span>-->
                            <#--                            </div>-->
                            <#--                            <div class="divider"></div>-->
                            <div class="group-recommend-content">
                                <ul id="${dipCodg???string('accordion_2','ac_2')}" class="accordion"
                                    style="margin-top: 1rem;">
                                    <li>
                                        <#--                                        <div class="link">-->
                                        <#--                                            <img src="data:image/png;base64,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" height="25px" width="25px"-->
                                        <#--                                                 class="des-icon"/>-->
                                        <#--                                            <#if zntj?? && (zntj ?size > 0)>-->
                                        <#--                                                <div class="sfq-header">-->
                                        <#--                                                    <#list zntj as dip>-->
                                        <#--                                                        <#if dip_index=0>-->
                                        <#--                                                            <p style="width: 22%;">-->
                                        <#--                                                                ${dip.dipCodg}-->
                                        <#--                                                            </p>-->
                                        <#--                                                            <p style="width: 26%;">-->
                                        <#--                                                                ${dip.dipName}-->
                                        <#--                                                            </p>-->
                                        <#--                                                            <p style="width: 25%;">-->
                                        <#--                                                                病组费用: ${dip.lastYearLevelStandardCost}-->
                                        <#--                                                            </p>-->
                                        <#--                                                            <p style="position: relative;width: 24%;height: 16px;color: #C00017;">-->
                                        <#--                                                                <#list 2..0 as t>-->
                                        <#--                                                                    <img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAWlJREFUOE+lkz9Iw0AUxr+XtIk4CqKCXqlW2uDk5qKbIqKLIAjuDoLgoptYR0EUFAe3Di5OgovQRRxd3NTE+qdNs+jqYHNt8yTFSqqNUr3t7n3vx/e+uyP8c9FP/ZbQNvx60pabYbpQwHUvOlRFKxDwkrTlQMsAM6YtgHHkNxJhNlmQJ80goQ4soV8weOyj6Thly/lQQK4bnZVIZCgoIEU5D+xd9rzJYD0K1Uk47n3NgSk0/stlpGxJdcAagK1WIEy0ahTc7c8M7oQ+7YH3AMR/AT0w05JRdLO1gIPifKy9p8SVQwAzIZBTTZWL/U94rte/3YIlolkGjTcFMM5SRTnVEPZXoSW0NwbawsbwgwsF5GLR4SrTVcBexgO/Emi5flb1eGTIKV82HcEU+grAuwQ4HvO6USxnfOFtnz4B4gMCEgSkg3+jwY4ptDkGj0JRd4x8KR+0+hhHl6xq+yqQHrTlTWiIrbwFX/sOGD12EZYVIWgAAAAASUVORK5CYII="-->
                                        <#--                                                                         height="16px" width="16px"/>-->
                                        <#--                                                                </#list>-->
                                        <#--                                                            </p>-->
                                        <#--                                                        </#if>-->
                                        <#--                                                    </#list>-->
                                        <#--                                                </div>-->
                                        <#--                                                <div class="sfq-header" style="display: none">-->
                                        <#--                                                    <p style="width: 22%;">病组信息</p>-->
                                        <#--                                                </div>-->
                                        <#--                                                <i class="fa fa-chevron-down"></i>-->
                                        <#--                                            <#else>-->
                                        <#--                                                <div class="sfq-header" style="display: flex">-->
                                        <#--                                                    <p style="width: 22%;">暂无病组信息</p>-->
                                        <#--                                                </div>-->
                                        <#--                                            </#if>-->
                                        <#--                                        </div>-->

                                        <ul class="submenu" style="display: block;"
                                            id="${(zntj?? && (zntj ?size > 0))?string('secondaryMenu','')}">
                                            <#if zntj?? && (zntj ?size > 0)>
                                                <#list zntj as dip>
                                                    <li class="open">
                                                        <#if dipCodg?? && dipCodg == dip.dipCodg>
                                                        <a href="#" class="a-hover secondary-menu">
                                                            <#else >
                                                            <a href="#" class="secondary-menu">
                                                                </#if>

                                                                <div class="sfq-header">
                                                                    <p style="width: 22%;">
                                                                        ${dip.dipCodg}
                                                                    </p>
                                                                    <p style="width: 26%;">
                                                                        ${dip.dipName}
                                                                    </p>
                                                                    <p style="width: 25%;">
                                                                        病组费用:  <#if dip.refeStandfee??>
                                                                           <#if dip.refeStandfee != 0>
                                                                               ${dip.refeStandfee}
                                                                           <#else>
                                                                               ${dip.levelCost!0}
                                                                           </#if>
                                                                       <#else>
                                                                           ${dip.levelCost!0}
                                                                       </#if>
                                                                    </p>
                                                                    <p style="width: 24%;">
                                                                        <#if dip_index=0>
                                                                            <#list 2..0 as t>
                                                                                <img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAWlJREFUOE+lkz9Iw0AUxr+XtIk4CqKCXqlW2uDk5qKbIqKLIAjuDoLgoptYR0EUFAe3Di5OgovQRRxd3NTE+qdNs+jqYHNt8yTFSqqNUr3t7n3vx/e+uyP8c9FP/ZbQNvx60pabYbpQwHUvOlRFKxDwkrTlQMsAM6YtgHHkNxJhNlmQJ80goQ4soV8weOyj6Thly/lQQK4bnZVIZCgoIEU5D+xd9rzJYD0K1Uk47n3NgSk0/stlpGxJdcAagK1WIEy0ahTc7c8M7oQ+7YH3AMR/AT0w05JRdLO1gIPifKy9p8SVQwAzIZBTTZWL/U94rte/3YIlolkGjTcFMM5SRTnVEPZXoSW0NwbawsbwgwsF5GLR4SrTVcBexgO/Emi5flb1eGTIKV82HcEU+grAuwQ4HvO6USxnfOFtnz4B4gMCEgSkg3+jwY4ptDkGj0JRd4x8KR+0+hhHl6xq+yqQHrTlTWiIrbwFX/sOGD12EZYVIWgAAAAASUVORK5CYII="
                                                                                     height="16px" width="16px"/>
                                                                            </#list>
                                                                        <#elseif dip_index=1>
                                                                            <#list 1..0 as t>
                                                                                <img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAWlJREFUOE+lkz9Iw0AUxr+XtIk4CqKCXqlW2uDk5qKbIqKLIAjuDoLgoptYR0EUFAe3Di5OgovQRRxd3NTE+qdNs+jqYHNt8yTFSqqNUr3t7n3vx/e+uyP8c9FP/ZbQNvx60pabYbpQwHUvOlRFKxDwkrTlQMsAM6YtgHHkNxJhNlmQJ80goQ4soV8weOyj6Thly/lQQK4bnZVIZCgoIEU5D+xd9rzJYD0K1Uk47n3NgSk0/stlpGxJdcAagK1WIEy0ahTc7c8M7oQ+7YH3AMR/AT0w05JRdLO1gIPifKy9p8SVQwAzIZBTTZWL/U94rte/3YIlolkGjTcFMM5SRTnVEPZXoSW0NwbawsbwgwsF5GLR4SrTVcBexgO/Emi5flb1eGTIKV82HcEU+grAuwQ4HvO6USxnfOFtnz4B4gMCEgSkg3+jwY4ptDkGj0JRd4x8KR+0+hhHl6xq+yqQHrTlTWiIrbwFX/sOGD12EZYVIWgAAAAASUVORK5CYII="
                                                                                     height="16px" width="16px"/>
                                                                            </#list>
                                                                        <#elseif dip_index=2>
                                                                            <img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAWlJREFUOE+lkz9Iw0AUxr+XtIk4CqKCXqlW2uDk5qKbIqKLIAjuDoLgoptYR0EUFAe3Di5OgovQRRxd3NTE+qdNs+jqYHNt8yTFSqqNUr3t7n3vx/e+uyP8c9FP/ZbQNvx60pabYbpQwHUvOlRFKxDwkrTlQMsAM6YtgHHkNxJhNlmQJ80goQ4soV8weOyj6Thly/lQQK4bnZVIZCgoIEU5D+xd9rzJYD0K1Uk47n3NgSk0/stlpGxJdcAagK1WIEy0ahTc7c8M7oQ+7YH3AMR/AT0w05JRdLO1gIPifKy9p8SVQwAzIZBTTZWL/U94rte/3YIlolkGjTcFMM5SRTnVEPZXoSW0NwbawsbwgwsF5GLR4SrTVcBexgO/Emi5flb1eGTIKV82HcEU+grAuwQ4HvO6USxnfOFtnz4B4gMCEgSkg3+jwY4ptDkGj0JRd4x8KR+0+hhHl6xq+yqQHrTlTWiIrbwFX/sOGD12EZYVIWgAAAAASUVORK5CYII="
                                                                                 height="16px" width="16px"/>
                                                                        <#else>
                                                                        </#if>
                                                                    </p>
                                                                </div>
                                                                <#if dipCodg?? && dipCodg == dip.dipCodg>
                                                            </a>
                                                            <#else >
                                                        </a>
                                                        </#if>

                                                        <ul class="secondary-submenu">
                                                            <li>
                                                                <a href="#">
                                                                    <div class="sfq-header">
                                                                        <p style="width: 35%;">
                                                                            dip编码: ${dip.dipCodg}
                                                                        </p>
                                                                        <p style="width: 55%;">
                                                                            dip名称: ${dip.dipName}
                                                                        </p>
                                                                    </div>
                                                                </a>
                                                            </li>
                                                            <li>
                                                                <a href="#">
                                                                    <div class="sfq-header">
                                                                        <p style="width: 22%;">
                                                                            基准分值: ${dip.jzfz}
                                                                        </p>
                                                                        <p style="width: 26%;">
                                                                            级别基本系数: ${dip.jbjbxs}
                                                                        </p>
                                                                        <p style="width: 25%;">
                                                                            计算分值: ${dip.ycfzFirst}
                                                                        </p>
                                                                        <p style="position: relative;width: 24%;">
                                                                            病例类型: ${dip.balx}
                                                                        </p>
                                                                    </div>
                                                                </a>
                                                            </li>
                                                            <li>
                                                                <a href="#">
                                                                    <div class="sfq-header">
                                                                        <p style="width: 22%;">
                                                                            中医优势病例系数: ${dip.zyysjcxs}
                                                                        </p>
                                                                        <p style="width: 26%;">
                                                                            基层病种系数:${dip.jcbzjcxs}
                                                                        </p>
                                                                        <p style="width: 25%;">
                                                                            <#if dlbzjcxs??>
                                                                                年龄病种系数: ${dlbzjcxs}
                                                                            </#if>
                                                                        </p>
                                                                        <p style="position: relative;width: 24%;">
                                                                            重点学科系数: ${dip.zdxkjcxs}
                                                                        </p>
                                                                    </div>
                                                                </a>
                                                            </li>
                                                            <li>
                                                                <a href="#">
                                                                    <div class="sfq-header">
                                                                        <p style="width: 22%;">
                                                                            加成分值: ${dip.jcfz}
                                                                        </p>
                                                                        <p style="width: 26%;">
                                                                            总分值: ${dip.zfz}
                                                                        </p>
                                                                        <p style="width: 25%;">
                                                                            分值单价: ${dip.fzdj}
                                                                        </p>
                                                                        <p style="position: relative;width: 24%;">
                                                                            预测费用: ${dip.ycfy}
                                                                        </p>
                                                                    </div>
                                                                </a>
                                                            </li>
                                                            <li>
                                                                <a href="#">
                                                                    <div class="sfq-header">
                                                                        <p style="width: 48%;">
                                                                            诊断编码: ${dip.diagnoseCode}
                                                                        </p>
                                                                        <p style="width: 48%;">
                                                                            <#if dip.fycy gt 0>
                                                                                <span style="color: rgb(0 159 7);font-size: 12px;">预测金额差异: ${dip.fycy}</span>
                                                                            <#else>
                                                                                <span style="color: red;font-size: 12px;">预测金额差异: ${dip.fycy}</span>
                                                                            </#if>
                                                                        </p>
                                                                    </div>
                                                                </a>
                                                            </li>
                                                        </ul>
                                                    </li>
                                                </#list>
                                            </#if>
                                        </ul>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <!--2、AI病例质控-->
                    <div id="med-vali-tab" class="tabcontent" style="display: none">
                        <div class="pre-pay">
                            <#--                            <div class="title">-->
                            <#--                                <img src="data:image/png;base64,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" height="35px" width="35px"/>-->
                            <#--                                <span>AI病例质控</span>-->
                            <#--                            </div>-->
                            <#--                            <div class="divider"></div>-->
                            <div class="record-content">
                                <ul id="accordion_3" class="accordion" style="margin-top: 1rem;">
                                    <li>
                                        <#--                                        <div class="link">-->
                                        <#--                                            <img src="data:image/png;base64,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" height="25px" width="25px"-->
                                        <#--                                                 class="des-icon"/>-->
                                        <#--                                            <div class="sfq-header">-->
                                        <#--                                                <p style="width: 22%;">-->
                                        <#--                                                    病例得分: ${medicalRecordScore}-->
                                        <#--                                                </p>-->
                                        <#--                                                <p style="width: 26%;">-->
                                        <#--                                                    逻辑性错误: ${countLogicalCheck}-->
                                        <#--                                                </p>-->
                                        <#--                                                <p style="width: 25%;">-->
                                        <#--                                                    完整性错误: ${countIntegrity}-->
                                        <#--                                                </p>-->
                                        <#--                                            </div>-->
                                        <#--                                            <div class="sfq-header" style="display: none">-->
                                        <#--                                                <p style="width: 22%;">质控信息</p>-->
                                        <#--                                            </div>-->
                                        <#--                                            <i class="fa fa-chevron-down"></i>-->
                                        <#--                                        </div>-->
                                        <ul class="submenu" style="display: block;">
                                            <div class="sfq-header">
                                                <li>
                                                    <a href="#">
                                                        <p style="width: 22%;">
                                                            病例得分: ${medicalRecordScore}
                                                        </p>
                                                        <p style="width: 26%;">
                                                            逻辑性错误: ${countLogicalCheck}
                                                        </p>
                                                        <p style="width: 25%;">
                                                            完整性错误: ${countIntegrity}
                                                        </p>
                                                    </a>
                                                </li>
                                                <li><a href="#"><p>逻辑性问题:</p></a></li>
                                                <#if logicalCheckList?? && (logicalCheckList?size > 0)>
                                                    <#list logicalCheckList as item>
                                                        <li class="quality-control-item">
                                                            <a href="#">
                                                                <p>${item_index+1}、${item}</p>
                                                            </a>
                                                        </li>
                                                    </#list>
                                                </#if>
                                                <li><a href="#"><p>完整性问题:</p></a></li>
                                                <#if integrityCheckList?? && (integrityCheckList?size > 0)>
                                                    <#list integrityCheckList as data>
                                                        <li class="quality-control-item">
                                                            <a href="#">
                                                                <p>${data_index+1}、${data.content}</p>
                                                                <p style="float: right;color: rgba(255, 0, 0, 0.93);">
                                                                    扣${data.refer_sco}分</p>
                                                            </a>
                                                        </li>
                                                    </#list>
                                                </#if>
                                            </div>
                                        </ul>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <!-- 3、相关核心病种 -->
                    <div id="med-dis-core-tab" class="tabcontent" style="display: none">
                        <div class="core-group">
                            <#--                            <div class="title">-->
                            <#--                                <img src="data:image/png;base64,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" height="35px" width="35px"/>-->
                            <#--                                <span>核心病种信息</span>-->
                            <#--                            </div>-->
                            <#--                            <div class="divider"></div>-->
                            <div class="group-recommend-content">
                                <ul id="accordion_7" class="accordion" style="margin-top: 1rem;">
                                    <li>
                                        <#--                                        <div class="link">-->
                                        <#--                                            <img src="data:image/png;base64,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" height="25px" width="25px"-->
                                        <#--                                                 class="des-icon"/>-->
                                        <#--                                            <#if hxbz?? && (hxbz ?size > 0)>-->
                                        <#--                                                <div class="sfq-header">-->
                                        <#--                                                    <#list hxbz as dip>-->
                                        <#--                                                        <#if dip_index=0>-->
                                        <#--                                                            <p style="width: 22%;">-->
                                        <#--                                                                病组编码: ${dip.coreDipCode}-->
                                        <#--                                                            </p>-->
                                        <#--                                                            <p style="width: 26%;">-->
                                        <#--                                                                病组名称: ${dip.coreDipName}-->
                                        <#--                                                            </p>-->
                                        <#--                                                            <p style="width: 25%;">-->
                                        <#--                                                                病组费用: ${dip.coreDipAvgCostLevel}-->
                                        <#--                                                            </p>-->
                                        <#--                                                        </#if>-->
                                        <#--                                                    </#list>-->
                                        <#--                                                </div>-->
                                        <#--                                                <div class="sfq-header" style="display: none">-->
                                        <#--                                                    <p style="width: 22%;">病组信息</p>-->
                                        <#--                                                </div>-->
                                        <#--                                                <i class="fa fa-chevron-down"></i>-->
                                        <#--                                            <#else>-->
                                        <#--                                                <div class="sfq-header" style="display: flex">-->
                                        <#--                                                    <p style="width: 22%;">暂无病组信息</p>-->
                                        <#--                                                </div>-->
                                        <#--                                            </#if>-->
                                        <#--                                        </div>-->
                                        <ul class="submenu" style="display: block;"
                                            id="${(hxbz?? && (hxbz ?size > 0))?string('secondaryMenu1','')}">
                                            <#if hxbz?? && (hxbz ?size > 0)>
                                                <#list hxbz as dip>
                                                    <li class="open">

                                                        <a href="#" class="secondary-menu">
                                                            <div class="sfq-header">
                                                                <p style="width: 50%;">
                                                                    病组编码: ${dip.coreDipCode}
                                                                </p>
                                                                <#--                 <p style="width: 40%;"> -->
                                                                <#--                    病组名称: ${dip.coreDipName} -->
                                                                <#--                </p>                            -->
                                                                <p style="width: 40%;">
                                                                    标杆费用: ${dip.coreDipAvgCostLevel}
                                                                </p>
                                                            </div>
                                                        </a>

                                                        <ul class="secondary-submenu">
                                                            <li>
                                                                <a href="#">
                                                                    <div>
                                                                        <p style="width: 90%;">
                                                                            DIP编码: ${dip.coreDipCode}
                                                                        </p>
                                                                        <p style="width: 90%;">
                                                                            DIP名称: ${dip.coreDipName}
                                                                        </p>
                                                                    </div>
                                                                </a>
                                                            </li>
                                                            <li>
                                                                <a href="#">
                                                                    <div class="sfq-header">
                                                                        <p style="width: 40%;">
                                                                            亚目编码: ${dip.coreDiagnoseCode}
                                                                        </p>
                                                                        <p style="width: 60%;">
                                                                            <#if dip.coreDiagnoseName??>
                                                                                亚目名称: ${dip.coreDiagnoseName}
                                                                            <#else>
                                                                                亚目名称:无
                                                                            </#if>
                                                                        </p>
                                                                    </div>
                                                                </a>
                                                            </li>
                                                            <#if dip.coreOperationCode??>
                                                                <li>
                                                                    <a href="#">
                                                                        <div class="sfq-header">
                                                                            <#if dip.coreOperationCode??>
                                                                                <p style="width: 40%;">
                                                                                    操作编码: ${dip.coreOperationCode}
                                                                                </p>
                                                                                <p style="position: relative;width: 60%;">
                                                                                    <#if dip.coreDiagnoseName??>
                                                                                        操作名称: ${dip.coreOperationName}
                                                                                    <#else>
                                                                                        亚目名称:无
                                                                                    </#if>

                                                                                </p>
                                                                            </#if>
                                                                        </div>
                                                                    </a>
                                                                </li>
                                                            </#if>
                                                            <li>
                                                                <a href="#">

                                                                    <div class="sfq-header">
                                                                        <p style="width: 90%;">
                                                                            辅助目录：
                                                                        </p>
                                                                        <p style="width: 40%;">
                                                                            烧伤: ${dip.coreAuxiliaryBurn}
                                                                        </p>
                                                                        <p style="width: 50%;">
                                                                            年龄: ${dip.coreAuxiliaryAge}
                                                                        </p>
                                                                        <p style="width: 40%;">
                                                                            疾病严重: ${dip.coreAuxiliaryIllness}
                                                                        </p>
                                                                        <p style="width: 50%;">
                                                                            肿瘤严重: ${dip.coreAuxiliaryTumour}
                                                                        </p>
                                                                    </div>
                                                                </a>
                                                            </li>
                                                            <li>
                                                                <a href="#">
                                                                    <div class="sfq-header">
                                                                        <p style="width: 30%;">
                                                                            基础分组: ${dip.coreScore}
                                                                        </p>
                                                                        <p style="position: relative;width: 60%;">
                                                                            标杆费用: ${dip.coreDipAvgCostLevel}
                                                                        </p>
                                                                    </div>
                                                                </a>
                                                            </li>
                                                        </ul>
                                                    </li>
                                                </#list>
                                            <#else>
                                                <a href="#">
                                                    <div class="sfq-header">
                                                        该诊断无核心病种信息推荐,
                                                    </div>
                                                    <div class="sfq-header">
                                                        请检查主诊断（第一个西医疾病编码）是否为西医诊断。
                                                    </div>
                                                    <div class="sfq-header">
                                                        若依然无法入组,请切换主诊断或者主手术。
                                                    </div>
                                                </a>
                                            </#if>
                                        </ul>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <!-- AI清单质控 -->
                    <#if isWeb?? && isWeb == '1'>
                        <!-- 4、清单质控分析 -->
                        <div id="setl-vali-tab" class="tabcontent" style="display: none">
                            <div class="pre-pay">
                                <div class="record-content">
                                    <ul id="accordion_5" class="accordion" style="margin-top: 1rem;">
                                        <li>
                                            <ul class="submenu" style="display: block;">
                                                <!-- 清单质控 -->
                                                <div class="sl-content">
                                                    <ul id="tabs">
                                                        <#list qualityInfo as qiItem>
                                                            <li><a href="#"
                                                                   title="tab${qiItem.index}">${qiItem.name}</a></li>
                                                        </#list>
                                                    </ul>
                                                    <div id="content">
                                                        <#list qualityInfo as qiItem>
                                                            <span id="tab${qiItem.index}"
                                                                  class="sl-content-item ${(qiItem.index == 1) ? string('sl-content-padding1', 'sl-content-padding2')}"
                                                                  style="overflow: ${(qiItem.index == 1) ? string('hidden', 'overflow')}">
                                    <#if qiItem.index == 1>
                                        <!-- 嵌套基本质控信息 -->
                                        <ul id="tabs2">
                                            <#list qiItem.data as qiChildItem>
                                                <li><a href="#"
                                                       title="tabTwo${qiChildItem.index}">${qiChildItem.name}</a></li>
                                            </#list>
                                        </ul>
                                        <div id="content2">
                                            <#if qiItem.data?size == 0>
                                                <p class="sl-qi-complete">已完成${qiItem.name}</p>
                                            </#if>
                                            <#list qiItem.data as qiChildItem>
                                                <span id="tabTwo${qiChildItem.index}" class="sl-content-item"
                                                      style="overflow: auto">
                                                        <#list qiChildItem.data?keys as key>
                                                            <#list qiChildItem.data[key] as obj>
                                                                <tr>
                                                                    <td>
                                                                        <p>${obj_index+1 + "."} ${obj.error}</p>
                                                                    </td>
                                                                </tr>
                                                            </#list>
                                                        </#list>
                                                </span>
                                            </#list>
                                          </div>
                                    <#else>
                                    <#--                                        <#if qiItem.data?size lte 0>-->
                                    <#--                                            <p class="sl-qi-complete">已完成${qiItem.name}</p>-->
                                    <#--                                            <#else>-->
                                    <#--                                            <#list qiItem.data?keys as key>-->
                                    <#--                                                <div class="sl-qi-item-wrap">-->
                                    <#--                                                <p class="sl-qi-item-wrap-title">${key}</p>-->
                                    <#--                                                <#list qiItem.data[key] as obj>-->
                                    <#--                                                    <tr>-->
                                    <#--                                                        <td>-->
                                    <#--                                                            <p>${obj_index+1 + "."} ${obj.error}</p>-->
                                    <#--                                                        </td>-->
                                    <#--                                                    </tr>-->
                                    <#--                                                </#list>-->
                                    <#--                                            </div>-->
                                    <#--                                            </#list>-->
                                    <#--                                        </#if>-->

                                        <div id="content2">
                                            <#if qiItem.data?size == 0>
                                                <p class="sl-qi-complete">已完成${qiItem.name}</p>
                                            </#if>
                                            <div class="sl-content-item"
                                                 style="overflow: auto">
                                                <#assign curr_index = 0>
                                                <#list qiItem.data as qiChildItem>
                                                    <#list qiChildItem.data?keys as key>
                                                        <#list qiChildItem.data[key] as obj>
                                                            <#assign curr_index = curr_index + 1>
                                                            <tr>
                                                            <td>
                                                                <p>${curr_index + "."} ${obj.error}</p>
                                                            </td>
                                                        </tr>
                                                        </#list>
                                                    </#list>
                                                </#list>
                                                 </div>
                                            </div>
                                    </#if>
                                </span>
                                                        </#list>
                                                    </div>
                                                </div>
                                            </ul>

                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </#if>

                    <!-- 智能查漏 -->
                    <div id="setl-leak-tab" class="tabcontent" style="display: none">
                        <div class="pre-pay">
                            <div class="record-content">
                                <ul id="accordion_5" class="accordion" style="margin-top: 1rem;">
                                    <li>
                                        <ul class="submenu" style="display: block;">
                                            <div class="sl-content">
                                                <ul id="tabs">
                                                    <#list oprnChrgDetl as qiItem>
                                                        <li><a href="#"
                                                               title="tabThree${qiItem.index}">${qiItem.name}</a></li>
                                                    </#list>
                                                </ul>
                                                <div id="content">
                                                    <#if oprnChrgDetl?size == 0>
                                                    <p class="sl-qi-complete">已完成智能查漏</p>
                                                    <#else>
                                                    <div id="content2">
                                                        <#list oprnChrgDetl as qiItem>
                                                            <#if qiItem.data?size == 0>
                                                                <span id="tabThree${qiItem.index}" class="sl-content-item none_item_content">已完成${qiItem.name}</span>
                                                            <#else>
                                                                <span id="tabThree${qiItem.index}" class="sl-content-item">
                                                                  <#assign curr_index = 0>
                                                                    <#list qiItem.data as qiChildItem>
                                                                        <#assign curr_index = curr_index + 1>
                                                                        <tr>
                                                                         <td>
                                                                          <p>${curr_index + "."} ${qiChildItem}</p>
                                                                          </td>
                                                                       </tr>
                                                                    </#list>
                                                            </span>
                                                            </#if>
                                                        </#list>
                                                        </#if>
                                                    </div>
                                                </div>
                                            </div>
                                        </ul>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </div>
    <script>
        window.onload = function () {
            // 左边padding宽度
            var left_padding = parseFloat(window.getComputedStyle(document.getElementById("pre-pay-content-id"), null).getPropertyValue("padding-left"))
            // 各个线宽度
            var range_min = document.getElementById("range-min")
            var range_min_normal = document.getElementById("range-min-normal")
            var range_normal_max = document.getElementById("range-normal-max")
            var range_max = document.getElementById("range-max")

            var range_min_offset_left = range_min.offsetLeft
            var range_min_normal_offset_left = range_min_normal.offsetLeft
            var range_normal_max_offset_left = range_normal_max.offsetLeft
            var range_max_offset_left = range_max.offsetLeft

            var rate = 0
            <#if rate ??>
            rate = ${rate?string("0.######")}
            </#if>

            var lowLmtfee = 0
            <#if lowLmtfee ??>
            lowLmtfee = ${lowLmtfee?string("0.####")}
            </#if>

            var refeStandfee = 0
            <#if refeStandfee ??>
            refeStandfee = ${refeStandfee?string("0.####")}
            </#if>

            var upLmtfee = 0
            <#if upLmtfee ??>
            upLmtfee = ${upLmtfee?string("0.####")}
            </#if>

            var sumfee = 0
            <#if sumfee ??>
            sumfee = ${sumfee?string("0.####")}
            </#if>

            var uplmtMag = 0
            <#if uplmtMag ??>
            uplmtMag = ${uplmtMag}
            </#if>

            let maxLimit = refeStandfee / upLmtfee
            let minLimit = lowLmtfee / upLmtfee

            let normalRightRate = (sumfee - refeStandfee) / (upLmtfee - refeStandfee)
            let normalLeftRate = (sumfee - lowLmtfee) / (refeStandfee - lowLmtfee)
            let minNormalRate = (sumfee / lowLmtfee)

            let range_normal_to_max_offset_left = range_max_offset_left - range_normal_max_offset_left
            let range_min_to_normal_offset_left = range_normal_max_offset_left - range_min_normal_offset_left

            var tooltip_left = 0
            if (rate >= 1.1) {
                tooltip_left = 1.1 * range_max_offset_left - left_padding - 27
            } else if (rate >= maxLimit && rate < 1.1) {
                tooltip_left = normalRightRate * range_normal_to_max_offset_left + range_normal_max_offset_left - left_padding - 27
            } else if (rate >= minLimit && rate < maxLimit) {
                tooltip_left = normalLeftRate * range_min_to_normal_offset_left + range_min_normal_offset_left - left_padding - 27
            } else {
                tooltip_left = minNormalRate * range_min_normal_offset_left - left_padding - 26
            }
            tooltip.style.left = tooltip_left + "px"

            var curTabName = 'tabTwo1'

            $("#content span").hide(); // Initially hide all content
            $("#tabs a:first").addClass("active")
            $('#tab1').fadeIn();

            $('#tabs a').click(function (e) {
                e.preventDefault();
                $("#content span").hide(); //Hide all content
                $("#tabs a").removeClass("active")
                $(this).addClass("active")
                $('#' + $(this).attr('title')).fadeIn();
                if ($(this).attr('title') === "tab1") {
                    $('#' + curTabName).fadeIn();
                }
            });

            $("#content2 span").hide(); // Initially hide all content
            $("#tabs2 a:first").addClass("active")
            $('#tabTwo1').fadeIn();

            $('#tabThree1').fadeIn();

            $('#tabs2 a').click(function (e) {
                e.preventDefault();
                $("#content2 span").hide(); //Hide all content
                $("#tabs2 a").removeClass("active")
                $(this).addClass("active")
                $('#' + $(this).attr('title')).fadeIn();
                curTabName = $(this).attr('title')
            });
        }

        /**
         * tab点击事件
         * @param evt
         * @param tabName
         */
        function openTab(evt, tabName) {

            $("#content span").hide(); //Hide all content
            $("#tabs a").removeClass("active")
            $("#content2 span").hide(); //Hide all content
            $("#tabs2 a").removeClass("active")

            var i, tabcontent, tablinks;
            tabcontent = document.getElementsByClassName("tabcontent");
            for (i = 0; i < tabcontent.length; i++) {
                tabcontent[i].style.display = "none";
            }
            tablinks = document.getElementsByClassName("tab");
            for (i = 0; i < tablinks.length; i++) {
                tablinks[i].className = tablinks[i].className.replace("active", "");
            }
            evt.currentTarget.className += " active";

            document.getElementById(tabName).style.display = "block";

            currObj = $("#" + tabName);
            currObj.find("#tabs a:first").addClass("active")
            currObj.find("#tabs2 a:first").addClass("active")
            currObj.find('#tab1').fadeIn();
            currObj.find('#tabTwo1').fadeIn();
            currObj.find('#tabThree1').fadeIn();
        }
    </script>
    <#else>
        <div style="position: relative" class="container">
            <div style="width: 30%;height: 30%;position: absolute;top:0;left: 0;right: 0;bottom: 0;margin: auto">
                <div style="float: top;text-align: center">
                    <img src="data:image/png;base64,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" height="100px" width="100px"/>
                    </br>
                </div>
                <div style="float: bottom;text-align: center">
                    <span>${errorMsg}</span>
                    <#--                <span>预分组失败，请重新核对数据</span>-->
                </div>
            </div>
        </div>
    </#if>
</body>
</html>
