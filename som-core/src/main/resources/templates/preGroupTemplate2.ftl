<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8"/>
    <title>预分组反馈</title>
    <style>
        body{
            margin: 0;
            padding: 0;
        }
        div{
            margin: 0;
            padding: 0;
        }
        .container{
            margin: 0 auto;
            height: 1200px;
            width: 650px;
            font-family: "Helvetica Neue",Helvetica,"PingFang SC","Hiragino Sans GB","Microsoft YaHei","微软雅黑",Arial,sans-serif;
        }
        .title{
            width: 100%;
            height: 50px;
            font-weight: 600;
            text-align: center;
            /*color: #fafafa;*/
            /*letter-spacing: 0.5;*/
            /*text-shadow: 0px 1px 0px #999, 0px 2px 0px #888, 0px 3px 0px #777, 0px 4px 0px #666, 0px 5px 0px #555,  0px 8px 7px #001135*/
        }
        .content {
            width: 100%;
            height: 90%;
        }
        .des-item{
            width: 95%;
            height: 50px;
            padding-left: 5%;
            position: relative;
        }
        .des-item-label{
            color: #1f2d3d;
        }
        .des-item-content{
            color: gray;
        }

        .benchmark{
            width: 70%;
            height: 100%;
            position: absolute;
            right: 0;
            top: 0;
        }
        .benchmark-content{
            margin-left: 0.5rem;
            color: gray;
        }
        .benchmark-item{
            display: inline-block;
            width: 49%;
        }
        .non-content{
            font-size: 18px;
        }
    </style>
</head>
<body>
<div class="container">
    <div class="title">
        <h2>DIP预分组结果</h2>
    </div>
    <div class="content">
        <#if dipGroupList?? && (dipGroupList?size > 0)>
            <!-- 循环字段 -->
            <#list dipGroupList as dip>
                <div class="des-item">
                    <span class="des-item-label">${dip.label}：</span>
                    <span class="des-item-content">${dip.content}</span>

                    <!-- 标杆 -->
                    <#if dip.area?? && dip.level??>
                        <div class="benchmark">
                            <div class="benchmark-item">
<#--                                <img src="${url}/preGroupImages/benchmark_level.png" width="25" height="25"/>-->
                                <span>级别例均费用：</span>
                                <span class="benchmark-content">${dip.level}</span>
                            </div>

                            <div class="benchmark-item">
<#--                                <img src="${url}/preGroupImages/benchmark_area.png" width="25" height="25"/>-->
                                <span>区域例均费用：</span>
                                <span class="benchmark-content">${dip.area}</span>
                            </div>
                        </div>
                    </#if>

                </div>
            </#list>

            <#else>
                <div class="non-content">
                    未找到DIP组！
                </div>
        </#if>

        <div class="title">
            <h2>DRG预分组结果</h2>
        </div>
        <!-- DRG -->
        <#if drgGroupList?? && (drgGroupList?size > 0)>
            <!-- 循环字段 -->
            <#list drgGroupList as drg>
                <div class="des-item">
                    <span class="des-item-label">${drg.label}：</span>
                    <span class="des-item-content">${drg.content}</span>

                    <!-- 标杆 -->
                    <#if drg.area?? && drg.level??>
                        <div class="benchmark">
                            <div class="benchmark-item">
<#--                                <img src="${url}/preGroupImages/benchmark_area.png" width="25" height="25"/>-->
                                <span>区域例均费用：</span>
                                <span class="benchmark-content">${drg.area}</span>
                            </div>
                        </div>
                    </#if>

                </div>
            </#list>

        <#else>
            <div class="non-content">
                未找到DRG组！
            </div>
        </#if>

    </div>

</div>
</body>
</html>
