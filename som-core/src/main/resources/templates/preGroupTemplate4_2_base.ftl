<!DOCTYPE html>
<html lang="en" xmlns="http://www.w3.org/1999/html">
<head>
    <meta charset="UTF-8">
    <title>预分组/预校验</title>

    <!-- styles -->
    <link rel="stylesheet" href="${url}/preMedia/css/pre.css" media="all"/>
    <link rel="stylesheet" href="${url}/preMedia/css/sfq.css"/>
    <link rel="stylesheet" href="${url}/preMedia/css/jq22.css"/>
    <link rel="stylesheet" href="${url}/preMedia/css/jq23.css"/>

    <style>
        @font-face {
            font-family: 'FontAwesome';
            src: url('${url}/preMedia/fonts/fontawesome-webfont.eot?v=4.7.0');
            src: url('${url}/preMedia/fonts/fontawesome-webfont.eot?#iefix&v=4.7.0') format('embedded-opentype'), url('${url}/preMedia/fonts/fontawesome-webfont.woff2?v=4.7.0') format('woff2'), url('${url}/preMedia/fonts/fontawesome-webfont.woff?v=4.7.0') format('woff'), url('${url}/preMedia/fonts/fontawesome-webfont.ttf?v=4.7.0') format('truetype'), url('${url}/preMedia/fonts/fontawesome-webfont.svg?v=4.7.0#fontawesomeregular') format('svg');
            font-weight: normal;
            font-style: normal;
        }
    </style>
    <link rel="stylesheet" href="${url}/preMedia/css/font-awesome.css"/>

    <!-- scroll-js -->
    <script type="text/javascript" src="${url}/preMedia/js/jquery.min.js"></script>
    <script>if (typeof module === 'object') {
            window.jQuery = window.$ = module.exports;
        }
        ;</script>

    <!-- script -->
    <script type="text/javascript" src="${url}/preMedia/js/sfq.js"></script>
</head>
<body>
<#if code == "1">
    <div class="container">
        <div class="header">
            <span class="header-title"></span>
        </div>
        <div class="content">
            <div class="content-inner">
                <!-- DIP入组情况 -->
                <div class="in-group-circumstance">
                    <div class="hop-title">
                        <#if hospName??>
                            ${hospName}-AI小助手
                        <#else >
                            测算医院-AI小助手
                        </#if>
                    </div>
                    <div class="divider"></div>
                    <div class="title">
                        <img src="${url}/preMedia/images/med-case-ingroup.png" height="35px" width="35px"/>
                        <span>AI病例入组</span>
                    </div>
                    <div class="divider"></div>
                    <div class="in-group-content">
                        <div class="in-group-base-info">
                            <div class="in-group-base">
                                <div>
                                    <img src="${url}/preMedia/images/patient_icon.png" height="15px" width="15px"/>
                                    <span>${xm}</span>
                                </div>

                                <div>
                                    <img src="${url}/preMedia/images/gender.png" height="15px" width="15px"/>
                                    <span>${xb}</span>
                                </div>
                                <div>
                                    <img src="${url}/preMedia/images/age.png" height="15px" width="15px"/>
                                    <span>${nl}</span>
                                </div>
                                <div style="margin-right: 1rem;">
                                    <img src="${url}/preMedia/images/med_no.png" height="15px" width="15px"/>
                                    <span>${medcasCodg}</span>
                                </div>
                                <div>
                                    <img src="${url}/preMedia/images/total_fee.png" height="15px" width="15px"/>
                                    <span>${inHosCost}</span>
                                </div>
                                <div>
                                    <img src="${url}/preMedia/images/total_fee.png" height="15px" width="15px"/>
                                    <span>${preHospExamfee}</span>
                                </div>

                                <div>
                                    <img src="${url}/preMedia/images/insure_type.png" height="15px" width="15px"/>
                                    <span>${cblx}</span>
                                </div>
                            </div>
                            <div class="in-group-diagnose">
                                <div class="in-group-diagnose-details">
                                    <img src="${url}/preMedia/images/disease.png" height="15px" width="15px"/>
                                    <#if zyzd?? && (zyzd?size > 0)>
                                        <#list zyzd as zyzdbm>
                                            <#if zyzdbm_index=0 >
                                                <span>${zyzdbm}</span>
                                                <span>${zyzdmc}</span>
                                            </#if>
                                        </#list>
                                    </#if>
                                </div>
                                <div class="in-group-diagnose-details">
                                    <img src="${url}/preMedia/images/operation.png" height="15px" width="15px"/>
                                    <div class="operation" id="operation">
                                        <div>
                                            <#if zyss?? && (zyss?size > 0)>
                                                <#list zyss as zyssbm>
                                                    <span>${zyssbm}</span>
                                                    <#list ssjczmc as zyssmc>
                                                        <#if zyssmc_index= zyssbm_index>
                                                            <span>${zyssmc}</span>
                                                            </br>
                                                        </#if>
                                                    </#list>
                                                </#list>
                                            <#else>
                                                <span>暂无手术</span>
                                            </#if>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="in-group-dip-group">
                            <img src="${url}/preMedia/images/inGourp.png" height="30px" width="30px"/>
                            <#if dipCodg?? && dipName??>
                                <div>
                                    <p>
                                        ${dipCodg}
                                    </p>
                                    <p>
                                        ${dipName}
                                    </p>
                                </div>
                            <#else>
                                <div>
                                    <p style="color: red;text-align: center;font-weight: bold">${rzqk}${rzqkxx}</p>
                                </div>
                            </#if>
                        </div>
                    </div>



                    <!-- 德阳不参与分组手术 -->
                    <#if dyNotGroupOpe ?? && (dyNotGroupOpe ?size >0)>
                        <div class="group-recommend-content">
                            <ul id=accordion_4 class="accordion">
                                <li>
                                    <div class="link">
                                        <img src="${url}/preMedia/images/费用.png" height="25px" width="25px"
                                             class="des-icon"/>
                                        <div class="sfq-header">
                                            <p style="width: 48%;">
                                                德阳不参与分组手术
                                            </p>
                                        </div>
                                        <div class="sfq-header" style="display: none">
                                            <p style="width: 48%;">德阳不参与分组手术</p>
                                        </div>
                                        <i class="fa fa-chevron-down"></i>
                                    </div>
                                    <ul class="submenu">
                                        <div class="sfq-header">
                                            <#if dyNotGroupOpe ?? && (dyNotGroupOpe ?size >0)>
                                                <#list dyNotGroupOpe as dyfj>
                                                    <li>
                                                        <a href="#">
                                                            <div style="display: flex;">
                                                            <span style="width: 50%;font-size: 12px;">
                                                                    手术及操作编码: ${dyfj}
                                                            </span>
                                                            </div>
                                                        </a>
                                                    </li>
                                                </#list>
                                            </#if>
                                        </div>
                                    </ul>
                                </li>
                            </ul>
                        </div>
                    </#if>

                    <!-- 德阳限定 -->
                    <#if dyLimited ?? && (dyLimited ?size >0)>
                        <div class="group-recommend-content">
                            <ul id=accordion_6 class="accordion">
                                <li>
                                    <div class="link">
                                        <img src="${url}/preMedia/images/total_fee.png" height="25px" width="25px"
                                             class="des-icon"/>
                                        <div class="sfq-header">
                                            <p style="width: 48%;">
                                                ${dyGroupType}限定
                                            </p>
                                        </div>
                                        <div class="sfq-header" style="display: none">
                                            <p style="width: 48%;">${dyGroupType}限定</p>
                                        </div>
                                        <i class="fa fa-chevron-down"></i>
                                    </div>
                                    <ul class="submenu">
                                        <div class="sfq-header">
                                            <#if dyLimited ?? && (dyLimited ?size >0)>
                                                <#list dyLimited as dyfj>
                                                    <li>
                                                        <a href="#">
                                                            <div style="display: flex;">
                                                            <span style="width: 50%;font-size: 12px;">
                                                                    手术及操作编码: ${dyfj}
                                                            </span>
                                                            </div>
                                                        </a>
                                                    </li>
                                                </#list>
                                            </#if>
                                        </div>
                                    </ul>
                                </li>
                            </ul>
                        </div>
                    </#if>

                    <div class="divider"></div>

                    <!-- DIP付费预测 -->
                    <div class="pre-pay">
                        <div class="title">
                            <img src="${url}/preMedia/images/payment-forecast.png" height="35px" width="35px"/>
                            <span>AI付费预测</span>
                        </div>
                        <div class="divider"></div>
                        <div class="pre-pay-content" id="pre-pay-content-id">
                            <div class="tooltip__popper is-dark" id="tooltip"
                                 style="transform-origin: center bottom;z-index: 2137;top: -10px;">
                                总费用:${inHosCost}
                                <div class="popper__arrow"></div>
                            </div>
                            <div class="pre-pay-content-range">
                                <div class="low range-item" id="range-min">
                                    <div class="range-money" style="right: 50%"><${lowlmtMag}%</div>
                                    <div class="range-money" style="right: -20%;top: 100%">
                                        <#if benchmarkLevel??>
                                            ${minFee}
                                        </#if>
                                    </div>
                                </div>
                                <div class="normal range-item" id="range-min-normal">
                                    <div class="range-money-normal" style="right: 26%">${lowlmtMag}%~100%</div>
                                    <div class="range-money-normal" style="top: 100%">
                                        <#if benchmarkLevel??>
                                            ${benchmarkLevel}
                                        </#if>
                                    </div>
                                </div>
                                <div class="normal-to-high range-item" id="range-normal-max">
                                    <div class="range-money" style="right: 36%">100%~${uplmtMag}%</div>
                                    <div class="range-money" style="right: -11%;top: 100%">
                                        <#if benchmarkLevel??>
                                            ${highFee}
                                        </#if>
                                    </div>
                                </div>
                                <div class="high range-item" id="range-max">>${uplmtMag}%</div>
                            </div>

                            <ul id="${dipCodg???string('accordion','ac_1')}" class="accordion">
                                <li>
                                    <div class="link">
                                        <img src="${url}/preMedia/images/pre_cost.png" height="25px" width="25px"
                                             class="des-icon"/>
                                        <#if dipCodg??>
                                            <div class="sfq-header">
                                                <p style="width: 22%;">
                                                    预测分值: ${zfz}
                                                </p>
                                                <p style="width: 26%;">
                                                    预测费用: ${ycfy}
                                                </p>
                                                <p style="width: 25%;">
                                                    <#if fycy gt 0>
                                                        <span style="color: rgba(91,174,99,0.53);font-size: 12px;">预测金额差异: ${fycy}</span>
                                                    <#else>
                                                        <span style="color: rgba(192,0,23,0.53);font-size: 12px;">预测金额差异: ${fycy}</span>
                                                    </#if>
                                                </p>
                                                <p style="position: relative;width: 24%;text-align: right">
                                                    <#if caseTypeT == "1">
                                                        <span style="color: rgba(91,174,99,0.53);font-size: 12px">${diseType}</span>
                                                        <img src="${url}/preMedia/images/goodCost.png" height="15px"
                                                             width="15px"
                                                             style="position: relative;left: -2px;top: 3px"/>
                                                    <#else>
                                                        <span style="color: rgba(192,0,23,0.53);font-size: 12px">${diseType}</span>
                                                        <img src="${url}/preMedia/images/warning.png" height="15px"
                                                             width="15px"
                                                             style="position: relative;left: -2px;top: 3px"/>
                                                    </#if>
                                                </p>
                                            </div>
                                            <div class="sfq-header" style="display: none;">
                                                <p style="width: 22%;">费用信息</p>
                                            </div>
                                            <i class="fa fa-chevron-down"></i>
                                        <#else >
                                            <div class="sfq-header" style="display: flex;">
                                                <p style="width: 22%;">暂无费用信息</p>
                                            </div>
                                        </#if>
                                    </div>
                                    <ul class="submenu">
                                        <#if dipCodg??>
                                            <li>
                                                <a href="#">
                                                    <div class="sfq-header">
                                                        <p style="width: 22%;">
                                                            基准分值: ${jzfz}
                                                        </p>
                                                        <p style="width: 26%;">
                                                            级别基本系数: ${jbjbxs}
                                                        </p>
                                                        <p style="width: 25%;">
                                                            计算分值: ${ycfzFirst}
                                                        </p>
                                                        <p style="position: relative;width: 24%;">
                                                            病例类型: ${balx}
                                                        </p>
                                                    </div>
                                                </a>
                                            </li>
                                            <li>
                                                <a href="#">
                                                    <div class="sfq-header">
                                                        <p style="width: 22%;">
                                                            中医优势病例系数: ${zyysjcxs}
                                                        </p>
                                                        <p style="width: 26%;">
                                                            基层病种系数:${jcbzjcxs}
                                                        </p>
                                                        <p style="width: 25%;">
                                                            低龄病种系数: ${dlbzjcxs}
                                                        </p>
                                                        <p style="position: relative;width: 24%;">
                                                            重点学科系数: ${zdxkjcxs}
                                                        </p>
                                                    </div>
                                                </a>
                                            </li>
                                            <li>
                                                <a href="#">
                                                    <div class="sfq-header">
                                                        <p style="width: 22%;">
                                                            加成分值: ${jcfz}
                                                        </p>
                                                        <p style="width: 26%;">
                                                            总分值: ${zfz}
                                                        </p>
                                                        <p style="width: 25%;">
                                                            分值单价: ${fzdj}
                                                        </p>
                                                        <p style="position: relative;width: 24%;">
                                                            预测费用: ${ycfy}
                                                        </p>
                                                    </div>
                                                </a>
                                            </li>
                                            <li>
                                                <a href="#">
                                                    <div class="sfq-header">
                                                        <p style="width: 48%;">
                                                            <#if fycy gt 0>
                                                                <span style="color: rgb(91,174,99);font-size: 12px;">预测金额差异: ${fycy}</span>
                                                            <#else>
                                                                <span style="color: red;font-size: 12px;">预测金额差异: ${fycy}</span>
                                                            </#if>
                                                        </p>
                                                        <p style="position: relative;width: 26%;">
                                                            <#if caseTypeT == "1">
                                                                <span style="color: rgba(91,174,99,0.53);font-size: 12px">${diseType}</span>
                                                                <img src="${url}/preMedia/images/goodCost.png"
                                                                     height="15px" width="15px"
                                                                     style="position: relative;left: -2px;top: 3px"/>
                                                            <#else>
                                                                <span style="color: rgba(192,0,23,0.53);font-size: 12px">${diseType}</span>
                                                                <img src="${url}/preMedia/images/warning.png"
                                                                     height="15px" width="15px"
                                                                     style="position: relative;left: -2px;top: 3px"/>
                                                            </#if>
                                                        </p>
                                                    </div>
                                                </a>
                                            </li>
                                        </#if>
                                    </ul>
                                </li>
                                <li>
                                    <div class="link">
                                        <img src="${url}/preMedia/images/benchmark_cost.png" height="25px" width="25px"
                                             class="des-icon"/>
                                        <#if dipCodg??>
                                            <div class="sfq-header">
                                                <p style="width: 22%;">
                                                    标杆费用: ${benchmarkLevel}
                                                </p>
                                                <p style="width: 26%;">
                                                    费用差异: ${bgfycy}
                                                </p>
                                                <p style="width: 25%;">
                                                    标杆住院天数: ${inHosAvgDays}
                                                </p>
                                                <p style="width: 24%;">
                                                    住院天数差异：${zytscy}
                                                </p>
                                            </div>
                                            <div class="sfq-header" style="display: none;">
                                                <p style="width: 22%;">标杆信息</p>
                                            </div>
                                            <i class="fa fa-chevron-down"></i>
                                        <#else >
                                            <div class="sfq-header" style="display: flex;">
                                                <p style="width: 22%;">暂无标杆信息</p>
                                            </div>
                                        </#if>
                                    </div>
                                    <ul class="submenu">
                                        <#if dipCodg??>
                                            <li>
                                                <a href="#">
                                                    <div class="sfq-header">
                                                        <p style="width: 22%;">
                                                            总费用: ${inHosCost}
                                                        </p>
                                                        <p style="width: 26%;">
                                                            标杆费用: ${benchmarkLevel}
                                                        </p>
                                                        <p style="width: 25%;">
                                                            标杆费用差异: ${bgfycy}
                                                        </p>
                                                        <p style="width: 24%;height: 22px;position: relative">
                                                            <#if bgfycy gt 0>
                                                                <img src="${url}/preMedia/images/好评.png" height="20px"
                                                                     width="20px"
                                                                     style="position: absolute;left: 38%;top: 2px"/>
                                                            <#elseif inHosCost lt minFee || bgfycy lt 0>
                                                                <img src="${url}/preMedia/images/差评.png" height="18px"
                                                                     width="18px"
                                                                     style="position: absolute;left: 38%;top: 2px"/>
                                                            </#if>
                                                        </p>
                                                    </div>
                                                </a>
                                            </li>
                                            <li>
                                                <a href="#">
                                                    <div class="sfq-header">
                                                        <p style="width: 22%;">
                                                            住院天数: ${inHosDays}
                                                        </p>
                                                        <p style="width: 26%;">
                                                            标杆天数: ${inHosAvgDays}
                                                        </p>
                                                        <p style="width: 25%;">
                                                            差距: ${zytscy}
                                                        </p>
                                                        <p style="width: 24%;height: 22px;position: relative">
                                                            <#if inHosAvgDays??>
                                                                <#if zytscy gt 0>
                                                                    <img src="${url}/preMedia/images/好评.png"
                                                                         height="20px" width="20px"
                                                                         style="position: absolute;left: 38%;top: 2px"/>
                                                                <#else>
                                                                    <img src="${url}/preMedia/images/差评.png"
                                                                         height="20px" width="20px"
                                                                         style="position: absolute;left: 38%;top: 2px"/>
                                                                </#if>
                                                            </#if>
                                                        </p>
                                                    </div>
                                                </a>
                                            </li>
                                            <li>
                                                <a href="#">
                                                    <div class="sfq-header">
                                                        <p style="width: 22%;">
                                                            药品费: ${ypf}
                                                        </p>
                                                        <p style="width: 26%;">
                                                            标杆药品费: ${bgypf}
                                                        </p>
                                                        <p style="width: 25%;">
                                                            差距: ${bgypfcy}
                                                        </p>
                                                        <p style="width: 24%;height: 22px;position: relative">
                                                            <#if bgypf??>
                                                                <#if bgypfcy gt 0>
                                                                    <img src="${url}/preMedia/images/好评.png"
                                                                         height="20px" width="20px"
                                                                         style="position: absolute;left: 38%;top: 2px"/>
                                                                <#else>
                                                                    <img src="${url}/preMedia/images/差评.png"
                                                                         height="20px" width="20px"
                                                                         style="position: absolute;left: 38%;top: 2px"/>
                                                                </#if>
                                                            </#if>
                                                        </p>
                                                    </div>
                                                </a>
                                            </li>
                                            <li>
                                                <a href="#">
                                                    <div class="sfq-header">
                                                        <p style="width: 22%;">
                                                            耗材费: ${hcfycy}
                                                        </p>
                                                        <p style="width: 26%;">
                                                            标杆耗材费: ${bghcf}
                                                        </p>
                                                        <p style="width: 25%;">
                                                            差距: ${bghcfcy}
                                                        </p>
                                                        <p style="width: 24%;height: 22px;position: relative">
                                                            <#if bghcf??>
                                                                <#if bghcfcy gt 0>
                                                                    <img src="${url}/preMedia/images/好评.png"
                                                                         height="20px" width="20px"
                                                                         style="position: absolute;left: 38%;top: 2px"/>
                                                                <#else>
                                                                    <img src="${url}/preMedia/images/差评.png"
                                                                         height="20px" width="20px"
                                                                         style="position: absolute;left: 38%;top: 2px"/>
                                                                </#if>
                                                            </#if>
                                                        </p>
                                                    </div>
                                                </a>
                                            </li>
                                            <li>
                                                <a href="#">
                                                    <div class="sfq-header">
                                                        <p style="width: 22%;">
                                                            药占比: ${ybfyzb}%
                                                        </p>
                                                        <p style="width: 26%;">
                                                            标杆占比: ${ypfbgzb}
                                                        </p>
                                                        <p style="width: 25%;">
                                                            差距: ${ypfycy}%
                                                        </p>
                                                        <p style="width: 24%;height: 22px;position: relative">
                                                            <#if ypfbgzb??>
                                                                <#if ypfycy gt 0>
                                                                    <img src="${url}/preMedia/images/好评.png"
                                                                         height="20px" width="20px"
                                                                         style="position: absolute;left: 38%;top: 2px"/>
                                                                <#else>
                                                                    <img src="${url}/preMedia/images/差评.png"
                                                                         height="20px" width="20px"
                                                                         style="position: absolute;left: 38%;top: 2px"/>
                                                                </#if>
                                                            </#if>
                                                        </p>
                                                    </div>
                                                </a>
                                            </li>
                                            <li>
                                                <a href="#">
                                                    <div class="sfq-header">
                                                        <p style="width: 22%;">
                                                            耗材比: ${hcfyzb}%
                                                        </p>
                                                        <p style="width: 26%;">
                                                            标杆占比: ${hcfybgzb}
                                                        </p>
                                                        <p style="width: 25%;">
                                                            差距: ${hcfycy}%
                                                        </p>
                                                        <p style="width: 24%;height: 22px;position: relative">
                                                            <#if hcfybgzb??>
                                                                <#if hcfycy gt 0>
                                                                    <img src="${url}/preMedia/images/好评.png"
                                                                         height="20px" width="20px"
                                                                         style="position: absolute;left: 38%;top: 2px"/>
                                                                <#else>
                                                                    <img src="${url}/preMedia/images/差评.png"
                                                                         height="20px" width="20px"
                                                                         style="position: absolute;left: 38%;top: 2px"/>
                                                                </#if>
                                                            </#if>
                                                        </p>
                                                    </div>
                                                </a>
                                            </li>
                                        </#if>
                                    </ul>
                                </li>
                            </ul>
                        </div>
                    </div>

                    <!--2、AI分组推荐、AI病例质控、核心病种信息、AI清单质控-->
                    <div class="top_tabs">
                        <button class="tab active" onclick="openTab(event, 'group-recommend-tab')"><img
                                    src="${url}/preMedia/images/group-recommend.png" class="tab_icon"/>
                            <span>AI分组推荐<#if zntj?? && (zntj ?size > 0)>${zntj ?size}</#if></button>
                        </span>

                        <button class="tab" onclick="openTab(event, 'med-vali-tab')"><img
                                    src="${url}/preMedia/images/med-vali.png" class="tab_icon"/>
                            <span>AI病例质控
                                <#assign med_val_size= 0>
                                <#if logicalCheckList?? && (logicalCheckList ?size > 0)>
                                    <#assign med_val_size= med_val_size+logicalCheckList ?size>
                                </#if>
                                <#if integrityCheckList?? && (integrityCheckList ?size > 0)>
                                    <#assign med_val_size= med_val_size+integrityCheckList ?size>
                                </#if>
                                ${(med_val_size)!}
                            </span>
                        </button>

                        <button class="tab" onclick="openTab(event, 'med-dis-core-tab')"><img
                                    src="${url}/preMedia/images/disCore.png" class="tab_icon"/>
                            <span>核心病种信息<#if hxbz?? && (hxbz ?size > 0)>${hxbz ?size}</#if>
                            </span>
                        </button>
                        <#if isWeb?? && isWeb == '1'>
                        <#else >
                            <button class="tab" onclick="openTab(event, 'setl-vali-tab')">
                                <img src="${url}/preMedia/images/setl-vali.png"
                                     class="tab_icon"/>
                                <span>AI清单质控
                                    <#if qualityInfo??>
                                        <#assign setl_val_size= 0>
                                        <#list qualityInfo as qiItem>
                                            <#if qiItem.data?? && (qiItem.data?size>0)>
                                                <#assign setl_val_size=setl_val_size+qiItem.data?size>
                                            </#if>
                                        </#list>
                                        ${(setl_val_size)!}
                                    </#if>
                                </span>
                            </button>
                        </#if>
                    </div>

                    <!-- 1、AI分组推荐 -->
                    <div id="group-recommend-tab" class="tabcontent">
                        <div class="group-recommend">
<#--                            <div class="title">-->
<#--                                <img src="${url}/preMedia/images/group-recommend.png" height="35px" width="35px"/>-->
<#--                                <span>AI分组推荐</span>-->
<#--                            </div>-->
<#--                            <div class="divider"></div>-->
                            <div class="group-recommend-content">
                                <ul id="${dipCodg???string('accordion_2','ac_2')}" class="accordion"
                                    style="margin-top: 1rem;">
                                    <li>
<#--                                        <div class="link">-->
<#--                                            <img src="${url}/preMedia/images/灯泡提示.png" height="25px" width="25px"-->
<#--                                                 class="des-icon"/>-->
<#--                                            <#if zntj?? && (zntj ?size > 0)>-->
<#--                                                <div class="sfq-header">-->
<#--                                                    <#list zntj as dip>-->
<#--                                                        <#if dip_index=0>-->
<#--                                                            <p style="width: 22%;">-->
<#--                                                                ${dip.dipCodg}-->
<#--                                                            </p>-->
<#--                                                            <p style="width: 26%;">-->
<#--                                                                ${dip.dipName}-->
<#--                                                            </p>-->
<#--                                                            <p style="width: 25%;">-->
<#--                                                                病组费用: ${dip.lastYearLevelStandardCost}-->
<#--                                                            </p>-->
<#--                                                            <p style="position: relative;width: 24%;height: 16px;color: #C00017;">-->
<#--                                                                <#list 2..0 as t>-->
<#--                                                                    <img src="${url}/preMedia/images/五星 (1).png"-->
<#--                                                                         height="16px" width="16px"/>-->
<#--                                                                </#list>-->
<#--                                                            </p>-->
<#--                                                        </#if>-->
<#--                                                    </#list>-->
<#--                                                </div>-->
<#--                                                <div class="sfq-header" style="display: none">-->
<#--                                                    <p style="width: 22%;">病组信息</p>-->
<#--                                                </div>-->
<#--                                                <i class="fa fa-chevron-down"></i>-->
<#--                                            <#else>-->
<#--                                                <div class="sfq-header" style="display: flex">-->
<#--                                                    <p style="width: 22%;">暂无病组信息</p>-->
<#--                                                </div>-->
<#--                                            </#if>-->
<#--                                        </div>-->

                                        <ul class="submenu" style="display: block;"
                                            id="${(zntj?? && (zntj ?size > 0))?string('secondaryMenu','')}">
                                            <#if zntj?? && (zntj ?size > 0)>
                                                <#list zntj as dip>
                                                    <li class="open">
                                                        <#if dipCodg?? && dipCodg == dip.dipCodg>
                                                        <a href="#" class="a-hover secondary-menu">
                                                            <#else >
                                                            <a href="#" class="secondary-menu">
                                                                </#if>

                                                                <div class="sfq-header">
                                                                    <p style="width: 22%;">
                                                                        ${dip.dipCodg}
                                                                    </p>
                                                                    <p style="width: 26%;">
                                                                        ${dip.dipName}
                                                                    </p>
                                                                    <p style="width: 25%;">
                                                                        病组费用: ${dip.lastYearLevelStandardCost}
                                                                    </p>
                                                                    <p style="width: 24%;">
                                                                        <#if dip_index=0>
                                                                            <#list 2..0 as t>
                                                                                <img src="${url}/preMedia/images/五星 (1).png"
                                                                                     height="16px" width="16px"/>
                                                                            </#list>
                                                                        <#elseif dip_index=1>
                                                                            <#list 1..0 as t>
                                                                                <img src="${url}/preMedia/images/五星 (1).png"
                                                                                     height="16px" width="16px"/>
                                                                            </#list>
                                                                        <#elseif dip_index=2>
                                                                            <img src="${url}/preMedia/images/五星 (1).png"
                                                                                 height="16px" width="16px"/>
                                                                        <#else>
                                                                        </#if>
                                                                    </p>
                                                                </div>
                                                                <#if dipCodg?? && dipCodg == dip.dipCodg>
                                                            </a>
                                                            <#else >
                                                        </a>
                                                        </#if>

                                                        <ul class="secondary-submenu">
                                                            <li>
                                                                <a href="#">
                                                                    <div class="sfq-header">
                                                                        <p style="width: 22%;">
                                                                            基准分值: ${dip.jzfz}
                                                                        </p>
                                                                        <p style="width: 26%;">
                                                                            级别基本系数: ${dip.jbjbxs}
                                                                        </p>
                                                                        <p style="width: 25%;">
                                                                            计算分值: ${dip.ycfzFirst}
                                                                        </p>
                                                                        <p style="position: relative;width: 24%;">
                                                                            病例类型: ${dip.balx}
                                                                        </p>
                                                                    </div>
                                                                </a>
                                                            </li>
                                                            <li>
                                                                <a href="#">
                                                                    <div class="sfq-header">
                                                                        <p style="width: 22%;">
                                                                            中医优势病例系数: ${dip.zyysjcxs}
                                                                        </p>
                                                                        <p style="width: 26%;">
                                                                            基层病种系数:${dip.jcbzjcxs}
                                                                        </p>
                                                                        <p style="width: 25%;">
                                                                            低龄病种系数: ${dip.dlbzjcxs}
                                                                        </p>
                                                                        <p style="position: relative;width: 24%;">
                                                                            重点学科系数: ${dip.zdxkjcxs}
                                                                        </p>
                                                                    </div>
                                                                </a>
                                                            </li>
                                                            <li>
                                                                <a href="#">
                                                                    <div class="sfq-header">
                                                                        <p style="width: 22%;">
                                                                            加成分值: ${dip.jcfz}
                                                                        </p>
                                                                        <p style="width: 26%;">
                                                                            总分值: ${dip.zfz}
                                                                        </p>
                                                                        <p style="width: 25%;">
                                                                            分值单价: ${dip.fzdj}
                                                                        </p>
                                                                        <p style="position: relative;width: 24%;">
                                                                            预测费用: ${dip.ycfy}
                                                                        </p>
                                                                    </div>
                                                                </a>
                                                            </li>
                                                            <li>
                                                                <a href="#">
                                                                    <div class="sfq-header">
                                                                        <p style="width: 48%;">
                                                                            诊断编码: ${dip.diagnoseCode}
                                                                        </p>
                                                                        <p style="width: 48%;">
                                                                            <#if dip.fycy gt 0>
                                                                                <span style="color: rgb(0 159 7);font-size: 12px;">预测金额差异: ${dip.fycy}</span>
                                                                            <#else>
                                                                                <span style="color: red;font-size: 12px;">预测金额差异: ${dip.fycy}</span>
                                                                            </#if>
                                                                        </p>
                                                                    </div>
                                                                </a>
                                                            </li>
                                                        </ul>
                                                    </li>
                                                </#list>
                                            </#if>
                                        </ul>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <!--2、AI病例质控-->
                    <div id="med-vali-tab" class="tabcontent" style="display: none">
                        <div class="pre-pay">
<#--                            <div class="title">-->
<#--                                <img src="${url}/preMedia/images/med-vali.png" height="35px" width="35px"/>-->
<#--                                <span>AI病例质控</span>-->
<#--                            </div>-->
<#--                            <div class="divider"></div>-->
                            <div class="record-content">
                                <ul id="accordion_3" class="accordion" style="margin-top: 1rem;">
                                    <li>
<#--                                        <div class="link">-->
<#--                                            <img src="${url}/preMedia/images/积分.png" height="25px" width="25px"-->
<#--                                                 class="des-icon"/>-->
<#--                                            <div class="sfq-header">-->
<#--                                                <p style="width: 22%;">-->
<#--                                                    病例得分: ${medicalRecordScore}-->
<#--                                                </p>-->
<#--                                                <p style="width: 26%;">-->
<#--                                                    逻辑性错误: ${countLogicalCheck}-->
<#--                                                </p>-->
<#--                                                <p style="width: 25%;">-->
<#--                                                    完整性错误: ${countIntegrity}-->
<#--                                                </p>-->
<#--                                            </div>-->
<#--                                            <div class="sfq-header" style="display: none">-->
<#--                                                <p style="width: 22%;">质控信息</p>-->
<#--                                            </div>-->
<#--                                            <i class="fa fa-chevron-down"></i>-->
<#--                                        </div>-->
                                        <ul class="submenu" style="display: block;">
                                            <div class="sfq-header">
                                                <li >
                                                    <a href="#">
                                                        <p style="width: 22%;">
                                                            病例得分: ${medicalRecordScore}
                                                        </p>
                                                        <p style="width: 26%;">
                                                            逻辑性错误: ${countLogicalCheck}
                                                        </p>
                                                        <p style="width: 25%;">
                                                            完整性错误: ${countIntegrity}
                                                        </p>
                                                    </a>
                                                </li>
                                                <li><a href="#"><p>逻辑性问题:</p></a></li>
                                                <#if logicalCheckList?? && (logicalCheckList?size > 0)>
                                                    <#list logicalCheckList as item>
                                                        <li class="quality-control-item">
                                                            <a href="#">
                                                                <p>${item_index+1}、${item}</p>
                                                            </a>
                                                        </li>
                                                    </#list>
                                                </#if>
                                                <li><a href="#"><p>完整性问题:</p></a></li>
                                                <#if integrityCheckList?? && (integrityCheckList?size > 0)>
                                                    <#list integrityCheckList as data>
                                                        <li class="quality-control-item">
                                                            <a href="#">
                                                                <p>${data_index+1}、${data.content}</p>
                                                                <p style="float: right;color: rgba(255, 0, 0, 0.93);">
                                                                    扣${data.refer_sco}分</p>
                                                            </a>
                                                        </li>
                                                    </#list>
                                                </#if>
                                            </div>
                                        </ul>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <!-- 3、相关核心病种 -->
                    <div id="med-dis-core-tab" class="tabcontent" style="display: none">
                        <div class="core-group">
<#--                            <div class="title">-->
<#--                                <img src="${url}/preMedia/images/disCore.png" height="35px" width="35px"/>-->
<#--                                <span>核心病种信息</span>-->
<#--                            </div>-->
<#--                            <div class="divider"></div>-->
                            <div class="group-recommend-content">
                                <ul id="accordion_7" class="accordion" style="margin-top: 1rem;">
                                    <li>
<#--                                        <div class="link">-->
<#--                                            <img src="${url}/preMedia/images/灯泡提示.png" height="25px" width="25px"-->
<#--                                                 class="des-icon"/>-->
<#--                                            <#if hxbz?? && (hxbz ?size > 0)>-->
<#--                                                <div class="sfq-header">-->
<#--                                                    <#list hxbz as dip>-->
<#--                                                        <#if dip_index=0>-->
<#--                                                            <p style="width: 22%;">-->
<#--                                                                病组编码: ${dip.coreDipCode}-->
<#--                                                            </p>-->
<#--                                                            <p style="width: 26%;">-->
<#--                                                                病组名称: ${dip.coreDipName}-->
<#--                                                            </p>-->
<#--                                                            <p style="width: 25%;">-->
<#--                                                                病组费用: ${dip.coreDipAvgCostLevel}-->
<#--                                                            </p>-->
<#--                                                        </#if>-->
<#--                                                    </#list>-->
<#--                                                </div>-->
<#--                                                <div class="sfq-header" style="display: none">-->
<#--                                                    <p style="width: 22%;">病组信息</p>-->
<#--                                                </div>-->
<#--                                                <i class="fa fa-chevron-down"></i>-->
<#--                                            <#else>-->
<#--                                                <div class="sfq-header" style="display: flex">-->
<#--                                                    <p style="width: 22%;">暂无病组信息</p>-->
<#--                                                </div>-->
<#--                                            </#if>-->
<#--                                        </div>-->
                                        <ul class="submenu" style="display: block;"
                                            id="${(hxbz?? && (hxbz ?size > 0))?string('secondaryMenu1','')}">
                                            <#if hxbz?? && (hxbz ?size > 0)>
                                                <#list hxbz as dip>
                                                    <li class="open">

                                                        <a href="#" class="secondary-menu">
                                                            <div class="sfq-header">
                                                                <p style="width: 30%;">
                                                                    病组编码: ${dip.coreDipCode}
                                                                </p>
                                                                <p style="width: 40%;">
                                                                    病组名称: ${dip.coreDipName}
                                                                </p>
                                                                <p style="width: 20%;">
                                                                    标杆费用: ${dip.coreDipAvgCostLevel}
                                                                </p>
                                                            </div>
                                                        </a>

                                                        <ul class="secondary-submenu">
                                                            <li>
                                                                <a href="#">
                                                                    <div>
                                                                        <p style="width: 40%;">
                                                                            DIP编码: ${dip.coreDipCode}
                                                                        </p>
                                                                        <p style="width: 60%;">
                                                                            DIP名称: ${dip.coreDipName}
                                                                        </p>
                                                                    </div>
                                                                </a>
                                                            </li>
                                                            <li>
                                                                <a href="#">
                                                                    <div class="sfq-header">
                                                                        <p style="width: 40%;">
                                                                            亚目编码: ${dip.coreDiagnoseCode}
                                                                        </p>
                                                                        <p style="width: 60%;">
                                                                            亚目名称: ${dip.coreDiagnoseName}
                                                                        </p>
                                                                    </div>
                                                                </a>
                                                            </li>
                                                            <#if dip.coreOperationCode??>
                                                                <li>
                                                                    <a href="#">
                                                                        <div class="sfq-header">
                                                                            <#if dip.coreOperationCode??>
                                                                                <p style="width: 40%;">
                                                                                    操作编码: ${dip.coreOperationCode}
                                                                                </p>
                                                                                <p style="position: relative;width: 60%;">
                                                                                    操作名称: ${dip.coreOperationName}
                                                                                </p>
                                                                            </#if>
                                                                        </div>
                                                                    </a>
                                                                </li>
                                                            </#if>
                                                            <li>
                                                                <a href="#">
                                                                    <div class="sfq-header">
                                                                        <p style="width: 30%;">
                                                                            辅助目录-年龄段: ${dip.coreAuxiliaryAge}
                                                                        </p>
                                                                        <p style="width: 35%;">
                                                                            辅助目录-疾病严重程度: ${dip.coreAuxiliaryIllness}
                                                                        </p>
                                                                        <p style="width: 30%;">
                                                                            辅助目录-肿瘤严重程度: ${dip.coreAuxiliaryTumour}
                                                                        </p>
                                                                    </div>
                                                                </a>
                                                            </li>
                                                            <li>
                                                                <a href="#">
                                                                    <div class="sfq-header">
                                                                        <p style="width: 25%;">
                                                                            基础分组: ${dip.coreScore}
                                                                        </p>
                                                                        <p style="position: relative;width: 24%;">
                                                                            标杆费用: ${dip.coreDipAvgCostLevel}
                                                                        </p>
                                                                    </div>
                                                                </a>
                                                            </li>
                                                        </ul>
                                                    </li>
                                                </#list>
                                            </#if>
                                        </ul>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <!-- AI病例质控 -->

                    <#if isWeb?? && isWeb == '1'>
                    <#else>
                        <!-- 4、清单质控分析 -->
                        <div id="setl-vali-tab" class="tabcontent" style="display: none">
                            <div class="pre-pay">
<#--                                <div class="title">-->
<#--                                    <img src="${url}/preMedia/images/质控模块.png" height="35px" width="35px"/>-->
<#--                                    <span>清单质控分析</span>-->
<#--                                </div>-->
<#--                                <div class="divider"></div>-->
                                <div class="record-content">
                                    <ul id="accordion_5" class="accordion" style="margin-top: 1rem;">
                                        <li>
<#--                                            <div class="link">-->
<#--                                                <img src="${url}/preMedia/images/积分.png" height="25px" width="25px"-->
<#--                                                     class="des-icon"/>-->
<#--                                                <div class="sfq-header">-->
<#--                                                    <#list qualityInfo as qiItem>-->
<#--                                                        <p style="width: 22%;">-->
<#--                                                            ${qiItem.name}: ${qiItem.errorNum}-->
<#--                                                        </p>-->
<#--                                                    </#list>-->
<#--                                                </div>-->
<#--                                                <div class="sfq-header" style="display: none">-->
<#--                                                    <p style="width: 22%;">清单质控信息</p>-->
<#--                                                </div>-->
<#--                                                <i class="fa fa-chevron-down"></i>-->
<#--                                            </div>-->
                                            <ul class="submenu" style="display: block;">
                                                <!-- 清单质控 -->
                                                <div class="sl-content">
                                                    <ul id="tabs">
                                                        <#list qualityInfo as qiItem>
                                                            <li><a href="#"
                                                                   title="tab${qiItem.index}">${qiItem.name}</a></li>
                                                        </#list>
                                                    </ul>
                                                    <div id="content">
                                                        <#list qualityInfo as qiItem>
                                                            <span id="tab${qiItem.index}"
                                                                  class="sl-content-item ${(qiItem.index == 1) ? string('sl-content-padding1', 'sl-content-padding2')}"
                                                                  style="overflow: ${(qiItem.index == 1) ? string('hidden', 'overflow')}">
                                    <#if qiItem.index == 1>

                                        <!-- 嵌套基本质控信息 -->
                                        <ul id="tabs2">
                                            <#list qiItem.data as qiChildItem>
                                                <li><a href="#"
                                                       title="tabTwo${qiChildItem.index}">${qiChildItem.name}</a></li>
                                            </#list>
                                        </ul>
                                        <div id="content2">
                                            <#if qiItem.data?size == 0>
                                                <p class="sl-qi-complete">已完成${qiItem.name}</p>
                                            </#if>
                                            <#list qiItem.data as qiChildItem>
                                                <span id="tabTwo${qiChildItem.index}" class="sl-content-item"
                                                      style="overflow: auto">
                                                        <#list qiChildItem.data?keys as key>
                                                            <#list qiChildItem.data[key] as obj>
                                                                <tr>
                                                                    <td>
                                                                        <p>${obj_index+1 + "."} ${obj.error}</p>
                                                                    </td>
                                                                </tr>
                                                            </#list>
                                                        </#list>
                                                    </span>
                                            </#list>
                                            </div>

                                    <#else>
                                        <#if qiItem.data?size lte 0>
                                            <p class="sl-qi-complete">已完成${qiItem.name}</p>
                                            <#else>
                                            <#list qiItem.data?keys as key>
                                                <div class="sl-qi-item-wrap">
                                                <p class="sl-qi-item-wrap-title">${key}</p>
                                                <#list qiItem.data[key] as obj>
                                                    <tr>
                                                        <td>
                                                            <p>${obj_index+1 + "."} ${obj.error}</p>
                                                        </td>
                                                    </tr>
                                                </#list>
                                            </div>
                                            </#list>
                                        </#if>
                                    </#if>
                                </span>
                                                        </#list>
                                                    </div>
                                                </div>
                                            </ul>

                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </#if>
                </div>
            </div>
        </div>
    </div>
    <script>
        window.onload = function () {
            // 左边padding宽度
            var left_padding = parseFloat(window.getComputedStyle(document.getElementById("pre-pay-content-id"), null).getPropertyValue("padding-left"))
            // 各个线宽度
            var range_min = document.getElementById("range-min")
            var range_min_normal = document.getElementById("range-min-normal")
            var range_normal_max = document.getElementById("range-normal-max")
            var range_max = document.getElementById("range-max")

            var range_min_offset_left = range_min.offsetLeft
            var range_min_normal_offset_left = range_min_normal.offsetLeft
            var range_normal_max_offset_left = range_normal_max.offsetLeft
            var range_max_offset_left = range_max.offsetLeft

            var rate = 0
            <#if rate ??>
            rate = ${rate?string("0.######")}
            </#if>

            var minFee = 0
            <#if minFee ??>
            minFee = ${minFee?string("0.####")}
            </#if>

            var benchmarkLevel = 0
            <#if benchmarkLevel ??>
            benchmarkLevel = ${benchmarkLevel?string("0.####")}
            </#if>

            var highFee = 0
            <#if highFee ??>
            highFee = ${highFee?string("0.####")}
            </#if>

            var inHosCost = 0
            <#if inHosCost ??>
            inHosCost = ${inHosCost?string("0.####")}
            </#if>

            var uplmtMag = 0
            <#if uplmtMag ??>
            uplmtMag = ${uplmtMag}
            </#if>

            let maxLimit = benchmarkLevel / highFee
            let minLimit = minFee / highFee

            let normalRightRate = (inHosCost - benchmarkLevel) / (highFee - benchmarkLevel)
            let normalLeftRate = (inHosCost - minFee) / (benchmarkLevel - minFee)
            let minNormalRate = (inHosCost / minFee)

            let range_normal_to_max_offset_left = range_max_offset_left - range_normal_max_offset_left
            let range_min_to_normal_offset_left = range_normal_max_offset_left - range_min_normal_offset_left

            var tooltip_left = 0
            if (rate >= 1.1) {
                tooltip_left = 1.1 * range_max_offset_left - left_padding - 27
            } else if (rate >= maxLimit && rate < 1.1) {
                tooltip_left = normalRightRate * range_normal_to_max_offset_left + range_normal_max_offset_left - left_padding - 27
            } else if (rate >= minLimit && rate < maxLimit) {
                tooltip_left = normalLeftRate * range_min_to_normal_offset_left + range_min_normal_offset_left - left_padding - 27
            } else {
                tooltip_left = minNormalRate * range_min_normal_offset_left - left_padding - 26
            }
            tooltip.style.left = tooltip_left + "px"

            var curTabName = 'tabTwo1'

            $("#content span").hide(); // Initially hide all content
            $("#tabs a:first").addClass("active")
            $('#tab1').fadeIn();

            $('#tabs a').click(function (e) {
                e.preventDefault();
                $("#content span").hide(); //Hide all content
                $("#tabs a").removeClass("active")
                $(this).addClass("active")
                $('#' + $(this).attr('title')).fadeIn();
                if ($(this).attr('title') === "tab1") {
                    $('#' + curTabName).fadeIn();
                }
            });


            $("#content2 span").hide(); // Initially hide all content
            $("#tabs2 a:first").addClass("active")
            $('#tabTwo1').fadeIn();

            $('#tabs2 a').click(function (e) {
                e.preventDefault();
                $("#content2 span").hide(); //Hide all content
                $("#tabs2 a").removeClass("active")
                $(this).addClass("active")
                $('#' + $(this).attr('title')).fadeIn();
                curTabName = $(this).attr('title')
            });
        }

        /**
         * tab点击事件
         * @param evt
         * @param tabName
         */
        function openTab(evt, tabName) {
            var i, tabcontent, tablinks;
            tabcontent = document.getElementsByClassName("tabcontent");
            for (i = 0; i < tabcontent.length; i++) {
                tabcontent[i].style.display = "none";
            }
            tablinks = document.getElementsByClassName("tab");
            for (i = 0; i < tablinks.length; i++) {
                tablinks[i].className = tablinks[i].className.replace(" active", "");
            }
            document.getElementById(tabName).style.display = "block";
            evt.currentTarget.className += " active";
        }
    </script>
<#else>
    <div style="position: relative" class="container">
        <div style="width: 30%;height: 30%;position: absolute;top:0;left: 0;right: 0;bottom: 0;margin: auto">
            <div style="float: top;text-align: center">
                <img src="${url}/preMedia/images/错误.png" height="100px" width="100px"/>
                </br>
            </div>
            <div style="float: bottom;text-align: center">
                <span>${errorMsg}</span>
                <#--                <span>预分组失败，请重新核对数据</span>-->
            </div>
        </div>
    </div>
</#if>
</body>
</html>
