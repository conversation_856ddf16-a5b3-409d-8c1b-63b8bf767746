<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title th:text="${pageTitle}">医院智能审核管理系统</title>
    <style>
        /* 基础样式重置 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: "Microsoft YaHei", "SimSun", Arial, sans-serif;
            font-size: 12px;
            background-color: #f7fafc;
            color: #333;
        }

        /* 顶部标题栏 */
        .header {
            background-color: #4a5568;
            color: white;
            text-align: center;
            padding: 8px 0;
            font-size: 16px;
            font-weight: bold;
        }

        .header h1 {
            font-size: 16px;
            font-weight: normal;
        }

        /* 主容器 */
        .main-container {
            padding: 10px;
            max-width: 1200px;
            margin: 0 auto;
        }

        /* 用户信息区域 */
        .user-info-section {
            background-color: white;
            border: 1px solid #d1d5db;
            margin-bottom: 10px;
            padding: 8px;
        }

        .user-info-table {
            width: 100%;
            border-collapse: collapse;
        }

        .user-info-table td {
            padding: 4px 8px;
            font-size: 12px;
            white-space: nowrap;
        }

        .user-info-table strong {
            font-weight: normal;
            color: #333;
        }

        /* 内容包装器 */
        .content-wrapper {
            display: flex;
            gap: 10px;
        }

        /* 左侧内容区域 */
        .left-content {
            flex: 1;
            background-color: white;
        }

        /* 表格区域 */
        .table-section {
            border: 1px solid #d1d5db;
            margin-bottom: 10px;
        }

        .table-header {
            background-color: #e2e8f0;
            padding: 6px 10px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid #d1d5db;
        }

        .table-title {
            font-weight: bold;
            color: #2d3748;
        }

        .table-controls {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .table-controls label {
            font-size: 12px;
            cursor: pointer;
        }

        .table-controls input[type="radio"] {
            margin-right: 4px;
        }

        .refresh-btn {
            background-color: #4299e1;
            color: white;
            border: none;
            padding: 4px 12px;
            font-size: 12px;
            cursor: pointer;
            border-radius: 2px;
        }

        /* 滚动容器 */
        .table-scroll-container {
            max-height: 200px;
            overflow-y: auto;
            border: 1px solid #d1d5db;
        }

        .detail-section-scroll {
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid #d1d5db;
        }

        .all-tables-scroll-container {
            max-height: 100%;
            overflow-y: auto;
            border: none;
        }

        /* 数据表格 */
        .data-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 12px;
        }

        .data-table th,
        .data-table td {
            border: 1px solid #d1d5db;
            padding: 6px 8px;
            text-align: left;
        }

        .data-table th {
            background-color: #f7fafc;
            font-weight: normal;
            color: #4a5568;
            position: sticky;
            top: 0;
            z-index: 1;
        }

        .data-table tbody tr:nth-child(even) {
            background-color: #f9f9f9;
        }

        .data-table .selected-row {
            background-color: #e6f3ff;
        }

        .data-table input[type="radio"],
        .data-table input[type="checkbox"] {
            margin: 0;
            transform: scale(0.8);
        }

        /* 详细记录区域 */
        .detail-section {
            border: 1px solid #d1d5db;
        }

        .detail-header {
            background-color: #e2e8f0;
            padding: 6px 10px;
            font-weight: bold;
            color: #2d3748;
            border-bottom: 1px solid #d1d5db;
        }

        .detail-table-wrapper {
            margin-bottom: 20px;
        }

        .detail-table-title {
            background-color: #f1f5f9;
            padding: 4px 8px;
            font-size: 11px;
            color: #4a5568;
            border-bottom: 1px solid #d1d5db;
        }

        .detail-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 11px;
            min-width: 800px; /* 确保表格有足够宽度 */
        }

        .detail-table th,
        .detail-table td {
            border: 1px solid #d1d5db;
            padding: 4px 6px;
            text-align: center;
            white-space: nowrap;
        }

        .detail-table th {
            background-color: #f7fafc;
            font-weight: normal;
            color: #4a5568;
            position: sticky;
            top: 0;
            z-index: 1;
        }

        /* 右侧面板 */
        .right-panel {
            width: 280px;
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        /* 注意事项区域 */
        .notice-section {
            background-color: white;
            border: 1px solid #d1d5db;
        }

        .notice-header {
            background-color: #fed7d7;
            color: #c53030;
            padding: 6px 10px;
            font-weight: bold;
            border-bottom: 1px solid #d1d5db;
        }

        .notice-content {
            padding: 10px;
            font-size: 12px;
            line-height: 1.4;
            max-height: 200px;
            overflow-y: auto;
            overflow-x: hidden;
        }

        /* 美化滚动条样式 */
        .notice-content::-webkit-scrollbar {
            width: 6px;
        }

        .notice-content::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 3px;
        }

        .notice-content::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 3px;
        }

        .notice-content::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }

        .notice-content p {
            margin-bottom: 8px;
        }

        .notice-content p:last-child {
            margin-bottom: 0;
        }

        .notice-content strong {
            color: #c53030;
        }

        /* 按钮区域 */
        .button-section {
            background-color: white;
            border: 1px solid #d1d5db;
            padding: 10px;
        }

        .operation-selection {
            display: flex;
            flex-direction: column;
            margin-bottom: 10px;
            gap: 10px;
        }

        .radio-group {
            display: flex;
            justify-content: space-between;
            gap: 10px;
        }

        .operation-option {
            display: flex;
            align-items: center;
            font-size: 12px;
            cursor: pointer;
            flex: 1;
        }

        .operation-option input[type="radio"] {
            margin-right: 6px;
            transform: scale(0.9);
        }

        .text-area-container {
            margin-top: 10px;
        }

        .comment-textarea {
            width: 100%;
            height: 80px;
            padding: 8px;
            border: 1px solid #d1d5db;
            border-radius: 3px;
            resize: none;
            font-family: inherit;
            font-size: 12px;
        }

        /* 移除旧的按钮样式，保留注释以便于理解修改 */

        /* 底部按钮 */
        .bottom-buttons {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        /* 按钮样式 */
        .btn {
            padding: 8px 16px;
            border: none;
            font-size: 12px;
            cursor: pointer;
            border-radius: 3px;
            font-family: inherit;
        }

        .btn-blue {
            background-color: #4299e1;
            color: white;
        }

        .btn-gray {
            background-color: #a0aec0;
            color: white;
        }

        .btn-green {
            background-color: #48bb78;
            color: white;
        }

        .btn-orange {
            background-color: #ed8936;
            color: white;
        }

        .btn-yellow {
            background-color: #ecc94b;
            color: #2d3748;
        }

        .btn:hover {
            opacity: 0.9;
        }

        /* 底部警告信息 */
        .footer-warning {
            margin-top: 10px;
            padding: 8px;
            font-size: 11px;
            text-align: center;
        }

        /* 响应式设计 */
        @media (max-width: 1024px) {
            .content-wrapper {
                flex-direction: column;
            }

            .right-panel {
                width: 100%;
            }

            .user-info-table {
                font-size: 11px;
            }

            .data-table,
            .detail-table {
                font-size: 10px;
            }
        }
        /* 滚动条样式 */
        .table-scroll-container::-webkit-scrollbar,
        .detail-section-scroll::-webkit-scrollbar,
        .all-tables-scroll-container::-webkit-scrollbar {
            width: 8px;
        }

        .table-scroll-container::-webkit-scrollbar-track,
        .detail-section-scroll::-webkit-scrollbar-track,
        .all-tables-scroll-container::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 4px;
        }

        .table-scroll-container::-webkit-scrollbar-thumb,
        .detail-section-scroll::-webkit-scrollbar-thumb,
        .all-tables-scroll-container::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 4px;
        }

        .table-scroll-container::-webkit-scrollbar-thumb:hover,
        .detail-section-scroll::-webkit-scrollbar-thumb:hover,
        .all-tables-scroll-container::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }

        /* 确保表格头部在滚动时保持可见 */
        .table-scroll-container,
        .detail-section-scroll,
        .all-tables-scroll-container {
            position: relative;
        }
    </style>
</head>

<body>
<!-- 顶部标题栏 -->
<header class="header">
    <h1 th:text="${systemTitle}">医院智能审核管理系统</h1>
</header>

<!-- 主要内容区域 -->
<div class="main-container">
    <!-- 用户信息区域 -->
    <div class="user-info-section">
        <table class="user-info-table">
            <tr>
                <td><strong>门诊号/住院号：</strong><span th:text="${userInfo.hospitalNumber}">32012829-01</span></td>
                <td><strong>性别：</strong><span th:text="${userInfo.gender}">女</span></td>
                <td><strong>入院诊断：</strong><span th:text="${userInfo.diagnosis}">霍乱,由于01霍乱群</span></td>
                <td><strong>入院日期：</strong><span th:text="${userInfo.admissionDate}">2023-02-20 16:42</span></td>
                <td><strong>人员类别：</strong><span th:text="${userInfo.personCategory}">退休</span></td>
            </tr>
            <tr>
                <td><strong>参保人姓名：</strong><span th:text="${userInfo.insuredName}">赵**</span></td>
                <td><strong>年龄：</strong><span th:text="${userInfo.age}">24</span></td>
                <td><strong>险种类型：</strong><span th:text="${userInfo.insuranceType}">职工基本医疗保险</span></td>
                <td><strong>入院天数：</strong><span th:text="${userInfo.hospitalizationDays}">23</span>天</td>
                <td><strong>就医方式：</strong><span th:text="${userInfo.treatmentMethod}">普通住院</span></td>
            </tr>
        </table>
    </div>

    <!-- 主要内容区域 -->
    <div class="content-wrapper">
        <!-- 左侧表格区域 -->
        <div class="left-content">
            <!-- 违规信息表格 -->
            <div class="table-section">
                <div class="table-header">
                    <span class="table-title" th:text="${violationTable.title}">违规信息</span>
                    <div class="table-controls">
                        <label><input type="radio" name="filter" checked> 全部</label>
                        <label><input type="radio" name="filter"> 已办</label>
                        <label><input type="radio" name="filter"> 待办</label>
                        <button class="refresh-btn">刷新</button>
                    </div>
                </div>
                <div class="table-scroll-container">
                    <table class="data-table">
                        <thead>
                        <tr>
                            <th>序号</th>
                            <th>是否处理</th>
                            <th>是否自费</th>
                            <th>审核渠道</th>
                            <th>违规等级</th>
                            <th>项目编码</th>
                            <th>项目名称</th>
                            <th>项目时间</th>
                        </tr>
                        </thead>
                        <tbody>
                        <tr th:each="violation, iterStat : ${violationTable.violations}"
                            th:class="${violation.selected} ? 'selected-row' : ''">
                            <td th:text="${iterStat.count}">1</td>
                            <td th:text="${violation.processed} ? '✓' : ''"></td>
                            <td><input type="checkbox" th:name="'selfpay' + ${iterStat.count}"
                                       th:checked="${violation.selfPay}"></td>
                            <td th:text="${violation.auditChannel}">医疗、药物</td>
                            <td th:text="${violation.violationLevel}">实事求是</td>
                            <td th:text="${violation.itemCode}">y0000000924</td>
                            <td th:text="${violation.itemName}">舒宁堂主要产品</td>
                            <td th:text="${violation.itemTime}">2024-03-07 21:24</td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- 详细记录表格 -->
            <div class="detail-section">
                <div class="detail-header" th:text="${detailSection.title}">相关记录</div>
                <div class="detail-section-scroll">
                    <div class="all-tables-scroll-container">
                        <!-- 动态渲染多个详细表格 -->
                        <div class="detail-table-wrapper" th:each="detailTable : ${detailSection.detailTables}">
                            <div class="detail-table-title" th:text="${detailTable.title}">编号-P01 配置信息</div>
                            <table class="detail-table">
                                <thead>
                                <tr>
                                    <th th:each="header : ${detailTable.headers}" th:text="${header}">序号</th>
                                </tr>
                                </thead>
                                <tbody>
                                <tr th:each="cell : ${detailTable.rows[0].cells}">
                                    <td th:text="${cell.projectDate}">2024-01-01</td>
                                    <td th:text="${cell.itemCode}">A001</td>
                                    <td th:text="${cell.itemName}">药品名称</td>
                                    <td th:text="${cell.drugSpec}">规格</td>
                                    <td th:text="${cell.price}">10.00</td>
                                    <td th:text="${cell.number}">1</td>
                                    <td th:text="${cell.deptName}">内科</td>
                                    <td th:text="${cell.drugUsedDays}">3</td>
                                    <td th:text="${cell.perDosage + '/' + cell.frequency}">1/1</td>
                                    <td th:text="${cell.itemType}">西药</td>
                                    <td th:text="${cell.totalAmount}">10.00</td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 右侧面板 -->
        <div class="right-panel">
            <!-- 注意事项 -->
            <div class="notice-section" th:if="${rightPanel.noticeSection != null}">
                <div class="notice-header" th:text="${rightPanel.noticeSection.header}">注意事项</div>
                <div class="notice-content">
                    <p th:each="notice : ${rightPanel.noticeSection.notices}" th:utext="${notice}">您好！您此条医嘱涉嫌违规。</p>
                </div>
            </div>
            <!-- 当没有注意事项时显示默认内容 -->
            <div class="notice-section" th:if="${rightPanel == null or rightPanel.noticeSection == null}">
                <div class="notice-header">注意事项</div>
                <div class="notice-content">
                    <p>暂无注意事项</p>
                </div>
            </div>

            <!-- 操作按钮区域 -->
            <div class="button-section">
                <div class="operation-selection">
                    <div class="radio-group">
                        <label class="operation-option">
                            <input type="radio" name="operation" value="patient-request" checked>
                            <span>患者要求</span>
                        </label>
                        <label class="operation-option">
                            <input type="radio" name="operation" value="patient-need">
                            <span>患者需要</span>
                        </label>
                    </div>
                    <div class="radio-group">
                        <label class="operation-option">
                            <input type="radio" name="operation" value="self-pay">
                            <span>自费</span>
                        </label>
                        <label class="operation-option">
                            <input type="radio" name="operation" value="other">
                            <span>其他</span>
                        </label>
                    </div>
                </div>
                <div class="text-area-container">
                    <textarea class="comment-textarea" placeholder="请输入备注信息..."></textarea>
                </div>
            </div>

            <!-- 底部按钮 -->
            <div class="bottom-buttons">
                <button class="btn btn-green">打印自费协议</button>
                <button class="btn btn-orange">轻质使用</button>
                <button class="btn btn-yellow">保存草稿</button>
                <button class="btn btn-blue">返回修改</button>
            </div>
        </div>
    </div>
</div>
</body>

</html>