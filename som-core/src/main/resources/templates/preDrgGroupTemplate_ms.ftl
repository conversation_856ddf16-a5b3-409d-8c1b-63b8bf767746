<!DOCTYPE html>
<html lang="en" xmlns="http://www.w3.org/1999/html">
<head>
    <meta charset="UTF-8">
    <title>预分组/预校验</title>

    <!-- styles -->
    <link rel="stylesheet" href="${url}/preMedia/css/pre.css" media="all"/>
    <link rel="stylesheet" href="${url}/preMedia/css/sfq.css"/>
    <link rel="stylesheet" href="${url}/preMedia/css/jq22.css"/>
    <link rel="stylesheet" href="${url}/preMedia/css/jq23.css"/>

    <style>
        @font-face {
            font-family: 'FontAwesome';
            src: url('${url}/preMedia/fonts/fontawesome-webfont.eot?v=4.7.0');
            src: url('${url}/preMedia/fonts/fontawesome-webfont.eot?#iefix&v=4.7.0') format('embedded-opentype'), url('${url}/preMedia/fonts/fontawesome-webfont.woff2?v=4.7.0') format('woff2'), url('${url}/preMedia/fonts/fontawesome-webfont.woff?v=4.7.0') format('woff'), url('${url}/preMedia/fonts/fontawesome-webfont.ttf?v=4.7.0') format('truetype'), url('${url}/preMedia/fonts/fontawesome-webfont.svg?v=4.7.0#fontawesomeregular') format('svg');
            font-weight: normal;
            font-style: normal;
        }
    </style>
    <link rel="stylesheet" href="${url}/preMedia/css/font-awesome.css"/>

    <!-- scroll-js -->
    <script type="text/javascript" src="${url}/preMedia/js/jquery.min.js"></script>
    <script>if (typeof module === 'object') {
            window.jQuery = window.$ = module.exports;
        }
        ;</script>

    <!-- script -->
    <script type="text/javascript" src="${url}/preMedia/js/sfq.js"></script>
</head>
<body>
<#if code == "1">
    <div class="container">
        <div class="header">
            <span class="header-title"></span>
        </div>
        <div class="content">
            <div class="content-inner">
                <!-- drg入组情况 -->
                <div class="in-group-circumstance">
                    <div class="hop-title">
                        <#if hospName??>
                            ${hospName}-AI小助手
                        <#else >
                            测算医院-AI小助手
                        </#if>
                    </div>
                    <div class="divider"></div>
                    <div class="title">
                        <img src="${url}/preMedia/images/med-case-ingroup.png" height="35px" width="35px"/>
                        <span>AI病例入组</span>
                    </div>
                    <#--                    <div class="divider"></div>-->
                    <div class="in-group-content">

                        <div class="in-group-base-info">
                            <div class="in-group-base">
                                <div>
                                    <img src="${url}/preMedia/images/patient_icon.png" height="15px" width="15px"/>
                                    <span>${xm}</span>
                                </div>

                                <div>
                                    <img src="${url}/preMedia/images/gender.png" height="15px" width="15px"/>
                                    <span>${xb}</span>
                                </div>
                                <div>
                                    <img src="${url}/preMedia/images/age.png" height="15px" width="15px"/>
                                    <span>${nl}</span>
                                </div>
                                <div style="margin-right: 1rem;">
                                    <img src="${url}/preMedia/images/med_no.png" height="15px" width="15px"/>
                                    <span>${medcasCodg}</span>
                                </div>
                                <div>
                                    <img src="${url}/preMedia/images/total_fee.png" height="15px" width="15px"/>
                                    <span>${inHosCost}</span>
                                </div>

                                <div>
                                    <img src="${url}/preMedia/images/insure_type.png" height="15px" width="15px"/>
                                    <span>${cblx}</span>
                                </div>

                                <div>
                                    <img src="${url}/preMedia/images/hosp_days.png" height="15px" width="15px"/>
                                    <span>${inHosDays}</span>
                                </div>

                            </div>

                            <div class="in-group-diagnose">
                                <div class="in-group-diagnose-details">
                                    <img src="${url}/preMedia/images/disease.png" height="15px" width="15px"/>
                                    <#if zyzd?? && (zyzd?size > 0)>
                                        <#list zyzd as zyzdbm>
                                            <#if zyzdbm_index=0 >
                                                <span>${zyzdbm}</span>
                                                <span>${zyzdmc}</span>
                                            </#if>
                                        </#list>
                                    </#if>
                                </div>
                                <div class="in-group-diagnose-details">
                                    <img src="${url}/preMedia/images/operation.png" height="15px" width="15px"/>
                                    <div class="operation" id="operation">
                                        <div>
                                            <#if zyss?? && (zyss?size > 0)>
                                                <#list zyss as zyssbm>
                                                    <span>${zyssbm}</span>
                                                    <#list ssjczmc as zyssmc>
                                                        <#if zyssmc_index= zyssbm_index>
                                                            <span>${zyssmc}</span>
                                                            </br>
                                                        </#if>
                                                    </#list>
                                                </#list>
                                            <#else>
                                                <span>暂无手术</span>
                                            </#if>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="in-ccmcc-base">
                                <div>
                                    <img src="${url}/preMedia/images/complication.png" height="15px"
                                         width="15px"/>MCC:
                                    <#if mccDisCode?? && (mccDisCode?size > 0)>
                                        <#list mccDisCode as mccItem>
                                            <span>${mccItem}</span>
                                        </#list>
                                    <#else>
                                        <span>无</span>
                                    </#if>
                                </div>
                                <div>
                                    <img src="${url}/preMedia/images/complication.png" height="15px"
                                         width="15px"/>CC:
                                    <#if ccDisCode?? && (ccDisCode?size > 0)>
                                        <#list ccDisCode as ccItem>
                                            <span>${ccItem}</span>
                                        </#list>
                                    <#else>
                                        <span>无</span>
                                    </#if>
                                </div>
                            </div>
                            <div class="in-group-dip-group">
                                <img src="${url}/preMedia/images/inGourp.png" height="30px" width="30px"/>
                                <#if drgCodg?? && drgName??>
                                    <div>
                                        <p>
                                            ${drgCodg}
                                        </p>
                                        <p>
                                            ${drgName}
                                        </p>
                                    </div>
                                <#else>
                                    <div>
                                        <p style="color: red;text-align: center;font-weight: bold">${rzqk}${rzqkxx}</p>
                                    </div>
                                </#if>
                            </div>


                        </div>
                    </div>
                    <#--                <#if dyJbbm ?? && (dyJbbm?length gt 1) || dySsjczbm ?? && (dySsjczbm ?size >0)>-->
                    <#--                    <div class="group-recommend-content">-->
                    <#--                        <ul id=accordion_4 class="accordion">-->
                    <#--                            <li>-->
                    <#--                                <div class="link">-->
                    <#--                                    <img src="${url}/preMedia/images/费用.png" height="25px" width="25px" class="des-icon"/>-->
                    <#--                                    <div class="sfq-header">-->
                    <#--                                        <p style="width: 48%;">-->
                    <#--                                            德阳市不参与分组手术和诊断及限定核心病种-->
                    <#--                                        </p>-->
                    <#--                                    </div>-->
                    <#--                                    <div class="sfq-header" style="display: none">-->
                    <#--                                        <p style="width: 48%;">德阳市不参与分组手术和诊断及限定核心病种</p>-->
                    <#--                                    </div>-->
                    <#--                                    <i class="fa fa-chevron-down"></i>-->
                    <#--                                </div>-->
                    <#--                                <ul class="submenu">-->
                    <#--                                    <div class="sfq-header">-->
                    <#--                                        <#if dyJbbm?? && (dyJbbm?length gt 1)>-->
                    <#--                                            <li>-->
                    <#--                                                <a href="#" >-->
                    <#--                                                    <div class="sfq-header">-->
                    <#--                                                        <p style="width: 99%;">-->
                    <#--                                                            <#list zyzd as zyzdbm>-->
                    <#--                                                                <#if zyzdbm_index=0 >-->
                    <#--                                                                    <span >${zyzdbm}${dyJbbm}</span>-->
                    <#--                                                                </#if>-->
                    <#--                                                            </#list>-->
                    <#--                                                        </p>-->
                    <#--                                                    </div>-->
                    <#--                                                </a>-->
                    <#--                                            </li>-->
                    <#--                                        </#if>-->
                    <#--                                        <#if dySsjczbm ?? && (dySsjczbm ?size >0)>-->
                    <#--                                            <#list zyss as zyssbm>-->
                    <#--                                                <li>-->
                    <#--                                                    <a href="#" >-->
                    <#--                                                        <div style="display: flex;">-->
                    <#--                                                    <span style="width: 50%;font-size: 12px;">-->
                    <#--                                                            手术及操作编码: ${zyssbm}-->
                    <#--                                                    </span>-->
                    <#--                                                            <#list dySsjczbm as dyfj>-->
                    <#--                                                                <#if dyfj_index= zyssbm_index>-->
                    <#--                                                                    <span style="width: 50%;font-size: 12px;">-->
                    <#--                                                                ${dyfj}-->
                    <#--                                                            </span>-->
                    <#--                                                                </#if>-->
                    <#--                                                            </#list>-->
                    <#--                                                        </div>-->
                    <#--                                                    </a>-->
                    <#--                                                </li>-->
                    <#--                                            </#list>-->
                    <#--                                        </#if>-->
                    <#--                                </ul>-->
                    <#--                            </li>-->
                    <#--                        </ul>-->
                    <#--                    </div>-->
                    <#--                </#if>-->

                    <div class="divider"></div>

                    <!-- 1、drg付费预测 -->
                    <div class="pre-pay">
                        <div class="title">
                            <img src="${url}/preMedia/images/payment-forecast.png" height="32px" width="32px"/>
                            <span>AI付费预测</span>
                        </div>
                        <#--                        <div class="divider"></div>-->
                        <div class="pre-pay-content" id="pre-pay-content-id">
                            <div class="tooltip__popper is-dark" id="tooltip"
                                 style="transform-origin: center bottom;z-index: 2137;top: -10px;color: black;">
                                总费用:${inHosCost}
                                <div class="popper__arrow"></div>
                            </div>
                            <div class="pre-pay-content-range">
                                <div class="low range-item" id="range-min">
                                    <div class="range-money" style="right: 50%"><${lowlmtMag}%</div>
                                    <div class="range-money" style="right: -20%;top: 100%">
                                        <#if benchmarkLevel??>
                                            ${minFee}
                                        </#if>
                                    </div>
                                </div>
                                <div class="normal range-item" id="range-min-normal">
                                    <div class="range-money-normal" style="right: 26%">${lowlmtMag}%~100%</div>
                                    <div class="range-money-normal" style="top: 100%">
                                        <#if ycfy??>
                                            ${ycfy}
                                        </#if>
                                    </div>
                                </div>
                                <div class="normal-to-high range-item" id="range-normal-max">
                                    <div class="range-money" style="right: 36%">100%~${uplmtMag}%</div>
                                    <div class="range-money" style="right: -11%;top: 100%">
                                        <#if benchmarkLevel??>
                                            ${highFee}
                                        </#if>
                                    </div>
                                </div>
                                <div class="high range-item" id="range-max">>${uplmtMag}%</div>
                            </div>

                            <ul id="${drgCodg???string('accordion','ac_1')}" class="accordion">
                                <li>
                                    <div class="link">
                                        <img src="${url}/preMedia/images/pre_cost.png" height="25px" width="25px"
                                             class="des-icon"/>
                                        <#if drgCodg??>
                                            <div class="sfq-header">
                                                <p style="width: 22%;">
                                                    预测分值: ${zfz}
                                                </p>
                                                <p style="width: 26%;">
                                                    预测费用: ${ycfy}
                                                </p>
                                                <p style="width: 25%;">
                                                    <#if fycy gt 0>
                                                        <span class="warn-tips">预测金额差异: ${fycy}</span>
                                                    <#else>
                                                        <span class="warn-tips">预测金额差异: ${fycy}</span>
                                                    </#if>
                                                </p>
                                                <p style="position: relative;width: 24%;">
                                                    <#if inHosCost gt highFee>
                                                        <span style="color: rgba(192,0,23,0.53);font-size: 12px">高倍率病例</span>
                                                        <img src="${url}/preMedia/images/warning.png" height="15px"
                                                             width="15px" style="position: absolute;left: 65px;"/>
                                                    <#elseif inHosCost lt minFee>
                                                        <span style="color: rgba(192,0,23,0.53);font-size: 12px">低倍率病例</span>
                                                        <img src="${url}/preMedia/images/warning.png" height="15px"
                                                             width="15px" style="position: absolute;left: 65px;"/>
                                                    <#else>
                                                        <span style="color: #3BC08E99;font-size: 12px">正常倍率病例</span>
                                                        <img src="${url}/preMedia/images/goodCost.png" height="15px"
                                                             width="15px" style="position: absolute;left: 75px;"/>
                                                    </#if>
                                                </p>
                                            </div>
                                            <div class="sfq-header" style="display: none;">
                                                <p style="width: 22%;">费用信息</p>
                                            </div>
                                            <i class="fa fa-chevron-down"></i>
                                        <#else >
                                            <div class="sfq-header" style="display: flex;">
                                                <p style="width: 22%;">暂无费用信息</p>
                                            </div>
                                        </#if>
                                    </div>
                                    <ul class="submenu">
                                        <#if drgCodg??>
                                            <li>
                                                <a href="#">
                                                    <div class="sfq-header">
                                                        <p style="width: 22%;">
                                                            基准分值: ${jzfz}
                                                        </p>
                                                        <p style="width: 26%;">
                                                            级别基本系数: ${jbjbxs}
                                                        </p>
                                                        <p style="width: 25%;">
                                                            计算分值: ${ycfzFirst}
                                                        </p>
                                                        <p style="position: relative;width: 24%;">
                                                            加成分值: ${jcfz}
                                                        </p>
                                                    </div>
                                                </a>
                                            </li>
                                            <li>
                                                <a href="#">
                                                    <div class="sfq-header">
                                                        <p style="width: 22%;">
                                                            总分值: ${zfz}
                                                        </p>
                                                        <p style="width: 26%;">
                                                            分值单价: ${fzdj}
                                                        </p>
                                                        <p style="position: relative;width: 25%;">
                                                            预测费用: ${ycfy}
                                                        </p>
                                                    </div>
                                                </a>
                                            </li>
                                            <li>
                                                <a href="#">
                                                    <div class="sfq-header">
                                                        <p style="width: 48%;">
                                                            <#if fycy gt 0>
                                                                <span style="color: rgb(91,174,99);font-size: 12px;">预测金额差异: ${fycy}</span>
                                                            <#else>
                                                                <span style="color: red;font-size: 12px;">预测金额差异: ${fycy}</span>
                                                            </#if>
                                                        </p>
                                                        <p style="position: relative;width: 26%;">
                                                            <#if inHosCost gt highFee>
                                                                <span style="color: red;font-size: 12px">高倍率病例</span>
                                                                <img src="${url}/preMedia/images/warning.png"
                                                                     height="15px"
                                                                     width="15px"
                                                                     style="position: absolute;left: 65px;"/>
                                                            <#elseif inHosCost lt minFee>
                                                                <span style="color: red;font-size: 12px">低倍率病例</span>
                                                                <img src="${url}/preMedia/images/warning.png"
                                                                     height="15px"
                                                                     width="15px"
                                                                     style="position: absolute;left: 65px;"/>
                                                            <#else>
                                                                <span style="color: rgb(91,174,99);font-size: 12px">正常倍率病例</span>
                                                                <img src="${url}/preMedia/images/goodCost.png"
                                                                     height="15px" width="15px"
                                                                     style="position: absolute;left: 75px;"/>
                                                            </#if>
                                                        </p>
                                                    </div>
                                                </a>
                                            </li>
                                        </#if>
                                    </ul>
                                </li>
                                <li>
                                    <div class="link">
                                        <img src="${url}/preMedia/images/benchmark_cost.png" height="25px" width="25px"
                                             class="des-icon"/>
                                        <#if drgCodg??>
                                            <div class="sfq-header">
                                                <p style="width: 22%;">
                                                    全市次均费用: ${benchmarkLevel}
                                                </p>
                                                <p style="width: 26%;">
                                                    费用差异: ${bgfycy}
                                                </p>
                                                <p style="width: 25%;">
                                                    全市次均住院天数: ${inHosAvgDays}
                                                </p>
                                                <p style="width: 24%;">
                                                    住院天数差异：${zytscy}
                                                </p>
                                            </div>
                                            <div class="sfq-header" style="display: none;">
                                                <p style="width: 22%;">标杆信息</p>
                                            </div>
                                            <i class="fa fa-chevron-down"></i>
                                        <#else >
                                            <div class="sfq-header" style="display: flex;">
                                                <p style="width: 22%;">暂无标杆信息</p>
                                            </div>
                                        </#if>
                                    </div>
                                    <ul class="submenu">
                                        <#if drgCodg??>
                                            <li>
                                                <a href="#">
                                                    <div class="sfq-header">
                                                        <p style="width: 22%;">
                                                            总费用: ${inHosCost}
                                                        </p>
                                                        <p style="width: 26%;">
                                                            全市次均费用: ${benchmarkLevel}
                                                        </p>
                                                        <p style="width: 25%;">
                                                            费用差异: ${bgfycy}
                                                        </p>
                                                        <p style="width: 24%;height: 22px;position: relative">
                                                            <#if bgfycy gt 0>
                                                                <img src="${url}/preMedia/images/好评.png" height="20px"
                                                                     width="20px"
                                                                     style="position: absolute;left: 38%;top: 2px"/>
                                                            <#elseif inHosCost lt minFee || bgfycy lt 0>
                                                                <img src="${url}/preMedia/images/差评.png" height="18px"
                                                                     width="18px"
                                                                     style="position: absolute;left: 38%;top: 2px"/>
                                                            </#if>
                                                        </p>
                                                    </div>
                                                </a>
                                            </li>
                                            <li>
                                                <a href="#">
                                                    <div class="sfq-header">
                                                        <p style="width: 22%;">
                                                            住院天数: ${inHosDays}
                                                        </p>
                                                        <p style="width: 26%;">
                                                            全市次均天数: ${inHosAvgDays}
                                                        </p>
                                                        <p style="width: 25%;">
                                                            差距: ${inHosAvgDays - inHosDays}
                                                        </p>
                                                        <p style="width: 24%;height: 22px;position: relative">
                                                            <#if inHosAvgDays??>
                                                                <#if zytscy gt 0>
                                                                    <img src="${url}/preMedia/images/好评.png"
                                                                         height="20px" width="20px"
                                                                         style="position: absolute;left: 38%;top: 2px"/>
                                                                <#else>
                                                                    <img src="${url}/preMedia/images/差评.png"
                                                                         height="20px" width="20px"
                                                                         style="position: absolute;left: 38%;top: 2px"/>
                                                                </#if>
                                                            </#if>
                                                        </p>
                                                    </div>
                                                </a>
                                            </li>
                                            <li>
                                                <a href="#">
                                                    <div class="sfq-header">
                                                        <p style="width: 22%;">
                                                            药占比: ${ybfyzb}%
                                                        </p>
                                                        <p style="width: 26%;">
                                                            全市次均占比: ${ypfbgzb}
                                                        </p>
                                                        <p style="width: 25%;">
                                                            差距: ${ypfycy}%
                                                        </p>
                                                        <p style="width: 24%;height: 22px;position: relative">
                                                            <#if ypfbgzb??>
                                                                <#if ypfycy gt 0>
                                                                    <img src="${url}/preMedia/images/好评.png"
                                                                         height="20px" width="20px"
                                                                         style="position: absolute;left: 38%;top: 2px"/>
                                                                <#else>
                                                                    <img src="${url}/preMedia/images/差评.png"
                                                                         height="20px" width="20px"
                                                                         style="position: absolute;left: 38%;top: 2px"/>
                                                                </#if>
                                                            </#if>
                                                        </p>
                                                    </div>
                                                </a>
                                            </li>
                                            <li>
                                                <a href="#">
                                                    <div class="sfq-header">
                                                        <p style="width: 22%;">
                                                            耗材比: ${hcfyzb}%
                                                        </p>
                                                        <p style="width: 26%;">
                                                            全市次均占比: ${hcfybgzb}
                                                        </p>
                                                        <p style="width: 25%;">
                                                            差距: ${hcfycy}%
                                                        </p>
                                                        <p style="width: 24%;height: 22px;position: relative">
                                                            <#if hcfybgzb??>
                                                                <#if hcfycy gt 0>
                                                                    <img src="${url}/preMedia/images/好评.png"
                                                                         height="20px" width="20px"
                                                                         style="position: absolute;left: 38%;top: 2px"/>
                                                                <#else>
                                                                    <img src="${url}/preMedia/images/差评.png"
                                                                         height="20px" width="20px"
                                                                         style="position: absolute;left: 38%;top: 2px"/>
                                                                </#if>
                                                            </#if>
                                                        </p>
                                                    </div>
                                                </a>
                                            </li>
                                        </#if>
                                    </ul>
                                </li>
                            </ul>
                        </div>
                    </div>

                    <!--2、分组推荐、病案质控、清单质控-->
                    <div class="top_tabs">
                        <button class="tab active" onclick="openTab(event, 'group-recommend-tab')"><img
                                    src="${url}/preMedia/images/group-recommend.png" class="tab_icon"/>
                            <span>AI分组推荐<#if zntj?? && (zntj ?size > 0)>${zntj ?size}</#if></button>
                        </span>

                        <#--                        <button class="tab" onclick="openTab(event, 'med-vali-tab')"><img-->
                        <#--                                    src="${url}/preMedia/images/med-vali.png" class="tab_icon"/>-->
                        <#--                            <span>AI病例质控-->
                        <#--                                <#assign med_val_size= 0>-->
                        <#--                                <#if logicalCheckList?? && (logicalCheckList ?size > 0)>-->
                        <#--                                    <#assign med_val_size= med_val_size+logicalCheckList ?size>-->
                        <#--                                </#if>-->
                        <#--                                <#if integrityCheckList?? && (integrityCheckList ?size > 0)>-->
                        <#--                                    <#assign med_val_size= med_val_size+integrityCheckList ?size>-->
                        <#--                                </#if>-->
                        <#--                                ${(med_val_size)!}-->
                        <#--                            </span>-->
                        <#--                        </button>-->
                        <#if isWeb?? && isWeb == '1'>
                        <#else >
                            <button class="tab" onclick="openTab(event, 'setl-vali-tab')">
                                <img src="${url}/preMedia/images/setl-vali.png"
                                     class="tab_icon"/>
                                <span>AI清单质控
                                    <#if qualityInfo??>
                                        <#assign setl_val_size= 0>
                                        <#list qualityInfo as qiItem>
                                            <#if qiItem.data?? && (qiItem.data?size>0)>
                                                <#assign setl_val_size=setl_val_size+qiItem.data?size>
                                            </#if>
                                        </#list>
                                        ${(setl_val_size)!}
                                    </#if>
                                </span>
                            </button>
                        </#if>
                    </div>

                    <!-- 3、AI分组推荐 -->
                    <div id="group-recommend-tab" class="tabcontent">
                        <div class="group-recommend">
                            <#--                        <div class="title">-->
                            <#--                            <img src="${url}/preMedia/images/推荐_sel.png" height="35px" width="35px"/>-->
                            <#--                            <span>AI分组推荐</span>-->
                            <#--                        </div>-->
                            <#--                        <div class="divider"></div>-->
                            <div class="group-recommend-content">
                                <ul id="${drgCodg???string('accordion_2','ac_2')}" class="accordion"
                                    style="margin-top: 1rem;">
                                    <li>
                                        <#--                                    <div class="link">-->
                                        <#--                                        <img src="${url}/preMedia/images/灯泡提示.png" height="25px" width="25px" class="des-icon"/>-->
                                        <#--                                        <#if zntj?? && (zntj ?size > 0)>-->
                                        <#--                                            <div class="sfq-header">-->
                                        <#--                                                <#list zntj as drg>-->
                                        <#--                                                    <#if drg_index=0>-->
                                        <#--                                                        <p style="width: 22%;">-->
                                        <#--                                                            ${drg.drgCodg}-->
                                        <#--                                                        </p>-->
                                        <#--                                                        <p style="width: 26%;">-->
                                        <#--                                                            ${drg.drgName}-->
                                        <#--                                                        </p>-->
                                        <#--                                                        <p style="width: 25%;">-->
                                        <#--                                                            病组费用: ${drg.levelStandardCost}-->
                                        <#--                                                        </p>-->
                                        <#--                                                        <p style="position: relative;width: 24%;height: 16px;color: #C00017;">-->
                                        <#--                                                            <#list 2..0 as t>-->
                                        <#--                                                                <img src="${url}/preMedia/images/五星 (1).png" height="16px" width="16px" />-->
                                        <#--                                                            </#list>-->
                                        <#--                                                        </p>-->
                                        <#--                                                    </#if>-->
                                        <#--                                                </#list>-->
                                        <#--                                            </div>-->
                                        <#--                                            <div class="sfq-header" style="display: none">-->
                                        <#--                                                <p style="width: 22%;">病组信息</p>-->
                                        <#--                                            </div>-->
                                        <#--                                            <i class="fa fa-chevron-down"></i>-->
                                        <#--                                        <#else>-->
                                        <#--                                            <div class="sfq-header" style="display: flex">-->
                                        <#--                                                <p style="width: 22%;">暂无病组信息</p>-->
                                        <#--                                            </div>-->
                                        <#--                                        </#if>-->
                                        <#--                                    </div>-->

                                        <ul class="submenu"
                                            id="${(zntj?? && (zntj ?size > 0))?string('secondaryMenu','')}"
                                            style="display:block;">
                                            <#if zntj?? && (zntj ?size > 0)>
                                                <#list zntj as drg>
                                                    <li class="open">
                                                        <#if drgCodg?? && drgCodg == drg.drgCodg>
                                                        <a href="#" class="secondary-menu">
                                                            <#else >
                                                            <a href="#" class="secondary-menu">
                                                                </#if>
                                                                <div class="sfq-header">
                                                                    <p style="width: 22%;">
                                                                        ${drg.drgCodg}
                                                                    </p>
                                                                    <p style="width: 26%;">
                                                                        ${drg.drgName}
                                                                    </p>
                                                                    <p style="width: 25%;">
                                                                        病组费用: ${drg.ycfy}
                                                                    </p>
                                                                    <p style="width: 24%;">
                                                                        <#if drg_index=0>
                                                                            <#list 2..0 as t>
                                                                                <img src="${url}/preMedia/images/五星 (1).png"
                                                                                     height="16px" width="16px"/>
                                                                            </#list>
                                                                        <#elseif drg_index=1>
                                                                            <#list 1..0 as t>
                                                                                <img src="${url}/preMedia/images/五星 (1).png"
                                                                                     height="16px" width="16px"/>
                                                                            </#list>
                                                                        <#elseif drg_index=2>
                                                                            <img src="${url}/preMedia/images/五星 (1).png"
                                                                                 height="16px" width="16px"/>
                                                                        <#else>
                                                                        </#if>
                                                                    </p>
                                                                </div>
                                                            </a>
                                                            <ul class="secondary-submenu">
                                                                <li>
                                                                    <a href="#">
                                                                        <div class="sfq-header">
                                                                            <p style="width: 22%;">
                                                                                基准分值: ${drg.jzfz}
                                                                            </p>
                                                                            <p style="width: 26%;">
                                                                                级别基本系数: ${drg.jbjbxs}
                                                                            </p>
                                                                            <p style="width: 25%;">
                                                                                计算分值: ${drg.ycfzFirst}
                                                                            </p>
                                                                            <p style="width: 22%;">
                                                                                加成分值: ${drg.jcfz}
                                                                            </p>
                                                                        </div>
                                                                    </a>
                                                                </li>
                                                                <li>
                                                                    <a href="#">
                                                                        <div class="sfq-header">
                                                                            <p style="width: 22%;">
                                                                                总分值: ${drg.zfz}
                                                                            </p>
                                                                            <p style="width: 26%;">
                                                                                分值单价: ${drg.fzdj}
                                                                            </p>
                                                                            <p style="width: 25%;">
                                                                                预测费用: ${drg.ycfy}
                                                                            </p>
                                                                            <p style="width: 22%;">
                                                                                诊断编码: ${drg.diagnoseCode}
                                                                            </p>
                                                                        </div>
                                                                    </a>
                                                                </li>
                                                                <li>
                                                                    <a href="#">
                                                                        <div class="sfq-header">
                                                                            <p style="width: 48%;">
                                                                                <#if drg.fycy gt 0>
                                                                                    <span style="color: rgb(0 159 7);font-size: 12px;">预测金额差异: ${drg.fycy}</span>
                                                                                <#else>
                                                                                    <span style="color: red;font-size: 12px;">预测金额差异: ${drg.fycy}</span>
                                                                                </#if>
                                                                            </p>
                                                                        </div>
                                                                    </a>
                                                                </li>
                                                            </ul>
                                                        </a>
                                                    </li>
                                                </#list>
                                            </#if>
                                        </ul>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <!-- 4、AI病例质控 -->
                    <div id="med-vali-tab" class="tabcontent" style="display: none">
                        <div class="pre-pay">
                            <#--                        <div class="title">-->
                            <#--                            <img src="${url}/preMedia/images/质控模块.png" height="35px" width="35px"/>-->
                            <#--                            <span>AI病例质控</span>-->
                            <#--                        </div>-->
                            <#--                        <div class="divider"></div>-->
                            <div class="record-content">
                                <ul id="accordion_3" class="accordion" style="margin-top: 1rem;">
                                    <li>
                                        <#--                                        <div class="link">-->
                                        <#--                                            <img src="${url}/preMedia/images/积分.png" height="25px" width="25px"-->
                                        <#--                                                 class="des-icon"/>-->
                                        <#--                                            <div class="sfq-header">-->
                                        <#--                                                <p style="width: 22%;">-->
                                        <#--                                                    病例得分: ${medicalRecordScore}-->
                                        <#--                                                </p>-->
                                        <#--                                                <p style="width: 26%;">-->
                                        <#--                                                    逻辑性错误: ${countLogicalCheck}-->
                                        <#--                                                </p>-->
                                        <#--                                                <p style="width: 25%;">-->
                                        <#--                                                    完整性错误: ${countIntegrity}-->
                                        <#--                                                </p>-->
                                        <#--                                            </div>-->
                                        <#--                                            <div class="sfq-header" style="display: none">-->
                                        <#--                                                <p style="width: 22%;">质控信息</p>-->
                                        <#--                                            </div>-->
                                        <#--                                            <i class="fa fa-chevron-down"></i>-->
                                        <#--                                        </div>-->
                                        <ul class="submenu" style="display:block;">
                                            <div class="sfq-header">
                                                <li>
                                                    <a href="#">
                                                        <p style="width: 22%;">
                                                            病例得分: ${medicalRecordScore}
                                                        </p>
                                                        <p style="width: 26%;">
                                                            逻辑性错误: ${countLogicalCheck}
                                                        </p>
                                                        <p style="width: 25%;">
                                                            完整性错误: ${countIntegrity}
                                                        </p>
                                                    </a>
                                                </li>
                                                <li><a href="#"><p>逻辑性问题:</p></a></li>
                                                <#if logicalCheckList?? && (logicalCheckList?size > 0)>
                                                    <#list logicalCheckList as item>
                                                        <li class="quality-control-item">
                                                            <a href="#">
                                                                <p>${item_index+1}、${item}</p>
                                                            </a>
                                                        </li>
                                                    </#list>
                                                </#if>
                                                <li><a href="#"><p>完整性问题:</p></a></li>
                                                <#if integrityCheckList?? && (integrityCheckList?size > 0)>
                                                    <#list integrityCheckList as data>
                                                        <li class="quality-control-item">
                                                            <a href="#">
                                                                <p>${data_index+1}、${data.content}</p>
                                                                <p style="float: right;color: rgba(255, 0, 0, 0.93);">
                                                                    扣${data.refer_sco}分</p>
                                                            </a>
                                                        </li>
                                                    </#list>
                                                </#if>
                                            </div>
                                        </ul>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <!-- 5、AI清单质控 -->
                    <#if isWeb?? && isWeb == '1'>
                    <#else >
                        <div id="setl-vali-tab" class="tabcontent" style="display: none">
                            <div class="setl-vali">
                                <#--                                <div class="title">-->
                                <#--                                    <img src="${url}/preMedia/images/质控模块.png" height="35px" width="35px"/>-->
                                <#--                                    <span>AI清单质控</span>-->
                                <#--                                </div>-->
                                <#--                                <div class="divider"></div>-->
                                <div class="record-content">
                                    <ul id="accordion_5" class="accordion" style="margin-top: 1rem;">
                                        <li>
                                            <#--                                            <div class="link">-->
                                            <#--                                                <img src="${url}/preMedia/images/积分.png" height="25px" width="25px"-->
                                            <#--                                                     class="des-icon"/>-->
                                            <#--                                                <div class="sfq-header">-->
                                            <#--                                                    <#list qualityInfo as qiItem>-->
                                            <#--                                                        <p style="width: 22%;">-->
                                            <#--                                                            ${qiItem.name}: ${qiItem.errorNum}-->
                                            <#--                                                        </p>-->
                                            <#--                                                    </#list>-->
                                            <#--                                                </div>-->
                                            <#--                                                <div class="sfq-header" style="display: none">-->
                                            <#--                                                    <p style="width: 22%;">清单质控信息</p>-->
                                            <#--                                                </div>-->
                                            <#--                                                <i class="fa fa-chevron-down"></i>-->
                                            <#--                                            </div>-->
                                            <ul class="submenu" style="display: block">
                                                <!-- 清单质控 -->
                                                <div class="sl-content">
                                                    <ul id="tabs">
                                                        <#list qualityInfo as qiItem>
                                                            <li><a href="#"
                                                                   title="tab${qiItem.index}">${qiItem.name}</a></li>
                                                        </#list>
                                                    </ul>
                                                    <div id="content">
                                                        <#list qualityInfo as qiItem>
                                                            <span id="tab${qiItem.index}"
                                                                  class="sl-content-item ${(qiItem.index == 1) ? string('sl-content-padding1', 'sl-content-padding2')}"
                                                                  style="overflow: ${(qiItem.index == 1) ? string('hidden', 'overflow')}">
                                    <#if qiItem.index == 1>
                                        <!-- 嵌套基本质控信息 -->
                                        <ul id="tabs2">
                                            <#list qiItem.data as qiChildItem>
                                                <li><a href="#"
                                                       title="tabTwo${qiChildItem.index}">${qiChildItem.name}</a></li>
                                            </#list>
                                        </ul>
                                        <div id="content2">
                                             <#if qiItem.data?size == 0>
                                                 <p class="sl-qi-complete">${qiItem.name}已完成</p>
                                             </#if>
                                            <#list qiItem.data as qiChildItem>
                                                <span id="tabTwo${qiChildItem.index}" class="sl-content-item"
                                                      style="overflow: auto">
                                                        <#list qiChildItem.data?keys as key>
                                                            <#list qiChildItem.data[key] as obj>
                                                                <tr>
                                                                    <td>
                                                                        <p>${obj_index+1 + "."} ${obj.error}</p>
                                                                    </td>
                                                                </tr>
                                                            </#list>
                                                        </#list>
                                                    </span>
                                            </#list>
                                            </div>

                                    <#else>
                                        <#if qiItem.data?size lte 0>
                                            <p class="sl-qi-complete">${qiItem.name}已完成</p>
                                            <#else>
                                            <#list qiItem.data?keys as key>
                                                <div class="sl-qi-item-wrap">
                                                <p class="sl-qi-item-wrap-title">${key}</p>
                                                <#list qiItem.data[key] as obj>
                                                    <tr>
                                                        <td>
                                                            <p>${obj_index+1 + "."} ${obj.error}</p>
                                                        </td>
                                                    </tr>
                                                </#list>
                                            </div>
                                            </#list>
                                        </#if>
                                    </#if>
                                </span>
                                                        </#list>
                                                    </div>
                                                </div>
                                            </ul>

                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </#if>
                    <#--                <div class="pre-pay">-->
                    <#--                    <div class="title">-->
                    <#--                        <img src="${url}/preMedia/images/质控模块.png" height="35px" width="35px"/>-->
                    <#--                        <span>清单质控分析</span>-->
                    <#--                    </div>-->
                    <#--                    <div class="divider"></div>-->
                    <#--                    <div class="record-content">-->
                    <#--                        <ul id="accordion_5" class="accordion" style="margin-top: 1rem;">-->
                    <#--                            <li>-->
                    <#--                                <div class="link">-->
                    <#--                                    <img src="${url}/preMedia/images/积分.png" height="25px" width="25px" class="des-icon"/>-->
                    <#--                                    <div class="sfq-header">-->
                    <#--                                        <p style="width: 22%;">-->
                    <#--                                            空错误: ${nullErrorNum}-->
                    <#--                                        </p>-->
                    <#--                                        <p style="width: 26%;">-->
                    <#--                                            字典错误: ${dictErrorNum}-->
                    <#--                                        </p>-->
                    <#--                                        <p style="width: 25%;">-->
                    <#--                                            正则错误: ${regErrorNum}-->
                    <#--                                        </p>-->
                    <#--                                        <p style="width: 24%;">-->
                    <#--                                            长度错误: ${lengthErrorNum}-->
                    <#--                                        </p>-->
                    <#--                                    </div>-->
                    <#--                                    <div class="sfq-header" style="display: none">-->
                    <#--                                        <p style="width: 22%;">清单质控信息</p>-->
                    <#--                                    </div>-->
                    <#--                                    <i class="fa fa-chevron-down"></i>-->
                    <#--                                </div>-->
                    <#--                                <ul class="submenu">-->
                    <#--                                    <div class="sfq-header">-->
                    <#--                                        <#if busSettleListResult??>-->
                    <#--                                            <#list busSettleListResult?keys as key>-->
                    <#--                                                <li><a href="#"><p>${key}:</p></a></li>-->
                    <#--                                                <#list busSettleListResult[key] as resultItem>-->
                    <#--                                                    <li class="quality-control-item">-->
                    <#--                                                        <a href="#">-->
                    <#--                                                            <p>${resultItem_index+1}、${resultItem.error}</p>-->
                    <#--                                                        </a>-->
                    <#--                                                    </li>-->
                    <#--                                                </#list>-->
                    <#--                                            </#list>-->
                    <#--                                        </#if>-->

                    <#--                                        &lt;#&ndash;                                        <li><a href="#"><p>字典错误:</p></a></li>&ndash;&gt;-->
                    <#--                                        &lt;#&ndash;                                        <#if dictErrorList?? && (dictErrorList?size > 0)>&ndash;&gt;-->
                    <#--                                        &lt;#&ndash;                                            <#list dictErrorList as dictItem>&ndash;&gt;-->
                    <#--                                        &lt;#&ndash;                                                <li class="quality-control-item">&ndash;&gt;-->
                    <#--                                        &lt;#&ndash;                                                    <a href="#">&ndash;&gt;-->
                    <#--                                        &lt;#&ndash;                                                        <p>${dictItem_index+1}、${dictItem.error}</p>&ndash;&gt;-->
                    <#--                                        &lt;#&ndash;                                                    </a>&ndash;&gt;-->
                    <#--                                        &lt;#&ndash;                                                </li>&ndash;&gt;-->
                    <#--                                        &lt;#&ndash;                                            </#list>&ndash;&gt;-->
                    <#--                                        &lt;#&ndash;                                        </#if>&ndash;&gt;-->
                    <#--                                        &lt;#&ndash;                                        <li><a href="#"><p>正则错误:</p></a></li>&ndash;&gt;-->
                    <#--                                        &lt;#&ndash;                                        <#if regErrorList?? && (regErrorList?size > 0)>&ndash;&gt;-->
                    <#--                                        &lt;#&ndash;                                            <#list regErrorList as regItem>&ndash;&gt;-->
                    <#--                                        &lt;#&ndash;                                                <li class="quality-control-item">&ndash;&gt;-->
                    <#--                                        &lt;#&ndash;                                                    <a href="#">&ndash;&gt;-->
                    <#--                                        &lt;#&ndash;                                                        <p>${regItem_index+1}、${regItem.error}</p>&ndash;&gt;-->
                    <#--                                        &lt;#&ndash;                                                    </a>&ndash;&gt;-->
                    <#--                                        &lt;#&ndash;                                                </li>&ndash;&gt;-->
                    <#--                                        &lt;#&ndash;                                            </#list>&ndash;&gt;-->
                    <#--                                        &lt;#&ndash;                                        </#if>&ndash;&gt;-->
                    <#--                                        &lt;#&ndash;                                        <li><a href="#"><p>长度错误:</p></a></li>&ndash;&gt;-->
                    <#--                                        &lt;#&ndash;                                        <#if lengthErrorList?? && (lengthErrorList?size > 0)>&ndash;&gt;-->
                    <#--                                        &lt;#&ndash;                                            <#list lengthErrorList as lengthItem>&ndash;&gt;-->
                    <#--                                        &lt;#&ndash;                                                <li class="quality-control-item">&ndash;&gt;-->
                    <#--                                        &lt;#&ndash;                                                    <a href="#">&ndash;&gt;-->
                    <#--                                        &lt;#&ndash;                                                        <p>${lengthItem_index+1}、${lengthItem.error}</p>&ndash;&gt;-->
                    <#--                                        &lt;#&ndash;                                                    </a>&ndash;&gt;-->
                    <#--                                        &lt;#&ndash;                                                </li>&ndash;&gt;-->
                    <#--                                        &lt;#&ndash;                                            </#list>&ndash;&gt;-->
                    <#--                                        &lt;#&ndash;                                        </#if>&ndash;&gt;-->
                    <#--                                    </div>-->
                    <#--                                </ul>-->
                    <#--                            </li>-->
                    <#--                        </ul>-->
                    <#--                    </div>-->
                    <#--                </div>-->
                </div>
            </div>
        </div>
    </div>
    <script>
        window.onload = function () {
            // 左边padding宽度
            var left_padding = parseFloat(window.getComputedStyle(document.getElementById("pre-pay-content-id"), null).getPropertyValue("padding-left"))
            // 各个线宽度
            var range_min = document.getElementById("range-min")
            var range_min_normal = document.getElementById("range-min-normal")
            var range_normal_max = document.getElementById("range-normal-max")
            var range_max = document.getElementById("range-max")

            var range_min_offset_left = range_min.offsetLeft
            var range_min_normal_offset_left = range_min_normal.offsetLeft
            var range_normal_max_offset_left = range_normal_max.offsetLeft
            var range_max_offset_left = range_max.offsetLeft

            var rate = 0
            <#if rate ??>
            rate = ${rate?string("0.######")}
            </#if>

            var minFee = 0
            <#if minFee ??>
            minFee = ${minFee?string("0.####")}
            </#if>

            var benchmarkLevel = 0
            <#if benchmarkLevel ??>
            benchmarkLevel = ${benchmarkLevel?string("0.####")}
            </#if>

            var highFee = 0
            <#if highFee ??>
            highFee = ${highFee?string("0.####")}
            </#if>

            var inHosCost = 0
            <#if inHosCost ??>
            inHosCost = ${inHosCost?string("0.####")}
            </#if>

            var uplmtMag = 0
            <#if uplmtMag ??>
            uplmtMag = ${uplmtMag}
            </#if>

            var tooltip_left = 0
            if (rate >= 1.1) {
                tooltip_left = 1.1 * range_max_offset_left - left_padding - 27
            } else if (rate >= benchmarkLevel / highFee && rate < 1.1) {
                tooltip_left = ((inHosCost - benchmarkLevel) / (highFee - benchmarkLevel)) * (range_max_offset_left - range_normal_max_offset_left) + range_normal_max_offset_left - left_padding - 27
            } else if (rate >= minFee / highFee && rate < benchmarkLevel / highFee) {
                tooltip_left = ((inHosCost - minFee) / (benchmarkLevel - minFee)) * (range_normal_max_offset_left - range_min_normal_offset_left) + range_min_normal_offset_left - left_padding - 27
            } else {
                tooltip_left = (inHosCost / minFee) * range_min_normal_offset_left - left_padding - 26
            }
            tooltip.style.left = tooltip_left + "px"

            var curTabName = 'tabTwo1'

            $("#content span").hide(); // Initially hide all content
            $("#tabs a:first").addClass("active")
            $('#tab1').fadeIn();

            $('#tabs a').click(function (e) {
                e.preventDefault();
                $("#content span").hide(); //Hide all content
                $("#tabs a").removeClass("active")
                $(this).addClass("active")
                $('#' + $(this).attr('title')).fadeIn();
                if ($(this).attr('title') === "tab1") {
                    $('#' + curTabName).fadeIn();
                }
            });

            $("#content2 span").hide(); // Initially hide all content
            $("#tabs2 a:first").addClass("active")
            $('#tabTwo1').fadeIn();

            $('#tabs2 a').click(function (e) {
                e.preventDefault();
                $("#content2 span").hide(); //Hide all content
                $("#tabs2 a").removeClass("active")
                $(this).addClass("active")
                $('#' + $(this).attr('title')).fadeIn();
                curTabName = $(this).attr('title')
            });
        }

        /**
         * tab点击事件
         * @param evt
         * @param tabName
         */
        function openTab(evt, tabName) {
            var i, tabcontent, tablinks;
            tabcontent = document.getElementsByClassName("tabcontent");
            for (i = 0; i < tabcontent.length; i++) {
                tabcontent[i].style.display = "none";
            }
            tablinks = document.getElementsByClassName("tab");
            for (i = 0; i < tablinks.length; i++) {
                tablinks[i].className = tablinks[i].className.replace(" active", "");
            }
            document.getElementById(tabName).style.display = "block";
            evt.currentTarget.className += " active";
        }
    </script>
<#else>
    <div style="position: relative" class="container">
        <div style="width: 30%;height: 30%;position: absolute;top:0;left: 0;right: 0;bottom: 0;margin: auto">
            <div style="float: top;text-align: center">
                <img src="${url}/preMedia/images/错误.png" height="100px" width="100px"/>
                </br>
            </div>
            <div style="float: bottom;text-align: center">
                <span>${errorMsg}</span>
                <#--                <span>预分组失败，请重新核对数据</span>-->
            </div>
        </div>
    </div>
</#if>
</body>
</html>
