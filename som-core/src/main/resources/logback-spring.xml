<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE configuration>
<configuration>
    <include resource="org/springframework/boot/logging/logback/defaults.xml"/>
    <include resource="org/springframework/boot/logging/logback/console-appender.xml"/>
    <!--应用名称-->
    <property name="APP_NAME" value="som-core"/>
    <!-- 清单保存日志名称 -->
    <property name="SETTLE_LIST_NAME" value="settle_list_upload"/>
    <!-- 接口数据保存日志名称 -->
    <property name="WEBSERVICE_LOG_NAME" value="webservice"/>
    <!--日志文件保存路径-->
    <property name="LOG_FILE_PATH" value="${LOG_FILE:-${LOG_PATH:-${LOG_TEMP:-${java.io.tmpdir:-/tmp}}}/logs}"/>
    <contextName>${APP_NAME}</contextName>
    <!--每天记录日志到文件appender-->
    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOG_FILE_PATH}/${APP_NAME}-%d{yyyy-MM-dd}.log</fileNamePattern>
            <maxHistory>30</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>${FILE_LOG_PATTERN}</pattern>
        </encoder>
    </appender>
    <!--输出到logstash的appender-->
    <appender name="LOGSTASH" class="net.logstash.logback.appender.LogstashTcpSocketAppender">
        <destination>localhost:4560</destination>
        <encoder charset="UTF-8" class="net.logstash.logback.encoder.LogstashEncoder"/>
    </appender>

    <!-- 清单输出 -->
    <appender name="SETTLE_LIST" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOG_FILE_PATH}/${SETTLE_LIST_NAME}-%d{yyyy-MM-dd}.log</fileNamePattern>
            <maxHistory>30</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>${FILE_LOG_PATTERN}</pattern>
        </encoder>
    </appender>

    <!-- webservice 输出 -->
    <appender name="WEBSERVICE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOG_FILE_PATH}/${WEBSERVICE_LOG_NAME}-%d{yyyy-MM-dd}.log</fileNamePattern>
            <maxHistory>30</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>${FILE_LOG_PATTERN}</pattern>
        </encoder>
    </appender>

    <!-- 设置清单上传日志开始 -->
    <logger name="com.my.som.util.SettleListUploadUtil" level="INFO" addtivity="false">
      <appender-ref ref="SETTLE_LIST" />
    </logger>

    <logger name="com.my.som.util.HttpRequestUtil" level="INFO" addtivity="false">
      <appender-ref ref="SETTLE_LIST" />
    </logger>

    <logger name="com.my.som.service.listManagement.impl.ListUploadServiceImpl" level="INFO" addtivity="false">
      <appender-ref ref="SETTLE_LIST" />
    </logger>
    <!-- 设置清单上传日志结束 -->

    <!-- 设置webservice日志开始 -->
    <logger name="com.my.som.service.webservice.impl.DataAcquisitionDipInterfaceServiceImpl" level="INFO" addtivity="false">
      <appender-ref ref="WEBSERVICE" />
    </logger>

    <logger name="com.my.som.service.webservice.impl.SomDataAcquisitionInterfaceServiceImpl" level="INFO" addtivity="false">
      <appender-ref ref="WEBSERVICE" />
    </logger>
    <!-- 设置webservice日志结束 -->


    <root level="INFO">
        <appender-ref ref="CONSOLE"/>
        <appender-ref ref="FILE"/>
        <appender-ref ref="LOGSTASH"/>
    </root>
</configuration>
