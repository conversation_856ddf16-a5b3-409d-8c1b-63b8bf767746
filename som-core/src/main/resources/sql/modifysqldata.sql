-- 注意事项：请先更新som_drg_sco表的四个字段，首先添加som_drg_gen_cfg的ym字段格式yyyyMM --
   UPDATE som_drg_sco t1
    JOIN (
    SELECT
    a.SETTLE_LIST_ID,
    a.price,
    count( 1 ) AS drgInGroupMedcasVal,
    IFNULL( ROUND( SUM( a.ipt_sumfee ), 4 ), 0 ) AS sumfee ,
    IFNULL( ROUND( SUM( a.totl_sco * a.price ), 2 ), 0 ) AS forecastAmount,
    ROUND( IFNULL( SUM( a.totl_sco *a.price), 0 ) - IFNULL( SUM( a.ipt_sumfee ), 0 ), 4 ) AS forecastAmountDiff,
    IFNULL( ROUND( IFNULL( SUM( a.ipt_sumfee ), 0 ) / NULLIF( SUM( a.totl_sco *a.price), 0 ), 2 ), 0 ) AS oeVal
    FROM
    (
    SELECT
    a.drg_codg,
    a.drg_NAME,
    a.dscg_caty_codg_inhosp,
    a.`NAME`,
    a.PATIENT_ID,
    a.SETTLE_LIST_ID,
    a.ipt_sumfee,
    a.drugfee,
    a.mcs_fee,
    b.dise_type,
       b.totl_sco,
    a.main_diag_dise_codg,
    a.main_diag_dise_name,
    b.price
    FROM
    som_drg_grp_info a
    LEFT JOIN (
    SELECT
    a.SETTLE_LIST_ID,
    a.dise_type,
    a.totl_sco,
       CASE
                WHEN a.insu_type = 1 THEN ifnull(b.czPrice,y.czPrice)
                WHEN a.insu_type =  2 THEN ifnull(b.cxPrice,y.cxPrice)
                ELSE ifnull(b.price,y.price) END AS price

    FROM
    som_drg_sco a
    Left JOIN (
    SELECT
    ym,
    MAX( CASE WHEN `KEY` = 'CX_PRICE' THEN `VALUE` ELSE NULL END ) AS cxPrice,
    MAX( CASE WHEN `KEY` = 'CZ_PRICE' THEN `VALUE` ELSE NULL END ) AS czPrice,
    MAX( CASE WHEN `KEY` = 'PRICE' THEN `VALUE` ELSE NULL END ) AS price
    FROM
    som_drg_gen_cfg
    WHERE
    TYPE = 'PREDICTED_PRICE'
    GROUP BY
    TYPE ,ym
    ) b  on
    REPLACE(SUBSTR(a.ym, 1, 7), '-', '') = b.ym
    CROSS JOIN
             (SELECT MAX(CASE WHEN `KEY` = 'CX_PRICE' THEN `VALUE` ELSE NULL END) AS cxPrice,
                     MAX(CASE WHEN `KEY` = 'CZ_PRICE' THEN `VALUE` ELSE NULL END) AS czPrice,
                     MAX(CASE WHEN `KEY` = 'PRICE' THEN `VALUE` ELSE NULL END)    AS price
              FROM som_drg_gen_cfg
              WHERE TYPE = 'PREDICTED_PRICE'
                and ym = ''
              GROUP BY TYPE
    ) y
    ) b ON a.SETTLE_LIST_ID = b.SETTLE_LIST_ID
    WHERE
    a.drg_codg IS NOT NULL
    ) a

    LEFT JOIN som_drg_grp_rcd b ON a.SETTLE_LIST_ID = b.SETTLE_LIST_ID
    GROUP BY
    a.SETTLE_LIST_ID,
    a.price
     ) t2 ON t1.SETTLE_LIST_ID = t2.SETTLE_LIST_ID
    SET t1.forecast_fee = t2.forecastAmount,
        t1.price = t2.price,
        t1.profitloss = t2.forecastAmountDiff,
        t1.sumfee = t2.sumfee




    -- 注意事项：请先更新som_dip_sco表的四个字段，首先添加som_dip_gen_cfg的ym字段格式yyyyMM --
       -- 注意事项：请先更新som_drg_sco表的四个字段，首先添加som_drg_gen_cfg的ym字段格式yyyyMM --
          UPDATE som_drg_sco t1
           JOIN (
           SELECT
           a.SETTLE_LIST_ID,
           a.price,
           count( 1 ) AS drgInGroupMedcasVal,
           IFNULL( ROUND( SUM( a.ipt_sumfee ), 4 ), 0 ) AS sumfee ,
           IFNULL( ROUND( SUM( a.totl_sco * a.price ), 2 ), 0 ) AS forecastAmount,
           ROUND( IFNULL( SUM( a.totl_sco *a.price), 0 ) - IFNULL( SUM( a.ipt_sumfee ), 0 ), 4 ) AS forecastAmountDiff,
           IFNULL( ROUND( IFNULL( SUM( a.ipt_sumfee ), 0 ) / NULLIF( SUM( a.totl_sco *a.price), 0 ), 2 ), 0 ) AS oeVal
           FROM
           (
           SELECT
           a.drg_codg,
           a.drg_NAME,
           a.dscg_caty_codg_inhosp,
           a.`NAME`,
           a.PATIENT_ID,
           a.SETTLE_LIST_ID,
           a.ipt_sumfee,
           a.drugfee,
           a.mcs_fee,
           b.dise_type,
              b.totl_sco,
           a.main_diag_dise_codg,
           a.main_diag_dise_name,
           b.price
           FROM
           som_drg_grp_info a
           LEFT JOIN (
           SELECT
           a.SETTLE_LIST_ID,
           a.dise_type,
           a.totl_sco,
              CASE
                       WHEN a.insu_type = 1 THEN ifnull(b.czPrice,y.czPrice)
                       WHEN a.insu_type =  2 THEN ifnull(b.cxPrice,y.cxPrice)
                       ELSE ifnull(b.price,y.price) END AS price

           FROM
           som_drg_sco a
           Left JOIN (
           SELECT
           ym,
           MAX( CASE WHEN `KEY` = 'CX_PRICE' THEN `VALUE` ELSE NULL END ) AS cxPrice,
           MAX( CASE WHEN `KEY` = 'CZ_PRICE' THEN `VALUE` ELSE NULL END ) AS czPrice,
           MAX( CASE WHEN `KEY` = 'PRICE' THEN `VALUE` ELSE NULL END ) AS price
           FROM
           som_drg_gen_cfg
           WHERE
           TYPE = 'PREDICTED_PRICE'
           GROUP BY
           TYPE ,ym
           ) b  on
           REPLACE(SUBSTR(a.ym, 1, 7), '-', '') = b.ym
           CROSS JOIN
                    (SELECT MAX(CASE WHEN `KEY` = 'CX_PRICE' THEN `VALUE` ELSE NULL END) AS cxPrice,
                            MAX(CASE WHEN `KEY` = 'CZ_PRICE' THEN `VALUE` ELSE NULL END) AS czPrice,
                            MAX(CASE WHEN `KEY` = 'PRICE' THEN `VALUE` ELSE NULL END)    AS price
                     FROM som_drg_gen_cfg
                     WHERE TYPE = 'PREDICTED_PRICE'
                       and ym = ''
                     GROUP BY TYPE
           ) y
           ) b ON a.SETTLE_LIST_ID = b.SETTLE_LIST_ID
           WHERE
           a.drg_codg IS NOT NULL
           ) a

           LEFT JOIN som_drg_grp_rcd b ON a.SETTLE_LIST_ID = b.SETTLE_LIST_ID
           GROUP BY
           a.SETTLE_LIST_ID,
           a.price
            ) t2 ON t1.SETTLE_LIST_ID = t2.SETTLE_LIST_ID
           SET t1.forecast_fee = t2.forecastAmount,
               t1.price = t2.price,
               t1.profitloss = t2.forecastAmountDiff,
               t1.sumfee = t2.sumfee




           -- 注意事项：请先更新som_dip_sco表的四个字段，首先添加som_dip_gen_cfg的ym字段格式yyyyMM --
              UPDATE som_dip_sco t1
               JOIN (
               SELECT
               a.SETTLE_LIST_ID,
               a.price,
               IFNULL( ROUND( SUM( a.ipt_sumfee ), 4 ), 0 ) AS sumfee ,
               IFNULL( ROUND( SUM( a.totl_sco * a.price ), 2 ), 0 ) AS forecastAmount,
               ROUND( IFNULL( SUM( a.totl_sco *a.price), 0 ) - IFNULL( SUM( a.ipt_sumfee ), 0 ), 4 ) AS forecastAmountDiff
               FROM
               (
               SELECT
               a.SETTLE_LIST_ID,
               a.ipt_sumfee,
               b.dise_type,
               b.totl_sco,
               b.price
               FROM
               som_dip_grp_info a
               LEFT JOIN (
               SELECT
               a.SETTLE_LIST_ID,
               a.dise_type,
               a.totl_sco,
                  CASE
                           WHEN a.insu_type = 1 THEN ifnull(b.czPrice,y.czPrice)
                           WHEN a.insu_type =  2 THEN ifnull(b.cxPrice,y.cxPrice)
                           ELSE ifnull(b.price,y.price) END AS price

               FROM
               som_dip_sco a
               Left JOIN (
               SELECT
               ym,
               MAX( CASE WHEN `KEY` = 'CX_PRICE' THEN `VALUE` ELSE NULL END ) AS cxPrice,
               MAX( CASE WHEN `KEY` = 'CZ_PRICE' THEN `VALUE` ELSE NULL END ) AS czPrice,
               MAX( CASE WHEN `KEY` = 'PRICE' THEN `VALUE` ELSE NULL END ) AS price
               FROM
               som_dip_gen_cfg
               WHERE
               TYPE = 'PREDICTED_PRICE'
               GROUP BY
               TYPE ,ym
               ) b  on
               REPLACE(SUBSTR(a.ym, 1, 7), '-', '') = b.ym
               CROSS JOIN
                        (SELECT MAX(CASE WHEN `KEY` = 'CX_PRICE' THEN `VALUE` ELSE NULL END) AS cxPrice,
                                MAX(CASE WHEN `KEY` = 'CZ_PRICE' THEN `VALUE` ELSE NULL END) AS czPrice,
                                MAX(CASE WHEN `KEY` = 'PRICE' THEN `VALUE` ELSE NULL END)    AS price
                         FROM som_dip_gen_cfg
                         WHERE TYPE = 'PREDICTED_PRICE'
                           and ym = ''
                         GROUP BY TYPE
               ) y
               ) b ON a.SETTLE_LIST_ID = b.SETTLE_LIST_ID
               WHERE
               a.dip_codg IS NOT NULL
               ) a

               LEFT JOIN som_dip_grp_rcd b ON a.SETTLE_LIST_ID = b.SETTLE_LIST_ID
               GROUP BY
               a.SETTLE_LIST_ID,
               a.price
                ) t2 ON t1.SETTLE_LIST_ID = t2.SETTLE_LIST_ID
               SET t1.forecast_fee = t2.forecastAmount,
                   t1.price = t2.price,
                   t1.profitloss = t2.forecastAmountDiff,
                   t1.sumfee = t2.sumfee








