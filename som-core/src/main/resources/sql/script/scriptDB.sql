-- 在对应不同省份的医疗机构时 需要重新设计脚本质检的表（som_chain_scrpit），而这个表的数据需要从
-- som_chain_script_all 表中抽取  抽取后 将som_chain 表的 基础质控 和内涵质控 清空
-- 同时设置 som_chain_script  启动状态为0 在前端 重启即可
-- update lihongxiang 20240924 begin

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for som_chain_script
-- ----------------------------
DROP TABLE IF EXISTS `som_chain_script`;
CREATE TABLE `som_chain_script`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `application_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '应用名称',
  `script_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '脚本ID',
  `script_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '脚本名称',
  `script_data` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '脚本数据',
  `script_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '脚本类型',
  `script_language` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '脚本语言',
  `create_time` timestamp NULL DEFAULT NULL COMMENT '创建时间',
  `mean` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '检测对象含义 ',
  `action_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '质检的作用域',
  `time_type` tinyint NULL DEFAULT NULL COMMENT '质检的时间线 0:事前,1:事后',
  `script_enable` tinyint NULL DEFAULT NULL COMMENT '启用标志0无效，1有效',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 153 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;


INSERT INTO
`som_chain_script`  -- 目标表
( `id`, `application_name`, `script_id`, `script_name`, `script_data`, `script_type`, `script_language`, `create_time`, `mean`, `action_type`, `time_type`, `script_enable` )
SELECT
`id`,
`application_name`,
`script_id`,
`script_name`,
`script_data`,
`script_type`,
`script_language`,
`create_time`,
`mean`,
`action_type`,
`time_type`,
`script_enable`
FROM
`som_chain_script_all` -- 所有表
WHERE
  `region` LIKE '%四川%'; -- 改成对应的省份

SET FOREIGN_KEY_CHECKS = 1;
-- 四川
UPDATE som_chain SET el_data =
'THEN(CATCH(check_adm_time_basic).DO(settleValidateHandleExceptionNode),CATCH(check_oprn_oprt_time_after_basic).DO(settleValidateHandleExceptionNode),CATCH(check_scs_cutd_ward_type_after_basic).DO(settleValidateHandleExceptionNode),CATCH(check_otp_wm_dise_code_after_basic).DO(settleValidateHandleExceptionNode),CATCH(check_otp_tcm_dise_after_basic).DO(settleValidateHandleExceptionNode),CATCH(check_dscg_time_basic).DO(settleValidateHandleExceptionNode),CATCH(check_oper_dr_name_basic).DO(settleValidateHandleExceptionNode),CATCH(check_oper_dr_code_basic).DO(settleValidateHandleExceptionNode),CATCH(check_anst_way_after_basic).DO(settleValidateHandleExceptionNode),CATCH(check_hi_paymtd_after_basic).DO(settleValidateHandleExceptionNode),CATCH(check_lyfs_basic).DO(settleValidateHandleExceptionNode),CATCH(check_medins_fill_dept_after_basic).DO(settleValidateHandleExceptionNode),CATCH(check_medins_fill_psn_after_basic).DO(settleValidateHandleExceptionNode),CATCH(check_refl_dept_dept_after_basic).DO(settleValidateHandleExceptionNode),CATCH(check_setl_date_after_basic).DO(settleValidateHandleExceptionNode),CATCH(check_diag_code_basic).DO(settleValidateHandleExceptionNode),CATCH(check_nwb_adm_type_after_basic).DO(settleValidateHandleExceptionNode),CATCH(check_nwb_bir_wt_after_basic).DO(settleValidateHandleExceptionNode),CATCH(check_age_basic).DO(settleValidateHandleExceptionNode),CATCH(check_coner_addr_basic).DO(settleValidateHandleExceptionNode),CATCH(check_coner_tel_basic).DO(settleValidateHandleExceptionNode),CATCH(check_hi_no_basic).DO(settleValidateHandleExceptionNode),CATCH(check_medcasno_basic).DO(settleValidateHandleExceptionNode),CATCH(check_ntly_basic).DO(settleValidateHandleExceptionNode),CATCH(check_nwb_adm_wt_basic).DO(settleValidateHandleExceptionNode),CATCH(check_nwb_bir_wt_basic).DO(settleValidateHandleExceptionNode),CATCH(check_nwb_age_basic).DO(settleValidateHandleExceptionNode),CATCH(check_patn_rlts_basic).DO(settleValidateHandleExceptionNode),CATCH(check_pjdm_basic).DO(settleValidateHandleExceptionNode),CATCH(check_pjhm_basic).DO(settleValidateHandleExceptionNode),CATCH(check_prfs_basic).DO(settleValidateHandleExceptionNode),CATCH(check_ywlsh_basic).DO(settleValidateHandleExceptionNode),CATCH(check_zrhsdm_basic).DO(settleValidateHandleExceptionNode),CATCH(check_zrhsxm_basic).DO(settleValidateHandleExceptionNode),CATCH(check_zzysdm_basic).DO(settleValidateHandleExceptionNode),CATCH(check_zzysxm_basic).DO(settleValidateHandleExceptionNode),CATCH(check_curr_addr_after_basic).DO(settleValidateHandleExceptionNode),CATCH(check_nwb_age_after_basic).DO(settleValidateHandleExceptionNode),CATCH(check_trt_type_after_basic).DO(settleValidateHandleExceptionNode),CATCH(check_emp_addr_after_basic).DO(settleValidateHandleExceptionNode),CATCH(check_emp_name_after_basic).DO(settleValidateHandleExceptionNode),CATCH(check_emp_tel_after_basic).DO(settleValidateHandleExceptionNode),CATCH(check_mdeic_type_after_basic).DO(settleValidateHandleExceptionNode),CATCH(check_pos_code_after_basic).DO(settleValidateHandleExceptionNode),CATCH(check_anst_dr_code_basic).DO(settleValidateHandleExceptionNode),CATCH(check_anst_dr_name_basic).DO(settleValidateHandleExceptionNode),CATCH(check_dscg_caty_code_after_basic).DO(settleValidateHandleExceptionNode),CATCH(check_days_rinp_flag_31_after_basic).DO(settleValidateHandleExceptionNode),CATCH(check_bld_cat_after_basic).DO(settleValidateHandleExceptionNode),CATCH(check_adm_way_after_basic).DO(settleValidateHandleExceptionNode))'
where chain_name= 'GlobalExecSettleValidateBasicChain';
UPDATE som_chain SET el_data =
'THEN(CATCH(check_dscg_time_depth).DO(settleValidateHandleExceptionNode),CATCH(check_bld_unt_after_depth).DO(settleValidateHandleExceptionNode),CATCH(check_bld_amt_after_depth).DO(settleValidateHandleExceptionNode),CATCH(check_tcm_diagnosis_after_depth).DO(settleValidateHandleExceptionNode),CATCH(check_scs_cutd_sum_dura_after_depth).DO(settleValidateHandleExceptionNode),CATCH(check_ipt_days_after_depth).DO(settleValidateHandleExceptionNode),CATCH(check_oprn_merge_comb_after_depth).DO(settleValidateHandleExceptionNode),CATCH(check_wm_adm_cond_after_depth).DO(settleValidateHandleExceptionNode),CATCH(check_dscg_wm_other_dise_new_age_depth).DO(settleValidateHandleExceptionNode),CATCH(check_diag_merge_comb_after_depth).DO(settleValidateHandleExceptionNode),CATCH(check_oprn_oprt_time_after_depth).DO(settleValidateHandleExceptionNode),CATCH(check_oprn_code_depth).DO(settleValidateHandleExceptionNode),CATCH(check_anst_time_after_depth).DO(settleValidateHandleExceptionNode),CATCH(check_setl_date_after_depth).DO(settleValidateHandleExceptionNode),CATCH(check_diag_code_depth).DO(settleValidateHandleExceptionNode),CATCH(check_nwb_adm_wt_depth).DO(settleValidateHandleExceptionNode),CATCH(check_nwb_age_depth).DO(settleValidateHandleExceptionNode),CATCH(check_nwb_bir_wt_depth).DO(settleValidateHandleExceptionNode),CATCH(check_otp_wm_dise_code_after_depth).DO(settleValidateHandleExceptionNode),CATCH(check_brn_damg_coma_dura_after_depth).DO(settleValidateHandleExceptionNode),CATCH(check_emp_addr_after_depth).DO(settleValidateHandleExceptionNode),CATCH(check_emp_name_after_depth).DO(settleValidateHandleExceptionNode),CATCH(check_emp_tel_after_depth).DO(settleValidateHandleExceptionNode),CATCH(check_pos_code_after_depth).DO(settleValidateHandleExceptionNode),CATCH(check_vent_used_dura_after_depth).DO(settleValidateHandleExceptionNode))'
 where chain_name= 'GlobalExecSettleValidateDepthChain';

-- 湖北
UPDATE som_chain SET el_data =
                         'THEN(CATCH(check_adm_time_basic).DO(settleValidateHandleExceptionNode),CATCH(check_adm_way_after_basic).DO(settleValidateHandleExceptionNode),CATCH(check_age_basic).DO(settleValidateHandleExceptionNode),CATCH(check_anst_way_after_basic).DO(settleValidateHandleExceptionNode),CATCH(check_birthday_basic).DO(settleValidateHandleExceptionNode),CATCH(check_diag_code_basic).DO(settleValidateHandleExceptionNode),CATCH(check_dscg_time_basic).DO(settleValidateHandleExceptionNode),CATCH(check_lyfs_basic).DO(settleValidateHandleExceptionNode),CATCH(check_nwb_adm_wt_basic).DO(settleValidateHandleExceptionNode),CATCH(check_nwb_age_after_basic).DO(settleValidateHandleExceptionNode),CATCH(check_nwb_age_basic).DO(settleValidateHandleExceptionNode),CATCH(check_nwb_bir_wt_after_basic).DO(settleValidateHandleExceptionNode),CATCH(check_nwb_bir_wt_basic).DO(settleValidateHandleExceptionNode),CATCH(check_oper_dr_code_basic).DO(settleValidateHandleExceptionNode),CATCH(check_oper_dr_name_basic).DO(settleValidateHandleExceptionNode),CATCH(check_oprn_oprt_time_after_basic).DO(settleValidateHandleExceptionNode),CATCH(check_otp_tcm_dise_after_basic).DO(settleValidateHandleExceptionNode),CATCH(check_refl_dept_dept_after_basic).DO(settleValidateHandleExceptionNode),CATCH(check_trt_type_after_basic).DO(settleValidateHandleExceptionNode),CATCH(check_sex_basic).DO(settleValidateHandleExceptionNode))'
where chain_name= 'GlobalExecSettleValidateBasicChain';
UPDATE som_chain SET el_data =
                         'THEN(CATCH(check_anst_time_hubei_after_depth).DO(settleValidateHandleExceptionNode),CATCH(check_birthday_depth).DO(settleValidateHandleExceptionNode),CATCH(check_diag_code_depth).DO(settleValidateHandleExceptionNode),CATCH(check_diag_merge_comb_after_depth).DO(settleValidateHandleExceptionNode),CATCH(check_dscg_time_depth).DO(settleValidateHandleExceptionNode),CATCH(check_dscg_wm_other_dise_new_age_depth).DO(settleValidateHandleExceptionNode),CATCH(check_ipt_days_after_depth).DO(settleValidateHandleExceptionNode),CATCH(check_nwb_adm_wt_depth).DO(settleValidateHandleExceptionNode),CATCH(check_nwb_bir_wt_depth).DO(settleValidateHandleExceptionNode),CATCH(check_oprn_code_depth).DO(settleValidateHandleExceptionNode),CATCH(check_oprn_merge_comb_after_depth).DO(settleValidateHandleExceptionNode),CATCH(check_oprn_oprt_time_hubei_after_depth).DO(settleValidateHandleExceptionNode),CATCH(check_otp_wm_dise_code_after_depth).DO(settleValidateHandleExceptionNode),CATCH(check_tcm_diagnosis_after_depth).DO(settleValidateHandleExceptionNode))'
where chain_name= 'GlobalExecSettleValidateDepthChain';
-- update lihongxiang 20240924 begin


-- update lihongxiang 20240923 begin
-- som_icd_codg 新增医保手术操作代码

INSERT INTO `som_icd_codg` (`id`, `icd_type`, `icd_codg_ver`, `code`, `name`, `enab_flag_codg`, `trt_type`, `is_gray`) VALUES (null, 'ICD-9', '10', '01.5900x053', '经皮脑病损冷冻消融术', '1', NULL, '0');
INSERT INTO `som_icd_codg` (`id`, `icd_type`, `icd_codg_ver`, `code`, `name`, `enab_flag_codg`, `trt_type`, `is_gray`) VALUES (null, 'ICD-9', '10', '01.5900x054', '经皮脑病损激光消融术(映射)', '1', NULL, '0');
INSERT INTO `som_icd_codg` (`id`, `icd_type`, `icd_codg_ver`, `code`, `name`, `enab_flag_codg`, `trt_type`, `is_gray`) VALUES (null, 'ICD-9', '10', '04.5x00x025', '异体神经移植修复臂丛神经术', '1', NULL, '0');
INSERT INTO `som_icd_codg` (`id`, `icd_type`, `icd_codg_ver`, `code`, `name`, `enab_flag_codg`, `trt_type`, `is_gray`) VALUES (null, 'ICD-9', '10', '04.5x00x026', '异体神经移植修复肌皮神经术', '1', NULL, '0');
INSERT INTO `som_icd_codg` (`id`, `icd_type`, `icd_codg_ver`, `code`, `name`, `enab_flag_codg`, `trt_type`, `is_gray`) VALUES (null, 'ICD-9', '10', '04.5x00x027', '异体神经移植修复正中神经术', '1', NULL, '0');
INSERT INTO `som_icd_codg` (`id`, `icd_type`, `icd_codg_ver`, `code`, `name`, `enab_flag_codg`, `trt_type`, `is_gray`) VALUES (null, 'ICD-9', '10', '04.5x00x028', '异体神经移植修复桡神经神经术', '1', NULL, '0');
INSERT INTO `som_icd_codg` (`id`, `icd_type`, `icd_codg_ver`, `code`, `name`, `enab_flag_codg`, `trt_type`, `is_gray`) VALUES (null, 'ICD-9', '10', '04.5x00x029', '异体神经移植修复尺神经术', '1', NULL, '0');
INSERT INTO `som_icd_codg` (`id`, `icd_type`, `icd_codg_ver`, `code`, `name`, `enab_flag_codg`, `trt_type`, `is_gray`) VALUES (null, 'ICD-9', '10', '04.5x00x030', '异体神经移植修复坐骨神经术', '1', NULL, '0');
INSERT INTO `som_icd_codg` (`id`, `icd_type`, `icd_codg_ver`, `code`, `name`, `enab_flag_codg`, `trt_type`, `is_gray`) VALUES (null, 'ICD-9', '10', '04.5x00x031', '异体神经移植修复股神经术', '1', NULL, '0');
INSERT INTO `som_icd_codg` (`id`, `icd_type`, `icd_codg_ver`, `code`, `name`, `enab_flag_codg`, `trt_type`, `is_gray`) VALUES (null, 'ICD-9', '10', '04.5x00x032', '异体神经移植修复胫神经术', '1', NULL, '0');
INSERT INTO `som_icd_codg` (`id`, `icd_type`, `icd_codg_ver`, `code`, `name`, `enab_flag_codg`, `trt_type`, `is_gray`) VALUES (null, 'ICD-9', '10', '04.5x00x033', '异体神经移植修复腓总神经术', '1', NULL, '0');
INSERT INTO `som_icd_codg` (`id`, `icd_type`, `icd_codg_ver`, `code`, `name`, `enab_flag_codg`, `trt_type`, `is_gray`) VALUES (null, 'ICD-9', '10', '06.3100x002', '经皮甲状腺病损微波消融术', '1', NULL, '0');
INSERT INTO `som_icd_codg` (`id`, `icd_type`, `icd_codg_ver`, `code`, `name`, `enab_flag_codg`, `trt_type`, `is_gray`) VALUES (null, 'ICD-9', '10', '06.9900x003', '经皮甲状旁腺病损微波消融术', '1', NULL, '0');
INSERT INTO `som_icd_codg` (`id`, `icd_type`, `icd_codg_ver`, `code`, `name`, `enab_flag_codg`, `trt_type`, `is_gray`) VALUES (null, 'ICD-9', '10', '07.2100x002', '经皮肾上腺病损纳米刀消融术', '1', NULL, '0');
INSERT INTO `som_icd_codg` (`id`, `icd_type`, `icd_codg_ver`, `code`, `name`, `enab_flag_codg`, `trt_type`, `is_gray`) VALUES (null, 'ICD-9', '10', '33.5000x001', '自体肺移植术', '1', NULL, '0');
INSERT INTO `som_icd_codg` (`id`, `icd_type`, `icd_codg_ver`, `code`, `name`, `enab_flag_codg`, `trt_type`, `is_gray`) VALUES (null, 'ICD-9', '10', '37.5100x001', '自体心脏移植', '1', NULL, '0');
INSERT INTO `som_icd_codg` (`id`, `icd_type`, `icd_codg_ver`, `code`, `name`, `enab_flag_codg`, `trt_type`, `is_gray`) VALUES (null, 'ICD-9', '10', '39.6500x001', '体外膜肺氧合[ECMO]安装术', '1', NULL, '0');
INSERT INTO `som_icd_codg` (`id`, `icd_type`, `icd_codg_ver`, `code`, `name`, `enab_flag_codg`, `trt_type`, `is_gray`) VALUES (null, 'ICD-9', '10', '39.6500x002', '体外膜肺氧合[ECMO]撤离术', '1', NULL, '0');
INSERT INTO `som_icd_codg` (`id`, `icd_type`, `icd_codg_ver`, `code`, `name`, `enab_flag_codg`, `trt_type`, `is_gray`) VALUES (null, 'ICD-9', '10', '41.4200x005', '经皮脾病损射频消融术', '1', NULL, '0');
INSERT INTO `som_icd_codg` (`id`, `icd_type`, `icd_codg_ver`, `code`, `name`, `enab_flag_codg`, `trt_type`, `is_gray`) VALUES (null, 'ICD-9', '10', '43.4100x026', '内镜下胃病损射频消融术', '1', NULL, '0');
INSERT INTO `som_icd_codg` (`id`, `icd_type`, `icd_codg_ver`, `code`, `name`, `enab_flag_codg`, `trt_type`, `is_gray`) VALUES (null, 'ICD-9', '10', '46.9700x001', '自体小肠移植术', '1', NULL, '0');
INSERT INTO `som_icd_codg` (`id`, `icd_type`, `icd_codg_ver`, `code`, `name`, `enab_flag_codg`, `trt_type`, `is_gray`) VALUES (null, 'ICD-9', '10', '50.2400x001', '肝病损聚焦超声消融术', '1', NULL, '0');
INSERT INTO `som_icd_codg` (`id`, `icd_type`, `icd_codg_ver`, `code`, `name`, `enab_flag_codg`, `trt_type`, `is_gray`) VALUES (null, 'ICD-9', '10', '50.2400x002', '经皮肝病损纳米刀消融术', '1', NULL, '0');
INSERT INTO `som_icd_codg` (`id`, `icd_type`, `icd_codg_ver`, `code`, `name`, `enab_flag_codg`, `trt_type`, `is_gray`) VALUES (null, 'ICD-9', '10', '50.5100x002', '自体肝移植术', '1', NULL, '0');
INSERT INTO `som_icd_codg` (`id`, `icd_type`, `icd_codg_ver`, `code`, `name`, `enab_flag_codg`, `trt_type`, `is_gray`) VALUES (null, 'ICD-9', '10', '52.2100x001', '腹腔镜下胰腺病损射频消融术', '1', NULL, '0');
INSERT INTO `som_icd_codg` (`id`, `icd_type`, `icd_codg_ver`, `code`, `name`, `enab_flag_codg`, `trt_type`, `is_gray`) VALUES (null, 'ICD-9', '10', '52.2100x002', '腹腔镜下胰腺病损微波消融术', '1', NULL, '0');
INSERT INTO `som_icd_codg` (`id`, `icd_type`, `icd_codg_ver`, `code`, `name`, `enab_flag_codg`, `trt_type`, `is_gray`) VALUES (null, 'ICD-9', '10', '52.2100x003', '腹腔镜下胰腺病损冷冻消融术', '1', NULL, '0');
INSERT INTO `som_icd_codg` (`id`, `icd_type`, `icd_codg_ver`, `code`, `name`, `enab_flag_codg`, `trt_type`, `is_gray`) VALUES (null, 'ICD-9', '10', '52.2100x004', '腹腔镜下胰腺病损纳米刀消融术', '1', NULL, '0');
INSERT INTO `som_icd_codg` (`id`, `icd_type`, `icd_codg_ver`, `code`, `name`, `enab_flag_codg`, `trt_type`, `is_gray`) VALUES (null, 'ICD-9', '10', '52.2200x002', '术中胰腺病损微波消融术', '1', NULL, '0');
INSERT INTO `som_icd_codg` (`id`, `icd_type`, `icd_codg_ver`, `code`, `name`, `enab_flag_codg`, `trt_type`, `is_gray`) VALUES (null, 'ICD-9', '10', '52.2200x003', '术中胰腺病损冷冻消融术', '1', NULL, '0');
INSERT INTO `som_icd_codg` (`id`, `icd_type`, `icd_codg_ver`, `code`, `name`, `enab_flag_codg`, `trt_type`, `is_gray`) VALUES (null, 'ICD-9', '10', '52.2200x005', '经皮胰腺病损射频消融术', '1', NULL, '0');
INSERT INTO `som_icd_codg` (`id`, `icd_type`, `icd_codg_ver`, `code`, `name`, `enab_flag_codg`, `trt_type`, `is_gray`) VALUES (null, 'ICD-9', '10', '52.2200x006', '经皮胰腺病损微波消融术', '1', NULL, '0');
INSERT INTO `som_icd_codg` (`id`, `icd_type`, `icd_codg_ver`, `code`, `name`, `enab_flag_codg`, `trt_type`, `is_gray`) VALUES (null, 'ICD-9', '10', '52.2200x007', '经皮胰腺病损冷冻消融术', '1', NULL, '0');
INSERT INTO `som_icd_codg` (`id`, `icd_type`, `icd_codg_ver`, `code`, `name`, `enab_flag_codg`, `trt_type`, `is_gray`) VALUES (null, 'ICD-9', '10', '52.2200x008', '胰腺病损聚焦超声消融术', '1', NULL, '0');
INSERT INTO `som_icd_codg` (`id`, `icd_type`, `icd_codg_ver`, `code`, `name`, `enab_flag_codg`, `trt_type`, `is_gray`) VALUES (null, 'ICD-9', '10', '52.2200x009', '经皮胰腺病损纳米刀消融术', '1', NULL, '0');
INSERT INTO `som_icd_codg` (`id`, `icd_type`, `icd_codg_ver`, `code`, `name`, `enab_flag_codg`, `trt_type`, `is_gray`) VALUES (null, 'ICD-9', '10', '52.8400x001', '胰岛细胞自体移植', '1', NULL, '0');
INSERT INTO `som_icd_codg` (`id`, `icd_type`, `icd_codg_ver`, `code`, `name`, `enab_flag_codg`, `trt_type`, `is_gray`) VALUES (null, 'ICD-9', '10', '52.8500x001', '胰岛细胞异体移植', '1', NULL, '0');
INSERT INTO `som_icd_codg` (`id`, `icd_type`, `icd_codg_ver`, `code`, `name`, `enab_flag_codg`, `trt_type`, `is_gray`) VALUES (null, 'ICD-9', '10', '52.8600x001', '胰岛细胞移植', '1', NULL, '0');
INSERT INTO `som_icd_codg` (`id`, `icd_type`, `icd_codg_ver`, `code`, `name`, `enab_flag_codg`, `trt_type`, `is_gray`) VALUES (null, 'ICD-9', '10', '54.4x00x055', '经皮腹膜后病损纳米刀消融术', '1', NULL, '0');
INSERT INTO `som_icd_codg` (`id`, `icd_type`, `icd_codg_ver`, `code`, `name`, `enab_flag_codg`, `trt_type`, `is_gray`) VALUES (null, 'ICD-9', '10', '55.3200x002', '术中肾病损纳米刀消融术', '1', NULL, '0');
INSERT INTO `som_icd_codg` (`id`, `icd_type`, `icd_codg_ver`, `code`, `name`, `enab_flag_codg`, `trt_type`, `is_gray`) VALUES (null, 'ICD-9', '10', '55.3300x001', '经皮肾病损纳米刀消融术', '1', NULL, '0');
INSERT INTO `som_icd_codg` (`id`, `icd_type`, `icd_codg_ver`, `code`, `name`, `enab_flag_codg`, `trt_type`, `is_gray`) VALUES (null, 'ICD-9', '10', '55.3400x002', '腹腔镜下肾病损纳米刀消融术', '1', NULL, '0');
INSERT INTO `som_icd_codg` (`id`, `icd_type`, `icd_codg_ver`, `code`, `name`, `enab_flag_codg`, `trt_type`, `is_gray`) VALUES (null, 'ICD-9', '10', '60.9900x001', '经皮前列腺病损纳米刀消融术', '1', NULL, '0');
INSERT INTO `som_icd_codg` (`id`, `icd_type`, `icd_codg_ver`, `code`, `name`, `enab_flag_codg`, `trt_type`, `is_gray`) VALUES (null, 'ICD-9', '10', '77.6100x013', '经皮肋骨病损纳米刀消融术', '1', NULL, '0');
INSERT INTO `som_icd_codg` (`id`, `icd_type`, `icd_codg_ver`, `code`, `name`, `enab_flag_codg`, `trt_type`, `is_gray`) VALUES (null, 'ICD-9', '10', '77.6900x059', '经皮椎骨病损射频消融术', '1', NULL, '0');
INSERT INTO `som_icd_codg` (`id`, `icd_type`, `icd_codg_ver`, `code`, `name`, `enab_flag_codg`, `trt_type`, `is_gray`) VALUES (null, 'ICD-9', '10', '77.6900x060', '经皮椎骨病损微波消融术', '1', NULL, '0');
INSERT INTO `som_icd_codg` (`id`, `icd_type`, `icd_codg_ver`, `code`, `name`, `enab_flag_codg`, `trt_type`, `is_gray`) VALUES (null, 'ICD-9', '10', '77.6900x061', '经皮椎骨病损冷冻消融术', '1', NULL, '0');
INSERT INTO `som_icd_codg` (`id`, `icd_type`, `icd_codg_ver`, `code`, `name`, `enab_flag_codg`, `trt_type`, `is_gray`) VALUES (null, 'ICD-9', '10', '77.6900x062', '经皮椎骨病损纳米刀消融术', '1', NULL, '0');
INSERT INTO `som_icd_codg` (`id`, `icd_type`, `icd_codg_ver`, `code`, `name`, `enab_flag_codg`, `trt_type`, `is_gray`) VALUES (null, 'ICD-9', '10', '77.6900x068', '经皮骼骨病损纳米刀消融术', '1', NULL, '0');
INSERT INTO `som_icd_codg` (`id`, `icd_type`, `icd_codg_ver`, `code`, `name`, `enab_flag_codg`, `trt_type`, `is_gray`) VALUES (null, 'ICD-9', '10', '77.6900x069', '经皮骶骨病损纳米刀消融术', '1', NULL, '0');
INSERT INTO `som_icd_codg` (`id`, `icd_type`, `icd_codg_ver`, `code`, `name`, `enab_flag_codg`, `trt_type`, `is_gray`) VALUES (null, 'ICD-9', '10', '78.0100x003', '异体肩胛骨移植术', '1', NULL, '0');
INSERT INTO `som_icd_codg` (`id`, `icd_type`, `icd_codg_ver`, `code`, `name`, `enab_flag_codg`, `trt_type`, `is_gray`) VALUES (null, 'ICD-9', '10', '78.0100x004', '异体锁骨移植术', '1', NULL, '0');
INSERT INTO `som_icd_codg` (`id`, `icd_type`, `icd_codg_ver`, `code`, `name`, `enab_flag_codg`, `trt_type`, `is_gray`) VALUES (null, 'ICD-9', '10', '78.0200x003', '异体肱骨上段半关节移植术', '1', NULL, '0');
INSERT INTO `som_icd_codg` (`id`, `icd_type`, `icd_codg_ver`, `code`, `name`, `enab_flag_codg`, `trt_type`, `is_gray`) VALUES (null, 'ICD-9', '10', '78.0200x004', '异体肱骨下段半关节移植术', '1', NULL, '0');
INSERT INTO `som_icd_codg` (`id`, `icd_type`, `icd_codg_ver`, `code`, `name`, `enab_flag_codg`, `trt_type`, `is_gray`) VALUES (null, 'ICD-9', '10', '78.0300x006', '异体桡骨移植术', '1', NULL, '0');
INSERT INTO `som_icd_codg` (`id`, `icd_type`, `icd_codg_ver`, `code`, `name`, `enab_flag_codg`, `trt_type`, `is_gray`) VALUES (null, 'ICD-9', '10', '78.0300x007', '异体尺骨移植术', '1', NULL, '0');
INSERT INTO `som_icd_codg` (`id`, `icd_type`, `icd_codg_ver`, `code`, `name`, `enab_flag_codg`, `trt_type`, `is_gray`) VALUES (null, 'ICD-9', '10', '78.0500x003', '异体股骨上段半关节移植术', '1', NULL, '0');
INSERT INTO `som_icd_codg` (`id`, `icd_type`, `icd_codg_ver`, `code`, `name`, `enab_flag_codg`, `trt_type`, `is_gray`) VALUES (null, 'ICD-9', '10', '78.0500x004', '异体股骨下段半关节移植术', '1', NULL, '0');
INSERT INTO `som_icd_codg` (`id`, `icd_type`, `icd_codg_ver`, `code`, `name`, `enab_flag_codg`, `trt_type`, `is_gray`) VALUES (null, 'ICD-9', '10', '78.0500x005', '异体股骨头移植术', '1', NULL, '0');
INSERT INTO `som_icd_codg` (`id`, `icd_type`, `icd_codg_ver`, `code`, `name`, `enab_flag_codg`, `trt_type`, `is_gray`) VALUES (null, 'ICD-9', '10', '78.0500x006', '异体股骨骨板移植术', '1', NULL, '0');
INSERT INTO `som_icd_codg` (`id`, `icd_type`, `icd_codg_ver`, `code`, `name`, `enab_flag_codg`, `trt_type`, `is_gray`) VALUES (null, 'ICD-9', '10', '78.0700x007', '异体胫骨上段半关节移植术', '1', NULL, '0');
INSERT INTO `som_icd_codg` (`id`, `icd_type`, `icd_codg_ver`, `code`, `name`, `enab_flag_codg`, `trt_type`, `is_gray`) VALUES (null, 'ICD-9', '10', '78.0700x008', '异体胫骨下段半关节移植术', '1', NULL, '0');
INSERT INTO `som_icd_codg` (`id`, `icd_type`, `icd_codg_ver`, `code`, `name`, `enab_flag_codg`, `trt_type`, `is_gray`) VALUES (null, 'ICD-9', '10', '78.0700x009', '异体腓骨移植术', '1', NULL, '0');
INSERT INTO `som_icd_codg` (`id`, `icd_type`, `icd_codg_ver`, `code`, `name`, `enab_flag_codg`, `trt_type`, `is_gray`) VALUES (null, 'ICD-9', '10', '78.0900x025', '异体半骨盆移植术', '1', NULL, '0');
INSERT INTO `som_icd_codg` (`id`, `icd_type`, `icd_codg_ver`, `code`, `name`, `enab_flag_codg`, `trt_type`, `is_gray`) VALUES (null, 'ICD-9', '10', '78.0900x026', '异体椎间融合骨块移植术', '1', NULL, '0');
INSERT INTO `som_icd_codg` (`id`, `icd_type`, `icd_codg_ver`, `code`, `name`, `enab_flag_codg`, `trt_type`, `is_gray`) VALUES (null, 'ICD-9', '10', '81.4000x007', '髋关节镜下异体骨软骨移植术', '1', NULL, '0');
INSERT INTO `som_icd_codg` (`id`, `icd_type`, `icd_codg_ver`, `code`, `name`, `enab_flag_codg`, `trt_type`, `is_gray`) VALUES (null, 'ICD-9', '10', '81.4000x008', '髋关节异体骨软骨移植术', '1', NULL, '0');
INSERT INTO `som_icd_codg` (`id`, `icd_type`, `icd_codg_ver`, `code`, `name`, `enab_flag_codg`, `trt_type`, `is_gray`) VALUES (null, 'ICD-9', '10', '81.4400x001', '异体韧带重建膝关节内侧支持带术', '1', NULL, '0');
INSERT INTO `som_icd_codg` (`id`, `icd_type`, `icd_codg_ver`, `code`, `name`, `enab_flag_codg`, `trt_type`, `is_gray`) VALUES (null, 'ICD-9', '10', '81.4400x002', '异体韧带重建膝关节外侧支持带术', '1', NULL, '0');
INSERT INTO `som_icd_codg` (`id`, `icd_type`, `icd_codg_ver`, `code`, `name`, `enab_flag_codg`, `trt_type`, `is_gray`) VALUES (null, 'ICD-9', '10', '81.4600x002', '异体韧带重建膝关节内侧副韧带术', '1', NULL, '0');
INSERT INTO `som_icd_codg` (`id`, `icd_type`, `icd_codg_ver`, `code`, `name`, `enab_flag_codg`, `trt_type`, `is_gray`) VALUES (null, 'ICD-9', '10', '81.4600x003', '异体韧带重建膝关节外侧副韧带术', '1', NULL, '0');
INSERT INTO `som_icd_codg` (`id`, `icd_type`, `icd_codg_ver`, `code`, `name`, `enab_flag_codg`, `trt_type`, `is_gray`) VALUES (null, 'ICD-9', '10', '81.4700x020', '膝关节镜下异体内侧半月板移植术', '1', NULL, '0');
INSERT INTO `som_icd_codg` (`id`, `icd_type`, `icd_codg_ver`, `code`, `name`, `enab_flag_codg`, `trt_type`, `is_gray`) VALUES (null, 'ICD-9', '10', '81.4700x021', '膝关节异体骨软骨移植术', '1', NULL, '0');
INSERT INTO `som_icd_codg` (`id`, `icd_type`, `icd_codg_ver`, `code`, `name`, `enab_flag_codg`, `trt_type`, `is_gray`) VALUES (null, 'ICD-9', '10', '81.4900x007', '踝关节异体骨软骨移植术', '1', NULL, '0');
INSERT INTO `som_icd_codg` (`id`, `icd_type`, `icd_codg_ver`, `code`, `name`, `enab_flag_codg`, `trt_type`, `is_gray`) VALUES (null, 'ICD-9', '10', '81.4900x008', '异体韧带重建踝关节韧带术', '1', NULL, '0');
INSERT INTO `som_icd_codg` (`id`, `icd_type`, `icd_codg_ver`, `code`, `name`, `enab_flag_codg`, `trt_type`, `is_gray`) VALUES (null, 'ICD-9', '10', '81.8500x009', '异体韧带重建肘关节周围韧带术', '1', NULL, '0');
INSERT INTO `som_icd_codg` (`id`, `icd_type`, `icd_codg_ver`, `code`, `name`, `enab_flag_codg`, `trt_type`, `is_gray`) VALUES (null, 'ICD-9', '10', '83.3900x064', '经皮头部软组织病损纳米刀消融术', '1', NULL, '0');
INSERT INTO `som_icd_codg` (`id`, `icd_type`, `icd_codg_ver`, `code`, `name`, `enab_flag_codg`, `trt_type`, `is_gray`) VALUES (null, 'ICD-9', '10', '83.3900x065', '经皮颈部软组织病损纳米刀消融术', '1', NULL, '0');
INSERT INTO `som_icd_codg` (`id`, `icd_type`, `icd_codg_ver`, `code`, `name`, `enab_flag_codg`, `trt_type`, `is_gray`) VALUES (null, 'ICD-9', '10', '83.3900x066', '经皮上肢软组织病损纳米刀消融术', '1', NULL, '0');
INSERT INTO `som_icd_codg` (`id`, `icd_type`, `icd_codg_ver`, `code`, `name`, `enab_flag_codg`, `trt_type`, `is_gray`) VALUES (null, 'ICD-9', '10', '85.2100x024', '经皮乳腺病损纳米刀消融术', '1', NULL, '0');
INSERT INTO `som_icd_codg` (`id`, `icd_type`, `icd_codg_ver`, `code`, `name`, `enab_flag_codg`, `trt_type`, `is_gray`) VALUES (null, 'ICD-9', '10', '92.2600x001', '重离子(碳离子)远距离放射治疗', '1', NULL, '0');
-- update lihongxiang 20240923 end


-- 质控脚本相关sql 更新
-- updateby lihongxiang 202050606 begin
# 修改 工作单位邮政编码[a31c]内涵质控
UPDATE `som_chain_script`
SET script_data = 'import com.my.som.common.util.ValidateUtil;
import com.my.som.model.dataHandle.SomHiInvyBasInfo;
import com.my.som.model.medicalQuality.SomFundPay;
import com.my.som.settlevalidate.constant.SettleValidateConst;
import com.my.som.settlevalidate.util.SettleValidateUtil;
import com.my.som.settlevalidate.vo.SetlValidBaseBusiVo;
import com.my.som.vo.dataHandle.SettleListHandlerVo;
import com.my.som.vo.dataHandle.SettleListValidateVo;
import com.yomahub.liteflow.script.ScriptExecuteWrap;
import com.yomahub.liteflow.script.body.JaninoCommonScriptBody;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 职工医保时 单位邮编不为空
 */
public class check_pos_code_after_depth implements JaninoCommonScriptBody {
    private static final Logger logger = LoggerFactory.getLogger(check_pos_code_after_depth.class);

    private static final String MAIN_CHECK_FIELD_1 = "a54";
    private static final String MAIN_CHECK_NAME_1 = "医保类型";
    private static final List<String> EMPLOYEE_MEDICARE_CODE = Arrays.asList("310","1","01");
    private static final List<String> EMPLOYEE_FUND_CODE = Arrays.asList("310100","310","城镇职工基本医疗保险统筹基金");

    private static final String MAIN_CHECK_FIELD = "a31c";
    private static final String MAIN_CHECK_NAME = "工作单位邮政编码";
    private static final String MAIN_CHECK_REGION = "四川";
    /**
     * 事后校验
     * 医保类型 逻辑质检
     * 判断是否为职工医疗保险
     * 如果是：
     * 判断就诊前的工作单位邮政编码是否为空(poscode)
     * @param wrap
     * @return
     */
    @Override
    public Void body(ScriptExecuteWrap wrap) {
        SetlValidBaseBusiVo setlValidBaseBusiVo = (SetlValidBaseBusiVo) wrap.cmp.getFirstContextBean();
        SettleListValidateVo settleListValidateVo = (SettleListValidateVo) wrap.cmp.getRequestData();
        Map<String, String> keysBasicResultMap = setlValidBaseBusiVo.getKeysBasicResultMap();
        SomHiInvyBasInfo somHiInvyBasInfo = settleListValidateVo.getSomHiInvyBasInfo();
        SettleListHandlerVo settleListHandlerVo = new SettleListHandlerVo();
        List<SomFundPay> busFundPayList = settleListValidateVo.getBusFundPayList();
        // 检查 somHiInvyBasInfo 是否为空
        if (SettleValidateUtil.isEmpty(somHiInvyBasInfo)) {
            SettleValidateUtil.addErrorDetail(settleListHandlerVo, SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY,SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY,
                    (String)SettleValidateConst.CHECK_TYPE_MAP.get(SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY),MAIN_CHECK_FIELD,
                    SettleValidateConst.VALIDATE_STATE_FAIL, setlValidBaseBusiVo);
            return null;
        }

        //获取医保类型质检结果
        String mdeic_type_stas = (String)keysBasicResultMap.get(MAIN_CHECK_FIELD_1) ;
        //获取工作单位邮编检结果
        String poscode_stas = (String)keysBasicResultMap.get(MAIN_CHECK_FIELD) ;
        //判断当医保类型为职工医保时，工作单位邮编不能为空
        String a31c = somHiInvyBasInfo.getA31c();
        if(SettleValidateConst.PASS.equals(mdeic_type_stas)){
            // 获取医保类型
            String a54 = somHiInvyBasInfo.getA54();
            if(EMPLOYEE_MEDICARE_CODE.contains(a54)){

                if(SettleValidateConst.NULL.equals(poscode_stas)){
                    SettleValidateUtil.addErrorDetail(settleListHandlerVo, SettleValidateConst.VALIDATE_TYPE_2,SettleValidateConst.VALIDATE_TYPE_1,
                            MAIN_CHECK_NAME + "[" + a31c + "]不能为空", MAIN_CHECK_FIELD,
                            SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
                }
                else {
                    //此时说明工作邮编有值
                    //判断是否为阿拉伯数字,同时是否审核字段为6位
                    if(!a31c.matches(SettleValidateUtil.POS_CODE_PATTERN)){
                        SettleValidateUtil.addErrorDetail(settleListHandlerVo, SettleValidateConst.VALIDATE_TYPE_2,SettleValidateConst.VALIDATE_TYPE_2,
                                MAIN_CHECK_NAME + "[" + a31c + "]格式错误",MAIN_CHECK_FIELD,
                                SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
                    }
                }
            }
        }
        if (!ValidateUtil.isEmpty(busFundPayList)) {
            for (SomFundPay busFundPay : busFundPayList){
                if(!ValidateUtil.isEmpty(busFundPay) && !ValidateUtil.isEmpty(busFundPay.getFundPayType()) ){
                    if(EMPLOYEE_FUND_CODE.contains(busFundPay.getFundPayType())){
                        if (SettleValidateConst.NULL.equals(poscode_stas)) {
                            SettleValidateUtil.addErrorDetail(settleListHandlerVo, SettleValidateConst.VALIDATE_TYPE_2, SettleValidateConst.VALIDATE_TYPE_1,
                                    "基金类型为职工,"+MAIN_CHECK_NAME + "[" + a31c + "]不能为空", MAIN_CHECK_FIELD,
                                    SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
                        }
                        else {
                        //此时说明工作邮编有值
                        //判断是否为阿拉伯数字,同时是否审核字段为6位
                        if(!a31c.matches(SettleValidateUtil.POS_CODE_PATTERN)){
                            SettleValidateUtil.addErrorDetail(settleListHandlerVo, SettleValidateConst.VALIDATE_TYPE_2,SettleValidateConst.VALIDATE_TYPE_2,
                                    MAIN_CHECK_NAME + "[" + a31c + "]格式错误",MAIN_CHECK_FIELD,
                                    SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
                        }
                    }
                    }
                }
            }
        }
        return null;
    }
}'
where script_id='check_pos_code_after_depth'


# 工作单位电话[a30]内涵质控
UPDATE `som_chain_script`
SET script_data = 'import com.my.som.common.util.ValidateUtil;
import com.my.som.model.dataHandle.SomHiInvyBasInfo;
import com.my.som.model.medicalQuality.SomFundPay;
import com.my.som.settlevalidate.constant.SettleValidateConst;
import com.my.som.settlevalidate.util.SettleValidateUtil;
import com.my.som.settlevalidate.vo.SetlValidBaseBusiVo;
import com.my.som.vo.dataHandle.SettleListHandlerVo;
import com.my.som.vo.dataHandle.SettleListValidateVo;
import com.yomahub.liteflow.script.ScriptExecuteWrap;
import com.yomahub.liteflow.script.body.JaninoCommonScriptBody;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 职工医保时  单位电话不为空
 */
public class check_emp_tel_after_depth implements JaninoCommonScriptBody {
    private static final Logger logger = LoggerFactory.getLogger(check_emp_tel_after_depth.class);
    private static final String MAIN_CHECK_FIELD = "a30";
    private static final String MAIN_CHECK_NAME = "工作单位电话";
    private static final String MAIN_CHECK_FIELD_1 = "a54";
    private static final String MAIN_CHECK_NAME_1  = "医保类型";
    private static final List<String> EMPLOYEE_MEDICARE_CODE = Arrays.asList("310","1","01");
        private static final List<String> EMPLOYEE_FUND_CODE = Arrays.asList("310100","310","城镇职工基本医疗保险统筹基金");
    private static final String MAIN_CHECK_FIELD_2 = "a30";
    private static final String MAIN_CHECK_NAME_2 = "工作单位电话";
    private static final String MAIN_CHECK_REGION = "四川";

    /**
     * 事后校验
     * 医保类型 逻辑质检
     * 判断是否为职工医疗保险
     * 如果是：
     * 判断就诊前的工作单位是否为空(emp_name)
     * 判断就诊前的工作地址是否为空(emp_addr)
     * @param wrap
     * @return
     */
    @Override
    public Void body(ScriptExecuteWrap wrap) {
        SetlValidBaseBusiVo setlValidBaseBusiVo = (SetlValidBaseBusiVo) wrap.cmp.getFirstContextBean();
        SettleListValidateVo settleListValidateVo = (SettleListValidateVo) wrap.cmp.getRequestData();
        Map<String, String> keysBasicResultMap = setlValidBaseBusiVo.getKeysBasicResultMap();
        SomHiInvyBasInfo somHiInvyBasInfo = settleListValidateVo.getSomHiInvyBasInfo();
        SettleListHandlerVo settleListHandlerVo = new SettleListHandlerVo();
        List<SomFundPay> busFundPayList = settleListValidateVo.getBusFundPayList();
        // 检查 somHiInvyBasInfo 是否为空
        if (SettleValidateUtil.isEmpty(somHiInvyBasInfo)) {
            SettleValidateUtil.addErrorDetail(settleListHandlerVo, SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY,SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY,
                    (String) SettleValidateConst.CHECK_TYPE_MAP.get(SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY),MAIN_CHECK_FIELD_2,
                    SettleValidateConst.VALIDATE_STATE_FAIL, setlValidBaseBusiVo);
            return null;
        }

        //获取医保类型质检结果
        String mdeic_type_stas = (String)keysBasicResultMap.get(MAIN_CHECK_FIELD_1) ;
        //获取工作单位电话质检结果
        String emp_tel_stas = (String)keysBasicResultMap.get(MAIN_CHECK_FIELD_2) ;
        //判断当医保类型为职工医保时，工作单位不能为空
        String a30 = somHiInvyBasInfo.getA30();
        if(SettleValidateConst.PASS.equals(mdeic_type_stas)){
            // 获取医保类型
            String a54 = somHiInvyBasInfo.getA54();
            if(EMPLOYEE_MEDICARE_CODE.contains(a54)){

                if(SettleValidateConst.NULL.equals(emp_tel_stas)){
                    SettleValidateUtil.addErrorDetail(settleListHandlerVo, SettleValidateConst.VALIDATE_TYPE_2,SettleValidateConst.VALIDATE_TYPE_1,
                            MAIN_CHECK_NAME_2 + "[" + a30 + "]为空",MAIN_CHECK_FIELD_2,
                            SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
                }
            }
        }
        if (!ValidateUtil.isEmpty(busFundPayList)) {
            for (SomFundPay busFundPay : busFundPayList){
                if(!ValidateUtil.isEmpty(busFundPay) && !ValidateUtil.isEmpty(busFundPay.getFundPayType()) ){
                    if(EMPLOYEE_FUND_CODE.contains(busFundPay.getFundPayType())){
                        if (SettleValidateConst.NULL.equals(emp_tel_stas)) {
                            SettleValidateUtil.addErrorDetail(settleListHandlerVo, SettleValidateConst.VALIDATE_TYPE_2, SettleValidateConst.VALIDATE_TYPE_1,
                                    "基金类型为职工,"+MAIN_CHECK_NAME_2 + "[" + a30 + "]不能为空", MAIN_CHECK_FIELD_2,
                                    SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
                        }
                    }
                }
            }
        }
        return null;
    }
}'
where script_id='check_emp_tel_after_depth'


# 工作单位名称[a29n]内涵质控
UPDATE `som_chain_script`
SET script_data = 'import com.my.som.common.util.ValidateUtil;
import com.my.som.model.dataHandle.SomHiInvyBasInfo;
import com.my.som.model.medicalQuality.SomFundPay;
import com.my.som.settlevalidate.constant.SettleValidateConst;
import com.my.som.settlevalidate.script.base.check_coner_tel_basic;
import com.my.som.settlevalidate.util.SettleValidateUtil;
import com.my.som.settlevalidate.vo.SetlValidBaseBusiVo;
import com.my.som.vo.dataHandle.SettleListHandlerVo;
import com.my.som.vo.dataHandle.SettleListValidateVo;
import com.yomahub.liteflow.script.ScriptExecuteWrap;
import com.yomahub.liteflow.script.body.JaninoCommonScriptBody;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 职工医保时 单位名称不为空
 */
public class check_emp_name_after_depth implements JaninoCommonScriptBody {
    private static final Logger logger = LoggerFactory.getLogger(check_emp_name_after_depth.class);

    private static final String MAIN_CHECK_FIELD = "a29n";
    private static final String MAIN_CHECK_NAME  = "工作单位名称";
    private static final String MAIN_CHECK_FIELD_1 = "a54";
    private static final String MAIN_CHECK_NAME_1  = "医保类型";
    private static final List<String> EMPLOYEE_MEDICARE_CODE = Arrays.asList("310","1","01");
    private static final List<String> EMPLOYEE_FUND_CODE = Arrays.asList("310100","310","城镇职工基本医疗保险统筹基金");
    private static final String MAIN_CHECK_FIELD_2 = "a29n";
    private static final String MAIN_CHECK_NAME_2  = "工作单位名称";
    private static final String MAIN_CHECK_REGION = "四川";
    /**
     * 事后校验
     *
     * 判断是否为职工医疗保险
     * 如果是：
     * 判断就诊前的工作单位是否为空(emp_name)
     * @param wrap
     * @return
     */
    @Override
    public Void body(ScriptExecuteWrap wrap) {
        SetlValidBaseBusiVo setlValidBaseBusiVo = (SetlValidBaseBusiVo) wrap.cmp.getFirstContextBean();
        SettleListValidateVo settleListValidateVo = (SettleListValidateVo) wrap.cmp.getRequestData();
        Map<String, String> keysBasicResultMap = setlValidBaseBusiVo.getKeysBasicResultMap();
        SomHiInvyBasInfo somHiInvyBasInfo = settleListValidateVo.getSomHiInvyBasInfo();
        SettleListHandlerVo settleListHandlerVo = new SettleListHandlerVo();
        List<SomFundPay> busFundPayList = settleListValidateVo.getBusFundPayList();
        // 检查 somHiInvyBasInfo 是否为空
        if (SettleValidateUtil.isEmpty(somHiInvyBasInfo)) {
            SettleValidateUtil.addErrorDetail(settleListHandlerVo, SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY,SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY,
                    (String)SettleValidateConst.CHECK_TYPE_MAP.get(SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY),null,
                    SettleValidateConst.VALIDATE_STATE_FAIL, setlValidBaseBusiVo);
            return null;
        }

        //获取医保类型质检结果
        String mdeic_type_stas = (String)keysBasicResultMap.get(MAIN_CHECK_FIELD_1) ;
        //获取工作单位名称质检结果
        String emp_name_stas = (String)keysBasicResultMap.get(MAIN_CHECK_FIELD_2) ;
        //判断当医保类型为职工医保时，工作单位不能为空
        String a29n = somHiInvyBasInfo.getA29n();
        if(SettleValidateConst.PASS.equals(mdeic_type_stas)){
            // 获取医保类型
            String a54 = somHiInvyBasInfo.getA54();
            if(EMPLOYEE_MEDICARE_CODE.contains(a54)){
                if(SettleValidateConst.NULL.equals(emp_name_stas)){
                    SettleValidateUtil.addErrorDetail(settleListHandlerVo, SettleValidateConst.VALIDATE_TYPE_2,SettleValidateConst.VALIDATE_TYPE_1,
                            MAIN_CHECK_NAME_2 + "[" + a29n + "]不能为空", MAIN_CHECK_FIELD_2,
                            SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
                }
            }
        }

        if (!ValidateUtil.isEmpty(busFundPayList)) {
            for (SomFundPay busFundPay : busFundPayList){
                if(!ValidateUtil.isEmpty(busFundPay) && !ValidateUtil.isEmpty(busFundPay.getFundPayType()) ){
                    if(EMPLOYEE_FUND_CODE.contains(busFundPay.getFundPayType())){
                        if (SettleValidateConst.NULL.equals(emp_name_stas)) {
                            SettleValidateUtil.addErrorDetail(settleListHandlerVo, SettleValidateConst.VALIDATE_TYPE_2, SettleValidateConst.VALIDATE_TYPE_1,
                                    "基金类型为职工,"+MAIN_CHECK_NAME_2 + "[" + a29n + "]不能为空", MAIN_CHECK_FIELD_2,
                                    SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
                        }
                    }
                }
            }
        }
        return null;
    }
}
'
where script_id='check_emp_name_after_depth'



# 工作单位地址[a29]内涵质控
UPDATE `som_chain_script`
SET script_data = 'import com.my.som.common.util.ValidateUtil;
import com.my.som.model.dataHandle.SomHiInvyBasInfo;
import com.my.som.model.medicalQuality.SomFundPay;
import com.my.som.settlevalidate.constant.SettleValidateConst;
import com.my.som.settlevalidate.util.SettleValidateUtil;
import com.my.som.settlevalidate.vo.SetlValidBaseBusiVo;
import com.my.som.vo.dataHandle.SettleListHandlerVo;
import com.my.som.vo.dataHandle.SettleListValidateVo;
import com.yomahub.liteflow.script.ScriptExecuteWrap;
import com.yomahub.liteflow.script.body.JaninoCommonScriptBody;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 职工医保时 单位地址不为空
 */
public class check_emp_addr_after_depth implements JaninoCommonScriptBody {
    private static final Logger logger = LoggerFactory.getLogger(check_emp_addr_after_depth.class);

    private static final String MAIN_CHECK_FIELD = "a29";
    private static final String MAIN_CHECK_NAME = "工作单位地址";

    private static final String MAIN_CHECK_FIELD_1 = "a54";
    private static final String MAIN_CHECK_NAME_1 = "医保类型";
    private static final List<String> EMPLOYEE_MEDICARE_CODE = Arrays.asList("310","1","01");
    private static final List<String> EMPLOYEE_FUND_CODE = Arrays.asList("310100","310","城镇职工基本医疗保险统筹基金");
    private static final String MAIN_CHECK_FIELD_2 = "a29";
    private static final String MAIN_CHECK_NAME_2 = "工作单位地址";
    private static final String MAIN_CHECK_REGION = "四川";
    /**
     * 事后校验
     * 医保类型 逻辑质检
     * 判断是否为职工医疗保险
     * 如果是：
     * 判断就诊前的工作地址是否为空(emp_addr)
     *
     * @param wrap
     * @return
     */
    @Override
    public Void body(ScriptExecuteWrap wrap) {
        SetlValidBaseBusiVo setlValidBaseBusiVo = (SetlValidBaseBusiVo) wrap.cmp.getFirstContextBean();
        SettleListValidateVo settleListValidateVo = (SettleListValidateVo) wrap.cmp.getRequestData();
        Map<String, String> keysBasicResultMap = setlValidBaseBusiVo.getKeysBasicResultMap();
        SomHiInvyBasInfo somHiInvyBasInfo = settleListValidateVo.getSomHiInvyBasInfo();
        SettleListHandlerVo settleListHandlerVo = new SettleListHandlerVo();
        List<SomFundPay> busFundPayList = settleListValidateVo.getBusFundPayList();
        // 检查 somHiInvyBasInfo 是否为空
        if (SettleValidateUtil.isEmpty(somHiInvyBasInfo)) {
            SettleValidateUtil.addErrorDetail(settleListHandlerVo, SettleValidateConst.VALIDATE_TYPE_2, SettleValidateConst.VALIDATE_TYPE_2,
                    (String) SettleValidateConst.CHECK_TYPE_MAP.get(SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY), MAIN_CHECK_FIELD_2,
                    SettleValidateConst.VALIDATE_STATE_FAIL, setlValidBaseBusiVo);
            return null;
        }

        //获取医保类型质检结果
        String mdeic_type_stas = (String) keysBasicResultMap.get(MAIN_CHECK_FIELD_1);
        //获取工作单位地址质检结果
        String emp_addr_stas = (String) keysBasicResultMap.get(MAIN_CHECK_FIELD_2);

        //判断当医保类型为职工医保时，工作单位地址不能为空
        String a29 = somHiInvyBasInfo.getA29();
        if (SettleValidateConst.PASS.equals(mdeic_type_stas)) {
            // 获取医保类型
            String a54 = somHiInvyBasInfo.getA54();
            if (EMPLOYEE_MEDICARE_CODE.contains(a54)) {

                if (SettleValidateConst.NULL.equals(emp_addr_stas)) {
                    SettleValidateUtil.addErrorDetail(settleListHandlerVo, SettleValidateConst.VALIDATE_TYPE_2, SettleValidateConst.VALIDATE_TYPE_1,
                            MAIN_CHECK_NAME_2 + "[" + a29 + "]为空", MAIN_CHECK_FIELD_2,
                            SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
                }
            }
        }
        if (!ValidateUtil.isEmpty(busFundPayList)) {
            for (SomFundPay busFundPay : busFundPayList){
                if(!ValidateUtil.isEmpty(busFundPay) && !ValidateUtil.isEmpty(busFundPay.getFundPayType()) ){
                    if(EMPLOYEE_FUND_CODE.contains(busFundPay.getFundPayType())){
                        if (SettleValidateConst.NULL.equals(emp_addr_stas)) {
                            SettleValidateUtil.addErrorDetail(settleListHandlerVo, SettleValidateConst.VALIDATE_TYPE_2, SettleValidateConst.VALIDATE_TYPE_1,
                                    "基金类型为职工,"+MAIN_CHECK_NAME_2 + "[" + a29 + "]为空", MAIN_CHECK_FIELD_2,
                                    SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
                        }
                    }
                }

            }
        }
        return null;
    }
}
'
where script_id='check_emp_addr_after_depth'
# 入院前昏迷时间（天）[C28]内涵质控
UPDATE `som_chain_script`
SET script_data = '
import com.my.som.common.constant.DrgConst;
import com.my.som.common.util.ValidateUtil;
import com.my.som.dto.medicalQuality.BusDiseaseDiagnosisTrim;
import com.my.som.model.dataHandle.SomHiInvyBasInfo;
import com.my.som.settlevalidate.constant.SettleValidateConst;
import com.my.som.settlevalidate.util.SettleValidateUtil;
import com.my.som.settlevalidate.vo.SetlValidBaseBusiVo;

import com.my.som.util.SettleListValidateUtil;
import com.my.som.vo.dataHandle.SettleListDisSectionVo;
import com.my.som.vo.dataHandle.SettleListHandlerVo;
import com.my.som.vo.dataHandle.SettleListValidateVo;
import com.yomahub.liteflow.script.ScriptExecuteWrap;
import com.yomahub.liteflow.script.body.JaninoCommonScriptBody;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
/**
 * 颅脑损伤昏迷时间校验
 */
public class check_brn_damg_coma_dura_after_depth implements JaninoCommonScriptBody {
    private static final Logger logger = LoggerFactory.getLogger(check_brn_damg_coma_dura_after_depth.class);
    private static final String MAIN_CHECK_FIELD = "C28";
    private static final String MAIN_CHECK_NAME = "入院前昏迷时间（天）";
    private static final String MAIN_CHECK_REGION = "四川";
    /**
     * 颅脑损伤昏迷时间校验
     * 1. 颅脑损伤患者入院前昏迷时长
     * 2. 颅脑损伤患者入院后昏迷时长
     */
    @Override
    public Void body(ScriptExecuteWrap wrap) {
        SetlValidBaseBusiVo setlValidBaseBusiVo = (SetlValidBaseBusiVo) wrap.cmp.getFirstContextBean();
        SettleListValidateVo settleListValidateVo = (SettleListValidateVo) wrap.cmp.getRequestData();

        SomHiInvyBasInfo somHiInvyBasInfo = settleListValidateVo.getSomHiInvyBasInfo();
        SettleListHandlerVo settleListHandlerVo = new SettleListHandlerVo();
        List<BusDiseaseDiagnosisTrim> busDiseaseDiagnosisTrimsList = settleListValidateVo.getBusDiseaseDiagnosisTrimsList();
        // 检查 somHiInvyBasInfo 是否为空

        // 检查诊断信息是否为空
        if (SettleValidateUtil.isEmpty(busDiseaseDiagnosisTrimsList)) {
            return null;
        }

        // 颅脑损伤入院前和入院后的字段
        Integer[] admissionBefore = {somHiInvyBasInfo.getC28(), somHiInvyBasInfo.getC29(), somHiInvyBasInfo.getC30()};
        Integer[] admissionAfter = {somHiInvyBasInfo.getC31(), somHiInvyBasInfo.getC32(), somHiInvyBasInfo.getC33()};

        // 检查颅脑损伤入院时间是否合法
        validateAdmissionTime(admissionBefore, setlValidBaseBusiVo, "颅脑损伤入院前昏迷时间不合理");
        validateAdmissionTime(admissionAfter, setlValidBaseBusiVo, "颅脑损伤入院后昏迷时间不合理");

        //判断入院后昏迷时长是否大于住院天数a20
        String b20 =   somHiInvyBasInfo.getB20();

        checkAfterLessThanIptDay(admissionAfter,b20,setlValidBaseBusiVo);
        checkReasonable(admissionBefore,admissionAfter, setlValidBaseBusiVo, busDiseaseDiagnosisTrimsList);
        return null;
    }

    private void checkAfterLessThanIptDay(Integer[] admissionAfter, String b20, SetlValidBaseBusiVo setlValidBaseBusiVo) {
        Integer c31 = admissionAfter[0];
        Integer c32 = admissionAfter[1];
        Integer c33 = admissionAfter[2];

        if ((c31 != null && c31 != 0) || (c32 != null && c32 != 0 ) || (c33 != null && c33 != 0)) {
            if(ValidateUtil.isEmpty(b20)){
                addErrorDetail(SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_ERROR_TYPE_4,
                        "c06c1",
                        "入院后昏迷时间存在，但没有实际住院天数",
                        SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
            }else{
                Integer inhospitalDays = null;
                try {
                    inhospitalDays = Integer.valueOf(b20);
                } catch (NumberFormatException e) {
                    addErrorDetail(SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_ERROR_TYPE_4,
                            "b20",
                            "实际住院天数不合理",
                            SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
                }

                double totalDays = 0.0;

                if (c31 != null) {
                    totalDays += c31;  // 添加天数
                }

                if (c32 != null) {
                    totalDays += c32 / 24.0;  // 将小时转为天数
                }

                if (c33 != null) {
                    totalDays += c33 / 1440.0;  // 将分钟转为天数
                }
                if(totalDays > inhospitalDays){
                    addErrorDetail(SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_ERROR_TYPE_4,
                            "c06c1",
                            "入院后昏迷时间["+totalDays+"] 不应该超过实际住院天数["+b20+"]",
                            SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
                }
            }

        }
    }
    private void checkReasonable(Integer[] admissionBefore, Integer[] admissionAfter,SetlValidBaseBusiVo setlValidBaseBusiVo, List<BusDiseaseDiagnosisTrim> busDiseaseDiagnosisTrimsList) {
        boolean flag = false;
        for(Integer num : admissionBefore){
            if (num != null && num != 0) {
                flag =true;
            }
        }
        for(Integer num : admissionAfter){
            if (num != null && num != 0) {
                flag =true;
            }
        }

        if(flag){
            boolean has = false;
            Map<String, Object> dictMap = (Map<String, Object>) setlValidBaseBusiVo.getSettleListParams().get(SettleListValidateUtil.KEY_DIS_SECTION_LIST);
            List<String> codeList = new ArrayList<>();
            List<SettleListDisSectionVo> settleListDisSectionVos = (List<SettleListDisSectionVo>) dictMap.get(DrgConst.COMA_CODE_FLAG);
            for (SettleListDisSectionVo settleListDisSectionVo : settleListDisSectionVos) {
                codeList.add(settleListDisSectionVo.getDiagSec());
            }
            for(String code: codeList){
                for(BusDiseaseDiagnosisTrim busDiseaseDiagnosisTrim : busDiseaseDiagnosisTrimsList){
                    if(code.equals(busDiseaseDiagnosisTrim.getC06c1())){
                        has= true;
                    }
                }
            }

            if(!has){
                addErrorDetail(SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_ERROR_TYPE_2,
                        "c06c1",
                        "当颅脑损伤患者昏迷时间的“天”“时”“分”其中之一值≠0时，主要诊断或者其他诊断需包含与颅脑损伤诊断相关的编码，颅脑损伤相关编码如下"+ codeList.toString(),
                        SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
            }
        }
    }

    private void addErrorDetail( String index,String errType,String errorFields, String errDscr, String chkStas, SetlValidBaseBusiVo baseBusiVo) {
        SettleListHandlerVo handlerVo =new SettleListHandlerVo();
        handlerVo.setIndex(index);
        handlerVo.setErrDscr(errDscr);
        handlerVo.setErrorFields(errorFields);
        handlerVo.setChkStas(chkStas);
        handlerVo.setErrType(errType);
        baseBusiVo.getErrorDetails().add(handlerVo);
    }

    private void validateAdmissionTime(Integer[] admissionTimes,SetlValidBaseBusiVo baseBusiVo, String errorMessage) {
        Integer c28 = admissionTimes[0];
        Integer c29 = admissionTimes[1];
        Integer c30 = admissionTimes[2];

        if (!SettleValidateUtil.isEmpty(c28) || !SettleValidateUtil.isEmpty(c29) || !SettleValidateUtil.isEmpty(c30)) {
            if (c28 == null || c28 < 0 || c29 == null || c29 < 0 || c29 > 24 || c30 == null || c30 < 0 || c30 > 60) {
                addErrorDetail( SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_ERROR_TYPE_4,
                        "c28", errorMessage, SettleValidateConst.VALIDATE_STATE_SUCCESS, baseBusiVo);
            }
        }
    }
}
'
where script_id='check_brn_damg_coma_dura_after_depth';

# 颅脑损伤相关代码
delete from som_chk_diag_sec where chk_type = 7;
INSERT INTO `som_chk_diag_sec` (`id`, `diag_sec`, `chk_type`, `dscr`, `diag_name`) VALUES (null, 'S06.700x001', '7', "当颅脑损伤患者昏迷时间的“天”“时”“分”其中之一值≠0时，主要诊断或者其他诊断需包含与颅脑损伤诊断相关的编码", '闭合性颅脑损伤轻型');
INSERT INTO `som_chk_diag_sec` (`id`, `diag_sec`, `chk_type`, `dscr`, `diag_name`) VALUES (null, 'S06.700x002', '7', "当颅脑损伤患者昏迷时间的“天”“时”“分”其中之一值≠0时，主要诊断或者其他诊断需包含与颅脑损伤诊断相关的编码", '闭合性颅脑损伤中型');
INSERT INTO `som_chk_diag_sec` (`id`, `diag_sec`, `chk_type`, `dscr`, `diag_name`) VALUES (null, 'S06.700x003', '7', "当颅脑损伤患者昏迷时间的“天”“时”“分”其中之一值≠0时，主要诊断或者其他诊断需包含与颅脑损伤诊断相关的编码", '闭合性颅脑损伤重型');
INSERT INTO `som_chk_diag_sec` (`id`, `diag_sec`, `chk_type`, `dscr`, `diag_name`) VALUES (null, 'S06.700x004', '7', "当颅脑损伤患者昏迷时间的“天”“时”“分”其中之一值≠0时，主要诊断或者其他诊断需包含与颅脑损伤诊断相关的编码", '闭合性颅脑损伤特重型');
INSERT INTO `som_chk_diag_sec` (`id`, `diag_sec`, `chk_type`, `dscr`, `diag_name`) VALUES (null, 'S06.700x005', '7', "当颅脑损伤患者昏迷时间的“天”“时”“分”其中之一值≠0时，主要诊断或者其他诊断需包含与颅脑损伤诊断相关的编码", '开放性颅脑损伤轻型');
INSERT INTO `som_chk_diag_sec` (`id`, `diag_sec`, `chk_type`, `dscr`, `diag_name`) VALUES (null, 'S06.700x006', '7', "当颅脑损伤患者昏迷时间的“天”“时”“分”其中之一值≠0时，主要诊断或者其他诊断需包含与颅脑损伤诊断相关的编码", '开放性颅脑损伤中型');
INSERT INTO `som_chk_diag_sec` (`id`, `diag_sec`, `chk_type`, `dscr`, `diag_name`) VALUES (null, 'S06.700x007', '7', "当颅脑损伤患者昏迷时间的“天”“时”“分”其中之一值≠0时，主要诊断或者其他诊断需包含与颅脑损伤诊断相关的编码", '开放性颅脑损伤重型');
INSERT INTO `som_chk_diag_sec` (`id`, `diag_sec`, `chk_type`, `dscr`, `diag_name`) VALUES (null, 'S06.700x008', '7', "当颅脑损伤患者昏迷时间的“天”“时”“分”其中之一值≠0时，主要诊断或者其他诊断需包含与颅脑损伤诊断相关的编码", '开放性颅脑损伤特重型');
INSERT INTO `som_chk_diag_sec` (`id`, `diag_sec`, `chk_type`, `dscr`, `diag_name`) VALUES (null, 'T90.501', '7', "当颅脑损伤患者昏迷时间的“天”“时”“分”其中之一值≠0时，主要诊断或者其他诊断需包含与颅脑损伤诊断相关的编码", '陈旧性颅脑损伤');


-- updateby lihongxiang 202050606 end

-- updateby lihongxiang 202050606 begin
INSERT INTO `som_chk_diag_sec` (`id`, `diag_sec`, `chk_type`, `dscr`, `diag_name`) VALUES (null, '71.3x01', '11', '病人为男性，不能使用该手术编码', '会阴病损切除术');


UPDATE  `som_chain_script`
SET script_data = 'import com.my.som.common.constant.DrgConst;
import com.my.som.common.util.ValidateUtil;
import com.my.som.dto.medicalQuality.BusDiseaseDiagnosisTrim;
import com.my.som.model.dataHandle.SomHiInvyBasInfo;
import com.my.som.settlevalidate.constant.SettleValidateConst;
import com.my.som.settlevalidate.util.SettleValidateUtil;
import com.my.som.settlevalidate.vo.SetlValidBaseBusiVo;

import com.my.som.util.SettleListValidateUtil;
import com.my.som.vo.dataHandle.SettleListDisSectionVo;
import com.my.som.vo.dataHandle.SettleListHandlerVo;
import com.my.som.vo.dataHandle.SettleListValidateVo;
import com.yomahub.liteflow.script.ScriptExecuteWrap;
import com.yomahub.liteflow.script.body.JaninoCommonScriptBody;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 颅脑损伤昏迷时间校验
 */
public class check_brn_damg_coma_dura_after_depth implements JaninoCommonScriptBody {
    private static final Logger logger = LoggerFactory.getLogger(check_brn_damg_coma_dura_after_depth.class);
    private static final String MAIN_CHECK_FIELD = "C28";
    private static final String MAIN_CHECK_NAME = "入院前昏迷时间（天）";
    private static final String MAIN_CHECK_REGION = "四川";
    /**
     * 颅脑损伤昏迷时间校验
     * 1. 颅脑损伤患者入院前昏迷时长
     * 2. 颅脑损伤患者入院后昏迷时长
     */
    @Override
    public Void body(ScriptExecuteWrap wrap) {
        SetlValidBaseBusiVo setlValidBaseBusiVo = (SetlValidBaseBusiVo) wrap.cmp.getFirstContextBean();
        SettleListValidateVo settleListValidateVo = (SettleListValidateVo) wrap.cmp.getRequestData();

        SomHiInvyBasInfo somHiInvyBasInfo = settleListValidateVo.getSomHiInvyBasInfo();
        SettleListHandlerVo settleListHandlerVo = new SettleListHandlerVo();
        List<BusDiseaseDiagnosisTrim> busDiseaseDiagnosisTrimsList = settleListValidateVo.getBusDiseaseDiagnosisTrimsList();
        // 检查 somHiInvyBasInfo 是否为空

        // 检查诊断信息是否为空
        if (SettleValidateUtil.isEmpty(busDiseaseDiagnosisTrimsList)) {
            return null;
        }

        // 颅脑损伤入院前和入院后的字段
        Integer[] admissionBefore = {somHiInvyBasInfo.getC28(), somHiInvyBasInfo.getC29(), somHiInvyBasInfo.getC30()};
        Integer[] admissionAfter = {somHiInvyBasInfo.getC31(), somHiInvyBasInfo.getC32(), somHiInvyBasInfo.getC33()};

        // 检查颅脑损伤入院时间是否合法
        validateAdmissionTime(admissionBefore, setlValidBaseBusiVo, "颅脑损伤入院前昏迷时间不合理");
        validateAdmissionTime(admissionAfter, setlValidBaseBusiVo, "颅脑损伤入院后昏迷时间不合理");

        //判断入院后昏迷时长是否大于住院天数a20
        String b20 =   somHiInvyBasInfo.getB20();
        // 获取入院时间
        String b12 = somHiInvyBasInfo.getB12();
        // 获取出院时间
        String b15 = somHiInvyBasInfo.getB15();
        String daysDiff = getDateDifferenceInDays(b12, b15);
        checkAfterLessThanIptDay(admissionAfter,daysDiff,b20,setlValidBaseBusiVo);
        checkReasonable(admissionBefore,admissionAfter, setlValidBaseBusiVo, busDiseaseDiagnosisTrimsList);
        return null;
    }

    public static String getDateDifferenceInDays(String date1Str, String date2Str) {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String formattedDifference = "";
        try {
            // 将时间字符串转换为 Date 对象
            Date date1 = dateFormat.parse(date1Str);
            Date date2 = dateFormat.parse(date2Str);

            // 计算时间差（毫秒）
            long differenceInMillis = date2.getTime() - date1.getTime();

            // 将毫秒差值转换为天数、小时数和分钟数
            long differenceInDays = TimeUnit.MILLISECONDS.toDays(differenceInMillis);
            long remainderMillisAfterDays = differenceInMillis % TimeUnit.DAYS.toMillis(1);
            long differenceInHours = TimeUnit.MILLISECONDS.toHours(remainderMillisAfterDays);
            long remainderMillisAfterHours = remainderMillisAfterDays % TimeUnit.HOURS.toMillis(1);
            long differenceInMinutes = TimeUnit.MILLISECONDS.toMinutes(remainderMillisAfterHours);

            // 将小时和分钟数也转为天数
            double hoursToDays = (double) differenceInHours / 24;
            double minutesToDays = (double) differenceInMinutes / (24 * 60);

            // 计算总天数，包括小时和分钟
            double totalDays = differenceInDays + hoursToDays + minutesToDays;

            // 格式化输出
            formattedDifference = String.format("%.2f", totalDays);


        } catch (ParseException e) {
            logger.error(" 实际住院天数时间转换失败: " + e.getMessage());
        }
        return formattedDifference;
    }

    private void checkAfterLessThanIptDay(Integer[] admissionAfter, String daysDiff ,String b20, SetlValidBaseBusiVo setlValidBaseBusiVo) {
        Integer c31 = admissionAfter[0];
        Integer c32 = admissionAfter[1];
        Integer c33 = admissionAfter[2];

        if ((c31 != null && c31 != 0) || (c32 != null && c32 != 0 ) || (c33 != null && c33 != 0)) {
            if(ValidateUtil.isEmpty(b20)){
                addErrorDetail(SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_ERROR_TYPE_4,
                        "c06c1",
                        "入院后昏迷时间存在，但没有实际住院天数",
                        SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
            }else{
                Integer inhospitalDays = null;
                Double inhosDays = null;
                try {
                    inhospitalDays = Integer.valueOf(b20);
                    inhosDays = Double.valueOf(daysDiff);
                } catch (NumberFormatException e) {
                    addErrorDetail(SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_ERROR_TYPE_4,
                            "b20",
                            "实际住院天数不合理",
                            SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
                }

                double totalDays = 0.0;

                if (c31 != null) {
                    totalDays += c31;  // 添加天数
                }

                if (c32 != null) {
                    totalDays += c32 / 24.0;  // 将小时转为天数
                }

                if (c33 != null) {
                    totalDays += c33 / 1440.0;  // 将分钟转为天数
                }
                if(totalDays > inhosDays){
                    addErrorDetail(SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_ERROR_TYPE_4,
                            "c06c1",
                            "入院后昏迷时间["+totalDays+"] 不应该超过实际住院天数["+b20+"]",
                            SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
                }
            }

        }
    }
    private void checkReasonable(Integer[] admissionBefore, Integer[] admissionAfter,SetlValidBaseBusiVo setlValidBaseBusiVo, List<BusDiseaseDiagnosisTrim> busDiseaseDiagnosisTrimsList) {
        boolean flag = false;
        for(Integer num : admissionBefore){
            if (num != null && num != 0) {
                flag =true;
            }
        }
        for(Integer num : admissionAfter){
            if (num != null && num != 0) {
                flag =true;
            }
        }

        if(flag){
            boolean has = false;
            Map<String, Object> dictMap = (Map<String, Object>) setlValidBaseBusiVo.getSettleListParams().get(SettleListValidateUtil.KEY_DIS_SECTION_LIST);
            List<String> codeList = new ArrayList<>();
            List<SettleListDisSectionVo> settleListDisSectionVos = (List<SettleListDisSectionVo>) dictMap.get(DrgConst.COMA_CODE_FLAG);
            for (SettleListDisSectionVo settleListDisSectionVo : settleListDisSectionVos) {
                codeList.add(settleListDisSectionVo.getDiagSec());
            }
            for(String code: codeList){
                for(BusDiseaseDiagnosisTrim busDiseaseDiagnosisTrim : busDiseaseDiagnosisTrimsList){
                    if(code.equals(busDiseaseDiagnosisTrim.getC06c1())){
                        has= true;
                    }
                }
            }
            System.out.println(codeList.toString() );
            if(!has){
                addErrorDetail(SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_ERROR_TYPE_2,
                        "c06c1",
                        "当颅脑损伤患者昏迷时间的“天”“时”“分”其中之一值≠0时，主要诊断或者其他诊断需包含与颅脑损伤诊断相关的编码;颅脑损伤相关编码如下【"+ codeList.toString()
                                +"】",
                        SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
            }
        }
    }

    private void addErrorDetail( String index,String errType,String errorFields, String errDscr, String chkStas, SetlValidBaseBusiVo baseBusiVo) {
        SettleListHandlerVo handlerVo =new SettleListHandlerVo();
        handlerVo.setIndex(index);
        handlerVo.setErrDscr(errDscr);
        handlerVo.setErrorFields(errorFields);
        handlerVo.setChkStas(chkStas);
        handlerVo.setErrType(errType);
        baseBusiVo.getErrorDetails().add(handlerVo);
    }

    private void validateAdmissionTime(Integer[] admissionTimes,SetlValidBaseBusiVo baseBusiVo, String errorMessage) {
        Integer c28 = admissionTimes[0];
        Integer c29 = admissionTimes[1];
        Integer c30 = admissionTimes[2];

        if (!SettleValidateUtil.isEmpty(c28) || !SettleValidateUtil.isEmpty(c29) || !SettleValidateUtil.isEmpty(c30)) {
            if (c28 == null || c28 < 0 || c29 == null || c29 < 0 || c29 > 24 || c30 == null || c30 < 0 || c30 > 60) {
                addErrorDetail( SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_ERROR_TYPE_4,
                        "c28", errorMessage, SettleValidateConst.VALIDATE_STATE_SUCCESS, baseBusiVo);
            }
        }
    }
}
'
WHERE script_id = 'check_brn_damg_coma_dura_after_depth';




UPDATE  `som_chain_script`
SET script_data = '
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.my.som.common.constant.DrgConst;
import com.my.som.model.dataHandle.SomHiInvyBasInfo;
import com.my.som.model.dataHandle.SomOprnOprtInfo;
import com.my.som.settlevalidate.constant.SettleValidateConst;
import com.my.som.settlevalidate.util.SettleValidateUtil;
import com.my.som.settlevalidate.vo.SetlValidBaseBusiVo;
import com.my.som.util.SettleListValidateUtil;
import com.my.som.vo.common.OprtDiffBodyParts;
import com.my.som.vo.dataHandle.SettleListDisSectionVo;
import com.my.som.vo.dataHandle.SettleListHandlerVo;
import com.my.som.vo.dataHandle.SettleListValidateVo;
import com.yomahub.liteflow.script.ScriptExecuteWrap;
import com.yomahub.liteflow.script.body.JaninoCommonScriptBody;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

public class check_oprn_code_depth implements JaninoCommonScriptBody {
    private static final Logger logger = LoggerFactory.getLogger(check_oprn_code_depth.class);
    private static final String MAIN_CHECK_NAME = "手术编码";
    private static final String MAIN_CHECK_FIELD = "c35c";
    private static final String MAIN_CHECK_NAME_1 = "手术编码";
    private static final String MAIN_CHECK_NAME_2 = "手术名称";

    private static final String MAIN_CHECK_CODE1 = "c35c";
    private static final String MAIN_CHECK_CODE2 = "c36n";
    private static final String MAIN_CHECK_REGION = "四川,湖北";
    /**
     * 手术信息质控
     * 1.判断手术代码与名称是否符合医保2.0版ICD-9标准。
     * 2.判断手术代码是否存在医保停用码。
     * 3.判断手术代码是否存在重复
     *
     * @param wrap
     * @return
     */
    @Override
    public Void body(ScriptExecuteWrap wrap) {

        SetlValidBaseBusiVo setlValidBaseBusiVo = (SetlValidBaseBusiVo) wrap.cmp.getFirstContextBean();
        SettleListValidateVo settleListValidateVo = (SettleListValidateVo) wrap.cmp.getRequestData();
        List<SomOprnOprtInfo> busOperateDiagnosisList = settleListValidateVo.getBusOperateDiagnosisList();
        // 检查手术信息是否为空
        if (SettleValidateUtil.isEmpty(busOperateDiagnosisList)) {
            return null;
        }
        SomHiInvyBasInfo somHiInvyBasInfo = settleListValidateVo.getSomHiInvyBasInfo();
        if (SettleValidateUtil.isEmpty(somHiInvyBasInfo)) {
            return null;
        }
        // 获取清单字典和ICD-10相关映射
        Map<String, Object> grepMap = (Map<String, Object>) setlValidBaseBusiVo.getSettleListParams().get(SettleListValidateUtil.KEY_ICD9_GRAY_CODE_2);
        Map<String, Object> icd9Map = (Map<String, Object>) setlValidBaseBusiVo.getSettleListParams().get(SettleListValidateUtil.KEY_ICD9);

        // 构造set判断是否存在重复编码
        Set<String> oprnCodeSet = new HashSet<>();
        Map<String, Object> dictMap = (Map<String, Object>) setlValidBaseBusiVo.getSettleListParams().get(SettleListValidateUtil.KEY_DIS_SECTION_LIST);


        //获取性别校对集合
        List<String> checkSexList = getCheckSexList(dictMap, somHiInvyBasInfo, setlValidBaseBusiVo);




        Map<String, String> oprtDiffBodyPartMap = getOprtDiffBodyPartMap(setlValidBaseBusiVo);

        //主诊端与年龄需要判断的集合
        Integer a14 = somHiInvyBasInfo.getA14();
        List<String> checkAgeCodeList = getCheckAgeCodeList(a14, dictMap);


        // 遍历操作列表
        for (int i = 0; i < busOperateDiagnosisList.size(); i++) {
            SomOprnOprtInfo somOprnOprtInfo = (SomOprnOprtInfo) busOperateDiagnosisList.get(i);
            String oprnCode = somOprnOprtInfo.getC35c();
            String oprnName = somOprnOprtInfo.getC36n();
            //检测主诊段

            if (SettleValidateUtil.isNotEmpty(oprnCode) && SettleValidateUtil.isNotEmpty(oprnCode)) {

                if (checkAgeCodeList.size() > 0) {
                    if (checkAgeCodeList.contains(oprnCode)) {
                        addErrorDetail(SettleValidateConst.VALIDATE_TYPE_2, SettleValidateConst.VALIDATE_ERROR_TYPE_1,
                                MAIN_CHECK_NAME_1,
                                "第" + (i + 1) + "个手术操作：编码为["+oprnCode+"]的" + "患者年龄为" + a14 + "岁 大于等于 11岁，手术操作编码不应填报" + MAIN_CHECK_NAME_1 + "[" + oprnCode + "]",
                                SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
                    }
                }
                //判断手术部位一致
              //  if (oprtDiffBodyPartMap.containsKey(oprnCode)) {
              //       String codes = (String) oprtDiffBodyPartMap.get(oprnCode);
              //       List<String> codeList = Arrays.asList(codes.split(","));
               //      for (SomOprnOprtInfo newSomOprnOprtInfo : busOperateDiagnosisList) {
              //           String c35c = newSomOprnOprtInfo.getC35c().toUpperCase();
              //           for (String code : codeList){
             //                if (code.toUpperCase().equals(c35c)) {
             //                    addErrorDetail(SettleValidateConst.VALIDATE_TYPE_2, SettleValidateConst.VALIDATE_ERROR_TYPE_1,
              //                           MAIN_CHECK_NAME_1,
              //                           "第" + (i + 1) + "个手术操作：编码为["+oprnCode+"]的" + " 编码 [" + oprnCode + "] 与 手术编码为 [" + c35c + "]的手术部位不匹配",
              //                           SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
              //               }
              //           }
               //      }
              //   }

              //手术性别
                if (checkSexList.size() > 0) {
                    // 判断是否和性别相符合
                    checkDiagnosisGenderConsistency(i, checkSexList, oprnCode, setlValidBaseBusiVo);
                }

                // 验证编码是否符合ICD-10标准
                checkICD10Specification(icd9Map, oprnCode, i, setlValidBaseBusiVo, oprnName, grepMap);
                // 判断是否有重复编码
                checkRepeat(oprnCodeSet, oprnCode, i, setlValidBaseBusiVo);
            } else {
                if (SettleValidateUtil.isNotEmpty(oprnCode)) {
                    addErrorDetail(SettleValidateConst.VALIDATE_TYPE_2, SettleValidateConst.VALIDATE_ERROR_TYPE_1,
                            MAIN_CHECK_CODE1,
                            "第" + (i + 1) + "个手术操作：编码为["+oprnCode+"]的" +  MAIN_CHECK_NAME_1 + "[" + oprnCode + "]为空 ",
                            SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
                }
                if (SettleValidateUtil.isNotEmpty(oprnName) ){
                    addErrorDetail(SettleValidateConst.VALIDATE_TYPE_2, SettleValidateConst.VALIDATE_ERROR_TYPE_1,
                            MAIN_CHECK_CODE2,
                            "第" + (i + 1) + "个手术操作：编码为["+oprnCode+"]的" + MAIN_CHECK_NAME_2 + "[" + oprnName + "]为空 ",
                            SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
                }
            }
        }
        return null;
    }

        private void checkDiagnosisGenderConsistency(int i, List<String> checkSexList, String diagCode, SetlValidBaseBusiVo setlValidBaseBusiVo) {
        if (checkSexList.contains(diagCode.toUpperCase().trim()) ) {
            addErrorDetail(SettleValidateConst.VALIDATE_TYPE_2, SettleValidateConst.VALIDATE_ERROR_TYPE_307,
                    MAIN_CHECK_FIELD,
                    "第" + (i + 1) + "个手术编码：" + MAIN_CHECK_NAME + "[" + diagCode + "]与性别不符",
                    SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
        }
    }


    private List<String> getCheckSexList(Map<String, Object> map, SomHiInvyBasInfo somHiInvyBasInfo, SetlValidBaseBusiVo setlValidBaseBusiVo) {
        String a12c = somHiInvyBasInfo.getA12c();
        List<String> diagSections = new ArrayList<>();
        if (!SettleValidateUtil.isEmpty(a12c)) {
            if (DrgConst.MAN.equals(a12c)) {
                List<SettleListDisSectionVo> settleListDisSectionVos = (List<SettleListDisSectionVo>) map.get("1"+DrgConst.MAN);
                for (SettleListDisSectionVo settleListDisSectionVo : settleListDisSectionVos) {
                    diagSections.add(settleListDisSectionVo.getDiagSec().trim());
                }
            } else if (DrgConst.WOMAN.equals(a12c)) {
                List<SettleListDisSectionVo> settleListDisSectionVos = (List<SettleListDisSectionVo>) map.get("1"+DrgConst.WOMAN);
                for (SettleListDisSectionVo settleListDisSectionVo : settleListDisSectionVos) {
                    diagSections.add(settleListDisSectionVo.getDiagSec().trim());
                }
            } else {
                addErrorDetail(SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_ERROR_TYPE_2,
                        MAIN_CHECK_FIELD,
                        "该性别性别填写不规范无法获取到诊断该性别编码的目录",
                        SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
            }
        } else {
            addErrorDetail(SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_ERROR_TYPE_1,
                    MAIN_CHECK_FIELD,
                    "性别为空",
                    SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
        }

        return listToUpCase(diagSections);
    }

    private List<String> listToUpCase(List<String> list) {
        List<String> upperCaseStrings = new ArrayList<>();
        for (String str : list) {
            upperCaseStrings.add(str.toUpperCase());
        }
        return upperCaseStrings;
    }

    private Map<String, String> getOprtDiffBodyPartMap(SetlValidBaseBusiVo setlValidBaseBusiVo) {
        Map<String, String> map = new HashMap<>();
        List<OprtDiffBodyParts> oprtDiffBodyPartList = (List<OprtDiffBodyParts>) setlValidBaseBusiVo.getSettleListParams().get(SettleListValidateUtil.KEY_DIFF_BODY_PARTS);
        for (OprtDiffBodyParts oprtDiffBodyParts : oprtDiffBodyPartList) {
            map.put(oprtDiffBodyParts.getCleanCode(), oprtDiffBodyParts.getOprtCode());
        }
        return map;
    }

    private List<String> getCheckAgeCodeList(Integer a14, Map<String, Object> map) {
        List<String> list = new ArrayList<>();
        if (ObjectUtils.isNotEmpty(a14) && (a14 > 11 || a14 == 11)) {
            List<SettleListDisSectionVo> settleListDisSectionVos = (List<SettleListDisSectionVo>) map.get(DrgConst.AGE_MORE_THAN_11Y);
            for (SettleListDisSectionVo settleListDisSectionVo : settleListDisSectionVos) {
                list.add(settleListDisSectionVo.getDiagSec());
            }
        }
        return list;
    }


    private void checkRepeat(Set<String> oprnCodeSet, String oprnCode, int i, SetlValidBaseBusiVo setlValidBaseBusiVo) {
        if (!oprnCodeSet.add(oprnCode)) {
            addErrorDetail(SettleValidateConst.VALIDATE_TYPE_2, SettleValidateConst.VALIDATE_ERROR_TYPE_307,
                    MAIN_CHECK_CODE1,
                    "第" + (i + 1) + "个手术操作："+  MAIN_CHECK_NAME_1 + "[" + oprnCode + "]重复",
                    SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
        }
    }


    private void checkICD10Specification(Map<String, Object> icd9Map, String oprnCode, int i, SetlValidBaseBusiVo setlValidBaseBusiVo, String oprnName, Map<String, Object> grepMap) {
        if (!icd9Map.containsKey(oprnCode)) {
            addErrorDetail(SettleValidateConst.VALIDATE_TYPE_2, SettleValidateConst.VALIDATE_ERROR_TYPE_5,
                    MAIN_CHECK_CODE1,
                    "第" + (i + 1) + "个手术操作：编码为["+oprnCode+"]的" +  MAIN_CHECK_NAME_1 + "[" + oprnCode + "]不符合医保ICD-9",
                    SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
        } else {
            // 验证名称是否符合ICD-10标准
            String icdName = (String) icd9Map.get(oprnCode);
            if (!oprnName.equals(icdName)) {
                addErrorDetail(SettleValidateConst.VALIDATE_TYPE_2, SettleValidateConst.VALIDATE_ERROR_TYPE_5,
                        MAIN_CHECK_CODE1,
                        "第" + (i + 1) + "个手术操作：编码为["+oprnCode+"]的" +  MAIN_CHECK_NAME_2 + "[" + oprnName + "]与医保编码[" + oprnCode + "]名称不符",
                        SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
            }
        }
        // 检查操作编码是否存在医保停用码
        if (grepMap.containsKey(oprnCode)) {
            addErrorDetail(SettleValidateConst.VALIDATE_TYPE_2, SettleValidateConst.VALIDATE_ERROR_TYPE_13,
                    MAIN_CHECK_CODE1,
                    "第" + (i + 1) + "个手术操作：编码为["+oprnCode+"]的" +  MAIN_CHECK_NAME_1 + "[" + oprnCode + "]是医保停用码",
                    SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
        }
    }

    private void addErrorDetail(String index, String errType, String errorFields, String errDscr, String chkStas, SetlValidBaseBusiVo baseBusiVo) {
        SettleListHandlerVo handlerVo = new SettleListHandlerVo();
        handlerVo.setIndex(index);
        handlerVo.setErrorFields(errorFields);
        handlerVo.setErrDscr(errDscr);
        handlerVo.setChkStas(chkStas);
        handlerVo.setErrType(errType);
        baseBusiVo.getErrorDetails().add(handlerVo);
    }
}
'
WHERE script_id = 'check_oprn_code_depth';

UPDATE  `som_chain_script`
SET script_data = 'import com.my.som.common.util.ValidateUtil;
import com.my.som.model.dataHandle.SomHiInvyBasInfo;
import com.my.som.model.medicalQuality.SomSetlInvyScsCutdInfo;
import com.my.som.settlevalidate.constant.SettleValidateConst;
import com.my.som.settlevalidate.util.SettleValidateUtil;
import com.my.som.settlevalidate.vo.SetlValidBaseBusiVo;
import com.my.som.vo.dataHandle.SettleListHandlerVo;
import com.my.som.vo.dataHandle.SettleListValidateVo;
import com.yomahub.liteflow.script.ScriptExecuteWrap;
import com.yomahub.liteflow.script.body.JaninoCommonScriptBody;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.concurrent.TimeUnit;

/**
 * 事后判断 重症监护合计时长
 */
public class check_scs_cutd_sum_dura_after_depth implements JaninoCommonScriptBody {
    private static final Logger logger = LoggerFactory.getLogger(check_scs_cutd_sum_dura_after_depth.class);

    private static final String MAIN_CHECK_FIELD = "scsCutdSumDura";
    private static final String MAIN_CHECK_NAME = "重症监护合计时长";
    private static final String MAIN_CHECK_FIELD_1 = "scsCutdInpoolTime";
    private static final String MAIN_CHECK_NAME_1 = "重症监护进入时间";
    private static final String MAIN_CHECK_FIELD_2 = "scsCutdExitTime";
    private static final String MAIN_CHECK_NAME_2 = "重症监护退出时间";
    private static final Pattern PATTERN = Pattern.compile(SettleValidateUtil.SCS_CUTD_SUM_DURA_PATTERN);
    private static final String MAIN_CHECK_FIELD_3 = "b12";
    private static final String MAIN_CHECK_FIELD_4 = "b15";
    private static final String MAIN_CHECK_NAME_3 = "结算清单";
    private static final String MAIN_CHECK_REGION = "四川";

    /**
     * 重症监护合计时长(scs_cutd_sum_dura)基础质控
     * 重症监护合计时长是否合理
     *
     * @param wrap
     * @return
     */
    @Override
    public Void body(ScriptExecuteWrap wrap) {
        SetlValidBaseBusiVo setlValidBaseBusiVo = (SetlValidBaseBusiVo) wrap.cmp.getFirstContextBean();
        SettleListValidateVo settleListValidateVo = (SettleListValidateVo) wrap.cmp.getRequestData();
        Map<String, String> keysBasicResultMap = setlValidBaseBusiVo.getKeysBasicResultMap();
        SettleListHandlerVo settleListHandlerVo = new SettleListHandlerVo();
        SomHiInvyBasInfo somHiInvyBasInfo = settleListValidateVo.getSomHiInvyBasInfo();
        // 检查 somHiInvyBasInfo 是否为空
        if (SettleValidateUtil.isEmpty(somHiInvyBasInfo)) {
            addErrorDetail(SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY, SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY,
                    (String) SettleValidateConst.CHECK_TYPE_MAP.get(SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY), MAIN_CHECK_FIELD_1,
                    SettleValidateConst.VALIDATE_STATE_FAIL, setlValidBaseBusiVo);
            return null;
        }

        List<SomSetlInvyScsCutdInfo> busIcuList = settleListValidateVo.getBusIcuList();
        // 检查 somHiInvyBasInfo 是否为空
        if (SettleValidateUtil.isEmpty(busIcuList)) {
            return null;
        }

        if (SettleValidateConst.PASS.equals(keysBasicResultMap.get(MAIN_CHECK_FIELD_3))
                && SettleValidateConst.PASS.equals(keysBasicResultMap.get(MAIN_CHECK_FIELD_4))) {
            Date b12 = StringToConvert(somHiInvyBasInfo.getB12(), "入院时间");
            Date b15 = StringToConvert(somHiInvyBasInfo.getB15(), "出院时间");
            Date mimDate =StringToConvert("9999-12-31 00:00:00", "重症最大时间错误") ;
            Date maxDate = StringToConvert("0000-01-01 00:00:00", "重症最小时间错误") ;

            Set<String> ICUCodeSet = new HashSet<>();;
            boolean flag = false;
            for (int i = 0; i < busIcuList.size(); i++) {
                SomSetlInvyScsCutdInfo somSetlInvyScsCutdInfo = (SomSetlInvyScsCutdInfo) busIcuList.get(i);
                if (SettleValidateConst.PASS.equals(keysBasicResultMap.get(i + MAIN_CHECK_FIELD_1))) {
                    Date inpool = StringToConvert(i, somSetlInvyScsCutdInfo.getScsCutdInpoolTime(), MAIN_CHECK_FIELD_1, MAIN_CHECK_NAME_1,
                            somSetlInvyScsCutdInfo.getSettleListId(), setlValidBaseBusiVo,i==0?true:false);
                    Date exitTime = StringToConvert(i, somSetlInvyScsCutdInfo.getScsCutdExitTime(), MAIN_CHECK_FIELD_2, MAIN_CHECK_NAME_2,
                            somSetlInvyScsCutdInfo.getSettleListId(), setlValidBaseBusiVo,i==0?true:false);

                    //如果大于入院时间
                    if (inpool.before(b12)) {
                        addErrorDetail(SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_ERROR_TYPE_2,
                                MAIN_CHECK_FIELD_1,
                                "第" + (i + 1) + "个重症监护: id为[" + somSetlInvyScsCutdInfo.getId() + "]的"
                                        + MAIN_CHECK_NAME_1 + "[" + somSetlInvyScsCutdInfo.getScsCutdInpoolTime()
                                        + "]小于于入院时间[" + somHiInvyBasInfo.getB12() + "]",
                                SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
                    }

                    //是否小于出院时间
                    if (b15.before(exitTime)) {
                        addErrorDetail(SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_ERROR_TYPE_2,
                                MAIN_CHECK_FIELD_2,
                                "第" + (i + 1) + "个重症监护: id为[" + somSetlInvyScsCutdInfo.getId() + "]的"
                                        +  MAIN_CHECK_NAME_2 + "[" + somSetlInvyScsCutdInfo.getScsCutdExitTime()
                                        + "]大于出院时间[" + somHiInvyBasInfo.getB15() + "]",
                                SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
                    }

                    if (exitTime.before(inpool)) {
                        addErrorDetail(SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_ERROR_TYPE_2,
                                MAIN_CHECK_FIELD_1,
                                "第" + (i + 1) + "个重症监护: id为[" + somSetlInvyScsCutdInfo.getId() + "]的"
                                        +  MAIN_CHECK_NAME_2 + "[" + somSetlInvyScsCutdInfo.getScsCutdInpoolTime() + "]大于"
                                        + MAIN_CHECK_NAME_1 + "[" + somSetlInvyScsCutdInfo.getScsCutdExitTime() + "]",
                                SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
                    }
                    if (SettleValidateUtil.isNotEmpty(somSetlInvyScsCutdInfo.getScsCutdSumDura())) {
                        //格式规范
                        if (!isValidFormat(somSetlInvyScsCutdInfo.getScsCutdSumDura())) {
                            addErrorDetail(SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_ERROR_TYPE_2,
                                    MAIN_CHECK_FIELD,
                                    "第" + (i + 1) + "个重症监护: id为[" + somSetlInvyScsCutdInfo.getId() + "]的" +  MAIN_CHECK_NAME + "[" + somSetlInvyScsCutdInfo.getScsCutdSumDura() + "]的日期填写格式错误",
                                    SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
                        } else {
                            //比较时间是否能匹配上
                            String diff = getDifference(somSetlInvyScsCutdInfo.getSettleListId(), somSetlInvyScsCutdInfo.getScsCutdInpoolTime(), somSetlInvyScsCutdInfo.getScsCutdExitTime());
                            if (!diff.equals(somSetlInvyScsCutdInfo.getScsCutdSumDura())) {
                                addErrorDetail(SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_ERROR_TYPE_2,
                                        MAIN_CHECK_FIELD,
                                        "第" + (i + 1) + "个重症监护: id为[" + somSetlInvyScsCutdInfo.getId() + "]的" +  MAIN_CHECK_NAME + "[" + somSetlInvyScsCutdInfo.getScsCutdSumDura() + "] 与 出入重症监护室时间差额 [" + diff + "] 不符",
                                        SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
                            }
                        }
                    } else {
                        addErrorDetail(SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_ERROR_TYPE_1,
                                MAIN_CHECK_FIELD,
                                "第" + (i + 1) + "个重症监护: id为[" + somSetlInvyScsCutdInfo.getId() + "]的" +  MAIN_CHECK_NAME + "[" + somSetlInvyScsCutdInfo.getScsCutdSumDura() + "]为空",
                                SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
                    }
                    //判断时间不重叠
                    for (int j = i + 1; j < busIcuList.size(); j++) {
                        SomSetlInvyScsCutdInfo somSetlInvyScsCutdInfoNext = (SomSetlInvyScsCutdInfo) busIcuList.get(j);
                        System.out.println(SettleValidateConst.PASS.equals(keysBasicResultMap.get(j + MAIN_CHECK_FIELD_1)));
                        if (SettleValidateConst.PASS.equals(keysBasicResultMap.get(j + MAIN_CHECK_FIELD_1))) {
                            Date inpoolNext = StringToConvert(j, somSetlInvyScsCutdInfoNext.getScsCutdInpoolTime(), MAIN_CHECK_FIELD_1, MAIN_CHECK_NAME_1,
                                    somSetlInvyScsCutdInfo.getSettleListId(), setlValidBaseBusiVo,true);

                            Date exitNext = StringToConvert(j, somSetlInvyScsCutdInfoNext.getScsCutdExitTime(), MAIN_CHECK_FIELD_2, MAIN_CHECK_NAME_2,
                                    somSetlInvyScsCutdInfo.getSettleListId(), setlValidBaseBusiVo,true);

                            if ((inpoolNext.before(inpool)  && inpool.before(exitNext))
                                    || (inpoolNext.before(exitTime)  && exitTime.before(exitNext)) ) {
                                // 如果当前时间段与其它时间段重叠，设置标志为false
                                addErrorDetail(SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_ERROR_TYPE_1,
                                        MAIN_CHECK_FIELD,
                                        "第" + (i + 1) + "个重症监护与第"+ (j + 1) +"个重症监护的进入时间冲突。" ,
                                        SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
                            }
                        }
                    }

                    boolean inpoolflag=  !ICUCodeSet.add(inpool.toString());
                    boolean exitflag=  !ICUCodeSet.add(exitTime.toString());

                    if ( inpoolflag &&exitflag){
                        addErrorDetail(SettleValidateConst.VALIDATE_TYPE_2, SettleValidateConst.VALIDATE_ERROR_TYPE_304,
                                MAIN_CHECK_FIELD_1,
                                "第" + (i + 1) + "个重症监护记录重复",
                                SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
                    }
                    else {
                        if (inpoolflag ) {
                            addErrorDetail(SettleValidateConst.VALIDATE_TYPE_2, SettleValidateConst.VALIDATE_ERROR_TYPE_304,
                                    MAIN_CHECK_FIELD_1,
                                    "第" + (i + 1) + "个重症监护记录：" + MAIN_CHECK_NAME_1 + "[" + somSetlInvyScsCutdInfo.getScsCutdInpoolTime() + "]重复",
                                    SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
                        }

                        if (exitflag) {
                            addErrorDetail(SettleValidateConst.VALIDATE_TYPE_2, SettleValidateConst.VALIDATE_ERROR_TYPE_304,
                                    MAIN_CHECK_FIELD_1,
                                    "第" + (i + 1) + "个重症监护记录：" + MAIN_CHECK_NAME_2 + "[" + somSetlInvyScsCutdInfo.getScsCutdExitTime() + "]重复",
                                    SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
                        }
                    }
                    //此时判断实际住院时间不为空且住院类型不为日间手术
                    if(!ValidateUtil.isEmpty(somHiInvyBasInfo.getB20()) ){
                        if (ValidateUtil.isEmpty(somHiInvyBasInfo.getB38()) || !"2".equals(somHiInvyBasInfo.getB38())){
                            if (judgeTimeOK(inpool, mimDate)) {
                                mimDate = inpool;
                            }
                            if (judgeTimeOK(maxDate, exitTime)) {
                                maxDate = exitTime;
                            }
                            flag = true;
                        }
                    }

                }
            }
            if (flag) {
                //判断

                //double iptDay120rate = Long.parseLong(somHiInvyBasInfo.getB20())*1.2;
                double iptDay120rate = getDateDifferenceInDays(b12, b15);

                double daysDiff = getDateDifferenceInDays(mimDate, maxDate);

                if (daysDiff > iptDay120rate) {
                    addErrorDetail(SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_ERROR_TYPE_2,
                            MAIN_CHECK_FIELD,
                            "ICU累计住院时间["+daysDiff+"]不能超过实际住院天数120%["+iptDay120rate+"]",
                            SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
                }
            }
        }

        return null;
    }
    public  double getDateDifferenceInDays(Date date1, Date date2) {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String formattedDifference = "";
        // 计算时间差（毫秒）
        long differenceInMillis = date2.getTime() - date1.getTime();

        // 将毫秒差值转换为天数、小时数和分钟数
        double differenceInDays = (double) differenceInMillis / TimeUnit.DAYS.toMillis(1);

        return differenceInDays;
    }
    /**
     * 判断时间2 大于时间1
     *
     * @param time1
     * @param time2
     * @return
     */
    private boolean judgeTimeOK(Date time1, Date time2) {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        boolean flag = false;
        // 入院时间大于出院时间
        if (time2.after(time1)) {
            flag = true;
        }
        return flag;
    }

    private String getDifference(Long settleListId, String time1, String time2) {
        // 定义时间格式
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String formattedDifference = "";
        try {
            // 将时间字符串转换为 Date 对象
            Date date1 = dateFormat.parse(time1);
            Date date2 = dateFormat.parse(time2);

            // 计算时间差（毫秒）
            long differenceInMillis = date2.getTime() - date1.getTime();

            // 将毫秒差值转换为天数、小时数和分钟数
            long differenceInDays = TimeUnit.MILLISECONDS.toDays(differenceInMillis);
            long remainderMillisAfterDays = differenceInMillis % TimeUnit.DAYS.toMillis(1);
            long differenceInHours = TimeUnit.MILLISECONDS.toHours(remainderMillisAfterDays);
            long remainderMillisAfterHours = remainderMillisAfterDays % TimeUnit.HOURS.toMillis(1);
            long differenceInMinutes = TimeUnit.MILLISECONDS.toMinutes(remainderMillisAfterHours);
            if("00".equals(differenceInMinutes)){
                differenceInMinutes = 0;
            }
            if("00".equals(differenceInHours)){
                differenceInHours = 0;
            }
            // 格式化天数/小时数/分钟数字符串
            formattedDifference = String.format("%d/%d/%d",
                    differenceInDays, differenceInHours, differenceInMinutes);


        } catch (ParseException e) {
            logger.error("清单编码为[" + settleListId + "] 计算重症时间差失败: " + e.getMessage());
        }
        return formattedDifference;
    }


    /**
     * 判断格式是否规范
     *
     * @param input
     * @return
     */
    public boolean isValidFormat(String input) {

        // 简化的正则表达式：匹配两个正整数用斜杠分隔
        Matcher matcher = PATTERN.matcher(input);

        if (matcher.matches()) {
            try {
                int days = Integer.parseInt(matcher.group(1));
                int hours = Integer.parseInt(matcher.group(2));
                int minutes = Integer.parseInt(matcher.group(3));
                // 可选：进一步验证值的范围
                if (days >= 0 && hours >= 0 && hours < 24 && minutes >= 0 && minutes < 60) {
                    return true;
                }
            } catch (NumberFormatException e) {
                // 处理转换错误
                return false;
            }
        }
        return false;
    }

    private void addErrorDetail(String index, String errType, String errorFields, String errDscr, String chkStas, SetlValidBaseBusiVo baseBusiVo) {
        SettleListHandlerVo handlerVo = new SettleListHandlerVo();
        handlerVo.setIndex(index);
        handlerVo.setErrDscr(errDscr);
        handlerVo.setErrorFields(errorFields);
        handlerVo.setChkStas(chkStas);
        handlerVo.setErrType(errType);
        baseBusiVo.getErrorDetails().add(handlerVo);
    }

    private Date StringToConvert(int i, String time, String field, String name, Long settleListId,
                                 SetlValidBaseBusiVo setlValidBaseBusiVo,boolean flag) {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date date = new Date();
        try {
            date = dateFormat.parse(time);

        } catch (ParseException e) {
            if(flag) {
                addErrorDetail(SettleValidateConst.VALIDATE_TYPE_2, SettleValidateConst.VALIDATE_ERROR_TYPE_2,
                        field,
                        "第" + (i + 1) + "个的" + name + "[" + time + "]日期格式不符合规范",
                        SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
            }
            logger.error("清单编码为[" + settleListId + "]的" + name + "日期解析失败: " + e.getMessage());
        }
        return date;
    }

    private Date StringToConvert(String time, String name) {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date date = new Date();
        try {
            date = dateFormat.parse(time);
        } catch (ParseException e) {
            logger.error(name + ":[" + time + "]" + "日期解析失败: " + e.getMessage());
        }
        return date;
    }
}
'
WHERE script_id = 'check_scs_cutd_sum_dura_after_depth'
-- updateby lihongxiang 202050606 end