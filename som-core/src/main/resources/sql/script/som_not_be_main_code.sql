
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for som_not_be_main_code
-- ----------------------------
DROP TABLE IF EXISTS `som_not_be_main_code`;
CREATE TABLE `som_not_be_main_code`  (
  `ID` int NOT NULL AUTO_INCREMENT COMMENT '不能作为主要诊断id',
  `code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'ICD-10（医保2.0）编码',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'ICD-10（医保2.0）名称',
  `dscr` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '描述',
  `enable` tinyint NULL DEFAULT 1 COMMENT '启用状态 0：禁用 1：启用',
  `type` tinyint NULL DEFAULT NULL COMMENT '类型 1：不能为主要诊断  2 不能为主要手术',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 853 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of som_not_be_main_code
-- ----------------------------
INSERT INTO `som_not_be_main_code` VALUES (1, 'B95.000', 'A族链球菌作为分类于其他章疾病的原因', '(该疾病编码)(该疾病编码名称)，标明分类于他处疾病中的传染性病原体，不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (2, 'B95.100', 'B族链球菌作为分类于其他章疾病的原因', '(该疾病编码)(该疾病编码名称)，标明分类于他处疾病中的传染性病原体，不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (3, 'B95.200', 'D组链球菌和肠球菌疾病作为其他章节疾病分类的原因', '(该疾病编码)(该疾病编码名称)，标明分类于他处疾病中的传染性病原体，不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (4, 'B95.300', '肺炎链球菌作为分类于其他章疾病的原因', '(该疾病编码)(该疾病编码名称)，标明分类于他处疾病中的传染性病原体，不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (5, 'B95.400', '链球菌作为分类于其他章疾病的原因，其他的', '(该疾病编码)(该疾病编码名称)，标明分类于他处疾病中的传染性病原体，不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (6, 'B95.500', '链球菌作为分类于其他章疾病的原因', '(该疾病编码)(该疾病编码名称)，标明分类于他处疾病中的传染性病原体，不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (7, 'B95.600', '金黄色葡萄球菌作为分类于其他章疾病的原因', '(该疾病编码)(该疾病编码名称)，标明分类于他处疾病中的传染性病原体，不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (8, 'B95.700', '葡萄球菌，其他的，作为分类于其他章疾病的原因', '(该疾病编码)(该疾病编码名称)，标明分类于他处疾病中的传染性病原体，不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (9, 'B95.800', '葡萄球菌作为分类于其他章疾病的原因', '(该疾病编码)(该疾病编码名称)，标明分类于他处疾病中的传染性病原体，不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (10, 'B96.000', '肺炎支原体作为分类于其他章疾病的原因', '(该疾病编码)(该疾病编码名称)，标明分类于他处疾病中的传染性病原体，不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (11, 'B96.100', '肺炎杆菌作为分类于其他章疾病的原因', '(该疾病编码)(该疾病编码名称)，标明分类于他处疾病中的传染性病原体，不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (12, 'B96.200', '大肠杆菌作为分类于其他章疾病的原因', '(该疾病编码)(该疾病编码名称)，标明分类于他处疾病中的传染性病原体，不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (13, 'B96.300', '流感嗜血杆菌作为分类于其他章疾病的原因', '(该疾病编码)(该疾病编码名称)，标明分类于他处疾病中的传染性病原体，不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (14, 'B96.400', '变形菌（奇异）（摩根）作为分类于其他章的疾病的原因', '(该疾病编码)(该疾病编码名称)，标明分类于他处疾病中的传染性病原体，不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (15, 'B96.500', '假单胞菌属（铜绿）作为分类于其他章疾病的原因', '(该疾病编码)(该疾病编码名称)，标明分类于他处疾病中的传染性病原体，不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (16, 'B96.501', '绿脓杆菌感染', '(该疾病编码)(该疾病编码名称)，标明分类于他处疾病中的传染性病原体，不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (17, 'B96.600', '脆弱（微小）杆菌作为分类于其他章疾病的原因', '(该疾病编码)(该疾病编码名称)，标明分类于他处疾病中的传染性病原体，不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (18, 'B96.700', '产气荚膜梭状芽胞杆菌作为分类于其他章疾病的原因', '(该疾病编码)(该疾病编码名称)，标明分类于他处疾病中的传染性病原体，不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (19, 'B96.800', '细菌性病原体作为分类于其他章疾病的原因，其他特指的', '(该疾病编码)(该疾病编码名称)，标明分类于他处疾病中的传染性病原体，不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (20, 'B96.801', '难辨梭状芽胞杆菌感染', '(该疾病编码)(该疾病编码名称)，标明分类于他处疾病中的传染性病原体，不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (21, 'B97.000', '腺病毒作为分类于其他章疾病的原因', '(该疾病编码)(该疾病编码名称)，标明分类于他处疾病中的传染性病原体，不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (22, 'B97.100', '肠病毒作为分类于其他章疾病的原因', '(该疾病编码)(该疾病编码名称)，标明分类于他处疾病中的传染性病原体，不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (23, 'B97.200', '冠状病毒作为分类于其他章疾病的原因', '(该疾病编码)(该疾病编码名称)，标明分类于他处疾病中的传染性病原体，不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (24, 'B97.300', '反转录病毒作为分类于其他章疾病的原因', '(该疾病编码)(该疾病编码名称)，标明分类于他处疾病中的传染性病原体，不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (25, 'B97.400', '呼吸道合胞体病毒作为分类于其他章疾病的原因', '(该疾病编码)(该疾病编码名称)，标明分类于他处疾病中的传染性病原体，不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (26, 'B97.500', '呼肠孤病毒作为分类于其他章疾病的原因', '(该疾病编码)(该疾病编码名称)，标明分类于他处疾病中的传染性病原体，不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (27, 'B97.600', '细小病毒作为分类于其他章疾病的原因', '(该疾病编码)(该疾病编码名称)，标明分类于他处疾病中的传染性病原体，不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (28, 'B97.700', '乳头状瘤病毒作为分类于其他章疾病的原因', '(该疾病编码)(该疾病编码名称)，标明分类于他处疾病中的传染性病原体，不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (29, 'B97.800', '病毒性病原体，其他的作为分类于其他章疾病的原因', '(该疾病编码)(该疾病编码名称)，标明分类于他处疾病中的传染性病原体，不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (30, 'B98.000', '幽门螺杆菌作为其他章节疾病分类的原因', '(该疾病编码)(该疾病编码名称)，标明分类于他处疾病中的传染性病原体，不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (31, 'B98.100', '创伤弧菌作为其他章节的疾病分类的原因', '(该疾病编码)(该疾病编码名称)，标明分类于他处疾病中的传染性病原体，不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (32, 'I50.900x007', '心功能Ⅱ级（NYHA\n分级）', '(该疾病编码)(该疾病编码名称)，心功能分级不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (33, 'I50.900x008', '心功能III级\n（NYHA分级）', '(该疾病编码)(该疾病编码名称)，心功能分级不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (34, 'I50.900x009', '心功能Ⅱ--Ⅲ级\n（NYHA分级）', '(该疾病编码)(该疾病编码名称)，心功能分级不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (35, 'I50.900x010', '心功能IV级（NYHA\n分级）', '(该疾病编码)(该疾病编码名称)，心功能分级不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (36, 'I50.900x014', 'KillipII级', '(该疾病编码)(该疾病编码名称)，心功能分级不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (37, 'I50.900x015', 'KillipIII级', '(该疾病编码)(该疾病编码名称)，心功能分级不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (38, 'I50.900x016', 'KillipIV级', '(该疾病编码)(该疾病编码名称)，心功能分级不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (39, 'O26.900', '与妊娠有关的情况', '(该疾病编码)(该疾病编码名称)，孕周信息不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (40, 'O26.900x001', '孕<5周', '(该疾病编码)(该疾病编码名称)，孕周信息不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (41, 'O26.900x101', '孕5周', '(该疾病编码)(该疾病编码名称)，孕周信息不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (42, 'O26.900x102', '孕6周', '(该疾病编码)(该疾病编码名称)，孕周信息不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (43, 'O26.900x103', '孕7周', '(该疾病编码)(该疾病编码名称)，孕周信息不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (44, 'O26.900x104', '孕8周', '(该疾病编码)(该疾病编码名称)，孕周信息不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (45, 'O26.900x105', '孕9周', '(该疾病编码)(该疾病编码名称)，孕周信息不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (46, 'O26.900x106', '孕10周', '(该疾病编码)(该疾病编码名称)，孕周信息不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (47, 'O26.900x107', '孕11周', '(该疾病编码)(该疾病编码名称)，孕周信息不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (48, 'O26.900x108', '孕12周', '(该疾病编码)(该疾病编码名称)，孕周信息不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (49, 'O26.900x109', '孕13周', '(该疾病编码)(该疾病编码名称)，孕周信息不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (50, 'O26.900x201', '孕14周', '(该疾病编码)(该疾病编码名称)，孕周信息不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (51, 'O26.900x202', '孕15周', '(该疾病编码)(该疾病编码名称)，孕周信息不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (52, 'O26.900x203', '孕16周', '(该疾病编码)(该疾病编码名称)，孕周信息不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (53, 'O26.900x204', '孕17周', '(该疾病编码)(该疾病编码名称)，孕周信息不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (54, 'O26.900x205', '孕18周', '(该疾病编码)(该疾病编码名称)，孕周信息不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (55, 'O26.900x206', '孕19周', '(该疾病编码)(该疾病编码名称)，孕周信息不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (56, 'O26.900x301', '孕20周', '(该疾病编码)(该疾病编码名称)，孕周信息不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (57, 'O26.900x302', '孕21周', '(该疾病编码)(该疾病编码名称)，孕周信息不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (58, 'O26.900x303', '孕22周', '(该疾病编码)(该疾病编码名称)，孕周信息不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (59, 'O26.900x304', '孕23周', '(该疾病编码)(该疾病编码名称)，孕周信息不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (60, 'O26.900x305', '孕24周', '(该疾病编码)(该疾病编码名称)，孕周信息不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (61, 'O26.900x306', '孕25周', '(该疾病编码)(该疾病编码名称)，孕周信息不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (62, 'O26.900x401', '孕26周', '(该疾病编码)(该疾病编码名称)，孕周信息不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (63, 'O26.900x402', '孕27周', '(该疾病编码)(该疾病编码名称)，孕周信息不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (64, 'O26.900x403', '孕28周', '(该疾病编码)(该疾病编码名称)，孕周信息不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (65, 'O26.900x404', '孕29周', '(该疾病编码)(该疾病编码名称)，孕周信息不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (66, 'O26.900x405', '孕30周', '(该疾病编码)(该疾病编码名称)，孕周信息不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (67, 'O26.900x406', '孕31周', '(该疾病编码)(该疾病编码名称)，孕周信息不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (68, 'O26.900x407', '孕32周', '(该疾病编码)(该疾病编码名称)，孕周信息不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (69, 'O26.900x408', '孕33周', '(该疾病编码)(该疾病编码名称)，孕周信息不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (70, 'O26.900x501', '孕34周', '(该疾病编码)(该疾病编码名称)，孕周信息不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (71, 'O26.900x502', '孕35周', '(该疾病编码)(该疾病编码名称)，孕周信息不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (72, 'O26.900x503', '孕36周', '(该疾病编码)(该疾病编码名称)，孕周信息不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (73, 'O26.900x504', '孕37周', '(该疾病编码)(该疾病编码名称)，孕周信息不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (74, 'O26.900x505', '孕38周', '(该疾病编码)(该疾病编码名称)，孕周信息不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (75, 'O26.900x506', '孕39周', '(该疾病编码)(该疾病编码名称)，孕周信息不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (76, 'O26.900x507', '孕40周', '(该疾病编码)(该疾病编码名称)，孕周信息不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (77, 'O26.900x508', '孕41周', '(该疾病编码)(该疾病编码名称)，孕周信息不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (78, 'O26.900x509', '孕42周', '(该疾病编码)(该疾病编码名称)，孕周信息不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (79, 'O26.900x510', '孕>42周', '(该疾病编码)(该疾病编码名称)，孕周信息不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (80, 'O26.900x601', '孕0次', '(该疾病编码)(该疾病编码名称)，孕周信息不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (81, 'O26.900x602', '孕1次', '(该疾病编码)(该疾病编码名称)，孕周信息不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (82, 'O26.900x603', '孕2次', '(该疾病编码)(该疾病编码名称)，孕周信息不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (83, 'O26.900x604', '孕3次', '(该疾病编码)(该疾病编码名称)，孕周信息不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (84, 'O26.900x605', '孕4次', '(该疾病编码)(该疾病编码名称)，孕周信息不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (85, 'O26.900x606', '孕5次', '(该疾病编码)(该疾病编码名称)，孕周信息不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (86, 'O26.900x607', '孕6次', '(该疾病编码)(该疾病编码名称)，孕周信息不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (87, 'O26.900x608', '孕7次', '(该疾病编码)(该疾病编码名称)，孕周信息不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (88, 'O26.900x609', '孕8次', '(该疾病编码)(该疾病编码名称)，孕周信息不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (89, 'O26.900x610', '孕9次', '(该疾病编码)(该疾病编码名称)，孕周信息不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (90, 'O26.900x611', '孕10次', '(该疾病编码)(该疾病编码名称)，孕周信息不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (91, 'O26.900x701', '产0次', '(该疾病编码)(该疾病编码名称)，孕周信息不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (92, 'O26.900x702', '产1次', '(该疾病编码)(该疾病编码名称)，孕周信息不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (93, 'O26.900x703', '产2次', '(该疾病编码)(该疾病编码名称)，孕周信息不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (94, 'O26.900x704', '产3次', '(该疾病编码)(该疾病编码名称)，孕周信息不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (95, 'O26.900x705', '产4次', '(该疾病编码)(该疾病编码名称)，孕周信息不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (96, 'O26.900x706', '产5次', '(该疾病编码)(该疾病编码名称)，孕周信息不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (97, 'O26.900x707', '产6次', '(该疾病编码)(该疾病编码名称)，孕周信息不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (98, 'O26.900x708', '产7次', '(该疾病编码)(该疾病编码名称)，孕周信息不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (99, 'O26.900x709', '产8次', '(该疾病编码)(该疾病编码名称)，孕周信息不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (100, 'O26.900x710', '产9次', '(该疾病编码)(该疾病编码名称)，孕周信息不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (101, 'O26.900x711', '产10次', '(该疾病编码)(该疾病编码名称)，孕周信息不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (102, 'R65.000', '传染性病因的全身炎症反应综合征不伴有器官衰竭', '(该疾病编码)(该疾病编码名称)，应作为附加编码以标明任何原因导致的这一状况，不能作为主要诊断编码。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (103, 'R65.100', '传染性病因的全身炎症反应综合征伴有器官衰竭', '(该疾病编码)(该疾病编码名称)，应作为附加编码以标明任何原因导致的这一状况，不能作为主要诊断编码。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (104, 'R65.101', '重症脓毒症', '(该疾病编码)(该疾病编码名称)，应作为附加编码以标明任何原因导致的这一状况，不能作为主要诊断编码。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (105, 'R65.200', '非传染性病因的全身炎症反应综合征不伴有器官衰竭', '(该疾病编码)(该疾病编码名称)，应作为附加编码以标明任何原因导致的这一状况，不能作为主要诊断编码。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (106, 'R65.300', '非传染性病因的全身炎症反应综合征伴有器官衰竭', '(该疾病编码)(该疾病编码名称)，应作为附加编码以标明任何原因导致的这一状况，不能作为主要诊断编码。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (107, 'R65.301', '非感染性多器官功\n能障碍综合征\n（MODS）', '(该疾病编码)(该疾病编码名称)，应作为附加编码以标明任何原因导致的这一状况，不能作为主要诊断编码。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (108, 'R65.900', '全身炎症反应综合征', '(该疾病编码)(该疾病编码名称)，应作为附加编码以标明任何原因导致的这一状况，不能作为主要诊断编码。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (109, 'T31.000', '累及体表10%以下的烧伤', '(该疾病编码)(该疾病编码名称)，说明烧伤和腐蚀的体表面积，不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (110, 'T31.100', '累及体表10%~19%的烧伤', '(该疾病编码)(该疾病编码名称)，说明烧伤和腐蚀的体表面积，不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (111, 'T31.200', '累及体表20%~29%的烧伤', '(该疾病编码)(该疾病编码名称)，说明烧伤和腐蚀的体表面积，不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (112, 'T31.300', '累及体表30%~39%的烧伤', '(该疾病编码)(该疾病编码名称)，说明烧伤和腐蚀的体表面积，不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (113, 'T31.400', '累及体表40%~49%的烧伤', '(该疾病编码)(该疾病编码名称)，说明烧伤和腐蚀的体表面积，不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (114, 'T31.500', '累及体表50%~59%的烧伤', '(该疾病编码)(该疾病编码名称)，说明烧伤和腐蚀的体表面积，不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (115, 'T31.600', '累及体表60%~69%的烧伤', '(该疾病编码)(该疾病编码名称)，说明烧伤和腐蚀的体表面积，不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (116, 'T31.700', '累及体表70%~79%的烧伤', '(该疾病编码)(该疾病编码名称)，说明烧伤和腐蚀的体表面积，不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (117, 'T31.800', '累及体表80%~89%的烧伤', '(该疾病编码)(该疾病编码名称)，说明烧伤和腐蚀的体表面积，不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (118, 'T31.900', '累及体表90%及以上的烧伤', '(该疾病编码)(该疾病编码名称)，说明烧伤和腐蚀的体表面积，不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (119, 'T32.000', '累及体表10%以下的腐蚀伤', '(该疾病编码)(该疾病编码名称)，说明烧伤和腐蚀的体表面积，不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (120, 'T32.100', '累及体表10%~19%的腐蚀伤', '(该疾病编码)(该疾病编码名称)，说明烧伤和腐蚀的体表面积，不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (121, 'T32.200', '累及体表20%~29%的腐蚀伤', '(该疾病编码)(该疾病编码名称)，说明烧伤和腐蚀的体表面积，不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (122, 'T32.300', '累及体表30%~39%的腐蚀伤', '(该疾病编码)(该疾病编码名称)，说明烧伤和腐蚀的体表面积，不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (123, 'T32.400', '累及体表40%~49%的腐蚀伤', '(该疾病编码)(该疾病编码名称)，说明烧伤和腐蚀的体表面积，不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (124, 'T32.500', '累及体表50%~59%的腐蚀伤', '(该疾病编码)(该疾病编码名称)，说明烧伤和腐蚀的体表面积，不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (125, 'T32.600', '累及体表60%~69%的腐蚀伤', '(该疾病编码)(该疾病编码名称)，说明烧伤和腐蚀的体表面积，不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (126, 'T32.700', '累及体表70%~79%的腐蚀伤', '(该疾病编码)(该疾病编码名称)，说明烧伤和腐蚀的体表面积，不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (127, 'T32.800', '累及体表80%~89%的腐蚀伤', '(该疾病编码)(该疾病编码名称)，说明烧伤和腐蚀的体表面积，不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (128, 'T32.900', '累及体表90%及以上的腐蚀伤', '(该疾病编码)(该疾病编码名称)，说明烧伤和腐蚀的体表面积，不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (129, 'U82.000', '耐青霉素', '(该疾病编码)(该疾病编码名称)，补充说明对抗生素已产生耐药性的菌株，不能作为主要诊断编码。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (130, 'U82.001', '耐阿莫西林抗生素', '(该疾病编码)(该疾病编码名称)，补充说明对抗生素已产生耐药性的菌株，不能作为主要诊断编码。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (131, 'U82.002', '耐氨苄西林抗生素', '(该疾病编码)(该疾病编码名称)，补充说明对抗生素已产生耐药性的菌株，不能作为主要诊断编码。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (132, 'U82.100', '耐甲氧西林抗生素', '(该疾病编码)(该疾病编码名称)，补充说明对抗生素已产生耐药性的菌株，不能作为主要诊断编码。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (133, 'U82.100x001', '耐甲氧西林金黄色葡萄球菌（MRSA）', '(该疾病编码)(该疾病编码名称)，补充说明对抗生素已产生耐药性的菌株，不能作为主要诊断编码。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (134, 'U82.101', '耐氯唑西林抗生素', '(该疾病编码)(该疾病编码名称)，补充说明对抗生素已产生耐药性的菌株，不能作为主要诊断编码。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (135, 'U82.102', '耐氟氯西林抗生素', '(该疾病编码)(该疾病编码名称)，补充说明对抗生素已产生耐药性的菌株，不能作为主要诊断编码。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (136, 'U82.103', '耐苯唑西林抗生素', '(该疾病编码)(该疾病编码名称)，补充说明对抗生素已产生耐药性的菌株，不能作为主要诊断编码。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (137, 'U82.200', '耐广谱β-内酰胺酶', '(该疾病编码)(该疾病编码名称)，补充说明对抗生素已产生耐药性的菌株，不能作为主要诊断编码。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (138, 'U82.200x001', '产超广谱β-内酰胺酶肺炎克雷伯菌（ESBLs）', '(该疾病编码)(该疾病编码名称)，补充说明对抗生素已产生耐药性的菌株，不能作为主要诊断编码。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (139, 'U82.200x002', '产超广谱β-内酰胺酶大肠埃希菌（ESBLs）', '(该疾病编码)(该疾病编码名称)，补充说明对抗生素已产生耐药性的菌株，不能作为主要诊断编码。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (140, 'U82.800', '耐其他β-内酰胺类抗生素', '(该疾病编码)(该疾病编码名称)，补充说明对抗生素已产生耐药性的菌株，不能作为主要诊断编码。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (141, 'U82.800x001', '耐碳青霉烯类的鲍曼不动杆菌（CRAB）', '(该疾病编码)(该疾病编码名称)，补充说明对抗生素已产生耐药性的菌株，不能作为主要诊断编码。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (142, 'U82.800x002', '耐碳青霉烯类的铜绿假单胞菌（CRPA）', '(该疾病编码)(该疾病编码名称)，补充说明对抗生素已产生耐药性的菌株，不能作为主要诊断编码。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (143, 'U82.800x003', '耐碳青霉烯类的肠杆菌科菌（CRE）', '(该疾病编码)(该疾病编码名称)，补充说明对抗生素已产生耐药性的菌株，不能作为主要诊断编码。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (144, 'U82.900', '耐β-内酰胺类抗生素，未特指', '(该疾病编码)(该疾病编码名称)，补充说明对抗生素已产生耐药性的菌株，不能作为主要诊断编码。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (145, 'U83.000', '耐万古霉素', '(该疾病编码)(该疾病编码名称)，补充说明对抗生素已产生耐药性的菌株，不能作为主要诊断编码。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (146, 'U83.000x001', '耐万古霉素肠球菌（VRE）', '(该疾病编码)(该疾病编码名称)，补充说明对抗生素已产生耐药性的菌株，不能作为主要诊断编码。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (147, 'U83.100', '耐其他万古霉素相关类抗生素', '(该疾病编码)(该疾病编码名称)，补充说明对抗生素已产生耐药性的菌株，不能作为主要诊断编码。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (148, 'U83.200', '耐喹诺酮类抗生素', '(该疾病编码)(该疾病编码名称)，补充说明对抗生素已产生耐药性的菌株，不能作为主要诊断编码。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (149, 'U83.700', '耐多种抗生素', '(该疾病编码)(该疾病编码名称)，补充说明对抗生素已产生耐药性的菌株，不能作为主要诊断编码。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (150, 'U83.800', '耐其他特指单一抗生素', '(该疾病编码)(该疾病编码名称)，补充说明对抗生素已产生耐药性的菌株，不能作为主要诊断编码。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (151, 'U83.900', '耐其他抗生素的菌株', '(该疾病编码)(该疾病编码名称)，补充说明对抗生素已产生耐药性的菌株，不能作为主要诊断编码。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (152, 'U84.000', '耐抗寄生虫药物', '(该疾病编码)(该疾病编码名称)，补充说明对抗生素已产生耐药性的菌株，不能作为主要诊断编码。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (153, 'U84.100', '耐抗真菌药物', '(该疾病编码)(该疾病编码名称)，补充说明对抗生素已产生耐药性的菌株，不能作为主要诊断编码。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (154, 'U84.200', '耐抗病毒药物', '(该疾病编码)(该疾病编码名称)，补充说明对抗生素已产生耐药性的菌株，不能作为主要诊断编码。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (155, 'U84.300', '耐抗结核药物', '(该疾病编码)(该疾病编码名称)，补充说明对抗生素已产生耐药性的菌株，不能作为主要诊断编码。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (156, 'U84.700', '耐多种抗菌药物', '(该疾病编码)(该疾病编码名称)，补充说明对抗生素已产生耐药性的菌株，不能作为主要诊断编码。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (157, 'U84.800', '耐其他特指抗菌药物', '(该疾病编码)(该疾病编码名称)，补充说明对抗生素已产生耐药性的菌株，不能作为主要诊断编码。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (158, 'U84.900', '耐抗菌药物', '(该疾病编码)(该疾病编码名称)，补充说明对抗生素已产生耐药性的菌株，不能作为主要诊断编码。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (159, 'U84.901', '药物抵抗', '(该疾病编码)(该疾病编码名称)，补充说明对抗生素已产生耐药性的菌株，不能作为主要诊断编码。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (160, 'U85.x00', '耐抗肿瘤药物', '(该疾病编码)(该疾病编码名称)，补充说明对抗生素已产生耐药性的菌株，不能作为主要诊断编码。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (161, 'U85.x01', '抗肿瘤药物无效', '(该疾病编码)(该疾病编码名称)，补充说明对抗生素已产生耐药性的菌株，不能作为主要诊断编码。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (162, 'U85.x02', '难治性癌症', '(该疾病编码)(该疾病编码名称)，补充说明对抗生素已产生耐药性的菌株，不能作为主要诊断编码。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (163, 'Z37.000', '单一活产', '(该疾病编码)(该疾病编码名称)，分娩结局不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (164, 'Z37.000x001', '单胎活产', '(该疾病编码)(该疾病编码名称)，分娩结局不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (165, 'Z37.001', '人工授精，单胎活产', '(该疾病编码)(该疾病编码名称)，分娩结局不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (166, 'Z37.002', '试管婴儿，单胎活产', '(该疾病编码)(该疾病编码名称)，分娩结局不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (167, 'Z37.100', '单一死产', '(该疾病编码)(该疾病编码名称)，分娩结局不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (168, 'Z37.100x002', '单胎死产', '(该疾病编码)(该疾病编码名称)，分娩结局不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (169, 'Z37.200', '双胎，均为活产', '(该疾病编码)(该疾病编码名称)，分娩结局不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (170, 'Z37.200x003', '双胎活产', '(该疾病编码)(该疾病编码名称)，分娩结局不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (171, 'Z37.201', '单卵双胎活产', '(该疾病编码)(该疾病编码名称)，分娩结局不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (172, 'Z37.202', '双卵双胎活产', '(该疾病编码)(该疾病编码名称)，分娩结局不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (173, 'Z37.203', '人工授精，双胎活产', '(该疾病编码)(该疾病编码名称)，分娩结局不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (174, 'Z37.204', '试管婴儿，双胎活产', '(该疾病编码)(该疾病编码名称)，分娩结局不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (175, 'Z37.300', '双胎，一为活产，一为死产', '(该疾病编码)(该疾病编码名称)，分娩结局不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (176, 'Z37.300x001', '双胎,一胎活产,一胎死产', '(该疾病编码)(该疾病编码名称)，分娩结局不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (177, 'Z37.301', '双胎，一胎活产，一胎葡萄胎', '(该疾病编码)(该疾病编码名称)，分娩结局不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (178, 'Z37.302', '人工授精，一胎活产，一胎死产', '(该疾病编码)(该疾病编码名称)，分娩结局不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (179, 'Z37.303', '试管婴儿，一胎活产，一胎死产', '(该疾病编码)(该疾病编码名称)，分娩结局不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (180, 'Z37.400', '双胎，均为死产', '(该疾病编码)(该疾病编码名称)，分娩结局不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (181, 'Z37.400x001', '双胎死产', '(该疾病编码)(该疾病编码名称)，分娩结局不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (182, 'Z37.500', '多胎产，均为活产，其他的', '(该疾病编码)(该疾病编码名称)，分娩结局不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (183, 'Z37.500x001', '多胎活产', '(该疾病编码)(该疾病编码名称)，分娩结局不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (184, 'Z37.501', '三胎活产', '(该疾病编码)(该疾病编码名称)，分娩结局不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (185, 'Z37.502', '试管婴儿，三胎活产', '(该疾病编码)(该疾病编码名称)，分娩结局不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (186, 'Z37.600', '多胎产，某些为活产，其他的', '(该疾病编码)(该疾病编码名称)，分娩结局不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (187, 'Z37.600x001', '多胎产,某些为活产', '(该疾病编码)(该疾病编码名称)，分娩结局不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (188, 'Z37.700', '多胎产，均为死产，其他的', '(该疾病编码)(该疾病编码名称)，分娩结局不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (189, 'Z37.700x001', '多胎产死产', '(该疾病编码)(该疾病编码名称)，分娩结局不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (190, 'Z37.900', '分娩的结局', '(该疾病编码)(该疾病编码名称)，分娩结局不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (191, 'Z37.900x003', '珍贵儿', '(该疾病编码)(该疾病编码名称)，分娩结局不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (192, 'Z38.000', '单胎，在医院内出生', '(该疾病编码)(该疾病编码名称)，分娩地点不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (193, 'Z38.000x001', '在医院内出生的单胎活产婴儿', '(该疾病编码)(该疾病编码名称)，分娩地点不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (194, 'Z38.100', '单胎，在医院外出生', '(该疾病编码)(该疾病编码名称)，分娩地点不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (195, 'Z38.100x001', '在医院外出生的单胎活产婴儿', '(该疾病编码)(该疾病编码名称)，分娩地点不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (196, 'Z38.200', '单胎，未特指出生地点', '(该疾病编码)(该疾病编码名称)，分娩地点不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (197, 'Z38.200x001', '未知出生地点的单胎活产婴儿', '(该疾病编码)(该疾病编码名称)，分娩地点不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (198, 'Z38.300', '双胎，在医院内出生', '(该疾病编码)(该疾病编码名称)，分娩地点不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (199, 'Z38.300x001', '在医院内出生的双胎活产婴儿', '(该疾病编码)(该疾病编码名称)，分娩地点不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (200, 'Z38.400', '双胎，在医院外出生', '(该疾病编码)(该疾病编码名称)，分娩地点不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (201, 'Z38.400x001', '在医院外出生的双胎活产婴儿', '(该疾病编码)(该疾病编码名称)，分娩地点不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (202, 'Z38.500', '双胎，未特指出生地点', '(该疾病编码)(该疾病编码名称)，分娩地点不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (203, 'Z38.500x001', '未知出生地点的双胎活产婴儿', '(该疾病编码)(该疾病编码名称)，分娩地点不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (204, 'Z38.600', '多胎，在医院内出生，其他的', '(该疾病编码)(该疾病编码名称)，分娩地点不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (205, 'Z38.600x001', '在医院内出生的多胎活产婴儿', '(该疾病编码)(该疾病编码名称)，分娩地点不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (206, 'Z38.700', '多胎，在医院外出生，其他的', '(该疾病编码)(该疾病编码名称)，分娩地点不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (207, 'Z38.700x001', '在医院外出生的多胎活产婴儿', '(该疾病编码)(该疾病编码名称)，分娩地点不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (208, 'Z38.800', '多胎，其他的', '(该疾病编码)(该疾病编码名称)，分娩地点不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (209, 'Z38.800x001', '未知出生地点的多胎活产婴儿', '(该疾病编码)(该疾病编码名称)，分娩地点不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (210, 'Z53.000', '由于禁忌证而未进行操作', '(该疾病编码)(该疾病编码名称)，此编码指示为特殊操作而与保健机构接触的人，但操作未进行，不能作为主诊。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (211, 'Z53.000x001', '因禁忌症未进行操作', '(该疾病编码)(该疾病编码名称)，此编码指示为特殊操作而与保健机构接触的人，但操作未进行，不能作为主诊。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (212, 'Z53.100', '由于信仰或群体压力而使病人决定不进行操作', '(该疾病编码)(该疾病编码名称)，此编码指示为特殊操作而与保健机构接触的人，但操作未进行，不能作为主诊。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (213, 'Z53.100x001', '因信仰或群体压力使病人决定不进行操作', '(该疾病编码)(该疾病编码名称)，此编码指示为特殊操作而与保健机构接触的人，但操作未进行，不能作为主诊。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (214, 'Z53.200', '由于其他和未特指原因而使病人决定不进行操作', '(该疾病编码)(该疾病编码名称)，此编码指示为特殊操作而与保健机构接触的人，但操作未进行，不能作为主诊。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (215, 'Z53.200x001', '因病人原因未进行操作', '(该疾病编码)(该疾病编码名称)，此编码指示为特殊操作而与保健机构接触的人，但操作未进行，不能作为主诊。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (216, 'Z53.800', '由于其他原因而未进行操作', '(该疾病编码)(该疾病编码名称)，此编码指示为特殊操作而与保健机构接触的人，但操作未进行，不能作为主诊。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (217, 'Z53.800x001', '因病人家属原因未进行操作', '(该疾病编码)(该疾病编码名称)，此编码指示为特殊操作而与保健机构接触的人，但操作未进行，不能作为主诊。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (218, 'Z53.800x002', '因医生原因而未进行操作', '(该疾病编码)(该疾病编码名称)，此编码指示为特殊操作而与保健机构接触的人，但操作未进行，不能作为主诊。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (219, 'Z53.800x003', '因医疗条件未进行操作', '(该疾病编码)(该疾病编码名称)，此编码指示为特殊操作而与保健机构接触的人，但操作未进行，不能作为主诊。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (220, 'Z53.900', '未进行操作', '(该疾病编码)(该疾病编码名称)，此编码指示为特殊操作而与保健机构接触的人，但操作未进行，不能作为主诊。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (221, 'Z53.900x001', '未按计划诊疗', '(该疾病编码)(该疾病编码名称)，此编码指示为特殊操作而与保健机构接触的人，但操作未进行，不能作为主诊。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (222, 'Z53.900x002', '维持性救治', '(该疾病编码)(该疾病编码名称)，此编码指示为特殊操作而与保健机构接触的人，但操作未进行，不能作为主诊。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (223, 'Z80.000', '消化器官恶性肿瘤家族史', '(该疾病编码)(该疾病编码名称)，疾病家族史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (224, 'Z80.001', '胃肠道恶性肿瘤家族史', '(该疾病编码)(该疾病编码名称)，疾病家族史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (225, 'Z80.002', '大肠恶性肿瘤家族史', '(该疾病编码)(该疾病编码名称)，疾病家族史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (226, 'Z80.100', '气管、支气管和肺恶性肿瘤家族史', '(该疾病编码)(该疾病编码名称)，疾病家族史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (227, 'Z80.200', '呼吸和胸腔内器官恶性肿瘤家族史，其他的', '(该疾病编码)(该疾病编码名称)，疾病家族史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (228, 'Z80.300', '乳房恶性肿瘤家族史', '(该疾病编码)(该疾病编码名称)，疾病家族史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (229, 'Z80.400', '生殖器官恶性肿瘤家族史', '(该疾病编码)(该疾病编码名称)，疾病家族史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (230, 'Z80.401', '卵巢恶性肿瘤家族史', '(该疾病编码)(该疾病编码名称)，疾病家族史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (231, 'Z80.500', '泌尿道恶性肿瘤家族史', '(该疾病编码)(该疾病编码名称)，疾病家族史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (232, 'Z80.600', '白血病家族史', '(该疾病编码)(该疾病编码名称)，疾病家族史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (233, 'Z80.700', '淋巴、造血和有关组织恶性肿瘤家族史', '(该疾病编码)(该疾病编码名称)，疾病家族史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (234, 'Z80.800', '器官或系统恶性肿瘤家族史,其他的', '(该疾病编码)(该疾病编码名称)，疾病家族史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (235, 'Z80.801', '涎腺恶性肿瘤家族史', '(该疾病编码)(该疾病编码名称)，疾病家族史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (236, 'Z80.900', '恶性肿瘤家族史', '(该疾病编码)(该疾病编码名称)，疾病家族史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (237, 'Z81.000', '精神发育迟缓家族史', '(该疾病编码)(该疾病编码名称)，疾病家族史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (238, 'Z81.000x001', '精神发育迟滞家族史', '(该疾病编码)(该疾病编码名称)，疾病家族史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (239, 'Z81.100', '酒精滥用家族史', '(该疾病编码)(该疾病编码名称)，疾病家族史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (240, 'Z81.200', '烟草滥用家族史', '(该疾病编码)(该疾病编码名称)，疾病家族史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (241, 'Z81.300', '精神活性物质滥用家族史，其他的', '(该疾病编码)(该疾病编码名称)，疾病家族史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (242, 'Z81.400', '家族史，其他物质滥用的', '(该疾病编码)(该疾病编码名称)，疾病家族史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (243, 'Z81.800', '精神和行为障碍家族史，其他的', '(该疾病编码)(该疾病编码名称)，疾病家族史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (244, 'Z82.000', '癫痫和神经系统其他疾病家族史', '(该疾病编码)(该疾病编码名称)，疾病家族史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (245, 'Z82.000x001', '癫痫病家族史', '(该疾病编码)(该疾病编码名称)，疾病家族史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (246, 'Z82.100', '盲和视力丧失家族史', '(该疾病编码)(该疾病编码名称)，疾病家族史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (247, 'Z82.200', '聋和听力丧失家族史', '(该疾病编码)(该疾病编码名称)，疾病家族史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (248, 'Z82.300', '脑卒中家族史', '(该疾病编码)(该疾病编码名称)，疾病家族史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (249, 'Z82.300x001', '中风家族史', '(该疾病编码)(该疾病编码名称)，疾病家族史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (250, 'Z82.400', '缺血性心脏病和其他循环系统疾病家族史', '(该疾病编码)(该疾病编码名称)，疾病家族史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (251, 'Z82.500', '哮喘和其他慢性下呼吸道疾病家族史', '(该疾病编码)(该疾病编码名称)，疾病家族史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (252, 'Z82.600', '关节炎和肌肉骨骼系统和结缔组织其他疾病家族史', '(该疾病编码)(该疾病编码名称)，疾病家族史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (253, 'Z82.700', '先天性畸形、变形和染色体异常家族史', '(该疾病编码)(该疾病编码名称)，疾病家族史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (254, 'Z82.701', '唐氏综合征家族史', '(该疾病编码)(该疾病编码名称)，疾病家族史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (255, 'Z82.800', '导致劳动能力丧失的某些伤残和慢性疾病家族史，不可归类在他处者', '(该疾病编码)(该疾病编码名称)，疾病家族史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (256, 'Z83.000', '人类免疫缺陷病毒[HIV]病家族史', '(该疾病编码)(该疾病编码名称)，疾病家族史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (257, 'Z83.100', '传染病和寄生虫病家族史，其他的', '(该疾病编码)(该疾病编码名称)，疾病家族史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (258, 'Z83.200', '血液及造血器官疾病和某些涉及免疫机制的疾患家族史', '(该疾病编码)(该疾病编码名称)，疾病家族史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (259, 'Z83.201', '血友病家族史', '(该疾病编码)(该疾病编码名称)，疾病家族史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (260, 'Z83.300', '糖尿病家族史', '(该疾病编码)(该疾病编码名称)，疾病家族史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (261, 'Z83.400', '内分泌、营养和代谢疾病家族史，其他的', '(该疾病编码)(该疾病编码名称)，疾病家族史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (262, 'Z83.500', '眼和耳疾患家族史', '(该疾病编码)(该疾病编码名称)，疾病家族史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (263, 'Z83.600', '呼吸系统疾病家族史', '(该疾病编码)(该疾病编码名称)，疾病家族史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (264, 'Z83.700', '消化系统疾病家族史', '(该疾病编码)(该疾病编码名称)，疾病家族史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (265, 'Z84.000', '皮肤和皮下组织疾病家族史', '(该疾病编码)(该疾病编码名称)，疾病家族史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (266, 'Z84.100', '肾和输尿管疾患家族史', '(该疾病编码)(该疾病编码名称)，疾病家族史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (267, 'Z84.200', '泌尿生殖系统其他疾病家族史', '(该疾病编码)(该疾病编码名称)，疾病家族史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (268, 'Z84.201', '生殖系统疾病家族史', '(该疾病编码)(该疾病编码名称)，疾病家族史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (269, 'Z84.300', '同血缘家族史', '(该疾病编码)(该疾病编码名称)，疾病家族史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (270, 'Z84.800', '家族史，其他特指情况', '(该疾病编码)(该疾病编码名称)，疾病家族史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (271, 'Z85.000', '消化器官恶性肿瘤\n个人史', '(该疾病编码)(该疾病编码名称)，恶性肿瘤个人史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (272, 'Z85.000x001', '胆囊恶性肿瘤史', '(该疾病编码)(该疾病编码名称)，恶性肿瘤个人史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (273, 'Z85.000x008', '胆道恶性肿瘤史', '(该疾病编码)(该疾病编码名称)，恶性肿瘤个人史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (274, 'Z85.001', '食管恶性肿瘤个人\n史', '(该疾病编码)(该疾病编码名称)，恶性肿瘤个人史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (275, 'Z85.002', '胃恶性肿瘤个人史', '(该疾病编码)(该疾病编码名称)，恶性肿瘤个人史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (276, 'Z85.003', '小肠恶性肿瘤个人\n史', '(该疾病编码)(该疾病编码名称)，恶性肿瘤个人史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (277, 'Z85.004', '盲肠恶性肿瘤个人\n史', '(该疾病编码)(该疾病编码名称)，恶性肿瘤个人史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (278, 'Z85.005', '阑尾恶性肿瘤个人\n史', '(该疾病编码)(该疾病编码名称)，恶性肿瘤个人史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (279, 'Z85.006', '结肠恶性肿瘤个人\n史', '(该疾病编码)(该疾病编码名称)，恶性肿瘤个人史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (280, 'Z85.007', '直肠恶性肿瘤个人\n史', '(该疾病编码)(该疾病编码名称)，恶性肿瘤个人史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (281, 'Z85.008', '肝恶性肿瘤个人史', '(该疾病编码)(该疾病编码名称)，恶性肿瘤个人史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (282, 'Z85.009', '胰腺恶性肿瘤个人\n史', '(该疾病编码)(该疾病编码名称)，恶性肿瘤个人史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (283, 'Z85.100', '气管、支气管和肺\n恶性肿瘤个人史', '(该疾病编码)(该疾病编码名称)，恶性肿瘤个人史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (284, 'Z85.101', '肺恶性肿瘤个人史', '(该疾病编码)(该疾病编码名称)，恶性肿瘤个人史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (285, 'Z85.200', '呼吸和胸腔内器官，其他的恶性肿瘤个人史', '(该疾病编码)(该疾病编码名称)，恶性肿瘤个人史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (286, 'Z85.201', '呼吸系统恶性肿瘤\n个人史', '(该疾病编码)(该疾病编码名称)，恶性肿瘤个人史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (287, 'Z85.203', '鼻窦恶性肿瘤个人\n史', '(该疾病编码)(该疾病编码名称)，恶性肿瘤个人史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (288, 'Z85.204', '喉恶性肿瘤个人史', '(该疾病编码)(该疾病编码名称)，恶性肿瘤个人史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (289, 'Z85.205', '胸腺恶性肿瘤个人\n史', '(该疾病编码)(该疾病编码名称)，恶性肿瘤个人史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (290, 'Z85.300', '乳房恶性肿瘤个人\n史', '(该疾病编码)(该疾病编码名称)，恶性肿瘤个人史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (291, 'Z85.300x001', '乳腺恶性肿瘤史', '(该疾病编码)(该疾病编码名称)，恶性肿瘤个人史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (292, 'Z85.400', '生殖器官恶性肿瘤\n个人史', '(该疾病编码)(该疾病编码名称)，恶性肿瘤个人史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (293, 'Z85.400x003', '子宫恶性肿瘤史', '(该疾病编码)(该疾病编码名称)，恶性肿瘤个人史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (294, 'Z85.400x008', '附睾恶性肿瘤史', '(该疾病编码)(该疾病编码名称)，恶性肿瘤个人史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (295, 'Z85.401', '外阴恶性肿瘤个人\n史', '(该疾病编码)(该疾病编码名称)，恶性肿瘤个人史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (296, 'Z85.402', '子宫内膜恶性肿瘤\n个人史', '(该疾病编码)(该疾病编码名称)，恶性肿瘤个人史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (297, 'Z85.403', '宫颈恶性肿瘤个人\n史', '(该疾病编码)(该疾病编码名称)，恶性肿瘤个人史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (298, 'Z85.404', '卵巢恶性肿瘤个人\n史', '(该疾病编码)(该疾病编码名称)，恶性肿瘤个人史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (299, 'Z85.406', '绒毛膜癌个人史', '(该疾病编码)(该疾病编码名称)，恶性肿瘤个人史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (300, 'Z85.407', '前列腺恶性肿瘤个人史', '(该疾病编码)(该疾病编码名称)，恶性肿瘤个人史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (301, 'Z85.408', '阴茎恶性肿瘤个人\n史', '(该疾病编码)(该疾病编码名称)，恶性肿瘤个人史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (302, 'Z85.409', '睾丸恶性肿瘤个人\n史', '(该疾病编码)(该疾病编码名称)，恶性肿瘤个人史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (303, 'Z85.500', '泌尿道恶性肿瘤个人史', '(该疾病编码)(该疾病编码名称)，恶性肿瘤个人史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (304, 'Z85.500x002', '肾盂恶性肿瘤史', '(该疾病编码)(该疾病编码名称)，恶性肿瘤个人史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (305, 'Z85.501', '肾恶性肿瘤个人史', '(该疾病编码)(该疾病编码名称)，恶性肿瘤个人史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (306, 'Z85.502', '输尿管恶性肿瘤个人史', '(该疾病编码)(该疾病编码名称)，恶性肿瘤个人史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (307, 'Z85.503', '膀胱恶性肿瘤个人\n史', '(该疾病编码)(该疾病编码名称)，恶性肿瘤个人史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (308, 'Z85.600', '白血病个人史', '(该疾病编码)(该疾病编码名称)，恶性肿瘤个人史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (309, 'Z85.600x001', '白血病史', '(该疾病编码)(该疾病编码名称)，恶性肿瘤个人史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (310, 'Z85.700', '淋巴、造血和有关组织其他恶性肿瘤个人史', '(该疾病编码)(该疾病编码名称)，恶性肿瘤个人史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (311, 'Z85.700x001', '多发性骨髓瘤史', '(该疾病编码)(该疾病编码名称)，恶性肿瘤个人史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (312, 'Z85.701', '恶性淋巴瘤个人史', '(该疾病编码)(该疾病编码名称)，恶性肿瘤个人史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (313, 'Z85.800', '器官和系统恶性肿瘤个人史,其他的', '(该疾病编码)(该疾病编码名称)，恶性肿瘤个人史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (314, 'Z85.800x002', '口底恶性肿瘤史', '(该疾病编码)(该疾病编码名称)，恶性肿瘤个人史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (315, 'Z85.800x003', '口咽恶性肿瘤史', '(该疾病编码)(该疾病编码名称)，恶性肿瘤个人史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (316, 'Z85.800x005', '下咽部恶性肿瘤史', '(该疾病编码)(该疾病编码名称)，恶性肿瘤个人史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (317, 'Z85.800x006', '脊髓恶性肿瘤史', '(该疾病编码)(该疾病编码名称)，恶性肿瘤个人史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (318, 'Z85.800x011', '恶性黑色素瘤史', '(该疾病编码)(该疾病编码名称)，恶性肿瘤个人史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (319, 'Z85.801', '脑恶性肿瘤个人史', '(该疾病编码)(该疾病编码名称)，恶性肿瘤个人史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (320, 'Z85.802', '口腔恶性肿瘤个人\n史', '(该疾病编码)(该疾病编码名称)，恶性肿瘤个人史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (321, 'Z85.803', '舌恶性肿瘤个人史', '(该疾病编码)(该疾病编码名称)，恶性肿瘤个人史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (322, 'Z85.804', '甲状腺恶性肿瘤个人史', '(该疾病编码)(该疾病编码名称)，恶性肿瘤个人史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (323, 'Z85.805', '扁桃体恶性肿瘤个人史', '(该疾病编码)(该疾病编码名称)，恶性肿瘤个人史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (324, 'Z85.806', '腮腺恶性肿瘤个人\n史', '(该疾病编码)(该疾病编码名称)，恶性肿瘤个人史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (325, 'Z85.807', '胸内恶性肿瘤个人\n史', '(该疾病编码)(该疾病编码名称)，恶性肿瘤个人史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (326, 'Z85.808', '腹膜恶性肿瘤个人\n史', '(该疾病编码)(该疾病编码名称)，恶性肿瘤个人史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (327, 'Z85.809', '鼻咽恶性肿瘤个人\n史', '(该疾病编码)(该疾病编码名称)，恶性肿瘤个人史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (328, 'Z85.810', '皮肤恶性肿瘤个人\n史', '(该疾病编码)(该疾病编码名称)，恶性肿瘤个人史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (329, 'Z85.900', '恶性肿瘤个人史', '(该疾病编码)(该疾病编码名称)，恶性肿瘤个人史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (330, 'Z86.000', '肿瘤个人史，其他的', '(该疾病编码)(该疾病编码名称)，患者的疾病个人史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (331, 'Z86.001', '宫颈原位肿瘤个人\n史', '(该疾病编码)(该疾病编码名称)，患者的疾病个人史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (332, 'Z86.002', '肾上腺良性肿瘤个人史', '(该疾病编码)(该疾病编码名称)，患者的疾病个人史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (333, 'Z86.003', '恶性葡萄胎个人史', '(该疾病编码)(该疾病编码名称)，患者的疾病个人史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (334, 'Z86.100', '传染病和寄生虫病\n个人史', '(该疾病编码)(该疾病编码名称)，患者的疾病个人史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (335, 'Z86.100x021', '脊髓灰质炎病史', '(该疾病编码)(该疾病编码名称)，患者的疾病个人史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (336, 'Z86.100x031', '疟疾病史', '(该疾病编码)(该疾病编码名称)，患者的疾病个人史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (337, 'Z86.100x801', '严重急性呼吸综合\n征个人史', '(该疾病编码)(该疾病编码名称)，患者的疾病个人史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (338, 'Z86.101', '伤寒个人史', '(该疾病编码)(该疾病编码名称)，患者的疾病个人史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (339, 'Z86.102', '血吸虫病个人史', '(该疾病编码)(该疾病编码名称)，患者的疾病个人史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (340, 'Z86.103', '梅毒个人史', '(该疾病编码)(该疾病编码名称)，患者的疾病个人史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (341, 'Z86.104', '结核个人史', '(该疾病编码)(该疾病编码名称)，患者的疾病个人史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (342, 'Z86.200', '血液和造血器官疾病和某些涉及免疫机制的疾患个人史', '(该疾病编码)(该疾病编码名称)，患者的疾病个人史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (343, 'Z86.200x001', '血液和造血器官疾病史', '(该疾病编码)(该疾病编码名称)，患者的疾病个人史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (344, 'Z86.200x002', '免疫系统疾病史', '(该疾病编码)(该疾病编码名称)，患者的疾病个人史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (345, 'Z86.300', '内分泌、营养和代\n谢疾病个人史', '(该疾病编码)(该疾病编码名称)，患者的疾病个人史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (346, 'Z86.300x001', '甲状腺功能亢进史', '(该疾病编码)(该疾病编码名称)，患者的疾病个人史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (347, 'Z86.301', '营养缺乏个人史', '(该疾病编码)(该疾病编码名称)，患者的疾病个人史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (348, 'Z86.400', '精神活性物质滥用\n个人史', '(该疾病编码)(该疾病编码名称)，患者的疾病个人史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (349, 'Z86.500', '精神和行为障碍个人史，其他的', '(该疾病编码)(该疾病编码名称)，患者的疾病个人史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (350, 'Z86.501', '精神病个人史', '(该疾病编码)(该疾病编码名称)，患者的疾病个人史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (351, 'Z86.600', '神经系统和感觉器官疾病个人史', '(该疾病编码)(该疾病编码名称)，患者的疾病个人史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (352, 'Z86.600x001', '视力问题个人史', '(该疾病编码)(该疾病编码名称)，患者的疾病个人史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (353, 'Z86.600x002', '听觉问题个人史', '(该疾病编码)(该疾病编码名称)，患者的疾病个人史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (354, 'Z86.600x003', '嗅觉和味觉问题个人史', '(该疾病编码)(该疾病编码名称)，患者的疾病个人史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (355, 'Z86.600x004', '发声问题个人史', '(该疾病编码)(该疾病编码名称)，患者的疾病个人史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (356, 'Z86.600x005', '吞咽和咀嚼问题个人史', '(该疾病编码)(该疾病编码名称)，患者的疾病个人史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (357, 'Z86.700', '循环系统疾病个人\n史', '(该疾病编码)(该疾病编码名称)，患者的疾病个人史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (358, 'Z86.701', '病毒性心肌炎个人\n史', '(该疾病编码)(该疾病编码名称)，患者的疾病个人史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (359, 'Z86.702', '脑出血个人史', '(该疾病编码)(该疾病编码名称)，患者的疾病个人史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (360, 'Z86.703', '脑梗死个人史', '(该疾病编码)(该疾病编码名称)，患者的疾病个人史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (361, 'Z87.000', '呼吸系统疾病个人\n史', '(该疾病编码)(该疾病编码名称)，患者的疾病个人史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (362, 'Z87.100', '消化系统疾病个人\n史', '(该疾病编码)(该疾病编码名称)，患者的疾病个人史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (363, 'Z87.100x011', '消化性溃疡个人史', '(该疾病编码)(该疾病编码名称)，患者的疾病个人史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (364, 'Z87.100x021', '结肠息肉个人史', '(该疾病编码)(该疾病编码名称)，患者的疾病个人史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (365, 'Z87.200', '皮肤和皮下组织疾病个人史', '(该疾病编码)(该疾病编码名称)，患者的疾病个人史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (366, 'Z87.300', '肌肉骨骼系统和结\n缔组织疾病个人史', '(该疾病编码)(该疾病编码名称)，患者的疾病个人史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (367, 'Z87.400', '泌尿生殖系统疾病\n个人史', '(该疾病编码)(该疾病编码名称)，患者的疾病个人史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (368, 'Z87.500', '妊娠、分娩和产褥\n期并发症个人史', '(该疾病编码)(该疾病编码名称)，患者的疾病个人史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (369, 'Z87.500x001', '绒毛膜上皮性疾病\n个人史', '(该疾病编码)(该疾病编码名称)，患者的疾病个人史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (370, 'Z87.500x003', '剖宫产个人史', '(该疾病编码)(该疾病编码名称)，患者的疾病个人史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (371, 'Z87.500x004', '异位妊娠个人史', '(该疾病编码)(该疾病编码名称)，患者的疾病个人史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (372, 'Z87.500x005', '不良孕产个人史', '(该疾病编码)(该疾病编码名称)，患者的疾病个人史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (373, 'Z87.600', '起源于围生期的某些情况个人史', '(该疾病编码)(该疾病编码名称)，患者的疾病个人史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (374, 'Z87.600x001', '围生期问题个人史', '(该疾病编码)(该疾病编码名称)，患者的疾病个人史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (375, 'Z87.700', '先天性畸形、变形和染色体异常个人史', '(该疾病编码)(该疾病编码名称)，患者的疾病个人史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (376, 'Z87.701', '先天异常疾病个人\n史', '(该疾病编码)(该疾病编码名称)，患者的疾病个人史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (377, 'Z87.800', '个人史，其他特指\n情况的', '(该疾病编码)(该疾病编码名称)，患者的疾病个人史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (378, 'Z88.000', '青霉素过敏个人史', '(该疾病编码)(该疾病编码名称)，患者的药物过敏史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (379, 'Z88.000x001', '青霉素过敏史', '(该疾病编码)(该疾病编码名称)，患者的药物过敏史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (380, 'Z88.100', '抗生素制剂过敏个人史，其他的', '(该疾病编码)(该疾病编码名称)，患者的药物过敏史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (381, 'Z88.101', '链霉素过敏个人史', '(该疾病编码)(该疾病编码名称)，患者的药物过敏史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (382, 'Z88.102', '氟哌酸过敏个人史', '(该疾病编码)(该疾病编码名称)，患者的药物过敏史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (383, 'Z88.200', '磺胺类药过敏个人\n史', '(该疾病编码)(该疾病编码名称)，患者的药物过敏史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (384, 'Z88.200x001', '磺胺类药过敏史', '(该疾病编码)(该疾病编码名称)，患者的药物过敏史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (385, 'Z88.300', '抗感染制剂过敏个人史，其他的', '(该疾病编码)(该疾病编码名称)，患者的药物过敏史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (386, 'Z88.300x001', '抗感染剂过敏史', '(该疾病编码)(该疾病编码名称)，患者的药物过敏史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (387, 'Z88.301', '呋喃坦啶过敏个人\n史', '(该疾病编码)(该疾病编码名称)，患者的药物过敏史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (388, 'Z88.302', '痢特灵过敏个人史', '(该疾病编码)(该疾病编码名称)，患者的药物过敏史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (389, 'Z88.400', '麻醉药过敏个人史', '(该疾病编码)(该疾病编码名称)，患者的药物过敏史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (390, 'Z88.400x001', '麻醉剂过敏史', '(该疾病编码)(该疾病编码名称)，患者的药物过敏史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (391, 'Z88.500', '麻醉品过敏个人史', '(该疾病编码)(该疾病编码名称)，患者的药物过敏史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (392, 'Z88.500x001', '催眠剂过敏史', '(该疾病编码)(该疾病编码名称)，患者的药物过敏史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (393, 'Z88.600', '镇痛药过敏个人史', '(该疾病编码)(该疾病编码名称)，患者的药物过敏史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (394, 'Z88.600x001', '镇痛药过敏史', '(该疾病编码)(该疾病编码名称)，患者的药物过敏史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (395, 'Z88.700', '血清和疫苗过敏个人史', '(该疾病编码)(该疾病编码名称)，患者的药物过敏史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (396, 'Z88.700x001', '血清和疫苗过敏史', '(该疾病编码)(该疾病编码名称)，患者的药物过敏史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (397, 'Z88.800', '对其他药物、药剂和生物制品过敏个人史', '(该疾病编码)(该疾病编码名称)，患者的药物过敏史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (398, 'Z88.801', '阿托品过敏个人史', '(该疾病编码)(该疾病编码名称)，患者的药物过敏史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (399, 'Z88.802', '氨茶碱过敏个人史', '(该疾病编码)(该疾病编码名称)，患者的药物过敏史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (400, 'Z88.803', '鼻炎宁冲剂过敏个人史', '(该疾病编码)(该疾病编码名称)，患者的药物过敏史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (401, 'Z88.804', '别嘌呤醇过敏个人\n史', '(该疾病编码)(该疾病编码名称)，患者的药物过敏史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (402, 'Z88.805', '低分子右旋糖酐过\n敏个人史', '(该疾病编码)(该疾病编码名称)，患者的药物过敏史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (403, 'Z88.806', '颠茄过敏个人史', '(该疾病编码)(该疾病编码名称)，患者的药物过敏史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (404, 'Z88.807', '碘剂过敏个人史', '(该疾病编码)(该疾病编码名称)，患者的药物过敏史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (405, 'Z88.808', '泛影葡胺过敏个人\n史', '(该疾病编码)(该疾病编码名称)，患者的药物过敏史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (406, 'Z88.811', '汞剂过敏个人史', '(该疾病编码)(该疾病编码名称)，患者的药物过敏史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (407, 'Z88.812', '甲氰咪胍过敏个人\n史', '(该疾病编码)(该疾病编码名称)，患者的药物过敏史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (408, 'Z88.813', '酒精过敏个人史', '(该疾病编码)(该疾病编码名称)，患者的药物过敏史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (409, 'Z88.814', '抗高血压药过敏个人史', '(该疾病编码)(该疾病编码名称)，患者的药物过敏史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (410, 'Z88.815', '抗甲状腺素药过敏\n个人史', '(该疾病编码)(该疾病编码名称)，患者的药物过敏史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (411, 'Z88.817', '麻黄素过敏个人史', '(该疾病编码)(该疾病编码名称)，患者的药物过敏史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (412, 'Z88.818', '灭吐灵过敏个人史', '(该疾病编码)(该疾病编码名称)，患者的药物过敏史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (413, 'Z88.819', '脑益嗪过敏个人史', '(该疾病编码)(该疾病编码名称)，患者的药物过敏史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (414, 'Z88.820', '扑尔敏过敏个人史', '(该疾病编码)(该疾病编码名称)，患者的药物过敏史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (415, 'Z88.821', '氢化可的松过敏个人史', '(该疾病编码)(该疾病编码名称)，患者的药物过敏史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (416, 'Z88.822', '肾上腺素过敏个人\n史', '(该疾病编码)(该疾病编码名称)，患者的药物过敏史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (417, 'Z88.823', '心律平过敏个人史', '(该疾病编码)(该疾病编码名称)，患者的药物过敏史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (418, 'Z88.824', '烟酸过敏个人史', '(该疾病编码)(该疾病编码名称)，患者的药物过敏史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (419, 'Z88.825', '优降糖过敏个人史', '(该疾病编码)(该疾病编码名称)，患者的药物过敏史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (420, 'Z88.900', '对药物、药剂和生物制品过敏个人史', '(该疾病编码)(该疾病编码名称)，患者的药物过敏史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (421, 'Z88.900x001', '药物过敏史', '(该疾病编码)(该疾病编码名称)，患者的药物过敏史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (422, 'Z89.000', '单侧手指[包括拇指]后天性缺失', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (423, 'Z89.000x001', '后天性单侧手指缺\n失', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (424, 'Z89.000x002', '后天性单侧指缺损', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (425, 'Z89.000x003', '后天性单侧拇指缺损', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (426, 'Z89.100', '手和腕后天性缺失', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (427, 'Z89.100x001', '后天性手和腕缺失', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (428, 'Z89.200', '腕以上的上肢后天\n性缺失', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (429, 'Z89.200x002', '后天性肱骨缺失', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (430, 'Z89.300', '双上肢[任何水平]后天性缺失', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (431, 'Z89.300x002', '后天性双侧指缺失', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (432, 'Z89.400', '足和踝后天性缺失', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (433, 'Z89.400x001', '后天性足和踝缺失', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (434, 'Z89.401', '后天性趾缺失', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (435, 'Z89.500', '膝或膝以下小腿后\n天性缺失', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (436, 'Z89.500x002', '后天性小腿缺失', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (437, 'Z89.600', '膝以上大腿后天性\n缺失', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (438, 'Z89.600x001', '手术后股骨缺失', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (439, 'Z89.600x002', '后天性膝以上大腿\n缺失', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (440, 'Z89.700', '双下肢后天性缺失 [任何水平，除外仅趾]', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (441, 'Z89.700x001', '后天性双下肢缺失', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (442, 'Z89.800', '上肢和下肢后天性\n缺失[任何水平]', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (443, 'Z89.800x001', '后天性上肢和下肢\n缺失', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (444, 'Z89.900', '四肢后天性缺失', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (445, 'Z89.900x001', '后天性四肢缺失', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (446, 'Z90.000', '头和颈部分后天性\n缺失', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (447, 'Z90.000x001', '后天性头部器官缺\n失', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (448, 'Z90.000x002', '后天性头皮缺失', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (449, 'Z90.000x003', '手术后颅骨缺失', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (450, 'Z90.000x004', '手术后颌骨缺损', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (451, 'Z90.000x005', '后天性上颌骨缺失', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (452, 'Z90.000x006', '后天性无眼球', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (453, 'Z90.000x007', '后天性上腭缺损', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (454, 'Z90.000x008', '后天性下颌骨缺失', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (455, 'Z90.000x009', '后天性喉缺失', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (456, 'Z90.000x010', '后天性睫毛缺失', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (457, 'Z90.000x011', '后天性睑缘缺失', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (458, 'Z90.000x012', '后天性颊的缺损', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (459, 'Z90.000x013', '后天性颧骨缺失', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (460, 'Z90.000x014', '后天性眶壁缺失', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (461, 'Z90.000x015', '后天性眼睑缺失', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (462, 'Z90.000x016', '后天性颅骨缺损', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (463, 'Z90.000x017', '后天性额部缺损', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (464, 'Z90.000x018', '后天性颞部缺损', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (465, 'Z90.000x019', '后天性面部缺损', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (466, 'Z90.000x021', '后天性唇缺失', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (467, 'Z90.000x022', '后天性耳缺损', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (468, 'Z90.000x023', '后天性耳廓缺损', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (469, 'Z90.001', '眼球摘除术后状态', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (470, 'Z90.002', '后天性鼻缺失', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (471, 'Z90.100', '乳房后天性缺失', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (472, 'Z90.100x001', '后天性乳房缺失', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (473, 'Z90.200', '肺[部分]后天性缺\n失', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (474, 'Z90.200x001', '后天性肺[部分]缺\n失', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (475, 'Z90.300', '胃部分后天性缺失', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (476, 'Z90.300x001', '后天性胃部分缺失', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (477, 'Z90.400', '消化道其他部分后\n天性缺失', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (478, 'Z90.400x001', '后天性结肠[部分]\n缺失', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (479, 'Z90.400x002', '后天性消化道部分\n缺失', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (480, 'Z90.400x003', '后天性胆囊缺失', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (481, 'Z90.401', '食管切除术后状态', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (482, 'Z90.402', '食管部分切除术后状态', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (483, 'Z90.403', '小肠部分切除术后状态', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (484, 'Z90.404', '全结肠切除术后状\n态', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (485, 'Z90.405', '结肠部分切除术后状态', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (486, 'Z90.406', '直肠切除术后状态', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (487, 'Z90.407', '胰腺切除术后状态', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (488, 'Z90.500', '肾后天性缺失', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (489, 'Z90.500x001', '手术后肾缺失', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (490, 'Z90.600', '泌尿道其他部分后\n天性缺失', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (491, 'Z90.600x002', '后天性膀胱缺失', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (492, 'Z90.600x003', '后天性输尿管缺失', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (493, 'Z90.700', '生殖器官后天性缺\n失', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (494, 'Z90.700x002', '后天性阴囊缺失', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (495, 'Z90.700x003', '后天性龟头缺失', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (496, 'Z90.700x005', '后天性阴茎缺失', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (497, 'Z90.700x006', '后天性卵巢缺失', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (498, 'Z90.701', '子宫切除术后状态', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (499, 'Z90.702', '子宫部分切除术后\n状态', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (500, 'Z90.703', '宫颈切除术后状态', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (501, 'Z90.704', '单侧卵巢切除术后状态', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (502, 'Z90.705', '双侧卵巢切除术后\n状态', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (503, 'Z90.706', '单侧输卵管卵巢切\n除术后状态', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (504, 'Z90.707', '双侧输卵管卵巢切\n除术后状态', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (505, 'Z90.708', '前列腺切除术后状\n态', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (506, 'Z90.709', '睾丸切除术后状态', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (507, 'Z90.800', '器官后天性缺失,\n其他的', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (508, 'Z90.800x002', '后天性肋骨缺失', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (509, 'Z91.000', '非药物和生物制品过敏个人史', '(该疾病编码)(该疾病编码名称)，患者的疾病个人史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (510, 'Z91.100', '不服从医疗和医疗制度个人史', '(该疾病编码)(该疾病编码名称)，患者的疾病个人史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (511, 'Z91.200', '个人卫生不良个人\n史', '(该疾病编码)(该疾病编码名称)，患者的疾病个人史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (512, 'Z91.300', '有害健康的作息安排个人史', '(该疾病编码)(该疾病编码名称)，患者的疾病个人史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (513, 'Z91.400', '心理创伤个人史，不可归类在他处者', '(该疾病编码)(该疾病编码名称)，患者的疾病个人史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (514, 'Z91.400x001', '心理创伤史', '(该疾病编码)(该疾病编码名称)，患者的疾病个人史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (515, 'Z91.500', '自我伤害个人史', '(该疾病编码)(该疾病编码名称)，患者的疾病个人史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (516, 'Z91.500x001', '假自杀史', '(该疾病编码)(该疾病编码名称)，患者的疾病个人史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (517, 'Z91.500x003', '自杀企图史', '(该疾病编码)(该疾病编码名称)，患者的疾病个人史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (518, 'Z91.501', '服毒个人史', '(该疾病编码)(该疾病编码名称)，患者的疾病个人史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (519, 'Z91.600', '身体创伤个人史,\n其他的', '(该疾病编码)(该疾病编码名称)，患者的疾病个人史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (520, 'Z91.601', '损伤个人史', '(该疾病编码)(该疾病编码名称)，患者的疾病个人史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (521, 'Z91.700', '女性生殖器官切割\n史', '(该疾病编码)(该疾病编码名称)，患者的疾病个人史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (522, 'Z91.800', '危险因素个人史，其他特指的不可归类在他处者', '(该疾病编码)(该疾病编码名称)，患者的疾病个人史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (523, 'Z91.800x001', '铅中毒史', '(该疾病编码)(该疾病编码名称)，患者的疾病个人史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (524, 'Z91.800x002', '吸烟史', '(该疾病编码)(该疾病编码名称)，患者的疾病个人史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (525, 'Z92.000', '避孕个人史', '(该疾病编码)(该疾病编码名称)，患者的疾病个人史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (526, 'Z92.000x001', '避孕史', '(该疾病编码)(该疾病编码名称)，患者的疾病个人史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (527, 'Z92.001', '输卵管绝育史', '(该疾病编码)(该疾病编码名称)，患者的疾病个人史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (528, 'Z92.100', '长期（近期）使用\n抗凝血药个人史', '(该疾病编码)(该疾病编码名称)，患者的疾病个人史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (529, 'Z92.200', '长期（近期）使用\n其他药剂个人史', '(该疾病编码)(该疾病编码名称)，患者的疾病个人史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (530, 'Z92.200x021', '胰岛素使用史', '(该疾病编码)(该疾病编码名称)，患者的疾病个人史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (531, 'Z92.201', '使用阿司匹林个人\n史', '(该疾病编码)(该疾病编码名称)，患者的疾病个人史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (532, 'Z92.300', '辐射照射个人史', '(该疾病编码)(该疾病编码名称)，患者的疾病个人史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (533, 'Z92.300x001', '放射治疗史', '(该疾病编码)(该疾病编码名称)，患者的疾病个人史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (534, 'Z92.400', '大手术个人史，不可归类在他处者', '(该疾病编码)(该疾病编码名称)，患者的疾病个人史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (535, 'Z92.401', '脑血管手术史', '(该疾病编码)(该疾病编码名称)，患者的疾病个人史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (536, 'Z92.402', '脑肿瘤切除史', '(该疾病编码)(该疾病编码名称)，患者的疾病个人史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (537, 'Z92.500', '康复措施个人史', '(该疾病编码)(该疾病编码名称)，患者的疾病个人史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (538, 'Z92.600', '肿瘤化疗个人史', '(该疾病编码)(该疾病编码名称)，患者的疾病个人史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (539, 'Z92.800', '个人史，其他医疗\n的', '(该疾病编码)(该疾病编码名称)，患者的疾病个人史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (540, 'Z92.800x001', '电除颤史', '(该疾病编码)(该疾病编码名称)，患者的疾病个人史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (541, 'Z92.800x002', '电复律史', '(该疾病编码)(该疾病编码名称)，患者的疾病个人史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (542, 'Z92.900', '医疗个人史', '(该疾病编码)(该疾病编码名称)，患者的疾病个人史不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (543, 'Z93.000', '气管造口状态', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (544, 'Z93.100', '胃造口状态', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (545, 'Z93.200', '回肠造口状态', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (546, 'Z93.201', '空肠造口状态', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (547, 'Z93.300', '结肠造口状态', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (548, 'Z93.400', '胃肠道的其他人工\n造口状态', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (549, 'Z93.500', '膀胱造口状态', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (550, 'Z93.600', '泌尿道的其他人工\n造口状态', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (551, 'Z93.601', '肾造口术状态', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (552, 'Z93.602', '输尿管造术状态', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (553, 'Z93.603', '尿道造口状态', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (554, 'Z93.800', '人工造口状态,其他的', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (555, 'Z93.900', '人工造口状态', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (556, 'Z94.000', '肾移植状态', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (557, 'Z94.001', '自体肾移植状态', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (558, 'Z94.002', '异体肾移植状态', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (559, 'Z94.100', '心脏移植状态', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (560, 'Z94.200', '肺移植状态', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (561, 'Z94.300', '心和肺移植状态', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (562, 'Z94.300x001', '心肺移植状态', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (563, 'Z94.400', '肝移植状态', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (564, 'Z94.500', '皮肤移植状态', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (565, 'Z94.500x001', '自体皮肤移植状态', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (566, 'Z94.500x003', '皮管移植状态', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (567, 'Z94.500x004', '皮瓣移植状态', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (568, 'Z94.500x005', '原皮回植状态', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (569, 'Z94.500x006', '皮瓣舒平状态', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (570, 'Z94.500x007', '皮瓣延迟状态', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (571, 'Z94.600', '骨移植状态', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (572, 'Z94.700', '角膜移植状态', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (573, 'Z94.800', '器官和组织的移植\n状态,其他的', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (574, 'Z94.800x004', '外周干细胞移植状\n态', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (575, 'Z94.800x006', '外耳移植状态', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (576, 'Z94.800x007', '毛发移植状态', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (577, 'Z94.800x010', '脐血干细胞移植状\n态', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (578, 'Z94.800x011', '自体造血干细胞移植状态', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (579, 'Z94.800x012', '造血干细胞移植状态', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (580, 'Z94.801', '肠移植状态', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (581, 'Z94.802', '骨髓移植状态', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (582, 'Z94.803', '胰腺移植状态', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (583, 'Z94.804', '干细胞移植状态', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (584, 'Z94.900', '器官和组织移植状\n态', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (585, 'Z95.000', '具有电子心脏装置', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (586, 'Z95.001', '具有心脏起搏器', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (587, 'Z95.002', '具有心脏再同步治疗除颤器（CRT- D）', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (588, 'Z95.003', '具有心脏再同步治\n疗起搏器', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (589, 'Z95.004', '具有复律除颤器', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (590, 'Z95.100', '具有主动脉冠状动脉搭桥术移植物', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (591, 'Z95.101', '冠状动脉搭桥术后\n状态', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (592, 'Z95.200', '具有假体心脏瓣膜', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (593, 'Z95.200x002', '二尖瓣机械瓣置换\n状态', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (594, 'Z95.200x003', '主动脉瓣机械瓣置\n换状态', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (595, 'Z95.200x004', '三尖瓣机械瓣置换\n状态', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (596, 'Z95.300', '具有异种心脏瓣膜', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (597, 'Z95.300x002', '二尖瓣生物瓣置换\n状态', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (598, 'Z95.300x003', '主动脉瓣生物瓣置\n换状态', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (599, 'Z95.300x004', '三尖瓣生物瓣置换\n状态', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (600, 'Z95.400', '具有其他心脏瓣膜\n置换', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (601, 'Z95.500', '具有冠状血管成形\n术植入物和移植物', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (602, 'Z95.500x002', '冠状血管成形术后\n状态', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (603, 'Z95.501', '冠状动脉支架植入\n后状态', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (604, 'Z95.800', '具有其他心脏和血管植入物和移植物', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (605, 'Z95.800x001', '血管置换术后状态', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (606, 'Z95.800x003', '周围血管支架植入\n术后状态', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (607, 'Z95.800x004', '血管支架植入术后状态', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (608, 'Z95.800x005', '心脏介入封堵术后状态', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (609, 'Z95.800x006', '人工心脏置入术后', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (610, 'Z95.800x007', '具有心脏除颤器', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (611, 'Z95.801', '周围血管成形术后\n状态', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (612, 'Z95.900', '具有心脏和血管植\n入物和移植物', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (613, 'Z96.000', '具有泌尿生殖器植\n入物', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (614, 'Z96.001', '输尿管内支架', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (615, 'Z96.100', '具有眼内晶状体', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (616, 'Z96.101', '人工晶体植入术后', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (617, 'Z96.102', '带晶状体眼的人工\n晶体植入术后', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (618, 'Z96.200', '具有耳科学和听力\n学植入物', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (619, 'Z96.200x001', '骨传导听力装置植\n入状态', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (620, 'Z96.200x005', '镫骨装置植入状态', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (621, 'Z96.201', '人工耳蜗植入', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (622, 'Z96.300', '具有人工喉', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (623, 'Z96.400', '具有内分泌科植入\n物', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (624, 'Z96.401', '胰岛素泵植入状态', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (625, 'Z96.500', '具有牙根和下颌骨\n植入物', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (626, 'Z96.600', '具有矫形外科关节\n植入物', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (627, 'Z96.600x001', '具有关节置入物', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (628, 'Z96.600x011', '具有肩关节置入物', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (629, 'Z96.600x031', '具有腕关节置入物', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (630, 'Z96.600x061', '具有踝关节置入物', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (631, 'Z96.601', '人工髋关节', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (632, 'Z96.602', '人工膝关节', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (633, 'Z96.603', '人工肘关节', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (634, 'Z96.700', '具有其他骨和腱的\n植入物', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (635, 'Z96.701', '颅骨板植入物', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (636, 'Z96.800', '具有其他特指的功\n能性植入物', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (637, 'Z96.900', '具有功能性植入物', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (638, 'Z97.000', '具有人工眼', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (639, 'Z97.000x001', '义眼', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (640, 'Z97.000x002', '人工义眼托植入状\n态', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (641, 'Z97.100', '具有人工肢体（完\n全）（部分）', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (642, 'Z97.100x001', '具有人工肢体', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (643, 'Z97.200', '具有假牙装置（完\n全）（部分）', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (644, 'Z97.200x001', '具有假牙装置', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (645, 'Z97.300', '具有眼镜和接触镜', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (646, 'Z97.300x002', '具有接触镜', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (647, 'Z97.301', '配戴眼镜', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (648, 'Z97.400', '具有外部助听器', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (649, 'Z97.500', '子宫内具有避孕装\n置', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (650, 'Z97.500x001', '具有子宫内避孕装\n置', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (651, 'Z97.800', '具有其他特指的装\n置', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (652, 'Z98.000', '肠搭桥术和吻合术状态', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (653, 'Z98.000x001', '肠搭桥状态', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (654, 'Z98.000x002', '肠吻合状态', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (655, 'Z98.100', '关节固定术状态', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (656, 'Z98.200', '具有脑脊液引流装\n置', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (657, 'Z98.200x001', '脑脊液分流状态', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (658, 'Z98.800', '手术后状态，其他特指的', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (659, 'Z98.800x001', '鼻咽术后', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (660, 'Z98.800x002', '扁桃体术后', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (661, 'Z98.800x003', '喉术后', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (662, 'Z98.800x004', '气管术后', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (663, 'Z98.800x005', '肺术后', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (664, 'Z98.800x006', '胸腔闭式引流术后', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (665, 'Z98.800x007', '腺样体术后', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (666, 'Z98.800x101', '牙外科正畸术后', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (667, 'Z98.800x102', '牙龈术后', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (668, 'Z98.800x103', '舌术后', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (669, 'Z98.800x104', '咽术后', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (670, 'Z98.800x105', '咽腭成形术后', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (671, 'Z98.800x107', '食管术后', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (672, 'Z98.800x108', '胃术后', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (673, 'Z98.800x109', '壶腹术后', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (674, 'Z98.800x110', '贲门术后', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (675, 'Z98.800x111', '胆道术后', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (676, 'Z98.800x112', '胆囊术后', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (677, 'Z98.800x114', '腹膜术后', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (678, 'Z98.800x115', '肝术后', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (679, 'Z98.800x116', '空肠术后', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (680, 'Z98.800x117', '胰腺术后', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (681, 'Z98.800x118', '结肠术后', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (682, 'Z98.800x119', '回肠术后', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (683, 'Z98.800x120', '肛管术后', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (684, 'Z98.800x121', '盲肠术后', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (685, 'Z98.800x122', '直肠术后', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (686, 'Z98.800x123', '阑尾术后', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (687, 'Z98.800x124', '十二指肠术后', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (688, 'Z98.800x201', '输尿管术后', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (689, 'Z98.800x202', '尿道术后', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (690, 'Z98.800x203', '膀胱术后', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (691, 'Z98.800x205', '前列腺术后', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (692, 'Z98.800x208', '阴茎术后', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (693, 'Z98.800x209', '睾丸术后', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (694, 'Z98.800x210', '肾术后', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (695, 'Z98.800x301', '宫颈术后', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (696, 'Z98.800x302', '子宫术后', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (697, 'Z98.800x303', '卵巢术后', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (698, 'Z98.800x305', '盆腔术后', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (699, 'Z98.800x306', '葡萄胎术后', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (700, 'Z98.800x307', '前庭大腺术后', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (701, 'Z98.800x308', '人流术后', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (702, 'Z98.800x309', '输卵管术后', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (703, 'Z98.800x310', '引产术后', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (704, 'Z98.800x311', '外阴术后', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (705, 'Z98.800x312', '药物流产术后', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (706, 'Z98.800x313', '胚胎移植术后', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (707, 'Z98.800x315', '输卵管假体置入术后', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (708, 'Z98.800x316', '左侧输卵管绝育术\n后', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (709, 'Z98.800x317', '右侧输卵管绝育术\n后', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (710, 'Z98.800x318', '双侧输卵管绝育术\n后', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (711, 'Z98.800x319', '多胎妊娠减胎术后', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (712, 'Z98.800x401', '动静脉畸形栓塞术\n后', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (713, 'Z98.800x402', '动脉狭窄扩张成型\n术后', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (714, 'Z98.800x403', '冠状动脉造影术后', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (715, 'Z98.800x404', '静脉曲张术后', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (716, 'Z98.800x405', '先天性心脏病术后', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (717, 'Z98.800x406', '二尖瓣球囊扩张术\n后', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (718, 'Z98.800x407', '法洛四联症术后', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (719, 'Z98.800x408', '室间隔缺损修补术\n后', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (720, 'Z98.800x409', '二尖瓣成形术后', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (721, 'Z98.800x410', '心内膜垫缺损修补术后', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (722, 'Z98.800x411', '房间隔缺损修补术\n后', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (723, 'Z98.800x412', '肺动脉瓣球囊扩张\n术后', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (724, 'Z98.800x413', '三尖瓣成形术后', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (725, 'Z98.800x414', '主动脉瓣成形术后', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (726, 'Z98.800x416', '动脉导管结扎术后', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (727, 'Z98.800x417', '淋巴静脉吻合术后', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (728, 'Z98.800x418', '心脏消融术后', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (729, 'Z98.800x420', '血管成形术后', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (730, 'Z98.800x421', '心脏置入装置调整\n术后', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (731, 'Z98.800x422', '心脏术后', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (732, 'Z98.800x423', '主动脉瓣球囊扩张\n术后', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (733, 'Z98.800x501', '垂体术后', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (734, 'Z98.800x502', '甲状腺术后', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (735, 'Z98.800x503', '肾上腺术后', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (736, 'Z98.800x507', '脑术后', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (737, 'Z98.800x602', '骨折术后', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (738, 'Z98.800x605', '胸廓术后', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (739, 'Z98.800x606', '腹股沟术后', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (740, 'Z98.800x609', '清创术后', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (741, 'Z98.800x610', '韧带术后', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (742, 'Z98.800x611', '脊柱术后', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (743, 'Z98.800x612', '乳腺术后', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (744, 'Z98.800x614', '颈椎术后', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (745, 'Z98.800x615', '胸椎术后', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (746, 'Z98.800x616', '腰椎术后', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (747, 'Z98.800x701', '耳术后', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (748, 'Z98.800x704', '眼术后', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (749, 'Z98.800x901', '气管支架置入术后', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (750, 'Z98.800x902', '食管支架置入术后', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (751, 'Z98.800x903', '胆管支架置入术后', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (752, 'Z98.800x904', '胰管支架置入术后', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (753, 'Z98.800x905', '十二直肠支架置入\n术后', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (754, 'Z98.800x906', '结肠支架置入术后', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (755, 'Z98.800x907', '输尿管支架置入术后', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (756, 'Z98.800x908', '脾术后', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (757, 'Z98.801', '玻璃体切除硅油充\n填状态', '(该疾病编码)(该疾病编码名称)，手术后状态不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (758, 'Z99.000', '依赖吸引器', '(该疾病编码)(该疾病编码名称)，为某种医疗治疗状态的表现，不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (759, 'Z99.100', '依赖呼吸机', '(该疾病编码)(该疾病编码名称)，为某种医疗治疗状态的表现，不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (760, 'Z99.100x001', '依赖于呼吸器者', '(该疾病编码)(该疾病编码名称)，为某种医疗治疗状态的表现，不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (761, 'Z99.200', '依赖肾透析', '(该疾病编码)(该疾病编码名称)，为某种医疗治疗状态的表现，不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (762, 'Z99.200x002', '肾脏透析状态', '(该疾病编码)(该疾病编码名称)，为某种医疗治疗状态的表现，不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (763, 'Z99.201', '血液透析状态', '(该疾病编码)(该疾病编码名称)，为某种医疗治疗状态的表现，不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (764, 'Z99.300', '依赖轮椅', '(该疾病编码)(该疾病编码名称)，为某种医疗治疗状态的表现，不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (765, 'Z99.400', '依赖人工心脏', '(该疾病编码)(该疾病编码名称)，为某种医疗治疗状态的表现，不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (766, 'Z99.800', '依赖其他可启动机\n器和装置', '(该疾病编码)(该疾病编码名称)，为某种医疗治疗状态的表现，不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (767, 'Z99.900', '依赖可启动机器和\n装置', '(该疾病编码)(该疾病编码名称)，为某种医疗治疗状态的表现，不能作为主要诊断。', 1, 1);
INSERT INTO `som_not_be_main_code` VALUES (784, '00.3100', 'CT/CTA的计算机辅助外科手术', '(该疾病编码)(该疾病编码名称)，此编码表示计算机辅助外科手术，不能作为主手术。', 1, 2);
INSERT INTO `som_not_be_main_code` VALUES (785, '00.3101', 'CT导航计算机辅助外科手术', '(该疾病编码)(该疾病编码名称)，此编码表示计算机辅助外科手术，不能作为主手术。', 1, 2);
INSERT INTO `som_not_be_main_code` VALUES (786, '00.3200', 'MR/MRA的计算机辅助外科手术', '(该疾病编码)(该疾病编码名称)，此编码表示计算机辅助外科手术，不能作为主手术。', 1, 2);
INSERT INTO `som_not_be_main_code` VALUES (787, '00.3200x001', 'MRA神经导航辅助外科', '(该疾病编码)(该疾病编码名称)，此编码表示计算机辅助外科手术，不能作为主手术。', 1, 2);
INSERT INTO `som_not_be_main_code` VALUES (788, '00.3201', 'MR神经导航计算机辅助外科手术', '(该疾病编码)(该疾病编码名称)，此编码表示计算机辅助外科手术，不能作为主手术。', 1, 2);
INSERT INTO `som_not_be_main_code` VALUES (789, '00.3300', '荧光透视的计算机辅助外科手术', '(该疾病编码)(该疾病编码名称)，此编码表示计算机辅助外科手术，不能作为主手术。', 1, 2);
INSERT INTO `som_not_be_main_code` VALUES (790, '00.3400', '非显像导航计算机辅助外科手术', '(该疾病编码)(该疾病编码名称)，此编码表示计算机辅助外科手术，不能作为主手术。', 1, 2);
INSERT INTO `som_not_be_main_code` VALUES (791, '00.3400x002', '磁导航计算机辅助外科', '(该疾病编码)(该疾病编码名称)，此编码表示计算机辅助外科手术，不能作为主手术。', 1, 2);
INSERT INTO `som_not_be_main_code` VALUES (792, '00.3500', '多数据的计算机辅助外科手术', '(该疾病编码)(该疾病编码名称)，此编码表示计算机辅助外科手术，不能作为主手术。', 1, 2);
INSERT INTO `som_not_be_main_code` VALUES (793, '00.3500x001', '显像导航计算机辅助外科', '(该疾病编码)(该疾病编码名称)，此编码表示计算机辅助外科手术，不能作为主手术。', 1, 2);
INSERT INTO `som_not_be_main_code` VALUES (794, '00.3900', '其他计算机辅助外科手术', '(该疾病编码)(该疾病编码名称)，此编码表示计算机辅助外科手术，不能作为主手术。', 1, 2);
INSERT INTO `som_not_be_main_code` VALUES (795, '00.3900x001', '计算机辅助外科', '(该疾病编码)(该疾病编码名称)，此编码表示计算机辅助外科手术，不能作为主手术。', 1, 2);
INSERT INTO `som_not_be_main_code` VALUES (796, '00.4000', '单根血管操作', '(该疾病编码)(该疾病编码名称)，此编码提供血管手术数量和植入支架数量的附加信息，不能作为主手术。', 1, 2);
INSERT INTO `som_not_be_main_code` VALUES (797, '00.4100', '两根血管操作', '(该疾病编码)(该疾病编码名称)，此编码提供血管手术数量和植入支架数量的附加信息，不能作为主手术。', 1, 2);
INSERT INTO `som_not_be_main_code` VALUES (798, '00.4200', '三根血管操作', '(该疾病编码)(该疾病编码名称)，此编码提供血管手术数量和植入支架数量的附加信息，不能作为主手术。', 1, 2);
INSERT INTO `som_not_be_main_code` VALUES (799, '00.4300', '四根或更多根血管操作', '(该疾病编码)(该疾病编码名称)，此编码提供血管手术数量和植入支架数量的附加信息，不能作为主手术。', 1, 2);
INSERT INTO `som_not_be_main_code` VALUES (800, '00.4301', '四根血管操作', '(该疾病编码)(该疾病编码名称)，此编码提供血管手术数量和植入支架数量的附加信息，不能作为主手术。', 1, 2);
INSERT INTO `som_not_be_main_code` VALUES (801, '00.4302', '四根以上血管操作', '(该疾病编码)(该疾病编码名称)，此编码提供血管手术数量和植入支架数量的附加信息，不能作为主手术。', 1, 2);
INSERT INTO `som_not_be_main_code` VALUES (802, '00.4400', '分支血管操作', '(该疾病编码)(该疾病编码名称)，此编码提供血管手术数量和植入支架数量的附加信息，不能作为主手术。', 1, 2);
INSERT INTO `som_not_be_main_code` VALUES (803, '00.4400x001', '血管分叉部位的操作', '(该疾病编码)(该疾病编码名称)，此编码提供血管手术数量和植入支架数量的附加信息，不能作为主手术。', 1, 2);
INSERT INTO `som_not_be_main_code` VALUES (804, '00.4500', '置入一根血管支架', '(该疾病编码)(该疾病编码名称)，此编码提供血管手术数量和植入支架数量的附加信息，不能作为主手术。', 1, 2);
INSERT INTO `som_not_be_main_code` VALUES (805, '00.4600', '置入两根血管支架', '(该疾病编码)(该疾病编码名称)，此编码提供血管手术数量和植入支架数量的附加信息，不能作为主手术。', 1, 2);
INSERT INTO `som_not_be_main_code` VALUES (806, '00.4700', '置入三根血管支架', '(该疾病编码)(该疾病编码名称)，此编码提供血管手术数量和植入支架数量的附加信息，不能作为主手术。', 1, 2);
INSERT INTO `som_not_be_main_code` VALUES (807, '00.4800', '置入四根或更多根血管支架', '(该疾病编码)(该疾病编码名称)，此编码提供血管手术数量和植入支架数量的附加信息，不能作为主手术。', 1, 2);
INSERT INTO `som_not_be_main_code` VALUES (808, '00.4801', '四根血管支架置入', '(该疾病编码)(该疾病编码名称)，此编码提供血管手术数量和植入支架数量的附加信息，不能作为主手术。', 1, 2);
INSERT INTO `som_not_be_main_code` VALUES (809, '00.4802', '四根以上血管支架置入', '(该疾病编码)(该疾病编码名称)，此编码提供血管手术数量和植入支架数量的附加信息，不能作为主手术。', 1, 2);
INSERT INTO `som_not_be_main_code` VALUES (810, '0.74', '髋轴面，金属与聚乙烯', '(该疾病编码)(该疾病编码名称)，此编码说明轴面类型，不能作为主手术。', 1, 2);
INSERT INTO `som_not_be_main_code` VALUES (811, '00.7500', '髋轴面，金属与金属', '(该疾病编码)(该疾病编码名称)，此编码说明轴面类型，不能作为主手术。', 1, 2);
INSERT INTO `som_not_be_main_code` VALUES (812, '00.7600', '髋轴面，陶瓷与陶瓷', '(该疾病编码)(该疾病编码名称)，此编码说明轴面类型，不能作为主手术。', 1, 2);
INSERT INTO `som_not_be_main_code` VALUES (813, '00.7601', '黑金股骨头', '(该疾病编码)(该疾病编码名称)，此编码说明轴面类型，不能作为主手术。', 1, 2);
INSERT INTO `som_not_be_main_code` VALUES (814, '00.7700', '髋轴面，陶瓷与聚乙烯', '(该疾病编码)(该疾病编码名称)，此编码说明轴面类型，不能作为主手术。', 1, 2);
INSERT INTO `som_not_be_main_code` VALUES (815, '00.7800', '髋轴面，陶瓷与金属', '(该疾病编码)(该疾病编码名称)，此编码说明轴面类型，不能作为主手术。', 1, 2);
INSERT INTO `som_not_be_main_code` VALUES (816, '00.9100', '与供者有血缘关系的活体移植', '(该疾病编码)(该疾病编码名称)，此编码表明移植手术的附加信息，不能作为主要手术。', 1, 2);
INSERT INTO `som_not_be_main_code` VALUES (817, '00.9200', '与供者无血缘关系的活体移植', '(该疾病编码)(该疾病编码名称)，此编码表明移植手术的附加信息，不能作为主要手术。', 1, 2);
INSERT INTO `som_not_be_main_code` VALUES (818, '00.9300', '从尸体上移植', '(该疾病编码)(该疾病编码名称)，此编码表明移植手术的附加信息，不能作为主要手术。', 1, 2);
INSERT INTO `som_not_be_main_code` VALUES (819, '00.9400', '手术中神经生理监测', '(该疾病编码)(该疾病编码名称)，此编码表明手术的附加信息，不能作为主要手术。', 1, 2);
INSERT INTO `som_not_be_main_code` VALUES (820, '17.4100', '开放性机器人援助操作', '(该疾病编码)(该疾病编码名称)，此编码表明手术为机器人援助操作，不能作为主手术。', 1, 2);
INSERT INTO `som_not_be_main_code` VALUES (821, '17.4200', '腹腔镜机器人援助操作', '(该疾病编码)(该疾病编码名称)，此编码表明手术为机器人援助操作，不能作为主手术。', 1, 2);
INSERT INTO `som_not_be_main_code` VALUES (822, '17.4300', '经皮机器人援助操作', '(该疾病编码)(该疾病编码名称)，此编码表明手术为机器人援助操作，不能作为主手术。', 1, 2);
INSERT INTO `som_not_be_main_code` VALUES (823, '17.4400', '内镜机器人援助操作', '(该疾病编码)(该疾病编码名称)，此编码表明手术为机器人援助操作，不能作为主手术。', 1, 2);
INSERT INTO `som_not_be_main_code` VALUES (824, '17.4500', '胸腔镜机器人援助操作', '(该疾病编码)(该疾病编码名称)，此编码表明手术为机器人援助操作，不能作为主手术。', 1, 2);
INSERT INTO `som_not_be_main_code` VALUES (825, '17.4900', '其他和未特指的机器人援助操作', '(该疾病编码)(该疾病编码名称)，此编码表明手术为机器人援助操作，不能作为主手术。', 1, 2);
INSERT INTO `som_not_be_main_code` VALUES (826, '17.4901', '机器人辅助康复', '(该疾病编码)(该疾病编码名称)，此编码表明手术为机器人援助操作，不能作为主手术。', 1, 2);
INSERT INTO `som_not_be_main_code` VALUES (827, '17.8100', '抗菌外膜置入', '(该疾病编码)(该疾病编码名称)，此编码对作为操作一部分的装置提供附加信息，不能作为主手术。', 1, 2);
INSERT INTO `som_not_be_main_code` VALUES (828, '39.6100', '体外循环辅助开放性心脏手术', '(该疾病编码)(该疾病编码名称)，此编码为心脏手术的附加操作，不能作为主手术。', 1, 2);
INSERT INTO `som_not_be_main_code` VALUES (829, '39.6200', '低温(全身性)下开放性心脏手术', '(该疾病编码)(该疾病编码名称)，此编码为心脏手术的附加操作，不能作为主手术。', 1, 2);
INSERT INTO `som_not_be_main_code` VALUES (830, '39.6300', '心麻痹', '(该疾病编码)(该疾病编码名称)，此编码为心脏手术的附加操作，不能作为主手术。', 1, 2);
INSERT INTO `som_not_be_main_code` VALUES (831, '39.6400', '手术中心脏起搏器', '(该疾病编码)(该疾病编码名称)，此编码为心脏手术的附加操作，不能作为主手术。', 1, 2);
INSERT INTO `som_not_be_main_code` VALUES (832, '39.6600', '经皮心肺搭桥', '(该疾病编码)(该疾病编码名称)，此编码为心脏手术的附加操作，不能作为主手术。', 1, 2);
INSERT INTO `som_not_be_main_code` VALUES (833, '70.9400', '生物移植物的置入术', '(该疾病编码)(该疾病编码名称)，此编码表明移植手术的附加信息，不能作为主要手术。', 1, 2);
INSERT INTO `som_not_be_main_code` VALUES (834, '70.9400x001', '膀胱/直肠/阴道同种异体补片植入', '(该疾病编码)(该疾病编码名称)，此编码表明移植手术的附加信息，不能作为主要手术。', 1, 2);
INSERT INTO `som_not_be_main_code` VALUES (835, '70.9400x002', '膀胱/直肠/阴道自体补片植入', '(该疾病编码)(该疾病编码名称)，此编码表明移植手术的附加信息，不能作为主要手术。', 1, 2);
INSERT INTO `som_not_be_main_code` VALUES (836, '70.9400x003', '膀胱/直肠/阴道异种补片植入', '(该疾病编码)(该疾病编码名称)，此编码表明移植手术的附加信息，不能作为主要手术。', 1, 2);
INSERT INTO `som_not_be_main_code` VALUES (837, '70.9500', '人造移植物或假体的置入术', '(该疾病编码)(该疾病编码名称)，此编码表明移植手术的附加信息，不能作为主要手术。', 1, 2);
INSERT INTO `som_not_be_main_code` VALUES (838, '70.9500x001', '膀胱/直肠/阴道人工补片置入', '(该疾病编码)(该疾病编码名称)，此编码表明移植手术的附加信息，不能作为主要手术。', 1, 2);
INSERT INTO `som_not_be_main_code` VALUES (839, '84.51', '椎体脊椎融合装置的置入', '(该疾病编码)(该疾病编码名称)，此编码表明脊柱手术的附加信息，不能作为主要手术。', 1, 2);
INSERT INTO `som_not_be_main_code` VALUES (840, '84.5100x002', '碳纤维脊椎融合物置入术', '(该疾病编码)(该疾病编码名称)，此编码表明脊柱手术的附加信息，不能作为主要手术。', 1, 2);
INSERT INTO `som_not_be_main_code` VALUES (841, '84.5100x003', '陶瓷脊椎融合物置入术', '(该疾病编码)(该疾病编码名称)，此编码表明脊柱手术的附加信息，不能作为主要手术。', 1, 2);
INSERT INTO `som_not_be_main_code` VALUES (842, '84.5100x004', '金属脊椎融合物置入术', '(该疾病编码)(该疾病编码名称)，此编码表明脊柱手术的附加信息，不能作为主要手术。', 1, 2);
INSERT INTO `som_not_be_main_code` VALUES (843, '84.5100x005', '塑胶脊椎融合物置入术', '(该疾病编码)(该疾病编码名称)，此编码表明脊柱手术的附加信息，不能作为主要手术。', 1, 2);
INSERT INTO `som_not_be_main_code` VALUES (844, '84.5100x006', '钛合金脊椎融合物置入术', '(该疾病编码)(该疾病编码名称)，此编码表明脊柱手术的附加信息，不能作为主要手术。', 1, 2);
INSERT INTO `som_not_be_main_code` VALUES (845, '84.5100x007', '3D打印脊椎融合物置入术', '(该疾病编码)(该疾病编码名称)，此编码表明脊柱手术的附加信息，不能作为主要手术。', 1, 2);
INSERT INTO `som_not_be_main_code` VALUES (846, '84.5200', '重组骨形态形成蛋白的置入', '(该疾病编码)(该疾病编码名称)，此编码表明骨折手术的附加信息，不能作为主要手术。', 1, 2);
INSERT INTO `som_not_be_main_code` VALUES (847, '84.7100', '外部固定装置应用，单相系统', '(该疾病编码)(该疾病编码名称)，此编码表明骨折手术的附加信息，不能作为主要手术。', 1, 2);
INSERT INTO `som_not_be_main_code` VALUES (848, '84.7100x001', '应用单平面外固定架', '(该疾病编码)(该疾病编码名称)，此编码表明骨折手术的附加信息，不能作为主要手术。', 1, 2);
INSERT INTO `som_not_be_main_code` VALUES (849, '84.7200', '外部固定装置的应用，环型系统', '(该疾病编码)(该疾病编码名称)，此编码表明骨折手术的附加信息，不能作为主要手术。', 1, 2);
INSERT INTO `som_not_be_main_code` VALUES (850, '84.7200x001', '应用环形外固定架系统', '(该疾病编码)(该疾病编码名称)，此编码表明骨折手术的附加信息，不能作为主要手术。', 1, 2);
INSERT INTO `som_not_be_main_code` VALUES (851, '84.7300', '混合外部固定装置的应用', '(该疾病编码)(该疾病编码名称)，此编码表明骨折手术的附加信息，不能作为主要手术。', 1, 2);
INSERT INTO `som_not_be_main_code` VALUES (852, '84.7300x001', '应用组合外固定架系统', '(该疾病编码)(该疾病编码名称)，此编码表明骨折手术的附加信息，不能作为主要手术。', 1, 2);

SET FOREIGN_KEY_CHECKS = 1;
