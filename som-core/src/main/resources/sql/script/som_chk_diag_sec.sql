-- Table structure for som_chk_diag_sec
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

DROP TABLE IF EXISTS `som_chk_diag_sec`;
CREATE TABLE `som_chk_diag_sec`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `diag_sec` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '诊断/段',
  `chk_type` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '校验类型',
  `dscr` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '描述',
  `diag_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '诊断名称/手术名称',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4499 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '校验-诊断段' ROW_FORMAT = DYNAMIC;
-- Records of som_chk_diag_sec
INSERT INTO `som_chk_diag_sec` VALUES (1, '17.9271', '6', '患者年龄  大于等于 11岁，手术操作编码不应填报（X手术操作编码，X手术操作名称）', '小儿肌性斜颈推拿治疗');
INSERT INTO `som_chk_diag_sec` VALUES (2, '17.9272', '6', '患者年龄  大于等于 11岁，手术操作编码不应填报（X手术操作编码，X手术操作名称）', '小儿发热推拿治疗');
INSERT INTO `som_chk_diag_sec` VALUES (3, '17.9273', '6', '患者年龄  大于等于 11岁，手术操作编码不应填报（X手术操作编码，X手术操作名称）', '小儿腹泻推拿治疗');
INSERT INTO `som_chk_diag_sec` VALUES (4, '17.9274', '6', '患者年龄  大于等于 11岁，手术操作编码不应填报（X手术操作编码，X手术操作名称）', '小儿咳嗽推拿治疗');
INSERT INTO `som_chk_diag_sec` VALUES (5, '17.9275', '6', '患者年龄  大于等于 11岁，手术操作编码不应填报（X手术操作编码，X手术操作名称）', '小儿捏脊治疗');
INSERT INTO `som_chk_diag_sec` VALUES (6, '17.9276', '6', '患者年龄  大于等于 11岁，手术操作编码不应填报（X手术操作编码，X手术操作名称）', '小儿流涎症推拿治疗');
INSERT INTO `som_chk_diag_sec` VALUES (7, '17.9277', '6', '患者年龄  大于等于 11岁，手术操作编码不应填报（X手术操作编码，X手术操作名称）', '小儿腹痛推拿治疗');
INSERT INTO `som_chk_diag_sec` VALUES (8, '17.9278', '6', '患者年龄  大于等于 11岁，手术操作编码不应填报（X手术操作编码，X手术操作名称）', '小儿夜啼推拿治疗');
INSERT INTO `som_chk_diag_sec` VALUES (9, '17.9279', '6', '患者年龄  大于等于 11岁，手术操作编码不应填报（X手术操作编码，X手术操作名称）', '小儿厌食推拿治疗');
INSERT INTO `som_chk_diag_sec` VALUES (10, '17.927A0', '6', '患者年龄  大于等于 11岁，手术操作编码不应填报（X手术操作编码，X手术操作名称）', '小儿呕吐推拿治疗');
INSERT INTO `som_chk_diag_sec` VALUES (11, '17.927B0', '6', '患者年龄  大于等于 11岁，手术操作编码不应填报（X手术操作编码，X手术操作名称）', '小儿便秘推拿治疗');
INSERT INTO `som_chk_diag_sec` VALUES (12, '17.927C0', '6', '患者年龄  大于等于 11岁，手术操作编码不应填报（X手术操作编码，X手术操作名称）', '小儿遗尿推拿治疗');
INSERT INTO `som_chk_diag_sec` VALUES (13, '17.927D0', '6', '患者年龄  大于等于 11岁，手术操作编码不应填报（X手术操作编码，X手术操作名称）', '小儿脱肛推拿治疗');
INSERT INTO `som_chk_diag_sec` VALUES (14, '17.927E0', '6', '患者年龄  大于等于 11岁，手术操作编码不应填报（X手术操作编码，X手术操作名称）', '小儿疳积推拿治疗');
INSERT INTO `som_chk_diag_sec` VALUES (15, '89.1600 ', '6', '患者年龄  大于等于 11岁，手术操作编码不应填报（X手术操作编码，X手术操作名称）', '新生儿颅骨透照法');
INSERT INTO `som_chk_diag_sec` VALUES (16, '93.3905 ', '6', '患者年龄  大于等于 11岁，手术操作编码不应填报（X手术操作编码，X手术操作名称）', '小儿捏脊治疗');
INSERT INTO `som_chk_diag_sec` VALUES (17, '93.3906 ', '6', '患者年龄  大于等于 11岁，手术操作编码不应填报（X手术操作编码，X手术操作名称）', '小儿推拿按摩');
INSERT INTO `som_chk_diag_sec` VALUES (18, '94.0102 ', '6', '患者年龄  大于等于 11岁，手术操作编码不应填报（X手术操作编码，X手术操作名称）', '儿童智商测验');
INSERT INTO `som_chk_diag_sec` VALUES (19, '96.3903 ', '6', '患者年龄  大于等于 11岁，手术操作编码不应填报（X手术操作编码，X手术操作名称）', '小儿中药灌肠退热术');
INSERT INTO `som_chk_diag_sec` VALUES (20, '99.8301 ', '6', '患者年龄  大于等于 11岁，手术操作编码不应填报（X手术操作编码，X手术操作名称）', '新生儿蓝光治疗');
INSERT INTO `som_chk_diag_sec` VALUES (2216, 'A34', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2217, 'B37.3', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2218, 'C51', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2219, 'C52', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2220, 'C53', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2221, 'C54', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2222, 'C55', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2223, 'C56', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2224, 'C57', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2225, 'C58', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2226, 'C79.6', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2227, 'D06', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2228, 'D07.0', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2229, 'D07.1', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2230, 'D07.2', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2231, 'D07.3', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2232, 'D25', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2233, 'D26', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2234, 'D27', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2235, 'D28', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2236, 'D39', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2237, 'E28', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2238, 'E89.4', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2239, 'F52.5', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2240, 'F53', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2241, 'I86.3', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2242, 'L29.2', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2243, 'M80.0', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2244, 'M80.1', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2245, 'M81.0', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2246, 'M81.1', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2247, 'M83.0', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2248, 'N70', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2249, 'N71', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2250, 'N72', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2251, 'N73', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2252, 'N74', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2253, 'N75', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2254, 'N76', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2255, 'N77', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2256, 'N78', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2257, 'N79', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2258, 'N80', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2259, 'N81', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2260, 'N82', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2261, 'N83', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2262, 'N84', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2263, 'N85', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2264, 'N86', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2265, 'N87', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2266, 'N88', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2267, 'N89', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2268, 'N90', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2269, 'N91', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2270, 'N92', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2271, 'N93', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2272, 'N94', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2273, 'N95', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2274, 'N96', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2275, 'N97', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2276, 'N98', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2277, 'N99.2', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2278, 'N99.3', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2279, 'O00', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2280, 'O01', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2281, 'O02', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2282, 'O03', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2283, 'O04', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2284, 'O05', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2285, 'O06', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2286, 'O07', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2287, 'O08', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2288, 'O09', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2289, 'O10', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2290, 'O11', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2291, 'O12', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2292, 'O13', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2293, 'O14', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2294, 'O15', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2295, 'O16', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2296, 'O17', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2297, 'O18', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2298, 'O19', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2299, 'O20', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2300, 'O21', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2301, 'O22', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2302, 'O23', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2303, 'O24', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2304, 'O25', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2305, 'O26', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2306, 'O27', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2307, 'O28', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2308, 'O29', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2309, 'O30', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2310, 'O31', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2311, 'O32', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2312, 'O33', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2313, 'O34', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2314, 'O35', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2315, 'O36', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2316, 'O37', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2317, 'O38', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2318, 'O39', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2319, 'O40', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2320, 'O41', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2321, 'O42', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2322, 'O43', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2323, 'O44', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2324, 'O45', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2325, 'O46', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2326, 'O47', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2327, 'O48', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2328, 'O49', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2329, 'O50', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2330, 'O51', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2331, 'O52', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2332, 'O53', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2333, 'O54', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2334, 'O55', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2335, 'O56', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2336, 'O57', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2337, 'O58', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2338, 'O59', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2339, 'O60', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2340, 'O61', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2341, 'O62', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2342, 'O63', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2343, 'O64', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2344, 'O65', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2345, 'O66', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2346, 'O67', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2347, 'O68', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2348, 'O69', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2349, 'O70', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2350, 'O71', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2351, 'O72', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2352, 'O73', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2353, 'O74', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2354, 'O75', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2355, 'O76', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2356, 'O77', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2357, 'O78', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2358, 'O79', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2359, 'O80', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2360, 'O81', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2361, 'O82', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2362, 'O83', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2363, 'O84', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2364, 'O85', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2365, 'O86', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2366, 'O87', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2367, 'O88', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2368, 'O89', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2369, 'O90', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2370, 'O91', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2371, 'O92', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2372, 'O93', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2373, 'O94', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2374, 'O95', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2375, 'O96', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2376, 'O97', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2377, 'O98', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2378, 'O99', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2379, 'P54.6', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2380, 'Q50', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2381, 'Q51', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2382, 'Q52', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2383, 'R87', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2384, 'S31.4', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2385, 'S37.4', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2386, 'S37.5', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2387, 'S37.6', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2388, 'T19.2', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2389, 'T19.3', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2390, 'T83.3', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2391, 'Z01.4', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2392, 'Z12.4', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2393, 'Z30.1', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2394, 'Z30.3', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2395, 'Z30.5', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2396, 'Z31.1', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2397, 'Z31.2', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2398, 'Z32', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2399, 'Z33', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2400, 'Z34', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2401, 'Z35', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2402, 'Z36', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2403, 'Z37', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2404, 'Z39', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2405, 'Z87.5', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2406, 'Z97.5', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2407, 'B26.0', '2', '病人性别为女性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2408, 'C60', '2', '病人性别为女性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2409, 'C61', '2', '病人性别为女性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2410, 'C62', '2', '病人性别为女性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2411, 'C63', '2', '病人性别为女性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2412, 'D07.4', '2', '病人性别为女性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2413, 'D07.5', '2', '病人性别为女性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2414, 'D07.6', '2', '病人性别为女性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2415, 'D17.6', '2', '病人性别为女性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2416, 'D29', '2', '病人性别为女性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2417, 'D40', '2', '病人性别为女性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2418, 'E29', '2', '病人性别为女性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2419, 'E89.5', '2', '病人性别为女性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2420, 'F52.4', '2', '病人性别为女性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2421, 'I86.1', '2', '病人性别为女性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2422, 'L29.1', '2', '病人性别为女性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2423, 'N40', '2', '病人性别为女性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2424, 'N41', '2', '病人性别为女性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2425, 'N42', '2', '病人性别为女性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2426, 'N43', '2', '病人性别为女性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2427, 'N44', '2', '病人性别为女性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2428, 'N45', '2', '病人性别为女性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2429, 'N46', '2', '病人性别为女性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2430, 'N47', '2', '病人性别为女性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2431, 'N48', '2', '病人性别为女性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2432, 'N49', '2', '病人性别为女性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2433, 'N50', '2', '病人性别为女性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2434, 'N51', '2', '病人性别为女性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2435, 'Q53', '2', '病人性别为女性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2436, 'Q54', '2', '病人性别为女性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2437, 'Q55', '2', '病人性别为女性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2438, 'R86', '2', '病人性别为女性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2439, 'S31.2', '2', '病人性别为女性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2440, 'S31.3', '2', '病人性别为女性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2441, 'Z12.5', '2', '病人性别为女性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2442, 'P00', '3', '疾病编码指起源于围生期，但在以后发病的情况。年龄＞29天的病人，使用此编码时，不能作为主要诊断，只能作为附加编码，辅助说明是起源于围生期的情况', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2443, 'P01', '3', '疾病编码指起源于围生期，但在以后发病的情况。年龄＞29天的病人，使用此编码时，不能作为主要诊断，只能作为附加编码，辅助说明是起源于围生期的情况', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2444, 'P02', '3', '疾病编码指起源于围生期，但在以后发病的情况。年龄＞29天的病人，使用此编码时，不能作为主要诊断，只能作为附加编码，辅助说明是起源于围生期的情况', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2445, 'P03', '3', '疾病编码指起源于围生期，但在以后发病的情况。年龄＞29天的病人，使用此编码时，不能作为主要诊断，只能作为附加编码，辅助说明是起源于围生期的情况', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2446, 'P04', '3', '疾病编码指起源于围生期，但在以后发病的情况。年龄＞29天的病人，使用此编码时，不能作为主要诊断，只能作为附加编码，辅助说明是起源于围生期的情况', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2447, 'P05', '3', '疾病编码指起源于围生期，但在以后发病的情况。年龄＞29天的病人，使用此编码时，不能作为主要诊断，只能作为附加编码，辅助说明是起源于围生期的情况', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2448, 'P06', '3', '疾病编码指起源于围生期，但在以后发病的情况。年龄＞29天的病人，使用此编码时，不能作为主要诊断，只能作为附加编码，辅助说明是起源于围生期的情况', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2449, 'P07', '3', '疾病编码指起源于围生期，但在以后发病的情况。年龄＞29天的病人，使用此编码时，不能作为主要诊断，只能作为附加编码，辅助说明是起源于围生期的情况', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2450, 'P08', '3', '疾病编码指起源于围生期，但在以后发病的情况。年龄＞29天的病人，使用此编码时，不能作为主要诊断，只能作为附加编码，辅助说明是起源于围生期的情况', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2451, 'P09', '3', '疾病编码指起源于围生期，但在以后发病的情况。年龄＞29天的病人，使用此编码时，不能作为主要诊断，只能作为附加编码，辅助说明是起源于围生期的情况', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2452, 'P10', '3', '疾病编码指起源于围生期，但在以后发病的情况。年龄＞29天的病人，使用此编码时，不能作为主要诊断，只能作为附加编码，辅助说明是起源于围生期的情况', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2453, 'P11', '3', '疾病编码指起源于围生期，但在以后发病的情况。年龄＞29天的病人，使用此编码时，不能作为主要诊断，只能作为附加编码，辅助说明是起源于围生期的情况', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2454, 'P12', '3', '疾病编码指起源于围生期，但在以后发病的情况。年龄＞29天的病人，使用此编码时，不能作为主要诊断，只能作为附加编码，辅助说明是起源于围生期的情况', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2455, 'P13', '3', '疾病编码指起源于围生期，但在以后发病的情况。年龄＞29天的病人，使用此编码时，不能作为主要诊断，只能作为附加编码，辅助说明是起源于围生期的情况', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2456, 'P14', '3', '疾病编码指起源于围生期，但在以后发病的情况。年龄＞29天的病人，使用此编码时，不能作为主要诊断，只能作为附加编码，辅助说明是起源于围生期的情况', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2457, 'P15', '3', '疾病编码指起源于围生期，但在以后发病的情况。年龄＞29天的病人，使用此编码时，不能作为主要诊断，只能作为附加编码，辅助说明是起源于围生期的情况', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2458, 'P16', '3', '疾病编码指起源于围生期，但在以后发病的情况。年龄＞29天的病人，使用此编码时，不能作为主要诊断，只能作为附加编码，辅助说明是起源于围生期的情况', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2459, 'P17', '3', '疾病编码指起源于围生期，但在以后发病的情况。年龄＞29天的病人，使用此编码时，不能作为主要诊断，只能作为附加编码，辅助说明是起源于围生期的情况', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2460, 'P18', '3', '疾病编码指起源于围生期，但在以后发病的情况。年龄＞29天的病人，使用此编码时，不能作为主要诊断，只能作为附加编码，辅助说明是起源于围生期的情况', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2461, 'P19', '3', '疾病编码指起源于围生期，但在以后发病的情况。年龄＞29天的病人，使用此编码时，不能作为主要诊断，只能作为附加编码，辅助说明是起源于围生期的情况', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2462, 'P20', '3', '疾病编码指起源于围生期，但在以后发病的情况。年龄＞29天的病人，使用此编码时，不能作为主要诊断，只能作为附加编码，辅助说明是起源于围生期的情况', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2463, 'P21', '3', '疾病编码指起源于围生期，但在以后发病的情况。年龄＞29天的病人，使用此编码时，不能作为主要诊断，只能作为附加编码，辅助说明是起源于围生期的情况', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2464, 'P22', '3', '疾病编码指起源于围生期，但在以后发病的情况。年龄＞29天的病人，使用此编码时，不能作为主要诊断，只能作为附加编码，辅助说明是起源于围生期的情况', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2465, 'P23', '3', '疾病编码指起源于围生期，但在以后发病的情况。年龄＞29天的病人，使用此编码时，不能作为主要诊断，只能作为附加编码，辅助说明是起源于围生期的情况', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2466, 'P24', '3', '疾病编码指起源于围生期，但在以后发病的情况。年龄＞29天的病人，使用此编码时，不能作为主要诊断，只能作为附加编码，辅助说明是起源于围生期的情况', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2467, 'P25', '3', '疾病编码指起源于围生期，但在以后发病的情况。年龄＞29天的病人，使用此编码时，不能作为主要诊断，只能作为附加编码，辅助说明是起源于围生期的情况', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2468, 'P26', '3', '疾病编码指起源于围生期，但在以后发病的情况。年龄＞29天的病人，使用此编码时，不能作为主要诊断，只能作为附加编码，辅助说明是起源于围生期的情况', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2469, 'P27', '3', '疾病编码指起源于围生期，但在以后发病的情况。年龄＞29天的病人，使用此编码时，不能作为主要诊断，只能作为附加编码，辅助说明是起源于围生期的情况', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2470, 'P28', '3', '疾病编码指起源于围生期，但在以后发病的情况。年龄＞29天的病人，使用此编码时，不能作为主要诊断，只能作为附加编码，辅助说明是起源于围生期的情况', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2471, 'P29', '3', '疾病编码指起源于围生期，但在以后发病的情况。年龄＞29天的病人，使用此编码时，不能作为主要诊断，只能作为附加编码，辅助说明是起源于围生期的情况', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2472, 'P30', '3', '疾病编码指起源于围生期，但在以后发病的情况。年龄＞29天的病人，使用此编码时，不能作为主要诊断，只能作为附加编码，辅助说明是起源于围生期的情况', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2473, 'P31', '3', '疾病编码指起源于围生期，但在以后发病的情况。年龄＞29天的病人，使用此编码时，不能作为主要诊断，只能作为附加编码，辅助说明是起源于围生期的情况', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2474, 'P32', '3', '疾病编码指起源于围生期，但在以后发病的情况。年龄＞29天的病人，使用此编码时，不能作为主要诊断，只能作为附加编码，辅助说明是起源于围生期的情况', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2475, 'P33', '3', '疾病编码指起源于围生期，但在以后发病的情况。年龄＞29天的病人，使用此编码时，不能作为主要诊断，只能作为附加编码，辅助说明是起源于围生期的情况', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2476, 'P34', '3', '疾病编码指起源于围生期，但在以后发病的情况。年龄＞29天的病人，使用此编码时，不能作为主要诊断，只能作为附加编码，辅助说明是起源于围生期的情况', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2477, 'P35', '3', '疾病编码指起源于围生期，但在以后发病的情况。年龄＞29天的病人，使用此编码时，不能作为主要诊断，只能作为附加编码，辅助说明是起源于围生期的情况', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2478, 'P36', '3', '疾病编码指起源于围生期，但在以后发病的情况。年龄＞29天的病人，使用此编码时，不能作为主要诊断，只能作为附加编码，辅助说明是起源于围生期的情况', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2479, 'P37', '3', '疾病编码指起源于围生期，但在以后发病的情况。年龄＞29天的病人，使用此编码时，不能作为主要诊断，只能作为附加编码，辅助说明是起源于围生期的情况', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2480, 'P38', '3', '疾病编码指起源于围生期，但在以后发病的情况。年龄＞29天的病人，使用此编码时，不能作为主要诊断，只能作为附加编码，辅助说明是起源于围生期的情况', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2481, 'P39', '3', '疾病编码指起源于围生期，但在以后发病的情况。年龄＞29天的病人，使用此编码时，不能作为主要诊断，只能作为附加编码，辅助说明是起源于围生期的情况', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2482, 'P40', '3', '疾病编码指起源于围生期，但在以后发病的情况。年龄＞29天的病人，使用此编码时，不能作为主要诊断，只能作为附加编码，辅助说明是起源于围生期的情况', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2483, 'P41', '3', '疾病编码指起源于围生期，但在以后发病的情况。年龄＞29天的病人，使用此编码时，不能作为主要诊断，只能作为附加编码，辅助说明是起源于围生期的情况', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2484, 'P42', '3', '疾病编码指起源于围生期，但在以后发病的情况。年龄＞29天的病人，使用此编码时，不能作为主要诊断，只能作为附加编码，辅助说明是起源于围生期的情况', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2485, 'P43', '3', '疾病编码指起源于围生期，但在以后发病的情况。年龄＞29天的病人，使用此编码时，不能作为主要诊断，只能作为附加编码，辅助说明是起源于围生期的情况', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2486, 'P44', '3', '疾病编码指起源于围生期，但在以后发病的情况。年龄＞29天的病人，使用此编码时，不能作为主要诊断，只能作为附加编码，辅助说明是起源于围生期的情况', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2487, 'P45', '3', '疾病编码指起源于围生期，但在以后发病的情况。年龄＞29天的病人，使用此编码时，不能作为主要诊断，只能作为附加编码，辅助说明是起源于围生期的情况', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2488, 'P46', '3', '疾病编码指起源于围生期，但在以后发病的情况。年龄＞29天的病人，使用此编码时，不能作为主要诊断，只能作为附加编码，辅助说明是起源于围生期的情况', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2489, 'P47', '3', '疾病编码指起源于围生期，但在以后发病的情况。年龄＞29天的病人，使用此编码时，不能作为主要诊断，只能作为附加编码，辅助说明是起源于围生期的情况', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2490, 'P48', '3', '疾病编码指起源于围生期，但在以后发病的情况。年龄＞29天的病人，使用此编码时，不能作为主要诊断，只能作为附加编码，辅助说明是起源于围生期的情况', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2491, 'P49', '3', '疾病编码指起源于围生期，但在以后发病的情况。年龄＞29天的病人，使用此编码时，不能作为主要诊断，只能作为附加编码，辅助说明是起源于围生期的情况', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2492, 'P50', '3', '疾病编码指起源于围生期，但在以后发病的情况。年龄＞29天的病人，使用此编码时，不能作为主要诊断，只能作为附加编码，辅助说明是起源于围生期的情况', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2493, 'P51', '3', '疾病编码指起源于围生期，但在以后发病的情况。年龄＞29天的病人，使用此编码时，不能作为主要诊断，只能作为附加编码，辅助说明是起源于围生期的情况', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2494, 'P52', '3', '疾病编码指起源于围生期，但在以后发病的情况。年龄＞29天的病人，使用此编码时，不能作为主要诊断，只能作为附加编码，辅助说明是起源于围生期的情况', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2495, 'P53', '3', '疾病编码指起源于围生期，但在以后发病的情况。年龄＞29天的病人，使用此编码时，不能作为主要诊断，只能作为附加编码，辅助说明是起源于围生期的情况', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2496, 'P54', '3', '疾病编码指起源于围生期，但在以后发病的情况。年龄＞29天的病人，使用此编码时，不能作为主要诊断，只能作为附加编码，辅助说明是起源于围生期的情况', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2497, 'P55', '3', '疾病编码指起源于围生期，但在以后发病的情况。年龄＞29天的病人，使用此编码时，不能作为主要诊断，只能作为附加编码，辅助说明是起源于围生期的情况', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2498, 'P56', '3', '疾病编码指起源于围生期，但在以后发病的情况。年龄＞29天的病人，使用此编码时，不能作为主要诊断，只能作为附加编码，辅助说明是起源于围生期的情况', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2499, 'P57', '3', '疾病编码指起源于围生期，但在以后发病的情况。年龄＞29天的病人，使用此编码时，不能作为主要诊断，只能作为附加编码，辅助说明是起源于围生期的情况', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2500, 'P58', '3', '疾病编码指起源于围生期，但在以后发病的情况。年龄＞29天的病人，使用此编码时，不能作为主要诊断，只能作为附加编码，辅助说明是起源于围生期的情况', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2501, 'P59', '3', '疾病编码指起源于围生期，但在以后发病的情况。年龄＞29天的病人，使用此编码时，不能作为主要诊断，只能作为附加编码，辅助说明是起源于围生期的情况', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2502, 'P60', '3', '疾病编码指起源于围生期，但在以后发病的情况。年龄＞29天的病人，使用此编码时，不能作为主要诊断，只能作为附加编码，辅助说明是起源于围生期的情况', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2503, 'P61', '3', '疾病编码指起源于围生期，但在以后发病的情况。年龄＞29天的病人，使用此编码时，不能作为主要诊断，只能作为附加编码，辅助说明是起源于围生期的情况', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2504, 'P62', '3', '疾病编码指起源于围生期，但在以后发病的情况。年龄＞29天的病人，使用此编码时，不能作为主要诊断，只能作为附加编码，辅助说明是起源于围生期的情况', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2505, 'P63', '3', '疾病编码指起源于围生期，但在以后发病的情况。年龄＞29天的病人，使用此编码时，不能作为主要诊断，只能作为附加编码，辅助说明是起源于围生期的情况', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2506, 'P64', '3', '疾病编码指起源于围生期，但在以后发病的情况。年龄＞29天的病人，使用此编码时，不能作为主要诊断，只能作为附加编码，辅助说明是起源于围生期的情况', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2507, 'P65', '3', '疾病编码指起源于围生期，但在以后发病的情况。年龄＞29天的病人，使用此编码时，不能作为主要诊断，只能作为附加编码，辅助说明是起源于围生期的情况', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2508, 'P66', '3', '疾病编码指起源于围生期，但在以后发病的情况。年龄＞29天的病人，使用此编码时，不能作为主要诊断，只能作为附加编码，辅助说明是起源于围生期的情况', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2509, 'P67', '3', '疾病编码指起源于围生期，但在以后发病的情况。年龄＞29天的病人，使用此编码时，不能作为主要诊断，只能作为附加编码，辅助说明是起源于围生期的情况', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2510, 'P68', '3', '疾病编码指起源于围生期，但在以后发病的情况。年龄＞29天的病人，使用此编码时，不能作为主要诊断，只能作为附加编码，辅助说明是起源于围生期的情况', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2511, 'P69', '3', '疾病编码指起源于围生期，但在以后发病的情况。年龄＞29天的病人，使用此编码时，不能作为主要诊断，只能作为附加编码，辅助说明是起源于围生期的情况', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2512, 'P70', '3', '疾病编码指起源于围生期，但在以后发病的情况。年龄＞29天的病人，使用此编码时，不能作为主要诊断，只能作为附加编码，辅助说明是起源于围生期的情况', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2513, 'P71', '3', '疾病编码指起源于围生期，但在以后发病的情况。年龄＞29天的病人，使用此编码时，不能作为主要诊断，只能作为附加编码，辅助说明是起源于围生期的情况', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2514, 'P72', '3', '疾病编码指起源于围生期，但在以后发病的情况。年龄＞29天的病人，使用此编码时，不能作为主要诊断，只能作为附加编码，辅助说明是起源于围生期的情况', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2515, 'P73', '3', '疾病编码指起源于围生期，但在以后发病的情况。年龄＞29天的病人，使用此编码时，不能作为主要诊断，只能作为附加编码，辅助说明是起源于围生期的情况', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2516, 'P74', '3', '疾病编码指起源于围生期，但在以后发病的情况。年龄＞29天的病人，使用此编码时，不能作为主要诊断，只能作为附加编码，辅助说明是起源于围生期的情况', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2517, 'P75', '3', '疾病编码指起源于围生期，但在以后发病的情况。年龄＞29天的病人，使用此编码时，不能作为主要诊断，只能作为附加编码，辅助说明是起源于围生期的情况', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2518, 'P76', '3', '疾病编码指起源于围生期，但在以后发病的情况。年龄＞29天的病人，使用此编码时，不能作为主要诊断，只能作为附加编码，辅助说明是起源于围生期的情况', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2519, 'P77', '3', '疾病编码指起源于围生期，但在以后发病的情况。年龄＞29天的病人，使用此编码时，不能作为主要诊断，只能作为附加编码，辅助说明是起源于围生期的情况', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2520, 'P78', '3', '疾病编码指起源于围生期，但在以后发病的情况。年龄＞29天的病人，使用此编码时，不能作为主要诊断，只能作为附加编码，辅助说明是起源于围生期的情况', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2521, 'P79', '3', '疾病编码指起源于围生期，但在以后发病的情况。年龄＞29天的病人，使用此编码时，不能作为主要诊断，只能作为附加编码，辅助说明是起源于围生期的情况', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2522, 'P80', '3', '疾病编码指起源于围生期，但在以后发病的情况。年龄＞29天的病人，使用此编码时，不能作为主要诊断，只能作为附加编码，辅助说明是起源于围生期的情况', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2523, 'P81', '3', '疾病编码指起源于围生期，但在以后发病的情况。年龄＞29天的病人，使用此编码时，不能作为主要诊断，只能作为附加编码，辅助说明是起源于围生期的情况', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2524, 'P82', '3', '疾病编码指起源于围生期，但在以后发病的情况。年龄＞29天的病人，使用此编码时，不能作为主要诊断，只能作为附加编码，辅助说明是起源于围生期的情况', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2525, 'P83', '3', '疾病编码指起源于围生期，但在以后发病的情况。年龄＞29天的病人，使用此编码时，不能作为主要诊断，只能作为附加编码，辅助说明是起源于围生期的情况', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2526, 'P84', '3', '疾病编码指起源于围生期，但在以后发病的情况。年龄＞29天的病人，使用此编码时，不能作为主要诊断，只能作为附加编码，辅助说明是起源于围生期的情况', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2527, 'P85', '3', '疾病编码指起源于围生期，但在以后发病的情况。年龄＞29天的病人，使用此编码时，不能作为主要诊断，只能作为附加编码，辅助说明是起源于围生期的情况', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2528, 'P86', '3', '疾病编码指起源于围生期，但在以后发病的情况。年龄＞29天的病人，使用此编码时，不能作为主要诊断，只能作为附加编码，辅助说明是起源于围生期的情况', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2529, 'P87', '3', '疾病编码指起源于围生期，但在以后发病的情况。年龄＞29天的病人，使用此编码时，不能作为主要诊断，只能作为附加编码，辅助说明是起源于围生期的情况', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2530, 'P88', '3', '疾病编码指起源于围生期，但在以后发病的情况。年龄＞29天的病人，使用此编码时，不能作为主要诊断，只能作为附加编码，辅助说明是起源于围生期的情况', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2531, 'P89', '3', '疾病编码指起源于围生期，但在以后发病的情况。年龄＞29天的病人，使用此编码时，不能作为主要诊断，只能作为附加编码，辅助说明是起源于围生期的情况', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2532, 'P90', '3', '疾病编码指起源于围生期，但在以后发病的情况。年龄＞29天的病人，使用此编码时，不能作为主要诊断，只能作为附加编码，辅助说明是起源于围生期的情况', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2533, 'P91', '3', '疾病编码指起源于围生期，但在以后发病的情况。年龄＞29天的病人，使用此编码时，不能作为主要诊断，只能作为附加编码，辅助说明是起源于围生期的情况', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2534, 'P92', '3', '疾病编码指起源于围生期，但在以后发病的情况。年龄＞29天的病人，使用此编码时，不能作为主要诊断，只能作为附加编码，辅助说明是起源于围生期的情况', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2535, 'P93', '3', '疾病编码指起源于围生期，但在以后发病的情况。年龄＞29天的病人，使用此编码时，不能作为主要诊断，只能作为附加编码，辅助说明是起源于围生期的情况', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2536, 'P94', '3', '疾病编码指起源于围生期，但在以后发病的情况。年龄＞29天的病人，使用此编码时，不能作为主要诊断，只能作为附加编码，辅助说明是起源于围生期的情况', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2537, 'P95', '3', '疾病编码指起源于围生期，但在以后发病的情况。年龄＞29天的病人，使用此编码时，不能作为主要诊断，只能作为附加编码，辅助说明是起源于围生期的情况', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2538, 'P96', '3', '疾病编码指起源于围生期，但在以后发病的情况。年龄＞29天的病人，使用此编码时，不能作为主要诊断，只能作为附加编码，辅助说明是起源于围生期的情况', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2539, 'V01', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2540, 'V02', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2541, 'V03', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2542, 'V04', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2543, 'V05', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2544, 'V06', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2545, 'V07', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2546, 'V08', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2547, 'V09', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2548, 'V10', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2549, 'V11', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2550, 'V12', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2551, 'V13', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2552, 'V14', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2553, 'V15', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2554, 'V16', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2555, 'V17', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2556, 'V18', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2557, 'V19', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2558, 'V20', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2559, 'V21', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2560, 'V22', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2561, 'V23', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2562, 'V24', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2563, 'V25', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2564, 'V26', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2565, 'V27', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2566, 'V28', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2567, 'V29', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2568, 'V30', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2569, 'V31', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2570, 'V32', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2571, 'V33', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2572, 'V34', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2573, 'V35', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2574, 'V36', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2575, 'V37', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2576, 'V38', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2577, 'V39', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2578, 'V40', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2579, 'V41', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2580, 'V42', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2581, 'V43', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2582, 'V44', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2583, 'V45', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2584, 'V46', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2585, 'V47', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2586, 'V48', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2587, 'V49', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2588, 'V50', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2589, 'V51', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2590, 'V52', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2591, 'V53', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2592, 'V54', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2593, 'V55', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2594, 'V56', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2595, 'V57', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2596, 'V58', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2597, 'V59', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2598, 'V60', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2599, 'V61', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2600, 'V62', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2601, 'V63', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2602, 'V64', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2603, 'V65', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2604, 'V66', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2605, 'V67', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2606, 'V68', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2607, 'V69', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2608, 'V70', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2609, 'V71', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2610, 'V72', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2611, 'V73', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2612, 'V74', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2613, 'V75', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2614, 'V76', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2615, 'V77', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2616, 'V78', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2617, 'V79', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2618, 'V80', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2619, 'V81', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2620, 'V82', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2621, 'V83', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2622, 'V84', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2623, 'V85', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2624, 'V86', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2625, 'V87', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2626, 'V88', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2627, 'V89', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2628, 'V90', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2629, 'V91', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2630, 'V92', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2631, 'V93', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2632, 'V94', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2633, 'V95', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2634, 'V96', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2635, 'V97', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2636, 'V98', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2637, 'V99', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2638, 'W00', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2639, 'W01', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2640, 'W02', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2641, 'W03', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2642, 'W04', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2643, 'W05', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2644, 'W06', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2645, 'W07', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2646, 'W08', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2647, 'W09', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2648, 'W10', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2649, 'W11', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2650, 'W12', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2651, 'W13', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2652, 'W14', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2653, 'W15', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2654, 'W16', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2655, 'W17', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2656, 'W18', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2657, 'W19', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2658, 'W20', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2659, 'W21', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2660, 'W22', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2661, 'W23', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2662, 'W24', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2663, 'W25', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2664, 'W26', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2665, 'W27', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2666, 'W28', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2667, 'W29', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2668, 'W30', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2669, 'W31', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2670, 'W32', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2671, 'W33', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2672, 'W34', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2673, 'W35', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2674, 'W36', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2675, 'W37', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2676, 'W38', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2677, 'W39', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2678, 'W40', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2679, 'W41', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2680, 'W42', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2681, 'W43', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2682, 'W44', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2683, 'W45', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2684, 'W46', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2685, 'W47', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2686, 'W48', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2687, 'W49', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2688, 'W50', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2689, 'W51', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2690, 'W52', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2691, 'W53', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2692, 'W54', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2693, 'W55', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2694, 'W56', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2695, 'W57', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2696, 'W58', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2697, 'W59', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2698, 'W60', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2699, 'W61', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2700, 'W62', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2701, 'W63', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2702, 'W64', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2703, 'W65', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2704, 'W66', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2705, 'W67', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2706, 'W68', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2707, 'W69', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2708, 'W70', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2709, 'W71', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2710, 'W72', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2711, 'W73', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2712, 'W74', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2713, 'W75', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2714, 'W76', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2715, 'W77', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2716, 'W78', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2717, 'W79', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2718, 'W80', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2719, 'W81', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2720, 'W82', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2721, 'W83', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2722, 'W84', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2723, 'W85', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2724, 'W86', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2725, 'W87', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2726, 'W88', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2727, 'W89', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2728, 'W90', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2729, 'W91', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2730, 'W92', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2731, 'W93', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2732, 'W94', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2733, 'W95', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2734, 'W96', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2735, 'W97', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2736, 'W98', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2737, 'W99', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2738, 'X00', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2739, 'X01', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2740, 'X02', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2741, 'X03', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2742, 'X04', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2743, 'X05', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2744, 'X06', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2745, 'X07', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2746, 'X08', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2747, 'X09', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2748, 'X10', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2749, 'X11', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2750, 'X12', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2751, 'X13', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2752, 'X14', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2753, 'X15', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2754, 'X16', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2755, 'X17', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2756, 'X18', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2757, 'X19', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2758, 'X20', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2759, 'X21', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2760, 'X22', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2761, 'X23', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2762, 'X24', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2763, 'X25', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2764, 'X26', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2765, 'X27', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2766, 'X28', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2767, 'X29', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2768, 'X30', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2769, 'X31', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2770, 'X32', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2771, 'X33', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2772, 'X34', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2773, 'X35', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2774, 'X36', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2775, 'X37', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2776, 'X38', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2777, 'X39', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2778, 'X40', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2779, 'X41', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2780, 'X42', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2781, 'X43', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2782, 'X44', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2783, 'X45', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2784, 'X46', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2785, 'X47', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2786, 'X48', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2787, 'X49', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2788, 'X50', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2789, 'X51', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2790, 'X52', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2791, 'X53', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2792, 'X54', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2793, 'X55', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2794, 'X56', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2795, 'X57', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2796, 'X58', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2797, 'X59', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2798, 'X60', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2799, 'X61', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2800, 'X62', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2801, 'X63', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2802, 'X64', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2803, 'X65', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2804, 'X66', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2805, 'X67', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2806, 'X68', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2807, 'X69', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2808, 'X70', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2809, 'X71', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2810, 'X72', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2811, 'X73', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2812, 'X74', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2813, 'X75', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2814, 'X76', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2815, 'X77', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2816, 'X78', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2817, 'X79', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2818, 'X80', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2819, 'X81', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2820, 'X82', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2821, 'X83', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2822, 'X84', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2823, 'X85', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2824, 'X86', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2825, 'X87', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2826, 'X88', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2827, 'X89', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2828, 'X90', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2829, 'X91', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2830, 'X92', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2831, 'X93', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2832, 'X94', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2833, 'X95', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2834, 'X96', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2835, 'X97', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2836, 'X98', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2837, 'X99', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2838, 'Y00', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2839, 'Y01', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2840, 'Y02', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2841, 'Y03', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2842, 'Y04', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2843, 'Y05', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2844, 'Y06', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2845, 'Y07', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2846, 'Y08', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2847, 'Y09', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2848, 'Y10', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2849, 'Y11', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2850, 'Y12', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2851, 'Y13', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2852, 'Y14', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2853, 'Y15', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2854, 'Y16', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2855, 'Y17', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2856, 'Y18', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2857, 'Y19', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2858, 'Y20', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2859, 'Y21', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2860, 'Y22', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2861, 'Y23', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2862, 'Y24', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2863, 'Y25', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2864, 'Y26', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2865, 'Y27', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2866, 'Y28', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2867, 'Y29', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2868, 'Y30', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2869, 'Y31', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2870, 'Y32', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2871, 'Y33', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2872, 'Y34', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2873, 'Y35', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2874, 'Y36', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2875, 'Y37', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2876, 'Y38', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2877, 'Y39', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2878, 'Y40', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2879, 'Y41', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2880, 'Y42', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2881, 'Y43', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2882, 'Y44', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2883, 'Y45', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2884, 'Y46', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2885, 'Y47', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2886, 'Y48', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2887, 'Y49', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2888, 'Y50', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2889, 'Y51', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2890, 'Y52', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2891, 'Y53', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2892, 'Y54', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2893, 'Y55', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2894, 'Y56', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2895, 'Y57', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2896, 'Y58', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2897, 'Y59', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2898, 'Y60', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2899, 'Y61', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2900, 'Y62', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2901, 'Y63', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2902, 'Y64', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2903, 'Y65', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2904, 'Y66', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2905, 'Y67', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2906, 'Y68', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2907, 'Y69', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2908, 'Y70', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2909, 'Y71', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2910, 'Y72', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2911, 'Y73', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2912, 'Y74', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2913, 'Y75', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2914, 'Y76', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2915, 'Y77', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2916, 'Y78', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2917, 'Y79', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2918, 'Y80', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2919, 'Y81', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2920, 'Y82', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2921, 'Y83', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2922, 'Y84', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2923, 'Y85', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2924, 'Y86', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2925, 'Y87', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2926, 'Y88', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2927, 'Y89', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2928, 'Y90', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2929, 'Y91', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2930, 'Y92', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2931, 'Y93', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2932, 'Y94', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2933, 'Y95', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2934, 'Y96', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2935, 'Y97', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2936, 'Y98', '4', '疾病编码为外因编码，不能用于疾病诊断，请填写损伤、中毒和外因的某些其他后果（S00-T98）', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2937, 'M80000/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2938, 'M80000/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2939, 'M80000/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2940, 'M80000/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2941, 'M80001/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2942, 'M80002/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2943, 'M80010/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2944, 'M80010/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2945, 'M80010/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2946, 'M80020/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2947, 'M80030/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2948, 'M80030/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2949, 'M80040/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2950, 'M80040/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2951, 'M80100/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2952, 'M80100/2', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2953, 'M80100/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2954, 'M80100/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2955, 'M80101/2', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2956, 'M80110/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2957, 'M80110/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2958, 'M80120/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2959, 'M80120/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2960, 'M80130/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2961, 'M80130/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2962, 'M80140/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2963, 'M80150/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2964, 'M80200/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2965, 'M80200/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2966, 'M80210/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2967, 'M80220/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2968, 'M80220/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2969, 'M80221/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2970, 'M80300/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2971, 'M80310/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2972, 'M80310/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2973, 'M80320/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2974, 'M80320/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2975, 'M80330/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2976, 'M80330/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2977, 'M80331/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2978, 'M80331/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2979, 'M80340/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2980, 'M80340/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2981, 'M80350/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2982, 'M80400/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2983, 'M80400/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2984, 'M80410/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2985, 'M80410/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2986, 'M80411/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2987, 'M80412/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2988, 'M80413/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2989, 'M80420/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2990, 'M80430/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2991, 'M80440/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2992, 'M80450/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2993, 'M80450/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2994, 'M80451/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2995, 'M80452/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2996, 'M80453/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2997, 'M80460/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2998, 'M80460/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (2999, 'M80500/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3000, 'M80500/2', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3001, 'M80500/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3002, 'M80500/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3003, 'M80510/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3004, 'M80510/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3005, 'M80510/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3006, 'M80511/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3007, 'M80520/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3008, 'M80520/2', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3009, 'M80520/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3010, 'M80520/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3011, 'M80530/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3012, 'M80530/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3013, 'M80530/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3014, 'M80600/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3015, 'M80700/2', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3016, 'M80700/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3017, 'M80700/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3018, 'M80710/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3019, 'M80710/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3020, 'M80711/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3021, 'M80720/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3022, 'M80720/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3023, 'M80730/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3024, 'M80740/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3025, 'M80750/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3026, 'M80750/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3027, 'M80760/2', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3028, 'M80760/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3029, 'M80770/2', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3030, 'M80771/2', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3031, 'M80772/2', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3032, 'M80773/2', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3033, 'M80774/2', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3034, 'M80780/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3035, 'M80800/2', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3036, 'M80810/2', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3037, 'M80820/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3038, 'M80820/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3039, 'M80821/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3040, 'M80830/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3041, 'M80840/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3042, 'M80900/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3043, 'M80900/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3044, 'M80900/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3045, 'M80910/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3046, 'M80920/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3047, 'M80930/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3048, 'M80940/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3049, 'M80940/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3050, 'M80950/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3051, 'M80950/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3052, 'M80960/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3053, 'M80970/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3054, 'M80970/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3055, 'M80980/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3056, 'M80980/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3057, 'M81000/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3058, 'M81010/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3059, 'M81020/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3060, 'M81020/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3061, 'M81020/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3062, 'M81020/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3063, 'M81030/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3064, 'M81030/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3065, 'M81030/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3066, 'M81100/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3067, 'M81100/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3068, 'M81200/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3069, 'M81200/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3070, 'M81200/2', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3071, 'M81200/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3072, 'M81200/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3073, 'M81201/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3074, 'M81210/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3075, 'M81210/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3076, 'M81210/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3077, 'M81220/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3078, 'M81230/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3079, 'M81230/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3080, 'M81240/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3081, 'M81300/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3082, 'M81300/2', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3083, 'M81300/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3084, 'M81300/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3085, 'M81301/2', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3086, 'M81310/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3087, 'M81310/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3088, 'M81400/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3089, 'M81400/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3090, 'M81400/2', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3091, 'M81400/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3092, 'M81400/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3093, 'M81401/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3094, 'M81410/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3095, 'M81410/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3096, 'M81420/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3097, 'M81430/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3098, 'M81440/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3099, 'M81450/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3100, 'M81460/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3101, 'M81470/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3102, 'M81470/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3103, 'M81480/2', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3104, 'M81481/2', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3105, 'M81500/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3106, 'M81500/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3107, 'M81500/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3108, 'M81500/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3109, 'M81510/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3110, 'M81510/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3111, 'M81510/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3112, 'M81520/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3113, 'M81520/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3114, 'M81520/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3115, 'M81530/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3116, 'M81530/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3117, 'M81530/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3118, 'M81540/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3119, 'M81541/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3120, 'M81550/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3121, 'M81550/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3122, 'M81550/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3123, 'M81560/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3124, 'M81560/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3125, 'M81560/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3126, 'M81570/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3127, 'M81570/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3128, 'M81570/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3129, 'M81600/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3130, 'M81600/2', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3131, 'M81600/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3132, 'M81600/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3133, 'M81610/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3134, 'M81610/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3135, 'M81620/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3136, 'M81700/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3137, 'M81700/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3138, 'M81700/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3139, 'M81710/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3140, 'M81720/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3141, 'M81730/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3142, 'M81740/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3143, 'M81750/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3144, 'M81800/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3145, 'M81800/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3146, 'M81900/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3147, 'M81900/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3148, 'M81910/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3149, 'M82000/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3150, 'M82000/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3151, 'M82000/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3152, 'M82010/2', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3153, 'M82010/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3154, 'M82010/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3155, 'M82020/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3156, 'M82040/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3157, 'M82100/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3158, 'M82100/2', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3159, 'M82100/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3160, 'M82101/2', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3161, 'M82101/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3162, 'M82102/2', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3163, 'M82102/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3164, 'M82110/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3165, 'M82110/2', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3166, 'M82110/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3167, 'M82110/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3168, 'M82120/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3169, 'M82130/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3170, 'M82140/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3171, 'M82150/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3172, 'M82200/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3173, 'M82200/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3174, 'M82201/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3175, 'M82201/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3176, 'M82202/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3177, 'M82202/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3178, 'M82210/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3179, 'M82210/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3180, 'M82300/2', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3181, 'M82300/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3182, 'M82300/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3183, 'M82310/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3184, 'M82310/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3185, 'M82400/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3186, 'M82400/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3187, 'M82400/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3188, 'M82401/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3189, 'M82402/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3190, 'M82410/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3191, 'M82410/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3192, 'M82420/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3193, 'M82420/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3194, 'M82430/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3195, 'M82431/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3196, 'M82440/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3197, 'M82441/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3198, 'M82442/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3199, 'M82443/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3200, 'M82450/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3201, 'M82450/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3202, 'M82460/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3203, 'M82460/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3204, 'M82470/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3205, 'M82471/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3206, 'M82480/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3207, 'M82490/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3208, 'M82490/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3209, 'M82500/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3210, 'M82500/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3211, 'M82500/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3212, 'M82510/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3213, 'M82510/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3214, 'M82510/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3215, 'M82520/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3216, 'M82530/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3217, 'M82540/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3218, 'M82550/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3219, 'M82600/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3220, 'M82600/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3221, 'M82600/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3222, 'M82610/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3223, 'M82610/2', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3224, 'M82610/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3225, 'M82620/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3226, 'M82620/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3227, 'M82630/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3228, 'M82630/2', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3229, 'M82630/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3230, 'M82631/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3231, 'M82631/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3232, 'M82640/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3233, 'M82700/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3234, 'M82700/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3235, 'M82700/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3236, 'M82710/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3237, 'M82720/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3238, 'M82720/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3239, 'M82720/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3240, 'M82800/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3241, 'M82800/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3242, 'M82800/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3243, 'M82810/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3244, 'M82810/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3245, 'M82810/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3246, 'M82900/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3247, 'M82900/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3248, 'M82900/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3249, 'M83000/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3250, 'M83000/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3251, 'M83000/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3252, 'M83001/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3253, 'M83100/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3254, 'M83100/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3255, 'M83100/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3256, 'M83101/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3257, 'M83110/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3258, 'M83120/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3259, 'M83120/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3260, 'M83121/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3261, 'M83130/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3262, 'M83130/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3263, 'M83140/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3264, 'M83150/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3265, 'M83160/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3266, 'M83170/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3267, 'M83180/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3268, 'M83190/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3269, 'M83200/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3270, 'M83200/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3271, 'M83210/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3272, 'M83220/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3273, 'M83220/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3274, 'M83220/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3275, 'M83230/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3276, 'M83230/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3277, 'M83230/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3278, 'M83231/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3279, 'M83232/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3280, 'M83233/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3281, 'M83240/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3282, 'M83250/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3283, 'M83300/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3284, 'M83300/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3285, 'M83300/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3286, 'M83300/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3287, 'M83310/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3288, 'M83320/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3289, 'M83330/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3290, 'M83330/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3291, 'M83331/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3292, 'M83340/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3293, 'M83341/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3294, 'M83350/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3295, 'M83360/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3296, 'M83370/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3297, 'M83400/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3298, 'M83400/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3299, 'M83410/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3300, 'M83410/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3301, 'M83420/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3302, 'M83430/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3303, 'M83440/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3304, 'M83450/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3305, 'M83460/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3306, 'M83470/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3307, 'M83500/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3308, 'M83600/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3309, 'M83601/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3310, 'M83602/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3311, 'M83603/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3312, 'M83610/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3313, 'M83700/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3314, 'M83700/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3315, 'M83700/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3316, 'M83710/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3317, 'M83720/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3318, 'M83730/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3319, 'M83740/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3320, 'M83750/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3321, 'M83800/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3322, 'M83800/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3323, 'M83800/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3324, 'M83800/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3325, 'M83810/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3326, 'M83810/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3327, 'M83810/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3328, 'M83810/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3329, 'M83820/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3330, 'M83830/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3331, 'M83840/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3332, 'M83900/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3333, 'M83900/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3334, 'M83910/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3335, 'M83920/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3336, 'M84000/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3337, 'M84000/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3338, 'M84000/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3339, 'M84000/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3340, 'M84010/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3341, 'M84010/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3342, 'M84020/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3343, 'M84030/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3344, 'M84040/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3345, 'M84050/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3346, 'M84060/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3347, 'M84070/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3348, 'M84080/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3349, 'M84080/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3350, 'M84080/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3351, 'M84090/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3352, 'M84090/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3353, 'M84100/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3354, 'M84100/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3355, 'M84130/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3356, 'M84200/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3357, 'M84200/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3358, 'M84300/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3359, 'M84300/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3360, 'M84300/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3361, 'M84400/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3362, 'M84400/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3363, 'M84400/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3364, 'M84410/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3365, 'M84410/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3366, 'M84410/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3367, 'M84420/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3368, 'M84420/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3369, 'M84420/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3370, 'M84421/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3371, 'M84430/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3372, 'M84440/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3373, 'M84500/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3374, 'M84500/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3375, 'M84500/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3376, 'M84510/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3377, 'M84520/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3378, 'M84520/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3379, 'M84520/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3380, 'M84521/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3381, 'M84530/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3382, 'M84530/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3383, 'M84530/2', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3384, 'M84530/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3385, 'M84540/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3386, 'M84600/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3387, 'M84600/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3388, 'M84600/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3389, 'M84610/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3390, 'M84610/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3391, 'M84620/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3392, 'M84630/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3393, 'M84700/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3394, 'M84700/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3395, 'M84700/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3396, 'M84700/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3397, 'M84701/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3398, 'M84701/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3399, 'M84710/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3400, 'M84710/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3401, 'M84710/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3402, 'M84720/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3403, 'M84720/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3404, 'M84730/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3405, 'M84800/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3406, 'M84800/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3407, 'M84800/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3408, 'M84801/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3409, 'M84801/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3410, 'M84810/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3411, 'M84820/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3412, 'M84900/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3413, 'M84900/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3414, 'M84901/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3415, 'M85000/2', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3416, 'M85000/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3417, 'M85000/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3418, 'M85001/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3419, 'M85010/2', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3420, 'M85010/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3421, 'M85010/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3422, 'M85020/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3423, 'M85030/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3424, 'M85030/2', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3425, 'M85030/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3426, 'M85031/2', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3427, 'M85040/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3428, 'M85040/2', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3429, 'M85040/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3430, 'M85040/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3431, 'M85050/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3432, 'M85060/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3433, 'M85070/2', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3434, 'M85080/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3435, 'M85090/2', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3436, 'M85100/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3437, 'M85100/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3438, 'M85110/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3439, 'M85120/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3440, 'M85130/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3441, 'M85140/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3442, 'M85200/2', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3443, 'M85200/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3444, 'M85200/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3445, 'M85210/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3446, 'M85220/2', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3447, 'M85220/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3448, 'M85230/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3449, 'M85230/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3450, 'M85231/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3451, 'M85232/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3452, 'M85233/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3453, 'M85234/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3454, 'M85240/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3455, 'M85250/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3456, 'M85300/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3457, 'M85300/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3458, 'M85400/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3459, 'M85400/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3460, 'M85410/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3461, 'M85410/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3462, 'M85420/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3463, 'M85420/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3464, 'M85430/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3465, 'M85430/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3466, 'M85500/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3467, 'M85500/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3468, 'M85500/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3469, 'M85500/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3470, 'M85510/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3471, 'M85600/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3472, 'M85600/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3473, 'M85610/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3474, 'M85620/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3475, 'M85620/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3476, 'M85700/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3477, 'M85700/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3478, 'M85701/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3479, 'M85710/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3480, 'M85720/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3481, 'M85730/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3482, 'M85740/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3483, 'M85750/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3484, 'M85751/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3485, 'M85760/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3486, 'M85800/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3487, 'M85800/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3488, 'M85800/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3489, 'M85800/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3490, 'M85801/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3491, 'M85810/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3492, 'M85810/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3493, 'M85820/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3494, 'M85820/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3495, 'M85830/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3496, 'M85830/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3497, 'M85840/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3498, 'M85840/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3499, 'M85850/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3500, 'M85850/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3501, 'M85850/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3502, 'M85860/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3503, 'M85870/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3504, 'M85880/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3505, 'M85890/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3506, 'M85900/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3507, 'M85901/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3508, 'M85910/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3509, 'M85920/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3510, 'M85921/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3511, 'M85930/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3512, 'M86000/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3513, 'M86000/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3514, 'M86000/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3515, 'M86010/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3516, 'M86020/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3517, 'M86100/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3518, 'M86200/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3519, 'M86200/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3520, 'M86200/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3521, 'M86210/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3522, 'M86220/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3523, 'M86230/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3524, 'M86300/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3525, 'M86300/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3526, 'M86300/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3527, 'M86300/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3528, 'M86310/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3529, 'M86310/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3530, 'M86310/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3531, 'M86320/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3532, 'M86330/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3533, 'M86340/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3534, 'M86340/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3535, 'M86400/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3536, 'M86400/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3537, 'M86410/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3538, 'M86500/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3539, 'M86500/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3540, 'M86500/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3541, 'M86500/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3542, 'M86600/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3543, 'M86700/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3544, 'M86710/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3545, 'M86800/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3546, 'M86800/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3547, 'M86800/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3548, 'M86810/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3549, 'M86820/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3550, 'M86830/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3551, 'M86900/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3552, 'M86910/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3553, 'M86920/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3554, 'M86930/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3555, 'M86930/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3556, 'M86930/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3557, 'M86931/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3558, 'M86931/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3559, 'M87000/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3560, 'M87000/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3561, 'M87000/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3562, 'M87001/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3563, 'M87100/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3564, 'M87110/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3565, 'M87110/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3566, 'M87120/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3567, 'M87130/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3568, 'M87130/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3569, 'M87140/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3570, 'M87200/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3571, 'M87200/2', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3572, 'M87200/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3573, 'M87200/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3574, 'M87201/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3575, 'M87203/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3576, 'M87210/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3577, 'M87220/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3578, 'M87220/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3579, 'M87230/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3580, 'M87230/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3581, 'M87240/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3582, 'M87250/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3583, 'M87260/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3584, 'M87270/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3585, 'M87280/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3586, 'M87300/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3587, 'M87300/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3588, 'M87400/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3589, 'M87400/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3590, 'M87410/2', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3591, 'M87410/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3592, 'M87420/2', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3593, 'M87420/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3594, 'M87430/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3595, 'M87440/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3596, 'M87450/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3597, 'M87500/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3598, 'M87600/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3599, 'M87610/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3600, 'M87610/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3601, 'M87620/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3602, 'M87700/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3603, 'M87700/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3604, 'M87710/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3605, 'M87710/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3606, 'M87720/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3607, 'M87720/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3608, 'M87730/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3609, 'M87740/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3610, 'M87800/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3611, 'M87800/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3612, 'M87801/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3613, 'M87900/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3614, 'M87901/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3615, 'M88000/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3616, 'M88000/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3617, 'M88000/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3618, 'M88001/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3619, 'M88010/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3620, 'M88010/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3621, 'M88020/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3622, 'M88020/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3623, 'M88030/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3624, 'M88040/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3625, 'M88050/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3626, 'M88060/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3627, 'M88060/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3628, 'M88100/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3629, 'M88100/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3630, 'M88100/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3631, 'M88100/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3632, 'M88101/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3633, 'M88102/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3634, 'M88110/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3635, 'M88110/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3636, 'M88110/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3637, 'M88120/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3638, 'M88120/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3639, 'M88130/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3640, 'M88130/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3641, 'M88130/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3642, 'M88140/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3643, 'M88150/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3644, 'M88150/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3645, 'M88150/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3646, 'M88150/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3647, 'M88151/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3648, 'M88151/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3649, 'M88200/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3650, 'M88210/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3651, 'M88211/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3652, 'M88220/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3653, 'M88230/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3654, 'M88230/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3655, 'M88240/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3656, 'M88240/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3657, 'M88241/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3658, 'M88250/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3659, 'M88250/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3660, 'M88250/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3661, 'M88260/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3662, 'M88270/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3663, 'M88300/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3664, 'M88300/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3665, 'M88300/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3666, 'M88300/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3667, 'M88301/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3668, 'M88310/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3669, 'M88320/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3670, 'M88320/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3671, 'M88321/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3672, 'M88330/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3673, 'M88340/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3674, 'M88350/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3675, 'M88360/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3676, 'M88400/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3677, 'M88400/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3678, 'M88400/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3679, 'M88400/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3680, 'M88401/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3681, 'M88410/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3682, 'M88500/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3683, 'M88500/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3684, 'M88500/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3685, 'M88500/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3686, 'M88501/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3687, 'M88510/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3688, 'M88510/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3689, 'M88511/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3690, 'M88520/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3691, 'M88520/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3692, 'M88521/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3693, 'M88530/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3694, 'M88540/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3695, 'M88540/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3696, 'M88550/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3697, 'M88560/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3698, 'M88561/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3699, 'M88570/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3700, 'M88570/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3701, 'M88580/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3702, 'M88600/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3703, 'M88600/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3704, 'M88610/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3705, 'M88620/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3706, 'M88700/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3707, 'M88800/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3708, 'M88810/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3709, 'M88811/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3710, 'M88900/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3711, 'M88900/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3712, 'M88900/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3713, 'M88900/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3714, 'M88901/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3715, 'M88910/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3716, 'M88910/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3717, 'M88911/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3718, 'M88920/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3719, 'M88930/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3720, 'M88940/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3721, 'M88940/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3722, 'M88950/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3723, 'M88960/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3724, 'M88970/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3725, 'M89000/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3726, 'M89000/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3727, 'M89000/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3728, 'M89010/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3729, 'M89020/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3730, 'M89030/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3731, 'M89040/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3732, 'M89050/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3733, 'M89100/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3734, 'M89100/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3735, 'M89101/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3736, 'M89120/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3737, 'M89200/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3738, 'M89210/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3739, 'M89300/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3740, 'M89300/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3741, 'M89300/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3742, 'M89310/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3743, 'M89320/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3744, 'M89321/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3745, 'M89330/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3746, 'M89330/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3747, 'M89340/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3748, 'M89340/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3749, 'M89350/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3750, 'M89350/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3751, 'M89350/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3752, 'M89350/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3753, 'M89360/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3754, 'M89360/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3755, 'M89360/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3756, 'M89360/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3757, 'M89361/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3758, 'M89400/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3759, 'M89400/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3760, 'M89400/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3761, 'M89401/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3762, 'M89402/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3763, 'M89410/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3764, 'M89500/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3765, 'M89510/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3766, 'M89510/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3767, 'M89590/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3768, 'M89590/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3769, 'M89590/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3770, 'M89590/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3771, 'M89600/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3772, 'M89600/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3773, 'M89600/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3774, 'M89630/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3775, 'M89640/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3776, 'M89640/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3777, 'M89650/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3778, 'M89660/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3779, 'M89670/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3780, 'M89700/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3781, 'M89700/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3782, 'M89710/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3783, 'M89710/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3784, 'M89720/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3785, 'M89720/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3786, 'M89730/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3787, 'M89740/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3788, 'M89800/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3789, 'M89800/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3790, 'M89810/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3791, 'M89820/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3792, 'M89820/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3793, 'M89820/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3794, 'M89830/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3795, 'M89900/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3796, 'M89900/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3797, 'M89900/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3798, 'M89900/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3799, 'M89901/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3800, 'M89910/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3801, 'M89910/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3802, 'M90000/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3803, 'M90000/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3804, 'M90000/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3805, 'M90000/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3806, 'M90100/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3807, 'M90110/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3808, 'M90120/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3809, 'M90130/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3810, 'M90130/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3811, 'M90131/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3812, 'M90140/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3813, 'M90140/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3814, 'M90140/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3815, 'M90141/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3816, 'M90150/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3817, 'M90150/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3818, 'M90160/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3819, 'M90200/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3820, 'M90200/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3821, 'M90200/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3822, 'M90201/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3823, 'M90201/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3824, 'M90300/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3825, 'M90400/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3826, 'M90400/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3827, 'M90400/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3828, 'M90410/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3829, 'M90420/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3830, 'M90430/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3831, 'M90440/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3832, 'M90440/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3833, 'M90500/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3834, 'M90500/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3835, 'M90500/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3836, 'M90510/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3837, 'M90510/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3838, 'M90520/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3839, 'M90520/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3840, 'M90530/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3841, 'M90530/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3842, 'M90540/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3843, 'M90550/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3844, 'M90600/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3845, 'M90600/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3846, 'M90610/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3847, 'M90610/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3848, 'M90620/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3849, 'M90630/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3850, 'M90630/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3851, 'M90640/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3852, 'M90640/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3853, 'M90650/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3854, 'M90700/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3855, 'M90700/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3856, 'M90710/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3857, 'M90710/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3858, 'M90711/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3859, 'M90720/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3860, 'M90730/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3861, 'M90800/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3862, 'M90800/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3863, 'M90800/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3864, 'M90800/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3865, 'M90810/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3866, 'M90820/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3867, 'M90830/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3868, 'M90840/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3869, 'M90840/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3870, 'M90841/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3871, 'M90850/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3872, 'M90850/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3873, 'M90851/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3874, 'M90900/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3875, 'M90900/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3876, 'M90900/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3877, 'M90910/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3878, 'M91000/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3879, 'M91000/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3880, 'M91000/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3881, 'M91000/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3882, 'M91010/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3883, 'M91020/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3884, 'M91030/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3885, 'M91040/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3886, 'M91050/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3887, 'M91100/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3888, 'M91100/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3889, 'M91100/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3890, 'M91100/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3891, 'M91200/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3892, 'M91200/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3893, 'M91200/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3894, 'M91210/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3895, 'M91211/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3896, 'M91220/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3897, 'M91230/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3898, 'M91231/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3899, 'M91240/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3900, 'M91250/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3901, 'M91260/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3902, 'M91300/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3903, 'M91300/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3904, 'M91300/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3905, 'M91300/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3906, 'M91310/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3907, 'M91311/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3908, 'M91320/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3909, 'M91330/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3910, 'M91330/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3911, 'M91331/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3912, 'M91350/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3913, 'M91360/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3914, 'M91400/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3915, 'M91410/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3916, 'M91420/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3917, 'M91500/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3918, 'M91500/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3919, 'M91500/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3920, 'M91500/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3921, 'M91600/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3922, 'M91601/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3923, 'M91610/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3924, 'M91700/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3925, 'M91700/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3926, 'M91700/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3927, 'M91710/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3928, 'M91720/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3929, 'M91730/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3930, 'M91740/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3931, 'M91740/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3932, 'M91750/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3933, 'M91800/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3934, 'M91800/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3935, 'M91800/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3936, 'M91810/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3937, 'M91820/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3938, 'M91820/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3939, 'M91830/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3940, 'M91840/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3941, 'M91850/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3942, 'M91860/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3943, 'M91870/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3944, 'M91900/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3945, 'M91910/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3946, 'M91920/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3947, 'M91930/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3948, 'M91940/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3949, 'M91950/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3950, 'M92000/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3951, 'M92000/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3952, 'M92100/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3953, 'M92100/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3954, 'M92100/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3955, 'M92120/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3956, 'M92200/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3957, 'M92200/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3958, 'M92200/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3959, 'M92200/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3960, 'M92201/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3961, 'M92210/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3962, 'M92210/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3963, 'M92211/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3964, 'M92300/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3965, 'M92300/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3966, 'M92300/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3967, 'M92310/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3968, 'M92310/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3969, 'M92400/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3970, 'M92400/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3971, 'M92410/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3972, 'M92420/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3973, 'M92430/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3974, 'M92500/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3975, 'M92500/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3976, 'M92500/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3977, 'M92510/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3978, 'M92510/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3979, 'M92510/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3980, 'M92520/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3981, 'M92520/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3982, 'M92600/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3983, 'M92600/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3984, 'M92610/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3985, 'M92610/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3986, 'M92620/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3987, 'M92621/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3988, 'M92700/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3989, 'M92700/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3990, 'M92700/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3991, 'M92700/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3992, 'M92710/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3993, 'M92720/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3994, 'M92730/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3995, 'M92740/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3996, 'M92750/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3997, 'M92800/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3998, 'M92810/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (3999, 'M92820/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4000, 'M92900/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4001, 'M92900/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4002, 'M92900/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4003, 'M93000/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4004, 'M93001/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4005, 'M93010/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4006, 'M93020/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4007, 'M93100/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4008, 'M93100/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4009, 'M93100/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4010, 'M93110/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4011, 'M93120/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4012, 'M93200/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4013, 'M93210/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4014, 'M93220/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4015, 'M93300/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4016, 'M93300/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4017, 'M93400/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4018, 'M93410/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4019, 'M93420/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4020, 'M93500/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4021, 'M93510/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4022, 'M93520/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4023, 'M93600/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4024, 'M93610/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4025, 'M93620/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4026, 'M93620/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4027, 'M93630/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4028, 'M93640/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4029, 'M93640/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4030, 'M93650/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4031, 'M93700/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4032, 'M93700/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4033, 'M93710/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4034, 'M93720/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4035, 'M93730/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4036, 'M93800/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4037, 'M93800/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4038, 'M93810/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4039, 'M93820/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4040, 'M93830/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4041, 'M93840/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4042, 'M93850/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4043, 'M93900/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4044, 'M93900/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4045, 'M93900/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4046, 'M93900/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4047, 'M93910/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4048, 'M93910/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4049, 'M93920/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4050, 'M93921/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4051, 'M93930/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4052, 'M93940/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4053, 'M94000/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4054, 'M94000/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4055, 'M94010/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4056, 'M94010/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4057, 'M94100/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4058, 'M94110/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4059, 'M94120/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4060, 'M94130/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4061, 'M94200/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4062, 'M94210/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4063, 'M94210/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4064, 'M94211/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4065, 'M94220/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4066, 'M94220/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4067, 'M94230/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4068, 'M94240/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4069, 'M94300/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4070, 'M94400/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4071, 'M94400/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4072, 'M94410/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4073, 'M94420/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4074, 'M94420/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4075, 'M94430/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4076, 'M94440/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4077, 'M94500/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4078, 'M94500/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4079, 'M94510/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4080, 'M94600/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4081, 'M94700/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4082, 'M94700/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4083, 'M94710/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4084, 'M94720/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4085, 'M94730/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4086, 'M94730/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4087, 'M94740/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4088, 'M94740/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4089, 'M94800/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4090, 'M94800/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4091, 'M94810/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4092, 'M94900/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4093, 'M94900/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4094, 'M94900/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4095, 'M94910/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4096, 'M94920/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4097, 'M94930/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4098, 'M95000/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4099, 'M95000/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4100, 'M95010/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4101, 'M95010/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4102, 'M95020/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4103, 'M95030/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4104, 'M95030/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4105, 'M95040/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4106, 'M95050/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4107, 'M95060/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4108, 'M95060/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4109, 'M95060/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4110, 'M95061/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4111, 'M95070/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4112, 'M95080/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4113, 'M95090/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4114, 'M95100/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4115, 'M95100/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4116, 'M95110/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4117, 'M95120/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4118, 'M95130/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4119, 'M95140/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4120, 'M95200/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4121, 'M95200/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4122, 'M95210/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4123, 'M95220/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4124, 'M95220/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4125, 'M95230/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4126, 'M95300/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4127, 'M95300/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4128, 'M95300/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4129, 'M95300/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4130, 'M95301/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4131, 'M95301/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4132, 'M95310/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4133, 'M95320/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4134, 'M95330/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4135, 'M95340/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4136, 'M95350/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4137, 'M95360/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4138, 'M95370/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4139, 'M95380/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4140, 'M95381/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4141, 'M95390/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4142, 'M95390/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4143, 'M95400/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4144, 'M95400/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4145, 'M95400/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4146, 'M95400/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4147, 'M95401/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4148, 'M95410/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4149, 'M95500/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4150, 'M95600/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4151, 'M95600/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4152, 'M95600/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4153, 'M95600/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4154, 'M95610/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4155, 'M95620/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4156, 'M95700/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4157, 'M95710/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4158, 'M95710/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4159, 'M95710/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4160, 'M95800/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4161, 'M95800/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4162, 'M95800/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4163, 'M95801/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4164, 'M95810/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4165, 'M95810/6', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4166, 'M95820/0', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4167, 'M95900/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4168, 'M95901/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4169, 'M95902/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4170, 'M95910/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4171, 'M95920/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4172, 'M95930/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4173, 'M95940/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4174, 'M95950/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4175, 'M95960/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4176, 'M95970/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4177, 'M96500/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4178, 'M96510/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4179, 'M96520/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4180, 'M96521/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4181, 'M96530/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4182, 'M96531/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4183, 'M96540/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4184, 'M96541/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4185, 'M96550/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4186, 'M96551/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4187, 'M96570/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4188, 'M96580/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4189, 'M96590/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4190, 'M96600/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4191, 'M96610/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4192, 'M96620/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4193, 'M96630/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4194, 'M96640/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4195, 'M96650/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4196, 'M96660/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4197, 'M96670/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4198, 'M96700/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4199, 'M96701/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4200, 'M96710/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4201, 'M96720/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4202, 'M96730/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4203, 'M96740/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4204, 'M96750/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4205, 'M96760/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4206, 'M96770/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4207, 'M96780/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4208, 'M96790/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4209, 'M96791/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4210, 'M96800/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4211, 'M96801/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4212, 'M96802/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4213, 'M96803/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4214, 'M96804/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4215, 'M96805/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4216, 'M96810/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4217, 'M96820/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4218, 'M96830/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4219, 'M96840/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4220, 'M96841/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4221, 'M96842/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4222, 'M96850/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4223, 'M96860/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4224, 'M96870/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4225, 'M96871/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4226, 'M96890/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4227, 'M96891/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4228, 'M96900/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4229, 'M96910/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4230, 'M96920/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4231, 'M96930/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4232, 'M96940/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4233, 'M96950/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4234, 'M96960/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4235, 'M96970/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4236, 'M96980/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4237, 'M96990/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4238, 'M96991/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4239, 'M96992/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4240, 'M96993/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4241, 'M96994/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4242, 'M96995/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4243, 'M97000/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4244, 'M97010/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4245, 'M97020/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4246, 'M97021/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4247, 'M97022/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4248, 'M97030/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4249, 'M97040/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4250, 'M97041/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4251, 'M97050/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4252, 'M97051/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4253, 'M97052/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4254, 'M97060/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4255, 'M97070/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4256, 'M97080/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4257, 'M97090/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4258, 'M97091/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4259, 'M97092/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4260, 'M97110/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4261, 'M97120/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4262, 'M97130/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4263, 'M97131/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4264, 'M97140/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4265, 'M97141/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4266, 'M97142/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4267, 'M97160/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4268, 'M97161/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4269, 'M97170/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4270, 'M97171/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4271, 'M97180/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4272, 'M97181/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4273, 'M97190/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4274, 'M97200/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4275, 'M97221/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4276, 'M97222/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4277, 'M97230/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4278, 'M97240/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4279, 'M97241/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4280, 'M97250/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4281, 'M97260/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4282, 'M97270/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4283, 'M97272/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4284, 'M97280/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4285, 'M97290/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4286, 'M97310/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4287, 'M97311/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4288, 'M97312/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4289, 'M97313/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4290, 'M97320/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4291, 'M97321/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4292, 'M97322/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4293, 'M97330/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4294, 'M97340/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4295, 'M97370/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4296, 'M97380/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4297, 'M97400/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4298, 'M97401/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4299, 'M97410/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4300, 'M97500/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4301, 'M97510/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4302, 'M97520/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4303, 'M97530/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4304, 'M97540/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4305, 'M97550/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4306, 'M97560/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4307, 'M97570/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4308, 'M97580/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4309, 'M97581/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4310, 'M97590/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4311, 'M97600/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4312, 'M97610/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4313, 'M97611/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4314, 'M97612/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4315, 'M97620/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4316, 'M97640/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4317, 'M97650/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4318, 'M97660/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4319, 'M97660/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4320, 'M97670/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4321, 'M97680/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4322, 'M97690/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4323, 'M97690/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4324, 'M98000/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4325, 'M98010/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4326, 'M98011/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4327, 'M98012/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4328, 'M98020/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4329, 'M98030/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4330, 'M98040/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4331, 'M98050/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4332, 'M98060/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4333, 'M98070/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4334, 'M98080/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4335, 'M98090/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4336, 'M98110/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4337, 'M98120/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4338, 'M98130/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4339, 'M98140/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4340, 'M98150/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4341, 'M98160/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4342, 'M98170/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4343, 'M98180/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4344, 'M98200/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4345, 'M98201/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4346, 'M98210/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4347, 'M98211/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4348, 'M98212/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4349, 'M98213/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4350, 'M98214/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4351, 'M98220/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4352, 'M98230/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4353, 'M98240/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4354, 'M98250/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4355, 'M98260/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4356, 'M98270/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4357, 'M98300/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4358, 'M98310/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4359, 'M98310/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4360, 'M98320/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4361, 'M98330/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4362, 'M98331/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4363, 'M98340/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4364, 'M98350/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4365, 'M98360/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4366, 'M98370/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4367, 'M98400/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4368, 'M98410/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4369, 'M98420/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4370, 'M98421/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4371, 'M98500/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4372, 'M98600/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4373, 'M98610/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4374, 'M98620/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4375, 'M98630/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4376, 'M98631/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4377, 'M98632/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4378, 'M98640/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4379, 'M98650/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4380, 'M98660/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4381, 'M98661/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4382, 'M98670/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4383, 'M98680/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4384, 'M98700/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4385, 'M98710/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4386, 'M98720/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4387, 'M98730/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4388, 'M98740/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4389, 'M98750/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4390, 'M98760/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4391, 'M98800/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4392, 'M98900/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4393, 'M98901/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4394, 'M98910/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4395, 'M98920/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4396, 'M98930/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4397, 'M98931/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4398, 'M98940/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4399, 'M98950/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4400, 'M98960/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4401, 'M98970/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4402, 'M98980/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4403, 'M99000/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4404, 'M99100/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4405, 'M99110/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4406, 'M99300/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4407, 'M99310/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4408, 'M99320/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4409, 'M99400/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4410, 'M99410/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4411, 'M99460/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4412, 'M99480/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4413, 'M99500/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4414, 'M99600/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4415, 'M99610/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4416, 'M99611/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4417, 'M99620/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4418, 'M99630/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4419, 'M99640/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4420, 'M99650/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4421, 'M99660/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4422, 'M99670/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4423, 'M99700/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4424, 'M99700/3', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4425, 'M99800/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4426, 'M99810/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4427, 'M99820/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4428, 'M99830/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4429, 'M99840/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4430, 'M99890/1', '5', '疾病编码为病理编码，不能用于疾病诊断，肿瘤诊断应说明肿瘤的部位及性质（C00-D48），如左肺恶性肿瘤', NULL);
INSERT INTO `som_chk_diag_sec` VALUES (4431, 'B15.000', '7', '此编码为昏迷编码，当昏迷时间的当“天”“时”“分”其中之一值\r\n≠0时，主要诊断或者其他诊断需包\r\n含与昏迷诊断相关的编码', '甲型肝炎，伴有肝昏迷');
INSERT INTO `som_chk_diag_sec` VALUES (4432, 'B15.001', '7', '此编码为昏迷编码，当昏迷时间的当“天”“时”“分”其中之一值\r\n≠0时，主要诊断或者其他诊断需包\r\n含与昏迷诊断相关的编码', '急性甲型病毒性肝炎伴肝昏迷');
INSERT INTO `som_chk_diag_sec` VALUES (4433, 'B15.002', '7', '此编码为昏迷编码，当昏迷时间的当“天”“时”“分”其中之一值\r\n≠0时，主要诊断或者其他诊断需包\r\n含与昏迷诊断相关的编码', '急性重型甲型病毒性肝炎伴肝昏迷');
INSERT INTO `som_chk_diag_sec` VALUES (4434, 'B15.003', '7', '此编码为昏迷编码，当昏迷时间的当“天”“时”“分”其中之一值\r\n≠0时，主要诊断或者其他诊断需包\r\n含与昏迷诊断相关的编码', '亚急性重型甲型病毒性肝炎伴肝昏迷');
INSERT INTO `som_chk_diag_sec` VALUES (4435, 'B15.900', '7', '此编码为昏迷编码，当昏迷时间的当“天”“时”“分”其中之一值\r\n≠0时，主要诊断或者其他诊断需包\r\n含与昏迷诊断相关的编码', '甲型肝炎，不伴有肝昏迷');
INSERT INTO `som_chk_diag_sec` VALUES (4436, 'B16.000', '7', '此编码为昏迷编码，当昏迷时间的当“天”“时”“分”其中之一值\r\n≠0时，主要诊断或者其他诊断需包\r\n含与昏迷诊断相关的编码', '急性乙型肝炎，伴有δ因子（共同感染），并伴有肝昏迷');
INSERT INTO `som_chk_diag_sec` VALUES (4437, 'B16.001', '7', '此编码为昏迷编码，当昏迷时间的当“天”“时”“分”其中之一值\r\n≠0时，主要诊断或者其他诊断需包\r\n含与昏迷诊断相关的编码', '急性乙型丁型病毒性肝炎伴肝昏迷');
INSERT INTO `som_chk_diag_sec` VALUES (4438, 'B16.100', '7', '此编码为昏迷编码，当昏迷时间的当“天”“时”“分”其中之一值\r\n≠0时，主要诊断或者其他诊断需包\r\n含与昏迷诊断相关的编码', '急性乙型肝炎，伴有δ因子（共同感染），但不伴有肝昏迷');
INSERT INTO `som_chk_diag_sec` VALUES (4439, 'B16.101', '7', '此编码为昏迷编码，当昏迷时间的当“天”“时”“分”其中之一值\r\n≠0时，主要诊断或者其他诊断需包\r\n含与昏迷诊断相关的编码', '急性乙型丁型病毒性肝炎，不伴有肝昏迷');
INSERT INTO `som_chk_diag_sec` VALUES (4440, 'B16.200', '7', '此编码为昏迷编码，当昏迷时间的当“天”“时”“分”其中之一值\r\n≠0时，主要诊断或者其他诊断需包\r\n含与昏迷诊断相关的编码', '急性乙型肝炎，不伴有δ因子（共同感染），但伴有肝昏迷');
INSERT INTO `som_chk_diag_sec` VALUES (4441, 'B16.201', '7', '此编码为昏迷编码，当昏迷时间的当“天”“时”“分”其中之一值\r\n≠0时，主要诊断或者其他诊断需包\r\n含与昏迷诊断相关的编码', '急性乙型病毒性肝炎伴肝昏迷');
INSERT INTO `som_chk_diag_sec` VALUES (4442, 'B16.202', '7', '此编码为昏迷编码，当昏迷时间的当“天”“时”“分”其中之一值\r\n≠0时，主要诊断或者其他诊断需包\r\n含与昏迷诊断相关的编码', '亚急性重型乙型病毒性肝炎伴肝昏迷');
INSERT INTO `som_chk_diag_sec` VALUES (4443, 'B16.203', '7', '此编码为昏迷编码，当昏迷时间的当“天”“时”“分”其中之一值\r\n≠0时，主要诊断或者其他诊断需包\r\n含与昏迷诊断相关的编码', '急性重型乙型病毒性肝炎伴肝昏迷');
INSERT INTO `som_chk_diag_sec` VALUES (4444, 'B16.204', '7', '此编码为昏迷编码，当昏迷时间的当“天”“时”“分”其中之一值\r\n≠0时，主要诊断或者其他诊断需包\r\n含与昏迷诊断相关的编码', '急性无黄疸型乙型肝炎伴肝昏迷');
INSERT INTO `som_chk_diag_sec` VALUES (4445, 'B16.900', '7', '此编码为昏迷编码，当昏迷时间的当“天”“时”“分”其中之一值\r\n≠0时，主要诊断或者其他诊断需包\r\n含与昏迷诊断相关的编码', '急性乙型肝炎，不伴有δ因子（共同感染），也不伴有肝昏迷');
INSERT INTO `som_chk_diag_sec` VALUES (4446, 'B19.000', '7', '此编码为昏迷编码，当昏迷时间的当“天”“时”“分”其中之一值\r\n≠0时，主要诊断或者其他诊断需包\r\n含与昏迷诊断相关的编码', '病毒性肝炎，伴有肝昏迷');
INSERT INTO `som_chk_diag_sec` VALUES (4447, 'B19.001', '7', '此编码为昏迷编码，当昏迷时间的当“天”“时”“分”其中之一值\r\n≠0时，主要诊断或者其他诊断需包\r\n含与昏迷诊断相关的编码', '急性重型病毒性肝炎伴肝昏迷');
INSERT INTO `som_chk_diag_sec` VALUES (4448, 'B19.002', '7', '此编码为昏迷编码，当昏迷时间的当“天”“时”“分”其中之一值\r\n≠0时，主要诊断或者其他诊断需包\r\n含与昏迷诊断相关的编码', '亚急性重型病毒性肝炎伴肝昏迷');
INSERT INTO `som_chk_diag_sec` VALUES (4449, 'B19.900', '7', '此编码为昏迷编码，当昏迷时间的当“天”“时”“分”其中之一值\r\n≠0时，主要诊断或者其他诊断需包\r\n含与昏迷诊断相关的编码', '病毒性肝炎，不伴有肝昏迷');
INSERT INTO `som_chk_diag_sec` VALUES (4450, 'B25.101+K77.0*', '7', '此编码为昏迷编码，当昏迷时间的当“天”“时”“分”其中之一值\r\n≠0时，主要诊断或者其他诊断需包\r\n含与昏迷诊断相关的编码', '巨细胞病毒性肝炎伴肝昏迷');
INSERT INTO `som_chk_diag_sec` VALUES (4451, 'E03.500', '7', '此编码为昏迷编码，当昏迷时间的当“天”“时”“分”其中之一值\r\n≠0时，主要诊断或者其他诊断需包\r\n含与昏迷诊断相关的编码', '黏液性水肿昏迷');
INSERT INTO `som_chk_diag_sec` VALUES (4452, 'E10.000', '7', '此编码为昏迷编码，当昏迷时间的当“天”“时”“分”其中之一值\r\n≠0时，主要诊断或者其他诊断需包\r\n含与昏迷诊断相关的编码', '1型糖尿病伴有昏迷');
INSERT INTO `som_chk_diag_sec` VALUES (4453, 'E10.000x001', '7', '此编码为昏迷编码，当昏迷时间的当“天”“时”“分”其中之一值\r\n≠0时，主要诊断或者其他诊断需包\r\n含与昏迷诊断相关的编码', '1型糖尿病性高渗性高血糖状态昏迷');
INSERT INTO `som_chk_diag_sec` VALUES (4454, 'E10.000x002', '7', '此编码为昏迷编码，当昏迷时间的当“天”“时”“分”其中之一值\r\n≠0时，主要诊断或者其他诊断需包\r\n含与昏迷诊断相关的编码', '1型糖尿病性高血糖状态昏迷');
INSERT INTO `som_chk_diag_sec` VALUES (4455, 'E10.000x005', '7', '此编码为昏迷编码，当昏迷时间的当“天”“时”“分”其中之一值\r\n≠0时，主要诊断或者其他诊断需包\r\n含与昏迷诊断相关的编码', '1型糖尿病性乳酸性酸中毒并昏迷');
INSERT INTO `som_chk_diag_sec` VALUES (4456, 'E10.000x006', '7', '此编码为昏迷编码，当昏迷时间的当“天”“时”“分”其中之一值\r\n≠0时，主要诊断或者其他诊断需包\r\n含与昏迷诊断相关的编码', '1型糖尿病性酮症酸中毒和乳酸性酸中毒并昏迷');
INSERT INTO `som_chk_diag_sec` VALUES (4457, 'E10.001', '7', '此编码为昏迷编码，当昏迷时间的当“天”“时”“分”其中之一值\r\n≠0时，主要诊断或者其他诊断需包\r\n含与昏迷诊断相关的编码', '1型糖尿病性高渗性昏迷');
INSERT INTO `som_chk_diag_sec` VALUES (4458, 'E10.002', '7', '此编码为昏迷编码，当昏迷时间的当“天”“时”“分”其中之一值\r\n≠0时，主要诊断或者其他诊断需包\r\n含与昏迷诊断相关的编码', '1型糖尿病性低血糖昏迷');
INSERT INTO `som_chk_diag_sec` VALUES (4459, 'E10.003', '7', '此编码为昏迷编码，当昏迷时间的当“天”“时”“分”其中之一值\r\n≠0时，主要诊断或者其他诊断需包\r\n含与昏迷诊断相关的编码', '1型糖尿病性酮症酸中毒昏迷');
INSERT INTO `som_chk_diag_sec` VALUES (4460, 'E11.000', '7', '此编码为昏迷编码，当昏迷时间的当“天”“时”“分”其中之一值\r\n≠0时，主要诊断或者其他诊断需包\r\n含与昏迷诊断相关的编码', '2型糖尿病伴有昏迷');
INSERT INTO `som_chk_diag_sec` VALUES (4461, 'E11.000x001', '7', '此编码为昏迷编码，当昏迷时间的当“天”“时”“分”其中之一值\r\n≠0时，主要诊断或者其他诊断需包\r\n含与昏迷诊断相关的编码', '2型糖尿病性高渗性高血糖状态昏迷');
INSERT INTO `som_chk_diag_sec` VALUES (4462, 'E11.000x005', '7', '此编码为昏迷编码，当昏迷时间的当“天”“时”“分”其中之一值\r\n≠0时，主要诊断或者其他诊断需包\r\n含与昏迷诊断相关的编码', '2型糖尿病性乳酸性酸中毒并昏迷');
INSERT INTO `som_chk_diag_sec` VALUES (4463, 'E11.000x006', '7', '此编码为昏迷编码，当昏迷时间的当“天”“时”“分”其中之一值\r\n≠0时，主要诊断或者其他诊断需包\r\n含与昏迷诊断相关的编码', '2型糖尿病性酮症酸中毒和乳酸性酸中毒并昏迷');
INSERT INTO `som_chk_diag_sec` VALUES (4464, 'E11.001', '7', '此编码为昏迷编码，当昏迷时间的当“天”“时”“分”其中之一值\r\n≠0时，主要诊断或者其他诊断需包\r\n含与昏迷诊断相关的编码', '2型糖尿病性高渗性昏迷');
INSERT INTO `som_chk_diag_sec` VALUES (4465, 'E11.002', '7', '此编码为昏迷编码，当昏迷时间的当“天”“时”“分”其中之一值\r\n≠0时，主要诊断或者其他诊断需包\r\n含与昏迷诊断相关的编码', '2型糖尿病性低血糖性昏迷');
INSERT INTO `som_chk_diag_sec` VALUES (4466, 'E11.003', '7', '此编码为昏迷编码，当昏迷时间的当“天”“时”“分”其中之一值\r\n≠0时，主要诊断或者其他诊断需包\r\n含与昏迷诊断相关的编码', '2型糖尿病性酮症酸中毒昏迷');
INSERT INTO `som_chk_diag_sec` VALUES (4467, 'E12.000', '7', '此编码为昏迷编码，当昏迷时间的当“天”“时”“分”其中之一值\r\n≠0时，主要诊断或者其他诊断需包\r\n含与昏迷诊断相关的编码', '营养不良相关性糖尿病伴有昏迷');
INSERT INTO `som_chk_diag_sec` VALUES (4468, 'E13.000', '7', '此编码为昏迷编码，当昏迷时间的当“天”“时”“分”其中之一值\r\n≠0时，主要诊断或者其他诊断需包\r\n含与昏迷诊断相关的编码', '糖尿病伴有昏迷，其他特指的');
INSERT INTO `som_chk_diag_sec` VALUES (4469, 'E14.000', '7', '此编码为昏迷编码，当昏迷时间的当“天”“时”“分”其中之一值\r\n≠0时，主要诊断或者其他诊断需包\r\n含与昏迷诊断相关的编码', '糖尿病伴有昏迷');
INSERT INTO `som_chk_diag_sec` VALUES (4470, 'E14.000x001', '7', '此编码为昏迷编码，当昏迷时间的当“天”“时”“分”其中之一值\r\n≠0时，主要诊断或者其他诊断需包\r\n含与昏迷诊断相关的编码', '糖尿病性高渗性高血糖状态昏迷');
INSERT INTO `som_chk_diag_sec` VALUES (4471, 'E14.000x002', '7', '此编码为昏迷编码，当昏迷时间的当“天”“时”“分”其中之一值\r\n≠0时，主要诊断或者其他诊断需包\r\n含与昏迷诊断相关的编码', '糖尿病性高血糖状态昏迷');
INSERT INTO `som_chk_diag_sec` VALUES (4472, 'E14.000x003', '7', '此编码为昏迷编码，当昏迷时间的当“天”“时”“分”其中之一值\r\n≠0时，主要诊断或者其他诊断需包\r\n含与昏迷诊断相关的编码', '糖尿病性低血糖昏迷');
INSERT INTO `som_chk_diag_sec` VALUES (4473, 'E14.000x004', '7', '此编码为昏迷编码，当昏迷时间的当“天”“时”“分”其中之一值\r\n≠0时，主要诊断或者其他诊断需包\r\n含与昏迷诊断相关的编码', '糖尿病性酮症酸中毒并昏迷');
INSERT INTO `som_chk_diag_sec` VALUES (4474, 'E14.000x005', '7', '此编码为昏迷编码，当昏迷时间的当“天”“时”“分”其中之一值\r\n≠0时，主要诊断或者其他诊断需包\r\n含与昏迷诊断相关的编码', '糖尿病性乳酸性酸中毒并昏迷');
INSERT INTO `som_chk_diag_sec` VALUES (4475, 'E14.000x006', '7', '此编码为昏迷编码，当昏迷时间的当“天”“时”“分”其中之一值\r\n≠0时，主要诊断或者其他诊断需包\r\n含与昏迷诊断相关的编码', '糖尿病性酮症酸中毒和乳酸性酸中毒并昏迷');
INSERT INTO `som_chk_diag_sec` VALUES (4476, 'E16.000', '7', '此编码为昏迷编码，当昏迷时间的当“天”“时”“分”其中之一值\r\n≠0时，主要诊断或者其他诊断需包\r\n含与昏迷诊断相关的编码', '药物性低血糖不伴有昏迷');
INSERT INTO `som_chk_diag_sec` VALUES (4477, 'E16.107+G94.8*', '7', '此编码为昏迷编码，当昏迷时间的当“天”“时”“分”其中之一值\r\n≠0时，主要诊断或者其他诊断需包\r\n含与昏迷诊断相关的编码', '低血糖昏迷性脑病');
INSERT INTO `som_chk_diag_sec` VALUES (4478, 'E16.111+G94.3*', '7', '此编码为昏迷编码，当昏迷时间的当“天”“时”“分”其中之一值\r\n≠0时，主要诊断或者其他诊断需包\r\n含与昏迷诊断相关的编码', '低血糖性脑昏迷');
INSERT INTO `som_chk_diag_sec` VALUES (4479, 'E15.x00', '7', '此编码为昏迷编码，当昏迷时间的当“天”“时”“分”其中之一值\r\n≠0时，主要诊断或者其他诊断需包\r\n含与昏迷诊断相关的编码', '非糖尿病低血糖性昏迷');
INSERT INTO `som_chk_diag_sec` VALUES (4480, 'E15.x00x001', '7', '此编码为昏迷编码，当昏迷时间的当“天”“时”“分”其中之一值\r\n≠0时，主要诊断或者其他诊断需包\r\n含与昏迷诊断相关的编码', '低血糖性昏迷');
INSERT INTO `som_chk_diag_sec` VALUES (4481, 'E15.x00x002', '7', '此编码为昏迷编码，当昏迷时间的当“天”“时”“分”其中之一值\r\n≠0时，主要诊断或者其他诊断需包\r\n含与昏迷诊断相关的编码', '非糖尿病引起的药物性胰岛素性昏迷');
INSERT INTO `som_chk_diag_sec` VALUES (4482, 'E15.x00x004', '7', '此编码为昏迷编码，当昏迷时间的当“天”“时”“分”其中之一值\r\n≠0时，主要诊断或者其他诊断需包\r\n含与昏迷诊断相关的编码', '胰岛素分泌过多伴低血糖性昏迷');
INSERT INTO `som_chk_diag_sec` VALUES (4483, 'E89.101', '7', '此编码为昏迷编码，当昏迷时间的当“天”“时”“分”其中之一值\r\n≠0时，主要诊断或者其他诊断需包\r\n含与昏迷诊断相关的编码', '手术后低血糖昏迷');
INSERT INTO `som_chk_diag_sec` VALUES (4484, 'G93.800x012', '7', '此编码为昏迷编码，当昏迷时间的当“天”“时”“分”其中之一值\r\n≠0时，主要诊断或者其他诊断需包\r\n含与昏迷诊断相关的编码', '无动性缄默[睁眼昏迷]');
INSERT INTO `som_chk_diag_sec` VALUES (4485, 'K70.403', '7', '此编码为昏迷编码，当昏迷时间的当“天”“时”“分”其中之一值\r\n≠0时，主要诊断或者其他诊断需包\r\n含与昏迷诊断相关的编码', '酒精性肝衰竭伴肝昏迷');
INSERT INTO `som_chk_diag_sec` VALUES (4486, 'P91.500', '7', '此编码为昏迷编码，当昏迷时间的当“天”“时”“分”其中之一值\r\n≠0时，主要诊断或者其他诊断需包\r\n含与昏迷诊断相关的编码', '新生儿昏迷');
INSERT INTO `som_chk_diag_sec` VALUES (4487, 'S06.700', '7', '此编码为昏迷编码，当昏迷时间的当“天”“时”“分”其中之一值\r\n≠0时，主要诊断或者其他诊断需包\r\n含与昏迷诊断相关的编码', '颅内损伤伴有延长的昏迷');
INSERT INTO `som_chk_diag_sec` VALUES (4488, 'S06.710', '7', '此编码为昏迷编码，当昏迷时间的当“天”“时”“分”其中之一值\r\n≠0时，主要诊断或者其他诊断需包\r\n含与昏迷诊断相关的编码', '开放性颅内损伤伴长时间昏迷');
INSERT INTO `som_chk_diag_sec` VALUES (4489, 'R40.000', '7', '此编码为昏迷编码，当昏迷时间的当“天”“时”“分”其中之一值\r\n≠0时，主要诊断或者其他诊断需包\r\n含与昏迷诊断相关的编码', '嗜眠');
INSERT INTO `som_chk_diag_sec` VALUES (4490, 'R40.100', '7', '此编码为昏迷编码，当昏迷时间的当“天”“时”“分”其中之一值\r\n≠0时，主要诊断或者其他诊断需包\r\n含与昏迷诊断相关的编码', '木僵');
INSERT INTO `som_chk_diag_sec` VALUES (4491, 'R40.100x002', '7', '此编码为昏迷编码，当昏迷时间的当“天”“时”“分”其中之一值\r\n≠0时，主要诊断或者其他诊断需包\r\n含与昏迷诊断相关的编码', '中昏迷');
INSERT INTO `som_chk_diag_sec` VALUES (4492, 'R40.100x003', '7', '此编码为昏迷编码，当昏迷时间的当“天”“时”“分”其中之一值\r\n≠0时，主要诊断或者其他诊断需包\r\n含与昏迷诊断相关的编码', '浅昏迷');
INSERT INTO `som_chk_diag_sec` VALUES (4493, 'R40.100x005', '7', '此编码为昏迷编码，当昏迷时间的当“天”“时”“分”其中之一值\r\n≠0时，主要诊断或者其他诊断需包\r\n含与昏迷诊断相关的编码', '亚木僵');
INSERT INTO `som_chk_diag_sec` VALUES (4494, 'R40.200', '7', '此编码为昏迷编码，当昏迷时间的当“天”“时”“分”其中之一值\r\n≠0时，主要诊断或者其他诊断需包\r\n含与昏迷诊断相关的编码', '昏迷');
INSERT INTO `som_chk_diag_sec` VALUES (4495, 'R40.200x002', '7', '此编码为昏迷编码，当昏迷时间的当“天”“时”“分”其中之一值\r\n≠0时，主要诊断或者其他诊断需包\r\n含与昏迷诊断相关的编码', '一过性意识丧失');
INSERT INTO `som_chk_diag_sec` VALUES (4496, 'R40.200x004', '7', '此编码为昏迷编码，当昏迷时间的当“天”“时”“分”其中之一值\r\n≠0时，主要诊断或者其他诊断需包\r\n含与昏迷诊断相关的编码', '深昏迷');
INSERT INTO `som_chk_diag_sec` VALUES (4497, 'R40.200x005', '7', '此编码为昏迷编码，当昏迷时间的当“天”“时”“分”其中之一值\r\n≠0时，主要诊断或者其他诊断需包\r\n含与昏迷诊断相关的编码', '意识模糊');
INSERT INTO `som_chk_diag_sec` VALUES (4498, 'R40.201', '7', '此编码为昏迷编码，当昏迷时间的当“天”“时”“分”其中之一值\r\n≠0时，主要诊断或者其他诊断需包\r\n含与昏迷诊断相关的编码', '意识丧失');

SET FOREIGN_KEY_CHECKS = 1;
