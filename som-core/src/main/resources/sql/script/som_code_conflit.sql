
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for som_code_conflit
-- ----------------------------
DROP TABLE IF EXISTS `som_code_conflit`;
CREATE TABLE `som_code_conflit`  (
  `ID` int NOT NULL AUTO_INCREMENT COMMENT '诊断编码存在冲突集合ID',
  `cate_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '类目/亚目编码',
  `cate_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '类目/亚目编码名称',
  `conflict_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '冲突编码',
  `conflict_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '冲突名称',
  `dscr` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '描述',
  `enable` tinyint NULL DEFAULT 1 COMMENT '启用状态 0：禁用 1：启用',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 26 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of som_code_conflit
-- ----------------------------
INSERT INTO `som_code_conflit` VALUES (1, 'O00', '异位妊娠', 'Z37', '分娩的结局', '当其他诊断包含Z37编码时，主要诊断或者其他诊断编码不能同时存在O00-O08编码。', 1);
INSERT INTO `som_code_conflit` VALUES (2, 'O01', '葡萄胎[水泡状胎块]', 'Z37', '分娩的结局', '当其他诊断包含Z37编码时，主要诊断或者其他诊断编码不能同时存在O00-O08编码。', 1);
INSERT INTO `som_code_conflit` VALUES (3, 'O02', '受孕的其他异常产物', 'Z37', '分娩的结局', '当其他诊断包含Z37编码时，主要诊断或者其他诊断编码不能同时存在O00-O08编码。', 1);
INSERT INTO `som_code_conflit` VALUES (4, 'O03', '自然流产', 'Z37', '分娩的结局', '当其他诊断包含Z37编码时，主要诊断或者其他诊断编码不能同时存在O00-O08编码。', 1);
INSERT INTO `som_code_conflit` VALUES (5, 'O04', '医疗性流产', 'Z37', '分娩的结局', '当其他诊断包含Z37编码时，主要诊断或者其他诊断编码不能同时存在O00-O08编码。', 1);
INSERT INTO `som_code_conflit` VALUES (6, 'O05', '其他流产', 'Z37', '分娩的结局', '当其他诊断包含Z37编码时，主要诊断或者其他诊断编码不能同时存在O00-O08编码。', 1);
INSERT INTO `som_code_conflit` VALUES (7, 'O06', '未特指的流产', 'Z37', '分娩的结局', '当其他诊断包含Z37编码时，主要诊断或者其他诊断编码不能同时存在O00-O08编码。', 1);
INSERT INTO `som_code_conflit` VALUES (8, 'O07', '企图流产失败', 'Z37', '分娩的结局', '当其他诊断包含Z37编码时，主要诊断或者其他诊断编码不能同时存在O00-O08编码。', 1);
INSERT INTO `som_code_conflit` VALUES (9, 'O08', '流产、异位妊娠和葡萄胎妊娠后的并发症', 'Z37', '分娩的结局', '当其他诊断包含Z37编码时，主要诊断或者其他诊断编码不能同时存在O00-O08编码。', 1);
INSERT INTO `som_code_conflit` VALUES (10, 'J09', '被标明的人畜共患传染病病毒或流感病毒引起的流行性感冒', 'J85.1', '肺脓肿伴有肺炎', '“当主要诊断或其他诊断包含J09-J16时，主要诊断或者其他诊断编码不能同时存在J85.1。”\n变更为“当主要诊断或其他诊断包含J12-J16时，主要诊断或者其他诊断编码不能同时存在J85.1。”', 1);
INSERT INTO `som_code_conflit` VALUES (11, 'J10', '被标明的季节性流感病毒引起的流行性感冒', 'J85.1', '肺脓肿伴有肺炎', '“当主要诊断或其他诊断包含J09-J16时，主要诊断或者其他诊断编码不能同时存在J85.1。”\n变更为“当主要诊断或其他诊断包含J12-J16时，主要诊断或者其他诊断编码不能同时存在J85.1。”', 1);
INSERT INTO `som_code_conflit` VALUES (12, 'J11', '流行性感冒，病毒未标明', 'J85.1', '肺脓肿伴有肺炎', '“当主要诊断或其他诊断包含J09-J16时，主要诊断或者其他诊断编码不能同时存在J85.1。”\n变更为“当主要诊断或其他诊断包含J12-J16时，主要诊断或者其他诊断编码不能同时存在J85.1。”', 1);
INSERT INTO `som_code_conflit` VALUES (13, 'J12', '病毒性肺炎，不可归类在他处者', 'J85.1', '肺脓肿伴有肺炎', '“当主要诊断或其他诊断包含J09-J16时，主要诊断或者其他诊断编码不能同时存在J85.1。”\n变更为“当主要诊断或其他诊断包含J12-J16时，主要诊断或者其他诊断编码不能同时存在J85.1。”', 1);
INSERT INTO `som_code_conflit` VALUES (14, 'J13', '链球菌性肺炎', 'J85.1', '肺脓肿伴有肺炎', '“当主要诊断或其他诊断包含J09-J16时，主要诊断或者其他诊断编码不能同时存在J85.1。”\n变更为“当主要诊断或其他诊断包含J12-J16时，主要诊断或者其他诊断编码不能同时存在J85.1。”', 1);
INSERT INTO `som_code_conflit` VALUES (15, 'J14', '流感嗜血杆菌性肺炎', 'J85.1', '肺脓肿伴有肺炎', '“当主要诊断或其他诊断包含J09-J16时，主要诊断或者其他诊断编码不能同时存在J85.1。”\n变更为“当主要诊断或其他诊断包含J12-J16时，主要诊断或者其他诊断编码不能同时存在J85.1。”', 1);
INSERT INTO `som_code_conflit` VALUES (16, 'J15', '细菌性肺炎,不可归类在他处者', 'J85.1', '肺脓肿伴有肺炎', '“当主要诊断或其他诊断包含J09-J16时，主要诊断或者其他诊断编码不能同时存在J85.1。”\n变更为“当主要诊断或其他诊断包含J12-J16时，主要诊断或者其他诊断编码不能同时存在J85.1。”', 1);
INSERT INTO `som_code_conflit` VALUES (17, 'J16', '其他传染性病原体引起的肺炎，不可归类在他处者', 'J85.1', '肺脓肿伴有肺炎', '“当主要诊断或其他诊断包含J09-J16时，主要诊断或者其他诊断编码不能同时存在J85.1。”\n变更为“当主要诊断或其他诊断包含J12-J16时，主要诊断或者其他诊断编码不能同时存在J85.1。”', 1);

SET FOREIGN_KEY_CHECKS = 1;
