/*
 Navicat Premium Dump SQL

 Source Server         : 本地
 Source Server Type    : MySQL
 Source Server Version : 80039 (8.0.39)
 Source Host           : localhost:3306
 Source Schema         : neijiang

 Target Server Type    : MySQL
 Target Server Version : 80039 (8.0.39)
 File Encoding         : 65001

 Date: 25/03/2025 09:13:31
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for som_chain_script
-- ----------------------------
DROP TABLE IF EXISTS `som_chain_script`;
CREATE TABLE `som_chain_script`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `application_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '应用名称',
  `script_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '脚本ID',
  `script_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '脚本名称',
  `script_data` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '脚本数据',
  `script_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '脚本类型',
  `script_language` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '脚本语言',
  `create_time` timestamp NULL DEFAULT NULL COMMENT '创建时间',
  `mean` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '检测对象含义 ',
  `action_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '质检的作用域',
  `time_type` tinyint NULL DEFAULT NULL COMMENT '质检的时间线 0:事前,1:事后',
  `script_enable` tinyint NULL DEFAULT NULL COMMENT '启用标志0无效，1有效',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 153 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of som_chain_script
-- ----------------------------
INSERT INTO `som_chain_script` VALUES (1, 'somplatform', 'check_adm_time_basic', '入院时间[b12]基础质控', 'import com.my.som.model.dataHandle.SomHiInvyBasInfo;\r\nimport com.my.som.settlevalidate.constant.SettleValidateConst;\r\nimport com.my.som.settlevalidate.util.SettleValidateUtil;\r\nimport com.my.som.settlevalidate.vo.SetlValidBaseBusiVo;\r\nimport com.my.som.vo.dataHandle.SettleListHandlerVo;\r\nimport com.my.som.vo.dataHandle.SettleListValidateVo;\r\nimport com.yomahub.liteflow.script.ScriptExecuteWrap;\r\nimport com.yomahub.liteflow.script.body.JaninoCommonScriptBody;\r\nimport org.slf4j.Logger;\r\nimport org.slf4j.LoggerFactory;\r\n\r\nimport java.time.LocalDateTime;\r\nimport java.time.format.DateTimeFormatter;\r\nimport java.time.format.DateTimeParseException;\r\nimport java.util.Map;\r\n\r\npublic class check_adm_time_basic implements JaninoCommonScriptBody {\r\n    private static final Logger logger = LoggerFactory.getLogger(check_adm_time_basic.class);\r\n\r\n    private static final String MAIN_CHECK_FIELD = \"b12\";\r\n    private static final String MAIN_CHECK_NAME = \"入院时间\";\r\n    private static final String MAIN_CHECK_REGION = \"四川,湖北\";\r\n    /**\r\n     * 入院时间(adm_time)基础质控\r\n     * 判断入院时间是否为空\r\n     *\r\n     * @param wrap\r\n     * @return\r\n     */\r\n    @Override\r\n    public Void body(ScriptExecuteWrap wrap) {\r\n        SetlValidBaseBusiVo setlValidBaseBusiVo = (SetlValidBaseBusiVo) wrap.cmp.getFirstContextBean();\r\n        SettleListValidateVo settleListValidateVo = (SettleListValidateVo) wrap.cmp.getRequestData();\r\n        Map<String, String> keysBasicResultMap = setlValidBaseBusiVo.getKeysBasicResultMap();\r\n        SomHiInvyBasInfo somHiInvyBasInfo = settleListValidateVo.getSomHiInvyBasInfo();\r\n        SettleListHandlerVo settleListHandlerVo = new SettleListHandlerVo();\r\n\r\n        // 检查 somHiInvyBasInfo 是否为空\r\n        if (SettleValidateUtil.isEmpty(somHiInvyBasInfo)) {\r\n            return null;\r\n        }\r\n\r\n        // 获取入院时间\r\n        String b12 = somHiInvyBasInfo.getB12();\r\n        if (SettleValidateUtil.isEmpty(b12)) {\r\n            addErrorDetail(SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_ERROR_TYPE_4,\r\n                    MAIN_CHECK_FIELD,\r\n                    MAIN_CHECK_NAME + \"[\" + b12 + \"]为空\",\r\n                    SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n            keysBasicResultMap.put(MAIN_CHECK_FIELD, SettleValidateConst.NULL);\r\n        } else {\r\n            if (!isValidDateTime(b12)) {\r\n                addErrorDetail( SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_ERROR_TYPE_2,\r\n                        MAIN_CHECK_FIELD,MAIN_CHECK_NAME + \"[\" + b12 + \"]时间格式错误\",\r\n                        SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n                keysBasicResultMap.put(MAIN_CHECK_FIELD, SettleValidateConst.OUTLIER);\r\n            } else {\r\n                keysBasicResultMap.put(MAIN_CHECK_FIELD, SettleValidateConst.PASS);\r\n            }\r\n        }\r\n\r\n        return null;\r\n    }\r\n\r\n    public static boolean isValidDateTime(String dateTimeStr) {\r\n        String format =  \"yyyy-MM-dd HH:mm:ss\";\r\n        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(format);\r\n        try {\r\n            LocalDateTime.parse(dateTimeStr, formatter);\r\n            return true;\r\n        } catch (DateTimeParseException e) {\r\n            return false;\r\n        }\r\n    }\r\n\r\n    private void addErrorDetail(String index, String errType, String errorFields, String errDscr, String chkStas, SetlValidBaseBusiVo baseBusiVo) {\r\n        SettleListHandlerVo handlerVo =new SettleListHandlerVo();\r\n        handlerVo.setIndex(index);\r\n        handlerVo.setErrDscr(errDscr);\r\n        handlerVo.setErrorFields(errorFields);\r\n        handlerVo.setChkStas(chkStas);\r\n        handlerVo.setErrType(errType);\r\n        baseBusiVo.getErrorDetails().add(handlerVo);\r\n    }\r\n}\r\n', 'script', 'java', '2024-09-24 17:23:21', '入院时间', '住院诊断', 0, 1);
INSERT INTO `som_chain_script` VALUES (2, 'somplatform', 'check_adm_way_after_basic', '入院途径[b11c]基础质控', 'import com.my.som.model.dataHandle.SomHiInvyBasInfo;\r\nimport com.my.som.settlevalidate.constant.SettleValidateConst;\r\nimport com.my.som.settlevalidate.util.SettleValidateUtil;\r\nimport com.my.som.settlevalidate.vo.SetlValidBaseBusiVo;\r\nimport com.my.som.util.SettleListValidateUtil;\r\nimport com.my.som.vo.dataHandle.SettleListHandlerVo;\r\nimport com.my.som.vo.dataHandle.SettleListValidateVo;\r\nimport com.yomahub.liteflow.script.ScriptExecuteWrap;\r\nimport com.yomahub.liteflow.script.body.JaninoCommonScriptBody;\r\nimport org.slf4j.Logger;\r\nimport org.slf4j.LoggerFactory;\r\n\r\nimport java.util.List;\r\nimport java.util.Map;\r\n\r\n/**\r\n * 判断 入院途径不为空\r\n */\r\npublic class check_adm_way_after_basic implements JaninoCommonScriptBody {\r\n    private static final Logger logger = LoggerFactory.getLogger(check_adm_way_after_basic.class);\r\n\r\n    private static final String MAIN_CHECK_FIELD = \"b11c\";\r\n    private static final String MAIN_CHECK_NAME = \"入院途径\";\r\n    private static final String MAIN_CHECK_REGION = \"四川,湖北\";\r\n    /**\r\n     * 事后校验\r\n     * 入院途径(adm_way)基础质控\r\n     * 判断入院途径是否为空\r\n     * @param wrap\r\n     * @return\r\n     */\r\n    @Override\r\n    public Void body(ScriptExecuteWrap wrap) {\r\n        SetlValidBaseBusiVo setlValidBaseBusiVo = (SetlValidBaseBusiVo) wrap.cmp.getFirstContextBean();\r\n        SettleListValidateVo settleListValidateVo = (SettleListValidateVo) wrap.cmp.getRequestData();\r\n        Map<String, String> keysBasicResultMap = setlValidBaseBusiVo.getKeysBasicResultMap();\r\n        SomHiInvyBasInfo somHiInvyBasInfo = settleListValidateVo.getSomHiInvyBasInfo();\r\n        SettleListHandlerVo settleListHandlerVo = new SettleListHandlerVo();\r\n\r\n        // 检查 somHiInvyBasInfo 是否为空\r\n        if (SettleValidateUtil.isEmpty(somHiInvyBasInfo)) {\r\n            return null;\r\n        }\r\n        Map<String,Object> map = (Map<String,Object>)setlValidBaseBusiVo.getSettleListParams().get(SettleListValidateUtil.KEY_DICT);\r\n        //获取入院途径\r\n        List<String> admWayList = (List<String>)map.get(\"RYTJ\");\r\n        // 获取入院途径\r\n        String b11c = somHiInvyBasInfo.getB11c();\r\n        if (SettleValidateUtil.isEmpty(b11c)) {\r\n            addErrorDetail( SettleValidateConst.VALIDATE_TYPE_1,SettleValidateConst.VALIDATE_TYPE_1,\r\n                    MAIN_CHECK_NAME + \"[\" + b11c + \"]为空\",\r\n                    MAIN_CHECK_FIELD,\r\n                    SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n            keysBasicResultMap.put(MAIN_CHECK_FIELD, SettleValidateConst.NULL);\r\n        } else {\r\n            if(admWayList.contains(b11c)){\r\n                keysBasicResultMap.put(MAIN_CHECK_FIELD, SettleValidateConst.PASS);\r\n            }else {\r\n                addErrorDetail( SettleValidateConst.VALIDATE_TYPE_1,SettleValidateConst.VALIDATE_TYPE_1,\r\n                        MAIN_CHECK_NAME + \"[\" + b11c + \"] 不符合规范\",\r\n                        MAIN_CHECK_FIELD,\r\n                        SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n                keysBasicResultMap.put(MAIN_CHECK_FIELD, SettleValidateConst.OUTLIER);\r\n\r\n            }\r\n        }\r\n        return null;\r\n    }\r\n\r\n    private void addErrorDetail( String index, String errType, String errDscr, String errorFields, String chkStas, SetlValidBaseBusiVo baseBusiVo) {\r\n        SettleListHandlerVo handlerVo = new SettleListHandlerVo();\r\n        handlerVo.setIndex(index);\r\n        handlerVo.setErrDscr(errDscr);\r\n        handlerVo.setErrorFields(errorFields);\r\n        handlerVo.setChkStas(chkStas);\r\n        handlerVo.setErrType(errType);\r\n        baseBusiVo.getErrorDetails().add(handlerVo);\r\n    }\r\n}\r\n', 'script', 'java', '2024-09-24 17:23:21', '入院途径', '基础信息', 1, 1);
INSERT INTO `som_chain_script` VALUES (3, 'somplatform', 'check_age_basic', '年龄(岁)[a14]基础质控', 'import com.my.som.model.dataHandle.SomHiInvyBasInfo;\r\nimport com.my.som.settlevalidate.constant.SettleValidateConst;\r\nimport com.my.som.settlevalidate.util.SettleValidateUtil;\r\nimport com.my.som.settlevalidate.vo.SetlValidBaseBusiVo;\r\nimport com.my.som.vo.dataHandle.SettleListHandlerVo;\r\nimport com.my.som.vo.dataHandle.SettleListValidateVo;\r\nimport com.yomahub.liteflow.script.ScriptExecuteWrap;\r\nimport com.yomahub.liteflow.script.body.JaninoCommonScriptBody;\r\nimport org.slf4j.Logger;\r\nimport org.slf4j.LoggerFactory;\r\n\r\nimport java.util.Map;\r\n\r\npublic class check_age_basic implements JaninoCommonScriptBody {\r\n    private static final Logger logger = LoggerFactory.getLogger(check_age_basic.class);\r\n\r\n    private static final String MAIN_CHECK_FIELD = \"a14\";\r\n    private static final String MAIN_CHECK_NAME = \"年龄(岁)\";\r\n    private static final String MAIN_CHECK_REGION = \"四川,湖北\";\r\n    /**\r\n     * 年龄(age)基础质控\r\n     * 1.判断年龄是否为空\r\n     * 2.不为空判断数值是否合理\r\n     * @param wrap\r\n     * @return\r\n     */\r\n    @Override\r\n    public Void body(ScriptExecuteWrap wrap) {\r\n        SetlValidBaseBusiVo setlValidBaseBusiVo = (SetlValidBaseBusiVo) wrap.cmp.getFirstContextBean();\r\n        SettleListValidateVo settleListValidateVo = (SettleListValidateVo) wrap.cmp.getRequestData();\r\n        Map<String, String> keysBasicResultMap = setlValidBaseBusiVo.getKeysBasicResultMap();\r\n        SomHiInvyBasInfo somHiInvyBasInfo = settleListValidateVo.getSomHiInvyBasInfo();\r\n        SettleListHandlerVo settleListHandlerVo = new SettleListHandlerVo();\r\n\r\n        // 检查 somHiInvyBasInfo 是否为空\r\n        if (SettleValidateUtil.isEmpty(somHiInvyBasInfo)) {\r\n            return null;\r\n        }\r\n\r\n        // 获取年龄\r\n        Integer a14 = somHiInvyBasInfo.getA14();\r\n        if (SettleValidateUtil.isEmpty(a14) || a14 == 0 ){\r\n            if(SettleValidateUtil.isEmpty(a14) ) {\r\n                addErrorDetail(SettleValidateConst.VALIDATE_TYPE_1,SettleValidateConst.VALIDATE_ERROR_TYPE_4,\r\n                        MAIN_CHECK_FIELD,\r\n                        MAIN_CHECK_NAME + \"[\" + a14 + \"]不能为空，如果不满一周岁请填0\",\r\n                        SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n            }\r\n            keysBasicResultMap.put(MAIN_CHECK_FIELD, SettleValidateConst.NULL);\r\n        } else {\r\n            // 判断数值是否合理\r\n            if(a14 < 1 || a14 > 150){\r\n                addErrorDetail(SettleValidateConst.VALIDATE_TYPE_1,SettleValidateConst.VALIDATE_ERROR_TYPE_4,\r\n                        MAIN_CHECK_FIELD,\r\n                        MAIN_CHECK_NAME + \"[\" + a14 + \"]超出[1-150]合理值范围\",\r\n                        SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n                keysBasicResultMap.put(MAIN_CHECK_FIELD, SettleValidateConst.OUTLIER);\r\n            }else{\r\n                keysBasicResultMap.put(MAIN_CHECK_FIELD, SettleValidateConst.PASS);\r\n            }\r\n\r\n        }\r\n\r\n        return null;\r\n    }\r\n\r\n    private void addErrorDetail( String index,String errType,String errorFields, String errDscr, String chkStas, SetlValidBaseBusiVo baseBusiVo) {\r\n        SettleListHandlerVo handlerVo = new SettleListHandlerVo();\r\n        handlerVo.setIndex(index);\r\n        handlerVo.setErrDscr(errDscr);\r\n        handlerVo.setErrorFields(errorFields);\r\n        handlerVo.setChkStas(chkStas);\r\n        handlerVo.setErrType(errType);\r\n        baseBusiVo.getErrorDetails().add(handlerVo);\r\n    }\r\n}', 'script', 'java', '2024-09-24 17:23:21', '年龄', '基本数据', 0, 1);
INSERT INTO `som_chain_script` VALUES (4, 'somplatform', 'check_anst_dr_code_basic', '麻醉医师代码[oprn_oprt_anst_dr_code]基础质控', 'import com.my.som.model.dataHandle.SomHiInvyBasInfo;\r\nimport com.my.som.model.dataHandle.SomOprnOprtInfo;\r\nimport com.my.som.settlevalidate.constant.SettleValidateConst;\r\nimport com.my.som.settlevalidate.util.SettleValidateUtil;\r\nimport com.my.som.settlevalidate.vo.SetlValidBaseBusiVo;\r\nimport com.my.som.util.SettleListValidateUtil;\r\nimport com.my.som.vo.dataHandle.SettleListHandlerVo;\r\nimport com.my.som.vo.dataHandle.SettleListValidateVo;\r\nimport com.yomahub.liteflow.script.ScriptExecuteWrap;\r\nimport com.yomahub.liteflow.script.body.JaninoCommonScriptBody;\r\nimport org.slf4j.Logger;\r\nimport org.slf4j.LoggerFactory;\r\n\r\nimport java.util.List;\r\nimport java.util.Map;\r\n\r\npublic class check_anst_dr_code_basic implements JaninoCommonScriptBody {\r\n    private static final Logger logger = LoggerFactory.getLogger(check_anst_dr_code_basic.class);\r\n\r\n    private static final String MAIN_CHECK_FIELD = \"oprn_oprt_anst_dr_code\";\r\n    private static final String MAIN_CHECK_NAME = \"麻醉医师代码\";\r\n    private static final String MAIN_CHECK_REGION = \"四川\";\r\n    /**\r\n     * 麻醉医师代码(oper_dr_code)质控\r\n     * 1.存在手术或操作时且麻醉方式不为空，麻醉医师代码不能为空。\r\n     * 2.判断麻醉医师代码是否与医保业务编码标准动态维护平合上的麻醉医师姓名相匹配。\r\n     * @param wrap\r\n     * @return\r\n     */\r\n    @Override\r\n    public Void body(ScriptExecuteWrap wrap) {\r\n\r\n        SetlValidBaseBusiVo setlValidBaseBusiVo = (SetlValidBaseBusiVo) wrap.cmp.getFirstContextBean();\r\n        SettleListValidateVo settleListValidateVo = (SettleListValidateVo) wrap.cmp.getRequestData();\r\n        List<SomOprnOprtInfo> busOperateDiagnosisList = settleListValidateVo.getBusOperateDiagnosisList();\r\n        SomHiInvyBasInfo somHiInvyBasInfo = settleListValidateVo.getSomHiInvyBasInfo();\r\n\r\n        Map<String, Object> doctorMap = (Map<String, Object>) setlValidBaseBusiVo.getSettleListParams().get(SettleListValidateUtil.KEY_DOCTOR_CODE);\r\n\r\n        if (!SettleValidateUtil.isEmpty(busOperateDiagnosisList)) {\r\n            for (int i = 0; i < busOperateDiagnosisList.size(); i++) {\r\n                SomOprnOprtInfo somOprnOprtInfo = (SomOprnOprtInfo)busOperateDiagnosisList.get(i);\r\n                // 麻醉方式不为空时\r\n                if(!SettleValidateUtil.isEmpty(somOprnOprtInfo.getC43())){\r\n                    String anstCode = somOprnOprtInfo.getOprn_oprt_anst_dr_code();\r\n                    if (SettleValidateUtil.isEmpty(anstCode)) {\r\n                        addErrorDetail(SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_ERROR_TYPE_1,\r\n                                MAIN_CHECK_FIELD,\r\n                                \"第\" + (i + 1) + \"个手术操作：编码为[\"+somOprnOprtInfo.getC35c()+\"]的\" +MAIN_CHECK_NAME + \"[\" + anstCode + \"]为空\",\r\n                                SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n                    }else{\r\n                        // 不为空 判断是否符合医保医师代码标准\r\n\r\n                        // 获取机构编码\r\n                        String hospitalId = somHiInvyBasInfo.getHospitalId();\r\n                        // 获取医保医师映射表\r\n                        Map<String, Object> map = (Map<String, Object>)doctorMap.get(hospitalId);\r\n                        if(SettleValidateUtil.isEmpty(map)){\r\n                            addErrorDetail(SettleValidateConst.VALIDATE_TYPE_2, SettleValidateConst.VALIDATE_ERROR_TYPE_1,\r\n                                    MAIN_CHECK_FIELD,\r\n                                    \"未获取到医保医师映射编码信息\" ,\r\n                                    SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n                        }else{\r\n                            // 存在医保医师编码映射信息质控通过\r\n                            if(!map.containsKey(anstCode)){\r\n                                addErrorDetail(SettleValidateConst.VALIDATE_TYPE_2, SettleValidateConst.VALIDATE_ERROR_TYPE_1,\r\n                                        MAIN_CHECK_FIELD,\r\n                                        \"第\" + (i + 1) + \"个手术：编码为[\"+somOprnOprtInfo.getC35c()+\"]的\" +MAIN_CHECK_NAME + \"[\" + anstCode + \"] 未在医院医师表中\",\r\n                                        SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n                            }\r\n                        }\r\n                    }\r\n                }\r\n\r\n            }\r\n        }\r\n        return null;\r\n    }\r\n\r\n    private void addErrorDetail(String index,String errType,String errorFields, String errDscr, String chkStas, SetlValidBaseBusiVo baseBusiVo) {\r\n        SettleListHandlerVo handlerVo = new SettleListHandlerVo();\r\n        handlerVo.setIndex(index);\r\n        handlerVo.setErrorFields(errorFields);\r\n        handlerVo.setErrDscr(errDscr);\r\n        handlerVo.setChkStas(chkStas);\r\n        handlerVo.setErrType(errType);\r\n        baseBusiVo.getErrorDetails().add(handlerVo);\r\n    }\r\n}\r\n', 'script', 'java', '2024-09-24 17:23:21', '麻醉医师代码', '手术信息', 0, 1);
INSERT INTO `som_chain_script` VALUES (5, 'somplatform', 'check_anst_dr_name_basic', '麻醉医师姓名[oprn_oprt_anst_dr_name]基础质控', 'import com.my.som.model.dataHandle.SomHiInvyBasInfo;\r\nimport com.my.som.model.dataHandle.SomOprnOprtInfo;\r\nimport com.my.som.settlevalidate.constant.SettleValidateConst;\r\nimport com.my.som.settlevalidate.util.SettleValidateUtil;\r\nimport com.my.som.settlevalidate.vo.SetlValidBaseBusiVo;\r\nimport com.my.som.util.SettleListValidateUtil;\r\nimport com.my.som.vo.dataHandle.SettleListHandlerVo;\r\nimport com.my.som.vo.dataHandle.SettleListValidateVo;\r\nimport com.yomahub.liteflow.script.ScriptExecuteWrap;\r\nimport com.yomahub.liteflow.script.body.JaninoCommonScriptBody;\r\nimport org.slf4j.Logger;\r\nimport org.slf4j.LoggerFactory;\r\n\r\nimport java.util.List;\r\nimport java.util.Map;\r\n\r\npublic class check_anst_dr_name_basic implements JaninoCommonScriptBody {\r\n    private static final Logger logger = LoggerFactory.getLogger(check_anst_dr_name_basic.class);\r\n\r\n    private static final String MAIN_CHECK_FIELD = \"oprn_oprt_anst_dr_name\";\r\n    private static final String MAIN_CHECK_NAME = \"麻醉医师姓名\";\r\n    private static final String MAIN_CHECK_REGION = \"四川\";\r\n    /**\r\n     * 麻醉医师姓名(anst_dr_name)质控\r\n     * 1.存在手术或操作时且麻醉方式不为空，麻醉医师姓名不能为空\r\n     * @param wrap\r\n     * @return\r\n     */\r\n    @Override\r\n    public Void body(ScriptExecuteWrap wrap) {\r\n\r\n        SetlValidBaseBusiVo setlValidBaseBusiVo = (SetlValidBaseBusiVo) wrap.cmp.getFirstContextBean();\r\n        SettleListValidateVo settleListValidateVo = (SettleListValidateVo) wrap.cmp.getRequestData();\r\n        List<SomOprnOprtInfo> busOperateDiagnosisList = settleListValidateVo.getBusOperateDiagnosisList();\r\n        SomHiInvyBasInfo somHiInvyBasInfo = settleListValidateVo.getSomHiInvyBasInfo();\r\n        if (SettleValidateUtil.isEmpty(somHiInvyBasInfo)) {\r\n            return null;\r\n        }\r\n        if (!SettleValidateUtil.isEmpty(busOperateDiagnosisList)) {\r\n            for (int i = 0; i < busOperateDiagnosisList.size(); i++) {\r\n                SomOprnOprtInfo somOprnOprtInfo = (SomOprnOprtInfo)busOperateDiagnosisList.get(i);\r\n                // 麻醉方式不为空\r\n                if(!SettleValidateUtil.isEmpty(somOprnOprtInfo.getC43())){\r\n                    String anstName = somOprnOprtInfo.getOprn_oprt_anst_dr_name();\r\n                    String anstCode = somOprnOprtInfo.getOprn_oprt_anst_dr_code();\r\n                    if (SettleValidateUtil.isEmpty(anstName)) {\r\n                        addErrorDetail(SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_ERROR_TYPE_1,\r\n                                MAIN_CHECK_FIELD,\r\n                                \"第\" + (i + 1) + \"个手术操作：编码为[\"+somOprnOprtInfo.getC35c()+\"]的\" +MAIN_CHECK_NAME + \"[\" + somOprnOprtInfo.getOprn_oprt_anst_dr_name() + \"]为空\",\r\n                                SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n                    }else{\r\n                        if(SettleValidateUtil.isNotEmpty(anstCode)){\r\n                            Map<String,Object> map = (Map<String,Object>)(setlValidBaseBusiVo.getSettleListParams().get(SettleListValidateUtil.KEY_DOCTOR_NAME));\r\n                            Map<String,Object> doctorNameMap = (Map<String,Object>)map.get(somHiInvyBasInfo.getHospitalId()+SettleListValidateUtil.KEY_DOCTOR_NAME);\r\n                            String doctorName= (String)doctorNameMap.get(anstCode);\r\n                            if(SettleValidateUtil.isNotEmpty(doctorName)){\r\n                                if(!anstName.equals(doctorName)){\r\n                                    // 正常值，基础校验通过不写错误信息\r\n                                    addErrorDetail( SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_ERROR_TYPE_2,\r\n                                            MAIN_CHECK_FIELD,\r\n                                            \"第\" + (i + 1) +\"个手术操作：编码为[\"+somOprnOprtInfo.getC35c()+\"]的\"+ MAIN_CHECK_NAME + \"[\" + anstName + \"]与该编码[\" + anstCode + \"]对应的医院麻醉医生名称[\" + doctorName + \"]不匹配\",\r\n                                            SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n                                }\r\n                            }\r\n                        }\r\n                    }\r\n                }\r\n\r\n            }\r\n        }\r\n        return null;\r\n    }\r\n\r\n    private void addErrorDetail(String index,String errType,String errorFields, String errDscr, String chkStas, SetlValidBaseBusiVo baseBusiVo) {\r\n        SettleListHandlerVo handlerVo = new SettleListHandlerVo();\r\n        handlerVo.setIndex(index);\r\n        handlerVo.setErrorFields(errorFields);\r\n        handlerVo.setErrDscr(errDscr);\r\n        handlerVo.setChkStas(chkStas);\r\n        handlerVo.setErrType(errType);\r\n        baseBusiVo.getErrorDetails().add(handlerVo);\r\n    }\r\n}\r\n', 'script', 'java', '2024-09-24 17:23:21', '麻醉医师名称', '手术信息', 0, 1);
INSERT INTO `som_chain_script` VALUES (6, 'somplatform', 'check_anst_way_after_basic', '麻醉方式[c43]基础质控', 'import com.my.som.common.util.ValidateUtil;\r\nimport com.my.som.model.dataHandle.SomOprnOprtInfo;\r\nimport com.my.som.settlevalidate.constant.SettleValidateConst;\r\nimport com.my.som.settlevalidate.vo.SetlValidBaseBusiVo;\r\nimport com.my.som.util.SettleListValidateUtil;\r\nimport com.my.som.vo.dataHandle.SettleListHandlerVo;\r\nimport com.my.som.vo.dataHandle.SettleListValidateVo;\r\nimport com.yomahub.liteflow.script.ScriptExecuteWrap;\r\nimport com.yomahub.liteflow.script.body.JaninoCommonScriptBody;\r\nimport org.slf4j.Logger;\r\nimport org.slf4j.LoggerFactory;\r\n\r\nimport java.util.List;\r\nimport java.util.Map;\r\n\r\n/**\r\n * 事后质检 麻醉方式\r\n */\r\npublic class check_anst_way_after_basic implements JaninoCommonScriptBody {\r\n    private static final Logger logger = LoggerFactory.getLogger(check_anst_way_after_basic.class);\r\n\r\n    private static final String MAIN_CHECK_FIELD = \"c43\";\r\n    private static final String MAIN_CHECK_NAME = \"麻醉方式\";\r\n    private static final String MZFS = \"MZFS\";\r\n    private static final String MAIN_CHECK_REGION = \"四川,湖北\";\r\n    /**\r\n     * 麻醉开始时间(anst_begntime)基础质控\r\n     * 麻醉结束时间(anst_endtime)基础质控\r\n     * 判断麻醉开始时间是否为空、麻醉结束时间是否为空\r\n     * 麻醉开始时间是否小于麻醉结束时间\r\n     *\r\n     * @param wrap\r\n     * @return\r\n     */\r\n    @Override\r\n    public Void body(ScriptExecuteWrap wrap) {\r\n        SetlValidBaseBusiVo setlValidBaseBusiVo = (SetlValidBaseBusiVo) wrap.cmp.getFirstContextBean();\r\n        SettleListValidateVo settleListValidateVo = (SettleListValidateVo) wrap.cmp.getRequestData();\r\n        Map<String, String> keysBasicResultMap = setlValidBaseBusiVo.getKeysBasicResultMap();\r\n        List<SomOprnOprtInfo> busOperateDiagnosisList = settleListValidateVo.getBusOperateDiagnosisList();\r\n        SettleListHandlerVo settleListHandlerVo = new SettleListHandlerVo();\r\n\r\n        // 检查 busOperateDiagnosisList(手术操作) 是否为空\r\n        if (ValidateUtil.isEmpty(busOperateDiagnosisList)) {\r\n            return null;\r\n        }\r\n\r\n        //判断是否符合规范 获取麻醉方式字典 集合\r\n        Map<String, Object> map = (Map<String, Object>) setlValidBaseBusiVo.getSettleListParams().get(SettleListValidateUtil.KEY_DICT);\r\n        List<String> anstWayList = (List<String>) map.get(MZFS);\r\n\r\n        for (int i = 0; i < busOperateDiagnosisList.size(); i++) {\r\n            SomOprnOprtInfo somOprnOprtInfo = (SomOprnOprtInfo)busOperateDiagnosisList.get(i);\r\n\r\n            if (ValidateUtil.isNotEmpty(somOprnOprtInfo.getC43())) {\r\n                if (!anstWayList.contains(somOprnOprtInfo.getC43())) {\r\n                    addErrorDetail(SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_ERROR_TYPE_2,\r\n                            MAIN_CHECK_FIELD, \"第\" + (i + 1) + \"个手术操作：编码为[\"+somOprnOprtInfo.getC35c()+\"]的\" + MAIN_CHECK_NAME + \"[\" + somOprnOprtInfo.getC43() + \"]不符合规范\",\r\n                            SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n                    keysBasicResultMap.put(i + MAIN_CHECK_FIELD, SettleValidateConst.OUTLIER);\r\n                } else {\r\n                    keysBasicResultMap.put(i + MAIN_CHECK_FIELD, SettleValidateConst.PASS);\r\n                }\r\n            } else {\r\n                keysBasicResultMap.put(i + MAIN_CHECK_FIELD, SettleValidateConst.NULL);\r\n            }\r\n        }\r\n        return null;\r\n    }\r\n\r\n    private void addErrorDetail(String index, String errType, String errorFields, String errDscr, String chkStas, SetlValidBaseBusiVo baseBusiVo) {\r\n        SettleListHandlerVo handlerVo = new SettleListHandlerVo();\r\n        handlerVo.setIndex(index);\r\n        handlerVo.setErrorFields(errorFields);\r\n        handlerVo.setErrDscr(errDscr);\r\n        handlerVo.setChkStas(chkStas);\r\n        handlerVo.setErrType(errType);\r\n        baseBusiVo.getErrorDetails().add(handlerVo);\r\n    }\r\n}\r\n', 'script', 'java', '2024-09-24 17:23:21', '麻醉方式', '手术信息', 1, 1);
INSERT INTO `som_chain_script` VALUES (8, 'somplatform', 'check_bld_cat_after_basic', '输血品种[c45]基础质控', 'import com.my.som.common.util.ValidateUtil;\r\nimport com.my.som.model.dataHandle.SomHiInvyBasInfo;\r\nimport com.my.som.model.medicalQuality.SomSetlInvyBldInfo;\r\nimport com.my.som.settlevalidate.constant.SettleValidateConst;\r\nimport com.my.som.settlevalidate.vo.SetlValidBaseBusiVo;\r\nimport com.my.som.util.SettleListValidateUtil;\r\nimport com.my.som.vo.dataHandle.SettleListHandlerVo;\r\nimport com.my.som.vo.dataHandle.SettleListValidateVo;\r\nimport com.yomahub.liteflow.script.ScriptExecuteWrap;\r\nimport com.yomahub.liteflow.script.body.JaninoCommonScriptBody;\r\nimport org.slf4j.Logger;\r\nimport org.slf4j.LoggerFactory;\r\n\r\nimport java.util.List;\r\nimport java.util.Map;\r\n\r\n/**\r\n * 判断 输血品种不为空\r\n */\r\npublic class check_bld_cat_after_basic implements JaninoCommonScriptBody {\r\n    private static final Logger logger = LoggerFactory.getLogger(check_bld_cat_after_basic.class);\r\n\r\n\r\n    private static final String MAIN_CHECK_FIELD = \"c45\";\r\n    private static final String MAIN_CHECK_NAME = \"输血品种\";\r\n\r\n    private static final String DICT_SXPZ_KEY = \"SXPZ\";\r\n    private static final String DICT_BLD_CAT_KEY = \"BLD_CAT_NAME\";\r\n    private static final String MAIN_CHECK_REGION = \"四川\";\r\n    /**\r\n     * 事后校验\r\n     * 输血品种(bld_cat)基础质控\r\n     * 判断 输血品种 是否为空\r\n     *\r\n     * @param wrap\r\n     * @return\r\n     */\r\n    @Override\r\n    public Void body(ScriptExecuteWrap wrap) {\r\n        SetlValidBaseBusiVo setlValidBaseBusiVo = (SetlValidBaseBusiVo) wrap.cmp.getFirstContextBean();\r\n        SettleListValidateVo settleListValidateVo = (SettleListValidateVo) wrap.cmp.getRequestData();\r\n        Map<String, String> keysBasicResultMap = setlValidBaseBusiVo.getKeysBasicResultMap();\r\n        SomHiInvyBasInfo somHiInvyBasInfo = settleListValidateVo.getSomHiInvyBasInfo();\r\n        SettleListHandlerVo settleListHandlerVo = new SettleListHandlerVo();\r\n\r\n        // 检查 somHiInvyBasInfo 是否为空\r\n        if (ValidateUtil.isEmpty(somHiInvyBasInfo)) {\r\n\r\n            addErrorDetail(SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY, SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY,\r\n                    (String) SettleValidateConst.CHECK_TYPE_MAP.get(SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY), null,\r\n                    SettleValidateConst.VALIDATE_STATE_FAIL, setlValidBaseBusiVo);\r\n            return null;\r\n        }\r\n        List<SomSetlInvyBldInfo> busTransfusionList = settleListValidateVo.getBusTransfusionList();\r\n        // 检查 busTransfusionList 是否为空\r\n        if (ValidateUtil.isEmpty(busTransfusionList)) {\r\n            return null;\r\n        }\r\n        for (int i = 0; i < busTransfusionList.size(); i++) {\r\n            SomSetlInvyBldInfo somSetlInvyBldInfo = (SomSetlInvyBldInfo)busTransfusionList.get(i);\r\n            String bldCat = somSetlInvyBldInfo.getBldCat();\r\n            if (!ValidateUtil.isEmpty(bldCat)) {\r\n                Map<String, Object> map = (Map<String, Object>) setlValidBaseBusiVo.getSettleListParams().get(SettleListValidateUtil.KEY_DICT);\r\n                // 获取输血品种\r\n                List<String> bldCatList = (List<String>) map.get(DICT_SXPZ_KEY);\r\n                // 获取输血品种含义\r\n                List<String> bldCatNameList = (List<String>) map.get(DICT_BLD_CAT_KEY);\r\n                if (bldCatList.contains(bldCat) || bldCatNameList.contains(bldCat)) {\r\n\r\n                    keysBasicResultMap.put(somSetlInvyBldInfo.getSettleListId() + MAIN_CHECK_FIELD, SettleValidateConst.PASS);\r\n                } else {\r\n                    addErrorDetail(SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_ERROR_TYPE_2,\r\n                            \"第\" + (i + 1) + \"个输血信息：id为[\" + somSetlInvyBldInfo.getId() + \"]的\" + MAIN_CHECK_NAME + \"[\" + bldCat + \"]不符合规范\", MAIN_CHECK_FIELD,\r\n                            SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n                    keysBasicResultMap.put(i + MAIN_CHECK_FIELD, SettleValidateConst.OUTLIER);\r\n                }\r\n            }\r\n        }\r\n        return null;\r\n    }\r\n\r\n    private void addErrorDetail(String index, String errType, String errDscr, String errorFields, String\r\n            chkStas, SetlValidBaseBusiVo baseBusiVo) {\r\n        SettleListHandlerVo handlerVo = new SettleListHandlerVo();\r\n        handlerVo.setIndex(index);\r\n        handlerVo.setErrDscr(errDscr);\r\n        handlerVo.setErrorFields(errorFields);\r\n        handlerVo.setChkStas(chkStas);\r\n        handlerVo.setErrType(errType);\r\n        baseBusiVo.getErrorDetails().add(handlerVo);\r\n    }\r\n}\r\n', 'script', 'java', '2024-09-24 17:23:21', '输血品种', '基础信息', 1, 1);
INSERT INTO `som_chain_script` VALUES (9, 'somplatform', 'check_coner_addr_basic', '联系人地址[a34]基础质控', 'import com.my.som.model.dataHandle.SomHiInvyBasInfo;\r\nimport com.my.som.settlevalidate.constant.SettleValidateConst;\r\nimport com.my.som.settlevalidate.util.SettleValidateUtil;\r\nimport com.my.som.settlevalidate.vo.SetlValidBaseBusiVo;\r\nimport com.my.som.vo.dataHandle.SettleListHandlerVo;\r\nimport com.my.som.vo.dataHandle.SettleListValidateVo;\r\nimport com.yomahub.liteflow.script.ScriptExecuteWrap;\r\nimport com.yomahub.liteflow.script.body.JaninoCommonScriptBody;\r\nimport org.slf4j.Logger;\r\nimport org.slf4j.LoggerFactory;\r\n\r\nimport java.util.Map;\r\n\r\npublic class check_coner_addr_basic implements JaninoCommonScriptBody {\r\n    private static final Logger logger = LoggerFactory.getLogger(check_coner_addr_basic.class);\r\n\r\n    private static final String MAIN_CHECK_FIELD = \"a34\";\r\n    private static final String MAIN_CHECK_NAME = \"联系人地址\";\r\n    private static final String MAIN_CHECK_REGION = \"四川\";\r\n    /**\r\n     * 联系人地址(coner_addr)基础质控\r\n     * 判断联系人地址是否为空\r\n     * @param wrap\r\n     * @return\r\n     */\r\n    @Override\r\n    public Void body(ScriptExecuteWrap wrap) {\r\n        SetlValidBaseBusiVo setlValidBaseBusiVo = (SetlValidBaseBusiVo) wrap.cmp.getFirstContextBean();\r\n        SettleListValidateVo settleListValidateVo = (SettleListValidateVo) wrap.cmp.getRequestData();\r\n        Map<String, String> keysBasicResultMap = setlValidBaseBusiVo.getKeysBasicResultMap();\r\n        SomHiInvyBasInfo somHiInvyBasInfo = settleListValidateVo.getSomHiInvyBasInfo();\r\n        SettleListHandlerVo settleListHandlerVo = new SettleListHandlerVo();\r\n\r\n        // 检查 somHiInvyBasInfo 是否为空\r\n        if (SettleValidateUtil.isEmpty(somHiInvyBasInfo)) {\r\n            addErrorDetail(settleListHandlerVo, SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY,SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY,\r\n                    MAIN_CHECK_FIELD,\r\n                    (String)SettleValidateConst.CHECK_TYPE_MAP.get(SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY),\r\n                    SettleValidateConst.VALIDATE_STATE_FAIL, setlValidBaseBusiVo);\r\n            return null;\r\n        }\r\n\r\n        // 获取联系人地址\r\n        String a34 = somHiInvyBasInfo.getA34();\r\n        if (SettleValidateUtil.isEmpty(a34)) {\r\n            addErrorDetail(settleListHandlerVo, SettleValidateConst.VALIDATE_TYPE_1,SettleValidateConst.VALIDATE_TYPE_1,\r\n                    MAIN_CHECK_FIELD,\r\n                    MAIN_CHECK_NAME + \"[\" + a34 + \"]为空\",\r\n                    SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n            keysBasicResultMap.put(MAIN_CHECK_FIELD, SettleValidateConst.NULL);\r\n        } else {\r\n            keysBasicResultMap.put(MAIN_CHECK_FIELD, SettleValidateConst.PASS);\r\n        }\r\n        return null;\r\n    }\r\n\r\n    private void addErrorDetail(SettleListHandlerVo handlerVo, String index,String errType,String errorFields, String errDscr, String chkStas, SetlValidBaseBusiVo baseBusiVo) {\r\n        handlerVo.setIndex(index);\r\n        handlerVo.setErrDscr(errDscr);\r\n        handlerVo.setErrorFields(errorFields);\r\n        handlerVo.setChkStas(chkStas);\r\n        handlerVo.setErrType(errType);\r\n        baseBusiVo.getErrorDetails().add(handlerVo);\r\n    }\r\n}\r\n', 'script', 'java', '2024-09-24 17:23:21', '联系人地址', '基本数据', 0, 1);
INSERT INTO `som_chain_script` VALUES (10, 'somplatform', 'check_coner_tel_basic', '联系人电话[a35]基础质控', '\r\nimport com.my.som.model.dataHandle.SomHiInvyBasInfo;\r\nimport com.my.som.settlevalidate.constant.SettleValidateConst;\r\nimport com.my.som.settlevalidate.util.SettleValidateUtil;\r\nimport com.my.som.settlevalidate.vo.SetlValidBaseBusiVo;\r\nimport com.my.som.vo.dataHandle.SettleListHandlerVo;\r\nimport com.my.som.vo.dataHandle.SettleListValidateVo;\r\nimport com.yomahub.liteflow.script.ScriptExecuteWrap;\r\nimport com.yomahub.liteflow.script.body.JaninoCommonScriptBody;\r\nimport org.slf4j.Logger;\r\nimport org.slf4j.LoggerFactory;\r\n\r\nimport java.util.Map;\r\n\r\npublic class check_coner_tel_basic implements JaninoCommonScriptBody {\r\n    private static final Logger logger = LoggerFactory.getLogger(check_coner_tel_basic.class);\r\n\r\n    private static final String MAIN_CHECK_FIELD = \"a35\";\r\n    private static final String MAIN_CHECK_NAME = \"联系人电话\";\r\n    private static final String MAIN_CHECK_REGION = \"四川\";\r\n    /**\r\n     * 联系人电话(coner_tel)基础质控\r\n     * 判断联系人电话是否为空\r\n     * @param wrap\r\n     * @return\r\n     */\r\n    @Override\r\n    public Void body(ScriptExecuteWrap wrap) {\r\n        SetlValidBaseBusiVo setlValidBaseBusiVo = (SetlValidBaseBusiVo) wrap.cmp.getFirstContextBean();\r\n        SettleListValidateVo settleListValidateVo = (SettleListValidateVo) wrap.cmp.getRequestData();\r\n        Map<String, String> keysBasicResultMap = setlValidBaseBusiVo.getKeysBasicResultMap();\r\n        SomHiInvyBasInfo somHiInvyBasInfo = settleListValidateVo.getSomHiInvyBasInfo();\r\n        SettleListHandlerVo settleListHandlerVo = new SettleListHandlerVo();\r\n\r\n        // 检查 somHiInvyBasInfo 是否为空\r\n        if (SettleValidateUtil.isEmpty(somHiInvyBasInfo)) {\r\n            addErrorDetail(settleListHandlerVo, SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY,SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY,\r\n                    MAIN_CHECK_FIELD,\r\n                    (String)SettleValidateConst.CHECK_TYPE_MAP.get(SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY),\r\n                    SettleValidateConst.VALIDATE_STATE_FAIL, setlValidBaseBusiVo);\r\n            return null;\r\n        }\r\n\r\n        // 获取联系人地址\r\n        String a35 = somHiInvyBasInfo.getA35();\r\n        if (SettleValidateUtil.isEmpty(a35)) {\r\n            addErrorDetail(settleListHandlerVo, SettleValidateConst.VALIDATE_TYPE_1,SettleValidateConst.VALIDATE_TYPE_1,\r\n                    MAIN_CHECK_FIELD,\r\n                    MAIN_CHECK_NAME + \"[\" + a35 + \"]为空\",\r\n                    SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n            keysBasicResultMap.put(MAIN_CHECK_FIELD, SettleValidateConst.NULL);\r\n        } else {\r\n            keysBasicResultMap.put(MAIN_CHECK_FIELD, SettleValidateConst.PASS);\r\n        }\r\n\r\n        return null;\r\n    }\r\n\r\n    private void addErrorDetail(SettleListHandlerVo handlerVo, String index,String errType,String errorFields, String errDscr, String chkStas, SetlValidBaseBusiVo baseBusiVo) {\r\n        handlerVo.setIndex(index);\r\n        handlerVo.setErrDscr(errDscr);\r\n        handlerVo.setErrorFields(errorFields);\r\n        handlerVo.setChkStas(chkStas);\r\n        handlerVo.setErrType(errType);\r\n        baseBusiVo.getErrorDetails().add(handlerVo);\r\n    }\r\n}\r\n', 'script', 'java', '2024-09-24 17:23:21', '联系人电话', '基本数据', 0, 1);
INSERT INTO `som_chain_script` VALUES (11, 'somplatform', 'check_curr_addr_after_basic', '现住址[a26]基础质控', '\r\nimport com.my.som.model.dataHandle.SomHiInvyBasInfo;\r\nimport com.my.som.settlevalidate.constant.SettleValidateConst;\r\nimport com.my.som.settlevalidate.util.SettleValidateUtil;\r\nimport com.my.som.settlevalidate.vo.SetlValidBaseBusiVo;\r\nimport com.my.som.vo.dataHandle.SettleListHandlerVo;\r\nimport com.my.som.vo.dataHandle.SettleListValidateVo;\r\nimport com.yomahub.liteflow.script.ScriptExecuteWrap;\r\nimport com.yomahub.liteflow.script.body.JaninoCommonScriptBody;\r\nimport org.slf4j.Logger;\r\nimport org.slf4j.LoggerFactory;\r\nimport java.util.Map;\r\n\r\n/**\r\n * 判断 现住址不为空\r\n */\r\npublic class check_curr_addr_after_basic implements JaninoCommonScriptBody {\r\n    private static final Logger logger = LoggerFactory.getLogger(check_curr_addr_after_basic.class);\r\n\r\n    private static final String MAIN_CHECK_FIELD = \"a26\";\r\n    private static final String MAIN_CHECK_NAME = \"现住址\";\r\n    private static final String MAIN_CHECK_REGION = \"四川\";\r\n    /**\r\n     * 事后校验\r\n     * 现住址(curr_addr)基础质控\r\n     * 判断现住址是否为空\r\n     * @param wrap\r\n     * @return\r\n     */\r\n    @Override\r\n    public Void body(ScriptExecuteWrap wrap) {\r\n        SetlValidBaseBusiVo setlValidBaseBusiVo = (SetlValidBaseBusiVo) wrap.cmp.getFirstContextBean();\r\n        SettleListValidateVo settleListValidateVo = (SettleListValidateVo) wrap.cmp.getRequestData();\r\n        Map<String, String> keysBasicResultMap = setlValidBaseBusiVo.getKeysBasicResultMap();\r\n        SomHiInvyBasInfo somHiInvyBasInfo = settleListValidateVo.getSomHiInvyBasInfo();\r\n        SettleListHandlerVo settleListHandlerVo = new SettleListHandlerVo();\r\n\r\n        // 检查 somHiInvyBasInfo 是否为空\r\n        if (SettleValidateUtil.isEmpty(somHiInvyBasInfo)) {\r\n            addErrorDetail(settleListHandlerVo, SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY,SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY,\r\n                    (String) SettleValidateConst.CHECK_TYPE_MAP.get(SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY),null,\r\n                    SettleValidateConst.VALIDATE_STATE_FAIL, setlValidBaseBusiVo);\r\n            return null;\r\n        }\r\n\r\n        // 获取联系人现住址地址\r\n        String a26 = somHiInvyBasInfo.getA26();\r\n        if (SettleValidateUtil.isEmpty(a26)) {\r\n            addErrorDetail(settleListHandlerVo, SettleValidateConst.VALIDATE_TYPE_1,SettleValidateConst.VALIDATE_ERROR_TYPE_1,\r\n                    MAIN_CHECK_NAME + \"[\" + a26 + \"]为空\",MAIN_CHECK_FIELD,\r\n                    SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n            keysBasicResultMap.put(MAIN_CHECK_FIELD, SettleValidateConst.NULL);\r\n        } else {\r\n            keysBasicResultMap.put(MAIN_CHECK_FIELD, SettleValidateConst.PASS);\r\n        }\r\n        return null;\r\n    }\r\n\r\n    private void addErrorDetail(SettleListHandlerVo handlerVo, String index, String errType, String errDscr, String errorFields, String chkStas, SetlValidBaseBusiVo baseBusiVo) {\r\n        handlerVo.setIndex(index);\r\n        handlerVo.setErrDscr(errDscr);\r\n        handlerVo.setErrorFields(errorFields);\r\n        handlerVo.setChkStas(chkStas);\r\n        handlerVo.setErrType(errType);\r\n        baseBusiVo.getErrorDetails().add(handlerVo);\r\n    }\r\n}\r\n', 'script', 'java', '2024-09-24 17:23:21', '现住址', '基础信息 ', 1, 1);
INSERT INTO `som_chain_script` VALUES (12, 'somplatform', 'check_days_rinp_flag_31_after_basic', '是否有出院31日内再住院计划[b36c]基础质控', 'import com.my.som.model.dataHandle.SomHiInvyBasInfo;\r\nimport com.my.som.settlevalidate.constant.SettleValidateConst;\r\nimport com.my.som.settlevalidate.util.SettleValidateUtil;\r\nimport com.my.som.settlevalidate.vo.SetlValidBaseBusiVo;\r\nimport com.my.som.vo.dataHandle.SettleListHandlerVo;\r\nimport com.my.som.vo.dataHandle.SettleListValidateVo;\r\nimport com.yomahub.liteflow.script.ScriptExecuteWrap;\r\nimport com.yomahub.liteflow.script.body.JaninoCommonScriptBody;\r\nimport org.slf4j.Logger;\r\nimport org.slf4j.LoggerFactory;\r\n\r\nimport java.util.Map;\r\n\r\n/**\r\n * 判断 是否有31天内再住院计划不为空\r\n */\r\npublic class check_days_rinp_flag_31_after_basic implements JaninoCommonScriptBody {\r\n    private static final Logger logger = LoggerFactory.getLogger(check_days_rinp_flag_31_after_basic.class);\r\n    private static final String MAIN_CHECK_FIELD = \"b36c\";\r\n    private static final String MAIN_CHECK_NAME = \"是否有出院31日内再住院计划\";\r\n    private static final String MAIN_CHECK_FIELD_1 = \"b36c\";\r\n    private static final String MAIN_CHECK_NAME_1 = \"是否有出院31日内再住院计划\";\r\n    private static final String MAIN_CHECK_FIELD_2 = \"b37\";\r\n    private static final String MAIN_CHECK_NAME_2 = \"是否有出院31日内再住院目的\";\r\n    private static final String MAIN_CHECK_REGION = \"四川\";\r\n    /**\r\n     * 事后校验\r\n     * 结算开是否有出院31日内再住院计划始时间(days_rinp_flag_31)基础质控\r\n     * 判断 是否有出院31日内再住院计划 是否为空\r\n     *  不为空则判断 出院31日内再住院目的不为空\r\n     * @param wrap\r\n     * @return\r\n     */\r\n    @Override\r\n    public Void body(ScriptExecuteWrap wrap) {\r\n        SetlValidBaseBusiVo setlValidBaseBusiVo = (SetlValidBaseBusiVo) wrap.cmp.getFirstContextBean();\r\n        SettleListValidateVo settleListValidateVo = (SettleListValidateVo) wrap.cmp.getRequestData();\r\n        Map<String, String> keysBasicResultMap = setlValidBaseBusiVo.getKeysBasicResultMap();\r\n        SomHiInvyBasInfo somHiInvyBasInfo = settleListValidateVo.getSomHiInvyBasInfo();\r\n        SettleListHandlerVo settleListHandlerVo = new SettleListHandlerVo();\r\n\r\n        // 检查 somHiInvyBasInfo 是否为空\r\n        if (SettleValidateUtil.isEmpty(somHiInvyBasInfo)) {\r\n            addErrorDetail(settleListHandlerVo, SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY,SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY,\r\n                    (String) SettleValidateConst.CHECK_TYPE_MAP.get(SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY), null,\r\n                    SettleValidateConst.VALIDATE_STATE_FAIL, setlValidBaseBusiVo);\r\n            return null;\r\n        }\r\n\r\n        // 获取是否有31天内再住院计划\r\n         String b36c = somHiInvyBasInfo.getB36c();\r\n        //彭山如果出院方式为5 31天在住院\r\n//        String b34c = somHiInvyBasInfo.getB34c();\r\n//        if(\"5\".equals(b34c)) {\r\n//            return  null;\r\n//        }\r\n\r\n        if (SettleValidateUtil.isNotEmpty(b36c)) {\r\n            //判断 是否为；(1)无(代码1)、(2)有(代码2)以上两种中的任意一种。\r\n            if(b36c.equals(\"1\")||b36c.equals(\"2\")){\r\n                if(b36c.equals(\"2\")){\r\n                    //获取是否有31天内再住院目的\r\n                    String b37 = somHiInvyBasInfo.getB37();\r\n                    //判断31天内再住院目的是否为空\r\n                    if (SettleValidateUtil.isEmpty(b37)){\r\n                        addErrorDetail(settleListHandlerVo, SettleValidateConst.VALIDATE_TYPE_1,SettleValidateConst.VALIDATE_ERROR_TYPE_1,\r\n                                MAIN_CHECK_NAME_2 + \"[\" + b37 + \"]为空\",MAIN_CHECK_FIELD_2,\r\n                                SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n                        keysBasicResultMap.put(MAIN_CHECK_FIELD_2, SettleValidateConst.NULL);\r\n                    }else {\r\n                        keysBasicResultMap.put(MAIN_CHECK_FIELD_2, SettleValidateConst.PASS);\r\n                    }\r\n                }else {\r\n                    keysBasicResultMap.put(MAIN_CHECK_FIELD_2, SettleValidateConst.PASS);\r\n                }\r\n            }else {\r\n                addErrorDetail(settleListHandlerVo, SettleValidateConst.VALIDATE_TYPE_1,SettleValidateConst.VALIDATE_ERROR_TYPE_2,\r\n                        MAIN_CHECK_NAME_1 + \"[\" + b36c + \"]不符合规范\",MAIN_CHECK_FIELD_1,\r\n                        SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n                keysBasicResultMap.put(MAIN_CHECK_FIELD_1, SettleValidateConst.OUTLIER);\r\n            }\r\n        }else {\r\n            addErrorDetail(settleListHandlerVo, SettleValidateConst.VALIDATE_TYPE_1,SettleValidateConst.VALIDATE_ERROR_TYPE_1,\r\n                    MAIN_CHECK_NAME_1 + \"[\" + b36c + \"]为空\",MAIN_CHECK_FIELD_1,\r\n                    SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n            keysBasicResultMap.put(MAIN_CHECK_FIELD_1, SettleValidateConst.NULL);\r\n        }\r\n        return  null;\r\n    }\r\n\r\n    private void addErrorDetail(SettleListHandlerVo handlerVo, String index, String errType, String errDscr, String errorFields, String chkStas, SetlValidBaseBusiVo baseBusiVo) {\r\n        handlerVo.setIndex(index);\r\n        handlerVo.setErrDscr(errDscr);\r\n        handlerVo.setErrorFields(errorFields);\r\n        handlerVo.setChkStas(chkStas);\r\n        handlerVo.setErrType(errType);\r\n        baseBusiVo.getErrorDetails().add(handlerVo);\r\n    }\r\n}\r\n', 'script', 'java', '2024-09-24 17:23:21', '31天内再住院计划', '基础信息', 1, 1);
INSERT INTO `som_chain_script` VALUES (13, 'somplatform', 'check_diag_code_basic', '出院西医诊断编码[c06c1]基础质控', 'import com.my.som.dto.medicalQuality.BusDiseaseDiagnosisTrim;\r\nimport com.my.som.settlevalidate.constant.SettleValidateConst;\r\nimport com.my.som.settlevalidate.util.SettleValidateUtil;\r\nimport com.my.som.settlevalidate.vo.SetlValidBaseBusiVo;\r\nimport com.my.som.vo.dataHandle.SettleListHandlerVo;\r\nimport com.my.som.vo.dataHandle.SettleListValidateVo;\r\nimport com.yomahub.liteflow.script.ScriptExecuteWrap;\r\nimport com.yomahub.liteflow.script.body.JaninoCommonScriptBody;\r\nimport org.slf4j.Logger;\r\nimport org.slf4j.LoggerFactory;\r\n\r\n\r\nimport java.util.List;\r\nimport java.util.Map;\r\n\r\n/**\r\n * 诊断编码基础质检\r\n */\r\npublic class check_diag_code_basic implements JaninoCommonScriptBody {\r\n    private static final Logger logger = LoggerFactory.getLogger(check_diag_code_basic.class);\r\n    private static final String MAIN_CHECK_NAME = \"出院西医诊断编码\";\r\n    private static final String MAIN_CHECK_FIELD = \"c06c1\";\r\n    private static final String MAIN_CHECK_NAME1 = \"出院西医诊断名称\";\r\n    private static final String MAIN_CHECK_CODE = \"c06c1\";\r\n    private static final String MAIN_CHECK_CODE1 = \"c07n1\";\r\n    private static final String MAIN_CHECK_REGION = \"四川,湖北\";\r\n    private static final String WX_DIAG = \"1\";\r\n    /**\r\n     * 诊断编码基础质检\r\n     * @param wrap\r\n     * @return\r\n     */\r\n    @Override\r\n    public Void body(ScriptExecuteWrap wrap) {\r\n        SetlValidBaseBusiVo setlValidBaseBusiVo = (SetlValidBaseBusiVo) wrap.cmp.getFirstContextBean();\r\n        SettleListValidateVo settleListValidateVo = (SettleListValidateVo) wrap.cmp.getRequestData();\r\n        Map<String, String> keysBasicResultMap = setlValidBaseBusiVo.getKeysBasicResultMap();\r\n\r\n        List<BusDiseaseDiagnosisTrim> busDiseaseDiagnosisTrimsList = settleListValidateVo.getBusDiseaseDiagnosisTrimsList();\r\n\r\n        // 检查诊断信息是否为空\r\n        if (SettleValidateUtil.isEmpty(busDiseaseDiagnosisTrimsList)) {\r\n            return null;\r\n        }\r\n\r\n        for (int i = 0; i < busDiseaseDiagnosisTrimsList.size(); i++) {\r\n            BusDiseaseDiagnosisTrim busDiseaseDiagnosisTrim =(BusDiseaseDiagnosisTrim)busDiseaseDiagnosisTrimsList.get(i);\r\n            if(WX_DIAG.equals(busDiseaseDiagnosisTrim.getType())) {\r\n                String diagCode = busDiseaseDiagnosisTrim.getC06c1();\r\n                String diagName = busDiseaseDiagnosisTrim.getC07n1();\r\n                if (SettleValidateUtil.isEmpty(diagCode)) {\r\n                    addErrorDetail(SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_ERROR_TYPE_1,\r\n                            MAIN_CHECK_CODE,\r\n                            \"第\" + (i + 1) + \"个疾病诊断：\" + MAIN_CHECK_NAME + \"[\" + diagCode + \"]为空\",\r\n                            SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n                    keysBasicResultMap.put(i + MAIN_CHECK_CODE, SettleValidateConst.NULL);\r\n                } else {\r\n                    if (diagCode.length() < 5) {\r\n                        addErrorDetail(SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_ERROR_TYPE_6,\r\n                                MAIN_CHECK_CODE,\r\n                                \"第\" + (i + 1) + \"个疾病诊断：\" + MAIN_CHECK_NAME + \"[\" + diagCode + \"]格式错误\",\r\n                                SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n                        keysBasicResultMap.put(i + MAIN_CHECK_CODE, SettleValidateConst.OUTLIER);\r\n                    } else {\r\n                        keysBasicResultMap.put(i + MAIN_CHECK_CODE, SettleValidateConst.PASS);\r\n                    }\r\n                }\r\n                if (SettleValidateUtil.isEmpty(diagName)) {\r\n                    addErrorDetail(SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_ERROR_TYPE_1,\r\n                            MAIN_CHECK_CODE,\r\n                            \"第\" + (i + 1) + \"个疾病诊断：\" + MAIN_CHECK_NAME1 + \"[\" + diagName + \"]为空\",\r\n                            SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n                    keysBasicResultMap.put(i + MAIN_CHECK_CODE1, SettleValidateConst.NULL);\r\n                } else {\r\n                    keysBasicResultMap.put(i + MAIN_CHECK_CODE1, SettleValidateConst.PASS);\r\n                }\r\n            }\r\n        }\r\n        return null;\r\n    }\r\n\r\n    private void addErrorDetail(String index,String errType,String errorFields, String errDscr, String chkStas, SetlValidBaseBusiVo baseBusiVo) {\r\n        SettleListHandlerVo handlerVo = new SettleListHandlerVo();\r\n        handlerVo.setIndex(index);\r\n        handlerVo.setErrorFields(errorFields);\r\n        handlerVo.setErrDscr(errDscr);\r\n        handlerVo.setChkStas(chkStas);\r\n        handlerVo.setErrType(errType);\r\n        baseBusiVo.getErrorDetails().add(handlerVo);\r\n    }\r\n}', 'script', 'java', '2024-09-24 17:23:21', '诊断编码', '诊断信息 ', 1, 1);
INSERT INTO `som_chain_script` VALUES (14, 'somplatform', 'check_dscg_caty_code_after_basic', '出院科别编码[b16c]基础质控', '\r\nimport com.my.som.model.dataHandle.SomHiInvyBasInfo;\r\nimport com.my.som.settlevalidate.constant.SettleValidateConst;\r\nimport com.my.som.settlevalidate.util.SettleValidateUtil;\r\nimport com.my.som.settlevalidate.vo.SetlValidBaseBusiVo;\r\nimport com.my.som.util.SettleListValidateUtil;\r\nimport com.my.som.vo.dataHandle.SettleListHandlerVo;\r\nimport com.my.som.vo.dataHandle.SettleListValidateVo;\r\nimport com.yomahub.liteflow.script.ScriptExecuteWrap;\r\nimport com.yomahub.liteflow.script.body.JaninoCommonScriptBody;\r\nimport org.slf4j.Logger;\r\nimport org.slf4j.LoggerFactory;\r\nimport java.util.List;\r\nimport java.util.Map;\r\n\r\n/**\r\n * 事后判断 出院科别编码不为空\r\n */\r\npublic class check_dscg_caty_code_after_basic implements JaninoCommonScriptBody {\r\n    private static final Logger logger = LoggerFactory.getLogger(check_dscg_caty_code_after_basic.class);\r\n\r\n    private static final String MAIN_CHECK_FIELD = \"b16c\";\r\n    private static final String MAIN_CHECK_NAME = \"出院科别编码\";\r\n    private static final String MAIN_CHECK_REGION = \"四川\";\r\n\r\n    /**\r\n     * 事后 校验\r\n     * 出院科别编码(dscg_caty_code) 基础质控\r\n     * 判断出院科别编码是否为空\r\n     *\r\n     * @param wrap\r\n     * @return\r\n     */\r\n    @Override\r\n    public Void body(ScriptExecuteWrap wrap) {\r\n\r\n        SetlValidBaseBusiVo setlValidBaseBusiVo = (SetlValidBaseBusiVo) wrap.cmp.getFirstContextBean();\r\n        SettleListValidateVo settleListValidateVo = (SettleListValidateVo) wrap.cmp.getRequestData();\r\n        Map<String, String> keysBasicResultMap = setlValidBaseBusiVo.getKeysBasicResultMap();\r\n        SomHiInvyBasInfo somHiInvyBasInfo = settleListValidateVo.getSomHiInvyBasInfo();\r\n        SettleListHandlerVo settleListHandlerVo = new SettleListHandlerVo();\r\n\r\n        // 检查 somHiInvyBasInfo 是否为空\r\n        if (SettleValidateUtil.isEmpty(somHiInvyBasInfo)) {\r\n            addErrorDetail(settleListHandlerVo, SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY, SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY,\r\n                    (String) SettleValidateConst.CHECK_TYPE_MAP.get(SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY), null,\r\n                    SettleValidateConst.VALIDATE_STATE_FAIL, setlValidBaseBusiVo);\r\n            return null;\r\n        }\r\n\r\n        // 获取出院科别\r\n        String b16c = somHiInvyBasInfo.getB16c();\r\n        if (SettleValidateUtil.isEmpty(b16c)) {\r\n            addErrorDetail(settleListHandlerVo, SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_TYPE_1,\r\n                    MAIN_CHECK_NAME + \"[\" + b16c + \"]为空\", MAIN_CHECK_FIELD,\r\n                    SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n            keysBasicResultMap.put(MAIN_CHECK_FIELD, SettleValidateConst.NULL);\r\n        } else {\r\n            //获取出院科别\r\n           // 获取科室编码\r\n            List<String> dscgCatyCodeList = (List<String>)setlValidBaseBusiVo.getSettleListParams().get(SettleListValidateUtil.KEY_DEPT_CODE);\r\n            // 获取诊疗类别\r\n            List<String> trtTypeList = (List<String>)((Map<String,Object>)setlValidBaseBusiVo.getSettleListParams().get(SettleListValidateUtil.KEY_DICT)).get(\"ZLLB\");\r\n            if(! dscgCatyCodeList.contains(b16c) && !trtTypeList.contains(b16c)){\r\n                addErrorDetail(settleListHandlerVo, SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_TYPE_1,\r\n                        MAIN_CHECK_NAME + \"[\" + b16c + \"]不符合规范\", MAIN_CHECK_FIELD,\r\n                        SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n                keysBasicResultMap.put(MAIN_CHECK_FIELD, SettleValidateConst.OUTLIER);\r\n            }else{\r\n\r\n                    keysBasicResultMap.put(MAIN_CHECK_FIELD, SettleValidateConst.PASS);\r\n            }\r\n        }\r\n        return null;\r\n    }\r\n\r\n    private void addErrorDetail(SettleListHandlerVo handlerVo, String index, String errType, String errDscr, String errorFields, String chkStas, SetlValidBaseBusiVo baseBusiVo) {\r\n        handlerVo.setIndex(index);\r\n        handlerVo.setErrDscr(errDscr);\r\n        handlerVo.setErrorFields(errorFields);\r\n        handlerVo.setChkStas(chkStas);\r\n        handlerVo.setErrType(errType);\r\n        baseBusiVo.getErrorDetails().add(handlerVo);\r\n    }\r\n}\r\n\r\n', 'script', 'java', '2024-09-24 17:23:21', '出院科别编码', '基础信息', 1, 1);
INSERT INTO `som_chain_script` VALUES (15, 'somplatform', 'check_dscg_time_basic', '出院时间[b15]基础质控', '\r\nimport com.my.som.model.dataHandle.SomHiInvyBasInfo;\r\nimport com.my.som.settlevalidate.constant.SettleValidateConst;\r\nimport com.my.som.settlevalidate.util.SettleValidateUtil;\r\nimport com.my.som.settlevalidate.vo.SetlValidBaseBusiVo;\r\nimport com.my.som.vo.dataHandle.SettleListHandlerVo;\r\nimport com.my.som.vo.dataHandle.SettleListValidateVo;\r\nimport com.yomahub.liteflow.script.ScriptExecuteWrap;\r\nimport com.yomahub.liteflow.script.body.JaninoCommonScriptBody;\r\nimport org.slf4j.Logger;\r\nimport org.slf4j.LoggerFactory;\r\n\r\nimport java.time.LocalDateTime;\r\nimport java.time.format.DateTimeFormatter;\r\nimport java.time.format.DateTimeParseException;\r\nimport java.util.Map;\r\n\r\n/**\r\n * 出院时间\r\n */\r\npublic class check_dscg_time_basic implements JaninoCommonScriptBody {\r\n    private static final Logger logger = LoggerFactory.getLogger(check_dscg_time_basic.class);\r\n\r\n    private static final String MAIN_CHECK_FIELD = \"b15\";\r\n    private static final String MAIN_CHECK_NAME = \"出院时间\";\r\n    private static final String MAIN_CHECK_REGION = \"四川,湖北\";\r\n    /**\r\n     * 出院时间(dscg_time)基础质控\r\n     * 判断出院时间是否为空\r\n     * @param wrap\r\n     * @return\r\n     */\r\n    @Override\r\n    public Void body(ScriptExecuteWrap wrap) {\r\n        SetlValidBaseBusiVo setlValidBaseBusiVo = (SetlValidBaseBusiVo) wrap.cmp.getFirstContextBean();\r\n        SettleListValidateVo settleListValidateVo = (SettleListValidateVo) wrap.cmp.getRequestData();\r\n        Map<String, String> keysBasicResultMap = setlValidBaseBusiVo.getKeysBasicResultMap();\r\n        SomHiInvyBasInfo somHiInvyBasInfo = settleListValidateVo.getSomHiInvyBasInfo();\r\n        SettleListHandlerVo settleListHandlerVo = new SettleListHandlerVo();\r\n\r\n        // 检查 somHiInvyBasInfo 是否为空\r\n        if (SettleValidateUtil.isEmpty(somHiInvyBasInfo)) {\r\n            addErrorDetail(settleListHandlerVo, SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY,SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY,\r\n                    MAIN_CHECK_FIELD,\r\n                    (String)SettleValidateConst.CHECK_TYPE_MAP.get(SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY),\r\n                    SettleValidateConst.VALIDATE_STATE_FAIL, setlValidBaseBusiVo);\r\n            return null;\r\n        }\r\n\r\n        // 获取出院时间\r\n        String b15 = somHiInvyBasInfo.getB15();\r\n        if (SettleValidateUtil.isEmpty(b15)) {\r\n            addErrorDetail(settleListHandlerVo, SettleValidateConst.VALIDATE_TYPE_1,SettleValidateConst.VALIDATE_ERROR_TYPE_4,\r\n                    MAIN_CHECK_FIELD,\r\n                    MAIN_CHECK_NAME + \"[\" + b15 + \"]为空\",\r\n                    SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n            keysBasicResultMap.put(MAIN_CHECK_FIELD, SettleValidateConst.NULL);\r\n        } else {\r\n            if (!isValidDateTime(b15)) {\r\n                addErrorDetail(settleListHandlerVo, SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_ERROR_TYPE_2,\r\n                        MAIN_CHECK_FIELD,MAIN_CHECK_NAME + \"[\" + b15 + \"]时间格式错误\",\r\n                        SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n                keysBasicResultMap.put(MAIN_CHECK_FIELD, SettleValidateConst.OUTLIER);\r\n            } else {\r\n                keysBasicResultMap.put(MAIN_CHECK_FIELD, SettleValidateConst.PASS);\r\n            }\r\n        }\r\n        return null;\r\n    }\r\n\r\n    public static boolean isValidDateTime(String dateTimeStr) {\r\n        String format =  \"yyyy-MM-dd HH:mm:ss\";\r\n        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(format);\r\n        try {\r\n            LocalDateTime.parse(dateTimeStr, formatter);\r\n            return true;\r\n        } catch (DateTimeParseException e) {\r\n            return false;\r\n        }\r\n    }\r\n\r\n    private void addErrorDetail(SettleListHandlerVo handlerVo, String index,String errType,String errorFields, String errDscr, String chkStas, SetlValidBaseBusiVo baseBusiVo) {\r\n        handlerVo.setIndex(index);\r\n        handlerVo.setErrDscr(errDscr);\r\n        handlerVo.setErrorFields(errorFields);\r\n        handlerVo.setChkStas(chkStas);\r\n        handlerVo.setErrType(errType);\r\n        baseBusiVo.getErrorDetails().add(handlerVo);\r\n    }\r\n}\r\n', 'script', 'java', '2024-09-24 17:23:21', '出院时间', '住院诊断', 0, 1);
INSERT INTO `som_chain_script` VALUES (16, 'somplatform', 'check_emp_addr_after_basic', '工作单位地址[a29]基础质控', '\r\nimport com.my.som.model.dataHandle.SomHiInvyBasInfo;\r\nimport com.my.som.settlevalidate.constant.SettleValidateConst;\r\nimport com.my.som.settlevalidate.util.SettleValidateUtil;\r\nimport com.my.som.settlevalidate.vo.SetlValidBaseBusiVo;\r\nimport com.my.som.vo.dataHandle.SettleListHandlerVo;\r\nimport com.my.som.vo.dataHandle.SettleListValidateVo;\r\nimport com.yomahub.liteflow.script.ScriptExecuteWrap;\r\nimport com.yomahub.liteflow.script.body.JaninoCommonScriptBody;\r\nimport org.slf4j.Logger;\r\nimport org.slf4j.LoggerFactory;\r\n\r\nimport java.util.Map;\r\n\r\n/**\r\n * 事后判断 工作单位地址不为空\r\n */\r\npublic class check_emp_addr_after_basic implements JaninoCommonScriptBody {\r\n    private static final Logger logger = LoggerFactory.getLogger(check_emp_addr_after_basic.class);\r\n\r\n    private static final String MAIN_CHECK_FIELD = \"a29\";\r\n    private static final String MAIN_CHECK_NAME = \"工作单位地址\";\r\n    private static final String MAIN_CHECK_REGION = \"四川\";\r\n\r\n    /**\r\n     * 事后校验 基础质检\r\n     * 判断就诊前的工作地址是否为空(emp_addr)\r\n     *\r\n     * @param wrap\r\n     * @return\r\n     */\r\n    @Override\r\n    public Void body(ScriptExecuteWrap wrap) {\r\n        SetlValidBaseBusiVo setlValidBaseBusiVo = (SetlValidBaseBusiVo) wrap.cmp.getFirstContextBean();\r\n        SettleListValidateVo settleListValidateVo = (SettleListValidateVo) wrap.cmp.getRequestData();\r\n        Map<String, String> keysBasicResultMap = setlValidBaseBusiVo.getKeysBasicResultMap();\r\n        SomHiInvyBasInfo somHiInvyBasInfo = settleListValidateVo.getSomHiInvyBasInfo();\r\n        SettleListHandlerVo settleListHandlerVo = new SettleListHandlerVo();\r\n\r\n        // 检查 somHiInvyBasInfo 是否为空\r\n        if (SettleValidateUtil.isEmpty(somHiInvyBasInfo)) {\r\n            SettleValidateUtil.addErrorDetail(settleListHandlerVo, SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY, SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY,\r\n                    (String)SettleValidateConst.CHECK_TYPE_MAP.get(SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY),MAIN_CHECK_FIELD,\r\n                    SettleValidateConst.VALIDATE_STATE_FAIL, setlValidBaseBusiVo);\r\n            return null;\r\n        }\r\n\r\n        // 获取工作单位地址\r\n        String a29 = somHiInvyBasInfo.getA29();\r\n        //判断工作单位是否为空\r\n        if (SettleValidateUtil.isEmpty(a29)) {\r\n            keysBasicResultMap.put(MAIN_CHECK_FIELD, SettleValidateConst.NULL);\r\n        } else {\r\n            keysBasicResultMap.put(MAIN_CHECK_FIELD, SettleValidateConst.PASS);\r\n        }\r\n        return null;\r\n    }\r\n}\r\n', 'script', 'java', '2024-09-24 17:23:21', '工作地址', '基本信息', 1, 1);
INSERT INTO `som_chain_script` VALUES (17, 'somplatform', 'check_emp_name_after_basic', '工作单位名称[a29n]基础质控', '\r\nimport com.my.som.model.dataHandle.SomHiInvyBasInfo;\r\nimport com.my.som.settlevalidate.constant.SettleValidateConst;\r\nimport com.my.som.settlevalidate.util.SettleValidateUtil;\r\nimport com.my.som.settlevalidate.vo.SetlValidBaseBusiVo;\r\nimport com.my.som.vo.dataHandle.SettleListHandlerVo;\r\nimport com.my.som.vo.dataHandle.SettleListValidateVo;\r\nimport com.yomahub.liteflow.script.ScriptExecuteWrap;\r\nimport com.yomahub.liteflow.script.body.JaninoCommonScriptBody;\r\nimport org.slf4j.Logger;\r\nimport org.slf4j.LoggerFactory;\r\nimport java.util.Map;\r\n\r\n/**\r\n * 判断 工作单位名称\r\n */\r\npublic class check_emp_name_after_basic implements JaninoCommonScriptBody {\r\n    private static final Logger logger = LoggerFactory.getLogger(check_emp_name_after_basic.class);\r\n\r\n    private static final String MAIN_CHECK_FIELD = \"a29n\";\r\n    private static final String MAIN_CHECK_NAME  = \"工作单位名称\";\r\n    private static final String MAIN_CHECK_REGION = \"四川\";\r\n    /**\r\n     * 事后校验 基础质检\r\n     * 判断就诊前的工作单位名称是否为空(emp_name)\r\n     * @param wrap\r\n     * @return\r\n     */\r\n    @Override\r\n    public Void body(ScriptExecuteWrap wrap) {\r\n        SetlValidBaseBusiVo setlValidBaseBusiVo = (SetlValidBaseBusiVo) wrap.cmp.getFirstContextBean();\r\n        SettleListValidateVo settleListValidateVo = (SettleListValidateVo) wrap.cmp.getRequestData();\r\n        Map<String, String> keysBasicResultMap = setlValidBaseBusiVo.getKeysBasicResultMap();\r\n        SomHiInvyBasInfo somHiInvyBasInfo = settleListValidateVo.getSomHiInvyBasInfo();\r\n        SettleListHandlerVo settleListHandlerVo = new SettleListHandlerVo();\r\n\r\n        // 检查 somHiInvyBasInfo 是否为空\r\n        if (SettleValidateUtil.isEmpty(somHiInvyBasInfo)) {\r\n            return null;\r\n        }\r\n\r\n        //判断工作单位地址是否为空\r\n        String a29n = somHiInvyBasInfo.getA29n();\r\n        if(SettleValidateUtil.isEmpty(a29n)){\r\n            keysBasicResultMap.put(MAIN_CHECK_FIELD, SettleValidateConst.NULL);\r\n        }else{\r\n            keysBasicResultMap.put(MAIN_CHECK_FIELD, SettleValidateConst.PASS);\r\n        }\r\n        return null;\r\n    }\r\n}', 'script', 'java', '2024-09-24 17:23:21', '单位名称', '基本信息', 1, 1);
INSERT INTO `som_chain_script` VALUES (18, 'somplatform', 'check_emp_tel_after_basic', '工作单位电话[a30]基础质控', '\r\nimport com.my.som.model.dataHandle.SomHiInvyBasInfo;\r\nimport com.my.som.settlevalidate.constant.SettleValidateConst;\r\nimport com.my.som.settlevalidate.util.SettleValidateUtil;\r\nimport com.my.som.settlevalidate.vo.SetlValidBaseBusiVo;\r\nimport com.my.som.vo.dataHandle.SettleListHandlerVo;\r\nimport com.my.som.vo.dataHandle.SettleListValidateVo;\r\nimport com.yomahub.liteflow.script.ScriptExecuteWrap;\r\nimport com.yomahub.liteflow.script.body.JaninoCommonScriptBody;\r\nimport org.slf4j.Logger;\r\nimport org.slf4j.LoggerFactory;\r\n\r\nimport java.util.Map;\r\n\r\n/**\r\n * 事后判断 单位电话不为空\r\n */\r\npublic class check_emp_tel_after_basic implements JaninoCommonScriptBody {\r\n    private static final Logger logger = LoggerFactory.getLogger(check_emp_tel_after_basic.class);\r\n\r\n\r\n    private static final String MAIN_CHECK_FIELD = \"a30\";\r\n    private static final String MAIN_CHECK_NAME = \"工作单位电话\";\r\n    private static final String MAIN_CHECK_REGION = \"四川\";\r\n\r\n    /**\r\n     * 事后校验\r\n     * 医保类型 逻辑质检\r\n     * 判断是否为职工医疗保险\r\n     * 如果是：\r\n     * 判断就诊前的工作单位是否为空(emp_name)\r\n     * 判断就诊前的工作地址是否为空(emp_addr)\r\n     * @param wrap\r\n     * @return\r\n     */\r\n    @Override\r\n    public Void body(ScriptExecuteWrap wrap) {\r\n        SetlValidBaseBusiVo setlValidBaseBusiVo = (SetlValidBaseBusiVo) wrap.cmp.getFirstContextBean();\r\n        SettleListValidateVo settleListValidateVo = (SettleListValidateVo) wrap.cmp.getRequestData();\r\n        Map<String, String> keysBasicResultMap = setlValidBaseBusiVo.getKeysBasicResultMap();\r\n        SomHiInvyBasInfo somHiInvyBasInfo = settleListValidateVo.getSomHiInvyBasInfo();\r\n        SettleListHandlerVo settleListHandlerVo = new SettleListHandlerVo();\r\n\r\n        // 检查 somHiInvyBasInfo 是否为空\r\n        if (SettleValidateUtil.isEmpty(somHiInvyBasInfo)) {\r\n            SettleValidateUtil.addErrorDetail(settleListHandlerVo, SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY,SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY,\r\n                    (String) SettleValidateConst.CHECK_TYPE_MAP.get(SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY),MAIN_CHECK_FIELD,\r\n                    SettleValidateConst.VALIDATE_STATE_FAIL, setlValidBaseBusiVo);\r\n            return null;\r\n        }\r\n        //获取医保类型质检结果\r\n        String a30 = somHiInvyBasInfo.getA30();\r\n\r\n        if(SettleValidateUtil.isEmpty(a30)){\r\n            keysBasicResultMap.put(MAIN_CHECK_FIELD, SettleValidateConst.NULL);\r\n        }  else {\r\n            keysBasicResultMap.put(MAIN_CHECK_FIELD, SettleValidateConst.PASS);\r\n        }\r\n        return null;\r\n    }\r\n}\r\n', 'script', 'java', '2024-09-24 17:23:21', '单位电话', '基本信息', 1, 1);
INSERT INTO `som_chain_script` VALUES (19, 'somplatform', 'check_hi_no_basic', '医保编号[a51]基础质控', '\r\nimport com.my.som.model.dataHandle.SomHiInvyBasInfo;\r\nimport com.my.som.settlevalidate.constant.SettleValidateConst;\r\nimport com.my.som.settlevalidate.util.SettleValidateUtil;\r\nimport com.my.som.settlevalidate.vo.SetlValidBaseBusiVo;\r\nimport com.my.som.vo.dataHandle.SettleListHandlerVo;\r\nimport com.my.som.vo.dataHandle.SettleListValidateVo;\r\nimport com.yomahub.liteflow.script.ScriptExecuteWrap;\r\nimport com.yomahub.liteflow.script.body.JaninoCommonScriptBody;\r\nimport org.slf4j.Logger;\r\nimport org.slf4j.LoggerFactory;\r\n\r\nimport java.util.Map;\r\n\r\npublic class check_hi_no_basic implements JaninoCommonScriptBody {\r\n    private static final Logger logger = LoggerFactory.getLogger(check_hi_no_basic.class);\r\n\r\n    private static final String MAIN_CHECK_FIELD = \"a51\";\r\n    private static final String MAIN_CHECK_NAME = \"医保编号\";\r\n    private static final String MAIN_CHECK_REGION = \"四川\";\r\n    /**\r\n     * 医保编号(hi_no)基础质控\r\n     * 判断医保编号是否为空\r\n     * @param wrap\r\n     * @return\r\n     */\r\n    @Override\r\n    public Void body(ScriptExecuteWrap wrap) {\r\n        SetlValidBaseBusiVo setlValidBaseBusiVo = (SetlValidBaseBusiVo) wrap.cmp.getFirstContextBean();\r\n        SettleListValidateVo settleListValidateVo = (SettleListValidateVo) wrap.cmp.getRequestData();\r\n        Map<String, String> keysBasicResultMap = setlValidBaseBusiVo.getKeysBasicResultMap();\r\n        SomHiInvyBasInfo somHiInvyBasInfo = settleListValidateVo.getSomHiInvyBasInfo();\r\n        SettleListHandlerVo settleListHandlerVo = new SettleListHandlerVo();\r\n\r\n        // 检查 somHiInvyBasInfo 是否为空\r\n        if (SettleValidateUtil.isEmpty(somHiInvyBasInfo)) {\r\n            addErrorDetail(settleListHandlerVo, SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY,SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY,\r\n                    MAIN_CHECK_FIELD,\r\n                    (String)SettleValidateConst.CHECK_TYPE_MAP.get(SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY),\r\n                    SettleValidateConst.VALIDATE_STATE_FAIL, setlValidBaseBusiVo);\r\n            return null;\r\n        }\r\n\r\n        // 获取医保编号并检查是否为空\r\n        String a51 = somHiInvyBasInfo.getA51();\r\n        if (SettleValidateUtil.isEmpty(a51)) {\r\n            addErrorDetail(settleListHandlerVo, SettleValidateConst.VALIDATE_TYPE_1,SettleValidateConst.VALIDATE_ERROR_TYPE_1,\r\n                    MAIN_CHECK_FIELD,\r\n                    MAIN_CHECK_NAME + \"[\" + a51 + \"]为空\",\r\n                    SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n            keysBasicResultMap.put(MAIN_CHECK_FIELD, SettleValidateConst.NULL);\r\n        } else {\r\n            keysBasicResultMap.put(MAIN_CHECK_FIELD, SettleValidateConst.PASS);\r\n        }\r\n        return null;\r\n    }\r\n\r\n    private void addErrorDetail(SettleListHandlerVo handlerVo, String index,String errType,String errorFields, String errDscr, String chkStas, SetlValidBaseBusiVo baseBusiVo) {\r\n        handlerVo.setIndex(index);\r\n        handlerVo.setErrDscr(errDscr);\r\n        handlerVo.setErrorFields(errorFields);\r\n        handlerVo.setChkStas(chkStas);\r\n        handlerVo.setErrType(errType);\r\n        baseBusiVo.getErrorDetails().add(handlerVo);\r\n    }\r\n}\r\n', 'script', 'java', '2024-09-24 17:23:21', '医保编号', '基本数据', 0, 1);
INSERT INTO `som_chain_script` VALUES (20, 'somplatform', 'check_hi_paymtd_after_basic', '医保支付方式[d58]基础质控', '\r\nimport com.my.som.model.dataHandle.SomHiInvyBasInfo;\r\nimport com.my.som.settlevalidate.constant.SettleValidateConst;\r\nimport com.my.som.settlevalidate.util.SettleValidateUtil;\r\nimport com.my.som.settlevalidate.vo.SetlValidBaseBusiVo;\r\nimport com.my.som.util.SettleListValidateUtil;\r\nimport com.my.som.vo.dataHandle.SettleListHandlerVo;\r\nimport com.my.som.vo.dataHandle.SettleListValidateVo;\r\nimport com.yomahub.liteflow.script.ScriptExecuteWrap;\r\nimport com.yomahub.liteflow.script.body.JaninoCommonScriptBody;\r\nimport org.slf4j.Logger;\r\nimport org.slf4j.LoggerFactory;\r\n\r\nimport java.util.List;\r\nimport java.util.Map;\r\n\r\n/**\r\n * 判断 医保支付方式不为空\r\n */\r\npublic class check_hi_paymtd_after_basic implements JaninoCommonScriptBody {\r\n    private static final Logger logger = LoggerFactory.getLogger(check_hi_paymtd_after_basic.class);\r\n\r\n    private static final String MAIN_CHECK_FIELD = \"d58\";\r\n    private static final String MAIN_CHECK_NAME = \"医保支付方式\";\r\n    private static final String YBZFFS = \"YBZFFS\";\r\n    private static final String MAIN_CHECK_REGION = \"四川\";\r\n    /**\r\n     * 事后校验\r\n     * 医保支付方式(hi_paymtd)基础质控\r\n     * 判断医保支付方式是否为空\r\n     *\r\n     * @param wrap\r\n     * @return\r\n     */\r\n    @Override\r\n    public Void body(ScriptExecuteWrap wrap) {\r\n        SetlValidBaseBusiVo setlValidBaseBusiVo = (SetlValidBaseBusiVo) wrap.cmp.getFirstContextBean();\r\n        SettleListValidateVo settleListValidateVo = (SettleListValidateVo) wrap.cmp.getRequestData();\r\n        Map<String, String> keysBasicResultMap = setlValidBaseBusiVo.getKeysBasicResultMap();\r\n        SomHiInvyBasInfo somHiInvyBasInfo = settleListValidateVo.getSomHiInvyBasInfo();\r\n        SettleListHandlerVo settleListHandlerVo = new SettleListHandlerVo();\r\n\r\n        // 检查 somHiInvyBasInfo 是否为空\r\n        if (SettleValidateUtil.isEmpty(somHiInvyBasInfo)) {\r\n            addErrorDetail(settleListHandlerVo, SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY, SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY,\r\n                    (String) SettleValidateConst.CHECK_TYPE_MAP.get(SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY), null,\r\n                    SettleValidateConst.VALIDATE_STATE_FAIL, setlValidBaseBusiVo);\r\n            return null;\r\n        }\r\n\r\n        // 获取医保支付方式\r\n        String d58 = somHiInvyBasInfo.getD58();\r\n        if (SettleValidateUtil.isEmpty(d58)) {\r\n            addErrorDetail(settleListHandlerVo, SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_TYPE_1,\r\n                    MAIN_CHECK_NAME + \"[\" + d58 + \"]为空\",\r\n                     MAIN_CHECK_FIELD,\r\n                    SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n            keysBasicResultMap.put(MAIN_CHECK_FIELD, SettleValidateConst.NULL);\r\n        } else {\r\n            // 获取医保支付方式\r\n            Map<String, Object> map = (Map<String, Object>) setlValidBaseBusiVo.getSettleListParams().get(SettleListValidateUtil.KEY_DICT);\r\n            List<String> dscgCatyCodeList = (List<String>) map.get(YBZFFS);\r\n            if (dscgCatyCodeList.contains(d58)) {\r\n                keysBasicResultMap.put(MAIN_CHECK_FIELD, SettleValidateConst.PASS);\r\n            } else {\r\n                addErrorDetail(settleListHandlerVo, SettleValidateConst.VALIDATE_ERROR_TYPE_2, SettleValidateConst.VALIDATE_ERROR_TYPE_2,\r\n                        MAIN_CHECK_NAME + \"[\" + d58 + \"]填写不规范\",\r\n                        MAIN_CHECK_FIELD,\r\n                        SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n                keysBasicResultMap.put(MAIN_CHECK_FIELD, SettleValidateConst.OUTLIER);\r\n            }\r\n        }\r\n        return null;\r\n    }\r\n\r\n    private void addErrorDetail(SettleListHandlerVo handlerVo, String index, String errType, String errDscr, String errorFields, String chkStas, SetlValidBaseBusiVo baseBusiVo) {\r\n        handlerVo.setIndex(index);\r\n        handlerVo.setErrDscr(errDscr);\r\n        handlerVo.setErrorFields(errorFields);\r\n        handlerVo.setChkStas(chkStas);\r\n        handlerVo.setErrType(errType);\r\n        baseBusiVo.getErrorDetails().add(handlerVo);\r\n    }\r\n}\r\n', 'script', 'java', '2024-09-24 17:23:21', '医保支付方式', '基础信息', 1, 1);
INSERT INTO `som_chain_script` VALUES (21, 'somplatform', 'check_lyfs_basic', '离院方式[b34c]基础质控', '\r\nimport com.my.som.model.dataHandle.SomHiInvyBasInfo;\r\nimport com.my.som.settlevalidate.constant.SettleValidateConst;\r\nimport com.my.som.settlevalidate.util.SettleValidateUtil;\r\nimport com.my.som.settlevalidate.vo.SetlValidBaseBusiVo;\r\nimport com.my.som.vo.dataHandle.SettleListHandlerVo;\r\nimport com.my.som.vo.dataHandle.SettleListValidateVo;\r\nimport com.yomahub.liteflow.script.ScriptExecuteWrap;\r\nimport com.yomahub.liteflow.script.body.JaninoCommonScriptBody;\r\nimport org.slf4j.Logger;\r\nimport org.slf4j.LoggerFactory;\r\n\r\nimport java.util.Map;\r\n\r\npublic class check_lyfs_basic implements JaninoCommonScriptBody {\r\n    private static final Logger logger = LoggerFactory.getLogger(check_lyfs_basic.class);\r\n    private static final String MAIN_CHECK_FIELD = \"b34c\";\r\n    private static final String MAIN_CHECK_NAME = \"离院方式\";\r\n    private static final String MAIN_CHECK_REGION = \"四川,湖北\";\r\n    /**\r\n     * [离院方式基础质控]\r\n     *\r\n     * @param wrap\r\n     * @return\r\n     */\r\n    @Override\r\n    public Void body(ScriptExecuteWrap wrap) {\r\n        SetlValidBaseBusiVo setlValidBaseBusiVo = (SetlValidBaseBusiVo) wrap.cmp.getFirstContextBean();\r\n        SettleListValidateVo settleListValidateVo = (SettleListValidateVo) wrap.cmp.getRequestData();\r\n        Map<String, String> keysBasicResultMap = setlValidBaseBusiVo.getKeysBasicResultMap();\r\n        SomHiInvyBasInfo somHiInvyBasInfo = settleListValidateVo.getSomHiInvyBasInfo();\r\n        SettleListHandlerVo settleListHandlerVo = new SettleListHandlerVo();\r\n        if (SettleValidateUtil.isEmpty(somHiInvyBasInfo)) {\r\n            settleListHandlerVo.setIndex(SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY);\r\n            settleListHandlerVo.setErrDscr((String) SettleValidateConst.CHECK_TYPE_MAP.get(SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY));\r\n            settleListHandlerVo.setChkStas(SettleValidateConst.VALIDATE_STATE_FAIL);\r\n            settleListHandlerVo.setErrType(SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY);\r\n            setlValidBaseBusiVo.getErrorDetails().add(settleListHandlerVo);\r\n            return null;\r\n        }\r\n        String b34c = somHiInvyBasInfo.getB34c();\r\n        boolean checkBool = SettleValidateUtil.checkNotNullOrNotBlank(b34c,keysBasicResultMap,MAIN_CHECK_FIELD);\r\n        if (checkBool) {\r\n            // 判断码值范围\r\n            boolean checkListBool = SettleValidateUtil.checkCodeList(b34c,SettleValidateUtil.lyfsCodeList,\r\n                    keysBasicResultMap,settleListHandlerVo,MAIN_CHECK_FIELD,\r\n                    MAIN_CHECK_NAME,setlValidBaseBusiVo,somHiInvyBasInfo);\r\n            if (checkListBool){\r\n                // 正常值，基础校验通过不写错误信息\r\n                keysBasicResultMap.put(MAIN_CHECK_FIELD, SettleValidateConst.PASS);\r\n            }\r\n        }else{\r\n            SettleValidateUtil.addErrorDetail(settleListHandlerVo, SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_TYPE_1,\r\n                    MAIN_CHECK_NAME + \"[\" + b34c + \"]为空\", MAIN_CHECK_FIELD,\r\n                    SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n            keysBasicResultMap.put(MAIN_CHECK_FIELD, SettleValidateConst.NULL);\r\n        }\r\n        return null;\r\n    }\r\n}\r\n', 'script', 'java', '2024-09-24 17:23:21', '离院方式', '基础信息', 0, 1);
INSERT INTO `som_chain_script` VALUES (22, 'somplatform', 'check_mdeic_type_after_basic', '医保类型[a54]基础质控', '\r\nimport com.my.som.model.dataHandle.SomHiInvyBasInfo;\r\nimport com.my.som.settlevalidate.constant.SettleValidateConst;\r\nimport com.my.som.settlevalidate.util.SettleValidateUtil;\r\nimport com.my.som.settlevalidate.vo.SetlValidBaseBusiVo;\r\nimport com.my.som.vo.dataHandle.SettleListHandlerVo;\r\nimport com.my.som.vo.dataHandle.SettleListValidateVo;\r\nimport com.yomahub.liteflow.script.ScriptExecuteWrap;\r\nimport com.yomahub.liteflow.script.body.JaninoCommonScriptBody;\r\nimport org.slf4j.Logger;\r\nimport org.slf4j.LoggerFactory;\r\n\r\nimport java.util.Map;\r\n\r\n/**\r\n * 判断 医保类型不为空\r\n */\r\npublic class check_mdeic_type_after_basic implements JaninoCommonScriptBody {\r\n    private static final Logger logger = LoggerFactory.getLogger(check_mdeic_type_after_basic.class);\r\n\r\n    private static final String MAIN_CHECK_FIELD = \"a54\";\r\n    private static final String MAIN_CHECK_NAME = \"医保类型\";\r\n    private static final String MAIN_CHECK_REGION = \"四川\";\r\n\r\n    /**\r\n     * 事后 校验\r\n     * 工作单位电话(emp_tel) 基础质控\r\n     * 判断当为职工医保时 工作单位电话是否为空\r\n     *\r\n     * @param wrap\r\n     * @return\r\n     */\r\n    @Override\r\n    public Void body(ScriptExecuteWrap wrap) {\r\n        SetlValidBaseBusiVo setlValidBaseBusiVo = (SetlValidBaseBusiVo) wrap.cmp.getFirstContextBean();\r\n        SettleListValidateVo settleListValidateVo = (SettleListValidateVo) wrap.cmp.getRequestData();\r\n        Map<String, String> keysBasicResultMap = setlValidBaseBusiVo.getKeysBasicResultMap();\r\n        SomHiInvyBasInfo somHiInvyBasInfo = settleListValidateVo.getSomHiInvyBasInfo();\r\n        SettleListHandlerVo settleListHandlerVo = new SettleListHandlerVo();\r\n\r\n        // 检查 somHiInvyBasInfo 是否为空\r\n        if (SettleValidateUtil.isEmpty(somHiInvyBasInfo)) {\r\n            addErrorDetail(settleListHandlerVo, SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY, SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY,\r\n                    (String) SettleValidateConst.CHECK_TYPE_MAP.get(SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY), null,\r\n                    SettleValidateConst.VALIDATE_STATE_FAIL, setlValidBaseBusiVo);\r\n            return null;\r\n        }\r\n\r\n        // 获取医保类型\r\n        String a54 = somHiInvyBasInfo.getA54();\r\n        if (SettleValidateUtil.isEmpty(a54)) {\r\n            addErrorDetail(settleListHandlerVo, SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_TYPE_1,\r\n                    MAIN_CHECK_NAME + \"[\" + a54 + \"]为空\",\r\n                    MAIN_CHECK_FIELD,\r\n                    SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n            keysBasicResultMap.put(MAIN_CHECK_FIELD, SettleValidateConst.NULL);\r\n        } else {\r\n            keysBasicResultMap.put(MAIN_CHECK_FIELD, SettleValidateConst.PASS);\r\n        }\r\n        return null;\r\n    }\r\n\r\n    private void addErrorDetail(SettleListHandlerVo handlerVo, String index, String errType, String errDscr, String errorFields, String chkStas, SetlValidBaseBusiVo baseBusiVo) {\r\n        handlerVo.setIndex(index);\r\n        handlerVo.setErrDscr(errDscr);\r\n        handlerVo.setErrorFields(errorFields);\r\n        handlerVo.setChkStas(chkStas);\r\n        handlerVo.setErrType(errType);\r\n        baseBusiVo.getErrorDetails().add(handlerVo);\r\n    }\r\n}\r\n\r\n', 'script', 'java', '2024-09-24 17:23:21', '医保类型', '基本信息', 1, 1);
INSERT INTO `som_chain_script` VALUES (23, 'somplatform', 'check_medcasno_basic', '病案号[a48]基础质控', '\r\nimport com.my.som.model.dataHandle.SomHiInvyBasInfo;\r\nimport com.my.som.settlevalidate.constant.SettleValidateConst;\r\nimport com.my.som.settlevalidate.util.SettleValidateUtil;\r\nimport com.my.som.settlevalidate.vo.SetlValidBaseBusiVo;\r\nimport com.my.som.vo.dataHandle.SettleListHandlerVo;\r\nimport com.my.som.vo.dataHandle.SettleListValidateVo;\r\nimport com.yomahub.liteflow.script.ScriptExecuteWrap;\r\nimport com.yomahub.liteflow.script.body.JaninoCommonScriptBody;\r\nimport org.slf4j.Logger;\r\nimport org.slf4j.LoggerFactory;\r\n\r\nimport java.util.Map;\r\n\r\npublic class check_medcasno_basic implements JaninoCommonScriptBody {\r\n    private static final Logger logger = LoggerFactory.getLogger(check_medcasno_basic.class);\r\n\r\n    private static final String MAIN_CHECK_FIELD = \"a48\";\r\n    private static final String MAIN_CHECK_NAME = \"病案号\";\r\n    private static final String MAIN_CHECK_REGION = \"四川\";\r\n    /**\r\n     * 病案号(medcasno)基础质控\r\n     * 判断病案号是否为空\r\n     * @param wrap\r\n     * @return\r\n     */\r\n    @Override\r\n    public Void body(ScriptExecuteWrap wrap) {\r\n        SetlValidBaseBusiVo setlValidBaseBusiVo = (SetlValidBaseBusiVo) wrap.cmp.getFirstContextBean();\r\n        SettleListValidateVo settleListValidateVo = (SettleListValidateVo) wrap.cmp.getRequestData();\r\n        Map<String, String> keysBasicResultMap = setlValidBaseBusiVo.getKeysBasicResultMap();\r\n        SomHiInvyBasInfo somHiInvyBasInfo = settleListValidateVo.getSomHiInvyBasInfo();\r\n        SettleListHandlerVo settleListHandlerVo = new SettleListHandlerVo();\r\n\r\n        // 检查 somHiInvyBasInfo 是否为空\r\n        if (SettleValidateUtil.isEmpty(somHiInvyBasInfo)) {\r\n            addErrorDetail(settleListHandlerVo, SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY,SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY,\r\n                    MAIN_CHECK_FIELD,\r\n                    (String)SettleValidateConst.CHECK_TYPE_MAP.get(SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY),\r\n                    SettleValidateConst.VALIDATE_STATE_FAIL, setlValidBaseBusiVo);\r\n            return null;\r\n        }\r\n\r\n        // 获取病案号并检查是否为空\r\n        String a48 = somHiInvyBasInfo.getA48();\r\n        if (SettleValidateUtil.isEmpty(a48)) {\r\n            addErrorDetail(settleListHandlerVo, SettleValidateConst.VALIDATE_TYPE_1,SettleValidateConst.VALIDATE_ERROR_TYPE_1,\r\n                    MAIN_CHECK_FIELD,\r\n                    MAIN_CHECK_NAME + \"[\" + a48 + \"]为空\",\r\n                    SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n            keysBasicResultMap.put(MAIN_CHECK_FIELD, SettleValidateConst.NULL);\r\n        } else {\r\n            keysBasicResultMap.put(MAIN_CHECK_FIELD, SettleValidateConst.PASS);\r\n        }\r\n\r\n        return null;\r\n    }\r\n\r\n    private void addErrorDetail(SettleListHandlerVo handlerVo, String index,String errType,String errorFields, String errDscr, String chkStas, SetlValidBaseBusiVo baseBusiVo) {\r\n        handlerVo.setIndex(index);\r\n        handlerVo.setErrDscr(errDscr);\r\n        handlerVo.setErrorFields(errorFields);\r\n        handlerVo.setChkStas(chkStas);\r\n        handlerVo.setErrType(errType);\r\n        baseBusiVo.getErrorDetails().add(handlerVo);\r\n    }\r\n}\r\n', 'script', 'java', '2024-09-24 17:23:21', '病案号', '基本数据', 0, 1);
INSERT INTO `som_chain_script` VALUES (24, 'somplatform', 'check_medins_fill_dept_after_basic', '医疗机构填报部门[d59]基础质控', '\r\nimport com.my.som.model.dataHandle.SomHiInvyBasInfo;\r\nimport com.my.som.settlevalidate.constant.SettleValidateConst;\r\nimport com.my.som.settlevalidate.util.SettleValidateUtil;\r\nimport com.my.som.settlevalidate.vo.SetlValidBaseBusiVo;\r\nimport com.my.som.vo.dataHandle.SettleListHandlerVo;\r\nimport com.my.som.vo.dataHandle.SettleListValidateVo;\r\nimport com.yomahub.liteflow.script.ScriptExecuteWrap;\r\nimport com.yomahub.liteflow.script.body.JaninoCommonScriptBody;\r\nimport org.slf4j.Logger;\r\nimport org.slf4j.LoggerFactory;\r\n\r\nimport java.util.Map;\r\n\r\n/**\r\n     * 判断 医疗机构填报部门不为空\r\n */\r\npublic class check_medins_fill_dept_after_basic implements JaninoCommonScriptBody {\r\n    private static final Logger logger = LoggerFactory.getLogger(check_medins_fill_dept_after_basic.class);\r\n\r\n    private static final String MAIN_CHECK_FIELD = \"d59\";\r\n    private static final String MAIN_CHECK_NAME = \"医疗机构填报部门\";\r\n    private static final String MAIN_CHECK_REGION = \"四川\";\r\n    /**\r\n     * 事后校验\r\n     * 医疗机构填报部门(medins_fill_dept)基础质控\r\n     * 判断医疗机构填报部门是否为空\r\n     * @param wrap\r\n     * @return\r\n     */\r\n    @Override\r\n    public Void body(ScriptExecuteWrap wrap) {\r\n        SetlValidBaseBusiVo setlValidBaseBusiVo = (SetlValidBaseBusiVo) wrap.cmp.getFirstContextBean();\r\n        SettleListValidateVo settleListValidateVo = (SettleListValidateVo) wrap.cmp.getRequestData();\r\n        Map<String, String> keysBasicResultMap = setlValidBaseBusiVo.getKeysBasicResultMap();\r\n        SomHiInvyBasInfo somHiInvyBasInfo = settleListValidateVo.getSomHiInvyBasInfo();\r\n        SettleListHandlerVo settleListHandlerVo = new SettleListHandlerVo();\r\n\r\n        // 检查 somHiInvyBasInfo 是否为空\r\n        if (SettleValidateUtil.isEmpty(somHiInvyBasInfo)) {\r\n            addErrorDetail(settleListHandlerVo, SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY,SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY,\r\n                    (String) SettleValidateConst.CHECK_TYPE_MAP.get(SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY), null,\r\n                    SettleValidateConst.VALIDATE_STATE_FAIL, setlValidBaseBusiVo);\r\n            return null;\r\n        }\r\n\r\n        // 获取医疗机构填报部门\r\n        String d59 = somHiInvyBasInfo.getD59();\r\n        if (SettleValidateUtil.isEmpty(d59)) {\r\n            addErrorDetail(settleListHandlerVo, SettleValidateConst.VALIDATE_TYPE_1,SettleValidateConst.VALIDATE_TYPE_1,\r\n                    MAIN_CHECK_NAME + \"[\" + d59 + \"]为空\",\r\n                    MAIN_CHECK_FIELD,\r\n                    SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n            keysBasicResultMap.put(MAIN_CHECK_FIELD, SettleValidateConst.NULL);\r\n        } else {\r\n            keysBasicResultMap.put(MAIN_CHECK_FIELD, SettleValidateConst.PASS);\r\n        }\r\n        return null;\r\n    }\r\n\r\n    private void addErrorDetail(SettleListHandlerVo handlerVo, String index, String errType, String errDscr, String errorFields, String chkStas, SetlValidBaseBusiVo baseBusiVo) {\r\n        handlerVo.setIndex(index);\r\n        handlerVo.setErrDscr(errDscr);\r\n        handlerVo.setErrorFields(errorFields);\r\n        handlerVo.setChkStas(chkStas);\r\n        handlerVo.setErrType(errType);\r\n        baseBusiVo.getErrorDetails().add(handlerVo);\r\n    }\r\n}\r\n', 'script', 'java', '2024-09-24 17:23:21', '医疗机构填报部门', '基础信息', 1, 1);
INSERT INTO `som_chain_script` VALUES (25, 'somplatform', 'check_medins_fill_psn_after_basic', '医疗机构填报人[d60]基础质控', '\r\nimport com.my.som.model.dataHandle.SomHiInvyBasInfo;\r\nimport com.my.som.settlevalidate.constant.SettleValidateConst;\r\nimport com.my.som.settlevalidate.util.SettleValidateUtil;\r\nimport com.my.som.settlevalidate.vo.SetlValidBaseBusiVo;\r\nimport com.my.som.vo.dataHandle.SettleListHandlerVo;\r\nimport com.my.som.vo.dataHandle.SettleListValidateVo;\r\nimport com.yomahub.liteflow.script.ScriptExecuteWrap;\r\nimport com.yomahub.liteflow.script.body.JaninoCommonScriptBody;\r\nimport org.slf4j.Logger;\r\nimport org.slf4j.LoggerFactory;\r\n\r\nimport java.util.Map;\r\n\r\n/**\r\n * 判断 医疗机构填报人不为空\r\n */\r\npublic class check_medins_fill_psn_after_basic implements JaninoCommonScriptBody {\r\n    private static final Logger logger = LoggerFactory.getLogger(check_medins_fill_psn_after_basic.class);\r\n\r\n    private static final String MAIN_CHECK_FIELD = \"d60\";\r\n    private static final String MAIN_CHECK_NAME = \"医疗机构填报人\";\r\n    private static final String MAIN_CHECK_REGION = \"四川\";\r\n    /**\r\n     * 事后校验\r\n     * 医疗机构填报人(medins_fill_psn)基础质控\r\n     * 判断医疗机构填报人是否为空\r\n     * @param wrap\r\n     * @return\r\n     */\r\n    @Override\r\n    public Void body(ScriptExecuteWrap wrap) {\r\n        SetlValidBaseBusiVo setlValidBaseBusiVo = (SetlValidBaseBusiVo) wrap.cmp.getFirstContextBean();\r\n        SettleListValidateVo settleListValidateVo = (SettleListValidateVo) wrap.cmp.getRequestData();\r\n        Map<String, String> keysBasicResultMap = setlValidBaseBusiVo.getKeysBasicResultMap();\r\n        SomHiInvyBasInfo somHiInvyBasInfo = settleListValidateVo.getSomHiInvyBasInfo();\r\n        SettleListHandlerVo settleListHandlerVo = new SettleListHandlerVo();\r\n\r\n        // 检查 somHiInvyBasInfo 是否为空\r\n        if (SettleValidateUtil.isEmpty(somHiInvyBasInfo)) {\r\n            addErrorDetail(settleListHandlerVo, SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY,SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY,\r\n                    (String)SettleValidateConst.CHECK_TYPE_MAP.get(SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY), null,\r\n                    SettleValidateConst.VALIDATE_STATE_FAIL, setlValidBaseBusiVo);\r\n            return null;\r\n        }\r\n\r\n        // 获取医疗机构填报人\r\n        String d60 = somHiInvyBasInfo.getD60();\r\n        if (SettleValidateUtil.isEmpty(d60)) {\r\n            addErrorDetail(settleListHandlerVo, SettleValidateConst.VALIDATE_TYPE_1,SettleValidateConst.VALIDATE_TYPE_1,\r\n                    MAIN_CHECK_NAME + \"[\" + d60 + \"]为空\",MAIN_CHECK_FIELD,\r\n                    SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n            keysBasicResultMap.put(MAIN_CHECK_FIELD, SettleValidateConst.NULL);\r\n        } else {\r\n            keysBasicResultMap.put(MAIN_CHECK_FIELD, SettleValidateConst.PASS);\r\n        }\r\n        return null;\r\n    }\r\n\r\n    private void addErrorDetail(SettleListHandlerVo handlerVo, String index, String errType, String errDscr, String errorFields, String chkStas, SetlValidBaseBusiVo baseBusiVo) {\r\n        handlerVo.setIndex(index);\r\n        handlerVo.setErrDscr(errDscr);\r\n        handlerVo.setErrorFields(errorFields);\r\n        handlerVo.setChkStas(chkStas);\r\n        handlerVo.setErrType(errType);\r\n        baseBusiVo.getErrorDetails().add(handlerVo);\r\n    }\r\n}\r\n', 'script', 'java', '2024-09-24 17:23:21', '定点医疗机构填报人', '基础信息', 1, 1);
INSERT INTO `som_chain_script` VALUES (26, 'somplatform', 'check_ntly_basic', '国籍[a15c]基础质控', '\r\nimport com.my.som.model.dataHandle.SomHiInvyBasInfo;\r\nimport com.my.som.settlevalidate.constant.SettleValidateConst;\r\nimport com.my.som.settlevalidate.util.SettleValidateUtil;\r\nimport com.my.som.settlevalidate.vo.SetlValidBaseBusiVo;\r\nimport com.my.som.util.SettleListValidateUtil;\r\nimport com.my.som.vo.dataHandle.SettleListHandlerVo;\r\nimport com.my.som.vo.dataHandle.SettleListValidateVo;\r\nimport com.yomahub.liteflow.script.ScriptExecuteWrap;\r\nimport com.yomahub.liteflow.script.body.JaninoCommonScriptBody;\r\nimport org.slf4j.Logger;\r\nimport org.slf4j.LoggerFactory;\r\n\r\nimport java.util.ArrayList;\r\nimport java.util.List;\r\nimport java.util.Map;\r\n\r\npublic class check_ntly_basic implements JaninoCommonScriptBody {\r\n    private static final Logger logger = LoggerFactory.getLogger(check_ntly_basic.class);\r\n\r\n    private static final String MAIN_CHECK_FIELD = \"a15c\";\r\n    private static final String MAIN_CHECK_NAME = \"国籍\";\r\n    private static final String MAIN_CHECK_REGION = \"四川\";\r\n    /**\r\n     * 国籍(ntly)基础质控\r\n     * 1.判断国籍是否为空\r\n     * 2.判断是否符合GB/T2659-2000 标准\r\n     * @param wrap\r\n     * @return\r\n     */\r\n    @Override\r\n    public Void body(ScriptExecuteWrap wrap) {\r\n        SetlValidBaseBusiVo setlValidBaseBusiVo = (SetlValidBaseBusiVo) wrap.cmp.getFirstContextBean();\r\n        SettleListValidateVo settleListValidateVo = (SettleListValidateVo) wrap.cmp.getRequestData();\r\n        Map<String, String> keysBasicResultMap = setlValidBaseBusiVo.getKeysBasicResultMap();\r\n        SomHiInvyBasInfo somHiInvyBasInfo = settleListValidateVo.getSomHiInvyBasInfo();\r\n        SettleListHandlerVo settleListHandlerVo = new SettleListHandlerVo();\r\n\r\n        // 检查 somHiInvyBasInfo 是否为空\r\n        if (SettleValidateUtil.isEmpty(somHiInvyBasInfo)) {\r\n            addErrorDetail(settleListHandlerVo, SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY,SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY,\r\n                    MAIN_CHECK_FIELD,\r\n                    (String)SettleValidateConst.CHECK_TYPE_MAP.get(SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY),\r\n                    SettleValidateConst.VALIDATE_STATE_FAIL, setlValidBaseBusiVo);\r\n            return null;\r\n        }\r\n\r\n        // 获取国籍\r\n        String a15c = somHiInvyBasInfo.getA15c();\r\n        if (SettleValidateUtil.isEmpty(a15c)) {\r\n            addErrorDetail(settleListHandlerVo, SettleValidateConst.VALIDATE_TYPE_1,SettleValidateConst.VALIDATE_ERROR_TYPE_1,\r\n                    MAIN_CHECK_FIELD,\r\n                    MAIN_CHECK_NAME + \"[\" + a15c + \"]为空\",\r\n                    SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n            keysBasicResultMap.put(MAIN_CHECK_FIELD, SettleValidateConst.NULL);\r\n        } else {\r\n            // 判断是否符合国籍字典\r\n            Map<String,Object> map = (Map<String,Object>)setlValidBaseBusiVo.getSettleListParams().get(SettleListValidateUtil.KEY_DICT);\r\n            List<String> gj = (List<String>)map.get(\"GJ\");\r\n            if(!gj.contains(a15c)){\r\n                addErrorDetail(settleListHandlerVo, SettleValidateConst.VALIDATE_TYPE_1,SettleValidateConst.VALIDATE_ERROR_TYPE_2,\r\n                        MAIN_CHECK_FIELD,\r\n                        MAIN_CHECK_NAME + \"[\" + a15c + \"]未按照国籍标准字典映射\",\r\n                        SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n                keysBasicResultMap.put(MAIN_CHECK_FIELD, SettleValidateConst.OUTLIER);\r\n            }else{\r\n                keysBasicResultMap.put(MAIN_CHECK_FIELD, SettleValidateConst.PASS);\r\n            }\r\n\r\n        }\r\n\r\n        return null;\r\n    }\r\n\r\n    private void addErrorDetail(SettleListHandlerVo handlerVo, String index,String errType,String errorFields, String errDscr, String chkStas, SetlValidBaseBusiVo baseBusiVo) {\r\n        handlerVo.setIndex(index);\r\n        handlerVo.setErrDscr(errDscr);\r\n        handlerVo.setErrorFields(errorFields);\r\n        handlerVo.setChkStas(chkStas);\r\n        handlerVo.setErrType(errType);\r\n        baseBusiVo.getErrorDetails().add(handlerVo);\r\n    }\r\n}\r\n', 'script', 'java', '2024-09-24 17:23:21', '国籍', '基本数据', 0, 1);
INSERT INTO `som_chain_script` VALUES (27, 'somplatform', 'check_nwb_adm_type_after_basic', '新生儿入院类型[a57]基础质控', '\r\nimport com.my.som.model.dataHandle.SomHiInvyBasInfo;\r\nimport com.my.som.settlevalidate.constant.SettleValidateConst;\r\nimport com.my.som.settlevalidate.util.SettleValidateUtil;\r\nimport com.my.som.settlevalidate.vo.SetlValidBaseBusiVo;\r\nimport com.my.som.util.SettleListValidateUtil;\r\nimport com.my.som.vo.dataHandle.SettleListHandlerVo;\r\nimport com.my.som.vo.dataHandle.SettleListValidateVo;\r\nimport com.yomahub.liteflow.script.ScriptExecuteWrap;\r\nimport com.yomahub.liteflow.script.body.JaninoCommonScriptBody;\r\nimport org.slf4j.Logger;\r\nimport org.slf4j.LoggerFactory;\r\n\r\nimport java.util.Arrays;\r\nimport java.util.List;\r\nimport java.util.Map;\r\n\r\n/**\r\n * 判断 新生儿入院类型不为空\r\n */\r\npublic class check_nwb_adm_type_after_basic implements JaninoCommonScriptBody {\r\n    private static final Logger logger = LoggerFactory.getLogger(check_nwb_adm_type_after_basic.class);\r\n\r\n    private static final String MAIN_CHECK_FIELD = \"a57\";\r\n    private static final String MAIN_CHECK_NAME = \"新生儿入院类型\";\r\n    private static final String MAIN_CHECK_REGION = \"四川\";\r\n    /**\r\n     * 事后 校验\r\n     * 新生儿入院类型(nwb_admtype) 基础质控\r\n     * 判断新生儿入院类型是否为空\r\n     *\r\n     * @param wrap\r\n     * @return\r\n     */\r\n    @Override\r\n    public Void body(ScriptExecuteWrap wrap) {\r\n        SetlValidBaseBusiVo setlValidBaseBusiVo = (SetlValidBaseBusiVo) wrap.cmp.getFirstContextBean();\r\n        SettleListValidateVo settleListValidateVo = (SettleListValidateVo) wrap.cmp.getRequestData();\r\n        Map<String, String> keysBasicResultMap = setlValidBaseBusiVo.getKeysBasicResultMap();\r\n        SomHiInvyBasInfo somHiInvyBasInfo = settleListValidateVo.getSomHiInvyBasInfo();\r\n        SettleListHandlerVo settleListHandlerVo = new SettleListHandlerVo();\r\n\r\n        // 检查 somHiInvyBasInfo 是否为空\r\n        if (SettleValidateUtil.isEmpty(somHiInvyBasInfo)) {\r\n            addErrorDetail(settleListHandlerVo, SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY, SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY,\r\n                    (String) SettleValidateConst.CHECK_TYPE_MAP.get(SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY), null,\r\n                    SettleValidateConst.VALIDATE_STATE_FAIL, setlValidBaseBusiVo);\r\n            return null;\r\n        }\r\n\r\n        // 新生儿入院类型\r\n        String a57 = somHiInvyBasInfo.getA57();\r\n        if (SettleValidateUtil.isEmpty(a57)) {\r\n            keysBasicResultMap.put(MAIN_CHECK_FIELD, SettleValidateConst.NULL);\r\n        } else {\r\n            //新生儿入院类型 不为空，判断是否属于规范\r\n            //获取规范类型\r\n            Map<String,Object> map = (Map<String,Object>)setlValidBaseBusiVo.getSettleListParams().get(SettleListValidateUtil.KEY_DICT);\r\n            List<String> birthTypes = (List<String>)map.get(\"XSELX\");\r\n\r\n            List<String> a57List = Arrays.asList(a57.split(\",\"));\r\n\r\n                boolean flag = true;\r\n                //说明此时是多选情况\r\n                for (int i = 0; i < a57List.size(); i++) {\r\n                    if(!birthTypes.contains(a57List.get(i))){\r\n                        SettleValidateUtil.addErrorDetail(settleListHandlerVo, SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_TYPE_2,\r\n                                MAIN_CHECK_NAME + \"为[\" + a57 + \"]其中第\"+(i+1)+\"个新生儿入院类型不符合规范\", MAIN_CHECK_FIELD,\r\n                                SettleValidateConst.OUTLIER, setlValidBaseBusiVo);\r\n                        flag = false;\r\n                    }\r\n                }\r\n                if(flag){\r\n                    keysBasicResultMap.put(MAIN_CHECK_FIELD, SettleValidateConst.PASS);\r\n                }else{\r\n                    keysBasicResultMap.put(MAIN_CHECK_FIELD, SettleValidateConst.OUTLIER);\r\n                }\r\n\r\n        }\r\n        return null;\r\n    }\r\n\r\n    private void addErrorDetail(SettleListHandlerVo handlerVo, String index, String errType, String errDscr, String errorFields, String chkStas, SetlValidBaseBusiVo baseBusiVo) {\r\n        handlerVo.setIndex(index);\r\n        handlerVo.setErrDscr(errDscr);\r\n        handlerVo.setErrorFields(errorFields);\r\n        handlerVo.setChkStas(chkStas);\r\n        handlerVo.setErrType(errType);\r\n        baseBusiVo.getErrorDetails().add(handlerVo);\r\n    }\r\n}\r\n\r\n', 'script', 'java', '2024-09-24 17:23:21', '新生儿入院类型', '诊断信息 ', 1, 1);
INSERT INTO `som_chain_script` VALUES (28, 'somplatform', 'check_nwb_adm_wt_basic', '新生儿入院体重[a17]基础质控', 'import com.my.som.model.dataHandle.SomHiInvyBasInfo;\r\nimport com.my.som.settlevalidate.constant.SettleValidateConst;\r\nimport com.my.som.settlevalidate.util.SettleValidateUtil;\r\nimport com.my.som.settlevalidate.vo.SetlValidBaseBusiVo;\r\nimport com.my.som.vo.dataHandle.SettleListHandlerVo;\r\nimport com.my.som.vo.dataHandle.SettleListValidateVo;\r\nimport com.yomahub.liteflow.script.ScriptExecuteWrap;\r\nimport com.yomahub.liteflow.script.body.JaninoCommonScriptBody;\r\nimport org.slf4j.Logger;\r\nimport org.slf4j.LoggerFactory;\r\n\r\nimport java.util.Map;\r\n\r\npublic class check_nwb_adm_wt_basic implements JaninoCommonScriptBody {\r\n    private static final Logger logger = LoggerFactory.getLogger(check_nwb_adm_wt_basic.class);\r\n\r\n    private static final String MAIN_CHECK_FIELD = \"a17\";\r\n    private static final String MAIN_CHECK_NAME = \"新生儿入院体重\";\r\n    private static final String MAIN_CHECK_REGION = \"四川,湖北\";\r\n    /**\r\n     * 新生儿出生体重-[a17]-基础校验\r\n     * 1、当新生儿入院体重为空时，不用判断新生儿入院体重项下的规则；当新生儿入院体重不为空时，判断新生儿入院体重是否为阿拉伯数字，若否则不通过。\r\n     * 2、审核新生儿入院体重范围是否在[200,20000]内，若否则不通过。\r\n     *\r\n     * @param wrap\r\n     * @return\r\n     */\r\n    @Override\r\n    public Void body(ScriptExecuteWrap wrap) {\r\n        String main_check_field = \"a17\";\r\n        String main_check_name = \"新生儿入院体重\";\r\n        SetlValidBaseBusiVo setlValidBaseBusiVo = (SetlValidBaseBusiVo) wrap.cmp.getFirstContextBean();\r\n        SettleListValidateVo settleListValidateVo = (SettleListValidateVo) wrap.cmp.getRequestData();\r\n        Map<String, String> keysBasicResultMap = setlValidBaseBusiVo.getKeysBasicResultMap();\r\n        SomHiInvyBasInfo somHiInvyBasInfo = settleListValidateVo.getSomHiInvyBasInfo();\r\n        SettleListHandlerVo settleListHandlerVo = new SettleListHandlerVo();\r\n        if (SettleValidateUtil.isEmpty(somHiInvyBasInfo)) {\r\n            settleListHandlerVo.setIndex(SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY);\r\n            settleListHandlerVo.setErrDscr((String) SettleValidateConst.CHECK_TYPE_MAP.get(SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY));\r\n            settleListHandlerVo.setChkStas(SettleValidateConst.VALIDATE_STATE_FAIL);\r\n            settleListHandlerVo.setErrType(SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY);\r\n            setlValidBaseBusiVo.getErrorDetails().add(settleListHandlerVo);\r\n            return null;\r\n        }\r\n        Double a17 = somHiInvyBasInfo.getA17();\r\n        if (!SettleValidateUtil.isEmpty(a17)) {\r\n            //不为空,则一定是一个正常值，判断范围是否在[200,20000]\r\n            if (a17 == 0.0) {\r\n                //处理为null,被默认赋值为0.0数据\r\n                keysBasicResultMap.put(main_check_field, SettleValidateConst.NULL);\r\n                return null;\r\n            }\r\n            if (!(a17 >= 200 && a17 < 10000)) {\r\n                //不在合理值范围，提示\r\n                settleListHandlerVo.setIndex(SettleValidateConst.VALIDATE_TYPE_1);\r\n                settleListHandlerVo.setSettleListId(somHiInvyBasInfo.getId());\r\n                settleListHandlerVo.setErrorFields(main_check_field);\r\n                settleListHandlerVo.setErrDscr(main_check_name + \"[\" + a17 + \"]超出合理值范围[0,10000)单位为g\");\r\n                settleListHandlerVo.setChkStas(SettleValidateConst.VALIDATE_STATE_SUCCESS);\r\n                settleListHandlerVo.setErrType(SettleValidateConst.VALIDATE_ERROR_TYPE_2);\r\n                setlValidBaseBusiVo.getErrorDetails().add(settleListHandlerVo);\r\n                //放入字段基础校验\r\n                keysBasicResultMap.put(main_check_field, SettleValidateConst.OUTLIER);\r\n            } else {\r\n                //正常值，基础校验通过不写错误信息\r\n                keysBasicResultMap.put(main_check_field, SettleValidateConst.PASS);\r\n            }\r\n        } else {\r\n            keysBasicResultMap.put(main_check_field, SettleValidateConst.NULL);\r\n        }\r\n        return null;\r\n    }\r\n}', 'script', 'java', '2024-09-24 17:23:21', '新生儿入院体重', '基本数据', 0, 1);
INSERT INTO `som_chain_script` VALUES (29, 'somplatform', 'check_nwb_age_after_basic', '不满一周岁天龄[a16]基础质控', '\r\nimport com.my.som.model.dataHandle.SomHiInvyBasInfo;\r\nimport com.my.som.settlevalidate.constant.SettleValidateConst;\r\nimport com.my.som.settlevalidate.util.SettleValidateUtil;\r\nimport com.my.som.settlevalidate.vo.SetlValidBaseBusiVo;\r\nimport com.my.som.util.SettleListValidateUtil;\r\nimport com.my.som.vo.dataHandle.SettleListHandlerVo;\r\nimport com.my.som.vo.dataHandle.SettleListValidateVo;\r\nimport com.yomahub.liteflow.script.ScriptExecuteWrap;\r\nimport com.yomahub.liteflow.script.body.JaninoCommonScriptBody;\r\nimport org.slf4j.Logger;\r\nimport org.slf4j.LoggerFactory;\r\n\r\nimport java.util.Map;\r\n\r\n/**\r\n * [新生儿天龄-[a16]-基础校验]\r\n */\r\npublic class check_nwb_age_after_basic implements JaninoCommonScriptBody {\r\n    private static final Logger logger = LoggerFactory.getLogger(check_nwb_age_after_basic.class);\r\n    private static final String MAIN_CHECK_FIELD = \"a16\";\r\n    private static final String MAIN_CHECK_NAME = \"新生儿天龄\";\r\n    private static final String MAIN_CHECK_REGION = \"四川,湖北\";\r\n\r\n    /**\r\n     * [新生儿天龄-[a16]-基础校验]\r\n     * 1、门(急)诊诊断编码出现P10-P15,\r\n     * 2. 天龄不为空\r\n     * 3. 且(年龄不足1周岁的)天龄必须小于365天\r\n     *\r\n     * @param wrap\r\n     * @return\r\n     */\r\n    @Override\r\n    public Void body(ScriptExecuteWrap wrap) {\r\n\r\n        SetlValidBaseBusiVo setlValidBaseBusiVo = (SetlValidBaseBusiVo) wrap.cmp.getFirstContextBean();\r\n        SettleListValidateVo settleListValidateVo = (SettleListValidateVo) wrap.cmp.getRequestData();\r\n        Map<String, String> keysBasicResultMap = setlValidBaseBusiVo.getKeysBasicResultMap();\r\n        SomHiInvyBasInfo somHiInvyBasInfo = settleListValidateVo.getSomHiInvyBasInfo();\r\n        SettleListHandlerVo settleListHandlerVo = new SettleListHandlerVo();\r\n        if (SettleValidateUtil.isEmpty(somHiInvyBasInfo)) {\r\n            return null;\r\n        }\r\n        Integer a16 = somHiInvyBasInfo.getA16();\r\n\r\n        String c01c = somHiInvyBasInfo.getC01c();\r\n\r\n        if (!SettleValidateUtil.isEmpty(a16) && SettleValidateUtil.isNotEmpty(c01c))  {\r\n            for(String code : SettleListValidateUtil.P10TP15_LIST) {\r\n                if (c01c.startsWith(code)) {\r\n                    if (!SettleValidateUtil.isEmpty(a16)) {\r\n                        //不为空,则一定是一个正常值，判断范围是否在[200,10000]\r\n                        if (!(a16 >= 1 && a16 <= 365)) {\r\n                            //不在合理值范围，提示\r\n                            addErrorDetail(SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_ERROR_TYPE_2,\r\n                                    MAIN_CHECK_FIELD,\r\n                                    \"门(急)诊诊断编码出现P10-P15\" + MAIN_CHECK_NAME + \"[\" + a16 + \"]超出合理值范围\",\r\n                                    SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n                        }\r\n                    } else {\r\n                        addErrorDetail(SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_ERROR_TYPE_2,\r\n                                MAIN_CHECK_FIELD,\r\n                                \"门(急)诊诊断编码出现P10-P15\" + MAIN_CHECK_NAME + \"[\" + a16 + \"]为空\",\r\n                                SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n                    }\r\n                }\r\n            }\r\n        }\r\n        return null;\r\n    }\r\n\r\n    private void addErrorDetail(String index, String errType, String errorFields, String errDscr, String chkStas, SetlValidBaseBusiVo baseBusiVo) {\r\n        SettleListHandlerVo handlerVo = new SettleListHandlerVo();\r\n        handlerVo.setIndex(index);\r\n        handlerVo.setErrorFields(errorFields);\r\n        handlerVo.setErrDscr(errDscr);\r\n        handlerVo.setChkStas(chkStas);\r\n        handlerVo.setErrType(errType);\r\n        baseBusiVo.getErrorDetails().add(handlerVo);\r\n    }\r\n}\r\n', 'script', 'java', '2024-09-24 17:23:21', '新生儿年龄(天)', '基础信息 ', 1, 1);
INSERT INTO `som_chain_script` VALUES (30, 'somplatform', 'check_nwb_age_basic', '新生儿天龄[a16]基础质控', 'import com.my.som.model.dataHandle.SomHiInvyBasInfo;\r\nimport com.my.som.settlevalidate.constant.SettleValidateConst;\r\nimport com.my.som.settlevalidate.util.SettleValidateUtil;\r\nimport com.my.som.settlevalidate.vo.SetlValidBaseBusiVo;\r\nimport com.my.som.vo.dataHandle.SettleListHandlerVo;\r\nimport com.my.som.vo.dataHandle.SettleListValidateVo;\r\nimport com.yomahub.liteflow.script.ScriptExecuteWrap;\r\nimport com.yomahub.liteflow.script.body.JaninoCommonScriptBody;\r\nimport org.slf4j.Logger;\r\nimport org.slf4j.LoggerFactory;\r\n\r\nimport java.util.Map;\r\n\r\npublic class check_nwb_age_basic implements JaninoCommonScriptBody {\r\n    private static final Logger logger = LoggerFactory.getLogger(check_nwb_age_basic.class);\r\n    private static final String MAIN_CHECK_FIELD = \"a16\";\r\n    private static final String MAIN_CHECK_NAME = \"新生儿天龄\";\r\n    private static final String MAIN_CHECK_REGION = \"四川,湖北\";\r\n    /**\r\n     * [新生儿天龄-[a16]-基础校验]\r\n     * 1、判断是否为空，非空时判断是否为数字，不为数字时则提示异常\r\n     *\r\n     * @param wrap\r\n     * @return\r\n     */\r\n    @Override\r\n    public Void body(ScriptExecuteWrap wrap) {\r\n        String main_check_field = \"a16\";\r\n        String main_check_name = \"新生儿天龄\";\r\n        SetlValidBaseBusiVo setlValidBaseBusiVo = (SetlValidBaseBusiVo) wrap.cmp.getFirstContextBean();\r\n        SettleListValidateVo settleListValidateVo = (SettleListValidateVo) wrap.cmp.getRequestData();\r\n        Map<String, String> keysBasicResultMap = setlValidBaseBusiVo.getKeysBasicResultMap();\r\n        SomHiInvyBasInfo somHiInvyBasInfo = settleListValidateVo.getSomHiInvyBasInfo();\r\n        SettleListHandlerVo settleListHandlerVo = new SettleListHandlerVo();\r\n        if (SettleValidateUtil.isEmpty(somHiInvyBasInfo)) {\r\n            settleListHandlerVo.setIndex(SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY);\r\n            settleListHandlerVo.setErrDscr((String) SettleValidateConst.CHECK_TYPE_MAP.get(SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY));\r\n            settleListHandlerVo.setChkStas(SettleValidateConst.VALIDATE_STATE_FAIL);\r\n            settleListHandlerVo.setErrType(SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY);\r\n            setlValidBaseBusiVo.getErrorDetails().add(settleListHandlerVo);\r\n            return null;\r\n        }\r\n\r\n        Integer a16 = somHiInvyBasInfo.getA16();\r\n        if (SettleValidateUtil.isEmpty(a16) || a16==0) {\r\n            keysBasicResultMap.put(main_check_field, SettleValidateConst.NULL);\r\n        } else {\r\n            if (!(a16 >= 1 && a16 <= 365)) {\r\n                //不在合理值范围，提示\r\n                settleListHandlerVo.setIndex(SettleValidateConst.VALIDATE_TYPE_1);\r\n                settleListHandlerVo.setSettleListId(somHiInvyBasInfo.getId());\r\n                settleListHandlerVo.setErrorFields(main_check_field);\r\n                settleListHandlerVo.setErrDscr(main_check_name + \"[\" + a16 + \"]超出合理值范围\");\r\n                settleListHandlerVo.setChkStas(SettleValidateConst.VALIDATE_STATE_SUCCESS);\r\n                settleListHandlerVo.setErrType(SettleValidateConst.VALIDATE_ERROR_TYPE_2);\r\n                setlValidBaseBusiVo.getErrorDetails().add(settleListHandlerVo);\r\n                //放入字段基础校验\r\n                keysBasicResultMap.put(main_check_field, SettleValidateConst.OUTLIER);\r\n            }else {\r\n                //正常值，基础校验通过\r\n                keysBasicResultMap.put(main_check_field, SettleValidateConst.PASS);\r\n            }\r\n      \r\n        }\r\n        return null;\r\n    }\r\n}\r\n', 'script', 'java', '2024-09-24 17:23:21', '年龄不足一周岁', '基本数据', 0, 1);
INSERT INTO `som_chain_script` VALUES (31, 'somplatform', 'check_nwb_bir_wt_after_basic', '新生儿出生体重[a18]基础质控', 'import com.my.som.dto.medicalQuality.BusDiseaseDiagnosisTrim;\r\nimport com.my.som.model.dataHandle.SomHiInvyBasInfo;\r\nimport com.my.som.settlevalidate.constant.SettleValidateConst;\r\nimport com.my.som.settlevalidate.util.SettleValidateUtil;\r\nimport com.my.som.settlevalidate.vo.SetlValidBaseBusiVo;\r\nimport com.my.som.vo.dataHandle.SettleListHandlerVo;\r\nimport com.my.som.vo.dataHandle.SettleListValidateVo;\r\nimport com.yomahub.liteflow.script.ScriptExecuteWrap;\r\nimport com.yomahub.liteflow.script.body.JaninoCommonScriptBody;\r\nimport org.slf4j.Logger;\r\nimport org.slf4j.LoggerFactory;\r\n\r\nimport java.util.Arrays;\r\nimport java.util.List;\r\nimport java.util.Map;\r\n\r\n/**\r\n * 事后\r\n * 分娩结局新生儿 出生体重非空性\r\n */\r\npublic class check_nwb_bir_wt_after_basic implements JaninoCommonScriptBody {\r\n    private static final Logger logger = LoggerFactory.getLogger(check_nwb_bir_wt_after_basic.class);\r\n\r\n    private static final String MAIN_CHECK_FIELD = \"a18\";\r\n    private static final String MAIN_CHECK_NAME = \"新生儿出生体重\";\r\n    private static final String MAIN_CHECK_REGION = \"四川,湖北\";\r\n    List<String> PARTURITION_CODE_LIST = Arrays.asList(\"Z37.0\", \"Z37.2\", \"Z37.3\", \"Z37.5\", \"Z37.6\");\r\n\r\n    /**\r\n     * 新生儿出生体重-[a18]-基础校验\r\n     * 1、非空时判断是否为整数，不为数字时则提示异常\r\n     * 2、单项数值范围是否在[200,10000]之间\r\n     * 3、多新生儿是否用逗号隔开（隔开后每个判断数字范围）\r\n     *\r\n     * @param wrap\r\n     * @return\r\n     */\r\n    @Override\r\n    public Void body(ScriptExecuteWrap wrap) {\r\n        SetlValidBaseBusiVo setlValidBaseBusiVo = (SetlValidBaseBusiVo) wrap.cmp.getFirstContextBean();\r\n        SettleListValidateVo settleListValidateVo = (SettleListValidateVo) wrap.cmp.getRequestData();\r\n        Map<String, String> keysBasicResultMap = setlValidBaseBusiVo.getKeysBasicResultMap();\r\n        //获取所有诊断集合\r\n        List<BusDiseaseDiagnosisTrim> busDiseaseDiagnosisTrimsList = settleListValidateVo.getBusDiseaseDiagnosisTrimsList();\r\n        SettleListHandlerVo settleListHandlerVo = new SettleListHandlerVo();\r\n        // 检查 somHiInvyBasInfo 是否为空\r\n        SomHiInvyBasInfo somHiInvyBasInfo = settleListValidateVo.getSomHiInvyBasInfo();\r\n\r\n\r\n        if (SettleValidateUtil.isEmpty(busDiseaseDiagnosisTrimsList)) {\r\n            return null;\r\n        }\r\n        if (SettleValidateUtil.isEmpty(somHiInvyBasInfo.getA18())) {\r\n            String mulNwbBirWt = somHiInvyBasInfo.getMulNwbBirWt();\r\n            if (SettleValidateUtil.isEmpty(mulNwbBirWt)) {\r\n                for (BusDiseaseDiagnosisTrim diagnosis : busDiseaseDiagnosisTrimsList) {\r\n                    if (diagnosis.getC06c1().length() > 5) {\r\n                        //此时说明诊断中含有分娩诊断\r\n                        if (PARTURITION_CODE_LIST.contains(diagnosis.getC06c1().substring(0, 5))) {\r\n\r\n                            addErrorDetail(SettleValidateConst.VALIDATE_TYPE_2, SettleValidateConst.VALIDATE_ERROR_TYPE_1,\r\n                                    MAIN_CHECK_FIELD,\r\n                                    \"诊断编码中含有Z37.0/Z37.2/Z37.3/Z37.5/Z37.6,\" + MAIN_CHECK_NAME + \"[\" + somHiInvyBasInfo.getA18() + \"]或多新生儿体重[\"+mulNwbBirWt+\"]不能为空\",\r\n                                    SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n        return null;\r\n    }\r\n\r\n    private void addErrorDetail(String index, String errType, String errorFields, String errDscr, String chkStas, SetlValidBaseBusiVo baseBusiVo) {\r\n        SettleListHandlerVo handlerVo = new SettleListHandlerVo();\r\n        handlerVo.setIndex(index);\r\n        handlerVo.setErrorFields(errorFields);\r\n        handlerVo.setErrDscr(errDscr);\r\n        handlerVo.setChkStas(chkStas);\r\n        handlerVo.setErrType(errType);\r\n        baseBusiVo.getErrorDetails().add(handlerVo);\r\n    }\r\n}', 'script', 'java', '2024-09-24 17:23:21', '新生儿体重', '诊断信息 ', 1, 1);
INSERT INTO `som_chain_script` VALUES (32, 'somplatform', 'check_nwb_bir_wt_basic', '新生儿出生体重[a18]基础质控', 'import com.my.som.model.dataHandle.SomHiInvyBasInfo;\r\nimport com.my.som.settlevalidate.constant.SettleValidateConst;\r\nimport com.my.som.settlevalidate.util.SettleValidateUtil;\r\nimport com.my.som.settlevalidate.vo.SetlValidBaseBusiVo;\r\nimport com.my.som.vo.dataHandle.SettleListHandlerVo;\r\nimport com.my.som.vo.dataHandle.SettleListValidateVo;\r\nimport com.yomahub.liteflow.script.ScriptExecuteWrap;\r\nimport com.yomahub.liteflow.script.body.JaninoCommonScriptBody;\r\nimport org.slf4j.Logger;\r\nimport org.slf4j.LoggerFactory;\r\n\r\nimport java.util.Map;\r\n\r\npublic class check_nwb_bir_wt_basic implements JaninoCommonScriptBody {\r\n    private static final Logger logger = LoggerFactory.getLogger(check_nwb_bir_wt_basic.class);\r\n    private static final String MAIN_CHECK_FIELD = \"a18\";\r\n    private static final String MAIN_CHECK_NAME = \"新生儿出生体重\";\r\n    private static final String MAIN_CHECK_REGION = \"四川,湖北\";\r\n    /**\r\n     * 新生儿出生体重-[a18]-基础校验\r\n     * 1、非空时判断是否为整数，不为数字时则提示异常\r\n     * 2、单项数值范围是否在[200,10000]之间\r\n     * 3、多新生儿是否用逗号隔开（隔开后每个判断数字范围）\r\n     *\r\n     * @param wrap\r\n     * @return\r\n     */\r\n    @Override\r\n    public Void body(ScriptExecuteWrap wrap) {\r\n        String main_check_field = \"a18\";\r\n        String main_check_name = \"新生儿出生体重\";\r\n        SetlValidBaseBusiVo setlValidBaseBusiVo = (SetlValidBaseBusiVo) wrap.cmp.getFirstContextBean();\r\n        SettleListValidateVo settleListValidateVo = (SettleListValidateVo) wrap.cmp.getRequestData();\r\n        Map<String, String> keysBasicResultMap = setlValidBaseBusiVo.getKeysBasicResultMap();\r\n        SomHiInvyBasInfo somHiInvyBasInfo = settleListValidateVo.getSomHiInvyBasInfo();\r\n        SettleListHandlerVo settleListHandlerVo = new SettleListHandlerVo();\r\n        if (SettleValidateUtil.isEmpty(somHiInvyBasInfo)) {\r\n            settleListHandlerVo.setIndex(SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY);\r\n            settleListHandlerVo.setErrDscr((String) SettleValidateConst.CHECK_TYPE_MAP.get(SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY));\r\n            settleListHandlerVo.setChkStas(SettleValidateConst.VALIDATE_STATE_FAIL);\r\n            settleListHandlerVo.setErrType(SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY);\r\n            setlValidBaseBusiVo.getErrorDetails().add(settleListHandlerVo);\r\n            return null;\r\n        }\r\n        Double a18 = somHiInvyBasInfo.getA18();\r\n        String mulNwbBirWt = somHiInvyBasInfo.getMulNwbBirWt();\r\n\r\n        if (!SettleValidateUtil.isEmpty(a18)) {\r\n            //不为空,则一定是一个正常值，判断范围是否在[200,10000]\r\n            if (a18 == 0.0) {\r\n                //处理为null,被默认赋值为0.0数据\r\n                keysBasicResultMap.put(main_check_field, SettleValidateConst.NULL);\r\n                return null;\r\n            }\r\n            if (!(a18 >= 200 && a18 <= 10000)) {\r\n                //不在合理值范围，提示\r\n                settleListHandlerVo.setIndex(SettleValidateConst.VALIDATE_TYPE_1);\r\n                settleListHandlerVo.setSettleListId(somHiInvyBasInfo.getId());\r\n                settleListHandlerVo.setErrorFields(main_check_field);\r\n                settleListHandlerVo.setErrDscr(main_check_name + \"[\" + a18 + \"]超出合理值范围\");\r\n                settleListHandlerVo.setChkStas(SettleValidateConst.VALIDATE_STATE_SUCCESS);\r\n                settleListHandlerVo.setErrType(SettleValidateConst.VALIDATE_ERROR_TYPE_2);\r\n                setlValidBaseBusiVo.getErrorDetails().add(settleListHandlerVo);\r\n                //放入字段基础校验\r\n                keysBasicResultMap.put(main_check_field, SettleValidateConst.OUTLIER);\r\n            } else {\r\n                //正常值，基础校验通过不写错误信息\r\n                keysBasicResultMap.put(main_check_field, SettleValidateConst.PASS);\r\n            }\r\n        } else {\r\n            keysBasicResultMap.put(main_check_field, SettleValidateConst.NULL);\r\n        }\r\n\r\n        checkMulNwbBirWt(mulNwbBirWt, settleListHandlerVo, somHiInvyBasInfo, setlValidBaseBusiVo, keysBasicResultMap);\r\n        return null;\r\n    }\r\n\r\n\r\n    /**\r\n     * 检查多新生儿出生体重\r\n     * @param mulNwbBirWt\r\n     * @param settleListHandlerVo\r\n     * @param somHiInvyBasInfo\r\n     * @param setlValidBaseBusiVo\r\n     * @param keysBasicResultMap\r\n     */\r\n    private  void checkMulNwbBirWt(String mulNwbBirWt, SettleListHandlerVo settleListHandlerVo, SomHiInvyBasInfo somHiInvyBasInfo, SetlValidBaseBusiVo setlValidBaseBusiVo, Map<String, String> keysBasicResultMap) {\r\n        if (!SettleValidateUtil.isEmpty(mulNwbBirWt)) {\r\n            if(!mulNwbBirWt.contains(\",\")){\r\n                addErrorDetail(SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_ERROR_TYPE_303,\r\n                        \"mulNwbBirWt\", \"多新生儿体重[\"+mulNwbBirWt+\"]不存在,请将体重填写到【新生儿入院体重】中\",\r\n                        SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n            }else{\r\n            // 分割字符串\r\n            String[] stringValues = mulNwbBirWt.split(\",\");\r\n            // 转换并添加到 List 中\r\n            for (int i = 0; i < stringValues.length; i++) {\r\n                String value = stringValues[i];\r\n                try {\r\n                    Double wt = Double.parseDouble(value.trim());\r\n                    if (!(wt >= 200 && wt <= 10000)) {\r\n                        addErrorDetail(SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_ERROR_TYPE_2,\r\n                                \"mulNwbBirWt\", \"多新生儿体重：\"+\"第\"+(i +1)+\"个新生儿体重\"+ \"[\" + value + \"]的超出合理值范围\",\r\n                                SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n                    }\r\n                } catch (NumberFormatException e) {\r\n                    addErrorDetail(SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_ERROR_TYPE_2,\r\n                            \"mulNwbBirWt\", \"多新生儿体重：\"+\"第\"+(i+1)+\"个新生儿体重\"+ \"[\" + value + \"]不为体重阿拉伯数字\",\r\n                            SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n                }\r\n            }}\r\n        } else {\r\n            keysBasicResultMap.put(\"mulNwbBirWt\", SettleValidateConst.NULL);\r\n        }\r\n    }\r\n\r\n    private  void addErrorDetail(String index, String errType, String errorFields, String errDscr, String chkStas, SetlValidBaseBusiVo baseBusiVo) {\r\n        SettleListHandlerVo handlerVo = new SettleListHandlerVo();\r\n        handlerVo.setIndex(index);\r\n        handlerVo.setErrorFields(errorFields);\r\n        handlerVo.setErrDscr(errDscr);\r\n        handlerVo.setChkStas(chkStas);\r\n        handlerVo.setErrType(errType);\r\n        baseBusiVo.getErrorDetails().add(handlerVo);\r\n    }\r\n}\r\n', 'script', 'java', '2024-09-24 17:23:21', '新生儿出生体重', '基本数据', 0, 1);
INSERT INTO `som_chain_script` VALUES (33, 'somplatform', 'check_oper_dr_code_basic', '术者医师代码[c39c]基础质控', 'import com.my.som.model.dataHandle.SomHiInvyBasInfo;\r\nimport com.my.som.model.dataHandle.SomOprnOprtInfo;\r\nimport com.my.som.settlevalidate.constant.SettleValidateConst;\r\nimport com.my.som.settlevalidate.util.SettleValidateUtil;\r\nimport com.my.som.settlevalidate.vo.SetlValidBaseBusiVo;\r\nimport com.my.som.util.SettleListValidateUtil;\r\nimport com.my.som.vo.dataHandle.SettleListHandlerVo;\r\nimport com.my.som.vo.dataHandle.SettleListValidateVo;\r\nimport com.yomahub.liteflow.script.ScriptExecuteWrap;\r\nimport com.yomahub.liteflow.script.body.JaninoCommonScriptBody;\r\nimport org.slf4j.Logger;\r\nimport org.slf4j.LoggerFactory;\r\n\r\nimport java.util.List;\r\nimport java.util.Map;\r\n\r\n\r\n\r\npublic class check_oper_dr_code_basic implements JaninoCommonScriptBody {\r\n    private static final Logger logger = LoggerFactory.getLogger(check_oper_dr_code_basic.class);\r\n\r\n    private static final String MAIN_CHECK_FIELD = \"c39c\";\r\n    private static final String MAIN_CHECK_NAME = \"术者医师代码\";\r\n    private static final String MAIN_CHECK_REGION = \"四川,湖北\";\r\n    /**\r\n     * 术者医师代码(oper_dr_code)基础质控\r\n     * 1.存在手术或操作时，术者医师代码不能为空。\r\n     * 2.判断术者医师代码是否与医保业务编码标准动态维护平合上的术者医师姓名相匹配。\r\n     *\r\n     * @param wrap\r\n     * @return\r\n     */\r\n    @Override\r\n    public Void body(ScriptExecuteWrap wrap) {\r\n\r\n        SetlValidBaseBusiVo setlValidBaseBusiVo = (SetlValidBaseBusiVo) wrap.cmp.getFirstContextBean();\r\n        SettleListValidateVo settleListValidateVo = (SettleListValidateVo) wrap.cmp.getRequestData();\r\n        List<SomOprnOprtInfo> busOperateDiagnosisList = settleListValidateVo.getBusOperateDiagnosisList();\r\n        SomHiInvyBasInfo somHiInvyBasInfo = settleListValidateVo.getSomHiInvyBasInfo();\r\n\r\n        Map<String, Object> doctorMap = (Map<String, Object>) setlValidBaseBusiVo.getSettleListParams().get(SettleListValidateUtil.KEY_DOCTOR_CODE);\r\n        Map<String, Object> nurseMap = (Map<String, Object>) setlValidBaseBusiVo.getSettleListParams().get(SettleListValidateUtil.KEY_NURSE_CODE);\r\n        // 获取机构编码\r\n        String hospitalId = somHiInvyBasInfo.getHospitalId();\r\n        // 获取医保医师映射表\r\n        Map<String, Object> doctorCodeMap = (Map<String, Object>) doctorMap.get(hospitalId);\r\n        Map<String, Object> nurseCodeMap = (Map<String, Object>) nurseMap.get(hospitalId);\r\n\r\n        if(!SettleValidateUtil.isEmpty(doctorCodeMap) && !SettleValidateUtil.isEmpty(nurseCodeMap)){\r\n            // 不为空 判断是否符合医保医师代码标准\r\n            if (!SettleValidateUtil.isEmpty(busOperateDiagnosisList)) {\r\n                for (int i = 0; i < busOperateDiagnosisList.size(); i++) {\r\n                    SomOprnOprtInfo somOprnOprtInfo = (SomOprnOprtInfo) busOperateDiagnosisList.get(i);\r\n                    String operCode = somOprnOprtInfo.getC39c();\r\n                    if (SettleValidateUtil.isEmpty(operCode)) {\r\n                        addErrorDetail(SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_ERROR_TYPE_1,\r\n                                MAIN_CHECK_FIELD,\r\n                                \"第\" + (i + 1) + \"个手术操作：编码为[\"+somOprnOprtInfo.getC35c()+\"]的\" + MAIN_CHECK_NAME + \"[\" + operCode + \"]为空\",\r\n                                SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n                    } else {\r\n                        // 存在医保医师编码映射信息质控通过\r\n                        if (!doctorCodeMap.containsKey(operCode) && !nurseCodeMap.containsKey(operCode)) {\r\n                            addErrorDetail(SettleValidateConst.VALIDATE_TYPE_2, SettleValidateConst.VALIDATE_ERROR_TYPE_1,\r\n                                    MAIN_CHECK_FIELD,\r\n                                    \"第\" + (i + 1) + \"个手术操作：编码为[\"+somOprnOprtInfo.getC35c()+\"]的\" + MAIN_CHECK_NAME + \"[\" + operCode + \"]与医保业务编码不匹配\",\r\n                                    SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n                        }\r\n\r\n                    }\r\n                }\r\n            }\r\n        }else{\r\n            if(SettleValidateUtil.isEmpty(doctorCodeMap) ){\r\n                addErrorDetail(SettleValidateConst.VALIDATE_TYPE_2, SettleValidateConst.VALIDATE_ERROR_TYPE_1,\r\n                        MAIN_CHECK_FIELD,\r\n                        \"未获取到医保医师映射编码信息\",\r\n                        SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n            }\r\n            if(SettleValidateUtil.isEmpty(nurseCodeMap) ){\r\n                addErrorDetail(SettleValidateConst.VALIDATE_TYPE_2, SettleValidateConst.VALIDATE_ERROR_TYPE_1,\r\n                        MAIN_CHECK_FIELD,\r\n                        \"未获取到医保护士映射编码信息\",\r\n                        SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n            }\r\n\r\n        }\r\n\r\n\r\n        return null;\r\n    }\r\n\r\n    private void addErrorDetail(String index, String errType, String errorFields, String errDscr, String chkStas, SetlValidBaseBusiVo baseBusiVo) {\r\n        SettleListHandlerVo handlerVo = new SettleListHandlerVo();\r\n        handlerVo.setIndex(index);\r\n        handlerVo.setErrorFields(errorFields);\r\n        handlerVo.setErrDscr(errDscr);\r\n        handlerVo.setChkStas(chkStas);\r\n        handlerVo.setErrType(errType);\r\n        baseBusiVo.getErrorDetails().add(handlerVo);\r\n    }\r\n}\r\n', 'script', 'java', '2024-09-24 17:23:21', '术者医师代码', '手术信息', 0, 1);
INSERT INTO `som_chain_script` VALUES (34, 'somplatform', 'check_oper_dr_name_basic', '术者医师姓名[oprn_oprt_oper_name]基础质控', 'import com.my.som.model.dataHandle.SomHiInvyBasInfo;\r\nimport com.my.som.model.dataHandle.SomOprnOprtInfo;\r\nimport com.my.som.settlevalidate.constant.SettleValidateConst;\r\nimport com.my.som.settlevalidate.util.SettleValidateUtil;\r\nimport com.my.som.settlevalidate.vo.SetlValidBaseBusiVo;\r\nimport com.my.som.util.SettleListValidateUtil;\r\nimport com.my.som.vo.dataHandle.SettleListHandlerVo;\r\nimport com.my.som.vo.dataHandle.SettleListValidateVo;\r\nimport com.yomahub.liteflow.script.ScriptExecuteWrap;\r\nimport com.yomahub.liteflow.script.body.JaninoCommonScriptBody;\r\nimport org.slf4j.Logger;\r\nimport org.slf4j.LoggerFactory;\r\n\r\nimport java.util.List;\r\nimport java.util.Map;\r\n\r\npublic class check_oper_dr_name_basic implements JaninoCommonScriptBody {\r\n    private static final Logger logger = LoggerFactory.getLogger(check_oper_dr_name_basic.class);\r\n\r\n    private static final String MAIN_CHECK_FIELD = \"oprn_oprt_oper_name\";\r\n    private static final String MAIN_CHECK_NAME = \"术者医师姓名\";\r\n    private static final String MAIN_CHECK_REGION = \"四川,湖北\";\r\n    /**\r\n     * 术者医师姓名(oper_dr_name)质控\r\n     * 1.存在手术或操作时，术者医师姓名不能为空\r\n     * @param wrap\r\n     * @return\r\n     */\r\n    @Override\r\n    public Void body(ScriptExecuteWrap wrap) {\r\n\r\n        SetlValidBaseBusiVo setlValidBaseBusiVo = (SetlValidBaseBusiVo) wrap.cmp.getFirstContextBean();\r\n        SettleListValidateVo settleListValidateVo = (SettleListValidateVo) wrap.cmp.getRequestData();\r\n        List<SomOprnOprtInfo> busOperateDiagnosisList = settleListValidateVo.getBusOperateDiagnosisList();\r\n        SomHiInvyBasInfo somHiInvyBasInfo = settleListValidateVo.getSomHiInvyBasInfo();\r\n        if (SettleValidateUtil.isEmpty(somHiInvyBasInfo)) {\r\n            return null;\r\n        }\r\n        Map<String,Object> doctormap = (Map<String,Object>)(setlValidBaseBusiVo.getSettleListParams().get(SettleListValidateUtil.KEY_DOCTOR_NAME));\r\n        Map<String,Object> doctorNameMap = (Map<String,Object>)doctormap.get(somHiInvyBasInfo.getHospitalId()+SettleListValidateUtil.KEY_DOCTOR_NAME);\r\n        Map<String, Object> nurseMap = (Map<String, Object>) (setlValidBaseBusiVo.getSettleListParams().get(SettleListValidateUtil.KEY_NURSE_NAME));\r\n        //获取护士名称\r\n        Map<String, String> nurseNameMap = (Map<String, String>) nurseMap.get(somHiInvyBasInfo.getHospitalId() + SettleListValidateUtil.KEY_NURSE_NAME);\r\n        if(!SettleValidateUtil.isEmpty(nurseNameMap) && !SettleValidateUtil.isEmpty(doctorNameMap))    {\r\n            if (!SettleValidateUtil.isEmpty(busOperateDiagnosisList)) {\r\n                for (int i = 0; i < busOperateDiagnosisList.size(); i++) {\r\n                    SomOprnOprtInfo somOprnOprtInfo = (SomOprnOprtInfo) busOperateDiagnosisList.get(i);\r\n                    String operName = somOprnOprtInfo.getOprn_oprt_oper_name();\r\n                    String operCode = somOprnOprtInfo.getC39c();\r\n                    if (SettleValidateUtil.isEmpty(operName)) {\r\n                        addErrorDetail(SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_ERROR_TYPE_1,\r\n                                MAIN_CHECK_FIELD,\r\n                                \"第\" + (i + 1) + \"个手术操作：编码为[\"+somOprnOprtInfo.getC35c()+\"]的\" +MAIN_CHECK_NAME + \"[\" + operName + \"]为空\",\r\n                                SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n                    }else{\r\n                        String doctorName= (String)doctorNameMap.get(operCode);\r\n                        String nurseName= (String)nurseNameMap.get(operCode);\r\n                        if(!SettleValidateUtil.isEmpty(doctorName) || !SettleValidateUtil.isEmpty(nurseName) ){\r\n                            if(!operName.equals(doctorName) && !operName.equals(nurseName) ){\r\n\r\n                                addErrorDetail( SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_ERROR_TYPE_2,\r\n                                        MAIN_CHECK_FIELD,\r\n                                        \"第\" + (i + 1) + \"个手术操作：编码为[\"+somOprnOprtInfo.getC35c()+\"]的\" + MAIN_CHECK_NAME + \"[\" + operName + \"]与该编码[\" + operCode + \"]对应的医院医生名称[\" + doctorName + \"]不匹配\",\r\n\r\n                                        SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n                            }\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n\r\n        }else{\r\n            if(SettleValidateUtil.isEmpty(doctorNameMap) ){\r\n                addErrorDetail(SettleValidateConst.VALIDATE_TYPE_2, SettleValidateConst.VALIDATE_ERROR_TYPE_1,\r\n                        MAIN_CHECK_FIELD,\r\n                        \"未获取到医保医师映射名称信息\",\r\n                        SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n            }\r\n            if(SettleValidateUtil.isEmpty(nurseNameMap) ){\r\n                addErrorDetail(SettleValidateConst.VALIDATE_TYPE_2, SettleValidateConst.VALIDATE_ERROR_TYPE_1,\r\n                        MAIN_CHECK_FIELD,\r\n                        \"未获取到医保护士映射名称信息\",\r\n                        SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n            }\r\n        }\r\n        return null;\r\n    }\r\n\r\n    private void addErrorDetail(String index,String errType,String errorFields, String errDscr, String chkStas, SetlValidBaseBusiVo baseBusiVo) {\r\n        SettleListHandlerVo handlerVo = new SettleListHandlerVo();\r\n        handlerVo.setIndex(index);\r\n        handlerVo.setErrorFields(errorFields);\r\n        handlerVo.setErrDscr(errDscr);\r\n        handlerVo.setChkStas(chkStas);\r\n        handlerVo.setErrType(errType);\r\n        baseBusiVo.getErrorDetails().add(handlerVo);\r\n    }\r\n}', 'script', 'java', '2024-09-24 17:23:21', '术者医师名称', '手术信息', 0, 1);
INSERT INTO `som_chain_script` VALUES (35, 'somplatform', 'check_oprn_oprt_time_after_basic', '手术时间[oprnOprtTime]基础质控', 'import com.my.som.common.util.ValidateUtil;\r\nimport com.my.som.model.dataHandle.SomOprnOprtInfo;\r\nimport com.my.som.settlevalidate.constant.SettleValidateConst;\r\nimport com.my.som.settlevalidate.util.SettleValidateUtil;\r\nimport com.my.som.settlevalidate.vo.SetlValidBaseBusiVo;\r\nimport com.my.som.vo.dataHandle.SettleListHandlerVo;\r\nimport com.my.som.vo.dataHandle.SettleListValidateVo;\r\nimport com.yomahub.liteflow.script.ScriptExecuteWrap;\r\nimport com.yomahub.liteflow.script.body.JaninoCommonScriptBody;\r\nimport org.slf4j.Logger;\r\nimport org.slf4j.LoggerFactory;\r\n\r\nimport java.time.LocalDateTime;\r\nimport java.time.format.DateTimeFormatter;\r\nimport java.time.format.DateTimeParseException;\r\nimport java.util.List;\r\nimport java.util.Map;\r\n\r\n/**\r\n * 判断 手术时间不为空且规范\r\n */\r\npublic class check_oprn_oprt_time_after_basic implements JaninoCommonScriptBody {\r\n    private static final Logger logger = LoggerFactory.getLogger(check_oprn_oprt_time_after_basic.class);\r\n\r\n    private static final String MAIN_CHECK_FIELD = \"oprnOprtTime\";\r\n    private static final String MAIN_CHECK_NAME = \"手术时间\";\r\n    private static final String MAIN_CHECK_REGION = \"四川,湖北\";\r\n    private static final String MAIN_CHECK_FIELD_1 = \"oprnOprtBegntime\";\r\n    private static final String MAIN_CHECK_NAME_1 = \"手术开始时间\";\r\n\r\n    private static final String MAIN_CHECK_FIELD_2 = \"oprnOprtEndtime\";\r\n    private static final String MAIN_CHECK_NAME_2 = \"手术结束时间\";\r\n\r\n    private static final String REPEAT_OPRN_RIME_CODE = \"REPEAT_OPRN_RIME_CODE\";\r\n    /**\r\n     * 事后校验\r\n     * 结算结束时间(setl_date)基础质控\r\n     * 判断结算时间是否为空\r\n     *\r\n     * @param wrap\r\n     * @return\r\n     */\r\n    @Override\r\n    public Void body(ScriptExecuteWrap wrap) {\r\n        SetlValidBaseBusiVo setlValidBaseBusiVo = (SetlValidBaseBusiVo) wrap.cmp.getFirstContextBean();\r\n        SettleListValidateVo settleListValidateVo = (SettleListValidateVo) wrap.cmp.getRequestData();\r\n        Map<String, String> keysBasicResultMap = setlValidBaseBusiVo.getKeysBasicResultMap();\r\n        List<SomOprnOprtInfo> busOperateDiagnosisList = settleListValidateVo.getBusOperateDiagnosisList();\r\n\r\n        // 检查 somHiInvyBasInfo 是否为空\r\n        if (SettleValidateUtil.isEmpty(busOperateDiagnosisList)) {\r\n            return null;\r\n        }\r\n\r\n        // 获取结算 结束时间\r\n        for (int i = 0; i < busOperateDiagnosisList.size(); i++) {\r\n            SomOprnOprtInfo somOprnOprtInfo = (SomOprnOprtInfo)busOperateDiagnosisList.get(i);\r\n            String oprnOprtBegntime = somOprnOprtInfo.getOprnOprtBegntime();\r\n            String oprnOprtEndtime = somOprnOprtInfo.getOprnOprtEndtime();\r\n            //手术开始时间\r\n            if (SettleValidateUtil.isEmpty(oprnOprtBegntime)) {\r\n                //手术开始时间为空\r\n                addErrorDetail(SettleValidateConst.VALIDATE_TYPE_2, SettleValidateConst.VALIDATE_TYPE_1,\r\n                        \"第\" + (i + 1) + \"个手术操作：编码为[\"+somOprnOprtInfo.getC35c()+\"]的\" + MAIN_CHECK_NAME_1 + \"[\" + oprnOprtBegntime + \"]为空\",\r\n                        MAIN_CHECK_FIELD_1,\r\n                        SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n                keysBasicResultMap.put(i + i+MAIN_CHECK_FIELD_1, SettleValidateConst.NULL);\r\n            } else {\r\n                if (!isValidDateTime(oprnOprtBegntime)) {\r\n                    addErrorDetail(SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_ERROR_TYPE_2,\r\n                            \"第\" + (i + 1) + \"个手术操作：编码为[\"+somOprnOprtInfo.getC35c()+\"]的\" + MAIN_CHECK_NAME_1 + \"[\" + oprnOprtBegntime + \"]时间格式错误\",\r\n                            MAIN_CHECK_FIELD_1,\r\n                            SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n                    keysBasicResultMap.put(i+MAIN_CHECK_FIELD_1, SettleValidateConst.OUTLIER);\r\n                } else {\r\n                    keysBasicResultMap.put(i+MAIN_CHECK_FIELD_1, SettleValidateConst.PASS);\r\n                }\r\n            }\r\n\r\n            // 手术结束时间\r\n            if (SettleValidateUtil.isEmpty(oprnOprtEndtime)) {\r\n                addErrorDetail(SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_TYPE_1,\r\n                        \"第\" + (i + 1) + \"个手术操作：编码为[\"+somOprnOprtInfo.getC35c()+\"]的\" + MAIN_CHECK_NAME_2 + \"[\" + oprnOprtEndtime + \"]为空\",\r\n                        MAIN_CHECK_FIELD_2,\r\n                        SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n                keysBasicResultMap.put(i+MAIN_CHECK_FIELD_2, SettleValidateConst.NULL);\r\n            } else {\r\n                if (!isValidDateTime(oprnOprtEndtime)) {\r\n                    addErrorDetail(SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_ERROR_TYPE_2,\r\n                            \"第\" + (i + 1) + \"个手术操作：编码为[\"+somOprnOprtInfo.getC35c()+\"]的\" + MAIN_CHECK_NAME_2 + \"[\" + oprnOprtEndtime + \"]时间格式错误\",\r\n                            MAIN_CHECK_FIELD_2,\r\n                            SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n                    keysBasicResultMap.put(i+MAIN_CHECK_FIELD_2, SettleValidateConst.OUTLIER);\r\n                } else {\r\n                    keysBasicResultMap.put(i+MAIN_CHECK_FIELD_2, SettleValidateConst.PASS);\r\n                }\r\n            }\r\n        }\r\n        // 从时间上判断编码是否重复（方便维护版）\r\n        for (int i = 0; i < busOperateDiagnosisList.size(); i++) {\r\n            SomOprnOprtInfo somOprnOprtInfo = (SomOprnOprtInfo) busOperateDiagnosisList.get(i);\r\n            //如果手术时间不为空\r\n            if (SettleValidateConst.PASS.equals((String) keysBasicResultMap.get(i + MAIN_CHECK_FIELD_1))\r\n                    && SettleValidateConst.PASS.equals((String) keysBasicResultMap.get(i + MAIN_CHECK_FIELD_2))) {\r\n                //判断时间不重叠\r\n                for (int j = i + 1; j < busOperateDiagnosisList.size(); j++) {\r\n                    SomOprnOprtInfo somOprnOprtInfoNext = (SomOprnOprtInfo) busOperateDiagnosisList.get(j);\r\n                    System.out.println(SettleValidateConst.PASS.equals(keysBasicResultMap.get(j + MAIN_CHECK_FIELD_1)));\r\n                    if (SettleValidateConst.PASS.equals(keysBasicResultMap.get(j + MAIN_CHECK_FIELD_1)) &&\r\n                            SettleValidateConst.PASS.equals(keysBasicResultMap.get(j + MAIN_CHECK_FIELD_2))) {\r\n                        // 时间格式\r\n                        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(\"yyyy-MM-dd HH:mm:ss\");\r\n\r\n                        // 将字符串转为 LocalDateTime\r\n                        LocalDateTime inpool = LocalDateTime.parse(somOprnOprtInfo.getOprnOprtBegntime(), formatter);\r\n                        LocalDateTime exit = LocalDateTime.parse(somOprnOprtInfo.getOprnOprtEndtime(), formatter);\r\n                        LocalDateTime inpoolNext = LocalDateTime.parse(somOprnOprtInfoNext.getOprnOprtBegntime(), formatter);\r\n                        LocalDateTime exitNext = LocalDateTime.parse(somOprnOprtInfoNext.getOprnOprtEndtime(), formatter);\r\n\r\n                        if ((inpoolNext.isBefore(inpool)  && inpool.isBefore(exitNext))\r\n                                || (inpoolNext.isBefore(exit)  && exit.isBefore(exitNext)) || inpoolNext.equals(inpool) ||exit.equals(exitNext) ) {\r\n\r\n                            String code = \"\";\r\n                            if(ValidateUtil.isEmpty( keysBasicResultMap.get(REPEAT_OPRN_RIME_CODE))){\r\n                                code = somOprnOprtInfo.getC35c() +\"@\" + somOprnOprtInfoNext.getC35c();\r\n                                keysBasicResultMap.put(REPEAT_OPRN_RIME_CODE, code);\r\n                            }else{\r\n                                code= code +\"@\" +somOprnOprtInfoNext.getC35c();\r\n                                keysBasicResultMap.put(REPEAT_OPRN_RIME_CODE, code);\r\n                            }\r\n\r\n                            // 如果当前时间段与其它时间段重叠，设置标志为false\r\n                            addErrorDetail(SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_ERROR_TYPE_1,\r\n                                    \"第\" + (i + 1) + \"个手术与第\"+ (j + 1) +\"个手术的时间冲突。\" ,   MAIN_CHECK_FIELD,\r\n                                    SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n\r\n        }\r\n        return null;\r\n    }\r\n\r\n    public static boolean isValidDateTime(String dateTimeStr) {\r\n        String format = \"yyyy-MM-dd HH:mm:ss\";\r\n        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(format);\r\n        try {\r\n            LocalDateTime.parse(dateTimeStr, formatter);\r\n            return true;\r\n        } catch (DateTimeParseException e) {\r\n            return false;\r\n        }\r\n    }\r\n\r\n    private void addErrorDetail(String index, String errType, String errDscr, String errorFields, String chkStas, SetlValidBaseBusiVo baseBusiVo) {\r\n        SettleListHandlerVo handlerVo = new SettleListHandlerVo();\r\n        handlerVo.setIndex(index);\r\n        handlerVo.setErrDscr(errDscr);\r\n        handlerVo.setErrorFields(errorFields);\r\n        handlerVo.setChkStas(chkStas);\r\n        handlerVo.setErrType(errType);\r\n        baseBusiVo.getErrorDetails().add(handlerVo);\r\n    }\r\n}\r\n', 'script', 'java', '2024-09-24 17:23:21', '手术时间', '手术信息 ', 1, 1);
INSERT INTO `som_chain_script` VALUES (36, 'somplatform', 'check_otp_tcm_dise_after_basic', '门（急）诊诊断编码（中医）[c35c]基础质控', 'import com.my.som.model.dataHandle.SomHiInvyBasInfo;\r\nimport com.my.som.settlevalidate.constant.SettleValidateConst;\r\nimport com.my.som.settlevalidate.util.SettleValidateUtil;\r\nimport com.my.som.settlevalidate.vo.SetlValidBaseBusiVo;\r\nimport com.my.som.util.SettleListValidateUtil;\r\nimport com.my.som.vo.dataHandle.SettleListHandlerVo;\r\nimport com.my.som.vo.dataHandle.SettleListValidateVo;\r\nimport com.yomahub.liteflow.script.ScriptExecuteWrap;\r\nimport com.yomahub.liteflow.script.body.JaninoCommonScriptBody;\r\nimport org.slf4j.Logger;\r\nimport org.slf4j.LoggerFactory;\r\n\r\nimport java.util.Arrays;\r\nimport java.util.List;\r\nimport java.util.Map;\r\n\r\n/**\r\n * 判断 门（急）诊诊断（中医诊断）\r\n */\r\npublic class check_otp_tcm_dise_after_basic implements JaninoCommonScriptBody {\r\n    private static final Logger logger = LoggerFactory.getLogger(check_otp_tcm_dise_after_basic.class);\r\n    private static final String MAIN_CHECK_FIELD = \"c35c\";\r\n    private static final String MAIN_CHECK_NAME = \"门（急）诊诊断编码（中医）\";\r\n    private static final String MAIN_CHECK_FIELD1 = \"c36n\";\r\n    private static final String MAIN_CHECK_NAME1 = \"门（急）诊诊断名称（中医）\";\r\n    private static final String MAIN_CHECK_REGION = \"四川,湖北\";\r\n    /**\r\n     * 事后校验\r\n     * 门（急）诊诊断编码（中医）(otp_tcm_dise_name)基础质控\r\n     * 判断门（急）诊诊断编码（中医）是否为空\r\n     *\r\n     * @param wrap\r\n     * @return\r\n     */\r\n    @Override\r\n    public Void body(ScriptExecuteWrap wrap) {\r\n        SetlValidBaseBusiVo setlValidBaseBusiVo = (SetlValidBaseBusiVo) wrap.cmp.getFirstContextBean();\r\n        SettleListValidateVo settleListValidateVo = (SettleListValidateVo) wrap.cmp.getRequestData();\r\n        Map<String, String> keysBasicResultMap = setlValidBaseBusiVo.getKeysBasicResultMap();\r\n        SomHiInvyBasInfo somHiInvyBasInfo = settleListValidateVo.getSomHiInvyBasInfo();\r\n        SettleListHandlerVo settleListHandlerVo = new SettleListHandlerVo();\r\n\r\n        // 检查 somHiInvyBasInfo 是否为空\r\n        if (SettleValidateUtil.isEmpty(somHiInvyBasInfo)) {\r\n            addErrorDetail(SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY, SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY,\r\n                    (String) SettleValidateConst.CHECK_TYPE_MAP.get(SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY), null,\r\n                    SettleValidateConst.VALIDATE_STATE_FAIL, setlValidBaseBusiVo);\r\n            return null;\r\n        }\r\n        // 获取主证诊断\r\n        Map<String, String> principaltMap = (Map<String, String>) setlValidBaseBusiVo.getSettleListParams().get(SettleListValidateUtil.KEY_TCM_PRINCIPAL);\r\n        //获取主病诊断 A03.06.04.05\r\n        Map<String, String> mainDiseasetMap = (Map<String, String>) setlValidBaseBusiVo.getSettleListParams().get(SettleListValidateUtil.KEY_TCM_MAIN_DISS);\r\n\r\n        // 获取门（急）诊诊断名称（中医）\r\n        String c35c = somHiInvyBasInfo.getC35c();\r\n        String c36n = somHiInvyBasInfo.getC36n();\r\n\r\n        if (SettleValidateUtil.isNotEmpty(c35c)) {\r\n            if (SettleValidateUtil.isEmpty(c36n)) {\r\n                addErrorDetail(SettleValidateConst.VALIDATE_TYPE_2, SettleValidateConst.VALIDATE_ERROR_TYPE_1,\r\n                        MAIN_CHECK_NAME1 + \"[\" + c36n + \"]为空\", MAIN_CHECK_FIELD1,\r\n                        SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n            }\r\n\r\n            if (principaltMap.containsKey(c35c)) {\r\n                    List<String> list = Arrays.asList(principaltMap.get(c35c).toString().split(\";\"));\r\n                    boolean flag = false;\r\n                    for (String code :list){\r\n                        if(c36n.equals(code)){\r\n                            flag =true;\r\n                        }\r\n                    }\r\n                    if (!flag) {\r\n                        addErrorDetail(SettleValidateConst.VALIDATE_TYPE_2, SettleValidateConst.VALIDATE_ERROR_TYPE_2,\r\n                                MAIN_CHECK_NAME1 + \"[\" + c36n + \"]与中医主证编码对应的名称不符\", MAIN_CHECK_FIELD1,\r\n                                SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n                    }\r\n            } else if (mainDiseasetMap.containsKey(c35c)) {\r\n                List<String> list = Arrays.asList(mainDiseasetMap.get(c35c).toString().split(\";\"));\r\n                boolean flag = false;\r\n                for (String code :list){\r\n                    if(c36n.equals(code)){\r\n                        flag =true;\r\n                    }\r\n                }\r\n                if (!flag) {\r\n                    addErrorDetail(SettleValidateConst.VALIDATE_TYPE_2, SettleValidateConst.VALIDATE_ERROR_TYPE_2,\r\n                            MAIN_CHECK_NAME1 + \"[\" + c36n + \"]与中医主病编码对应的名称不符\", MAIN_CHECK_FIELD1,\r\n                            SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n                }\r\n            } else {\r\n                addErrorDetail(SettleValidateConst.VALIDATE_TYPE_2, SettleValidateConst.VALIDATE_ERROR_TYPE_2,\r\n                        MAIN_CHECK_NAME + \"[\" + c35c + \"]不属于中医主证或者中医主病编码标准代码\", MAIN_CHECK_FIELD,\r\n                        SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n            }\r\n\r\n        } else {\r\n            if (SettleValidateUtil.isNotEmpty(c36n)) {\r\n                addErrorDetail(SettleValidateConst.VALIDATE_TYPE_2, SettleValidateConst.VALIDATE_ERROR_TYPE_1,\r\n                        MAIN_CHECK_NAME + \"[\" + c35c + \"]为空但是\" + MAIN_CHECK_NAME1 + \"存在\", MAIN_CHECK_FIELD,\r\n                        SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n            }\r\n        }\r\n        return null;\r\n    }\r\n\r\n    private void addErrorDetail(String index, String errType, String errDscr, String errorFields, String chkStas, SetlValidBaseBusiVo baseBusiVo) {\r\n        SettleListHandlerVo handlerVo = new SettleListHandlerVo();\r\n        handlerVo.setIndex(index);\r\n        handlerVo.setErrDscr(errDscr);\r\n        handlerVo.setErrorFields(errorFields);\r\n        handlerVo.setChkStas(chkStas);\r\n        handlerVo.setErrType(errType);\r\n        baseBusiVo.getErrorDetails().add(handlerVo);\r\n    }\r\n}\r\n', 'script', 'java', '2024-09-24 17:23:21', '门（急）诊诊断（中医诊断）', '住院信息 ', 1, 1);
INSERT INTO `som_chain_script` VALUES (37, 'somplatform', 'check_otp_wm_dise_code_after_basic', '门（急）诊诊断编码（西医）[c01c]基础质控', '\r\nimport com.my.som.model.dataHandle.SomHiInvyBasInfo;\r\nimport com.my.som.settlevalidate.constant.SettleValidateConst;\r\nimport com.my.som.settlevalidate.util.SettleValidateUtil;\r\nimport com.my.som.settlevalidate.vo.SetlValidBaseBusiVo;\r\nimport com.my.som.vo.dataHandle.SettleListHandlerVo;\r\nimport com.my.som.vo.dataHandle.SettleListValidateVo;\r\nimport com.yomahub.liteflow.script.ScriptExecuteWrap;\r\nimport com.yomahub.liteflow.script.body.JaninoCommonScriptBody;\r\nimport org.slf4j.Logger;\r\nimport org.slf4j.LoggerFactory;\r\nimport java.util.Arrays;\r\nimport java.util.List;\r\nimport java.util.Map;\r\n\r\n/**\r\n * 判断 门（急）诊诊断（西医诊断编码）不为空\r\n */\r\npublic class check_otp_wm_dise_code_after_basic implements JaninoCommonScriptBody {\r\n    private static final Logger logger = LoggerFactory.getLogger(check_otp_wm_dise_code_after_basic.class);\r\n\r\n    private static final String MAIN_CHECK_FIELD = \"c01c\";\r\n    private static final String MAIN_CHECK_NAME = \"门（急）诊诊断编码（西医）\";\r\n    private static final List<String> ERROR_CODE_BEGIN_LIST = Arrays.asList(\"V\", \"W\", \"X\", \"Y\");\r\n    private static final String MAIN_CHECK_REGION = \"四川\";\r\n    /**\r\n     * 事后校验\r\n     * 门（急）诊诊断编码（西医）(otp_wm_dise_name)基础质控\r\n     * 判断门（急）诊诊断编码（西医）是否为空\r\n     *\r\n     * @param wrap\r\n     * @return\r\n     */\r\n    @Override\r\n    public Void body(ScriptExecuteWrap wrap) {\r\n        SetlValidBaseBusiVo setlValidBaseBusiVo = (SetlValidBaseBusiVo) wrap.cmp.getFirstContextBean();\r\n        SettleListValidateVo settleListValidateVo = (SettleListValidateVo) wrap.cmp.getRequestData();\r\n        Map<String, String> keysBasicResultMap = setlValidBaseBusiVo.getKeysBasicResultMap();\r\n        SomHiInvyBasInfo somHiInvyBasInfo = settleListValidateVo.getSomHiInvyBasInfo();\r\n        SettleListHandlerVo settleListHandlerVo = new SettleListHandlerVo();\r\n\r\n\r\n        // 检查 somHiInvyBasInfo 是否为空\r\n        if (SettleValidateUtil.isEmpty(somHiInvyBasInfo)) {\r\n            return null;\r\n        }\r\n\r\n        // 获取门（急）诊诊断名称（西医）\r\n        String c01c = somHiInvyBasInfo.getC01c();\r\n        if (SettleValidateUtil.isEmpty(c01c)) {\r\n            keysBasicResultMap.put(MAIN_CHECK_FIELD, SettleValidateConst.NULL);\r\n        } else {\r\n            if(c01c.length() < 5){\r\n                //不在合理值范围，提示\r\n                addErrorDetail(SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_ERROR_TYPE_2,\r\n                        \"门(急)诊诊断编码格式错误\",   MAIN_CHECK_FIELD,\r\n                        SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n            }\r\n            keysBasicResultMap.put(MAIN_CHECK_FIELD, SettleValidateConst.PASS);\r\n        }\r\n        return null;\r\n    }\r\n\r\n    private void addErrorDetail(String index, String errType, String errDscr, String errorFields, String chkStas, SetlValidBaseBusiVo baseBusiVo) {\r\n        SettleListHandlerVo handlerVo = new SettleListHandlerVo();\r\n        handlerVo.setIndex(index);\r\n        handlerVo.setErrDscr(errDscr);\r\n        handlerVo.setErrorFields(errorFields);\r\n        handlerVo.setChkStas(chkStas);\r\n        handlerVo.setErrType(errType);\r\n        baseBusiVo.getErrorDetails().add(handlerVo);\r\n    }\r\n}\r\n', 'script', 'java', '2024-09-24 17:23:21', '门(急)诊(西医)诊断', '住院信息 ', 1, 1);
INSERT INTO `som_chain_script` VALUES (38, 'somplatform', 'check_patn_rlts_basic', '联系人与患者关系[a33c]基础质控', '\r\nimport com.my.som.model.dataHandle.SomHiInvyBasInfo;\r\nimport com.my.som.settlevalidate.constant.SettleValidateConst;\r\nimport com.my.som.settlevalidate.util.SettleValidateUtil;\r\nimport com.my.som.settlevalidate.vo.SetlValidBaseBusiVo;\r\nimport com.my.som.util.SettleListValidateUtil;\r\nimport com.my.som.vo.dataHandle.SettleListHandlerVo;\r\nimport com.my.som.vo.dataHandle.SettleListValidateVo;\r\nimport com.yomahub.liteflow.script.ScriptExecuteWrap;\r\nimport com.yomahub.liteflow.script.body.JaninoCommonScriptBody;\r\nimport org.slf4j.Logger;\r\nimport org.slf4j.LoggerFactory;\r\n\r\nimport java.util.List;\r\nimport java.util.Map;\r\n\r\npublic class check_patn_rlts_basic implements JaninoCommonScriptBody {\r\n    private static final Logger logger = LoggerFactory.getLogger(check_patn_rlts_basic.class);\r\n\r\n    private static final String MAIN_CHECK_FIELD = \"a33c\";\r\n    private static final String MAIN_CHECK_NAME = \"联系人与患者关系\";\r\n    private static final String MAIN_CHECK_REGION = \"四川\";\r\n    /**\r\n     * 联系人与患者关系(patn_rlts)基础质控\r\n     * 1.判断联系人与患者关系是否为空\r\n     * 2.判断是否符合GB/T4761-2008 标准\r\n     * @param wrap\r\n     * @return\r\n     */\r\n    @Override\r\n    public Void body(ScriptExecuteWrap wrap) {\r\n        SetlValidBaseBusiVo setlValidBaseBusiVo = (SetlValidBaseBusiVo) wrap.cmp.getFirstContextBean();\r\n        SettleListValidateVo settleListValidateVo = (SettleListValidateVo) wrap.cmp.getRequestData();\r\n        Map<String, String> keysBasicResultMap = setlValidBaseBusiVo.getKeysBasicResultMap();\r\n        SomHiInvyBasInfo somHiInvyBasInfo = settleListValidateVo.getSomHiInvyBasInfo();\r\n        SettleListHandlerVo settleListHandlerVo = new SettleListHandlerVo();\r\n\r\n        // 检查 somHiInvyBasInfo 是否为空\r\n        if (SettleValidateUtil.isEmpty(somHiInvyBasInfo)) {\r\n            addErrorDetail(settleListHandlerVo, SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY,SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY,\r\n                    MAIN_CHECK_FIELD,\r\n                    (String)SettleValidateConst.CHECK_TYPE_MAP.get(SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY),\r\n                    SettleValidateConst.VALIDATE_STATE_FAIL, setlValidBaseBusiVo);\r\n            return null;\r\n        }\r\n\r\n        // 获取联系人与患者关系\r\n        String a33c = somHiInvyBasInfo.getA33c();\r\n        if (SettleValidateUtil.isEmpty(a33c)) {\r\n            addErrorDetail(settleListHandlerVo, SettleValidateConst.VALIDATE_TYPE_1,SettleValidateConst.VALIDATE_ERROR_TYPE_1,\r\n                    MAIN_CHECK_FIELD,\r\n                    MAIN_CHECK_NAME + \"[\" + a33c + \"]为空\",\r\n                    SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n            keysBasicResultMap.put(MAIN_CHECK_FIELD, SettleValidateConst.NULL);\r\n        } else {\r\n            // 判断是否符合标准字典\r\n            Map<String,Object> map = (Map<String,Object>)setlValidBaseBusiVo.getSettleListParams().get(SettleListValidateUtil.KEY_DICT);\r\n            List<String> jtgx = (List<String>)map.get(\"JTGX\");\r\n            if(!jtgx.contains(a33c)){\r\n                addErrorDetail(settleListHandlerVo, SettleValidateConst.VALIDATE_TYPE_1,SettleValidateConst.VALIDATE_ERROR_TYPE_2,\r\n                        MAIN_CHECK_FIELD,\r\n                        MAIN_CHECK_NAME + \"[\" + a33c + \"]未按照标准字典映射\",\r\n                        SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n                keysBasicResultMap.put(MAIN_CHECK_FIELD, SettleValidateConst.OUTLIER);\r\n            }else{\r\n                keysBasicResultMap.put(MAIN_CHECK_FIELD, SettleValidateConst.PASS);\r\n            }\r\n\r\n        }\r\n        return null;\r\n    }\r\n\r\n    private void addErrorDetail(SettleListHandlerVo handlerVo, String index,String errType,String errorFields, String errDscr, String chkStas, SetlValidBaseBusiVo baseBusiVo) {\r\n        handlerVo.setIndex(index);\r\n        handlerVo.setErrDscr(errDscr);\r\n        handlerVo.setErrorFields(errorFields);\r\n        handlerVo.setChkStas(chkStas);\r\n        handlerVo.setErrType(errType);\r\n        baseBusiVo.getErrorDetails().add(handlerVo);\r\n    }\r\n}\r\n', 'script', 'java', '2024-09-24 17:23:21', '联系人与患者关系', '基本数据', 0, 1);
INSERT INTO `som_chain_script` VALUES (39, 'somplatform', 'check_pjdm_basic', '票据代码[d38]基础质控', '\r\nimport com.my.som.model.dataHandle.SomHiInvyBasInfo;\r\nimport com.my.som.settlevalidate.constant.SettleValidateConst;\r\nimport com.my.som.settlevalidate.util.SettleValidateUtil;\r\nimport com.my.som.settlevalidate.vo.SetlValidBaseBusiVo;\r\nimport com.my.som.vo.dataHandle.SettleListHandlerVo;\r\nimport com.my.som.vo.dataHandle.SettleListValidateVo;\r\nimport com.yomahub.liteflow.script.ScriptExecuteWrap;\r\nimport com.yomahub.liteflow.script.body.JaninoCommonScriptBody;\r\nimport org.slf4j.Logger;\r\nimport org.slf4j.LoggerFactory;\r\n\r\nimport java.util.Map;\r\n\r\npublic class check_pjdm_basic implements JaninoCommonScriptBody {\r\n    private static final Logger logger = LoggerFactory.getLogger(check_pjdm_basic.class);\r\n\r\n    private static final String MAIN_CHECK_FIELD = \"d38\";\r\n    private static final String MAIN_CHECK_NAME = \"票据代码\";\r\n    private static final String MAIN_CHECK_REGION = \"四川\";\r\n\r\n    /**\r\n     * 票据代码-[d38]-基础校验\r\n     * @param wrap\r\n     * @return\r\n     */\r\n    @Override\r\n    public Void body(ScriptExecuteWrap wrap) {\r\n        SetlValidBaseBusiVo setlValidBaseBusiVo = (SetlValidBaseBusiVo) wrap.cmp.getFirstContextBean();\r\n        SettleListValidateVo settleListValidateVo = (SettleListValidateVo) wrap.cmp.getRequestData();\r\n        Map<String, String> keysBasicResultMap = setlValidBaseBusiVo.getKeysBasicResultMap();\r\n        SomHiInvyBasInfo somHiInvyBasInfo = settleListValidateVo.getSomHiInvyBasInfo();\r\n        SettleListHandlerVo settleListHandlerVo = new SettleListHandlerVo();\r\n        if (SettleValidateUtil.isEmpty(somHiInvyBasInfo)) {\r\n            settleListHandlerVo.setIndex(SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY);\r\n            settleListHandlerVo.setErrDscr((String) SettleValidateConst.CHECK_TYPE_MAP.get(SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY));\r\n            settleListHandlerVo.setChkStas(SettleValidateConst.VALIDATE_STATE_FAIL);\r\n            settleListHandlerVo.setErrType(SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY);\r\n            setlValidBaseBusiVo.getErrorDetails().add(settleListHandlerVo);\r\n            return null;\r\n        }\r\n\r\n        String d38 = somHiInvyBasInfo.getD38();\r\n        boolean checkBool = SettleValidateUtil.checkNotNullOrNotBlank(d38,keysBasicResultMap,MAIN_CHECK_FIELD);\r\n        if (checkBool) {\r\n            // 正常值，基础校验通过不写错误信息\r\n            keysBasicResultMap.put(MAIN_CHECK_FIELD, SettleValidateConst.PASS);\r\n        }else{\r\n            SettleValidateUtil.addErrorDetail(settleListHandlerVo, SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_TYPE_1,\r\n                    MAIN_CHECK_NAME + \"[\" + d38 + \"]为空\", MAIN_CHECK_FIELD,\r\n                    SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n            keysBasicResultMap.put(MAIN_CHECK_FIELD, SettleValidateConst.NULL);\r\n        }\r\n        return null;\r\n    }\r\n}\r\n', 'script', 'java', '2024-09-24 17:23:21', '票据代码', '基本数据', 0, 1);
INSERT INTO `som_chain_script` VALUES (40, 'somplatform', 'check_pjhm_basic', '票据号码[d39]基础质控', '\r\nimport com.my.som.model.dataHandle.SomHiInvyBasInfo;\r\nimport com.my.som.settlevalidate.constant.SettleValidateConst;\r\nimport com.my.som.settlevalidate.util.SettleValidateUtil;\r\nimport com.my.som.settlevalidate.vo.SetlValidBaseBusiVo;\r\nimport com.my.som.vo.dataHandle.SettleListHandlerVo;\r\nimport com.my.som.vo.dataHandle.SettleListValidateVo;\r\nimport com.yomahub.liteflow.script.ScriptExecuteWrap;\r\nimport com.yomahub.liteflow.script.body.JaninoCommonScriptBody;\r\nimport org.slf4j.Logger;\r\nimport org.slf4j.LoggerFactory;\r\n\r\nimport java.util.Map;\r\n\r\npublic class check_pjhm_basic implements JaninoCommonScriptBody {\r\n    private static final Logger logger = LoggerFactory.getLogger(check_pjhm_basic.class);\r\n\r\n    private static final String MAIN_CHECK_FIELD = \"d39\";\r\n    private static final String MAIN_CHECK_NAME = \"票据号码\";\r\n    private static final String MAIN_CHECK_REGION = \"四川\";\r\n    /**\r\n     *\r\n     * @param wrap\r\n     * @return\r\n     */\r\n    @Override\r\n    public Void body(ScriptExecuteWrap wrap) {\r\n        SetlValidBaseBusiVo setlValidBaseBusiVo = (SetlValidBaseBusiVo) wrap.cmp.getFirstContextBean();\r\n        SettleListValidateVo settleListValidateVo = (SettleListValidateVo) wrap.cmp.getRequestData();\r\n        Map<String, String> keysBasicResultMap = setlValidBaseBusiVo.getKeysBasicResultMap();\r\n        SomHiInvyBasInfo somHiInvyBasInfo = settleListValidateVo.getSomHiInvyBasInfo();\r\n        SettleListHandlerVo settleListHandlerVo = new SettleListHandlerVo();\r\n        if (SettleValidateUtil.isEmpty(somHiInvyBasInfo)) {\r\n            settleListHandlerVo.setIndex(SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY);\r\n            settleListHandlerVo.setErrDscr((String) SettleValidateConst.CHECK_TYPE_MAP.get(SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY));\r\n            settleListHandlerVo.setChkStas(SettleValidateConst.VALIDATE_STATE_FAIL);\r\n            settleListHandlerVo.setErrType(SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY);\r\n            setlValidBaseBusiVo.getErrorDetails().add(settleListHandlerVo);\r\n            return null;\r\n        }\r\n        String d39 = somHiInvyBasInfo.getD39();\r\n        boolean checkBool = SettleValidateUtil.checkNotNullOrNotBlank(d39,keysBasicResultMap,MAIN_CHECK_FIELD);\r\n        if (checkBool) {\r\n            // 正常值，基础校验通过不写错误信息\r\n            keysBasicResultMap.put(MAIN_CHECK_FIELD, SettleValidateConst.PASS);\r\n        }else{\r\n            SettleValidateUtil.addErrorDetail(settleListHandlerVo, SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_TYPE_1,\r\n                    MAIN_CHECK_NAME + \"[\" + d39 + \"]为空\", MAIN_CHECK_FIELD,\r\n                    SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n            keysBasicResultMap.put(MAIN_CHECK_FIELD, SettleValidateConst.NULL);\r\n        }\r\n        return null;\r\n    }\r\n}\r\n', 'script', 'java', '2024-09-24 17:23:21', '票据号码', '基本数据', 0, 1);
INSERT INTO `som_chain_script` VALUES (41, 'somplatform', 'check_pos_code_after_basic', '工作单位邮政编码[a31c]基础质控', '\r\nimport com.my.som.model.dataHandle.SomHiInvyBasInfo;\r\nimport com.my.som.settlevalidate.constant.SettleValidateConst;\r\nimport com.my.som.settlevalidate.util.SettleValidateUtil;\r\nimport com.my.som.settlevalidate.vo.SetlValidBaseBusiVo;\r\nimport com.my.som.vo.dataHandle.SettleListHandlerVo;\r\nimport com.my.som.vo.dataHandle.SettleListValidateVo;\r\nimport com.yomahub.liteflow.script.ScriptExecuteWrap;\r\nimport com.yomahub.liteflow.script.body.JaninoCommonScriptBody;\r\nimport org.slf4j.Logger;\r\nimport org.slf4j.LoggerFactory;\r\n\r\nimport java.util.Map;\r\n\r\n/**\r\n * 事后 单位邮编\r\n */\r\npublic class check_pos_code_after_basic implements JaninoCommonScriptBody {\r\n    private static final Logger logger = LoggerFactory.getLogger(check_pos_code_after_basic.class);\r\n\r\n    private static final String MAIN_CHECK_FIELD = \"a31c\";\r\n    private static final String MAIN_CHECK_NAME = \"工作单位邮政编码\";\r\n    private static final String MAIN_CHECK_REGION = \"四川\";\r\n\r\n    /**\r\n     * 事后校验\r\n     * 工作单位邮编(pos_code) 基础质控\r\n     * 工作单位邮编是否为空\r\n     * @param wrap\r\n     * @return\r\n     */\r\n    @Override\r\n    public Void body(ScriptExecuteWrap wrap) {\r\n        SetlValidBaseBusiVo setlValidBaseBusiVo = (SetlValidBaseBusiVo) wrap.cmp.getFirstContextBean();\r\n        SettleListValidateVo settleListValidateVo = (SettleListValidateVo) wrap.cmp.getRequestData();\r\n        Map<String, String> keysBasicResultMap = setlValidBaseBusiVo.getKeysBasicResultMap();\r\n        SomHiInvyBasInfo somHiInvyBasInfo = settleListValidateVo.getSomHiInvyBasInfo();\r\n        SettleListHandlerVo settleListHandlerVo = new SettleListHandlerVo();\r\n\r\n        // 检查 somHiInvyBasInfo 是否为空\r\n        if (SettleValidateUtil.isEmpty(somHiInvyBasInfo)) {\r\n            SettleValidateUtil.addErrorDetail(settleListHandlerVo, SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY, SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY,\r\n                    (String) SettleValidateConst.CHECK_TYPE_MAP.get(SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY), null,\r\n                    SettleValidateConst.VALIDATE_STATE_FAIL, setlValidBaseBusiVo);\r\n            return null;\r\n        }\r\n\r\n        // 获取工作单位邮编\r\n        String a31c = somHiInvyBasInfo.getA31c();\r\n        if (SettleValidateUtil.isEmpty(a31c)) {\r\n            keysBasicResultMap.put(MAIN_CHECK_FIELD, SettleValidateConst.NULL);\r\n        } else {\r\n            keysBasicResultMap.put(MAIN_CHECK_FIELD, SettleValidateConst.PASS);\r\n        }\r\n        return null;\r\n    }\r\n}\r\n', 'script', 'java', '2024-09-24 17:23:21', '单位邮编', '基本信息', 1, 1);
INSERT INTO `som_chain_script` VALUES (42, 'somplatform', 'check_prfs_basic', '职业[a38c]基础质控', '\r\nimport com.my.som.model.dataHandle.SomHiInvyBasInfo;\r\nimport com.my.som.settlevalidate.constant.SettleValidateConst;\r\nimport com.my.som.settlevalidate.util.SettleValidateUtil;\r\nimport com.my.som.settlevalidate.vo.SetlValidBaseBusiVo;\r\nimport com.my.som.util.SettleListValidateUtil;\r\nimport com.my.som.vo.dataHandle.SettleListHandlerVo;\r\nimport com.my.som.vo.dataHandle.SettleListValidateVo;\r\nimport com.yomahub.liteflow.script.ScriptExecuteWrap;\r\nimport com.yomahub.liteflow.script.body.JaninoCommonScriptBody;\r\nimport org.slf4j.Logger;\r\nimport org.slf4j.LoggerFactory;\r\n\r\nimport java.util.List;\r\nimport java.util.Map;\r\n\r\npublic class check_prfs_basic implements JaninoCommonScriptBody {\r\n    private static final Logger logger = LoggerFactory.getLogger(check_prfs_basic.class);\r\n\r\n    private static final String MAIN_CHECK_FIELD = \"a38c\";\r\n    private static final String MAIN_CHECK_NAME = \"职业\";\r\n    private static final String MAIN_CHECK_REGION = \"四川\";\r\n    /**\r\n     * 职业(prfs)基础质控\r\n     * 1.判断职业是否为空\r\n     * 2.判断是否符合GB/T2261.4-2003 标准\r\n     * @param wrap\r\n     * @return\r\n     */\r\n    @Override\r\n    public Void body(ScriptExecuteWrap wrap) {\r\n        SetlValidBaseBusiVo setlValidBaseBusiVo = (SetlValidBaseBusiVo) wrap.cmp.getFirstContextBean();\r\n        SettleListValidateVo settleListValidateVo = (SettleListValidateVo) wrap.cmp.getRequestData();\r\n        Map<String, String> keysBasicResultMap = setlValidBaseBusiVo.getKeysBasicResultMap();\r\n        SomHiInvyBasInfo somHiInvyBasInfo = settleListValidateVo.getSomHiInvyBasInfo();\r\n        SettleListHandlerVo settleListHandlerVo = new SettleListHandlerVo();\r\n\r\n        // 检查 somHiInvyBasInfo 是否为空\r\n        if (SettleValidateUtil.isEmpty(somHiInvyBasInfo)) {\r\n            addErrorDetail(settleListHandlerVo, SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY,SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY,\r\n                    MAIN_CHECK_FIELD,\r\n                    (String)SettleValidateConst.CHECK_TYPE_MAP.get(SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY),\r\n                    SettleValidateConst.VALIDATE_STATE_FAIL, setlValidBaseBusiVo);\r\n            return null;\r\n        }\r\n\r\n        // 获取职业\r\n        String a38c = somHiInvyBasInfo.getA38c();\r\n        if (SettleValidateUtil.isEmpty(a38c)) {\r\n            addErrorDetail(settleListHandlerVo, SettleValidateConst.VALIDATE_TYPE_1,SettleValidateConst.VALIDATE_ERROR_TYPE_1,\r\n                    MAIN_CHECK_FIELD,\r\n                    MAIN_CHECK_NAME + \"[\" + a38c + \"]为空\",\r\n                    SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n            keysBasicResultMap.put(MAIN_CHECK_FIELD, SettleValidateConst.NULL);\r\n        } else {\r\n            // 判断是否符合标准字典\r\n            Map<String,Object> map = (Map<String,Object>)setlValidBaseBusiVo.getSettleListParams().get(SettleListValidateUtil.KEY_DICT);\r\n            List<String> zy = (List<String>)map.get(\"ZY\");\r\n            if(!zy.contains(a38c)){\r\n                addErrorDetail(settleListHandlerVo, SettleValidateConst.VALIDATE_TYPE_1,SettleValidateConst.VALIDATE_ERROR_TYPE_2,\r\n                        MAIN_CHECK_FIELD,\r\n                        MAIN_CHECK_NAME + \"[\" + a38c + \"]未按照标准字典映射\",\r\n                        SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n                keysBasicResultMap.put(MAIN_CHECK_FIELD, SettleValidateConst.OUTLIER);\r\n            }else{\r\n                keysBasicResultMap.put(MAIN_CHECK_FIELD, SettleValidateConst.PASS);\r\n            }\r\n\r\n        }\r\n\r\n        return null;\r\n    }\r\n\r\n    private void addErrorDetail(SettleListHandlerVo handlerVo, String index,String errType,String errorFields, String errDscr, String chkStas, SetlValidBaseBusiVo baseBusiVo) {\r\n        handlerVo.setIndex(index);\r\n        handlerVo.setErrDscr(errDscr);\r\n        handlerVo.setErrorFields(errorFields);\r\n        handlerVo.setChkStas(chkStas);\r\n        handlerVo.setErrType(errType);\r\n        baseBusiVo.getErrorDetails().add(handlerVo);\r\n    }\r\n}\r\n', 'script', 'java', '2024-09-24 17:23:21', '职业', '基本数据', 0, 1);
INSERT INTO `som_chain_script` VALUES (43, 'somplatform', 'check_refl_dept_dept_after_basic', '转科科别[b21c]基础质控', 'import com.my.som.model.dataHandle.SomHiInvyBasInfo;\r\nimport com.my.som.settlevalidate.constant.SettleValidateConst;\r\nimport com.my.som.settlevalidate.util.SettleValidateUtil;\r\nimport com.my.som.settlevalidate.vo.SetlValidBaseBusiVo;\r\nimport com.my.som.util.SettleListValidateUtil;\r\nimport com.my.som.vo.dataHandle.SettleListHandlerVo;\r\nimport com.my.som.vo.dataHandle.SettleListValidateVo;\r\nimport com.yomahub.liteflow.script.ScriptExecuteWrap;\r\nimport com.yomahub.liteflow.script.body.JaninoCommonScriptBody;\r\nimport org.slf4j.Logger;\r\nimport org.slf4j.LoggerFactory;\r\n\r\nimport java.util.Arrays;\r\nimport java.util.List;\r\nimport java.util.Map;\r\n\r\n/**\r\n * 判断 转科科别不为空\r\n */\r\npublic class check_refl_dept_dept_after_basic implements JaninoCommonScriptBody {\r\n    private static final Logger logger = LoggerFactory.getLogger(check_refl_dept_dept_after_basic.class);\r\n\r\n    private static final String MAIN_CHECK_FIELD = \"b21c\";\r\n    private static final String MAIN_CHECK_NAME = \"转科科别\";\r\n    private static final String MAIN_CHECK_REGION = \"四川,湖北\";\r\n    /**\r\n     * 事后 校验\r\n     * 转科科别(refldept_dept) 基础质控\r\n     * 判断转科科别是否为空\r\n     *\r\n     * @param wrap\r\n     * @return\r\n     */\r\n    @Override\r\n    public Void body(ScriptExecuteWrap wrap) {\r\n        SetlValidBaseBusiVo setlValidBaseBusiVo = (SetlValidBaseBusiVo) wrap.cmp.getFirstContextBean();\r\n        SettleListValidateVo settleListValidateVo = (SettleListValidateVo) wrap.cmp.getRequestData();\r\n        Map<String, String> keysBasicResultMap = setlValidBaseBusiVo.getKeysBasicResultMap();\r\n        SomHiInvyBasInfo somHiInvyBasInfo = settleListValidateVo.getSomHiInvyBasInfo();\r\n        SettleListHandlerVo settleListHandlerVo = new SettleListHandlerVo();\r\n\r\n        // 检查 somHiInvyBasInfo 是否为空\r\n        if (SettleValidateUtil.isEmpty(somHiInvyBasInfo)) {\r\n            addErrorDetail(SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY, SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY,\r\n                    (String) SettleValidateConst.CHECK_TYPE_MAP.get(SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY), null,\r\n                    SettleValidateConst.VALIDATE_STATE_FAIL, setlValidBaseBusiVo);\r\n            return null;\r\n        }\r\n\r\n        // 获取转科科别\r\n        String b21c = somHiInvyBasInfo.getB21c();\r\n\r\n        if (SettleValidateUtil.isNotEmpty(b21c)) {\r\n\r\n            //获取出院科别\r\n            // 获取科室编码\r\n            List<String> dscgCatyCodeList = (List<String>) setlValidBaseBusiVo.getSettleListParams().get(SettleListValidateUtil.KEY_DEPT_CODE);\r\n            // 获取诊疗类别\r\n            List<String> trtTypeList = (List<String>) ((Map<String, Object>) setlValidBaseBusiVo.getSettleListParams().get(SettleListValidateUtil.KEY_DICT)).get(\"ZLLB\");\r\n\r\n            //说明此时转科有多个\r\n            List<String> list = Arrays.asList(b21c.split(\"→\"));\r\n            for (int i = 0; i < list.size(); i++) {\r\n                //转科科别 不符合《医疗卫生机构业务科室分类与代码》(CT 08.00.002)里的代码或诊疗科目两项中的其中一项\r\n                if (!dscgCatyCodeList.contains(list.get(i)) && !trtTypeList.contains(list.get(i))) {\r\n                    addErrorDetail(SettleValidateConst.VALIDATE_ERROR_TYPE_2, SettleValidateConst.VALIDATE_TYPE_1,\r\n                            MAIN_CHECK_NAME + \"为[\" + b21c + \"], 其中第\" + (i + 1) + \"个转科科别[\"+list.get(i)+\"]不符合科别规范，请查看连接符 是否为 → \", MAIN_CHECK_FIELD,\r\n                            SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n                    keysBasicResultMap.put(MAIN_CHECK_FIELD, SettleValidateConst.OUTLIER);\r\n                } else {\r\n                    keysBasicResultMap.put(MAIN_CHECK_FIELD, SettleValidateConst.PASS);\r\n                }\r\n            }\r\n        }\r\n        return null;\r\n    }\r\n\r\n    private void addErrorDetail(String index, String errType, String errDscr, String errorFields, String chkStas, SetlValidBaseBusiVo baseBusiVo) {\r\n        SettleListHandlerVo handlerVo = new SettleListHandlerVo();\r\n        handlerVo.setIndex(index);\r\n        handlerVo.setErrDscr(errDscr);\r\n        handlerVo.setErrorFields(errorFields);\r\n        handlerVo.setChkStas(chkStas);\r\n        handlerVo.setErrType(errType);\r\n        baseBusiVo.getErrorDetails().add(handlerVo);\r\n    }\r\n}\r\n\r\n', 'script', 'java', '2024-09-24 17:23:21', '转科科别编码', '基础信息', 1, 1);
INSERT INTO `som_chain_script` VALUES (44, 'somplatform', 'check_scs_cutd_ward_type_after_basic', '重症监护病房类型[scsCutdWardType]基础质控', 'import com.my.som.model.dataHandle.SomHiInvyBasInfo;\r\nimport com.my.som.model.medicalQuality.SomSetlInvyScsCutdInfo;\r\nimport com.my.som.settlevalidate.constant.SettleValidateConst;\r\nimport com.my.som.settlevalidate.util.SettleValidateUtil;\r\nimport com.my.som.settlevalidate.vo.SetlValidBaseBusiVo;\r\nimport com.my.som.util.SettleListValidateUtil;\r\nimport com.my.som.vo.dataHandle.SettleListHandlerVo;\r\nimport com.my.som.vo.dataHandle.SettleListValidateVo;\r\nimport com.yomahub.liteflow.script.ScriptExecuteWrap;\r\nimport com.yomahub.liteflow.script.body.JaninoCommonScriptBody;\r\nimport org.slf4j.Logger;\r\nimport org.slf4j.LoggerFactory;\r\n\r\nimport java.util.List;\r\nimport java.util.Map;\r\n\r\n/**\r\n * 事后判断 重症监护室\r\n */\r\npublic class check_scs_cutd_ward_type_after_basic implements JaninoCommonScriptBody {\r\n    private static final Logger logger = LoggerFactory.getLogger(check_scs_cutd_ward_type_after_basic.class);\r\n\r\n    private static final String MAIN_CHECK_FIELD = \"scsCutdWardType\";\r\n    private static final String MAIN_CHECK_NAME = \"重症监护病房类型\";\r\n    private static final String MAIN_CHECK_REGION = \"四川\";\r\n\r\n    private static final String MAIN_CHECK_FIELD_1 = \"scsCutdInpoolTime\";\r\n    private static final String MAIN_CHECK_NAME_1 = \"重症监护进入时间\";\r\n    private static final String MAIN_CHECK_FIELD_2 = \"scsCutdExitTime\";\r\n    private static final String MAIN_CHECK_NAME_2 = \"重症监护退出时间\";\r\n    /**\r\n     * 重症监护病房类型(scs_cutd_ward_type)基础质控\r\n     * 判断入院时间是否为空\r\n     * @param wrap\r\n     * @return\r\n     */\r\n    @Override\r\n    public Void body(ScriptExecuteWrap wrap) {\r\n        SetlValidBaseBusiVo setlValidBaseBusiVo = (SetlValidBaseBusiVo) wrap.cmp.getFirstContextBean();\r\n        SettleListValidateVo settleListValidateVo = (SettleListValidateVo) wrap.cmp.getRequestData();\r\n        Map<String, String> keysBasicResultMap = setlValidBaseBusiVo.getKeysBasicResultMap();\r\n        SomHiInvyBasInfo somHiInvyBasInfo = settleListValidateVo.getSomHiInvyBasInfo();\r\n        SettleListHandlerVo settleListHandlerVo = new SettleListHandlerVo();\r\n        List<SomSetlInvyScsCutdInfo> busIcuList = settleListValidateVo.getBusIcuList();\r\n        // 检查 somHiInvyBasInfo 是否为空\r\n        if (SettleValidateUtil.isEmpty(busIcuList)) {\r\n            return null;\r\n        }\r\n\r\n        for (int i = 0; i < busIcuList.size(); i++) {\r\n            SomSetlInvyScsCutdInfo somSetlInvyScsCutdInfo = (SomSetlInvyScsCutdInfo) busIcuList.get(i);\r\n\r\n            if(SettleValidateUtil.isNotEmpty(somSetlInvyScsCutdInfo.getScsCutdWardType())){\r\n                //判断 重症监护病房类型是否 合理\r\n                Map<String,Object> map = (Map<String,Object>)setlValidBaseBusiVo.getSettleListParams().get(SettleListValidateUtil.KEY_DICT);\r\n                List<String> scsCutdWardTypeList = (List<String>)map.get(\"ZZJHBFLX\");\r\n                if(!scsCutdWardTypeList.contains(somSetlInvyScsCutdInfo.getScsCutdWardType())){\r\n                    addErrorDetail(SettleValidateConst.VALIDATE_TYPE_1,SettleValidateConst.VALIDATE_ERROR_TYPE_2,\r\n                            MAIN_CHECK_FIELD,\r\n                            \"第\" + (i + 1) + \"个重症监护: id为[\" + somSetlInvyScsCutdInfo.getId() + \"]的\" +MAIN_CHECK_NAME + \"[\" + somSetlInvyScsCutdInfo.getScsCutdWardType() + \"]不符合规范\",\r\n                            SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n                    keysBasicResultMap.put(i+MAIN_CHECK_FIELD, SettleValidateConst.OUTLIER);\r\n                }\r\n\r\n                //进入时间\r\n                if(SettleValidateUtil.isNotEmpty(somSetlInvyScsCutdInfo.getScsCutdInpoolTime())){\r\n                    keysBasicResultMap.put(i+MAIN_CHECK_FIELD_1, SettleValidateConst.PASS);\r\n                }else{\r\n                    addErrorDetail(SettleValidateConst.VALIDATE_TYPE_1,SettleValidateConst.VALIDATE_ERROR_TYPE_1,\r\n                            MAIN_CHECK_FIELD_1,\r\n                            \"第\" + (i + 1) + \"个重症监护: id为[\" + somSetlInvyScsCutdInfo.getId() + \"]的\" +MAIN_CHECK_NAME_1 + \"[\" + MAIN_CHECK_FIELD_1 + \"]为空\",\r\n                            SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n                    keysBasicResultMap.put(i+MAIN_CHECK_FIELD_1, SettleValidateConst.NULL);\r\n                }\r\n                //退出时间\r\n                if(SettleValidateUtil.isNotEmpty(somSetlInvyScsCutdInfo.getScsCutdExitTime())){\r\n                    keysBasicResultMap.put(i+MAIN_CHECK_FIELD_2, SettleValidateConst.PASS);\r\n                }else{\r\n                    addErrorDetail(SettleValidateConst.VALIDATE_TYPE_1,SettleValidateConst.VALIDATE_ERROR_TYPE_1,\r\n                            MAIN_CHECK_FIELD_2,\r\n                            \"第\" + (i + 1) + \"个重症监护: id为[\" + somSetlInvyScsCutdInfo.getId() + \"]的\" +MAIN_CHECK_NAME_2 + \"[\" + MAIN_CHECK_FIELD_2 + \"]为空\",\r\n                            SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n                    keysBasicResultMap.put(i+MAIN_CHECK_FIELD_2, SettleValidateConst.NULL);\r\n                }\r\n            }\r\n\r\n        }\r\n        return null;\r\n    }\r\n    private void addErrorDetail( String index,String errType,String errorFields, String errDscr, String chkStas, SetlValidBaseBusiVo baseBusiVo) {\r\n        SettleListHandlerVo handlerVo =new SettleListHandlerVo();\r\n        handlerVo.setIndex(index);\r\n        handlerVo.setErrDscr(errDscr);\r\n        handlerVo.setErrorFields(errorFields);\r\n        handlerVo.setChkStas(chkStas);\r\n        handlerVo.setErrType(errType);\r\n        baseBusiVo.getErrorDetails().add(handlerVo);\r\n    }\r\n}', 'script', 'java', '2024-09-24 17:23:21', '出重症监护室时间', '住院信息 ', 1, 1);
INSERT INTO `som_chain_script` VALUES (45, 'somplatform', 'check_setl_date_after_basic', '结算时间[d36]基础质控', '\r\nimport com.my.som.model.dataHandle.SomHiInvyBasInfo;\r\nimport com.my.som.settlevalidate.constant.SettleValidateConst;\r\nimport com.my.som.settlevalidate.util.SettleValidateUtil;\r\nimport com.my.som.settlevalidate.vo.SetlValidBaseBusiVo;\r\nimport com.my.som.vo.dataHandle.SettleListHandlerVo;\r\nimport com.my.som.vo.dataHandle.SettleListValidateVo;\r\nimport com.yomahub.liteflow.script.ScriptExecuteWrap;\r\nimport com.yomahub.liteflow.script.body.JaninoCommonScriptBody;\r\nimport org.slf4j.Logger;\r\nimport org.slf4j.LoggerFactory;\r\n\r\nimport java.time.LocalDateTime;\r\nimport java.time.format.DateTimeFormatter;\r\nimport java.time.format.DateTimeParseException;\r\nimport java.util.Map;\r\n\r\n/**\r\n * 判断 结算日期不为空\r\n */\r\npublic class check_setl_date_after_basic implements JaninoCommonScriptBody {\r\n    private static final Logger logger = LoggerFactory.getLogger(check_setl_date_after_basic.class);\r\n\r\n    private static final String MAIN_CHECK_FIELD = \"d36\";\r\n    private static final String MAIN_CHECK_NAME = \"结算时间\";\r\n    private static final String MAIN_CHECK_REGION = \"四川\";\r\n    private static final String MAIN_CHECK_FIELD_1 = \"d37\";\r\n    private static final String MAIN_CHECK_NAME_1 = \"结算结束时间\";\r\n\r\n    private static final String MAIN_CHECK_FIELD_2 = \"d36\";\r\n    private static final String MAIN_CHECK_NAME_2 = \"结算开始时间\";\r\n\r\n    /**\r\n     * 事后校验\r\n     * 结算结束时间(setl_date)基础质控\r\n     * 判断结算时间是否为空\r\n     *\r\n     * @param wrap\r\n     * @return\r\n     */\r\n    @Override\r\n    public Void body(ScriptExecuteWrap wrap) {\r\n        SetlValidBaseBusiVo setlValidBaseBusiVo = (SetlValidBaseBusiVo) wrap.cmp.getFirstContextBean();\r\n        SettleListValidateVo settleListValidateVo = (SettleListValidateVo) wrap.cmp.getRequestData();\r\n        Map<String, String> keysBasicResultMap = setlValidBaseBusiVo.getKeysBasicResultMap();\r\n        SomHiInvyBasInfo somHiInvyBasInfo = settleListValidateVo.getSomHiInvyBasInfo();\r\n        SettleListHandlerVo settleListHandlerVo = new SettleListHandlerVo();\r\n\r\n        // 检查 somHiInvyBasInfo 是否为空\r\n        if (SettleValidateUtil.isEmpty(somHiInvyBasInfo)) {\r\n            addErrorDetail(settleListHandlerVo, SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY, SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY,\r\n                    (String) SettleValidateConst.CHECK_TYPE_MAP.get(SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY), null,\r\n                    SettleValidateConst.VALIDATE_STATE_FAIL, setlValidBaseBusiVo);\r\n            return null;\r\n        }\r\n\r\n        // 获取结算 结束时间\r\n        String d37 = somHiInvyBasInfo.getD37();\r\n        if (SettleValidateUtil.isEmpty(d37)) {\r\n            addErrorDetail(settleListHandlerVo, SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_TYPE_1,\r\n                    MAIN_CHECK_NAME_1 + \"[\" + d37 + \"]为空\",\r\n                    MAIN_CHECK_FIELD_1,\r\n                    SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n            keysBasicResultMap.put(MAIN_CHECK_FIELD_1, SettleValidateConst.NULL);\r\n        } else {\r\n            if (!isValidDateTime(d37)) {\r\n                addErrorDetail(settleListHandlerVo, SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_ERROR_TYPE_2,\r\n                        MAIN_CHECK_NAME_1 + \"[\" + d37 + \"]时间格式错误\",\r\n                        MAIN_CHECK_FIELD_1,\r\n                        SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n                keysBasicResultMap.put(MAIN_CHECK_FIELD_1, SettleValidateConst.OUTLIER);\r\n            } else {\r\n                keysBasicResultMap.put(MAIN_CHECK_FIELD_1, SettleValidateConst.PASS);\r\n            }\r\n        }\r\n\r\n        // 获取结算开始日期\r\n        String d36 = somHiInvyBasInfo.getD36();\r\n        if (SettleValidateUtil.isEmpty(d36)) {\r\n            addErrorDetail(settleListHandlerVo, SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_TYPE_1,\r\n                    MAIN_CHECK_NAME_2 + \"[\" + d36 + \"]为空\", MAIN_CHECK_FIELD_2,\r\n                    SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n            keysBasicResultMap.put(MAIN_CHECK_FIELD_2, SettleValidateConst.NULL);\r\n        } else {\r\n            if (!isValidDateTime(d36)) {\r\n                addErrorDetail(settleListHandlerVo, SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_ERROR_TYPE_2,\r\n                        MAIN_CHECK_NAME_2 + \"[\" + d36 + \"]时间格式错误\", MAIN_CHECK_FIELD_2,\r\n                        SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n                keysBasicResultMap.put(MAIN_CHECK_FIELD_2, SettleValidateConst.OUTLIER);\r\n            } else {\r\n                keysBasicResultMap.put(MAIN_CHECK_FIELD_2, SettleValidateConst.PASS);\r\n            }\r\n        }\r\n        return null;\r\n\r\n    }\r\n\r\n    public static boolean isValidDateTime(String dateTimeStr) {\r\n        String format = \"yyyy-MM-dd HH:mm:ss\";\r\n        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(format);\r\n        try {\r\n            LocalDateTime.parse(dateTimeStr, formatter);\r\n            return true;\r\n        } catch (DateTimeParseException e) {\r\n            return false;\r\n        }\r\n    }\r\n\r\n    private void addErrorDetail(SettleListHandlerVo handlerVo, String index, String errType, String errDscr, String errorFields, String chkStas, SetlValidBaseBusiVo baseBusiVo) {\r\n        handlerVo.setIndex(index);\r\n        handlerVo.setErrDscr(errDscr);\r\n        handlerVo.setErrorFields(errorFields);\r\n        handlerVo.setChkStas(chkStas);\r\n        handlerVo.setErrType(errType);\r\n        baseBusiVo.getErrorDetails().add(handlerVo);\r\n    }\r\n}\r\n', 'script', 'java', '2024-09-24 17:23:21', '结算日期', '基础信息', 1, 1);
INSERT INTO `som_chain_script` VALUES (46, 'somplatform', 'check_trt_type_after_basic', '治疗类别[b39]基础质控', 'import com.my.som.model.dataHandle.SomHiInvyBasInfo;\r\nimport com.my.som.settlevalidate.constant.SettleValidateConst;\r\nimport com.my.som.settlevalidate.util.SettleValidateUtil;\r\nimport com.my.som.settlevalidate.vo.SetlValidBaseBusiVo;\r\nimport com.my.som.vo.dataHandle.SettleListHandlerVo;\r\nimport com.my.som.vo.dataHandle.SettleListValidateVo;\r\nimport com.yomahub.liteflow.script.ScriptExecuteWrap;\r\nimport com.yomahub.liteflow.script.body.JaninoCommonScriptBody;\r\nimport org.slf4j.Logger;\r\nimport org.slf4j.LoggerFactory;\r\n\r\nimport java.util.Arrays;\r\nimport java.util.List;\r\nimport java.util.Map;\r\n\r\n/**\r\n * 判断 治疗类别不为空\r\n */\r\npublic class check_trt_type_after_basic implements JaninoCommonScriptBody {\r\n    private static final Logger logger = LoggerFactory.getLogger(check_trt_type_after_basic.class);\r\n\r\n    private static final String MAIN_CHECK_FIELD = \"b39\";\r\n    private static final String MAIN_CHECK_NAME = \"治疗类别\";\r\n    private static final String MAIN_CHECK_REGION = \"四川,湖北\";\r\n    //国家码以及省码治疗类别\r\n    private static final List<String> STANDARD_TREATMENT_CATEGORY = Arrays.asList(\"1\",\"2.1\",\"2.2\",\"3\",\"10\",\"21\",\"22\",\"30\");\r\n\r\n\r\n    /**\r\n     * 事后 校验\r\n     * 治疗类别(trt_type) 基础质控\r\n     * 判断治疗类别是否为空\r\n     *\r\n     * @param wrap\r\n     * @return\r\n     */\r\n    @Override\r\n    public Void body(ScriptExecuteWrap wrap) {\r\n        SetlValidBaseBusiVo setlValidBaseBusiVo = (SetlValidBaseBusiVo) wrap.cmp.getFirstContextBean();\r\n        SettleListValidateVo settleListValidateVo = (SettleListValidateVo) wrap.cmp.getRequestData();\r\n        Map<String, String> keysBasicResultMap = setlValidBaseBusiVo.getKeysBasicResultMap();\r\n        SomHiInvyBasInfo somHiInvyBasInfo = settleListValidateVo.getSomHiInvyBasInfo();\r\n        SettleListHandlerVo settleListHandlerVo = new SettleListHandlerVo();\r\n\r\n        // 检查 somHiInvyBasInfo 是否为空\r\n        if (SettleValidateUtil.isEmpty(somHiInvyBasInfo)) {\r\n            SettleValidateUtil.addErrorDetail(settleListHandlerVo, SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY, SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY,\r\n                    (String) SettleValidateConst.CHECK_TYPE_MAP.get(SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY), null,\r\n                    SettleValidateConst.VALIDATE_STATE_FAIL, setlValidBaseBusiVo);\r\n            return null;\r\n        }\r\n        // 获取医保类型\r\n        String b39 = somHiInvyBasInfo.getB39();\r\n        if (SettleValidateUtil.isEmpty(b39)) {\r\n            SettleValidateUtil.addErrorDetail(settleListHandlerVo, SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_TYPE_1,\r\n                    MAIN_CHECK_NAME + \"[\" + b39 + \"]为空\",\r\n                    MAIN_CHECK_FIELD,\r\n                    SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n            keysBasicResultMap.put(MAIN_CHECK_FIELD, SettleValidateConst.NULL);\r\n        } else {\r\n            //判断是否符合国家或省码的标准码\r\n            if(STANDARD_TREATMENT_CATEGORY.contains(b39)){\r\n                keysBasicResultMap.put(MAIN_CHECK_FIELD, SettleValidateConst.PASS);\r\n            }else {\r\n                SettleValidateUtil.addErrorDetail(settleListHandlerVo, SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_TYPE_1,\r\n                        MAIN_CHECK_NAME + \"[\" + b39 + \"]不符合国家码或省码的治疗类别\",\r\n                        MAIN_CHECK_FIELD,\r\n                        SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n                keysBasicResultMap.put(MAIN_CHECK_FIELD, SettleValidateConst.OUTLIER);\r\n            }\r\n        }\r\n        return null;\r\n    }\r\n}\r\n\r\n', 'script', 'java', '2024-09-24 17:23:21', '治疗类别', '基础信息 ', 1, 1);
INSERT INTO `som_chain_script` VALUES (47, 'somplatform', 'check_ywlsh_basic', '业务流水号[d35]基础质控', '\r\nimport com.my.som.model.dataHandle.SomHiInvyBasInfo;\r\nimport com.my.som.settlevalidate.constant.SettleValidateConst;\r\nimport com.my.som.settlevalidate.util.SettleValidateUtil;\r\nimport com.my.som.settlevalidate.vo.SetlValidBaseBusiVo;\r\nimport com.my.som.vo.dataHandle.SettleListHandlerVo;\r\nimport com.my.som.vo.dataHandle.SettleListValidateVo;\r\nimport com.yomahub.liteflow.script.ScriptExecuteWrap;\r\nimport com.yomahub.liteflow.script.body.JaninoCommonScriptBody;\r\nimport org.slf4j.Logger;\r\nimport org.slf4j.LoggerFactory;\r\n\r\nimport java.util.Map;\r\n\r\npublic class check_ywlsh_basic implements JaninoCommonScriptBody {\r\n    private static final Logger logger = LoggerFactory.getLogger(check_ywlsh_basic.class);\r\n\r\n    private static final String MAIN_CHECK_FIELD = \"d35\";\r\n    private static final String MAIN_CHECK_NAME = \"业务流水号\";\r\n    private static final String MAIN_CHECK_REGION = \"四川\";\r\n    /**\r\n     *\r\n     * @param wrap\r\n     * @return\r\n     */\r\n    @Override\r\n    public Void body(ScriptExecuteWrap wrap) {\r\n        SetlValidBaseBusiVo setlValidBaseBusiVo = (SetlValidBaseBusiVo) wrap.cmp.getFirstContextBean();\r\n        SettleListValidateVo settleListValidateVo = (SettleListValidateVo) wrap.cmp.getRequestData();\r\n        Map<String, String> keysBasicResultMap = setlValidBaseBusiVo.getKeysBasicResultMap();\r\n        SomHiInvyBasInfo somHiInvyBasInfo = settleListValidateVo.getSomHiInvyBasInfo();\r\n        SettleListHandlerVo settleListHandlerVo = new SettleListHandlerVo();\r\n\r\n        if (SettleValidateUtil.isEmpty(somHiInvyBasInfo)) {\r\n            addErrorDetail(settleListHandlerVo, SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY, SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY,\r\n                    (String) SettleValidateConst.CHECK_TYPE_MAP.get(SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY), MAIN_CHECK_FIELD,\r\n                    SettleValidateConst.VALIDATE_STATE_FAIL, setlValidBaseBusiVo);\r\n            return null;\r\n        }\r\n\r\n        String d35 = somHiInvyBasInfo.getD35();\r\n       if (SettleValidateUtil.isEmpty(d35)) {\r\n           addErrorDetail(settleListHandlerVo, SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_TYPE_1,\r\n                   MAIN_CHECK_NAME + \"[\" + d35 + \"]为空\", MAIN_CHECK_FIELD,\r\n                   SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n           keysBasicResultMap.put(MAIN_CHECK_FIELD, SettleValidateConst.NULL);\r\n        }else{\r\n           keysBasicResultMap.put(MAIN_CHECK_FIELD, SettleValidateConst.PASS);\r\n        }\r\n        return null;\r\n    }\r\n\r\n    private void addErrorDetail(SettleListHandlerVo handlerVo, String index, String errType, String errDscr, String errorFields, String chkStas, SetlValidBaseBusiVo baseBusiVo) {\r\n        handlerVo.setIndex(index);\r\n        handlerVo.setErrDscr(errDscr);\r\n        handlerVo.setErrorFields(errorFields);\r\n        handlerVo.setChkStas(chkStas);\r\n        handlerVo.setErrType(errType);\r\n        baseBusiVo.getErrorDetails().add(handlerVo);\r\n    }\r\n}\r\n', 'script', 'java', '2024-09-24 17:23:21', '业务流水号', '基本数据', 0, 1);
INSERT INTO `som_chain_script` VALUES (48, 'somplatform', 'check_zrhsdm_basic', '责任护士代码[b26c]基础质控', '\r\nimport com.my.som.model.dataHandle.SomHiInvyBasInfo;\r\nimport com.my.som.settlevalidate.constant.SettleValidateConst;\r\nimport com.my.som.settlevalidate.util.SettleValidateUtil;\r\nimport com.my.som.settlevalidate.vo.SetlValidBaseBusiVo;\r\nimport com.my.som.util.SettleListValidateUtil;\r\nimport com.my.som.vo.dataHandle.SettleListHandlerVo;\r\nimport com.my.som.vo.dataHandle.SettleListValidateVo;\r\nimport com.yomahub.liteflow.script.ScriptExecuteWrap;\r\nimport com.yomahub.liteflow.script.body.JaninoCommonScriptBody;\r\nimport org.slf4j.Logger;\r\nimport org.slf4j.LoggerFactory;\r\n\r\nimport java.util.List;\r\nimport java.util.Map;\r\n\r\npublic class check_zrhsdm_basic implements JaninoCommonScriptBody {\r\n    private static final Logger logger = LoggerFactory.getLogger(check_zrhsdm_basic.class);\r\n\r\n    private static final String MAIN_CHECK_FIELD = \"b26c\";\r\n    private static final String MAIN_CHECK_NAME = \"责任护士代码\";\r\n    private static final String MAIN_CHECK_REGION = \"四川\";\r\n    /**\r\n     *\r\n     * @param wrap\r\n     * @return\r\n     */\r\n    @Override\r\n    public Void body(ScriptExecuteWrap wrap) {\r\n        SetlValidBaseBusiVo setlValidBaseBusiVo = (SetlValidBaseBusiVo) wrap.cmp.getFirstContextBean();\r\n        SettleListValidateVo settleListValidateVo = (SettleListValidateVo) wrap.cmp.getRequestData();\r\n        Map<String, String> keysBasicResultMap = setlValidBaseBusiVo.getKeysBasicResultMap();\r\n        SomHiInvyBasInfo somHiInvyBasInfo = settleListValidateVo.getSomHiInvyBasInfo();\r\n        SettleListHandlerVo settleListHandlerVo = new SettleListHandlerVo();\r\n        if (SettleValidateUtil.isEmpty(somHiInvyBasInfo)) {\r\n            addErrorDetail(settleListHandlerVo, SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY, SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY,\r\n                    (String) SettleValidateConst.CHECK_TYPE_MAP.get(SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY), MAIN_CHECK_FIELD,\r\n                    SettleValidateConst.VALIDATE_STATE_FAIL, setlValidBaseBusiVo);\r\n            return null;\r\n        }\r\n\r\n        //获取责任护士代码\r\n        String b26c = somHiInvyBasInfo.getB26c();\r\n        boolean checkBool = SettleValidateUtil.checkNotNullOrNotBlank(b26c,keysBasicResultMap,MAIN_CHECK_FIELD);\r\n        if (checkBool) {\r\n            Map<String,Object> map = (Map<String,Object>)(setlValidBaseBusiVo.getSettleListParams().get(SettleListValidateUtil.KEY_NURSE_CODE));\r\n\r\n            Map<String,Object> NurseCodeMap = (Map<String,Object>)map.get(somHiInvyBasInfo.getHospitalId());\r\n\r\n\r\n            if (NurseCodeMap.containsKey(b26c)){\r\n                keysBasicResultMap.put(MAIN_CHECK_FIELD, SettleValidateConst.PASS);\r\n            }else {\r\n                addErrorDetail(settleListHandlerVo, SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_TYPE_1,\r\n                        MAIN_CHECK_NAME + \"[\" + b26c + \"] 不是医院护士代码\", MAIN_CHECK_FIELD,\r\n                        SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n                keysBasicResultMap.put(MAIN_CHECK_FIELD, SettleValidateConst.OUTLIER);\r\n            }\r\n        }\r\n        else {\r\n            addErrorDetail(settleListHandlerVo, SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_TYPE_1,\r\n                    MAIN_CHECK_NAME + \"[\" + b26c + \"]为空\", MAIN_CHECK_FIELD,\r\n                    SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n            keysBasicResultMap.put(MAIN_CHECK_FIELD, SettleValidateConst.NULL);\r\n        }\r\n        return null;\r\n    }\r\n\r\n    private void addErrorDetail(SettleListHandlerVo handlerVo, String index, String errType, String errDscr, String errorFields, String chkStas, SetlValidBaseBusiVo baseBusiVo) {\r\n        handlerVo.setIndex(index);\r\n        handlerVo.setErrDscr(errDscr);\r\n        handlerVo.setErrorFields(errorFields);\r\n        handlerVo.setChkStas(chkStas);\r\n        handlerVo.setErrType(errType);\r\n        baseBusiVo.getErrorDetails().add(handlerVo);\r\n    }\r\n}\r\n', 'script', 'java', '2024-09-24 17:23:21', '责任护士代码', '基本数据', 0, 1);
INSERT INTO `som_chain_script` VALUES (49, 'somplatform', 'check_zrhsxm_basic', '责任护士姓名[b26n]基础质控', '\r\nimport com.my.som.model.dataHandle.SomHiInvyBasInfo;\r\nimport com.my.som.settlevalidate.constant.SettleValidateConst;\r\nimport com.my.som.settlevalidate.util.SettleValidateUtil;\r\nimport com.my.som.settlevalidate.vo.SetlValidBaseBusiVo;\r\nimport com.my.som.util.SettleListValidateUtil;\r\nimport com.my.som.vo.dataHandle.SettleListHandlerVo;\r\nimport com.my.som.vo.dataHandle.SettleListValidateVo;\r\nimport com.yomahub.liteflow.script.ScriptExecuteWrap;\r\nimport com.yomahub.liteflow.script.body.JaninoCommonScriptBody;\r\nimport org.slf4j.Logger;\r\nimport org.slf4j.LoggerFactory;\r\n\r\nimport java.util.Map;\r\n\r\npublic class check_zrhsxm_basic implements JaninoCommonScriptBody {\r\n    private static final Logger logger = LoggerFactory.getLogger(check_zrhsxm_basic.class);\r\n    private static final String MAIN_CHECK_FIELD = \"b26n\";\r\n    private static final String MAIN_CHECK_NAME = \"责任护士姓名\";\r\n    private static final String MAIN_CHECK_REGION = \"四川\";\r\n    /**\r\n     * 离院方式\r\n     * 1、判断离院方式是否为空。\r\n     * 2、判断离院方式是否为；(1)医嘱离\r\n     * 院(代码1)、(2)医嘱转院(代\r\n     * 码2)、(3)医嘱转社区卫生服务\r\n     * 机构/乡镇卫生院(代码为3)、(4)\r\n     * 非医嘱离院(代码4)、(5)死亡\r\n     * (代码5)、(6)其他(代码9)以\r\n     * 上六种中的任意一种。\r\n     *\r\n     * @param wrap\r\n     * @return\r\n     */\r\n    @Override\r\n    public Void body(ScriptExecuteWrap wrap) {\r\n        SetlValidBaseBusiVo setlValidBaseBusiVo = (SetlValidBaseBusiVo) wrap.cmp.getFirstContextBean();\r\n        SettleListValidateVo settleListValidateVo = (SettleListValidateVo) wrap.cmp.getRequestData();\r\n        Map<String, String> keysBasicResultMap = setlValidBaseBusiVo.getKeysBasicResultMap();\r\n        SomHiInvyBasInfo somHiInvyBasInfo = settleListValidateVo.getSomHiInvyBasInfo();\r\n        SettleListHandlerVo settleListHandlerVo = new SettleListHandlerVo();\r\n        if (SettleValidateUtil.isEmpty(somHiInvyBasInfo)) {\r\n            addErrorDetail(settleListHandlerVo, SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY, SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY,\r\n                    (String) SettleValidateConst.CHECK_TYPE_MAP.get(SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY), MAIN_CHECK_FIELD,\r\n                    SettleValidateConst.VALIDATE_STATE_FAIL, setlValidBaseBusiVo);\r\n            return null;\r\n        }\r\n\r\n        //获取责任护士姓名\r\n        String b26n = somHiInvyBasInfo.getB26n();\r\n        //获取责任护士代码\r\n        String b26c = somHiInvyBasInfo.getB26c();\r\n\r\n        boolean checkBool = SettleValidateUtil.checkNotNullOrNotBlank(b26n, keysBasicResultMap, MAIN_CHECK_FIELD);\r\n        if (checkBool) {\r\n            Map<String, Object> map = (Map<String, Object>) (setlValidBaseBusiVo.getSettleListParams().get(SettleListValidateUtil.KEY_NURSE_NAME));\r\n            //获取护士名称\r\n            Map<String, String> NurseNameMap = (Map<String, String>) map.get(somHiInvyBasInfo.getHospitalId() + SettleListValidateUtil.KEY_NURSE_NAME);\r\n            if (SettleValidateUtil.isNotEmpty(b26c)) {\r\n                String NurseName = (String) NurseNameMap.get(b26c);\r\n                if(SettleValidateUtil.isNotEmpty(NurseName)){\r\n                    if (b26n.equals(NurseName)) {\r\n                        // 正常值，基础校验通过不写错误信息\r\n                        keysBasicResultMap.put(MAIN_CHECK_FIELD, SettleValidateConst.PASS);\r\n                    } else {\r\n                        addErrorDetail(settleListHandlerVo, SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_ERROR_TYPE_2,\r\n                                \"页面中的\" + MAIN_CHECK_NAME + \"[\" + b26n + \"]与该编码[\" + b26c + \"]对应的医院护士名称[\" + NurseName + \"]不匹配\",\r\n                                MAIN_CHECK_FIELD,\r\n                                SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n                        keysBasicResultMap.put(MAIN_CHECK_FIELD, SettleValidateConst.OUTLIER);\r\n                    }\r\n                }\r\n            }\r\n        } else {\r\n            addErrorDetail(settleListHandlerVo, SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_TYPE_1,\r\n                    MAIN_CHECK_NAME + \"[\" + b26n + \"]为空\",\r\n                    MAIN_CHECK_FIELD,\r\n                    SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n            keysBasicResultMap.put(MAIN_CHECK_FIELD, SettleValidateConst.NULL);\r\n        }\r\n        return null;\r\n    }\r\n\r\n    private void addErrorDetail(SettleListHandlerVo handlerVo, String index, String errType, String errDscr, String errorFields, String chkStas, SetlValidBaseBusiVo baseBusiVo) {\r\n        handlerVo.setIndex(index);\r\n        handlerVo.setErrDscr(errDscr);\r\n        handlerVo.setErrorFields(errorFields);\r\n        handlerVo.setChkStas(chkStas);\r\n        handlerVo.setErrType(errType);\r\n        baseBusiVo.getErrorDetails().add(handlerVo);\r\n    }\r\n}\r\n', 'script', 'java', '2024-09-24 17:23:21', '责任护士名称', '基本数据', 0, 1);
INSERT INTO `som_chain_script` VALUES (50, 'somplatform', 'check_zzysdm_basic', '主诊医师代码[b51c]基础质控', '\r\nimport com.my.som.model.dataHandle.SomHiInvyBasInfo;\r\nimport com.my.som.settlevalidate.constant.SettleValidateConst;\r\nimport com.my.som.settlevalidate.util.SettleValidateUtil;\r\nimport com.my.som.settlevalidate.vo.SetlValidBaseBusiVo;\r\nimport com.my.som.util.SettleListValidateUtil;\r\nimport com.my.som.vo.dataHandle.SettleListHandlerVo;\r\nimport com.my.som.vo.dataHandle.SettleListValidateVo;\r\nimport com.yomahub.liteflow.script.ScriptExecuteWrap;\r\nimport com.yomahub.liteflow.script.body.JaninoCommonScriptBody;\r\nimport org.slf4j.Logger;\r\nimport org.slf4j.LoggerFactory;\r\n\r\nimport java.util.Map;\r\n\r\npublic class check_zzysdm_basic implements JaninoCommonScriptBody {\r\n    private static final Logger logger = LoggerFactory.getLogger(check_zzysdm_basic.class);\r\n    private static final String MAIN_CHECK_FIELD = \"b51c\";\r\n    private static final String MAIN_CHECK_NAME = \"主诊医师代码\";\r\n    private static final String MAIN_CHECK_REGION = \"四川\";\r\n    /**\r\n     *\r\n     * @param wrap\r\n     * @return\r\n     */\r\n    @Override\r\n    public Void body(ScriptExecuteWrap wrap) {\r\n        SetlValidBaseBusiVo setlValidBaseBusiVo = (SetlValidBaseBusiVo) wrap.cmp.getFirstContextBean();\r\n        SettleListValidateVo settleListValidateVo = (SettleListValidateVo) wrap.cmp.getRequestData();\r\n        Map<String, String> keysBasicResultMap = setlValidBaseBusiVo.getKeysBasicResultMap();\r\n        SomHiInvyBasInfo somHiInvyBasInfo = settleListValidateVo.getSomHiInvyBasInfo();\r\n        SettleListHandlerVo settleListHandlerVo = new SettleListHandlerVo();\r\n        if (SettleValidateUtil.isEmpty(somHiInvyBasInfo)) {\r\n            SettleValidateUtil.addErrorDetail(settleListHandlerVo, SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY, SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY,\r\n                    (String) SettleValidateConst.CHECK_TYPE_MAP.get(SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY), MAIN_CHECK_FIELD,\r\n                    SettleValidateConst.VALIDATE_STATE_FAIL, setlValidBaseBusiVo);\r\n            return null;\r\n        }\r\n\r\n        //获取主诊医师代码\r\n        String b51c = somHiInvyBasInfo.getB51c();\r\n        boolean checkBool = SettleValidateUtil.checkNotNullOrNotBlank(b51c,keysBasicResultMap,MAIN_CHECK_FIELD);\r\n        if (checkBool) {\r\n            Map<String,Object> map = (Map<String,Object>)(setlValidBaseBusiVo.getSettleListParams().get(SettleListValidateUtil.KEY_DOCTOR_CODE));\r\n\r\n            Map<String,Object> doctorCodeMap = (Map<String,Object>)map.get(somHiInvyBasInfo.getHospitalId());\r\n\r\n\r\n            if (doctorCodeMap.containsKey(b51c)){\r\n                // 正常值，基础校验通过不写错误信息\r\n                keysBasicResultMap.put(MAIN_CHECK_FIELD, SettleValidateConst.PASS);\r\n            }else {\r\n                SettleValidateUtil.addErrorDetail(settleListHandlerVo, SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_TYPE_1,\r\n                        MAIN_CHECK_NAME + \"[\" + b51c + \"] 不是医院医生代码\", MAIN_CHECK_FIELD,\r\n                        SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n                keysBasicResultMap.put(MAIN_CHECK_FIELD, SettleValidateConst.OUTLIER);\r\n            }\r\n        }else{\r\n            SettleValidateUtil.addErrorDetail(settleListHandlerVo, SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_TYPE_1,\r\n                    MAIN_CHECK_NAME + \"[\" + b51c + \"]为空\", MAIN_CHECK_FIELD,\r\n                    SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n            keysBasicResultMap.put(MAIN_CHECK_FIELD, SettleValidateConst.NULL);\r\n        }\r\n        return null;\r\n    }\r\n}\r\n', 'script', 'java', '2024-09-24 17:23:21', '主诊医师代码', '基本数据', 0, 1);
INSERT INTO `som_chain_script` VALUES (51, 'somplatform', 'check_zzysxm_basic', '主诊医师姓名[b52n]基础质控', 'import com.my.som.model.dataHandle.SomHiInvyBasInfo;\r\nimport com.my.som.settlevalidate.constant.SettleValidateConst;\r\nimport com.my.som.settlevalidate.util.SettleValidateUtil;\r\nimport com.my.som.settlevalidate.vo.SetlValidBaseBusiVo;\r\nimport com.my.som.util.SettleListValidateUtil;\r\nimport com.my.som.vo.dataHandle.SettleListHandlerVo;\r\nimport com.my.som.vo.dataHandle.SettleListValidateVo;\r\nimport com.yomahub.liteflow.script.ScriptExecuteWrap;\r\nimport com.yomahub.liteflow.script.body.JaninoCommonScriptBody;\r\nimport org.slf4j.Logger;\r\nimport org.slf4j.LoggerFactory;\r\n\r\nimport java.util.Map;\r\n\r\npublic class check_zzysxm_basic implements JaninoCommonScriptBody {\r\n    private static final Logger logger = LoggerFactory.getLogger(check_zzysxm_basic.class);\r\n\r\n    private static final String MAIN_CHECK_FIELD = \"b52n\";\r\n    private static final String MAIN_CHECK_NAME = \"主诊医师姓名\";\r\n    private static final String MAIN_CHECK_REGION = \"四川\";\r\n    /**\r\n     *\r\n     *\r\n     * @param wrap\r\n     * @return\r\n     */\r\n    @Override\r\n    public Void body(ScriptExecuteWrap wrap) {\r\n        SetlValidBaseBusiVo setlValidBaseBusiVo = (SetlValidBaseBusiVo) wrap.cmp.getFirstContextBean();\r\n        SettleListValidateVo settleListValidateVo = (SettleListValidateVo) wrap.cmp.getRequestData();\r\n        Map<String, String> keysBasicResultMap = setlValidBaseBusiVo.getKeysBasicResultMap();\r\n        SomHiInvyBasInfo somHiInvyBasInfo = settleListValidateVo.getSomHiInvyBasInfo();\r\n        SettleListHandlerVo settleListHandlerVo = new SettleListHandlerVo();\r\n        if (SettleValidateUtil.isEmpty(somHiInvyBasInfo)) {\r\n            return null;\r\n        }\r\n\r\n        //获取主诊医师代码\r\n        String b51c = somHiInvyBasInfo.getB51c();\r\n        //获取主诊医师姓名\r\n        String b52n = somHiInvyBasInfo.getB52n();\r\n        boolean checkBool = SettleValidateUtil.checkNotNullOrNotBlank(b52n,keysBasicResultMap,MAIN_CHECK_FIELD);\r\n        if (checkBool) {\r\n            if(SettleValidateUtil.isNotEmpty(b51c)){\r\n                Map<String,Object> map = (Map<String,Object>)(setlValidBaseBusiVo.getSettleListParams().get(SettleListValidateUtil.KEY_DOCTOR_NAME));\r\n                Map<String,Object> doctorNameMap = (Map<String,Object>)map.get(somHiInvyBasInfo.getHospitalId()+SettleListValidateUtil.KEY_DOCTOR_NAME);\r\n                String doctorName= (String)doctorNameMap.get(b51c);\r\n                if(SettleValidateUtil.isNotEmpty(doctorName)){\r\n                    if(b52n.equals(doctorName)){\r\n                        // 正常值，基础校验通过不写错误信息\r\n                        keysBasicResultMap.put(MAIN_CHECK_FIELD, SettleValidateConst.PASS);\r\n                    }else {\r\n                        addErrorDetail(settleListHandlerVo, SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_ERROR_TYPE_2,\r\n                                \"页面中的\" + MAIN_CHECK_NAME + \"[\" + b52n + \"]与该编码[\" + b51c + \"]对应的医院医生名称[\" + doctorName + \"]不匹配\",\r\n                                MAIN_CHECK_FIELD,\r\n                                SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n                        keysBasicResultMap.put(MAIN_CHECK_FIELD, SettleValidateConst.OUTLIER);\r\n                    }\r\n                }\r\n            }\r\n        }else {\r\n            SettleValidateUtil.addErrorDetail(settleListHandlerVo, SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_TYPE_1,\r\n                    MAIN_CHECK_NAME + \"[\" + b52n + \"]为空\", MAIN_CHECK_FIELD,\r\n                    SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n            keysBasicResultMap.put(MAIN_CHECK_FIELD, SettleValidateConst.NULL);\r\n        }\r\n        return null;\r\n    }\r\n\r\n    private void addErrorDetail(SettleListHandlerVo handlerVo, String index, String errType, String errDscr, String errorFields, String chkStas, SetlValidBaseBusiVo baseBusiVo) {\r\n        handlerVo.setIndex(index);\r\n        handlerVo.setErrDscr(errDscr);\r\n        handlerVo.setErrorFields(errorFields);\r\n        handlerVo.setChkStas(chkStas);\r\n        handlerVo.setErrType(errType);\r\n        baseBusiVo.getErrorDetails().add(handlerVo);\r\n    }\r\n}\r\n', 'script', 'java', '2024-09-24 17:23:21', '主诊医师名称', '基本数据', 0, 1);
INSERT INTO `som_chain_script` VALUES (52, 'somplatform', 'check_anst_time_after_depth', '麻醉时间[anstTime]内涵质控', 'import com.my.som.model.dataHandle.SomHiInvyBasInfo;\r\nimport com.my.som.model.dataHandle.SomOprnOprtInfo;\r\nimport com.my.som.settlevalidate.constant.SettleValidateConst;\r\nimport com.my.som.settlevalidate.util.SettleValidateUtil;\r\nimport com.my.som.settlevalidate.vo.SetlValidBaseBusiVo;\r\nimport com.my.som.vo.dataHandle.SettleListHandlerVo;\r\nimport com.my.som.vo.dataHandle.SettleListValidateVo;\r\nimport com.yomahub.liteflow.script.ScriptExecuteWrap;\r\nimport com.yomahub.liteflow.script.body.JaninoCommonScriptBody;\r\nimport org.slf4j.Logger;\r\nimport org.slf4j.LoggerFactory;\r\n\r\nimport java.text.ParseException;\r\nimport java.text.SimpleDateFormat;\r\nimport java.time.LocalDateTime;\r\nimport java.time.format.DateTimeFormatter;\r\nimport java.time.format.DateTimeParseException;\r\nimport java.util.Date;\r\nimport java.util.List;\r\nimport java.util.Map;\r\n\r\n/**\r\n * 事后判断 检测麻醉时间\r\n */\r\npublic class check_anst_time_after_depth implements JaninoCommonScriptBody {\r\n    private static final Logger logger = LoggerFactory.getLogger(check_anst_time_after_depth.class);\r\n    private static final String MAIN_CHECK_FIELD = \"anstTime\";\r\n    private static final String MAIN_CHECK_NAME = \"麻醉时间\";\r\n    private static final String MAIN_CHECK_FIELD1 = \"c43\";\r\n    private static final String MAIN_CHECK_FIELD_1 = \"anstBegntime\";\r\n    private static final String MAIN_CHECK_NAME_1 = \"麻醉开始时间\";\r\n\r\n    private static final String MAIN_CHECK_FIELD_2 = \"anstEndtime\";\r\n    private static final String MAIN_CHECK_NAME_2 = \"麻醉结束时间\";\r\n    private static final String MAIN_CHECK_FIELD_3 = \"b12\";\r\n    private static final String MAIN_CHECK_FIELD_4 = \"b15\";\r\n    private static final String MAIN_CHECK_REGION = \"四川\";\r\n    /**\r\n     * 麻醉开始时间(anst_begntime)基础质控\r\n     * 麻醉结束时间(anst_endtime)基础质控\r\n     * 判断麻醉开始时间是否为空、麻醉结束时间是否为空\r\n     * 麻醉开始时间是否小于麻醉结束时间\r\n     *\r\n     * @param wrap\r\n     * @return\r\n     */\r\n    @Override\r\n    public Void body(ScriptExecuteWrap wrap) {\r\n        SetlValidBaseBusiVo setlValidBaseBusiVo = (SetlValidBaseBusiVo) wrap.cmp.getFirstContextBean();\r\n        SettleListValidateVo settleListValidateVo = (SettleListValidateVo) wrap.cmp.getRequestData();\r\n        Map<String, String> keysBasicResultMap = setlValidBaseBusiVo.getKeysBasicResultMap();\r\n        List<SomOprnOprtInfo> busOperateDiagnosisList = settleListValidateVo.getBusOperateDiagnosisList();\r\n        SomHiInvyBasInfo somHiInvyBasInfo = settleListValidateVo.getSomHiInvyBasInfo();\r\n        SettleListHandlerVo settleListHandlerVo = new SettleListHandlerVo();\r\n\r\n        // 检查 somHiInvyBasInfo 是否为空\r\n        if (SettleValidateUtil.isEmpty(somHiInvyBasInfo)) {\r\n            addErrorDetail(SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY, SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY,\r\n                    (String) SettleValidateConst.CHECK_TYPE_MAP.get(SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY), MAIN_CHECK_FIELD_1,\r\n                    SettleValidateConst.VALIDATE_STATE_FAIL, setlValidBaseBusiVo);\r\n            return null;\r\n        }\r\n\r\n        // 检查 busOperateDiagnosisList(手术操作) 是否为空\r\n        if (SettleValidateUtil.isEmpty(busOperateDiagnosisList)) {\r\n            return null;\r\n        }\r\n\r\n        for (int i = 0; i < busOperateDiagnosisList.size(); i++) {\r\n            String c43_stas = (String) keysBasicResultMap.get(i + MAIN_CHECK_FIELD1);\r\n            if (SettleValidateConst.PASS.equals(c43_stas)) {\r\n                SomOprnOprtInfo somOprnOprtInfo = (SomOprnOprtInfo) busOperateDiagnosisList.get(i);\r\n                String anstBegntime = somOprnOprtInfo.getAnstBegntime();\r\n                String anstEndtime = somOprnOprtInfo.getAnstEndtime();\r\n                if (SettleValidateUtil.isNotEmpty(anstBegntime) && SettleValidateUtil.isNotEmpty(anstEndtime)) {\r\n                    //说明此时需要麻醉时间判断的规范性\r\n                    checkAnstTimestandards(i, anstBegntime, somOprnOprtInfo, setlValidBaseBusiVo, anstEndtime);\r\n                } else {\r\n                    //判断麻醉时间非空性\r\n                    checkAnstTimeIsEmpty(anstBegntime, i, somOprnOprtInfo, setlValidBaseBusiVo, anstEndtime);\r\n                }}\r\n        }\r\n         if (SettleValidateConst.PASS.equals((String) keysBasicResultMap.get(MAIN_CHECK_FIELD_3))\r\n                && SettleValidateConst.PASS.equals((String) keysBasicResultMap.get(MAIN_CHECK_FIELD_4))) {\r\n            //入院时间\r\n            String b12 = somHiInvyBasInfo.getB12();\r\n            //出院时间\r\n            String b15 = somHiInvyBasInfo.getB15();\r\n\r\n            for (int i = 0; i < busOperateDiagnosisList.size(); i++) {\r\n                //麻醉方式\r\n                String c43_stas = (String) keysBasicResultMap.get(i + MAIN_CHECK_FIELD1);\r\n                if (SettleValidateConst.PASS.equals(c43_stas)) {\r\n                    SomOprnOprtInfo somOprnOprtInfo = (SomOprnOprtInfo) busOperateDiagnosisList.get(i);\r\n                    String anstBegntime = somOprnOprtInfo.getAnstBegntime();\r\n                    String anstEndtime = somOprnOprtInfo.getAnstEndtime();\r\n                  //如果麻醉时间不为空\r\n                    if (SettleValidateUtil.isNotEmpty(anstBegntime) && SettleValidateUtil.isNotEmpty(anstEndtime)) {\r\n                        //判断麻醉时间合理性\r\n                        checkAnstIsplausible(i,b12, anstBegntime, somOprnOprtInfo, setlValidBaseBusiVo, anstEndtime, b15);\r\n                    }\r\n                }\r\n            }\r\n        }\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * 判断麻醉时间不为空\r\n     */\r\n    private void checkAnstTimeIsEmpty(String anstBegntime, int i, SomOprnOprtInfo somOprnOprtInfo, SetlValidBaseBusiVo setlValidBaseBusiVo, String anstEndtime) {\r\n        if (SettleValidateUtil.isEmpty(anstBegntime)) {\r\n            //麻醉开始时间为空\r\n            addErrorDetail(SettleValidateConst.VALIDATE_TYPE_2, SettleValidateConst.VALIDATE_TYPE_1,\r\n                    \"第\" + (i + 1) + \"个手术操作：编码为[\"+somOprnOprtInfo.getC35c()+\"]的\" + MAIN_CHECK_NAME_1 + \"[\" + somOprnOprtInfo.getAnstBegntime() + \"]为空\", MAIN_CHECK_FIELD_1,\r\n                    SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n        }\r\n        if (SettleValidateUtil.isEmpty(anstEndtime)) {\r\n            //麻醉结束时间为空\r\n            addErrorDetail(SettleValidateConst.VALIDATE_TYPE_2, SettleValidateConst.VALIDATE_TYPE_1,\r\n                    \"第\" + (i + 1) + \"个手术操作：编码为[\"+somOprnOprtInfo.getC35c()+\"]的\" + MAIN_CHECK_NAME_2 + \"[\" + somOprnOprtInfo.getAnstEndtime() + \"]为空\", MAIN_CHECK_FIELD_2,\r\n                    SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 判断麻醉时间的合理性\r\n     */\r\n    private void checkAnstIsplausible( int i,String b12, String anstBegntime, SomOprnOprtInfo somOprnOprtInfo, SetlValidBaseBusiVo setlValidBaseBusiVo, String anstEndtime, String b15) {\r\n        //麻醉开始时间大于 入院时间\r\n        if (judgeTimeOK(anstBegntime,b12)) {\r\n            addErrorDetail(SettleValidateConst.VALIDATE_TYPE_2, SettleValidateConst.VALIDATE_ERROR_TYPE_2,\r\n                    \"第\" + (i + 1) + \"个手术操作：编码为[\"+somOprnOprtInfo.getC35c()+\"]的\" +  MAIN_CHECK_NAME_1 + \"[\" + somOprnOprtInfo.getAnstBegntime() + \"] 应该大于入院时间[\"+b12+\"]\", MAIN_CHECK_FIELD_1,\r\n                    SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n        }\r\n        //麻醉结束时间 小于  出院时间\r\n        if (judgeTimeOK(b15, anstEndtime)) {\r\n            addErrorDetail(SettleValidateConst.VALIDATE_TYPE_2, SettleValidateConst.VALIDATE_ERROR_TYPE_2,\r\n                    \"第\" + (i + 1) + \"个手术操作：编码为[\"+somOprnOprtInfo.getC35c()+\"]的\" +  MAIN_CHECK_NAME_2 + \"[\" + somOprnOprtInfo.getAnstEndtime() + \"] 应该小于出院时间[\"+b15+\"]\", MAIN_CHECK_FIELD_2,\r\n                    SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n        }\r\n        //麻醉结束时间大于 麻醉开始时间\r\n        if (judgeTimeOK(anstEndtime,anstBegntime)) {\r\n            addErrorDetail(SettleValidateConst.VALIDATE_TYPE_2, SettleValidateConst.VALIDATE_ERROR_TYPE_2,\r\n                    \"第\" + (i + 1) + \"个手术操作：编码为[\"+somOprnOprtInfo.getC35c()+\"]的\" +  \"麻醉开始时间[\"+somOprnOprtInfo.getAnstBegntime()+\"]应该小于麻醉结束时间[\" +somOprnOprtInfo.getAnstEndtime()+\"]\", MAIN_CHECK_FIELD_1,\r\n                    SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 判断麻醉时间是否规范\r\n     */\r\n    private boolean checkAnstTimestandards(int i, String anstBegntime, SomOprnOprtInfo somOprnOprtInfo, SetlValidBaseBusiVo setlValidBaseBusiVo, String anstEndtime) {\r\n        boolean flag = true;\r\n        if (!isValidDateTime(anstBegntime)) {\r\n            addErrorDetail(SettleValidateConst.VALIDATE_TYPE_2, SettleValidateConst.VALIDATE_ERROR_TYPE_2,\r\n                    \"第\" + (i + 1) + \"个手术操作：编码为[\"+somOprnOprtInfo.getC35c()+\"]的\" + MAIN_CHECK_NAME_1 + \"[\" + somOprnOprtInfo.getAnstBegntime() + \"] 格式错误\", MAIN_CHECK_FIELD_1,\r\n                    SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n            flag = false;\r\n        }\r\n\r\n        if (!isValidDateTime(anstEndtime)) {\r\n            addErrorDetail(SettleValidateConst.VALIDATE_TYPE_2, SettleValidateConst.VALIDATE_ERROR_TYPE_2,\r\n                    \"第\" + (i + 1) + \"个手术操作：编码为[\"+somOprnOprtInfo.getC35c()+\"]的\" + MAIN_CHECK_NAME_2 + \"[\" + somOprnOprtInfo.getAnstEndtime() + \"] 格式错误\", MAIN_CHECK_FIELD_2,\r\n                    SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n            flag = false;\r\n        }\r\n        return flag;\r\n    }\r\n\r\n    private void addErrorDetail(String index, String errType, String errDscr, String errorFields, String\r\n            chkStas, SetlValidBaseBusiVo baseBusiVo) {\r\n        SettleListHandlerVo handlerVo = new SettleListHandlerVo();\r\n        handlerVo.setIndex(index);\r\n        handlerVo.setErrorFields(errorFields);\r\n        handlerVo.setErrDscr(errDscr);\r\n        handlerVo.setChkStas(chkStas);\r\n        handlerVo.setErrType(errType);\r\n        baseBusiVo.getErrorDetails().add(handlerVo);\r\n    }\r\n\r\n    public static boolean isValidDateTime(String dateTimeStr) {\r\n        String format = \"yyyy-MM-dd HH:mm:ss\";\r\n        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(format);\r\n        try {\r\n            LocalDateTime.parse(dateTimeStr, formatter);\r\n            return true;\r\n        } catch (DateTimeParseException e) {\r\n            return false;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 时间2 小于  时间1  返回 false\r\n     *\r\n     * @param time1 时间1\r\n     * @param time2 时间2\r\n     * @return\r\n     */\r\n    private boolean judgeTimeOK(String time1, String time2) {\r\n\r\n        SimpleDateFormat dateFormat = new SimpleDateFormat(\"yyyy-MM-dd HH:mm:ss\");\r\n        boolean flag = false;\r\n        try {\r\n            Date admDate = dateFormat.parse(time1);\r\n            Date  dscgDate= dateFormat.parse(time2);\r\n            // 入院时间大于出院时间\r\n            if (!(dscgDate.before(admDate))) {\r\n                flag = true;\r\n            }\r\n        } catch (ParseException e) {\r\n            logger.error(\"麻醉时间质检 日期解析失败: \" + e.getMessage());\r\n        }\r\n        return flag;\r\n    }\r\n}', 'script', 'java', '2024-09-24 17:23:21', '麻醉时间', '手术信息', 1, 1);
INSERT INTO `som_chain_script` VALUES (55, 'somplatform', 'check_bld_amt_after_depth', '输血量[c46]内涵质控', '\r\nimport com.my.som.model.medicalQuality.SomSetlInvyBldInfo;\r\nimport com.my.som.settlevalidate.constant.SettleValidateConst;\r\nimport com.my.som.settlevalidate.util.SettleValidateUtil;\r\nimport com.my.som.settlevalidate.vo.SetlValidBaseBusiVo;\r\nimport com.my.som.vo.dataHandle.SettleListHandlerVo;\r\nimport com.my.som.vo.dataHandle.SettleListValidateVo;\r\nimport com.yomahub.liteflow.script.ScriptExecuteWrap;\r\nimport com.yomahub.liteflow.script.body.JaninoCommonScriptBody;\r\nimport org.slf4j.Logger;\r\nimport org.slf4j.LoggerFactory;\r\n\r\nimport java.util.List;\r\nimport java.util.Map;\r\n\r\n/**\r\n * 事后判断 输血量不为空\r\n */\r\npublic class check_bld_amt_after_depth implements JaninoCommonScriptBody {\r\n    private static final Logger logger = LoggerFactory.getLogger(check_bld_amt_after_depth.class);\r\n    private static final String MAIN_CHECK_FIELD = \"c46\";\r\n    private static final String MAIN_CHECK_NAME = \"输血量\";\r\n    private static final String MAIN_CHECK_FIELD_1 = \"c45\";\r\n    private static final String MAIN_CHECK_NAME_1 = \"输血品种\";\r\n    private static final String MAIN_CHECK_FIELD_2 = \"c46\";\r\n    private static final String MAIN_CHECK_NAME_2 = \"输血量\";\r\n    private static final String MAIN_CHECK_REGION = \"四川\";\r\n    /**\r\n     * 事后校验\r\n     * 输血量(bld_amt)基础质控\r\n     * 判断 输血量 是否为空\r\n     *\r\n     * @param wrap\r\n     * @return\r\n     */\r\n    @Override\r\n    public Void body(ScriptExecuteWrap wrap) {\r\n        SetlValidBaseBusiVo setlValidBaseBusiVo = (SetlValidBaseBusiVo) wrap.cmp.getFirstContextBean();\r\n        SettleListValidateVo settleListValidateVo = (SettleListValidateVo) wrap.cmp.getRequestData();\r\n        Map<String, String> keysBasicResultMap = setlValidBaseBusiVo.getKeysBasicResultMap();\r\n        SettleListHandlerVo settleListHandlerVo = new SettleListHandlerVo();\r\n\r\n        List<SomSetlInvyBldInfo> busTransfusionList = settleListValidateVo.getBusTransfusionList();\r\n        // 检查 busTransfusionList 是否为空\r\n        if (SettleValidateUtil.isEmpty(busTransfusionList)) {\r\n            return null;\r\n        }\r\n        for (int i = 0; i < busTransfusionList.size(); i++) {\r\n            SomSetlInvyBldInfo somSetlInvyBldInfo = (SomSetlInvyBldInfo) busTransfusionList.get(i);\r\n            if (SettleValidateConst.PASS.equals(keysBasicResultMap.get(i+ MAIN_CHECK_FIELD_1))) {\r\n                if (SettleValidateUtil.isNotEmpty(somSetlInvyBldInfo.getBldAmt())) {\r\n                    Double c46 = Double.valueOf(somSetlInvyBldInfo.getBldAmt());\r\n                    // 获取输血量\r\n                    if (c46 < 0 || c46 == 0) {\r\n                        addErrorDetail(settleListHandlerVo, SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_ERROR_TYPE_2,\r\n                                \"第\" + (i + 1) + \"个输血信息：id为[\" + somSetlInvyBldInfo.getId() + \"]的\" +MAIN_CHECK_NAME_2 + \"[\" + c46 + \"]不符合规范\", MAIN_CHECK_FIELD_2,\r\n                                SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n                    }\r\n                } else {\r\n                    addErrorDetail(settleListHandlerVo, SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_TYPE_1,\r\n                            \"第\" + (i + 1) + \"个输血信息：id为[\" + somSetlInvyBldInfo.getId() + \"]的\" +MAIN_CHECK_NAME_2 + \"[\" + somSetlInvyBldInfo.getBldAmt() + \"]为空\", MAIN_CHECK_FIELD_2,\r\n                            SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n                }\r\n            }\r\n        }\r\n        return null;\r\n    }\r\n\r\n    private void addErrorDetail(SettleListHandlerVo handlerVo, String index, String errType, String errDscr, String errorFields, String chkStas, SetlValidBaseBusiVo baseBusiVo) {\r\n        handlerVo.setIndex(index);\r\n        handlerVo.setErrDscr(errDscr);\r\n        handlerVo.setErrorFields(errorFields);\r\n        handlerVo.setChkStas(chkStas);\r\n        handlerVo.setErrType(errType);\r\n        baseBusiVo.getErrorDetails().add(handlerVo);\r\n    }\r\n}\r\n', 'script', 'java', '2024-09-24 17:23:21', '输血量', '手术信息 ', 1, 1);
INSERT INTO `som_chain_script` VALUES (56, 'somplatform', 'check_bld_unt_after_depth', '输血计量单位[c47]内涵质控', '\r\nimport com.my.som.model.dataHandle.SomHiInvyBasInfo;\r\nimport com.my.som.model.medicalQuality.SomSetlInvyBldInfo;\r\nimport com.my.som.settlevalidate.constant.SettleValidateConst;\r\nimport com.my.som.settlevalidate.util.SettleValidateUtil;\r\nimport com.my.som.settlevalidate.vo.SetlValidBaseBusiVo;\r\nimport com.my.som.vo.dataHandle.SettleListHandlerVo;\r\nimport com.my.som.vo.dataHandle.SettleListValidateVo;\r\nimport com.yomahub.liteflow.script.ScriptExecuteWrap;\r\nimport com.yomahub.liteflow.script.body.JaninoCommonScriptBody;\r\nimport org.slf4j.Logger;\r\nimport org.slf4j.LoggerFactory;\r\n\r\nimport java.util.List;\r\nimport java.util.Map;\r\n\r\n/**\r\n * 判断 输血计量单位不为空\r\n */\r\npublic class check_bld_unt_after_depth implements JaninoCommonScriptBody {\r\n    private static final Logger logger = LoggerFactory.getLogger(check_bld_unt_after_depth.class);\r\n    private static final String MAIN_CHECK_FIELD = \"c47\";\r\n    private static final String MAIN_CHECK_NAME = \"输血计量单位\";\r\n    private static final String MAIN_CHECK_FIELD_1 = \"c45\";\r\n    private static final String MAIN_CHECK_NAME_1 = \"输血品种\";\r\n    private static final String MAIN_CHECK_FIELD_2 = \"c47\";\r\n    private static final String MAIN_CHECK_NAME_2 = \"输血计量单位\";\r\n    private static final String MAIN_CHECK_REGION = \"四川\";\r\n    /**\r\n     * 事后校验\r\n     * 输血计量单位(bld_unt)基础质控\r\n     * 判断 输血计量单位 是否为空\r\n     *\r\n     * @param wrap\r\n     * @return\r\n     */\r\n    @Override\r\n    public Void body(ScriptExecuteWrap wrap) {\r\n        SetlValidBaseBusiVo setlValidBaseBusiVo = (SetlValidBaseBusiVo) wrap.cmp.getFirstContextBean();\r\n        SettleListValidateVo settleListValidateVo = (SettleListValidateVo) wrap.cmp.getRequestData();\r\n        Map<String, String> keysBasicResultMap = setlValidBaseBusiVo.getKeysBasicResultMap();\r\n        SomHiInvyBasInfo somHiInvyBasInfo = settleListValidateVo.getSomHiInvyBasInfo();\r\n        SettleListHandlerVo settleListHandlerVo = new SettleListHandlerVo();\r\n\r\n        // 检查 somHiInvyBasInfo 是否为空\r\n        if (SettleValidateUtil.isEmpty(somHiInvyBasInfo)) {\r\n            addErrorDetail(SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY, SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY,\r\n                    (String) SettleValidateConst.CHECK_TYPE_MAP.get(SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY), null,\r\n                    SettleValidateConst.VALIDATE_STATE_FAIL, setlValidBaseBusiVo);\r\n            return null;\r\n        }\r\n        List<SomSetlInvyBldInfo> busTransfusionList = settleListValidateVo.getBusTransfusionList();\r\n        // 检查 busTransfusionList 是否为空\r\n        if (SettleValidateUtil.isEmpty(busTransfusionList)) {\r\n            return null;\r\n        }\r\n        for (int i = 0; i < busTransfusionList.size(); i++) {\r\n            SomSetlInvyBldInfo somSetlInvyBldInfo = (SomSetlInvyBldInfo) busTransfusionList.get(i);\r\n            if (SettleValidateConst.PASS.equals(keysBasicResultMap.get(i + MAIN_CHECK_FIELD_1))) {\r\n                if (SettleValidateUtil.isEmpty(somSetlInvyBldInfo.getBldUnt())) {\r\n                    addErrorDetail(SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_TYPE_1,\r\n                            \"第\" + (i + 1) + \"个输血信息：id为[\" + somSetlInvyBldInfo.getId() + \"]的\" +MAIN_CHECK_NAME_2 + \"[\" + somSetlInvyBldInfo.getBldUnt() + \"]为空\", MAIN_CHECK_FIELD_2,\r\n                            SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n                }\r\n            }\r\n        }\r\n        return null;\r\n    }\r\n\r\n    private void addErrorDetail(String index, String errType, String errDscr, String errorFields, String chkStas, SetlValidBaseBusiVo baseBusiVo) {\r\n        SettleListHandlerVo handlerVo =new SettleListHandlerVo() ;\r\n        handlerVo.setIndex(index);\r\n        handlerVo.setErrDscr(errDscr);\r\n        handlerVo.setErrorFields(errorFields);\r\n        handlerVo.setChkStas(chkStas);\r\n        handlerVo.setErrType(errType);\r\n        baseBusiVo.getErrorDetails().add(handlerVo);\r\n    }\r\n}\r\n', 'script', 'java', '2024-09-24 17:23:21', '输血计量单位', '手术信息 ', 1, 1);
INSERT INTO `som_chain_script` VALUES (57, 'somplatform', 'check_brn_damg_coma_dura_after_depth', '入院前昏迷时间（天）[C28]内涵质控', 'import com.my.som.common.constant.DrgConst;\r\nimport com.my.som.common.util.ValidateUtil;\r\nimport com.my.som.dto.medicalQuality.BusDiseaseDiagnosisTrim;\r\nimport com.my.som.model.dataHandle.SomHiInvyBasInfo;\r\nimport com.my.som.settlevalidate.constant.SettleValidateConst;\r\nimport com.my.som.settlevalidate.util.SettleValidateUtil;\r\nimport com.my.som.settlevalidate.vo.SetlValidBaseBusiVo;\r\nimport com.my.som.util.SettleListValidateUtil;\r\nimport com.my.som.vo.dataHandle.SettleListDisSectionVo;\r\nimport com.my.som.vo.dataHandle.SettleListHandlerVo;\r\nimport com.my.som.vo.dataHandle.SettleListValidateVo;\r\nimport com.yomahub.liteflow.script.ScriptExecuteWrap;\r\nimport com.yomahub.liteflow.script.body.JaninoCommonScriptBody;\r\nimport org.slf4j.Logger;\r\nimport org.slf4j.LoggerFactory;\r\n\r\nimport java.util.ArrayList;\r\nimport java.util.List;\r\nimport java.util.Map;\r\n/**\r\n * 颅脑损伤昏迷时间校验\r\n */\r\npublic class check_brn_damg_coma_dura_after_depth implements JaninoCommonScriptBody {\r\n    private static final Logger logger = LoggerFactory.getLogger(check_brn_damg_coma_dura_after_depth.class);\r\n    private static final String MAIN_CHECK_FIELD = \"C28\";\r\n    private static final String MAIN_CHECK_NAME = \"入院前昏迷时间（天）\";\r\n    private static final String MAIN_CHECK_REGION = \"四川\";\r\n    /**\r\n     * 颅脑损伤昏迷时间校验\r\n     * 1. 颅脑损伤患者入院前昏迷时长\r\n     * 2. 颅脑损伤患者入院后昏迷时长\r\n     */\r\n    @Override\r\n    public Void body(ScriptExecuteWrap wrap) {\r\n        SetlValidBaseBusiVo setlValidBaseBusiVo = (SetlValidBaseBusiVo) wrap.cmp.getFirstContextBean();\r\n        SettleListValidateVo settleListValidateVo = (SettleListValidateVo) wrap.cmp.getRequestData();\r\n\r\n        SomHiInvyBasInfo somHiInvyBasInfo = settleListValidateVo.getSomHiInvyBasInfo();\r\n        SettleListHandlerVo settleListHandlerVo = new SettleListHandlerVo();\r\n        List<BusDiseaseDiagnosisTrim> busDiseaseDiagnosisTrimsList = settleListValidateVo.getBusDiseaseDiagnosisTrimsList();\r\n        // 检查 somHiInvyBasInfo 是否为空\r\n\r\n        // 检查诊断信息是否为空\r\n        if (SettleValidateUtil.isEmpty(busDiseaseDiagnosisTrimsList)) {\r\n            return null;\r\n        }\r\n\r\n        // 颅脑损伤入院前和入院后的字段\r\n        Integer[] admissionBefore = {somHiInvyBasInfo.getC28(), somHiInvyBasInfo.getC29(), somHiInvyBasInfo.getC30()};\r\n        Integer[] admissionAfter = {somHiInvyBasInfo.getC31(), somHiInvyBasInfo.getC32(), somHiInvyBasInfo.getC33()};\r\n\r\n        // 检查颅脑损伤入院时间是否合法\r\n        validateAdmissionTime(admissionBefore, setlValidBaseBusiVo, \"颅脑损伤入院前昏迷时间不合理\");\r\n        validateAdmissionTime(admissionAfter, setlValidBaseBusiVo, \"颅脑损伤入院后昏迷时间不合理\");\r\n\r\n        //判断入院后昏迷时长是否大于住院天数a20\r\n        String b20 =   somHiInvyBasInfo.getB20();\r\n\r\n        checkAfterLessThanIptDay(admissionAfter,b20,setlValidBaseBusiVo);\r\n        checkReasonable(admissionBefore, setlValidBaseBusiVo, busDiseaseDiagnosisTrimsList);\r\n        return null;\r\n    }\r\n\r\n    private void checkAfterLessThanIptDay(Integer[] admissionAfter, String b20, SetlValidBaseBusiVo setlValidBaseBusiVo) {\r\n        Integer c31 = admissionAfter[0];\r\n        Integer c32 = admissionAfter[1];\r\n        Integer c33 = admissionAfter[2];\r\n\r\n        if ((c31 != null && c31 != 0) || (c32 != null && c32 != 0 ) || (c33 != null && c33 != 0)) {\r\n          if(ValidateUtil.isEmpty(b20)){\r\n              addErrorDetail(SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_ERROR_TYPE_4,\r\n                      \"c06c1\",\r\n                      \"入院后昏迷时间存在，但没有实际住院天数\",\r\n                      SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n          }else{\r\n              Integer inhospitalDays = null;\r\n              try {\r\n                  inhospitalDays = Integer.valueOf(b20);\r\n              } catch (NumberFormatException e) {\r\n                  addErrorDetail(SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_ERROR_TYPE_4,\r\n                          \"b20\",\r\n                          \"实际住院天数不合理\",\r\n                          SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n              }\r\n\r\n              double totalDays = 0.0;\r\n\r\n              if (c31 != null) {\r\n                  totalDays += c31;  // 添加天数\r\n              }\r\n\r\n              if (c32 != null) {\r\n                  totalDays += c32 / 24.0;  // 将小时转为天数\r\n              }\r\n\r\n              if (c33 != null) {\r\n                  totalDays += c33 / 1440.0;  // 将分钟转为天数\r\n              }\r\n              if(totalDays > inhospitalDays){\r\n                  addErrorDetail(SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_ERROR_TYPE_4,\r\n                          \"c06c1\",\r\n                          \"入院后昏迷时间[\"+totalDays+\"] 不应该超过实际住院天数[\"+b20+\"]\",\r\n                          SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n              }\r\n          }\r\n\r\n        }\r\n    }\r\n    private void checkReasonable(Integer[] admissionBefore, SetlValidBaseBusiVo setlValidBaseBusiVo, List<BusDiseaseDiagnosisTrim> busDiseaseDiagnosisTrimsList) {\r\n        boolean flag = false;\r\n        for(Integer num : admissionBefore){\r\n            if (num != null && num != 0) {\r\n                flag =true;\r\n            }\r\n        }\r\n        for(Integer num : admissionBefore){\r\n            if (num != null && num != 0) {\r\n                flag =true;\r\n            }\r\n        }\r\n\r\n        if(flag){\r\n            boolean has = false;\r\n            Map<String, Object> dictMap = (Map<String, Object>) setlValidBaseBusiVo.getSettleListParams().get(SettleListValidateUtil.KEY_DIS_SECTION_LIST);\r\n            List<String> codeList = new ArrayList<>();\r\n            List<SettleListDisSectionVo> settleListDisSectionVos = (List<SettleListDisSectionVo>) dictMap.get(DrgConst.COMA_CODE_FLAG);\r\n            for (SettleListDisSectionVo settleListDisSectionVo : settleListDisSectionVos) {\r\n                codeList.add(settleListDisSectionVo.getDiagSec());\r\n            }\r\n            for(String code: codeList){\r\n                for(BusDiseaseDiagnosisTrim busDiseaseDiagnosisTrim : busDiseaseDiagnosisTrimsList){\r\n                    if(code.equals(busDiseaseDiagnosisTrim.getC06c1())){\r\n                        has= true;\r\n                    }\r\n                }\r\n            }\r\n            if(!has){\r\n                addErrorDetail(SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_ERROR_TYPE_4,\r\n                        \"c06c1\",\r\n                        \"当昏迷时间的“天”“时”“分”其中之一值≠0时，主要诊断或者其他诊断需包含与昏迷诊断相关的编码\",\r\n                        SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n            }\r\n        }\r\n    }\r\n\r\n    private void addErrorDetail( String index,String errType,String errorFields, String errDscr, String chkStas, SetlValidBaseBusiVo baseBusiVo) {\r\n        SettleListHandlerVo handlerVo =new SettleListHandlerVo();\r\n        handlerVo.setIndex(index);\r\n        handlerVo.setErrDscr(errDscr);\r\n        handlerVo.setErrorFields(errorFields);\r\n        handlerVo.setChkStas(chkStas);\r\n        handlerVo.setErrType(errType);\r\n        baseBusiVo.getErrorDetails().add(handlerVo);\r\n    }\r\n\r\n    private void validateAdmissionTime(Integer[] admissionTimes,SetlValidBaseBusiVo baseBusiVo, String errorMessage) {\r\n        Integer c28 = admissionTimes[0];\r\n        Integer c29 = admissionTimes[1];\r\n        Integer c30 = admissionTimes[2];\r\n\r\n        if (!SettleValidateUtil.isEmpty(c28) || !SettleValidateUtil.isEmpty(c29) || !SettleValidateUtil.isEmpty(c30)) {\r\n            if (c28 == null || c28 < 0 || c29 == null || c29 < 0 || c29 > 24 || c30 == null || c30 < 0 || c30 > 60) {\r\n                addErrorDetail( SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_ERROR_TYPE_4,\r\n                        \"c28\", errorMessage, SettleValidateConst.VALIDATE_STATE_SUCCESS, baseBusiVo);\r\n            }\r\n        }\r\n    }\r\n}\r\n', 'script', 'java', '2024-09-24 17:23:21', '颅脑损伤昏迷时间', '基本信息', 1, 1);
INSERT INTO `som_chain_script` VALUES (58, 'somplatform', 'check_diag_code_depth', '出院西医诊断编码[c06c1]内涵质控', 'import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;\r\nimport com.my.som.common.constant.DrgConst;\r\nimport com.my.som.common.exception.AppException;\r\nimport com.my.som.dto.medicalQuality.BusDiseaseDiagnosisTrim;\r\nimport com.my.som.model.dataHandle.SomHiInvyBasInfo;\r\nimport com.my.som.settlevalidate.constant.SettleValidateConst;\r\nimport com.my.som.settlevalidate.util.SettleValidateUtil;\r\nimport com.my.som.settlevalidate.vo.SetlValidBaseBusiVo;\r\nimport com.my.som.util.SettleListValidateUtil;\r\nimport com.my.som.vo.common.DiagConFlictCodeVo;\r\nimport com.my.som.vo.dataHandle.SettleListDisSectionVo;\r\nimport com.my.som.vo.dataHandle.SettleListHandlerVo;\r\nimport com.my.som.vo.dataHandle.SettleListValidateVo;\r\nimport com.yomahub.liteflow.script.ScriptExecuteWrap;\r\nimport com.yomahub.liteflow.script.body.JaninoCommonScriptBody;\r\nimport org.slf4j.Logger;\r\nimport org.slf4j.LoggerFactory;\r\n\r\nimport java.util.*;\r\n\r\n/**\r\n * 诊断编码深度质控\r\n */\r\npublic class check_diag_code_depth implements JaninoCommonScriptBody {\r\n    private static final Logger logger = LoggerFactory.getLogger(check_diag_code_depth.class);\r\n\r\n    private static final String MAIN_CHECK_NAME = \"出院西医诊断编码\";\r\n    private static final String MAIN_CHECK_NAME1 = \"出院西医诊断名称\";\r\n    private static final String MAIN_CHECK_FIELD = \"c06c1\";\r\n    private static final String MAIN_CHECK_FIELD1 = \"c07n1\";\r\n    private static final String CODE = \"b34c\";\r\n    private static final String MAIN_CHECK_REGION = \"四川,湖北\";\r\n    private static final String WX_DIAG = \"1\";\r\n    private static final List<String> ERROR_CODE_BEGIN_LIST = Arrays.asList(\"V\", \"W\", \"X\", \"Y\");\r\n    //分娩\r\n    private static final List<String> DELIVER_CODE_LIST = Arrays.asList(\"O80\", \"O81\", \"O82\", \"O83\", \"O84\");\r\n    //流产\r\n    private static final List<String> ABORTION_CODE_LIST = Arrays.asList(\"O00\", \"O01\", \"O02\", \"O03\", \"O04\", \"O05\", \"O06\", \"O07\", \"O08\");\r\n    //编码长度\r\n    private static final int CODE_LENGTH = 5;\r\n    // 离院方式 5\r\n    private static final String DSCG_WAY_5 = \"5\";\r\n\r\n    /**\r\n     * @param wrap\r\n     * @return\r\n     */\r\n    @Override\r\n    public Void body(ScriptExecuteWrap wrap) {\r\n\r\n        SetlValidBaseBusiVo setlValidBaseBusiVo = (SetlValidBaseBusiVo) wrap.cmp.getFirstContextBean();\r\n        SettleListValidateVo settleListValidateVo = (SettleListValidateVo) wrap.cmp.getRequestData();\r\n        Map<String, String> keysBasicResultMap = setlValidBaseBusiVo.getKeysBasicResultMap();\r\n\r\n        List<BusDiseaseDiagnosisTrim> busDiseaseDiagnosisTrimsList = settleListValidateVo.getBusDiseaseDiagnosisTrimsList();\r\n        SomHiInvyBasInfo somHiInvyBasInfo = settleListValidateVo.getSomHiInvyBasInfo();\r\n        if (SettleValidateUtil.isEmpty(busDiseaseDiagnosisTrimsList)) {\r\n            return null;\r\n        }\r\n        // 检查诊断信息是否为空\r\n        if (SettleValidateUtil.isEmpty(somHiInvyBasInfo)) {\r\n            return null;\r\n        }\r\n\r\n        //获取编码为灰码的集合\r\n        Map<String, Object> grepMap = (Map<String, Object>) setlValidBaseBusiVo.getSettleListParams().get(SettleListValidateUtil.KEY_ICD10_GRAY_CODE_2);\r\n        // 获取清单字典和ICD-10相关映射\r\n        Map<String, Object> icd10Map = (Map<String, Object>) setlValidBaseBusiVo.getSettleListParams().get(SettleListValidateUtil.KEY_ICD10);\r\n\r\n        Map<String, Object> dictMap = (Map<String, Object>) setlValidBaseBusiVo.getSettleListParams().get(SettleListValidateUtil.KEY_DIS_SECTION_LIST);\r\n        //获取性别校对集合\r\n        List<String> checkSexList = getCheckSexList(dictMap, somHiInvyBasInfo, setlValidBaseBusiVo);\r\n        // 获取不能作为主要诊断编码集合\r\n        Map<String, Object> notBeMainCodemap = getNotBeMainCodeList(setlValidBaseBusiVo);\r\n        //获取离院方式为5的编码集合\r\n        String b34c = somHiInvyBasInfo.getB34c();\r\n        List<String> dscgWayIs5CodeList = getDscgWayIs5CodeList(setlValidBaseBusiVo, keysBasicResultMap);\r\n        Map<String, String> codeConflictMap = getCodeConflictMap(setlValidBaseBusiVo);\r\n\r\n\r\n        // 构造set判断是否存在重复编码\r\n        Set<String> diagCodeSet = new HashSet<>();\r\n        //主诊端集合\r\n        List<BusDiseaseDiagnosisTrim> mainCodeList = new ArrayList<>();\r\n\r\n\r\n        for (int i = 0; i < busDiseaseDiagnosisTrimsList.size(); i++) {\r\n            BusDiseaseDiagnosisTrim busDiseaseDiagnosisTrim = (BusDiseaseDiagnosisTrim) busDiseaseDiagnosisTrimsList.get(i);\r\n            //检测主诊段\r\n            checkMainDiag(busDiseaseDiagnosisTrim, mainCodeList, i, setlValidBaseBusiVo);\r\n\r\n            if(WX_DIAG.equals(busDiseaseDiagnosisTrim.getType())){\r\n\r\n            if (!SettleValidateConst.NULL.equals((String) keysBasicResultMap.get(i + MAIN_CHECK_FIELD))) {\r\n                String diagCode = busDiseaseDiagnosisTrim.getC06c1().toUpperCase();\r\n\r\n                if (diagCode.length() < CODE_LENGTH) {\r\n                    throw new AppException(\"第\" + (i + 1) + \"个疾病诊断的\" + MAIN_CHECK_NAME + \"的字符串长度不符\");\r\n                }\r\n                //判断编码规范性 编码是否符合ICD-10标准\r\n                checkCodeSpecification(i, busDiseaseDiagnosisTrim, keysBasicResultMap, icd10Map, setlValidBaseBusiVo, grepMap);\r\n\r\n                if (checkSexList.size() > 0) {\r\n                    // 判断是否和性别相符合\r\n                    checkDiagnosisGenderConsistency(i, checkSexList, diagCode, setlValidBaseBusiVo);\r\n                }\r\n                // 判断诊断是否和开头是否符合\r\n                checkDiagnosisCodeConsistency(i, diagCode, setlValidBaseBusiVo);\r\n\r\n                if (dscgWayIs5CodeList.size() > 0) {\r\n                    //判断诊断编码是死亡诊断 离院方式却不为5\r\n                    checkCodeBelongDeathDiag(dscgWayIs5CodeList, diagCode, b34c, setlValidBaseBusiVo);\r\n                }\r\n\r\n                // 判断是否有重复编码\r\n                checkRepeat(i, diagCodeSet, diagCode, setlValidBaseBusiVo);\r\n            }\r\n            }\r\n        }\r\n        //判断离院方式为死亡 ，但没有死亡诊断\r\n        checkDeathRelatedDiag(b34c, busDiseaseDiagnosisTrimsList, dscgWayIs5CodeList, setlValidBaseBusiVo);\r\n        checkDiagMainCode(mainCodeList, somHiInvyBasInfo, dictMap, notBeMainCodemap, setlValidBaseBusiVo, keysBasicResultMap);\r\n        //判断诊断代码是否存在冲突\r\n\r\n        checkDiagConflit(codeConflictMap, busDiseaseDiagnosisTrimsList, setlValidBaseBusiVo);\r\n\r\n        //主要诊断或者其它诊断编码出现080-084编码\r\n        checkDiagnosisLackDelivery(busDiseaseDiagnosisTrimsList, setlValidBaseBusiVo, keysBasicResultMap);\r\n\r\n        return null;\r\n    }\r\n\r\n    private void checkDeathRelatedDiag(String b34c, List<BusDiseaseDiagnosisTrim> busDiseaseDiagnosisTrimsList, List<String> dscgWayIs5CodeList, SetlValidBaseBusiVo setlValidBaseBusiVo) {\r\n\r\n        if (DSCG_WAY_5.equals(b34c)) {\r\n            boolean flag = true;\r\n            for (int i = 0; i < busDiseaseDiagnosisTrimsList.size(); i++) {\r\n                BusDiseaseDiagnosisTrim busDiseaseDiagnosisTrim = (BusDiseaseDiagnosisTrim) busDiseaseDiagnosisTrimsList.get(i);\r\n                if(WX_DIAG.equals(busDiseaseDiagnosisTrim.getType())){\r\n                String diagCode = busDiseaseDiagnosisTrim.getC06c1().toUpperCase();\r\n                if (dscgWayIs5CodeList.contains(diagCode)) {\r\n                    flag = false;\r\n                }}\r\n            }\r\n            if (flag) {\r\n                addErrorDetail(SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_ERROR_TYPE_2,\r\n                        MAIN_CHECK_FIELD, \"离院方式编码[\" + b34c + \"]为5 但无与死亡相关疾病诊断\",\r\n                        SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 获取转换后的诊断冲突集合\r\n     *\r\n     * @param setlValidBaseBusiVo\r\n     * @return\r\n     */\r\n    private Map<String, String> getCodeConflictMap(SetlValidBaseBusiVo setlValidBaseBusiVo) {\r\n        List<DiagConFlictCodeVo> codeConflictList = (List<DiagConFlictCodeVo>) setlValidBaseBusiVo.getSettleListParams().get(SettleListValidateUtil.KEY_DIAG_CONFLICT);\r\n        Map<String, String> map = new HashMap<>();\r\n        for (DiagConFlictCodeVo diagConFlictCodeVo : codeConflictList) {\r\n            String cateCode = diagConFlictCodeVo.getCateCode();\r\n            map.put(diagConFlictCodeVo.getConflictCode(), cateCode);\r\n        }\r\n        return map;\r\n    }\r\n\r\n    /**\r\n     * 判断诊断代码是否存在冲突\r\n     */\r\n    private void checkDiagConflit(Map<String, String> codeConflictList, List<BusDiseaseDiagnosisTrim> busDiseaseDiagnosisTrimsList, SetlValidBaseBusiVo setlValidBaseBusiVo) {\r\n        List<String> keys = new ArrayList<>();\r\n        for (BusDiseaseDiagnosisTrim busDiseaseDiagnosisTrim : busDiseaseDiagnosisTrimsList) {\r\n            if(WX_DIAG.equals(busDiseaseDiagnosisTrim.getType())){\r\n            String code = busDiseaseDiagnosisTrim.getC06c1().toUpperCase();\r\n            if (SettleValidateUtil.isNotEmpty(code)) {\r\n                for (String key : codeConflictList.keySet()) {\r\n                    if (code.startsWith(key)) {\r\n                        //将主冲突的编码放入集合\r\n                        keys.add(key);\r\n                    }\r\n                }\r\n            }\r\n            }\r\n        }\r\n\r\n        if (!SettleValidateUtil.isEmpty(keys)) {\r\n            for (String key : keys) {\r\n                //获取到具体的次要冲突编码集合\r\n                String codeListStr = (String) codeConflictList.get(key);\r\n                List<String> list = Arrays.asList(codeListStr.split(\",\"));\r\n                for (String codeStart : list) {\r\n                    for (BusDiseaseDiagnosisTrim busDiseaseDiagnosisTrim : busDiseaseDiagnosisTrimsList) {\r\n                        String oldcode = busDiseaseDiagnosisTrim.getC06c1().toUpperCase();\r\n                        if (SettleValidateUtil.isNotEmpty(oldcode) && oldcode.startsWith(codeStart.toUpperCase())) {\r\n                            addErrorDetail(SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_ERROR_TYPE_306,\r\n                                    MAIN_CHECK_FIELD,\r\n                                    \"开头为 [\" + key + \"]的疾病诊断编码 与开头为[\" + list + \"]的疾病诊断编码 冲突 \",\r\n                                    SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 检查主诊端编码\r\n     */\r\n    private void checkDiagMainCode(List<BusDiseaseDiagnosisTrim> mainCodeList, SomHiInvyBasInfo somHiInvyBasInfo, Map<String, Object> dictMap, Map<String, Object> notBeMainCodemap, SetlValidBaseBusiVo setlValidBaseBusiVo, Map<String, String> keysBasicResultMap) {\r\n        if (mainCodeList.size() > 1) {\r\n            //说明重复\r\n            addErrorDetail(SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_ERROR_TYPE_10,\r\n                    MAIN_CHECK_FIELD,\r\n                    \"存在多个主诊断编码\",\r\n                    SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n        } else if (mainCodeList.size() == 0) {\r\n            //没有主诊端\r\n            addErrorDetail(SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_ERROR_TYPE_10,\r\n                    MAIN_CHECK_FIELD,\r\n                    \"主诊断编码不存在\",\r\n                    SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n            //没有主诊端\r\n        } else {\r\n            //判断不为作为该患者的主要编码是否不嫩作为主要编码\r\n            if (notBeMainCodemap.size() > 0) {\r\n                // 判断年龄大于29天不能作为主要诊断相符合\r\n                BusDiseaseDiagnosisTrim busDiseaseDiagnosisTrim = (BusDiseaseDiagnosisTrim) mainCodeList.get(0);\r\n                checkDiagnosisAgeConsistency(busDiseaseDiagnosisTrim, somHiInvyBasInfo, dictMap, notBeMainCodemap, setlValidBaseBusiVo, keysBasicResultMap);\r\n            }\r\n        }\r\n    }\r\n\r\n    private void checkRepeat(int i, Set<String> diagCodeSet, String diagCode, SetlValidBaseBusiVo setlValidBaseBusiVo) {\r\n        if (!diagCodeSet.add(diagCode)) {\r\n            addErrorDetail(SettleValidateConst.VALIDATE_TYPE_2, SettleValidateConst.VALIDATE_ERROR_TYPE_306,\r\n                    MAIN_CHECK_FIELD,\r\n                    \"第\" + (i + 1) + \"个疾病诊断：\" + MAIN_CHECK_NAME + \"[\" + diagCode + \"]重复\",\r\n                    SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n        }\r\n    }\r\n\r\n    private void checkCodeBelongDeathDiag(List<String> dscgWayIs5CodeList, String diagCode, String b34c, SetlValidBaseBusiVo setlValidBaseBusiVo) {\r\n        if (dscgWayIs5CodeList.contains(diagCode)) {\r\n            if (!DSCG_WAY_5.equals(b34c)) {\r\n                addErrorDetail(SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_ERROR_TYPE_2,\r\n                        MAIN_CHECK_FIELD, \"存在与死亡相关疾病诊断[\" + diagCode + \"] 但患者离院方式[\" + b34c + \"]不为5\",\r\n                        SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n            }\r\n        }\r\n    }\r\n\r\n    private List<String> getDscgWayIs5CodeList(SetlValidBaseBusiVo setlValidBaseBusiVo, Map<String, String> keysBasicResultMap) {\r\n        List<String> dscgWayIs5CodeList = new ArrayList<>();\r\n        if (SettleValidateConst.PASS.equals((String) keysBasicResultMap.get(CODE))) {\r\n            //获取院系 科别编码\r\n            Map<String, Object> map = (Map<String, Object>) setlValidBaseBusiVo.getSettleListParams().get(SettleListValidateUtil.KEY_DICT);\r\n            dscgWayIs5CodeList.addAll((List<String>) map.get(\"SWZDBM\"));\r\n        }\r\n        return listToUpCase(dscgWayIs5CodeList);\r\n    }\r\n\r\n    private Map<String, Object> getNotBeMainCodeList(SetlValidBaseBusiVo setlValidBaseBusiVo) {\r\n        Map<String, Object> orgMap = (Map<String, Object>) setlValidBaseBusiVo.getSettleListParams().get(SettleListValidateUtil.KEY_NOT_BE_MAIN_DIAG);\r\n//        Map<String, Object> resultMap = new HashMap<>();\r\n//        if (orgMap == null || orgMap.isEmpty()) {\r\n//            return resultMap;\r\n//        }\r\n//        Set<Map.Entry<String,Object>> entrySet = orgMap.entrySet();\r\n//        for (Map.Entry<String, Object> entry : entrySet) {\r\n//            String key = entry.getKey();\r\n//            Object value = entry.getValue();\r\n//            resultMap.put(key.toUpperCase(), value.toString());\r\n//        }\r\n        return orgMap;\r\n    }\r\n\r\n\r\n    private List<String> getCheckSexList(Map<String, Object> map, SomHiInvyBasInfo somHiInvyBasInfo, SetlValidBaseBusiVo setlValidBaseBusiVo) {\r\n        String a12c = somHiInvyBasInfo.getA12c();\r\n        List<String> diagSections = new ArrayList<>();\r\n        if (!SettleValidateUtil.isEmpty(a12c)) {\r\n            if (DrgConst.MAN.equals(a12c)) {\r\n                List<SettleListDisSectionVo> settleListDisSectionVos = (List<SettleListDisSectionVo>) map.get(DrgConst.MAN);\r\n                for (SettleListDisSectionVo settleListDisSectionVo : settleListDisSectionVos) {\r\n                    diagSections.add(settleListDisSectionVo.getDiagSec());\r\n                }\r\n            } else if (DrgConst.WOMAN.equals(a12c)) {\r\n                List<SettleListDisSectionVo> settleListDisSectionVos = (List<SettleListDisSectionVo>) map.get(DrgConst.WOMAN);\r\n                for (SettleListDisSectionVo settleListDisSectionVo : settleListDisSectionVos) {\r\n                    diagSections.add(settleListDisSectionVo.getDiagSec());\r\n                }\r\n            } else {\r\n                addErrorDetail(SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_ERROR_TYPE_2,\r\n                        MAIN_CHECK_FIELD,\r\n                        \"该性别性别填写不规范无法获取到诊断该性别编码的目录\",\r\n                        SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n            }\r\n        } else {\r\n            addErrorDetail(SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_ERROR_TYPE_1,\r\n                    MAIN_CHECK_FIELD,\r\n                    \"性别为空\",\r\n                    SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n        }\r\n\r\n        return listToUpCase(diagSections);\r\n    }\r\n\r\n    private void checkMainDiag(BusDiseaseDiagnosisTrim busDiseaseDiagnosisTrim, List<BusDiseaseDiagnosisTrim> mainCodeList, int i, SetlValidBaseBusiVo setlValidBaseBusiVo) {\r\n        // 判断是否存在主要诊断\r\n        if (busDiseaseDiagnosisTrim.getSeq() == 0) {\r\n            if (DrgConst.MAINDIAG.equals(busDiseaseDiagnosisTrim.getMainDiagFlag())) {\r\n                mainCodeList.add(busDiseaseDiagnosisTrim);\r\n            } else {\r\n                addErrorDetail(SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_ERROR_TYPE_1,\r\n                        MAIN_CHECK_FIELD,\r\n                        \"主要诊断信息应该排列在第一位\",\r\n                        SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n            }\r\n        } else if (SettleValidateUtil.isEmpty(busDiseaseDiagnosisTrim.getSeq())) {\r\n            addErrorDetail(SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_ERROR_TYPE_1,\r\n                    MAIN_CHECK_FIELD,\r\n                    \"第\" + (i + 1) + \"个疾病诊断：编码为[\" + busDiseaseDiagnosisTrim.getC06c1() + \"]的\" + \"Seq数据为空\",\r\n                    SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n        }\r\n    }\r\n\r\n\r\n    /**\r\n     * 判断开头是否符合标准\r\n     *\r\n     * @param setlValidBaseBusiVo\r\n     */\r\n    private void checkDiagnosisCodeConsistency(int i, String diseCode, SetlValidBaseBusiVo setlValidBaseBusiVo) {\r\n        if (ERROR_CODE_BEGIN_LIST.contains(diseCode.substring(0, 1))) {\r\n            addErrorDetail(SettleValidateConst.VALIDATE_TYPE_2, SettleValidateConst.VALIDATE_ERROR_TYPE_2,\r\n                    MAIN_CHECK_FIELD,\r\n                    \"第\" + (i + 1) + \"个疾病诊断：\" + MAIN_CHECK_NAME + \"[\" + diseCode + \"]诊断及编码范围应填写A~U开头和Z开头的编码及名称\",\r\n                    SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 不能作为主要诊断\r\n     */\r\n    private void checkDiagnosisAgeConsistency(BusDiseaseDiagnosisTrim busDiseaseDiagnosisTrim, SomHiInvyBasInfo somHiInvyBasInfo, Map<String, Object> dicmap,\r\n                                              Map<String, Object> notBeMainCodemap, SetlValidBaseBusiVo setlValidBaseBusiVo, Map<String, String> keysBasicResultMap) {\r\n        String diagCode = busDiseaseDiagnosisTrim.getC06c1();\r\n        if (notBeMainCodemap.containsKey(diagCode)) {\r\n            String dscr = (String) notBeMainCodemap.get(diagCode);\r\n            addErrorDetail(SettleValidateConst.VALIDATE_TYPE_2, SettleValidateConst.VALIDATE_ERROR_TYPE_306,\r\n                    MAIN_CHECK_FIELD,\r\n                    MAIN_CHECK_NAME + \"[\" + busDiseaseDiagnosisTrim.getC06c1() + \"]\" + dscr,\r\n                    SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n        }\r\n        if (diagCode.length() < 3) {\r\n            addErrorDetail(SettleValidateConst.VALIDATE_TYPE_2, SettleValidateConst.VALIDATE_ERROR_TYPE_306,\r\n                    MAIN_CHECK_FIELD,\r\n                    MAIN_CHECK_NAME + \"[\" + busDiseaseDiagnosisTrim.getC06c1() + \"]编码格式错误\",\r\n                    SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n        }\r\n        List<String> dayMoreThanList = getlist(somHiInvyBasInfo, dicmap);\r\n        if (!SettleValidateUtil.isEmpty(dayMoreThanList)) {\r\n            diagCode = busDiseaseDiagnosisTrim.getC06c1().toUpperCase();\r\n            if (dayMoreThanList.contains(diagCode.substring(0, 3))) {\r\n                addErrorDetail(SettleValidateConst.VALIDATE_TYPE_2, SettleValidateConst.VALIDATE_ERROR_TYPE_306,\r\n                        MAIN_CHECK_FIELD,\r\n                        MAIN_CHECK_NAME + \"[\" + busDiseaseDiagnosisTrim.getC06c1() + \"],疾病编码指起源于围生期,患者年龄大于29天不能作为主要诊断\",\r\n                        SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n            }\r\n        }\r\n    }\r\n\r\n\r\n    private List<String> getlist(SomHiInvyBasInfo somHiInvyBasInfo, Map<String, Object> dicmap) {\r\n        Integer a16 = somHiInvyBasInfo.getA16();\r\n        Integer a14 = somHiInvyBasInfo.getA14();\r\n        List<String> dayMoreThanList = new ArrayList<String>();\r\n        if ((ObjectUtils.isNotEmpty(a16) && a16 > 29) || (ObjectUtils.isNotEmpty(a14) && a14 > 0)) {\r\n            List<SettleListDisSectionVo> settleListDisSectionVos = (List<SettleListDisSectionVo>) dicmap.get(DrgConst.AGE_MORE_THAN_29D);\r\n            for (SettleListDisSectionVo settleListDisSectionVo : settleListDisSectionVos) {\r\n                dayMoreThanList.add(settleListDisSectionVo.getDiagSec());\r\n            }\r\n            return listToUpCase(dayMoreThanList);\r\n        }\r\n        return null;\r\n    }\r\n\r\n    private void checkDiagnosisGenderConsistency(int i, List<String> checkSexList, String diagCode, SetlValidBaseBusiVo setlValidBaseBusiVo) {\r\n        if (checkSexList.contains(diagCode.substring(0, 3)) || checkSexList.contains(diagCode.substring(0, 5))) {\r\n            addErrorDetail(SettleValidateConst.VALIDATE_TYPE_2, SettleValidateConst.VALIDATE_ERROR_TYPE_306,\r\n                    MAIN_CHECK_FIELD,\r\n                    \"第\" + (i + 1) + \"个疾病诊断：\" + MAIN_CHECK_NAME + \"[\" + diagCode + \"]与性别不符\",\r\n                    SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n        }\r\n    }\r\n\r\n    private void checkCodeSpecification(int i, BusDiseaseDiagnosisTrim busDiseaseDiagnosisTrim, Map<String, String> keysBasicResultMap,\r\n                                        Map<String, Object> icd10Map, SetlValidBaseBusiVo setlValidBaseBusiVo,\r\n                                        Map<String, Object> grepMap) {\r\n        String diagCode = busDiseaseDiagnosisTrim.getC06c1();\r\n        String diagName = busDiseaseDiagnosisTrim.getC07n1();\r\n        if (SettleValidateConst.PASS.equals((String) keysBasicResultMap.get(i + MAIN_CHECK_FIELD))\r\n                && SettleValidateConst.PASS.equals((String) keysBasicResultMap.get(i + MAIN_CHECK_FIELD1))) {\r\n            // 验证编码是否符合ICD-10标准\r\n            if (!icd10Map.containsKey(diagCode)) {\r\n                addErrorDetail(SettleValidateConst.VALIDATE_TYPE_2, SettleValidateConst.VALIDATE_ERROR_TYPE_6,\r\n                        MAIN_CHECK_FIELD,\r\n                        \"第\" + (i + 1) + \"个疾病诊断：\" + MAIN_CHECK_NAME + \"[\" + diagCode + \"]不符合医保2.0\",\r\n                        SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n            } else {\r\n                // 验证名称是否符合ICD-10标准\r\n                String icdName = (String) icd10Map.get(diagCode);\r\n                if (!diagName.equals(icdName)) {\r\n                    addErrorDetail(SettleValidateConst.VALIDATE_TYPE_2, SettleValidateConst.VALIDATE_ERROR_TYPE_6,\r\n                            MAIN_CHECK_FIELD,\r\n                            \"第\" + (i + 1) + \"个疾病诊断：\" + MAIN_CHECK_NAME1 + \"[\" + diagName + \"]不符合医保2.0,应改为[\" + icdName + \"]\",\r\n                            SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n                }\r\n            }\r\n            // 检查主要诊断是否存在医保停用码\r\n            if (grepMap.containsKey(diagCode)) {\r\n                addErrorDetail(SettleValidateConst.VALIDATE_TYPE_2, SettleValidateConst.VALIDATE_ERROR_TYPE_12,\r\n                        MAIN_CHECK_FIELD,\r\n                        \"第\" + (i + 1) + \"个疾病诊断：\" + MAIN_CHECK_NAME + \"[\" + diagCode + \"]是医保停用码\",\r\n                        SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n            }\r\n        }\r\n    }\r\n\r\n    private void checkDiagnosisLackDelivery(List<BusDiseaseDiagnosisTrim> busDiseaseDiagnosisTrimsList, SetlValidBaseBusiVo setlValidBaseBusiVo, Map<String, String> keysBasicResultMap) {\r\n        boolean isAbortion = false;\r\n        boolean isDeliver = false;\r\n        boolean bornOK = false;\r\n        for (int i = 0; i < busDiseaseDiagnosisTrimsList.size(); i++) {\r\n            if (!SettleValidateConst.NULL.equals(keysBasicResultMap.get(i + MAIN_CHECK_FIELD))) {\r\n                BusDiseaseDiagnosisTrim busDiseaseDiagnosisTrim = (BusDiseaseDiagnosisTrim) busDiseaseDiagnosisTrimsList.get(i);\r\n                if(WX_DIAG.equals(busDiseaseDiagnosisTrim.getType())){\r\n                String diagCode = busDiseaseDiagnosisTrim.getC06c1();\r\n                if (DELIVER_CODE_LIST.contains(diagCode.substring(0, 3))) {\r\n                    isDeliver = true;\r\n                }\r\n                if (ABORTION_CODE_LIST.contains(diagCode.substring(0, 3))) {\r\n                    isAbortion = true;\r\n                }\r\n                if (busDiseaseDiagnosisTrim.getC06c1().substring(0, 3).equals(\"Z37\")) {\r\n                    bornOK = true;\r\n                }\r\n                }\r\n            }\r\n        }\r\n\r\n        if (isDeliver) {\r\n            if (!isAbortion && !bornOK) {\r\n                addErrorDetail(SettleValidateConst.VALIDATE_TYPE_2, SettleValidateConst.VALIDATE_ERROR_TYPE_2,\r\n                        MAIN_CHECK_FIELD,\r\n                        MAIN_CHECK_NAME + \"不规范\" + \"诊断编码出现080-084编码 无流产编码 也没有有分娩结局编码Z37\",\r\n                        SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n            }\r\n        }\r\n\r\n    }\r\n\r\n    private List<String> listToUpCase(List<String> list) {\r\n        List<String> upperCaseStrings = new ArrayList<>();\r\n        for (String str : list) {\r\n            upperCaseStrings.add(str.toUpperCase());\r\n        }\r\n        return upperCaseStrings;\r\n    }\r\n\r\n    private void addErrorDetail(String index, String errType, String errorFields, String errDscr, String chkStas, SetlValidBaseBusiVo baseBusiVo) {\r\n        SettleListHandlerVo handlerVo = new SettleListHandlerVo();\r\n        handlerVo.setIndex(index);\r\n        handlerVo.setErrorFields(errorFields);\r\n        handlerVo.setErrDscr(errDscr);\r\n        handlerVo.setChkStas(chkStas);\r\n        handlerVo.setErrType(errType);\r\n        baseBusiVo.getErrorDetails().add(handlerVo);\r\n    }\r\n}', 'script', 'java', '2024-09-24 17:23:21', '诊断编码', '诊断信息 ', 1, 1);
INSERT INTO `som_chain_script` VALUES (59, 'somplatform', 'check_diag_merge_comb_after_depth', '诊断联合编码名称[c06c1]内涵质控', '\r\nimport com.my.som.dto.medicalQuality.BusDiseaseDiagnosisTrim;\r\nimport com.my.som.settlevalidate.constant.SettleValidateConst;\r\nimport com.my.som.settlevalidate.vo.SetlValidBaseBusiVo;\r\nimport com.my.som.util.SettleListValidateUtil;\r\nimport com.my.som.vo.dataConfig.CombCodeVO;\r\nimport com.my.som.vo.dataHandle.SettleListHandlerVo;\r\nimport com.my.som.vo.dataHandle.SettleListValidateVo;\r\nimport com.yomahub.liteflow.script.ScriptExecuteWrap;\r\nimport com.yomahub.liteflow.script.body.JaninoCommonScriptBody;\r\nimport org.slf4j.Logger;\r\nimport org.slf4j.LoggerFactory;\r\n\r\n\r\nimport java.util.ArrayList;\r\nimport java.util.Arrays;\r\nimport java.util.List;\r\nimport java.util.Map;\r\n\r\n/**\r\n * 诊断联合编码深度质控\r\n */\r\npublic class check_diag_merge_comb_after_depth implements JaninoCommonScriptBody {\r\n    private static final Logger logger = LoggerFactory.getLogger(check_diag_merge_comb_after_depth.class);\r\n\r\n    private static final String MAIN_CHECK_FIELD = \"c06c1\";\r\n    private static final String MAIN_CHECK_NAME = \"诊断联合编码名称\";\r\n    private static final String MAIN_CHECK_REGION = \"四川,湖北\";\r\n    /**\r\n     * 诊断联合编码校验\r\n     * @param wrap\r\n     * @return\r\n     */\r\n    @Override\r\n    public Void body(ScriptExecuteWrap wrap) {\r\n        SetlValidBaseBusiVo setlValidBaseBusiVo = (SetlValidBaseBusiVo) wrap.cmp.getFirstContextBean();\r\n        SettleListValidateVo settleListValidateVo = (SettleListValidateVo) wrap.cmp.getRequestData();\r\n        List<BusDiseaseDiagnosisTrim> busDiseaseDiagnosisTrimsList = settleListValidateVo.getBusDiseaseDiagnosisTrimsList();\r\n\r\n        if (!busDiseaseDiagnosisTrimsList.isEmpty()) {\r\n            List<String> diags = new ArrayList<>();\r\n            for (BusDiseaseDiagnosisTrim vo : busDiseaseDiagnosisTrimsList) {\r\n                diags.add(vo.getC06c1());\r\n            }\r\n            Map<String, Object> map = (Map<String, Object>) setlValidBaseBusiVo.getSettleListParams().get(SettleListValidateUtil.KEY_COMB_CODE);\r\n            List<CombCodeVO> combCodeVOS = (List<CombCodeVO>) map.get(SettleListValidateUtil.COMB_TYPE_DIAG);\r\n\r\n            for (CombCodeVO combCodeVO : combCodeVOS) {\r\n                List<String> combCodes = Arrays.asList(combCodeVO.getCombCodes().split(\";\"));\r\n\r\n                if (diags.containsAll(combCodes)) {\r\n                    // 存在联合编码合并时，进行质控提示\r\n                    addErrorDetail(SettleValidateConst.VALIDATE_TYPE_2,SettleValidateConst.VALIDATE_ERROR_TYPE_306,\r\n                            MAIN_CHECK_FIELD,\r\n                            \"诊断编码\"+combCodes.toString() + \"需调整为联合诊断编码[\" + combCodeVO.getGeneCode() + \"]\",\r\n                            SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n                }\r\n            }\r\n        }\r\n\r\n        return null;\r\n    }\r\n\r\n    private void addErrorDetail(String index,String errType,String errorFields, String errDscr, String chkStas, SetlValidBaseBusiVo baseBusiVo) {\r\n        SettleListHandlerVo handlerVo = new SettleListHandlerVo();\r\n        handlerVo.setIndex(index);\r\n        handlerVo.setErrorFields(errorFields);\r\n        handlerVo.setErrDscr(errDscr);\r\n        handlerVo.setChkStas(chkStas);\r\n        handlerVo.setErrType(errType);\r\n        baseBusiVo.getErrorDetails().add(handlerVo);\r\n    }\r\n}\r\n', 'script', 'java', '2024-09-24 17:23:21', '出院西医诊断', '住院诊断', 1, 1);
INSERT INTO `som_chain_script` VALUES (60, 'somplatform', 'check_dscg_time_depth', '出院时间[b15]内涵质控', '\r\nimport com.my.som.model.dataHandle.SomHiInvyBasInfo;\r\nimport com.my.som.settlevalidate.constant.SettleValidateConst;\r\nimport com.my.som.settlevalidate.util.SettleValidateUtil;\r\nimport com.my.som.settlevalidate.vo.SetlValidBaseBusiVo;\r\nimport com.my.som.vo.dataHandle.SettleListHandlerVo;\r\nimport com.my.som.vo.dataHandle.SettleListValidateVo;\r\nimport com.yomahub.liteflow.script.ScriptExecuteWrap;\r\nimport com.yomahub.liteflow.script.body.JaninoCommonScriptBody;\r\nimport org.slf4j.Logger;\r\nimport org.slf4j.LoggerFactory;\r\n\r\nimport java.text.ParseException;\r\nimport java.text.SimpleDateFormat;\r\nimport java.util.Date;\r\nimport java.util.Map;\r\n\r\npublic class check_dscg_time_depth implements JaninoCommonScriptBody {\r\n    private static final Logger logger = LoggerFactory.getLogger(check_dscg_time_depth.class);\r\n    private static final String MAIN_CHECK_FIELD = \"b15\";\r\n    private static final String MAIN_CHECK_NAME = \"出院时间\";\r\n    private static final String MAIN_CHECK_FIELD_1 = \"b12\";\r\n    private static final String MAIN_CHECK_NAME_1 = \"入院时间\";\r\n    private static final String MAIN_CHECK_FIELD_2 = \"b15\";\r\n    private static final String MAIN_CHECK_NAME_2 = \"出院时间\";\r\n    private static final String MAIN_CHECK_REGION = \"四川,湖北\";\r\n    /**\r\n     * 出院时间(dscg_time)深度质控\r\n     * 1.判断入院时间是否小于出院时间 若否则不通过\r\n     * @param wrap\r\n     * @return\r\n     */\r\n    @Override\r\n    public Void body(ScriptExecuteWrap wrap) {\r\n        SetlValidBaseBusiVo setlValidBaseBusiVo = (SetlValidBaseBusiVo) wrap.cmp.getFirstContextBean();\r\n        SettleListValidateVo settleListValidateVo = (SettleListValidateVo) wrap.cmp.getRequestData();\r\n        Map<String, String> keysBasicResultMap = setlValidBaseBusiVo.getKeysBasicResultMap();\r\n        SomHiInvyBasInfo somHiInvyBasInfo = settleListValidateVo.getSomHiInvyBasInfo();\r\n        SettleListHandlerVo settleListHandlerVo = new SettleListHandlerVo();\r\n\r\n        // 检查 somHiInvyBasInfo 是否为空\r\n        if (SettleValidateUtil.isEmpty(somHiInvyBasInfo)) {\r\n            addErrorDetail(settleListHandlerVo, SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY,SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY,\r\n                    MAIN_CHECK_FIELD_2,\r\n                    (String)SettleValidateConst.CHECK_TYPE_MAP.get(SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY),\r\n                    SettleValidateConst.VALIDATE_STATE_FAIL, setlValidBaseBusiVo);\r\n            return null;\r\n        }\r\n\r\n        // 获取入院时间校验结果\r\n        String b12_stas = (String) keysBasicResultMap.get(MAIN_CHECK_FIELD_1);\r\n\r\n        // 获取出院时间校验结果\r\n        String b15_stas = (String) keysBasicResultMap.get(MAIN_CHECK_FIELD_2);\r\n\r\n        if(SettleValidateConst.PASS.equals(b12_stas) && SettleValidateConst.PASS.equals(b15_stas)){\r\n            // 入院时间、出院时间基础质控通过 进一步判断入院时间合理性\r\n            // 获取入院时间\r\n            String b12 = somHiInvyBasInfo.getB12();\r\n            // 获取出院时间\r\n            String b15 = somHiInvyBasInfo.getB15();\r\n            SimpleDateFormat dateFormat = new SimpleDateFormat(\"yyyy-MM-dd HH:mm:ss\");\r\n            try{\r\n                Date admDate = dateFormat.parse(b12);\r\n                Date dscgDate = dateFormat.parse(b15);\r\n                // 入院时间大于出院时间\r\n                if (admDate.after(dscgDate)) {\r\n                    addErrorDetail(settleListHandlerVo, SettleValidateConst.VALIDATE_TYPE_2,SettleValidateConst.VALIDATE_ERROR_TYPE_304,\r\n                            MAIN_CHECK_FIELD_2,\r\n                            MAIN_CHECK_NAME_2 + \"[\" + b15 + \"]小于\"+MAIN_CHECK_NAME_1 + \"[\" + b12 + \"]\",\r\n                            SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n                }\r\n            }catch (ParseException e){\r\n                logger.error(\"日期解析失败: \" + e.getMessage());\r\n            }\r\n        }\r\n\r\n        return null;\r\n    }\r\n\r\n    private void addErrorDetail(SettleListHandlerVo handlerVo, String index,String errType,String errorFields, String errDscr, String chkStas, SetlValidBaseBusiVo baseBusiVo) {\r\n        handlerVo.setIndex(index);\r\n        handlerVo.setErrDscr(errDscr);\r\n        handlerVo.setErrorFields(errorFields);\r\n        handlerVo.setChkStas(chkStas);\r\n        handlerVo.setErrType(errType);\r\n        baseBusiVo.getErrorDetails().add(handlerVo);\r\n    }\r\n}\r\n', 'script', 'java', '2024-09-24 17:23:21', '出院时间', '住院诊断', 0, 1);
INSERT INTO `som_chain_script` VALUES (61, 'somplatform', 'check_dscg_wm_other_dise_new_age_depth', '出院西医诊断编码[c06c1]新生儿相关内涵质控', 'import com.my.som.dto.medicalQuality.BusDiseaseDiagnosisTrim;\r\nimport com.my.som.model.dataHandle.SomHiInvyBasInfo;\r\nimport com.my.som.settlevalidate.constant.SettleValidateConst;\r\nimport com.my.som.settlevalidate.util.SettleValidateUtil;\r\nimport com.my.som.settlevalidate.vo.SetlValidBaseBusiVo;\r\nimport com.my.som.util.SettleListValidateUtil;\r\nimport com.my.som.vo.dataHandle.SettleListHandlerVo;\r\nimport com.my.som.vo.dataHandle.SettleListValidateVo;\r\nimport com.yomahub.liteflow.script.ScriptExecuteWrap;\r\nimport com.yomahub.liteflow.script.body.JaninoCommonScriptBody;\r\nimport org.slf4j.Logger;\r\nimport org.slf4j.LoggerFactory;\r\nimport java.util.List;\r\nimport java.util.Map;\r\nimport java.util.stream.Collectors;\r\n\r\n/**\r\n * 事后 出院西医诊断 新生儿年龄\r\n */\r\npublic class check_dscg_wm_other_dise_new_age_depth implements JaninoCommonScriptBody {\r\n    private static final Logger logger = LoggerFactory.getLogger(check_dscg_wm_other_dise_new_age_depth.class);\r\n\r\n    private static final String MAIN_CHECK_FIELD = \"c06c1\";\r\n    private static final String MAIN_CHECK_CODE = \"a16\";\r\n    private static final String MAIN_CHECK_NAME = \"出院西医诊断编码\";\r\n\r\n    private static final String MAIN_CHECK_REGION = \"四川,湖北\";\r\n    private static final String NWE_AGE_TYPE = \"1\";\r\n\r\n\r\n\r\n    /**\r\n     * 出院西医其他诊断(dscg_wm_other_dise)基础质控\r\n     * 判断入院时间是否为空\r\n     *\r\n     * @param wrap\r\n     * @return\r\n     */\r\n    @Override\r\n    public Void body(ScriptExecuteWrap wrap) {\r\n        SetlValidBaseBusiVo setlValidBaseBusiVo = (SetlValidBaseBusiVo) wrap.cmp.getFirstContextBean();\r\n        SettleListValidateVo settleListValidateVo = (SettleListValidateVo) wrap.cmp.getRequestData();\r\n        Map<String, String> keysBasicResultMap = setlValidBaseBusiVo.getKeysBasicResultMap();\r\n        SettleListHandlerVo settleListHandlerVo = new SettleListHandlerVo();\r\n        List<BusDiseaseDiagnosisTrim> busDiseaseDiagnosisTrimsList = settleListValidateVo.getBusDiseaseDiagnosisTrimsList();\r\n        SomHiInvyBasInfo somHiInvyBasInfo = settleListValidateVo.getSomHiInvyBasInfo();\r\n        // 检查 busDiseaseDiagnosisTrimsList 是否为空\r\n        if (SettleValidateUtil.isEmpty(busDiseaseDiagnosisTrimsList)) {\r\n            return null;\r\n        }\r\n        // 检查 somHiInvyBasInfo 是否为空\r\n        if (SettleValidateUtil.isEmpty(somHiInvyBasInfo)) {\r\n            return null;\r\n        }\r\n        //先获取 新生儿 诊断的目录\r\n      Map<String, Object>  nwbAgeMap = (Map<String, Object>) setlValidBaseBusiVo.getSettleListParams().get(SettleListValidateUtil.KEY_NWB_AGE_CODE);\r\n\r\n\r\n        boolean flag = false;\r\n        String code = null;\r\n        for (BusDiseaseDiagnosisTrim busDiseaseDiagnosisTrim : busDiseaseDiagnosisTrimsList) {\r\n            //说明此时诊断不为空\r\n            //判断 新生婴儿/早产婴儿，其他的与患者年龄不符\r\n            if (nwbAgeMap.containsKey(busDiseaseDiagnosisTrim.getC06c1())) {\r\n                //说明是新生儿，此时判断新生儿年龄\r\n                // 获取年龄天校验结果\r\n               flag = true;\r\n                code = busDiseaseDiagnosisTrim.getC06c1();\r\n            }\r\n        }\r\n        String a16_stas = (String) keysBasicResultMap.get(MAIN_CHECK_CODE);\r\n        Integer a16 =  somHiInvyBasInfo.getA16();\r\n       ;\r\n        if ( checkA16IsStandard(flag,a16_stas,a16)) {\r\n            addErrorDetail(SettleValidateConst.VALIDATE_TYPE_2, SettleValidateConst.VALIDATE_TYPE_2,\r\n                    MAIN_CHECK_NAME + \" : [\" + code +\r\n                            \"]为新生诊断,新生儿年龄[\"+a16 +\" 天]与规范[1天——28天]不符\", MAIN_CHECK_FIELD,\r\n                    SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n        }\r\n        return null;\r\n    }\r\n\r\n    private boolean checkA16IsStandard(boolean flag, String a16Stas, Integer a16) {\r\n        if(!flag){\r\n            return false;\r\n        }\r\n        if (!SettleValidateConst.PASS.equals(a16Stas)) {\r\n            return true;\r\n        }\r\n        if ( 1> a16 && a16 > 28) {\r\n            return true;\r\n        }\r\n        return false;\r\n    }\r\n\r\n    private void addErrorDetail(String index, String errType, String errDscr, String errorFields, String chkStas, SetlValidBaseBusiVo baseBusiVo) {\r\n        SettleListHandlerVo handlerVo = new SettleListHandlerVo();\r\n        handlerVo.setIndex(index);\r\n        handlerVo.setErrorFields(errorFields);\r\n        handlerVo.setErrDscr(errDscr);\r\n        handlerVo.setChkStas(chkStas);\r\n        handlerVo.setErrType(errType);\r\n        baseBusiVo.getErrorDetails().add(handlerVo);\r\n    }\r\n}', 'script', 'java', '2024-09-24 17:23:21', '出院西医其他诊断新生儿', '住院诊断', 1, 1);
INSERT INTO `som_chain_script` VALUES (62, 'somplatform', 'check_emp_addr_after_depth', '工作单位地址[a29]内涵质控', '\r\nimport com.my.som.model.dataHandle.SomHiInvyBasInfo;\r\nimport com.my.som.settlevalidate.constant.SettleValidateConst;\r\nimport com.my.som.settlevalidate.util.SettleValidateUtil;\r\nimport com.my.som.settlevalidate.vo.SetlValidBaseBusiVo;\r\nimport com.my.som.vo.dataHandle.SettleListHandlerVo;\r\nimport com.my.som.vo.dataHandle.SettleListValidateVo;\r\nimport com.yomahub.liteflow.script.ScriptExecuteWrap;\r\nimport com.yomahub.liteflow.script.body.JaninoCommonScriptBody;\r\nimport org.slf4j.Logger;\r\nimport org.slf4j.LoggerFactory;\r\nimport java.util.Map;\r\n\r\n/**\r\n * 职工医保时 单位地址不为空\r\n */\r\npublic class check_emp_addr_after_depth implements JaninoCommonScriptBody {\r\n    private static final Logger logger = LoggerFactory.getLogger(check_emp_addr_after_depth.class);\r\n\r\n    private static final String MAIN_CHECK_FIELD = \"a29\";\r\n    private static final String MAIN_CHECK_NAME = \"工作单位地址\";\r\n\r\n    private static final String MAIN_CHECK_FIELD_1 = \"a54\";\r\n    private static final String MAIN_CHECK_NAME_1 = \"医保类型\";\r\n    private static final String EMPLOYEE_MEDICARE_CODE = \"310\";\r\n\r\n    private static final String MAIN_CHECK_FIELD_2 = \"a29\";\r\n    private static final String MAIN_CHECK_NAME_2 = \"工作单位地址\";\r\n    private static final String MAIN_CHECK_REGION = \"四川\";\r\n    /**\r\n     * 事后校验\r\n     * 医保类型 逻辑质检\r\n     * 判断是否为职工医疗保险\r\n     * 如果是：\r\n     * 判断就诊前的工作地址是否为空(emp_addr)\r\n     *\r\n     * @param wrap\r\n     * @return\r\n     */\r\n    @Override\r\n    public Void body(ScriptExecuteWrap wrap) {\r\n        SetlValidBaseBusiVo setlValidBaseBusiVo = (SetlValidBaseBusiVo) wrap.cmp.getFirstContextBean();\r\n        SettleListValidateVo settleListValidateVo = (SettleListValidateVo) wrap.cmp.getRequestData();\r\n        Map<String, String> keysBasicResultMap = setlValidBaseBusiVo.getKeysBasicResultMap();\r\n        SomHiInvyBasInfo somHiInvyBasInfo = settleListValidateVo.getSomHiInvyBasInfo();\r\n        SettleListHandlerVo settleListHandlerVo = new SettleListHandlerVo();\r\n\r\n        // 检查 somHiInvyBasInfo 是否为空\r\n        if (SettleValidateUtil.isEmpty(somHiInvyBasInfo)) {\r\n            SettleValidateUtil.addErrorDetail(settleListHandlerVo, SettleValidateConst.VALIDATE_TYPE_2, SettleValidateConst.VALIDATE_TYPE_2,\r\n                    (String) SettleValidateConst.CHECK_TYPE_MAP.get(SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY), MAIN_CHECK_FIELD_2,\r\n                    SettleValidateConst.VALIDATE_STATE_FAIL, setlValidBaseBusiVo);\r\n            return null;\r\n        }\r\n\r\n        //获取医保类型质检结果\r\n        String mdeic_type_stas = (String) keysBasicResultMap.get(MAIN_CHECK_FIELD_1);\r\n        //获取工作单位地址质检结果\r\n        String emp_addr_stas = (String) keysBasicResultMap.get(MAIN_CHECK_FIELD_2);\r\n\r\n        if (SettleValidateConst.PASS.equals(mdeic_type_stas)) {\r\n            // 获取医保类型\r\n            String a54 = somHiInvyBasInfo.getA54();\r\n            if (EMPLOYEE_MEDICARE_CODE.equals(a54)) {\r\n\r\n                //判断当医保类型为职工医保时，工作单位地址不能为空\r\n                String a29 = somHiInvyBasInfo.getA29();\r\n                if (SettleValidateConst.NULL.equals(emp_addr_stas)) {\r\n                    SettleValidateUtil.addErrorDetail(settleListHandlerVo, SettleValidateConst.VALIDATE_TYPE_2, SettleValidateConst.VALIDATE_TYPE_1,\r\n                            MAIN_CHECK_NAME_2 + \"[\" + a29 + \"]为空\", MAIN_CHECK_FIELD_2,\r\n                            SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n                }\r\n            }\r\n        }\r\n        return null;\r\n    }\r\n}\r\n', 'script', 'java', '2024-09-24 17:23:21', '单位地址', '基本信息', 1, 1);
INSERT INTO `som_chain_script` VALUES (63, 'somplatform', 'check_emp_name_after_depth', '工作单位名称[a29n]内涵质控', '\r\nimport com.my.som.model.dataHandle.SomHiInvyBasInfo;\r\nimport com.my.som.settlevalidate.constant.SettleValidateConst;\r\nimport com.my.som.settlevalidate.script.base.check_coner_tel_basic;\r\nimport com.my.som.settlevalidate.util.SettleValidateUtil;\r\nimport com.my.som.settlevalidate.vo.SetlValidBaseBusiVo;\r\nimport com.my.som.vo.dataHandle.SettleListHandlerVo;\r\nimport com.my.som.vo.dataHandle.SettleListValidateVo;\r\nimport com.yomahub.liteflow.script.ScriptExecuteWrap;\r\nimport com.yomahub.liteflow.script.body.JaninoCommonScriptBody;\r\nimport org.slf4j.Logger;\r\nimport org.slf4j.LoggerFactory;\r\n\r\nimport java.util.Map;\r\n\r\n/**\r\n * 职工医保时 单位名称不为空\r\n */\r\npublic class check_emp_name_after_depth implements JaninoCommonScriptBody {\r\n    private static final Logger logger = LoggerFactory.getLogger(check_emp_name_after_depth.class);\r\n\r\n    private static final String MAIN_CHECK_FIELD = \"a29n\";\r\n    private static final String MAIN_CHECK_NAME  = \"工作单位名称\";\r\n    private static final String MAIN_CHECK_FIELD_1 = \"a54\";\r\n    private static final String MAIN_CHECK_NAME_1  = \"医保类型\";\r\n    private static final String EMPLOYEE_MEDICARE_CODE  = \"310\";\r\n    private static final String MAIN_CHECK_FIELD_2 = \"a29n\";\r\n    private static final String MAIN_CHECK_NAME_2  = \"工作单位名称\";\r\n    private static final String MAIN_CHECK_REGION = \"四川\";\r\n    /**\r\n     * 事后校验\r\n     *\r\n     * 判断是否为职工医疗保险\r\n     * 如果是：\r\n     * 判断就诊前的工作单位是否为空(emp_name)\r\n     * @param wrap\r\n     * @return\r\n     */\r\n    @Override\r\n    public Void body(ScriptExecuteWrap wrap) {\r\n        SetlValidBaseBusiVo setlValidBaseBusiVo = (SetlValidBaseBusiVo) wrap.cmp.getFirstContextBean();\r\n        SettleListValidateVo settleListValidateVo = (SettleListValidateVo) wrap.cmp.getRequestData();\r\n        Map<String, String> keysBasicResultMap = setlValidBaseBusiVo.getKeysBasicResultMap();\r\n        SomHiInvyBasInfo somHiInvyBasInfo = settleListValidateVo.getSomHiInvyBasInfo();\r\n        SettleListHandlerVo settleListHandlerVo = new SettleListHandlerVo();\r\n\r\n        // 检查 somHiInvyBasInfo 是否为空\r\n        if (SettleValidateUtil.isEmpty(somHiInvyBasInfo)) {\r\n            SettleValidateUtil.addErrorDetail(settleListHandlerVo, SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY,SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY,\r\n                    (String)SettleValidateConst.CHECK_TYPE_MAP.get(SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY),null,\r\n                    SettleValidateConst.VALIDATE_STATE_FAIL, setlValidBaseBusiVo);\r\n            return null;\r\n        }\r\n\r\n        //获取医保类型质检结果\r\n        String mdeic_type_stas = (String)keysBasicResultMap.get(MAIN_CHECK_FIELD_1) ;\r\n        //获取工作单位名称质检结果\r\n        String emp_name_stas = (String)keysBasicResultMap.get(MAIN_CHECK_FIELD_2) ;\r\n\r\n        if(SettleValidateConst.PASS.equals(mdeic_type_stas)){\r\n            // 获取医保类型\r\n            String a54 = somHiInvyBasInfo.getA54();\r\n            if(EMPLOYEE_MEDICARE_CODE.equals(a54)){\r\n                //判断当医保类型为职工医保时，工作单位不能为空\r\n                String a29n = somHiInvyBasInfo.getA29n();\r\n                if(SettleValidateConst.NULL.equals(emp_name_stas)){\r\n                    SettleValidateUtil.addErrorDetail(settleListHandlerVo, SettleValidateConst.VALIDATE_TYPE_2,SettleValidateConst.VALIDATE_TYPE_1,\r\n                            MAIN_CHECK_NAME_2 + \"[\" + a29n + \"]为空\", MAIN_CHECK_FIELD_2,\r\n                            SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n                }\r\n            }\r\n        }\r\n        return null;\r\n    }\r\n}\r\n', 'script', 'java', '2024-09-24 17:23:21', '单位名称', '基本信息', 1, 1);
INSERT INTO `som_chain_script` VALUES (64, 'somplatform', 'check_emp_tel_after_depth', '工作单位电话[a30]内涵质控', '\r\nimport com.my.som.model.dataHandle.SomHiInvyBasInfo;\r\nimport com.my.som.settlevalidate.constant.SettleValidateConst;\r\nimport com.my.som.settlevalidate.util.SettleValidateUtil;\r\nimport com.my.som.settlevalidate.vo.SetlValidBaseBusiVo;\r\nimport com.my.som.vo.dataHandle.SettleListHandlerVo;\r\nimport com.my.som.vo.dataHandle.SettleListValidateVo;\r\nimport com.yomahub.liteflow.script.ScriptExecuteWrap;\r\nimport com.yomahub.liteflow.script.body.JaninoCommonScriptBody;\r\nimport org.slf4j.Logger;\r\nimport org.slf4j.LoggerFactory;\r\nimport java.util.Map;\r\n\r\n/**\r\n * 职工医保时  单位电话不为空\r\n */\r\npublic class check_emp_tel_after_depth implements JaninoCommonScriptBody {\r\n    private static final Logger logger = LoggerFactory.getLogger(check_emp_tel_after_depth.class);\r\n    private static final String MAIN_CHECK_FIELD = \"a30\";\r\n    private static final String MAIN_CHECK_NAME = \"工作单位电话\";\r\n    private static final String MAIN_CHECK_FIELD_1 = \"a54\";\r\n    private static final String MAIN_CHECK_NAME_1  = \"医保类型\";\r\n    private static final String EMPLOYEE_MEDICARE_CODE  = \"310\";\r\n    private static final String MAIN_CHECK_FIELD_2 = \"a30\";\r\n    private static final String MAIN_CHECK_NAME_2 = \"工作单位电话\";\r\n    private static final String MAIN_CHECK_REGION = \"四川\";\r\n\r\n    /**\r\n     * 事后校验\r\n     * 医保类型 逻辑质检\r\n     * 判断是否为职工医疗保险\r\n     * 如果是：\r\n     * 判断就诊前的工作单位是否为空(emp_name)\r\n     * 判断就诊前的工作地址是否为空(emp_addr)\r\n     * @param wrap\r\n     * @return\r\n     */\r\n    @Override\r\n    public Void body(ScriptExecuteWrap wrap) {\r\n        SetlValidBaseBusiVo setlValidBaseBusiVo = (SetlValidBaseBusiVo) wrap.cmp.getFirstContextBean();\r\n        SettleListValidateVo settleListValidateVo = (SettleListValidateVo) wrap.cmp.getRequestData();\r\n        Map<String, String> keysBasicResultMap = setlValidBaseBusiVo.getKeysBasicResultMap();\r\n        SomHiInvyBasInfo somHiInvyBasInfo = settleListValidateVo.getSomHiInvyBasInfo();\r\n        SettleListHandlerVo settleListHandlerVo = new SettleListHandlerVo();\r\n\r\n        // 检查 somHiInvyBasInfo 是否为空\r\n        if (SettleValidateUtil.isEmpty(somHiInvyBasInfo)) {\r\n            SettleValidateUtil.addErrorDetail(settleListHandlerVo, SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY,SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY,\r\n                    (String) SettleValidateConst.CHECK_TYPE_MAP.get(SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY),MAIN_CHECK_FIELD_2,\r\n                    SettleValidateConst.VALIDATE_STATE_FAIL, setlValidBaseBusiVo);\r\n            return null;\r\n        }\r\n\r\n        //获取医保类型质检结果\r\n        String mdeic_type_stas = (String)keysBasicResultMap.get(MAIN_CHECK_FIELD_1) ;\r\n        //获取工作单位电话质检结果\r\n        String emp_tel_stas = (String)keysBasicResultMap.get(MAIN_CHECK_FIELD_2) ;\r\n\r\n        if(SettleValidateConst.PASS.equals(mdeic_type_stas)){\r\n            // 获取医保类型\r\n            String a54 = somHiInvyBasInfo.getA54();\r\n            if(EMPLOYEE_MEDICARE_CODE.equals(a54)){\r\n                //判断当医保类型为职工医保时，工作单位不能为空\r\n                String a30 = somHiInvyBasInfo.getA30();\r\n                if(SettleValidateConst.NULL.equals(emp_tel_stas)){\r\n                    SettleValidateUtil.addErrorDetail(settleListHandlerVo, SettleValidateConst.VALIDATE_TYPE_2,SettleValidateConst.VALIDATE_TYPE_1,\r\n                            MAIN_CHECK_NAME_2 + \"[\" + a30 + \"]为空\",MAIN_CHECK_FIELD_2,\r\n                            SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n                }\r\n            }\r\n        }\r\n        return null;\r\n    }\r\n}\r\n', 'script', 'java', '2024-09-24 17:23:21', '单位电话', '基本信息', 1, 1);
INSERT INTO `som_chain_script` VALUES (65, 'somplatform', 'check_ipt_days_after_depth', '实际住院天数[b20]内涵质控', 'import com.my.som.model.dataHandle.SomHiInvyBasInfo;\r\nimport com.my.som.model.dataHandle.SomOprnOprtInfo;\r\nimport com.my.som.settlevalidate.constant.SettleValidateConst;\r\nimport com.my.som.settlevalidate.util.SettleValidateUtil;\r\nimport com.my.som.settlevalidate.vo.SetlValidBaseBusiVo;\r\nimport com.my.som.vo.dataHandle.SettleListHandlerVo;\r\nimport com.my.som.vo.dataHandle.SettleListValidateVo;\r\nimport com.yomahub.liteflow.script.ScriptExecuteWrap;\r\nimport com.yomahub.liteflow.script.body.JaninoCommonScriptBody;\r\nimport org.slf4j.Logger;\r\nimport org.slf4j.LoggerFactory;\r\n\r\nimport java.text.ParseException;\r\nimport java.text.SimpleDateFormat;\r\nimport java.util.Date;\r\nimport java.util.List;\r\nimport java.util.Map;\r\nimport java.util.concurrent.TimeUnit;\r\n\r\n/**\r\n * 实际住院天数\r\n */\r\npublic class check_ipt_days_after_depth implements JaninoCommonScriptBody {\r\n    private static final Logger logger = LoggerFactory.getLogger(check_ipt_days_after_depth.class);\r\n    private static final String MAIN_CHECK_FIELD = \"b20\";\r\n    private static final String MAIN_CHECK_NAME = \"实际住院天数\";\r\n    private static final String MAIN_CHECK_REGION = \"四川,湖北\";\r\n    private static final String MAIN_CHECK_FIELD_1 = \"oprnOprtBegntime\";\r\n    private static final String MAIN_CHECK_NAME_1 = \"手术开始时间\";\r\n\r\n    private static final String MAIN_CHECK_FIELD_2 = \"oprnOprtEndtime\";\r\n    private static final String MAIN_CHECK_NAME_2 = \"手术结束时间\";\r\n\r\n\r\n    /**\r\n     * [实际住院天数-[b20]-基础校验]\r\n     * 1、手术与住院天数不符,\r\n     *\r\n     * @param wrap\r\n     * @return\r\n     */\r\n    @Override\r\n    public Void body(ScriptExecuteWrap wrap) {\r\n\r\n        SetlValidBaseBusiVo setlValidBaseBusiVo = (SetlValidBaseBusiVo) wrap.cmp.getFirstContextBean();\r\n        SettleListValidateVo settleListValidateVo = (SettleListValidateVo) wrap.cmp.getRequestData();\r\n        Map<String, String> keysBasicResultMap = setlValidBaseBusiVo.getKeysBasicResultMap();\r\n        SomHiInvyBasInfo somHiInvyBasInfo = settleListValidateVo.getSomHiInvyBasInfo();\r\n        SettleListHandlerVo settleListHandlerVo = new SettleListHandlerVo();\r\n\r\n        List<SomOprnOprtInfo> busOperateDiagnosisList = settleListValidateVo.getBusOperateDiagnosisList();\r\n        if (SettleValidateUtil.isEmpty(busOperateDiagnosisList)) {\r\n            return null;\r\n        }\r\n        if (SettleValidateUtil.isEmpty(somHiInvyBasInfo)) {\r\n            return null;\r\n        }\r\n        String b20 = somHiInvyBasInfo.getB20();\r\n        if (SettleValidateUtil.isEmpty(b20)) {\r\n            addErrorDetail(SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_ERROR_TYPE_2,\r\n                    \"存在手术时间但\" + MAIN_CHECK_NAME + \"[\" + b20 + \"]为空\", MAIN_CHECK_FIELD,\r\n                    SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n            return null;\r\n        }\r\n        String mimDate = \"9999-12-31 00:00:00\";\r\n        String maxDate = \"0000-01-01 00:00:00\";\r\n        boolean flag = false;\r\n\r\n        for (int i = 0; i < busOperateDiagnosisList.size(); i++) {\r\n            SomOprnOprtInfo somOprnOprtInfo = (SomOprnOprtInfo)busOperateDiagnosisList.get(i);\r\n            String oprnOprtBegntime = somOprnOprtInfo.getOprnOprtBegntime();\r\n            String oprnOprtEndtime = somOprnOprtInfo.getOprnOprtEndtime();\r\n            //如果手术时间不为空\r\n            if (SettleValidateConst.PASS.equals((String) keysBasicResultMap.get(i + MAIN_CHECK_FIELD_1))\r\n                    && SettleValidateConst.PASS.equals((String)keysBasicResultMap.get(i + MAIN_CHECK_FIELD_2))) {\r\n                keysBasicResultMap.put(i + MAIN_CHECK_FIELD_2, SettleValidateConst.PASS);\r\n                if (judgeTimeOK(oprnOprtBegntime, mimDate)) {\r\n                    mimDate = oprnOprtBegntime;\r\n                }\r\n                if (judgeTimeOK(maxDate, oprnOprtEndtime)) {\r\n                    maxDate = oprnOprtEndtime;\r\n                }\r\n                flag = true;\r\n            }\r\n        }\r\n        if (flag) {\r\n            long daysDiff = getDateDifferenceInDays(mimDate, maxDate);\r\n            if (daysDiff > Long.parseLong(b20)) {\r\n                addErrorDetail(SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_ERROR_TYPE_2,\r\n                        MAIN_CHECK_NAME + \"[\" + b20 + \"]与所有手术时间开始结束的时间差\" + daysDiff + \"不符\", MAIN_CHECK_FIELD,\r\n                        SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n            }\r\n        }\r\n        return null;\r\n    }\r\n\r\n    public static long getDateDifferenceInDays(String date1Str, String date2Str) {\r\n        SimpleDateFormat dateFormat = new SimpleDateFormat(\"yyyy-MM-dd HH:mm:ss\");\r\n        String formattedDifference = \"\";\r\n        try {\r\n            // 将时间字符串转换为 Date 对象\r\n            Date date1 = dateFormat.parse(date1Str);\r\n            Date date2 = dateFormat.parse(date2Str);\r\n\r\n            // 计算时间差（毫秒）\r\n            long differenceInMillis = date2.getTime() - date1.getTime();\r\n\r\n            // 将毫秒差值转换为天数、小时数和分钟数\r\n            long differenceInDays = TimeUnit.MILLISECONDS.toDays(differenceInMillis);\r\n            long remainderMillisAfterDays = differenceInMillis % TimeUnit.DAYS.toMillis(1);\r\n            long differenceInHours = TimeUnit.MILLISECONDS.toHours(remainderMillisAfterDays);\r\n\r\n            if (differenceInHours > 0 && differenceInDays == 0) {\r\n                differenceInDays = differenceInDays + 1;\r\n            }\r\n\r\n            // 格式化天数/小时数/分钟数字符串\r\n            formattedDifference = String.format(\"%d\", differenceInDays);\r\n\r\n        } catch (ParseException e) {\r\n            logger.error(\" 实际住院天数时间转换失败: \" + e.getMessage());\r\n        }\r\n        return Long.parseLong(formattedDifference);\r\n    }\r\n\r\n    /**\r\n     * 判断时间2 大于时间1\r\n     *\r\n     * @param time1\r\n     * @param time2\r\n     * @return\r\n     */\r\n    private boolean judgeTimeOK(String time1, String time2) {\r\n        SimpleDateFormat dateFormat = new SimpleDateFormat(\"yyyy-MM-dd HH:mm:ss\");\r\n        boolean flag = false;\r\n        try {\r\n            Date admDate = dateFormat.parse(time1);\r\n            Date dscgDate = dateFormat.parse(time2);\r\n            // 入院时间大于出院时间\r\n            if (dscgDate.after(admDate)) {\r\n                flag = true;\r\n            }\r\n        } catch (ParseException e) {\r\n            logger.error(\"实际住院天数 日期解析失败: \" + e.getMessage());\r\n        }\r\n        return flag;\r\n    }\r\n\r\n    private void addErrorDetail(String index, String errType, String errDscr, String errorFields, String chkStas, SetlValidBaseBusiVo baseBusiVo) {\r\n        SettleListHandlerVo handlerVo = new SettleListHandlerVo();\r\n        handlerVo.setIndex(index);\r\n        handlerVo.setErrorFields(errorFields);\r\n        handlerVo.setErrDscr(errDscr);\r\n        handlerVo.setChkStas(chkStas);\r\n        handlerVo.setErrType(errType);\r\n        baseBusiVo.getErrorDetails().add(handlerVo);\r\n    }\r\n}', 'script', 'java', '2024-09-24 17:23:21', '实际住院天数', '住院信息 ', 1, 1);
INSERT INTO `som_chain_script` VALUES (66, 'somplatform', 'check_nwb_adm_wt_depth', '新生儿入院体重[a17]内涵质控', '\r\nimport com.my.som.model.dataHandle.SomHiInvyBasInfo;\r\nimport com.my.som.settlevalidate.constant.SettleValidateConst;\r\nimport com.my.som.settlevalidate.util.SettleValidateUtil;\r\nimport com.my.som.settlevalidate.vo.SetlValidBaseBusiVo;\r\nimport com.my.som.vo.dataHandle.SettleListHandlerVo;\r\nimport com.my.som.vo.dataHandle.SettleListValidateVo;\r\nimport com.yomahub.liteflow.script.ScriptExecuteWrap;\r\nimport com.yomahub.liteflow.script.body.JaninoCommonScriptBody;\r\nimport org.slf4j.Logger;\r\nimport org.slf4j.LoggerFactory;\r\n\r\nimport java.util.Map;\r\n\r\npublic class check_nwb_adm_wt_depth implements JaninoCommonScriptBody {\r\n    private static final Logger logger = LoggerFactory.getLogger(check_nwb_adm_wt_depth.class);\r\n    private static final String MAIN_CHECK_FIELD = \"a17\";\r\n    private static final String MAIN_CHECK_NAME = \"新生儿入院体重\";\r\n    private static final String MAIN_CHECK_REGION = \"四川,湖北\";\r\n    /**\r\n     * 新生儿入院体重-[17]-深度校验\r\n     * 1、新生儿(天龄小于28天)入院时，新生儿入院体重必须填写\r\n     * 2、获取基础质控结果，判断是否需要走校验\r\n     *\r\n     * @param wrap\r\n     * @return\r\n     */\r\n    @Override\r\n    public Void body(ScriptExecuteWrap wrap) {\r\n        String main_check_field = \"a17\";\r\n        String main_check_name = \"新生儿入院体重\";\r\n        String tieup_field = \"a16\";\r\n        String tieup_field_name = \"年龄不足1周岁的年龄\";\r\n        SetlValidBaseBusiVo setlValidBaseBusiVo = (SetlValidBaseBusiVo) wrap.cmp.getFirstContextBean();\r\n        SettleListValidateVo settleListValidateVo = (SettleListValidateVo) wrap.cmp.getRequestData();\r\n        Map<String, String> keysBasicResultMap = setlValidBaseBusiVo.getKeysBasicResultMap();\r\n        SomHiInvyBasInfo somHiInvyBasInfo = settleListValidateVo.getSomHiInvyBasInfo();\r\n        SettleListHandlerVo settleListHandlerVo = new SettleListHandlerVo();\r\n        String main_field_baisc_stas = (String) keysBasicResultMap.get(main_check_field);\r\n\r\n        if (SettleValidateConst.NULL.equals(main_field_baisc_stas)) {\r\n            Double a17 = somHiInvyBasInfo.getA17();\r\n            //判断是否逻辑必填\r\n            checkA16LessThan28(somHiInvyBasInfo,settleListHandlerVo,main_check_field,main_check_name,a17, setlValidBaseBusiVo);\r\n        }\r\n        return null;\r\n    }\r\n\r\n    private static void checkA16LessThan28(SomHiInvyBasInfo somHiInvyBasInfo, SettleListHandlerVo settleListHandlerVo, String main_check_field, String main_check_name, Double a17, SetlValidBaseBusiVo setlValidBaseBusiVo) {\r\n        Integer a16 = somHiInvyBasInfo.getA16();\r\n        if (!SettleValidateUtil.isEmpty(a16)&&a16 <= 28 &&a16 > 0) {\r\n            //是新生儿，需要逻辑必填\r\n            settleListHandlerVo.setIndex(SettleValidateConst.VALIDATE_TYPE_3);\r\n            settleListHandlerVo.setSettleListId(somHiInvyBasInfo.getId());\r\n            settleListHandlerVo.setErrorFields(main_check_field);\r\n            settleListHandlerVo.setErrDscr((String) main_check_name + \"[\" + a17 + \"]，患者是新生儿时，\" + main_check_name + \"必填\");\r\n            settleListHandlerVo.setChkStas(SettleValidateConst.VALIDATE_STATE_SUCCESS);\r\n            settleListHandlerVo.setErrType(SettleValidateConst.VALIDATE_ERROR_TYPE_303);\r\n            setlValidBaseBusiVo.getErrorDetails().add(settleListHandlerVo);\r\n        }\r\n    }\r\n}\r\n', 'script', 'java', '2024-09-24 17:23:21', '新生儿入院体重', '基本数据', 0, 1);
INSERT INTO `som_chain_script` VALUES (67, 'somplatform', 'check_nwb_age_depth', '不足一周岁年龄(天)[a16]内涵质控', '\r\nimport com.my.som.model.dataHandle.SomHiInvyBasInfo;\r\nimport com.my.som.settlevalidate.constant.SettleValidateConst;\r\nimport com.my.som.settlevalidate.util.SettleValidateUtil;\r\nimport com.my.som.settlevalidate.vo.SetlValidBaseBusiVo;\r\nimport com.my.som.vo.dataHandle.SettleListHandlerVo;\r\nimport com.my.som.vo.dataHandle.SettleListValidateVo;\r\nimport com.yomahub.liteflow.script.ScriptExecuteWrap;\r\nimport com.yomahub.liteflow.script.body.JaninoCommonScriptBody;\r\nimport org.slf4j.Logger;\r\nimport org.slf4j.LoggerFactory;\r\n\r\nimport java.util.Map;\r\n\r\npublic class check_nwb_age_depth implements JaninoCommonScriptBody {\r\n    private static final Logger logger = LoggerFactory.getLogger(check_nwb_age_depth.class);\r\n\r\n    private static final String MAIN_CHECK_FIELD = \"a16\";\r\n    private static final String MAIN_CHECK_NAME = \"不足一周岁年龄(天)\";\r\n    private static final String MAIN_CHECK_FIELD_1 = \"a16\";\r\n    private static final String MAIN_CHECK_NAME_1 = \"不足一周岁年龄(天)\";\r\n    private static final String MAIN_CHECK_FIELD_2 = \"a14\";\r\n    private static final String MAIN_CHECK_NAME_2 = \"年龄(岁)\";\r\n    private static final String MAIN_CHECK_REGION = \"四川\";\r\n    /**\r\n     * 新生儿年龄(nwb_age)深度质控\r\n     * 1.年龄(岁)和年龄(天)不能同时存在\r\n     * 2.年龄(岁)为空时，年龄(天)不能同时为空\r\n     * @param wrap\r\n     * @return\r\n     */\r\n    @Override\r\n    public Void body(ScriptExecuteWrap wrap) {\r\n        SetlValidBaseBusiVo setlValidBaseBusiVo = (SetlValidBaseBusiVo) wrap.cmp.getFirstContextBean();\r\n        SettleListValidateVo settleListValidateVo = (SettleListValidateVo) wrap.cmp.getRequestData();\r\n        Map<String, String> keysBasicResultMap = setlValidBaseBusiVo.getKeysBasicResultMap();\r\n        SomHiInvyBasInfo somHiInvyBasInfo = settleListValidateVo.getSomHiInvyBasInfo();\r\n        SettleListHandlerVo settleListHandlerVo = new SettleListHandlerVo();\r\n\r\n        // 检查 somHiInvyBasInfo 是否为空\r\n        if (SettleValidateUtil.isEmpty(somHiInvyBasInfo)) {\r\n            addErrorDetail(settleListHandlerVo, SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY,SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY,\r\n                    null,\r\n                    (String)SettleValidateConst.CHECK_TYPE_MAP.get(SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY),\r\n                    SettleValidateConst.VALIDATE_STATE_FAIL, setlValidBaseBusiVo);\r\n            return null;\r\n        }\r\n\r\n        // 获取年龄天校验结果\r\n        String a16_stas = (String) keysBasicResultMap.get(MAIN_CHECK_FIELD_1);\r\n\r\n        // 获取年龄岁校验结果\r\n        String a14_stas = (String) keysBasicResultMap.get(MAIN_CHECK_FIELD_2);\r\n\r\n        // 同时存在时\r\n        if(SettleValidateConst.PASS.equals(a16_stas) && SettleValidateConst.PASS.equals(a14_stas) ){\r\n            addErrorDetail(settleListHandlerVo, SettleValidateConst.VALIDATE_TYPE_2,SettleValidateConst.VALIDATE_ERROR_TYPE_1,\r\n                    MAIN_CHECK_FIELD_2,\r\n                    MAIN_CHECK_NAME_1 + \"[\" + somHiInvyBasInfo.getA16() + \"]和\" + MAIN_CHECK_NAME_2 + \"[\" + somHiInvyBasInfo.getA14()  + \"] 不能同时存在\",\r\n                    SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n        }else if (SettleValidateConst.NULL.equals(a16_stas) && SettleValidateConst.NULL.equals(a14_stas)){\r\n            addErrorDetail(settleListHandlerVo, SettleValidateConst.VALIDATE_TYPE_2,SettleValidateConst.VALIDATE_ERROR_TYPE_1,\r\n                     MAIN_CHECK_FIELD_2,\r\n                    MAIN_CHECK_NAME_2 + \"[\" + somHiInvyBasInfo.getA14() + \"]为 0时\" + MAIN_CHECK_NAME_1 + \"[\" + somHiInvyBasInfo.getA16() + \"] 不能为空\",\r\n                    SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n        }\r\n\r\n        return null;\r\n    }\r\n\r\n    private void addErrorDetail(SettleListHandlerVo handlerVo, String index,String errType,String errorFields, String errDscr, String chkStas, SetlValidBaseBusiVo baseBusiVo) {\r\n        handlerVo.setIndex(index);\r\n        handlerVo.setErrDscr(errDscr);\r\n        handlerVo.setErrorFields(errorFields);\r\n        handlerVo.setChkStas(chkStas);\r\n        handlerVo.setErrType(errType);\r\n        baseBusiVo.getErrorDetails().add(handlerVo);\r\n    }\r\n}\r\n', 'script', 'java', '2024-09-24 17:23:21', '新生儿年龄', '基本数据', 0, 1);
INSERT INTO `som_chain_script` VALUES (68, 'somplatform', 'check_nwb_bir_wt_depth', '新生儿出生体重[a18]内涵质控', 'import com.my.som.model.dataHandle.SomHiInvyBasInfo;\r\nimport com.my.som.settlevalidate.constant.SettleValidateConst;\r\nimport com.my.som.settlevalidate.util.SettleValidateUtil;\r\nimport com.my.som.settlevalidate.vo.SetlValidBaseBusiVo;\r\nimport com.my.som.vo.dataHandle.SettleListHandlerVo;\r\nimport com.my.som.vo.dataHandle.SettleListValidateVo;\r\nimport com.yomahub.liteflow.script.ScriptExecuteWrap;\r\nimport com.yomahub.liteflow.script.body.JaninoCommonScriptBody;\r\nimport org.slf4j.Logger;\r\nimport org.slf4j.LoggerFactory;\r\n\r\nimport java.util.Map;\r\n\r\npublic class check_nwb_bir_wt_depth implements JaninoCommonScriptBody {\r\n    private static final Logger logger = LoggerFactory.getLogger(check_nwb_bir_wt_depth.class);\r\n    private static final String MAIN_CHECK_FIELD = \"a18\";\r\n    private static final String MAIN_CHECK_NAME = \"新生儿出生体重\";\r\n    private static final String MAIN_CHECK_REGION = \"四川,湖北\";\r\n    /**\r\n     * 新生儿出生体重-[18]-深度校验\r\n     * 1、新生儿(天龄小于28天nwb_age)入院时，新生儿出生体重必须填写。\r\n     * 2、获取基础质控结果，判断是否需要走校验\r\n     *\r\n     * @param wrap\r\n     * @return\r\n     */\r\n    @Override\r\n    public Void body(ScriptExecuteWrap wrap) {\r\n        String main_check_field = \"a18\";\r\n        String main_check_name = \"新生儿出生体重\";\r\n        String tieup_field = \"a16\";\r\n        String tieup_field_name = \"年龄不足1周岁的年龄\";\r\n        SetlValidBaseBusiVo setlValidBaseBusiVo = (SetlValidBaseBusiVo) wrap.cmp.getFirstContextBean();\r\n        SettleListValidateVo settleListValidateVo = (SettleListValidateVo) wrap.cmp.getRequestData();\r\n        Map<String, String> keysBasicResultMap = setlValidBaseBusiVo.getKeysBasicResultMap();\r\n        SomHiInvyBasInfo somHiInvyBasInfo = settleListValidateVo.getSomHiInvyBasInfo();\r\n        SettleListHandlerVo settleListHandlerVo = new SettleListHandlerVo();\r\n        if (SettleValidateUtil.isEmpty(somHiInvyBasInfo)) {\r\n            return null;\r\n        }\r\n        //新生儿体重\r\n        String main_field_baisc_stas = (String) keysBasicResultMap.get(main_check_field);\r\n        //多新生儿体重\r\n        String mulNwbBirWt_stas = (String) keysBasicResultMap.get(\"mulNwbBirWt\");\r\n        //获取关联字段基础校验结果\r\n        String tieup_field_baisc_stas = (String) keysBasicResultMap.get(tieup_field);\r\n\r\n        if (SettleValidateConst.NULL.equals(main_field_baisc_stas)\r\n                &&SettleValidateConst.NULL.equals(mulNwbBirWt_stas) ) {\r\n            //判断是否逻辑必填\r\n            if (isNewBaby(somHiInvyBasInfo, tieup_field_baisc_stas)) {\r\n                Double a18 = somHiInvyBasInfo.getA18();\r\n                //是新生儿，需要逻辑必填\r\n                settleListHandlerVo.setIndex(SettleValidateConst.VALIDATE_TYPE_3);\r\n                settleListHandlerVo.setSettleListId(somHiInvyBasInfo.getId());\r\n                settleListHandlerVo.setErrorFields(main_check_field);\r\n                settleListHandlerVo.setErrDscr((String) main_check_name + \"[\" + a18 + \"]，患者是新生儿时，\" + main_check_name + \"必填\");\r\n                settleListHandlerVo.setChkStas(SettleValidateConst.VALIDATE_STATE_SUCCESS);\r\n                settleListHandlerVo.setErrType(SettleValidateConst.VALIDATE_ERROR_TYPE_303);\r\n                setlValidBaseBusiVo.getErrorDetails().add(settleListHandlerVo);\r\n            }\r\n        }else {\r\n            if (!SettleValidateConst.NULL.equals(main_field_baisc_stas)\r\n                    &&!SettleValidateUtil.isEmpty(somHiInvyBasInfo.getMulNwbBirWt())){\r\n                settleListHandlerVo.setIndex(SettleValidateConst.VALIDATE_TYPE_3);\r\n                settleListHandlerVo.setSettleListId(somHiInvyBasInfo.getId());\r\n                settleListHandlerVo.setErrorFields(main_check_field);\r\n                settleListHandlerVo.setErrDscr((String) main_check_name + \"[\" + somHiInvyBasInfo.getA18() + \"]，和多新生儿体重[\"+ somHiInvyBasInfo.getMulNwbBirWt()+\"]不能同时存在\" );\r\n                settleListHandlerVo.setChkStas(SettleValidateConst.VALIDATE_STATE_SUCCESS);\r\n                settleListHandlerVo.setErrType(SettleValidateConst.VALIDATE_ERROR_TYPE_303);\r\n                setlValidBaseBusiVo.getErrorDetails().add(settleListHandlerVo);\r\n            }\r\n        }\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * 判断是否为新生儿【基础校验通过且新生儿天数<=28天】\r\n     *\r\n     * @param somHiInvyBasInfo\r\n     * @param tieup_field_baisc_stas\r\n     * @return\r\n     */\r\n    private boolean isNewBaby(SomHiInvyBasInfo somHiInvyBasInfo, String tieup_field_baisc_stas) {\r\n        if (SettleValidateConst.PASS.equals(tieup_field_baisc_stas) && somHiInvyBasInfo.getA16() <= 28  && somHiInvyBasInfo.getA16() > 0) {\r\n            return true;\r\n        }\r\n        return false;\r\n    }\r\n}', 'script', 'java', '2024-09-24 17:23:21', '新生儿出生体重', '基本数据', 0, 1);
INSERT INTO `som_chain_script` VALUES (69, 'somplatform', 'check_oprn_code_depth', '手术编码[c35c]内涵质控', 'import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;\r\nimport com.my.som.common.constant.DrgConst;\r\nimport com.my.som.common.util.ValidateUtil;\r\nimport com.my.som.model.dataHandle.SomHiInvyBasInfo;\r\nimport com.my.som.model.dataHandle.SomOprnOprtInfo;\r\nimport com.my.som.settlevalidate.constant.SettleValidateConst;\r\nimport com.my.som.settlevalidate.util.SettleValidateUtil;\r\nimport com.my.som.settlevalidate.vo.SetlValidBaseBusiVo;\r\nimport com.my.som.util.SettleListValidateUtil;\r\nimport com.my.som.vo.common.OprtDiffBodyParts;\r\nimport com.my.som.vo.dataHandle.SettleListDisSectionVo;\r\nimport com.my.som.vo.dataHandle.SettleListHandlerVo;\r\nimport com.my.som.vo.dataHandle.SettleListValidateVo;\r\nimport com.yomahub.liteflow.script.ScriptExecuteWrap;\r\nimport com.yomahub.liteflow.script.body.JaninoCommonScriptBody;\r\nimport org.slf4j.Logger;\r\nimport org.slf4j.LoggerFactory;\r\nimport java.util.*;\r\n\r\npublic class check_oprn_code_depth implements JaninoCommonScriptBody {\r\n    private static final Logger logger = LoggerFactory.getLogger(check_oprn_code_depth.class);\r\n    private static final String MAIN_CHECK_NAME = \"手术编码\";\r\n    private static final String MAIN_CHECK_FIELD = \"c35c\";\r\n    private static final String MAIN_CHECK_NAME_1 = \"手术编码\";\r\n    private static final String MAIN_CHECK_NAME_2 = \"手术名称\";\r\n\r\n    private static final String MAIN_CHECK_CODE1 = \"c35c\";\r\n    private static final String MAIN_CHECK_CODE2 = \"c36n\";\r\n    private static final String MAIN_CHECK_FIELD_1 = \"oprnOprtBegntime\";\r\n    private static final String MAIN_CHECK_FIELD_2 = \"oprnOprtEndtime\";\r\n\r\n    private static final String MAIN_CHECK_REGION = \"四川,湖北\";\r\n    private static final String REPEAT_OPRN_RIME_CODE = \"REPEAT_OPRN_RIME_CODE\";\r\n    /**\r\n     * 手术信息质控\r\n     * 1.判断手术代码与名称是否符合医保2.0版ICD-9标准。\r\n     * 2.判断手术代码是否存在医保停用码。\r\n     * 3.判断手术代码是否存在重复\r\n     *\r\n     * @param wrap\r\n     * @return\r\n     */\r\n    @Override\r\n    public Void body(ScriptExecuteWrap wrap) {\r\n\r\n        SetlValidBaseBusiVo setlValidBaseBusiVo = (SetlValidBaseBusiVo) wrap.cmp.getFirstContextBean();\r\n        SettleListValidateVo settleListValidateVo = (SettleListValidateVo) wrap.cmp.getRequestData();\r\n        Map<String, String> keysBasicResultMap = setlValidBaseBusiVo.getKeysBasicResultMap();\r\n        List<SomOprnOprtInfo> busOperateDiagnosisList = settleListValidateVo.getBusOperateDiagnosisList();\r\n        // 检查手术信息是否为空\r\n        if (SettleValidateUtil.isEmpty(busOperateDiagnosisList)) {\r\n            return null;\r\n        }\r\n        SomHiInvyBasInfo somHiInvyBasInfo = settleListValidateVo.getSomHiInvyBasInfo();\r\n        if (SettleValidateUtil.isEmpty(somHiInvyBasInfo)) {\r\n            return null;\r\n        }\r\n        // 获取清单字典和ICD-10相关映射\r\n        Map<String, Object> grepMap = (Map<String, Object>) setlValidBaseBusiVo.getSettleListParams().get(SettleListValidateUtil.KEY_ICD9_GRAY_CODE_2);\r\n        Map<String, Object> icd9Map = (Map<String, Object>) setlValidBaseBusiVo.getSettleListParams().get(SettleListValidateUtil.KEY_ICD9);\r\n\r\n\r\n        // 构造set判断是否存在重复编码\r\n        Set<String> oprnCodeSet = new HashSet<>();\r\n        Map<String, Object> dictMap = (Map<String, Object>) setlValidBaseBusiVo.getSettleListParams().get(SettleListValidateUtil.KEY_DIS_SECTION_LIST);\r\n\r\n        Map<String, String> oprtDiffBodyPartMap = getOprtDiffBodyPartMap(setlValidBaseBusiVo);\r\n\r\n        //主诊端与年龄需要判断的集合\r\n        Integer a14 = somHiInvyBasInfo.getA14();\r\n        List<String> checkAgeCodeList = getCheckAgeCodeList(a14, dictMap);\r\n\r\n\r\n        // 遍历操作列表\r\n        for (int i = 0; i < busOperateDiagnosisList.size(); i++) {\r\n            SomOprnOprtInfo somOprnOprtInfo = (SomOprnOprtInfo) busOperateDiagnosisList.get(i);\r\n            String oprnCode = somOprnOprtInfo.getC35c();\r\n            String oprnName = somOprnOprtInfo.getC36n();\r\n            //检测主诊段\r\n\r\n\r\n\r\n            if (SettleValidateUtil.isNotEmpty(oprnCode) && SettleValidateUtil.isNotEmpty(oprnCode)) {\r\n\r\n                if (checkAgeCodeList.size() > 0) {\r\n                    if (checkAgeCodeList.contains(oprnCode)) {\r\n                        addErrorDetail(SettleValidateConst.VALIDATE_TYPE_2, SettleValidateConst.VALIDATE_ERROR_TYPE_1,\r\n                                MAIN_CHECK_NAME_1,\r\n                                \"第\" + (i + 1) + \"个手术操作：编码为[\"+oprnCode+\"]的\" + \"患者年龄为\" + a14 + \"岁 大于等于 11岁，手术操作编码不应填报\" + MAIN_CHECK_NAME_1 + \"[\" + oprnCode + \"]\",\r\n                                SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n                    }\r\n                }\r\n                //判断手术部位一致\r\n                if (oprtDiffBodyPartMap.containsKey(oprnCode)) {\r\n                    String codes = (String) oprtDiffBodyPartMap.get(oprnCode);\r\n                    List<String> codeList = Arrays.asList(codes.split(\",\"));\r\n                    for (SomOprnOprtInfo newSomOprnOprtInfo : busOperateDiagnosisList) {\r\n                        String c35c = newSomOprnOprtInfo.getC35c().toUpperCase();\r\n                        for (String code : codeList){\r\n                            if (code.toUpperCase().equals(c35c)) {\r\n                                addErrorDetail(SettleValidateConst.VALIDATE_TYPE_2, SettleValidateConst.VALIDATE_ERROR_TYPE_1,\r\n                                        MAIN_CHECK_NAME_1,\r\n                                        \"第\" + (i + 1) + \"个手术操作：编码为[\"+oprnCode+\"]的\" + \" 编码 [\" + oprnCode + \"] 与 手术编码为 [\" + c35c + \"]的手术部位不匹配\",\r\n                                        SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n                            }\r\n                        }\r\n                    }\r\n                }\r\n\r\n                // 验证编码是否符合ICD-10标准\r\n                checkICD10Specification(icd9Map, oprnCode, i, setlValidBaseBusiVo, oprnName, grepMap);\r\n                // 判断是否有重复编码\r\n                checkRepeat(oprnCodeSet, oprnCode, i, setlValidBaseBusiVo,keysBasicResultMap);\r\n            } else {\r\n                if (SettleValidateUtil.isEmpty(oprnCode)) {\r\n                    addErrorDetail(SettleValidateConst.VALIDATE_TYPE_2, SettleValidateConst.VALIDATE_ERROR_TYPE_1,\r\n                            MAIN_CHECK_CODE1,\r\n                            \"第\" + (i + 1) + \"个手术操作：编码为[\"+oprnCode+\"]的\" +  MAIN_CHECK_NAME_1 + \"[\" + oprnCode + \"]为空 \",\r\n                            SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n                }\r\n                if (SettleValidateUtil.isEmpty(oprnName) ){\r\n                    addErrorDetail(SettleValidateConst.VALIDATE_TYPE_2, SettleValidateConst.VALIDATE_ERROR_TYPE_1,\r\n                            MAIN_CHECK_CODE2,\r\n                            \"第\" + (i + 1) + \"个手术操作：编码为[\"+oprnCode+\"]的\" + MAIN_CHECK_NAME_2 + \"[\" + oprnName + \"]为空 \",\r\n                            SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n                }\r\n            }\r\n\r\n        }\r\n\r\n        return null;\r\n    }\r\n\r\n\r\n\r\n    private Map<String, String> getOprtDiffBodyPartMap(SetlValidBaseBusiVo setlValidBaseBusiVo) {\r\n        Map<String, String> map = new HashMap<>();\r\n        List<OprtDiffBodyParts> oprtDiffBodyPartList = (List<OprtDiffBodyParts>) setlValidBaseBusiVo.getSettleListParams().get(SettleListValidateUtil.KEY_DIFF_BODY_PARTS);\r\n        for (OprtDiffBodyParts oprtDiffBodyParts : oprtDiffBodyPartList) {\r\n            map.put(oprtDiffBodyParts.getCleanCode(), oprtDiffBodyParts.getOprtCode());\r\n        }\r\n        return map;\r\n    }\r\n\r\n    private List<String> getCheckAgeCodeList(Integer a14, Map<String, Object> map) {\r\n        List<String> list = new ArrayList<>();\r\n        if (ObjectUtils.isNotEmpty(a14) && (a14 > 11 || a14 == 11)) {\r\n            List<SettleListDisSectionVo> settleListDisSectionVos = (List<SettleListDisSectionVo>) map.get(DrgConst.AGE_MORE_THAN_11Y);\r\n            for (SettleListDisSectionVo settleListDisSectionVo : settleListDisSectionVos) {\r\n                list.add(settleListDisSectionVo.getDiagSec());\r\n            }\r\n        }\r\n        return list;\r\n    }\r\n\r\n    private void checkRepeat(Set<String> oprnCodeSet, String oprnCode, int i, SetlValidBaseBusiVo setlValidBaseBusiVo, Map<String, String> keysBasicResultMap) {\r\n        if (!oprnCodeSet.add(oprnCode)) {\r\n            //此时需要判断联合手术这个时间\r\n            if(!ValidateUtil.isEmpty(keysBasicResultMap.get(REPEAT_OPRN_RIME_CODE))){\r\n                String codes = keysBasicResultMap.get(REPEAT_OPRN_RIME_CODE).toString();\r\n                List<String> codeList = Arrays.asList(codes.split(\"@\"));\r\n                if(codeList.contains(oprnCode)){\r\n                    addErrorDetail(SettleValidateConst.VALIDATE_TYPE_2, SettleValidateConst.VALIDATE_ERROR_TYPE_307,\r\n                            MAIN_CHECK_CODE1,\r\n                            \"第\" + (i + 1) + \"个手术操作：\"+  MAIN_CHECK_NAME_1 + \"[\" + oprnCode + \"]重复\",\r\n                            SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    private void checkICD10Specification(Map<String, Object> icd9Map, String oprnCode, int i, SetlValidBaseBusiVo setlValidBaseBusiVo, String oprnName, Map<String, Object> grepMap) {\r\n        if (!icd9Map.containsKey(oprnCode)) {\r\n            addErrorDetail(SettleValidateConst.VALIDATE_TYPE_2, SettleValidateConst.VALIDATE_ERROR_TYPE_5,\r\n                    MAIN_CHECK_CODE1,\r\n                    \"第\" + (i + 1) + \"个手术操作：编码为[\"+oprnCode+\"]的\" +  MAIN_CHECK_NAME_1 + \"[\" + oprnCode + \"]不符合医保ICD-9\",\r\n                    SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n        } else {\r\n            // 验证名称是否符合ICD-10标准\r\n            String icdName = (String) icd9Map.get(oprnCode);\r\n            if (!oprnName.equals(icdName)) {\r\n                addErrorDetail(SettleValidateConst.VALIDATE_TYPE_2, SettleValidateConst.VALIDATE_ERROR_TYPE_5,\r\n                        MAIN_CHECK_CODE1,\r\n                        \"第\" + (i + 1) + \"个手术操作：编码为[\"+oprnCode+\"]的\" +  MAIN_CHECK_NAME_2 + \"[\" + oprnName + \"]与医保编码[\" + oprnCode + \"]名称不符\",\r\n                        SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n            }\r\n        }\r\n        // 检查操作编码是否存在医保停用码\r\n        if (grepMap.containsKey(oprnCode)) {\r\n            addErrorDetail(SettleValidateConst.VALIDATE_TYPE_2, SettleValidateConst.VALIDATE_ERROR_TYPE_13,\r\n                    MAIN_CHECK_CODE1,\r\n                    \"第\" + (i + 1) + \"个手术操作：编码为[\"+oprnCode+\"]的\" +  MAIN_CHECK_NAME_1 + \"[\" + oprnCode + \"]是医保停用码\",\r\n                    SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n        }\r\n    }\r\n\r\n    private void addErrorDetail(String index, String errType, String errorFields, String errDscr, String chkStas, SetlValidBaseBusiVo baseBusiVo) {\r\n        SettleListHandlerVo handlerVo = new SettleListHandlerVo();\r\n        handlerVo.setIndex(index);\r\n        handlerVo.setErrorFields(errorFields);\r\n        handlerVo.setErrDscr(errDscr);\r\n        handlerVo.setChkStas(chkStas);\r\n        handlerVo.setErrType(errType);\r\n        baseBusiVo.getErrorDetails().add(handlerVo);\r\n    }\r\n}\r\n', 'script', 'java', '2024-09-24 17:23:21', '手术信息', '手术信息', 0, 1);
INSERT INTO `som_chain_script` VALUES (70, 'somplatform', 'check_oprn_merge_comb_after_depth', '手术联合编码[c35c]内涵质控', '\r\nimport com.my.som.common.util.ValidateUtil;\r\nimport com.my.som.model.dataHandle.SomOprnOprtInfo;\r\nimport com.my.som.settlevalidate.constant.SettleValidateConst;\r\nimport com.my.som.settlevalidate.vo.SetlValidBaseBusiVo;\r\nimport com.my.som.util.SettleListValidateUtil;\r\nimport com.my.som.vo.dataConfig.CombCodeVO;\r\nimport com.my.som.vo.dataHandle.SettleListHandlerVo;\r\nimport com.my.som.vo.dataHandle.SettleListValidateVo;\r\nimport com.yomahub.liteflow.script.ScriptExecuteWrap;\r\nimport com.yomahub.liteflow.script.body.JaninoCommonScriptBody;\r\nimport org.slf4j.Logger;\r\nimport org.slf4j.LoggerFactory;\r\n\r\nimport java.util.ArrayList;\r\nimport java.util.Arrays;\r\nimport java.util.List;\r\nimport java.util.Map;\r\n\r\npublic class check_oprn_merge_comb_after_depth implements JaninoCommonScriptBody {\r\n    private static final Logger logger = LoggerFactory.getLogger(check_oprn_merge_comb_after_depth.class);\r\n\r\n    private static final String MAIN_CHECK_FIELD = \"c35c\";\r\n    private static final String MAIN_CHECK_NAME = \"手术联合编码\";\r\n    private static final String MAIN_CHECK_REGION = \"四川,湖北\";\r\n    private static final String REPEAT_OPRN_RIME_CODE = \"REPEAT_OPRN_RIME_CODE\";\r\n    /**\r\n     * 操作联合编码校验\r\n     * @param wrap\r\n     * @return\r\n     */\r\n    @Override\r\n    public Void body(ScriptExecuteWrap wrap) {\r\n        SetlValidBaseBusiVo setlValidBaseBusiVo = (SetlValidBaseBusiVo) wrap.cmp.getFirstContextBean();\r\n        SettleListValidateVo settleListValidateVo = (SettleListValidateVo) wrap.cmp.getRequestData();\r\n        List<SomOprnOprtInfo> oprnOprtInfos = settleListValidateVo.getBusOperateDiagnosisList();\r\n        Map<String, String> keysBasicResultMap = setlValidBaseBusiVo.getKeysBasicResultMap();\r\n        if (!oprnOprtInfos.isEmpty()) {\r\n            List<String> oprns = new ArrayList<>();\r\n            for (SomOprnOprtInfo vo : oprnOprtInfos) {\r\n                oprns.add(vo.getC35c());\r\n            }\r\n\r\n            Map<String, Object> map = (Map<String, Object>) setlValidBaseBusiVo.getSettleListParams().get(SettleListValidateUtil.KEY_COMB_CODE);\r\n            List<CombCodeVO> combCodeVOS = (List<CombCodeVO>) map.get(SettleListValidateUtil.COMB_TYPE_OPRN);\r\n\r\n            for (CombCodeVO combCodeVO : combCodeVOS) {\r\n                List<String> combCodes = Arrays.asList(combCodeVO.getCombCodes().split(\";\"));\r\n                if (oprns.containsAll(combCodes)) {\r\n\r\n                    if(ValidateUtil.isEmpty(keysBasicResultMap.get(REPEAT_OPRN_RIME_CODE))){\r\n                        String codes = keysBasicResultMap.get(REPEAT_OPRN_RIME_CODE).toString();\r\n\r\n                        List<String> codeList = Arrays.asList(codes.split(\"@\"));\r\n                        if(codeList.containsAll(combCodes)) {\r\n                            // 存在联合编码合并时，进行质控提示\r\n                            addErrorDetail(SettleValidateConst.VALIDATE_TYPE_2,SettleValidateConst.VALIDATE_ERROR_TYPE_307,\r\n                                    MAIN_CHECK_FIELD,\r\n                                    \"手术操作编码\"+combCodes.toString() + \"需调整为联合操作编码[\" + combCodeVO.getGeneCode() + \"]\",\r\n                                    SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n\r\n                        }\r\n                    }                    //此时需要判断联合手术这个时间\r\n                 }\r\n            }\r\n        }\r\n\r\n        return null;\r\n    }\r\n\r\n    private void addErrorDetail(String index,String errType,String errorFields, String errDscr, String chkStas, SetlValidBaseBusiVo baseBusiVo) {\r\n        SettleListHandlerVo handlerVo = new SettleListHandlerVo();\r\n        handlerVo.setIndex(index);\r\n        handlerVo.setErrorFields(errorFields);\r\n        handlerVo.setErrDscr(errDscr);\r\n        handlerVo.setChkStas(chkStas);\r\n        handlerVo.setErrType(errType);\r\n        baseBusiVo.getErrorDetails().add(handlerVo);\r\n    }\r\n}\r\n', 'script', 'java', '2024-09-24 17:23:21', '操作信息', '手术诊断', 1, 1);
INSERT INTO `som_chain_script` VALUES (71, 'somplatform', 'check_oprn_oprt_time_after_depth', '手术时间[oprnOprtTime]内涵质控', 'import com.my.som.model.dataHandle.SomHiInvyBasInfo;\r\nimport com.my.som.model.dataHandle.SomOprnOprtInfo;\r\nimport com.my.som.settlevalidate.constant.SettleValidateConst;\r\nimport com.my.som.settlevalidate.util.SettleValidateUtil;\r\nimport com.my.som.settlevalidate.vo.SetlValidBaseBusiVo;\r\nimport com.my.som.vo.dataHandle.SettleListHandlerVo;\r\nimport com.my.som.vo.dataHandle.SettleListValidateVo;\r\nimport com.yomahub.liteflow.script.ScriptExecuteWrap;\r\nimport com.yomahub.liteflow.script.body.JaninoCommonScriptBody;\r\nimport org.slf4j.Logger;\r\nimport org.slf4j.LoggerFactory;\r\n\r\nimport java.text.ParseException;\r\nimport java.text.SimpleDateFormat;\r\nimport java.time.LocalDateTime;\r\nimport java.time.format.DateTimeFormatter;\r\nimport java.time.format.DateTimeParseException;\r\nimport java.util.*;\r\n\r\n/**\r\n * 事后判断 手术操作时间合理性\r\n */\r\npublic class check_oprn_oprt_time_after_depth implements JaninoCommonScriptBody {\r\n    private static final Logger logger = LoggerFactory.getLogger(check_oprn_oprt_time_after_depth.class);\r\n    private static final String MAIN_CHECK_FIELD = \"oprnOprtTime\";\r\n    private static final String MAIN_CHECK_NAME = \"手术时间\";\r\n    private static final String MAIN_CHECK_REGION = \"四川\";\r\n\r\n    private static final String MAIN_CHECK_FIELD_1 = \"oprnOprtBegntime\";\r\n    private static final String MAIN_CHECK_NAME_1 = \"手术开始时间\";\r\n\r\n    private static final String MAIN_CHECK_FIELD_2 = \"oprnOprtEndtime\";\r\n    private static final String MAIN_CHECK_NAME_2 = \"手术结束时间\";\r\n    private static final String MAIN_CHECK_FIELD_3 = \"b12\";\r\n    private static final String MAIN_CHECK_FIELD_4 = \"b15\";\r\n\r\n\r\n    /**\r\n     * 手术及操作起止时间\r\n     * <p>\r\n     * 1.出院时间 大于 手术结束时间 大于 手术开始时间 大于 入院时间按\r\n     * 2.判断手术代码是否存在医保停用码。\r\n     * 3.判断手术代码是否存在重复\r\n     *\r\n     * @param wrap\r\n     * @return\r\n     */\r\n    @Override\r\n    public Void body(ScriptExecuteWrap wrap) {\r\n\r\n        SetlValidBaseBusiVo setlValidBaseBusiVo = (SetlValidBaseBusiVo) wrap.cmp.getFirstContextBean();\r\n        SettleListValidateVo settleListValidateVo = (SettleListValidateVo) wrap.cmp.getRequestData();\r\n        List<SomOprnOprtInfo> busOperateDiagnosisList = settleListValidateVo.getBusOperateDiagnosisList();\r\n        SomHiInvyBasInfo somHiInvyBasInfo = settleListValidateVo.getSomHiInvyBasInfo();\r\n        SettleListHandlerVo settleListHandlerVo = new SettleListHandlerVo();\r\n        Map<String, String> keysBasicResultMap = setlValidBaseBusiVo.getKeysBasicResultMap();\r\n\r\n        // 检查 somHiInvyBasInfo 是否为空\r\n        if (SettleValidateUtil.isEmpty(somHiInvyBasInfo)) {\r\n            addErrorDetail(SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY, SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY,\r\n                    (String) SettleValidateConst.CHECK_TYPE_MAP.get(SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY), MAIN_CHECK_FIELD_2,\r\n                    SettleValidateConst.VALIDATE_STATE_FAIL, setlValidBaseBusiVo);\r\n            return null;\r\n        }\r\n\r\n        // 检查手术信息是否为空\r\n        if (SettleValidateUtil.isEmpty(busOperateDiagnosisList)) {\r\n            return null;\r\n        }\r\n        if (SettleValidateConst.PASS.equals((String) keysBasicResultMap.get(MAIN_CHECK_FIELD_3))\r\n                && SettleValidateConst.PASS.equals((String) keysBasicResultMap.get(MAIN_CHECK_FIELD_4))) {\r\n            //入院时间\r\n            String b12 = somHiInvyBasInfo.getB12();\r\n            //出院时间\r\n            String b15 = somHiInvyBasInfo.getB15();\r\n\r\n            // 遍历操作列表\r\n            for (int i = 0; i < busOperateDiagnosisList.size(); i++) {\r\n                SomOprnOprtInfo somOprnOprtInfo = (SomOprnOprtInfo)busOperateDiagnosisList.get(i);\r\n                String oprnOprtBegntime = somOprnOprtInfo.getOprnOprtBegntime();\r\n                String oprnOprtEndtime = somOprnOprtInfo.getOprnOprtEndtime();\r\n\r\n                //如果手术时间不为空\r\n                if (SettleValidateConst.PASS.equals((String) keysBasicResultMap.get(i + MAIN_CHECK_FIELD_1))\r\n                        && SettleValidateConst.PASS.equals((String) keysBasicResultMap.get(i + MAIN_CHECK_FIELD_2))) {\r\n                    //说明此时需要手术时间判断的规范性\r\n                    boolean isStandards = checkOprtTimestandards(i, oprnOprtBegntime, somOprnOprtInfo, setlValidBaseBusiVo, oprnOprtEndtime);\r\n                    if (!isStandards) {\r\n                        continue;\r\n                    }\r\n                    //判断手术时间合理性\r\n                    checkOprtIsplausible(i, b12, oprnOprtBegntime, somOprnOprtInfo, setlValidBaseBusiVo, oprnOprtEndtime, b15);\r\n                }\r\n            }\r\n        }\r\n        return null;\r\n    }\r\n\r\n    private void checkOprtIsplausible(int i, String b12, String oprnOprtBegntime, SomOprnOprtInfo somOprnOprtInfo, SetlValidBaseBusiVo setlValidBaseBusiVo, String oprnOprtEndtime, String b15) {\r\n        //手术开始时间应该大于入院时间\r\n        if (judgeTimeOK(oprnOprtBegntime, b12)) {\r\n            addErrorDetail(SettleValidateConst.VALIDATE_TYPE_2, SettleValidateConst.VALIDATE_TYPE_2,\r\n                    \"第\" + (i + 1) + \"个手术操作：编码为[\"+somOprnOprtInfo.getC35c()+\"]的\" + MAIN_CHECK_NAME_1 + \"[\" + oprnOprtBegntime + \"] 应该大于入院时间[\" + b12 + \"]\",\r\n                    MAIN_CHECK_FIELD_1,\r\n                    SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n        }\r\n\r\n\r\n        //手术结束时间应该小于出院时间\r\n        if (judgeTimeOK(b15,oprnOprtEndtime)) {\r\n            addErrorDetail(SettleValidateConst.VALIDATE_TYPE_2, SettleValidateConst.VALIDATE_TYPE_2,\r\n                    \"第\" + (i + 1) + \"个手术操作：编码为[\"+somOprnOprtInfo.getC35c()+\"]的\" +  MAIN_CHECK_NAME_2 + \"[\" + oprnOprtEndtime + \"] 应该小于出院时间[\" + b15 + \"]\",\r\n                    MAIN_CHECK_FIELD_2,\r\n                    SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n        }\r\n\r\n\r\n        //手术结束时间应该大于手术开始时间\r\n        if (judgeTimeOK(oprnOprtEndtime, oprnOprtBegntime)) {\r\n            addErrorDetail(SettleValidateConst.VALIDATE_TYPE_2, SettleValidateConst.VALIDATE_TYPE_2,\r\n                    \"第\" + (i + 1) + \"个手术操作：编码为[\"+somOprnOprtInfo.getC35c()+\"]的\" +  \"手术开始时间[\" + oprnOprtBegntime + \"]应该小于手术结束时间[\" + oprnOprtEndtime + \"]\",\r\n                    MAIN_CHECK_FIELD_1,\r\n                    SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n        }\r\n    }\r\n\r\n    private boolean checkOprtTimestandards(int i, String oprnOprtBegntime, SomOprnOprtInfo somOprnOprtInfo, SetlValidBaseBusiVo setlValidBaseBusiVo, String oprnOprtEndtime) {\r\n        boolean flag = true;\r\n        if (!isValidDateTime(oprnOprtBegntime)) {\r\n            addErrorDetail(SettleValidateConst.VALIDATE_TYPE_2, SettleValidateConst.VALIDATE_ERROR_TYPE_2,\r\n                    \"第\" + (i + 1) + \"个手术操作：编码为[\"+somOprnOprtInfo.getC35c()+\"]的\" +  MAIN_CHECK_NAME_1 + \"[\" + oprnOprtBegntime + \"] 格式错误\",\r\n                    MAIN_CHECK_FIELD_1,\r\n                    SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n            flag = false;\r\n        }\r\n\r\n        if (!isValidDateTime(oprnOprtEndtime)) {\r\n            addErrorDetail(SettleValidateConst.VALIDATE_TYPE_2, SettleValidateConst.VALIDATE_ERROR_TYPE_2,\r\n                    \"第\" + (i + 1) + \"个手术操作：编码为[\"+somOprnOprtInfo.getC35c()+\"]的\" +  MAIN_CHECK_NAME_2 + \"[\" + oprnOprtEndtime + \"] 格式错误\",\r\n                    MAIN_CHECK_FIELD_2,\r\n                    SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n            flag = false;\r\n        }\r\n        return flag;\r\n    }\r\n\r\n    public static boolean isValidDateTime(String dateTimeStr) {\r\n        String format = \"yyyy-MM-dd HH:mm:ss\";\r\n        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(format);\r\n        try {\r\n            LocalDateTime.parse(dateTimeStr, formatter);\r\n            return true;\r\n        } catch (DateTimeParseException e) {\r\n            return false;\r\n        }\r\n    }\r\n\r\n    private void addErrorDetail(String index, String errType, String errDscr, String errorFields, String chkStas, SetlValidBaseBusiVo baseBusiVo) {\r\n        SettleListHandlerVo handlerVo = new SettleListHandlerVo();\r\n        handlerVo.setIndex(index);\r\n        handlerVo.setErrorFields(errorFields);\r\n        handlerVo.setErrDscr(errDscr);\r\n        handlerVo.setChkStas(chkStas);\r\n        handlerVo.setErrType(errType);\r\n        baseBusiVo.getErrorDetails().add(handlerVo);\r\n    }\r\n\r\n    /**\r\n     * 时间2 大于  时间1  返回 true\r\n     *\r\n     * @param time1 时间1\r\n     * @param time2 时间2\r\n     * @return\r\n     */\r\n    private boolean judgeTimeOK(String time1, String time2) {\r\n        SimpleDateFormat dateFormat = new SimpleDateFormat(\"yyyy-MM-dd HH:mm:ss\");\r\n        boolean flag = false;\r\n        try {\r\n            Date admDate = dateFormat.parse(time1);\r\n            Date dscgDate = dateFormat.parse(time2);\r\n            // 入院时间大于出院时间\r\n            if (!dscgDate.before(admDate)) {\r\n                flag = true;\r\n            }\r\n        } catch (ParseException e) {\r\n            logger.error(\"手术时间质检 日期解析失败: \" + e.getMessage());\r\n        }\r\n        return flag;\r\n    }\r\n}\r\n', 'script', 'java', '2024-09-24 17:23:21', '手术操作时间', '手术信息', 1, 1);
INSERT INTO `som_chain_script` VALUES (73, 'somplatform', 'check_otp_wm_dise_code_after_depth', '门（急）诊诊断编码（西医）[c01c]内涵质控', '\r\nimport com.my.som.common.constant.DrgConst;\r\nimport com.my.som.model.dataHandle.SomHiInvyBasInfo;\r\nimport com.my.som.settlevalidate.constant.SettleValidateConst;\r\nimport com.my.som.settlevalidate.util.SettleValidateUtil;\r\nimport com.my.som.settlevalidate.vo.SetlValidBaseBusiVo;\r\nimport com.my.som.util.SettleListValidateUtil;\r\nimport com.my.som.vo.dataHandle.SettleListDisSectionVo;\r\nimport com.my.som.vo.dataHandle.SettleListHandlerVo;\r\nimport com.my.som.vo.dataHandle.SettleListValidateVo;\r\nimport com.yomahub.liteflow.script.ScriptExecuteWrap;\r\nimport com.yomahub.liteflow.script.body.JaninoCommonScriptBody;\r\nimport org.slf4j.Logger;\r\nimport org.slf4j.LoggerFactory;\r\n\r\nimport java.text.ParseException;\r\nimport java.text.SimpleDateFormat;\r\nimport java.util.*;\r\nimport java.util.concurrent.TimeUnit;\r\n\r\n/**\r\n * 判断 门（急）诊诊断（西医诊断编码）\r\n */\r\npublic class check_otp_wm_dise_code_after_depth implements JaninoCommonScriptBody {\r\n    private static final Logger logger = LoggerFactory.getLogger(check_otp_wm_dise_code_after_depth.class);\r\n\r\n    private static final String MAIN_CHECK_FIELD = \"c01c\";\r\n    private static final String MAIN_CHECK_NAME = \"门（急）诊诊断编码（西医）\";\r\n    private static final String MAIN_CHECK_REGION = \"四川,湖北\";\r\n\r\n    private static final String MAIN_CHECK_FIELD_2 = \"c02n\";\r\n    private static final String MAIN_CHECK_NAME_2 = \"门（急）诊诊断名称（西医）\";\r\n    private static final List<String> ERROR_CODE_BEGIN_LIST = Arrays.asList(\"V\", \"W\", \"X\", \"Y\");\r\n\r\n    /**\r\n     * 事后校验\r\n     * 门（急）诊诊断编码（西医）(otp_wm_dise_name)基础质控\r\n     * 判断门（急）诊诊断编码（西医）是否为空\r\n     *\r\n     * @param wrap\r\n     * @return\r\n     */\r\n    @Override\r\n    public Void body(ScriptExecuteWrap wrap) {\r\n        SetlValidBaseBusiVo setlValidBaseBusiVo = (SetlValidBaseBusiVo) wrap.cmp.getFirstContextBean();\r\n        SettleListValidateVo settleListValidateVo = (SettleListValidateVo) wrap.cmp.getRequestData();\r\n        Map<String, String> keysBasicResultMap = setlValidBaseBusiVo.getKeysBasicResultMap();\r\n        SomHiInvyBasInfo somHiInvyBasInfo = settleListValidateVo.getSomHiInvyBasInfo();\r\n        SettleListHandlerVo settleListHandlerVo = new SettleListHandlerVo();\r\n        \r\n\r\n        // 检查 somHiInvyBasInfo 是否为空\r\n        if (SettleValidateUtil.isEmpty(somHiInvyBasInfo)) {\r\n            addErrorDetail(SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY, SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY,\r\n                    (String) SettleValidateConst.CHECK_TYPE_MAP.get(SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY), null,\r\n                    SettleValidateConst.VALIDATE_STATE_FAIL, setlValidBaseBusiVo);\r\n            return null;\r\n        }\r\n\r\n        // 获取门（急）诊诊断名称（西医）\r\n        String c01c = somHiInvyBasInfo.getC01c();\r\n        String c02n = somHiInvyBasInfo.getC02n();\r\n        if (SettleValidateConst.PASS.equals(keysBasicResultMap.get(MAIN_CHECK_FIELD))) {\r\n            // 判断其他诊断是否和开头是否符合\r\n            checkDiagnosisCodeConsistency(c01c, settleListHandlerVo, setlValidBaseBusiVo, keysBasicResultMap);\r\n\r\n            // 判断其他诊断是否和性别相符合\r\n            String a12c = settleListValidateVo.getSomHiInvyBasInfo().getA12c();\r\n            checkDiagnosisGenderConsistency(a12c, c01c,settleListHandlerVo,setlValidBaseBusiVo);\r\n\r\n            //判断 是否为医保2.0版ICD-10里的标准诊断名称\r\n            checkDiagnosisStandConsistency(setlValidBaseBusiVo,c01c,c02n);\r\n            checkDiagCodeIsGrep(setlValidBaseBusiVo,c01c);\r\n            //判断 出现P10～P15，入院日期减出生日期必须<365天。\r\n            checkDiagnosisAgeConsistency(c01c, somHiInvyBasInfo,setlValidBaseBusiVo);\r\n        }\r\n        return null;\r\n    }\r\n\r\n    private void checkDiagnosisAgeConsistency(String c01c,  SomHiInvyBasInfo somHiInvyBasInfo , SetlValidBaseBusiVo setlValidBaseBusiVo) {\r\n        if (SettleListValidateUtil.P10TP15_LIST.contains(c01c.substring(0,3))) {\r\n            String b12 = somHiInvyBasInfo.getB12();\r\n            String a13 = somHiInvyBasInfo.getA13();\r\n            if (SettleValidateUtil.isNotEmpty(b12) && SettleValidateUtil.isNotEmpty(a13) ) {\r\n                //判断入院日期减出生日期必须<365天。\r\n                long  diffDate = getDateDifferenceInDays(a13,b12 );\r\n                if (diffDate > 365) {\r\n                    //不在合理值范围，提示\r\n                    addErrorDetail(SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_ERROR_TYPE_2,\r\n                            \"门(急)诊诊断编码出现P10-P15入院日期减出生日期必须<365天\",   MAIN_CHECK_FIELD,\r\n                            SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n                }\r\n            } else {\r\n                if(SettleValidateUtil.isEmpty(b12) ){\r\n                addErrorDetail(SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_ERROR_TYPE_1,\r\n                        \"门(急)诊诊断编码出现P10-P15年龄 入院时间\" + \"[\" + b12 + \"]为空\",\"b12\",\r\n                        SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);}\r\n\r\n                if(SettleValidateUtil.isEmpty(a13) ){\r\n                    addErrorDetail(SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_ERROR_TYPE_1,\r\n                            \"门(急)诊诊断编码出现P10-P15年龄 出生日期\" + \"[\" + a13 + \"]为空\",\"a13\",\r\n                            SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);}\r\n            }\r\n        }\r\n    }\r\n\r\n    public static long getDateDifferenceInDays(String date1Str, String date2Str) {\r\n        SimpleDateFormat dateFormat = new SimpleDateFormat(\"yyyy-MM-dd HH:mm:ss\");\r\n        String formattedDifference = \"\";\r\n        try {\r\n            // 将时间字符串转换为 Date 对象\r\n            Date date1 = dateFormat.parse(date1Str);\r\n            Date date2 = dateFormat.parse(date2Str);\r\n\r\n            // 计算时间差（毫秒）\r\n            long differenceInMillis = date2.getTime()-date1.getTime() ;\r\n\r\n            // 将毫秒差值转换为天数、小时数和分钟数\r\n            long differenceInDays = TimeUnit.MILLISECONDS.toDays(differenceInMillis);\r\n            long remainderMillisAfterDays = differenceInMillis % TimeUnit.DAYS.toMillis(1);\r\n            long differenceInHours = TimeUnit.MILLISECONDS.toHours(remainderMillisAfterDays);\r\n\r\n            if(differenceInHours > 0  ){\r\n                differenceInDays =differenceInDays +1;\r\n            }\r\n\r\n            // 格式化天数/小时数/分钟数字符串\r\n            formattedDifference = String.format(\"%d\",differenceInDays);\r\n\r\n        } catch (ParseException e) {\r\n            logger.error(\" 计算差失败: \" + e.getMessage());\r\n        }\r\n        return Long.parseLong(formattedDifference);\r\n    }\r\n\r\n    private void checkDiagnosisStandConsistency(SetlValidBaseBusiVo setlValidBaseBusiVo,String diagCode,  String diagName ) {\r\n        Map<String, Object> icd10Map = (Map<String, Object>) setlValidBaseBusiVo.getSettleListParams().get(SettleListValidateUtil.KEY_ICD10);\r\n\r\n        // 验证编码是否符合ICD-10标准\r\n        if (!icd10Map.containsKey(diagCode)) {\r\n            addErrorDetail(SettleValidateConst.VALIDATE_TYPE_3,SettleValidateConst.VALIDATE_ERROR_TYPE_6,\r\n       MAIN_CHECK_NAME + \"[\" + diagCode + \"]不符合医保2.0\",            MAIN_CHECK_FIELD,\r\n                    SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n        } else {\r\n            // 验证名称是否符合ICD-10标准\r\n            String icdName = (String) icd10Map.get(diagCode);\r\n            if (!diagName.equals(icdName)) {\r\n                addErrorDetail(SettleValidateConst.VALIDATE_TYPE_3,SettleValidateConst.VALIDATE_ERROR_TYPE_6,\r\n                        MAIN_CHECK_NAME_2 + \"[\" + diagName + \"]不符合医保2.0,应改为[\"+icdName+\"]\",    MAIN_CHECK_FIELD_2,\r\n                        SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n            }\r\n        }\r\n    }\r\n    private void checkDiagCodeIsGrep(SetlValidBaseBusiVo setlValidBaseBusiVo,String diagCode) {\r\n        Map<String, Object> grepMap = (Map<String, Object>) setlValidBaseBusiVo.getSettleListParams().get(SettleListValidateUtil.KEY_ICD10_GRAY_CODE_2);\r\n\r\n        // 检查主要诊断是否存在医保停用码\r\n        if (grepMap.containsKey(diagCode)) {\r\n            addErrorDetail(SettleValidateConst.VALIDATE_TYPE_2, SettleValidateConst.VALIDATE_ERROR_TYPE_12,\r\n\r\n                    MAIN_CHECK_NAME + \"[\" + diagCode + \"]是医保停用码\",    MAIN_CHECK_FIELD,\r\n                    SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n        }\r\n    }\r\n\r\n\r\n        private void checkDiagnosisCodeConsistency(String c01c, SettleListHandlerVo settleListHandlerVo, SetlValidBaseBusiVo setlValidBaseBusiVo, Map<String, String> keysBasicResultMap) {\r\n        for(String str : ERROR_CODE_BEGIN_LIST){\r\n            if (c01c.startsWith(str)){\r\n                addErrorDetail(SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_TYPE_1,\r\n                        MAIN_CHECK_NAME + \"[\" + c01c + \"]应填写A~U开头和Z开头的编码及名称\",  MAIN_CHECK_FIELD,\r\n                        SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n            }\r\n        }\r\n    }\r\n    private void checkDiagnosisGenderConsistency(String gender, String c01c,SettleListHandlerVo settleListHandlerVo,SetlValidBaseBusiVo setlValidBaseBusiVo) {\r\n        Map<String, Object> map = (Map<String, Object>) setlValidBaseBusiVo.getSettleListParams().get(SettleListValidateUtil.KEY_DIS_SECTION_LIST);\r\n\r\n        if (DrgConst.MAN.equals(gender)) {\r\n            List<SettleListDisSectionVo> settleListDisSectionVos = (List<SettleListDisSectionVo>) map.get(DrgConst.MAN);\r\n            List<String> femaleDiagSections = new ArrayList<>();\r\n            for (SettleListDisSectionVo settleListDisSectionVo : settleListDisSectionVos) {\r\n                femaleDiagSections.add(settleListDisSectionVo.getDiagSec());\r\n            }\r\n            checkGenderIsSame(c01c, setlValidBaseBusiVo, femaleDiagSections);\r\n        } else if (DrgConst.WOMAN.equals(gender)) {\r\n            List<SettleListDisSectionVo> settleListDisSectionVos = (List<SettleListDisSectionVo>) map.get(DrgConst.WOMAN);\r\n            List<String> maleDiagSections = new ArrayList<>();\r\n            for (SettleListDisSectionVo settleListDisSectionVo : settleListDisSectionVos) {\r\n                maleDiagSections.add(settleListDisSectionVo.getDiagSec());\r\n            }\r\n            checkGenderIsSame(c01c, setlValidBaseBusiVo, maleDiagSections);\r\n        }\r\n    }\r\n\r\n    private void checkGenderIsSame(String c01c, SetlValidBaseBusiVo setlValidBaseBusiVo, List<String> maleDiagSections) {\r\n\r\n        if (maleDiagSections.contains(c01c.substring(0, 3)) || maleDiagSections.contains(c01c.substring(0, 5))) {\r\n            addErrorDetail(SettleValidateConst.VALIDATE_TYPE_2, SettleValidateConst.VALIDATE_ERROR_TYPE_306,\r\n                    MAIN_CHECK_NAME + \"[\" + c01c + \"]与性别不符\",              MAIN_CHECK_FIELD,\r\n                    SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n        }\r\n    }\r\n\r\n    private void addErrorDetail( String index, String errType, String errDscr, String errorFields, String chkStas, SetlValidBaseBusiVo baseBusiVo) {\r\n        SettleListHandlerVo handlerVo =new SettleListHandlerVo();\r\n        handlerVo.setIndex(index);\r\n        handlerVo.setErrDscr(errDscr);\r\n        handlerVo.setErrorFields(errorFields);\r\n        handlerVo.setChkStas(chkStas);\r\n        handlerVo.setErrType(errType);\r\n        baseBusiVo.getErrorDetails().add(handlerVo);\r\n    }\r\n}\r\n', 'script', 'java', '2024-09-24 17:23:21', '门(急)诊(西医)诊断', '基础信息 ', 1, 1);
INSERT INTO `som_chain_script` VALUES (74, 'somplatform', 'check_pos_code_after_depth', '工作单位邮政编码[a31c]内涵质控', '\r\nimport com.my.som.model.dataHandle.SomHiInvyBasInfo;\r\nimport com.my.som.settlevalidate.constant.SettleValidateConst;\r\nimport com.my.som.settlevalidate.util.SettleValidateUtil;\r\nimport com.my.som.settlevalidate.vo.SetlValidBaseBusiVo;\r\nimport com.my.som.vo.dataHandle.SettleListHandlerVo;\r\nimport com.my.som.vo.dataHandle.SettleListValidateVo;\r\nimport com.yomahub.liteflow.script.ScriptExecuteWrap;\r\nimport com.yomahub.liteflow.script.body.JaninoCommonScriptBody;\r\nimport org.slf4j.Logger;\r\nimport org.slf4j.LoggerFactory;\r\nimport java.util.Map;\r\n\r\n/**\r\n * 职工医保时 单位邮编不为空\r\n */\r\npublic class check_pos_code_after_depth implements JaninoCommonScriptBody {\r\n    private static final Logger logger = LoggerFactory.getLogger(check_pos_code_after_depth.class);\r\n\r\n    private static final String MAIN_CHECK_FIELD_1 = \"a54\";\r\n    private static final String MAIN_CHECK_NAME_1 = \"医保类型\";\r\n    private static final String EMPLOYEE_MEDICARE_CODE  = \"310\";\r\n\r\n    private static final String MAIN_CHECK_FIELD = \"a31c\";\r\n    private static final String MAIN_CHECK_NAME = \"工作单位邮政编码\";\r\n    private static final String MAIN_CHECK_REGION = \"四川\";\r\n    /**\r\n     * 事后校验\r\n     * 医保类型 逻辑质检\r\n     * 判断是否为职工医疗保险\r\n     * 如果是：\r\n     * 判断就诊前的工作单位邮政编码是否为空(poscode)\r\n     * @param wrap\r\n     * @return\r\n     */\r\n    @Override\r\n    public Void body(ScriptExecuteWrap wrap) {\r\n        SetlValidBaseBusiVo setlValidBaseBusiVo = (SetlValidBaseBusiVo) wrap.cmp.getFirstContextBean();\r\n        SettleListValidateVo settleListValidateVo = (SettleListValidateVo) wrap.cmp.getRequestData();\r\n        Map<String, String> keysBasicResultMap = setlValidBaseBusiVo.getKeysBasicResultMap();\r\n        SomHiInvyBasInfo somHiInvyBasInfo = settleListValidateVo.getSomHiInvyBasInfo();\r\n        SettleListHandlerVo settleListHandlerVo = new SettleListHandlerVo();\r\n\r\n        // 检查 somHiInvyBasInfo 是否为空\r\n        if (SettleValidateUtil.isEmpty(somHiInvyBasInfo)) {\r\n            SettleValidateUtil.addErrorDetail(settleListHandlerVo, SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY,SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY,\r\n                    (String)SettleValidateConst.CHECK_TYPE_MAP.get(SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY),MAIN_CHECK_FIELD,\r\n                    SettleValidateConst.VALIDATE_STATE_FAIL, setlValidBaseBusiVo);\r\n            return null;\r\n        }\r\n\r\n        //获取医保类型质检结果\r\n        String mdeic_type_stas = (String)keysBasicResultMap.get(MAIN_CHECK_FIELD_1) ;\r\n        //获取工作单位邮编检结果\r\n        String poscode_stas = (String)keysBasicResultMap.get(MAIN_CHECK_FIELD) ;\r\n\r\n        if(SettleValidateConst.PASS.equals(mdeic_type_stas)){\r\n            // 获取医保类型\r\n            String a54 = somHiInvyBasInfo.getA54();\r\n            if(EMPLOYEE_MEDICARE_CODE.equals(a54)){\r\n                //判断当医保类型为职工医保时，工作单位邮编不能为空\r\n                String a31c = somHiInvyBasInfo.getA31c();\r\n                if(SettleValidateConst.NULL.equals(poscode_stas)){\r\n                    SettleValidateUtil.addErrorDetail(settleListHandlerVo, SettleValidateConst.VALIDATE_TYPE_2,SettleValidateConst.VALIDATE_TYPE_1,\r\n                            MAIN_CHECK_NAME + \"[\" + a31c + \"]为空\", MAIN_CHECK_FIELD,\r\n                            SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n                }\r\n                else {\r\n                    //此时说明工作邮编有值\r\n                    //判断是否为阿拉伯数字,同时是否审核字段为6位\r\n                    if(!a31c.matches(SettleValidateUtil.POS_CODE_PATTERN)){\r\n                        SettleValidateUtil.addErrorDetail(settleListHandlerVo, SettleValidateConst.VALIDATE_TYPE_2,SettleValidateConst.VALIDATE_TYPE_2,\r\n                                MAIN_CHECK_NAME + \"[\" + a31c + \"]格式错误\",MAIN_CHECK_FIELD,\r\n                                SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n                    }\r\n                }\r\n            }\r\n        }\r\n        return null;\r\n    }\r\n}\r\n', 'script', 'java', '2024-09-24 17:23:21', '单位邮编', '基本信息', 1, 1);
INSERT INTO `som_chain_script` VALUES (75, 'somplatform', 'check_scs_cutd_sum_dura_after_depth', '重症监护合计时长[scsCutdSumDura]内涵质控', 'import com.my.som.common.util.ValidateUtil;\r\nimport com.my.som.model.dataHandle.SomHiInvyBasInfo;\r\nimport com.my.som.model.medicalQuality.SomSetlInvyScsCutdInfo;\r\nimport com.my.som.settlevalidate.constant.SettleValidateConst;\r\nimport com.my.som.settlevalidate.util.SettleValidateUtil;\r\nimport com.my.som.settlevalidate.vo.SetlValidBaseBusiVo;\r\nimport com.my.som.vo.dataHandle.SettleListHandlerVo;\r\nimport com.my.som.vo.dataHandle.SettleListValidateVo;\r\nimport com.yomahub.liteflow.script.ScriptExecuteWrap;\r\nimport com.yomahub.liteflow.script.body.JaninoCommonScriptBody;\r\nimport org.slf4j.Logger;\r\nimport org.slf4j.LoggerFactory;\r\n\r\nimport java.text.ParseException;\r\nimport java.text.SimpleDateFormat;\r\nimport java.util.Date;\r\nimport java.util.List;\r\nimport java.util.regex.Matcher;\r\nimport java.util.regex.Pattern;\r\nimport java.util.Map;\r\nimport java.util.concurrent.TimeUnit;\r\n\r\n/**\r\n * 事后判断 重症监护合计时长\r\n */\r\npublic class check_scs_cutd_sum_dura_after_depth implements JaninoCommonScriptBody {\r\n    private static final Logger logger = LoggerFactory.getLogger(check_scs_cutd_sum_dura_after_depth.class);\r\n\r\n    private static final String MAIN_CHECK_FIELD = \"scsCutdSumDura\";\r\n    private static final String MAIN_CHECK_NAME = \"重症监护合计时长\";\r\n    private static final String MAIN_CHECK_FIELD_1 = \"scsCutdInpoolTime\";\r\n    private static final String MAIN_CHECK_NAME_1 = \"重症监护进入时间\";\r\n    private static final String MAIN_CHECK_FIELD_2 = \"scsCutdExitTime\";\r\n    private static final String MAIN_CHECK_NAME_2 = \"重症监护退出时间\";\r\n    private static final Pattern PATTERN = Pattern.compile(SettleValidateUtil.SCS_CUTD_SUM_DURA_PATTERN);\r\n    private static final String MAIN_CHECK_FIELD_3 = \"b12\";\r\n    private static final String MAIN_CHECK_FIELD_4 = \"b15\";\r\n    private static final String MAIN_CHECK_NAME_3 = \"结算清单\";\r\n    private static final String MAIN_CHECK_REGION = \"四川\";\r\n\r\n    /**\r\n     * 重症监护合计时长(scs_cutd_sum_dura)基础质控\r\n     * 重症监护合计时长是否合理\r\n     *\r\n     * @param wrap\r\n     * @return\r\n     */\r\n    @Override\r\n    public Void body(ScriptExecuteWrap wrap) {\r\n        SetlValidBaseBusiVo setlValidBaseBusiVo = (SetlValidBaseBusiVo) wrap.cmp.getFirstContextBean();\r\n        SettleListValidateVo settleListValidateVo = (SettleListValidateVo) wrap.cmp.getRequestData();\r\n        Map<String, String> keysBasicResultMap = setlValidBaseBusiVo.getKeysBasicResultMap();\r\n        SettleListHandlerVo settleListHandlerVo = new SettleListHandlerVo();\r\n        SomHiInvyBasInfo somHiInvyBasInfo = settleListValidateVo.getSomHiInvyBasInfo();\r\n        // 检查 somHiInvyBasInfo 是否为空\r\n        if (SettleValidateUtil.isEmpty(somHiInvyBasInfo)) {\r\n            addErrorDetail(SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY, SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY,\r\n                    (String) SettleValidateConst.CHECK_TYPE_MAP.get(SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY), MAIN_CHECK_FIELD_1,\r\n                    SettleValidateConst.VALIDATE_STATE_FAIL, setlValidBaseBusiVo);\r\n            return null;\r\n        }\r\n\r\n        List<SomSetlInvyScsCutdInfo> busIcuList = settleListValidateVo.getBusIcuList();\r\n        // 检查 somHiInvyBasInfo 是否为空\r\n        if (SettleValidateUtil.isEmpty(busIcuList)) {\r\n            return null;\r\n        }\r\n\r\n        if (SettleValidateConst.PASS.equals(keysBasicResultMap.get(MAIN_CHECK_FIELD_3))\r\n                && SettleValidateConst.PASS.equals(keysBasicResultMap.get(MAIN_CHECK_FIELD_4))) {\r\n            Date b12 = StringToConvert(somHiInvyBasInfo.getB12(), \"入院时间\");\r\n            Date b15 = StringToConvert(somHiInvyBasInfo.getB15(), \"出院时间\");\r\n            Date mimDate =StringToConvert(\"9999-12-31 00:00:00\", \"重症最大时间错误\") ;\r\n            Date maxDate = StringToConvert(\"0000-01-01 00:00:00\", \"重症最小时间错误\") ;\r\n            boolean flag = false;\r\n            for (int i = 0; i < busIcuList.size(); i++) {\r\n                SomSetlInvyScsCutdInfo somSetlInvyScsCutdInfo = (SomSetlInvyScsCutdInfo) busIcuList.get(i);\r\n                if (SettleValidateConst.PASS.equals(keysBasicResultMap.get(i + MAIN_CHECK_FIELD_1))) {\r\n                    Date inpool = StringToConvert(i, somSetlInvyScsCutdInfo.getScsCutdInpoolTime(), MAIN_CHECK_FIELD_1, MAIN_CHECK_NAME_1,\r\n                            somSetlInvyScsCutdInfo.getSettleListId(), setlValidBaseBusiVo,i==0?true:false);\r\n                    Date exit = StringToConvert(i, somSetlInvyScsCutdInfo.getScsCutdExitTime(), MAIN_CHECK_FIELD_2, MAIN_CHECK_NAME_2,\r\n                            somSetlInvyScsCutdInfo.getSettleListId(), setlValidBaseBusiVo,i==0?true:false);\r\n\r\n                    //如果大于入院时间\r\n                    if (inpool.before(b12)) {\r\n                        addErrorDetail(SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_ERROR_TYPE_2,\r\n                                MAIN_CHECK_FIELD_1,\r\n                                \"第\" + (i + 1) + \"个重症监护: id为[\" + somSetlInvyScsCutdInfo.getId() + \"]的\"\r\n                                        + MAIN_CHECK_NAME_1 + \"[\" + somSetlInvyScsCutdInfo.getScsCutdInpoolTime()\r\n                                        + \"]小于于入院时间[\" + somHiInvyBasInfo.getB12() + \"]\",\r\n                                SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n                    }\r\n\r\n                    //是否小于出院时间\r\n                    if (b15.before(exit)) {\r\n                        addErrorDetail(SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_ERROR_TYPE_2,\r\n                                MAIN_CHECK_FIELD_2,\r\n                                \"第\" + (i + 1) + \"个重症监护: id为[\" + somSetlInvyScsCutdInfo.getId() + \"]的\"\r\n                                        +  MAIN_CHECK_NAME_2 + \"[\" + somSetlInvyScsCutdInfo.getScsCutdExitTime()\r\n                                        + \"]大于出院时间[\" + somHiInvyBasInfo.getB15() + \"]\",\r\n                                SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n                    }\r\n\r\n                    if (exit.before(inpool)) {\r\n                        addErrorDetail(SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_ERROR_TYPE_2,\r\n                                MAIN_CHECK_FIELD_1,\r\n                                \"第\" + (i + 1) + \"个重症监护: id为[\" + somSetlInvyScsCutdInfo.getId() + \"]的\"\r\n                                        +  MAIN_CHECK_NAME_2 + \"[\" + somSetlInvyScsCutdInfo.getScsCutdInpoolTime() + \"]大于\"\r\n                                        + MAIN_CHECK_NAME_1 + \"[\" + somSetlInvyScsCutdInfo.getScsCutdExitTime() + \"]\",\r\n                                SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n                    }\r\n                    if (SettleValidateUtil.isNotEmpty(somSetlInvyScsCutdInfo.getScsCutdSumDura())) {\r\n                        //格式规范\r\n                        if (!isValidFormat(somSetlInvyScsCutdInfo.getScsCutdSumDura())) {\r\n                            addErrorDetail(SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_ERROR_TYPE_2,\r\n                                    MAIN_CHECK_FIELD,\r\n                                    \"第\" + (i + 1) + \"个重症监护: id为[\" + somSetlInvyScsCutdInfo.getId() + \"]的\" +  MAIN_CHECK_NAME + \"[\" + somSetlInvyScsCutdInfo.getScsCutdSumDura() + \"]的日期填写格式错误\",\r\n                                    SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n                        } else {\r\n                            //比较时间是否能匹配上\r\n                            String diff = getDifference(somSetlInvyScsCutdInfo.getSettleListId(), somSetlInvyScsCutdInfo.getScsCutdInpoolTime(), somSetlInvyScsCutdInfo.getScsCutdExitTime());\r\n                            if (!diff.equals(somSetlInvyScsCutdInfo.getScsCutdSumDura())) {\r\n                                addErrorDetail(SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_ERROR_TYPE_2,\r\n                                        MAIN_CHECK_FIELD,\r\n                                        \"第\" + (i + 1) + \"个重症监护: id为[\" + somSetlInvyScsCutdInfo.getId() + \"]的\" +  MAIN_CHECK_NAME + \"[\" + somSetlInvyScsCutdInfo.getScsCutdSumDura() + \"] 与 出入重症监护室时间差额 [\" + diff + \"] 不符\",\r\n                                        SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n                            }\r\n                        }\r\n                    } else {\r\n                        addErrorDetail(SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_ERROR_TYPE_1,\r\n                                MAIN_CHECK_FIELD,\r\n                                \"第\" + (i + 1) + \"个重症监护: id为[\" + somSetlInvyScsCutdInfo.getId() + \"]的\" +  MAIN_CHECK_NAME + \"[\" + somSetlInvyScsCutdInfo.getScsCutdSumDura() + \"]为空\",\r\n                                SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n                    }\r\n                    //判断时间不重叠\r\n                    for (int j = i + 1; j < busIcuList.size(); j++) {\r\n                        SomSetlInvyScsCutdInfo somSetlInvyScsCutdInfoNext = (SomSetlInvyScsCutdInfo) busIcuList.get(j);\r\n                        System.out.println(SettleValidateConst.PASS.equals(keysBasicResultMap.get(j + MAIN_CHECK_FIELD_1)));\r\n                        if (SettleValidateConst.PASS.equals(keysBasicResultMap.get(j + MAIN_CHECK_FIELD_1))) {\r\n                            Date inpoolNext = StringToConvert(j, somSetlInvyScsCutdInfoNext.getScsCutdInpoolTime(), MAIN_CHECK_FIELD_1, MAIN_CHECK_NAME_1,\r\n                                    somSetlInvyScsCutdInfo.getSettleListId(), setlValidBaseBusiVo,true);\r\n\r\n                            Date exitNext = StringToConvert(j, somSetlInvyScsCutdInfoNext.getScsCutdExitTime(), MAIN_CHECK_FIELD_2, MAIN_CHECK_NAME_2,\r\n                                    somSetlInvyScsCutdInfo.getSettleListId(), setlValidBaseBusiVo,true);\r\n\r\n                            if ((inpoolNext.before(inpool)  && inpool.before(exitNext))\r\n                                || (inpoolNext.before(exit)  && exit.before(exitNext)) ) {\r\n                                // 如果当前时间段与其它时间段重叠，设置标志为false\r\n                                addErrorDetail(SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_ERROR_TYPE_1,\r\n                                        MAIN_CHECK_FIELD,\r\n                                        \"第\" + (i + 1) + \"个重症监护与第\"+ (j + 1) +\"个重症监护的进入时间冲突。\" ,\r\n                                        SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n                            }\r\n\r\n                        }\r\n\r\n                    }\r\n\r\n                    //此时判断实际住院时间不为空且住院类型不为日间手术\r\n                    if(!ValidateUtil.isEmpty(somHiInvyBasInfo.getB20()) ){\r\n                        if (ValidateUtil.isEmpty(somHiInvyBasInfo.getB38()) || !\"2\".equals(somHiInvyBasInfo.getB38())){\r\n                            if (judgeTimeOK(inpool, mimDate)) {\r\n                                mimDate = inpool;\r\n                            }\r\n                            if (judgeTimeOK(maxDate, exit)) {\r\n                                maxDate = exit;\r\n                            }\r\n                            flag = true;\r\n                        }\r\n                    }\r\n\r\n                }\r\n            }\r\n            if (flag) {\r\n                //判断\r\n\r\n                double iptDay120rate = Long.parseLong(somHiInvyBasInfo.getB20())*1.2;\r\n\r\n                double daysDiff = getDateDifferenceInDays(mimDate, maxDate);\r\n\r\n                if (daysDiff > iptDay120rate) {\r\n                    addErrorDetail(SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_ERROR_TYPE_2,\r\n                            MAIN_CHECK_FIELD,\r\n                            \"ICU累计住院时间[\"+daysDiff+\"]不能超过实际住院天数120%[\"+iptDay120rate+\"]\",\r\n                            SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n                }\r\n            }\r\n        }\r\n\r\n        return null;\r\n    }\r\n    public  double getDateDifferenceInDays(Date date1, Date date2) {\r\n        SimpleDateFormat dateFormat = new SimpleDateFormat(\"yyyy-MM-dd HH:mm:ss\");\r\n        String formattedDifference = \"\";\r\n        // 计算时间差（毫秒）\r\n        long differenceInMillis = date2.getTime() - date1.getTime();\r\n\r\n        // 将毫秒差值转换为天数、小时数和分钟数\r\n        double differenceInDays = (double) differenceInMillis / TimeUnit.DAYS.toMillis(1);\r\n\r\n        return differenceInDays;\r\n    }\r\n    /**\r\n     * 判断时间2 大于时间1\r\n     *\r\n     * @param time1\r\n     * @param time2\r\n     * @return\r\n     */\r\n    private boolean judgeTimeOK(Date time1, Date time2) {\r\n        SimpleDateFormat dateFormat = new SimpleDateFormat(\"yyyy-MM-dd HH:mm:ss\");\r\n        boolean flag = false;\r\n            // 入院时间大于出院时间\r\n        if (time2.after(time1)) {\r\n           flag = true;\r\n        }\r\n        return flag;\r\n    }\r\n\r\n    private String getDifference(Long settleListId, String time1, String time2) {\r\n        // 定义时间格式\r\n        SimpleDateFormat dateFormat = new SimpleDateFormat(\"yyyy-MM-dd HH:mm:ss\");\r\n        String formattedDifference = \"\";\r\n        try {\r\n            // 将时间字符串转换为 Date 对象\r\n            Date date1 = dateFormat.parse(time1);\r\n            Date date2 = dateFormat.parse(time2);\r\n\r\n            // 计算时间差（毫秒）\r\n            long differenceInMillis = date2.getTime() - date1.getTime();\r\n\r\n            // 将毫秒差值转换为天数、小时数和分钟数\r\n            long differenceInDays = TimeUnit.MILLISECONDS.toDays(differenceInMillis);\r\n            long remainderMillisAfterDays = differenceInMillis % TimeUnit.DAYS.toMillis(1);\r\n            long differenceInHours = TimeUnit.MILLISECONDS.toHours(remainderMillisAfterDays);\r\n            long remainderMillisAfterHours = remainderMillisAfterDays % TimeUnit.HOURS.toMillis(1);\r\n            long differenceInMinutes = TimeUnit.MILLISECONDS.toMinutes(remainderMillisAfterHours);\r\n            if(\"00\".equals(differenceInMinutes)){\r\n                differenceInMinutes = 0;\r\n            }\r\n            if(\"00\".equals(differenceInHours)){\r\n                differenceInHours = 0;\r\n            }\r\n            // 格式化天数/小时数/分钟数字符串\r\n            formattedDifference = String.format(\"%d/%d/%d\",\r\n                    differenceInDays, differenceInHours, differenceInMinutes);\r\n\r\n\r\n        } catch (ParseException e) {\r\n            logger.error(\"清单编码为[\" + settleListId + \"] 计算重症时间差失败: \" + e.getMessage());\r\n        }\r\n        return formattedDifference;\r\n    }\r\n\r\n\r\n    /**\r\n     * 判断格式是否规范\r\n     *\r\n     * @param input\r\n     * @return\r\n     */\r\n    public boolean isValidFormat(String input) {\r\n\r\n        // 简化的正则表达式：匹配两个正整数用斜杠分隔\r\n        Matcher matcher = PATTERN.matcher(input);\r\n\r\n        if (matcher.matches()) {\r\n            try {\r\n                int days = Integer.parseInt(matcher.group(1));\r\n                int hours = Integer.parseInt(matcher.group(2));\r\n                int minutes = Integer.parseInt(matcher.group(3));\r\n                // 可选：进一步验证值的范围\r\n                if (days >= 0 && hours >= 0 && hours < 24 && minutes >= 0 && minutes < 60) {\r\n                    return true;\r\n                }\r\n            } catch (NumberFormatException e) {\r\n                // 处理转换错误\r\n                return false;\r\n            }\r\n        }\r\n        return false;\r\n    }\r\n\r\n    private void addErrorDetail(String index, String errType, String errorFields, String errDscr, String chkStas, SetlValidBaseBusiVo baseBusiVo) {\r\n        SettleListHandlerVo handlerVo = new SettleListHandlerVo();\r\n        handlerVo.setIndex(index);\r\n        handlerVo.setErrDscr(errDscr);\r\n        handlerVo.setErrorFields(errorFields);\r\n        handlerVo.setChkStas(chkStas);\r\n        handlerVo.setErrType(errType);\r\n        baseBusiVo.getErrorDetails().add(handlerVo);\r\n    }\r\n\r\n    private Date StringToConvert(int i, String time, String field, String name, Long settleListId,\r\n                                 SetlValidBaseBusiVo setlValidBaseBusiVo,boolean flag) {\r\n        SimpleDateFormat dateFormat = new SimpleDateFormat(\"yyyy-MM-dd HH:mm:ss\");\r\n        Date date = new Date();\r\n        try {\r\n            date = dateFormat.parse(time);\r\n\r\n        } catch (ParseException e) {\r\n            if(flag) {\r\n                addErrorDetail(SettleValidateConst.VALIDATE_TYPE_2, SettleValidateConst.VALIDATE_ERROR_TYPE_2,\r\n                        field,\r\n                        \"第\" + (i + 1) + \"个的\" + name + \"[\" + field + \"]日期格式不符合规范\",\r\n                        SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n            }\r\n            logger.error(\"清单编码为[\" + settleListId + \"]的\" + name + \"日期解析失败: \" + e.getMessage());\r\n        }\r\n        return date;\r\n    }\r\n\r\n    private Date StringToConvert(String time, String name) {\r\n        SimpleDateFormat dateFormat = new SimpleDateFormat(\"yyyy-MM-dd HH:mm:ss\");\r\n        Date date = new Date();\r\n        try {\r\n            date = dateFormat.parse(time);\r\n        } catch (ParseException e) {\r\n            logger.error(name + \":[\" + time + \"]\" + \"日期解析失败: \" + e.getMessage());\r\n        }\r\n        return date;\r\n    }\r\n}\r\n', 'script', 'java', '2024-09-24 17:23:21', '重症监护合计时长', '住院信息 ', 1, 1);
INSERT INTO `som_chain_script` VALUES (76, 'somplatform', 'check_setl_date_after_depth', '结算开始时间[d36]内涵质控', 'import com.my.som.model.dataHandle.SomHiInvyBasInfo;\r\nimport com.my.som.settlevalidate.constant.SettleValidateConst;\r\nimport com.my.som.settlevalidate.util.SettleValidateUtil;\r\nimport com.my.som.settlevalidate.vo.SetlValidBaseBusiVo;\r\nimport com.my.som.vo.dataHandle.SettleListHandlerVo;\r\nimport com.my.som.vo.dataHandle.SettleListValidateVo;\r\nimport com.yomahub.liteflow.script.ScriptExecuteWrap;\r\nimport com.yomahub.liteflow.script.body.JaninoCommonScriptBody;\r\nimport org.slf4j.Logger;\r\nimport org.slf4j.LoggerFactory;\r\n\r\nimport java.text.ParseException;\r\nimport java.text.SimpleDateFormat;\r\nimport java.util.Date;\r\nimport java.util.Map;\r\n\r\n/**\r\n * 事后判断 结算日期深度质检\r\n */\r\npublic class check_setl_date_after_depth implements JaninoCommonScriptBody {\r\n    private static final Logger logger = LoggerFactory.getLogger(check_setl_date_after_depth.class);\r\n\r\n    private static final String MAIN_CHECK_FIELD = \"d36\";\r\n    private static final String MAIN_CHECK_NAME = \"结算开始时间\";\r\n    private static final String MAIN_CHECK_FIELD_2 = \"d37\";\r\n    private static final String MAIN_CHECK_NAME_2 = \"结算结束时间\";\r\n    private static final String MAIN_CHECK_FIELD_3 = \"b12\";\r\n    private static final String MAIN_CHECK_FIELD_4 = \"b15\";\r\n    private static final String MAIN_CHECK_REGION = \"四川\";\r\n    /**\r\n     * 事后校验\r\n     * 结算开始时间(setl_date)是否在合理范围\r\n     * 判断结算开始时间是否为空\r\n     * @param wrap\r\n     * @return\r\n     */\r\n    @Override\r\n    public Void body(ScriptExecuteWrap wrap) {\r\n        SetlValidBaseBusiVo setlValidBaseBusiVo = (SetlValidBaseBusiVo) wrap.cmp.getFirstContextBean();\r\n        SettleListValidateVo settleListValidateVo = (SettleListValidateVo) wrap.cmp.getRequestData();\r\n        Map<String, String> keysBasicResultMap = setlValidBaseBusiVo.getKeysBasicResultMap();\r\n        SomHiInvyBasInfo somHiInvyBasInfo = settleListValidateVo.getSomHiInvyBasInfo();\r\n        SettleListHandlerVo settleListHandlerVo = new SettleListHandlerVo();\r\n\r\n        // 检查 somHiInvyBasInfo 是否为空\r\n        if (SettleValidateUtil.isEmpty(somHiInvyBasInfo)) {\r\n            addErrorDetail( SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY,SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY,\r\n                    (String) SettleValidateConst.CHECK_TYPE_MAP.get(SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY), null,\r\n                    SettleValidateConst.VALIDATE_STATE_FAIL, setlValidBaseBusiVo);\r\n            return null;\r\n        }\r\n\r\n        // 获取结算开始日期\r\n        String d36_stas =  (String) keysBasicResultMap.get(MAIN_CHECK_FIELD);\r\n        // 获取结算开始日期\r\n        String d36 = somHiInvyBasInfo.getD36();\r\n        // 获取结算开始日期\r\n        String d37_stas =  (String) keysBasicResultMap.get(MAIN_CHECK_FIELD_2);\r\n        // 获取计算时间\r\n        String d37 = somHiInvyBasInfo.getD37();\r\n        String b12_stas =  (String) keysBasicResultMap.get(MAIN_CHECK_FIELD_3);\r\n        //入院时间\r\n        String b12 =  somHiInvyBasInfo.getB12();\r\n\r\n        String b15_stas =  (String) keysBasicResultMap.get(MAIN_CHECK_FIELD_4);\r\n        //出院时间\r\n        String b15 =  somHiInvyBasInfo.getB15();\r\n\r\n       if (SettleValidateConst.PASS.equals(d36_stas) && SettleValidateConst.PASS.equals(b12_stas)) {\r\n            //此时判断结算开始时间是否大于或等于入院时间\r\n            //如果入院时间大于 结算开始时间\r\n            if(judgeTimeOK(d36,b12)){\r\n                addErrorDetail(SettleValidateConst.VALIDATE_TYPE_1,SettleValidateConst.VALIDATE_ERROR_TYPE_2,\r\n                        MAIN_CHECK_NAME + \"[\" + d36 + \"] 应该大于入院时间[\"+b12+\"]\",MAIN_CHECK_FIELD,\r\n                        SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n            }\r\n        }\r\n        if (SettleValidateConst.PASS.equals(d37_stas) && SettleValidateConst.PASS.equals(b15_stas)) {\r\n            //此时判断结算截止时间是否大于或等于出院时间\r\n            //如果出院院时间大于 结算截止时间\r\n            if(judgeTimeOK(d37,b15)){\r\n                addErrorDetail( SettleValidateConst.VALIDATE_TYPE_1,SettleValidateConst.VALIDATE_ERROR_TYPE_2,\r\n                        MAIN_CHECK_NAME_2 + \"[\" + d37 + \"] 应该大于出院时间[\"+b15+\"]\",MAIN_CHECK_FIELD,\r\n                        SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n            }\r\n        }\r\n\r\n        //此时判断结算截止时间是否大于或等于结算开始时间\r\n        //如果  结算开始时间 大于 结算截止时间\r\n        if(SettleValidateConst.PASS.equals(d36_stas) &&SettleValidateConst.PASS.equals(d37_stas)) {\r\n            if (judgeTimeOK(d37, d36)) {\r\n                addErrorDetail( SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_ERROR_TYPE_2,\r\n                        MAIN_CHECK_NAME + \"[\" + d36 + \"] \" + \"应该小于\" + MAIN_CHECK_NAME_2 + \"[\" + d37 + \"] \", MAIN_CHECK_FIELD,\r\n                        SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n            }\r\n        }\r\n        return null;\r\n    }\r\n\r\n\r\n\r\n    /**\r\n     * 时间2 大于  时间1  返回 true\r\n     * @param time1 时间1\r\n     * @param time2 时间2\r\n     * @return\r\n     */\r\n    private boolean judgeTimeOK(String time1, String time2) {\r\n        SimpleDateFormat dateFormat = new SimpleDateFormat(\"yyyy-MM-dd HH:mm:ss\");\r\n        boolean flag = false;\r\n        try{\r\n            Date admDate = dateFormat.parse(time1);\r\n            Date dscgDate = dateFormat.parse(time2);\r\n            // 时间2 大于或等于时间1\r\n            if (dscgDate.after(admDate)) {\r\n                flag = true;\r\n            }\r\n        }catch (ParseException e){\r\n            logger.error(\"结算开始时间质检 日期解析失败: \" + e.getMessage());\r\n        }\r\n        return flag ;\r\n    }\r\n\r\n    private void addErrorDetail(String index, String errType, String errDscr, String errorFields, String chkStas, SetlValidBaseBusiVo baseBusiVo) {\r\n        SettleListHandlerVo handlerVo =new SettleListHandlerVo();\r\n        handlerVo.setIndex(index);\r\n        handlerVo.setErrDscr(errDscr);\r\n        handlerVo.setErrorFields(errorFields);\r\n        handlerVo.setChkStas(chkStas);\r\n        handlerVo.setErrType(errType);\r\n        baseBusiVo.getErrorDetails().add(handlerVo);\r\n    }\r\n}\r\n', 'script', 'java', '2024-09-24 17:23:21', '结算日期', '基础信息', 1, 1);
INSERT INTO `som_chain_script` VALUES (77, 'somplatform', 'check_tcm_diagnosis_after_depth', '出院中医诊断编码[c06c2]内涵质控', 'import com.my.som.dto.medicalQuality.BusDiseaseDiagnosisTrim;\r\nimport com.my.som.settlevalidate.constant.SettleValidateConst;\r\nimport com.my.som.settlevalidate.util.SettleValidateUtil;\r\nimport com.my.som.settlevalidate.vo.SetlValidBaseBusiVo;\r\nimport com.my.som.util.SettleListValidateUtil;\r\nimport com.my.som.vo.dataHandle.SettleListHandlerVo;\r\nimport com.my.som.vo.dataHandle.SettleListValidateVo;\r\nimport com.yomahub.liteflow.script.ScriptExecuteWrap;\r\nimport com.yomahub.liteflow.script.body.JaninoCommonScriptBody;\r\nimport org.slf4j.Logger;\r\nimport org.slf4j.LoggerFactory;\r\n\r\nimport java.util.Arrays;\r\nimport java.util.List;\r\nimport java.util.Map;\r\n\r\n/**\r\n * 出院中医诊断\r\n */\r\npublic class check_tcm_diagnosis_after_depth implements JaninoCommonScriptBody {\r\n    private static final Logger logger = LoggerFactory.getLogger(check_tcm_diagnosis_after_depth.class);\r\n\r\n    private static final String MAIN_CHECK_NAME = \"出院中医诊断编码\";\r\n    private static final String MAIN_CHECK_FIELD = \"c06c2\";\r\n    private static final String MAIN_CHECK_NAME1 = \"出院中医诊断名称\";\r\n    private static final String MAIN_CHECK_CODE = \"c06c2\";\r\n\r\n    private static final String MAIN_CHECK_CODE1 = \"c07n2\";\r\n    private static final String TCM_MAIN_DISE = \"2\";\r\n    private static final String TCM_PRINCIPAL_DIAG = \"3\";\r\n    private static final String MAIN_CHECK_REGION = \"四川,湖北\";\r\n    /**\r\n     * 主要诊断 逻辑质控\r\n     *\r\n     * @param wrap\r\n     * @return\r\n     */\r\n    @Override\r\n    public Void body(ScriptExecuteWrap wrap) {\r\n\r\n        SetlValidBaseBusiVo setlValidBaseBusiVo = (SetlValidBaseBusiVo) wrap.cmp.getFirstContextBean();\r\n        SettleListValidateVo settleListValidateVo = (SettleListValidateVo) wrap.cmp.getRequestData();\r\n\r\n\r\n        // 获取主证诊断\r\n        Map<String, Object> principaltMap = (Map<String, Object>) setlValidBaseBusiVo.getSettleListParams().get(SettleListValidateUtil.KEY_TCM_PRINCIPAL);\r\n        //获取主病诊断\r\n        Map<String, Object> mainDiseasetMap = (Map<String, Object>) setlValidBaseBusiVo.getSettleListParams().get(SettleListValidateUtil.KEY_TCM_MAIN_DISS);\r\n\r\n        List<BusDiseaseDiagnosisTrim> busDiseaseDiagnosisTrimsList = settleListValidateVo.getBusDiseaseDiagnosisTrimsList();\r\n\r\n        // 检查诊断信息是否为空\r\n        if (SettleValidateUtil.isEmpty(busDiseaseDiagnosisTrimsList)) {\r\n            return null;\r\n        }\r\n        for (int i = 0; i < busDiseaseDiagnosisTrimsList.size(); i++) {\r\n            BusDiseaseDiagnosisTrim vo = (BusDiseaseDiagnosisTrim)busDiseaseDiagnosisTrimsList.get(i);\r\n            if(SettleValidateUtil.isNotEmpty(vo.getC06c1()) ){\r\n                if(SettleValidateUtil.isEmpty(vo.getC07n1())){\r\n                    addErrorDetail(SettleValidateConst.VALIDATE_TYPE_2, SettleValidateConst.VALIDATE_ERROR_TYPE_1,\r\n                            MAIN_CHECK_CODE1,\r\n                            \"第\" + (i + 1) + \"个疾病诊断：编码为[\" +vo.getC06c1()+\"]的\"+  MAIN_CHECK_NAME1 + \"[\" + vo.getC07n1() + \"]为空\",\r\n                            SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n                }else{\r\n                    if(TCM_PRINCIPAL_DIAG.equals(vo.getType())) {\r\n                        if(principaltMap.containsKey(vo.getC06c1())){\r\n                            List<String> list = Arrays.asList(principaltMap.get(vo.getC06c1()).toString().split(\";\"));\r\n                            boolean flag = false;\r\n                            for (String code :list){\r\n                                if(vo.getC07n1().equals(code)){\r\n                                    flag =true;\r\n                                }\r\n                            }\r\n                            if(!flag){\r\n                                addErrorDetail(SettleValidateConst.VALIDATE_TYPE_2, SettleValidateConst.VALIDATE_ERROR_TYPE_2,\r\n                                        MAIN_CHECK_CODE1,\r\n                                        \"第\" + (i + 1) + \"个疾病诊断：编码为[\" +vo.getC06c1()+\"]的\"+MAIN_CHECK_NAME1 + \"[\" + vo.getC07n1() + \"]与中医主证编码对应的名称[\"+list.toString()+\"]不符\",\r\n                                        SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n                            }\r\n                       }else{\r\n                            addErrorDetail(SettleValidateConst.VALIDATE_TYPE_2, SettleValidateConst.VALIDATE_ERROR_TYPE_2,\r\n                                    MAIN_CHECK_CODE1,\r\n                                    \"第\" + (i + 1) + \"个疾病诊断：编码为[\" +vo.getC06c1()+\"]的\"+MAIN_CHECK_NAME1 + \"[\" + vo.getC07n1() + \"]未在中医主证编码中\",\r\n                                    SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n                        }\r\n                    }\r\n                    else if(TCM_MAIN_DISE.equals(vo.getType())) {\r\n                        if (mainDiseasetMap.containsKey(vo.getC06c1())) {\r\n                            List<String> list = Arrays.asList(mainDiseasetMap.get(vo.getC06c1()).toString().split(\";\"));\r\n                            boolean flag = false;\r\n                            for (String code :list){\r\n                                if(vo.getC07n1().equals(code)){\r\n                                    flag =true;\r\n                                }\r\n                            }\r\n                            if(!flag){\r\n                            addErrorDetail(SettleValidateConst.VALIDATE_TYPE_2, SettleValidateConst.VALIDATE_ERROR_TYPE_2,\r\n                                    MAIN_CHECK_CODE1,\r\n                                    \"第\" + (i + 1) + \"个疾病诊断：编码为[\" +vo.getC06c1()+\"]的\"+MAIN_CHECK_NAME1 + \"[\" + vo.getC07n1()+ \"]与中医主病编码对应的名称[\"+list.toString()+\"]不符\",\r\n                                    SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n                            }\r\n                        }else{\r\n                            addErrorDetail(SettleValidateConst.VALIDATE_TYPE_2, SettleValidateConst.VALIDATE_ERROR_TYPE_2,\r\n                                    MAIN_CHECK_CODE1,\r\n                                    \"第\" + (i + 1) + \"个疾病诊断：编码为[\" +vo.getC06c1()+\"]的\"+MAIN_CHECK_NAME1 + \"[\" + vo.getC07n1() + \"]未在中医主病编码中\",\r\n                                    SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n                        }\r\n                    }\r\n//                    else {\r\n//                        addErrorDetail(SettleValidateConst.VALIDATE_TYPE_2, SettleValidateConst.VALIDATE_ERROR_TYPE_1,\r\n//                                MAIN_CHECK_CODE,\r\n//                                \"第\" + (i + 1) + \"个疾病诊断：编码为[\" +vo.getC06c1()+\"]的\"+MAIN_CHECK_NAME + \"[\" + vo.getC06c1() + \"]不属于中医主证或者中医主病编码标准代码\",\r\n//                                SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n//                    }\r\n                }\r\n            }\r\n//                else{\r\n//                if(SettleValidateUtil.isNotEmpty(vo.getC07n1())){\r\n//                    addErrorDetail(SettleValidateConst.VALIDATE_TYPE_2, SettleValidateConst.VALIDATE_ERROR_TYPE_1,\r\n//                            MAIN_CHECK_CODE,\r\n//                            \"第\" + (i + 1) + \"个疾病诊断：编码为[\" +vo.getC06c1()+\"]的\"+MAIN_CHECK_NAME + \"[\" + vo.getC06c1() + \"]为空但是\"+MAIN_CHECK_NAME1+\"存在\",\r\n//                            SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n//                }\r\n//            }\r\n\r\n        }\r\n        return null;\r\n    }\r\n\r\n    private void addErrorDetail(String index,String errType,String errorFields, String errDscr, String chkStas, SetlValidBaseBusiVo baseBusiVo) {\r\n        SettleListHandlerVo handlerVo = new SettleListHandlerVo();\r\n        handlerVo.setIndex(index);\r\n        handlerVo.setErrorFields(errorFields);\r\n        handlerVo.setErrDscr(errDscr);\r\n        handlerVo.setChkStas(chkStas);\r\n        handlerVo.setErrType(errType);\r\n        baseBusiVo.getErrorDetails().add(handlerVo);\r\n    }\r\n}', 'script', 'java', '2024-09-24 17:23:21', '出院中医诊断', '住院信息 ', 1, 1);
INSERT INTO `som_chain_script` VALUES (78, 'somplatform', 'check_vent_used_dura_after_depth', '呼吸机使用时长[vent_used_dura]内涵质控', 'import com.my.som.common.util.ValidateUtil;\r\nimport com.my.som.model.dataHandle.SomHiInvyBasInfo;\r\nimport com.my.som.model.dataHandle.SomOprnOprtInfo;\r\nimport com.my.som.settlevalidate.constant.SettleValidateConst;\r\nimport com.my.som.settlevalidate.vo.SetlValidBaseBusiVo;\r\nimport com.my.som.vo.dataHandle.SettleListHandlerVo;\r\nimport com.my.som.vo.dataHandle.SettleListValidateVo;\r\nimport com.yomahub.liteflow.script.ScriptExecuteWrap;\r\nimport com.yomahub.liteflow.script.body.JaninoCommonScriptBody;\r\nimport org.slf4j.Logger;\r\nimport org.slf4j.LoggerFactory;\r\n\r\nimport java.util.Arrays;\r\nimport java.util.List;\r\n\r\n/**\r\n * 事后 呼吸机合理性校验\r\n */\r\npublic class check_vent_used_dura_after_depth implements JaninoCommonScriptBody {\r\n    private static final Logger logger = LoggerFactory.getLogger(check_vent_used_dura_after_depth.class);\r\n\r\n    private static final String MAIN_CHECK_FIELD = \"vent_used_dura\";\r\n    private static final String MAIN_CHECK_NAME = \"呼吸机使用时长\";\r\n\r\n    private static final List<String> HX_CODE = Arrays.asList(\"96.7101\", \"96.7201\");\r\n    private static final String MAIN_CHECK_REGION = \"四川\";\r\n    /**\r\n     * 呼吸机合理性校验\r\n     *\r\n     * @param wrap\r\n     * @return\r\n     */\r\n    @Override\r\n    public Void body(ScriptExecuteWrap wrap) {\r\n        SetlValidBaseBusiVo setlValidBaseBusiVo = (SetlValidBaseBusiVo) wrap.cmp.getFirstContextBean();\r\n        SettleListValidateVo settleListValidateVo = (SettleListValidateVo) wrap.cmp.getRequestData();\r\n        List<SomOprnOprtInfo> oprnOprtInfos = settleListValidateVo.getBusOperateDiagnosisList();\r\n        SomHiInvyBasInfo somHiInvyBasInfo = settleListValidateVo.getSomHiInvyBasInfo();\r\n\r\n        if (!oprnOprtInfos.isEmpty()) {\r\n            for (int i = 0; i < oprnOprtInfos.size(); i++) {\r\n                SomOprnOprtInfo vo = (SomOprnOprtInfo) oprnOprtInfos.get(i);\r\n                String c35c = vo.getC35c();\r\n                if (HX_CODE.contains(c35c)) {\r\n                    // 存在呼吸机相关操作编码，判断是否存在呼吸机使用时间\r\n                    Integer c42 = somHiInvyBasInfo.getC42();\r\n                    Short c43 = somHiInvyBasInfo.getC43();\r\n                    Short c44 = somHiInvyBasInfo.getC44();\r\n                    if (c42 == null || c43 == null || c44 == null || (c42 == 0 && c43 == 0 && c44 == 0)) {\r\n                        addErrorDetail(SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_ERROR_TYPE_1,\r\n                                MAIN_CHECK_FIELD,\r\n                                \"第\" + (i + 1) + \"个手术操作：编码为[\"+c35c+\"]的\" + \"使用呼吸机时间不合理\",\r\n                                SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n                    }else {\r\n                        if(c43 >= 24) {\r\n                            addErrorDetail(SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_ERROR_TYPE_1,\r\n                                    MAIN_CHECK_FIELD,\r\n                                    \"第\" + (i + 1) + \"个手术操作：编码为[\"+c35c+\"]的\" + \"使用呼吸机时间 格式有误，小时不能大于等于24\",\r\n                                    SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n                        }\r\n                        if(c44 >= 60) {\r\n                            addErrorDetail(SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_ERROR_TYPE_1,\r\n                                    MAIN_CHECK_FIELD,\r\n                                    \"第\" + (i + 1) + \"个手术操作：编码为[\"+c35c+\"]的\" + \"使用呼吸机时间 格式有误，分钟不能大于等于60\",\r\n                                    SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n                        }\r\n                    }\r\n\r\n                    if(HX_CODE.get(1).equals(c35c)){\r\n                        String  b20 = somHiInvyBasInfo.getB20();\r\n                        if(!ValidateUtil.isEmpty(b20) ){\r\n                            int b20Int = Integer.parseInt(b20);\r\n                            if (b20Int < 4) {\r\n                                addErrorDetail(SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_ERROR_TYPE_1,\r\n                                        MAIN_CHECK_FIELD,\r\n                                        \"第\" + (i + 1) + \"个手术操作：编码为[\"+c35c+\"]的使用呼吸机时间[大于等于96小时]与住院天数[\"+b20+\"]不符\",\r\n                                        SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n                            }\r\n                        }else {\r\n                            addErrorDetail(SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_ERROR_TYPE_1,\r\n                                    MAIN_CHECK_FIELD,\r\n                                    \"第\" + (i + 1) + \"个手术操作：编码为[\"+c35c+\"]的使用呼吸机时间[大于等于96小时]与住院天数[\"+b20+\"]不符\",\r\n                                    SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n\r\n        return null;\r\n    }\r\n\r\n    private void addErrorDetail(String index, String errType, String errorFields, String errDscr, String chkStas, SetlValidBaseBusiVo baseBusiVo) {\r\n        SettleListHandlerVo handlerVo = new SettleListHandlerVo();\r\n        handlerVo.setIndex(index);\r\n        handlerVo.setErrorFields(errorFields);\r\n        handlerVo.setErrDscr(errDscr);\r\n        handlerVo.setChkStas(chkStas);\r\n        handlerVo.setErrType(errType);\r\n        baseBusiVo.getErrorDetails().add(handlerVo);\r\n    }\r\n}\r\n', 'script', 'java', '2024-09-24 17:23:21', '呼吸机使用时长', '基本信息', 1, 1);
INSERT INTO `som_chain_script` VALUES (79, 'somplatform', 'check_wm_adm_cond_after_depth', '出院主要诊断入院病情（西医）[c06c1]内涵质控', '\r\nimport com.my.som.common.constant.DrgConst;\r\nimport com.my.som.dto.medicalQuality.BusDiseaseDiagnosisTrim;\r\nimport com.my.som.model.dataHandle.SomHiInvyBasInfo;\r\nimport com.my.som.settlevalidate.constant.SettleValidateConst;\r\nimport com.my.som.settlevalidate.util.SettleValidateUtil;\r\nimport com.my.som.settlevalidate.vo.SetlValidBaseBusiVo;\r\nimport com.my.som.vo.dataHandle.SettleListHandlerVo;\r\nimport com.my.som.vo.dataHandle.SettleListValidateVo;\r\nimport com.yomahub.liteflow.script.ScriptExecuteWrap;\r\nimport com.yomahub.liteflow.script.body.JaninoCommonScriptBody;\r\nimport org.slf4j.Logger;\r\nimport org.slf4j.LoggerFactory;\r\n\r\nimport java.util.Arrays;\r\nimport java.util.List;\r\nimport java.util.Map;\r\n\r\n/**\r\n * 事后  诊断  (西医)入院病情合理性\r\n */\r\npublic class check_wm_adm_cond_after_depth implements JaninoCommonScriptBody {\r\n    private static final Logger logger = LoggerFactory.getLogger(check_wm_adm_cond_after_depth.class);\r\n\r\n    private static final String MAIN_CHECK_FIELD = \"c06c1\";\r\n    private static final String MAIN_CHECK_NAME = \"出院主要诊断入院病情（西医）\";\r\n\r\n    private static final List<String> COND_LIST = Arrays.asList(\"1\", \"2\", \"3\", \"4\");\r\n    private static final String ADM_CODE_WAY_4 = \"4\";\r\n    //1：\"有\", 2：\"临床未确定\", 3：\"情况不明\", 4：\"无\"\r\n    private static final String MAIN_CHECK_REGION = \"四川\";\r\n    /**\r\n     * 事后 校验\r\n     * 入院病情（西医）(wm_adm_cond) 基础质控\r\n     * 判断出院主要诊断入院病情（西医）是否为空\r\n     *\r\n     * @param wrap\r\n     * @return\r\n     */\r\n    @Override\r\n    public Void body(ScriptExecuteWrap wrap) {\r\n        SetlValidBaseBusiVo setlValidBaseBusiVo = (SetlValidBaseBusiVo) wrap.cmp.getFirstContextBean();\r\n        SettleListValidateVo settleListValidateVo = (SettleListValidateVo) wrap.cmp.getRequestData();\r\n        Map<String, String> keysBasicResultMap = setlValidBaseBusiVo.getKeysBasicResultMap();\r\n        SomHiInvyBasInfo somHiInvyBasInfo = settleListValidateVo.getSomHiInvyBasInfo();\r\n        SettleListHandlerVo settleListHandlerVo = new SettleListHandlerVo();\r\n        List<BusDiseaseDiagnosisTrim> busDiseaseDiagnosisTrimsList = settleListValidateVo.getBusDiseaseDiagnosisTrimsList();\r\n        // 检查 somHiInvyBasInfo 是否为空\r\n        if (SettleValidateUtil.isEmpty(somHiInvyBasInfo)) {\r\n           addErrorDetail( SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY, SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY,\r\n                    (String) SettleValidateConst.CHECK_TYPE_MAP.get(SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY), MAIN_CHECK_FIELD,\r\n                    SettleValidateConst.VALIDATE_STATE_FAIL, setlValidBaseBusiVo);\r\n            return null;\r\n        }\r\n        // 检查 busDiseaseDiagnosisTrimsList(诊断) 是否为空\r\n        if (SettleValidateUtil.isEmpty(busDiseaseDiagnosisTrimsList)) {\r\n            return null;\r\n        }\r\n        for (int i = 0; i < busDiseaseDiagnosisTrimsList.size(); i++) {\r\n            BusDiseaseDiagnosisTrim busDiseaseDiagnosisTrim = (BusDiseaseDiagnosisTrim) busDiseaseDiagnosisTrimsList.get(i);\r\n            if (SettleValidateUtil.isNotEmpty(busDiseaseDiagnosisTrim.getC06c1())) {\r\n                // 获取西医入院病情\r\n                String c08c1 = busDiseaseDiagnosisTrim.getC08c1();\r\n                if (SettleValidateUtil.isEmpty(c08c1)) {\r\n                   addErrorDetail( SettleValidateConst.VALIDATE_TYPE_2, SettleValidateConst.VALIDATE_TYPE_1,\r\n                           \"第\" + (i + 1) + \"个疾病诊断：编码为[\" +busDiseaseDiagnosisTrim.getC06c1()+\"]的\"+  MAIN_CHECK_NAME + \"[\" + c08c1 + \"]为空\", MAIN_CHECK_FIELD,\r\n                            SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n                } else {\r\n                    //判断是否符合入院病情规范\r\n                    if (!COND_LIST.contains(c08c1)) {\r\n                       addErrorDetail( SettleValidateConst.VALIDATE_TYPE_2, SettleValidateConst.VALIDATE_ERROR_TYPE_2,\r\n                               \"第\" + (i + 1) + \"个疾病诊断：编码为[\" +busDiseaseDiagnosisTrim.getC06c1()+\"]的\"+ MAIN_CHECK_NAME + \"[\" + c08c1 + \"]不符合入院病情规范性\", MAIN_CHECK_FIELD,\r\n                                SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n                    } else {\r\n                        if (DrgConst.MAINDIAG.equals(busDiseaseDiagnosisTrim.getMainDiagFlag())) {\r\n                            if (ADM_CODE_WAY_4.equals(c08c1)) {\r\n                               addErrorDetail( SettleValidateConst.VALIDATE_TYPE_2, SettleValidateConst.VALIDATE_TYPE_1,\r\n                                       \"第\" + (i + 1) + \"个疾病诊断：编码为[\" +busDiseaseDiagnosisTrim.getC06c1()+\"]的\"+  MAIN_CHECK_NAME + \":\" + \"主要病情不能为“无”\", MAIN_CHECK_FIELD,\r\n                                        SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);\r\n                            }\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n        return null;\r\n    }\r\n    private void addErrorDetail( String index,String errType,String errDscr,String errorFields,  String chkStas, SetlValidBaseBusiVo baseBusiVo) {\r\n        SettleListHandlerVo handlerVo =new SettleListHandlerVo();\r\n        handlerVo.setIndex(index);\r\n        handlerVo.setErrDscr(errDscr);\r\n        handlerVo.setErrorFields(errorFields);\r\n        handlerVo.setChkStas(chkStas);\r\n        handlerVo.setErrType(errType);\r\n        baseBusiVo.getErrorDetails().add(handlerVo);\r\n    }\r\n}\r\n\r\n', 'script', 'java', '2024-09-24 17:23:21', '(西医)入院病情', '住院诊断', 1, 1);

SET FOREIGN_KEY_CHECKS = 1;
