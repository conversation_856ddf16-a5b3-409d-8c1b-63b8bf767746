/*
 Navicat Premium Dump SQL

 Source Server         : 本地
 Source Server Type    : MySQL
 Source Server Version : 80039 (8.0.39)
 Source Host           : localhost:3306
 Source Schema         : drg_ps_11

 Target Server Type    : MySQL
 Target Server Version : 80039 (8.0.39)
 File Encoding         : 65001

 Date: 01/12/2024 18:19:48
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for som_nwb_code
-- ----------------------------
DROP TABLE IF EXISTS `som_nwb_code`;
CREATE TABLE `som_nwb_code`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '新生儿诊断ID',
  `code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '编码',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '诊断',
  `type` int NULL DEFAULT NULL COMMENT '类型 1是年龄',
  `vali_flag` tinyint NULL DEFAULT 1 COMMENT '是否启动 1：启动 0：未启用 ',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 948 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (948, 'P22.000', '新生儿呼吸窘迫综合征', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (949, 'P22.000x001', '新生儿肺透明膜病', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (950, 'P22.100x003', '新生儿肺水肿', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (951, 'P22.101', '新生儿湿肺', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (952, 'P22.801', '新生儿呼吸困难', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (953, 'P22.900', '新生儿的呼吸窘迫', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (954, 'P07.200', '极度不成熟', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (955, 'P07.200x011', '未成熟儿（孕期小于24整周）', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (956, 'P07.200x021', '未成熟儿（孕期等于或大于24整周以上,但小于28整周）', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (957, 'P07.300', '早产婴儿，其他的', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (958, 'P07.300x001', '早产儿', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (959, 'P07.300x011', '早产儿（孕期等于或大于28整周,但小于32整周）', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (960, 'P07.300x021', '早产儿（孕期等于或大于32整周,但小于37整周）', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (961, 'A04.000x001', '新生儿肠致病性大肠杆菌肠炎', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (962, 'A04.100x002', '新生儿肠毒性大肠杆菌肠炎', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (963, 'A04.200x002', '新生儿肠侵袭性大肠杆菌肠炎', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (964, 'A04.301', '新生儿肠出血性大肠杆菌肠炎', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (965, 'A04.400x003', '新生儿肠粘附性大肠杆菌肠炎', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (966, 'A04.402', '新生儿大肠杆菌肠炎', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (967, 'A09.900x005', '新生儿腹泻', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (968, 'A33.x00', '新生儿破伤风', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (969, 'A54.301+H13.1*', '淋球菌性新生儿眼炎', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (970, 'E55.000x006', '新生儿佝偻病', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (971, 'E84.101+P75*', '囊性纤维化性胎粪性肠梗阻', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (972, 'K10.200x009', '新生儿颌骨骨髓炎', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (973, 'K30.x00x002', '新生儿消化不良', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (974, 'L00.x01', '新生儿天疱疮', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (975, 'L01.006', '新生儿大疱性脓疱疮', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (976, 'P05.001', '低体重儿', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (977, 'P05.100', '小于胎龄', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (978, 'P05.102', '足月小样低体重儿', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (979, 'P05.200', '胎儿营养不良', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (980, 'P05.900', '胎儿生长缓慢', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (981, 'P05.900x001', '胎儿宫内生长迟缓', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (982, 'P08.000', '特大婴儿', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (983, 'P08.100x001', '大于胎龄儿', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (984, 'P08.100x002', '巨大儿', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (985, 'P08.200x002', '过期产儿', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (986, 'P10.000', '产伤引起的硬膜下出血', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (987, 'P10.100', '产伤引起的大脑出血', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (988, 'P10.200', '产伤引起的脑室内出血', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (989, 'P10.300', '产伤引起的蛛网膜下隙出血', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (990, 'P10.400', '产伤引起的脑幕撕裂', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (991, 'P10.800', '产伤引起的其他颅内撕裂和出血', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (992, 'P10.901', '产伤致新生儿颅内出血', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (993, 'P11.000', '产伤引起的脑水肿', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (994, 'P11.100', '产伤引起的其他特指的脑损害', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (995, 'P11.101', '产伤致新生儿脑白质损伤', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (996, 'P11.200', '产伤引起的脑损害', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (997, 'P11.300', '面神经产伤', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (998, 'P11.400', '脑神经的产伤，其他的', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (999, 'P11.500x002', '新生儿脊髓损伤', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1000, 'P11.500x003', '新生儿脊柱损伤', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1001, 'P11.900x001', '新生儿中枢神经系统损伤', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1002, 'P12.000x001', '新生儿头颅血肿', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1003, 'P12.100', '产伤引起的热带毛孢子菌病', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1004, 'P12.201', '产伤引起的帽状腱膜下血肿', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1005, 'P12.300', '产伤引起的头皮挫伤', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1006, 'P12.400', '新生儿头皮监测性损伤', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1007, 'P12.801', '产伤致新生儿头皮水肿', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1008, 'P12.900', '头皮产伤', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1009, 'P13.000', '产伤引起的颅骨骨折', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1010, 'P13.100', '颅骨的其他产伤', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1011, 'P13.200', '股骨产伤', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1012, 'P13.300', '长骨的产伤，其他的', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1013, 'P13.301', '产伤致新生儿肱骨骨折', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1014, 'P13.400', '产伤引起的锁骨骨折', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1015, 'P13.800', '骨骼其他部位的产伤', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1016, 'P13.801', '产伤致新生儿肋骨骨折', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1017, 'P13.900', '骨骼产伤', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1018, 'P14.000', '产伤引起的埃尔布麻痹', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1019, 'P14.100', '产伤引起的克隆普克麻痹', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1020, 'P14.200', '产伤引起的膈神经麻痹', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1021, 'P14.300', '臂丛神经的产伤，其他的', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1022, 'P14.800x001', '新生儿喉返神经麻痹', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1023, 'P14.800x002', '新生儿桡神经麻痹', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1024, 'P14.900', '周围神经系统的产伤', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1025, 'P15.000', '肝的产伤', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1026, 'P15.100', '脾的产伤', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1027, 'P15.201', '产伤致新生儿斜颈', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1028, 'P15.300', '眼的产伤', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1029, 'P15.400', '面部产伤', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1030, 'P15.500', '外生殖器产伤', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1031, 'P15.600', '产伤引起的皮下脂肪坏死', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1032, 'P15.800x004', '新生儿软组织挤压伤', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1033, 'P15.801', '产伤致新生儿咽部损伤', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1034, 'P15.802', '产伤致新生儿肛门裂伤', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1035, 'P15.803', '产伤致新生儿足挫伤', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1036, 'P15.804', '产伤致新生儿皮肤损伤', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1037, 'P15.900', '产伤', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1038, 'P15.901', '新生儿挤压综合征', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1039, 'P20.000', '在产程开始前首先察觉到的子宫内低氧症', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1040, 'P20.100', '在产程和分娩中首先察觉到的子宫内低氧症', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1041, 'P20.900', '子宫内低氧症', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1042, 'P20.901', '新生儿子宫内低氧酸中毒', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1043, 'P21.000', '严重的出生窒息', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1044, 'P21.101', '新生儿中度出生窒息', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1045, 'P21.102', '新生儿轻度出生窒息', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1046, 'P21.900', '出生窒息', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1047, 'P21.900x002', '新生儿低氧血症', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1048, 'P23.000x001', '新生儿病毒性肺炎', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1049, 'P23.100', '衣原体性先天性肺炎', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1050, 'P23.200', '葡萄球菌性先天性肺炎', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1051, 'P23.300', 'B族链球菌性先天性肺炎', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1052, 'P23.400', '大肠杆菌性先天性肺炎', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1053, 'P23.500', '假单胞菌性先天性肺炎', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1054, 'P23.600', '先天性肺炎，其他细菌性病原体引起的', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1055, 'P23.600x001', '新生儿支原体肺炎', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1056, 'P23.600x002', '新生儿流感嗜血杆菌肺炎', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1057, 'P23.600x003', '新生儿肺炎杆菌肺炎', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1058, 'P23.600x004', '新生儿链球菌肺炎（非B族）', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1059, 'P23.800', '先天性肺炎，其他病原体引起的', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1060, 'P23.900', '先天性肺炎', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1061, 'P24.001', '新生儿胎粪吸入综合征', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1062, 'P24.002', '新生儿胎粪吸入性肺炎', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1063, 'P24.101', '新生儿羊水吸入性肺炎', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1064, 'P24.102', '新生儿羊水吸入综合征', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1065, 'P24.200', '新生儿吸入血液', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1066, 'P24.300', '新生儿吸入奶和反流食物', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1067, 'P24.800', '新生儿吸入综合征，其他的', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1068, 'P24.900', '新生儿吸入综合征', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1069, 'P24.901', '新生儿吸入性肺炎', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1070, 'P25.000', '起源于围生期的间质肺气肿', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1071, 'P25.100', '起源于围生期的气胸', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1072, 'P25.200', '起源于围生期的纵隔气肿', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1073, 'P25.300', '起源于围生期的心包积气', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1074, 'P25.801', '新生儿肺大疱', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1075, 'P26.000', '起源于围生期的气管支气管出血', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1076, 'P26.100', '起源于围生期的大量肺出血', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1077, 'P26.800', '起源于围生期的其他肺出血', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1078, 'P26.900', '起源于围生期的肺出血', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1079, 'P27.000', '威尔逊-米基迪综合征', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1080, 'P27.000x001', '肺发育未成熟', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1081, 'P27.100', '起源于围生期的支气管肺发育不良', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1082, 'P27.801', '新生儿通气机肺', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1083, 'P27.802', '先天性肺纤维化', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1084, 'P27.900', '起源于围生期的慢性呼吸性疾病', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1085, 'P28.000', '新生儿原发性肺不张', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1086, 'P28.102', '新生儿肺不张', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1087, 'P28.200', '新生儿青紫发作', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1088, 'P28.300', '新生儿原发性睡眠呼吸暂停', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1089, 'P28.301', '中枢性新生儿睡眠呼吸暂停', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1090, 'P28.302', '阻塞性新生儿睡眠呼吸暂停', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1091, 'P28.303', '未特指新生儿睡眠呼吸暂停', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1092, 'P28.400', '新生儿的其他呼吸暂停', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1093, 'P28.401', '阻塞性新生儿呼吸暂停', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1094, 'P28.402', '早产儿呼吸暂停', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1095, 'P28.500', '新生儿呼吸衰竭', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1096, 'P28.800x101', '新生儿鼻塞', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1097, 'P28.800x201', '新生儿插管后声门下狭窄', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1098, 'P28.800x202', '新生儿后天性声门下狭窄', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1099, 'P28.800x901', '新生儿周期性呼吸', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1100, 'P28.800x903', '新生儿上呼吸道感染', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1101, 'P28.801', '先天性喉喘鸣', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1102, 'P28.900', '新生儿的呼吸性情况', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1103, 'P29.000', '新生儿心力衰竭', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1104, 'P29.100', '新生儿心律失常', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1105, 'P29.200', '新生儿高血压', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1106, 'P29.300', '持久的胎儿循环', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1107, 'P29.301', '新生儿持续性肺动脉高压', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1108, 'P29.400', '新生儿短暂性心肌缺血', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1109, 'P29.401', '新生儿缺血缺氧性心肌损害', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1110, 'P29.800x201', '新生儿心脏生理性杂音', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1111, 'P29.800x901', '新生儿循环衰竭', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1112, 'P29.800x902', '新生儿心包积液', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1113, 'P29.802', '新生儿低血压', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1114, 'P29.900', '起源于围生期心血管疾患', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1115, 'P35.000', '先天性风疹综合征', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1116, 'P35.000x001', '先天性风疹肺炎', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1117, 'P35.100', '先天性巨细胞病毒感染', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1118, 'P35.200', '先天性疱疹病毒[单纯疱疹]感染', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1119, 'P35.300', '先天性病毒性肝炎', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1120, 'P35.400', '先天性寨卡病毒病', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1121, 'P35.401', '先天性寨卡病毒病引起的小头畸形', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1122, 'P35.800x001', '先天性水痘', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1123, 'P35.900', '先天性病毒性疾病', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1124, 'P35.900x001', '新生儿病毒血症', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1125, 'P36.000', 'B族链球菌性新生儿脓毒症', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1126, 'P36.101', '链球菌性新生儿脓毒症', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1127, 'P36.200', '金黄色酿脓葡萄球菌性新生儿脓毒症', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1128, 'P36.301', '葡萄球菌性新生儿脓毒症', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1129, 'P36.400', '大肠杆菌性新生儿脓毒症', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1130, 'P36.500', '厌氧菌性新生儿脓毒症', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1131, 'P36.800x001', '新生儿铜绿假单胞菌脓毒症', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1132, 'P36.800x002', '新生儿肺炎克雷伯菌脓毒症', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1133, 'P36.800x003', '新生儿阴沟肠杆菌脓毒症', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1134, 'P36.800x004', '新生儿不动杆菌脓毒症', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1135, 'P36.800x005', '新生儿枸橼酸杆菌脓毒症', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1136, 'P36.900', '新生儿的细菌性脓毒症', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1137, 'P36.901', '新生儿脓毒症', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1138, 'P36.902', '新生儿菌血症', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1139, 'P37.200', '新生儿（播散性）利斯特菌病', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1140, 'P37.400', '先天性疟疾，其他的', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1141, 'P37.500', '新生儿念珠菌病', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1142, 'P37.800x001', '新生儿真菌性脑膜炎', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1143, 'P37.800x002', '新生儿真菌性脓毒症', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1144, 'P37.900', '先天性传染病和寄生虫病', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1145, 'P37.901', '先天性寄生虫病', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1146, 'P38.x00x001', '新生儿脐炎伴有出血', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1147, 'P38.x01', '新生儿脐炎', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1148, 'P39.000', '新生儿感染性乳腺炎', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1149, 'P39.100x003', '新生儿眼炎', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1150, 'P39.100x004', '新生儿结膜炎', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1151, 'P39.101', '新生儿泪囊炎', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1152, 'P39.102', '新生儿衣原体性结膜炎', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1153, 'P39.200', '胎儿羊膜腔内感染，不可归类在他处者', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1154, 'P39.300', '新生儿泌尿道感染', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1155, 'P39.401', '新生儿脓皮病', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1156, 'P39.402', '新生儿皮肤霉菌感染', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1157, 'P39.403', '新生儿臀炎', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1158, 'P39.800x004', '新生儿沙门菌感染', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1159, 'P39.800x005', '新生儿鼠伤寒沙门菌感染', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1160, 'P39.800x006', '新生儿猪霍乱沙门菌感染', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1161, 'P39.800x007', '新生儿梭状芽胞杆菌感染', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1162, 'P39.800x008', '新生儿大肠杆菌感染', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1163, 'P39.801', '新生儿颅内感染', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1164, 'P39.900', '特发于围生期的感染', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1165, 'P50.000', '前置血管所致的胎儿失血', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1166, 'P50.100', '脐带破裂所致的胎儿失血', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1167, 'P50.200', '胎盘所致的胎儿失血', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1168, 'P50.300', '出血流入双胎之另一胎儿', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1169, 'P50.400', '出血流入母体循环', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1170, 'P50.500', '双胎之另一胎儿的脐带断端所致的胎儿失血', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1171, 'P50.800', '胎儿失血，其他的', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1172, 'P50.900', '胎儿失血', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1173, 'P51.000', '新生儿脐带大量出血', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1174, 'P51.801', '新生儿脐带结扎滑脱', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1175, 'P51.900', '新生儿的脐带出血', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1176, 'P52.000', '胎儿和新生儿脑室内（非创伤性）出血，Ⅰ度', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1177, 'P52.100', '胎儿和新生儿脑室内（非创伤性）出血，Ⅱ度', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1178, 'P52.200x001', '新生儿脑室内出血Ⅲ度（非创伤性）', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1179, 'P52.200x002', '新生儿脑室内出血Ⅳ度（非创伤性）', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1180, 'P52.300', '胎儿和新生儿的脑室内（非创伤性）出血', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1181, 'P52.400', '胎儿和新生儿大脑内（非创伤性）出血', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1182, 'P52.500', '胎儿和新生儿蛛网膜下（非创伤性）出血', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1183, 'P52.600x001', '新生儿小脑出血（非创伤性）', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1184, 'P52.600x002', '新生儿后颅凹出血（非创伤性）', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1185, 'P52.801', '非创伤性新生儿硬膜外出血', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1186, 'P52.802', '非创伤性新生儿硬膜下出血', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1187, 'P52.900', '胎儿和新生儿的颅内（非创伤性）出血', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1188, 'P53.x00x001', '新生儿出血病', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1189, 'P53.x00x002', '新生儿维生素K缺乏性出血症', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1190, 'P54.000', '新生儿呕血', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1191, 'P54.100', '新生儿黑粪症', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1192, 'P54.200', '新生儿直肠出血', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1193, 'P54.300x001', '新生儿胃肠道出血', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1194, 'P54.300x002', '新生儿胃出血', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1195, 'P54.300x003', '新生儿肠出血', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1196, 'P54.400', '新生儿肾上腺出血', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1197, 'P54.500', '新生儿皮肤出血', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1198, 'P54.600', '新生儿阴道出血', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1199, 'P54.800x002', '新生儿结膜出血', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1200, 'P54.800x003', '新生儿心包积血', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1201, 'P54.801', '新生儿视网膜出血', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1202, 'P54.802', '新生儿鼻出血', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1203, 'P54.900', '新生儿出血', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1204, 'P55.000x002', '新生儿Rh血型不合溶血性贫血', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1205, 'P55.001', '新生儿抗D抗体增高', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1206, 'P55.002', '新生儿RH溶血症', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1207, 'P55.101', '新生儿ABO溶血性黄疸', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1208, 'P55.102', '新生儿ABO溶血性贫血', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1209, 'P55.800x002', '新生儿血型不合溶血病（Duffy系统）', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1210, 'P55.801', '新生儿MN溶血症', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1211, 'P55.900', '胎儿和新生儿的溶血性疾病', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1212, 'P56.000', '同种免疫引起的胎儿水肿', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1213, 'P56.900', '溶血性疾病引起的胎儿水肿，其他和未特指的', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1214, 'P57.000', '同种免疫引起的核黄疸', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1215, 'P57.800', '核黄疸，其他特指的', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1216, 'P57.900', '核黄疸', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1217, 'P57.901', '新生儿胆红素脑病', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1218, 'P58.000', '挫伤引起的新生儿黄疸', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1219, 'P58.100', '出血引起的新生儿黄疸', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1220, 'P58.200', '感染引起的新生儿黄疸', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1221, 'P58.300', '红细胞增多引起的新生儿黄疸', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1222, 'P58.401', '母体传新生儿黄疸', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1223, 'P58.402', '服用药物致新生儿黄疸', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1224, 'P58.403', '毒素致新生儿黄疸', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1225, 'P58.500', '吞咽母血引起的新生儿黄疸', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1226, 'P58.800', '过度溶血引起的新生儿黄疸，其他特指的', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1227, 'P58.800x001', '新生儿葡萄糖-6-磷酸脱氢酶[G6PD]缺乏性溶血性贫血', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1228, 'P58.900', '过度溶血引起的新生儿黄疸', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1229, 'P59.000', '与早产有关的新生儿黄疸', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1230, 'P59.100', '胆汁浓缩综合征', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1231, 'P59.201', '新生儿肝炎', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1232, 'P59.202', '婴儿肝炎综合征', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1233, 'P59.203', '胎儿或新生儿巨细胞肝炎', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1234, 'P59.301', '新生儿母乳性黄疸', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1235, 'P59.801', '新生儿病理性黄疸', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1236, 'P59.901', '新生儿高胆红素血症', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1237, 'P59.902', '新生儿生理性黄疸', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1238, 'P60.x00', '胎儿和新生儿播散性血管内凝血', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1239, 'P61.000', '短暂性新生儿血小板减少', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1240, 'P61.001', '新生儿血小板减少性紫癜', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1241, 'P61.100', '新生儿红细胞增多症', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1242, 'P61.200', '早产性贫血', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1243, 'P61.300', '胎儿失血所致的先天性贫血', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1244, 'P61.401', '新生儿贫血', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1245, 'P61.500', '短暂性新生儿中性粒细胞减少', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1246, 'P61.601', '新生儿低凝血酶原血症', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1247, 'P61.800', '围生期血液疾患，其他特指的', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1248, 'P61.900', '围生期血液疾患', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1249, 'P70.000', '母亲伴有妊娠糖尿病的婴儿综合征', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1250, 'P70.100', '糖尿病母亲的婴儿综合征', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1251, 'P70.200', '新生儿糖尿病', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1252, 'P70.300', '医源性新生儿低血糖症', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1253, 'P70.400x001', '新生儿低血糖症', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1254, 'P70.400x002', '新生儿短暂性低血糖症', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1255, 'P70.401', '新生儿顽固性低血糖', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1256, 'P70.801', '新生儿高血糖症', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1257, 'P70.900', '胎儿和新生儿的暂时性碳水化合物代谢疾患', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1258, 'P71.000', '新生儿牛乳性低钙血症', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1259, 'P71.100', '新生儿低钙血症，其他的', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1260, 'P71.100x001', '新生儿低钙血症', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1261, 'P71.200', '新生儿低镁血症', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1262, 'P71.300x001', '新生儿手足搐搦', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1263, 'P71.400', '暂时性新生儿甲状旁腺功能减退症', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1264, 'P71.800', '暂时性新生儿钙和镁代谢紊乱，其他的', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1265, 'P71.901', '新生儿暂时性镁代谢紊乱', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1266, 'P71.902', '新生儿暂时性钙代谢紊乱', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1267, 'P72.000', '新生儿甲状腺肿，不可归类在他处者', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1268, 'P72.100', '新生儿暂时性甲状腺功能亢进症', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1269, 'P72.200x001', '新生儿甲状腺功能减退症', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1270, 'P72.800', '新生儿其他特指的暂时性内分泌疾患', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1271, 'P72.900', '新生儿暂时性内分泌疾患', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1272, 'P74.001', '新生儿短暂性代谢性酸中毒', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1273, 'P74.002', '新生儿呼吸性酸中毒', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1274, 'P74.100', '新生儿脱水', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1275, 'P74.201', '新生儿低钠血症', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1276, 'P74.202', '新生儿高钠血症', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1277, 'P74.301', '新生儿高钾血症', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1278, 'P74.302', '新生儿低钾血症', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1279, 'P74.400', '新生儿其他的暂时性电解质失调', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1280, 'P74.401', '新生儿低氯血症', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1281, 'P74.402', '新生儿代谢性碱中毒', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1282, 'P74.403', '新生儿呼吸性碱中毒', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1283, 'P74.501', '新生儿高酪氨酸血症', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1284, 'P74.800x003', '新生儿乳糖代谢紊乱', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1285, 'P74.801', '新生儿低磷血症', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1286, 'P74.802', '新生儿低蛋白血症', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1287, 'P74.900', '新生儿暂时性代谢紊乱', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1288, 'P76.000', '胎粪堵塞综合征', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1289, 'P76.100', '新生儿暂时性肠梗阻', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1290, 'P76.200', '浓缩乳汁引起的肠梗阻', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1291, 'P76.801', '新生儿肠麻痹', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1292, 'P76.900', '新生儿肠梗阻', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1293, 'P77.x01', '新生儿坏死性小肠结肠炎', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1294, 'P78.000x003', '新生儿空肠穿孔', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1295, 'P78.000x004', '新生儿回肠穿孔', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1296, 'P78.000x005', '新生儿结肠穿孔', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1297, 'P78.000x006', '新生儿乙状结肠穿孔', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1298, 'P78.000x007', '新生儿直肠穿孔', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1299, 'P78.001', '新生儿肠穿孔', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1300, 'P78.002', '胎粪性腹膜炎', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1301, 'P78.100x001', '新生儿腹膜炎', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1302, 'P78.200x001', '新生儿咽下综合征', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1303, 'P78.300x001', '新生儿非感染性腹泻', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1304, 'P78.300x002', '非感染新生儿性肠炎', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1305, 'P78.300x003', '新生儿生理性腹泻', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1306, 'P78.300x005', '非感染性新生儿结肠炎', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1307, 'P78.800x004', '新生儿贲门失弛缓', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1308, 'P78.800x005', '新生儿幽门痉挛', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1309, 'P78.800x006', '新生儿便秘', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1310, 'P78.800x007', '新生儿阑尾炎', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1311, 'P78.800x008', '新生儿胆汁淤积症', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1312, 'P78.800x009', '新生儿胃穿孔', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1313, 'P78.800x010', '新生儿气胀', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1314, 'P78.800x012', '新生儿吞咽动作不协调', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1315, 'P78.801', '新生儿胆囊结石', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1316, 'P78.802', '新生儿腹胀', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1317, 'P78.803', '先天性肝硬化', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1318, 'P78.804', '新生儿消化性溃疡', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1319, 'P78.805', '新生儿暂时性胃扭转', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1320, 'P78.806', '新生儿食管反流', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1321, 'P78.807', '新生儿胃肠功能紊乱', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1322, 'P78.900', '围生期消化系统疾患', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1323, 'P78.901', '新生儿胎粪延迟排出', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1324, 'P80.000', '冷伤综合征', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1325, 'P80.800x001', '新生儿轻度低体温', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1326, 'P80.801', '新生儿环境性低体温', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1327, 'P80.900', '新生儿低温症', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1328, 'P81.000', '新生儿环境性高温', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1329, 'P81.001', '新生儿捂热综合征', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1330, 'P81.800', '新生儿其他特指的体温调节障碍', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1331, 'P81.901', '新生儿脱水热', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1332, 'P81.902', '新生儿发热', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1333, 'P83.000', '新生儿硬化病[硬肿症]', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1334, 'P83.100', '新生儿中毒性红斑', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1335, 'P83.200', '非溶血性疾病引起的胎儿水肿', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1336, 'P83.301', '新生儿水肿', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1337, 'P83.302', '胎儿水肿', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1338, 'P83.400', '新生儿乳房肿胀', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1339, 'P83.401', '新生儿非感染性乳腺炎', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1340, 'P83.500', '先天性鞘膜积液', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1341, 'P83.500x002', '先天性睾丸鞘膜积液', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1342, 'P83.500x003', '先天性精索鞘膜积液', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1343, 'P83.600', '新生儿脐息肉', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1344, 'P83.800x004', '新生儿硬皮病', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1345, 'P83.800x005', '青铜症[婴儿青铜综合征]', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1346, 'P83.800x006', '蓝莓松饼状婴儿', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1347, 'P83.800x007', '新生儿皮肤附属器息肉', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1348, 'P83.801', '新生儿红斑', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1349, 'P83.802', '新生儿皮下脂肪坏疽', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1350, 'P83.803', '新生儿荨麻疹', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1351, 'P83.901', '新生儿骶尾肿物', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1352, 'P90.x00', '新生儿惊厥', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1353, 'P91.000x002', '新生儿脑梗死', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1354, 'P91.100', '新生儿后天性脑室周围囊肿', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1355, 'P91.200', '新生儿脑白质软化', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1356, 'P91.300', '新生儿大脑兴奋增盛', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1357, 'P91.400', '新生儿大脑抑制', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1358, 'P91.500', '新生儿昏迷', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1359, 'P91.600', '新生儿缺氧缺血性脑病', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1360, 'P91.700', '后天性新生儿脑积水', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1361, 'P91.800x001', '新生儿脑病', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1362, 'P91.801', '新生儿颅内静脉窦血栓形成', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1363, 'P91.802', '新生儿中毒性脑病', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1364, 'P91.900', '新生儿大脑障碍', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1365, 'P91.900x001', '围生期脑损伤', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1366, 'P92.000', '新生儿呕吐', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1367, 'P92.001', '新生儿贲门松弛', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1368, 'P92.100x001', '新生儿胃食管反流', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1369, 'P92.200', '新生儿进食缓慢', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1370, 'P92.300', '新生儿喂养不足', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1371, 'P92.400', '新生儿喂养过量', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1372, 'P92.500', '新生儿母乳喂养困难', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1373, 'P92.800x001', '新生儿喂养不当', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1374, 'P92.800x003', '新生儿喂养不耐受', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1375, 'P92.900', '新生儿喂养问题', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1376, 'P93.x00', '胎儿和新生儿用药引起的反应和中毒', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1377, 'P93.x01', '新生儿用药中毒', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1378, 'P93.x02', '新生儿灰白综合征', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1379, 'P94.000', '短暂性新生儿重症肌无力', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1380, 'P94.100x001', '先天性肌张力增高', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1381, 'P94.200x001', '先天性肌张力减退', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1382, 'P94.200x002', '先天性肌弛缓综合征[松软儿]', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1383, 'P94.800', '新生儿其他的肌张力疾患', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1384, 'P94.900', '新生儿肌张力疾患', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1385, 'P95.x00', '胎儿死亡', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1386, 'P96.000x001', '先天性肾功能衰竭', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1387, 'P96.000x002', '新生儿尿毒症', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1388, 'P96.100x001', '新生儿撤药综合征（母亲药瘾）', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1389, 'P96.100x002', '新生儿药物戒断综合征（母亲药瘾）', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1390, 'P96.200', '新生儿使用治疗性药物所致的脱瘾性症状', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1391, 'P96.300', '新生儿宽颅缝', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1392, 'P96.301', '新生儿颅骨软化', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1393, 'P96.400', '妊娠终止，影响到胎儿和新生儿', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1394, 'P96.500', '子宫内操作的并发症，不可归类在他处者', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1395, 'P96.800x101', '新生儿颤抖', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1396, 'P96.800x904', '高危儿', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1397, 'P96.801', '新生儿多器官功能损害', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1398, 'P96.802', '新生儿缺血缺氧性肾损害', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1399, 'P96.803', '新生儿死亡', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1400, 'P96.804', '新生儿休克', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1401, 'P96.900x001', '新生儿反应低下', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1402, 'Z03.800x701', '可疑新生儿疾病的观察', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1403, 'Z03.800x711', '可疑新生儿感染情况的观察', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1404, 'Z03.800x721', '可疑新生儿神经病学的观察', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1405, 'Z03.800x731', '可疑新生儿呼吸情况的观察', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1406, 'Z03.803', '可疑新生儿红细胞增多症观察', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1407, 'P00.807', '母体系统性红斑狼疮新生儿', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1408, 'P04.001', '母体服麻醉药物新生儿', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1409, 'P04.101', '母体服抗凝药新生儿', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1410, 'P04.102', '母体癌症化疗新生儿', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1411, 'P04.200', '胎儿和新生儿受母体使用烟草的影响', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1412, 'P04.300', '胎儿和新生儿受母体使用酒精的影响', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1413, 'P04.400', '胎儿和新生儿受母体药瘾的影响', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1414, 'P04.500', '胎儿和新生儿受母体使用营养性、化学性', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1415, 'P04.600', '胎儿和新生儿受母体暴露于环境中化学物质的影响', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1416, 'P04.800', '胎儿和新生儿受母体内其他有害物质的影响', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1417, 'P04.900', '胎儿和新生儿受母体内有害物质的影响', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1418, 'P78.300x004', '新生儿消化不良', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1419, 'P04.800x001', '母体有机磷中毒新生儿', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1420, 'P07.000', '极低出生体重（小于999克）', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1421, 'P07.002', '极低出生体重儿（小于499克）', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1422, 'P07.100', '其他低出生体重（1000-2499克）', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1423, 'P07.101', '低出生体重儿（1500-2499克）', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1424, 'P07.001', '极低出生体重儿(500-999克)', 1, 1);
INSERT INTO `som_nwb_code` (`id`, `code`, `name`, `type`, `vali_flag`) VALUES (1425, 'P07.102', '低出生体重儿（1000-1499克）', 1, 1);

-- Records of som_nwb_code
-- ----------------------------
SET FOREIGN_KEY_CHECKS = 1;
