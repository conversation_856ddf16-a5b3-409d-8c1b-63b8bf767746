/*
 Navicat Premium Data Transfer

 Source Server         : mysql
 Source Server Type    : MySQL
 Source Server Version : 80034 (8.0.34)
 Source Host           : localhost:3306
 Source Schema         : dcdipv5

 Target Server Type    : MySQL
 Target Server Version : 80034 (8.0.34)
 File Encoding         : 65001

 Date: 03/06/2025 10:37:15
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for hcm_settle_diag_b
-- ----------------------------
DROP TABLE IF EXISTS `hcm_settle_diag_b`;
CREATE TABLE `hcm_settle_diag_b` (
  `hospital_id` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '医疗机构编码',
  `hospital_name` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '医疗机构名称',
  `bridge_id` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '病案关联字段',
  `hisid` varchar(100) COLLATE utf8mb4_general_ci NOT NULL COMMENT '结算单据号',
  `zyh` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '住院号',
  `patient_id` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '个人编码',
  `social_id` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '患者社会保障号码',
  `id_card` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '身份证号',
  `admission_disease_id` varchar(200) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '入院诊断编码',
  `admission_disease_name` varchar(200) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '入院诊断名称',
  `discharge_disease_id_main` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '出院主诊断编码',
  `discharge_disease_name_main` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '出院主诊断名称',
  `discharge_disease_id_other1` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '出院副诊断编码-1',
  `discharge_disease_name_other1` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '出院副诊断名称-1',
  `discharge_disease_id_other2` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '出院副诊断编码-2',
  `discharge_disease_name_other2` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '出院副诊断名称-2',
  `discharge_disease_id_other3` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '出院副诊断编码-3',
  `discharge_disease_name_other3` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '出院副诊断名称-3',
  `discharge_disease_id_other4` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '出院副诊断编码-4',
  `discharge_disease_name_other4` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '出院副诊断名称-4',
  `discharge_disease_id_other5` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '出院副诊断编码-5',
  `discharge_disease_name_other5` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '出院副诊断名称-5',
  `discharge_disease_id_other6` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '出院副诊断编码-6',
  `discharge_disease_name_other6` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '出院副诊断名称-6',
  `discharge_disease_id_other7` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '出院副诊断编码-7',
  `discharge_disease_name_other7` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '出院副诊断名称-7',
  `discharge_disease_id_other8` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '出院副诊断编码-8',
  `discharge_disease_name_other8` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '出院副诊断名称-8',
  `discharge_disease_id_other9` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '出院副诊断编码-9',
  `discharge_disease_name_other9` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '出院副诊断名称-9',
  `discharge_disease_id_other10` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '出院副诊断编码-10',
  `discharge_disease_name_other10` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '出院副诊断名称-10',
  `discharge_disease_id_other11` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '出院副诊断编码-11',
  `discharge_disease_name_other11` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '出院副诊断名称-11',
  `discharge_disease_id_other12` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '出院副诊断编码-12',
  `discharge_disease_name_other12` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '出院副诊断名称-12',
  `discharge_disease_id_other13` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '出院副诊断编码-13',
  `discharge_disease_name_other13` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '出院副诊断名称-13',
  `discharge_disease_id_other14` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '出院副诊断编码-14',
  `discharge_disease_name_other14` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '出院副诊断名称-14',
  `discharge_disease_id_other15` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '出院副诊断编码-15',
  `discharge_disease_name_other15` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '出院副诊断名称-15',
  `tid` bigint NOT NULL AUTO_INCREMENT COMMENT '记录id',
  PRIMARY KEY (`tid`)
) ENGINE=InnoDB AUTO_INCREMENT=39123 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- ----------------------------
-- Table structure for hcm_settle_mz_b
-- ----------------------------
DROP TABLE IF EXISTS `hcm_settle_mz_b`;
CREATE TABLE `hcm_settle_mz_b` (
  `hospital_id` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '医疗机构编码',
  `hospital_name` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '医疗机构名称',
  `hisid` varchar(100) COLLATE utf8mb4_general_ci NOT NULL COMMENT '结算单据号',
  `p_level` varchar(30) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '医保结算等级',
  `bill_date` datetime DEFAULT NULL COMMENT '结算日期',
  `year` varchar(30) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '结算年份',
  `month` varchar(30) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '结算月份',
  `patient_id` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '个人编码',
  `social_id` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '患者社会保障号码',
  `benefit_type` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '险种类型',
  `benefit_group_id` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '人员类别',
  `mzh` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '门诊号',
  `admission_dept_id` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '就诊科室编码',
  `admission_dept_name` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '就诊科室名称',
  `doctor_id` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '医师编码',
  `doctor_name` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '医师名称',
  `id_card` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '身份证号',
  `patient_gender` varchar(6) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '患者性别',
  `patient_birthday` datetime DEFAULT NULL COMMENT '患者出生日期',
  `patient_age` decimal(16,2) DEFAULT NULL COMMENT '患者年龄',
  `claim_type` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '就医类型',
  `if_local_flag` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '异地标志',
  `admission_date` datetime DEFAULT NULL COMMENT '就诊日期',
  `outpatient_disease_id` varchar(300) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '诊断编码',
  `admission_disease_name` varchar(300) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '诊断名称',
  `total_amount` decimal(16,2) DEFAULT NULL COMMENT '医疗总费用',
  `bmi_pay_amount` decimal(16,2) DEFAULT NULL COMMENT '基本统筹支付',
  `cash` decimal(16,2) DEFAULT NULL COMMENT '现金支付',
  `self_pay_amount` decimal(16,2) DEFAULT NULL COMMENT '个人账户支付',
  `bmi_convered_amount` decimal(16,2) DEFAULT NULL COMMENT '符合基本医疗保险的费用',
  `hospital_area` varchar(30) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '院区',
  `medical_insurance_flag` varchar(6) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '医保结算与非医保结算标志',
  `refund_flag_type` varchar(6) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '退费标识',
  `refund_hisid` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '退费冲销原结算单据号',
  `tid` bigint NOT NULL AUTO_INCREMENT COMMENT '记录id',
  PRIMARY KEY (`tid`)
) ENGINE=InnoDB AUTO_INCREMENT=48023 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- ----------------------------
-- Table structure for hcm_settle_mz_detail_b
-- ----------------------------
DROP TABLE IF EXISTS `hcm_settle_mz_detail_b`;
CREATE TABLE `hcm_settle_mz_detail_b` (
  `hospital_id` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '医疗机构编码',
  `hospital_name` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '医疗机构名称',
  `hisid` varchar(100) COLLATE utf8mb4_general_ci NOT NULL COMMENT '结算单据号',
  `patient_id` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '个人编码',
  `billing_dept_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '开单科室编码',
  `billing_dept_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '开单科室名称',
  `billing_time` datetime DEFAULT NULL COMMENT '项目开单时间',
  `excute_dept_id` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '执行科室编码',
  `excute_dept_name` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '执行科室名称',
  `usage_date` datetime DEFAULT NULL COMMENT '项目执行时间',
  `doctor_id` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '开单医师编码',
  `doctor_name` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '开单医师姓名',
  `bill_date` datetime DEFAULT NULL COMMENT '结算日期',
  `prescription` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '处方号',
  `p_category` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '费用类别',
  `item_id_hosp` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '医院项目编码',
  `item_name_hosp` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '医院项目名称',
  `item_id` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '医保项目编码',
  `item_name` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '医保项目名称',
  `drug_spec` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '规格',
  `dosage_form` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '剂型',
  `package_unit` varchar(30) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '计价单位',
  `unit_price` decimal(16,2) DEFAULT NULL COMMENT '单价',
  `num` decimal(16,2) DEFAULT NULL COMMENT '数量',
  `cost` decimal(16,2) DEFAULT NULL COMMENT '金额',
  `self_pay_limit` decimal(16,2) DEFAULT NULL COMMENT '拒付金额',
  `bmi_convered_amount` decimal(16,2) DEFAULT NULL COMMENT '医保范围内金额',
  `p_type` decimal(16,2) DEFAULT NULL COMMENT '支付类别',
  `p_type_pct` decimal(16,2) DEFAULT NULL COMMENT '报销比例',
  `refund_flag_type` varchar(30) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '处方退费标识',
  `refund_hisid` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '退费冲销原结算单据号',
  `fee_serial_no` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '费用流水号',
  `refund_serial_no` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '原退费流水号',
  `tid` bigint NOT NULL AUTO_INCREMENT COMMENT '记录id',
  PRIMARY KEY (`tid`)
) ENGINE=InnoDB AUTO_INCREMENT=250937 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- ----------------------------
-- Table structure for hcm_settle_oprn_b
-- ----------------------------
DROP TABLE IF EXISTS `hcm_settle_oprn_b`;
CREATE TABLE `hcm_settle_oprn_b` (
  `hospital_id` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '医疗机构编码',
  `hospital_name` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '医疗机构名称',
  `hisid` varchar(100) COLLATE utf8mb4_general_ci NOT NULL COMMENT '结算单据号',
  `zyh` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '住院号',
  `patient_id` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '个人编码',
  `social_id` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '患者社会保障号码',
  `id_card` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '身份证号',
  `icd9_code_main` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '主手术及操作编码',
  `icd9_name_main` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '主手术及操作名称',
  `icd9_code_other` varchar(4000) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '其他手术及操作编码',
  `icd9_name_other` varchar(4000) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '其他手术及操作名称',
  `oprn_oprt_date` datetime DEFAULT NULL COMMENT '手术操作日期',
  `anst_way` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '麻醉方式',
  `anst_begin_time` datetime DEFAULT NULL COMMENT '麻醉开始时间',
  `anst_end_time` datetime DEFAULT NULL COMMENT '麻醉结束时间',
  `oper_begin_time` datetime DEFAULT NULL COMMENT '手术开始时间',
  `oper_end_time` datetime DEFAULT NULL COMMENT '手术结束时间',
  `oper_dr_name` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '主刀术者医师姓名',
  `oper_dr_code` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '主刀术者医师代码',
  `oper_other_dr_name` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '其他术者医师姓名',
  `oper_other_dr_code` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '其他术者医师代码',
  `anst_dr_name` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '麻醉医师姓名',
  `anst_dr_code` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '麻醉医师代码',
  `tid` bigint NOT NULL AUTO_INCREMENT COMMENT '记录id',
  PRIMARY KEY (`tid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- ----------------------------
-- Table structure for hcm_settle_zy_b
-- ----------------------------
DROP TABLE IF EXISTS `hcm_settle_zy_b`;
CREATE TABLE `hcm_settle_zy_b` (
  `hospital_id` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '医疗机构编码',
  `hospital_name` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '医疗机构名称',
  `bridge_id` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '病案关联字段',
  `hisid` varchar(100) COLLATE utf8mb4_general_ci NOT NULL COMMENT '结算单据号',
  `p_level` varchar(30) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '医保结算等级',
  `bill_date` datetime DEFAULT NULL COMMENT '结算日期',
  `zyh` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '住院号',
  `patient_id` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '个人编码',
  `social_id` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '患者社会保障号码',
  `id_card` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '身份证号',
  `benefit_type` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '险种类型',
  `benefit_group_id` varchar(30) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '人员类型',
  `admission_dept_id` varchar(30) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '入院科室编码',
  `admission_dept_name` varchar(30) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '入院科室名称',
  `transfer_dept_id` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '转诊科室编码',
  `transfer_dept_name` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '转科科室名称',
  `discharge_dept_id` varchar(30) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '出院科室编码',
  `discharge_dept_name` varchar(30) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '出院科室名称',
  `doctor_id` varchar(30) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '主诊医师编码',
  `doctor_name` varchar(30) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '主诊医师姓名',
  `patient_gender` varchar(30) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '患者性别',
  `patient_birthday` datetime DEFAULT NULL COMMENT '患者出生日期',
  `patient_age` decimal(16,2) DEFAULT NULL COMMENT '患者年龄',
  `bedid` varchar(30) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '患者床位号',
  `nb_type` varchar(30) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '新生儿入院类型',
  `nb_birth_weight` decimal(16,2) DEFAULT NULL COMMENT '新生儿出生体重',
  `nb_inpatient_weight` decimal(16,2) DEFAULT NULL COMMENT '新生儿入院体重',
  `claim_type` varchar(30) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '住院医疗类型',
  `if_local_flag` varchar(30) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '异地标志',
  `admission_date` datetime DEFAULT NULL COMMENT '入院日期',
  `discharge_date` datetime DEFAULT NULL COMMENT '出院日期',
  `zyts` decimal(16,2) DEFAULT NULL COMMENT '住院天数',
  `discharge_status` varchar(30) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '离院方式',
  `total_amount` decimal(16,2) DEFAULT NULL COMMENT '医疗总费用',
  `bmi_pay_amount` decimal(16,2) DEFAULT NULL COMMENT '基本统筹支付',
  `cash` decimal(16,2) DEFAULT NULL COMMENT '个人现金支付',
  `self_pay_amount` decimal(16,2) DEFAULT NULL COMMENT '个人账户支付',
  `self_pay_in` decimal(16,2) DEFAULT NULL COMMENT '个人自付',
  `self_pay_out` decimal(16,2) DEFAULT NULL COMMENT '个人自费',
  `bmi_convered_amount` decimal(16,2) DEFAULT NULL COMMENT '符合基本医疗保险的费用',
  `hospital_area` varchar(30) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '院区',
  `medical_insurance_flag` varchar(30) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '医保结算与非医保结算标志',
  `refund_flag_type` varchar(6) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '退费标识',
  `refund_hisid` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '退费冲销原结算单据号',
  `tid` bigint NOT NULL AUTO_INCREMENT COMMENT '记录id',
  PRIMARY KEY (`tid`)
) ENGINE=InnoDB AUTO_INCREMENT=2623 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- ----------------------------
-- Table structure for hcm_settle_zy_detail_b
-- ----------------------------
DROP TABLE IF EXISTS `hcm_settle_zy_detail_b`;
CREATE TABLE `hcm_settle_zy_detail_b` (
  `hospital_id` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '医疗机构编码',
  `hospital_name` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '医疗机构名称',
  `hisid` varchar(100) COLLATE utf8mb4_general_ci NOT NULL COMMENT '结算单据号',
  `patient_id` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '个人编码',
  `zyh` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '住院号',
  `id_card` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '身份证号',
  `billing_dept_id` varchar(30) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '开单科室编码',
  `billing_dept_name` varchar(30) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '开单科室名称',
  `billing_time` datetime DEFAULT NULL COMMENT '项目开单时间',
  `usage_date` datetime DEFAULT NULL COMMENT '项目执行时间',
  `excute_dept_id` varchar(30) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '执行科室编码',
  `excute_dept_name` varchar(30) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '执行科室名称',
  `doctor_id` varchar(30) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '开单医师编码',
  `doctor_name` varchar(30) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '开单医师姓名',
  `p_category` varchar(30) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '费用类别',
  `bill_date` datetime DEFAULT NULL COMMENT '结算日期',
  `discharge_medication` varchar(30) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '出院带药标识',
  `item_id_hosp` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '医院项目编码',
  `item_name_hosp` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '医院项目名称',
  `item_id` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '医保项目编码',
  `item_name` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '医保项目名称',
  `drug_spec` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '规格',
  `dosage_form` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '剂型',
  `package_unit` varchar(30) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '计价单位',
  `unit_price` decimal(16,2) DEFAULT NULL COMMENT '单价',
  `num` decimal(16,2) DEFAULT NULL COMMENT '数量',
  `cost` decimal(16,2) DEFAULT NULL COMMENT '金额',
  `self_pay_limit` decimal(16,2) DEFAULT NULL COMMENT '拒付金额',
  `bmi_convered_amount` decimal(16,2) DEFAULT NULL COMMENT '医保范围内金额',
  `p_type` decimal(16,2) DEFAULT NULL COMMENT '支付类别',
  `p_type_pct` decimal(16,2) DEFAULT NULL COMMENT '报销比例',
  `refund_flag_type` varchar(2) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '退费标识',
  `refund_hisid` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '退费冲销原结算单据号',
  `fee_serial_no` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '费用流水号',
  `refund_serial_no` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '原退费流水号',
  `tid` bigint NOT NULL AUTO_INCREMENT COMMENT '记录id',
  PRIMARY KEY (`tid`)
) ENGINE=InnoDB AUTO_INCREMENT=830335 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

SET FOREIGN_KEY_CHECKS = 1;
