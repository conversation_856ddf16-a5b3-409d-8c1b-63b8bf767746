/*
 Navicat Premium Dump SQL

 Source Server         : 本地
 Source Server Type    : MySQL
 Source Server Version : 80039 (8.0.39)
 Source Host           : localhost:3306
 Source Schema         : cdym

 Target Server Type    : MySQL
 Target Server Version : 80039 (8.0.39)
 File Encoding         : 65001

 Date: 17/04/2025 14:30:06
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for hcm_rule_cfg
-- ----------------------------
DROP TABLE IF EXISTS `hcm_rule_cfg`;
CREATE TABLE `hcm_rule_cfg`  (
  `rule_id` bigint NOT NULL AUTO_INCREMENT COMMENT '规则id',
  `rule_scen_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '规则场景编码(住院、门诊)',
  `rule_type_codg` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '规则类型编码',
  `rule_type_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '规则类型名称',
  `rule_grp_codg` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '规则分组编码',
  `rule_grp_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '规则分组名称',
  `rule_detl_codg` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '规则明细编码',
  `rule_detl_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '规则明细名称',
  `opra_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '算子类型',
  `data_grp` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '数据分组',
  `rule_data_meta` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '规则数据元',
  `rule_year` varchar(4) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '规则年度',
  `add_time` datetime NOT NULL COMMENT '新增时间',
  `update_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `vali_flag` varchar(6) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '有效标志',
  `target_field` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '校验目标字段(多个|线分隔)',
  PRIMARY KEY (`rule_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 739 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '自查自纠规则配置表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of hcm_rule_cfg
-- ----------------------------
INSERT INTO `hcm_rule_cfg` VALUES (1, 'be_in_hosp', 'R01', '超标准收费', 'R0100001', '根据《四川省医疗服务价格项目》：开展腔镜等微创类手术时，超标准收取特大换药或大换药费用', 'R010000101', '', 'DoubleExistOperator', 'hosp', 'D01T00001', '2024', '2025-03-09 17:23:32', NULL, '1', 'med_list_codg');
INSERT INTO `hcm_rule_cfg` VALUES (2, 'be_in_hosp', 'R01', '超标准收费', 'R0100001', '根据《四川省医疗服务价格项目》：开展腔镜等微创类手术时，超标准收取特大换药或大换药费用', 'R010000102', '', 'DoubleExistOperator', 'hosp', 'D01T00002', '2024', '2025-03-09 17:23:32', NULL, '1', 'med_list_codg');
INSERT INTO `hcm_rule_cfg` VALUES (3, 'be_in_hosp', 'R02', '违规使用项目', 'R0200001', '不合规使用医疗服务项目', 'R020000101', '', 'ExistOperator', 'hosp', 'D02T00001', '2024', '2025-03-09 17:24:25', NULL, '1', 'med_list_codg');
INSERT INTO `hcm_rule_cfg` VALUES (4, 'be_in_hosp', 'R03', '过度检查', 'R0300001', '开展“数字化摄影(DR)”检查，收取“数字化摄影(DR)”费用，同时将“胶片”的费用按照诊疗项目类“X线摄影”进行收费', 'R030000101', '', 'DoubleExistOperator', 'day', 'D03T00001', '2024', '2025-03-09 17:24:38', NULL, '1', 'med_list_codg');
INSERT INTO `hcm_rule_cfg` VALUES (5, 'be_in_hosp', 'R03', '过度检查', 'R0300002', '行“X线计算机体层(CT)平扫”的患者均打包收取“三维重建”费用.其中，“三维重建”属于过度检查。', 'R030000201', '', 'DoubleExistOperator', 'day', 'D03T00002', '2024', '2025-03-09 17:24:38', NULL, '1', 'med_list_codg');
INSERT INTO `hcm_rule_cfg` VALUES (6, 'be_in_hosp', 'R03', '过度检查', 'R0300003', '“粪便隐血试验”一般用于消化道慢性出血的诊断.某医院将“粪寄生虫镜检”“粪便隐血试验”与大便常规检查打包', 'R030000301', '', 'DoubleExistOperator', 'day', 'D03T00003', '2024', '2025-03-09 17:24:38', NULL, '1', 'med_list_codg');
INSERT INTO `hcm_rule_cfg` VALUES (7, 'be_in_hosp', 'R03', '过度检查', 'R0300004', '开展“C一反应蛋白”检查时，打包“超敏C反应蛋白”,属于过度检查。', 'R030000401', '', 'DoubleExistOperator', 'day', 'D03T00004', '2024', '2025-03-09 17:24:38', NULL, '1', 'med_list_codg');
INSERT INTO `hcm_rule_cfg` VALUES (8, 'be_in_hosp', 'R03', '过度检查', 'R0300005', '没有根据病人实际病情，将“降钙素原(PCT)”检测、“超敏C反应蛋白(hs-CRP)”打包，属于过度检查。', 'R030000501', '', 'DoubleExistOperator', 'day', 'D03T00004', '2024', '2025-03-09 17:24:38', NULL, '1', 'med_list_codg');
INSERT INTO `hcm_rule_cfg` VALUES (9, 'be_in_hosp', 'R03', '过度检查', 'R0300006', '“不加热血清反应素试验”与“梅毒螺旋体特异抗体测定”打包，属于过度检查', 'R030000601', '', 'DoubleExistOperator', 'day', 'D03T00005', '2024', '2025-03-09 17:24:38', NULL, '1', 'med_list_codg');
INSERT INTO `hcm_rule_cfg` VALUES (10, 'be_in_hosp', 'R03', '过度检查', 'R0300007', '将“癌胚抗原测定\"“糖类抗原测定”列入患者入院常规检查大规模开展，属于过度检查。', 'R030000701', '', 'DoubleExistOperator', 'day', 'D03T00006', '2024', '2025-03-09 17:24:38', NULL, '1', 'med_list_codg');
INSERT INTO `hcm_rule_cfg` VALUES (11, 'be_in_hosp', 'R03', '过度检查', 'R0300008', '进行“阴道分泌物检查”时，将“特殊细菌涂片检查、 一般细菌涂片检查、真菌涂片检查，特殊细菌涂片检查、一般细菌涂片检查、真菌涂片检查”打包检查，属于过度检查。', 'R030000801', '', 'DoubleExistOperator', 'day', 'D03T00007', '2024', '2025-03-09 17:24:38', NULL, '1', 'med_list_codg');
INSERT INTO `hcm_rule_cfg` VALUES (12, 'be_in_hosp', 'R03', '过度检查', 'R0300009', '进行“血气分析”时，没有根据病人实际病情，打包“血浆乳酸测定”检查，属于过度检查', 'R030000901', '', 'DoubleExistOperator', 'day', 'D03T00008', '2024', '2025-03-09 17:24:38', NULL, '1', 'med_list_codg');
INSERT INTO `hcm_rule_cfg` VALUES (13, 'be_in_hosp', 'R03', '过度检查', 'R0300010', '无贫血情况的病人，进行“糖化血红蛋白测定”时检查时，打包针对贫血检查项目“抗碱血红蛋白测定(HbF)”检查，属于过度检查', 'R030001001', '', 'DoubleExistOperator', 'day', 'D03T00009', '2024', '2025-03-09 17:24:38', NULL, '1', 'med_list_codg');
INSERT INTO `hcm_rule_cfg` VALUES (14, 'be_in_hosp', 'R04', '限频次', 'R0400001', '《四川省医疗服务价格项目》中：按小时收费，每天不超限定阈值24', 'R040000101', '', 'OverclockTime', 'day', 'D04T00002', '2024', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (15, 'be_in_hosp', 'R04', '限频次', 'R0400001', '《四川省医疗服务价格项目》中：按小时收费，每天不超限定阈值24', 'R040000102', '', 'OverclockTime', 'day', 'D04T00001', '2024', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (16, 'be_in_hosp', 'R04', '限频次', 'R0400002', '《四川省医疗服务价格项目》中：以扩张一支冠脉血管为基价，扩张多支血管按该主项目的加收项目加收', 'R040000201', '', 'OverclockTime', 'day', 'D04T00011', '2024', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (17, 'be_in_hosp', 'R04', '限频次', 'R0400002', '《四川省医疗服务价格项目》中：以扩张一支冠脉血管为基价，扩张多支血管按该主项目的加收项目加收', 'R040000202', '', 'OverclockTime', 'day', 'D04T00004', '2024', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (18, 'be_in_hosp', 'R04', '限频次', 'R0400002', '《四川省医疗服务价格项目》中：以扩张一支冠脉血管为基价，扩张多支血管按该主项目的加收项目加收', 'R040000203', '', 'OverclockTime', 'day', 'D04T00008', '2024', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (19, 'be_in_hosp', 'R04', '限频次', 'R0400002', '《四川省医疗服务价格项目》中：以扩张一支冠脉血管为基价，扩张多支血管按该主项目的加收项目加收', 'R040000204', '', 'OverclockTime', 'day', 'D04T00006', '2024', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (20, 'be_in_hosp', 'R04', '限频次', 'R0400002', '《四川省医疗服务价格项目》中：以扩张一支冠脉血管为基价，扩张多支血管按该主项目的加收项目加收', 'R040000205', '', 'OverclockTime', 'day', 'D04T00003', '2024', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (21, 'be_in_hosp', 'R04', '限频次', 'R0400002', '《四川省医疗服务价格项目》中：以扩张一支冠脉血管为基价，扩张多支血管按该主项目的加收项目加收', 'R040000206', '', 'OverclockTime', 'day', 'D04T00010', '2024', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (22, 'be_in_hosp', 'R04', '限频次', 'R0400002', '《四川省医疗服务价格项目》中：以扩张一支冠脉血管为基价，扩张多支血管按该主项目的加收项目加收', 'R040000207', '', 'OverclockTime', 'day', 'D04T00012', '2024', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (23, 'be_in_hosp', 'R04', '限频次', 'R0400002', '《四川省医疗服务价格项目》中：以扩张一支冠脉血管为基价，扩张多支血管按该主项目的加收项目加收', 'R040000208', '', 'OverclockTime', 'day', 'D04T00013', '2024', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (24, 'be_in_hosp', 'R04', '限频次', 'R0400002', '《四川省医疗服务价格项目》中：以扩张一支冠脉血管为基价，扩张多支血管按该主项目的加收项目加收', 'R040000209', '', 'OverclockTime', 'day', 'D04T00007', '2024', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (25, 'be_in_hosp', 'R04', '限频次', 'R0400002', '《四川省医疗服务价格项目》中：以扩张一支冠脉血管为基价，扩张多支血管按该主项目的加收项目加收', 'R040000210', '', 'OverclockTime', 'day', 'D04T00009', '2024', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (26, 'be_in_hosp', 'R04', '限频次', 'R0400002', '《四川省医疗服务价格项目》中：以扩张一支冠脉血管为基价，扩张多支血管按该主项目的加收项目加收', 'R040000211', '', 'OverclockTime', 'day', 'D04T00005', '2024', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (27, 'be_in_hosp', 'R04', '限频次', 'R0400003', '《四川省医疗服务价格项目》中：经血管介入治疗原则上以经一根血管的介入治疗为起点，每增加一根血管的治疗按20%加收，最高加收不超过该项治疗的100', 'R040000301', '', 'OverclockTime', 'day', 'D04T00015', '2024', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (28, 'be_in_hosp', 'R04', '限频次', 'R0400003', '《四川省医疗服务价格项目》中：经血管介入治疗原则上以经一根血管的介入治疗为起点，每增加一根血管的治疗按20%加收，最高加收不超过该项治疗的100', 'R040000302', '', 'OverclockTime', 'day', 'D04T00016', '2024', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (29, 'be_in_hosp', 'R04', '限频次', 'R0400003', '《四川省医疗服务价格项目》中：经血管介入治疗原则上以经一根血管的介入治疗为起点，每增加一根血管的治疗按20%加收，最高加收不超过该项治疗的100', 'R040000303', '', 'OverclockTime', 'day', 'D04T00017', '2024', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (30, 'be_in_hosp', 'R04', '限频次', 'R0400003', '《四川省医疗服务价格项目》中：经血管介入治疗原则上以经一根血管的介入治疗为起点，每增加一根血管的治疗按20%加收，最高加收不超过该项治疗的100', 'R040000304', '', 'OverclockTime', 'day', 'D04T00014', '2024', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (31, 'be_in_hosp', 'R04', '限频次', 'R0400003', '《四川省医疗服务价格项目》中：经血管介入治疗原则上以经一根血管的介入治疗为起点，每增加一根血管的治疗按20%加收，最高加收不超过该项治疗的100', 'R040000305', '', 'OverclockTime', 'day', 'D04T00018', '2024', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (32, 'be_in_hosp', 'R04', '限频次', 'R0400004', '《四川省医疗服务价格项目汇编》中，血液透析、血液灌流、血液滤过、血液透析滤过、血透监测项目计价单位为“次”，不能按“小时”收费。', 'R040000401', '', 'OverclockTime', 'day', 'D04T00026', '2024', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (33, 'be_in_hosp', 'R04', '限频次', 'R0400004', '《四川省医疗服务价格项目汇编》中，血液透析、血液灌流、血液滤过、血液透析滤过、血透监测项目计价单位为“次”，不能按“小时”收费。', 'R040000402', '', 'OverclockTime', 'day', 'D04T00021', '2024', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (34, 'be_in_hosp', 'R04', '限频次', 'R0400004', '《四川省医疗服务价格项目汇编》中，血液透析、血液灌流、血液滤过、血液透析滤过、血透监测项目计价单位为“次”，不能按“小时”收费。', 'R040000403', '', 'OverclockTime', 'day', 'D04T00023', '2024', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (35, 'be_in_hosp', 'R04', '限频次', 'R0400004', '《四川省医疗服务价格项目汇编》中，血液透析、血液灌流、血液滤过、血液透析滤过、血透监测项目计价单位为“次”，不能按“小时”收费。', 'R040000404', '', 'OverclockTime', 'day', 'D04T00020', '2024', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (36, 'be_in_hosp', 'R04', '限频次', 'R0400004', '《四川省医疗服务价格项目汇编》中，血液透析、血液灌流、血液滤过、血液透析滤过、血透监测项目计价单位为“次”，不能按“小时”收费。', 'R040000405', '', 'OverclockTime', 'day', 'D04T00024', '2024', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (37, 'be_in_hosp', 'R04', '限频次', 'R0400004', '《四川省医疗服务价格项目汇编》中，血液透析、血液灌流、血液滤过、血液透析滤过、血透监测项目计价单位为“次”，不能按“小时”收费。', 'R040000406', '', 'OverclockTime', 'day', 'D04T00027', '2024', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (38, 'be_in_hosp', 'R04', '限频次', 'R0400004', '《四川省医疗服务价格项目汇编》中，血液透析、血液灌流、血液滤过、血液透析滤过、血透监测项目计价单位为“次”，不能按“小时”收费。', 'R040000407', '', 'OverclockTime', 'day', 'D04T00019', '2024', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (39, 'be_in_hosp', 'R04', '限频次', 'R0400004', '《四川省医疗服务价格项目汇编》中，血液透析、血液灌流、血液滤过、血液透析滤过、血透监测项目计价单位为“次”，不能按“小时”收费。', 'R040000408', '', 'OverclockTime', 'day', 'D04T00022', '2024', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (40, 'be_in_hosp', 'R04', '限频次', 'R0400004', '《四川省医疗服务价格项目汇编》中，血液透析、血液灌流、血液滤过、血液透析滤过、血透监测项目计价单位为“次”，不能按“小时”收费。', 'R040000409', '', 'OverclockTime', 'day', 'D04T00025', '2024', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (41, 'be_in_hosp', 'R04', '限频次', 'R0400005', '《人力资源社会保障部国家卫生计生委民政部财政部中国残联关于新增部分医疗康复项目纳入基本医疗保障支付范围的通知》(人社部发〔2016〕23号)中，言语能力筛查项目支付条件：限支付疑似言语功能障碍患者，不包括言语功能不能恢复的患者， 一个疾病过程支付不超过两次。', 'R040000501', '', 'OverclockTime', 'hosp', 'D04T00032', '2024', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (42, 'be_in_hosp', 'R04', '限频次', 'R0400005', '《人力资源社会保障部国家卫生计生委民政部财政部中国残联关于新增部分医疗康复项目纳入基本医疗保障支付范围的通知》(人社部发〔2016〕23号)中，言语能力筛查项目支付条件：限支付疑似言语功能障碍患者，不包括言语功能不能恢复的患者， 一个疾病过程支付不超过两次。', 'R040000502', '', 'OverclockTime', 'hosp', 'D04T00029', '2024', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (43, 'be_in_hosp', 'R04', '限频次', 'R0400005', '《人力资源社会保障部国家卫生计生委民政部财政部中国残联关于新增部分医疗康复项目纳入基本医疗保障支付范围的通知》(人社部发〔2016〕23号)中，言语能力筛查项目支付条件：限支付疑似言语功能障碍患者，不包括言语功能不能恢复的患者， 一个疾病过程支付不超过两次。', 'R040000503', '', 'OverclockTime', 'hosp', 'D04T00031', '2024', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (44, 'be_in_hosp', 'R04', '限频次', 'R0400005', '《人力资源社会保障部国家卫生计生委民政部财政部中国残联关于新增部分医疗康复项目纳入基本医疗保障支付范围的通知》(人社部发〔2016〕23号)中，言语能力筛查项目支付条件：限支付疑似言语功能障碍患者，不包括言语功能不能恢复的患者， 一个疾病过程支付不超过两次。', 'R040000504', '', 'OverclockTime', 'hosp', 'D04T00030', '2024', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (45, 'be_in_hosp', 'R04', '限频次', 'R0400005', '《人力资源社会保障部国家卫生计生委民政部财政部中国残联关于新增部分医疗康复项目纳入基本医疗保障支付范围的通知》(人社部发〔2016〕23号)中，言语能力筛查项目支付条件：限支付疑似言语功能障碍患者，不包括言语功能不能恢复的患者， 一个疾病过程支付不超过两次。', 'R040000505', '', 'OverclockTime', 'hosp', 'D04T00028', '2024', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (46, 'be_in_hosp', 'R04', '限频次', 'R0400005', '《人力资源社会保障部国家卫生计生委民政部财政部中国残联关于新增部分医疗康复项目纳入基本医疗保障支付范围的通知》(人社部发〔2016〕23号)中，言语能力筛查项目支付条件：限支付疑似言语功能障碍患者，不包括言语功能不能恢复的患者， 一个疾病过程支付不超过两次。', 'R040000506', '', 'OverclockTime', 'hosp', 'D04T00033', '2024', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (47, 'be_in_hosp', 'R04', '限频次', 'R0400006', '《人力资源社会保障部国家卫生计生委民政部财政部中国残联关于新增部分医疗康复项目纳入基本医疗保障支付范围的通知》(人社部发〔2016〕23号)中，吞咽功能障碍检查项目支付条件：一个疾病过程支付不超过三次。', 'R040000601', '', 'OverclockTime', 'hosp', 'D04T00034', '2024', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (48, 'be_in_hosp', 'R04', '限频次', 'R0400007', '《人力资源社会保障部国家卫生计生委民政部财政部中国残联关于新增部分医疗康复项目纳入基本医疗保障支付范围的通知》(人社部发〔2016〕23号)中，手功能评定项目支付条件：明确手功能障碍，且支付总时间不超过90天，评定间隔时间不短于14天。', 'R040000701', '', 'OverclockTime', 'hosp', 'D04T00036', '2024', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (49, 'be_in_hosp', 'R04', '限频次', 'R0400007', '《人力资源社会保障部国家卫生计生委民政部财政部中国残联关于新增部分医疗康复项目纳入基本医疗保障支付范围的通知》(人社部发〔2016〕23号)中，手功能评定项目支付条件：明确手功能障碍，且支付总时间不超过90天，评定间隔时间不短于14天。', 'R040000702', '', 'OverclockTime', 'hosp', 'D04T00037', '2024', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (50, 'be_in_hosp', 'R04', '限频次', 'R0400007', '《人力资源社会保障部国家卫生计生委民政部财政部中国残联关于新增部分医疗康复项目纳入基本医疗保障支付范围的通知》(人社部发〔2016〕23号)中，手功能评定项目支付条件：明确手功能障碍，且支付总时间不超过90天，评定间隔时间不短于14天。', 'R040000703', '', 'OverclockTime', 'hosp', 'D04T00035', '2024', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (51, 'be_in_hosp', 'R04', '限频次', 'R0400008', '依据《关于将部分医疗康复项目纳入基本医疗保障范围的通知》(卫农卫发(2010)80号)文件，脑瘫肢体综合训练每日支付不超过2次。', 'R040000801', '', 'OverclockTime', 'day', 'D04T00038', '2024', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (52, 'be_in_hosp', 'R04', '限频次', 'R0400009', '《关于将部分医疗康复项目纳入基本医疗保障范围的通知》(卫农卫发(2010)80号)中，截瘫肢体综合训练1个疾病过程支付不超过3个月', 'R040000901', '', 'OverclockTime', 'hosp', 'D04T00039', '2024', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (53, 'be_in_hosp', 'R04', '限频次', 'R0400010', '《关于将部分医疗康复项目纳入基本医疗保障范围的通知》(卫农卫发(2010)80号)中，截瘫肢体综合训练每日医保支付不超过2次。', 'R040001001', '', 'OverclockTime', 'day', 'D04T00040', '2024', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (54, 'be_in_hosp', 'R04', '限频次', 'R0400011', '《关于将部分医疗康复项目纳入基本医疗保障范围的通知》(卫农卫发(2010)80号)中，运动疗法1个疾病过程支付不超过3个月', 'R040001101', '', 'OverclockTime', 'hosp', 'D04T00041', '2024', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (55, 'be_in_hosp', 'R04', '限频次', 'R0400011', '《关于将部分医疗康复项目纳入基本医疗保障范围的通知》(卫农卫发(2010)80号)中，运动疗法1个疾病过程支付不超过3个月', 'R040001102', '', 'OverclockTime', 'hosp', 'D04T00042', '2024', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (56, 'be_in_hosp', 'R04', '限频次', 'R0400011', '《关于将部分医疗康复项目纳入基本医疗保障范围的通知》(卫农卫发(2010)80号)中，运动疗法1个疾病过程支付不超过3个月', 'R040001103', '', 'OverclockTime', 'hosp', 'D04T00048', '2024', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (57, 'be_in_hosp', 'R04', '限频次', 'R0400011', '《关于将部分医疗康复项目纳入基本医疗保障范围的通知》(卫农卫发(2010)80号)中，运动疗法1个疾病过程支付不超过3个月', 'R040001104', '', 'OverclockTime', 'hosp', 'D04T00046', '2024', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (58, 'be_in_hosp', 'R04', '限频次', 'R0400011', '《关于将部分医疗康复项目纳入基本医疗保障范围的通知》(卫农卫发(2010)80号)中，运动疗法1个疾病过程支付不超过3个月', 'R040001105', '', 'OverclockTime', 'hosp', 'D04T00047', '2024', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (59, 'be_in_hosp', 'R04', '限频次', 'R0400011', '《关于将部分医疗康复项目纳入基本医疗保障范围的通知》(卫农卫发(2010)80号)中，运动疗法1个疾病过程支付不超过3个月', 'R040001106', '', 'OverclockTime', 'hosp', 'D04T00045', '2024', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (60, 'be_in_hosp', 'R04', '限频次', 'R0400011', '《关于将部分医疗康复项目纳入基本医疗保障范围的通知》(卫农卫发(2010)80号)中，运动疗法1个疾病过程支付不超过3个月', 'R040001107', '', 'OverclockTime', 'hosp', 'D04T00049', '2024', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (61, 'be_in_hosp', 'R04', '限频次', 'R0400011', '《关于将部分医疗康复项目纳入基本医疗保障范围的通知》(卫农卫发(2010)80号)中，运动疗法1个疾病过程支付不超过3个月', 'R040001108', '', 'OverclockTime', 'hosp', 'D04T00043', '2024', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (62, 'be_in_hosp', 'R04', '限频次', 'R0400011', '《关于将部分医疗康复项目纳入基本医疗保障范围的通知》(卫农卫发(2010)80号)中，运动疗法1个疾病过程支付不超过3个月', 'R040001109', '', 'OverclockTime', 'hosp', 'D04T00044', '2024', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (63, 'be_in_hosp', 'R04', '限频次', 'R0400012', '《关于将部分医疗康复项目纳入基本医疗保障范围的通知》(卫农卫发(2010)80号)中，运动疗法每日支付不超过2次', 'R040001201', '', 'OverclockTime', 'day', 'D04T00053', '2024', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (64, 'be_in_hosp', 'R04', '限频次', 'R0400012', '《关于将部分医疗康复项目纳入基本医疗保障范围的通知》(卫农卫发(2010)80号)中，运动疗法每日支付不超过2次', 'R040001202', '', 'OverclockTime', 'day', 'D04T00055', '2024', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (65, 'be_in_hosp', 'R04', '限频次', 'R0400012', '《关于将部分医疗康复项目纳入基本医疗保障范围的通知》(卫农卫发(2010)80号)中，运动疗法每日支付不超过2次', 'R040001203', '', 'OverclockTime', 'day', 'D04T00058', '2024', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (66, 'be_in_hosp', 'R04', '限频次', 'R0400012', '《关于将部分医疗康复项目纳入基本医疗保障范围的通知》(卫农卫发(2010)80号)中，运动疗法每日支付不超过2次', 'R040001204', '', 'OverclockTime', 'day', 'D04T00051', '2024', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (67, 'be_in_hosp', 'R04', '限频次', 'R0400012', '《关于将部分医疗康复项目纳入基本医疗保障范围的通知》(卫农卫发(2010)80号)中，运动疗法每日支付不超过2次', 'R040001205', '', 'OverclockTime', 'day', 'D04T00056', '2024', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (68, 'be_in_hosp', 'R04', '限频次', 'R0400012', '《关于将部分医疗康复项目纳入基本医疗保障范围的通知》(卫农卫发(2010)80号)中，运动疗法每日支付不超过2次', 'R040001206', '', 'OverclockTime', 'day', 'D04T00057', '2024', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (69, 'be_in_hosp', 'R04', '限频次', 'R0400012', '《关于将部分医疗康复项目纳入基本医疗保障范围的通知》(卫农卫发(2010)80号)中，运动疗法每日支付不超过2次', 'R040001207', '', 'OverclockTime', 'day', 'D04T00054', '2024', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (70, 'be_in_hosp', 'R04', '限频次', 'R0400012', '《关于将部分医疗康复项目纳入基本医疗保障范围的通知》(卫农卫发(2010)80号)中，运动疗法每日支付不超过2次', 'R040001208', '', 'OverclockTime', 'day', 'D04T00050', '2024', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (71, 'be_in_hosp', 'R04', '限频次', 'R0400012', '《关于将部分医疗康复项目纳入基本医疗保障范围的通知》(卫农卫发(2010)80号)中，运动疗法每日支付不超过2次', 'R040001209', '', 'OverclockTime', 'day', 'D04T00052', '2024', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (72, 'be_in_hosp', 'R04', '限频次', 'R0400013', '《关于将部分医疗康复项目纳入基本医疗保障范围的通知》(卫农卫发(2010)80号)中，作业疗法每日支付不超过1次', 'R040001301', '', 'OverclockTime', 'day', 'D04T00059', '2024', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (73, 'be_in_hosp', 'R04', '限频次', 'R0400014', '《关于将部分医疗康复项目纳入基本医疗保障范围的通知》(卫农卫发(2010)80号)中，偏瘫肢体综合训练1个疾病过程支付不超过3个月', 'R040001401', '', 'OverclockTime', 'hosp', 'D04T00060', '2024', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (74, 'be_in_hosp', 'R04', '限频次', 'R0400015', '《关于将部分医疗康复项目纳入基本医疗保障范围的通知》(卫农卫发(2010)80号)中，偏瘫肢体综合训练每日支付不超过2次。', 'R040001501', '', 'OverclockTime', 'day', 'D04T00061', '2024', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (75, 'be_in_hosp', 'R04', '限频次', 'R0400016', '《关于将部分医疗康复项目纳入基本医疗保障范围的通知》(卫农卫发(2010)80号)中，吞咽功能障碍训练1个疾病过程支付不超过3个月。', 'R040001601', '', 'OverclockTime', 'hosp', 'D04T00062', '2024', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (76, 'be_in_hosp', 'R04', '限频次', 'R0400017', '《关于将部分医疗康复项目纳入基本医疗保障范围的通知》(卫农卫发(2010)80号)中，吞咽功能障碍训练每日支付不超过2次', 'R040001701', '', 'OverclockTime', 'day', 'D04T00063', '2024', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (77, 'be_in_hosp', 'R04', '限频次', 'R0400018', '《人力资源社会保障部国家卫生计生委民政部财政部中国残联关于新增部分医疗康复项目纳入基本医疗保障支付范围的通知》(人社部发〔2016〕23号)规定， 一个疾病过程支付不超过90天。', 'R040001801', '', 'OverclockTime', 'hosp', 'D04T00064', '2024', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (78, 'be_in_hosp', 'R04', '限频次', 'R0400019', '《人力资源社会保障部国家卫生计生委民政部财政部中国残联关于新增部分医疗康复项目纳入基本医疗保障支付范围的通知》(人社部发〔2016〕23号)规定，支付不超过30天。', 'R040001901', '', 'OverclockTime', 'hosp', 'D04T00065', '2024', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (79, 'be_in_hosp', 'R04', '限频次', 'R0400020', '《关于将部分医疗康复项目纳入基本医疗保障范围的通知》(卫农卫发(2010)80号)中，作业疗法1个疾病过程支付不超过3个月', 'R040002001', '', 'OverclockTime', 'hosp', 'D04T00066', '2024', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (80, 'be_in_hosp', 'R04', '限频次', 'R0400021', '《关于将部分医疗康复项目纳入基本医疗保障范围的通知》(卫农卫发(2010)80号)中，认知知觉功能障碍训练1个疾病过程支付不超过3个月', 'R040002101', '', 'OverclockTime', 'hosp', 'D04T00068', '2024', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (81, 'be_in_hosp', 'R04', '限频次', 'R0400021', '《关于将部分医疗康复项目纳入基本医疗保障范围的通知》(卫农卫发(2010)80号)中，认知知觉功能障碍训练1个疾病过程支付不超过3个月', 'R040002102', '', 'OverclockTime', 'hosp', 'D04T00067', '2024', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (82, 'be_in_hosp', 'R04', '限频次', 'R0400022', '《关于将部分医疗康复项目纳入基本医疗保障范围的通知》(卫农卫发(2010)80号)中，认知知觉功能障碍训练每日支付不超过2次。', 'R040002201', '', 'OverclockTime', 'day', 'D04T00069', '2024', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (83, 'be_in_hosp', 'R04', '限频次', 'R0400022', '《关于将部分医疗康复项目纳入基本医疗保障范围的通知》(卫农卫发(2010)80号)中，认知知觉功能障碍训练每日支付不超过2次。', 'R040002202', '', 'OverclockTime', 'day', 'D04T00070', '2024', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (84, 'be_in_hosp', 'R04', '限频次', 'R0400023', '《四川省医疗服务价格项目库》内涵：含肺、胸腔、纵隔。', 'R040002301', '', 'OverclockTime', 'day', 'D04T00071', '2024', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (85, 'be_in_hosp', 'R04', '限频次', 'R0400024', '《四川省医疗服务价格项目库》内涵：含肝、胆、胰、脾、双肾', 'R040002401', '', 'OverclockTime', 'day', 'D04T00072', '2024', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (86, 'be_in_hosp', 'R04', '限频次', 'R0400025', '《四川省医疗服务价格项目库》内涵：胃', 'R040002501', '', 'OverclockTime', 'day', 'D04T00073', '2024', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (87, 'be_in_hosp', 'R04', '限频次', 'R0400026', '《四川省医疗服务价格项目库》内涵：含双肾、输尿管、膀胱、前列腺', 'R040002601', '', 'OverclockTime', 'day', 'D04T00074', '2024', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (88, 'be_in_hosp', 'R04', '限频次', 'R0400027', '《四川省医疗服务价格项目库》内涵：含子宫、附件、膀胱及周围组织', 'R040002701', '', 'OverclockTime', 'day', 'D04T00075', '2024', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (89, 'be_in_hosp', 'R04', '限频次', 'R0400028', '《四川省医疗服务价格项目库》内涵：含胎儿及宫腔', 'R040002801', '', 'OverclockTime', 'day', 'D04T00076', '2024', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (90, 'be_in_hosp', 'R04', '限频次', 'R0400029', '《四川省医疗服务价格项目库》内涵：含睾丸、附睾、输精管、精索、前列腺', 'R040002901', '', 'OverclockTime', 'day', 'D04T00077', '2024', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (91, 'be_in_hosp', 'R04', '限频次', 'R0400030', '《四川省医疗服务价格项目库》中，320排(640层)动态容积CT平扫计价单位“次”，不按部位收取', 'R040003001', '', 'OverclockTime', 'day', 'D04T00078', '2024', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (92, 'be_in_hosp', 'R04', '限频次', 'R0400031', '《四川省医疗服务项目价格2024版》中：糖化血红蛋白测定、糖化血红蛋白测定(色谱法)检查项目计价单位为“次”。', 'R040003101', '', 'OverclockTime', 'hosp', 'D04T00079', '2024', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (93, 'be_in_hosp', 'R04', '限频次', 'R0400031', '《四川省医疗服务项目价格2024版》中：糖化血红蛋白测定、糖化血红蛋白测定(色谱法)检查项目计价单位为“次”。', 'R040003102', '', 'OverclockTime', 'hosp', 'D04T00080', '2024', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (94, 'be_in_hosp', 'R04', '限频次', 'R0400032', '《四川省医疗服务项目价格2024版》中：有创性血流动力学监测(床旁)、有创性心内电生理检查等心脏电理检查项目计价单位为“次”。', 'R040003201', '', 'OverclockTime', 'day', 'D04T00082', '2024', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (95, 'be_in_hosp', 'R04', '限频次', 'R0400032', '《四川省医疗服务项目价格2024版》中：有创性血流动力学监测(床旁)、有创性心内电生理检查等心脏电理检查项目计价单位为“次”。', 'R040003202', '', 'OverclockTime', 'day', 'D04T00081', '2024', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (96, 'be_in_hosp', 'R04', '限频次', 'R0400033', '《四川省医疗服务价格项目库》数字化摄影DR检查内涵：每个部位采集次数最多不超过2次。', 'R040003301', '', 'OverclockTime', 'day', 'D04T00083', '2024', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (97, 'be_in_hosp', 'R04', '限频次', 'R0400034', '入院前已进行”丙型肝炎、乙型肝炎测定“等，输血前再次进行“乙肝表面抗原、乙肝表面抗体、乙肝核心抗原、乙肝核心抗体、乙肝e抗体、丙型肝炎病毒抗体、梅毒抗体、艾滋病抗体检查', 'R040003401', '', 'OverclockTime', 'hosp', 'D04T00086', '2024', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (98, 'be_in_hosp', 'R04', '限频次', 'R0400034', '入院前已进行”丙型肝炎、乙型肝炎测定“等，输血前再次进行“乙肝表面抗原、乙肝表面抗体、乙肝核心抗原、乙肝核心抗体、乙肝e抗体、丙型肝炎病毒抗体、梅毒抗体、艾滋病抗体检查', 'R040003402', '', 'OverclockTime', 'hosp', 'D04T00085', '2024', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (99, 'be_in_hosp', 'R04', '限频次', 'R0400034', '入院前已进行”丙型肝炎、乙型肝炎测定“等，输血前再次进行“乙肝表面抗原、乙肝表面抗体、乙肝核心抗原、乙肝核心抗体、乙肝e抗体、丙型肝炎病毒抗体、梅毒抗体、艾滋病抗体检查', 'R040003403', '', 'OverclockTime', 'hosp', 'D04T00084', '2024', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (162, 'be_in_hosp', 'R05', '重复收费', 'R0500001', '根据《四川省医疗服务价格项目》，“经皮冠状动脉内溶栓术”项目含冠脉造影', 'R050000101', '', 'DoubleExistOperator', 'day', 'D05T00001', '2024', '2025-03-09 17:26:12', NULL, '1', 'med_list_codg');
INSERT INTO `hcm_rule_cfg` VALUES (163, 'be_in_hosp', 'R05', '重复收费', 'R0500002', '根据《四川省医疗服务价格项目》，“冠脉内局部放射治疗术”项目含冠脉造影、同位素放射源 及放疗装置的使用', 'R050000201', '', 'DoubleExistOperator', 'day', 'D05T00001', '2024', '2025-03-09 17:26:12', NULL, '1', 'med_list_codg');
INSERT INTO `hcm_rule_cfg` VALUES (164, 'be_in_hosp', 'R05', '重复收费', 'R0500003', '根据《四川省医疗服务价格项目》，“冠脉内局部药物释放治疗术”项目含冠脉造影', 'R050000301', '', 'DoubleExistOperator', 'day', 'D05T00001', '2024', '2025-03-09 17:26:12', NULL, '1', 'med_list_codg');
INSERT INTO `hcm_rule_cfg` VALUES (165, 'be_in_hosp', 'R05', '重复收费', 'R0500004', '根据《四川省医疗服务价格项目》，“经皮激光心肌血管重建术（PMR）”项目含冠脉造影', 'R050000401', '', 'DoubleExistOperator', 'day', 'D05T00001', '2024', '2025-03-09 17:26:12', NULL, '1', 'med_list_codg');
INSERT INTO `hcm_rule_cfg` VALUES (166, 'be_in_hosp', 'R05', '重复收费', 'R0500005', '根据《四川省医疗服务价格项目》，“冠状动脉内超声溶栓术”项目含冠脉造影', 'R050000501', '', 'DoubleExistOperator', 'day', 'D05T00001', '2024', '2025-03-09 17:26:12', NULL, '1', 'med_list_codg');
INSERT INTO `hcm_rule_cfg` VALUES (167, 'be_in_hosp', 'R05', '重复收费', 'R0500006', '根据《四川省医疗服务价格项目》，“冠脉血管内压力导丝测定术”项目含冠状动脉造影', 'R050000601', '', 'DoubleExistOperator', 'day', 'D05T00001', '2024', '2025-03-09 17:26:12', NULL, '1', 'med_list_codg');
INSERT INTO `hcm_rule_cfg` VALUES (168, 'be_in_hosp', 'R05', '重复收费', 'R0500007', '根据《四川省医疗服务价格项目》，经皮冠状动脉内支架置入术（STENT）含为放置冠脉内支架而进行的球囊预扩张和支架打开后的支架内球囊高压扩张及术前的靶血管造影', 'R050000701', '', 'DoubleExistOperator', 'day', 'D05T00002', '2024', '2025-03-09 17:26:12', NULL, '1', 'med_list_codg');
INSERT INTO `hcm_rule_cfg` VALUES (169, 'be_in_hosp', 'R05', '重复收费', 'R0500008', '根据《四川省医疗服务价格项目》，高速冠状动脉内膜旋磨术含旋磨后球囊扩张和/或支架置入及术前的靶血管造', 'R050000801', '', 'DoubleExistOperator', 'day', 'D05T00002', '2024', '2025-03-09 17:26:12', NULL, '1', 'med_list_codg');
INSERT INTO `hcm_rule_cfg` VALUES (170, 'be_in_hosp', 'R05', '重复收费', 'R0500009', '根据《四川省医疗服务价格项目》，经皮冠状动脉腔内激光成形术（ELCA）含激光消融后球囊扩张和/或支架置入及术前的靶血管造影', 'R050000901', '', 'DoubleExistOperator', 'day', 'D05T00002', '2024', '2025-03-09 17:26:12', NULL, '1', 'med_list_codg');
INSERT INTO `hcm_rule_cfg` VALUES (171, 'be_in_hosp', 'R05', '重复收费', 'R0500009', '根据《四川省医疗服务价格项目》，经皮冠状动脉腔内激光成形术（ELCA）含激光消融后球囊扩张和/或支架置入及术前的靶血管造影', 'R050000902', '', 'DoubleExistOperator', 'day', 'D05T00003', '2024', '2025-03-09 17:26:12', NULL, '1', 'med_list_codg');
INSERT INTO `hcm_rule_cfg` VALUES (172, 'be_in_hosp', 'R05', '重复收费', 'R0500010', '根据《四川省医疗服务价格项目》，高速冠状动脉内膜旋磨术含旋磨后球囊扩张和/或支架置入及术前的靶血管造影。', 'R050001001', '', 'DoubleExistOperator', 'day', 'D05T00003', '2024', '2025-03-09 17:26:12', NULL, '1', 'med_list_codg');
INSERT INTO `hcm_rule_cfg` VALUES (173, 'be_in_hosp', 'R05', '重复收费', 'R0500011', '根据《四川省医疗服务项目价格》：经血管介入治疗分类含局部麻醉和数字减影（DSA机）引导使用费', 'R050001101', '', 'DoubleExistOperator', 'day', 'D05T00005', '2024', '2025-03-09 17:26:12', NULL, '1', 'med_list_codg');
INSERT INTO `hcm_rule_cfg` VALUES (174, 'be_in_hosp', 'R05', '重复收费', 'R0500011', '根据《四川省医疗服务项目价格》：经血管介入治疗分类含局部麻醉和数字减影（DSA机）引导使用费', 'R050001102', '', 'DoubleExistOperator', 'day', 'D05T00004', '2024', '2025-03-09 17:26:12', NULL, '1', 'med_list_codg');
INSERT INTO `hcm_rule_cfg` VALUES (175, 'be_in_hosp', 'R05', '重复收费', 'R0500012', '根据《四川省医疗服务价格项目》：冠脉内局部放射治疗术含冠脉造影、同位素放射源及放疗装置的使用', 'R050001201', '', 'DoubleExistOperator', 'day', 'D05T00006', '2024', '2025-03-09 17:26:12', NULL, '1', 'med_list_codg');
INSERT INTO `hcm_rule_cfg` VALUES (176, 'be_in_hosp', 'R05', '重复收费', 'R0500013', '根据《全国医疗服务项目技术规范》（2023年版）：收取遥测心电监护费用同时收取心电监测费用', 'R050001301', '', 'DoubleExistOperator', 'hosp', 'D05T00007', '2024', '2025-03-09 17:26:12', NULL, '1', 'med_list_codg');
INSERT INTO `hcm_rule_cfg` VALUES (177, 'be_in_hosp', 'R05', '重复收费', 'R0500014', '根据《四川省医疗服务价格项目》：收取脊柱椎间融合器植入植骨融合术费用同时收取脊髓和神经根粘连松解术费用', 'R050001401', '', 'DoubleExistOperator', 'day', 'D05T00008', '2024', '2025-03-09 17:26:12', NULL, '1', 'med_list_codg');
INSERT INTO `hcm_rule_cfg` VALUES (178, 'be_in_hosp', 'R05', '重复收费', 'R0500015', '根据《全国医疗服务项目技术规范》（2023年版）：收取后路腰椎间盘镜椎间盘髓核摘除术（MED）费用同时收取椎间盘微创消融术（椎间盘摘除术）费用', 'R050001501', '', 'DoubleExistOperator', 'day', 'D05T00009', '2024', '2025-03-09 17:26:12', NULL, '1', 'med_list_codg');
INSERT INTO `hcm_rule_cfg` VALUES (179, 'be_in_hosp', 'R05', '重复收费', 'R0500016', '根据《全国医疗服务项目技术规范》（2023年版）：收取后路腰椎间盘镜椎间盘髓核摘除术（MED）费用同时收取椎管扩大减压术费用', 'R050001601', '', 'DoubleExistOperator', 'day', 'D05T00009', '2024', '2025-03-09 17:26:12', NULL, '1', 'med_list_codg');
INSERT INTO `hcm_rule_cfg` VALUES (180, 'be_in_hosp', 'R05', '重复收费', 'R0500017', '根据《全国医疗服务项目技术规范》（2023年版）：收取关节腔灌注治疗费用同时收取关节穿刺术费用', 'R050001701', '', 'DoubleExistOperator', 'day', 'D05T00010', '2024', '2025-03-09 17:26:12', NULL, '1', 'med_list_codg');
INSERT INTO `hcm_rule_cfg` VALUES (181, 'be_in_hosp', 'R05', '重复收费', 'R0500018', '收取连续性血液净化费用同时收取血液透析费用', 'R050001801', '', 'DoubleExistOperator', 'day', 'D05T00011', '2024', '2025-03-09 17:26:12', NULL, '1', 'med_list_codg');
INSERT INTO `hcm_rule_cfg` VALUES (182, 'be_in_hosp', 'R05', '重复收费', 'R0500019', '收取连续性血液净化费用同时收取血液透析（碳酸液透析）费用', 'R050001901', '', 'DoubleExistOperator', 'day', 'D05T00011', '2024', '2025-03-09 17:26:12', NULL, '1', 'med_list_codg');
INSERT INTO `hcm_rule_cfg` VALUES (183, 'be_in_hosp', 'R05', '重复收费', 'R0500020', '收取连续性血液净化费用同时收取血液透析（醋酸液透析）费用', 'R050002001', '', 'DoubleExistOperator', 'day', 'D05T00011', '2024', '2025-03-09 17:26:12', NULL, '1', 'med_list_codg');
INSERT INTO `hcm_rule_cfg` VALUES (184, 'be_in_hosp', 'R05', '重复收费', 'R0500021', '收取连续性血液净化费用同时收取血液滤过费用', 'R050002101', '', 'DoubleExistOperator', 'day', 'D05T00011', '2024', '2025-03-09 17:26:12', NULL, '1', 'med_list_codg');
INSERT INTO `hcm_rule_cfg` VALUES (185, 'be_in_hosp', 'R05', '重复收费', 'R0500022', '收取连续性血液净化费用同时收取血液透析滤过费用', 'R050002201', '', 'DoubleExistOperator', 'day', 'D05T00011', '2024', '2025-03-09 17:26:12', NULL, '1', 'med_list_codg');
INSERT INTO `hcm_rule_cfg` VALUES (186, 'be_in_hosp', 'R05', '重复收费', 'R0500023', '同时开展脑瘫肢体综合训练和运动疗法并纳入医保支付', 'R050002301', '', 'DoubleExistOperator', 'day', 'D05T00012', '2024', '2025-03-09 17:26:12', NULL, '1', 'med_list_codg');
INSERT INTO `hcm_rule_cfg` VALUES (187, 'be_in_hosp', 'R05', '重复收费', 'R0500024', '同时开展截瘫肢体综合训练和运动疗法并纳入医保支付', 'R050002401', '', 'DoubleExistOperator', 'day', 'D05T00012', '2024', '2025-03-09 17:26:12', NULL, '1', 'med_list_codg');
INSERT INTO `hcm_rule_cfg` VALUES (188, 'be_in_hosp', 'R05', '重复收费', 'R0500025', '同时开展偏瘫肢体综合训练和运动疗法并纳入医保支付', 'R050002501', '', 'DoubleExistOperator', 'day', 'D05T00012', '2024', '2025-03-09 17:26:12', NULL, '1', 'med_list_codg');
INSERT INTO `hcm_rule_cfg` VALUES (189, 'be_in_hosp', 'R05', '重复收费', 'R0500026', '收取数字化摄影（DR）费用同时收取普通透视费用', 'R050002601', '', 'DoubleExistOperator', 'day', 'D05T00013', '2024', '2025-03-09 17:26:12', NULL, '1', 'med_list_codg');
INSERT INTO `hcm_rule_cfg` VALUES (190, 'be_in_hosp', 'R05', '重复收费', 'R0500027', '收取数字化摄影（DR）费用同时收取普通透视(胸)费用', 'R050002701', '', 'DoubleExistOperator', 'day', 'D05T00013', '2024', '2025-03-09 17:26:12', NULL, '1', 'med_list_codg');
INSERT INTO `hcm_rule_cfg` VALUES (191, 'be_in_hosp', 'R05', '重复收费', 'R0500028', '收取数字化摄影（DR）费用同时收取普通透视(腹)费用', 'R050002801', '', 'DoubleExistOperator', 'day', 'D05T00013', '2024', '2025-03-09 17:26:12', NULL, '1', 'med_list_codg');
INSERT INTO `hcm_rule_cfg` VALUES (192, 'be_in_hosp', 'R05', '重复收费', 'R0500029', '收取数字化摄影（DR）费用同时收取普通透视（盆腔）费用', 'R050002901', '', 'DoubleExistOperator', 'day', 'D05T00013', '2024', '2025-03-09 17:26:12', NULL, '1', 'med_list_codg');
INSERT INTO `hcm_rule_cfg` VALUES (193, 'be_in_hosp', 'R05', '重复收费', 'R0500030', '收取数字化摄影（DR）费用同时收取普通透视(四肢)费用', 'R050003001', '', 'DoubleExistOperator', 'day', 'D05T00013', '2024', '2025-03-09 17:26:12', NULL, '1', 'med_list_codg');
INSERT INTO `hcm_rule_cfg` VALUES (194, 'be_in_hosp', 'R05', '重复收费', 'R0500031', '收取经皮超选择性动脉造影术费用同时收取经皮选择性动脉造影术费用', 'R050003101', '', 'DoubleExistOperator', 'day', 'D05T00014', '2024', '2025-03-09 17:26:12', NULL, '1', 'med_list_codg');
INSERT INTO `hcm_rule_cfg` VALUES (195, 'be_in_hosp', 'R05', '重复收费', 'R0500032', '收取上消化道造影费用同时收取食管造影费用', 'R050003201', '', 'DoubleExistOperator', 'day', 'D05T00015', '2024', '2025-03-09 17:26:12', NULL, '1', 'med_list_codg');
INSERT INTO `hcm_rule_cfg` VALUES (196, 'be_in_hosp', 'R05', '重复收费', 'R0500033', '收取心脏彩色多普勒超声费用同时收取普通心脏M型超声检查费用', 'R050003301', '', 'DoubleExistOperator', 'day', 'D05T00016', '2024', '2025-03-09 17:26:12', NULL, '1', 'med_list_codg');
INSERT INTO `hcm_rule_cfg` VALUES (197, 'be_in_hosp', 'R05', '重复收费', 'R0500034', '收取心脏彩色多普勒超声费用同时收取普通二维超声心动图费用', 'R050003401', '', 'DoubleExistOperator', 'day', 'D05T00016', '2024', '2025-03-09 17:26:12', NULL, '1', 'med_list_codg');
INSERT INTO `hcm_rule_cfg` VALUES (198, 'be_in_hosp', 'R05', '重复收费', 'R0500035', '收取床旁透视与术中透视费用同时收取普通透视费用', 'R050003501', '', 'DoubleExistOperator', 'day', 'D05T00017', '2024', '2025-03-09 17:26:12', NULL, '1', 'med_list_codg');
INSERT INTO `hcm_rule_cfg` VALUES (199, 'be_in_hosp', 'R05', '重复收费', 'R0500036', '收取床旁透视与术中透视费用同时收取普通透视(胸)费用', 'R050003601', '', 'DoubleExistOperator', 'day', 'D05T00017', '2024', '2025-03-09 17:26:12', NULL, '1', 'med_list_codg');
INSERT INTO `hcm_rule_cfg` VALUES (200, 'be_in_hosp', 'R05', '重复收费', 'R0500037', '收取床旁透视与术中透视费用同时收取普通透视(腹)费用', 'R050003701', '', 'DoubleExistOperator', 'day', 'D05T00017', '2024', '2025-03-09 17:26:12', NULL, '1', 'med_list_codg');
INSERT INTO `hcm_rule_cfg` VALUES (201, 'be_in_hosp', 'R05', '重复收费', 'R0500038', '收取床旁透视与术中透视费用同时收取普通透视（盆腔）费用', 'R050003801', '', 'DoubleExistOperator', 'day', 'D05T00017', '2024', '2025-03-09 17:26:12', NULL, '1', 'med_list_codg');
INSERT INTO `hcm_rule_cfg` VALUES (202, 'be_in_hosp', 'R05', '重复收费', 'R0500039', '收取床旁透视与术中透视费用同时收取普通透视(四肢)费用', 'R050003901', '', 'DoubleExistOperator', 'day', 'D05T00017', '2024', '2025-03-09 17:26:12', NULL, '1', 'med_list_codg');
INSERT INTO `hcm_rule_cfg` VALUES (203, 'be_in_hosp', 'R05', '重复收费', 'R0500040', '收取床旁B超检查费用同时收取B超常规检查费用', 'R050004001', '', 'DoubleExistOperator', 'day', 'D05T00018', '2024', '2025-03-09 17:26:12', NULL, '1', 'med_list_codg');
INSERT INTO `hcm_rule_cfg` VALUES (204, 'be_in_hosp', 'R05', '重复收费', 'R0500041', '收取床旁B超检查费用同时收取B超常规检查（胸部）费用', 'R050004101', '', 'DoubleExistOperator', 'day', 'D05T00018', '2024', '2025-03-09 17:26:12', NULL, '1', 'med_list_codg');
INSERT INTO `hcm_rule_cfg` VALUES (205, 'be_in_hosp', 'R05', '重复收费', 'R0500042', '收取床旁B超检查费用同时收取B超常规检查（腹部）费用', 'R050004201', '', 'DoubleExistOperator', 'day', 'D05T00018', '2024', '2025-03-09 17:26:12', NULL, '1', 'med_list_codg');
INSERT INTO `hcm_rule_cfg` VALUES (206, 'be_in_hosp', 'R05', '重复收费', 'R0500043', '收取床旁B超检查费用同时收取B超常规检查（胃肠道）费用', 'R050004301', '', 'DoubleExistOperator', 'day', 'D05T00018', '2024', '2025-03-09 17:26:12', NULL, '1', 'med_list_codg');
INSERT INTO `hcm_rule_cfg` VALUES (207, 'be_in_hosp', 'R05', '重复收费', 'R0500044', '收取床旁B超检查费用同时收取B超常规检查（泌尿系统）费用', 'R050004401', '', 'DoubleExistOperator', 'day', 'D05T00018', '2024', '2025-03-09 17:26:12', NULL, '1', 'med_list_codg');
INSERT INTO `hcm_rule_cfg` VALUES (208, 'be_in_hosp', 'R05', '重复收费', 'R0500045', '收取床旁B超检查费用同时收取B超常规检查（妇科）费用', 'R050004501', '', 'DoubleExistOperator', 'day', 'D05T00018', '2024', '2025-03-09 17:26:12', NULL, '1', 'med_list_codg');
INSERT INTO `hcm_rule_cfg` VALUES (209, 'be_in_hosp', 'R05', '重复收费', 'R0500046', '收取床旁B超检查费用同时收取B超常规检查（产科）费用', 'R050004601', '', 'DoubleExistOperator', 'day', 'D05T00018', '2024', '2025-03-09 17:26:12', NULL, '1', 'med_list_codg');
INSERT INTO `hcm_rule_cfg` VALUES (210, 'be_in_hosp', 'R05', '重复收费', 'R0500047', '收取床旁超声心动图费用同时收取普通二维超声心动图费用', 'R050004701', '', 'DoubleExistOperator', 'day', 'D05T00019', '2024', '2025-03-09 17:26:12', NULL, '1', 'med_list_codg');
INSERT INTO `hcm_rule_cfg` VALUES (211, 'be_in_hosp', 'R05', '重复收费', 'R0500048', '收取临床操作的CT引导费用同时收取X线计算机体层（CT）平扫费用', 'R050004801', '', 'DoubleExistOperator', 'hosp', 'D05T00020', '2024', '2025-03-09 17:26:12', NULL, '1', 'med_list_codg');
INSERT INTO `hcm_rule_cfg` VALUES (212, 'be_in_hosp', 'R05', '重复收费', 'R0500049', '收取临床操作的CT引导费用同时收取普通CT平扫费用', 'R050004901', '', 'DoubleExistOperator', 'hosp', 'D05T00020', '2024', '2025-03-09 17:26:12', NULL, '1', 'med_list_codg');
INSERT INTO `hcm_rule_cfg` VALUES (213, 'be_in_hosp', 'R05', '重复收费', 'R0500050', '收取临床操作的CT引导费用同时收取螺旋CT平扫费用', 'R050005001', '', 'DoubleExistOperator', 'hosp', 'D05T00020', '2024', '2025-03-09 17:26:12', NULL, '1', 'med_list_codg');
INSERT INTO `hcm_rule_cfg` VALUES (214, 'be_in_hosp', 'R05', '重复收费', 'R0500051', '收取临床操作的CT引导费用同时收取单次多层CT平扫费用', 'R050005101', '', 'DoubleExistOperator', 'hosp', 'D05T00020', '2024', '2025-03-09 17:26:12', NULL, '1', 'med_list_codg');
INSERT INTO `hcm_rule_cfg` VALUES (215, 'be_in_hosp', 'R05', '重复收费', 'R0500052', '收取临床操作的磁共振引导费用同时收取磁共振平扫费用', 'R050005201', '', 'DoubleExistOperator', 'hosp', 'D05T00021', '2024', '2025-03-09 17:26:12', NULL, '1', 'med_list_codg');
INSERT INTO `hcm_rule_cfg` VALUES (216, 'be_in_hosp', 'R05', '重复收费', 'R0500053', '收取临床操作的磁共振引导费用同时收取磁共振平扫（0.5T以下，不含0.5T）费用', 'R050005301', '', 'DoubleExistOperator', 'hosp', 'D05T00021', '2024', '2025-03-09 17:26:12', NULL, '1', 'med_list_codg');
INSERT INTO `hcm_rule_cfg` VALUES (217, 'be_in_hosp', 'R05', '重复收费', 'R0500054', '收取临床操作的磁共振引导费用同时收取磁共振平扫（0.5T—1T）费用', 'R050005401', '', 'DoubleExistOperator', 'hosp', 'D05T00021', '2024', '2025-03-09 17:26:12', NULL, '1', 'med_list_codg');
INSERT INTO `hcm_rule_cfg` VALUES (218, 'be_in_hosp', 'R05', '重复收费', 'R0500055', '收取临床操作的磁共振引导费用同时收取磁共振平扫（1T以上，不含1T）费用', 'R050005501', '', 'DoubleExistOperator', 'hosp', 'D05T00021', '2024', '2025-03-09 17:26:12', NULL, '1', 'med_list_codg');
INSERT INTO `hcm_rule_cfg` VALUES (219, 'be_in_hosp', 'R05', '重复收费', 'R0500056', '经胆道镜手术和胆道镜检查。', 'R050005601', '', 'DoubleExistOperator', 'day', 'D05T00030', '2024', '2025-03-09 17:26:12', NULL, '1', 'med_list_codg');
INSERT INTO `hcm_rule_cfg` VALUES (220, 'be_in_hosp', 'R05', '重复收费', 'R0500057', '经膀胱镜尿道镜手术和膀胱镜尿道镜检查。', 'R050005701', '', 'DoubleExistOperator', 'day', 'D05T00031', '2024', '2025-03-09 17:26:12', NULL, '1', 'med_list_codg');
INSERT INTO `hcm_rule_cfg` VALUES (221, 'be_in_hosp', 'R05', '重复收费', 'R0500058', '经输尿管镜手术和经尿道输尿管镜检查。', 'R050005801', '', 'DoubleExistOperator', 'day', 'D05T00032', '2024', '2025-03-09 17:26:12', NULL, '1', 'med_list_codg');
INSERT INTO `hcm_rule_cfg` VALUES (222, 'be_in_hosp', 'R05', '重复收费', 'R0500059', '经宫腔镜类手术和经宫腔镜检查。', 'R050005901', '', 'DoubleExistOperator', 'day', 'D05T00033', '2024', '2025-03-09 17:26:12', NULL, '1', 'med_list_codg');
INSERT INTO `hcm_rule_cfg` VALUES (223, 'be_in_hosp', 'R05', '重复收费', 'R0500060', '经腹腔镜类手术和腹腔镜检查。', 'R050006001', '', 'DoubleExistOperator', 'day', 'D05T00034', '2024', '2025-03-09 17:26:12', NULL, '1', 'med_list_codg');
INSERT INTO `hcm_rule_cfg` VALUES (479, 'be_in_hosp', 'R01', '超标准收费', 'R0100001', '根据《四川省医疗服务价格项目》：开展腔镜等微创类手术时，超标准收取特大换药或大换药费用', 'R010000101', '', 'DoubleExistOperator', 'hosp', 'D01T00001', '2025', '2025-03-09 17:23:32', NULL, '1', 'med_list_codg');
INSERT INTO `hcm_rule_cfg` VALUES (480, 'be_in_hosp', 'R01', '超标准收费', 'R0100001', '根据《四川省医疗服务价格项目》：开展腔镜等微创类手术时，超标准收取特大换药或大换药费用', 'R010000102', '', 'DoubleExistOperator', 'hosp', 'D01T00002', '2025', '2025-03-09 17:23:32', NULL, '1', 'med_list_codg');
INSERT INTO `hcm_rule_cfg` VALUES (481, 'be_in_hosp', 'R02', '违规使用项目', 'R0200001', '不合规使用医疗服务项目', 'R020000101', '', 'ExistOperator', 'hosp', 'D02T00001', '2025', '2025-03-09 17:24:25', NULL, '1', 'med_list_codg');
INSERT INTO `hcm_rule_cfg` VALUES (482, 'be_in_hosp', 'R03', '过度检查', 'R0300001', '开展“数字化摄影(DR)”检查，收取“数字化摄影(DR)”费用，同时将“胶片”的费用按照诊疗项目类“X线摄影”进行收费', 'R030000101', '', 'DoubleExistOperator', 'day', 'D03T00001', '2025', '2025-03-09 17:24:38', NULL, '1', 'med_list_codg');
INSERT INTO `hcm_rule_cfg` VALUES (483, 'be_in_hosp', 'R03', '过度检查', 'R0300002', '行“X线计算机体层(CT)平扫”的患者均打包收取“三维重建”费用.其中，“三维重建”属于过度检查。', 'R030000201', '', 'DoubleExistOperator', 'day', 'D03T00002', '2025', '2025-03-09 17:24:38', NULL, '1', 'med_list_codg');
INSERT INTO `hcm_rule_cfg` VALUES (484, 'be_in_hosp', 'R03', '过度检查', 'R0300003', '“粪便隐血试验”一般用于消化道慢性出血的诊断.某医院将“粪寄生虫镜检”“粪便隐血试验”与大便常规检查打包', 'R030000301', '', 'DoubleExistOperator', 'day', 'D03T00003', '2025', '2025-03-09 17:24:38', NULL, '1', 'med_list_codg');
INSERT INTO `hcm_rule_cfg` VALUES (485, 'be_in_hosp', 'R03', '过度检查', 'R0300004', '开展“C一反应蛋白”检查时，打包“超敏C反应蛋白”,属于过度检查。', 'R030000401', '', 'DoubleExistOperator', 'day', 'D03T00004', '2025', '2025-03-09 17:24:38', NULL, '1', 'med_list_codg');
INSERT INTO `hcm_rule_cfg` VALUES (486, 'be_in_hosp', 'R03', '过度检查', 'R0300005', '没有根据病人实际病情，将“降钙素原(PCT)”检测、“超敏C反应蛋白(hs-CRP)”打包，属于过度检查。', 'R030000501', '', 'DoubleExistOperator', 'day', 'D03T00004', '2025', '2025-03-09 17:24:38', NULL, '1', 'med_list_codg');
INSERT INTO `hcm_rule_cfg` VALUES (487, 'be_in_hosp', 'R03', '过度检查', 'R0300006', '“不加热血清反应素试验”与“梅毒螺旋体特异抗体测定”打包，属于过度检查', 'R030000601', '', 'DoubleExistOperator', 'day', 'D03T00005', '2025', '2025-03-09 17:24:38', NULL, '1', 'med_list_codg');
INSERT INTO `hcm_rule_cfg` VALUES (488, 'be_in_hosp', 'R03', '过度检查', 'R0300007', '将“癌胚抗原测定\"“糖类抗原测定”列入患者入院常规检查大规模开展，属于过度检查。', 'R030000701', '', 'DoubleExistOperator', 'day', 'D03T00006', '2025', '2025-03-09 17:24:38', NULL, '1', 'med_list_codg');
INSERT INTO `hcm_rule_cfg` VALUES (489, 'be_in_hosp', 'R03', '过度检查', 'R0300008', '进行“阴道分泌物检查”时，将“特殊细菌涂片检查、 一般细菌涂片检查、真菌涂片检查，特殊细菌涂片检查、一般细菌涂片检查、真菌涂片检查”打包检查，属于过度检查。', 'R030000801', '', 'DoubleExistOperator', 'day', 'D03T00007', '2025', '2025-03-09 17:24:38', NULL, '1', 'med_list_codg');
INSERT INTO `hcm_rule_cfg` VALUES (490, 'be_in_hosp', 'R03', '过度检查', 'R0300009', '进行“血气分析”时，没有根据病人实际病情，打包“血浆乳酸测定”检查，属于过度检查', 'R030000901', '', 'DoubleExistOperator', 'day', 'D03T00008', '2025', '2025-03-09 17:24:38', NULL, '1', 'med_list_codg');
INSERT INTO `hcm_rule_cfg` VALUES (491, 'be_in_hosp', 'R03', '过度检查', 'R0300010', '无贫血情况的病人，进行“糖化血红蛋白测定”时检查时，打包针对贫血检查项目“抗碱血红蛋白测定(HbF)”检查，属于过度检查', 'R030001001', '', 'DoubleExistOperator', 'day', 'D03T00009', '2025', '2025-03-09 17:24:38', NULL, '1', 'med_list_codg');
INSERT INTO `hcm_rule_cfg` VALUES (492, 'be_in_hosp', 'R04', '限频次', 'R0400001', '《四川省医疗服务价格项目》中：按小时收费，每天不超限定阈值24', 'R040000101', '', 'OverclockTime', 'day', 'D04T00002', '2025', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (493, 'be_in_hosp', 'R04', '限频次', 'R0400001', '《四川省医疗服务价格项目》中：按小时收费，每天不超限定阈值24', 'R040000102', '', 'OverclockTime', 'day', 'D04T00001', '2025', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (494, 'be_in_hosp', 'R04', '限频次', 'R0400002', '《四川省医疗服务价格项目》中：以扩张一支冠脉血管为基价，扩张多支血管按该主项目的加收项目加收', 'R040000201', '', 'OverclockTime', 'day', 'D04T00011', '2025', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (495, 'be_in_hosp', 'R04', '限频次', 'R0400002', '《四川省医疗服务价格项目》中：以扩张一支冠脉血管为基价，扩张多支血管按该主项目的加收项目加收', 'R040000202', '', 'OverclockTime', 'day', 'D04T00004', '2025', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (496, 'be_in_hosp', 'R04', '限频次', 'R0400002', '《四川省医疗服务价格项目》中：以扩张一支冠脉血管为基价，扩张多支血管按该主项目的加收项目加收', 'R040000203', '', 'OverclockTime', 'day', 'D04T00008', '2025', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (497, 'be_in_hosp', 'R04', '限频次', 'R0400002', '《四川省医疗服务价格项目》中：以扩张一支冠脉血管为基价，扩张多支血管按该主项目的加收项目加收', 'R040000204', '', 'OverclockTime', 'day', 'D04T00006', '2025', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (498, 'be_in_hosp', 'R04', '限频次', 'R0400002', '《四川省医疗服务价格项目》中：以扩张一支冠脉血管为基价，扩张多支血管按该主项目的加收项目加收', 'R040000205', '', 'OverclockTime', 'day', 'D04T00003', '2025', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (499, 'be_in_hosp', 'R04', '限频次', 'R0400002', '《四川省医疗服务价格项目》中：以扩张一支冠脉血管为基价，扩张多支血管按该主项目的加收项目加收', 'R040000206', '', 'OverclockTime', 'day', 'D04T00010', '2025', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (500, 'be_in_hosp', 'R04', '限频次', 'R0400002', '《四川省医疗服务价格项目》中：以扩张一支冠脉血管为基价，扩张多支血管按该主项目的加收项目加收', 'R040000207', '', 'OverclockTime', 'day', 'D04T00012', '2025', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (501, 'be_in_hosp', 'R04', '限频次', 'R0400002', '《四川省医疗服务价格项目》中：以扩张一支冠脉血管为基价，扩张多支血管按该主项目的加收项目加收', 'R040000208', '', 'OverclockTime', 'day', 'D04T00013', '2025', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (502, 'be_in_hosp', 'R04', '限频次', 'R0400002', '《四川省医疗服务价格项目》中：以扩张一支冠脉血管为基价，扩张多支血管按该主项目的加收项目加收', 'R040000209', '', 'OverclockTime', 'day', 'D04T00007', '2025', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (503, 'be_in_hosp', 'R04', '限频次', 'R0400002', '《四川省医疗服务价格项目》中：以扩张一支冠脉血管为基价，扩张多支血管按该主项目的加收项目加收', 'R040000210', '', 'OverclockTime', 'day', 'D04T00009', '2025', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (504, 'be_in_hosp', 'R04', '限频次', 'R0400002', '《四川省医疗服务价格项目》中：以扩张一支冠脉血管为基价，扩张多支血管按该主项目的加收项目加收', 'R040000211', '', 'OverclockTime', 'day', 'D04T00005', '2025', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (505, 'be_in_hosp', 'R04', '限频次', 'R0400003', '《四川省医疗服务价格项目》中：经血管介入治疗原则上以经一根血管的介入治疗为起点，每增加一根血管的治疗按20%加收，最高加收不超过该项治疗的100', 'R040000301', '', 'OverclockTime', 'day', 'D04T00015', '2025', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (506, 'be_in_hosp', 'R04', '限频次', 'R0400003', '《四川省医疗服务价格项目》中：经血管介入治疗原则上以经一根血管的介入治疗为起点，每增加一根血管的治疗按20%加收，最高加收不超过该项治疗的100', 'R040000302', '', 'OverclockTime', 'day', 'D04T00016', '2025', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (507, 'be_in_hosp', 'R04', '限频次', 'R0400003', '《四川省医疗服务价格项目》中：经血管介入治疗原则上以经一根血管的介入治疗为起点，每增加一根血管的治疗按20%加收，最高加收不超过该项治疗的100', 'R040000303', '', 'OverclockTime', 'day', 'D04T00017', '2025', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (508, 'be_in_hosp', 'R04', '限频次', 'R0400003', '《四川省医疗服务价格项目》中：经血管介入治疗原则上以经一根血管的介入治疗为起点，每增加一根血管的治疗按20%加收，最高加收不超过该项治疗的100', 'R040000304', '', 'OverclockTime', 'day', 'D04T00014', '2025', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (509, 'be_in_hosp', 'R04', '限频次', 'R0400003', '《四川省医疗服务价格项目》中：经血管介入治疗原则上以经一根血管的介入治疗为起点，每增加一根血管的治疗按20%加收，最高加收不超过该项治疗的100', 'R040000305', '', 'OverclockTime', 'day', 'D04T00018', '2025', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (510, 'be_in_hosp', 'R04', '限频次', 'R0400004', '《四川省医疗服务价格项目汇编》中，血液透析、血液灌流、血液滤过、血液透析滤过、血透监测项目计价单位为“次”，不能按“小时”收费。', 'R040000401', '', 'OverclockTime', 'day', 'D04T00026', '2025', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (511, 'be_in_hosp', 'R04', '限频次', 'R0400004', '《四川省医疗服务价格项目汇编》中，血液透析、血液灌流、血液滤过、血液透析滤过、血透监测项目计价单位为“次”，不能按“小时”收费。', 'R040000402', '', 'OverclockTime', 'day', 'D04T00021', '2025', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (512, 'be_in_hosp', 'R04', '限频次', 'R0400004', '《四川省医疗服务价格项目汇编》中，血液透析、血液灌流、血液滤过、血液透析滤过、血透监测项目计价单位为“次”，不能按“小时”收费。', 'R040000403', '', 'OverclockTime', 'day', 'D04T00023', '2025', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (513, 'be_in_hosp', 'R04', '限频次', 'R0400004', '《四川省医疗服务价格项目汇编》中，血液透析、血液灌流、血液滤过、血液透析滤过、血透监测项目计价单位为“次”，不能按“小时”收费。', 'R040000404', '', 'OverclockTime', 'day', 'D04T00020', '2025', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (514, 'be_in_hosp', 'R04', '限频次', 'R0400004', '《四川省医疗服务价格项目汇编》中，血液透析、血液灌流、血液滤过、血液透析滤过、血透监测项目计价单位为“次”，不能按“小时”收费。', 'R040000405', '', 'OverclockTime', 'day', 'D04T00024', '2025', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (515, 'be_in_hosp', 'R04', '限频次', 'R0400004', '《四川省医疗服务价格项目汇编》中，血液透析、血液灌流、血液滤过、血液透析滤过、血透监测项目计价单位为“次”，不能按“小时”收费。', 'R040000406', '', 'OverclockTime', 'day', 'D04T00027', '2025', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (516, 'be_in_hosp', 'R04', '限频次', 'R0400004', '《四川省医疗服务价格项目汇编》中，血液透析、血液灌流、血液滤过、血液透析滤过、血透监测项目计价单位为“次”，不能按“小时”收费。', 'R040000407', '', 'OverclockTime', 'day', 'D04T00019', '2025', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (517, 'be_in_hosp', 'R04', '限频次', 'R0400004', '《四川省医疗服务价格项目汇编》中，血液透析、血液灌流、血液滤过、血液透析滤过、血透监测项目计价单位为“次”，不能按“小时”收费。', 'R040000408', '', 'OverclockTime', 'day', 'D04T00022', '2025', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (518, 'be_in_hosp', 'R04', '限频次', 'R0400004', '《四川省医疗服务价格项目汇编》中，血液透析、血液灌流、血液滤过、血液透析滤过、血透监测项目计价单位为“次”，不能按“小时”收费。', 'R040000409', '', 'OverclockTime', 'day', 'D04T00025', '2025', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (519, 'be_in_hosp', 'R04', '限频次', 'R0400005', '《人力资源社会保障部国家卫生计生委民政部财政部中国残联关于新增部分医疗康复项目纳入基本医疗保障支付范围的通知》(人社部发〔2016〕23号)中，言语能力筛查项目支付条件：限支付疑似言语功能障碍患者，不包括言语功能不能恢复的患者， 一个疾病过程支付不超过两次。', 'R040000501', '', 'OverclockTime', 'hosp', 'D04T00032', '2025', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (520, 'be_in_hosp', 'R04', '限频次', 'R0400005', '《人力资源社会保障部国家卫生计生委民政部财政部中国残联关于新增部分医疗康复项目纳入基本医疗保障支付范围的通知》(人社部发〔2016〕23号)中，言语能力筛查项目支付条件：限支付疑似言语功能障碍患者，不包括言语功能不能恢复的患者， 一个疾病过程支付不超过两次。', 'R040000502', '', 'OverclockTime', 'hosp', 'D04T00029', '2025', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (521, 'be_in_hosp', 'R04', '限频次', 'R0400005', '《人力资源社会保障部国家卫生计生委民政部财政部中国残联关于新增部分医疗康复项目纳入基本医疗保障支付范围的通知》(人社部发〔2016〕23号)中，言语能力筛查项目支付条件：限支付疑似言语功能障碍患者，不包括言语功能不能恢复的患者， 一个疾病过程支付不超过两次。', 'R040000503', '', 'OverclockTime', 'hosp', 'D04T00031', '2025', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (522, 'be_in_hosp', 'R04', '限频次', 'R0400005', '《人力资源社会保障部国家卫生计生委民政部财政部中国残联关于新增部分医疗康复项目纳入基本医疗保障支付范围的通知》(人社部发〔2016〕23号)中，言语能力筛查项目支付条件：限支付疑似言语功能障碍患者，不包括言语功能不能恢复的患者， 一个疾病过程支付不超过两次。', 'R040000504', '', 'OverclockTime', 'hosp', 'D04T00030', '2025', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (523, 'be_in_hosp', 'R04', '限频次', 'R0400005', '《人力资源社会保障部国家卫生计生委民政部财政部中国残联关于新增部分医疗康复项目纳入基本医疗保障支付范围的通知》(人社部发〔2016〕23号)中，言语能力筛查项目支付条件：限支付疑似言语功能障碍患者，不包括言语功能不能恢复的患者， 一个疾病过程支付不超过两次。', 'R040000505', '', 'OverclockTime', 'hosp', 'D04T00028', '2025', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (524, 'be_in_hosp', 'R04', '限频次', 'R0400005', '《人力资源社会保障部国家卫生计生委民政部财政部中国残联关于新增部分医疗康复项目纳入基本医疗保障支付范围的通知》(人社部发〔2016〕23号)中，言语能力筛查项目支付条件：限支付疑似言语功能障碍患者，不包括言语功能不能恢复的患者， 一个疾病过程支付不超过两次。', 'R040000506', '', 'OverclockTime', 'hosp', 'D04T00033', '2025', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (525, 'be_in_hosp', 'R04', '限频次', 'R0400006', '《人力资源社会保障部国家卫生计生委民政部财政部中国残联关于新增部分医疗康复项目纳入基本医疗保障支付范围的通知》(人社部发〔2016〕23号)中，吞咽功能障碍检查项目支付条件：一个疾病过程支付不超过三次。', 'R040000601', '', 'OverclockTime', 'hosp', 'D04T00034', '2025', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (526, 'be_in_hosp', 'R04', '限频次', 'R0400007', '《人力资源社会保障部国家卫生计生委民政部财政部中国残联关于新增部分医疗康复项目纳入基本医疗保障支付范围的通知》(人社部发〔2016〕23号)中，手功能评定项目支付条件：明确手功能障碍，且支付总时间不超过90天，评定间隔时间不短于14天。', 'R040000701', '', 'OverclockTime', 'hosp', 'D04T00036', '2025', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (527, 'be_in_hosp', 'R04', '限频次', 'R0400007', '《人力资源社会保障部国家卫生计生委民政部财政部中国残联关于新增部分医疗康复项目纳入基本医疗保障支付范围的通知》(人社部发〔2016〕23号)中，手功能评定项目支付条件：明确手功能障碍，且支付总时间不超过90天，评定间隔时间不短于14天。', 'R040000702', '', 'OverclockTime', 'hosp', 'D04T00037', '2025', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (528, 'be_in_hosp', 'R04', '限频次', 'R0400007', '《人力资源社会保障部国家卫生计生委民政部财政部中国残联关于新增部分医疗康复项目纳入基本医疗保障支付范围的通知》(人社部发〔2016〕23号)中，手功能评定项目支付条件：明确手功能障碍，且支付总时间不超过90天，评定间隔时间不短于14天。', 'R040000703', '', 'OverclockTime', 'hosp', 'D04T00035', '2025', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (529, 'be_in_hosp', 'R04', '限频次', 'R0400008', '依据《关于将部分医疗康复项目纳入基本医疗保障范围的通知》(卫农卫发(2010)80号)文件，脑瘫肢体综合训练每日支付不超过2次。', 'R040000801', '', 'OverclockTime', 'day', 'D04T00038', '2025', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (530, 'be_in_hosp', 'R04', '限频次', 'R0400009', '《关于将部分医疗康复项目纳入基本医疗保障范围的通知》(卫农卫发(2010)80号)中，截瘫肢体综合训练1个疾病过程支付不超过3个月', 'R040000901', '', 'OverclockTime', 'hosp', 'D04T00039', '2025', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (531, 'be_in_hosp', 'R04', '限频次', 'R0400010', '《关于将部分医疗康复项目纳入基本医疗保障范围的通知》(卫农卫发(2010)80号)中，截瘫肢体综合训练每日医保支付不超过2次。', 'R040001001', '', 'OverclockTime', 'day', 'D04T00040', '2025', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (532, 'be_in_hosp', 'R04', '限频次', 'R0400011', '《关于将部分医疗康复项目纳入基本医疗保障范围的通知》(卫农卫发(2010)80号)中，运动疗法1个疾病过程支付不超过3个月', 'R040001101', '', 'OverclockTime', 'hosp', 'D04T00041', '2025', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (533, 'be_in_hosp', 'R04', '限频次', 'R0400011', '《关于将部分医疗康复项目纳入基本医疗保障范围的通知》(卫农卫发(2010)80号)中，运动疗法1个疾病过程支付不超过3个月', 'R040001102', '', 'OverclockTime', 'hosp', 'D04T00042', '2025', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (534, 'be_in_hosp', 'R04', '限频次', 'R0400011', '《关于将部分医疗康复项目纳入基本医疗保障范围的通知》(卫农卫发(2010)80号)中，运动疗法1个疾病过程支付不超过3个月', 'R040001103', '', 'OverclockTime', 'hosp', 'D04T00048', '2025', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (535, 'be_in_hosp', 'R04', '限频次', 'R0400011', '《关于将部分医疗康复项目纳入基本医疗保障范围的通知》(卫农卫发(2010)80号)中，运动疗法1个疾病过程支付不超过3个月', 'R040001104', '', 'OverclockTime', 'hosp', 'D04T00046', '2025', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (536, 'be_in_hosp', 'R04', '限频次', 'R0400011', '《关于将部分医疗康复项目纳入基本医疗保障范围的通知》(卫农卫发(2010)80号)中，运动疗法1个疾病过程支付不超过3个月', 'R040001105', '', 'OverclockTime', 'hosp', 'D04T00047', '2025', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (537, 'be_in_hosp', 'R04', '限频次', 'R0400011', '《关于将部分医疗康复项目纳入基本医疗保障范围的通知》(卫农卫发(2010)80号)中，运动疗法1个疾病过程支付不超过3个月', 'R040001106', '', 'OverclockTime', 'hosp', 'D04T00045', '2025', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (538, 'be_in_hosp', 'R04', '限频次', 'R0400011', '《关于将部分医疗康复项目纳入基本医疗保障范围的通知》(卫农卫发(2010)80号)中，运动疗法1个疾病过程支付不超过3个月', 'R040001107', '', 'OverclockTime', 'hosp', 'D04T00049', '2025', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (539, 'be_in_hosp', 'R04', '限频次', 'R0400011', '《关于将部分医疗康复项目纳入基本医疗保障范围的通知》(卫农卫发(2010)80号)中，运动疗法1个疾病过程支付不超过3个月', 'R040001108', '', 'OverclockTime', 'hosp', 'D04T00043', '2025', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (540, 'be_in_hosp', 'R04', '限频次', 'R0400011', '《关于将部分医疗康复项目纳入基本医疗保障范围的通知》(卫农卫发(2010)80号)中，运动疗法1个疾病过程支付不超过3个月', 'R040001109', '', 'OverclockTime', 'hosp', 'D04T00044', '2025', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (541, 'be_in_hosp', 'R04', '限频次', 'R0400012', '《关于将部分医疗康复项目纳入基本医疗保障范围的通知》(卫农卫发(2010)80号)中，运动疗法每日支付不超过2次', 'R040001201', '', 'OverclockTime', 'day', 'D04T00053', '2025', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (542, 'be_in_hosp', 'R04', '限频次', 'R0400012', '《关于将部分医疗康复项目纳入基本医疗保障范围的通知》(卫农卫发(2010)80号)中，运动疗法每日支付不超过2次', 'R040001202', '', 'OverclockTime', 'day', 'D04T00055', '2025', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (543, 'be_in_hosp', 'R04', '限频次', 'R0400012', '《关于将部分医疗康复项目纳入基本医疗保障范围的通知》(卫农卫发(2010)80号)中，运动疗法每日支付不超过2次', 'R040001203', '', 'OverclockTime', 'day', 'D04T00058', '2025', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (544, 'be_in_hosp', 'R04', '限频次', 'R0400012', '《关于将部分医疗康复项目纳入基本医疗保障范围的通知》(卫农卫发(2010)80号)中，运动疗法每日支付不超过2次', 'R040001204', '', 'OverclockTime', 'day', 'D04T00051', '2025', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (545, 'be_in_hosp', 'R04', '限频次', 'R0400012', '《关于将部分医疗康复项目纳入基本医疗保障范围的通知》(卫农卫发(2010)80号)中，运动疗法每日支付不超过2次', 'R040001205', '', 'OverclockTime', 'day', 'D04T00056', '2025', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (546, 'be_in_hosp', 'R04', '限频次', 'R0400012', '《关于将部分医疗康复项目纳入基本医疗保障范围的通知》(卫农卫发(2010)80号)中，运动疗法每日支付不超过2次', 'R040001206', '', 'OverclockTime', 'day', 'D04T00057', '2025', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (547, 'be_in_hosp', 'R04', '限频次', 'R0400012', '《关于将部分医疗康复项目纳入基本医疗保障范围的通知》(卫农卫发(2010)80号)中，运动疗法每日支付不超过2次', 'R040001207', '', 'OverclockTime', 'day', 'D04T00054', '2025', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (548, 'be_in_hosp', 'R04', '限频次', 'R0400012', '《关于将部分医疗康复项目纳入基本医疗保障范围的通知》(卫农卫发(2010)80号)中，运动疗法每日支付不超过2次', 'R040001208', '', 'OverclockTime', 'day', 'D04T00050', '2025', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (549, 'be_in_hosp', 'R04', '限频次', 'R0400012', '《关于将部分医疗康复项目纳入基本医疗保障范围的通知》(卫农卫发(2010)80号)中，运动疗法每日支付不超过2次', 'R040001209', '', 'OverclockTime', 'day', 'D04T00052', '2025', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (550, 'be_in_hosp', 'R04', '限频次', 'R0400013', '《关于将部分医疗康复项目纳入基本医疗保障范围的通知》(卫农卫发(2010)80号)中，作业疗法每日支付不超过1次', 'R040001301', '', 'OverclockTime', 'day', 'D04T00059', '2025', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (551, 'be_in_hosp', 'R04', '限频次', 'R0400014', '《关于将部分医疗康复项目纳入基本医疗保障范围的通知》(卫农卫发(2010)80号)中，偏瘫肢体综合训练1个疾病过程支付不超过3个月', 'R040001401', '', 'OverclockTime', 'hosp', 'D04T00060', '2025', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (552, 'be_in_hosp', 'R04', '限频次', 'R0400015', '《关于将部分医疗康复项目纳入基本医疗保障范围的通知》(卫农卫发(2010)80号)中，偏瘫肢体综合训练每日支付不超过2次。', 'R040001501', '', 'OverclockTime', 'day', 'D04T00061', '2025', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (553, 'be_in_hosp', 'R04', '限频次', 'R0400016', '《关于将部分医疗康复项目纳入基本医疗保障范围的通知》(卫农卫发(2010)80号)中，吞咽功能障碍训练1个疾病过程支付不超过3个月。', 'R040001601', '', 'OverclockTime', 'hosp', 'D04T00062', '2025', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (554, 'be_in_hosp', 'R04', '限频次', 'R0400017', '《关于将部分医疗康复项目纳入基本医疗保障范围的通知》(卫农卫发(2010)80号)中，吞咽功能障碍训练每日支付不超过2次', 'R040001701', '', 'OverclockTime', 'day', 'D04T00063', '2025', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (555, 'be_in_hosp', 'R04', '限频次', 'R0400018', '《人力资源社会保障部国家卫生计生委民政部财政部中国残联关于新增部分医疗康复项目纳入基本医疗保障支付范围的通知》(人社部发〔2016〕23号)规定， 一个疾病过程支付不超过90天。', 'R040001801', '', 'OverclockTime', 'hosp', 'D04T00064', '2025', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (556, 'be_in_hosp', 'R04', '限频次', 'R0400019', '《人力资源社会保障部国家卫生计生委民政部财政部中国残联关于新增部分医疗康复项目纳入基本医疗保障支付范围的通知》(人社部发〔2016〕23号)规定，支付不超过30天。', 'R040001901', '', 'OverclockTime', 'hosp', 'D04T00065', '2025', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (557, 'be_in_hosp', 'R04', '限频次', 'R0400020', '《关于将部分医疗康复项目纳入基本医疗保障范围的通知》(卫农卫发(2010)80号)中，作业疗法1个疾病过程支付不超过3个月', 'R040002001', '', 'OverclockTime', 'hosp', 'D04T00066', '2025', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (558, 'be_in_hosp', 'R04', '限频次', 'R0400021', '《关于将部分医疗康复项目纳入基本医疗保障范围的通知》(卫农卫发(2010)80号)中，认知知觉功能障碍训练1个疾病过程支付不超过3个月', 'R040002101', '', 'OverclockTime', 'hosp', 'D04T00068', '2025', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (559, 'be_in_hosp', 'R04', '限频次', 'R0400021', '《关于将部分医疗康复项目纳入基本医疗保障范围的通知》(卫农卫发(2010)80号)中，认知知觉功能障碍训练1个疾病过程支付不超过3个月', 'R040002102', '', 'OverclockTime', 'hosp', 'D04T00067', '2025', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (560, 'be_in_hosp', 'R04', '限频次', 'R0400022', '《关于将部分医疗康复项目纳入基本医疗保障范围的通知》(卫农卫发(2010)80号)中，认知知觉功能障碍训练每日支付不超过2次。', 'R040002201', '', 'OverclockTime', 'day', 'D04T00069', '2025', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (561, 'be_in_hosp', 'R04', '限频次', 'R0400022', '《关于将部分医疗康复项目纳入基本医疗保障范围的通知》(卫农卫发(2010)80号)中，认知知觉功能障碍训练每日支付不超过2次。', 'R040002202', '', 'OverclockTime', 'day', 'D04T00070', '2025', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (562, 'be_in_hosp', 'R04', '限频次', 'R0400023', '《四川省医疗服务价格项目库》内涵：含肺、胸腔、纵隔。', 'R040002301', '', 'OverclockTime', 'day', 'D04T00071', '2025', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (563, 'be_in_hosp', 'R04', '限频次', 'R0400024', '《四川省医疗服务价格项目库》内涵：含肝、胆、胰、脾、双肾', 'R040002401', '', 'OverclockTime', 'day', 'D04T00072', '2025', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (564, 'be_in_hosp', 'R04', '限频次', 'R0400025', '《四川省医疗服务价格项目库》内涵：胃', 'R040002501', '', 'OverclockTime', 'day', 'D04T00073', '2025', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (565, 'be_in_hosp', 'R04', '限频次', 'R0400026', '《四川省医疗服务价格项目库》内涵：含双肾、输尿管、膀胱、前列腺', 'R040002601', '', 'OverclockTime', 'day', 'D04T00074', '2025', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (566, 'be_in_hosp', 'R04', '限频次', 'R0400027', '《四川省医疗服务价格项目库》内涵：含子宫、附件、膀胱及周围组织', 'R040002701', '', 'OverclockTime', 'day', 'D04T00075', '2025', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (567, 'be_in_hosp', 'R04', '限频次', 'R0400028', '《四川省医疗服务价格项目库》内涵：含胎儿及宫腔', 'R040002801', '', 'OverclockTime', 'day', 'D04T00076', '2025', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (568, 'be_in_hosp', 'R04', '限频次', 'R0400029', '《四川省医疗服务价格项目库》内涵：含睾丸、附睾、输精管、精索、前列腺', 'R040002901', '', 'OverclockTime', 'day', 'D04T00077', '2025', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (569, 'be_in_hosp', 'R04', '限频次', 'R0400030', '《四川省医疗服务价格项目库》中，320排(640层)动态容积CT平扫计价单位“次”，不按部位收取', 'R040003001', '', 'OverclockTime', 'day', 'D04T00078', '2025', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (570, 'be_in_hosp', 'R04', '限频次', 'R0400031', '《四川省医疗服务项目价格2024版》中：糖化血红蛋白测定、糖化血红蛋白测定(色谱法)检查项目计价单位为“次”。', 'R040003101', '', 'OverclockTime', 'hosp', 'D04T00079', '2025', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (571, 'be_in_hosp', 'R04', '限频次', 'R0400031', '《四川省医疗服务项目价格2024版》中：糖化血红蛋白测定、糖化血红蛋白测定(色谱法)检查项目计价单位为“次”。', 'R040003102', '', 'OverclockTime', 'hosp', 'D04T00080', '2025', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (572, 'be_in_hosp', 'R04', '限频次', 'R0400032', '《四川省医疗服务项目价格2024版》中：有创性血流动力学监测(床旁)、有创性心内电生理检查等心脏电理检查项目计价单位为“次”。', 'R040003201', '', 'OverclockTime', 'day', 'D04T00082', '2025', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (573, 'be_in_hosp', 'R04', '限频次', 'R0400032', '《四川省医疗服务项目价格2024版》中：有创性血流动力学监测(床旁)、有创性心内电生理检查等心脏电理检查项目计价单位为“次”。', 'R040003202', '', 'OverclockTime', 'day', 'D04T00081', '2025', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (574, 'be_in_hosp', 'R04', '限频次', 'R0400033', '《四川省医疗服务价格项目库》数字化摄影DR检查内涵：每个部位采集次数最多不超过2次。', 'R040003301', '', 'OverclockTime', 'day', 'D04T00083', '2025', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (575, 'be_in_hosp', 'R04', '限频次', 'R0400034', '入院前已进行”丙型肝炎、乙型肝炎测定“等，输血前再次进行“乙肝表面抗原、乙肝表面抗体、乙肝核心抗原、乙肝核心抗体、乙肝e抗体、丙型肝炎病毒抗体、梅毒抗体、艾滋病抗体检查', 'R040003401', '', 'OverclockTime', 'hosp', 'D04T00086', '2025', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (576, 'be_in_hosp', 'R04', '限频次', 'R0400034', '入院前已进行”丙型肝炎、乙型肝炎测定“等，输血前再次进行“乙肝表面抗原、乙肝表面抗体、乙肝核心抗原、乙肝核心抗体、乙肝e抗体、丙型肝炎病毒抗体、梅毒抗体、艾滋病抗体检查', 'R040003402', '', 'OverclockTime', 'hosp', 'D04T00085', '2025', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (577, 'be_in_hosp', 'R04', '限频次', 'R0400034', '入院前已进行”丙型肝炎、乙型肝炎测定“等，输血前再次进行“乙肝表面抗原、乙肝表面抗体、乙肝核心抗原、乙肝核心抗体、乙肝e抗体、丙型肝炎病毒抗体、梅毒抗体、艾滋病抗体检查', 'R040003403', '', 'OverclockTime', 'hosp', 'D04T00084', '2025', '2025-03-09 17:24:55', NULL, '1', 'med_list_codg|count_size');
INSERT INTO `hcm_rule_cfg` VALUES (578, 'be_in_hosp', 'R05', '重复收费', 'R0500001', '根据《四川省医疗服务价格项目》，“经皮冠状动脉内溶栓术”项目含冠脉造影', 'R050000101', '', 'DoubleExistOperator', 'day', 'D05T00001', '2025', '2025-03-09 17:26:12', NULL, '1', 'med_list_codg');
INSERT INTO `hcm_rule_cfg` VALUES (579, 'be_in_hosp', 'R05', '重复收费', 'R0500002', '根据《四川省医疗服务价格项目》，“冠脉内局部放射治疗术”项目含冠脉造影、同位素放射源 及放疗装置的使用', 'R050000201', '', 'DoubleExistOperator', 'day', 'D05T00001', '2025', '2025-03-09 17:26:12', NULL, '1', 'med_list_codg');
INSERT INTO `hcm_rule_cfg` VALUES (580, 'be_in_hosp', 'R05', '重复收费', 'R0500003', '根据《四川省医疗服务价格项目》，“冠脉内局部药物释放治疗术”项目含冠脉造影', 'R050000301', '', 'DoubleExistOperator', 'day', 'D05T00001', '2025', '2025-03-09 17:26:12', NULL, '1', 'med_list_codg');
INSERT INTO `hcm_rule_cfg` VALUES (581, 'be_in_hosp', 'R05', '重复收费', 'R0500004', '根据《四川省医疗服务价格项目》，“经皮激光心肌血管重建术（PMR）”项目含冠脉造影', 'R050000401', '', 'DoubleExistOperator', 'day', 'D05T00001', '2025', '2025-03-09 17:26:12', NULL, '1', 'med_list_codg');
INSERT INTO `hcm_rule_cfg` VALUES (582, 'be_in_hosp', 'R05', '重复收费', 'R0500005', '根据《四川省医疗服务价格项目》，“冠状动脉内超声溶栓术”项目含冠脉造影', 'R050000501', '', 'DoubleExistOperator', 'day', 'D05T00001', '2025', '2025-03-09 17:26:12', NULL, '1', 'med_list_codg');
INSERT INTO `hcm_rule_cfg` VALUES (583, 'be_in_hosp', 'R05', '重复收费', 'R0500006', '根据《四川省医疗服务价格项目》，“冠脉血管内压力导丝测定术”项目含冠状动脉造影', 'R050000601', '', 'DoubleExistOperator', 'day', 'D05T00001', '2025', '2025-03-09 17:26:12', NULL, '1', 'med_list_codg');
INSERT INTO `hcm_rule_cfg` VALUES (584, 'be_in_hosp', 'R05', '重复收费', 'R0500007', '根据《四川省医疗服务价格项目》，经皮冠状动脉内支架置入术（STENT）含为放置冠脉内支架而进行的球囊预扩张和支架打开后的支架内球囊高压扩张及术前的靶血管造影', 'R050000701', '', 'DoubleExistOperator', 'day', 'D05T00002', '2025', '2025-03-09 17:26:12', NULL, '1', 'med_list_codg');
INSERT INTO `hcm_rule_cfg` VALUES (585, 'be_in_hosp', 'R05', '重复收费', 'R0500008', '根据《四川省医疗服务价格项目》，高速冠状动脉内膜旋磨术含旋磨后球囊扩张和/或支架置入及术前的靶血管造', 'R050000801', '', 'DoubleExistOperator', 'day', 'D05T00002', '2025', '2025-03-09 17:26:12', NULL, '1', 'med_list_codg');
INSERT INTO `hcm_rule_cfg` VALUES (586, 'be_in_hosp', 'R05', '重复收费', 'R0500009', '根据《四川省医疗服务价格项目》，经皮冠状动脉腔内激光成形术（ELCA）含激光消融后球囊扩张和/或支架置入及术前的靶血管造影', 'R050000901', '', 'DoubleExistOperator', 'day', 'D05T00002', '2025', '2025-03-09 17:26:12', NULL, '1', 'med_list_codg');
INSERT INTO `hcm_rule_cfg` VALUES (587, 'be_in_hosp', 'R05', '重复收费', 'R0500009', '根据《四川省医疗服务价格项目》，经皮冠状动脉腔内激光成形术（ELCA）含激光消融后球囊扩张和/或支架置入及术前的靶血管造影', 'R050000902', '', 'DoubleExistOperator', 'day', 'D05T00003', '2025', '2025-03-09 17:26:12', NULL, '1', 'med_list_codg');
INSERT INTO `hcm_rule_cfg` VALUES (588, 'be_in_hosp', 'R05', '重复收费', 'R0500010', '根据《四川省医疗服务价格项目》，高速冠状动脉内膜旋磨术含旋磨后球囊扩张和/或支架置入及术前的靶血管造影。', 'R050001001', '', 'DoubleExistOperator', 'day', 'D05T00003', '2025', '2025-03-09 17:26:12', NULL, '1', 'med_list_codg');
INSERT INTO `hcm_rule_cfg` VALUES (589, 'be_in_hosp', 'R05', '重复收费', 'R0500011', '根据《四川省医疗服务项目价格》：经血管介入治疗分类含局部麻醉和数字减影（DSA机）引导使用费', 'R050001101', '', 'DoubleExistOperator', 'day', 'D05T00005', '2025', '2025-03-09 17:26:12', NULL, '1', 'med_list_codg');
INSERT INTO `hcm_rule_cfg` VALUES (590, 'be_in_hosp', 'R05', '重复收费', 'R0500011', '根据《四川省医疗服务项目价格》：经血管介入治疗分类含局部麻醉和数字减影（DSA机）引导使用费', 'R050001102', '', 'DoubleExistOperator', 'day', 'D05T00004', '2025', '2025-03-09 17:26:12', NULL, '1', 'med_list_codg');
INSERT INTO `hcm_rule_cfg` VALUES (591, 'be_in_hosp', 'R05', '重复收费', 'R0500012', '根据《四川省医疗服务价格项目》：冠脉内局部放射治疗术含冠脉造影、同位素放射源及放疗装置的使用', 'R050001201', '', 'DoubleExistOperator', 'day', 'D05T00006', '2025', '2025-03-09 17:26:12', NULL, '1', 'med_list_codg');
INSERT INTO `hcm_rule_cfg` VALUES (592, 'be_in_hosp', 'R05', '重复收费', 'R0500013', '根据《全国医疗服务项目技术规范》（2023年版）：收取遥测心电监护费用同时收取心电监测费用', 'R050001301', '', 'DoubleExistOperator', 'hosp', 'D05T00007', '2025', '2025-03-09 17:26:12', NULL, '1', 'med_list_codg');
INSERT INTO `hcm_rule_cfg` VALUES (593, 'be_in_hosp', 'R05', '重复收费', 'R0500014', '根据《四川省医疗服务价格项目》：收取脊柱椎间融合器植入植骨融合术费用同时收取脊髓和神经根粘连松解术费用', 'R050001401', '', 'DoubleExistOperator', 'day', 'D05T00008', '2025', '2025-03-09 17:26:12', NULL, '1', 'med_list_codg');
INSERT INTO `hcm_rule_cfg` VALUES (594, 'be_in_hosp', 'R05', '重复收费', 'R0500015', '根据《全国医疗服务项目技术规范》（2023年版）：收取后路腰椎间盘镜椎间盘髓核摘除术（MED）费用同时收取椎间盘微创消融术（椎间盘摘除术）费用', 'R050001501', '', 'DoubleExistOperator', 'day', 'D05T00009', '2025', '2025-03-09 17:26:12', NULL, '1', 'med_list_codg');
INSERT INTO `hcm_rule_cfg` VALUES (595, 'be_in_hosp', 'R05', '重复收费', 'R0500016', '根据《全国医疗服务项目技术规范》（2023年版）：收取后路腰椎间盘镜椎间盘髓核摘除术（MED）费用同时收取椎管扩大减压术费用', 'R050001601', '', 'DoubleExistOperator', 'day', 'D05T00009', '2025', '2025-03-09 17:26:12', NULL, '1', 'med_list_codg');
INSERT INTO `hcm_rule_cfg` VALUES (596, 'be_in_hosp', 'R05', '重复收费', 'R0500017', '根据《全国医疗服务项目技术规范》（2023年版）：收取关节腔灌注治疗费用同时收取关节穿刺术费用', 'R050001701', '', 'DoubleExistOperator', 'day', 'D05T00010', '2025', '2025-03-09 17:26:12', NULL, '1', 'med_list_codg');
INSERT INTO `hcm_rule_cfg` VALUES (597, 'be_in_hosp', 'R05', '重复收费', 'R0500018', '收取连续性血液净化费用同时收取血液透析费用', 'R050001801', '', 'DoubleExistOperator', 'day', 'D05T00011', '2025', '2025-03-09 17:26:12', NULL, '1', 'med_list_codg');
INSERT INTO `hcm_rule_cfg` VALUES (598, 'be_in_hosp', 'R05', '重复收费', 'R0500019', '收取连续性血液净化费用同时收取血液透析（碳酸液透析）费用', 'R050001901', '', 'DoubleExistOperator', 'day', 'D05T00011', '2025', '2025-03-09 17:26:12', NULL, '1', 'med_list_codg');
INSERT INTO `hcm_rule_cfg` VALUES (599, 'be_in_hosp', 'R05', '重复收费', 'R0500020', '收取连续性血液净化费用同时收取血液透析（醋酸液透析）费用', 'R050002001', '', 'DoubleExistOperator', 'day', 'D05T00011', '2025', '2025-03-09 17:26:12', NULL, '1', 'med_list_codg');
INSERT INTO `hcm_rule_cfg` VALUES (600, 'be_in_hosp', 'R05', '重复收费', 'R0500021', '收取连续性血液净化费用同时收取血液滤过费用', 'R050002101', '', 'DoubleExistOperator', 'day', 'D05T00011', '2025', '2025-03-09 17:26:12', NULL, '1', 'med_list_codg');
INSERT INTO `hcm_rule_cfg` VALUES (601, 'be_in_hosp', 'R05', '重复收费', 'R0500022', '收取连续性血液净化费用同时收取血液透析滤过费用', 'R050002201', '', 'DoubleExistOperator', 'day', 'D05T00011', '2025', '2025-03-09 17:26:12', NULL, '1', 'med_list_codg');
INSERT INTO `hcm_rule_cfg` VALUES (602, 'be_in_hosp', 'R05', '重复收费', 'R0500023', '同时开展脑瘫肢体综合训练和运动疗法并纳入医保支付', 'R050002301', '', 'DoubleExistOperator', 'day', 'D05T00012', '2025', '2025-03-09 17:26:12', NULL, '1', 'med_list_codg');
INSERT INTO `hcm_rule_cfg` VALUES (603, 'be_in_hosp', 'R05', '重复收费', 'R0500024', '同时开展截瘫肢体综合训练和运动疗法并纳入医保支付', 'R050002401', '', 'DoubleExistOperator', 'day', 'D05T00012', '2025', '2025-03-09 17:26:12', NULL, '1', 'med_list_codg');
INSERT INTO `hcm_rule_cfg` VALUES (604, 'be_in_hosp', 'R05', '重复收费', 'R0500025', '同时开展偏瘫肢体综合训练和运动疗法并纳入医保支付', 'R050002501', '', 'DoubleExistOperator', 'day', 'D05T00012', '2025', '2025-03-09 17:26:12', NULL, '1', 'med_list_codg');
INSERT INTO `hcm_rule_cfg` VALUES (605, 'be_in_hosp', 'R05', '重复收费', 'R0500026', '收取数字化摄影（DR）费用同时收取普通透视费用', 'R050002601', '', 'DoubleExistOperator', 'day', 'D05T00013', '2025', '2025-03-09 17:26:12', NULL, '1', 'med_list_codg');
INSERT INTO `hcm_rule_cfg` VALUES (606, 'be_in_hosp', 'R05', '重复收费', 'R0500027', '收取数字化摄影（DR）费用同时收取普通透视(胸)费用', 'R050002701', '', 'DoubleExistOperator', 'day', 'D05T00013', '2025', '2025-03-09 17:26:12', NULL, '1', 'med_list_codg');
INSERT INTO `hcm_rule_cfg` VALUES (607, 'be_in_hosp', 'R05', '重复收费', 'R0500028', '收取数字化摄影（DR）费用同时收取普通透视(腹)费用', 'R050002801', '', 'DoubleExistOperator', 'day', 'D05T00013', '2025', '2025-03-09 17:26:12', NULL, '1', 'med_list_codg');
INSERT INTO `hcm_rule_cfg` VALUES (608, 'be_in_hosp', 'R05', '重复收费', 'R0500029', '收取数字化摄影（DR）费用同时收取普通透视（盆腔）费用', 'R050002901', '', 'DoubleExistOperator', 'day', 'D05T00013', '2025', '2025-03-09 17:26:12', NULL, '1', 'med_list_codg');
INSERT INTO `hcm_rule_cfg` VALUES (609, 'be_in_hosp', 'R05', '重复收费', 'R0500030', '收取数字化摄影（DR）费用同时收取普通透视(四肢)费用', 'R050003001', '', 'DoubleExistOperator', 'day', 'D05T00013', '2025', '2025-03-09 17:26:12', NULL, '1', 'med_list_codg');
INSERT INTO `hcm_rule_cfg` VALUES (610, 'be_in_hosp', 'R05', '重复收费', 'R0500031', '收取经皮超选择性动脉造影术费用同时收取经皮选择性动脉造影术费用', 'R050003101', '', 'DoubleExistOperator', 'day', 'D05T00014', '2025', '2025-03-09 17:26:12', NULL, '1', 'med_list_codg');
INSERT INTO `hcm_rule_cfg` VALUES (611, 'be_in_hosp', 'R05', '重复收费', 'R0500032', '收取上消化道造影费用同时收取食管造影费用', 'R050003201', '', 'DoubleExistOperator', 'day', 'D05T00015', '2025', '2025-03-09 17:26:12', NULL, '1', 'med_list_codg');
INSERT INTO `hcm_rule_cfg` VALUES (612, 'be_in_hosp', 'R05', '重复收费', 'R0500033', '收取心脏彩色多普勒超声费用同时收取普通心脏M型超声检查费用', 'R050003301', '', 'DoubleExistOperator', 'day', 'D05T00016', '2025', '2025-03-09 17:26:12', NULL, '1', 'med_list_codg');
INSERT INTO `hcm_rule_cfg` VALUES (613, 'be_in_hosp', 'R05', '重复收费', 'R0500034', '收取心脏彩色多普勒超声费用同时收取普通二维超声心动图费用', 'R050003401', '', 'DoubleExistOperator', 'day', 'D05T00016', '2025', '2025-03-09 17:26:12', NULL, '1', 'med_list_codg');
INSERT INTO `hcm_rule_cfg` VALUES (614, 'be_in_hosp', 'R05', '重复收费', 'R0500035', '收取床旁透视与术中透视费用同时收取普通透视费用', 'R050003501', '', 'DoubleExistOperator', 'day', 'D05T00017', '2025', '2025-03-09 17:26:12', NULL, '1', 'med_list_codg');
INSERT INTO `hcm_rule_cfg` VALUES (615, 'be_in_hosp', 'R05', '重复收费', 'R0500036', '收取床旁透视与术中透视费用同时收取普通透视(胸)费用', 'R050003601', '', 'DoubleExistOperator', 'day', 'D05T00017', '2025', '2025-03-09 17:26:12', NULL, '1', 'med_list_codg');
INSERT INTO `hcm_rule_cfg` VALUES (616, 'be_in_hosp', 'R05', '重复收费', 'R0500037', '收取床旁透视与术中透视费用同时收取普通透视(腹)费用', 'R050003701', '', 'DoubleExistOperator', 'day', 'D05T00017', '2025', '2025-03-09 17:26:12', NULL, '1', 'med_list_codg');
INSERT INTO `hcm_rule_cfg` VALUES (617, 'be_in_hosp', 'R05', '重复收费', 'R0500038', '收取床旁透视与术中透视费用同时收取普通透视（盆腔）费用', 'R050003801', '', 'DoubleExistOperator', 'day', 'D05T00017', '2025', '2025-03-09 17:26:12', NULL, '1', 'med_list_codg');
INSERT INTO `hcm_rule_cfg` VALUES (618, 'be_in_hosp', 'R05', '重复收费', 'R0500039', '收取床旁透视与术中透视费用同时收取普通透视(四肢)费用', 'R050003901', '', 'DoubleExistOperator', 'day', 'D05T00017', '2025', '2025-03-09 17:26:12', NULL, '1', 'med_list_codg');
INSERT INTO `hcm_rule_cfg` VALUES (619, 'be_in_hosp', 'R05', '重复收费', 'R0500040', '收取床旁B超检查费用同时收取B超常规检查费用', 'R050004001', '', 'DoubleExistOperator', 'day', 'D05T00018', '2025', '2025-03-09 17:26:12', NULL, '1', 'med_list_codg');
INSERT INTO `hcm_rule_cfg` VALUES (620, 'be_in_hosp', 'R05', '重复收费', 'R0500041', '收取床旁B超检查费用同时收取B超常规检查（胸部）费用', 'R050004101', '', 'DoubleExistOperator', 'day', 'D05T00018', '2025', '2025-03-09 17:26:12', NULL, '1', 'med_list_codg');
INSERT INTO `hcm_rule_cfg` VALUES (621, 'be_in_hosp', 'R05', '重复收费', 'R0500042', '收取床旁B超检查费用同时收取B超常规检查（腹部）费用', 'R050004201', '', 'DoubleExistOperator', 'day', 'D05T00018', '2025', '2025-03-09 17:26:12', NULL, '1', 'med_list_codg');
INSERT INTO `hcm_rule_cfg` VALUES (622, 'be_in_hosp', 'R05', '重复收费', 'R0500043', '收取床旁B超检查费用同时收取B超常规检查（胃肠道）费用', 'R050004301', '', 'DoubleExistOperator', 'day', 'D05T00018', '2025', '2025-03-09 17:26:12', NULL, '1', 'med_list_codg');
INSERT INTO `hcm_rule_cfg` VALUES (623, 'be_in_hosp', 'R05', '重复收费', 'R0500044', '收取床旁B超检查费用同时收取B超常规检查（泌尿系统）费用', 'R050004401', '', 'DoubleExistOperator', 'day', 'D05T00018', '2025', '2025-03-09 17:26:12', NULL, '1', 'med_list_codg');
INSERT INTO `hcm_rule_cfg` VALUES (624, 'be_in_hosp', 'R05', '重复收费', 'R0500045', '收取床旁B超检查费用同时收取B超常规检查（妇科）费用', 'R050004501', '', 'DoubleExistOperator', 'day', 'D05T00018', '2025', '2025-03-09 17:26:12', NULL, '1', 'med_list_codg');
INSERT INTO `hcm_rule_cfg` VALUES (625, 'be_in_hosp', 'R05', '重复收费', 'R0500046', '收取床旁B超检查费用同时收取B超常规检查（产科）费用', 'R050004601', '', 'DoubleExistOperator', 'day', 'D05T00018', '2025', '2025-03-09 17:26:12', NULL, '1', 'med_list_codg');
INSERT INTO `hcm_rule_cfg` VALUES (626, 'be_in_hosp', 'R05', '重复收费', 'R0500047', '收取床旁超声心动图费用同时收取普通二维超声心动图费用', 'R050004701', '', 'DoubleExistOperator', 'day', 'D05T00019', '2025', '2025-03-09 17:26:12', NULL, '1', 'med_list_codg');
INSERT INTO `hcm_rule_cfg` VALUES (627, 'be_in_hosp', 'R05', '重复收费', 'R0500048', '收取临床操作的CT引导费用同时收取X线计算机体层（CT）平扫费用', 'R050004801', '', 'DoubleExistOperator', 'hosp', 'D05T00020', '2025', '2025-03-09 17:26:12', NULL, '1', 'med_list_codg');
INSERT INTO `hcm_rule_cfg` VALUES (628, 'be_in_hosp', 'R05', '重复收费', 'R0500049', '收取临床操作的CT引导费用同时收取普通CT平扫费用', 'R050004901', '', 'DoubleExistOperator', 'hosp', 'D05T00020', '2025', '2025-03-09 17:26:12', NULL, '1', 'med_list_codg');
INSERT INTO `hcm_rule_cfg` VALUES (629, 'be_in_hosp', 'R05', '重复收费', 'R0500050', '收取临床操作的CT引导费用同时收取螺旋CT平扫费用', 'R050005001', '', 'DoubleExistOperator', 'hosp', 'D05T00020', '2025', '2025-03-09 17:26:12', NULL, '1', 'med_list_codg');
INSERT INTO `hcm_rule_cfg` VALUES (630, 'be_in_hosp', 'R05', '重复收费', 'R0500051', '收取临床操作的CT引导费用同时收取单次多层CT平扫费用', 'R050005101', '', 'DoubleExistOperator', 'hosp', 'D05T00020', '2025', '2025-03-09 17:26:12', NULL, '1', 'med_list_codg');
INSERT INTO `hcm_rule_cfg` VALUES (631, 'be_in_hosp', 'R05', '重复收费', 'R0500052', '收取临床操作的磁共振引导费用同时收取磁共振平扫费用', 'R050005201', '', 'DoubleExistOperator', 'hosp', 'D05T00021', '2025', '2025-03-09 17:26:12', NULL, '1', 'med_list_codg');
INSERT INTO `hcm_rule_cfg` VALUES (632, 'be_in_hosp', 'R05', '重复收费', 'R0500053', '收取临床操作的磁共振引导费用同时收取磁共振平扫（0.5T以下，不含0.5T）费用', 'R050005301', '', 'DoubleExistOperator', 'hosp', 'D05T00021', '2025', '2025-03-09 17:26:12', NULL, '1', 'med_list_codg');
INSERT INTO `hcm_rule_cfg` VALUES (633, 'be_in_hosp', 'R05', '重复收费', 'R0500054', '收取临床操作的磁共振引导费用同时收取磁共振平扫（0.5T—1T）费用', 'R050005401', '', 'DoubleExistOperator', 'hosp', 'D05T00021', '2025', '2025-03-09 17:26:12', NULL, '1', 'med_list_codg');
INSERT INTO `hcm_rule_cfg` VALUES (634, 'be_in_hosp', 'R05', '重复收费', 'R0500055', '收取临床操作的磁共振引导费用同时收取磁共振平扫（1T以上，不含1T）费用', 'R050005501', '', 'DoubleExistOperator', 'hosp', 'D05T00021', '2025', '2025-03-09 17:26:12', NULL, '1', 'med_list_codg');
INSERT INTO `hcm_rule_cfg` VALUES (635, 'be_in_hosp', 'R05', '重复收费', 'R0500056', '经胆道镜手术和胆道镜检查。', 'R050005601', '', 'DoubleExistOperator', 'day', 'D05T00030', '2025', '2025-03-09 17:26:12', NULL, '1', 'med_list_codg');
INSERT INTO `hcm_rule_cfg` VALUES (636, 'be_in_hosp', 'R05', '重复收费', 'R0500057', '经膀胱镜尿道镜手术和膀胱镜尿道镜检查。', 'R050005701', '', 'DoubleExistOperator', 'day', 'D05T00031', '2025', '2025-03-09 17:26:12', NULL, '1', 'med_list_codg');
INSERT INTO `hcm_rule_cfg` VALUES (637, 'be_in_hosp', 'R05', '重复收费', 'R0500058', '经输尿管镜手术和经尿道输尿管镜检查。', 'R050005801', '', 'DoubleExistOperator', 'day', 'D05T00032', '2025', '2025-03-09 17:26:12', NULL, '1', 'med_list_codg');
INSERT INTO `hcm_rule_cfg` VALUES (638, 'be_in_hosp', 'R05', '重复收费', 'R0500059', '经宫腔镜类手术和经宫腔镜检查。', 'R050005901', '', 'DoubleExistOperator', 'day', 'D05T00033', '2025', '2025-03-09 17:26:12', NULL, '1', 'med_list_codg');
INSERT INTO `hcm_rule_cfg` VALUES (639, 'be_in_hosp', 'R05', '重复收费', 'R0500060', '经腹腔镜类手术和腹腔镜检查。', 'R050006001', '', 'DoubleExistOperator', 'day', 'D05T00034', '2025', '2025-03-09 17:26:12', NULL, '1', 'med_list_codg');

SET FOREIGN_KEY_CHECKS = 1;
