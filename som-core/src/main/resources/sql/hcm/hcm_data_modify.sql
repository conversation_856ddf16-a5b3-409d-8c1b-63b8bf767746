##### 1、新增规则配置表结构

-- 1、新增自查自纠规则配置表
CREATE TABLE `hcm_rule_cfg` (
  `rule_id` bigint NOT NULL AUTO_INCREMENT COMMENT '规则id',
  `rule_scen_type` varchar(50) DEFAULT NULL COMMENT '规则场景编码(住院、门诊)',
  `rule_type_codg` varchar(50) NOT NULL COMMENT '规则类型编码',
  `rule_type_name` varchar(200) DEFAULT NULL COMMENT '规则类型名称',
  `rule_grp_codg` varchar(50) NOT NULL COMMENT '规则分组编码',
  `rule_grp_name` varchar(200) DEFAULT NULL COMMENT '规则分组名称',
  `rule_detl_codg` varchar(50) NOT NULL COMMENT '规则明细编码',
  `rule_detl_name` varchar(200) DEFAULT NULL COMMENT '规则明细名称',
  `opra_type` varchar(100) NOT NULL COMMENT '算子类型',
  `data_grp` varchar(50) NOT NULL COMMENT '数据分组',
  `rule_data_meta` varchar(50) NOT NULL COMMENT '规则数据元',
  `rule_year` varchar(4) DEFAULT NULL COMMENT '规则年度',
  `add_time` datetime NOT NULL COMMENT '新增时间',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `vali_flag` varchar(6) NOT NULL COMMENT '有效标志',
  PRIMARY KEY (`rule_id`)
) ENGINE=InnoDB AUTO_INCREMENT=224 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='自查自纠规则配置表';

alter table hcm_rule_cfg add column target_field varchar(200) DEFAULT NULL COMMENT '校验目标字段(多个|线分隔)';
alter table hcm_rule_cfg add column vola_deg varchar(6) DEFAULT NULL COMMENT '违规程度(1明确违规,2高度可疑)';
alter table hcm_rule_cfg add column exct_cont varchar(500) DEFAULT NULL COMMENT '除外内容';
alter table hcm_rule_cfg add column rule_souc varchar(500) DEFAULT NULL COMMENT '规则来源';
alter table hcm_rule_cfg add column memo varchar(500) DEFAULT NULL COMMENT '备注';
alter table hcm_rule_cfg add column efft_date varchar(500) DEFAULT NULL COMMENT '生效日期';
alter table hcm_rule_cfg add column invd_date varchar(500) DEFAULT NULL COMMENT '失效日期';
alter table hcm_rule_cfg add column exct_type varchar(6) DEFAULT NULL COMMENT '除外类型(1诊断2项目)';


-- 2、自查自纠数据组配置表
CREATE TABLE hcm_data_grp_cfg (
    data_grp_id tinyint NOT NULL AUTO_INCREMENT COMMENT '数据分组id',
    data_grp_code varchar(50) NOT NULL COMMENT '数据分组编码',
    data_detail_code varchar(50) NOT NULL COMMENT '数据分组明细编码',
    data_code varchar(200) NOT NULL COMMENT '数据编码',
    data_name varchar(50) DEFAULT NULL COMMENT '数据名称',
    add_time datetime NOT NULL COMMENT '新增时间',
    update_time datetime DEFAULT NULL COMMENT '修改时间',
    vali_flag varchar(6) NOT NULL COMMENT '有效标志',
    PRIMARY KEY (`data_grp_id`)
) COMMENT='自查自纠数据组配置表';



##### 2、获取待校验数据

-- 1、查询待校验数据
SELECT
  a.k00 AS uniqueId,
  substr( b.med_list_codg, 1, 15 ) AS medListCodg,
  feeOcurTime,
  sum( cnt ) AS countSize 
FROM
  som_hi_invy_bas_info a
  LEFT JOIN ( SELECT unique_id, med_list_codg, cnt, DATE_FORMAT( fee_ocur_time, '%Y-%m-%d' ) AS feeOcurTime FROM som_chrg_detl_intf WHERE instr( med_list_codg, '00' )= 1 ) b ON a.k00 = b.unique_id 
GROUP BY
  a.k00,
  b.med_list_codg,
  b.feeOcurTime



##### 4、更新校验目标字段

update hcm_rule_cfg set target_field = 'med_list_codg' where opra_type  in ('DoubleExistOperator','ExistOperator');
update hcm_rule_cfg set target_field = 'med_list_codg|count_size' where opra_type = 'OverclockTime';



##### 5、新增校验结果表字段

-- 住院自查自纠明细表
DROP TABLE IF EXISTS `hcm_valid_result_inhosp`;
CREATE TABLE `hcm_valid_result_inhosp` (
  `rule_valid_result_id` bigint NOT NULL AUTO_INCREMENT COMMENT '校验结果id',
  `unique_id` varchar(50) NOT NULL COMMENT '患者唯一ID',
  `rule_scen_type` varchar(50) DEFAULT NULL COMMENT '场景类型(门诊outpat、住院inhosp)',
  `rule_detl_codg` varchar(50) NOT NULL COMMENT '规则明细编码',
  `rule_data_meta` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '规则元组编码',
  `error_type` varchar(50) DEFAULT NULL COMMENT '错误类型',
  `error_desc` varchar(200) DEFAULT NULL COMMENT '错误备注',
  `error_detail_codg` varchar(50) NOT NULL COMMENT '校验错误编码',
  `med_list_codg` varchar(50) NOT NULL COMMENT '项目编码',
  `med_list_name` varchar(200) DEFAULT NULL COMMENT '项目名称',
  `violation_amount` decimal(10,2) NOT NULL COMMENT '项目金额',
  `cnt` decimal(10,2) DEFAULT NULL COMMENT '数量',
  `pric` decimal(10,2) DEFAULT NULL COMMENT '单价',
  `oprn_date` datetime NOT NULL COMMENT '新增时间',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `vali_flag` varchar(6) NOT NULL COMMENT '有效标志',
  PRIMARY KEY (`rule_valid_result_id`)
)  COMMENT='住院自查自纠明细表';

-- 门诊自查自纠明细表
DROP TABLE IF EXISTS `hcm_valid_result_outpat`;
CREATE TABLE `hcm_valid_result_outpat` (
  `rule_valid_result_id` bigint NOT NULL AUTO_INCREMENT COMMENT '校验结果id',
  `unique_id` varchar(50) NOT NULL COMMENT '患者唯一ID',
  `rule_scen_type` varchar(50) DEFAULT NULL COMMENT '场景类型(门诊outpat、住院inhosp)',
  `rule_detl_codg` varchar(50) NOT NULL COMMENT '规则明细编码',
  `rule_data_meta` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '规则元组编码',
  `error_type` varchar(50) DEFAULT NULL COMMENT '错误类型',
  `error_desc` varchar(200) DEFAULT NULL COMMENT '错误备注',
  `error_detail_codg` varchar(50) NOT NULL COMMENT '校验错误编码',
  `med_list_codg` varchar(50) NOT NULL COMMENT '项目编码',
  `med_list_name` varchar(200) DEFAULT NULL COMMENT '项目名称',
  `violation_amount` decimal(10,2) NOT NULL COMMENT '项目金额',
  `cnt` decimal(10,2) DEFAULT NULL COMMENT '数量',
  `pric` decimal(10,2) DEFAULT NULL COMMENT '单价',
  `oprn_date` datetime NOT NULL COMMENT '新增时间',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `vali_flag` varchar(6) NOT NULL COMMENT '有效标志',
  PRIMARY KEY (`rule_valid_result_id`)
)  COMMENT='门诊自查自纠明细表';

alter table hcm_valid_result_inhosp add column vola_deg varchar(6) DEFAULT NULL COMMENT '违规程度(1明确违规,2高度可疑)';
alter table hcm_valid_result_outpat add column  vola_deg varchar(6) DEFAULT NULL COMMENT '违规程度(1明确违规,2高度可疑)';


6、菜单新增
INSERT INTO `dcdipv5`.`som_menu_mgt` ( `name`, `prnt_menu_id_lv1_menu`, `url`, `auth`, `type`, `icon`, `disp_seq`, `crter`, `crte_time`, `updt_psn`, `updt_time`, `is_del`, `is_hide`, `is_cahe`, `is_double_cahe`, `is_admin_menu`) VALUES ( '自查自纠', 0, '', '', 0, 'el-icon-data-line', 12, NULL, NULL, NULL, NULL, 0, '0', '', '0', '0');
INSERT INTO `dcdipv5`.`som_menu_mgt` ( `name`, `prnt_menu_id_lv1_menu`, `url`, `auth`, `type`, `icon`, `disp_seq`, `crter`, `crte_time`, `updt_psn`, `updt_time`, `is_del`, `is_hide`, `is_cahe`, `is_double_cahe`, `is_admin_menu`) VALUES ( '月度违规汇总', 376, '/examAndCorrection/monthlyRollups', '', 1, 'el-icon-upload', 0, NULL, NULL, NULL, NULL, 0, '0', '1', '0', '0');
INSERT INTO `dcdipv5`.`som_menu_mgt` ( `name`, `prnt_menu_id_lv1_menu`, `url`, `auth`, `type`, `icon`, `disp_seq`, `crter`, `crte_time`, `updt_psn`, `updt_time`, `is_del`, `is_hide`, `is_cahe`, `is_double_cahe`, `is_admin_menu`) VALUES ( '科室违规汇总', 376, '/examAndCorrection/deptVioalSummary', '', 1, 'el-icon-upload', 0, NULL, NULL, NULL, NULL, 0, '0', '1', '0', '0');
INSERT INTO `dcdipv5`.`som_menu_mgt` ( `name`, `prnt_menu_id_lv1_menu`, `url`, `auth`, `type`, `icon`, `disp_seq`, `crter`, `crte_time`, `updt_psn`, `updt_time`, `is_del`, `is_hide`, `is_cahe`, `is_double_cahe`, `is_admin_menu`) VALUES ( '医生违规汇总', 376, '/examAndCorrection/doctorVioalSummary', '', 1, 'el-icon-upload', 0, NULL, NULL, NULL, NULL, 0, '0', '1', '0', '0');
INSERT INTO `dcdipv5`.`som_menu_mgt` ( `name`, `prnt_menu_id_lv1_menu`, `url`, `auth`, `type`, `icon`, `disp_seq`, `crter`, `crte_time`, `updt_psn`, `updt_time`, `is_del`, `is_hide`, `is_cahe`, `is_double_cahe`, `is_admin_menu`) VALUES ( '违规规则汇总', 376, '/examAndCorrection/violationRules', '', 1, 'el-icon-upload', 0, NULL, NULL, NULL, NULL, 0, '0', '1', '0', '0');
INSERT INTO `dcdipv5`.`som_menu_mgt` ( `name`, `prnt_menu_id_lv1_menu`, `url`, `auth`, `type`, `icon`, `disp_seq`, `crter`, `crte_time`, `updt_psn`, `updt_time`, `is_del`, `is_hide`, `is_cahe`, `is_double_cahe`, `is_admin_menu`) VALUES ( '违规患者明细', 376, '/examAndCorrection/pationtsCompliant', '', 1, 'el-icon-upload', 0, NULL, NULL, NULL, NULL, 0, '0', '1', '0', '0');
INSERT INTO `dcdipv5`.`som_menu_mgt` ( `name`, `prnt_menu_id_lv1_menu`, `url`, `auth`, `type`, `icon`, `disp_seq`, `crter`, `crte_time`, `updt_psn`, `updt_time`, `is_del`, `is_hide`, `is_cahe`, `is_double_cahe`, `is_admin_menu`) VALUES ( '质控规则查询', 376, '/examAndCorrection/rule', '', 1, 'el-icon-upload', 0, NULL, NULL, NULL, NULL, 0, '0', '1', '0', '0');
INSERT INTO `dcdipv5`.`som_menu_mgt` ( `name`, `prnt_menu_id_lv1_menu`, `url`, `auth`, `type`, `icon`, `disp_seq`, `crter`, `crte_time`, `updt_psn`, `updt_time`, `is_del`, `is_hide`, `is_cahe`, `is_double_cahe`, `is_admin_menu`) VALUES ( '规则元组查询', 376, '/examAndCorrection/ruleTuples', '', 1, 'el-icon-upload', 0, NULL, NULL, NULL, NULL, 0, '0', '1', '0', '0');
INSERT INTO `dcdipv5`.`som_menu_mgt` ( `name`, `prnt_menu_id_lv1_menu`, `url`, `auth`, `type`, `icon`, `disp_seq`, `crter`, `crte_time`, `updt_psn`, `updt_time`, `is_del`, `is_hide`, `is_cahe`, `is_double_cahe`, `is_admin_menu`) VALUES ( '违规明细下钻', 376, '/examAndCorrection/docerVialDetial', '', 1, '', 31, NULL, NULL, NULL, NULL, 0, '1', '1', '1', '0');
INSERT INTO `dcdipv5`.`som_menu_mgt` ( `name`, `prnt_menu_id_lv1_menu`, `url`, `auth`, `type`, `icon`, `disp_seq`, `crter`, `crte_time`, `updt_psn`, `updt_time`, `is_del`, `is_hide`, `is_cahe`, `is_double_cahe`, `is_admin_menu`) VALUES ( '患者违规信息明细', 376, '/examAndCorrection/pationtsDetial', '', 1, '', 0, NULL, NULL, NULL, NULL, 0, '1', '1', '1', '0');
INSERT INTO `dcdipv5`.`som_menu_mgt` ( `name`, `prnt_menu_id_lv1_menu`, `url`, `auth`, `type`, `icon`, `disp_seq`, `crter`, `crte_time`, `updt_psn`, `updt_time`, `is_del`, `is_hide`, `is_cahe`, `is_double_cahe`, `is_admin_menu`) VALUES ( '质控规则新增', 376, '/examAndCorrection/generalRules', '', 1, 'el-icon-upload', 0, NULL, NULL, NULL, NULL, 0, '0', '1', '0', '0');

7、码表新增
INSERT INTO `som_sys_code` ( `data_val`, `labl_name`, `code_type`, `dscr`, `srt`, `crter`, `crte_time`, `updt_psn`, `updt_time`, `memo_info`, `is_del`) VALUES ( '1', '违规', 'VOLA_DEG_CODE', '违规程度', 0, NULL, NULL, NULL, NULL, '', 0);
INSERT INTO `som_sys_code` ( `data_val`, `labl_name`, `code_type`, `dscr`, `srt`, `crter`, `crte_time`, `updt_psn`, `updt_time`, `memo_info`, `is_del`) VALUES ( '2', '疑似', 'VOLA_DEG_CODE', '违规程度', 0, NULL, NULL, NULL, NULL, '', 0);
INSERT INTO `som_sys_code` ( `data_val`, `labl_name`, `code_type`, `dscr`, `srt`, `crter`, `crte_time`, `updt_psn`, `updt_time`, `memo_info`, `is_del`) VALUES ( '1', '除外诊断', 'EXCT_CONT_CODE', '除外类型', 0, NULL, NULL, NULL, NULL, '', 0);
INSERT INTO `som_sys_code` ( `data_val`, `labl_name`, `code_type`, `dscr`, `srt`, `crter`, `crte_time`, `updt_psn`, `updt_time`, `memo_info`, `is_del`) VALUES ( '2', '除外项目', 'EXCT_CONT_CODE', '除外类型', 0, NULL, NULL, NULL, NULL, '', 0);
INSERT INTO `som_sys_code` ( `data_val`, `labl_name`, `code_type`, `dscr`, `srt`, `crter`, `crte_time`, `updt_psn`, `updt_time`, `memo_info`, `is_del`) VALUES ( 'hosp', '一次住院', 'DATA_TYPE_CODE', '数据分组类型', 0, NULL, NULL, NULL, NULL, '', 0);
INSERT INTO `som_sys_code` ( `data_val`, `labl_name`, `code_type`, `dscr`, `srt`, `crter`, `crte_time`, `updt_psn`, `updt_time`, `memo_info`, `is_del`) VALUES ( 'day', '同日', 'DATA_TYPE_CODE', '数据分组类型', 0, NULL, NULL, NULL, NULL, '', 0);
INSERT INTO `som_sys_code` ( `data_val`, `labl_name`, `code_type`, `dscr`, `srt`, `crter`, `crte_time`, `updt_psn`, `updt_time`, `memo_info`, `is_del`) VALUES ( 'hour', '同时', 'DATA_TYPE_CODE', '数据分组类型', 0, NULL, NULL, NULL, NULL, '', 0);



8、费用明细表退费标志
alter table som_chrg_detl_intf add column refund_flag varchar(6) DEFAULT NULL COMMENT '退费标志(1退费,0正常)';

9、更新退费标志
-- 剔除退费记录
-- 01 更新原始数据退费标志
update som_chrg_detl_intf set refund_flag = '1' where id in(
select id from (
select id from som_chrg_detl_intf a inner join 
(select unique_id, init_feedetl_sn from som_chrg_detl_intf where det_item_fee_sumamt < 0) b on a.unique_id = b.unique_id and a.feedetl_sn = b.init_feedetl_sn
) a
)
-- 02 更新负数据退费标志
update som_chrg_detl_intf set refund_flag = '1' where det_item_fee_sumamt < 0



10、结果增加费用时间字段
alter table hcm_valid_result_inhosp add column fee_ocur_time datetime DEFAULT NULL COMMENT '费用发生时间';



11、项目违规对统计sql
select violation_code as violationCode,violation_name as violationName ,rule_grp_name AS ruleGrpName,count(DISTINCT unique_id) as medSize,sum(violationSize) as allViolationSize,sum(sumfee) as allSumfee,sum(case when vola_deg = '1' then sumfee else 0 end) as clearlySumfee, sum(case when vola_deg = '2' then sumfee else 0 end) as suspectSumfee from 
(SELECT  c.rule_grp_name,c.vola_deg, unique_id, error_detail_codg,count(1) as violationSize, GROUP_CONCAT(a.med_list_codg ORDER BY med_list_codg SEPARATOR '/') as violation_code,
GROUP_CONCAT(b.itemname ORDER BY b.itemname SEPARATOR '/') as violation_name,
sum(violation_amount) as sumfee
FROM hcm_valid_result_inhosp a left join  (select item_codg,itemname from som_med_serv_cfg group by item_codg,itemname  ) b on a.med_list_codg = b.item_codg
inner join hcm_rule_cfg c on a.rule_detl_codg = c.rule_detl_codg
GROUP BY c.rule_grp_name,c.vola_deg,unique_id, error_detail_codg
) a
group by rule_grp_name,violation_code,violation_name

12、违规项目明细统计sql
SELECT
  unique_id as uniqueId,
  d.a48 as medcasno,
  e.NAME as deptName,
  DATE_FORMAT( d.b15, '%Y-%m-%d' ) AS dscgDate ,
  error_detail_codg as errorDetailCodg,
  count( 1 ) AS violationSize,
  GROUP_CONCAT( a.med_list_codg ORDER BY med_list_codg SEPARATOR '/' ) AS violationCode,
  GROUP_CONCAT( b.itemname ORDER BY b.itemname SEPARATOR '/' ) AS violationName,
  c.rule_grp_name AS ruleGrpName,
  c.vola_deg AS volaDeg,
  sum( violation_amount ) AS allSumfee
FROM
  hcm_valid_result_inhosp a
  LEFT JOIN ( SELECT item_codg, itemname FROM som_med_serv_cfg GROUP BY item_codg, itemname ) b ON a.med_list_codg = b.item_codg
  INNER JOIN hcm_rule_cfg c ON a.rule_detl_codg = c.rule_detl_codg
  INNER JOIN som_hi_invy_bas_info d ON a.unique_id = d.k00
  LEFT JOIN som_dept e ON d.B16C = e.CODE 
GROUP BY
  c.rule_grp_name,
  c.vola_deg,
  unique_id,
  d.a48,
  d.b15,
  e.NAME,
  error_detail_codg

13、更新规则描述中的
update hcm_rule_cfg set rule_grp_name = REPLACE(rule_grp_name,"\n","") 

