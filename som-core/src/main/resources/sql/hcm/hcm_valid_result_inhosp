/*
 Navicat Premium Dump SQL

 Source Server         : 本地
 Source Server Type    : MySQL
 Source Server Version : 80039 (8.0.39)
 Source Host           : localhost:3306
 Source Schema         : cdym

 Target Server Type    : MySQL
 Target Server Version : 80039 (8.0.39)
 File Encoding         : 65001

 Date: 17/04/2025 14:30:38
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for hcm_valid_result_inhosp
-- ----------------------------
DROP TABLE IF EXISTS `hcm_valid_result_inhosp`;
CREATE TABLE `hcm_valid_result_inhosp`  (
  `rule_valid_result_id` bigint NOT NULL AUTO_INCREMENT COMMENT '校验结果id',
  `unique_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '患者唯一ID',
  `rule_scen_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '场景类型(门诊outpat、住院inhosp)',
  `rule_detl_codg` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '规则明细编码',
  `rule_data_meta` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '规则元组编码',
  `error_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '错误类型',
  `error_desc` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '错误备注',
  `error_detail_codg` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '校验错误编码',
  `med_list_codg` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '项目编码',
  `med_list_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '项目名称',
  `violation_amount` decimal(10, 2) NOT NULL COMMENT '项目金额',
  `cnt` decimal(10, 2) NULL DEFAULT NULL COMMENT '数量',
  `pric` decimal(10, 2) NULL DEFAULT NULL COMMENT '单价',
  `oprn_date` datetime NOT NULL COMMENT '新增时间',
  `update_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `vali_flag` varchar(6) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '有效标志',
  PRIMARY KEY (`rule_valid_result_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 23 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '住院自查自纠明细表' ROW_FORMAT = Dynamic;

SET FOREIGN_KEY_CHECKS = 1;
