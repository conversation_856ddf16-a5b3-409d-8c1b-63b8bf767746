

-- --------update by qingfeng 240724start ----------
-- 创建床日诊断配置表并初始化
DROP TABLE IF EXISTS `bed_diag_codg_cfg`;
CREATE TABLE `bed_diag_codg_cfg`
(
    `id`             int NOT NULL AUTO_INCREMENT COMMENT 'id',
    `bed_type`       varchar(50) COLLATE utf8mb4_general_ci                        DEFAULT NULL COMMENT '床日类型',
    `bed_name`       varchar(100) COLLATE utf8mb4_general_ci                       DEFAULT NULL COMMENT '床日名称',
    `main_diag_codg` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '主诊断编码',
    `main_diag_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '主诊断名称',
    `year`           varchar(6) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   DEFAULT NULL COMMENT '标杆年度',
    `vali_flag`      varchar(6) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   DEFAULT NULL COMMENT '有效标志',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 661
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci;

-- ----------------------------
-- Records of bed_diag_codg_cfg
-- ----------------------------
BEGIN;
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (1, 'BR', '康复类', 'B90.002', '结核性脑膜炎后遗症', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (2, 'BR', '康复类', 'B94.100', '病毒性脑炎的后遗症', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (3, 'BR', '康复类', 'B94.101', '流行性乙型脑炎后遗症', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (4, 'BR', '康复类', 'B94.800x003', '流行性脑脊髓膜炎后遗症', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (5, 'BR', '康复类', 'G09.x00', '中枢神经系统炎性疾病的后遗症', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (6, 'BR', '康复类', 'G09.x00x001', '脊髓炎后遗症', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (7, 'BR', '康复类', 'G09.x00x002', '脑炎后遗症', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (8, 'BR', '康复类', 'G09.x00x003', '脑膜炎后遗症', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (9, 'BR', '康复类', 'G09.x00x004', '脑脊髓炎后遗症', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (10, 'BR', '康复类', 'G09.x01', '感染中毒性脑病后遗症', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (11, 'BR', '康复类', 'G80.000', '痉挛性四肢麻痹性脑瘫', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (12, 'BR', '康复类', 'G80.000x011', '双侧痉挛型脑性瘫痪', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (13, 'BR', '康复类', 'G80.000x021', '偏侧痉挛型脑性瘫痪', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (14, 'BR', '康复类', 'G80.100', '痉挛性双侧脑瘫', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (15, 'BR', '康复类', 'G80.101', '痉挛型脑性瘫痪', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (16, 'BR', '康复类', 'G80.200', '痉挛性偏侧脑瘫', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (17, 'BR', '康复类', 'G80.200x001', '婴儿性偏瘫', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (18, 'BR', '康复类', 'G80.300', '运动障碍性脑瘫', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (19, 'BR', '康复类', 'G80.300x003', '强直型脑性瘫痪', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (20, 'BR', '康复类', 'G80.302', '手足徐动型脑性瘫痪', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (21, 'BR', '康复类', 'G80.303', '肌张力低下型脑性瘫痪', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (22, 'BR', '康复类', 'G80.400', '共济失调性脑瘫', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (23, 'BR', '康复类', 'G80.800', '大脑性瘫痪［脑瘫］，其他的', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (24, 'BR', '康复类', 'G80.801', '震颤型脑性瘫痪', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (25, 'BR', '康复类', 'G80.802', '混合型脑性瘫痪', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (26, 'BR', '康复类', 'G80.900', '大脑性瘫痪［脑瘫］', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (27, 'BR', '康复类', 'G81.000', '松弛性偏瘫', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (28, 'BR', '康复类', 'G81.100', '痉挛性偏瘫', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (29, 'BR', '康复类', 'G81.900', '偏瘫', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (30, 'BR', '康复类', 'G81.900x002', '轻偏瘫', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (31, 'BR', '康复类', 'G81.901', '交替性偏瘫', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (32, 'BR', '康复类', 'G81.902', '完全性偏瘫', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (33, 'BR', '康复类', 'G81.903', '不完全性偏瘫', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (34, 'BR', '康复类', 'G82.000', '松弛性截瘫', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (35, 'BR', '康复类', 'G82.000x021', '慢性驰缓性截瘫', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (36, 'BR', '康复类', 'G82.000x041', '慢性完全性驰缓性截瘫', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (37, 'BR', '康复类', 'G82.000x061', '慢性不完全性驰缓性截瘫', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (38, 'BR', '康复类', 'G82.100', '痉挛性截瘫', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (39, 'BR', '康复类', 'G82.100x021', '慢性痉挛性截瘫', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (40, 'BR', '康复类', 'G82.100x041', '慢性完全性痉挛性截瘫', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (41, 'BR', '康复类', 'G82.100x061', '慢性不完全性痉挛性截瘫', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (42, 'BR', '康复类', 'G82.200', '截瘫', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (43, 'BR', '康复类', 'G82.200x021', '慢性截瘫', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (44, 'BR', '康复类', 'G82.200x041', '慢性完全性截瘫', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (45, 'BR', '康复类', 'G82.201', '慢性不完全性截瘫', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (46, 'BR', '康复类', 'G82.204', '高位截瘫', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (47, 'BR', '康复类', 'G82.300', '松弛性四肢瘫痪', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (48, 'BR', '康复类', 'G82.300x021', '慢性驰缓性四肢瘫', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (49, 'BR', '康复类', 'G82.300x041', '慢性完全性驰缓性四肢瘫', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (50, 'BR', '康复类', 'G82.300x061', '慢性不完全性驰缓性四肢瘫', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (51, 'BR', '康复类', 'G82.400', '痉挛性四肢瘫痪', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (52, 'BR', '康复类', 'G82.400x011', '急性痉挛性四肢瘫', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (53, 'BR', '康复类', 'G82.400x041', '慢性完全性痉挛性四肢瘫', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (54, 'BR', '康复类', 'G82.400x061', '慢性不完全性痉挛性四肢瘫', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (55, 'BR', '康复类', 'G82.401', '慢性痉挛性四肢瘫', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (56, 'BR', '康复类', 'G82.500', '四肢瘫痪', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (57, 'BR', '康复类', 'G82.500x041', '慢性完全性四肢瘫', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (58, 'BR', '康复类', 'G82.502', '慢性四肢瘫', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (59, 'BR', '康复类', 'G82.504', '慢性不完全性四肢瘫', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (60, 'BR', '康复类', 'G83.000', '双上肢瘫', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (61, 'BR', '康复类', 'G83.000x002', '完全性双上肢瘫', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (62, 'BR', '康复类', 'G83.000x003', '不完全性双上肢瘫', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (63, 'BR', '康复类', 'G83.100', '下肢单瘫', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (64, 'BR', '康复类', 'G83.100x002', '完全性下肢单瘫', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (65, 'BR', '康复类', 'G83.100x003', '不完全性下肢单瘫', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (66, 'BR', '康复类', 'G83.200', '上肢单瘫', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (67, 'BR', '康复类', 'G83.200x002', '完全性上肢单瘫', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (68, 'BR', '康复类', 'G83.200x003', '不完全性上肢单瘫', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (69, 'BR', '康复类', 'G83.300', '单瘫', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (70, 'BR', '康复类', 'G83.300x002', '完全性单瘫', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (71, 'BR', '康复类', 'G83.300x003', '不完全性单瘫', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (72, 'BR', '康复类', 'G83.800', '麻痹［瘫痪］综合征，其他特指的', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (73, 'BR', '康复类', 'G83.800x001', '脊髓半切综合征[布朗-塞卡尔氏综合征]', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (74, 'BR', '康复类', 'G83.800x003', '交叉性瘫痪', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (75, 'BR', '康复类', 'G83.801', '布朗-塞卡尔综合征', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (76, 'BR', '康复类', 'G83.802', '脊髓完全性瘫痪', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (77, 'BR', '康复类', 'G83.803', '托德瘫痪', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (78, 'BR', '康复类', 'G83.900', '麻痹［瘫痪］综合征', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (79, 'BR', '康复类', 'G83.900x001', '痉挛性瘫痪[中枢性瘫痪]', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (80, 'BR', '康复类', 'G83.900x002', '瘫痪', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (81, 'BR', '康复类', 'G83.900x003', '完全性瘫痪', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (82, 'BR', '康复类', 'G83.900x004', '不完全性瘫痪', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (83, 'BR', '康复类', 'G83.900x005', '弛缓性瘫痪[周围性瘫痪]', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (84, 'BR', '康复类', 'G83.901', '轻度瘫痪', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (85, 'BR', '康复类', 'G97.800x004', '手术后瘫痪', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (86, 'BR', '康复类', 'I63.302', '血栓性偏瘫', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (87, 'BR', '康复类', 'I63.401', '大脑动脉栓塞引起的偏瘫', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (88, 'BR', '康复类', 'I69.000', '蛛网膜下出血后遗症', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (89, 'BR', '康复类', 'I69.000x001', '蛛网膜下腔出血后遗症', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (90, 'BR', '康复类', 'I69.000x002', '蛛网膜下腔出血恢复期', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (91, 'BR', '康复类', 'I69.000x003', '陈旧性蛛网膜下腔出血', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (92, 'BR', '康复类', 'I69.100', '脑内出血后遗症', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (93, 'BR', '康复类', 'I69.100x001', '脑出血后遗症', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (94, 'BR', '康复类', 'I69.100x002', '脑出血恢复期', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (95, 'BR', '康复类', 'I69.100x003', '陈旧性脑出血', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (96, 'BR', '康复类', 'I69.200', '非创伤性颅内出血后遗症，其他的', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (97, 'BR', '康复类', 'I69.200x001', '颅内出血后遗症', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (98, 'BR', '康复类', 'I69.300', '脑梗死后遗症', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (99, 'BR', '康复类', 'I69.400', '脑卒中后遗症', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (100, 'BR', '康复类', 'I69.800', '脑血管病后遗症，其他和未特指的', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (101, 'BR', '康复类', 'I69.800x002', '脑血管病恢复期', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (102, 'BR', '康复类', 'I69.800x003', '缺血缺氧性脑病后遗症', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (103, 'BR', '康复类', 'I69.801', '脑血栓后遗症', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (104, 'BR', '康复类', 'I69.802', '脑血管病后遗症', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (105, 'BR', '康复类', 'M23.205', '陈旧性膝内侧半月板损伤', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (106, 'BR', '康复类', 'M23.210', '陈旧性膝外侧半月板损伤', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (107, 'BR', '康复类', 'M23.211', '陈旧性膝关节囊韧带损伤', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (108, 'BR', '康复类', 'M23.213', '陈旧性膝半月板损伤', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (109, 'BR', '康复类', 'M23.800x094', '陈旧性膝关节韧带损伤', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (110, 'BR', '康复类', 'M23.800x095', '陈旧性膝关节软骨损伤', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (111, 'BR', '康复类', 'M50.101+G55.1*', '颈椎间盘突出伴有神经根病', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (112, 'BR', '康复类', 'M50.201', '颈椎间盘突出', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (113, 'BR', '康复类', 'M50.202', '颈椎胸椎椎间盘突出', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (114, 'BR', '康复类', 'M51.003+G99.2*', '腰椎间盘突出伴脊髓病', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (115, 'BR', '康复类', 'M51.101+G55.1*', '腰椎间盘脱出伴坐骨神经痛', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (116, 'BR', '康复类', 'M51.202', '腰椎间盘突出', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (117, 'BR', '康复类', 'M62.300', '不动综合征（截瘫性）', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (118, 'BR', '康复类', 'M96.802', '手术后腰椎间盘粘连', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (119, 'BR', '康复类', 'M96.803', '腰椎间盘切除术后状态关节紊乱', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (120, 'BR', '康复类', 'N18.507+G99.8*', '尿毒症性偏瘫', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (121, 'BR', '康复类', 'O99.308', '妊娠合并截瘫', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (122, 'BR', '康复类', 'O99.309', '妊娠合并脑瘫', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (123, 'BR', '康复类', 'T09.301', '创伤性截瘫', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (124, 'BR', '康复类', 'T70.300x002', '潜水员瘫痪', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (125, 'BR', '康复类', 'T90.100', '头部开放性伤口后遗症', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (126, 'BR', '康复类', 'T90.300', '脑神经损伤后遗症', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (127, 'BR', '康复类', 'T90.301', '陈旧性脑神经损伤', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (128, 'BR', '康复类', 'T90.400x001', '眼部开放性损伤后遗症', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (129, 'BR', '康复类', 'T90.500', '颅内损伤后遗症', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (130, 'BR', '康复类', 'T90.500x002', '脑外伤后遗症', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (131, 'BR', '康复类', 'T90.500x003', '颅内开放性损伤后遗症', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (132, 'BR', '康复类', 'T90.501', '陈旧性颅脑损伤', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (133, 'BR', '康复类', 'T90.502', '陈旧性颅内损伤', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (134, 'BR', '康复类', 'T90.800', '头部其他特指损伤的后遗症', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (135, 'BR', '康复类', 'T90.900', '头部损伤的后遗症', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (136, 'BR', '康复类', 'T91.000', '颈部和躯干浅表损伤和开放性伤口后遗症', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (137, 'BR', '康复类', 'T91.000x003', '颈部开放性损伤后遗症', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (138, 'BR', '康复类', 'T91.000x004', '躯干开放性损伤后遗症', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (139, 'BR', '康复类', 'T91.002', '陈旧性开放性颈部和躯干损伤', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (140, 'BR', '康复类', 'T91.100', '脊柱骨折后遗症', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (141, 'BR', '康复类', 'T91.102', '陈旧性颈椎骨折', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (142, 'BR', '康复类', 'T91.200', '胸和骨盆的其他骨折后遗症', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (143, 'BR', '康复类', 'T91.300', '脊髓损伤后遗症', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (144, 'BR', '康复类', 'T91.300x002', '颈部脊髓损伤后遗症', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (145, 'BR', '康复类', 'T91.300x003', '胸部脊髓损伤后遗症', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (146, 'BR', '康复类', 'T91.300x004', '腰部脊髓损伤后遗症', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (147, 'BR', '康复类', 'T91.301', '陈旧性脊髓损伤', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (148, 'BR', '康复类', 'T91.800', '颈部和躯干其他特指损伤的后遗症', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (149, 'BR', '康复类', 'T91.800x001', '膈神经损伤后遗症', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (150, 'BR', '康复类', 'T91.800x002', '陈旧性环枢椎脱位', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (151, 'BR', '康复类', 'T91.800x003', '陈旧性颈椎脱位', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (152, 'BR', '康复类', 'T91.800x004', '腰丛神经损伤后遗症', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (153, 'BR', '康复类', 'T91.800x005', '骶丛神经损伤后遗症', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (154, 'BR', '康复类', 'T91.800x006', '陈旧性脊柱脱位', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (155, 'BR', '康复类', 'T91.800x007', '躯干神经损伤后遗症', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (156, 'BR', '康复类', 'T91.800x008', '脊柱韧带扭伤后遗症', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (157, 'BR', '康复类', 'T91.802', '陈旧性躯干神经损伤', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (158, 'BR', '康复类', 'T91.803', '陈旧性脊柱韧带扭伤', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (159, 'BR', '康复类', 'T91.900', '颈部和躯干损伤的后遗症', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (160, 'BR', '康复类', 'T91.900x002', '会阴损伤后遗症', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (161, 'BR', '康复类', 'T91.900x003', '躯干损伤后遗症', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (162, 'BR', '康复类', 'T91.800x009', '脊柱损伤后遗症', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (163, 'BR', '康复类', 'T91.800x010', '陈旧性颈部和躯干损伤', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (165, 'BR', '康复类', 'T92.000', '上肢开放性伤口后遗症', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (166, 'BR', '康复类', 'T92.001', '陈旧性开放性上肢损伤', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (167, 'BR', '康复类', 'T92.100', '臂骨折后遗症', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (168, 'BR', '康复类', 'T92.200', '腕和手水平骨折后遗症', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (169, 'BR', '康复类', 'T92.300', '上肢脱位、扭伤和劳损后遗症', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (170, 'BR', '康复类', 'T92.300x001', '上肢脱位后遗症', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (171, 'BR', '康复类', 'T92.300x002', '上肢扭伤后遗症', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (172, 'BR', '康复类', 'T92.300x005', '陈旧性腕掌关节脱位', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (173, 'BR', '康复类', 'T92.300x006', '陈旧性掌指关节脱位', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (174, 'BR', '康复类', 'T92.300x007', '陈旧性指关节脱位', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (175, 'BR', '康复类', 'T92.300x008', '陈旧性桡骨头脱位', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (176, 'BR', '康复类', 'T92.300x011', '指韧带损伤后遗症', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (177, 'BR', '康复类', 'T92.300x012', '陈旧性下尺桡关节损伤', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (178, 'BR', '康复类', 'T92.300x013', '陈旧性舟骨月骨周围脱位', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (179, 'BR', '康复类', 'T92.300x015', '掌板侧副韧带损伤后遗症', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (180, 'BR', '康复类', 'T92.300x016', '肘关节韧带损伤后遗症', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (181, 'BR', '康复类', 'T92.300x017', '上肢韧带损伤后遗症', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (182, 'BR', '康复类', 'T92.301', '陈旧性手部关节韧带损伤', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (183, 'BR', '康复类', 'T92.302', '陈旧性桡尺关节脱位', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (184, 'BR', '康复类', 'T92.303', '肩关节扭伤后遗症', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (185, 'BR', '康复类', 'T92.304', '肩关节劳损后遗症', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (186, 'BR', '康复类', 'T92.305', '肘关节脱位后遗症', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (187, 'BR', '康复类', 'T92.306', '肘关节扭伤后遗症', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (188, 'BR', '康复类', 'T92.307', '肘关节劳损后遗症', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (189, 'BR', '康复类', 'T92.400', '上肢神经损伤后遗症', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (190, 'BR', '康复类', 'T92.400x002', '桡神经损伤后遗症', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (191, 'BR', '康复类', 'T92.400x003', '正中神经损伤后遗症', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (192, 'BR', '康复类', 'T92.400x004', '指神经损伤后遗症', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (193, 'BR', '康复类', 'T92.400x005', '肌皮神经损伤后遗症', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (194, 'BR', '康复类', 'T92.400x006', '腋神经损伤后遗症', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (195, 'BR', '康复类', 'T92.400x007', '臂丛神经损伤后遗症', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (196, 'BR', '康复类', 'T92.400x008', '尺神经损伤后遗症', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (197, 'BR', '康复类', 'T92.401', '陈旧性上肢神经损伤', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (198, 'BR', '康复类', 'T92.402', '陈旧性手部神经损伤', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (199, 'BR', '康复类', 'T92.500x017', '上肢肌肉损伤后遗症', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (200, 'BR', '康复类', 'T92.500x001', '屈肌腱断裂后遗症', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (201, 'BR', '康复类', 'T92.500x002', '屈肌腱粘连后遗症', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (202, 'BR', '康复类', 'T92.500x003', '屈拇长肌腱损伤后遗症', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (203, 'BR', '康复类', 'T92.500x004', '屈指肌腱损伤后遗症', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (204, 'BR', '康复类', 'T92.500x006', '伸肌腱断裂后遗症', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (205, 'BR', '康复类', 'T92.500x007', '伸肌腱粘连后遗症', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (206, 'BR', '康复类', 'T92.500x008', '伸拇长肌腱损伤后遗症', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (207, 'BR', '康复类', 'T92.500x009', '伸指肌腱损伤后遗症', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (208, 'BR', '康复类', 'T92.500x010', '手部肌腱挛缩后遗症', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (209, 'BR', '康复类', 'T92.500x011', '手部肌腱损伤后遗症', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (210, 'BR', '康复类', 'T92.500x012', '上肢肌腱粘连后遗症', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (211, 'BR', '康复类', 'T92.500x013', '指伸肌腱粘连后遗症', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (212, 'BR', '康复类', 'T92.500x014', '手部肌肉损伤后遗症', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (213, 'BR', '康复类', 'T92.500x015', '指屈肌腱粘连后遗症', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (214, 'BR', '康复类', 'T92.500x016', '肩袖损伤后遗症', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (216, 'BR', '康复类', 'T92.500x018', '上肢肌腱损伤后遗症', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (217, 'BR', '康复类', 'T92.501', '陈旧性上肢肌腱断裂', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (218, 'BR', '康复类', 'T92.502', '陈旧性上肢肌肉撕裂', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (219, 'BR', '康复类', 'T92.504', '陈旧性腕关节肌腱损伤', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (220, 'BR', '康复类', 'T92.505', '陈旧性肱二头肌肌肉损伤', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (221, 'BR', '康复类', 'T92.506', '陈旧性肱二头肌建损伤', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (222, 'BR', '康复类', 'T92.600', '上肢挤压伤和创伤性切断后遗症', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (223, 'BR', '康复类', 'T92.601', '陈旧性上肢挤压伤', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (224, 'BR', '康复类', 'T92.602', '创伤性上肢切断后遗症', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (225, 'BR', '康复类', 'T92.603', '陈旧性手压伤', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (226, 'BR', '康复类', 'T92.800', '上肢其他特指损伤的后遗症', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (227, 'BR', '康复类', 'T92.800x001', '上肢血管损伤后遗症', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (228, 'BR', '康复类', 'T92.800x002', '陈旧性肩关节SLAP损伤', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (229, 'BR', '康复类', 'T92.801', '手其他损伤后遗症', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (230, 'BR', '康复类', 'T92.900', '上肢损伤的后遗症', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (231, 'BR', '康复类', 'T93.000', '下肢开放性伤口后遗症', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (232, 'BR', '康复类', 'T93.001', '陈旧性开放性下肢损伤', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (233, 'BR', '康复类', 'T93.100', '股骨骨折后遗症', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (234, 'BR', '康复类', 'T93.200', '下肢其他骨折的后遗症', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (235, 'BR', '康复类', 'T93.300', '下肢脱位、扭伤和劳损后遗症', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (236, 'BR', '康复类', 'T93.300x001', '下肢脱位后遗症', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (237, 'BR', '康复类', 'T93.300x002', '下肢扭伤后遗症', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (238, 'BR', '康复类', 'T93.300x003', '下肢劳损后遗症', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (239, 'BR', '康复类', 'T93.300x005', '陈旧性趾间关节脱位', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (240, 'BR', '康复类', 'T93.300x008', '陈旧性足舟骨脱位', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (241, 'BR', '康复类', 'T93.300x009', '陈旧性跖趾关节脱位', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (242, 'BR', '康复类', 'T93.301', '陈旧性髌骨脱位', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (243, 'BR', '康复类', 'T93.400', '下肢神经损伤后遗症', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (244, 'BR', '康复类', 'T93.400x002', '坐骨神经损伤后遗症', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (245, 'BR', '康复类', 'T93.400x003', '股神经损伤后遗症', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (246, 'BR', '康复类', 'T93.400x004', '腓总神经损伤后遗症', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (247, 'BR', '康复类', 'T93.400x005', '胫神经损伤后遗症', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (248, 'BR', '康复类', 'T93.400x006', '腓肠神经损伤后遗症', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (249, 'BR', '康复类', 'T93.500', '下肢肌肉和肌腱损伤后遗症', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (250, 'BR', '康复类', 'T93.500x001', '下肢肌肉损伤后遗症', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (251, 'BR', '康复类', 'T93.500x002', '下肢肌腱损伤后遗症', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (252, 'BR', '康复类', 'T93.501', '跟腱断裂后遗症', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (253, 'BR', '康复类', 'T93.600', '下肢挤压伤和创伤性切断后遗症', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (254, 'BR', '康复类', 'T93.600x002', '陈旧性下肢挤压伤', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (255, 'BR', '康复类', 'T93.600x003', '创伤性下肢切断后遗症', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (256, 'BR', '康复类', 'T93.800', '下肢其他特指损伤的后遗症', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (257, 'BR', '康复类', 'T93.800x001', '趾浅表挫伤后遗症', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (258, 'BR', '康复类', 'T93.800x002', '下肢创伤性动静脉瘘后遗症', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (259, 'BR', '康复类', 'T93.800x003', '下肢血管损伤后遗症', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (260, 'BR', '康复类', 'T93.801', '陈旧性趾挫伤', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (261, 'BR', '康复类', 'T93.900', '下肢损伤的后遗症', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (262, 'BR', '康复类', 'T94.000', '涉及多个身体部位损伤的后遗症', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (263, 'BR', '康复类', 'T94.001', '陈旧性多处身体部位损伤', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (264, 'BR', '康复类', 'T94.100', '损伤后遗症', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (265, 'BR', '康复类', 'T94.102', '陈旧性损伤', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (266, 'BR', '康复类', 'T97.x00x003', '中毒性脑病后遗症', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (267, 'BR', '康复类', 'T97.x01', '一氧化碳中毒后遗症', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (268, 'BR', '康复类', 'T97.x02', '非药用物质中毒性脑病后遗症', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (269, 'BR', '康复类', 'T98.300x007', '手术后缺氧性脑损害的后遗症', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (270, 'BR', '康复类', 'Z50.000', '心脏病康复', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (271, 'BR', '康复类', 'Z50.101', '脑出血后物理康复训练', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (272, 'BR', '康复类', 'Z50.300', '药物滥用康复', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (273, 'BR', '康复类', 'Z50.501', '脑出血后语言康复训练', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (274, 'BR', '康复类', 'Z50.700', '职业性治疗和职业性康复，不可归类在他处者', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (275, 'BR', '康复类', 'Z50.700x001', '职业康复训练和治疗', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (276, 'BR', '康复类', 'Z50.800', '涉及使用其他康复操作的医疗', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (277, 'BS', '康复类', 'Z50.801', '烧伤后康复治疗', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (278, 'BS', '康复类', 'Z50.900', '涉及使用康复操作的医疗', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (279, 'BS', '康复类', 'Z50.900x001', '康复医疗', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (280, 'BS', '精神类', 'F20.000', '偏执型精神分裂症', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (281, 'BS', '精神类', 'F20.100', '青春型精神分裂症', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (282, 'BS', '精神类', 'F20.200', '紧张型精神分裂症', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (283, 'BS', '精神类', 'F20.200x002', '紧张性木僵', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (284, 'BS', '精神类', 'F20.201', '紧张症综合征', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (285, 'BS', '精神类', 'F20.300', '未分化型精神分裂症', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (286, 'BS', '精神类', 'F20.301', '非典型精神分裂症', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (287, 'BS', '精神类', 'F20.400', '精神分裂症后抑郁', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (288, 'BS', '精神类', 'F20.500', '残留型精神分裂症', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (289, 'BS', '精神类', 'F20.501', '慢性精神分裂症', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (290, 'BS', '精神类', 'F20.600', '单纯型精神分裂症', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (291, 'BS', '精神类', 'F20.800x001', '难治性精神分裂症', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (292, 'BS', '精神类', 'F20.800x002', '精神分裂症衰退期', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (293, 'BS', '精神类', 'F20.800x003', '精神分裂症缓解期', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (294, 'BS', '精神类', 'F20.801', '体感异常性精神分裂症', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (295, 'BS', '精神类', 'F20.802', '晚发性精神分裂症', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (296, 'BS', '精神类', 'F20.803', '强迫型精神分裂症', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (297, 'BS', '精神类', 'F20.900', '精神分裂症', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (298, 'BS', '精神类', 'F31.000', '双相情感障碍，目前为轻躁狂发作', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (299, 'BS', '精神类', 'F31.100', '双相情感障碍，目前为不伴有精神病性症状的躁狂发作', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (300, 'BS', '精神类', 'F31.200', '双相情感障碍，目前为伴有精神病性症状的躁狂发作', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (301, 'BS', '精神类', 'F31.300x002', '双相情感障碍,目前为轻度抑郁发作', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (302, 'BS', '精神类', 'F31.300x003', '双相情感障碍,目前为不伴有躯体症状的轻度抑郁发作', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (303, 'BS', '精神类', 'F31.300x005', '双相情感障碍,目前为不伴有躯体症状的中度抑郁发作', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (304, 'BS', '精神类', 'F31.300x011', '双相情感障碍,目前为伴有躯体症状的轻度抑郁发作', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (305, 'BS', '精神类', 'F31.300x012', '双相情感障碍,目前为伴有躯体症状的中度抑郁发作', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (306, 'BS', '精神类', 'F31.301', '双相情感障碍，目前为中度抑郁发作', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (307, 'BS', '精神类', 'F31.400', '双相情感障碍，目前为不伴有精神病性症状的重度抑郁发作', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (308, 'BS', '精神类', 'F31.500', '双相情感障碍，目前为伴有精神病性症状的重度抑郁发作', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (309, 'BS', '精神类', 'F31.600', '双相情感障碍，目前为混合性发作', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (310, 'BS', '精神类', 'F31.700', '双相情感障碍，目前为缓解状态', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (311, 'BS', '精神类', 'F31.800x001', '复发性躁狂发作', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (312, 'BS', '精神类', 'F31.800x002', '双相情感障碍2型', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (313, 'BS', '精神类', 'F31.800x003', '难治性双相情感障碍', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (314, 'BS', '精神类', 'F31.801', '慢性躁狂症', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (315, 'BS', '精神类', 'F31.802', '双相情感障碍,快速循环型', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (316, 'BS', '精神类', 'F31.803', '非典型双相情感障碍', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (317, 'BS', '精神类', 'F31.900', '双相情感障碍', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (318, 'BS', '精神类', 'F31.901', '双相情感障碍Ⅰ型', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (319, 'BS', '精神类', 'F31.902', '躁郁症', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (320, 'BS', '精神类', 'F32.000x002', '不伴有躯体症状的轻度抑郁发作', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (321, 'BS', '精神类', 'F32.000x011', '伴有躯体症状的轻度抑郁发作', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (322, 'BS', '精神类', 'F32.100x002', '不伴有躯体症状的中度抑郁发作', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (323, 'BS', '精神类', 'F32.100x011', '伴有躯体症状的中度抑郁发作', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (324, 'BS', '精神类', 'F32.200', '不伴有精神病性症状的重度抑郁发作', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (325, 'BS', '精神类', 'F32.300', '伴有精神病性症状的重度抑郁发作', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (326, 'BS', '精神类', 'F32.301', '抑郁性精神病', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (327, 'BS', '精神类', 'F32.800x001', '抑郁性木僵', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (328, 'BS', '精神类', 'F32.800x002', '难治性抑郁症', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (329, 'BS', '精神类', 'F32.801', '更年期抑郁症', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (330, 'BS', '精神类', 'F32.802', '非典型抑郁症', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (331, 'BS', '精神类', 'F32.900', '抑郁发作', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (332, 'BS', '精神类', 'F32.901', '抑郁状态', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (333, 'BS', '精神类', 'F32.902', '反应性抑郁症', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (334, 'BS', '精神类', 'F33.000', '复发性抑郁障碍，目前为轻度发作', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (335, 'BS', '精神类', 'F33.000x002', '复发性抑郁障碍,目前为伴有躯体症状的轻度发作', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (336, 'BS', '精神类', 'F33.000x011', '复发性抑郁障碍,目前为不伴有躯体症状的轻度发作', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (337, 'BS', '精神类', 'F33.100', '复发性抑郁障碍，目前为中度发作', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (338, 'BS', '精神类', 'F33.100x002', '复发性抑郁障碍,目前为伴有躯体症状的中度发作', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (339, 'BS', '精神类', 'F33.100x011', '复发性抑郁障碍,目前为不伴有躯体症状的中度发作', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (340, 'BS', '精神类', 'F33.200', '复发性抑郁障碍，目前为不伴有精神病性症状的重度发作', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (341, 'BS', '精神类', 'F33.300', '复发性抑郁障碍，目前为伴有精神病性症状的重度发作', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (342, 'BS', '精神类', 'F33.400', '复发性抑郁障碍，目前为缓解状态', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (343, 'BS', '精神类', 'F33.800', '复发性抑郁障碍，其他的', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (344, 'BS', '精神类', 'F33.900', '复发性抑郁障碍', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (345, 'BS', '精神类', 'F30.000', '轻躁狂', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (346, 'BS', '精神类', 'F30.100', '不伴有精神病性症状的躁狂', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (347, 'BS', '精神类', 'F30.100x001', '不伴有精神病性症状的躁狂发作', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (348, 'BS', '精神类', 'F30.200', '伴有精神病性症状的躁狂', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (349, 'BS', '精神类', 'F30.200x001', '伴有精神病性症状的躁狂发作', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (350, 'BS', '精神类', 'F30.200x002', '躁狂性木僵', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (351, 'BS', '精神类', 'F30.201', '谵妄性躁狂症', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (352, 'BS', '精神类', 'F30.800x002', '兴奋状态', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (353, 'BS', '精神类', 'F30.900', '躁狂发作', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (354, 'BS', '精神类', 'F30.901', '兴奋躁动状态', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (355, 'BS', '精神类', 'F23.000', '不伴有精神分裂症症状的急性多形性精神病性障碍', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (356, 'BS', '精神类', 'F23.001', '妄想阵发,急性妄想发作', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (357, 'BS', '精神类', 'F23.002', '周期性精神病', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (358, 'BS', '精神类', 'F23.100', '伴有精神分裂症症状的急性多形性精神病性障碍', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (359, 'BS', '精神类', 'F23.200', '急性精神分裂症样精神病性障碍', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (360, 'BS', '精神类', 'F23.200x003', '急性精神分裂样精神病性障碍,不伴急性应激反应', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (361, 'BS', '精神类', 'F23.200x011', '急性精神分裂样精神病性障碍,伴有急性应激反应', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (362, 'BS', '精神类', 'F23.300x001', '偏执性反应', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (363, 'BS', '精神类', 'F23.300x002', '心因性偏执性精神障碍', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (364, 'BS', '精神类', 'F23.300x003', '以妄想为主的急性精神病性障碍', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (365, 'BS', '精神类', 'F23.301', '急性偏执性反应状态', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (366, 'BS', '精神类', 'F23.800', '急性而短暂的精神病性障碍，其他的', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (367, 'BS', '精神类', 'F23.900', '急性而短暂的精神病性障碍', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (368, 'BS', '精神类', 'F23.901', '反应性精神病', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (369, 'BS', '精神类', 'F23.902', '旅途精神病', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (370, 'BS', '精神类', 'F23.903', '急性反应性木僵状态', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (371, 'BS', '精神类', 'F10.000', '急性酒精中毒引起的精神和行为障碍', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (372, 'BS', '精神类', 'F10.001', '急性酒精中毒', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (373, 'BS', '精神类', 'F10.002', '病理性醉酒', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (374, 'BS', '精神类', 'F10.003', '复杂性醉酒', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (375, 'BS', '精神类', 'F10.100', '有害性使用酒精引起的精神和行为障碍', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (376, 'BS', '精神类', 'F10.100x002', '酒精非成瘾性滥用', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (377, 'BS', '精神类', 'F10.200', '使用酒精引起的依赖综合征', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (378, 'BS', '精神类', 'F10.201', '慢性酒精中毒', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (379, 'BS', '精神类', 'F10.300', '使用酒精引起的戒断状态', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (380, 'BS', '精神类', 'F10.400', '使用酒精引起的戒断状态伴有谵妄', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (381, 'BS', '精神类', 'F10.401', '酒精性谵妄', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (382, 'BS', '精神类', 'F10.500', '使用酒精引起的精神性障碍', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (383, 'BS', '精神类', 'F10.501', '慢性酒精中毒性分裂样精神病', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (384, 'BS', '精神类', 'F10.502', '慢性酒精中毒性妄想症', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (385, 'BS', '精神类', 'F10.503', '慢性酒精中毒性幻觉症', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (386, 'BS', '精神类', 'F10.504', '酒精中毒性抑郁状态', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (387, 'BS', '精神类', 'F10.505', '酒精中毒性躁狂状态', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (388, 'BS', '精神类', 'F10.600', '使用酒精引起的遗忘综合征', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (389, 'BS', '精神类', 'F10.600x002', '酒精中毒性科尔萨科夫综合征', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (390, 'BS', '精神类', 'F10.601', '慢性酒精性谵妄', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (391, 'BS', '精神类', 'F10.700', '使用酒精引起的残留性和迟发性精神病性障碍', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (392, 'BS', '精神类', 'F10.700x091', '慢性酒精性脑综合征', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (393, 'BS', '精神类', 'F10.701', '酒精中毒性痴呆', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (394, 'BS', '精神类', 'F10.800', '使用酒精引起的其他精神和行为障碍', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (395, 'BS', '精神类', 'F10.900', '使用酒精引起的精神和行为障碍', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (396, 'BS', '精神类', 'F70.000', '轻度精神发育迟缓，无或轻微行为缺陷的', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (397, 'BS', '精神类', 'F70.000x001', '轻度精神发育迟滞', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (398, 'BS', '精神类', 'F70.100', '轻度精神发育迟缓，需要加以关注或治疗的显著行为缺陷', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (399, 'BS', '精神类', 'F70.800', '轻度精神发育迟缓，其他行为缺陷', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (400, 'BS', '精神类', 'F70.900', '轻度精神发育迟缓，未提及行为缺陷的', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (401, 'BS', '精神类', 'F71.000', '中度精神发育迟缓，无或轻微行为缺陷的', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (402, 'BS', '精神类', 'F71.000x001', '中度精神发育迟滞', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (403, 'BS', '精神类', 'F71.100', '中度精神发育迟缓，需要加以关注或治疗的显著行为缺陷', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (404, 'BS', '精神类', 'F71.800', '中度精神发育迟缓，其他的行为缺陷', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (405, 'BS', '精神类', 'F71.900', '中度精神发育迟缓，未提及行为缺陷的', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (406, 'BS', '精神类', 'F72.000', '重度精神发育迟缓，无或轻微行为缺陷的', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (407, 'BS', '精神类', 'F72.000x001', '重度精神发育迟滞', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (408, 'BS', '精神类', 'F72.100', '重度精神发育迟缓，需要加以关注或治疗的显著行为缺陷', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (409, 'BS', '精神类', 'F72.800', '重度精神发育迟缓，其他行为缺陷', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (410, 'BS', '精神类', 'F72.900', '重度精神发育迟缓，未提及行为缺陷的', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (411, 'BS', '精神类', 'F73.000', '极重度精神发育迟缓，无或轻微行为缺陷的', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (412, 'BS', '精神类', 'F73.000x001', '极重度精神发育迟滞', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (413, 'BS', '精神类', 'F73.100', '极重度精神发育迟缓，需要加以关注或治疗的显著行为缺陷', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (414, 'BS', '精神类', 'F73.800', '极重度精神发育迟缓，其他行为缺陷', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (415, 'BS', '精神类', 'F73.900', '极重度精神发育迟缓，未提及行为缺陷的', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (416, 'BS', '精神类', 'F78.000', '其他的精神发育迟缓，无或轻微行为缺陷的', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (417, 'BS', '精神类', 'F78.100', '其他精神发育迟缓，需要加以关注或治疗的显著行为缺陷', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (418, 'BS', '精神类', 'F78.800', '其他精神发育迟缓，其他行为缺陷的', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (419, 'BS', '精神类', 'F78.900', '精神发育迟缓其他的，未提及行为缺陷', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (420, 'BS', '精神类', 'F79.000', '精神发育迟缓，无或轻微行为缺陷的', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (421, 'BS', '精神类', 'F79.000x001', '精神发育迟滞', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (422, 'BS', '精神类', 'F79.100', '精神发育迟缓，需要加以关注或治疗的显著行为缺陷', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (423, 'BS', '精神类', 'F79.800', '精神发育迟缓引起的，其他的', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (424, 'BS', '精神类', 'F79.900', '精神发育迟缓，未提及行为缺陷', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (425, 'BS', '精神类', 'F79.901', '智力低下', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (426, 'BS', '精神类', 'F06.000', '器质性幻觉症', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (427, 'BS', '精神类', 'F06.100', '器质性紧张性障碍', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (428, 'BS', '精神类', 'F06.200', '器质性妄想性［精神分裂症样］障碍', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (429, 'BS', '精神类', 'F06.300', '器质性心境［情感］障碍', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (430, 'BS', '精神类', 'F06.300x002', '器质性躁狂障碍', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (431, 'BS', '精神类', 'F06.300x010', '器质性双相障碍', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (432, 'BS', '精神类', 'F06.300x020', '器质性抑郁障碍', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (433, 'BS', '精神类', 'F06.300x021', '卒中后抑郁', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (434, 'BS', '精神类', 'F06.300x030', '器质性混合型情感障碍', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (435, 'BS', '精神类', 'F06.301', '癫痫性情感障碍', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (436, 'BS', '精神类', 'F06.302', '颅脑外伤性情感障碍', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (437, 'BS', '精神类', 'F06.400', '器质性焦虑障碍', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (438, 'BS', '精神类', 'F06.400x003', '卒中后焦虑', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (439, 'BS', '精神类', 'F06.500', '器质性分离性障碍', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (440, 'BS', '精神类', 'F06.600', '器质性情绪不稳定［衰弱］障碍', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (441, 'BS', '精神类', 'F06.700', '轻度认知障碍', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (442, 'BS', '精神类', 'F06.800', '脑损害和功能障碍及躯体疾病引起的其他特指的精神障碍', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (443, 'BS', '精神类', 'F06.800x002', '胆道感染所致精神障碍', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (444, 'BS', '精神类', 'F06.800x003', '胆道术后精神障碍', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (445, 'BS', '精神类', 'F06.800x004', '低血糖所致精神障碍', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (446, 'BS', '精神类', 'F06.800x005', '肺结核所致精神障碍', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (447, 'BS', '精神类', 'F06.800x006', '肺气肿所致精神障碍', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (448, 'BS', '精神类', 'F06.800x007', '肺炎所致精神障碍', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (449, 'BS', '精神类', 'F06.800x008', '肝硬化所致精神障碍', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (450, 'BS', '精神类', 'F06.800x009', '感冒所致精神障碍', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (451, 'BS', '精神类', 'F06.800x010', '高热所致精神障碍', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (452, 'BS', '精神类', 'F06.800x011', '高血压所致精神障碍', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (453, 'BS', '精神类', 'F06.800x012', '过敏性紫癜所致精神障碍', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (454, 'BS', '精神类', 'F06.800x013', '甲状腺功能亢进所致精神障碍', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (455, 'BS', '精神类', 'F06.800x014', '疟疾所致精神障碍', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (456, 'BS', '精神类', 'F06.800x015', '肾炎所致精神障碍', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (457, 'BS', '精神类', 'F06.800x016', '细菌性痢疾所致精神障碍', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (458, 'BS', '精神类', 'F06.800x017', '心脏病所致精神障碍', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (459, 'BS', '精神类', 'F06.800x018', '营养不良所致精神障碍', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (460, 'BS', '精神类', 'F06.800x019', '有害气体中毒后精神障碍', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (461, 'BS', '精神类', 'F06.800x020', '中暑伴发精神障碍', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (462, 'BS', '精神类', 'F06.800x021', '系统性红斑狼疮所致的精神障碍', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (463, 'BS', '精神类', 'F06.800x023', '甲状腺功能减退所致精神障碍', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (464, 'BS', '精神类', 'F06.800x024', '一氧化碳中毒所致精神障碍', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (465, 'BS', '精神类', 'F06.800x025', '肠伤寒所致精神障碍', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (466, 'BS', '精神类', 'F06.800x026', '血管性认知功能障碍', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (467, 'BS', '精神类', 'F06.800x027', '认知障碍', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (468, 'BS', '精神类', 'F06.800x032', '血液病所致精神障碍', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (469, 'BS', '精神类', 'F06.800x033', '染色体异常所致精神障碍', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (470, 'BS', '精神类', 'F06.800x034', '物理因素所致精神障碍', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (471, 'BS', '精神类', 'F06.800x037', '肝脑病变所致精神障碍', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (472, 'BS', '精神类', 'F06.800x038', '心脏病（心力衰竭）所致精神障碍', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (473, 'BS', '精神类', 'F06.800x039', '肺脑综合征所致精神障碍', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (474, 'BS', '精神类', 'F06.800x040', '尿毒症所致精神障碍', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (475, 'BS', '精神类', 'F06.800x041', '内分泌疾病所致精神障碍', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (476, 'BS', '精神类', 'F06.800x042', '甲低所致精神障碍', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (477, 'BS', '精神类', 'F06.800x043', '脑下垂体疾病所致精神障碍', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (478, 'BS', '精神类', 'F06.800x044', 'Sheeham病所致精神障碍', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (479, 'BS', '精神类', 'F06.800x045', 'Addison氏病所致精神障碍', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (480, 'BS', '精神类', 'F06.800x046', '肾上腺功能亢进所致精神障碍', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (481, 'BS', '精神类', 'F06.800x047', '营养代谢疾病所致精神障碍', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (482, 'BS', '精神类', 'F06.800x048', '糖尿病所致精神障碍', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (483, 'BS', '精神类', 'F06.800x049', '胶原性疾病所致精神障碍', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (484, 'BS', '精神类', 'F06.800x050', 'Behcet氏病所致精神障碍', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (485, 'BS', '精神类', 'F06.801', '癫痫性精神病', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (486, 'BS', '精神类', 'F06.802', '颅脑外伤性精神病', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (487, 'BS', '精神类', 'F06.803', '颅内感染所致精神障碍', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (488, 'BS', '精神类', 'F06.804', '病毒性脑炎所致精神障碍', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (489, 'BS', '精神类', 'F06.805', '脑瘤所致精神障碍', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (490, 'BS', '精神类', 'F06.806', '肝豆核变性症所致精神障碍', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (491, 'BS', '精神类', 'F06.807', '多发性硬化症所致精神障碍', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (492, 'BS', '精神类', 'F06.808', '躯体疾病所致精神障碍', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (493, 'BS', '精神类', 'F06.809', '脑血管病所致精神障碍', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (494, 'BS', '精神类', 'F06.810', '卒中后精神病态', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (495, 'BS', '精神类', 'F06.811', '脑炎后精神障碍', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (496, 'BS', '精神类', 'F06.900', '脑损害和功能障碍及躯体疾病引起的精神障碍', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (497, 'BS', '精神类', 'F11.000', '急性阿片类物质中毒引起的精神和行为障碍', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (498, 'BS', '精神类', 'F11.000x001', '阿片类药急性中毒', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (499, 'BS', '精神类', 'F11.100', '有害性使用阿片类物质引起的精神和行为障碍', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (500, 'BS', '精神类', 'F11.100x001', '阿片类药有害使用', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (501, 'BS', '精神类', 'F11.200', '使用阿片类物质引起的依赖综合征', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (502, 'BS', '精神类', 'F11.200x001', '杜冷丁药物依赖', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (503, 'BS', '精神类', 'F11.200x003', '镇痛药物成瘾', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (504, 'BS', '精神类', 'F11.201', '吗啡型药物瘾', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (505, 'BS', '精神类', 'F11.202', '哌替啶药物瘾', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (506, 'BS', '精神类', 'F11.203', '咖啡型药物瘾', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (507, 'BS', '精神类', 'F11.204', '海洛因药物瘾', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (508, 'BS', '精神类', 'F11.300', '使用阿片类物质引起的戒断状态', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (509, 'BS', '精神类', 'F11.400', '使用阿片类物质引起的戒断状态伴有谵妄', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (510, 'BS', '精神类', 'F11.500', '使用阿片类物质引起的精神性障碍', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (511, 'BS', '精神类', 'F11.600', '使用阿片类物质引起的遗忘综合征', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (512, 'BS', '精神类', 'F11.700', '使用阿片类物质引起的残留性和迟发性精神病性障碍', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (513, 'BS', '精神类', 'F11.800', '使用阿片类物质引起的其他精神和行为障碍', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (514, 'BS', '精神类', 'F11.900', '使用阿片类物质引起的精神和行为障碍', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (515, 'BS', '精神类', 'F12.000', '急性大麻类物质中毒引起的精神和行为障碍', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (516, 'BS', '精神类', 'F12.000x002', '大麻类物质急性中毒', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (517, 'BS', '精神类', 'F12.100', '有害性使用大麻类物质引起的精神和行为障碍', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (518, 'BS', '精神类', 'F12.100x001', '大麻类物质非成瘾性滥用', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (519, 'BS', '精神类', 'F12.200', '使用大麻类物质引起的依赖综合征', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (520, 'BS', '精神类', 'F12.300', '使用大麻类物质引起的戒断状态', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (521, 'BS', '精神类', 'F12.400', '使用大麻类物质引起的戒断状态伴有谵妄', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (522, 'BS', '精神类', 'F12.500', '使用大麻类物质引起的精神性障碍', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (523, 'BS', '精神类', 'F12.600', '使用大麻类物质引起的遗忘综合征', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (524, 'BS', '精神类', 'F12.700', '使用大麻类物质引起的残留性和迟发性精神病性障碍', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (525, 'BS', '精神类', 'F12.800', '使用大麻类物质引起的其他精神和行为障碍', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (526, 'BS', '精神类', 'F12.900', '使用大麻类物质引起的精神和行为障碍', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (527, 'BS', '精神类', 'F13.000', '急性镇静剂或催眠剂中毒引起的精神和行为障碍', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (528, 'BS', '精神类', 'F13.000x001', '镇静剂或催眠剂急性中毒', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (529, 'BS', '精神类', 'F13.100', '有害性使用镇静剂或催眠剂引起的精神和行为障碍', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (530, 'BS', '精神类', 'F13.100x001', '镇静剂或催眠剂的有害使用', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (531, 'BS', '精神类', 'F13.200', '使用镇静剂或催眠剂引起的依赖综合征', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (532, 'BS', '精神类', 'F13.200x001', '巴比妥盐药物成瘾', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (533, 'BS', '精神类', 'F13.201', '安眠药物成瘾', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (534, 'BS', '精神类', 'F13.300', '使用镇静剂或催眠剂引起的戒断状态', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (535, 'BS', '精神类', 'F13.400', '使用镇静剂或催眠剂引起的戒断状态伴有谵妄', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (536, 'BS', '精神类', 'F13.500', '使用镇静剂或催眠剂引起的精神性障碍', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (537, 'BS', '精神类', 'F13.600', '使用镇静剂或催眠剂引起的遗忘综合征', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (538, 'BS', '精神类', 'F13.700', '使用镇静剂或催眠剂引起的残留性和迟发性精神病性障碍', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (539, 'BS', '精神类', 'F13.800', '使用镇静剂或催眠剂质引起的其他精神和行为障碍', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (540, 'BS', '精神类', 'F13.900', '使用镇静剂或催眠剂引起的精神和行为障碍', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (541, 'BS', '精神类', 'F14.000', '急性可卡因中毒引起的精神和行为障碍', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (542, 'BS', '精神类', 'F14.000x001', '可卡因急性中毒', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (543, 'BS', '精神类', 'F14.100', '有害性使用可卡因引起的精神和行为障碍', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (544, 'BS', '精神类', 'F14.100x001', '可卡因非成瘾性滥用', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (545, 'BS', '精神类', 'F14.200', '使用可卡因引起的依赖综合征', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (546, 'BS', '精神类', 'F14.300', '使用可卡因引起的戒断状态', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (547, 'BS', '精神类', 'F14.400', '使用可卡因引起的戒断状态伴有谵妄', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (548, 'BS', '精神类', 'F14.400x001', '伴有谵妄的可卡因戒断状态', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (549, 'BS', '精神类', 'F14.500', '使用可卡因引起的精神性障碍', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (550, 'BS', '精神类', 'F14.600', '使用可卡因引起的遗忘综合征', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (551, 'BS', '精神类', 'F14.700', '使用可卡因引起的残留性和迟发性精神病性障碍', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (552, 'BS', '精神类', 'F14.800', '使用可卡因质引起的其他精神和行为障碍', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (553, 'BS', '精神类', 'F14.900', '使用可卡因引起的精神和行为障碍', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (554, 'BS', '精神类', 'F15.000', '使用其他兴奋剂（包括咖啡因）急性中毒引起的精神和行为障碍', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (555, 'BS', '精神类', 'F15.000x002', '含有咖啡因的兴奋剂急性中毒', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (556, 'BS', '精神类', 'F15.000x003', '苯丙胺类兴奋剂急性中毒', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (557, 'BS', '精神类', 'F15.000x004', '氯胺酮急性中毒', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (558, 'BS', '精神类', 'F15.100', '有害性使用其他兴奋剂（包括咖啡 因）引起的精神和行为障碍', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (559, 'BS', '精神类', 'F15.100x001', '含有咖啡因的兴奋剂有害使用', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (560, 'BS', '精神类', 'F15.100x002', '咖啡因的有害使用', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (561, 'BS', '精神类', 'F15.100x003', '苯丙胺类兴奋剂的有害使用', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (562, 'BS', '精神类', 'F15.100x004', '氯胺酮的有害使用', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (563, 'BS', '精神类', 'F15.200x001', '含有咖啡因的兴奋剂依赖综合征', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (564, 'BS', '精神类', 'F15.200x002', '咖啡因依赖综合征', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (565, 'BS', '精神类', 'F15.200x003', '苯丙胺类兴奋剂依赖综合征', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (566, 'BS', '精神类', 'F15.200x004', '氯胺酮依赖综合征', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (567, 'BS', '精神类', 'F15.300x001', '含有咖啡因的兴奋剂戒断状态', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (568, 'BS', '精神类', 'F15.300x002', '咖啡因戒断状态', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (569, 'BS', '精神类', 'F15.300x003', '苯丙胺类兴奋剂戒断状态', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (570, 'BS', '精神类', 'F15.300x004', '氯胺酮戒断状态', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (571, 'BS', '精神类', 'F15.400x001', '伴有谵妄的含有咖啡因兴奋剂戒断状态', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (572, 'BS', '精神类', 'F15.400x002', '伴有谵妄的咖啡因戒断状态', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (573, 'BS', '精神类', 'F15.400x003', '伴有谵妄的苯丙胺类兴奋剂戒断状态', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (574, 'BS', '精神类', 'F15.400x004', '伴有谵妄的氯胺酮戒断状态', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (575, 'BS', '精神类', 'F15.500x001', '含有咖啡因的兴奋剂所致的精神病性障碍', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (576, 'BS', '精神类', 'F15.500x002', '咖啡因所致的精神病性障碍', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (577, 'BS', '精神类', 'F15.500x003', '苯丙胺类兴奋剂所致的精神病性障碍', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (578, 'BS', '精神类', 'F15.500x004', '氯胺酮所致的精神病性障碍', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (579, 'BS', '精神类', 'F15.501', '苯丙胺类中毒性精神病', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (580, 'BS', '精神类', 'F15.600x001', '含有咖啡因的兴奋剂所致的遗忘综合征', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (581, 'BS', '精神类', 'F15.600x002', '咖啡因所致的遗忘综合征', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (582, 'BS', '精神类', 'F15.600x003', '苯丙胺类兴奋剂所致的遗忘综合征', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (583, 'BS', '精神类', 'F15.600x004', '氯胺酮所致的遗忘综合征', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (584, 'BS', '精神类', 'F15.700x001', '含有咖啡因的兴奋剂所致的残留性和迟发性精神病性障碍', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (585, 'BS', '精神类', 'F15.700x002', '咖啡因所致的残留性和迟发性精神病性障碍', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (586, 'BS', '精神类', 'F15.700x003', '苯丙胺类兴奋剂所致的残留性和迟发性精神病性障碍', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (587, 'BS', '精神类', 'F15.700x004', '氯胺酮所致的残留性和迟发性精神病性障碍', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (588, 'BS', '精神类', 'F15.800', '使用其他兴奋剂（包括咖啡因）引起的其他精神和行为障碍', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (589, 'BS', '精神类', 'F15.900x001', '含有咖啡因的兴奋剂所致的精神和行为障碍', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (590, 'BS', '精神类', 'F15.900x002', '咖啡因所致的精神\n和行为障碍', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (591, 'BS', '精神类', 'F15.900x003', '苯丙胺类兴奋剂所致的精神和行为障碍', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (592, 'BS', '精神类', 'F15.900x004', '氯胺酮所致的精神\n和行为障碍', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (593, 'BS', '精神类', 'F16.000', '使用致幻剂急性中毒引起的精神和行为障碍', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (594, 'BS', '精神类', 'F16.000x002', '致幻剂急性中毒', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (595, 'BS', '精神类', 'F16.100', '有害性使用致幻剂引起的精神和行为障碍', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (596, 'BS', '精神类', 'F16.100x002', '致幻剂非成瘾性滥用', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (597, 'BS', '精神类', 'F16.200', '使用致幻剂引起的依赖综合征', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (598, 'BS', '精神类', 'F16.300', '使用致幻剂引起的戒断状态', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (599, 'BS', '精神类', 'F16.400', '使用致幻剂引起的戒断状态伴有谵妄', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (600, 'BS', '精神类', 'F16.400x001', '伴有谵妄的致幻剂戒断状态', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (601, 'BS', '精神类', 'F16.500', '使用致幻剂引起的精神性障碍', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (602, 'BS', '精神类', 'F16.600', '使用致幻剂引起的遗忘综合征', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (603, 'BS', '精神类', 'F16.700', '使用致幻剂引起的残留性和迟发性精神病性障碍', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (604, 'BS', '精神类', 'F16.800', '使用致幻剂质引起的其他精神和行为障碍', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (605, 'BS', '精神类', 'F16.900', '使用致幻剂引起的精神和行为障碍', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (606, 'BS', '精神类', 'F17.000', '使用烟草急性中毒引起的精神和行为障碍', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (607, 'BS', '精神类', 'F17.000x001', '烟草急性中毒', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (608, 'BS', '精神类', 'F17.100', '有害性使用烟草引起的精神和行为障碍', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (609, 'BS', '精神类', 'F17.100x001', '烟草的有害使用', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (610, 'BS', '精神类', 'F17.200', '使用烟草引起的依赖综合征', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (611, 'BS', '精神类', 'F17.300', '使用烟草引起的戒断状态', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (612, 'BS', '精神类', 'F17.400', '使用烟草引起的戒断状态伴有谵妄', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (613, 'BS', '精神类', 'F17.400x001', '伴有谵妄的烟草戒断状态', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (614, 'BS', '精神类', 'F17.500', '使用烟草引起的精神性障碍', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (615, 'BS', '精神类', 'F17.600', '使用烟草引起的遗忘综合征', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (616, 'BS', '精神类', 'F17.700', '使用烟草引起的残留性和迟发性精神病性障碍', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (617, 'BS', '精神类', 'F17.800', '使用烟草质引起的其他精神和行为障碍', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (618, 'BS', '精神类', 'F17.900', '使用烟草引起的精神和行为障碍', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (619, 'BS', '精神类', 'F18.000', '使用挥发性溶剂急性中毒引起的精神和行为障碍', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (620, 'BS', '精神类', 'F18.000x001', '挥发性溶剂急性中毒', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (621, 'BS', '精神类', 'F18.100', '有害性使用挥发性溶剂引起的精神和行为障碍', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (622, 'BS', '精神类', 'F18.100x001', '挥发性溶剂的有害使用', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (623, 'BS', '精神类', 'F18.200', '使用挥发性溶剂引起的依赖综合征', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (624, 'BS', '精神类', 'F18.300', '使用挥发性溶剂引起的戒断状态', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (625, 'BS', '精神类', 'F18.400', '使用挥发性溶剂引起的戒断状态伴有谵妄', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (626, 'BS', '精神类', 'F18.500', '使用挥发性溶剂引起的精神性障碍', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (627, 'BS', '精神类', 'F18.600', '使用挥发性溶剂引起的遗忘综合征', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (628, 'BS', '精神类', 'F18.700', '使用挥发性溶剂引起的残留性和迟发性精神病性障碍', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (629, 'BS', '精神类', 'F18.800', '使用挥发性溶剂引起的其他精神和行为障碍', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (630, 'BS', '精神类', 'F18.900', '使用挥发性溶剂引起的精神和行为障碍', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (631, 'BS', '精神类', 'F19.000', '使用多种药物和其他精神活性物质急性中毒引起的精神和行为障碍', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (632, 'BS', '精神类', 'F19.000x002', '多种药物和其他精神活性物质急性中毒', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (633, 'BS', '精神类', 'F19.100', '有害性使用多种药物和其他精神活性物质引起的精神和行为障碍', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (634, 'BS', '精神类', 'F19.100x004', '多种药物和其他精神活性物质的有害使用', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (635, 'BS', '精神类', 'F19.200', '使用多种药物和其他精神活性物质引起的依赖综合征', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (636, 'BS', '精神类', 'F19.200x001', 'A.P.C药物成瘾', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (637, 'BS', '精神类', 'F19.201', '镇痛药物瘾', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (638, 'BS', '精神类', 'F19.300', '使用多种药物和其他精神活性物质引起的戒断状态', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (639, 'BS', '精神类', 'F19.400', '使用多种药物和其他精神活性物质引起的戒断状态伴有谵妄', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (640, 'BS', '精神类', 'F19.400x001', '伴有谵妄的多种药物和其他精神活性物质戒断状态', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (641, 'BS', '精神类', 'F19.500', '使用多种药物和其他精神活性物质引起的精神性障碍', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (642, 'BS', '精神类', 'F19.600', '使用多种药物和其他精神活性物质引起的遗忘综合征', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (643, 'BS', '精神类', 'F19.700', '使用多种药物和其他精神活性物质引起的残留性和迟发性精神病性障碍', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (644, 'BS', '精神类', 'F19.800', '使用多种药物和其他精神活性物质引起的其他精神和行为障碍', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (645, 'BS', '精神类', 'F19.900', '使用多种药物和其他精神活性物质引起的精神和行为障碍', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (646, 'BS', '精神类', 'F19.900x002', 'A.P.C中毒致精神障碍', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (647, 'BS', '精神类', 'F19.900x003', '阿的平中毒致精神障碍', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (648, 'BS', '精神类', 'F19.900x004', '合霉素中毒致精神障碍', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (649, 'BS', '精神类', 'F19.900x005', '激素类药物致精神障碍', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (650, 'BS', '精神类', 'F19.900x006', '眠尔通中毒致精神障碍', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (651, 'BS', '精神类', 'F19.900x007', '药物源性精神障碍', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (652, 'BS', '精神类', 'F19.900x008', '抗帕金森药物所致精神障碍', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (653, 'BS', '精神类', 'F19.900x009', '利血平所致精神障碍', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (654, 'BS', '精神类', 'F04.x00x001', '脑器质性创伤后遗忘', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (655, 'BS', '精神类', 'F04.x00x901', '器质性遗忘综合征', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (656, 'BS', '精神类', 'F09.x00x003', '器质性精神病', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (657, 'BS', '精神类', 'F09.x00x004', '症状性精神病', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (658, 'BS', '精神类', 'F09.x01', '症状性精神障碍', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (659, 'BS', '精神类', 'F09.x02', '一氧化碳所致精神障碍', '2024', '1');
INSERT INTO `bed_diag_codg_cfg` (`id`, `bed_type`, `bed_name`, `main_diag_codg`, `main_diag_name`, `year`, `vali_flag`)
VALUES (660, 'BS', '精神类', 'F09.x03', '器质性精神障碍', '2024', '1');
COMMIT;

-- 创建床日支付标准表并初始化
DROP TABLE IF EXISTS `drg_bed_standard`;
CREATE TABLE `drg_bed_standard`
(
    `id`            int NOT NULL AUTO_INCREMENT COMMENT 'id',
    `bed_type`      varchar(50) COLLATE utf8mb4_general_ci                        DEFAULT NULL COMMENT '床日类型',
    `bed_dise_codg` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '床日病组编码',
    `bed_dise_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '床日病组名称',
    `medins_lv`     varchar(200) COLLATE utf8mb4_general_ci                       DEFAULT NULL COMMENT '医疗机构级别',
    `pay_std_fee`   decimal(10, 4)                                                DEFAULT NULL COMMENT '支付标准费用',
    `bed_stand_val` decimal(10, 4)                                                DEFAULT NULL COMMENT '床日基准点数',
    `year`          varchar(6) COLLATE utf8mb4_general_ci                         DEFAULT NULL COMMENT '标杆年度',
    `vali_flag`     varchar(6) COLLATE utf8mb4_general_ci                         DEFAULT NULL COMMENT '有效标志',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 15
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci;

-- ----------------------------
-- Records of drg_bed_standard
-- ----------------------------
BEGIN;
INSERT INTO `drg_bed_standard` (`id`, `bed_type`, `bed_dise_codg`, `bed_dise_name`, `medins_lv`, `pay_std_fee`,
                                `bed_stand_val`, `year`, `vali_flag`)
VALUES (1, 'BS', 'BS01', '精神类-三甲', '1', 238.0600, 3.1826, '2024', '1');
INSERT INTO `drg_bed_standard` (`id`, `bed_type`, `bed_dise_codg`, `bed_dise_name`, `medins_lv`, `pay_std_fee`,
                                `bed_stand_val`, `year`, `vali_flag`)
VALUES (2, 'BS', 'BS03', '精神类-三乙', '3', 221.2200, 2.9575, '2024', '1');
INSERT INTO `drg_bed_standard` (`id`, `bed_type`, `bed_dise_codg`, `bed_dise_name`, `medins_lv`, `pay_std_fee`,
                                `bed_stand_val`, `year`, `vali_flag`)
VALUES (3, 'BS', 'BS02', '精神类二甲', '2', 178.8100, 2.3905, '2024', '1');
INSERT INTO `drg_bed_standard` (`id`, `bed_type`, `bed_dise_codg`, `bed_dise_name`, `medins_lv`, `pay_std_fee`,
                                `bed_stand_val`, `year`, `vali_flag`)
VALUES (4, 'BS', 'BS04', '精神类二乙', '4', 114.3600, 1.5289, '2024', '1');
INSERT INTO `drg_bed_standard` (`id`, `bed_type`, `bed_dise_codg`, `bed_dise_name`, `medins_lv`, `pay_std_fee`,
                                `bed_stand_val`, `year`, `vali_flag`)
VALUES (5, 'BR', 'BR01', '康复类-三甲', '1', 518.1300, 6.9269, '2024', '1');
INSERT INTO `drg_bed_standard` (`id`, `bed_type`, `bed_dise_codg`, `bed_dise_name`, `medins_lv`, `pay_std_fee`,
                                `bed_stand_val`, `year`, `vali_flag`)
VALUES (6, 'BR', 'BR03', '康复类-三乙', '3', 366.1300, 4.8948, '2024', '1');
INSERT INTO `drg_bed_standard` (`id`, `bed_type`, `bed_dise_codg`, `bed_dise_name`, `medins_lv`, `pay_std_fee`,
                                `bed_stand_val`, `year`, `vali_flag`)
VALUES (7, 'BR', 'BR02', '康复类-二甲', '2', 308.7200, 4.1273, '2024', '1');
INSERT INTO `drg_bed_standard` (`id`, `bed_type`, `bed_dise_codg`, `bed_dise_name`, `medins_lv`, `pay_std_fee`,
                                `bed_stand_val`, `year`, `vali_flag`)
VALUES (8, 'BR', 'BR04', '康复类-二乙', '4', 229.5500, 3.0689, '2024', '1');
INSERT INTO `drg_bed_standard` (`id`, `bed_type`, `bed_dise_codg`, `bed_dise_name`, `medins_lv`, `pay_std_fee`,
                                `bed_stand_val`, `year`, `vali_flag`)
VALUES (9, 'BR', 'BR07', '康复类-基层', '7', 210.1600, 2.8096, '2024', '1');
INSERT INTO `drg_bed_standard` (`id`, `bed_type`, `bed_dise_codg`, `bed_dise_name`, `medins_lv`, `pay_std_fee`,
                                `bed_stand_val`, `year`, `vali_flag`)
VALUES (10, 'BL', 'BL01', '住院超60日-三甲', '1', 521.0500, 6.9659, '2024', '1');
INSERT INTO `drg_bed_standard` (`id`, `bed_type`, `bed_dise_codg`, `bed_dise_name`, `medins_lv`, `pay_std_fee`,
                                `bed_stand_val`, `year`, `vali_flag`)
VALUES (11, 'BL', 'BL03', '住院超60日-三乙', '3', 484.5300, 6.4777, '2024', '1');
INSERT INTO `drg_bed_standard` (`id`, `bed_type`, `bed_dise_codg`, `bed_dise_name`, `medins_lv`, `pay_std_fee`,
                                `bed_stand_val`, `year`, `vali_flag`)
VALUES (12, 'BL', 'BL02', '住院超60日-二甲', '2', 316.8000, 4.2353, '2024', '1');
INSERT INTO `drg_bed_standard` (`id`, `bed_type`, `bed_dise_codg`, `bed_dise_name`, `medins_lv`, `pay_std_fee`,
                                `bed_stand_val`, `year`, `vali_flag`)
VALUES (13, 'BL', 'BL04', '住院超60日-二乙', '4', 216.6600, 2.8965, '2024', '1');
INSERT INTO `drg_bed_standard` (`id`, `bed_type`, `bed_dise_codg`, `bed_dise_name`, `medins_lv`, `pay_std_fee`,
                                `bed_stand_val`, `year`, `vali_flag`)
VALUES (14, 'BL', 'BL07', '住院超60日-基层', '7', 214.6700, 2.8699, '2024', '1');
COMMIT;

-- 创建规则引擎配置并初始化
DROP TABLE IF EXISTS `som_chain`;
CREATE TABLE `som_chain`
(
    `id`               tinyint                                                       NOT NULL AUTO_INCREMENT COMMENT 'id',
    `application_name` varchar(100) COLLATE utf8mb4_general_ci                            DEFAULT NULL COMMENT '应用名称',
    `chain_name`       varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '调用规则链编码',
    `chain_desc`       varchar(500) COLLATE utf8mb4_general_ci                            DEFAULT NULL COMMENT '调用规则链名称',
    `el_data`          varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '调用规则链表达式',
    `create_time`      timestamp                                                     NULL DEFAULT NULL COMMENT '创建时间',
    `chain_enable`     tinyint                                                            DEFAULT NULL COMMENT '启用标志0无效，1有效',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 5
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci;



-- 支付标准表新增字段
alter table som_drg_standard
    add column is_base_dise varchar(100) DEFAULT NULL COMMENT '是否基层病组';
-- --------update by qingfeng 240724 end ----------
-- ----update by qingfeng 240730 start ------------
-- 新增组织架构字段
alter table som_bas_dept
    add column biz_org_type char(2) COMMENT '业务组织类型' after teamlead_type;
-- 新增码表
INSERT INTO som_sys_code (data_val, labl_name, code_type, dscr, srt, crter, crte_time, updt_psn, updt_time, memo_info,
                          is_del)
VALUES ('1', '医疗集团', 'BIZ_ORG_TYPE', '业务组织类型', 0, NULL, NULL, NULL, NULL, '', 0),
       ('2', '医疗机构', 'BIZ_ORG_TYPE', '业务组织类型', 0, NULL, NULL, NULL, NULL, '', 0),
       ('3', '院区', 'BIZ_ORG_TYPE', '业务组织类型', 0, NULL, NULL, NULL, NULL, '', 0),
       ('4', '科室', 'BIZ_ORG_TYPE', '业务组织类型', 0, NULL, NULL, NULL, NULL, '', 0),
       ('5', '诊疗组', 'BIZ_ORG_TYPE', '业务组织类型', 0, NULL, NULL, NULL, NULL, '', 0);
-- --update by qingfeng 240730 end ------------

-- ----update by qingfeng 240801 start ------------
insert into som_sys_gen_cfg (`key`, value, `type`, description)
values ('MED_SYSTEMS_AUTH', '29', 'AUTH', '医疗集体权限');
INSERT INTO som_sys_code (data_val, labl_name, code_type, dscr, srt, crter, crte_time, updt_psn, updt_time, memo_info,
                          is_del)
VALUES ('6', '医生', 'BIZ_ORG_TYPE', '业务组织类型', 0, NULL, NULL, NULL, NULL, '', 0);
-- ----update by qingfeng 240801 end --------------

-- ----update by qingfeng 240807 start
alter table som_dip_gen_cfg
    add column ym varchar(7) comment '期号' after description;
alter table som_drg_gen_cfg
    add column ym varchar(7) comment '期号' after description;

ALTER TABLE som_dip_gen_cfg
    MODIFY COLUMN id bigint AUTO_INCREMENT;
ALTER TABLE som_drg_gen_cfg
    MODIFY COLUMN id bigint AUTO_INCREMENT;

ALTER TABLE som_dip_sco
    add COLUMN profitloss decimal(10, 4) comment '预测盈亏' after hosp_cof;
ALTER TABLE som_dip_sco
    add COLUMN forecast_fee decimal(10, 4) comment '预测金额' after hosp_cof;
ALTER TABLE som_dip_sco
    add COLUMN sumfee decimal(10, 4) comment '医疗总费用' after hosp_cof;
ALTER TABLE som_dip_sco
    add COLUMN price decimal(10, 4) comment '预测单价' after hosp_cof;

ALTER TABLE som_drg_sco
    add COLUMN profitloss decimal(10, 4) comment '预测盈亏' after dise_type;
ALTER TABLE som_drg_sco
    add COLUMN forecast_fee decimal(10, 4) comment '预测金额' after dise_type;
ALTER TABLE som_drg_sco
    add COLUMN sumfee decimal(10, 4) comment '医疗总费用' after dise_type;
ALTER TABLE som_drg_sco
    add COLUMN price decimal(10, 4) comment '预测单价' after dise_type;

alter table som_dip_sco
    add medcasno varchar(40) comment '病案号';
alter table som_drg_sco
    add medcasno varchar(40) comment '病案号';
alter table som_drg_sco
    drop column a48;
-- ----update by qingfeng 240807 end

-- ----update by qingfeng 240812 end
INSERT INTO som_role_mgt (id, name, memo_info, crter, crte_time, updt_psn, updt_time, is_del)
VALUES (29, 'med_group', '医疗集体权限', NULL, NULL, NULL, NULL, 0);
-- -----update by qingfeng 240812 end

-- ----update by fyz 240812 begin
-- 基卫数据抽取新增表（其余机构无需执行）
CREATE TABLE `som_basic_health_hospital`
(
    `id`           bigint       NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `org_code`     varchar(100) NOT NULL COMMENT '机构编码、接口调用使用此编码',
    `org_name`     varchar(255) NOT NULL COMMENT '机构名称',
    `ver_code`     varchar(100)      DEFAULT NULL COMMENT '验证码',
    `api_url`      varchar(500)      DEFAULT NULL COMMENT '接口地址',
    `created_time` timestamp    NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_time` timestamp    NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    `hospital_id`  varchar(255) NOT NULL,
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci COMMENT ='基卫医院信息表';

INSERT INTO som_basic_health_hospital
    (org_code, org_name, ver_code, api_url, hospital_id)
VALUES ('F2653AE44AA7436AAE7A60CF8DF17862', '梓潼县黎雅镇中心卫生院', '1DE0FEF9AB55B7E3E0630B58580A0129',
        'http://***********:8081/WebService.asmx/HIS_Interface', 'H51072500519');
-- ----update by fyz 240812 end

-- update by qingfeng 240814 start 更新执行器配置
INSERT INTO som_timer_task_time_cfg (cron_expr)
VALUES ('*/10 * * * * ?');
-- update by qingfeng 240814 end


-- update by qingfeng 240817 start
CREATE TABLE `som_chain_script`
(
    `id`               tinyint      NOT NULL AUTO_INCREMENT COMMENT 'id',
    `application_name` varchar(100)      DEFAULT NULL COMMENT '应用名称',
    `script_id`        varchar(100) NOT NULL COMMENT '脚本ID',
    `script_name`      varchar(200) NOT NULL COMMENT '脚本名称',
    `script_data`      text         NOT NULL COMMENT '脚本数据',
    `script_type`      varchar(50)  NOT NULL COMMENT '脚本类型',
    `script_language`  varchar(50)  NOT NULL COMMENT '脚本语言',
    `create_time`      timestamp    NULL DEFAULT NULL COMMENT '创建时间',
    `script_enable`    tinyint           DEFAULT NULL COMMENT '启用标志0无效，1有效',
    PRIMARY KEY (`id`)
);
-- update by qingfeng 240817 end


-- update by qingfeng 240826 start
truncate table som_chain;
INSERT INTO som_chain (application_name,chain_name,chain_desc,el_data,create_time,chain_enable) VALUES
                                                                                                    ('somplatform','GainHospBaseInfoChain','获取医疗机构基础配置及支付标准配置','THEN(HospCostRateNode,MonthForecastPriceNode,GainDrgPayStandardsInfoChain)','2024-07-24 10:17:47',1),
                                                                                                    ('somplatform','GainDrgPayStandardsInfoChain','获取DRG支付标准配置','THEN(DrgPayStandardsInfoNode,DrgBedPayStandardsInfoNode)','2024-07-24 10:25:57',1),
                                                                                                    ('somplatform','GainHospPreGroupGenerateScoreChain','生成支付分值及费用预测-预分组','THEN(CalculateDiseGroupTypeNode,CalculateMedCaseTypeNode,CalculateGeneratePointsNode,GainHospPreGroupForecastCostChain)','2024-07-24 10:25:57',1),
                                                                                                    ('somplatform','GainHospPreGroupForecastCostChain','生成支付费用预测-预分组','THEN(CalculatePreGroupPayForecastNode)','2024-07-24 10:25:57',1),
                                                                                                    ('somplatform','GainHospBusiProcGenerateScoreChain','生成支付分值及费用预测-业务流程','THEN(CalculateMedCaseTypeNode,CalculateGeneratePointsNode,GainHospBusiProcForecastCostChain)','2024-07-24 10:25:57',1),
                                                                                                    ('somplatform','GainHospBusiProcForecastCostChain','生成支付费用预测-业务流程','THEN(CalculateBusiProcPayForecastNode)','2024-07-24 10:25:57',1),
                                                                                                    ('somplatform','GlobalExecSettleValidateChain','结算清单质控总调用链','THEN(GlobalExecSettleValidateBasicChain,GlobalExecSettleValidateDepthChain)','2024-07-24 10:25:57',1),
                                                                                                    ('somplatform','GlobalExecSettleValidateBasicChain','清单字段基础校验调用链','THEN(check_nwb_age_basic,check_nwb_bir_wt_basic)','2024-07-24 10:25:57',1),
                                                                                                    ('somplatform','GlobalExecSettleValidateDepthChain','清单字段内涵校验调用链','THEN(check_nwb_bir_wt_depth)','2024-07-24 10:25:57',1);


truncate table som_chain_script;
INSERT INTO som_chain_script (application_name,script_id,script_name,script_data,script_type,script_language,create_time,script_enable) VALUES
                                                                                                                                            ('somplatform','check_nwb_bir_wt_basic','新生儿出生体重基础质控','import com.my.som.model.dataHandle.SomHiInvyBasInfo;
import com.my.som.settlevalidate.constant.SettleValidateConst;
import com.my.som.settlevalidate.util.SettleValidateUtil;
import com.my.som.settlevalidate.vo.SetlValidBaseBusiVo;
import com.my.som.vo.dataHandle.SettleListHandlerVo;
import com.my.som.vo.dataHandle.SettleListValidateVo;
import com.yomahub.liteflow.script.ScriptExecuteWrap;
import com.yomahub.liteflow.script.body.JaninoCommonScriptBody;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

public class check_nwb_bir_wt_basic implements JaninoCommonScriptBody {
    private static final Logger logger = LoggerFactory.getLogger(check_nwb_bir_wt_basic.class);

    /**
     * 新生儿出生体重-[a18]-基础校验
     * 1、非空时判断是否为整数，不为数字时则提示异常
     * 2、单项数值范围是否在[200,10000]之间
     * 3、多新生儿是否用逗号隔开（隔开后每个判断数字范围）
     *
     * @param wrap
     * @return
     */
    @Override
    public Void body(ScriptExecuteWrap wrap) {
        String main_check_field = "a18";
        String main_check_name = "新生儿出生体重";
        SetlValidBaseBusiVo setlValidBaseBusiVo = (SetlValidBaseBusiVo) wrap.cmp.getFirstContextBean();
        SettleListValidateVo settleListValidateVo = (SettleListValidateVo) wrap.cmp.getRequestData();
        Map<String, String> keysBasicResultMap = setlValidBaseBusiVo.getKeysBasicResultMap();
        SomHiInvyBasInfo somHiInvyBasInfo = settleListValidateVo.getSomHiInvyBasInfo();
        SettleListHandlerVo settleListHandlerVo = new SettleListHandlerVo();
        if (SettleValidateUtil.isEmpty(somHiInvyBasInfo)) {
            settleListHandlerVo.setIndex(SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY);
            settleListHandlerVo.setErrDscr((String) SettleValidateConst.CHECK_TYPE_MAP.get(SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY));
            settleListHandlerVo.setChkStas(SettleValidateConst.VALIDATE_STATE_FAIL);
            settleListHandlerVo.setErrType(SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY);
            setlValidBaseBusiVo.getErrorDetails().add(settleListHandlerVo);
            return null;
        }
        Double a18 = somHiInvyBasInfo.getA18();
        if (!SettleValidateUtil.isEmpty(a18)) {
            //不为空,则一定是一个正常值，判断范围是否在[200,10000]
            if (a18 == 0.0) {
                //处理为null,被默认赋值为0.0数据
                keysBasicResultMap.put(main_check_field, SettleValidateConst.NULL);
                return null;
            }
            if (!(a18 >= 200 && a18 <= 10000)) {
                //不在合理值范围，提示
                settleListHandlerVo.setIndex(SettleValidateConst.VALIDATE_TYPE_1);
                settleListHandlerVo.setSettleListId(somHiInvyBasInfo.getId());
                settleListHandlerVo.setErrorFields(main_check_field);
                settleListHandlerVo.setErrDscr(main_check_name + "[" + a18 + "]超出合理值范围");
                settleListHandlerVo.setChkStas(SettleValidateConst.VALIDATE_STATE_SUCCESS);
                settleListHandlerVo.setErrType(SettleValidateConst.VALIDATE_ERROR_TYPE_2);
                setlValidBaseBusiVo.getErrorDetails().add(settleListHandlerVo);
                //放入字段基础校验
                keysBasicResultMap.put(main_check_field, SettleValidateConst.OUTLIER);
            } else {
                //正常值，基础校验通过不写错误信息
                keysBasicResultMap.put(main_check_field, SettleValidateConst.PASS);
            }
        } else {
            keysBasicResultMap.put(main_check_field, SettleValidateConst.NULL);
        }
        return null;
    }
}','script','java','2024-08-17 15:04:45',1),
                                                                                                                                            ('somplatform','check_nwb_bir_wt_depth','新生儿出生体重内涵质控','import com.my.som.model.dataHandle.SomHiInvyBasInfo;
import com.my.som.settlevalidate.constant.SettleValidateConst;
import com.my.som.settlevalidate.vo.SetlValidBaseBusiVo;
import com.my.som.vo.dataHandle.SettleListHandlerVo;
import com.my.som.vo.dataHandle.SettleListValidateVo;
import com.yomahub.liteflow.script.ScriptExecuteWrap;
import com.yomahub.liteflow.script.body.JaninoCommonScriptBody;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

public class check_nwb_bir_wt_depth implements JaninoCommonScriptBody {
    private static final Logger logger = LoggerFactory.getLogger(check_nwb_bir_wt_depth.class);

    /**
     * 新生儿出生体重-[18]-深度校验
     * 1、新生儿(天龄小于28天nwb_age)入院时，新生儿出生体重必须填写。
     * 2、获取基础质控结果，判断是否需要走校验
     *
     * @param wrap
     * @return
     */
    @Override
    public Void body(ScriptExecuteWrap wrap) {
        String main_check_field = "a18";
        String main_check_name = "新生儿出生体重";
        String tieup_field = "a16";
        SetlValidBaseBusiVo setlValidBaseBusiVo = (SetlValidBaseBusiVo) wrap.cmp.getFirstContextBean();
        SettleListValidateVo settleListValidateVo = (SettleListValidateVo) wrap.cmp.getRequestData();
        Map<String, String> keysBasicResultMap = setlValidBaseBusiVo.getKeysBasicResultMap();
        SomHiInvyBasInfo somHiInvyBasInfo = settleListValidateVo.getSomHiInvyBasInfo();
        SettleListHandlerVo settleListHandlerVo = new SettleListHandlerVo();
        String main_field_baisc_stas = (String) keysBasicResultMap.get(main_check_field);
        //获取关联字段基础校验结果
        String tieup_field_baisc_stas = (String) keysBasicResultMap.get(tieup_field);
        Double a18 = somHiInvyBasInfo.getA18();
        if (SettleValidateConst.NULL.equals(main_field_baisc_stas)) {
            //判断是否逻辑必填
            if (isNewBaby(somHiInvyBasInfo, tieup_field_baisc_stas)) {
                //是新生儿，需要逻辑必填
                settleListHandlerVo.setIndex(SettleValidateConst.VALIDATE_TYPE_3);
                settleListHandlerVo.setSettleListId(somHiInvyBasInfo.getId());
                settleListHandlerVo.setErrorFields(main_check_field);
                settleListHandlerVo.setErrDscr((String) main_check_name + "[" + a18 + "]，患者是新生儿时，新生儿出生体重必填");
                settleListHandlerVo.setChkStas(SettleValidateConst.VALIDATE_STATE_SUCCESS);
                settleListHandlerVo.setErrType(SettleValidateConst.VALIDATE_ERROR_TYPE_303);
                setlValidBaseBusiVo.getErrorDetails().add(settleListHandlerVo);
            }
        } else if (SettleValidateConst.PASS.equals(main_field_baisc_stas)) {
            //正常值，则需要判断是否存在合理的关联基础校验结果
            if (!isNewBaby(somHiInvyBasInfo, tieup_field_baisc_stas)) {
                //不是新生儿，存在逻辑错误
                settleListHandlerVo.setIndex(SettleValidateConst.VALIDATE_TYPE_3);
                settleListHandlerVo.setSettleListId(somHiInvyBasInfo.getId());
                settleListHandlerVo.setErrorFields(main_check_field);
                settleListHandlerVo.setErrDscr(main_check_name + "[" + a18 + "]，新生儿体重存在时，新生儿天龄必填");
                settleListHandlerVo.setChkStas(SettleValidateConst.VALIDATE_STATE_SUCCESS);
                settleListHandlerVo.setErrType(SettleValidateConst.VALIDATE_ERROR_TYPE_303);
                setlValidBaseBusiVo.getErrorDetails().add(settleListHandlerVo);
            }
        }
        return null;
    }

    /**
     * 判断是否为新生儿【基础校验通过且新生儿天数<=28天】
     *
     * @param somHiInvyBasInfo
     * @param tieup_field_baisc_stas
     * @return
     */
    private boolean isNewBaby(SomHiInvyBasInfo somHiInvyBasInfo, String tieup_field_baisc_stas) {
        if (SettleValidateConst.PASS.equals(tieup_field_baisc_stas) && somHiInvyBasInfo.getA16() <= 28) {
            return true;
        }
        return false;
    }
}','script','java','2024-08-17 15:04:45',1),
                                                                                                                                            ('somplatform','check_nwb_age_basic','年龄不足一周岁（天龄）基础质控','import com.my.som.model.dataHandle.SomHiInvyBasInfo;
import com.my.som.settlevalidate.constant.SettleValidateConst;
import com.my.som.settlevalidate.util.SettleValidateUtil;
import com.my.som.settlevalidate.vo.SetlValidBaseBusiVo;
import com.my.som.vo.dataHandle.SettleListHandlerVo;
import com.my.som.vo.dataHandle.SettleListValidateVo;
import com.yomahub.liteflow.script.ScriptExecuteWrap;
import com.yomahub.liteflow.script.body.JaninoCommonScriptBody;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

public class check_nwb_age_basic implements JaninoCommonScriptBody {
    private static final Logger logger = LoggerFactory.getLogger(check_nwb_age_basic.class);

    /**
     * [新生儿天龄-[a16]-基础校验]
     * 1、判断是否为空，非空时判断是否为数字，不为数字时则提示异常
     *
     * @param wrap
     * @return
     */
    @Override
    public Void body(ScriptExecuteWrap wrap) {
        String main_check_field = "a16";
        String main_check_name = "新生儿天龄";
        SetlValidBaseBusiVo setlValidBaseBusiVo = (SetlValidBaseBusiVo) wrap.cmp.getFirstContextBean();
        SettleListValidateVo settleListValidateVo = (SettleListValidateVo) wrap.cmp.getRequestData();
        Map<String, String> keysBasicResultMap = setlValidBaseBusiVo.getKeysBasicResultMap();
        SomHiInvyBasInfo somHiInvyBasInfo = settleListValidateVo.getSomHiInvyBasInfo();
        SettleListHandlerVo settleListHandlerVo = new SettleListHandlerVo();
        if (SettleValidateUtil.isEmpty(somHiInvyBasInfo)) {
            settleListHandlerVo.setIndex(SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY);
            settleListHandlerVo.setErrDscr((String) SettleValidateConst.CHECK_TYPE_MAP.get(SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY));
            settleListHandlerVo.setChkStas(SettleValidateConst.VALIDATE_STATE_FAIL);
            settleListHandlerVo.setErrType(SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY);
            setlValidBaseBusiVo.getErrorDetails().add(settleListHandlerVo);
            return null;
        }
        Integer a16 = somHiInvyBasInfo.getA16();
        if (!SettleValidateUtil.isEmpty(a16)) {
            //不为空,则一定是一个正常值，判断范围是否在[200,10000]
            if (!(a16 >= 1 && a16 <= 365)) {
                //不在合理值范围，提示
                settleListHandlerVo.setIndex(SettleValidateConst.VALIDATE_TYPE_1);
                settleListHandlerVo.setSettleListId(somHiInvyBasInfo.getId());
                settleListHandlerVo.setErrorFields(main_check_field);
                settleListHandlerVo.setErrDscr(main_check_name + "[" + a16 + "]超出合理值范围");
                settleListHandlerVo.setChkStas(SettleValidateConst.VALIDATE_STATE_SUCCESS);
                settleListHandlerVo.setErrType(SettleValidateConst.VALIDATE_ERROR_TYPE_2);
                setlValidBaseBusiVo.getErrorDetails().add(settleListHandlerVo);
                //放入字段基础校验
                keysBasicResultMap.put(main_check_field, SettleValidateConst.OUTLIER);
            }else {
                //正常值，基础校验通过
                keysBasicResultMap.put(main_check_field, SettleValidateConst.PASS);
            }
        } else {
            keysBasicResultMap.put(main_check_field, SettleValidateConst.NULL);
        }
        return null;
    }
}','script','java','2024-08-17 15:04:45',1),
                                                                                                                                            ('somplatform','check_nwb_adm_wt_basic','新生儿入院体重基础质控','import com.my.som.model.dataHandle.SomHiInvyBasInfo;
import com.my.som.settlevalidate.constant.SettleValidateConst;
import com.my.som.settlevalidate.util.SettleValidateUtil;
import com.my.som.settlevalidate.vo.SetlValidBaseBusiVo;
import com.my.som.vo.dataHandle.SettleListHandlerVo;
import com.my.som.vo.dataHandle.SettleListValidateVo;
import com.yomahub.liteflow.script.ScriptExecuteWrap;
import com.yomahub.liteflow.script.body.JaninoCommonScriptBody;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

public class check_nwb_adm_wt_basic implements JaninoCommonScriptBody {
    private static final Logger logger = LoggerFactory.getLogger(check_nwb_adm_wt_basic.class);

    /**
     * 新生儿出生体重-[a17]-基础校验
     * 1、当新生儿入院体重为空时，不用判断新生儿入院体重项下的规则；当新生儿入院体重不为空时，判断新生儿入院体重是否为阿拉伯数字，若否则不通过。
     * 2、审核新生儿入院体重范围是否在[200,20000]内，若否则不通过。
     *
     * @param wrap
     * @return
     */
    @Override
    public Void body(ScriptExecuteWrap wrap) {
        String main_check_field = "a17";
        String main_check_name = "新生儿入院体重";
        SetlValidBaseBusiVo setlValidBaseBusiVo = (SetlValidBaseBusiVo) wrap.cmp.getFirstContextBean();
        SettleListValidateVo settleListValidateVo = (SettleListValidateVo) wrap.cmp.getRequestData();
        Map<String, String> keysBasicResultMap = setlValidBaseBusiVo.getKeysBasicResultMap();
        SomHiInvyBasInfo somHiInvyBasInfo = settleListValidateVo.getSomHiInvyBasInfo();
        SettleListHandlerVo settleListHandlerVo = new SettleListHandlerVo();
        if (SettleValidateUtil.isEmpty(somHiInvyBasInfo)) {
            settleListHandlerVo.setIndex(SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY);
            settleListHandlerVo.setErrDscr((String) SettleValidateConst.CHECK_TYPE_MAP.get(SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY));
            settleListHandlerVo.setChkStas(SettleValidateConst.VALIDATE_STATE_FAIL);
            settleListHandlerVo.setErrType(SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY);
            setlValidBaseBusiVo.getErrorDetails().add(settleListHandlerVo);
            return null;
        }
        Double a17 = somHiInvyBasInfo.getA17();
        if (!SettleValidateUtil.isEmpty(a17)) {
            //不为空,则一定是一个正常值，判断范围是否在[200,20000]
            if (a17 == 0.0) {
                //处理为null,被默认赋值为0.0数据
                keysBasicResultMap.put(main_check_field, SettleValidateConst.NULL);
                return null;
            }
            if (!(a17 >= 200 && a17 <= 20000)) {
                //不在合理值范围，提示
                settleListHandlerVo.setIndex(SettleValidateConst.VALIDATE_TYPE_1);
                settleListHandlerVo.setSettleListId(somHiInvyBasInfo.getId());
                settleListHandlerVo.setErrorFields(main_check_field);
                settleListHandlerVo.setErrDscr(main_check_name + "[" + a17 + "]超出合理值范围");
                settleListHandlerVo.setChkStas(SettleValidateConst.VALIDATE_STATE_SUCCESS);
                settleListHandlerVo.setErrType(SettleValidateConst.VALIDATE_ERROR_TYPE_2);
                setlValidBaseBusiVo.getErrorDetails().add(settleListHandlerVo);
                //放入字段基础校验
                keysBasicResultMap.put(main_check_field, SettleValidateConst.OUTLIER);
            } else {
                //正常值，基础校验通过不写错误信息
                keysBasicResultMap.put(main_check_field, SettleValidateConst.PASS);
            }
        } else {
            keysBasicResultMap.put(main_check_field, SettleValidateConst.NULL);
        }
        return null;
    }
}
','script','java','2024-08-17 15:04:45',1),
                                                                                                                                            ('somplatform','check_nwb_adm_wt_depth','新生儿出生体重内涵质控','import com.my.som.model.dataHandle.SomHiInvyBasInfo;
import com.my.som.settlevalidate.constant.SettleValidateConst;
import com.my.som.settlevalidate.vo.SetlValidBaseBusiVo;
import com.my.som.vo.dataHandle.SettleListHandlerVo;
import com.my.som.vo.dataHandle.SettleListValidateVo;
import com.yomahub.liteflow.script.ScriptExecuteWrap;
import com.yomahub.liteflow.script.body.JaninoCommonScriptBody;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

public class check_nwb_adm_wt_depth implements JaninoCommonScriptBody {
    private static final Logger logger = LoggerFactory.getLogger(check_nwb_adm_wt_depth.class);

    /**
     * 新生儿入院体重-[17]-深度校验
     * 1、新生儿(天龄小于28天)入院时，新生儿入院体重必须填写
     * 2、获取基础质控结果，判断是否需要走校验
     *
     * @param wrap
     * @return
     */
    @Override
    public Void body(ScriptExecuteWrap wrap) {
        String main_check_field = "a17";
        String main_check_name = "新生儿入院体重";
        String tieup_field = "a16";
        String tieup_field_name = "年龄不足1周岁的年龄";
        SetlValidBaseBusiVo setlValidBaseBusiVo = (SetlValidBaseBusiVo) wrap.cmp.getFirstContextBean();
        SettleListValidateVo settleListValidateVo = (SettleListValidateVo) wrap.cmp.getRequestData();
        Map<String, String> keysBasicResultMap = setlValidBaseBusiVo.getKeysBasicResultMap();
        SomHiInvyBasInfo somHiInvyBasInfo = settleListValidateVo.getSomHiInvyBasInfo();
        SettleListHandlerVo settleListHandlerVo = new SettleListHandlerVo();
        String main_field_baisc_stas = (String) keysBasicResultMap.get(main_check_field);
        //获取关联字段基础校验结果
        String tieup_field_baisc_stas = (String) keysBasicResultMap.get(tieup_field);
        Double a17 = somHiInvyBasInfo.getA17();
        if (SettleValidateConst.NULL.equals(main_field_baisc_stas)) {
            //判断是否逻辑必填
            if (isNewBaby(somHiInvyBasInfo, tieup_field_baisc_stas)) {
                //是新生儿，需要逻辑必填
                settleListHandlerVo.setIndex(SettleValidateConst.VALIDATE_TYPE_3);
                settleListHandlerVo.setSettleListId(somHiInvyBasInfo.getId());
                settleListHandlerVo.setErrorFields(main_check_field);
                settleListHandlerVo.setErrDscr((String) main_check_name + "[" + a17 + "]，患者是新生儿时，" + main_check_name + "必填");
                settleListHandlerVo.setChkStas(SettleValidateConst.VALIDATE_STATE_SUCCESS);
                settleListHandlerVo.setErrType(SettleValidateConst.VALIDATE_ERROR_TYPE_303);
                setlValidBaseBusiVo.getErrorDetails().add(settleListHandlerVo);
            }
        } else if (SettleValidateConst.PASS.equals(main_field_baisc_stas)) {
            //正常值，则需要判断是否存在合理的关联基础校验结果
            if (!isNewBaby(somHiInvyBasInfo, tieup_field_baisc_stas)) {
                //不是新生儿，存在逻辑错误
                settleListHandlerVo.setIndex(SettleValidateConst.VALIDATE_TYPE_3);
                settleListHandlerVo.setSettleListId(somHiInvyBasInfo.getId());
                settleListHandlerVo.setErrorFields(main_check_field);
                settleListHandlerVo.setErrDscr(main_check_name + "[" + a17 + "]，" + main_check_name + "存在时，" + tieup_field_name + "必填");
                settleListHandlerVo.setChkStas(SettleValidateConst.VALIDATE_STATE_SUCCESS);
                settleListHandlerVo.setErrType(SettleValidateConst.VALIDATE_ERROR_TYPE_303);
                setlValidBaseBusiVo.getErrorDetails().add(settleListHandlerVo);
            }
        }
        return null;
    }

    /**
     * 判断是否为新生儿【基础校验通过且新生儿天数<=28天】
     *
     * @param somHiInvyBasInfo
     * @param tieup_field_baisc_stas
     * @return
     */
    private boolean isNewBaby(SomHiInvyBasInfo somHiInvyBasInfo, String tieup_field_baisc_stas) {
        if (SettleValidateConst.PASS.equals(tieup_field_baisc_stas) && somHiInvyBasInfo.getA16() <= 28) {
            return true;
        }
        return false;
    }
}
','script','java','2024-08-17 15:04:45',1);

-- update by qingfeng 240826 end

-- update by lihongxiang 240812 end
-- ---清单校验规则 病案首页规则 还原按钮 关联的som_medcas_in_group_rule_cfg 表结构修改
ALTER TABLE som_medcas_in_group_rule_cfg_bac add COLUMN invy_conv varchar(255) comment 'CONVERTER' after enab_flag;
ALTER TABLE som_medcas_in_group_rule_cfg_bac add COLUMN skip_chk_flag varchar(10) comment '跳过校验标识' after enab_flag;
ALTER TABLE som_medcas_in_group_rule_cfg_bac add COLUMN converter varchar(50) comment '清单转换' after enab_flag;
-- ---update by lihongxiang 240812 end

-- ---update by fyz begin 20240828
-- ---清单诊断表添加主要诊断标识字段
ALTER TABLE som_diag
    ADD COLUMN maindiag_flag VARCHAR(2) COMMENT '主要诊断标识';
-- update by fyz end 20240828

-- update by fyz 20240829
-- 新增合并编码校验表
DROP TABLE IF EXISTS `som_comb_code_chk`;
CREATE TABLE `som_comb_code_chk` (
                                     `id` int NOT NULL AUTO_INCREMENT,
                                     `combined_codes` varchar(255) NOT NULL COMMENT '可合并编码，多个编码使用分号（;）分隔',
                                     `generated_code` varchar(20) DEFAULT NULL COMMENT '新生成的合并编码',
                                     `description` varchar(255) DEFAULT NULL COMMENT '编码组合的规则和含义',
                                     `is_type` varchar(2) DEFAULT NULL COMMENT '1.诊断；2.手术',
                                     `is_active` tinyint(1) DEFAULT '1' COMMENT '是否启用',
                                     `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
                                     `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                                     PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=201 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='联合编码校验';

INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (1, 'S83.201;S83.500x003;', 'S83.700x005', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (2, 'S83.201;S83.400x003;', 'S83.700x004', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (3, 'S83.202;S83.500x003;', 'S83.700x002', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (4, 'S83.202;S83.400x003;', 'S83.700x001', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (5, 'S82.400x001;S82.800x082;', 'S82.600x001', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (6, 'S82.400x001;S82.600;', 'S82.600x001', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (7, 'S82.202;S82.800x082;', 'S82.500x001', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (8, 'S82.202;S82.500;', 'S82.500x001', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (9, 'S82.211;S82.800x082;', 'S82.500x001', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (10, 'S82.202;S82.800x081;', 'S82.500x001', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (11, 'S82.300x081;S82.400x001;', 'S82.300x011', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (12, 'S82.200x081;S82.400x001;', 'S82.200x011', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (13, 'S82.100x087;S82.100x085;', 'S82.100x088', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (14, 'S82.100x087;S82.400x001;', 'S82.100x012', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (15, 'S82.100x081;S82.400x001;', 'S82.100x011', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (16, 'S61.900x002;S62.101;', 'S61.800x013', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (17, 'S61.901;S62.301;', 'S61.800x012', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (18, 'S61.901;S62.311;', 'S61.800x012', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (19, 'S52.802;S52.500x001;', 'S52.600x002', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (20, 'Z33.x00x001;K29.700;', 'O99.604', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (21, 'Z32.100;J20.900;', 'O99.503', NULL, '1', 1, '2024-08-29 07:04:32', '2024-08-29 07:04:32');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (22, 'Z32.100;E03.900;', 'O99.215', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (23, 'Z33.x00x001;D64.901;', 'O99.005', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (24, 'Z33.x00x001;A09.901;', 'O98.800x035', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (25, 'Z32.100;A03.900;', 'O98.800x007', NULL, '1', 1, '2024-08-29 07:04:32', '2024-08-29 07:04:32');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (26, 'Z33.x00x001;K76.000;', 'O26.605', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (27, 'Z34.800;K76.000;', 'O26.605', NULL, '1', 1, '2024-08-29 07:04:32', '2024-08-29 07:04:32');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (28, 'Z33.x00x001;E14.900x001;', 'O24.300x001', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (29, 'Z33.x00x001;E10.900;', 'O24.000x021', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (30, 'Z33.x00x001;N75.100;', 'O23.500x009', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (31, 'Z33.x00x001;N39.000;', 'O23.400', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (32, 'Z33.x00x001;N10.x02;', 'O23.001', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (33, 'O21.900;E87.801;', 'O21.100', NULL, '1', 1, '2024-08-29 07:04:32', '2024-08-29 07:04:32');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (34, 'N70.900x007;N70.902;', 'N70.905', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (35, 'N13.504;N28.834;', 'N13.605', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (36, 'N13.203;N10.x02;', 'N13.603', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (37, 'N13.203;N28.834;', 'N13.603', NULL, '1', 1, '2024-08-29 07:42:49', '2024-08-29 07:42:49');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (38, 'N13.201;N12.x02;', 'N13.601', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (39, 'N13.201;N10.x02;', 'N13.601', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (40, 'N13.301;N20.100;', 'N13.202', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (41, 'N13.301;N20.000;', 'N13.201', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (42, 'N13.301;N13.504;', 'N13.100x001', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (43, 'N13.301;N13.501;', 'N13.000', NULL, '1', 1, '2024-08-29 07:42:49', '2024-08-29 07:42:49');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (44, 'M54.502;M54.300;', 'M54.400', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (45, 'M51.202;M54.300;', 'M51.101+G55.1*', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (46, 'M34.900x001;J84.101;', 'M34.801+J99.1*', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (47, 'M06.900;J84.101;', 'M05.102+J99.0*', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (48, 'C22.900;K76.803;', 'K92.800x006', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (49, 'C16.900;K92.201;', 'K92.800x002', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (50, 'C15.900;K22.804;', 'K92.800x001', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (51, 'K80.501;K81.100;', 'K80.404', NULL, '1', 1, '2024-08-29 07:42:49', '2024-08-29 07:42:49');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (52, 'K80.500x002;K81.100;', 'K80.403', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (53, 'K80.501;K81.000;', 'K80.402', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (54, 'K80.500x002;K81.000;', 'K80.401', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (55, 'K80.503;K81.100;', 'K80.400x004', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (56, 'K80.503;K81.900;', 'K80.400', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (57, 'K80.503;K81.000;', 'K80.400', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (58, 'K80.501;K83.001;', 'K80.304', NULL, '1', 1, '2024-08-29 07:42:49', '2024-08-29 07:42:49');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (59, 'K80.501;K83.000;', 'K80.302', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (60, 'K80.501;K83.000x007;', 'K80.301', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (61, 'K80.500x002;K83.000;', 'K80.300x002', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (62, 'K80.200x003;K81.100;', 'K80.101', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (63, 'K80.203;K81.100;', 'K80.101', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (64, 'K80.200x003;K81.900;', 'K80.100x001', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (65, 'K80.200x003;K81.900;', 'K80.100x001', NULL, '1', 1, '2024-08-29 07:42:49', '2024-08-29 07:42:49');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (66, 'K80.203;K81.002;', 'K80.002', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (67, 'K80.201;K81.003;', 'K80.001', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (68, 'K80.200x003;K81.003;', 'K80.001', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (69, 'K80.203;K81.003;', 'K80.001', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (70, 'K80.200x003;K81.006;', 'K80.000x004', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (71, 'K80.203;K81.006;', 'K80.000x004', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (72, 'K80.200x003;K81.006;', 'K80.000x004', NULL, '1', 1, '2024-08-29 07:42:49', '2024-08-29 07:42:49');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (73, 'K80.200x003;K81.000;', 'K80.000x002', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (74, 'K80.203;K81.000;', 'K80.000x002', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (75, 'K74.100;I86.400x001;', 'K74.620+I98.2*', NULL, '1', 1, '2024-08-29 07:42:49', '2024-08-29 07:42:49');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (76, 'K74.100;I85.900x001;', 'K74.616+I98.2*', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (77, 'K70.300;I86.800x014;', 'K70.306+I98.3*', NULL, '1', 1, '2024-08-29 07:42:49', '2024-08-29 07:42:49');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (78, 'K70.300;I85.000x001;', 'K70.302+I98.3*', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (79, 'K70.300;I85.900x001;', 'K70.301+I98.2*', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (80, 'K57.303;K65.003;', 'K57.202', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (81, 'K56.700;K55.004;', 'K56.603', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (82, 'K55.902;K92.204;', 'K55.900x004', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (83, 'K46.900x002;K56.700;', 'K46.000x002', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (84, 'K40.901;K40.902;', 'K40.900x005', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (85, 'K35.800x001;K65.901;', 'K35.300', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (86, 'K35.800x001;K65.003;', 'K35.200', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (87, 'K26.900x002;K92.207;K31.800x806', 'K26.200x002', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (88, 'K26.900x002;K92.208;K27.503', 'K26.200x002', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (89, 'K26.900x001;K92.208;K31.800x806', 'K26.200x001', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (90, 'K26.900x001;K92.207;K31.800x806', 'K26.200x001', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (91, 'K26.900x001;K92.203;K31.800x806', 'K26.200x001', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (92, 'K25.900x001;K31.814;K92.201', 'K25.200x001', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (93, 'K31.904;K92.208;', 'K25.000x002', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (94, 'R09.100;J94.804;', 'J90.x00x003', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (95, 'J47.x00;R04.200;', 'J47.x01', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (96, 'J30.400;J45.005;', 'J45.004', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (97, 'J30.400;J46.x01;', 'J45.004', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (98, 'J30.400;J45.900;', 'J45.004', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (99, 'J42.x00;J43.900;', 'J44.801', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (100, 'J44.802;J43.900;', 'J44.801', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (101, 'J44.900;J18.900;', 'J44.000', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (102, 'J44.900;J15.902;', 'J44.000', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (103, 'J44.900;J15.900;', 'J44.000', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (104, 'J44.900x004;J18.900;', 'J44.000', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (105, 'J44.900;J18.803;', 'J44.000', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (106, 'J44.900x005;J18.900;', 'J44.000', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (107, 'J44.900x004;J15.902;', 'J44.000', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (108, 'J44.900x004;J15.903;', 'J44.000', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (109, 'J44.900x003;J15.902;', 'J44.000', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (110, 'J44.900x005;J15.903;', 'J44.000', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (111, 'J44.900x005;J15.902;', 'J44.000', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (112, 'J44.900x003;J18.900;', 'J44.000', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (113, 'J44.900x005;J18.001;', 'J44.000', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (114, 'J44.900x004;J18.803;', 'J44.000', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (115, 'J44.900;J15.903;', 'J44.000', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (116, 'J44.900;J20.900;', 'J44.000', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (117, 'J44.900x002;J18.900;', 'J44.000', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (118, 'J44.900x005;J18.803;', 'J44.000', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (119, 'J44.900x004;J18.002;', 'J44.000', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (120, 'J44.900x004;J18.000;', 'J44.000', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (121, 'J44.900x005;J18.000;', 'J44.000', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (122, 'J44.900x003;J18.000;', 'J44.000', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (123, 'J44.900x004;J15.900;', 'J44.000', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (124, 'J44.900x003;J18.002;', 'J44.000', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (125, 'J44.900x002;J15.902;', 'J44.000', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (126, 'J44.900x004;J18.001;', 'J44.000', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (127, 'J44.900x003;J15.903;', 'J44.000', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (128, 'J44.900x003;J18.803;', 'J44.000', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (129, 'J44.900;J18.800x001;', 'J44.000', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (130, 'J44.900x004;J18.200;', 'J44.000', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (131, 'J44.900x002;J20.900;', 'J44.000', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (132, 'J44.900;J15.902;', 'J44.000', NULL, '1', 1, '2024-08-29 07:42:49', '2024-08-29 07:42:49');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (133, 'J44.900;J20.900;', 'J44.000', NULL, '1', 1, '2024-08-29 07:42:49', '2024-08-29 07:42:49');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (134, 'J44.900;J18.900;', 'J44.000', NULL, '1', 1, '2024-08-29 07:42:49', '2024-08-29 07:42:49');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (135, 'J35.100;J35.200;', 'J35.300', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (136, 'B34.101;J20.900;', 'J20.300', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (137, 'I83.900x004;I80.301;', 'I83.101', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (138, 'I65.001;I63.900;', 'I63.208', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (139, 'I65.002;I63.900;', 'I63.207', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (140, 'I65.102;I63.900;', 'I63.206', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (141, 'I65.200x015;I63.900;', 'I63.204', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (142, 'I65.200x001;I63.900;', 'I63.203', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (143, 'I65.202;I63.900;', 'I63.202', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (144, 'I65.201;I63.900;', 'I63.201', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (145, 'I50.100;J81.x00;', 'I50.103', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (146, 'I50.100x006;J81.x00;', 'I50.103', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (147, 'I35.000;I35.100;', 'I35.200', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (148, 'I11.901;I50.000;', 'I11.002', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (149, 'I11.901;I50.908;', 'I11.002', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (150, 'I11.901;I50.900;', 'I11.002', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (151, 'I11.901;I50.907;', 'I11.002', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (152, 'I34.000;I35.100;I07.100', 'I08.303', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (153, 'I35.100;I07.100;', 'I08.201', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (154, 'I34.000;I07.100;', 'I08.103', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (155, 'I05.000;I07.100;', 'I08.101', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (156, 'I34.000;I35.000;', 'I08.002', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (157, 'I05.000;I35.100;', 'I08.001', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (158, 'I05.000;I34.000;', 'I05.200', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (159, 'H02.003;H02.004;', 'H02.000', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (160, 'G45.801;I77.102;', 'G45.800x004', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (161, 'F10.300;F05.900;', 'F10.400', NULL, '1', 1, '2024-08-29 07:42:49', '2024-08-29 07:42:49');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (162, 'E16.800x901;I10.x05;', 'E16.800x102', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (163, 'E16.800x901;I10.x00x002;', 'E16.800x102', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (164, 'E16.800x901;I10.x04;', 'E16.800x102', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (165, 'E16.800x901;I10.x03;', 'E16.800x102', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (166, 'E05.900x001;E05.003;', 'E05.001', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (167, 'C17.900;C18.600;', 'C26.800x001', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (168, 'C17.900;C18.900;', 'C26.800x001', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (169, 'B65.900x010+K77.0*;K74.100;', 'B65.202+K77.0*', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (170, 'B49.x00x007;J18.903;', 'B49.x14+J99.8*', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (171, 'B49.x00x007;J18.900;', 'B49.x14+J99.8*', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (172, 'B37.901;N76.000x001;', 'B37.301+N77.1*', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (173, 'B37.900;N76.000x001;', 'B37.301+N77.1*', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (174, 'B24.x01;B77.900;', 'B20.901', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (175, 'B24.x01;B48.501+J17.2*;', 'B20.600x001', NULL, '1', 1, '2024-08-29 07:04:32', '2024-08-29 07:04:32');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (176, 'B24.x01;B02.202+G53.0*;', 'B20.301', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (177, 'B24.x01;A16.200x015;', 'B20.003', NULL, '1', 1, '2024-08-29 07:04:32', '2024-08-29 07:04:32');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (178, 'B24.x01;A16.207;', 'B20.003', NULL, '1', 1, '2024-08-29 07:04:32', '2024-08-29 07:04:32');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (179, 'B02.700;H10.900;', 'B02.304+H13.1*', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (180, 'B02.700;H16.900;', 'B02.302+H19.2*', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (181, 'B02.700;M79.207;', 'B02.202+G53.0*', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (182, 'B02.900x002;M79.207;', 'B02.202+G53.0*', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (183, 'B00.902;A41.900;', 'B00.701', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (184, 'B00.902;H16.900;', 'B00.501+H19.1*', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (185, 'A54.900x001;N73.101;', 'A54.201+N74.3*', NULL, '1', 1, '2024-08-29 07:42:49', '2024-08-29 07:42:49');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (186, 'A53.900;K65.900;', 'A52.710+K67.2*', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (187, 'A53.900;K74.100;', 'A52.705+K77.0*', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (188, 'A18.106+N29.1*;N13.504;', 'A18.107+N29.1*', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (189, 'A02.003;J18.900;', 'A02.201+J17.0*', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (190, '80.2000;80.7200;', '80.7201', NULL, '2', 1, '2024-08-29 07:42:49', '2024-08-29 07:42:49');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (191, '80.2600;80.6x03;', '80.6x06', NULL, '2', 1, '2024-08-29 07:42:49', '2024-08-29 07:42:49');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (192, '80.2000;80.6x03;', '80.6x06', NULL, '2', 1, '2024-08-29 07:42:49', '2024-08-29 07:42:49');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (193, '70.5101;70.5201;', '70.5001', NULL, '2', 1, '2024-08-29 07:42:49', '2024-08-29 07:42:49');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (194, '54.2100;70.1201;', '70.1202', NULL, '2', 1, '2024-08-29 07:04:32', '2024-08-29 07:04:32');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (195, '54.2100;68.0x00x005;', '68.0x00x006', NULL, '2', 1, '2024-08-29 07:04:32', '2024-08-29 09:24:24');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (196, '48.3501;45.2302;', '48.3600x002', NULL, '2', 1, '2024-08-29 07:42:49', '2024-08-29 07:42:49');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (197, '47.0901;54.2100;', '47.0100', NULL, '2', 1, '2024-08-29 07:04:32', '2024-08-29 07:04:32');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (198, '38.5902;38.5903;', '38.5901', NULL, '2', 1, '2024-08-29 07:42:49', '2024-08-29 07:42:49');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (199, '28.2x00x002;28.6x00x002;', '28.3x01', NULL, '2', 1, '2024-08-29 07:42:49', '2024-08-29 07:42:49');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (200, '06.2x00;6.3905;', '06.2x02', NULL, '2', 1, '2024-08-29 07:42:49', '2024-08-29 07:42:49');
-- update by fyz 20240829 end

-- update by fyz 20240831 begin
-- DIP支付链路
GainHospPreGroupGenerateScoreChain : THEN(CaseInfoGetterComponent4203,DiseaseTypeJudgmentComponent4203,MultiplierJudgmentComponent4203,ScoreCalcComponent4203,GainHospPreGroupForecastCostChain,PaymentResultComponent)
GainHospPreGroupForecastCostChain: THEN(ForecastedGainDeficitComputingComponent)
GainHospBusiProcGenerateScoreChain: THEN(DiseaseTypeJudgmentComponent4203,MultiplierJudgmentComponent4203,ScoreCalcComponent4203,GainHospBusiProcForecastCostChain)
GainHospBusiProcForecastCostChain:  THEN(ForecastedGainDeficitCompProcessComponent)
-- update by fyz 20240831 end

-- 在字典表中 加上入院途径
INSERT INTO `som_sys_dic` (`id`, `data_val`, `labl_name`, `code_type`, `dscr`, `srt`, `crter`, `crte_time`, `updt_psn`, `updt_time`, `memo_info`, `is_del`) VALUES (null, '1', '急诊', 'RYTJ', '入院途径', 255, NULL, NULL, NULL, NULL, NULL, 0);
INSERT INTO `som_sys_dic` (`id`, `data_val`, `labl_name`, `code_type`, `dscr`, `srt`, `crter`, `crte_time`, `updt_psn`, `updt_time`, `memo_info`, `is_del`) VALUES (null, '2', '门诊', 'RYTJ', '入院途径', 255, NULL, NULL, NULL, NULL, NULL, 0);
INSERT INTO `som_sys_dic` (`id`, `data_val`, `labl_name`, `code_type`, `dscr`, `srt`, `crter`, `crte_time`, `updt_psn`, `updt_time`, `memo_info`, `is_del`) VALUES (null, '3', '其他医疗机构转入', 'RYTJ', '入院途径', 255, NULL, NULL, NULL, NULL, NULL, 0);
INSERT INTO `som_sys_dic` (`id`, `data_val`, `labl_name`, `code_type`, `dscr`, `srt`, `crter`, `crte_time`, `updt_psn`, `updt_time`, `memo_info`, `is_del`) VALUES (null, '9', '其他', 'RYTJ', '入院途径', 255, NULL, NULL, NULL, NULL, NULL, 0);


-- 先隔离出 新增字段的id
update som_sys_dic set id=id+100 where id >723;
-- 字典表中加入 综合(ICU)       重症监护病房类型
INSERT INTO `som_sys_dic` (`id`, `data_val`, `labl_name`, `code_type`, `dscr`, `srt`, `crter`, `crte_time`, `updt_psn`, `updt_time`, `memo_info`, `is_del`) VALUES (724, '7', '综合(ICU)', 'ZZJHBFLX', '重症监护病房类型', 255, NULL, NULL, NULL, NULL, NULL, 0);
-- 顺序重排
update som_sys_dic set id=id-99 where id >724;

-- 修改患者关系
UPDATE som_sys_dic SET  data_val = '01' WHERE labl_name = "本人";
UPDATE som_sys_dic SET  data_val = '02' WHERE labl_name = "户主";
-- 添加 离院方式为5 的主要诊断编码
INSERT INTO `som_sys_dic` (`id`, `data_val`, `labl_name`, `code_type`, `dscr`, `srt`, `crter`, `crte_time`, `updt_psn`, `updt_time`, `memo_info`, `is_del`) VALUES (NULL, 'I46.100x001', '心源性猝死', 'SWZDBM', '关于死亡诊断编码', 255, NULL, NULL, NULL, NULL, '当出现此主要诊断编码时，离院方式应当为5死亡，否则，校验不通过。', 0);
INSERT INTO `som_sys_dic` (`id`, `data_val`, `labl_name`, `code_type`, `dscr`, `srt`, `crter`, `crte_time`, `updt_psn`, `updt_time`, `memo_info`, `is_del`) VALUES (NULL, 'O96.000', '直接产科原因的死亡，发生于分娩后 42天以上一年以内', 'SWZDBM', '关于死亡诊断编码', 255, NULL, NULL, NULL, NULL, '当出现此主要诊断编码时，离院方式应当为5死亡，否则，校验不通过。', 0);
INSERT INTO `som_sys_dic` (`id`, `data_val`, `labl_name`, `code_type`, `dscr`, `srt`, `crter`, `crte_time`, `updt_psn`, `updt_time`, `memo_info`, `is_del`) VALUES (NULL, 'O96.100', '间接产科原因的死亡，发生于分娩后 42天以上一年以内', 'SWZDBM', '关于死亡诊断编码', 255, NULL, NULL, NULL, NULL, '当出现此主要诊断编码时，离院方式应当为5死亡，否则，校验不通过。', 0);
INSERT INTO `som_sys_dic` (`id`, `data_val`, `labl_name`, `code_type`, `dscr`, `srt`, `crter`, `crte_time`, `updt_psn`, `updt_time`, `memo_info`, `is_del`) VALUES (NULL, 'O96.900', '未特指产科原因的死亡，发生于分娩后42天以上一年以内', 'SWZDBM', '关于死亡诊断编码', 255, NULL, NULL, NULL, NULL, '当出现此主要诊断编码时，离院方式应当为5死亡，否则，校验不通过。', 0);
INSERT INTO `som_sys_dic` (`id`, `data_val`, `labl_name`, `code_type`, `dscr`, `srt`, `crter`, `crte_time`, `updt_psn`, `updt_time`, `memo_info`, `is_del`) VALUES (NULL, 'O97.000', '直接产科原因后遗症的死亡', 'SWZDBM', '关于死亡诊断编码', 255, NULL, NULL, NULL, NULL, '当出现此主要诊断编码时，离院方式应当为5死亡，否则，校验不通过。', 0);
INSERT INTO `som_sys_dic` (`id`, `data_val`, `labl_name`, `code_type`, `dscr`, `srt`, `crter`, `crte_time`, `updt_psn`, `updt_time`, `memo_info`, `is_del`) VALUES (NULL, 'O97.100', '间接产科原因后遗症的死亡', 'SWZDBM', '关于死亡诊断编码', 255, NULL, NULL, NULL, NULL, '当出现此主要诊断编码时，离院方式应当为5死亡，否则，校验不通过。', 0);
INSERT INTO `som_sys_dic` (`id`, `data_val`, `labl_name`, `code_type`, `dscr`, `srt`, `crter`, `crte_time`, `updt_psn`, `updt_time`, `memo_info`, `is_del`) VALUES (NULL, 'O97.900', '产科原因后遗症的死亡', 'SWZDBM', '关于死亡诊断编码', 255, NULL, NULL, NULL, NULL, '当出现此主要诊断编码时，离院方式应当为5死亡，否则，校验不通过。', 0);
INSERT INTO `som_sys_dic` (`id`, `data_val`, `labl_name`, `code_type`, `dscr`, `srt`, `crter`, `crte_time`, `updt_psn`, `updt_time`, `memo_info`, `is_del`) VALUES (NULL, 'R95.000', '婴儿猝死综合征伴提及尸体解剖', 'SWZDBM', '关于死亡诊断编码', 255, NULL, NULL, NULL, NULL, '当出现此主要诊断编码时，离院方式应当为5死亡，否则，校验不通过。', 0);
INSERT INTO `som_sys_dic` (`id`, `data_val`, `labl_name`, `code_type`, `dscr`, `srt`, `crter`, `crte_time`, `updt_psn`, `updt_time`, `memo_info`, `is_del`) VALUES (NULL, 'R96.000', '婴儿猝死综合征伴未提及尸体解剖', 'SWZDBM', '关于死亡诊断编码', 255, NULL, NULL, NULL, NULL, '当出现此主要诊断编码时，离院方式应当为5死亡，否则，校验不通过。', 0);
INSERT INTO `som_sys_dic` (`id`, `data_val`, `labl_name`, `code_type`, `dscr`, `srt`, `crter`, `crte_time`, `updt_psn`, `updt_time`, `memo_info`, `is_del`) VALUES (NULL, 'R96.000x001', '猝死', 'SWZDBM', '关于死亡诊断编码', 255, NULL, NULL, NULL, NULL, '当出现此主要诊断编码时，离院方式应当为5死亡，否则，校验不通过。', 0);
INSERT INTO `som_sys_dic` (`id`, `data_val`, `labl_name`, `code_type`, `dscr`, `srt`, `crter`, `crte_time`, `updt_psn`, `updt_time`, `memo_info`, `is_del`) VALUES (NULL, 'R96.001', '突然不明原因的死亡', 'SWZDBM', '关于死亡诊断编码', 255, NULL, NULL, NULL, NULL, '当出现此主要诊断编码时，离院方式应当为5死亡，否则，校验不通过。', 0);
INSERT INTO `som_sys_dic` (`id`, `data_val`, `labl_name`, `code_type`, `dscr`, `srt`, `crter`, `crte_time`, `updt_psn`, `updt_time`, `memo_info`, `is_del`) VALUES (NULL, 'R96.100', '死亡发生于症状起始后24小时以内，另无解释', 'SWZDBM', '关于死亡诊断编码', 255, NULL, NULL, NULL, NULL, '当出现此主要诊断编码时，离院方式应当为5死亡，否则，校验不通过。', 0);
INSERT INTO `som_sys_dic` (`id`, `data_val`, `labl_name`, `code_type`, `dscr`, `srt`, `crter`, `crte_time`, `updt_psn`, `updt_time`, `memo_info`, `is_del`) VALUES (NULL, 'R96.100x001', '无疾病体征的死亡', 'SWZDBM', '关于死亡诊断编码', 255, NULL, NULL, NULL, NULL, '当出现此主要诊断编码时，离院方式应当为5死亡，否则，校验不通过。', 0);
INSERT INTO `som_sys_dic` (`id`, `data_val`, `labl_name`, `code_type`, `dscr`, `srt`, `crter`, `crte_time`, `updt_psn`, `updt_time`, `memo_info`, `is_del`) VALUES (NULL, 'R98.x00', '无人在场的死亡', 'SWZDBM', '关于死亡诊断编码', 255, NULL, NULL, NULL, NULL, '当出现此主要诊断编码时，离院方式应当为5死亡，否则，校验不通过。', 0);
INSERT INTO `som_sys_dic` (`id`, `data_val`, `labl_name`, `code_type`, `dscr`, `srt`, `crter`, `crte_time`, `updt_psn`, `updt_time`, `memo_info`, `is_del`) VALUES (NULL, 'R98.x00x002', '发现时已死亡', 'SWZDBM', '关于死亡诊断编码', 255, NULL, NULL, NULL, NULL, '当出现此主要诊断编码时，离院方式应当为5死亡，否则，校验不通过。', 0);
INSERT INTO `som_sys_dic` (`id`, `data_val`, `labl_name`, `code_type`, `dscr`, `srt`, `crter`, `crte_time`, `updt_psn`, `updt_time`, `memo_info`, `is_del`) VALUES (NULL, 'R99.x00', '其他原因不明确和未特指原因的死亡', 'SWZDBM', '关于死亡诊断编码', 255, NULL, NULL, NULL, NULL, '当出现此主要诊断编码时，离院方式应当为5死亡，否则，校验不通过。', 0);
INSERT INTO `som_sys_dic` (`id`, `data_val`, `labl_name`, `code_type`, `dscr`, `srt`, `crter`, `crte_time`, `updt_psn`, `updt_time`, `memo_info`, `is_del`) VALUES (NULL, 'R99.x00x002', '原因不明的死亡', 'SWZDBM', '关于死亡诊断编码', 255, NULL, NULL, NULL, NULL, '当出现此主要诊断编码时，离院方式应当为5死亡，否则，校验不通过。', 0);
INSERT INTO `som_sys_dic` (`id`, `data_val`, `labl_name`, `code_type`, `dscr`, `srt`, `crter`, `crte_time`, `updt_psn`, `updt_time`, `memo_info`, `is_del`) VALUES (NULL, 'R99.x01', '死亡', 'SWZDBM', '关于死亡诊断编码', 255, NULL, NULL, NULL, NULL, '当出现此主要诊断编码时，离院方式应当为5死亡，否则，校验不通过。', 0);
-- 修改 som_oprn_rcd 手术操作的时间格式
ALTER TABLE som_oprn_rcd MODIFY anst_begntime datetime;
ALTER TABLE som_oprn_rcd MODIFY anst_endtime datetime;
ALTER TABLE som_oprn_rcd MODIFY pacu_begntime datetime;
ALTER TABLE som_oprn_rcd MODIFY pacu_endtime datetime;
ALTER TABLE som_oprn_rcd MODIFY oprn_oprt_begntime datetime;
ALTER TABLE som_oprn_rcd MODIFY oprn_oprt_endtime datetime;

-- 新增脚本异常质检

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for som_script_chk_log
-- ----------------------------
DROP TABLE IF EXISTS `som_script_chk_log`;
CREATE TABLE `som_script_chk_log`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '脚本错误日志 id',
  `k00` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '唯一id',
  `ex_dscr` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '错误描述',
  `script_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '错误脚本字段',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1779 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;
SET FOREIGN_KEY_CHECKS = 1;


-- 修改表结构时间字段
ALTER TABLE `som_setl_invy_bas_info`
    MODIFY COLUMN `adm_date` datetime(0) NULL DEFAULT NULL COMMENT '入院日期' AFTER `adm_ward`,
    MODIFY COLUMN `dscg_date` datetime(0) NULL DEFAULT NULL COMMENT '出院日期' AFTER `adm_date`,
    MODIFY COLUMN `qltctrl_date` datetime(0) NULL DEFAULT NULL COMMENT '质控日期' AFTER `medcas_qlt_code`,
    MODIFY COLUMN `cnfm_date` datetime(0) NULL DEFAULT NULL COMMENT '确诊日期' AFTER `vent_used_dura`;
ALTER TABLE `som_scs_cutd_info`
    MODIFY COLUMN `scs_cutd_inpool_time` datetime(0) NULL DEFAULT NULL COMMENT '重症监护进入时间' AFTER `scs_cutd_ward_type`,
    MODIFY COLUMN `scs_cutd_exit_time` datetime(0) NULL DEFAULT NULL COMMENT '重症监护退出时间' AFTER `scs_cutd_inpool_time`;


-- update by qingfeng 20240911 begin
ALTER TABLE som_dept add column std_dept_name varchar(100) DEFAULT NULL COMMENT '标准科室名称' AFTER std_dept_codg;





-- update by qingfeng 20240918 DIP支付结算政策组件化适配 begin
truncate table som_chain;
INSERT INTO som_chain (application_name,chain_name,chain_desc,el_data,create_time,chain_enable) VALUES
                                                                                                    ('somplatform','GlobalExecSettleValidateChain','结算清单质控总调用链','THEN(GlobalExecSettleValidateBasicChain,GlobalExecSettleValidateDepthChain)','2024-07-24 10:25:57',1),
                                                                                                    ('somplatform','GlobalExecSettleValidateBasicChain','清单字段基础校验调用链','THEN(CATCH(check_adm_time_basic).DO(settleValidateHandleExceptionNode),CATCH(check_age_basic).DO(settleValidateHandleExceptionNode),CATCH(check_anst_dr_code_basic).DO(settleValidateHandleExceptionNode),CATCH(check_anst_dr_name_basic).DO(settleValidateHandleExceptionNode),CATCH(check_coner_addr_basic).DO(settleValidateHandleExceptionNode),CATCH(check_coner_tel_basic).DO(settleValidateHandleExceptionNode),CATCH(check_dscg_time_basic).DO(settleValidateHandleExceptionNode),CATCH(check_hi_no_basic).DO(settleValidateHandleExceptionNode),CATCH(check_lyfs_basic).DO(settleValidateHandleExceptionNode),CATCH(check_medcasno_basic).DO(settleValidateHandleExceptionNode),CATCH(check_ntly_basic).DO(settleValidateHandleExceptionNode),CATCH(check_nwb_adm_wt_basic).DO(settleValidateHandleExceptionNode),CATCH(check_nwb_age_basic).DO(settleValidateHandleExceptionNode),CATCH(check_nwb_bir_wt_basic).DO(settleValidateHandleExceptionNode),CATCH(check_oper_dr_code_basic).DO(settleValidateHandleExceptionNode),CATCH(check_oper_dr_name_basic).DO(settleValidateHandleExceptionNode),CATCH(check_patn_rlts_basic).DO(settleValidateHandleExceptionNode),CATCH(check_pjdm_basic).DO(settleValidateHandleExceptionNode),CATCH(check_pjhm_basic).DO(settleValidateHandleExceptionNode),CATCH(check_prfs_basic).DO(settleValidateHandleExceptionNode),CATCH(check_ywlsh_basic).DO(settleValidateHandleExceptionNode),CATCH(check_zrhsdm_basic).DO(settleValidateHandleExceptionNode),CATCH(check_zrhsxm_basic).DO(settleValidateHandleExceptionNode),CATCH(check_zzysdm_basic).DO(settleValidateHandleExceptionNode),CATCH(check_zzysxm_basic).DO(settleValidateHandleExceptionNode),CATCH(check_emp_addr_after_basic).DO(settleValidateHandleExceptionNode),CATCH(check_emp_name_after_basic).DO(settleValidateHandleExceptionNode),CATCH(check_emp_tel_after_basic).DO(settleValidateHandleExceptionNode),CATCH(check_pos_code_after_basic).DO(settleValidateHandleExceptionNode),CATCH(check_mdeic_type_after_basic).DO(settleValidateHandleExceptionNode),CATCH(check_anst_way_after_basic).DO(settleValidateHandleExceptionNode),CATCH(check_refl_dept_dept_after_basic).DO(settleValidateHandleExceptionNode),CATCH(check_dscg_caty_code_after_basic).DO(settleValidateHandleExceptionNode),CATCH(check_adm_way_after_basic).DO(settleValidateHandleExceptionNode),CATCH(check_medins_fill_dept_after_basic).DO(settleValidateHandleExceptionNode),CATCH(check_medins_fill_psn_after_basic).DO(settleValidateHandleExceptionNode),CATCH(check_hi_paymtd_after_basic).DO(settleValidateHandleExceptionNode),CATCH(check_setl_date_after_basic).DO(settleValidateHandleExceptionNode),CATCH(check_days_rinp_flag_31_after_basic).DO(settleValidateHandleExceptionNode),CATCH(check_nwb_age_after_basic).DO(settleValidateHandleExceptionNode),CATCH(check_bld_cat_after_basic).DO(settleValidateHandleExceptionNode),CATCH(check_scs_cutd_ward_type_after_basic).DO(settleValidateHandleExceptionNode),CATCH(check_otp_wm_dise_code_after_basic).DO(settleValidateHandleExceptionNode),CATCH(check_trt_type_after_basic).DO(settleValidateHandleExceptionNode),CATCH(check_nwb_bir_wt_after_basic).DO(settleValidateHandleExceptionNode),CATCH(check_nwb_adm_type_after_basic).DO(settleValidateHandleExceptionNode),CATCH(check_curr_addr_after_basic).DO(settleValidateHandleExceptionNode),CATCH(check_otp_tcm_dise_after_basic).DO(settleValidateHandleExceptionNode),
CATCH(check_diag_code_basic).DO(settleValidateHandleExceptionNode),CATCH(check_oprn_oprt_time_after_basic).DO(settleValidateHandleExceptionNode))','2024-07-24 10:25:57',1),
                                                                                                    ('somplatform','GlobalExecSettleValidateDepthChain','清单字段内涵校验调用链','THEN(CATCH(check_adm_time_depth).DO(settleValidateHandleExceptionNode),CATCH(check_dscg_time_depth).DO(settleValidateHandleExceptionNode),CATCH(check_nwb_adm_wt_depth).DO(settleValidateHandleExceptionNode),CATCH(check_nwb_age_depth).DO(settleValidateHandleExceptionNode),CATCH(check_nwb_bir_wt_depth).DO(settleValidateHandleExceptionNode),CATCH(check_oprn_code_depth).DO(settleValidateHandleExceptionNode),CATCH(check_diag_merge_comb_after_depth).DO(settleValidateHandleExceptionNode),CATCH(check_oprn_merge_comb_after_depth).DO(settleValidateHandleExceptionNode),CATCH(check_emp_addr_after_depth).DO(settleValidateHandleExceptionNode),CATCH(check_emp_name_after_depth).DO(settleValidateHandleExceptionNode),CATCH(check_emp_tel_after_depth).DO(settleValidateHandleExceptionNode),CATCH(check_pos_code_after_depth).DO(settleValidateHandleExceptionNode),CATCH(check_dscg_wm_other_dise_new_age_depth).DO(settleValidateHandleExceptionNode),CATCH(check_wm_adm_cond_after_depth).DO(settleValidateHandleExceptionNode),CATCH(check_anst_time_after_depth).DO(settleValidateHandleExceptionNode),CATCH(check_oprn_oprt_time_after_depth).DO(settleValidateHandleExceptionNode),CATCH(check_brn_damg_coma_dura_after_depth).DO(settleValidateHandleExceptionNode),CATCH(check_vent_used_dura_after_depth).DO(settleValidateHandleExceptionNode),CATCH(check_setl_date_after_depth).DO(settleValidateHandleExceptionNode),CATCH(check_bld_amt_after_depth).DO(settleValidateHandleExceptionNode),CATCH(check_bld_unt_after_depth).DO(settleValidateHandleExceptionNode),CATCH(check_scs_cutd_sum_dura_after_depth).DO(settleValidateHandleExceptionNode),CATCH(check_otp_wm_dise_code_after_depth).DO(settleValidateHandleExceptionNode),CATCH(check_ipt_days_after_depth).DO(settleValidateHandleExceptionNode),CATCH(check_tcm_diagnosis_after_depth).DO(settleValidateHandleExceptionNode),CATCH(check_diag_code_depth).DO(settleValidateHandleExceptionNode))','2024-07-24 10:25:57',1),
                                                                                                    ('somplatform','DrgGainHospBaseInfoChain','【DRG】获取医疗机构基础配置及支付标准配置','THEN(HospCostRateNode,MonthForecastPriceNode,GainDrgPayStandardsInfoChain)','2024-07-24 10:17:47',1),
                                                                                                    ('somplatform','GainDrgPayStandardsInfoChain','【DRG】获取DRG支付标准配置','THEN(DrgPayStandardsInfoNode,DrgBedPayStandardsInfoNode)','2024-07-24 10:25:57',1),
                                                                                                    ('somplatform','DrgGainHospPreGroupPaymentChain','【DRG预分组】Drg付费预测','THEN(DrgPreGroupGenerateScoreChain5114,DrgPreGroupForecastCostChain5114)','2024-07-24 10:25:57',1),
                                                                                                    ('somplatform','DrgPreGroupGenerateScoreChain5114','【DRG预分组】生成支付分值-眉山','THEN(DrgCalculateDiseGroupTypeNode5114,DrgCalculateMedCaseTypeNode5114,DrgCalculateGeneratePointsNode5114)','2024-07-24 10:25:57',1),
                                                                                                    ('somplatform','DrgPreGroupForecastCostChain5114','【DRG预分组】生成费用预测-眉山','THEN(DrgCalculatePreGroupPayForecastNode5114)','2024-07-24 10:25:57',1),
                                                                                                    ('somplatform','DrgPreGroupGenerateScoreChain5107','【DRG预分组】生成支付分值-绵阳','THEN(DrgCalculateDiseGroupTypeNode5107,DrgCalculateMedCaseTypeNode5107,DrgCalculateGeneratePointsNode5107)','2024-07-24 10:25:57',1),
                                                                                                    ('somplatform','DrgPreGroupForecastCostChain','【DRG预分组】生成费用预测','THEN(DrgCalculatePreGroupPayForecastNode)','2024-07-24 10:25:57',1);
INSERT INTO som_chain (application_name,chain_name,chain_desc,el_data,create_time,chain_enable) VALUES
                                                                                                    ('somplatform','DrgGainHospBusiProcPaymentChain','【DRG业务流程】Drg付费预测','THEN(DrgBusiProcGenerateScoreChain5114,DrgBusiProcForecastCostChain)','2024-07-24 10:25:57',1),
                                                                                                    ('somplatform','DrgBusiProcGenerateScoreChain5114','【DRG业务流程】生成支付分值-眉山','THEN(DrgCalculateMedCaseTypeNode5114,DrgCalculateGeneratePointsNode5114)','2024-07-24 10:25:57',1),
                                                                                                    ('somplatform','DrgBusiProcForecastCostChain','【DRG业务流程】生成费用预测','THEN(DrgCalculateBusiProcPayForecastNode)','2024-07-24 10:25:57',1),
                                                                                                    ('somplatform','DrgBusiProcGenerateScoreChain5107','【DRG业务流程】生成支付分值-绵阳','THEN(DrgCalculateMedCaseTypeNode5107,DrgCalculateGeneratePointsNode5107)','2024-07-24 10:25:57',1),
                                                                                                    ('somplatform','DipGainHospBaseInfoChain','【DIP】获取医疗机构基础配置及支付标准配置','THEN(HospCostRateNode,MonthForecastPriceNode,GainDipPayStandardsInfoChain)','2024-07-24 10:17:47',1),
                                                                                                    ('somplatform','GainDipPayStandardsInfoChain','【DIP】获取Dip支付标准配置','THEN(DipPayStandardsInfoNode)','2024-07-24 10:25:57',1),
                                                                                                    ('somplatform','DipGainHospPreGroupPaymentChain','【DIP预分组】Dip付费预测','THEN(DipPreGroupGenerateScoreChain5134,DipPreGroupForecastCostChain5134)','2024-07-24 10:25:57',1),
                                                                                                    ('somplatform','DipPreGroupGenerateScoreChain5134','【DIP预分组】生成支付分值-凉山','THEN(DipCalculateDiseGroupTypeNode5134,DipCalculateMedCaseTypeNode5134,DipCalculateGeneratePointsNode5134,DipCalculateOtherSpecialNode5134)','2024-07-24 10:25:57',1),
                                                                                                    ('somplatform','DipPreGroupForecastCostChain5134','【DIP预分组】生成费用预测-凉山','THEN(DipCalculatePreGroupPayForecastNode,DipPaymentResultComponent)','2024-07-24 10:25:57',1),
                                                                                                    ('somplatform','DipGainHospBusiProcPaymentChain','【DIP业务流程】Dip付费预测','THEN(DipBusiProcGenerateScoreChain5134,DipBusiProcForecastCostChain5134)','2024-07-24 10:25:57',1);
INSERT INTO som_chain (application_name,chain_name,chain_desc,el_data,create_time,chain_enable) VALUES
                                                                                                    ('somplatform','DipBusiProcGenerateScoreChain5134','【DIP业务流程】生成支付分值-凉山','THEN(DipCalculateMedCaseTypeNode5134,DipCalculateGeneratePointsNode5134,DipCalculateOtherSpecialNode5134)','2024-07-24 10:25:57',1),
                                                                                                    ('somplatform','DipBusiProcForecastCostChain5134','【DIP业务流程】生成费用预测-凉山','THEN(DipCalculateBusiProcPayForecastNode)','2024-07-24 10:25:57',1);

-- update by qingfeng 20240918 DIP支付结算政策组件化适配 end

CREATE INDEX idx_dscg_diag_codg ON som_diag(dscg_diag_codg);
CREATE INDEX idx_crsp_icd_codg ON som_codg_crsp(crsp_icd_codg);
CREATE INDEX idx_crsp_icd_codg_ver ON som_codg_crsp(crsp_icd_codg_ver);
CREATE INDEX idx_C01C ON som_hi_invy_bas_info (C01C);
CREATE INDEX idx_c35c ON som_oprn_oprt_info (c35c);

-- update by lihongxiang 20240919 begin
INSERT INTO `som_sys_dic` (`id`, `data_val`, `labl_name`, `code_type`, `dscr`, `srt`, `crter`, `crte_time`, `updt_psn`, `updt_time`, `memo_info`, `is_del`) VALUES (null, '1', '无', 'ZZYJH', '31天再入院计划编码', 1, NULL, NULL, NULL, NULL, NULL, 0);
INSERT INTO `som_sys_dic` (`id`, `data_val`, `labl_name`, `code_type`, `dscr`, `srt`, `crter`, `crte_time`, `updt_psn`, `updt_time`, `memo_info`, `is_del`) VALUES (null, '2', '有', 'ZZYJH', '31天再入院计划编码', 2, NULL, NULL, NULL, NULL, NULL, 0);
-- update by lihongxiang 20240919 end

-- update by gaoxinjie 20240919 begin

-- 新增D37字段修复bug,手动添加结算时间
ALTER TABLE som_init_hi_setl_invy_med_fee_info add column D37 varchar(100) DEFAULT NULL COMMENT '结算时间' AFTER D34;
UPDATE som_init_hi_setl_invy_med_fee_info a LEFT JOIN som_hi_invy_bas_info b ON a.k00 = b.K00 SET a.D37 = b.d37;
-- update by gaoxinjie 20240919 end


-- update by gaoxinjie 20240919 start
INSERT INTO som_chain (application_name,chain_name,chain_desc,el_data,create_time,chain_enable) VALUES
                                                                                                    ('somplatform','DrgPreGroupGenerateScoreChain5120','【DRG预分组】生成支付分值-资阳','THEN(DrgCalculateDiseGroupTypeNode5120,DrgCalculateMedCaseTypeNode5120,DrgCalculateGeneratePointsNode5120)','2024-07-24 10:25:57',1),
                                                                                                    ('somplatform','DrgPreGroupForecastCostChain5120','【DRG预分组】生成费用预测-资阳','THEN(DrgCalculatePreGroupPayForecastNode)','2024-07-24 10:25:57',1),
                                                                                                    ('somplatform','DrgBusiProcGenerateScoreChain5120','【DRG业务流程】生成支付分值-资阳','THEN(DrgCalculateMedCaseTypeNode5120,DrgCalculateGeneratePointsNode5120)','2024-07-24 10:25:57',1);
-- update by gaoxinjie 20240919 end

-- update by lihongxiang 20240920 begin
-- 重新执行 resources下的 sql/script/som_icd_codg.sql 和som_codg_crsp.sql
-- 由于文件过大 请在文件夹下打开  选中文件右键 open in(打开于) /explorer(资源管理器)
-- ----------------------------som_diag---------------------------- dscg_diag_codg  dscg_diag_name
UPDATE som_diag AS a LEFT JOIN som_codg_crsp AS b ON a.dscg_diag_codg = b.crsp_icd_codg AND b.crsp_icd_codg_ver = '10' SET a.dscg_diag_name = b.crsp_icd_name WHERE a.dscg_diag_codg = b.crsp_icd_codg;
-- ----------------------------som_hi_invy_bas_info----------------------------  c03c  c04n
UPDATE som_hi_invy_bas_info AS a INNER JOIN som_codg_crsp AS b ON a.c03c = b.crsp_icd_codg AND b.crsp_icd_codg_ver = '10' SET a.c04n = b.crsp_icd_name WHERE a.c03c = b.crsp_icd_codg;
-- ----------------------------som_hi_invy_bas_info---------------------------- C14x01C  C15x01N
UPDATE som_hi_invy_bas_info AS a INNER JOIN som_codg_crsp AS b ON a.C14x01C = b.crsp_icd_codg AND b.crsp_icd_codg_ver = '10' SET a.C15x01N = b.crsp_icd_name WHERE a.C14x01C = b.crsp_icd_codg;
-- ---------------------------som_hi_invy_bas_info-----------------------------------C01C  C02N
UPDATE som_hi_invy_bas_info AS a INNER JOIN som_codg_crsp AS b ON  a.C01C = b.crsp_icd_codg AND b.crsp_icd_codg_ver = '10' SET a.C02N = b.crsp_icd_name WHERE  a.C01C = b.crsp_icd_codg;
-- ---------------------------som_oprn_oprt_info-----------------------------------c35c  c36n
UPDATE som_oprn_oprt_info AS a INNER JOIN som_codg_crsp AS b ON a.c35c = b.crsp_icd_codg AND b.crsp_icd_codg_ver = '10' SET a.c36n = b.crsp_icd_name WHERE a.c35c = b.crsp_icd_codg;
-- update by lihongxiang 20240920 end



-- update by  lihongxiang 20240923 begin
INSERT INTO `som_sys_dic` (`id`, `data_val`, `labl_name`, `code_type`, `dscr`, `srt`, `crter`, `crte_time`, `updt_psn`, `updt_time`, `memo_info`, `is_del`) VALUES (null, '17', '出现质控异常', 'QDJYCWLX', '清单校验错误类型清单校验错误类型', 255, NULL, NULL, NULL, NULL, '', 0);
-- update by  lihongxiang 20240923 end

-- update by  lihongxiang 20240923 begin
alter table som_chain_script_all
    add column region varchar(200) DEFAULT NULL COMMENT '使用地区';
-- update by  lihongxiang 20240923 end


-- update by  lihongxiang 20241008 begin
UPDATE som_not_be_main_code
SET dscr = REPLACE(dscr, '(该疾病编码)(该疾病编码名称)，', '')
WHERE dscr LIKE '%(该疾病编码)(该疾病编码名称)，%';
-- update by  lihongxiang 20241008 end

-- update by  lihongxiang 20241009 begin
INSERT INTO `som_menu_mgt` (`name`, `prnt_menu_id_lv1_menu`, `url`, `auth`, `type`, `icon`, `disp_seq`, `crter`, `crte_time`, `updt_psn`, `updt_time`, `is_del`, `is_hide`, `is_cahe`, `is_double_cahe`, `is_admin_menu`)
VALUES ( '结算清单修改', 343, '/dataSyncProcess/settleListModification', '', 1, 'el-icon-setting', 3, NULL, NULL, NULL, NULL, 0, '0', '1', '0', '0');
-- update by  lihongxiang 20241009 end

-- update by lihongxiang 20241011 begin
UPDATE som_chk_unable_action_main_diag_codg
SET dscr = REPLACE(dscr, '此编码为残余类目编码', '不能作为主要诊断编码，此编码为残余类目编码')
WHERE TYPE = '3';
-- update by lihongxiang 20241011 end



ALTER TABLE `som_dip_standard`
    ADD COLUMN `is_base_dise` varchar(255) NULL COMMENT '是否是基层病种0否1是' AFTER `dis_type`;

-- update by lihongxiang 20241103 begin
alter table som_dip_standard
    add column aux_coefficient decimal(10,4) DEFAULT 1.00 COMMENT '辅助目录系数';
alter table som_dip_sco
    add column pre_hosp_examfee decimal(10,4) DEFAULT 0.00 COMMENT '院前检查费 d02';
    -- update by lihongxiang 20241103 end

-- update by lihongxiang 20241113 begin
alter table som_dip_sco
    add column in_hosp_cost decimal(10,4) DEFAULT 0.00 COMMENT '住院费用d01';
alter table som_dip_sco
    add column medicare_settlement_cost decimal(10,4) DEFAULT 0.00 COMMENT '医保结算费用 14项费用总和';

INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (null, 'K35.800x001;K65.001;', 'K35.200', NULL, '1', 1, '2024-08-29 07:42:49', '2024-08-29 07:42:49');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (null, 'B24.x01;A15.000x002;', 'B20.003', NULL, '1', 1, '2024-08-29 07:04:32', '2024-08-29 07:04:32');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (null, 'E04.902;E05.900x001;', 'E05.202', NULL, '1', 1, '2024-08-29 07:04:32', '2024-08-29 07:04:32');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (null, 'B24.x01;B02.200x008+G63.0*;', 'B20.301', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (null, 'O80.900;D64.900;', 'O99.000x021', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (null, 'O80.900;D64.900;', 'O99.000x02', NULL, '1', 1, '2024-08-29 07:04:32', '2024-08-29 07:04:32');

INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (null, '80.5107;03.0900x010;', '80.5108', NULL, '2', 1, '2024-08-29 07:04:32', '2024-08-29 07:04:32');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (null, '80.2200;80.4201;', '80.510', NULL, '2', 1, '2024-08-29 07:04:32', '2024-08-29 07:04:32');

INSERT INTO `som_chk_diag_sec` (`id`, `diag_sec`, `chk_type`, `dscr`, `diag_name`) VALUES (null, 'S30.200x010', '1', '病人性别为男性，不能使用该疾病编码', NULL);
INSERT INTO `som_chk_diag_sec` (`id`, `diag_sec`, `chk_type`, `dscr`, `diag_name`) VALUES (null, 'K62.014', '2', '病人性别为女性，不能使用该疾病编码', NULL);

-- update by lihongxiang 20241113 end


-- update by gaoxinjie 20241113 begin
ALTER TABLE som_setl_invy_bas_info add column adm_diag varchar(255) DEFAULT NULL COMMENT '入院诊断' AFTER otp_tcm_diag;
ALTER TABLE som_hi_invy_bas_info add column ADM_DIAG varchar(255) DEFAULT NULL COMMENT '入院诊断' AFTER DATA_LOG_ID;
-- update by gaoxinjie 20241113 end


-- update by lihongxiang 20241113 begin
 -- 彭山特有
INSERT INTO `som_timer_task_time_cfg` (`id`, `cron_expr`) VALUES (4, '0 0 1 * * ?');
INSERT INTO `som_timer_task_time_cfg` (`id`, `cron_expr`) VALUES (5, '0 0 2 * * ?');
INSERT INTO `som_timer_task_time_cfg` (`id`, `cron_expr`) VALUES (6, '0 0 0 1 * ?');
-- update by lihongxiang 20241113 end

-- -- update by lihongxiang 20241122 begin
INSERT INTO `som_chain` (`id`, `application_name`, `chain_name`, `chain_desc`, `el_data`, `create_time`, `chain_enable`) VALUES (36, 'somplatform', 'DrgPreGroupGenerateScoreChain5110', '【DRG预分组】生成支付分值-内江', 'THEN(DrgCalculateDiseGroupTypeNode5110,DrgCalculateMedCaseTypeNode5110,DrgCalculateGeneratePointsNode5110)', '2024-07-24 10:25:57', 1);
INSERT INTO `som_chain` (`id`, `application_name`, `chain_name`, `chain_desc`, `el_data`, `create_time`, `chain_enable`) VALUES (37, 'somplatform', 'DrgPreGroupForecastCostChain5110', '【DRG预分组】生成费用预测-内江', 'THEN(DrgCalculatePreGroupPayForecastNode5110)', '2024-07-24 10:25:57', 1);
INSERT INTO `som_chain` (`id`, `application_name`, `chain_name`, `chain_desc`, `el_data`, `create_time`, `chain_enable`) VALUES (38, 'somplatform', 'DrgBusiProcGenerateScoreChain5110', '【DRG业务流程】生成支付分值-内江', 'THEN(DrgCalculateMedCaseTypeNode5110,DrgCalculateGeneratePointsNode5110)', '2024-07-24 10:25:57', 1);
INSERT INTO `som_chain` (`id`, `application_name`, `chain_name`, `chain_desc`, `el_data`, `create_time`, `chain_enable`) VALUES (39, 'somplatform', 'DrgGainHospPreGroupPaymentChain_5110', '【DRG预分组】Drg付费预测', 'THEN(DrgPreGroupGenerateScoreChain5110,DrgPreGroupForecastCostChain5110)', '2024-07-24 10:25:57', 1);
INSERT INTO `som_chain` (`id`, `application_name`, `chain_name`, `chain_desc`, `el_data`, `create_time`, `chain_enable`) VALUES (40, 'somplatform', 'DrgGainHospBusiProcPaymentChain_5110', '【DRG业务流程】Drg付费预测', 'THEN(DrgBusiProcGenerateScoreChain5110,DrgBusiProcForecastCostChain)', '2024-07-24 10:25:57', 1);
-- update by lihongxiang 20241122 end

-- -- update by lihongxiang 20241125 begin
-- 眉山彭山
INSERT INTO `som_chain` (`id`, `application_name`, `chain_name`, `chain_desc`, `el_data`, `create_time`, `chain_enable`) VALUES (null, 'somplatform', 'PercentagePolicyAdjustments', '【DRG政策调整】Drg政策调整', 'THEN(DrgPercentagePolicyAdjustmentsNode5114)', '2024-07-24 10:25:57', 1);
-- 如果有需要调整政策的医院 则添加，没有可以不用添加
INSERT INTO `som_sys_gen_cfg` (`id`, `key`, `value`, `type`, `description`) VALUES (null, 'POLICY_ADJUSTMENTS', 'true', 'SYS_CONFIG', '按政策进行百分比调整');

-- -- update by lihongxiang 20241125 end

-- -- update by lihongxiang 20241202 begin
ALTER TABLE som_drg_sco add column remark varchar(255) DEFAULT NULL COMMENT '备注 ' AFTER insuplc_admdvs;
-- -- update by lihongxiang 20241202 end


-- -- update by lihongxiang 20241209 begin
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (null, '54.2100;54.5904;', '54.5100x009', NULL, '2', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (null, 'B24.x01;B02.900x001;', 'B20.301', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (null, 'Z32.100;N73.902;', 'O23.501', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
INSERT INTO `som_comb_code_chk` (`id`, `combined_codes`, `generated_code`, `description`, `is_type`, `is_active`, `created_at`, `updated_at`) VALUES (null, 'K80.201;K81.000;', 'K80.000x002', NULL, '1', 1, '2024-08-29 06:56:20', '2024-08-29 06:56:20');
-- -- update by lihongxiang 20241209 end

-- -- update by lihongxiang 20241209 begin
-- 内江通康定时抽取
INSERT INTO `som_timer_task_time_cfg` (`id`, `cron_expr`) VALUES (7, '0 0 1 * * ?');
-- -- update by lihongxiang 20241209 end

-- -- update by lihongxiang 20241217begin
-- 广安政策调配
INSERT INTO `som_chain` (`id`, `application_name`, `chain_name`, `chain_desc`, `el_data`, `create_time`, `chain_enable`) VALUES (null, 'somplatform', 'DrgPreGroupGenerateScoreChain5116', '【DRG预分组】生成支付分值-广安', 'THEN(DrgCalculateDiseGroupTypeNode5116,DrgCalculateMedCaseTypeNode5116,DrgCalculateGeneratePointsNode5116)', '2024-07-24 10:25:57', 1);
INSERT INTO `som_chain` (`id`, `application_name`, `chain_name`, `chain_desc`, `el_data`, `create_time`, `chain_enable`) VALUES (null, 'somplatform', 'DrgPreGroupForecastCostChain5116', '【DRG预分组】生成费用预测-广安', 'THEN(DrgCalculatePreGroupPayForecastNode)', '2024-07-24 10:25:57', 1);
INSERT INTO `som_chain` (`id`, `application_name`, `chain_name`, `chain_desc`, `el_data`, `create_time`, `chain_enable`) VALUES (null, 'somplatform', 'DrgBusiProcGenerateScoreChain5116', '【DRG业务流程】生成支付分值-广安', 'THEN(DrgCalculateMedCaseTypeNode5116,DrgCalculateGeneratePointsNode5116)', '2024-07-24 10:25:57', 1);
-- -- update by lihongxiang 20241217 end

-- -- update by lihongxiang 20241226begin
-- 德阳中江his点击结算清单（其他地方不需要）
INSERT INTO `som_menu_mgt` (`id`, `name`, `prnt_menu_id_lv1_menu`, `url`, `auth`, `type`, `icon`, `disp_seq`, `crter`, `crte_time`, `updt_psn`, `updt_time`, `is_del`, `is_hide`, `is_cahe`, `is_double_cahe`, `is_admin_menu`) VALUES (null, '中江His跳转页面', 343, '/newBusiness/settleListInfo2', '', 1, '', 0, NULL, NULL, NULL, NULL, 0, '1', '1', '1', '0');
-- -- update by lihongxiang 20241226 end


-- -- update by lihongxiang 20240112begin

ALTER TABLE som_dip_grp_info add column auxiliary_burn varchar(255) DEFAULT '未使用' COMMENT '辅助目录-烧伤严重程度 ' AFTER asst_list_tmor_sev_deg;
ALTER TABLE som_dip_grp_rcd add column auxiliary_burn varchar(255) DEFAULT '未使用' COMMENT '辅助目录-烧伤严重程度 ' AFTER asst_list_tmor_sev_deg;
ALTER TABLE som_dip_standard add column auxiliary_burn varchar(255) DEFAULT NULL COMMENT '辅助目录-烧伤严重程度 ' AFTER asst_list_tmor_sev_deg;
ALTER TABLE som_dip_supe_ultra_low_bind add column auxiliary_burn varchar(255) DEFAULT NULL COMMENT '辅助目录-烧伤严重程度 ' AFTER asst_list_tmor_sev_deg;
-- -- 德阳中江只使用主手术参与分组（其他地方不需要）
INSERT INTO `som_sys_gen_cfg` (`key`, `value`, `type`, `description`) VALUES ('ONLY_MAIN_OPRN', 'true', 'SYS_CONFIG', '分组只要主手术');
-- -- update by lihongxiang 20240112 end

-- -- update by lihongxiang 20240121begin
-- 有灰码映射的医院 将灰码 配置于这张表中
CREATE TABLE `som_gray_crsp` (
  `id` int NOT NULL,
  `icd_type` varchar(255) NOT NULL COMMENT 'ICD类别',
  `icd_codg` varchar(255) NOT NULL COMMENT 'ICD编码',
  `icd_name` varchar(255) DEFAULT NULL COMMENT 'ICD名称',
  `crsp_icd_codg` varchar(255) DEFAULT NULL COMMENT '映射ICD编码',
  `crsp_icd_name` varchar(255) DEFAULT NULL COMMENT '映射ICD名称',
  `active_flag` varchar(255) DEFAULT NULL COMMENT '有效标志',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
-- -- update by lihongxiang 20240121 end


-- update by lihongxiang 20240121begin
-- 德昌中江需要配置
INSERT INTO `som_sys_gen_cfg` (`key`, `value`, `type`, `description`) VALUES ( 'GREY_CRSP_FLAG', 'true', 'SYS_CONFIG', '是否开启灰码映射');
INSERT INTO `som_sys_gen_cfg` (`key`, `value`, `type`, `description`) VALUES ('NON_GROUP_FLAG', 'true', 'SYS_CONFIG', '是否开启不参与分组转换');
-- update by lihongxiang 20240121 end

-- update by lihongxiang 20250213begin
ALTER TABLE som_dip_grp_detl add column dip_name varchar(255) DEFAULT NULL COMMENT 'DIP入组名称（基层病种） ' AFTER dip_codg;
ALTER TABLE som_hi_tcm_codg_crsp add column dip_code varchar(255) DEFAULT NULL COMMENT 'DIP入组名称（中医病种） ' AFTER hospital_id;
ALTER TABLE som_hi_tcm_codg_crsp add column dip_name varchar(255) DEFAULT NULL COMMENT 'DIP入组名称（中医病种） ' AFTER hospital_id;


-- update by lihongxiang 20250213 end

-- update by lihongxiang 20250226 begin
-- 1、分组器配置表中新增区域最低分值、区域病例均费、医院系数、床日系数
alter table som_grp_reqt_addr_info add column min_area_point decimal(10,4) DEFAULT NULL COMMENT '区域最低分值';
alter table som_grp_reqt_addr_info add column area_cost decimal(10,4) DEFAULT NULL COMMENT '区域病例均费';
alter table som_grp_reqt_addr_info add column hosp_cof decimal(4,3) DEFAULT NULL COMMENT '医院DRG系数';
alter table som_grp_reqt_addr_info add column hosp_bedday_cof decimal(4,3) DEFAULT NULL COMMENT '医院DRG床日系数';

-- 2、DRG标杆表新居民等级系数，原有项目默认为职工系数
alter table som_drg_standard add column adjm_resid_cof decimal(4,4) DEFAULT NULL COMMENT '调节系数居民' after adjm_cof;

ALTER TABLE som_dip_sco add column tcm_org_cof varchar(255) DEFAULT NULL COMMENT '中医医疗机构编码系数 ' AFTER hospital_id;
-- update by lihongxiang 20250226 end


ALTER TABLE som_hosp_info add column pre_group_url varchar(255) DEFAULT NULL COMMENT '预分组地址 ' AFTER username;
-- updateby lihongxiang 20250330 end

-- updateby lihongxiang 20250412 begin
INSERT INTO `som_menu_mgt` (`id`, `name`, `prnt_menu_id_lv1_menu`, `url`, `auth`, `type`, `icon`, `disp_seq`, `crter`, `crte_time`, `updt_psn`, `updt_time`, `is_del`, `is_hide`, `is_cahe`, `is_double_cahe`, `is_admin_menu`) VALUES (null, '自查自纠', 0, '', '', 0, 'el-icon-data-line', 12, NULL, NULL, NULL, NULL, 0, '0', '', '0', '0');
INSERT INTO `som_menu_mgt` (`id`, `name`, `prnt_menu_id_lv1_menu`, `url`, `auth`, `type`, `icon`, `disp_seq`, `crter`, `crte_time`, `updt_psn`, `updt_time`, `is_del`, `is_hide`, `is_cahe`, `is_double_cahe`, `is_admin_menu`) VALUES (null, '月度违规汇总', 376, '/examAndCorrection/monthlyRollups', '', 1, 'el-icon-upload', 0, NULL, NULL, NULL, NULL, 0, '0', '1', '0', '0');
INSERT INTO `som_menu_mgt` (`id`, `name`, `prnt_menu_id_lv1_menu`, `url`, `auth`, `type`, `icon`, `disp_seq`, `crter`, `crte_time`, `updt_psn`, `updt_time`, `is_del`, `is_hide`, `is_cahe`, `is_double_cahe`, `is_admin_menu`) VALUES (null, '科室违规汇总', 376, '/examAndCorrection/deptVioalSummary', '', 1, 'el-icon-upload', 0, NULL, NULL, NULL, NULL, 0, '0', '1', '0', '0');
INSERT INTO `som_menu_mgt` (`id`, `name`, `prnt_menu_id_lv1_menu`, `url`, `auth`, `type`, `icon`, `disp_seq`, `crter`, `crte_time`, `updt_psn`, `updt_time`, `is_del`, `is_hide`, `is_cahe`, `is_double_cahe`, `is_admin_menu`) VALUES (null, '医生违规汇总', 376, '/examAndCorrection/doctorVioalSummary', '', 1, 'el-icon-upload', 0, NULL, NULL, NULL, NULL, 0, '0', '1', '0', '0');
INSERT INTO `som_menu_mgt` (`id`, `name`, `prnt_menu_id_lv1_menu`, `url`, `auth`, `type`, `icon`, `disp_seq`, `crter`, `crte_time`, `updt_psn`, `updt_time`, `is_del`, `is_hide`, `is_cahe`, `is_double_cahe`, `is_admin_menu`) VALUES (null, '违规规则汇总', 376, '/examAndCorrection/violationRules', '', 1, 'el-icon-upload', 0, NULL, NULL, NULL, NULL, 0, '0', '1', '0', '0');
INSERT INTO `som_menu_mgt` (`id`, `name`, `prnt_menu_id_lv1_menu`, `url`, `auth`, `type`, `icon`, `disp_seq`, `crter`, `crte_time`, `updt_psn`, `updt_time`, `is_del`, `is_hide`, `is_cahe`, `is_double_cahe`, `is_admin_menu`) VALUES (null, '违规患者明细', 376, '/examAndCorrection/pationtsCompliant', '', 1, 'el-icon-upload', 0, NULL, NULL, NULL, NULL, 0, '0', '1', '0', '0');
INSERT INTO `som_menu_mgt` (`id`, `name`, `prnt_menu_id_lv1_menu`, `url`, `auth`, `type`, `icon`, `disp_seq`, `crter`, `crte_time`, `updt_psn`, `updt_time`, `is_del`, `is_hide`, `is_cahe`, `is_double_cahe`, `is_admin_menu`) VALUES (null, '质控规则查询', 376, '/examAndCorrection/rule', '', 1, 'el-icon-upload', 0, NULL, NULL, NULL, NULL, 0, '0', '1', '0', '0');
INSERT INTO `som_menu_mgt` (`id`, `name`, `prnt_menu_id_lv1_menu`, `url`, `auth`, `type`, `icon`, `disp_seq`, `crter`, `crte_time`, `updt_psn`, `updt_time`, `is_del`, `is_hide`, `is_cahe`, `is_double_cahe`, `is_admin_menu`) VALUES (null, '规则元组查询', 376, '/examAndCorrection/ruleTuples', '', 1, 'el-icon-upload', 0, NULL, NULL, NULL, NULL, 0, '0', '1', '0', '0');
INSERT INTO `som_menu_mgt` (`id`, `name`, `prnt_menu_id_lv1_menu`, `url`, `auth`, `type`, `icon`, `disp_seq`, `crter`, `crte_time`, `updt_psn`, `updt_time`, `is_del`, `is_hide`, `is_cahe`, `is_double_cahe`, `is_admin_menu`) VALUES (null, '违规明细下钻', 376, '/examAndCorrection/docerVialDetial', '', 1, '', 31, NULL, NULL, NULL, NULL, 0, '1', '1', '1', '0');
INSERT INTO `som_menu_mgt` (`id`, `name`, `prnt_menu_id_lv1_menu`, `url`, `auth`, `type`, `icon`, `disp_seq`, `crter`, `crte_time`, `updt_psn`, `updt_time`, `is_del`, `is_hide`, `is_cahe`, `is_double_cahe`, `is_admin_menu`) VALUES (null, '患者违规信息明细', 376, '/examAndCorrection/pationtsDetial', '', 1, '', 0, NULL, NULL, NULL, NULL, 0, '1', '1', '1', '0');
INSERT INTO `som_menu_mgt` (`id`, `name`, `prnt_menu_id_lv1_menu`, `url`, `auth`, `type`, `icon`, `disp_seq`, `crter`, `crte_time`, `updt_psn`, `updt_time`, `is_del`, `is_hide`, `is_cahe`, `is_double_cahe`, `is_admin_menu`) VALUES (null, '质控规则新增', 376, '/examAndCorrection/generalRules', '', 1, 'el-icon-upload', 0, NULL, NULL, NULL, NULL, 0, '0', '1', '0', '0');
-- updateby lihongxiang 20250412 end


-- updateby lihongxiang 20250416 begin
INSERT INTO `som_sys_code` (`id`, `data_val`, `labl_name`, `code_type`, `dscr`, `srt`, `crter`, `crte_time`, `updt_psn`, `updt_time`, `memo_info`, `is_del`) VALUES (3601, 'R01', '超标准收费', 'RULE_TYPE_CODE', '自查自纠规则类型', 1, NULL, NULL, NULL, NULL, NULL, 0);
INSERT INTO `som_sys_code` (`id`, `data_val`, `labl_name`, `code_type`, `dscr`, `srt`, `crter`, `crte_time`, `updt_psn`, `updt_time`, `memo_info`, `is_del`) VALUES (3602, 'R02', '违规使用项目', 'RULE_TYPE_CODE', '自查自纠规则类型', 2, NULL, NULL, NULL, NULL, NULL, 0);
INSERT INTO `som_sys_code` (`id`, `data_val`, `labl_name`, `code_type`, `dscr`, `srt`, `crter`, `crte_time`, `updt_psn`, `updt_time`, `memo_info`, `is_del`) VALUES (3603, 'R03', '过度检查', 'RULE_TYPE_CODE', '自查自纠规则类型', 3, NULL, NULL, NULL, NULL, NULL, 0);
INSERT INTO `som_sys_code` (`id`, `data_val`, `labl_name`, `code_type`, `dscr`, `srt`, `crter`, `crte_time`, `updt_psn`, `updt_time`, `memo_info`, `is_del`) VALUES (3604, 'R04', '限频次', 'RULE_TYPE_CODE', '自查自纠规则类型', 4, NULL, NULL, NULL, NULL, NULL, 0);
INSERT INTO `som_sys_code` (`id`, `data_val`, `labl_name`, `code_type`, `dscr`, `srt`, `crter`, `crte_time`, `updt_psn`, `updt_time`, `memo_info`, `is_del`) VALUES (3605, 'R05', '重复收费', 'RULE_TYPE_CODE', '自查自纠规则类型', 5, NULL, NULL, NULL, NULL, NULL, 0);
INSERT INTO `som_sys_code` (`id`, `data_val`, `labl_name`, `code_type`, `dscr`, `srt`, `crter`, `crte_time`, `updt_psn`, `updt_time`, `memo_info`, `is_del`) VALUES (3606, 'DoubleExistOperator', '不能同时存在', 'OPRA_TYPE_CODE', '自查自纠规则算子类型', 1, NULL, NULL, NULL, NULL, NULL, 0);
INSERT INTO `som_sys_code` (`id`, `data_val`, `labl_name`, `code_type`, `dscr`, `srt`, `crter`, `crte_time`, `updt_psn`, `updt_time`, `memo_info`, `is_del`) VALUES (3607, 'ExistOperator', '违规存在', 'OPRA_TYPE_CODE', '自查自纠规则算子类型', 2, NULL, NULL, NULL, NULL, NULL, 0);
INSERT INTO `som_sys_code` (`id`, `data_val`, `labl_name`, `code_type`, `dscr`, `srt`, `crter`, `crte_time`, `updt_psn`, `updt_time`, `memo_info`, `is_del`) VALUES (3608, 'OverclockTime', '超过既定次数', 'OPRA_TYPE_CODE', '自查自纠规则算子类型', 3, NULL, NULL, NULL, NULL, NULL, 0);
INSERT INTO `som_sys_code` (`id`, `data_val`, `labl_name`, `code_type`, `dscr`, `srt`, `crter`, `crte_time`, `updt_psn`, `updt_time`, `memo_info`, `is_del`) VALUES (3609, 'hosp', '单次住院', 'DATA_TYPE_CODE', '自查自纠规则分组类型', 1, NULL, NULL, NULL, NULL, NULL, 0);
INSERT INTO `som_sys_code` (`id`, `data_val`, `labl_name`, `code_type`, `dscr`, `srt`, `crter`, `crte_time`, `updt_psn`, `updt_time`, `memo_info`, `is_del`) VALUES (3610, 'day', '单日次数', 'DATA_TYPE_CODE', '自查自纠规则分组类型', 2, NULL, NULL, NULL, NULL, NULL, 0);
INSERT INTO `som_sys_code` (`id`, `data_val`, `labl_name`, `code_type`, `dscr`, `srt`, `crter`, `crte_time`, `updt_psn`, `updt_time`, `memo_info`, `is_del`) VALUES (3612, 'not_in_hosp', '门诊', 'RULE_SCEN_TYPE', '自查自纠规则场景类型', 2, NULL, NULL, NULL, NULL, NULL, 0);
INSERT INTO `som_sys_code` (`id`, `data_val`, `labl_name`, `code_type`, `dscr`, `srt`, `crter`, `crte_time`, `updt_psn`, `updt_time`, `memo_info`, `is_del`) VALUES (3611, 'be_in_hosp', '住院', 'RULE_SCEN_TYPE', '自查自纠规则场景类型', 1, NULL, NULL, NULL, NULL, NULL, 0);

-- updateby lihongxiang 20250418 end

-- updateby lihongxiang 20250418 begin
-- 目前只有凉山州的政策实现 因此仅凉山的数据进行添加
INSERT INTO `som_menu_mgt` (`id`, `name`, `prnt_menu_id_lv1_menu`, `url`, `auth`, `type`, `icon`, `disp_seq`, `crter`, `crte_time`, `updt_psn`, `updt_time`, `is_del`, `is_hide`, `is_cahe`, `is_double_cahe`, `is_admin_menu`) VALUES (null, '特病单议汇总', 343, '/setlListManage/SpecialDiseaseSummary', '', 1, 'el-icon-document-copy', 0, NULL, NULL, NULL, NULL, 0, '0', '1', '1', '0');
INSERT INTO `som_chain` (`id`, `application_name`, `chain_name`, `chain_desc`, `el_data`, `create_time`, `chain_enable`) VALUES (null, 'somplatform', 'QuerySpecialDisease', '【特病单议病例汇总】特病单议病例汇总', 'THEN(QuerySpecialDisease5134)', '2024-07-24 10:25:57', 1);
-- updateby lihongxiang 20250418 end

ALTER TABLE som_std_fee add column auxiliary_burn varchar(255) DEFAULT NULL COMMENT '辅助目录-烧伤严重程度 ' AFTER asst_list_tmor_sev_deg;

-- updateby lihongxiang 20250506 begin
INSERT INTO `som_sys_code` (`id`, `data_val`, `labl_name`, `code_type`, `dscr`, `srt`, `crter`, `crte_time`, `updt_psn`, `updt_time`, `memo_info`, `is_del`) VALUES (NULL, '1', '偏高', 'FUNDRATIO_TYPE', '补偿比类型', 3, NULL, NULL, NULL, NULL, '', 0);
INSERT INTO `som_sys_code` (`id`, `data_val`, `labl_name`, `code_type`, `dscr`, `srt`, `crter`, `crte_time`, `updt_psn`, `updt_time`, `memo_info`, `is_del`) VALUES (NULL, '2', '偏低', 'FUNDRATIO_TYPE', '补偿比类型', 3, NULL, NULL, NULL, NULL, '', 0);
INSERT INTO `som_sys_code` (`id`, `data_val`, `labl_name`, `code_type`, `dscr`, `srt`, `crter`, `crte_time`, `updt_psn`, `updt_time`, `memo_info`, `is_del`) VALUES (NULL, '3', '正常', 'FUNDRATIO_TYPE', '补偿比类型', 3, NULL, NULL, NULL, NULL, '', 0);
ALTER TABLE som_dip_sco add column fundAmtSum decimal(10,2) DEFAULT NULL COMMENT '基金总额' AFTER medicare_settlement_cost;
ALTER TABLE som_dip_sco add column presonBearCost decimal(10,2) DEFAULT NULL COMMENT '个人自付部分' AFTER medicare_settlement_cost;
ALTER TABLE som_dip_sco add column preFundFee decimal(10,2) DEFAULT NULL COMMENT '预测报销费用' AFTER medicare_settlement_cost;
ALTER TABLE som_dip_sco add column fundRatio varchar(10) DEFAULT NULL COMMENT '补偿比' AFTER medicare_settlement_cost;
ALTER TABLE som_dip_sco add column fundSourceType varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '基金来源类型' AFTER medicare_settlement_cost;
ALTER TABLE som_dip_sco add column fundRatioType varchar(10) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '补偿比类型' AFTER medicare_settlement_cost;

-- updateby lihongxiang 20250506 end

-- updateby lihongxiang 20250519 begin
INSERT INTO `som_sys_code` (`id`, `data_val`, `labl_name`, `code_type`, `dscr`, `srt`, `crter`, `crte_time`, `updt_psn`, `updt_time`, `memo_info`, `is_del`) VALUES (null, '0', '市医保', 'INSUPLCADMDVS_TYPE', '', 1, NULL, NULL, NULL, NULL, '参保地划分类型', 0);
INSERT INTO `som_sys_code` (`id`, `data_val`, `labl_name`, `code_type`, `dscr`, `srt`, `crter`, `crte_time`, `updt_psn`, `updt_time`, `memo_info`, `is_del`) VALUES (null, '1', '省内异地', 'INSUPLCADMDVS_TYPE', '', 2, NULL, NULL, NULL, NULL, '参保地划分类型', 0);
INSERT INTO `som_sys_code` (`id`, `data_val`, `labl_name`, `code_type`, `dscr`, `srt`, `crter`, `crte_time`, `updt_psn`, `updt_time`, `memo_info`, `is_del`) VALUES (null, '2', '省外异地', 'INSUPLCADMDVS_TYPE', '', 3, NULL, NULL, NULL, NULL, '参保地划分类型', 0);
INSERT INTO `som_sys_code` (`id`, `data_val`, `labl_name`, `code_type`, `dscr`, `srt`, `crter`, `crte_time`, `updt_psn`, `updt_time`, `memo_info`, `is_del`) VALUES (null, '3', '省本级', 'INSUPLCADMDVS_TYPE', '', 4, NULL, NULL, NULL, NULL, '参保地划分类型', 0);
INSERT INTO `som_sys_code` (`id`, `data_val`, `labl_name`, `code_type`, `dscr`, `srt`, `crter`, `crte_time`, `updt_psn`, `updt_time`, `memo_info`, `is_del`) VALUES (null, '4', '未传值', 'INSUPLCADMDVS_TYPE', '', 5, NULL, NULL, NULL, NULL, '参保地划分类型', 0);

-- 一般情况不加 结石病 特有
INSERT INTO `som_sys_gen_cfg` (`id`, `key`, `value`, `type`, `description`) VALUES (42, 'MODIFY_OFF_SITE_CASE', 'true', 'SYS_CONFIG', '修改异地病例的 医保支付方式和 结算区域规划');
-- updateby lihongxiang 20250519 end

-- updateby lihongxiang 20250526 begin
ALTER TABLE som_hosp_info add column emp_fund_ratio decimal(10,4) DEFAULT '0.0000' COMMENT '职工基金补偿比' AFTER pre_group_url;
ALTER TABLE som_hosp_info add column resid_fund_ratio decimal(10,4) DEFAULT '0.0000' COMMENT '居民基金补偿比' AFTER pre_group_url;
-- updateby lihongxiang 20250526 end

-- updateby lihongxiang 20250527 begin
-- 结石病 特有
INSERT INTO `som_timer_task_time_cfg` (`id`, `cron_expr`) VALUES (8, '0 0 1 * * ?');
-- updateby lihongxiang 20250527 end

-- updateby lihongxiang 20250527 begin
alter table hcm_rule_cfg add column target_field varchar(200) DEFAULT NULL COMMENT '校验目标字段(多个|线分隔)';
alter table hcm_rule_cfg add column vola_deg varchar(6) DEFAULT NULL COMMENT '违规程度(1明确违规,2高度可疑)';
alter table hcm_rule_cfg add column exct_cont varchar(500) DEFAULT NULL COMMENT '除外内容';
alter table hcm_rule_cfg add column rule_souc varchar(500) DEFAULT NULL COMMENT '规则来源';
alter table hcm_rule_cfg add column memo varchar(500) DEFAULT NULL COMMENT '备注';
alter table hcm_rule_cfg add column efft_date varchar(500) DEFAULT NULL COMMENT '生效日期';
alter table hcm_rule_cfg add column invd_date varchar(500) DEFAULT NULL COMMENT '失效日期';
alter table hcm_rule_cfg add column exct_type varchar(6) DEFAULT NULL COMMENT '除外类型(1诊断2项目)';
-- updateby lihongxiang 20250527 end


-- updateby lihongxiang 20250527 begin
 -- 暂时中江特有
INSERT INTO `som_sys_gen_cfg` (`id`, `key`, `value`, `type`, `description`) VALUES (null, 'ALGORITHMA_TYPE', '1', 'SYS_CONFIG', '计算预测费用差异类型');
-- updateby lihongxiang 20250527 end

-- updateby lihongxiang 20250618 begin
-- 罗江特有
INSERT INTO `som_sys_gen_cfg` (`id`, `key`, `value`, `type`, `description`) VALUES (NULL, 'HIS_INTEFANCE_RETRUN', '1', 'SYS_CONFIG', 'his接口传参返回（罗江）');
-- updateby lihongxiang 20250618 begin
INSERT INTO `som_menu_mgt` (`id`, `name`, `prnt_menu_id_lv1_menu`, `url`, `auth`, `type`, `icon`, `disp_seq`, `crter`, `crte_time`, `updt_psn`, `updt_time`, `is_del`, `is_hide`, `is_cahe`, `is_double_cahe`, `is_admin_menu`) VALUES (null, '模拟预分组', 343, '/setlListManage/SimulatePreGroup', '', 1, 'el-icon-document-copy', 0, NULL, NULL, NULL, NULL, 0, '0', '1', '1', '0');
-- updateby lihongxiang 20250618 end

-- updateby lihongxiang 20250618 begin
INSERT INTO `som_sys_code` (`id`, `data_val`, `labl_name`, `code_type`, `dscr`, `srt`, `crter`, `crte_time`, `updt_psn`, `updt_time`, `memo_info`, `is_del`) VALUES (null, '7', '基层病例', 'CASE_TYPE', '病例类型', 7, NULL, NULL, NULL, NULL, '病例类型', 0);
INSERT INTO `som_sys_code` (`id`, `data_val`, `labl_name`, `code_type`, `dscr`, `srt`, `crter`, `crte_time`, `updt_psn`, `updt_time`, `memo_info`, `is_del`) VALUES (null, '8', '住院不完整病例', 'CASE_TYPE', '病例类型', 8, NULL, NULL, NULL, NULL, '病例类型', 0);
-- updateby lihongxiang 20250618 end

-- updateby lihongxiang 20250626 begin
-- 眉山特有
INSERT INTO `som_sys_gen_cfg` (`id`, `key`, `value`, `type`, `description`) VALUES (null, 'LOW_DISE_TYPE_NOT_MODIFY', 'true', 'FEE_RESET', '费用重算时不对低倍率数据进行重算（眉山）');
-- updateby lihongxiang 20250626 end

-- updateby lihongxiang 20250626 begin
alter table som_in_group_rcd  add column forecast_fee varchar(100) DEFAULT NULL COMMENT '预测费用';
alter table som_in_group_rcd  add column profitloss varchar(100) DEFAULT NULL COMMENT '盈亏';
-- updateby lihongxiang 20250626 end

-- updateby lihongxiang 202500707 begin
-- 四川结石病要求
INSERT INTO `som_sys_gen_cfg` (`id`, `key`, `value`, `type`, `description`) VALUES (45, 'FEE_MULTI_ADJMCON', 'true', 'SYS_CONFIG', 'drg综合患者分析标杆乘以系数');
-- updateby lihongxiang 20250707 end

-- 修改字段为医保三目录项目名称 begin
ALTER TABLE som_med_serv_cfg
    CHANGE COLUMN itemname med_list_name VARCHAR(255);
-- 修改字段为医保三目录项目名称 end

-- updateby lihongxiang 202500707 begin
-- 德阳中江 、 德昌 设置未入组不允许上传
INSERT INTO `som_sys_gen_cfg` (`id`, `key`, `value`, `type`, `description`) VALUES (45, 'ONLY_GROUY_CAN_UPLOAD', 'true', 'SYS_CONFIG', 'drg综合患者分析标杆乘以系数');
-- updateby lihongxiang 20250707 end

-- updateby lihongxiang 202500721 begin
INSERT INTO `som_timer_task_time_cfg` (`id`, `cron_expr`) VALUES (11, '0 30 1 * * ?');
-- updateby lihongxiang 20250721 end
