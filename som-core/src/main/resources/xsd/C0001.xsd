<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema">
    <xs:element name="input">
        <xs:complexType>
            <xs:choice maxOccurs="unbounded" minOccurs="0">
                <xs:element name="head">
                    <xs:complexType>
                        <xs:complexContent>
                            <xs:extension base="dataElementType">
                                <xs:attributeGroup
                                        ref="dataRequiredAttributeType"/>
                            </xs:extension>
                        </xs:complexContent>
                    </xs:complexType>
                </xs:element>
                <xs:element name="body">
                    <xs:complexType>
                        <xs:complexContent>
                            <xs:extension base="bodyDataElementType">
                                <xs:attributeGroup
                                        ref="dataRequiredAttributeType"/>
                            </xs:extension>
                        </xs:complexContent>
                    </xs:complexType>
                </xs:element>
            </xs:choice>
        </xs:complexType>
    </xs:element>

    <xs:attributeGroup name="dataRequiredAttributeType">
    </xs:attributeGroup>
    <!-- head节点的输入定义 -->
    <xs:complexType name="dataElementType">
        <xs:all>
            <xs:element name="wsa001" type="wsa001" minOccurs="1"/><!--交易代码 -->
            <xs:element name="wsa002" type="length1-30" minOccurs="1"/><!--发送方交易流水号 -->
        </xs:all>
    </xs:complexType>
    <!-- head节点的输入定义 -->
    <xs:complexType name="bodyDataElementType">
        <xs:all>
            <xs:element name="wsa004" type="length1-50" minOccurs="1"/><!-- 主要诊断编码 -->
            <xs:element name="wsa005" type="xs:string" minOccurs="1"/><!-- 年龄 -->
            <xs:element name="wsa006" type="xs:string" minOccurs="1"/><!-- 性别 -->
            <xs:element name="wsa007" type="xs:string" minOccurs="1"/><!-- 新生儿天数 -->
            <xs:element name="wsa008" type="xs:string" minOccurs="1"/><!-- 新生儿体重 -->
            <xs:element name="wsa010" type="xs:string" minOccurs="1"/><!-- 离院方式 -->
            <xs:element name="wsa011" type="xs:string" minOccurs="1"/><!-- 住院天数 -->
            <xs:element name="wsa012" type="xs:string" minOccurs="1"/><!-- 住院总费用 -->
            <xs:element name="wsa013s"><!-- 其他诊断编码 -->
                <xs:complexType>
                    <xs:choice maxOccurs="unbounded" minOccurs="0">
                        <xs:element name="row">
                            <xs:complexType>
                                <xs:all>
                                    <xs:element name="wsa013" type="length1-200"  minOccurs="1"/>
                                </xs:all>
                            </xs:complexType>
                        </xs:element>
                    </xs:choice>
                </xs:complexType>
            </xs:element>
            <xs:element name="wsa014s"><!-- 手术编码 -->
                <xs:complexType>
                    <xs:choice maxOccurs="unbounded" minOccurs="0">
                        <xs:element name="row">
                            <xs:complexType>
                                <xs:all>
                                    <xs:element name="wsa014" type="length1-200"  minOccurs="1"/>
                                </xs:all>
                            </xs:complexType>
                        </xs:element>
                    </xs:choice>
                </xs:complexType>
            </xs:element>
        </xs:all>
    </xs:complexType>
    <!--年龄(岁)-->
    <xs:simpleType name="age">
        <xs:restriction base="xs:integer">
            <xs:maxInclusive value="150"/>
            <xs:minInclusive value="0"/>
        </xs:restriction>
    </xs:simpleType>
    <!--性别-->
    <xs:simpleType name="sex">
        <xs:restriction base="xs:string">
            <xs:enumeration value="男"/>
            <xs:enumeration value="女"/>
        </xs:restriction>
    </xs:simpleType>
    <!--天数-->
    <xs:simpleType name="day">
        <xs:restriction base="xs:integer">
            <xs:maxInclusive value="365"/>
            <xs:minInclusive value="0"/>
        </xs:restriction>
    </xs:simpleType>
    <!--小数保留两位-->
    <xs:simpleType name="decimal2">
        <xs:restriction base="xs:decimal">
            <xs:fractionDigits value="2"/>
        </xs:restriction>
    </xs:simpleType>
    <!--离院方式-->
    <xs:simpleType name="lyfs">
        <xs:restriction base="xs:string">
            <xs:enumeration value="1"/>
            <xs:enumeration value="2"/>
            <xs:enumeration value="3"/>
            <xs:enumeration value="4"/>
            <xs:enumeration value="5"/>
            <xs:enumeration value="9"/>
        </xs:restriction>
    </xs:simpleType>
    <!--长度30-->
    <xs:simpleType name="length1-30">
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="30"/>
        </xs:restriction>
    </xs:simpleType>
    <!-- 交易代码 -->
    <xs:simpleType name="wsa001">
        <xs:restriction base="xs:string">
            <xs:pattern value="(C00[0-9]{2})"/>
            <xs:minLength value="1"/>
            <xs:maxLength value="8"/>
        </xs:restriction>
    </xs:simpleType>
    <!-- 长度50 -->
    <xs:simpleType name="length1-50">
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="50"/>
        </xs:restriction>
    </xs:simpleType>
    <!-- 长度200 -->
    <xs:simpleType name="length1-200">
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="200"/>
        </xs:restriction>
    </xs:simpleType>
</xs:schema>