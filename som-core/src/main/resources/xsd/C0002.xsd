<?xml version="1.0" encoding="gbk"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema">
	<xs:element name="input">
		<xs:complexType>
			<xs:choice maxOccurs="unbounded" minOccurs="0">
				<xs:element name="head">
					<xs:complexType>
						<xs:complexContent>
							<xs:extension base="dataElementType">
								<xs:attributeGroup
										ref="dataRequiredAttributeType"/>
							</xs:extension>
						</xs:complexContent>
					</xs:complexType>
				</xs:element>
				<xs:element name="body">
					<xs:complexType>
						<xs:choice maxOccurs="unbounded" minOccurs="1">
							<xs:element name="row">
								<xs:complexType>
									<xs:complexContent>
										<xs:extension base="bodyDataElementType">
											<xs:attributeGroup
													ref="dataRequiredAttributeType"/>
										</xs:extension>
									</xs:complexContent>
								</xs:complexType>
							</xs:element>
						</xs:choice>
					</xs:complexType>
				</xs:element>
			</xs:choice>
		</xs:complexType>
	</xs:element>

	<xs:attributeGroup name="dataRequiredAttributeType">
	</xs:attributeGroup>
	<!-- head节点的输入定义 -->
	<xs:complexType name="dataElementType">
		<xs:all>
			<xs:element name="wsa001" type="wsa001" minOccurs="1"/><!--交易代码 -->
			<xs:element name="wsa002" type="wsa002" minOccurs="1"/><!--发送方交易流水号 -->
			<xs:element name="wsa007" type="wsa007" minOccurs="1"/><!--病案首页类型 -->
		</xs:all>
	</xs:complexType>
	<!-- 交易代码 -->
	<xs:simpleType name="wsa001">
		<xs:restriction base="xs:string">
			<xs:pattern value="(C00[0-9]{2})"/>
			<xs:minLength value="1"/>
			<xs:maxLength value="8"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="wsa002">
		<xs:restriction base="xs:string">
			<xs:minLength value="1" />
			<xs:maxLength value="30" />
		</xs:restriction>
	</xs:simpleType>

	<!--病案首页类型 -->
	<xs:simpleType name="wsa007">
		<xs:restriction base="xs:string">
			<xs:length value="1"/>
		</xs:restriction>
	</xs:simpleType>

	<!-- body节点的输入定义 -->
	<xs:complexType name="bodyDataElementType">
		<xs:all>
			<xs:element name="username" type="xs:string" minOccurs="1"/> <!-- 机构名称 -->
			<xs:element name="basylx" type="xs:string" minOccurs="1"/> <!-- 病案首页类型 -->
			<xs:element name="ylfkfs" type="xs:string" minOccurs="0"/>     <!-- 医疗付款方式  -->
			<xs:element name="jkkh" type="xs:string" minOccurs="0" />         <!-- 健康卡号 -->
			<xs:element name="zycs" type="xs:string" minOccurs="0" /> 		<!-- 住院次数 -->
			<xs:element name="bah" type="bah" minOccurs="1"/> 			<!-- 病案号  -->
			<xs:element name="xm" type="xs:string" minOccurs="0" /> 			<!-- 姓名 -->
			<xs:element name="xb" type="xs:string" minOccurs="0" /> 			<!-- 性别 -->
			<xs:element name="csrq" type="xs:string" minOccurs="0" /> 		<!-- 出生日期 yyyy-mm-dd  -->
			<xs:element name="nl" type="xs:string" minOccurs="0" /> 			<!-- 年龄 -->
			<xs:element name="gj" type="xs:string" minOccurs="0" /> 			<!-- 国籍 -->
			<xs:element name="bzyzsnl" type="xs:string" minOccurs="0" />   <!-- (年龄不足 0 周岁的)年龄(月)  -->
			<xs:element name="xsecstz" type="xs:string" minOccurs="0" /> 	<!-- 新生儿出生体重(克) -->
			<xs:element name="xserytz" type="xs:string" minOccurs="0" /> 	<!-- 新生儿入院体重(克）  -->
			<xs:element name="csd" type="xs:string" minOccurs="0" /> 			<!-- 出生地 -->
			<xs:element name="gg" type="xs:string" minOccurs="0" /> 			<!-- 籍贯  -->
			<xs:element name="mz" type="xs:string" minOccurs="0" /> 			<!-- 民族 -->
			<xs:element name="sfzh" type="sfzh" minOccurs="1" /> 		<!-- 身份证号 -->
			<xs:element name="zy" type="xs:string" minOccurs="0" /> 			<!-- 职业 -->
			<xs:element name="hy" type="xs:string" minOccurs="0" /> 			<!-- 婚姻 -->
			<xs:element name="xzz" type="xs:string" minOccurs="0" /> 			<!-- 现住址 -->
			<xs:element name="dh" type="xs:string" minOccurs="0" /> 			<!-- 电话 -->
			<xs:element name="yb1" type="xs:string" minOccurs="0" /> 			<!-- 邮编 -->
			<xs:element name="hkdz" type="xs:string" minOccurs="0" /> 		<!-- 户口地址 -->
			<xs:element name="yb2" type="xs:string" minOccurs="0" /> 			<!-- 邮编 -->
			<xs:element name="gzdwjdz" type="xs:string" minOccurs="0" /> 	<!-- 工作单位及地址 -->
			<xs:element name="dwdh" type="xs:string" minOccurs="0" /> 		<!-- 单位电话 -->
			<xs:element name="yb3" type="xs:string" minOccurs="0" /> 			<!-- 邮编 -->
			<xs:element name="lxrxm" type="xs:string" minOccurs="0" /> 		<!-- 联系人姓名-->
			<xs:element name="gx" type="xs:string" minOccurs="0" /> 			<!-- 关系 -->
			<xs:element name="dz" type="xs:string" minOccurs="0" /> 			<!-- 地址 -->
			<xs:element name="dh2" type="xs:string" minOccurs="1" /> 			<!-- 电话 -->
			<xs:element name="rytj" type="xs:string" minOccurs="0" /> 		<!-- 入院途径 -->
			<xs:element name="rysj" type="rysj" minOccurs="1" /> 		<!-- 入院时间-->
			<xs:element name="rysjs" type="xs:string" minOccurs="0" /> 		<!-- 时 -->
			<xs:element name="rykbbm" type="xs:string" minOccurs="0" /> 		<!-- 入院科别编码  -->
			<xs:element name="rykbmc" type="xs:string" minOccurs="0" /> 		<!-- 入院科别名称  -->
			<xs:element name="rybf" type="xs:string" minOccurs="0" /> 		<!-- 入院病房 -->
			<xs:element name="zkkbbm" type="xs:string" minOccurs="0" /> 		<!-- 转科科别 -->
			<xs:element name="cysj" type="cysj" minOccurs="1" /> 		<!-- 出院时间  -->
			<xs:element name="cysjs" type="xs:string" minOccurs="0" /> 		<!-- 时-->
			<xs:element name="cykbbm" type="xs:string" minOccurs="0" /> 		<!-- 出院科别编码 -->
			<xs:element name="cykbmc" type="xs:string" minOccurs="0" /> 		<!-- 出院科别名称-->
			<xs:element name="cybf" type="xs:string" minOccurs="0" /> 		<!-- 出院病房 -->
			<xs:element name="sjzyts" type="xs:string" minOccurs="0" /> 	<!-- 实际住院(天)  -->
			<xs:element name="mzzd" type="xs:string" minOccurs="1"/> 		<!-- 门(急)诊诊断  -->
			<xs:element name="jbbm" type="xs:string" minOccurs="1"/> 		<!-- 疾病编码 -->
			<xs:element name="zyzd" type="xs:string" minOccurs="1"/> 		<!-- 主要诊断-->
			<xs:element name="jbdm" type="xs:string" minOccurs="1"/> 		<!-- 疾病编码 -->
			<xs:element name="rybq" type="xs:string" minOccurs="0" /> 		<!-- 入院病情 -->
			<xs:element name="cybq" type="xs:string" minOccurs="0" /> 		<!-- 出院病情 -->
			<xs:element name="qtzd8" type="xs:string" minOccurs="0" /> 		<!-- 其他诊断-->
			<xs:element name="jbdm8" type="xs:string" minOccurs="0" /> 		<!-- 疾病编码 -->
			<xs:element name="rybq8" type="xs:string" minOccurs="0" /> 		<!-- 入院病情 -->
			<xs:element name="cybq8" type="xs:string" minOccurs="0" /> 		<!-- 出院病情 -->
			<xs:element name="qtzd1" type="xs:string" minOccurs="0" /> 		<!-- 其他诊断-->
			<xs:element name="jbdm1" type="xs:string" minOccurs="0" /> 		<!-- 疾病编码 -->
			<xs:element name="rybq1" type="xs:string" minOccurs="0" /> 		<!-- 入院病情 -->
			<xs:element name="cybq1" type="xs:string" minOccurs="0" /> 		<!-- 出院病情 -->
			<xs:element name="qtzd9" type="xs:string" minOccurs="0" /> 		<!-- 其他诊断-->
			<xs:element name="jbdm9" type="xs:string" minOccurs="0" /> 		<!-- 疾病编码 -->
			<xs:element name="rybq9" type="xs:string" minOccurs="0" /> 		<!-- 入院病情 -->
			<xs:element name="cybq9" type="xs:string" minOccurs="0" /> 		<!-- 出院病情 -->
			<xs:element name="qtzd2" type="xs:string" minOccurs="0" /> 		<!-- 其他诊断-->
			<xs:element name="jbdm2" type="xs:string" minOccurs="0" /> 		<!-- 疾病编码 -->
			<xs:element name="rybq2" type="xs:string" minOccurs="0" /> 		<!-- 入院病情 -->
			<xs:element name="cybq2" type="xs:string" minOccurs="0" /> 		<!-- 出院病情 -->
			<xs:element name="qtzd10" type="xs:string" minOccurs="0" /> 	<!-- 其他诊断-->
			<xs:element name="jbdm10" type="xs:string" minOccurs="0" /> 	<!-- 疾病编码 -->
			<xs:element name="rybq10" type="xs:string" minOccurs="0" /> 	<!-- 入院病情 -->
			<xs:element name="cybq10" type="xs:string" minOccurs="0" /> 		<!-- 出院病情 -->
			<xs:element name="qtzd3" type="xs:string" minOccurs="0" /> 		<!-- 其他诊断-->
			<xs:element name="jbdm3" type="xs:string" minOccurs="0" /> 		<!-- 疾病编码 -->
			<xs:element name="rybq3" type="xs:string" minOccurs="0" /> 		<!-- 入院病情 -->
			<xs:element name="cybq3" type="xs:string" minOccurs="0" /> 		<!-- 出院病情 -->
			<xs:element name="qtzd11" type="xs:string" minOccurs="0" /> 	<!-- 其他诊断-->
			<xs:element name="jbdm11" type="xs:string" minOccurs="0" /> 	<!-- 疾病编码 -->
			<xs:element name="rybq11" type="xs:string" minOccurs="0" /> 	<!-- 入院病情 -->
			<xs:element name="cybq11" type="xs:string" minOccurs="0" /> 		<!-- 出院病情 -->
			<xs:element name="qtzd4" type="xs:string" minOccurs="0" /> 		<!-- 其他诊断-->
			<xs:element name="jbdm4" type="xs:string" minOccurs="0" /> 		<!-- 疾病编码 -->
			<xs:element name="rybq4" type="xs:string" minOccurs="0" /> 		<!-- 入院病情 -->
			<xs:element name="cybq4" type="xs:string" minOccurs="0" /> 		<!-- 出院病情 -->
			<xs:element name="qtzd12" type="xs:string" minOccurs="0" /> 	<!-- 其他诊断-->
			<xs:element name="jbdm12" type="xs:string" minOccurs="0" /> 	<!-- 疾病编码 -->
			<xs:element name="rybq12" type="xs:string" minOccurs="0" /> 	<!-- 入院病情 -->
			<xs:element name="cybq12" type="xs:string" minOccurs="0" /> 		<!-- 出院病情 -->
			<xs:element name="qtzd5" type="xs:string" minOccurs="0" /> 		<!-- 其他诊断-->
			<xs:element name="jbdm5" type="xs:string" minOccurs="0" /> 		<!-- 疾病编码 -->
			<xs:element name="rybq5" type="xs:string" minOccurs="0" /> 		<!-- 入院病情 -->
			<xs:element name="cybq5" type="xs:string" minOccurs="0" /> 		<!-- 出院病情 -->
			<xs:element name="qtzd13" type="xs:string" minOccurs="0" /> 	<!-- 其他诊断-->
			<xs:element name="jbdm13" type="xs:string" minOccurs="0" /> 	<!-- 疾病编码 -->
			<xs:element name="rybq13" type="xs:string" minOccurs="0" /> 	<!-- 入院病情 -->
			<xs:element name="cybq13" type="xs:string" minOccurs="0" /> 		<!-- 出院病情 -->
			<xs:element name="qtzd6" type="xs:string" minOccurs="0" /> 		<!-- 其他诊断-->
			<xs:element name="jbdm6" type="xs:string" minOccurs="0" /> 		<!-- 疾病编码 -->
			<xs:element name="rybq6" type="xs:string" minOccurs="0" /> 		<!-- 入院病情 -->
			<xs:element name="cybq6" type="xs:string" minOccurs="0" /> 		<!-- 出院病情 -->
			<xs:element name="qtzd14" type="xs:string" minOccurs="0" /> 	<!-- 其他诊断-->
			<xs:element name="jbdm14" type="xs:string" minOccurs="0" /> 	<!-- 疾病编码 -->
			<xs:element name="rybq14" type="xs:string" minOccurs="0" /> 	<!-- 入院病情 -->
			<xs:element name="cybq14" type="xs:string" minOccurs="0" /> 		<!-- 出院病情 -->
			<xs:element name="qtzd7" type="xs:string" minOccurs="0" /> 		<!-- 其他诊断-->
			<xs:element name="jbdm7" type="xs:string" minOccurs="0" /> 		<!-- 疾病编码 -->
			<xs:element name="rybq7" type="xs:string" minOccurs="0" /> 		<!-- 入院病情 -->
			<xs:element name="cybq7" type="xs:string" minOccurs="0" /> 		<!-- 出院病情 -->
			<xs:element name="qtzd15" type="xs:string" minOccurs="0" /> 	<!-- 其他诊断-->
			<xs:element name="jbdm15" type="xs:string" minOccurs="0" /> 	<!-- 疾病编码 -->
			<xs:element name="rybq15" type="xs:string" minOccurs="0" /> 	<!-- 入院病情 -->
			<xs:element name="cybq15" type="xs:string" minOccurs="0" /> 		<!-- 出院病情 -->
			<xs:element name="wbyy" type="xs:string" minOccurs="0" /> 		<!-- 中毒的外部原因 -->
			<xs:element name="h23" type="xs:string" minOccurs="0" /> 			<!-- 疾病编码 -->
			<xs:element name="blzd" type="xs:string" minOccurs="0" /> 		<!-- 病理诊断出 -->
			<xs:element name="jbmm" type="xs:string" minOccurs="0" /> 		<!-- 疾病编码-->
			<xs:element name="blh" type="xs:string" minOccurs="0" /> 			<!-- 病理号 -->
			<xs:element name="ywgm" type="xs:string" minOccurs="0" /> 		<!-- 药物过敏 -->
			<xs:element name="gmyw" type="xs:string" minOccurs="0" /> 		<!-- 过敏药物疾病 -->
			<xs:element name="swhzsj" type="xs:string" minOccurs="0" /> 	<!-- 死亡患者尸检 -->
			<xs:element name="xx" type="xs:string" minOccurs="0" /> 			<!-- 血型 -->
			<xs:element name="rh" type="xs:string" minOccurs="0" /> 			<!-- rh -->
			<xs:element name="kzr" type="xs:string" minOccurs="0" /> 			<!-- 科主任 -->
			<xs:element name="kzrdm" type="xs:string" minOccurs="0" /> 			<!-- 科主任代码 -->
			<xs:element name="zrys" type="xs:string" minOccurs="0" /> 		<!-- 主任（副主任）医师 -->
			<xs:element name="zrysdm" type="xs:string" minOccurs="0" /> 		<!-- 主任（副主任）医师代码 -->
			<xs:element name="zzys" type="xs:string" minOccurs="0" /> 		<!-- 主治医师病理号死亡患者尸检  -->
			<xs:element name="zzysdm1" type="xs:string" minOccurs="0" /> 		<!-- 主治医师病理号死亡患者尸检代码  -->
			<xs:element name="zyys" type="xs:string" minOccurs="0" /> 		<!-- 住院医师出院情况入院病情 -->
			<xs:element name="zyysdm" type="xs:string" minOccurs="0" /> 		<!-- 住院医师出院情况入院病情代码 -->
			<xs:element name="zrhs" type="xs:string" minOccurs="0" /> 		<!-- 责任护士 -->
			<xs:element name="zrhsdm" type="xs:string" minOccurs="0" /> 		<!-- 责任护士 -->
			<xs:element name="jxys" type="xs:string" minOccurs="0" /> 		<!-- 进修医师住 -->
			<xs:element name="jxysdm" type="xs:string" minOccurs="0" /> 		<!-- 进修医师住 -->
			<xs:element name="sxys" type="xs:string" minOccurs="0" /> 		<!-- 实习医师 -->
			<xs:element name="bmy" type="xs:string" minOccurs="0" /> 			<!-- 编码员 -->
			<xs:element name="bmydm" type="xs:string" minOccurs="0" /> 			<!-- 编码员 -->
			<xs:element name="bazl" type="xs:string" minOccurs="0" /> 		<!-- 病案质量-->
			<xs:element name="zkys" type="xs:string" minOccurs="0" /> 		<!-- 质控医师 -->
			<xs:element name="zkysdm" type="xs:string" minOccurs="0" /> 		<!-- 质控医师代码 -->
			<xs:element name="zkhs" type="xs:string" minOccurs="0" /> 		<!-- 质控护士-->
			<xs:element name="zkhsdm" type="xs:string" minOccurs="0" /> 		<!-- 质控护士代码-->
			<xs:element name="zkrq" type="xs:string" minOccurs="0" /> 		<!-- 质控日期 -->
			<xs:element name="ssjczbm1" type="xs:string" minOccurs="0" /> <!-- 手术及操作编码-->
			<xs:element name="ssjczrq1" type="xs:string" minOccurs="0" /> <!-- 手术及操作日期 -->
			<xs:element name="ssjb1" type="xs:string" minOccurs="0" /> 		<!-- 手术级别 -->
			<xs:element name="ssjczmc1" type="xs:string" minOccurs="0" /> <!-- 手术及操作名称 -->
			<xs:element name="sz1" type="xs:string" minOccurs="0" /> 			<!-- 术者 -->
			<xs:element name="yz1" type="xs:string" minOccurs="0" /> 			<!-- i 助  -->
			<xs:element name="ez1" type="xs:string" minOccurs="0" /> 			<!-- ii 助  -->
			<xs:element name="qkdj1" type="xs:string" minOccurs="0" /> 		<!-- 切口等级 -->
			<xs:element name="qkyhlb1" type="xs:string" minOccurs="0" /> 	<!-- 切口愈合类别-->
			<xs:element name="mzfs1" type="xs:string" minOccurs="0" /> 		<!-- 麻醉方式 -->
			<xs:element name="mzys1" type="xs:string" minOccurs="0" /> 		<!-- 麻醉医师 -->
			<xs:element name="ssjczbm2" type="xs:string" minOccurs="0" /> <!-- 手术及操作编码-->
			<xs:element name="ssjczrq2" type="xs:string" minOccurs="0" /> <!-- 手术及操作日期 -->
			<xs:element name="ssjb2" type="xs:string" minOccurs="0" /> 		<!-- 手术级别 -->
			<xs:element name="ssjczmc2" type="xs:string" minOccurs="0" /> <!-- 手术及操作名称 -->
			<xs:element name="sz2" type="xs:string" minOccurs="0" /> 			<!-- 术者 -->
			<xs:element name="yz2" type="xs:string" minOccurs="0" /> 			<!-- i 助  -->
			<xs:element name="ez2" type="xs:string" minOccurs="0" /> 			<!-- ii 助  -->
			<xs:element name="qkdj2" type="xs:string" minOccurs="0" /> 		<!-- 切口等级 -->
			<xs:element name="qkyhlb2" type="xs:string" minOccurs="0" /> 	<!-- 切口愈合类别-->
			<xs:element name="mzfs2" type="xs:string" minOccurs="0" /> 		<!-- 麻醉方式 -->
			<xs:element name="mzys2" type="xs:string" minOccurs="0" /> 		<!-- 麻醉医师 -->
			<xs:element name="ssjczbm3" type="xs:string" minOccurs="0" /> <!-- 手术及操作编码-->
			<xs:element name="ssjczrq3" type="xs:string" minOccurs="0" /> <!-- 手术及操作日期 -->
			<xs:element name="ssjb3" type="xs:string" minOccurs="0" /> 		<!-- 手术级别 -->
			<xs:element name="ssjczmc3" type="xs:string" minOccurs="0" /> <!-- 手术及操作名称 -->
			<xs:element name="sz3" type="xs:string" minOccurs="0" /> 			<!-- 术者 -->
			<xs:element name="yz3" type="xs:string" minOccurs="0" /> 			<!-- i 助  -->
			<xs:element name="ez3" type="xs:string" minOccurs="0" /> 			<!-- ii 助  -->
			<xs:element name="qkdj3" type="xs:string" minOccurs="0" /> 		<!-- 切口等级 -->
			<xs:element name="qkyhlb3" type="xs:string" minOccurs="0" /> 	<!-- 切口愈合类别-->
			<xs:element name="mzfs3" type="xs:string" minOccurs="0" /> 		<!-- 麻醉方式 -->
			<xs:element name="mzys3" type="xs:string" minOccurs="0" /> 		<!-- 麻醉医师 -->
			<xs:element name="ssjczbm4" type="xs:string" minOccurs="0" /> <!-- 手术及操作编码-->
			<xs:element name="ssjczrq4" type="xs:string" minOccurs="0" /> <!-- 手术及操作日期 -->
			<xs:element name="ssjb4" type="xs:string" minOccurs="0" /> 		<!-- 手术级别 -->
			<xs:element name="ssjczmc4" type="xs:string" minOccurs="0" /> <!-- 手术及操作名称 -->
			<xs:element name="sz4" type="xs:string" minOccurs="0" /> 			<!-- 术者 -->
			<xs:element name="yz4" type="xs:string" minOccurs="0" /> 			<!-- i 助  -->
			<xs:element name="ez4" type="xs:string" minOccurs="0" /> 			<!-- ii 助  -->
			<xs:element name="qkdj4" type="xs:string" minOccurs="0" /> 		<!-- 切口等级 -->
			<xs:element name="qkyhlb4" type="xs:string" minOccurs="0" /> 	<!-- 切口愈合类别-->
			<xs:element name="mzfs4" type="xs:string" minOccurs="0" /> 		<!-- 麻醉方式 -->
			<xs:element name="mzys4" type="xs:string" minOccurs="0" /> 		<!-- 麻醉医师 -->
			<xs:element name="ssjczbm5" type="xs:string" minOccurs="0" /> <!-- 手术及操作编码-->
			<xs:element name="ssjczrq5" type="xs:string" minOccurs="0" /> <!-- 手术及操作日期 -->
			<xs:element name="ssjb5" type="xs:string" minOccurs="0" /> 		<!-- 手术级别 -->
			<xs:element name="ssjczmc5" type="xs:string" minOccurs="0" /> <!-- 手术及操作名称 -->
			<xs:element name="sz5" type="xs:string" minOccurs="0" /> 			<!-- 术者 -->
			<xs:element name="yz5" type="xs:string" minOccurs="0" /> 			<!-- i 助  -->
			<xs:element name="ez5" type="xs:string" minOccurs="0" /> 			<!-- ii 助  -->
			<xs:element name="qkdj5" type="xs:string" minOccurs="0" /> 		<!-- 切口等级 -->
			<xs:element name="qkyhlb5" type="xs:string" minOccurs="0" /> 	<!-- 切口愈合类别-->
			<xs:element name="mzfs5" type="xs:string" minOccurs="0" /> 		<!-- 麻醉方式 -->
			<xs:element name="mzys5" type="xs:string" minOccurs="0" /> 		<!-- 麻醉医师 -->
			<xs:element name="ssjczbm6" type="xs:string" minOccurs="0" /> <!-- 手术及操作编码-->
			<xs:element name="ssjczrq6" type="xs:string" minOccurs="0" /> <!-- 手术及操作日期 -->
			<xs:element name="ssjb6" type="xs:string" minOccurs="0" /> 		<!-- 手术级别 -->
			<xs:element name="ssjczmc6" type="xs:string" minOccurs="0" /> <!-- 手术及操作名称 -->
			<xs:element name="sz6" type="xs:string" minOccurs="0" /> 			<!-- 术者 -->
			<xs:element name="yz6" type="xs:string" minOccurs="0"/> 			<!-- i 助  -->
			<xs:element name="ez6" type="xs:string" minOccurs="0"/> 			<!-- ii 助  -->
			<xs:element name="qkdj6" type="xs:string" minOccurs="0"/> 		<!-- 切口等级 -->
			<xs:element name="qkyhlb6" type="xs:string" minOccurs="0"/> 	<!-- 切口愈合类别-->
			<xs:element name="mzfs6" type="xs:string" minOccurs="0"/> 		<!-- 麻醉方式 -->
			<xs:element name="mzys6" type="xs:string" minOccurs="0"/> 		<!-- 麻醉医师 -->
			<xs:element name="ssjczbm7" type="xs:string" minOccurs="0"/> <!-- 手术及操作编码-->
			<xs:element name="ssjczrq7" type="xs:string" minOccurs="0"/> <!-- 手术及操作日期 -->
			<xs:element name="ssjb7" type="xs:string" minOccurs="0"/> 		<!-- 手术级别 -->
			<xs:element name="ssjczmc7" type="xs:string" minOccurs="0"/> <!-- 手术及操作名称 -->
			<xs:element name="sz7" type="xs:string" minOccurs="0"/> 			<!-- 术者 -->
			<xs:element name="yz7" type="xs:string" minOccurs="0"/> 			<!-- i 助  -->
			<xs:element name="ez7" type="xs:string" minOccurs="0"/> 			<!-- ii 助  -->
			<xs:element name="qkdj7" type="xs:string" minOccurs="0"/> 		<!-- 切口等级 -->
			<xs:element name="qkyhlb7" type="xs:string" minOccurs="0"/> 	<!-- 切口愈合类别-->
			<xs:element name="mzfs7" type="xs:string" minOccurs="0"/> 		<!-- 麻醉方式 -->
			<xs:element name="mzys7" type="xs:string" minOccurs="0"/> 		<!-- 麻醉医师 -->
			<xs:element name="lyfs" type="xs:string" minOccurs="0"/> 		<!-- 离院方式 -->
			<xs:element name="yzzy_yljg" type="xs:string" minOccurs="0"/> <!-- 医嘱转院，拟接收医疗机构名 称  -->
			<xs:element name="wsy_yljg" type="xs:string" minOccurs="0"/> <!-- 医嘱转社区卫生服务机构/乡镇 卫生院，拟接收医疗机构名称 -->
			<xs:element name="sfzzyjh" type="xs:string" minOccurs="0"/>   <!-- 是否有出院 31 天内再住院计划 手术情况 -->
			<xs:element name="md" type="xs:string" minOccurs="0"/> 			<!-- 目的 -->
			<xs:element name="ryq_t" type="xs:string" minOccurs="0"/> 		<!-- 颅脑损伤患者昏迷入院前时 间：天 -->
			<xs:element name="ryq_xs" type="xs:string" minOccurs="0"/> 	<!-- 颅脑损伤患者昏迷入院前时 间：小时  -->
			<xs:element name="ryq_f" type="xs:string" minOccurs="0"/> 		<!-- 颅脑损伤患者昏迷入院前时 间：分  -->
			<xs:element name="ryh_t" type="xs:string" minOccurs="0"/> 		<!-- 颅脑损伤患者昏迷入院后时 间：天  -->
			<xs:element name="ryh_xs" type="xs:string" minOccurs="0"/> 	<!-- 颅脑损伤患者昏迷入院后时 间：小时 -->
			<xs:element name="ryh_f" type="xs:string" minOccurs="0"/> 		<!-- 颅脑损伤患者昏迷入院后时 间：分  -->
			<xs:element name="zfy" type="xs:string" minOccurs="0" /> 			<!-- 住院费用(元)：总费用 -->
			<xs:element name="zfje" type="xs:string" minOccurs="0" /> 		<!-- 自付金额 -->
			<xs:element name="ylfuf" type="xs:string" minOccurs="0" /> 		<!-- 综合医疗服务类：(1)一般医疗 服务费 -->
			<xs:element name="zlczf" type="xs:string" minOccurs="0" /> 		<!-- 一般治疗操作费(2)  -->
			<xs:element name="hlf" type="xs:string" minOccurs="0" /> 			<!-- 护理费(3)住院费 -->
			<xs:element name="qtfy" type="xs:string" minOccurs="0" /> 		<!-- 其他费用(4)  -->
			<xs:element name="blzdf" type="xs:string" minOccurs="0" /> 		<!-- 诊断类：(5)病理诊断费  -->
			<xs:element name="syszdf" type="xs:string" minOccurs="0" /> 	<!--实验室诊断费(6) -->
			<xs:element name="yxxzdf" type="xs:string" minOccurs="0" /> 	<!-- 影像学诊断费(7)  -->
			<xs:element name="lczdxmf" type="xs:string" minOccurs="0" /> 	<!-- 临床诊断项目费(8)  -->
			<xs:element name="fsszlxmf" type="xs:string" minOccurs="0" /> <!-- 治疗类：(9)非手术治疗项目费  -->
			<xs:element name="wlzlf" type="xs:string" minOccurs="0" /> 		<!-- 临床物理治疗费(9)  -->
			<xs:element name="sszlf" type="xs:string" minOccurs="0" /> 		<!-- 手术治疗费(10)  -->
			<xs:element name="maf" type="xs:string" minOccurs="0" /> 			<!-- 麻醉费(10) -->
			<xs:element name="ssf" type="xs:string" minOccurs="0" /> 			<!--手术费(10) -->
			<xs:element name="kff" type="xs:string" minOccurs="0" /> 			<!-- 康复类：(11)康复费  -->
			<xs:element name="zyzlf" type="xs:string" minOccurs="0" /> 		<!-- 中医类:(12)中医治疗费  -->
			<xs:element name="xyf" type="xs:string" minOccurs="0" /> 			<!-- 西药类:(13)西药费  -->
			<xs:element name="kjywf" type="xs:string" minOccurs="0" /> 		<!-- 抗菌药物费(13)  -->
			<xs:element name="zcyf" type="xs:string" minOccurs="0" /> 		<!-- 中药类:(14)中成药费  -->
			<xs:element name="zcyf1" type="xs:string" minOccurs="0" /> 		<!-- 中草药费(15) -->
			<xs:element name="xf" type="xs:string" minOccurs="0" /> 			<!-- 血液和血液制品类:(16)血费 -->
			<xs:element name="bdblzpf" type="xs:string" minOccurs="0" /> 	<!-- 白蛋白类制品费(17)  -->
			<xs:element name="qdblzpf" type="xs:string" minOccurs="0" /> 	<!-- 球蛋白类制品费(18)  -->
			<xs:element name="nxyzlzpf" type="xs:string" minOccurs="0" /> <!-- 凝血因子类制品费(19)  -->
			<xs:element name="xbyzlzpf" type="xs:string" minOccurs="0" /> <!-- 细胞因子类制品费(20)  -->
			<xs:element name="hcyyclf" type="xs:string" minOccurs="0" /> 	<!-- 耗材类:(21)检查用一次性医用 材料费 -->
			<xs:element name="yyclf" type="xs:string" minOccurs="0" /> 		<!-- (22)治疗用一次性医用材料费 -->
			<xs:element name="ycxyyclf" type="xs:string" minOccurs="0" /> <!-- (23)手术用一次性医用材料费 -->
			<xs:element name="qtf" type="xs:string" minOccurs="0" /> 			<!-- 其他类：(24)其他费 -->
			<xs:element name="mzzd_zyzd" type="xs:string" minOccurs="0" /> 			<!-- 门（急）诊诊断名称（中医） -->
			<xs:element name="jbbm_zyzd" type="xs:string" minOccurs="0" /> 			<!-- 门（急）诊诊断编码（中医） -->
			<xs:element name="zyzd_jbbm" type="xs:string" minOccurs="0" /> 			<!-- 出院主要诊断编码（中医） -->
			<xs:element name="zb" type="xs:string" minOccurs="0" /> 			<!-- 出院主要诊断名称（中医） -->
			<xs:element name="zyzd_rybq" type="xs:string" minOccurs="0" /> 			<!-- 出院主要诊断入院病情（中医） -->
			<xs:element name="zyzd_cybq" type="xs:string" minOccurs="0" /> 			<!-- 出院主要诊断名称（中医） -->
			<xs:element name="bzlzf" type="xs:string" minOccurs="0" /> 			<!-- 中医辨证论治费 -->
			<xs:element name="zyblzhzf" type="xs:string" minOccurs="0" /> 			<!-- 中医辨证论治会诊费 -->
			<xs:element name="zyl_zyzd" type="xs:string" minOccurs="0" /> 			<!-- 出院主要诊断名称（中医） -->
			<xs:element name="zyqt" type="xs:string" minOccurs="0" /> 			<!-- 出院主要诊断名称（中医） -->
			<xs:element name="zytstpjg" type="xs:string" minOccurs="0" /> 			<!-- 出院主要诊断名称（中医） -->
			<xs:element name="bzss" type="xs:string" minOccurs="0" /> 			<!-- 出院主要诊断名称（中医） -->
			<xs:element name="zyzjf" type="xs:string" minOccurs="0" /> 			<!-- 出院主要诊断名称（中医） -->
			<xs:element name="zcyjf" type="xs:string" minOccurs="0" /> 			<!-- 出院主要诊断名称（中医） -->
			<xs:element name="zygczl" type="xs:string" minOccurs="0" /> 			<!-- 出院主要诊断名称（中医） -->
			<xs:element name="zygs" type="xs:string" minOccurs="0" /> 			<!-- 出院主要诊断名称（中医） -->
			<xs:element name="zytnzl" type="xs:string" minOccurs="0" />
			<xs:element name="zytszl" type="xs:string" minOccurs="0" />
			<xs:element name="zywz" type="xs:string" minOccurs="0" />
			<xs:element name="hxjsysj_t" type="xs:string" minOccurs="0" />
			<xs:element name="hxjsysj_xs" type="xs:string" minOccurs="0" />
			<xs:element name="hxjsysj_fz" type="xs:string" minOccurs="0" />
			<xs:element name="xserylx" type="xs:string" minOccurs="0" />
			<xs:element name="tsrylx" type="xs:string" minOccurs="0" />
			<xs:element name="zyyllx" type="xs:string" minOccurs="0" />
			<xs:element name="zllb" type="xs:string" minOccurs="0" />
			<xs:element name="sxpz" type="xs:string" minOccurs="0" />
			<xs:element name="sxl" type="xs:string" minOccurs="0" />
			<xs:element name="sxjldw" type="xs:string" minOccurs="0" />
			<xs:element name="tjhlts" type="xs:string" minOccurs="0" />
			<xs:element name="yjhlts" type="xs:string" minOccurs="0" />
			<xs:element name="ejhlts" type="xs:string" minOccurs="0" />
			<xs:element name="sjhlts" type="xs:string" minOccurs="0" />
			<xs:element name="zzysxm" type="xs:string" minOccurs="0" />
			<xs:element name="zzysdm" type="xs:string" minOccurs="0" />
			<xs:element name="mzmtzdkb" type="xs:string" minOccurs="0" />
			<xs:element name="mzmtjzsj" type="xs:string" minOccurs="0" />

			<!-- 中医其他诊断 -->
			<xs:element name="jbbm_qtzd1" type="xs:string" minOccurs="0" /> <!-- 中医其他诊断编码1 -->
			<xs:element name="mzzd_qtzd1" type="xs:string" minOccurs="0" /> <!-- 中医其他诊断名称1 -->
			<xs:element name="qtzd_rybq1" type="xs:string" minOccurs="0" /> <!-- 中医其他诊断入院病情1 -->
			<xs:element name="qtzd_cybq1" type="xs:string" minOccurs="0" /> <!-- 中医其他诊断出院病情1 -->
			<xs:element name="jbbm_qtzd2" type="xs:string" minOccurs="0" /> <!-- 中医其他诊断编码2 -->
			<xs:element name="mzzd_qtzd2" type="xs:string" minOccurs="0" /> <!-- 中医其他诊断名称2 -->
			<xs:element name="qtzd_rybq2" type="xs:string" minOccurs="0" /> <!-- 中医其他诊断入院病情2 -->
			<xs:element name="qtzd_cybq2" type="xs:string" minOccurs="0" /> <!-- 中医其他诊断出院病情2 -->
			<xs:element name="jbbm_qtzd3" type="xs:string" minOccurs="0" /> <!-- 中医其他诊断编码3 -->
			<xs:element name="mzzd_qtzd3" type="xs:string" minOccurs="0" /> <!-- 中医其他诊断名称3 -->
			<xs:element name="qtzd_rybq3" type="xs:string" minOccurs="0" /> <!-- 中医其他诊断入院病情3 -->
			<xs:element name="qtzd_cybq3" type="xs:string" minOccurs="0" /> <!-- 中医其他诊断出院病情3 -->
			<xs:element name="jbbm_qtzd4" type="xs:string" minOccurs="0" /> <!-- 中医其他诊断编码4 -->
			<xs:element name="mzzd_qtzd4" type="xs:string" minOccurs="0" /> <!-- 中医其他诊断名称4 -->
			<xs:element name="qtzd_rybq4" type="xs:string" minOccurs="0" /> <!-- 中医其他诊断入院病情4 -->
			<xs:element name="qtzd_cybq4" type="xs:string" minOccurs="0" /> <!-- 中医其他诊断出院病情4 -->
			<xs:element name="jbbm_qtzd5" type="xs:string" minOccurs="0" /> <!-- 中医其他诊断编码5 -->
			<xs:element name="mzzd_qtzd5" type="xs:string" minOccurs="0" /> <!-- 中医其他诊断名称5 -->
			<xs:element name="qtzd_rybq5" type="xs:string" minOccurs="0" /> <!-- 中医其他诊断入院病情5 -->
			<xs:element name="qtzd_cybq5" type="xs:string" minOccurs="0" /> <!-- 中医其他诊断出院病情5 -->
			<xs:element name="jbbm_qtzd6" type="xs:string" minOccurs="0" /> <!-- 中医其他诊断编码6 -->
			<xs:element name="mzzd_qtzd6" type="xs:string" minOccurs="0" /> <!-- 中医其他诊断名称6 -->
			<xs:element name="qtzd_rybq6" type="xs:string" minOccurs="0" /> <!-- 中医其他诊断入院病情6 -->
			<xs:element name="qtzd_cybq6" type="xs:string" minOccurs="0" /> <!-- 中医其他诊断出院病情6 -->
			<xs:element name="jbbm_qtzd7" type="xs:string" minOccurs="0" /> <!-- 中医其他诊断编码7 -->
			<xs:element name="mzzd_qtzd7" type="xs:string" minOccurs="0" /> <!-- 中医其他诊断名称7 -->
			<xs:element name="qtzd_rybq7" type="xs:string" minOccurs="0" /> <!-- 中医其他诊断入院病情7 -->
			<xs:element name="qtzd_cybq7" type="xs:string" minOccurs="0" /> <!-- 中医其他诊断出院病情7 -->


			<xs:element name="mzmtlist" maxOccurs="1" minOccurs="0"> <!-- 门诊慢特病诊疗信息 -->
				<xs:complexType>
					<xs:choice maxOccurs="unbounded" minOccurs="0">
						<xs:element name="row">
							<xs:complexType>
								<xs:complexContent>
									<xs:extension base="mzmtlist">
									</xs:extension>
								</xs:complexContent>
							</xs:complexType>
						</xs:element>
					</xs:choice>
				</xs:complexType>
			</xs:element>
			<xs:element name="zzjhslist" maxOccurs="1" minOccurs="0"> <!-- 重症监护室信息 -->
				<xs:complexType>
					<xs:choice maxOccurs="unbounded" minOccurs="0">
						<xs:element name="row">
							<xs:complexType>
								<xs:complexContent>
									<xs:extension base="zzjhslist">
									</xs:extension>
								</xs:complexContent>
							</xs:complexType>
						</xs:element>
					</xs:choice>
				</xs:complexType>
			</xs:element>

			<xs:element name="ysqdlsh" type="xs:string" minOccurs="0" />
			<xs:element name="yljgdm" type="xs:string" minOccurs="0" />
			<xs:element name="ybjsdj" type="xs:string" minOccurs="0" />
			<xs:element name="ybbh" type="xs:string" minOccurs="0" />
			<xs:element name="sbsj" type="xs:string" minOccurs="0" />
			<xs:element name="cbd" type="xs:string" minOccurs="0" />
			<xs:element name="yblx" type="xs:string" minOccurs="0" />
			<xs:element name="ywlsh" type="xs:string" minOccurs="0" />
			<xs:element name="pjdm" type="xs:string" minOccurs="0" />
			<xs:element name="pjhm" type="xs:string" minOccurs="0" />
			<xs:element name="jskssj" type="xs:string" minOccurs="0" />
			<xs:element name="jsjssj" type="xs:string" minOccurs="0" />

			<xs:element name="ylfylist" maxOccurs="1" minOccurs="0"> <!-- 医保结算清单医疗费用list -->
				<xs:complexType>
					<xs:choice maxOccurs="unbounded" minOccurs="0">
						<xs:element name="row">
							<xs:complexType>
								<xs:complexContent>
									<xs:extension base="ylfylist">
									</xs:extension>
								</xs:complexContent>
							</xs:complexType>
						</xs:element>
					</xs:choice>
				</xs:complexType>
			</xs:element>

			<xs:element name="ybzffs" type="xs:string" minOccurs="0" />		<!-- 医保支付方式 -->
			<xs:element name="yljgtbbm" type="xs:string" minOccurs="0" />	<!-- 医疗机构填报部门 -->
			<xs:element name="yljgtbr" type="xs:string" minOccurs="0" />	<!-- 医疗机构填报部门 -->
			<xs:element name="grzf" type="xs:string" minOccurs="0" />	<!-- 个人自付 -->
			<xs:element name="grzf1" type="xs:string" minOccurs="0" />	<!-- 个人自费 -->
			<xs:element name="grzhzf" type="xs:string" minOccurs="0" />	<!-- 个人账户支付 -->
			<xs:element name="grxjzf" type="xs:string" minOccurs="0" /> <!-- 个人现金支付 -->

			<xs:element name="ybjjzflist" maxOccurs="1" minOccurs="0"> <!-- 医保基金支付list -->
				<xs:complexType>
					<xs:choice maxOccurs="unbounded" minOccurs="0">
						<xs:element name="row">
							<xs:complexType>
								<xs:complexContent>
									<xs:extension base="ybjjzflist">
									</xs:extension>
								</xs:complexContent>
							</xs:complexType>
						</xs:element>
					</xs:choice>
				</xs:complexType>
			</xs:element>

		</xs:all>
	</xs:complexType>

	<!-- 门诊慢特病诊疗信息 -->
	<xs:complexType name="mzmtlist">
		<xs:all>
			<xs:element name="mzmtzdmc" type="xs:string"  minOccurs="0" />   <!-- 门诊慢特诊断名称  -->
			<xs:element name="mzmtzddm" type="xs:string"  minOccurs="0" />   <!-- 门诊慢特诊断代码 -->
			<xs:element name="mzmtssjczmc" type="xs:string"  minOccurs="0" />   <!-- 门诊慢特诊断代码  -->
			<xs:element name="mzmtssjczdm" type="xs:string"  minOccurs="0" />   <!-- 门诊慢特手术及操作代码 -->
		</xs:all>
	</xs:complexType>

	<!-- 重症监护室信息 -->
	<xs:complexType name="zzjhslist">
		<xs:all>
			<xs:element name="zzjhbflx" type="xs:string"  minOccurs="0" />   <!-- 重症监护病房类型  -->
			<xs:element name="zzjhjrsj" type="xs:string"  minOccurs="0" />   <!-- 重症监护进入时间 -->
			<xs:element name="zzjhtcsj" type="xs:string"  minOccurs="0" />   <!-- 重症监护进入时间  -->
			<xs:element name="zzjhhjsc" type="xs:string"  minOccurs="0" />   <!-- 重症监护进入时间 -->
		</xs:all>
	</xs:complexType>

	<!-- 医疗费用信息 -->
	<xs:complexType name="ylfylist">
		<xs:all>
			<xs:element name="xmmc" type="xs:string"  minOccurs="0" />   <!-- 项目名称（费用名称）  -->
			<xs:element name="je" type="xs:string"  minOccurs="0" />   <!-- 金额 -->
			<xs:element name="jl" type="xs:string"  minOccurs="0" />   <!-- 甲类  -->
			<xs:element name="yl" type="xs:string"  minOccurs="0" />   <!-- 乙类 -->
			<xs:element name="zf" type="xs:string"  minOccurs="0" />   <!-- 自费 -->
			<xs:element name="qt" type="xs:string"  minOccurs="0" />   <!-- 其他 -->
		</xs:all>
	</xs:complexType>

	<!-- 医保基金支付信息 -->
	<xs:complexType name="ybjjzflist">
		<xs:all>
			<xs:element name="jjzflx" type="xs:string"  minOccurs="0" />   <!-- 基金支付类型  -->
			<xs:element name="jjzfje" type="xs:string"  minOccurs="0" />   <!-- 基金支付金额 -->
		</xs:all>
	</xs:complexType>

	<!-- username 机构名称 varchar(60) N  -->
	<xs:simpleType name="username">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="60"/>
		</xs:restriction>
	</xs:simpleType>

	<!-- 病案首页类型  -->
	<xs:simpleType name="basylx">
		<xs:restriction base="xs:string">
			<xs:length value="1"/>
		</xs:restriction>
	</xs:simpleType>

	<!-- ylfkfs 医疗付款方式   varchar(100)  Y -->
	<xs:simpleType name="ylfkfs">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="100"/>
		</xs:restriction>
	</xs:simpleType>

	<!-- jkkh 健康卡号   varchar(100)  Y -->
	<xs:simpleType name="jkkh">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="100"/>
		</xs:restriction>
	</xs:simpleType>

	<!-- zycs 住院次数  varchar(100)  Y -->
	<xs:simpleType name="zycs">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="100"/>
		</xs:restriction>
	</xs:simpleType>

	<!-- bah 病案号  varchar(100)  Y -->
	<xs:simpleType name="bah">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="100"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- xm 姓名  varchar(100)  Y -->
	<xs:simpleType name="xm">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="100"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- xb 性别  varchar(100)  Y -->
	<xs:simpleType name="xb">
		<xs:restriction base="xs:string">
			<xs:pattern value="(|0|1|2|9)" />
			<xs:minLength value="0" />
			<xs:maxLength value="1" />
		</xs:restriction>
	</xs:simpleType>


	<!-- csrq 出生日期 yyyy-mm-dd  varchar(12) Y -->
	<xs:simpleType name="csrq">
		<xs:restriction base="xs:string">
			<xs:pattern value="(|[1-9]\d{3}-(0[1-9]|1[0-2])-(0[1-9]|[1-2][0-9]|3[0-1]))"/>
			<xs:minLength value="0"/>
			<xs:maxLength value="12"/>
		</xs:restriction>
	</xs:simpleType>

	<!-- nl 年龄 number(10) Y -->
	<xs:simpleType name="nl">
		<xs:restriction base="xs:string">
			<xs:pattern value="(|[0-9]*)"/>
			<xs:minLength value="0"/>
			<xs:maxLength value="4"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- gj 国籍 varchar(100)  Y -->
	<xs:simpleType name="gj">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="100"/>
		</xs:restriction>
	</xs:simpleType>

	<!-- bzyzsnl  年龄不足 1 周岁的)年龄(月) number(4)   Y -->
	<xs:simpleType name="bzyzsnl">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="4"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- xsecstz  新生儿出生体重(克) number(12,2)   Y -->
	<xs:simpleType name="xsecstz">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="12"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- xserytz  新生儿入院体重(克） number(12,2)   Y -->
	<xs:simpleType name="xserytz">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="12"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- csd  出生地  varchar(200)  Y -->
	<xs:simpleType name="csd">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="200"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- gg  籍贯  varchar(200)  Y -->
	<xs:simpleType name="gg">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="200"/>
		</xs:restriction>
	</xs:simpleType>

	<!-- mz  民族  varchar(100)  Y -->
	<xs:simpleType name="mz">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="100"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- sfzh  身份证号   varchar(100)  Y -->
	<xs:simpleType name="sfzh">
		<xs:restriction base="xs:string">
			<xs:pattern value="(\d{15})|(\d{18})|(\d{17}(\d|X|x))"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- zy  职业   varchar(100)  Y -->
	<xs:simpleType name="zy">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="100"/>
		</xs:restriction>
	</xs:simpleType>

	<!-- hy  婚姻   varchar(100)  Y -->
	<xs:simpleType name="hy">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="100"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- xzz  现住址   varchar(100)  Y -->
	<xs:simpleType name="xzz">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="100"/>
		</xs:restriction>
	</xs:simpleType>



	<!-- dh  电话  varchar(100)  Y -->
	<xs:simpleType name="dh">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="100"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- yb1  邮编  varchar(100)  Y -->
	<xs:simpleType name="yb1">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="100"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- hkdz  户口地址  varchar(100)  Y -->
	<xs:simpleType name="hkdz">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="100"/>
		</xs:restriction>
	</xs:simpleType>

	<!-- yb2  邮编  varchar(100)  Y -->
	<xs:simpleType name="yb2">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="100"/>
		</xs:restriction>
	</xs:simpleType>

	<!-- gzdwjdz  工作单位及地址   varchar(100)  Y -->
	<xs:simpleType name="gzdwjdz">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="100"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- dwdh  单位电话   varchar(100)  Y -->
	<xs:simpleType name="dwdh">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="100"/>
		</xs:restriction>
	</xs:simpleType>

	<!-- yb3  邮编   varchar(100)  Y -->
	<xs:simpleType name="yb3">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="100"/>
		</xs:restriction>
	</xs:simpleType>

	<!-- lxrxm  联系人姓名   varchar(100)  Y -->
	<xs:simpleType name="lxrxm">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="100"/>
		</xs:restriction>
	</xs:simpleType>



	<!-- gx  关系   varchar(100)  Y -->
	<xs:simpleType name="gx">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="100"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- dz  地址   varchar(100)  Y -->
	<xs:simpleType name="dz">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="100"/>
		</xs:restriction>
	</xs:simpleType>

	<!-- dh2  电话   varchar(100)  Y -->
	<xs:simpleType name="dh2">
		<xs:restriction base="xs:string">
			<xs:length value="11"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- rytj  入院途径   varchar(100)  Y -->
	<xs:simpleType name="rytj">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="100"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- rysj  入院时间   varchar(12)   Y -->
	<xs:simpleType name="rysj">
		<xs:restriction base="xs:string">
<!--			<xs:pattern value="(|[1-9]\d{3}-(0[1-9]|1[0-2])-(0[1-9]|[1-2][0-9]|3[0-1]))"/>-->
			<xs:minLength value="10"/>
			<xs:maxLength value="20"/>
		</xs:restriction>
	</xs:simpleType>

	<!-- rysjs  时   number(24)   Y -->
	<xs:simpleType name="rysjs">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="24"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- rykbbm  入院科别   varchar(100)    Y -->
	<xs:simpleType name="rykbbm">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="100"/>
		</xs:restriction>
	</xs:simpleType>

	<!-- rybf  入院病房    varchar(100)    Y -->
	<xs:simpleType name="rybf">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="100"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- zkkbbm  转科科别    varchar(100)    Y -->
	<xs:simpleType name="zkkbbm">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="100"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- cysj  出院时间    varchar(12)    Y -->
	<xs:simpleType name="cysj">
		<xs:restriction base="xs:string"  >
<!--			<xs:pattern value="(|[1-9]\d{3}-(0[1-9]|1[0-2])-(0[1-9]|[1-2][0-9]|3[0-1]))"/>-->
			<xs:minLength value="10"/>
			<xs:maxLength value="20"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- cysjs  时  number(24)    Y -->
	<xs:simpleType name="cysjs">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="24"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- cykbbm  出院科别  varchar(100)    Y -->
	<xs:simpleType name="cykbbm">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="100"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- cybf  出院病房  varchar(100)    Y -->
	<xs:simpleType name="cybf">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="100"/>
		</xs:restriction>
	</xs:simpleType>

	<!-- sjzyts  实际住院(天)   varchar(100)    Y -->
	<xs:simpleType name="sjzyts">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="100"/>
		</xs:restriction>
	</xs:simpleType>

	<!-- mzzd  门(急)诊诊断    varchar(200)    Y -->
	<xs:simpleType name="mzzd">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="200"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- jbbm  疾病编码     varchar(100)    Y -->
	<xs:simpleType name="jbbm">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="100"/>
		</xs:restriction>
	</xs:simpleType>

	<!-- zyzd  主要诊断     varchar(200)    Y -->
	<xs:simpleType name="zyzd">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="200"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- jbdm  疾病编码     varchar(100)     Y -->
	<xs:simpleType name="jbdm">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="100"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- rybq  入院病情     varchar(100)     Y -->
	<xs:simpleType name="rybq">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="100"/>
		</xs:restriction>
	</xs:simpleType>

	<!-- cybq  出院病情     varchar(100)     Y -->
	<xs:simpleType name="cybq">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="100"/>
		</xs:restriction>
	</xs:simpleType>

	<!-- qtzd8  其他诊断    varchar(200)     Y -->
	<xs:simpleType name="qtzd8">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="200"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- jbdm8  疾病编码    varchar(100)     Y -->
	<xs:simpleType name="jbdm8">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="100"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- rybq8  入院病情   varchar(100)     Y -->
	<xs:simpleType name="rybq8">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="100"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- qtzd1  其他诊断   varchar(200)     Y -->
	<xs:simpleType name="qtzd1">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="200"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- jbdm1  其他诊断   varchar(100)     Y -->
	<xs:simpleType name="jbdm1">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="100"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- rybq1  入院病情   varchar(100)     Y -->
	<xs:simpleType name="rybq1">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="100"/>
		</xs:restriction>
	</xs:simpleType>



	<!-- qtzd9  其他诊断   varchar(200)     Y -->
	<xs:simpleType name="qtzd9">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="200"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- jbdm9  疾病编码   varchar(100)     Y -->
	<xs:simpleType name="jbdm9">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="100"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- rybq9  入院病情   varchar(100)     Y -->
	<xs:simpleType name="rybq9">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="100"/>
		</xs:restriction>
	</xs:simpleType>

	<!-- qtzd2  其他诊断   varchar(200)     Y -->
	<xs:simpleType name="qtzd2">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="200"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- jbdm2  疾病编码   varchar(200)     Y -->
	<xs:simpleType name="jbdm2">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="200"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- rybq2  入院病情   varchar(100)     Y -->
	<xs:simpleType name="rybq2">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="100"/>
		</xs:restriction>
	</xs:simpleType>

	<!-- qtzd10  其他诊断   varchar(200)     Y -->
	<xs:simpleType name="qtzd10">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="200"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- jbdm10  疾病编码   varchar(100)     Y -->
	<xs:simpleType name="jbdm10">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="100"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- rybq10  入院病情   varchar(100)     Y -->
	<xs:simpleType name="rybq10">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="100"/>
		</xs:restriction>
	</xs:simpleType>

	<!-- qtzd3  其他诊断   varchar(200)     Y -->
	<xs:simpleType name="qtzd3">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="200"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- jbdm3  疾病编码   varchar(100)     Y -->
	<xs:simpleType name="jbdm3">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="100"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- rybq3  入院病情   varchar(100)     Y -->
	<xs:simpleType name="rybq3">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="100"/>
		</xs:restriction>
	</xs:simpleType>

	<!-- qtzd11  其他诊断   varchar(200)     Y -->
	<xs:simpleType name="qtzd11">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="200"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- jbdm11  疾病编码   varchar(100)     Y -->
	<xs:simpleType name="jbdm11">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="100"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- rybq11  入院病情   varchar(100)     Y -->
	<xs:simpleType name="rybq11">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="100"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- qtzd4  其他诊断   varchar(200)     Y -->
	<xs:simpleType name="qtzd4">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="200"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- jbdm4  疾病编码   varchar(100)     Y -->
	<xs:simpleType name="jbdm4">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="100"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- rybq4  入院病情   varchar(100)     Y -->
	<xs:simpleType name="rybq4">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="100"/>
		</xs:restriction>
	</xs:simpleType>

	<!-- qtzd12  其他诊断   varchar(200)     Y -->
	<xs:simpleType name="qtzd12">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="200"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- jbdm12  疾病编码   varchar(100)     Y -->
	<xs:simpleType name="jbdm12">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="100"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- rybq12  入院病情   varchar(100)     Y -->
	<xs:simpleType name="rybq12">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="100"/>
		</xs:restriction>
	</xs:simpleType>

	<!-- qtzd5  其他诊断   varchar(200)     Y -->
	<xs:simpleType name="qtzd5">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="200"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- jbdm5  疾病编码   varchar(100)     Y -->
	<xs:simpleType name="jbdm5">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="100"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- rybq5  入院病情   varchar(100)     Y -->
	<xs:simpleType name="rybq5">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="100"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- qtzd13  其他诊断   varchar(200)     Y -->
	<xs:simpleType name="qtzd13">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="200"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- jbdm13  疾病编码   varchar(100)     Y -->
	<xs:simpleType name="jbdm13">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="100"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- rybq13  入院病情   varchar(100)     Y -->
	<xs:simpleType name="rybq13">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="100"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- qtzd6  其他诊断   varchar(200)     Y -->
	<xs:simpleType name="qtzd6">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="200"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- jbdm6  疾病编码   varchar(100)     Y -->
	<xs:simpleType name="jbdm6">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="100"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- rybq6  入院病情   varchar(100)     Y -->
	<xs:simpleType name="rybq6">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="100"/>
		</xs:restriction>
	</xs:simpleType>

	<!-- qtzd14  其他诊断   varchar(200)     Y -->
	<xs:simpleType name="qtzd14">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="200"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- jbdm14  疾病编码   varchar(100)     Y -->
	<xs:simpleType name="jbdm14">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="100"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- rybq14  入院病情   varchar(100)     Y -->
	<xs:simpleType name="rybq14">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="100"/>
		</xs:restriction>
	</xs:simpleType>

	<!-- qtzd7  其他诊断   varchar(200)     Y -->
	<xs:simpleType name="qtzd7">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="200"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- jbdm7  疾病编码   varchar(100)     Y -->
	<xs:simpleType name="jbdm7">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="100"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- rybq7  入院病情   varchar(100)     Y -->
	<xs:simpleType name="rybq7">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="100"/>
		</xs:restriction>
	</xs:simpleType>

	<!-- qtzd15  其他诊断   varchar(200)     Y -->
	<xs:simpleType name="qtzd15">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="200"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- jbdm15  疾病编码   varchar(100)     Y -->
	<xs:simpleType name="jbdm15">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="100"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- rybq15  入院病情   varchar(100)     Y -->
	<xs:simpleType name="rybq15">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="100"/>
		</xs:restriction>
	</xs:simpleType>

	<!-- wbyy  中毒的外部原因   varchar(254)     Y -->
	<xs:simpleType name="wbyy">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="254"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- h23  疾病编码   varchar(100)     Y -->
	<xs:simpleType name="h23">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="100"/>
		</xs:restriction>
	</xs:simpleType>

	<!-- blzd  病理诊断出    varchar(100)     Y -->
	<xs:simpleType name="blzd">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="100"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- jbmm  疾病编码    varchar(100)     Y -->
	<xs:simpleType name="jbmm">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="100"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- blh  病理号    varchar(100)     Y -->
	<xs:simpleType name="blh">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="100"/>
		</xs:restriction>
	</xs:simpleType>

	<!-- ywgm  药物过敏    varchar(254)     Y -->
	<xs:simpleType name="ywgm">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="254"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- gmyw  过敏药物疾病    varchar(100)     Y -->
	<xs:simpleType name="gmyw">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="100"/>
		</xs:restriction>
	</xs:simpleType>

	<!-- swhzsj  死亡患者尸检    varchar(100)     Y -->
	<xs:simpleType name="swhzsj">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="100"/>
		</xs:restriction>
	</xs:simpleType>

	<!-- xx  血型    varchar(100)     Y -->
	<xs:simpleType name="xx">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="100"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- rh  rh    varchar(100)     Y -->
	<xs:simpleType name="rh">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="100"/>
		</xs:restriction>
	</xs:simpleType>

	<!-- kzr  科主任    varchar(100)     Y -->
	<xs:simpleType name="kzr">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="100"/>
		</xs:restriction>
	</xs:simpleType>

	<!-- zrys  主任（副主任）医师     varchar(100)     Y -->
	<xs:simpleType name="zrys">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="100"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- zzys  主治医师病理号死亡患者尸检     varchar(100)     Y -->
	<xs:simpleType name="zzys">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="100"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- zyys  住院医师出院情况入院病情     varchar(100)     Y -->
	<xs:simpleType name="zyys">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="100"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- zrhs  责任护士     varchar(100)     Y -->
	<xs:simpleType name="zrhs">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="100"/>
		</xs:restriction>
	</xs:simpleType>

	<!-- jxys  进修医师住     varchar(100)     Y -->
	<xs:simpleType name="jxys">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="100"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- sxys  实习医师     varchar(100)     Y -->
	<xs:simpleType name="sxys">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="100"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- bmy  编码员    varchar(100)     Y -->
	<xs:simpleType name="bmy">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="100"/>
		</xs:restriction>
	</xs:simpleType>

	<!-- bazl  病案质量    varchar(100)     Y -->
	<xs:simpleType name="bazl">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="100"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- zkys  质控医师    varchar(100)     Y -->
	<xs:simpleType name="zkys">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="100"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- zkhs  质控护士    varchar(100)     Y -->
	<xs:simpleType name="zkhs">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="100"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- zkrq  质控日期    varchar(12)     Y -->
	<xs:simpleType name="zkrq">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="12"/>
		</xs:restriction>
	</xs:simpleType>

	<!-- ssjczbm1  手术及操作编码    varchar(100)     Y -->
	<xs:simpleType name="ssjczbm1">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="100"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- ssjczrq1  手术及操作日期    varchar(12)     Y -->
	<xs:simpleType name="ssjczrq1">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="12"/>
		</xs:restriction>
	</xs:simpleType>

	<!-- ssjb1  手术级别    varchar(100)     Y -->
	<xs:simpleType name="ssjb1">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="100"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- ssjczmc1  手术及操作名称    varchar(200)     Y -->
	<xs:simpleType name="ssjczmc1">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="200"/>
		</xs:restriction>
	</xs:simpleType>

	<!-- sz1  术者    varchar(100)     Y -->
	<xs:simpleType name="sz1">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="100"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- yz1  i 助     varchar(100)     Y -->
	<xs:simpleType name="yz1">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="100"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- ez1  ii 助     varchar(100)     Y -->
	<xs:simpleType name="ez1">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="100"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- qkdj1  切口等级    varchar(100)     Y -->
	<xs:simpleType name="qkdj1">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="100"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- qkyhlb1  切口愈合类别    varchar(100)     Y -->
	<xs:simpleType name="qkyhlb1">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="100"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- mzfs1  麻醉方式    varchar(100)     Y -->
	<xs:simpleType name="mzfs1">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="100"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- mzys1  麻醉医师   varchar(100)     Y -->
	<xs:simpleType name="mzys1">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="100"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- ssjczbm2  手术及操作编码    varchar(100)     Y -->
	<xs:simpleType name="ssjczbm2">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="100"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- ssjczrq2  手术及操作日期    varchar(12)     Y -->
	<xs:simpleType name="ssjczrq2">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="12"/>
		</xs:restriction>
	</xs:simpleType>

	<!-- ssjb2  手术级别    varchar(100)     Y -->
	<xs:simpleType name="ssjb2">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="100"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- ssjczmc2  手术及操作名称    varchar(200)     Y -->
	<xs:simpleType name="ssjczmc2">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="200"/>
		</xs:restriction>
	</xs:simpleType>

	<!-- sz2  术者    varchar(100)     Y -->
	<xs:simpleType name="sz2">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="100"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- yz2  i 助     varchar(100)     Y -->
	<xs:simpleType name="yz2">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="100"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- ez2  ii 助     varchar(100)     Y -->
	<xs:simpleType name="ez2">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="100"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- qkdj2  切口等级    varchar(100)     Y -->
	<xs:simpleType name="qkdj2">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="100"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- qkyhlb2  切口愈合类别    varchar(100)     Y -->
	<xs:simpleType name="qkyhlb2">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="100"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- mzfs2  麻醉方式    varchar(100)     Y -->
	<xs:simpleType name="mzfs2">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="100"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- mzys2  麻醉医师   varchar(100)     Y -->
	<xs:simpleType name="mzys2">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="100"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- ssjczbm3  手术及操作编码    varchar(100)     Y -->
	<xs:simpleType name="ssjczbm3">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="100"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- ssjczrq3  手术及操作日期    varchar(12)     Y -->
	<xs:simpleType name="ssjczrq3">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="12"/>
		</xs:restriction>
	</xs:simpleType>

	<!-- ssjb3  手术级别    varchar(100)     Y -->
	<xs:simpleType name="ssjb3">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="100"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- ssjczmc3  手术及操作名称    varchar(200)     Y -->
	<xs:simpleType name="ssjczmc3">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="200"/>
		</xs:restriction>
	</xs:simpleType>

	<!-- sz3  术者    varchar(100)     Y -->
	<xs:simpleType name="sz3">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="100"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- yz3  i 助     varchar(100)     Y -->
	<xs:simpleType name="yz3">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="100"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- ez3  ii 助     varchar(100)     Y -->
	<xs:simpleType name="ez3">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="100"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- qkdj3  切口等级    varchar(100)     Y -->
	<xs:simpleType name="qkdj3">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="100"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- qkyhlb3  切口愈合类别    varchar(100)     Y -->
	<xs:simpleType name="qkyhlb3">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="100"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- mzfs3  麻醉方式    varchar(100)     Y -->
	<xs:simpleType name="mzfs3">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="100"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- mzys3  麻醉医师   varchar(100)     Y -->
	<xs:simpleType name="mzys3">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="100"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- ssjczbm4  手术及操作编码    varchar(100)     Y -->
	<xs:simpleType name="ssjczbm4">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="100"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- ssjczrq4  手术及操作日期    varchar(12)     Y -->
	<xs:simpleType name="ssjczrq4">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="12"/>
		</xs:restriction>
	</xs:simpleType>

	<!-- ssjb4  手术级别    varchar(100)     Y -->
	<xs:simpleType name="ssjb4">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="100"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- ssjczmc4  手术及操作名称    varchar(200)     Y -->
	<xs:simpleType name="ssjczmc4">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="200"/>
		</xs:restriction>
	</xs:simpleType>

	<!-- sz4  术者    varchar(100)     Y -->
	<xs:simpleType name="sz4">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="100"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- yz4  i 助     varchar(100)     Y -->
	<xs:simpleType name="yz4">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="100"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- ez4  ii 助     varchar(100)     Y -->
	<xs:simpleType name="ez4">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="100"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- qkdj4  切口等级    varchar(100)     Y -->
	<xs:simpleType name="qkdj4">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="100"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- qkyhlb4  切口愈合类别    varchar(100)     Y -->
	<xs:simpleType name="qkyhlb4">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="100"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- mzfs4  麻醉方式    varchar(100)     Y -->
	<xs:simpleType name="mzfs4">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="100"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- mzys4  麻醉医师   varchar(100)     Y -->
	<xs:simpleType name="mzys4">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="100"/>
		</xs:restriction>
	</xs:simpleType>

	<!-- ssjczbm5  手术及操作编码    varchar(100)     Y -->
	<xs:simpleType name="ssjczbm5">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="100"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- ssjczrq5  手术及操作日期    varchar(12)     Y -->
	<xs:simpleType name="ssjczrq5">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="12"/>
		</xs:restriction>
	</xs:simpleType>

	<!-- ssjb5  手术级别    varchar(100)     Y -->
	<xs:simpleType name="ssjb5">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="100"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- ssjczmc5  手术及操作名称    varchar(200)     Y -->
	<xs:simpleType name="ssjczmc5">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="200"/>
		</xs:restriction>
	</xs:simpleType>

	<!-- sz5  术者    varchar(100)     Y -->
	<xs:simpleType name="sz5">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="100"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- yz5  i 助     varchar(100)     Y -->
	<xs:simpleType name="yz5">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="100"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- ez5  ii 助     varchar(100)     Y -->
	<xs:simpleType name="ez5">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="100"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- qkdj5  切口等级    varchar(100)     Y -->
	<xs:simpleType name="qkdj5">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="100"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- qkyhlb5  切口愈合类别    varchar(100)     Y -->
	<xs:simpleType name="qkyhlb5">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="100"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- mzfs5  麻醉方式    varchar(100)     Y -->
	<xs:simpleType name="mzfs5">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="100"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- mzys5  麻醉医师   varchar(100)     Y -->
	<xs:simpleType name="mzys5">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="100"/>
		</xs:restriction>
	</xs:simpleType>

	<!-- ssjczbm6  手术及操作编码    varchar(100)     Y -->
	<xs:simpleType name="ssjczbm6">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="100"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- ssjczrq6  手术及操作日期    varchar(12)     Y -->
	<xs:simpleType name="ssjczrq6">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="12"/>
		</xs:restriction>
	</xs:simpleType>

	<!-- ssjb6  手术级别    varchar(100)     Y -->
	<xs:simpleType name="ssjb6">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="100"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- ssjczmc6  手术及操作名称    varchar(200)     Y -->
	<xs:simpleType name="ssjczmc6">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="200"/>
		</xs:restriction>
	</xs:simpleType>

	<!-- sz6  术者    varchar(100)     Y -->
	<xs:simpleType name="sz6">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="100"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- yz6  i 助     varchar(100)     Y -->
	<xs:simpleType name="yz6">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="100"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- ez6  ii 助     varchar(100)     Y -->
	<xs:simpleType name="ez6">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="100"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- qkdj6  切口等级    varchar(100)     Y -->
	<xs:simpleType name="qkdj6">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="100"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- qkyhlb6  切口愈合类别    varchar(100)     Y -->
	<xs:simpleType name="qkyhlb6">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="100"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- mzfs6  麻醉方式    varchar(100)     Y -->
	<xs:simpleType name="mzfs6">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="100"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- mzys6  麻醉医师   varchar(100)     Y -->
	<xs:simpleType name="mzys6">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="100"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- ssjczbm7  手术及操作编码    varchar(100)     Y -->
	<xs:simpleType name="ssjczbm7">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="100"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- ssjczrq7  手术及操作日期    varchar(12)     Y -->
	<xs:simpleType name="ssjczrq7">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="12"/>
		</xs:restriction>
	</xs:simpleType>

	<!-- ssjb7  手术级别    varchar(100)     Y -->
	<xs:simpleType name="ssjb7">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="100"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- ssjczmc7  手术及操作名称    varchar(200)     Y -->
	<xs:simpleType name="ssjczmc7">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="200"/>
		</xs:restriction>
	</xs:simpleType>

	<!-- sz7  术者    varchar(100)     Y -->
	<xs:simpleType name="sz7">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="100"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- yz7  i 助     varchar(100)     Y -->
	<xs:simpleType name="yz7">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="100"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- ez7  ii 助     varchar(100)     Y -->
	<xs:simpleType name="ez7">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="100"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- qkdj7  切口等级    varchar(100)     Y -->
	<xs:simpleType name="qkdj7">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="100"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- qkyhlb7  切口愈合类别    varchar(100)     Y -->
	<xs:simpleType name="qkyhlb7">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="100"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- mzfs7  麻醉方式    varchar(100)     Y -->
	<xs:simpleType name="mzfs7">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="100"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- mzys7  麻醉医师   varchar(100)     Y -->
	<xs:simpleType name="mzys7">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="100"/>
		</xs:restriction>
	</xs:simpleType>

	<!-- lyfs  离院方式   varchar(100)     Y -->
	<xs:simpleType name="lyfs">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="100"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- yzzy_yljg  医嘱转院，拟接收医疗机构名称   varchar(200)     Y -->
	<xs:simpleType name="yzzy_yljg">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="200"/>
		</xs:restriction>
	</xs:simpleType>

	<!-- wsy_yljg   医嘱转社区卫生服务机构/乡镇 卫生院，拟接收医疗机构名称    varchar(200)     Y -->
	<xs:simpleType name="wsy_yljg">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="200"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- sfzzyjh   是否有出院 31 天内再住院计划 手术情况     varchar(100)     Y -->
	<xs:simpleType name="sfzzyjh">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="100"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- md   目的    varchar(100)     Y -->
	<xs:simpleType name="md">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="100"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- ryq_t   颅脑损伤患者昏迷入院前时 间：天   number(12)      Y -->
	<xs:simpleType name="ryq_t">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="12"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- ryq_xs   颅脑损伤患者昏迷入院前时 间：小时    number(24)      Y -->
	<xs:simpleType name="ryq_xs">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="24"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- ryq_f   颅脑损伤患者昏迷入院前时 间：分     number(12)      Y -->
	<xs:simpleType name="ryq_f">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="12"/>
		</xs:restriction>
	</xs:simpleType>

	<!-- ryh_t     颅脑损伤患者昏迷入院后时 间：天      number(12)      Y -->
	<xs:simpleType name="ryh_t">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="12"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- ryh_xs     颅脑损伤患者昏迷入院后时 间：小时      number(24)      Y -->
	<xs:simpleType name="ryh_xs">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="24"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- ryh_f     颅脑损伤患者昏迷入院后时 间：分      number(12)      Y -->
	<xs:simpleType name="ryh_f">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="12"/>
		</xs:restriction>
	</xs:simpleType>

	<!-- zfy     住院费用(元)：总费用      number(12,2)     N-->
	<xs:simpleType name="zfy">
		<xs:restriction base="xs:string">
			<xs:pattern value="|((([1-9]{1}\d{0,9})|([0]{1}))(\.\d{1,2})?)"/>
			<xs:minLength value="1"/>
			<xs:maxLength value="12"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- zfje     自付金额      number(12,2)    Y-->
	<xs:simpleType name="zfje">
		<xs:restriction base="xs:string">
			<xs:pattern value="|((([1-9]{1}\d{0,9})|([0]{1}))(\.\d{1,2})?)"/>
			<xs:minLength value="0"/>
			<xs:maxLength value="12"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- ylfuf     综合医疗服务类：(1)一般医疗 服务费     number(12,2)     Y-->
	<xs:simpleType name="ylfuf">
		<xs:restriction base="xs:string">
			<xs:pattern value="|((([1-9]{1}\d{0,9})|([0]{1}))(\.\d{1,2})?)"/>
			<xs:minLength value="0"/>
			<xs:maxLength value="12"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- zlczf     一般治疗操作费(2)     number(12,2)     Y-->
	<xs:simpleType name="zlczf">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="12"/>
			<xs:pattern value="|((([1-9]{1}\d{0,9})|([0]{1}))(\.\d{1,2})?)"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- hlf     护理费(3)住院费      number(12,2)     Y-->
	<xs:simpleType name="hlf">
		<xs:restriction base="xs:string">
			<xs:pattern value="|((([1-9]{1}\d{0,9})|([0]{1}))(\.\d{1,2})?)"/>
			<xs:minLength value="0"/>
			<xs:maxLength value="12"/>
		</xs:restriction>
	</xs:simpleType>

	<!-- qtfy     其他费用(4)       number(12,2)     Y-->
	<xs:simpleType name="qtfy">
		<xs:restriction base="xs:string">
			<xs:pattern value="|((([1-9]{1}\d{0,9})|([0]{1}))(\.\d{1,2})?)"/>
			<xs:minLength value="0"/>
			<xs:maxLength value="12"/>
		</xs:restriction>
	</xs:simpleType>

	<!-- blzdf     诊断类：(5)病理诊断费       number(12,2)      Y-->
	<xs:simpleType name="blzdf">
		<xs:restriction base="xs:string">
			<xs:pattern value="|((([1-9]{1}\d{0,9})|([0]{1}))(\.\d{1,2})?)"/>
			<xs:minLength value="0"/>
			<xs:maxLength value="12"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- syszdf     实验室诊断费(6)      number(12,2)     Y-->
	<xs:simpleType name="syszdf">
		<xs:restriction base="xs:string">
			<xs:pattern value="|((([1-9]{1}\d{0,9})|([0]{1}))(\.\d{1,2})?)"/>
			<xs:minLength value="0"/>
			<xs:maxLength value="12"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- yxxzdf     影像学诊断费(7)       number(12,2)     Y-->
	<xs:simpleType name="yxxzdf">
		<xs:restriction base="xs:string">
			<xs:pattern value="|((([1-9]{1}\d{0,9})|([0]{1}))(\.\d{1,2})?)"/>
			<xs:minLength value="0"/>
			<xs:maxLength value="12"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- lczdxmf     临床诊断项目费(8)        number(12,2)     Y-->
	<xs:simpleType name="lczdxmf">
		<xs:restriction base="xs:string">
			<xs:pattern value="|((([1-9]{1}\d{0,9})|([0]{1}))(\.\d{1,2})?)"/>
			<xs:minLength value="0"/>
			<xs:maxLength value="12"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- fsszlxmf     治疗类：(9)非手术治疗项目费        number(12,2)     Y-->
	<xs:simpleType name="fsszlxmf">
		<xs:restriction base="xs:string">
			<xs:pattern value="|((([1-9]{1}\d{0,9})|([0]{1}))(\.\d{1,2})?)"/>
			<xs:minLength value="0"/>
			<xs:maxLength value="12"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- wlzlf     临床物理治疗费(9)         number(12,2)     Y-->
	<xs:simpleType name="wlzlf">
		<xs:restriction base="xs:string">
			<xs:pattern value="|((([1-9]{1}\d{0,9})|([0]{1}))(\.\d{1,2})?)"/>
			<xs:minLength value="0"/>
			<xs:maxLength value="12"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- sszlf     手术治疗费(10)          number(12,2)     Y-->
	<xs:simpleType name="sszlf">
		<xs:restriction base="xs:string">
			<xs:pattern value="|((([1-9]{1}\d{0,9})|([0]{1}))(\.\d{1,2})?)"/>
			<xs:minLength value="0"/>
			<xs:maxLength value="12"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- maf     麻醉费(10)          number(12,2)     Y-->
	<xs:simpleType name="maf">
		<xs:restriction base="xs:string">
			<xs:pattern value="|((([1-9]{1}\d{0,9})|([0]{1}))(\.\d{1,2})?)"/>
			<xs:minLength value="0"/>
			<xs:maxLength value="12"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- ssf     手术费(10)          number(12,2)     Y-->
	<xs:simpleType name="ssf">
		<xs:restriction base="xs:string">
			<xs:pattern value="|((([1-9]{1}\d{0,9})|([0]{1}))(\.\d{1,2})?)"/>
			<xs:minLength value="0"/>
			<xs:maxLength value="12"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- kff     康复类：(11)康复费           number(12,2)     Y-->
	<xs:simpleType name="kff">
		<xs:restriction base="xs:string">
			<xs:pattern value="|((([1-9]{1}\d{0,9})|([0]{1}))(\.\d{1,2})?)"/>
			<xs:minLength value="0"/>
			<xs:maxLength value="12"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- zyzlf     中医类:(12)中医治疗费         number(12,2)     Y-->
	<xs:simpleType name="zyzlf">
		<xs:restriction base="xs:string">
			<xs:pattern value="|((([1-9]{1}\d{0,9})|([0]{1}))(\.\d{1,2})?)"/>
			<xs:minLength value="0"/>
			<xs:maxLength value="12"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- xyf     西药类:(13)西药费          number(12,2)     Y-->
	<xs:simpleType name="xyf">
		<xs:restriction base="xs:string">
			<xs:pattern value="|((([1-9]{1}\d{0,9})|([0]{1}))(\.\d{1,2})?)"/>
			<xs:minLength value="0"/>
			<xs:maxLength value="12"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- kjywf     抗菌药物费(13)          number(12,2)     Y-->
	<xs:simpleType name="kjywf">
		<xs:restriction base="xs:string">
			<xs:pattern value="|((([1-9]{1}\d{0,9})|([0]{1}))(\.\d{1,2})?)"/>
			<xs:minLength value="0"/>
			<xs:maxLength value="12"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- zcyf    中药类:(14)中成药费          number(12,2)     Y-->
	<xs:simpleType name="zcyf">
		<xs:restriction base="xs:string">
			<xs:pattern value="|((([1-9]{1}\d{0,9})|([0]{1}))(\.\d{1,2})?)"/>
			<xs:minLength value="0"/>
			<xs:maxLength value="12"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- zcyf1    中草药费(15)          number(12,2)     Y-->
	<xs:simpleType name="zcyf1">
		<xs:restriction base="xs:string">
			<xs:pattern value="|((([1-9]{1}\d{0,9})|([0]{1}))(\.\d{1,2})?)"/>
			<xs:minLength value="0"/>
			<xs:maxLength value="12"/>
		</xs:restriction>
	</xs:simpleType>

	<!-- xf    血液和血液制品类:(16)血费         number(12,2)     Y-->
	<xs:simpleType name="xf">
		<xs:restriction base="xs:string">
			<xs:pattern value="|((([1-9]{1}\d{0,9})|([0]{1}))(\.\d{1,2})?)"/>
			<xs:minLength value="0"/>
			<xs:maxLength value="12"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- bdblzpf    白蛋白类制品费(17)          number(12,2)     Y-->
	<xs:simpleType name="bdblzpf">
		<xs:restriction base="xs:string">
			<xs:pattern value="|((([1-9]{1}\d{0,9})|([0]{1}))(\.\d{1,2})?)"/>
			<xs:minLength value="0"/>
			<xs:maxLength value="12"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- qdblzpf    球蛋白类制品费(18)           number(12,2)     Y-->
	<xs:simpleType name="qdblzpf">
		<xs:restriction base="xs:string">
			<xs:pattern value="|((([1-9]{1}\d{0,9})|([0]{1}))(\.\d{1,2})?)"/>
			<xs:minLength value="0"/>
			<xs:maxLength value="12"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- nxyzlzpf    凝血因子类制品费(19)           number(12,2)     Y-->
	<xs:simpleType name="nxyzlzpf">
		<xs:restriction base="xs:string">
			<xs:pattern value="|((([1-9]{1}\d{0,9})|([0]{1}))(\.\d{1,2})?)"/>
			<xs:minLength value="0"/>
			<xs:maxLength value="12"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- xbyzlzpf    细胞因子类制品费(20)           number(12,2)     Y-->
	<xs:simpleType name="xbyzlzpf">
		<xs:restriction base="xs:string">
			<xs:pattern value="|((([1-9]{1}\d{0,9})|([0]{1}))(\.\d{1,2})?)"/>
			<xs:minLength value="0"/>
			<xs:maxLength value="12"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- hcyyclf    耗材类:(21)检查用一次性医用 材料费           number(12,2)     Y-->
	<xs:simpleType name="hcyyclf">
		<xs:restriction base="xs:string">
			<xs:pattern value="|((([1-9]{1}\d{0,9})|([0]{1}))(\.\d{1,2})?)"/>
			<xs:minLength value="0"/>
			<xs:maxLength value="12"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- yyclf    (22)治疗用一次性医用材料费         number(12,2)     Y-->
	<xs:simpleType name="yyclf">
		<xs:restriction base="xs:string">
			<xs:pattern value="|((([1-9]{1}\d{0,9})|([0]{1}))(\.\d{1,2})?)"/>
			<xs:minLength value="0"/>
			<xs:maxLength value="12"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- ycxyyclf    (23)手术用一次性医用材料费          number(12,2)     Y-->
	<xs:simpleType name="ycxyyclf">
		<xs:restriction base="xs:string">
			<xs:pattern value="|((([1-9]{1}\d{0,9})|([0]{1}))(\.\d{1,2})?)"/>
			<xs:minLength value="0"/>
			<xs:maxLength value="12"/>
		</xs:restriction>
	</xs:simpleType>

	<!-- qtf    其他类：(24)其他费          number(12,2)     Y-->
	<xs:simpleType name="qtf">
		<xs:restriction base="xs:string">
			<xs:pattern value="|((([1-9]{1}\d{0,9})|([0]{1}))(\.\d{1,2})?)"/>
			<xs:minLength value="0"/>
			<xs:maxLength value="12"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- hxjsysj    呼吸机使用时间(小时)         number(8,2)      Y-->
	<xs:simpleType name="hxjsysj">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="9"/>
		</xs:restriction>
	</xs:simpleType>

	<!-- zzjhbflx1    重症监护病房类型 1     varchar(6)       Y-->
	<xs:simpleType name="zzjhbflx1">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="6"/>
		</xs:restriction>
	</xs:simpleType>



	<!-- jzzjhssj1    进重症监护室时间 1     date       Y-->
	<xs:simpleType name="jzzjhssj1">
		<xs:restriction base="xs:string">
			<xs:pattern value="|[1-9]\d{3}-(0[1-9]|1[0-2])-(0[1-9]|[1-2][0-9]|3[0-1])\s+(20|21|22|23|[0-1]\d):[0-5]\d:[0-5]\d" />
			<xs:minLength value="0" />
			<xs:maxLength value="20" />
		</xs:restriction>
	</xs:simpleType>


	<!-- czzjhssj1    出重症监护室时间 1      date       Y-->
	<xs:simpleType name="czzjhssj1">
		<xs:restriction base="xs:string">
			<xs:pattern value="|[1-9]\d{3}-(0[1-9]|1[0-2])-(0[1-9]|[1-2][0-9]|3[0-1])\s+(20|21|22|23|[0-1]\d):[0-5]\d:[0-5]\d" />
			<xs:minLength value="0" />
			<xs:maxLength value="20" />
		</xs:restriction>
	</xs:simpleType>

	<!-- zzjhbflx2    重症监护病房类型 2     varchar(6)       Y-->
	<xs:simpleType name="zzjhbflx2">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="6"/>
		</xs:restriction>
	</xs:simpleType>



	<!-- jzzjhssj2   进重症监护室时间 2     date       Y-->
	<xs:simpleType name="jzzjhssj2">
		<xs:restriction base="xs:string">
			<xs:pattern value="|[1-9]\d{3}-(0[1-9]|1[0-2])-(0[1-9]|[1-2][0-9]|3[0-1])\s+(20|21|22|23|[0-1]\d):[0-5]\d:[0-5]\d" />
			<xs:minLength value="0" />
			<xs:maxLength value="20" />
		</xs:restriction>
	</xs:simpleType>


	<!-- czzjhssj2    出重症监护室时间2      date       Y-->
	<xs:simpleType name="czzjhssj2">
		<xs:restriction base="xs:string">
			<xs:pattern value="|[1-9]\d{3}-(0[1-9]|1[0-2])-(0[1-9]|[1-2][0-9]|3[0-1])\s+(20|21|22|23|[0-1]\d):[0-5]\d:[0-5]\d" />
			<xs:minLength value="0" />
			<xs:maxLength value="20" />
		</xs:restriction>
	</xs:simpleType>

	<!-- zzjhbflx3    重症监护病房类型 3     varchar(6)       Y-->
	<xs:simpleType name="zzjhbflx3">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="6"/>
		</xs:restriction>
	</xs:simpleType>



	<!-- jzzjhssj3    进重症监护室时间 1     date       Y-->
	<xs:simpleType name="jzzjhssj3">
		<xs:restriction base="xs:string">
			<xs:pattern value="|[1-9]\d{3}-(0[1-9]|1[0-2])-(0[1-9]|[1-2][0-9]|3[0-1])\s+(20|21|22|23|[0-1]\d):[0-5]\d:[0-5]\d" />
			<xs:minLength value="0" />
			<xs:maxLength value="20" />
		</xs:restriction>
	</xs:simpleType>


	<!-- czzjhssj3    出重症监护室时间 1      date       Y-->
	<xs:simpleType name="czzjhssj3">
		<xs:restriction base="xs:string">
			<xs:pattern value="|[1-9]\d{3}-(0[1-9]|1[0-2])-(0[1-9]|[1-2][0-9]|3[0-1])\s+(20|21|22|23|[0-1]\d):[0-5]\d:[0-5]\d" />
			<xs:minLength value="0" />
			<xs:maxLength value="20" />
		</xs:restriction>
	</xs:simpleType>

	<!-- qjqk1    抢救次数 integer   number(3)    Y-->
	<xs:simpleType name="qjqk1">
		<xs:restriction base="xs:string">
			<xs:pattern value="|\d{1,3}"></xs:pattern> <!--校验数字-->
		</xs:restriction>
	</xs:simpleType>

	<!-- qjqk2    抢救成功次数 integer   number(3)    Y-->
	<xs:simpleType name="qjqk2">
		<xs:restriction base="xs:string">
			<xs:pattern value="|\d{1,3}"></xs:pattern> <!--校验数字-->
		</xs:restriction>
	</xs:simpleType>

	<!-- cyzgqk    转归情况 integer   number(2)    Y-->
	<xs:simpleType name="cyzgqk">
		<xs:restriction base="xs:string">
			<xs:pattern value="\d{1,2}"></xs:pattern> <!--校验数字-->
		</xs:restriction>
	</xs:simpleType>
	
	
	<!-- aaa027    统筹区 string   varchar(10)    N-->
	<xs:simpleType name="aaa027">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="10"/>
		</xs:restriction>
	</xs:simpleType>

	<!-- akc190    医保结算交易流水号    varchar(20)       Y-->
	<xs:simpleType name="akc190">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="50"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- akb020    医疗机构编号    varchar(40)       N-->
	<xs:simpleType name="akb020">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="40"/>
		</xs:restriction>
	</xs:simpleType>

	<!-- ake302    病案号    varchar(20)       N-->
	<xs:simpleType name="ake302">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="50"/>
		</xs:restriction>
	</xs:simpleType>

	<!-- bked47   医嘱类型    varchar(6)      N-->
	<xs:simpleType name="bked47">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="6"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- bked48    医嘱制定时间    date       N-->
	<xs:simpleType name="bked48">
		<xs:restriction base="xs:string">
			<xs:pattern value="|[1-9]\d{3}-(0[1-9]|1[0-2])-(0[1-9]|[1-2][0-9]|3[0-1])\s+(20|21|22|23|[0-1]\d):[0-5]\d:[0-5]\d" />
			<xs:minLength value="1"/>
			<xs:maxLength value="20"/>
		</xs:restriction>
	</xs:simpleType>

	<!-- bked49    医嘱内容   varchar(200)        N-->
	<xs:simpleType name="bked49">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="1000"/>
		</xs:restriction>
	</xs:simpleType>

	<!-- bked50    医嘱制定医师编码   varchar(20)      	N-->
	<xs:simpleType name="bked50">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="50"/>
		</xs:restriction>
	</xs:simpleType>

	<!-- bked51    医嘱制定医师名称   varchar(30)  N -->
	<xs:simpleType name="bked51">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="30"/>
		</xs:restriction>
	</xs:simpleType>

	<!-- bked52   医嘱开始执行时间    date     N -->
	<xs:simpleType name="bked52">
		<xs:restriction base="xs:string">
			<xs:pattern value="|[1-9]\d{3}-(0[1-9]|1[0-2])-(0[1-9]|[1-2][0-9]|3[0-1])\s+(20|21|22|23|[0-1]\d):[0-5]\d:[0-5]\d" />
			<xs:minLength value="1"/>
			<xs:maxLength value="20"/>
		</xs:restriction>
	</xs:simpleType>

	<!-- bked53    医嘱开始执行护士    varchar(30)  N 	-->
	<xs:simpleType name="bked53">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="30"/>
		</xs:restriction>
	</xs:simpleType>

	<!-- bked54    医嘱停止时间   date  Y 	-->
	<xs:simpleType name="bked54">
		<xs:restriction base="xs:string">
			<xs:pattern value="|[1-9]\d{3}-(0[1-9]|1[0-2])-(0[1-9]|[1-2][0-9]|3[0-1])\s+(20|21|22|23|[0-1]\d):[0-5]\d:[0-5]\d" />
			<xs:minLength value="0"/>
			<xs:maxLength value="20"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- bked55    医嘱停止医师编码   varchar(20)  Y 	-->
	<xs:simpleType name="bked55">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="50"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- bked56    医嘱停止医师名称   varchar(30)   Y 	-->
	<xs:simpleType name="bked56">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="30"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- bked57    医嘱停止执行时间   date Y 	-->
	<xs:simpleType name="bked57">
		<xs:restriction base="xs:string">
			<xs:pattern value="|[1-9]\d{3}-(0[1-9]|1[0-2])-(0[1-9]|[1-2][0-9]|3[0-1])\s+(20|21|22|23|[0-1]\d):[0-5]\d:[0-5]\d" />
			<xs:minLength value="0"/>
			<xs:maxLength value="20"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- bked58    医嘱停止执行护士  varchar(30)  Y 	-->
	<xs:simpleType name="bked58">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="30"/>
		</xs:restriction>
	</xs:simpleType>

	<!-- bked66    检验类别  varchar(6)   N 	-->
	<xs:simpleType name="bked66">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="6"/>
		</xs:restriction>
	</xs:simpleType>

	<!-- bked67    检验时间 	date    N 	-->
	<xs:simpleType name="bked67">
		<xs:restriction base="xs:string">
			<xs:pattern value="|[1-9]\d{3}-(0[1-9]|1[0-2])-(0[1-9]|[1-2][0-9]|3[0-1])\s+(20|21|22|23|[0-1]\d):[0-5]\d:[0-5]\d" />
			<xs:minLength value="1"/>
			<xs:maxLength value="20"/>
		</xs:restriction>
	</xs:simpleType>

	<!-- bked68    检验医师编码  varchar(20)    N 	-->
	<xs:simpleType name="bked68">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="50"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- bked69    检验医师名称  varchar(30)    N 	-->
	<xs:simpleType name="bked69">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="30"/>
		</xs:restriction>
	</xs:simpleType>

	<!-- bked70    检验项目  varchar(50)    N 	-->
	<xs:simpleType name="bked70">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="50"/>
		</xs:restriction>
	</xs:simpleType>

	<!-- bked71    检验结果  varchar(20)    N 	-->
	<xs:simpleType name="bked71">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="100"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- bked73    异常标识  varchar(6)     Y 	-->
	<xs:simpleType name="bked73">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="6"/>
		</xs:restriction>
	</xs:simpleType>



	<!-- bked59    报告类别  varchar(6)     N 	-->
	<xs:simpleType name="bked59">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="6"/>
		</xs:restriction>
	</xs:simpleType>

	<!-- bked60    检查部位  varchar(50)      N 	-->
	<xs:simpleType name="bked60">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="50"/>
		</xs:restriction>
	</xs:simpleType>

	<!-- bked61    检查时间 	date    N 	-->
	<xs:simpleType name="bked61">
		<xs:restriction base="xs:string">
			<xs:pattern value="|[1-9]\d{3}-(0[1-9]|1[0-2])-(0[1-9]|[1-2][0-9]|3[0-1])\s+(20|21|22|23|[0-1]\d):[0-5]\d:[0-5]\d" />
			<xs:minLength value="1"/>
			<xs:maxLength value="20"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- bked62    检查描述 	date    N 	-->
	<xs:simpleType name="bked62">
		<xs:restriction base="xs:string">
		</xs:restriction>
	</xs:simpleType>


	<!-- bked64   检查医师编码 varchar(20)     N 	-->
	<xs:simpleType name="bked64">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="50"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- bked65   检查医师名称 varchar(30)     N 	-->
	<xs:simpleType name="bked65">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="30"/>
		</xs:restriction>
	</xs:simpleType>

	<!-- ykg029   手术记录序号 varchar(20)      N 	-->
	<xs:simpleType name="ykg029">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="20"/>
		</xs:restriction>
	</xs:simpleType>

	<!-- ykg030   手术医师编码 varchar(20)      N 	-->
	<xs:simpleType name="ykg030">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="50"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- ykg031   手术医师姓名 varchar(30)      N 	-->
	<xs:simpleType name="ykg031">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="30"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- ykg032   手术医师|助编码  varchar(20)      Y 	-->
	<xs:simpleType name="ykg032">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="50"/>
		</xs:restriction>
	</xs:simpleType>

	<!-- ykg033   手术医师|助名称   varchar(30)      Y 	-->
	<xs:simpleType name="ykg033">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="30"/>
		</xs:restriction>
	</xs:simpleType>

	<!-- ykg034   麻醉师编码  varchar(20)      Y 	-->
	<xs:simpleType name="ykg034">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="50"/>
		</xs:restriction>
	</xs:simpleType>

	<!-- ykg035   麻醉师姓名   varchar(30)      Y 	-->
	<xs:simpleType name="ykg035">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="30"/>
		</xs:restriction>
	</xs:simpleType>

	<!-- ykg036    手术执行日期开始时间 	date    N 	-->
	<xs:simpleType name="ykg036">
		<xs:restriction base="xs:string">
			<xs:pattern value="|[1-9]\d{3}-(0[1-9]|1[0-2])-(0[1-9]|[1-2][0-9]|3[0-1])\s+(20|21|22|23|[0-1]\d):[0-5]\d:[0-5]\d" />
			<xs:minLength value="1"/>
			<xs:maxLength value="20"/>
		</xs:restriction>
	</xs:simpleType>

	<!-- ykg037    手术执行日期完成时间 	date    N 	-->
	<xs:simpleType name="ykg037">
		<xs:restriction base="xs:string">
			<xs:pattern value="|[1-9]\d{3}-(0[1-9]|1[0-2])-(0[1-9]|[1-2][0-9]|3[0-1])\s+(20|21|22|23|[0-1]\d):[0-5]\d:[0-5]\d" />
			<xs:minLength value="1"/>
			<xs:maxLength value="20"/>
		</xs:restriction>
	</xs:simpleType>

	<!-- ykg038   麻醉执行方式   varchar(6)      Y 	-->
	<xs:simpleType name="ykg038">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="6"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- ykg039   是否有手术并发症  varchar(6)      N 	-->
	<xs:simpleType name="ykg039">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="6"/>
		</xs:restriction>
	</xs:simpleType>

	<!-- ykg040   手术并发症编码   varchar(20)      Y 	-->
	<xs:simpleType name="ykg040">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="20"/>
		</xs:restriction>
	</xs:simpleType>

	<!-- ykg041   手术并发症名称   varchar(30)      Y 	-->
	<xs:simpleType name="ykg041">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="30"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- ykg042   手术并发症名称   varchar(2000)      Y 	-->
	<xs:simpleType name="ykg042">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="2000"/>
		</xs:restriction>
	</xs:simpleType>

	<!-- ykg043   手术记录医师编码   varchar(20)      Y 	-->
	<xs:simpleType name="ykg043">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="50"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- ykg044   手术记录医师名称   varchar(30)      Y 	-->
	<xs:simpleType name="ykg044">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="30"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- ykg047   手术明细序号   varchar(20)       N 	-->
	<xs:simpleType name="ykg047">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="20"/>
		</xs:restriction>
	</xs:simpleType>

	<!-- ykg048   手术代码   varchar(20)       N 	-->
	<xs:simpleType name="ykg048">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="20"/>
		</xs:restriction>
	</xs:simpleType>

	<!-- ykg049   手术名称   varchar(30)       N 	-->
	<xs:simpleType name="ykg049">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="100"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- ykg050   手术等级   varchar(6)        N 	-->
	<xs:simpleType name="ykg050">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="6"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- ykg051   手术切口分类   varchar(6)        N 	-->
	<xs:simpleType name="ykg051">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="6"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- ykg052   手术切口愈合等级   varchar(6)        N 	-->
	<xs:simpleType name="ykg052">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="6"/>
		</xs:restriction>
	</xs:simpleType>


	<!-- ykg053   主次标志   varchar(6)        N 	-->
	<xs:simpleType name="ykg053">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="6"/>
		</xs:restriction>
	</xs:simpleType>

	<!-- ykg061   是否医源性手术   varchar(6)        Y 	-->
	<xs:simpleType name="ykg061">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="6"/>
		</xs:restriction>
	</xs:simpleType>

	<!-- bked40   入院情况   clob        N 	-->
	<xs:simpleType name="bked40">
		<xs:restriction base="xs:string">
		</xs:restriction>
	</xs:simpleType>


	<!-- bked42   主要检查及检验结果   clob        N 	-->
	<xs:simpleType name="bked42">
		<xs:restriction base="xs:string">
		</xs:restriction>
	</xs:simpleType>

	<!-- bked43   诊疗经过   clob        N 	-->
	<xs:simpleType name="bked43">
		<xs:restriction base="xs:string">
		</xs:restriction>
	</xs:simpleType>


	<!-- bked44   出院情况   clob        N 	-->
	<xs:simpleType name="bked44">
		<xs:restriction base="xs:string">
		</xs:restriction>
	</xs:simpleType>

	<!-- bked46   出院医嘱      clob       N 	-->
	<xs:simpleType name="bked46">
		<xs:restriction base="xs:string">
		</xs:restriction>
	</xs:simpleType>


	<!-- bkc009    入院时间 	date    Y 	-->
	<xs:simpleType name="bkc009">
		<xs:restriction base="xs:string">
			<xs:pattern value="|[1-9]\d{3}-(0[1-9]|1[0-2])-(0[1-9]|[1-2][0-9]|3[0-1])\s+(20|21|22|23|[0-1]\d):[0-5]\d:[0-5]\d" />
			<xs:minLength value="0"/>
			<xs:maxLength value="20"/>
		</xs:restriction>
	</xs:simpleType>

	<!-- bkc010    出院时间 	date    Y	-->
	<xs:simpleType name="bkc010">
		<xs:restriction base="xs:string">
			<xs:pattern value="|[1-9]\d{3}-(0[1-9]|1[0-2])-(0[1-9]|[1-2][0-9]|3[0-1])\s+(20|21|22|23|[0-1]\d):[0-5]\d:[0-5]\d" />
			<xs:minLength value="0"/>
			<xs:maxLength value="20"/>
		</xs:restriction>
	</xs:simpleType>
	
	<!-- bkeh81   医保中心对应收费项目编码      varchar(50)       Y 	-->
	<xs:simpleType name="bkeh81">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="50"/>
		</xs:restriction>
	</xs:simpleType>

	<!--  bkek35   医院收费项目编码  varchar(50)  N    -->
	<xs:simpleType name="bkek35">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="50"/>
		</xs:restriction>
	</xs:simpleType>

	<!--  bkek36   医院收费项目名称  varchar(50)  N   -->
	<xs:simpleType name="bkek36">
		<xs:restriction base="xs:string">
			<xs:minLength value="0"/>
			<xs:maxLength value="50"/>
		</xs:restriction>
	</xs:simpleType>
	
</xs:schema>
