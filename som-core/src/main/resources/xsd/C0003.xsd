<?xml version="1.0" encoding="gbk"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema">
	<xs:element name="input">
		<xs:complexType>
			<xs:choice maxOccurs="unbounded" minOccurs="0">
				<xs:element name="head">
					<xs:complexType>
						<xs:complexContent>
							<xs:extension base="dataElementType">
								<xs:attributeGroup ref="dataRequiredAttributeType"/>
							</xs:extension>
						</xs:complexContent>
					</xs:complexType>
				</xs:element>
				<xs:element name="body">
					<xs:complexType>
						<xs:choice maxOccurs="unbounded" minOccurs="1">
							<xs:element name="row">
								<xs:complexType>
									<xs:complexContent>
										<xs:extension base="bodyDataElementType">
											<xs:attributeGroup
                                                    ref="dataRequiredAttributeType"/>
										</xs:extension>
									</xs:complexContent>
								</xs:complexType>
							</xs:element>
						</xs:choice>
					</xs:complexType>
				</xs:element>
			</xs:choice>
		</xs:complexType>
	</xs:element>

	<xs:attributeGroup name="dataRequiredAttributeType">
	</xs:attributeGroup>
    <!-- head节点的输入定义 -->
    <xs:complexType name="dataElementType">
		<xs:all>
			<xs:element name="wsa001" type="wsa001" minOccurs="1"/><!--交易代码 -->
			<xs:element name="wsa002" type="wsa002" minOccurs="1"/><!--发送方交易流水号 -->
		</xs:all>
	</xs:complexType>

	<!-- 交易代码 -->
	<xs:simpleType name="wsa001">
		<xs:restriction base="xs:string">
			<xs:pattern value="(C00[0-9]{2})"/>
			<xs:minLength value="1"/>
			<xs:maxLength value="8"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="wsa002">
		<xs:restriction base="xs:string">
			<xs:minLength value="1" />
			<xs:maxLength value="30" />
		</xs:restriction>
	</xs:simpleType>
    
    <!-- body节点的输入定义 -->
    <xs:complexType name="bodyDataElementType">
		<xs:all>
			<xs:element name="code" type="xs:string" minOccurs="1"/> <!--院内科室编码-->
            <xs:element name="name" type="xs:string" minOccurs="1"/> <!--院内科室名称-->
            <xs:element name="level" type="xs:string" minOccurs="1"/> <!--院内科室级别-->
            <xs:element name="parent_code" type="xs:string" minOccurs="1"/> <!--上级院内科室编码-->
            <xs:element name="common_code" type="xs:string" minOccurs="1"/> <!--标准（上传医保）科室编码-->
           
            <xs:element name="yhrylist" maxOccurs="1" minOccurs="0"> <!-- 科室内医护人员信息 -->
                <xs:complexType>
					<xs:choice maxOccurs="unbounded" minOccurs="0">
						<xs:element name="row" >
							<xs:complexType>
								<xs:complexContent>
									<xs:extension base="yhrylist">
									</xs:extension>
								</xs:complexContent>
							</xs:complexType>
						</xs:element>
					</xs:choice>
				</xs:complexType>
			</xs:element>
   
		</xs:all>
	</xs:complexType>
    
    <!-- 科室内医护人员信息 -->
    <xs:complexType name="yhrylist">
		<xs:all>
			<xs:element name="code" type="xs:string"/>   <!-- 医护人员代码（编码）  -->
            <xs:element name="name" type="xs:string"/>   <!-- 姓名  -->
            <xs:element name="id_card" type="xs:string"/>   <!-- 公民身份号码  -->
            <xs:element name="birthday" type="xs:string"/>   <!-- 出生日期 -->
            <xs:element name="sex" type="sex"/>   <!-- 性别 -->
            <xs:element name="title" type="xs:string"/>   <!-- 职称-->
            <xs:element name="type" type="type"/>   <!-- 执业类别  -->
		</xs:all>
	</xs:complexType>




	<!-- sex 性别  -->
	<xs:simpleType name="sex">
		<xs:restriction base="xs:string">
			<xs:pattern value="(|0|1|2|9)" />
			<xs:minLength value="0" />
			<xs:maxLength value="1" />
		</xs:restriction>
	</xs:simpleType>

	<!-- type 执业类别   -->
	<xs:simpleType name="type">
		<xs:restriction base="xs:string">
			<xs:pattern value="(|1|2|9)" />
			<xs:minLength value="0" />
			<xs:maxLength value="1" />
		</xs:restriction>
	</xs:simpleType>

</xs:schema>
