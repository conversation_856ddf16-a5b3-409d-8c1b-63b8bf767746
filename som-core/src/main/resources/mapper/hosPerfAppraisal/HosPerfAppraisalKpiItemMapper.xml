<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.som.mapper.hosPerfAppraisal.HosPerfAppraisalKpiItemMapper">
    <!-- 新增 -->
    <insert id="add">
        INSERT INTO cfg_nat_exam_kpi_item(
            ver,
            kpi_item_code,
            kpi_item_name,
            kpi_calc_formula,
            parent_kpi_item_code,
            `desc`,
            kpi_type,
            active_flag,
            crte_time
        )
        VALUES(
              #{ver,jdbcType=VARCHAR},
              #{kpiItemCode,jdbcType=VARCHAR},
              #{kpiItemName,jdbcType=VARCHAR},
              #{kpiCalcFormula,jdbcType=VARCHAR},
              #{parentKpiItemCode,jdbcType=VARCHAR},
              #{desc,jdbcType=VARCHAR},
              #{kpiType,jdbcType=VARCHAR},
              '',
              now()
        )
    </insert>

    <!-- 修改 -->
    <update id="update">
        UPDATE cfg_nat_exam_kpi_item
        SET ver = #{ver,jdbcType=VARCHAR},
            kpi_item_code = #{kpiItemCode,jdbcType=VARCHAR},
            kpi_item_name = #{kpiItemName,jdbcType=VARCHAR},
            kpi_calc_formula = #{kpiCalcFormula,jdbcType=VARCHAR},
            parent_kpi_item_code = #{parentKpiItemCode,jdbcType=VARCHAR},
            `desc` = #{desc,jdbcType=VARCHAR},
            kpi_type = #{kpiType,jdbcType=VARCHAR},
            active_flag = #{activeFlag,jdbcType=VARCHAR},
            crte_time = #{crteTime,jdbcType=VARCHAR}
        WHERE id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 移除 -->
    <delete id="remove">
        DELETE FROM cfg_nat_exam_kpi_item
        WHERE id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 查询数据 -->
    <select id="queryList" resultType="com.my.som.vo.hosPerfAppraisal.HosPerfAppraisalKpiItemVo">
        SELECT a.id as id,
               a.ver as ver,
               a.kpi_item_code as kpiItemCode,
               a.kpi_item_name as kpiItemName,
               a.kpi_calc_formula as kpiCalcFormula,
               a.parent_kpi_item_code as parentKpiItemCode,
               a.desc as "desc",
               a.kpi_type as kpiType,
               a.active_flag as activeFlag,
               a.crte_time as crteTime
        FROM cfg_nat_exam_kpi_item a
        WHERE a.ver = #{ver,jdbcType=VARCHAR}
    </select>

</mapper>
