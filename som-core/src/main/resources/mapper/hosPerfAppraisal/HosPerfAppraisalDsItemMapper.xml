<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.som.mapper.hosPerfAppraisal.HosPerfAppraisalDsItemMapper">
    <!-- 新增 -->
    <insert id="add">
        INSERT INTO cfg_nat_exam_ds_item(
            ds_item_code,
            ds_item_name,
            data_source,
            query_field,
            `desc`,
            active_flag,
            crte_time
        )
        VALUES(
               #{dsItemCode,jdbcType=VARCHAR},
               #{dsItemName,jdbcType=VARCHAR},
               #{dataSource,jdbcType=VARCHAR},
               #{queryField,jdbcType=VARCHAR},
               #{desc,jdbcType=VARCHAR},
               #{activeFlag,jdbcType=VARCHAR},
               now()
        )
    </insert>

    <!-- 修改 -->
    <update id="update">
        UPDATE cfg_nat_exam_ds_item
        SET ds_item_code = #{dsItemCode,jdbcType=VARCHAR},
            ds_item_name = #{dsItemName,jdbcType=VARCHAR},
            data_source = #{dataSource,jdbcType=VARCHAR},
            query_field = #{queryField,jdbcType=VARCHAR},
            `desc` = #{desc,jdbcType=VARCHAR},
            active_flag = #{activeFlag,jdbcType=VARCHAR}
        WHERE id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 移除 -->
    <delete id="remove">
        DELETE FROM cfg_nat_exam_ds_item
        WHERE id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 查询数据 -->
    <select id="queryList" resultType="com.my.som.vo.hosPerfAppraisal.HosPerfAppraisalDsItemVo">
        SELECT a.id AS id,
               a.ds_item_code AS dsItemCode,
               a.ds_item_name AS dsItemName,
               a.data_source AS dataSource,
               a.query_field AS queryField,
               a.desc AS "desc",
               a.active_flag AS activeFlag,
               a.crte_time AS crteTime
        FROM cfg_nat_exam_ds_item a
        <where>
            <if test="dsItemCode != null and dsItemCode != ''">
                AND a.ds_item_code LIKE CONCAT('%', #{dsItemCode,jdbcType=VARCHAR}, '%')
            </if>
            <if test="searchText != null and searchText != ''">
                AND (a.ds_item_name LIKE CONCAT('%', #{searchText,jdbcType=VARCHAR}, '%') OR a.ds_item_code LIKE CONCAT('%', #{searchText,jdbcType=VARCHAR}, '%'))
            </if>
            <if test="dataSource != null and dataSource != ''">
                AND a.data_source = #{dataSource,jdbcType=VARCHAR}
            </if>
            <if test="desc != null and desc != ''">
                AND a.desc LIKE CONCAT('%', #{desc,jdbcType=VARCHAR}, '%')
            </if>
        </where>
    </select>

</mapper>
