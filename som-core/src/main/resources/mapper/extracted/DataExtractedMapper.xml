<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.som.mapper.extracted.DataExtractedMapper">

    <update id="updateDi20">
        update som_setl_invy_bas_info set extract_flag = '1' where id = #{id} and extract_flag = '0'
    </update>

    <select id="queryDIInfo" resultType="java.util.Map">
        SELECT a.id,
               a.mdtrt_sn,
               a.unique_id AS uniqueid,
               a.fixmedins_code AS usercode,
               trim(d.rybh) as rybh,
                CASE
                WHEN TRIM(d.rybh) != TRIM(d.ybbh) THEN TRIM(d.rybh)
                ELSE TRIM(d.ybbh)
                END AS ybbh,
               d.mdtrt_id AS jzid,
               a.psn_name AS xm,
               a.gend AS xb,
               DATE_FORMAT(a.brdy, '%Y-%m-%d %H:%i:%s') AS csrq,
               a.age AS nl,
               a.ntly AS gj,
               a.nwb_adm_wt AS xserytz,
               a.nwb_bir_wt AS xsecstz,
               a.naty AS mz,
               a.certno AS sfzh,
               a.mrg_stas AS hy,
               a.birplc AS csd,
               a.napl AS gg,
               a.resd_addr AS hkdz,
               a.resd_addr_poscode AS yb2,
               a.curr_addr AS xzz,
               a.psn_tel AS dh,
               a.curr_addr_poscode AS yb1,
               a.empr_addr AS gzdwjdz,
               a.empr_tel AS dwdh,
               a.empr_poscode AS yb3,
               a.coner_name AS lxrxm,
               a.coner_rlts_code AS gx,
               a.coner_addr AS dz,
               a.coner_tel AS dh2,
               a.prfs AS zy,
               a.medfee_paymtd_code AS ylfkfs,
                <choose>
                    <when test="convert == 1">
                        a.ipt_no AS bah,
                    </when>
                    <when test="convert == 0">
                        a.medcasno AS bah,
                    </when>
                </choose>
               a.patn_ipt_cnt AS zycs,
               a.adm_way_code AS rytj,
               DATE_FORMAT(a.adm_date,'%Y-%m-%d %H:%i:%s') AS rysj,
               a.adm_caty AS rykbbm,
               a.adm_ward AS rybf,
               DATE_FORMAT(a.dscg_date,'%Y-%m-%d %H:%i:%s') AS cysj,
               a.dscg_caty AS cykbbm,
               a.dscg_ward AS cybf,
               a.ipt_days AS sjzyts,
               a.refldept_dept AS zkkbbm,
               a.deptdrt_name AS kzrdm,
               a.chfdr_name AS zrysdm,
               a.atddr_name AS zzysdm1,
               a.ipt_dr_code AS zyysdm,
               a.ipt_dr_name AS zyys,
               a.resp_nurs_code AS zrhsdm,
                a.resp_nurs_name AS zrhsxm,
               a.train_dr_name AS jxysdm,
               a.intn_dr_name AS sxys,
               a.codr_name AS bmydm,
               a.medcas_qlt_code AS bazl,
               a.qltctrl_dr_name AS zkysdm,
               a.qltctrl_nurs_name AS zkhsdm,
               DATE_FORMAT(a.qltctrl_date,'%Y-%m-%d %H:%i:%s') AS zkrq,
               a.dscg_way AS lyfs,
               a.dscg_31days_rinp_flag AS sfzzyjh,
               a.dscg_31days_rinp_pup AS md,
               a.trt_type AS zllb,
               a.acp_medins_code AS zynjsjgdm,
               a.acp_medins_name AS zynjsjg,
               a.chfpdr_code AS zzysdm,
               a.chfpdr_name AS zzysxm,
               a.damg_intx_ext_rea_disecode AS h23,
               a.damg_intx_ext_rea AS wbyy,
               a.drug_dicm_flag AS ywgm,
               a.dicm_drug_name AS gmyw,
               a.abo_code AS xx,
               a.rh_code rh,
               a.brn_damg_bfadm_coma_dura,
               a.brn_damg_afadm_coma_dura,
               a.die_autp_flag AS swhzsj,
               a.vent_used_dura,
               a.medfee_sumamt AS zfy,
               a.selfpay_amt AS zfje,
               a.ordn_med_servfee AS ylfuf,
               a.ordn_trt_oprt_fee AS zlczf,
               a.nurs_fee AS nursfee,
               a.com_med_serv_oth_fee AS oth_fee_com,
               a.palg_diag_fee AS cas_diag_fee,
               a.lab_diag_fee AS lab_diag_fee,
               a.rdhy_diag_fee AS rdhy_diag_fee,
               a.clnc_dise_fee AS clnc_diag_item_fee,
               a.nsrgtrt_item_fee AS nsrgtrt_item_fee,
               a.clnc_phys_trt_fee AS wlzlf,
               a.rgtrt_trt_fee AS oprn_treat_fee,
               a.anst_fee AS maf,
               a.oprn_fee AS ssf,
               a.rhab_fee AS rhab_fee,
               a.tcm_trt_fee AS tcm_treat_fee,
               a.wmfee AS west_fee,
               a.abtl_medn_fee AS kjywf,
               a.tcmpat_fee AS tcmpat_fee,
               a.tcmherb_fee AS tcmherb,
               a.blo_fee AS blo_fee,
               a.albu_fee AS bdblzpf,
               a.glon_fee AS qdblzpf,
               a.clotfac_fee AS nxyzlzpf,
               a.cyki_fee AS xbyzlzpf,
               a.exam_dspo_matl_fee AS hcyyclf,
               a.trt_dspo_matl_fee AS yyclf,
               a.oprn_dspo_matl_fee AS ycxyyclf,
               a.oth_fee AS oth_fee,
               a.offsite_med_treat AS ydjy,
               a.pre_exam as yqjcf,
                a.ADM_DIAG as adm_diag,
               b.ipt_patn_disediag_type_code,
               b.diag_code,
               b.maindiag_flag,
               b.diag_name,
               b.adm_cond_code,
               b.zd_ids,
               c.oprn_oprt_code,
               c.oprn_oprt_name,
               c.oprn_oprt_date,
               c.oprn_lv_code,
               c.oper_code,
               c.oper_name,
               c.asit_1_name,
               c.asit_name2,
               c.sinc_heal_lv_code,
               c.anst_mtd_code,
               c.anst_dr_code,
               c.anst_dr_name,
               c.anst_begntime,
               c.anst_endtime,
               c.oprn_oprt_begntime,
               c.oprn_oprt_endtime,
               c.ss_ids,
               d.*
        FROM som_setl_invy_bas_info a
                 LEFT JOIN
             (
                 SELECT di20_id,
                        GROUP_CONCAT(ipt_patn_disediag_type_code ORDER BY maindiag_flag DESC,ipt_patn_disediag_type_code asc,id SEPARATOR '@') AS ipt_patn_disediag_type_code,
                        GROUP_CONCAT(diag_code ORDER BY maindiag_flag DESC,ipt_patn_disediag_type_code asc,id SEPARATOR '@') AS diag_code,
                        GROUP_CONCAT(diag_name ORDER BY maindiag_flag DESC,ipt_patn_disediag_type_code asc,id SEPARATOR '@') AS diag_name,
                        GROUP_CONCAT(adm_dise_cond_code ORDER BY maindiag_flag DESC,ipt_patn_disediag_type_code asc,id SEPARATOR '@') AS adm_cond_code,
                        GROUP_CONCAT(maindiag_flag ORDER BY maindiag_flag DESC,ipt_patn_disediag_type_code asc,id SEPARATOR '@') AS maindiag_flag,
                        GROUP_CONCAT(di20_id ORDER BY maindiag_flag DESC,ipt_patn_disediag_type_code asc,id SEPARATOR '@') AS zd_ids
                 FROM som_diag_info
                 WHERE di20_id IN <include refid="di20IDs"/>
                 GROUP BY di20_id
             ) b
             ON a.id = b.di20_id
                 LEFT JOIN
             (
                 SELECT di20_id,
                        GROUP_CONCAT(oprn_oprt_code ORDER BY main_oprn_flag DESC,oprn_oprt_sn ASC SEPARATOR '@') oprn_oprt_code,
                        GROUP_CONCAT(oprn_oprt_name ORDER BY main_oprn_flag DESC,oprn_oprt_sn ASC SEPARATOR '@') oprn_oprt_name,
                        GROUP_CONCAT(DATE_FORMAT(oprn_oprt_date, '%Y-%m-%d %H:%i:%s') ORDER BY main_oprn_flag DESC,oprn_oprt_sn ASC SEPARATOR '@') oprn_oprt_date,
                        GROUP_CONCAT(COALESCE(oprn_lv_code,'-') ORDER BY main_oprn_flag DESC,oprn_oprt_sn ASC SEPARATOR '@') oprn_lv_code,
                        GROUP_CONCAT(COALESCE(oper_code,'-') ORDER BY main_oprn_flag DESC,oprn_oprt_sn ASC SEPARATOR '@') oper_code,
                        GROUP_CONCAT(COALESCE(oper_name,'-')  ORDER BY main_oprn_flag DESC,oprn_oprt_sn ASC SEPARATOR '@') oper_name,
                        GROUP_CONCAT(COALESCE(asit1_name,'-')  ORDER BY main_oprn_flag DESC,oprn_oprt_sn ASC SEPARATOR '@') asit_1_name,
                        GROUP_CONCAT(COALESCE(asit2_name,'-')  ORDER BY main_oprn_flag DESC,oprn_oprt_sn ASC SEPARATOR '@') asit_name2,
                        GROUP_CONCAT(COALESCE(sinc_heal_lv_code,'-')  ORDER BY main_oprn_flag DESC,oprn_oprt_sn ASC SEPARATOR '@') sinc_heal_lv_code,
                        GROUP_CONCAT(COALESCE(anst_mtd_code,'-') ORDER BY main_oprn_flag DESC,oprn_oprt_sn ASC SEPARATOR '@') anst_mtd_code,
                        GROUP_CONCAT(COALESCE(anst_dr_code, '-') ORDER BY main_oprn_flag DESC,oprn_oprt_sn ASC SEPARATOR '@') anst_dr_code,
                        GROUP_CONCAT(COALESCE(anst_dr_name, '-') ORDER BY main_oprn_flag DESC,oprn_oprt_sn ASC SEPARATOR '@') anst_dr_name,
                        GROUP_CONCAT(COALESCE(DATE_FORMAT(COALESCE(anst_begntime,''), '%Y-%m-%d %H:%i:%s'),'-') ORDER BY main_oprn_flag DESC,oprn_oprt_sn ASC SEPARATOR '@') anst_begntime,
                        GROUP_CONCAT(COALESCE(DATE_FORMAT(COALESCE(anst_endtime,''), '%Y-%m-%d %H:%i:%s'),'-') ORDER BY main_oprn_flag DESC,oprn_oprt_sn ASC SEPARATOR '@') anst_endtime,
                        GROUP_CONCAT(DATE_FORMAT(oprn_oprt_begntime, '%Y-%m-%d %H:%i:%s') ORDER BY main_oprn_flag DESC,oprn_oprt_sn ASC SEPARATOR '@') oprn_oprt_begntime,
                        GROUP_CONCAT(DATE_FORMAT(oprn_oprt_endtime, '%Y-%m-%d %H:%i:%s') ORDER BY main_oprn_flag DESC,oprn_oprt_sn ASC SEPARATOR '@') oprn_oprt_endtime,
                        GROUP_CONCAT(di20_id ORDER BY main_oprn_flag DESC,oprn_oprt_sn ASC SEPARATOR '@') AS ss_ids
                 FROM som_oprn_rcd
                 WHERE di20_id IN <include refid="di20IDs"/>
                 GROUP BY di20_id
             ) c
             ON a.id = c.di20_id
                 LEFT JOIN
             (
                 SELECT ifnull (g.psn_no,a.psn_no) as rybh,
                        g.hi_no as ybbh,
                        ifnull (g.mdtrt_id,a.mdtrt_id) as mdtrt_id,
                        g.setl_id  as jsid,
                        g.bill_code AS pjdm,
                        g.bill_no AS pjhm,
                        a.mul_nwb_bir_wt as dxsecstz,
                        a.mul_nwb_adm_wt as dxserytz,
                        a.mdtrt_id AS mi,

                        a.nwb_age AS bzyzsnl,
                        g.hi_setl_lv AS ybjsdj,
                        '' as patn_cert_type,
                        g.hi_type AS yblx,
                        g.sp_psn_type AS tsrylx,
                        g.insuplc AS cbd,
                        a.nwb_adm_type AS xserylx,
                        g.biz_sn AS ywlsh,
                        g.ipt_med_type AS zyyllx,
                        a.spga_nurscare_days AS tjhlts,
                        a.lv1_nurscare_days AS yjhlts,
                        a.scd_nurscare_days AS ejhlts,
                        a.lv3_nurscare_days AS sjhlts,
                        a.chfpdr_code,
                        a.chfpdr_name,
                        a.otp_wm_diag_dise_code AS jbbm,
                        a.otp_wm_diag AS mzzd,
                        a.otp_tcm_diag_dise_code AS jbbm_zyzd,
                        a.otp_tcm_diag AS mzzd_zyzd,
                        a.bld_cat AS sxpz,
                        a.bld_amt AS sxl,
                        a.bld_unt AS sxjldw,
                        DATE_FORMAT(g.setl_begn_date,'%Y-%m-%d %H:%i:%s') AS jskssj,
                        DATE_FORMAT(g.setl_end_date,'%Y-%m-%d %H:%i:%s') AS jsjssj,

                        g.psn_selfpay AS grzf,
                        g.psn_ownpay AS grzf1,
                        g.acct_pay AS grzhzf,
                        g.psn_cashpay AS grxjzf,
                        g.hi_paymtd AS ybzffs,
                        g.medins_fill_dept AS yljgtbbm,
                        g.medins_fill_psn AS yljgtbr,
                        a.opsp_diag_caty AS mtmmzdkb,
                        DATE_FORMAT(a.opsp_mdtrt_date, '%Y-%m-%d %H:%i:%s') AS mtmmjzrq,
                        g.hsorg AS ybjg,
                        g.hsorg_opter AS ybjgjbr,
                        b.fund_pay_type,
                        b.fund_payamt,
                        b.pay_ids,
                        c.mtmm_oprn_oprt_name,
                        c.mtmm_oprn_oprt_code,
                        c.mtmm_diag_code,
                        c.mtmm_diag_name,
                        c.opsp_ids,
                        d.med_chrgitm,
                        d.amt,
                        d.claa_sumfee,
                        d.clab_amt,
                        d.fulamt_ownpay_amt,
                        d.oth_amt,
                        d.item_ids,
                        e.scs_cutd_ward_type,
                        e.scs_cutd_inpool_time,
                        e.scs_cutd_exit_time,
                        e.scs_cutd_sum_dura,
                        e.icu_ids,
                        f.bld_cat,
                        f.bld_amt,
                        f.bld_unt,
                        f.bld_ids
                 FROM som_setl_invy_bas_info a
                          LEFT JOIN
                      (
                          SELECT di20_id,
                                 GROUP_CONCAT(fund_pay_type SEPARATOR '@') fund_pay_type,
                                 GROUP_CONCAT(fund_payamt SEPARATOR '@') fund_payamt,
                                 GROUP_CONCAT(di20_id SEPARATOR '@') AS pay_ids
                          FROM som_fund_pay_info
                            where di20_id in <include refid="di20IDs"/>
                        and fund_payamt is not null and fund_payamt != 0
                        and fund_pay_type is not null and fund_pay_type !=''
                          GROUP BY di20_id
                      ) b
                      ON a.id = b.di20_id
                          LEFT JOIN
                      (
                          SELECT di20_id,
                                 GROUP_CONCAT(diag_code SEPARATOR '@') mtmm_diag_code,
                                 GROUP_CONCAT(diag_name SEPARATOR '@') mtmm_diag_name,
                                 GROUP_CONCAT(oprn_oprt_name SEPARATOR '@') mtmm_oprn_oprt_name,
                                 GROUP_CONCAT(oprn_oprt_code SEPARATOR '@') mtmm_oprn_oprt_code,
                                 GROUP_CONCAT(di20_id SEPARATOR '@') AS opsp_ids
                          FROM som_otp_crds_diag_info
                          where di20_id in <include refid="di20IDs"/>
                          GROUP BY di20_id
                      ) c
                      ON a.id = c.di20_id
                          LEFT JOIN
                      (
                          SELECT di20_id,
                                 GROUP_CONCAT(med_chrgitm SEPARATOR '@') med_chrgitm,
                                 GROUP_CONCAT(amt SEPARATOR '@') amt,
                                 GROUP_CONCAT(claa_sumfee SEPARATOR '@') claa_sumfee,
                                 GROUP_CONCAT(clab_amt SEPARATOR '@') clab_amt,
                                 GROUP_CONCAT(fulamt_ownpay_amt SEPARATOR '@') fulamt_ownpay_amt,
                                 GROUP_CONCAT(oth_amt SEPARATOR '@') oth_amt,
                                 GROUP_CONCAT(di20_id SEPARATOR '@') AS item_ids
                          FROM som_chrg_item_info
                        where di20_id in <include refid="di20IDs"/>
                          GROUP BY di20_id
                      ) d
                      ON a.id = d.di20_id
                          LEFT JOIN
                      (
                          SELECT di20_id,
                                 GROUP_CONCAT(scs_cutd_ward_type SEPARATOR '@') scs_cutd_ward_type,
                                 GROUP_CONCAT(DATE_FORMAT(scs_cutd_inpool_time, '%Y-%m-%d %H:%i:%s') SEPARATOR '@') scs_cutd_inpool_time,
                                 GROUP_CONCAT(DATE_FORMAT(scs_cutd_exit_time, '%Y-%m-%d %H:%i:%s') SEPARATOR '@') scs_cutd_exit_time,
                                 GROUP_CONCAT(scs_cutd_sum_dura SEPARATOR '@') scs_cutd_sum_dura,
                                 GROUP_CONCAT(di20_id SEPARATOR '@') AS icu_ids
                          FROM som_scs_cutd_info
                            where di20_id in <include refid="di20IDs"/>
                          GROUP BY di20_id
                      ) e
                      ON a.id = e.di20_id
                          LEFT JOIN
                      (
                          SELECT di20_id,
                                 GROUP_CONCAT(bld_cat SEPARATOR '@') bld_cat,
                                 GROUP_CONCAT(bld_amt SEPARATOR '@') bld_amt,
                                 GROUP_CONCAT(bld_unt SEPARATOR '@') bld_unt,
                                 GROUP_CONCAT(di20_id SEPARATOR '@') AS bld_ids
                          FROM som_bld_info
                            where di20_id in <include refid="di20IDs"/>
                          GROUP BY di20_id
                      ) f
                      ON a.id = f.di20_id
                    left join som_setl_info g on a.id = g.di20_id
                    WHERE a.id IN <include refid="di20IDs"/>
             ) d
             ON a.mdtrt_id = d.mi
            WHERE a.id IN <include refid="di20IDs"/>
    </select>

    <select id="queryRepeatIdsDi20" resultType="com.my.som.vo.task.HandlerTask">
        SELECT id FROM som_setl_invy_bas_info
        WHERE unique_id IN ( SELECT unique_id FROM som_setl_invy_bas_info GROUP BY unique_id HAVING COUNT(1) > 1 )
          AND id NOT IN ( SELECT MAX(id) FROM som_setl_invy_bas_info GROUP BY unique_id HAVING COUNT(1) > 1 )
    </select>


    <delete id="deleteDi20">
        DELETE FROM som_setl_invy_bas_info where id = #{id,jdbcType=INTEGER}
    </delete>
    <delete id="deleteDi20Bldinfo">
        DELETE FROM som_bld_info where di20_id = #{id,jdbcType=INTEGER}
    </delete>
    <delete id="deleteDi20Diseinfo">
        DELETE FROM som_diag_info where di20_id = #{id,jdbcType=INTEGER}
    </delete>
    <delete id="deleteDi20Icuinfo">
        DELETE FROM som_scs_cutd_info where di20_id = #{id,jdbcType=INTEGER}
    </delete>
    <delete id="deleteDi20Iteminfo">
        DELETE FROM som_chrg_item_info where di20_id = #{id,jdbcType=INTEGER}
    </delete>
    <delete id="deleteDi20Oprninfo">
        DELETE FROM som_oprn_rcd where di20_id = #{id,jdbcType=INTEGER}
    </delete>
    <delete id="deleteDi20Opspdiseinfo">
        DELETE FROM som_otp_crds_diag_info where di20_id = #{id,jdbcType=INTEGER}
    </delete>
    <delete id="deleteDi20Payinfo">
        DELETE FROM som_fund_pay_info where di20_id = #{id,jdbcType=INTEGER}
    </delete>
    <delete id="deleteDi20Setlinfo">
        DELETE FROM som_setl_info where di20_id = #{id,jdbcType=INTEGER}
    </delete>

    <sql id="di20IDs">
        <foreach collection="pageIds" open="(" close=")" separator="," item="id">
            #{id}
        </foreach>
    </sql>
</mapper>
