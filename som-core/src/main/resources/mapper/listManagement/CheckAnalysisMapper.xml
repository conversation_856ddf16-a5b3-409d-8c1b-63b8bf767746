<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.som.mapper.listManagement.CheckAnalysisMapper">
    <select id="queryErrorNum" resultType="com.my.som.vo.listManagement.CheckAnalysisVo">
        SELECT a.errorNum,
               a.errType
        FROM
             (
                 SELECT COUNT(1) AS errorNum,
                        a.err_type AS errType
                 FROM som_invy_chk_detl a
                 LEFT JOIN som_hi_invy_bas_info b
                 ON a.SETTLE_LIST_ID = b.ID
                 <where>
                     <if test="begnDate != null and begnDate != ''">
                        AND b.B15 BETWEEN #{begnDate} AND CONCAT(#{expiDate},' 23:59:59')
                    </if>
                     <if test="settlementStartTime != null and settlementStartTime != ''">
                        AND b.D37 BETWEEN #{settlementStartTime,jdbcType=VARCHAR} AND CONCAT(#{settlementEndTime,jdbcType=VARCHAR},' 23:59:59')
                    </if>
                     <if test="inStartTime != null and
                               inStartTime != '' and
                               inEndTime != null and
                               inEndTime != ''">
                         AND b.B12 BETWEEN #{inStartTime} AND CONCAT(#{inEndTime},' 23:59:59')
                     </if>
                     <if test="hospitalId != null and hospitalId != ''">
                       AND b.HOSPITAL_ID = #{hospitalId}
                     </if>
                     <if test="deptCode != null and deptCode != ''">
                       AND b.B16C = #{deptCode}
                     </if>
                     <if test="drCodg != null and drCodg != ''">
                       AND b.B25C = #{drCodg}
                     </if>
                 </where>
                 GROUP BY err_type
             ) a
        ORDER BY errorNum
    </select>

    <select id="queryErrorData" resultType="com.my.som.vo.listManagement.CheckAnalysisVo">
        SELECT b.A48 AS bah,
               b.A11 AS name,
               b.B15 AS outHosTime,
               b.B16C AS deptCode,
               b.B25C AS drCodg,
               b.B25N AS drName,
               c.`NAME` AS deptName,
               a.err_type AS errType,
               a.err_dscr AS errDscr
        FROM som_invy_chk_detl a
        LEFT JOIN som_hi_invy_bas_info b
        ON a.SETTLE_LIST_ID = b.ID
        LEFT JOIN som_dept c
            ON b.B16C = c.`CODE`
        AND b.HOSPITAL_ID = c.HOSPITAL_ID
        <if test="enableSeAns != null and enableSeAns != '' and enableSeAns == 1">
            INNER JOIN som_setl_cas_crsp p
                ON b.K00 = p.K00
        </if>
        <where>
            <if test="begnDate != null and begnDate != ''">
                AND b.B15 BETWEEN #{begnDate} AND CONCAT(#{expiDate},' 23:59:59')
            </if>
            <if test="settlementStartTime != null and settlementStartTime != ''">
                AND b.D37 BETWEEN #{settlementStartTime,jdbcType=VARCHAR} AND CONCAT(#{settlementEndTime,jdbcType=VARCHAR},' 23:59:59')
            </if>
            <if test="inStartTime != null and
                  inStartTime != '' and
                  inEndTime != null and
                  inEndTime != ''">
                AND b.B12 BETWEEN #{inStartTime} AND CONCAT(#{inEndTime},' 23:59:59')
            </if>
            <if test="errType != null and errType != ''">
                AND a.err_type = #{errType}
            </if>
            <if test="deptCode != null and deptCode != ''">
                AND b.B16C = #{deptCode}
            </if>
            <if test="drCodg != null and drCodg != ''">
                AND b.B25C = #{drCodg}
            </if>
            <if test="hospitalId != null and hospitalId != ''">
                AND b.HOSPITAL_ID = #{hospitalId}
            </if>
            <if test="medcasCodg != null and medcasCodg != ''">
                AND b.A48 LIKE CONCAT('%',#{medcasCodg},'%')
            </if>
        </where>
    </select>

    <select id="queryDeptErrorNum"  resultType="com.my.som.vo.listManagement.CheckAnalysisVo">
        SELECT a.errorNum,
               a.deptCode,
               a.deptName
        FROM
             (
                 SELECT COUNT(DISTINCT(SETTLE_LIST_ID)) AS errorNum,
                        b.B16C AS deptCode,
                        c.`NAME` AS deptName
                 FROM som_invy_chk_detl a
                 LEFT JOIN som_hi_invy_bas_info b
                 ON a.SETTLE_LIST_ID = b.ID
                 LEFT JOIN som_dept c
                 ON b.B16C = c.`CODE`
                 AND b.HOSPITAL_ID = c.HOSPITAL_ID
                 WHERE b.B16C IS NOT NULL
                   AND b.B16C != ''
                   <if test="begnDate != null and begnDate != ''">
                        AND b.B15 BETWEEN #{begnDate} AND CONCAT(#{expiDate},' 23:59:59')
                    </if>
                    <if test="settlementStartTime != null and settlementStartTime != ''">
                        AND b.D37 BETWEEN #{settlementStartTime,jdbcType=VARCHAR} AND CONCAT(#{settlementEndTime,jdbcType=VARCHAR},' 23:59:59')
                    </if>
                 <if test="inStartTime != null and
                           inStartTime != '' and
                           inEndTime != null and
                           inEndTime != ''">
                     AND b.B12 BETWEEN #{inStartTime} AND CONCAT(#{inEndTime},' 23:59:59')
                 </if>
                 <if test="hospitalId != null and hospitalId != ''">
                     AND b.HOSPITAL_ID = #{hospitalId}
                 </if>
                 GROUP BY b.B16C,
                          c.`NAME`
             ) a
        ORDER BY a.errorNum DESC
        <if test="chartType == 2">
            LIMIT ${limit}
        </if>
    </select>

    <select id="queryErrorType" resultType="java.lang.String">
        SELECT b.err_type AS errType
        FROM som_hi_invy_bas_info a
        INNER JOIN som_invy_chk_detl b
            ON a.ID = b.SETTLE_LIST_ID
        <if test="enableSeAns != null and enableSeAns != '' and enableSeAns == 1">
            INNER JOIN som_setl_cas_crsp p
                ON a.K00 = p.K00
        </if>
        <where>
            <if test="begnDate != null and begnDate != '' and
                      expiDate != null and expiDate != ''">
                AND a.B15 BETWEEN #{begnDate} AND CONCAT(#{expiDate},' 23:59:59')
            </if>
            <if test="settlementStartTime != null and settlementStartTime != ''">
                AND a.D37 BETWEEN #{settlementStartTime,jdbcType=VARCHAR} AND CONCAT(#{settlementEndTime,jdbcType=VARCHAR},' 23:59:59')
            </if>
            <if test="inStartTime != null and inStartTime != '' and
                      inEndTime != null and inEndTime != ''">
                AND a.B12 BETWEEN #{inStartTime} AND CONCAT(#{inEndTime},' 23:59:59')
            </if>
            <if test="hospitalId != null and hospitalId != ''">
                AND a.HOSPITAL_ID = #{hospitalId}
            </if>
        </where>
        GROUP BY b.err_type
    </select>
</mapper>
