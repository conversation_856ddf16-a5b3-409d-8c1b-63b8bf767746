<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.som.mapper.listManagement.PatientInfoMapper">

    <select id="queryPatientInfo" resultType="com.my.som.vo.listManagement.PatientInfoVo">
        select a.ID as id,
               a.A11 as a11,
               a.A48 as a48,
               a.B16N as b16n,
               a.B25N as b25n,
               case when b.k00 is null then 0 else 1 end as mark,
               a.k00,
               a.id AS settleListId
        from som_hi_invy_bas_info a
        left join som_invy_label b
        on a.K00 = b.K00
        left join som_invy_upld_log c on a.K00 = c.K00
        where a.ACTIVE_FLAG = '1'
        <if test="dto.errType != null and dto.errType.size() > 0">
            AND
            <foreach collection="dto.errType" item="item" open="(" close=")" separator=" OR ">
                <if test="item == 1">
                    <foreach collection="list" item="listItem" separator=" OR ">
                        ${listItem} IS NULL
                        OR ${listItem} = ''
                    </foreach>
                </if>
                <if test="item == 2">
                    c.err_msg LIKE '%住院时间与系统内住院日期不符%'
                </if>
                <if test="item == 3">
                    (a.A46C = 7 OR a.A46C = 07) AND c.upld_stas = 1
                </if>
            </foreach>
        </if>
          <if test="dto.uploadFlag != null and dto.uploadFlag.size() > 0">
              AND
              <foreach collection="dto.uploadFlag" item="item" open="(" close=")" separator=" OR ">
                  <if test="item == 0">
                      a.UPLOAD_FLAG = 0
                  </if>
                  <if test="item == 1">
                      c.upld_stas = 0
                  </if>
                  <if test="item == 2">
                      c.upld_stas = 1
                  </if>
              </foreach>
          </if>
          <if test="dto.settlementStartTime != null and dto.settlementStartTime != '' and dto.settlementEndTime != null and dto.settlementEndTime != ''">
              AND a.D37 BETWEEN #{dto.settlementStartTime,jdbcType=VARCHAR}
                            AND CONCAT(#{dto.settlementEndTime,jdbcType=VARCHAR},' 23:59:59')
          </if>
        <if test="dto.finishSign != null and dto.finishSign != ''">
            <if test="dto.finishSign == 0">
                and a.K00 not in (
                    select K00 from som_invy_label where k00 is not null
                )
            </if>
            <if test="dto.finishSign == 1">
                and a.K00 in (
                    select K00 from som_invy_label
                )
            </if>
        </if>
        <if test="dto.hospitalId != null and dto.hospitalId != ''">
            and a.HOSPITAL_ID = #{dto.hospitalId,jdbcType=VARCHAR}
        </if>
        <if test="dto.cy_start_date != null and dto.cy_start_date != '' and dto.cy_end_date != null and dto.cy_end_date != ''">
            and a.B15 BETWEEN #{dto.cy_start_date,jdbcType=VARCHAR} and CONCAT(#{dto.cy_end_date,jdbcType=VARCHAR},' 23:59:59')
        </if>
        <if test="dto.a48 != null and dto.a48 != ''">
            and a.A48 like concat('%',#{dto.a48,jdbcType=VARCHAR},'%')
        </if>
        <if test="dto.deptCode != null and dto.deptCode != ''">
            and a.B16C = #{dto.deptCode,jdbcType=VARCHAR}
        </if>
        <if test="dto.updateSign != null and dto.updateSign != ''">
            <if test="dto.updateSign == 0">
                and a.K00 not in (
                select K00 from som_invy_upld_modi_rcd where k00 is not null
                )
            </if>
            <if test="dto.updateSign == 1">
                and a.K00 in (
                select K00 from som_invy_upld_modi_rcd
                )
            </if>
        </if>
    </select>
</mapper>
