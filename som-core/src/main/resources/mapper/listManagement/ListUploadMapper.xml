<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.som.mapper.listManagement.ListUploadMapper">
    <!-- 删除操作明细 -->
    <delete id="deleteUploadLog">
        DELETE
        FROM som_invy_upld_log
        WHERE K00 IN
        <foreach collection="k00s" item="k00" open="(" separator="," close=")">
            #{k00}
        </foreach>
    </delete>
    <delete id="deleteUploadSybLog">
        DELETE
        FROM som_city_hi_invy_upld_log
        WHERE K00 IN
        <foreach collection="k00s" item="k00" open="(" separator="," close=")">
            #{k00}
        </foreach>
    </delete>

    <select id="queryData" resultType="com.my.som.vo.listManagement.ListUploadVo">

        SELECT a.id      AS id,
               a.K00     AS k00,
               a.A48     AS medcasCodg,
               a.A11     AS name,
               a.A12C    AS gend,
               a.A14     AS age,
               a.B12     AS inHosTime,
               a.B15     AS outHosTime,
               a.B16C    AS deptCode,
               a.C03C    AS mainDiseaseCode,
               a.C04N    AS mainDiseaseName,
               a.C14x01C AS mainOperatorCode,
               a.C15x01N AS mainOperatorName,
               a.D01     AS inHosTotalCost,
                a.D35 AS listSerialNumFlag,
        CASE  WHEN a.a56  IS NULL or a.a56 =''  THEN '未传值'
        WHEN SUBSTRING(a.a56, 1, 4) = SUBSTRING(#{provLevelInsuplcAdmdvs}, 1, 4)THEN '省本级'
        WHEN SUBSTRING(a.a56, 1, 4) = SUBSTRING(#{insuplcAdmdvs}, 1, 4) THEN '市医保'
        WHEN SUBSTRING(a.a56, 1, 2) = SUBSTRING(#{insuplcAdmdvs}, 1, 2) AND SUBSTRING(a.a56, 1, 4) != SUBSTRING(#{insuplcAdmdvs}, 1, 4) THEN CONCAT('省内异地-', t2.cityName)
        ELSE '省外异地'
        END AS isRemote,    c.`NAME`         AS deptName,
        d.chk_stas       AS chkStas,
        a.look_over      AS lookOver,
        m.`NAME`         AS drName
        <choose>
            <when test="grperType == 1">
                <choose>
                    <when test="onlyGroupCanUpload != null and onlyGroupCanUpload != '' and onlyGroupCanUpload == 'true' ">
                        ,b.grp_stas as isGroup
                    </when>
                    <otherwise>
                        ,'1' as isGroup
                    </otherwise>
                </choose>
               ,b.dip_codg AS dipCodg,
                b.DIP_NAME AS dipName
            </when>
            <when test="grperType == 3">
                ,b.drg_codg AS drgCodg,
                b.DRG_NAME AS drgName
                <choose>
                    <when test="onlyGroupCanUpload != null and onlyGroupCanUpload != '' and onlyGroupCanUpload == 'true' ">
                        ,b.grp_stas as isGroup
                    </when>
                    <otherwise>
                        ,'1' as isGroup
                    </otherwise>
                </choose>
            </when>
            <otherwise>
                ,'1' as isGroup
            </otherwise>
        </choose>

<!--       , f.seq,-->
<!--        f.dscg_diag_codg AS mainDiseaseCode,-->
<!--        f.dscg_diag_name AS mainDiseaseName,-->
<!--        g.seq,-->
<!--        g.C35C           AS mainOperatorCode,-->

        FROM som_hi_invy_bas_info a
        <choose>
            <when test="grperType == 1">
                LEFT JOIN som_dip_grp_info b ON a.ID = b.SETTLE_LIST_ID
            </when>
            <when test="grperType == 3">
                LEFT JOIN som_drg_grp_info b ON a.ID = b.SETTLE_LIST_ID
            </when>
        </choose>

        LEFT JOIN som_dept c
                  ON a.B16C = c.`CODE`
                      AND a.HOSPITAL_ID = c.HOSPITAL_ID
        LEFT JOIN som_hi_dr_crsp m
                  ON a.B25C = m.`oper_dr_code`
        AND a.HOSPITAL_ID = m.HOSPITAL_ID
        LEFT JOIN som_setl_invy_chk d
                  ON a.id = d.SETTLE_LIST_ID
        LEFT JOIN som_invy_upld_modi_rcd e
                  ON a.K00 = e.K00
        LEFT JOIN som_diag f
                  ON f.SETTLE_LIST_ID = a.ID
        LEFT JOIN som_oprn_oprt_info g
                  ON g.SETTLE_LIST_ID = a.ID
        LEFT JOIN (
        SELECT SUBSTR(data_val, 1, 4) AS cbd, labl_name  as  cityName
        FROM `som_sys_code`
        WHERE code_type = 'XZQHDM'
        AND data_val LIKE '%00'
        AND SUBSTRING(data_val, 1, 2) = SUBSTRING(#{insuplcAdmdvs}, 1, 2)
        union
        SELECT
        CONCAT(SUBSTR(data_val, 1, 2), '99') AS cbd,
        labl_name AS cityName
        FROM
        `som_sys_code`
        WHERE
        code_type = 'XZQHDM'
        AND data_val LIKE '%0000'
        AND SUBSTRING(data_val, 1, 2) = SUBSTRING(#{insuplcAdmdvs}, 1, 2)

        ) t2 ON t2.cbd = SUBSTRING(a.a56, 1, 4)
        <if test="enableSeAns != null and enableSeAns != '' and enableSeAns == 1">
            INNER JOIN som_setl_cas_crsp p
                       ON a.K00 = p.K00
        </if>
        WHERE a.ACTIVE_FLAG = 1
        <if test="isRemote != null and isRemote != ''">
            <if test="isRemote == 0">
                AND SUBSTRING(a.a56,1,4)
                = SUBSTRING(#{insuplcAdmdvs},1,4)
<!--                AND a.YDJY = '否'-->
            </if>
            <if test="isRemote == 1">
            <!--省内异地-->
                AND  SUBSTRING(a.a56,1,4)
                != SUBSTRING(#{insuplcAdmdvs},1,4)
                AND  SUBSTRING(a.a56,1,2)
                = SUBSTRING(#{insuplcAdmdvs},1,2)
<!--                AND a.YDJY = '是'-->
            </if>
            <if test="isRemote == 2">
            <!--省外异地-->
                AND  SUBSTRING(a.a56,1,2)
                 != SUBSTRING(#{insuplcAdmdvs},1,2)
<!--                AND a.YDJY = '是'-->
            </if>
            <if test="isRemote == 3">
                <!--省本级-->
                AND  SUBSTRING(a.a56,1,4)
                = #{provLevelInsuplcAdmdvs}
                <!--                AND a.YDJY = '是'-->
            </if>
            <if test="isRemote == 4">
                <!--未传值-->
                AND (a.a56  IS NULL or a.a56 ='')
            </if>
        </if>
        <if test="isSpend != null and isSpend != ''">
            <if test="isSpend == 0">
                AND a.A46C != 7 AND a.A46C != 07
            </if>
            <if test="isSpend == 1">
                OR a.A46C = 7
                OR a.A46C = 07
            </if>
        </if>
        AND a.D37 BETWEEN #{begnDate} AND CONCAT(#{expiDate}, ' 23:59:59')
            AND a.UPLOAD_FLAG = #{uploadFlag}
            AND a.k00 NOT IN (SELECT k00
                              from som_invy_upld_log
                              WHERE K00 in (select k00
                                            from som_hi_invy_bas_info
                                            where D37 BETWEEN #{begnDate} AND CONCAT(#{expiDate}, ' 23:59:59')))
            AND f.seq = 0
            AND COALESCE(g.seq, 0) = 0
        <if test="isGroup != null and isGroup != ''">
            AND  b.grp_stas = #{isGroup}
        </if>
        <if test="allUpload">
            AND d.chk_stas = '1'
        </if>
        <if test="inStartTime != null and inStartTime != '' and inEndTime != null and inEndTime != ''">
            AND a.B12 BETWEEN #{inStartTime} AND CONCAT(#{inEndTime}, ' 23:59:59')
        </if>
        <if test="lookOver != null and lookOver != ''">
            AND a.look_over = #{lookOver,jdbcType=VARCHAR}
        </if>
        <if test="validateOver != null and validateOver != ''">
            AND d.chk_stas = #{validateOver,jdbcType=VARCHAR}
        </if>

        <if test="listSerialNumFlag!=null and listSerialNumFlag!='' ">
            <choose>
                <when test="listSerialNumFlag == 1">
                    AND a.D35 != '' AND a.D35 IS NOT NULL
                </when>
                <when test="listSerialNumFlag == 0">
                    AND (a.D35 = '' OR a.D35 IS NULL)
                </when>
            </choose>
        </if>
        <include refid="queryCondition"/>
        <include refid="com.my.som.mapper.newBusiness.common.NewBusinessCommonAnalysisMapper.queryCommon">
        </include>
        ORDER BY a.B15
    </select>

    <update id="updateData">
        UPDATE som_hi_invy_bas_info
        SET UPLOAD_FLAG = !UPLOAD_FLAG
        WHERE ID IN (
        <foreach collection="ids" item="id" separator=",">
            #{id}
        </foreach>
        )
    </update>

    <update id="updateDataBySetlId">
        UPDATE som_hi_invy_bas_info
        SET UPLOAD_FLAG = #{upldStas}
        WHERE ID = #{settleListId}
    </update>

    <insert id="insertOplog">
        INSERT INTO list_upload_revoke_log(USER_NAME, nknm, `TIME`, REASON, `TYPE`, NEWEST_FLAG, BATCH_NUM)
        VALUES (#{userName}, #{nknm}, now(), #{reason}, #{type}, '1', #{batchNum})
    </insert>

    <select id="queryOplog" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM list_upload_revoke_log
        WHERE USER_NAME = #{userName}
    </select>

    <update id="updateOplog">
        UPDATE list_upload_revoke_log
        SET NEWEST_FLAG = '0'
        WHERE USER_NAME = #{userName}
          AND TYPE = #{type}
    </update>

    <!-- 修改状态(stas_type) -->
    <update id="updateStasType">
        UPDATE som_invy_upld_log
        SET STAS_TYPE = #{isSuccess,jdbcType=VARCHAR}
        WHERE k00 IN
        <foreach collection="k00s" item="k00" separator="," open="(" close=")">
            #{k00,jdbcType=VARCHAR}
        </foreach>
    </update>
    <update id="updateSybData">
        UPDATE som_hi_invy_bas_info
        SET UPLOAD_SYB_FLAG = '1' WHERE ID IN (
        <foreach collection="ids" item="id" separator=",">
            #{id}
        </foreach>
        )
    </update>

    <insert id="insertOpPatient">
        INSERT INTO list_uolpd_revoke_patient_log(BATCH_NUM, K00) VALUES
        <foreach collection="k00s" item="k00" separator=",">
            (#{batchNum}, #{k00})
        </foreach>
    </insert>

    <!-- 上传操作记录 -->
    <insert id="insertUploadOpeLog">
        INSERT INTO som_setl_invy_oprt_log(USER_NAME, TIME, succ_cnt, fale_cnt, BATCH_NUM)
        VALUES (#{userName,jdbcType=VARCHAR},
                now(),
                #{succCnt,jdbcType=VARCHAR},
                #{faleCnt,jdbcType=VARCHAR},
                #{batchNum,jdbcType=VARCHAR})
    </insert>

    <!-- 上传记录表 -->
    <insert id="insertUploadLog">
        INSERT INTO som_invy_upld_log(K00, SETTLE_LIST_ID, reqt_para, trns_no, err_msg, upld_stas, BATCH_NUM, upld_time,
                                      retn_invy_sn, STAS_TYPE)
        <foreach collection="list" item="item" separator="," open="VALUES">
            (#{item.k00,jdbcType=VARCHAR},
             #{item.settleListId,jdbcType=VARCHAR},
             #{item.reqtPara,jdbcType=VARCHAR},
             #{item.trns_no,jdbcType=VARCHAR},
             #{item.errMsg,jdbcType=VARCHAR},
             #{item.upldStas,jdbcType=VARCHAR},
             #{item.batchNum,jdbcType=VARCHAR},
             now(),
             #{item.resSettleListNo,jdbcType=VARCHAR},
             #{item.stasType,jdbcType=VARCHAR})
        </foreach>
    </insert>
    <insert id="insertUploadSybOpeLog">
        INSERT INTO som_city_hi_setl_invy_oprt_log(USER_NAME, TIME, succ_cnt, fale_cnt, BATCH_NUM)
        VALUES (#{userName,jdbcType=VARCHAR},
                now(),
                #{succCnt,jdbcType=VARCHAR},
                #{faleCnt,jdbcType=VARCHAR},
                #{batchNum,jdbcType=VARCHAR})
    </insert>
    <insert id="insertUploadSybLog">
        INSERT INTO som_city_hi_invy_upld_log(K00, SETTLE_LIST_ID, REQUEST_PARAM, INFNO, ERROR_MSG, UPLOAD_STATE, BATCH_NUM,
        UPLOAD_TIME, RES_SETTLE_LIST_ID, STAS_TYPE)
        <foreach collection="list" item="item" separator="," open="VALUES">
            (#{item.k00,jdbcType=VARCHAR},
             #{item.settleListId,jdbcType=VARCHAR},
             #{item.reqtPara,jdbcType=VARCHAR},
             #{item.trns_no,jdbcType=VARCHAR},
             #{item.errMsg,jdbcType=VARCHAR},
             #{item.upldStas,jdbcType=VARCHAR},
             #{item.batchNum,jdbcType=VARCHAR},
             now(),
             #{item.resSettleListNo,jdbcType=VARCHAR},
             #{item.stasType,jdbcType=VARCHAR})
        </foreach>
    </insert>

    <select id="queryRecordData" resultType="com.my.som.vo.listManagement.ListUploadVo">
        SELECT a.USER_NAME AS userName,
               a.Time      AS time,
               a.succ_cnt  AS succCnt,
               a.fale_cnt  AS faleCnt,
               a.BATCH_NUM AS batchNum
        FROM som_setl_invy_oprt_log a
        WHERE a.`TIME` BETWEEN #{begnDate,jdbcType=VARCHAR} and CONCAT(#{expiDate,jdbcType=VARCHAR}, ' 23:59:59')
        ORDER BY a.`TIME` DESC
    </select>

    <select id="querySetlInfoData" resultType="com.my.som.vo.listManagement.SetlInfoVo">
        SELECT a.id  AS settleListId,
               a.k00,
               a.A20 AS a20, <!-- 身份证 -->
        a.A11 AS a11, <!-- 姓名 -->
        a.PSN_NO AS psn_no, <!-- 人员编号 -->
        a.clinic_id AS mdtrt_id, <!-- 就诊id -->
        a.settlement_id AS setl_id, <!-- 结算id -->
        a.A51 AS hi_no,<!-- 医保编号 -->
        a.A48 AS medcasno,<!-- 病案号 -->
        COALESCE(a.A52, NOW()) AS dcla_time,<!-- 申报时间 按眉山要求将当前时间转为申报时间 -->
        a.A15C AS ntly,<!-- 国籍 -->
        a.A38C AS prfs,<!-- 职业 -->
        a.A26 AS curr_addr,<!-- 现住址 -->
        a.A29N AS emp_name,<!-- 单位名称 -->
        a.A29 AS emp_addr,<!-- 单位地址 -->
        a.A30 AS emp_tel,<!-- 单位电话 -->
        a.A31C AS poscode,<!-- 邮编 -->
        a.A32 AS coner_name,<!-- 联系人姓名 -->
        a.A33C AS patn_rlts,<!-- 与患者关系 -->
        a.A34 AS coner_addr,<!-- 联系人地址 -->
        a.A35 AS coner_tel,<!-- 联系人电话 -->
        a.A57 AS nwb_adm_type,<!-- 新生儿入院类型 -->
        a.A18 AS nwb_bir_wt,<!-- 新生儿出生体重 -->
        a.A17 AS nwb_adm_wt,<!-- 新生儿入院体重 -->
        a.MUL_NWB_BIR_WT AS mul_nwb_bir_wt, <!-- 多新生儿出生体重 -->
        a.MUL_NWB_ADM_WT AS mul_nwb_adm_wt, <!-- 多新生儿入院体重 -->
        a.MTMM_IMP_DEPT_NAME AS opsp_diag_caty,<!-- 门诊慢特病诊断科别 -->
        a.MTMM_INP_DATE AS opsp_mdtrt_date,<!-- 门诊慢特病就诊日期 -->
        a.B13C as adm_dept_codg, <!-- 入院科别编码 -->
        a.B13N as adm_dept_name, <!-- 入院科别名称 -->
        a.B11C AS adm_way,<!-- 入院途径 -->
        a.B39 AS trt_type,<!-- 治疗类别 -->
        a.B12 AS adm_time,<!-- 入院时间 -->
        a.B21C AS refldept_dept,<!-- 转科科别 -->
        a.B15 AS dscg_time,<!-- 出院时间 -->
        a.B16C AS dscg_caty,<!-- 出院科别 -->
        a.C02N AS otp_wm_dise,<!-- 门(急)诊西医诊断 -->
        a.C01C AS wm_dise_code,<!-- 西医诊断疾病代码 -->
        a.C36N AS otp_tcm_dise,<!-- 门(急)诊中医诊断 -->
        a.C35C AS tcm_dise_code,<!-- 中医诊断代码 -->
        CONCAT(a.C42, '/', a.C43, '/', a.C44) AS vent_used_dura,<!-- 呼吸机使用时长 -->
        CONCAT(a.C28, '/', a.C29, '/', a.C30) AS pwcry_bfadm_coma_dura,<!-- 颅脑损伤患者入院前昏迷时长 -->
        CONCAT(a.C31, '/', a.C32, '/', a.C33) AS pwcry_afadm_coma_dura,<!-- 颅脑损伤患者入院后昏迷时长 -->
        a.B44 AS spga_nurscare_days,<!-- 特级护理天数 -->
        a.B45 AS lv1_nurscare_days,<!-- 一级护理天数 -->
        a.B46 AS scd_nurscare_days,<!-- 二级护理天数 -->
        a.B47 AS lv3_nurscare_days,<!-- 三级护理天数 -->
        a.B34C AS dscg_way,<!-- 离院方式 -->
        a.B49 AS acp_medins_name,<!-- 拟接收机构名称 -->
        a.B48 AS acp_medins_code,<!-- 拟接收机构代码 -->
        a.D38 AS bill_code,<!-- 票据代码 -->
        a.D39 AS bill_no,<!-- 票据号码 -->
        a.D35 AS biz_sn,<!-- 业务流水号 -->
        a.B36C AS days_rinp_flag_31,<!-- 出院31天内再住院计划标志 -->
        a.B37 AS days_rinp_pup_31,<!-- 出院31天内再住院目的 -->
        a.B51C AS chfpdr_code,<!-- 主诊医师代码 -->
        DATE_FORMAT(a.D36, '%Y-%m-%d %H:%i:%s') AS setl_begn_date,<!-- 结算开始日期 -->
        DATE_FORMAT(a.D37, '%Y-%m-%d %H:%i:%s') AS setl_end_date,<!-- 结算结束日期 -->
        a.D59 AS medins_fill_dept,<!-- 医疗机构填报部门 -->
        a.D60 AS medins_fill_psn,<!-- 医疗机构填报人 -->
        a.B26C AS resp_nurs_code, <!-- 责任护士代码 -->
        '0' AS stas_type, <!-- 状态分类 -->
        a.D58 AS hi_paymtd, <!-- 医保支付方式 -->
        a.C45 AS bld_cat, <!-- 输血品种 -->
        a.C46 AS bld_amt, <!-- 输血量 -->
        a.C47 AS bld_unt, <!-- 输血计量单位 -->
        a.B16C AS dscg_dept_codg,<!--出院科室编码-->
        a.B16N AS dscg_dept_name,<!--出院科室名称-->
        a.A16 AS age_days,<!--新生儿天龄-->
        a.B48 AS acp_optins_code,<!--拟接收机构代码-->
        a.insuplc_admdvs as insuplcAdmdvs, <!--参保地区域-->
        a.a56 as insuredPlace<!--参保地-->
        FROM som_hi_invy_bas_info a
        <where>
            <include refid="uploadIdFor"/>
            <if test="hospitalId != null and hospitalId != ''">
                AND a.HOSPITAL_ID = #{hospitalId}
            </if>
        </where>
    </select>

    <select id="queryPayInfoData" resultType="com.my.som.vo.listManagement.PayInfoVo">
        SELECT a.hi_setl_invy_id AS settleListId,
               a.FUND_PAY_TYPE   AS fund_pay_type,<!-- 基金支付类型 -->
        a.FUND_PAYAMT AS fund_payamt<!-- 基金支付金额 -->
        FROM som_fund_pay a
        WHERE a.hi_setl_invy_id IN (
        <include refid="uploadFieldsCondition"/>
        )
    </select>

    <select id="queryOpspDiseInfoData" resultType="com.my.som.vo.listManagement.OpspDiseInfoVo">
        SELECT a.hi_setl_invy_id AS settleListId,
               a.oprn_oprt_name  AS oprn_oprt_name,<!-- 手术操作名称 -->
        a.oprn_oprt_code AS oprn_oprt_code <!-- 手术操作代码 -->
        FROM som_otp_slow_special_trt_info a
        WHERE a.hi_setl_invy_id IN (
        <include refid="uploadFieldsCondition"/>
        )
    </select>

    <!-- 清单id循环 -->
    <sql id="uploadIdFor">
        <foreach collection="ids" item="id" separator="," open="AND ID IN  (" close=")">
            #{id,jdbcType=VARCHAR}
        </foreach>
    </sql>

    <!-- 清单Sybid循环 -->
    <sql id="uploadSybIdFor">
        <foreach collection="ids" item="id" separator="," open="AND a.ID IN  (" close=")">
            #{id,jdbcType=VARCHAR}
        </foreach>
    </sql>

    <!-- 查询清单id -->
    <sql id="uploadFieldsCondition">
        select id
        from som_hi_invy_bas_info
        <where>
            <include refid="uploadIdFor"/>
        </where>
    </sql>

    <select id="queryDiseInfoData" resultType="com.my.som.vo.listManagement.DiseInfoVo">
        SELECT a.SETTLE_LIST_ID AS settleListId,
               a.TYPE           AS diag_type,<!-- 诊断类别 -->
        a.dscg_diag_codg AS diag_code,<!-- 诊断代码 -->
        a.dscg_diag_name AS diag_name,<!-- 诊断名称 -->
        a.dscg_diag_adm_cond AS adm_cond_type,<!-- 入院病情类型 -->
        CASE WHEN a.seq = 0 THEN 1 ELSE 0 END AS maindiag_flag <!-- 主诊标识 -->
        FROM som_diag a
        WHERE a.SETTLE_LIST_ID IN (
        <include refid="uploadFieldsCondition"/>
        )
    </select>

    <select id="queryItemInfoData" resultType="com.my.som.vo.listManagement.ItemInfoVo">
        SELECT a.hi_setl_invy_id   AS settleListId,
               a.med_chrg_itemname AS med_chrgitm,<!-- 医疗收费项目 -->
        a.amt AS amt,<!-- 金额 -->
        a.claa AS claa_sumfee,<!-- 甲类费用合计 -->
        a.clab AS clab_amt,<!-- 乙类金额 -->
        a.ownpay AS fulamt_ownpay_amt,<!-- 全自费金额 -->
        a.oth AS oth_amt<!-- 其他金额 -->
        FROM som_hi_setl_invy_med_fee_info a
        WHERE a.hi_setl_invy_id IN (
        <include refid="uploadFieldsCondition"/>
        )
    </select>

    <select id="queryOprnInfoData" resultType="com.my.som.vo.listManagement.OprnInfoVo">
        SELECT a.SETTLE_LIST_ID                      AS settleListId,
               CASE WHEN a.seq = 0 THEN 1 ELSE 2 END AS oprn_oprt_type,<!-- 手术操作类别 -->
        a.C36N AS oprn_oprt_name,<!-- 手术操作名称 -->
        a.C35C AS oprn_oprt_code,<!-- 手术操作代码 -->
        a.C43 AS anst_way,<!-- 麻醉方式 -->
        a.C39C AS oper_dr_code,<!-- 术者医师代码 -->
        NULLIF(a.oprn_oprt_anst_dr_code, '-') AS anst_dr_code,<!-- 麻醉医师代码 -->
        a.OPRN_OPRT_BEGNTIME AS oprn_oprt_begntime,<!-- 手术操作日期 (开始时间)-->
        a.OPRN_OPRT_ENDTIME AS oprn_oprt_endtime,<!-- 手术操作日期 (结束时间)-->
        a.ANST_BEGNTIME AS anst_begntime, <!-- 麻醉开始时间 -->
        a.ANST_ENDTIME AS anst_endtime <!-- 麻醉结束时间 -->
        FROM som_oprn_oprt_info a
        WHERE a.SETTLE_LIST_ID IN (
        <include refid="uploadFieldsCondition"/>
        )
    </select>

    <select id="queryIcuInfoData" resultType="com.my.som.vo.listManagement.IcuInfoVo">
        SELECT a.hi_setl_invy_id    AS settleListId,
               a.scs_cutd_ward_type AS scs_cutd_ward_type,<!-- 重症监护病房类型 -->
        a.scs_cutd_inpool_time AS scs_cutd_inpool_time,<!-- 重症监护进入时间 -->
        a.scs_cutd_exit_time AS scs_cutd_exit_time,<!-- 重症监护退出时间 -->
        a.scs_cutd_sum_dura AS scs_cutd_sum_dura<!-- 重症监护合计时长 -->
        FROM som_setl_invy_scs_cutd_info a
        WHERE a.hi_setl_invy_id IN (
        <include refid="uploadFieldsCondition"/>
        )
    </select>

    <!-- 获取清单配置 -->
    <select id="querySettleConfig" resultType="com.my.som.vo.listManagement.SettleListConfigVo">
        SELECT send_msg_id       AS msgid,
               mdtrt_hi_admdvs   AS mdtrtarea_admvs,
               insuplc_hi_admdvs AS insuplc_admdvs,
               rec_sys_code      as recer_sys_code,
               DEV_NO            AS dev_no,
               DEV_SAFE_INFO     AS dev_safe_info,
               digsig_info       AS digsig_info,
               sign_type         AS signtype,
               intf_ver          AS infver,
               OPTER_TYPE        AS opter_type,
               opter_upld        AS opter,
               opter_name_last   AS opter_name,
               FIXMEDINS_CODE    AS fixmedins_code,
               FIXMEDINS_NAME    AS fixmedins_name,
               URL               AS url,
                substring(insuplc_hi_admdvs, 1, 4) as insuplcAdmdvs
        FROM som_setl_invy_upld_cfg
        <where>
            <if test="insuplcAdmdvs != '' and insuplcAdmdvs != null">
                and substring(insuplc_hi_admdvs, 1, 4) = #{insuplcAdmdvs,jdbcType=VARCHAR}
            </if>
        </where>
    </select>

    <!-- 获取清单配置 -->
    <select id="querySettleConfigOne" resultType="com.my.som.vo.listManagement.SettleListConfigVo">
        SELECT send_msg_id       AS msgid,
               mdtrt_hi_admdvs   AS mdtrtarea_admvs,
               insuplc_hi_admdvs AS insuplc_admdvs,
               rec_sys_code      as recer_sys_code,
               DEV_NO            AS dev_no,
               DEV_SAFE_INFO     AS dev_safe_info,
               digsig_info       AS digsig_info,
               sign_type         AS signtype,
               intf_ver          AS infver,
               OPTER_TYPE        AS opter_type,
               opter_upld        AS opter,
               opter_name_last   AS opter_name,
               FIXMEDINS_CODE    AS fixmedins_code,
               FIXMEDINS_NAME    AS fixmedins_name,
               URL               AS url
        FROM som_setl_invy_upld_cfg
    </select>

    <!-- 查询上传后数据 -->
    <select id="queryUploadedData" resultType="com.my.som.vo.listManagement.ListUploadVo">
        SELECT a.ID              AS id,
               a.K00             AS K00,
               a.A48             AS medcasCodg,
               a.A11             AS name,
               a.A12C            AS gend,
               a.A14             AS age,
               a.B15             AS outHosTime,
               a.B16C            AS deptCode,
        a.D35 AS listSerialNumFlag,
               CASE
                   WHEN a.A03 = 1 THEN a.C03C
                   WHEN a.A03 = 2 THEN a.C37C
                   ELSE NULL END AS mainDiseaseCode,
               CASE
                   WHEN a.A03 = 1 THEN a.C04N
                   WHEN a.A03 = 2 THEN a.C38N
                   ELSE NULL END AS mainDiseaseName,
               a.C14x01C         AS mainOperatorCode,
               a.C15x01N         AS mainOperatorName,
               a.D01             AS inHosTotalCost,
               c.`NAME`          AS deptName,
               d.reqt_para       AS reqtPara,
               d.err_msg         AS errMsg,
               d.upld_time       AS upldTime,
               d.STAS_TYPE       AS stasType,
               e.chk_stas        AS chkStas,
               m.`NAME`          AS drName
        <if test="provLevelInsuplcAdmdvs != null and provLevelInsuplcAdmdvs != ''" >
        ,
            CASE WHEN a.a56  IS NULL or a.a56 =''  THEN '未传值'
        WHEN SUBSTRING(a.a56, 1, 4) = SUBSTRING(#{provLevelInsuplcAdmdvs}, 1, 4)THEN '省本级'
        WHEN SUBSTRING(a.a56, 1, 4) = SUBSTRING(#{insuplcAdmdvs}, 1, 4) THEN '市医保'
        WHEN SUBSTRING(a.a56, 1, 2) = SUBSTRING(#{insuplcAdmdvs}, 1, 2) AND SUBSTRING(a.a56, 1, 4) != SUBSTRING(#{insuplcAdmdvs}, 1, 4) THEN CONCAT('省内异地-', t2.cityName)
        ELSE '省外异地'
        END AS isRemote
        </if>
        FROM som_hi_invy_bas_info a
            LEFT JOIN som_invy_upld_log d
                      ON a.k00 = d.k00
            LEFT JOIN som_dept c
                      ON a.B16C = c.`CODE`
                          AND a.HOSPITAL_ID = c.HOSPITAL_ID
             LEFT JOIN som_hi_dr_crsp m
        ON a.B25C = m.`oper_dr_code`
        AND a.HOSPITAL_ID = m.HOSPITAL_ID
        LEFT JOIN som_setl_invy_chk e
                      ON a.id = e.SETTLE_LIST_ID
            LEFT JOIN som_invy_upld_modi_rcd f
                      ON a.K00 = f.K00
        <if test="provLevelInsuplcAdmdvs != null and provLevelInsuplcAdmdvs != ''" >
        LEFT JOIN (
        SELECT SUBSTR(data_val, 1, 4) AS cbd, labl_name  as  cityName
        FROM `som_sys_code`
        WHERE code_type = 'XZQHDM'
        AND data_val LIKE '%00'
        AND SUBSTRING(data_val, 1, 2) = SUBSTRING(#{insuplcAdmdvs}, 1, 2)
        union
        SELECT
        CONCAT(SUBSTR(data_val, 1, 2), '99') AS cbd,
        labl_name AS cityName
        FROM
        `som_sys_code`
        WHERE
        code_type = 'XZQHDM'
        AND data_val LIKE '%0000'
        AND SUBSTRING(data_val, 1, 2) = SUBSTRING(#{insuplcAdmdvs}, 1, 2)

        ) t2 ON t2.cbd = SUBSTRING(a.a56, 1, 4)
        </if>
        <if test="enableSeAns != null and enableSeAns != '' and enableSeAns == 1">
            INNER JOIN som_setl_cas_crsp p
                       ON a.K00 = p.K00
        </if>
        WHERE a.ACTIVE_FLAG = 1
          AND d.upld_stas = #{upldStas,jdbcType=VARCHAR}
        <if test="begnDate != null and begnDate != ''">
          AND a.D37 BETWEEN #{begnDate,jdbcType=VARCHAR} AND CONCAT(#{expiDate,jdbcType=VARCHAR}, ' 23:59:59')
        </if>
        <if test="isRemote != null and isRemote != ''">
            <if test="isRemote == 0">
                AND SUBSTRING(a.a56,1,4)
                = SUBSTRING(#{insuplcAdmdvs},1,4)
                <!--                AND a.YDJY = '否'-->
            </if>
            <if test="isRemote == 1">
                <!--省内异地-->
                AND  SUBSTRING(a.a56,1,4)
                != SUBSTRING(#{insuplcAdmdvs},1,4)
                AND  SUBSTRING(a.a56,1,2)
                = SUBSTRING(#{insuplcAdmdvs},1,2)
                <!--                AND a.YDJY = '是'-->
            </if>
            <if test="isRemote == 2">
                <!--省外异地-->
                AND  SUBSTRING(a.a56,1,2)
                != SUBSTRING(#{insuplcAdmdvs},1,2)
                <!--                AND a.YDJY = '是'-->
            </if>
            <if test="isRemote == 3">
                <!--省本级-->
                AND  SUBSTRING(a.a56,1,4)
                = #{provLevelInsuplcAdmdvs}
                <!--                AND a.YDJY = '是'-->
            </if>
            <if test="isRemote == 4">
                <!--未传值-->
                AND (a.a56  IS NULL or a.a56 ='')
            </if>
        </if>
        <if test="errMsg != null and errMsg != ''">
            AND d.err_msg LIKE CONCAT('%', #{errMsg}, '%')
        </if>
        <include refid="queryCondition"/>
        <if test="hospitalId != null and hospitalId != ''">
            AND a.HOSPITAL_ID = #{hospitalId}
        </if>

        <if test="isRevise != null and isRevise != ''">
            <if test="isRevise == 0">
                AND f.K00 IS NULL
            </if>
            <if test="isRevise == 1">
                AND f.K00 IS NOT NULL
            </if>
        </if>

        <!-- 是否完成 -->
        <if test="isSuccess != null and isSuccess != ''">
            <if test="isSuccess == 0">
                AND d.STAS_TYPE = '0'
            </if>
            <if test="isSuccess == 1">
                AND d.STAS_TYPE = '1'
            </if>
        </if>

        <if test="k00s != null and k00s.size > 0">
            AND a.K00 in
            <foreach collection="k00s" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        ORDER BY d.upld_time DESC
    </select>

    <select id="queryDetailData" resultType="com.my.som.vo.listManagement.ListUploadVo">
        SELECT a.id              AS id,
               a.K00             AS K00,
               a.A48             AS medcasCodg,
               a.A11             AS name,
               a.A12C            AS gend,
               a.A14             AS age,
               a.B15             AS outHosTime,
               a.B16C            AS deptCode,
               CASE
                   WHEN a.A03 = 1 THEN a.C03C
                   WHEN a.A03 = 2 THEN a.C37C
                   ELSE NULL END AS mainDiseaseCode,
               CASE
                   WHEN a.A03 = 1 THEN a.C04N
                   WHEN a.A03 = 2 THEN a.C38N
                   ELSE NULL END AS mainDiseaseName,
               a.C14x01C         AS mainOperatorCode,
               a.C15x01N         AS mainOperatorName,
               a.D01             AS inHosTotalCost,
               b.dip_codg        AS dipCodg,
               b.DIP_NAME        AS dipName,
               c.`NAME`          AS deptName,
               d.chk_stas        AS chkStas,
        <if test="upld_stas = 1">
            e.err_msg as errMsg,
        </if>
        a.D35 AS listSerialNumFlag
        FROM som_hi_invy_bas_info a
                 LEFT JOIN som_dip_grp_info b
                           ON a.ID = b.SETTLE_LIST_ID
                 LEFT JOIN som_dept c
                           ON a.B16C = c.`CODE`
                 LEFT JOIN som_setl_invy_chk d
                           ON a.id = d.SETTLE_LIST_ID
                LEFT JOIN som_invy_upld_log e
                    on a.ID = e.SETTLE_LIST_ID
        WHERE a.ACTIVE_FLAG = 1
          AND a.ID IN
              (SELECT SETTLE_LIST_ID
               FROM som_invy_upld_log
               WHERE BATCH_NUM = #{batchNum}
                 AND upld_stas = #{upldStas})
    </select>

    <!-- 查询输血信息 -->
    <select id="queryTransfusionData" resultType="com.my.som.vo.listManagement.TransfusionVo">
        SELECT a.SETTLE_LIST_ID AS settleListId,
               a.BLD_CAT        AS bld_cat,<!-- 重症监护病房类型 -->
        a.BLD_AMT AS bld_amt,<!-- 重症监护进入时间 -->
        a.BLD_UNT AS bld_unt <!-- 重症监护退出时间 -->
        FROM som_setl_invy_bld_info a
        WHERE a.SETTLE_LIST_ID IN (
        <include refid="uploadFieldsCondition"/>
        )
    </select>
    <select id="queryUploadedLogByK00" resultType="com.my.som.vo.listManagement.SettleListUploadLogVo">
        select retn_invy_sn as resSettleListNo
        from som_invy_upld_log
        where K00 = #{k00,jdbcType=VARCHAR}
    </select>

    <select id="queryIDAndK00By" resultType="com.my.som.vo.listManagement.BatchListUploadVo">
        select K00 AS K00,
               ID  AS id
        FROM som_hi_invy_bas_info WHERE id in
        <if test="settleIds != null and settleIds.size > 0">
            <foreach collection="settleIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <!-- 查询需要修改的信息 -->
    <select id="queryModifySettleListData" resultType="java.util.Map">
        select a.PSN_NO            AS psn_no,
               a.SETTLEMENT_ID     AS setl_id,
               a.insuplc_admdvs as insuplcAdmdvs
        <if test="isSuccess != null and isSuccess != ''">
            <if test="isSuccess == 0">
                ,
                    '1' AS stas_type
            </if>
            <if test="isSuccess == 1">
                ,
                    '0' AS stas_type
            </if>
        </if>
        from som_hi_invy_bas_info a
                 INNER JOIN som_invy_upld_log b
                            ON a.k00 = b.k00
        WHERE
          1=1
        <if test="isSuccess != null and isSuccess != ''">
            <if test="isSuccess == 0">
                and b.STAS_TYPE = '0'
            </if>
            <if test="isSuccess == 1">
                and b.STAS_TYPE = '1'
            </if>
        </if>

        AND a.ID IN

        <foreach collection="ids" item="id" separator="," open="(" close=")">
            #{id,jdbcType=VARCHAR}
        </foreach>
    </select>
    <select id="querySetlSybInfoData" resultType="com.my.som.vo.listManagement.SetLSybInfoVo">
        SELECT a.ID                                  AS settleListId,
               a.k00                                 AS K00,
               a.CLINIC_ID                           AS mdtrt_id,
               a.PSN_NO AS psn_no,
                  DATE_FORMAT(STR_TO_DATE(a.d37,'%Y-%m-%d'),'%Y%m') AS setl_mon,
               a.SETTLEMENT_ID                       AS setl_id,
               a.A01                                 AS fixmedins_code,
               a.A02                                 AS fixmedins_name,
               a.A51                                 AS hi_no,
               a.A48                                 AS medcasno,
               a.A52                                 AS dcla_time,
               a.A11                                 AS psn_name,
               a.A12C                                AS gend,
               a.A13                                 AS brdy,
               a.A14                                 AS age,
               a.A15C                                AS ntly,
               a.A16                                 AS nwb_age,
               a.A19C                                AS naty,
               a.A53                                 AS patn_cert_type,
               a.A20                                 AS certno,
               a.A38C                                AS prfs,
               a.A26                                 AS curr_addr,
               a.A29N                                AS emp_name,
               a.A29                                 AS emp_addr,
               a.A30                                 AS emp_tel,
               a.A31C                                AS poscode,
               a.A32                                 AS coner_name,
               a.A33C                                AS patn_rlts,
               a.A34                                 AS coner_addr,
               a.A35                                 AS coner_tel,
               a.A54                                 AS hi_type,
               a.A56                                 AS insuplc,
               a.A55                                 AS sp_psn_type,
               a.A57                                 AS nwb_adm_type,
               a.A18                                 AS nwb_bir_wt,
               a.A17                                 AS nwb_adm_wt,
               a.MTMM_IMP_DEPT_NAME                  AS opsp_diag_caty,
               a.MTMM_INP_DATE                       AS opsp_mdtrt_date,
               a.B38                                 AS ipt_med_type,
               a.B11C                                AS adm_way,
               a.B39                                 AS trt_type,
               a.B12                                 AS adm_time,
               a.B13C                                AS adm_caty,
               a.B21C                                AS refldept_dept,
               a.B15                                 AS dscg_time,
               a.B16C                                AS dscg_caty,
               a.B20                                 AS act_ipt_days,
               a.C02N                                AS otp_wm_dise,
               a.C01C                                AS wm_dise_code,
               a.C36N                                AS otp_tcm_dise,
               a.C35C                                AS tcm_dise_code,
               CONCAT(a.C28, '/', a.C29, '/', a.C30) AS pwcry_bfadm_coma_dura,
               CONCAT(a.C31, '/', a.C32, '/', a.C33) AS pwcry_afadm_coma_dura,
               a.B44                                 AS spga_nurscare_days,
               a.B45                                 AS lv1_nurscare_days,
               a.B46                                 AS scd_nurscare_days,
               a.B47                                 AS lv3_nurscare_days,
               a.B34C                                AS dscg_way,
               a.B49                                 AS acp_medins_name,
               a.D38                                 AS bill_code,
               a.D39                                 AS bill_no,
               a.D35                                 AS biz_sn,
               a.B36C                                AS days_rinp_flag_31,
               a.B37                                 AS days_rinp_pup_31,
               a.B51C                                AS chfpdr_code,
               a.B52N                                AS chfpdr_name,
               a.B26C                                AS charge_nurse_code,
               a.B26N                                AS charge_nurse_name,
               a.D36                                 AS setl_begn_date,
               a.D37                                 AS setl_end_date,
               a.D54                                 AS psn_selfpay,
               a.D55                                 AS psn_ownpay,
               a.D56                                 AS acct_pay,
               a.D57                                 AS psn_cashpay,
               a.D58                                 AS hi_paymtd,
               a.MED_INS_ORGAN                       AS hsorg,
               a.MED_INS_ORGAN_OPERATOR              AS hsorg_opter,
               a.D59                                 AS medins_fill_dept,
               '0'                                   AS stas_type, <!-- 状态分类 -->
        a.D60                                                                 AS medins_fill_psn,
        (SELECT COUNT(*) FROM som_diag WHERE SETTLE_LIST_ID = a.ID)           AS diag_code_cnt,
        (SELECT COUNT(*) FROM som_oprn_oprt_info WHERE SETTLE_LIST_ID = a.ID) AS oprn_oprt_code_cnt
        FROM som_hi_invy_bas_info a
                 LEFT JOIN som_diag b on a.ID = b.SETTLE_LIST_ID
                 LEFT JOIN som_oprn_oprt_info c on a.ID = b.SETTLE_LIST_ID
        <where>
            <include refid="uploadSybIdFor"/>
            <if test="hospitalId != null and hospitalId != ''">
                AND a.HOSPITAL_ID = #{hospitalId}
            </if>
        </where>
        GROUP BY a.ID
    </select>
    <select id="querySybOpspDiseInfoData" resultType="com.my.som.vo.listManagement.OpspDiseInfoVo">
        SELECT a.hi_setl_invy_id AS settleListId,
               a.diag_code       AS diag_name,
               a.diag_name       AS diag_code,
               a.oprn_oprt_name  AS oprn_oprt_name,<!-- 手术操作名称 -->
        a.oprn_oprt_code AS oprn_oprt_code <!-- 手术操作代码 -->
        FROM som_otp_slow_special_trt_info a
        WHERE a.hi_setl_invy_id IN (
        <include refid="uploadFieldsCondition"/>
        )
    </select>
    <select id="queryOprnSybInfoData" resultType="com.my.som.vo.listManagement.OprnSybInfoVo">
        SELECT a.SETTLE_LIST_ID                      AS settleListId,
               CASE WHEN a.seq = 0 THEN 1 ELSE 2 END AS oprn_oprt_type,<!-- 手术操作类别 -->
        a.C36N AS oprn_oprt_name,<!-- 手术操作名称 -->
        a.C35C AS oprn_oprt_code,<!-- 手术操作代码 -->
        a.C43 AS anst_way,<!-- 麻醉方式 -->
        a.oprn_oprt_oper_name AS oper_dr_name,<!-- 术者医师姓名 -->
        a.C39C AS oper_dr_code,<!-- 术者医师代码 -->
        NULLIF(a.oprn_oprt_anst_dr_code, '-') AS anst_dr_code,<!-- 麻醉医师代码 -->
        a.OPRN_OPRT_BEGNTIME AS oprn_oprt_time_begin,<!-- 手术操作日期 (开始时间)-->
        a.OPRN_OPRT_ENDTIME AS oprn_oprt_time_end,<!-- 手术操作日期 (结束时间)-->
        a.ANST_BEGNTIME AS anst_time_begin, <!-- 麻醉开始时间 -->
        a.ANST_ENDTIME AS anst_time_end <!-- 麻醉结束时间 -->
        FROM som_oprn_oprt_info a
        WHERE a.SETTLE_LIST_ID IN (
        <include refid="uploadFieldsCondition"/>
        )
    </select>
    <select id="querySybTransfusionData" resultType="com.my.som.vo.listManagement.TransfusionSybVo">
        SELECT a.SETTLE_LIST_ID AS settleListId,
               a.BLD_CAT        AS bld_cat,<!-- 重症监护病房类型 -->
        a.BLD_AMT AS bld_amt,<!-- 重症监护进入时间 -->
        a.BLD_UNT AS bld_unit <!-- 重症监护退出时间 -->
        FROM som_setl_invy_bld_info a
        WHERE a.SETTLE_LIST_ID IN (
        <include refid="uploadFieldsCondition"/>
        )
    </select>
    <select id="querySettleSybConfig" resultType="com.my.som.vo.listManagement.SettleListSybConfigVo">
        SELECT APP_ID as appid, ORG_CODE as orgCode, url
        from som_city_hi_setl_invy_upld_cfg
    </select>
    <select id="queryItemSybInfoData" resultType="com.my.som.vo.listManagement.ItemInfoVo">
        SELECT a.hi_setl_invy_id AS settleListId,
               CASE
                   WHEN a.med_chrg_itemname = '1' THEN '01'
                   WHEN a.med_chrg_itemname = '2' THEN '02'
                   WHEN a.med_chrg_itemname = '3' THEN '03'
                   WHEN a.med_chrg_itemname = '4' THEN '04'
                   WHEN a.med_chrg_itemname = '5' THEN '05'
                   WHEN a.med_chrg_itemname = '6' THEN '06'
                   WHEN a.med_chrg_itemname = '7' THEN '07'
                   WHEN a.med_chrg_itemname = '8' THEN '08'
                   WHEN a.med_chrg_itemname = '9' THEN '09'
                   WHEN a.med_chrg_itemname = '10' THEN '10'
                   WHEN a.med_chrg_itemname = '11' THEN '11'
                   WHEN a.med_chrg_itemname = '12' THEN '12'
                   WHEN a.med_chrg_itemname = '13' THEN '13'
                   ELSE '14' END
                                 AS med_chrgitm,<!-- 医疗收费项目 -->
        a.amt AS amt,<!-- 金额 -->
        a.claa AS claa_sumfee,<!-- 甲类费用合计 -->
        a.clab AS clab_amt,<!-- 乙类金额 -->
        a.ownpay AS fulamt_ownpay_amt,<!-- 全自费金额 -->
        a.oth AS oth_amt<!-- 其他金额 -->
        FROM som_hi_setl_invy_med_fee_info a
        WHERE a.hi_setl_invy_id IN (
        <include refid="uploadFieldsCondition"/>
        )
    </select>
    <select id="querySybData" resultType="com.my.som.vo.listManagement.ListUploadVo">
        SELECT a.id      AS id,
               a.K00     AS k00,
               a.A48     AS medcasCodg,
               a.A11     AS name,
               a.A12C    AS gend,
               a.A14     AS age,
               a.B12     AS inHosTime,
               a.B15     AS outHosTime,
               a.B16C    AS deptCode,
               a.C03C    AS mainDiseaseCode,
               a.C04N    AS mainDiseaseName,
               a.C14x01C AS mainOperatorCode,
               a.C15x01N AS mainOperatorName,
               a.D01     AS inHosTotalCost,
        <choose>
            <when test="grperType == 1">
                b.dip_codg AS dipCodg,
                b.DIP_NAME AS dipName,
            </when>
            <when test="grperType == 3">
                b.drg_codg AS drgCodg,
                b.DRG_NAME AS drgName,
            </when>
        </choose>
        c.`NAME`    AS deptName,
        d.chk_stas  AS chkStas,
        a.look_over AS lookOver,
        m.`NAME`    AS drName

        FROM som_hi_invy_bas_info a
        <choose>
            <when test="grperType == 1">
                LEFT JOIN som_dip_grp_info b ON a.ID = b.SETTLE_LIST_ID
            </when>
            <when test="grperType == 3">
                LEFT JOIN som_drg_grp_info b ON a.ID = b.SETTLE_LIST_ID
            </when>
        </choose>

        LEFT JOIN som_dept c
                  ON a.B16C = c.`CODE`
                      AND a.HOSPITAL_ID = c.HOSPITAL_ID
        LEFT JOIN som_hi_dr_crsp m
            ON a.B25C = m.`oper_dr_code`
        LEFT JOIN som_setl_invy_chk d
                  ON a.id = d.SETTLE_LIST_ID
        LEFT JOIN som_invy_upld_modi_rcd e
                  ON a.K00 = e.K00
        <if test="enableSeAns != null and enableSeAns != '' and enableSeAns == 1">
            INNER JOIN som_setl_cas_crsp p
                       ON a.K00 = p.K00
        </if>
        WHERE a.ACTIVE_FLAG = 1
        <if test="isRemote != null and isRemote != ''">
            <if test="isRemote == 0">
                AND a.YDJY = '否'
            </if>
            <if test="isRemote == 1">
                AND a.YDJY = '是'
            </if>
        </if>
        <if test="isSpend != null and isSpend != ''">
            <if test="isSpend == 0">
                AND a.A46C != 7 AND a.A46C != 07
            </if>
            <if test="isSpend == 1">
                OR a.A46C = 7
                OR a.A46C = 07
            </if>
        </if>
        AND a.D37 BETWEEN #{begnDate} AND CONCAT(#{expiDate}, ' 23:59:59')
        <if test="uploadFlag == 0">
            AND a.UPLOAD_SYB_FLAG is null
        </if>
        <if test="uploadFlag != 0">
            AND a.UPLOAD_SYB_FLAG = #{uploadFlag}
        </if>
        AND a.k00 NOT IN (SELECT k00
                          from som_city_hi_invy_upld_log
                          WHERE K00 in (select k00
                                        from som_hi_invy_bas_info
                                        where D37 BETWEEN #{begnDate} AND CONCAT(#{expiDate}, ' 23:59:59')))
        <if test="allUpload">
            AND d.chk_stas = '1'
        </if>
        <if test="inStartTime != null and inStartTime != '' and inEndTime != null and inEndTime != ''">
            AND a.B12 BETWEEN #{inStartTime} AND CONCAT(#{inEndTime}, ' 23:59:59')
        </if>
        <if test="lookOver != null and lookOver != ''">
            AND a.look_over = #{lookOver,jdbcType=VARCHAR}
        </if>
        <if test="validateOver != null and validateOver != ''">
            AND d.chk_stas = #{validateOver,jdbcType=VARCHAR}
        </if>
        <include refid="queryCondition"/>
        <include refid="com.my.som.mapper.newBusiness.common.NewBusinessCommonAnalysisMapper.queryCommon">
        </include>
        ORDER BY a.B15
    </select>

    <select id="queryUploadedSybData" resultType="com.my.som.vo.listManagement.ListUploadVo">
        SELECT a.ID              AS id,
               a.K00             AS K00,
               a.A48             AS medcasCodg,
               a.A11             AS name,
               a.A12C            AS gend,
               a.A14             AS age,
               a.B15             AS outHosTime,
               a.B16C            AS deptCode,
               CASE
                   WHEN a.A03 = 1 THEN a.C03C
                   WHEN a.A03 = 2 THEN a.C37C
                   ELSE NULL END AS mainDiseaseCode,
               CASE
                   WHEN a.A03 = 1 THEN a.C04N
                   WHEN a.A03 = 2 THEN a.C38N
                   ELSE NULL END AS mainDiseaseName,
               a.C14x01C         AS mainOperatorCode,
               a.C15x01N         AS mainOperatorName,
               a.D01             AS inHosTotalCost,
               c.`NAME`          AS deptName,
               d.REQUEST_PARAM       AS reqtPara,
               d.ERROR_MSG         AS errMsg,
               d.UPLOAD_TIME       AS upldTime,
               d.STAS_TYPE       AS stasType,
               e.chk_stas        AS chkStas,
               m.`NAME`          AS drName
        FROM som_hi_invy_bas_info a
            LEFT JOIN som_city_hi_invy_upld_log d
                      ON a.k00 = d.k00
            LEFT JOIN som_dept c
                      ON a.B16C = c.`CODE`
                          AND a.HOSPITAL_ID = c.HOSPITAL_ID
        LEFT JOIN som_hi_dr_crsp m
            ON a.B25C = m.`oper_dr_code`
            LEFT JOIN som_setl_invy_chk e
                      ON a.id = e.SETTLE_LIST_ID
            LEFT JOIN som_invy_upld_modi_rcd f
                      ON a.K00 = f.K00
        <if test="enableSeAns != null and enableSeAns != '' and enableSeAns == 1">
            INNER JOIN som_setl_cas_crsp p
                       ON a.K00 = p.K00
        </if>
        WHERE a.ACTIVE_FLAG = 1
          AND d.UPLOAD_STATE = #{upldStas,jdbcType=VARCHAR}
          AND a.D37 BETWEEN #{begnDate,jdbcType=VARCHAR} AND CONCAT(#{expiDate,jdbcType=VARCHAR}, ' 23:59:59')
        <include refid="queryCondition"/>
        <if test="hospitalId != null and hospitalId != ''">
            AND a.HOSPITAL_ID = #{hospitalId}
        </if>
        <if test="isRevise != null and isRevise != ''">
            <if test="isRevise == 0">
                AND f.K00 IS NULL
            </if>
            <if test="isRevise == 1">
                AND f.K00 IS NOT NULL
            </if>
        </if>

        <!-- 是否完成 -->
        <if test="isSuccess != null and isSuccess != ''">
            <if test="isSuccess == 0">
                AND d.STAS_TYPE = '0'
            </if>
            <if test="isSuccess == 1">
                AND d.STAS_TYPE = '1'
            </if>
        </if>

        <if test="k00s != null and k00s.size > 0">
            AND a.K00 in
            <foreach collection="k00s" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        ORDER BY d.UPLOAD_TIME DESC
    </select>
    <select id="queryDetailSybData" resultType="com.my.som.vo.listManagement.ListUploadVo">
        SELECT a.id              AS id,
               a.K00             AS K00,
               a.A48             AS medcasCodg,
               a.A11             AS name,
               a.A12C            AS gend,
               a.A14             AS age,
               a.B15             AS outHosTime,
               a.B16C            AS deptCode,
               CASE
                   WHEN a.A03 = 1 THEN a.C03C
                   WHEN a.A03 = 2 THEN a.C37C
                   ELSE NULL END AS mainDiseaseCode,
               CASE
                   WHEN a.A03 = 1 THEN a.C04N
                   WHEN a.A03 = 2 THEN a.C38N
                   ELSE NULL END AS mainDiseaseName,
               a.C14x01C         AS mainOperatorCode,
               a.C15x01N         AS mainOperatorName,
               a.D01             AS inHosTotalCost,
               b.dip_codg        AS dipCodg,
               b.DIP_NAME        AS dipName,
               c.`NAME`          AS deptName,
               d.chk_stas        AS chkStas
        FROM som_hi_invy_bas_info a
                 LEFT JOIN som_dip_grp_info b
                           ON a.ID = b.SETTLE_LIST_ID
                 LEFT JOIN som_dept c
                           ON a.B16C = c.`CODE`
                 LEFT JOIN som_setl_invy_chk d
                           ON a.id = d.SETTLE_LIST_ID
        WHERE a.ACTIVE_FLAG = 1
          AND a.ID IN
              (SELECT SETTLE_LIST_ID
               FROM som_city_hi_invy_upld_log
               WHERE BATCH_NUM = #{batchNum}
                 AND UPLOAD_STATE = #{upldStas})
    </select>
    <select id="queryRecordSybData" resultType="com.my.som.vo.listManagement.ListUploadVo">
        SELECT a.USER_NAME AS userName,
               a.Time      AS time,
               a.succ_cnt  AS succCnt,
               a.fale_cnt  AS faleCnt,
               a.BATCH_NUM AS batchNum
        FROM som_city_hi_setl_invy_oprt_log a
        WHERE a.`TIME` BETWEEN #{begnDate,jdbcType=VARCHAR} and CONCAT(#{expiDate,jdbcType=VARCHAR}, ' 23:59:59')
        ORDER BY a.`TIME` DESC
    </select>
    <select id="queryHospId" resultType="com.my.som.dto.listManagement.ListUploadDto">
        SELECT HOSPITAL_ID as hospitalId, medins_name as medinsName
        FROM som_hosp_info
    </select>
    <select id="listWithdrawn" resultType="java.util.Map">
        select a.PSN_NO            AS psn_no,
               a.SETTLEMENT_ID     AS setl_id,
               '0'                 AS stas_type,
               a.insuplc_admdvs as insuplcAdmdvs
        from som_hi_invy_bas_info a
                 INNER JOIN som_invy_upld_log b
                            ON a.k00 = b.k00
        WHERE  a.ID IN
        <foreach collection="ids" item="id" separator="," open="(" close=")">
            #{id,jdbcType=VARCHAR}
        </foreach>
    </select>
    <select id="getSettleListByTime" resultType="java.util.Map">
        select a.psn_no, a.setl_id,ifnull(a.insuplc, b.insuplc_admdvs) as insuplc from som_setl_info a
        inner join som_hi_invy_bas_info b on a.unique_id =b.k00
        where setl_end_date between #{setlBegnDate} and #{setlEndDate} and b.UPLOAD_FLAG=1
    </select>

    <sql id="queryCondition">
        <if test="medcasCodg != null and medcasCodg != ''">
            AND a.A48 LIKE CONCAT('%', #{medcasCodg,jdbcType=VARCHAR}, '%')
        </if>
        <if test="deptCode != null and deptCode != ''">
            AND a.B16C LIKE CONCAT('%',
                #{deptCode,jdbcType=VARCHAR}, '%')
        </if>
    </sql>


    <insert id="copyToNewUpldLog" >
        INSERT INTO som_invy_upld_log_new (k00, settle_list_id, reqt_para,
        trns_no, err_msg, upld_stas,batch_num,upld_time,retn_invy_sn,stas_type
        )
        select k00, settle_list_id, reqt_para,
        trns_no, err_msg, upld_stas,batch_num,upld_time,retn_invy_sn,stas_type
        from som_invy_upld_log
        WHERE  k00 IN
        <foreach collection="k00s" item="k00" separator="," open="(" close=")">
            #{k00,jdbcType=VARCHAR}
        </foreach>
    </insert>
    <update id="updateUploadFlag" >
      update som_hi_invy_bas_info set UPLOAD_FLAG =0
        WHERE  k00 IN
        <foreach collection="k00s" item="k00" separator="," open="(" close=")">
            #{k00,jdbcType=VARCHAR}
        </foreach>
    </update>

    <delete id="deleteUpldLog" >
        delete  from som_invy_upld_log
        WHERE  k00 IN
        <foreach collection="k00s" item="k00" separator="," open="(" close=")">
            #{k00,jdbcType=VARCHAR}
        </foreach>
    </delete>

    <update id="updateUploadStes" >
        update som_invy_upld_log set upld_stas =1
        WHERE  k00 IN
        <foreach collection="k00s" item="k00" separator="," open="(" close=")">
            #{k00,jdbcType=VARCHAR}
        </foreach>
    </update>
    <update id="updateSettleListLookOverBySeTime" >
        update som_hi_invy_bas_info set  LOOK_OVER = 0
        WHERE
        LOOK_OVER = 1
        AND k00 IN
        <foreach collection="k00s" item="k00" separator="," open="(" close=")">
            #{k00,jdbcType=VARCHAR}
        </foreach>
    </update>

    <update id="deleteLabl" >
        DELETE FROM som_invy_label
        WHERE k00 IN
        <foreach collection="k00s" item="k00" separator="," open="(" close=")">
            #{k00,jdbcType=VARCHAR}
        </foreach>
<!--        -->
<!--        ( SELECT k00 FROM-->
<!--        som_hi_invy_bas_info-->
<!--        WHERE-->
<!--         LOOK_OVER = 1-->
<!--        AND  D37 BETWEEN #{begnDate} AND CONCAT(#{expiDate}, ' 23:59:59'))-->
    </update>

    <insert id="insertSettleListOpeLog">
        INSERT INTO som_setl_invy_flag_rcd
        (USER_NAME, nknm, crte_time, HOSPITAL_ID, K00, SETTLE_LIST_ID, oprt)
        SELECT
        #{userName} AS userName,
        #{nknm} AS nknm,
        NOW() AS crte_time,
        #{hospitalId} AS hospitalId,
        K00,
        id AS SETTLE_LIST_ID,
        '0' AS oprt
        FROM som_hi_invy_bas_info
        WHERE LOOK_OVER = 1
       AND  k00 IN
        <foreach collection="k00s" item="k00" separator="," open="(" close=")">
            #{k00,jdbcType=VARCHAR}
        </foreach>
    </insert>

    <update id="updateUpldFlag">
       update som_hi_invy_bas_info
        set UPLOAD_FLAG=0
        WHERE
        id in
        <foreach collection="ids" item="id" separator="," open="(" close=")">
            #{id,jdbcType=VARCHAR}
        </foreach>
    </update>



    <select id="selectStateUpdateMedsno" resultType="java.lang.String">
        select b.a48 from som_invy_upld_log a
        inner join  som_hi_invy_bas_info b  on a.settle_list_id = b.id
        WHERE
        a.stas_type = '1'
        and
        a.settle_list_id in
        <foreach collection="ids" item="id" separator="," open="(" close=")">
            #{id,jdbcType=VARCHAR}
        </foreach>
    </select>
    <select id="querySettleConfigByParam" resultType="com.my.som.vo.listManagement.SettleListConfigVo">
        SELECT send_msg_id       AS msgid,
               mdtrt_hi_admdvs   AS mdtrtarea_admvs,
               insuplc_hi_admdvs AS insuplc_admdvs,
               rec_sys_code      as recer_sys_code,
               DEV_NO            AS dev_no,
               DEV_SAFE_INFO     AS dev_safe_info,
               digsig_info       AS digsig_info,
               sign_type         AS signtype,
               intf_ver          AS infver,
               OPTER_TYPE        AS opter_type,
               opter_upld        AS opter,
               opter_name_last   AS opter_name,
               FIXMEDINS_CODE    AS fixmedins_code,
               FIXMEDINS_NAME    AS fixmedins_name,
               URL               AS url
        FROM som_setl_invy_upld_cfg
        <where>
            <if test="msgId != null">
                 send_msg_id = #{msgId}
            </if>
        </where>

    </select>
</mapper>
