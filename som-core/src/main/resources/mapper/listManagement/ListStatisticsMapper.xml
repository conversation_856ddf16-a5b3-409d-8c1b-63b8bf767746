<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.som.mapper.listManagement.ListStatisticsMapper">

    <select id="queryUnmarkData">
        SELECT *
        FROM som_hi_invy_bas_info
        <where>
            <if test="hospitalId != null and hospitalId != ''">
                AND HOSPITAL_ID = #{hospitalId,jdbcType=VARCHAR}
            </if>
            <if test="begnDate != null and begnDate != '' and expiDate != null and expiDate !=''">
                AND B15 BETWEEN #{begnDate,jdbcType=VARCHAR}
                            AND CONCAT(#{expiDate,jdbcType=VARCHAR},' 23:59:59')
            </if>
            <if test="lookOver != null and lookOver != ''">
                AND LOOK_OVER = #{lookOver,jdbcType=VARCHAR}
            </if>
        </where>
    </select>

</mapper>