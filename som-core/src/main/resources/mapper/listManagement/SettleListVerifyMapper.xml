<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.som.mapper.listManagement.SettleListVerifyMapper">

    <!-- k00 -->
    <sql id="foreachMarkSql">
        <foreach collection="k00s" item="k00" open="(" close=")" separator=",">
            #{k00}
        </foreach>
    </sql>

    <!-- 查询字段 -->
    <sql id="markQueryFields">
        a.id,a.k00,a.a48,a.a11,a.b12,a.b15,a.d37,
        a.LOOK_OVER AS mark,b.name AS b25n,c.name AS b16n
    </sql>

    <!-- 添加需要标记数据 -->
    <insert id="addMarkData">
        INSERT INTO som_invy_label
        (
            K00,SETTLE_LIST_ID,HOSPITAL_ID
        )
        select a.k00,
               a.id,
               a.HOSPITAL_ID
        FROM som_hi_invy_bas_info a
        WHERE a.K00 IN <include refid="foreachMarkSql" />
    </insert>

    <!-- 添加核对数据 -->
    <insert id="addSeParData">
        INSERT INTO som_setl_cas_crsp(
            K00,A48,A11,B12,B15,D37,ym,HOSPITAL_ID
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
             #{item.k00,jdbcType=VARCHAR},
             #{item.a48,jdbcType=VARCHAR},
             #{item.a11,jdbcType=VARCHAR},
             #{item.b12,jdbcType=VARCHAR},
             #{item.b15,jdbcType=VARCHAR},
             #{item.d37,jdbcType=VARCHAR},
             #{item.ym,jdbcType=VARCHAR},
             #{item.hospitalId,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>

    <!-- 更新标记信息 -->
    <update id="updateMarkInfo">
        UPDATE som_hi_invy_bas_info
        SET LOOK_OVER = #{finishSign,jdbcType=VARCHAR}
        WHERE K00 IN <include refid="foreachMarkSql" />
    </update>

    <!-- 删除需要标记数据 -->
    <delete id="delMarkData">
        DELETE FROM som_invy_label
        WHERE K00 IN <include refid="foreachMarkSql" />
    </delete>

    <!-- 删除核对数据 -->
    <delete id="delSeParData">
        DELETE FROM som_setl_cas_crsp
        WHERE ym = #{ym,jdbcType=VARCHAR}
    </delete>

    <!-- 查询清单需要验证信息 -->
    <select id="queryVerifyInfo" resultType="com.my.som.vo.listManagement.PatientInfoVo">
        select <include refid="markQueryFields" />
        from som_hi_invy_bas_info a
        left join som_medstff_info b
        on a.B25C = b.code
        left join som_dept c
        on a.b16c = c.code
        inner join som_setl_cas_crsp d
        on a.k00 = d.k00
        <where>
            <!-- 期号 -->
            <if test="begnDate != '' and begnDate != null">
                AND d.ym = #{ym,jdbcType=VARCHAR}
            </if>
        </where>
    </select>

    <!-- 查询需要标记的数据 -->
    <select id="queryMarkStateData" resultType="com.my.som.vo.listManagement.PatientInfoVo">
        SELECT k00
        FROM som_setl_cas_crsp d
        <where>
            <!-- 期号 -->
            <if test="begnDate != '' and begnDate != null">
                AND d.ym = #{ym,jdbcType=VARCHAR}
            </if>
        </where>
    </select>

    <!-- 查询不在系统数据 -->
    <select id="queryNotInSysData" resultType="com.my.som.vo.listManagement.PatientInfoVo">
        select d.k00, d.a11,d.b12,d.b15,d.d37,d.a48
        from som_setl_cas_crsp d
        left join som_hi_invy_bas_info a
        on a.k00 = d.k00
        left join som_medstff_info b
        on a.B25C = b.code
        left join som_dept c
        on a.b16c = c.code
        where a.id IS NULL
        <!-- 期号 -->
        <if test="begnDate != '' and begnDate != null">
            AND d.ym = #{ym,jdbcType=VARCHAR}
        </if>
    </select>

    <!-- 查询多余数据 -->
    <select id="queryUnnecessaryData" resultType="com.my.som.vo.listManagement.PatientInfoVo">
        select <include refid="markQueryFields" />
        from som_hi_invy_bas_info a
        left join som_medstff_info b
        on a.B25C = b.code
        left join som_dept c
        on a.b16c = c.code
        left join som_setl_cas_crsp d
        on a.k00 = d.k00
        where d.id is null
        <!-- 期号 -->
        <if test="begnDate != '' and begnDate != null">
            AND REPLACE(SUBSTR(a.d37,1,7),'-','') = #{ym,jdbcType=VARCHAR}
        </if>
    </select>

</mapper>
