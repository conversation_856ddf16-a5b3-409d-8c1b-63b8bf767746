<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.som.mapper.upload.WorkersUploadMapper">
    <!--批量新增-->
    <insert id="insert">
        INSERT INTO som_medstff_info (
         CODE, NAME, citi_idet_no, brdy, gend, profttl,TYPE,DEPT_CODE,ACTIVE_FLAG,HOSPITAL_ID
        ) VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.code,jdbcType=INTEGER},
            #{item.name,jdbcType=VARCHAR},#{item.citiIdetNo,jdbcType=VARCHAR},#{item.brdy,jdbcType=VARCHAR},
            #{item.gend,jdbcType=VARCHAR},#{item.profttl,jdbcType=VARCHAR},#{item.type,jdbcType=VARCHAR},
            #{item.deptCode,jdbcType=VARCHAR},#{item.activeFlag,jdbcType=VARCHAR},#{item.hospitalId,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>

    <insert id="saveLog">
        INSERT INTO som_file_upld_log(FILE_NAME,
                                        file_upld_time_time,
                                        file_upld_cnt,
                                         `TYPE`,
                                        err_msg,
                                        `STATE`
        )
        VALUES(#{name,jdbcType=VARCHAR},
               now(),
               #{count,jdbcType=INTEGER},
               #{type,jdbcType=VARCHAR},
               #{errMsg,jdbcType=VARCHAR},
               #{state,jdbcType=VARCHAR}
              )
    </insert>

    <!-- 查询医护人员信息文件上传日志 -->
    <select id="workersFileUploadLog" resultType="com.my.som.vo.upload.FileUploadVo">
        SELECT ID,
        FILE_NAME AS fileName,
        date_format(file_upld_time_time,'%Y-%m-%d %H:%i:%s') as fileUpldTimeTime,
        file_upld_cnt AS fileUpldCnt,
        err_msg AS errMsg,
        STATE AS state
        FROM som_file_upld_log
        <where>
            <if test="type != null and type != ''">
                AND type = #{type,jdbcType=VARCHAR}
            </if>
            <if test="begnDate !=null and begnDate !='' and expiDate !=null and expiDate !=''">
                AND file_upld_time_time BETWEEN #{begnDate,jdbcType=VARCHAR} and CONCAT(#{expiDate,jdbcType=VARCHAR},' 23:59:59')
            </if>
        </where>
    </select>
<!--    /**更新启用标志*/-->
    <update id="updateWorkers">
        UPDATE som_medstff_info SET ACTIVE_FLAG = #{activeFlag} WHERE 1=1
        AND ACTIVE_FLAG != '0'
        <if test="hospitalId != null and hospitalId != ''">
            AND HOSPITAL_ID = #{hospitalId}
        </if>
    </update>
</mapper>
