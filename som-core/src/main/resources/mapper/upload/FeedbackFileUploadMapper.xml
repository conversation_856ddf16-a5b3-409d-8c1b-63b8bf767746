<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.som.mapper.upload.FeedbackFileUploadMapper">

    <!-- 保存日志 -->
    <insert id="saveLog">
        INSERT INTO som_grp_fbck_upld_log(
            FILE_NAME, file_upld_cnt, memo,
            BATCH_NUM, file_upld_time_date_time, file_upld_stas,
            err_msg, HOSPITAL_ID
        )
        VALUES(
            #{name,jdbcType=VARCHAR}, #{count,jdbcType=INTEGER},  #{memo_info,jdbcType=VARCHAR},
            #{batchNum,jdbcType=VARCHAR}, now(), #{state,jdbcType=VARCHAR},
            #{errMsg,jdbcType=VARCHAR}, #{hospitalId,jdbcType=VARCHAR}
        )
    </insert>

    <!-- 新增组信息 -->
    <insert id="insertGroupInfo">
        INSERT INTO som_dip_grp_fbck(
            rid_idt_codg, medcas_codg, BATCH_NUM, is_in_group, dip_codg,
            DIP_NAME, used_asst_list, asst_list_age_grp, asst_list_dise_sev_deg,
            asst_list_tmor_sev_deg, adm_time, dscg_time, setl_time,HOSPITAL_ID,
            dise_type,com_dis_gp_type
        )
        <foreach collection="list" item="item" open="VALUES" separator=",">
            (
                #{item.uniqueId,jdbcType=VARCHAR},
                #{item.medcasCodg,jdbcType=VARCHAR},
                #{item.batchNum,jdbcType=VARCHAR},
                #{item.isInGroup,jdbcType=VARCHAR},
                #{item.dipCodg,jdbcType=VARCHAR},
                #{item.dipName,jdbcType=VARCHAR},
                #{item.usedAsstList,jdbcType=VARCHAR},
                #{item.asstListAgeGrp,jdbcType=VARCHAR},
                #{item.asstListDiseSevDeg,jdbcType=VARCHAR},
                #{item.isUsedAsstList,jdbcType=VARCHAR},
                #{item.inHosTime,jdbcType=VARCHAR},
                #{item.outHosTime,jdbcType=VARCHAR},
                #{item.setlTime,jdbcType=VARCHAR},
                #{item.hospitalId,jdbcType=VARCHAR},
                #{item.diseType,jdbcType=VARCHAR},
                #{item.comDisGpType,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>

    <!-- 新增费用信息 -->
    <insert id="insertFundsInfo">
        INSERT INTO som_fund_dfr_fbck(
            rid_idt_codg, BATCH_NUM, INSURED_TYPE, MED_TYPE,
            bas_sco, incr_sco, setl_sco,
            setl_pt_val, sumfee, dfr_fee, oth_fee,HOSPITAL_ID,over_exp_blnc_amt
        )
        <foreach collection="list" item="item" open="VALUES" separator=",">
            (
                #{item.uniqueId,jdbcType=VARCHAR},
                #{item.batchNum,jdbcType=VARCHAR},
                #{item.insuredType,jdbcType=VARCHAR},
                #{item.medType,jdbcType=VARCHAR},
                #{item.basSco,jdbcType=DOUBLE},
                #{item.incrSco,jdbcType=DOUBLE},
                #{item.setlSco,jdbcType=DOUBLE},
                #{item.setlPtVal,jdbcType=DOUBLE},
                #{item.sumfee,jdbcType=DOUBLE},
                #{item.dfrFee,jdbcType=DOUBLE},
                #{item.othFee,jdbcType=DOUBLE},
                #{item.hospitalId,jdbcType=VARCHAR},
                #{item.overExpBlncAmt,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>

    <!-- 新增反馈数据 -->
    <insert id="insertFeedback">
        INSERT INTO som_fbck_ana(
              dis_gp_codg,dis_gp_name,asst_list_tmor_sev_deg,asst_list_age_grp,
              asst_list_dise_sev_deg,MDTRT_ID,MEDCASNO,PSN_NAME,ADM_TIME,
              DSCG_TIME,SETL_TIME,DEPT_NAME,dr_name,setl_pt_num,
              pt_num_fee,sumfee,pool_fee,dis_gp_sumfee,
              dis_gp_pool_fee,setl_patn_type,INSURED_TYPE, is_in_group,
              BATCH_NUM
        )
        <foreach collection="list" item="item" open="VALUES" separator=",">
            (
                #{item.disGpCodg,jdbcType=VARCHAR},
                #{item.disGpName,jdbcType=VARCHAR},
                #{item.asstListTmorSevDeg,jdbcType=VARCHAR},
                #{item.asstListAgeGrp,jdbcType=VARCHAR},
                #{item.asstListDiseSevDeg,jdbcType=DOUBLE},
                #{item.mdtrtId,jdbcType=DOUBLE},
                #{item.medcasNo,jdbcType=DOUBLE},
                #{item.psmName,jdbcType=DOUBLE},
                #{item.admTime,jdbcType=DOUBLE},
                #{item.dscgTime,jdbcType=DOUBLE},
                #{item.setlTime,jdbcType=DOUBLE},
                #{item.deptName,jdbcType=VARCHAR},
                #{item.drName,jdbcType=VARCHAR},
                #{item.setlPtNum,jdbcType=DOUBLE},
                #{item.ptNumFee,jdbcType=DOUBLE},
                #{item.sumfee,jdbcType=DOUBLE},
                #{item.poolFee,jdbcType=DOUBLE},
                #{item.disGpSumfee,jdbcType=DOUBLE},
                #{item.disGpPoolFee,jdbcType=DOUBLE},
                #{item.setlPatnType,jdbcType=VARCHAR},
                #{item.insuredType,jdbcType=VARCHAR},
                #{item.isInGroup,jdbcType=VARCHAR},
                #{item.batchNum,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>

    <!-- 去重 -->
    <delete id="repeatGroupInfo">
        delete from som_dip_grp_fbck
        where id in (
            select id from (
               select min(id) as id from som_dip_grp_fbck group by rid_idt_codg, medcas_codg, adm_time, dscg_time having count(1) > 1
           ) a
        )
        and hospital_id = #{hospitalId,jdbcType=VARCHAR}
    </delete>

    <!-- 去重 -->
    <delete id="repeatFundsInfo">
        delete from som_fund_dfr_fbck
        where id in (
            select id from (
               select min(id) as id from som_fund_dfr_fbck group by rid_idt_codg having count(1) > 1
            ) a
        )
        and hospital_id = #{hospitalId,jdbcType=VARCHAR}
    </delete>

    <!-- 去重反馈数据 -->
    <delete id="repeatFeedback">
        delete from som_fbck_ana
        where id in (
            select id from (
               select min(id) as id from som_fbck_ana group by mdtrt_id having count(1) > 1
           ) a
        )
    </delete>

    <!-- 查询上传日志 -->
    <select id="queryFileUploadLog" resultType="com.my.som.vo.upload.FileUploadVo">
        SELECT a.id,
               a.FILE_NAME AS fileName,
               a.file_upld_cnt AS fileUpldCnt,
               a.memo AS memo_info,
               a.BATCH_NUM AS batchNum,
               a.file_upld_time_date_time AS fileUpldTimeTime,
               a.file_upld_stas AS state,
               a.err_msg AS errMsg,
               case when b.batch_num != '' then 1 else 0 end as fundsFlag
        FROM som_grp_fbck_upld_log a
        left join (
            select batch_num from som_fund_dfr_fbck group by batch_num
        ) b
        on a.BATCH_NUM = b.batch_num
        <where>
            <if test="begnDate != null and expiDate != null and begnDate != '' and expiDate != ''">
               AND a.file_upld_time_date_time BETWEEN #{begnDate,jdbcType=VARCHAR} AND CONCAT(#{expiDate,jdbcType=VARCHAR}, ' 23:59:59')
            </if>
            <if test="hospitalId != null and hospitalId != ''">
                AND a.HOSPITAL_ID = #{hospitalId,jdbcType=VARCHAR}
            </if>
        </where>
        ORDER BY a.file_upld_time_date_time DESC
    </select>

    <!-- 查询详情 -->
    <select id="queryFeedbackDetail" resultType="com.my.som.entity.upload.FeedbackEntity">
        SELECT a.rid_idt_codg AS uniqueId,
               a.medcas_codg AS medcasCodg,
               a.BATCH_NUM AS batchNum,
               a.is_in_group AS isInGroup,
               a.dip_codg AS dipCodg,
               a.DIP_NAME AS dipName,
               a.used_asst_list AS usedAsstList,
               a.asst_list_age_grp AS asstListAgeGrp,
               a.asst_list_dise_sev_deg AS asstListDiseSevDeg,
               a.asst_list_tmor_sev_deg AS isUsedAsstList,
               a.adm_time AS inHosTime,
               a.dscg_time AS outHosTime,
               a.setl_time AS setlTime,
               b.INSURED_TYPE AS insuredType,
               b.MED_TYPE AS medType,
               b.bas_sco AS basSco,
               b.incr_sco AS incrSco,
               b.setl_sco AS setlSco,
               b.setl_pt_val AS setlPtVal,
               b.sumfee AS sumfee,
               b.dfr_fee AS dfrFee
        FROM som_dip_grp_fbck a
        LEFT JOIN som_fund_dfr_fbck b
        ON a.rid_idt_codg = b.rid_idt_codg
        AND a.BATCH_NUM = b.BATCH_NUM
        <where>
            <if test="begnDate != null and begnDate != '' and expiDate != null and expiDate != ''">
               AND a.dscg_time BETWEEN  #{begnDate,jdbcType=VARCHAR}
               AND CONCAT(#{expiDate,jdbcType=VARCHAR}, '23:59:59')
            </if>
            <if test="batchNum != null and batchNum != ''">
                AND a.BATCH_NUM = #{batchNum,jdbcType=VARCHAR}
            </if>
            <if test="medcasCodg != null and medcasCodg != ''">
                AND a.medcas_codg LIKE CONCAT('%',#{medcasCodg,jdbcType=VARCHAR},'%')
            </if>
            <if test="hospitalId != null and hospitalId != ''">
                AND a.HOSPITAL_ID = #{hospitalId,jdbcType=VARCHAR}
            </if>
        </where>
    </select>

    <!-- 获取德阳组 -->
    <select id="queryDyGroup" resultType="java.util.Map">
        SELECT DY_GROUP AS dyGroup,
               dip_codg AS dipCodg,
               DIP_NAME AS dipName
        FROM dy_group
    </select>

    <!-- 查询分组详情2 -->
    <select id="queryFeedbackDetail2" resultType="com.my.som.entity.upload.FeedbackEntity2">
        select m.*,
               round(m.setlPtNum * m.ptNumFee,2) as disGpSumfee,
               round(m.setlPtNum * m.ptNumFee - m.sumfee) as balance
        from(
            SELECT  a.dis_gp_codg AS disGpCodg,
                    a.dis_gp_name AS disGpName,
                    a.asst_list_tmor_sev_deg AS asstListTmorSevDeg,
                    a.asst_list_age_grp AS asstListAgeGrp,
                    a.asst_list_dise_sev_deg AS asstListDiseSevDeg,
                    a.MDTRT_ID AS mdtrtId,
                    a.MEDCASNO AS medcasNo,
                    a.PSN_NAME AS psmName,
                    a.ADM_TIME AS admTime,
                    a.DSCG_TIME AS dscgTime,
                    a.SETL_TIME AS setlTime,
                    a.DEPT_NAME AS deptName,
                    a.dr_name AS drName,
                    a.setl_pt_num AS setlPtNum,
                    case when a.INSURED_TYPE = '城镇职工' or a.INSURED_TYPE = '1'
                         then ${czPrice}
                         when a.INSURED_TYPE = '城乡居民' or a.INSURED_TYPE = '2'
                         then ${cxPrice}
                    else ${price} end
                    AS ptNumFee,
                    a.sumfee AS sumfee,
                    a.pool_fee AS poolFee,
                    a.dis_gp_sumfee,
                    a.dis_gp_pool_fee AS disGpPoolFee,
                    a.setl_patn_type AS setlPatnType,
                    case when a.INSURED_TYPE = '城镇职工' or a.INSURED_TYPE = '1'
                         then '城镇职工'
                         when a.INSURED_TYPE = '城乡居民' or a.INSURED_TYPE = '2'
                         then '城乡居民'
                    else '其他' end
                    AS insuredType,
                    a.is_in_group AS isInGroup,
                    a.BATCH_NUM AS batchNum
                    <if test="grperType == 1 or grperType == 3">
                        ,b.*,
<!--                        b.price as ptNumFee,-->
                        case when a.dis_gp_codg = b.sysGroupCode and
                        a.asst_list_age_grp = b.sysAuxiliaryAge and
                        a.asst_list_dise_sev_deg = b.sysAuxiliaryIllness and
                        a.asst_list_tmor_sev_deg = b.sysAuxiliaryTumour
                        then 1 else 0 end as matched
                    </if>

        FROM som_fbck_ana a
        <if test="grperType == 1 or grperType == 3">
            left join (
                <choose>
                    <when test="grperType == 1">
                        select b.dip_codg as sysGroupCode,
                               b.DIP_NAME as sysGroupName,
                               b.asst_list_age_grp as sysAuxiliaryAge,
                               b.asst_list_dise as sysAuxiliaryIllness,
                               b.asst_list_tmor_sev_deg as sysAuxiliaryTumour,
                               a.id,
                               a.a48,
                               a.d01 as sysTotalCost,
                               a.CLINIC_ID as mdtrt_id,
                               c.price,
                               round(c.totl_sco, 2) as sysSelPoint
                        from som_hi_invy_bas_info a
                        left join som_dip_grp_rcd b
                        on a.id = b.SETTLE_LIST_ID
                        left join som_dip_sco c
                        on a.id = c.SETTLE_LIST_ID
                        where a.CLINIC_ID IN (
                            select MDTRT_ID
                            from som_fbck_ana a
                            <where>
                                <include refid="backCondition" />
                            </where>
                        )
                    </when>
                    <when test="grperType == 3">
                        select b.drg_codg as sysGroupCode,
                               b.DRG_NAME as sysGroupName,
                               a.id,
                               a.a48,
                               a.d01 as sysTotalCost,
                               a.CLINIC_ID as mdtrt_id,
                                c.price,
                               c.totl_sco as sysSelPoint
                        from som_hi_invy_bas_info a
                        inner join som_drg_grp_rcd b
                        on a.id = b.SETTLE_LIST_ID
                        inner join som_drg_sco c
                        on a.id = c.SETTLE_LIST_ID
    <!--                    <where>-->
    <!--                        <if test="seStartTime != null and seStartTime != ''">-->
    <!--                            AND a.d37 BETWEEN #{seStartTime,jdbcType=VARCHAR}-->
    <!--                            AND CONCAT(#{seEndTime,jdbcType=VARCHAR}, ' 59:59:59')-->
    <!--                        </if>-->
    <!--                        <if test="begnDate != null and begnDate != ''">-->
    <!--                            AND a.b15 BETWEEN #{begnDate,jdbcType=VARCHAR}-->
    <!--                            AND CONCAT(#{expiDate,jdbcType=VARCHAR}, ' 59:59:59')-->
    <!--                        </if>-->
    <!--                    </where>-->
                    </when>
                </choose>
            ) b
            on a.mdtrt_id = b.mdtrt_id
        </if>
            <where>
                <include refid="backCondition" />
            </where>
        ) m
    </select>

    <sql id="backCondition">
        <if test="seStartTime != null and seStartTime != ''">
            AND a.SETL_TIME BETWEEN #{seStartTime,jdbcType=VARCHAR}
            AND CONCAT(#{seEndTime,jdbcType=VARCHAR}, ' 59:59:59')
            OR SUBSTR(SETL_TIME,1,7) between (#{seStartTime,jdbcType=VARCHAR}) and concat(#{seEndTime,jdbcType=VARCHAR},'23:59:59')
        </if>
        <if test="begnDate != null and begnDate != ''">
            AND a.DSCG_TIME BETWEEN #{begnDate,jdbcType=VARCHAR}
            AND CONCAT(#{expiDate,jdbcType=VARCHAR}, ' 59:59:59')
        </if>
        <if test="medcasCodg != null and medcasCodg != ''">
            AND a.MEDCASNO LIKE CONCAT('%', #{medcasCodg,jdbcType=VARCHAR}, '%')
        </if>
        <if test="batchNum != null and batchNum != ''">
            AND a.BATCH_NUM LIKE CONCAT('%', #{batchNum,jdbcType=VARCHAR}, '%')
        </if>
    </sql>

    <!-- 查询汇总 -->
    <select id="queryFeedbackDetail2Summary" resultType="com.my.som.vo.upload.FeedbackSummaryVo">
        select d.*,
               round(d.matchedNum / d.totalNum * 100, 2) as matchedRate,
			   round(d.groupMatchedNum / d.matchedNum * 100, 2) as groupMatchedRate
        from(
                select max(totalNum) as totalNum,
                       sum(case when matched = '1' then 1 else 0 end) as matchedNum,
                       sum(case when matched = '0' then 1 else 0 end) as nonMatchedNum,
				       sum(case when groupMatched = 1 then 1 else 0 end) as groupMatchedNum
                from (
                         SELECT case when b.mdtrt_id is not null then 1 else 0 end as matched,
                                case when a.dis_gp_codg = b.sysGroupCode and
									   a.asst_list_age_grp = b.sysAuxiliaryAge and
										 a.asst_list_dise_sev_deg = b.sysAuxiliaryIllness and
										 a.asst_list_tmor_sev_deg = b.sysAuxiliaryTumour
								then 1 else 0 end as groupMatched,
                                count(1)over() as totalNum
                         FROM som_fbck_ana a
                         LEFT JOIN (
                                 SELECT
                                     b.dip_codg AS sysGroupCode,
                                     b.DIP_NAME AS sysGroupName,
                                     b.asst_list_age_grp AS sysAuxiliaryAge,
                                     b.asst_list_dise AS sysAuxiliaryIllness,
                                     b.asst_list_tmor_sev_deg AS sysAuxiliaryTumour,
                                     a.id,
                                     a.CLINIC_ID AS mdtrt_id
                                 FROM som_hi_invy_bas_info a
                                 left JOIN som_dip_grp_rcd b ON a.id = b.SETTLE_LIST_ID
                         ) b
                         on a.mdtrt_id = b.mdtrt_id
                         <where>
                            <include refid="backCondition" />
                        </where>
                     ) c
            ) d
    </select>

</mapper>
