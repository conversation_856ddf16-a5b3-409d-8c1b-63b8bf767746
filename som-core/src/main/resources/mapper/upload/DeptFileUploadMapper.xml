<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.som.mapper.upload.DeptFileUploadMapper">

    <!-- 插入科室数据 -->
    <insert id="insert">
        INSERT INTO som_dept(CODE,
                             NAME,
                             dept_lv,
                             prnt_dept_codg,
                             TYPE,
                             bed_cnt,
                             std_dept_codg,
                             dept_area,
                             is_oprn_dept,
                             HOSPITAL_ID,
                             ACTIVE_FLAG
        )
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
               (
               #{item.code,jdbcType=VARCHAR},
               #{item.name,jdbcType=VARCHAR},
               #{item.dept_lv,jdbcType=VARCHAR},
               #{item.prntDeptCodg,jdbcType=VARCHAR},
               #{item.type,jdbcType=VARCHAR},
               #{item.bedCnt,jdbcType=VARCHAR},
               #{item.std_dept_codg,jdbcType=VARCHAR},
               #{item.dept_area,jdbcType=VARCHAR},
               #{item.isOper,jdbcType=VARCHAR},
               #{item.hospitalId,jdbcType=VARCHAR},
               #{item.activeFlag,jdbcType=VARCHAR}
        )
        </foreach>
    </insert>
    
    <!-- 插入文件上传日志 -->
    <insert id="saveLog">
        INSERT INTO som_file_upld_log(FILE_NAME,
                                        file_upld_time_time,
                                        file_upld_cnt,
                                        `TYPE`,
                                        err_msg,
                                        `STATE`
        )
        VALUES(#{name,jdbcType=VARCHAR},
               now(),
               #{count,jdbcType=INTEGER},
               #{type,jdbcType=VARCHAR},
               #{errMsg,jdbcType=VARCHAR},
               #{state,jdbcType=VARCHAR}
              )
    </insert>
    
    <!-- 查询科室文件上传日志 -->
    <select id="queryDeptFileUploadLog" resultType="com.my.som.vo.upload.FileUploadVo">
        SELECT ID,
               FILE_NAME AS fileName,
               date_format(file_upld_time_time,'%Y-%m-%d %H:%i:%s') as fileUpldTimeTime,
               file_upld_cnt AS fileUpldCnt,
               err_msg AS errMsg,
               STATE AS state
        FROM som_file_upld_log
        <where>
            <if test="type != null and type != ''">
                AND type = #{type,jdbcType=VARCHAR}
            </if>
            <if test="begnDate !=null and begnDate !='' and expiDate !=null and expiDate !=''">
                AND file_upld_time_time BETWEEN #{begnDate,jdbcType=VARCHAR} and CONCAT(#{expiDate,jdbcType=VARCHAR},' 23:59:59')
            </if>
        </where>
    </select>

</mapper>