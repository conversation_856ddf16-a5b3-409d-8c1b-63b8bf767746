<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.som.mapper.basicHea.BasicHealthMapper">
    <insert id="insertBaseInfo" useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        INSERT INTO som_setl_invy_bas_info (
            unique_id,
            mdtrt_sn,
            mdtrt_id,
            psn_no,
            patn_ipt_cnt,
            ipt_no,
            medcasno,
            psn_name,
            gend,
            brdy,
            age,
            ntly,
            nwb_age,
            nwb_bir_wt,
            nwb_adm_wt,
            birplc,
            napl,
            naty,
            certno,
            prfs,
            mrg_stas,
            curr_addr_poscode,
            curr_addr,
            psn_tel,
            resd_addr_prov,
            resd_addr_city,
            resd_addr_coty,
            resd_addr_subd,
            resd_addr_vil,
            resd_addr_housnum,
            resd_addr_poscode,
            resd_addr,
            empr_tel,
            empr_poscode,
            empr_addr,
            coner_tel,
            coner_name,
            coner_addr,
            coner_rlts_code,
            adm_way_name,
            adm_way_code,
            trt_type_name,
            trt_type,
            adm_caty,
            adm_ward,
            adm_date,
            dscg_date,
            dscg_caty,
            refldept_dept,
            dscg_ward,
            ipt_days,
            drug_dicm_flag,
            dicm_drug_name,
            die_autp_flag,
            abo_code,
            abo_name,
            rh_code,
            rh_name,
            die_flag,
            deptdrt_name,
            deptdrt_code,
            chfdr_name,
            chfdr_code,
            atddr_name,
            atddr_code,
            chfpdr_name,
            chfpdr_code,
            ipt_dr_name,
            ipt_dr_code,
            resp_nurs_name,
            resp_nurs_code,
            train_dr_name,
            train_dr_code,
            intn_dr_name,
            intn_dr_code,
            codr_name,
            codr_code,
            qltctrl_dr_name,
            qltctrl_dr_code,
            qltctrl_nurs_name,
            qltctrl_nurs_code,
            medcas_qlt_name,
            medcas_qlt_code,
            qltctrl_date,
            dscg_way_name,
            dscg_way,
            acp_medins_code,
            acp_medins_name,
            dscg_31days_rinp_flag,
            dscg_31days_rinp_pup,
            damg_intx_ext_rea,
            damg_intx_ext_rea_disecode,
            brn_damg_bfadm_coma_dura,
            brn_damg_afadm_coma_dura,
            vent_used_dura,
            cnfm_date,
            patn_dise_diag_crsp,
            patn_dise_diag_crsp_code,
            ipt_patn_diag_inscp,
            ipt_patn_diag_inscp_code,
            dscg_trt_rslt,
            dscg_trt_rslt_code,
            medins_orgcode,
            aise,
            pote_intn_dr_name,
            hbsag,
            hcvab,
            hivab,
            resc_cnt,
            resc_succ_cnt,
            hosp_dise_fsttime,
            hif_pay_way_name,
            hif_pay_way_code,
            med_fee_paymtd_name,
            medfee_paymtd_code,
            selfpay_amt,
            medfee_sumamt,
            ordn_med_servfee,
            ordn_trt_oprt_fee,
            nurs_fee,
            com_med_serv_oth_fee,
            palg_diag_fee,
            lab_diag_fee,
            rdhy_diag_fee,
            clnc_dise_fee,
            nsrgtrt_item_fee,
            clnc_phys_trt_fee,
            rgtrt_trt_fee,
            anst_fee,
            oprn_fee,
            rhab_fee,
            tcm_trt_fee,
            wmfee,
            abtl_medn_fee,
            tcmpat_fee,
            tcmherb_fee,
            blo_fee,
            albu_fee,
            glon_fee,
            clotfac_fee,
            cyki_fee,
            exam_dspo_matl_fee,
            trt_dspo_matl_fee,
            oprn_dspo_matl_fee,
            oth_fee,
            vali_flag,
            fixmedins_code,
            offsite_med_treat,
            pre_exam,
            patn_rlts,
            nwb_adm_type,
            mul_nwb_bir_wt,
            mul_nwb_adm_wt,
            opsp_diag_caty,
            opsp_mdtrt_date,
            spga_nurscare_days,
            lv1_nurscare_days,
            scd_nurscare_days,
            lv3_nurscare_days,
            otp_wm_diag,
            otp_wm_diag_dise_code,
            otp_tcm_diag,
            otp_tcm_diag_dise_code,
            adm_diag,
            bld_cat,
            bld_amt,
            bld_unt,
            extract_flag
        ) VALUES
        <foreach item="item" index="index" collection="list" separator=",">
              (
            #{item.uniqueId},
            #{item.mdtrtSn},
            #{item.mdtrtId},
            #{item.psnNo},
            #{item.patnIptCnt},
            #{item.iptNo},
            #{item.medcasno},
            #{item.psnName},
            #{item.gend},
            #{item.brdy},
            #{item.age},
            #{item.ntly},
            #{item.nwbAge},
            #{item.nwbBirWt},
            #{item.nwbAdmWt},
            #{item.birplc},
            #{item.napl},
            #{item.naty},
            #{item.certno},
            #{item.prfs},
            #{item.mrgStas},
            #{item.currAddrPoscode},
            #{item.currAddr},
            #{item.psnTel},
            #{item.resdAddrProv},
            #{item.resdAddrCity},
            #{item.resdAddrCoty},
            #{item.resdAddrSubd},
            #{item.resdAddrVil},
            #{item.resdAddrHousnum},
            #{item.resdAddrPoscode},
            #{item.resdAddr},
            #{item.emprTel},
            #{item.emprPoscode},
            #{item.emprAddr},
            #{item.conerTel},
            #{item.conerName},
            #{item.conerAddr},
            #{item.conerRltsCode},
            #{item.admWayName},
            #{item.admWayCode},
            #{item.trtTypeName},
            #{item.trtType},
            #{item.admCaty},
            #{item.admWard},
            #{item.admDate},
            #{item.dscgDate},
            #{item.dscgCaty},
            #{item.refldeptDept},
            #{item.dscgWard},
            #{item.iptDays},
            #{item.drugDicmFlag},
            #{item.dicmDrugName},
            #{item.dieAutpFlag},
            #{item.aboCode},
            #{item.aboName},
            #{item.rhCode},
            #{item.rhName},
            #{item.dieFlag},
            #{item.deptdrtName},
            #{item.deptdrtCode},
            #{item.chfdrName},
            #{item.chfdrCode},
            #{item.atddrName},
            #{item.atddrCode},
            #{item.chfpdrName},
            #{item.chfpdrCode},
            #{item.iptDrName},
            #{item.iptDrCode},
            #{item.respNursName},
            #{item.respNursCode},
            #{item.trainDrName},
            #{item.trainDrCode},
            #{item.intnDrName},
            #{item.intnDrCode},
            #{item.codrName},
            #{item.codrCode},
            #{item.qltctrlDrName},
            #{item.qltctrlDrCode},
            #{item.qltctrlNursName},
            #{item.qltctrlNursCode},
            #{item.medcasQltName},
            #{item.medcasQltCode},
            #{item.qltctrlDate},
            #{item.dscgWayName},
            #{item.dscgWay},
            #{item.acpMedinsCode},
            #{item.acpMedinsName},
            #{item.dscg31daysRinpFlag},
            #{item.dscg31daysRinpPup},
            #{item.damgIntxExtRea},
            #{item.damgIntxExtReaDisecode},
            #{item.brnDamgBfadmComaDura},
            #{item.brnDamgAfadmComaDura},
            #{item.ventUsedDura},
            #{item.cnfmDate},
            #{item.patnDiseDiagCrsp},
            #{item.patnDiseDiagCrspCode},
            #{item.iptPatnDiagInscp},
            #{item.iptPatnDiagInscpCode},
            #{item.dscgTrtRslt},
            #{item.dscgTrtRsltCode},
            #{item.medinsOrgcode},
            #{item.aise},
            #{item.poteIntnDrName},
            #{item.hbsag},
            #{item.hcvab},
            #{item.hivab},
            #{item.rescCnt},
            #{item.rescSuccCnt},
            #{item.hospDiseFsttime},
            #{item.hifPayWayName},
            #{item.hifPayWayCode},
            #{item.medFeePaymtdName},
            #{item.medfeePaymtdCode},
            #{item.selfpayAmt},
            #{item.medfeeSumamt},
            #{item.ordnMedServfee},
            #{item.ordnTrtOprtFee},
            #{item.nursFee},
            #{item.comMedServOthFee},
            #{item.palgDiagFee},
            #{item.labDiagFee},
            #{item.rdhyDiagFee},
            #{item.clncDiseFee},
            #{item.nsrgtrtItemFee},
            #{item.clncPhysTrtFee},
            #{item.rgtrtTrtFee},
            #{item.anstFee},
            #{item.oprnFee},
            #{item.rhabFee},
            #{item.tcmTrtFee},
            #{item.wmfee},
            #{item.abtlMednFee},
            #{item.tcmpatFee},
            #{item.tcmherbFee},
            #{item.bloFee},
            #{item.albuFee},
            #{item.glonFee},
            #{item.clotfacFee},
            #{item.cykiFee},
            #{item.examDspoMatlFee},
            #{item.trtDspoMatlFee},
            #{item.oprnDspoMatlFee},
            #{item.othFee},
            #{item.valiFlag},
            #{item.fixmedinsCode},
            #{item.offsiteMedTreat},
            #{item.preExam},
            #{item.patnRlts},
            #{item.nwbAdmType},
            #{item.mulNwbBirWt},
            #{item.mulNwbAdmWt},
            #{item.opspDiagCaty},
            #{item.opspMdtrtDate},
            #{item.spgaNurscareDays},
            #{item.lv1NurscareDays},
            #{item.scdNurscareDays},
            #{item.lv3NurscareDays},
            #{item.otpWmDiag},
            #{item.otpWmDiagDiseCode},
            #{item.otpTcmDiag},
            #{item.otpTcmDiagDiseCode},
            #{item.admDiag},
            #{item.bldCat},
            #{item.bldAmt},
            #{item.bldUnt},
            '0'
                 )
        </foreach>
    </insert>

    <insert id="insertDiagInfo">
        INSERT INTO som_diag_info (
        unique_id,
        palg_no,
        ipt_patn_disediag_type_code,
        disediag_type,
        maindiag_flag,
        diag_code,
        diag_name,
        inhosp_diag_code,
        inhosp_diag_name,
        adm_dise_cond_name,
        adm_dise_cond_code,
        adm_cond,
        adm_cond_code,
        high_diag_evid,
        bkup_deg,
        bkup_deg_code,
        vali_flag,
        ipt_medcas_hmpg_sn,
        mdtrt_sn,
        fixmedins_code,
        di20_id
        ) VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (
            #{item.uniqueId},
            #{item.palgNo},
            #{item.iptPatnDisediagTypeCode},
            #{item.disediagType},
            #{item.maindiagFlag},
            #{item.diagCode},
            #{item.diagName},
            #{item.inhospDiagCode},
            #{item.inhospDiagName},
            #{item.admDiseCondName},
            #{item.admDiseCondCode},
            #{item.admCond},
            #{item.admCondCode},
            #{item.highDiagEvid},
            #{item.bkupDeg},
            #{item.bkupDegCode},
            #{item.valiFlag},
            #{item.iptMedcasHmpgSn},
            #{item.mdtrtSn},
            #{item.fixmedinsCode},
            #{item.di20Id}
            )
        </foreach>
    </insert>

    <insert id="insertOprnInfo">
        INSERT INTO som_oprn_rcd (
        unique_id,
        oprn_oprt_date,
        oprn_oprt_name,
        oprn_oprt_code,
        oprn_oprt_sn,
        oprn_lv_code,
        oprn_lv_name,
        oper_name,
        oper_code,
        asit1_name,
        asit2_name,
        sinc_heal_lv,
        sinc_heal_lv_code,
        anst_mtd_name,
        anst_mtd_code,
        anst_dr_name,
        anst_dr_code,
        oprn_oper_part,
        oprn_oper_part_code,
        oprn_con_time,
        anst_lv_name,
        anst_lv_code,
        oprn_patn_type,
        oprn_patn_type_code,
        main_oprn_flag,
        anst_asa_lv_code,
        anst_asa_lv_name,
        anst_medn_code,
        anst_medn_name,
        anst_medn_dos,
        unt,
        anst_begntime,
        anst_endtime,
        anst_copn_code,
        anst_copn_name,
        anst_copn_dscr,
        pacu_begntime,
        pacu_endtime,
        canc_oprn_flag,
        vali_flag,
        ipt_medcas_hmpg_sn,
        mdtrt_sn,
        oprn_oprt_begntime,
        oprn_oprt_endtime,
        di20_id
        ) VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (
            #{item.uniqueId},
            #{item.oprnOprtDate},
            #{item.oprnOprtName},
            #{item.oprnOprtCode},
            #{item.oprnOprtSn},
            #{item.oprnLvCode},
            #{item.oprnLvName},
            #{item.operName},
            #{item.operCode},
            #{item.asit1Name},
            #{item.asit2Name},
            #{item.sincHealLv},
            #{item.sincHealLvCode},
            #{item.anstMtdName},
            #{item.anstMtdCode},
            #{item.anstDrName},
            #{item.anstDrCode},
            #{item.oprnOperPart},
            #{item.oprnOperPartCode},
            #{item.oprnConTime},
            #{item.anstLvName},
            #{item.anstLvCode},
            #{item.oprnPatnType},
            #{item.oprnPatnTypeCode},
            #{item.mainOprnFlag},
            #{item.anstAsaLvCode},
            #{item.anstAsaLvName},
            #{item.anstMednCode},
            #{item.anstMednName},
            #{item.anstMednDos},
            #{item.unt},
            #{item.anstBegntime},
            #{item.anstEndtime},
            #{item.anstCopnCode},
            #{item.anstCopnName},
            #{item.anstCopnDscr},
            #{item.pacuBegntime},
            #{item.pacuEndtime},
            #{item.cancOprnFlag},
            #{item.valiFlag},
            #{item.iptMedcasHmpgSn},
            #{item.mdtrtSn},
            #{item.oprnOprtBegntime},
            #{item.oprnOprtEndtime},
            #{item.di20Id}
            )
        </foreach>
    </insert>

    <insert id="insertIcuInfo">
        INSERT INTO som_scs_cutd_info (
        unique_id,
        scs_cutd_ward_type,
        scs_cutd_inpool_time,
        scs_cutd_exit_time,
        scs_cutd_sum_dura,
        di20_id
        ) VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (
            #{item.uniqueId},
            #{item.scsCutdWardType},
            #{item.scsCutdInpoolTime},
            #{item.scsCutdExitTime},
            #{item.scsCutdSumDura},
            #{item.di20Id}
            )
        </foreach>
    </insert>

    <insert id="insertPayInfo">
        INSERT INTO som_fund_pay_info (
        unique_id,
        fund_pay_type,
        fund_payamt,
        di20_id
        ) VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (
            #{item.uniqueId},
            #{item.fundPayType},
            #{item.fundPayamt},
            #{item.di20Id}
            )
        </foreach>
    </insert>

    <insert id="insertOpspInfo">
        INSERT INTO som_otp_crds_diag_info (
        unique_id,
        diag_name,
        diag_code,
        oprn_oprt_name,
        oprn_oprt_code,
        maindiag_flag,
        di20_id
        ) VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (
            #{item.uniqueId},
            #{item.diagName},
            #{item.diagCode},
            #{item.oprnOprtName},
            #{item.oprnOprtCode},
            #{item.maindiagFlag},
            #{item.di20Id}
            )
        </foreach>
    </insert>

    <insert id="insertChrigtmInfo">
        INSERT INTO som_chrg_item_info (
        unique_id,
        med_chrgitm,
        amt,
        claa_sumfee,
        clab_amt,
        fulamt_ownpay_amt,
        oth_amt,
        di20_id
        ) VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (
            #{item.uniqueId},
            #{item.medChrgitm},
            #{item.amt},
            #{item.claaSumfee},
            #{item.clabAmt},
            #{item.fulamtOwnpayAmt},
            #{item.othAmt},
            #{item.di20Id}
            )
        </foreach>
    </insert>

    <insert id="insertSetlInfo">
        INSERT INTO som_setl_info (
        unique_id,
        psn_no,
        mdtrt_id,
        setl_id,
        bill_code,
        bill_no,
        biz_sn,
        setl_begn_date,
        setl_end_date,
        medins_fill_dept,
        medins_fill_psn,
        hsorg,
        hsorg_opter,
        hi_paymtd,
        psn_selfpay,
        psn_ownpay,
        acct_pay,
        psn_cashpay,
        med_ins_fund,
        hi_no,
        hi_setl_lv,
        hi_type,
        sp_psn_type,
        insuplc,
        ipt_med_type,
        di20_id
        ) VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (
            #{item.uniqueId},
            #{item.psnNo},
            #{item.mdtrtId},
            #{item.setlId},
            #{item.billCode},
            #{item.billNo},
            #{item.bizSn},
            #{item.setlBegnDate},
            #{item.setlEndDate},
            #{item.medinsFillDept},
            #{item.medinsFillPsn},
            #{item.hsorg},
            #{item.hsorgOpter},
            #{item.hiPaymtd},
            #{item.psnSelfpay},
            #{item.psnOwnpay},
            #{item.acctPay},
            #{item.psnCashpay},
            #{item.medInsFund},
            #{item.hiNo},
            #{item.hiSetlLv},
            #{item.hiType},
            #{item.spPsnType},
            #{item.insuplc},
            #{item.iptMedType},
            #{item.di20Id}
            )
        </foreach>
    </insert>

    <insert id="insertBldInfo">
        INSERT INTO som_bld_info (
        unique_id,
        bld_cat,
        bld_amt,
        bld_unt,
        di20_id
        ) VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (
            #{item.uniqueId},
            #{item.bldCat},
            #{item.bldAmt},
            #{item.bldUnt},
            #{item.di20Id}
            )
        </foreach>
    </insert>

    <insert id="insertLogExtract">
        INSERT INTO som_log_extraction (
            extraction_start_time,
            extraction_end_time,
            extraction_count,
            extraction_status
        ) VALUES (
                     #{extractionStartTime},
                     #{extractionEndTime},
                     #{extractionCount},
                     #{extractionStatus}
                 )
    </insert>

    <select id="queryHosp" resultType="com.my.som.vo.basicHea.BasicHealthHospitalVO">
        SELECT
            org_code as orgCode,
            org_name as orgName,
            ver_code as verCode,
            api_url as apiUrl
        FROM som_basic_health_hospital
        <if test="fixCode != null and fixCode != '' ">
        WHERE hospital_id = #{fixCode,jdbcType=VARCHAR}
        </if>
    </select>

</mapper>
