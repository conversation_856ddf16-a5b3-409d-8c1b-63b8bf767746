<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.som.mapper.integratedQuery.IntegratedQueryMapper">
        <!--获取页面展示字段-->
        <select id="list" resultType="com.my.som.vo.integratedQuery.IntegratedQueryVo">
                SELECT
                        id,
                        fld,
                        field_name as fieldName,
                        type,
                        fld_type_name as fldTypeName,
                        page_disp_fld as pageDispFld,
                        false as `check`
                from som_qury_fld_cfg
                where type = #{type,jdbcType=VARCHAR}
        </select>


    <select id="queryData" resultType="java.util.Map">
        <if test='sqlType == "1"'>
            <include refid="sqlType1"></include>
        </if>
        <if test='sqlType == "2"'>
            <include refid="sqlType2"></include>
        </if>

    </select>
    <sql id="sqlType2">
            SELECT
            <if test='indexColumn.fullAbnormalNum == "1"'>
                a.notCompeleteNum AS fullAbnormalNum,
            </if>
            <if test='indexColumn.fullAbnormalRate == "1"'>
                ROUND( a.notCompeleteNum / b.totalNum, 4 ) AS fullAbnormalRate,
            </if>
            <if test='indexColumn.casesAbnormalNum == "1"'>
                c.casesAbnormalNum,
            </if>
            <if test='indexColumn.casesAbnormalRate == "1"'>
                ROUND( c.casesAbnormalNum / b.totalNum, 4 ) AS casesAbnormalRate,
            </if>
            <if test='indexColumn.logicAbnormalNum == "1"'>
                a.logicAbnormalNum,
            </if>
            <if test='indexColumn.logicAbnormalRate == "1"'>
                ROUND( a.logicAbnormalNum / b.totalNum, 4 ) AS logicAbnormalRate,
            </if>
            '1' as zw
            FROM
            (
            SELECT
            <if test='indexColumn.fullAbnormalNum == "1" or indexColumn.fullAbnormalRate == "1"'>
                count( DISTINCT ( CASE WHEN err_type IN ( 'CE01', 'CE02', 'CE03', 'CE04' ) THEN a.settle_list_id ELSE NULL END )) AS notCompeleteNum,
            </if>
            <if test='indexColumn.logicAbnormalNum == "1" or indexColumn.logicAbnormalRate == "1"'>
                count(distinct(case when err_type in ('LE01','LE02','LE03','LE04','LE05','LE06','LE07') then a.settle_list_id else null end)) as logicAbnormalNum,
            </if>
            '1' as zw
            FROM
            som_setl_invy_chk_err_rcd a
            WHERE
            settle_list_id IN (
                SELECT id
                FROM som_init_hi_setl_invy_med_fee_info
                WHERE active_flag = 1
                <if test="inStartTime!=null and inStartTime!='' and inEndTime!=null and inEndTime!=''">
                    AND B12 BETWEEN #{inStartTime,jdbcType=VARCHAR} and CONCAT(#{inEndTime,jdbcType=VARCHAR},' 23:59:59')
                </if>
                <if test="begnDate!=null and begnDate!='' and expiDate!=null and expiDate!=''">
                    AND B15 BETWEEN #{begnDate,jdbcType=VARCHAR} and CONCAT(#{expiDate,jdbcType=VARCHAR},' 23:59:59')
                </if>
        )
            ) a
            CROSS JOIN (
                SELECT count( 1 ) AS totalNum FROM som_init_hi_setl_invy_med_fee_info a
                WHERE a.active_flag = 1
                    <if test="inStartTime!=null and inStartTime!='' and inEndTime!=null and inEndTime!=''">
                        AND a.B12 BETWEEN #{inStartTime,jdbcType=VARCHAR} and CONCAT(#{inEndTime,jdbcType=VARCHAR},' 23:59:59')
                    </if>
                    <if test="begnDate!=null and begnDate!='' and expiDate!=null and expiDate!=''">
                        AND a.B15 BETWEEN #{begnDate,jdbcType=VARCHAR} and CONCAT(#{expiDate,jdbcType=VARCHAR},' 23:59:59')
                    </if>
                ) b
            <if test='indexColumn.casesAbnormalNum == "1" or indexColumn.casesAbnormalRate == "1"'>
                CROSS JOIN (
                SELECT
                COUNT(DISTINCT settle_list_id) as casesAbnormalNum
                FROM
                som_setl_invy_chk_err_rcd a
                WHERE
                settle_list_id IN (
                SELECT id
                FROM som_init_hi_setl_invy_med_fee_info
                WHERE active_flag = 1
                <if test="inStartTime!=null and inStartTime!='' and inEndTime!=null and inEndTime!=''">
                    AND B12 BETWEEN #{inStartTime,jdbcType=VARCHAR} and CONCAT(#{inEndTime,jdbcType=VARCHAR},' 23:59:59')
                </if>
                <if test="begnDate!=null and begnDate!='' and expiDate!=null and expiDate!=''">
                    AND B15 BETWEEN #{begnDate,jdbcType=VARCHAR} and CONCAT(#{expiDate,jdbcType=VARCHAR},' 23:59:59')
                </if>
                )
                )c
            </if>
    </sql>
    <sql id="sqlType1">
            select
                <include refid="publicColumn"></include>
            <if test='dimensionalityColumn.isChoose == "1"'>
                <include refid="chooseDimField"></include>
            </if>
            <if test='dimensionalityColumn.isChoose != "1"'>
                <include refid="notChooseDimField"></include>
            </if>
            from som_hi_invy_bas_info a
            <if test='indexColumn.inventory14Coststructure == "1"'>
                LEFT JOIN (SELECT hi_setl_invy_id,
                MAX(CASE WHEN med_chrg_itemname = '床位费' THEN amt ELSE 0 END) AS fourteencwf,
                MAX(CASE WHEN med_chrg_itemname = '诊查费' THEN amt ELSE 0 END) AS fourteenzcf,
                MAX(CASE WHEN med_chrg_itemname = '检查费' THEN amt ELSE 0 END) AS fourteenjcf,
                MAX(CASE WHEN med_chrg_itemname = '化验费' THEN amt ELSE 0 END) AS fourteenhyf,
                MAX(CASE WHEN med_chrg_itemname = '治疗费' THEN amt ELSE 0 END) AS fourteenzlf,
                MAX(CASE WHEN med_chrg_itemname = '手术费' THEN amt ELSE 0 END) AS fourteenssf,
                MAX(CASE WHEN med_chrg_itemname = '护理费' THEN amt ELSE 0 END) AS fourteenhlf,
                MAX(CASE WHEN med_chrg_itemname = '卫生材料费' THEN amt ELSE 0 END) AS fourteenwsclf,
                MAX(CASE WHEN med_chrg_itemname = '西药费' THEN amt ELSE 0 END) AS fourteenxyf,
                MAX(CASE WHEN med_chrg_itemname = '中药饮片费' THEN amt ELSE 0 END) AS fourteenzyypf,
                MAX(CASE WHEN med_chrg_itemname = '中成药费' THEN amt ELSE 0 END) AS fourteenzcyf,
                MAX(CASE WHEN med_chrg_itemname = '一般诊疗费' THEN amt ELSE 0 END) AS fourteenybzlf,
                MAX(CASE WHEN med_chrg_itemname = '挂号费' THEN amt ELSE 0 END) AS fourteenghf,
                MAX(CASE WHEN med_chrg_itemname = '其他费' THEN amt ELSE 0 END) AS fourteenqtf
                FROM som_hi_setl_invy_med_fee_info
                WHERE hi_setl_invy_id IN (
                    SELECT ID AS hi_setl_invy_id
                    FROM som_hi_invy_bas_info a
                    where a.ACTIVE_FLAG = '1'
                    <if test="inStartTime!=null and inStartTime!='' and inEndTime!=null and inEndTime!=''">
                        AND a.B12 BETWEEN #{inStartTime,jdbcType=VARCHAR} and CONCAT(#{inEndTime,jdbcType=VARCHAR},' 23:59:59')
                    </if>
                    <if test="begnDate!=null and begnDate!='' and expiDate!=null and expiDate!=''">
                        AND a.B15 BETWEEN #{begnDate,jdbcType=VARCHAR} and CONCAT(#{expiDate,jdbcType=VARCHAR},' 23:59:59')
                    </if>
                    <if test="seStartTime!=null and seStartTime!='' and seEndTime!=null and seEndTime!=''">
                        AND a.D37 BETWEEN #{seStartTime,jdbcType=VARCHAR} and CONCAT(#{seStartTime,jdbcType=VARCHAR},' 23:59:59')
                    </if>
                )
                GROUP BY hi_setl_invy_id)b on a.ID = b.hi_setl_invy_id
            </if>
            left join ${somDrgGrpInfo} c on a.ID = c.SETTLE_LIST_ID
            and c.HOSPITAL_ID = a.HOSPITAL_ID
            <if test='indexColumn.totalWeight == "1"
            or indexColumn.timeIndex == "1"
            or indexColumn.costIndex == "1"
            or indexColumn.avgTotalCostBen == "1"
            or indexColumn.weight == "1"
            or indexColumn.avgInHosDaysBench == "1"'>
                left join ${somDrgStandard} d
                <choose>
                    <when test='groupType == "1"'>
                        on c.dip_codg = d.dip_codg
                    </when>
                    <when test='groupType == "3"'>
                        on c.drg_codg = d.drg_codg
                    </when>
                </choose>

                and substr(c.dscg_time,1,4) =  d.STANDARD_YEAR
                AND c.HOSPITAL_ID = d.HOSPITAL_ID
            </if>
            <if test='dimensionalityColumn.outHosDept == "1"'>
                left join som_dept e
                on e.CODE = a.B16C
                AND a.HOSPITAL_ID = e.HOSPITAL_ID
            </if>
            <if test='indexColumn.normalNum == "1"
                    or indexColumn.ultrahighNum == "1"
                    or indexColumn.ultrahighRate == "1"
                    or indexColumn.ultraLowNum == "1"
                    or indexColumn.ultraLowRate == "1"
                    or indexColumn.forecastAmount == "1"
                    or indexColumn.forecastAmountDiff == "1"
                    or indexColumn.oeVal == "1"
                    '>
                LEFT JOIN
                (
                SELECT b.SETTLE_LIST_ID,
                b.dise_type,
                CASE WHEN b.insu_type = 1 THEN b.totl_sco * c.czPrice
                WHEN b.insu_type = 2 THEN b.totl_sco * c.cxPrice
                ELSE b.totl_sco * c.price END AS ycCost
                FROM ${busPatientScore} b
                CROSS JOIN
                (
                SELECT MAX( CASE WHEN `KEY` = 'CX_PRICE' THEN `VALUE` ELSE NULL END ) AS cxPrice,
                MAX( CASE WHEN `KEY` = 'CZ_PRICE' THEN `VALUE` ELSE NULL END ) AS czPrice,
                MAX( CASE WHEN `KEY` = 'PRICE' THEN `VALUE` ELSE NULL END ) AS price
                FROM ${cfgCommon}
                WHERE TYPE = 'PREDICTED_PRICE'
                GROUP BY TYPE
                ) c
                ) f
                ON a.ID = f.SETTLE_LIST_ID
            </if>
            <if test='indexColumn.lookOver == "1"'>
                LEFT JOIN som_setl_invy_chk g
                ON a.ID = g.SETTLE_LIST_ID
            </if>
            <if test='indexColumn.insuredType == "1"
                       or indexColumn.stsbFee == "1"
                       or indexColumn.refer_sco == "1"
                       or indexColumn.adjm_cof == "1"
                       or indexColumn.calculateScore == "1"
                       or indexColumn.hospCof == "1"
                       or indexColumn.addScore == "1"
                       or indexColumn.totlSco == "1"
                       or indexColumn.preCost == "1"
                       or indexColumn.profitAndLoss == "1"
                '>
                LEFT JOIN ${busPatientScore} h
                ON a.ID = h.SETTLE_LIST_ID
            </if>
            <if test='indexColumn.preCost == "1"
                      or indexColumn.profitAndLoss == "1"
              '>
                CROSS JOIN
                (
                SELECT MAX(CASE WHEN `KEY` = 'CX_PRICE' THEN `VALUE` ELSE NULL END) AS cxPrice,
                MAX(CASE WHEN `KEY` = 'CZ_PRICE' THEN `VALUE` ELSE NULL END) AS czPrice,
                MAX(CASE WHEN `KEY` = 'PRICE' THEN `VALUE` ELSE NULL END) AS price
                FROM ${cfgCommon}
                WHERE TYPE = 'PREDICTED_PRICE'
                GROUP BY TYPE
                ) i
            </if>
            <!--德阳无首页校验数据-->
            <if test='indexColumn.casesScore=="1"'>
                <include refid="queryCasesScore"></include>
            </if>
            <if test='indexColumn.fullScore=="1" or
                      indexColumn.logicScore=="1"'>
              <include refid="queryFullOrLogicScore"></include>
            </if>
            <if test="indexColumn.b13n == 1">
                left join som_dept l
                on a.b13c = l.code
            </if>
            <if test="indexColumn.b16n == 1">
                left join som_dept m
                on a.b16c = m.code
            </if>
            <if test="indexColumn.c04n == 1">
                left join (
                    SELECT
                    SETTLE_LIST_ID,
                    MAX( CASE WHEN seq = 0 THEN dscg_diag_codg ELSE NULL END ) AS 'mainCode',
                    MAX( CASE WHEN seq = 0 THEN dscg_diag_name ELSE NULL END ) AS 'mainName',
                    MAX( CASE WHEN seq = 1 THEN dscg_diag_codg ELSE NULL END ) AS 'otherCode1',
                    MAX( CASE WHEN seq = 1 THEN dscg_diag_name ELSE NULL END ) AS 'otherName1',
                    MAX( CASE WHEN seq = 2 THEN dscg_diag_codg ELSE NULL END ) AS 'otherCode2',
                    MAX( CASE WHEN seq = 2 THEN dscg_diag_name ELSE NULL END ) AS 'otherName2',
                    MAX( CASE WHEN seq = 3 THEN dscg_diag_codg ELSE NULL END ) AS 'otherCode3',
                    MAX( CASE WHEN seq = 3 THEN dscg_diag_name ELSE NULL END ) AS 'otherName3',
                    MAX( CASE WHEN seq = 4 THEN dscg_diag_codg ELSE NULL END ) AS 'otherCode4',
                    MAX( CASE WHEN seq = 4 THEN dscg_diag_name ELSE NULL END ) AS 'otherName4',
                    MAX( CASE WHEN seq = 5 THEN dscg_diag_codg ELSE NULL END ) AS 'otherCode5',
                    MAX( CASE WHEN seq = 5 THEN dscg_diag_name ELSE NULL END ) AS 'otherName5',
                    MAX( CASE WHEN seq = 6 THEN dscg_diag_codg ELSE NULL END ) AS 'otherCode6',
                    MAX( CASE WHEN seq = 6 THEN dscg_diag_name ELSE NULL END ) AS 'otherName6',
                    MAX( CASE WHEN seq = 7 THEN dscg_diag_codg ELSE NULL END ) AS 'otherCode7',
                    MAX( CASE WHEN seq = 7 THEN dscg_diag_name ELSE NULL END ) AS 'otherName7',
                    MAX( CASE WHEN seq = 8 THEN dscg_diag_codg ELSE NULL END ) AS 'otherCode8',
                    MAX( CASE WHEN seq = 8 THEN dscg_diag_name ELSE NULL END ) AS 'otherName8',
                    MAX( CASE WHEN seq = 9 THEN dscg_diag_codg ELSE NULL END ) AS 'otherCode9',
                    MAX( CASE WHEN seq = 9 THEN dscg_diag_name ELSE NULL END ) AS 'otherName9',
                    MAX( CASE WHEN seq = 10 THEN dscg_diag_codg ELSE NULL END ) AS 'otherCode10',
                    MAX( CASE WHEN seq = 10 THEN dscg_diag_name ELSE NULL END ) AS 'otherName10',
                    MAX( CASE WHEN seq = 11 THEN dscg_diag_codg ELSE NULL END ) AS 'otherCode11',
                    MAX( CASE WHEN seq = 11 THEN dscg_diag_name ELSE NULL END ) AS 'otherName11',
                    MAX( CASE WHEN seq = 12 THEN dscg_diag_codg ELSE NULL END ) AS 'otherCode12',
                    MAX( CASE WHEN seq = 12 THEN dscg_diag_name ELSE NULL END ) AS 'otherName12',
                    MAX( CASE WHEN seq = 13 THEN dscg_diag_codg ELSE NULL END ) AS 'otherCode13',
                    MAX( CASE WHEN seq = 13 THEN dscg_diag_name ELSE NULL END ) AS 'otherName13',
                    MAX( CASE WHEN seq = 14 THEN dscg_diag_codg ELSE NULL END ) AS 'otherCode14',
                    MAX( CASE WHEN seq = 14 THEN dscg_diag_name ELSE NULL END ) AS 'otherName14',
                    MAX( CASE WHEN seq = 15 THEN dscg_diag_codg ELSE NULL END ) AS 'otherCode15',
                    MAX( CASE WHEN seq = 15 THEN dscg_diag_name ELSE NULL END ) AS 'otherName15'
                    FROM
                    som_diag
                    WHERE SETTLE_LIST_ID IN (
                        SELECT ID FROM som_hi_invy_bas_info where 1 = 1
                            <if test="inStartTime!=null and inStartTime!='' and inEndTime!=null and inEndTime!=''">
                                AND B12 BETWEEN #{inStartTime,jdbcType=VARCHAR} and CONCAT(#{inEndTime,jdbcType=VARCHAR},' 23:59:59')
                            </if>
                            <if test="begnDate!=null and begnDate!='' and expiDate!=null and expiDate!=''">
                                AND B15 BETWEEN #{begnDate,jdbcType=VARCHAR} and CONCAT(#{expiDate,jdbcType=VARCHAR},' 23:59:59')
                            </if>
                            <if test="seStartTime!=null and seStartTime!='' and seEndTime!=null and seEndTime!=''">
                                AND D37 BETWEEN #{seStartTime,jdbcType=VARCHAR} and CONCAT(#{seStartTime,jdbcType=VARCHAR},' 23:59:59')
                            </if>
                    )
                    GROUP BY SETTLE_LIST_ID
                ) n
                on a.id = n.SETTLE_LIST_ID
            </if>
            <if test="indexColumn.c15x01n == 1">
                left join (
                    SELECT
                    SETTLE_LIST_ID,
                    MAX( CASE WHEN seq = 0 THEN C35C ELSE NULL END ) AS 'operateCode1',
                    MAX( CASE WHEN seq = 0 THEN C36N ELSE NULL END ) AS 'operatename1',
                    MAX( CASE WHEN seq = 1 THEN C35C ELSE NULL END ) AS 'operateCode2',
                    MAX( CASE WHEN seq = 1 THEN C36N ELSE NULL END ) AS 'operatename2',
                    MAX( CASE WHEN seq = 2 THEN C35C ELSE NULL END ) AS 'operateCode3',
                    MAX( CASE WHEN seq = 2 THEN C36N ELSE NULL END ) AS 'operatename3',
                    MAX( CASE WHEN seq = 3 THEN C35C ELSE NULL END ) AS 'operateCode4',
                    MAX( CASE WHEN seq = 3 THEN C36N ELSE NULL END ) AS 'operatename4',
                    MAX( CASE WHEN seq = 4 THEN C35C ELSE NULL END ) AS 'operateCode5',
                    MAX( CASE WHEN seq = 4 THEN C36N ELSE NULL END ) AS 'operatename5',
                    MAX( CASE WHEN seq = 5 THEN C35C ELSE NULL END ) AS 'operateCode6',
                    MAX( CASE WHEN seq = 5 THEN C36N ELSE NULL END ) AS 'operatename6',
                    MAX( CASE WHEN seq = 6 THEN C35C ELSE NULL END ) AS 'operateCode7',
                    MAX( CASE WHEN seq = 6 THEN C36N ELSE NULL END ) AS 'operatename7'
                    FROM
                    som_oprn_oprt_info
                    WHERE SETTLE_LIST_ID IN (
                        SELECT ID FROM som_hi_invy_bas_info where 1 = 1
                            <if test="inStartTime!=null and inStartTime!='' and inEndTime!=null and inEndTime!=''">
                                AND B12 BETWEEN #{inStartTime,jdbcType=VARCHAR} and CONCAT(#{inEndTime,jdbcType=VARCHAR},' 23:59:59')
                            </if>
                            <if test="begnDate!=null and begnDate!='' and expiDate!=null and expiDate!=''">
                                AND B15 BETWEEN #{begnDate,jdbcType=VARCHAR} and CONCAT(#{expiDate,jdbcType=VARCHAR},' 23:59:59')
                            </if>
                            <if test="seStartTime!=null and seStartTime!='' and seEndTime!=null and seEndTime!=''">
                                AND D37 BETWEEN #{seStartTime,jdbcType=VARCHAR} and CONCAT(#{seStartTime,jdbcType=VARCHAR},' 23:59:59')
                            </if>
                        )
                    GROUP BY SETTLE_LIST_ID
                ) o
                on o.SETTLE_LIST_ID = a.id
            </if>
            where a.ACTIVE_FLAG = '1'
            <if test="inStartTime!=null and inStartTime!='' and inEndTime!=null and inEndTime!=''">
                AND a.B12 BETWEEN #{inStartTime,jdbcType=VARCHAR} and CONCAT(#{inEndTime,jdbcType=VARCHAR},' 23:59:59')
            </if>
            <if test="begnDate!=null and begnDate!='' and expiDate!=null and expiDate!=''">
                AND a.B15 BETWEEN #{begnDate,jdbcType=VARCHAR} and CONCAT(#{expiDate,jdbcType=VARCHAR},' 23:59:59')
            </if>
            <if test="seStartTime!=null and seStartTime!='' and seEndTime!=null and seEndTime!=''">
                AND a.D37 BETWEEN #{seStartTime,jdbcType=VARCHAR} and CONCAT(#{seStartTime,jdbcType=VARCHAR},' 23:59:59')
            </if>
            <if test="hospitalId!=null and hospitalId!=''">
                AND a.HOSPITAL_ID = #{hospitalId,jdbcType=VARCHAR}
            </if>
            <if test='dimensionalityColumn.isChoose == "1"'>
                <if test='dimensionalityColumn.drg == "1"'>
                    <choose>
                        <when test='groupType == "1"'>
                            group by c.dip_codg,c.DIP_NAME
                        </when>
                        <when test='groupType == "3"'>
                            group by c.drg_codg,c.DRG_NAME
                        </when>
                    </choose>
                </if>
                <if test='dimensionalityColumn.outHosDept == "1"'>
                    group by e.CODE,e.NAME
                </if>
                <if test='dimensionalityColumn.doctor == "1"'>
                    group by a.B25C,a.B25N
                </if>
            </if>
    </sql>
    <!-- 未选择维度了的列 -->
    <sql id="notChooseDimField">
        <if test='indexColumn.weight == "1"'>
            <if test="grperType == 1">
                ROUND(IFNULL(d.dip_wt,0), 2) AS weight,
            </if>
            <if test="grperType == 3">
                ROUND(IFNULL(d.drg_wt,0), 2) AS weight,
            </if>
        </if>
        <if test='indexColumn.profitAndLoss == "1"'>
           ROUND( IFNULL(CASE WHEN h.insu_type = 1 THEN round(h.totl_sco * i.czPrice,2)
            WHEN h.insu_type = 2 THEN round(h.totl_sco * i.cxPrice,2)
            ELSE round(h.totl_sco * i.price,2) END - IFNULL(a.D01,0),0),2) as profitAndLoss,
        </if>
        <if test='indexColumn.preCost == "1"'>
            CASE WHEN h.insu_type = 1 THEN round(h.totl_sco * i.czPrice,2)
            WHEN h.insu_type = 2 THEN round(h.totl_sco * i.cxPrice,2)
            ELSE round(h.totl_sco * i.price,2) END AS preCost,
        </if>
        <if test='indexColumn.totlSco == "1"'>
            h.totl_sco AS totlSco,
        </if>
        <if test='indexColumn.addScore == "1"'>
            IFNULL(h.incr_sco,0) AS addScore,
        </if>
        <if test='indexColumn.hospCof == "1"'>
            h.hosp_cof AS hospCof,
        </if>
        <if test='indexColumn.calculateScore == "1"'>
            h.setl_sco AS calculateScore,
        </if>
        <if test='indexColumn.adjm_cof == "1"'>
            h.adjm_cof AS adjm_cof,
        </if>
        <if test='indexColumn.refer_sco == "1"'>
            ROUND(h.refer_sco,2) AS refer_sco,
        </if>
        <if test='indexColumn.stsbFee == "1"'>
            <if test="grperType == 1">
                CASE WHEN h.dise_type = '1' THEN '超高病例'
                     WHEN h.dise_type = '2' THEN '超低病例'
                     WHEN h.dise_type = '3' THEN '正常病例'
                     WHEN h.dise_type = '4' THEN '暂无标杆'
                     WHEN h.dise_type = '5' THEN '非稳定病例'
                     WHEN h.dise_type = '6' THEN '极高费用病例'
                     WHEN h.dise_type = '0' THEN '暂未分组'
                ELSE '其他' END as stsbFee,
            </if>
            <if test="grperType == 3">
                CASE WHEN h.dise_type = '1' THEN '超高病例'
                     WHEN h.dise_type = '2' THEN '超低病例'
                     WHEN h.dise_type = '3' THEN '正常病例'
                     WHEN h.dise_type = '4' THEN '暂无标杆'
                     WHEN h.dise_type = '5' THEN '非稳定病例'
                     WHEN h.dise_type = '6' THEN '床日病例'
                     WHEN h.dise_type = '7' THEN '住院过程不完整病例'
                     WHEN h.dise_type = '8' THEN '日间手术病例'
                     WHEN h.dise_type = '9' THEN '暂未分组'
                ELSE '其他' END as stsbFee,
            </if>
        </if>
        <if test='indexColumn.insuredType == "1"'>
            CASE WHEN h.insu_type = '1' THEN '城职'
                 WHEN h.insu_type = '01' THEN '城职'
                 WHEN h.insu_type = '2' THEN '城乡'
                 WHEN h.insu_type = '02' THEN '城乡'
            ELSE '其他' END AS insuredType,
        </if>
        <if test='indexColumn.d37 == "1"'>
            a.D37 as d37,
        </if>
        <if test='indexColumn.lookOver == "1"'>
            CASE WHEN a.LOOK_OVER = '0' THEN '未标记'
                 WHEN a.LOOK_OVER = '1' THEN '已标记'
            ELSE '未标记' END as lookOver,
        </if>
        <if test='indexColumn.chkStas == "1"'>
            CASE WHEN g.chk_stas = '0' THEN '未通过'
                 WHEN g.chk_stas = '1' THEN '已通过'
            ELSE '未通过' END as chkStas,
        </if>
        <if test='indexColumn.b15 == "1"'>
            a.B15 as b15,
        </if>
        <if test='indexColumn.b12 == "1"'>
            a.B12 as b12,
        </if>
        <if test='indexColumn.c15x01n == "1"'>
            CONCAT(o.operateCode1,o.operateName1) as c15x01n,
        </if>
        <if test='indexColumn.c04n == "1"'>
            CONCAT(n.mainCode,n.mainName) as c04n,
        </if>
        <if test='indexColumn.b16n == "1"'>
            m.name as b16n,
        </if>
        <if test='indexColumn.b13n == "1"'>
            l.name as b13n,
        </if>
        <if test='indexColumn.diseaseCodeAndName == "1"'>
            <if test="grperType == 1">
                CONCAT(c.dip_codg,c.DIP_NAME) as diseaseCodeAndName,
            </if>
            <if test="grperType == 3">
                CONCAT(c.drg_codg,c.DRG_NAME) as diseaseCodeAndName,
            </if>
        </if>
        <if test='indexColumn.a14 == "1"'>
            a.A14 as a14,
        </if>
        <if test='indexColumn.a12c == "1"'>
            CASE WHEN a.A12C = '1' THEN '男'
                 WHEN a.A12C = '2' THEN '女'
            ELSE '其他' END as a12c,
        </if>
        <if test='indexColumn.a11 == "1"'>
            a.A11 as a11,
        </if>
        <if test='indexColumn.a48 == "1"'>
            a.A48 as a48,
        </if>
        <if test='indexColumn.casesTotal == "1"'>
            1 as casesTotal,
        </if>
        <if test='indexColumn.iptSumfee == "1"'>
            a.D01 as iptSumfee,
        </if>
        <if test='indexColumn.inventory14Coststructure == "1"'>
            b.*,
        </if>
        <!-- 10项费用 -->
        <if test='indexColumn.medical10Coststructure == "1"'>
            a.D11,<!-- 一般医疗服务费 -->
            a.D12,<!-- 一般治疗操作费 -->
            a.D13,<!-- 护理费 -->
            a.D14,<!-- 综合医疗服务类其他费用 -->
            a.D15,<!-- 病理诊断费 -->
            a.D16,<!-- 实验室诊断费 -->
            a.D17,<!-- 影像学诊断费 -->
            a.D18,<!-- 临床诊断项目费 -->
            a.D19,<!-- 非手术治疗项目费 -->
            a.D20,<!-- 手术治疗费 -->
            a.D21,<!-- 康复费 -->
            a.D22,<!-- 中医治疗费 -->
            a.D23,<!-- 西药费 -->
            a.D24,<!-- 中成药费 -->
            a.D25,<!-- 中草药费 -->
            a.D26,<!-- 血费 -->
            a.D27,<!-- 白蛋白类制品费 -->
            a.D28,<!-- 球蛋白类制品费 -->
            a.D29,<!-- 凝血因子类制品费 -->
            a.D30,<!-- 细胞因子类制品费 -->
            a.D31,<!-- 检查用一次性医用材料费 -->
            a.D32,<!-- 治疗用一次性医用材料费 -->
            a.D33,<!-- 手术用一次性医用材料费 -->
            a.D34,<!-- 其他费 -->
        </if>
        <if test='indexColumn.cmi == "1"'>
            <if test='groupType == "1"'>
                IFNULL(round(SUM(CASE WHEN c.grp_stas = '1' THEN c.dip_wt ELSE NULL END)/
                NULLIF (COUNT(CASE WHEN c.grp_stas = '1' THEN 1 ELSE NULL END),0),2),0)   AS cmi,
            </if>
            <if test='groupType == "3"'>
                IFNULL(round(SUM(CASE WHEN c.grp_stas = '1' THEN c.drg_wt ELSE NULL END)/
                NULLIF (COUNT(CASE WHEN c.grp_stas = '1' THEN 1 ELSE NULL END),0),2),0)   AS cmi,
            </if>

        </if>
        <if test='indexColumn.totalWeight == "1"'>
            <if test='groupType == "1"'>
                ROUND(IFNULL(SUM(c.dip_wt),0),2) AS totalWeight,
            </if>
            <if test='groupType == "3"'>
                ROUND(IFNULL(SUM(c.drg_wt),0),2) AS totalWeight,
            </if>
        </if>
        <if test='indexColumn.timeIndex == "1"'>
            <if test="grperType == 1">
                IFNULL(ROUND(SUM(CASE WHEN c.grp_stas = '1' THEN c.act_ipt ELSE NULL END / NULLIF(convert(AES_DECRYPT(UNHEX(d.dip_standard_ipt_days_same_lv),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) using utf8),0)) /
                NULLIF(COUNT(CASE WHEN c.grp_stas = '1' THEN 1 ELSE NULL END),0), 2),0)  AS timeIndex,
            </if>
            <if test="grperType == 3">
                IFNULL(ROUND(SUM(CASE WHEN c.grp_stas = '1' THEN c.act_ipt ELSE NULL END / NULLIF(convert(AES_DECRYPT(UNHEX(d.standard_ave_hosp_day),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) using utf8),0)) /
                NULLIF(COUNT(CASE WHEN c.grp_stas = '1' THEN 1 ELSE NULL END),0), 2),0)  AS timeIndex,
            </if>
        </if>
        <if test='indexColumn.costIndex == "1"'>
            <if test="grperType == 1">
                IFNULL(ROUND(SUM(CASE WHEN c.grp_stas = '1' THEN c.ipt_sumfee ELSE NULL END / NULLIF(convert(AES_DECRYPT(UNHEX(d.dip_standard_avg_fee_same_lv),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) using utf8),0)) /
                NULLIF(COUNT(CASE WHEN c.grp_stas = '1' THEN 1 ELSE NULL END),0), 2),0)   AS costIndex,
            </if>
            <if test="grperType == 3">
                IFNULL(ROUND(SUM(CASE WHEN c.grp_stas = '1' THEN c.ipt_sumfee ELSE NULL END / NULLIF(convert(AES_DECRYPT(UNHEX(d.standard_avg_fee),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) using utf8),0)) /
                NULLIF(COUNT(CASE WHEN c.grp_stas = '1' THEN 1 ELSE NULL END),0), 2),0)   AS costIndex,
            </if>
        </if>
        <if test='indexColumn.avgInHosDays == "1"'>
            IFNULL(ROUND(sum(CASE WHEN a.ACTIVE_FLAG = '1' THEN a.B20 ELSE NULL END) / NULLIF(COUNT(CASE WHEN a.ACTIVE_FLAG = '1' THEN 1 ELSE null END),0)),0) AS avgInHosDays,
        </if>
        <if test='indexColumn.avgInHosDaysBench == "1"'>
            <if test="grperType == 1">
                ROUND(avg(convert(AES_DECRYPT(UNHEX(d.dip_standard_ipt_days_same_lv),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) using utf8)),4) as avgInHosDaysBench,
            </if>
            <if test="grperType == 3">
                ROUND(avg(convert(AES_DECRYPT(UNHEX(d.standard_ave_hosp_day),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) using utf8)),4) as avgInHosDaysBench,
            </if>
        </if>
        <if test='indexColumn.avgFee == "1"'>
            IFNULL(ROUND(sum(CASE WHEN a.ACTIVE_FLAG = '1' THEN a.D01 ELSE NULL END) / NULLIF(COUNT(CASE WHEN a.ACTIVE_FLAG = '1' THEN 1 ELSE null END),0)),0) AS avgFee,
        </if>
        <if test='indexColumn.avgTotalCostBen == "1"'>
            <if test="grperType == 1">
                ROUND(avg(convert(AES_DECRYPT(UNHEX(d.dip_standard_avg_fee_same_lv),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) using utf8)),4) AS avgTotalCostBen,
            </if>
            <if test="grperType == 3">
                ROUND(avg(convert(AES_DECRYPT(UNHEX(d.standard_avg_fee),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) using utf8)),4) AS avgTotalCostBen,
            </if>
        </if>
        <!--病案评分-->
        <if test='indexColumn.casesScore=="1"'>
            j.casesScore,
        </if>
        <!--完整性评分-->
        <if test='indexColumn.fullScore=="1"'>
            k.fullScore,
        </if>
        <!--逻辑性评分-->
        <if test='indexColumn.logicScore=="1"'>
            k.logicScore,
        </if>
        1 as zw
    </sql>
    <!-- 选择维度了的列 -->
    <sql id="chooseDimField">
        <if test='indexColumn.weight == "1"'>
            ROUND(IFNULL(SUM(d.drg_wt),0), 2) AS weight,
        </if>
        <if test='dimensionalityColumn.doctor == "1"'>
            a.B25N as doctor,
        </if>
        <if test='dimensionalityColumn.outHosDept == "1"'>
            e.name as outHosDept,
        </if>
        <if test='dimensionalityColumn.drg == "1"'>
            <choose>
                <when test='groupType == "1"'>
                    CONCAT(c.dip_codg,c.DIP_NAME) as drg,
                </when>
                <when test='groupType == "3"'>
                    CONCAT(c.drg_codg,c.DRG_NAME) as drg,
                </when>
            </choose>

        </if>
        <if test='indexColumn.casesTotal == "1"'>
            COUNT(1) as casesTotal,
        </if>
        <if test='indexColumn.iptSumfee == "1"'>
            IFNULL(avg(a.D01),0) as iptSumfee,
        </if>
        <if test='indexColumn.inventory14Coststructure == "1"'>
            avg(b.fourteencwf) as fourteencwf,
            avg(b.fourteenzcf) as fourteenzcf,
            avg(b.fourteenjcf) as fourteenjcf,
            avg(b.fourteenhyf) as fourteenhyf,
            avg(b.fourteenzlf) as fourteenzlf,
            avg(b.fourteenssf) as fourteenssf,
            avg(b.fourteenhlf) as fourteenhlf,
            avg(b.fourteenwsclf) as fourteenwsclf,
            avg(b.fourteenxyf) as fourteenxyf,
            avg(b.fourteenzyypf) as fourteenzyypf,
            avg(b.fourteenzcyf) as fourteenzcyf,
            avg(b.fourteenybzlf) as fourteenybzlf,
            avg(b.fourteenghf) as fourteenghf,
            avg(b.fourteenqtf) as fourteenqtf,
        </if>
        <!-- 10项费用 -->
        <if test='indexColumn.medical10Coststructure == "1"'>
            avg(a.D11) as D11,<!-- 一般医疗服务费 -->
            avg(a.D12) as D12,<!-- 一般治疗操作费 -->
            avg(a.D13) as D13,<!-- 护理费 -->
            avg(a.D14) as D14,<!-- 综合医疗服务类其他费用 -->
            avg(a.D15) as D15,<!-- 病理诊断费 -->
            avg(a.D16) as D16,<!-- 实验室诊断费 -->
            avg(a.D17) as D17,<!-- 影像学诊断费 -->
            avg(a.D18) as D18,<!-- 临床诊断项目费 -->
            avg(a.D19) as D19,<!-- 非手术治疗项目费 -->
            avg(a.D20) as D20,<!-- 手术治疗费 -->
            avg(a.D21) as D21,<!-- 康复费 -->
            avg(a.D22) as D22,<!-- 中医治疗费 -->
            avg(a.D23) as D23,<!-- 西药费 -->
            avg(a.D24) as D24,<!-- 中成药费 -->
            avg(a.D25) as D25,<!-- 中草药费 -->
            avg(a.D26) as D26,<!-- 血费 -->
            avg(a.D27) as D27,<!-- 白蛋白类制品费 -->
            avg(a.D28) as D28,<!-- 球蛋白类制品费 -->
            avg(a.D29) as D29,<!-- 凝血因子类制品费 -->
            avg(a.D30) as D30,<!-- 细胞因子类制品费 -->
            avg(a.D31) as D31,<!-- 检查用一次性医用材料费 -->
            avg(a.D32) as D32,<!-- 治疗用一次性医用材料费 -->
            avg(a.D33) as D33,<!-- 手术用一次性医用材料费 -->
            avg(a.D34) as D34,<!-- 其他费 -->
        </if>
        <if test='indexColumn.cmi == "1"'>
            <choose>
                <when test='groupType == "1"'>
                    IFNULL(round(SUM(CASE WHEN c.grp_stas = '1' THEN c.dip_wt ELSE NULL END)/
                    NULLIF (COUNT(CASE WHEN c.grp_stas = '1' THEN 1 ELSE NULL END),0),2),0)   AS cmi,
                </when>
                <when test='groupType == "3"'>
                    IFNULL(round(SUM(CASE WHEN c.grp_stas = '1' THEN c.drg_wt ELSE NULL END)/
                    NULLIF (COUNT(CASE WHEN c.grp_stas = '1' THEN 1 ELSE NULL END),0),2),0)   AS cmi,
                </when>
            </choose>
        </if>
        <if test='indexColumn.totalWeight == "1"'>
            <choose>
                <when test='groupType == "1"'>
                    ROUND(IFNULL(SUM(c.dip_wt),0),2) AS totalWeight,
                </when>
                <when test='groupType == "3"'>
                    ROUND(IFNULL(SUM(c.drg_wt),0),2) AS totalWeight,
                </when>
            </choose>
        </if>
        <if test='indexColumn.timeIndex == "1"'>
            <if test="grperType == 1">
                IFNULL(ROUND(SUM(CASE WHEN c.grp_stas = '1' THEN c.act_ipt ELSE NULL END / NULLIF(convert(AES_DECRYPT(UNHEX(d.dip_standard_ipt_days_same_lv),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) using utf8),0)) /
                NULLIF(COUNT(CASE WHEN c.grp_stas = '1' THEN 1 ELSE NULL END),0), 2),0)  AS timeIndex,
            </if>
            <if test="grperType == 3">
                IFNULL(ROUND(SUM(CASE WHEN c.grp_stas = '1' THEN c.act_ipt ELSE NULL END / NULLIF(convert(AES_DECRYPT(UNHEX(d.standard_ave_hosp_day),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) using utf8),0)) /
                NULLIF(COUNT(CASE WHEN c.grp_stas = '1' THEN 1 ELSE NULL END),0), 2),0)  AS timeIndex,
            </if>
        </if>
        <if test='indexColumn.costIndex == "1"'>
            <if test="grperType == 1">
                IFNULL(ROUND(SUM(CASE WHEN c.grp_stas = '1' THEN c.ipt_sumfee ELSE NULL END / NULLIF(convert(AES_DECRYPT(UNHEX(d.dip_standard_avg_fee_same_lv),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) using utf8),0)) /
                NULLIF(COUNT(CASE WHEN c.grp_stas = '1' THEN 1 ELSE NULL END),0), 2),0)   AS costIndex,
            </if>
            <if test="grperType == 3">
                IFNULL(ROUND(SUM(CASE WHEN c.grp_stas = '1' THEN c.ipt_sumfee ELSE NULL END / NULLIF(convert(AES_DECRYPT(UNHEX(d.standard_avg_fee),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) using utf8),0)) /
                NULLIF(COUNT(CASE WHEN c.grp_stas = '1' THEN 1 ELSE NULL END),0), 2),0)   AS costIndex,
            </if>
        </if>
        <if test='indexColumn.avgInHosDays == "1"'>
            IFNULL(ROUND(sum(CASE WHEN a.ACTIVE_FLAG = '1' THEN a.B20 ELSE NULL END) / NULLIF(COUNT(CASE WHEN a.ACTIVE_FLAG = '1' THEN 1 ELSE null END),0)),0) AS avgInHosDays,
        </if>
        <if test='indexColumn.avgInHosDaysBench == "1"'>
            <if test="grperType == 1">
                ROUND(avg(convert(AES_DECRYPT(UNHEX(d.dip_standard_ipt_days_same_lv),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) using utf8)),4) as avgInHosDaysBench,
            </if>
            <if test="grperType == 3">
                ROUND(avg(convert(AES_DECRYPT(UNHEX(d.standard_ave_hosp_day),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) using utf8)),4) as avgInHosDaysBench,
            </if>
        </if>
        <if test='indexColumn.avgFee == "1"'>
            IFNULL(ROUND(sum(CASE WHEN a.ACTIVE_FLAG = '1' THEN a.D01 ELSE NULL END) / NULLIF(COUNT(CASE WHEN a.ACTIVE_FLAG = '1' THEN 1 ELSE null END),0)),0) AS avgFee,
        </if>
        <if test='indexColumn.avgTotalCostBen == "1"'>
            <if test="grperType == 1">
                ROUND(avg(convert(AES_DECRYPT(UNHEX(d.dip_standard_avg_fee_same_lv),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) using utf8)),4) AS avgTotalCostBen,
            </if>
            <if test="grperType == 3">
                ROUND(avg(convert(AES_DECRYPT(UNHEX(d.standard_avg_fee),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) using utf8)),4) AS avgTotalCostBen,
            </if>
        </if>
        <!--病案评分-->
        <if test='indexColumn.casesScore=="1"'>
           ROUND(IFNULL( AVG(j.casesScore),0),2) as casesScore,
        </if>
        <!--完整性评分-->
        <if test='indexColumn.fullScore=="1"'>
           ROUND(IFNULL(AVG(k.fullScore),0),2) as fullScore ,

        </if>
        <!--逻辑性评分-->
        <if test='indexColumn.logicScore=="1"'>
            ROUND(IFNULL(AVG( k.logicScore),0),2)  as  logicScore,
        </if>
        1 as zw
    </sql>
    <!-- 公共的列 -->
    <sql id="publicColumn">
        <if test='indexColumn.oeVal == "1"'>
            IFNULL(ROUND(SUM(a.D01) / SUM(f.ycCost),2),0) AS oeVal,
        </if>
        <if test='indexColumn.forecastAmountDiff == "1"'>
            IFNULL(ROUND(IFNULL(SUM(f.ycCost),0) - IFNULL(SUM(a.D01),0),2),0) AS forecastAmountDiff,
        </if>
        <if test='indexColumn.forecastAmount == "1"'>
            IFNULL(ROUND(SUM(f.ycCost),2),0) AS forecastAmount,
        </if>
        <if test='indexColumn.sumfee == "1"'>
            IFNULL(ROUND(SUM(a.D01),2),0) AS sumfee,
        </if>
        <if test='indexColumn.ultraLowRate == "1"'>
            IFNULL(ROUND(COUNT(CASE WHEN f.dise_type = 2 THEN 1 ELSE NULL END) / COUNT(1) * 100,2),0) AS ultraLowRate,
        </if>
        <if test='indexColumn.ultraLowNum == "1"'>
            IFNULL(COUNT(CASE WHEN f.dise_type = 2 THEN 1 ELSE NULL END),0) AS ultraLowNum,
        </if>
        <if test='indexColumn.ultrahighRate == "1"'>
            IFNULL(ROUND(COUNT(CASE WHEN f.dise_type = 1 THEN 1 ELSE NULL END) / COUNT(1) * 100,2),0) AS ultrahighRate,
        </if>
        <if test='indexColumn.ultrahighNum == "1"'>
            IFNULL(COUNT(CASE WHEN f.dise_type = 1 THEN 1 ELSE NULL END),0) AS ultrahighNum,
        </if>
        <if test='indexColumn.normalNum == "1"'>
            IFNULL(COUNT(CASE WHEN f.dise_type = 3 THEN 1 ELSE NULL END),0) AS normalNum,
        </if>
        <if test='indexColumn.medicalCostRate == "1"'>
            IFNULL(ROUND(SUM(c.drugfee)/NULLIF(SUM(c.ipt_sumfee),0)*100,2),0) AS medicalCostRate,
        </if>
        <if test='indexColumn.materialCostRate == "1"'>
            IFNULL(ROUND(SUM(c.mcs_fee)/NULLIF(SUM(c.ipt_sumfee),0)*100,2),0) AS materialCostRate,
        </if>
        <if test='indexColumn.inGroupRate == "1"'>
            IFNULL(ROUND(NULLIF(sum(case when c.grp_stas = '1' then 1 else 0 end),0)/count(1) * 100,2),0) as inGroupRate,
        </if>
        <if test='indexColumn.drgInGroupMedcasVal == "1"'>
            IFNULL(count(case when c.grp_stas = '1' then 1 else null end),0) as drgInGroupMedcasVal,
        </if>
        <if test='indexColumn.nonGroupNum == "1"'>
            IFNULL(count(case when c.grp_stas = '0' then 1 else null end),0) as nonGroupNum,
        </if>
        <if test='indexColumn.groupNum == "1"'>
            <choose>
                <when test='groupType == "1"'>
                    IFNULL(count(DISTINCT(case when c.grp_stas = '1' then c.dip_codg else null end)),0) as groupNum,
                </when>
                <when test='groupType == "3"'>
                    IFNULL(count(DISTINCT(case when c.grp_stas = '1' then c.drg_codg else null end)),0) as groupNum,
                </when>
            </choose>
        </if>
    </sql>

        <!--病案评分-->
        <sql id="queryCasesScore">

            left join(
            SELECT

            round( IFNULL(sum( refer_sco )/ count( 1 ), 0 ),2) AS casesScore,
            <if test='dimensionalityColumn.isChoose==null or dimensionalityColumn.isChoose==""'>
                c.ID,
            </if>
            <if test='dimensionalityColumn.outHosDept=="1" or dimensionalityColumn.inHosDept=="1"'>
                d.CODE,d.NAME,
            </if>
            <if test='dimensionalityColumn.doctor=="1"'>
                c.B25C,
                c.B25N,
            </if>
            <if test='dimensionalityColumn.drg=="1"'>
                c.C03C,
                c.C04N,
            </if>
            c.HOSPITAL_ID,
            '1' as joint1
            FROM
            som_hi_invy_bas_info c
            left join som_setl_invy_qlt_dedu_point_detl a on c.id=A.SETTLE_LIST_ID
            LEFT JOIN som_setl_invy_chk_err_rcd b on a.SETTLE_LIST_ID=b.SETTLE_LIST_ID
            <if test='dimensionalityColumn.outHosDept=="1" or dimensionalityColumn.inHosDept=="1"'>
                left join som_dept d on d.CODE=c.B13C or d.CODE=c.B16C
            </if>
            WHERE
            a.settle_list_id IN (
            SELECT
            id
            FROM
            som_init_hi_setl_invy_med_fee_info
            <where>
                active_flag = 1
                <if test="
                inStartTime!=null and inStartTime!='' and inEndTime!=null and inEndTime!=''">
                    AND c.B12 BETWEEN #{inStartTime,jdbcType=VARCHAR} and CONCAT(#{inEndTime,jdbcType=VARCHAR
                    },' 23:59:59')
                </if>
                <if test="begnDate!=null and begnDate!='' and expiDate!=null and expiDate!=''">
                    AND c.B15 BETWEEN #{begnDate,jdbcType=VARCHAR} and CONCAT(#{expiDate,jdbcType=VARCHAR},' 23:59:59')
                </if>
                <if test="seStartTime!=null and seStartTime!='' and seEndTime!=null and seEndTime!=''">
                    AND c.D37 BETWEEN #{seStartTime,jdbcType=VARCHAR} and CONCAT(#{
                    seEndTime,jdbcType=VARCHAR},' 23:59:59')
                </if>
            </where>
            )

            <if test='dimensionalityColumn.isChoose==null or dimensionalityColumn.isChoose=="" '>
                group by c.ID ,  c.HOSPITAL_ID
                )j on j.id=a.id  AND j.HOSPITAL_ID=a.HOSPITAL_ID
            </if>
            <if test='dimensionalityColumn.isChoose=="1" '>
                <if test='dimensionalityColumn.outHosDept=="1" or dimensionalityColumn.inHosDept=="1"'>
                    group by d.CODE,d.NAME,  c.HOSPITAL_ID
                    )j on j.CODE=e.CODE  AND j.HOSPITAL_ID=a.HOSPITAL_ID
                </if>
                <if test='dimensionalityColumn.doctor=="1"'>
                    group by c.B25C,  c.HOSPITAL_ID,
                    c.B25N
                    )j on j.B25C=a.B25C  AND j.HOSPITAL_ID=a.HOSPITAL_ID
                </if>
                <if test='dimensionalityColumn.drg=="1"'>
                    group by  c.C03C,  c.HOSPITAL_ID,
                    c.C04N
                    )j on j.C03C=a.C03C  AND j.HOSPITAL_ID=a.HOSPITAL_ID
                </if>

            </if>

        </sql>
        <!--完整和逻辑分-->
        <sql id="queryFullOrLogicScore">
            right join(

            SELECT
            ROUND(IFNULL(100-AVG(ROUND(IFNULL(case when b.err_type ='CE01' OR b.err_type='CE02' OR b.err_type='CE03' then
            a.nwb_bir_wt_dedu_point+ a.medcas_no_dedu_point+ a.gend_dedu_point+ a.brdy_dedu_point+ a.age_dedu_point+ a.med_pay_way_dedu_point+ a.oth_patn_bas_info_dedu_point+ a.dscg_way_dedu_point+ a.adm_time_dedu_point+ a.dscg_time_dedu_point+ a.act_ipt_days_dedu_point+ a.dscg_caty_dedu_point+ a.is_31_day_in_ipt_plan_dedu_point+ a.adm_way_dedu_point+ a.adm_caty_dedu_point+ a.refldept_caty_dedu_point+ a.dscg_main_diag_dedu_point+ a.main_diag_codg_dedu_point+ a.oth_diag_dedu_point+ a.oth_diag_codg_dedu_point+ a.main_oprn_oprt_name_dedu_point+ a.main_oprn_oprt_codg_dedu_point+ a.adm_cond_dedu_point+ a.palg_diag_dedu_point+ a.palg_diag_codg_dedu_point+ a.incs_heal_lv_dedu_point+ a.brn_damg_patn_coma_time_dedu_point+ a.oth_oprn_oprt_name_dedu_point+ a.oth_oprn_oprt_codg_dedu_point+ a.oprn_oprt_date_dedu_point+ a.otp_diag_dedu_point+ a.otp_diag_dise_codg_dedu_point+ a.anst_way_dedu_point+ a.oth_trt_info_dedu_point+ a.sumfee_dedu_point+ a.oth_ast_info_dedu_point
            else 0 end ,0),2) ),0),2) as fullScore,

            ROUND( IFNULL(100-AVG(ROUND( IFNULL(case when b.err_type='LE01' OR b.err_type='LE05' or b.err_type='LE03' then

            a.nwb_bir_wt_dedu_point+ a.medcas_no_dedu_point+ a.gend_dedu_point+ a.brdy_dedu_point+ a.age_dedu_point+ a.med_pay_way_dedu_point+ a.oth_patn_bas_info_dedu_point+ a.dscg_way_dedu_point+ a.adm_time_dedu_point+ a.dscg_time_dedu_point+ a.act_ipt_days_dedu_point+ a.dscg_caty_dedu_point+ a.is_31_day_in_ipt_plan_dedu_point+ a.adm_way_dedu_point+ a.adm_caty_dedu_point+ a.refldept_caty_dedu_point+ a.dscg_main_diag_dedu_point+ a.main_diag_codg_dedu_point+ a.oth_diag_dedu_point+ a.oth_diag_codg_dedu_point+ a.main_oprn_oprt_name_dedu_point+ a.main_oprn_oprt_codg_dedu_point+ a.adm_cond_dedu_point+ a.palg_diag_dedu_point+ a.palg_diag_codg_dedu_point+ a.incs_heal_lv_dedu_point+ a.brn_damg_patn_coma_time_dedu_point+ a.oth_oprn_oprt_name_dedu_point+ a.oth_oprn_oprt_codg_dedu_point+ a.oprn_oprt_date_dedu_point+ a.otp_diag_dedu_point+ a.otp_diag_dise_codg_dedu_point+ a.anst_way_dedu_point+ a.oth_trt_info_dedu_point+ a.sumfee_dedu_point+ a.oth_ast_info_dedu_point

            else 0 end ,0) , 2)) ,0) ,2) as logicScore,
            <if test='dimensionalityColumn.isChoose=="" or dimensionalityColumn.isChoose==null'>
                a.SETTLE_LIST_ID as id,
            </if>

            <if test='dimensionalityColumn.outHosDept=="1"'>
                d.CODE,d.`NAME`,
            </if>
            <if test='dimensionalityColumn.doctor=="1"'>
                c.B25C,
                c.B25N,
            </if>
            <if test='dimensionalityColumn.drg=="1"'>
                c.C03C,
                c.C04N,
            </if>
            c.HOSPITAL_ID,
            '1' as joint2
            FROM
            som_hi_invy_bas_info c
            left join som_setl_invy_qlt_dedu_point_detl a on c.id=A.SETTLE_LIST_ID
            LEFT JOIN som_setl_invy_chk_err_rcd b on a.SETTLE_LIST_ID=b.SETTLE_LIST_ID

            <if test='dimensionalityColumn.outHosDept=="1" or dimensionalityColumn.inHosDept=="1"'>
                left join som_dept d on d.CODE=c.B13C or d.CODE=c.B16C
            </if>
            where a.settle_list_id IN (
            SELECT
            id
            FROM
            som_init_hi_setl_invy_med_fee_info
            <where>
                active_flag = 1
                <if test="inStartTime!=null and inStartTime!='' and inEndTime!=null and inEndTime!=''">
                    AND c.B12 BETWEEN #{inStartTime,jdbcType=VARCHAR} and CONCAT(#{inEndTime,jdbcType=VARCHAR},' 23:59:59')
                </if>
                <if test="begnDate!=null and begnDate!='' and expiDate!=null and expiDate!=''">
                    AND c.B15 BETWEEN #{begnDate,jdbcType=VARCHAR} and CONCAT(#{expiDate,jdbcType=VARCHAR},' 23:59:59')
                </if>
                <if test="seStartTime!=null and seStartTime!='' and seEndTime!=null and seEndTime!=''">
                    AND c.D37 BETWEEN #{seStartTime,jdbcType=VARCHAR} and CONCAT(#{seEndTime,jdbcType=VARCHAR},' 23:59:59')
                </if>
            </where>

            )
            <if test='dimensionalityColumn.isChoose=="" or dimensionalityColumn.isChoose==null'>
                group by a.id, c.HOSPITAL_ID
                )k on k.id=a.ID AND k.HOSPITAL_ID=a.HOSPITAL_ID
            </if>
            <if test='dimensionalityColumn.outHosDept=="1" or dimensionalityColumn.inHosDept=="1"'>
                group by
                d.CODE,
                d.NAME, c.HOSPITAL_ID
                )k on k.CODE=e.CODE AND k.HOSPITAL_ID=a.HOSPITAL_ID
            </if>
            <if test='dimensionalityColumn.doctor=="1"'>
                group by
                c.B25C,
                c.B25N,c.HOSPITAL_ID
                )k on k.B25C=a.B25C AND k.HOSPITAL_ID=a.HOSPITAL_ID
            </if>
            <if  test='dimensionalityColumn.drg=="1"'>
                group by
                c.C03C,
                c.C04N,c.HOSPITAL_ID
                )k on k.C03C=a.C03C AND k.HOSPITAL_ID=a.HOSPITAL_ID
            </if>
        </sql>
</mapper>