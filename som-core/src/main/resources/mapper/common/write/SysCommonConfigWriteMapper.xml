<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.som.mapper.common.write.SysCommonConfigWriteMapper">

    <!-- 修改系统配置 -->
    <update id="modifyConfig">
        UPDATE som_sys_gen_cfg
        SET `value` = #{configValue,jdbcType=VARCHAR}
        WHERE id = #{id,jdbcType=INTEGER}
    </update>

    <!-- 修改分组记录状态 -->
    <update id="modifyGroupRecordStatus">
        UPDATE som_drg_grp_rcd
        SET active_flag = #{activeFlag,jdbcType=VARCHAR}
        <include refid="where_modifyGroupStatus" />
    </update>

    <!-- 修改分组日志状态 -->
    <update id="modifyGroupLogStatus">
        UPDATE som_drg_grper_intf_trns_log
        SET active_flag = #{activeFlag,jdbcType=VARCHAR}
        <include refid="where_modifyGroupStatus" />
    </update>

    <!-- 删除bus_key数据 -->
    <delete id="deleteBusKeyData">
        TRUNCATE TABLE som_drg_grp_info
    </delete>

    <update id="updateSysSettleListConfig">
        UPDATE som_setl_invy_upld_cfg SET ${configKey} = #{configValue}
        WHERE  FIXMEDINS_CODE = #{hospitalId,jdbcType=VARCHAR}
    </update>

    <!-- 修改系统配置 -->
    <update id="modifyConfigByKeyType">
        UPDATE som_sys_gen_cfg
        SET `value` = #{configValue,jdbcType=VARCHAR}
        WHERE `key` = #{configKey,jdbcType=VARCHAR}
        AND type = #{type,jdbcType=VARCHAR}
    </update>

    <!-- 修改分组数据状态条件 -->
    <sql id="where_modifyGroupStatus">
        <where>
            <choose>
                <when test='type == "1"'>
                    dise_type = #{configValue,jdbcType=VARCHAR}
                </when>
                <when test='type == "2"'>
                    dise_type != #{configValue,jdbcType=VARCHAR}
                </when>
            </choose>
        </where>
    </sql>

</mapper>
