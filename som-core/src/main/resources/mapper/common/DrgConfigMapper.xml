<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.som.mapper.common.DrgConfigMapper">

    <select id="queryDrgConfig" resultType="com.my.som.vo.common.DipConfigVo">
        SELECT id, `key`, `value`, `type`, description, ym,insuplc_admdvs as insuplcAdmdvs
        FROM som_drg_gen_cfg a
        <where>
            <if test="configKey != null and configKey != ''">
                AND a.key like CONCAT('%', #{configKey,jdbcType=VARCHAR}, '%')
            </if>
<!--            <if test="configValue != null and configValue != ''">-->
<!--                AND a.value = #{configValue,jdbcType=VARCHAR}-->
<!--            </if>-->
            <if test="ym != null and ym != ''">
                AND a.ym= replace(substring(#{ym,jdbcType=VARCHAR},1,7),'-','')
            </if>
            <if test="insuplcAdmdvs != null and insuplcAdmdvs != ''">
                AND a.insuplc_admdvs= #{insuplcAdmdvs,jdbcType=VARCHAR}
            </if>
        </where>
    </select>

    <select id="queryDrgDefaultConfig" resultType="com.my.som.vo.common.DipConfigVo">
        SELECT id, `key`, `value`, `type`, description, ym,insuplc_admdvs as insuplcAdmdvs
        FROM som_drg_gen_cfg where (ym= '' or ym is null)

    </select>

    <!-- 查询drg城职城乡单价 -->
    <select id="selectDrgData" resultType="com.my.som.vo.common.DipConfigVo">
        SELECT id, `key`, `value`, `type`, description, ym
        FROM som_drg_gen_cfg a
        <where>
            <if test="ym != null and ym != ''">
                AND a.ym = replace(substring(#{ym,jdbcType=VARCHAR}, 1, 7), '-', '')
            </if>
            <if test="insuplcAdmdvs != null and insuplcAdmdvs != ''">
                AND a.insuplc_admdvs = #{insuplcAdmdvs,jdbcType=VARCHAR}
            </if>
        </where>
    </select>

    <update id="updateDrgConfig">
        UPDATE som_drg_gen_cfg
        <set>
            <if test="configKey != null and configKey != ''">
                `key` =#{configKey,jdbcType=VARCHAR},
            </if>
            <if test="configValue != null and configValue != ''">
                `value` = #{configValue,jdbcType=VARCHAR},
            </if>
            <if test="ym != null and ym != ''">
                ym =  replace(substring(#{ym,jdbcType=VARCHAR},1,7),'-',''),
            </if>
            <if test="description != null and description != ''">
                description = #{description,jdbcType=VARCHAR}
            </if>
        </set>
        WHERE `id` = #{id,jdbcType=VARCHAR}
    </update>

    <insert id="addDrgConfig">
        INSERT INTO som_drg_gen_cfg (`key`, `value`, `type`, `description`, `ym`, `insuplc_admdvs`)
        SELECT
        #{configKey,jdbcType=VARCHAR},
        #{configValue,jdbcType=VARCHAR},
        'PREDICTED_PRICE',
        #{description,jdbcType=VARCHAR},
        REPLACE(#{ym,jdbcType=VARCHAR}, '-', ''),
        #{insuplcAdmdvs,jdbcType=VARCHAR}


    </insert>

    <delete id="deleteDrg">
        DELETE
        FROM som_drg_gen_cfg
        WHERE ID = #{id}
    </delete>

    <delete id="deleteDip">
        DELETE
        FROM som_dip_gen_cfg
        WHERE ID = #{id}
    </delete>

    <update id="resetDipFee">
        UPDATE
        <if test="type==1">
            som_dip_sco   t1
        </if>
        <if test="type==2">
            som_drg_sco   t1
        </if>

        JOIN (
        SELECT
        a.SETTLE_LIST_ID,
        a.price,
        IFNULL( ROUND( SUM( a.sumfee ), 4 ), 0 ) AS sumfee,
        IFNULL(
        ROUND( SUM( a.totl_sco * a.price ), 2 ),
        ROUND( SUM( a.sumfee ), 4 )) AS forecastAmount,
        ROUND(
        IFNULL(
        ROUND( SUM( a.totl_sco * a.price ), 2 ),
        ROUND( SUM( a.sumfee ), 4 )) - IFNULL( SUM( a.sumfee ), 0 ),
        4
        ) AS forecastAmountDiff
        FROM
        (
        SELECT
        a.SETTLE_LIST_ID,
        a.sumfee, a.totl_sco,
        CASE
        WHEN a.insu_type = 1
        OR a.insu_type = 310 THEN#{czPrice ,jdbcType=VARCHAR}
        WHEN a.insu_type = 2
        OR a.insu_type = 390 THEN#{cxPrice ,jdbcType=VARCHAR}
        ELSE #{price ,jdbcType=VARCHAR}
        END AS price
        FROM
        <if test="type==1">
            som_dip_sco a
        </if>
        <if test="type==2">
            som_drg_sco a
        </if>
        WHERE  replace(a.ym,'-','')= #{ym ,jdbcType=VARCHAR}
        <if test="type==2">
        <if test="insuplcAdmdvs != null and insuplcAdmdvs != ''">
            AND a.insuplc_admdvs= #{insuplcAdmdvs,jdbcType=VARCHAR}
        </if>
        </if>
        ) a
        GROUP BY a.SETTLE_LIST_ID, a.price
        ) t2 ON t1.SETTLE_LIST_ID = t2.SETTLE_LIST_ID
        SET t1.forecast_fee = t2.forecastAmount,
        t1.price = t2.price,
        t1.profitloss = t2.forecastAmountDiff,
        t1.sumfee = t2.sumfee
        <where>
        <if test="lowTypeModifyFlag != null and lowTypeModifyFlag != '' and lowTypeModifyFlag= 'true'">
           and t1.dise_type != '2'
        </if>
        </where>
    </update>


    <select id="queryPrice" resultType="com.my.som.dto.common.PriceDto">
        select
        MAX( CASE WHEN `KEY` = 'CX_PRICE' THEN `VALUE` ELSE NULL END ) AS cxPrice,
        MAX( CASE WHEN `KEY` = 'CZ_PRICE' THEN `VALUE` ELSE NULL END ) AS czPrice,
        MAX( CASE WHEN `KEY` = 'PRICE' THEN `VALUE` ELSE NULL END ) AS price,
        ym
        FROM
        <if test="type==1">
        som_dip_gen_cfg
        </if>
        <if test="type==2">
           som_drg_gen_cfg
        </if>
        WHERE
        TYPE = 'PREDICTED_PRICE' and ym = #{ym,jdbcType=VARCHAR}
        <if test="type==2">
        <if test="insuplcAdmdvs != null and insuplcAdmdvs != ''">
            AND insuplc_admdvs= #{insuplcAdmdvs,jdbcType=VARCHAR}
        </if>
        </if>
        GROUP BY
        TYPE ,ym
    </select>

    <update id="updatePrice" >
        update
        <if test="type==1">
            som_dip_gen_cfg
        </if>
        <if test="type==2">
            som_drg_gen_cfg
        </if>
        SET value = CASE
        WHEN `key` = 'CX_PRICE' THEN #{cxPrice,jdbcType=VARCHAR}
        WHEN `key` = 'CZ_PRICE' THEN #{czPrice,jdbcType=VARCHAR}
        WHEN `key` = 'PRICE' THEN #{price,jdbcType=VARCHAR}
        END
        WHERE ym = #{ym,jdbcType=VARCHAR}
        <if test="insuplcAdmdvs != null and insuplcAdmdvs != ''">
            AND insuplc_admdvs= #{insuplcAdmdvs,jdbcType=VARCHAR}
        </if>
        AND `key` IN ('CX_PRICE', 'CZ_PRICE', 'PRICE');
    </update>

    <update id="updatePriceByMouth" >
        update
        <if test="type==1">
            som_dip_gen_cfg
        </if>
        <if test="type==2">
            som_drg_gen_cfg
        </if>
        SET value = #{configValue,jdbcType=VARCHAR}

        WHERE ym = #{ym,jdbcType=VARCHAR}
        AND  `key` = #{configKey,jdbcType=VARCHAR}
        <if test="type==2">
        <if test="insuplcAdmdvs != null and insuplcAdmdvs != ''">
            AND insuplc_admdvs= #{insuplcAdmdvs,jdbcType=VARCHAR}
        </if>
        </if>
    </update>

    <insert id="insertPriceByMouth">
        INSERT INTO
        <choose>
            <when test="type==1">
                som_dip_gen_cfg
            </when>
            <when test="type==2">
                som_drg_gen_cfg
            </when>
        </choose>
        (`key`, `value`, `type`, `description`, `ym`
        <if test="type==2">, `insuplc_admdvs` </if>)
        SELECT
        #{configKey,jdbcType=VARCHAR},
        #{configValue,jdbcType=VARCHAR},
        'PREDICTED_PRICE',
        #{description,jdbcType=VARCHAR},
        REPLACE(#{ym,jdbcType=VARCHAR}, '-', '')
        <if test="type==2">, #{insuplcAdmdvs,jdbcType=VARCHAR} </if>
    </insert>

</mapper>