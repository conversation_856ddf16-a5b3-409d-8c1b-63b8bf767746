<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.som.mapper.common.read.SysCommonConfigReadMapper">

    <!-- 查询系统通用配置 -->
    <select id="querySysCommonConfig" resultType="com.my.som.vo.common.SysCommonConfigVo">
        SELECT id, `key`, `value`, `type`, description
        FROM som_sys_gen_cfg a
        <where>
            <if test="configKey != null and configKey != ''">
                AND a.key like CONCAT('%',#{configKey,jdbcType=VARCHAR},'%')
            </if>
            <if test="configValue != null and configValue != ''">
                AND a.value = #{configValue,jdbcType=VARCHAR}
            </if>
        </where>
        ORDER BY `type`
    </select>



    <!-- 查询icd编码 -->
    <select id="queryICDCode" resultType="com.my.som.common.vo.ICDCodeVo">
        select a.ICD_TYPE AS icdType,
        a.icd_codg_ver AS icdCodgVer,
        a.icd_codg AS icdCodg,
        a.ICD_NAME AS icdName,
        a.crsp_icd_codg AS crspIcdCodg,
        a.crsp_icd_name AS crspIcdName,
        a.crsp_icd_codg_ver AS crspIcdCodgVer
        from som_codg_crsp a
        <where>
            <if test="icdCodgVer != null and icdCodgVer != ''">
                AND a.icd_codg_ver = #{icdCodgVer,jdbcType=VARCHAR}
            </if>
            <if test="crspIcdCodgVer != null and crspIcdCodgVer != ''">
                AND a.crsp_icd_codg_ver = #{crspIcdCodgVer,jdbcType=VARCHAR}
            </if>
        </where>
    </select>

    <select id="querySysSettleListConfig" resultType="java.util.Map">
        SELECT send_msg_id,
               mdtrt_hi_admdvs,
               insuplc_hi_admdvs,
               rec_sys_code,
               DEV_NO,
               DEV_SAFE_INFO,
               digsig_info,
               sign_type,
               intf_ver,
               OPTER_TYPE,
               opter_upld,
               opter_name_last,
               FIXMEDINS_CODE,
               FIXMEDINS_NAME,
               URL
        FROM som_setl_invy_upld_cfg
        WHERE FIXMEDINS_CODE = #{hospitalId,jdbcType=VARCHAR}
    </select>

    <select id="queryIcdCodeYB" resultType="com.my.som.common.vo.ICDCodeVo">
        SELECT
            code icdCodg,
            name icdName
        FROM som_icd_codg sic WHERE icd_codg_ver = '10'
    </select>

    <select id="queryIcdCodeGrey" resultType="com.my.som.common.vo.ICDCodeVo">
        SELECT
        icd_type as icdType,
        icd_codg as icdCodg,
        icd_name as icdName,
        crsp_icd_codg as crspIcdCodg,
        crsp_icd_name as crspIcdname
        FROM som_gray_crsp  WHERE active_flag = 1
    </select>
    <select id="queryFlag" resultType="string">
        SELECT `value`
        FROM som_sys_gen_cfg
        WHERE `key` = #{key,jdbcType=VARCHAR}
        and `type` = #{type,jdbcType=VARCHAR}
    </select>

    <select id="queryNonGroupsList" parameterType="String" resultType="com.my.som.vo.medicalQuality.NonGroupVo">
    SELECT code ,name , type
    FROM som_non_group
    WHERE active_flag ='1'
    </select>
    <select id="queryTCMMainList" resultType="com.my.som.common.vo.ICDCodeVo">
        SELECT
        disease_classification_code AS icdCodg,
        GROUP_CONCAT(disease_classification_name )AS icdName
        FROM
        som_tcm_main_disease_diagnosis
        WHERE
        vali_flag = '1'
        GROUP BY disease_classification_code
    </select>

    <select id="queryTCMPrincipaList" resultType="com.my.som.common.vo.ICDCodeVo">
        SELECT
        classification_code AS icdCodg,
        GROUP_CONCAT(classification_name )AS icdName
        FROM
        som_tcm_principal_diagnosis
        WHERE
        vali_flag = '1'
        GROUP BY classification_code
    </select>
</mapper>

