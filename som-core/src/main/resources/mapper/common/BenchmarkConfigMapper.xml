<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.som.mapper.common.BenchmarkConfigMapper">
    <select id="selectDip" resultType="com.my.som.vo.common.BenchmarkConfigVo">
        select DISTINCT
        <choose>
            <when test="dto.queryType == 1">
                a.dip_codg as dipCodg,
                a.DIP_NAME as dipName,
                a.is_used_asst_list AS usedAsstList,
                a.asst_list_age_grp AS asstListAgeGrp,
                a.asst_list_dise_sev_deg AS asstListDiseSevDeg,
                a.asst_list_tmor_sev_deg AS asstListTmorSevDeg,
                IFNULL(ROUND(convert(AES_DECRYPT(UNHEX(a.dip_standard_inpf),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY})
                using utf8),2),0) AS dipStandardCost,
                IFNULL(ROUND(convert(AES_DECRYPT(UNHEX(a.dip_standard_avg_fee_same_lv),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY})
                using utf8),2),0) AS dipStandardCostLevel,
                IFNULL(ROUND(convert(AES_DECRYPT(UNHEX(a.dip_standard_ipt_days_same_lv),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY})
                using utf8),2),0) AS dipStandardDaysLevel,
                IFNULL(ROUND(convert(AES_DECRYPT(UNHEX(a.drug_ratio),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY})
                using utf8),2),0) AS dipDrugRate,
                IFNULL(ROUND(convert(AES_DECRYPT(UNHEX(a.consum_ratio),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY})
                using utf8),2),0) AS dipComsumableRate,
                a.STANDARD_YEAR as standardYear,
                a.refer_sco AS basSco,
                a.uplmt_mag AS uplmtMag,
                a.lowlmt_mag AS lowlmtMag,
                CONCAT(ROUND(IFNULL(ROUND(convert(AES_DECRYPT(UNHEX(a.dip_standard_avg_fee_same_lv),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY})
                using utf8),2),0) * a.lowlmt_mag,2), '-',
                ROUND(IFNULL(ROUND(convert(AES_DECRYPT(UNHEX(a.dip_standard_avg_fee_same_lv),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY})
                using utf8),2),0) * a.uplmt_mag, 2)) AS recommendRange,
                CONCAT(ROUND(IFNULL(ROUND(convert(AES_DECRYPT(UNHEX(a.dip_standard_avg_fee_same_lv),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY})
                using utf8),2),0) * a.lowlmt_mag,2), '-',
                ROUND(IFNULL(ROUND(convert(AES_DECRYPT(UNHEX(a.dip_standard_avg_fee_same_lv),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY})
                using utf8),2),0) * a.uplmt_mag, 2)) AS accurateRange
                FROM som_dip_standard a
                left join som_dip_supe_ultra_low_bind b
                ON a.dip_codg = b.CODE
                AND a.is_used_asst_list = b.is_used_asst_list
                AND a.asst_list_age_grp = b.asst_list_age_grp
                AND a.asst_list_dise_sev_deg = b.asst_list_dise_sev_deg
                AND a.asst_list_tmor_sev_deg = b.asst_list_tmor_sev_deg
                AND a.STANDARD_YEAR = b.YEAR
            </when>
            <when test="dto.queryType == 3">
                a.drg_codg as drgCodg,
                a.DRG_NAME as drgName,
                IFNULL(ROUND(convert(AES_DECRYPT(UNHEX(a.standard_avg_fee),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY})
                using utf8),2),0) AS drgStandardCost,
                IFNULL(ROUND(convert(AES_DECRYPT(UNHEX(a.standard_ave_hosp_day),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY})
                using utf8),2),0) AS drgStandardDays,
                a.refer_sco AS basSco,
                a.uplmt_mag AS uplmtMag,
                a.lowlmt_mag AS lowlmtMag,
                a.STANDARD_YEAR as standardYear,
                CONCAT(ROUND(IFNULL(ROUND(convert(AES_DECRYPT(UNHEX(a.standard_avg_fee),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY})
                using utf8),2),0) * a.lowlmt_mag,2), '-',
                ROUND(IFNULL(ROUND(convert(AES_DECRYPT(UNHEX(a.standard_avg_fee),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY})
                using utf8),2),0) * a.uplmt_mag, 2)) AS recommendRange,
                CONCAT(ROUND(IFNULL(ROUND(convert(AES_DECRYPT(UNHEX(a.standard_avg_fee),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY})
                using utf8),2),0) * a.lowlmt_mag,2), '-',
                ROUND(IFNULL(ROUND(convert(AES_DECRYPT(UNHEX(a.standard_avg_fee),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY})
                using utf8),2),0) * a.uplmt_mag, 2)) AS accurateRange,
                IFNULL(ROUND(convert(AES_DECRYPT(UNHEX(a.all_avg_fee),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY})
                using utf8),2),0) AS allAvgFee
                FROM som_drg_standard a
                left join som_drg_supe_ultra_low_bind b
                on a.drg_codg = b.CODE AND a.STANDARD_YEAR = b.YEAR
            </when>
        </choose>
        <where>
            a.ACTIVE_FLAG=1
            <if test="dto.standardYear != null and dto.standardYear != ''">
                AND a.STANDARD_YEAR = #{dto.standardYear,jdbcType=VARCHAR}
            </if>
            <if test="dto.dipCodg != null and dto.dipCodg != ''">
                AND a.dip_codg = #{dto.dipCodg,jdbcType=VARCHAR}
            </if>
            <if test="dto.drgCodg != null and dto.drgCodg != ''">
                AND a.drg_codg = #{dto.drgCodg,jdbcType=VARCHAR}
            </if>
            <if test="dto.hospitalId != null and dto.hospitalId != ''">
                AND a.HOSPITAL_ID = #{dto.hospitalId}
            </if>
        </where>
    </select>

    <!-- 查询上传组生成费用区间 -->
    <select id="queryGenerateRatioRange" resultType="com.my.som.vo.common.BenchmarkConfigVo">
        SELECT a.year,
        a.code,
        a.name,
        a.is_used_asst_list AS usedAsstList,
        a.asst_list_age_grp AS asstListAgeGrp,
        a.asst_list_dise_sev_deg AS asstListDiseSevDeg,
        a.asst_list_tmor_sev_deg AS asstListTmorSevDeg,
        a.high_fee AS highFee,
        a.min_fee AS minFee,
        a.stsb_fee AS accurateRange,
        a.type,
        a.hospital_id AS hospitalId,
        a.active_flag
        FROM som_dip_supe_ultra_low_bind a
        <where>
            <if test="standardYear != null and standardYear != ''">
                AND a.year = #{standardYear,jdbcType=VARCHAR}
            </if>
            <if test="queryType != null and queryType != ''">
                AND a.type = #{queryType,jdbcType=VARCHAR}
            </if>
        </where>
    </select>


    <update id="update">
        update
        <choose>
            <when test="dto.queryType == 1">
                som_dip_standard
                SET dip_standard_inpf = HEX(AES_ENCRYPT(#{dto.dipStandardCost},'2#03@7.6%{=n+tjy&amp;4lk098/d5r.dewg')),
                dip_standard_avg_fee_same_lv = HEX(AES_ENCRYPT(#{dto.dipStandardCostLevel},'2#03@7.6%{=n+tjy&amp;4lk098/d5r.dewg')),
                refer_sco = #{dto.basSco,jdbcType=DOUBLE}
                WHERE STANDARD_YEAR=#{dto.standardYear} AND dip_codg=#{dto.dipCodg} AND HOSPITAL_ID = #{dto.hospitalId}
            </when>
            <when test="dto.queryType == 3">
                som_drg_standard
                SET standard_avg_fee=HEX(AES_ENCRYPT(#{dto.drgStandardCost},'2#03@7.6%{=n+tjy&amp;4lk098/d5r.dewg'))
                WHERE STANDARD_YEAR=#{dto.standardYear} AND drg_codg=#{dto.drgCodg} AND HOSPITAL_ID = #{dto.hospitalId}
            </when>
        </choose>
    </update>
    <delete id="delete">
        DELETE from
        <choose>
            <when test="dto.queryType == 1">
                som_dip_standard
                WHERE dip_codg=#{dto.dipCodg} AND STANDARD_YEAR=#{dto.standardYear} AND HOSPITAL_ID = #{dto.hospitalId}
            </when>
            <when test="dto.queryType == 3">
                som_drg_standard
                WHERE STANDARD_YEAR=#{dto.standardYear} AND drg_codg=#{dto.drgCodg} AND HOSPITAL_ID = #{dto.hospitalId}
            </when>
        </choose>
    </delete>
    <insert id="insertData">
        insert into
        <choose>
            <when test="dto.queryType == 1">
                som_dip_standard
                ( dip_codg, DIP_NAME, dip_standard_inpf, dip_standard_avg_fee_same_lv,STANDARD_YEAR,ACTIVE_FLAG )
                VALUES
                (
                #{dto.dipCodg},#{dto.dipName},HEX(AES_ENCRYPT(#{dto.dipStandardCost},'2#03@7.6%{=n+tjy&amp;4lk098/d5r.dewg')),
                HEX(AES_ENCRYPT(#{dto.dipStandardCostLevel},'2#03@7.6%{=n+tjy&amp;4lk098/d5r.dewg')),#{dto.standardYear},#{dto.activeFlag}
                )
            </when>
            <when test="dto.queryType == 3">
                som_drg_standard
                ( drg_codg,DRG_NAME,standard_avg_fee,STANDARD_YEAR,ACTIVE_FLAG)
                VALUES
                (
                #{dto.drgCodg},#{dto.drgName},HEX(AES_ENCRYPT(#{dto.drgStandardCost},'2#03@7.6%{=n+tjy&amp;4lk098/d5r.dewg')),
                #{dto.standardYear},#{dto.activeFlag}
                )
            </when>
        </choose>
    </insert>

    <!--查询床日支付标准-->
    <select id="selectDrgBedDiseStandard" parameterType="com.my.som.dto.dataHandle.DrgBedStandardDto"
            resultType="com.my.som.model.dataHandle.DrgBedStandard">
        select
        id, <!--id-->
        bed_type, <!--床日类型-->
        bed_dise_codg, <!--床日病种编码-->
        bed_dise_name, <!--床日病种名称-->
        medins_lv, <!--医院级别-->
        pay_std_fee, <!--支付标准费用-->
        bed_stand_val, <!--基准分值-->
        year, <!--标杆年度-->
        vali_flag <!--有效标志-->
        from drg_bed_standard
        where vali_flag = '1'
        and year = #{year}
        and medins_lv = #{medins_lv}
    </select>
</mapper>
