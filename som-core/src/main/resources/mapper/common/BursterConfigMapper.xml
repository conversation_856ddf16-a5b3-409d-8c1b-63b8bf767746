<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.som.mapper.common.BursterConfigMapper">
    <!-- 查询系统通用配置 -->
    <select id="queryData" resultType="com.my.som.vo.common.BursterConfigVo">
        SELECT a.ID as id,
               a.grper_ver as ver,
               a.grper_url_dip as url,
               a.ACTIVE_FLAG   as activeflag
        FROM som_grp_reqt_addr_info a
        <where>
            <if test="configKey != null and configKey != ''">
                AND a.grper_ver like CONCAT('%',#{configKey,jdbcType=VARCHAR},'%')
            </if>
            <if test="configValue != null and configValue != ''">
                AND a.grper_url_dip = #{configValue,jdbcType=VARCHAR}
            </if>
        </where>
    </select>

    <!-- 更新系统通用配置 -->
    <update id="update" >
        update som_grp_reqt_addr_info
        set grper_url_dip = #{configValue,jdbcType=VARCHAR}
        WHERE grper_ver =#{configKey,jdbcType=VARCHAR}
    </update>
</mapper>