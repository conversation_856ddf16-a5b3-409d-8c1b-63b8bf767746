<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.som.mapper.common.DipConfigMapper">
    <!-- 查询系统通用配置 -->
    <select id="queryDipConfig" resultType="com.my.som.vo.common.DipConfigVo">
        SELECT id, `key`, `value`, `type`, description, ym
        FROM som_dip_gen_cfg a
        <where>
            <if test="configKey != null and configKey != ''">
                AND a.key like CONCAT('%', #{configKey,jdbcType=VARCHAR}, '%')
            </if>
<!--            <if test="configValue != null and configValue != ''">-->
<!--                AND a.value = #{configValue,jdbcType=VARCHAR}-->
<!--            </if>-->
            <if test="ym != null and ym != ''">
                AND a.ym =  replace(substring(#{ym,jdbcType=VARCHAR},1,7),'-','')
            </if>
        </where>
    </select>
    <select id="queryDipDefaultConfig" resultType="com.my.som.vo.common.DipConfigVo">
        SELECT id, `key`, `value`, `type`, description, ym
        FROM som_dip_gen_cfg a where  (ym= '' or ym is null)

    </select>


    <!-- 更新系统通用配置 -->
    <update id="updateDipConfig">
        update som_dip_gen_cfg
        <set>
            <if test="configKey != null and configKey != ''">
                `key` =#{configKey,jdbcType=VARCHAR},
            </if>
            <if test="configValue != null and configValue != ''">
                `value` = #{configValue,jdbcType=VARCHAR},
            </if>
            <if test="ym != null and ym != ''">
                ym =  replace(substring(#{ym,jdbcType=VARCHAR},1,7),'-',''),
            </if>
            <if test="description != null and description != ''">
                description = #{description,jdbcType=VARCHAR}
            </if>
        </set>
        WHERE `id` = #{id,jdbcType=VARCHAR}
    </update>

    <!--新增dip配置-->
    <insert id="addDipConfig">
        insert into som_dip_gen_cfg (`key`, `value`, `type`, description, ym)
        values (#{configKey,jdbcType=VARCHAR}, #{configValue,jdbcType=VARCHAR}, 'PREDICTED_PRICE',
                #{description,jdbcType=VARCHAR},
        replace(#{ym,jdbcType=VARCHAR},'-',''))
    </insert>

    <!-- 查询dip城职城乡单价 -->
    <select id="selectData" resultType="com.my.som.vo.common.DipConfigVo">
        SELECT id, `key`, `value`, `type`, description,ym
        FROM som_dip_gen_cfg a
        <where>
            <if test="ym != null and ym != ''">
                AND a.ym =  replace(substring(#{ym,jdbcType=VARCHAR},1,7),'-','')
            </if>
        </where>
    </select>

    <!-- 查询当前医院编码-->
    <select id="selectHospitalId" resultType="java.lang.String">
        SELECT MAX(CASE WHEN `key` = 'HOSPITAL_ID' THEN `VALUE` ELSE NULL END) AS HOSPITALID
        FROM som_sys_gen_cfg
    </select>

    <!-- 查询当前医院等级-->
    <select id="selectHospitalLevel" resultType="java.lang.String">
        select hosp_lv
        from som_hosp_info
        WHERE HOSPITAL_ID = #{s}
    </select>

    <select id="selectCoreGroupData" resultType="com.my.som.vo.pregroup.CoreGroupVo">
        SELECT a.dip_codg               as dipCodg,
               a.DIP_NAME               as dipName,
               a.asst_list_age_grp      as asstListAgeGrp,
               a.asst_list_dise_sev_deg as asstListDiseSevDeg,
               a.asst_list_tmor_sev_deg as asstListTmorSevDeg,
               a.auxiliary_burn as auxiliaryBurn,
               a.dip_diag_codg          as diagnoseCode,
               a.dip_diag_name          as diagnoseName,
               a.dip_oprt_codg          as oprnOprtCode,
               a.dip_oprt_name          as oprnOprtName,
               '1'                      as groupStatus,
               b.high_fee               as highFee,
               b.min_fee                as minFee,
               a.refer_sco              as refer_sco,
               a.adjm_cof               as adjm_cof,
               IFNULL(CONVERT(AES_DECRYPT(UNHEX(a.dip_standard_avg_fee_same_lv),
                                          ${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) USING utf8),
                      0)                AS dipAvgCostLevel,
               IFNULL(CONVERT(AES_DECRYPT(UNHEX(a.dip_standard_inpf),
                                          ${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) USING utf8),
                      0)                AS dipAvgCost,
               a.uplmt_mag              as uplmtMag
        FROM som_dip_standard a
                 LEFT JOIN som_dip_supe_ultra_low_bind b
                           ON a.dip_codg = b.CODE
                               AND a.asst_list_age_grp = b.asst_list_age_grp
                               AND a.asst_list_dise_sev_deg = b.asst_list_dise_sev_deg
                               AND a.asst_list_tmor_sev_deg = b.asst_list_tmor_sev_deg
                               AND a.auxiliary_burn = b.auxiliary_burn
                               AND a.STANDARD_YEAR = b.YEAR
        WHERE a.dip_diag_codg = SUBSTR(#{jbdm}, 1, 5)
          AND a.STANDARD_YEAR = SUBSTR(#{standYear}, 1, 4)
    </select>
</mapper>