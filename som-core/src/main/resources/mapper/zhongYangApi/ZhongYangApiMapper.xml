<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.som.mapper.zhongYangApi.ZhongYangApiMapper">
    <insert id="insertBaseInfo" parameterType="map" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO som_setl_invy_bas_info (
            unique_id,
            mdtrt_sn,
            mdtrt_id,
            psn_no,
            patn_ipt_cnt,
            ipt_no,
            medcasno,
            psn_name,
            gend,
            brdy,
            age,
            ntly,
            nwb_age,
            nwb_bir_wt,
            nwb_adm_wt,
            birplc,
            napl,
            naty,
            certno,
            prfs,
            mrg_stas,
            curr_addr_poscode,
            curr_addr,
            psn_tel,
            resd_addr_prov,
            resd_addr_city,
            resd_addr_coty,
            resd_addr_subd,
            resd_addr_vil,
            resd_addr_housnum,
            resd_addr_poscode,
            resd_addr,
            empr_tel,
            empr_poscode,
            empr_addr,
            coner_tel,
            coner_name,
            coner_addr,
            coner_rlts_code,
            adm_way_name,
            adm_way_code,
            trt_type_name,
            trt_type,
            adm_caty,
            adm_ward,
            adm_date,
            dscg_date,
            dscg_caty,
            refldept_dept,
            dscg_ward,
            ipt_days,
            drug_dicm_flag,
            dicm_drug_name,
            die_autp_flag,
            abo_code,
            abo_name,
            rh_code,
            rh_name,
            die_flag,
            deptdrt_name,
            deptdrt_code,
            chfdr_name,
            chfdr_code,
            atddr_name,
            atddr_code,
            chfpdr_name,
            chfpdr_code,
            ipt_dr_name,
            ipt_dr_code,
            resp_nurs_name,
            resp_nurs_code,
            train_dr_name,
            train_dr_code,
            intn_dr_name,
            intn_dr_code,
            codr_name,
            codr_code,
            qltctrl_dr_name,
            qltctrl_dr_code,
            qltctrl_nurs_name,
            qltctrl_nurs_code,
            medcas_qlt_name,
            medcas_qlt_code,
            qltctrl_date,
            dscg_way_name,
            dscg_way,
            acp_medins_code,
            acp_medins_name,
            dscg_31days_rinp_flag,
            dscg_31days_rinp_pup,
            damg_intx_ext_rea,
            damg_intx_ext_rea_disecode,
            brn_damg_bfadm_coma_dura,
            brn_damg_afadm_coma_dura,
            vent_used_dura,
            cnfm_date,
            patn_dise_diag_crsp,
            patn_dise_diag_crsp_code,
            ipt_patn_diag_inscp,
            ipt_patn_diag_inscp_code,
            dscg_trt_rslt,
            dscg_trt_rslt_code,
            medins_orgcode,
            aise,
            pote_intn_dr_name,
            hbsag,
            hcvab,
            hivab,
            resc_cnt,
            resc_succ_cnt,
            hosp_dise_fsttime,
            hif_pay_way_name,
            hif_pay_way_code,
            med_fee_paymtd_name,
            medfee_paymtd_code,
            selfpay_amt,
            medfee_sumamt,
            ordn_med_servfee,
            ordn_trt_oprt_fee,
            nurs_fee,
            com_med_serv_oth_fee,
            palg_diag_fee,
            lab_diag_fee,
            rdhy_diag_fee,
            clnc_dise_fee,
            nsrgtrt_item_fee,
            clnc_phys_trt_fee,
            rgtrt_trt_fee,
            anst_fee,
            oprn_fee,
            rhab_fee,
            tcm_trt_fee,
            wmfee,
            abtl_medn_fee,
            tcmpat_fee,
            tcmherb_fee,
            blo_fee,
            albu_fee,
            glon_fee,
            clotfac_fee,
            cyki_fee,
            exam_dspo_matl_fee,
            trt_dspo_matl_fee,
            oprn_dspo_matl_fee,
            oth_fee,
            vali_flag,
            fixmedins_code,
            offsite_med_treat,
            pre_exam,
            patn_rlts,
            nwb_adm_type,
            mul_nwb_bir_wt,
            mul_nwb_adm_wt,
            opsp_diag_caty,
            opsp_mdtrt_date,
            spga_nurscare_days,
            lv1_nurscare_days,
            scd_nurscare_days,
            lv3_nurscare_days,
            otp_wm_diag,
            otp_wm_diag_dise_code,
            otp_tcm_diag,
            otp_tcm_diag_dise_code,
            bld_cat,
            bld_amt,
            bld_unt,
            extract_flag
        ) VALUES
              (
                  #{unique_id},
                  #{mdtrt_sn},
                  #{mdtrt_id},
                  #{psn_no},
                  #{patn_ipt_cnt},
                  #{ipt_no},
                  #{medcasno},
                  #{psn_name},
                  #{gend},
                  #{brdy},
                  #{age},
                  #{ntly},
                  #{nwb_age},
                  #{nwb_bir_wt},
                  #{nwb_adm_wt},
                  #{birplc},
                  #{napl},
                  #{naty},
                  #{certno},
                  #{prfs},
                  #{mrg_stas},
                  #{curr_addr_poscode},
                  #{curr_addr},
                  #{psn_tel},
                  #{resd_addr_prov},
                  #{resd_addr_city},
                  #{resd_addr_coty},
                  #{resd_addr_subd},
                  #{resd_addr_vil},
                  #{resd_addr_housnum},
                  #{resd_addr_poscode},
                  #{resd_addr},
                  #{empr_tel},
                  #{empr_poscode},
                  #{empr_addr},
                  #{coner_tel},
                  #{coner_name},
                  #{coner_addr},
                  #{coner_rlts_code},
                  #{adm_way_name},
                  #{adm_way_code},
                  #{trt_type_name},
                  #{trt_type},
                  #{adm_caty},
                  #{adm_ward},
                  #{adm_date},
                  #{dscg_date},
                  #{dscg_caty},
                  #{refldept_dept},
                  #{dscg_ward},
                  #{ipt_days},
                  #{drug_dicm_flag},
                  #{dicm_drug_name},
                  #{die_autp_flag},
                  #{abo_code},
                  #{abo_name},
                  #{rh_code},
                  #{rh_name},
                  #{die_flag},
                  #{deptdrt_name},
                  #{deptdrt_code},
                  #{chfdr_name},
                  #{chfdr_code},
                  #{atddr_name},
                  #{atddr_code},
                  #{chfpdr_name},
                  #{chfpdr_code},
                  #{ipt_dr_name},
                  #{ipt_dr_code},
                  #{resp_nurs_name},
                  #{resp_nurs_code},
                  #{train_dr_name},
                  #{train_dr_code},
                  #{intn_dr_name},
                  #{intn_dr_code},
                  #{codr_name},
                  #{codr_code},
                  #{qltctrl_dr_name},
                  #{qltctrl_dr_code},
                  #{qltctrl_nurs_name},
                  #{qltctrl_nurs_code},
                  #{medcas_qlt_name},
                  #{medcas_qlt_code},
                  #{qltctrl_date},
                  #{dscg_way_name},
                  #{dscg_way},
                  #{acp_medins_code},
                  #{acp_medins_name},
                  #{dscg_31days_rinp_flag},
                  #{dscg_31days_rinp_pup},
                  #{damg_intx_ext_rea},
                  #{damg_intx_ext_rea_disecode},
                  #{brn_damg_bfadm_coma_dura},
                  #{brn_damg_afadm_coma_dura},
                  #{vent_used_dura},
                  #{cnfm_date},
                  #{patn_dise_diag_crsp},
                  #{patn_dise_diag_crsp_code},
                  #{ipt_patn_diag_inscp},
                  #{ipt_patn_diag_inscp_code},
                  #{dscg_trt_rslt},
                  #{dscg_trt_rslt_code},
                  #{medins_orgcode},
                  #{aise},
                  #{pote_intn_dr_name},
                  #{hbsag},
                  #{hcvab},
                  #{hivab},
                  #{resc_cnt},
                  #{resc_succ_cnt},
                  #{hosp_dise_fsttime},
                  #{hif_pay_way_name},
                  #{hif_pay_way_code},
                  #{med_fee_paymtd_name},
                  #{medfee_paymtd_code},
                  #{selfpay_amt},
                  #{medfee_sumamt},
                  #{ordn_med_servfee},
                  #{ordn_trt_oprt_fee},
                  #{nurs_fee},
                  #{com_med_serv_oth_fee},
                  #{palg_diag_fee},
                  #{lab_diag_fee},
                  #{rdhy_diag_fee},
                  #{clnc_dise_fee},
                  #{nsrgtrt_item_fee},
                  #{clnc_phys_trt_fee},
                  #{rgtrt_trt_fee},
                  #{anst_fee},
                  #{oprn_fee},
                  #{rhab_fee},
                  #{tcm_trt_fee},
                  #{wmfee},
                  #{abtl_medn_fee},
                  #{tcmpat_fee},
                  #{tcmherb_fee},
                  #{blo_fee},
                  #{albu_fee},
                  #{glon_fee},
                  #{clotfac_fee},
                  #{cyki_fee},
                  #{exam_dspo_matl_fee},
                  #{trt_dspo_matl_fee},
                  #{oprn_dspo_matl_fee},
                  #{oth_fee},
                  #{vali_flag},
                  #{fixmedins_code},
                  #{offsite_med_treat},
                  #{pre_exam},
                  #{patn_rlts},
                  #{nwb_adm_type},
                  #{mul_nwb_bir_wt},
                  #{mul_nwb_adm_wt},
                  #{opsp_diag_caty},
                  #{opsp_mdtrt_date},
                  #{spga_nurscare_days},
                  #{lv1_nurscare_days},
                  #{scd_nurscare_days},
                  #{lv3_nurscare_days},
                  #{otp_wm_diag},
                  #{otp_wm_diag_dise_code},
                  #{otp_tcm_diag},
                  #{otp_tcm_diag_dise_code},
                  #{bld_cat},
                  #{bld_amt},
                  #{bld_unt},
                  #{extract_flag}
                 )

    </insert>

    <insert id="insertDiagInfo"  parameterType="java.util.List">
        INSERT INTO som_diag_info (
        unique_id,
        palg_no,
        ipt_patn_disediag_type_code,
        disediag_type,
        maindiag_flag,
        diag_code,
        diag_name,
        inhosp_diag_code,
        inhosp_diag_name,
        adm_dise_cond_name,
        adm_dise_cond_code,
        adm_cond,
        adm_cond_code,
        high_diag_evid,
        bkup_deg,
        bkup_deg_code,
        vali_flag,
        ipt_medcas_hmpg_sn,
        mdtrt_sn,
        fixmedins_code,
        di20_id
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.unique_id},
            #{item.palg_no},
            #{item.ipt_patn_disediag_type_code},
            #{item.disediag_type},
            #{item.maindiag_flag},
            #{item.diag_code},
            #{item.diag_name},
            #{item.inhosp_diag_code},
            #{item.inhosp_diag_name},
            #{item.adm_dise_cond_name},
            #{item.adm_dise_cond_code},
            #{item.adm_cond},
            #{item.adm_cond_code},
            #{item.high_diag_evid},
            #{item.bkup_deg},
            #{item.bkup_deg_code},
            #{item.vali_flag},
            #{item.ipt_medcas_hmpg_sn},
            #{item.mdtrt_sn},
            #{item.fixmedins_code},
            #{item.di20_id}
            )
        </foreach>
    </insert>

    <insert id="insertOprnInfo" parameterType="java.util.List">
        INSERT INTO som_oprn_rcd (
        unique_id,
        oprn_oprt_date,
        oprn_oprt_name,
        oprn_oprt_code,
        oprn_oprt_sn,
        oprn_lv_code,
        oprn_lv_name,
        oper_name,
        oper_code,
        asit1_name,
        asit2_name,
        sinc_heal_lv,
        sinc_heal_lv_code,
        anst_mtd_name,
        anst_mtd_code,
        anst_dr_name,
        anst_dr_code,
        oprn_oper_part,
        oprn_oper_part_code,
        oprn_con_time,
        anst_lv_name,
        anst_lv_code,
        oprn_patn_type,
        oprn_patn_type_code,
        main_oprn_flag,
        anst_asa_lv_code,
        anst_asa_lv_name,
        anst_medn_code,
        anst_medn_name,
        anst_medn_dos,
        unt,
        anst_begntime,
        anst_endtime,
        anst_copn_code,
        anst_copn_name,
        anst_copn_dscr,
        pacu_begntime,
        pacu_endtime,
        canc_oprn_flag,
        vali_flag,
        ipt_medcas_hmpg_sn,
        mdtrt_sn,
        oprn_oprt_begntime,
        oprn_oprt_endtime,
        di20_id
        ) VALUES
        <foreach collection="list" item="item"  separator=",">
            (
            #{item.unique_id},
            #{item.oprn_oprt_date},
            #{item.oprn_oprt_name},
            #{item.oprn_oprt_code},
            #{item.oprn_oprt_sn},
            #{item.oprn_lv_code},
            #{item.oprn_lv_name},
            #{item.oper_name},
            #{item.oper_code},
            #{item.asit1_name},
            #{item.asit2_name},
            #{item.sinc_heal_lv},
            #{item.sinc_heal_lv_code},
            #{item.anst_mtd_name},
            #{item.anst_mtd_code},
            #{item.anst_dr_name},
            #{item.anst_dr_code},
            #{item.oprn_oper_part},
            #{item.oprn_oper_part_code},
            #{item.oprn_con_time},
            #{item.anst_lv_name},
            #{item.anst_lv_code},
            #{item.oprn_patn_type},
            #{item.oprn_patn_type_code},
            #{item.main_oprn_flag},
            #{item.anst_asa_lv_code},
            #{item.anst_asa_lv_name},
            #{item.anst_medn_code},
            #{item.anst_medn_name},
            #{item.anst_medn_dos},
            #{item.unt},
            #{item.anst_begntime},
            #{item.anst_endtime},
            #{item.anst_copn_code},
            #{item.anst_copn_name},
            #{item.anst_copn_dscr},
            #{item.pacu_begntime},
            #{item.pacu_endtime},
            #{item.canc_oprn_flag},
            #{item.vali_flag},
            #{item.ipt_medcas_hmpg_sn},
            #{item.mdtrt_sn},
            #{item.oprn_oprt_begntime},
            #{item.oprn_oprt_endtime},
            #{item.di20_id}
            )
        </foreach>
    </insert>
    <insert id="insertIcuInfo" parameterType="java.util.List">
        INSERT INTO som_scs_cutd_info (
        unique_id,
        scs_cutd_ward_type,
        scs_cutd_inpool_time,
        scs_cutd_exit_time,
        scs_cutd_sum_dura,
        di20_id
        ) VALUES
        <foreach collection="list" item="item"  separator=",">
            (
            #{item.unique_id},
            #{item.scs_cutd_ward_type},
            #{item.scs_cutd_inpool_time},
            #{item.scs_cutd_exit_time},
            #{item.scs_cutd_sum_dura},
            #{item.di20_id}
            )
        </foreach>
    </insert>

    <insert id="insertPayInfo">
        INSERT INTO som_fund_pay_info (
        unique_id,
        fund_pay_type,
        fund_payamt,
        di20_id
        ) VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (
            #{item.uniqueId},
            #{item.fundPayType},
            #{item.fundPayamt},
            #{item.di20_id}
            )
        </foreach>
    </insert>

    <insert id="insertOpspInfo">
        INSERT INTO som_otp_crds_diag_info (
        unique_id,
        diag_name,
        diag_code,
        oprn_oprt_name,
        oprn_oprt_code,
        maindiag_flag,
        di20_id
        ) VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (
            #{item.uniqueId},
            #{item.diagName},
            #{item.diagCode},
            #{item.oprnOprtName},
            #{item.oprnOprtCode},
            #{item.maindiagFlag},
            #{item.di20Id}
            )
        </foreach>
    </insert>

    <insert id="insertChrigtmInfo">
        INSERT INTO som_chrg_item_info (
        unique_id,
        med_chrgitm,
        amt,
        claa_sumfee,
        clab_amt,
        fulamt_ownpay_amt,
        oth_amt,
        di20_id
        ) VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (
            #{item.uniqueId},
            #{item.medChrgitm},
            #{item.amt},
            #{item.claaSumfee},
            #{item.clabAmt},
            #{item.fulamtOwnpayAmt},
            #{item.othAmt},
            #{item.di20Id}
            )
        </foreach>
    </insert>

    <insert id="insertSetlInfo">
        INSERT INTO som_setl_info (
        unique_id,
        psn_no,
        mdtrt_id,
        setl_id,
        bill_code,
        bill_no,
        biz_sn,
        setl_begn_date,
        setl_end_date,
        medins_fill_dept,
        medins_fill_psn,
        hsorg,
        hsorg_opter,
        hi_paymtd,
        psn_selfpay,
        psn_ownpay,
        acct_pay,
        psn_cashpay,
        med_ins_fund,
        hi_no,
        hi_setl_lv,
        hi_type,
        sp_psn_type,
        insuplc,
        ipt_med_type,
        di20_id
        ) VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (
            #{item.uniqueId},
            #{item.psnNo},
            #{item.mdtrtId},
            #{item.setlId},
            #{item.billCode},
            #{item.billNo},
            #{item.bizSn},
            #{item.setlBegnDate},
            #{item.setlEndDate},
            #{item.medinsFillDept},
            #{item.medinsFillPsn},
            #{item.hsorg},
            #{item.hsorgOpter},
            #{item.hiPaymtd},
            #{item.psnSelfpay},
            #{item.psnOwnpay},
            #{item.acctPay},
            #{item.psnCashpay},
            #{item.medInsFund},
            #{item.hiNo},
            #{item.hiSetlLv},
            #{item.hiType},
            #{item.spPsnType},
            #{item.insuplc},
            #{item.iptMedType},
            #{item.di20Id}
            )
        </foreach>
    </insert>

    <insert id="insertBldInfo">
        INSERT INTO som_bld_info (
        unique_id,
        bld_cat,
        bld_amt,
        bld_unt,
        di20_id
        ) VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (
            #{item.uniqueId},
            #{item.bldCat},
            #{item.bldAmt},
            #{item.bldUnt},
            #{item.di20Id}
            )
        </foreach>
    </insert>

    <insert id="insertLogExtract">
        INSERT INTO som_log_extraction (
            extraction_start_time,
            extraction_end_time,
            extraction_count,
            extraction_status
        ) VALUES (
                     #{extractionStartTime},
                     #{extractionEndTime},
                     #{extractionCount},
                     #{extractionStatus}
                 )
    </insert>

    <update id="updateFlag">
        UPDATE som_setl_invy_bas_info
        SET extract_flag = '0'
        <where>
            dscg_date <![CDATA[>=]]> #{pat_out_time_start}
            AND adm_date <![CDATA[<=]]> #{pat_out_time_end}
            <if test="uniqueId != null">
                and unique_id = #{uniqueId}
            </if>
        </where>

    </update>


    <select id="getBaseInfo" resultType="com.my.som.vo.hisview.SetlBaseInfoVO">
        SELECT
            a.unique_id AS uniqueId,
            a.mdtrt_sn AS mdtrtSn,
            a.mdtrt_id AS mdtrtId,
            a.psn_no AS psnNo,
            a.patn_ipt_cnt AS patnIptCnt,
            a.ipt_no AS iptNo,
            a.medcasno AS medcasno,
            a.psn_name AS psnName,
            a.gend AS gend,
            a.brdy AS brdy,
            a.age AS age,
            a.ntly AS ntly,
            a.nwb_age AS nwbAge,
            a.nwb_bir_wt AS nwbBirWt,
            a.nwb_adm_wt AS nwbAdmWt,
            a.birplc AS birplc,
            a.napl AS napl,
            a.naty AS naty,
            a.certno AS certno,
            a.prfs AS prfs,
            a.mrg_stas AS mrgStas,
            a.curr_addr_poscode AS currAddrPoscode,
            a.curr_addr AS currAddr,
            a.psn_tel AS psnTel,
            a.resd_addr_prov AS resdAddrProv,
            a.resd_addr_city AS resdAddrCity,
            a.resd_addr_coty AS resdAddrCoty,
            a.resd_addr_subd AS resdAddrSubd,
            a.resd_addr_vil AS resdAddrVil,
            a.resd_addr_housnum AS resdAddrHousnum,
            a.resd_addr_poscode AS resdAddrPoscode,
            a.resd_addr AS resdAddr,
            a.empr_tel AS emprTel,
            a.empr_poscode AS emprPoscode,
            a.empr_addr AS emprAddr,
            a.coner_tel AS conerTel,
            a.coner_name AS conerName,
            a.coner_addr AS conerAddr,
            a.coner_rlts_code AS conerRltsCode,
            a.adm_way_name AS admWayName,
            a.adm_way_code AS admWayCode,
            a.trt_type_name AS trtTypeName,
            a.trt_type AS trtType,
            a.adm_caty AS admCaty,
            a.adm_ward AS admWard,
            a.adm_date AS admDate,
            a.dscg_date AS dscgDate,
            a.dscg_caty AS dscgCaty,
            a.refldept_dept AS refldeptDept,
            a.dscg_ward AS dscgWard,
            a.ipt_days AS iptDays,
            a.drug_dicm_flag AS drugDicmFlag,
            a.dicm_drug_name AS dicmDrugName,
            a.die_autp_flag AS dieAutpFlag,
            a.abo_code AS aboCode,
            a.abo_name AS aboName,
            a.rh_code AS rhCode,
            a.rh_name AS rhName,
            a.die_flag AS dieFlag,
            a.deptdrt_name AS deptdrtName,
            a.deptdrt_code AS deptdrtCode,
            a.chfdr_name AS chfdrName,
            a.chfdr_code AS chfdrCode,
            a.atddr_name AS atddrName,
            a.atddr_code AS atddrCode,
            a.chfpdr_name AS chfpdrName,
            a.chfpdr_code AS chfpdrCode,
            a.ipt_dr_name AS iptDrName,
            a.ipt_dr_code AS iptDrCode,
            a.resp_nurs_name AS respNursName,
            a.resp_nurs_code AS respNursCode,
            a.train_dr_name AS trainDrName,
            a.train_dr_code AS trainDrCode,
            a.intn_dr_name AS intnDrName,
            a.intn_dr_code AS intnDrCode,
            a.codr_name AS codrName,
            a.codr_code AS codrCode,
            a.qltctrl_dr_name AS qltctrlDrName,
            a.qltctrl_dr_code AS qltctrlDrCode,
            a.qltctrl_nurs_name AS qltctrlNursName,
            a.qltctrl_nurs_code AS qltctrlNursCode,
            a.medcas_qlt_name AS medcasQltName,
            a.medcas_qlt_code AS medcasQltCode,
            a.qltctrl_date AS qltctrlDate,
            a.dscg_way_name AS dscgWayName,
            a.dscg_way AS dscgWay,
            a.acp_medins_code AS acpMedinsCode,
            a.acp_medins_name AS acpMedinsName,
            a.dscg_31days_rinp_flag AS dscg31daysRinpFlag,
            a.dscg_31days_rinp_pup AS dscg31daysRinpPup,
            a.damg_intx_ext_rea AS damgIntxExtRea,
            a.damg_intx_ext_rea_disecode AS damgIntxExtReaDisecode,
            a.brn_damg_bfadm_coma_dura AS brnDamgBfadmComaDura,
            a.brn_damg_afadm_coma_dura AS brnDamgAfadmComaDura,
            a.vent_used_dura AS ventUsedDura,
            a.cnfm_date AS cnfmDate,
            a.patn_dise_diag_crsp AS patnDiseDiagCrsp,
            a.patn_dise_diag_crsp_code AS patnDiseDiagCrspCode,
            a.ipt_patn_diag_inscp AS iptPatnDiagInscp,
            a.ipt_patn_diag_inscp_code AS iptPatnDiagInscpCode,
            a.dscg_trt_rslt AS dscgTrtRslt,
            a.dscg_trt_rslt_code AS dscgTrtRsltCode,
            a.medins_orgcode AS medinsOrgcode,
            a.aise AS aise,
            a.pote_intn_dr_name AS poteIntnDrName,
            a.hbsag AS hbsag,
            a.hcvab AS hcvab,
            a.hivab AS hivab,
            a.resc_cnt AS rescCnt,
            a.resc_succ_cnt AS rescSuccCnt,
            a.hosp_dise_fsttime AS hospDiseFsttime,
            a.hif_pay_way_name AS hifPayWayName,
            a.hif_pay_way_code AS hifPayWayCode,
            a.med_fee_paymtd_name AS medFeePaymtdName,
            a.medfee_paymtd_code AS medfeePaymtdCode,
            a.selfpay_amt AS selfpayAmt,
            a.medfee_sumamt AS medfeeSumamt,
            a.ordn_med_servfee AS ordnMedServfee,
            a.ordn_trt_oprt_fee AS ordnTrtOprtFee,
            a.nurs_fee AS nursFee,
            a.com_med_serv_oth_fee AS comMedServOthFee,
            a.palg_diag_fee AS palgDiagFee,
            a.lab_diag_fee AS labDiagFee,
            a.rdhy_diag_fee AS rdhyDiagFee,
            a.clnc_dise_fee AS clncDiseFee,
            a.nsrgtrt_item_fee AS nsrgtrtItemFee,
            a.clnc_phys_trt_fee AS clncPhysTrtFee,
            a.rgtrt_trt_fee AS rgtrtTrtFee,
            a.anst_fee AS anstFee,
            a.oprn_fee AS oprnFee,
            a.rhab_fee AS rhabFee,
            a.tcm_trt_fee AS tcmTrtFee,
            a.wmfee AS wmfee,
            a.abtl_medn_fee AS abtlMednFee,
            a.tcmpat_fee AS tcmpatFee,
            a.tcmherb_fee AS tcmherbFee,
            a.blo_fee AS bloFee,
            a.albu_fee AS albuFee,
            a.glon_fee AS glonFee,
            a.clotfac_fee AS clotfacFee,
            a.cyki_fee AS cykiFee,
            a.exam_dspo_matl_fee AS examDspoMatlFee,
            a.trt_dspo_matl_fee AS trtDspoMatlFee,
            a.oprn_dspo_matl_fee AS oprnDspoMatlFee,
            a.oth_fee AS othFee,
            a.vali_flag AS valiFlag,
            a.fixmedins_code AS fixmedinsCode,
            a.offsite_med_treat AS offsiteMedTreat,
            a.pre_exam AS preExam,
            a.patn_rlts AS patnRlts,
            a.nwb_adm_type AS nwbAdmType,
            a.mul_nwb_bir_wt AS mulNwbBirWt,
            a.mul_nwb_adm_wt AS mulNwbAdmWt,
            a.opsp_diag_caty AS opspDiagCaty,
            a.opsp_mdtrt_date AS opspMdtrtDate,
            a.spga_nurscare_days AS spgaNurscareDays,
            a.lv1_nurscare_days AS lv1NurscareDays,
            a.scd_nurscare_days AS scdNurscareDays,
            a.lv3_nurscare_days AS lv3NurscareDays,
            a.otp_wm_diag AS otpWmDiag,
            a.otp_wm_diag_dise_code AS otpWmDiagDiseCode,
            a.otp_tcm_diag AS otpTcmDiag,
            a.otp_tcm_diag_dise_code AS otpTcmDiagDiseCode,
            a.bld_cat AS bldCat,
            a.bld_amt AS bldAmt,
            a.bld_unt AS bldUnt
        FROM
            ${baseTableName} a
        LEFT JOIN ${othTableName} b on a.unique_id = b.unique_id
        <where>
            <if test="startTime!= null and startTime!= ''">
                AND b.setl_end_date &gt;= TO_DATE(#{startTime} || '00:00:00', 'YYYY-MM-DD HH24:MI:SS')
            </if>
            <if test="endTime!= null and endTime!= ''">
                AND b.setl_end_date &lt;= TO_DATE(#{endTime} || '23:59:59', 'YYYY-MM-DD HH24:MI:SS')
            </if>
            <if test="medcasno != null and medcasno != ''">
                AND a.medcasno = #{medcasno}
            </if>
            <if test="mdtrtId != null and mdtrtId != ''">
                AND a.mdtrt_id = #{mdtrtId}
            </if>
            <if test="uniqueId != null and uniqueId != ''">
                AND a.unique_id = #{uniqueId}
            </if>
        </where>
    </select>

    <select id="getDiagInfo" resultType="com.my.som.vo.hisview.SetlDiagInfoVO">
        SELECT
            a.unique_id AS uniqueId,
            a.palg_no AS palgNo,
            a.ipt_patn_disediag_type_code AS iptPatnDisediagTypeCode,
            a.disediag_type AS disediagType,
            a.maindiag_flag AS maindiagFlag,
            a.diag_code AS diagCode,
            a.diag_name AS diagName,
            a.inhosp_diag_code AS inhospDiagCode,
            a.inhosp_diag_name AS inhospDiagName,
            a.adm_dise_cond_name AS admDiseCondName,
            a.adm_dise_cond_code AS admDiseCondCode,
            a.adm_cond AS admCond,
            a.adm_cond_code AS admCondCode,
            a.high_diag_evid AS highDiagEvid,
            a.bkup_deg AS bkupDeg,
            a.bkup_deg_code AS bkupDegCode,
            a.vali_flag AS valiFlag,
            a.ipt_medcas_hmpg_sn AS iptMedcasHmpgSn,
            a.mdtrt_sn AS mdtrtSn,
            a.fixmedins_code AS fixmedinsCode
        FROM
            ${hisViewDTO.othTableName} a
        <where>
            a.unique_id in (
            <foreach item="item" collection="uniqueIds" separator=",">
            #{item}
            </foreach>
            )
        </where>
    </select>

    <select id="getOprnInfo" resultType="com.my.som.vo.hisview.SetlOprnInfoVO">
        SELECT
            a.unique_id AS uniqueId,
            a.oprn_oprt_date AS oprnOprtDate,
            a.oprn_oprt_name AS oprnOprtName,
            a.oprn_oprt_code AS oprnOprtCode,
            a.oprn_oprt_sn AS oprnOprtSn,
            a.oprn_lv_code AS oprnLvCode,
            a.oprn_lv_name AS oprnLvName,
            a.oper_name AS operName,
            a.oper_code AS operCode,
            a.asit1_name AS asit1Name,
            a.asit2_name AS asit2Name,
            a.sinc_heal_lv AS sincHealLv,
            a.sinc_heal_lv_code AS sincHealLvCode,
            a.anst_mtd_name AS anstMtdName,
            a.anst_mtd_code AS anstMtdCode,
            a.anst_dr_name AS anstDrName,
            a.anst_dr_code AS anstDrCode,
            a.oprn_oper_part AS oprnOperPart,
            a.oprn_oper_part_code AS oprnOperPartCode,
            a.oprn_con_time AS oprnConTime,
            a.anst_lv_name AS anstLvName,
            a.anst_lv_code AS anstLvCode,
            a.oprn_patn_type AS oprnPatnType,
            a.oprn_patn_type_code AS oprnPatnTypeCode,
            a.main_oprn_flag AS mainOprnFlag,
            a.anst_asa_lv_code AS anstAsaLvCode,
            a.anst_asa_lv_name AS anstAsaLvName,
            a.anst_medn_code AS anstMednCode,
            a.anst_medn_name AS anstMednName,
            a.anst_medn_dos AS anstMednDos,
            a.unt AS unt,
            a.anst_begntime AS anstBegntime,
            a.anst_endtime AS anstEndtime,
            a.anst_copn_code AS anstCopnCode,
            a.anst_copn_name AS anstCopnName,
            a.anst_copn_dscr AS anstCopnDscr,
            a.pacu_begntime AS pacuBegntime,
            a.pacu_endtime AS pacuEndtime,
            a.canc_oprn_flag AS cancOprnFlag,
            a.vali_flag AS valiFlag,
            a.ipt_medcas_hmpg_sn AS iptMedcasHmpgSn,
            a.mdtrt_sn AS mdtrtSn,
            a.oprn_oprt_begntime AS oprnOprtBegntime,
            a.oprn_oprt_endtime AS oprnOprtEndtime
        FROM
        ${hisViewDTO.othTableName} a
        <where>
            a.unique_id in (
            <foreach item="item" collection="uniqueIds" separator=",">
                #{item}
            </foreach>
            )
        </where>
    </select>


    <select id="getOpspInfo" resultType="com.my.som.vo.hisview.SetlOpspInfoVO">
        SELECT
            a.unique_id AS uniqueId,
            a.diag_name AS diagName,
            a.diag_code AS diagCode,
            a.oprn_oprt_name AS oprnOprtName,
            a.oprn_oprt_code AS oprnOprtCode,
            a.maindiag_flag AS maindiagFlag
        FROM
        ${hisViewDTO.othTableName} a
        <where>
            a.unique_id in (
            <foreach item="item" collection="uniqueIds" separator=",">
                #{item}
            </foreach>
            )
        </where>
    </select>

    <select id="getIcuInfo" resultType="com.my.som.vo.hisview.SetlIcuInfoVO">
        SELECT
            a.unique_id AS uniqueId,
            a.scs_cutd_ward_type AS scsCutdWardType,
            a.scs_cutd_inpool_time AS scsCutdInpoolTime,
            a.scs_cutd_exit_time AS scsCutdExitTime,
            a.scs_cutd_sum_dura AS scsCutdSumDura
        FROM
        ${hisViewDTO.othTableName} a
        <where>
            a.unique_id in (
            <foreach item="item" collection="uniqueIds" separator=",">
                #{item}
            </foreach>
            )
        </where>
    </select>

    <select id="getPayInfo" resultType="com.my.som.vo.hisview.SetlPayInfoVO">
        SELECT
            a.unique_id AS uniqueId,
            a.fund_pay_type AS fundPayType,
            a.fund_payamt AS fundPayamt
        FROM
        ${hisViewDTO.othTableName} a
        <where>
            a.unique_id in (
            <foreach item="item" collection="uniqueIds" separator=",">
                #{item}
            </foreach>
            )
        </where>
    </select>

    <select id="getChrigtmInfo" resultType="com.my.som.vo.hisview.SetlChrigtmInfoVO">
        SELECT
            a.unique_id AS uniqueId,
            a.med_chrgitm AS medChrgitm,
            a.amt AS amt,
            a.claa_sumfee AS claaSumfee,
            a.clab_amt AS clabAmt,
            a.fulamt_ownpay_amt AS fulamtOwnpayAmt,
            a.oth_amt AS othAmt
        FROM
        ${hisViewDTO.othTableName} a
        <where>
            a.unique_id in (
            <foreach item="item" collection="uniqueIds" separator=",">
                #{item}
            </foreach>
            )
        </where>
    </select>

    <select id="getSetlInfo" resultType="com.my.som.vo.hisview.SetlInfoVO">
        SELECT
            a.unique_id AS uniqueId,
            a.psn_no AS psnNo,
            a.mdtrt_id AS mdtrtId,
            a.setl_id AS setlId,
            a.bill_code AS billCode,
            a.bill_no AS billNo,
            a.biz_sn AS bizSn,
            a.setl_begn_date AS setlBegnDate,
            a.setl_end_date AS setlEndDate,
            a.medins_fill_dept AS medinsFillDept,
            a.medins_fill_psn AS medinsFillPsn,
            a.hsorg AS hsorg,
            a.hsorg_opter AS hsorgOpter,
            a.hi_paymtd AS hiPaymtd,
            a.psn_selfpay AS psnSelfpay,
            a.psn_ownpay AS psnOwnpay,
            a.acct_pay AS acctPay,
            a.psn_cashpay AS psnCashpay,
            a.med_ins_fund AS medInsFund,
            a.hi_no AS hiNo,
            a.hi_setl_lv AS hiSetlLv,
            a.hi_type AS hiType,
            a.sp_psn_type AS spPsnType,
            a.insuplc AS insuplc,
            a.ipt_med_type AS iptMedType
        FROM
        ${hisViewDTO.othTableName} a
        <where>
            a.unique_id in (
            <foreach item="item" collection="uniqueIds" separator=",">
                #{item}
            </foreach>
            )
        </where>
    </select>

    <select id="getBldInfo" resultType="com.my.som.vo.hisview.SetlBldInfoVO">
        SELECT
            a.unique_id AS uniqueId,
            a.bld_cat AS bldCat,
            a.bld_amt AS bldAmt,
            a.bld_unt AS bldUnt
        FROM
        ${hisViewDTO.othTableName} a
        <where>
            a.unique_id in (
            <foreach item="item" collection="uniqueIds" separator=",">
                #{item}
            </foreach>
            )
        </where>
    </select>
    <select id="queryId" resultType="java.lang.String">
        select id from som_setl_invy_bas_info where  unique_id = #{uniqueId}
    </select>

</mapper>
