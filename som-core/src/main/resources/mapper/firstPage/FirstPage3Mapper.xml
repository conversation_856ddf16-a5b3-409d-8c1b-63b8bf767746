<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.som.mapper.firstpage.FirstPage3Mapper">
    <sql id="timeCondition">
        <if test="begnDate!=null and expiDate!=null and begnDate!='' and expiDate!='' ">
            substr(updt_time,1,10) BETWEEN #{begnDate,jdbcType=VARCHAR} AND CONCAT(#{expiDate,jdbcType=VARCHAR},'23:59:59')
        </if>
    </sql>
    <sql id="hosIdCondition">
        <if test="hospitalId!=null and hospitalId!='' ">
            AND HOSPITAL_ID=#{hospitalId,jdbcType=VARCHAR}
        </if>
    </sql>
    <sql id="issueCondition">
        <if test="ym != null and ym != ''">
            <!--出院-->
            <if test="type == 1 ">
                AND substr( a.B15, 1, 7 )= #{ym,jdbcType=VARCHAR}
            </if>
            <!--结算-->
            <if test="type == 2 ">
                AND substr( a.D37, 1, 7 )= #{ym,jdbcType=VARCHAR}
            </if>
        </if>
    </sql>

    <!-- 权限 -->
    <sql id="f3Auth">
        <!-- 科室权限 -->
        <if test="deptCode != null and deptCode != ''">
            AND B16C = #{deptCode,jdbcType=VARCHAR}
        </if>

        <!-- 医生权限 -->
        <if test="drCodg != null and drCodg != ''">
            AND B25C = #{drCodg,jdbcType=VARCHAR}
        </if>
    </sql>
    <!--出院 入院 结算 可优化-->
    <select id="sickCaseNum" resultType="com.my.som.vo.firstPage.FirstPage3Vo"
            parameterType="com.my.som.dto.firstpage.FirstPageDto">
            SELECT *
            from
             (

                SELECT
                    <if test="ym != null and ym != ''">
                        IFNULL( count( CASE WHEN SUBSTR( a1.B15, 1, 7 ) = #{ym,jdbcType=VARCHAR} THEN 1 ELSE NULL END ), 0 ) AS leaveHospitalCount,
                        IFNULL( count( CASE WHEN SUBSTR( a1.B12, 1, 7 ) =#{ym,jdbcType=VARCHAR} THEN 1 ELSE NULL END ), 0 ) AS inHospitalCount,
                        IFNULL( count( CASE WHEN SUBSTR( a1.D37, 1, 7 ) = #{ym,jdbcType=VARCHAR} THEN 1 ELSE NULL END ), 0 ) AS payCount
                    </if>
                FROM som_hi_invy_bas_info a1
                <if test='enableSeAns != null and enableSeAns != "" and enableSeAns == "1"'>
                    INNER JOIN som_setl_cas_crsp p
                    ON a1.k00 = p.k00
                </if>
                where 1 = 1
                    <if test="hospitalId!=null and hospitalId!='' ">
                        AND a1.HOSPITAL_ID=#{hospitalId,jdbcType=VARCHAR}
                    </if>
                    <include refid="f3Auth" />

            )m
            left join (

                    SELECT
                        IFNULL(count(1),0) as optimizableCount,t
                    from(
                        SELECT b.k00,
                        b.HOSPITAL_ID,
                        <if test="ym!=null and ym!='' ">
                            #{ym,jdbcType=VARCHAR} as t
                        </if>
                        FROM som_can_opt_medcas_info b
                        LEFT JOIN som_hi_invy_bas_info c on c.id=b.k00
                        <if test='enableSeAns != null and enableSeAns != "" and enableSeAns == "1"'>
                            INNER JOIN som_setl_cas_crsp p
                            ON c.k00 = p.k00
                        </if>
                        <where>
                            AND SETTLE_LIST_ID IN (
                                SELECT id
                                FROM som_hi_invy_bas_info a
                                <!--出院-->
                                <where>
                                    <if test="type == 1 ">
                                        AND substr( a.B15, 1, 7 )= #{ym,jdbcType=VARCHAR}
                                    </if>
                                    <!--结算-->
                                    <if test="type == 2 ">
                                        AND substr( a.D37, 1, 7 )= #{ym,jdbcType=VARCHAR}
                                    </if>
                                </where>
                            )

                            <if test="grperType !=null and grperType!=''">
                                AND b.grper_type = #{grperType,jdbcType=VARCHAR}
                            </if>
                            <!-- 科室权限 -->
                            <if test="deptCode != null and deptCode != ''">
                                AND b.DEPT_CODE = #{deptCode,jdbcType=VARCHAR}
                            </if>

                            <!-- 医生权限 -->
                            <if test="drCodg != null and drCodg != ''">
                                AND b.dr_codg = #{drCodg,jdbcType=VARCHAR}
                            </if>

                            <if test="hospitalId != null and hospitalId != ''">
                                AND b.HOSPITAL_ID= #{hospitalId,jdbcType=VARCHAR}
                            </if>

                        </where>
                    )k
            )b1
            <if test="ym != null and ym != ''">
                on b1.t=#{ym,jdbcType=VARCHAR}
            </if>
    </select>
    <!--预测费用 总费用 超高 超低 费用差异-->
    <select id="forcastCostAndHighLow" resultType="com.my.som.vo.firstPage.FirstPage3Vo"
            parameterType="com.my.som.dto.firstpage.FirstPageDto">
        SELECT
            IFNULL( COUNT( CASE WHEN a.ratioRange = 1 THEN 1 ELSE NULL END ), 0 ) AS ultrahighNum,
            IFNULL( COUNT( CASE WHEN a.ratioRange = 2 THEN 1 ELSE NULL END ), 0 ) AS ultraLowNum,
            IFNULL( ROUND( SUM( a.D01 ), 2 ), 0 ) AS inHospitalTotalCost,
            IFNULL( ROUND( SUM( a.ycCost ), 2 ), 0 ) AS forecastAmount,
            IFNULL( ROUND( IFNULL( SUM( a.ycCost ), 0 ) - IFNULL( SUM( a.D01 ), 0 ) - IFNULL( SUM( a.D02 ), 0 ), 2 ), 0 ) AS forecastAmountDiff
        FROM
            (
            SELECT
                a.D01,
                a.d02,
                z.ratioRange,
                z.ycCost
            FROM
                som_hi_invy_bas_info a
            INNER JOIN (
                SELECT
                    b.SETTLE_LIST_ID,
                b.forecast_fee AS ycCost,
                dise_type AS ratioRange
                FROM
                <if test="grperType !=null and grperType!='' and grperType==1">
                    som_dip_sco b
                </if>
                <if test="grperType !=null and grperType!='' and grperType==3">
                    som_drg_sco b
                </if>

                    WHERE 	b.SETTLE_LIST_ID in(
                        select id
                        from som_hi_invy_bas_info a
                    <where>
                        <include refid="issueCondition"></include>
                        <include refid="hosIdCondition"></include>
                        <include refid="f3Auth" />
                    </where>
                    )
            ) z ON a.id = z.SETTLE_LIST_ID
            <if test='enableSeAns != null and enableSeAns != "" and enableSeAns == "1"'>
                INNER JOIN som_setl_cas_crsp p
                ON a.k00 = p.k00
            </if>
            where 1 = 1
                <if test="hospitalId!=null and hospitalId!='' ">
                    AND a.HOSPITAL_ID=#{hospitalId,jdbcType=VARCHAR}
                </if>
                <include refid="issueCondition"></include>
                <include refid="f3Auth" />
        ) a
    </select>

    <!--查询入组数量-->
    <select id="queryInGroup" resultType="com.my.som.vo.firstPage.FirstPage3Vo">
            SELECT
					count(case when f.grp_stas=1 then 1 else null end)as inGroupSuccessNum,
					count(case when f.grp_stas =0 then 1 else null end)as noGroupNum
			from
			(
				SELECT *
				from
                <if test="grperType !=null and grperType!='' and grperType==1">
                    som_dip_grp_rcd sg2
                </if>
                <if test="grperType !=null and grperType!='' and grperType==3">
                    som_drg_grp_rcd sg2
                </if>
				cross join(
						SELECT distinct a.id as inId
						from som_hi_invy_bas_info a
                        <if test='enableSeAns != null and enableSeAns != "" and enableSeAns == "1"'>
                            INNER JOIN som_setl_cas_crsp p
                            ON a.k00 = p.k00
                        </if>
						<where>
                             a.id in(
                                 SELECT distinct sg.SETTLE_LIST_ID
                                 from
                                <if test="grperType !=null and grperType!='' and grperType==1">
                                    som_dip_grp_rcd sg
                                </if>
                                <if test="grperType !=null and grperType!='' and grperType==3">
                                    som_drg_grp_rcd sg
                                </if>
                             )
                           <include refid="issueCondition"></include>
                            <if test="hospitalId!=null and hospitalId!='' ">
                                AND a.HOSPITAL_ID=#{hospitalId,jdbcType=VARCHAR}
                            </if>
                           <include refid="f3Auth"></include>

                        </where>
				)d on d.inId=sg2.SETTLE_LIST_ID
			)f
    </select>
    <!--清单上传信息汇总-->
    <select id="querySettleListUploadList" parameterType="com.my.som.dto.firstpage.FirstPageDto"
            resultType="com.my.som.vo.firstPage.FirstPage3Vo">
            select
                IFNULL(d.markNum,0) as confirmNum,
                IFNULL(d.totalNum-d.markNum,0) as noConfirmNum,
                IFNULL(d.uploadSNum,0) as totalUploadSuccessNum,
                IFNULL(d.totalNum-d.uploadFNum-d.uploadSNum,0) as noUploadNum,
                IFNULL(d.validateSNum,0) as passedValidateNum,
                IFNULL(d.uploadFNum,0) as uploadFailedNum,
                IFNULL(d.totalNum - d.validateSNum ,0) as noPassedValidateNum,
                IFNULL(d.totalNum,0) as neededUploadNum
            from (
                select  sum(case when upld_stas = '1' then 1 else 0 end) AS uploadSNum,
                        sum(case when upld_stas = '0' then 1 else 0 end) AS uploadFNum,
                        sum(case when LOOK_OVER = '1' then 1 else 0 end) AS markNum,
                        sum(case when chk_stas = '1' then 1 else 0 end) AS validateSNum,
                        avg(totalNum) AS totalNum
                from(
                        select a.LOOK_OVER,
                        a.UPLOAD_FLAG,
                        b.chk_stas,
                        c.upld_stas,
                        count(1)over() AS totalNum
                        from som_hi_invy_bas_info a
                        <if test='enableSeAns != null and enableSeAns != "" and enableSeAns == "1"'>
                            INNER JOIN som_setl_cas_crsp p
                            ON a.k00 = p.k00
                        </if>
                        left join som_setl_invy_chk b
                        on a.id = b.SETTLE_LIST_ID
                        left join som_invy_upld_log c
                        on a.k00 = c.k00
                        where 1 = 1
                            <if test="hospitalId!=null and hospitalId!='' ">
                                AND a.HOSPITAL_ID=#{hospitalId,jdbcType=VARCHAR}
                            </if>
                            <include refid="issueCondition"></include>
                            <include refid="f3Auth"></include>
                    ) c
            ) d
    </select>
    <!--统计科室清单修改-->
    <select id="countSettleListModify" resultType="com.my.som.vo.firstPage.FirstPage3Vo"
            parameterType="com.my.som.dto.firstpage.FirstPageDto">
            SELECT
                n.t1 as modifyTime , n.modifyedSettleListUserNum,
                n.modifyedSettleListNumByDept,n.modifyedSettleListNumByDay,
                round(n.oldForcastCost,2) as oldForcastCost,
                round(n.modifyedForcastCost,2) as  modifyedForcastCost
                from (
                    select *
                    from(
                        SELECT count(1) as modifyedSettleListUserNum,f.t1
                        from(
                         select   updt_time AS t1,
                            a.USER_NAME
                         from (
                            SELECT substr( updt_time, 1, 10 ) as updt_time,k00,USER_NAME
                            from som_invy_upld_modi_rcd
                            <where>
                                <if test="begnDate!=null and expiDate!=null and begnDate!='' and expiDate!='' ">
                                      substr(updt_time,1,10) BETWEEN #{begnDate,jdbcType=VARCHAR} AND CONCAT(#{expiDate,jdbcType=VARCHAR},'23:59:59')
                                </if>
                            </where>
                         ) a inner join som_hi_invy_bas_info b on a.k00 = b.k00
                        <if test='enableSeAns != null and enableSeAns != "" and enableSeAns == "1"'>
                            INNER JOIN som_setl_cas_crsp p
                            ON b.k00 = p.k00
                        </if>
                       <where>
                           <if test="hospitalId!=null and hospitalId!='' ">
                               AND b.HOSPITAL_ID=#{hospitalId,jdbcType=VARCHAR}
                           </if>
                       </where>
                         GROUP BY t1 ,a.USER_NAME
                         )f group by f.t1
                    )b
                inner join(
                    SELECT count(k.k00) as modifyedSettleListNumByDay,
                    k.t2
                    from(
                         select updt_time as t2,a1.k00
                         from (
                             SELECT substr( updt_time, 1, 10 ) as updt_time,k00
                                from som_invy_upld_modi_rcd
                                <where>
                                    <if test="begnDate!=null and expiDate!=null and begnDate!='' and expiDate!='' ">
                                        substr(updt_time,1,10) BETWEEN #{begnDate,jdbcType=VARCHAR} AND CONCAT(#{expiDate,jdbcType=VARCHAR},'23:59:59')
                                    </if>
                                </where>
                        ) a1
                         inner join som_hi_invy_bas_info b  on a1.k00 = b.k00
                        <if test='enableSeAns != null and enableSeAns != "" and enableSeAns == "1"'>
                            INNER JOIN som_setl_cas_crsp p
                            ON b.k00 = p.k00
                        </if>
                            <where>
                                <if test="hospitalId!=null and hospitalId!='' ">
                                    AND b.HOSPITAL_ID=#{hospitalId,jdbcType=VARCHAR}
                                </if>
                            </where>
                         GROUP BY t2,a1.k00
                     )k group by k.t2
                )c on b.t1=c.t2
                inner join(
                    SELECT count(v.B16C) as modifyedSettleListNumByDept, v.t3
                    from(
                        select b.b16c,updt_time AS t3
                        from (
                            SELECT substr( updt_time, 1, 10 ) as updt_time,k00
                            from som_invy_upld_modi_rcd
                            <where>
                                <if test="begnDate!=null and expiDate!=null and begnDate!='' and expiDate!='' ">
                                      substr(updt_time,1,10) BETWEEN #{begnDate,jdbcType=VARCHAR} AND CONCAT(#{expiDate,jdbcType=VARCHAR},'23:59:59')
                                </if>
                            </where>
                       ) a
                        inner join som_hi_invy_bas_info b on a.k00 = b.k00
                        <if test='enableSeAns != null and enableSeAns != "" and enableSeAns == "1"'>
                            INNER JOIN som_setl_cas_crsp p
                            ON b.k00 = p.k00
                        </if>
                        <where>
                            <if test="hospitalId!=null and hospitalId!='' ">
                                AND b.HOSPITAL_ID=#{hospitalId,jdbcType=VARCHAR}
                            </if>
                        </where>
                        group by t3,b.b16C
                    )v group by v.t3
                 )k on k.t3=c.t2
                 inner join(
                        SELECT
                            SUBSTR(m.modi_time ,1,10) as modifyTime,
        IFNULL(sum( m.old_score * m.price), 0) AS oldForcastCost,
        IFNULL(sum(m.totl_sco * m.price), 0) AS modifyedForcastCost
                        from(
                            SELECT
                                insu_type,totl_sco,price,old_score,modi_time
                            from
                            <if test="grperType!=null and grperType!= '' and grperType==1">
                                som_dip_sco f
                            </if>
                            <if test="grperType!=null and grperType!= '' and grperType==3">
                                som_drg_sco f
                            </if>
                            inner join
                            (
                                SELECT
                                    c.updatedId as updId,
                                    c.k00 as updK00,
                                    c.oldScore as old_score,
                                    c.updTime as modi_time,
                                    c.newScore as newScore
                                FROM(
                                    SELECT a.id as updatedId ,a.k00,
                                    b.refer_sco as oldScore,
                                    c.totl_sco as newScore,
                                    b.updt_time as updTime,
                                    c.price AS price
                                    from som_hi_invy_bas_info a
                                    <if test='enableSeAns != null and enableSeAns != "" and enableSeAns == "1"'>
                                        INNER JOIN som_setl_cas_crsp p
                                        ON a.k00 = p.k00
                                    </if>
                                    <if test="grperType!=null and grperType!= '' and grperType==1">
                                        inner  join  som_dip_sco c on c.SETTLE_LIST_ID=a.id
                                    </if>
                                    <if test="grperType!=null and grperType!= '' and grperType==3">
                                        inner  join  som_drg_sco c on c.SETTLE_LIST_ID=a.id
                                    </if>
                                    inner join (
                                        SELECT updt_time,refer_sco,k00
                                        from som_invy_upld_modi_rcd
                                        <where>
                                            <if test="begnDate!=null and expiDate!=null and begnDate!='' and expiDate!='' ">
                                                 substr(updt_time,1,10) BETWEEN #{begnDate,jdbcType=VARCHAR} AND CONCAT(#{expiDate,jdbcType=VARCHAR},'23:59:59')
                                            </if>
                                        </where>
                                    ) b on b.K00=a.k00
                                )c
                            )as d on d.updId=f.SETTLE_LIST_ID
                            <where>
                                <include refid="hosIdCondition"></include>
                            </where>
                            )m GROUP BY  substr( m.modi_time,1,10)
                        )g on g.modifyTime=k.t3
                    )n  ORDER BY n.t1 desc

    </select>
    <sql id="cfgType">
        <if test="grperType !=null and grperType!='' and grperType==1">
            som_dip_gen_cfg
        </if>
        <if test="grperType !=null and grperType!='' and grperType==3">
            som_drg_gen_cfg
        </if>
    </sql>
    <!--科室亏损排名-->
    <select id="queryOrderData" resultType="com.my.som.vo.firstPage.FirstPage3Vo"
            parameterType="com.my.som.dto.firstpage.FirstPageDto">
        SELECT k.*
        FROM
        (
        SELECT
        <choose>
            <when test='analysisType == "dept"'>
                deptCode,deptName,
            </when>
            <when test='analysisType == "doctor"'>
                drCodg,drName,
            </when>
            <when test='analysisType == "dis"'>
                drgCodg,
                drgName,
                deptCode,
                deptName,
            </when>
            <when test='analysisType == "med"'>
                id,patientId, name,
            </when>
        </choose>
        IFNULL( ROUND( sum( preCost ) - sum( inHosTotalCost ), 2 ), 0 ) AS diff
        FROM
        (
        SELECT
        g.*,
        <if test="feeStas != null and feeStas != ''">
            <choose>
                <when test="feeStas == 0">
                    IFNULL(ROUND(g.refer_sco * h.price,2),0) AS preCost
                </when>
                <when test="feeStas == 1">
                    g.ycCost AS preCost
                </when>
            </choose>
        </if>
        FROM
        (
        SELECT
        a.B25N AS drName,
        a.B25C AS drCodg,
        a.B16C AS deptCode,
        a.A11 AS name,
        a.id AS id,
        e.`NAME` AS deptName,
        a.A48 AS patientId,
        <if test="feeStas != null and feeStas != ''">
            <choose>
                <when test="feeStas == 0">
                    a.a46c AS insuredType,
                    a.D01 AS inHosTotalCost,
                    b.drg_codg AS drgCodg,
                    b.DRG_NAME AS drgName,
                    f.totl_sco AS refer_sco,
                    f.dise_type AS highLowType
                </when>
                <when test="feeStas == 1">
                    z.INSURED_TYPE AS insuredType,
                    z.ipt_sumfee AS inHosTotalCost,
                    z.drg_codg AS drgCodg,
                    z.DRG_NAME AS drgName,
                    z.setl_sco AS refer_sco,
                    z.MED_TYPE AS highLowType,
                    z.ycCost
                </when>
            </choose>
        </if>
        FROM
        som_hi_invy_bas_info a
        <if test='enableSeAns != null and enableSeAns != "" and enableSeAns == "1"'>
            INNER JOIN som_setl_cas_crsp p
            ON a.k00 = p.k00
        </if>
        LEFT JOIN som_drg_grp_info b ON a.ID = b.SETTLE_LIST_ID
        LEFT JOIN som_drg_standard c ON b.drg_codg = c.drg_codg
        AND SUBSTR( b.dscg_time, 1, 4 ) = c.STANDARD_YEAR
        AND b.HOSPITAL_ID = c.HOSPITAL_ID
        AND c.ACTIVE_FLAG = 1
        LEFT JOIN som_dept e ON a.B16C = e.`CODE` AND a.A01 = e.HOSPITAL_ID
        LEFT JOIN som_drg_sco f ON a.id = f.SETTLE_LIST_ID AND a.A01 = f.HOSPITAL_ID

        <if test="feeStas != null and feeStas != '' ">
            <choose>
                <when test="feeStas == 1">
                    INNER JOIN
                    (
                    SELECT
                    a.medcas_codg,
                    a.HOSPITAL_ID,
                    a.adm_time,
                    a.drg_codg,
                    a.DRG_NAME,
                    a.dscg_time,
                    b.INSURED_TYPE,
                    b.MED_TYPE,
                    b.setl_sco,
                    b.sumfee AS ipt_sumfee,
                    b.dfr_fee AS ycCost
                    FROM som_drg_grp_fbck a
                    LEFT JOIN som_drg_pt_val_pay b
                    ON a.rid_idt_codg = b.rid_idt_codg
                    AND a.HOSPITAL_ID = b.HOSPITAL_ID
                    <![CDATA[
                                        WHERE a.dscg_time >= #{begnDate,jdbcType=VARCHAR} AND a.dscg_time <= #{expiDate,jdbcType=VARCHAR}
                                        ]]>
                    <include refid="com.my.som.common.mapper.CommonMapper.fbAuth" />
                    ) z
                    ON b.PATIENT_ID = z.medcas_codg
                    AND SUBSTR(b.adm_time,1,10) = z.adm_time
                    AND SUBSTR(b.dscg_time,1,10) = z.dscg_time
                    AND b.HOSPITAL_ID = z.HOSPITAL_ID
                </when>
            </choose>
        </if>
        WHERE
        b.grp_stas = 1
        AND a.ACTIVE_FLAG = '1'
        AND a.b15 BETWEEN #{begnDate,jdbcType=VARCHAR} and concat(#{expiDate,jdbcType=VARCHAR},' 23:59:59')
        <include refid="com.my.som.mapper.newBusiness.common.NewBusinessCommonAnalysisMapper.queryCommon" />
        ) g
        Left JOIN
        (
        SELECT
            SETTLE_LIST_ID,
            price
        FROM
            som_drg_sco
        )   h
        on h.SETTLE_LIST_ID = g.id
        )   i
        GROUP BY
        <choose>
            <when test='analysisType == "dept"'>
                deptCode,deptName
            </when>
            <when test='analysisType == "doctor"'>
                drCodg,drName
            </when>
            <when test='analysisType == "dis"'>
                drgCodg,
                drgName,
                deptCode,
                deptName
            </when>
            <when test='analysisType == "med"'>
                id,patientId, name
            </when>
        </choose>

        ) k
        WHERE <![CDATA[
            diff ${gtOrLtSymbol} 0
        ]]>
        ORDER BY diff ${sortSymbol}
        LIMIT 5
    </select>
</mapper>
