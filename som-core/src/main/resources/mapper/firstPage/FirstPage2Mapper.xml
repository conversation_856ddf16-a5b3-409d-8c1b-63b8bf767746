<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.som.mapper.firstpage.FirstPage2Mapper">

    <!-- 查询入院人次信息 -->
    <select id="queryInHospitalInfo" resultType="com.my.som.vo.firstPage.FirstPageBaseVo">
        SELECT b.cur_count AS inHospitalCount, <!-- 当前月份入院人次 -->
               IFNULL(((b.cur_count - b.last_month_count) / b.last_month_count) * 100,0) AS inHospitalCountLrr, <!-- 环比 -->
               IFNULL(((b.cur_count - c.last_year_month_count) / c.last_year_month_count) * 100,0) AS inHospitalCountYoy <!-- 同比 -->
        FROM (
                SELECT e.*
                FROM (
                    SELECT a.*,
                           LAG(cur_count)OVER(order by cur_month) AS last_month_count
                    FROM (
                        SELECT COUNT(1) AS cur_count,
                                     SUBSTRING(B12,1,7) AS cur_month
                        FROM som_hi_invy_bas_info a
                        WHERE B12 IS NOT NULL
                        <include refid="deptCode"></include>
                        <include refid="com.my.som.common.mapper.CommonMapper.fbAuth" />
                        GROUP BY SUBSTRING(B12,1,7)
                        ORDER BY SUBSTRING(B12,1,7)
                    ) a
                ) e
                WHERE e.cur_month = #{curMonth,jdbcType=VARCHAR}
             ) b
        LEFT JOIN (
            SELECT d.*,
				 SUBSTRING(DATE_ADD(STR_TO_DATE(CONCAT(last_year_month,'-01') ,'%Y-%m-%d'), INTERVAL 1 YEAR),1,7) AS cur_month
            FROM (
                SELECT COUNT(1) AS last_year_month_count,
                       SUBSTRING(B12,1,7) as last_year_month
                FROM som_hi_invy_bas_info a
                WHERE B12 IS NOT NULL
                  <include refid="deptCode"></include>
                  <include refid="com.my.som.common.mapper.CommonMapper.fbAuth" />
                AND SUBSTRING(B12,1,7) = #{lastYearMonth,jdbcType=VARCHAR}
                GROUP BY SUBSTRING(B12,1,7)
            ) d
        ) c
        ON b.cur_month = c.cur_month
    </select>

    <!-- 查询出院和费用信息 -->
    <select id="queryLeaveHospitalAndCostInfo" resultType="com.my.som.vo.firstPage.FirstPageBaseVo">
        SELECT b.cur_count AS leaveHospitalCount, <!-- 出院人次 -->
               b.cur_sum_cost AS inHospitalTotalCost, <!-- 住院总费用 -->
               IFNULL(((b.cur_count - b.last_month_count) / b.last_month_count) * 100,0) AS leaveHospitalCountLrr, <!-- 出院人次-环比 -->
               IFNULL(((b.cur_count - c.last_year_month_count) / c.last_year_month_count) * 100,0) AS leaveHospitalCountYoy, <!-- 出院人次-同比 -->
               IFNULL(((b.cur_sum_cost - b.last_month_sum_cost) / b.last_month_sum_cost) * 100,0) AS inHospitalTotalCostLrr, <!-- 住院总费用-环比 -->
               IFNULL(((b.cur_sum_cost - c.last_year_month_sum_cost) / c.last_year_month_sum_cost) * 100,0) AS inHospitalTotalCostYoy <!-- 住院总费用-同比 -->
        FROM (
                 SELECT e.*
                 FROM (
                          SELECT a.*,
                                 LAG(cur_count)OVER(order by cur_month) AS last_month_count,
                                  LAG(cur_sum_cost)OVER(order by cur_month) AS last_month_sum_cost
                          FROM (
                                   SELECT COUNT(1) AS cur_count,
                                          SUM(D01) AS cur_sum_cost,
                                          SUBSTRING(B15,1,7) AS cur_month
                                   FROM som_hi_invy_bas_info a
                                   WHERE B15 IS NOT NULL
                                   <include refid="deptCode"></include>
                                   <include refid="com.my.som.common.mapper.CommonMapper.fbAuth" />
                                   GROUP BY SUBSTRING(B15,1,7)
                                   ORDER BY SUBSTRING(B15,1,7)
                               ) a
                      ) e
                 WHERE e.cur_month = #{curMonth,jdbcType=VARCHAR}
             ) b
                 LEFT JOIN (
            SELECT d.*,
                   SUBSTRING(DATE_ADD(STR_TO_DATE(CONCAT(last_year_month,'-01') ,'%Y-%m-%d'), INTERVAL 1 YEAR),1,7) AS cur_month
            FROM (
                     SELECT COUNT(1) AS last_year_month_count,
                            SUM(D01) AS last_year_month_sum_cost,
                            SUBSTRING(B15,1,7) as last_year_month
                     FROM som_hi_invy_bas_info a
                     WHERE B15 IS NOT NULL
                       <include refid="deptCode"></include>
                       <include refid="com.my.som.common.mapper.CommonMapper.fbAuth" />
                       AND SUBSTRING(B15,1,7) = #{lastYearMonth,jdbcType=VARCHAR}
                     GROUP BY SUBSTRING(B15,1,7)
                 ) d
        ) c
        ON b.cur_month = c.cur_month
    </select>

    <!-- 查询组信息 -->
    <select id="queryGroupInfo" resultType="com.my.som.vo.firstPage.FirstPageBaseVo">
        SELECT c.cur_group_count AS groupCount, <!-- 当前组数 -->
               IFNULL(((c.cur_group_count - c.last_month_group_count) / c.last_month_group_count) * 100,0) AS groupCountLrr, <!-- 同比 -->
               IFNULL(((c.cur_group_count - e.last_year_month_group_count) / e.last_year_month_group_count) * 100,0) AS groupCountYoy <!-- 环比 -->
        FROM (
                 SELECT b.*
                 FROM (
                          SELECT COUNT(1) AS cur_group_count,
                                 cur_month,
                                 LAG(COUNT(1))OVER(order by cur_month) AS last_month_group_count
                          FROM (
                                   SELECT COUNT(1) AS group_count,
                                          SUBSTRING(dscg_time,1,7) AS cur_month
                                   FROM
                                   <choose>
                                       <when test='groupType == "3"'>
                                           som_drg_grp_info a
                                       </when>
                                       <when test='groupType == "2"'>
                                           som_cd_grp_info a
                                       </when>
                                       <when test='groupType == "1"'>
                                           som_dip_grp_info a
                                       </when>
                                   </choose>
                                   WHERE
                                   <choose>
                                         <when test='groupType == "3"'>
                                             drg_codg IS NOT NULL
                                             AND drg_codg != ''
                                         </when>
                                         <when test='groupType == "2"'>
                                             cd_codg IS NOT NULL
                                             AND cd_codg != ''
                                         </when>
                                         <when test='groupType == "1"'>
                                             dip_codg IS NOT NULL
                                             AND dip_codg != ''
                                         </when>
                                     </choose>
                                   AND dscg_time IS NOT NULL
                                   <include refid="keyDeptCode"></include>
                                   <include refid="com.my.som.common.mapper.CommonMapper.fbAuth" />
                                   GROUP BY SUBSTRING(dscg_time,1,7),
                                   <choose>
                                         <when test='groupType == "3"'>
                                             drg_codg
                                         </when>
                                         <when test='groupType == "2"'>
                                             cd_codg
                                         </when>
                                         <when test='groupType == "1"'>
                                             dip_codg
                                         </when>
                                     </choose>
                                   ORDER BY SUBSTRING(dscg_time,1,7)
                               ) a
                          GROUP BY cur_month
                      ) b
                 WHERE b.cur_month = #{curMonth,jdbcType=VARCHAR}
             ) c
        LEFT JOIN (
            SELECT COUNT(1) AS last_year_month_group_count,
                   last_year_month,
                   SUBSTRING(DATE_ADD(STR_TO_DATE(CONCAT(last_year_month,'-01') ,'%Y-%m-%d'), INTERVAL 1 YEAR),1,7) AS cur_month
            FROM (
                     SELECT COUNT(1) AS group_count,
                            SUBSTRING(dscg_time,1,7) AS last_year_month
                     FROM
                     <choose>
                         <when test='groupType == "3"'>
                             som_drg_grp_info a
                         </when>
                         <when test='groupType == "2"'>
                             som_cd_grp_info a
                         </when>
                        <when test='groupType == "1"'>
                             som_dip_grp_info a
                        </when>
                     </choose>
                     WHERE
                      <choose>
                         <when test='groupType == "3"'>
                             drg_codg IS NOT NULL
                             AND drg_codg != ''
                         </when>
                         <when test='groupType == "2"'>
                             cd_codg IS NOT NULL
                             AND cd_codg != ''
                         </when>
                         <when test='groupType == "1"'>
                             dip_codg IS NOT NULL
                             AND dip_codg != ''
                         </when>
                      </choose>
                      IS NOT NULL
                      AND dscg_time IS NOT NULL
                      <include refid="keyDeptCode"></include>
                     <include refid="com.my.som.common.mapper.CommonMapper.fbAuth" />
                      AND SUBSTRING(dscg_time,1,7) = #{lastYearMonth,jdbcType=VARCHAR}
                      GROUP BY SUBSTRING(dscg_time,1,7),
                       <choose>
                         <when test='groupType == "3"'>
                             drg_codg
                         </when>
                         <when test='groupType == "2"'>
                             cd_codg
                         </when>
                         <when test='groupType == "1"'>
                             dip_codg
                         </when>
                     </choose>
                 ) d
            GROUP BY last_year_month
        ) e
        ON c.cur_month = e.cur_month
    </select>

    <!-- 查询城职城乡汇总信息 -->
    <select id="queryForecastSummary" resultType="com.my.som.vo.firstPage.FirstPageForecastVo">
        select d.*
        from (
             select b.a46c AS type, <!-- 城职城乡类型 -->
                    COUNT(1) AS totalCount, <!-- 数量 -->
                    ROUND(SUM(a.ipt_sumfee),2) as projTotalCost, <!-- 项目支付金额 -->
                    <choose>
                        <when test="grperType == 3 or grperType == 2">
                            ROUND(SUM(AES_DECRYPT(UNHEX(c.standard_avg_fee),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY})),2) as predictCost <!-- 病种支付金额 -->
                        </when>
                        <when test="grperType == 1">
                             <!--ROUND(SUM(AES_DECRYPT(UNHEX(c.dip_standard_inpf),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY})),2) as predictCost 病种支付金额 -->
                            ROUND(SUM(d.ycCost),2) AS predictCost <!-- 病种支付金额 -->
                        </when>
                    </choose>
             from
                <choose>
                    <when test="grperType == 3">
                        som_drg_grp_info a
                    </when>
                    <when test="grperType == 2">
                        som_cd_grp_info a
                    </when>
                    <when test="grperType == 1">
                        som_dip_grp_info a
                    </when>
                </choose>
             inner join som_hi_invy_bas_info b
             on a.SETTLE_LIST_ID = b.id
             left join
                 <choose>
                    <when test="grperType == 3">
                        som_drg_standard c
                        ON a.drg_codg = c.drg_codg
                        AND a.HOSPITAL_ID = c.HOSPITAL_ID
                    </when>
                    <when test="grperType == 2">
                        som_cd_standard_info c
                        ON a.cd_codg = c.cd_codg
                        AND a.HOSPITAL_ID = c.HOSPITAL_ID
                    </when>
                    <when test="grperType == 1">
                        som_dip_standard c
                        ON a.dip_codg = c.dip_codg
                        AND a.HOSPITAL_ID = c.HOSPITAL_ID
                    </when>
                </choose>
             and a.HOSPITAL_ID = c.HOSPITAL_ID
             and SUBSTRING(a.dscg_time,1,4) = c.STANDARD_YEAR
            <if test="grperType == 1">
                left join (
                     SELECT
                            b.SETTLE_LIST_ID,
                            b.forecast_fee AS ycCost,
                            b.price,
                            b.profitloss AS profitloss,
                            b.sumfee AS sumfee
                    from som_dip_sco b
                    where b.ym = #{curMonth,jdbcType=VARCHAR}
                ) d
             on a.SETTLE_LIST_ID = d.SETTLE_LIST_ID
            </if>
             where SUBSTRING(a.dscg_time,1,7) = #{curMonth,jdbcType=VARCHAR}
             AND SUBSTRING(b.b15,1,7) = #{curMonth,jdbcType=VARCHAR}
             AND a.grp_stas = '1'
             <if test="deptCode != null and deptCode != ''">
                AND a.dscg_caty_codg_inhosp = #{deptCode}
             </if>
             <include refid="com.my.som.common.mapper.CommonMapper.fbAuth" />
             group by b.a46c
         ) d
    </select>

    <!-- 查询倍率信息 -->
    <select id="queryForecastMultiple" resultType="com.my.som.vo.firstPage.FirstPageForecastVo">
        select f.range_type AS rangeType, <!-- 类型 -->
			 COUNT(1) AS rangeCount <!-- 数量 -->
        from (
            select d.*,
                   <choose>
                       <when test="grperType == 3">
                           <![CDATA[
                               case when d.sumfee >= d.standard_avg_fee * e.mutiple_up then 1
                                    when  d.sumfee < d.standard_avg_fee * e.mutiple_down then 2
                                    when d.sumfee < d.standard_avg_fee * e.mutiple_up and
                                    d.sumfee >= d.standard_avg_fee * e.mutiple_down then 3
                               else 4 end as range_type
                           ]]>
                       </when>
                       <when test="grperType == 2">
                            <![CDATA[
                               case when d.pay_stsb is not null and d.pay_stsb != '' then
                                      case when d.pay_stsb in ('1.4-4','4以上') then 1
                                           when d.pay_stsb = '0.7以下' then 2
                                           when d.pay_stsb in ('0.7-1', '1-1.4') then 3
                                      else 4 end
                               else
                                      case when d.sumfee >= d.standard_avg_fee * e.mutiple_up then 1
                                           when  d.sumfee < d.standard_avg_fee * e.mutiple_down then 2
                                           when d.sumfee < d.standard_avg_fee * e.mutiple_up and
                                           d.sumfee >= d.standard_avg_fee * e.mutiple_down then 3
                                       else 4 end
                              end as range_type
                          ]]>
                       </when>
                        <when test="grperType == 1">
                           <![CDATA[
                             CASE WHEN m.dise_type = 1 THEN 1
                                  WHEN m.dise_type = 2 THEN 2
                                  WHEN m.dise_type = 3 THEN 3
                             ELSE 4 END AS range_type
                           ]]>
                       </when>
                   </choose>
            from
            (
                select a.ipt_sumfee as sumfee,
                       a.HOSPITAL_ID,
                       a.SETTLE_LIST_ID,
                        <choose>
                            <when test="grperType == 3">
                                AES_DECRYPT(UNHEX(c.standard_avg_fee), ${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) as standard_avg_fee
                            </when>
                            <when test="grperType == 2">
                                AES_DECRYPT(UNHEX(c.standard_avg_fee), ${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) as standard_avg_fee,
                                n.pay_stsb
                            </when>
                            <when test="grperType == 1">
                                AES_DECRYPT(UNHEX(c.dip_standard_inpf), ${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) as standard_avg_fee,
                                a.dip_codg,
                                a.is_used_asst_list,
                                a.asst_list_age_grp,
                                a.asst_list_dise_sev_deg,
                                a.asst_list_tmor_sev_deg
                            </when>
                        </choose>
                from
                     <choose>
                         <when test="grperType == 3">
                             som_drg_grp_info a
                         </when>
                         <when test="grperType == 2">
                             som_cd_grp_info a
                         </when>
                         <when test="grperType == 1">
                             som_dip_grp_info a
                         </when>
                     </choose>
                inner join som_hi_invy_bas_info b
                on a.SETTLE_LIST_ID = b.id
                left join
                    <choose>
                        <when test="grperType == 3">
                            som_drg_standard c
                            ON a.drg_codg = c.drg_codg
                            AND a.HOSPITAL_ID = c.HOSPITAL_ID
                        </when>
                        <when test="grperType == 2">
                            som_cd_standard_info c
                            ON a.cd_codg = c.cd_codg
                            AND a.HOSPITAL_ID = c.HOSPITAL_ID
                        </when>
                        <when test="grperType == 1">
                            som_dip_standard c
                            ON a.dip_codg = c.dip_codg
                            AND a.HOSPITAL_ID = c.HOSPITAL_ID
                        </when>
                    </choose>
                and a.HOSPITAL_ID = c.HOSPITAL_ID
                and SUBSTRING(a.dscg_time,1,4) = c.STANDARD_YEAR
                <if test="grperType == 2">
                    left join som_grp_rcd n
                    on a.SETTLE_LIST_ID = n.SETTLE_LIST_ID
                </if>
                where SUBSTRING(a.dscg_time,1,7) = #{curMonth,jdbcType=VARCHAR}
                <if test="deptCode != null and deptCode != ''">
                and a.dscg_caty_codg_inhosp = #{deptCode}
                </if>
                <include refid="com.my.som.common.mapper.CommonMapper.fbAuth" />
            ) d
            <if test="grperType == 1">
                LEFT JOIN som_dip_sco m
                    ON d.HOSPITAL_ID = m.HOSPITAL_ID
                   AND d.SETTLE_LIST_ID = m.SETTLE_LIST_ID
            </if>
            cross join (
                select MAX(case when `key` = 'RANGE_UP' THEN `VALUE` ELSE NULL END) AS mutiple_up,
                             MAX(case when `key` = 'RANGE_DOWN' THEN `VALUE` ELSE NULL END) AS mutiple_down
                from som_sys_gen_cfg
                WHERE
                    <choose>
                        <when test="grperType == 3">
                            type = 'DIP_MULTIPLE_RANGE'
                        </when>
                        <when test="grperType == 2">
                            type = 'CD_MULTIPLE_RANGE'
                        </when>
                        <when test="grperType == 1">
                            type = 'DRG_MULTIPLE_RANGE'
                        </when>
                    </choose>

                GROUP BY type
            ) e
        ) f group by range_type
    </select>

    <!-- 查询逆差组 -->
    <select id="queryForecastBalance" resultType="com.my.som.vo.firstPage.FirstPageForecastVo">
        select e.code,
               e.totalCount,
               e.totalBalance
        from (
                 select d.*,
                        ROUND(predictCost - projTotalCost,2) as totalBalance <!-- 差额 -->
                 from (
                          select
                                 <choose>
                                     <when test="grperType == 3">
                                         a.drg_codg AS code, <!-- DRG编码 -->
                                         ROUND(SUM(AES_DECRYPT(UNHEX(c.standard_avg_fee),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY})),2) as predictCost, <!-- 病种支付金额 -->
                                     </when>
                                     <when test="grperType == 2">
                                         a.cd_codg AS code, <!-- 成都编码 -->
                                         ROUND(SUM(AES_DECRYPT(UNHEX(c.standard_avg_fee),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY})),2) as predictCost, <!-- 病种支付金额 -->
                                     </when>
                                     <when test="grperType == 1">
                                         a.dip_codg AS code, <!-- DIP编码 -->
                                         ROUND(SUM(d.ycCost),2) AS predictCost, <!-- 病种支付金额 -->
                                         <!--ROUND(SUM(AES_DECRYPT(UNHEX(c.dip_standard_inpf),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY})),2) as predictCost,  病种支付金额 -->
                                     </when>
                                 </choose>
                                 count(1) as totalCount, <!-- 人数 -->
                                 ROUND(SUM(a.ipt_sumfee),2) as projTotalCost <!-- 项目支付金额 -->
                          from
                               <choose>
                                   <when test="grperType == 3">
                                       som_drg_grp_info a
                                       left join som_drg_standard c
                                       on a.drg_codg = c.drg_codg
                                       AND a.HOSPITAL_ID = c.HOSPITAL_ID
                                   </when>
                                   <when test="grperType == 2">
                                       som_cd_grp_info a
                                       left join som_cd_standard_info c
                                       on a.cd_codg = c.cd_codg
                                       AND a.HOSPITAL_ID = c.HOSPITAL_ID
                                   </when>
                                   <when test="grperType == 1">
                                       som_dip_grp_info a
                                       left join som_dip_standard c
                                       on a.dip_codg = c.dip_codg
                                       AND a.HOSPITAL_ID = c.HOSPITAL_ID
                                   </when>
                               </choose>
                          and a.HOSPITAL_ID = c.HOSPITAL_ID
                          and SUBSTRING(a.dscg_time,1,4) = c.STANDARD_YEAR
                          <if test="grperType == 1">
                                left join (
                                    SELECT
                                        b.SETTLE_LIST_ID,
                                        b.forecast_fee AS ycCost,
                                        b.price,
                                        b.profitloss AS profitloss,
                                        b.sumfee AS sumfee
                                        FROM som_dip_sco b
                                        where b.ym = #{curMonth,jdbcType=VARCHAR}
                                ) d
                                on a.SETTLE_LIST_ID = d.SETTLE_LIST_ID
                            </if>
                          where SUBSTRING(a.dscg_time,1,7) = #{curMonth,jdbcType=VARCHAR}
                          <if test="deptCode != null and deptCode != ''">
                              and a.dscg_caty_codg_inhosp = #{deptCode}
                          </if>
                          <include refid="com.my.som.common.mapper.CommonMapper.fbAuth" />
                          <choose>
                              <when test="grperType == 3">
                                  and a.drg_codg is not null
                                  and c.standard_avg_fee is not null
                                  group by a.drg_codg
                              </when>
                              <when test="grperType == 2">
                                  and a.cd_codg is not null
                                  and c.standard_avg_fee is not null
                                  group by a.cd_codg
                              </when>
                              <when test="grperType == 1">
                                  and a.dip_codg is not null
                                  and c.dip_standard_inpf is not null
                                  group by a.dip_codg
                              </when>
                          </choose>

                      ) d
             ) e
        order by e.totalBalance
        limit #{balanceOrderLimit,jdbcType=INTEGER}
    </select>

    <!-- 查询病案平均得分 -->
    <select id="queryControlAvgScore" resultType="com.my.som.vo.firstPage.FirstPageForecastVo">
        select ifnull(round(avg(refer_sco),2),0) as avgScore
        from som_setl_invy_qlt_dedu_point_detl a
        where exists
        (
            select b.ID
            from som_init_hi_setl_invy_med_fee_info b
            where a.SETTLE_LIST_ID = b.id
              <if test="deptCode != null and deptCode != ''">
                  and b.B16C = #{deptCode}
              </if>
              <if test="hospitalId != null and hospitalId != ''">
                  and b.HOSPITAL_ID = #{hospitalId,jdbcType=VARCHAR}
              </if>

            and SUBSTRING(b.b15,1,7) = #{curMonth,jdbcType=VARCHAR}
        )
    </select>

    <!-- 查询完整性校验信息 -->
    <select id="queryControlRate" resultType="com.my.som.vo.firstPage.FirstPageForecastVo">
        select ifnull(round((d.all_count - c.error_num) / d.all_count * 100,2),0) as curMonthRate,
               ifnull(round(((d.all_count - c.error_num) - (d.last_month_all_count - c.last_month_count))
				/ (d.last_month_all_count - c.last_month_count) * 100,2),0) lastMonthRate,
               all_count AS totalCount
        from (
                 select m.*,
                        lag(error_num)over(order by cur_month) as last_month_count
                 from (
                      SELECT count(distinct(case when err_type in
                             <foreach collection="validateErrorCode" item="code" open="(" close=")" separator=",">
                                 #{code}
                             </foreach>
                             then a.settle_list_id else null end)) as error_num,
                             SUBSTRING(b.b15,1,7) as cur_month
                      FROM
                      som_setl_invy_chk_err_rcd a
                      inner join som_init_hi_setl_invy_med_fee_info b
                      on a.settle_list_id = b.id
                        <if test="deptCode != null and deptCode != ''">
                            and b.B16C = #{deptCode}
                        </if>
                      <if test="hospitalId != null and hospitalId != ''">
                          and b.HOSPITAL_ID = #{hospitalId,jdbcType=VARCHAR}
                      </if>
                      group by SUBSTRING(b.b15,1,7)
                 ) m
             ) c
             left join (
                select n.*,
                       lag(all_count)over(order by cur_month) as last_month_all_count
                from (
                     select count(1) as all_count,
                            SUBSTRING(b15,1,7) as cur_month
                     from som_init_hi_setl_invy_med_fee_info a
                     <where>
                         <if test="deptCode != null and deptCode != ''">
                             and B16C = #{deptCode}
                         </if>
                        <include refid="com.my.som.common.mapper.CommonMapper.fbAuth" />
                     </where>
                     group by SUBSTRING(b15,1,7)
                    ) n
            ) d
        on c.cur_month = d.cur_month
        where c.cur_month = #{curMonth,jdbcType=VARCHAR}
    </select>

    <!-- 查询病案质控监测对比图信息 -->
    <select id="queryMedicalRecordControlInfo"
            resultType="com.my.som.vo.firstPage.FirstPageForecastVo">
        select a.count as totalCount, <!-- 人数 -->
			   b.count as groupCount, <!-- 组数 -->
			   c.count as completionCount, <!-- 完整性校验数量 -->
			   d.count as logicCount, <!-- 逻辑性校验数量 -->
               e.count as codeCount, <!-- 编码正确数量 -->
			   a.month
        from (
            <!-- 人数 -->
            select SUBSTRING(a.b15,1,7) as month,
                         count(1) as count
            from som_init_hi_setl_invy_med_fee_info a
            <![CDATA[
            WHERE SUBSTRING( a.b15, 1, 7 ) >= #{begnDate,jdbcType=VARCHAR}
			AND SUBSTRING( a.b15, 1, 7 ) <= #{expiDate,jdbcType=VARCHAR}
            ]]>
            <if test="deptCode != null and deptCode != ''">
                and a.B16C = #{deptCode}
            </if>
            <include refid="com.my.som.common.mapper.CommonMapper.fbAuth" />
            group by SUBSTRING(a.b15,1,7)
        ) a
        left join (
            <!-- 组数 -->
            select month,count(1) as count
            from (
            select SUBSTRING(a.dscg_time,1,7) as month
            from
               <choose>
                   <when test='groupType == "3"'>
                       som_drg_grp_info a
                   </when>
                   <when test='groupType == "2"'>
                       som_cd_grp_info a
                   </when>
                   <when test='groupType == "1"'>
                       som_dip_grp_info a
                   </when>
               </choose>
        <![CDATA[
            WHERE SUBSTRING( a.dscg_time, 1, 7 ) >= #{begnDate,jdbcType=VARCHAR}
			AND SUBSTRING( a.dscg_time, 1, 7 ) <= #{expiDate,jdbcType=VARCHAR}
            ]]>
            <if test="deptCode != null and deptCode != ''">
                and a.dscg_caty_codg_inhosp = #{deptCode}
            </if>
            <include refid="com.my.som.common.mapper.CommonMapper.fbAuth" />
            group by SUBSTRING(a.dscg_time,1,7),
                <choose>
                   <when test='groupType == "3"'>
                       a.drg_codg
                   </when>
                   <when test='groupType == "1"'>
                       a.dip_codg
                   </when>
               </choose>

            ) b group by month
        ) b
        on a.month = b.month
        left join (
            <!-- 完整性校验数量 -->
            select p.month,
				 p.all_count - count as count
	        from (
                SELECT count(distinct(case when err_type in ('CE01','CE02','CE03','CE04') then a.settle_list_id else null end)) as count,
                       count(distinct a.SETTLE_LIST_ID) as all_count,
                       SUBSTRING(b.b15,1,7) as month
                FROM som_setl_invy_chk_err_rcd a
                inner join som_init_hi_setl_invy_med_fee_info b
                on a.settle_list_id = b.id
                <if test="deptCode != null and deptCode != ''">
                    and b.B16C = #{deptCode}
                </if>
                <include refid="com.my.som.common.mapper.CommonMapper.fbAuth" />
                <![CDATA[
                    WHERE SUBSTRING( b.b15, 1, 7 ) >= #{begnDate,jdbcType=VARCHAR}
                    AND SUBSTRING( b.b15, 1, 7 ) <= #{expiDate,jdbcType=VARCHAR}
                    ]]>
                group by SUBSTRING(b.b15,1,7)
            ) p
        ) c
        on a.month = c.month
        left join (
            <!-- 逻辑性校验数量 -->
            select p.month,
				   p.all_count - count as count
	        from (
                SELECT count(distinct(case when err_type in ('LE01','LE02','LE03','LE04','LE05','LE06','LE07') then a.settle_list_id else null end)) as count,
                       count(distinct a.SETTLE_LIST_ID) as all_count,
                       SUBSTRING(b.b15,1,7) as month
                FROM som_setl_invy_chk_err_rcd a
                inner join som_init_hi_setl_invy_med_fee_info b
                on a.settle_list_id = b.id
                <if test="deptCode != null and deptCode != ''">
                    and b.B16C = #{deptCode}
                </if>
                <include refid="com.my.som.common.mapper.CommonMapper.fbAuth" />
                <![CDATA[
                    WHERE SUBSTRING( b.b15, 1, 7 ) >= #{begnDate,jdbcType=VARCHAR}
                    AND SUBSTRING( b.b15, 1, 7 ) <= #{expiDate,jdbcType=VARCHAR}
                    ]]>
                group by SUBSTRING(b.b15,1,7)
            ) p
        ) d
        on a.month = d.month
        left join (
            <!-- 编码错误 -->
            select p.month,
                   p.all_count - count as count
            from (
                    SELECT count(distinct(case when err_type in ('LE01') then a.settle_list_id else null end)) as count,
                                 count(distinct a.SETTLE_LIST_ID) as all_count,
                                 SUBSTRING(b.b15,1,7) as month
                    FROM som_setl_invy_chk_err_rcd a
                    inner join som_init_hi_setl_invy_med_fee_info b
                    on a.settle_list_id = b.id
                    <if test="deptCode != null and deptCode != ''">
                        and b.B16C = #{deptCode}
                    </if>
                    <include refid="com.my.som.common.mapper.CommonMapper.fbAuth" />
                    <![CDATA[
                    WHERE SUBSTRING( b.b15, 1, 7 ) >= #{begnDate,jdbcType=VARCHAR}
                    AND SUBSTRING( b.b15, 1, 7 ) <= #{expiDate,jdbcType=VARCHAR}
                    ]]>
                    group by SUBSTRING(b.b15,1,7)
            ) p
        ) e
        on a.month = e. month
        <![CDATA[
            where a.month >= #{begnDate,jdbcType=VARCHAR}
            and a.month <= #{expiDate,jdbcType=VARCHAR}
        ]]>
        order by a.month asc
    </select>

    <sql id="deptCode">
        <if test="deptCode != null and deptCode != ''">
            AND B16C = #{deptCode}
        </if>
    </sql>

    <sql id="keyDeptCode">
        <if test="deptCode != null and deptCode != ''">
            AND dscg_caty_codg_inhosp = #{deptCode}
        </if>
    </sql>
</mapper>
