<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.som.mapper.dataConfig.VersionMessagePromptMapper">
<!--  查询版本更新数据  -->
    <select id="queryData" resultType="com.my.som.vo.dataConfig.VersionMessagePromptVo">
        select
               a.ver as ver,
               a.`dscr_info` as content,
               a.updt_date as ym
        FROM som_sys_ver a
        where 1=1
        <if test="ym != null and ym != ''">
            AND SUBSTR(a.updt_date,1,7) = #{ym,jdbcType=VARCHAR}
        </if>
        <if test="ver != null and ver != ''">
            AND a.ver like "%"#{ver,jdbcType=VARCHAR}"%"
        </if>
    </select>
<!--  删除版本更新数据  -->
    <delete id="deleteData">
        DELETE FROM som_sys_ver
        WHERE ver = #{ver,jdbcType=VARCHAR}
    </delete>
<!--  新增版本更新数据  -->
    <insert id="addData">
        insert into som_sys_ver
        (ver,`DESCRIBE`,updt_date) VALUE (#{ver},#{content},#{ym})
    </insert>
<!--  修改版本更新数据  -->
    <update id="updateData">
        update som_sys_ver set `DESCRIBE`=#{content} WHERE ver=#{ver}
    </update>
<!--  查询用户是否查看历史更新数据  -->
    <select id="selectSys" resultType="com.my.som.vo.dataConfig.VersionMessagePromptVo">
        select username as username,
               message as message
        from som_back_user
    </select>
<!--   更新用户是否查看历史更新消息-->
    <update id="updateSys">
        <foreach collection="updatePersonList" item="itemUpdate" index="index" separator=";">
            update som_back_user
            <set>
                <if test="itemUpdate.message != null and itemUpdate.message != ''">
                    message=#{itemUpdate.message,jdbcType=VARCHAR}
                </if>
            </set>
            where username = #{itemUpdate.username,jdbcType=VARCHAR}
        </foreach>
    </update>
<!--    更新用户查看历史数据状态-->
    <update id="updateMessage">
        update som_back_user
        set message= #{message,jdbcType=VARCHAR}
        where username = #{username,jdbcType=VARCHAR}
    </update>
</mapper>