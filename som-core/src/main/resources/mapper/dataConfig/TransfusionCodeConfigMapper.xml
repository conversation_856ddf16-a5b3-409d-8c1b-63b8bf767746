<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.som.mapper.dataConfig.TransfusionCodeConfigMapper">
    <insert id="insertTransfusionCodeInfo">
        insert into som_bld_crsp (inhosp_bld_code, inhosp_bld_name, hi_bld_code, hi_bld_name, HOSPITAL_ID)
        values (#{inhospBldCode,jdbcType=VARCHAR}, #{inhospBldName,jdbcType=VARCHAR}, #{hiBldCode,jdbcType=VARCHAR}, #{hiBldName,jdbcType=VARCHAR}, #{hospitalId,jdbcType=VARCHAR})
    </insert>
    <update id="updateTransfusionCodeInfo">
        update som_bld_crsp <set>
        <if test="inhospBldCode != null and inhospBldCode != ''">
            inhosp_bld_code = #{inhospBldCode,jdbcType=VARCHAR},
        </if>
        <if test="inhospBldName != null and inhospBldName != ''">
            inhosp_bld_name = #{inhospBldName,jdbcType=VARCHAR},
        </if>
        <if test="hiBldCode != null and hiBldCode != ''">
            hi_bld_code = #{hiBldCode,jdbcType=VARCHAR},
        </if>
        <if test="hiBldName != null and hiBldName != ''">
            hi_bld_name = #{hiBldName,jdbcType=VARCHAR},
        </if>
        <if test="hospitalId != null and hospitalId != ''">
            HOSPITAL_ID = #{hospitalId,jdbcType=VARCHAR}
        </if>
    </set>
    where ID =#{id,jdbcType=VARCHAR}
    </update>
    <delete id="removeAll">
        delete from som_bld_crsp
        <where>
            <if test="hospitalId != null and hospitalId != ''">
                HOSPITAL_ID = #{hospitalId,jdbcType=VARCHAR},
            </if>
        </where>
    </delete>
    <update id="batchDoctorCode">
        insert into som_bld_crsp (inhosp_bld_code, inhosp_bld_name, hi_bld_code, hi_bld_name, HOSPITAL_ID)
        values <foreach collection="list" item="item"  separator=",">
            (trim(#{item.inhospBldCode,jdbcType=VARCHAR}),
             trim(#{item.inhospBldName,jdbcType=VARCHAR}),
             trim(#{item.hiBldCode,jdbcType=VARCHAR}),
             trim(#{item.hiBldName,jdbcType=VARCHAR}),
             trim(#{dto.hospitalId,jdbcType=VARCHAR}))
    </foreach>
    </update>
    <delete id="deleteTransfusionCodeInfo">
        delete from som_bld_crsp where ID = #{id,jdbcType=VARCHAR}
        <if test="hospitalId != null and hospitalId != ''">
            and HOSPITAL_ID = #{hospitalId,jdbcType=VARCHAR}
        </if>
    </delete>

    <select id="queryTransfusionCodeInfo" resultType="com.my.som.vo.dataConfig.TransfusionCodeVo">
        select ID as id, inhosp_bld_code as inhospBldCode, inhosp_bld_name as inhospBldName,
               hi_bld_code as hiBldCode, hi_bld_name as hiBldName,
               HOSPITAL_ID AS hospitalId
        from som_bld_crsp
        <where>
            <if test="inhospBldCode != null and inhospBldCode != ''">
                inhosp_bld_code = #{inhospBldCode,jdbcType=VARCHAR}
            </if>
            <if test="inhospBldName != null and inhospBldName != ''">
                and inhosp_bld_name like concat('%',#{inhospBldName,jdbcType=VARCHAR},'%')
            </if>
            <if test="hiBldCode != null and hiBldCode != ''">
                and hi_bld_code = #{hiBldCode,jdbcType=VARCHAR}
            </if>
            <if test="hiBldName != null and hiBldName != ''">
                and hi_bld_name like concat('%',#{hiBldName,jdbcType=VARCHAR},'%')
            </if>
            <if test="hospitalId != null and hospitalId != ''">
                and HOSPITAL_ID = #{hospitalId,jdbcType=VARCHAR}
            </if>
        </where>
    </select>
    <select id="queryByHosTransCode" resultType="com.my.som.vo.dataConfig.TransfusionCodeVo">
        select ID as id, inhosp_bld_code as inhospBldCode, inhosp_bld_name as inhospBldName,
               hi_bld_code as hiBldCode, hi_bld_name as hiBldName
        from som_bld_crsp
        where inhosp_bld_code = #{inhospBldCode,jdbcType=VARCHAR}
        <if test="hospitalId != null and hospitalId != ''">
            and HOSPITAL_ID = #{hospitalId,jdbcType=VARCHAR}
        </if>
    </select>
</mapper>
