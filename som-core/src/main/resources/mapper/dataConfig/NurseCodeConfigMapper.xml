<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.som.mapper.dataConfig.NurseCodeConfigMapper">
    <insert id="insertNurseCodeInfo">
        insert into som_hi_nurs_codg_crsp (hi_nurs_code, NAME, empno, HOSPITAL_ID)
        values (trim(#{hiNursCode,jdbcType=VARCHAR}), trim(#{name,jdbcType=VARCHAR}), trim(#{empno,jdbcType=VARCHAR}), trim(#{hospitalId,jdbcType=VARCHAR}))
    </insert>
    <update id="updateNurseCodeInfo">
        update som_hi_nurs_codg_crsp <set>
        <if test="hiNursCode != null and hiNursCode != ''">
            hi_nurs_code = trim(#{hiNursCode,jdbcType=VARCHAR}),
        </if>
        <if test="name != null and name != ''">
            NAME = trim(#{name,jdbcType=VARCHAR}),
        </if>
        <if test="empno != null and empno != ''">
            empno = trim(#{empno,jdbcType=VARCHAR})
        </if>
    </set>
        where ID = #{id,jdbcType=VARCHAR}
        <if test="hospitalId != null and hospitalId != ''">
            and HOSPITAL_ID = #{hospitalId,jdbcType=VARCHAR}
        </if>
    </update>
    <update id="removeAll">
        delete from som_hi_nurs_codg_crsp
        <where>
            <if test="hospitalId != null and hospitalId != ''">
                HOSPITAL_ID = #{hospitalId,jdbcType=VARCHAR}
            </if>
        </where>
    </update>
    <insert id="batchDoctorCode">
        insert into som_hi_nurs_codg_crsp (hi_nurs_code, NAME, empno, HOSPITAL_ID) values
        <foreach collection="list" item="item" index="index" separator=","> (
            trim(#{item.hiNursCode,jdbcType=VARCHAR}),
            trim(#{item.name,jdbcType=VARCHAR}),
            trim(#{item.empno,jdbcType=VARCHAR}),
            trim(#{dto.hospitalId,jdbcType=VARCHAR})
            )
        </foreach>
    </insert>
    <delete id="deleteNurseCodeInfo">
        delete from som_hi_nurs_codg_crsp where ID = #{id,jdbcType=VARCHAR}
        <if test="hospitalId != null and hospitalId != ''">
            and HOSPITAL_ID = #{hospitalId,jdbcType=VARCHAR}
        </if>
    </delete>

    <select id="selectAll" resultType="com.my.som.vo.dataConfig.NurseCodeVo">
        select ID as id, trim(hi_nurs_code) as hiNursCode, trim(NAME) as name, trim(empno) as empno, trim(HOSPITAL_ID) AS hospitalId from som_hi_nurs_codg_crsp
        <where>
            <if test="name != null and name != ''">
                NAME like concat('%',#{name,jdbcType=VARCHAR},'%')
            </if>
            <if test="hiNursCode != null and hiNursCode != ''">
                and hi_nurs_code = #{hiNursCode,jdbcType=VARCHAR}
            </if>
            <if test="empno != null and empno != ''">
                and empno = #{empno,jdbcType=VARCHAR}
            </if>
            <if test="hospitalId != null and hospitalId != ''">
                and HOSPITAL_ID = #{hospitalId,jdbcType=VARCHAR}
            </if>
        </where>
    </select>
    <select id="selectByCode" resultType="com.my.som.vo.dataConfig.NurseCodeVo">
        select ID as id, trim(hi_nurs_code) as hiNursCode, trim(NAME) as name, trim(empno) as empno from som_hi_nurs_codg_crsp
        where empno = #{empno,jdbcType=VARCHAR}
        <if test="hospitalId != null and hospitalId != ''">
            and HOSPITAL_ID = #{hospitalId,jdbcType=VARCHAR}
        </if>
    </select>

    <select id="selectNameByCode" resultType="string">
        select trim(NAME)  from som_hi_nurs_codg_crsp
        where empno = #{respNursCode,jdbcType=VARCHAR}
    </select>
</mapper>
