<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.som.mapper.dataConfig.ViewHospitalMapper">
    <insert id="insertHospitalInfo">
        INSERT INTO som_hosp_info (HOSPITAL_ID, medins_name, hosp_lv, admdvs_code, ACTIVE_FLAG, hosp_cof)
        VALUES (#{hospitalId}, #{medinsName}, #{hospLv}, #{admdvsCode}, #{activeFlag}, #{hospCof})
    </insert>
    <update id="updateHospitalInfo">
        UPDATE som_hosp_info
        SET hosp_lv     = #{hospLv},
            admdvs_code = #{admdvsCode},
            ACTIVE_FLAG = #{activeFlag},
            hosp_cof    = #{hospCof}
        WHERE ID = #{id}
    </update>

    <!-- 更新医院用户名 -->
    <update id="updateHospitalUsername">
        UPDATE som_hosp_info
        SET USERNAME = #{username,jdbcType=VARCHAR}
        WHERE medins_name = #{nknm,jdbcType=VARCHAR}
    </update>
    <delete id="deleteHospitalInfo">
        DELETE
        FROM som_hosp_info
        WHERE ID = #{id}
    </delete>
    <select id="queryHospitalInfo" resultType="com.my.som.vo.dataConfig.ViewHospitalVo">
        select ID as id,
        HOSPITAL_ID AS hospitalId,
        medins_name AS medinsName,
        hosp_lv AS hospLv,
        admdvs_code AS admdvsCode,
        hosp_cof AS hospCof,
        username,
        trim(pre_group_url) as preGroupURl
        from som_hosp_info WHERE ACTIVE_FLAG = 1
        <if test="hospital != null and hospital != ''">
            AND HOSPITAL_ID = #{hospital} OR medins_name = #{hospital}
        </if>
        <if test="hospitalId != null and hospitalId != ''">
            AND HOSPITAL_ID = #{hospitalId,jdbcType=VARCHAR}
        </if>
    </select>

    <!--查询床日疾病诊断配置-->
    <select id="queryBedDrgDiseCodeList" parameterType="com.my.som.grouppay.dto.BedDrgDiseCodeCfgDto"
            resultType="com.my.som.grouppay.vo.BedDrgDiseCodeCfgVo"
    >
        select bed_type,
               main_diag_codg
        from bed_diag_codg_cfg
        where vali_flag = '1'
    </select>
</mapper>
