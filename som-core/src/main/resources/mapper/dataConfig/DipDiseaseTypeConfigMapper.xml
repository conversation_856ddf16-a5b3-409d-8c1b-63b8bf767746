<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.som.mapper.dataConfig.DipDiseaseTypeConfigMapper">

    <!-- 查询中医优势病种信息 -->
    <select id="queryDipChineseDisease" resultType="com.my.som.vo.dataConfig.DipDiseaseTypeVo">
        SELECT a.ID AS id,
                a.dip_code as  dipCodg,
        a.dip_name AS dipName,
               a.tcm_dise_codg AS chineseDiseaseCode,
               a.tcm_dise_name AS chineseDiseaseName,
               a.hi_codg AS medicalCode,
               a.hi_name AS medicalName,
               a.ACTIVE_FLAG AS activeFlag,
               a.HOSPITAL_ID AS hospitalId
        FROM som_hi_tcm_codg_crsp a
        WHERE a.ACTIVE_FLAG = 1
        <if test="chineseCode != null and chineseCode != ''">
            AND a.tcm_dise_codg LIKE CONCAT('%', #{chineseCode,jdbcType=VARCHAR},'%')
        </if>
        <if test="medicalCode != null and medicalCode != ''">
            AND a.hi_codg LIKE CONCAT('%', #{medicalCode,jdbcType=VARCHAR},'%')
        </if>
        <include refid="hospitalId" />
    </select>

    <!-- 查询基层病种信息 -->
    <select id="queryDipBaseDisease" resultType="com.my.som.vo.dataConfig.DipDiseaseTypeVo">
        select a.ID AS id,
               a.dip_codg AS dipCodg,
                a.dip_name as dipName,
               a.dip_diag_codg_dis_gp AS dipDiagCodg,
               a.dip_diag_name_dis_gp AS dipDiagName,
               a.dip_oprt_codg AS dipOprtCodg,
               a.dip_oprt_name AS dipOprtName,
               a.is_used_asst_list_grp_rcd AS usedAsstList,
               a.is_sd_dise_grp_list AS isStabilizeDisease,
               a.dise_type AS diseType
        from som_dip_grp_detl a
        where a.active_flag = '1'
        <if test="dipCodg != null and dipCodg != ''">
            AND a.dip_codg = #{dipCodg,jdbcType=VARCHAR}
        </if>
        <include refid="hospitalId" />
    </select>

    <!-- 查询重点专科信息 -->
    <select id="queryDipProfessionalDisease" resultType="com.my.som.vo.dataConfig.DipDiseaseTypeVo">
        select a.ID AS id,
               a.inhosp_dept_codg AS inhospDeptCodg,
               a.inhosp_dept_name AS inhospDeptName,
               a.key_spcy_name AS keySpcyName,
               a.key_spcy_type AS keySpcyType
        from som_dip_key_spcy_info a
        where a.active_flag = '1'
        <if test="deptCode != null and deptCode != ''">
            AND a.inhosp_dept_codg = #{deptCode,jdbcType=VARCHAR}
        </if>
        <include refid="hospitalId" />
    </select>

    <!-- 查询病种系数信息 -->
    <select id="queryDipDiseaseCoefficient" resultType="com.my.som.vo.dataConfig.DipDiseaseTypeVo">
        SELECT a.ID AS id,
               a.dise_type AS diseaseCFTType,
               a.hosp_lv AS hospLv,
               a.hosp_ratg AS hospRatg,
               a.key_disc AS keyDisc,
               a.dise_cof AS diseCof
        FROM som_dip_grp_cof a
        WHERE 1 = 1
        <if test="diseaseCFTType != null and diseaseCFTType != ''">
          AND a.dise_type = #{diseaseCFTType}
        </if>
        <include refid="hospitalId" />
    </select>

    <!-- 获取所有有数据月份 -->
    <select id="queryDipMonthData" resultType="java.lang.String">
        SELECT SUBSTR(dscg_time,1,7) AS month
        FROM
        <choose>
            <when test="queryType == 1">
                som_dip_grp_info
            </when>
            <when test="queryType == 3">
                som_drg_grp_info
            </when>
        </choose>
        WHERE SUBSTR(dscg_time,1,4) = #{standardYear,jdbcType=VARCHAR}
        GROUP BY SUBSTR(dscg_time,1,7)
        ORDER BY SUBSTR(dscg_time,1,7)
    </select>

    <!-- 查询非稳定病种 -->
    <select id="queryUnstableDisease" resultType="com.my.som.vo.dataConfig.DipDiseaseTypeVo">
        SELECT a.ID AS id,
               a.dip_codg AS dipCodg,
               a.DIP_NAME AS dipName,
               a.ACTIVE_FLAG AS activeFlag
        FROM som_dip_grp a
        WHERE a.ACTIVE_FLAG = '1'
        <if test="dipCodg != null and dipCodg != ''">
            a.dip_codg = #{dipCodg}
        </if>
    </select>

    <!-- 插入中医优势病种信息 -->
    <insert id="insertDipChineseDisease">
        INSERT INTO som_hi_tcm_codg_crsp
            (
             tcm_dise_codg, tcm_dise_name, hi_codg, hi_name, ACTIVE_FLAG
            ) VALUES
                     (
                      #{chineseCode}, #{chineseName}, #{medicalCode}, #{medicalName}, #{activeFlag}
                     )
    </insert>

    <!-- 插入基层病种信息 -->
    <insert id="insertDipBaseDisease">
        INSERT INTO som_dip_grp_detl
            (
             dip_codg,
             dip_diag_codg_dis_gp, dip_diag_name_dis_gp,
             dip_oprt_codg, dip_oprt_name,
             is_used_asst_list_grp_rcd, is_sd_dise_grp_list,
             dise_type, ACTIVE_FLAG
            ) VALUES
                     (
                      #{dipCodg},
                      #{dipDiagCodg}, #{dipDiagName},
                      #{dipOprtCodg}, #{dipOprtName},
                      #{usedAsstList}, #{isStabilizeDisease},
                      #{diseType}, #{activeFlag}
                     )
    </insert>

    <!-- 插入重点专科信息 -->
    <insert id="insertDipProfessionalDisease">
        INSERT INTO som_dip_key_spcy_info
            (
             inhosp_dept_codg, inhosp_dept_name,
             key_spcy_name, key_spcy_type,
             ACTIVE_FLAG
            ) VALUES
                     (
                      #{inhospDeptCodg}, #{inhospDeptName},
                      #{keySpcyName}, #{keySpcyType},
                      #{activeFlag}
                     )
    </insert>

    <!-- 插入病种系数信息 -->
    <insert id="insertDipDiseaseCoefficient">
        INSERT INTO som_dip_grp_cof
            (
             dise_type,
             hosp_lv, hosp_ratg, key_disc, dise_cof
            ) VALUES
                     (
                      #{diseaseCFTType},
                      #{hospLv}, #{hospRatg}, #{keyDisc}, #{diseCof}
                     )
    </insert>

    <!-- 插入非稳定病种 -->
    <insert id="insertUnstableDisease">
        INSERT INTO som_dip_grp
            (
                dip_codg,
                DIP_NAME,
                ACTIVE_FLAG
            ) VALUES
                     (
                      #{dipCodg},
                      #{dipName},
                      #{activeFlag}
                     )
    </insert>

    <!-- 删除中医优势病种信息 -->
    <delete id="deleteDipChineseDisease">
        DELETE FROM som_hi_tcm_codg_crsp WHERE ID = #{id}
    </delete>

    <!-- 删除基层病种信息 -->
    <delete id="deleteDipBaseDisease">
        DELETE FROM som_dip_grp_detl WHERE ID = #{id}
    </delete>

    <!-- 删除重点专科信息 -->
    <delete id="deleteDipProfessionalDisease">
        DELETE FROM som_dip_key_spcy_info WHERE ID = #{id}
    </delete>

    <!-- 删除病种系数信息 -->
    <delete id="deleteDipDiseaseCoefficient">
        DELETE FROM som_dip_grp_cof WHERE ID = #{id}
    </delete>

    <!-- 删除当前年份分值数据 -->
    <delete id="deleteCurYearScoreData">
        DELETE FROM
        <choose>
            <when test="queryType == 1">
                som_dip_sco
            </when>
            <when test="queryType == 3">
                som_drg_sco
            </when>
        </choose>
        WHERE SUBSTR(ym,1,4) = #{standardYear,jdbcType=VARCHAR}
        <if test="hospitalId != null and hospitalId != ''">
            AND HOSPITAL_ID = #{hospitalId,jdbcType=VARCHAR}
        </if>
    </delete>

    <!-- 删除非稳定病种 -->
    <delete id="deleteUnstableDisease">
        delete from som_dip_grp WHERE ID = #{id}
    </delete>

    <!-- 更新中医优势病种信息 -->
    <update id="updateDipChineseDisease">
        UPDATE som_hi_tcm_codg_crsp SET tcm_dise_codg = #{chineseCode},
                                           tcm_dise_name = #{chineseName},
                                           hi_codg = #{medicalCode},
                                           hi_name = #{medicalName}
        WHERE ID = #{id}
    </update>

    <!-- 更新基层病种信息 -->
    <update id="updateDipBaseDisease">
        UPDATE som_dip_grp_detl SET dip_codg = #{dipCodg},
                                        dip_diag_codg_dis_gp = #{dipDiagCodg},
                                        dip_diag_name_dis_gp = #{dipDiagName},
                                        dip_oprt_codg = #{dipOprtCodg},
                                        dip_oprt_name = #{dipOprtName},
                                        is_used_asst_list_grp_rcd = #{usedAsstList},
                                        is_sd_dise_grp_list = #{isStabilizeDisease},
                                        dise_type = #{diseType}
        WHERE ID = #{id}
    </update>

    <!-- 更新重点专科信息 -->
    <update id="updateDipProfessionalDisease">
        UPDATE som_dip_key_spcy_info SET inhosp_dept_codg = #{inhospDeptCodg},
                                                inhosp_dept_name = #{inhospDeptName},
                                                key_spcy_name = #{keySpcyName},
                                                key_spcy_type = #{keySpcyType}
        WHERE ID = #{id}
    </update>

    <!-- 更新病种系数信息 -->
    <update id="updateDipDiseaseCoefficient">
        UPDATE som_dip_grp_cof SET dise_type = #{diseaseCFTType},
                                               hosp_lv = #{hospLv},
                                               hosp_ratg = #{hospRatg},
                                               key_disc = #{keyDisc},
                                               dise_cof = #{diseCof} WHERE ID = #{id}
    </update>

    <!-- 更新非稳定病种 -->
    <update id="updateUnstableDisease">
        UPDATE som_dip_grp SET dip_codg = #{dipCodg},
                                            DIP_NAME = #{dipName} WHERE ID = #{id}
    </update>

    <select id="queryAreaData" resultType="java.util.Map">
        SELECT STANDARD_YEAR AS year,
               CONVERT(AES_DECRYPT(UNHEX(AREA_AVG_COST),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) USING utf8) AS areaAvgCost
        FROM som_regn_year_sum_data
    </select>

    <select id="queryDipAuxDisease" resultType="com.my.som.vo.dataConfig.DipDiseaseTypeVo">
        SELECT
            dip_codg dipCodg,
            aux_type auxType,
            aux_xs auxXs
        FROM som_dip_grp_aux
        WHERE 1 = 1
        <include refid="hospitalId" />
    </select>

    <sql id="hospitalId">
        <if test="hospitalId != null and hospitalId != ''">
            AND HOSPITAL_ID = #{ hospitalId, jdbcType=VARCHAR }
        </if>
    </sql>

</mapper>
