<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.som.mapper.dataConfig.SomDeptCrspConfigMapper">
    <resultMap id="BaseResultMap" type="com.my.som.vo.dataConfig.SomDeptConfigVo">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="code" jdbcType="VARCHAR" property="code"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="dept_lv" jdbcType="INTEGER" property="dept_lv"/>
        <result column="prnt_dept_codg" jdbcType="VARCHAR" property="prnt_dept_codg"/>
        <result column="type" jdbcType="INTEGER" property="type"/>
        <result column="std_dept_codg" jdbcType="VARCHAR" property="std_dept_codg"/>
        <result column="std_dept_name" jdbcType="VARCHAR" property="std_dept_name"/>
        <result column="dept_area" jdbcType="DECIMAL" property="dept_area"/>
        <result column="is_oprn_dept" jdbcType="VARCHAR" property="is_oprn_dept"/>
        <result column="hospital_id" jdbcType="VARCHAR" property="hospital_id"/>
        <result column="active_flag" jdbcType="VARCHAR" property="active_flag"/>
    </resultMap>

    <!--查询科室转码配置信息-->
    <select id="queryDeptTransCode" parameterType="com.my.som.dto.dataConfig.SomDeptConfigDto"
            resultMap="BaseResultMap">
        select id,
               code,
               name,
               dept_lv,
               prnt_dept_codg,
               type,
               bed_cnt,
               std_dept_codg,
               std_dept_name,
               dept_area,
               is_oprn_dept,
               hospital_id,
               active_flag
        from som_dept
        where active_flag = '1'
          and hospital_id = #{hospital_id,jdbcType=VARCHAR}
    </select>

    <!--查询科室配置信息-->
    <select id="queryHospDeptCodeConfigList" parameterType="com.my.som.dto.dataConfig.HospDeptCodeDto"
            resultType="com.my.som.vo.dataConfig.HospDeptCodeVo"
    >
        select id,
               code          as inhospDeptCode,
               name          as inhospDeptName,
               std_dept_codg as hiDeptCode,
               std_dept_name as hiDeptName,
               hospital_id   as hospital_id,
               active_flag   as active_flag
        from som_dept
        where active_flag = '1'
        <if test="hospital_id != null and hospital_id != ''">
            and hospital_id = #{hospital_id,jdbcType=VARCHAR}
        </if>
        <if test="inhospDeptCode != null and inhospDeptCode != ''">
            and `CODE` = #{inhospDeptCode,jdbcType=VARCHAR}
        </if>
        <if test="inhospDeptName != null and inhospDeptName != ''">
            AND `NAME` LIKE CONCAT('%',#{inhospDeptName,jdbcType=VARCHAR},'%')
        </if>
    </select>

    <!--查询科室Id信息-->
    <select id="selectBasDeptOrdId"  resultType="com.my.som.model.SomBasDept" >
        SELECT org_id,prnt_dept_id,dept_id_path,dept_name_path FROM `som_bas_dept` ORDER BY org_id DESC
    </select>
</mapper>
