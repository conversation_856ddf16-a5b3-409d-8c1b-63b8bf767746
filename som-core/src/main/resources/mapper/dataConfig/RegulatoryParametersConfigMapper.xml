<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.som.mapper.dataConfig.RegulatoryParametersConfigMapper">
    <insert id="insertDiseaseGroup">
        INSERT INTO cfg_regulatory_parameters (`CODE`,`NAME`,TYPE,MODIFIER,MODIFICATION_TIME,HOSPITAL_ID)
        VALUES (#{code,jdbcType=VARCHAR},#{name,jdbcType=VARCHAR},#{type,jdbcType=VARCHAR},#{modifier,jdbcType=VARCHAR},CURRENT_TIMESTAMP,#{hospitalId,jdbcType=VARCHAR})
    </insert>

    <insert id="insertDiagnosis">
        INSERT INTO cfg_regulatory_parameters (`CODE`,`NAME`,TYPE,M<PERSON><PERSON>IE<PERSON>,M<PERSON><PERSON><PERSON>ATION_TIME,HOSPITAL_ID)
        VALUES (#{code,jdbcType=VARCHAR},#{name,jdbcType=VARCHAR},#{type,jdbcType=VARCHAR},#{modifier,jdbcType=VARCHAR},CURRENT_TIMESTAMP,#{hospitalId,jdbcType=VARCHAR})
    </insert>

    <insert id="insertOperation">
        INSERT INTO cfg_regulatory_parameters (`CODE`,`NAME`,TYPE,MODIFIER,MODIFICATION_TIME,HOSPITAL_ID)
        VALUES (#{code,jdbcType=VARCHAR},#{name,jdbcType=VARCHAR},#{type,jdbcType=VARCHAR},#{modifier,jdbcType=VARCHAR},CURRENT_TIMESTAMP,#{hospitalId,jdbcType=VARCHAR})
    </insert>

    <insert id="batchDiseaseGroup" useGeneratedKeys="true" keyColumn="ID">
        INSERT INTO cfg_regulatory_parameters (`CODE`,`NAME`,TYPE,MODIFIER,MODIFICATION_TIME,HOSPITAL_ID) values
        <foreach collection="list" item="item" index="index" separator=",">
            (
             trim(#{item.code}),
             trim(#{item.name}),
             trim(1),
             trim(#{dto.modifier}),
             trim(CURRENT_TIMESTAMP),
             trim(#{dto.hospitalId})
            )
        </foreach>
    </insert>

    <insert id="batchDiagnosis" useGeneratedKeys="true" keyColumn="ID">
        INSERT INTO cfg_regulatory_parameters (`CODE`,`NAME`,TYPE,MODIFIER,MODIFICATION_TIME,HOSPITAL_ID) values
            <foreach collection="list" item="item" index="index" separator=",">
                (
                trim(#{item.code}),
                trim(#{item.name}),
                trim(2),
                trim(#{dto.modifier}),
                trim(CURRENT_TIMESTAMP),
                trim(#{dto.hospitalId})
                )
            </foreach>
    </insert>

    <insert id="batchOperation" useGeneratedKeys="true" keyColumn="ID">
        INSERT INTO cfg_regulatory_parameters (`CODE`,`NAME`,TYPE,MODIFIER,MODIFICATION_TIME,HOSPITAL_ID) values
            <foreach collection="list" item="item" index="index" separator=",">
                (
                trim(#{item.code}),
                trim(#{item.name}),
                trim(3),
                trim(#{dto.modifier}),
                trim(CURRENT_TIMESTAMP),
                trim(#{dto.hospitalId})
                )
            </foreach>
    </insert>

    <update id="updateDiseaseGroup">
        UPDATE cfg_regulatory_parameters
        <set>
            <if test="code != null and code != ''">
                `CODE` = #{code,jdbcType=VARCHAR},
            </if>
            <if test="name != null and name != ''">
                `NAME` = #{name,jdbcType=VARCHAR},
            </if>
            <if test="modifier != null and modifier != ''">
                MODIFIER = #{modifier,jdbcType=VARCHAR}
            </if>
        </set>
        where ID = #{id}
        <if test="hospitalId != null and hospitalId != ''">
            AND HOSPITAL_ID = #{hospitalId,jdbcType=VARCHAR}
        </if>
    </update>

    <delete id="deleteDiseaseGroup">
        DELETE FROM cfg_regulatory_parameters WHERE ID = #{id}
        <if test="hospitalId != null and hospitalId != ''">
            AND HOSPITAL_ID = #{hospitalId,jdbcType=VARCHAR}
        </if>
    </delete>

    <select id="selectDiseaseGroupData" resultType="com.my.som.vo.dataConfig.RegulatoryParametersConfigVo">
        SELECT
            ID AS id,
            `CODE` AS code,
            `NAME` AS name,
            MODIFIER AS modifier,
            MODIFICATION_TIME AS modificationTime
        FROM cfg_regulatory_parameters
        <where>
            TYPE = '1'
            <if test="code != null and code != ''">
                AND `CODE` LIKE CONCAT('%',#{code,jdbcType=VARCHAR},'%')
            </if>
            <if test="name != null and name != ''">
                AND `NAME` LIKE CONCAT('%',#{name,jdbcType=VARCHAR},'%')
            </if>
            <if test="modifier != null and modifier != ''">
                AND MODIFIER LIKE CONCAT('%',#{modifier,jdbcType=VARCHAR},'%')
            </if>
            <if test="hospitalId != null and hospitalId != ''">
                AND HOSPITAL_ID = #{hospitalId,jdbcType=VARCHAR}
            </if>
        </where>
    </select>

    <select id="selectDiagnosisData" resultType="com.my.som.vo.dataConfig.RegulatoryParametersConfigVo">
        SELECT
        ID AS id,
        `CODE` AS code,
        `NAME` AS name,
        MODIFIER AS modifier,
        MODIFICATION_TIME AS modificationTime
        FROM cfg_regulatory_parameters
        <where>
            TYPE = '2'
            <if test="code != null and code != ''">
                AND `CODE` LIKE CONCAT('%',#{code,jdbcType=VARCHAR},'%')
            </if>
            <if test="name != null and name != ''">
                AND `NAME` LIKE CONCAT('%',#{name,jdbcType=VARCHAR},'%')
            </if>
            <if test="modifier != null and modifier != ''">
                AND MODIFIER LIKE CONCAT('%',#{modifier,jdbcType=VARCHAR},'%')
            </if>
            <if test="hospitalId != null and hospitalId != ''">
                AND HOSPITAL_ID = #{hospitalId,jdbcType=VARCHAR}
            </if>
        </where>
    </select>

    <select id="selectOperationData" resultType="com.my.som.vo.dataConfig.RegulatoryParametersConfigVo">
        SELECT
        ID AS id,
        `CODE` AS code,
        `NAME` AS name,
        MODIFIER AS modifier,
        MODIFICATION_TIME AS modificationTime
        FROM cfg_regulatory_parameters
        <where>
            TYPE = '3'
            <if test="code != null and code != ''">
                AND `CODE` LIKE CONCAT('%',#{code,jdbcType=VARCHAR},'%')
            </if>
            <if test="name != null and name != ''">
                AND `NAME` LIKE CONCAT('%',#{name,jdbcType=VARCHAR},'%')
            </if>
            <if test="modifier != null and modifier != ''">
                AND MODIFIER LIKE CONCAT('%',#{modifier,jdbcType=VARCHAR},'%')
            </if>
            <if test="hospitalId != null and hospitalId != ''">
                AND HOSPITAL_ID = #{hospitalId,jdbcType=VARCHAR}
            </if>
        </where>
    </select>
    <select id="selectDiseaseGroup" resultType="com.my.som.vo.dataConfig.RegulatoryParametersConfigVo">
        SELECT
        DISTINCT
        drg_codg AS code,
        DRG_NAME AS name
        FROM som_drg_name
        <where>
            <if test="code != null and code != ''">
                AND drg_codg LIKE CONCAT('%',#{code,jdbcType=VARCHAR},'%')
            </if>
            <if test="name != null and name != ''">
                AND DRG_NAME LIKE CONCAT('%',#{name,jdbcType=VARCHAR},'%')
            </if>
        </where>
    </select>
    <select id="selectDiagnosis" resultType="com.my.som.vo.dataConfig.RegulatoryParametersConfigVo">
        SELECT
        DISTINCT
        crsp_icd_codg AS code,
        crsp_icd_name AS name
        FROM som_codg_crsp
        <where>
            crsp_icd_codg_ver = 10 AND ICD_TYPE = 'ICD-10'
            <if test="code != null and code != ''">
                AND crsp_icd_codg LIKE CONCAT('%',#{code,jdbcType=VARCHAR},'%')
            </if>
            <if test="name != null and name != ''">
                AND crsp_icd_name LIKE CONCAT('%',#{name,jdbcType=VARCHAR},'%')
            </if>
        </where>
    </select>
    <select id="selectOperation" resultType="com.my.som.vo.dataConfig.RegulatoryParametersConfigVo">
        SELECT
        DISTINCT
        crsp_icd_codg AS code,
        crsp_icd_name AS name
        FROM som_codg_crsp
        <where>
            crsp_icd_codg_ver = 10 AND ICD_TYPE = 'ICD-9'
            <if test="code != null and code != ''">
                AND crsp_icd_codg LIKE CONCAT('%',#{code,jdbcType=VARCHAR},'%')
            </if>
            <if test="name != null and name != ''">
                AND crsp_icd_name LIKE CONCAT('%',#{name,jdbcType=VARCHAR},'%')
            </if>
        </where>
    </select>
</mapper>