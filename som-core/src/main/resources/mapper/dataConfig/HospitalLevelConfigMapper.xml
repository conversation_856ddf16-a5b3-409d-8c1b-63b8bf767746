<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.som.mapper.dataConfig.HospitalLevelConfigMapper">

    <!-- 查询医院信息 -->
    <select id="queryHospital" resultType="com.my.som.vo.dataConfig.HospitalDataVo">
        select HOSPITAL_ID as hospitalId,
               medins_name as medinsName,
               hosp_lv_chn as hospLvChn,
               hosp_lv as hospLv
        from som_hosp_info
        <where>
            <if test="hospitalId != null and hospitalId != ''">
                and HOSPITAL_ID = #{hospitalId}
            </if>
            <if test="medinsName != null and medinsName != ''">
                 and medins_name like concat('%',#{medinsName},'%')
            </if>
            <if test="hospLvChn != null and hospLvChn != ''">
                and hosp_lv_chn = #{hospLvChn}
            </if>
        </where>
    </select>

    <select id="queryHospitalLevel" resultType="com.my.som.vo.dataConfig.HospLvVo">
        select labl_name as hospLvChn,
               data_val as hospLv
        from som_sys_code where code_type = 'YYJB'
    </select>

    <select id="queryHospitalLevelByCon" resultType="com.my.som.vo.dataConfig.HospLvVo">
        select labl_name as hospLvChn,
               data_val as hospLv
        from som_sys_code where code_type = 'YYJB' and data_val = #{type}
    </select>

    <update id="updateHospitalLevel">
        update som_hosp_info set hosp_lv = #{vo.hospLv}, hosp_lv_chn = #{vo.hospLvChn} where HOSPITAL_ID = #{dto.hospitalId}
    </update>
</mapper>