<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.som.mapper.dataConfig.HospitalCoefficientSMapper">
    <select id="queryHC" resultType="com.my.som.vo.dataConfig.HospCofVo">
        select
               HOSPITAL_ID AS hospitalId,
               adjm_cof AS adjm_cof
        from cfg_hospital_coefficient
        where HOSPITAL_ID=#{hospitalId};
    </select>
    <update id="updateHC">
        update cfg_hospital_coefficient
        set adjm_cof=#{adjm_cof}
        where HOSPITAL_ID=#{hospitalId};
    </update>
    <insert id="insertHC">
        insert into cfg_hospital_coefficient (HOSPITAL_ID,adjm_cof)
        value (#{hospitalId},#{adjm_cof});
    </insert>
    <delete id="deleteHC">
        delete from cfg_hospital_coefficient
        where HOSPITAL_ID=#{hospitalId};
    </delete>
</mapper>