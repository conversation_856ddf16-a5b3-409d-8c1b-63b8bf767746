<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.som.mapper.dataConfig.DoctorCodeConfigMapper">
    <insert id="insertDoctorCodeInfo" useGeneratedKeys="true" keyColumn="ID" keyProperty="id">
        insert into som_hi_dr_crsp (hi_dr_code, oper_dr_code, NAME, HOSPITAL_ID)
        values (trim(#{hiDrCode,jdbcType=VARCHAR}), trim(#{operDrCode,jdbcType=VARCHAR}),
                trim(#{name,jdbcType=VARCHAR}), trim(#{hospitalId,jdbcType=VARCHAR}))
    </insert>
    <update id="updateDoctorCodeInfo">
        update som_hi_dr_crsp
        <set>
            <if test="hiDrCode != null and hiDrCode != ''">
                hi_dr_code=trim(#{hiDrCode,jdbcType=VARCHAR}),
            </if>
            <if test="operDrCode != null and operDrCode != ''">
                oper_dr_code=trim(#{operDrCode,jdbcType=VARCHAR}),
            </if>
            <if test="name != null and name != ''">
                NAME=trim(#{name,jdbcType=VARCHAR})
            </if>
        </set>
        where ID = #{id}
        <if test="hospitalId != null and hospitalId != ''">
            and HOSPITAL_ID = #{hospitalId,jdbcType=VARCHAR}
        </if>
    </update>
    <update id="removeAll">
        delete from som_hi_dr_crsp
        <where>
            <if test="hospitalId != null and hospitalId != ''">
                HOSPITAL_ID = #{hospitalId,jdbcType=VARCHAR}
            </if>
        </where>
    </update>
    <insert id="batchDoctorCode" useGeneratedKeys="true" keyColumn="ID">
        insert into som_hi_dr_crsp (hi_dr_code,oper_dr_code,NAME,HOSPITAL_ID) values
        <foreach collection="list" item="item" index="index" separator=",">(
            trim(#{item.hiDrCode}),
            trim(#{item.operDrCode}),
            trim(#{item.name}),
            trim(#{dto.hospitalId})
            )
        </foreach>
    </insert>
    <delete id="deleteDoctorCodeInfo">
        delete from som_hi_dr_crsp where ID = #{id}
        <if test="hospitalId != null and hospitalId != ''">
            and HOSPITAL_ID = #{hospitalId,jdbcType=VARCHAR}
        </if>
    </delete>

    <select id="queryDoctorCodeInfo" resultType="com.my.som.vo.dataConfig.DrCodgVo">
        SELECT
        ID AS id,
        trim(hi_dr_code) AS hiDrCode,
        trim(oper_dr_code) AS operDrCode,
        trim(NAME) AS NAME,
        trim(HOSPITAL_ID) AS hospitalId
        FROM
        som_hi_dr_crsp
        <where>
            <if test="hiDrCode != null and hiDrCode != ''">
                AND hi_dr_code LIKE CONCAT('%', #{hiDrCode,jdbcType=VARCHAR},'%')
            </if>
            <if test="operDrCode != null and operDrCode != ''">
                AND oper_dr_code LIKE CONCAT('%', #{operDrCode,jdbcType=VARCHAR},'%')
            </if>
            <if test="name != null and name != ''">
                AND NAME LIKE CONCAT('%', #{name,jdbcType=VARCHAR},'%')
            </if>
            <if test="hospitalId != null and hospitalId != ''">
                and HOSPITAL_ID = #{hospitalId,jdbcType=VARCHAR}
            </if>
        </where>
    </select>
    <select id="queryByPerformerDoctorCode" resultType="com.my.som.vo.dataConfig.DrCodgVo">
        SELECT
        ID AS id,
        trim(hi_dr_code) AS hiDrCode,
        trim(oper_dr_code) AS operDrCode,
        trim(NAME) AS NAME
        FROM
        som_hi_dr_crsp
        where oper_dr_code = #{operDrCode,jdbcType=VARCHAR}
        <if test="hospitalId != null and hospitalId != ''">
            and HOSPITAL_ID = #{hospitalId,jdbcType=VARCHAR}
        </if>
    </select>
</mapper>
