<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.som.mapper.dataConfig.AreaBenchmarkConfigMapper">
    <insert id="insertAreaBenchmarkInfoDip">
        INSERT INTO som_dip_regn_standard
        (
            STANDARD_YEAR,
            dip_codg, DIP_NAME,
            is_used_asst_list, asst_list_age_grp,
            asst_list_dise_sev_deg, asst_list_tmor_sev_deg,
            dip_diag_codg, dip_diag_name,
            dip_oprt_codg, dip_oprt_name,
            dip_wt, standard_avg_fee,
            dip_standard_avg_fee_same_lv, last_year_avg_fee,
            refer_sco, adjm_cof,
            hosp_lv, ACTIVE_FLAG,
            uplmt_mag,lowlmt_mag

        ) VALUES
            (
                #{standardYear},
                #{dipCodg}, #{dipName},
                #{isUsedAsstList}, #{asstListAgeGrp},
                #{auxiliaryIlness}, #{asstListTmorSevDeg},
                #{dipDiagCodg}, #{dipDiagName},
                #{dipOprtCodg}, #{dipOprtName},
                #{dipWt}, #{standardAvgFee},
                #{dipStandardAvgFeeSameLv}, #{lastYearAvgFee},
                #{refer_sco}, #{adjm_cof},
                #{hospLv}, #{activeFlag},
                #{uplmtMag},#{lowlmtMag}
            )
    </insert>
    <insert id="insertAreaBenchmarkInfoDrg">
        INSERT INTO som_drg_regn_standard
        (
            STANDARD_YEAR,
            drg_codg, DRG_NAME,
            mdc_standard_ave_hosp_day, mdc_standard_avg_fee,
            adrg_standard_ave_hosp_day, adrg_standard_avg_fee,
            drg_wt, standard_avg_fee,
            drg_standard_inpf, AVG_AGE,
            refer_sco, adjm_cof,
            hosp_lv, ACTIVE_FLAG,
            uplmt_mag,lowlmt_mag
        ) VALUES
            (
                #{standardYear},
                #{drgCodg}, #{drgName},
                #{mdcStandardAveHospDay}, #{mdcStandardAvgFee},
                #{adrgStandardAveHospDay}, #{adrgStandardAvgFee},
                #{drgWt}, #{standardAvgFee},
                #{drgStandardInpf}, #{avgAge},
                #{refer_sco}, #{adjm_cof},
                #{hospLv}, #{activeFlag},
                #{uplmtMag},#{lowlmtMag}
            )
    </insert>
    <insert id="insertAreaBenchmarkInfoDipList">
        INSERT INTO som_dip_regn_standard(
            STANDARD_YEAR,
            dip_codg, DIP_NAME,
            is_used_asst_list, asst_list_age_grp,
            asst_list_dise_sev_deg, asst_list_tmor_sev_deg,
            dip_diag_codg, dip_diag_name,
            dip_oprt_codg, dip_oprt_name,
            dip_wt, standard_avg_fee,
            dip_standard_avg_fee_same_lv, last_year_avg_fee,
            refer_sco, adjm_cof,
            hosp_lv, ACTIVE_FLAG,
            uplmt_mag,lowlmt_mag,is_sd_dise
        )VALUES
        <foreach collection="sl" item="item" index="index" separator=","> (
            #{item.standardYear},
            #{item.dipCodg}, #{item.dipName},
            #{item.isUsedAsstList}, #{item.asstListAgeGrp},
            #{item.auxiliaryIlness}, #{item.asstListTmorSevDeg},
            #{item.dipDiagCodg}, #{item.dipDiagName},
            #{item.dipOprtCodg}, #{item.dipOprtName},
            #{item.dipWt}, #{item.standardAvgFee},
            #{item.dipStandardAvgFeeSameLv}, #{item.lastYearAvgFee},
            #{item.refer_sco}, #{item.adjm_cof},
            #{item.hospLv}, #{item.activeFlag},
            #{item.uplmtMag},#{item.lowlmtMag},#{item.stableDis}
        )
        </foreach>
    </insert>
    <insert id="insertAreaBenchmarkInfoDrgList">
        INSERT INTO som_drg_regn_standard(
            STANDARD_YEAR,
            drg_codg, DRG_NAME,
            mdc_standard_ave_hosp_day, mdc_standard_avg_fee,
            adrg_standard_ave_hosp_day, adrg_standard_avg_fee,
            drg_wt, standard_avg_fee,
            drg_standard_inpf, AVG_AGE,
            refer_sco, adjm_cof,
            hosp_lv, ACTIVE_FLAG,
            uplmt_mag,lowlmt_mag
        )VALUES
        <foreach collection="sl" item="item" index="index" separator=","> (
            #{item.standardYear},
            #{item.drgCodg}, #{item.drgName},
            #{item.mdcStandardAveHospDay}, #{item.mdcStandardAvgFee},
            #{item.adrgStandardAveHospDay}, #{item.adrgStandardAvgFee},
            #{item.drgWt}, #{item.standardAvgFee},
            #{item.drgStandardInpf}, #{item.avgAge},
            #{item.refer_sco}, #{item.adjm_cof},
            #{item.hospLv}, #{item.activeFlag},
            #{item.uplmtMag},#{item.lowlmtMag}
            )
        </foreach>
    </insert>
    <update id="updateAreaBenchmarkInfoDrg">
        update som_drg_regn_standard
        <set>
            <if test="adrgStandardAveHospDay != null and adrgStandardAveHospDay != ''">
                adrg_standard_ave_hosp_day=#{adrgStandardAveHospDay,jdbcType=VARCHAR},
            </if>
            <if test="adrgStandardAvgFee != null and adrgStandardAvgFee != ''">
                adrg_standard_avg_fee=#{adrgStandardAvgFee,jdbcType=VARCHAR},
            </if>
            <if test="mdcStandardAvgFee != null and mdcStandardAvgFee != ''">
                mdc_standard_avg_fee=#{mdcStandardAvgFee,jdbcType=VARCHAR},
            </if>
            <if test="mdcStandardAveHospDay != null and mdcStandardAveHospDay != ''">
                mdc_standard_ave_hosp_day=#{mdcStandardAveHospDay,jdbcType=VARCHAR},
            </if>
            <if test="drgWt != null and drgWt != ''">
                drg_wt=#{drgWt,jdbcType=VARCHAR},
            </if>
            <if test="standardAvgFee != null and standardAvgFee != ''">
                standard_avg_fee=#{standardAvgFee,jdbcType=VARCHAR},
            </if>
            <if test="drgStandardInpf != null and drgStandardInpf != ''">
                drg_standard_inpf=#{drgStandardInpf,jdbcType=VARCHAR},
            </if>
            <if test="refer_sco != null and refer_sco != ''">
                refer_sco=#{refer_sco,jdbcType=VARCHAR},
            </if>
            <if test="adjm_cof != null and adjm_cof != ''">
                adjm_cof=#{adjm_cof,jdbcType=VARCHAR},
            </if>
            <if test="hospLv != null and hospLv != ''">
                hosp_lv=#{hospLv,jdbcType=VARCHAR}
            </if>
        </set>
        where id = #{id}
    </update>
    <update id="updateAreaBenchmarkInfoDip">
        update som_dip_regn_standard
        <set>
            <if test="dipDiagCodg != null and dipDiagCodg != ''">
                dip_diag_codg=#{dipDiagCodg,jdbcType=VARCHAR},
            </if>
            <if test="dipDiagName != null and dipDiagName != ''">
                dip_diag_name=#{dipDiagName,jdbcType=VARCHAR},
            </if>
            <if test="dipOprtCodg != null and dipOprtCodg != ''">
                dip_oprt_codg=#{dipOprtCodg,jdbcType=VARCHAR},
            </if>
            <if test="dipOprtName != null and dipOprtName != ''">
                dip_oprt_name=#{dipOprtName,jdbcType=VARCHAR},
            </if>
            <if test="dipWt != null and dipWt != ''">
                dip_wt=#{dipWt,jdbcType=VARCHAR},
            </if>
            <if test="standardAvgFee != null and standardAvgFee != ''">
                standard_avg_fee=#{standardAvgFee,jdbcType=VARCHAR},
            </if>
            <if test="dipStandardAvgFeeSameLv != null and dipStandardAvgFeeSameLv != ''">
                dip_standard_avg_fee_same_lv=#{dipStandardAvgFeeSameLv,jdbcType=VARCHAR},
            </if>
            <if test="lastYearAvgFee != null and lastYearAvgFee != ''">
                last_year_avg_fee=#{lastYearAvgFee,jdbcType=VARCHAR},
            </if>
            <if test="refer_sco != null and refer_sco != ''">
                refer_sco=#{refer_sco,jdbcType=VARCHAR},
            </if>
            <if test="adjm_cof != null and adjm_cof != ''">
                adjm_cof=#{adjm_cof,jdbcType=VARCHAR},
            </if>
            <if test="hospLv != null and hospLv != ''">
                hosp_lv=#{hospLv,jdbcType=VARCHAR}
            </if>
        </set>
        where id = #{id}
    </update>
    <delete id="deleteAreaBenchmarkInfoDip">
        DELETE FROM som_dip_regn_standard WHERE id = #{id}
    </delete>
    <delete id="deleteAreaBenchmarkInfoDrg">
        DELETE FROM som_drg_regn_standard WHERE id = #{id}
    </delete>
    <delete id="deleteAllDip">
        DELETE FROM som_dip_regn_standard where STANDARD_YEAR = #{year}
    </delete>
    <delete id="deleteAllDrg">
        DELETE FROM som_drg_regn_standard where STANDARD_YEAR = #{year}
    </delete>

    <select id="queryAreaBenchmarkInfoDip" resultType="com.my.som.vo.dataConfig.AreaBenchmarkDipVo">
        SELECT
            id,
            STANDARD_YEAR AS standardYear,
            dip_codg AS dipCodg,
            DIP_NAME AS dipName,
            is_used_asst_list AS isUsedAsstList,
            asst_list_age_grp AS asstListAgeGrp,
            asst_list_dise_sev_deg AS auxiliaryIlness,
            asst_list_tmor_sev_deg AS asstListTmorSevDeg,
            dip_diag_codg AS dipDiagCodg,
            dip_diag_name AS dipDiagName,
            dip_oprt_codg AS dipOprtCodg,
            dip_oprt_name AS dipOprtName,
            dip_wt AS dipWt,
            standard_avg_fee AS standardAvgFee,
            dip_standard_avg_fee_same_lv AS dipStandardAvgFeeSameLv,
            last_year_avg_fee AS lastYearAvgFee,
            refer_sco AS refer_sco,
            adjm_cof AS AdjmCof,
            hosp_lv AS hospLv,
            ACTIVE_FLAG AS activeFlag,
            uplmt_mag AS uplmtMag,
            lowlmt_mag AS lowlmtMag
        FROM
            som_dip_regn_standard
        WHERE
            ACTIVE_FLAG = '1'
        <if test="dipCodg != null and dipCodg != ''">
            AND dip_codg = #{dipCodg}
        </if>
        <if test="hospLv != null and hospLv != ''">
            AND hosp_lv = #{hospLv}
        </if>
        <if test="year != null and year != ''">
            AND STANDARD_YEAR = #{year}
        </if>
    </select>
    <select id="queryAreaBenchmarkInfoDrg" resultType="com.my.som.vo.dataConfig.AreaBenchmarkDrgVo">
        SELECT
            id,
            STANDARD_YEAR AS standardYear,
            drg_codg AS drgCodg,
            DRG_NAME AS drgName,
            mdc_standard_ave_hosp_day AS mdcStandardAveHospDay,
            mdc_standard_avg_fee AS mdcStandardAvgFee,
            adrg_standard_ave_hosp_day AS adrgStandardAveHospDay,
            adrg_standard_avg_fee AS adrgStandardAvgFee,
            drg_wt AS drgWt,
            standard_avg_fee AS standardAvgFee,
            drg_standard_inpf AS drgStandardInpf,
            AVG_AGE AS avgAge,
            refer_sco AS refer_sco,
            adjm_cof AS adjm_cof,
            hosp_lv AS hospLv,
            ACTIVE_FLAG AS activeFlag,
            uplmt_mag AS uplmtMag,
            lowlmt_mag AS lowlmtMag
        FROM
            som_drg_regn_standard
        WHERE
            ACTIVE_FLAG = '1'
        <if test="drgCodg != null and drgCodg != ''">
            AND drg_codg = #{drgCodg}
        </if>
        <if test="hospLv != null and hospLv != ''">
            AND hosp_lv = #{hospLv}
        </if>
        <if test="year != null and year != ''">
            AND STANDARD_YEAR = #{year}
        </if>
    </select>
</mapper>
