<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.som.mapper.custom.CustomMapper">

    <!-- 查询DIP分组信息 -->
    <select id="queryCurHospitalInfo" resultType="com.my.som.vo.common.BenchmarkConfigVo">
        select a.dip_codg as dipCodg,
               a.DIP_NAME as dipName,
               a.is_used_asst_list AS usedAsstList,
               a.asst_list_age_grp AS asstListAgeGrp,
               a.asst_list_dise_sev_deg AS asstListDiseSevDeg,
               a.asst_list_tmor_sev_deg AS asstListTmorSevDeg,
               ROUND(AVG(ipt_sumfee),2) AS dipStandardCost,
               ROUND(AVG(act_ipt),2) AS dipStandardDaysLevel,
               ROUND(SUM(drugfee) / SUM(ipt_sumfee) * 100,2) AS dipDrugRate,
               ROUND(SUM(mcs_fee) / SUM(ipt_sumfee) * 100,2) AS dipComsumableRate

        from som_dip_grp_info a
        inner join som_dip_grp_rcd b
        ON a.SETTLE_LIST_ID = b.SETTLE_LIST_ID
        where substr(a.dscg_time,1,4) = #{standardYear,jdbcType=VARCHAR}
        and b.grp_stas = '1'
          <if test="inputData !='' and inputData !=null">
              AND (a.dip_codg like CONCAT("%",#{inputData},"%") or
                   a.DIP_NAME like concat('%',#{inputData},'%'))
          </if>
        <include refid="com.my.som.common.mapper.CommonMapper.fbAuth" />
<!--        <if test="dipCodes.size() > 0">-->
<!--            AND a.dip_codg in-->
<!--            (-->
<!--            <foreach collection="dipCodes" index="index" item="item" separator=",">-->
<!--               -->
<!--            </foreach>-->
<!--            )-->
<!--        </if>-->
        GROUP BY a.dip_codg,
                 a.DIP_NAME,
                 a.is_used_asst_list,
                 a.asst_list_age_grp,
                 a.asst_list_dise_sev_deg,
                 a.asst_list_tmor_sev_deg
        ORDER BY a.dip_codg
    </select>

    <!-- 获取对照信息 -->
    <select id="queryContrastHospitalInfo" resultType="com.my.som.vo.common.BenchmarkConfigVo">
        select a.dip_codg as dipCodg,
               a.DIP_NAME as dipName,
               a.is_used_asst_list AS usedAsstList,
               a.asst_list_age_grp AS asstListAgeGrp,
               a.asst_list_dise_sev_deg AS asstListDiseSevDeg,
               a.asst_list_tmor_sev_deg AS asstListTmorSevDeg,
               a.dip_standard_inpf AS dipStandardCost,
               a.dip_standard_ipt_days AS dipStandardDaysLevel,
               a.drug_ratio AS dipDrugRate,
               a.consum_ratio AS dipComsumableRate
        from som_regn_dip_dise_avg_fee a
        where a.STANDARD_YEAR = #{standardYear,jdbcType=VARCHAR}
        <if test="inputData !='' and inputData !=null">
            AND (a.dip_codg like CONCAT("%",#{inputData},"%") or
            a.DIP_NAME like concat('%',#{inputData},'%'))
        </if>
        ORDER BY a.dip_codg
    </select>

</mapper>
