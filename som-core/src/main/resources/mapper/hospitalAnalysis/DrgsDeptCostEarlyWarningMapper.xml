<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.my.som.mapper.hospitalAnalysis.DrgsDeptCostEarlyWarningMapper">

    <!-- 获取数据 -->
    <select id="getList" resultType="com.my.som.vo.hospitalAnalysis.DrgsDeptCostEarlyWarningVo">
        SELECT g.*
        FROM
             (
                 SELECT d.*,
                        e.name AS deptName,
                        f.medicalCount,
                        <![CDATA[
                            CASE WHEN balanceRate <= -10 THEN 1
                                 WHEN balanceRate > -10 AND balanceRate <= -1 THEN 2
                                 WHEN balanceRate > -1 AND balanceRate <= 1 THEN 3
                                 WHEN balanceRate > 1 THEN 4 ELSE 3 END AS rate
                        ]]> <!-- 盈亏比区间 默认3可能情况为 -0.0 -->
                 FROM
                      (
                          SELECT c.dscg_caty_codg_inhosp AS deptCode,
                                 c.HOSPITAL_ID,
                                 count(1) AS groupCount,
                                 SUM(pelCount) AS allCount,
                                 sum(c.upNum) AS upNum,
                                 sum(c.lowNum) AS lowNum,
                                 IFNULL(ROUND(SUM(forecast_fee) - SUM(sumfee),2),0) AS balanceCost,
                                 IFNULL(ROUND((SUM(forecast_fee) - SUM(sumfee)) / SUM(sumfee) * 100,2),0) AS balanceRate
                          FROM
                               (
                                   SELECT a.dscg_caty_codg_inhosp,
                                          a.HOSPITAL_ID,
                                          count(1) as pelCount,
                                          <choose>
                                            <when test="feeStas == 0">
                                                a.drg_codg,
                                                SUM(a.ipt_sumfee) AS sumfee,
<!--                                                SUM(CASE WHEN z.dise_type = 1 THEN IFNULL(z.totl_sco,0) * IFNULL(x.czPrice,0)-->
<!--                                                     WHEN z.dise_type = 2 THEN IFNULL(z.totl_sco,0) * IFNULL(x.cxPrice,0) ELSE IFNULL(z.totl_sco,0) * IFNULL(x.price,0) END) AS areaStandardCost,-->
                                             SUM(z.forecast_fee)  AS forecast_fee,
--                                                 SUM(convert(AES_DECRYPT(UNHEX(b.standard_avg_fee),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) using utf8)) AS areaStandardCost,
                                                COUNT(CASE WHEN z.dise_type = 1 THEN 1 ELSE null END) AS upNum,
                                                COUNT(CASE WHEN z.dise_type = 2 THEN 2 ELSE null END) AS lowNum
                                            </when>
                                            <when test="feeStas == 1">
                                                y.drg_codg,
                                                SUM(y.ipt_sumfee) AS sumfee,
                                                y.ycCost AS forecast_fee,
                                                COUNT(CASE WHEN y.dise_type = 1 THEN 1 ELSE NULL END) AS upNum,
                                                COUNT(CASE WHEN y.dise_type = 2 THEN 2 ELSE NULL END) AS lowNum
                                            </when>
                                          </choose>
                                   FROM som_drg_grp_info a
                                   <if test='enableSeAns != null and enableSeAns != "" and enableSeAns == "1"'>
                                       LEFT JOIN som_hi_invy_bas_info q
                                       ON q.ID = a.SETTLE_LIST_ID
                                       INNER JOIN som_setl_cas_crsp p
                                       ON q.k00 = p.k00
                                   </if>
                                    LEFT JOIN som_drg_sco z
                                    ON a.SETTLE_LIST_ID = z.SETTLE_LIST_ID
                                    LEFT JOIN som_drg_standard b
                                       ON a.drg_codg = b.drg_codg
                                    and z.insuplc_admdvs = b.insuplc_admdvs
                                    <if test="seStartTime != null and seStartTime != '' and seEndTime != null and seEndTime != ''">
                                        AND SUBSTR(a.setl_end_time,1,4) = b.STANDARD_YEAR
                                    </if>
                                    <if test="begnDate != null and begnDate != '' and expiDate != null and expiDate != ''">
                                        AND SUBSTR(a.dscg_time,1,4) = b.STANDARD_YEAR
                                    </if>

                                      AND a.HOSPITAL_ID = b.HOSPITAL_ID

<!--                                   CROSS JOIN (-->
<!--                                                SELECT-->
<!--                                                MAX( CASE WHEN `KEY` = 'CX_PRICE' THEN `VALUE` ELSE NULL END ) AS cxPrice,-->
<!--                                                MAX( CASE WHEN `KEY` = 'CZ_PRICE' THEN `VALUE` ELSE NULL END ) AS czPrice,-->
<!--                                                MAX( CASE WHEN `KEY` = 'PRICE' THEN `VALUE` ELSE NULL END ) AS price-->
<!--                                                FROM-->
<!--                                                som_drg_//gen_cfg-->
<!--                                                WHERE-->
<!--                                                TYPE = 'PREDICTED_PRICE'-->
<!--                                                GROUP BY-->
<!--                                                TYPE-->
<!--                                    ) x-->

                                   <if test="feeStas == 1">
                                       INNER JOIN
                                           (
                                               SELECT a.drg_codg,
                                                      a.DRG_NAME,
                                                      a.rid_idt_codg,
                                                      a.medcas_codg,
                                                      a.adm_time,
                                                      a.dscg_time,
                                                      a.setl_time,
                                                      a.is_in_group,
                                                      b.sumfee AS ipt_sumfee,
                                                      b.INSURED_TYPE,
                                                      b.MED_TYPE AS dise_type,
                                                      b.setl_pt_val,
                                                      b.dfr_fee AS ycCost,
                                                      b.MED_TYPE,
                                                      b.setl_sco
                                               FROM som_drg_grp_fbck a
                                               LEFT JOIN som_drg_pt_val_pay b
                                                   ON a.rid_idt_codg = b.rid_idt_codg
                                              <if test="begnDate != null and begnDate != '' and expiDate != null and expiDate != ''">
                                               <![CDATA[
                                                   WHERE
                                                   a.dscg_time >= #{begnDate}
                                                     AND a.dscg_time <= #{expiDate}
                                               ]]>
                                             </if>
                                               <if test="seStartTime != null and seStartTime != '' and seEndTime != null and seEndTime != '' ">
                                                   <![CDATA[
                                                   AND a.setl_end_time >= #{seStartTime,jdbcType=VARCHAR}
                                                   AND a.setl_end_time <= #{seEndTime,jdbcType=VARCHAR}
                                               ]]>
                                               </if>
                                           ) y
                                           ON a.PATIENT_ID = y.medcas_codg
                                          <if test="begnDate != null and begnDate != '' and expiDate != null and expiDate != '' ">
                                              AND SUBSTR(a.dscg_time,1,7) = SUBSTR(y.dscg_time,1,7)
                                          </if>
                                           <if test="seStartTime != null and seStartTime != '' and seEndTime != null and seEndTime != ''">
                                                AND SUBSTR(a.setl_end_time,1,10)= y.setl_time
                                           </if>
                                   </if>
                                   WHERE 1 = 1

                                       <if test="begnDate != null and begnDate != '' and expiDate != null and expiDate != ''">
                                           <![CDATA[
                                               AND SUBSTR(a.dscg_time,1,10) >= #{begnDate,jdbcType=VARCHAR}
                                               AND SUBSTR(a.dscg_time,1,10) <= #{expiDate,jdbcType=VARCHAR}
                                           ]]>
                                       </if>


                                        <if test="seStartTime != null and seStartTime != '' and seEndTime != null and seEndTime != ''">
                                            <![CDATA[
                                                AND SUBSTR( a.setl_end_time, 1, 10 ) >=#{seStartTime,jdbcType=VARCHAR}
                                                AND SUBSTR( a.setl_end_time, 1, 10 ) <= #{seEndTime,jdbcType=VARCHAR}
                                             ]]>
                                        </if>

                                   <include refid="com.my.som.mapper.newBusiness.common.NewBusinessCommonAnalysisMapper.queryBusCommon"></include>
                                   GROUP BY
                                   <choose>
                                       <when test="feeStas == 0">
                                           a.drg_codg,
                                       </when>
                                       <when test="feeStas == 1">
                                           y.drg_codg,
                                       </when>
                                   </choose>
                                       a.dscg_caty_codg_inhosp,
                                       a.HOSPITAL_ID
                               ) c
                          GROUP BY c.dscg_caty_codg_inhosp,
                                   c.HOSPITAL_ID
                      ) d
                 INNER JOIN som_dept e
                    ON d.deptCode = e.code
                   AND d.HOSPITAL_ID = e.HOSPITAL_ID
                 LEFT JOIN
                          (
                              SELECT B16C AS dept_code,
                                     count(distinct trim(B25C)) AS medicalCount
                              FROM som_hi_invy_bas_info
                              GROUP BY B16C
                          ) f
                    ON d.deptCode = f.dept_code
                 ORDER BY balanceRate
             ) g
             <where>
                <if test="rate != null and rate != '' and rate != 0">
                    g.rate = #{rate,jdbcType=VARCHAR}
                </if>
             </where>
        </select>
</mapper>
