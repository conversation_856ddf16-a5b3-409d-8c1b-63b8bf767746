<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.my.som.mapper.handleUpdateData.HandleUpdateDataMapper">
    <select id="selectDi04ById" resultType="java.lang.Integer">
        select count(1) from som_drord_info_intf where mdtrt_id = #{id}
    </select>

    <select id="selectDi11ById" resultType="java.lang.Integer">
        select count(1) from som_chrg_detl_intf where mdtrt_id = #{id}
    </select>

    <select id="selectDi05ById" resultType="java.lang.Integer">
        select count(1) from som_clnc_examrpt_main where mdtrt_id = #{id}
    </select>

    <select id="selectDi06ById" resultType="java.lang.Integer">
        select count(1) from som_clnc_test_rpot_main where mdtrt_id = #{id}
    </select>

    <select id="selectDi07ById" resultType="java.lang.Integer">
        select count(1) from som_elec_medrcd_info where mdtrt_id = #{id}
    </select>

    <select id="selectAllDi02Id" resultType="java.lang.String">
        select mdtrt_id from som_medcas_intf_bas_info
    </select>

</mapper>