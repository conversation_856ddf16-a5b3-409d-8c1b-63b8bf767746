<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.som.mapper.hcs.ExamCorrectionResultMapper">
    <insert id="insertSomVOutpatMedInfo">
        INSERT INTO som_v_outpat_med_info (
            fixmedins_code,
            fixmedins_name,
            hisid,
            p_level,
            bill_date,
            year,
            month,
            patient_id,
            social_id,
            benefit_type,
            benefit_group_id,
            mzh,
            admission_dept_id,
            admission_dept_name,
            doctor_id,
            doctor_name,
            id_card,
            patient_gender,
            patient_birthday,
            patient_age,
            claim_type,
            if_local_flag,
            admission_date,
            outpatient_disease_id,
            admission_disease_name,
            total_amount,
            bmi_pay_amount,
            cash,
            self_pay_amount,
            bmi_convered_amount,
            hospital_area,
            medical_insurance_flag,
            refund_flag_type,
            refund_hisid,
            insuplc
        ) VALUES (
                     #{fixmedinsCode},
                     #{fixmedinsName},
                     #{hisid},
                     #{pLevel},
                     #{billDate},
                     #{year},
                     #{month},
                     #{patientId},
                     #{socialId},
                     #{benefitType},
                     #{benefitGroupId},
                     #{mzh},
                     #{admissionDeptId},
                     #{admissionDeptName},
                     #{doctorId},
                     #{doctorName},
                     #{idCard},
                     #{patientGender},
                     #{patientBirthday},
                     #{patientAge},
                     #{claimType},
                     #{ifLocalFlag},
                     #{admissionDate},
                     #{outpatientDiseaseId},
                     #{admissionDiseaseName},
                     #{totalAmount},
                     #{bmiPayAmount},
                     #{cash},
                     #{selfPayAmount},
                     #{bmiConveredAmount},
                     #{hospitalArea},
                     #{medicalInsuranceFlag},
                     #{refundFlagType},
                     #{refundHisid},
                     #{insuplc}
                 )
    </insert>

    <!-- 查询规则数据 -->
    <select id="getResultListByPatient" resultType="com.my.som.vo.hcs.ExamCorrectionPatientVo">
        SELECT a.*,
        <choose>
            <when test="ruleScenType == '2'">
                b.mzh as medcasno,
                b.psn_name as name,
                b.patient_age as age,
                b.admission_date as b12,
                b.admission_date as b15,
                0 as iptDay,
                b.total_amount as sumfee
            </when>
            <when test="ruleScenType == '1'">
                b.zyh as medcasno,
                b.psn_name as name,
                b.patient_age as age,
                b.admission_date as b12,
                b.discharge_date as b15,
                CASE
                WHEN TIMESTAMPDIFF(MINUTE, b.admission_date, b.discharge_date) &lt; 1440 THEN 1
                ELSE DATEDIFF(b.discharge_date, b.admission_date)
                END AS iptDay,
                b.total_amount as sumfee,
                b.admission_dept_name as deptName
            </when>
            <otherwise>
                b.zyh as medcasno,
                b.psn_name as name,
                b.patient_age as age,
                b.admission_date as b12,
                b.discharge_date as b15,
                CASE
                WHEN TIMESTAMPDIFF(MINUTE, b.admission_date, b.discharge_date) &lt; 1440 THEN 1
                ELSE DATEDIFF(b.discharge_date, b.admission_date)
                END AS iptDay,
                b.total_amount as sumfee,
                b.discharge_dept_name as deptName
            </otherwise>
        </choose>
              , e.diagCodeAndName,
               d.oprnCodeAndName,
               b.doctor_name as doctorName
        FROM
        (SELECT unique_id                      as k00,
                rule_scen_type                 as ruleScenType,
                MAX(rule_detl_codg) AS ruleDetlCodg,
                COUNT(*)                       AS tupleNum,
                COUNT(DISTINCT rule_detl_codg) AS totalRecords,
                SUM(violation_amount)          AS totalAmount,
                MAX(error_desc) AS errorDesc
         FROM hcm_valid_result_inhosp
         <where>
             <if test="errorType != null and errorType != ''">
                 AND error_type = #{errorType}
             </if>
             <if test="ruleDetlCodg != null and ruleDetlCodg != ''">
                 AND rule_detl_codg = #{ruleDetlCodg}
             </if>
             <if test="ruleScenType != null and ruleScenType != ''">
                 AND rule_scen_type = #{ruleScenType}
             </if>
         </where>
         GROUP BY unique_id,
                  rule_scen_type) a
            inner JOIN
        <choose>
            <when test="ruleScenType == '2'">
                hcm_settle_mz_b b ON a.k00 = b.hisid

            </when>
            <when test="ruleScenType == '1'">
                hcm_settle_zy_b b ON a.k00 = b.hisid
            </when>
            <otherwise>
                hcm_settle_zy_b b ON a.k00 = b.hisid
            </otherwise>
        </choose>
        <if test="begnDate != null and begnDate != '' and
        expiDate != null and expiDate != ''">
            <choose>
                <when test="ruleScenType == '2'">
                    AND b.admission_date BETWEEN #{begnDate} and CONCAT(#{expiDate}, ' 23:59:59')
                </when>
                <when test="ruleScenType == '1'">
                    AND b.discharge_date BETWEEN #{begnDate} and CONCAT(#{expiDate}, ' 23:59:59')
                </when>
                <otherwise>
                    AND b.discharge_date BETWEEN #{begnDate} and CONCAT(#{expiDate}, ' 23:59:59')
                </otherwise>
            </choose>
        </if>
        <if test="medcasno != null and medcasno != ''">
            <choose>
                <when test="ruleScenType == '2'">
                    AND b.mzh LIKE CONCAT('%', #{medcasno}, '%')
                </when>
                <when test="ruleScenType == '1'">
                    AND b.zyh LIKE CONCAT('%', #{medcasno}, '%')
                </when>
                <otherwise>
                    AND b.zyh LIKE CONCAT('%', #{medcasno}, '%')
                </otherwise>
            </choose>
        </if>
        <!-- 患者姓名字段在hcm_settle_mz_b和hcm_settle_zy_b表中不存在，暂时移除此查询条件 -->

        <if test="patientName != null and patientName != ''">
            AND b.psn_name LIKE CONCAT('%', #{patientName}, '%')
        </if>

        LEFT JOIN som_hi_invy_bas_info bas ON bas.k00 = a.k00
        LEFT JOIN
        <if test="grperType == 'DIP'">
            som_dip_sco c ON c.settle_list_id = bas.id
        </if>
        <if test="grperType == 'DRG'">
            som_drg_sco c ON c.settle_list_id = bas.id
        </if>
        left join (
        SELECT
        hisid,
        GROUP_CONCAT(
        CONCAT(
        COALESCE(discharge_disease_id_main, ''),
        ':',
        COALESCE(discharge_disease_name_main, '')
        )
        SEPARATOR ', '
        ) AS diagCodeAndName
        FROM hcm_settle_diag_b
        GROUP BY hisid
        ) e   ON b.hisid = e.hisid
        left join (
        SELECT
        hisid,
        GROUP_CONCAT(
        CONCAT(
        COALESCE(icd9_code_main, ''),
        ':',
        COALESCE(icd9_name_main, '')
        )
        SEPARATOR ', '
        ) AS oprnCodeAndName
        FROM hcm_settle_oprn_b
        GROUP BY hisid
        ) d
        ON d.hisid = b.hisid
    </select>

    <!-- 查询违规汇总 -->
    <select id="getResultListByMouth" resultType="com.my.som.vo.hcs.ExamCorrectionMouthVo">
        SELECT a.mouth,
               a.deptName,
               a.deptCode,
               COUNT(DISTINCT a.doctorName)   AS doctorNum,
               COUNT(*)                       AS violationTupleNum,
               COUNT(DISTINCT rule_detl_codg) AS violationRuleNum,
               COUNT(DISTINCT a.UNIQUE_ID)    AS casesNum,
               SUM(a.violation_amount)        AS totalAmount
        FROM
        (select
        <choose>
            <when test="ruleScenType == '2'">
                IFNULL(LEFT(b.admission_date, 7), '') AS mouth,
                IFNULL(b.admission_dept_name, '未填写科室') AS deptName,
                IFNULL(b.admission_dept_id, '未填写科室') AS deptCode,
                IFNULL(b.doctor_name, '未填写医生') AS doctorName,
                b.doctor_id as doctorCode,
            </when>
            <when test="ruleScenType == '1'">
                IFNULL(LEFT(b.discharge_date, 7), '') AS mouth,
                IFNULL(b.discharge_dept_name, '未填写科室') AS deptName,
                IFNULL(b.discharge_dept_id, '未填写科室') AS deptCode,
                IFNULL(b.doctor_name, '未填写医生') AS doctorName,
                b.doctor_id as doctorCode,
            </when>
            <otherwise>
                IFNULL(LEFT(b.discharge_date, 7), '') AS mouth,
                IFNULL(b.discharge_dept_name, '未填写科室') AS deptName,
                IFNULL(b.discharge_dept_id, '未填写科室') AS deptCode,
                IFNULL(b.doctor_name, '未填写医生') AS doctorName,
                b.doctor_id as doctorCode,
            </otherwise>
        </choose>
                a.rule_detl_codg,
                a.UNIQUE_ID,
                a.violation_amount

         from hcm_valid_result_inhosp a
                  left join
        <choose>
            <when test="ruleScenType == '2'">
                hcm_settle_mz_b b on b.hisid = a.unique_id
            </when>
            <when test="ruleScenType == '1'">
                hcm_settle_zy_b b on b.hisid = a.unique_id
            </when>
            <otherwise>
                hcm_settle_zy_b b on b.hisid = a.unique_id
            </otherwise>
        </choose>
        where 1 = 1
        <if test="ruleScenType != null and ruleScenType != ''">
            AND a.rule_scen_type = #{ruleScenType}
        </if>
        <if test="begnDate != null and begnDate != '' and
        expiDate != null and expiDate != ''">
            <choose>
                <when test="ruleScenType == '2'">
                    AND b.admission_date BETWEEN #{begnDate} and CONCAT(#{expiDate}, ' 23:59:59')
                </when>
                <when test="ruleScenType == '1'">
                    AND b.discharge_date BETWEEN #{begnDate} and CONCAT(#{expiDate}, ' 23:59:59')
                </when>
                <otherwise>
                    AND b.discharge_date BETWEEN #{begnDate} and CONCAT(#{expiDate}, ' 23:59:59')
                </otherwise>
            </choose>
        </if>
        )a
        GROUP BY a.mouth, a.deptName, a.deptCode
    </select>

    <!-- 查询违规汇总 -->
    <select id="getViolationsSummary" resultType="com.my.som.vo.hcs.ViolationsSummaryVo">
        SELECT
        <if test="deptCode != null and deptCode != ''">
            #{deptCode} as deptCode,
        </if>
        <if test="doctorCode != null and doctorCode != ''">
            #{doctorCode} as doctorCode,
        </if>
        <if test="priOutHosDeptName != null and priOutHosDeptName != ''">
            #{priOutHosDeptName} as deptName,
        </if>
        a.*,
        b.rule_type_name AS ruleType,
        b.rule_grp_name  as ruleGrpName
        FROM
        (
        SELECT a.rule_detl_codg            AS ruleDetlCodg,
               a.mouth,
               COUNT(DISTINCT a.UNIQUE_ID) AS casesNum,
               COUNT(CASE WHEN a.UNIQUE_ID IS NOT NULL THEN 1 END) AS violationTupleNum,
               SUM(a.violation_amount)     AS totalAmount
        FROM
        (select
        <choose>
            <when test="ruleMouth != null and ruleMouth != ''">
                #{ruleMouth} AS mouth,
            </when>
            <otherwise>
                <choose>
                    <when test="ruleScenType == '2'">
                        IFNULL(LEFT(b.admission_date, 7), '') AS mouth,
                    </when>
                    <when test="ruleScenType == '1'">
                        IFNULL(LEFT(b.discharge_date, 7), '') AS mouth,
                    </when>
                    <otherwise>
                        IFNULL(LEFT(b.discharge_date, 7), '') AS mouth,
                    </otherwise>
                </choose>
            </otherwise>
        </choose>
        a.rule_detl_codg,
        a.UNIQUE_ID,
        a.violation_amount
        from hcm_valid_result_inhosp a
                 left join
        <choose>
            <when test="ruleScenType == '2'">
                hcm_settle_mz_b b on b.hisid = a.unique_id
                <if test="ruleMouth != null and ruleMouth != ''">
                    and b.admission_date like CONCAT(#{ruleMouth}, '%')
                </if>
            </when>
            <when test="ruleScenType == '1'">
                hcm_settle_zy_b b on b.hisid = a.unique_id
                <if test="ruleMouth != null and ruleMouth != ''">
                    and b.discharge_date like CONCAT(#{ruleMouth}, '%')
                </if>
            </when>
            <otherwise>
                hcm_settle_zy_b b on b.hisid = a.unique_id
                <if test="ruleMouth != null and ruleMouth != ''">
                    and b.discharge_date like CONCAT(#{ruleMouth}, '%')
                </if>
            </otherwise>
        </choose>
        where 1 = 1
        <if test="ruleScenType != null and ruleScenType != ''">
            AND a.rule_scen_type = #{ruleScenType}
        </if>
        <choose>
            <when test="ruleMouth != null and ruleMouth != ''">
                <if test="begnDate != null and begnDate != '' and expiDate != null and expiDate != ''">
                    <choose>
                        <when test="ruleScenType == '2'">
                            AND b.admission_date BETWEEN
                                GREATEST(#{begnDate}, CONCAT(#{ruleMouth}, '-01'))
                                AND
                                LEAST(CONCAT(#{expiDate}, ' 23:59:59'), CONCAT(LAST_DAY(CONCAT(#{ruleMouth}, '-01')), ' 23:59:59'))
                        </when>
                        <when test="ruleScenType == '1'">
                            AND b.discharge_date BETWEEN
                                GREATEST(#{begnDate}, CONCAT(#{ruleMouth}, '-01'))
                                AND
                                LEAST(CONCAT(#{expiDate}, ' 23:59:59'), CONCAT(LAST_DAY(CONCAT(#{ruleMouth}, '-01')), ' 23:59:59'))
                        </when>
                        <otherwise>
                            AND b.discharge_date BETWEEN
                                GREATEST(#{begnDate}, CONCAT(#{ruleMouth}, '-01'))
                                AND
                                LEAST(CONCAT(#{expiDate}, ' 23:59:59'), CONCAT(LAST_DAY(CONCAT(#{ruleMouth}, '-01')), ' 23:59:59'))
                        </otherwise>
                    </choose>
                </if>
            </when>
            <otherwise>
                <if test="begnDate != null and begnDate != '' and
                expiDate != null and expiDate != ''">
                    <choose>
                        <when test="ruleScenType == '2'">
                            AND b.admission_date BETWEEN #{begnDate} and CONCAT(#{expiDate}, ' 23:59:59')
                        </when>
                        <when test="ruleScenType == '1'">
                            AND b.discharge_date BETWEEN #{begnDate} and CONCAT(#{expiDate}, ' 23:59:59')
                        </when>
                        <otherwise>
                            AND b.discharge_date BETWEEN #{begnDate} and CONCAT(#{expiDate}, ' 23:59:59')
                        </otherwise>
                    </choose>
                </if>
            </otherwise>
        </choose>
        <if test="deptCode != null and deptCode != ''">
            <choose>
                <when test="ruleScenType == '2'">
                    AND IFNULL(b.admission_dept_id, '未填写科室') = #{deptCode}
                    <if test="priOutHosDeptName != '' and priOutHosDeptName != null">
                        AND b.discharge_dept_name = #{priOutHosDeptName}
                    </if>
                </when>
                <when test="ruleScenType == '1'">
                    AND IFNULL(b.discharge_dept_id, '未填写科室') = #{deptCode}
                    <if test="priOutHosDeptName != '' and priOutHosDeptName != null">
                        AND b.discharge_dept_name = #{priOutHosDeptName}
                    </if>
                </when>
                <otherwise>
                    AND IFNULL(b.discharge_dept_id, '未填写科室') = #{deptCode}
                    <if test="priOutHosDeptName != '' and priOutHosDeptName != null">
                        AND b.discharge_dept_name = #{priOutHosDeptName}
                    </if>
                </otherwise>
            </choose>
        </if>
        <if test="doctorCode != null and doctorCode != ''">
            AND b.doctor_id = #{doctorCode}
        </if>
        <if test="doctorName != null and doctorName != ''">
            <choose>
                <when test="doctorName == '未填写医生'">
                    AND (b.doctor_name = '未填写医生' OR b.doctor_name IS NULL OR b.doctor_name = '')
                </when>
                <otherwise>
                    AND b.doctor_name = #{doctorName}
                </otherwise>
            </choose>
        </if>
        )a
        GROUP BY a.rule_detl_codg, a.mouth
        ) a
            INNER JOIN hcm_rule_cfg b ON a.ruleDetlCodg = b.rule_detl_codg and LEFT(a.mouth, 4) = b.rule_year
            and b.vali_flag = 1
        <where>
            <if test="ruleDetlCodg != null and ruleDetlCodg != ''">
                and a.ruleDetlCodg LIKE CONCAT('%', #{ruleDetlCodg}, '%')
            </if>
            <if test="ruleType != null and ruleType != ''">
                and b.rule_type_name LIKE CONCAT('%', #{ruleType}, '%')
            </if>
        </where>
    </select>

    <!-- 查询违规科室汇总 -->
    <select id="getResultListByDept" resultType="com.my.som.vo.hcs.ExamCorrectionMouthVo">
        SELECT a.mouth,
               a.deptName,
               a.deptCode,
               COUNT(DISTINCT a.doctorName)                                            AS doctorNum,
               ifnull(GROUP_CONCAT(DISTINCT a.doctorName SEPARATOR ';'), '无违规医生') AS doctorNames,
               COUNT(DISTINCT a.rule_detl_codg)                                        AS violationRuleNum,
               COUNT(DISTINCT a.UNIQUE_ID)                                             AS casesNum,
               COUNT(CASE WHEN a.rule_detl_codg IS NOT NULL THEN 1 END)                AS violationTupleNum,
               SUM(a.violation_amount)                                                 AS totalAmount
        FROM
        (
        SELECT
        <choose>
            <when test="ruleScenType == '2'">
                IFNULL(LEFT(b.admission_date, 7), '') AS mouth,
                IFNULL(b.admission_dept_name, '未填写科室') AS deptName,
                IFNULL(b.admission_dept_id, '未填写科室') AS deptCode,
                IFNULL(b.doctor_name, '未填写医生') AS doctorName,
            </when>
            <when test="ruleScenType == '1'">
                IFNULL(LEFT(b.discharge_date, 7), '') AS mouth,
                IFNULL(b.discharge_dept_name, '未填写科室') AS deptName,
                IFNULL(b.discharge_dept_id, '未填写科室') AS deptCode,
                IFNULL(b.doctor_name, '未填写医生') AS doctorName,
            </when>
            <otherwise>
                IFNULL(LEFT(b.discharge_date, 7), '') AS mouth,
                IFNULL(b.discharge_dept_name, '未填写科室') AS deptName,
                IFNULL(b.discharge_dept_id, '未填写科室') AS deptCode,
                IFNULL(b.doctor_name, '未填写医生') AS doctorName,
            </otherwise>
        </choose>
               c.rule_detl_codg,
               c.UNIQUE_ID,
               c.violation_amount
        FROM
        <choose>
            <when test="ruleScenType == '2'">
                hcm_settle_mz_b b
            </when>
            <when test="ruleScenType == '1'">
                hcm_settle_zy_b b
            </when>
            <otherwise>
                hcm_settle_zy_b b
            </otherwise>
        </choose>
        left JOIN hcm_valid_result_inhosp c ON b.hisid = c.unique_id
        <where>
            <if test="ruleScenType != null and ruleScenType != ''">
                AND c.rule_scen_type = #{ruleScenType}
            </if>
            <if test="begnDate != null and begnDate != '' and
            expiDate != null and expiDate != ''">
                <choose>
                    <when test="ruleScenType == '2'">
                        AND b.admission_date BETWEEN #{begnDate} and CONCAT(#{expiDate}, ' 23:59:59')
                    </when>
                    <when test="ruleScenType == '1'">
                        AND b.discharge_date BETWEEN #{begnDate} and CONCAT(#{expiDate}, ' 23:59:59')
                    </when>
                    <otherwise>
                        AND b.discharge_date BETWEEN #{begnDate} and CONCAT(#{expiDate}, ' 23:59:59')
                    </otherwise>
                </choose>
            </if>
        </where>
        )a
        <where>
            <if test="deptCode != null and deptCode != ''">
               and a.deptCode = #{deptCode}
            </if>
            <if test="deptName != null and deptName != ''">
                a.deptName like CONCAT('%', #{deptName}, '%')
            </if>
            <if test="showVioal == 1">
                a.doctorName IS NOT NULL
            </if>
        </where>
        GROUP BY
            a.mouth, a.deptName, a.deptCode
        order by a.mouth ASC, totalAmount DESC
    </select>


    <!-- 查询违规医生汇总 -->
    <select id="getResultListByDoctor" resultType="com.my.som.vo.hcs.ExamCorrectionMouthVo">
        select a.* from (
        SELECT a.mouth,
               a.doctorName,
               a.doctorCode,
               COUNT(DISTINCT a.rule_detl_codg)                         AS violationRuleNum,
               COUNT(DISTINCT a.UNIQUE_ID)                              AS casesNum,
               COUNT(CASE WHEN a.rule_detl_codg IS NOT NULL THEN 1 END) AS violationTupleNum,
               SUM(a.violation_amount)                                  AS totalAmount
        FROM
        (
        SELECT
        <choose>
            <when test="ruleScenType == '2'">
                IFNULL(LEFT(b.admission_date, 7), '') AS mouth,
                IFNULL(b.doctor_name, '未填写医生') AS doctorName,
                IFNULL(b.doctor_id, '') AS doctorCode,
            </when>
            <when test="ruleScenType == '1'">
                IFNULL(LEFT(b.discharge_date, 7), '') AS mouth,
                IFNULL(b.doctor_name, '未填写医生') AS doctorName,
                IFNULL(b.doctor_id, '') AS doctorCode,
            </when>
            <otherwise>
                IFNULL(LEFT(b.discharge_date, 7), '') AS mouth,
                IFNULL(b.doctor_name, '未填写医生') AS doctorName,
                IFNULL(b.doctor_id, '') AS doctorCode,
            </otherwise>
        </choose>
               c.rule_detl_codg,
               c.UNIQUE_ID,
               c.violation_amount
        FROM
        <choose>
            <when test="ruleScenType == '2'">
                hcm_settle_mz_b b
            </when>
            <when test="ruleScenType == '1'">
                hcm_settle_zy_b b
            </when>
            <otherwise>
                hcm_settle_zy_b b
            </otherwise>
        </choose>
                 LEFT JOIN hcm_valid_result_inhosp c ON b.hisid = c.unique_id
        <where>
            <if test="ruleScenType != null and ruleScenType != ''">
                AND c.rule_scen_type = #{ruleScenType}
            </if>
            <if test="begnDate != null and begnDate != '' and
            expiDate != null and expiDate != ''">
                <choose>
                    <when test="ruleScenType == '2'">
                        AND b.admission_date BETWEEN #{begnDate} and CONCAT(#{expiDate}, ' 23:59:59')
                    </when>
                    <when test="ruleScenType == '1'">
                        AND b.discharge_date BETWEEN #{begnDate} and CONCAT(#{expiDate}, ' 23:59:59')
                    </when>
                    <otherwise>
                        AND b.discharge_date BETWEEN #{begnDate} and CONCAT(#{expiDate}, ' 23:59:59')
                    </otherwise>
                </choose>
            </if>
            <if test="doctorName != null and doctorName != ''">
                <choose>
                    <when test="doctorName == '未填写医生'">
                        AND (b.doctor_name = '未填写医生' OR b.doctor_name IS NULL OR b.doctor_name = '')
                    </when>
                    <otherwise>
                        and b.doctor_name LIKE CONCAT('%', #{doctorName}, '%')
                    </otherwise>
                </choose>
            </if>
        </where>
        ) a
        GROUP BY a.mouth,
                 a.doctorName,
                 a.doctorCode
        order by a.mouth ASC, totalAmount DESC )a
        <where>
            <if test="showVioal == 1">
                and (a.totalAmount IS NOT NULL or a.casesNum != 0)
            </if>
        </where>
    </select>

    <!-- 查询违规详情通过规则名称和月份 -->
    <select id="getTupleDetailByRuleCode" resultType="com.my.som.vo.hcs.ViolationsSummaryVo">
        select
        <choose>
            <when test="ruleScenType == '2'">
                b.mzh as medcasno,
            </when>
            <when test="ruleScenType == '1'">
                b.zyh as medcasno,
            </when>
            <otherwise>
                b.zyh as medcasno,
            </otherwise>
        </choose>
               a.rule_detl_codg   as ruleDetlCodg,
               a.error_desc       as ruleType,
               c.data_code        as dataCode,
               c.data_name        as dataName,
               a.violation_amount as totalAmount
        from hcm_valid_result_inhosp a
                 LEFT JOIN
        <choose>
            <when test="ruleScenType == '2'">
                hcm_settle_mz_b b ON b.hisid = a.unique_id
            </when>
            <when test="ruleScenType == '1'">
                hcm_settle_zy_b b ON b.hisid = a.unique_id
            </when>
            <otherwise>
                hcm_settle_zy_b b ON b.hisid = a.unique_id
            </otherwise>
        </choose>
                 LEFT JOIN hcm_data_grp_cfg c ON a.med_list_codg = c.data_code
                    and a.rule_data_meta = data_grp_code
                    and c.rule_year = LEFT(#{ruleMouth}, 4)
                 INNER JOIN hcm_rule_cfg d ON a.rule_detl_codg = d.rule_detl_codg
                    and d.rule_year = LEFT(#{ruleMouth}, 4)
                    and d.vali_flag = 1
        <where>
            <!-- 过滤掉病案号为空的记录 -->
            <choose>
                <when test="ruleScenType == '2'">
                    AND b.mzh IS NOT NULL AND b.mzh != ''
                </when>
                <when test="ruleScenType == '1'">
                    AND b.zyh IS NOT NULL AND b.zyh != ''
                </when>
                <otherwise>
                    AND b.zyh IS NOT NULL AND b.zyh != ''
                </otherwise>
            </choose>
            <if test="ruleScenType != null and ruleScenType != ''">
                AND a.rule_scen_type = #{ruleScenType}
            </if>
            <if test="deptCode != null and deptCode != ''">
                <choose>
                    <when test="ruleScenType == '2'">
                        AND IFNULL(b.admission_dept_id, '未填写科室') = #{deptCode}
                        <if test="priOutHosDeptName != '' and priOutHosDeptName != null">
                            AND b.discharge_dept_name = #{priOutHosDeptName}
                        </if>
                    </when>
                    <when test="ruleScenType == '1'">
                        AND IFNULL(b.discharge_dept_id, '未填写科室') = #{deptCode}
                        <if test="priOutHosDeptName != '' and priOutHosDeptName != null">
                            AND b.discharge_dept_name = #{priOutHosDeptName}
                        </if>
                    </when>
                    <otherwise>
                        AND IFNULL(b.discharge_dept_id, '未填写科室') = #{deptCode}
                        <if test="priOutHosDeptName != '' and priOutHosDeptName != null">
                            AND b.discharge_dept_name = #{priOutHosDeptName}
                        </if>
                    </otherwise>
                </choose>
            </if>
            <if test="doctorCode != null and doctorCode != ''">
                AND b.doctor_id = #{doctorCode}
            </if>
            <if test="ruleDetlCodg != null and ruleDetlCodg != ''">
                AND a.rule_detl_codg = #{ruleDetlCodg}
            </if>
            <if test="begnDate != null and begnDate != '' and
            expiDate != null and expiDate != ''">
                <choose>
                    <when test="ruleScenType == '2'">
                        AND b.admission_date BETWEEN #{begnDate} and CONCAT(#{expiDate}, ' 23:59:59')
                    </when>
                    <when test="ruleScenType == '1'">
                        AND b.discharge_date BETWEEN #{begnDate} and CONCAT(#{expiDate}, ' 23:59:59')
                    </when>
                    <otherwise>
                        AND b.discharge_date BETWEEN #{begnDate} and CONCAT(#{expiDate}, ' 23:59:59')
                    </otherwise>
                </choose>
            </if>
            <if test="ruleMouth != null and ruleMouth != ''">
                AND b.discharge_date LIKE CONCAT(#{ruleMouth}, '%')
            </if>
        </where>
    </select>

    <!-- 查询违规详情通过规则名称和月份 -->
    <select id="queryVioalDetailList" resultType="com.my.som.vo.hcs.ExamCorrectionPatientVo">
        SELECT a.*,
        <choose>
            <when test="ruleScenType == '2'">
                b.mzh as medcasno,
                b.psn_name as name,
                b.patient_age as age,
                b.admission_date as b12,
                b.admission_date as b15,
                0 as iptDay,
                b.admission_dept_name AS deptName,
                b.admission_dept_id as deptCode,
                b.doctor_name AS doctorName,
                b.doctor_id as doctorCode
            </when>
            <when test="ruleScenType == '1'">
                b.zyh as medcasno,
                b.psn_name as name,
                b.patient_age as age,
                b.admission_date as b12,
                b.discharge_date as b15,
                DATEDIFF(b.discharge_date, b.admission_date) as iptDay,
                b.discharge_dept_name AS deptName,
                b.discharge_dept_id as deptCode,
                b.doctor_name AS doctorName,
                b.doctor_id as doctorCode
            </when>
            <otherwise>
                b.zyh as medcasno,
                b.psn_name as name,
                b.patient_age as age,
                b.admission_date as b12,
                b.discharge_date as b15,
                DATEDIFF(b.discharge_date, b.admission_date) as iptDay,
                b.discharge_dept_name AS deptName,
                b.discharge_dept_id as deptCode,
                b.doctor_name AS doctorName,
                b.doctor_id as doctorCode
            </otherwise>
        </choose>
        ,c.combined_diagnoses AS diagCodeAndName,
        d.combined_oprn as oprnCodeAndName
        FROM
        (
        SELECT unique_id,
               rule_scen_type                 as ruleScenType,
               COUNT(*)                       AS tupleNum,
               COUNT(DISTINCT rule_detl_codg) AS totalRecords,
               SUM(violation_amount)          AS totalAmount
        FROM hcm_valid_result_inhosp e
        where 1 = 1
        <if test="ruleScenType != null and ruleScenType != ''">
            AND e.rule_scen_type = #{ruleScenType}
        </if>
        <if test="errorType != null and errorType != ''">
            AND e.error_type = #{errorType}
        </if>
        <if test="ruleDetlCodg != null and ruleDetlCodg != ''">
            AND e.rule_detl_codg = #{ruleDetlCodg}
        </if>
        GROUP BY unique_id,
                 rule_scen_type
        ) a
            inner JOIN
        <choose>
            <when test="ruleScenType == '2'">
                hcm_settle_mz_b b ON a.unique_id = b.hisid
            </when>
            <when test="ruleScenType == '1'">
                hcm_settle_zy_b b ON a.unique_id = b.hisid
            </when>
            <otherwise>
                hcm_settle_zy_b b ON a.unique_id = b.hisid
            </otherwise>
        </choose>
        <if test="priOutHosDeptName != null and priOutHosDeptName != '' ">
                AND b.discharge_dept_name = #{priOutHosDeptName}
        </if>
        <if test="begnDate != null and begnDate != '' and
        expiDate != null and expiDate != ''">
            <choose>
                <when test="ruleScenType == '2'">
                    AND b.admission_date BETWEEN #{begnDate} and CONCAT(#{expiDate}, ' 23:59:59')
                </when>
                <when test="ruleScenType == '1'">
                    AND b.discharge_date BETWEEN #{begnDate} and CONCAT(#{expiDate}, ' 23:59:59')
                </when>
                <otherwise>
                    AND b.discharge_date BETWEEN #{begnDate} and CONCAT(#{expiDate}, ' 23:59:59')
                </otherwise>
            </choose>
        </if>
        <if test="doctorCode != null and doctorCode != ''">
            AND b.doctor_id = #{doctorCode}
        </if>
        <if test="deptCode != null and deptCode != ''">
            <choose>
                <when test="ruleScenType == '2'">
                    AND IFNULL(b.admission_dept_id, '未填写科室') = #{deptCode}
                </when>
                <when test="ruleScenType == '1'">
                    AND IFNULL(b.discharge_dept_id, '未填写科室') = #{deptCode}
                </when>
                <otherwise>
                    AND IFNULL(b.discharge_dept_id, '未填写科室') = #{deptCode}
                </otherwise>
            </choose>
        </if>
            left join (
                SELECT
                hisid,
                GROUP_CONCAT(
                CONCAT(
                COALESCE(discharge_disease_id_main, ''),
                ':',
                COALESCE(discharge_disease_name_main, '')
                )
                SEPARATOR ', '
                ) AS combined_diagnoses
                FROM hcm_settle_diag_b
                GROUP BY hisid
            ) c   ON b.hisid = c.hisid
                left join (
                SELECT
                hisid,
                GROUP_CONCAT(
                CONCAT(
                COALESCE(icd9_code_main, ''),
                ':',
                COALESCE(icd9_name_main, '')
                )
                SEPARATOR ', '
                ) AS combined_oprn
                FROM hcm_settle_oprn_b
                GROUP BY hisid
                ) d
                ON d.hisid = c.hisid
    </select>

    <!-- 查询违规明细详情 -->
    <select id="fetchViolationRulesByUniqueId" resultType="com.my.som.vo.hcs.ExamCorrectionResutlVo">
        select a.rule_valid_result_id as id,
               a.unique_id            as uniqueId,
               a.rule_scen_type       as ruleScenType,
               a.rule_detl_codg       AS ruleDetlCodg,
               a.rule_data_meta       AS ruleDataMeta,
               a.error_desc           AS errorDesc,
               a.error_detail_codg    AS errorDetailCodg,
               a.violation_amount     AS violationAmount,
               a.med_list_codg        as medListCodg,
               b.data_name            AS dataName,
               d.rule_grp_name        as ruleGrpName,
               a.cnt,
               a.pric,
               a.vola_deg             as volaDeg
        FROM hcm_valid_result_inhosp a
                 inner join hcm_data_grp_cfg b on
                    a.med_list_codg = b.data_code
                and LEFT(a.oprn_date, 4) = b.rule_year
                and b.data_grp_code = a.rule_data_meta
                 inner join hcm_rule_cfg d
                            on a.rule_detl_codg = d.rule_detl_codg
                                AND d.rule_year = LEFT(a.oprn_date, 4)
        where unique_id = #{uniqueId}
    </select>

    <!-- 查询违规明细详情 all -->
    <select id="fetchViolationRulesByParam" resultType="com.my.som.vo.hcs.ExamCorrectionResutlVo">
        select a.rule_valid_result_id as id,
               a.unique_id            as uniqueId,
               a.rule_scen_type       as ruleScenType,
               a.rule_detl_codg       AS ruleDetlCodg,
               a.rule_data_meta       AS ruleDataMeta,
               a.error_desc           AS errorDesc,
               a.error_detail_codg    AS errorDetailCodg,
               a.violation_amount     AS violationAmount,
               a.med_list_codg        as medListCodg,
        cfg.itemname as medListName,
               b.data_name            AS dataName,
               d.rule_grp_name        as ruleGrpName,
               a.cnt,
               a.pric,
               a.vola_deg             as volaDeg
        FROM hcm_valid_result_inhosp a
        left join som_med_serv_cfg cfg on  cfg.item_codg = a.med_list_codg
                 inner join hcm_data_grp_cfg b on
                    a.med_list_codg = b.data_code
                and LEFT(a.oprn_date, 4) = b.rule_year
                and b.data_grp_code = a.rule_data_meta
                 inner join hcm_rule_cfg d
                            on a.rule_detl_codg = d.rule_detl_codg
                                AND d.rule_year = LEFT(a.oprn_date, 4)
                 inner join
        <choose>
            <when test="ruleScenType == '2'">
                hcm_settle_mz_b e on a.unique_id = e.hisid
            </when>
            <when test="ruleScenType == '1'">
                hcm_settle_zy_b e on a.unique_id = e.hisid
            </when>
            <otherwise>
                hcm_settle_zy_b e on a.unique_id = e.hisid
            </otherwise>
        </choose>
        where 1 = 1
        <if test="ruleScenType != null and ruleScenType != ''">
            AND a.rule_scen_type = #{ruleScenType}
        </if>
        <if test="uniqueId != null and uniqueId != ''">
            AND a.unique_id = #{uniqueId}
        </if>
        <if test="begnDate != null and begnDate != '' and
        expiDate != null and expiDate != ''">
            <choose>
                <when test="ruleScenType == '2'">
                    AND e.admission_date BETWEEN #{begnDate} and CONCAT(#{expiDate}, ' 23:59:59')
                </when>
                <when test="ruleScenType == '1'">
                    AND e.discharge_date BETWEEN #{begnDate} and CONCAT(#{expiDate}, ' 23:59:59')
                </when>
                <otherwise>
                    AND e.discharge_date BETWEEN #{begnDate} and CONCAT(#{expiDate}, ' 23:59:59')
                </otherwise>
            </choose>
        </if>
    </select>

    <!-- 查询收费明细-->
    <select id="filteredChargeItemsByUniqueId" resultType="com.my.som.vo.hcs.ChargeItemsVo">
        SELECT med_list_codg       as code,
               pric                as price,
               cnt                 as count,
               det_item_fee_sumamt AS detitemFeeSumamt,
               fee_ocur_time       as feeOcurTime,
               bilg_dept_codg      as bilgDeptCodg,
               bilg_dept_name      as department,
               bilg_dr_codg        as bilgDrCodg,
               bilg_dr_name        as bilgDrName
        FROM `som_chrg_detl_intf`
        where unique_id = #{uniqueId}
        <if test="chrgCode != null and chrgCode != ''">
            AND med_list_codg LIKE CONCAT('%', #{chrgCode}, '%')
        </if>
    </select>

    <!-- 查询规则数据 -->
    <select id="fetchPatientInfoByUniqueId" resultType="com.my.som.vo.hcs.ExamCorrectionPatientVo">
        SELECT a.*,
        <choose>
            <when test="ruleScenType == '2'">
                b.mzh as medcasno,
                b.psn_name as name,
                b.patient_age as age,
                b.admission_date as b12,
                b.admission_date as b15,
                0 as iptDay,
                b.total_amount as sumfee
            </when>
            <when test="ruleScenType == '1'">
                b.zyh as medcasno,
                b.psn_name as name,
                b.patient_age as age,
                b.admission_date as b12,
                b.discharge_date as b15,
                CASE
                    WHEN TIMESTAMPDIFF(MINUTE, b.admission_date, b.discharge_date) &lt; 1440 THEN 1
                    ELSE DATEDIFF(b.discharge_date, b.admission_date)
                END AS iptDay,
                b.total_amount as sumfee
            </when>
            <otherwise>
                b.zyh as medcasno,
                b.psn_name as name,
                b.patient_age as age,
                b.admission_date as b12,
                b.discharge_date as b15,
                CASE
                    WHEN TIMESTAMPDIFF(MINUTE, b.admission_date, b.discharge_date) &lt; 1440 THEN 1
                    ELSE DATEDIFF(b.discharge_date, b.admission_date)
                END AS iptDay,
                b.total_amount as sumfee
            </otherwise>
        </choose>
        , c.combined_diagnoses as diagCodeAndName,
          e.combined_oprn as oprnCodeAndName
        FROM
        (SELECT unique_id                      as k00,
                rule_scen_type                 as ruleScenType,
                COUNT(*)                       AS tupleNum,
                COUNT(DISTINCT rule_detl_codg) AS totalRecords,
                SUM(violation_amount)          AS totalAmount
         FROM hcm_valid_result_inhosp
         <where>
             <if test="ruleScenType != null and ruleScenType != ''">
                 AND rule_scen_type = #{ruleScenType}
             </if>
         </where>
         GROUP BY unique_id,
                  rule_scen_type) a
            inner JOIN
        <choose>
            <when test="ruleScenType == '2'">
                hcm_settle_mz_b b ON a.k00 = b.hisid
            </when>
            <when test="ruleScenType == '1'">
                hcm_settle_zy_b b ON a.k00 = b.hisid
            </when>
            <otherwise>
                hcm_settle_zy_b b ON a.k00 = b.hisid
            </otherwise>
        </choose>
        <if test="uniqueId != null and uniqueId != ''">
            AND b.hisid = #{uniqueId}
        </if>
        <if test="begnDate != null and begnDate != '' and
        expiDate != null and expiDate != ''">
            <choose>
                <when test="ruleScenType == '2'">
                    AND b.admission_date BETWEEN #{begnDate} and CONCAT(#{expiDate}, ' 23:59:59')
                </when>
                <when test="ruleScenType == '1'">
                    AND b.discharge_date BETWEEN #{begnDate} and CONCAT(#{expiDate}, ' 23:59:59')
                </when>
                <otherwise>
                    AND b.discharge_date BETWEEN #{begnDate} and CONCAT(#{expiDate}, ' 23:59:59')
                </otherwise>
            </choose>
        </if>
        <if test="medcasno != null and medcasno != ''">
            <choose>
                <when test="ruleScenType == '2'">
                    AND b.mzh LIKE CONCAT('%', #{medcasno}, '%')
                </when>
                <when test="ruleScenType == '1'">
                    AND b.zyh LIKE CONCAT('%', #{medcasno}, '%')
                </when>
                <otherwise>
                    AND b.zyh LIKE CONCAT('%', #{medcasno}, '%')
                </otherwise>
            </choose>
        </if>
        <!-- 患者姓名字段在hcm_settle_mz_b和hcm_settle_zy_b表中不存在，暂时移除此查询条件 -->
        <!--
        <if test="patientName != null and patientName != ''">
            AND b.patient_name LIKE CONCAT('%', #{patientName}, '%')
        </if>
        -->
        LEFT JOIN som_hi_invy_bas_info bas ON bas.k00 = a.k00
        LEFT JOIN
        <if test="grperType == 'DIP'">
            som_dip_sco c ON c.settle_list_id = bas.id
        </if>
        <if test="grperType == 'DRG'">
            som_drg_sco c ON c.settle_list_id = bas.id
        </if>
        left join (
        SELECT
        hisid,
        GROUP_CONCAT(
        CONCAT(
        COALESCE(discharge_disease_id_main, ''),
        ':',
        COALESCE(discharge_disease_name_main, '')
        )
        SEPARATOR ', '
        ) AS combined_diagnoses
        FROM hcm_settle_diag_b
        where hisid = #{uniqueId}
        GROUP BY hisid
        ) c   ON b.hisid = c.hisid
        left join (
        SELECT
        hisid,
        GROUP_CONCAT(
        CONCAT(
        COALESCE(icd9_code_main, ''),
        ':',
        COALESCE(icd9_name_main, '')
        )
        SEPARATOR ', '
        ) AS combined_oprn
        FROM hcm_settle_oprn_b
        where hisid = #{uniqueId}
        GROUP BY hisid
        ) e
        ON e.hisid = e.hisid
            LEFT JOIN (
            SELECT d.settle_list_id, GROUP_CONCAT( d.codeAndName ORDER BY d.seq SEPARATOR ';' ) AS oprnCodeAndName
            FROM
            (SELECT d.settle_list_id, CONCAT( d.c35c, '【', d.c36n, '】' ) AS codeAndName, d.seq FROM som_oprn_oprt_info d ) d
            GROUP BY d.settle_list_id
            ) d ON d.settle_list_id = bas.id
    </select>

    <!--查询违规项目信息-->
    <select id="getViolationItemSummary" resultType="com.my.som.vo.hcs.ViolationsItemSummaryVo">
        select violation_code as violationCode,<!--违规项目编码对-->
        violation_name as violationName,<!--违规项目名称对-->
        rule_grp_name AS ruleGrpName,<!--违规内容-->
        count(DISTINCT unique_id) as medSize,<!--总人次-->
        sum(violationSize) as allViolationSize,<!--违规次数-->
        sum(sumfee) as allSumfee,<!--项目总费用-->
        sum(case when vola_deg = '1' then sumfee else 0 end) as clearlySumfee,<!--明确违规费用-->
        sum(case when vola_deg = '2' then sumfee else 0 end) as suspectSumfee<!--疑似费用-->
        from (SELECT c.rule_grp_name,
                     c.vola_deg,
                     unique_id,
                     error_detail_codg,
                     count(1)                                                           as violationSize,
                     GROUP_CONCAT(a.med_list_codg ORDER BY med_list_codg SEPARATOR '/') as violation_code,
                     GROUP_CONCAT(b.itemname ORDER BY b.itemname SEPARATOR '/')         as violation_name,
                     sum(violation_amount)                                              as sumfee
              FROM hcm_valid_result_inhosp a
                       left join (select item_codg, itemname from som_med_serv_cfg group by item_codg, itemname) b
                                 on a.med_list_codg = b.item_codg
                       inner join hcm_rule_cfg c on a.rule_detl_codg = c.rule_detl_codg
                       inner join
        <choose>
            <when test='ruleScenType == "2"'>
                hcm_settle_mz_b e
            </when>
            <when test='ruleScenType == "1"'>
                hcm_settle_zy_b e
            </when>
        </choose> on a.unique_id = e.hisid
        where 1 = 1
        <if test="uniqueId != null and uniqueId != ''">
            AND a.unique_id = #{uniqueId}
        </if>
        <if test="begnDate != null and begnDate != '' and
        expiDate != null and expiDate != ''">
            <choose>
                <when test='ruleScenType == "2"'>
                    AND e.admission_date BETWEEN #{begnDate}
                    and CONCAT(#{expiDate}
                    , ' 23:59:59')
                </when>
                <when test='ruleScenType == "1"'>
                    AND e.discharge_date BETWEEN #{begnDate}
                    and CONCAT(#{expiDate}
                    , ' 23:59:59')
                </when>
            </choose>
        </if>
        GROUP BY c.rule_grp_name, c.vola_deg, unique_id, error_detail_codg) a
        group by rule_grp_name, violation_code, violation_name
    </select>

    <!--查询违规明细统计-->
    <select id="getViolationDetailSummary" resultType="com.my.som.vo.hcs.ViolationsDetailSummaryVo">
        SELECT unique_id                                                          as uniqueId,
        <choose>
            <when test='ruleScenType == "2"'>
                d.mzh as medcasno,
                d.admission_dept_name as deptName,
                DATE_FORMAT(d.admission_date, '%Y-%m-%d')                                     AS dscgDate,
            </when>
            <when test='ruleScenType == "1"'>
                d.zyh as medcasno,
                d.discharge_dept_name as deptName,
                DATE_FORMAT(d.discharge_date, '%Y-%m-%d')                                     AS dscgDate,
            </when>
        </choose>
               error_detail_codg                                                  as errorDetailCodg,
               count(1)                                                           AS violationSize,
               GROUP_CONCAT(a.med_list_codg ORDER BY med_list_codg SEPARATOR '/') AS violationCode,
               GROUP_CONCAT(b.itemname ORDER BY b.itemname SEPARATOR '/')         AS violationName,
               c.rule_grp_name                                                    AS ruleGrpName,
               c.vola_deg                                                         AS volaDeg,
               sum(violation_amount)                                              AS allSumfee,
                e.diagCodes,
                e.diagNames,
                f.oprnOprtCodes,
                f.oprnOprtNames,
                d.benefit_type as benefitType,
                d.psn_name as psnName
        FROM hcm_valid_result_inhosp a
                 LEFT JOIN (SELECT item_codg, itemname FROM som_med_serv_cfg GROUP BY item_codg, itemname) b
                           ON a.med_list_codg = b.item_codg
                 INNER JOIN hcm_rule_cfg c ON a.rule_detl_codg = c.rule_detl_codg
                 INNER JOIN <choose>
        <when test='ruleScenType == "2"'>
            hcm_settle_mz_b d
        </when>
        <when test='ruleScenType == "1"'>
            hcm_settle_zy_b d
        </when>
    </choose> ON a.unique_id = d.hisid
        LEFT JOIN (SELECT hisid,
        CONCAT_WS('@', discharge_disease_id_main,
        discharge_disease_id_other1,
        discharge_disease_id_other2,
        discharge_disease_id_other3,
        discharge_disease_id_other4,
        discharge_disease_id_other5,
        discharge_disease_id_other6,
        discharge_disease_id_other7,
        discharge_disease_id_other8,
        discharge_disease_id_other9,
        discharge_disease_id_other10,
        discharge_disease_id_other11,
        discharge_disease_id_other12,
        discharge_disease_id_other13,
        discharge_disease_id_other14,
        discharge_disease_id_other15) as diagCodes,
        CONCAT_WS('@', discharge_disease_name_main,
        discharge_disease_name_other1,
        discharge_disease_name_other2,
        discharge_disease_name_other3,
        discharge_disease_name_other4,
        discharge_disease_name_other5,
        discharge_disease_name_other6,
        discharge_disease_name_other7,
        discharge_disease_name_other8,
        discharge_disease_name_other9,
        discharge_disease_name_other10,
        discharge_disease_name_other11,
        discharge_disease_name_other12,
        discharge_disease_name_other13,
        discharge_disease_name_other14,
        discharge_disease_name_other15 ) as diagNames
        FROM hcm_settle_diag_b) e
        ON a.unique_id = e.hisid
        LEFT JOIN (SELECT hisid,
        GROUP_CONCAT(icd9_code_main ORDER BY tid ASC SEPARATOR '@') oprnOprtCodes,
        GROUP_CONCAT(icd9_name_main ORDER BY tid ASC SEPARATOR '@') oprnOprtNames
        FROM hcm_settle_oprn_b
        GROUP BY hisid) f ON a.unique_id = f.hisid
        where 1=1

        <if test="uniqueId != null and uniqueId != ''">
            AND a.unique_id = #{uniqueId}
        </if>
        <if test="a48 != null and a48 != ''">
            AND a.a48 LIKE CONCAT('%',#{a48,jdbcType=VARCHAR},'%')
        </if>
        <if test="a11 != null and a11 != ''">
            AND a.a11 LIKE CONCAT('%',#{a11,jdbcType=VARCHAR},'%')
        </if>
        <if test="begnDate != null and begnDate != '' and
        expiDate != null and expiDate != ''">
            <choose>
                <when test='ruleScenType == "2"'>
                    AND d.admission_date BETWEEN #{begnDate}
                    and CONCAT(#{expiDate}
                    , ' 23:59:59')
                </when>
                <when test='ruleScenType == "1"'>
                    AND d.discharge_date BETWEEN #{begnDate}
                    and CONCAT(#{expiDate}
                    , ' 23:59:59')
                </when>
            </choose>
        </if>
        GROUP BY c.rule_grp_name,
                 c.vola_deg,
                 unique_id,
                e.diagCodes,
                e.diagNames,
                f.oprnOprtCodes,
                f.oprnOprtNames,
                d.benefit_type,
        <choose>
            <when test='ruleScenType == "2"'>
                d.mzh,
                d.admission_date,
                d.admission_dept_name,
            </when>
            <when test='ruleScenType == "1"'>
                d.zyh,
                d.discharge_date,
                d.discharge_dept_name,
            </when>
        </choose>
                 error_detail_codg
    </select>
    <select id="filteredChargeItemsByUniqueIdNew" resultType="com.my.som.vo.hcs.ChargeItemsVo">
        SELECT
        item_id       as code,
        unit_price                as price,
        num                 as count,
        cost AS detitemFeeSumamt,
        bill_date       as feeOcurTime,
        excute_dept_id      as bilgDeptCodg,
        excute_dept_name      as department,
        doctor_id        as bilgDrCodg,
        doctor_name        as bilgDrName
        FROM `hcm_settle_zy_detail_b`
        where hisid = #{uniqueId}
        <if test="chrgCode != null and chrgCode != ''">
            AND item_id LIKE CONCAT('%', #{chrgCode}, '%')
        </if>
    </select>
</mapper>
