<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.som.mapper.hcs.ExamCorrectionMapper">

    <!-- 查询规则数据 -->
    <select id="getRuleList" resultType="com.my.som.vo.hcs.ExamCorrectionRuleVo">
        SELECT
        a.`rule_id` as ruleId ,
        CASE
        WHEN a.rule_scen_type = 'be_in_hosp'  THEN '住院'
        ELSE '门诊'
        END AS ruleScenType,
        a.`rule_type_codg` as ruleTypeCodg,
        a.`rule_type_name` as ruleTypeName,
        a.`rule_grp_codg` as ruleGrpCodg,
        a.`rule_grp_name` as ruleGrpName,
        a.`rule_detl_codg` as ruleDetlCodg,
        a.`rule_detl_name` as ruleDetlName,
        a.`opra_type` as opraType,
        b.labl_name as opraTypeName,
        a.data_grp AS dataGrp,
        a.vola_deg as volaDeg,
        a.exct_type as exctType,
        a.exct_cont as exctCont,
        a.`rule_data_meta` as ruleDataMeta,
        a.`rule_year` as ruleYear,
        a.`rule_souc` as ruleSouc
        FROM hcm_rule_cfg a
        left join (
            select data_val,labl_name from som_sys_code where code_type = 'OPRA_TYPE_CODE'
        ) b on a.opra_type =b.data_val
        WHERE a.vali_flag =1
        <if test='ruleTypeName != null and ruleTypeName != "" '>
           and a.rule_type_name like CONCAT('%', #{ruleTypeName}, '%')
        </if>
        <if test='dataGrp != null and dataGrp != "" '>
            and a.data_grp = #{dataGrp,jdbcType=VARCHAR}
        </if>
        <if test='ruleGrpName != null and ruleGrpName != "" '>
            and a.rule_grp_name like CONCAT('%', #{ruleGrpName}, '%')
        </if>
        <if test="opraType != null and opraType !='' " >
            and a.opra_type = #{opraType,jdbcType=VARCHAR}
        </if>
        <if test="ruleDataMeta !=null and ruleDataMeta !=''">
            and a.rule_data_meta like CONCAT('%', #{ruleDataMeta}, '%')
        </if>
        <if test="ruleDetlCodg !=null and ruleDetlCodg!=''">
            and a.rule_detl_codg like CONCAT('%', #{ruleDetlCodg}, '%')
        </if>
        <if test='ruleYear != null and ruleYear != "" '>
            and a.rule_year = #{ruleYear,jdbcType=VARCHAR}
        </if>
        order by rule_year desc
    </select>

    <!-- 查询元组数据 -->
    <select id="queryTupleList" resultType="com.my.som.vo.hcs.ExamCorrectionTupleVo">
        SELECT
        `data_grp_code` as dataGrpCode ,
        `data_detail_code` as dataDetailCode,
        `data_code` as dataCode,
        `data_name` as dataName,
        `rule_year` as ruleYear
        FROM hcm_data_grp_cfg
        WHERE vali_flag =1
        <if test='dataName != null and dataName != "" '>
            and data_name like CONCAT('%', #{dataName}, '%')
        </if>
        <if test='ruleYear != null and ruleYear != "" '>
            and rule_year = #{ruleYear,jdbcType=VARCHAR}
        </if>
        <if test='dataDetailCode != null and dataDetailCode != "" '>
            and data_detail_code = #{dataDetailCode,jdbcType=VARCHAR}
        </if>
        <if test='dataGrpCode != null and dataGrpCode != "" '>
            and data_grp_code = #{dataGrpCode,jdbcType=VARCHAR}
        </if>
        order by rule_year desc
    </select>

    <!-- 添加规则时查询元组数据 -->
    <select id="getTupleTypeList" resultType="com.my.som.vo.hcs.ExamCorrectionTupleVo">
        SELECT
        CONCAT( data_code, '【', data_name, '】' ) AS dataCodeAndName,
        data_code as dataCode,
        data_name as dataName
        FROM
        hcm_data_grp_cfg
        GROUP BY
        data_code,
        data_name

    </select>

    <!-- 根据分组编码查询最大的分组编码 -->
    <select id="selectMaxRuleGrpCodgOld" resultType="String">
        SELECT rule_grp_codg
        FROM hcm_rule_cfg
        WHERE rule_type_codg = #{ruleTypeCodg}
        ORDER BY CAST(REGEXP_SUBSTR(rule_grp_codg, '[0-9]+') AS UNSIGNED) DESC
        LIMIT 1;

    </select>


    <!-- 根据分组编码查询最大的规则数据元 -->
    <select id="selectMaxRuleDataMetaOld" resultType="String">
        SELECT rule_data_meta
        FROM hcm_rule_cfg
        WHERE rule_type_codg = #{ruleTypeCodg}
        ORDER BY
        CAST(SUBSTRING(rule_data_meta, 6) AS UNSIGNED) DESC
        LIMIT 1;

    </select>
    <select id="getDataGroup" resultType="com.my.som.vo.hcs.ExamCorrectionTupleVo">
        select item_codg as dataCode,itemname as dataName,CONCAT(item_codg, '【', itemname, '】') as dataCodeAndName from som_med_serv_cfg
    </select>

    <!-- 插入新规则 -->
    <insert id="insertRule" >
        INSERT INTO hcm_rule_cfg
        (`rule_scen_type`, `rule_type_codg`, `rule_type_name`, `rule_grp_codg`,
        `rule_grp_name`, `rule_detl_codg`, `rule_detl_name`,`opra_type`, `data_grp`,
        `rule_data_meta`, `rule_year`, `add_time`,  `vali_flag`, `target_field`) VALUES(
        #{ruleScenType ,jdbcType=VARCHAR},
        #{ruleTypeCodg ,jdbcType=VARCHAR},
        #{ruleTypeName ,jdbcType=VARCHAR},
        #{ruleGrpCodg ,jdbcType=VARCHAR},
        #{ruleGrpName ,jdbcType=VARCHAR},
        #{ruleDetlCodg ,jdbcType=VARCHAR},
        #{ruleDetlName ,jdbcType=VARCHAR},
        #{opraType ,jdbcType=VARCHAR},
        #{dataGrp ,jdbcType=VARCHAR},
        #{ruleDataMeta ,jdbcType=VARCHAR},
        #{ruleYear ,jdbcType=VARCHAR},
        #{addTime ,jdbcType=VARCHAR},
        #{valiFlag ,jdbcType=VARCHAR},
        #{targetField ,jdbcType=VARCHAR}
        )
    </insert>


    <!-- 插入元组对应的 -->
    <insert id="insertTuples" >
        INSERT INTO `hcm_data_grp_cfg`
        ( `data_grp_code`, `data_detail_code`, `data_code`, `data_name`,  `add_time`, `vali_flag`, `rule_year`)
        VALUES
        <foreach collection="tuples" item="tuple"  separator=",">
        (#{tuple.dataGrpCode ,jdbcType=VARCHAR},
        #{tuple.dataDetailCode ,jdbcType=VARCHAR},
        #{tuple.dataCode ,jdbcType=VARCHAR},
        #{tuple.dataName ,jdbcType=VARCHAR},
        #{tuple.addTime ,jdbcType=VARCHAR},
        #{tuple.valiFlag ,jdbcType=VARCHAR},
        #{tuple.ruleYear ,jdbcType=VARCHAR})
        </foreach>
    </insert>
</mapper>
