<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.som.mapper.hcs.HcmSettleMzBMapper">

    <insert id="insertHcmSettleMzB">
        INSERT INTO hcm_settle_mz_b (
            hospital_id,
            hospital_name,
            hisid,
            p_level,
            bill_date,
            year,
            month,
            patient_id,
            social_id,
            benefit_type,
            benefit_group_id,
            mzh,
            admission_dept_id,
            admission_dept_name,
            doctor_id,
            doctor_name,
            id_card,
            patient_gender,
            patient_birthday,
            patient_age,
            claim_type,
            if_local_flag,
            admission_date,
            outpatient_disease_id,
            admission_disease_name,
            total_amount,
            bmi_pay_amount,
            cash,
            self_pay_amount,
            bmi_convered_amount,
            hospital_area,
            medical_insurance_flag,
            refund_flag_type,
            refund_hisid,
            tid,
            psn_name,
            insuplc
        ) VALUES (
                     #{hospital_id},
                     #{hospital_name},
                     #{hisid},
                     #{p_level},
                     #{bill_date},
                     #{year},
                     #{month},
                     #{patient_id},
                     #{social_id},
                     #{benefit_type},
                     #{benefit_group_id},
                     #{mzh},
                     #{admission_dept_id},
                     #{admission_dept_name},
                     #{doctor_id},
                     #{doctor_name},
                     #{id_card},
                     #{patient_gender},
                     #{patient_birthday},
                     #{patient_age},
                     #{claim_type},
                     #{if_local_flag},
                     #{admission_date},
                     #{outpatient_disease_id},
                     #{admission_disease_name},
                     #{total_amount},
                     #{bmi_pay_amount},
                     #{cash},
                     #{self_pay_amount},
                     #{bmi_convered_amount},
                     #{hospital_area},
                     #{medical_insurance_flag},
                     #{refund_flag_type},
                     #{refund_hisid},
                     #{tid},
                     #{psn_name},
                     #{insuplc}
                 )
    </insert>
    <!-- 批量插入 -->
    <insert id="insertBatch" parameterType="java.util.List">
        INSERT INTO hcm_settle_mz_b (
        hospital_id,
        hospital_name,
        hisid,
        p_level,
        bill_date,
        year,
        month,
        patient_id,
        social_id,
        benefit_type,
        benefit_group_id,
        mzh,
        admission_dept_id,
        admission_dept_name,
        doctor_id,
        doctor_name,
        id_card,
        patient_gender,
        patient_birthday,
        patient_age,
        claim_type,
        if_local_flag,
        admission_date,
        outpatient_disease_id,
        admission_disease_name,
        total_amount,
        bmi_pay_amount,
        cash,
        self_pay_amount,
        bmi_convered_amount,
        hospital_area,
        medical_insurance_flag,
        refund_flag_type,
        refund_hisid,
        tid,
        psn_name,
        insuplc
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.hospitalId},
            #{item.hospitalName},
            #{item.hisid},
            #{item.pLevel},
            #{item.billDate},
            #{item.year},
            #{item.month},
            #{item.patientId},
            #{item.socialId},
            #{item.benefitType},
            #{item.benefitGroupId},
            #{item.mzh},
            #{item.admissionDeptId},
            #{item.admissionDeptName},
            #{item.doctorId},
            #{item.doctorName},
            #{item.idCard},
            #{item.patientGender},
            #{item.patientBirthday},
            #{item.patientAge},
            #{item.claimType},
            #{item.ifLocalFlag},
            #{item.admissionDate},
            #{item.outpatientDiseaseId},
            #{item.admissionDiseaseName},
            #{item.totalAmount},
            #{item.bmiPayAmount},
            #{item.cash},
            #{item.selfPayAmount},
            #{item.bmiConveredAmount},
            #{item.hospitalArea},
            #{item.medicalInsuranceFlag},
            #{item.refundFlagType},
            #{item.refundHisid},
            #{item.tid},
            #{item.psnName},
            #{item.insuplc}
            )
        </foreach>
    </insert>
    <insert id="insetDetailBatch" parameterType="java.util.List">
        INSERT INTO hcm_settle_mz_detail_b (
        hospital_id,
        hospital_name,
        hisid,
        patient_id,
        billing_dept_id,
        billing_dept_name,
        billing_time,
        excute_dept_id,
        excute_dept_name,
        usage_date,
        doctor_id,
        doctor_name,
        bill_date,
        prescription,
        p_category,
        item_id_hosp,
        item_name_hosp,
        item_id,
        item_name,
        drug_spec,
        dosage_form,
        package_unit,
        unit_price,
        num,
        cost,
        self_pay_limit,
        bmi_convered_amount,
        p_type,
        p_type_pct,
        refund_flag_type,
        refund_hisid,
        fee_serial_no,
        refund_serial_no
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.hospitalId},
            #{item.hospitalName},
            #{item.hisid},
            #{item.patientId},
            #{item.billingDeptId},
            #{item.billingDeptName},
            #{item.billingTime},
            #{item.excuteDeptId},
            #{item.excuteDeptName},
            #{item.usageDate},
            #{item.doctorId},
            #{item.doctorName},
            #{item.billDate},
            #{item.prescription},
            #{item.pCategory},
            #{item.itemIdHosp},
            #{item.itemNameHosp},
            #{item.itemId},
            #{item.itemName},
            #{item.drugSpec},
            #{item.dosageForm},
            #{item.packageUnit},
            #{item.unitPrice},
            #{item.num},
            #{item.cost},
            #{item.selfPayLimit},
            #{item.bmiConveredAmount},
            #{item.pType},
            #{item.pTypePct},
            #{item.refundFlagType},
            #{item.refundHisid},
            #{item.feeSerialNo},
            #{item.refundSerialNo}
            )
        </foreach>
    </insert>

    <select id="getUserInfo" resultType="com.my.som.dts.entity.dto.UserInfo">
        SELECT
        a.patient_gender as gender,
        b.discharge_disease_name_main as diagnosis,
        a.admission_date as admissionDate,
        "" as yblx,
        a.psn_name as insuredName,
        a.patient_age as age,
        a.benefit_type as insuranceType,
        <choose>
            <when test="type==1">
                a.zyts as hospitalizationDays,
                "普通住院" as treatmentMethod,
                a.zyh as hospitalNumber
                FROM
                hcm_settle_zy_b a
                LEFT JOIN hcm_settle_diag_b b ON a.hisid = b.hisid
            </when>
            <otherwise>
                "普通门诊" as treatmentMethod
                a.mzh as hospitalNumber
                FROM
                hcm_settle_zy_b a
                LEFT JOIN hcm_settle_diag_b b ON a.hisid = b.hisid
            </otherwise>
        </choose>
        where a.hisid = #{hisid}

    </select>
    <select id="getViolationTable" resultType="com.my.som.dts.entity.dto.Violation">
        SELECT
        FALSE AS processed,
        b.medical_insurance_flag AS selfPay,
        "" AS auditChannel,
        case
        when a.vola_deg='2' THEN "高度可疑"
        ELSE "明确违规"
        end as violationLevel,
        a.med_list_codg AS itemCode,
        a.med_list_name AS itemName,
        b.admission_date AS itemTime,
        FALSE AS selected
        FROM
        <choose>
            <when test="type==1">
                hcm_valid_result_inhosp a
                LEFT JOIN hcm_settle_zy_b b ON a.unique_id = b.hisid
            </when>
            <otherwise>
                hcm_valid_result_outhosp a
                LEFT JOIN hcm_settle_mz_b b ON a.unique_id = b.hisid
            </otherwise>
        </choose>
        where b.hisid = #{hisid}
    </select>
    <select id="getRightsTable" resultType="java.lang.String">
        SELECT
        DISTINCT (CONCAT(a.error_type, '：', a.error_desc, GROUP_CONCAT(DISTINCT(b.data_name), '、'))) as notice
        FROM
        <choose>
            <when test="type==1">
                hcm_valid_result_inhosp a
            </when>
            <otherwise>
                hcm_valid_result_outhosp a
            </otherwise>
        </choose>
        INNER JOIN hcm_data_grp_cfg b ON a.med_list_codg = b.data_code
        AND LEFT(a.oprn_date, 4) = b.rule_year
        AND b.data_grp_code = a.rule_data_meta
        WHERE
        unique_id = #{hisid}
        GROUP BY
        a.error_type,
        a.error_desc;
    </select>
    <select id="getDetailTable" resultType="com.my.som.dts.entity.dto.DetailCell">
        SELECT
        MIN(a.bill_date) AS projectDate,
        a.item_id AS itemCode,
        a.item_name as itemName,
        a.drug_spec as drugSpec,
        a.unit_price as price,
        SUM(a.num) as number,
        a.billing_dept_name as deptName,
        10 as drugUsedDays,
        1 as perDosage,
        1 as frequency,
        "西药" as itemType,
        SUM(a.cost) as totalAmount,
        CONCAT(b.error_type,b.error_desc) as errCode,
        b.error_type
        FROM
        <choose>
            <when test="type==1">
                hcm_settle_zy_detail_b a
                LEFT JOIN
                hcm_valid_result_inhosp b
            </when>
            <otherwise>
                hcm_settle_mz_detail_b a
                LEFT JOIN
                hcm_valid_result_outhosp b
            </otherwise>
        </choose>
        ON a.hisid = b.unique_id
        WHERE a.hisid=#{hisid}
        AND a.item_id = b.med_list_codg
        GROUP BY
        a.item_id,
        a.item_name,
        a.drug_spec,
        a.unit_price,
        a.billing_dept_name,
        b.error_type,
        b.error_desc
    </select>


</mapper>
