<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.my.som.mapper.sys.SysHospitalEnvGenerateMapper">

    <!-- 生成医院标杆 -->
    <insert id="generateDipHosBenchmark">
        insert into som_dip_standard(
            dip_diag_codg,
            dip_diag_name,
            dip_oprt_codg,
            dip_oprt_name,
            dip_codg,
            DIP_NAME,
            is_used_asst_list,
            asst_list_age_grp,
            asst_list_dise_sev_deg,
            asst_list_tmor_sev_deg,
            dip_standard_inpf,
            dip_standard_avg_fee_same_lv,
            last_year_avg_fee,
            drug_ratio,
            consum_ratio,
            dip_standard_ipt_days,
            dip_standard_ipt_days_same_lv,
            dip_wt,
            refer_sco,
            adjm_cof,
            uplmt_mag,
            lowlmt_mag,
            dip_setl_fee,
            STANDARD_YEAR,
            ACTIVE_FLAG,
            HOSPITAL_ID,
            is_sd_dise
        )
        select a.dip_diag_codg,
               a.dip_diag_name,
               a.dip_oprt_codg,
               a.dip_oprt_name,
               a.dip_codg,
               a.DIP_NAME,
               a.is_used_asst_list,
               a.asst_list_age_grp,
               a.asst_list_dise_sev_deg,
               a.asst_list_tmor_sev_deg,
               HEX(AES_ENCRYPT(a.standard_avg_fee,${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY})),
               HEX(AES_ENCRYPT(a.dip_standard_avg_fee_same_lv,${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY})),
               HEX(AES_ENCRYPT(a.last_year_avg_fee,${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY})),
               b.drug_ratio,
               b.mcs_fee_rat,
               HEX(AES_ENCRYPT(b.dip_standard_ipt_days,${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY})),
               HEX(AES_ENCRYPT(b.dip_standard_ipt_days_same_lv,${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY})),
               a.dip_wt,
               a.refer_sco,
               a.adjm_cof,
               a.uplmt_mag,
               a.lowlmt_mag,
               HEX(AES_ENCRYPT(0,${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY})), <!-- 支付费用 -->
               #{year,jdbcType=VARCHAR},
               '1', <!-- 有效标准 -->
               #{hospitalId,jdbcType=VARCHAR},
               a.is_sd_dise
        from som_dip_regn_standard a
        left join som_std_fee b
        on a.dip_codg = b.code
        and a.asst_list_age_grp = b.asst_list_age_grp
        and a.asst_list_dise_sev_deg = b.asst_list_dise_sev_deg
        and a.asst_list_tmor_sev_deg = b.asst_list_tmor_sev_deg
        and a.STANDARD_YEAR = b.STANDARD_YEAR
        and b.TYPE = #{group,jdbcType=VARCHAR}
        where a.STANDARD_YEAR = #{year,jdbcType=VARCHAR}
        and a.hosp_lv = (
            select hosp_lv
            from som_hosp_info
            where hospital_id = #{hospitalId,jdbcType=VARCHAR}
        )
    </insert>

    <!-- 生成医院DRG标杆 -->
    <insert id="generateDrgHosBenchmark">
        insert into som_drg_standard(
            STANDARD_YEAR,
            drg_codg,
            DRG_NAME,
            HOSPITAL_ID,
            hosp_lv,
            medcas_val,
            drg_wt,
            standard_ave_hosp_day,
            standard_avg_fee,
            mdc_standard_ave_hosp_day,
            mdc_standard_avg_fee,
            adrg_standard_ave_hosp_day,
            adrg_standard_avg_fee,
            AVG_AGE,
            avg_drug_fee,
            avg_mcs_fee,
            avg_abt_fee,
            inspect_fee_standard_val,
            begn_date,
            expi_date,
            ACTIVE_FLAG,
            refer_sco,
            adjm_cof,
            uplmt_mag,
            lowlmt_mag
        )
        select #{year,jdbcType=VARCHAR},
               a.drg_codg,
               a.DRG_NAME,
               #{hospitalId,jdbcType=VARCHAR},
               '',
               0,
               a.drg_wt,
               HEX(AES_ENCRYPT(b.dip_standard_ipt_days_same_lv,${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY})),
               HEX(AES_ENCRYPT(a.drg_standard_inpf,${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY})),
               a.mdc_standard_ave_hosp_day,
               a.mdc_standard_avg_fee,
               a.adrg_standard_ave_hosp_day,
               a.adrg_standard_avg_fee,
               a.AVG_AGE,
               b.drug_ratio,
               b.mcs_fee_rat,
               0,
               0,
               now(),
               '',
               '1',
               a.refer_sco,
               a.adjm_cof,
               a.uplmt_mag,
               a.lowlmt_mag
        from som_drg_regn_standard a
        left join som_std_fee b
        on a.drg_codg = b.`CODE`
        and b.type = #{group,jdbcType=VARCHAR}
        where a.STANDARD_YEAR = #{year,jdbcType=VARCHAR}
        <if test="hosLevelNotNull">
            and a.hosp_lv = (
                select hosp_lv
                from som_hosp_info
                where hospital_id = #{hospitalId,jdbcType=VARCHAR}
            )
        </if>
    </insert>

    <!-- 生成DIP费用区间 -->
    <insert id="generateDipMagnificaInterval">
        insert into som_dip_supe_ultra_low_bind(
            YEAR,
            CODE,
            NAME,
            is_used_asst_list,
            asst_list_age_grp,
            asst_list_dise_sev_deg,
            asst_list_tmor_sev_deg,
            high_fee,
            min_fee,
            stsb_fee,
            TYPE,
            HOSPITAL_ID,
            ACTIVE_FLAG
        )
        select YEAR,
               b.CODE,
               b.NAME,
               b.is_used_asst_list,
               b.asst_list_age_grp,
               b.asst_list_dise_sev_deg,
               b.asst_list_tmor_sev_deg,
               b.high_fee,
               b.min_fee,
               concat(min_fee,'-',high_fee) as stsb_fee,
               '1',
               b.HOSPITAL_ID,
               '1'
        from (
         select
            a.*,
            uplmt_mag * standard_fee as high_fee,
            lowlmt_mag * standard_fee as min_fee
         from(
          select  ifnull(case when dip_standard_avg_fee_same_lv is not null and dip_standard_avg_fee_same_lv != '' then
                  AES_DECRYPT(UNHEX(dip_standard_avg_fee_same_lv),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) else
                  AES_DECRYPT(UNHEX(dip_standard_inpf),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) end, 0) as standard_fee,
                  uplmt_mag,
                  lowlmt_mag,
                  STANDARD_YEAR AS YEAR,
                  dip_codg AS CODE,
                  DIP_NAME AS NAME,
                  is_used_asst_list,
                  asst_list_age_grp,
                  asst_list_dise_sev_deg,
                  asst_list_tmor_sev_deg,
                  ACTIVE_FLAG,
                  HOSPITAL_ID
          from som_dip_standard
          where STANDARD_YEAR = #{year,jdbcType=VARCHAR}
          AND HOSPITAL_ID = #{hospitalId,jdbcType=VARCHAR}
         ) a
        ) b
    </insert>

    <!-- 生成DRG费用区间 -->
    <insert id="generateDrgMagnificaInterval">
        insert into som_drg_supe_ultra_low_bind(
            YEAR,
            CODE,
            NAME,
            high_fee,
            min_fee,
            stsb_fee,
            HOSPITAL_ID,
            ACTIVE_FLAG
        )
        select YEAR,
               b.CODE,
               b.NAME,
               b.high_fee,
               b.min_fee,
               concat(min_fee,'-',high_fee) as stsb_fee,
               HOSPITAL_ID,
               ACTIVE_FLAG
        from (
                 select uplmt_mag * AES_DECRYPT(UNHEX(standard_avg_fee), ${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) as high_fee,
                        lowlmt_mag * AES_DECRYPT(UNHEX(standard_avg_fee), ${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) as min_fee,
                        STANDARD_YEAR AS YEAR,
                        drg_codg AS CODE,
                        DRG_NAME AS NAME,
                        HOSPITAL_ID,
                        ACTIVE_FLAG
                 from som_drg_standard
                 where STANDARD_YEAR = #{year,jdbcType=VARCHAR}
                 AND HOSPITAL_ID = #{hospitalId,jdbcType=VARCHAR}
         ) b
    </insert>

    <!-- 修改系统配置 -->
    <update id="modifySysConfig">
        UPDATE som_sys_gen_cfg SET value = #{value,jdbcType=VARCHAR}
        WHERE id = #{id,jdbcType=VARCHAR}
    </update>

    <!-- 删除当前医院组织机构信息 -->
    <delete id="deleteOrgInfo">
        delete from som_bas_dept
        where org_id in (
            <include refid="orgIdsQuery"/>
        )
    </delete>

    <!-- 删除用户角色信息 -->
    <delete id="deleteUserRoleInfo">
        delete from som_user_role where user_id in (
            <foreach collection="ids" item="id" separator=",">
                #{id,jdbcType=VARCHAR}
            </foreach>
        )
    </delete>

    <!-- 删除用户 -->
    <delete id="deleteUserInfo">
        delete from som_back_user where id in (
            <foreach collection="ids" item="id" separator=",">
                #{id,jdbcType=VARCHAR}
            </foreach>
        )
    </delete>

    <!-- 清空环境 -->
    <delete id="truncateAllEnv">
        <!-- 流程表 -->
        <!-- 结算清单信息表（新） -->
        TRUNCATE TABLE som_setl_intf_bas_info;
        <!-- 结算清单诊断信息表（新） -->
        TRUNCATE TABLE som_setl_intf_diag;
        <!-- 结算清单重症监护信息表（新） -->
        TRUNCATE TABLE som_setl_intf_scs_cutd;
        <!-- 结算清单费用信息表（新） -->
        TRUNCATE TABLE som_setl_intf_chrg_item;
        <!-- 结算清单手术信息表（新） -->
        TRUNCATE TABLE som_setl_intf_oprn;
        <!-- 结算清单门诊慢特病信息表（新） -->
        TRUNCATE TABLE som_setl_intf_slow_special;
        <!-- 结算清单基金支付信息表（新） -->
        TRUNCATE TABLE som_setl_intf_fund_pay;
        <!-- 病案首页信息表（新） -->
        TRUNCATE TABLE som_medcas_intf_bas_info;
        <!-- 病案首页诊断信息表（新） -->
        TRUNCATE TABLE som_medcas_intf_diag_info;
        <!-- 病案首页重症监护信息表（新） -->
        TRUNCATE TABLE som_medcas_intf_scs_cutd_info;
        <!-- 病案首页手术信息表（新） -->
        TRUNCATE TABLE som_medcas_intf_oprn_oprt_info;
        <!-- 住院结算信息表（新） -->
        TRUNCATE TABLE som_chrg_item_intf;
        <!-- 住院医嘱信息表（新） -->
        TRUNCATE TABLE som_drord_info_intf;
        <!-- 临床检查信息表（新） -->
        TRUNCATE TABLE som_clnc_examrpt_main;
        <!-- 临床检查详情信息表（新） -->
        TRUNCATE TABLE som_exam_item_info;
        <!-- 临床检查样本信息表（新） -->
        TRUNCATE TABLE som_exam_spcm_info;
        <!-- 临床检查影像信息表（新） -->
        TRUNCATE TABLE som_exam_img_info;
        <!-- 临床检验信息表（新） -->
        TRUNCATE TABLE som_clnc_test_rpot_main;
        <!-- 临床检验详情信息表（新） -->
        TRUNCATE TABLE som_test_detl_info;
        <!-- 临床检验样本信息表（新） -->
        TRUNCATE TABLE som_test_spcm_info;
        <!-- 电子病历信息表（新） -->
        TRUNCATE TABLE som_elec_medrcd_info;
        <!-- 电子病历鉴别诊断记录表（新） -->
        TRUNCATE TABLE som_elec_medrcd_codse_rcd;
        <!-- 电子病历诊断信息表（新） -->
        TRUNCATE TABLE som_elec_medrcd_diag_info;
        <!-- 电子病历死亡记录表（新） -->
        TRUNCATE TABLE som_elec_medrcd_die_rcd;
        <!-- 电子病历手术信息表（新） -->
        TRUNCATE TABLE som_elec_medrcd_oprn_rcd;
        <!-- 电子病历出院小结信息表（新） -->
        TRUNCATE TABLE som_elec_medrcd_dscg_sumy;
        <!-- 电子病历抢救信息表（新） -->
        TRUNCATE TABLE som_elec_medrcd_cond_resc_rcd;
        TRUNCATE TABLE som_otp_mdtrt_info;
        TRUNCATE TABLE som_otp_mdtrt_diag_info;
        TRUNCATE TABLE som_fee_detl_info;
        <!-- 住院费用信息表（新） -->
        TRUNCATE TABLE som_chrg_detl_intf;
        <!-- 基本信息表 -->
        <!-- 结算清单信息表 -->
        TRUNCATE TABLE som_hi_invy_bas_info;
        <!-- 结算清单诊断信息表 -->
        TRUNCATE TABLE som_diag;
        <!-- 结算清单手术表 -->
        TRUNCATE TABLE som_oprn_oprt_info;
        <!-- 结算清单门诊慢特病信息表 -->
        TRUNCATE TABLE som_otp_slow_special_trt_info;
        <!-- 结算清单重症监护信息表 -->
        TRUNCATE TABLE som_setl_invy_scs_cutd_info;
        <!-- 结算清单费用信息表 -->
        TRUNCATE TABLE som_hi_setl_invy_med_fee_info;
        <!-- 结算清单基金支付信息表 -->
        TRUNCATE TABLE som_fund_pay;
        <!-- 结算清单输血表 -->
        TRUNCATE TABLE som_setl_invy_bld_info;

        <!-- 分组产生数据表 -->
        <!-- DRG分组记录表 -->
        TRUNCATE TABLE som_drg_grp_rcd;
        <!-- DRG分组日志表 -->
        TRUNCATE TABLE som_drg_grper_intf_trns_log;
        <!-- DIP分组记录表 -->
        TRUNCATE TABLE som_dip_grp_rcd;
        <!-- DIP分组日志表 -->
        TRUNCATE TABLE som_dip_grper_intf_trns_log;
        <!-- 成都分组记录表 -->
        TRUNCATE TABLE som_grp_rcd;
        <!-- 成都分组日志表 -->
        TRUNCATE TABLE som_cd_grper_intf_trns_log;
        <!-- DRG信息表 -->
        TRUNCATE TABLE som_drg_grp_info;
        <!-- 成都信息表 -->
        TRUNCATE TABLE som_cd_grp_info;
        <!-- DIP信息表 -->
        TRUNCATE TABLE som_dip_grp_info;
        <!-- 数据处理日志表 -->
        TRUNCATE TABLE som_datapros_log;
        <!-- 编码资源消耗预表(校验) -->
        TRUNCATE TABLE som_codg_resu_adjm_rcd;
        <!-- 结算清单校验错误记录(校验,清洗) -->
        TRUNCATE TABLE som_setl_invy_chk_err_rcd;
        <!-- 结算清单质量扣分明细表(校验) -->
        TRUNCATE TABLE som_setl_invy_qlt_dedu_point_detl;
        <!-- DIP分值信息表 -->
        TRUNCATE TABLE som_dip_sco;
        <!-- DRG分值信息表 -->
        TRUNCATE TABLE som_drg_sco;
        <!-- 结算清单校验 -->
        TRUNCATE TABLE som_setl_invy_chk;
        <!-- 结算清单校验明细 -->
        TRUNCATE TABLE som_invy_chk_detl;
        <!-- 病案首页校验信息表 -->
        TRUNCATE TABLE som_init_hi_setl_invy_med_fee_info;
        <!-- 病案首页校验诊断信息表 -->
        TRUNCATE TABLE som_init_diag_info;
        <!-- 病案首页校验手术信息表 -->
        TRUNCATE TABLE som_medcas_hmpg_oprn_info;
        <!-- 可优化病案患者信息表 -->
        TRUNCATE TABLE som_can_opt_medcas_info;
        <!-- 可优化病案病组信息表 -->
        TRUNCATE TABLE som_in_group_rcd;
        <!-- 结算清单诊断修改信息表 -->
        TRUNCATE TABLE som_his_diag;
        <!-- 结算清单手术修改信息表 -->
        TRUNCATE TABLE som_his_oprn_oprt_info;
        <!-- DIP信息备份表 -->
        TRUNCATE TABLE som_dip_grp_bac;
        <!-- DRG费用范围表 -->
        TRUNCATE TABLE som_drg_standard;
        <!-- DRG标杆信息表 -->
        TRUNCATE TABLE som_drg_supe_ultra_low_bind;
        <!-- 成都标杆信息表 -->
        TRUNCATE TABLE som_cd_standard_info;
        <!-- DIP标杆表 -->
        TRUNCATE TABLE som_dip_standard;
        <!-- DIP费用范围表 -->
        TRUNCATE TABLE som_dip_supe_ultra_low_bind;
        <!-- DIP区域标杆表 -->
        TRUNCATE TABLE som_dip_regn_standard;
        <!-- DRG区域标杆表 -->
        TRUNCATE TABLE som_drg_regn_standard;
        <!-- 标杆费用表 -->
        TRUNCATE TABLE som_std_fee;
        <!-- DRG表 -->
        TRUNCATE TABLE som_drg_name;

        <!-- DIP反馈数据上传信息表 -->
        TRUNCATE TABLE som_fund_dfr_fbck;
        <!-- DIP反馈数据详情信息表 -->
        TRUNCATE TABLE som_dip_grp_fbck;
        <!-- DRG反馈数据上传信息表 -->
        TRUNCATE TABLE som_drg_pt_val_pay;
        <!-- DRG反馈数据详情信息表 -->
        TRUNCATE TABLE som_drg_grp_fbck;

        <!-- APP标签表 -->
        TRUNCATE TABLE app_cfg_label;
        <!-- APP收藏表 -->
        TRUNCATE TABLE app_collection;
        <!-- APP配置表 -->
        TRUNCATE TABLE app_config;
        <!-- APP反馈表 -->
        TRUNCATE TABLE app_feedback;
        <!-- APP信息表 -->
        TRUNCATE TABLE app_message;
        <!-- APP历史信息表 -->
        TRUNCATE TABLE app_message_history;
        <!-- APP搜索记录表 -->
        TRUNCATE TABLE app_search_history;
        <!-- APP用户头像表 -->
        TRUNCATE TABLE app_user_image;
        <!-- APP版本表 -->
        TRUNCATE TABLE app_versions;

        <!-- 组织架构表 -->
        <!-- 组织架构信息表 -->
        TRUNCATE TABLE som_bas_dept;
        <!-- 系统用户信息表 -->
        TRUNCATE TABLE som_back_user;
        <!-- 用户权限信息表 -->
        TRUNCATE TABLE som_user_role;
        <!-- 医院信息表 -->
        TRUNCATE TABLE som_hosp_info;
        <!-- 科室信息表 -->
        TRUNCATE TABLE som_dept;
        <!-- 医生信息表 -->
        TRUNCATE TABLE som_medstff_info;
    </delete>

    <!-- 删除DIP标杆 -->
    <delete id="deleteDipBenchmark">
        DELETE FROM som_dip_standard
        WHERE STANDARD_YEAR = #{year,jdbcType=VARCHAR}
        AND HOSPITAL_ID = #{hospitalId,jdbcType=VARCHAR}
    </delete>

    <!-- 删除DRG标杆 -->
    <delete id="deleteDrgBenchmark">
        DELETE FROM som_drg_standard
        WHERE STANDARD_YEAR = #{year,jdbcType=VARCHAR}
        AND HOSPITAL_ID = #{hospitalId,jdbcType=VARCHAR}
    </delete>

    <!-- 删除DIP费用区间表 -->
    <delete id="deleteDipMagnificaInterval">
        DELETE FROM som_dip_supe_ultra_low_bind
        WHERE YEAR = #{year,jdbcType=VARCHAR}
        AND HOSPITAL_ID = #{hospitalId,jdbcType=VARCHAR}
    </delete>

    <!-- 删除DRG费用区间表 -->
    <delete id="deleteDrgMagnificaInterval">
        DELETE FROM som_drg_supe_ultra_low_bind
        WHERE YEAR = #{year,jdbcType=VARCHAR}
        AND HOSPITAL_ID = #{hospitalId,jdbcType=VARCHAR}
    </delete>

    <!-- 清空环境 -->
    <update id="truncateAllEnv2">
        ${truncateSql}
    </update>

    <!-- 修改确认项 -->
    <update id="updateConfirm">
        UPDATE som_env_cnfm
        SET complete = #{complete,jdbcType=VARCHAR},
            updt_time = now()
        WHERE id = #{id,jdbcType=VARCHAR}
    </update>

    <!-- 查询所有需要删除的用户id -->
    <select id="queryAllDeleteUserId" resultType="java.lang.String">
        select distinct id from (
            select id from som_back_user where blng_org_org_id in (
                <include refid="orgIdsQuery"/>
            )
            union all
            select id from som_back_user where HOSPITAL_ID = #{hospitalId,jdbcType=VARCHAR}
        ) a
    </select>
<!-- 插入tpd——drgs表-->
    <insert id="insertTpdDrgs">
        TRUNCATE TABLE som_drg_name;
        insert into som_drg_name (
          grper_type, drg_codg, DRG_NAME,
          mdc_codg, adrg_codg, ADRG_NAME,
          begn_date, expi_date, ACTIVE_FLAG
        )
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.grperType,jdbcType=VARCHAR}, #{item.drgCodg,jdbcType=VARCHAR}, #{item.drgName,jdbcType=VARCHAR},
            #{item.mdcCodg,jdbcType=VARCHAR}, #{item.adrgCodg,jdbcType=VARCHAR}, #{item.adrgName,jdbcType=VARCHAR},
            #{item.begnDate,jdbcType=VARCHAR}, #{item.expiDate,jdbcType=VARCHAR}, #{item.activeFlag,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>

    <!-- 新增确认项 -->
    <insert id="addConfirm">
        INSERT INTO som_env_cnfm
        VALUES(null,
               #{confirmName,jdbcType=VARCHAR},
               #{memo_info,jdbcType=VARCHAR},
               #{complete,jdbcType=VARCHAR}
        )
    </insert>

    <!-- 生成tpd_drgs表 -->
    <insert id="generateDrgs">
        insert into som_drg_name(grper_type, drg_codg,drg_name,mdc_codg,adrg_codg,adrg_name, begn_date,expi_date, active_flag)
        select '3',
               drg_codg,
               drg_name,
               concat('MDC',substr(drg_codg,1,1)),
               substr(drg_codg,1,3),
               substr(drg_name,1, case when LOCATE(',', drg_name) = 0 then length(drg_name) else LOCATE(',', drg_name) - 1 end),
               '',
               '',
               '1'
        from som_drg_standard where STANDARD_YEAR = YEAR(now());
    </insert>

    <select id="selectTableCount" resultType="com.my.som.vo.SysHospitalTableVo">
        SELECT  COUNT(1) as count,
                #{dto.tabName} as tabName,
                #{dto.tableNameChinese} as tableNameChinese
        from ${dto.tabName}
        where ${dto.yearField} =#{dto.standardYear}
    </select>

    <select id="selectUpdateLog" resultType="com.my.som.vo.SysHospitalTableVo">
        select tab_name as tabName,
               TIME as time,
               SPECIAL_UPDATES as specialFlag
        from som_gena_hosp_cfg_updt_log
    </select>

    <!-- 查询标杆是否分级别 -->
    <select id="queryBenchmarkLevelDataExists" resultType="java.lang.Integer">
        select count(1) from som_drg_regn_standard where hosp_lv is not null
    </select>

    <!-- 查询是否存在 -->
    <select id="queryExists" resultType="java.lang.Integer">
        select count(1)
        from ${tabName}
    </select>

    <!-- 查询清单上传信息是否存在 -->
    <select id="querySelInfoExists" resultType="java.lang.Integer">
        select count(1)
        from som_setl_invy_upld_cfg a
        inner join som_hosp_info b
        on a.FIXMEDINS_CODE = b.HOSPITAL_ID
    </select>

    <!-- 查询和年份相关信息是否存在 -->
    <select id="queryYearInfoExists" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM ${tabName}
        WHERE standard_year = YEAR(NOW())
    </select>

    <!-- 查询确认项是否存在 -->
    <select id="queryConfirmExists" resultType="java.lang.Integer">
        select count(1)
        from som_env_cnfm
        where name = #{confirmName,jdbcType=VARCHAR}
    </select>

    <!-- 查询确认项 -->
    <select id="queryConfirm" resultType="com.my.som.vo.sys.EnvConfirmVo">
        select id, name, memo_info , complete, updt_time as updtTime
        from som_env_cnfm
    </select>

    <!-- 查询org_id -->
    <sql id="orgIdsQuery">
        with  RECURSIVE temp AS (
            select * from som_bas_dept r where ORG_ID = #{hospitalId,jdbcType=VARCHAR}
            UNION ALL
            SELECT b.* from som_bas_dept b, temp t where b.prnt_dept_id = t.ORG_ID
        )
        select org_id from temp
    </sql>

</mapper>
