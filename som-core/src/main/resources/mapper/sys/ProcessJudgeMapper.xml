<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.my.som.mapper.sys.ProcessJudgeMapper">
    <update id="setDataLogId">
        UPDATE som_hi_invy_bas_info SET DATA_LOG_ID = '-1'
        <where>
            <choose>
                <when test="dateType == 1">
                    AND SUBSTRING(B15,1,4) = #{year}
                </when>
                <when test="dateType == 2">
                    AND SUBSTRING(B15,1,7) = #{month}
                </when>
                <when test="dateType == 3">
                    AND SUBSTRING(B15,1,10) BETWEEN #{begnDate} AND CONCAT(#{expiDate},' 23:59:59')
                </when>
            </choose>
        </where>;
    </update>
    <update id="setDataLogIdByDate">
        update som_hi_invy_bas_info set DATA_LOG_ID = NULL where 1 = 1
        <if test="year != null and year != ''">
            AND SUBSTRING(B15,1,4) = #{year}
        </if>
        <if test="month != null and month != ''">
            AND SUBSTRING(B15,1,7) = #{month}
        </if>
        <if test="begnDate != null and begnDate != '' and expiDate != null and expiDate != ''">
            AND SUBSTRING(B15,1,10) between #{begnDate} and CONCAT(#{expiDate},' 23:59:59')
        </if>
    </update>
    <update id="updateStart">
        update som_prcs_seq set enab_flag = #{enabFlag} where ID = #{id}
    </update>

    <!-- 根据settleListIds删除 -->
    <update id="setDataLogIdByIds">
        update som_hi_invy_bas_info set DATA_LOG_ID = NULL
        WHERE ID IN
        <foreach collection="list" item="id" open="(" close=")" separator=",">
            #{id,jdbcType=VARCHAR}
        </foreach>
    </update>

    <!-- 修改清单数据 -->
    <update id="updateSettleList">
        UPDATE som_hi_invy_bas_info
        SET
        <foreach collection="data" item="item" separator=",">
            ${item.key} = #{item.value,jdbcType=VARCHAR}
        </foreach>
        WHERE id = #{id,jdbcType=VARCHAR}
    </update>

    <delete id="deleteBySettlelisIds">
        delete from ${tabName} where SETTLE_LIST_ID in
        <foreach collection="ids" open="(" item="id" separator="," close=")" index="index">
            #{id}
        </foreach>
    </delete>
    <delete id="deleteBySettlelisId">
        delete from ${tabName}
    </delete>

    <select id="queryProcessJudge" resultType="com.my.som.vo.sys.ProcessJudgeVo">
        SELECT  ID AS id,
        TASK_NAME AS taskName,
        TASK_KEY AS taskKey,
        enab_flag AS enabFlag,
        exe_seq AS exeSeq
        FROM som_prcs_seq
        where 1=1
        <if test="taskName != null and taskName != ''">
            AND TASK_NAME LIKE CONCAT('%',#{taskName},'%')
        </if>
        <if test="taskKey != null and taskKey != ''">
            AND TASK_KEY LIKE CONCAT('%',#{taskKey},'%')
        </if>
        <if test="enabFlag != null and enabFlag != ''">
            AND enab_flag LIKE #{enabFlag}
        </if>
    </select>
    <select id="querySettleListId" resultType="java.lang.String">
        SELECT ID AS settleListId
        FROM som_hi_invy_bas_info
        <where>
            <choose>
                <when test="dateType == 1">
                    AND SUBSTRING(B15,1,4) = #{year}
                </when>
                <when test="dateType == 2">
                    AND SUBSTRING(B15,1,7) = #{month}
                </when>
                <when test="dateType == 3">
                    AND SUBSTRING(B15,1,10) between #{begnDate} and CONCAT(#{expiDate},' 23:59:59')
                </when>
            </choose>
        </where>
    </select>

    <select id="queryMedicalRecordId" resultType="java.lang.String">
        SELECT ID AS medcasHmpgId
        FROM som_init_hi_setl_invy_med_fee_info
        <where>
            <if test="ids != null and ids.size() > 0">
                <foreach collection="ids" item="id" open="AND SETTLE_LIST_ID IN (" close=")" index="index" separator=",">
                    #{id}
                </foreach>
            </if>
        </where>
    </select>

    <delete id="deleteByMedicalRecordIds">
        DELETE FROM ${tabName}
        <where>
            <foreach collection="ids" item="id" open="AND SETTLE_LIST_ID IN (" separator="," close=")">
                #{id}
            </foreach>
        </where>
    </delete>

</mapper>