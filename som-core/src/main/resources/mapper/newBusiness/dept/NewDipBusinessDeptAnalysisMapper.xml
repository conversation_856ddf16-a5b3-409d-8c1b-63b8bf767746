<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.som.mapper.newBusiness.dept.NewDipBusinessDeptAnalysisMapper">

    <!-- 查询科室Kpi -->
    <select id="queryKpiData" resultType="com.my.som.vo.newBusiness.dept.NewBusinessDeptVo">
        select a.*,
               b.NAME as deptName
        from
            (
                select a.dscg_caty_codg_inhosp as deptCode,
                       a.HOSPITAL_ID,
                       IFNULL(count(1),0) as medicalTotalNum,
                       <choose>
                           <when test="feeStas==0">
                               ROUND(AVG(a.ipt_sumfee),2) AS avgFee,
                           </when>
                           <when test="feeStas==1">
                               ROUND(AVG(z.sumfee),2) AS avgFee,
                           </when>
                       </choose>
                       ROUND(AVG(a.act_ipt),2) AS avgInHosDays,
                       ROUND(IFNULL(SUM(c.dip_wt),0),2) AS totalWeight,
                       <choose>
                           <when test="feeStas==0">
                               ROUND(SUM(CASE WHEN a.grp_stas = '1' THEN a.dip_wt ELSE NULL END) / NULLIF (COUNT(CASE WHEN a.grp_stas = '1' THEN 1 ELSE NULL END),0),2) as cmi,
                               IFNULL(count(DISTINCT (CASE WHEN a.grp_stas = '1' THEN concat( a.dip_codg, ifnull(a.asst_list_age_grp,0),  ifnull(a.asst_list_dise_sev_deg,0),ifnull(a.asst_list_tmor_sev_deg,0)) ELSE NULL END )),0 ) AS groupNum,
                               IFNULL(ROUND(NULLIF(sum(case when a.grp_stas = '1' then 1 else 0 end),0)/count(1) * 100,2),0) as inGroupRate,
                               count(case when a.grp_stas = '1' then 1 else null end) as drgInGroupMedcasVal,
                               count(case when a.grp_stas != '1' then 1 else null end) as nonGroupNum,
                               IFNULL(ROUND(SUM(CASE WHEN a.grp_stas = '1' THEN a.act_ipt ELSE NULL END / NULLIF(convert(AES_DECRYPT(UNHEX(c.dip_standard_ipt_days_same_lv),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) using utf8),0)) /
                               NULLIF(COUNT(CASE WHEN a.grp_stas = '1' THEN 1 ELSE NULL END),0), 2),0)  AS timeIndex,
                               IFNULL(ROUND(SUM(CASE WHEN a.grp_stas = '1' THEN a.ipt_sumfee ELSE NULL END / NULLIF(convert(AES_DECRYPT(UNHEX(c.dip_standard_avg_fee_same_lv),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) using utf8),0)) /
                               NULLIF(COUNT(CASE WHEN a.grp_stas = '1' THEN 1 ELSE NULL END),0), 2),0)   AS costIndex,
                               IFNULL(ROUND(SUM(a.drugfee)/NULLIF(SUM(a.ipt_sumfee),0)*100,2),0) AS medicalCostRate,
                               IFNULL(ROUND(SUM(a.mcs_fee)/NULLIF(SUM(a.ipt_sumfee),0)*100,2),0) AS materialCostRate
                           </when>
                           <when test="feeStas==1">
                               IFNULL(count(DISTINCT (CASE WHEN a.grp_stas = '1' THEN concat( a.dip_codg, ifnull(a.asst_list_age_grp,0),  ifnull(a.asst_list_dise_sev_deg,0),ifnull(a.asst_list_tmor_sev_deg,0)) ELSE NULL END )),0 ) AS groupNum,
                               IFNULL(ROUND(NULLIF(sum(case when z.grp_stas = '1' then 1 else 0 end),0)/count(1) * 100,2),0) as inGroupRate,
                               count(case when z.grp_stas = '1' then 1 else null end) as drgInGroupMedcasVal,
                               count(case when z.grp_stas = '0' then 1 else null end) as nonGroupNum,
                               IFNULL(ROUND(SUM(CASE WHEN z.grp_stas = '1' THEN a.act_ipt ELSE NULL END / NULLIF(convert(AES_DECRYPT(UNHEX(c.dip_standard_ipt_days_same_lv),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) using utf8),0)) /
                               NULLIF(COUNT(CASE WHEN z.grp_stas = '1' THEN 1 ELSE NULL END),0), 2),0)  AS timeIndex,
                               IFNULL(ROUND(SUM(CASE WHEN z.grp_stas = '1' THEN z.sumfee ELSE NULL END / NULLIF(convert(AES_DECRYPT(UNHEX(c.dip_standard_avg_fee_same_lv),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) using utf8),0)) /
                               NULLIF(COUNT(CASE WHEN z.grp_stas = '1' THEN 1 ELSE NULL END),0), 2),0)   AS costIndex,
                               IFNULL(ROUND(SUM(a.drugfee)/NULLIF(SUM(z.sumfee),0)*100,2),0) AS medicalCostRate,
                               IFNULL(ROUND(SUM(a.mcs_fee)/NULLIF(SUM(z.sumfee),0)*100,2),0) AS materialCostRate
                           </when>
                       </choose>
                from som_dip_grp_info a
                LEFT JOIN som_hi_invy_bas_info f
                ON a.SETTLE_LIST_ID = f.ID

                <if test="enableSeAns != null and enableSeAns != '' and enableSeAns == 1">
                    INNER JOIN som_setl_cas_crsp p
                    ON f.K00 = p.K00
                </if>
                <if test="feeStas==1">
                    INNER JOIN
                    (
                        SELECT
                        a.rid_idt_codg,
                        a.medcas_codg,
                        a.adm_time,
                        a.dscg_time,
                        a.is_in_group as grp_stas,
                        a.dip_codg,
                        a.DIP_NAME,
                        b.MED_TYPE,
                        b.sumfee,
                        b.dfr_fee
                        FROM som_dip_grp_fbck a
                        LEFT JOIN som_fund_dfr_fbck b
                        ON a.rid_idt_codg = b.rid_idt_codg
                        AND a.HOSPITAL_ID = b.HOSPITAL_ID
                        WHERE a.dscg_time between #{begnDate,jdbcType=VARCHAR} and concat(#{expiDate,jdbcType=VARCHAR},' 23:59:59')
                        <include refid="com.my.som.common.mapper.CommonMapper.fbAuth" />
                    ) z
                    ON a.PATIENT_ID = z.medcas_codg
                    AND SUBSTR(a.adm_time,1,10) = z.adm_time
                    AND SUBSTR(a.dscg_time,1,10) = z.dscg_time
                </if>
                <if test="feeStas==0">
                    left join som_dip_grp_rcd b
                    on a.SETTLE_LIST_ID = b.SETTLE_LIST_ID
                </if>
                    left join som_dip_standard c
                    on a.dip_codg = c.dip_codg
                    AND ifnull(a.asst_list_age_grp,'未使用') = ifnull(c.asst_list_age_grp,'未使用')
                    AND ifnull(a.asst_list_dise_sev_deg,'未使用')  =  ifnull(c.asst_list_dise_sev_deg,'未使用')
                    AND ifnull(a.asst_list_tmor_sev_deg,'未使用')  =  ifnull(c.asst_list_tmor_sev_deg,'未使用')
                    AND ifnull(a.auxiliary_burn,'未使用')  =  ifnull(c.auxiliary_burn,'未使用')
                    AND a.HOSPITAL_ID = c.HOSPITAL_ID
                    and substr(a.dscg_time,1,4) =  c.STANDARD_YEAR
                where a.dscg_caty_codg_inhosp IS NOT NULL
                AND a.dscg_caty_codg_inhosp != ''
                <!-- 传入条件-日期 -->
                <if test="begnDate != null and begnDate != '' and expiDate != null and expiDate != ''">
                AND a.dscg_time BETWEEN #{begnDate,jdbcType=VARCHAR} AND concat(#{expiDate,jdbcType=VARCHAR},' 23:59:59')
                </if>
                <if test="inStartTime != null and inStartTime != '' and inEndTime != null and inEndTime != ''">
                    AND a.adm_time BETWEEN #{inStartTime} AND CONCAT(#{inEndTime},' 23:59:59')
                </if>
                <if test="seStartTime!=null and seStartTime!='' and seEndTime!=null and seEndTime!=''">
                    AND a.setl_end_time BETWEEN #{seStartTime} and CONCAT(#{seEndTime},' 23:59:59')
                </if>
                <if test='setlway != null and setlway != "" and setlway == "1"'>
                    AND f.setlway = #{setlway,jdbcType=VARCHAR}
                </if>
                 <if test="categories != null and categories != ''">
                     <choose>
                         <when test="categories == 1">
                             and f.A54 in ('1', '01', '310')
                         </when>
                         <when test="categories == 2">
                             and f.A54 in ('2', '02', '390')
                         </when>
                         <when test="categories == 9">
                             and f.A54 not in ('1', '01', '310', '2', '02', '390')
                         </when>
                     </choose>
                 </if>
                <!-- 通用查询条件 -->
                <include refid="queryCommon" />
                group by a.dscg_caty_codg_inhosp, a.HOSPITAL_ID
                ) a
            left join som_dept b
            on a.deptCode = b.CODE
            AND a.HOSPITAL_ID = b.HOSPITAL_ID
            ORDER BY inGroupRate DESC
    </select>

    <!-- 查询科室预测 -->
    <select id="queryForecastData" resultType="com.my.som.vo.newBusiness.dept.NewBusinessDeptVo">
        SELECT m.*,
			 n.`NAME` AS deptName
        FROM
             (
                 SELECT a.dscg_caty_codg_inhosp AS deptCode,
                        a.HOSPITAL_ID,
                        IFNULL(COUNT(1),0) AS medicalTotalNum,
                        IFNULL(COUNT(CASE WHEN a.dise_type = 4 THEN 1 ELSE NULL END) - COUNT(CASE WHEN b.grp_stas = '0' THEN 1 ELSE NULL END),0) AS nonBenchmarkNum,
                        IFNULL(COUNT(CASE WHEN a.dise_type = 3 THEN 1 ELSE NULL END),0) AS normalNum,
                        IFNULL(COUNT(CASE WHEN a.dise_type = 1 THEN 1 ELSE NULL END),0) AS ultrahighNum,
                        IFNULL(ROUND(COUNT(CASE WHEN a.dise_type = 1 THEN 1 ELSE NULL END) / COUNT(1) * 100,2),0) AS ultrahighRate,
                        IFNULL(COUNT(CASE WHEN a.dise_type = 2 THEN 1 ELSE NULL END),0) AS ultraLowNum,
                        IFNULL(ROUND(COUNT(CASE WHEN a.dise_type = 2 THEN 1 ELSE NULL END) / COUNT(1) * 100,2),0) AS ultraLowRate,
                        IFNULL(ROUND(SUM(a.ipt_sumfee),2),0) AS sumfee,
                        IFNULL(ROUND(SUM(a.ycCost),2),0) AS forecastAmount,
                        <choose>
                            <when test="feeStas==0">
                                IFNULL(ROUND(IFNULL(SUM(a.profitloss),0) -SUM(IFNULL(a.pre_hosp_examfee,0)),2),0) AS forecastAmountDiff,
                                IFNULL(ROUND(SUM(a.ipt_sumfee) / SUM(a.ycCost),2),0) AS oeVal
                            </when>
                            <when test="feeStas==1">
                                IFNULL(ROUND(SUM(a.FB_INHOS_TOTAL_COST),2),0) AS fbTotalCost,
                                IFNULL(ROUND(IFNULL(SUM(a.ycCost),0) - IFNULL(SUM(a.FB_INHOS_TOTAL_COST),0),2),0) AS forecastAmountDiff,
                                IFNULL(ROUND(SUM(a.FB_INHOS_TOTAL_COST) / SUM(a.ycCost),2),0) AS oeVal
                            </when>
                        </choose>

                 FROM
                      (
                          SELECT a.dscg_caty_codg_inhosp,
                                 a.SETTLE_LIST_ID,
                                 a.dscg_time,
                                 a.ipt_sumfee,
                                 <if test="feeStas==0">
                                 z.profitloss,
                                 </if>
                                <if test="feeStas==1">
                                    z.FB_INHOS_TOTAL_COST,
                                </if>
                                 a.HOSPITAL_ID,
                                 a.drugfee,
                                 a.mcs_fee,
                                 z.dise_type,
                                 z.ycCost,

                                 a.pre_hosp_examfee
                          FROM som_dip_grp_info a
                          LEFT JOIN som_hi_invy_bas_info f
                          ON a.SETTLE_LIST_ID = f.ID
                        <if test="enableSeAns != null and enableSeAns != '' and enableSeAns == 1">
                            INNER JOIN som_setl_cas_crsp p
                            ON f.K00 = p.K00
                        </if>
                          <choose>
                              <when test="feeStas==0">
                                  LEFT JOIN
                                  (
                                      SELECT b.SETTLE_LIST_ID,
                                      b.dise_type,
                                        b.forecast_fee AS ycCost,
                                        b.price,
                                        b.profitloss AS profitloss
                                  from som_dip_sco b
                                  ) z
                                  ON a.SETTLE_LIST_ID = z.SETTLE_LIST_ID
                              </when>
                              <when test="feeStas==1">
                              INNER JOIN
                                  (
                                  SELECT
                                      a.rid_idt_codg,
                                      a.medcas_codg,
                                      a.adm_time,
                                      a.dscg_time,
                                      a.is_in_group,
                                      a.dip_codg,
                                      b.MED_TYPE as dise_type ,
                                      b.sumfee as FB_INHOS_TOTAL_COST,
                                      b.dfr_fee as ycCost
                                  FROM som_dip_grp_fbck a
                                  LEFT JOIN som_fund_dfr_fbck b
                                  ON a.rid_idt_codg = b.rid_idt_codg
                                  AND a.HOSPITAL_ID = b.HOSPITAL_ID
                                  WHERE a.dscg_time between #{begnDate,jdbcType=VARCHAR} and concat(#{expiDate,jdbcType=VARCHAR},' 23:59:59')
                                  <include refid="com.my.som.common.mapper.CommonMapper.fbAuth" />
                                  ) z
                                  ON a.PATIENT_ID = z.medcas_codg
                                  AND SUBSTR(a.adm_time,1,10) = z.adm_time
                                  AND SUBSTR(a.dscg_time,1,10) = z.dscg_time
                              </when>
                          </choose>
                            WHERE 1=1
                                <if test="categories != null and categories != ''">
                                    <choose>
                                        <when test="categories == 1">
                                            and f.A54 in ('1', '01', '310')
                                        </when>
                                        <when test="categories == 2">
                                            and f.A54 in ('2', '02', '390')
                                        </when>
                                        <when test="categories == 9">
                                            and f.A54 not in ('1', '01', '310', '2', '02', '390')
                                        </when>
                                    </choose>
                                </if>
                                <if test="begnDate != null and begnDate != '' and expiDate != null and expiDate != ''">
                                    AND a.dscg_time BETWEEN #{begnDate,jdbcType=VARCHAR} AND concat(#{expiDate,jdbcType=VARCHAR},' 23:59:59')
                                </if>
                                <if test="inStartTime != null and inStartTime != '' and inEndTime != null and inEndTime != ''">
                                    AND a.adm_time BETWEEN #{inStartTime} AND CONCAT(#{inEndTime},' 23:59:59')
                                </if>
                                <if test="seStartTime!=null and seStartTime!='' and seEndTime!=null and seEndTime!=''">
                                    AND a.setl_end_time BETWEEN #{seStartTime} and CONCAT(#{seEndTime},' 23:59:59')
                                </if>
                                <if test='setlway != null and setlway != "" and setlway == "1"'>
                                    AND f.setlway = #{setlway,jdbcType=VARCHAR}
                                </if>
                            <!-- 通用查询条件 -->
                            <include refid="com.my.som.mapper.newBusiness.common.NewBusinessCommonAnalysisMapper.queryBusCommon" />
            ) a
            LEFT JOIN som_dip_grp_rcd b
            ON a.SETTLE_LIST_ID = b.SETTLE_LIST_ID
            GROUP BY a.dscg_caty_codg_inhosp, a.HOSPITAL_ID
        ) m
        LEFT JOIN som_dept n
        ON m.deptCode = n.`CODE`
        AND m.HOSPITAL_ID = n.HOSPITAL_ID
        AND n.ACTIVE_FLAG = '1'
    </select>

    <!-- 查询通用 -->
    <sql id="queryCommon">
        <!-- 传入条件-科室 -->
        <if test="deptCode !=null and deptCode !=''">
            AND a.dscg_caty_codg_inhosp = #{deptCode,jdbcType=VARCHAR}
        </if>
        <!-- 传入条件-医院id -->
        <if test="hospitalId != null and hospitalId != ''" >
            AND a.HOSPITAL_ID = #{hospitalId,jdbcType=VARCHAR}
        </if>
        <!-- 传入条件-医生 -->
        <if test="drCodg !=null and drCodg !=''">
            AND a.ipdr_code = #{drCodg,jdbcType=VARCHAR}
        </if>
    </sql>

</mapper>
