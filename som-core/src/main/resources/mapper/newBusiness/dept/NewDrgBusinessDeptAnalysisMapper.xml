<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.som.mapper.newBusiness.dept.NewDrgBusinessDeptAnalysisMapper">

    <!-- 查询科室Kpi -->
    <select id="queryKpiData" resultType="com.my.som.vo.newBusiness.dept.NewBusinessDeptVo">
        select a.*,
        b.NAME as deptName
        from
        (
        select a.dscg_caty_codg_inhosp as deptCode,
               a.HOSPITAL_ID AS hospitalId,
        IFNULL(count(1),0) as medicalTotalNum,
        <choose>
            <when test="feeStas==0">
                IFNULL(round(SUM(CASE WHEN a.grp_stas = '1' THEN a.drg_wt ELSE NULL END)/
                NULLIF (COUNT(CASE WHEN a.grp_stas = '1' THEN 1 ELSE NULL END),0),2),0)   AS cmi,
                ROUND(AVG(a.ipt_sumfee),2) AS avgFee,
            </when>
            <when test="feeStas==1">
                ROUND(AVG(z.sumfee),2) AS avgFee,
            </when>
        </choose>
        ROUND(AVG(a.act_ipt),2) AS avgInHosDays,
        ROUND(IFNULL(SUM(c.drg_wt),0),2) AS totalWeight,
        ROUND(IFNULL(AVG(c.drg_wt),0),2) AS avgWeight,
        IFNULL(SUM(psn_selfpay),0) AS psnSelfpay,
        IFNULL(SUM(psn_ownpay),0) AS psnOwnpay,
        IFNULL(SUM(acct_pay),0) AS acctPay,
        IFNULL(SUM(psn_cashpay),0) AS psnCashpay,
        IFNULL(SUM(fp.370100_amount),0) AS Amount370100,
        IFNULL(ROUND(SUM(e.ycCost),2),0) AS forecastAmount,
        IFNULL(ROUND(IFNULL(SUM(e.ycCost),0) - IFNULL(SUM(a.ipt_sumfee),0),2),0) AS forecastAmountDiff,
        ROUND(IFNULL(SUM(IFNULL(a.drugfee,0)),0) ,2) AS drugFee,
        ROUND(IFNULL(SUM(IFNULL(a.mcs_fee,0)),0) ,2) AS consumeFee,
        ROUND(IFNULL(SUM(IFNULL(a.ipt_sumfee,0)),0) ,2) AS sumfee,
        <choose>
            <when test="feeStas==0">
                IFNULL(count(DISTINCT(case when a.grp_stas = '1' then a.drg_codg else null end)),0) as groupNum,
                IFNULL(ROUND(NULLIF(sum(case when a.grp_stas = '1' then 1 else 0 end),0)/count(1) * 100,2),0) as inGroupRate,
                count(case when a.grp_stas = '1' then 1 else null end) as drgInGroupMedcasVal,
                count(case when a.grp_stas != '1' then 1 else null end) as nonGroupNum,
                IFNULL(ROUND(SUM(CASE WHEN a.grp_stas = '1' THEN a.act_ipt ELSE NULL END / NULLIF(convert(AES_DECRYPT(UNHEX(c.standard_ave_hosp_day),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) using utf8),0)) /
                NULLIF(COUNT(CASE WHEN a.grp_stas = '1' THEN 1 ELSE NULL END),0), 2),0)  AS timeIndex,
                IFNULL(ROUND(SUM(CASE WHEN a.grp_stas = '1' THEN a.ipt_sumfee ELSE NULL END / NULLIF(convert(AES_DECRYPT(UNHEX(c.standard_avg_fee),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) using utf8),0)) /
                NULLIF(COUNT(CASE WHEN a.grp_stas = '1' THEN 1 ELSE NULL END),0), 2),0)   AS costIndex,
                IFNULL(ROUND(SUM(a.drugfee)/NULLIF(SUM(a.ipt_sumfee),0)*100,2),0) AS medicalCostRate,
                IFNULL(ROUND(SUM(a.mcs_fee)/NULLIF(SUM(a.ipt_sumfee),0)*100,2),0) AS materialCostRate
            </when>
            <when test="feeStas==1">
                IFNULL(count(DISTINCT(case when z.grp_stas = '1' then z.drg_codg else null end)),0) as groupNum,
                IFNULL(ROUND(NULLIF(sum(case when z.grp_stas = '1' then 1 else 0 end),0)/count(1) * 100,2),0) as inGroupRate,
                count(case when z.grp_stas = '1' then 1 else null end) as drgInGroupMedcasVal,
                count(case when z.grp_stas = '0' then 1 else null end) as nonGroupNum,
                IFNULL(ROUND(SUM(CASE WHEN z.grp_stas = '1' THEN a.act_ipt ELSE NULL END / NULLIF(convert(AES_DECRYPT(UNHEX(c.standard_ave_hosp_day),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) using utf8),0)) /
                NULLIF(COUNT(CASE WHEN z.grp_stas = '1' THEN 1 ELSE NULL END),0), 2),0)  AS timeIndex,
                IFNULL(ROUND(SUM(CASE WHEN z.grp_stas = '1' THEN z.sumfee ELSE NULL END / NULLIF(convert(AES_DECRYPT(UNHEX(c.standard_avg_fee),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) using utf8),0)) /
                NULLIF(COUNT(CASE WHEN z.grp_stas = '1' THEN 1 ELSE NULL END),0), 2),0)   AS costIndex,
                IFNULL(ROUND(SUM(a.drugfee)/NULLIF(SUM(z.sumfee),0)*100,2),0) AS medicalCostRate,
                IFNULL(ROUND(SUM(a.mcs_fee)/NULLIF(SUM(z.sumfee),0)*100,2),0) AS materialCostRate
            </when>
        </choose>
        from som_drg_grp_info a
        LEFT JOIN som_hi_invy_bas_info q
        ON q.ID = a.SETTLE_LIST_ID
        <if test='enableSeAns != null and enableSeAns != "" and enableSeAns == "1"'>

            INNER JOIN som_setl_cas_crsp p
            ON q.k00 = p.k00
        </if>
        <if test="feeStas==1">
            INNER JOIN
            (
            SELECT
            a.rid_idt_codg,
            a.HOSPITAL_ID,
            a.medcas_codg,
            a.adm_time,
            a.dscg_time,
            a.is_in_group as grp_stas,
            a.drg_codg,
            a.DRG_NAME,
            b.MED_TYPE,
            b.sumfee,
            b.dfr_fee
            FROM som_drg_grp_fbck a
            LEFT JOIN som_drg_pt_val_pay b
            ON a.rid_idt_codg = b.rid_idt_codg
            AND a.HOSPITAL_ID = b.HOSPITAL_ID
            <where>
                <if test="inStartTime!=null and inStartTime!='' and
                inEndTime!=null and inEndTime!=''">
                    AND a.adm_time BETWEEN #{inStartTime,jdbcType=VARCHAR} and CONCAT(#{inEndTime,jdbcType=VARCHAR},' 23:59:59')
                </if>
                <if test="begnDate!=null and begnDate!='' and
            expiDate!=null and expiDate!=''">
                    AND a.dscg_time BETWEEN #{begnDate,jdbcType=VARCHAR} and CONCAT(#{expiDate,jdbcType=VARCHAR},' 23:59:59')
                </if>
                <if test="seStartTime!=null and seStartTime!='' and
            seEndTime!=null and seEndTime!=''">
                    AND a.setl_time BETWEEN #{seStartTime,jdbcType=VARCHAR} and CONCAT(#{seStartTime,jdbcType=VARCHAR},' 23:59:59')
                </if>
            </where>
--             WHERE a.dscg_time between #{begnDate,jdbcType=VARCHAR} and concat(#{expiDate,jdbcType=VARCHAR},' 23:59:59')
            <include refid="com.my.som.common.mapper.CommonMapper.fbAuth" />
            ) z
            ON a.PATIENT_ID = z.medcas_codg
            AND SUBSTR(a.adm_time,1,10) = z.adm_time
            AND SUBSTR(a.dscg_time,1,10) = z.dscg_time
            AND a.HOSPITAL_ID = z.HOSPITAL_ID
        </if>
        <if test="feeStas==0">
            left join som_drg_grp_rcd b
            on a.SETTLE_LIST_ID = b.SETTLE_LIST_ID
        </if>
        LEFT JOIN (
        SELECT
        hi_setl_invy_id,
        SUM(CASE WHEN fund_pay_type  = '370100' THEN fund_payamt  ELSE 0 END) AS `370100_amount`
        FROM som_fund_pay
        GROUP BY hi_setl_invy_id
        ) fp ON a.SETTLE_LIST_ID = fp.hi_setl_invy_id
        LEFT JOIN
        (
        SELECT b.SETTLE_LIST_ID,
        b.dise_type,
        b.profitloss AS profitloss,
        b.forecast_fee AS ycCost,
        b.sumfee
        FROM som_drg_sco b
        ) e
        ON a.SETTLE_LIST_ID = e.SETTLE_LIST_ID
        left join som_drg_standard c
        on a.drg_codg = c.drg_codg
        AND SUBSTR(ifnull(q.d37, a.dscg_time),1,4) = c.STANDARD_YEAR
        AND a.HOSPITAL_ID = c.HOSPITAL_ID
        and c.insuplc_admdvs = q.insuplc_admdvs
        where a.dscg_caty_codg_inhosp IS NOT NULL
        AND a.dscg_caty_codg_inhosp != ''
        <!-- 传入条件-日期 -->
        <if test="inStartTime!=null and inStartTime!='' and
                inEndTime!=null and inEndTime!=''">
            AND a.adm_time BETWEEN #{inStartTime,jdbcType=VARCHAR} and CONCAT(#{inEndTime,jdbcType=VARCHAR},' 23:59:59')
        </if>
        <if test="begnDate!=null and begnDate!='' and
            expiDate!=null and expiDate!=''">
            AND a.dscg_time BETWEEN #{begnDate,jdbcType=VARCHAR} and CONCAT(#{expiDate,jdbcType=VARCHAR},' 23:59:59')
        </if>
        <if test="seStartTime!=null and seStartTime!='' and
            seEndTime!=null and seEndTime!=''">
            AND a.setl_end_time BETWEEN #{seStartTime,jdbcType=VARCHAR} and CONCAT(#{seEndTime,jdbcType=VARCHAR},' 23:59:59')
        </if>
<!--        and a.dscg_time between #{begnDate,jdbcType=VARCHAR} and concat(#{expiDate,jdbcType=VARCHAR},' 23:59:59')-->
<!--        <if test="inStartTime != null and inStartTime != '' and inEndTime != null and inEndTime != ''">-->
<!--            AND a.adm_time BETWEEN #{inStartTime} AND CONCAT(#{inEndTime},' 23:59:59')-->
<!--        </if>-->
        <!-- 通用查询条件 -->
        <include refid="com.my.som.mapper.newBusiness.common.NewBusinessCommonAnalysisMapper.queryBusCommon" />
        group by a.dscg_caty_codg_inhosp,a.HOSPITAL_ID
        ) a
        left join som_dept b
        on a.deptCode = b.CODE
        and a.hospitalId = b.HOSPITAL_ID
        ORDER BY inGroupRate DESC
    </select>

    <!-- 查询科室预测 -->
    <select id="queryForecastData" resultType="com.my.som.vo.newBusiness.dept.NewBusinessDeptVo">
        SELECT m.*,
        n.`NAME` AS deptName
        FROM
        (
        SELECT a.dscg_caty_codg_inhosp AS deptCode,
               a.hospitalId,
        IFNULL(COUNT(1),0) AS medicalTotalNum,
        IFNULL(COUNT(CASE WHEN a.dise_type = 4 THEN 1 ELSE NULL END) - COUNT(CASE WHEN b.grp_stas = '0' THEN 1 ELSE NULL END),0) AS nonBenchmarkNum,
        IFNULL(COUNT(CASE WHEN a.dise_type = 3 THEN 1 ELSE NULL END),0) AS normalNum,
        IFNULL(COUNT(CASE WHEN a.dise_type = 1 THEN 1 ELSE NULL END),0) AS ultrahighNum,
        IFNULL(ROUND(COUNT(CASE WHEN a.dise_type = 1 THEN 1 ELSE NULL END) / COUNT(1) * 100,2),0) AS ultrahighRate,
        IFNULL(COUNT(CASE WHEN a.dise_type = 2 THEN 1 ELSE NULL END),0) AS ultraLowNum,
        IFNULL(ROUND(COUNT(CASE WHEN a.dise_type = 2 THEN 1 ELSE NULL END) / COUNT(1) * 100,2),0) AS ultraLowRate,
        IFNULL(ROUND(SUM(a.ipt_sumfee),2),0) AS sumfee,
        IFNULL(ROUND(SUM(a.ycCost),2),0) AS forecastAmount,
        <choose>
            <when test="feeStas==0">
                IFNULL(ROUND(IFNULL(SUM(a.ycCost),0) - IFNULL(SUM(a.ipt_sumfee),0),2),0) AS forecastAmountDiff,
                IFNULL(ROUND(SUM(a.ipt_sumfee) / SUM(a.ycCost),2),0) AS oeVal
            </when>
            <when test="feeStas==1">
                IFNULL(ROUND(SUM(a.FB_INHOS_TOTAL_COST),2),0) AS fbTotalCost,
                IFNULL(ROUND(IFNULL(SUM(a.ycCost),0) - IFNULL(SUM(a.FB_INHOS_TOTAL_COST),0),2),0) AS forecastAmountDiff,
                IFNULL(ROUND(SUM(a.FB_INHOS_TOTAL_COST) / SUM(a.ycCost),2),0) AS oeVal
            </when>
        </choose>
        FROM
        (
        SELECT a.dscg_caty_codg_inhosp,
        a.SETTLE_LIST_ID,
        a.dscg_time,
        a.ipt_sumfee,
        a.HOSPITAL_ID AS hospitalId,
        <if test="feeStas==1">
            z.FB_INHOS_TOTAL_COST,
        </if>
        a.drugfee,
        a.mcs_fee,
        z.dise_type,
        z.ycCost
        FROM som_drg_grp_info a
        <if test='enableSeAns != null and enableSeAns != "" and enableSeAns == "1"'>
            LEFT JOIN som_hi_invy_bas_info q
            ON q.ID = a.SETTLE_LIST_ID
            INNER JOIN som_setl_cas_crsp p
            ON q.k00 = p.k00
        </if>
        <choose>
            <when test="feeStas==0">
                LEFT JOIN
                (
                SELECT b.SETTLE_LIST_ID,
                b.dise_type,
                b.profitloss AS profitloss,
                b.forecast_fee AS ycCost,
                b.sumfee
                FROM som_drg_sco b
                ) z
                ON a.SETTLE_LIST_ID = z.SETTLE_LIST_ID
            </when>
            <when test="feeStas==1">
                INNER JOIN
                (
                SELECT
                a.rid_idt_codg,
                a.HOSPITAL_ID,
                a.medcas_codg,
                a.adm_time,
                a.dscg_time,
                a.is_in_group,
                a.drg_codg,
                b.MED_TYPE as dise_type ,
                b.sumfee as FB_INHOS_TOTAL_COST,
                b.dfr_fee as ycCost
                FROM som_drg_grp_fbck a
                LEFT JOIN som_drg_pt_val_pay b
                ON a.rid_idt_codg = b.rid_idt_codg
                AND a.HOSPITAL_ID = b.HOSPITAL_ID
                <where>
                    <if test="inStartTime!=null and inStartTime!='' and inEndTime!=null and inEndTime!=''">
                        AND a.adm_time BETWEEN #{inStartTime,jdbcType=VARCHAR} and CONCAT(#{inEndTime,jdbcType=VARCHAR},' 23:59:59')
                    </if>
                    <if test="begnDate!=null and begnDate!='' and expiDate!=null and expiDate!=''">
                        AND a.dscg_time BETWEEN #{begnDate,jdbcType=VARCHAR} and CONCAT(#{expiDate,jdbcType=VARCHAR},' 23:59:59')
                    </if>
                    <if test="seStartTime!=null and seStartTime!='' and seEndTime!=null and seEndTime!=''">
                        AND a.setl_time BETWEEN #{seStartTime,jdbcType=VARCHAR} and CONCAT(#{seStartTime,jdbcType=VARCHAR},' 23:59:59')
                    </if>
                </where>
--                 WHERE a.dscg_time between #{begnDate,jdbcType=VARCHAR} and concat(#{expiDate,jdbcType=VARCHAR},' 23:59:59')
                <include refid="com.my.som.common.mapper.CommonMapper.fbAuth" />
                ) z
                ON a.PATIENT_ID = z.medcas_codg
                AND SUBSTR(a.adm_time,1,10) = z.adm_time
                AND SUBSTR(a.dscg_time,1,10) = z.dscg_time
                AND o.HOSPITAL_ID = z.HOSPITAL_ID
            </when>
        </choose>
            <where>
                <if test="inStartTime!=null and inStartTime!='' and inEndTime!=null and inEndTime!=''">
                    AND a.adm_time BETWEEN #{inStartTime,jdbcType=VARCHAR} and CONCAT(#{inEndTime,jdbcType=VARCHAR},' 23:59:59')
                </if>
                <if test="begnDate!=null and begnDate!='' and expiDate!=null and expiDate!=''">
                    AND a.dscg_time BETWEEN #{begnDate,jdbcType=VARCHAR} and CONCAT(#{expiDate,jdbcType=VARCHAR},' 23:59:59')
                </if>
                <if test="seStartTime!=null and seStartTime!='' and seEndTime!=null and seEndTime!=''">
                    AND a.setl_end_time BETWEEN #{seStartTime,jdbcType=VARCHAR} and CONCAT(#{seEndTime,jdbcType=VARCHAR},' 23:59:59')
                </if>
            </where>
<!--        WHERE a.dscg_time between #{begnDate,jdbcType=VARCHAR} and concat(#{expiDate,jdbcType=VARCHAR},' 23:59:59') &lt;!&ndash; 开始时间，结束时间 &ndash;&gt;-->
<!--        <if test="inStartTime != null and inStartTime != '' and inEndTime != null and inEndTime != ''">-->
<!--            AND a.adm_time BETWEEN #{inStartTime} AND CONCAT(#{inEndTime},' 23:59:59')-->
<!--        </if>-->
        <include refid="com.my.som.mapper.newBusiness.common.NewBusinessCommonAnalysisMapper.queryBusCommon" />
        ) a
        LEFT JOIN som_drg_grp_rcd b
        ON a.SETTLE_LIST_ID = b.SETTLE_LIST_ID
        GROUP BY a.dscg_caty_codg_inhosp,a.hospitalId
        ) m
        LEFT JOIN som_dept n
        ON m.deptCode = n.`CODE`
               and m.hospitalId = n.HOSPITAL_ID
        AND n.ACTIVE_FLAG = '1'
    </select>
    <select id="queryCountData" resultType="com.my.som.vo.newBusiness.dept.NewBusinessDeptVo">
        select sum(a.medicalTotalNum) AS countMedicalTotal,sum(a.drgInGroupMedcasVal)  AS countInGroup, sum(a.nonGroupNum) as countNoGroup
        from
        (
        select a.dscg_caty_codg_inhosp as deptCode,
        a.HOSPITAL_ID AS hospitalId,
        IFNULL(count(1),0) as medicalTotalNum,
        <choose>
            <when test="feeStas==0">
                IFNULL(round(SUM(CASE WHEN a.grp_stas = '1' THEN a.drg_wt ELSE NULL END)/
                NULLIF (COUNT(CASE WHEN a.grp_stas = '1' THEN 1 ELSE NULL END),0),2),0)   AS cmi,
                ROUND(AVG(a.ipt_sumfee),2) AS avgFee,
            </when>
            <when test="feeStas==1">
                ROUND(AVG(z.sumfee),2) AS avgFee,
            </when>
        </choose>
        ROUND(AVG(a.act_ipt),2) AS avgInHosDays,
        ROUND(IFNULL(SUM(c.drg_wt),0),2) AS totalWeight,
        <choose>
            <when test="feeStas==0">
                IFNULL(count(DISTINCT(case when a.grp_stas = '1' then a.drg_codg else null end)),0) as groupNum,
                IFNULL(ROUND(NULLIF(sum(case when a.grp_stas = '1' then 1 else 0 end),0)/count(1) * 100,2),0) as inGroupRate,
                count(case when a.grp_stas = '1' then 1 else null end) as drgInGroupMedcasVal,
                count(case when a.grp_stas != '1' then 1 else null end) as nonGroupNum,
                IFNULL(ROUND(SUM(CASE WHEN a.grp_stas = '1' THEN a.act_ipt ELSE NULL END / NULLIF(convert(AES_DECRYPT(UNHEX(c.standard_ave_hosp_day),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) using utf8),0)) /
                NULLIF(COUNT(CASE WHEN a.grp_stas = '1' THEN 1 ELSE NULL END),0), 2),0)  AS timeIndex,
                IFNULL(ROUND(SUM(CASE WHEN a.grp_stas = '1' THEN a.ipt_sumfee ELSE NULL END / NULLIF(convert(AES_DECRYPT(UNHEX(c.standard_avg_fee),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) using utf8),0)) /
                NULLIF(COUNT(CASE WHEN a.grp_stas = '1' THEN 1 ELSE NULL END),0), 2),0)   AS costIndex,
                IFNULL(ROUND(SUM(a.drugfee)/NULLIF(SUM(a.ipt_sumfee),0)*100,2),0) AS medicalCostRate,
                IFNULL(ROUND(SUM(a.mcs_fee)/NULLIF(SUM(a.ipt_sumfee),0)*100,2),0) AS materialCostRate
            </when>
            <when test="feeStas==1">
                IFNULL(count(DISTINCT(case when z.grp_stas = '1' then z.drg_codg else null end)),0) as groupNum,
                IFNULL(ROUND(NULLIF(sum(case when z.grp_stas = '1' then 1 else 0 end),0)/count(1) * 100,2),0) as inGroupRate,
                count(case when z.grp_stas = '1' then 1 else null end) as drgInGroupMedcasVal,
                count(case when z.grp_stas = '0' then 1 else null end) as nonGroupNum,
                IFNULL(ROUND(SUM(CASE WHEN z.grp_stas = '1' THEN a.act_ipt ELSE NULL END / NULLIF(convert(AES_DECRYPT(UNHEX(c.standard_ave_hosp_day),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) using utf8),0)) /
                NULLIF(COUNT(CASE WHEN z.grp_stas = '1' THEN 1 ELSE NULL END),0), 2),0)  AS timeIndex,
                IFNULL(ROUND(SUM(CASE WHEN z.grp_stas = '1' THEN z.sumfee ELSE NULL END / NULLIF(convert(AES_DECRYPT(UNHEX(c.standard_avg_fee),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) using utf8),0)) /
                NULLIF(COUNT(CASE WHEN z.grp_stas = '1' THEN 1 ELSE NULL END),0), 2),0)   AS costIndex,
                IFNULL(ROUND(SUM(a.drugfee)/NULLIF(SUM(z.sumfee),0)*100,2),0) AS medicalCostRate,
                IFNULL(ROUND(SUM(a.mcs_fee)/NULLIF(SUM(z.sumfee),0)*100,2),0) AS materialCostRate
            </when>
        </choose>
        from som_drg_grp_info a
        <if test='enableSeAns != null and enableSeAns != "" and enableSeAns == "1"'>
            LEFT JOIN som_hi_invy_bas_info q
            ON q.ID = a.SETTLE_LIST_ID
            INNER JOIN som_setl_cas_crsp p
            ON q.k00 = p.k00
        </if>
        <if test="feeStas==1">
            INNER JOIN
            (
            SELECT
            a.rid_idt_codg,
            a.HOSPITAL_ID,
            a.medcas_codg,
            a.adm_time,
            a.dscg_time,
            a.is_in_group as grp_stas,
            a.drg_codg,
            a.DRG_NAME,
            b.MED_TYPE,
            b.sumfee,
            b.dfr_fee
            FROM som_drg_grp_fbck a
            LEFT JOIN som_drg_pt_val_pay b
            ON a.rid_idt_codg = b.rid_idt_codg
            AND a.HOSPITAL_ID = b.HOSPITAL_ID
            <where>
                <if test="inStartTime!=null and inStartTime!='' and
                inEndTime!=null and inEndTime!=''">
                    AND a.adm_time BETWEEN #{inStartTime,jdbcType=VARCHAR} and CONCAT(#{inEndTime,jdbcType=VARCHAR},' 23:59:59')
                </if>
                <if test="begnDate!=null and begnDate!='' and
            expiDate!=null and expiDate!=''">
                    AND a.dscg_time BETWEEN #{begnDate,jdbcType=VARCHAR} and CONCAT(#{expiDate,jdbcType=VARCHAR},' 23:59:59')
                </if>
                <if test="seStartTime!=null and seStartTime!='' and
            seEndTime!=null and seEndTime!=''">
                    AND a.setl_time BETWEEN #{seStartTime,jdbcType=VARCHAR} and CONCAT(#{seStartTime,jdbcType=VARCHAR},' 23:59:59')
                </if>
            </where>
            --             WHERE a.dscg_time between #{begnDate,jdbcType=VARCHAR} and concat(#{expiDate,jdbcType=VARCHAR},' 23:59:59')
            <include refid="com.my.som.common.mapper.CommonMapper.fbAuth" />
            ) z
            ON a.PATIENT_ID = z.medcas_codg
            AND SUBSTR(a.adm_time,1,10) = z.adm_time
            AND SUBSTR(a.dscg_time,1,10) = z.dscg_time
            AND a.HOSPITAL_ID = z.HOSPITAL_ID
        </if>
        <if test="feeStas==0">
            left join som_drg_grp_rcd b
            on a.SETTLE_LIST_ID = b.SETTLE_LIST_ID
        </if>
        left join som_drg_standard c
        on a.drg_codg = c.drg_codg
        and substr(a.dscg_time,1,4) =  c.STANDARD_YEAR
        AND a.HOSPITAL_ID = c.HOSPITAL_ID
        where a.dscg_caty_codg_inhosp IS NOT NULL
        AND a.dscg_caty_codg_inhosp != ''
        <!-- 传入条件-日期 -->
        <if test="inStartTime!=null and inStartTime!='' and
                inEndTime!=null and inEndTime!=''">
            AND a.adm_time BETWEEN #{inStartTime,jdbcType=VARCHAR} and CONCAT(#{inEndTime,jdbcType=VARCHAR},' 23:59:59')
        </if>
        <if test="begnDate!=null and begnDate!='' and
            expiDate!=null and expiDate!=''">
            AND a.dscg_time BETWEEN #{begnDate,jdbcType=VARCHAR} and CONCAT(#{expiDate,jdbcType=VARCHAR},' 23:59:59')
        </if>
        <if test="seStartTime!=null and seStartTime!='' and
            seEndTime!=null and seEndTime!=''">
            AND a.setl_end_time BETWEEN #{seStartTime,jdbcType=VARCHAR} and CONCAT(#{seEndTime,jdbcType=VARCHAR},' 23:59:59')
        </if>
        <!--        and a.dscg_time between #{begnDate,jdbcType=VARCHAR} and concat(#{expiDate,jdbcType=VARCHAR},' 23:59:59')-->
        <!--        <if test="inStartTime != null and inStartTime != '' and inEndTime != null and inEndTime != ''">-->
        <!--            AND a.adm_time BETWEEN #{inStartTime} AND CONCAT(#{inEndTime},' 23:59:59')-->
        <!--        </if>-->
        <!-- 通用查询条件 -->
        <include refid="com.my.som.mapper.newBusiness.common.NewBusinessCommonAnalysisMapper.queryBusCommon" />
        group by a.dscg_caty_codg_inhosp,a.HOSPITAL_ID
        ) a
        left join som_dept b
        on a.deptCode = b.CODE
        and a.hospitalId = b.HOSPITAL_ID
        ORDER BY inGroupRate DESC
    </select>
    <select id="queryYCCountData" resultType="com.my.som.vo.newBusiness.dept.NewBusinessDeptVo">
        SELECT sum(normalNum) AS normalNumCount,sum(ultrahighNum) AS ultrahighNumCount,sum(ultraLowNum) AS ultraLowNumCount
        FROM
        (
        SELECT a.dscg_caty_codg_inhosp AS deptCode,
        a.hospitalId,
        IFNULL(COUNT(1),0) AS medicalTotalNum,
        IFNULL(COUNT(CASE WHEN a.dise_type = 4 THEN 1 ELSE NULL END) - COUNT(CASE WHEN b.grp_stas = '0' THEN 1 ELSE NULL END),0) AS nonBenchmarkNum,
        IFNULL(COUNT(CASE WHEN a.dise_type = 3 THEN 1 ELSE NULL END),0) AS normalNum,
        IFNULL(COUNT(CASE WHEN a.dise_type = 1 THEN 1 ELSE NULL END),0) AS ultrahighNum,
        IFNULL(ROUND(COUNT(CASE WHEN a.dise_type = 1 THEN 1 ELSE NULL END) / COUNT(1) * 100,2),0) AS ultrahighRate,
        IFNULL(COUNT(CASE WHEN a.dise_type = 2 THEN 1 ELSE NULL END),0) AS ultraLowNum,
        IFNULL(ROUND(COUNT(CASE WHEN a.dise_type = 2 THEN 1 ELSE NULL END) / COUNT(1) * 100,2),0) AS ultraLowRate,
        IFNULL(ROUND(SUM(a.ipt_sumfee),2),0) AS sumfee,
        IFNULL(ROUND(SUM(a.ycCost),2),0) AS forecastAmount,
        <choose>
            <when test="feeStas==0">
                IFNULL(ROUND(IFNULL(SUM(a.profitloss),0),2),0) AS forecastAmountDiff,
                IFNULL(ROUND(SUM(a.ipt_sumfee) / SUM(a.ycCost),2),0) AS oeVal
            </when>
            <when test="feeStas==1">
                IFNULL(ROUND(SUM(a.FB_INHOS_TOTAL_COST),2),0) AS fbTotalCost,
                IFNULL(ROUND(IFNULL(SUM(a.ycCost),0) - IFNULL(SUM(a.FB_INHOS_TOTAL_COST),0),2),0) AS forecastAmountDiff,
                IFNULL(ROUND(SUM(a.FB_INHOS_TOTAL_COST) / SUM(a.ycCost),2),0) AS oeVal
            </when>
        </choose>
        FROM
        (
        SELECT a.dscg_caty_codg_inhosp,
        a.SETTLE_LIST_ID,
        a.dscg_time,
        a.ipt_sumfee,
        a.HOSPITAL_ID AS hospitalId,
        <if test="feeStas==1">
            z.FB_INHOS_TOTAL_COST,
        </if>
        a.drugfee,
        a.mcs_fee,
        z.dise_type,
        z.ycCost,
        z.profitloss
        FROM som_drg_grp_info a
        <if test='enableSeAns != null and enableSeAns != "" and enableSeAns == "1"'>
            LEFT JOIN som_hi_invy_bas_info q
            ON q.ID = a.SETTLE_LIST_ID
            INNER JOIN som_setl_cas_crsp p
            ON q.k00 = p.k00
        </if>
        <choose>
            <when test="feeStas==0">
                LEFT JOIN
                (
                SELECT b.SETTLE_LIST_ID,
                b.dise_type,

                b.profitloss AS profitloss,
                b.forecast_fee AS ycCost
                FROM som_drg_sco b

                ) z
                ON a.SETTLE_LIST_ID = z.SETTLE_LIST_ID
            </when>
            <when test="feeStas==1">
                INNER JOIN
                (
                SELECT
                a.rid_idt_codg,
                a.HOSPITAL_ID,
                a.medcas_codg,
                a.adm_time,
                a.dscg_time,
                a.is_in_group,
                a.drg_codg,
                b.MED_TYPE as dise_type ,
                b.sumfee as FB_INHOS_TOTAL_COST,
                b.dfr_fee as ycCost
                FROM som_drg_grp_fbck a
                LEFT JOIN som_drg_pt_val_pay b
                ON a.rid_idt_codg = b.rid_idt_codg
                AND a.HOSPITAL_ID = b.HOSPITAL_ID
                <where>
                    <if test="inStartTime!=null and inStartTime!='' and inEndTime!=null and inEndTime!=''">
                        AND a.adm_time BETWEEN #{inStartTime,jdbcType=VARCHAR} and CONCAT(#{inEndTime,jdbcType=VARCHAR},' 23:59:59')
                    </if>
                    <if test="begnDate!=null and begnDate!='' and expiDate!=null and expiDate!=''">
                        AND a.dscg_time BETWEEN #{begnDate,jdbcType=VARCHAR} and CONCAT(#{expiDate,jdbcType=VARCHAR},' 23:59:59')
                    </if>
                    <if test="seStartTime!=null and seStartTime!='' and seEndTime!=null and seEndTime!=''">
                        AND a.setl_time BETWEEN #{seStartTime,jdbcType=VARCHAR} and CONCAT(#{seStartTime,jdbcType=VARCHAR},' 23:59:59')
                    </if>
                </where>
                --                 WHERE a.dscg_time between #{begnDate,jdbcType=VARCHAR} and concat(#{expiDate,jdbcType=VARCHAR},' 23:59:59')
                <include refid="com.my.som.common.mapper.CommonMapper.fbAuth" />
                ) z
                ON a.PATIENT_ID = z.medcas_codg
                AND SUBSTR(a.adm_time,1,10) = z.adm_time
                AND SUBSTR(a.dscg_time,1,10) = z.dscg_time
                AND o.HOSPITAL_ID = z.HOSPITAL_ID
            </when>
        </choose>
        <where>
            <if test="inStartTime!=null and inStartTime!='' and inEndTime!=null and inEndTime!=''">
                AND a.adm_time BETWEEN #{inStartTime,jdbcType=VARCHAR} and CONCAT(#{inEndTime,jdbcType=VARCHAR},' 23:59:59')
            </if>
            <if test="begnDate!=null and begnDate!='' and expiDate!=null and expiDate!=''">
                AND a.dscg_time BETWEEN #{begnDate,jdbcType=VARCHAR} and CONCAT(#{expiDate,jdbcType=VARCHAR},' 23:59:59')
            </if>
            <if test="seStartTime!=null and seStartTime!='' and seEndTime!=null and seEndTime!=''">
                AND a.setl_end_time BETWEEN #{seStartTime,jdbcType=VARCHAR} and CONCAT(#{seEndTime,jdbcType=VARCHAR},' 23:59:59')
            </if>
        </where>
        <!--        WHERE a.dscg_time between #{begnDate,jdbcType=VARCHAR} and concat(#{expiDate,jdbcType=VARCHAR},' 23:59:59') &lt;!&ndash; 开始时间，结束时间 &ndash;&gt;-->
        <!--        <if test="inStartTime != null and inStartTime != '' and inEndTime != null and inEndTime != ''">-->
        <!--            AND a.adm_time BETWEEN #{inStartTime} AND CONCAT(#{inEndTime},' 23:59:59')-->
        <!--        </if>-->
        <include refid="com.my.som.mapper.newBusiness.common.NewBusinessCommonAnalysisMapper.queryBusCommon" />
        ) a
        LEFT JOIN som_drg_grp_rcd b
        ON a.SETTLE_LIST_ID = b.SETTLE_LIST_ID
        GROUP BY a.dscg_caty_codg_inhosp,a.hospitalId
        ) m
        LEFT JOIN som_dept n
        ON m.deptCode = n.`CODE`
        and m.hospitalId = n.HOSPITAL_ID
        AND n.ACTIVE_FLAG = '1'
    </select>
<!--    drg病组分析象限图-->
    <select id="selectDrgSickGroupQuadrant" resultType="com.my.som.vo.newBusiness.dept.NewBusinessDeptVo">
        SELECT
        a.drgCodg AS drgCodg,
        a.drgName AS drgName,
        count(*) AS medicalTotalNum,
        IFNULL( sum( a.DrgWt ), 0 ) AS totalWeight,
        IFNULL( round( sum( a.DrgWt )/ count( CASE WHEN a.isInGroup = 1 THEN 1 ELSE NULL END ), 2 ), 0 ) AS cmi,
        IFNULL( round( sum( a.sumfee ), 2 ), 0 ) AS sumfee,
        IFNULL(round(sum(a.ycCost ), 2),0) AS forecastAmount,
        IFNULL(round(sum(profitloss),2),0) AS forecastAmountDiff
        FROM
        (
        SELECT
        a.A11,
        b.drg_codg AS drgCodg,
        b.DRG_NAME AS drgName,
        b.drg_wt AS DrgWt,
        b.grp_stas AS isInGroup,
        a.D01 AS sumfee,
        d.totl_sco AS totlSco,
        a.A46C AS medPayWay,
        d.forecast_fee AS ycCost,
        d.profitloss AS profitloss,
        d.price AS price
        FROM
        som_hi_invy_bas_info a
        LEFT JOIN som_drg_grp_info b ON a.ID = b.SETTLE_LIST_ID
        LEFT JOIN som_dept c ON a.B16C = c.`CODE`
        LEFT JOIN som_drg_sco d ON a.ID = d.SETTLE_LIST_ID

        where 1=1
        <if test="seStartTime != null and seStartTime != '' and seEndTime != null and seEndTime !=''">
            AND a.D37 BETWEEN #{seStartTime,jdbcType=VARCHAR} AND CONCAT(#{seEndTime,jdbcType=VARCHAR},' 23:59:59')
        </if>
        <if test="begnDate != null and begnDate != '' and expiDate != null and expiDate != ''">
            AND a.B15 BETWEEN #{begnDate,jdbcType=VARCHAR} AND CONCAT(#{expiDate,jdbcType=VARCHAR},' 23:59:59')
        </if>
        <if test="hospitalId != null and hospitalId != ''">
            AND a.A01 = #{hospitalId,jdbcType=VARCHAR}
        </if>
        <if test="deptCode != null and deptCode != ''">
            AND a.B16C = #{deptCode,jdbcType=VARCHAR}
        </if>
        <if test="inStartTime != null and inStartTime != '' and inEndTime != null and inEndTime != ''">
            AND a.B12 BETWEEN #{inStartTime,jdbcType=VARCHAR} AND CONCAT(#{inEndTime,jdbcType=VARCHAR},' 23:59:59')
        </if>
        ) a
        GROUP BY
        a.drgCodg,a.drgName
    </select>
</mapper>
