<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.som.mapper.newBusiness.ilness.NewDrgBusinessIlnessAnalysisMapper">

    <select id="queryDrgIlnessKpiData" resultType="com.my.som.vo.newBusiness.disease.NewBusinessDiseaseVo">
        SELECT
        COUNT(1) AS drgInGroupMedcasVal,
        a.main_diag_dise_codg AS icdCodg,
        a.main_diag_dise_name AS icdName,
        ROUND(AVG(IFNULL(a.act_ipt,0)),2) AS avgInHosDays,
        ROUND(MAX(IFNULL(AES_DECRYPT(UNHEX(c.standard_avg_fee),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}),0)),2) AS standardInHosCost,
        ROUND(MAX(IFNULL(AES_DECRYPT(UNHEX(c.standard_ave_hosp_day),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}),0)),2) AS standardInHosDays,
        IFNULL(ROUND(AVG(IFNULL(a.act_ipt,0))/NULLIF(AVG(IFNULL(AES_DECRYPT(UNHEX(c.standard_ave_hosp_day),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}),0)),0),2),0) AS timeIndex,
        ROUND(AVG(IFNULL(a.ipt_sumfee,0)),2) AS avgInHosCost,
        <!--MAX(IFNULL(AES_DECRYPT(UNHEX(c.drug_ratio),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}),0)) AS standardDrugRatio,-->
        <!--MAX(IFNULL(AES_DECRYPT(UNHEX(c.consum_ratio),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}),0)) AS standardConsumeRatio,-->
        ROUND(IFNULL(SUM(IFNULL(a.drugfee,0)),0),2) AS drugFee,
        ROUND(IFNULL(SUM(IFNULL(a.mcs_fee,0)),0),2) AS consumeFee,
        <choose>
            <when test="feeStas == 0">
               <!--
                a.drg_codg AS drgCodg,
                a.DRG_NAME AS drgName, -->
                IFNULL(ROUND(AVG(IFNULL(a.ipt_sumfee,0))/NULLIF(AVG(IFNULL(AES_DECRYPT(UNHEX(c.standard_avg_fee),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}),0)),0),2),0) AS costIndex,
                ROUND(IFNULL(SUM(IFNULL(a.drugfee,0))/NULLIF(SUM(IFNULL(a.ipt_sumfee,0)),0),0) * 100,2) AS drugRatio,
                ROUND(IFNULL(SUM(IFNULL(a.mcs_fee,0))/NULLIF(SUM(IFNULL(a.ipt_sumfee,0)),0),0) * 100,2) AS consumeRatio,
                ROUND( IFNULL(  SUM( IFNULL( a.ipt_sumfee, 0 )), 0 ), 2 ) AS sumfee,

                </when>
            <when test="feeStas == 1">
                <!--
                e.drg_codg AS drgCodg,
                e.DRG_NAME AS drgName,
                 -->
                ROUND(AVG(IFNULL(e.ipt_sumfee,0)),2) AS fbAvgInHosCost,
                IFNULL(ROUND(AVG(IFNULL(e.ipt_sumfee,0))/NULLIF(AVG(IFNULL(AES_DECRYPT(UNHEX(c.standard_avg_fee),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}),0)),0),2),0) AS costIndex,
                ROUND(IFNULL(SUM(IFNULL(a.drugfee,0))/NULLIF(SUM(IFNULL(e.ipt_sumfee,0)),0),0) * 100,2) AS drugRatio,
                ROUND(IFNULL(SUM(IFNULL(a.mcs_fee,0))/NULLIF(SUM(IFNULL(e.ipt_sumfee,0)),0),0) * 100,2) AS consumeRatio,
                ROUND( IFNULL(  SUM( IFNULL( e.ipt_sumfee, 0 )), 0 ), 2 ) AS sumfee,
            </when>
        </choose>

        ROUND(AVG(IFNULL(IFNULL(D11,0)+IFNULL(D12,0)+IFNULL(D13,0)+IFNULL(D14,0),0)),2) AS com_med_servfee, <!-- 综合医疗服务费 -->
        ROUND(AVG(IFNULL(D11,0)),2) AS ordn_med_servfee, <!-- 一般医疗服务费 -->
        ROUND(AVG(IFNULL(D12,0)),2) AS ordn_trt_oprt_fee, <!-- 一般治疗操作费 -->
        ROUND(AVG(IFNULL(D13,0)),2) AS nursfee, <!-- 护理费 -->
        ROUND(AVG(IFNULL(D14,0)),2) AS zhylfwqtf, <!-- 综合医疗服务其他费 -->
        ROUND(AVG(IFNULL(IFNULL(D15,0)+IFNULL(D16,0)+IFNULL(D17,0)+IFNULL(D18,0),0)),2) AS diag_fee, <!-- 诊断费 -->
        ROUND(AVG(IFNULL(D15,0)),2) AS cas_diag_fee, <!-- 病理诊断费 -->
        ROUND(AVG(IFNULL(D16,0)),2) AS lab_diag_fee, <!-- 实验室诊断费 -->
        ROUND(AVG(IFNULL(D17,0)),2) AS rdhy_diag_fee, <!-- 影像学诊断费 -->
        ROUND(AVG(IFNULL(D18,0)),2) AS clnc_diag_item_fee, <!-- 临床诊断项目费 -->
        ROUND(AVG(IFNULL(IFNULL(D19,0)+IFNULL(D20,0),0)),2) AS treat_fee, <!-- 治疗费 -->
        ROUND(AVG(IFNULL(D19,0)),2) AS nsrgtrt_item_fee, <!-- 非手术治疗项目费 -->
        ROUND(AVG(IFNULL(D20,0)),2) AS oprn_treat_fee, <!-- 手术治疗费 -->
        ROUND(AVG(IFNULL(D21,0)),2) AS rhab_fee, <!-- 康复费 -->
        ROUND(AVG(IFNULL(D22,0)),2) AS tcmdrug_fee, <!-- 中医费 -->
        ROUND(AVG(IFNULL(D23,0)),2) AS west_fee, <!-- 西药费 -->
        ROUND(AVG(IFNULL(IFNULL(D24,0)+IFNULL(D25,0),0)),2) AS zyf1, <!-- 中药费 -->
        ROUND(AVG(IFNULL(D24,0)),2) AS tcmpat_fee, <!-- 中成药费 -->
        ROUND(AVG(IFNULL(D25,0)),2) AS tcmherb, <!-- 中草药费-->
        ROUND(AVG(IFNULL(IFNULL(D26,0)+IFNULL(D27,0)+IFNULL(D28,0)+IFNULL(D29,0)+IFNULL(D30,0),0)),2) AS blood_blo_pro_fee, <!-- 血液和血液制品费 -->
        ROUND(AVG(IFNULL(D26,0)),2) AS blo_fee, <!-- 血费 -->
        ROUND(AVG(IFNULL(D27,0)),2) AS bdblzpf, <!-- 白蛋白类制品费 -->
        ROUND(AVG(IFNULL(D28,0)),2) AS qdblzpf, <!-- 球蛋白类制品费 -->
        ROUND(AVG(IFNULL(D29,0)),2) AS nxyzlzpf, <!-- 凝血因子类制品费 -->
        ROUND(AVG(IFNULL(D30,0)),2) AS xbyzlzpf, <!-- 细胞因子类制品费 -->
        ROUND(AVG(IFNULL(IFNULL(D31,0)+IFNULL(D32,0)+IFNULL(D33,0),0)),2) AS mcs_fee, <!-- 耗材费 -->
        ROUND(AVG(IFNULL(D31,0)),2) AS jcyycxyyclf, <!-- 检查用一次性医用材料费 -->
        ROUND(AVG(IFNULL(D32,0)),2) AS trt_use_dspo_med_matlfee, <!-- 治疗用一次性医用材料费 -->
        ROUND(AVG(IFNULL(D33,0)),2) AS oprn_use_dspo_med_matlfee, <!-- 手术用一次性医用材料费 -->
        ROUND(AVG(IFNULL(D34,0)),2) AS oth_fee, <!-- 其他费 -->
        MAX(IFNULL(d.com_med_servfee,0)) AS bgzhylfwf, <!-- 标杆综合医疗服务费 -->
        MAX(IFNULL(d.ordn_med_servfee,0)) AS bgybylfwf, <!-- 标杆一般医疗服务费 -->
        MAX(IFNULL(d.ordn_trt_oprt_fee,0)) AS bgybzlczf, <!-- 标杆一般治疗操作费 -->
        MAX(IFNULL(d.nursfee,0)) AS bghlf, <!-- 标杆护理费 -->
        MAX(IFNULL(d.oth_fee_com,0)) AS bgzhylfwqtf, <!-- 标杆综合医疗服务其他费 -->
        MAX(IFNULL(d.diag_fee,0)) AS bgzdf, <!-- 标杆诊断费 -->
        MAX(IFNULL(d.cas_diag_fee,0)) AS bgblzdf, <!-- 标杆病理诊断费 -->
        MAX(IFNULL(d.lab_diag_fee,0)) AS bgsyszdf, <!-- 标杆实验室诊断费 -->
        MAX(IFNULL(d.rdhy_diag_fee,0)) AS bgyxxzdf, <!-- 标杆影像学诊断费 -->
        MAX(IFNULL(d.clnc_diag_item_fee,0)) AS bglczdxmf, <!-- 标杆临床诊断项目费 -->
        MAX(IFNULL(d.treat_fee,0)) AS bgzlf, <!-- 标杆治疗费 -->
        MAX(IFNULL(d.nsrgtrt_item_fee,0)) AS bgfsszlxmf, <!-- 标杆非手术治疗项目费 -->
        MAX(IFNULL(d.oprn_treat_fee,0)) AS bgsszlf, <!-- 标杆手术治疗费 -->
        MAX(IFNULL(d.rhab_fee,0)) AS bgkff, <!-- 标杆康复费 -->
        MAX(IFNULL(d.tcm_treat_fee,0)) AS bgzyf, <!-- 标杆中医费 -->
        MAX(IFNULL(d.west_fee,0)) AS bgxyf, <!-- 标杆西药费 -->
        MAX(IFNULL(d.tcmdrug_fee,0)) AS bgzyf1, <!-- 标杆中药费 -->
        MAX(IFNULL(d.tcmpat_fee,0)) AS bgzcyf, <!-- 标杆中成药费 -->
        MAX(IFNULL(d.tcmherb,0)) AS bgzcyf1, <!-- 标杆中草药费 -->
        MAX(IFNULL(d.blood_blo_pro_fee,0)) AS bgxyhxyzpf, <!-- 标杆血液和血液制品费 -->
        MAX(IFNULL(d.blo_fee,0)) AS bgxf, <!-- 标杆血费 -->
        MAX(IFNULL(d.albu_clss_prod_fee,0)) AS bgbdblzpf, <!-- 标杆白蛋白类制品费 -->
        MAX(IFNULL(d.glon_clss_prod_fee,0)) AS bgqdblzpf, <!-- 标杆球蛋白类制品费 -->
        MAX(IFNULL(d.clotfac_clss_prod_fee,0)) AS bgnxyzlzpf, <!-- 标杆凝血因子类制品费 -->
        MAX(IFNULL(d.cyki_clss_prod_fee,0)) AS bgxbyzlzpf, <!-- 标杆细胞因子类制品费 -->
        MAX(IFNULL(d.mcs_fee,0)) AS bghcf, <!-- 标杆耗材费 -->
        MAX(IFNULL(d.exam_use_dspo_med_matlfee,0)) AS bgjcyycxyyclf, <!-- 标杆检查用一次性医用材料费 -->
        MAX(IFNULL(d.trt_use_dspo_med_matlfee,0)) AS bgzlyycxyyclf, <!-- 标杆治疗用一次性医用材料费 -->
        MAX(IFNULL(d.oprn_use_dspo_med_matlfee,0)) AS bgssyycxyyclf, <!-- 标杆手术用一次性医用材料费 -->
        MAX(IFNULL(d.oth_fee,0)) AS bgqtf <!-- 标杆其他费 -->
        FROM som_drg_grp_info a
        LEFT JOIN som_hi_invy_bas_info b
        ON a.SETTLE_LIST_ID = b.ID
        <if test='enableSeAns != null and enableSeAns != "" and enableSeAns == "1"'>
            INNER JOIN som_setl_cas_crsp p
            ON b.k00 = p.k00
        </if>
        AND a.HOSPITAL_ID = b.HOSPITAL_ID
        LEFT JOIN som_drg_standard c
        ON a.drg_codg = c.drg_codg
        AND SUBSTR(ifnull(b.d37,a.dscg_time),1,4) = c.STANDARD_YEAR
        AND a.HOSPITAL_ID = c.HOSPITAL_ID
        and b.insuplc_admdvs = c.insuplc_admdvs
        LEFT JOIN som_std_fee d
        ON a.drg_codg = d.`CODE`
        AND SUBSTR(a.dscg_time,1,4) = d.STANDARD_YEAR
        AND d.TYPE = '3'

        <choose>
            <when test="feeStas == 1">
                INNER JOIN
                (
                SELECT
                a.medcas_codg,
                a.HOSPITAL_ID,
                a.adm_time,
                a.drg_codg,
                a.DRG_NAME,
                a.dscg_time,
                b.MED_TYPE,
                b.setl_pt_val,
                b.sumfee AS ipt_sumfee,
                b.dfr_fee AS ycCost
                FROM som_drg_grp_fbck a
                LEFT JOIN som_drg_pt_val_pay b
                ON a.rid_idt_codg = b.rid_idt_codg
                AND a.HOSPITAL_ID = b.HOSPITAL_ID
                <!--<![CDATA[
                    WHERE a.dscg_time >= #{begnDate} AND a.dscg_time <= #{expiDate}
                    ]]>-->
                <where>
                    <if test="inStartTime!=null and inStartTime!='' and inEndTime!=null and inEndTime!=''">
                        AND a.adm_time BETWEEN #{inStartTime,jdbcType=VARCHAR} and CONCAT(#{inEndTime,jdbcType=VARCHAR},' 23:59:59')
                    </if>
                    <if test="begnDate!=null and begnDate!='' and expiDate!=null and expiDate!=''">
                        AND a.dscg_time BETWEEN #{begnDate,jdbcType=VARCHAR} and CONCAT(#{expiDate,jdbcType=VARCHAR},' 23:59:59')
                    </if>
                    <if test="seStartTime!=null and seStartTime!='' and seEndTime!=null and seEndTime!=''">
                        AND a.setl_time BETWEEN #{seStartTime,jdbcType=VARCHAR} and CONCAT(#{seStartTime,jdbcType=VARCHAR},' 23:59:59')
                    </if>
                </where>
                <include refid="com.my.som.common.mapper.CommonMapper.fbAuth" />
                ) e
                ON a.PATIENT_ID = e.medcas_codg
                AND SUBSTR(a.adm_time,1,10) = e.adm_time
                AND SUBSTR(a.dscg_time,1,10) = e.dscg_time
                AND a.HOSPITAL_ID = e.HOSPITAL_ID
            </when>
        </choose>
        WHERE a.grp_stas = 1
        <!--
            AND a.dscg_time BETWEEN #{begnDate,jdbcType=VARCHAR} AND CONCAT(#{expiDate,jdbcType=VARCHAR},' 23:59:59')
         -->
        <if test="inStartTime!=null and inStartTime!='' and inEndTime!=null and inEndTime!=''">
            AND a.adm_time BETWEEN #{inStartTime,jdbcType=VARCHAR} and CONCAT(#{inEndTime,jdbcType=VARCHAR},' 23:59:59')
        </if>
        <if test="begnDate!=null and begnDate!='' and expiDate!=null and expiDate!=''">
            AND a.dscg_time BETWEEN #{begnDate,jdbcType=VARCHAR} and CONCAT(#{expiDate,jdbcType=VARCHAR},' 23:59:59')
        </if>
        <if test="seStartTime!=null and seStartTime!='' and seEndTime!=null and seEndTime!=''">
            AND a.setl_end_time BETWEEN #{seStartTime,jdbcType=VARCHAR} and CONCAT(#{seEndTime,jdbcType=VARCHAR},' 23:59:59')
        </if>
        <if test="drgCodg != null and drgCodg != ''">
            AND a.drg_codg = #{drgCodg,jdbcType=VARCHAR}
        </if>
        <if test="icdCodg != null and icdCodg != ''">
            AND a.main_diag_dise_codg = #{icdCodg,jdbcType=VARCHAR}
        </if>
        <include refid="com.my.som.mapper.newBusiness.common.NewBusinessCommonAnalysisMapper.queryBusCommon" />
        <choose>
            <when test="feeStas == 0">
                GROUP BY a.main_diag_dise_codg,a.main_diag_dise_name
            </when>
            <when test="feeStas == 1">
                <!--
                GROUP BY e.drg_codg,e.DRG_NAME
                -->
            </when>
        </choose>
    </select>
    <select id="queryDrgDiseaseForecastData" resultType="com.my.som.vo.newBusiness.disease.NewBusinessDiseaseVo">
        SELECT a.* FROM ( SELECT
        a.main_diag_dise_codg AS icdCodg,
        a.main_diag_dise_name AS icdName,
        count(1) AS drgInGroupMedcasVal,
        IFNULL(ROUND(SUM(a.ipt_sumfee),2),0) AS sumfee,
        IFNULL(ROUND(SUM(a.ycCost),2),0) AS forecastAmount,
        ROUND( IFNULL( SUM( a.profitloss ), 0 ) , 2 )  AS forecastAmountDiff,
        IFNULL(ROUND(IFNULL(SUM(a.ipt_sumfee),0) / NULLIF(SUM(a.ycCost),0),2),0) AS oeVal
        FROM
        (
        SELECT a.drg_codg,
        a.DRG_NAME,
        a.dscg_caty_codg_inhosp,
        a.ipdr_code,
        a.ipdr_name,
        a.`NAME`,
        a.PATIENT_ID,
        a.SETTLE_LIST_ID,
        a.ipt_sumfee,
        a.drugfee,
        a.mcs_fee,
        b.dise_type,
        b.ycCost,
        b.profitloss,
        a.main_diag_dise_codg,
        a.main_diag_dise_name
        FROM som_drg_grp_info a
        <if test='enableSeAns != null and enableSeAns != "" and enableSeAns == "1"'>
            LEFT JOIN som_hi_invy_bas_info q
            ON q.ID = a.SETTLE_LIST_ID
            INNER JOIN som_setl_cas_crsp p
            ON q.k00 = p.k00
        </if>
        LEFT JOIN
        (
        SELECT a.SETTLE_LIST_ID,
        a.dise_type,
        a.forecast_fee AS ycCost,
        a.profitloss AS profitloss
        <!--       ,# 总费用 a.sumfee -->
        FROM som_drg_sco a
        ) b
        ON a.SETTLE_LIST_ID = b.SETTLE_LIST_ID
        WHERE 1 = 1
        AND a.grp_stas = 1
        <!--
            a.dscg_time BETWEEN #{begnDate} AND CONCAT(#{expiDate},' 23:59:59')
         -->
        <if test="inStartTime!=null and inStartTime!='' and inEndTime!=null and inEndTime!=''">
            AND a.adm_time BETWEEN #{inStartTime,jdbcType=VARCHAR} and CONCAT(#{inEndTime,jdbcType=VARCHAR},' 23:59:59')
        </if>
        <if test="begnDate!=null and begnDate!='' and expiDate!=null and expiDate!=''">
            AND a.dscg_time BETWEEN #{begnDate,jdbcType=VARCHAR} and CONCAT(#{expiDate,jdbcType=VARCHAR},' 23:59:59')
        </if>
        <if test="seStartTime!=null and seStartTime!='' and seEndTime!=null and seEndTime!=''">
            AND a.setl_end_time BETWEEN #{seStartTime,jdbcType=VARCHAR} and CONCAT(#{seEndTime,jdbcType=VARCHAR},' 23:59:59')
        </if>
        <if test="icdCodg != null and icdCodg != ''">
            AND a.main_diag_dise_codg = #{icdCodg,jdbcType=VARCHAR}
        </if>
        AND a.drg_codg IS NOT NULL
        <include refid="com.my.som.mapper.newBusiness.common.NewBusinessCommonAnalysisMapper.queryBusCommon" />
       ) a
        LEFT JOIN som_drg_grp_rcd b
        ON a.SETTLE_LIST_ID = b.SETTLE_LIST_ID
        <if test="drgCodg != null and drgCodg != ''">
            WHERE a.drg_codg = #{drgCodg,jdbcType=VARCHAR}
        </if>
        GROUP BY a.main_diag_dise_codg,a.main_diag_dise_name) a
    </select>
</mapper>
