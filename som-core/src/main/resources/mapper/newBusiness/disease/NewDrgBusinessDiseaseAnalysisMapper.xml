<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.som.mapper.newBusiness.disease.NewDrgBusinessDiseaseAnalysisMapper">
    <!-- 查询病组Kpi -->
    <select id="queryDrgDiseaseKpiData" resultType="com.my.som.vo.newBusiness.disease.NewBusinessDiseaseVo">
        SELECT
               COUNT(1) AS drgInGroupMedcasVal,
               ROUND(AVG(IFNULL(a.act_ipt,0)),2) AS avgInHosDays,
               MAX(IFNULL(AES_DECRYPT(UNHEX(c.standard_ave_hosp_day),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}),0)) AS standardInHosDays,
               IFNULL(ROUND(AVG(IFNULL(a.act_ipt,0))/NULLIF(AVG(IFNULL(AES_DECRYPT(UNHEX(c.standard_ave_hosp_day),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}),0)),0),2),0) AS timeIndex,
               ROUND(AVG(IFNULL(a.ipt_sumfee,0)),2) AS avgInHosCost,
        ROUND(IFNULL(SUM(IFNULL(a.drugfee,0)),0) ,2) AS drugFee,
        ROUND(IFNULL(SUM(IFNULL(a.mcs_fee,0)),0) ,2) AS consumeFee,

                   <choose>
                       <when test="feeStas == 0">
                           a.drg_codg AS drgCodg,
                           a.DRG_NAME AS drgName,
                           <choose>
                               <when test="feeMulAdjmConf != null and feeMulAdjmConf != '' and feeMulAdjmConf == 'true' ">
                               case when
                               b.a54 in ('1','01','310') then '职工'
                               else '居民' end as categories,
                                   case when
                                   LTRIM(RTRIM(b.insuplc_admdvs)) = #{insuplcAdmdvs}   THEN '市医保'
                                   else '省本级'
                               END as cbd,
                               case when
                               b.a54 in ('1','01','310') then ROUND( MAX( IFNULL( AES_DECRYPT( UNHEX( c.standard_avg_fee ), ${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY} ), 0 ))* c.adjm_cof, 2 )
                               else ROUND( MAX( IFNULL( AES_DECRYPT( UNHEX( c.standard_avg_fee ), ${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY} ), 0 ))* c.adjm_resid_cof, 2 )
                               end as standardInHosCost,
                               </when>
                           <otherwise>
                               ROUND(MAX(IFNULL(AES_DECRYPT(UNHEX(c.standard_avg_fee),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}),0)),2) AS standardInHosCost,
                           </otherwise>
                           </choose>
                            IFNULL(ROUND(AVG(IFNULL(a.ipt_sumfee,0))/NULLIF(AVG(IFNULL(AES_DECRYPT(UNHEX(c.standard_avg_fee),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}),0)),0),2),0) AS costIndex,
                           ROUND(IFNULL(SUM(IFNULL(a.drugfee,0))/NULLIF(SUM(IFNULL(a.ipt_sumfee,0)),0),0) * 100,2) AS drugRatio,
                           ROUND(IFNULL(SUM(IFNULL(a.mcs_fee,0))/NULLIF(SUM(IFNULL(a.ipt_sumfee,0)),0),0) * 100,2) AS consumeRatio,
                           ROUND( IFNULL(  SUM( IFNULL( a.ipt_sumfee, 0 )), 0 ), 2 ) AS sumfee,
                       </when>
                       <when test="feeStas == 1">
                           e.drg_codg AS drgCodg,
                           e.DRG_NAME AS drgName,
                           ROUND(AVG(IFNULL(e.ipt_sumfee,0)),2) AS fbAvgInHosCost,
                           ROUND(MAX(IFNULL(AES_DECRYPT(UNHEX(c.standard_avg_fee),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}),0)),2) AS standardInHosCost,
                           IFNULL(ROUND(AVG(IFNULL(e.ipt_sumfee,0))/NULLIF(AVG(IFNULL(AES_DECRYPT(UNHEX(c.standard_avg_fee),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}),0)),0),2),0) AS costIndex,
                           ROUND(IFNULL(SUM(IFNULL(a.drugfee,0))/NULLIF(SUM(IFNULL(e.ipt_sumfee,0)),0),0) * 100,2) AS drugRatio,
                           ROUND(IFNULL(SUM(IFNULL(a.mcs_fee,0))/NULLIF(SUM(IFNULL(e.ipt_sumfee,0)),0),0) * 100,2) AS consumeRatio,
                           ROUND( IFNULL(  SUM( IFNULL( e.ipt_sumfee, 0 )), 0 ), 2 ) AS sumfee,
                       </when>
                   </choose>
               ROUND(IFNULL(SUM(c.drg_wt),0),2) AS totalWeight,
               ROUND(IFNULL(AVG(c.drg_wt),0),2) AS avgWeight,
               ROUND(AVG(IFNULL(IFNULL(D11,0)+IFNULL(D12,0)+IFNULL(D13,0)+IFNULL(D14,0),0)),2) AS com_med_servfee, <!-- 综合医疗服务费 -->
               ROUND(AVG(IFNULL(D11,0)),2) AS ordn_med_servfee, <!-- 一般医疗服务费 -->
               ROUND(AVG(IFNULL(D12,0)),2) AS ordn_trt_oprt_fee, <!-- 一般治疗操作费 -->
               ROUND(AVG(IFNULL(D13,0)),2) AS nursfee, <!-- 护理费 -->
               ROUND(AVG(IFNULL(D14,0)),2) AS zhylfwqtf, <!-- 综合医疗服务其他费 -->
               ROUND(AVG(IFNULL(IFNULL(D15,0)+IFNULL(D16,0)+IFNULL(D17,0)+IFNULL(D18,0),0)),2) AS diag_fee, <!-- 诊断费 -->
               ROUND(AVG(IFNULL(D15,0)),2) AS cas_diag_fee, <!-- 病理诊断费 -->
               ROUND(AVG(IFNULL(D16,0)),2) AS lab_diag_fee, <!-- 实验室诊断费 -->
               ROUND(AVG(IFNULL(D17,0)),2) AS rdhy_diag_fee, <!-- 影像学诊断费 -->
               ROUND(AVG(IFNULL(D18,0)),2) AS clnc_diag_item_fee, <!-- 临床诊断项目费 -->
               ROUND(AVG(IFNULL(IFNULL(D19,0)+IFNULL(D20,0),0)),2) AS treat_fee, <!-- 治疗费 -->
               ROUND(AVG(IFNULL(D19,0)),2) AS nsrgtrt_item_fee, <!-- 非手术治疗项目费 -->
               ROUND(AVG(IFNULL(D20,0)),2) AS oprn_treat_fee, <!-- 手术治疗费 -->
               ROUND(AVG(IFNULL(D21,0)),2) AS rhab_fee, <!-- 康复费 -->
               ROUND(AVG(IFNULL(D22,0)),2) AS tcmdrug_fee, <!-- 中医费 -->
               ROUND(AVG(IFNULL(D23,0)),2) AS west_fee, <!-- 西药费 -->
               ROUND(AVG(IFNULL(IFNULL(D24,0)+IFNULL(D25,0),0)),2) AS zyf1, <!-- 中药费 -->
               ROUND(AVG(IFNULL(D24,0)),2) AS tcmpat_fee, <!-- 中成药费 -->
               ROUND(AVG(IFNULL(D25,0)),2) AS tcmherb, <!-- 中草药费-->
               ROUND(AVG(IFNULL(IFNULL(D26,0)+IFNULL(D27,0)+IFNULL(D28,0)+IFNULL(D29,0)+IFNULL(D30,0),0)),2) AS blood_blo_pro_fee, <!-- 血液和血液制品费 -->
               ROUND(AVG(IFNULL(D26,0)),2) AS blo_fee, <!-- 血费 -->
               ROUND(AVG(IFNULL(D27,0)),2) AS bdblzpf, <!-- 白蛋白类制品费 -->
               ROUND(AVG(IFNULL(D28,0)),2) AS qdblzpf, <!-- 球蛋白类制品费 -->
               ROUND(AVG(IFNULL(D29,0)),2) AS nxyzlzpf, <!-- 凝血因子类制品费 -->
               ROUND(AVG(IFNULL(D30,0)),2) AS xbyzlzpf, <!-- 细胞因子类制品费 -->
               ROUND(AVG(IFNULL(IFNULL(D31,0)+IFNULL(D32,0)+IFNULL(D33,0),0)),2) AS mcs_fee, <!-- 耗材费 -->
               ROUND(AVG(IFNULL(D31,0)),2) AS jcyycxyyclf, <!-- 检查用一次性医用材料费 -->
               ROUND(AVG(IFNULL(D32,0)),2) AS trt_use_dspo_med_matlfee, <!-- 治疗用一次性医用材料费 -->
               ROUND(AVG(IFNULL(D33,0)),2) AS oprn_use_dspo_med_matlfee, <!-- 手术用一次性医用材料费 -->
               ROUND(AVG(IFNULL(D34,0)),2) AS oth_fee, <!-- 其他费 -->
               MAX(IFNULL(d.com_med_servfee,0)) AS bgzhylfwf, <!-- 标杆综合医疗服务费 -->
               MAX(IFNULL(d.ordn_med_servfee,0)) AS bgybylfwf, <!-- 标杆一般医疗服务费 -->
               MAX(IFNULL(d.ordn_trt_oprt_fee,0)) AS bgybzlczf, <!-- 标杆一般治疗操作费 -->
               MAX(IFNULL(d.nursfee,0)) AS bghlf, <!-- 标杆护理费 -->
               MAX(IFNULL(d.oth_fee_com,0)) AS bgzhylfwqtf, <!-- 标杆综合医疗服务其他费 -->
               MAX(IFNULL(d.diag_fee,0)) AS bgzdf, <!-- 标杆诊断费 -->
               MAX(IFNULL(d.cas_diag_fee,0)) AS bgblzdf, <!-- 标杆病理诊断费 -->
               MAX(IFNULL(d.lab_diag_fee,0)) AS bgsyszdf, <!-- 标杆实验室诊断费 -->
               MAX(IFNULL(d.rdhy_diag_fee,0)) AS bgyxxzdf, <!-- 标杆影像学诊断费 -->
               MAX(IFNULL(d.clnc_diag_item_fee,0)) AS bglczdxmf, <!-- 标杆临床诊断项目费 -->
               MAX(IFNULL(d.treat_fee,0)) AS bgzlf, <!-- 标杆治疗费 -->
               MAX(IFNULL(d.nsrgtrt_item_fee,0)) AS bgfsszlxmf, <!-- 标杆非手术治疗项目费 -->
               MAX(IFNULL(d.oprn_treat_fee,0)) AS bgsszlf, <!-- 标杆手术治疗费 -->
               MAX(IFNULL(d.rhab_fee,0)) AS bgkff, <!-- 标杆康复费 -->
               MAX(IFNULL(d.tcm_treat_fee,0)) AS bgzyf, <!-- 标杆中医费 -->
               MAX(IFNULL(d.west_fee,0)) AS bgxyf, <!-- 标杆西药费 -->
               MAX(IFNULL(d.tcmdrug_fee,0)) AS bgzyf1, <!-- 标杆中药费 -->
               MAX(IFNULL(d.tcmpat_fee,0)) AS bgzcyf, <!-- 标杆中成药费 -->
               MAX(IFNULL(d.tcmherb,0)) AS bgzcyf1, <!-- 标杆中草药费 -->
               MAX(IFNULL(d.blood_blo_pro_fee,0)) AS bgxyhxyzpf, <!-- 标杆血液和血液制品费 -->
               MAX(IFNULL(d.blo_fee,0)) AS bgxf, <!-- 标杆血费 -->
               MAX(IFNULL(d.albu_clss_prod_fee,0)) AS bgbdblzpf, <!-- 标杆白蛋白类制品费 -->
               MAX(IFNULL(d.glon_clss_prod_fee,0)) AS bgqdblzpf, <!-- 标杆球蛋白类制品费 -->
               MAX(IFNULL(d.clotfac_clss_prod_fee,0)) AS bgnxyzlzpf, <!-- 标杆凝血因子类制品费 -->
               MAX(IFNULL(d.cyki_clss_prod_fee,0)) AS bgxbyzlzpf, <!-- 标杆细胞因子类制品费 -->
               MAX(IFNULL(d.mcs_fee,0)) AS bghcf, <!-- 标杆耗材费 -->
               MAX(IFNULL(d.exam_use_dspo_med_matlfee,0)) AS bgjcyycxyyclf, <!-- 标杆检查用一次性医用材料费 -->
               MAX(IFNULL(d.trt_use_dspo_med_matlfee,0)) AS bgzlyycxyyclf, <!-- 标杆治疗用一次性医用材料费 -->
               MAX(IFNULL(d.oprn_use_dspo_med_matlfee,0)) AS bgssyycxyyclf, <!-- 标杆手术用一次性医用材料费 -->
               MAX(IFNULL(d.oth_fee,0)) AS bgqtf, <!-- 标杆其他费 -->
               <!-- 十四项_标杆 -->
               MAX(IFNULL(d.medi_fee_type_bedfee,0)) AS bgfourteencwf,<!-- 十四项_床位费 -->
               MAX(IFNULL(d.medi_fee_type_diag_fee,0)) AS bgfourteenzcf,<!-- 十四项_诊查费 -->
               MAX(IFNULL(d.medi_fee_type_examfee,0)) AS bgfourteenjcf,<!-- 十四项_检查费 -->
               MAX(IFNULL(d.medi_fee_type_asy_fee,0)) AS bgfourteenhyf,<!-- 十四项_化验费 -->
               MAX(IFNULL(d.medi_fee_type_treat_fee,0)) AS bgfourteenzlf,<!-- 十四项_治疗费 -->
               MAX(IFNULL(d.medi_fee_type_oper_fee,0)) AS bgfourteenssf,<!-- 十四项_手术费 -->
               MAX(IFNULL(d.medi_fee_type_nursfee,0)) AS bgfourteenhlf,<!-- 十四项_护理费 -->
               MAX(IFNULL(d.medi_fee_type_hc_matlfee,0)) AS bgfourteenwsclf,<!-- 十四项_卫生材料费 -->
               MAX(IFNULL(d.medi_fee_type_west_fee,0)) AS bgfourteenxyf,<!-- 十四项_西药费 -->
               MAX(IFNULL(d.medi_fee_type_tmdp_fee,0)) AS bgfourteenzyypf,<!-- 十四项_中药饮片费 -->
               MAX(IFNULL(d.medi_fee_type_tcmpat_fee,0)) AS bgfourteenzcyf,<!-- 十四项_中成药费 -->
               MAX(IFNULL(d.medi_fee_type_ordn_trtfee,0)) AS bgfourteenybzlf,<!-- 十四项_一般诊疗费 -->
               MAX(IFNULL(d.medi_fee_type_regfee,0)) AS bgfourteenghf,<!-- 十四项_挂号费 -->
               MAX(IFNULL(d.medi_fee_type_oth_fee,0)) AS bgfourteenqtf,<!-- 十四项_其他费 -->
               <!-- 十四项_患者 -->
               ROUND(AVG(IFNULL(f.fourteencwf,0)),2) AS fourteencwf,<!-- 十四项_床位费 -->
               ROUND(AVG(IFNULL(f.fourteenzcf,0)),2) AS fourteenzcf,<!-- 十四项_诊查费 -->
               ROUND(AVG(IFNULL(f.fourteenjcf,0)),2) AS fourteenjcf,<!-- 十四项_检查费 -->
               ROUND(AVG(IFNULL(f.fourteenhyf,0)),2) AS fourteenhyf,<!-- 十四项_化验费 -->
               ROUND(AVG(IFNULL(f.fourteenzlf,0)),2) AS fourteenzlf,<!-- 十四项_治疗费 -->
               ROUND(AVG(IFNULL(f.fourteenssf,0)),2) AS fourteenssf,<!-- 十四项_手术费 -->
               ROUND(AVG(IFNULL(f.fourteenhlf,0)),2) AS fourteenhlf,<!-- 十四项_护理费 -->
               ROUND(AVG(IFNULL(f.fourteenwsclf,0)),2) AS fourteenwsclf,<!-- 十四项_卫生材料费 -->
               ROUND(AVG(IFNULL(f.fourteenxyf,0)),2) AS fourteenxyf,<!-- 十四项_西药费 -->
               ROUND(AVG(IFNULL(f.fourteenzyypf,0)),2) AS fourteenzyypf,<!-- 十四项_中药饮片费 -->
               ROUND(AVG(IFNULL(f.fourteenzcyf,0)),2) AS fourteenzcyf,<!-- 十四项_中成药费 -->
               ROUND(AVG(IFNULL(f.fourteenybzlf,0)),2) AS fourteenybzlf,<!-- 十四项_一般诊疗费 -->
               ROUND(AVG(IFNULL(f.fourteenghf,0)),2) AS fourteenghf,<!-- 十四项_挂号费 -->
               ROUND(AVG(IFNULL(f.fourteenqtf,0)),2) AS fourteenqtf<!-- 十四项_其他费 -->
        FROM som_drg_grp_info a
        LEFT JOIN som_hi_invy_bas_info b
        ON a.SETTLE_LIST_ID = b.ID
            AND a.HOSPITAL_ID = b.HOSPITAL_ID
        <if test='enableSeAns != null and enableSeAns != "" and enableSeAns == "1"'>
            INNER JOIN som_setl_cas_crsp p
            ON b.k00 = p.k00
        </if>
        LEFT JOIN som_drg_standard c
        ON a.drg_codg = c.drg_codg
        AND SUBSTR(ifnull(b.d37, a.dscg_time),1,4) = c.STANDARD_YEAR
        AND a.HOSPITAL_ID = c.HOSPITAL_ID
        and c.insuplc_admdvs = b.insuplc_admdvs
        LEFT JOIN som_std_fee d
        ON a.drg_codg = d.`CODE`
        AND SUBSTR(a.dscg_time,1,4) = d.STANDARD_YEAR
        AND d.TYPE = 3

            <choose>
                <when test="feeStas == 1">
                    INNER JOIN
                    (
                    SELECT
                    a.medcas_codg,
                    a.HOSPITAL_ID,
                    a.adm_time,
                    a.drg_codg,
                    a.DRG_NAME,
                    a.dscg_time,
                    b.MED_TYPE,
                    b.setl_pt_val,
                    b.sumfee AS ipt_sumfee,
                    b.dfr_fee AS ycCost
                    FROM som_drg_grp_fbck a
                    LEFT JOIN som_drg_pt_val_pay b
                    ON a.rid_idt_codg = b.rid_idt_codg
                    AND a.HOSPITAL_ID = b.HOSPITAL_ID
                    <if test="inStartTime!=null and inStartTime!='' and inEndTime!=null and inEndTime!=''">
                        <![CDATA[
                            WHERE a.adm_time >= #{inStartTime,jdbcType=VARCHAR} AND a.adm_time <= #{inEndTime,jdbcType=VARCHAR}
                        ]]>
                    </if>
                    <if test="begnDate!=null and begnDate!='' and expiDate!=null and expiDate!=''">
                        <![CDATA[
                            WHERE a.dscg_time >= #{begnDate,jdbcType=VARCHAR} AND a.dscg_time <= #{expiDate,jdbcType=VARCHAR}
                        ]]>
                    </if>
                    <if test="seStartTime!=null and seStartTime!='' and seEndTime!=null and seEndTime!=''">
                        <![CDATA[
                            WHERE a.setl_end_time >= #{seStartTime,jdbcType=VARCHAR} AND a.setl_end_time <= #{seEndTime,jdbcType=VARCHAR}
                        ]]>
                    </if>
                    <include refid="com.my.som.common.mapper.CommonMapper.fbAuth" />
                    ) e
                    ON a.PATIENT_ID = e.medcas_codg
                    AND SUBSTR(a.adm_time,1,10) = e.adm_time
                    AND SUBSTR(a.dscg_time,1,10) = e.dscg_time
                    AND a.HOSPITAL_ID = e.HOSPITAL_ID
                </when>
            </choose>
        LEFT JOIN (
            SELECT
                hi_setl_invy_id,
                MAX(CASE WHEN med_chrg_itemname = '床位费' THEN amt ELSE 0 END) AS fourteencwf,
                MAX(CASE WHEN med_chrg_itemname = '诊查费' THEN amt ELSE 0 END) AS fourteenzcf,
                MAX(CASE WHEN med_chrg_itemname = '检查费' THEN amt ELSE 0 END) AS fourteenjcf,
                MAX(CASE WHEN med_chrg_itemname = '化验费' THEN amt ELSE 0 END) AS fourteenhyf,
                MAX(CASE WHEN med_chrg_itemname = '治疗费' THEN amt ELSE 0 END) AS fourteenzlf,
                MAX(CASE WHEN med_chrg_itemname = '手术费' THEN amt ELSE 0 END) AS fourteenssf,
                MAX(CASE WHEN med_chrg_itemname = '护理费' THEN amt ELSE 0 END) AS fourteenhlf,
                MAX(CASE WHEN med_chrg_itemname = '卫生材料费' THEN amt ELSE 0 END) AS fourteenwsclf,
                MAX(CASE WHEN med_chrg_itemname = '西药费' THEN amt ELSE 0 END) AS fourteenxyf,
                MAX(CASE WHEN med_chrg_itemname = '中药饮片费' THEN amt ELSE 0 END) AS fourteenzyypf,
                MAX(CASE WHEN med_chrg_itemname = '中成药费' THEN amt ELSE 0 END) AS fourteenzcyf,
                MAX(CASE WHEN med_chrg_itemname = '一般诊疗费' THEN amt ELSE 0 END) AS fourteenybzlf,
                MAX(CASE WHEN med_chrg_itemname = '挂号费' THEN amt ELSE 0 END) AS fourteenghf,
                MAX(CASE WHEN med_chrg_itemname = '其他费' THEN amt ELSE 0 END) AS fourteenqtf
            FROM som_hi_setl_invy_med_fee_info
            WHERE hi_setl_invy_id IN(
                    SELECT SETTLE_LIST_ID AS hi_setl_invy_id
                    FROM som_drg_grp_info
                    <where>
                        <if test="inStartTime!=null and inStartTime!='' and inEndTime!=null and inEndTime!=''">
                            AND adm_time BETWEEN #{inStartTime,jdbcType=VARCHAR} and CONCAT(#{inEndTime,jdbcType=VARCHAR},' 23:59:59')
                        </if>
                        <if test="begnDate!=null and begnDate!='' and expiDate!=null and expiDate!=''">
                            AND dscg_time BETWEEN #{begnDate,jdbcType=VARCHAR} and CONCAT(#{expiDate,jdbcType=VARCHAR},' 23:59:59')
                        </if>
                        <if test="seStartTime!=null and seStartTime!='' and seEndTime!=null and seEndTime!=''">
                            AND setl_end_time BETWEEN #{seStartTime,jdbcType=VARCHAR} and CONCAT(#{seEndTime,jdbcType=VARCHAR},' 23:59:59')
                        </if>
                    </where>
                    )
            GROUP BY hi_setl_invy_id
            ) f
        ON a.SETTLE_LIST_ID = f.hi_setl_invy_id

        WHERE a.grp_stas = 1
        <!-- AND a.dscg_time BETWEEN #{begnDate,jdbcType=VARCHAR} AND CONCAT(#{expiDate,jdbcType=VARCHAR},' 23:59:59')  -->
        <if test="inStartTime != null and inStartTime != '' and inEndTime != null and inEndTime != ''">
            AND a.adm_time BETWEEN #{inStartTime,jdbcType=VARCHAR} AND CONCAT(#{inEndTime,jdbcType=VARCHAR},' 23:59:59')
        </if>
        <if test="begnDate!=null and begnDate!='' and expiDate!=null and expiDate!=''">
            AND a.dscg_time BETWEEN #{begnDate,jdbcType=VARCHAR} and CONCAT(#{expiDate,jdbcType=VARCHAR},' 23:59:59')
        </if>
        <if test="seStartTime!=null and seStartTime!='' and seEndTime!=null and seEndTime!=''">
            AND a.setl_end_time BETWEEN #{seStartTime,jdbcType=VARCHAR} and CONCAT(#{seEndTime,jdbcType=VARCHAR},' 23:59:59')
        </if>
        <if test="drgCodg != null and drgCodg != ''">
            AND a.drg_codg = #{drgCodg,jdbcType=VARCHAR}
        </if>
        <if test="cbd != null and cbd != ''  ">
            <choose>
                <when test="cbd == 1">
                    and  b.insuplc_admdvs = SUBSTRING(#{insuplcAdmdvs}, 1, 4)
                </when>
                <otherwise>
                    <!-- 这里是 else 部分的逻辑 -->
                    and b.insuplc_admdvs != SUBSTRING(#{insuplcAdmdvs}, 1, 4) <!-- 你可以在这里设置默认值 -->
                </otherwise>
            </choose>
        </if>
        <if test="categories != null and categories != ''">
            <choose>
                <when test="categories == 1">
                    and   b.a54 in  ('1','01','310')
                </when>
                <otherwise>
                    <!-- 这里是 else 部分的逻辑 -->
                    and    b.a54 not in  ('1','01','310') <!-- 你可以在这里设置默认值 -->
                </otherwise>
            </choose>
        </if>
        <include refid="com.my.som.mapper.newBusiness.common.NewBusinessCommonAnalysisMapper.queryBusCommon" />
        GROUP BY
            <choose>
                <when test="feeStas == 0">
                    <choose>
                        <when test="feeMulAdjmConf != null and feeMulAdjmConf != '' and feeMulAdjmConf == 'true' ">
                            a.drg_codg,a.DRG_NAME,b.insuplc_admdvs,b.a54,c.adjm_cof,c.adjm_resid_cof
                        </when>
                        <otherwise>
                            a.drg_codg,a.DRG_NAME
                          </otherwise>
                    </choose>

                </when>
                <when test="feeStas == 1">
                    e.drg_codg,e.DRG_NAME
                </when>
            </choose>

    </select>

    <select id="queryDrgDiseaseForecastData" resultType="com.my.som.vo.newBusiness.disease.NewBusinessDiseaseVo">
        SELECT a.* FROM ( SELECT
                a.drg_codg AS drgCodg,
                a.DRG_NAME AS drgName,
               count(1) AS drgInGroupMedcasVal,
               IFNULL(ROUND(SUM(a.ipt_sumfee),2),0) AS sumfee,
               IFNULL(ROUND(SUM(a.ycCost),2),0) AS forecastAmount,
                ROUND( IFNULL( SUM( a.profitloss ), 0 ), 2 ) AS forecastAmountDiff,
                IFNULL(ROUND(IFNULL(SUM(a.ipt_sumfee),0) / NULLIF(SUM(a.ycCost),0),2),0) AS oeVal
        <if test="feeMulAdjmConf != null and feeMulAdjmConf != '' and feeMulAdjmConf == 'true' ">
        ,case when
        c.a54 in ('1','01','310') then '职工'
        else '居民' end as categories,
        case when
        LTRIM(RTRIM(c.insuplc_admdvs)) = #{insuplcAdmdvs}   THEN '市医保'
        else '省本级'
        END as cbd
        </if>
        FROM
            (
                SELECT a.drg_codg,
                       a.DRG_NAME,
                       a.dscg_caty_codg_inhosp,
                       a.ipdr_code,
                       a.ipdr_name,
                       a.`NAME`,
                       a.PATIENT_ID,
                       a.SETTLE_LIST_ID,
                       a.ipt_sumfee,
                       a.drugfee,
                       a.mcs_fee,
                       b.dise_type,
                       b.ycCost,
                        b.profitloss
                FROM som_drg_grp_info a
                <if test='enableSeAns != null and enableSeAns != "" and enableSeAns == "1"'>
                    LEFT JOIN som_hi_invy_bas_info q
                    ON q.ID = a.SETTLE_LIST_ID
                    INNER JOIN som_setl_cas_crsp p
                    ON q.k00 = p.k00
                </if>
                         LEFT JOIN
                     (
                        SELECT
                            SETTLE_LIST_ID,
                            dise_type,
                            forecast_fee AS ycCost,
                            profitloss AS profitloss
                            FROM som_drg_sco
                     ) b
                     ON a.SETTLE_LIST_ID = b.SETTLE_LIST_ID
                <!--
                    WHERE a.dscg_time BETWEEN #{begnDate} AND CONCAT(#{expiDate},' 23:59:59')
                <if test="inStartTime != null and inStartTime != '' and inEndTime != null and inEndTime != ''">
                    AND a.adm_time BETWEEN #{inStartTime} AND CONCAT(#{inEndTime},' 23:59:59')
                </if>
                 -->
                WHERE a.drg_codg IS NOT NULL
                <if test="inStartTime!=null and inStartTime!='' and inEndTime!=null and inEndTime!=''">
                    AND a.adm_time BETWEEN #{inStartTime,jdbcType=VARCHAR} and CONCAT(#{inEndTime,jdbcType=VARCHAR},' 23:59:59')
                </if>
                <if test="begnDate!=null and begnDate!='' and expiDate!=null and expiDate!=''">
                    AND a.dscg_time BETWEEN #{begnDate,jdbcType=VARCHAR} and CONCAT(#{expiDate,jdbcType=VARCHAR},' 23:59:59')
                </if>
                <if test="seStartTime!=null and seStartTime!='' and seEndTime!=null and seEndTime!=''">
                    AND a.setl_end_time BETWEEN #{seStartTime,jdbcType=VARCHAR} and CONCAT(#{seEndTime,jdbcType=VARCHAR},' 23:59:59')
                </if>
                <include refid="com.my.som.mapper.newBusiness.common.NewBusinessCommonAnalysisMapper.queryBusCommon" />
            ) a
        LEFT JOIN som_drg_grp_rcd b
        ON a.SETTLE_LIST_ID = b.SETTLE_LIST_ID
        LEFT JOIN som_hi_invy_bas_info c
        ON a.SETTLE_LIST_ID = c.id
        <where>
        <if test="drgCodg != null and drgCodg != ''">
          a.drg_codg = #{drgCodg,jdbcType=VARCHAR}
        </if>
        <if test="cbd != null and cbd != ''  ">
            <choose>
                <when test="cbd == 1">
                    and  c.insuplc_admdvs = SUBSTRING(#{insuplcAdmdvs}, 1, 4)
                </when>
                <otherwise>
                    <!-- 这里是 else 部分的逻辑 -->
                    and c.insuplc_admdvs != SUBSTRING(#{insuplcAdmdvs}, 1, 4) <!-- 你可以在这里设置默认值 -->
                </otherwise>
            </choose>
        </if>
        <if test="categories != null and categories != ''">
            <choose>
                <when test="categories == 1">
                    and   c.a54 in  ('1','01','310')
                </when>
                <otherwise>
                    <!-- 这里是 else 部分的逻辑 -->
                    and    c.a54 not in  ('1','01','310') <!-- 你可以在这里设置默认值 -->
                </otherwise>
            </choose>
        </if>
        </where>
        GROUP BY a.drg_codg,a.DRG_NAME
        <if test="feeMulAdjmConf != null and feeMulAdjmConf != '' and feeMulAdjmConf == 'true' ">
            ,c.insuplc_admdvs,c.a54
        </if>
        ) a
<!--        <where>-->
<!--            <if test="isLossType != null and isLossType != ''">-->
<!--                <choose>-->
<!--                    <when test="isLossType == 1">-->
<!--                        <![CDATA[-->
<!--                            a.forecastAmountDiff < 0-->
<!--                        ]]>-->
<!--                    </when>-->
<!--                    <when test="isLossType == 0">-->
<!--                        <![CDATA[-->
<!--                            a.forecastAmountDiff >= 0-->
<!--                        ]]>-->
<!--                    </when>-->
<!--                </choose>-->
<!--            </if>-->
<!--        </where>-->
    </select>

</mapper>
