<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.som.mapper.newBusiness.disease.NewDipBusinessDiseaseAnalysisMapper">
    <!-- 查询病组Kpi -->
    <select id="queryDiseaseKpiData" resultType="com.my.som.vo.newBusiness.disease.NewBusinessDiseaseVo">
        SELECT
               COUNT(1) AS drgInGroupMedcasVal,
               ROUND(AVG(IFNULL(a.act_ipt,0)),2) AS avgInHosDays,
               ROUND(MAX(IFNULL(AES_DECRYPT(UNHEX(c.dip_standard_avg_fee_same_lv),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}),0)),2) AS standardInHosCost,
               MAX(IFNULL(AES_DECRYPT(UNHEX(c.dip_standard_ipt_days_same_lv),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}),0)) AS standardInHosDays,
               IFNULL(ROUND(AVG(IFNULL(a.act_ipt,0))/NULLIF(AVG(IFNULL(AES_DECRYPT(UNHEX(c.dip_standard_ipt_days_same_lv),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}),0)),0),2),0) AS timeIndex,
               MAX(IFNULL(sc.drug_ratio,0)) * 100 AS standardDrugRatio,
               MAX(IFNULL(sc.mcs_fee_rat,0)) * 100 AS standardConsumeRatio,
               ROUND(AVG(IFNULL(a.ipt_sumfee,0)),2) AS avgInHosCost,

                   <choose>
                       <when test="feeStas == 0">
                           a.dip_codg AS dipCodg,
                           a.DIP_NAME AS dipName,
                           IFNULL(ROUND(AVG(IFNULL(a.ipt_sumfee,0))/NULLIF(AVG(IFNULL(AES_DECRYPT(UNHEX(c.dip_standard_avg_fee_same_lv),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}),0)),0),2),0) AS costIndex,
                           ROUND(IFNULL(SUM(IFNULL(a.drugfee,0))/NULLIF(SUM(IFNULL(a.ipt_sumfee,0)),0),0) * 100,2) AS drugRatio,
                           ROUND(IFNULL(SUM(IFNULL(a.mcs_fee,0))/NULLIF(SUM(IFNULL(a.ipt_sumfee,0)),0),0) * 100,2) AS consumeRatio,
                       </when>
                       <when test="feeStas == 1">
                           e.dip_codg AS dipCodg,
                           e.DIP_NAME AS dipName,
                           ROUND(AVG(IFNULL(e.ipt_sumfee,0)),2) AS fbAvgInHosCost,
                           IFNULL(ROUND(AVG(IFNULL(e.ipt_sumfee,0))/NULLIF(AVG(IFNULL(AES_DECRYPT(UNHEX(c.dip_standard_avg_fee_same_lv),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}),0)),0),2),0) AS costIndex,
                           ROUND(IFNULL(SUM(IFNULL(a.drugfee,0))/NULLIF(SUM(IFNULL(e.ipt_sumfee,0)),0),0) * 100,2) AS drugRatio,
                           ROUND(IFNULL(SUM(IFNULL(a.mcs_fee,0))/NULLIF(SUM(IFNULL(e.ipt_sumfee,0)),0),0) * 100,2) AS consumeRatio,
                       </when>
                   </choose>

               ROUND(AVG(IFNULL(IFNULL(D11,0)+IFNULL(D12,0)+IFNULL(D13,0)+IFNULL(D14,0),0)),2) AS com_med_servfee, <!-- 综合医疗服务费 -->
               ROUND(AVG(IFNULL(D11,0)),2) AS ordn_med_servfee, <!-- 一般医疗服务费 -->
               ROUND(AVG(IFNULL(D12,0)),2) AS ordn_trt_oprt_fee, <!-- 一般治疗操作费 -->
               ROUND(AVG(IFNULL(D13,0)),2) AS nursfee, <!-- 护理费 -->
               ROUND(AVG(IFNULL(D14,0)),2) AS zhylfwqtf, <!-- 综合医疗服务其他费 -->
               ROUND(AVG(IFNULL(IFNULL(D15,0)+IFNULL(D16,0)+IFNULL(D17,0)+IFNULL(D18,0),0)),2) AS diag_fee, <!-- 诊断费 -->
               ROUND(AVG(IFNULL(D15,0)),2) AS cas_diag_fee, <!-- 病理诊断费 -->
               ROUND(AVG(IFNULL(D16,0)),2) AS lab_diag_fee, <!-- 实验室诊断费 -->
               ROUND(AVG(IFNULL(D17,0)),2) AS rdhy_diag_fee, <!-- 影像学诊断费 -->
               ROUND(AVG(IFNULL(D18,0)),2) AS clnc_diag_item_fee, <!-- 临床诊断项目费 -->
               ROUND(AVG(IFNULL(IFNULL(D19,0)+IFNULL(D20,0),0)),2) AS treat_fee, <!-- 治疗费 -->
               ROUND(AVG(IFNULL(D19,0)),2) AS nsrgtrt_item_fee, <!-- 非手术治疗项目费 -->
               ROUND(AVG(IFNULL(D20,0)),2) AS oprn_treat_fee, <!-- 手术治疗费 -->
               ROUND(AVG(IFNULL(D21,0)),2) AS rhab_fee, <!-- 康复费 -->
               ROUND(AVG(IFNULL(D22,0)),2) AS tcmdrug_fee, <!-- 中医费 -->
               ROUND(AVG(IFNULL(D23,0)),2) AS west_fee, <!-- 西药费 -->
               ROUND(AVG(IFNULL(IFNULL(D24,0)+IFNULL(D25,0),0)),2) AS zyf1, <!-- 中药费 -->
               ROUND(AVG(IFNULL(D24,0)),2) AS tcmpat_fee, <!-- 中成药费 -->
               ROUND(AVG(IFNULL(D25,0)),2) AS tcmherb, <!-- 中草药费-->
               ROUND(AVG(IFNULL(IFNULL(D26,0)+IFNULL(D27,0)+IFNULL(D28,0)+IFNULL(D29,0)+IFNULL(D30,0),0)),2) AS blood_blo_pro_fee, <!-- 血液和血液制品费 -->
               ROUND(AVG(IFNULL(D26,0)),2) AS blo_fee, <!-- 血费 -->
               ROUND(AVG(IFNULL(D27,0)),2) AS bdblzpf, <!-- 白蛋白类制品费 -->
               ROUND(AVG(IFNULL(D28,0)),2) AS qdblzpf, <!-- 球蛋白类制品费 -->
               ROUND(AVG(IFNULL(D29,0)),2) AS nxyzlzpf, <!-- 凝血因子类制品费 -->
               ROUND(AVG(IFNULL(D30,0)),2) AS xbyzlzpf, <!-- 细胞因子类制品费 -->
               ROUND(AVG(IFNULL(IFNULL(D31,0)+IFNULL(D32,0)+IFNULL(D33,0),0)),2) AS mcs_fee, <!-- 耗材费 -->
               ROUND(AVG(IFNULL(D31,0)),2) AS jcyycxyyclf, <!-- 检查用一次性医用材料费 -->
               ROUND(AVG(IFNULL(D32,0)),2) AS trt_use_dspo_med_matlfee, <!-- 治疗用一次性医用材料费 -->
               ROUND(AVG(IFNULL(D33,0)),2) AS oprn_use_dspo_med_matlfee, <!-- 手术用一次性医用材料费 -->
               ROUND(AVG(IFNULL(D34,0)),2) AS oth_fee, <!-- 其他费 -->
               MAX(IFNULL(d.com_med_servfee,0)) AS bgzhylfwf, <!-- 标杆综合医疗服务费 -->
               MAX(IFNULL(d.ordn_med_servfee,0)) AS bgybylfwf, <!-- 标杆一般医疗服务费 -->
               MAX(IFNULL(d.ordn_trt_oprt_fee,0)) AS bgybzlczf, <!-- 标杆一般治疗操作费 -->
               MAX(IFNULL(d.nursfee,0)) AS bghlf, <!-- 标杆护理费 -->
               MAX(IFNULL(d.oth_fee_com,0)) AS bgzhylfwqtf, <!-- 标杆综合医疗服务其他费 -->
               MAX(IFNULL(d.diag_fee,0)) AS bgzdf, <!-- 标杆诊断费 -->
               MAX(IFNULL(d.cas_diag_fee,0)) AS bgblzdf, <!-- 标杆病理诊断费 -->
               MAX(IFNULL(d.lab_diag_fee,0)) AS bgsyszdf, <!-- 标杆实验室诊断费 -->
               MAX(IFNULL(d.rdhy_diag_fee,0)) AS bgyxxzdf, <!-- 标杆影像学诊断费 -->
               MAX(IFNULL(d.clnc_diag_item_fee,0)) AS bglczdxmf, <!-- 标杆临床诊断项目费 -->
               MAX(IFNULL(d.treat_fee,0)) AS bgzlf, <!-- 标杆治疗费 -->
               MAX(IFNULL(d.nsrgtrt_item_fee,0)) AS bgfsszlxmf, <!-- 标杆非手术治疗项目费 -->
               MAX(IFNULL(d.oprn_treat_fee,0)) AS bgsszlf, <!-- 标杆手术治疗费 -->
               MAX(IFNULL(d.rhab_fee,0)) AS bgkff, <!-- 标杆康复费 -->
               MAX(IFNULL(d.tcm_treat_fee,0)) AS bgzyf, <!-- 标杆中医费 -->
               MAX(IFNULL(d.west_fee,0)) AS bgxyf, <!-- 标杆西药费 -->
               MAX(IFNULL(d.tcmdrug_fee,0)) AS bgzyf1, <!-- 标杆中药费 -->
               MAX(IFNULL(d.tcmpat_fee,0)) AS bgzcyf, <!-- 标杆中成药费 -->
               MAX(IFNULL(d.tcmherb,0)) AS bgzcyf1, <!-- 标杆中草药费 -->
               MAX(IFNULL(d.blood_blo_pro_fee,0)) AS bgxyhxyzpf, <!-- 标杆血液和血液制品费 -->
               MAX(IFNULL(d.blo_fee,0)) AS bgxf, <!-- 标杆血费 -->
               MAX(IFNULL(d.albu_clss_prod_fee,0)) AS bgbdblzpf, <!-- 标杆白蛋白类制品费 -->
               MAX(IFNULL(d.glon_clss_prod_fee,0)) AS bgqdblzpf, <!-- 标杆球蛋白类制品费 -->
               MAX(IFNULL(d.clotfac_clss_prod_fee,0)) AS bgnxyzlzpf, <!-- 标杆凝血因子类制品费 -->
               MAX(IFNULL(d.cyki_clss_prod_fee,0)) AS bgxbyzlzpf, <!-- 标杆细胞因子类制品费 -->
               MAX(IFNULL(d.mcs_fee,0)) AS bghcf, <!-- 标杆耗材费 -->
               MAX(IFNULL(d.exam_use_dspo_med_matlfee,0)) AS bgjcyycxyyclf, <!-- 标杆检查用一次性医用材料费 -->
               MAX(IFNULL(d.trt_use_dspo_med_matlfee,0)) AS bgzlyycxyyclf, <!-- 标杆治疗用一次性医用材料费 -->
               MAX(IFNULL(d.oprn_use_dspo_med_matlfee,0)) AS bgssyycxyyclf, <!-- 标杆手术用一次性医用材料费 -->
               MAX(IFNULL(d.oth_fee,0)) AS bgqtf <!-- 标杆其他费 -->
        FROM som_dip_grp_info a
        LEFT JOIN som_hi_invy_bas_info b
        ON a.SETTLE_LIST_ID = b.ID
        LEFT JOIN som_std_fee sc
        ON a.dip_codg = sc.`CODE`
        AND a.asst_list_dise_sev_deg = sc.asst_list_dise_sev_deg
        AND a.asst_list_age_grp = sc.asst_list_age_grp
        AND a.asst_list_tmor_sev_deg = sc.asst_list_tmor_sev_deg
        AND a.HOSPITAL_ID = sc.HOSPITAL_ID
        AND SUBSTR(a.dscg_time,1,4) = sc.STANDARD_YEAR
        AND sc.TYPE = 1
        LEFT JOIN som_dip_standard c
        ON a.dip_codg = c.dip_codg
        AND a.is_used_asst_list = c.is_used_asst_list
        AND a.asst_list_age_grp = c.asst_list_age_grp
        AND a.asst_list_dise_sev_deg = c.asst_list_dise_sev_deg
        AND a.asst_list_tmor_sev_deg = c.asst_list_tmor_sev_deg
        AND SUBSTR(a.dscg_time,1,4) = c.STANDARD_YEAR
        AND a.HOSPITAL_ID = c.HOSPITAL_ID
        LEFT JOIN som_std_fee d
        ON a.dip_codg = d.`CODE`
        AND a.asst_list_age_grp = d.asst_list_age_grp
        AND a.asst_list_dise_sev_deg = d.asst_list_dise_sev_deg
        AND a.asst_list_tmor_sev_deg = d.asst_list_tmor_sev_deg
        AND SUBSTR(a.dscg_time,1,4) = d.STANDARD_YEAR
        AND d.TYPE = 1
            <choose>
                <when test="feeStas == 1">
                    INNER JOIN
                    (
                    SELECT
                    a.medcas_codg,
                    a.adm_time,
                    a.dip_codg,
                    a.DIP_NAME,
                    a.dscg_time,
                    a.used_asst_list,
                    a.asst_list_age_grp,
                    a.asst_list_dise_sev_deg,
                    a.asst_list_tmor_sev_deg,
                    b.MED_TYPE,
                    b.setl_pt_val,
                    b.sumfee AS ipt_sumfee,
                    b.dfr_fee AS ycCost
                    FROM som_dip_grp_fbck a
                    LEFT JOIN som_fund_dfr_fbck b
                    ON a.rid_idt_codg = b.rid_idt_codg
                    AND a.HOSPITAL_ID = b.HOSPITAL_ID
                    <![CDATA[
                    WHERE a.dscg_time >= #{begnDate} AND a.dscg_time <= #{expiDate}
                    ]]>
                    <include refid="com.my.som.common.mapper.CommonMapper.fbAuth" />
                    ) e
                    ON a.PATIENT_ID = e.medcas_codg
                    AND SUBSTR(a.adm_time,1,10) = e.adm_time
                    AND SUBSTR(a.dscg_time,1,10) = e.dscg_time
                </when>
            </choose>

        WHERE a.grp_stas = 1
        AND a.dscg_time BETWEEN #{begnDate,jdbcType=VARCHAR} AND CONCAT(#{expiDate,jdbcType=VARCHAR},' 23:59:59')
          <if test="inStartTime != null and inStartTime != '' and inEndTime != null and inEndTime != ''">
              AND a.adm_time BETWEEN #{inStartTime} AND CONCAT(#{inEndTime},' 23:59:59')
          </if>
        <if test="dipCodg != null and dipCodg != ''">
            AND a.dip_codg = #{dipCodg,jdbcType=VARCHAR}
        </if>
        <!-- 通用查询条件 -->
        <include refid="com.my.som.mapper.newBusiness.common.NewBusinessCommonAnalysisMapper.queryBusCommon" />
        GROUP BY
            <choose>
                <when test="feeStas == 0">
                    a.dip_codg,a.DIP_NAME
                </when>
                <when test="feeStas == 1">
                    e.dip_codg,e.DIP_NAME
                </when>
            </choose>

    </select>

    <select id="queryDiseaseKpiData2" resultType="com.my.som.vo.newBusiness.disease.NewBusinessDiseaseVo">
        SELECT
        COUNT(1) AS drgInGroupMedcasVal,
        ROUND(AVG(IFNULL(a.act_ipt,0)),2) AS avgInHosDays,
        ROUND(MAX(IFNULL(AES_DECRYPT(UNHEX(c.dip_standard_avg_fee_same_lv),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}),0)),2) AS standardInHosCost,
        MAX(IFNULL(AES_DECRYPT(UNHEX(c.dip_standard_ipt_days_same_lv),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}),0)) AS standardInHosDays,
        IFNULL(ROUND(AVG(IFNULL(a.act_ipt,0))/NULLIF(AVG(IFNULL(AES_DECRYPT(UNHEX(c.dip_standard_ipt_days_same_lv),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}),0)),0),2),0) AS timeIndex,
        MAX(IFNULL(sc.drug_ratio,0)) * 100 AS standardDrugRatio,
        MAX(IFNULL(sc.mcs_fee_rat,0)) * 100 AS standardConsumeRatio,
        ROUND(AVG(IFNULL(a.ipt_sumfee,0)),2) AS avgInHosCost,

        <choose>
            <when test="feeStas == 0">
                a.dip_codg AS dipCodg,
                a.DIP_NAME AS dipName,
                a.asst_list_age_grp as asstListAgeGrp,
                a.asst_list_dise_sev_deg as asstListDiseSevDeg,
                a.asst_list_tmor_sev_deg as asstListTmorSevDeg,
                IFNULL(ROUND(AVG(IFNULL(a.ipt_sumfee,0))/NULLIF(AVG(IFNULL(AES_DECRYPT(UNHEX(c.dip_standard_avg_fee_same_lv),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}),0)),0),2),0) AS costIndex,
                ROUND(IFNULL(SUM(IFNULL(a.drugfee,0))/NULLIF(SUM(IFNULL(a.ipt_sumfee,0)),0),0) * 100,2) AS drugRatio,
                ROUND(IFNULL(SUM(IFNULL(a.mcs_fee,0))/NULLIF(SUM(IFNULL(a.ipt_sumfee,0)),0),0) * 100,2) AS consumeRatio,
            </when>
            <when test="feeStas == 1">
                e.dip_codg AS dipCodg,
                e.DIP_NAME AS dipName,
                ROUND(AVG(IFNULL(e.ipt_sumfee,0)),2) AS fbAvgInHosCost,
                IFNULL(ROUND(AVG(IFNULL(e.ipt_sumfee,0))/NULLIF(AVG(IFNULL(AES_DECRYPT(UNHEX(c.dip_standard_avg_fee_same_lv),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}),0)),0),2),0) AS costIndex,
                ROUND(IFNULL(SUM(IFNULL(a.drugfee,0))/NULLIF(SUM(IFNULL(e.ipt_sumfee,0)),0),0) * 100,2) AS drugRatio,
                ROUND(IFNULL(SUM(IFNULL(a.mcs_fee,0))/NULLIF(SUM(IFNULL(e.ipt_sumfee,0)),0),0) * 100,2) AS consumeRatio,
            </when>
        </choose>

        ROUND(AVG(IFNULL(IFNULL(D11,0)+IFNULL(D12,0)+IFNULL(D13,0)+IFNULL(D14,0),0)),2) AS com_med_servfee, <!-- 综合医疗服务费 -->
        ROUND(AVG(IFNULL(D11,0)),2) AS ordn_med_servfee, <!-- 一般医疗服务费 -->
        ROUND(AVG(IFNULL(D12,0)),2) AS ordn_trt_oprt_fee, <!-- 一般治疗操作费 -->
        ROUND(AVG(IFNULL(D13,0)),2) AS nursfee, <!-- 护理费 -->
        ROUND(AVG(IFNULL(D14,0)),2) AS zhylfwqtf, <!-- 综合医疗服务其他费 -->
        ROUND(AVG(IFNULL(IFNULL(D15,0)+IFNULL(D16,0)+IFNULL(D17,0)+IFNULL(D18,0),0)),2) AS diag_fee, <!-- 诊断费 -->
        ROUND(AVG(IFNULL(D15,0)),2) AS cas_diag_fee, <!-- 病理诊断费 -->
        ROUND(AVG(IFNULL(D16,0)),2) AS lab_diag_fee, <!-- 实验室诊断费 -->
        ROUND(AVG(IFNULL(D17,0)),2) AS rdhy_diag_fee, <!-- 影像学诊断费 -->
        ROUND(AVG(IFNULL(D18,0)),2) AS clnc_diag_item_fee, <!-- 临床诊断项目费 -->
        ROUND(AVG(IFNULL(IFNULL(D19,0)+IFNULL(D20,0),0)),2) AS treat_fee, <!-- 治疗费 -->
        ROUND(AVG(IFNULL(D19,0)),2) AS nsrgtrt_item_fee, <!-- 非手术治疗项目费 -->
        ROUND(AVG(IFNULL(D20,0)),2) AS oprn_treat_fee, <!-- 手术治疗费 -->
        ROUND(AVG(IFNULL(D21,0)),2) AS rhab_fee, <!-- 康复费 -->
        ROUND(AVG(IFNULL(D22,0)),2) AS tcmdrug_fee, <!-- 中医费 -->
        ROUND(AVG(IFNULL(D23,0)),2) AS west_fee, <!-- 西药费 -->
        ROUND(AVG(IFNULL(IFNULL(D24,0)+IFNULL(D25,0),0)),2) AS zyf1, <!-- 中药费 -->
        ROUND(AVG(IFNULL(D24,0)),2) AS tcmpat_fee, <!-- 中成药费 -->
        ROUND(AVG(IFNULL(D25,0)),2) AS tcmherb, <!-- 中草药费-->
        ROUND(AVG(IFNULL(IFNULL(D26,0)+IFNULL(D27,0)+IFNULL(D28,0)+IFNULL(D29,0)+IFNULL(D30,0),0)),2) AS blood_blo_pro_fee, <!-- 血液和血液制品费 -->
        ROUND(AVG(IFNULL(D26,0)),2) AS blo_fee, <!-- 血费 -->
        ROUND(AVG(IFNULL(D27,0)),2) AS bdblzpf, <!-- 白蛋白类制品费 -->
        ROUND(AVG(IFNULL(D28,0)),2) AS qdblzpf, <!-- 球蛋白类制品费 -->
        ROUND(AVG(IFNULL(D29,0)),2) AS nxyzlzpf, <!-- 凝血因子类制品费 -->
        ROUND(AVG(IFNULL(D30,0)),2) AS xbyzlzpf, <!-- 细胞因子类制品费 -->
        ROUND(AVG(IFNULL(IFNULL(D31,0)+IFNULL(D32,0)+IFNULL(D33,0),0)),2) AS mcs_fee, <!-- 耗材费 -->
        ROUND(AVG(IFNULL(D31,0)),2) AS jcyycxyyclf, <!-- 检查用一次性医用材料费 -->
        ROUND(AVG(IFNULL(D32,0)),2) AS trt_use_dspo_med_matlfee, <!-- 治疗用一次性医用材料费 -->
        ROUND(AVG(IFNULL(D33,0)),2) AS oprn_use_dspo_med_matlfee, <!-- 手术用一次性医用材料费 -->
        ROUND(AVG(IFNULL(D34,0)),2) AS oth_fee, <!-- 其他费 -->
        MAX(IFNULL(d.com_med_servfee,0)) AS bgzhylfwf, <!-- 标杆综合医疗服务费 -->
        MAX(IFNULL(d.ordn_med_servfee,0)) AS bgybylfwf, <!-- 标杆一般医疗服务费 -->
        MAX(IFNULL(d.ordn_trt_oprt_fee,0)) AS bgybzlczf, <!-- 标杆一般治疗操作费 -->
        MAX(IFNULL(d.nursfee,0)) AS bghlf, <!-- 标杆护理费 -->
        MAX(IFNULL(d.oth_fee_com,0)) AS bgzhylfwqtf, <!-- 标杆综合医疗服务其他费 -->
        MAX(IFNULL(d.diag_fee,0)) AS bgzdf, <!-- 标杆诊断费 -->
        MAX(IFNULL(d.cas_diag_fee,0)) AS bgblzdf, <!-- 标杆病理诊断费 -->
        MAX(IFNULL(d.lab_diag_fee,0)) AS bgsyszdf, <!-- 标杆实验室诊断费 -->
        MAX(IFNULL(d.rdhy_diag_fee,0)) AS bgyxxzdf, <!-- 标杆影像学诊断费 -->
        MAX(IFNULL(d.clnc_diag_item_fee,0)) AS bglczdxmf, <!-- 标杆临床诊断项目费 -->
        MAX(IFNULL(d.treat_fee,0)) AS bgzlf, <!-- 标杆治疗费 -->
        MAX(IFNULL(d.nsrgtrt_item_fee,0)) AS bgfsszlxmf, <!-- 标杆非手术治疗项目费 -->
        MAX(IFNULL(d.oprn_treat_fee,0)) AS bgsszlf, <!-- 标杆手术治疗费 -->
        MAX(IFNULL(d.rhab_fee,0)) AS bgkff, <!-- 标杆康复费 -->
        MAX(IFNULL(d.tcm_treat_fee,0)) AS bgzyf, <!-- 标杆中医费 -->
        MAX(IFNULL(d.west_fee,0)) AS bgxyf, <!-- 标杆西药费 -->
        MAX(IFNULL(d.tcmdrug_fee,0)) AS bgzyf1, <!-- 标杆中药费 -->
        MAX(IFNULL(d.tcmpat_fee,0)) AS bgzcyf, <!-- 标杆中成药费 -->
        MAX(IFNULL(d.tcmherb,0)) AS bgzcyf1, <!-- 标杆中草药费 -->
        MAX(IFNULL(d.blood_blo_pro_fee,0)) AS bgxyhxyzpf, <!-- 标杆血液和血液制品费 -->
        MAX(IFNULL(d.blo_fee,0)) AS bgxf, <!-- 标杆血费 -->
        MAX(IFNULL(d.albu_clss_prod_fee,0)) AS bgbdblzpf, <!-- 标杆白蛋白类制品费 -->
        MAX(IFNULL(d.glon_clss_prod_fee,0)) AS bgqdblzpf, <!-- 标杆球蛋白类制品费 -->
        MAX(IFNULL(d.clotfac_clss_prod_fee,0)) AS bgnxyzlzpf, <!-- 标杆凝血因子类制品费 -->
        MAX(IFNULL(d.cyki_clss_prod_fee,0)) AS bgxbyzlzpf, <!-- 标杆细胞因子类制品费 -->
        MAX(IFNULL(d.mcs_fee,0)) AS bghcf, <!-- 标杆耗材费 -->
        MAX(IFNULL(d.exam_use_dspo_med_matlfee,0)) AS bgjcyycxyyclf, <!-- 标杆检查用一次性医用材料费 -->
        MAX(IFNULL(d.trt_use_dspo_med_matlfee,0)) AS bgzlyycxyyclf, <!-- 标杆治疗用一次性医用材料费 -->
        MAX(IFNULL(d.oprn_use_dspo_med_matlfee,0)) AS bgssyycxyyclf, <!-- 标杆手术用一次性医用材料费 -->
        MAX(IFNULL(d.oth_fee,0)) AS bgqtf, <!-- 标杆其他费 -->
        <!-- 十四项_标杆 -->
        MAX(IFNULL(d.medi_fee_type_bedfee,0)) AS bgfourteencwf,<!-- 十四项_床位费 -->
        MAX(IFNULL(d.medi_fee_type_diag_fee,0)) AS bgfourteenzcf,<!-- 十四项_诊查费 -->
        MAX(IFNULL(d.medi_fee_type_examfee,0)) AS bgfourteenjcf,<!-- 十四项_检查费 -->
        MAX(IFNULL(d.medi_fee_type_asy_fee,0)) AS bgfourteenhyf,<!-- 十四项_化验费 -->
        MAX(IFNULL(d.medi_fee_type_treat_fee,0)) AS bgfourteenzlf,<!-- 十四项_治疗费 -->
        MAX(IFNULL(d.medi_fee_type_oper_fee,0)) AS bgfourteenssf,<!-- 十四项_手术费 -->
        MAX(IFNULL(d.medi_fee_type_nursfee,0)) AS bgfourteenhlf,<!-- 十四项_护理费 -->
        MAX(IFNULL(d.medi_fee_type_hc_matlfee,0)) AS bgfourteenwsclf,<!-- 十四项_卫生材料费 -->
        MAX(IFNULL(d.medi_fee_type_west_fee,0)) AS bgfourteenxyf,<!-- 十四项_西药费 -->
        MAX(IFNULL(d.medi_fee_type_tmdp_fee,0)) AS bgfourteenzyypf,<!-- 十四项_中药饮片费 -->
        MAX(IFNULL(d.medi_fee_type_tcmpat_fee,0)) AS bgfourteenzcyf,<!-- 十四项_中成药费 -->
        MAX(IFNULL(d.medi_fee_type_ordn_trtfee,0)) AS bgfourteenybzlf,<!-- 十四项_一般诊疗费 -->
        MAX(IFNULL(d.medi_fee_type_regfee,0)) AS bgfourteenghf,<!-- 十四项_挂号费 -->
        MAX(IFNULL(d.medi_fee_type_oth_fee,0)) AS bgfourteenqtf,<!-- 十四项_其他费 -->
        <!-- 十四项_患者 -->
        ROUND(AVG(IFNULL(f.fourteencwf,0)),2) AS fourteencwf,<!-- 十四项_床位费 -->
        ROUND(AVG(IFNULL(f.fourteenzcf,0)),2) AS fourteenzcf,<!-- 十四项_诊查费 -->
        ROUND(AVG(IFNULL(f.fourteenjcf,0)),2) AS fourteenjcf,<!-- 十四项_检查费 -->
        ROUND(AVG(IFNULL(f.fourteenhyf,0)),2) AS fourteenhyf,<!-- 十四项_化验费 -->
        ROUND(AVG(IFNULL(f.fourteenzlf,0)),2) AS fourteenzlf,<!-- 十四项_治疗费 -->
        ROUND(AVG(IFNULL(f.fourteenssf,0)),2) AS fourteenssf,<!-- 十四项_手术费 -->
        ROUND(AVG(IFNULL(f.fourteenhlf,0)),2) AS fourteenhlf,<!-- 十四项_护理费 -->
        ROUND(AVG(IFNULL(f.fourteenwsclf,0)),2) AS fourteenwsclf,<!-- 十四项_卫生材料费 -->
        ROUND(AVG(IFNULL(f.fourteenxyf,0)),2) AS fourteenxyf,<!-- 十四项_西药费 -->
        ROUND(AVG(IFNULL(f.fourteenzyypf,0)),2) AS fourteenzyypf,<!-- 十四项_中药饮片费 -->
        ROUND(AVG(IFNULL(f.fourteenzcyf,0)),2) AS fourteenzcyf,<!-- 十四项_中成药费 -->
        ROUND(AVG(IFNULL(f.fourteenybzlf,0)),2) AS fourteenybzlf,<!-- 十四项_一般诊疗费 -->
        ROUND(AVG(IFNULL(f.fourteenghf,0)),2) AS fourteenghf,<!-- 十四项_挂号费 -->
        ROUND(AVG(IFNULL(f.fourteenqtf,0)),2) AS fourteenqtf<!-- 十四项_其他费 -->
        FROM som_dip_grp_info a
        LEFT JOIN som_hi_invy_bas_info b
        ON a.SETTLE_LIST_ID = b.ID
        <if test="enableSeAns != null and enableSeAns != '' and enableSeAns == 1">
            INNER JOIN som_setl_cas_crsp p
            ON b.K00 = p.K00
        </if>
        LEFT JOIN som_std_fee sc
        ON a.dip_codg = sc.`CODE`
        AND a.asst_list_dise_sev_deg = sc.asst_list_dise_sev_deg
        AND a.asst_list_age_grp = sc.asst_list_age_grp
        AND a.asst_list_tmor_sev_deg = sc.asst_list_tmor_sev_deg
        AND a.HOSPITAL_ID = sc.HOSPITAL_ID
        AND SUBSTR(a.dscg_time,1,4) = sc.STANDARD_YEAR
        AND sc.TYPE = 1
        LEFT JOIN som_dip_standard c
        ON a.dip_codg = c.dip_codg
--         AND a.is_used_asst_list = c.is_used_asst_list
--         AND a.asst_list_age_grp = c.asst_list_age_grp
--         AND a.asst_list_dise_sev_deg = c.asst_list_dise_sev_deg
--         AND a.asst_list_tmor_sev_deg = c.asst_list_tmor_sev_deg
        AND SUBSTR(a.dscg_time,1,4) = c.STANDARD_YEAR
        AND a.HOSPITAL_ID = c.HOSPITAL_ID
        LEFT JOIN som_std_fee d
        ON a.dip_codg = d.`CODE`
--         AND a.asst_list_age_grp = d.asst_list_age_grp
--         AND a.asst_list_dise_sev_deg = d.asst_list_dise_sev_deg
--         AND a.asst_list_tmor_sev_deg = d.asst_list_tmor_sev_deg
        AND SUBSTR(a.dscg_time,1,4) = d.STANDARD_YEAR
        AND d.TYPE = 1
        <choose>
            <when test="feeStas == 1">
                INNER JOIN
                (
                SELECT
                a.medcas_codg,
                a.adm_time,
                a.dip_codg,
                a.DIP_NAME,
                a.dscg_time,
                a.used_asst_list,
                a.asst_list_age_grp,
                a.asst_list_dise_sev_deg,
                a.asst_list_tmor_sev_deg,
                b.MED_TYPE,
                b.setl_pt_val,
                b.sumfee AS ipt_sumfee,
                b.dfr_fee AS ycCost
                FROM som_dip_grp_fbck a
                LEFT JOIN som_fund_dfr_fbck b
                ON a.rid_idt_codg = b.rid_idt_codg
                AND a.HOSPITAL_ID = b.HOSPITAL_ID
                WHERE 1=1
                <if test="begnDate != null and begnDate != '' and expiDate != null and expiDate != ''">
                    AND a.dscg_time BETWEEN #{begnDate,jdbcType=VARCHAR} AND concat(#{expiDate,jdbcType=VARCHAR},' 23:59:59')
                </if>
                <if test="inStartTime != null and inStartTime != '' and inEndTime != null and inEndTime != ''">
                    AND a.adm_time BETWEEN #{inStartTime} AND CONCAT(#{inEndTime},' 23:59:59')
                </if>
                <if test="seStartTime!=null and seStartTime!='' and seEndTime!=null and seEndTime!=''">
                    AND a.setl_time BETWEEN #{seStartTime} and CONCAT(#{seEndTime},' 23:59:59')
                </if>
                <include refid="com.my.som.common.mapper.CommonMapper.fbAuth" />
                ) e
                ON a.PATIENT_ID = e.medcas_codg
                AND SUBSTR(a.adm_time,1,10) = e.adm_time
                AND SUBSTR(a.dscg_time,1,10) = e.dscg_time
            </when>
        </choose>
        LEFT JOIN (
            SELECT
                hi_setl_invy_id,
                MAX(CASE WHEN med_chrg_itemname = '床位费' THEN amt ELSE 0 END) AS fourteencwf,
                MAX(CASE WHEN med_chrg_itemname = '诊查费' THEN amt ELSE 0 END) AS fourteenzcf,
                MAX(CASE WHEN med_chrg_itemname = '检查费' THEN amt ELSE 0 END) AS fourteenjcf,
                MAX(CASE WHEN med_chrg_itemname = '化验费' THEN amt ELSE 0 END) AS fourteenhyf,
                MAX(CASE WHEN med_chrg_itemname = '治疗费' THEN amt ELSE 0 END) AS fourteenzlf,
                MAX(CASE WHEN med_chrg_itemname = '手术费' THEN amt ELSE 0 END) AS fourteenssf,
                MAX(CASE WHEN med_chrg_itemname = '护理费' THEN amt ELSE 0 END) AS fourteenhlf,
                MAX(CASE WHEN med_chrg_itemname = '卫生材料费' THEN amt ELSE 0 END) AS fourteenwsclf,
                MAX(CASE WHEN med_chrg_itemname = '西药费' THEN amt ELSE 0 END) AS fourteenxyf,
                MAX(CASE WHEN med_chrg_itemname = '中药饮片费' THEN amt ELSE 0 END) AS fourteenzyypf,
                MAX(CASE WHEN med_chrg_itemname = '中成药费' THEN amt ELSE 0 END) AS fourteenzcyf,
                MAX(CASE WHEN med_chrg_itemname = '一般诊疗费' THEN amt ELSE 0 END) AS fourteenybzlf,
                MAX(CASE WHEN med_chrg_itemname = '挂号费' THEN amt ELSE 0 END) AS fourteenghf,
                MAX(CASE WHEN med_chrg_itemname = '其他费' THEN amt ELSE 0 END) AS fourteenqtf
            FROM som_hi_setl_invy_med_fee_info
            WHERE hi_setl_invy_id IN(
                SELECT SETTLE_LIST_ID AS hi_setl_invy_id
                FROM som_dip_grp_info
                <where>
                    <if test="begnDate != null and begnDate != '' and expiDate != null and expiDate != ''">
                        AND dscg_time BETWEEN #{begnDate,jdbcType=VARCHAR} AND concat(#{expiDate,jdbcType=VARCHAR},' 23:59:59')
                    </if>
                    <if test="inStartTime != null and inStartTime != '' and inEndTime != null and inEndTime != ''">
                        AND adm_time BETWEEN #{inStartTime} AND CONCAT(#{inEndTime},' 23:59:59')
                    </if>
                    <if test="seStartTime!=null and seStartTime!='' and seEndTime!=null and seEndTime!=''">
                        AND setl_end_time BETWEEN #{seStartTime} and CONCAT(#{seEndTime},' 23:59:59')
                    </if>
                    <if test="dipCodg != null and dipCodg != ''">
                        AND dip_codg = #{dipCodg,jdbcType=VARCHAR}
                    </if>
                </where>
                <!--
                 WHERE dscg_time BETWEEN #{begnDate,jdbcType=VARCHAR} AND CONCAT(#{expiDate,jdbcType=VARCHAR},' 23:59:59')
                 -->
                )
            GROUP BY hi_setl_invy_id
            ) f
        ON a.SETTLE_LIST_ID = f.hi_setl_invy_id

        WHERE a.grp_stas = 1
        <if test="begnDate != null and begnDate != '' and expiDate != null and expiDate != ''">
            AND a.dscg_time BETWEEN #{begnDate,jdbcType=VARCHAR} AND concat(#{expiDate,jdbcType=VARCHAR},' 23:59:59')
        </if>
        <if test="inStartTime != null and inStartTime != '' and inEndTime != null and inEndTime != ''">
            AND a.adm_time BETWEEN #{inStartTime} AND CONCAT(#{inEndTime},' 23:59:59')
        </if>
        <if test="seStartTime!=null and seStartTime!='' and seEndTime!=null and seEndTime!=''">
            AND a.setl_end_time BETWEEN #{seStartTime} and CONCAT(#{seEndTime},' 23:59:59')
        </if>
        <if test="dipCodg != null and dipCodg != ''">
            AND a.dip_codg = #{dipCodg,jdbcType=VARCHAR}
        </if>
        <if test='setlway != null and setlway != "" and setlway == "1"'>
            AND b.setlway = #{setlway,jdbcType=VARCHAR}
        </if>
        <!-- 通用查询条件 -->
        <if test="categories != null and categories != ''">
            <choose>
                <when test="categories == 1">
                    and b.A54 in ('1', '01', '310')
                </when>
                <when test="categories == 2">
                    and b.A54 in ('2', '02', '390')
                </when>
                <when test="categories == 9">
                    and b.A54 not in ('1', '01', '310', '2', '02', '390')
                </when>
            </choose>
        </if>
        <include refid="com.my.som.mapper.newBusiness.common.NewBusinessCommonAnalysisMapper.queryBusCommon" />
        GROUP BY
        <choose>
            <when test="feeStas == 0">
                a.dip_codg,a.DIP_NAME,a.asst_list_age_grp,a.asst_list_dise_sev_deg,a.asst_list_tmor_sev_deg
            </when>
            <when test="feeStas == 1">
                e.dip_codg,e.DIP_NAME
            </when>
        </choose>

    </select>

    <select id="queryDiseaseForecastData" resultType="com.my.som.vo.newBusiness.disease.NewBusinessDiseaseVo">
        SELECT a.* FROM ( SELECT a.dip_codg AS dipCodg,
               a.DIP_NAME AS dipName,
               count(1) AS drgInGroupMedcasVal,
               IFNULL(ROUND(SUM(a.ipt_sumfee),2),0) AS sumfee,
               IFNULL(ROUND(SUM(a.ycCost),2),0) AS forecastAmount,
        ROUND( IFNULL( SUM( a.profitloss ), 0 ) - IFNULL( SUM(  a.pre_hosp_examfee ), 0 ),2 ) AS forecastAmountDiff,
               IFNULL(ROUND(IFNULL(SUM(a.ipt_sumfee),0) / NULLIF(SUM(a.ycCost),0),2),0) AS oeVal
        FROM
            (
                SELECT a.dip_codg,
                       a.DIP_NAME,
                       a.dscg_caty_codg_inhosp,
                       a.ipdr_code,
                       a.ipdr_name,
                       a.`NAME`,
                       a.PATIENT_ID,
                       a.SETTLE_LIST_ID,
                       a.ipt_sumfee,
                       a.drugfee,
                       a.mcs_fee,
                       b.dise_type,
                       a.pre_hosp_examfee,
                       b.ycCost,
                        b.profitloss
                FROM som_dip_grp_info a
                LEFT JOIN som_hi_invy_bas_info f
                ON a.SETTLE_LIST_ID = f.ID
                <if test="enableSeAns != null and enableSeAns != '' and enableSeAns == 1">
                    INNER JOIN som_setl_cas_crsp p
                    ON f.K00 = p.K00
                </if>
                         LEFT JOIN
                     (
                        SELECT
                        b.SETTLE_LIST_ID,
                        b.dise_type,
                        b.price,b.forecast_fee AS ycCost,
                        b.profitloss AS profitloss,
                        b.sumfee AS sumfee
                        FROM
                        som_dip_sco b
                        ) b
                     ON a.SETTLE_LIST_ID = b.SETTLE_LIST_ID
                WHERE a.dip_codg IS NOT NULL AND a.grp_stas = 1
                    <if test="begnDate != null and begnDate != '' and expiDate != null and expiDate != ''">
                        AND a.dscg_time BETWEEN #{begnDate,jdbcType=VARCHAR} AND concat(#{expiDate,jdbcType=VARCHAR},' 23:59:59')
                    </if>
                    <if test="inStartTime != null and inStartTime != '' and inEndTime != null and inEndTime != ''">
                        AND a.adm_time BETWEEN #{inStartTime} AND CONCAT(#{inEndTime},' 23:59:59')
                    </if>
                    <if test="seStartTime!=null and seStartTime!='' and seEndTime!=null and seEndTime!=''">
                        AND a.setl_end_time BETWEEN #{seStartTime} and CONCAT(#{seEndTime},' 23:59:59')
                    </if>
                    <if test='setlway != null and setlway != "" and setlway == "1"'>
                        AND f.setlway = #{setlway,jdbcType=VARCHAR}
                    </if>
                    <if test="categories != null and categories != ''">
                        <choose>
                            <when test="categories == 1">
                                and f.A54 in ('1', '01', '310')
                            </when>
                            <when test="categories == 2">
                                and f.A54 in ('2', '02', '390')
                            </when>
                            <when test="categories == 9">
                                and f.A54 not in ('1', '01', '310', '2', '02', '390')
                            </when>
                        </choose>
                    </if>
                <!-- 通用查询条件 -->
                <include refid="com.my.som.mapper.newBusiness.common.NewBusinessCommonAnalysisMapper.queryBusCommon" />
            ) a
        LEFT JOIN som_dip_grp_rcd b
        ON a.SETTLE_LIST_ID = b.SETTLE_LIST_ID
        <if test="dipCodg != null and dipCodg != ''">
            WHERE a.dip_codg = #{dipCodg,jdbcType=VARCHAR}
        </if>
        GROUP BY a.dip_codg,a.DIP_NAME) a
<!--        <where>-->
<!--            <if test="isLossType != null and isLossType != ''">-->
<!--                <choose>-->
<!--                    <when test="isLossType == 1">-->
<!--                        <![CDATA[-->
<!--                            a.forecastAmountDiff < 0-->
<!--                        ]]>-->
<!--                    </when>-->
<!--                    <when test="isLossType == 0">-->
<!--                        <![CDATA[-->
<!--                            a.forecastAmountDiff >= 0-->
<!--                        ]]>-->
<!--                    </when>-->
<!--                </choose>-->
<!--            </if>-->
<!--        </where>-->
    </select>

</mapper>
