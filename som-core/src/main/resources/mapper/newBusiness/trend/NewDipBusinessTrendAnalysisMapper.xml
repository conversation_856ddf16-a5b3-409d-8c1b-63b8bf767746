<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.som.mapper.newBusiness.trend.NewDipBusinessTrendAnalysisMapper">
    <select id="queryData" resultType="com.my.som.vo.newBusiness.trend.NewBusinessTrendAnalysisVo">
        SELECT SUBSTR(a.dscg_time,1,7) AS month,
--                ROUND(COUNT(CASE WHEN b.is_in_group = 1 THEN 1 ELSE NULL END)/COUNT(1) * 100,2) AS inGroupRate,
--                COUNT(CASE WHEN b.MED_TYPE = 1 THEN 1 ELSE NULL END) AS upMedicalNum,
--                COUNT(CASE WHEN b.MED_TYPE = 2 THEN 1 ELSE NULL END) AS lowMedicalNum,
--                COUNT(CASE WHEN b.MED_TYPE = 3 THEN 1 ELSE NULL END) AS ZCMedicalNum,
--                COUNT(CASE WHEN b.MED_TYPE = 4 THEN 1 ELSE NULL END) AS otherMedicalNum,
--                COUNT(1) as medcasVal,
--                ROUND(SUM(CASE WHEN b.MED_TYPE = 1 THEN b.dfr_fee - b.sumfee ELSE 0 END)/10000,2) AS upProfitLoss,
--                ROUND(SUM(CASE WHEN b.MED_TYPE = 2 THEN b.dfr_fee - b.sumfee ELSE 0 END)/10000,2) AS lowProfitLoss,
--                ROUND(SUM(CASE WHEN b.MED_TYPE = 3 THEN b.dfr_fee - b.sumfee ELSE 0 END)/10000,2) AS ZCProfitLoss,
--                ROUND(SUM(CASE WHEN b.MED_TYPE = 4 THEN b.dfr_fee - b.sumfee ELSE 0 END)/10000,2) AS otherProfitLoss,
--                ROUND(SUM(b.dfr_fee - b.sumfee)/10000,2) as profitLoss
                ROUND(COUNT(CASE WHEN a.grp_stas  = '1' THEN 1 ELSE NULL END)/COUNT(1) * 100,2) AS inGroupRate,
                COUNT(CASE WHEN b.dise_type = '3' THEN 1 ELSE NULL END) AS upMedicalNum,
                COUNT(CASE WHEN b.dise_type = '2' THEN 1 ELSE NULL END) AS lowMedicalNum,
                COUNT(CASE WHEN b.dise_type = '1' THEN 1 ELSE NULL END) AS ZCMedicalNum,
                COUNT(CASE WHEN b.dise_type NOT IN ('3','2','1') THEN 1 ELSE NULL END) AS otherMedicalNum,
                COUNT(1) as medcasVal,
                ROUND(SUM(CASE WHEN b.dise_type = '3' THEN b.ycCost - a.ipt_sumfee  ELSE 0 END)/10000,2) AS upProfitLoss,
                ROUND(SUM(CASE WHEN b.dise_type = '2' THEN b.ycCost - a.ipt_sumfee ELSE 0 END)/10000,2) AS lowProfitLoss,
                ROUND(SUM(CASE WHEN b.dise_type = '1' THEN b.ycCost - a.ipt_sumfee ELSE 0 END)/10000,2) AS ZCProfitLoss,
                ROUND(SUM(CASE WHEN b.dise_type NOT IN ('3','2','1') THEN b.ycCost - a.ipt_sumfee ELSE 0 END)/10000,2) AS otherProfitLoss,
                ROUND(SUM(b.profitloss )/10000,2) as profitLoss
        FROM som_dip_grp_info a
--         INNER JOIN
--             (
--                 SELECT a.medcas_codg,
--                        a.adm_time,
--                        a.dscg_time,
--                        b.MED_TYPE,
--                        b.dfr_fee,
--                        a.is_in_group,
--                        b.sumfee
--                 FROM som_dip_grp_fbck a
--                 LEFT JOIN som_fund_dfr_fbck b
--                 ON a.rid_idt_codg = b.rid_idt_codg
--             ) b
--         ON a.PATIENT_ID = b.medcas_codg
--         AND SUBSTR(a.adm_time, 1, 10) = b.adm_time
--         AND SUBSTR(a.dscg_time, 1, 10) = b.dscg_time
        LEFT JOIN (
        SELECT b.SETTLE_LIST_ID,
        case
        when b.dise_type = '1' then '3'
        when b.dise_type = '2' then '2'
        when b.dise_type = '3' then '1'
        else b.dise_type end as dise_type,
        b.forecast_fee AS ycCost,
        b.profitloss AS profitloss
        FROM som_dip_sco b

        )b  ON a.SETTLE_LIST_ID = b.SETTLE_LIST_ID
        LEFT JOIN som_dept c
        ON a.dscg_caty_codg_inhosp = c.`CODE`
        WHERE
        <![CDATA[
            SUBSTR(a.dscg_time,1,4) = #{year}
        ]]>
        <!-- 通用查询条件 -->
        <include refid="com.my.som.mapper.newBusiness.common.NewBusinessCommonAnalysisMapper.queryBusCommon" />
        GROUP BY SUBSTR(a.dscg_time,1,7)
        ORDER BY SUBSTR(a.dscg_time,1,7)
    </select>
</mapper>
