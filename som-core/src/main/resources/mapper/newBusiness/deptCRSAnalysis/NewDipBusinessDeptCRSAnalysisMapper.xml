<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.som.mapper.newBusiness.deptCRSAnalysis.NewDipBusinessDeptCRSAnalysisMapper">
    <!-- 查询科室数据 -->
    <select id="queryDeptCRSData" resultType="com.my.som.vo.newBusiness.deptCRSAnalysis.NewDipBusinessDeptCRSAnalysisVo">
        SELECT a.dscg_caty_codg_inhosp AS deptCode,
               b.`NAME` AS deptName,
               IFNULL(a.totalNum,0) AS totalNum,
               ROUND(IFNULL((a.toalScore/a.totalNum)/100,0),2) AS cmi,
               ROUND(IFNULL(a.yccost-a.sumfee,0),2) AS balanceCost
        FROM
             (
                SELECT dscg_caty_codg_inhosp,
                       COUNT(1) AS totalNum,
                       SUM(IFNULL(b.refer_sco,0)) AS toalScore,
                       SUM(IFNULL(a.ipt_sumfee,0)) as sumfee,
                       SUM( IFNULL(d.forecast_fee,0) ) AS yccost
                    FROM som_dip_grp_info a
                    LEFT JOIN som_dip_standard b
                        ON a.dip_codg = b.dip_codg
                       AND a.is_used_asst_list = b.is_used_asst_list
                       AND a.asst_list_age_grp = b.asst_list_age_grp
                       AND a.asst_list_dise_sev_deg = b.asst_list_dise_sev_deg
                       AND a.asst_list_tmor_sev_deg = b.asst_list_tmor_sev_deg
                       AND SUBSTR(a.dscg_time,1,4) = b.STANDARD_YEAR
                       AND a.HOSPITAL_ID = b.HOSPITAL_ID
                       AND b.ACTIVE_FLAG = 1
                    LEFT JOIN som_hi_invy_bas_info c
                        ON a.SETTLE_LIST_ID = c.ID
                    LEFT JOIN som_dip_sco d
                        ON a.SETTLE_LIST_ID = d.SETTLE_LIST_ID

                    WHERE a.grp_stas = 1
                      AND a.dip_codg NOT IN ( SELECT dip_codg FROM som_dip_grp )
                      <if test="begnDate != null and begnDate != '' and expiDate != null and expiDate != ''">
                          AND a.dscg_time BETWEEN #{ begnDate, jdbcType=VARCHAR } AND CONCAT(#{ expiDate, jdbcType=VARCHAR },' 23:59:59')
                      </if>
                      <if test="hospitalId != null and hospitalId != ''">
                          AND a.HOSPITAL_ID = #{ hospitalId, jdbcType=VARCHAR }
                      </if>
                    GROUP BY a.dscg_caty_codg_inhosp
             ) a
        LEFT JOIN som_dept b
            ON a.dscg_caty_codg_inhosp = b.`CODE`
    </select>
</mapper>