<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.som.mapper.newBusiness.customPatientAnalysis.NewDrgBusinessCustomPatientAnalysisMapper">

    <!-- 查询患者基本信息 -->
    <select id="queryDrgPatientBasicInfoData"  resultType="com.my.som.vo.newBusiness.customPatient.NewBusinessCustomPatientVo">
        SELECT a.SETTLE_LIST_ID AS settleListId,
               a.`NAME` AS name,
               a.act_ipt AS inHosDays,
               a.ipdr_code AS drCodg,
               a.ipdr_name AS drName,
               a.dscg_caty_codg_inhosp AS deptCode,
               d.`NAME` AS deptName,
               b.C04N AS mainDiag,
               b.C15x01N AS majorSurgery,
               c.grp_fale_rea AS grpFaleRea,
               a.PATIENT_ID AS bah,
               a.adm_time AS inHosTime,
               a.dscg_time AS outHosTime,


        <choose>
            <when test="feeStas == 0">

                a.ipt_sumfee AS inHosTotalCost,
                a.drg_codg AS drgCodg,
                a.DRG_NAME AS drgName,
                IFNULL(ROUND(IFNULL(a.drugfee,0),2),0) AS medicalCost,
                IFNULL(ROUND(IFNULL(a.drugfee,0)/NULLIF(a.ipt_sumfee,0)*100,2),0) AS medicalCostRate,
                IFNULL(ROUND(IFNULL(a.mcs_fee,0),2),0) AS materialCost,
                IFNULL(ROUND(IFNULL(a.mcs_fee,0)/NULLIF(a.ipt_sumfee,0)*100,2),0) AS materialCostRate
           </when>
            <when test="feeStas == 1">
                e.ipt_sumfee AS inHosTotalCost,
                e.drg_codg AS drgCodg,
                e.DRG_NAME AS drgName,
                IFNULL(ROUND(IFNULL(a.drugfee,0),2),0) AS medicalCost,
                IFNULL(ROUND(IFNULL(a.drugfee,0)/NULLIF(e.ipt_sumfee,0)*100,2),0) AS medicalCostRate,
                IFNULL(ROUND(IFNULL(a.mcs_fee,0),2),0) AS materialCost,
                IFNULL(ROUND(IFNULL(a.mcs_fee,0)/NULLIF(e.ipt_sumfee,0)*100,2),0) AS materialCostRate
            </when>
        </choose>
        FROM som_drg_grp_info a
        LEFT JOIN som_hi_invy_bas_info b
        ON a.SETTLE_LIST_ID = b.ID
        AND a.HOSPITAL_ID = b.HOSPITAL_ID
        LEFT JOIN som_drg_grp_rcd c
        ON a.SETTLE_LIST_ID = c.SETTLE_LIST_ID
        AND a.HOSPITAL_ID = c.HOSPITAL_ID
        LEFT JOIN som_dept d
        ON a.dscg_caty_codg_inhosp = d.`CODE`
        AND a.HOSPITAL_ID = d.HOSPITAL_ID
        <if test="enableSeAns != null and enableSeAns != '' and enableSeAns == 1">
            INNER JOIN som_hi_invy_bas_info f
                ON a.SETTLE_LIST_ID = f.ID
            INNER JOIN som_setl_cas_crsp p
                ON f.K00 = p.K00
        </if>
        <choose>
            <when test="feeStas == 1">
                INNER JOIN
                (
                SELECT
                a.medcas_codg,
                a.adm_time,
                a.drg_codg,
                a.DRG_NAME,
                a.dscg_time,
                a.is_in_group,
                b.sumfee AS ipt_sumfee,
                b.dfr_fee AS ycCost
                FROM som_drg_grp_fbck a
                LEFT JOIN som_drg_pt_val_pay b
                ON a.rid_idt_codg = b.rid_idt_codg
                AND a.HOSPITAL_ID = b.HOSPITAL_ID
                <!--<![CDATA[
                            WHERE a.dscg_time >= #{begnDate} AND a.dscg_time <= #{expiDate}
                            ]]>-->
                <where>
                    <if test="inStartTime!=null and inStartTime!='' and inEndTime!=null and inEndTime!=''">
                        AND a.adm_time BETWEEN #{inStartTime,jdbcType=VARCHAR} and CONCAT(#{inEndTime,jdbcType=VARCHAR},' 23:59:59')
                    </if>
                    <if test="begnDate!=null and begnDate!='' and expiDate!=null and expiDate!=''">
                        AND a.dscg_time BETWEEN #{begnDate,jdbcType=VARCHAR} and CONCAT(#{expiDate,jdbcType=VARCHAR},' 23:59:59')
                    </if>
                    <if test="seStartTime!=null and seStartTime!='' and seEndTime!=null and seEndTime!=''">
                        AND a.setl_time BETWEEN #{seStartTime,jdbcType=VARCHAR} and CONCAT(#{seStartTime,jdbcType=VARCHAR},' 23:59:59')
                    </if>
                </where>
                <include refid="com.my.som.common.mapper.CommonMapper.fbAuth" />
                ) e
                ON a.PATIENT_ID = e.medcas_codg
                AND SUBSTR(a.adm_time,1,10) = e.adm_time
                AND SUBSTR(a.dscg_time,1,10) = e.dscg_time
            </when>
        </choose>
        WHERE 1 = 1
        <if test="inStartTime!=null and inStartTime!='' and
                inEndTime!=null and inEndTime!=''">
            AND a.adm_time BETWEEN #{inStartTime,jdbcType=VARCHAR} and CONCAT(#{inEndTime,jdbcType=VARCHAR},' 23:59:59')
        </if>
        <if test="begnDate!=null and begnDate!='' and
            expiDate!=null and expiDate!=''">
            AND a.dscg_time BETWEEN #{begnDate,jdbcType=VARCHAR} and CONCAT(#{expiDate,jdbcType=VARCHAR},' 23:59:59')
        </if>
        <if test="seStartTime!=null and seStartTime!='' and
            seEndTime!=null and seEndTime!=''">
            AND a.setl_end_time BETWEEN #{seStartTime,jdbcType=VARCHAR} and CONCAT(#{seEndTime,jdbcType=VARCHAR},' 23:59:59')
        </if>
        <!--a.dscg_time BETWEEN #{begnDate,jdbcType=VARCHAR} AND CONCAT(#{expiDate,jdbcType=VARCHAR},' 23:59:59')
        <if test="inStartTime != null and inStartTime != '' and inEndTime != null and inEndTime != ''">
            AND a.adm_time BETWEEN #{inStartTime} AND CONCAT(#{inEndTime},' 23:59:59')
        </if>-->
        <if test="grpFlag != null and grpFlag != ''">
            <choose>
                <when test="grpFlag == 1">
                    AND c.grp_fale_rea IS NOT NULL
                </when>
            </choose>
        </if>
        <if test="errorReason != null and errorReason.size() > 0">
            AND (
            <foreach collection="errorReason" item="item" index="index" separator=" OR ">
                c.grp_fale_rea LIKE #{item}
            </foreach>
            )
        </if>
        <if test="patientIdList !=null and patientIdList.size()>0" >
            AND
            a.PATIENT_ID in
            <foreach collection="patientIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="drCodg != null and drCodg != ''">
           AND a.ipdr_code = #{drCodg}
        </if>
        <if test="icdCodg != null and icdCodg != ''">
            AND a.main_diag_dise_codg = #{icdCodg}
        </if>
        <include refid="queryCriteria" />
        ORDER BY a.dscg_time
    </select>

    <select id="queryDrgPatientCostInfoData2"  resultType="com.my.som.vo.newBusiness.customPatient.NewBusinessCustomPatientVo">
    SELECT a.*
      FROM(
        SELECT a.SETTLE_LIST_ID AS settleListId,
               a.`NAME` AS name,
               a.dscg_caty_codg_inhosp AS deptCode,
               c.`NAME` AS deptName,
               a.PATIENT_ID AS bah,
               a.adm_time AS inHosTime,
               a.dscg_time AS outHosTime,
               a.main_diag_dise_codg,
            <choose>
               <when test="feeStas == 0">
               a.ipt_sumfee AS inHosTotalCost,
               IFNULL(ROUND(e.ycCost,2),0) AS forecastAmount,
               IFNULL(ROUND(IFNULL(e.profitloss,0) ,2),0) AS forecastAmountDiff,
               a.drg_codg AS drgCodg,
               a.DRG_NAME AS drgName,
               e.dise_type AS stsbFee,
               </when>
               <when test="feeStas == 1">
               e.ipt_sumfee AS inHosTotalCost,
               IFNULL(ROUND(e.ycCost,2),0) AS forecastAmount,
               IFNULL(ROUND(IFNULL(e.ycCost,0) - IFNULL(e.ipt_sumfee,0),2),0) AS forecastAmountDiff,
               e.drg_codg AS drgCodg,
               e.DRG_NAME AS drgName,
               e.dise_type AS stsbFee,
               </when>
            </choose>

        IFNULL(IFNULL(b.D11,0)+IFNULL(b.D12,0)+IFNULL(b.D13,0)+IFNULL(b.D14,0),0) AS com_med_servfee, <!-- 综合医疗服务费 -->
        IFNULL(b.D11,0) AS ordn_med_servfee, <!-- 一般医疗服务费 -->
        IFNULL(b.D12,0) AS ordn_trt_oprt_fee, <!-- 一般治疗操作费 -->
        IFNULL(b.D13,0) AS nursfee, <!-- 护理费 -->
        IFNULL(b.D14,0) AS zhylfwqtf, <!-- 综合医疗服务其他费 -->
        IFNULL(IFNULL(b.D15,0)+IFNULL(b.D16,0)+IFNULL(b.D17,0)+IFNULL(b.D18,0),0) AS diag_fee, <!-- 诊断费 -->
        IFNULL(b.D15,0) AS cas_diag_fee, <!-- 病理诊断费 -->
        IFNULL(b.D16,0) AS lab_diag_fee, <!-- 实验室诊断费 -->
        IFNULL(b.D17,0) AS rdhy_diag_fee, <!-- 影像学诊断费 -->
        IFNULL(b.D18,0) AS clnc_diag_item_fee, <!-- 临床诊断项目费 -->
        IFNULL(IFNULL(b.D19,0)+IFNULL(b.D20,0),0) AS treat_fee, <!-- 治疗费 -->
        IFNULL(b.D19,0) AS nsrgtrt_item_fee, <!-- 非手术治疗项目费 -->
        IFNULL(b.D20,0) AS oprn_treat_fee, <!-- 手术治疗费 -->
        IFNULL(b.D21,0) AS rhab_fee, <!-- 康复费 -->
        IFNULL(b.D22,0) AS tcmdrug_fee, <!-- 中医费 -->
        IFNULL(b.D23,0) AS west_fee, <!-- 西药费 -->
        IFNULL(IFNULL(b.D24,0)+IFNULL(b.D25,0),0) AS zyf1, <!-- 中药费 -->
        IFNULL(b.D24,0) AS tcmpat_fee, <!-- 中成药费 -->
        IFNULL(b.D25,0) AS tcmherb, <!-- 中草药费-->
        IFNULL(IFNULL(b.D26,0)+IFNULL(b.D27,0)+IFNULL(b.D28,0)+IFNULL(b.D29,0)+IFNULL(b.D30,0),0) AS blood_blo_pro_fee, <!-- 血液和血液制品费 -->
        IFNULL(b.D26,0) AS blo_fee, <!-- 血费 -->
        IFNULL(b.D27,0) AS bdblzpf, <!-- 白蛋白类制品费 -->
        IFNULL(b.D28,0) AS qdblzpf, <!-- 球蛋白类制品费 -->
        IFNULL(b.D29,0) AS nxyzlzpf, <!-- 凝血因子类制品费 -->
        IFNULL(b.D30,0) AS xbyzlzpf, <!-- 细胞因子类制品费 -->
        IFNULL(IFNULL(b.D31,0)+IFNULL(b.D32,0)+IFNULL(b.D33,0),0) AS mcs_fee, <!-- 耗材费 -->
        IFNULL(b.D31,0) AS jcyycxyyclf, <!-- 检查用一次性医用材料费 -->
        IFNULL(b.D32,0) AS trt_use_dspo_med_matlfee, <!-- 治疗用一次性医用材料费 -->
        IFNULL(b.D33,0) AS oprn_use_dspo_med_matlfee, <!-- 手术用一次性医用材料费 -->
        IFNULL(b.D34,0) AS oth_fee, <!-- 其他费 -->
        IFNULL(d.avg_fee,0) AS ljfy,<!-- 例均费用 -->
        IFNULL(d.com_med_servfee,0) AS bgzhylfwf, <!-- 标杆综合医疗服务费 -->
        IFNULL(d.ordn_med_servfee,0) AS bgybylfwf, <!-- 标杆一般医疗服务费 -->
        IFNULL(d.ordn_trt_oprt_fee,0) AS bgybzlczf, <!-- 标杆一般治疗操作费 -->
        IFNULL(d.nursfee,0) AS bghlf, <!-- 标杆护理费 -->
        IFNULL(d.oth_fee_com,0) AS bgzhylfwqtf, <!-- 标杆综合医疗服务其他费 -->
        IFNULL(d.diag_fee,0) AS bgzdf, <!-- 标杆诊断费 -->
        IFNULL(d.cas_diag_fee,0) AS bgblzdf, <!-- 标杆病理诊断费 -->
        IFNULL(d.lab_diag_fee,0) AS bgsyszdf, <!-- 标杆实验室诊断费 -->
        IFNULL(d.rdhy_diag_fee,0) AS bgyxxzdf, <!-- 标杆影像学诊断费 -->
        IFNULL(d.clnc_diag_item_fee,0) AS bglczdxmf, <!-- 标杆临床诊断项目费 -->
        IFNULL(d.treat_fee,0) AS bgzlf, <!-- 标杆治疗费 -->
        IFNULL(d.nsrgtrt_item_fee,0) AS bgfsszlxmf, <!-- 标杆非手术治疗项目费 -->
        IFNULL(d.oprn_treat_fee,0) AS bgsszlf, <!-- 标杆手术治疗费 -->
        IFNULL(d.rhab_fee,0) AS bgkff, <!-- 标杆康复费 -->
        IFNULL(d.tcm_treat_fee,0) AS bgzyf, <!-- 标杆中医费 -->
        IFNULL(d.west_fee,0) AS bgxyf, <!-- 标杆西药费 -->
        IFNULL(d.tcmdrug_fee,0) AS bgzyf1, <!-- 标杆中药费 -->
        IFNULL(d.tcmpat_fee,0) AS bgzcyf, <!-- 标杆中成药费 -->
        IFNULL(d.tcmherb,0) AS bgzcyf1, <!-- 标杆中草药费 -->
        IFNULL(d.blood_blo_pro_fee,0) AS bgxyhxyzpf, <!-- 标杆血液和血液制品费 -->
        IFNULL(d.blo_fee,0) AS bgxf, <!-- 标杆血费 -->
        IFNULL(d.albu_clss_prod_fee,0) AS bgbdblzpf, <!-- 标杆白蛋白类制品费 -->
        IFNULL(d.glon_clss_prod_fee,0) AS bgqdblzpf, <!-- 标杆球蛋白类制品费 -->
        IFNULL(d.clotfac_clss_prod_fee,0) AS bgnxyzlzpf, <!-- 标杆凝血因子类制品费 -->
        IFNULL(d.cyki_clss_prod_fee,0) AS bgxbyzlzpf, <!-- 标杆细胞因子类制品费 -->
        IFNULL(d.mcs_fee,0) AS bghcf, <!-- 标杆耗材费 -->
        IFNULL(d.exam_use_dspo_med_matlfee,0) AS bgjcyycxyyclf, <!-- 标杆检查用一次性医用材料费 -->
        IFNULL(d.trt_use_dspo_med_matlfee,0) AS bgzlyycxyyclf, <!-- 标杆治疗用一次性医用材料费 -->
        IFNULL(d.oprn_use_dspo_med_matlfee,0) AS bgssyycxyyclf, <!-- 标杆手术用一次性医用材料费 -->
        IFNULL(d.oth_fee,0) AS bgqtf,<!-- 标杆其他费 -->
        <!-- 十四项_标杆 -->
        IFNULL(d.medi_fee_type_bedfee,0) AS bgfourteencwf,<!-- 十四项_床位费 -->
        IFNULL(d.medi_fee_type_diag_fee,0) AS bgfourteenzcf,<!-- 十四项_诊查费 -->
        IFNULL(d.medi_fee_type_examfee,0) AS bgfourteenjcf,<!-- 十四项_检查费 -->
        IFNULL(d.medi_fee_type_asy_fee,0) AS bgfourteenhyf,<!-- 十四项_化验费 -->
        IFNULL(d.medi_fee_type_treat_fee,0) AS bgfourteenzlf,<!-- 十四项_治疗费 -->
        IFNULL(d.medi_fee_type_oper_fee,0) AS bgfourteenssf,<!-- 十四项_手术费 -->
        IFNULL(d.medi_fee_type_nursfee,0) AS bgfourteenhlf,<!-- 十四项_护理费 -->
        IFNULL(d.medi_fee_type_hc_matlfee,0) AS bgfourteenwsclf,<!-- 十四项_卫生材料费 -->
        IFNULL(d.medi_fee_type_west_fee,0) AS bgfourteenxyf,<!-- 十四项_西药费 -->
        IFNULL(d.medi_fee_type_tmdp_fee,0) AS bgfourteenzyypf,<!-- 十四项_中药饮片费 -->
        IFNULL(d.medi_fee_type_tcmpat_fee,0) AS bgfourteenzcyf,<!-- 十四项_中成药费 -->
        IFNULL(d.medi_fee_type_ordn_trtfee,0) AS bgfourteenybzlf,<!-- 十四项_一般诊疗费 -->
        IFNULL(d.medi_fee_type_regfee,0) AS bgfourteenghf,<!-- 十四项_挂号费 -->
        IFNULL(d.medi_fee_type_oth_fee,0) AS bgfourteenqtf,<!-- 十四项_其他费 -->
        <!-- 十四项_患者 -->
        f.fourteencwf,<!-- 十四项_床位费 -->
        f.fourteenzcf,<!-- 十四项_诊查费 -->
        f.fourteenjcf,<!-- 十四项_检查费 -->
        f.fourteenhyf,<!-- 十四项_化验费 -->
        f.fourteenzlf,<!-- 十四项_治疗费 -->
        f.fourteenssf,<!-- 十四项_手术费 -->
        f.fourteenhlf,<!-- 十四项_护理费 -->
        f.fourteenwsclf,<!-- 十四项_卫生材料费 -->
        f.fourteenxyf,<!-- 十四项_西药费 -->
        f.fourteenzyypf,<!-- 十四项_中药饮片费 -->
        f.fourteenzcyf,<!-- 十四项_中成药费 -->
        f.fourteenybzlf,<!-- 十四项_一般诊疗费 -->
        f.fourteenghf,<!-- 十四项_挂号费 -->
        f.fourteenqtf<!-- 十四项_其他费 -->
        FROM som_drg_grp_info a
        LEFT JOIN som_hi_invy_bas_info b
            ON a.SETTLE_LIST_ID = b.ID
           AND a.HOSPITAL_ID = b.HOSPITAL_ID
        LEFT JOIN som_dept c
            ON a.dscg_caty_codg_inhosp = c.`CODE`
           AND a.HOSPITAL_ID = c.HOSPITAL_ID
        LEFT JOIN std_cost_dy_sj d
            ON a.drg_codg = d.`CODE`
           AND SUBSTR(a.dscg_time,1,4) = d.STANDARD_YEAR
           AND d.TYPE = 3
        <if test="enableSeAns != null and enableSeAns != '' and enableSeAns == 1">
            INNER JOIN som_hi_invy_bas_info f
            ON a.SETTLE_LIST_ID = f.ID
            INNER JOIN som_setl_cas_crsp p
            ON f.K00 = p.K00
        </if>
        <choose>
            <when test="feeStas == 0">
            LEFT JOIN
                (
                SELECT b.SETTLE_LIST_ID,
                b.dise_type,
                b.forecast_fee AS ycCost,
                b.profitloss AS profitloss
                FROM som_drg_sco b
                ) e
                ON a.SETTLE_LIST_ID = e.SETTLE_LIST_ID
            </when>
            <when test="feeStas == 1">
                INNER JOIN
                (
                SELECT
                a.medcas_codg,
                a.adm_time,
                a.drg_codg,
                a.DRG_NAME,
                a.dscg_time,
                b.sumfee AS ipt_sumfee,
                b.dfr_fee AS ycCost
                b.INSURED_TYPE AS dise_type
                FROM som_drg_grp_fbck a
                LEFT JOIN som_drg_pt_val_pay b
                ON a.rid_idt_codg = b.rid_idt_codg
                AND a.HOSPITAL_ID = b.HOSPITAL_ID
                <!--<![CDATA[
                    WHERE a.dscg_time >= #{begnDate} AND a.dscg_time <= #{expiDate}
                    ]]>-->
                <where>
                    <if test="inStartTime!=null and inStartTime!='' and inEndTime!=null and inEndTime!=''">
                        AND a.adm_time BETWEEN #{inStartTime,jdbcType=VARCHAR} and CONCAT(#{inEndTime,jdbcType=VARCHAR},' 23:59:59')
                    </if>
                    <if test="begnDate!=null and begnDate!='' and expiDate!=null and expiDate!=''">
                        AND a.dscg_time BETWEEN #{begnDate,jdbcType=VARCHAR} and CONCAT(#{expiDate,jdbcType=VARCHAR},' 23:59:59')
                    </if>
                    <if test="seStartTime!=null and seStartTime!='' and seEndTime!=null and seEndTime!=''">
                        AND a.setl_time BETWEEN #{seStartTime,jdbcType=VARCHAR} and CONCAT(#{seStartTime,jdbcType=VARCHAR},' 23:59:59')
                    </if>
                </where>
                <include refid="com.my.som.common.mapper.CommonMapper.fbAuth" />
                ) e
                ON a.PATIENT_ID = e.medcas_codg
                AND SUBSTR(a.adm_time,1,10) = e.adm_time
                AND SUBSTR(a.dscg_time,1,10) = e.dscg_time
            </when>
        </choose>
        LEFT JOIN (
            SELECT
                    hi_setl_invy_id,
                    MAX(CASE WHEN med_chrg_itemname = '床位费' THEN amt ELSE 0 END) AS fourteencwf,
                    MAX(CASE WHEN med_chrg_itemname = '诊查费' THEN amt ELSE 0 END) AS fourteenzcf,
                    MAX(CASE WHEN med_chrg_itemname = '检查费' THEN amt ELSE 0 END) AS fourteenjcf,
                    MAX(CASE WHEN med_chrg_itemname = '化验费' THEN amt ELSE 0 END) AS fourteenhyf,
                    MAX(CASE WHEN med_chrg_itemname = '治疗费' THEN amt ELSE 0 END) AS fourteenzlf,
                    MAX(CASE WHEN med_chrg_itemname = '手术费' THEN amt ELSE 0 END) AS fourteenssf,
                    MAX(CASE WHEN med_chrg_itemname = '护理费' THEN amt ELSE 0 END) AS fourteenhlf,
                    MAX(CASE WHEN med_chrg_itemname = '卫生材料费' THEN amt ELSE 0 END) AS fourteenwsclf,
                    MAX(CASE WHEN med_chrg_itemname = '西药费' THEN amt ELSE 0 END) AS fourteenxyf,
                    MAX(CASE WHEN med_chrg_itemname = '中药饮片费' THEN amt ELSE 0 END) AS fourteenzyypf,
                    MAX(CASE WHEN med_chrg_itemname = '中成药费' THEN amt ELSE 0 END) AS fourteenzcyf,
                    MAX(CASE WHEN med_chrg_itemname = '一般诊疗费' THEN amt ELSE 0 END) AS fourteenybzlf,
                    MAX(CASE WHEN med_chrg_itemname = '挂号费' THEN amt ELSE 0 END) AS fourteenghf,
                    MAX(CASE WHEN med_chrg_itemname = '其他费' THEN amt ELSE 0 END) AS fourteenqtf
            FROM som_hi_setl_invy_med_fee_info
            WHERE hi_setl_invy_id IN(
                    SELECT SETTLE_LIST_ID AS hi_setl_invy_id
                    FROM som_drg_grp_info
                    <!--
                        WHERE dscg_time BETWEEN #{begnDate,jdbcType=VARCHAR} AND CONCAT(#{expiDate,jdbcType=VARCHAR},' 23:59:59')
                     -->
                    <where>
                        <if test="inStartTime!=null and inStartTime!='' and inEndTime!=null and inEndTime!=''">
                            AND adm_time BETWEEN #{inStartTime,jdbcType=VARCHAR} and CONCAT(#{inEndTime,jdbcType=VARCHAR},' 23:59:59')
                        </if>
                        <if test="begnDate!=null and begnDate!='' and expiDate!=null and expiDate!=''">
                            AND dscg_time BETWEEN #{begnDate,jdbcType=VARCHAR} and CONCAT(#{expiDate,jdbcType=VARCHAR},' 23:59:59')
                        </if>
                        <if test="seStartTime!=null and seStartTime!='' and seEndTime!=null and seEndTime!=''">
                            AND setl_end_time BETWEEN #{seStartTime,jdbcType=VARCHAR} and CONCAT(#{seEndTime,jdbcType=VARCHAR},' 23:59:59')
                        </if>
                    </where>
                    )
            GROUP BY hi_setl_invy_id
            ) f
        ON a.SETTLE_LIST_ID = f.hi_setl_invy_id
        WHERE 1 = 1
       <!-- a.dscg_time BETWEEN #{begnDate,jdbcType=VARCHAR} AND CONCAT(#{expiDate,jdbcType=VARCHAR},' 23:59:59')
        <if test="inStartTime != null and inStartTime != '' and inEndTime != null and inEndTime != ''">
            AND a.adm_time BETWEEN #{inStartTime} AND CONCAT(#{inEndTime},' 23:59:59')
        </if>-->
        <if test="inStartTime!=null and inStartTime!='' and inEndTime!=null and inEndTime!=''">
            AND a.adm_time BETWEEN #{inStartTime,jdbcType=VARCHAR} and CONCAT(#{inEndTime,jdbcType=VARCHAR},' 23:59:59')
        </if>
        <if test="begnDate!=null and begnDate!='' and expiDate!=null and expiDate!=''">
            AND a.dscg_time BETWEEN #{begnDate,jdbcType=VARCHAR} and CONCAT(#{expiDate,jdbcType=VARCHAR},' 23:59:59')
        </if>
        <if test="seStartTime!=null and seStartTime!='' and seEndTime!=null and seEndTime!=''">
            AND a.setl_end_time BETWEEN #{seStartTime,jdbcType=VARCHAR} and CONCAT(#{seEndTime,jdbcType=VARCHAR},' 23:59:59')
        </if>
        <if test="patientIdList !=null and patientIdList.size()>0" >
            AND
            a.PATIENT_ID in
            <foreach collection="patientIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <include refid="queryCriteria" />
        ORDER BY a.dscg_time) a
        <where>
            <if test="isLoss != null and isLoss != ''">
              <if test="isLoss == 1">
                <![CDATA[
                    AND a.forecastAmountDiff < 0
                ]]>
              </if>
              <if test="isLoss == 0">
                <![CDATA[
                    AND a.forecastAmountDiff >= 0
                ]]>
              </if>
            </if>
            <if test="costSection != null and costSection != ''">
                AND a.stsbFee = #{costSection}
            </if>
            <if test="icdCodg != null and icdCodg != ''">
                AND a.main_diag_dise_codg = #{icdCodg}
            </if>
        </where>
    </select>

    <select id="queryDrgPatientForecastData"  resultType="com.my.som.vo.newBusiness.customPatient.NewBusinessCustomPatientVo">
        SELECT a.settleListId,
        a.name,
        a.inHosTotalCost,
        a.bah,
        a.deptCode,
        a.deptName,
        a.drgCodg,
        a.drgName,
        a.insuType,
        ROUND(a.price,2) AS price,
        a.ratioRange,
        ROUND(a.totlSco,2) AS totlSco,
        IFNULL(ROUND(a.ycCost,2),0) AS forecastAmount,
        IFNULL(ROUND(a.ycCost - a.inHosTotalCost,2),0) AS forecastAmountDiff,
        IFNULL(ROUND(a.inHosTotalCost/a.ycCost,2),0) AS oeVal
        FROM
        (
        SELECT a.SETTLE_LIST_ID AS settleListId,
        a.`NAME` AS name,
        a.dscg_caty_codg_inhosp AS deptCode,
        d.`NAME` AS deptName,
        a.PATIENT_ID AS bah,
        <choose>
            <when test="feeStas == 0">
                a.ipt_sumfee AS inHosTotalCost,
                a.drg_codg AS drgCodg,
                a.DRG_NAME AS drgName,
                CASE WHEN b.A54 = 1 THEN 1 WHEN b.A54 = 2 THEN 2 ELSE 9 END AS insuType,
<!--                CASE WHEN b.A54 = 1 THEN e.czPrice WHEN b.A54 = 2 THEN e.cxPrice ELSE e.price END AS price,-->
<!--                CASE WHEN b.A54 = 1 THEN c.totl_sco * e.czPrice WHEN b.A54 = 2 THEN c.totl_sco * e.cxPrice ELSE c.totl_sco * e.price END AS ycCost,-->
                c.dise_type AS ratioRange,
                IFNULL(c.totl_sco,0) AS totlSco,

                c.price AS price,
                c.forecast_fee AS ycCost,
                c.profitloss AS profitloss,
            </when>
            <when test="feeStas == 1">
                b.sumfee AS inHosTotalCost,
                b.drg_codg AS drgCodg,
                b.DRG_NAME AS drgName,
                b.INSURED_TYPE AS insuType,
                b.setl_pt_val AS price,
                b.dfr_fee AS ycCost,
                b.MED_TYPE AS ratioRange,
                b.setl_sco AS totlSco

            </when>
        </choose>
        FROM som_drg_grp_info a
        LEFT JOIN som_dept d
        ON a.dscg_caty_codg_inhosp = d.`CODE`
            AND a.HOSPITAL_ID = d.HOSPITAL_ID
        <if test="enableSeAns != null and enableSeAns != '' and enableSeAns == 1">
            INNER JOIN som_hi_invy_bas_info f
            ON a.SETTLE_LIST_ID = f.ID
            INNER JOIN som_setl_cas_crsp p
            ON f.K00 = p.K00
        </if>
        <choose>
            <when test="feeStas == 0">
                LEFT JOIN som_hi_invy_bas_info b
                ON a.SETTLE_LIST_ID = b.ID
                AND a.HOSPITAL_ID = b.HOSPITAL_ID
                LEFT JOIN som_drg_sco c
                ON a.SETTLE_LIST_ID = c.SETTLE_LIST_ID
<!--                CROSS JOIN (-->
<!--                SELECT-->
<!--                MAX( CASE WHEN `KEY` = 'CX_PRICE' THEN `VALUE` ELSE NULL END ) AS cxPrice,-->
<!--                MAX( CASE WHEN `KEY` = 'CZ_PRICE' THEN `VALUE` ELSE NULL END ) AS czPrice,-->
<!--                MAX( CASE WHEN `KEY` = 'PRICE' THEN `VALUE` ELSE NULL END ) AS price-->
<!--                FROM-->
<!--                som_drg_gen_///cfg-->
<!--                WHERE-->
<!--                TYPE = 'PREDICTED_PRICE'-->
<!--                GROUP BY-->
<!--                TYPE-->
<!--                ) e-->
            </when>
            <when test="feeStas == 1">
                INNER JOIN
                (
                SELECT
                a.drg_codg,
                a.DRG_NAME,
                a.rid_idt_codg,
                a.medcas_codg,
                a.adm_time,
                a.dscg_time,
                b.sumfee,
                b.INSURED_TYPE,
                b.setl_pt_val,
                b.dfr_fee,
                b.MED_TYPE,
                b.setl_sco
                FROM som_drg_grp_fbck a
                LEFT JOIN som_drg_pt_val_pay b
                ON a.rid_idt_codg = b.rid_idt_codg
                AND a.HOSPITAL_ID = b.HOSPITAL_ID
                <!--<![CDATA[
                                 WHERE a.dscg_time >= #{begnDate} AND a.dscg_time <= #{expiDate}
                                 ]]>-->
                <where>
                    <if test="inStartTime!=null and inStartTime!='' and inEndTime!=null and inEndTime!=''">
                        AND a.adm_time BETWEEN #{inStartTime,jdbcType=VARCHAR} and CONCAT(#{inEndTime,jdbcType=VARCHAR},' 23:59:59')
                    </if>
                    <if test="begnDate!=null and begnDate!='' and expiDate!=null and expiDate!=''">
                        AND a.dscg_time BETWEEN #{begnDate,jdbcType=VARCHAR} and CONCAT(#{expiDate,jdbcType=VARCHAR},' 23:59:59')
                    </if>
                    <if test="seStartTime!=null and seStartTime!='' and seEndTime!=null and seEndTime!=''">
                        AND a.setl_time BETWEEN #{seStartTime,jdbcType=VARCHAR} and CONCAT(#{seStartTime,jdbcType=VARCHAR},' 23:59:59')
                    </if>
                </where>
                <include refid="com.my.som.common.mapper.CommonMapper.fbAuth" />
                ) b
                ON a.PATIENT_ID = b.medcas_codg
                AND SUBSTR(a.adm_time,1,10) = b.adm_time
                AND SUBSTR(a.dscg_time,1,10) = b.dscg_time
            </when>
        </choose>

        WHERE 1 = 1
       <!-- a.dscg_time BETWEEN #{begnDate} AND CONCAT(#{expiDate}, ' 23:59:59')
        <if test="inStartTime != null and inStartTime != '' and inEndTime != null and inEndTime != ''">
            AND a.adm_time BETWEEN #{inStartTime} AND CONCAT(#{inEndTime},' 23:59:59')
        </if>-->
        <if test="icdCodg != null and icdCodg != ''">
            AND a.main_diag_dise_codg = #{icdCodg}
        </if>
        <if test="inStartTime!=null and inStartTime!='' and inEndTime!=null and inEndTime!=''">
            AND a.adm_time BETWEEN #{inStartTime,jdbcType=VARCHAR} and CONCAT(#{inEndTime,jdbcType=VARCHAR},' 23:59:59')
        </if>
        <if test="begnDate!=null and begnDate!='' and expiDate!=null and expiDate!=''">
            AND a.dscg_time BETWEEN #{begnDate,jdbcType=VARCHAR} and CONCAT(#{expiDate,jdbcType=VARCHAR},' 23:59:59')
        </if>
        <if test="seStartTime!=null and seStartTime!='' and seEndTime!=null and seEndTime!=''">
            AND a.setl_end_time BETWEEN #{seStartTime,jdbcType=VARCHAR} and CONCAT(#{seEndTime,jdbcType=VARCHAR},' 23:59:59')
        </if>
        <include refid="queryCriteria" />
        <if test="patientIdList !=null and patientIdList.size()>0" >
            AND
            a.PATIENT_ID in
            <foreach collection="patientIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        ) a
    </select>

    <select id="queryDrgPatientCostInfoData"  resultType="com.my.som.vo.newBusiness.customPatient.NewBusinessCustomPatientVo">
        SELECT a.*
        FROM(
        SELECT a.SETTLE_LIST_ID AS settleListId,
        a.`NAME` AS name,
        a.dscg_caty_codg_inhosp AS deptCode,
        c.`NAME` AS deptName,
        a.PATIENT_ID AS bah,
        a.adm_time AS inHosTime,
        a.dscg_time AS outHosTime,



        ROUND(IFNULL(g.avgInHosDays,0),2) AS avgInHosDays,
        IFNULL(AES_DECRYPT(UNHEX(g.standardInHosCost),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}),0) AS standardInHosCost,
        IFNULL(AES_DECRYPT(UNHEX(g.standardInHosDays),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}),0) AS standardInHosDays,

        IFNULL(g.standardDrugRatio,0)  AS standardDrugRatio,
        IFNULL(g.standardConsumeRatio,0)  AS standardConsumeRatio,
        ROUND(IFNULL(g.avgInHosCost,0),2) AS avgInHosCost,




        <choose>
            <when test="feeStas == 0">
                a.ipt_sumfee AS inHosTotalCost,
                IFNULL(ROUND(e.ycCost,2),0) AS forecastAmount,
                IFNULL(ROUND(IFNULL(e.profitloss,0) ,2),0) AS forecastAmountDiff,
                a.drg_codg AS drgCodg,
                a.DRG_NAME AS drgName,
                e.dise_type AS stsbFee,
            </when>
            <when test="feeStas == 1">
                e.ipt_sumfee AS inHosTotalCost,
                IFNULL(ROUND(e.ycCost,2),0) AS forecastAmount,
                IFNULL(ROUND(IFNULL(e.ycCost,0) - IFNULL(e.ipt_sumfee,0),2),0) AS forecastAmountDiff,
                e.drg_codg AS drgCodg,
                e.DRG_NAME AS drgName,
                e.dise_type AS stsbFee,
            </when>
        </choose>

        IFNULL(IFNULL(b.D11,0)+IFNULL(b.D12,0)+IFNULL(b.D13,0)+IFNULL(b.D14,0),0) AS com_med_servfee, <!-- 综合医疗服务费 -->
        IFNULL(b.D11,0) AS ordn_med_servfee, <!-- 一般医疗服务费 -->
        IFNULL(b.D12,0) AS ordn_trt_oprt_fee, <!-- 一般治疗操作费 -->
        IFNULL(b.D13,0) AS nursfee, <!-- 护理费 -->
        IFNULL(b.D14,0) AS zhylfwqtf, <!-- 综合医疗服务其他费 -->
        IFNULL(IFNULL(b.D15,0)+IFNULL(b.D16,0)+IFNULL(b.D17,0)+IFNULL(b.D18,0),0) AS diag_fee, <!-- 诊断费 -->
        IFNULL(b.D15,0) AS cas_diag_fee, <!-- 病理诊断费 -->
        IFNULL(b.D16,0) AS lab_diag_fee, <!-- 实验室诊断费 -->
        IFNULL(b.D17,0) AS rdhy_diag_fee, <!-- 影像学诊断费 -->
        IFNULL(b.D18,0) AS clnc_diag_item_fee, <!-- 临床诊断项目费 -->
        IFNULL(IFNULL(b.D19,0)+IFNULL(b.D20,0),0) AS treat_fee, <!-- 治疗费 -->
        IFNULL(b.D19,0) AS nsrgtrt_item_fee, <!-- 非手术治疗项目费 -->
        IFNULL(b.D20,0) AS oprn_treat_fee, <!-- 手术治疗费 -->
        IFNULL(b.D21,0) AS rhab_fee, <!-- 康复费 -->
        IFNULL(b.D22,0) AS tcmdrug_fee, <!-- 中医费 -->
        IFNULL(b.D23,0) AS west_fee, <!-- 西药费 -->
        IFNULL(IFNULL(b.D24,0)+IFNULL(b.D25,0),0) AS zyf1, <!-- 中药费 -->
        IFNULL(b.D24,0) AS tcmpat_fee, <!-- 中成药费 -->
        IFNULL(b.D25,0) AS tcmherb, <!-- 中草药费-->
        IFNULL(IFNULL(b.D26,0)+IFNULL(b.D27,0)+IFNULL(b.D28,0)+IFNULL(b.D29,0)+IFNULL(b.D30,0),0) AS blood_blo_pro_fee, <!-- 血液和血液制品费 -->
        IFNULL(b.D26,0) AS blo_fee, <!-- 血费 -->
        IFNULL(b.D27,0) AS bdblzpf, <!-- 白蛋白类制品费 -->
        IFNULL(b.D28,0) AS qdblzpf, <!-- 球蛋白类制品费 -->
        IFNULL(b.D29,0) AS nxyzlzpf, <!-- 凝血因子类制品费 -->
        IFNULL(b.D30,0) AS xbyzlzpf, <!-- 细胞因子类制品费 -->
        IFNULL(IFNULL(b.D31,0)+IFNULL(b.D32,0)+IFNULL(b.D33,0),0) AS mcs_fee, <!-- 耗材费 -->
        IFNULL(b.D31,0) AS jcyycxyyclf, <!-- 检查用一次性医用材料费 -->
        IFNULL(b.D32,0) AS trt_use_dspo_med_matlfee, <!-- 治疗用一次性医用材料费 -->
        IFNULL(b.D33,0) AS oprn_use_dspo_med_matlfee, <!-- 手术用一次性医用材料费 -->
        IFNULL(b.D34,0) AS oth_fee, <!-- 其他费 -->
        IFNULL(d.avg_fee,0) AS ljfy,<!-- 例均费用 -->
        IFNULL(d.com_med_servfee,0) AS bgzhylfwf, <!-- 标杆综合医疗服务费 -->
        IFNULL(d.ordn_med_servfee,0) AS bgybylfwf, <!-- 标杆一般医疗服务费 -->
        IFNULL(d.ordn_trt_oprt_fee,0) AS bgybzlczf, <!-- 标杆一般治疗操作费 -->
        IFNULL(d.nursfee,0) AS bghlf, <!-- 标杆护理费 -->
        IFNULL(d.oth_fee_com,0) AS bgzhylfwqtf, <!-- 标杆综合医疗服务其他费 -->
        IFNULL(d.diag_fee,0) AS bgzdf, <!-- 标杆诊断费 -->
        IFNULL(d.cas_diag_fee,0) AS bgblzdf, <!-- 标杆病理诊断费 -->
        IFNULL(d.lab_diag_fee,0) AS bgsyszdf, <!-- 标杆实验室诊断费 -->
        IFNULL(d.rdhy_diag_fee,0) AS bgyxxzdf, <!-- 标杆影像学诊断费 -->
        IFNULL(d.clnc_diag_item_fee,0) AS bglczdxmf, <!-- 标杆临床诊断项目费 -->
        IFNULL(d.treat_fee,0) AS bgzlf, <!-- 标杆治疗费 -->
        IFNULL(d.nsrgtrt_item_fee,0) AS bgfsszlxmf, <!-- 标杆非手术治疗项目费 -->
        IFNULL(d.oprn_treat_fee,0) AS bgsszlf, <!-- 标杆手术治疗费 -->
        IFNULL(d.rhab_fee,0) AS bgkff, <!-- 标杆康复费 -->
        IFNULL(d.tcm_treat_fee,0) AS bgzyf, <!-- 标杆中医费 -->
        IFNULL(d.west_fee,0) AS bgxyf, <!-- 标杆西药费 -->
        IFNULL(d.tcmdrug_fee,0) AS bgzyf1, <!-- 标杆中药费 -->
        IFNULL(d.tcmpat_fee,0) AS bgzcyf, <!-- 标杆中成药费 -->
        IFNULL(d.tcmherb,0) AS bgzcyf1, <!-- 标杆中草药费 -->
        IFNULL(d.blood_blo_pro_fee,0) AS bgxyhxyzpf, <!-- 标杆血液和血液制品费 -->
        IFNULL(d.blo_fee,0) AS bgxf, <!-- 标杆血费 -->
        IFNULL(d.albu_clss_prod_fee,0) AS bgbdblzpf, <!-- 标杆白蛋白类制品费 -->
        IFNULL(d.glon_clss_prod_fee,0) AS bgqdblzpf, <!-- 标杆球蛋白类制品费 -->
        IFNULL(d.clotfac_clss_prod_fee,0) AS bgnxyzlzpf, <!-- 标杆凝血因子类制品费 -->
        IFNULL(d.cyki_clss_prod_fee,0) AS bgxbyzlzpf, <!-- 标杆细胞因子类制品费 -->
        IFNULL(d.mcs_fee,0) AS bghcf, <!-- 标杆耗材费 -->
        IFNULL(d.exam_use_dspo_med_matlfee,0) AS bgjcyycxyyclf, <!-- 标杆检查用一次性医用材料费 -->
        IFNULL(d.trt_use_dspo_med_matlfee,0) AS bgzlyycxyyclf, <!-- 标杆治疗用一次性医用材料费 -->
        IFNULL(d.oprn_use_dspo_med_matlfee,0) AS bgssyycxyyclf, <!-- 标杆手术用一次性医用材料费 -->
        IFNULL(d.oth_fee,0) AS bgqtf <!-- 标杆其他费 -->
        FROM som_drg_grp_info a
        LEFT JOIN som_hi_invy_bas_info b
        ON a.SETTLE_LIST_ID = b.ID
        AND a.HOSPITAL_ID = b.HOSPITAL_ID
        LEFT JOIN som_dept c
        ON a.dscg_caty_codg_inhosp = c.`CODE`
        AND a.HOSPITAL_ID = c.HOSPITAL_ID
        LEFT JOIN std_cost_dy_sj d
        ON a.drg_codg = d.`CODE`
        AND SUBSTR(a.dscg_time,1,4) = d.STANDARD_YEAR
        AND d.TYPE = 3
        <choose>
            <when test="feeStas == 0">
                LEFT JOIN
                (
                SELECT b.SETTLE_LIST_ID,
                b.dise_type,
                b.forecast_fee AS ycCost,
                b.profitloss AS profitloss
                FROM som_drg_sco b
                ) e
                ON a.SETTLE_LIST_ID = e.SETTLE_LIST_ID
            </when>
            <when test="feeStas == 1">
                INNER JOIN
                (
                SELECT
                a.medcas_codg,
                a.adm_time,
                a.drg_codg,
                a.DRG_NAME,
                a.dscg_time,
                b.sumfee AS ipt_sumfee,
                b.dfr_fee AS ycCost
                b.INSURED_TYPE AS dise_type
                FROM som_drg_grp_fbck a
                LEFT JOIN som_drg_pt_val_pay b
                ON a.rid_idt_codg = b.rid_idt_codg
                AND a.HOSPITAL_ID = b.HOSPITAL_ID
                <![CDATA[
                    WHERE a.dscg_time >= #{begnDate} AND a.dscg_time <= #{expiDate}
                    ]]>
                <include refid="com.my.som.common.mapper.CommonMapper.fbAuth" />
                ) e
                ON a.PATIENT_ID = e.medcas_codg
                AND SUBSTR(a.adm_time,1,10) = e.adm_time
                AND SUBSTR(a.dscg_time,1,10) = e.dscg_time
            </when>
        </choose>
            <!--新增开始-->
        <choose>
            <when test="feeStas==0">
                LEFT JOIN(
                SELECT
                MAX(IFNULL(d.drug_ratio,0)) * 100 AS standardDrugRatio,
                MAX(IFNULL(d.mcs_fee_rat,0)) * 100 AS standardConsumeRatio,
                ROUND(IFNULL(SUM(IFNULL(a.drugfee,0))/NULLIF(SUM(IFNULL(a.ipt_sumfee,0)),0),0) * 100,2) AS drugRatio,
                ROUND(IFNULL(SUM(IFNULL(a.mcs_fee,0))/NULLIF(SUM(IFNULL(a.ipt_sumfee,0)),0),0) * 100,2) AS consumeRatio,
                MAX(IFNULL(h.dip_standard_avg_fee_same_lv,0)) AS standardInHosCost,
                MAX(IFNULL(h.dip_standard_ipt_days_same_lv,0)) AS standardInHosDays,
                ROUND(AVG(IFNULL(a.act_ipt,0)),2) AS avgInHosDays,
                ROUND(AVG(IFNULL(a.ipt_sumfee,0)),2) AS avgInHosCost,

                a.dscg_time,
                a.SETTLE_LIST_ID

                FROM som_drg_grp_info a
                LEFT JOIN som_hi_invy_bas_info b ON a.SETTLE_LIST_ID = b.ID
                LEFT JOIN std_cost_dy_sj d ON a.dip_codg = d.`CODE`
                AND SUBSTR( a.dscg_time, 1, 4 ) = d.STANDARD_YEAR
                AND a.asst_list_age_grp = d.asst_list_age_grp
                AND a.asst_list_dise_sev_deg = d.asst_list_dise_sev_deg
                AND a.asst_list_tmor_sev_deg = d.asst_list_tmor_sev_deg
                AND d.TYPE = 1
                LEFT JOIN som_drg_standard h
                ON a.dip_codg = h.dip_codg
                AND a.is_used_asst_list = h.is_used_asst_list
                AND a.asst_list_age_grp = h.asst_list_age_grp
                AND a.asst_list_dise_sev_deg = h.asst_list_dise_sev_deg
                AND a.asst_list_tmor_sev_deg = h.asst_list_tmor_sev_deg
                AND SUBSTR(a.dscg_time,1,4) = h.STANDARD_YEAR
                AND a.HOSPITAL_ID = h.HOSPITAL_ID
                WHERE 1=1
                <if test="begnDate != null and begnDate != '' and expiDate != null and expiDate != ''">
                    AND a.dscg_time BETWEEN #{begnDate,jdbcType=VARCHAR} AND concat(#{expiDate,jdbcType=VARCHAR},' 23:59:59')
                </if>
                <if test="inStartTime != null and inStartTime != '' and inEndTime != null and inEndTime != ''">
                    AND a.adm_time BETWEEN #{inStartTime} AND CONCAT(#{inEndTime},' 23:59:59')
                </if>
                <if test="seStartTime!=null and seStartTime!='' and seEndTime!=null and seEndTime!=''">
                    AND a.setl_end_time BETWEEN #{seStartTime} and CONCAT(#{seEndTime},' 23:59:59')
                </if>
                group by a.SETTLE_LIST_ID,a.dscg_time
                )g on g.SETTLE_LIST_ID=a.SETTLE_LIST_ID
            </when>
            <when test="feeStas == 1">
                LEFT JOIN (
                SELECT
                ROUND(
                IFNULL( SUM( IFNULL( c1.drugfee, 0 ))/ NULLIF( SUM( IFNULL( b1.sumfee, 0 )), 0 ), 0 ) * 100,
                2
                ) AS drugRatio,
                ROUND(
                IFNULL( SUM( IFNULL( c1.mcs_fee, 0 ))/ NULLIF( SUM( IFNULL( b1.sumfee, 0 )), 0 ), 0 ) * 100,
                2
                ) AS consumeRatio,
                MAX(
                IFNULL( d.drug_ratio, 0 )) * 100 AS standardDrugRatio,
                MAX(
                IFNULL( d.mcs_fee_rat, 0 )) * 100 AS standardConsumeRatio,
                MAX(
                IFNULL( h.dip_standard_avg_fee_same_lv, 0 )) AS standardInHosCost,
                MAX(
                IFNULL( h.dip_standard_ipt_days_same_lv, 0 )) AS standardInHosDays,
                ROUND( AVG( IFNULL( c1.act_ipt, 0 )), 2 ) AS avgInHosDays,
                ROUND( AVG( IFNULL( c1.ipt_sumfee, 0 )), 2 ) AS avgInHosCost,

                a1.medcas_codg
                FROM
                som_drg_grp_fbck a1
                LEFT JOIN som_drg_grp_info c1 ON c1.PATIENT_ID = a1.medcas_codg
                AND SUBSTR( c1.adm_time, 1, 10 ) = a1.adm_time
                AND SUBSTR( c1.dscg_time, 1, 10 ) = a1.dscg_time
                AND c1.dip_codg = a1.dip_codg
                AND c1.HOSPITAL_ID = a1.HOSPITAL_ID
                AND c1.PATIENT_ID = a1.medcas_codg
                LEFT JOIN som_drg_standard h ON c1.dip_codg = h.dip_codg
                AND c1.is_used_asst_list = h.is_used_asst_list
                AND c1.asst_list_age_grp = h.asst_list_age_grp
                AND c1.asst_list_dise_sev_deg = h.asst_list_dise_sev_deg
                AND c1.asst_list_tmor_sev_deg = h.asst_list_tmor_sev_deg
                AND SUBSTR( c1.dscg_time, 1, 4 ) = h.STANDARD_YEAR
                AND c1.HOSPITAL_ID = h.HOSPITAL_ID
                LEFT JOIN som_drg_pt_val_pay b1 ON a1.rid_idt_codg = b1.rid_idt_codg
                AND a1.HOSPITAL_ID = b1.HOSPITAL_ID
                LEFT JOIN std_cost_dy_sj d ON c1.dip_codg = d.`CODE`
                AND SUBSTR( c1.dscg_time, 1, 4 ) = d.STANDARD_YEAR
                AND c1.asst_list_age_grp = d.asst_list_age_grp
                AND c1.asst_list_dise_sev_deg = d.asst_list_dise_sev_deg
                AND c1.asst_list_tmor_sev_deg = d.asst_list_tmor_sev_deg
                AND d.TYPE = 1
                WHERE
                1 = 1
                AND a1.setl_time BETWEEN '2022-06-01'
                AND concat( '2023-07-31', ' 23:59:59' )
                GROUP BY
                a1.medcas_codg
                where 1=1

                <if test="begnDate != null and begnDate != '' and expiDate != null and expiDate != ''">
                    AND a1.dscg_time BETWEEN #{begnDate,jdbcType=VARCHAR} AND concat(#{expiDate,jdbcType=VARCHAR},' 23:59:59')
                </if>
                <if test="inStartTime != null and inStartTime != '' and inEndTime != null and inEndTime != ''">
                    AND a1.adm_time BETWEEN #{inStartTime} AND CONCAT(#{inEndTime},' 23:59:59')
                </if>
                <if test="seStartTime!=null and seStartTime!='' and seEndTime!=null and seEndTime!=''">
                    AND a1.setl_time BETWEEN #{seStartTime} and CONCAT(#{seEndTime},' 23:59:59')
                </if>
                <if test="hospitalId != null and hospitalId != ''">
                    AND a1.HOSPITAL_ID = #{hospitalId,jdbcType=VARCHAR}
                </if>

                )g on g.medcas_codg=a.PATIENT_ID
            </when>
        </choose>
        <!--新增结束-->
        WHERE a.dscg_time BETWEEN #{begnDate,jdbcType=VARCHAR} AND CONCAT(#{expiDate,jdbcType=VARCHAR},' 23:59:59')
        <if test="inStartTime != null and inStartTime != '' and inEndTime != null and inEndTime != ''">
            AND a.adm_time BETWEEN #{inStartTime} AND CONCAT(#{inEndTime},' 23:59:59')
        </if>
        <if test="patientIdList !=null and patientIdList.size()>0" >
            AND
            a.PATIENT_ID in
            <foreach collection="patientIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <include refid="queryCriteria" />
        ORDER BY a.dscg_time) a
        <where>
            <if test="isLoss != null and isLoss != ''">
                <if test="isLoss == 1">
                    <![CDATA[
                    AND a.forecastAmountDiff < 0
                ]]>
                </if>
                <if test="isLoss == 0">
                    <![CDATA[
                    AND a.forecastAmountDiff >= 0
                ]]>
                </if>
            </if>
            <if test="costSection != null and costSection != ''">
                AND a.stsbFee = #{costSection}
            </if>
        </where>
    </select>

    <select id="queryDrgPatientBasicSortInfo"  resultType="com.my.som.vo.newBusiness.customPatient.NewBusinessCustomPatientVo">
        SELECT a.name,
        a.inHosDays,
        a.inHosTotalCost,
        a.materialCostRate,
        a.medicalCostRate
        FROM
        (
        SELECT a.A11 AS name,
        a.B20+0 AS inHosDays,
        a.D01+0 AS inHosTotalCost,
        IFNULL(ROUND(IFNULL(b.drugfee,0)/NULLIF(a.D01,0) * 100,2),0) AS medicalCostRate,
        IFNULL(ROUND(IFNULL(b.mcs_fee,0)/NULLIF(a.D01,0) * 100,2),0) AS materialCostRate
        FROM som_hi_invy_bas_info a
        LEFT JOIN som_drg_grp_info b ON a.ID = b.SETTLE_LIST_ID
        LEFT JOIN som_drg_grp_rcd c ON a.ID = c.SETTLE_LIST_ID
        LEFT JOIN som_dept d ON a.B16C = d.CODE AND a.HOSPITAL_ID = d.HOSPITAL_ID
        <where>
            <if test="inStartTime != null and inStartTime != '' and
                      inEndTime != null and inEndTime != ''">
                AND a.B12 BETWEEN #{inStartTime} AND CONCAT(#{inEndTime},' 23:59:59')
            </if>
            <if test="begnDate != null and begnDate != '' and
                      expiDate != null and expiDate != ''">
                AND a.B15 BETWEEN #{begnDate} AND CONCAT(#{expiDate},' 23:59:59')
            </if>
            <if test="seStartTime != null and seStartTime != '' and
                      seEndTime != null and seEndTime != ''">
                AND a.D37 BETWEEN #{seStartTime} AND CONCAT(#{seEndTime},' 23:59:59')
            </if>
            <if test="drgCodg != null and drgCodg != ''">
                AND b.drg_codg = #{drgCodg}
            </if>
            <if test="bah != null and bah != ''">
                AND a.A48 LIKE CONCAT('%',#{bah},'%')
            </if>
            <if test="patientIdList != null and patientIdList.size() > 0">
                AND a.A48 IN
                <foreach collection="patientIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="grpFlag != null and grpFlag != ''">
                <choose>
                    <when test="grpFlag === 1">
                        AND c.grp_fale_rea IS NOT NULL
                    </when>
                </choose>
            </if>
            <if test="errorReason != null and errorReason.size() > 0">
                AND
                <foreach collection="errorReason" index="index" item="item" open="(" separator="OR" close=")">
                    c.grp_fale_rea LIKE CONCAT(#{item})
                </foreach>
            </if>
            <if test="drCodg != null and drCodg != ''">
                AND a.B25C = #{drCodg}
            </if>
        </where>
        ) a
        ORDER BY ${selectVal}
        <choose>
            <when test="!sort">
                DESC
            </when>
        </choose>
        LIMIT 50
    </select>

    <sql id="queryCriteria">
        <include refid="com.my.som.mapper.newBusiness.common.NewBusinessCommonAnalysisMapper.queryBusCommon" />
        <if test="bah != null and bah != ''">
            AND a.PATIENT_ID like CONCAT('%',#{bah},'%')
        </if>
        <if test="drgCodg != null and drgCodg != ''">
            <if test="feeStas != null and feeStas != ''">
                <choose>
                    <when test="feeStas == 0">
                        AND a.drg_codg = #{drgCodg}
                    </when>
                    <when test="feeStas == 1">
                        AND e.drg_codg = #{drgCodg}
                    </when>
                </choose>
            </if>
        </if>
        <if test="isInGroup != null and isInGroup != ''">
            <choose>
                <when test="isInGroup == 0">
                    <if test="feeStas == 0">
                        AND a.grp_stas != 1
                    </if>
                    <if test="feeStas == 1">
                        AND e.is_in_group != 1
                    </if>
                </when>
                <when test="isInGroup == 1">
                    <if test="feeStas == 0">
                        AND a.grp_stas = 1
                    </if>
                    <if test="feeStas == 1">
                        AND e.is_in_group = 1
                    </if>
                </when>
            </choose>
        </if>
    </sql>
</mapper>
