<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.som.mapper.newBusiness.diseaseCRSAnalysis.NewDrgBusinessDiseaseCRSAnalysisMapper">
    <select id="queryDiseaseCRSData" resultType="com.my.som.vo.newBusiness.diseaseCRSAnalysis.NewDrgBusinessDiseaseCRSAnalysisVo">
        SELECT a.mdc_codg AS mdcCodg,
               a.standardDiseaseNum,
               b.diseaseNum,
               ROUND(IFNULL(b.diseaseNum/a.standardDiseaseNum,0) * 100,2) AS coverage,
               IFNULL(b.cmi,0) AS cmi,
               c.MDC_NAME AS mdcName
        FROM
             (
                SELECT mdc_codg,
                       COUNT(1) AS standardDiseaseNum
                FROM som_drg_name
                GROUP BY mdc_codg
             ) a
        LEFT JOIN
             (
                SELECT count(1) AS diseaseNum,
                       IFNULL(ROUND(SUM(IFNULL(a.medcasVal,0) * IFNULL(a.drg_wt,0))/SUM(IFNULL(a.medcasVal,0)),2),0) AS cmi,
                       mdc_codg,
                       MDC_NAME
                FROM
                     (
                        SELECT a.drg_codg,
                               COUNT(1) AS medcasVal,
                               a.mdc_codg,
                               a.MDC_NAME,
                               b.drg_wt
                        FROM som_drg_grp_info a
                        LEFT JOIN som_drg_standard b
                            ON a.drg_codg = b.drg_codg
                           AND SUBSTR(a.dscg_time,1,4) = b.STANDARD_YEAR
                           AND b.ACTIVE_FLAG = 1
                        WHERE a.grp_stas = 1
                          AND a.mdc_codg IS NOT NULL
                        <if test="begnDate!= null and begnDate != '' and expiDate != null and expiDate != ''">
                            AND a.dscg_time BETWEEN #{ begnDate, jdbcType=VARCHAR } AND CONCAT(#{ expiDate, jdbcType=VARCHAR }, ' 23:59:59')
                        </if>
                        GROUP BY a.drg_codg,
                                 a.mdc_codg,
                                 a.MDC_NAME,
                                 b.drg_wt
                     ) a
                GROUP BY a.mdc_codg,
                         a.MDC_NAME
             ) b
            ON a.mdc_codg = b.mdc_codg
        LEFT JOIN som_mdc_cfg c
            ON a.mdc_codg = c.mdc_codg
           AND c.ACTIVE_FLAG = 1
        ORDER BY a.mdc_codg DESC
    </select>
</mapper>