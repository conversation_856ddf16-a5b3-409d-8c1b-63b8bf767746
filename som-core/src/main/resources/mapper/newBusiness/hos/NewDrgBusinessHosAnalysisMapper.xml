<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.som.mapper.newBusiness.hos.NewDrgBusinessHosAnalysisMapper">

    <!-- 查询头部汇总数据 -->
    <select id="querySummaryData" resultType="com.my.som.vo.newBusiness.dept.NewBusinessDeptVo">
        SELECT COUNT(1) AS totalNum,IFNULL( COUNT( CASE WHEN a.grp_stas = 1 THEN 1 ELSE NULL END ), 0 ) AS
        drgInGroupMedcasVal,
        IFNULL( COUNT( CASE WHEN a.grp_stas = 0 THEN 1 ELSE NULL END ), 0 ) AS notInGroupNum,
        IFNULL( COUNT( CASE WHEN a.ratioRange = 1 THEN 1 ELSE NULL END ), 0 ) AS ultrahighNum,
        IFNULL( COUNT( CASE WHEN a.ratioRange = 2 THEN 1 ELSE NULL END ), 0 ) AS ultraLowNum,
        IFNULL( COUNT( CASE WHEN a.ratioRange = 3 THEN 1 ELSE NULL END ), 0 ) AS normalNum,
        IFNULL( COUNT( CASE WHEN a.ratioRange = 7 THEN 1 ELSE NULL END ), 0 ) AS ultraIncompleteNum,
        <if test="feeStas == 0">
            ROUND(IFNULL(SUM(a.refer_sco)/COUNT(1)/100,0),4) AS cmi,
        </if>
        IFNULL( ROUND( SUM( a.ipt_sumfee ), 2 ), 0 ) AS sumfee,
        IFNULL( ROUND( SUM( a.ycCost ), 2 ), 0 ) AS forecastAmount,
        IFNULL( ROUND( IFNULL( SUM( a.profitloss ), 0 ) , 2 ), 0 ) AS   forecastAmountDiff
        FROM
        (
        SELECT
        <if test="feeStas != null and feeStas != ''">
            <choose>
                <when test="feeStas == 0">
                    a.ipt_sumfee,
                    a.grp_stas,
                    z.refer_sco,
                    z.ratioRange,
                    z.ycCost,
                    z.profitloss
                </when>
                <when test="feeStas == 1">
                    b.ipt_sumfee,
                    b.ratioRange,
                    b.ycCost
                </when>
            </choose>
        </if>
        FROM som_drg_grp_info a
        <if test='enableSeAns != null and enableSeAns != "" and enableSeAns == "1"'>
            LEFT JOIN som_hi_invy_bas_info q
            ON q.ID = a.SETTLE_LIST_ID
            INNER JOIN som_setl_cas_crsp p
            ON q.k00 = p.k00
        </if>
        <if test="feeStas != null and feeStas != ''">
            <choose>
                <when test="feeStas == 0">
                    LEFT JOIN (
                    SELECT
                    b.SETTLE_LIST_ID,
                    IFNULL(b.refer_sco,0) AS refer_sco,
                    b.forecast_fee as ycCost,
                    b.profitloss as profitloss,
                    dise_type AS ratioRange
                    FROM
                    som_drg_sco b

                    ) z ON a.SETTLE_LIST_ID = z.SETTLE_LIST_ID

                </when>
                <when test="feeStas == 1">
                    INNER JOIN
                    (
                    SELECT a.medcas_codg,
                    a.HOSPITAL_ID,
                    a.adm_time,
                    a.dscg_time,
                    b.MED_TYPE AS ratioRange,
                    b.sumfee AS ipt_sumfee,
                    b.dfr_fee AS ycCost
                    FROM som_drg_grp_fbck a
                    LEFT JOIN som_drg_pt_val_pay b
                    ON a.rid_idt_codg = b.rid_idt_codg
                    AND a.HOSPITAL_ID = b.HOSPITAL_ID
                    <![CDATA[
                                            WHERE a.dscg_time >= #{begnDate} AND a.dscg_time <= #{expiDate}
                                            ]]>
                    <include refid="com.my.som.common.mapper.CommonMapper.fbAuth"/>
                    ) b
                    ON a.PATIENT_ID = b.medcas_codg
                    AND SUBSTR(a.adm_time,1,10) = b.adm_time
                    AND SUBSTR(a.dscg_time,1,10) = b.dscg_time
                </when>
            </choose>
        </if>
        <where>
            <if test="dateType == 1">
                AND a.dscg_time between #{begnDate,jdbcType=VARCHAR} and concat(#{expiDate,jdbcType=VARCHAR},' 23:59:59') <!-- 开始时间，结束时间 -->
            </if>
            <if test="dateType == 2">
                AND a.setl_end_time BETWEEN #{begnDate,jdbcType=VARCHAR} AND concat(#{expiDate,jdbcType=VARCHAR},' 23:59:59')
            </if>
            <include refid="com.my.som.mapper.newBusiness.common.NewBusinessCommonAnalysisMapper.queryBusCommon"/>
        </where>
        ) a
    </select>

    <!-- 查询错误病例 -->
    <select id="queryErrorData" resultType="com.my.som.vo.dipBusiness.DipGroupErrorMsgVo">
        SELECT grp_fale_rea AS notInGroupReason,
        grp_stas AS grpStas,
        COUNT(1)OVER() AS totalNum
        FROM som_drg_grp_rcd
        WHERE SETTLE_LIST_ID IN (
        SELECT SETTLE_LIST_ID
        FROM som_drg_grp_info a
        <if test='enableSeAns != null and enableSeAns != "" and enableSeAns == "1"'>
            LEFT JOIN som_hi_invy_bas_info q
            ON a.SETTLE_LIST_ID = q.ID
            INNER JOIN som_setl_cas_crsp p
            ON q.k00 = p.k00
        </if>
        <where>
            <if test="begnDate != null and begnDate != ''
                                                     and expiDate!=null and expiDate!=''">
                <if test="dateType == 1">
                    AND a.dscg_time BETWEEN #{begnDate} AND CONCAT(#{expiDate},' 23:59:59')
                </if>
                <if test="dateType == 2">
                    AND a.setl_end_time BETWEEN #{begnDate} AND CONCAT(#{expiDate},' 23:59:59')
                </if>
            </if>
            <if test="hospitalId != null and hospitalId != ''">
                AND a.HOSPITAL_ID = #{hospitalId}
            </if>
        </where>
        )

        <!--
         select c.*,
        error1 + error2 + error3 + error4 + error5 + error6 + error7 + error8  AS errorSummaryNum,
        CASE WHEN c.totalNum = 0 THEN 0 ELSE ROUND(drgInGroupMedcasVal * 100 / totalNum,0) END AS inGroupRate
        from (
        SELECT  IFNULL( COUNT( CASE WHEN b.grp_fale_rea LIKE '%字段类型错误%' THEN '1' ELSE NULL END ), 0 ) AS error1,
        IFNULL( COUNT( CASE WHEN b.grp_fale_rea LIKE '%主要诊断不能为空%' THEN '1' ELSE NULL END ), 0 ) AS error2,
        IFNULL( COUNT( CASE WHEN b.grp_fale_rea LIKE '%该诊断不能为主要诊断%' THEN 1 ELSE NULL END ),0 ) AS error3,
        IFNULL( COUNT( CASE WHEN b.grp_fale_rea LIKE '%性别填写错误%' THEN '1' ELSE NULL END ), 0 ) AS error4,
        IFNULL( COUNT( CASE WHEN b.grp_fale_rea LIKE '%总费用小于5元%' THEN '1' ELSE NULL END ), 0 ) AS error5,
        IFNULL( COUNT( CASE WHEN b.grp_fale_rea LIKE '%住院天数大于60天或者小于0%' THEN '1' ELSE NULL END ), 0 ) AS error6,
        IFNULL( COUNT( CASE WHEN b.grp_fale_rea LIKE '%无分组方案%' THEN '1' ELSE NULL END ), 0 ) AS error7,
        IFNULL( COUNT( CASE WHEN b.grp_fale_rea LIKE '%主要诊断编码不规范%' THEN '1' ELSE NULL END ), 0 ) AS error8,
        IFNULL( COUNT( CASE WHEN b.grp_stas = '1'  THEN 1 ELSE NULL END ), 0) AS drgInGroupMedcasVal,
        COUNT(1) AS totalNum,
        IFNULL(count(case when c.compeleteErrorNum != 0 then 1 else null end),0) as compeleteErrorNum,
        IFNULL(count(case when c.logicErrorNum != 0 then 1 else null end),0) as logicErrorNum
        FROM som_drg_grp_info a
        <if test='enableSeAns != null and enableSeAns != "" and enableSeAns == "1"'>
            LEFT JOIN som_hi_invy_bas_info q
            ON q.ID = a.SETTLE_LIST_ID
            INNER JOIN som_setl_cas_crsp p
            ON q.k00 = p.k00
        </if>
        LEFT JOIN som_drg_grp_rcd b
        ON a.SETTLE_LIST_ID = b.SETTLE_LIST_ID
        left join  (
        select IFNULL(count(case when err_type in ('CE01','CE02','CE03','CE04') then 1 else null end),0) as compeleteErrorNum,
        IFNULL(count(case when err_type in ('LE01','LE02','LE03','LE04','LE05','LE06','LE07') then 1 else null end),0) as logicErrorNum,
        SETTLE_LIST_ID
        from
        som_setl_invy_chk_err_rcd a
        group by SETTLE_LIST_ID
        ) c
        on a.SETTLE_LIST_ID = c.SETTLE_LIST_ID
        left join som_dept d
        on a.dscg_caty_codg_inhosp = d.`CODE` and a.HOSPITAL_ID = d.HOSPITAL_ID
        WHERE   a.dscg_caty_codg_inhosp IS NOT NULL
        <include refid="com.my.som.mapper.newBusiness.common.NewBusinessCommonAnalysisMapper.queryBusCommon" />
        AND a.dscg_caty_codg_inhosp != ''
        AND a.dscg_time between #{begnDate,jdbcType=VARCHAR}
        AND concat(#{expiDate,jdbcType=VARCHAR},' 23:59:59')
        ) c
         -->
    </select>

    <!-- 查询排序数据 -->
    <select id="queryOrderData" resultType="com.my.som.vo.newBusiness.NewBusinessAnalysisVo">
        select k.* from (
        SELECT
        <choose>
            <when test='analysisType == "dept"'>
                deptCode,deptName,
            </when>
            <when test='analysisType == "doctor"'>
                drCodg,drName,
            </when>
            <when test='analysisType == "dis"'>
                drgCodg,
                concat(drgCodg,drgName) as drgName,

            </when>
            <when test='analysisType == "med"'>
                id,patientId, name,
            </when>
        </choose>
        IFNULL( ROUND( sum( preCost ) - sum( inHosTotalCost ), 2 ), 0 ) AS diff
        FROM
        (
        SELECT
        g.*,
        <if test="feeStas != null and feeStas != ''">
            <choose>
                <when test="feeStas == 0">
                    IFNULL(ROUND(g.refer_sco * g.price,2),0) AS preCost
                </when>
                <when test="feeStas == 1">
                    g.ycCost AS preCost
                </when>
            </choose>
        </if>
        FROM
        (
        SELECT
        a.B25N AS drName,
        a.B25C AS drCodg,
        a.B16C AS deptCode,
        a.A11 AS name,
        a.id AS id,
        e.`NAME` AS deptName,
        a.A48 AS patientId,
        <if test="feeStas != null and feeStas != ''">
            <choose>
                <when test="feeStas == 0">
                    a.a54 AS insuredType,
                    a.D01 AS inHosTotalCost,
                    b.drg_codg AS drgCodg,
                    b.DRG_NAME AS drgName,
                    f.totl_sco AS refer_sco,
                    f.dise_type AS highLowType,
                    f.price AS price
                </when>
                <when test="feeStas == 1">
                    z.INSURED_TYPE AS insuredType,
                    z.ipt_sumfee AS inHosTotalCost,
                    z.drg_codg AS drgCodg,
                    z.DRG_NAME AS drgName,
                    z.setl_sco AS refer_sco,
                    z.MED_TYPE AS highLowType,
                    z.ycCost
                </when>
            </choose>
        </if>
        FROM
        som_hi_invy_bas_info a
        <if test='enableSeAns != null and enableSeAns != "" and enableSeAns == "1"'>
            INNER JOIN som_setl_cas_crsp p
            ON a.k00 = p.k00
        </if>
        LEFT JOIN som_drg_grp_info b ON a.ID = b.SETTLE_LIST_ID
        LEFT JOIN som_drg_standard c ON b.drg_codg = c.drg_codg
        AND SUBSTR( b.dscg_time, 1, 4 ) = c.STANDARD_YEAR
        AND b.HOSPITAL_ID = c.HOSPITAL_ID
        AND c.ACTIVE_FLAG = 1
        LEFT JOIN som_dept e ON a.B16C = e.`CODE` AND a.A01 = e.HOSPITAL_ID
        LEFT JOIN som_drg_sco f ON a.id = f.SETTLE_LIST_ID AND a.A01 = f.HOSPITAL_ID

        <if test="feeStas != null and feeStas != ''">
            <choose>
                <when test="feeStas == 1">
                    INNER JOIN
                    (
                    SELECT
                    a.medcas_codg,
                    a.HOSPITAL_ID,
                    a.adm_time,
                    a.drg_codg,
                    a.DRG_NAME,
                    a.dscg_time,
                    b.INSURED_TYPE,
                    b.MED_TYPE,
                    b.setl_sco,
                    b.sumfee AS ipt_sumfee,
                    b.dfr_fee AS ycCost
                    FROM som_drg_grp_fbck a
                    LEFT JOIN som_drg_pt_val_pay b
                    ON a.rid_idt_codg = b.rid_idt_codg
                    AND a.HOSPITAL_ID = b.HOSPITAL_ID
                    <!--                    <![CDATA[-->
                    <!--                                        WHERE a.dscg_time >= #{begnDate,jdbcType=VARCHAR} AND a.dscg_time <= #{expiDate,jdbcType=VARCHAR}-->
                    <!--                                        ]]>-->
                    <if test="dateType == 1">
                        AND a.dscg_time BETWEEN #{begnDate,jdbcType=VARCHAR} and concat(#{expiDate,jdbcType=VARCHAR},'
                        23:59:59')
                    </if>
                    <if test="dateType == 2">
                        AND a.setl_time BETWEEN #{begnDate,jdbcType=VARCHAR} and concat(#{expiDate,jdbcType=VARCHAR},'
                        23:59:59')
                    </if>
                    <include refid="com.my.som.common.mapper.CommonMapper.fbAuth"/>
                    ) z
                    ON b.PATIENT_ID = z.medcas_codg
                    AND SUBSTR(b.adm_time,1,10) = z.adm_time
                    AND SUBSTR(b.dscg_time,1,10) = z.dscg_time
                    AND b.HOSPITAL_ID = z.HOSPITAL_ID
                </when>
            </choose>
        </if>


        WHERE
        b.grp_stas = 1
        AND a.ACTIVE_FLAG = '1'
        <if test="dateType == 1">
            AND a.b15 BETWEEN #{begnDate,jdbcType=VARCHAR} and concat(#{expiDate,jdbcType=VARCHAR},' 23:59:59')
        </if>
        <if test="dateType == 2">
            AND a.D37 BETWEEN #{begnDate,jdbcType=VARCHAR} and concat(#{expiDate,jdbcType=VARCHAR},' 23:59:59')
        </if>
        <include refid="com.my.som.mapper.newBusiness.common.NewBusinessCommonAnalysisMapper.queryCommon"/>
        ) g

        ) i
        GROUP BY
        <choose>
            <when test='analysisType == "dept"'>
                deptCode,deptName
            </when>
            <when test='analysisType == "dis"'>
                drgCodg,drgName
            </when>
            <when test='analysisType == "doctor"'>
                drCodg,drName
            </when>
            <when test='analysisType == "med"'>
                id,patientId, name
            </when>
        </choose>

        ) k
        WHERE <![CDATA[
            diff ${gtOrLtSymbol} 0
        ]]>
        ORDER BY diff ${sortSymbol}
        LIMIT ${limit}
    </select>
    <select id="queryCompeleteAndLogicErroNum" resultType="com.my.som.vo.dipBusiness.DipInGroupAnalysisVo">
        SELECT IFNULL( count( CASE WHEN c.compeleteErrorNum != 0 THEN 1 ELSE NULL END ), 0 ) AS compeleteErrorNum,
        IFNULL( count( CASE WHEN c.logicErrorNum != 0 THEN 1 ELSE NULL END ), 0 ) AS logicErrorNum
        FROM som_drg_grp_info a
        LEFT JOIN (
        SELECT
        IFNULL( count( CASE WHEN err_type IN ( 'CE01', 'CE02', 'CE03', 'CE04' ) THEN 1 ELSE NULL END ), 0 ) AS
        compeleteErrorNum,
        IFNULL(count( CASE WHEN err_type IN ( 'LE01', 'LE02', 'LE03', 'LE04', 'LE05', 'LE06', 'LE07' ) THEN 1 ELSE
        NULL END ),0) AS logicErrorNum,
        SETTLE_LIST_ID
        FROM
        som_setl_invy_chk_err_rcd a
        GROUP BY
        SETTLE_LIST_ID
        ) c ON a.SETTLE_LIST_ID = c.SETTLE_LIST_ID
        <if test='enableSeAns != null and enableSeAns != "" and enableSeAns == "1"'>
            LEFT JOIN som_hi_invy_bas_info q
            ON a.SETTLE_LIST_ID = q.ID
            INNER JOIN som_setl_cas_crsp p
            ON q.k00 = p.k00
        </if>
        <where>
            <if test="begnDate != null and begnDate != ''
                                                     and expiDate!=null and expiDate!=''">
                AND a.dscg_time BETWEEN #{begnDate} AND CONCAT(#{expiDate},' 23:59:59')
            </if>
        </where>
    </select>

    <!-- 查询趋势数据 -->
    <select id="queryTrendData" resultType="com.my.som.vo.newBusiness.dept.NewBusinessDeptVo">
        select COUNT(1) AS totalNum,
        yearMonth,
        IFNULL( COUNT( CASE WHEN a.grp_stas = 1 THEN 1 ELSE NULL END ), 0 ) AS drgInGroupMedcasVal,
        IFNULL( COUNT( CASE WHEN a.grp_stas = 0 THEN 1 ELSE NULL END ), 0 ) AS notInGroupNum,
        IFNULL( COUNT( CASE WHEN a.ratioRange = 1 THEN 1 ELSE NULL END ), 0 ) AS ultrahighNum,
        IFNULL( COUNT( CASE WHEN a.ratioRange = 2 THEN 1 ELSE NULL END ), 0 ) AS ultraLowNum,
        IFNULL( ROUND( SUM( a.ipt_sumfee ), 2 ), 0 ) AS sumfee,
        IFNULL( ROUND( SUM( a.ycCost ), 2 ), 0 ) AS forecastAmount,
        IFNULL( ROUND( IFNULL( SUM( a.profitloss ), 0 ), 2 ), 0 ) ASforecastAmountDiff

        from
        (
        select a.ipt_sumfee,
        a.grp_stas,
        <choose>
            <when test="dateType == 1">
                SUBSTR(a.dscg_time,1,7) as yearMonth,
            </when>
            <when test="dateType == 2">
                SUBSTR(a.setl_end_time,1,7) as yearMonth,
            </when>
        </choose>
        z.ratioRange,
        z.ycCost,
        z.profitloss
        from som_drg_grp_info a
        left join
        (
        select b.SETTLE_LIST_ID,
        b.profitloss AS profitloss,
        b.forecast_fee AS ycCost,
        b.dise_type AS ratioRange
        from som_drg_sco b

        ) z
        on a.SETTLE_LIST_ID = z.SETTLE_LIST_ID
        <where>
            <choose>
                <when test="dateType == 1">
                    <if test="begnDate != null and begnDate != '' and expiDate != null and expiDate != ''">
                        <![CDATA[
                            and SUBSTR(a.dscg_time,1,7) >= #{begnDate}
                            and SUBSTR(a.dscg_time,1,7) <= #{expiDate}
                            ]]>
                    </if>
                </when>
                <when test="dateType == 2">
                    <if test="begnDate != null and begnDate != '' and expiDate != null and expiDate != ''">
                        <![CDATA[
                            and SUBSTR(a.setl_end_time,1,7) >= #{begnDate}
                            and SUBSTR(a.setl_end_time,1,7) <= #{expiDate}
                            ]]>
                    </if>
                </when>
            </choose>
            <if test="hospitalId != null and hospitalId != ''">
                AND a.HOSPITAL_ID = #{hospitalId,jdbcType=VARCHAR}
            </if>
        </where>
        ) a
        group by yearMonth
        order by yearMonth
    </select>

    <select id="queryQuadrantData" resultType="com.my.som.vo.newBusiness.dept.NewBusinessDeptVo">
        SELECT
        a.deptCode AS deptCode,
        a.deptName AS deptName,
        count(*) AS medicalTotalNum,
        IFNULL( sum( a.drgWt ), 0 ) AS totalWeight,
        IFNULL( round( sum( a.drgWt )/ count( CASE WHEN a.isInGroup = 1 THEN 1 ELSE NULL END ), 2 ), 0 ) AS cmi,
        IFNULL( round( sum( a.sumfee ), 2 ), 0 ) AS sumfee,
        IFNULL(round( sum(( a.ycCost )),  2  ),0) AS forecastAmount,
        IFNULL( round(sum(( a.ycCost) - a.sumfee - a.preTotalCost),2),0) AS forecastAmountDiff
        FROM
        (
        SELECT
        a.A11,
        a.B16C AS deptCode,
        c.`NAME` AS deptName,
        b.drg_wt AS drgWt,
        b.grp_stas AS isInGroup,
        a.D01 AS sumfee,
        CASE WHEN a.D02 is NULL THEN 0 ELSE a.D02 END AS preTotalCost,
        d.totl_sco AS totlSco,
        a.A46C AS medPayWay,
        d.price AS price,
        d.forecast_fee AS ycCost,
        d.profitloss AS profitloss
        FROM
        som_hi_invy_bas_info a
        LEFT JOIN som_drg_grp_info b ON a.ID = b.SETTLE_LIST_ID
        LEFT JOIN som_dept c ON a.B16C = c.`CODE`
        LEFT JOIN som_drg_sco d ON a.ID = d.SETTLE_LIST_ID

        where 1=1
        <if test="seStartTime != null and seStartTime != '' and seEndTime != null and seEndTime !=''">
            AND a.D37 BETWEEN #{seStartTime,jdbcType=VARCHAR} AND CONCAT(#{seEndTime,jdbcType=VARCHAR},' 23:59:59')
        </if>
        <if test="begnDate != null and begnDate != '' and expiDate != null and expiDate != ''">
            AND a.B15 BETWEEN #{begnDate,jdbcType=VARCHAR} AND CONCAT(#{expiDate,jdbcType=VARCHAR},' 23:59:59')
        </if>
        <if test="hospitalId != null and hospitalId != ''">
            AND a.A01 = #{hospitalId,jdbcType=VARCHAR}
        </if>
        ) a
        GROUP BY
        a.deptCode,
        a.deptName
    </select>
    <select id="queryQuadrantDipData" resultType="com.my.som.vo.newBusiness.dept.NewBusinessDeptVo">
        SELECT
        a.deptCode AS deptCode,
        a.deptName AS deptName,
        count(*) AS medicalTotalNum,
        IFNULL( sum( a.dipWt ), 0 ) AS totalWeight,
        IFNULL( round( sum( CASE WHEN a.isInGroup = 1 THEN a.dipWt ELSE 0 END )/ count( CASE WHEN a.isInGroup = 1 THEN 1 ELSE NULL END ), 2 ), 0 ) AS cmi,
        IFNULL( round( sum( a.sumfee ), 2 ), 0 ) AS sumfee,
        IFNULL(round(sum(a.ycCost), 2 ),  0  ) AS forecastAmount,
        IFNULL( round( sum( a.profitloss) - sum(a.preTotalCost ), 2 ), 0 ) AS forecastAmountDiff
        FROM
        (
            SELECT
            a.A11,
            a.B16C AS deptCode,
            c.`NAME` AS deptName,
            b.dip_wt AS dipWt,
            b.grp_stas AS isInGroup,
            a.D01 AS sumfee,
            CASE WHEN a.D02 is NULL THEN 0 ELSE a.D02 END AS preTotalCost,
            d.totl_sco AS totlSco,
            a.A46C AS medPayWay,
            d.profitloss AS profitloss,
            d.forecast_fee AS ycCost,
            d.price AS price
            FROM
            som_hi_invy_bas_info a
            LEFT JOIN som_dip_grp_info b ON a.ID = b.SETTLE_LIST_ID
            LEFT JOIN som_dept c ON a.B16C = c.`CODE`
            LEFT JOIN som_dip_sco d ON a.ID = d.SETTLE_LIST_ID

            where 1=1
            <if test="seStartTime != null and seStartTime != '' and seEndTime != null and seEndTime !=''">
                AND a.D37 BETWEEN #{seStartTime,jdbcType=VARCHAR} AND CONCAT(#{seEndTime,jdbcType=VARCHAR},' 23:59:59')
            </if>
            <if test="begnDate != null and begnDate != '' and expiDate != null and expiDate != ''">
                AND a.B15 BETWEEN #{begnDate,jdbcType=VARCHAR} AND CONCAT(#{expiDate,jdbcType=VARCHAR},' 23:59:59')
            </if>
            <if test="hospitalId != null and hospitalId != ''">
                AND a.A01 = #{hospitalId,jdbcType=VARCHAR}
            </if>
            <if test='setlway != null and setlway != "" and setlway == "1"'>
                AND a.setlway = #{setlway,jdbcType=VARCHAR}
            </if>
        ) a
        GROUP BY
        a.deptCode,
        a.deptName
    </select>
    <select id="queryQuadrantDipDoctorData" resultType="com.my.som.vo.newBusiness.dept.NewBusinessDeptVo">
        SELECT
        a.drCodg AS drCodg,
        a.drName AS drName,
        count(*) AS medicalTotalNum,
        IFNULL( sum( a.dipWt ), 0 ) AS totalWeight,
        IFNULL( round( sum( CASE WHEN a.isInGroup = 1 THEN a.dipWt ELSE 0 END )/ count( CASE WHEN a.isInGroup = 1 THEN 1 ELSE NULL END ), 2 ), 0 ) AS cmi,
        IFNULL( round( sum( a.sumfee ), 2 ), 0 ) AS sumfee,
        IFNULL(round(sum(a.ycCost), 2 ),  0  ) AS forecastAmount,
        IFNULL( round( sum( a.profitloss) - sum(a.preTotalCost ), 2 ), 0 ) AS forecastAmountDiff
        FROM
        (
            SELECT
            a.A11,
            a.B25C AS drCodg,
            a.B25N AS drName,
            b.dip_wt AS dipWt,
            b.grp_stas AS isInGroup,
            a.D01 AS sumfee,
            CASE WHEN a.D02 is NULL THEN 0 ELSE a.D02 END AS preTotalCost,
            d.totl_sco AS totlSco,
            a.A46C AS medPayWay,
            d.profitloss AS profitloss,
            d.forecast_fee AS ycCost,
            d.price AS price
            FROM
            som_hi_invy_bas_info a
            LEFT JOIN som_dip_grp_info b ON a.ID = b.SETTLE_LIST_ID
            LEFT JOIN som_dept c ON a.B16C = c.`CODE`
            LEFT JOIN som_dip_sco d ON a.ID = d.SETTLE_LIST_ID

            where 1=1
            <if test="seStartTime != null and seStartTime != '' and seEndTime != null and seEndTime !=''">
                AND a.D37 BETWEEN #{seStartTime,jdbcType=VARCHAR} AND CONCAT(#{seEndTime,jdbcType=VARCHAR},' 23:59:59')
            </if>
            <if test="begnDate != null and begnDate != '' and expiDate != null and expiDate != ''">
                AND a.B15 BETWEEN #{begnDate,jdbcType=VARCHAR} AND CONCAT(#{expiDate,jdbcType=VARCHAR},' 23:59:59')
            </if>
            <if test="hospitalId != null and hospitalId != ''">
                AND a.A01 = #{hospitalId,jdbcType=VARCHAR}
            </if>
        ) a
        GROUP BY
        a.drCodg,
        a.drName
    </select>
    <select id="queryQuadrantDrgDoctorData" resultType="com.my.som.vo.newBusiness.dept.NewBusinessDeptVo">
        SELECT
        a.drCodg AS drCodg,
        a.drName AS drName,
        count(*) AS medicalTotalNum,
        IFNULL( sum( a.drgWt ), 0 ) AS totalWeight,
        IFNULL( round( sum( a.drgWt )/ count( CASE WHEN a.isInGroup = 1 THEN 1 ELSE NULL END ), 2 ), 0 ) AS cmi,
        IFNULL( round( sum( a.sumfee ), 2 ), 0 ) AS sumfee,
        IFNULL(round( sum(( a.ycCost )),  2  ),0) AS forecastAmount,
        IFNULL( round(sum(( a.ycCost) - a.sumfee - a.preTotalCost),2),0) AS forecastAmountDiff
        FROM
        (
        SELECT
        a.A11,
        a.B25C AS drCodg,
        a.B25N AS drName,
        b.drg_wt AS drgWt,
        b.grp_stas AS isInGroup,
        a.D01 AS sumfee,
        CASE WHEN a.D02 is NULL THEN 0 ELSE a.D02 END AS preTotalCost,
        d.totl_sco AS totlSco,
        a.A46C AS medPayWay,
        d.price AS price,
        d.forecast_fee AS ycCost,
        d.profitloss AS profitloss
        FROM
        som_hi_invy_bas_info a
        LEFT JOIN som_drg_grp_info b ON a.ID = b.SETTLE_LIST_ID
        LEFT JOIN som_dept c ON a.B16C = c.`CODE`
        LEFT JOIN som_drg_sco d ON a.ID = d.SETTLE_LIST_ID

        where 1=1
        <if test="seStartTime != null and seStartTime != '' and seEndTime != null and seEndTime !=''">
            AND a.D37 BETWEEN #{seStartTime,jdbcType=VARCHAR} AND CONCAT(#{seEndTime,jdbcType=VARCHAR},' 23:59:59')
        </if>
        <if test="begnDate != null and begnDate != '' and expiDate != null and expiDate != ''">
            AND a.B15 BETWEEN #{begnDate,jdbcType=VARCHAR} AND CONCAT(#{expiDate,jdbcType=VARCHAR},' 23:59:59')
        </if>
        <if test="hospitalId != null and hospitalId != ''">
            AND a.A01 = #{hospitalId,jdbcType=VARCHAR}
        </if>
        ) a
        GROUP BY
        a.drCodg,
        a.drName
    </select>
    <select id="queryEntireAssessment" resultType="com.my.som.vo.newBusiness.dept.NewBusinessDeptVo">
        SELECT
        <choose>
        <when test="dateType == 1">
            SUBSTR(a.dscg_time,1,7) as yearMonth,
        </when>
        <when test="dateType == 2">
            SUBSTR(a.setl_end_time,1,7) as yearMonth,
        </when>
        </choose>
        IFNULL(
        round(
        SUM(CASE WHEN a.grp_stas = '1' THEN a.drg_wt ELSE NULL END) /
        NULLIF(COUNT(CASE WHEN a.grp_stas = '1' THEN 1 ELSE NULL END), 0),
        2
        ),
        0
        ) AS cmi,
        IFNULL(
        ROUND(
        SUM(
        CASE

        WHEN a.grp_stas = '1' THEN
        a.act_ipt
        ELSE NULL
        END / NULLIF(CONVERT(AES_DECRYPT(UNHEX(c.standard_ave_hosp_day),
        ${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY})
        USING utf8), 0)) /
        NULLIF(COUNT(CASE WHEN a.grp_stas = '1' THEN 1 ELSE NULL END), 0),
        2
        ),
        0
        ) AS timeIndex,
        IFNULL(
        ROUND(
        SUM(
        CASE

        WHEN a.grp_stas = '1' THEN
        a.ipt_sumfee
        ELSE NULL
        END / NULLIF(CONVERT(AES_DECRYPT(UNHEX(c.standard_avg_fee),
        ${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY})
        USING utf8), 0)) /
        NULLIF(COUNT(CASE WHEN a.grp_stas = '1' THEN 1 ELSE NULL END), 0),
        2
        ),
        0
        ) AS costIndex
        FROM som_drg_grp_info a
        LEFT JOIN som_drg_grp_rcd b ON a.SETTLE_LIST_ID = b.SETTLE_LIST_ID
        LEFT JOIN som_drg_standard c ON a.drg_codg = c.drg_codg
        AND substr(a.dscg_time, 1, 4) = c.STANDARD_YEAR
        AND a.HOSPITAL_ID = c.HOSPITAL_ID
        <where>
        <choose>
        <when test="dateType == 1">
            <if test="begnDate != null and begnDate != '' and expiDate != null and expiDate != ''">
                <![CDATA[
            AND SUBSTR(a.dscg_time,1,7) >= #{begnDate}
            AND SUBSTR(a.dscg_time,1,7) <= #{expiDate}
            ]]>
            </if>
        </when>
        <when test="dateType == 2">
            <if test="begnDate != null and begnDate != '' and expiDate != null and expiDate != ''">
                <![CDATA[
               AND SUBSTR(a.setl_end_time,1,7) >= #{begnDate}
               AND SUBSTR(a.setl_end_time,1,7) <= #{expiDate}
         ]]>
            </if>
        </when>
        </choose>
        AND a.HOSPITAL_ID = b.HOSPITAL_ID
        </where>
        GROUP BY
        yearMonth
        ORDER BY
        yearMonth
    </select>

</mapper>
