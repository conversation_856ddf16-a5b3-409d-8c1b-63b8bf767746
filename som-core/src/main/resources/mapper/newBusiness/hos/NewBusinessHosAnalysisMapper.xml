<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.som.mapper.newBusiness.hos.NewBusinessHosAnalysisMapper">

    <!-- 查询头部汇总数据 -->
    <select id="querySummaryData" resultType="com.my.som.vo.newBusiness.dept.NewBusinessDeptVo">
        SELECT COUNT(1) AS totalNum,
        IFNULL( COUNT( CASE WHEN a.ratioRange = 1 THEN 1 ELSE NULL END ), 0 ) AS ultrahighNum,
        IFNULL( COUNT( CASE WHEN a.grp_stas = 1 THEN 1 ELSE NULL END ), 0 ) AS drgInGroupMedcasVal,
        IFNULL( COUNT( CASE WHEN a.grp_stas = 0 THEN 1 ELSE NULL END ), 0 ) AS notInGroupNum,
        IFNULL( COUNT( CASE WHEN a.ratioRange = 2 THEN 1 ELSE NULL END ), 0 ) AS ultraLowNum,
        IFNULL( COUNT( CASE WHEN a.ratioRange = 3 THEN 1 ELSE NULL END ), 0 ) AS normalNumCount,
        IFNULL( COUNT( CASE WHEN a.ratioRange = 7 THEN 1 ELSE NULL END ), 0 ) AS ultraIncompleteNum,
        IFNULL( ROUND( SUM( a.ipt_sumfee ), 2 ), 0 ) AS sumfee,
        IFNULL( ROUND( SUM( a.ycCost ), 2 ), 0 ) AS forecastAmount,
        <choose>
        <when test="feeStas == 0">
            IFNULL( ROUND( IFNULL( SUM( a.profitloss ), 0 )  - IFNULL( SUM( a.pre_hosp_examfee ), 0 ), 2 ), 0 ) AS forecastAmountDiff,
        </when>
        <when test="feeStas == 1">
            IFNULL( ROUND( IFNULL( SUM( a.ycCost ), 0 ) - IFNULL( SUM( a.ipt_sumfee ), 0 ) - IFNULL( SUM( a.pre_hosp_examfee ), 0 ), 2 ), 0 ) AS forecastAmountDiff,
        </when>
    </choose>
       ROUND(sum(a.dip_wt) / count(1),2) cmi,
        ROUND(SUM(a.totl_sco),2) totalSco
        FROM
        (
        SELECT
        <if test="feeStas != null and feeStas != ''">
            <choose>
                <when test="feeStas == 0">
                    a.ipt_sumfee,
                    a.pre_hosp_examfee,
                    a.grp_stas,
                    a.dip_wt,
                    z.totl_sco,
                    z.ratioRange,
                    z.ycCost,
                    z.profitloss

                </when>
                <when test="feeStas == 1">
                    b.ipt_sumfee,
                    0 AS pre_hosp_examfee,
                    b.ratioRange,
                    b.ycCost
                </when>
            </choose>
        </if>
        FROM som_dip_grp_info a
        left JOIN som_hi_invy_bas_info f
        ON a.SETTLE_LIST_ID = f.ID
        <if test="enableSeAns != null and enableSeAns != '' and enableSeAns == 1">
            INNER JOIN som_setl_cas_crsp p
            ON f.K00 = p.K00
        </if>
        <if test="feeStas != null and feeStas != ''">
            <choose>
                <when test="feeStas == 0">
                    LEFT JOIN (
                    SELECT
                    b.SETTLE_LIST_ID,
                    b.totl_sco,
                    b.price,
                    b.profitloss AS profitloss,
                    b.forecast_fee AS ycCost,
                    b.sumfee AS sumfee,
                    dise_type AS ratioRange
                    FROM som_dip_sco b

                    ) z ON a.SETTLE_LIST_ID = z.SETTLE_LIST_ID

                </when>
                <when test="feeStas == 1">
                    INNER JOIN
                    (
                    SELECT a.medcas_codg,
                    a.adm_time,
                    a.dscg_time,
                    b.MED_TYPE AS ratioRange,
                    b.sumfee AS ipt_sumfee,
                    b.dfr_fee AS ycCost
                    FROM som_dip_grp_fbck a
                    LEFT JOIN som_fund_dfr_fbck b
                    ON a.rid_idt_codg = b.rid_idt_codg
                    AND a.HOSPITAL_ID = b.HOSPITAL_ID
                    <![CDATA[
                                        WHERE a.dscg_time >= #{begnDate} AND a.dscg_time <= #{expiDate}
                                        ]]>
                    <include refid="com.my.som.common.mapper.CommonMapper.fbAuth"/>
                    ) b
                    ON a.PATIENT_ID = b.medcas_codg
                    AND SUBSTR(a.adm_time,1,10) = b.adm_time
                    AND SUBSTR(a.dscg_time,1,10) = b.dscg_time
                </when>
            </choose>
        </if>
        <where>
            <if test="dateType == 1">
                AND a.dscg_time between #{begnDate,jdbcType=VARCHAR} and concat(#{expiDate,jdbcType=VARCHAR},' 23:59:59') <!-- 开始时间，结束时间 -->
            </if>
            <if test="dateType == 2">
                AND a.setl_end_time BETWEEN #{begnDate,jdbcType=VARCHAR} AND concat(#{expiDate,jdbcType=VARCHAR},' 23:59:59')
            </if>
            <if test='setlway != null and setlway != "" and setlway == "1"'>
                AND f.setlway = #{setlway,jdbcType=VARCHAR}
            </if>
        </where>
        <include refid="com.my.som.mapper.newBusiness.common.NewBusinessCommonAnalysisMapper.queryBusCommon"/>
        ) a
    </select>

    <!-- 查询错误病例 -->
    <select id="queryErrorData" resultType="com.my.som.vo.dipBusiness.DipGroupErrorMsgVo">
        SELECT grp_fale_rea AS notInGroupReason,
        grp_stas AS grpStas,
        COUNT(1)OVER() AS totalNum
        FROM som_dip_grp_rcd
        WHERE SETTLE_LIST_ID IN (
            SELECT SETTLE_LIST_ID
            FROM som_dip_grp_info a
            LEFT JOIN som_hi_invy_bas_info q
            ON a.SETTLE_LIST_ID = q.ID
            <if test='enableSeAns != null and enableSeAns != "" and enableSeAns == "1"'>
                INNER JOIN som_setl_cas_crsp p
                ON q.k00 = p.k00
            </if>
            <where>
                <if test="begnDate != null and begnDate != ''
                                                         and expiDate!=null and expiDate!=''">
                    <if test="dateType == 1">
                        AND a.dscg_time BETWEEN #{begnDate} AND CONCAT(#{expiDate},' 23:59:59')
                    </if>
                    <if test="dateType == 2">
                        AND a.setl_end_time BETWEEN #{begnDate} AND CONCAT(#{expiDate},' 23:59:59')
                    </if>
                </if>
                <if test="hospitalId != null and hospitalId != ''">
                    AND a.HOSPITAL_ID = #{hospitalId}
                </if>
                <if test='setlway != null and setlway != "" and setlway == "1"'>
                    AND q.setlway = #{setlway,jdbcType=VARCHAR}
                </if>
            </where>
        )
    </select>

    <!-- 查询排序数据 -->
    <select id="queryOrderData" resultType="com.my.som.vo.newBusiness.NewBusinessAnalysisVo">
        SELECT k.*
        FROM
        (
        SELECT
        <choose>
            <when test='analysisType == "dept"'>
                deptCode,deptName,
            </when>
            <when test='analysisType == "doctor"'>
                drCodg,drName,
            </when>
            <when test='analysisType == "dis"'>
                dipCodg,
                dipName,
                asstListAgeGrp,
                asstListTmorSevDeg,
                asstListDiseSevDeg,
            </when>
            <when test='analysisType == "med"'>
                id,patientId, name,
            </when>
        </choose>
        <if test="feeStas != null and feeStas != ''">
            <choose>
                <when test="feeStas == 0">

                    IFNULL( ROUND( sum( profitloss ) - sum( preTotalCost ), 2 ), 0 ) AS diff
                </when>
                <when test="feeStas == 1">

                    IFNULL( ROUND( sum( preCost ) - sum( inHosTotalCost ) - sum( preTotalCost ), 2 ), 0 ) AS diff
                </when>
            </choose>
        </if>

        FROM
        (
        SELECT
        g.*
        <if test="feeStas != null and feeStas != ''">
            <choose>
                <when test="feeStas == 0">
                    , g.ycCost AS preCost
                </when>
                <when test="feeStas == 1">
                    , g.ycCost AS preCost
                </when>
            </choose>
        </if>
        FROM
        (
        SELECT
        a.B25N AS drName,
        a.B25C AS drCodg,
        a.B16C AS deptCode,
        a.A11 AS name,
        a.id AS id,
        e.`NAME` AS deptName,
        a.A48 AS patientId,
        <if test="feeStas != null and feeStas != ''">
            <choose>
                <when test="feeStas == 0">
                    a.a54 AS insuredType,
                    a.D01 AS inHosTotalCost,
                    a.D02 AS preTotalCost,
                    b.dip_codg AS dipCodg,
                    b.DIP_NAME AS dipName,
                    b.asst_list_age_grp AS asstListAgeGrp,
                    b.asst_list_tmor_sev_deg AS asstListTmorSevDeg,
                    b.asst_list_dise_sev_deg AS asstListDiseSevDeg,
                    f.totl_sco AS refer_sco,
                    f.dise_type AS highLowType,
                    f.price,
                    f.forecast_fee AS ycCost,
                    f.profitloss AS profitloss
                </when>
                <when test="feeStas == 1">
                    z.INSURED_TYPE AS insuredType,
                    z.ipt_sumfee AS inHosTotalCost,
                    0 AS preTotalCost,
                    z.dip_codg AS dipCodg,
                    z.DIP_NAME AS dipName,
                    z.asst_list_age_grp AS asstListAgeGrp,
                    z.asst_list_dise_sev_deg AS asstListDiseSevDeg,
                    z.asst_list_tmor_sev_deg AS asstListTmorSevDeg,
                    z.setl_sco AS refer_sco,
                    z.MED_TYPE AS highLowType,
                    z.ycCost
                </when>
            </choose>
        </if>
        FROM
        som_hi_invy_bas_info a
        <if test="enableSeAns != null and enableSeAns != '' and enableSeAns == 1">
            INNER JOIN som_setl_cas_crsp p
            ON a.K00 = p.K00
        </if>
        LEFT JOIN som_dip_grp_info b ON a.ID = b.SETTLE_LIST_ID
        LEFT JOIN som_dip_standard c ON b.dip_codg = c.dip_codg
        AND SUBSTR( b.dscg_time, 1, 4 ) = c.STANDARD_YEAR
        AND b.is_used_asst_list = c.is_used_asst_list
        AND b.asst_list_age_grp = c.asst_list_age_grp
        AND b.asst_list_dise_sev_deg = c.asst_list_dise_sev_deg
        AND b.asst_list_tmor_sev_deg = c.asst_list_tmor_sev_deg
        AND b.HOSPITAL_ID = c.HOSPITAL_ID
        AND c.ACTIVE_FLAG = 1
        LEFT JOIN som_dept e ON a.B16C = e.`CODE`
        AND a.HOSPITAL_ID = e.HOSPITAL_ID
        LEFT JOIN som_dip_sco f ON a.id = f.SETTLE_LIST_ID

        <if test="feeStas != null and feeStas != ''">
            <choose>
                <when test="feeStas == 1">
                    INNER JOIN
                    (
                    SELECT
                    a.medcas_codg,
                    a.adm_time,
                    a.dip_codg,
                    a.DIP_NAME,
                    a.dscg_time,
                    a.used_asst_list,
                    a.asst_list_age_grp,
                    a.asst_list_dise_sev_deg,
                    a.asst_list_tmor_sev_deg,
                    b.INSURED_TYPE,
                    b.MED_TYPE,
                    b.setl_sco,
                    b.sumfee AS ipt_sumfee,
                    b.dfr_fee AS ycCost
                    FROM som_dip_grp_fbck a
                    LEFT JOIN som_fund_dfr_fbck b
                    ON a.rid_idt_codg = b.rid_idt_codg
                    AND a.HOSPITAL_ID = b.HOSPITAL_ID
<!--                    <![CDATA[-->
<!--                                            WHERE a.dscg_time >= #{begnDate,jdbcType=VARCHAR} AND a.dscg_time <= #{expiDate,jdbcType=VARCHAR}-->
<!--                                        ]]>-->
                    <if test="dateType == 1">
                        AND a.dscg_time BETWEEN #{begnDate,jdbcType=VARCHAR} and concat(#{expiDate,jdbcType=VARCHAR},' 23:59:59')
                    </if>
                    <if test="dateType == 2">
                        AND a.setl_time BETWEEN #{begnDate,jdbcType=VARCHAR} and concat(#{expiDate,jdbcType=VARCHAR},' 23:59:59')
                    </if>
                    <include refid="com.my.som.common.mapper.CommonMapper.fbAuth" />
                    ) z
                    ON b.PATIENT_ID = z.medcas_codg
                    AND SUBSTR(b.adm_time,1,10) = z.adm_time
                    AND SUBSTR(b.dscg_time,1,10) = z.dscg_time
                </when>
            </choose>
        </if>
        WHERE a.ACTIVE_FLAG = '1'
        <if test="dateType == 1">
            AND a.b15 BETWEEN #{begnDate,jdbcType=VARCHAR} and concat(#{expiDate,jdbcType=VARCHAR},' 23:59:59')
        </if>
        <if test="dateType == 2">
            AND a.D37 BETWEEN #{begnDate,jdbcType=VARCHAR} and concat(#{expiDate,jdbcType=VARCHAR},' 23:59:59')
        </if>
        <if test='setlway != null and setlway != "" and setlway == "1"'>
            AND a.setlway = #{setlway,jdbcType=VARCHAR}
        </if>
        <include refid="com.my.som.mapper.newBusiness.common.NewBusinessCommonAnalysisMapper.queryCommon" />
        ) g

        ) i
        GROUP BY
        <choose>
            <when test='analysisType == "dept"'>
                deptCode,deptName
            </when>
            <when test='analysisType == "doctor"'>
                drCodg,drName
            </when>
            <when test='analysisType == "dis"'>
                dipCodg,
                dipName,
                asstListAgeGrp,
                asstListTmorSevDeg,
                asstListDiseSevDeg
            </when>
            <when test='analysisType == "med"'>
                id,patientId, name
            </when>
        </choose>

        ) k
        WHERE <![CDATA[
            diff ${gtOrLtSymbol} 0
        ]]>
        ORDER BY diff ${sortSymbol}
        LIMIT ${limit}
    </select>
    <select id="queryCompeleteAndLogicErroNum" resultType="com.my.som.vo.dipBusiness.DipInGroupAnalysisVo">
        SELECT  IFNULL( count( CASE WHEN c.compeleteErrorNum != 0 THEN 1 ELSE NULL END ), 0 ) AS compeleteErrorNum,
                IFNULL( count( CASE WHEN c.logicErrorNum != 0 THEN 1 ELSE NULL END ), 0 ) AS logicErrorNum
        FROM som_drg_grp_info a
        LEFT JOIN (
            SELECT
                IFNULL( count( CASE WHEN err_type IN ( 'CE01', 'CE02', 'CE03', 'CE04' ) THEN 1 ELSE NULL END ), 0 ) AS
                compeleteErrorNum,
                IFNULL(count( CASE WHEN err_type IN ( 'LE01', 'LE02', 'LE03', 'LE04', 'LE05', 'LE06', 'LE07' ) THEN 1 ELSE
                NULL END ),0) AS logicErrorNum,
                SETTLE_LIST_ID
            FROM
            som_setl_invy_chk_err_rcd a
            GROUP BY
            SETTLE_LIST_ID
        ) c ON a.SETTLE_LIST_ID = c.SETTLE_LIST_ID
        LEFT JOIN som_hi_invy_bas_info q
        ON a.SETTLE_LIST_ID = q.ID
        <if test='enableSeAns != null and enableSeAns != "" and enableSeAns == "1"'>
            INNER JOIN som_setl_cas_crsp p
            ON q.k00 = p.k00
        </if>
        <where>
            <if test="begnDate != null and begnDate != '' and expiDate!=null and expiDate!=''">
                AND a.dscg_time BETWEEN #{begnDate} AND CONCAT(#{expiDate},' 23:59:59')
            </if>
            <if test='setlway != null and setlway != "" and setlway == "1"'>
                AND q.setlway = #{setlway,jdbcType=VARCHAR}
            </if>
        </where>
    </select>
    <!-- 查询趋势数据 -->
    <select id="queryTrendData" resultType="com.my.som.vo.newBusiness.dept.NewBusinessDeptVo">
        select COUNT(1) AS totalNum,
        yearMonth,
        IFNULL( COUNT( CASE WHEN a.grp_stas = 1 THEN 1 ELSE NULL END ), 0 ) AS drgInGroupMedcasVal,
        IFNULL( COUNT( CASE WHEN a.grp_stas = 0 THEN 1 ELSE NULL END ), 0 ) AS notInGroupNum,
        IFNULL( COUNT( CASE WHEN a.ratioRange = 1 THEN 1 ELSE NULL END ), 0 ) AS ultrahighNum,
        IFNULL( COUNT( CASE WHEN a.ratioRange = 2 THEN 1 ELSE NULL END ), 0 ) AS ultraLowNum,
        IFNULL( ROUND( SUM( a.ipt_sumfee ), 2 ), 0 ) AS sumfee,
        IFNULL( ROUND( SUM( a.ycCost ), 2 ), 0 ) AS forecastAmount,
        IFNULL( ROUND( IFNULL( SUM( a.ycCost ), 0 ) - IFNULL( SUM( a.ipt_sumfee ), 0 ) - IFNULL( SUM( a.pre_hosp_examfee ), 0 ), 2 ), 0 ) AS
        forecastAmountDiff
        from
        (
        select a.ipt_sumfee,
               a.pre_hosp_examfee,
        a.grp_stas,
        <choose>
            <when test="dateType == 1">
                SUBSTR(a.dscg_time,1,7) as yearMonth,
            </when>
            <when test="dateType == 2">
                SUBSTR(a.setl_end_time,1,7) as yearMonth,
            </when>
        </choose>
        z.ratioRange,
        z.ycCost,
        z.profitloss AS profitloss
        from som_dip_grp_info a
        left join
        (
             SELECT b.SETTLE_LIST_ID,
        b.dise_type AS ratioRange,
             b.forecast_fee AS ycCost,
             b.price,
             b.profitloss AS profitloss,
             b.sumfee AS sumfee
             FROM som_dip_sco b
        ) z
        on a.SETTLE_LIST_ID = z.SETTLE_LIST_ID
        left join som_hi_invy_bas_info f
        on a.settle_list_id = f.id
        <where>
            <choose>
                <when test="dateType == 1">
                    <if test="begnDate != null and begnDate != '' and expiDate != null and expiDate != ''">
                        <![CDATA[
                            and SUBSTR(a.dscg_time,1,7) >= #{begnDate}
                            and SUBSTR(a.dscg_time,1,7) <= #{expiDate}
                            ]]>
                    </if>
                </when>
                <when test="dateType == 2">
                    <if test="begnDate != null and begnDate != '' and expiDate != null and expiDate != ''">
                        <![CDATA[
                            and SUBSTR(a.setl_end_time,1,7) >= #{begnDate}
                            and SUBSTR(a.setl_end_time,1,7) <= #{expiDate}
                            ]]>
                    </if>
                </when>
            </choose>
            <if test="hospitalId != null and hospitalId != ''">
                AND a.HOSPITAL_ID = #{hospitalId,jdbcType=VARCHAR}
            </if>
            <if test='setlway != null and setlway != "" and setlway == "1"'>
                AND f.setlway = #{setlway,jdbcType=VARCHAR}
            </if>
        </where>
        ) a
        group by yearMonth
        order by yearMonth
    </select>


</mapper>
