<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.som.mapper.newBusiness.feedbackAnalyse.FeedbackAnalyseMapper">

    <!--查询数据变化趋势-->
    <select id="queryDataThread" resultType="com.my.som.vo.newBusiness.feedbackAnalyse.FeedbackAnalyseVo">
        SELECT
        SUM(A.sumfee) AS sumfee,<!--总费用-->
        SUM(A.pool_fee) AS poolFee, <!--统筹费用-->
        SUM(A.dis_gp_sumfee) AS disGpSumfee, <!--分组总费用-->
        SUM(A.dis_gp_pool_fee) AS disGpPoolFee, <!--分组统筹费用-->
        COUNT(1) AS totalCount, <!--总人次-->
        SUBSTR(<PERSON><PERSON>TL_TIME,1,7) as ym <!--期号-->
        FROM som_fbck_ana A
        <where>

        </where>
        GROUP BY SUBSTR(A.SETL_TIME,1,7)

    </select>
    <select id="selectSummary" resultType="com.my.som.vo.newBusiness.feedbackAnalyse.FeedbackAnalyseVo">
        SELECT SUM(sumfee) as sumfee,<!-- 总费用 -->
        SUM(staffTotalCost) as staffTotalCost,<!-- 职工总费用 -->
        SUM(residentTotalCost) as residentTotalCost,<!-- 居民总费用 -->
        SUM(overallTotalCost) as overallTotalCost,<!-- 统筹总费用 -->
        SUM(staffOverallTotalCost) as staffOverallTotalCost,<!-- 职工统筹总费用 -->
        SUM(residentOverallTotalCost) as residentOverallTotalCost,<!-- 居民统筹总费用 -->
        SUM(groupOverallTocalCost) as groupOverallTocalCost,<!-- 病组统筹总费用 -->
        SUM(staffGroupOverallTotalCost) as staffGroupOverallTotalCost,<!-- 职工病组统筹总费用 -->
        SUM(residentGroupOverallTotalCos) as residentGroupOverallTotalCost,<!-- 居民病组统筹总费用 -->
        IFNULL(SUM(groupOverallTocalCost) - SUM(overallTotalCost),0) as diffTocalCost,<!-- 差异总费用 -->
        IFNULL(SUM(staffGroupOverallTotalCost) - SUM(staffOverallTotalCost),0) as staffDiffTotalCost,<!-- 职工差异总费用 -->
        IFNULL(SUM(residentGroupOverallTotalCos) - SUM(residentOverallTotalCost),0) as
        residentDiffTotalCost <!-- 居民差异总费用 -->
        FROM
        (
        SELECT SUM(IFNULL(sumfee,0)) as sumfee,
        CASE WHEN INSURED_TYPE=1 THEN SUM(IFNULL(sumfee,0)) ELSE 0 END as staffTotalCost,
        CASE WHEN INSURED_TYPE=2 THEN SUM(IFNULL(sumfee,0)) ELSE 0 END as residentTotalCost,
        SUM(IFNULL(pool_fee,0)) as overallTotalCost,
        CASE WHEN INSURED_TYPE=1 THEN SUM(IFNULL(pool_fee,0)) ELSE 0 END as staffOverallTotalCost,
        CASE WHEN INSURED_TYPE=2 THEN SUM(IFNULL(pool_fee,0)) ELSE 0 END as residentOverallTotalCost,
        SUM(IFNULL(dis_gp_pool_fee,0)) as groupOverallTocalCost,
        CASE WHEN INSURED_TYPE=1 THEN SUM(IFNULL(dis_gp_pool_fee,0)) ELSE 0 END as staffGroupOverallTotalCost,
        CASE WHEN INSURED_TYPE=2 THEN SUM(IFNULL(dis_gp_pool_fee,0)) ELSE 0 END as residentGroupOverallTotalCos
        from som_fbck_ana
        <where>
            <if test="seStartTime != null and seStartTime != '' and seEndTime != null and seEndTime != ''">
                SUBSTR(SETL_TIME,1,7) between (#{seStartTime,jdbcType=VARCHAR}) and
                concat(#{seEndTime,jdbcType=VARCHAR},'23:59:59')
            </if>
        </where>
        GROUP BY INSURED_TYPE
        ) a
    </select>
    <select id="selectTotalCost" resultType="com.my.som.vo.newBusiness.feedbackAnalyse.FeedbackAnalyseVo">
        SELECT
        SUM(A.pool_fee) AS overallTotalCost, <!--统筹费用-->
        SUM(A.dis_gp_pool_fee) AS groupOverallTocalCost <!--分组统筹费用-->
        FROM som_fbck_ana A
        where SUBSTR(SETL_TIME,1,7) = #{setlTime,jdbcType=VARCHAR}
    </select>
    <select id="selectParTypePie" resultType="com.my.som.vo.newBusiness.feedbackAnalyse.FeedbackAnalyseVo">
        SELECT COUNT(1) as count,
        SUM(IFNULL(sumfee,0)) as sumfee,
        SUM(IFNULL(pool_fee,0)) as overallTotalCost,
        SUM(IFNULL(dis_gp_pool_fee,0)) as groupOverallTocalCost,
        CASE WHEN SUM(IFNULL(dis_gp_pool_fee,0)) - SUM(IFNULL(pool_fee,0)) &lt; 0 THEN
        SUM(IFNULL(dis_gp_pool_fee,0)) - SUM(IFNULL(pool_fee,0)) ELSE 0 END as diffTocalCost,
        setl_patn_type as setlPatnType
        FROM som_fbck_ana
        <where>
            <if test="seStartTime != null and seStartTime != '' and seEndTime != null and seEndTime != ''">
                SUBSTR(SETL_TIME,1,7) between (#{seStartTime,jdbcType=VARCHAR}) and
                concat(#{seEndTime,jdbcType=VARCHAR},'23:59:59')
            </if>
        </where>
        GROUP BY setl_patn_type
    </select>
    <select id="selectOrderData" resultType="com.my.som.vo.newBusiness.feedbackAnalyse.FeedbackAnalyseVo">
        SELECT SUM(dis_gp_pool_fee) - SUM(pool_fee) as diffTocalCost, ${groupField} as deptName
        FROM som_fbck_ana
        <where>
            <if test="seStartTime != null and seStartTime != '' and seEndTime != null and seEndTime != ''">
                SUBSTR(SETL_TIME,1,7) between (#{seStartTime,jdbcType=VARCHAR}) and
                concat(#{seEndTime,jdbcType=VARCHAR},'23:59:59')
            </if>
        </where>
        <if test="groupField != null and groupField != ''">
            group by ${groupField}
        </if>
        ORDER BY diffTocalCost ${orderSign}
        limit 5
    </select>
    <select id="queryDropdown" resultType="com.my.som.vo.newBusiness.NewBusinessCommonVo">
        select distinct
        dr_name as value,
        dr_name as label
        FROM som_fbck_ana
    </select>
    <select id="queryDeptDropdown" resultType="com.my.som.vo.newBusiness.NewBusinessCommonVo">
        select distinct
        DEPT_NAME as value,
        DEPT_NAME as label
        FROM som_fbck_ana
    </select>
    <select id="queryFeedAnalysis" resultType="com.my.som.vo.newBusiness.feedbackAnalyse.FeedbackDimeVo">
        SELECT
        a.*,
        a.groupOverallCostAll - a.overallCostAll AS profitAll
        FROM
        (
        SELECT
        dr_name AS drName,
        DEPT_NAME AS deptName,
        count( 1 ) AS CountMedical,
        sum( setl_pt_num ) AS selPointAll,
        sum( sumfee ) AS totalCostAll,
        sum( pool_fee ) AS overallCostAll,
        sum( dis_gp_sumfee ) AS groupTotalCostAll,
        sum( dis_gp_pool_fee ) AS groupOverallCostAll
        FROM
        som_fbck_ana
        where 1=1
        <if test="drName != null and drName != ''">
            AND dr_name LIKE CONCAT('%',#{drName},'%')
        </if>
        <if test="seStartTime != null and seStartTime != '' and seEndTime != null and seEndTime != ''">
            AND SETL_TIME between #{seStartTime,jdbcType=VARCHAR} and concat(#{seEndTime,jdbcType=VARCHAR},' 23:59:59')
        </if>
        <if test="begnDate != null and begnDate != '' and expiDate != null and expiDate != ''">
            AND DSCG_TIME between #{begnDate,jdbcType=VARCHAR} and concat(#{expiDate,jdbcType=VARCHAR},' 23:59:59')
        </if>
        GROUP BY
        dr_name,
        DEPT_NAME
        ) a
    </select>

    <select id="selectFeedbackDeptData" resultType="com.my.som.vo.newBusiness.feedbackAnalyse.FeedbackDimeVo">
        select
        DEPT_NAME AS deptName,
        COUNT(1) AS countMedical,
        ROUND(SUM(setl_pt_num),4) AS selPointAll,
        ROUND(SUM(sumfee),4) AS totalCostAll,
        ROUND(SUM(pool_fee),4) AS overallCostAll,
        ROUND(SUM(dis_gp_sumfee),4) AS groupTotalCostAll,
        ROUND(SUM(dis_gp_pool_fee),4) AS groupOverallCostAll,
        ROUND(SUM(dis_gp_pool_fee - pool_fee),4) AS profitAll
        from som_fbck_ana
        where 1 = 1
        <if test="begnDate != null and begnDate != '' and expiDate != null and expiDate != ''">
            AND DSCG_TIME BETWEEN #{begnDate} AND CONCAT(#{expiDate},' 23:59:59')
        </if>
        <if test="seStartTime != null and seStartTime != '' and seEndTime != null and seEndTime != ''">
            AND SETL_TIME BETWEEN #{seStartTime} AND CONCAT(#{seEndTime},' 23:59:59')
        </if>
        <if test="deptName != null and deptName != ''">
            AND DEPT_NAME = #{deptName}
        </if>
        GROUP BY DEPT_NAME
    </select>

    <select id="selectFeedbackDeptOptions" resultType="com.my.som.vo.newBusiness.NewBusinessCommonVo">
        select DEPT_NAME AS label,
               DEPT_NAME AS value
        from som_fbck_ana
        GROUP BY DEPT_NAME
    </select>

    <select id="selectFeedbackPersonData" resultType="com.my.som.vo.newBusiness.feedbackAnalyse.FeedbackAnalysisVo">
        select
        dis_gp_codg as disGpCodg,
        dis_gp_name as disGpName,
        asst_list_tmor_sev_deg as asstListTmorSevDeg,
        asst_list_age_grp as asstListAgeGrp,
        asst_list_dise_sev_deg as asstListDiseSevDeg,
        MEDCASNO as medcasno,
        PSN_NAME as psnName,
        ADM_TIME as admTime,
        DSCG_TIME as dscgTime,
        SETL_TIME as setlTime,
        DEPT_NAME as deptName,
        dr_name as drName,
        setl_pt_num as setlPtNum,
        pt_num_fee as ptNumFee,
        sumfee as sumfee,
        pool_fee as poolFee,
        dis_gp_sumfee as disGpSumfee,
        dis_gp_pool_fee as disGpPoolFee,
        setl_patn_type as setlPatnType,
        CASE WHEN INSURED_TYPE = '1' THEN '城职'
        WHEN INSURED_TYPE = '2' THEN '城乡'
        ELSE '其他' END as insuredType,
        ROUND(dis_gp_pool_fee - pool_fee,4) as profit
        from som_fbck_ana
        where 1 = 1
        <if test="begnDate != null and begnDate != '' and expiDate != null and expiDate != ''">
            AND DSCG_TIME BETWEEN #{begnDate} AND CONCAT(#{expiDate},' 23:59:59')
        </if>
        <if test="seStartTime != null and seStartTime != '' and seEndTime != null and seEndTime != ''">
            AND SETL_TIME BETWEEN #{seStartTime} AND CONCAT(#{seEndTime},' 23:59:59')
        </if>
        <if test="deptName != null and deptName != ''">
            AND DEPT_NAME = #{deptName}
        </if>
        <if test="drName != null and drName != ''">
            AND dr_name = #{drName}
        </if>
        <if test="psnName != null and psnName != ''">
            AND PSN_NAME like concat('%',#{psnName},'%')
        </if>
        <if test="dipCodg != null and dipCodg != ''">
            AND dis_gp_codg = #{dipCodg}
        </if>
    </select>

    <select id="selectFeedbackDiseaseData" resultType="com.my.som.vo.newBusiness.feedbackAnalyse.FeedbackDimeVo">
        select
        dis_gp_codg AS dipCodg,
        <if test="queryType == 2">
            DEPT_NAME as deptName,
        </if>
        COUNT(1) AS countMedical,
        ROUND(SUM(setl_pt_num),4) AS selPointAll,
        ROUND(SUM(sumfee),4) AS totalCostAll,
        ROUND(SUM(pool_fee),4) AS overallCostAll,
        ROUND(SUM(dis_gp_sumfee),4) AS groupTotalCostAll,
        ROUND(SUM(dis_gp_pool_fee),4) AS groupOverallCostAll,
        ROUND(SUM(dis_gp_pool_fee - pool_fee),4) AS profitAll,
        ROUND(SUM(pool_fee)/COUNT(1),4) AS overallCostAllAvg,
        ROUND(SUM(sumfee)/COUNT(1),4) AS totalCostAllAvg
        from som_fbck_ana
        where 1 = 1
        <if test="begnDate != null and begnDate != '' and expiDate != null and expiDate != ''">
            AND DSCG_TIME BETWEEN #{begnDate} AND CONCAT(#{expiDate},' 23:59:59')
        </if>
        <if test="seStartTime != null and seStartTime != '' and seEndTime != null and seEndTime != ''">
            AND SETL_TIME BETWEEN #{seStartTime} AND CONCAT(#{seEndTime},' 23:59:59')
        </if>
        <if test="dipCodg != null and dipCodg != ''">
            AND dis_gp_codg = #{dipCodg}
        </if>
        <if test="queryType == 1">
            GROUP BY dis_gp_codg
        </if>
        <if test="queryType == 2">
            GROUP BY DEPT_NAME,dis_gp_codg
        </if>
    </select>

    <select id="selectFeedbackDiseaseOptions" resultType="com.my.som.vo.newBusiness.NewBusinessCommonVo">
        select dis_gp_codg AS label,
               dis_gp_codg AS value
        from som_fbck_ana
        GROUP BY dis_gp_codg
    </select>
    <select id="selectFeedbackDataRight"
            resultType="com.my.som.vo.newBusiness.feedbackAnalyse.FeedbackDataComparisonVo">
        SELECT a.deptName AS deptName,
        IFNULL(count(*), 0) AS totalCases,
        count(DISTINCT a.disGpCodg) AS groupNum,
        IFNULL(count(CASE WHEN a.isInGroup = 1 THEN 1 ELSE NULL END), 0) AS isInGroup,
        IFNULL( round(count( CASE WHEN a.isInGroup = 1 THEN 1 ELSE NULL END )/count(*)*100,2),0) AS inGroupRate,
        IFNULL(count(CASE WHEN a.isInGroup = 2 OR a.isInGroup = 0 THEN 1 ELSE NULL END),
        0) noInGroup,
        IFNULL(count(CASE WHEN a.setlPatnType = '高倍率' THEN 1 ELSE NULL END), 0) AS highCases,
        IFNULL(count(CASE WHEN a.setlPatnType = '低倍率' THEN 1 ELSE NULL END), 0) AS lowCases,
        IFNULL(count(CASE WHEN a.setlPatnType = '正常病例' THEN 1 ELSE NULL END), 0) AS normalCases,
        IFNULL(count(CASE WHEN a.setlPatnType = '特殊病例' THEN 1 ELSE NULL END), 0) AS specialCases,
        IFNULL(count(CASE WHEN a.setlPatnType = '非稳定病种' THEN 1 ELSE NULL END), 0) AS unstableDisease,
        IFNULL(count(CASE WHEN a.setlPatnType = '康复病例' THEN 1 ELSE NULL END), 0) AS recoverCases,
        IFNULL(count(CASE WHEN a.setlPatnType = '特病单议-扣费病例' THEN 1 ELSE NULL END),
        0) AS specialDeductionCases,
        IFNULL(count(CASE WHEN a.setlPatnType = '住院过程不完整病例' THEN 1 ELSE NULL END),
        0) AS incomHospitalCases,
        IFNULL(round(sum(a.actIpt) / count(*), 1), 0) AS aveHospitalDays,
        IFNULL(round(sum(a.iptSumfee) / count(*), 2), 0) AS aveHospitalCost,
        IFNULL(round((sum(a.drugfee) / sum(a.iptSumfee)) * 100, 2),
        0) AS drugProportion,
        IFNULL(round((sum(a.mcsFee) / sum(a.iptSumfee)) * 100, 2),
        0) AS ConsumRatio,
        IFNULL(sum(a.dipWt), 0) AS totalDipWeight,
        IFNULL(round(sum(a.dipWt) / count(CASE WHEN a.isInGroup = 1 THEN 1 ELSE NULL END) * 100, 2),
        0) AS cmi,
        IFNULL(round(sum(CASE WHEN a.isInGroup = 1 THEN a.actIpt ELSE NULL END / NULLIF(
        CONVERT(AES_DECRYPT(UNHEX(a.dipStandardIptDays), ${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY})
        USING
        utf8), 0)) / NULLIF(count(CASE WHEN a.isInGroup = 1 THEN 1 ELSE NULL END), 0), 2),
        0) AS timeIndex,
        IFNULL(round(sum(CASE WHEN a.isInGroup = 1 THEN a.iptSumfee ELSE NULL END / NULLIF(
        CONVERT(AES_DECRYPT(UNHEX(a.dipStandardInpf), ${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY})
        USING
        utf8), 0)) / NULLIF(count(CASE WHEN a.isInGroup = 1 THEN 1 ELSE NULL END), 0), 2),
        0) AS costIndex,
        IFNULL(round((count(CASE WHEN a.setlPatnType = '高倍率' THEN 1 ELSE NULL END) / count(*)) * 100, 2), 0) AS
        highRate,
        IFNULL(round((count(CASE WHEN a.setlPatnType = '低倍率' THEN 1 ELSE NULL END) / count(*)) * 100, 2), 0) AS
        lowRate,
        IFNULL(round((count(CASE WHEN a.setlPatnType = '正常病例' THEN 1 ELSE NULL END) / count(*)) * 100, 2), 0) AS
        normalRate,
        IFNULL(round((count(CASE WHEN a.setlPatnType = '特殊病例' THEN 1 ELSE NULL END) / count(*)) * 100, 2), 0) AS
        specialRate,
        IFNULL(round((count(CASE WHEN a.setlPatnType = '非稳定病种' THEN 1 ELSE NULL END) / count(*)) * 100, 2), 0) AS
        unstableRate,
        IFNULL(round((count(CASE WHEN a.setlPatnType = '康复病例' THEN 1 ELSE NULL END) / count(*)) * 100, 2), 0) AS
        recoverRate,
        IFNULL(round((count(CASE WHEN a.setlPatnType = '特病单议-扣费病例' THEN 1 ELSE NULL END) / count(*)) * 100, 2),
        0) AS specialDeductionRate,
        IFNULL(round((count(CASE WHEN a.setlPatnType = '住院过程不完整病例' THEN 1 ELSE NULL END) / count(*)) * 100, 2),
        0) AS incomHospitalRate,
        IFNULL(sum(a.poolFee), 0) AS sumfee,
        IFNULL(sum(a.disGpPoolFee), 0) AS feedbackAmount,
        IFNULL(sum(a.disGpPoolFee) - sum(a.poolFee), 0) AS amountDifference,
        IFNULL(round(sum(a.disGpPoolFee) / sum(a.poolFee), 2),
        0) AS oe
        FROM (
        SELECT dis_gp_codg AS disGpCodg,
        dis_gp_name AS disGpName,
        MDTRT_ID AS mdtrtId,
        MEDCASNO AS medcasno,
        PSN_NAME AS psnName,
        ADM_TIME AS admTime,
        DSCG_TIME AS dscgTime,
        SETL_TIME AS setlTime,
        DEPT_NAME AS deptName,
        dr_name AS drName,
        setl_pt_num AS setlPtNum,
        pt_num_fee AS ptNumFee,
        sumfee AS sumfee,
        pool_fee AS poolFee,
        dis_gp_sumfee AS disGpSumfee,
        dis_gp_pool_fee AS disGpPoolFee,
        setl_patn_type AS setlPatnType,
        is_in_group AS isInGroup,
        INSURED_TYPE AS insuredType,
        b.dscg_caty_codg_inhosp AS deptCode,
        b.act_ipt AS actIpt,
        b.ipt_sumfee AS iptSumfee,
        b.drugfee AS drugfee,
        b.mcs_fee AS mcsFee,
        b.dip_wt AS dipWt,
        td.dip_standard_ipt_days AS dipStandardIptDays,
        td.dip_standard_inpf AS dipStandardInpf
        FROM som_fbck_ana bk
        LEFT JOIN som_dip_grp_info b ON bk.MEDCASNO = b.PATIENT_ID
        LEFT JOIN som_dip_standard td ON bk.dis_gp_codg = td.dip_codg
        where 1=1
        <if test="deptName != null and deptName != ''">
            and DEPT_NAME LIKE concat('%',#{deptName,jdbcType=VARCHAR},'%')
        </if>
        <if test="begnDate != null and begnDate != ''
                    and expiDate != null and expiDate != '' ">
            and DSCG_TIME BETWEEN #{begnDate,jdbcType=VARCHAR} and #{expiDate,jdbcType=VARCHAR}
        </if>
        <if test="seStartTime != null and seStartTime != ''
                    and seEndTime != null and seEndTime != '' ">
            and SETL_TIME BETWEEN #{seStartTime,jdbcType=VARCHAR} and #{seEndTime,jdbcType=VARCHAR}
        </if>
        ) a
        GROUP BY a.deptName
    </select>
    <select id="selectFeedbackDoctor"
            resultType="com.my.som.vo.newBusiness.feedbackAnalyse.FeedbackDataComparisonVo">
        SELECT a.drName AS drName,
        IFNULL(count(*), 0) AS totalCases,
        count(DISTINCT a.disGpCodg) AS groupNum,
        IFNULL(count(CASE WHEN a.isInGroup = 1 THEN 1 ELSE NULL END), 0) AS isInGroup,
        IFNULL( round(count( CASE WHEN a.isInGroup = 1 THEN 1 ELSE NULL END )/count(*)*100,2),0) AS inGroupRate,
        IFNULL(count(CASE WHEN a.isInGroup = 2 OR a.isInGroup = 0 THEN 1 ELSE NULL END),
        0) noInGroup,
        IFNULL(count(CASE WHEN a.setlPatnType = '高倍率' THEN 1 ELSE NULL END), 0) AS highCases,
        IFNULL(count(CASE WHEN a.setlPatnType = '低倍率' THEN 1 ELSE NULL END), 0) AS lowCases,
        IFNULL(count(CASE WHEN a.setlPatnType = '正常病例' THEN 1 ELSE NULL END), 0) AS normalCases,
        IFNULL(count(CASE WHEN a.setlPatnType = '特殊病例' THEN 1 ELSE NULL END), 0) AS specialCases,
        IFNULL(count(CASE WHEN a.setlPatnType = '非稳定病种' THEN 1 ELSE NULL END), 0) AS unstableDisease,
        IFNULL(count(CASE WHEN a.setlPatnType = '康复病例' THEN 1 ELSE NULL END), 0) AS recoverCases,
        IFNULL(count(CASE WHEN a.setlPatnType = '特病单议-扣费病例' THEN 1 ELSE NULL END),
        0) AS specialDeductionCases,
        IFNULL(count(CASE WHEN a.setlPatnType = '住院过程不完整病例' THEN 1 ELSE NULL END),
        0) AS incomHospitalCases,
        IFNULL(round(sum(a.actIpt) / count(*), 1), 0) AS aveHospitalDays,
        IFNULL(round(sum(a.iptSumfee) / count(*), 2), 0) AS aveHospitalCost,
        IFNULL(round((sum(a.drugfee) / sum(a.iptSumfee)) * 100, 2),
        0) AS drugProportion,
        IFNULL(round((sum(a.mcsFee) / sum(a.iptSumfee)) * 100, 2),
        0) AS ConsumRatio,
        IFNULL(sum(a.dipWt), 0) AS totalDipWeight,
        IFNULL(round(sum(a.dipWt) / count(CASE WHEN a.isInGroup = 1 THEN 1 ELSE NULL END) * 100, 2),
        0) AS cmi,
        IFNULL(round(sum(CASE WHEN a.isInGroup = 1 THEN a.actIpt ELSE NULL END / NULLIF(
        CONVERT(AES_DECRYPT(UNHEX(a.dipStandardIptDays), ${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY})
        USING
        utf8), 0)) / NULLIF(count(CASE WHEN a.isInGroup = 1 THEN 1 ELSE NULL END), 0), 2),
        0) AS timeIndex,
        IFNULL(round(sum(CASE WHEN a.isInGroup = 1 THEN a.iptSumfee ELSE NULL END / NULLIF(
        CONVERT(AES_DECRYPT(UNHEX(a.dipStandardInpf), ${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY})
        USING
        utf8), 0)) / NULLIF(count(CASE WHEN a.isInGroup = 1 THEN 1 ELSE NULL END), 0), 2),
        0) AS costIndex,
        IFNULL(round((count(CASE WHEN a.setlPatnType = '高倍率' THEN 1 ELSE NULL END) / count(*)) * 100, 2), 0) AS
        highRate,
        IFNULL(round((count(CASE WHEN a.setlPatnType = '低倍率' THEN 1 ELSE NULL END) / count(*)) * 100, 2), 0) AS
        lowRate,
        IFNULL(round((count(CASE WHEN a.setlPatnType = '正常病例' THEN 1 ELSE NULL END) / count(*)) * 100, 2), 0) AS
        normalRate,
        IFNULL(round((count(CASE WHEN a.setlPatnType = '特殊病例' THEN 1 ELSE NULL END) / count(*)) * 100, 2), 0) AS
        specialRate,
        IFNULL(round((count(CASE WHEN a.setlPatnType = '非稳定病种' THEN 1 ELSE NULL END) / count(*)) * 100, 2), 0) AS
        unstableRate,
        IFNULL(round((count(CASE WHEN a.setlPatnType = '康复病例' THEN 1 ELSE NULL END) / count(*)) * 100, 2), 0) AS
        recoverRate,
        IFNULL(round((count(CASE WHEN a.setlPatnType = '特病单议-扣费病例' THEN 1 ELSE NULL END) / count(*)) * 100, 2),
        0) AS specialDeductionRate,
        IFNULL(round((count(CASE WHEN a.setlPatnType = '住院过程不完整病例' THEN 1 ELSE NULL END) / count(*)) * 100, 2),
        0) AS incomHospitalRate,
        IFNULL(sum(a.poolFee), 0) AS sumfee,
        IFNULL(sum(a.disGpPoolFee), 0) AS feedbackAmount,
        IFNULL(sum(a.disGpPoolFee) - sum(a.poolFee), 0) AS amountDifference,
        IFNULL(round(sum(a.disGpPoolFee) / sum(a.poolFee), 2),
        0) AS oe
        FROM (
        SELECT dis_gp_codg AS disGpCodg,
        dis_gp_name AS disGpName,
        MDTRT_ID AS mdtrtId,
        MEDCASNO AS medcasno,
        PSN_NAME AS psnName,
        ADM_TIME AS admTime,
        DSCG_TIME AS dscgTime,
        SETL_TIME AS setlTime,
        DEPT_NAME AS deptName,
        dr_name AS drName,
        setl_pt_num AS setlPtNum,
        pt_num_fee AS ptNumFee,
        sumfee AS sumfee,
        pool_fee AS poolFee,
        dis_gp_sumfee AS disGpSumfee,
        dis_gp_pool_fee AS disGpPoolFee,
        setl_patn_type AS setlPatnType,
        is_in_group AS isInGroup,
        INSURED_TYPE AS insuredType,
        b.dscg_caty_codg_inhosp AS deptCode,
        b.act_ipt AS actIpt,
        b.ipt_sumfee AS iptSumfee,
        b.drugfee AS drugfee,
        b.mcs_fee AS mcsFee,
        b.dip_wt AS dipWt,
        td.dip_standard_ipt_days AS dipStandardIptDays,
        td.dip_standard_inpf AS dipStandardInpf
        FROM som_fbck_ana bk
        LEFT JOIN som_dip_grp_info b ON bk.MEDCASNO = b.PATIENT_ID
        LEFT JOIN som_dip_standard td ON bk.dis_gp_codg = td.dip_codg
        where 1=1
        <if test="drName != null and drName != ''">
            and dr_name LIKE concat('%',#{drName,jdbcType=VARCHAR},'%')
        </if>
        <if test="begnDate != null and begnDate != ''
                    and expiDate != null and expiDate != '' ">
            and DSCG_TIME BETWEEN #{begnDate,jdbcType=VARCHAR} and #{expiDate,jdbcType=VARCHAR}
        </if>
        <if test="seStartTime != null and seStartTime != ''
                    and seEndTime != null and seEndTime != '' ">
            and SETL_TIME BETWEEN #{seStartTime,jdbcType=VARCHAR} and #{seEndTime,jdbcType=VARCHAR}
        </if>
        ) a
        GROUP BY a.drName
    </select>
</mapper>
