<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.som.mapper.newBusiness.patient.NewDipBusinessPatientAnalysisMapper">

    <!-- 查询患者基本信息 -->
    <select id="queryPatientBasicInfoData" resultType="com.my.som.vo.newBusiness.patient.NewBusinessPatientVo">
        SELECT a.SETTLE_LIST_ID AS settleListId,
               a.`NAME` AS name,
               a.act_ipt AS inHosDays,
               a.ipdr_code AS drCodg,
               a.ipdr_name AS drName,
               a.asst_list_age_grp as asstListAgeGrp,
               a.asst_list_dise_sev_deg as asstListDiseSevDeg,
               a.asst_list_tmor_sev_deg as asstListTmorSevDeg,
               a.dscg_caty_codg_inhosp AS deptCode,
               d.`NAME` AS deptName,
               CONCAT(b.C03C,'|',b.C04N) AS mainDiag,
               CONCAT(b.C14x01C,'|',b.C15x01N) AS majorSurgery,
               b.k00,
               c.grp_fale_rea AS grpFaleRea,
               a.PATIENT_ID AS bah,
               a.adm_time AS inHosTime,
               a.dscg_time AS outHosTime,
               IFNULL(convert(AES_DECRYPT(UNHEX(f.dip_standard_ipt_days_same_lv),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) using utf8),0) as standardDays,
               IFNULL(convert(AES_DECRYPT(UNHEX(f.dip_standard_avg_fee_same_lv),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) using utf8),0) as standardInHosTotalCost,
               sc.drug_ratio * 100 as drugRatio,
               sc.mcs_fee_rat * 100 as consumRatio,
            <choose>
               <when test="feeStas == 0">
               a.ipt_sumfee AS inHosTotalCost,
               a.dip_codg AS dipCodg,
               a.DIP_NAME AS dipName,
               IFNULL(ROUND(IFNULL(a.drugfee,0)/NULLIF(a.ipt_sumfee,0)*100,2),0) AS medicalCostRate,
               IFNULL(ROUND(IFNULL(a.mcs_fee,0)/NULLIF(a.ipt_sumfee,0)*100,2),0) AS materialCostRate
               </when>
               <when test="feeStas == 1">
               e.ipt_sumfee AS inHosTotalCost,
               e.dip_codg AS dipCodg,
               e.DIP_NAME AS dipName,
               IFNULL(ROUND(IFNULL(a.drugfee,0)/NULLIF(e.ipt_sumfee,0)*100,2),0) AS medicalCostRate,
               IFNULL(ROUND(IFNULL(a.mcs_fee,0)/NULLIF(e.ipt_sumfee,0)*100,2),0) AS materialCostRate
               </when>
            </choose>
        FROM som_dip_grp_info a
        INNER JOIN som_hi_invy_bas_info bs
        ON a.SETTLE_LIST_ID = bs.ID
        <if test="categories != null and categories != ''">
            <choose>
                <when test="categories == 1">
                    and bs.A54 in ('1', '01', '310')
                </when>
                <when test="categories == 2">
                    and bs.A54 in ('2', '02', '390')
                </when>
                <when test="categories == 9">
                    and bs.A54 not in ('1', '01', '310', '2', '02', '390')
                </when>
            </choose>
        </if>

        <if test="enableSeAns != null and enableSeAns != '' and enableSeAns == 1">
            INNER JOIN som_setl_cas_crsp p
            ON bs.K00 = p.K00
        </if>
        LEFT JOIN som_std_fee sc
            ON a.dip_codg = sc.`CODE`
            AND a.asst_list_age_grp = sc.asst_list_age_grp
            AND a.asst_list_dise_sev_deg = sc.asst_list_dise_sev_deg
            AND a.asst_list_tmor_sev_deg = sc.asst_list_tmor_sev_deg
           AND SUBSTR(a.dscg_time,1,4) = sc.STANDARD_YEAR
           AND sc.TYPE = '1'
        LEFT JOIN som_hi_invy_bas_info b
            ON a.SETTLE_LIST_ID = b.ID
        LEFT JOIN som_dip_grp_rcd c
            ON a.SETTLE_LIST_ID = c.SETTLE_LIST_ID
        LEFT JOIN som_dept d
            ON a.dscg_caty_codg_inhosp = d.`CODE`
           AND a.HOSPITAL_ID = d.HOSPITAL_ID
        LEFT JOIN som_dip_standard f
            ON a.dip_codg = f.dip_codg
           AND SUBSTR(a.dscg_time,1,4) = f.STANDARD_YEAR
            AND a.is_used_asst_list = f.is_used_asst_list
           AND a.asst_list_age_grp = f.asst_list_age_grp
            AND a.asst_list_tmor_sev_deg = f.asst_list_tmor_sev_deg
           AND a.asst_list_dise_sev_deg = f.asst_list_dise_sev_deg
           AND a.HOSPITAL_ID = f.HOSPITAL_ID
        <choose>
            <when test="feeStas == 1">
            INNER JOIN
                 (
                     SELECT a.medcas_codg,
                            a.adm_time,
                            a.dip_codg,
                            a.DIP_NAME,
                            a.dscg_time,
                            a.is_in_group,
                            b.sumfee AS ipt_sumfee,
                            b.dfr_fee AS ycCost
                     FROM som_dip_grp_fbck a
                     LEFT JOIN som_fund_dfr_fbck b
                     ON a.rid_idt_codg = b.rid_idt_codg
                     AND a.HOSPITAL_ID = b.HOSPITAL_ID
                     WHERE 1=1
                     <if test="begnDate != null and begnDate != '' and expiDate != null and expiDate != ''">
                         AND a.dscg_time BETWEEN #{begnDate,jdbcType=VARCHAR} AND concat(#{expiDate,jdbcType=VARCHAR},' 23:59:59')
                     </if>
                     <if test="inStartTime != null and inStartTime != '' and inEndTime != null and inEndTime != ''">
                         AND a.adm_time BETWEEN #{inStartTime} AND CONCAT(#{inEndTime},' 23:59:59')
                     </if>
                     <if test="seStartTime!=null and seStartTime!='' and seEndTime!=null and seEndTime!=''">
                         AND a.setl_time BETWEEN #{seStartTime} and CONCAT(#{seEndTime},' 23:59:59')
                     </if>
                     <include refid="com.my.som.common.mapper.CommonMapper.fbAuth" />
                 ) e
                 ON a.PATIENT_ID = e.medcas_codg
                AND SUBSTR(a.adm_time,1,10) = e.adm_time
                AND SUBSTR(a.dscg_time,1,10) = e.dscg_time
            </when>
        </choose>
        WHERE 1=1
        <if test="begnDate != null and begnDate != '' and expiDate != null and expiDate != ''">
            AND a.dscg_time BETWEEN #{begnDate,jdbcType=VARCHAR} AND concat(#{expiDate,jdbcType=VARCHAR},' 23:59:59')
        </if>
        <if test="inStartTime != null and inStartTime != '' and inEndTime != null and inEndTime != ''">
            AND a.adm_time BETWEEN #{inStartTime} AND CONCAT(#{inEndTime},' 23:59:59')
        </if>
        <if test="seStartTime!=null and seStartTime!='' and seEndTime!=null and seEndTime!=''">
            AND a.setl_end_time BETWEEN #{seStartTime} and CONCAT(#{seEndTime},' 23:59:59')
        </if>
        <if test="icdCodg!=null and icdCodg!=''">
            AND a.main_diag_dise_codg = #{icdCodg,jdbcType=VARCHAR}
        </if>
        <if test="bah != null and bah != ''">
            AND a.PATIENT_ID = #{bah,jdbcType=VARCHAR}
        </if>
        <if test='setlway != null and setlway != "" and setlway == "1"'>
            AND bs.setlway = #{setlway,jdbcType=VARCHAR}
        </if>
        <if test="grpFlag != null and grpFlag != ''">
            <choose>
                <when test="grpFlag == 1">
                    AND c.grp_fale_rea IS NOT NULL
                </when>
            </choose>
        </if>
        <if test="errorReason != null and errorReason.size() > 0">
           AND (
            <foreach collection="errorReason" item="item" index="index" separator=" OR ">
               c.grp_fale_rea LIKE #{item}
            </foreach>
            )
        </if>
        <if test="patientIdList !=null and patientIdList.size()>0" >
             AND
            a.PATIENT_ID in
            <foreach collection="patientIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <include refid="queryCriteria" />
        ORDER BY a.dscg_time
    </select>

    <select id="queryPatientCostInfoData" resultType="com.my.som.vo.newBusiness.patient.NewBusinessPatientVo">
        SELECT a.*
        FROM
             (
                 SELECT a.SETTLE_LIST_ID AS settleListId,
                        a.`NAME` AS name,
                        a.dscg_caty_codg_inhosp AS deptCode,
                        c.`NAME` AS deptName,
                        a.PATIENT_ID AS bah,
                        a.adm_time AS inHosTime,
                        a.dscg_time AS outHosTime,
                        a.ipt_sumfee AS inHosTotalCost,
                        <choose>
                            <when test="feeStas == 0">
                                IFNULL(ROUND(e.ycCost,2),0) AS forecastAmount,
                                IFNULL(ROUND(IFNULL(e.profitloss,0),2),0) AS forecastAmountDiff,
                                a.dip_codg AS dipCodg,
                                a.DIP_NAME AS dipName,
                                e.dise_type AS stsbFee,
                            </when>
                            <when test="feeStas == 1">
                                e.ipt_sumfee AS fbInHosTotalCost,
                                IFNULL(ROUND(e.ycCost,2),0) AS forecastAmount,
                                IFNULL(ROUND(IFNULL(e.ycCost,0) - IFNULL(e.ipt_sumfee,0),2),0) AS forecastAmountDiff,
                                e.dip_codg AS dipCodg,
                                e.DIP_NAME AS dipName,
                                e.dise_type AS stsbFee,
                            </when>
                        </choose>
                        IFNULL(IFNULL(b.D11,0)+IFNULL(b.D12,0)+IFNULL(b.D13,0)+IFNULL(b.D14,0),0) AS com_med_servfee, <!-- 综合医疗服务费 -->
                        IFNULL(b.D11,0) AS ordn_med_servfee, <!-- 一般医疗服务费 -->
                        IFNULL(b.D12,0) AS ordn_trt_oprt_fee, <!-- 一般治疗操作费 -->
                        IFNULL(b.D13,0) AS nursfee, <!-- 护理费 -->
                        IFNULL(b.D14,0) AS zhylfwqtf, <!-- 综合医疗服务其他费 -->
                        IFNULL(IFNULL(b.D15,0)+IFNULL(b.D16,0)+IFNULL(b.D17,0)+IFNULL(b.D18,0),0) AS diag_fee, <!-- 诊断费 -->
                        IFNULL(b.D15,0) AS cas_diag_fee, <!-- 病理诊断费 -->
                        IFNULL(b.D16,0) AS lab_diag_fee, <!-- 实验室诊断费 -->
                        IFNULL(b.D17,0) AS rdhy_diag_fee, <!-- 影像学诊断费 -->
                        IFNULL(b.D18,0) AS clnc_diag_item_fee, <!-- 临床诊断项目费 -->
                        IFNULL(IFNULL(b.D19,0)+IFNULL(b.D20,0),0) AS treat_fee, <!-- 治疗费 -->
                        IFNULL(b.D19,0) AS nsrgtrt_item_fee, <!-- 非手术治疗项目费 -->
                        IFNULL(b.D20,0) AS oprn_treat_fee, <!-- 手术治疗费 -->
                        IFNULL(b.D21,0) AS rhab_fee, <!-- 康复费 -->
                        IFNULL(b.D22,0) AS tcmdrug_fee, <!-- 中医费 -->
                        IFNULL(b.D23,0) AS west_fee, <!-- 西药费 -->
                        IFNULL(IFNULL(b.D24,0)+IFNULL(b.D25,0),0) AS zyf1, <!-- 中药费 -->
                        IFNULL(b.D24,0) AS tcmpat_fee, <!-- 中成药费 -->
                        IFNULL(b.D25,0) AS tcmherb, <!-- 中草药费-->
                        IFNULL(IFNULL(b.D26,0)+IFNULL(b.D27,0)+IFNULL(b.D28,0)+IFNULL(b.D29,0)+IFNULL(b.D30,0),0) AS blood_blo_pro_fee, <!-- 血液和血液制品费 -->
                        IFNULL(b.D26,0) AS blo_fee, <!-- 血费 -->
                        IFNULL(b.D27,0) AS bdblzpf, <!-- 白蛋白类制品费 -->
                        IFNULL(b.D28,0) AS qdblzpf, <!-- 球蛋白类制品费 -->
                        IFNULL(b.D29,0) AS nxyzlzpf, <!-- 凝血因子类制品费 -->
                        IFNULL(b.D30,0) AS xbyzlzpf, <!-- 细胞因子类制品费 -->
                        IFNULL(IFNULL(b.D31,0)+IFNULL(b.D32,0)+IFNULL(b.D33,0),0) AS mcs_fee, <!-- 耗材费 -->
                        IFNULL(b.D31,0) AS jcyycxyyclf, <!-- 检查用一次性医用材料费 -->
                        IFNULL(b.D32,0) AS trt_use_dspo_med_matlfee, <!-- 治疗用一次性医用材料费 -->
                        IFNULL(b.D33,0) AS oprn_use_dspo_med_matlfee, <!-- 手术用一次性医用材料费 -->
                        IFNULL(b.D34,0) AS oth_fee, <!-- 其他费 -->
                        IFNULL(d.avg_fee,0) AS ljfy,<!-- 例均费用 -->
                        IFNULL(d.com_med_servfee,0) AS bgzhylfwf, <!-- 标杆综合医疗服务费 -->
                        IFNULL(d.ordn_med_servfee,0) AS bgybylfwf, <!-- 标杆一般医疗服务费 -->
                        IFNULL(d.ordn_trt_oprt_fee,0) AS bgybzlczf, <!-- 标杆一般治疗操作费 -->
                        IFNULL(d.nursfee,0) AS bghlf, <!-- 标杆护理费 -->
                        IFNULL(d.oth_fee_com,0) AS bgzhylfwqtf, <!-- 标杆综合医疗服务其他费 -->
                        IFNULL(d.diag_fee,0) AS bgzdf, <!-- 标杆诊断费 -->
                        IFNULL(d.cas_diag_fee,0) AS bgblzdf, <!-- 标杆病理诊断费 -->
                        IFNULL(d.lab_diag_fee,0) AS bgsyszdf, <!-- 标杆实验室诊断费 -->
                        IFNULL(d.rdhy_diag_fee,0) AS bgyxxzdf, <!-- 标杆影像学诊断费 -->
                        IFNULL(d.clnc_diag_item_fee,0) AS bglczdxmf, <!-- 标杆临床诊断项目费 -->
                        IFNULL(d.treat_fee,0) AS bgzlf, <!-- 标杆治疗费 -->
                        IFNULL(d.nsrgtrt_item_fee,0) AS bgfsszlxmf, <!-- 标杆非手术治疗项目费 -->
                        IFNULL(d.oprn_treat_fee,0) AS bgsszlf, <!-- 标杆手术治疗费 -->
                        IFNULL(d.rhab_fee,0) AS bgkff, <!-- 标杆康复费 -->
                        IFNULL(d.tcm_treat_fee,0) AS bgzyf, <!-- 标杆中医费 -->
                        IFNULL(d.west_fee,0) AS bgxyf, <!-- 标杆西药费 -->
                        IFNULL(d.tcmdrug_fee,0) AS bgzyf1, <!-- 标杆中药费 -->
                        IFNULL(d.tcmpat_fee,0) AS bgzcyf, <!-- 标杆中成药费 -->
                        IFNULL(d.tcmherb,0) AS bgzcyf1, <!-- 标杆中草药费 -->
                        IFNULL(d.blood_blo_pro_fee,0) AS bgxyhxyzpf, <!-- 标杆血液和血液制品费 -->
                        IFNULL(d.blo_fee,0) AS bgxf, <!-- 标杆血费 -->
                        IFNULL(d.albu_clss_prod_fee,0) AS bgbdblzpf, <!-- 标杆白蛋白类制品费 -->
                        IFNULL(d.glon_clss_prod_fee,0) AS bgqdblzpf, <!-- 标杆球蛋白类制品费 -->
                        IFNULL(d.clotfac_clss_prod_fee,0) AS bgnxyzlzpf, <!-- 标杆凝血因子类制品费 -->
                        IFNULL(d.cyki_clss_prod_fee,0) AS bgxbyzlzpf, <!-- 标杆细胞因子类制品费 -->
                        IFNULL(d.mcs_fee,0) AS bghcf, <!-- 标杆耗材费 -->
                        IFNULL(d.exam_use_dspo_med_matlfee,0) AS bgjcyycxyyclf, <!-- 标杆检查用一次性医用材料费 -->
                        IFNULL(d.trt_use_dspo_med_matlfee,0) AS bgzlyycxyyclf, <!-- 标杆治疗用一次性医用材料费 -->
                        IFNULL(d.oprn_use_dspo_med_matlfee,0) AS bgssyycxyyclf, <!-- 标杆手术用一次性医用材料费 -->
                        IFNULL(d.oth_fee,0) AS bgqtf <!-- 标杆其他费 -->
                 FROM som_dip_grp_info a
                 LEFT JOIN som_hi_invy_bas_info b
                     ON a.SETTLE_LIST_ID = b.ID
                <if test="enableSeAns != null and enableSeAns != '' and enableSeAns == 1">
                    INNER JOIN som_setl_cas_crsp p
                    ON b.K00 = p.K00
                </if>
                LEFT JOIN som_dept c
                     ON a.dscg_caty_codg_inhosp = c.`CODE`
                    AND a.HOSPITAL_ID = c.HOSPITAL_ID
                 LEFT JOIN som_std_fee d
                     ON a.dip_codg = d.`CODE`
                    AND SUBSTR(a.dscg_time,1,4) = d.STANDARD_YEAR
--                     AND a.asst_list_age_grp = d.asst_list_age_grp
--                     AND a.asst_list_dise_sev_deg = d.asst_list_dise_sev_deg
--                     AND a.asst_list_tmor_sev_deg = d.asst_list_tmor_sev_deg
                    AND d.TYPE = 1
                 <choose>
                     <when test="feeStas == 0">
                         LEFT JOIN
                         (
                         SELECT b.SETTLE_LIST_ID,
                         b.dise_type,
                         b.forecast_fee AS ycCost,
                         b.price,
                         b.profitloss AS profitloss,
                         b.sumfee AS sumfee
                         FROM som_dip_sco b
                         ) e
                         ON a.SETTLE_LIST_ID = e.SETTLE_LIST_ID
                     </when>
                     <when test="feeStas == 1">
                         INNER JOIN
                         (
                         SELECT
                         a.medcas_codg,
                         a.adm_time,
                         b.INSURED_TYPE AS dise_type,
                         a.dip_codg,
                         a.DIP_NAME,
                         a.dscg_time,
                         b.sumfee AS ipt_sumfee,
                         b.dfr_fee AS ycCost
                         FROM som_dip_grp_fbck a
                         LEFT JOIN som_fund_dfr_fbck b
                         ON a.rid_idt_codg = b.rid_idt_codg
                         AND a.HOSPITAL_ID = b.HOSPITAL_ID
                         <![CDATA[
                         WHERE a.dscg_time >= #{begnDate} AND a.dscg_time <= #{expiDate}
                         ]]>
                         <include refid="com.my.som.common.mapper.CommonMapper.fbAuth" />
                         ) e
                         ON a.PATIENT_ID = e.medcas_codg
                         AND SUBSTR(a.adm_time,1,10) = e.adm_time
                         AND SUBSTR(a.dscg_time,1,10) = e.dscg_time
                     </when>
                 </choose>
        WHERE a.dscg_time BETWEEN #{begnDate,jdbcType=VARCHAR} AND CONCAT(#{expiDate,jdbcType=VARCHAR},' 23:59:59')
          <if test="inStartTime != null and inStartTime != '' and inEndTime != null and inEndTime != ''">
              AND a.adm_time BETWEEN #{inStartTime} AND CONCAT(#{inEndTime}, ' 23:59:59')
          </if>
        <if test="patientIdList !=null and patientIdList.size()>0" >
            AND
            a.PATIENT_ID in
            <foreach collection="patientIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="costSection != null and costSection != ''">
            AND e.dise_type = #{costSection}
        </if>
        <include refid="queryCriteria" />
        ORDER BY a.dscg_time) a
        <where>
            <if test="isLoss != null and isLoss != ''">
                <if test="isLoss == 1">
                    <![CDATA[
                    AND a.forecastAmountDiff < 0
                ]]>
                </if>
                <if test="isLoss == 0">
                    <![CDATA[
                    AND a.forecastAmountDiff >= 0
                ]]>
                </if>
            </if>
            <if test="costSection != null and costSection != ''">
              AND  a.stsbFee = #{costSection}
            </if>
        </where>
    </select>

    <select id="queryPatientCostInfoData2" resultType="com.my.som.vo.newBusiness.patient.NewBusinessPatientVo">
        SELECT a.*
        FROM
        (
        SELECT a.SETTLE_LIST_ID AS settleListId,
        a.`NAME` AS name,
        a.dscg_caty_codg_inhosp AS deptCode,
        c.`NAME` AS deptName,
        a.PATIENT_ID AS bah,
        a.adm_time AS inHosTime,
        a.dscg_time AS outHosTime,
        a.ipt_sumfee AS inHosTotalCost,
        <choose>
            <when test="feeStas == 0">
                IFNULL(ROUND(e.ycCost,2),0) AS forecastAmount,
                IFNULL(ROUND(IFNULL(e.profitloss,0) -IFNULL(a.pre_hosp_examfee,0),2),0) AS forecastAmountDiff,
                a.dip_codg AS dipCodg,
                a.DIP_NAME AS dipName,
                case
                when e.dise_type = '1' then '3'
                when e.dise_type = '2' then '2'
                when e.dise_type = '3' then '1'
                else e.dise_type
                end AS stsbFee,
            </when>
            <when test="feeStas == 1">
                e.ipt_sumfee AS fbInHosTotalCost,
                IFNULL(ROUND(e.ycCost,2),0) AS forecastAmount,
                IFNULL(ROUND(IFNULL(e.ycCost,0) - IFNULL(e.ipt_sumfee,0),2),0) AS forecastAmountDiff,
                e.dip_codg AS dipCodg,
                e.DIP_NAME AS dipName,
                e.dise_type AS stsbFee,
            </when>
        </choose>
        IFNULL(IFNULL(b.D11,0)+IFNULL(b.D12,0)+IFNULL(b.D13,0)+IFNULL(b.D14,0),0) AS com_med_servfee, <!-- 综合医疗服务费 -->
        IFNULL(b.D11,0) AS ordn_med_servfee, <!-- 一般医疗服务费 -->
        IFNULL(b.D12,0) AS ordn_trt_oprt_fee, <!-- 一般治疗操作费 -->
        IFNULL(b.D13,0) AS nursfee, <!-- 护理费 -->
        IFNULL(b.D14,0) AS zhylfwqtf, <!-- 综合医疗服务其他费 -->
        IFNULL(IFNULL(b.D15,0)+IFNULL(b.D16,0)+IFNULL(b.D17,0)+IFNULL(b.D18,0),0) AS diag_fee, <!-- 诊断费 -->
        IFNULL(b.D15,0) AS cas_diag_fee, <!-- 病理诊断费 -->
        IFNULL(b.D16,0) AS lab_diag_fee, <!-- 实验室诊断费 -->
        IFNULL(b.D17,0) AS rdhy_diag_fee, <!-- 影像学诊断费 -->
        IFNULL(b.D18,0) AS clnc_diag_item_fee, <!-- 临床诊断项目费 -->
        IFNULL(IFNULL(b.D19,0)+IFNULL(b.D20,0),0) AS treat_fee, <!-- 治疗费 -->
        IFNULL(b.D19,0) AS nsrgtrt_item_fee, <!-- 非手术治疗项目费 -->
        IFNULL(b.D20,0) AS oprn_treat_fee, <!-- 手术治疗费 -->
        IFNULL(b.D21,0) AS rhab_fee, <!-- 康复费 -->
        IFNULL(b.D22,0) AS tcmdrug_fee, <!-- 中医费 -->
        IFNULL(b.D23,0) AS west_fee, <!-- 西药费 -->
        IFNULL(IFNULL(b.D24,0)+IFNULL(b.D25,0),0) AS zyf1, <!-- 中药费 -->
        IFNULL(b.D24,0) AS tcmpat_fee, <!-- 中成药费 -->
        IFNULL(b.D25,0) AS tcmherb, <!-- 中草药费-->
        IFNULL(IFNULL(b.D26,0)+IFNULL(b.D27,0)+IFNULL(b.D28,0)+IFNULL(b.D29,0)+IFNULL(b.D30,0),0) AS blood_blo_pro_fee, <!-- 血液和血液制品费 -->
        IFNULL(b.D26,0) AS blo_fee, <!-- 血费 -->
        IFNULL(b.D27,0) AS bdblzpf, <!-- 白蛋白类制品费 -->
        IFNULL(b.D28,0) AS qdblzpf, <!-- 球蛋白类制品费 -->
        IFNULL(b.D29,0) AS nxyzlzpf, <!-- 凝血因子类制品费 -->
        IFNULL(b.D30,0) AS xbyzlzpf, <!-- 细胞因子类制品费 -->
        IFNULL(IFNULL(b.D31,0)+IFNULL(b.D32,0)+IFNULL(b.D33,0),0) AS mcs_fee, <!-- 耗材费 -->
        IFNULL(b.D31,0) AS jcyycxyyclf, <!-- 检查用一次性医用材料费 -->
        IFNULL(b.D32,0) AS trt_use_dspo_med_matlfee, <!-- 治疗用一次性医用材料费 -->
        IFNULL(b.D33,0) AS oprn_use_dspo_med_matlfee, <!-- 手术用一次性医用材料费 -->
        IFNULL(b.D34,0) AS oth_fee, <!-- 其他费 -->
        IFNULL(d.avg_fee,0) AS ljfy,<!-- 例均费用 -->
        IFNULL(d.com_med_servfee,0) AS bgzhylfwf, <!-- 标杆综合医疗服务费 -->
        IFNULL(d.ordn_med_servfee,0) AS bgybylfwf, <!-- 标杆一般医疗服务费 -->
        IFNULL(d.ordn_trt_oprt_fee,0) AS bgybzlczf, <!-- 标杆一般治疗操作费 -->
        IFNULL(d.nursfee,0) AS bghlf, <!-- 标杆护理费 -->
        IFNULL(d.oth_fee_com,0) AS bgzhylfwqtf, <!-- 标杆综合医疗服务其他费 -->
        IFNULL(d.diag_fee,0) AS bgzdf, <!-- 标杆诊断费 -->
        IFNULL(d.cas_diag_fee,0) AS bgblzdf, <!-- 标杆病理诊断费 -->
        IFNULL(d.lab_diag_fee,0) AS bgsyszdf, <!-- 标杆实验室诊断费 -->
        IFNULL(d.rdhy_diag_fee,0) AS bgyxxzdf, <!-- 标杆影像学诊断费 -->
        IFNULL(d.clnc_diag_item_fee,0) AS bglczdxmf, <!-- 标杆临床诊断项目费 -->
        IFNULL(d.treat_fee,0) AS bgzlf, <!-- 标杆治疗费 -->
        IFNULL(d.nsrgtrt_item_fee,0) AS bgfsszlxmf, <!-- 标杆非手术治疗项目费 -->
        IFNULL(d.oprn_treat_fee,0) AS bgsszlf, <!-- 标杆手术治疗费 -->
        IFNULL(d.rhab_fee,0) AS bgkff, <!-- 标杆康复费 -->
        IFNULL(d.tcm_treat_fee,0) AS bgzyf, <!-- 标杆中医费 -->
        IFNULL(d.west_fee,0) AS bgxyf, <!-- 标杆西药费 -->
        IFNULL(d.tcmdrug_fee,0) AS bgzyf1, <!-- 标杆中药费 -->
        IFNULL(d.tcmpat_fee,0) AS bgzcyf, <!-- 标杆中成药费 -->
        IFNULL(d.tcmherb,0) AS bgzcyf1, <!-- 标杆中草药费 -->
        IFNULL(d.blood_blo_pro_fee,0) AS bgxyhxyzpf, <!-- 标杆血液和血液制品费 -->
        IFNULL(d.blo_fee,0) AS bgxf, <!-- 标杆血费 -->
        IFNULL(d.albu_clss_prod_fee,0) AS bgbdblzpf, <!-- 标杆白蛋白类制品费 -->
        IFNULL(d.glon_clss_prod_fee,0) AS bgqdblzpf, <!-- 标杆球蛋白类制品费 -->
        IFNULL(d.clotfac_clss_prod_fee,0) AS bgnxyzlzpf, <!-- 标杆凝血因子类制品费 -->
        IFNULL(d.cyki_clss_prod_fee,0) AS bgxbyzlzpf, <!-- 标杆细胞因子类制品费 -->
        IFNULL(d.mcs_fee,0) AS bghcf, <!-- 标杆耗材费 -->
        IFNULL(d.exam_use_dspo_med_matlfee,0) AS bgjcyycxyyclf, <!-- 标杆检查用一次性医用材料费 -->
        IFNULL(d.trt_use_dspo_med_matlfee,0) AS bgzlyycxyyclf, <!-- 标杆治疗用一次性医用材料费 -->
        IFNULL(d.oprn_use_dspo_med_matlfee,0) AS bgssyycxyyclf, <!-- 标杆手术用一次性医用材料费 -->
        IFNULL(d.oth_fee,0) AS bgqtf, <!-- 标杆其他费 -->
        <!-- 十四项_标杆 -->
        IFNULL(d.medi_fee_type_bedfee,0) AS bgfourteencwf,<!-- 十四项_床位费 -->
        IFNULL(d.medi_fee_type_diag_fee,0) AS bgfourteenzcf,<!-- 十四项_诊查费 -->
        IFNULL(d.medi_fee_type_examfee,0) AS bgfourteenjcf,<!-- 十四项_检查费 -->
        IFNULL(d.medi_fee_type_asy_fee,0) AS bgfourteenhyf,<!-- 十四项_化验费 -->
        IFNULL(d.medi_fee_type_treat_fee,0) AS bgfourteenzlf,<!-- 十四项_治疗费 -->
        IFNULL(d.medi_fee_type_oper_fee,0) AS bgfourteenssf,<!-- 十四项_手术费 -->
        IFNULL(d.medi_fee_type_nursfee,0) AS bgfourteenhlf,<!-- 十四项_护理费 -->
        IFNULL(d.medi_fee_type_hc_matlfee,0) AS bgfourteenwsclf,<!-- 十四项_卫生材料费 -->
        IFNULL(d.medi_fee_type_west_fee,0) AS bgfourteenxyf,<!-- 十四项_西药费 -->
        IFNULL(d.medi_fee_type_tmdp_fee,0) AS bgfourteenzyypf,<!-- 十四项_中药饮片费 -->
        IFNULL(d.medi_fee_type_tcmpat_fee,0) AS bgfourteenzcyf,<!-- 十四项_中成药费 -->
        IFNULL(d.medi_fee_type_ordn_trtfee,0) AS bgfourteenybzlf,<!-- 十四项_一般诊疗费 -->
        IFNULL(d.medi_fee_type_regfee,0) AS bgfourteenghf,<!-- 十四项_挂号费 -->
        IFNULL(d.medi_fee_type_oth_fee,0) AS bgfourteenqtf,<!-- 十四项_其他费 -->
        <!-- 十四项_患者 -->
        f.fourteencwf,<!-- 十四项_床位费 -->
        f.fourteenzcf,<!-- 十四项_诊查费 -->
        f.fourteenjcf,<!-- 十四项_检查费 -->
        f.fourteenhyf,<!-- 十四项_化验费 -->
        f.fourteenzlf,<!-- 十四项_治疗费 -->
        f.fourteenssf,<!-- 十四项_手术费 -->
        f.fourteenhlf,<!-- 十四项_护理费 -->
        f.fourteenwsclf,<!-- 十四项_卫生材料费 -->
        f.fourteenxyf,<!-- 十四项_西药费 -->
        f.fourteenzyypf,<!-- 十四项_中药饮片费 -->
        f.fourteenzcyf,<!-- 十四项_中成药费 -->
        f.fourteenybzlf,<!-- 十四项_一般诊疗费 -->
        f.fourteenghf,<!-- 十四项_挂号费 -->
        f.fourteenqtf<!-- 十四项_其他费 -->
        FROM som_dip_grp_info a
        LEFT JOIN som_hi_invy_bas_info b
        ON a.SETTLE_LIST_ID = b.ID
        <if test="enableSeAns != null and enableSeAns != '' and enableSeAns == 1">
            INNER JOIN som_setl_cas_crsp p
            ON b.K00 = p.K00
        </if>
        LEFT JOIN som_dept c
        ON a.dscg_caty_codg_inhosp = c.`CODE`
        AND a.HOSPITAL_ID = c.HOSPITAL_ID
        LEFT JOIN som_std_fee d
        ON a.dip_codg = d.`CODE`
        AND SUBSTR(a.dscg_time,1,4) = d.STANDARD_YEAR
--         AND a.asst_list_age_grp = d.asst_list_age_grp
--         AND a.asst_list_dise_sev_deg = d.asst_list_dise_sev_deg
--         AND a.asst_list_tmor_sev_deg = d.asst_list_tmor_sev_deg
        AND d.TYPE = 1
        <choose>
            <when test="feeStas == 0">
                LEFT JOIN
                (
                SELECT b.SETTLE_LIST_ID,
                b.dise_type,
                b.forecast_fee AS ycCost,
                b.price,
                b.profitloss AS profitloss,
                b.sumfee AS sumfee
                FROM som_dip_sco b
                ) e
                ON a.SETTLE_LIST_ID = e.SETTLE_LIST_ID
            </when>
            <when test="feeStas == 1">
                INNER JOIN
                (
                SELECT
                a.medcas_codg,
                a.adm_time,
                b.INSURED_TYPE AS dise_type,
                a.dip_codg,
                a.DIP_NAME,
                a.dscg_time,
                b.sumfee AS ipt_sumfee,
                b.dfr_fee AS ycCost
                FROM som_dip_grp_fbck a
                LEFT JOIN som_fund_dfr_fbck b
                ON a.rid_idt_codg = b.rid_idt_codg
                AND a.HOSPITAL_ID = b.HOSPITAL_ID
                WHERE 1=1
                <if test="begnDate != null and begnDate != '' and expiDate != null and expiDate != ''">
                    AND a.dscg_time BETWEEN #{begnDate,jdbcType=VARCHAR} AND concat(#{expiDate,jdbcType=VARCHAR},' 23:59:59')
                </if>
                <if test="inStartTime != null and inStartTime != '' and inEndTime != null and inEndTime != ''">
                    AND a.adm_time BETWEEN #{inStartTime} AND CONCAT(#{inEndTime},' 23:59:59')
                </if>
                <if test="seStartTime!=null and seStartTime!='' and seEndTime!=null and seEndTime!=''">
                    AND a.setl_time BETWEEN #{seStartTime} and CONCAT(#{seEndTime},' 23:59:59')
                </if>
                <include refid="com.my.som.common.mapper.CommonMapper.fbAuth" />
                ) e
                ON a.PATIENT_ID = e.medcas_codg
                AND SUBSTR(a.adm_time,1,10) = e.adm_time
                AND SUBSTR(a.dscg_time,1,10) = e.dscg_time
            </when>
        </choose>
        LEFT JOIN (
            SELECT
                hi_setl_invy_id,
                MAX(CASE WHEN med_chrg_itemname = '床位费' THEN amt ELSE 0 END) AS fourteencwf,
                MAX(CASE WHEN med_chrg_itemname = '诊查费' THEN amt ELSE 0 END) AS fourteenzcf,
                MAX(CASE WHEN med_chrg_itemname = '检查费' THEN amt ELSE 0 END) AS fourteenjcf,
                MAX(CASE WHEN med_chrg_itemname = '化验费' THEN amt ELSE 0 END) AS fourteenhyf,
                MAX(CASE WHEN med_chrg_itemname = '治疗费' THEN amt ELSE 0 END) AS fourteenzlf,
                MAX(CASE WHEN med_chrg_itemname = '手术费' THEN amt ELSE 0 END) AS fourteenssf,
                MAX(CASE WHEN med_chrg_itemname = '护理费' THEN amt ELSE 0 END) AS fourteenhlf,
                MAX(CASE WHEN med_chrg_itemname = '卫生材料费' THEN amt ELSE 0 END) AS fourteenwsclf,
                MAX(CASE WHEN med_chrg_itemname = '西药费' THEN amt ELSE 0 END) AS fourteenxyf,
                MAX(CASE WHEN med_chrg_itemname = '中药饮片费' THEN amt ELSE 0 END) AS fourteenzyypf,
                MAX(CASE WHEN med_chrg_itemname = '中成药费' THEN amt ELSE 0 END) AS fourteenzcyf,
                MAX(CASE WHEN med_chrg_itemname = '一般诊疗费' THEN amt ELSE 0 END) AS fourteenybzlf,
                MAX(CASE WHEN med_chrg_itemname = '挂号费' THEN amt ELSE 0 END) AS fourteenghf,
                MAX(CASE WHEN med_chrg_itemname = '其他费' THEN amt ELSE 0 END) AS fourteenqtf
            FROM som_hi_setl_invy_med_fee_info
            WHERE hi_setl_invy_id IN(
                SELECT SETTLE_LIST_ID AS hi_setl_invy_id
                FROM som_dip_grp_info
                WHERE dscg_time BETWEEN #{begnDate,jdbcType=VARCHAR} AND CONCAT(#{expiDate,jdbcType=VARCHAR},' 23:59:59')
            )
            GROUP BY hi_setl_invy_id
        ) f
        ON a.SETTLE_LIST_ID = f.hi_setl_invy_id
        WHERE 1=1
        <if test="categories != null and categories != ''">
            <choose>
                <when test="categories == 1">
                    and b.A54 in ('1', '01', '310')
                </when>
                <when test="categories == 2">
                    and b.A54 in ('2', '02', '390')
                </when>
                <when test="categories == 9">
                    and b.A54 not in ('1', '01', '310', '2', '02', '390')
                </when>
            </choose>
        </if>

        <if test="begnDate != null and begnDate != '' and expiDate != null and expiDate != ''">
            AND a.dscg_time BETWEEN #{begnDate,jdbcType=VARCHAR} AND concat(#{expiDate,jdbcType=VARCHAR},' 23:59:59')
        </if>
        <if test="inStartTime != null and inStartTime != '' and inEndTime != null and inEndTime != ''">
            AND a.adm_time BETWEEN #{inStartTime} AND CONCAT(#{inEndTime},' 23:59:59')
        </if>
        <if test="seStartTime!=null and seStartTime!='' and seEndTime!=null and seEndTime!=''">
            AND a.setl_end_time BETWEEN #{seStartTime} and CONCAT(#{seEndTime},' 23:59:59')
        </if>
        <if test="patientIdList !=null and patientIdList.size()>0" >
            AND
            a.PATIENT_ID in
            <foreach collection="patientIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="costSection != null and costSection != ''">
            AND e.dise_type = #{costSection}
        </if>
        <if test='setlway != null and setlway != "" and setlway == "1"'>
            AND b.setlway = #{setlway,jdbcType=VARCHAR}
        </if>
        <include refid="queryCriteria" />
        ORDER BY a.dscg_time) a
        <where>
            <if test="isLoss != null and isLoss != ''">
                <if test="isLoss == 1">
                    <![CDATA[
                    AND a.forecastAmountDiff < 0
                ]]>
                </if>
                <if test="isLoss == 0">
                    <![CDATA[
                    AND a.forecastAmountDiff >= 0
                ]]>
                </if>
            </if>
            <if test="costSection != null and costSection != ''">
                AND  a.stsbFee = #{costSection}
            </if>
        </where>
    </select>

    <select id="queryPatientForecastData" resultType="com.my.som.vo.newBusiness.patient.NewBusinessPatientVo">
        SELECT a.settleListId,
               a.name,
               a.bah,
               a.deptCode,
               a.deptName,
               a.dipCodg,
               a.dipName,
               a.insuType,
               ROUND(a.price,2) AS price,
               a.ratioRange,
               ROUND(a.totlSco,2) AS totlSco,
               a.isUsedAsstList,
               a.asstListAgeGrp,
               a.asstListDiseSevDeg,
               a.asstListTmorSevDeg,
               IFNULL(ROUND(a.ycCost,2),0) AS forecastAmount,
        <choose>
            <when test="feeStas == 0">
                a.sumfee as inHosTotalCost,
               IFNULL(ROUND(a.profitloss ,2),0) AS forecastAmountDiff,
               IFNULL(ROUND(a.sumfee/a.ycCost,2),0) AS oeVal
            </when>
            <when test="feeStas == 1">
                a.inHosTotalCost,
                IFNULL(ROUND(a.ycCost - IFNULL(a.inHosTotalCost,0)-IFNULL(a.preHospExamfee,0),2),0) AS forecastAmountDiff,
                IFNULL(ROUND(a.inHosTotalCost/a.ycCost,2),0) AS oeVal
            </when>
        </choose>
        FROM
             (
                 SELECT a.SETTLE_LIST_ID AS settleListId,
                        a.`NAME` AS name,
                        a.dscg_caty_codg_inhosp AS deptCode,
                        d.`NAME` AS deptName,
                        a.PATIENT_ID AS bah,
                     <choose>
                         <when test="feeStas == 0">
                             a.ipt_sumfee AS inHosTotalCost,
                             a.dip_codg AS dipCodg,
                             a.DIP_NAME AS dipName,
                             a.pre_hosp_examfee AS preHospExamfee,
                             CASE
                             WHEN b.A54 IN ('1', '01', 310) THEN 1
                             WHEN b.A54 IN ('2', '02', 390) THEN 2
                             ELSE 9
                             END AS insuType,
                             c.sumfee,
                             c.price ,
                              c.forecast_fee as ycCost,
                             c.profitloss AS profitloss,
                             case
                             when c.dise_type = '1' then '3'
                             when c.dise_type = '2' then '2'
                             when c.dise_type = '3' then '1'
                             else c.dise_type
                             end AS ratioRange,
                             IFNULL(c.totl_sco,0) AS totlSco,
                             a.is_used_asst_list AS isUsedAsstList,
                             a.asst_list_age_grp AS asstListAgeGrp,
                             a.asst_list_dise_sev_deg AS asstListDiseSevDeg,
                             a.asst_list_tmor_sev_deg AS asstListTmorSevDeg
                         </when>
                         <when test="feeStas == 1">
                             b.sumfee AS inHosTotalCost,
                             b.dip_codg AS dipCodg,
                             b.DIP_NAME AS dipName,
                             b.INSURED_TYPE AS insuType,
                             b.setl_pt_val AS price,
                             b.dfr_fee AS ycCost,
                             b.MED_TYPE AS ratioRange,
                             b.setl_sco AS totlSco,
                             b.used_asst_list AS isUsedAsstList,
                             b.asst_list_age_grp AS asstListAgeGrp,
                             b.asst_list_dise_sev_deg AS asstListDiseSevDeg,
                             b.asst_list_tmor_sev_deg AS asstListTmorSevDeg
                         </when>
                     </choose>
                 FROM som_dip_grp_info a
                 INNER JOIN som_hi_invy_bas_info f
                 ON a.SETTLE_LIST_ID = f.ID
                <if test="enableSeAns != null and enableSeAns != '' and enableSeAns == 1">
                    INNER JOIN som_setl_cas_crsp p
                    ON f.K00 = p.K00
                </if>
                 LEFT JOIN som_dept d
                 ON a.dscg_caty_codg_inhosp = d.`CODE`
                 AND a.HOSPITAL_ID = d.HOSPITAL_ID

                         <choose>
                             <when test="feeStas == 0">
                                 LEFT JOIN som_hi_invy_bas_info b
                                 ON a.SETTLE_LIST_ID = b.ID
                                 LEFT JOIN som_dip_sco c
                                 ON a.SETTLE_LIST_ID = c.SETTLE_LIST_ID

                             </when>
                             <when test="feeStas == 1">
                                 INNER JOIN
                                 (
                                 SELECT
                                 a.dip_codg,
                                 a.DIP_NAME,
                                 a.rid_idt_codg,
                                 a.medcas_codg,
                                 a.adm_time,
                                 a.dscg_time,
                                 b.sumfee,
                                 b.INSURED_TYPE,
                                 b.setl_pt_val,
                                 b.dfr_fee,
                                 b.MED_TYPE,
                                 b.setl_sco,
                                 a.used_asst_list,
                                 a.asst_list_age_grp,
                                 a.asst_list_dise_sev_deg,
                                 a.asst_list_tmor_sev_deg
                                 FROM som_dip_grp_fbck a
                                 LEFT JOIN som_fund_dfr_fbck b
                                 ON a.rid_idt_codg = b.rid_idt_codg
                                 AND a.HOSPITAL_ID = b.HOSPITAL_ID
                                WHERE 1=1
                                 <if test="begnDate != null and begnDate != '' and expiDate != null and expiDate != ''">
                                     AND a.dscg_time BETWEEN #{begnDate,jdbcType=VARCHAR} AND concat(#{expiDate,jdbcType=VARCHAR},' 23:59:59')
                                 </if>
                                 <if test="inStartTime != null and inStartTime != '' and inEndTime != null and inEndTime != ''">
                                     AND a.adm_time BETWEEN #{inStartTime} AND CONCAT(#{inEndTime},' 23:59:59')
                                 </if>
                                 <if test="seStartTime!=null and seStartTime!='' and seEndTime!=null and seEndTime!=''">
                                     AND a.setl_time BETWEEN #{seStartTime} and CONCAT(#{seEndTime},' 23:59:59')
                                 </if>
                                 <include refid="com.my.som.common.mapper.CommonMapper.fbAuth" />
                                 ) b
                                 <!--ON SUBSTR(a.PATIENT_ID,1,10) = b.medcas_codg-->
                                 ON a.PATIENT_ID = b.medcas_codg
                                 AND SUBSTR(a.adm_time,1,10) = b.adm_time
                                 AND SUBSTR(a.dscg_time,1,10) = b.dscg_time
                             </when>
                         </choose>

                 WHERE 1=1
        <if test="categories != null and categories != ''">
            <choose>
                <when test="categories == 1">
                    and f.A54 in ('1', '01', '310')
                </when>
                <when test="categories == 2">
                    and f.A54 in ('2', '02', '390')
                </when>
                <when test="categories == 9">
                    and f.A54 not in ('1', '01', '310', '2', '02', '390')
                </when>
            </choose>
        </if>
                    <if test="begnDate != null and begnDate != '' and expiDate != null and expiDate != ''">
                        AND a.dscg_time BETWEEN #{begnDate,jdbcType=VARCHAR} AND concat(#{expiDate,jdbcType=VARCHAR},' 23:59:59')
                    </if>
                    <if test="inStartTime != null and inStartTime != '' and inEndTime != null and inEndTime != ''">
                        AND a.adm_time BETWEEN #{inStartTime} AND CONCAT(#{inEndTime},' 23:59:59')
                    </if>
                    <if test="seStartTime!=null and seStartTime!='' and seEndTime!=null and seEndTime!=''">
                        AND a.setl_end_time BETWEEN #{seStartTime} and CONCAT(#{seEndTime},' 23:59:59')
                    </if>
                    <if test='setlway != null and setlway != "" and setlway == "1"'>
                        AND f.setlway = #{setlway,jdbcType=VARCHAR}
                    </if>
                <include refid="queryCriteria" />
                <if test="patientIdList !=null and patientIdList.size()>0" >
                    AND
                    a.PATIENT_ID in
                    <foreach collection="patientIdList" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                 </if>
             ) a
    </select>

    <sql id="queryCriteria">
        <if test="bah != null and bah != ''">
            AND a.PATIENT_ID LIKE CONCAT('%',#{bah},'%')
        </if>
        <if test="dipCodg != null and dipCodg != ''">
            <if test="feeStas != null and feeStas != ''">
                <choose>
                    <when test="feeStas == 0">
                        AND a.dip_codg = #{dipCodg}
                    </when>
                    <when test="feeStas == 1">
                        AND e.dip_codg = #{dipCodg}
                    </when>
                </choose>
            </if>
        </if>
        <!-- 通用查询条件 -->
        <include refid="com.my.som.mapper.newBusiness.common.NewBusinessCommonAnalysisMapper.queryBusCommon" />
        <if test="isInGroup != null and isInGroup != ''">
            <choose>
                <when test="isInGroup == 0">
                <if test="feeStas == 0">
                    AND a.grp_stas != 1
                </if>
                <if test="feeStas == 1">
                    AND e.is_in_group != 1
                </if>
                </when>
                <when test="isInGroup == 1">
                    <if test="feeStas == 0">
                        AND a.grp_stas = 1
                    </if>
                    <if test="feeStas == 1">
                        AND e.is_in_group = 1
                    </if>
                </when>
            </choose>
        </if>
    </sql>
</mapper>
