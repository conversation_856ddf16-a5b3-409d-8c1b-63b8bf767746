<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.som.mapper.newBusiness.common.NewBusinessCommonAnalysisMapper">

    <!-- 查询病组亏损 -->
    <select id="queryDiseaseLoss" resultType="com.my.som.vo.newBusiness.NewBusinessAnalysisVo">
        select k.*,
        ROUND(highPreTotalCost - highTotalCost,2) AS highDiff,
        ROUND(lowPreTotalCost - lowTotalCost,2) AS lowDiff,
        ROUND(normalPreTotalCost - normalTotalCost,2) AS normalDiff,
        ROUND(normalEarnPreTotalCost - normalEarnTotalCost,2) AS normalEarnDiff
        from(
        select case when dipCodg = '' or dipCodg is null then '未入组病组' else dipCodg end as dipCodg,
        case when dipName = '' or dipName is null then '未入组病组' else dipName end as dipName,
        asstListAgeGrp,asstListTmorSevDeg,asstListDiseSevDeg,
        IFNULL(ROUND(sum(preCost),2),0) as preTotalCost,
        IFNULL(ROUND(avg(inHosTotalCost),2),0) AS avgCost,
        IFNULL(ROUND(avg(bgzyfyjb),2),0) AS avgCostBenchmark,
        IFNULL(ROUND(sum(inHosTotalCost),2),0) AS sumfee,
        <choose>
            <when test="feeStas == 0">
                IFNULL(ROUND(sum(profitloss) -sum(IFNULL(preHospExamfee,0)),2),0) AS diff,
            </when>
            <when test="feeStas == 1">
                IFNULL(ROUND(sum(fbInHosTotalCost),2),0) AS fbTotalCost,
                IFNULL(ROUND(sum(preCost) - sum(fbInHosTotalCost),2),0) AS diff,
            </when>
        </choose>
        <![CDATA[
                   IFNULL(ROUND(COUNT(CASE WHEN highLowType = '1' and (preCost - inHosTotalCost) < 0 then inHosTotalCost ELSE null end),2),0) AS highNum,
                   GROUP_CONCAT(CASE WHEN highLowType = '1' AND ( preCost - inHosTotalCost ) < 0 THEN patientId ELSE NULL END) as highPatientId,
                   IFNULL(ROUND(SUM(CASE WHEN highLowType = '1' and (preCost - inHosTotalCost) < 0 then inHosTotalCost ELSE null end),2),0) AS highTotalCost,
                   IFNULL(ROUND(SUM(CASE WHEN highLowType = '1' and (preCost - inHosTotalCost) < 0 then preCost ELSE null end),2),0) AS highPreTotalCost,
                   IFNULL(ROUND(COUNT(CASE WHEN highLowType = '2' and (preCost - inHosTotalCost) < 0 then inHosTotalCost ELSE null end),2),0) AS lowNum,
                   GROUP_CONCAT(CASE WHEN highLowType = '2' AND ( preCost - inHosTotalCost ) < 0 THEN patientId ELSE NULL END) as lowPatientId,
                   IFNULL(ROUND(SUM(CASE WHEN highLowType = '2' and (preCost - inHosTotalCost) < 0 then inHosTotalCost ELSE null end),2),0) AS lowTotalCost,
                   IFNULL(ROUND(SUM(CASE WHEN highLowType = '2' and (preCost - inHosTotalCost) < 0 then preCost ELSE null end),2),0) AS lowPreTotalCost,
                   IFNULL(ROUND(COUNT(CASE WHEN highLowType = '3' and (preCost - inHosTotalCost) < 0 then inHosTotalCost ELSE null end),2),0) AS normalNum,
                   GROUP_CONCAT(CASE WHEN highLowType = '3' and (preCost - inHosTotalCost) < 0 THEN patientId ELSE NULL END) as normalPatientId,
                   IFNULL(ROUND(SUM(CASE WHEN highLowType = '3' and (preCost - inHosTotalCost) < 0 then inHosTotalCost ELSE null end),2),0) AS normalTotalCost,
                   IFNULL(ROUND(SUM(CASE WHEN highLowType = '3' and (preCost - inHosTotalCost) < 0 then preCost ELSE null end),2),0) AS normalPreTotalCost,
                   IFNULL(ROUND(COUNT(CASE WHEN highLowType = '3' and (preCost - inHosTotalCost) > 0 then inHosTotalCost ELSE null end),2),0) AS normalEarnNum,
                   GROUP_CONCAT(CASE WHEN highLowType = '3' and (preCost - inHosTotalCost) > 0 then patientId ELSE NULL END) as normalEarnPatientId,
                   IFNULL(ROUND(SUM(CASE WHEN highLowType = '3' and (preCost - inHosTotalCost) > 0 then inHosTotalCost ELSE null end),2),0) AS normalEarnTotalCost,
                   IFNULL(ROUND(SUM(CASE WHEN highLowType = '3' and (preCost - inHosTotalCost) > 0 then preCost ELSE null end),2),0) AS normalEarnPreTotalCost
                   ]]>
        from (
        select g.*
        <if test="feeStas==0">
            ,
            g.ycCost as preCost
        </if>
        from (

        SELECT case when a.A12C=1 then '男' else '女' end as gend,<!-- 性别 -->
        a.a54 AS insuredType,
        a.A14 as age,<!-- 年龄 -->
        D01 AS inHosTotalCost, <!-- 住院总费用 -->
        D02 AS preHospExamfee,
        <choose>
            <when test="feeStas==0">
                IFNULL(ROUND((IFNULL(D01,0) - IFNULL(CONVERT(AES_DECRYPT(UNHEX(c.dip_standard_avg_fee_same_lv
                ),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) USING utf8),0)),2),0) AS
                zyfycy,<!-- 住院费用与标杆差异 -->
                ROUND(((IFNULL(b.drugfee/a.D01,0))*100 - IFNULL(d.drug_ratio,0)*100),2) AS yzbcy,
                ROUND(((IFNULL(b.mcs_fee/(a.D01),0))*100 - IFNULL(d.mcs_fee_rat,0)*100),2) AS hzbcy,
                b.dip_codg AS dipCodg, <!-- DIP编码 -->
                b.DIP_NAME AS dipName, <!-- DIP名称 -->
                b.asst_list_age_grp as asstListAgeGrp,
                b.asst_list_tmor_sev_deg AS asstListTmorSevDeg,
                b.asst_list_dise_sev_deg AS asstListDiseSevDeg,
                ROUND((IFNULL(b.drugfee/a.D01,0))*100,2) AS yzb, <!-- 药占比 -->
                ROUND((IFNULL(b.mcs_fee/(a.D01),0))*100,2) AS hzb, <!-- 耗占比 -->
                f.totl_sco AS refer_sco,
                f.dise_type AS highLowType,
                f.forecast_fee AS ycCost,
                f.price,
                f.profitloss AS profitloss,
            </when>
            <when test="feeStas==1">
                z.sumfee AS fbInHosTotalCost,<!-- 住院总费用 -->
                IFNULL(ROUND((IFNULL(z.sumfee,0) - IFNULL(CONVERT(AES_DECRYPT(UNHEX(z.dfr_fee
                ),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) USING utf8),0)),2),0) AS
                zyfycy,<!-- 住院费用与医保反馈差异 -->
                ROUND(((IFNULL(b.drugfee/z.sumfee,0))*100 - IFNULL(d.drug_ratio,0)*100),2) AS yzbcy,
                ROUND(((IFNULL(b.mcs_fee/(z.sumfee),0))*100 - IFNULL(d.mcs_fee_rat,0)*100),2) AS hzbcy,
                z.dip_codg AS dipCodg, <!-- DIP编码 -->
                z.DIP_NAME AS dipName, <!-- DIP名称 -->
                z.asst_list_age_grp as asstListAgeGrp,
                z.asst_list_tmor_sev_deg AS asstListTmorSevDeg,
                z.asst_list_dise_sev_deg AS asstListDiseSevDeg,
                ROUND((IFNULL(b.drugfee/z.sumfee,0))*100,2) AS yzb, <!-- 药占比 -->
                ROUND((IFNULL(b.mcs_fee/(z.sumfee),0))*100,2) AS hzb, <!-- 耗占比 -->
                z.setl_sco AS refer_sco,
                z.MED_TYPE AS highLowType,
                z.dfr_fee AS preCost,
            </when>
        </choose>
        ROUND((IFNULL(B20,0) - IFNULL(CONVERT(AES_DECRYPT(UNHEX(c.dip_standard_ipt_days_same_lv
        ),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) USING utf8),0)),2) as zytscy,<!-- 住院天数与标杆差异 -->
        SUBSTRING(a.B15,1,10) AS outHosTime, <!-- 出院时间 -->
        SUBSTRING(a.B12,1,10) AS inHosTime, <!-- 入院时间 -->
        B16C AS deptCode, <!-- 出院科室编码 -->
        e.`NAME` AS deptName, <!-- 出院科室编码 -->
        a.A11 AS name, <!-- 患者姓名 -->
        a.ID AS id, <!-- settle_list_id-->
        a.A48 AS patientId, <!-- 病案号 -->
        TRIM(a.B25C) AS residentCode, <!-- 住院医师编码 -->
        TRIM(a.B25N) AS residentName, <!-- 住院医师姓名 -->
        IFNULL(a.B20,0) AS inHosDays, <!-- 住院天数 -->
        IFNULL(b.drugfee,0) AS ypf, <!-- 药品费 -->
        IFNULL(b.mcs_fee,0) AS clf, <!-- 材料费 -->
        IFNULL(CONVERT(AES_DECRYPT(UNHEX(c.dip_standard_avg_fee_same_lv
        ),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) USING utf8),0) AS bgzyfyjb, <!-- 标杆住院费用（级别） -->
        IFNULL(CONVERT(AES_DECRYPT(UNHEX(c.dip_standard_ipt_days_same_lv
        ),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) USING utf8),0) AS bgzytsjb, <!-- 标杆住院天数（级别） -->
        ROUND(IFNULL(d.drug_ratio,0)*100,2) AS bgyzb, <!-- 标杆药占比 -->
        ROUND(IFNULL(d.mcs_fee_rat,0)*100,2) AS bghzb, <!-- 标杆耗占比 -->
        IFNULL(d.avg_fee,0) AS ljfy,<!-- 例均费用 -->
        IFNULL(CONVERT(AES_DECRYPT(UNHEX(c.dip_standard_avg_fee_same_lv
        ),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) USING utf8),0) / d.avg_fee AS avgCostRate
        FROM som_dip_grp_info b
        INNER JOIN som_hi_invy_bas_info a
        ON a.ID = b.SETTLE_LIST_ID
        <if test="enableSeAns != null and enableSeAns != '' and enableSeAns == 1">
            INNER JOIN som_setl_cas_crsp p
            ON a.K00 = p.K00
        </if>
        LEFT JOIN som_dip_standard c
        ON b.dip_codg = c.dip_codg
        AND SUBSTR(b.dscg_time,1,4) = c.STANDARD_YEAR
--         AND b.is_used_asst_list = c.is_used_asst_list
--         AND b.asst_list_age_grp = c.asst_list_age_grp
--         AND b.asst_list_dise_sev_deg = c.asst_list_dise_sev_deg
--         AND b.asst_list_tmor_sev_deg = c.asst_list_tmor_sev_deg
        AND b.HOSPITAL_ID = c.HOSPITAL_ID
        AND c.ACTIVE_FLAG = 1
        LEFT JOIN som_std_fee d
        ON b.dip_codg = d.`CODE`
        AND SUBSTR(b.dscg_time,1,4) = d.STANDARD_YEAR
--         AND b.asst_list_age_grp = d.asst_list_age_grp
--         AND b.asst_list_dise_sev_deg = d.asst_list_dise_sev_deg
--         AND b.asst_list_tmor_sev_deg = d.asst_list_tmor_sev_deg
        AND d.TYPE = 1
        LEFT JOIN som_dept e
        ON a.B16C = e.`CODE`
        AND a.HOSPITAL_ID = e.HOSPITAL_ID
        <choose>
            <when test="feeStas==1">
                INNER JOIN
                (
                SELECT
                a.rid_idt_codg,
                a.medcas_codg,
                a.adm_time,
                a.dscg_time,
                a.is_in_group,
                a.dip_codg,
                a.DIP_NAME,
                a.asst_list_age_grp,
                a.asst_list_tmor_sev_deg,
                a.asst_list_dise_sev_deg,
                b.MED_TYPE,
                b.sumfee,
                b.setl_sco,
                b.dfr_fee
                FROM som_dip_grp_fbck a
                LEFT JOIN som_fund_dfr_fbck b
                ON a.rid_idt_codg = b.rid_idt_codg
                AND a.HOSPITAL_ID = b.HOSPITAL_ID
                WHERE a.dscg_time between #{begnDate,jdbcType=VARCHAR} and concat(#{expiDate,jdbcType=VARCHAR},'
                23:59:59')
                <include refid="com.my.som.common.mapper.CommonMapper.fbAuth"/>
                ) z
                ON b.PATIENT_ID = z.medcas_codg
                AND SUBSTR(b.adm_time,1,10) = z.adm_time
                AND SUBSTR(b.dscg_time,1,10) = z.dscg_time
            </when>
            <when test="feeStas==0">
                LEFT JOIN som_dip_sco f
                ON a.id = f.SETTLE_LIST_ID
            </when>
        </choose>
        WHERE a.ACTIVE_FLAG = '1'
        <if test="categories != null and categories != ''">
            <choose>
                <when test="categories == 1">
                    and a.A54 in ('1', '01', '310')
                </when>
                <when test="categories == 2">
                    and a.A54 in ('2', '02', '390')
                </when>
                <when test="categories == 9">
                    and a.A54 not in ('1', '01', '310', '2', '02', '390')
                </when>
            </choose>
        </if>
        <include refid="queryCommon"/>
        <if test="dipCodg != null and dipCodg != ''">
            AND b.dip_codg = #{dipCodg,jdbcType=VARCHAR}
        </if>
        <if test="begnDate != null and begnDate != '' and expiDate != null and expiDate != ''">
            and a.b15 between #{begnDate,jdbcType=VARCHAR} and concat(#{expiDate,jdbcType=VARCHAR},' 23:59:59')
        </if>
        <if test="inStartTime != null and inStartTime != '' and inEndTime != null and inEndTime != ''">
            AND a.B12 BETWEEN #{inStartTime} AND CONCAT(#{inEndTime},' 23:59:59')
        </if>
        <if test="seStartTime != null and seStartTime != '' and seEndTime != null and seEndTime != ''">
            AND a.D37 BETWEEN #{seStartTime} AND CONCAT(#{seEndTime},' 23:59:59')
        </if>
        <if test='setlway != null and setlway != "" and setlway == "1"'>
            AND a.setlway = #{setlway,jdbcType=VARCHAR}
        </if>
        ) g
        ) i
        group by dipCodg,dipName,asstListAgeGrp,asstListTmorSevDeg,asstListDiseSevDeg
        ) k
        where
        <![CDATA[
           diff ${gtOrLtSymbol} 0
        ]]>
        ORDER BY diff
    </select>

    <!-- 查询医生亏损 -->
    <select id="queryDoctorLoss" resultType="com.my.som.vo.newBusiness.NewBusinessAnalysisVo">
        select k.*,
        ROUND(highPreTotalCost - highTotalCost,2) AS highDiff,
        ROUND(lowPreTotalCost - lowTotalCost,2) AS lowDiff,
        ROUND(normalPreTotalCost - normalTotalCost,2) AS normalDiff,
        ROUND(normalEarnPreTotalCost - normalEarnTotalCost,2) AS normalEarnDiff
        from(
        select drCodg, drName,
        IFNULL(ROUND(sum(preCost),2),0) as preTotalCost,
        IFNULL(ROUND(avg(sumfee),2),0) AS avgCost,
        IFNULL(ROUND(sum(sumfee),2),0) AS sumfee,
        <choose>
            <when test="feeStas == 0">
                IFNULL(ROUND(sum(preCost) ,2),0) AS diff,
            </when>
            <when test="feeStas == 1">
                IFNULL(ROUND(sum(fbInHosTotalCost),2),0) AS fbTotalCost,
                IFNULL(ROUND(sum(preCost) - sum(fbInHosTotalCost),2),0) AS diff,
            </when>
        </choose>
        <![CDATA[
                   GROUP_CONCAT(CASE WHEN highLowType = '1' AND ( preCost - inHosTotalCost ) < 0 THEN patientId ELSE NULL END) as highPatientId,
                   GROUP_CONCAT(CASE WHEN highLowType = '2' AND ( preCost - inHosTotalCost ) < 0 THEN patientId ELSE NULL END) as lowPatientId,
                   GROUP_CONCAT(CASE WHEN highLowType = '3' and (preCost - inHosTotalCost) < 0 THEN patientId ELSE NULL END) as normalPatientId,
                   GROUP_CONCAT(CASE WHEN highLowType = '3' and (preCost - inHosTotalCost) > 0 then patientId ELSE NULL END) as normalEarnPatientId,
                   IFNULL(ROUND(COUNT(CASE WHEN highLowType = '1' and (preCost - inHosTotalCost) < 0 then inHosTotalCost ELSE null end),2),0) AS highNum,
                   IFNULL(ROUND(SUM(CASE WHEN highLowType = '1' and (preCost - inHosTotalCost) < 0 then inHosTotalCost ELSE null end),2),0) AS highTotalCost,
                   IFNULL(ROUND(SUM(CASE WHEN highLowType = '1' and (preCost - inHosTotalCost) < 0 then preCost ELSE null end),2),0) AS highPreTotalCost,
                   IFNULL(ROUND(COUNT(CASE WHEN highLowType = '2' and (preCost - inHosTotalCost) < 0 then inHosTotalCost ELSE null end),2),0) AS lowNum,
                   IFNULL(ROUND(SUM(CASE WHEN highLowType = '2' and (preCost - inHosTotalCost) < 0 then inHosTotalCost ELSE null end),2),0) AS lowTotalCost,
                   IFNULL(ROUND(SUM(CASE WHEN highLowType = '2' and (preCost - inHosTotalCost) < 0 then preCost ELSE null end),2),0) AS lowPreTotalCost,
                   IFNULL(ROUND(COUNT(CASE WHEN highLowType = '3' and (preCost - inHosTotalCost) < 0 then inHosTotalCost ELSE null end),2),0) AS normalNum,
                   IFNULL(ROUND(SUM(CASE WHEN highLowType = '3' and (preCost - inHosTotalCost) < 0 then inHosTotalCost ELSE null end),2),0) AS normalTotalCost,
                   IFNULL(ROUND(SUM(CASE WHEN highLowType = '3' and (preCost - inHosTotalCost) < 0 then preCost ELSE null end),2),0) AS normalPreTotalCost,
                   IFNULL(ROUND(COUNT(CASE WHEN highLowType = '3' and (preCost - inHosTotalCost) > 0 then inHosTotalCost ELSE null end),2),0) AS normalEarnNum,
                   IFNULL(ROUND(SUM(CASE WHEN highLowType = '3' and (preCost - inHosTotalCost) > 0 then inHosTotalCost ELSE null end),2),0) AS normalEarnTotalCost,
                   IFNULL(ROUND(SUM(CASE WHEN highLowType = '3' and (preCost - inHosTotalCost) > 0 then preCost ELSE null end),2),0) AS normalEarnPreTotalCost
                   ]]>
        from (
        select g.*
        <if test="feeStas==0">
            , g.ycCost AS preCost
        </if>
        from (

        SELECT case when a.A12C=1 then '男' else '女' end as gend,<!-- 性别 -->
        a.a54 AS insuredType,
        a.A14 as age,<!-- 年龄 -->
        D01 AS inHosTotalCost, <!-- 住院总费用 -->
        <choose>
            <when test="feeStas==0">
                IFNULL(ROUND((IFNULL(D01,0) - IFNULL(CONVERT(AES_DECRYPT(UNHEX(c.dip_standard_avg_fee_same_lv
                ),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) USING utf8),0)),2),0) AS
                zyfycy,<!-- 住院费用与标杆差异 -->
                ROUND(((IFNULL(b.drugfee/a.D01,0))*100 - IFNULL(d.drug_ratio,0)*100),2) AS yzbcy,
                ROUND(((IFNULL(b.mcs_fee/(a.D01),0))*100 - IFNULL(d.mcs_fee_rat,0)*100),2) AS hzbcy,
                b.dip_codg AS dipCodg, <!-- DIP编码 -->
                b.DIP_NAME AS dipName, <!-- DIP名称 -->
                b.asst_list_age_grp as asstListAgeGrp,
                b.asst_list_tmor_sev_deg AS asstListTmorSevDeg,
                b.asst_list_dise_sev_deg AS asstListDiseSevDeg,
                ROUND((IFNULL(b.drugfee/a.D01,0))*100,2) AS yzb, <!-- 药占比 -->
                ROUND((IFNULL(b.mcs_fee/(a.D01),0))*100,2) AS hzb, <!-- 耗占比 -->
                f.totl_sco AS refer_sco,
                f.sumfee ,
                f.dise_type AS highLowType,
                f.forecast_fee AS ycCost,
                f.price,
                f.profitloss AS profitloss,
            </when>
            <when test="feeStas==1">
                z.sumfee AS fbInHosTotalCost,<!-- 住院总费用 -->
                IFNULL(ROUND((IFNULL(z.sumfee,0) - IFNULL(CONVERT(AES_DECRYPT(UNHEX(z.dfr_fee
                ),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) USING utf8),0)),2),0) AS
                zyfycy,<!-- 住院费用与医保反馈差异 -->
                ROUND(((IFNULL(b.drugfee/z.sumfee,0))*100 - IFNULL(d.drug_ratio,0)*100),2) AS yzbcy,
                ROUND(((IFNULL(b.mcs_fee/(z.sumfee),0))*100 - IFNULL(d.mcs_fee_rat,0)*100),2) AS hzbcy,
                z.dip_codg AS dipCodg, <!-- DIP编码 -->
                z.DIP_NAME AS dipName, <!-- DIP名称 -->
                z.asst_list_age_grp as asstListAgeGrp,
                z.asst_list_tmor_sev_deg AS asstListTmorSevDeg,
                z.asst_list_dise_sev_deg AS asstListDiseSevDeg,
                ROUND((IFNULL(b.drugfee/z.sumfee,0))*100,2) AS yzb, <!-- 药占比 -->
                ROUND((IFNULL(b.mcs_fee/(z.sumfee),0))*100,2) AS hzb, <!-- 耗占比 -->
                z.setl_sco AS refer_sco,
                z.MED_TYPE AS highLowType,
                z.dfr_fee AS preCost,
            </when>
        </choose>
        a.B25N AS drName,
        a.B25C AS drCodg,
        ROUND((IFNULL(B20,0) - IFNULL(CONVERT(AES_DECRYPT(UNHEX(c.dip_standard_ipt_days_same_lv
        ),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) USING utf8),0)),2) as zytscy,<!-- 住院天数与标杆差异 -->
        SUBSTRING(a.B15,1,10) AS outHosTime, <!-- 出院时间 -->
        SUBSTRING(a.B12,1,10) AS inHosTime, <!-- 入院时间 -->
        B16C AS deptCode, <!-- 出院科室编码 -->
        e.`NAME` AS deptName, <!-- 出院科室编码 -->
        a.A11 AS name, <!-- 患者姓名 -->
        a.ID AS id, <!-- settle_list_id-->
        a.A48 AS patientId, <!-- 病案号 -->
        TRIM(a.B25C) AS residentCode, <!-- 住院医师编码 -->
        TRIM(a.B25N) AS residentName, <!-- 住院医师姓名 -->
        IFNULL(a.B20,0) AS inHosDays, <!-- 住院天数 -->
        IFNULL(b.drugfee,0) AS ypf, <!-- 药品费 -->
        IFNULL(b.mcs_fee,0) AS clf, <!-- 材料费 -->
        IFNULL(CONVERT(AES_DECRYPT(UNHEX(c.dip_standard_avg_fee_same_lv
        ),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) USING utf8),0) AS bgzyfyjb, <!-- 标杆住院费用（级别） -->
        IFNULL(CONVERT(AES_DECRYPT(UNHEX(c.dip_standard_ipt_days_same_lv
        ),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) USING utf8),0) AS bgzytsjb, <!-- 标杆住院天数（级别） -->
        ROUND(IFNULL(d.drug_ratio,0)*100,2) AS bgyzb, <!-- 标杆药占比 -->
        ROUND(IFNULL(d.mcs_fee_rat,0)*100,2) AS bghzb, <!-- 标杆耗占比 -->
        IFNULL(d.avg_fee,0) AS ljfy,<!-- 例均费用 -->
        IFNULL(CONVERT(AES_DECRYPT(UNHEX(c.dip_standard_avg_fee_same_lv
        ),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) USING utf8),0) / d.avg_fee AS avgCostRate
        FROM som_hi_invy_bas_info a
        <if test="enableSeAns != null and enableSeAns != '' and enableSeAns == 1">
            INNER JOIN som_setl_cas_crsp p
            ON a.K00 = p.K00
        </if>
        INNER JOIN som_dip_grp_info b
        ON a.ID = b.SETTLE_LIST_ID
        LEFT JOIN som_dip_standard c
        ON b.dip_codg = c.dip_codg
        AND SUBSTR(b.dscg_time,1,4) = c.STANDARD_YEAR
        AND b.is_used_asst_list = c.is_used_asst_list
        AND b.asst_list_age_grp = c.asst_list_age_grp
        AND b.asst_list_dise_sev_deg = c.asst_list_dise_sev_deg
        AND b.asst_list_tmor_sev_deg = c.asst_list_tmor_sev_deg
        AND b.HOSPITAL_ID = c.HOSPITAL_ID
        AND c.ACTIVE_FLAG = 1
        LEFT JOIN som_std_fee d
        ON b.dip_codg = d.`CODE`
        AND SUBSTR(b.dscg_time,1,4) = d.STANDARD_YEAR
        AND b.asst_list_age_grp = d.asst_list_age_grp
        AND b.asst_list_dise_sev_deg = d.asst_list_dise_sev_deg
        AND b.asst_list_tmor_sev_deg = d.asst_list_tmor_sev_deg
        AND TYPE = 1
        LEFT JOIN som_dept e
        ON a.B16C = e.`CODE`
        AND a.HOSPITAL_ID = e.HOSPITAL_ID
        <choose>
            <when test="feeStas==1">
                INNER JOIN
                (
                SELECT
                a.rid_idt_codg,
                a.medcas_codg,
                a.adm_time,
                a.dscg_time,
                a.is_in_group,
                a.dip_codg,
                a.DIP_NAME,
                a.asst_list_age_grp,
                a.asst_list_tmor_sev_deg,
                a.asst_list_dise_sev_deg,
                b.MED_TYPE,
                b.sumfee,
                b.setl_sco,
                b.dfr_fee
                FROM som_dip_grp_fbck a
                LEFT JOIN som_fund_dfr_fbck b
                ON a.rid_idt_codg = b.rid_idt_codg
                AND a.HOSPITAL_ID = b.HOSPITAL_ID
                WHERE 1=1
                <if test="begnDate != null and begnDate != '' and expiDate != null and expiDate != ''">
                    AND a.dscg_time BETWEEN #{begnDate,jdbcType=VARCHAR} AND concat(#{expiDate,jdbcType=VARCHAR},'
                    23:59:59')
                </if>
                <if test="inStartTime != null and inStartTime != '' and inEndTime != null and inEndTime != ''">
                    AND a.adm_time BETWEEN #{inStartTime} AND CONCAT(#{inEndTime},' 23:59:59')
                </if>
                <if test="seStartTime!=null and seStartTime!='' and seEndTime!=null and seEndTime!=''">
                    AND a.setl_time BETWEEN #{seStartTime} and CONCAT(#{seEndTime},' 23:59:59')
                </if>
                <include refid="com.my.som.common.mapper.CommonMapper.fbAuth"/>
                ) z
                ON b.PATIENT_ID = z.medcas_codg
                AND SUBSTR(b.adm_time,1,10) = z.adm_time
                AND SUBSTR(b.dscg_time,1,10) = z.dscg_time
            </when>
            <when test="feeStas==0">
                LEFT JOIN som_dip_sco f
                ON a.id = f.SETTLE_LIST_ID
            </when>
        </choose>
        WHERE a.ACTIVE_FLAG = '1'
        <include refid="queryCommon"/>
        <if test="dipCodg != null and dipCodg != ''">
            AND b.dip_codg = #{dipCodg,jdbcType=VARCHAR}
        </if>
        <if test="begnDate != null and begnDate != '' and expiDate != null and expiDate != ''">
            and a.b15 between #{begnDate,jdbcType=VARCHAR} and concat(#{expiDate,jdbcType=VARCHAR},' 23:59:59')
        </if>
        <if test="inStartTime != null and inStartTime != '' and inEndTime != null and inEndTime != ''">
            AND a.B12 BETWEEN #{inStartTime} AND CONCAT(#{inEndTime},' 23:59:59')
        </if>
        <if test="seStartTime != null and seStartTime != '' and seEndTime != null and seEndTime != ''">
            AND a.D37 BETWEEN #{seStartTime} AND CONCAT(#{seEndTime},' 23:59:59')
        </if>
        <if test='setlway != null and setlway != "" and setlway == "1"'>
            AND a.setlway = #{setlway,jdbcType=VARCHAR}
        </if>
        ) g
        ) i
        group by drCodg, drName
        ) k
        where
        <![CDATA[
           diff ${gtOrLtSymbol} 0
        ]]>
        ORDER BY diff
    </select>

    <!-- 查询患者亏损 -->
    <select id="queryPatientLoss" resultType="com.my.som.vo.newBusiness.NewBusinessAnalysisVo">
        select k.*
        from(
        select IFNULL(ROUND(preCost,2),0) as preTotalCost,
        IFNULL(ROUND(inHosTotalCost,2),0) AS avgCost,
        IFNULL(ROUND(inHosTotalCost,2),0) AS sumfee,
        <choose>
            <when test="feeStas == 0">
                IFNULL(ROUND(IFNULL(profitloss,0)-IFNULL(preHospExamfee,0),2),0) AS diff,
            </when>
            <when test="feeStas == 1">
                IFNULL(ROUND(fbInHosTotalCost,2),0) AS fbTotalCost,
                IFNULL(ROUND(preCost - fbInHosTotalCost,2),0) AS diff,
            </when>
        </choose>
        name,
        patientId,
        dipCodg,
        dipName,
        asstListAgeGrp,
        asstListTmorSevDeg,
        asstListDiseSevDeg,

        ROUND(com_med_servfee,2) AS com_med_servfee,
        ROUND(ordn_trt_oprt_fee,2) AS ordn_trt_oprt_fee,
        ROUND(ordn_med_servfee,2) AS ordn_med_servfee,
        ROUND(nursfee,2) AS nursfee,
        ROUND(zhylfwqtf,2) AS zhylfwqtf,

        ROUND(diag_fee,2) AS diag_fee,
        ROUND(cas_diag_fee,2) AS cas_diag_fee,
        ROUND(lab_diag_fee,2) AS lab_diag_fee,
        ROUND(rdhy_diag_fee,2) AS rdhy_diag_fee,
        ROUND(clnc_diag_item_fee,2) AS clnc_diag_item_fee,

        ROUND(treat_fee,2) AS treat_fee,
        ROUND(nsrgtrt_item_fee,2) AS nsrgtrt_item_fee,
        ROUND(oprn_treat_fee,2) AS oprn_treat_fee,

        ROUND(rhab_fee,2) AS rhab_fee,

        ROUND(tcmdrug_fee,2) AS tcmdrug_fee,

        ROUND(west_fee,2) AS west_fee,

        ROUND(zyf1,2) AS zyf1,
        ROUND(tcmpat_fee,2) AS tcmpat_fee,
        ROUND(tcmherb,2) AS tcmherb,

        ROUND(blood_blo_pro_fee,2) AS blood_blo_pro_fee,
        ROUND(blo_fee,2) AS blo_fee,
        ROUND(qdblzpf,2) AS qdblzpf,
        ROUND(bdblzpf,2) AS bdblzpf,
        ROUND(nxyzlzpf,2) AS nxyzlzpf,
        ROUND(xbyzlzpf,2) AS xbyzlzpf,

        ROUND(mcs_fee,2) AS mcs_fee,
        ROUND(jcyycxyyclf,2) AS jcyycxyyclf,
        ROUND(trt_use_dspo_med_matlfee,2) AS trt_use_dspo_med_matlfee,
        ROUND(oprn_use_dspo_med_matlfee,2) AS oprn_use_dspo_med_matlfee,

        ROUND(oth_fee,2) AS avgQtf,


        <!-- 标杆 -->
        ROUND(bgzhylfwf * avgCostRate,2) AS bgzhylfwf,
        ROUND(bgybylfwf * avgCostRate,2) AS bgybylfwf,
        ROUND(bgybzlczf * avgCostRate,2) AS bgybzlczf,
        ROUND(bghlf * avgCostRate,2) AS bghlf,
        ROUND(bgzhylfwqtf * avgCostRate,2) AS bgzhylfwqtf,

        ROUND(bgzdf * avgCostRate,2) AS bgzdf,
        ROUND(bgblzdf * avgCostRate,2) AS bgblzdf,
        ROUND(bgsyszdf * avgCostRate,2) AS bgsyszdf,
        ROUND(bgyxxzdf * avgCostRate,2) AS bgyxxzdf,
        ROUND(bglczdxmf * avgCostRate,2) AS bglczdxmf,

        ROUND(bgzlf * avgCostRate,2) AS bgzlf,
        ROUND(bgfsszlxmf * avgCostRate,2) AS bgfsszlxmf,
        ROUND(bgsszlf * avgCostRate,2) AS bgsszlf,

        ROUND(bgkff * avgCostRate,2) AS bgkff,

        ROUND(bgzyf * avgCostRate,2) AS bgzyf,

        ROUND(bgxyf * avgCostRate,2) AS bgxyf,

        ROUND(bgzyf1 * avgCostRate,2) AS bgzyf1,
        ROUND(bgzcyf * avgCostRate,2) AS bgzcyf,
        ROUND(bgzcyf1 * avgCostRate,2) AS bgzcyf1,

        ROUND(bgxyhxyzpf * avgCostRate,2) AS bgxyhxyzpf,
        ROUND(bgxf * avgCostRate,2) AS bgxf,
        ROUND(bgbdblzpf * avgCostRate,2) AS bgbdblzpf,
        ROUND(bgqdblzpf * avgCostRate,2) AS bgqdblzpf,
        ROUND(bgnxyzlzpf * avgCostRate,2) AS bgnxyzlzpf,
        ROUND(bgxbyzlzpf * avgCostRate,2) AS bgxbyzlzpf,

        ROUND(bghcf * avgCostRate,2) AS bghcf,
        ROUND(bgjcyycxyyclf * avgCostRate,2) AS bgjcyycxyyclf,
        ROUND(bgzlyycxyyclf * avgCostRate,2) AS bgzlyycxyyclf,
        ROUND(bgssyycxyyclf * avgCostRate,2) AS bgssyycxyyclf,

        ROUND(bgqtf,2) AS bgqtf,
        <!-- 标杆14项费用 -->
        bgfourteencwf,
        bgfourteenzcf,
        bgfourteenjcf,
        bgfourteenhyf,
        bgfourteenzlf,
        bgfourteenssf,
        bgfourteenhlf,
        bgfourteenwsclf,
        bgfourteenxyf,
        bgfourteenzyypf,
        bgfourteenzcyf,
        bgfourteenybzlf,
        bgfourteenghf,
        bgfourteenqtf,
        <!-- 14项费用 -->
        fourteencwf,
        fourteenzcf,
        fourteenjcf,
        fourteenhyf,
        fourteenzlf,
        fourteenssf,
        fourteenhlf,
        fourteenwsclf,
        fourteenxyf,
        fourteenzyypf,
        fourteenzcyf,
        fourteenybzlf,
        fourteenghf,
        fourteenqtf

        from (
        select g.*
        <if test="feeStas==0">
            , g.ycCost AS preCost
        </if>
        from (

        SELECT case when a.A12C=1 then '男' else '女' end as gend,<!-- 性别 -->
        a.a54 AS insuredType,
        a.A14 as age,<!-- 年龄 -->
        IFNULL(IFNULL(D11,0)+IFNULL(D12,0)+IFNULL(D13,0)+IFNULL(D14,0),0) AS com_med_servfee, <!-- 综合医疗服务费 -->
        IFNULL(D11,0) AS ordn_med_servfee, <!-- 一般医疗服务费 -->
        IFNULL(D12,0) AS ordn_trt_oprt_fee, <!-- 一般治疗操作费 -->
        IFNULL(D13,0) AS nursfee, <!-- 护理费 -->
        IFNULL(D14,0) AS zhylfwqtf, <!-- 综合医疗服务其他费 -->
        IFNULL(IFNULL(D15,0)+IFNULL(D16,0)+IFNULL(D17,0)+IFNULL(D18,0),0) AS diag_fee, <!-- 诊断费 -->
        IFNULL(D15,0) AS cas_diag_fee, <!-- 病理诊断费 -->
        IFNULL(D16,0) AS lab_diag_fee, <!-- 实验室诊断费 -->
        IFNULL(D17,0) AS rdhy_diag_fee, <!-- 影像学诊断费 -->
        IFNULL(D18,0) AS clnc_diag_item_fee, <!-- 临床诊断项目费 -->
        IFNULL(IFNULL(D19,0)+IFNULL(D20,0),0) AS treat_fee, <!-- 治疗费 -->
        IFNULL(D19,0) AS nsrgtrt_item_fee, <!-- 非手术治疗项目费 -->
        IFNULL(D20,0) AS oprn_treat_fee, <!-- 手术治疗费 -->
        IFNULL(D21,0) AS rhab_fee, <!-- 康复费 -->
        IFNULL(D22,0) AS tcmdrug_fee, <!-- 中医费 -->
        IFNULL(D23,0) AS west_fee, <!-- 西药费 -->
        IFNULL(IFNULL(D24,0)+IFNULL(D25,0),0) AS zyf1, <!-- 中药费 -->
        IFNULL(D24,0) AS tcmpat_fee, <!-- 中成药费 -->
        IFNULL(D25,0) AS tcmherb, <!-- 中草药费-->
        IFNULL(IFNULL(D26,0)+IFNULL(D27,0)+IFNULL(D28,0)+IFNULL(D29,0)+IFNULL(D30,0),0) AS blood_blo_pro_fee, <!-- 血液和血液制品费 -->
        IFNULL(D26,0) AS blo_fee, <!-- 血费 -->
        IFNULL(D27,0) AS bdblzpf, <!-- 白蛋白类制品费 -->
        IFNULL(D28,0) AS qdblzpf, <!-- 球蛋白类制品费 -->
        IFNULL(D29,0) AS nxyzlzpf, <!-- 凝血因子类制品费 -->
        IFNULL(D30,0) AS xbyzlzpf, <!-- 细胞因子类制品费 -->
        IFNULL(IFNULL(D31,0)+IFNULL(D32,0)+IFNULL(D33,0),0) AS mcs_fee, <!-- 耗材费 -->
        IFNULL(D31,0) AS jcyycxyyclf, <!-- 检查用一次性医用材料费 -->
        IFNULL(D32,0) AS trt_use_dspo_med_matlfee, <!-- 治疗用一次性医用材料费 -->
        IFNULL(D33,0) AS oprn_use_dspo_med_matlfee, <!-- 手术用一次性医用材料费 -->
        IFNULL(D34,0) AS oth_fee, <!-- 其他费 -->
        D01 AS inHosTotalCost, <!-- 住院总费用 -->
        D02 AS preHospExamfee,
        <choose>
            <when test="feeStas==0">
                IFNULL(ROUND((IFNULL(D01,0) - IFNULL(CONVERT(AES_DECRYPT(UNHEX(c.dip_standard_avg_fee_same_lv
                ),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) USING utf8),0)),2),0) AS
                zyfycy,<!-- 住院费用与标杆差异 -->
                ROUND(((IFNULL(b.drugfee/a.D01,0))*100 - IFNULL(d.drug_ratio,0)*100),2) AS yzbcy,
                ROUND(((IFNULL(b.mcs_fee/(a.D01-b.drugfee),0))*100 - IFNULL(d.mcs_fee_rat,0)*100),2) AS
                hzbcy,
                ROUND((IFNULL(b.drugfee/a.D01,0))*100,2) AS yzb, <!-- 药占比 -->
                ROUND((IFNULL(b.mcs_fee/(a.D01-b.drugfee),0))*100,2) AS hzb, <!-- 耗占比 -->
                b.dip_codg AS dipCodg, <!-- DIP编码 -->
                b.DIP_NAME AS dipName, <!-- DIP名称 -->
                b.asst_list_age_grp as asstListAgeGrp,
                b.asst_list_tmor_sev_deg AS asstListTmorSevDeg,
                b.asst_list_dise_sev_deg AS asstListDiseSevDeg,
                f.totl_sco AS refer_sco,
                f.forecast_fee AS ycCost,
                f.price,
                f.profitloss AS profitloss,
            </when>
            <when test="feeStas==1">
                z.sumfee AS fbInHosTotalCost, <!-- 住院总费用 -->
                IFNULL(ROUND((IFNULL(z.sumfee,0) - IFNULL(CONVERT(AES_DECRYPT(UNHEX(c.dip_standard_avg_fee_same_lv
                ),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) USING utf8),0)),2),0) AS
                zyfycy,<!-- 住院费用与标杆差异 -->
                ROUND(((IFNULL(b.drugfee/z.sumfee,0))*100 - IFNULL(d.drug_ratio,0)*100),2) AS yzbcy,
                ROUND(((IFNULL(b.mcs_fee/(z.sumfee),0))*100 - IFNULL(d.mcs_fee_rat,0)*100),2) AS hzbcy,
                ROUND((IFNULL(b.drugfee/z.sumfee,0))*100,2) AS yzb, <!-- 药占比 -->
                ROUND((IFNULL(b.mcs_fee/(z.sumfee),0))*100,2) AS hzb, <!-- 耗占比 -->
                z.dip_codg AS dipCodg, <!-- DIP编码 -->
                z.DIP_NAME AS dipName, <!-- DIP名称 -->
                z.asst_list_age_grp as asstListAgeGrp,
                z.asst_list_tmor_sev_deg AS asstListTmorSevDeg,
                z.asst_list_dise_sev_deg AS asstListDiseSevDeg,
                z.setl_sco AS refer_sco,
                z.dfr_fee AS preCost,
            </when>
        </choose>
        ROUND((IFNULL(B20,0) - IFNULL(CONVERT(AES_DECRYPT(UNHEX(c.dip_standard_ipt_days_same_lv
        ),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) USING utf8),0)),2) as zytscy,<!-- 住院天数与标杆差异 -->
        SUBSTRING(a.B15,1,10) AS outHosTime, <!-- 出院时间 -->
        SUBSTRING(a.B12,1,10) AS inHosTime, <!-- 入院时间 -->
        B16C AS deptCode, <!-- 出院科室编码 -->
        e.`NAME` AS deptName, <!-- 出院科室编码 -->
        a.A11 AS name, <!-- 患者姓名 -->
        a.ID AS id, <!-- settle_list_id-->
        a.A48 AS patientId, <!-- 病案号 -->
        TRIM(B25C) AS residentCode, <!-- 住院医师编码 -->
        TRIM(B25N) AS residentName, <!-- 住院医师姓名 -->
        IFNULL(B20,0) AS inHosDays, <!-- 住院天数 -->
        IFNULL(b.drugfee,0) AS ypf, <!-- 药品费 -->
        IFNULL(b.mcs_fee,0) AS clf, <!-- 材料费 -->
        IFNULL(CONVERT(AES_DECRYPT(UNHEX(c.dip_standard_avg_fee_same_lv
        ),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) USING utf8),0) AS bgzyfyjb, <!-- 标杆住院费用（级别） -->
        IFNULL(CONVERT(AES_DECRYPT(UNHEX(c.dip_standard_ipt_days_same_lv
        ),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) USING utf8),0) AS bgzytsjb, <!-- 标杆住院天数（级别） -->
        ROUND(IFNULL(d.drug_ratio,0)*100,2) AS bgyzb, <!-- 标杆药占比 -->
        ROUND(IFNULL(d.mcs_fee_rat,0)*100,2) AS bghzb, <!-- 标杆耗占比 -->
        IFNULL(d.avg_fee,0) AS ljfy,<!-- 例均费用 -->
        IFNULL(d.com_med_servfee,0) AS bgzhylfwf, <!-- 标杆综合医疗服务费 -->
        IFNULL(d.ordn_med_servfee,0) AS bgybylfwf, <!-- 标杆一般医疗服务费 -->
        IFNULL(d.ordn_trt_oprt_fee,0) AS bgybzlczf, <!-- 标杆一般治疗操作费 -->
        IFNULL(d.nursfee,0) AS bghlf, <!-- 标杆护理费 -->
        IFNULL(d.oth_fee_com,0) AS bgzhylfwqtf, <!-- 标杆综合医疗服务其他费 -->
        IFNULL(d.diag_fee,0) AS bgzdf, <!-- 标杆诊断费 -->
        IFNULL(d.cas_diag_fee,0) AS bgblzdf, <!-- 标杆病理诊断费 -->
        IFNULL(d.lab_diag_fee,0) AS bgsyszdf, <!-- 标杆实验室诊断费 -->
        IFNULL(d.rdhy_diag_fee,0) AS bgyxxzdf, <!-- 标杆影像学诊断费 -->
        IFNULL(d.clnc_diag_item_fee,0) AS bglczdxmf, <!-- 标杆临床诊断项目费 -->
        IFNULL(d.treat_fee,0) AS bgzlf, <!-- 标杆治疗费 -->
        IFNULL(d.nsrgtrt_item_fee,0) AS bgfsszlxmf, <!-- 标杆非手术治疗项目费 -->
        IFNULL(d.oprn_treat_fee,0) AS bgsszlf, <!-- 标杆手术治疗费 -->
        IFNULL(d.rhab_fee,0) AS bgkff, <!-- 标杆康复费 -->
        IFNULL(d.tcm_treat_fee,0) AS bgzyf, <!-- 标杆中医费 -->
        IFNULL(d.west_fee,0) AS bgxyf, <!-- 标杆西药费 -->
        IFNULL(d.tcmdrug_fee,0) AS bgzyf1, <!-- 标杆中药费 -->
        IFNULL(d.tcmpat_fee,0) AS bgzcyf, <!-- 标杆中成药费 -->
        IFNULL(d.tcmherb,0) AS bgzcyf1, <!-- 标杆中草药费 -->
        IFNULL(d.blood_blo_pro_fee,0) AS bgxyhxyzpf, <!-- 标杆血液和血液制品费 -->
        IFNULL(d.blo_fee,0) AS bgxf, <!-- 标杆血费 -->
        IFNULL(d.albu_clss_prod_fee,0) AS bgbdblzpf, <!-- 标杆白蛋白类制品费 -->
        IFNULL(d.glon_clss_prod_fee,0) AS bgqdblzpf, <!-- 标杆球蛋白类制品费 -->
        IFNULL(d.clotfac_clss_prod_fee,0) AS bgnxyzlzpf, <!-- 标杆凝血因子类制品费 -->
        IFNULL(d.cyki_clss_prod_fee,0) AS bgxbyzlzpf, <!-- 标杆细胞因子类制品费 -->
        IFNULL(d.mcs_fee,0) AS bghcf, <!-- 标杆耗材费 -->
        IFNULL(d.exam_use_dspo_med_matlfee,0) AS bgjcyycxyyclf, <!-- 标杆检查用一次性医用材料费 -->
        IFNULL(d.trt_use_dspo_med_matlfee,0) AS bgzlyycxyyclf, <!-- 标杆治疗用一次性医用材料费 -->
        IFNULL(d.oprn_use_dspo_med_matlfee,0) AS bgssyycxyyclf, <!-- 标杆手术用一次性医用材料费 -->
        IFNULL(d.oth_fee,0) AS bgqtf, <!-- 标杆其他费 -->
        1 AS avgCostRate,
        <!-- 标杆14项费用 -->
        IFNULL( d.medi_fee_type_bedfee, 0 ) AS bgfourteencwf,
        IFNULL( d.medi_fee_type_diag_fee, 0 ) AS bgfourteenzcf,
        IFNULL( d.medi_fee_type_examfee, 0 ) AS bgfourteenjcf,
        IFNULL( d.medi_fee_type_asy_fee, 0 ) AS bgfourteenhyf,
        IFNULL( d.medi_fee_type_treat_fee, 0 ) AS bgfourteenzlf,
        IFNULL( d.medi_fee_type_oper_fee, 0 ) AS bgfourteenssf,
        IFNULL( d.medi_fee_type_nursfee, 0 ) AS bgfourteenhlf,
        IFNULL( d.medi_fee_type_hc_matlfee, 0 ) AS bgfourteenwsclf,
        IFNULL( d.medi_fee_type_west_fee, 0 ) AS bgfourteenxyf,
        IFNULL( d.medi_fee_type_tmdp_fee, 0 ) AS bgfourteenzyypf,
        IFNULL( d.medi_fee_type_tcmpat_fee, 0 ) AS bgfourteenzcyf,
        IFNULL( d.medi_fee_type_ordn_trtfee, 0 ) AS bgfourteenybzlf,
        IFNULL( d.medi_fee_type_regfee, 0 ) AS bgfourteenghf,
        IFNULL( d.medi_fee_type_oth_fee, 0 ) AS bgfourteenqtf,
        <!-- 14项费用 -->
        i.fourteencwf,
        i.fourteenzcf,
        i.fourteenjcf,
        i.fourteenhyf,
        i.fourteenzlf,
        i.fourteenssf,
        i.fourteenhlf,
        i.fourteenwsclf,
        i.fourteenxyf,
        i.fourteenzyypf,
        i.fourteenzcyf,
        i.fourteenybzlf,
        i.fourteenghf,
        i.fourteenqtf
        FROM som_hi_invy_bas_info a
        <if test="enableSeAns != null and enableSeAns != '' and enableSeAns == 1">
            INNER JOIN som_setl_cas_crsp p
            ON a.K00 = p.K00
        </if>
        INNER JOIN som_dip_grp_info b
        ON a.ID = b.SETTLE_LIST_ID
        LEFT JOIN som_dip_standard c
        ON b.dip_codg = c.dip_codg
        AND SUBSTR(b.dscg_time,1,4) = c.STANDARD_YEAR
        AND b.is_used_asst_list = c.is_used_asst_list
        AND b.asst_list_age_grp = c.asst_list_age_grp
        AND b.asst_list_dise_sev_deg = c.asst_list_dise_sev_deg
        AND b.asst_list_tmor_sev_deg = c.asst_list_tmor_sev_deg
        AND b.HOSPITAL_ID = c.HOSPITAL_ID
        AND c.ACTIVE_FLAG = 1
        LEFT JOIN som_std_fee d
        ON b.dip_codg = d.`CODE`
        AND SUBSTR(b.dscg_time,1,4) = d.STANDARD_YEAR
        AND b.asst_list_age_grp = d.asst_list_age_grp
        AND b.asst_list_dise_sev_deg = d.asst_list_dise_sev_deg
        AND b.asst_list_tmor_sev_deg = d.asst_list_tmor_sev_deg
        AND TYPE = 1
        LEFT JOIN som_dept e
        ON a.B16C = e.`CODE`
        AND a.HOSPITAL_ID = e.HOSPITAL_ID
        <choose>
            <when test="feeStas==1">
                INNER JOIN
                (
                SELECT
                a.rid_idt_codg,
                a.medcas_codg,
                a.adm_time,
                a.dscg_time,
                a.is_in_group,
                a.dip_codg,
                a.DIP_NAME,
                a.asst_list_age_grp,
                a.asst_list_tmor_sev_deg,
                a.asst_list_dise_sev_deg,
                b.MED_TYPE,
                b.sumfee,
                b.setl_sco,
                b.dfr_fee
                FROM som_dip_grp_fbck a
                LEFT JOIN som_fund_dfr_fbck b
                ON a.rid_idt_codg = b.rid_idt_codg
                AND a.HOSPITAL_ID = b.HOSPITAL_ID
                WHERE 1=1
                <if test="begnDate != null and begnDate != '' and expiDate != null and expiDate != ''">
                    AND a.dscg_time BETWEEN #{begnDate,jdbcType=VARCHAR} AND concat(#{expiDate,jdbcType=VARCHAR},'
                    23:59:59')
                </if>
                <if test="inStartTime != null and inStartTime != '' and inEndTime != null and inEndTime != ''">
                    AND a.adm_time BETWEEN #{inStartTime} AND CONCAT(#{inEndTime},' 23:59:59')
                </if>
                <if test="seStartTime!=null and seStartTime!='' and seEndTime!=null and seEndTime!=''">
                    AND a.setl_time BETWEEN #{seStartTime} and CONCAT(#{seEndTime},' 23:59:59')
                </if>
                <include refid="com.my.som.common.mapper.CommonMapper.fbAuth"/>
                ) z
                ON b.PATIENT_ID = z.medcas_codg
                AND SUBSTR(b.adm_time,1,10) = z.adm_time
                AND SUBSTR(b.dscg_time,1,10) = z.dscg_time
            </when>
            <when test="feeStas==0">
                LEFT JOIN som_dip_sco f
                ON a.id = f.SETTLE_LIST_ID
            </when>
        </choose>
        LEFT JOIN (
        SELECT
        hi_setl_invy_id,
        MAX( CASE WHEN med_chrg_itemname = '床位费' THEN amt ELSE 0 END ) AS fourteencwf,
        MAX( CASE WHEN med_chrg_itemname = '诊查费' THEN amt ELSE 0 END ) AS fourteenzcf,
        MAX( CASE WHEN med_chrg_itemname = '检查费' THEN amt ELSE 0 END ) AS fourteenjcf,
        MAX( CASE WHEN med_chrg_itemname = '化验费' THEN amt ELSE 0 END ) AS fourteenhyf,
        MAX( CASE WHEN med_chrg_itemname = '治疗费' THEN amt ELSE 0 END ) AS fourteenzlf,
        MAX( CASE WHEN med_chrg_itemname = '手术费' THEN amt ELSE 0 END ) AS fourteenssf,
        MAX( CASE WHEN med_chrg_itemname = '护理费' THEN amt ELSE 0 END ) AS fourteenhlf,
        MAX( CASE WHEN med_chrg_itemname = '卫生材料费' THEN amt ELSE 0 END ) AS fourteenwsclf,
        MAX( CASE WHEN med_chrg_itemname = '西药费' THEN amt ELSE 0 END ) AS fourteenxyf,
        MAX( CASE WHEN med_chrg_itemname = '中药饮片费' THEN amt ELSE 0 END ) AS fourteenzyypf,
        MAX( CASE WHEN med_chrg_itemname = '中成药费' THEN amt ELSE 0 END ) AS fourteenzcyf,
        MAX( CASE WHEN med_chrg_itemname = '一般诊疗费' THEN amt ELSE 0 END ) AS fourteenybzlf,
        MAX( CASE WHEN med_chrg_itemname = '挂号费' THEN amt ELSE 0 END ) AS fourteenghf,
        MAX( CASE WHEN med_chrg_itemname = '其他费' THEN amt ELSE 0 END ) AS fourteenqtf
        FROM
        som_hi_setl_invy_med_fee_info
        WHERE
        hi_setl_invy_id IN ( SELECT ID AS hi_setl_invy_id FROM som_hi_invy_bas_info WHERE 1=1
        <if test="begnDate != null and begnDate != '' and expiDate != null and expiDate != ''">
            and B15 between #{begnDate,jdbcType=VARCHAR} and concat(#{expiDate,jdbcType=VARCHAR},' 23:59:59')
        </if>
        <if test="inStartTime != null and inStartTime != '' and inEndTime != null and inEndTime != ''">
            AND B12 BETWEEN #{inStartTime} AND CONCAT(#{inEndTime},' 23:59:59')
        </if>
        <if test="seStartTime != null and seStartTime != '' and seEndTime != null and seEndTime != ''">
            AND D37 BETWEEN #{seStartTime} AND CONCAT(#{seEndTime},' 23:59:59')
        </if>
        )
        GROUP BY
        hi_setl_invy_id
        ) i
        ON a.ID = i.hi_setl_invy_id
        WHERE a.ACTIVE_FLAG = '1'
        <if test="categories != null and categories != ''">
            <choose>
                <when test="categories == 1">
                    and a.A54 in ('1', '01', '310')
                </when>
                <when test="categories == 2">
                    and a.A54 in ('2', '02', '390')
                </when>
                <when test="categories == 9">
                    and a.A54 not in ('1', '01', '310', '2', '02', '390')
                </when>
            </choose>
        </if>
        <include refid="queryCommon"/>
        <if test="dipCodg != null and dipCodg != ''">
            AND b.dip_codg = #{dipCodg,jdbcType=VARCHAR}
        </if>
        <if test="begnDate != null and begnDate != '' and expiDate != null and expiDate != ''">
            and a.B15 between #{begnDate,jdbcType=VARCHAR} and concat(#{expiDate,jdbcType=VARCHAR},' 23:59:59')
        </if>
        <if test="inStartTime != null and inStartTime != '' and inEndTime != null and inEndTime != ''">
            AND a.B12 BETWEEN #{inStartTime} AND CONCAT(#{inEndTime},' 23:59:59')
        </if>
        <if test="seStartTime != null and seStartTime != '' and seEndTime != null and seEndTime != ''">
            AND a.D37 BETWEEN #{seStartTime} AND CONCAT(#{seEndTime},' 23:59:59')
        </if>
        <if test='setlway != null and setlway != "" and setlway == "1"'>
            AND a.setlway = #{setlway,jdbcType=VARCHAR}
        </if>
        ) g

        ) i
        ) k
        where
        <![CDATA[
       diff ${gtOrLtSymbol} 0
    ]]>
        ORDER BY diff
    </select>

    <!-- 查询患者亏损 -->
    <select id="queryPatientLoss2" resultType="com.my.som.vo.newBusiness.NewBusinessAnalysisVo">
        select k.*
        from(
        select IFNULL(ROUND(preCost,2),0) as preTotalCost,
        IFNULL(ROUND(inHosTotalCost,2),0) AS avgCost,
        IFNULL(ROUND(inHosTotalCost,2),0) AS sumfee,
        <choose>
            <when test="feeStas == 0">
                IFNULL(ROUND(preCost - inHosTotalCost,2),0) AS diff,
            </when>
            <when test="feeStas == 1">
                IFNULL(ROUND(fbInHosTotalCost,2),0) AS fbTotalCost,
                IFNULL(ROUND(preCost - fbInHosTotalCost,2),0) AS diff,
            </when>
        </choose>
        name,
        patientId,
        dipCodg,
        dipName,

        ROUND(com_med_servfee,2) AS com_med_servfee,
        ROUND(ordn_trt_oprt_fee,2) AS ordn_trt_oprt_fee,
        ROUND(ordn_med_servfee,2) AS ordn_med_servfee,
        ROUND(nursfee,2) AS nursfee,
        ROUND(zhylfwqtf,2) AS zhylfwqtf,

        ROUND(diag_fee,2) AS diag_fee,
        ROUND(cas_diag_fee,2) AS cas_diag_fee,
        ROUND(lab_diag_fee,2) AS lab_diag_fee,
        ROUND(rdhy_diag_fee,2) AS rdhy_diag_fee,
        ROUND(clnc_diag_item_fee,2) AS clnc_diag_item_fee,

        ROUND(treat_fee,2) AS treat_fee,
        ROUND(nsrgtrt_item_fee,2) AS nsrgtrt_item_fee,
        ROUND(oprn_treat_fee,2) AS oprn_treat_fee,

        ROUND(rhab_fee,2) AS rhab_fee,

        ROUND(tcmdrug_fee,2) AS tcmdrug_fee,

        ROUND(west_fee,2) AS west_fee,

        ROUND(zyf1,2) AS zyf1,
        ROUND(tcmpat_fee,2) AS tcmpat_fee,
        ROUND(tcmherb,2) AS tcmherb,

        ROUND(blood_blo_pro_fee,2) AS blood_blo_pro_fee,
        ROUND(blo_fee,2) AS blo_fee,
        ROUND(qdblzpf,2) AS qdblzpf,
        ROUND(bdblzpf,2) AS bdblzpf,
        ROUND(nxyzlzpf,2) AS nxyzlzpf,
        ROUND(xbyzlzpf,2) AS xbyzlzpf,

        ROUND(mcs_fee,2) AS mcs_fee,
        ROUND(jcyycxyyclf,2) AS jcyycxyyclf,
        ROUND(trt_use_dspo_med_matlfee,2) AS trt_use_dspo_med_matlfee,
        ROUND(oprn_use_dspo_med_matlfee,2) AS oprn_use_dspo_med_matlfee,

        ROUND(oth_fee,2) AS avgQtf,


        <!-- 标杆 -->
        ROUND(bgzhylfwf * avgCostRate,2) AS bgzhylfwf,
        ROUND(bgybylfwf * avgCostRate,2) AS bgybylfwf,
        ROUND(bgybzlczf * avgCostRate,2) AS bgybzlczf,
        ROUND(bghlf * avgCostRate,2) AS bghlf,
        ROUND(bgzhylfwqtf * avgCostRate,2) AS bgzhylfwqtf,

        ROUND(bgzdf * avgCostRate,2) AS bgzdf,
        ROUND(bgblzdf * avgCostRate,2) AS bgblzdf,
        ROUND(bgsyszdf * avgCostRate,2) AS bgsyszdf,
        ROUND(bgyxxzdf * avgCostRate,2) AS bgyxxzdf,
        ROUND(bglczdxmf * avgCostRate,2) AS bglczdxmf,

        ROUND(bgzlf * avgCostRate,2) AS bgzlf,
        ROUND(bgfsszlxmf * avgCostRate,2) AS bgfsszlxmf,
        ROUND(bgsszlf * avgCostRate,2) AS bgsszlf,

        ROUND(bgkff * avgCostRate,2) AS bgkff,

        ROUND(bgzyf * avgCostRate,2) AS bgzyf,

        ROUND(bgxyf * avgCostRate,2) AS bgxyf,

        ROUND(bgzyf1 * avgCostRate,2) AS bgzyf1,
        ROUND(bgzcyf * avgCostRate,2) AS bgzcyf,
        ROUND(bgzcyf1 * avgCostRate,2) AS bgzcyf1,

        ROUND(bgxyhxyzpf * avgCostRate,2) AS bgxyhxyzpf,
        ROUND(bgxf * avgCostRate,2) AS bgxf,
        ROUND(bgbdblzpf * avgCostRate,2) AS bgbdblzpf,
        ROUND(bgqdblzpf * avgCostRate,2) AS bgqdblzpf,
        ROUND(bgnxyzlzpf * avgCostRate,2) AS bgnxyzlzpf,
        ROUND(bgxbyzlzpf * avgCostRate,2) AS bgxbyzlzpf,

        ROUND(bghcf * avgCostRate,2) AS bghcf,
        ROUND(bgjcyycxyyclf * avgCostRate,2) AS bgjcyycxyyclf,
        ROUND(bgzlyycxyyclf * avgCostRate,2) AS bgzlyycxyyclf,
        ROUND(bgssyycxyyclf * avgCostRate,2) AS bgssyycxyyclf,

        ROUND(bgqtf,2) AS bgqtf,
        <!-- 标杆14项费用 -->
        bgfourteencwf,
        bgfourteenzcf,
        bgfourteenjcf,
        bgfourteenhyf,
        bgfourteenzlf,
        bgfourteenssf,
        bgfourteenhlf,
        bgfourteenwsclf,
        bgfourteenxyf,
        bgfourteenzyypf,
        bgfourteenzcyf,
        bgfourteenybzlf,
        bgfourteenghf,
        bgfourteenqtf,
        <!-- 14项费用 -->
        fourteencwf,
        fourteenzcf,
        fourteenjcf,
        fourteenhyf,
        fourteenzlf,
        fourteenssf,
        fourteenhlf,
        fourteenwsclf,
        fourteenxyf,
        fourteenzyypf,
        fourteenzcyf,
        fourteenybzlf,
        fourteenghf,
        fourteenqtf

        from (
        select g.*
        <if test="feeStas==0">
            , g.ycCost AS preCost
        </if>
        from (

        SELECT case when a.A12C=1 then '男' else '女' end as gend,<!-- 性别 -->
        a.a54 AS insuredType,
        a.A14 as age,<!-- 年龄 -->
        IFNULL(IFNULL(D11,0)+IFNULL(D12,0)+IFNULL(D13,0)+IFNULL(D14,0),0) AS com_med_servfee, <!-- 综合医疗服务费 -->
        IFNULL(D11,0) AS ordn_med_servfee, <!-- 一般医疗服务费 -->
        IFNULL(D12,0) AS ordn_trt_oprt_fee, <!-- 一般治疗操作费 -->
        IFNULL(D13,0) AS nursfee, <!-- 护理费 -->
        IFNULL(D14,0) AS zhylfwqtf, <!-- 综合医疗服务其他费 -->
        IFNULL(IFNULL(D15,0)+IFNULL(D16,0)+IFNULL(D17,0)+IFNULL(D18,0),0) AS diag_fee, <!-- 诊断费 -->
        IFNULL(D15,0) AS cas_diag_fee, <!-- 病理诊断费 -->
        IFNULL(D16,0) AS lab_diag_fee, <!-- 实验室诊断费 -->
        IFNULL(D17,0) AS rdhy_diag_fee, <!-- 影像学诊断费 -->
        IFNULL(D18,0) AS clnc_diag_item_fee, <!-- 临床诊断项目费 -->
        IFNULL(IFNULL(D19,0)+IFNULL(D20,0),0) AS treat_fee, <!-- 治疗费 -->
        IFNULL(D19,0) AS nsrgtrt_item_fee, <!-- 非手术治疗项目费 -->
        IFNULL(D20,0) AS oprn_treat_fee, <!-- 手术治疗费 -->
        IFNULL(D21,0) AS rhab_fee, <!-- 康复费 -->
        IFNULL(D22,0) AS tcmdrug_fee, <!-- 中医费 -->
        IFNULL(D23,0) AS west_fee, <!-- 西药费 -->
        IFNULL(IFNULL(D24,0)+IFNULL(D25,0),0) AS zyf1, <!-- 中药费 -->
        IFNULL(D24,0) AS tcmpat_fee, <!-- 中成药费 -->
        IFNULL(D25,0) AS tcmherb, <!-- 中草药费-->
        IFNULL(IFNULL(D26,0)+IFNULL(D27,0)+IFNULL(D28,0)+IFNULL(D29,0)+IFNULL(D30,0),0) AS blood_blo_pro_fee, <!-- 血液和血液制品费 -->
        IFNULL(D26,0) AS blo_fee, <!-- 血费 -->
        IFNULL(D27,0) AS bdblzpf, <!-- 白蛋白类制品费 -->
        IFNULL(D28,0) AS qdblzpf, <!-- 球蛋白类制品费 -->
        IFNULL(D29,0) AS nxyzlzpf, <!-- 凝血因子类制品费 -->
        IFNULL(D30,0) AS xbyzlzpf, <!-- 细胞因子类制品费 -->
        IFNULL(IFNULL(D31,0)+IFNULL(D32,0)+IFNULL(D33,0),0) AS mcs_fee, <!-- 耗材费 -->
        IFNULL(D31,0) AS jcyycxyyclf, <!-- 检查用一次性医用材料费 -->
        IFNULL(D32,0) AS trt_use_dspo_med_matlfee, <!-- 治疗用一次性医用材料费 -->
        IFNULL(D33,0) AS oprn_use_dspo_med_matlfee, <!-- 手术用一次性医用材料费 -->
        IFNULL(D34,0) AS oth_fee, <!-- 其他费 -->
        D01 AS inHosTotalCost, <!-- 住院总费用 -->
        <choose>
            <when test="feeStas==0">
                IFNULL(ROUND((IFNULL(D01,0) - IFNULL(CONVERT(AES_DECRYPT(UNHEX(c.dip_standard_avg_fee_same_lv
                ),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) USING utf8),0)),2),0) AS
                zyfycy,<!-- 住院费用与标杆差异 -->
                ROUND(((IFNULL(b.drugfee/a.D01,0))*100 - IFNULL(d.drug_ratio,0)*100),2) AS yzbcy,
                ROUND(((IFNULL(b.mcs_fee/(a.D01-b.drugfee),0))*100 - IFNULL(d.mcs_fee_rat,0)*100),2) AS
                hzbcy,
                ROUND((IFNULL(b.drugfee/a.D01,0))*100,2) AS yzb, <!-- 药占比 -->
                ROUND((IFNULL(b.mcs_fee/(a.D01-b.drugfee),0))*100,2) AS hzb, <!-- 耗占比 -->
                b.dip_codg AS dipCodg, <!-- DIP编码 -->
                b.DIP_NAME AS dipName, <!-- DIP名称 -->
                b.asst_list_age_grp as asstListAgeGrp,
                b.asst_list_tmor_sev_deg AS asstListTmorSevDeg,
                b.asst_list_dise_sev_deg AS asstListDiseSevDeg,
                f.sumfee,
                f.totl_sco AS refer_sco,
                f.forecast_fee AS ycCost,
                f.price,
                f.profitloss AS profitloss,
            </when>
            <when test="feeStas==1">
                z.sumfee AS fbInHosTotalCost, <!-- 住院总费用 -->
                IFNULL(ROUND((IFNULL(z.sumfee,0) - IFNULL(CONVERT(AES_DECRYPT(UNHEX(c.dip_standard_avg_fee_same_lv
                ),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) USING utf8),0)),2),0) AS
                zyfycy,<!-- 住院费用与标杆差异 -->
                ROUND(((IFNULL(b.drugfee/z.sumfee,0))*100 - IFNULL(d.drug_ratio,0)*100),2) AS yzbcy,
                ROUND(((IFNULL(b.mcs_fee/(z.sumfee),0))*100 - IFNULL(d.mcs_fee_rat,0)*100),2) AS hzbcy,
                ROUND((IFNULL(b.drugfee/z.sumfee,0))*100,2) AS yzb, <!-- 药占比 -->
                ROUND((IFNULL(b.mcs_fee/(z.sumfee),0))*100,2) AS hzb, <!-- 耗占比 -->
                z.dip_codg AS dipCodg, <!-- DIP编码 -->
                z.DIP_NAME AS dipName, <!-- DIP名称 -->
                z.asst_list_age_grp as asstListAgeGrp,
                z.asst_list_tmor_sev_deg AS asstListTmorSevDeg,
                z.asst_list_dise_sev_deg AS asstListDiseSevDeg,
                z.setl_sco AS refer_sco,
                z.dfr_fee AS preCost,
            </when>
        </choose>
        ROUND((IFNULL(B20,0) - IFNULL(CONVERT(AES_DECRYPT(UNHEX(c.dip_standard_ipt_days_same_lv
        ),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) USING utf8),0)),2) as zytscy,<!-- 住院天数与标杆差异 -->
        SUBSTRING(a.B15,1,10) AS outHosTime, <!-- 出院时间 -->
        SUBSTRING(a.B12,1,10) AS inHosTime, <!-- 入院时间 -->
        B16C AS deptCode, <!-- 出院科室编码 -->
        e.`NAME` AS deptName, <!-- 出院科室编码 -->
        a.A11 AS name, <!-- 患者姓名 -->
        a.ID AS id, <!-- settle_list_id-->
        a.A48 AS patientId, <!-- 病案号 -->
        TRIM(B25C) AS residentCode, <!-- 住院医师编码 -->
        TRIM(B25N) AS residentName, <!-- 住院医师姓名 -->
        IFNULL(B20,0) AS inHosDays, <!-- 住院天数 -->
        IFNULL(b.drugfee,0) AS ypf, <!-- 药品费 -->
        IFNULL(b.mcs_fee,0) AS clf, <!-- 材料费 -->
        IFNULL(CONVERT(AES_DECRYPT(UNHEX(c.dip_standard_avg_fee_same_lv
        ),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) USING utf8),0) AS bgzyfyjb, <!-- 标杆住院费用（级别） -->
        IFNULL(CONVERT(AES_DECRYPT(UNHEX(c.dip_standard_ipt_days_same_lv
        ),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) USING utf8),0) AS bgzytsjb, <!-- 标杆住院天数（级别） -->
        ROUND(IFNULL(d.drug_ratio,0)*100,2) AS bgyzb, <!-- 标杆药占比 -->
        ROUND(IFNULL(d.mcs_fee_rat,0)*100,2) AS bghzb, <!-- 标杆耗占比 -->
        IFNULL(d.avg_fee,0) AS ljfy,<!-- 例均费用 -->
        IFNULL(d.com_med_servfee,0) AS bgzhylfwf, <!-- 标杆综合医疗服务费 -->
        IFNULL(d.ordn_med_servfee,0) AS bgybylfwf, <!-- 标杆一般医疗服务费 -->
        IFNULL(d.ordn_trt_oprt_fee,0) AS bgybzlczf, <!-- 标杆一般治疗操作费 -->
        IFNULL(d.nursfee,0) AS bghlf, <!-- 标杆护理费 -->
        IFNULL(d.oth_fee_com,0) AS bgzhylfwqtf, <!-- 标杆综合医疗服务其他费 -->
        IFNULL(d.diag_fee,0) AS bgzdf, <!-- 标杆诊断费 -->
        IFNULL(d.cas_diag_fee,0) AS bgblzdf, <!-- 标杆病理诊断费 -->
        IFNULL(d.lab_diag_fee,0) AS bgsyszdf, <!-- 标杆实验室诊断费 -->
        IFNULL(d.rdhy_diag_fee,0) AS bgyxxzdf, <!-- 标杆影像学诊断费 -->
        IFNULL(d.clnc_diag_item_fee,0) AS bglczdxmf, <!-- 标杆临床诊断项目费 -->
        IFNULL(d.treat_fee,0) AS bgzlf, <!-- 标杆治疗费 -->
        IFNULL(d.nsrgtrt_item_fee,0) AS bgfsszlxmf, <!-- 标杆非手术治疗项目费 -->
        IFNULL(d.oprn_treat_fee,0) AS bgsszlf, <!-- 标杆手术治疗费 -->
        IFNULL(d.rhab_fee,0) AS bgkff, <!-- 标杆康复费 -->
        IFNULL(d.tcm_treat_fee,0) AS bgzyf, <!-- 标杆中医费 -->
        IFNULL(d.west_fee,0) AS bgxyf, <!-- 标杆西药费 -->
        IFNULL(d.tcmdrug_fee,0) AS bgzyf1, <!-- 标杆中药费 -->
        IFNULL(d.tcmpat_fee,0) AS bgzcyf, <!-- 标杆中成药费 -->
        IFNULL(d.tcmherb,0) AS bgzcyf1, <!-- 标杆中草药费 -->
        IFNULL(d.blood_blo_pro_fee,0) AS bgxyhxyzpf, <!-- 标杆血液和血液制品费 -->
        IFNULL(d.blo_fee,0) AS bgxf, <!-- 标杆血费 -->
        IFNULL(d.albu_clss_prod_fee,0) AS bgbdblzpf, <!-- 标杆白蛋白类制品费 -->
        IFNULL(d.glon_clss_prod_fee,0) AS bgqdblzpf, <!-- 标杆球蛋白类制品费 -->
        IFNULL(d.clotfac_clss_prod_fee,0) AS bgnxyzlzpf, <!-- 标杆凝血因子类制品费 -->
        IFNULL(d.cyki_clss_prod_fee,0) AS bgxbyzlzpf, <!-- 标杆细胞因子类制品费 -->
        IFNULL(d.mcs_fee,0) AS bghcf, <!-- 标杆耗材费 -->
        IFNULL(d.exam_use_dspo_med_matlfee,0) AS bgjcyycxyyclf, <!-- 标杆检查用一次性医用材料费 -->
        IFNULL(d.trt_use_dspo_med_matlfee,0) AS bgzlyycxyyclf, <!-- 标杆治疗用一次性医用材料费 -->
        IFNULL(d.oprn_use_dspo_med_matlfee,0) AS bgssyycxyyclf, <!-- 标杆手术用一次性医用材料费 -->
        IFNULL(d.oth_fee,0) AS bgqtf, <!-- 标杆其他费 -->
        1 AS avgCostRate,
        <!-- 标杆14项费用 -->
        IFNULL( d.medi_fee_type_bedfee, 0 ) AS bgfourteencwf,
        IFNULL( d.medi_fee_type_diag_fee, 0 ) AS bgfourteenzcf,
        IFNULL( d.medi_fee_type_examfee, 0 ) AS bgfourteenjcf,
        IFNULL( d.medi_fee_type_asy_fee, 0 ) AS bgfourteenhyf,
        IFNULL( d.medi_fee_type_treat_fee, 0 ) AS bgfourteenzlf,
        IFNULL( d.medi_fee_type_oper_fee, 0 ) AS bgfourteenssf,
        IFNULL( d.medi_fee_type_nursfee, 0 ) AS bgfourteenhlf,
        IFNULL( d.medi_fee_type_hc_matlfee, 0 ) AS bgfourteenwsclf,
        IFNULL( d.medi_fee_type_west_fee, 0 ) AS bgfourteenxyf,
        IFNULL( d.medi_fee_type_tmdp_fee, 0 ) AS bgfourteenzyypf,
        IFNULL( d.medi_fee_type_tcmpat_fee, 0 ) AS bgfourteenzcyf,
        IFNULL( d.medi_fee_type_ordn_trtfee, 0 ) AS bgfourteenybzlf,
        IFNULL( d.medi_fee_type_regfee, 0 ) AS bgfourteenghf,
        IFNULL( d.medi_fee_type_oth_fee, 0 ) AS bgfourteenqtf,
        <!-- 14项费用 -->
        i.fourteencwf,
        i.fourteenzcf,
        i.fourteenjcf,
        i.fourteenhyf,
        i.fourteenzlf,
        i.fourteenssf,
        i.fourteenhlf,
        i.fourteenwsclf,
        i.fourteenxyf,
        i.fourteenzyypf,
        i.fourteenzcyf,
        i.fourteenybzlf,
        i.fourteenghf,
        i.fourteenqtf
        FROM som_hi_invy_bas_info a
        <if test="enableSeAns != null and enableSeAns != '' and enableSeAns == 1">
            INNER JOIN som_setl_cas_crsp p
            ON a.K00 = p.K00
        </if>
        INNER JOIN som_dip_grp_info b
        ON a.ID = b.SETTLE_LIST_ID
        LEFT JOIN som_dip_standard c
        ON b.dip_codg = c.dip_codg
        AND SUBSTR(b.dscg_time,1,4) = c.STANDARD_YEAR
        AND b.is_used_asst_list = c.is_used_asst_list
        AND b.asst_list_age_grp = c.asst_list_age_grp
        AND b.asst_list_dise_sev_deg = c.asst_list_dise_sev_deg
        AND b.asst_list_tmor_sev_deg = c.asst_list_tmor_sev_deg
        AND b.HOSPITAL_ID = c.HOSPITAL_ID
        AND c.ACTIVE_FLAG = 1
        LEFT JOIN std_cost_dy_sj d
        ON b.dip_codg = d.`CODE`
        AND SUBSTR(b.dscg_time,1,4) = d.STANDARD_YEAR
        AND b.asst_list_age_grp = d.asst_list_age_grp
        AND b.asst_list_dise_sev_deg = d.asst_list_dise_sev_deg
        AND b.asst_list_tmor_sev_deg = d.asst_list_tmor_sev_deg
        AND TYPE = 1
        LEFT JOIN som_dept e
        ON a.B16C = e.`CODE`
        AND a.HOSPITAL_ID = e.HOSPITAL_ID
        <choose>
            <when test="feeStas==1">
                INNER JOIN
                (
                SELECT
                a.rid_idt_codg,
                a.medcas_codg,
                a.adm_time,
                a.dscg_time,
                a.is_in_group,
                a.dip_codg,
                a.DIP_NAME,
                a.asst_list_age_grp,
                a.asst_list_tmor_sev_deg,
                a.asst_list_dise_sev_deg,
                b.MED_TYPE,
                b.sumfee,
                b.setl_sco,
                b.dfr_fee
                FROM som_dip_grp_fbck a
                LEFT JOIN som_fund_dfr_fbck b
                ON a.rid_idt_codg = b.rid_idt_codg
                AND a.HOSPITAL_ID = b.HOSPITAL_ID
                WHERE 1=1
                <if test="begnDate != null and begnDate != '' and expiDate != null and expiDate != ''">
                    AND a.dscg_time BETWEEN #{begnDate,jdbcType=VARCHAR} AND concat(#{expiDate,jdbcType=VARCHAR},'
                    23:59:59')
                </if>
                <if test="inStartTime != null and inStartTime != '' and inEndTime != null and inEndTime != ''">
                    AND a.adm_time BETWEEN #{inStartTime} AND CONCAT(#{inEndTime},' 23:59:59')
                </if>
                <if test="seStartTime!=null and seStartTime!='' and seEndTime!=null and seEndTime!=''">
                    AND a.setl_time BETWEEN #{seStartTime} and CONCAT(#{seEndTime},' 23:59:59')
                </if>
                <include refid="com.my.som.common.mapper.CommonMapper.fbAuth"/>
                ) z
                ON b.PATIENT_ID = z.medcas_codg
                AND SUBSTR(b.adm_time,1,10) = z.adm_time
                AND SUBSTR(b.dscg_time,1,10) = z.dscg_time
            </when>
            <when test="feeStas==0">
                LEFT JOIN som_dip_sco f
                ON a.id = f.SETTLE_LIST_ID
            </when>
        </choose>
        LEFT JOIN (
        SELECT
        hi_setl_invy_id,
        MAX( CASE WHEN med_chrg_itemname = '床位费' THEN amt ELSE 0 END ) AS fourteencwf,
        MAX( CASE WHEN med_chrg_itemname = '诊查费' THEN amt ELSE 0 END ) AS fourteenzcf,
        MAX( CASE WHEN med_chrg_itemname = '检查费' THEN amt ELSE 0 END ) AS fourteenjcf,
        MAX( CASE WHEN med_chrg_itemname = '化验费' THEN amt ELSE 0 END ) AS fourteenhyf,
        MAX( CASE WHEN med_chrg_itemname = '治疗费' THEN amt ELSE 0 END ) AS fourteenzlf,
        MAX( CASE WHEN med_chrg_itemname = '手术费' THEN amt ELSE 0 END ) AS fourteenssf,
        MAX( CASE WHEN med_chrg_itemname = '护理费' THEN amt ELSE 0 END ) AS fourteenhlf,
        MAX( CASE WHEN med_chrg_itemname = '卫生材料费' THEN amt ELSE 0 END ) AS fourteenwsclf,
        MAX( CASE WHEN med_chrg_itemname = '西药费' THEN amt ELSE 0 END ) AS fourteenxyf,
        MAX( CASE WHEN med_chrg_itemname = '中药饮片费' THEN amt ELSE 0 END ) AS fourteenzyypf,
        MAX( CASE WHEN med_chrg_itemname = '中成药费' THEN amt ELSE 0 END ) AS fourteenzcyf,
        MAX( CASE WHEN med_chrg_itemname = '一般诊疗费' THEN amt ELSE 0 END ) AS fourteenybzlf,
        MAX( CASE WHEN med_chrg_itemname = '挂号费' THEN amt ELSE 0 END ) AS fourteenghf,
        MAX( CASE WHEN med_chrg_itemname = '其他费' THEN amt ELSE 0 END ) AS fourteenqtf
        FROM
        som_hi_setl_invy_med_fee_info
        WHERE
        hi_setl_invy_id IN ( SELECT ID AS hi_setl_invy_id FROM som_hi_invy_bas_info WHERE 1=1
        <if test="begnDate != null and begnDate != '' and expiDate != null and expiDate != ''">
            and B15 between #{begnDate,jdbcType=VARCHAR} and concat(#{expiDate,jdbcType=VARCHAR},' 23:59:59')
        </if>
        <if test="inStartTime != null and inStartTime != '' and inEndTime != null and inEndTime != ''">
            AND B12 BETWEEN #{inStartTime} AND CONCAT(#{inEndTime},' 23:59:59')
        </if>
        <if test="seStartTime != null and seStartTime != '' and seEndTime != null and seEndTime != ''">
            AND D37 BETWEEN #{seStartTime} AND CONCAT(#{seEndTime},' 23:59:59')
        </if>
        )
        GROUP BY
        hi_setl_invy_id
        ) i
        ON a.ID = i.hi_setl_invy_id
        WHERE a.ACTIVE_FLAG = '1'
        <include refid="queryCommon"/>
        <if test="dipCodg != null and dipCodg != ''">
            AND b.dip_codg = #{dipCodg,jdbcType=VARCHAR}
        </if>
        <if test="begnDate != null and begnDate != '' and expiDate != null and expiDate != ''">
            and a.B15 between #{begnDate,jdbcType=VARCHAR} and concat(#{expiDate,jdbcType=VARCHAR},' 23:59:59')
        </if>
        <if test="inStartTime != null and inStartTime != '' and inEndTime != null and inEndTime != ''">
            AND a.B12 BETWEEN #{inStartTime} AND CONCAT(#{inEndTime},' 23:59:59')
        </if>
        <if test="seStartTime != null and seStartTime != '' and seEndTime != null and seEndTime != ''">
            AND a.D37 BETWEEN #{seStartTime} AND CONCAT(#{seEndTime},' 23:59:59')
        </if>
        ) g

        ) i
        ) k
        where
        <![CDATA[
       diff ${gtOrLtSymbol} 0
    ]]>
        ORDER BY diff
    </select>
    <!-- 查询下拉 -->
    <select id="queryDropdown" resultType="com.my.som.vo.newBusiness.NewBusinessCommonVo">
        SELECT
        <choose>
            <when test='dropdownType == "1"'>
                TRIM(a.B16C) AS value,
                b.`name` AS label
            </when>
            <when test='dropdownType == "2"'>
                a.B25C AS value,
                a.B25N AS label
            </when>
            <when test='dropdownType == "3"'>
                distinct
                c.dip_codg AS value,
                c.DIP_NAME AS label
            </when>
        </choose>
        FROM som_hi_invy_bas_info a
        <if test="enableSeAns != null and enableSeAns != '' and enableSeAns == 1">
            INNER JOIN som_setl_cas_crsp p
            ON a.K00 = p.K00
        </if>
        LEFT JOIN som_dept b
        ON a.b16c = b.`code`
        AND a.HOSPITAL_ID = b.HOSPITAL_ID
        <if test='dropdownType == "3"'>
            LEFT JOIN som_dip_grp_info c
            ON a.ID = c.SETTLE_LIST_ID
        </if>
        where 1=1
        <if test="categories != null and categories != ''">
            <choose>
                <when test="categories == 1">
                    and a.A54 in ('1', '01', '310')
                </when>
                <when test="categories == 2">
                    and a.A54 in ('2', '02', '390')
                </when>
                <when test="categories == 9">
                    and a.A54 not in ('1', '01', '310', '2', '02', '390')
                </when>
            </choose>
        </if>
        <if test="begnDate != null and begnDate != '' and expiDate != null and expiDate != ''">
            AND a.b15 between #{begnDate,jdbcType=VARCHAR} and concat(#{expiDate,jdbcType=VARCHAR},' 23:59:59')
        </if>
        <if test="inStartTime != null and inStartTime != '' and inEndTime != null and inEndTime != ''">
            AND a.B12 between #{inStartTime,jdbcType=VARCHAR} and concat(#{inEndTime,jdbcType=VARCHAR},' 23:59:59')
        </if>
        <if test="seStartTime != null and seStartTime != '' and seEndTime != null and seEndTime != ''">
            AND a.D37 between #{seStartTime,jdbcType=VARCHAR} and concat(#{seEndTime,jdbcType=VARCHAR},' 23:59:59')
        </if>
        <if test='dropdownType == "2"'>
            and a.B25C IS NOT NULL
            and a.B25C !=''
        </if>
        <if test='dropdownType == "3"'>
            <if test="dipCodg !=null and dipCodg !=''">
                AND c.dip_codg = #{dipCodg,jdbcType=VARCHAR}
            </if>
        </if>
        <include refid="queryCommon"/>
        <if test='dropdownType == "3"'>
            AND c.grp_stas = 1
        </if>
        <choose>
            <when test='dropdownType == "1"'>
                GROUP BY a.B16C, b.`name`
            </when>
            <when test='dropdownType == "2"'>
                GROUP BY a.B25C,a.B25N
            </when>
        </choose>
    </select>

    <!-- 查询病案错误情况 -->
    <select id="queryMedError" resultType="com.my.som.vo.dipBusiness.DipInGroupAnalysisVo">
        select c.*,
        error1 + error2 + error3 + error4 + error5 + error6 + error7 AS errorSummaryNum
        from (
        SELECT
        <if test='queryType !="2" and dropdownType != 3'>
            a.dscg_caty_codg_inhosp AS priOutHosDeptCode,
            d.`NAME` AS priOutHosDeptName,
        </if>
        <if test='queryType =="2"'>
            a.ipdr_code AS drCodg,
            a.ipdr_name AS drName,
        </if>
        <if test="dropdownType == 3">
            a.dip_codg AS dipCodg,
            a.DIP_NAME AS dipName,
        </if>
        IFNULL( COUNT( CASE WHEN b.grp_fale_rea LIKE '%未找到级别%' THEN '1' ELSE NULL END ), 0 ) AS error1,
        IFNULL( COUNT( CASE WHEN b.grp_fale_rea LIKE '%未找到主要诊断节代码%' THEN '1' ELSE NULL END ), 0 ) AS error2,
        IFNULL( COUNT( CASE WHEN b.grp_fale_rea LIKE '%不在支付体系中%' THEN '1' ELSE NULL END ), 0 ) AS error3,
        IFNULL( COUNT( CASE WHEN b.grp_fale_rea LIKE '%诊断编码不是医保版编码%' THEN '1' ELSE NULL END ), 0 ) AS error4,
        IFNULL( COUNT( CASE WHEN b.grp_fale_rea LIKE '%手术及操作编码%' THEN '1' ELSE NULL END ), 0 ) AS error5,
        IFNULL( COUNT( CASE WHEN b.grp_fale_rea LIKE '%主要诊断编码为空%' THEN '1' ELSE NULL END ), 0 ) AS error6,
        IFNULL( COUNT( CASE WHEN b.grp_fale_rea LIKE '%不参与分组%' THEN '1' ELSE NULL END ), 0 ) AS error7,
        IFNULL(count(case when c.compeleteErrorNum != 0 then 1 else null end),0) as compeleteErrorNum,
        IFNULL(count(case when c.logicErrorNum != 0 then 1 else null end),0) as logicErrorNum
        FROM som_dip_grp_info a
        LEFT JOIN som_hi_invy_bas_info f
        ON a.SETTLE_LIST_ID = f.ID
        <if test="enableSeAns != null and enableSeAns != '' and enableSeAns == 1">
            INNER JOIN som_setl_cas_crsp p
            ON f.K00 = p.K00
        </if>
        LEFT JOIN som_dip_grp_rcd b
        ON a.SETTLE_LIST_ID = b.SETTLE_LIST_ID
        left join (
        select IFNULL(count(case when err_type in ('CE01','CE02','CE03','CE04') then 1 else null end),0) as
        compeleteErrorNum,
        IFNULL(count(case when err_type in ('LE01','LE02','LE03','LE04','LE05','LE06','LE07') then 1 else null end),0)
        as logicErrorNum,
        SETTLE_LIST_ID
        from
        som_setl_invy_chk_err_rcd a
        group by SETTLE_LIST_ID
        ) c
        on a.SETTLE_LIST_ID = c.SETTLE_LIST_ID
        left join som_dept d
        on a.dscg_caty_codg_inhosp = d.`CODE`
        AND a.HOSPITAL_ID = d.HOSPITAL_ID
        WHERE
        <if test='queryType !="2" and dropdownType != 3'>
            a.dscg_caty_codg_inhosp IS NOT NULL
            AND a.dscg_caty_codg_inhosp != ''
        </if>
        <if test='queryType =="2"'>
            a.ipdr_code IS NOT NULL
            AND a.ipdr_code != ''
        </if>
        <if test="dropdownType == 3">
            a.dip_codg IS NOT NULL AND a.dip_codg != ''
        </if>
        <if test="categories != null and categories != ''">
            <choose>
                <when test="categories == 1">
                    and f.A54 in ('1', '01', '310')
                </when>
                <when test="categories == 2">
                    and f.A54 in ('2', '02', '390')
                </when>
                <when test="categories == 9">
                    and f.A54 not in ('1', '01', '310', '2', '02', '390')
                </when>
            </choose>
        </if>

        <if test="begnDate != null and begnDate != '' and expiDate != null and expiDate != ''">
            AND a.dscg_time between #{begnDate,jdbcType=VARCHAR} AND concat(#{expiDate,jdbcType=VARCHAR},' 23:59:59')
        </if>
        <if test="inStartTime != null and inStartTime != '' and inEndTime != null and inEndTime != ''">
            AND a.adm_time BETWEEN #{inStartTime} AND CONCAT(#{inEndTime},' 23:59:59')
        </if>
        <if test="seStartTime != null and seStartTime != '' and seEndTime != null and seEndTime != ''">
            AND a.setl_end_time BETWEEN #{seStartTime} AND CONCAT(#{seEndTime},' 23:59:59')
        </if>
        <if test='setlway != null and setlway != "" and setlway == "1"'>
            AND f.setlway = #{setlway,jdbcType=VARCHAR}
        </if>
        <!-- 通用查询 -->
        <include refid="queryBusCommon"/>
        <if test="dipCodg != null and dipCodg != ''">
            AND a.dip_codg = #{dipCodg}
        </if>
        group by
        <if test='queryType !="2" and dropdownType != 3'>
            a.dscg_caty_codg_inhosp ,
            d.`NAME`
        </if>
        <if test='queryType =="2"'>
            a.ipdr_code,
            a.ipdr_name
        </if>
        <if test="dropdownType == 3">
            a.dip_codg,
            a.DIP_NAME
        </if>
        ) c
    </select>

    <!-- 查询汇总页面表格数据 -->
    <select id="queryAnalysisSummary" resultType="com.my.som.vo.newBusiness.NewBusinessAnalysisVo">
        select
        <if test='queryType !="2" and dropdownType != 3'>
            deptCode,
            deptName,
        </if>
        <if test='queryType =="2"'>
            drCodg,
            drName,
        </if>
        <if test="dropdownType == 3">
            dipCodg,
            dipName,
        </if>
        count(1) AS lossOrProfitNum
        from (
        SELECT
        k.*
        FROM
        (
        SELECT
        <choose>
            <!-- 病组 -->
            <when test='analysisType == "1"'>
                dipCodg,
                dipName,
                asstListAgeGrp,
                asstListTmorSevDeg,
                asstListDiseSevDeg,
            </when>
            <!-- 医生 -->
            <when test='analysisType == "2"'>
                drCodg,
                drName,
            </when>
            <!-- 患者 -->
            <when test='analysisType == "3"'>
                id,
            </when>
            <when test="analysisType == '4'">
                icdCodg,
                icdName
            </when>
        </choose>
        <if test='queryType !="2" and dropdownType != 3'>
            deptCode,
            deptName,
        </if>
        <if test='queryType =="2" and analysisType != "2"'>
            drCodg,
            drName,
        </if>
        <if test="dropdownType == 3 and analysisType != 1">
            dipCodg,
            dipName,
        </if>
        IFNULL( ROUND( sum( preCost ) - sum( inHosTotalCost ), 2 ), 0 ) AS diff
        FROM
        (
        SELECT
        g.*
        <if test="feeStas==0">
            , IFNULL(ROUND( g.ycCost,2),0
            ) AS preCost
        </if>
        FROM
        (
        SELECT a.a54 AS insuredType,
        a.B25N AS drName,
        a.B25C AS drCodg,
        a.A48 AS patientId,
        <choose>
            <when test="feeStas==0">
                a.D01 AS inHosTotalCost,
                b.dip_codg AS dipCodg,
                b.DIP_NAME AS dipName,
                b.asst_list_age_grp AS asstListAgeGrp,
                b.asst_list_tmor_sev_deg AS asstListTmorSevDeg,
                b.asst_list_dise_sev_deg AS asstListDiseSevDeg,
                f.totl_sco AS refer_sco,
                f.dise_type AS highLowType,
                f.forecast_fee AS ycCost,
                f.price,
                f.profitloss AS profitloss,
            </when>
            <when test="feeStas==1">
                z.sumfee AS inHosTotalCost,
                z.dip_codg AS dipCodg, <!-- DIP编码 -->
                z.DIP_NAME AS dipName, <!-- DIP名称 -->
                z.asst_list_age_grp as asstListAgeGrp,
                z.asst_list_tmor_sev_deg AS asstListTmorSevDeg,
                z.asst_list_dise_sev_deg AS asstListDiseSevDeg,
                z.setl_sco AS refer_sco,
                z.MED_TYPE AS highLowType,
                z.dfr_fee AS preCost,
            </when>
        </choose>
        a.B16C AS deptCode,
        a.id AS id,
        b.main_diag_dise_codg AS icdCodg,
        b.main_diag_dise_name AS icdName,
        e.`NAME` AS deptName
        FROM som_dip_grp_info b
        INNER JOIN som_hi_invy_bas_info a
        ON a.ID = b.SETTLE_LIST_ID
        <if test="enableSeAns != null and enableSeAns != '' and enableSeAns == 1">
            INNER JOIN som_setl_cas_crsp p
            ON a.K00 = p.K00
        </if>
        LEFT JOIN som_dip_standard c ON b.dip_codg = c.dip_codg
        AND SUBSTR( b.dscg_time, 1, 4 ) = c.STANDARD_YEAR
        AND b.is_used_asst_list = c.is_used_asst_list
        AND b.asst_list_age_grp = c.asst_list_age_grp
        AND b.asst_list_dise_sev_deg = c.asst_list_dise_sev_deg
        AND b.asst_list_tmor_sev_deg = c.asst_list_tmor_sev_deg
        AND b.HOSPITAL_ID = c.HOSPITAL_ID
        AND c.ACTIVE_FLAG = 1
        LEFT JOIN som_dept e ON a.B16C = e.`CODE`
        AND a.HOSPITAL_ID = e.HOSPITAL_ID
        <choose>
            <when test="feeStas==1">
                INNER JOIN
                (
                SELECT
                a.rid_idt_codg,
                a.medcas_codg,
                a.adm_time,
                a.dscg_time,
                a.is_in_group,
                a.dip_codg,
                a.DIP_NAME,
                a.asst_list_age_grp,
                a.asst_list_tmor_sev_deg,
                a.asst_list_dise_sev_deg,
                b.MED_TYPE,
                b.sumfee,
                b.setl_sco,
                b.dfr_fee,
                FROM som_dip_grp_fbck a
                LEFT JOIN som_fund_dfr_fbck b
                ON a.rid_idt_codg = b.rid_idt_codg
                AND a.HOSPITAL_ID = b.HOSPITAL_ID
                WHERE a.dscg_time between #{begnDate,jdbcType=VARCHAR} and concat(#{expiDate,jdbcType=VARCHAR},'
                23:59:59')
                <include refid="com.my.som.common.mapper.CommonMapper.fbAuth"/>
                ) z
                ON b.PATIENT_ID = z.medcas_codg
                AND SUBSTR(b.adm_time,1,10) = z.adm_time
                AND SUBSTR(b.dscg_time,1,10) = z.dscg_time
            </when>
            <when test="feeStas==0">
                LEFT JOIN som_dip_sco f
                ON a.id = f.SETTLE_LIST_ID
            </when>
        </choose>
        WHERE a.ACTIVE_FLAG = '1'
        <if test="begnDate != null and begnDate != '' and expiDate != null and expiDate != ''">
            AND a.b15 between #{begnDate,jdbcType=VARCHAR} AND concat(#{expiDate,jdbcType=VARCHAR},' 23:59:59')
        </if>
        <if test="inStartTime != null and inStartTime != '' and inEndTime != null and inEndTime != ''">
            AND a.B12 BETWEEN #{inStartTime} AND CONCAT(#{inEndTime},' 23:59:59')
        </if>
        <if test="seStartTime != null and seStartTime != '' and seEndTime != null and seEndTime != ''">
            AND a.d37 between #{seStartTime,jdbcType=VARCHAR} AND concat(#{seEndTime,jdbcType=VARCHAR},' 23:59:59')
        </if>
        <!-- 传入条件-科室 -->
        <if test="deptCode !=null and deptCode !=''">
            AND b.dscg_caty_codg_inhosp = #{deptCode,jdbcType=VARCHAR}
        </if>
        <if test="hospitalId !=null and hospitalId !=''">
            AND b.HOSPITAL_ID = #{hospitalId,jdbcType=VARCHAR}
        </if>
        <include refid="queryCommon"/>
        ) g

        ) i
        GROUP BY
        <choose>
            <!-- 病组 -->
            <when test='analysisType == "1"'>
                dipCodg,
                dipName,
                asstListAgeGrp,
                asstListTmorSevDeg,
                asstListDiseSevDeg,
            </when>

            <!-- 医生 -->
            <when test='analysisType == "2"'>
                drCodg,
                drName,
            </when>

            <!-- 患者 -->
            <when test='analysisType == "3"'>
                id,
            </when>
        </choose>
        <if test='queryType !="2" and dropdownType != 3'>
            deptCode,
            deptName
        </if>
        <if test='queryType =="2"'>
            drCodg,
            drName
        </if>
        <if test="dropdownType == 3">
            dipCodg,
            dipName
        </if>
        ) k
        <![CDATA[
                    WHERE diff ${gtOrLtSymbol} 0
                ]]>

        ) m
        group by
        <if test='queryType !="2" and dropdownType != 3'>
            deptCode,
            deptName
        </if>
        <if test='queryType =="2"'>
            drCodg,
            drName
        </if>
        <if test="dropdownType == 3">
            dipCodg,dipName
        </if>
    </select>

    <!-- 查询医生下拉 -->
    <select id="queryDoctorDropDown" resultType="com.my.som.vo.newBusiness.NewBusinessCommonVo">
        SELECT ipdr_code AS value,
        ipdr_name AS label
        FROM som_dip_grp_info
        WHERE ipdr_code IS NOT NULL
        AND ipdr_code != ''
        AND ipdr_code != '00'
        <if test="hospitalId !=null and hospitalId !=''">
            AND HOSPITAL_ID = #{hospitalId,jdbcType=VARCHAR}
        </if>
        GROUP BY ipdr_code,ipdr_name
        ORDER BY ipdr_code
    </select>

    <update id="updateSwitchState">
        UPDATE som_back_user
        SET fee_stas = #{feeStas}
        WHERE username = #{username}
    </update>


    <select id="queryDrgDiseaseLoss" resultType="com.my.som.vo.newBusiness.NewBusinessAnalysisVo">
        select k.*,
        ROUND(highPreTotalCost - highTotalCost,2) AS highDiff,
        ROUND(lowPreTotalCost - lowTotalCost,2) AS lowDiff,
        ROUND(normalPreTotalCost - normalTotalCost,2) AS normalDiff,
        ROUND(normalEarnPreTotalCost - normalEarnTotalCost,2) AS normalEarnDiff
        from(
        select drgCodg,
        drgName, count(*) as patCount,
        IFNULL(ROUND(sum(preCost),2),0) as preTotalCost,
        IFNULL(ROUND(avg(inHosTotalCost),2),0) AS avgCost,
        IFNULL(ROUND(avg(bgzyfyjb),2),0) AS avgCostBenchmark,
        IFNULL(ROUND(sum(inHosTotalCost),2),0) AS sumfee,
        IFNULL(ROUND(sum(preCost) - sum(inHosTotalCost),2),0) AS diff,
        <![CDATA[
                   IFNULL(ROUND(COUNT(CASE WHEN highLowType = '1' and (preCost - inHosTotalCost) < 0 then inHosTotalCost ELSE null end),2),0) AS highNum,
                   GROUP_CONCAT(CASE WHEN highLowType = '1' AND ( preCost - inHosTotalCost ) < 0 THEN patientId ELSE NULL END) as highPatientId,
                   IFNULL(ROUND(SUM(CASE WHEN highLowType = '1' and (preCost - inHosTotalCost) < 0 then inHosTotalCost ELSE null end),2),0) AS highTotalCost,
                   IFNULL(ROUND(SUM(CASE WHEN highLowType = '1' and (preCost - inHosTotalCost) < 0 then preCost ELSE null end),2),0) AS highPreTotalCost,
                   IFNULL(ROUND(COUNT(CASE WHEN highLowType = '2' and (preCost - inHosTotalCost) < 0 then inHosTotalCost ELSE null end),2),0) AS lowNum,
                   GROUP_CONCAT(CASE WHEN highLowType = '2' AND ( preCost - inHosTotalCost ) < 0 THEN patientId ELSE NULL END) as lowPatientId,
                   IFNULL(ROUND(SUM(CASE WHEN highLowType = '2' and (preCost - inHosTotalCost) < 0 then inHosTotalCost ELSE null end),2),0) AS lowTotalCost,
                   IFNULL(ROUND(SUM(CASE WHEN highLowType = '2' and (preCost - inHosTotalCost) < 0 then preCost ELSE null end),2),0) AS lowPreTotalCost,
                   IFNULL(ROUND(COUNT(CASE WHEN highLowType = '3' and (preCost - inHosTotalCost) < 0 then inHosTotalCost ELSE null end),2),0) AS normalNum,
                   GROUP_CONCAT(CASE WHEN highLowType = '3' and (preCost - inHosTotalCost) < 0 THEN patientId ELSE NULL END) as normalPatientId,
                   IFNULL(ROUND(SUM(CASE WHEN highLowType = '3' and (preCost - inHosTotalCost) < 0 then inHosTotalCost ELSE null end),2),0) AS normalTotalCost,
                   IFNULL(ROUND(SUM(CASE WHEN highLowType = '3' and (preCost - inHosTotalCost) < 0 then preCost ELSE null end),2),0) AS normalPreTotalCost,
                   IFNULL(ROUND(COUNT(CASE WHEN highLowType = '3' and (preCost - inHosTotalCost) > 0 then inHosTotalCost ELSE null end),2),0) AS normalEarnNum,
                   GROUP_CONCAT(CASE WHEN highLowType = '3' and (preCost - inHosTotalCost) > 0 then patientId ELSE NULL END) as normalEarnPatientId,
                   IFNULL(ROUND(SUM(CASE WHEN highLowType = '3' and (preCost - inHosTotalCost) > 0 then inHosTotalCost ELSE null end),2),0) AS normalEarnTotalCost,
                   IFNULL(ROUND(SUM(CASE WHEN highLowType = '3' and (preCost - inHosTotalCost) > 0 then preCost ELSE null end),2),0) AS normalEarnPreTotalCost
                   ]]>
        from (
        select g.*
        <if test="feeStas==0">
            ,
            IFNULL(ROUND(g.refer_sco * h.price,2),0) AS preCost
        </if>
        from (

        SELECT case when a.A12C=1 then '男' else '女' end as gend,<!-- 性别 -->
        a.a54 AS insuredType,
        a.A14 as age,<!-- 年龄 -->
        <choose>
            <when test="feeStas==0">
                D01 AS inHosTotalCost, <!-- 住院总费用 -->
                IFNULL(ROUND((IFNULL(D01,0) - IFNULL(CONVERT(AES_DECRYPT(UNHEX(c.standard_avg_fee
                ),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) USING utf8),0)),2),0) AS
                zyfycy,<!-- 住院费用与标杆差异 -->
                ROUND(((IFNULL(b.drugfee/a.D01,0))*100 - IFNULL(d.drug_ratio,0)*100),2) AS yzbcy,
                ROUND(((IFNULL(b.mcs_fee/(a.D01),0))*100 - IFNULL(d.mcs_fee_rat,0)*100),2) AS hzbcy,
                b.drg_codg AS drgCodg, <!-- DIP编码 -->
                b.DRG_NAME AS drgName, <!-- DIP名称 -->
                ROUND((IFNULL(b.drugfee/a.D01,0))*100,2) AS yzb, <!-- 药占比 -->
                ROUND((IFNULL(b.mcs_fee/(a.D01),0))*100,2) AS hzb, <!-- 耗占比 -->
                f.totl_sco AS refer_sco,
                f.dise_type AS highLowType,
            </when>
            <when test="feeStas==1">
                z.sumfee AS inHosTotalCost,<!-- 住院总费用 -->
                IFNULL(ROUND((IFNULL(z.sumfee,0) - IFNULL(CONVERT(AES_DECRYPT(UNHEX(z.dfr_fee
                ),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) USING utf8),0)),2),0) AS
                zyfycy,<!-- 住院费用与医保反馈差异 -->
                ROUND(((IFNULL(b.drugfee/z.sumfee,0))*100 - IFNULL(d.drug_ratio,0)*100),2) AS yzbcy,
                ROUND(((IFNULL(b.mcs_fee/(z.sumfee),0))*100 - IFNULL(d.mcs_fee_rat,0)*100),2) AS hzbcy,
                z.drg_codg AS drgCodg, <!-- DIP编码 -->
                z.DRG_NAME AS drgName, <!-- DIP名称 -->
                ROUND((IFNULL(b.drugfee/z.sumfee,0))*100,2) AS yzb, <!-- 药占比 -->
                ROUND((IFNULL(b.mcs_fee/(z.sumfee),0))*100,2) AS hzb, <!-- 耗占比 -->
                z.setl_sco AS refer_sco,
                z.MED_TYPE AS highLowType,
                z.dfr_fee AS preCost,
            </when>
        </choose>
        ROUND((IFNULL(B20,0) - IFNULL(CONVERT(AES_DECRYPT(UNHEX(c.standard_ave_hosp_day
        ),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) USING utf8),0)),2) as zytscy,<!-- 住院天数与标杆差异 -->
        SUBSTRING(a.B15,1,10) AS outHosTime, <!-- 出院时间 -->
        SUBSTRING(a.B12,1,10) AS inHosTime, <!-- 入院时间 -->
        a.B16C AS deptCode, <!-- 出院科室编码 -->
        e.`NAME` AS deptName, <!-- 出院科室编码 -->
        a.A11 AS name, <!-- 患者姓名 -->
        a.ID AS id, <!-- settle_list_id-->
        a.A48 AS patientId, <!-- 病案号 -->
        TRIM(a.B25C) AS residentCode, <!-- 住院医师编码 -->
        TRIM(a.B25N) AS residentName, <!-- 住院医师姓名 -->
        IFNULL(B20,0) AS inHosDays, <!-- 住院天数 -->
        IFNULL(b.drugfee,0) AS ypf, <!-- 药品费 -->
        IFNULL(b.mcs_fee,0) AS clf, <!-- 材料费 -->
        IFNULL(CONVERT(AES_DECRYPT(UNHEX(c.standard_avg_fee ),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY})
        USING utf8),0) AS bgzyfyjb, <!-- 标杆住院费用（级别） -->
        CASE WHEN A54 IN ('1','01','310') THEN c.adjm_cof  else c.adjm_resid_cof end as adjm_cof,
        IFNULL(CONVERT(AES_DECRYPT(UNHEX(c.standard_ave_hosp_day ),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY})
        USING utf8),0) AS bgzytsjb, <!-- 标杆住院天数（级别） -->
        ROUND(IFNULL(d.drug_ratio,0)*100,2) AS bgyzb, <!-- 标杆药占比 -->
        ROUND(IFNULL(d.mcs_fee_rat,0)*100,2) AS bghzb, <!-- 标杆耗占比 -->
        IFNULL(d.avg_fee,0) AS ljfy,<!-- 例均费用 -->
        IFNULL(CONVERT(AES_DECRYPT(UNHEX(c.standard_avg_fee ),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY})
        USING utf8),0) / d.avg_fee AS avgCostRate
        FROM som_drg_grp_info b
        INNER JOIN som_hi_invy_bas_info a
        ON a.ID = b.SETTLE_LIST_ID
        <if test="enableSeAns != null and enableSeAns != '' and enableSeAns == 1">
            INNER JOIN som_setl_cas_crsp p
            ON a.K00 = p.K00
        </if>
        LEFT JOIN som_drg_standard c
        ON b.drg_codg = c.drg_codg
        AND SUBSTR(b.dscg_time,1,4) = c.STANDARD_YEAR
        AND b.HOSPITAL_ID = c.HOSPITAL_ID
        AND c.ACTIVE_FLAG = 1
        AND a.insuplc_admdvs = c.insuplc_admdvs
        LEFT JOIN som_std_fee d
        ON b.drg_codg = d.`CODE`
        AND SUBSTR(b.dscg_time,1,4) = d.STANDARD_YEAR
        AND TYPE = 3
        LEFT JOIN som_dept e
        ON a.B16C = e.`CODE`
        AND a.HOSPITAL_ID = e.HOSPITAL_ID
        <choose>
            <when test="feeStas==1">
                INNER JOIN
                (
                SELECT
                a.rid_idt_codg,
                a.medcas_codg,
                a.adm_time,
                a.dscg_time,
                a.is_in_group,
                a.drg_codg,
                a.DRG_NAME,
                b.MED_TYPE,
                b.sumfee,
                b.setl_sco,
                b.dfr_fee
                FROM som_drg_grp_fbck a
                LEFT JOIN som_drg_pt_val_pay b
                ON a.rid_idt_codg = b.rid_idt_codg
                AND a.HOSPITAL_ID = b.HOSPITAL_ID
                WHERE a.dscg_time between #{begnDate,jdbcType=VARCHAR} and concat(#{expiDate,jdbcType=VARCHAR},'
                23:59:59')
                <include refid="com.my.som.common.mapper.CommonMapper.fbAuth"/>
                ) z
                ON b.PATIENT_ID = z.medcas_codg
                AND SUBSTR(b.adm_time,1,10) = z.adm_time
                AND SUBSTR(b.dscg_time,1,10) = z.dscg_time
            </when>
            <when test="feeStas==0">
                LEFT JOIN som_drg_sco f
                ON a.id = f.SETTLE_LIST_ID
            </when>
        </choose>
        WHERE a.ACTIVE_FLAG = '1'
        <include refid="queryCommon"/>
        <if test="drgCodg != null and drgCodg != ''">
            AND b.drg_codg = #{drgCodg,jdbcType=VARCHAR}
        </if>
        <if test="inStartTime != null and inStartTime != '' and inEndTime != null and inEndTime != ''">
            AND a.B12 BETWEEN #{inStartTime} AND CONCAT(#{inEndTime},' 23:59:59')
        </if>
        <if test="begnDate != null and begnDate !='' and expiDate != null and expiDate != ''">
            AND a.B15 BETWEEN #{begnDate,jdbcType=VARCHAR} AND concat(#{expiDate,jdbcType=VARCHAR},' 23:59:59')
        </if>
        <if test="seStartTime != null and seStartTime != '' and seEndTime != null and seEndTime != ''">
            AND a.d37 BETWEEN #{seStartTime,jdbcType=VARCHAR} AND concat(#{seEndTime,jdbcType=VARCHAR},' 23:59:59')
        </if>
        ) g
        <if test="feeStas==0">
            Left JOIN
            (
            SELECT
            SETTLE_LIST_ID,
            price
            FROM
            som_drg_sco
            )   h
            on h.SETTLE_LIST_ID = g.id
        </if>
        ) i
        group by drgCodg,drgName
        ) k
        where
        <![CDATA[
           diff ${gtOrLtSymbol} 0
        ]]>
        ORDER BY diff
    </select>

    <select id="queryDrgDoctorLoss" resultType="com.my.som.vo.newBusiness.NewBusinessAnalysisVo">
        select k.*,
        ROUND(highPreTotalCost - highTotalCost,2) AS highDiff,
        ROUND(lowPreTotalCost - lowTotalCost,2) AS lowDiff,
        ROUND(normalPreTotalCost - normalTotalCost,2) AS normalDiff,
        ROUND(normalEarnPreTotalCost - normalEarnTotalCost,2) AS normalEarnDiff
        from(
        select drCodg, drName,
        IFNULL(ROUND(sum(preCost),2),0) as preTotalCost,
        IFNULL(ROUND(avg(inHosTotalCost),2),0) AS avgCost,
        IFNULL(ROUND(sum(inHosTotalCost),2),0) AS sumfee,
        IFNULL(ROUND(sum(preCost) - sum(inHosTotalCost),2),0) AS diff,
        <![CDATA[
                   GROUP_CONCAT(CASE WHEN highLowType = '1' AND ( preCost - inHosTotalCost ) < 0 THEN patientId ELSE NULL END) as highPatientId,
                   GROUP_CONCAT(CASE WHEN highLowType = '2' AND ( preCost - inHosTotalCost ) < 0 THEN patientId ELSE NULL END) as lowPatientId,
                   GROUP_CONCAT(CASE WHEN highLowType = '3' and (preCost - inHosTotalCost) < 0 THEN patientId ELSE NULL END) as normalPatientId,
                   GROUP_CONCAT(CASE WHEN highLowType = '3' and (preCost - inHosTotalCost) > 0 then patientId ELSE NULL END) as normalEarnPatientId,
                   IFNULL(ROUND(COUNT(CASE WHEN highLowType = '1' and (preCost - inHosTotalCost) < 0 then inHosTotalCost ELSE null end),2),0) AS highNum,
                   IFNULL(ROUND(SUM(CASE WHEN highLowType = '1' and (preCost - inHosTotalCost) < 0 then inHosTotalCost ELSE null end),2),0) AS highTotalCost,
                   IFNULL(ROUND(SUM(CASE WHEN highLowType = '1' and (preCost - inHosTotalCost) < 0 then preCost ELSE null end),2),0) AS highPreTotalCost,
                   IFNULL(ROUND(COUNT(CASE WHEN highLowType = '2' and (preCost - inHosTotalCost) < 0 then inHosTotalCost ELSE null end),2),0) AS lowNum,
                   IFNULL(ROUND(SUM(CASE WHEN highLowType = '2' and (preCost - inHosTotalCost) < 0 then inHosTotalCost ELSE null end),2),0) AS lowTotalCost,
                   IFNULL(ROUND(SUM(CASE WHEN highLowType = '2' and (preCost - inHosTotalCost) < 0 then preCost ELSE null end),2),0) AS lowPreTotalCost,
                   IFNULL(ROUND(COUNT(CASE WHEN highLowType = '3' and (preCost - inHosTotalCost) < 0 then inHosTotalCost ELSE null end),2),0) AS normalNum,
                   IFNULL(ROUND(SUM(CASE WHEN highLowType = '3' and (preCost - inHosTotalCost) < 0 then inHosTotalCost ELSE null end),2),0) AS normalTotalCost,
                   IFNULL(ROUND(SUM(CASE WHEN highLowType = '3' and (preCost - inHosTotalCost) < 0 then preCost ELSE null end),2),0) AS normalPreTotalCost,
                   IFNULL(ROUND(COUNT(CASE WHEN highLowType = '3' and (preCost - inHosTotalCost) > 0 then inHosTotalCost ELSE null end),2),0) AS normalEarnNum,
                   IFNULL(ROUND(SUM(CASE WHEN highLowType = '3' and (preCost - inHosTotalCost) > 0 then inHosTotalCost ELSE null end),2),0) AS normalEarnTotalCost,
                   IFNULL(ROUND(SUM(CASE WHEN highLowType = '3' and (preCost - inHosTotalCost) > 0 then preCost ELSE null end),2),0) AS normalEarnPreTotalCost
                   ]]>
        from (
        select g.*
        <if test="feeStas==0">
            ,
            IFNULL(ROUND(g.refer_sco * h.price,2),0) AS preCost
        </if>
        from (

        SELECT case when a.A12C=1 then '男' else '女' end as gend,<!-- 性别 -->
        a.a54 AS insuredType,
        a.A14 as age,<!-- 年龄 -->
        <choose>
            <when test="feeStas==0">
                D01 AS inHosTotalCost, <!-- 住院总费用 -->
                IFNULL(ROUND((IFNULL(D01,0) - IFNULL(CONVERT(AES_DECRYPT(UNHEX(c.standard_avg_fee
                ),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) USING utf8),0)),2),0) AS
                zyfycy,<!-- 住院费用与标杆差异 -->
                ROUND(((IFNULL(b.drugfee/a.D01,0))*100 - IFNULL(d.drug_ratio,0)*100),2) AS yzbcy,
                ROUND(((IFNULL(b.mcs_fee/(a.D01),0))*100 - IFNULL(d.mcs_fee_rat,0)*100),2) AS hzbcy,
                b.drg_codg AS drgCodg, <!-- DIP编码 -->
                b.DRG_NAME AS drgName, <!-- DIP名称 -->
                ROUND((IFNULL(b.drugfee/a.D01,0))*100,2) AS yzb, <!-- 药占比 -->
                ROUND((IFNULL(b.mcs_fee/(a.D01),0))*100,2) AS hzb, <!-- 耗占比 -->
                f.totl_sco AS refer_sco,
                f.dise_type AS highLowType,
            </when>
            <when test="feeStas==1">
                z.sumfee AS inHosTotalCost,<!-- 住院总费用 -->
                IFNULL(ROUND((IFNULL(z.sumfee,0) - IFNULL(CONVERT(AES_DECRYPT(UNHEX(z.dfr_fee
                ),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) USING utf8),0)),2),0) AS
                zyfycy,<!-- 住院费用与医保反馈差异 -->
                ROUND(((IFNULL(b.drugfee/z.sumfee,0))*100 - IFNULL(d.drug_ratio,0)*100),2) AS yzbcy,
                ROUND(((IFNULL(b.mcs_fee/(z.sumfee),0))*100 - IFNULL(d.mcs_fee_rat,0)*100),2) AS hzbcy,
                z.drg_codg AS drgCodg, <!-- DIP编码 -->
                z.DRG_NAME AS drgName, <!-- DIP名称 -->
                ROUND((IFNULL(b.drugfee/z.sumfee,0))*100,2) AS yzb, <!-- 药占比 -->
                ROUND((IFNULL(b.mcs_fee/(z.sumfee),0))*100,2) AS hzb, <!-- 耗占比 -->
                z.setl_sco AS refer_sco,
                z.MED_TYPE AS highLowType,
                z.dfr_fee AS preCost,
            </when>
        </choose>
        a.B25N AS drName,
        a.B25C AS drCodg,
        ROUND((IFNULL(B20,0) - IFNULL(CONVERT(AES_DECRYPT(UNHEX(c.standard_ave_hosp_day
        ),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) USING utf8),0)),2) as zytscy,<!-- 住院天数与标杆差异 -->
        SUBSTRING(a.B15,1,10) AS outHosTime, <!-- 出院时间 -->
        SUBSTRING(a.B12,1,10) AS inHosTime, <!-- 入院时间 -->
        B16C AS deptCode, <!-- 出院科室编码 -->
        e.`NAME` AS deptName, <!-- 出院科室编码 -->
        a.A11 AS name, <!-- 患者姓名 -->
        a.ID AS id, <!-- settle_list_id-->
        a.A48 AS patientId, <!-- 病案号 -->
        TRIM(B25C) AS residentCode, <!-- 住院医师编码 -->
        TRIM(B25N) AS residentName, <!-- 住院医师姓名 -->
        IFNULL(B20,0) AS inHosDays, <!-- 住院天数 -->
        IFNULL(b.drugfee,0) AS ypf, <!-- 药品费 -->
        IFNULL(b.mcs_fee,0) AS clf, <!-- 材料费 -->
        IFNULL(CONVERT(AES_DECRYPT(UNHEX(c.standard_avg_fee ),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY})
        USING utf8),0) AS bgzyfyjb, <!-- 标杆住院费用（级别） -->
        IFNULL(CONVERT(AES_DECRYPT(UNHEX(c.standard_ave_hosp_day ),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY})
        USING utf8),0) AS bgzytsjb, <!-- 标杆住院天数（级别） -->
        ROUND(IFNULL(d.drug_ratio,0)*100,2) AS bgyzb, <!-- 标杆药占比 -->
        ROUND(IFNULL(d.mcs_fee_rat,0)*100,2) AS bghzb, <!-- 标杆耗占比 -->
        IFNULL(d.avg_fee,0) AS ljfy,<!-- 例均费用 -->
        IFNULL(CONVERT(AES_DECRYPT(UNHEX(c.standard_avg_fee ),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY})
        USING utf8),0) / d.avg_fee AS avgCostRate
        FROM som_hi_invy_bas_info a
        LEFT JOIN som_drg_grp_info b
        ON a.ID = b.SETTLE_LIST_ID
        <if test="enableSeAns != null and enableSeAns != '' and enableSeAns == 1">
            INNER JOIN som_setl_cas_crsp p
            ON a.K00 = p.K00
        </if>
        LEFT JOIN som_drg_standard c
        ON b.drg_codg = c.drg_codg
        AND SUBSTR(b.dscg_time,1,4) = c.STANDARD_YEAR
        AND b.HOSPITAL_ID = c.HOSPITAL_ID
        AND c.ACTIVE_FLAG = 1
        LEFT JOIN som_std_fee d
        ON b.drg_codg = d.`CODE`
        AND SUBSTR(b.dscg_time,1,4) = d.STANDARD_YEAR
        AND TYPE = 3
        LEFT JOIN som_dept e
        ON a.B16C = e.`CODE`
        AND a.HOSPITAL_ID = e.HOSPITAL_ID
        <choose>
            <when test="feeStas==1">
                INNER JOIN
                (
                SELECT
                a.rid_idt_codg,
                a.medcas_codg,
                a.adm_time,
                a.dscg_time,
                a.is_in_group,
                a.drg_codg,
                a.DRG_NAME,
                b.MED_TYPE,
                b.sumfee,
                b.setl_sco,
                b.dfr_fee
                FROM som_drg_grp_fbck a
                LEFT JOIN som_drg_pt_val_pay b
                ON a.rid_idt_codg = b.rid_idt_codg
                AND a.HOSPITAL_ID = b.HOSPITAL_ID
                WHERE 1=1
                <if test="inStartTime != null and inStartTime != '' and inEndTime != null and inEndTime != ''">
                    AND a.adm_time BETWEEN #{inStartTime} AND CONCAT(#{inEndTime},' 23:59:59')
                </if>
                <if test="begnDate != null and begnDate !='' and expiDate != null and expiDate != ''">
                    AND a.dscg_time BETWEEN #{begnDate,jdbcType=VARCHAR} AND concat(#{expiDate,jdbcType=VARCHAR},'
                    23:59:59')
                </if>
                <if test="seStartTime != null and seStartTime != '' and seEndTime != null and seEndTime != ''">
                    AND a.setl_time BETWEEN #{seStartTime,jdbcType=VARCHAR} AND concat(#{seEndTime,jdbcType=VARCHAR},'
                    23:59:59')
                </if>
                <include refid="com.my.som.common.mapper.CommonMapper.fbAuth"/>
                ) z
                ON b.PATIENT_ID = z.medcas_codg
                AND SUBSTR(b.adm_time,1,10) = z.adm_time
                AND SUBSTR(b.dscg_time,1,10) = z.dscg_time
            </when>
            <when test="feeStas==0">
                LEFT JOIN som_drg_sco f
                ON a.id = f.SETTLE_LIST_ID
            </when>
        </choose>
        WHERE a.ACTIVE_FLAG = '1'
        <include refid="queryCommon"/>
        <if test="drgCodg != null and drgCodg != ''">
            AND b.drg_codg = #{drgCodg,jdbcType=VARCHAR}
        </if>
        <if test="inStartTime != null and inStartTime != '' and inEndTime != null and inEndTime != ''">
            AND a.B12 BETWEEN #{inStartTime} AND CONCAT(#{inEndTime},' 23:59:59')
        </if>
        <if test="begnDate != null and begnDate !='' and expiDate != null and expiDate != ''">
            AND a.B15 BETWEEN #{begnDate,jdbcType=VARCHAR} AND concat(#{expiDate,jdbcType=VARCHAR},' 23:59:59')
        </if>
        <if test="seStartTime != null and seStartTime != '' and seEndTime != null and seEndTime != ''">
            AND a.d37 BETWEEN #{seStartTime,jdbcType=VARCHAR} AND concat(#{seEndTime,jdbcType=VARCHAR},' 23:59:59')
        </if>
        ) g
        <if test="feeStas==0">
            Left JOIN
            (
            SELECT
            SETTLE_LIST_ID,
            price
            FROM
            som_drg_sco
            )   h
            on h.SETTLE_LIST_ID = g.id
        </if>
        ) i
        group by drCodg, drName
        ) k
        where
        <![CDATA[
           diff ${gtOrLtSymbol} 0
        ]]>
        ORDER BY diff
    </select>

    <select id="queryDrgPatientLoss" resultType="com.my.som.vo.newBusiness.NewBusinessAnalysisVo">
        select k.*
        from(
        select IFNULL(ROUND(preCost,2),0) as preTotalCost,
        IFNULL(ROUND(inHosTotalCost,2),0) AS avgCost,
        IFNULL(ROUND(inHosTotalCost,2),0) AS sumfee,
        IFNULL(ROUND(preCost - inHosTotalCost,2),0) AS diff,
        name,
        patientId,
        drgCodg,
        drgName,
        icdCodg,
        icdName,
        ROUND(com_med_servfee,2) AS com_med_servfee,
        ROUND(ordn_trt_oprt_fee,2) AS ordn_trt_oprt_fee,
        ROUND(ordn_med_servfee,2) AS ordn_med_servfee,
        ROUND(nursfee,2) AS nursfee,
        ROUND(zhylfwqtf,2) AS zhylfwqtf,

        ROUND(diag_fee,2) AS diag_fee,
        ROUND(cas_diag_fee,2) AS cas_diag_fee,
        ROUND(lab_diag_fee,2) AS lab_diag_fee,
        ROUND(rdhy_diag_fee,2) AS rdhy_diag_fee,
        ROUND(clnc_diag_item_fee,2) AS clnc_diag_item_fee,

        ROUND(treat_fee,2) AS treat_fee,
        ROUND(nsrgtrt_item_fee,2) AS nsrgtrt_item_fee,
        ROUND(oprn_treat_fee,2) AS oprn_treat_fee,

        ROUND(rhab_fee,2) AS rhab_fee,

        ROUND(tcmdrug_fee,2) AS tcmdrug_fee,

        ROUND(west_fee,2) AS west_fee,

        ROUND(zyf1,2) AS zyf1,
        ROUND(tcmpat_fee,2) AS tcmpat_fee,
        ROUND(tcmherb,2) AS tcmherb,

        ROUND(blood_blo_pro_fee,2) AS blood_blo_pro_fee,
        ROUND(blo_fee,2) AS blo_fee,
        ROUND(qdblzpf,2) AS qdblzpf,
        ROUND(bdblzpf,2) AS bdblzpf,
        ROUND(nxyzlzpf,2) AS nxyzlzpf,
        ROUND(xbyzlzpf,2) AS xbyzlzpf,

        ROUND(mcs_fee,2) AS mcs_fee,
        ROUND(jcyycxyyclf,2) AS jcyycxyyclf,
        ROUND(trt_use_dspo_med_matlfee,2) AS trt_use_dspo_med_matlfee,
        ROUND(oprn_use_dspo_med_matlfee,2) AS oprn_use_dspo_med_matlfee,

        ROUND(oth_fee,2) AS avgQtf,


        <!-- 标杆 -->
        ROUND(bgzhylfwf * avgCostRate,2) AS bgzhylfwf,
        ROUND(bgybylfwf * avgCostRate,2) AS bgybylfwf,
        ROUND(bghlf * avgCostRate,2) AS bghlf,
        ROUND(bgzhylfwqtf * avgCostRate,2) AS bgzhylfwqtf,

        ROUND(bgzdf * avgCostRate,2) AS bgzdf,
        ROUND(bgblzdf * avgCostRate,2) AS bgblzdf,
        ROUND(bgsyszdf * avgCostRate,2) AS bgsyszdf,
        ROUND(bgyxxzdf * avgCostRate,2) AS bgyxxzdf,
        ROUND(bglczdxmf * avgCostRate,2) AS bglczdxmf,

        ROUND(bgzlf * avgCostRate,2) AS bgzlf,
        ROUND(bgfsszlxmf * avgCostRate,2) AS bgfsszlxmf,
        ROUND(bgsszlf * avgCostRate,2) AS bgsszlf,

        ROUND(bgkff * avgCostRate,2) AS bgkff,

        ROUND(bgzyf * avgCostRate,2) AS bgzyf,

        ROUND(bgxyf * avgCostRate,2) AS bgxyf,

        ROUND(bgzyf1 * avgCostRate,2) AS bgzyf1,
        ROUND(bgzcyf * avgCostRate,2) AS bgzcyf,
        ROUND(bgzcyf1 * avgCostRate,2) AS bgzcyf1,

        ROUND(bgxyhxyzpf * avgCostRate,2) AS bgxyhxyzpf,
        ROUND(bgxf * avgCostRate,2) AS bgxf,
        ROUND(bgbdblzpf * avgCostRate,2) AS bgbdblzpf,
        ROUND(bgqdblzpf * avgCostRate,2) AS bgqdblzpf,
        ROUND(bgnxyzlzpf * avgCostRate,2) AS bgnxyzlzpf,
        ROUND(bgxbyzlzpf * avgCostRate,2) AS bgxbyzlzpf,

        ROUND(bghcf * avgCostRate,2) AS bghcf,
        ROUND(bgjcyycxyyclf * avgCostRate,2) AS bgjcyycxyyclf,
        ROUND(bgzlyycxyyclf * avgCostRate,2) AS bgzlyycxyyclf,
        ROUND(bgssyycxyyclf * avgCostRate,2) AS bgssyycxyyclf,

        ROUND(bgqtf,2) AS bgqtf,
        <!-- 十四项_标杆 -->
        bgfourteencwf,<!-- 十四项_床位费 -->
        bgfourteenzcf,<!-- 十四项_诊查费 -->
        bgfourteenjcf,<!-- 十四项_检查费 -->
        bgfourteenhyf,<!-- 十四项_化验费 -->
        bgfourteenzlf,<!-- 十四项_治疗费 -->
        bgfourteenssf,<!-- 十四项_手术费 -->
        bgfourteenhlf,<!-- 十四项_护理费 -->
        bgfourteenwsclf,<!-- 十四项_卫生材料费 -->
        bgfourteenxyf,<!-- 十四项_西药费 -->
        bgfourteenzyypf,<!-- 十四项_中药饮片费 -->
        bgfourteenzcyf,<!-- 十四项_中成药费 -->
        bgfourteenybzlf,<!-- 十四项_一般诊疗费 -->
        bgfourteenghf,<!-- 十四项_挂号费 -->
        bgfourteenqtf,<!-- 十四项_其他费 -->
        <!-- 十四项_患者 -->
        fourteencwf,<!-- 十四项_床位费 -->
        fourteenzcf,<!-- 十四项_诊查费 -->
        fourteenjcf,<!-- 十四项_检查费 -->
        fourteenhyf,<!-- 十四项_化验费 -->
        fourteenzlf,<!-- 十四项_治疗费 -->
        fourteenssf,<!-- 十四项_手术费 -->
        fourteenhlf,<!-- 十四项_护理费 -->
        fourteenwsclf,<!-- 十四项_卫生材料费 -->
        fourteenxyf,<!-- 十四项_西药费 -->
        fourteenzyypf,<!-- 十四项_中药饮片费 -->
        fourteenzcyf,<!-- 十四项_中成药费 -->
        fourteenybzlf,<!-- 十四项_一般诊疗费 -->
        fourteenghf,<!-- 十四项_挂号费 -->
        fourteenqtf<!-- 十四项_其他费 -->

        from (
        select g.*
        <if test="feeStas==0">
            ,
            IFNULL(ROUND(g.refer_sco * h.price,2),0) AS preCost
        </if>
        from (

        SELECT case when a.A12C=1 then '男' else '女' end as gend,<!-- 性别 -->
        a.a54 AS insuredType,
        a.A14 as age,<!-- 年龄 -->
        IFNULL(IFNULL(D11,0)+IFNULL(D12,0)+IFNULL(D13,0)+IFNULL(D14,0),0) AS com_med_servfee, <!-- 综合医疗服务费 -->
        IFNULL(D11,0) AS ordn_med_servfee, <!-- 一般医疗服务费 -->
        IFNULL(D12,0) AS ordn_trt_oprt_fee, <!-- 一般治疗操作费 -->
        IFNULL(D13,0) AS nursfee, <!-- 护理费 -->
        IFNULL(D14,0) AS zhylfwqtf, <!-- 综合医疗服务其他费 -->
        IFNULL(IFNULL(D15,0)+IFNULL(D16,0)+IFNULL(D17,0)+IFNULL(D18,0),0) AS diag_fee, <!-- 诊断费 -->
        IFNULL(D15,0) AS cas_diag_fee, <!-- 病理诊断费 -->
        IFNULL(D16,0) AS lab_diag_fee, <!-- 实验室诊断费 -->
        IFNULL(D17,0) AS rdhy_diag_fee, <!-- 影像学诊断费 -->
        IFNULL(D18,0) AS clnc_diag_item_fee, <!-- 临床诊断项目费 -->
        IFNULL(IFNULL(D19,0)+IFNULL(D20,0),0) AS treat_fee, <!-- 治疗费 -->
        IFNULL(D19,0) AS nsrgtrt_item_fee, <!-- 非手术治疗项目费 -->
        IFNULL(D20,0) AS oprn_treat_fee, <!-- 手术治疗费 -->
        IFNULL(D21,0) AS rhab_fee, <!-- 康复费 -->
        IFNULL(D22,0) AS tcmdrug_fee, <!-- 中医费 -->
        IFNULL(D23,0) AS west_fee, <!-- 西药费 -->
        IFNULL(IFNULL(D24,0)+IFNULL(D25,0),0) AS zyf1, <!-- 中药费 -->
        IFNULL(D24,0) AS tcmpat_fee, <!-- 中成药费 -->
        IFNULL(D25,0) AS tcmherb, <!-- 中草药费-->
        IFNULL(IFNULL(D26,0)+IFNULL(D27,0)+IFNULL(D28,0)+IFNULL(D29,0)+IFNULL(D30,0),0) AS
        blood_blo_pro_fee, <!-- 血液和血液制品费 -->
        IFNULL(D26,0) AS blo_fee, <!-- 血费 -->
        IFNULL(D27,0) AS bdblzpf, <!-- 白蛋白类制品费 -->
        IFNULL(D28,0) AS qdblzpf, <!-- 球蛋白类制品费 -->
        IFNULL(D29,0) AS nxyzlzpf, <!-- 凝血因子类制品费 -->
        IFNULL(D30,0) AS xbyzlzpf, <!-- 细胞因子类制品费 -->
        IFNULL(IFNULL(D31,0)+IFNULL(D32,0)+IFNULL(D33,0),0) AS mcs_fee, <!-- 耗材费 -->
        IFNULL(D31,0) AS jcyycxyyclf, <!-- 检查用一次性医用材料费 -->
        IFNULL(D32,0) AS trt_use_dspo_med_matlfee, <!-- 治疗用一次性医用材料费 -->
        IFNULL(D33,0) AS oprn_use_dspo_med_matlfee, <!-- 手术用一次性医用材料费 -->
        IFNULL(D34,0) AS oth_fee, <!-- 其他费 -->
        <choose>
            <when test="feeStas==0">
                D01 AS inHosTotalCost, <!-- 住院总费用 -->
                IFNULL(ROUND((IFNULL(D01,0) - IFNULL(CONVERT(AES_DECRYPT(UNHEX(c.standard_avg_fee
                ),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) USING utf8),0)),2),0) AS
                zyfycy,<!-- 住院费用与标杆差异 -->
                ROUND(((IFNULL(b.drugfee/a.D01,0))*100 - IFNULL(d.drug_ratio,0)*100),2) AS yzbcy,
                ROUND(((IFNULL(b.mcs_fee/(a.D01-b.drugfee),0))*100 - IFNULL(d.mcs_fee_rat,0)*100),2) AS
                hzbcy,
                ROUND((IFNULL(b.drugfee/a.D01,0))*100,2) AS yzb, <!-- 药占比 -->
                ROUND((IFNULL(b.mcs_fee/(a.D01-b.drugfee),0))*100,2) AS hzb, <!-- 耗占比 -->
                b.drg_codg AS drgCodg, <!-- DIP编码 -->
                b.DRG_NAME AS drgName, <!-- DIP名称 -->
                f.totl_sco AS refer_sco,
                b.main_diag_dise_codg AS icdCodg,<!-- 病种编码 -->
                b.main_diag_dise_name AS icdName,<!-- 病种名称 -->
            </when>
            <when test="feeStas==1">
                D01 AS inHosTotalCost, <!-- 住院总费用 -->
                IFNULL(ROUND((IFNULL(z.sumfee,0) - IFNULL(CONVERT(AES_DECRYPT(UNHEX(c.standard_avg_fee
                ),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) USING utf8),0)),2),0) AS
                zyfycy,<!-- 住院费用与标杆差异 -->
                ROUND(((IFNULL(b.drugfee/z.sumfee,0))*100 - IFNULL(d.drug_ratio,0)*100),2) AS yzbcy,
                ROUND(((IFNULL(b.mcs_fee/(z.sumfee),0))*100 - IFNULL(d.mcs_fee_rat,0)*100),2) AS hzbcy,
                ROUND((IFNULL(b.drugfee/z.sumfee,0))*100,2) AS yzb, <!-- 药占比 -->
                ROUND((IFNULL(b.mcs_fee/(z.sumfee),0))*100,2) AS hzb, <!-- 耗占比 -->
                z.drg_codg AS drgCodg, <!-- DIP编码 -->
                z.DRG_NAME AS drgName, <!-- DIP名称 -->
                z.setl_sco AS refer_sco,
                z.dfr_fee AS preCost,
            </when>
        </choose>
        ROUND((IFNULL(B20,0) - IFNULL(CONVERT(AES_DECRYPT(UNHEX(c.standard_ave_hosp_day
        ),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) USING utf8),0)),2) as zytscy,<!-- 住院天数与标杆差异 -->
        SUBSTRING(a.B15,1,10) AS outHosTime, <!-- 出院时间 -->
        SUBSTRING(a.B12,1,10) AS inHosTime, <!-- 入院时间 -->
        B16C AS deptCode, <!-- 出院科室编码 -->
        e.`NAME` AS deptName, <!-- 出院科室编码 -->
        a.A11 AS name, <!-- 患者姓名 -->
        a.ID AS id, <!-- settle_list_id-->
        a.A48 AS patientId, <!-- 病案号 -->
        TRIM(B25C) AS residentCode, <!-- 住院医师编码 -->
        TRIM(B25N) AS residentName, <!-- 住院医师姓名 -->
        IFNULL(B20,0) AS inHosDays, <!-- 住院天数 -->
        IFNULL(b.drugfee,0) AS ypf, <!-- 药品费 -->
        IFNULL(b.mcs_fee,0) AS clf, <!-- 材料费 -->
        IFNULL(CONVERT(AES_DECRYPT(UNHEX(c.standard_avg_fee ),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY})
        USING utf8),0) AS bgzyfyjb, <!-- 标杆住院费用（级别） -->
        IFNULL(CONVERT(AES_DECRYPT(UNHEX(c.standard_ave_hosp_day
        ),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY})
        USING utf8),0) AS bgzytsjb, <!-- 标杆住院天数（级别） -->
        ROUND(IFNULL(d.drug_ratio,0)*100,2) AS bgyzb, <!-- 标杆药占比 -->
        ROUND(IFNULL(d.mcs_fee_rat,0)*100,2) AS bghzb, <!-- 标杆耗占比 -->
        IFNULL(d.avg_fee,0) AS ljfy,<!-- 例均费用 -->
        IFNULL(d.com_med_servfee,0) AS bgzhylfwf, <!-- 标杆综合医疗服务费 -->
        IFNULL(d.ordn_med_servfee,0) AS bgybylfwf, <!-- 标杆一般医疗服务费 -->
        IFNULL(d.ordn_trt_oprt_fee,0) AS bgybzlczf, <!-- 标杆一般治疗操作费 -->
        IFNULL(d.nursfee,0) AS bghlf, <!-- 标杆护理费 -->
        IFNULL(d.oth_fee_com,0) AS bgzhylfwqtf, <!-- 标杆综合医疗服务其他费 -->
        IFNULL(d.diag_fee,0) AS bgzdf, <!-- 标杆诊断费 -->
        IFNULL(d.cas_diag_fee,0) AS bgblzdf, <!-- 标杆病理诊断费 -->
        IFNULL(d.lab_diag_fee,0) AS bgsyszdf, <!-- 标杆实验室诊断费 -->
        IFNULL(d.rdhy_diag_fee,0) AS bgyxxzdf, <!-- 标杆影像学诊断费 -->
        IFNULL(d.clnc_diag_item_fee,0) AS bglczdxmf, <!-- 标杆临床诊断项目费 -->
        IFNULL(d.treat_fee,0) AS bgzlf, <!-- 标杆治疗费 -->
        IFNULL(d.nsrgtrt_item_fee,0) AS bgfsszlxmf, <!-- 标杆非手术治疗项目费 -->
        IFNULL(d.oprn_treat_fee,0) AS bgsszlf, <!-- 标杆手术治疗费 -->
        IFNULL(d.rhab_fee,0) AS bgkff, <!-- 标杆康复费 -->
        IFNULL(d.tcm_treat_fee,0) AS bgzyf, <!-- 标杆中医费 -->
        IFNULL(d.west_fee,0) AS bgxyf, <!-- 标杆西药费 -->
        IFNULL(d.tcmdrug_fee,0) AS bgzyf1, <!-- 标杆中药费 -->
        IFNULL(d.tcmpat_fee,0) AS bgzcyf, <!-- 标杆中成药费 -->
        IFNULL(d.tcmherb,0) AS bgzcyf1, <!-- 标杆中草药费 -->
        IFNULL(d.blood_blo_pro_fee,0) AS bgxyhxyzpf, <!-- 标杆血液和血液制品费 -->
        IFNULL(d.blo_fee,0) AS bgxf, <!-- 标杆血费 -->
        IFNULL(d.albu_clss_prod_fee,0) AS bgbdblzpf, <!-- 标杆白蛋白类制品费 -->
        IFNULL(d.glon_clss_prod_fee,0) AS bgqdblzpf, <!-- 标杆球蛋白类制品费 -->
        IFNULL(d.clotfac_clss_prod_fee,0) AS bgnxyzlzpf, <!-- 标杆凝血因子类制品费 -->
        IFNULL(d.cyki_clss_prod_fee,0) AS bgxbyzlzpf, <!-- 标杆细胞因子类制品费 -->
        IFNULL(d.mcs_fee,0) AS bghcf, <!-- 标杆耗材费 -->
        IFNULL(d.exam_use_dspo_med_matlfee,0) AS bgjcyycxyyclf, <!-- 标杆检查用一次性医用材料费 -->
        IFNULL(d.trt_use_dspo_med_matlfee,0) AS bgzlyycxyyclf, <!-- 标杆治疗用一次性医用材料费 -->
        IFNULL(d.oprn_use_dspo_med_matlfee,0) AS bgssyycxyyclf, <!-- 标杆手术用一次性医用材料费 -->
        IFNULL(d.oth_fee,0) AS bgqtf, <!-- 标杆其他费 -->
        1 AS avgCostRate,
        <!-- 十四项_标杆 -->
        IFNULL(d.medi_fee_type_bedfee,0) AS bgfourteencwf,<!-- 十四项_床位费 -->
        IFNULL(d.medi_fee_type_diag_fee,0) AS bgfourteenzcf,<!-- 十四项_诊查费 -->
        IFNULL(d.medi_fee_type_examfee,0) AS bgfourteenjcf,<!-- 十四项_检查费 -->
        IFNULL(d.medi_fee_type_asy_fee,0) AS bgfourteenhyf,<!-- 十四项_化验费 -->
        IFNULL(d.medi_fee_type_treat_fee,0) AS bgfourteenzlf,<!-- 十四项_治疗费 -->
        IFNULL(d.medi_fee_type_oper_fee,0) AS bgfourteenssf,<!-- 十四项_手术费 -->
        IFNULL(d.medi_fee_type_nursfee,0) AS bgfourteenhlf,<!-- 十四项_护理费 -->
        IFNULL(d.medi_fee_type_hc_matlfee,0) AS bgfourteenwsclf,<!-- 十四项_卫生材料费 -->
        IFNULL(d.medi_fee_type_west_fee,0) AS bgfourteenxyf,<!-- 十四项_西药费 -->
        IFNULL(d.medi_fee_type_tmdp_fee,0) AS bgfourteenzyypf,<!-- 十四项_中药饮片费 -->
        IFNULL(d.medi_fee_type_tcmpat_fee,0) AS bgfourteenzcyf,<!-- 十四项_中成药费 -->
        IFNULL(d.medi_fee_type_ordn_trtfee,0) AS bgfourteenybzlf,<!-- 十四项_一般诊疗费 -->
        IFNULL(d.medi_fee_type_regfee,0) AS bgfourteenghf,<!-- 十四项_挂号费 -->
        IFNULL(d.medi_fee_type_oth_fee,0) AS bgfourteenqtf,<!-- 十四项_其他费 -->
        <!-- 十四项_患者 -->
        n.fourteencwf,<!-- 十四项_床位费 -->
        n.fourteenzcf,<!-- 十四项_诊查费 -->
        n.fourteenjcf,<!-- 十四项_检查费 -->
        n.fourteenhyf,<!-- 十四项_化验费 -->
        n.fourteenzlf,<!-- 十四项_治疗费 -->
        n.fourteenssf,<!-- 十四项_手术费 -->
        n.fourteenhlf,<!-- 十四项_护理费 -->
        n.fourteenwsclf,<!-- 十四项_卫生材料费 -->
        n.fourteenxyf,<!-- 十四项_西药费 -->
        n.fourteenzyypf,<!-- 十四项_中药饮片费 -->
        n.fourteenzcyf,<!-- 十四项_中成药费 -->
        n.fourteenybzlf,<!-- 十四项_一般诊疗费 -->
        n.fourteenghf,<!-- 十四项_挂号费 -->
        n.fourteenqtf<!-- 十四项_其他费 -->
        FROM som_hi_invy_bas_info a
        LEFT JOIN som_drg_grp_info b
        ON a.ID = b.SETTLE_LIST_ID
        <if test="enableSeAns != null and enableSeAns != '' and enableSeAns == 1">
            INNER JOIN som_setl_cas_crsp p
            ON a.K00 = p.K00
        </if>
        LEFT JOIN som_drg_standard c
        ON b.drg_codg = c.drg_codg
        AND SUBSTR(b.dscg_time,1,4) = c.STANDARD_YEAR
        AND b.HOSPITAL_ID = c.HOSPITAL_ID
        AND c.ACTIVE_FLAG = 1
        AND a.insuplc_admdvs =c.insuplc_admdvs
        LEFT JOIN som_std_fee d
        ON b.drg_codg = d.`CODE`
        AND SUBSTR(b.dscg_time,1,4) = d.STANDARD_YEAR
        AND TYPE = 3
        LEFT JOIN som_dept e
        ON a.B16C = e.`CODE`
        AND a.HOSPITAL_ID = e.HOSPITAL_ID
        <choose>
            <when test="feeStas==1">
                INNER JOIN
                (
                SELECT
                a.rid_idt_codg,
                a.medcas_codg,
                a.adm_time,
                a.dscg_time,
                a.setl_time,
                a.is_in_group,
                a.drg_codg,
                a.DRG_NAME,
                b.MED_TYPE,
                b.sumfee,
                b.setl_sco,
                b.dfr_fee
                FROM som_drg_grp_fbck a
                LEFT JOIN som_drg_pt_val_pay b
                ON a.rid_idt_codg = b.rid_idt_codg
                AND a.HOSPITAL_ID = b.HOSPITAL_ID
                <!--
                 WHERE a.dscg_time between #{begnDate,jdbcType=VARCHAR} and concat(#{expiDate,jdbcType=VARCHAR},' 23:59:59')
                 -->
                WHERE 1=1
                <if test="begnDate != null and begnDate != '' and expiDate != null and expiDate != ''">
                    a.dscg_time between #{begnDate,jdbcType=VARCHAR} and concat(#{expiDate,jdbcType=VARCHAR},'
                    23:59:59')
                </if>
                <if test="inStartTime != null and inStartTime != '' and inEndTime != null and inEndTime != ''">
                    a.adm_time between #{inStartTime,jdbcType=VARCHAR} and concat(#{inEndTime,jdbcType=VARCHAR},'
                    23:59:59')
                </if>
                <if test="seStartTime != null and seStartTime != '' and seEndTime != null and seEndTime != ''">
                    a.setl_time between #{seStartTime,jdbcType=VARCHAR} and concat(#{seEndTime,jdbcType=VARCHAR},'
                    23:59:59')
                </if>
                <include refid="com.my.som.common.mapper.CommonMapper.fbAuth"/>
                ) z
                ON b.PATIENT_ID = z.medcas_codg
                AND SUBSTR(b.adm_time,1,10) = z.adm_time
                AND SUBSTR(b.dscg_time,1,10) = z.dscg_time
                AND SUBSTR(b.setl_end_time,1,10) = z.setl_time
            </when>
            <when test="feeStas==0">
                LEFT JOIN som_drg_sco f
                ON a.id = f.SETTLE_LIST_ID
            </when>
        </choose>
        LEFT JOIN (
        SELECT
        hi_setl_invy_id,
        MAX(CASE WHEN med_chrg_itemname = '床位费' THEN amt ELSE 0 END) AS fourteencwf,
        MAX(CASE WHEN med_chrg_itemname = '诊查费' THEN amt ELSE 0 END) AS fourteenzcf,
        MAX(CASE WHEN med_chrg_itemname = '检查费' THEN amt ELSE 0 END) AS fourteenjcf,
        MAX(CASE WHEN med_chrg_itemname = '化验费' THEN amt ELSE 0 END) AS fourteenhyf,
        MAX(CASE WHEN med_chrg_itemname = '治疗费' THEN amt ELSE 0 END) AS fourteenzlf,
        MAX(CASE WHEN med_chrg_itemname = '手术费' THEN amt ELSE 0 END) AS fourteenssf,
        MAX(CASE WHEN med_chrg_itemname = '护理费' THEN amt ELSE 0 END) AS fourteenhlf,
        MAX(CASE WHEN med_chrg_itemname = '卫生材料费' THEN amt ELSE 0 END) AS fourteenwsclf,
        MAX(CASE WHEN med_chrg_itemname = '西药费' THEN amt ELSE 0 END) AS fourteenxyf,
        MAX(CASE WHEN med_chrg_itemname = '中药饮片费' THEN amt ELSE 0 END) AS fourteenzyypf,
        MAX(CASE WHEN med_chrg_itemname = '中成药费' THEN amt ELSE 0 END) AS fourteenzcyf,
        MAX(CASE WHEN med_chrg_itemname = '一般诊疗费' THEN amt ELSE 0 END) AS fourteenybzlf,
        MAX(CASE WHEN med_chrg_itemname = '挂号费' THEN amt ELSE 0 END) AS fourteenghf,
        MAX(CASE WHEN med_chrg_itemname = '其他费' THEN amt ELSE 0 END) AS fourteenqtf
        FROM som_hi_setl_invy_med_fee_info
        WHERE hi_setl_invy_id IN(
        SELECT ID AS hi_setl_invy_id
        FROM som_hi_invy_bas_info
        <!-- WHERE B15 BETWEEN #{begnDate,jdbcType=VARCHAR} AND CONCAT(#{expiDate,jdbcType=VARCHAR},' 23:59:59') -->
        <where>
            <if test="begnDate != null and begnDate != '' and expiDate != null and expiDate != ''">
                AND B15 BETWEEN #{begnDate,jdbcType=VARCHAR} AND CONCAT(#{expiDate,jdbcType=VARCHAR},' 23:59:59')
            </if>
            <if test="inStartTime != null and inStartTime != '' and inEndTime != null and inEndTime != ''">
                AND B12 between #{inStartTime,jdbcType=VARCHAR} and concat(#{inEndTime,jdbcType=VARCHAR},' 23:59:59')
            </if>
            <if test="seStartTime != null and seStartTime != '' and seEndTime != null and seEndTime != ''">
                AND D37 between #{seStartTime,jdbcType=VARCHAR} and concat(#{seEndTime,jdbcType=VARCHAR},' 23:59:59')
            </if>
        </where>
        )
        GROUP BY hi_setl_invy_id
        ) n
        ON a.ID = n.hi_setl_invy_id
        WHERE a.ACTIVE_FLAG = '1'
        <include refid="queryCommon"/>
        <if test="drgCodg != null and drgCodg != ''">
            AND b.drg_codg = #{drgCodg,jdbcType=VARCHAR}
        </if>
        <if test="icdCodg != null and icdCodg != ''">
            AND b.main_diag_dise_codg = #{icdCodg,jdbcType=VARCHAR}
        </if>
        <!-- and a.b15 between #{begnDate,jdbcType=VARCHAR} and concat(#{expiDate,jdbcType=VARCHAR},' 23:59:59') -->
        <if test="inStartTime != null and inStartTime != '' and inEndTime != null and inEndTime != ''">
            AND a.B12 BETWEEN #{inStartTime} AND CONCAT(#{inEndTime},' 23:59:59')
        </if>
        <if test="begnDate != null and begnDate != '' and expiDate != null and expiDate != ''">
            AND a.B15 BETWEEN #{begnDate,jdbcType=VARCHAR} AND CONCAT(#{expiDate,jdbcType=VARCHAR},' 23:59:59')
        </if>
        <if test="seStartTime != null and seStartTime != '' and seEndTime != null and seEndTime != ''">
            AND a.D37 between #{seStartTime,jdbcType=VARCHAR} and concat(#{seEndTime,jdbcType=VARCHAR},' 23:59:59')
        </if>
        ) g
        <if test="feeStas==0">
            LEFT JOIN (
            SELECT
            SETTLE_LIST_ID,
            price
            FROM
            som_drg_sco
            )   h
            on h.SETTLE_LIST_ID = g.id
        </if>
        ) i
        ) k
        where
        <![CDATA[
           diff ${gtOrLtSymbol} 0
        ]]>
        ORDER BY diff
    </select>


    <select id="queryDrgPatientLoss2" resultType="com.my.som.vo.newBusiness.NewBusinessAnalysisVo">
        select k.*
        from(
        select IFNULL(ROUND(preCost,2),0) as preTotalCost,
        IFNULL(ROUND(inHosTotalCost,2),0) AS avgCost,
        IFNULL(ROUND(inHosTotalCost,2),0) AS sumfee,
        IFNULL(ROUND(preCost - inHosTotalCost,2),0) AS diff,
        name,
        patientId,
        drgCodg,
        drgName,
        icdCodg,
        icdName,
        ROUND(com_med_servfee,2) AS com_med_servfee,
        ROUND(ordn_trt_oprt_fee,2) AS ordn_trt_oprt_fee,
        ROUND(ordn_med_servfee,2) AS ordn_med_servfee,
        ROUND(nursfee,2) AS nursfee,
        ROUND(zhylfwqtf,2) AS zhylfwqtf,

        ROUND(diag_fee,2) AS diag_fee,
        ROUND(cas_diag_fee,2) AS cas_diag_fee,
        ROUND(lab_diag_fee,2) AS lab_diag_fee,
        ROUND(rdhy_diag_fee,2) AS rdhy_diag_fee,
        ROUND(clnc_diag_item_fee,2) AS clnc_diag_item_fee,

        ROUND(treat_fee,2) AS treat_fee,
        ROUND(nsrgtrt_item_fee,2) AS nsrgtrt_item_fee,
        ROUND(oprn_treat_fee,2) AS oprn_treat_fee,

        ROUND(rhab_fee,2) AS rhab_fee,

        ROUND(tcmdrug_fee,2) AS tcmdrug_fee,

        ROUND(west_fee,2) AS west_fee,

        ROUND(zyf1,2) AS zyf1,
        ROUND(tcmpat_fee,2) AS tcmpat_fee,
        ROUND(tcmherb,2) AS tcmherb,

        ROUND(blood_blo_pro_fee,2) AS blood_blo_pro_fee,
        ROUND(blo_fee,2) AS blo_fee,
        ROUND(qdblzpf,2) AS qdblzpf,
        ROUND(bdblzpf,2) AS bdblzpf,
        ROUND(nxyzlzpf,2) AS nxyzlzpf,
        ROUND(xbyzlzpf,2) AS xbyzlzpf,

        ROUND(mcs_fee,2) AS mcs_fee,
        ROUND(jcyycxyyclf,2) AS jcyycxyyclf,
        ROUND(trt_use_dspo_med_matlfee,2) AS trt_use_dspo_med_matlfee,
        ROUND(oprn_use_dspo_med_matlfee,2) AS oprn_use_dspo_med_matlfee,

        ROUND(oth_fee,2) AS avgQtf,


        <!-- 标杆 -->
        ROUND(bgzhylfwf * avgCostRate,2) AS bgzhylfwf,
        ROUND(bgybylfwf * avgCostRate,2) AS bgybylfwf,
        ROUND(bghlf * avgCostRate,2) AS bghlf,
        ROUND(bgzhylfwqtf * avgCostRate,2) AS bgzhylfwqtf,

        ROUND(bgzdf * avgCostRate,2) AS bgzdf,
        ROUND(bgblzdf * avgCostRate,2) AS bgblzdf,
        ROUND(bgsyszdf * avgCostRate,2) AS bgsyszdf,
        ROUND(bgyxxzdf * avgCostRate,2) AS bgyxxzdf,
        ROUND(bglczdxmf * avgCostRate,2) AS bglczdxmf,

        ROUND(bgzlf * avgCostRate,2) AS bgzlf,
        ROUND(bgfsszlxmf * avgCostRate,2) AS bgfsszlxmf,
        ROUND(bgsszlf * avgCostRate,2) AS bgsszlf,

        ROUND(bgkff * avgCostRate,2) AS bgkff,

        ROUND(bgzyf * avgCostRate,2) AS bgzyf,

        ROUND(bgxyf * avgCostRate,2) AS bgxyf,

        ROUND(bgzyf1 * avgCostRate,2) AS bgzyf1,
        ROUND(bgzcyf * avgCostRate,2) AS bgzcyf,
        ROUND(bgzcyf1 * avgCostRate,2) AS bgzcyf1,

        ROUND(bgxyhxyzpf * avgCostRate,2) AS bgxyhxyzpf,
        ROUND(bgxf * avgCostRate,2) AS bgxf,
        ROUND(bgbdblzpf * avgCostRate,2) AS bgbdblzpf,
        ROUND(bgqdblzpf * avgCostRate,2) AS bgqdblzpf,
        ROUND(bgnxyzlzpf * avgCostRate,2) AS bgnxyzlzpf,
        ROUND(bgxbyzlzpf * avgCostRate,2) AS bgxbyzlzpf,

        ROUND(bghcf * avgCostRate,2) AS bghcf,
        ROUND(bgjcyycxyyclf * avgCostRate,2) AS bgjcyycxyyclf,
        ROUND(bgzlyycxyyclf * avgCostRate,2) AS bgzlyycxyyclf,
        ROUND(bgssyycxyyclf * avgCostRate,2) AS bgssyycxyyclf,

        ROUND(bgqtf,2) AS bgqtf,
        <!-- 十四项_标杆 -->
        bgfourteencwf,<!-- 十四项_床位费 -->
        bgfourteenzcf,<!-- 十四项_诊查费 -->
        bgfourteenjcf,<!-- 十四项_检查费 -->
        bgfourteenhyf,<!-- 十四项_化验费 -->
        bgfourteenzlf,<!-- 十四项_治疗费 -->
        bgfourteenssf,<!-- 十四项_手术费 -->
        bgfourteenhlf,<!-- 十四项_护理费 -->
        bgfourteenwsclf,<!-- 十四项_卫生材料费 -->
        bgfourteenxyf,<!-- 十四项_西药费 -->
        bgfourteenzyypf,<!-- 十四项_中药饮片费 -->
        bgfourteenzcyf,<!-- 十四项_中成药费 -->
        bgfourteenybzlf,<!-- 十四项_一般诊疗费 -->
        bgfourteenghf,<!-- 十四项_挂号费 -->
        bgfourteenqtf,<!-- 十四项_其他费 -->
        <!-- 十四项_患者 -->
        fourteencwf,<!-- 十四项_床位费 -->
        fourteenzcf,<!-- 十四项_诊查费 -->
        fourteenjcf,<!-- 十四项_检查费 -->
        fourteenhyf,<!-- 十四项_化验费 -->
        fourteenzlf,<!-- 十四项_治疗费 -->
        fourteenssf,<!-- 十四项_手术费 -->
        fourteenhlf,<!-- 十四项_护理费 -->
        fourteenwsclf,<!-- 十四项_卫生材料费 -->
        fourteenxyf,<!-- 十四项_西药费 -->
        fourteenzyypf,<!-- 十四项_中药饮片费 -->
        fourteenzcyf,<!-- 十四项_中成药费 -->
        fourteenybzlf,<!-- 十四项_一般诊疗费 -->
        fourteenghf,<!-- 十四项_挂号费 -->
        fourteenqtf<!-- 十四项_其他费 -->

        from (
        select g.*
        <if test="feeStas==0">
            ,
            IFNULL(ROUND(g.refer_sco * h.price,2),0) AS preCost
        </if>
        from (

        SELECT case when a.A12C=1 then '男' else '女' end as gend,<!-- 性别 -->
        a.a54 AS insuredType,
        a.A14 as age,<!-- 年龄 -->
        IFNULL(IFNULL(D11,0)+IFNULL(D12,0)+IFNULL(D13,0)+IFNULL(D14,0),0) AS com_med_servfee, <!-- 综合医疗服务费 -->
        IFNULL(D11,0) AS ordn_med_servfee, <!-- 一般医疗服务费 -->
        IFNULL(D12,0) AS ordn_trt_oprt_fee, <!-- 一般治疗操作费 -->
        IFNULL(D13,0) AS nursfee, <!-- 护理费 -->
        IFNULL(D14,0) AS zhylfwqtf, <!-- 综合医疗服务其他费 -->
        IFNULL(IFNULL(D15,0)+IFNULL(D16,0)+IFNULL(D17,0)+IFNULL(D18,0),0) AS diag_fee, <!-- 诊断费 -->
        IFNULL(D15,0) AS cas_diag_fee, <!-- 病理诊断费 -->
        IFNULL(D16,0) AS lab_diag_fee, <!-- 实验室诊断费 -->
        IFNULL(D17,0) AS rdhy_diag_fee, <!-- 影像学诊断费 -->
        IFNULL(D18,0) AS clnc_diag_item_fee, <!-- 临床诊断项目费 -->
        IFNULL(IFNULL(D19,0)+IFNULL(D20,0),0) AS treat_fee, <!-- 治疗费 -->
        IFNULL(D19,0) AS nsrgtrt_item_fee, <!-- 非手术治疗项目费 -->
        IFNULL(D20,0) AS oprn_treat_fee, <!-- 手术治疗费 -->
        IFNULL(D21,0) AS rhab_fee, <!-- 康复费 -->
        IFNULL(D22,0) AS tcmdrug_fee, <!-- 中医费 -->
        IFNULL(D23,0) AS west_fee, <!-- 西药费 -->
        IFNULL(IFNULL(D24,0)+IFNULL(D25,0),0) AS zyf1, <!-- 中药费 -->
        IFNULL(D24,0) AS tcmpat_fee, <!-- 中成药费 -->
        IFNULL(D25,0) AS tcmherb, <!-- 中草药费-->
        IFNULL(IFNULL(D26,0)+IFNULL(D27,0)+IFNULL(D28,0)+IFNULL(D29,0)+IFNULL(D30,0),0) AS blood_blo_pro_fee, <!-- 血液和血液制品费 -->
        IFNULL(D26,0) AS blo_fee, <!-- 血费 -->
        IFNULL(D27,0) AS bdblzpf, <!-- 白蛋白类制品费 -->
        IFNULL(D28,0) AS qdblzpf, <!-- 球蛋白类制品费 -->
        IFNULL(D29,0) AS nxyzlzpf, <!-- 凝血因子类制品费 -->
        IFNULL(D30,0) AS xbyzlzpf, <!-- 细胞因子类制品费 -->
        IFNULL(IFNULL(D31,0)+IFNULL(D32,0)+IFNULL(D33,0),0) AS mcs_fee, <!-- 耗材费 -->
        IFNULL(D31,0) AS jcyycxyyclf, <!-- 检查用一次性医用材料费 -->
        IFNULL(D32,0) AS trt_use_dspo_med_matlfee, <!-- 治疗用一次性医用材料费 -->
        IFNULL(D33,0) AS oprn_use_dspo_med_matlfee, <!-- 手术用一次性医用材料费 -->
        IFNULL(D34,0) AS oth_fee, <!-- 其他费 -->
        <choose>
            <when test="feeStas==0">
                D01 AS inHosTotalCost, <!-- 住院总费用 -->
                IFNULL(ROUND((IFNULL(D01,0) - IFNULL(CONVERT(AES_DECRYPT(UNHEX(c.standard_avg_fee
                ),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) USING utf8),0)),2),0) AS
                zyfycy,<!-- 住院费用与标杆差异 -->
                ROUND(((IFNULL(b.drugfee/a.D01,0))*100 - IFNULL(d.drug_ratio,0)*100),2) AS yzbcy,
                ROUND(((IFNULL(b.mcs_fee/(a.D01-b.drugfee),0))*100 - IFNULL(d.mcs_fee_rat,0)*100),2) AS
                hzbcy,
                ROUND((IFNULL(b.drugfee/a.D01,0))*100,2) AS yzb, <!-- 药占比 -->
                ROUND((IFNULL(b.mcs_fee/(a.D01-b.drugfee),0))*100,2) AS hzb, <!-- 耗占比 -->
                b.drg_codg AS drgCodg, <!-- DIP编码 -->
                b.DRG_NAME AS drgName, <!-- DIP名称 -->
                f.totl_sco AS refer_sco,
                b.main_diag_dise_codg AS icdCodg,<!-- 病种编码 -->
                b.main_diag_dise_name AS icdName,<!-- 病种名称 -->
            </when>
            <when test="feeStas==1">
                D01 AS inHosTotalCost, <!-- 住院总费用 -->
                IFNULL(ROUND((IFNULL(z.sumfee,0) - IFNULL(CONVERT(AES_DECRYPT(UNHEX(c.standard_avg_fee
                ),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) USING utf8),0)),2),0) AS
                zyfycy,<!-- 住院费用与标杆差异 -->
                ROUND(((IFNULL(b.drugfee/z.sumfee,0))*100 - IFNULL(d.drug_ratio,0)*100),2) AS yzbcy,
                ROUND(((IFNULL(b.mcs_fee/(z.sumfee),0))*100 - IFNULL(d.mcs_fee_rat,0)*100),2) AS hzbcy,
                ROUND((IFNULL(b.drugfee/z.sumfee,0))*100,2) AS yzb, <!-- 药占比 -->
                ROUND((IFNULL(b.mcs_fee/(z.sumfee),0))*100,2) AS hzb, <!-- 耗占比 -->
                z.drg_codg AS drgCodg, <!-- DIP编码 -->
                z.DRG_NAME AS drgName, <!-- DIP名称 -->
                z.setl_sco AS refer_sco,
                z.dfr_fee AS preCost,
            </when>
        </choose>
        ROUND((IFNULL(B20,0) - IFNULL(CONVERT(AES_DECRYPT(UNHEX(c.standard_ave_hosp_day
        ),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) USING utf8),0)),2) as zytscy,<!-- 住院天数与标杆差异 -->
        SUBSTRING(a.B15,1,10) AS outHosTime, <!-- 出院时间 -->
        SUBSTRING(a.B12,1,10) AS inHosTime, <!-- 入院时间 -->
        B16C AS deptCode, <!-- 出院科室编码 -->
        e.`NAME` AS deptName, <!-- 出院科室编码 -->
        a.A11 AS name, <!-- 患者姓名 -->
        a.ID AS id, <!-- settle_list_id-->
        a.A48 AS patientId, <!-- 病案号 -->
        TRIM(B25C) AS residentCode, <!-- 住院医师编码 -->
        TRIM(B25N) AS residentName, <!-- 住院医师姓名 -->
        IFNULL(B20,0) AS inHosDays, <!-- 住院天数 -->
        IFNULL(b.drugfee,0) AS ypf, <!-- 药品费 -->
        IFNULL(b.mcs_fee,0) AS clf, <!-- 材料费 -->
        IFNULL(CONVERT(AES_DECRYPT(UNHEX(c.standard_avg_fee ),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY})
        USING utf8),0) AS bgzyfyjb, <!-- 标杆住院费用（级别） -->
        IFNULL(CONVERT(AES_DECRYPT(UNHEX(c.standard_ave_hosp_day ),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY})
        USING utf8),0) AS bgzytsjb, <!-- 标杆住院天数（级别） -->
        ROUND(IFNULL(d.drug_ratio,0)*100,2) AS bgyzb, <!-- 标杆药占比 -->
        ROUND(IFNULL(d.mcs_fee_rat,0)*100,2) AS bghzb, <!-- 标杆耗占比 -->
        IFNULL(d.avg_fee,0) AS ljfy,<!-- 例均费用 -->
        IFNULL(d.com_med_servfee,0) AS bgzhylfwf, <!-- 标杆综合医疗服务费 -->
        IFNULL(d.ordn_med_servfee,0) AS bgybylfwf, <!-- 标杆一般医疗服务费 -->
        IFNULL(d.ordn_trt_oprt_fee,0) AS bgybzlczf, <!-- 标杆一般治疗操作费 -->
        IFNULL(d.nursfee,0) AS bghlf, <!-- 标杆护理费 -->
        IFNULL(d.oth_fee_com,0) AS bgzhylfwqtf, <!-- 标杆综合医疗服务其他费 -->
        IFNULL(d.diag_fee,0) AS bgzdf, <!-- 标杆诊断费 -->
        IFNULL(d.cas_diag_fee,0) AS bgblzdf, <!-- 标杆病理诊断费 -->
        IFNULL(d.lab_diag_fee,0) AS bgsyszdf, <!-- 标杆实验室诊断费 -->
        IFNULL(d.rdhy_diag_fee,0) AS bgyxxzdf, <!-- 标杆影像学诊断费 -->
        IFNULL(d.clnc_diag_item_fee,0) AS bglczdxmf, <!-- 标杆临床诊断项目费 -->
        IFNULL(d.treat_fee,0) AS bgzlf, <!-- 标杆治疗费 -->
        IFNULL(d.nsrgtrt_item_fee,0) AS bgfsszlxmf, <!-- 标杆非手术治疗项目费 -->
        IFNULL(d.oprn_treat_fee,0) AS bgsszlf, <!-- 标杆手术治疗费 -->
        IFNULL(d.rhab_fee,0) AS bgkff, <!-- 标杆康复费 -->
        IFNULL(d.tcm_treat_fee,0) AS bgzyf, <!-- 标杆中医费 -->
        IFNULL(d.west_fee,0) AS bgxyf, <!-- 标杆西药费 -->
        IFNULL(d.tcmdrug_fee,0) AS bgzyf1, <!-- 标杆中药费 -->
        IFNULL(d.tcmpat_fee,0) AS bgzcyf, <!-- 标杆中成药费 -->
        IFNULL(d.tcmherb,0) AS bgzcyf1, <!-- 标杆中草药费 -->
        IFNULL(d.blood_blo_pro_fee,0) AS bgxyhxyzpf, <!-- 标杆血液和血液制品费 -->
        IFNULL(d.blo_fee,0) AS bgxf, <!-- 标杆血费 -->
        IFNULL(d.albu_clss_prod_fee,0) AS bgbdblzpf, <!-- 标杆白蛋白类制品费 -->
        IFNULL(d.glon_clss_prod_fee,0) AS bgqdblzpf, <!-- 标杆球蛋白类制品费 -->
        IFNULL(d.clotfac_clss_prod_fee,0) AS bgnxyzlzpf, <!-- 标杆凝血因子类制品费 -->
        IFNULL(d.cyki_clss_prod_fee,0) AS bgxbyzlzpf, <!-- 标杆细胞因子类制品费 -->
        IFNULL(d.mcs_fee,0) AS bghcf, <!-- 标杆耗材费 -->
        IFNULL(d.exam_use_dspo_med_matlfee,0) AS bgjcyycxyyclf, <!-- 标杆检查用一次性医用材料费 -->
        IFNULL(d.trt_use_dspo_med_matlfee,0) AS bgzlyycxyyclf, <!-- 标杆治疗用一次性医用材料费 -->
        IFNULL(d.oprn_use_dspo_med_matlfee,0) AS bgssyycxyyclf, <!-- 标杆手术用一次性医用材料费 -->
        IFNULL(d.oth_fee,0) AS bgqtf, <!-- 标杆其他费 -->
        1 AS avgCostRate,
        <!-- 十四项_标杆 -->
        IFNULL(d.medi_fee_type_bedfee,0) AS bgfourteencwf,<!-- 十四项_床位费 -->
        IFNULL(d.medi_fee_type_diag_fee,0) AS bgfourteenzcf,<!-- 十四项_诊查费 -->
        IFNULL(d.medi_fee_type_examfee,0) AS bgfourteenjcf,<!-- 十四项_检查费 -->
        IFNULL(d.medi_fee_type_asy_fee,0) AS bgfourteenhyf,<!-- 十四项_化验费 -->
        IFNULL(d.medi_fee_type_treat_fee,0) AS bgfourteenzlf,<!-- 十四项_治疗费 -->
        IFNULL(d.medi_fee_type_oper_fee,0) AS bgfourteenssf,<!-- 十四项_手术费 -->
        IFNULL(d.medi_fee_type_nursfee,0) AS bgfourteenhlf,<!-- 十四项_护理费 -->
        IFNULL(d.medi_fee_type_hc_matlfee,0) AS bgfourteenwsclf,<!-- 十四项_卫生材料费 -->
        IFNULL(d.medi_fee_type_west_fee,0) AS bgfourteenxyf,<!-- 十四项_西药费 -->
        IFNULL(d.medi_fee_type_tmdp_fee,0) AS bgfourteenzyypf,<!-- 十四项_中药饮片费 -->
        IFNULL(d.medi_fee_type_tcmpat_fee,0) AS bgfourteenzcyf,<!-- 十四项_中成药费 -->
        IFNULL(d.medi_fee_type_ordn_trtfee,0) AS bgfourteenybzlf,<!-- 十四项_一般诊疗费 -->
        IFNULL(d.medi_fee_type_regfee,0) AS bgfourteenghf,<!-- 十四项_挂号费 -->
        IFNULL(d.medi_fee_type_oth_fee,0) AS bgfourteenqtf,<!-- 十四项_其他费 -->
        <!-- 十四项_患者 -->
        n.fourteencwf,<!-- 十四项_床位费 -->
        n.fourteenzcf,<!-- 十四项_诊查费 -->
        n.fourteenjcf,<!-- 十四项_检查费 -->
        n.fourteenhyf,<!-- 十四项_化验费 -->
        n.fourteenzlf,<!-- 十四项_治疗费 -->
        n.fourteenssf,<!-- 十四项_手术费 -->
        n.fourteenhlf,<!-- 十四项_护理费 -->
        n.fourteenwsclf,<!-- 十四项_卫生材料费 -->
        n.fourteenxyf,<!-- 十四项_西药费 -->
        n.fourteenzyypf,<!-- 十四项_中药饮片费 -->
        n.fourteenzcyf,<!-- 十四项_中成药费 -->
        n.fourteenybzlf,<!-- 十四项_一般诊疗费 -->
        n.fourteenghf,<!-- 十四项_挂号费 -->
        n.fourteenqtf<!-- 十四项_其他费 -->
        FROM som_hi_invy_bas_info a
        LEFT JOIN som_drg_grp_info b
        ON a.ID = b.SETTLE_LIST_ID
        <if test="enableSeAns != null and enableSeAns != '' and enableSeAns == 1">
            INNER JOIN som_setl_cas_crsp p
            ON a.K00 = p.K00
        </if>
        LEFT JOIN som_drg_standard c
        ON b.drg_codg = c.drg_codg
        AND SUBSTR(b.dscg_time,1,4) = c.STANDARD_YEAR
        AND b.HOSPITAL_ID = c.HOSPITAL_ID
        AND c.ACTIVE_FLAG = 1
        LEFT JOIN std_cost_dy_sj d
        ON b.drg_codg = d.`CODE`
        AND SUBSTR(b.dscg_time,1,4) = d.STANDARD_YEAR
        AND TYPE = 3
        LEFT JOIN som_dept e
        ON a.B16C = e.`CODE`
        AND a.HOSPITAL_ID = e.HOSPITAL_ID
        <choose>
            <when test="feeStas==1">
                INNER JOIN
                (
                SELECT
                a.rid_idt_codg,
                a.medcas_codg,
                a.adm_time,
                a.dscg_time,
                a.setl_time,
                a.is_in_group,
                a.drg_codg,
                a.DRG_NAME,
                b.MED_TYPE,
                b.sumfee,
                b.setl_sco,
                b.dfr_fee
                FROM som_drg_grp_fbck a
                LEFT JOIN som_drg_pt_val_pay b
                ON a.rid_idt_codg = b.rid_idt_codg
                AND a.HOSPITAL_ID = b.HOSPITAL_ID
                <!--
                 WHERE a.dscg_time between #{begnDate,jdbcType=VARCHAR} and concat(#{expiDate,jdbcType=VARCHAR},' 23:59:59')
                 -->
                WHERE 1=1
                <if test="begnDate != null and begnDate != '' and expiDate != null and expiDate != ''">
                    a.dscg_time between #{begnDate,jdbcType=VARCHAR} and concat(#{expiDate,jdbcType=VARCHAR},'
                    23:59:59')
                </if>
                <if test="inStartTime != null and inStartTime != '' and inEndTime != null and inEndTime != ''">
                    a.adm_time between #{inStartTime,jdbcType=VARCHAR} and concat(#{inEndTime,jdbcType=VARCHAR},'
                    23:59:59')
                </if>
                <if test="seStartTime != null and seStartTime != '' and seEndTime != null and seEndTime != ''">
                    a.setl_time between #{seStartTime,jdbcType=VARCHAR} and concat(#{seEndTime,jdbcType=VARCHAR},'
                    23:59:59')
                </if>
                <include refid="com.my.som.common.mapper.CommonMapper.fbAuth"/>
                ) z
                ON b.PATIENT_ID = z.medcas_codg
                AND SUBSTR(b.adm_time,1,10) = z.adm_time
                AND SUBSTR(b.dscg_time,1,10) = z.dscg_time
                AND SUBSTR(b.setl_end_time,1,10) = z.setl_time
            </when>
            <when test="feeStas==0">
                LEFT JOIN som_drg_sco f
                ON a.id = f.SETTLE_LIST_ID
            </when>
        </choose>
        LEFT JOIN (
        SELECT
        hi_setl_invy_id,
        MAX(CASE WHEN med_chrg_itemname = '床位费' THEN amt ELSE 0 END) AS fourteencwf,
        MAX(CASE WHEN med_chrg_itemname = '诊查费' THEN amt ELSE 0 END) AS fourteenzcf,
        MAX(CASE WHEN med_chrg_itemname = '检查费' THEN amt ELSE 0 END) AS fourteenjcf,
        MAX(CASE WHEN med_chrg_itemname = '化验费' THEN amt ELSE 0 END) AS fourteenhyf,
        MAX(CASE WHEN med_chrg_itemname = '治疗费' THEN amt ELSE 0 END) AS fourteenzlf,
        MAX(CASE WHEN med_chrg_itemname = '手术费' THEN amt ELSE 0 END) AS fourteenssf,
        MAX(CASE WHEN med_chrg_itemname = '护理费' THEN amt ELSE 0 END) AS fourteenhlf,
        MAX(CASE WHEN med_chrg_itemname = '卫生材料费' THEN amt ELSE 0 END) AS fourteenwsclf,
        MAX(CASE WHEN med_chrg_itemname = '西药费' THEN amt ELSE 0 END) AS fourteenxyf,
        MAX(CASE WHEN med_chrg_itemname = '中药饮片费' THEN amt ELSE 0 END) AS fourteenzyypf,
        MAX(CASE WHEN med_chrg_itemname = '中成药费' THEN amt ELSE 0 END) AS fourteenzcyf,
        MAX(CASE WHEN med_chrg_itemname = '一般诊疗费' THEN amt ELSE 0 END) AS fourteenybzlf,
        MAX(CASE WHEN med_chrg_itemname = '挂号费' THEN amt ELSE 0 END) AS fourteenghf,
        MAX(CASE WHEN med_chrg_itemname = '其他费' THEN amt ELSE 0 END) AS fourteenqtf
        FROM som_hi_setl_invy_med_fee_info
        WHERE hi_setl_invy_id IN(
        SELECT ID AS hi_setl_invy_id
        FROM som_hi_invy_bas_info
        <!-- WHERE B15 BETWEEN #{begnDate,jdbcType=VARCHAR} AND CONCAT(#{expiDate,jdbcType=VARCHAR},' 23:59:59') -->
        <where>
            <if test="begnDate != null and begnDate != '' and expiDate != null and expiDate != ''">
                AND B15 BETWEEN #{begnDate,jdbcType=VARCHAR} AND CONCAT(#{expiDate,jdbcType=VARCHAR},' 23:59:59')
            </if>
            <if test="inStartTime != null and inStartTime != '' and inEndTime != null and inEndTime != ''">
                AND B12 between #{inStartTime,jdbcType=VARCHAR} and concat(#{inEndTime,jdbcType=VARCHAR},' 23:59:59')
            </if>
            <if test="seStartTime != null and seStartTime != '' and seEndTime != null and seEndTime != ''">
                AND D37 between #{seStartTime,jdbcType=VARCHAR} and concat(#{seEndTime,jdbcType=VARCHAR},' 23:59:59')
            </if>
        </where>
        )
        GROUP BY hi_setl_invy_id
        ) n
        ON a.ID = n.hi_setl_invy_id
        WHERE a.ACTIVE_FLAG = '1'
        <include refid="queryCommon"/>
        <if test="drgCodg != null and drgCodg != ''">
            AND b.drg_codg = #{drgCodg,jdbcType=VARCHAR}
        </if>
        <if test="icdCodg != null and icdCodg != ''">
            AND b.main_diag_dise_codg = #{icdCodg,jdbcType=VARCHAR}
        </if>
        <!-- and a.b15 between #{begnDate,jdbcType=VARCHAR} and concat(#{expiDate,jdbcType=VARCHAR},' 23:59:59') -->
        <if test="inStartTime != null and inStartTime != '' and inEndTime != null and inEndTime != ''">
            AND a.B12 BETWEEN #{inStartTime} AND CONCAT(#{inEndTime},' 23:59:59')
        </if>
        <if test="begnDate != null and begnDate != '' and expiDate != null and expiDate != ''">
            AND a.B15 BETWEEN #{begnDate,jdbcType=VARCHAR} AND CONCAT(#{expiDate,jdbcType=VARCHAR},' 23:59:59')
        </if>
        <if test="seStartTime != null and seStartTime != '' and seEndTime != null and seEndTime != ''">
            AND a.D37 between #{seStartTime,jdbcType=VARCHAR} and concat(#{seEndTime,jdbcType=VARCHAR},' 23:59:59')
        </if>
        ) g
        <if test="feeStas==0">
            Left JOIN
            (
            SELECT
            SETTLE_LIST_ID,
            price
            FROM
            som_drg_sco
            )   h
            on h.SETTLE_LIST_ID = g.id
        </if>
        ) i
        ) k
        where
        <![CDATA[
           diff ${gtOrLtSymbol} 0
        ]]>
        ORDER BY diff
    </select>


    <select id="queryDrgAnalysisSummary" resultType="com.my.som.vo.newBusiness.NewBusinessAnalysisVo">
        select
        <if test='queryType !="2" and dropdownType != 3 and dropdownType != 4'>
            deptCode,
            deptName,
        </if>
        <if test='queryType =="2"'>
            drCodg,
            drName,
        </if>
        <if test="dropdownType == 3">
            drgCodg,
            drgName,
        </if>
        <if test="dropdownType == 4">
            icdCodg,
            icdName,
        </if>
        count(1) AS lossOrProfitNum
        from (
        SELECT
        k.*
        FROM
        (
        SELECT
        <choose>
            <!-- 病组 -->
            <when test='analysisType == "1"'>
                drgCodg,
                drgName,
            </when>
            <!-- 医生 -->
            <when test='analysisType == "2"'>
                drCodg,
                drName,
            </when>
            <!-- 患者 -->
            <when test='analysisType == "3"'>
                id,
            </when>
            <!-- 病种 -->
            <when test='analysisType == "4"'>
                icdCodg,
                icdName,
            </when>

        </choose>
        <if test='queryType !="2" and dropdownType != 3  and dropdownType != 4'>
            deptCode,
            deptName,
        </if>
        <if test='queryType =="2" and analysisType != "2"'>
            drCodg,
            drName,
        </if>
        <if test="dropdownType == 3 and analysisType != 1">
            drgCodg,
            drgName,
        </if>
        <if test="dropdownType == 4 and analysisType != 4">
            icdCodg,
            icdName,
        </if>
        IFNULL( ROUND( sum( preCost ) - sum( inHosTotalCost ), 2 ), 0 ) AS diff
        FROM
        (
        SELECT
        g.*
        <if test="feeStas==0">
            ,
            IFNULL(ROUND(g.refer_sco * h.price,2),0) AS preCost
        </if>
        FROM
        (
        SELECT a.a54 AS insuredType,
        a.B25N AS drName,
        a.B25C AS drCodg,
        a.A48 AS patientId,
        <choose>
            <when test="feeStas==0">
                a.D01 AS inHosTotalCost,
                b.drg_codg AS drgCodg,
                b.DRG_NAME AS drgName,
                f.totl_sco AS refer_sco,
                f.dise_type AS highLowType,
                b.main_diag_dise_codg AS icdCodg,
                b.main_diag_dise_name AS icdName,
            </when>
            <when test="feeStas==1">
                z.sumfee AS inHosTotalCost,
                z.drg_codg AS drgCodg, <!-- DIP编码 -->
                z.DRG_NAME AS drgName, <!-- DIP名称 -->
                z.setl_sco AS refer_sco,
                z.MED_TYPE AS highLowType,
                z.dfr_fee AS preCost,
            </when>
        </choose>
        a.B16C AS deptCode,
        a.id AS id,
        e.`NAME` AS deptName
        FROM som_drg_grp_info b
        INNER JOIN som_hi_invy_bas_info a
        ON a.ID = b.SETTLE_LIST_ID
        <if test='enableSeAns != null and enableSeAns != "" and enableSeAns == "1"'>
            INNER JOIN som_setl_cas_crsp p
            ON a.k00 = p.k00
        </if>
        LEFT JOIN som_drg_standard c ON b.drg_codg = c.drg_codg
        AND SUBSTR( b.dscg_time, 1, 4 ) = c.STANDARD_YEAR
        AND b.HOSPITAL_ID = c.HOSPITAL_ID
        AND c.ACTIVE_FLAG = 1
        LEFT JOIN som_dept e ON a.B16C = e.`CODE`
        AND a.HOSPITAL_ID = e.HOSPITAL_ID
        <choose>
            <when test="feeStas==1">
                INNER JOIN
                (
                SELECT
                a.rid_idt_codg,
                a.medcas_codg,
                a.adm_time,
                a.dscg_time,
                a.is_in_group,
                a.drg_codg,
                a.DRG_NAME,
                b.MED_TYPE,
                b.sumfee,
                b.setl_sco,
                b.dfr_fee
                FROM som_drg_grp_fbck a
                LEFT JOIN som_drg_pt_val_pay b
                ON a.rid_idt_codg = b.rid_idt_codg
                AND a.HOSPITAL_ID = b.HOSPITAL_ID
                WHERE 1=1
                <if test="begnDate != null and begnDate != '' and expiDate != null and expiDate != ''">
                    AND a.dscg_time BETWEEN #{begnDate,jdbcType=VARCHAR} AND CONCAT(#{expiDate,jdbcType=VARCHAR},'
                    23:59:59')
                </if>
                <if test="inStartTime != null and inStartTime != '' and inEndTime != null and inEndTime != ''">
                    AND a.adm_time BETWEEN #{inStartTime,jdbcType=VARCHAR} AND CONCAT(#{inEndTime,jdbcType=VARCHAR},'
                    23:59:59')
                </if>
                <if test="seStartTime != null and seStartTime != '' and seEndTime != null and seEndTime != ''">
                    AND a.setl_time BETWEEN #{seStartTime,jdbcType=VARCHAR} AND CONCAT(#{seEndTime,jdbcType=VARCHAR},'
                    23:59:59')
                </if>
                <!--
                 WHERE a.dscg_time between #{begnDate,jdbcType=VARCHAR} and concat(#{expiDate,jdbcType=VARCHAR},' 23:59:59')
                 -->
                <include refid="com.my.som.common.mapper.CommonMapper.fbAuth"/>
                ) z
                ON b.PATIENT_ID = z.medcas_codg
                AND SUBSTR(b.adm_time,1,10) = z.adm_time
                AND SUBSTR(b.dscg_time,1,10) = z.dscg_time
            </when>
            <when test="feeStas==0">
                LEFT JOIN som_drg_sco f
                ON a.id = f.SETTLE_LIST_ID
            </when>
        </choose>
        WHERE a.ACTIVE_FLAG = '1'
        <if test="inStartTime != null and inStartTime != '' and inEndTime != null and inEndTime != ''">
            AND a.B12 BETWEEN #{inStartTime} AND CONCAT(#{inEndTime},' 23:59:59')
        </if>
        <if test="begnDate != null and begnDate != '' and expiDate != null and expiDate != ''">
            AND a.b15 between #{begnDate,jdbcType=VARCHAR}
            AND concat(#{expiDate,jdbcType=VARCHAR},' 23:59:59')
        </if>
        <if test="seStartTime != null and seStartTime != '' and seEndTime != null and seEndTime != ''">
            AND a.D37 between #{seStartTime,jdbcType=VARCHAR}
            AND concat(#{seEndTime,jdbcType=VARCHAR},' 23:59:59')
        </if>
        <include refid="queryCommon"/>
        ) g
        <if test="feeStas==0">
            Left JOIN
            (
            SELECT
                SETTLE_LIST_ID,
                price
            FROM
                som_drg_sco
            )h
            on h.SETTLE_LIST_ID = g.id
        </if>
        ) i
        GROUP BY
        <choose>
            <!-- 病组 -->
            <when test='analysisType == "1"'>
                drgCodg,
                drgName,
            </when>

            <!-- 医生 -->
            <when test='analysisType == "2"'>
                drCodg,
                drName,
            </when>

            <!-- 患者 -->
            <when test='analysisType == "3"'>
                id,
            </when>

            <!-- 病种 -->
            <when test='analysisType == "4"'>
                icdCodg,icdName,
            </when>
        </choose>
        <if test='queryType !="2" and dropdownType != 3  and dropdownType != 4'>
            deptCode,
            deptName
        </if>
        <if test='queryType =="2"'>
            drCodg,
            drName
        </if>
        <if test="dropdownType == 3">
            drgCodg,
            drgName
        </if>
        <if test="dropdownType == 4">
            icdCodg,
            icdName
        </if>
        ) k
        <![CDATA[
                    WHERE diff ${gtOrLtSymbol} 0
                ]]>

        ) m
        group by
        <if test='queryType !="2" and dropdownType != 3 and dropdownType != 4'>
            deptCode,
            deptName
        </if>
        <if test='queryType =="2"'>
            drCodg,
            drName
        </if>
        <if test="dropdownType == 3">
            drgCodg,drgName
        </if>
        <if test="dropdownType == 4">
            icdCodg,
            icdName
        </if>
    </select>

    <select id="queryDrgMedError" resultType="com.my.som.vo.dipBusiness.DipInGroupAnalysisVo">
        select c.*,
        error1 + error2 + error3 + error4 + error5 + error6 + error7 + error8 AS errorSummaryNum
        from (
        SELECT
        <if test='queryType !="2" and dropdownType != 3 and dropdownType != 4'>
            a.dscg_caty_codg_inhosp AS priOutHosDeptCode,
            d.`NAME` AS priOutHosDeptName,
        </if>
        <if test='queryType =="2"'>
            a.ipdr_code AS drCodg,
            a.ipdr_name AS drName,
        </if>
        <if test="dropdownType == 3">
            a.drg_codg AS drgCodg,
            a.DRG_NAME AS drgName,
        </if>
        <if test="dropdownType == 4">
            a.main_diag_dise_codg AS icdCodg,
            a.main_diag_dise_name AS icdName,
        </if>
        IFNULL( COUNT( CASE WHEN b.grp_fale_rea LIKE '%字段类型错误%' THEN '1' ELSE NULL END ), 0 ) AS error1,
        IFNULL( COUNT( CASE WHEN b.grp_fale_rea LIKE '%主要诊断不能为空%' THEN '1' ELSE NULL END ), 0 ) AS error2,
        IFNULL( COUNT( CASE WHEN b.grp_fale_rea LIKE '%该诊断不能为主要诊断%' THEN '1' ELSE NULL END ), 0 ) AS error3,
        IFNULL( COUNT( CASE WHEN b.grp_fale_rea LIKE '%性别填写错误%' THEN '1' ELSE NULL END ), 0 ) AS error4,
        IFNULL( COUNT( CASE WHEN b.grp_fale_rea LIKE '%总费用小于5元%' THEN '1' ELSE NULL END ), 0 ) AS error5,
        IFNULL( COUNT( CASE WHEN b.grp_fale_rea LIKE '%住院天数大于60天或者小于0%' THEN '1' ELSE NULL END ), 0 ) AS error6,
        IFNULL( COUNT( CASE WHEN b.grp_fale_rea LIKE '%无分组方案%' THEN '1' ELSE NULL END ), 0 ) AS error7,
        IFNULL( COUNT( CASE WHEN b.grp_fale_rea LIKE '%主要诊断编码不规范%' THEN '1' ELSE NULL END ), 0 ) AS error8,
        IFNULL(count(case when c.compeleteErrorNum != 0 then 1 else null end),0) as compeleteErrorNum,
        IFNULL(count(case when c.logicErrorNum != 0 then 1 else null end),0) as logicErrorNum
        FROM som_drg_grp_info a
        LEFT JOIN som_drg_grp_rcd b
        ON a.SETTLE_LIST_ID = b.SETTLE_LIST_ID
        <if test="enableSeAns != null and enableSeAns != '' and enableSeAns == 1">
            INNER JOIN som_hi_invy_bas_info o
            ON a.SETTLE_LIST_ID = o.ID
            INNER JOIN som_setl_cas_crsp p
            ON o.K00 = p.K00
        </if>
        left join (
        select IFNULL(count(case when err_type in ('CE01','CE02','CE03','CE04') then 1 else null end),0) as
        compeleteErrorNum,
        IFNULL(count(case when err_type in ('LE01','LE02','LE03','LE04','LE05','LE06','LE07') then 1 else null end),0)
        as logicErrorNum,
        SETTLE_LIST_ID
        from
        som_setl_invy_chk_err_rcd a
        group by SETTLE_LIST_ID
        ) c
        on a.SETTLE_LIST_ID = c.SETTLE_LIST_ID
        left join som_dept d
        on a.dscg_caty_codg_inhosp = d.`CODE`
        AND a.HOSPITAL_ID = d.HOSPITAL_ID
        WHERE
        <if test='queryType !="2" and dropdownType != 3 and dropdownType != 4'>
            a.dscg_caty_codg_inhosp IS NOT NULL
            AND a.dscg_caty_codg_inhosp != ''
        </if>
        <if test='queryType =="2"'>
            a.ipdr_code IS NOT NULL
            AND a.ipdr_code != ''
        </if>
        <if test="dropdownType == 3">
            a.drg_codg IS NOT NULL AND a.drg_codg != ''
        </if>
        <if test="dropdownType == 4">
            a.main_diag_dise_codg IS NOT NULL AND a.main_diag_dise_codg != ''
        </if>

        <if test="inStartTime != null and inStartTime != '' and inEndTime != null and inEndTime != ''">
            AND a.adm_time BETWEEN #{inStartTime} AND CONCAT(#{inEndTime},' 23:59:59')
        </if>
        <if test="begnDate != null and begnDate != '' and expiDate != null and expiDate != ''">
            AND a.dscg_time between #{begnDate,jdbcType=VARCHAR}
            AND concat(#{expiDate,jdbcType=VARCHAR},' 23:59:59')
        </if>
        <if test="seStartTime != null and seStartTime != '' and seEndTime != null and seEndTime != ''">
            AND a.setl_end_time between #{seStartTime,jdbcType=VARCHAR}
            AND concat(#{seEndTime,jdbcType=VARCHAR},' 23:59:59')
        </if>
        <!-- 传入条件-科室 -->
        <if test="deptCode !=null and deptCode !=''">
            AND a.dscg_caty_codg_inhosp = #{deptCode,jdbcType=VARCHAR}
        </if>
        <!-- 传入条件-医院id -->
        <if test="hospitalId !=null and hospitalId !=''">
            AND a.HOSPITAL_ID = #{hospitalId,jdbcType=VARCHAR}
        </if>
        <!-- 传入条件-医生 -->
        <if test="drCodg !=null and drCodg !=''">
            AND a.ipdr_code = #{drCodg,jdbcType=VARCHAR}
        </if>
        <include refid="queryBusCommon"/>
        <if test="drgCodg != null and drgCodg != ''">
            AND a.drg_codg = #{drgCodg}
        </if>
        group by
        <if test='queryType !="2" and dropdownType != 3 and dropdownType != 4'>
            a.dscg_caty_codg_inhosp,
            d.`NAME`
        </if>
        <if test='queryType =="2"'>
            a.ipdr_code,
            a.ipdr_name
        </if>
        <if test="dropdownType == 3">
            a.drg_codg,
            a.DRG_NAME
        </if>
        <if test="dropdownType == 4">
            a.main_diag_dise_codg,
            a.main_diag_dise_name
        </if>
        ) c
    </select>

    <select id="queryDrgDropdown" resultType="com.my.som.vo.newBusiness.NewBusinessCommonVo">
        SELECT
        <choose>
            <when test='dropdownType == "1"'>
                a.B16C AS value,
                b.`name` AS label
            </when>
            <when test='dropdownType == "2"'>
                a.B25C AS value,
                a.B25N AS label
            </when>
            <when test='dropdownType == "3"'>
                distinct
                c.drg_codg AS value,
                c.DRG_NAME AS label
            </when>
            <when test='dropdownType == "4"'>
                distinct
                c.main_diag_dise_codg AS value,
                c.main_diag_dise_name AS label
            </when>
        </choose>
        FROM som_hi_invy_bas_info a
        <if test='enableSeAns != null and enableSeAns != "" and enableSeAns == "1"'>
            INNER JOIN som_setl_cas_crsp p
            ON a.k00 = p.k00
        </if>
        LEFT JOIN som_dept b
        ON a.b16c = b.`code`
        AND a.HOSPITAL_ID = b.HOSPITAL_ID
        <if test='dropdownType == "3" or dropdownType == "4"'>
            LEFT JOIN som_drg_grp_info c
            ON a.ID = c.SETTLE_LIST_ID
        </if>
        where 1 = 1
        <if test="begnDate != null and begnDate != '' and expiDate != null and expiDate != ''">
            AND a.b15 between #{begnDate,jdbcType=VARCHAR} and concat(#{expiDate,jdbcType=VARCHAR},' 23:59:59')
        </if>
        <if test="inStartTime != null and inStartTime != '' and inEndTime != null and inEndTime != ''">
            AND a.b12 between #{inStartTime} and concat(#{inEndTime},' 23:59:59')
        </if>
        <if test="seStartTime != null and seStartTime != '' and seEndTime != null and seEndTime != ''">
            AND a.D37 between #{seStartTime} and concat(#{seEndTime},' 23:59:59')
        </if>
        <if test='dropdownType == "2"'>
            and a.B25C IS NOT NULL
            and a.B25C !=''
        </if>
        <if test='dropdownType == "3"'>
            <if test="drgCodg !=null and drgCodg !=''">
                AND c.drg_codg = #{drgCodg,jdbcType=VARCHAR}
            </if>
        </if>
        <include refid="queryCommon"/>
        <if test='dropdownType == "3" or dropdownType == "4"'>
            AND c.grp_stas = 1
        </if>
        <choose>
            <when test='dropdownType == "1"'>
                GROUP BY a.B16C, b.`name`
            </when>
            <when test='dropdownType == "2"'>
                GROUP BY a.B25C,a.B25N
            </when>
        </choose>
    </select>

    <!-- 查询通用 -->
    <sql id="queryCommon">
        <!-- 传入条件-科室 -->
        <if test="deptCode !=null and deptCode !=''">
            AND a.B16C = #{deptCode,jdbcType=VARCHAR}
        </if>
        <!-- 传入条件-医院id -->
        <if test="hospitalId !=null and hospitalId !=''">
            AND a.HOSPITAL_ID = #{hospitalId,jdbcType=VARCHAR}
        </if>

        <!-- 传入条件-医生 -->
        <if test="drCodg !=null and drCodg !=''">
            AND a.B25C = #{drCodg,jdbcType=VARCHAR}
        </if>
    </sql>

    <!-- 查询通用 -->
    <sql id="queryBusCommon">
        <!-- 传入条件-科室 -->
        <if test="deptCode !=null and deptCode !=''">
            AND a.dscg_caty_codg_inhosp = #{deptCode,jdbcType=VARCHAR}
        </if>

        <!-- 传入条件-医生 -->
        <if test="drCodg !=null and drCodg !=''">
            AND a.ipdr_code = #{drCodg,jdbcType=VARCHAR}
        </if>

        <!-- 传入条件-医院id -->
        <if test="hospitalId !=null and hospitalId !=''">
            AND a.HOSPITAL_ID = #{hospitalId,jdbcType=VARCHAR}
        </if>
    </sql>

    <select id="queryContrastData" resultType="com.my.som.vo.newBusiness.NewBusinessAnalysisVo">
        SELECT
        a.deptCode,
        a.drCodg,
        a.drName,
        ROUND(IFNULL(SUM(drugfee)/SUM(inHosTotalCost) * 100,0),2) AS drugRatio,
        ROUND(IFNULL(SUM(mcsFee)/SUM(inHosTotalCost) * 100,0),2) AS consumeRatio,
        ROUND(IFNULL(SUM(weight)/SUM(number),0),2) AS cmi,
        IFNULL(SUM(D)/SUM(a.number),0) AS standardDays,
        ROUND(IFNULL(SUM(E)/SUM(a.number),0),2) AS standardFee
        FROM
        (
        SELECT
        a.B25C AS drCodg,
        a.B25N AS drName,
        a.B16C AS deptCode,
        SUM(IFNULL(a.B20,0)) AS inHosDays,
        SUM(IFNULL(a.D01,0)) AS inHosTotalCost,
        AVG(IFNULL(a.B20,0)),
        AVG(IFNULL(a.D01,0)),
        b.dip_codg AS diseaseCode,
        b.DIP_NAME AS diseaseName,
        AVG(IFNULL(CONVERT(AES_DECRYPT(UNHEX(c.dip_standard_avg_fee_same_lv
        ),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) USING utf8),0)) AS standardFee,
        AVG(IFNULL(CONVERT(AES_DECRYPT(UNHEX(c.dip_standard_ipt_days_same_lv
        ),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) USING utf8),0)) AS standardDays,
        SUM(IFNULL(b.dip_wt,0)) AS weight,
        COUNT(1) AS number,
        SUM(IFNULL(b.drugfee,0)) AS drugfee,
        SUM(IFNULL(b.mcs_fee,0)) AS mcsFee,
        AVG(IFNULL(a.B20,0))/AVG(IFNULL(CONVERT(AES_DECRYPT(UNHEX(c.dip_standard_ipt_days_same_lv
        ),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) USING utf8),0))*COUNT(1) AS D,
        AVG(IFNULL(a.D01,0))/AVG(IFNULL(CONVERT(AES_DECRYPT(UNHEX(c.dip_standard_avg_fee_same_lv
        ),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) USING utf8),0))*COUNT(1) AS E
        FROM som_hi_invy_bas_info a
        LEFT JOIN som_dip_grp_info b
        ON a.ID = b.SETTLE_LIST_ID
        LEFT JOIN som_dip_standard c
        ON b.HOSPITAL_ID = c.HOSPITAL_ID
        AND b.dip_codg = c.dip_codg
        AND b.is_used_asst_list = c.is_used_asst_list
        AND b.asst_list_age_grp = c.asst_list_age_grp
        AND b.asst_list_dise_sev_deg = c.asst_list_dise_sev_deg
        AND b.asst_list_tmor_sev_deg = c.asst_list_tmor_sev_deg
        AND SUBSTR(b.dscg_time,1,4) = c.STANDARD_YEAR
        WHERE a.ACTIVE_FLAG = '1'
        AND b.grp_stas = '1'
        <if test="begnDate != null and begnDate !='' and expiDate != null and expiDate != ''">
            AND a.B15 BETWEEN #{begnDate,jdbcType=VARCHAR} AND CONCAT(#{expiDate,jdbcType=VARCHAR},' 23:59:59')
        </if>
        <if test="hospitalId != null and hospitalId != ''">
            AND a.HOSPITAL_ID = #{hospitalId,jdbcType=VARCHAR}
        </if>
        GROUP BY a.B16C,
        a.B25C,
        a.B25N,
        b.dip_codg,
        b.DIP_NAME
        ) a
        <where>
            <if test="deptCode != null and deptCode != ''">
                AND a.deptCode = #{deptCode,jdbcType=VARCHAR}
            </if>
            <if test='dimension == "4" and diseaseCode != null and diseaseCode != ""'>
                AND a.diseaseCode = #{diseaseCode,jdbcType=VARCHAR}
            </if>
        </where>
        GROUP BY a.deptCode,a.drCodg,a.drName
    </select>

    <select id="queryDeptDropdown" resultType="com.my.som.vo.newBusiness.NewBusinessCommonVo">
        SELECT a.B16C AS value,
        c.`NAME` AS label
        FROM som_hi_invy_bas_info a
        <if test="enableSeAns != null and enableSeAns != '' and enableSeAns == 1">
            INNER JOIN som_setl_cas_crsp p
            ON a.K00 = p.K00
        </if>
        LEFT JOIN som_dip_grp_info b
        ON a.ID = b.SETTLE_LIST_ID
        LEFT JOIN som_dept c
        ON a.B16C = c.`CODE`
        <where>
            <if test="begnDate != null and begnDate != '' and expiDate != null and expiDate != ''">
                and a.B15 between #{begnDate,jdbcType=VARCHAR} and concat(#{expiDate,jdbcType=VARCHAR},' 23:59:59')
            </if>
            <if test="inStartTime != null and inStartTime != '' and inEndTime != null and inEndTime != ''">
                AND a.B12 BETWEEN #{inStartTime} AND CONCAT(#{inEndTime},' 23:59:59')
            </if>
            <if test="seStartTime != null and seStartTime != '' and seEndTime != null and seEndTime != ''">
                AND a.D37 BETWEEN #{seStartTime} AND CONCAT(#{seEndTime},' 23:59:59')
            </if>
            <if test="hospitalId != null and hospitalId != ''">
                AND a.HOSPITAL_ID = #{hospitalId,jdbcType=VARCHAR}
            </if>
        </where>
        GROUP BY a.B16C,
        c.`NAME`
    </select>

    <select id="queryDoctorDropdown" resultType="com.my.som.vo.newBusiness.NewBusinessCommonVo">
        SELECT a.B25C AS value,
        a.B25N AS label
        FROM som_hi_invy_bas_info a
        <where>
            <if test="begnDate != null and begnDate != '' and expiDate != null and expiDate != ''">
                AND a.B15 BETWEEN #{begnDate,jdbcType=VARCHAR} AND CONCAT(#{expiDate,jdbcType=VARCHAR},' 23:59:59')
            </if>
            <if test="hospitalId != null and hospitalId != ''">
                AND a.HOSPITAL_ID = #{hospitalId,jdbcType=VARCHAR}
            </if>
        </where>
        GROUP BY a.B25C,
        a.B25N
    </select>

    <select id="queryDiseaseDropdown" resultType="com.my.som.vo.newBusiness.NewBusinessCommonVo">
        SELECT
        <choose>
            <when test="diseaseGroupType == 1">
                b.dip_codg AS value,
                b.DIP_NAME AS label,
                IFNULL(MAX(CONVERT(AES_DECRYPT(UNHEX(c.dip_standard_avg_fee_same_lv
                ),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) USING utf8)),0) AS standardFee,
                IFNULL(MAX(CONVERT(AES_DECRYPT(UNHEX(c.dip_standard_ipt_days_same_lv
                ),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) USING utf8)),0) AS standardDays,
                IFNULL(MAX(CONVERT(AES_DECRYPT(UNHEX(c.drug_ratio),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY})
                USING utf8)),0) AS standardDrugRatio,
                IFNULL(MAX(CONVERT(AES_DECRYPT(UNHEX(c.consum_ratio
                ),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) USING utf8)),0) AS standardConsumeRatio
            </when>
            <when test="diseaseGroupType == 2">
                b.drg_codg AS value,
                b.DRG_NAME AS label
            </when>
        </choose>
        FROM som_hi_invy_bas_info a
        <if test="enableSeAns != null and enableSeAns != '' and enableSeAns == 1">
            INNER JOIN som_setl_cas_crsp p
            ON a.K00 = p.K00
        </if>
        <choose>
            <when test="diseaseGroupType == 1">
                LEFT JOIN som_dip_grp_info b
                ON a.ID = b.SETTLE_LIST_ID
                LEFT JOIN som_dip_standard c
                ON SUBSTR(b.dscg_time,1,4) = c.STANDARD_YEAR
                AND b.HOSPITAL_ID = c.HOSPITAL_ID
                AND b.dip_codg = c.dip_codg
--                 AND b.is_used_asst_list = c.is_used_asst_list
--                 AND b.asst_list_age_grp = c.asst_list_age_grp
--                 AND b.asst_list_dise_sev_deg = c.asst_list_dise_sev_deg
--                 AND b.asst_list_tmor_sev_deg = c.asst_list_tmor_sev_deg
            </when>
            <when test="diseaseGroupType == 2">
                LEFT JOIN som_drg_grp_info b
                ON a.ID = b.SETTLE_LIST_ID
            </when>
        </choose>
        <where>
            AND a.ACTIVE_FLAG = '1'
            AND b.grp_stas = '1'
            <if test="begnDate != null and begnDate != '' and expiDate != null and expiDate != ''">
                and a.B15 between #{begnDate,jdbcType=VARCHAR} and concat(#{expiDate,jdbcType=VARCHAR},' 23:59:59')
            </if>
            <if test="inStartTime != null and inStartTime != '' and inEndTime != null and inEndTime != ''">
                AND a.B12 BETWEEN #{inStartTime} AND CONCAT(#{inEndTime},' 23:59:59')
            </if>
            <if test="seStartTime != null and seStartTime != '' and seEndTime != null and seEndTime != ''">
                AND a.D37 BETWEEN #{seStartTime} AND CONCAT(#{seEndTime},' 23:59:59')
            </if>
            <if test="hospitalId != null and hospitalId != ''">
                AND a.HOSPITAL_ID = #{hospitalId,jdbcType=VARCHAR}
            </if>
            <if test="deptCode != null and deptCode != ''">
                AND a.B16C = #{deptCode,jdbcType=VARCHAR}
            </if>
            <if test="drCodg != null and drCodg != ''">
                AND a.B25C = #{drCodg,jdbcType=VARCHAR}
            </if>
            <choose>
                <when test="diseaseGroupType == 1">
                    AND b.dip_codg IS NOT NULL
                </when>
                <when test="diseaseGroupType == 2">
                    AND b.drg_codg IS NOT NULL
                </when>
            </choose>
        </where>
        GROUP BY
        <choose>
            <when test="diseaseGroupType == 1">
                b.dip_codg,
                b.DIP_NAME
            </when>
            <when test="diseaseGroupType == 2">
                b.drg_codg,
                b.DRG_NAME
            </when>
        </choose>

    </select>

    <select id="queryPatientContrastData" resultType="com.my.som.vo.newBusiness.NewBusinessAnalysisVo">
        SELECT a.drCodg,
        a.name AS drName,
        ROUND(IFNULL(drugfee/inHosTotalCost * 100,0),2) AS drugRatio,
        ROUND(IFNULL(mcsFee/inHosTotalCost * 100,0),2) AS consumeRatio,
        ROUND(IFNULL(weight,0),2) AS cmi,
        IFNULL(D,0) AS standardDays,
        ROUND(IFNULL(E,0),2) AS standardFee
        FROM
        (
        SELECT a.B25C AS drCodg,
        a.B25N AS drName,
        a.A11 AS name,
        IFNULL(a.B20,0) AS inHosDays,
        IFNULL(a.D01,0) AS inHosTotalCost,
        IFNULL(a.B20,0),
        IFNULL(a.D01,0),
        b.dip_codg AS diseaseCode,
        b.DIP_NAME AS diseaseName,
        IFNULL(CONVERT(AES_DECRYPT(UNHEX(c.dip_standard_avg_fee_same_lv
        ),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) USING utf8),0) AS standardFee,
        IFNULL(CONVERT(AES_DECRYPT(UNHEX(c.dip_standard_ipt_days_same_lv
        ),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) USING utf8),0) AS standardDays,
        IFNULL(b.dip_wt,0) AS weight,
        IFNULL(b.drugfee,0) AS drugfee,
        IFNULL(b.mcs_fee,0) AS mcsFee,
        IFNULL(a.B20,0)/IFNULL(CONVERT(AES_DECRYPT(UNHEX(c.dip_standard_ipt_days_same_lv
        ),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) USING utf8),0) AS D,
        IFNULL(a.D01,0)/IFNULL(CONVERT(AES_DECRYPT(UNHEX(c.dip_standard_avg_fee_same_lv
        ),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) USING utf8),0) AS E
        FROM som_hi_invy_bas_info a
        LEFT JOIN som_dip_grp_info b
        ON a.ID = b.SETTLE_LIST_ID
        LEFT JOIN som_dip_standard c
        ON b.HOSPITAL_ID = c.HOSPITAL_ID
        AND b.dip_codg = c.dip_codg
        AND b.is_used_asst_list = c.is_used_asst_list
        AND b.asst_list_age_grp = c.asst_list_age_grp
        AND b.asst_list_dise_sev_deg = c.asst_list_dise_sev_deg
        AND b.asst_list_tmor_sev_deg = c.asst_list_tmor_sev_deg
        AND SUBSTR(b.dscg_time,1,4) = c.STANDARD_YEAR
        WHERE a.ACTIVE_FLAG = '1'
        AND b.grp_stas = '1'
        <if test="begnDate != null and begnDate !='' and expiDate != null and expiDate != ''">
            AND a.B15 BETWEEN #{begnDate,jdbcType=VARCHAR} AND CONCAT(#{expiDate,jdbcType=VARCHAR},' 23:59:59')
        </if>
        <if test="hospitalId != null and hospitalId != ''">
            AND a.HOSPITAL_ID = #{hospitalId,jdbcType=VARCHAR}
        </if>
        ) a
        <where>
            <if test="drCodg != null and drCodg != ''">
                AND a.drCodg = #{drCodg,jdbcType=VARCHAR}
            </if>
            <if test='dimension == "4" and diseaseCode != null and diseaseCode != ""'>
                AND a.diseaseCode = #{diseaseCode,jdbcType=VARCHAR}
            </if>
        </where>
    </select>

    <select id="queryCommonDropdown">
        SELECT
        <choose>
            <when test="dropdownType == 2">
                a.B16C AS value,
                b.`NAME` AS label
            </when>
            <when test="dropdownType == 3">
                a.B25C AS value,
                a.B25N AS label
            </when>
            <when test="dropdownType == 4">
                <if test="diseaseGroupType == 1">
                    b.dip_codg AS value,
                    b.DIP_NAME AS label
                </if>
            </when>
        </choose>
        FROM som_hi_invy_bas_info a
        <if test="dropdownType == 2">
            LEFT JOIN som_dept b
            ON a.B16C = b.`CODE`
            AND a.HOSPITAL_ID = b.HOSPITAL_ID
        </if>
        <if test="dropdownType == 4">
            <if test="diseaseGroupType == 1">
                LEFT JOIN som_dip_grp_info b
                ON a.ID = b.SETTLE_LIST_ID
            </if>
        </if>
        <where>
            AND a.ACTIVE_FLAG = '1'
            <if test="hospitalId != null and hospitalId != ''">
                AND a.HOSPITAL_ID = #{hospitalId,jdbcType=VARCHAR}
            </if>
            <if test="begnDate != null and begnDate != '' and expiDate != null and expiDate != ''">
                AND a.B15 BETWEEN #{begnDate} AND CONCAT(#{expiDate},' 23:59:59')
            </if>
        </where>
        GROUP BY
        <choose>
            <when test="dropdownType == 2">
                a.B16C,
                b.`NAME`
            </when>
            <when test="dropdownType == 3">
                a.B25C,
                a.B25N
            </when>
            <when test="dropdownType == 4">
                <if test="diseaseGroupType == 1">
                    b.dip_codg,
                    b.DIP_NAME
                </if>
            </when>
        </choose>
    </select>

    <select id="queryDoctorContrastData" resultType="com.my.som.vo.newBusiness.NewBusinessAnalysisVo">
        SELECT a.B25C AS drCodg,
        a.B25N AS drName,
        a.avgInHosCost,
        a.avgInHosDays,
        a.drugRatio,
        a.consumeRatio,
        b.costIndex,
        b.timeIndex,
        b.cmi
        FROM
        (
        SELECT a.B25C,
        a.B25N,
        ROUND(AVG(IFNULL(a.B20,0)),2) AS avgInHosDays,
        ROUND(AVG(IFNULL(a.D01,0)),2) AS avgInHosCost,
        SUM(IFNULL(b.drugfee,0)),
        SUM(IFNULL(b.mcs_fee,0)),
        ROUND(IFNULL(AVG(IFNULL(IFNULL(b.drugfee,0)/IFNULL(a.D01,0),0)),0),2) AS drugRatio,
        ROUND(IFNULL(AVG(IFNULL(IFNULL(b.mcs_fee,0)/IFNULL(a.D01,0),0)),0),2) AS consumeRatio
        FROM som_hi_invy_bas_info a
        <if test="enableSeAns != null and enableSeAns != '' and enableSeAns == 1">
            INNER JOIN som_setl_cas_crsp p
            ON a.K00 = p.K00
        </if>
        <choose>
            <when test="diseaseGroupType == 1">
                LEFT JOIN som_dip_grp_info b
                ON a.ID = b.SETTLE_LIST_ID
            </when>
            <when test="diseaseGroupType == 2">
                LEFT JOIN som_drg_grp_info b
                ON a.ID = b.SETTLE_LIST_ID
            </when>
        </choose>
        WHERE a.ACTIVE_FLAG = "1"
        <if test="categories != null and categories != ''">
            <choose>
                <when test="categories == 1">
                    and a.A54 in ('1', '01', '310')
                </when>
                <when test="categories == 2">
                    and a.A54 in ('2', '02', '390')
                </when>
                <when test="categories == 9">
                    and a.A54 not in ('1', '01', '310', '2', '02', '390')
                </when>
            </choose>
        </if>
        <if test="hospitalId != null and hospitalId != ''">
            AND a.HOSPITAL_ID = #{hospitalId,jdbcType=VARCHAR}
        </if>
        <if test="begnDate != null and begnDate != '' and expiDate != null and expiDate != ''">
            and a.B15 between #{begnDate,jdbcType=VARCHAR} and concat(#{expiDate,jdbcType=VARCHAR},' 23:59:59')
        </if>
        <if test="inStartTime != null and inStartTime != '' and inEndTime != null and inEndTime != ''">
            AND a.B12 BETWEEN #{inStartTime} AND CONCAT(#{inEndTime},' 23:59:59')
        </if>
        <if test="seStartTime != null and seStartTime != '' and seEndTime != null and seEndTime != ''">
            AND a.D37 BETWEEN #{seStartTime} AND CONCAT(#{seEndTime},' 23:59:59')
        </if>
        <if test="deptCode != null and deptCode != ''">
            AND a.B16C = #{deptCode,jdbcType=VARCHAR}
        </if>
        GROUP BY a.B25C,a.B25N
        ) a
        LEFT JOIN
        (
        SELECT a.B25C,
        a.B25N,
        ROUND(IFNULL(SUM(D)/SUM(a.N),0),2) AS timeIndex,
        ROUND(IFNULL(SUM(E)/SUM(a.N),0),0) AS costIndex,
        ROUND(IFNULL(SUM(a.totalWeight)/SUM(N),0),2) AS cmi
        FROM
        (
        SELECT a.B25C,
        a.B25N,
        COUNT(1) AS N,
        <choose>
            <when test="diseaseGroupType == 1">
                AVG(IFNULL(a.B20,0))/AVG(IFNULL(CONVERT(AES_DECRYPT(UNHEX(c.dip_standard_ipt_days_same_lv
                ),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) USING utf8),0)) * COUNT(1) AS D,
                AVG(IFNULL(a.D01,0))/AVG(IFNULL(CONVERT(AES_DECRYPT(UNHEX(c.dip_standard_avg_fee_same_lv
                ),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) USING utf8),0)) * COUNT(1) AS E,
                AVG(IFNULL(b.dip_wt,0)) * COUNT(1) AS totalWeight
            </when>
            <when test="diseaseGroupType == 2">
                AVG(IFNULL(a.B20,0))/AVG(IFNULL(CONVERT(AES_DECRYPT(UNHEX(c.standard_ave_hosp_day
                ),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) USING utf8),0)) * COUNT(1) AS D,
                AVG(IFNULL(a.D01,0))/AVG(IFNULL(CONVERT(AES_DECRYPT(UNHEX(c.standard_avg_fee
                ),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) USING utf8),0)) * COUNT(1) AS E,
                AVG(IFNULL(b.drg_wt,0)) * COUNT(1) AS totalWeight
            </when>
        </choose>
        FROM som_hi_invy_bas_info a
        <choose>
            <when test="diseaseGroupType == 1">
                LEFT JOIN som_dip_grp_info b
                ON a.ID = b.SETTLE_LIST_ID
                LEFT JOIN som_dip_standard c
                ON b.HOSPITAL_ID = c.HOSPITAL_ID
                AND b.dip_codg = c.dip_codg
                AND b.is_used_asst_list = c.is_used_asst_list
                AND b.asst_list_age_grp = c.asst_list_age_grp
                AND b.asst_list_dise_sev_deg = c.asst_list_dise_sev_deg
                AND b.asst_list_tmor_sev_deg = c.asst_list_tmor_sev_deg
                AND SUBSTR(b.dscg_time,1,4) = c.STANDARD_YEAR
            </when>
            <when test="diseaseGroupType == 2">
                LEFT JOIN som_drg_grp_info b
                ON a.ID = b.SETTLE_LIST_ID
                LEFT JOIN som_drg_standard c
                ON b.HOSPITAL_ID = c.HOSPITAL_ID
                AND b.drg_codg = c.drg_codg
                AND SUBSTR(b.dscg_time,1,4) = c.STANDARD_YEAR
            </when>
        </choose>
        WHERE a.ACTIVE_FLAG = "1"
        AND b.grp_stas = "1"
        <if test="hospitalId != null and hospitalId != ''">
            AND a.HOSPITAL_ID = #{hospitalId,jdbcType=VARCHAR}
        </if>
        <if test="begnDate != null and begnDate != '' and expiDate != null and expiDate != ''">
            and a.B15 between #{begnDate,jdbcType=VARCHAR} and concat(#{expiDate,jdbcType=VARCHAR},' 23:59:59')
        </if>
        <if test="inStartTime != null and inStartTime != '' and inEndTime != null and inEndTime != ''">
            AND a.B12 BETWEEN #{inStartTime} AND CONCAT(#{inEndTime},' 23:59:59')
        </if>
        <if test="seStartTime != null and seStartTime != '' and seEndTime != null and seEndTime != ''">
            AND a.D37 BETWEEN #{seStartTime} AND CONCAT(#{seEndTime},' 23:59:59')
        </if>
        <if test="deptCode != null and deptCode != ''">
            AND a.B16C = #{deptCode,jdbcType=VARCHAR}
        </if>
        GROUP BY a.B25C,
        a.B25N,
        <choose>
            <when test="diseaseGroupType == 1">
                b.dip_codg
            </when>
            <when test="diseaseGroupType == 2">
                b.drg_codg
            </when>
        </choose>
        ) a
        GROUP BY a.B25C,a.B25N
        ) b ON a.B25C = b.B25C AND a.B25N = b.B25N
    </select>

    <select id="selectPatientContrastData" resultType="com.my.som.vo.newBusiness.NewBusinessAnalysisVo">
        SELECT a.ID AS id,
        a.A11 AS name,
        a.A48 AS bah,
        a.B20 AS inHosDays,
        a.D01 AS sumfee,
        a.B25C AS drCodg,
        a.B25N AS drName,
        CASE WHEN a.C03C IS NOT NULL THEN a.C03C ELSE a.C37C END AS mainDiagCode,
        <choose>
            <when test="diseaseGroupType == 1">
                IFNULL(CONVERT(AES_DECRYPT(UNHEX(c.dip_standard_ipt_days_same_lv),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY})
                USING utf8),0) AS standardDays,
                IFNULL(CONVERT(AES_DECRYPT(UNHEX(c.dip_standard_avg_fee_same_lv),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY})
                USING utf8),0) AS standardFee,
                ROUND(IFNULL(IFNULL(a.B20,0)/IFNULL(CONVERT(AES_DECRYPT(UNHEX(c.dip_standard_ipt_days_same_lv),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY})
                USING utf8),0),0),2) AS timeIndex,
                ROUND(IFNULL(IFNULL(a.D01,0)/IFNULL(CONVERT(AES_DECRYPT(UNHEX(c.dip_standard_avg_fee_same_lv),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY})
                USING utf8),0),0),2) AS costIndex,
            </when>
            <when test="diseaseGroupType == 2">
                IFNULL(CONVERT(AES_DECRYPT(UNHEX(c.standard_ave_hosp_day),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY})
                USING utf8),0) AS standardDays,
                IFNULL(CONVERT(AES_DECRYPT(UNHEX(c.standard_avg_fee),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY})
                USING utf8),0) AS standardFee,
                ROUND(IFNULL(IFNULL(a.B20,0)/IFNULL(CONVERT(AES_DECRYPT(UNHEX(c.standard_ave_hosp_day),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY})
                USING utf8),0),0),2) AS timeIndex,
                ROUND(IFNULL(IFNULL(a.D01,0)/IFNULL(CONVERT(AES_DECRYPT(UNHEX(c.standard_avg_fee),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY})
                USING utf8),0),0),2) AS costIndex,
            </when>
        </choose>
        ROUND(IFNULL(IFNULL(b.drugfee,0)/IFNULL(a.D01,0) * 100,0),2) AS drugRatio,
        ROUND(IFNULL(IFNULL(b.mcs_fee,0)/IFNULL(a.D01,0) * 100,0),2) AS consumeRatio,
        IFNULL(CONVERT(AES_DECRYPT(UNHEX(c.drug_ratio),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) USING
        utf8),0) AS standardDrugRatio,
        IFNULL(CONVERT(AES_DECRYPT(UNHEX(c.consum_ratio),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY})
        USING utf8),0) AS standardConsumeRatio
        FROM som_hi_invy_bas_info a
        <if test="enableSeAns != null and enableSeAns != '' and enableSeAns == 1">
            INNER JOIN som_setl_cas_crsp p
            ON a.K00 = p.K00
        </if>
        <choose>
            <when test="diseaseGroupType == 1">
                LEFT JOIN som_dip_grp_info b
                ON a.ID = b.SETTLE_LIST_ID
                LEFT JOIN som_dip_standard c
                ON b.HOSPITAL_ID = c.HOSPITAL_ID
                AND b.dip_codg = c.dip_codg
--                 AND b.is_used_asst_list = c.is_used_asst_list
--                 AND b.asst_list_age_grp = c.asst_list_age_grp
--                 AND b.asst_list_dise_sev_deg = c.asst_list_dise_sev_deg
--                 AND b.asst_list_tmor_sev_deg = c.asst_list_tmor_sev_deg
                AND SUBSTR(b.dscg_time,1,4) = c.STANDARD_YEAR
            </when>
            <when test="diseaseGroupType == 2">
                LEFT JOIN som_drg_grp_info b
                ON a.ID = b.SETTLE_LIST_ID
                LEFT JOIN som_drg_standard c
                ON b.HOSPITAL_ID = c.HOSPITAL_ID
                AND b.drg_codg = c.drg_codg
                AND SUBSTR(b.dscg_time,1,4) = c.STANDARD_YEAR
                AND a.insuplc_admdvs = c.insuplc_admdvs
            </when>
        </choose>
        <where>
            AND a.ACTIVE_FLAG = '1'
            <if test="hospitalId != null and hospitalId != ''">
                AND a.HOSPITAL_ID = #{hospitalId,jdbcType=VARCHAR}
            </if>
            <if test="begnDate != null and begnDate != '' and expiDate != null and expiDate != ''">
                and a.B15 between #{begnDate,jdbcType=VARCHAR} and concat(#{expiDate,jdbcType=VARCHAR},' 23:59:59')
            </if>
            <if test="inStartTime != null and inStartTime != '' and inEndTime != null and inEndTime != ''">
                AND a.B12 BETWEEN #{inStartTime} AND CONCAT(#{inEndTime},' 23:59:59')
            </if>
            <if test="seStartTime != null and seStartTime != '' and seEndTime != null and seEndTime != ''">
                AND a.D37 BETWEEN #{seStartTime} AND CONCAT(#{seEndTime},' 23:59:59')
            </if>
            <choose>
                <when test="diseaseGroupType == 1">
                    AND b.dip_codg = #{dipCodg,jdbcType=VARCHAR}
                </when>
                <when test="diseaseGroupType == 2">
                    AND b.drg_codg = #{dipCodg,jdbcType=VARCHAR}
                </when>
            </choose>
            <if test="drCodg != null and drCodg != ''">
                AND a.B25C = #{drCodg,jdbcType=VARCHAR}
            </if>
        </where>
    </select>
<!--    查询病组象限图数据-->
        <select id="selectSickGroupQuadrant" resultType="com.my.som.vo.newBusiness.dept.NewBusinessDeptVo">
        SELECT
        a.dipCodg AS dipCodg,
        a.dipName AS dipName,
        count(*) AS medicalTotalNum,
        IFNULL( sum( a.DipWt ), 0 ) AS totalWeight,
        IFNULL( round( sum( a.DipWt )/ count( CASE WHEN a.isInGroup = 1 THEN 1 ELSE NULL END ), 2 ), 0 ) AS cmi,
        IFNULL( round( sum( a.sumfee ), 2 ), 0 ) AS sumfee,
            IFNULL(  round( sum( a.ycCost ), 2 ), 0  ) AS forecastAmount,
            IFNULL(  round(  sum( a.profitloss), 2),0) AS forecastAmountDiff
        FROM
        (
        SELECT
        a.A11,
        b.dip_codg AS dipCodg,
        b.DIP_NAME AS dipName,
        b.dip_wt AS DipWt,
        b.grp_stas AS isInGroup,
        a.D01 AS sumfee,
        d.totl_sco AS totlSco,
        a.A46C AS medPayWay,
            d.forecast_fee AS ycCost,
            d.price,
            d.profitloss AS profitloss
        FROM
        som_hi_invy_bas_info a
        LEFT JOIN som_dip_grp_info b ON a.ID = b.SETTLE_LIST_ID
        LEFT JOIN som_dept c ON a.B16C = c.`CODE`
        LEFT JOIN som_dip_sco d ON a.ID = d.SETTLE_LIST_ID
        where 1=1
        <if test="seStartTime != null and seStartTime != '' and seEndTime != null and seEndTime !=''">
            AND a.D37 BETWEEN #{seStartTime,jdbcType=VARCHAR} AND CONCAT(#{seEndTime,jdbcType=VARCHAR},' 23:59:59')
        </if>
        <if test="begnDate != null and begnDate != '' and expiDate != null and expiDate != ''">
            AND a.B15 BETWEEN #{begnDate,jdbcType=VARCHAR} AND CONCAT(#{expiDate,jdbcType=VARCHAR},' 23:59:59')
        </if>
        <if test="hospitalId != null and hospitalId != ''">
            AND a.A01 = #{hospitalId,jdbcType=VARCHAR}
        </if>
        <if test="deptCode != null and deptCode != ''">
            AND a.B16C = #{deptCode,jdbcType=VARCHAR}
        </if>
        <if test="inStartTime != null and inStartTime != '' and inEndTime != null and inEndTime != ''">
            AND a.B12 BETWEEN #{inStartTime,jdbcType=VARCHAR} AND CONCAT(#{inEndTime,jdbcType=VARCHAR},' 23:59:59')
        </if>
        ) a
        GROUP BY
        a.dipCodg,a.dipName
    </select>
</mapper>
