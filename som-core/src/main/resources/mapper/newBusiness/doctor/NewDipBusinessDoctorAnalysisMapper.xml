<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.som.mapper.newBusiness.doctor.NewDipBusinessDoctorAnalysisMapper">
    <!-- 查询医生Kpi -->
    <select id="queryDoctorKpiData" resultType="com.my.som.vo.newBusiness.doctor.NewBusinessDoctorVo">
        SELECT a.* FROM(SELECT a.ipdr_code AS drCodg,
               a.ipdr_name AS drName,
               IFNULL(COUNT(1),0) AS medicalTotalNum,
               IFNULL(ROUND(AVG(a.act_ipt),2),0) AS avgInHosDays,
               IFNULL(ROUND(SUM(b.dip_wt),2),0) AS doctorTotalWeight,
               a.dscg_caty_codg_inhosp AS deptCode,
               c.`NAME` AS deptName,

                    <choose>
                        <when test="feeStas == 0">
                            ROUND(SUM(CASE WHEN a.grp_stas = '1' THEN a.dip_wt ELSE NULL END)/NULLIF (COUNT(CASE WHEN a.grp_stas = '1' THEN 1 ELSE NULL END),0),2) as cmi,
                            IFNULL(ROUND(AVG(a.ipt_sumfee),2),0) AS avgInHosCost,
                            IFNULL(count(DISTINCT(case when a.grp_stas = '1' then concat(a.dip_codg,a.asst_list_age_grp,a.asst_list_dise_sev_deg,a.asst_list_tmor_sev_deg) else null end)),0) as groupNum,
                            IFNULL(ROUND(NULLIF(SUM(CASE WHEN a.grp_stas = 1 THEN 1 ELSE 0 END),0)/COUNT(1)*100,2),0) AS inGroupRate,
                            COUNT(CASE WHEN a.grp_stas = 1 THEN 1 ELSE NULL END) AS drgInGroupMedcasVal,
                            COUNT(CASE WHEN a.grp_stas != 1 THEN 1 ELSE NULL END) AS notGroupNum,
                            IFNULL(ROUND(SUM(CASE WHEN a.grp_stas = 1 THEN a.act_ipt ELSE NULL END / NULLIF(CONVERT(AES_DECRYPT(UNHEX(b.dip_standard_ipt_days_same_lv),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) USING utf8),0)) / NULLIF(COUNT(CASE WHEN a.grp_stas = 1 THEN 1 ELSE NULL END),0), 2),0) AS timeIndex,
                            IFNULL(ROUND(SUM(CASE WHEN a.grp_stas = 1 THEN a.ipt_sumfee ELSE NULL END / NULLIF(CONVERT(AES_DECRYPT(UNHEX(b.dip_standard_avg_fee_same_lv),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) USING utf8),0)) / NULLIF(COUNT(CASE WHEN a.grp_stas = 1 THEN 1 ELSE NULL END),0), 2),0) AS costIndex,
                            IFNULL(ROUND(SUM(a.drugfee) / NULLIF(SUM(a.ipt_sumfee),0) * 100,2),0) AS drugRatio,
                            IFNULL(ROUND(SUM(a.mcs_fee) / NULLIF(SUM(a.ipt_sumfee),0) * 100,2),0) AS consumeRatio
                        </when>
                        <when test="feeStas == 1">
                            IFNULL(ROUND(AVG(e.ipt_sumfee),2),0) AS avgInHosCost,
                            IFNULL(COUNT(DISTINCT(CASE WHEN e.is_in_group = 1 THEN e.dip_codg ELSE NULL END)),0) AS groupNum,
                            IFNULL(ROUND(NULLIF(SUM(CASE WHEN e.is_in_group = 1 THEN 1 ELSE 0 END),0)/COUNT(1)*100,2),0) AS inGroupRate,
                            COUNT(CASE WHEN e.is_in_group = 1 THEN 1 ELSE NULL END) AS drgInGroupMedcasVal,
                            COUNT(CASE WHEN e.is_in_group != 1 THEN 1 ELSE NULL END) AS notGroupNum,
                            IFNULL(ROUND(SUM(CASE WHEN e.is_in_group = 1 THEN a.act_ipt ELSE NULL END / NULLIF(CONVERT(AES_DECRYPT(UNHEX(b.dip_standard_ipt_days_same_lv),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) USING utf8),0)) / NULLIF(COUNT(CASE WHEN e.is_in_group = 1 THEN 1 ELSE NULL END),0), 2),0) AS timeIndex,
                            IFNULL(ROUND(SUM(CASE WHEN e.is_in_group = 1 THEN e.ipt_sumfee ELSE NULL END / NULLIF(CONVERT(AES_DECRYPT(UNHEX(b.dip_standard_avg_fee_same_lv),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) USING utf8),0)) / NULLIF(COUNT(CASE WHEN e.is_in_group = 1 THEN 1 ELSE NULL END),0), 2),0) AS costIndex,
                            IFNULL(ROUND(SUM(a.drugfee) / NULLIF(SUM(e.ipt_sumfee),0) * 100,2),0) AS drugRatio,
                            IFNULL(ROUND(SUM(a.mcs_fee) / NULLIF(SUM(e.ipt_sumfee),0) * 100,2),0) AS consumeRatio
                        </when>
                    </choose>
        FROM som_dip_grp_info a
        LEFT JOIN som_hi_invy_bas_info f
        ON a.SETTLE_LIST_ID = f.ID
        <if test="enableSeAns != null and enableSeAns != '' and enableSeAns == 1">
            INNER JOIN som_setl_cas_crsp p
            ON f.K00 = p.K00
        </if>
        LEFT JOIN som_dip_standard b
            ON a.dip_codg = b.dip_codg
           AND SUBSTR(a.dscg_time,1,4) = b.STANDARD_YEAR
            AND a.is_used_asst_list = b.is_used_asst_list
            AND a.asst_list_age_grp = b.asst_list_age_grp
            AND a.asst_list_dise_sev_deg = b.asst_list_dise_sev_deg
            AND a.asst_list_tmor_sev_deg = b.asst_list_tmor_sev_deg
           AND a.HOSPITAL_ID = b.HOSPITAL_ID
        LEFT JOIN som_dept c
        ON a.dscg_caty_codg_inhosp = c.`CODE`
        AND a.HOSPITAL_ID = c.HOSPITAL_ID
            <choose>
                <when test="feeStas == 1">
                    INNER JOIN
                    (
                    SELECT
                    a.dip_codg,
                    a.DIP_NAME,
                    a.rid_idt_codg,
                    a.medcas_codg,
                    a.adm_time,
                    a.dscg_time,
                    a.is_in_group,
                    b.sumfee AS ipt_sumfee,
                    b.INSURED_TYPE,
                    b.MED_TYPE AS dise_type,
                    b.setl_pt_val,
                    b.dfr_fee AS ycCost,
                    b.MED_TYPE,
                    b.setl_sco,
                    a.used_asst_list,
                    a.asst_list_age_grp,
                    a.asst_list_dise_sev_deg,
                    a.asst_list_tmor_sev_deg
                    FROM som_dip_grp_fbck a
                    LEFT JOIN som_fund_dfr_fbck b
                    ON a.rid_idt_codg = b.rid_idt_codg
                    AND a.HOSPITAL_ID = b.HOSPITAL_ID
                    WHERE 1=1
                    <if test="begnDate != null and begnDate != '' and expiDate != null and expiDate != ''">
                        AND a.dscg_time BETWEEN #{begnDate,jdbcType=VARCHAR} AND concat(#{expiDate,jdbcType=VARCHAR},' 23:59:59')
                    </if>
                    <if test="inStartTime != null and inStartTime != '' and inEndTime != null and inEndTime != ''">
                        AND a.adm_time BETWEEN #{inStartTime} AND CONCAT(#{inEndTime},' 23:59:59')
                    </if>
                    <if test="seStartTime!=null and seStartTime!='' and seEndTime!=null and seEndTime!=''">
                        AND a.setl_time BETWEEN #{seStartTime} and CONCAT(#{seEndTime},' 23:59:59')
                    </if>
                    <include refid="com.my.som.common.mapper.CommonMapper.fbAuth" />
                    ) e
                    <!--ON SUBSTR(a.PATIENT_ID,1,10) = e.medcas_codg-->
                    ON a.PATIENT_ID = e.medcas_codg
                    AND SUBSTR(a.adm_time,1,10) = e.adm_time
                    AND SUBSTR(a.dscg_time,1,10) = e.dscg_time
                </when>
            </choose>
        WHERE 1=1
        <if test="categories != null and categories != ''">
            <choose>
                <when test="categories == 1">
                    and f.A54 in ('1', '01', '310')
                </when>
                <when test="categories == 2">
                    and f.A54 in ('2', '02', '390')
                </when>
                <when test="categories == 9">
                    and f.A54 not in ('1', '01', '310', '2', '02', '390')
                </when>
            </choose>
        </if>

        <if test="begnDate != null and begnDate != '' and expiDate != null and expiDate != ''">
            AND a.dscg_time BETWEEN #{begnDate,jdbcType=VARCHAR} AND concat(#{expiDate,jdbcType=VARCHAR},' 23:59:59')
        </if>
        <if test="inStartTime != null and inStartTime != '' and inEndTime != null and inEndTime != ''">
            AND a.adm_time BETWEEN #{inStartTime} AND CONCAT(#{inEndTime},' 23:59:59')
        </if>
        <if test="seStartTime!=null and seStartTime!='' and seEndTime!=null and seEndTime!=''">
            AND a.setl_end_time BETWEEN #{seStartTime} and CONCAT(#{seEndTime},' 23:59:59')
        </if>
        <if test='setlway != null and setlway != "" and setlway == "1"'>
            AND f.setlway = #{setlway,jdbcType=VARCHAR}
        </if>
        AND a.ipdr_code is NOT NULL
        AND a.ipdr_code !=''
        <if test="dipCodg != null and dipCodg != ''">
            <if test="feeStas == 0">
                AND a.dip_codg = #{dipCodg,jdbcType=VARCHAR}
            </if>
            <if test="feeStas == 1">
                AND e.dip_codg = #{dipCodg,jdbcType=VARCHAR}
            </if>
        </if>
        <!-- 通用查询条件 -->
        <include refid="com.my.som.mapper.newBusiness.common.NewBusinessCommonAnalysisMapper.queryBusCommon" />
        GROUP BY a.ipdr_code,a.ipdr_name, a.dscg_caty_codg_inhosp, c.`NAME`) a
        ORDER BY inGroupRate DESC
    </select>

    <select id="queryDoctorForecastData" resultType="com.my.som.vo.newBusiness.doctor.NewBusinessDoctorVo">
        SELECT a.* FROM ( SELECT a.ipdr_code AS drCodg,
               a.ipdr_name AS drName,
               IFNULL(COUNT(1),0) AS medicalTotalNum,
               a.deptCode,
               a.deptName,

                 <if test="feeStas == 0">
                     IFNULL(COUNT(CASE WHEN a.dise_type = 4 THEN 1 ELSE NULL END) - COUNT(CASE WHEN b.grp_stas = '0' THEN 1 ELSE NULL END),0) AS nonBenchmarkNum,
                     IFNULL(ROUND(IFNULL(SUM(a.profitloss),0) - -SUM(IFNULL(a.pre_hosp_examfee,0)),2),0) AS forecastAmountDiff,
                     IFNULL(ROUND(SUM(a.ipt_sumfee) / SUM(a.ycCost),2),0) AS oeVal,
                 </if>
                 <if test="feeStas == 1">
                     IFNULL(COUNT(CASE WHEN a.dise_type = 4 THEN 1 ELSE NULL END) - COUNT(CASE WHEN a.is_in_group = '0' THEN 1 ELSE NULL END),0) AS nonBenchmarkNum,
                     IFNULL(ROUND(SUM(a.FB_INHOS_TOTAL_COST),2),0) AS fbTotalCost,
                     IFNULL(ROUND(IFNULL(SUM(a.ycCost),0) - IFNULL(SUM(a.FB_INHOS_TOTAL_COST),0),2),0) AS forecastAmountDiff,
                     IFNULL(ROUND(SUM(a.FB_INHOS_TOTAL_COST) / SUM(a.ycCost),2),0) AS oeVal,
                 </if>
               IFNULL(COUNT(CASE WHEN a.dise_type = 3 THEN 1 ELSE NULL END),0) AS normalNum,
               IFNULL(COUNT(CASE WHEN a.dise_type = 1 THEN 1 ELSE NULL END),0) AS ultrahighNum,
               IFNULL(ROUND(COUNT(CASE WHEN a.dise_type = 1 THEN 1 ELSE NULL END) / COUNT(1) * 100,2),0) AS ultrahighRate,
               IFNULL(COUNT(CASE WHEN a.dise_type = 2 THEN 1 ELSE NULL END),0) AS ultraLowNum,
               IFNULL(ROUND(COUNT(CASE WHEN a.dise_type = 2 THEN 1 ELSE NULL END) / COUNT(1) * 100,2),0) AS ultraLowRate,
               IFNULL(ROUND(SUM(a.ipt_sumfee),2),0) AS sumfee,
               IFNULL(ROUND(SUM(a.ycCost),2),0) AS forecastAmount
        FROM
             (
                 SELECT a.ipdr_code,
                        a.ipdr_name,
                        a.SETTLE_LIST_ID,
                        a.dscg_time,
                        a.drugfee,
                        a.mcs_fee,
                        a.ipt_sumfee,
                        a.dscg_caty_codg_inhosp AS deptCode,
                        c.`NAME` AS deptName,
                        a.pre_hosp_examfee,
                     <choose>
                         <when test="feeStas == 0">
                             b.dise_type,
                             b.ycCost,
                             b.profitloss
                         </when>
                         <when test="feeStas == 1">
                             e.FB_INHOS_TOTAL_COST,
                             e.dise_type,
                             e.is_in_group,
                             e.ycCost
                         </when>
                     </choose>
                 FROM som_dip_grp_info a
                 LEFT JOIN som_hi_invy_bas_info f
                 ON a.SETTLE_LIST_ID = f.ID
                <if test="enableSeAns != null and enableSeAns != '' and enableSeAns == 1">
                    INNER JOIN som_setl_cas_crsp p
                    ON f.K00 = p.K00
                </if>
                 LEFT JOIN
                     (
                         SELECT b.SETTLE_LIST_ID,
                                b.dise_type,
                                 b.price,
                                 b.profitloss AS profitloss,
                                 b.forecast_fee AS ycCost,
                                 b.sumfee AS sumfee
                                 FROM som_dip_sco b
                     ) b
                 ON a.SETTLE_LIST_ID = b.SETTLE_LIST_ID
                 LEFT JOIN som_dept c
                 ON a.dscg_caty_codg_inhosp = c.`CODE`
                 AND a.HOSPITAL_ID = c.HOSPITAL_ID
                    <choose>
                        <when test="feeStas == 1">
                            INNER JOIN
                            (
                            SELECT
                            a.dip_codg,
                            a.DIP_NAME,
                            a.rid_idt_codg,
                            a.medcas_codg,
                            a.adm_time,
                            a.dscg_time,
                            a.is_in_group,
                            b.sumfee AS FB_INHOS_TOTAL_COST,
                            b.INSURED_TYPE,
                            b.MED_TYPE AS dise_type,
                            b.setl_pt_val,
                            b.dfr_fee AS ycCost,
                            b.MED_TYPE,
                            b.setl_sco,
                            a.used_asst_list,
                            a.asst_list_age_grp,
                            a.asst_list_dise_sev_deg,
                            a.asst_list_tmor_sev_deg
                            FROM som_dip_grp_fbck a
                            LEFT JOIN som_fund_dfr_fbck b
                            ON a.rid_idt_codg = b.rid_idt_codg
                            AND a.HOSPITAL_ID = b.HOSPITAL_ID
                            WHERE 1=1
                            <if test="begnDate != null and begnDate != '' and expiDate != null and expiDate != ''">
                                AND a.dscg_time BETWEEN #{begnDate,jdbcType=VARCHAR} AND concat(#{expiDate,jdbcType=VARCHAR},' 23:59:59')
                            </if>
                            <if test="inStartTime != null and inStartTime != '' and inEndTime != null and inEndTime != ''">
                                AND a.adm_time BETWEEN #{inStartTime} AND CONCAT(#{inEndTime},' 23:59:59')
                            </if>
                            <if test="seStartTime!=null and seStartTime!='' and seEndTime!=null and seEndTime!=''">
                                AND a.setl_time BETWEEN #{seStartTime} and CONCAT(#{seEndTime},' 23:59:59')
                            </if>
                            <include refid="com.my.som.common.mapper.CommonMapper.fbAuth" />
                            ) e
                            <!--ON SUBSTR(a.PATIENT_ID,1,10) = e.medcas_codg-->
                            ON a.PATIENT_ID = e.medcas_codg
                            AND SUBSTR(a.adm_time,1,10) = e.adm_time
                            AND SUBSTR(a.dscg_time,1,10) = e.dscg_time
                        </when>
                    </choose>
                 WHERE 1=1
        <if test="categories != null and categories != ''">
            <choose>
                <when test="categories == 1">
                    and f.A54 in ('1', '01', '310')
                </when>
                <when test="categories == 2">
                    and f.A54 in ('2', '02', '390')
                </when>
                <when test="categories == 9">
                    and f.A54 not in ('1', '01', '310', '2', '02', '390')
                </when>
            </choose>
        </if>
                    <if test="begnDate != null and begnDate != '' and expiDate != null and expiDate != ''">
                        AND a.dscg_time BETWEEN #{begnDate,jdbcType=VARCHAR} AND concat(#{expiDate,jdbcType=VARCHAR},' 23:59:59')
                    </if>
                    <if test="inStartTime != null and inStartTime != '' and inEndTime != null and inEndTime != ''">
                        AND a.adm_time BETWEEN #{inStartTime} AND CONCAT(#{inEndTime},' 23:59:59')
                    </if>
                    <if test="seStartTime!=null and seStartTime!='' and seEndTime!=null and seEndTime!=''">
                        AND a.setl_end_time BETWEEN #{seStartTime} and CONCAT(#{seEndTime},' 23:59:59')
                    </if>
                    <if test='setlway != null and setlway != "" and setlway == "1"'>
                        AND f.setlway = #{setlway,jdbcType=VARCHAR}
                    </if>
                 AND a.ipdr_code is NOT NULL
                 AND a.ipdr_code !=''
                 <if test="dipCodg != null and dipCodg != ''">
                     AND a.dip_codg = #{dipCodg}
                 </if>
                <!-- 通用查询条件 -->
                <include refid="com.my.som.mapper.newBusiness.common.NewBusinessCommonAnalysisMapper.queryBusCommon" />
             ) a
        LEFT JOIN som_dip_grp_rcd b
        ON a.SETTLE_LIST_ID = b.SETTLE_LIST_ID
        GROUP BY a.ipdr_code,a.ipdr_name, a.deptCode, a.deptName) a
        <where>
            <if test="isLossType != null and isLossType != ''">
                <choose>
                    <when test="isLossType == 1">
                        <![CDATA[
                            a.forecastAmountDiff < 0
                        ]]>
                    </when>
                    <when test="isLossType == 0">
                        <![CDATA[
                            a.forecastAmountDiff >= 0
                        ]]>
                    </when>
                </choose>
            </if>
        </where>
    </select>
</mapper>
