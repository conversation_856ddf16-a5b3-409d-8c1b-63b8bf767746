<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.som.mapper.newBusiness.doctor.NewDrgBusinessDoctorAnalysisMapper">
    <!-- 查询医生Kpi -->
    <select id="queryDrgDoctorKpiData" resultType="com.my.som.vo.newBusiness.doctor.NewBusinessDoctorVo">
        SELECT a.* FROM(SELECT a.ipdr_code AS drCodg,
        a.ipdr_name AS drName,
        IFNULL(COUNT(1),0) AS medicalTotalNum,
        IFNULL(ROUND(AVG(a.act_ipt),2),0) AS avgInHosDays,
        IFNULL(ROUND(SUM(b.drg_wt),2),0) AS doctorTotalWeight,
        IFNULL(SUM(psn_selfpay),0) AS psnSelfpay,
        IFNULL(SUM(psn_ownpay),0) AS psnOwnpay,
        IFNULL(SUM(acct_pay),0) AS acctPay,
        IFNULL(SUM(psn_cashpay),0) AS psnCashpay,
        IFNULL(SUM(fp.370100_amount),0) AS Amount370100,
        IFNULL(ROUND(SUM(e.ycCost),2),0) AS forecastAmount,
        ROUND(IFNULL(SUM(b.drg_wt),0),2) AS totalWeight,
        ROUND(IFNULL(AVG(b.drg_wt),0),2) AS avgWeight,
        <!--                IFNULL(ROUND(IFNULL(SUM(e.ycCost),0) - IFNULL(SUM(a.ipt_sumfee),0),2),0) AS forecastAmountDiff,-->
        ROUND( IFNULL( SUM( e.profitloss ), 0 ) , 2 )   AS forecastAmountDiff,
        IFNULL(ROUND(SUM(a.drugfee) ,2),0) AS drugFee,
        IFNULL(ROUND(SUM(a.mcs_fee) ,2),0) AS consumeFee,
        --         e.price,
        IFNULL(ROUND(SUM(e.totlSco) ,2),0) AS totlSco,
        <choose>
            <when test="feeStas == 0">
                IFNULL(round(SUM(CASE WHEN a.grp_stas = '1' THEN a.drg_wt ELSE NULL END)/
                NULLIF (COUNT(CASE WHEN a.grp_stas = '1' THEN 1 ELSE NULL END),0),2),0)   AS cmi,
                IFNULL(ROUND(AVG(a.ipt_sumfee),2),0) AS avgInHosCost,
                IFNULL(COUNT(DISTINCT(CASE WHEN a.grp_stas = 1 THEN a.drg_codg ELSE NULL END)),0) AS groupNum,
                IFNULL(ROUND(NULLIF(SUM(CASE WHEN a.grp_stas = 1 THEN 1 ELSE 0 END),0)/COUNT(1)*100,2),0) AS inGroupRate,
                COUNT(CASE WHEN a.grp_stas = 1 THEN 1 ELSE NULL END) AS drgInGroupMedcasVal,
                COUNT(CASE WHEN a.grp_stas != 1 THEN 1 ELSE NULL END) AS notGroupNum,
                IFNULL(ROUND(SUM(CASE WHEN a.grp_stas = 1 THEN a.act_ipt ELSE NULL END / NULLIF(CONVERT(AES_DECRYPT(UNHEX(b.standard_ave_hosp_day),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) USING utf8),0)) / NULLIF(COUNT(CASE WHEN a.grp_stas = 1 THEN 1 ELSE NULL END),0), 2),0) AS timeIndex,
                IFNULL(ROUND(SUM(CASE WHEN a.grp_stas = 1 THEN a.ipt_sumfee ELSE NULL END / NULLIF(CONVERT(AES_DECRYPT(UNHEX(b.standard_avg_fee),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) USING utf8),0)) / NULLIF(COUNT(CASE WHEN a.grp_stas = 1 THEN 1 ELSE NULL END),0), 2),0) AS costIndex,
                IFNULL(ROUND(SUM(a.drugfee) / NULLIF(SUM(a.ipt_sumfee),0) * 100,2),0) AS drugRatio,
                IFNULL(ROUND(SUM(a.mcs_fee) / NULLIF(SUM(a.ipt_sumfee),0) * 100,2),0) AS consumeRatio,
                IFNULL(ROUND(SUM(a.ipt_sumfee) ,2),0) AS sumfee
            </when>
            <when test="feeStas == 1">
                IFNULL(ROUND(AVG(e.ipt_sumfee),2),0) AS avgInHosCost,
                IFNULL(COUNT(DISTINCT(CASE WHEN e.is_in_group = 1 THEN e.drg_codg ELSE NULL END)),0) AS groupNum,
                IFNULL(ROUND(NULLIF(SUM(CASE WHEN e.is_in_group = 1 THEN 1 ELSE 0 END),0)/COUNT(1)*100,2),0) AS inGroupRate,
                COUNT(CASE WHEN e.is_in_group = 1 THEN 1 ELSE NULL END) AS drgInGroupMedcasVal,
                COUNT(CASE WHEN e.is_in_group != 1 THEN 1 ELSE NULL END) AS notGroupNum,
                IFNULL(ROUND(SUM(CASE WHEN e.is_in_group = 1 THEN a.act_ipt ELSE NULL END / NULLIF(CONVERT(AES_DECRYPT(UNHEX(b.standard_ave_hosp_day),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) USING utf8),0)) / NULLIF(COUNT(CASE WHEN e.is_in_group = 1 THEN 1 ELSE NULL END),0), 2),0) AS timeIndex,
                IFNULL(ROUND(SUM(CASE WHEN e.is_in_group = 1 THEN e.ipt_sumfee ELSE NULL END / NULLIF(CONVERT(AES_DECRYPT(UNHEX(b.standard_avg_fee),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) USING utf8),0)) / NULLIF(COUNT(CASE WHEN e.is_in_group = 1 THEN 1 ELSE NULL END),0), 2),0) AS costIndex,
                IFNULL(ROUND(SUM(a.drugfee) / NULLIF(SUM(e.ipt_sumfee),0) * 100,2),0) AS drugRatio,
                IFNULL(ROUND(SUM(a.mcs_fee) / NULLIF(SUM(e.ipt_sumfee),0) * 100,2),0) AS consumeRatio,
                IFNULL(ROUND(SUM(a.ipt_sumfee) ,2),0) AS sumfee
            </when>
        </choose>
        FROM som_drg_grp_info a
        INNER JOIN som_hi_invy_bas_info f
        ON a.SETTLE_LIST_ID = f.ID
        LEFT JOIN som_drg_standard b
        ON a.drg_codg = b.drg_codg
        AND SUBSTR(ifnull(f.d37, a.dscg_time),1,4) = b.STANDARD_YEAR
        AND a.HOSPITAL_ID = b.HOSPITAL_ID
        and f.insuplc_admdvs = b.insuplc_admdvs
        LEFT JOIN (
        SELECT
        hi_setl_invy_id,
        SUM(CASE WHEN fund_pay_type  = '370100' THEN fund_payamt  ELSE 0 END) AS `370100_amount`
        FROM som_fund_pay
        GROUP BY hi_setl_invy_id
        ) fp ON a.SETTLE_LIST_ID = fp.hi_setl_invy_id
        LEFT JOIN
        (
        SELECT a.SETTLE_LIST_ID,
        a.dise_type,
        a.forecast_fee AS ycCost,
        a.profitloss AS profitloss,
        a.sumfee,
        --         a.price,
        a.totl_sco as totlSco
        FROM som_drg_sco a
        ) e
        ON a.SETTLE_LIST_ID = e.SETTLE_LIST_ID
        <if test="enableSeAns != null and enableSeAns != '' and enableSeAns == 1">

            INNER JOIN som_setl_cas_crsp p
            ON f.K00 = p.K00
        </if>

        <choose>
            <when test="feeStas == 1">
                INNER JOIN
                (
                SELECT
                a.drg_codg,
                a.DRG_NAME,
                a.rid_idt_codg,
                a.HOSPITAL_ID,
                a.medcas_codg,
                a.adm_time,
                a.dscg_time,
                a.is_in_group,
                b.sumfee AS ipt_sumfee,
                b.INSURED_TYPE,
                b.MED_TYPE AS dise_type,
                b.setl_pt_val,
                b.dfr_fee AS ycCost,
                b.MED_TYPE,
                b.setl_sco
                FROM som_drg_grp_fbck a
                LEFT JOIN som_drg_pt_val_pay b
                ON a.rid_idt_codg = b.rid_idt_codg
                AND a.HOSPITAL_ID = b.HOSPITAL_ID
                <!--
                     <![CDATA[
                                     WHERE a.dscg_time >= #{begnDate} AND a.dscg_time <= #{expiDate}
                                     ]]>
                 -->
                <where>
                    <if test="inStartTime!=null and inStartTime!='' and inEndTime!=null and inEndTime!=''">
                        AND a.adm_time BETWEEN #{inStartTime,jdbcType=VARCHAR} and CONCAT(#{inEndTime,jdbcType=VARCHAR},' 23:59:59')
                    </if>
                    <if test="begnDate!=null and begnDate!='' and expiDate!=null and expiDate!=''">
                        AND a.dscg_time BETWEEN #{begnDate,jdbcType=VARCHAR} and CONCAT(#{expiDate,jdbcType=VARCHAR},' 23:59:59')
                    </if>
                    <if test="seStartTime!=null and seStartTime!='' and seEndTime!=null and seEndTime!=''">
                        AND a.setl_time BETWEEN #{seStartTime,jdbcType=VARCHAR} and CONCAT(#{seStartTime,jdbcType=VARCHAR},' 23:59:59')
                    </if>
                </where>
                ) e
                <!--ON SUBSTR(a.PATIENT_ID,1,10) = e.medcas_codg-->
                ON a.PATIENT_ID = e.medcas_codg
                AND SUBSTR(a.adm_time,1,10) = e.adm_time
                AND SUBSTR(a.dscg_time,1,10) = e.dscg_time
                AND a.HOSPITAL_ID = e.HOSPITAL_ID
            </when>
        </choose>


        WHERE 1 = 1
        <!--
            a.dscg_time BETWEEN #{begnDate,jdbcType=VARCHAR} AND CONCAT(#{expiDate,jdbcType=VARCHAR},' 23:59:59')
        <if test="inStartTime != null and inStartTime != '' and inEndTime != null and inEndTime != ''">
            AND a.adm_time BETWEEN #{inStartTime} AND CONCAT(#{inEndTime},' 23:59:59')
        </if>
         -->
        <if test="inStartTime!=null and inStartTime!='' and inEndTime!=null and inEndTime!=''">
            AND a.adm_time BETWEEN #{inStartTime,jdbcType=VARCHAR} and CONCAT(#{inEndTime,jdbcType=VARCHAR},' 23:59:59')
        </if>
        <if test="begnDate!=null and begnDate!='' and expiDate!=null and expiDate!=''">
            AND a.dscg_time BETWEEN #{begnDate,jdbcType=VARCHAR} and CONCAT(#{expiDate,jdbcType=VARCHAR},' 23:59:59')
        </if>
        <if test="seStartTime!=null and seStartTime!='' and seEndTime!=null and seEndTime!=''">
            AND a.setl_end_time BETWEEN #{seStartTime,jdbcType=VARCHAR} and CONCAT(#{seEndTime,jdbcType=VARCHAR},' 23:59:59')
        </if>
        AND a.ipdr_code is NOT NULL
        AND a.ipdr_code !=''
        <if test="drgCodg != null and drgCodg != ''">
            <if test="feeStas == 0">
                AND a.drg_codg = #{drgCodg,jdbcType=VARCHAR}
            </if>
            <if test="feeStas == 1">
                AND e.drg_codg = #{drgCodg,jdbcType=VARCHAR}
            </if>
        </if>
        <!-- 通用查询条件 -->
        <include refid="com.my.som.mapper.newBusiness.common.NewBusinessCommonAnalysisMapper.queryBusCommon" />
        GROUP BY a.ipdr_code,a.ipdr_name
        --         ,e.price
        ) a
        ORDER BY inGroupRate DESC
    </select>

    <select id="queryDrgDoctorForecastData" resultType="com.my.som.vo.newBusiness.doctor.NewBusinessDoctorVo">
        SELECT a.* FROM ( SELECT a.ipdr_code AS drCodg,
        a.ipdr_name AS drName,
        IFNULL(COUNT(1),0) AS medicalTotalNum,

        <if test="feeStas == 0">
            IFNULL(COUNT(CASE WHEN a.dise_type = 4 THEN 1 ELSE NULL END) - COUNT(CASE WHEN b.grp_stas = '0' THEN 1 ELSE NULL END),0) AS nonBenchmarkNum,
        </if>
        <if test="feeStas == 1">
            IFNULL(COUNT(CASE WHEN a.dise_type = 4 THEN 1 ELSE NULL END) - COUNT(CASE WHEN a.is_in_group = '0' THEN 1 ELSE NULL END),0) AS nonBenchmarkNum,
        </if>
        IFNULL(COUNT(CASE WHEN a.dise_type = 3 THEN 1 ELSE NULL END),0) AS normalNum,
        IFNULL(COUNT(CASE WHEN a.dise_type = 1 THEN 1 ELSE NULL END),0) AS ultrahighNum,
        IFNULL(ROUND(COUNT(CASE WHEN a.dise_type = 1 THEN 1 ELSE NULL END) / COUNT(1) * 100,2),0) AS ultrahighRate,
        IFNULL(COUNT(CASE WHEN a.dise_type = 2 THEN 1 ELSE NULL END),0) AS ultraLowNum,
        IFNULL(ROUND(COUNT(CASE WHEN a.dise_type = 2 THEN 1 ELSE NULL END) / COUNT(1) * 100,2),0) AS ultraLowRate,
        IFNULL(ROUND(COUNT(CASE WHEN a.dise_type not in (1,2,3) THEN 1 ELSE NULL END) / COUNT(1) * 100,2),0) AS ultraOtherNum,
        IFNULL(ROUND(SUM(a.ipt_sumfee),2),0) AS sumfee,
        IFNULL(ROUND(SUM(a.ycCost),2),0) AS forecastAmount,
        IFNULL(ROUND(IFNULL(SUM(a.profitloss),0) ,2),0) AS forecastAmountDiff,
        IFNULL(ROUND(SUM(a.ipt_sumfee) / SUM(a.ycCost),2),0) AS oeVal
        FROM
        (
        SELECT a.ipdr_code,
        a.ipdr_name,
        a.SETTLE_LIST_ID,
        a.dscg_time,
        a.drugfee,
        a.mcs_fee,
        <choose>
            <when test="feeStas == 0">
                a.ipt_sumfee,
                b.dise_type,
                b.ycCost,
                b.profitloss
            </when>
            <when test="feeStas == 1">
                e.ipt_sumfee,
                e.dise_type,
                e.is_in_group,
                e.ycCost
            </when>
        </choose>
        FROM som_drg_grp_info a
        LEFT JOIN
        (
        SELECT a.SETTLE_LIST_ID,
        a.dise_type,

        a.forecast_fee AS ycCost,
        a.profitloss AS profitloss,
        a.sumfee
        FROM som_drg_sco a
        ) b
        ON a.SETTLE_LIST_ID = b.SETTLE_LIST_ID
        <if test="enableSeAns != null and enableSeAns != '' and enableSeAns == 1">
            INNER JOIN som_hi_invy_bas_info f
            ON a.SETTLE_LIST_ID = f.ID
            INNER JOIN som_setl_cas_crsp p
            ON f.K00 = p.K00
        </if>

        <choose>
            <when test="feeStas == 1">
                INNER JOIN
                (
                SELECT
                a.drg_codg,
                a.DRG_NAME,
                a.rid_idt_codg,
                a.HOSPITAL_ID,
                a.medcas_codg,
                a.adm_time,
                a.dscg_time,
                a.is_in_group,
                b.sumfee AS ipt_sumfee,
                b.INSURED_TYPE,
                b.MED_TYPE AS dise_type,
                b.setl_pt_val,
                b.dfr_fee AS ycCost,
                b.MED_TYPE,
                b.setl_sco
                FROM som_drg_grp_fbck a
                LEFT JOIN som_drg_pt_val_pay b
                ON a.rid_idt_codg = b.rid_idt_codg
                AND a.HOSPITAL_ID = b.HOSPITAL_ID
                <!--<![CDATA[
                             WHERE a.dscg_time >= #{begnDate} AND a.dscg_time <= #{expiDate}
                             ]]>-->
                <where>
                    <if test="inStartTime!=null and inStartTime!='' and inEndTime!=null and inEndTime!=''">
                        AND a.adm_time BETWEEN #{inStartTime,jdbcType=VARCHAR} and
                        CONCAT(#{inEndTime,jdbcType=VARCHAR},' 23:59:59')
                    </if>
                    <if test="begnDate!=null and begnDate!='' and expiDate!=null and expiDate!=''">
                        AND a.dscg_time BETWEEN #{begnDate,jdbcType=VARCHAR} and CONCAT(#{expiDate,jdbcType=VARCHAR},'
                        23:59:59')
                    </if>
                    <if test="seStartTime!=null and seStartTime!='' and seEndTime!=null and seEndTime!=''">
                        AND a.setl_time BETWEEN #{seStartTime,jdbcType=VARCHAR} and
                        CONCAT(#{seStartTime,jdbcType=VARCHAR},' 23:59:59')
                    </if>
                </where>
                <include refid="com.my.som.common.mapper.CommonMapper.fbAuth"/>
                ) e
                <!--ON SUBSTR(a.PATIENT_ID,1,10) = e.medcas_codg-->
                ON a.PATIENT_ID = e.medcas_codg
                AND SUBSTR(a.adm_time,1,10) = e.adm_time
                AND SUBSTR(a.dscg_time,1,10) = e.dscg_time
                AND a.HOSPITAL_ID = e.HOSPITAL_ID
            </when>
        </choose>


        WHERE 1 = 1
        <!--a.dscg_time BETWEEN #{begnDate,jdbcType=VARCHAR} AND CONCAT(#{expiDate,jdbcType=VARCHAR},' 23:59:59') &lt;!&ndash; 开始时间，结束时间 &ndash;&gt;
       <if test="inStartTime != null and inStartTime != '' and inEndTime != null and inEndTime != ''">
           AND a.adm_time BETWEEN #{inStartTime} AND CONCAT(#{inEndTime},' 23:59:59')
       </if>-->
        <if test="inStartTime!=null and inStartTime!='' and inEndTime!=null and inEndTime!=''">
            AND a.adm_time BETWEEN #{inStartTime,jdbcType=VARCHAR} and CONCAT(#{inEndTime,jdbcType=VARCHAR},' 23:59:59')
        </if>
        <if test="begnDate!=null and begnDate!='' and expiDate!=null and expiDate!=''">
            AND a.dscg_time BETWEEN #{begnDate,jdbcType=VARCHAR} and CONCAT(#{expiDate,jdbcType=VARCHAR},' 23:59:59')
        </if>
        <if test="seStartTime!=null and seStartTime!='' and seEndTime!=null and seEndTime!=''">
            AND a.setl_end_time BETWEEN #{seStartTime,jdbcType=VARCHAR} and CONCAT(#{seEndTime,jdbcType=VARCHAR},'
            23:59:59')
        </if>
        AND a.ipdr_code is NOT NULL
        AND a.ipdr_code !=''
        <if test="drgCodg != null and drgCodg != ''">
            AND a.drg_codg = #{drgCodg}
        </if>
        <if test="icdCodg != null and icdCodg != ''">
            AND a.main_diag_dise_codg = #{icdCodg}
        </if>
        <!-- 通用查询条件 -->
        <include refid="com.my.som.mapper.newBusiness.common.NewBusinessCommonAnalysisMapper.queryBusCommon"/>
        ) a
        LEFT JOIN som_drg_grp_rcd b
        ON a.SETTLE_LIST_ID = b.SETTLE_LIST_ID
        GROUP BY a.ipdr_code,a.ipdr_name) a
        <where>
            <if test="isLossType != null and isLossType != ''">
                <choose>
                    <when test="isLossType == 1">
                        <![CDATA[
                            a.forecastAmountDiff < 0
                        ]]>
                    </when>
                    <when test="isLossType == 0">
                        <![CDATA[
                            a.forecastAmountDiff >= 0
                        ]]>
                    </when>
                </choose>
            </if>
        </where>
    </select>
</mapper>
