<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.som.mapper.engine.GeneraScoreMapper">

    <!-- 写入分值 -->
    <insert id="insertScore">
        INSERT INTO som_dip_sco
        (ym,
         SETTLE_LIST_ID,
         `NAME`,
         insu_type,
         dip_codg,
         DIP_NAME,
         setl_sco,
         incr_sco,
         totl_sco,
         refer_sco,
         adjm_cof,
         uplmt_mag,
         tcm_adt_cof,
         grst_cof,
         young_cof,
         key_disc_cof,
         hosp_cof,
         dise_type,
         HOSPITAL_ID,
        medcasno)
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.ym},
             #{item.settleListId},
             #{item.name},
             #{item.insuType},
             #{item.dipCodg},
             #{item.dipName},
             #{item.calculateScore},
             #{item.addScore},
             #{item.totlSco},
             #{item.refer_sco},
             #{item.adjm_cof},
             #{item.uplmtMag},
             #{item.dominantDiseaseRate},
             #{item.baseDiseaseRate},
             #{item.youngerDiseaseRate},
             #{item.professionalDiseaseRate},
             #{item.hospCof},
             #{item.diseType},
             #{item.hospitalId,jdbcType=VARCHAR},
            #{item.patientId,jdbcType=VARCHAR})
        </foreach>
    </insert>

    <!-- 查询生成分值需要数据 -->
    <select id="queryGeneraScoreData" resultType="com.my.som.vo.dipBusiness.DipPayToPredictVo">
        SELECT SUBSTR(IFNULL(c.d37,a.dscg_time),1,7) AS ym,
               a.SETTLE_LIST_ID AS settleListID,
               a.PATIENT_ID AS patientId,
               a.`NAME` AS name,
               a.AGE AS age,
               a.dscg_caty_codg_inhosp AS deptCode,
               a.dip_codg AS dipCodg,
               a.DIP_NAME AS dipName,
               IFNULL(a.act_ipt,0) AS inHosDays,
               a.HOSPITAL_ID AS hospitalId,
               IFNULL(b.refer_sco,0) AS refer_sco,
               IFNULL(b.adjm_cof,0) AS adjm_cof,
               IFNULL(b.uplmt_mag,0) AS uplmtMag,
               IFNULL(b.lowlmt_mag,0) AS lowlmtMag,
               IFNULL(CONVERT(AES_DECRYPT(UNHEX(b.dip_standard_avg_fee_same_lv),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) USING utf8),0) AS levelStandardCost,
               IFNULL(CONVERT(AES_DECRYPT(UNHEX(b.last_year_avg_fee),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) USING utf8),0) AS lastYearLevelStandardCost,
               IFNULL(CONVERT(AES_DECRYPT(UNHEX(b.dip_standard_ipt_days_same_lv),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) USING utf8),0) AS levelStandardDays,
               IFNULL(a.pre_hosp_examfee,0) AS preHospExamfee,
               c.D01 AS inHosTotalCost,
               c.D22 AS TCMCost,
               c.C03C AS WMCode,
               c.C37C AS TCMCode,
               c.a46c AS insu_type,
               IFNULL(c.D22,0)+IFNULL(c.D64,0)+IFNULL(c.D24,0)+IFNULL(c.D25,0) AS TCMTreatmentCost,
               IFNULL(c.D22,0)+IFNULL(c.D64,0)+IFNULL(c.D24,0)+IFNULL(c.D25,0)+IFNULL(c.D19,0)+IFNULL(c.D20,0)+IFNULL(c.D23,0)+IFNULL(c.D26,0)+IFNULL(c.D27,0)+IFNULL(c.D28,0)+IFNULL(c.D29,0)+IFNULL(c.D30,0)+IFNULL(c.D32,0)+IFNULL(c.D33,0) AS hospitalizationExpenses,
               d.hosp_lv AS hospLv,
               e.high_fee AS max,
               e.min_fee AS min
        FROM som_dip_grp_info a
        INNER JOIN som_hi_invy_bas_info c
        ON a.SETTLE_LIST_ID = c.ID
        LEFT JOIN som_dip_standard b
        ON SUBSTR(IFNULL(c.d37,a.dscg_time),1,4) = b.STANDARD_YEAR
        AND a.dip_codg = b.dip_codg
        AND a.is_used_asst_list = b.is_used_asst_list
        AND a.asst_list_age_grp = b.asst_list_age_grp
        AND a.asst_list_dise_sev_deg = b.asst_list_dise_sev_deg
        AND a.asst_list_tmor_sev_deg = b.asst_list_tmor_sev_deg
        AND a.HOSPITAL_ID = b.HOSPITAL_ID
        LEFT JOIN som_hosp_info d
        ON a.HOSPITAL_ID = d.HOSPITAL_ID
        LEFT JOIN som_dip_supe_ultra_low_bind e
        ON SUBSTR(IFNULL(c.d37,a.dscg_time),1,4) = e.`YEAR`
        AND a.dip_codg = e.`CODE`
        AND a.is_used_asst_list = e.is_used_asst_list
        AND a.asst_list_age_grp = e.asst_list_age_grp
        AND a.asst_list_dise_sev_deg = e.asst_list_dise_sev_deg
        AND a.asst_list_tmor_sev_deg = e.asst_list_tmor_sev_deg
        AND a.HOSPITAL_ID = e.HOSPITAL_ID
        WHERE c.DATA_LOG_ID = #{logId,jdbcType=VARCHAR}
        LIMIT #{start,jdbcType=INTEGER},#{limit,jdbcType=INTEGER}
    </select>

    <!-- 查询DRG生成分值需要数据 -->
    <select id="queryDrgGeneraScoreData" resultType="com.my.som.vo.dipBusiness.DipPayToPredictVo">
        SELECT SUBSTR(IFNULL(c.d37,a.dscg_time),1,7) AS ym,
               a.SETTLE_LIST_ID AS settleListId,
               a.PATIENT_ID AS patientId,
               a.`NAME` AS name,
               a.AGE AS age,
        CASE
        WHEN LEFT(c.a56, 4)  != t4.ctiyInsuplc AND LEFT(c.a56, 4)  != t4.provInsuplc THEN 0
        ELSE 1
        END AS isLocal,
        c.B34C as descWay,
               a.dscg_caty_codg_inhosp AS deptCode,
               a.HOSPITAL_ID AS hospitalId,
                a.act_ipt AS inHosDays,
               IFNULL(b.refer_sco,0) AS refer_sco,
        CASE
        WHEN c.a54 IN ('1' , '01' , '310') THEN ifnull(b.adjm_cof,0)
        WHEN c.a54 IN ('2' , '02' , '390')  THEN ifnull(b.adjm_resid_cof,0)
        WHEN t2.hi_type IN ('1' , '01' , '310') THEN ifnull(b.adjm_cof,0)
        WHEN t2.hi_type IN ('2' , '02' , '390') THEN ifnull(b.adjm_resid_cof,0)
        WHEN c.A46C IN ('1' , '01' ,  '310') THEN ifnull(b.adjm_cof,0)
        WHEN c.A46C IN ('2' , '02' , '390') THEN ifnull(b.adjm_resid_cof,0)
        ELSE ifnull(b.adjm_resid_cof, b.adjm_cof) END AS adjm_cof,


        IFNULL(b.uplmt_mag,0) AS uplmtMag,
               IFNULL(b.lowlmt_mag,0) AS lowlmtMag,
        b.is_sd_dise as  stableFlag,
               a.ipt_sumfee AS inHosTotalCost,
               d.hosp_lv AS hospLv,
               IFNULL(a.pre_hosp_examfee,0) AS preHospExamfee,
               a.drg_codg AS drgCodg,
               a.DRG_NAME AS drgName,
        ROUND(IFNULL(CONVERT(AES_DECRYPT(UNHEX(b.standard_avg_fee),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) USING utf8),0),4)  AS levelStandardCost,
        ROUND(IFNULL( CONVERT(AES_DECRYPT(UNHEX(b.standard_avg_fee),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) USING utf8),0),4) AS standardFee,
               CONVERT(AES_DECRYPT(UNHEX(b.standard_avg_fee),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) USING utf8) AS lastYearLevelStandardCost,
        IFNULL(t2.hi_type, IFNULL(c.a54, c.a46c)) AS insuType,
              CONVERT(AES_DECRYPT(UNHEX(b.standard_avg_fee),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) USING utf8) * b.uplmt_mag  AS max,
              CONVERT(AES_DECRYPT(UNHEX(b.standard_avg_fee),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) USING utf8) * b.lowlmt_mag AS min,
        CASE
        WHEN c.a54 = 310 OR c.a54 = 1 OR c.a54 = 01 THEN ifnull(x.czPrice, y.czPrice)
        WHEN c.a54 = 390 OR c.a54 = 2 OR c.a54 = 02 THEN ifnull(x.cxPrice, y.cxPrice)
        WHEN t2.hi_type = 310 OR t2.hi_type = 1 OR t2.hi_type = 01 THEN ifnull(x.czPrice, y.czPrice)
        WHEN t2.hi_type = 390 OR t2.hi_type = 2 OR t2.hi_type = 02 THEN ifnull(x.cxPrice, y.cxPrice)
        WHEN c.A46C = 1 THEN ifnull(x.czPrice, y.czPrice)
        WHEN c.A46C = 2 THEN ifnull(x.cxPrice, y.cxPrice)
        ELSE ifnull(x.price, y.price) END                                                               AS price,
                a.ipt_sumfee as sumfee,
                c.insuplc_admdvs as insuplcAdmdvs,
        d.hosp_cof as hospCof

        <if test="ym != null and ym != ''">
            ,sco.dise_type as scoDiseType
            ,sco.price as scoPrice
            ,sco.totl_sco as scoTotlSco
            ,sco.profitloss as scoProfitloss
            ,sco.sumfee as scoSumfee
            ,sco.remark as scoRemark
            ,sco.settle_list_id as scoSettleListId,
            c.ydjy as ydjy,
            c.a56 as a56
        </if>
        <if test="caseTypeRelatToMainDiag != null and caseTypeRelatToMainDiag != '' and lowTypeModifyFlag= 'true'">
            ,t5.dscg_diag_codg as diagnoseCode
        </if>
        FROM som_drg_grp_info a
        INNER JOIN som_hi_invy_bas_info c
        ON a.SETTLE_LIST_ID = c.ID
        LEFT JOIN som_drg_standard b
        ON SUBSTR(IFNULL(c.d37,a.dscg_time),1,4) = b.STANDARD_YEAR and b.insuplc_admdvs = c.insuplc_admdvs
        AND a.drg_codg = b.drg_codg
        AND a.HOSPITAL_ID = b.HOSPITAL_ID
        <if test="ym != null and ym != ''">
                Left join som_drg_sco sco on sco.settle_list_id =c.id
        </if>
        left join som_setl_info t2 on t2.unique_id = c.k00
        LEFT JOIN som_hosp_info d
        ON a.HOSPITAL_ID = d.HOSPITAL_ID
        left join (SELECT ym,
        MAX(CASE WHEN `KEY` = 'CX_PRICE' THEN `VALUE` ELSE NULL END) AS cxPrice,
        MAX(CASE WHEN `KEY` = 'CZ_PRICE' THEN `VALUE` ELSE NULL END) AS czPrice,
        MAX(CASE WHEN `KEY` = 'PRICE' THEN `VALUE` ELSE NULL END)    AS price,
        insuplc_admdvs
        FROM som_drg_gen_cfg s
        WHERE TYPE = 'PREDICTED_PRICE'
        GROUP BY ym, TYPE,insuplc_admdvs
        ) x on REPLACE(SUBSTR(IFNULL(c.d37,a.dscg_time), 1, 7), '-', '') = x.ym and x.insuplc_admdvs = c.insuplc_admdvs
        left JOIN
        (SELECT MAX(CASE WHEN `KEY` = 'CX_PRICE' THEN `VALUE` ELSE NULL END) AS cxPrice,
        MAX(CASE WHEN `KEY` = 'CZ_PRICE' THEN `VALUE` ELSE NULL END) AS czPrice,
        MAX(CASE WHEN `KEY` = 'PRICE' THEN `VALUE` ELSE NULL END)    AS price,
        insuplc_admdvs
        FROM som_drg_gen_cfg z
        WHERE TYPE = 'PREDICTED_PRICE'
        and ym = ''
        GROUP BY insuplc_admdvs,TYPE) y on  y.insuplc_admdvs = c.insuplc_admdvs
        CROSS JOIN
        (SELECT
        MAX(CASE WHEN grper_ver LIKE '%市医保%' THEN insuplc_admdvs END) AS ctiyInsuplc,
        MAX(CASE WHEN grper_ver LIKE '%省医保%' THEN insuplc_admdvs END) AS provInsuplc
        FROM
        som_grp_reqt_addr_info
        WHERE
        grper_ver LIKE '%市医保%' OR grper_ver LIKE '%省医保%') t4
        <if test="caseTypeRelatToMainDiag != null and caseTypeRelatToMainDiag != '' and lowTypeModifyFlag= 'true'">
        LEFT JOIN SOM_diag t5  on t5.SETTLE_LIST_ID = c.ID AND t5.seq= 0 and t5.seq= 0 and t5.maindiag_flag =1
        </if>
        WHERE 1=1
        <if test="test=logId!= null and logId != ''  ">
            and c.DATA_LOG_ID = #{logId,jdbcType=VARCHAR}
        </if>
        <if test="ym != null and ym != ''">
            and  sco.ym = #{ym,jdbcType=VARCHAR}
        </if>
        <if test="start != null and start != '' and limit != null and limit != ''">
            LIMIT #{start,jdbcType=INTEGER},#{limit,jdbcType=INTEGER}
        </if>

    </select>

    <!-- 查询DIP测算分值数据 -->
    <select id="queryDipGeneraScoreData"
            resultType="com.my.som.vo.dipBusiness.DipPayToPredictVo">
        SELECT SUBSTR(IFNULL(c.d37,a.dscg_time),1,7)                                                                           AS ym,
               a.act_ipt  AS inHosDays,
        a.SETTLE_LIST_ID                                                                                    AS settleListId,
               a.PATIENT_ID                                                                                        AS patientId,
               a.`NAME`                                                                                            AS name,
               a.AGE                                                                                               AS age,
        c.A16 AS dayAge,
               a.dscg_caty_codg_inhosp                                                                             AS deptCode,
               a.HOSPITAL_ID                                                                                       AS hospitalId,
               IFNULL(b.refer_sco, 0)                                                                              AS refer_sco,
               IFNULL(b.adjm_cof, 0)                                                                               AS adjm_cof,
               IFNULL(b.uplmt_mag, 0)                                                                              AS uplmtMag,
               IFNULL(b.lowlmt_mag, 0)                                                                             AS lowlmtMag,
               a.ipt_sumfee                                                                                        AS inHosTotalCost,
               d.hosp_lv                                                                                           AS hospLv,
               d.hosp_cof                                                                                          AS hospCof,
               IFNULL(a.pre_hosp_examfee, 0)                                                                       AS preHospExamfee,
               b.is_sd_dise                                                                                        AS stableFlag,

        IFNULL(c.D22, 0) + IFNULL(c.D64, 0) + IFNULL(c.D24, 0) +
               IFNULL(c.D25, 0)                                                                                    AS TCMTreatmentCost,
               IFNULL(c.D22, 0) + IFNULL(c.D64, 0) + IFNULL(c.D24, 0) + IFNULL(c.D25, 0) + IFNULL(c.D19, 0) +
               IFNULL(c.D20, 0) + IFNULL(c.D23, 0) + IFNULL(c.D26, 0) + IFNULL(c.D27, 0) + IFNULL(c.D28, 0) +
               IFNULL(c.D29, 0) + IFNULL(c.D30, 0) + IFNULL(c.D32, 0) +
               IFNULL(c.D33, 0)                                                                                    AS hospitalizationExpenses,
               a.dip_codg                                                                                          AS dipCodg,
               a.DIP_NAME                                                                                          AS dipName,
               a.grp_stas                                                                                          AS grpStas,
               a.asst_list_dise_sev_deg                                                                            AS asstListDiseSevDeg,
               a.asst_list_tmor_sev_deg                                                                            AS asstListTmorSevDeg,
               a.asst_list_age_grp                                                                                 AS asstListAgeGrp,
                a.auxiliary_burn as auxiliaryBurn,
            b.aux_coefficient as AsstListAdm,
               IFNULL(CONVERT(AES_DECRYPT(UNHEX(b.dip_standard_inpf),
                                          ${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) USING utf8),
                      0)                                                                                           AS areaStandardCost,
               IFNULL(CONVERT(AES_DECRYPT(UNHEX(b.dip_standard_avg_fee_same_lv),
                                          ${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) USING utf8),
                      0)                                                                                           AS levelCost,
               IFNULL(CONVERT(AES_DECRYPT(UNHEX(b.last_year_avg_fee),
                                          ${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) USING utf8),
                      0)                                                                                           AS lastYearLevelStandardCost,
               IFNULL(CAST(CONVERT(AES_DECRYPT(UNHEX(b.dip_standard_inpf),
                                               ${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) USING utf8) /
                           b.refer_sco * 100 as decimal(8, 4)),
                      0)                                                                                           AS diseaseAverageCost,
        IFNULL(convert(AES_DECRYPT(UNHEX(b.dip_standard_ipt_days_same_lv),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) using utf8),0) as levelStandardDays,
               c.D22                                                                                               AS TCMCost,
               c.C03C                                                                                              AS WMCode,
               c.C37C                                                                                              AS TCMCode,
              ifnull(c.a54, ifnull(t2.hi_type,c.A46C ))                                                                               AS insuType,

               e.high_fee                                                                                          AS max,
               e.min_fee                                                                                           AS min,
               '1'                                                                                                 AS queryType,
        CASE
        WHEN c.a54 = 310 OR c.a54 = 1 OR c.a54 = 01 THEN ifnull(x.czPrice, y.czPrice)
        WHEN c.a54 = 390 OR c.a54 = 2 OR c.a54 = 02 THEN ifnull(x.cxPrice, y.cxPrice)
        WHEN t2.hi_type = 310 OR t2.hi_type = 1 OR t2.hi_type = 01 THEN ifnull(x.czPrice, y.czPrice)
        WHEN t2.hi_type = 390 OR t2.hi_type = 2 OR t2.hi_type = 02 THEN ifnull(x.cxPrice, y.cxPrice)
        WHEN c.A46C = 1 THEN ifnull(x.czPrice, y.czPrice)
        WHEN c.A46C = 2 THEN ifnull(x.cxPrice, y.cxPrice)
        ELSE ifnull(x.price, y.price) END                                                               AS price,
<!--        CASE-->
<!--        WHEN a.canbaoleixing = '城镇职工'  THEN ifnull(x.czPrice, y.czPrice)-->
<!--        WHEN a.canbaoleixing = '城乡居民' THEN ifnull(x.cxPrice, y.cxPrice)-->
<!--        ELSE ifnull(x.price, y.price) END                                                               AS price,-->
               a.ipt_sumfee                                                                                        as sumfee,
               b.dis_type AS disType,
    ifnull( t1.total, 0)    as checkTotalFee,
        c.d35 as businessSerial,
        ifnull(c.d01, 0) as inHospCost,
        ifnull(t3.fundAmtSum, 0) as fundAmtSum,
        d.emp_fund_ratio as empFundRatio,
        d.resid_fund_ratio as residFundratio
<!--        a.shijijisuanfeiy as yaoqiushiyongfeiyong-->
<!--        ifnull(c.medicare_settlement_cost, 0) as checkTotalFee-->
        FROM som_dip_grp_info a
                 inner JOIN som_hi_invy_bas_info c
                           ON a.SETTLE_LIST_ID = c.ID
        left join
        ( SELECT  sum( amt ) AS total, hi_setl_invy_id FROM `som_hi_setl_invy_med_fee_info` GROUP BY  hi_setl_invy_id ) t1
        on c.id = t1.hi_setl_invy_id
        left join som_setl_info t2 on t2.unique_id = c.k00
        left join
        (SELECT SUM(fund_payamt) as fundAmtSum,hi_setl_invy_id
        FROM som_fund_pay
        WHERE fund_pay_type IN ('310100', '390100','310','390','城镇职工基本医疗保险统筹基金','城乡居民基本医疗保险基金')
        GROUP BY hi_setl_invy_id) t3 on t3.hi_setl_invy_id = c.id
        INNER JOIN som_dip_grp_rcd x ON c.ID = x.SETTLE_LIST_ID
                 LEFT JOIN som_dip_standard b
                           ON SUBSTR(IFNULL(c.d37,a.dscg_time), 1, 4) = b.STANDARD_YEAR
                               AND a.dip_codg = b.dip_codg
                               AND a.HOSPITAL_ID = b.HOSPITAL_ID
        AND x.asst_list_age_grp = b.asst_list_age_grp
        AND x.asst_list_dise = b.asst_list_dise_sev_deg
        AND x.asst_list_tmor_sev_deg = b.asst_list_tmor_sev_deg
        AND x.auxiliary_burn = b.auxiliary_burn
                 LEFT JOIN som_hosp_info d
                           ON a.HOSPITAL_ID = d.HOSPITAL_ID
                 LEFT JOIN som_dip_supe_ultra_low_bind e
                           ON SUBSTR(IFNULL(c.d37,a.dscg_time), 1, 4) = e.`YEAR`
                               AND a.HOSPITAL_ID = e.HOSPITAL_ID
                               AND a.dip_codg = e.`CODE`
                               AND e.TYPE = 1
                                and x.asst_list_age_grp = e.asst_list_age_grp
                                AND x.asst_list_dise = e.asst_list_dise_sev_deg
                                AND x.asst_list_tmor_sev_deg = e.asst_list_tmor_sev_deg
                                AND x.auxiliary_burn = e.auxiliary_burn
                 left join (SELECT ym,
                                   MAX(CASE WHEN `KEY` = 'CX_PRICE' THEN `VALUE` ELSE NULL END) AS cxPrice,
                                   MAX(CASE WHEN `KEY` = 'CZ_PRICE' THEN `VALUE` ELSE NULL END) AS czPrice,
                                   MAX(CASE WHEN `KEY` = 'PRICE' THEN `VALUE` ELSE NULL END)    AS price
                            FROM som_dip_gen_cfg s
                            WHERE TYPE = 'PREDICTED_PRICE'
                            GROUP BY ym, TYPE) x on REPLACE(SUBSTR(IFNULL(c.d37,a.dscg_time), 1, 7), '-', '') = x.ym
                 CROSS JOIN
             (SELECT MAX(CASE WHEN `KEY` = 'CX_PRICE' THEN `VALUE` ELSE NULL END) AS cxPrice,
                     MAX(CASE WHEN `KEY` = 'CZ_PRICE' THEN `VALUE` ELSE NULL END) AS czPrice,
                     MAX(CASE WHEN `KEY` = 'PRICE' THEN `VALUE` ELSE NULL END)    AS price
              FROM som_dip_gen_cfg
              WHERE TYPE = 'PREDICTED_PRICE'
                and ym = ''
              GROUP BY TYPE) y
        where 1=1
        <if test="test=logId!= null and logId != ''  ">
            and c.DATA_LOG_ID = #{logId,jdbcType=VARCHAR}
        </if>
        LIMIT #{start,jdbcType=INTEGER},#{limit,jdbcType=INTEGER}


    </select>

    <!-- 查询分组数据 -->
    <select id="queryDipGeneraScoreData1"
            resultType="com.my.som.vo.dipBusiness.DipPayToPredictVo">
        SELECT SUBSTR(IFNULL(c.d37,a.dscg_time),1,7) AS ym,
               a.SETTLE_LIST_ID AS settleListID,
               a.PATIENT_ID AS patientID,
               a.`NAME` AS name,
               a.AGE AS age,
               a.dscg_caty_codg_inhosp AS deptCode,
               a.HOSPITAL_ID AS hospitalId,
               IFNULL(b.refer_sco,0) AS refer_sco,
               IFNULL(b.adjm_cof,0) AS adjm_cof,
               IFNULL(b.uplmt_mag,0) AS uplmtMag,
               IFNULL(b.lowlmt_mag,0) AS lowlmtMag,
               a.ipt_sumfee AS inHosTotalCost,
               d.hosp_lv AS hospLv,
               d.hosp_cof AS hospCof,
               IFNULL(a.pre_hosp_examfee,0) AS preHospExamfee,
               b.is_sd_dise AS stableFlag,
               IFNULL(c.D22,0)+IFNULL(c.D64,0)+IFNULL(c.D24,0)+IFNULL(c.D25,0) AS TCMTreatmentCost,
               IFNULL(c.D22,0)+IFNULL(c.D64,0)+IFNULL(c.D24,0)+IFNULL(c.D25,0)+IFNULL(c.D19,0)+IFNULL(c.D20,0)+IFNULL(c.D23,0)+IFNULL(c.D26,0)+IFNULL(c.D27,0)+IFNULL(c.D28,0)+IFNULL(c.D29,0)+IFNULL(c.D30,0)+IFNULL(c.D32,0)+IFNULL(c.D33,0) AS hospitalizationExpenses,
               a.dip_codg AS dipCodg,
               a.DIP_NAME AS dipName,
               a.grp_stas AS grpStas,
               IFNULL(CONVERT(AES_DECRYPT(UNHEX(b.dip_standard_inpf),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) USING utf8),0) AS areaStandardCost,
               IFNULL(CONVERT(AES_DECRYPT(UNHEX(b.last_year_avg_fee),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) USING utf8),0) AS levelCost,
               IFNULL(CONVERT(AES_DECRYPT(UNHEX(b.dip_standard_avg_fee_same_lv),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) USING utf8),0) AS lastYearLevelStandardCost,
               IFNULL(CAST(CONVERT(AES_DECRYPT(UNHEX(b.dip_standard_inpf),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) USING utf8)/b.refer_sco * 100 as decimal(8,4)),0) AS diseaseAverageCost,
               c.D22 AS TCMCost,
               c.C03C AS WMCode,
               c.C37C AS TCMCode,
               c.a54 AS insuType,
               e.high_fee AS max,
               e.min_fee AS min,
               '1' AS queryType,
               n.dis_gp_codg as disGpCodg,
               n.dis_gp_name as disGpName,
               n.asst_list_age_grp as asstListAgeGrp,
               n.asst_list_dise_sev_deg as asstListDiseSevDeg,
               n.asst_list_tmor_sev_deg as asstListTmorSevDeg,
               n.mdtrt_id as mdtrtId,
               a.adm_time as admTime,
               a.dscg_time as dscgTime,
               a.setl_end_time as setlTime,
               a.adm_caty_codg_inhosp as deptName,
               a.ipdr_name as drName,
        b.aux_coefficient as AsstListAdm,
               b.dis_type as disType

        FROM som_dip_grp_info a
        inner JOIN som_hi_invy_bas_info c
        ON a.SETTLE_LIST_ID = c.ID
        inner join som_fbck_ana n
        on c.CLINIC_ID = n.MDTRT_ID
        LEFT JOIN som_dip_standard b
        ON SUBSTR(IFNULL(c.d37,a.dscg_time), 1, 4) = b.STANDARD_YEAR
        AND n.dis_gp_codg = b.dip_codg
        AND n.asst_list_age_grp = b.asst_list_age_grp
        AND n.asst_list_dise_sev_deg = b.asst_list_dise_sev_deg
        AND n.asst_list_tmor_sev_deg = b.asst_list_tmor_sev_deg
        LEFT JOIN som_hosp_info d
        ON a.HOSPITAL_ID = d.HOSPITAL_ID
        LEFT JOIN som_dip_supe_ultra_low_bind e
        ON SUBSTR(IFNULL(c.d37,a.dscg_time), 1, 4) = e.`YEAR`
        AND a.HOSPITAL_ID = e.HOSPITAL_ID
        AND a.dip_codg = e.`CODE`
        AND a.is_used_asst_list = e.is_used_asst_list
        AND a.asst_list_age_grp = e.asst_list_age_grp
        AND a.asst_list_dise_sev_deg = e.asst_list_dise_sev_deg
        AND a.asst_list_tmor_sev_deg = e.asst_list_tmor_sev_deg
        AND e.TYPE = 1

<!--        where (-->
<!--        x.used_asst_list != 1 OR x.used_asst_list is NULL OR (-->
<!--        x.asst_list_age_grp = b.asst_list_age_grp-->
<!--        AND x.asst_list_dise = b.asst_list_dise_sev_deg-->
<!--        AND x.asst_list_tmor_sev_deg = b.asst_list_tmor_sev_deg-->
<!--        ))-->

<!--        and (-->
<!--        x.used_asst_list != 1 OR x.used_asst_list is NULL OR (-->
<!--        x.asst_list_age_grp = e.asst_list_age_grp-->
<!--        AND x.asst_list_dise = e.asst_list_dise_sev_deg-->
<!--        AND x.asst_list_tmor_sev_deg = e.asst_list_tmor_sev_deg-->
<!--        ))-->

        <where>
            <if test="seStartTime != null and seStartTime != ''">
                AND a.setl_end_time BETWEEN #{seStartTime,jdbcType=VARCHAR}
                AND CONCAT(#{seEndTime,jdbcType=VARCHAR}, ' 59:59:59')
            </if>
            <if test="begnDate != null and begnDate != ''">
                AND a.dscg_time BETWEEN #{begnDate,jdbcType=VARCHAR}
                AND CONCAT(#{expiDate,jdbcType=VARCHAR}, ' 59:59:59')
            </if>
        </where>
        LIMIT #{start,jdbcType=INTEGER},#{limit,jdbcType=INTEGER}
    </select>

    <update id="updateDipScoData">
        <foreach collection="list" item="item" separator=";">
        UPDATE som_dip_sco
        SET
        profitloss = #{item.scoProfitloss},
        forecast_fee = #{item.scoForecastFee},
        totl_sco = #{item.scoTotlSco},
        remark = #{item.scoRemark}
        WHERE settle_list_id = #{item.scoSettleListId}
        </foreach>
    </update>

    <update id="updateDrgScoData">
        <foreach collection="list" item="item" separator=";">
            UPDATE som_drg_sco
            SET
            profitloss = #{item.scoProfitloss},
            forecast_fee = #{item.scoForecastFee},
            totl_sco = #{item.scoTotlSco},
            remark = #{item.scoRemark}
            WHERE settle_list_id = #{item.scoSettleListId}
        </foreach>
    </update>

</mapper>
