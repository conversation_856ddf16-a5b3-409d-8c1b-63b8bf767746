<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.som.mapper.engine.DipGroupMapper">

    <!-- 获取DIP分组需要的信息 -->
    <select id="queryDIPGroupInfo" resultType="com.my.som.vo.dataHandle.dataGroup.DipGroupDataInfoVo" fetchSize="1000">
        SELECT
            a.k00, <!-- 病案唯一标识ID -->
        a.id as settleListId, <!-- 结算清单ID -->
        a.a03 as medcasType, <!-- 病案类型（1：西医，2：中医） -->
        a.hospital_id as hospitalId, <!-- 医疗机构ID -->
        a.a46c as grpType, <!-- 人群类别（医疗付款方式） -->
        a.d01 as iptSumfee, <!-- 住院总费用（病案首页） -->
        a.d09 as iptSumfeeInSelfpayAmt, <!-- 住院总费用其中自付金额（病案首页） -->
        a.opr_date, <!-- 操作时间 -->
        c.dscg_diag_codg, <!-- 主要诊断编码 -->
        c.dscg_diag_name, <!-- 主要诊断名称 -->
        b.* <!-- 所有手术编码 -->
        FROM
        som_hi_invy_bas_info a
        LEFT JOIN (
                SELECT
                    SETTLE_LIST_ID,
                    MAX( CASE WHEN seq = 0 THEN C35C ELSE NULL END ) AS 'C35C_0',
                    MAX( CASE WHEN seq = 1 THEN C35C ELSE NULL END ) AS 'C35C_1',
                    MAX( CASE WHEN seq = 2 THEN C35C ELSE NULL END ) AS 'C35C_2',
                    MAX( CASE WHEN seq = 3 THEN C35C ELSE NULL END ) AS 'C35C_3',
                    MAX( CASE WHEN seq = 4 THEN C35C ELSE NULL END ) AS 'C35C_4',
                    MAX( CASE WHEN seq = 5 THEN C35C ELSE NULL END ) AS 'C35C_5',
                    MAX( CASE WHEN seq = 6 THEN C35C ELSE NULL END ) AS 'C35C_6'
                FROM
                    som_oprn_oprt_info
                GROUP BY
                    SETTLE_LIST_ID
            ) b ON a.ID = b.SETTLE_LIST_ID
                LEFT JOIN (
                    SELECT dscg_diag_codg, dscg_diag_name, seq, SETTLE_LIST_ID
                    FROM som_diag
                    WHERE seq = '0'
        ) c ON a.ID = c.SETTLE_LIST_ID
        <where>
            <if test="logId!=null and logId!=''">
                AND a.DATA_LOG_ID = #{logId}
            </if>
            <if test="activeFlag!=null and activeFlag!=''">
                AND a.ACTIVE_FLAG = #{activeFlag}
            </if>
            <if test="hospitalId != null and hospitalId != ''">
                AND a.HOSPITAL_ID = #{hospitalId}
            </if>
        </where>
    </select>

    <!-- 查询DIP分组需要的数据2(不采用ResultHandler方式) -->
    <select id="queryDipGroupInfo2"
            resultType="com.my.som.vo.dataHandle.dataGroup.DipGroupDataInfoVo">
        SELECT
            a.k00, <!-- 病案唯一标识ID -->
            a.id as settleListId, <!-- 结算清单ID -->
            a.a03 as medcasType, <!-- 病案类型（1：西医，2：中医） -->
            a.hospital_id as hospitalId, <!-- 医疗机构ID -->
            a.a46c as grpType, <!-- 人群类别（医疗付款方式） -->
            a.d01 as iptSumfee, <!-- 住院总费用（病案首页） -->
            a.d09 as iptSumfeeInSelfpayAmt, <!-- 住院总费用其中自付金额（病案首页） -->
            a.opr_date, <!-- 操作时间 -->
            c.dscg_diag_codg, <!-- 主要诊断编码 -->
            c.dscg_diag_name, <!-- 主要诊断名称 -->
            b.*, <!-- 所有手术编码 -->
            e.*,<!-- 其他诊断 -->
            a.A14 as c40c, <!-- 年龄 -->
            a.B20 as c42c, <!-- 住院天数 -->
            a.B34C as c41c, <!-- 离院方式 -->
            d.hosp_lv_chn AS hosp_lv <!-- 医院级别 -->
        FROM
        som_hi_invy_bas_info a
        LEFT JOIN (
                SELECT
                    SETTLE_LIST_ID,
                    MAX( CASE WHEN seq = 0 THEN C35C ELSE NULL END ) AS 'C35C_0',
                    MAX( CASE WHEN seq = 1 THEN C35C ELSE NULL END ) AS 'C35C_1',
                    MAX( CASE WHEN seq = 2 THEN C35C ELSE NULL END ) AS 'C35C_2',
                    MAX( CASE WHEN seq = 3 THEN C35C ELSE NULL END ) AS 'C35C_3',
                    MAX( CASE WHEN seq = 4 THEN C35C ELSE NULL END ) AS 'C35C_4',
                    MAX( CASE WHEN seq = 5 THEN C35C ELSE NULL END ) AS 'C35C_5',
                    MAX( CASE WHEN seq = 6 THEN C35C ELSE NULL END ) AS 'C35C_6'
                FROM
                    som_oprn_oprt_info
                where SETTLE_LIST_ID in (
						select a.id from
						som_hi_invy_bas_info a
                        <where>
                            <if test="queryParam.logId!=null and queryParam.logId!=''">
                                AND a.DATA_LOG_ID = #{queryParam.logId}
                            </if>
                            <if test="queryParam.activeFlag!=null and queryParam.activeFlag!=''">
                                AND a.ACTIVE_FLAG = #{queryParam.activeFlag}
                            </if>
                        </where>
				)
                GROUP BY SETTLE_LIST_ID
        ) b ON a.ID = b.SETTLE_LIST_ID
        LEFT JOIN (
            SELECT dscg_diag_codg, dscg_diag_name, seq, SETTLE_LIST_ID
            FROM som_diag
            WHERE seq = '0'
        ) c ON a.ID = c.SETTLE_LIST_ID
        LEFT JOIN (
            select a.HOSPITAL_ID,b.labl_name AS hosp_lv_chn
            FROM som_hosp_info a
            LEFT JOIN (
                select * from som_sys_code
                where code_type = 'YYJB'
            ) b
            on a.hosp_lv = b.data_val
        ) d
        ON a.HOSPITAL_ID = d.HOSPITAL_ID
        LEFT JOIN (
            SELECT
                SETTLE_LIST_ID,
                MAX( CASE WHEN seq = 1 THEN dscg_diag_codg ELSE NULL END ) AS 'C06C_1',
                MAX( CASE WHEN seq = 2 THEN dscg_diag_codg ELSE NULL END ) AS 'C06C_2',
                MAX( CASE WHEN seq = 3 THEN dscg_diag_codg ELSE NULL END ) AS 'C06C_3',
                MAX( CASE WHEN seq = 4 THEN dscg_diag_codg ELSE NULL END ) AS 'C06C_4',
                MAX( CASE WHEN seq = 5 THEN dscg_diag_codg ELSE NULL END ) AS 'C06C_5',
                MAX( CASE WHEN seq = 6 THEN dscg_diag_codg ELSE NULL END ) AS 'C06C_6',
                MAX( CASE WHEN seq = 7 THEN dscg_diag_codg ELSE NULL END ) AS 'C06C_7',
                MAX( CASE WHEN seq = 8 THEN dscg_diag_codg ELSE NULL END ) AS 'C06C_8',
                MAX( CASE WHEN seq = 9 THEN dscg_diag_codg ELSE NULL END ) AS 'C06C_9',
                MAX( CASE WHEN seq = 10 THEN dscg_diag_codg ELSE NULL END ) AS 'C06C_10',
                MAX( CASE WHEN seq = 11 THEN dscg_diag_codg ELSE NULL END ) AS 'C06C_11',
                MAX( CASE WHEN seq = 12 THEN dscg_diag_codg ELSE NULL END ) AS 'C06C_12',
                MAX( CASE WHEN seq = 13 THEN dscg_diag_codg ELSE NULL END ) AS 'C06C_13',
                MAX( CASE WHEN seq = 14 THEN dscg_diag_codg ELSE NULL END ) AS 'C06C_14',
                MAX( CASE WHEN seq = 15 THEN dscg_diag_codg ELSE NULL END ) AS 'C06C_15'
            FROM
                som_diag
            where SETTLE_LIST_ID in (
                select a.id from
                som_hi_invy_bas_info a
                <where>
                    <if test="queryParam.logId!=null and queryParam.logId!=''">
                        AND a.DATA_LOG_ID = #{queryParam.logId}
                    </if>
                    <if test="queryParam.activeFlag!=null and queryParam.activeFlag!=''">
                        AND a.ACTIVE_FLAG = #{queryParam.activeFlag}
                    </if>
                </where>
            )
            GROUP BY SETTLE_LIST_ID
        ) e on a.ID = e.SETTLE_LIST_ID
        <where>
            <if test='queryParam.validateEnableType != null and queryParam.validateEnableType != "" and queryParam.validateEnableType == "1"'>
                AND a.id not in
                (
                    <include refid="com.my.som.dao.dataHandle.DrgGroupJobDao.settleListMedicalPageChoose" />
                )
            </if>
            <if test="queryParam.logId!=null and queryParam.logId!=''">
                AND a.DATA_LOG_ID = #{queryParam.logId}
            </if>
            <if test="queryParam.activeFlag!=null and queryParam.activeFlag!=''">
                AND a.ACTIVE_FLAG = #{queryParam.activeFlag}
            </if>
        </where>
        LIMIT #{queryParam.start,jdbcType=INTEGER},#{queryParam.limit,jdbcType=INTEGER}
    </select>

</mapper>
