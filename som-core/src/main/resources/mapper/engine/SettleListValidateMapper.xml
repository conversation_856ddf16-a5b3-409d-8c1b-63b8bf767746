<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.som.mapper.engine.SettleListValidateMapper">

    <!-- 写入校验日志 -->
    <insert id="insertValidateLog">
        INSERT INTO som_setl_invy_chk(
        SETTLE_LIST_ID,err_fld,err_dscr,chk_fld_unfind_field,chk_stas

        )
        <foreach collection="list" item="log" separator="," open="VALUES">
            (
            #{log.settleListId,jdbcType=VARCHAR},
            #{log.errorFields,jdbcType=VARCHAR},
            #{log.errDscr,jdbcType=VARCHAR},
            #{log.validateFieldNotFindFields,jdbcType=VARCHAR},
            #{log.chkStas,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>

    <!-- 新增校验日志详情 -->
    <insert id="insertValidateLogDetails">
        INSERT INTO som_invy_chk_detl(
        SETTLE_LIST_ID,err_fld,err_dscr,err_type
        )
        <foreach collection="list" item="log" separator="," open="VALUES">
            (
            #{log.settleListId,jdbcType=VARCHAR},
            #{log.errorFields,jdbcType=VARCHAR},
            #{log.errDscr,jdbcType=VARCHAR},
            #{log.errType,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>

    <!-- 查询医保结算期清单基础数据 -->
    <select id="querySettleListBase" resultType="com.my.som.model.dataHandle.SomHiInvyBasInfo">
        SELECT id,K00, A02, A01, A50,
               A51, A48, A52, A11,
               A12C, A13, A14, A16,
               A15C, A19C, A53, A20,
               A38C, A26, A29N, A29,
                A30, A31C, A32, A33C,
               A34, A35, A54, A55,
               A56, A57, A18, A17,
               MTMM_IMP_DEPT_NAME AS mtmmImpDeptName,
               MTMM_INP_DATE AS mtmmInpDate,
               B38, B11C, B39, B12,
               B13C, B13N, B21C, B15,
               B16C, B16N, B20, C01C,
               C02N, C35C, C36N, C42,
               C43, C44, C28, C29,
               C30, C31, C32, C33,
               C45, C46, C47, B44,
               B45, B46, B47, B34C,
               B48, B49, B36C, B37,
               B51C, B52N, D35, D38,
               D39, D36, D37, D54,D59,D60,
               D55,D56,D57,D58, A58,
               D01, B26C,B26N,
               MED_INS_ORGAN AS medInsOrgan,
               MED_INS_ORGAN_OPERATOR AS medInsOrganOperator,
               CLINIC_ID AS clinicId,
               SETTLEMENT_ID AS settlementId,
               PSN_NO AS psnNo,
               MUL_NWB_BIR_WT AS mulNwbBirWt,
               MUL_NWB_ADM_WT AS mulNwbAdmWt,
               STAS_TYPE AS stasType,
               HOSPITAL_ID AS hospitalId
        FROM som_hi_invy_bas_info
        WHERE data_log_id = #{logId,jdbcType=VARCHAR}
        LIMIT #{start,jdbcType=INTEGER},#{limit,jdbcType=INTEGER}
    </select>

    <!-- 查询医保结算清单诊断数据 -->
    <select id="querySettleListDiagnosis"
            resultType="com.my.som.dto.medicalQuality.BusDiseaseDiagnosisTrim">
        select id,
        seq AS seq,
        SETTLE_LIST_ID AS settleListId,
        dscg_diag_codg AS c06c1,
        dscg_diag_name AS c07n1,
        dscg_diag_adm_cond AS c08c1,
        `type` AS type,
        maindiag_flag as mainDiagFlag
        from som_diag
        where SETTLE_LIST_ID in (
        <include refid="settleListIds"/>
        )
    </select>

    <!-- 查询医保结算清单手术数据 -->
    <select id="querySettleListOperate" resultType="com.my.som.model.dataHandle.SomOprnOprtInfo">
        SELECT id,
        seq AS seq,
        SETTLE_LIST_ID AS settleListId,
        C35C,C36N,oprn_oprt_date,C43,C39C,oprn_oprt_oper_name,
        oprn_oprt_anst_dr_code,oprn_oprt_anst_dr_name,
        OPRN_OPRT_BEGNTIME AS oprnOprtBegntime,
        OPRN_OPRT_ENDTIME AS oprnOprtEndtime,
        ANST_BEGNTIME AS anstBegntime,
        ANST_ENDTIME AS anstEndtime
        FROM som_oprn_oprt_info
        WHERE SETTLE_LIST_ID IN (
        <include refid="settleListIds"/>
        )
    </select>

    <!-- 查询医保结算清单门特门慢数据 -->
    <select id="querySettleListOPCD" resultType="com.my.som.model.medicalQuality.SomOtpSlowSpecialTrtInfo">
        SELECT id,
        hi_setl_invy_id AS settleListId,
        diag_name AS diagName,
        diag_code AS diagCode,
        oprn_oprt_name AS oprnOprtCode,
        oprn_oprt_code AS oprnOprtName,
        DEPT_CODE AS deptCode,
        DEPT_NAME AS deptName,
        mdtrt_date AS mdtrtDate
        FROM som_otp_slow_special_trt_info
        WHERE hi_setl_invy_id IN (
        <include refid="settleListIds"/>
        )
    </select>

    <!-- 查询医保结算清单重症数据 -->
    <select id="querySettleListIcu" resultType="com.my.som.model.medicalQuality.SomSetlInvyScsCutdInfo">
        SELECT id,
        @rownum := @rownum + 1 AS seq,
        hi_setl_invy_id AS settleListId,
        scs_cutd_ward_type AS scsCutdWardType,
        scs_cutd_inpool_time AS scsCutdInpoolTime,
        scs_cutd_exit_time AS scsCutdExitTime,
        scs_cutd_sum_dura AS scsCutdSumDura
        FROM som_setl_invy_scs_cutd_info, ( SELECT @rownum := -1 ) r
        WHERE hi_setl_invy_id IN (
        <include refid="settleListIds"/>
        )
    </select>

    <!-- 查询医保结算清单医疗费用数据 -->
    <select id="querySettleListMedicalCost" resultType="com.my.som.model.medicalQuality.SomHiSetlInvyMedFeeInfo">
        SELECT id,
        hi_setl_invy_id AS settleListId,
        med_chrg_itemname AS medChrgItemname,
        amt AS amt,
        claa AS claa,
        clab AS clab,
        ownpay AS ownpay,
        oth AS oth
        FROM som_hi_setl_invy_med_fee_info
        WHERE hi_setl_invy_id IN (
        <include refid="settleListIds"/>
        )
    </select>

    <!-- 查询医保结算清单医疗费用数据 -->
    <select id="querySettleListFundPay" resultType="com.my.som.model.medicalQuality.SomFundPay">
        SELECT id,
        hi_setl_invy_id AS settleListId,
        FUND_PAY_TYPE AS fundPayType,
        pool_coty_fund_pay_type AS poolCotyFundPayType,
        pool_coty_fund_pay_type_name AS poolCotyFundPayTypeName,
        FUND_PAYAMT AS fundPayamt
        FROM som_fund_pay
        WHERE hi_setl_invy_id IN (
        <include refid="settleListIds"/>
        )
    </select>

    <!-- 查询医保结算清单启用规则 -->
    <select id="queryMedicalInsRule" resultType="com.my.som.vo.dataHandle.SettleListRuleVo">
        <include refid="com.my.som.dao.dataHandle.MedicalSettleCheckDao.settleListValidateQuery"/>
        WHERE enab_flag = 1
    </select>

    <!-- 查询码表 -->
    <select id="queryDict" resultType="com.my.som.vo.SomSysCode">
        select code_type AS codeType,
               GROUP_CONCAT(data_val) AS codeValues
        from som_sys_dic
        group by code_type
        UNION ALL
        SELECT  'BLD_CAT_NAME' AS codeType,
        GROUP_CONCAT(labl_name) AS codeValues
        FROM som_sys_dic
        WHERE code_type = 'SXPZ';
    </select>

    <!-- 查询诊断段数据 -->
    <select id="queryDisSection" resultType="com.my.som.vo.dataHandle.SettleListDisSectionVo">
        select diag_sec AS diagSec,
               chk_type AS chkType,
               dscr AS dscr
        from som_chk_diag_sec
    </select>

    <!-- 查询灰码 -->
    <select id="queryGrayCode" resultType="com.my.som.vo.common.IcdGreyCode">
        SELECT ICD_TYPE AS icdType,
               icd_grey_code AS icdGreyCode,
               icd_grey_code_name AS icdGreyCodeName,
               icd_exp_code AS icdExpCode,
               icd_exp_code_name AS icdExpCodeName,
               TYPE AS type
        FROM som_chk_grey_code
    </select>

    <!-- 查询不能作为主诊编码 -->
    <select id="queryCannotPrimaryCode" resultType="com.my.som.model.common.SomIcdCrsp">
        SELECT icd_codg AS icdCodg,
               ICD_NAME AS icdName,
               dscr AS dscr,
               TYPE AS type
        FROM som_chk_unable_action_main_diag_codg
    </select>

    <!-- 查询输血信息 -->
    <select id="querySettleListTransfusion"
            resultType="com.my.som.model.medicalQuality.SomSetlInvyBldInfo">
        SELECT id,
        SETTLE_LIST_ID AS settleListId,
        BLD_CAT AS bldCat,
        BLD_AMT AS bldAmt,
        BLD_UNT AS bldUnt
        FROM som_setl_invy_bld_info
        WHERE SETTLE_LIST_ID IN (
        <include refid="settleListIds"/>
        )
    </select>

    <select id="queryBusFeeBreakDown"
            resultType="com.my.som.model.medicalQuality.BusFeeBreakDown">
        SELECT

        a.id as settleListId,
        substr(b.med_list_codg,1,15) as medListCodg
        FROM
        som_hi_invy_bas_info a
        LEFT JOIN
        som_chrg_detl_intf b
        ON
        a.k00 = b.unique_id
        WHERE a.id IN (
        <include refid="settleListIds"/>
        )
        GROUP BY
        a.id,b.med_list_codg
    </select>

    <select id="queryBusFeeBreakDownByK00"
            resultType="com.my.som.model.medicalQuality.BusFeeBreakDown">
        SELECT
        b.unique_id AS uniqueId,
        GROUP_CONCAT(DISTINCT substr(b.med_list_codg, 1, 15) SEPARATOR ';') AS medListCodg
        FROM
        som_chrg_detl_intf b
        WHERE
        LEFT(b.med_list_codg, 2) = '00'
        AND b.unique_id IN
        <foreach collection="needQueryList" item="k00" open="(" close=")" separator=",">
            #{k00}
        </foreach>

        GROUP BY
        b.unique_id
    </select>

    <sql id="settleListIds">
        select id
        from (
            select id
            from som_hi_invy_bas_info
            where data_log_id = #{logId,jdbcType=VARCHAR}
            LIMIT #{start,jdbcType=INTEGER},#{limit,jdbcType=INTEGER}
        ) a
    </sql>

    <!--  查询合并编码配置  -->
    <select id="queryMergeDisCodeCfg" resultType="com.my.som.vo.dataConfig.MergeDisCodeVo">
        SELECT
	        CONCAT_WS('@', sub_disediag_codg_1 , sub_disediag_codg_2 , sub_disediag_codg_3,merg_disediag_codg) AS merge_dis_code,
	        CONCAT_WS('@', sub_disediag_name_1 , sub_disediag_name_2 , sub_disediag_name_3,merg_disediag_name) AS merge_dis_name
        FROM
	        som_merg_codg_chk_cfg a
        where
	        a.vali_flag = '1'
    </select>

    <select id="queryCombCodeCfg" resultType="com.my.som.vo.dataConfig.CombCodeVO">
        SELECT
            combined_codes AS  combCodes,
            generated_code AS geneCode,
            is_type AS isType
        FROM som_comb_code_chk
        WHERE is_active = '1'
    </select>

    <select id="queryMainDiseaseDiagnosis" resultType="com.my.som.vo.common.TcmMainDiseaseDiagnosisVo">

<!--        subject_category_code AS subjectCategoryCode,-->
<!--        subject_category_name AS subjectCategoryName,-->
<!--        system_classification_code AS specializedSystemClassificationCode,-->
<!--        system_classification_name AS specializedSystemClassificationName,-->
<!--        disease_classification_code AS diseaseClassificationCode,-->
<!--        disease_classification_name AS diseaseClassificationName-->

        SELECT
        disease_classification_code AS diseaseClassificationCode,
        GROUP_CONCAT(disease_classification_name )AS diseaseClassificationName
        FROM
        som_tcm_main_disease_diagnosis
        WHERE
        vali_flag = '1'
        GROUP BY disease_classification_code
    </select>

    <select id="queryPrincipalDiagnosis" resultType="com.my.som.vo.common.TcmPrincipalDiagnosisVo">

<!--        category_code AS categoryCode,-->
<!--        category_name AS categoryName,-->
<!--        attribute_code AS attributeCode,-->
<!--        attribute_name AS attributeName,-->

        SELECT
        classification_code AS classificationCode,
        GROUP_CONCAT(classification_name )AS classificationName
        FROM
        som_tcm_principal_diagnosis
        WHERE
        vali_flag = '1'
        GROUP BY classification_code
    </select>

    <select id="queryDeptCodeList" resultType = "java.lang.String">

        SELECT
        code
        FROM
        som_dept
        WHERE
        active_flag = '1'
    </select>


    <!-- 新增校验日志详情 -->
    <insert id="insert">
        INSERT INTO som_script_chk_log(
        k00,ex_dscr,script_name
        )VALUES
        (
        #{k00,jdbcType=VARCHAR},
        #{exDscr,jdbcType=VARCHAR},
        #{scriptName,jdbcType=VARCHAR}
        )
    </insert>

    <!-- 删除质检清单日志异常 -->
    <delete id="deleteErrorBySettleId">
       DELETE FROM som_script_chk_log
       WHERE k00 = #{k00,jdbcType=VARCHAR}
    </delete>

    <select id="queryNotBeMainDiagCodeList" resultType = "com.my.som.model.common.SomIcdCrsp">
        SELECT
        code  as icdCodg,dscr
        FROM
        som_not_be_main_code
        WHERE
        type = 1
        and enable = 1
    </select>

    <select id="queryNotBeMainOprtCodeList" resultType = "com.my.som.model.common.SomIcdCrsp">
        SELECT
        code  as icdCodg,dscr
        FROM
        som_not_be_main_code
        WHERE
        type = 2
        and enable = 1
    </select>
    <select id="getCodeConflitMap" resultType="com.my.som.vo.common.DiagConFlictCodeVo">
        select
        conflict_code AS conflictCode,
        dscr,
        GROUP_CONCAT(cate_code) AS cateCode
        from som_code_conflit
        group by conflict_code, dscr
    </select>

    <select id="getOprtDiffBodyPartsMap" resultType = "com.my.som.vo.common.OprtDiffBodyParts">
        SELECT
        clean_code AS cleanCode,
        GROUP_CONCAT(oprt_code) AS oprtCode
        FROM
        som_oprt_diff_body_parts
        GROUP BY
        clean_code;
    </select>

    <select id="queryAllGrayCode" resultType = "com.my.som.vo.common.GrayCodeVo">
        SELECT
        code,name,type
        FROM
        som_gray_code
    </select>
    <select id="queryOprnChargeMapping" resultType = "com.my.som.vo.dataHandle.OprnChrgMapVo">
        select oprn_code AS oprnCode,
        oprn_name AS oprnName,
       pay_code AS payCode,
       pay_name AS payName
        from som_oprn_charge_mapping

    </select>
    <select id="selectOprnChargDetl" resultType = "com.my.som.vo.dataHandle.ChrgMappingRecord">
        select err_dscr AS errDscr ,type
        from som_chrg_detl_rcd
        where settle_list_id = #{id}
    </select>

    <insert id="insertChrgRcd" >
        INSERT INTO som_chrg_detl_rcd(
        settle_list_id,k00,err_dscr,type
        )
        <foreach collection="list" item="log" separator="," open="VALUES">
            (
            #{log.settleListId,jdbcType=VARCHAR},
            #{log.k00,jdbcType=VARCHAR},
                #{log.errDscr,jdbcType=VARCHAR},
                #{log.type,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>
    <delete id="deleteChrgRcd" >
        delete
        from som_chrg_detl_rcd
        where k00 = #{k00}
    </delete>

    <select id="selectTCMTreatmentCost" resultType = "double">

        SELECT
        COALESCE(
        (SELECT (IFNULL(D22, 0) + IFNULL(D64, 0) + IFNULL(D24, 0) + IFNULL(D25, 0))
        FROM som_hi_invy_bas_info
        WHERE id =  #{id}),
        0) AS TCMTreatmentCost;
    </select>
    <select id="selectFundAmtSum" resultType="java.math.BigDecimal">
        SELECT COALESCE(
        (SELECT SUM(a.fund_payamt)
        FROM som_fund_pay a
        INNER JOIN som_hi_invy_bas_info b on b.id = a.hi_setl_invy_id
        WHERE a.fund_pay_type IN ('310100', '390100', '310','390' ,'城镇职工基本医疗保险统筹基金','城乡居民基本医疗保险基金')
        AND b.k00 = #{k00}),
        0
        ) AS FundAmtSum;
    </select>


    <select id="selectFundRatio" resultType="java.util.Map">
        SELECT
        emp_fund_ratio as empFundRatio,
        resid_fund_ratio as residFundRatio
        from  som_hosp_info
        where  hospital_id = #{yljgdm}
    </select>
</mapper>
