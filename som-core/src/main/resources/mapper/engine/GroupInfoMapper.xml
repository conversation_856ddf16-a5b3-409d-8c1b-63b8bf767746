<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.som.mapper.engine.GroupInfoMapper">
    <!-- 根据医院获取分组器信息 -->
    <select id="getHospitalDipByHospitalId"
            resultType="com.my.som.vo.dataHandle.dataGroup.HospitalVo">
        select a.id            as grperInfoId,
               a.grper_type    as grperType,
               a.grper_ver     as grperVer,
               a.grper_url_dip as groupUrl,
               a.hospital_id   as hospitalId,
               b.medins_name   as medinsName,
               b.hosp_lv       as hospLv
        from som_grp_reqt_addr_info a
                 inner join som_hosp_info b on a.hospital_id = b.hospital_id
        <where>
            <if test="active_flag != null and active_flag != ''">
                AND a.active_flag = #{active_flag,jdbcType=VARCHAR}
            </if>
            <if test="hospital_id != null and hospital_id != ''">
                AND a.hospital_id = #{hospital_id,jdbcType=VARCHAR}
            </if>
            <if test="grper_type != null and grper_type != ''">
                AND a.grper_type = #{grper_type,jdbcType=VARCHAR}
            </if>
        </where>
    </select>

    <!-- 获取分组器信息 -->
    <select id="getGroupInfo"
            resultType="com.my.som.vo.dataHandle.dataGroup.HospitalVo">
        <include refid="groupInfo"/>
    </select>

    <!-- 获取所有分组器信息 -->
    <select id="queryAllGroupInfo" resultType="com.my.som.vo.dataHandle.dataGroup.HospitalVo">
        <include refid="groupInfo"/>
    </select>
    <!--  获取德阳不参与入组信息  -->
    <select id="selectDyExclude" resultType="com.my.som.vo.dyGroup.DyCustomizedVo">
        select TYPE                             AS type,
               v10_diag_code_v10_oprn_oprt_code AS v10DiagCodeV10OprnOprtCode,
               v10_diag_name_v10_oprn_oprt_name AS v10DiagNameV10OprnOprtName,
               v20_diag_code_v20_oprn_oprt_code AS v20DiagCodeV20OprnOprtCode,
               v20_diag_name_v20_oprn_oprt_name AS v20DiagNameV20OprnOprtName
        from som_dy_excl
    </select>
    <!--  获取德阳限定核心病种信息  -->
    <select id="selectDyLimited" resultType="com.my.som.vo.dyGroup.DyCustomizedVo">
        select oprn_oprt_codg     AS oprnOprtCodg,
               oprn_oprt_name     AS oprnOprtName,
               main_diag_sor_codg AS mainDiagSorCodg,
               main_diag_sor_name AS mainDiagSorName,
               qua_cor_dise_codg  AS quaCorDiseCodg
        from som_dy_qua_dise_info
    </select>

    <!--  获取德阳限定核心病种信息  -->
    <select id="querySomHiInvy" resultType="String">
        select a56
        from som_hi_invy_bas_info
        where DATA_LOG_ID = #{logId}
    </select>
    <!--  获取德阳限定核心病种信息  -->
    <select id="getInsuplcAdmdvs" resultType="String">
        SELECT insuplc_admdvs FROM `som_grp_reqt_addr_info` where grper_ver LIKE ('%省医保%')
    </select>


    <!--  获取德阳限定核心病种信息  -->
    <select id="selectPreGroupDIPResult" resultType="com.my.som.dto.patienInfo.DataGroupInfo">
        SELECT
        dip_codg AS group_code,
        dip_name AS group_name,
        #{insuplc} as 'insuplc',
        #{medcasno} as 'medcasno',
        #{fixmedins_code} as 'fixmedins_code',
        ROUND( AES_DECRYPT( UNHEX( dip_standard_inpf ), ${@com.my.som.common.constant.DrgConst @AES_ENCRYPT_KEY}), 2 ) AS benchmark_fee,
        ROUND( AES_DECRYPT( UNHEX( dip_standard_ipt_days ), ${@com.my.som.common.constant.DrgConst @AES_ENCRYPT_KEY}), 2 ) AS benchmark_day,
        `asst_list_age_grp` AS auxiliary_age,
        `asst_list_dise_sev_deg` AS auxiliary_illness,
        `asst_list_tmor_sev_deg` AS auxiliary_tumour,
        `auxiliary_burn` auxiliary_burn
        FROM
        `som_dip_standard`
        WHERE
        dip_codg = #{group_code}
        AND STANDARD_YEAR =  SUBSTRING(#{dscg_date},1,4)
        AND `asst_list_age_grp` = #{auxiliary_age}
        AND `asst_list_dise_sev_deg` = #{auxiliary_illness}
        AND `asst_list_tmor_sev_deg` = #{auxiliary_tumour}
        AND `auxiliary_burn` = #{auxiliary_burn}
        AND `hospital_id` = #{fixmedins_code}
    </select>

    <select id="selectPreGroupDRGResult" resultType="com.my.som.dto.patienInfo.DataGroupInfo">
        SELECT
        drg_codg AS group_code,
        drg_name AS group_name,
        #{insuplc} as 'insuplc',
        #{medcasno} as 'medcasno',
        #{fixmedins_code} as 'fixmedins_code',
        ROUND( AES_DECRYPT( UNHEX( standard_avg_fee ), ${@com.my.som.common.constant.DrgConst @AES_ENCRYPT_KEY}), 2 ) AS benchmark_fee,
        ROUND( AES_DECRYPT( UNHEX( standard_ave_hosp_day ), ${@com.my.som.common.constant.DrgConst @AES_ENCRYPT_KEY}), 2 ) AS benchmark_day,
        '未使用' AS auxiliary_age,
        '未使用' AS auxiliary_illness,
        '未使用' AS auxiliary_tumour,
        '未使用' auxiliary_burn
        FROM
        `som_drg_standard`
        WHERE
        drg_codg = #{group_code}
        AND STANDARD_YEAR =  SUBSTRING(#{dscg_date},1,4)
        AND `hospital_id` = #{fixmedins_code}
        AND `insuplc_admdvs` = #{insuplc_admdvs}
    </select>
    <!-- 获取分组器信息 -->
    <sql id="groupInfo">
        select a.id as grperInfoId, <!-- 分组器信息ID -->
        a.grper_type as grperType, <!-- 分组器类型 -->
        a.grper_ver as grperVer, <!-- 分组器版本 -->
        a.grper_url_dip as groupUrl, <!-- 分组器请求URL -->
        a.insuplc_admdvs as insuplcAdmdvs, <!-- 参保地医保区划 -->
        a.min_area_point as minAreaPoint, <!-- 区域最低分值 -->
        a.area_cost as areaCost, <!-- 参保地医保区划 -->
        a.hosp_cof as hospCof, <!-- 参保地医保区划 -->
        a.hosp_bedday_cof as hospBeddayCof <!-- 参保地医保区划 -->
        from som_grp_reqt_addr_info a
        <where>
            1 = 1
            <if test="active_flag != null and active_flag != ''">
                AND a.active_flag = #{active_flag,jdbcType=VARCHAR}
            </if>
            <if test="grper_type != null and grper_type != ''">
                AND a.grper_type = #{grper_type,jdbcType=VARCHAR}
            </if>
            <if test="insuplcAdmdvs != null and insuplcAdmdvs != ''">
                AND a.insuplc_admdvs = #{insuplcAdmdvs,jdbcType=VARCHAR}
            </if>
        </where>
    </sql>
</mapper>
