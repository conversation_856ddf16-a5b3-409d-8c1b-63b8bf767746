<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.som.mapper.engine.CDGroupMapper">

    <!-- 查询成都分组数据 -->
    <select id="queryCDGroupInfo" resultType="com.my.som.dto.dataHandle.CDGroupDataDTO" fetchSize="1000">
        SELECT
            a.k00, <!-- 病案唯一标识ID -->
            a.id as settleListId, <!-- 结算清单ID -->
            a.a03 as medcasType, <!-- 病案类型（1：西医，2：中医） -->
            a.hospital_id as hospitalId, <!-- 医疗机构ID -->
            a.a46c as grpType, <!-- 人群类别（医疗付款方式） -->
            a.d01 as iptSumfee, <!-- 住院总费用（病案首页） -->
            a.d09 as iptSumfeeInSelfpayAmt, <!-- 住院总费用其中自付金额（病案首页） -->
            a.opr_date, <!-- 操作时间 -->
            b.*
        FROM
        som_hi_invy_bas_info a
					LEFT JOIN (
						SELECT
								a.SETTLE_LIST_ID,
								a.ykd018 as ykd018,
								a.ykd019 as ykd019,
								a.ykd020 as ykd020,
								a.ykd021 as ykd021,
								a.ykd022 as ykd022,
								a.ykd023 as ykd023,
								a.ykd024 as ykd024,
								a.ykd025 as ykd025,
								a.akc023 as akc023,
								a.akb063 as akb063
						FROM khe8 a
				) b ON a.ID = b.SETTLE_LIST_ID
        WHERE a.ID NOT IN (
            select
                settle_list_id
            from som_setl_invy_chk_err_rcd
            <where>
                <if test="logId!=null and logId!=''">
                    AND  data_log_id = #{logId}
                </if>
                AND err_type in (
                    select error_code from som_medcas_in_group_cfg
                    <where>
                        <if test="enabFlag!=null and enabFlag!=''">
                            AND  enab_flag=#{enabFlag}
                        </if>
                    </where>
                )
            </where>
        )
        <if test="logId!=null and logId!=''">
            AND a.DATA_LOG_ID = #{logId}
        </if>
        <if test="activeFlag!=null and activeFlag!=''">
            AND a.ACTIVE_FLAG = #{activeFlag}
        </if>
    </select>
    
    <!-- 查询明细 -->
    <select id="queryDetail" resultType="com.my.som.dto.dataHandle.CDGroupDetailDTO">
        SELECT  a.settle_list_id,
                a.yka094,
                a.yka095,
                a.akc226,
                a.yka315,
                a.yke123
        FROM khe9 a
        <where>
            <if test="settle_list_id !=null and settle_list_id != ''">
                AND a.settle_list_id = #{settle_list_id,jdbcType=VARCHAR} <!-- 结算清单id -->
            </if>
        </where>
    </select>

</mapper>