<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.som.mapper.intoGroupCalculate.IntoGroupCalculateMapper">
    <insert id="addCalculate" useGeneratedKeys="true" keyColumn="ID" keyProperty="list.id">
        insert into ${dto.tabName}
            (<choose><when test="dto.type == 1"><include refid="dipSpecial"></include></when><when test="dto.type == 3">drg_codg,DRG_NAME,</when></choose> <include refid="calculateField"/>)
        values
            <foreach collection="list" item="item" separator=",">
                (
                <choose>
                    <when test="dto.type == 1">
                        #{item.dipCodg,jdbcType=VARCHAR},#{item.dipName,jdbcType=VARCHAR},#{item.usedAsstList,jdbcType=VARCHAR},#{item.asstListAgeGrp,jdbcType=VARCHAR},#{item.asstListDise,jdbcType=VARCHAR},#{item.asstListTmorSevDeg,jdbcType=VARCHAR},
                    </when>
                    <when test="dto.type == 3">
                        #{item.drgCodg,jdbcType=VARCHAR},#{item.drgName,jdbcType=VARCHAR},
                    </when>
                </choose> <include refid="insertValue"></include>
                )
            </foreach>
    </insert>
    <insert id="batchAddDetails">
        INSERT INTO ${tableName3} (IN_HOS_COST_AVG, IN_HOS_DAY_AVG, IN_HOS_COST_BEN, IN_HOS_DAY_BEN, prnt_menu_id_lv1_menu)
        VALUES <foreach collection="list" item="item" separator=",">
        (#{item.inHosCostAvg,jdbcType=VARCHAR}, #{item.inHosDayAvg,jdbcType=VARCHAR},#{item.inHosCostBen,jdbcType=VARCHAR}, #{item.inHosDayBen,jdbcType=VARCHAR} , #{item.id,jdbcType=VARCHAR})
    </foreach>
     </insert>
    <delete id="deleteCalculate">
        truncate ${tabName}
    </delete>
    <delete id="deleteRecord">
        truncate ${tableName2}
    </delete>
    <delete id="deleteCount">
        truncate ${tableName3}
    </delete>

    <update id="groupCalculate" useGeneratedKeys="true" keyProperty="id" keyColumn="ID">
        insert into bus_into_group_calculate_dip (gend,AGE,IPT_DAYS,dip_codg,DIP_NAME,
            c06c_0,c06c_1,c06c_2,c06c_3,c06c_4,c06c_5,c06c_6,c06c_7,c06c_8,c06c_9,c06c_10,c06c_11,c06c_12,c06c_13,c06c_14,c06c_15
            c35c_0,c35c_1,c35c_2,c35c_3,c35c_4,c35c_5,c35c_6)
        VALUES (#{gend,jdbcType=VARCHAR},#{age,jdbcType=VARCHAR},#{iptDays,jdbcType=VARCHAR}, #{dipCodg,jdbcType=VARCHAR},#{dipName,jdbcType=VARCHAR}
            <foreach collection="dis" item="item" open="," separator=",">
                #{item.value}
            </foreach>
            <foreach collection="ope" item="item" open="," separator=",">
                #{item.value}
            </foreach>
        )
    </update>
    <update id="batchAddRecord">
        insert into ${tableName2} (SETTLE_LIST_ID,prnt_menu_id_lv1_menu,K00)
        values
        <foreach collection="list" separator="," item="item">
            (#{item.settleListId,jdbcType=VARCHAR},#{item.id,jdbcType=VARCHAR},#{item.k00,jdbcType=VARCHAR})
        </foreach>
    </update>
    <update id="batchAddCalculate">
        insert into bus_into_group_calculate_dip <include refid="calculateField"/>
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.gend,jdbcType=VARCHAR},#{item.age,jdbcType=VARCHAR},#{item.iptDays,jdbcType=VARCHAR}, #{item.dipCodg,jdbcType=VARCHAR},#{item.dipName,jdbcType=VARCHAR},#{item.usedAsstList,jdbcType=VARCHAR},#{item.asstListAgeGrp,jdbcType=VARCHAR},#{item.asstListDise,jdbcType=VARCHAR},#{item.asstListTmorSevDeg,jdbcType=VARCHAR},
             #{item.c06c_0,jdbcType=VARCHAR},#{item.c06c_1,jdbcType=VARCHAR},#{item.c06c_2,jdbcType=VARCHAR},#{item.c06c_3,jdbcType=VARCHAR},#{item.c06c_4,jdbcType=VARCHAR},#{item.c06c_5,jdbcType=VARCHAR},#{item.c06c_6,jdbcType=VARCHAR},#{item.c06c_7,jdbcType=VARCHAR},#{item.c06c_8,jdbcType=VARCHAR},#{item.c06c_9,jdbcType=VARCHAR},#{item.c06c_10,jdbcType=VARCHAR},#{item.c06c_11,jdbcType=VARCHAR},#{item.c06c_12,jdbcType=VARCHAR},#{item.c06c_13,jdbcType=VARCHAR},#{item.c06c_14,jdbcType=VARCHAR},#{item.c06c_15,jdbcType=VARCHAR},
            #{item.c35c_0,jdbcType=VARCHAR},#{item.c35c_1,jdbcType=VARCHAR},#{item.c35c_2,jdbcType=VARCHAR},#{item.c35c_3,jdbcType=VARCHAR},#{item.c35c_4,jdbcType=VARCHAR},#{item.c35c_5,jdbcType=VARCHAR},#{item.c35c_6,jdbcType=VARCHAR}
             )
        </foreach>
    </update>

    <select id="queryLocal" resultType="com.my.som.vo.intoGroupCalculate.IntoGroupCalculateVo">
        SELECT m.*, m.c06c_0 as mainDiagDiseCodg, m.c35c_0 as mainDiagDiseName
        from (SELECT
        <choose>
            <when test="type == 1">
                AES_DECRYPT(UNHEX(b.dip_standard_ipt_days_same_lv),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) as inHosDayBen,
                AES_DECRYPT(UNHEX(b.dip_standard_avg_fee_same_lv),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) as inHosCostBen,
            </when>
            <when test="type == 3">
                AES_DECRYPT(UNHEX(b.standard_ave_hosp_day),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) as inHosDayBen,
                AES_DECRYPT(UNHEX(b.standard_avg_fee),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) as inHosCostBen,
            </when>
        </choose>
        <choose>
            <when test="type == 1">
                a.dip_codg AS dipCodg,
                a.DIP_NAME AS dipName,
            </when>
            <when test="type == 3">
                a.drg_codg AS drgCodg,
                a.DRG_NAME AS drgName,
            </when>
        </choose>
        <include refid="diagnoseAndOperationField"></include>
        FROM
        ${tabName} a
        <choose>
            <when test="type == 1">
                LEFT JOIN ${benchmarkTable} b on b.dip_codg = a.dip_codg AND b.STANDARD_YEAR = (SELECT max(STANDARD_YEAR) as lastYear FROM ${benchmarkTable})
            </when>
            <when test="type == 3">
                LEFT JOIN ${benchmarkTable} b on b.drg_codg = a.drg_codg AND b.STANDARD_YEAR = (SELECT max(STANDARD_YEAR) as lastYear FROM ${benchmarkTable})
            </when>
        </choose>
        LEFT JOIN (
            SELECT AVG(b.D01) as inHosCostAvg, AVG(b.B20) as inHosDayAvg, count(1) AS peopleNum, a.prnt_menu_id_lv1_menu, SUM(b.D01) AS sumfee
            FROM ${tableName2} a
            LEFT JOIN som_hi_invy_bas_info b ON a.SETTLE_LIST_ID = b.ID
            WHERE a.prnt_menu_id_lv1_menu in (
                SELECT ID FROM ${tabName} a
            <where>
                <include refid="queryCondition"></include>
            </where>
             )
            GROUP BY a.prnt_menu_id_lv1_menu
        ) c on c.prnt_menu_id_lv1_menu = a.ID
        <where>
            <include refid="queryCondition"></include>
        </where>)m

    </select>

    <select id="queryAllRecord" resultType="com.my.som.vo.intoGroupCalculate.IntoGroupCalculateVo">
        SELECT
            (CASE WHEN a.A14 &lt; 18 THEN '1'
                 WHEN a.A14 &gt;= 65 THEN '3' ELSE '2' END) as age,
            a.A12C AS gend,
--             a.A14 AS age,
            a.B20 AS intDays,
            a.K00 AS k00,
            a.id AS settleListId,
            b.*,
            c.* ,
            <choose>
                <when test="type == 1">
                    d.dip_codg as dipCodg,
                    d.DIP_NAME as dipName,
                    d.used_asst_list as usedAsstList,
                    d.asst_list_age_grp as asstListAgeGrp,
                    d.asst_list_dise as asstListDise,
                    d.asst_list_tmor_sev_deg as asstListTmorSevDeg
                </when>
                <when test="type == 3">
                    d.drg_codg as drgCodg,
                    d.DRG_NAME as drgName
                </when>
            </choose>
        FROM
            som_hi_invy_bas_info a
        LEFT JOIN (
                SELECT
                    SETTLE_LIST_ID,
                    <include refid="operationQuery"></include>
                FROM
                    som_oprn_oprt_info
                WHERE SETTLE_LIST_ID IN <foreach collection="ids" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                GROUP BY
                    SETTLE_LIST_ID
            ) b ON a.ID = b.SETTLE_LIST_ID
        LEFT JOIN (
                SELECT
                    SETTLE_LIST_ID,
                    <include refid="diagnoseQuery"></include>
                FROM
                    som_diag
                WHERE SETTLE_LIST_ID IN <foreach collection="ids" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                GROUP BY
                    SETTLE_LIST_ID
            ) c ON c.SETTLE_LIST_ID = a.ID
        LEFT JOIN ${groupRecordTable} d ON d.SETTLE_LIST_ID = a.id
        WHERE
          a.ACTIVE_FLAG = '1'
          AND d.grp_stas = '1'
          AND a.ID IN <foreach collection="ids" item="item" open="(" separator="," close=")">
        #{item}
    </foreach>
    </select>
    <select id="querySettleId" resultType="com.my.som.vo.intoGroupCalculate.IntoGroupCalculateVo">
        SELECT
            m.*,n.*,m.SETTLE_LIST_ID as settleListId,
            <choose>
                <when test="type == 1">
                    o.dip_codg AS dipCodg,
                    o.DIP_NAME AS dipName,
                    o.used_asst_list as usedAsstList,
                    o.asst_list_age_grp as asstListAgeGrp,
                    o.asst_list_dise as asstListDise,
                    o.asst_list_tmor_sev_deg as asstListTmorSevDeg,
                </when>
                <when test="type == 3">
                    o.drg_codg AS drgCodg,
                    o.DRG_NAME AS drgName,
                </when>
            </choose>
            p.A12C AS gend,
            (CASE WHEN p.A14 &lt; 18 THEN '1'
            WHEN p.A14 &gt;= 65 THEN '3' ELSE '2' END) as age,
--             p.A14 AS age,
            p.B20 AS intDays,
            p.K00 AS k00,
            p.id AS settleListId
        FROM
            (
                SELECT b.* FROM (
                    SELECT SETTLE_LIST_ID,
                    <include refid="diagnoseQuery"></include>
                    FROM som_diag a
                    GROUP BY a.SETTLE_LIST_ID
                ) b
            <where>
                <if test="dis != null">
                    <foreach collection="dis" item="item" separator="AND" open="AND">
                        b.${item.label} = #{item.value,jdbcType=VARCHAR}
                    </foreach>
                </if>
            </where>
            )m
        INNER JOIN (
                    SELECT b.* FROM (
                        SELECT SETTLE_LIST_ID,
                        <include refid="operationQuery"/>
                        FROM
                        som_oprn_oprt_info a
                        GROUP BY a.SETTLE_LIST_ID
                    ) b
                <where>
                    <if test="ope != null">
                        <foreach collection="ope" item="item" separator="AND" open="AND">
                            b.${item.label} = #{item.value,jdbcType=VARCHAR}
                        </foreach>
                    </if>
                </where>
            ) n ON m.SETTLE_LIST_ID = n.SETTLE_LIST_ID
        <choose>
            <when test="type == 1">
                LEFT JOIN som_dip_grp_rcd O ON m.SETTLE_LIST_ID = O.SETTLE_LIST_ID
            </when>
            <when test="type == 3">
                LEFT JOIN som_drg_grp_rcd O ON m.SETTLE_LIST_ID = O.SETTLE_LIST_ID
            </when>
        </choose>
        INNER JOIN som_hi_invy_bas_info p on m.SETTLE_LIST_ID = p.ID
        WHERE O.grp_stas = '1'
        <if test = "gend != null and gend != ''" >
            AND p.A12C = #{gend,jdbcType=VARCHAR}
        </if>
        <if test = "age != null and age != ''" >
            <if test="age == 1">
                AND p.A14 &lt; 18
            </if>
            <if test="age == 2">
                AND p.A14 &lt; 65 and p.A14 &gt;= 18
            </if>
            <if test="age == 3">
                p.A14 &gt;= 65
            </if>
        </if>
    </select>
    <select id="queryDiagnoseAndOperation" resultType="com.my.som.vo.intoGroupCalculate.IntoGroupCalculateVo">
        SELECT
        <choose>
            <when test="dto.type == 1">
                a.dip_codg AS dipCodg,
                a.DIP_NAME AS dipName,
            </when>
            <when test="dto.type == 3">
                a.drg_codg AS drgCodg,
                a.DRG_NAME AS drgName,
            </when>
        </choose>
            d.preCost,
            ROUND((IFNULL(d.preCost, 0) - IFNULL(c.sumfee,0)) / IFNULL(c.peopleNum,1),4) AS profitCost,
            <include refid="diagnoseAndOperationField"></include>
        FROM ${dto.tabName} a
        LEFT JOIN (
            SELECT AVG(b.D01) as inHosCostAvg, AVG(b.B20) as inHosDayAvg, count(1) AS peopleNum, a.prnt_menu_id_lv1_menu, SUM(b.D01) AS sumfee
            FROM ${dto.tableName2} a
            LEFT JOIN som_hi_invy_bas_info b ON a.SETTLE_LIST_ID = b.ID
            GROUP BY a.prnt_menu_id_lv1_menu
        ) c on c.prnt_menu_id_lv1_menu = a.ID
        LEFT JOIN (
            SELECT a.prnt_menu_id_lv1_menu, a.totlSco * b.price AS preCost
            FROM(
                select a.prnt_menu_id_lv1_menu, SUM(c.totl_sco) as totlSco
                FROM ${dto.tableName2} a
                LEFT JOIN ${dto.patientScoreTable} c on a.SETTLE_LIST_ID = c.SETTLE_LIST_ID
                GROUP BY prnt_menu_id_lv1_menu
            ) a
            CROSS JOIN (
                SELECT MAX(CASE WHEN `key` = 'CX_PRICE' THEN `VALUE` ELSE NULL END) AS cxPrice,
                MAX(case when `key` = 'CZ_PRICE' THEN `VALUE` ELSE NULL END) AS czPrice,
                MAX(case when `key` = 'PRICE' THEN `VALUE` ELSE NULL END) AS price
                FROM ${dto.cfgCommon}
                WHERE type = 'PREDICTED_PRICE'
                GROUP BY type
            ) b
        )d ON d.prnt_menu_id_lv1_menu = a.ID
        WHERE a.ID in <foreach collection="ids" item="item" open="(" separator="," close=")">
        #{item}
        </foreach>
        <if test="dto.profitValue!= null and dto.profitValue != ''">
            <if test="dto.profitValue == 0">
                AND ROUND(IFNULL(d.preCost, 0) - IFNULL(c.sumfee,0),4) &lt; 0
            </if>
            <if test="dto.profitValue == 1">
                AND ROUND(IFNULL(d.preCost, 0) - IFNULL(c.sumfee,0),4) &gt;= 0
            </if>
        </if>
        <if test="dto.gend != null and dto.gend != ''">
            <if test="dto.gend == 1">
                AND a.gend = 1
            </if>
            <if test="dto.gend == 2">
                AND a.gend = 2
            </if>
        </if>
        <if test="dto.age != null and dto.age != ''">
            <if test="dto.age == 1">
                AND a.AGE = 1
            </if>
            <if test="dto.age == 2">
                AND a.AGE = 2
            </if>
            <if test="dto.age == 3">
                AND a.AGE = 3
            </if>
        </if>
    </select>
    <select id="selectCountInfo" resultType="com.my.som.vo.intoGroupCalculate.IntoGroupCalculateVo">
        SELECT  m.*,
               <choose>
                   <when test="type == 1">
                       AES_DECRYPT(UNHEX(n.dip_standard_ipt_days_same_lv),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) as inHosDayBen,
                       AES_DECRYPT(UNHEX(n.dip_standard_avg_fee_same_lv),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) as inHosCostBen
                   </when>
                   <when test="type == 3">
                        AES_DECRYPT(UNHEX(n.standard_ave_hosp_day),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) as inHosDayBen,
                        AES_DECRYPT(UNHEX(n.standard_avg_fee),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) as inHosCostBen
                   </when>
               </choose>
        FROM (
            SELECT
            round(AVG(a.B20),2) as inHosDayAvg, round(AVG(a.D01),2) as inHosCostAvg,
            <choose>
                <when test="type == 1">
                    b.dip_codg as dipCodg,
                    b.used_asst_list as usedAsstList,
                    b.asst_list_age_grp as asstListAgeGrp,
                    b.asst_list_dise as asstListDise,
                    b.asst_list_tmor_sev_deg as asstListTmorSevDeg
                </when>
                <when test="type == 3">
                    b.drg_codg as drgCodg
                </when>
            </choose>
            FROM som_hi_invy_bas_info a
            LEFT JOIN ${groupRecordTable} b on a.ID = b.SETTLE_LIST_ID
            <where>
                a.ID in <foreach collection="list" item="item" open="(" separator="," close=")">
                #{item.settleListId,jdbcType=VARCHAR}
            </foreach>
            </where>
            <choose>
                <when test="type == 1">
                    GROUP BY b.dip_codg, b.used_asst_list, b.asst_list_age_grp, b.asst_list_dise, b.asst_list_tmor_sev_deg
                </when>
                <when test="type == 3">
                    GROUP BY b.drg_codg
                </when>
            </choose>
            )m
        <if test="type == 1">
            LEFT JOIN ${benchmarkTable} n
                on m.dipCodg = n.dip_codg
                AND m.usedAsstList = n.is_used_asst_list
                AND m.asstListAgeGrp = n.asst_list_age_grp
                AND m.asstListDise = n.asst_list_dise_sev_deg
                AND m.asstListTmorSevDeg = n.asst_list_tmor_sev_deg
                AND n.STANDARD_YEAR = (SELECT max(STANDARD_YEAR) as lastYear FROM ${benchmarkTable})
        </if>
        <if test="type == 3">
            LEFT JOIN ${benchmarkTable} n on m.drgCodg = n.drg_codg
            AND n.STANDARD_YEAR = (SELECT max(STANDARD_YEAR) as lastYear FROM ${benchmarkTable})
        </if>
    </select>
    <select id="queryPage" resultType="java.lang.Integer">
        select a.id from som_hi_invy_bas_info a
        LEFT JOIN ${groupRecordTable} b ON b.SETTLE_LIST_ID = a.id
        where a.ACTIVE_FLAG = '1' AND b.grp_stas = '1'
        limit #{start,jdbcType=INTEGER}, #{limit,jdbcType=INTEGER}
    </select>
    <select id="queryCostDetails" resultType="com.my.som.paths.entity.PathsItems">
        select g.*,
               ROUND(g.catalogueNum / ct, 2) AS useAmount,
               ROUND(g.useNum / ct * 100, 2) AS useRate
        from (
                 select f.*,
                        CHAR_LENGTH(useNumStr)-CHAR_LENGTH(REPLACE(useNumStr,',','')) + 1 AS useNum
                 from (
                          select a.med_list_codg AS catalogueCode,
                                 GROUP_CONCAT(distinct mdtrt_id) as useNumStr,
                                 count(1) as catalogueNum,
                                 b.ct
                          from som_chrg_detl_intf a
                                   cross join (
                                     select count(1)over() as ct from som_chrg_detl_intf where mdtrt_id in (
                                        SELECT K00
                                        FROM ${tableName2}
                                        WHERE prnt_menu_id_lv1_menu in (
                                            <foreach collection="ids" item="item" separator=",">
                                                #{item,jdbcType=ARRAY}
                                            </foreach>)
                                          )
                          group by mdtrt_id
                          ) b
                          where mdtrt_id in (
                              SELECT K00
                              FROM ${tableName2}
                              WHERE prnt_menu_id_lv1_menu in (
                                <foreach collection="ids" item="item" separator=",">
                                    #{item,jdbcType=ARRAY}
                                </foreach>
                              ))
                          group by a.med_list_codg, b.ct
                      ) f
             ) g

    </select>

    <sql id="queryCondition">
        <if test="queryCondition != null and queryCondition == 1">
            <if test = "gend != null and gend != ''" >
                AND a.gend = #{gend,jdbcType=VARCHAR}
            </if>
            <if test = "age != null and age != ''" >
                AND a.AGE = #{age,jdbcType=VARCHAR}
            </if>
            <if test="dis != null and dis.size > 0">
                <foreach collection="dis" item="item" separator="AND" open="AND">
                    ${item.label} = #{item.value,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="ope != null and ope.size > 0">
                <foreach collection="ope" item="item" separator="AND" open="AND">
                    ${item.label} = #{item.value,jdbcType=VARCHAR}
                </foreach>
            </if>
        </if>

        <if test="queryCondition != null and queryCondition == 0">
            <if test="type == 1">
                a.dip_codg = #{dipCodg,jdbcType=VARCHAR}
            </if>
            <if test="type == 3">
                a.drg_codg = #{drgCodg,jdbcType=VARCHAR}
            </if>
        </if>
    </sql>
    <sql id="insertValue">
        #{item.gend,jdbcType=VARCHAR},#{item.age,jdbcType=VARCHAR},#{item.iptDays,jdbcType=VARCHAR},
        #{item.c06c_0,jdbcType=VARCHAR},#{item.c06c_1,jdbcType=VARCHAR},#{item.c06c_2,jdbcType=VARCHAR},#{item.c06c_3,jdbcType=VARCHAR},#{item.c06c_4,jdbcType=VARCHAR},#{item.c06c_5,jdbcType=VARCHAR},#{item.c06c_6,jdbcType=VARCHAR},#{item.c06c_7,jdbcType=VARCHAR},#{item.c06c_8,jdbcType=VARCHAR},#{item.c06c_9,jdbcType=VARCHAR},#{item.c06c_10,jdbcType=VARCHAR},#{item.c06c_11,jdbcType=VARCHAR},#{item.c06c_12,jdbcType=VARCHAR},#{item.c06c_13,jdbcType=VARCHAR},#{item.c06c_14,jdbcType=VARCHAR},#{item.c06c_15,jdbcType=VARCHAR},
        #{item.c35c_0,jdbcType=VARCHAR},#{item.c35c_1,jdbcType=VARCHAR},#{item.c35c_2,jdbcType=VARCHAR},#{item.c35c_3,jdbcType=VARCHAR},#{item.c35c_4,jdbcType=VARCHAR},#{item.c35c_5,jdbcType=VARCHAR},#{item.c35c_6,jdbcType=VARCHAR},
        #{item.c07n_0,jdbcType=VARCHAR},#{item.c07n_1,jdbcType=VARCHAR},#{item.c07n_2,jdbcType=VARCHAR},#{item.c07n_3,jdbcType=VARCHAR},#{item.c07n_4,jdbcType=VARCHAR},#{item.c07n_5,jdbcType=VARCHAR},#{item.c07n_6,jdbcType=VARCHAR},#{item.c07n_7,jdbcType=VARCHAR},#{item.c07n_8,jdbcType=VARCHAR},#{item.c07n_9,jdbcType=VARCHAR},#{item.c07n_10,jdbcType=VARCHAR},#{item.c07n_11,jdbcType=VARCHAR},#{item.c07n_12,jdbcType=VARCHAR},#{item.c07n_13,jdbcType=VARCHAR},#{item.c07n_14,jdbcType=VARCHAR},#{item.c07n_15,jdbcType=VARCHAR},
        #{item.c36n_0,jdbcType=VARCHAR},#{item.c36n_1,jdbcType=VARCHAR},#{item.c36n_2,jdbcType=VARCHAR},#{item.c36n_3,jdbcType=VARCHAR},#{item.c36n_4,jdbcType=VARCHAR},#{item.c36n_5,jdbcType=VARCHAR},#{item.c36n_6,jdbcType=VARCHAR}
    </sql>
    <sql id="operationQuery">
        MAX( CASE WHEN seq = 0 THEN c35c ELSE NULL END ) AS 'c35c_0',
        MAX( CASE WHEN seq = 0 THEN c36n ELSE NULL END ) AS 'c36n_0',
        MAX( CASE WHEN seq = 1 THEN c35c ELSE NULL END ) AS 'c35c_1',
        MAX( CASE WHEN seq = 1 THEN c36n ELSE NULL END ) AS 'c36n_1',
        MAX( CASE WHEN seq = 2 THEN c35c ELSE NULL END ) AS 'c35c_2',
        MAX( CASE WHEN seq = 2 THEN c36n ELSE NULL END ) AS 'c36n_2',
        MAX( CASE WHEN seq = 3 THEN c35c ELSE NULL END ) AS 'c35c_3',
        MAX( CASE WHEN seq = 3 THEN c36n ELSE NULL END ) AS 'c36n_3',
        MAX( CASE WHEN seq = 4 THEN c35c ELSE NULL END ) AS 'c35c_4',
        MAX( CASE WHEN seq = 4 THEN c36n ELSE NULL END ) AS 'c36n_4',
        MAX( CASE WHEN seq = 5 THEN c35c ELSE NULL END ) AS 'c35c_5',
        MAX( CASE WHEN seq = 5 THEN c36n ELSE NULL END ) AS 'c36n_5',
        MAX( CASE WHEN seq = 6 THEN c35c ELSE NULL END ) AS 'c35c_6',
        MAX( CASE WHEN seq = 6 THEN c36n ELSE NULL END ) AS 'c36n_6'
    </sql>
    <sql id="diagnoseQuery">
        MAX( CASE WHEN seq = 0 THEN dscg_diag_codg ELSE NULL END ) AS 'c06c_0',
        MAX( CASE WHEN seq = 0 THEN dscg_diag_name ELSE NULL END ) AS 'c07n_0',
        MAX( CASE WHEN seq = 1 THEN dscg_diag_codg ELSE NULL END ) AS 'c06c_1',
        MAX( CASE WHEN seq = 1 THEN dscg_diag_name ELSE NULL END ) AS 'c07n_1',
        MAX( CASE WHEN seq = 2 THEN dscg_diag_codg ELSE NULL END ) AS 'c06c_2',
        MAX( CASE WHEN seq = 2 THEN dscg_diag_name ELSE NULL END ) AS 'c07n_2',
        MAX( CASE WHEN seq = 3 THEN dscg_diag_codg ELSE NULL END ) AS 'c06c_3',
        MAX( CASE WHEN seq = 3 THEN dscg_diag_name ELSE NULL END ) AS 'c07n_3',
        MAX( CASE WHEN seq = 4 THEN dscg_diag_codg ELSE NULL END ) AS 'c06c_4',
        MAX( CASE WHEN seq = 4 THEN dscg_diag_name ELSE NULL END ) AS 'c07n_4',
        MAX( CASE WHEN seq = 5 THEN dscg_diag_codg ELSE NULL END ) AS 'c06c_5',
        MAX( CASE WHEN seq = 5 THEN dscg_diag_name ELSE NULL END ) AS 'c07n_5',
        MAX( CASE WHEN seq = 6 THEN dscg_diag_codg ELSE NULL END ) AS 'c06c_6',
        MAX( CASE WHEN seq = 6 THEN dscg_diag_name ELSE NULL END ) AS 'c07n_6',
        MAX( CASE WHEN seq = 7 THEN dscg_diag_codg ELSE NULL END ) AS 'c06c_7',
        MAX( CASE WHEN seq = 7 THEN dscg_diag_name ELSE NULL END ) AS 'c07n_7',
        MAX( CASE WHEN seq = 8 THEN dscg_diag_codg ELSE NULL END ) AS 'c06c_8',
        MAX( CASE WHEN seq = 8 THEN dscg_diag_name ELSE NULL END ) AS 'c07n_8',
        MAX( CASE WHEN seq = 9 THEN dscg_diag_codg ELSE NULL END ) AS 'c06c_9',
        MAX( CASE WHEN seq = 9 THEN dscg_diag_name ELSE NULL END ) AS 'c07n_9',
        MAX( CASE WHEN seq = 10 THEN dscg_diag_codg ELSE NULL END ) AS 'c06c_10',
        MAX( CASE WHEN seq = 10 THEN dscg_diag_name ELSE NULL END ) AS 'c07n_10',
        MAX( CASE WHEN seq = 11 THEN dscg_diag_codg ELSE NULL END ) AS 'c06c_11',
        MAX( CASE WHEN seq = 11 THEN dscg_diag_name ELSE NULL END ) AS 'c07n_11',
        MAX( CASE WHEN seq = 12 THEN dscg_diag_codg ELSE NULL END ) AS 'c06c_12',
        MAX( CASE WHEN seq = 12 THEN dscg_diag_name ELSE NULL END ) AS 'c07n_12',
        MAX( CASE WHEN seq = 13 THEN dscg_diag_codg ELSE NULL END ) AS 'c06c_13',
        MAX( CASE WHEN seq = 13 THEN dscg_diag_name ELSE NULL END ) AS 'c07n_13',
        MAX( CASE WHEN seq = 14 THEN dscg_diag_codg ELSE NULL END ) AS 'c06c_14',
        MAX( CASE WHEN seq = 14 THEN dscg_diag_name ELSE NULL END ) AS 'c07n_14',
        MAX( CASE WHEN seq = 14 THEN dscg_diag_codg ELSE NULL END ) AS 'c06c_15',
        MAX( CASE WHEN seq = 14 THEN dscg_diag_name ELSE NULL END ) AS 'c07n_15'
    </sql>
    <sql id="diagnoseAndOperationField">
                a.ID AS id,
                a.gend AS gend,
                a.AGE AS age,
                c.inHosDayAvg,
                c.inHosCostAvg,
                c.peopleNum,
                c.sumfee,
                c35c_0 AS 'c35c_0',
                c36n_0 AS 'c36n_0',
                c35c_1 AS 'c35c_1',
                c36n_1 AS 'c36n_1',
                c35c_2 AS 'c35c_2',
                c36n_2 AS 'c36n_2',
                c35c_3 AS 'c35c_3',
                c36n_3 AS 'c36n_3',
                c35c_4 AS 'c35c_4',
                c36n_4 AS 'c36n_4',
                c35c_5 AS 'c35c_5',
                c36n_5 AS 'c36n_5',
                c35c_6 AS 'c35c_6',
                c36n_6 AS 'c36n_6',
                c06c_0 AS 'c06c_0',
                c07n_0 AS 'c07n_0',
                c06c_1 AS 'c06c_1',
                c07n_1 AS 'c07n_1',
                c06c_2 AS 'c06c_2',
                c07n_2 AS 'c07n_2',
                c06c_3 AS 'c06c_3',
                c07n_3 AS 'c07n_3',
                c06c_4 AS 'c06c_4',
                c07n_4 AS 'c07n_4',
                c06c_5 AS 'c06c_5',
                c07n_5 AS 'c07n_5',
                c06c_6 AS 'c06c_6',
                c07n_6 AS 'c07n_6',
                c06c_7 AS 'c06c_7',
                c07n_7 AS 'c07n_7',
                c06c_8 AS 'c06c_8',
                c07n_8 AS 'c07n_8',
                c06c_9 AS 'c06c_9',
                c07n_9 AS 'c07n_9',
                c06c_10 AS 'c06c_10',
                c07n_10 AS 'c07n_10',
                c06c_11 AS 'c06c_11',
                c07n_11 AS 'c07n_11',
                c06c_12 AS 'c06c_12',
                c07n_12 AS 'c07n_12',
                c06c_13 AS 'c06c_13',
                c07n_13 AS 'c07n_13',
                c06c_14 AS 'c06c_14',
                c07n_14 AS 'c07n_14',
                c06c_15 AS 'c06c_15',
                c07n_15 AS 'c07n_15'
    </sql>
    <sql id="dipSpecial">
        dip_codg,DIP_NAME,used_asst_list,asst_list_age_grp,asst_list_dise,asst_list_tmor_sev_deg,
    </sql>
    <sql id="calculateField">
        gend,AGE,IPT_DAYS,
        c06c_0,c06c_1,c06c_2,c06c_3,c06c_4,c06c_5,c06c_6,c06c_7,c06c_8,c06c_9,c06c_10,c06c_11,c06c_12,c06c_13,c06c_14,c06c_15,
        c35c_0,c35c_1,c35c_2,c35c_3,c35c_4,c35c_5,c35c_6,
        c07n_0,c07n_1,c07n_2,c07n_3,c07n_4,c07n_5,c07n_6,c07n_7,c07n_8,c07n_9,c07n_10,c07n_11,c07n_12,c07n_13,c07n_14,c07n_15,
        c36n_0,c36n_1,c36n_2,c36n_3,c36n_4,c36n_5,c36n_6
    </sql>
    <sql id="calculateValue">
        #{calculateVo.gend,jdbcType=VARCHAR},#{calculateVo.age,jdbcType=VARCHAR},#{calculateVo.iptDays,jdbcType=VARCHAR}, #{calculateVo.dipCodg,jdbcType=VARCHAR},#{calculateVo.dipName,jdbcType=VARCHAR},#{calculateVo.usedAsstList,jdbcType=VARCHAR},#{calculateVo.asstListAgeGrp,jdbcType=VARCHAR},#{calculateVo.asstListDise,jdbcType=VARCHAR},#{calculateVo.asstListTmorSevDeg,jdbcType=VARCHAR},
        #{calculateVo.c06c_0,jdbcType=VARCHAR},#{calculateVo.c06c_1,jdbcType=VARCHAR},#{calculateVo.c06c_2,jdbcType=VARCHAR},#{calculateVo.c06c_3,jdbcType=VARCHAR},#{calculateVo.c06c_4,jdbcType=VARCHAR},#{calculateVo.c06c_5,jdbcType=VARCHAR},#{calculateVo.c06c_6,jdbcType=VARCHAR},#{calculateVo.c06c_7,jdbcType=VARCHAR},#{calculateVo.c06c_8,jdbcType=VARCHAR},#{calculateVo.c06c_9,jdbcType=VARCHAR},#{calculateVo.c06c_10,jdbcType=VARCHAR},#{calculateVo.c06c_11,jdbcType=VARCHAR},#{calculateVo.c06c_12,jdbcType=VARCHAR},#{calculateVo.c06c_13,jdbcType=VARCHAR},#{calculateVo.c06c_14,jdbcType=VARCHAR},#{calculateVo.c06c_15,jdbcType=VARCHAR},
        #{calculateVo.c35c_0,jdbcType=VARCHAR},#{calculateVo.c35c_1,jdbcType=VARCHAR},#{calculateVo.c35c_2,jdbcType=VARCHAR},#{calculateVo.c35c_3,jdbcType=VARCHAR},#{calculateVo.c35c_4,jdbcType=VARCHAR},#{calculateVo.c35c_5,jdbcType=VARCHAR},#{calculateVo.c35c_6,jdbcType=VARCHAR}
    </sql>
</mapper>