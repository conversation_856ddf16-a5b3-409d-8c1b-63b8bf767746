<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.som.mapper.supervision.MedicalRecordSupervisionMapper">
    <!-- 查询是否已保存原始数据-somDrgGrpInfo -->
    <select id="queryBusKeyRecordCount" resultType="int">
        SELECT COUNT(1) FROM som_init_grp WHERE K00 = #{k00}
    </select>

    <insert id="insertOriginalBusKeyData">
        INSERT INTO som_init_grp
            (
                K00,
                <if test="grperType == 1">
                    dip_codg,
                    DIP_NAME,
                </if>
                <if test="grperType == 3">
                    drg_codg,
                    DRG_NAME,
                </if>
                totl_sco
            )
        SELECT
               a.K00,
               <if test="grperType == 1">
                    c.dip_codg,
                    c.DIP_NAME,
               </if>
               <if test="grperType == 3">
                    c.drg_codg,
                    c.DRG_NAME,
               </if>
               d.totl_sco
        FROM
            som_hi_invy_bas_info a
            <if test="grperType == 1">
                LEFT JOIN som_dip_grp_info c ON a.id = c.SETTLE_LIST_ID
                LEFT JOIN som_dip_sco d ON a.id = d.SETTLE_LIST_ID
            </if>
            <if test="grperType == 3">
                LEFT JOIN som_drg_grp_info c ON a.id = c.SETTLE_LIST_ID
                LEFT JOIN som_drg_sco d ON a.id = d.SETTLE_LIST_ID
            </if>
        WHERE
            a.id = #{oldId}
    </insert>

    <!-- 查询是否已保存原始数据-诊断 -->
    <select id="queryDiseaseRecordCount" resultType="int">
        SELECT COUNT(1) FROM som_init_diag WHERE K00 = #{k00}
    </select>

    <!-- 添加原始诊断数据 -->
    <insert id="insertOriginalDiseaseData">
        INSERT INTO som_init_diag
            (
                K00,
                seq,
                TYPE,
                dscg_diag_codg,
                dscg_diag_name,
                dscg_diag_adm_cond,
                dscg_diag_dscg_cond
            )
        SELECT
                #{k00},
                seq,
                TYPE,
                dscg_diag_codg,
                dscg_diag_name,
                dscg_diag_adm_cond,
                dscg_diag_dscg_cond
        FROM som_diag
        WHERE SETTLE_LIST_ID = #{oldId}
    </insert>

    <!-- 查询是否已保存原始数据-手术 -->
    <select id="queryOperateRecordCount" resultType="int">
        SELECT COUNT(1) FROM som_init_oprn_oprt_info WHERE K00 = #{k00}
    </select>
    <!-- 查询监管信息是否为空 -->
    <select id="queryBusKeyCount" resultType="int">
        SELECT COUNT(1) FROM som_hi_invy_supn_info WHERE K00 = #{k00}
    </select>

    <!-- 添加原始手术数据 -->
    <insert id="insertOriginalOperateData">
        INSERT INTO som_init_oprn_oprt_info
        (
            K00,
            seq,
            C35C,
            C36N,
            oprn_oprt_date,
            oprn_oprt_lv,
            C39C,
            oprn_oprt_oper_name,
            oprn_oprt_1_asit,
            oprn_oprt_2_asit,
            C42,
            C43,
            oprn_oprt_anst_dr_code,
            oprn_oprt_anst_dr_name,
            OPRN_OPRT_BEGNTIME,
            OPRN_OPRT_ENDTIME,
            ANST_BEGNTIME,
            ANST_ENDTIME
        )
        SELECT
            #{k00},
            seq,
            C35C,
            C36N,
            oprn_oprt_date,
            oprn_oprt_lv,
            C39C,
            oprn_oprt_oper_name,
            oprn_oprt_1_asit,
            oprn_oprt_2_asit,
            C42,
            C43,
            oprn_oprt_anst_dr_code,
            oprn_oprt_anst_dr_name,
            OPRN_OPRT_BEGNTIME,
            OPRN_OPRT_ENDTIME,
            ANST_BEGNTIME,
            ANST_ENDTIME
        FROM som_oprn_oprt_info
        WHERE SETTLE_LIST_ID = #{oldId}
    </insert>

    <!-- 查询调整数据 -->
    <select id="queryAdjustData" resultType="com.my.som.vo.supervision.MedicalRecordSupervisionVo">
        SELECT
            a.k00 AS k00,
            a.a48 AS a48,
            a.a11 AS a11,
            a.b16c AS b16c,
            e.NAME AS b16n,
            a.b25c AS b25c,
            a.b25n AS b25n,
            f.dscg_diag_codg AS initDscgMainDiagCodg,
            f.dscg_diag_name AS initDscgMainDiagName,
            g.dscg_diag_codg AS mainDiagCodg,
            g.dscg_diag_name AS c04cNow,
            <if test="grperType == 1">
                b.dip_codg AS initDipCodg,
                b.DIP_NAME AS initDipName,
                c.dip_codg AS currDipCodg,
                c.DIP_NAME AS currDipName,
                b.totl_sco AS initSco,
                d.totl_sco AS currSco
            </if>
            <if test="grperType == 3">
                b.drg_codg AS initDrgCodg,
                b.DRG_NAME AS initDrgName,
                c.drg_codg AS currDrgCodg,
                c.DRG_NAME AS currDrgName,
                b.totl_sco AS initSco,
                d.totl_sco AS currSco
            </if>
        FROM
            som_hi_invy_bas_info a
                <if test="grperType == 1">
                    LEFT JOIN som_init_grp b ON a.K00 = b.K00
                    LEFT JOIN som_dip_grp_info c ON a.id = c.SETTLE_LIST_ID
                    LEFT JOIN som_dip_sco d ON a.id = d.SETTLE_LIST_ID
                    LEFT JOIN som_dept e ON a.b16c = e.CODE
                    LEFT JOIN som_init_diag f ON a.k00 = f.k00
                    LEFT JOIN som_diag g ON a.id = g.SETTLE_LIST_ID
                </if>
                <if test="grperType == 3">
                    LEFT JOIN som_init_grp b ON a.K00 = b.K00
                    LEFT JOIN som_drg_grp_info c ON a.id = c.SETTLE_LIST_ID
                    LEFT JOIN som_drg_sco d ON a.id = d.SETTLE_LIST_ID
                    LEFT JOIN som_dept e ON a.b16c = e.CODE
                    LEFT JOIN som_init_diag f ON a.k00 = f.k00
                    LEFT JOIN som_diag g ON a.id = g.SETTLE_LIST_ID
                </if>
        WHERE
            a.K00 = #{k00} and f.seq = '0' and g.seq = '0'
    </select>

    <!-- 删除旧的监管信息 -->
    <delete id="deleteOldSupervisionInfo">
        DELETE FROM som_hi_invy_supn_info WHERE K00 = #{k00}
    </delete>

    <!-- 查询原始诊断数据 -->
    <select id="queryOriginalDiseaseData" resultType="com.my.som.vo.supervision.OriginalDiseaseVo">
        SELECT
            K00 AS k00,
            seq AS seq,
            TYPE AS type,
            dscg_diag_codg AS dscg_diag_codg,
            dscg_diag_name AS dscg_diag_name,
            dscg_diag_adm_cond AS dscg_diag_adm_cond,
            dscg_diag_dscg_cond AS dscg_diag_dscg_cond
        FROM som_init_diag WHERE K00 = #{k00}
    </select>

    <!-- 查询原始手术数据 -->
    <select id="queryOriginalOperateData" resultType="com.my.som.vo.supervision.OriginalOperateVo">
        SELECT
            K00 AS k00,
            seq AS seq,
            C35C AS c35c,
            C36N AS c36n,
            oprn_oprt_date AS oprn_oprt_date,
            oprn_oprt_lv AS oprn_oprt_lv,
            C39C AS c39c,
            oprn_oprt_oper_name AS oprn_oprt_oper_name,
            oprn_oprt_1_asit AS oprn_oprt_1_asit,
            oprn_oprt_2_asit AS oprn_oprt_2_asit,
            C42 AS c42,
            C43 AS c43,
            oprn_oprt_anst_dr_code AS oprn_oprt_anst_dr_code,
            oprn_oprt_anst_dr_name AS oprn_oprt_anst_dr_name,
            OPRN_OPRT_BEGNTIME AS oprnOprtBeginTime,
            OPRN_OPRT_ENDTIME AS oprnOprtEndTime,
            ANST_BEGNTIME AS anstBeginTime,
            ANST_ENDTIME AS anstEndTime
        FROM som_init_oprn_oprt_info WHERE K00 = #{k00}
    </select>

    <!-- 查询最新诊断信息 -->
    <select id="queryNowDiseaseData" resultType="com.my.som.vo.supervision.NowDiseaseVo">
        SELECT
            SETTLE_LIST_ID AS settleListId,
            seq AS seq,
            TYPE AS type,
            dscg_diag_codg AS dscg_diag_codg,
            dscg_diag_name AS dscg_diag_name,
            dscg_diag_adm_cond AS dscg_diag_adm_cond,
            dscg_diag_dscg_cond AS dscg_diag_dscg_cond
        FROM som_diag WHERE SETTLE_LIST_ID = #{newId}
    </select>

    <select id="queryNowOperateData" resultType="com.my.som.vo.supervision.NowOperateVo">
        SELECT
            SETTLE_LIST_ID AS settleListId,
            seq AS seq,
            C35C AS c35c,
            C36N AS c36n,
            oprn_oprt_date AS oprn_oprt_date,
            oprn_oprt_lv AS oprn_oprt_lv,
            C39C AS c39c,
            oprn_oprt_oper_name AS oprn_oprt_oper_name,
            oprn_oprt_1_asit AS oprn_oprt_1_asit,
            oprn_oprt_2_asit AS oprn_oprt_2_asit,
            C42 AS c42,
            C43 AS c43,
            oprn_oprt_anst_dr_code AS oprn_oprt_anst_dr_code,
            oprn_oprt_anst_dr_name AS oprn_oprt_anst_dr_name,
            OPRN_OPRT_BEGNTIME AS oprnOprtBeginTime,
            OPRN_OPRT_ENDTIME AS oprnOprtEndTime,
            ANST_BEGNTIME AS anstBeginTime,
            ANST_ENDTIME AS anstEndTime
        FROM som_oprn_oprt_info WHERE SETTLE_LIST_ID = #{newId}
    </select>

    <!-- 添加监管信息 -->
    <insert id="insertSupervisionInfo">
        INSERT INTO som_hi_invy_supn_info(
            K00,A48,A11,B16C,B16N,B25C,B25N,init_dscg_main_diag_codg,init_dscg_main_diag_name,main_diag_codg,main_diag_name,
            init_dip_codg,init_dip_name,curr_dip_codg,curr_dip_name,
            init_drg_codg,init_drg_name,curr_drg_codg,curr_drg_name,
            init_sco,curr_sco,init_dif,curr_dif,dif_prop,dif_val,modi_psn,
            oprt_rcd
        ) VALUES (
                     #{vo.k00},#{vo.a48},#{vo.a11},#{vo.b16c},#{vo.b16n},#{vo.b25c},#{vo.b25n},#{vo.initDscgMainDiagCodg},
                     #{vo.initDscgMainDiagName},#{vo.mainDiagCodg},#{vo.c04cNow},#{vo.initDipCodg},#{vo.initDipName},#{vo.currDipCodg},
                     #{vo.currDipName},#{vo.initDrgCodg},#{vo.initDrgName},#{vo.currDrgCodg},#{vo.currDrgName},#{vo.initSco},
                     #{vo.currSco},#{vo.initDif},#{vo.currDif},#{vo.difProp},#{vo.difVal},#{vo.modiPsn},#{vo.oprtRcd}
                 )
    </insert>

    <select id="queryPatientInfo"
            resultType="com.my.som.vo.supervision.MedicalRecordSupervisionVo">
        select
            a.ID as settleListId,
            b.K00 as k00,
            b.A48 as a48,
            b.A11 as a11,
            b.B16C as b16c,
            b.B16N as b16n,
            b.B25C as b25c,
            b.B25N as b25n,
            b.init_dscg_main_diag_codg as initDscgMainDiagCodg,
            b.init_dscg_main_diag_name as initDscgMainDiagName,
            b.main_diag_codg as mainDiagCodg,
            b.main_diag_name as c04cNow,
            <if test="grperType == 1">
                b.init_dip_codg as initDipCodg,
                b.init_dip_name as initDipName,
                b.curr_dip_codg as currDipCodg,
                b.curr_dip_name as currDipName,
            </if>
            <if test="grperType == 3">
                b.init_drg_codg as initDrgCodg,
                b.init_drg_name as initDrgName,
                b.curr_drg_codg as currDrgCodg,
                b.curr_drg_name as currDrgName,
            </if>
            b.init_sco as initSco,
            b.curr_sco as currSco,
            b.modi_psn as modiPsn,
            b.oprt_rcd as oprtRcd,
            c.price  as price,
            a.D01 as inHosTotalCost,
            a.D02 as preHospExamfee
        from som_hi_invy_bas_info a
        inner join som_hi_invy_supn_info b on a.K00 = b.K00
        <if test="grperType == 1">
            Left join som_dip_sco c ON c.settle_list_id =a.ID
        </if>
        <if test="grperType == 3">
            Left join som_drg_sco c ON c.settle_list_id =a.ID
        </if>
        <where>
            <!-- 入院时间 -->
            <if test="inStartTime != null and inStartTime != '' and
                      inEndTime != null and inEndTime != ''">
                and a.B12 between #{inStartTime} and concat(#{inEndTime},' 23:59:59')
            </if>
            <!-- 出院时间 -->
            <if test="begnDate != null and begnDate != '' and
                      expiDate != null and expiDate != ''">
                and a.B15 between #{begnDate} and concat(#{expiDate},' 23:59:59')
            </if>
            <!-- 结算时间 -->
            <if test="seStartTime != null and seStartTime != '' and
                      seEndTime != null and seEndTime != ''">
                and a.D37 between #{seStartTime} and concat(#{seEndTime},' 23:59:59')
            </if>
            <!-- 出院科室 -->
            <if test="deptCode != null and deptCode != ''">
                and a.B16C = #{deptCode}
            </if>
            <if test="drCodg != null and drCodg != ''">
                and a.B25C = #{drCodg}
            </if>
            <!-- 医疗机构代码 -->
            <if test="hospitalId != null and hospitalId != ''">
                and ( a.A01 = #{hospitalId} or a.HOSPITAL_ID = #{hospitalId} )
            </if>
        </where>
    </select>

</mapper>