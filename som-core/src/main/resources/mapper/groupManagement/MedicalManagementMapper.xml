<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.my.som.mapper.groupManagement.MedicalManagementMapper">
    <select id="queryBasicData" resultType="com.my.som.vo.groupManagement.MedicalManagementVo">
        SELECT a.ID AS settleListId,
               a.K00 AS k00,
               a.A01 AS a01,
               a.A11 AS name,
               a.A12C AS a12c,
               a.A14 AS age,
               a.A16 AS a16,
               a.A17 AS a17,
               a.D37 as setlHosTime,
               a.A46C AS insuType,
               a.A48 AS patientId,
               a.A54 AS a54,
               a.B15 AS outHosTime,
               a.B16C AS deptCode,
               a.B20 AS inHosDays,
               a.B25C AS drCodg,
               a.B34C AS b34c,
               a.C03C AS WMCode,
               a.C37C AS TCMCode,
               a.D01 AS inHosTotalCost,
               a.D02 AS preHospExamfee,
               a.HOSPITAL_ID AS hospitalId,
               a.A14 as c40c, <!-- 年龄 -->
               a.B20 as c42c, <!-- 住院天数 -->
               a.B34C as c41c, <!-- 离院方式 -->
               b.dscg_diag_codg AS dscg_diag_codg,
               c.C35C AS c35c,
               d.dip_codg AS dipCodg,
               d.DIP_NAME AS dipName,
               e.drg_codg AS drgCodg,
               e.DRG_NAME AS drgName
        FROM som_hi_invy_bas_info a
            <if test='enableSeAns != null and enableSeAns != "" and enableSeAns == "1"'>
                INNER JOIN som_setl_cas_crsp p
                ON a.k00 = p.k00
            </if>
        LEFT JOIN
        (
            SELECT SETTLE_LIST_ID,
                   GROUP_CONCAT(CASE WHEN dscg_diag_codg IS NOT NULL AND dscg_diag_codg != '-' THEN dscg_diag_codg ELSE NULL END) AS dscg_diag_codg
            FROM som_diag
            <choose>
                <when test='type == "1"'>
                    <include refid="processSettleListIdSql"></include>
                </when>
                <when test='type == "2"'>
                    <include refid="settleListIdSql"></include>
                </when>
            </choose>
            GROUP BY SETTLE_LIST_ID
        ) b
        ON a.ID = b.SETTLE_LIST_ID
        LEFT JOIN
        (
            SELECT SETTLE_LIST_ID,
                   GROUP_CONCAT(CASE WHEN C35C IS NOT NULL AND C35C != '-' THEN C35C ELSE NULL END) AS C35C
            FROM som_oprn_oprt_info
            <choose>
                <when test='type == "1"'>
                    <include refid="processSettleListIdSql"></include>
                </when>
                <when test='type == "2"'>
                    <include refid="settleListIdSql"></include>
                </when>
            </choose>
            GROUP BY SETTLE_LIST_ID
        ) c
        ON a.ID = c.SETTLE_LIST_ID
        LEFT JOIN som_dip_grp_info d
        ON a.ID = d.SETTLE_LIST_ID
        LEFT JOIN som_drg_grp_info e
        ON a.ID = e.SETTLE_LIST_ID
        <choose>
            <when test='type == "1"'>
                <include refid="conditionProcessSql"></include>
            </when>
            <when test='type == "2"'>
                <include refid="conditionSql"></include>
            </when>
        </choose>
    </select>

    <insert id="insertOptimizedMedical">
        INSERT INTO som_can_opt_medcas_info
        (
            SETTLE_LIST_ID,
            K00,
            `NAME`,
            PATIENT_ID,
            AGE,
            dis_gp_codg,
            dis_gp_name,
            ipt_sumfee,
            pre_hosp_examfee,
            dscg_time,
            DEPT_CODE,
            dr_codg,
            HOSPITAL_ID,
            grper_type
        ) VALUES
        <foreach collection="dto" item="item" separator=",">
                 (
                    #{ item.settleListId, jdbcType=VARCHAR },
                    #{ item.k00, jdbcType=VARCHAR },
                    #{ item.name, jdbcType=VARCHAR },
                    #{ item.patientId, jdbcType=VARCHAR },
                    #{ item.age, jdbcType=VARCHAR },
                    #{ item.dipCodg, jdbcType=VARCHAR },
                    #{ item.dipName, jdbcType=VARCHAR },
                    #{ item.inHosTotalCost, jdbcType=VARCHAR },
                    #{ item.preHospExamfee, jdbcType=VARCHAR },
                    #{ item.outHosTime, jdbcType=VARCHAR },
                    #{ item.deptCode, jdbcType=VARCHAR },
                    #{ item.drCodg, jdbcType=VARCHAR },
                    #{ item.hospitalId, jdbcType=VARCHAR },
                    #{ item.grperType, jdbcType=VARCHAR }
                 )
        </foreach>
    </insert>

    <delete id="deleteOptimizedMedical">
        DELETE FROM som_can_opt_medcas_info WHERE SETTLE_LIST_ID IN
        <foreach collection="dto" item="item" open="(" separator="," close=")">
            #{ item.settleListId, jdbcType=VARCHAR }
        </foreach>
    </delete>

    <insert id="insertRecommendGroup">
        INSERT INTO som_in_group_rcd
        (
            SETTLE_LIST_ID,
            HOSPITAL_ID,
            dip_codg,
            DIP_NAME,
            main_diag,
            standard_fee,
            grper_type,forecast_fee,profitloss
        ) VALUES
        <foreach collection="dto" item="item" separator=",">
              <choose>
                  <when test='item.type == "1"'>
                      <if test="item.groupsInfo2 != null and item.groupsInfo2.size() > 0">
                         <foreach collection="item.groupsInfo2" item="data" separator=",">
                             (
                             #{data.settleListId},
                             #{data.hospitalId},
                             #{data.dipCodg},
                             #{data.dipName},
                             #{data.WMCode},
                             #{data.lastYearLevelStandardCost},
                             #{item.grperType,jdbcType=VARCHAR},
                             #{data.forecast_fee,jdbcType=VARCHAR},
                             #{data.profitloss,jdbcType=VARCHAR}
                             )
                         </foreach>
                     </if>
                  </when>

                  <!-- DRG -->
                  <when test='item.type == "3"'>
                        <if test="item.groupsInfo != null and item.groupsInfo.size() > 0">
                           <foreach collection="item.groupsInfo" item="data" separator=",">
                               (
                               #{data.settleListId},
                               #{data.hospitalId},
                               #{data.dipCodg},
                               #{data.dipName},
                               #{data.WMCode},
                               #{data.lastYearLevelStandardCost},
                               #{item.grperType,jdbcType=VARCHAR},
                               #{data.forecast_fee,jdbcType=VARCHAR},
                               #{data.profitloss,jdbcType=VARCHAR}
                               )
                           </foreach>
                       </if>
                  </when>

              </choose>

        </foreach>

    </insert>

    <delete id="deleteRecommendGroup">
        DELETE FROM som_in_group_rcd WHERE SETTLE_LIST_ID IN
        <foreach collection="dto" item="item" open="(" separator="," close=")">
            #{ item.settleListId, jdbcType=VARCHAR }
        </foreach>
    </delete>

    <select id="queryPatientInfo" resultType="com.my.som.vo.groupManagement.MedicalManagementVo">
        SELECT a.ID AS id,
               a.K00 AS k00,
               a.SETTLE_LIST_ID AS settleListId,
               a.HOSPITAL_ID AS hospitalId,
               a.`NAME` AS name,
               a.PATIENT_ID AS patientId,
               a.AGE AS age,
               a.dis_gp_codg AS dipCodg,
               a.dis_gp_name AS dipName,
               a.ipt_sumfee AS inHosTotalCost,
               a.pre_hosp_examfee AS preHospExamfee,
               a.dscg_time AS outHosTime,
               a.DEPT_CODE AS deptCode,
               b.`NAME` AS deptName,
               a.dr_codg AS drCodg,
               c.`NAME` AS drName,
               a.grper_type AS grperType
        FROM som_can_opt_medcas_info a
        LEFT JOIN som_dept b
            ON a.DEPT_CODE = b.`CODE`
           AND a.HOSPITAL_ID = b.HOSPITAL_ID
        LEFT JOIN som_medstff_info c
            ON a.dr_codg = c.`CODE`
           AND a.HOSPITAL_ID = c.HOSPITAL_ID
        LEFT JOIN som_hi_invy_bas_info d
            ON a.SETTLE_LIST_ID = d.id
        <if test='enableSeAns != null and enableSeAns != "" and enableSeAns == "1"'>
            INNER JOIN som_setl_cas_crsp p
            ON d.k00 = p.k00
        </if>
        <where>
            <if test="medcasCodg != null and medcasCodg != ''">
                AND a.PATIENT_ID LIKE CONCAT('%', #{ medcasCodg, jdbcType=VARCHAR }, '%')
            </if>
            <if test="hospitalId != null and hospitalId != ''">
                AND a.HOSPITAL_ID = #{ hospitalId, jdbcType=VARCHAR }
            </if>
            <if test="deptCode != null and deptCode != ''">
                AND a.DEPT_CODE = #{ deptCode, jdbcType=VARCHAR }
            </if>
            <if test="drCodg != null and drCodg != ''">
                AND a.dr_codg = #{ drCodg, jdbcType=VARCHAR }
            </if>
            <if test="begnDate != null and begnDate != '' and
                      expiDate != null and expiDate != ''">
                AND a.dscg_time BETWEEN #{ begnDate, jdbcType=VARCHAR } AND CONCAT(#{ expiDate, jdbcType=VARCHAR }, ' 23:59:59')
            </if>
            <if test="seStartTime != null and seStartTime != '' and
                      seEndTime != null and seEndTime != ''">
                AND d.D37 BETWEEN #{seStartTime,jdbcType=VARCHAR} AND CONCAT(#{seEndTime,jdbcType=VARCHAR}, ' 23:59:59')
            </if>
            <if test="deptCode != null and deptCode != ''">
                AND d.b16c = #{deptCode,jdbcType=VARCHAR}
            </if>
            <if test="drCodg != null and drCodg != ''">
                AND d.B25C = #{drCodg,jdbcType=VARCHAR}
            </if>
            <if test="grperType != null and grperType != ''">
                AND a.grper_type = #{ grperType, jdbcType=VARCHAR }
            </if>
        </where>
    </select>

    <select id="queryGroupsInfo" resultType="com.my.som.vo.groupManagement.MedicalManagementVo">
        SELECT SETTLE_LIST_ID AS settleListId,
               HOSPITAL_ID AS hospitalId,
               dip_codg AS dipCodg,
               DIP_NAME AS dipName,
               main_diag AS mainDiag,
               standard_fee AS standardFee,
               (@i:=@i+1) i
        FROM som_in_group_rcd,(SELECT @i:=0) AS it
        WHERE SETTLE_LIST_ID = #{ settleListId, jdbcType=VARCHAR }
          AND grper_type = #{ grperType, jdbcType=VARCHAR }
        ORDER BY profitloss DESC
    </select>

    <delete id="deleteGroupsData">
        DELETE FROM som_in_group_rcd
        WHERE SETTLE_LIST_ID IN (
            SELECT SETTLE_LIST_ID FROM som_can_opt_medcas_info
            <where>
                <if test="begnDate != null and begnDate != '' and
                      expiDate != null and expiDate != ''">
                    <![CDATA[
                        AND SUBSTR(dscg_time,1,7) >= #{ begnDate, jdbcType=VARCHAR } AND SUBSTR(dscg_time,1,7) <= #{ expiDate, jdbcType=VARCHAR }
                    ]]>
                </if>
                <if test="hospitalId != null and hospitalId != ''">
                    AND HOSPITAL_ID = #{ hospitalId, jdbcType=VARCHAR }
                </if>
                <if test="grperType != null and grperType != ''">
                    AND grper_type = #{ grperType, jdbcType=VARCHAR }
                </if>
            </where>
        )
    </delete>

    <delete id="deletePatientData">
        DELETE FROM som_can_opt_medcas_info
        <where>
            <if test="begnDate != null and begnDate != '' and
                      expiDate != null and expiDate !=''">
                <![CDATA[
                   AND SUBSTR(dscg_time,1,7) >= #{ begnDate, jdbcType=VARCHAR } AND SUBSTR(dscg_time,1,7) <= #{ expiDate, jdbcType=VARCHAR }
                ]]>
            </if>
            <if test="hospitalId != null and hospitalId != ''">
                AND HOSPITAL_ID = #{ hospitalId, jdbcType=VARCHAR }
            </if>
            <if test="grperType != null and grperType != ''">
                AND grper_type = #{ grperType, jdbcType=VARCHAR }
            </if>
        </where>
    </delete>

    <sql id="settleListIdSql">
        WHERE SETTLE_LIST_ID IN (
            SELECT ID FROM (
                SELECT ID
                FROM som_hi_invy_bas_info a
                <include refid="conditionSql"></include>
            ) b
        )
    </sql>

    <!-- 流程 -->
    <sql id="processSettleListIdSql">
        WHERE SETTLE_LIST_ID IN (
            SELECT ID FROM (
                SELECT ID
                FROM som_hi_invy_bas_info a
                <where>
                    <if test="dataLogId != null and dataLogId != ''">
                        AND a.DATA_LOG_ID = #{dataLogId}
                    </if>
                </where>
            ) b
        )
    </sql>

    <!-- 流程条件 -->
    <sql id="conditionProcessSql">
        <where>
            <if test="dataLogId != null and dataLogId != ''">
                AND a.DATA_LOG_ID = #{dataLogId}
            </if>
        </where>
        LIMIT #{ start, jdbcType=INTEGER }, #{ limit, jdbcType=INTEGER }
    </sql>

    <sql id="conditionSql">
        <where>
            <if test="begnDate != null and begnDate != '' and expiDate != null and expiDate != ''">
                <![CDATA[
                            SUBSTR( a.B15, 1, 7 ) >= #{ begnDate, jdbcType=VARCHAR } AND SUBSTR( a.B15, 1, 7 ) <= #{ expiDate, jdbcType=VARCHAR }
                        ]]>
            </if>
            <if test="hospitalId != null and hospitalId != ''">
                AND a.HOSPITAL_ID = #{ hospitalId, jdbcType=VARCHAR }
            </if>
        </where>
        LIMIT #{ start, jdbcType=INTEGER }, #{ limit, jdbcType=INTEGER }
    </sql>
</mapper>
