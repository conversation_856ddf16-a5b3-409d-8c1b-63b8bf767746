<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.som.mapper.uploadedList.UploadedListMapper">


    <insert id="insertSetinfo" parameterType="com.my.som.dto.listIUploadedQuery.SomSettlementSetlinfoDto">
        insert into som_settlement_setlinfo (
            emp_tel,
            curr_addr,
            psn_no,
            stas_type,
            coner_addr,
            gend,
            adm_way_code,
            insutype,
            yljzPay,
            setl_list_sn,
            psn_cashpay,
            otp_tcm_diag_dise_code,
            otp_wm_diag_dise_code,
            chfpdr_name,
            biz_sn,
            medins_fill_psn,
            billno,
            otp_wm_diag,
            bill_code,
            dscg_time,
            psn_selfpay_amt,
            naty,
            certno,
            fixmedins_code,
            hi_no,
            spga_nurscare_days,
            coner_tel,
            oprn_oprt_code_cnt,
            acct_payamt,
            resp_nurs_name,
            mul_nwb_bir_wt,
            otp_tcm_diag,
            refl_caty,
            setl_id,
            emp_poscode,
            psn_name,
            emp_addr,
            mul_nwb_adm_wt,
            zgdePay,
            patn_rlts,
            prfs,
            insu_admdvs,
            diag_code_cnt,
            sp_psn_type,
            psn_ownpay_fee,
            setl_begndate,
            hi_type,
            mdtrt_id,
            nwb_adm_type,
            opsp_diag_caty,
            scd_nurscare_days,
            hi_setl_lv,
            hi_paymtd,
            fixmedins_name,
            coner_name,
            lv1_nurscare_days,
            chfpdr_code,
            dscg31days_rinp_flag,
            resp_nurs_code,
            rinp_pup,
            emp_name,
            dscg_way,
            brdy,
            ybtcPay,
            trt_type,
            acp_optins_name,
            gwyPay,
            psn_cert_type,
            ntly,
            jmdbPay,
            act_ipt_days,
            lv3_nurscare_days,
            setl_enddate,
            adm_time,
            medins_fill_dept,
            medcasno,
            dscg_caty,
            ipt_med_type,
            age
        ) values (
                     #{empTel},
                     #{currAddr},
                     #{psnNo},
                     #{stasType},
                     #{conerAddr},
                     #{gend},
                     #{admWayCode},
                     #{insutype},
                     #{yljzPay},
                     #{setlListSn},
                     #{psnCashpay},
                     #{otpTcmDiagDiseCode},
                     #{otpWmDiagDiseCode},
                     #{chfpdrName},
                     #{bizSn},
                     #{medinsFillPsn},
                     #{billno},
                     #{otpWmDiag},
                     #{billCode},
                     #{dscgTime},
                     #{psnSelfpayAmt},
                     #{naty},
                     #{certno},
                     #{fixmedinsCode},
                     #{hiNo},
                     #{spgaNurscareDays},
                     #{conerTel},
                     #{oprnOprtCodeCnt},
                     #{acctPayamt},
                     #{respNursName},
                     #{mulNwbBirWt},
                     #{otpTcmDiag},
                     #{reflCaty},
                     #{setlId},
                     #{empPoscode},
                     #{psnName},
                     #{empAddr},
                     #{mulNwbAdmWt},
                     #{zgdePay},
                     #{patnRlts},
                     #{prfs},
                     #{insuAdmdvs},
                     #{diagCodeCnt},
                     #{spPsnType},
                     #{psnOwnpayFee},
                     #{setlBegndate},
                     #{hiType},
                     #{mdtrtId},
                     #{nwbAdmType},
                     #{opspDiagCaty},
                     #{scdNurscareDays},
                     #{hiSetlLv},
                     #{hiPaymtd},
                     #{fixmedinsName},
                     #{conerName},
                     #{lv1NurscareDays},
                     #{chfpdrCode},
                     #{dscg31daysRinpFlag},
                     #{respNursCode},
                     #{rinpPup},
                     #{empName},
                     #{dscgWay},
                     #{brdy},
                     #{ybtcPay},
                     #{trtType},
                     #{acpOptinsName},
                     #{gwyPay},
                     #{psnCertType},
                     #{ntly},
                     #{jmdbPay},
                     #{actIptDays},
                     #{lv3NurscareDays},
                     #{setlEnddate},
                     #{admTime},
                     #{medinsFillDept},
                     #{medcasno},
                     #{dscgCaty},
                     #{iptMedType},
                     #{age}
                 )
    </insert>
    <insert id="insertDiseinfo" parameterType="java.util.List">
        INSERT INTO som_settlement_diseinfo (
        setl_list_diag_id,
        setl_id,
        mdtrt_id,
        psn_no,
        diag_type,
        maindiag_flag,
        diag_code,
        diag_name,
        adm_cond_type
        ) VALUES
        <foreach collection="somSettlementDiseinfoList" item="item" separator=",">
            (
            #{item.setlListDiagId},
            #{item.setlId},
            #{item.mdtrtId},
            #{item.psnNo},
            #{item.diagType},
            #{item.maindiagFlag},
            #{item.diagCode},
            #{item.diagName},
            #{item.admCondType}
            )
        </foreach>
    </insert>
    <insert id="insertOprninfo" parameterType="java.util.List">
        INSERT INTO som_settlement_oprninfo (
        setl_list_oprn_id,
        setl_id,
        psn_no,
        mdtrt_id,
        main_oprn_flag,
        oprn_oprt_name,
        oprn_oprt_code,
        oprn_oprt_date,
        anst_way,
        oper_dr_name,
        oper_dr_code,
        anst_dr_name,
        anst_dr_code,
        oprn_oprt_begntime,
        oprn_oprt_endtime,
        anst_begntime,
        anst_endtime
        ) VALUES
        <foreach collection="somSettlementOprninfoList" item="item" separator=",">
            (
            #{item.setlListOprnId},
            #{item.setlId},
            #{item.psnNo},
            #{item.mdtrtId},
            #{item.mainOprnFlag},
            #{item.oprnOprtName},
            #{item.oprnOprtCode},
            #{item.oprnOprtDate},
            #{item.anstWay},
            #{item.operDrName},
            #{item.operDrCode},
            #{item.anstDrName},
            #{item.anstDrCode},
            #{item.oprnOprtBegntime},
            #{item.oprnOprtEndTime},
            #{item.anstBeginTime},
            #{item.anstEndTime}
            )
        </foreach>
    </insert>
    <insert id="insertBldinfo" parameterType="java.util.List">
        INSERT INTO som_settlement_bldinfo (
        setl_list_bld_id,
        psn_no,
        mdtrt_id,
        setl_id,
        bld_cat,
        bld_amt,
        bld_unt
        ) VALUES
        <foreach collection="somSettlementBldinfoList" item="item" separator=",">
            (
            #{item.setlListBldId},
            #{item.psnNo},
            #{item.mdtrtId},
            #{item.setlId},
            #{item.bldCat},
            #{item.bldAmt},
            #{item.bldUnt}
            )
        </foreach>
    </insert>
    <insert id="insertIcuinfo" parameterType="java.util.List">
        INSERT INTO som_settlement_icuinfo (
        setl_list_scs_cutd_id,
        psn_no,
        mdtrt_id,
        setl_id,
        scs_cutd_ward_type,
        scs_cutd_inpool_time,
        scs_cutd_exit_time,
        scs_cutd_sum_dura
        ) VALUES
        <foreach collection="somSettlementIcuinfoList" item="item" separator=",">
            (
            #{item.setlListScsCutdId},
            #{item.psnNo},
            #{item.mdtrtId},
            #{item.setlId},
            #{item.scsCutdWardType},
            #{item.scsCutdInpoolTime},
            #{item.scsCutdExitTime},
            #{item.scsCutdSumDura}
            )
        </foreach>
    </insert>
    <insert id="insertPayinfo" parameterType="java.util.List">
        INSERT INTO som_settlement_payinfo (
        setl_id,
        psn_no,
        mdtrt_id,
        fund_pay_type,
        poolarea_fund_pay_type,
        poolarea_fund_pay_name,
        fund_payamt
        ) VALUES
        <foreach collection="somSettlementPayinfoList" item="item" separator=",">
            (
            #{item.setlId},
            #{item.psnNo},
            #{item.mdtrtId},
            #{item.fundPayType},
            #{item.poolareaFundPayType},
            #{item.poolareaFundPayName},
            #{item.fundPayamt}
            )
        </foreach>
    </insert>
    <insert id="insertIteminfo" parameterType="java.util.List">
        INSERT INTO som_settlement_iteminfo (
        setl_list_chrgitm_id,
        setl_id,
        mdtrt_id,
        psn_no,
        med_chrgitm_type,
        item_sumamt,
        item_claa_amt,
        item_clab_amt,
        item_ownpay_amt,
        item_oth_amt,
        sindise_code_name,
        daysrg_code_name
        ) VALUES
        <foreach collection="SomSettlementIteminfoList" item="item" separator=",">
            (
            #{item.setlListChrgitmId},
            #{item.setlId},
            #{item.mdtrtId},
            #{item.psnNo},
            #{item.medChrgitmType},
            #{item.itemSumamt},
            #{item.itemClaaAmt},
            #{item.itemClabAmt},
            #{item.itemOwnpayAmt},
            #{item.itemOthAmt},
            #{item.sindiseCodeName},
            #{item.daysrgCodeName}
            )
        </foreach>
    </insert>
    <delete id="delSetinfo">
        DELETE FROM som_settlement_setlinfo
        WHERE setl_id IN
        <foreach item="item" index="index" collection="setlinfoList" open="(" close=")" separator=",">
            #{item.setl_id}
        </foreach>
    </delete>
    <delete id="delDiseinfo">
        DELETE FROM som_settlement_diseinfo
        WHERE setl_id IN
        <foreach item="item" index="index" collection="setlinfoList" open="(" close=")" separator=",">
            #{item.setl_id}
        </foreach>
    </delete>
    <delete id="delOprninfo">
        DELETE FROM som_settlement_oprninfo
        WHERE setl_id IN
        <foreach item="item" index="index" collection="setlinfoList" open="(" close=")" separator=",">
            #{item.setl_id}
        </foreach>
    </delete>
    <delete id="delBldinfo">
        DELETE FROM som_settlement_bldinfo
        WHERE setl_id IN
        <foreach item="item" index="index" collection="setlinfoList" open="(" close=")" separator=",">
            #{item.setl_id}
        </foreach>
    </delete>
    <delete id="delIcuinfo">
        DELETE FROM som_settlement_icuinfo
        WHERE setl_id IN
        <foreach item="item" index="index" collection="setlinfoList" open="(" close=")" separator=",">
            #{item.setl_id}
        </foreach>
    </delete>
    <delete id="delPayinfo">
        DELETE FROM som_settlement_payinfo
        WHERE setl_id IN
        <foreach item="item" index="index" collection="setlinfoList" open="(" close=")" separator=",">
            #{item.setl_id}
        </foreach>
    </delete>
    <delete id="delIteminfo">
        DELETE FROM som_settlement_iteminfo
        WHERE setl_id IN
        <foreach item="item" index="index" collection="setlinfoList" open="(" close=")" separator=",">
            #{item.setl_id}
        </foreach>
    </delete>

</mapper>