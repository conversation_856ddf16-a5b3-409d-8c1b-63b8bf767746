<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.som.mapper.cnBusiness.CNGroupMapper">

    <!-- 查询数据分组数据 -->
    <select id="queryCNData" resultType="com.my.som.vo.cnBusiness.CNGroupDataInfoVo">
        SELECT
            a.a48 as B_WT4_V1_ID,
            a.k00 as k00,
            a.a11 as name,
            a.a12c as GENDER,
            a.a14 as AGE,
            a.C03C as DISEASE_CODE,
            a.A16 as SF0100,
            a.A17 as SF0102,
            a.A18 as SF0101,
            a.C42 as SF0104,
            0 as SF0108,
            a.B20 as ACCTUAL_DAYS,
            a.D01 as TOTAL_EXPENSE,
            a.b12 as inHosTime,
            a.b15 as outHosTime,
            a.B25C as drCodg,
            a.B16C as outDept,
            b.*,
            c.*
        FROM som_hi_invy_bas_info a
        LEFT JOIN (
            SELECT
            SETTLE_LIST_ID,
            MAX( CASE WHEN seq = 1 THEN dscg_diag_codg ELSE NULL END ) AS 'C06C_1',
            MAX( CASE WHEN seq = 2 THEN dscg_diag_codg ELSE NULL END ) AS 'C06C_2',
            MAX( CASE WHEN seq = 3 THEN dscg_diag_codg ELSE NULL END ) AS 'C06C_3',
            MAX( CASE WHEN seq = 4 THEN dscg_diag_codg ELSE NULL END ) AS 'C06C_4',
            MAX( CASE WHEN seq = 5 THEN dscg_diag_codg ELSE NULL END ) AS 'C06C_5',
            MAX( CASE WHEN seq = 6 THEN dscg_diag_codg ELSE NULL END ) AS 'C06C_6',
            MAX( CASE WHEN seq = 7 THEN dscg_diag_codg ELSE NULL END ) AS 'C06C_7',
            MAX( CASE WHEN seq = 8 THEN dscg_diag_codg ELSE NULL END ) AS 'C06C_8',
            MAX( CASE WHEN seq = 9 THEN dscg_diag_codg ELSE NULL END ) AS 'C06C_9',
            MAX( CASE WHEN seq = 10 THEN dscg_diag_codg ELSE NULL END ) AS 'C06C_10',
            MAX( CASE WHEN seq = 11 THEN dscg_diag_codg ELSE NULL END ) AS 'C06C_11',
            MAX( CASE WHEN seq = 12 THEN dscg_diag_codg ELSE NULL END ) AS 'C06C_12',
            MAX( CASE WHEN seq = 13 THEN dscg_diag_codg ELSE NULL END ) AS 'C06C_13',
            MAX( CASE WHEN seq = 14 THEN dscg_diag_codg ELSE NULL END ) AS 'C06C_14',
            MAX( CASE WHEN seq = 15 THEN dscg_diag_codg ELSE NULL END ) AS 'C06C_15'
            FROM
            som_diag
            where SETTLE_LIST_ID in (
            select a.id from
            som_hi_invy_bas_info a
            <where>
                <if test="begnDate != '' and begnDate != null and expiDate != '' and expiDate != null" >
                    a.d37 BETWEEN #{begnDate} AND CONCAT(#{expiDate},' 23:59:59')
                </if>
            </where>
            )
            GROUP BY SETTLE_LIST_ID
        ) b on a.ID = b.SETTLE_LIST_ID
        LEFT JOIN (
            SELECT
            SETTLE_LIST_ID,
            MAX( CASE WHEN seq = 0 THEN C35C ELSE NULL END ) AS 'C35C_0',
            MAX( CASE WHEN seq = 1 THEN C35C ELSE NULL END ) AS 'C35C_1',
            MAX( CASE WHEN seq = 2 THEN C35C ELSE NULL END ) AS 'C35C_2',
            MAX( CASE WHEN seq = 3 THEN C35C ELSE NULL END ) AS 'C35C_3',
            MAX( CASE WHEN seq = 4 THEN C35C ELSE NULL END ) AS 'C35C_4',
            MAX( CASE WHEN seq = 5 THEN C35C ELSE NULL END ) AS 'C35C_5',
            MAX( CASE WHEN seq = 6 THEN C35C ELSE NULL END ) AS 'C35C_6'
            FROM
            som_oprn_oprt_info
            where SETTLE_LIST_ID in (
            select a.id from
            som_hi_invy_bas_info a
            <where>
                <if test="begnDate != '' and begnDate != null and expiDate != '' and expiDate != null" >
                    a.d37 BETWEEN #{begnDate} AND CONCAT(#{expiDate},' 23:59:59')
                </if>
            </where>
            )
            GROUP BY SETTLE_LIST_ID
        ) c on a.ID = c.SETTLE_LIST_ID
        WHERE 1 = 1
        <if test="begnDate != '' and begnDate != null and expiDate != '' and expiDate != null" >
            and a.d37 BETWEEN #{begnDate} AND CONCAT(#{expiDate},' 23:59:59')
        </if>
    </select>

    <select id="queryName" resultType="com.my.som.vo.cnBusiness.DrgsNameVo">
        SELECT
            drg_codg as code,
            drg_name as name
        FROM som_grp_info
    </select>

    <select id="queryKSList" resultType="com.my.som.vo.cnBusiness.OperateVo">
        select
            *
        from som_oprn_lv
    </select>

    <select id="levelThreeList" resultType="com.my.som.vo.cnBusiness.OperateVo">
        select
            oprn_oprt_codg as code,
            oprn_oprt_name as name
        from som_oprn_lv
        where oprn_lv = '3'
    </select>
</mapper>