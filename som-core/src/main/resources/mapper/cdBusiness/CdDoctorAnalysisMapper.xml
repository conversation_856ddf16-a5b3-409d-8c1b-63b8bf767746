<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.som.mapper.cdBusiness.CdDoctorAnalysisMapper">
<!--    查询医院费用分析主要信息-->
    <select id="list" resultType="com.my.som.vo.cdBusiness.CdDoctorAnalysisInfo">
        select p.*
        <if test="queryParam.queryType==1">
            <trim prefix=",">
                m.name AS priOutHosDeptName
            </trim>
        </if>
        from (
        SELECT
            <if test="queryParam.queryType==1">
                x.priOutHosDeptCode AS priOutHosDeptCode,
            </if>
            x.drCodg AS drCodg,
            x.drName AS drName,
            <if test="queryParam.queryType==2">
              x.doctorDepts AS doctorDepts,
            </if>
            SUM( X.totalMedicalNum) AS totalMedicalNum,
            SUM( X.inGroupMedicalNum) AS inGroupMedicalNum,
            SUM( X.notGroupMedicalNum) AS notGroupMedicalNum,
            SUM( X.cdGroupNum) AS cdGroupNum,
            ROUND(AVG( X.cmi),2) AS cmi,
            SUM( X.totalCdWeight) AS totalCdWeight,
            ROUND(AVG( X.avgDays),2) AS avgDays,
            ROUND(AVG( X.inGroupAvgDays),2) AS inGroupAvgDays,
            ROUND( AVG( X.inGroupAvgCost ), 2 ) AS inGroupAvgCost,
            ROUND(AVG( X.avgCost),2) AS avgCost,
            ROUND(AVG( X.timeIndex),2) AS timeIndex,
            ROUND(AVG( X.costIndex),2) AS costIndex,
            ROUND(AVG( X.medicalCostRate),2) AS medicalCostRate,
            ROUND(AVG( X.materialCostRate),2) AS materialCostRate
        FROM(
            select
            <if test="queryParam.queryType==1">
                x.dscg_caty_codg_inhosp AS priOutHosDeptCode,
            </if>
            x.drCodg AS drCodg,
            x.drName AS drName,
            <if test="queryParam.queryType==2">
                group_concat(distinct x.dscg_caty_name_inhosp) AS doctorDepts,
            </if>
            IFNULL(count(1),0) AS totalMedicalNum,
            IFNULL(count(case when x.grp_stas = '1' then 1 else null end),0)   AS inGroupMedicalNum,
            IFNULL(count(case when x.grp_stas != '1' then 1 else null end),0)  AS notGroupMedicalNum,
            IFNULL(count(distinct (case when x.grp_stas = '1' then x.cd_codg else null end)),0)   AS cdGroupNum,
            IFNULL(round(SUM(CASE WHEN x.grp_stas = '1' THEN x.cd_wt ELSE NULL END)/
            NULLIF (COUNT(CASE WHEN x.grp_stas = '1' THEN 1 ELSE NULL END),0),2),0)   AS cmi,
            IFNULL(SUM(CASE WHEN x.grp_stas = '1' THEN x.cd_wt ELSE NULL END),0)    AS totalCdWeight,
            IFNULL(ROUND(AVG(x.act_ipt), 2),0)   AS avgDays,
            IFNULL(ROUND(AVG(CASE WHEN x.grp_stas = '1' THEN x.act_ipt ELSE NULL END), 2),0)   AS inGroupAvgDays,
            IFNULL(ROUND(AVG(x.ipt_sumfee), 2),0)  AS avgCost,
            IFNULL(ROUND(AVG(CASE WHEN x.grp_stas = '1' THEN x.ipt_sumfee ELSE NULL END), 2),0)    AS inGroupAvgCost,
            IFNULL(ROUND(SUM(CASE WHEN x.grp_stas = '1' THEN x.act_ipt ELSE NULL END / NULLIF(x.standard_ave_hosp_day,0)) /
            NULLIF(COUNT(CASE WHEN x.grp_stas = '1' THEN 1 ELSE NULL END),0), 2),0)  AS timeIndex,
            IFNULL(ROUND(SUM(CASE WHEN x.grp_stas = '1' THEN x.ipt_sumfee ELSE NULL END / NULLIF(x.standard_avg_fee,0)) /
            NULLIF(COUNT(CASE WHEN x.grp_stas = '1' THEN 1 ELSE NULL END),0), 2),0)   AS costIndex,
            IFNULL(CONCAT(ROUND(SUM(x.drugfee)/NULLIF(SUM(x.ipt_sumfee),0)*100,2),'%'),0) AS medicalCostRate,<!--  药品费占比   -->
            IFNULL(CONCAT(ROUND(SUM(x.mcs_fee)/NULLIF(SUM(x.ipt_sumfee),0)*100,2),'%'),0) AS materialCostRate <!--  耗材费占比   -->
            from (
            select
            <if test="queryParam.doctorType!=null and queryParam.doctorType!=''">
                (CASE #{queryParam.doctorType}#
                WHEN 'dct0' THEN a.ipdr_code
                WHEN 'dct1' THEN a.atddr_code
                WHEN 'dct2' THEN a.deptdrt_code
                WHEN 'dct3' THEN a.chfdr_code
                WHEN 'dct4' THEN a.train_dr_code
                WHEN 'dct5' THEN a.qltctrl_dr_code
                WHEN 'dct6' THEN a.intn_dr
                ELSE  a.ipdr_name
                END) AS drCodg,
                (CASE #{queryParam.doctorType}#
                WHEN 'dct0' THEN a.ipdr_name
                WHEN 'dct1' THEN a.atddr_name
                WHEN 'dct2' THEN a.deptdrt_name
                WHEN 'dct3' THEN a.chfdr_name
                WHEN 'dct4' THEN a.train_dr_name
                WHEN 'dct5' THEN a.qltctrl_dr_name
                WHEN 'dct6' THEN a.intn_dr
                ELSE  a.ipdr_name
                END) AS drName,
            </if>
            <if test="queryParam.doctorType==null or queryParam.doctorType==''">
                c.drCodg as drCodg,
                c.drName as drName,
            </if>
            a.grp_stas as grp_stas,
            a.cd_codg as cd_codg,
            a.cd_wt as cd_wt,
            a.act_ipt as act_ipt,
            a.ipt_sumfee as ipt_sumfee,
            a.standard_ave_hosp_day as standard_ave_hosp_day,
            a.standard_avg_fee as standard_avg_fee,
            a.dscg_caty_codg_inhosp as dscg_caty_codg_inhosp,
            a.dscg_caty_name_inhosp AS dscg_caty_name_inhosp,
            a.drugfee as drugfee,
            a.mcs_fee as mcs_fee
            from som_cd_grp_info a
            <if test="queryParam.doctorType==null or queryParam.doctorType==''">
                inner join (
                select
                b.settle_list_id as settle_list_id,
                b.drCodg as drCodg,
                b.drName as drName
                from (
                select
                SETTLE_LIST_ID as settle_list_id,
                ipdr_code as drCodg,
                ipdr_name as drName
                FROM som_cd_grp_info
                where ipdr_name is not null and ipdr_name !='-' and ipdr_name !='未填写'
                <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
                    AND  HOSPITAL_ID = #{queryParam.hospitalId}
                </if>
                <if test="queryParam.b16c!=null and queryParam.b16c!=''">
                    AND dscg_caty_codg_inhosp = #{queryParam.b16c}
                </if>
                <if test="queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and queryParam.cy_end_date!=null and queryParam.cy_end_date!=''">
                    AND dscg_time BETWEEN #{queryParam.cy_start_date} and CONCAT(#{queryParam.cy_end_date},' 23:59:59')
                </if>
                <if test="queryParam.drCodg!=null and queryParam.drCodg!=''">
                    AND `INHOS_DOCTOR_CODE` = #{queryParam.drCodg}
                </if>
                union all
                select
                SETTLE_LIST_ID as settle_list_id,
                atddr_code as drCodg,
                atddr_name as drName
                FROM som_cd_grp_info
                where atddr_name is not null and atddr_name !='-' and atddr_name !='未填写'
                <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
                    AND  HOSPITAL_ID = #{queryParam.hospitalId}
                </if>
                <if test="queryParam.b16c!=null and queryParam.b16c!=''">
                    AND dscg_caty_codg_inhosp = #{queryParam.b16c}
                </if>
                <if test="queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and queryParam.cy_end_date!=null and queryParam.cy_end_date!=''">
                    AND dscg_time BETWEEN #{queryParam.cy_start_date} and CONCAT(#{queryParam.cy_end_date},' 23:59:59')
                </if>
                <if test="queryParam.drCodg!=null and queryParam.drCodg!=''">
                    AND `ATTENDING_DOCTOR_CODE` = #{queryParam.drCodg}
                </if>
                union all
                select
                SETTLE_LIST_ID as settle_list_id,
                deptdrt_code as drCodg,
                deptdrt_name as drName
                FROM som_cd_grp_info
                where deptdrt_name is not null and deptdrt_name !='-' and deptdrt_name !='未填写'
                <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
                    AND  HOSPITAL_ID = #{queryParam.hospitalId}
                </if>
                <if test="queryParam.b16c!=null and queryParam.b16c!=''">
                    AND dscg_caty_codg_inhosp = #{queryParam.b16c}
                </if>
                <if test="queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and queryParam.cy_end_date!=null and queryParam.cy_end_date!=''">
                    AND dscg_time BETWEEN #{queryParam.cy_start_date} and CONCAT(#{queryParam.cy_end_date},' 23:59:59')
                </if>
                <if test="queryParam.drCodg!=null and queryParam.drCodg!=''">
                    AND `DEPT_DIRECTOR_CODE` = #{queryParam.drCodg}
                </if>
                union all
                select
                SETTLE_LIST_ID as settle_list_id,
                chfdr_code as drCodg,
                chfdr_name as drName
                FROM
                som_cd_grp_info
                where chfdr_name is not null and chfdr_name !='-' and chfdr_name !='未填写'
                <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
                    AND  HOSPITAL_ID = #{queryParam.hospitalId}
                </if>
                <if test="queryParam.b16c!=null and queryParam.b16c!=''">
                    AND dscg_caty_codg_inhosp = #{queryParam.b16c}
                </if>
                <if test="queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and
                                                        queryParam.cy_end_date!=null and queryParam.cy_end_date!=''">
                    AND dscg_time BETWEEN #{queryParam.cy_start_date} and CONCAT(#{queryParam.cy_end_date},' 23:59:59')
                </if>
                <if test="queryParam.drCodg!=null and queryParam.drCodg!=''">
                    AND `MEDICAL_DIRECTOR_CODE` = #{queryParam.drCodg}
                </if>
                union all
                select
                SETTLE_LIST_ID as settle_list_id,
                train_dr_code as drCodg,
                train_dr_name as drName
                FROM som_cd_grp_info
                where train_dr_name is not null and train_dr_name !='-' and train_dr_name !='未填写'
                <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
                    AND  HOSPITAL_ID = #{queryParam.hospitalId}
                </if>
                <if test="queryParam.b16c!=null and queryParam.b16c!=''">
                    AND dscg_caty_codg_inhosp = #{queryParam.b16c}
                </if>
                <if test="queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and
                                            queryParam.cy_end_date!=null and queryParam.cy_end_date!=''">
                    AND dscg_time BETWEEN #{queryParam.cy_start_date} and CONCAT(#{queryParam.cy_end_date},' 23:59:59')
                </if>
                <if test="queryParam.drCodg!=null and queryParam.drCodg!=''">
                    AND `REFRESHER_DOCTOR_CODE` = #{queryParam.drCodg}
                </if>
                union all
                select
                SETTLE_LIST_ID as settle_list_id,
                qltctrl_dr_code as drCodg,
                qltctrl_dr_name as drName
                FROM som_cd_grp_info
                where qltctrl_dr_name is not null and qltctrl_dr_name !='-' and qltctrl_dr_name !='未填写'
                <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
                    AND  HOSPITAL_ID = #{queryParam.hospitalId}
                </if>
                <if test="queryParam.b16c!=null and queryParam.b16c!=''">
                    AND dscg_caty_codg_inhosp = #{queryParam.b16c}
                </if>
                <if test="queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and
                                                queryParam.cy_end_date!=null and queryParam.cy_end_date!=''">
                    AND dscg_time BETWEEN #{queryParam.cy_start_date} and CONCAT(#{queryParam.cy_end_date},' 23:59:59')
                </if>
                <if test="queryParam.drCodg!=null and queryParam.drCodg!=''">
                    AND `QUALITY_DOCTOR_CODE` = #{queryParam.drCodg}
                </if>
                union all
                select
                SETTLE_LIST_ID as settle_list_id,
                intn_dr as drCodg,
                intn_dr as drName
                FROM som_cd_grp_info
                where intn_dr is not null and intn_dr !='-' and intn_dr !='未填写'
                <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
                    AND  HOSPITAL_ID = #{queryParam.hospitalId}
                </if>
                <if test="queryParam.b16c!=null and queryParam.b16c!=''">
                    AND dscg_caty_codg_inhosp = #{queryParam.b16c}
                </if>
                <if test="queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and
                                                    queryParam.cy_end_date!=null and queryParam.cy_end_date!=''">
                    AND dscg_time BETWEEN #{queryParam.cy_start_date} and CONCAT(#{queryParam.cy_end_date},' 23:59:59')
                </if>
                <if test="queryParam.drCodg!=null and queryParam.drCodg!=''">
                    AND `INTERN_DOCTOR_NAME` = #{queryParam.drCodg}
                </if>
                ) b group by settle_list_id,drCodg,drName
                ) c on a.settle_list_id = c.settle_list_id
            </if>
            where 1=1
            <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
                AND  a.HOSPITAL_ID = #{queryParam.hospitalId}
            </if>
            <if test="queryParam.b16c!=null and queryParam.b16c!=''">
                AND a.dscg_caty_codg_inhosp = #{queryParam.b16c}
            </if>
            <if test="queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and
                                                        queryParam.cy_end_date!=null and queryParam.cy_end_date!=''">
                AND a.dscg_time BETWEEN #{queryParam.cy_start_date} and CONCAT(#{queryParam.cy_end_date},' 23:59:59')
            </if>
            <if test="queryParam.doctorIdList!=null">
                AND (
                a.deptdrt_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                a.chfdr_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                a.atddr_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                a.ipdr_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                a.resp_nurs_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                a.train_dr_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                a.intn_dr in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                a.codr_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                a.qltctrl_dr_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR
                a.qltctrl_nurs_code in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                )
            </if>
            ) x
            where x.drName is not null and x.drName !='-' and x.drName !='未填写'
            <if test="queryParam.drCodg!=null and queryParam.drCodg!=''">
                AND x.drCodg= #{queryParam.drCodg}
            </if>
            <if test="queryParam.doctorIdList!=null">
                AND x.drCodg in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="queryParam.queryType==1">
                GROUP BY x.dscg_caty_codg_inhosp,x.dscg_caty_name_inhosp,x.drCodg,x.drName
            </if>
            <if test="queryParam.queryType==2">
                GROUP BY x.drCodg,x.drName
            </if>
            ORDER BY totalCdWeight desc
        )X
            WHERE
            x.drName IS NOT NULL
            AND x.drName !='-'
            AND x.drName != '未填写'
            <if test="queryParam.drCodg!=null and queryParam.drCodg!=''">
                AND x.drCodg= #{queryParam.drCodg}
            </if>
            <if test="queryParam.doctorIdList!=null">
                AND x.drCodg in
                <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="queryParam.queryType==1">
                GROUP BY x.priOutHosDeptCode,x.drCodg,x.drName
            </if>
            <if test="queryParam.queryType==2">
                GROUP BY x.drCodg,x.drName
            </if>
            ORDER BY totalCdWeight desc
        ) p
        <if test="queryParam.queryType==1">
            left join som_dept m
            on p.priOutHosDeptCode = m.code
        </if>
    </select>
<!--查询医院费用分析统计信息-->
    <select id="getCountInfo" resultType="com.my.som.vo.cdBusiness.CdDoctorAnalysisCountInfo">
        select
        x.drCodg AS drCodg,
        x.drName AS drName,
        IFNULL(count(1),0) AS totalMedicalNum,
        IFNULL(ROUND(count(1) / NULLIF(MAX(y.totalMedicalNum),0)*100,2),0) AS totalMedicalNumRate,
        IFNULL(count(case when x.grp_stas = '1' then 1 else null end),0)   AS inGroupMedicalNum,
        IFNULL(ROUND(count(case when x.grp_stas = '1' then 1 else null end) /
        NULLIF(MAX(y.inGroupMedicalNum),0)*100,2),0) AS  inGroupMedicalNumRate,
        IFNULL(count(case when x.grp_stas != '1' then 1 else null end),0)  AS notGroupMedicalNum,
        IFNULL(ROUND(count(case when x.grp_stas != '1' then 1 else null end) /
        NULLIF(MAX(y.inGroupMedicalNum),0)*100,2),0) AS  notGroupMedicalNumRate,
        IFNULL(count(distinct (case when x.grp_stas = '1' then x.cd_codg else null end)),0)   AS cdGroupNum,
        IFNULL(ROUND(count(distinct (case when x.grp_stas = '1' then x.cd_codg else null end)) /
        NULLIF(MAX(y.cdGroupNum),0)*100,2),0) AS  cdGroupNumRate,
        IFNULL(round(SUM(CASE WHEN x.grp_stas = '1' THEN x.cd_wt ELSE NULL END)/
        NULLIF (COUNT(CASE WHEN x.grp_stas = '1' THEN 1 ELSE NULL END),0),2),0)   AS cmi,
        IFNULL(MAX(y.hosCmi),0) AS hosCmi,
        IFNULL(SUM(CASE WHEN x.grp_stas = '1' THEN x.cd_wt ELSE NULL END),0)    AS totalCdWeight,
        IFNULL(ROUND(SUM(CASE WHEN x.grp_stas = '1' THEN x.cd_wt ELSE NULL END) /
        NULLIF(MAX(y.totalCdWeight),0)*100,2),0) AS  totalCdWeightRate,
        IFNULL(ROUND(AVG(x.act_ipt), 2),0)   AS avgDays,
        IFNULL(MAX(y.hosAvgDays),0) as hosAvgDays,
        IFNULL(ROUND(AVG(CASE WHEN x.grp_stas = '1' THEN x.act_ipt ELSE NULL END), 2),0)   AS inGroupAvgDays,
        IFNULL(MAX(y.hosInGroupAvgDays),0) as hosInGroupAvgDays,
        IFNULL(ROUND(AVG(x.ipt_sumfee), 2),0)  AS avgCost,
        IFNULL(MAX(y.hosAvgCost),0) as hosAvgCost,
        IFNULL(ROUND(AVG(CASE WHEN x.grp_stas = '1' THEN x.ipt_sumfee ELSE NULL END), 2),0)    AS inGroupAvgCost,
        IFNULL(MAX(y.hosInGroupAvgCost),0) as hosInGroupAvgCost,
        IFNULL(ROUND(SUM(CASE WHEN x.grp_stas = '1' THEN x.act_ipt ELSE NULL END / NULLIF(x.standard_ave_hosp_day,0)) /
        NULLIF(COUNT(CASE WHEN x.grp_stas = '1' THEN 1 ELSE NULL END),0), 2),0)  AS timeIndex,
        IFNULL(MAX(y.hosTimeIndex),0) as hosTimeIndex,
        IFNULL(ROUND(SUM(CASE WHEN x.grp_stas = '1' THEN x.ipt_sumfee ELSE NULL END / NULLIF(x.standard_avg_fee,0)) /
        NULLIF(COUNT(CASE WHEN x.grp_stas = '1' THEN 1 ELSE NULL END),0), 2),0)   AS costIndex,
        IFNULL(MAX(y.hosCostIndex),0) as hosCostIndex,
        IFNULL(ROUND(AVG(x.drugfee), 2),0)   AS avgDrugFee,
        IFNULL(MAX(y.hosAvgMedicalCost),0) as hosAvgMedicalCost,
        IFNULL(ROUND(AVG(x.mcs_fee), 2),0)   AS avgMcsFee,
        IFNULL(MAX(y.hosAvgMaterialCost),0) as hosAvgMaterialCost
        from (
        select
        <if test="queryParam.doctorType!=null and queryParam.doctorType!=''">
            (CASE #{queryParam.doctorType}#
            WHEN 'dct0' THEN a.ipdr_code
            WHEN 'dct1' THEN a.atddr_code
            WHEN 'dct2' THEN a.deptdrt_code
            WHEN 'dct3' THEN a.chfdr_code
            WHEN 'dct4' THEN a.train_dr_code
            WHEN 'dct5' THEN a.qltctrl_dr_code
            WHEN 'dct6' THEN a.intn_dr
            ELSE  a.ipdr_name
            END) AS drCodg,
            (CASE #{queryParam.doctorType}#
            WHEN 'dct0' THEN a.ipdr_name
            WHEN 'dct1' THEN a.atddr_name
            WHEN 'dct2' THEN a.deptdrt_name
            WHEN 'dct3' THEN a.chfdr_name
            WHEN 'dct4' THEN a.train_dr_name
            WHEN 'dct5' THEN a.qltctrl_dr_name
            WHEN 'dct6' THEN a.intn_dr
            ELSE  a.ipdr_name
            END) AS drName,
        </if>
        <if test="queryParam.doctorType==null or queryParam.doctorType==''">
            c.drCodg as drCodg,
            c.drName as drName,
        </if>
        a.grp_stas as grp_stas,
        a.cd_codg as cd_codg,
        a.cd_wt as cd_wt,
        a.act_ipt as act_ipt,
        a.ipt_sumfee as ipt_sumfee,
        a.standard_ave_hosp_day as standard_ave_hosp_day,
        a.standard_avg_fee as standard_avg_fee,
        a.dscg_caty_codg_inhosp as dscg_caty_codg_inhosp,
        a.dscg_caty_name_inhosp AS dscg_caty_name_inhosp,
        a.drugfee as drugfee,
        a.mcs_fee as mcs_fee
        from som_cd_grp_info a
        <if test="queryParam.doctorType==null or queryParam.doctorType==''">
            inner join (
            select
            b.settle_list_id as settle_list_id,
            b.drCodg as drCodg,
            b.drName as drName
            from (
            select
            SETTLE_LIST_ID as settle_list_id,
            ipdr_code as drCodg,
            ipdr_name as drName
            FROM som_cd_grp_info
            where ipdr_name is not null and ipdr_name !='-' and ipdr_name !='未填写'
            <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
                AND  HOSPITAL_ID = #{queryParam.hospitalId}
            </if>
            <if test="queryParam.b16c!=null and queryParam.b16c!=''">
                AND dscg_caty_codg_inhosp = #{queryParam.b16c}
            </if>
            <if test="queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and
                                                queryParam.cy_end_date!=null and queryParam.cy_end_date!=''">
                AND dscg_time BETWEEN #{queryParam.cy_start_date} and CONCAT(#{queryParam.cy_end_date},' 23:59:59')
            </if>
            <if test="queryParam.drCodg!=null and queryParam.drCodg!=''">
                AND `INHOS_DOCTOR_CODE` = #{queryParam.drCodg}
            </if>
            union all
            select
            SETTLE_LIST_ID as settle_list_id,
            atddr_code as drCodg,
            atddr_name as drName
            FROM som_cd_grp_info
            where atddr_name is not null and atddr_name !='-' and atddr_name !='未填写'
            <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
                AND  HOSPITAL_ID = #{queryParam.hospitalId}
            </if>
            <if test="queryParam.b16c!=null and queryParam.b16c!=''">
                AND dscg_caty_codg_inhosp = #{queryParam.b16c}
            </if>
            <if test="queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and
                                                queryParam.cy_end_date!=null and queryParam.cy_end_date!=''">
                AND dscg_time BETWEEN #{queryParam.cy_start_date} and CONCAT(#{queryParam.cy_end_date},' 23:59:59')
            </if>
            <if test="queryParam.drCodg!=null and queryParam.drCodg!=''">
                AND `ATTENDING_DOCTOR_CODE` = #{queryParam.drCodg}
            </if>
            union all
            select
            SETTLE_LIST_ID as settle_list_id,
            deptdrt_code as drCodg,
            deptdrt_name as drName
            FROM som_cd_grp_info
            where deptdrt_name is not null and deptdrt_name !='-' and deptdrt_name !='未填写'
            <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
                AND  HOSPITAL_ID = #{queryParam.hospitalId}
            </if>
            <if test="queryParam.b16c!=null and queryParam.b16c!=''">
                AND dscg_caty_codg_inhosp = #{queryParam.b16c}
            </if>
            <if test="queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and
                                                            queryParam.cy_end_date!=null and queryParam.cy_end_date!=''">
                AND dscg_time BETWEEN #{queryParam.cy_start_date} and CONCAT(#{queryParam.cy_end_date},' 23:59:59')
            </if>
            <if test="queryParam.drCodg!=null and queryParam.drCodg!=''">
                AND `DEPT_DIRECTOR_CODE` = #{queryParam.drCodg}
            </if>
            union all
            select
            SETTLE_LIST_ID as settle_list_id,
            chfdr_code as drCodg,
            chfdr_name as drName
            FROM
            som_cd_grp_info
            where chfdr_name is not null and chfdr_name !='-' and chfdr_name !='未填写'
            <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
                AND  HOSPITAL_ID = #{queryParam.hospitalId}
            </if>
            <if test="queryParam.b16c!=null and queryParam.b16c!=''">
                AND dscg_caty_codg_inhosp = #{queryParam.b16c}
            </if>
            <if test="queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and
                                                    queryParam.cy_end_date!=null and queryParam.cy_end_date!=''">
                AND dscg_time BETWEEN #{queryParam.cy_start_date} and CONCAT(#{queryParam.cy_end_date},' 23:59:59')
            </if>
            <if test="queryParam.drCodg!=null and queryParam.drCodg!=''">
                AND `MEDICAL_DIRECTOR_CODE` = #{queryParam.drCodg}
            </if>
            union all
            select
            SETTLE_LIST_ID as settle_list_id,
            train_dr_code as drCodg,
            train_dr_name as drName
            FROM som_cd_grp_info
            where train_dr_name is not null and train_dr_name !='-' and train_dr_name !='未填写'
            <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
                AND  HOSPITAL_ID = #{queryParam.hospitalId}
            </if>
            <if test="queryParam.b16c!=null and queryParam.b16c!=''">
                AND dscg_caty_codg_inhosp = #{queryParam.b16c}
            </if>
            <if test="queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and
                                        queryParam.cy_end_date!=null and queryParam.cy_end_date!=''">
                AND dscg_time BETWEEN #{queryParam.cy_start_date} and CONCAT(#{queryParam.cy_end_date},' 23:59:59')
            </if>
            <if test="queryParam.drCodg!=null and queryParam.drCodg!=''">
                AND `REFRESHER_DOCTOR_CODE` = #{queryParam.drCodg}
            </if>
            union all
            select
            SETTLE_LIST_ID as settle_list_id,
            qltctrl_dr_code as drCodg,
            qltctrl_dr_name as drName
            FROM som_cd_grp_info
            where qltctrl_dr_name is not null and qltctrl_dr_name !='-' and qltctrl_dr_name !='未填写'
            <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
                AND  HOSPITAL_ID = #{queryParam.hospitalId}
            </if>
            <if test="queryParam.b16c!=null and queryParam.b16c!=''">
                AND dscg_caty_codg_inhosp = #{queryParam.b16c}
            </if>
            <if test="queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and
                                            queryParam.cy_end_date!=null and queryParam.cy_end_date!=''">
                AND dscg_time BETWEEN #{queryParam.cy_start_date} and CONCAT(#{queryParam.cy_end_date},' 23:59:59')
            </if>
            <if test="queryParam.drCodg!=null and queryParam.drCodg!=''">
                AND `QUALITY_DOCTOR_CODE` = #{queryParam.drCodg}
            </if>
            union all
            select
            SETTLE_LIST_ID as settle_list_id,
            intn_dr as drCodg,
            intn_dr as drName
            FROM som_cd_grp_info
            where intn_dr is not null and intn_dr !='-' and intn_dr !='未填写'
            <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
                AND  HOSPITAL_ID = #{queryParam.hospitalId}
            </if>
            <if test="queryParam.b16c!=null and queryParam.b16c!=''">
                AND dscg_caty_codg_inhosp = #{queryParam.b16c}
            </if>
            <if test="queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and
                                                queryParam.cy_end_date!=null and queryParam.cy_end_date!=''">
                AND dscg_time BETWEEN #{queryParam.cy_start_date} and CONCAT(#{queryParam.cy_end_date},' 23:59:59')
            </if>
            <if test="queryParam.drCodg!=null and queryParam.drCodg!=''">
                AND `INTERN_DOCTOR_NAME` = #{queryParam.drCodg}
            </if>
            ) b group by settle_list_id,drCodg,drName
            ) c on a.settle_list_id = c.settle_list_id
        </if>
        where 1=1
        <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
            AND  a.HOSPITAL_ID = #{queryParam.hospitalId}
        </if>
        <if test="queryParam.b16c!=null and queryParam.b16c!=''">
            AND a.dscg_caty_codg_inhosp = #{queryParam.b16c}
        </if>
        <if test="queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and
                                                queryParam.cy_end_date!=null and queryParam.cy_end_date!=''">
            AND a.dscg_time BETWEEN #{queryParam.cy_start_date} and CONCAT(#{queryParam.cy_end_date},' 23:59:59')
        </if>
        <if test="queryParam.doctorIdList!=null">
            AND (
            a.deptdrt_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.chfdr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.atddr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.ipdr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.resp_nurs_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.train_dr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.intn_dr in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.codr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.qltctrl_dr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.qltctrl_nurs_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            )
        </if>
        ) x
        cross join (
        select
        IFNULL(count(1),0) AS totalMedicalNum,
        IFNULL(count(case when grp_stas = '1' then 1 else null end),0) AS inGroupMedicalNum,
        IFNULL(count(case when grp_stas != '1' then 1 else null end),0) AS notGroupMedicalNum,
        IFNULL(count(distinct (case when grp_stas = '1' then cd_codg else null end)),0) AS cdGroupNum,
        IFNULL(round(SUM(CASE WHEN grp_stas = '1' THEN cd_wt ELSE NULL END)/
        NULLIF (COUNT(CASE WHEN grp_stas = '1' THEN 1 ELSE NULL END),0),2),0) AS hosCmi,
        IFNULL(SUM(CASE WHEN grp_stas = '1' THEN cd_wt ELSE NULL END),0) AS totalCdWeight,
        IFNULL(ROUND(AVG(act_ipt), 2),0) AS hosAvgDays,
        IFNULL(ROUND(AVG(CASE WHEN grp_stas = '1' THEN act_ipt ELSE NULL END), 2),0) AS hosInGroupAvgDays,
        IFNULL(ROUND(AVG(ipt_sumfee), 2),0) AS hosAvgCost,
        IFNULL(ROUND(AVG(CASE WHEN grp_stas = '1' THEN ipt_sumfee ELSE NULL END), 2),0) AS hosInGroupAvgCost,
        IFNULL(ROUND(SUM(CASE WHEN grp_stas = '1' THEN act_ipt ELSE NULL END / NULLIF(standard_ave_hosp_day,0))
        /NULLIF(COUNT(CASE WHEN grp_stas = '1' THEN 1 ELSE NULL END),0), 2),0) AS hosTimeIndex,
        IFNULL(ROUND(SUM(CASE WHEN grp_stas = '1' THEN ipt_sumfee ELSE NULL END /
        NULLIF(standard_avg_fee,0)) /NULLIF(COUNT(CASE WHEN grp_stas = '1' THEN 1 ELSE NULL END),0), 2),0) AS hosCostIndex,
        IFNULL(ROUND(AVG(drugfee), 2),0) AS hosAvgMedicalCost,
        IFNULL(ROUND(AVG(mcs_fee), 2),0)   AS hosAvgMaterialCost
        from som_cd_grp_info
        where 1 = 1
        <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
            AND  HOSPITAL_ID = #{queryParam.hospitalId}
        </if>
        <if test="queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and
                                                    queryParam.cy_end_date!=null and queryParam.cy_end_date!=''">
            AND dscg_time BETWEEN #{queryParam.cy_start_date} and CONCAT(#{queryParam.cy_end_date},' 23:59:59')
        </if>
        ) y
        where x.drName is not null and x.drName !='-' and x.drName !='未填写'
        <if test="queryParam.drCodg!=null and queryParam.drCodg!=''">
            AND x.drCodg= #{queryParam.drCodg}
        </if>
        <if test="queryParam.doctorIdList!=null">
            AND x.drCodg in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="queryParam.queryType==1">
            GROUP BY x.dscg_caty_codg_inhosp,x.dscg_caty_name_inhosp,x.drCodg,x.drName
        </if>
        <if test="queryParam.queryType==2">
            GROUP BY x.drCodg,x.drName
        </if>
        ORDER BY totalCdWeight desc
    </select>
</mapper>