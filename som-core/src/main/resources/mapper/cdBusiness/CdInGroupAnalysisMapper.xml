<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.som.mapper.cdBusiness.CdInGroupAnalysisMapper">
<!--    查询成都入组分析主要信息-->
    <select id="list" resultType="com.my.som.vo.cdBusiness.CdInGroupAnalysisInfo">
        select a.*,
               b.NAME as priOutHosDeptName
        from
            (
                select a.dscg_caty_codg_inhosp AS priOutHosDeptCode,
                       IFNULL(ROUND(NULLIF(sum(case when a.grp_stas = '1' then 1 else 0 end),0) /
                                    NULLIF(sum(case when b.chk_flag = '1' then 1 else null end),0) * 100,2),0) AS inGrpupRate,
                       NULLIF(count(DISTINCT(case when a.grp_stas = '1' then a.cd_codg else null end)),0) as cdNum,
                       count(1) as medicalTotalNum,
                       count(case when a.grp_stas = '1' then 1 else null end) as groupNum,
                       count(case when a.grp_stas != '1' then 1 else null end) as noGroupNum,
                       COUNT(case when b.chk_flag = '0' then 1 else null end) as eliminateNum,
                       count(case when b.grp_fale_rea = '未找到主要诊断节代码' then 1 else null end) as mainCodeErrorNum,
                       count(case when b.grp_fale_rea like '%未找到级别%' then 1 else null end) as noGroupPlanNum,
                       count(case when b.grp_fale_rea = '住院天数大于60天或小于0' then 1 else null end) as mainCodeAndOperateErrorNum,
                       count(case when b.grp_fale_rea = '编码和性格不匹配' then 1 else null end) as codeAndSexErrorNum
                from som_cd_grp_info a
                     left join som_grp_rcd b
                     on a.SETTLE_LIST_ID = b.SETTLE_LIST_ID
                     <where>
                        <include refid="Common_sql"></include>
                     </where>
                group by a.dscg_caty_codg_inhosp
                order by inGrpupRate desc
            ) a
            left join som_dept b on a.priOutHosDeptCode = b.CODE
    </select>
<!--查询全院入组情况人次统计信息-->
    <select id="getTopCountInfo" resultType="com.my.som.vo.cdBusiness.CdInGroupTopCountInfo">
        select a.*,
               ifnull(round(ifnull(ifnull(a.medicalRecordNum-a.lastMonthMedicalRecordNum,0)/nullif(a.lastMonthMedicalRecordNum,0),0) * 100,2),0) as medicalRecordNumRingRatio,
               ifnull(round(ifnull(ifnull(a.medicalRecordNum-lastYearMedicalRecordNum,0)/nullif(a.lastYearMedicalRecordNum,0),0) * 100,2),0) as medicalRecordNumYOY,

               ifnull(round(ifnull(ifnull(a.cdNum-lastMonthCdNum,0)/nullif(a.lastMonthCdNum,0),0) * 100,2),0) as cdNumRingRatio,
               ifnull(round(ifnull(ifnull(a.cdNum-lastYearCdNum,0)/nullif(a.lastYearCdNum,0),0) * 100,2),0) as cdNumYOY,

               ifnull(round(ifnull(ifnull(a.drgInGroupMedcasVal-lastMonthInGroupNum,0)/nullif(a.lastMonthInGroupNum,0),0) * 100,2),0) as inGroupNumRatio,
               ifnull(round(ifnull(ifnull(a.drgInGroupMedcasVal-lastYearInGroupNum,0)/nullif(a.lastYearInGroupNum,0),0) * 100,2),0) as inGroupNumYOY,

               ifnull(round(ifnull(ifnull(a.notInGroupNum-lastMonthNotInGroupNum,0)/nullif(a.lastMonthNotInGroupNum,0),0) * 100,2),0) as notInGroupNumRatio,
               ifnull(round(ifnull(ifnull(a.notInGroupNum-lastYearNotInGroupNum,0)/nullif(a.lastYearNotInGroupNum,0),0) * 100,2),0) as notInGroupNumYOY
        from
            (
                select
                    ifnull(count(case when a.dscg_time between #{queryParam.cy_start_date} and CONCAT(#{queryParam.cy_end_date},' 23:59:59') then 1 else null end),0) as medicalRecordNum,
                    ifnull(count(case when a.dscg_time between #{queryParam.lastMonth_cy_start_date} and CONCAT(#{queryParam.lastMonth_cy_end_date},' 23:59:59') then 1 else null end),0) as lastMonthMedicalRecordNum,
                    ifnull(count(case when a.dscg_time between #{queryParam.lastYear_cy_start_date} and CONCAT(#{queryParam.lastYear_cy_end_date},' 23:59:59') then 1 else null end),0) as lastYearMedicalRecordNum,

                    ifnull(count(distinct(case when a.dscg_time between #{queryParam.cy_start_date} and CONCAT(#{queryParam.cy_end_date},' 23:59:59') then b.cd_codg else null end)),0) as cdNum,
                    ifnull(count(distinct(case when a.dscg_time between #{queryParam.lastMonth_cy_start_date} and CONCAT(#{queryParam.lastMonth_cy_end_date},' 23:59:59') then b.cd_codg else null end)),0) as lastMonthCdNum,
                    ifnull(count(distinct(case when a.dscg_time between #{queryParam.lastYear_cy_start_date} and CONCAT(#{queryParam.lastYear_cy_end_date},' 23:59:59') then b.cd_codg else null end)),0) as lastYearCdNum,

                    ifnull(count(case when a.dscg_time between #{queryParam.cy_start_date} and CONCAT(#{queryParam.cy_end_date},' 23:59:59') and b.grp_stas = '1' then 1 else null end),0) as drgInGroupMedcasVal,
                    ifnull(count(case when a.dscg_time between #{queryParam.lastMonth_cy_start_date} and CONCAT(#{queryParam.lastMonth_cy_end_date},' 23:59:59') and b.grp_stas = '1' then 1 else null end),0) as lastMonthInGroupNum,
                    ifnull(count(case when a.dscg_time between #{queryParam.lastYear_cy_start_date} and CONCAT(#{queryParam.lastYear_cy_end_date},' 23:59:59') and b.grp_stas = '1' then 1 else null end),0) as lastYearInGroupNum,

                    ifnull(count(case when a.dscg_time between #{queryParam.cy_start_date} and CONCAT(#{queryParam.cy_end_date},' 23:59:59') and b.grp_stas != '1' then 1 else null end),0) as notInGroupNum,
                    ifnull(count(case when a.dscg_time between #{queryParam.lastMonth_cy_start_date} and CONCAT(#{queryParam.lastMonth_cy_end_date},' 23:59:59') and b.grp_stas != '1' then 1 else null end),0) as lastMonthNotInGroupNum,
                    ifnull(count(case when a.dscg_time between #{queryParam.lastYear_cy_start_date} and CONCAT(#{queryParam.lastYear_cy_end_date},' 23:59:59') and b.grp_stas != '1' then 1 else null end),0) as lastYearNotInGroupNum
                from som_cd_grp_info a
                         left join som_grp_rcd b
                                   on a.SETTLE_LIST_ID = b.SETTLE_LIST_ID
            ) a
    </select>
<!--查询未入组原因统计信息-->
    <select id="getNoGroupResonCountInfo" resultType="com.my.som.vo.cdBusiness.CdInGroupAnalysisCountInfo">
        select
        x.*,
        IFNULL(CONCAT(ROUND(IFNULL(x.medcasVal,0)/NULLIF(y.totals,0)*100,2),'%'),0) AS allRate
        from(
        select
        c.*,
        IFNULL(CONCAT(ROUND(IFNULL(c.medcasVal/NULLIF((d.notInGroupTotals+e.eliminateTotals),0),0)*100,2),'%'),0) as notInGroupRate
        from(
            select
            '未入组原因' AS notInGroupName,
            '排除病案数' AS notInGroupReason,
            COUNT(case when chk_flag = '0' then 1 else null end) AS medcasVal
            from som_cd_grp_info a
            left join som_grp_rcd b
            on a.SETTLE_LIST_ID = b.SETTLE_LIST_ID
            <where>
                <include refid="Common_sql"></include>
            </where>
            union all
            select
            '未入组原因' AS notInGroupName,
            '未找到主要节代码' AS notInGroupReason,
            COUNT(1) AS medcasVal
            from som_cd_grp_info a LEFT JOIN som_grp_rcd b ON a.SETTLE_LIST_ID = b.SETTLE_LIST_ID
            where b.grp_fale_rea ='未找到主要节代码'
            <include refid="Common_sql"></include>
            union all
            select
            '未入组原因' AS notInGroupName,
            '未找到级别' AS notInGroupReason,
            COUNT(1) AS medcasVal
            from som_cd_grp_info a LEFT JOIN som_grp_rcd b ON a.SETTLE_LIST_ID = b.SETTLE_LIST_ID
            where b.grp_fale_rea ='未找到级别'
            <include refid="Common_sql"></include>
            union all
            select
            '歧义原因' AS notInGroupName,
            '住院天数大于60天或小于0天' AS notInGroupReason,
            COUNT(1) AS medcasVal
            from som_cd_grp_info a LEFT JOIN som_grp_rcd b ON a.SETTLE_LIST_ID = b.SETTLE_LIST_ID
            where b.grp_fale_rea ='住院天数大于60天或小于0天'
                <include refid="Common_sql"></include>
            union all
            select
            '歧义原因' AS notInGroupName,
            '编码和性别不匹配' AS notInGroupReason,
            COUNT(1) AS medcasVal
            from som_cd_grp_info a LEFT JOIN som_grp_rcd b ON a.SETTLE_LIST_ID = b.SETTLE_LIST_ID
            where b.grp_fale_rea ='编码和性别不匹配'
                <include refid="Common_sql"></include>
            )c inner join(
            select
            count(1) as notInGroupTotals
            from som_cd_grp_info a LEFT JOIN som_grp_rcd b ON a.SETTLE_LIST_ID = b.SETTLE_LIST_ID
            where b.grp_fale_rea is not null
                <include refid="Common_sql"></include>
            )d
                inner join (
                select
                count(case when chk_flag = '0' then 1 else null end) as eliminateTotals
                from som_grp_rcd
                )e on 1=1
            ) x
            inner join (
                select count(1) as totals
                from som_cd_grp_info a
                left join som_grp_rcd b
                on a.SETTLE_LIST_ID = b.SETTLE_LIST_ID
                <where>
                    <include refid="Common_sql"></include>
                </where>
                )y
    </select>
    <sql id="Common_sql">
        <!-- 出院时间范围 -->
        <if test="queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and queryParam.cy_end_date!=null and queryParam.cy_end_date!=''">
            and a.dscg_time BETWEEN #{queryParam.cy_start_date} and CONCAT(#{queryParam.cy_end_date},' 23:59:59')
        </if>
        <if test="queryParam.b16c!=null and queryParam.b16c!=''">
            AND  a.dscg_caty_codg_inhosp = #{queryParam.b16c}
        </if>
    </sql>
</mapper>