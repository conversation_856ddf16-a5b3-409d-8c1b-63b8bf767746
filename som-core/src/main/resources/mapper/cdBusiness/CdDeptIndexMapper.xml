<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.som.mapper.cdBusiness.CdDeptIndexMapper">
<!-- 查询科室病组指标信息*/-->
    <select id="list" resultType="com.my.som.vo.cdBusiness.CdDeptIndexVo">
        select a.*,
            b.NAME as priOutHosDeptName
        from
        (select
            a.dscg_caty_codg_inhosp AS priOutHosDeptCode,
            a.dscg_caty_codg_inhosp AS dept_code,
            IFNULL(count(1),0) AS medicalRecordNum,
            COUNT(case when a.grp_stas = '1' then 1 else null end) AS groupNum,
            COUNT(case when a.grp_stas != '1' then 1 else null end) AS noGroupNum,
            IFNULL(count(distinct (case when a.grp_stas = '1' then a.cd_codg else null end)),0) AS cdGroupNum,
            IFNULL(round(SUM(CASE WHEN a.grp_stas = '1' THEN a.cd_wt ELSE NULL END)/
            NULLIF (COUNT(CASE WHEN a.grp_stas = '1' THEN 1 ELSE NULL END),0),2),0)   AS cmi,
            ROUND(IFNULL(SUM(a.cd_wt),0),2) AS totalAreaWeight, <!--总权重-->
            IFNULL(ROUND(AVG(a.act_ipt), 2),0)   AS avgDays,
            IFNULL(ROUND(AVG(CASE WHEN a.grp_stas = '1' THEN a.act_ipt ELSE NULL END), 2),0)   AS inGroupAvgDays,
            IFNULL(ROUND(AVG(a.ipt_sumfee), 2),0)  AS avgCost,
            IFNULL(ROUND(AVG(CASE WHEN a.grp_stas = '1' THEN a.ipt_sumfee ELSE NULL END), 2),0)    AS inGroupAvgCost,
            IFNULL(ROUND(SUM(CASE WHEN a.grp_stas = '1' THEN a.act_ipt ELSE NULL END / NULLIF(a.standard_ave_hosp_day,0)) /
            NULLIF(COUNT(CASE WHEN a.grp_stas = '1' THEN 1 ELSE NULL END),0), 2),0)  AS timeIndex,
            IFNULL(ROUND(SUM(CASE WHEN a.grp_stas = '1' THEN a.ipt_sumfee ELSE NULL END / NULLIF(a.standard_avg_fee,0)) /
            NULLIF(COUNT(CASE WHEN a.grp_stas = '1' THEN 1 ELSE NULL END),0), 2),0)   AS costIndex,
            IFNULL(CONCAT(ROUND(SUM(a.drugfee)/NULLIF(SUM(a.ipt_sumfee),0)*100,2),'%'),0) AS medicalCostRate,<!--  药品费占比   -->
            IFNULL(CONCAT(ROUND(SUM(a.mcs_fee)/NULLIF(SUM(a.ipt_sumfee),0)*100,2),'%'),0) AS materialCostRate <!--  耗材费占比   -->
        from som_cd_grp_info a
        where 1 = 1
        <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
            AND  a.HOSPITAL_ID = #{queryParam.hospitalId}
        </if>
        <if test="queryParam.b16c!=null and queryParam.b16c!=''">
            AND a.dscg_caty_codg_inhosp = #{queryParam.b16c}
        </if>
        <if test="queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and
        queryParam.cy_end_date!=null and queryParam.cy_end_date!=''">
            AND a.dscg_time BETWEEN #{queryParam.cy_start_date} and CONCAT(#{queryParam.cy_end_date},' 23:59:59')
        </if>
        <if test="queryParam.doctorIdList!=null">
            AND (
            a.deptdrt_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.chfdr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.atddr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.ipdr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.resp_nurs_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.train_dr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.intn_dr in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.codr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.qltctrl_dr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.qltctrl_nurs_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            )
        </if>
        GROUP BY a.dscg_caty_codg_inhosp
        ORDER BY totalAreaWeight desc
        ) a
            left join som_dept b
                on a.priOutHosDeptCode = b.CODE
    </select>
<!--    查询科室病组指标统计信息-->
    <select id="getCountInfo" resultType="com.my.som.vo.cdBusiness.CdDeptCountVo">
        select
        x.dscg_caty_codg_inhosp AS priOutHosDeptCode,
        IFNULL(x.dscg_caty_name_inhosp,'未填写科室') AS priOutHosDeptName,
        IFNULL(count(1),0) AS totalMedicalRecordNum,
        IFNULL(ROUND(count(1) / NULLIF(y.totalMedicalNum,0)*100,2),0) AS totalMedicalRecordNumRate,
        IFNULL(count(case when x.grp_stas = '1' then 1 else null end),0)   AS inGroupMedicalNum,
        IFNULL(ROUND(count(case when x.grp_stas = '1' then 1 else null end) /
        NULLIF(y.inGroupMedicalNum,0)*100,2),0) AS  inGroupMedicalNumRate,
        IFNULL(count(case when x.grp_stas != '1' then 1 else null end),0)  AS notGroupMedicalNum,
        IFNULL(ROUND(count(case when x.grp_stas != '1' then 1 else null end) /
        NULLIF(y.inGroupMedicalNum,0)*100,2),0) AS  notGroupMedicalNumRate,
        IFNULL(count(distinct (case when x.grp_stas = '1' then x.cd_codg else null end)),0)   AS cdGroupNum,
        IFNULL(ROUND(count(distinct (case when x.grp_stas = '1' then x.cd_codg else null end)) /
        NULLIF(y.cdGroupNum,0)*100,2),0) AS  cdGroupNumRate,
        IFNULL(ROUND(AVG(x.act_ipt), 2),0)   AS avgDays,
        y.hosAvgDays as hosAvgDays,
        IFNULL(ROUND(AVG(CASE WHEN x.grp_stas = '1' THEN x.act_ipt ELSE NULL END), 2),0)   AS inGroupAvgDays,
        y.hosInGroupAvgDays as hosInGroupAvgDays,
        IFNULL(ROUND(AVG(x.ipt_sumfee), 2),0)  AS avgCost,
        y.hosAvgCost as hosAvgCost,
        IFNULL(ROUND(AVG(CASE WHEN x.grp_stas = '1' THEN x.ipt_sumfee ELSE NULL END), 2),0)    AS inGroupAvgCost,
        y.hosInGroupAvgCost as hosInGroupAvgCost,
        IFNULL(ROUND(SUM(CASE WHEN x.grp_stas = '1' THEN x.act_ipt ELSE NULL END / NULLIF(x.standard_ave_hosp_day,0)) /
        NULLIF(COUNT(CASE WHEN x.grp_stas = '1' THEN 1 ELSE NULL END),0), 2),0)  AS timeIndex,
        y.hosTimeIndex as hosTimeIndex,
        IFNULL(ROUND(SUM(CASE WHEN x.grp_stas = '1' THEN x.ipt_sumfee ELSE NULL END / NULLIF(x.standard_avg_fee,0)) /
        NULLIF(COUNT(CASE WHEN x.grp_stas = '1' THEN 1 ELSE NULL END),0), 2),0)   AS costIndex,
        y.hosCostIndex as hosCostIndex,
        IFNULL(ROUND(AVG(x.drugfee), 2),0)   AS avgDrugFee,
        y.hosAvgMedicalCost as hosAvgMedicalCost,
        IFNULL(ROUND(AVG(x.mcs_fee), 2),0)   AS avgMcsFee,
        y.hosAvgMaterialCost as hosAvgMaterialCost
        from (
        select
        b.dscg_caty_codg_inhosp as dscg_caty_codg_inhosp,
        d.`NAME` AS dscg_caty_name_inhosp,
        a.grp_stas as grp_stas,
        a.cd_codg as cd_codg,
        b.act_ipt as act_ipt,
        b.ipt_sumfee as ipt_sumfee,
        convert(AES_DECRYPT(UNHEX(c.standard_ave_hosp_day),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) using utf8) as standard_ave_hosp_day,
        convert(AES_DECRYPT(UNHEX(c.standard_avg_fee),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) using utf8) as standard_avg_fee,
        b.drugfee as drugfee,
        b.mcs_fee as mcs_fee
        from som_grp_rcd a
        inner join som_cd_grp_info b on a.SETTLE_LIST_ID = b.SETTLE_LIST_ID
        left join som_cd_standard_info c on a.cd_codg=c.cd_codg AND SUBSTR(b.dscg_time,1,4) = c.STANDARD_YEAR
        LEFT JOIN som_dept d
        ON b.dscg_caty_codg_inhosp = d.`CODE`
        where 1 = 1
        <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
            AND  a.HOSPITAL_ID = #{queryParam.hospitalId}
        </if>
        <if test="queryParam.b16c!=null and queryParam.b16c!=''">
            AND b.dscg_caty_codg_inhosp = #{queryParam.b16c}
        </if>
        <if test="queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and
            queryParam.cy_end_date!=null and queryParam.cy_end_date!=''">
            AND b.dscg_time BETWEEN #{queryParam.cy_start_date} and CONCAT(#{queryParam.cy_end_date},' 23:59:59')
        </if>
        <if test="queryParam.doctorIdList!=null">
            AND (
            b.deptdrt_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            b.chfdr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            b.atddr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            b.ipdr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            b.resp_nurs_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            b.train_dr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            b.intn_dr in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            b.codr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            b.qltctrl_dr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            b.qltctrl_nurs_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            )
        </if>
        )x
        cross join (
        select
        IFNULL(count(1),0) AS totalMedicalNum,
        IFNULL(count(case when a.grp_stas = '1' then 1 else null end),0) AS inGroupMedicalNum,
        IFNULL(count(case when a.grp_stas != '1' then 1 else null end),0) AS notGroupMedicalNum,
        IFNULL(count(distinct (case when a.grp_stas = '1' then a.cd_codg else null end)),0) AS cdGroupNum,
        IFNULL(ROUND(AVG(b.act_ipt), 2),0) AS hosAvgDays,
        IFNULL(ROUND(AVG(CASE WHEN a.grp_stas = '1' THEN b.act_ipt ELSE NULL END), 2),0) AS hosInGroupAvgDays,
        IFNULL(ROUND(AVG(b.ipt_sumfee), 2),0) AS hosAvgCost,
        IFNULL(ROUND(AVG(CASE WHEN a.grp_stas = '1' THEN b.ipt_sumfee ELSE NULL END), 2),0) AS hosInGroupAvgCost,
        IFNULL(ROUND(SUM(CASE WHEN a.grp_stas = '1' THEN b.act_ipt ELSE NULL END / NULLIF(convert(AES_DECRYPT(UNHEX(c.standard_ave_hosp_day),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) using utf8),0))
        /NULLIF(COUNT(CASE WHEN a.grp_stas = '1' THEN 1 ELSE NULL END),0), 2),0) AS hosTimeIndex,
        IFNULL(ROUND(SUM(CASE WHEN a.grp_stas = '1' THEN b.ipt_sumfee ELSE NULL END /
        NULLIF(convert(AES_DECRYPT(UNHEX(c.standard_avg_fee),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) using utf8),0)) /NULLIF(COUNT(CASE WHEN a.grp_stas = '1' THEN 1 ELSE NULL END),0), 2),0) AS hosCostIndex,
        IFNULL(ROUND(AVG(b.drugfee), 2),0) AS hosAvgMedicalCost,
        IFNULL(ROUND(AVG(b.mcs_fee), 2),0)   AS hosAvgMaterialCost
        from som_grp_rcd a
        inner join som_cd_grp_info b on a.SETTLE_LIST_ID = b.SETTLE_LIST_ID
        left join som_cd_standard_info c on a.cd_codg=c.cd_codg AND SUBSTR(b.dscg_time,1,4) = c.STANDARD_YEAR
        where 1 = 1
        <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
            AND  a.HOSPITAL_ID = #{queryParam.hospitalId}
        </if>
        <if test="queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and queryParam.cy_end_date!=null and queryParam.cy_end_date!=''">
            AND b.dscg_time BETWEEN #{queryParam.cy_start_date} and CONCAT(#{queryParam.cy_end_date},' 23:59:59')
        </if>
        ) y
        GROUP BY x.dscg_caty_codg_inhosp,x.dscg_caty_name_inhosp, y.totalMedicalNum
        ORDER BY cdGroupNum desc
    </select>
<!--    查询指数详情信息-->
    <select id="queryConsumptionIndex" resultType="com.my.som.vo.cdBusiness.CdDeptIndexVo">
        SELECT a.dscg_caty_codg_inhosp AS deptCode,
        a.cd_codg AS cdCodg,
        ROUND(IFNULL(AVG(a.act_ipt),0),2) AS avgInHosDays,
        ROUND(IFNULL(CONVERT(AES_DECRYPT(UNHEX(b.standard_ave_hosp_day),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) using utf8),0),2) AS dipInHosDays,
        ROUND(IFNULL(AVG(a.ipt_sumfee),0),2) AS avgInHosCost,
        ROUND(IFNULL(IFNULL(AVG(a.act_ipt),0)/NULLIF(CONVERT(AES_DECRYPT(UNHEX(b.standard_ave_hosp_day),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) using utf8),0),0) - 1,2) AS timeIndex
        FROM som_cd_grp_info a
        LEFT JOIN som_cd_standard_info b
        ON a.cd_codg = b.cd_codg
        AND SUBSTR(a.dscg_time,1,4) = b.STANDARD_YEAR
        WHERE a.cd_codg IS NOT NULL
        <if test="queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and
                   queryParam.cy_end_date!=null and queryParam.cy_end_date!=''">
            <![CDATA[
           AND a.dscg_time >= #{queryParam.cy_start_date}
           AND a.dscg_time <= CONCAT(#{queryParam.cy_end_date},' 23:59:59')
           ]]>
        </if>
        <if test="queryParam.b16c!=null and queryParam.b16c!=''">
            AND a.dscg_caty_codg_inhosp = #{queryParam.b16c}
        </if>
        GROUP BY a.dscg_caty_codg_inhosp,
        a.cd_codg,
        b.standard_ave_hosp_day
    </select>
</mapper>