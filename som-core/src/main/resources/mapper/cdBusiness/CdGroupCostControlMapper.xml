<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.som.mapper.cdBusiness.CdGroupCostControlMapper">

    <!-- 查询数据 -->
    <select id="getList" resultType="com.my.som.vo.cdBusiness.CdCostControlVo">
        select a.PATIENT_ID AS patientId,
               a.cd_codg AS cdCodg,
               a.CD_NAME AS cdName,
               a.main_diag_dise_codg AS mainDiagDiseCodg,
               a.main_oprn_oprt_codg AS mainDiagDiseName,
               a.NAME AS name,
               a.gend AS gend,
               a.AGE AS age,
               b.A54 AS insuredType,
               a.dscg_way AS dscgWay,
               a.adm_time AS inHosTime,
               a.dscg_time AS outHosTime,
               a.adm_caty_name_inhosp AS priInHosDeptName,
               a.dscg_caty_name_inhosp AS priOutHosDeptName,
               a.ipdr_name AS inHosDoctorName,
               a.act_ipt AS inHosDays,
               a.ipt_sumfee AS inHosTotalCost

        from som_cd_grp_info a
        left join som_hi_invy_bas_info b
        on a.SETTLE_LIST_ID=b.ID
        <where>
            <!-- 开始时间 -->
            <if test="begnDate != null and begnDate != ''">
                AND a.dscg_time >= #{begnDate,jdbcType=VARCHAR}
            </if>

            <!-- 结束时间 -->
            <if test="expiDate != null and expiDate != ''">
                <![CDATA[
                    AND a.dscg_time <= #{expiDate,jdbcType=VARCHAR}
                ]]>
            </if>

               <include
                       refid="com.my.som.mapper.newBusiness.common.NewBusinessCommonAnalysisMapper.queryBusCommon"></include>

            <!-- 患者姓名 -->
            <if test="name != null and name != ''">
                AND INSTR(a.NAME, #{name,jdbcType=VARCHAR}) > 0
            </if>

            <!-- 成都分组编码或名称 -->
            <if test="cdGroup != null and cdGroup != ''">
                AND (
                    INSTR(a.cd_codg, #{cdGroup,jdbcType=VARCHAR}) > 0
                    OR
                    INSTR(a.CD_NAME, #{cdGroup,jdbcType=VARCHAR}) > 0
                )
            </if>

            <!-- 参保类型 -->
            <if test="insuType != null and insuType.size() > 0">
                AND b.A54 in
                <foreach item="item" index="list" collection="insuType" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <!-- 离院方式 -->
            <if test="outHos != null and outHos.size() > 0">
                AND a.dscg_way in
                <foreach item="item" index="list" collection="outHos" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>
</mapper>