<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.som.mapper.cdBusiness.CdGroupDetailMapper">
<!--    获取数据-->
    <select id="getList" resultType="com.my.som.vo.cdBusiness.CdDetailVo">
        select  e.*,
                convert(AES_DECRYPT(UNHEX(f.standard_ave_hosp_day),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) using utf8) AS standardAveHospDay,
                convert(AES_DECRYPT(UNHEX(f.standard_avg_fee),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) using utf8) AS standardAvgFee,
                f.stsb_fee AS stsbFee
        from (
                select  a.PATIENT_ID AS patientId,
                        a.NAME AS name,
                        a.gend AS gend,
                        a.AGE AS age,
                        b.A54 AS insuredType,
                        a.cd_codg AS cdCodg,
                        a.CD_NAME AS cdName,
                        a.main_diag_dise_codg AS mainDiagDiseCodg,
                        a.main_oprn_oprt_codg AS mainDiagDiseName,
                        c.pay_stsb AS payStsb,
                        a.adm_time AS inHosTime,
                        a.dscg_time AS outHosTime,
                        a.act_ipt AS inHosDays,
                        a.ipt_sumfee AS inHosTotalCost,
                        d.hosp_lv AS hospLv,
                        g.*
                from som_cd_grp_info a
                left join (
                        select  hi_setl_invy_id,
                                MAX( CASE WHEN med_chrg_itemname = '床位费' THEN amt ELSE NULL END ) AS cwf,
                                MAX( CASE WHEN med_chrg_itemname = '诊查费' THEN amt ELSE NULL END ) AS zcf,
                                MAX( CASE WHEN med_chrg_itemname = '检查费' THEN amt ELSE NULL END ) AS jcf,
                                MAX( CASE WHEN med_chrg_itemname = '化验费' THEN amt ELSE NULL END ) AS hyf,
                                MAX( CASE WHEN med_chrg_itemname = '治疗费' THEN amt ELSE NULL END ) AS treat_fee,
                                MAX( CASE WHEN med_chrg_itemname = '手术费' THEN amt ELSE NULL END ) AS ssf,
                                MAX( CASE WHEN med_chrg_itemname = '护理费' THEN amt ELSE NULL END ) AS nursfee,
                                MAX( CASE WHEN med_chrg_itemname = '卫生材料费' THEN amt ELSE NULL END ) AS wsclf,
                                MAX( CASE WHEN med_chrg_itemname = '西药费' THEN amt ELSE NULL END ) AS west_fee,
                                MAX( CASE WHEN med_chrg_itemname = '中药饮片费' THEN amt ELSE NULL END ) AS zyypf,
                                MAX( CASE WHEN med_chrg_itemname = '中成药' THEN amt ELSE NULL END ) AS zcy,
                                MAX( CASE WHEN med_chrg_itemname = '一般治疗费' THEN amt ELSE NULL END ) AS ybzlf,
                                MAX( CASE WHEN med_chrg_itemname = '挂号费' THEN amt ELSE NULL END ) AS ghf,
                                MAX( CASE WHEN med_chrg_itemname = '其他费' THEN amt ELSE NULL END ) AS qt
                        from
                        som_hi_setl_invy_med_fee_info
                        GROUP BY
                        hi_setl_invy_id
                ) g
                on a.SETTLE_LIST_ID = g.hi_setl_invy_id
                left join som_hi_invy_bas_info b
                on a.SETTLE_LIST_ID=b.ID
                left join som_grp_rcd c
                on a.SETTLE_LIST_ID=c.SETTLE_LIST_ID
                left join som_hosp_info d
                on a.HOSPITAL_ID = d.HOSPITAL_ID
                <where>
                    <!-- 开始时间 -->
                    <if test="begnDate != null and begnDate != ''">
                        AND a.dscg_time >= #{begnDate,jdbcType=VARCHAR}
                    </if>

                    <!-- 结束时间 -->
                    <if test="expiDate != null and expiDate != ''">
                        <![CDATA[
                            AND a.dscg_time <= #{expiDate,jdbcType=VARCHAR}
                        ]]>
                    </if>

                       <include
                               refid="com.my.som.mapper.newBusiness.common.NewBusinessCommonAnalysisMapper.queryBusCommon"></include>

                    <!-- 成都分组编码或名称 -->
                    <if test="cdGroup != null and cdGroup != ''">
                        AND (
                            INSTR(a.cd_codg, #{cdGroup,jdbcType=VARCHAR}) > 0
                            OR
                            INSTR(a.CD_NAME, #{cdGroup,jdbcType=VARCHAR}) > 0
                        )
                    </if>

                    <!-- 患者姓名 -->
                    <if test="name != null and name != ''">
                        AND INSTR(a.NAME, #{name,jdbcType=VARCHAR}) > 0
                    </if>

                    <!-- 付费区间 -->
                    <if test="payRanges != null and payRanges.size() > 0 ">
                        AND c.pay_stsb in
                        <foreach item="item" index="list" collection="payRanges" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                    </if>

                    <!-- 参保类型 -->
                    <if test="insuType != null and insuType.size() > 0">
                        AND b.A54 in
                        <foreach item="item" index="list" collection="insuType" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                    </if>
                </where>
        ) e
        left join som_cd_standard_info f
        on e.cdCodg = f.cd_codg
        and e.hospLv = f.hosp_lv
    </select>

    <!-- 获取入院入组信息 -->
    <select id="queryGroupInfo" resultType="com.my.som.vo.cdBusiness.CdDetailVo">
        select count(1) AS inHosCount,
               IFNULL(sum(case when c.grp_stas = 1 then 1 else null end),0) as drgInGroupMedcasVal,
               IFNULL(sum(case when c.grp_stas = 0 or c.grp_stas is null then 1 else null end),0) as nonInGroupNum
        from som_cd_grp_info a
        INNER JOIN som_hi_invy_bas_info b
        on a.settle_list_id = b.id
        left join som_grp_rcd c
        on a.SETTLE_LIST_ID = c.SETTLE_LIST_ID
        <where>
            <include refid="queryCondition" />
            <!-- 开始时间 -->
                <if test="begnDate != null and begnDate != ''">
                AND a.adm_time >= #{begnDate,jdbcType=VARCHAR}
            </if>

            <!-- 结束时间 -->
            <if test="expiDate != null and expiDate != ''">
                <![CDATA[
                    AND a.adm_time <= #{expiDate,jdbcType=VARCHAR}
                ]]>
            </if>
        </where>
    </select>
    
    <!-- 获取资源消耗信息 -->
    <select id="queryResourceConsumptionData" resultType="com.my.som.vo.cdBusiness.CdDetailVo">
        select m.*, n.*
        from (
             select IFNULL(ROUND(avg(a.act_ipt),2),0) as avgDays,
                    IFNULL(ROUND(avg(a.ipt_sumfee),2),0) as avgCost
             from som_cd_grp_info a
             INNER JOIN som_hi_invy_bas_info b
             on a.settle_list_id = b.id
             left join som_grp_rcd c
             on a.SETTLE_LIST_ID = c.SETTLE_LIST_ID
             <where>
                 <include refid="queryCondition" />
                 <!-- 开始时间 -->
                 <if test="begnDate != null and begnDate != ''">
                    AND a.dscg_time >= #{begnDate,jdbcType=VARCHAR}
                 </if>

                 <!-- 结束时间 -->
                 <if test="expiDate != null and expiDate != ''">
                    <![CDATA[
                        AND a.dscg_time <= #{expiDate,jdbcType=VARCHAR}
                    ]]>
                 </if>
            <include
                    refid="com.my.som.mapper.newBusiness.common.NewBusinessCommonAnalysisMapper.queryBusCommon"></include>
             </where>
        ) m
        CROSS JOIN (
            select IFNULL(ROUND(AVG(a.act_ipt),2),0) AS areaAvgDays,
                   IFNULL(ROUND(avg(a.ipt_sumfee),2),0) as areaAvgCost
            from som_cd_grp_info a
        ) n
    </select>
    
    <!-- 获取不同医保类型数据 -->
    <select id="queryDifferentMedicalTypeData" resultType="com.my.som.vo.cdBusiness.CdDetailVo">
        select n.labl_name AS insuredType,
			 m.*
        from (
            select b.A54,
                   count(1) AS totalNum,
                   sum(case when c.grp_stas = 1 then 1 else null end) as drgInGroupMedcasVal,
                   sum(case when c.grp_stas = 0 or c.grp_stas is null then 1 else null end) as nonInGroupNum,
                   ROUND(IFNULL(avg(d.refer_sco),0),2) as avgScore,
                   count(case when e.settle_list_id is not null then 1 else null end) AS codeErrorNum
            from som_cd_grp_info a
            INNER JOIN som_hi_invy_bas_info b on a.settle_list_id = b.id
            left join som_grp_rcd c on a.SETTLE_LIST_ID = c.SETTLE_LIST_ID
            left join som_setl_invy_qlt_dedu_point_detl d on a.settle_list_id = d.settle_list_id
            left join (
                select settle_list_id
                from som_setl_invy_chk_err_rcd
                where err_type = 'LE01'
            ) e on a.settle_list_id = e.settle_list_id
            <where>
                <include refid="queryCondition" />
                <!-- 开始时间 -->
                <if test="begnDate != null and begnDate != ''">
                    AND a.dscg_time >= #{begnDate,jdbcType=VARCHAR}
                 </if>

                <!-- 结束时间 -->
                <if test="expiDate != null and expiDate != ''">
                    <![CDATA[
                        AND a.dscg_time <= #{expiDate,jdbcType=VARCHAR}
                    ]]>
                 </if>

            <include
                    refid="com.my.som.mapper.newBusiness.common.NewBusinessCommonAnalysisMapper.queryBusCommon"></include>
            </where>
            group by b.A54
        ) m
        left join (
            select data_val,
                   labl_name
            from som_sys_code
            where code_type = 'INSURANCE_TYPE'
            and is_del = '0'
        ) n
        on m.a54 = n.data_val
    </select>

    <!-- 查询条件 -->
    <sql id="queryCondition">
        <!-- 科室 -->
        <if test="deptCode != null and deptCode != ''">
            AND a.dscg_caty_codg_inhosp = #{deptCode,jdbcType=VARCHAR}
        </if>

        <!-- 成都分组编码或名称 -->
        <if test="cdGroup != null and cdGroup != ''">
            AND (
                INSTR(a.cd_codg, #{cdGroup,jdbcType=VARCHAR}) > 0
                OR
                INSTR(a.CD_NAME, #{cdGroup,jdbcType=VARCHAR}) > 0
            )
        </if>
        <!-- 参保类型 -->
        <if test="insuType != null and insuType.size() > 0">
            AND b.A54 in
            <foreach item="item" index="list" collection="insuType" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

        <!-- 离院方式 -->
        <if test="outHos != null and outHos.size() > 0">
            AND a.dscg_way in
            <foreach item="item" index="list" collection="outHos" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </sql>
</mapper>