<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.my.som.mapper.cdBusiness.CdPayToPredictMapper">

    <!-- 查询数据 -->
    <select id="getList" resultType="com.my.som.vo.cdBusiness.CdPayToPredictVo">
        select a.PATIENT_ID AS patientId,
               a.name,
               b.A54 as insuredType,
               a.cd_codg AS cdCodg,
               a.CD_NAME AS cdName,
               (ifnull(c.cd_grp_wt,0) * 100) as point,
               a.medcas_type as recodeType,
               a.dscg_caty_name_inhosp as dept,
               null AS basePoint,
               null as adjm_cof,
               a.ipt_sumfee AS sumfee,
               IFNULL(ROUND(convert(AES_DECRYPT(UNHEX(c.standard_avg_fee),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) using utf8),2),0) AS areaStandardCost,
               null as recordType ,
               null as payCost,
               a.ipt_sumfee as originProjCost
        from som_cd_grp_info a
            left join som_hi_invy_bas_info b
            on a.SETTLE_LIST_ID = b.ID
                left join som_cd_standard_info c
                on  a.cd_codg = c.cd_codg
        AND SUBSTR(a.dscg_time,1,4) = c.STANDARD_YEAR
        <where>
            SUBSTR(a.dscg_time,1,7) = #{ym,jdbcType=VARCHAR}
            <include
                    refid="com.my.som.mapper.newBusiness.common.NewBusinessCommonAnalysisMapper.queryBusCommon"></include>
            <if test="bah != null and bah !=''">
                and instr(a.PATIENT_ID,#{bah,jdbcType=VARCHAR}) > 0
            </if>
            <if test="categories != null and categories != ''">
                and b.A54 = #{categories}
            </if>
            <include refid="Condition_query"></include>
        </where>
    </select>

    <select id="getListPoint" resultType="com.my.som.vo.cdBusiness.CdPayToPredictVo">
        select a.*,
               b.*,
               case when a.insuType = 1 then a.point * b.czPrice
                    when a.insuType = 2 then a.point * b.cxPrice
                    when a.insuType = 3 then a.point * b.cxPrice
               end as predictCost
        from
             (
                 select ifnull(b.A54,0) as insuType,
                        count(1) as totalCount,
                        sum(ifnull(a.ipt_sumfee,0)) as sumfee,
                        sum(ifnull(c.cd_grp_wt,0) * 100) as point
                 from som_cd_grp_info a
                 left join som_hi_invy_bas_info b
                 on a.SETTLE_LIST_ID = b.ID
                 left join som_cd_standard_info c
                 on a.cd_codg = c.cd_codg
                 and substr(a.dscg_time,1,4) = c.STANDARD_YEAR
                 where substr(a.dscg_time,1,7) = #{ym,jdbcType=VARCHAR}
        <include refid="Condition_query"></include>
                 <include
                         refid="com.my.som.mapper.newBusiness.common.NewBusinessCommonAnalysisMapper.queryBusCommon"></include>
                 group by b.A54
             ) a
        cross join
             (SELECT MAX(CASE WHEN `key` = 'CX_PRICE' THEN `VALUE` ELSE NULL END) AS cxPrice,
                                   MAX(case when `key` = 'CZ_PRICE' THEN `VALUE` ELSE NULL END) AS czPrice,
                                   MAX(case when `key` = 'PRICE' THEN `VALUE` ELSE NULL END) AS price
                 FROM som_dip_gen_cfg
                 WHERE type = 'PREDICTED_PRICE'
                 GROUP BY type
             ) b
    </select>

    <select id="getMonthData" resultType="com.my.som.vo.cdBusiness.CdPayToPredictVo">
        select c.month,
               round((sum(c.payCost)/10000),2) as sumfee,
               round((sum(c.ycCost)/10000),2) as predictCost,
               (round(((sum(c.ycCost)/sum(c.payCost))-1)*100,2)) as cyb
        from (
                 select a.*
                 from
                     (
                         select b.A54 as insuType,
                                substring(a.dscg_time,1,7) as month,
                               sum(a.ipt_sumfee) as payCost,
                             ifnull(sum(d.forecast_fee),0) as ycCost
                         from som_cd_grp_info a
                             left join som_hi_invy_bas_info b
                         on a.SETTLE_LIST_ID = b.ID
                             left join som_cd_standard_info c
                             on a.cd_codg = c.cd_codg
                             AND SUBSTR(a.dscg_time,1,4) = c.STANDARD_YEAR
                        left join som_dip_sco d
                        on a.SETTLE_LIST_ID = d.SETTLE_LIST_ID
                         <where>
                             <include refid="Condition_query"></include>
                         <include
                                 refid="com.my.som.mapper.newBusiness.common.NewBusinessCommonAnalysisMapper.queryBusCommon"></include>
                         </where>
                         group by b.A54,
                             SUBSTRING(a.dscg_time,1,7)
                     ) a

             ) c
        <![CDATA[
        where c.month >= #{begnDate,jdbcType=VARCHAR}
          and c.month <= #{expiDate,jdbcType=VARCHAR}
          and c.insuType is not null
          and c.insuType != ''
        ]]>
        group by c.month
        order by c.month asc
    </select>
    <sql id="Condition_query">
        <if test="cdGroup != null and cdGroup != ''">
            and (
            instr(a.cd_codg, #{cdGroup,jdbcType=VARCHAR}) > 0
            or
            instr(a.CD_NAME, #{cdGroup,jdbcType=VARCHAR}) > 0
            )
        </if>

        <if test="recordType != null and recordType != ''">
            and a.medcas_type = #{recordType}
        </if>
        <if test="dept != null and dept !=''">
            and a.dscg_caty_codg_inhosp = #{dept}
        </if>
    </sql>
</mapper>
