<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.som.mapper.cdBusiness.CdKnowledgeBaseMapper">
<!--    CD分组情况-->
    <select id="list" resultType="java.util.HashMap">
        SELECT a.*,
        ROUND(IFNULL(a.cityAvgCost,0)-IFNULL(a.hosAvgCost,0),2) AS costDifference,
        ROUND(IFNULL(a.cityAvgDays,0)-IFNULL(a.hosAvgDays,0),2) AS dayDifference
        from(
        select
        b.cd_codg AS cdCodg,
        b.CD_NAME AS cdName,
        COUNT(1) AS medcasVal,
        COUNT(DISTINCT(a.dscg_caty_codg_inhosp)) AS deptNum,
        IFNULL(ROUND(c.cd_grp_wt, 2),0) AS areaWeight,
        IFNULL(ROUND(AVG(a.ipt_sumfee)/NULLIF(MAX(convert(AES_DECRYPT(UNHEX(f.AREA_AVG_COST),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) using utf8)),0), 4),0) AS hosWeight,
        IFNULL(MAX(convert(AES_DECRYPT(UNHEX(c.standard_avg_fee),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY})
        using utf8)),0) as cityAvgCost,
        IFNULL(ROUND(AVG(a.ipt_sumfee), 2),0) AS hosAvgCost,
        IFNULL(MAX(convert(AES_DECRYPT(UNHEX(c.standard_ave_hosp_day),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY})
        using utf8)),0) as cityAvgDays,
        IFNULL(ROUND(AVG(a.act_ipt), 2),0) AS hosAvgDays,
--         (case when
--         MAX(convert(AES_DECRYPT(UNHEX(c.dip_setl_fee),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) using
--         utf8)) is null then '暂无该病种数据' else
--         (CONCAT(ROUND(IFNULL(MAX(convert(AES_DECRYPT(UNHEX(c.dip_setl_fee),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY})
--         using utf8)),0)*e.control_fee_stsb_min_prop,1),'-',ROUND(IFNULL(MAX(convert(AES_DECRYPT(UNHEX(c.dip_setl_fee),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY})
--         using utf8)),0)*e.control_fee_stsb_max_prop,1)))
--         end) as costControlRange,

        IFNULL(ROUND(SUM(a.ipt_sumfee), 2),0) AS inHosTotalCost,
        IFNULL(ROUND(SUM(a.com_med_servfee),2),0) AS comMedServfee,
        IFNULL(CONCAT(ROUND(SUM(a.com_med_servfee)/NULLIF(SUM(a.ipt_sumfee),0)*100,2),'%'),0) AS serviceCostRate,
        IFNULL(ROUND(SUM(a.rhab_fee),2),0) AS rhabFee,
        IFNULL(CONCAT(ROUND(SUM(a.rhab_fee)/NULLIF(SUM(a.ipt_sumfee),0)*100,2),'%'),0) AS recoverCostRate,
        IFNULL(ROUND(SUM(a.diag_fee),2),0) AS diagFee,
        IFNULL(CONCAT(ROUND(SUM(a.diag_fee)/NULLIF(SUM(a.ipt_sumfee),0)*100,2),'%'),0) AS diagnoseCostRate,
        IFNULL(ROUND(SUM(a.treat_fee),2),0) AS treatFee,
        IFNULL(CONCAT(ROUND(SUM(a.treat_fee)/NULLIF(SUM(a.ipt_sumfee),0)*100,2),'%'),0) AS treatmentCostRate,
        IFNULL(ROUND(SUM(a.drugfee),2),0) AS drugfee,
        IFNULL(CONCAT(ROUND(SUM(a.drugfee)/NULLIF(SUM(a.ipt_sumfee),0)*100,2),'%'),0) AS medicalCostRate,
        IFNULL(ROUND(SUM(a.blood_blo_pro),2),0) AS bloodBloPro,
        IFNULL(CONCAT(ROUND(SUM(a.blood_blo_pro)/NULLIF(SUM(a.ipt_sumfee),0)*100,2),'%'),0) AS bloodCostRate,
        IFNULL(ROUND(SUM(a.mcs_fee),2),0) AS mcsFee,
        IFNULL(CONCAT(ROUND(SUM(a.mcs_fee)/NULLIF(SUM(a.ipt_sumfee),0)*100,2),'%'),0) AS materialCostRate,
        IFNULL(ROUND(SUM(a.tcm_oth),2),0) AS tcmOth,
        IFNULL(CONCAT(ROUND(SUM(a.tcm_oth)/NULLIF(SUM(a.ipt_sumfee),0)*100,2),'%'),0) AS chineseOtherRate,
        IFNULL(ROUND(SUM(a.oth_fee),2),0) AS othFee,
        IFNULL(CONCAT(ROUND(SUM(a.oth_fee)/NULLIF(SUM(a.ipt_sumfee),0)*100,2),'%'),0) AS otherCostRate,
        IFNULL(ROUND(SUM(a.abt_fee),2),0) AS abtFee,
        IFNULL(CONCAT(ROUND(SUM(a.abt_fee)/NULLIF(SUM(a.ipt_sumfee),0)*100,2),'%'),0) AS antibioticCostRate,
        IFNULL(ROUND(SUM(a.inspect_fee),2),0) AS inspectFee,
        IFNULL(CONCAT(ROUND(SUM(a.inspect_fee)/NULLIF(SUM(a.ipt_sumfee),0)*100,2),'%'),0) AS inspectionCostRate
        from som_cd_grp_info a
        INNER JOIN som_grp_rcd b on a.SETTLE_LIST_ID = b.SETTLE_LIST_ID
        LEFT JOIN som_cd_standard_info c on b.cd_codg = c.cd_codg AND SUBSTR(a.dscg_time,1,4) = c.STANDARD_YEAR AND c.ACTIVE_FLAG = '1'
        LEFT JOIN som_regn_year_sum_data f on SUBSTR(a.dscg_time,1,4) = f.STANDARD_YEAR
        cross join som_cd_control_fee_stsb_cfg e
        where b.grp_stas = '1'
        <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
            AND a.HOSPITAL_ID = #{queryParam.hospitalId}
        </if>
        <if test="queryParam.year!=null and queryParam.year!=''">
            AND  SUBSTR(a.dscg_time,1,4) = #{queryParam.year}
        </if>
        <if test="queryParam.b16c!=null and queryParam.b16c!=''">
            AND ( a.dscg_caty_codg_inhosp = #{queryParam.b16c} or
            a.dscg_caty_name_inhosp = (select NAME from som_dept where TYPE='2' and CODE=#{queryParam.b16c} AND HOSPITAL_ID = #{queryParam.hospitalId} AND ACTIVE_FLAG='1')
            )
        </if>
        <if test="queryParam.queryCdGroup!=null and queryParam.queryCdGroup!=''">
            AND b.cd_codg = #{queryParam.queryCdGroup}
        </if>
        <if test="queryParam.doctorIdList!=null">
            AND (
            a.deptdrt_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.chfdr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.atddr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.ipdr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.resp_nurs_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.train_dr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.intn_dr in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.codr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.qltctrl_dr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.qltctrl_nurs_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            )
        </if>
        GROUP BY b.cd_codg,b.CD_NAME,
        c.cd_grp_wt,
        e.control_fee_stsb_min_prop,
        e.control_fee_stsb_max_prop
        ORDER BY hosWeight desc,cdCodg asc
        ) a
    </select>
<!--    查询全院CD分组覆盖占比-->
    <select id="getCountByCoverRate" resultType="com.my.som.vo.cdBusiness.CdCoverRateVo">
        select
        count(distinct(case when b.grp_stas = '1' then b.cd_codg else null end)) as cdCoverNum,
        max(d.totalGroupNum) - count(DISTINCT ( CASE WHEN b.grp_stas = '1' THEN b.cd_codg ELSE NULL END )) AS cdNotCoverNum
        from som_cd_grp_info a
        INNER JOIN som_grp_rcd b on a.SETTLE_LIST_ID = b.SETTLE_LIST_ID
        LEFT JOIN som_regn_year_sum_data c on SUBSTR(a.dscg_time,1,4) = c.STANDARD_YEAR
        cross join (
        select count(1) as totalGroupNum from som_cd_standard_info where STANDARD_YEAR  = #{queryParam.year}
        ) d
        where 1=1
        <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
            AND  a.HOSPITAL_ID = #{queryParam.hospitalId}
        </if>
        <if test="queryParam.year!=null and queryParam.year!=''">
            AND  SUBSTR(a.dscg_time,1,4) = #{queryParam.year}
        </if>
        <if test="queryParam.b16c!=null and queryParam.b16c!=''">
            AND ( a.dscg_caty_codg_inhosp = #{queryParam.b16c} or
            a.dscg_caty_name_inhosp = (select NAME from som_dept where TYPE='2' and CODE=#{queryParam.b16c} AND HOSPITAL_ID = #{queryParam.hospitalId} AND ACTIVE_FLAG='1')
            )
        </if>
        <if test="queryParam.doctorIdList!=null">
            AND (
            a.deptdrt_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.chfdr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.atddr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.ipdr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.resp_nurs_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.train_dr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.intn_dr in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.codr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.qltctrl_dr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.qltctrl_nurs_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            )
        </if>
    </select>
<!--查询本期同期各月CD组数-->
    <select id="getCountByMonth" resultType="com.my.som.vo.cdBusiness.CdNumByMonthVo">
        select
            x.mon_name as month,
            IFNULL(y.dipNum,0) as thisYearCdNum
        from som_mon_cfg x
        left join (
        select
        SUBSTR(a.dscg_time,1,7) as time,
        count(distinct(case when b.grp_stas = '1' then b.cd_codg else null end)) as dipNum
        from som_cd_grp_info a
        inner JOIN som_grp_rcd b on a.SETTLE_LIST_ID = b.SETTLE_LIST_ID
        where 1=1
        <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
            AND  a.HOSPITAL_ID = #{queryParam.hospitalId}
        </if>
        <if test="queryParam.year!=null and queryParam.year!=''">
            AND  SUBSTR(a.dscg_time,1,4) = #{queryParam.year}
        </if>
        <if test="queryParam.b16c!=null and queryParam.b16c!=''">
            AND ( a.dscg_caty_codg_inhosp = #{queryParam.b16c} or
            a.dscg_caty_name_inhosp = (select NAME from som_dept where TYPE='2' and CODE=#{queryParam.b16c} AND HOSPITAL_ID = #{queryParam.hospitalId} AND ACTIVE_FLAG='1')
            )
        </if>
        <if test="queryParam.doctorIdList!=null">
            AND (
            a.deptdrt_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.chfdr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.atddr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.ipdr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.resp_nurs_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.train_dr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.intn_dr in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.codr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.qltctrl_dr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            a.qltctrl_nurs_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            )
        </if>
        GROUP BY SUBSTR(a.dscg_time,1,7)
        ) y on x.mon_digl = SUBSTR(y.time,6,2)
        order by x.mon_digl
    </select>
</mapper>
