<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.my.som.mapper.cdBusiness.CdDeptCostEarlyWarningMapper">

    <!-- 获取数据 -->
<!--    <select id="getList" resultType="com.my.som.vo.cdBusiness.CdDeptCostEarlyWarningVo">-->
<!--        select g.*-->
<!--        from (-->
<!--            select d.*,-->
<!--                   e.name AS deptName,-->
<!--                   f.medicalCount,-->
<!--                   <![CDATA[-->
<!--                        case when balanceRate <= -10 then 1-->
<!--                            when balanceRate > -10 and balanceRate <= -1 then 2-->
<!--                            when balanceRate > -1 and balanceRate <= 1 then 3-->
<!--                            when balanceRate > 1 then 4-->
<!--                        else 3 end as rate-->
<!--                   ]]> &lt;!&ndash; 盈亏比区间 默认3可能情况为 -0.0 &ndash;&gt;-->
<!--            from (-->
<!--                     select c.dscg_caty_codg_inhosp AS deptCode,-->
<!--                            count(1) AS groupCount,-->
<!--                            SUM(pelCount) as allCount,-->
<!--                            IFNULL(ROUND(SUM(areaStandardCost) - SUM(sumfee),2),0) AS balanceCost,-->
<!--                            IFNULL(ROUND((SUM(areaStandardCost) - SUM(sumfee)) / SUM(sumfee) * 100,2),0) AS balanceRate-->
<!--                     from (-->
<!--                              select a.cd_codg,-->
<!--                                     a.dscg_caty_codg_inhosp,-->
<!--                                     count(1) as pelCount,-->
<!--                                     SUM(a.ipt_sumfee) AS sumfee,-->
<!--                                     SUM(convert(AES_DECRYPT(UNHEX(b.standard_avg_fee),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) using utf8)) AS areaStandardCost-->
<!--                              from som_cd_grp_info a-->
<!--                              left join som_cd_standard_info b-->
<!--                              on a.cd_codg = b.cd_codg-->
<!--                              AND SUBSTR(a.dscg_time,1,4) = b.STANDARD_YEAR-->
<!--                              LEFT JOIN som_grp_rcd c-->
<!--                              ON a.settle_list_id = c.settle_list_id-->
<!--                              where c.grp_stas = '1'-->
<!--                              <if test="begnDate != null and begnDate != '' and expiDate != null and expiDate != ''">-->
<!--                                  <![CDATA[-->
<!--                                    AND SUBSTR(a.dscg_time,1,7) >= #{begnDate,jdbcType=VARCHAR}-->
<!--                                    AND SUBSTR(a.dscg_time,1,7) <= #{expiDate,jdbcType=VARCHAR}-->
<!--                                  ]]>-->
<!--                              </if>-->
<!--                              <if test="deptCode != null and deptCode != ''">-->
<!--                                AND a.dscg_caty_codg_inhosp = #{deptCode,jdbcType=VARCHAR}-->
<!--                              </if>-->
<!--                              group by a.cd_codg,a.dscg_caty_codg_inhosp-->
<!--                     ) c group by c.dscg_caty_codg_inhosp-->
<!--            ) d-->
<!--            inner join som_dept e-->
<!--            on d.deptCode = e.code-->
<!--            LEFT JOIN (-->
<!--                select B16C as dept_code,count(distinct trim(B25C)) as medicalCount-->
<!--                from som_hi_invy_bas_info-->
<!--                group by B16C-->
<!--            ) f-->
<!--            on d.deptCode = f.dept_code-->
<!--            order by balanceRate-->
<!--        ) g-->
<!--        <where>-->
<!--            <if test="rate != null and rate != '' and rate != 0">-->
<!--                g.rate = #{rate,jdbcType=VARCHAR}-->
<!--            </if>-->
<!--        </where>-->
<!--    </select>-->

    <select id="getList" resultType="com.my.som.vo.cdBusiness.CdDeptCostEarlyWarningVo">
        SELECT e.*
        FROM
        (
            SELECT
            c.*,
            CASE WHEN b.`NAME` IS NULL THEN '未填写科室' ELSE b.`NAME` END AS deptName,
             <![CDATA[
            CASE WHEN c.balanceRate <= - 10 THEN 1
                 WHEN c.balanceRate <= - 1 THEN 2
                 WHEN c.balanceRate > - 1
            AND c.balanceRate <= 1 THEN 3
                WHEN c.balanceRate > 1 THEN 4 ELSE 3 END AS rate
             ]]>
            FROM(
                SELECT
                    COUNT( 1 ) AS groupCount,
                    A.deptCode AS deptCode,
                    SUM( A.inGroupMedicalNum ) AS allCount,
                    SUM( A.upNum ) AS upNum,
                    SUM( A.lowNum ) AS lowNum,
                    ROUND( SUM( A.sumfee ), 2 ) AS sumfee,
                    ROUND( SUM( A.forecastAmount ), 2 ) AS forecastAmount,
                    ROUND( SUM(A.balanceCost), 2 ) AS balanceCost,
                    ROUND((( SUM( A.forecastAmount )- SUM( A.sumfee ))/ SUM( A.sumfee ) * 100 ), 2 ) AS balanceRate
                FROM
                (
                    SELECT
                    a.dscg_caty_codg_inhosp AS deptCode,
                    COUNT( 1 ) AS medcasVal,
                    a.cd_codg,
                    COUNT( CASE WHEN b.grp_stas = '1' THEN 1 ELSE NULL END ) AS inGroupMedicalNum,
                    COUNT( CASE WHEN a.ratioRange = 1 THEN 1 ELSE NULL END ) AS upNum,
                    COUNT( CASE WHEN a.ratioRange = 2 THEN 1 ELSE NULL END ) AS lowNum,
                    ROUND( SUM( a.ipt_sumfee ), 2 ) AS sumfee,
                    ROUND( SUM( a.standardFee ), 2 ) AS forecastAmount,
                    ROUND( IFNULL( SUM( a.standardFee ), 0 ) - IFNULL( SUM( a.ipt_sumfee ), 0 ), 2 ) AS balanceCost
                    FROM
                    (
                        SELECT
                        a.dscg_caty_codg_inhosp,
                        a.SETTLE_LIST_ID,
                        a.dscg_time,
                        a.ipt_sumfee,
                        a.cd_codg,
                        CASE WHEN ifnull( a.ipt_sumfee, 0 ) > b.max THEN 1
                         <![CDATA[
                        WHEN ifnull( a.ipt_sumfee, 0 ) < b.min THEN 2 WHEN ifnull( a.ipt_sumfee, 0 ) >= b.min
                        AND ifnull( a.ipt_sumfee, 0 ) <= b.max THEN 3 ELSE 4 END AS ratioRange,
                               ]]>
                        b.standardFee
                        FROM
                        som_cd_grp_info a
                        LEFT JOIN
                        (
                            SELECT
                            a.cd_codg,
                            a.standardAvgFee AS standardFee,
                            CASE WHEN a.highFee IS NOT NULL THEN a.highFee ELSE a.standardRangeUp END AS max,
                            CASE WHEN a.minFee IS NOT NULL THEN a.minFee ELSE a.standardRangeDown END AS min
                            FROM
                            (
                                SELECT
                                a.highFee,
                                a.minFee,
                                a.cd_codg,
                                a.standardAvgFee,
                                a.standardAvgFee * mutiple_up AS standardRangeUp,
                                a.standardAvgFee * mutiple_down AS standardRangeDown
                                FROM
                                (
                                SELECT
                                a.cd_codg,
                                IFNULL( ROUND( CONVERT ( AES_DECRYPT( UNHEX( a.standard_avg_fee ),${@com.my.som.common.constant.DrgConst@AES_ENCRYPT_KEY}) USING utf8 ), 2 ), 0 ) AS standardAvgFee,
                                a.STANDARD_YEAR AS standardYear,
                                a.high_fee AS highFee,
                                a.min_fee AS minFee,
                                c.mutiple_up,
                                c.mutiple_down
                                FROM
                                som_cd_standard_info a
                                CROSS JOIN (
                                    SELECT
                                        MAX( CASE WHEN `key` = 'RANGE_UP' THEN `VALUE` ELSE NULL END ) AS mutiple_up,
                                        MAX( CASE WHEN `key` = 'RANGE_DOWN' THEN `VALUE` ELSE NULL END ) AS mutiple_down
                                    FROM
                                        som_sys_gen_cfg
                                        WHERE
                                        TYPE = 'CD_MULTIPLE_RANGE'
                                        GROUP BY
                                        TYPE
                                                ) c
                                    WHERE
                                    STANDARD_YEAR = #{year,jdbcType=VARCHAR}
                                ) a
                            ) a
                        ) b ON a.cd_codg = b.cd_codg
                        WHERE 1=1
                        <if test="begnDate != null and begnDate != '' and expiDate != null and expiDate != ''">
                            <![CDATA[
                                AND SUBSTR(a.dscg_time,1,7) = #{ym}
                            ]]>
                        </if>
                    ) a
                    LEFT JOIN som_grp_rcd b ON a.SETTLE_LIST_ID = b.SETTLE_LIST_ID
                    WHERE a.cd_codg !='' AND a.cd_codg IS NOT NULL
                    <include
                            refid="com.my.som.mapper.newBusiness.common.NewBusinessCommonAnalysisMapper.queryBusCommon"></include>
                    GROUP BY
                    a.dscg_caty_codg_inhosp,
                    a.cd_codg
                )A
                GROUP BY A.deptCode
            )c
            LEFT JOIN som_dept b ON c.deptCode = b.`CODE`
            AND b.ACTIVE_FLAG = '1'
            ORDER BY
            balanceRate
        )e
        <where>
            <if test="rate != null and rate != '' and rate != 0">
                e.rate = #{rate,jdbcType=VARCHAR}
            </if>
        </where>
    </select>
</mapper>
