<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.som.mapper.cdBusiness.CdAnalysisMapper">
<!--    查询医院病组分析主要信息-->
    <select id="list" resultType="com.my.som.vo.cdBusiness.CdAnalysisVo">
        select
            cd_codg AS cdCodg,
            CD_NAME AS cdName,
            CONCAT(STANDARD_YEAR,'年') AS standardYear,<!--标杆年份-->
            COUNT(1) AS inGroupMedicalNum, <!--入组病案数-->
            COUNT(DISTINCT(dscg_caty_codg_inhosp)) AS deptNum,<!--科室数-->
            ROUND(IFNULL(cd_wt,0), 2) AS cdWt,  <!--权重-->
            ROUND(IFNULL(SUM(cd_wt),0),2) AS totalCdWeight, <!--总权重-->
            ROUND(IFNULL(MAX(standard_avg_fee),0), 2) AS avgCost, <!--平均住院费用-->
            ROUND(IFNULL(MAX(standard_ave_hosp_day),0), 2) AS avgDays, <!--平均住院日-->
            ROUND(IFNULL(sum(IFNULL(act_ipt,0)/NULLIF(standard_ave_hosp_day, 0))/COUNT(1),0),2) as timeIndex, <!--  时间消耗指数   -->
            ROUND(IFNULL(sum(IFNULL(ipt_sumfee,0)/NULLIF(standard_avg_fee, 0))/COUNT(1),0),2) as costIndex  <!--  费用消耗指数   -->
            from som_cd_grp_info
        where grp_stas = '1'
        <if test="queryParam.deptCode != null and queryParam.deptCode != ''">
            and dscg_caty_codg_inhosp = #{queryParam.deptCode}
        </if>
        <if test="queryParam.b16c!=null and queryParam.b16c!=''">
            AND cd_codg in (
            select
            distinct cd_codg
            from som_drg_grp_info
            where grp_stas = '1' AND dscg_caty_codg_inhosp = #{queryParam.b16c}
            <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
                AND  HOSPITAL_ID = #{queryParam.hospitalId}
            </if>
            <if test="queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and
                        queryParam.cy_end_date!=null and queryParam.cy_end_date!=''">
                AND dscg_time BETWEEN #{queryParam.cy_start_date} and CONCAT(#{queryParam.cy_end_date},' 23:59:59')
            </if>
            )
        </if>
        <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
            AND  HOSPITAL_ID = #{queryParam.hospitalId}
        </if>
        <if test="queryParam.queryCd!=null and queryParam.queryCd!=''">
            AND `CD_CODE` = #{queryParam.queryCd}
        </if>
        <if test="queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and
        queryParam.cy_end_date!=null and queryParam.cy_end_date!=''">
            AND dscg_time BETWEEN #{queryParam.cy_start_date} and CONCAT(#{queryParam.cy_end_date},' 23:59:59')
        </if>
        <if test="queryParam.doctorIdList!=null">
            AND (
            deptdrt_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            chfdr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            atddr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            ipdr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            resp_nurs_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            train_dr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            intn_dr in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            codr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            qltctrl_dr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            qltctrl_nurs_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            )
        </if>
        GROUP BY cd_codg,CD_NAME,STANDARD_YEAR,ROUND(IFNULL(cd_wt,0), 2)
        ORDER BY totalCdWeight desc
    </select>
<!--查询医院病组分析统计信息-->
    <select id="getCountInfo" resultType="java.util.Map">
        select
        COUNT(CASE WHEN right(cd_codg,1) = '1' THEN 1 ELSE NULL END) AS seriousComplicationMedicalNum,<!--伴严重合并症及伴随病的CD组病案数-->
        IFNULL(COUNT(DISTINCT(CASE WHEN right(cd_codg,1) = '1' AND grp_stas = '1' THEN cd_codg ELSE NULL END)),0) AS seriousComplicationCdNum,<!--伴严重合并症及伴随病的CD组CD组数-->
        IFNULL(CONCAT(ROUND((IFNULL(COUNT(DISTINCT(CASE WHEN right(cd_codg,1) = '1' THEN cd_codg ELSE NULL END)),0)/NULLIF(COUNT(DISTINCT(cd_codg)),0))*100,2),'%'),0)
        AS seriousComplicationCdRate,<!--伴严重合并症及伴随病的CD组CD组数占比-->
        IFNULL(ROUND(SUM(CASE WHEN right(cd_codg,1) = '1' THEN cd_wt ELSE 0 END),2),0) AS seriousComplicationTotalCdWeight, <!--伴严重合并症及伴随病的CD组总权重-->
        IFNULL(ROUND(SUM(CASE WHEN right(cd_codg,1) = '1' THEN cd_wt ELSE 0 END) /
        NULLIF(COUNT(CASE WHEN right(cd_codg,1) = '1' THEN 1 ELSE NULL END),0), 2),0) AS seriousComplicationCmi,  <!--伴严重合并症及伴随病的CD组CMI指数-->

        COUNT(CASE WHEN right(cd_codg,1) = '3' THEN 1 ELSE NULL END) AS normalComplicationMedicalNum,<!--伴一般合并症及伴随病的CD组病案数-->
        IFNULL(COUNT(DISTINCT(CASE WHEN right(cd_codg,1) = '3' AND grp_stas = '1' THEN cd_codg ELSE NULL END)),0) AS normalComplicationCdNum,<!--伴一般合并症及伴随病的CD组CD组数-->
        IFNULL(CONCAT(ROUND((IFNULL(COUNT(DISTINCT(CASE WHEN right(cd_codg,1) = '3' THEN cd_codg ELSE NULL END)),0)/NULLIF(COUNT(DISTINCT(cd_codg)),0))*100,2),'%'),0)
        AS normalComplicationCdRate,<!--伴一般合并症及伴随病的CD组CD组数占比-->
        IFNULL(ROUND(SUM(CASE WHEN right(cd_codg,1) = '3' THEN cd_wt ELSE 0 END),2),0) AS normalComplicationTotalCdWeight, <!--伴一般合并症及伴随病的CD组总权重-->
        IFNULL(ROUND(SUM(CASE WHEN right(cd_codg,1) = '3' THEN cd_wt ELSE 0 END) /
        NULLIF(COUNT(CASE WHEN right(cd_codg,1) = '3' THEN 1 ELSE NULL END),0), 2),0) AS normalComplicationCmi,  <!--伴一般合并症及伴随病的CD组CMI指数-->

        COUNT(CASE WHEN right(cd_codg,1) = '5' THEN 1 ELSE NULL END) AS noComplicationMedicalNum,<!--不伴合并症及伴随病的CD组病案数-->
        IFNULL(COUNT(DISTINCT(CASE WHEN right(cd_codg,1) = '5' AND grp_stas = '1' THEN cd_codg ELSE NULL END)),0) AS noComplicationCdNum,<!--不伴合并症及伴随病的CD组CD组数-->
        IFNULL(CONCAT(ROUND((IFNULL(COUNT(DISTINCT(CASE WHEN right(cd_codg,1) = '5' THEN cd_codg ELSE NULL END)),0)/NULLIF(COUNT(DISTINCT(cd_codg)),0))*100,2),'%'),0)
        AS noComplicationCdRate,<!--不伴合并症及伴随病的CD组CD组数占比-->
        IFNULL(ROUND(SUM(CASE WHEN right(cd_codg,1) = '5' THEN cd_wt ELSE 0 END),2),0) AS noComplicationTotalCdWeight, <!--不伴合并症及伴随病的CD组总权重-->
        IFNULL(ROUND(SUM(CASE WHEN right(cd_codg,1) = '5' THEN cd_wt ELSE 0 END) /
        NULLIF(COUNT(CASE WHEN right(cd_codg,1) = '5' THEN 1 ELSE NULL END),0), 2),0) AS noComplicationCmi,  <!--不伴合并症及伴随病的CD组CMI指数-->

        COUNT(CASE WHEN SUBSTR(cd_codg,2,1) in ('R','S','T','U','V','W','X','Y','Z')  THEN 1 ELSE NULL END) AS medicalDeptMedicalNum,<!--内科组的CD病案数-->
        IFNULL(COUNT(DISTINCT(CASE WHEN SUBSTR(cd_codg,2,1) in ('R','S','T','U','V','W','X','Y','Z') AND grp_stas = '1' THEN cd_codg ELSE NULL END)),0) AS medicalDeptCdNum,<!--内科组的CD组CD组数-->
        IFNULL(CONCAT(ROUND((IFNULL(COUNT(DISTINCT(CASE WHEN SUBSTR(cd_codg,2,1) in ('R','S','T','U','V','W','X','Y','Z') THEN cd_codg ELSE NULL END)),0)/NULLIF(COUNT(DISTINCT(cd_codg)),0))*100,2),'%'),0)
        AS medicalDeptCdRate,<!--内科组CD组数占比-->
        IFNULL(ROUND(SUM(CASE WHEN SUBSTR(cd_codg,2,1) in ('R','S','T','U','V','W','X','Y','Z') THEN cd_wt ELSE 0 END),2),0) AS medicalDeptTotalCdWeight, <!--内科组的CD组总权重-->
        IFNULL(ROUND(SUM(CASE WHEN SUBSTR(cd_codg,2,1) in ('R','S','T','U','V','W','X','Y','Z') THEN cd_wt ELSE 0 END) /
        NULLIF(COUNT(CASE WHEN SUBSTR(cd_codg,2,1) in ('R','S','T','U','V','W','X','Y','Z') THEN 1 ELSE NULL END),0), 2),0) AS medicalDeptCmi,  <!--内科组的CD组CMI指数-->

        COUNT(CASE WHEN SUBSTR(cd_codg,2,1) in ('K','L','M','N','P','Q')  THEN 1 ELSE NULL END) AS notOperationMedicalNum,<!--非手术室操作组的CD病案数-->
        IFNULL(COUNT(DISTINCT(CASE WHEN SUBSTR(cd_codg,2,1) in ('K','L','M','N','P','Q') AND grp_stas = '1' THEN cd_codg ELSE NULL END)),0) AS notOperationCdNum,<!--非手术室操作组的CD组CD组数-->
        IFNULL(CONCAT(ROUND((IFNULL(COUNT(DISTINCT(CASE WHEN SUBSTR(cd_codg,2,1) in ('K','L','M','N','P','Q') THEN cd_codg ELSE NULL END)),0)/NULLIF(COUNT(DISTINCT(cd_codg)),0))*100,2),'%'),0)
        AS notOperationCdRate,<!--非手术室操作组CD组数占比-->
        IFNULL(ROUND(SUM(CASE WHEN SUBSTR(cd_codg,2,1) in ('K','L','M','N','P','Q') THEN cd_wt ELSE 0 END),2),0) AS notOperationTotalCdWeight, <!--非手术室操作组的CD组总权重-->
        IFNULL(ROUND(SUM(CASE WHEN SUBSTR(cd_codg,2,1) in ('K','L','M','N','P','Q') THEN cd_wt ELSE 0 END) /
        NULLIF(COUNT(CASE WHEN SUBSTR(cd_codg,2,1) in ('K','L','M','N','P','Q') THEN 1 ELSE NULL END),0), 2),0) AS notOperationCmi,  <!--非手术室操作组的CD组CMI指数-->

        COUNT(CASE WHEN SUBSTR(cd_codg,2,1) in ('A','B','C','D','E','F','G','H','J')  THEN 1 ELSE NULL END) AS surgeryDeptMedicalNum,<!--外科组的CD病案数-->
        IFNULL(COUNT(DISTINCT(CASE WHEN SUBSTR(cd_codg,2,1) in ('A','B','C','D','E','F','G','H','J') AND grp_stas = '1' THEN cd_codg ELSE NULL END)),0) AS surgeryDeptCdNum,<!--外科组的CD组CD组数-->
        IFNULL(CONCAT(ROUND((IFNULL(COUNT(DISTINCT(CASE WHEN SUBSTR(cd_codg,2,1) in ('A','B','C','D','E','F','G','H','J') THEN cd_codg ELSE NULL END)),0)/NULLIF(COUNT(DISTINCT(cd_codg)),0))*100,2),'%'),0)
        AS surgeryDeptCdRate,<!--外科组CD组数占比-->
        IFNULL(ROUND(SUM(CASE WHEN SUBSTR(cd_codg,2,1) in ('A','B','C','D','E','F','G','H','J') THEN cd_wt ELSE 0 END),2),0) AS surgeryDeptTotalCdWeight, <!--外科组CD组总权重-->
        IFNULL(ROUND(SUM(CASE WHEN SUBSTR(cd_codg,2,1) in ('A','B','C','D','E','F','G','H','J') THEN cd_wt ELSE 0 END) /
        NULLIF(COUNT(CASE WHEN SUBSTR(cd_codg,2,1) in ('A','B','C','D','E','F','G','H','J') THEN 1 ELSE NULL END),0), 2),0) AS surgeryDeptCmi  <!--外科组的CD组CMI指数-->
        from som_cd_grp_info
        where 1 = 1
        <if test="queryParam.hospitalId!=null and queryParam.hospitalId!=''">
            AND  HOSPITAL_ID = #{queryParam.hospitalId}
        </if>
        <if test="queryParam.b16c!=null and queryParam.b16c!=''">
            AND dscg_caty_codg_inhosp = #{queryParam.b16c}
        </if>
        <if test="queryParam.queryCd!=null and queryParam.queryCd!=''">
            AND `CD_CODE` = #{queryParam.queryCd}
        </if>
        <if test="queryParam.cy_start_date!=null and queryParam.cy_start_date!='' and
        queryParam.cy_end_date!=null and queryParam.cy_end_date!=''">
            AND dscg_time BETWEEN #{queryParam.cy_start_date} and CONCAT(#{queryParam.cy_end_date},' 23:59:59')
        </if>
        <if test="queryParam.doctorIdList!=null">
            AND (
            deptdrt_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            chfdr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            atddr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            ipdr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            resp_nurs_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            train_dr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            intn_dr in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            codr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            qltctrl_dr_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR
            qltctrl_nurs_code in
            <foreach collection="queryParam.doctorIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            )
        </if>
    </select>
</mapper>