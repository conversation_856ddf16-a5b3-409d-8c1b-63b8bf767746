<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.som.mapper.conditionalOptions.ConditionalOptionsMapper">
    <select id="getData" resultType="integer">
        SELECT
            <if test="dto.queryConditions != null and dto.queryConditions.size() > 0">
                <foreach collection="dto.queryConditions" item="item" separator=",">
                    <if test="item == a">
                        COUNT(DISTINCT dip_codg)
                    </if>
                </foreach>
            </if>
        FROM som_dip_grp_info
        WHERE 1 = 1
        <if test="dto.begnDate != null and dto.begnDate != '' and
                  dto.expiDate != null and dto.expiDate != ''">
            AND dscg_time BETWEEN #{dto.begnDate} AND CONCAT(#{dto.expiDate},' 23.59.59')
        </if>
        <if test="dto.inStartTime != null and dto.inStartTime != '' and
                 dto.inEndTime != null and dto.inEndTime != ''">
            AND adm_time BETWEEN #{dto.inStartTime} AND CONCAT(#{dto.inEndTime},' 23.59.59')
        </if>
        <if test="dto.seStartTime != null and dto.seStartTime != '' and
                 dto.seEndTime != null and dto.seEndTime != ''">
            AND setl_end_time BETWEEN #{dto.seStartTime} AND CONCAT(#{dto.seEndTime},' 23.59.59')
        </if>
    </select>
</mapper>