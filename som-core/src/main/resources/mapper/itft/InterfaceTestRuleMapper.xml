<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.som.mapper.itft.InterfaceTestRuleMapper">

    <!-- 新增数据 -->
    <insert id="addRule">
        INSERT INTO som_intf_test_rule_cfg(chk_fld_codg,
                               chk_field_name,
                               chk_fld_type,
                               dic_type,
                               regl_chk,
                               blng_intf,
                               active_flag
        )
        VALUES( #{data.valFieldCode,jdbcType=VARCHAR},
                #{data.chkFieldName,jdbcType=VARCHAR},
                #{data.chkFldType,jdbcType=VARCHAR},
                #{data.dicType,jdbcType=VARCHAR},
                #{data.reglChk,jdbcType=VARCHAR},
                #{data.blngIntf,jdbcType=VARCHAR},
                #{data.activeFlag,jdbcType=VARCHAR}
       )
    </insert>

    <!-- 添加校验日志 -->
    <insert id="addValLog">
        INSERT INTO som_chk_log(trns_sn,
                                err_abn_info,
                                blng_intf,
                                crte_time
        )
        VALUES
        <foreach collection="list" separator="," item="item">
            (
             #{item.trns_sn,jdbcType=VARCHAR},
             #{item.errAbnInfo,jdbcType=VARCHAR},
             #{item.blngIntf,jdbcType=VARCHAR},
             now()
            )
        </foreach>
    </insert>

    <!-- 修改数据 -->
    <update id="updateRule">
        UPDATE som_intf_test_rule_cfg
        SET chk_fld_codg = #{data.valFieldCode,jdbcType=VARCHAR},
            chk_field_name = #{data.chkFieldName,jdbcType=VARCHAR},
            chk_fld_type = #{data.chkFldType,jdbcType=VARCHAR},
            dic_type = #{data.dicType,jdbcType=VARCHAR},
            regl_chk = #{data.reglChk,jdbcType=VARCHAR},
            blng_intf = #{data.blngIntf,jdbcType=VARCHAR},
            active_flag = #{data.activeFlag,jdbcType=VARCHAR},
            required = #{data.required,jdbcType=VARCHAR}
        WHERE id = #{data.id,jdbcType=INTEGER}
    </update>

    <!-- 删除上一个交易流水号校验数据 -->
    <delete id="delValLogByNo">
        DELETE FROM som_chk_log
        WHERE trns_sn = #{trns_sn,jdbcType=VARCHAR}
        AND blng_intf LIKE CONCAT('%', #{itftNo,jdbcType=VARCHAR}, '%')
    </delete>

    <!-- 查询数据 -->
    <select id="queryData" resultType="com.my.som.vo.itft.ITFTRuleVo">
        select id,
               chk_fld_codg as valFieldCode,
               chk_field_name as chkFieldName,
               chk_fld_type as chkFldType,
               dic_type as dicType,
               regl_chk as reglChk,
               blng_intf as blngIntf,
               active_flag as activeFlag,
               required
        from som_intf_test_rule_cfg
        <where>
            <if test="condition != null and condition != ''">
                chk_fld_codg like concat('%', #{condition,jdbcType=VARCHAR}, '%') or
                chk_field_name like concat('%', #{condition,jdbcType=VARCHAR}, '%') or
                chk_fld_type like concat('%', #{condition,jdbcType=VARCHAR}, '%') or
                dic_type like concat('%', #{condition,jdbcType=VARCHAR}, '%') or
                blng_intf like concat('%', #{condition,jdbcType=VARCHAR}, '%')
            </if>
        </where>
    </select>

    <!-- 查询校验日志 -->
    <select id="queryValLog" resultType="com.my.som.entity.itft.ITFTValidateLog">
        SELECT DISTINCT
               trns_sn,
               err_abn_info as errAbnInfo,
               blng_intf as blngIntf,
               crte_time as crteTime
        FROM som_chk_log
        WHERE blng_intf LIKE CONCAT('%', #{ownIft,jdbcType=VARCHAR}, '%')
    </select>

    <insert id="addProLog">
        INSERT  INTO  som_trns_log_rcd(
            trns_sn,log_info,crte_time
        )
        values (
                   #{was002,jdbcType=VARCHAR},
                   #{logInfo,jdbcType=VARCHAR},
                   #{crteTime,jdbcType=VARCHAR}
               )
    </insert>

    <select id="queryLog" resultType="com.my.som.vo.itft.ItftLogVo">
        select trns_sn as was002,
               log_info as logInfo,
               crte_time as crteTime from som_trns_log_rcd ORDER BY crte_time desc;
    </select>

</mapper>
