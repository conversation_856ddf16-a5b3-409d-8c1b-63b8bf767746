<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.som.mapper.itft.ITFTDictMapper">

    <resultMap id="BaseResultMap" type="com.my.som.vo.SomSysCode">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="data_val" jdbcType="VARCHAR" property="dataVal" />
        <result column="labl_name" jdbcType="VARCHAR" property="lablName" />
        <result column="code_type" jdbcType="VARCHAR" property="codeType" />
        <result column="dscr" jdbcType="VARCHAR" property="dscr" />
        <result column="srt" jdbcType="DECIMAL" property="srt" />
        <result column="crter" jdbcType="BIGINT" property="crter" />
        <result column="crte_time" jdbcType="TIMESTAMP" property="crteTime" />
        <result column="updt_psn" jdbcType="BIGINT" property="updtPsn" />
        <result column="updt_time" jdbcType="TIMESTAMP" property="updtTime" />
        <result column="memo_info" jdbcType="VARCHAR" property="memo_info" />
        <result column="is_del" jdbcType="TINYINT" property="isDel" />
    </resultMap>
    <sql id="Base_Column_List">
        a.id,
        a.data_val,
        a.labl_name,
        a.code_type,
        a.dscr,
        a.srt,
        c.username as crter,
        a.crte_time,
        b.username as updt_psn,
        a.updt_time,
        a.memo_info,
        a.is_del
    </sql>
    <insert id="saveDict">
        insert into som_dic_code (data_val, labl_name,
                               code_type, dscr, srt,
                               crter, crte_time, updt_psn,
                               updt_time, memo_info, is_del)
        values (#{dataVal,jdbcType=VARCHAR}, #{lablName,jdbcType=VARCHAR},
                #{codeType,jdbcType=VARCHAR}, #{dscr,jdbcType=VARCHAR}, #{srt,jdbcType=DECIMAL},
                #{crter,jdbcType=BIGINT}, NOW(), #{updtPsn,jdbcType=BIGINT},
                NOW(), #{memo_info,jdbcType=VARCHAR}, 0)
    </insert>
    <update id="updateDict">
        update som_dic_code
        <set>
            updt_time = NOW(),
            <if test="dataVal != null">
                data_val = #{dataVal,jdbcType=VARCHAR},
            </if>
            <if test="lablName != null">
                labl_name = #{lablName,jdbcType=VARCHAR},
            </if>
            <if test="codeType != null">
                code_type = #{codeType,jdbcType=VARCHAR},
            </if>
            <if test="dscr != null">
                dscr = #{dscr,jdbcType=VARCHAR},
            </if>
            <if test="srt != null">
                srt = #{srt,jdbcType=DECIMAL},
            </if>
            <if test="crter != null">
                crter = #{crter,jdbcType=BIGINT},
            </if>
            <if test="crteTime != null">
                crte_time = #{crteTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updtPsn != null">
                updt_psn = #{updtPsn,jdbcType=BIGINT},
            </if>
            <if test="memo_info != null">
                memo_info = #{memo_info,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <delete id="deleteDict">
        delete from som_dic_code
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <select id="selectAll" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from som_dic_code a
        left join som_back_user b on a.updt_psn = b.id
        left join som_back_user c on a.crter = c.id
        <where>
            <if test="queryData != null and queryData != ''">
                a.labl_name like concat('%',#{queryData,jdbcType=VARCHAR},'%') or
                a.code_type like concat('%',#{queryData,jdbcType=VARCHAR},'%') or
                a.dscr like concat('%',#{queryData,jdbcType=VARCHAR},'%')
            </if>
        </where>
    </select>
</mapper>
