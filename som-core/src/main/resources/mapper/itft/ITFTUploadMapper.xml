<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.som.mapper.itft.ITFTUploadMapper">

    <insert id="insertDiBatch"  useGeneratedKeys="true" keyColumn="id" keyProperty="id">
        INSERT INTO som_setl_invy_bas_info(
            unique_id,
            mdtrt_sn,
            mdtrt_id,
            psn_no,
            patn_ipt_cnt,
            ipt_no,
            medcasno,
            psn_name,
            gend,
            brdy,
            age,
            ntly,
            nwb_age,
            nwb_bir_wt,
            nwb_adm_wt,
            birplc,
            napl,
            naty,
            certno,
            prfs,
            mrg_stas,
            curr_addr_poscode,
            curr_addr,
            psn_tel,
            resd_addr_prov,
            resd_addr_city,
            resd_addr_coty,
            resd_addr_subd,
            resd_addr_vil,
            resd_addr_housnum,
            resd_addr_poscode,
            resd_addr,
            empr_tel,
            empr_poscode,
            empr_addr,
            coner_tel,
            coner_name,
            coner_addr,
            coner_rlts_code,
            adm_way_name,
            adm_way_code,
            trt_type_name,
            trt_type,
            adm_caty,
            adm_ward,
            adm_date,
            dscg_date,
            dscg_caty,
            refldept_dept,
            dscg_ward,
            ipt_days,
            drug_dicm_flag,
            dicm_drug_name,
            die_autp_flag,
            abo_code,
            abo_name,
            rh_code,
            rh_name,
            die_flag,
            deptdrt_name,
            deptdrt_code,
            chfdr_name,
            chfdr_code,
            atddr_name,
            atddr_code,
            chfpdr_name,
            chfpdr_code,
            ipt_dr_name,
            ipt_dr_code,
            resp_nurs_name,
            resp_nurs_code,
            train_dr_name,
            train_dr_code,
            intn_dr_name,
            intn_dr_code,
            codr_name,
            codr_code,
            qltctrl_dr_name,
            qltctrl_dr_code,
            qltctrl_nurs_name,
            qltctrl_nurs_code,
            medcas_qlt_name,
            medcas_qlt_code,
            qltctrl_date,
            dscg_way_name,
            dscg_way,
            acp_medins_code,
            acp_medins_name,
            dscg_31days_rinp_flag,
            dscg_31days_rinp_pup,
            damg_intx_ext_rea,
            damg_intx_ext_rea_disecode,
            brn_damg_bfadm_coma_dura,
            brn_damg_afadm_coma_dura,
            vent_used_dura,
            cnfm_date,
            patn_dise_diag_crsp,
            patn_dise_diag_crsp_code,
            ipt_patn_diag_inscp,
            ipt_patn_diag_inscp_code,
            dscg_trt_rslt,
            dscg_trt_rslt_code,
            medins_orgcode,
            aise,
            pote_intn_dr_name,
            hbsag,
            hcvab,
            hivab,
            resc_cnt,
            resc_succ_cnt,
            hosp_dise_fsttime,
            hif_pay_way_name,
            hif_pay_way_code,
            med_fee_paymtd_name,
            medfee_paymtd_code,
            selfpay_amt,
            medfee_sumamt,
            ordn_med_servfee,
            ordn_trt_oprt_fee,
            nurs_fee,
            com_med_serv_oth_fee,
            palg_diag_fee,
            lab_diag_fee,
            rdhy_diag_fee,
            clnc_dise_fee,
            nsrgtrt_item_fee,
            clnc_phys_trt_fee,
            rgtrt_trt_fee,
            anst_fee,
            oprn_fee,
            rhab_fee,
            tcm_trt_fee,
            wmfee,
            abtl_medn_fee,
            tcmpat_fee,
            tcmherb_fee,
            blo_fee,
            albu_fee,
            glon_fee,
            clotfac_fee,
            cyki_fee,
            exam_dspo_matl_fee,
            trt_dspo_matl_fee,
            oprn_dspo_matl_fee,
            oth_fee,
            vali_flag,
            fixmedins_code,
            offsite_med_treat,
            pre_exam,
            patn_rlts,
            nwb_adm_type,
            mul_nwb_bir_wt,
            mul_nwb_adm_wt,
            opsp_diag_caty,
            opsp_mdtrt_date,
            otp_wm_dise,
            wm_dise_code,
            otp_tcm_dise,
            tcm_dise_code,
            spga_nurscare_days,
            lv1_nurscare_days,
            scd_nurscare_days,
            lv3_nurscare_days,
            otp_wm_diag,
            otp_wm_diag_dise_code,
            otp_tcm_diag,
            otp_tcm_diag_dise_code,
            adm_diag,
            bld_cat,
            bld_amt,
            bld_unt,
            extract_flag
        )values (
                    #{unique_id,jdbcType=VARCHAR},
                    #{mdtrt_sn,jdbcType=VARCHAR},
                    #{mdtrt_id,jdbcType=VARCHAR},
                    #{psn_no,jdbcType=VARCHAR},
                    #{patn_ipt_cnt,jdbcType=VARCHAR},
                    #{ipt_no,jdbcType=VARCHAR},
                    #{medcasno,jdbcType=VARCHAR},
                    #{psn_name,jdbcType=VARCHAR},
                    #{gend,jdbcType=VARCHAR},
                    #{brdy,jdbcType=VARCHAR},
                    #{age,jdbcType=VARCHAR},
                    #{ntly,jdbcType=VARCHAR},
                    #{nwb_age,jdbcType=VARCHAR},
                    #{nwb_bir_wt,jdbcType=VARCHAR},
                    #{nwb_adm_wt,jdbcType=VARCHAR},
                    #{birplc,jdbcType=VARCHAR},
                    #{napl,jdbcType=VARCHAR},
                    #{naty,jdbcType=VARCHAR},
                    #{certno,jdbcType=VARCHAR},
                    #{prfs,jdbcType=VARCHAR},
                    #{mrg_stas,jdbcType=VARCHAR},
                    #{curr_addr_poscode,jdbcType=VARCHAR},
                    #{curr_addr,jdbcType=VARCHAR},
                    #{psn_tel,jdbcType=VARCHAR},
                    #{resd_addr_prov,jdbcType=VARCHAR},
                    #{resd_addr_city,jdbcType=VARCHAR},
                    #{resd_addr_coty,jdbcType=VARCHAR},
                    #{resd_addr_subd,jdbcType=VARCHAR},
                    #{resd_addr_vil,jdbcType=VARCHAR},
                    #{resd_addr_housnum,jdbcType=VARCHAR},
                    #{resd_addr_poscode,jdbcType=VARCHAR},
                    #{resd_addr,jdbcType=VARCHAR},
                    #{empr_tel,jdbcType=VARCHAR},
                    #{empr_poscode,jdbcType=VARCHAR},
                    #{empr_addr,jdbcType=VARCHAR},
                    #{coner_tel,jdbcType=VARCHAR},
                    #{coner_name,jdbcType=VARCHAR},
                    #{coner_addr,jdbcType=VARCHAR},
                    #{coner_rlts_code,jdbcType=VARCHAR},
                    #{adm_way_name,jdbcType=VARCHAR},
                    #{adm_way_code,jdbcType=VARCHAR},
                    #{trt_type_name,jdbcType=VARCHAR},
                    #{trt_type,jdbcType=VARCHAR},
                    #{adm_caty,jdbcType=VARCHAR},
                    #{adm_ward,jdbcType=VARCHAR},
                    #{adm_date,jdbcType=VARCHAR},
                    #{dscg_date,jdbcType=VARCHAR},
                    #{dscg_caty,jdbcType=VARCHAR},
                    #{refldept_dept,jdbcType=VARCHAR},
                    #{dscg_ward,jdbcType=VARCHAR},
                    #{ipt_days,jdbcType=VARCHAR},
                    #{drug_dicm_flag,jdbcType=VARCHAR},
                    #{dicm_drug_name,jdbcType=VARCHAR},
                    #{die_autp_flag,jdbcType=VARCHAR},
                    #{abo_code,jdbcType=VARCHAR},
                    #{abo_name,jdbcType=VARCHAR},
                    #{rh_code,jdbcType=VARCHAR},
                    #{rh_name,jdbcType=VARCHAR},
                    #{die_flag,jdbcType=VARCHAR},
                    #{deptdrt_name,jdbcType=VARCHAR},
                    #{deptdrt_code,jdbcType=VARCHAR},
                    #{chfdr_name,jdbcType=VARCHAR},
                    #{chfdr_code,jdbcType=VARCHAR},
                    #{atddr_name,jdbcType=VARCHAR},
                    #{atddr_code,jdbcType=VARCHAR},
                    #{chfpdr_name,jdbcType=VARCHAR},
                    #{chfpdr_code,jdbcType=VARCHAR},
                    #{ipt_dr_name,jdbcType=VARCHAR},
                    #{ipt_dr_code,jdbcType=VARCHAR},
                    #{resp_nurs_name,jdbcType=VARCHAR},
                    #{resp_nurs_code,jdbcType=VARCHAR},
                    #{train_dr_name,jdbcType=VARCHAR},
                    #{train_dr_code,jdbcType=VARCHAR},
                    #{intn_dr_name,jdbcType=VARCHAR},
                    #{intn_dr_code,jdbcType=VARCHAR},
                    #{codr_name,jdbcType=VARCHAR},
                    #{codr_code,jdbcType=VARCHAR},
                    #{qltctrl_dr_name,jdbcType=VARCHAR},
                    #{qltctrl_dr_code,jdbcType=VARCHAR},
                    #{qltctrl_nurs_name,jdbcType=VARCHAR},
                    #{qltctrl_nurs_code,jdbcType=VARCHAR},
                    #{medcas_qlt_name,jdbcType=VARCHAR},
                    #{medcas_qlt_code,jdbcType=VARCHAR},
                    #{qltctrl_date,jdbcType=VARCHAR},
                    #{dscg_way_name,jdbcType=VARCHAR},
                    #{dscg_way,jdbcType=VARCHAR},
                    #{acp_medins_code,jdbcType=VARCHAR},
                    #{acp_medins_name,jdbcType=VARCHAR},
                    #{dscg_31days_rinp_flag,jdbcType=VARCHAR},
                    #{dscg_31days_rinp_pup,jdbcType=VARCHAR},
                    #{damg_intx_ext_rea,jdbcType=VARCHAR},
                    #{damg_intx_ext_rea_disecode,jdbcType=VARCHAR},
                    #{brn_damg_bfadm_coma_dura,jdbcType=VARCHAR},
                    #{brn_damg_afadm_coma_dura,jdbcType=VARCHAR},
                    #{vent_used_dura,jdbcType=VARCHAR},
                    #{cnfm_date,jdbcType=VARCHAR},
                    #{patn_dise_diag_crsp,jdbcType=VARCHAR},
                    #{patn_dise_diag_crsp_code,jdbcType=VARCHAR},
                    #{ipt_patn_diag_inscp,jdbcType=VARCHAR},
                    #{ipt_patn_diag_inscp_code,jdbcType=VARCHAR},
                    #{dscg_trt_rslt,jdbcType=VARCHAR},
                    #{dscg_trt_rslt_code,jdbcType=VARCHAR},
                    #{medins_orgcode,jdbcType=VARCHAR},
                    #{aise,jdbcType=VARCHAR},
                    #{pote_intn_dr_name,jdbcType=VARCHAR},
                    #{hbsag,jdbcType=VARCHAR},
                    #{hcvab,jdbcType=VARCHAR},
                    #{hivab,jdbcType=VARCHAR},
                    #{resc_cnt,jdbcType=VARCHAR},
                    #{resc_succ_cnt,jdbcType=VARCHAR},
                    #{hosp_dise_fsttime,jdbcType=VARCHAR},
                    #{hif_pay_way_name,jdbcType=VARCHAR},
                    #{hif_pay_way_code,jdbcType=VARCHAR},
                    #{med_fee_paymtd_name,jdbcType=VARCHAR},
                    #{medfee_paymtd_code,jdbcType=VARCHAR},
                    #{selfpay_amt,jdbcType=VARCHAR},
                    #{medfee_sumamt,jdbcType=VARCHAR},
                    #{ordn_med_servfee,jdbcType=VARCHAR},
                    #{ordn_trt_oprt_fee,jdbcType=VARCHAR},
                    #{nurs_fee,jdbcType=VARCHAR},
                    #{com_med_serv_oth_fee,jdbcType=VARCHAR},
                    #{palg_diag_fee,jdbcType=VARCHAR},
                    #{lab_diag_fee,jdbcType=VARCHAR},
                    #{rdhy_diag_fee,jdbcType=VARCHAR},
                    #{clnc_dise_fee,jdbcType=VARCHAR},
                    #{nsrgtrt_item_fee,jdbcType=VARCHAR},
                    #{clnc_phys_trt_fee,jdbcType=VARCHAR},
                    #{rgtrt_trt_fee,jdbcType=VARCHAR},
                    #{anst_fee,jdbcType=VARCHAR},
                    #{oprn_fee,jdbcType=VARCHAR},
                    #{rhab_fee,jdbcType=VARCHAR},
                    #{tcm_trt_fee,jdbcType=VARCHAR},
                    #{wmfee,jdbcType=VARCHAR},
                    #{abtl_medn_fee,jdbcType=VARCHAR},
                    #{tcmpat_fee,jdbcType=VARCHAR},
                    #{tcmherb_fee,jdbcType=VARCHAR},
                    #{blo_fee,jdbcType=VARCHAR},
                    #{albu_fee,jdbcType=VARCHAR},
                    #{glon_fee,jdbcType=VARCHAR},
                    #{clotfac_fee,jdbcType=VARCHAR},
                    #{cyki_fee,jdbcType=VARCHAR},
                    #{exam_dspo_matl_fee,jdbcType=VARCHAR},
                    #{trt_dspo_matl_fee,jdbcType=VARCHAR},
                    #{oprn_dspo_matl_fee,jdbcType=VARCHAR},
                    #{oth_fee,jdbcType=VARCHAR},
                    #{vali_flag,jdbcType=VARCHAR},
                    #{fixmedins_code,jdbcType=VARCHAR},
                    #{offsite_med_treat,jdbcType=VARCHAR},
                    #{pre_exam,jdbcType=VARCHAR},
                    #{patn_rlts,jdbcType=VARCHAR},
                    #{nwb_adm_type,jdbcType=VARCHAR},
                    #{mul_nwb_bir_wt,jdbcType=VARCHAR},
                    #{mul_nwb_adm_wt,jdbcType=VARCHAR},
                    #{opsp_diag_caty,jdbcType=VARCHAR},
                    #{opsp_mdtrt_date,jdbcType=VARCHAR},
                    #{otp_wm_dise,jdbcType=VARCHAR},
                    #{wm_dise_code,jdbcType=VARCHAR},
                    #{otp_tcm_dise,jdbcType=VARCHAR},
                    #{tcm_dise_code,jdbcType=VARCHAR},
                    #{spga_nurscare_days,jdbcType=VARCHAR},
                    #{lv1_nurscare_days,jdbcType=VARCHAR},
                    #{scd_nurscare_days,jdbcType=VARCHAR},
                    #{lv3_nurscare_days,jdbcType=VARCHAR},
                    #{otp_wm_diag,jdbcType=VARCHAR},
                    #{otp_wm_diag_dise_code,jdbcType=VARCHAR},
                    #{otp_tcm_diag,jdbcType=VARCHAR},
                    #{otp_tcm_diag_dise_code,jdbcType=VARCHAR},
                    #{adm_diag,jdbcType=VARCHAR},
                    #{bld_cat,jdbcType=VARCHAR},
                    #{bld_amt,jdbcType=VARCHAR},
                    #{bld_unt,jdbcType=VARCHAR},
                    '0'
                )
    </insert>

    <insert id="insertSetlinfoBatch">
        INSERT INTO som_setl_info(
            unique_id,
            psn_no,
            mdtrt_id,
            setl_id,
            bill_code,
            bill_no,
            biz_sn,
            setl_begn_date,
            setl_end_date,
            medins_fill_dept,
            medins_fill_psn,
            hsorg,
            hsorg_opter,
            hi_paymtd,
            psn_selfpay,
            psn_ownpay,
            acct_pay,
            psn_cashpay,
            med_ins_fund,
            hi_no,
            hi_setl_lv,
            hi_type,
            sp_psn_type,
            insuplc,
            ipt_med_type,
            di20_id
        ) values
            (
                #{unique_id,jdbcType=VARCHAR},
                #{psn_no,jdbcType=VARCHAR},
                #{mdtrt_id,jdbcType=VARCHAR},
                #{setl_id,jdbcType=VARCHAR},
                #{bill_code,jdbcType=VARCHAR},
                #{bill_no,jdbcType=VARCHAR},
                #{biz_sn,jdbcType=VARCHAR},
                #{setl_begn_date,jdbcType=VARCHAR},
                #{setl_end_date,jdbcType=VARCHAR},
                #{medins_fill_dept,jdbcType=VARCHAR},
                #{medins_fill_psn,jdbcType=VARCHAR},
                #{hsorg,jdbcType=VARCHAR},
                #{hsorg_opter,jdbcType=VARCHAR},
                #{hi_paymtd,jdbcType=VARCHAR},
                #{psn_selfpay,jdbcType=VARCHAR},
                #{psn_ownpay,jdbcType=VARCHAR},
                #{acct_pay,jdbcType=VARCHAR},
                #{psn_cashpay,jdbcType=VARCHAR},
                #{med_ins_fund,jdbcType=VARCHAR},
                #{hi_no,jdbcType=VARCHAR},
                #{hi_setl_lv,jdbcType=VARCHAR},
                #{hi_type,jdbcType=VARCHAR},
                #{sp_psn_type,jdbcType=VARCHAR},
                #{insuplc,jdbcType=VARCHAR},
                #{ipt_med_type,jdbcType=VARCHAR},
                #{di20_id,jdbcType=VARCHAR}
            )
    </insert>

    <insert id="insertDiseinfoBatch">
        INSERT INTO som_diag_info(
            unique_id,
            palg_no,
            ipt_patn_disediag_type_code,
            disediag_type,
            maindiag_flag,
            diag_code,
            diag_name,
            inhosp_diag_code,
            inhosp_diag_name,
            adm_dise_cond_name,
            adm_dise_cond_code,
            adm_cond,
            adm_cond_code,
            high_diag_evid,
            bkup_deg,
            bkup_deg_code,
            vali_flag,
            ipt_medcas_hmpg_sn,
            mdtrt_sn,
            fixmedins_code,
            di20_id
        )VALUES (
                    #{unique_id,jdbcType=VARCHAR},
                    #{palg_no,jdbcType=VARCHAR},
                    #{ipt_patn_disediag_type_code,jdbcType=VARCHAR},
                    #{disediag_type,jdbcType=VARCHAR},
                    #{maindiag_flag,jdbcType=VARCHAR},
                    #{diag_code,jdbcType=VARCHAR},
                    #{diag_name,jdbcType=VARCHAR},
                    #{inhosp_diag_code,jdbcType=VARCHAR},
                    #{inhosp_diag_name,jdbcType=VARCHAR},
                    #{adm_dise_cond_name,jdbcType=VARCHAR},
                    #{adm_dise_cond_code,jdbcType=VARCHAR},
                    #{adm_cond,jdbcType=VARCHAR},
                    #{adm_cond_code,jdbcType=VARCHAR},
                    #{high_diag_evid,jdbcType=VARCHAR},
                    #{bkup_deg,jdbcType=VARCHAR},
                    #{bkup_deg_code,jdbcType=VARCHAR},
                    #{vali_flag,jdbcType=VARCHAR},
                    #{ipt_medcas_hmpg_sn,jdbcType=VARCHAR},
                    #{mdtrt_sn,jdbcType=VARCHAR},
                    #{fixmedins_code,jdbcType=VARCHAR},
                    #{di20_id,jdbcType=VARCHAR}
                )
    </insert>

    <insert id="insertOprninfoBatch">
        INSERT INTO som_oprn_rcd
        (
            unique_id,
            oprn_oprt_date,
            oprn_oprt_name,
            oprn_oprt_code,
            oprn_oprt_sn,
            oprn_lv_code,
            oprn_lv_name,
            oper_name,
            oper_code,
            asit1_name,
            asit2_name,
            sinc_heal_lv,
            sinc_heal_lv_code,
            anst_mtd_name,
            anst_mtd_code,
            anst_dr_name,
            anst_dr_code,
            oprn_oper_part,
            oprn_oper_part_code,
            oprn_con_time,
            anst_lv_name,
            anst_lv_code,
            oprn_patn_type,
            oprn_patn_type_code,
            main_oprn_flag,
            anst_asa_lv_code,
            anst_asa_lv_name,
            anst_medn_code,
            anst_medn_name,
            anst_medn_dos,
            unt,
            anst_begntime,
            anst_endtime,
            anst_copn_code,
            anst_copn_name,
            anst_copn_dscr,
            pacu_begntime,
            pacu_endtime,
            canc_oprn_flag,
            vali_flag,
            ipt_medcas_hmpg_sn,
            mdtrt_sn,
            oprn_oprt_begntime,
            oprn_oprt_endtime,
            di20_id
        )
        VALUES
            (
                #{unique_id,jdbcType=VARCHAR},
                #{oprn_oprt_date,jdbcType=VARCHAR},
                #{oprn_oprt_name,jdbcType=VARCHAR},
                #{oprn_oprt_code,jdbcType=VARCHAR},
                #{oprn_oprt_sn,jdbcType=VARCHAR},
                #{oprn_lv_code,jdbcType=VARCHAR},
                #{oprn_lv_name,jdbcType=VARCHAR},
                #{oper_name,jdbcType=VARCHAR},
                #{oper_code,jdbcType=VARCHAR},
                #{asit1_name,jdbcType=VARCHAR},
                #{asit2_name,jdbcType=VARCHAR},
                #{sinc_heal_lv,jdbcType=VARCHAR},
                #{sinc_heal_lv_code,jdbcType=VARCHAR},
                #{anst_mtd_name,jdbcType=VARCHAR},
                #{anst_mtd_code,jdbcType=VARCHAR},
                #{anst_dr_name,jdbcType=VARCHAR},
                #{anst_dr_code,jdbcType=VARCHAR},
                #{oprn_oper_part,jdbcType=VARCHAR},
                #{oprn_oper_part_code,jdbcType=VARCHAR},
                #{oprn_con_time,jdbcType=VARCHAR},
                #{anst_lv_name,jdbcType=VARCHAR},
                #{anst_lv_code,jdbcType=VARCHAR},
                #{oprn_patn_type,jdbcType=VARCHAR},
                #{oprn_patn_type_code,jdbcType=VARCHAR},
                #{main_oprn_flag,jdbcType=VARCHAR},
                #{anst_asa_lv_code,jdbcType=VARCHAR},
                #{anst_asa_lv_name,jdbcType=VARCHAR},
                #{anst_medn_code,jdbcType=VARCHAR},
                #{anst_medn_name,jdbcType=VARCHAR},
                #{anst_medn_dos,jdbcType=VARCHAR},
                #{unt,jdbcType=VARCHAR},
                #{anst_begntime,jdbcType=VARCHAR},
                #{anst_endtime,jdbcType=VARCHAR},
                #{anst_copn_code,jdbcType=VARCHAR},
                #{anst_copn_name,jdbcType=VARCHAR},
                #{anst_copn_dscr,jdbcType=VARCHAR},
                #{pacu_begntime,jdbcType=VARCHAR},
                #{pacu_endtime,jdbcType=VARCHAR},
                #{canc_oprn_flag,jdbcType=VARCHAR},
                #{vali_flag,jdbcType=VARCHAR},
                #{ipt_medcas_hmpg_sn,jdbcType=VARCHAR},
                #{mdtrt_sn,jdbcType=VARCHAR},
                #{oprn_oprt_begntime,jdbcType=VARCHAR},
                #{oprn_oprt_endtime,jdbcType=VARCHAR},
                #{di20_id,jdbcType=VARCHAR}
            )
    </insert>

    <insert id="insertIcuinfoBatch">
        insert into som_scs_cutd_info(
            unique_id,
            scs_cutd_ward_type,
            scs_cutd_inpool_time,
            scs_cutd_exit_time,
            scs_cutd_sum_dura,
            di20_id

        )VALUES (
                    #{unique_id,jdbcType=VARCHAR},
                    #{scs_cutd_ward_type,jdbcType=VARCHAR},
                    #{scs_cutd_inpool_time,jdbcType=VARCHAR},
                    #{scs_cutd_exit_time,jdbcType=VARCHAR},
                    #{scs_cutd_sum_dura,jdbcType=VARCHAR},
                    #{di20_id,jdbcType=VARCHAR}
                )
    </insert>

    <insert id="insertOpspdiseinfoBatch">
        INSERT INTO som_otp_crds_diag_info(
            unique_id,
            diag_name,
            diag_code,
            oprn_oprt_name,
            oprn_oprt_code,
            maindiag_flag,
            di20_id
        )values (
                    #{unique_id,jdbcType=VARCHAR},
                    #{diag_name,jdbcType=VARCHAR},
                    #{diag_code,jdbcType=VARCHAR},
                    #{oprn_oprt_name,jdbcType=VARCHAR},
                    #{oprn_oprt_code,jdbcType=VARCHAR},
                    #{maindiag_flag,jdbcType=VARCHAR},
                    #{di20_id,jdbcType=VARCHAR}
                )
    </insert>

    <insert id="insertPayinfoBatch">
        INSERT INTO som_fund_pay_info(
            unique_id,
            fund_pay_type,
            fund_payamt,
            di20_id
        )values
            (
                #{unique_id,jdbcType=VARCHAR},
                #{fund_pay_type,jdbcType=VARCHAR},
                #{fund_payamt,jdbcType=VARCHAR},
                #{di20_id,jdbcType=VARCHAR}
            )
    </insert>

    <insert id="insertIteminfoBatch">
        INSERT INTO som_chrg_item_info(
            unique_id,
            med_chrgitm,
            amt,
            claa_sumfee,
            clab_amt,
            fulamt_ownpay_amt,
            oth_amt,
            di20_id
        )values (
                    #{unique_id,jdbcType=VARCHAR},
                    #{med_chrgitm,jdbcType=VARCHAR},
                    #{amt,jdbcType=VARCHAR},
                    #{claa_sumfee,jdbcType=VARCHAR},
                    #{clab_amt,jdbcType=VARCHAR},
                    #{fulamt_ownpay_amt,jdbcType=VARCHAR},
                    #{oth_amt,jdbcType=VARCHAR},
                    #{di20_id,jdbcType=VARCHAR}
                )

    </insert>

    <insert id="insertBldinfoBatch">
        INSERT INTO som_bld_info(
            unique_id,
            bld_cat,
            bld_amt,
            bld_unt,
            di20_id
        )values (
                    #{unique_id,jdbcType=VARCHAR},
                    #{bld_cat,jdbcType=VARCHAR},
                    #{bld_amt,jdbcType=VARCHAR},
                    #{bld_unt,jdbcType=VARCHAR},
                    #{di20_id,jdbcType=VARCHAR}
                )

    </insert>
</mapper>
