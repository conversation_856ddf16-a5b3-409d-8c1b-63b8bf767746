<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:activiti="http://activiti.org/bpmn" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:omgdc="http://www.omg.org/spec/DD/20100524/DC" xmlns:omgdi="http://www.omg.org/spec/DD/20100524/DI" xmlns:tns="http://www.activiti.org/test" typeLanguage="http://www.w3.org/2001/XMLSchema" expressionLanguage="http://www.w3.org/1999/XPath" targetNamespace="http://www.activiti.org/test" id="m1625922653200" name="">
  <process id="drg_process_2020" name="DRG数据流程" isExecutable="true" isClosed="false" processType="None">
    <startEvent id="start_event" name="开始流程"></startEvent>
    <endEvent id="EndEvent_Main" name="总流程结束"></endEvent>
    <exclusiveGateway id="ExclusiveGateway_ValidateYesOrNo" name="是否校验"></exclusiveGateway>
    <sequenceFlow id="flow5" sourceRef="start_event" targetRef="ExclusiveGateway_ValidateYesOrNo"></sequenceFlow>
    <userTask id="Task_Validate" name="病案校验" activiti:assignee="${UserId}"></userTask>
    <sequenceFlow id="flow2" sourceRef="ExclusiveGateway_ValidateYesOrNo" targetRef="Task_Validate">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${isValidate == 1}]]></conditionExpression>
    </sequenceFlow>
    <userTask id="Task_DrgsGroup" name="DRGs分组" activiti:assignee="${UserId}"></userTask>
    <exclusiveGateway id="ExclusiveGateway_DipGroupYesOrNo" name="是否DIP分组"></exclusiveGateway>
    <sequenceFlow id="flow13" sourceRef="Task_DrgsGroup" targetRef="ExclusiveGateway_DipGroupYesOrNo"></sequenceFlow>
    <userTask id="Task_DipGroup" name="DIP分组" activiti:assignee="${UserId}"></userTask>
    <sequenceFlow id="flow15" sourceRef="ExclusiveGateway_DipGroupYesOrNo" targetRef="Task_DipGroup">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${isDipGroup == 1}]]></conditionExpression>
    </sequenceFlow>
    <exclusiveGateway id="ExclusiveGateway_PpsGroupYesOrNo" name="是否成都分组"></exclusiveGateway>
    <sequenceFlow id="flow16" sourceRef="Task_DipGroup" targetRef="ExclusiveGateway_PpsGroupYesOrNo"></sequenceFlow>
    <sequenceFlow id="flow17" sourceRef="ExclusiveGateway_DipGroupYesOrNo" targetRef="ExclusiveGateway_PpsGroupYesOrNo">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${isDipGroup == 0}]]></conditionExpression>
    </sequenceFlow>
    <userTask id="Task_PpsGroup" name="成都分组" activiti:assignee="${UserId}"></userTask>
    <sequenceFlow id="flow18" sourceRef="ExclusiveGateway_PpsGroupYesOrNo" targetRef="Task_PpsGroup">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${isPpsGroup == 1}]]></conditionExpression>
    </sequenceFlow>
    <exclusiveGateway id="ExclusiveGateway_ExtractBusKeyYesOrNo" name="是否抽取bus_key"></exclusiveGateway>
    <sequenceFlow id="flow19" sourceRef="Task_PpsGroup" targetRef="ExclusiveGateway_ExtractBusKeyYesOrNo"></sequenceFlow>
    <sequenceFlow id="flow20" sourceRef="ExclusiveGateway_PpsGroupYesOrNo" targetRef="ExclusiveGateway_ExtractBusKeyYesOrNo">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${isPpsGroup == 0}]]></conditionExpression>
    </sequenceFlow>
    <userTask id="Task_ExtractBusKey" name="抽取bus_key" activiti:assignee="${UserId}"></userTask>
    <sequenceFlow id="flow21" sourceRef="ExclusiveGateway_ExtractBusKeyYesOrNo" targetRef="Task_ExtractBusKey">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${isExtractBusKey == 1}]]></conditionExpression>
    </sequenceFlow>
    <userTask id="Task_CleanData" name="数据清洗" activiti:assignee="${UserId}"></userTask>
    <exclusiveGateway id="ExclusiveGateway_CleanDateYesOrNo" name="是否清洗"></exclusiveGateway>
    <sequenceFlow id="flow28" sourceRef="Task_Validate" targetRef="ExclusiveGateway_CleanDateYesOrNo"></sequenceFlow>
    <sequenceFlow id="flow29" sourceRef="ExclusiveGateway_CleanDateYesOrNo" targetRef="Task_CleanData">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${isCleanData == 1}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="flow30" sourceRef="ExclusiveGateway_ValidateYesOrNo" targetRef="ExclusiveGateway_CleanDateYesOrNo">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${isValidate == 0}]]></conditionExpression>
    </sequenceFlow>
    <userTask id="Task_GeneraScore" name="生成分值" activiti:assignee="${UserId}"></userTask>
    <exclusiveGateway id="ExclusiveGateway_GeneraScoreYesOrNo" name="是否抽取bus_key"></exclusiveGateway>
    <sequenceFlow id="flow32" sourceRef="Task_ExtractBusKey" targetRef="ExclusiveGateway_GeneraScoreYesOrNo"></sequenceFlow>
    <sequenceFlow id="flow33" sourceRef="ExclusiveGateway_ExtractBusKeyYesOrNo" targetRef="ExclusiveGateway_GeneraScoreYesOrNo">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${isExtractBusKey == 0}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="flow34" sourceRef="ExclusiveGateway_GeneraScoreYesOrNo" targetRef="Task_GeneraScore">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${isGeneraScore == 1}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="flow35" sourceRef="ExclusiveGateway_GeneraScoreYesOrNo" targetRef="EndEvent_Main">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${isGeneraScore == 0}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="flow36" sourceRef="Task_GeneraScore" targetRef="EndEvent_Main"></sequenceFlow>
    <userTask id="Task_Settle_List_Validate" name="清单校验" activiti:assignee="${UserId}"></userTask>
    <exclusiveGateway id="ExclusiveGateway_DrgsGroupYesOrNO" name="是否DRGs分组"></exclusiveGateway>
    <sequenceFlow id="flow38" sourceRef="Task_Settle_List_Validate" targetRef="ExclusiveGateway_DrgsGroupYesOrNO"></sequenceFlow>
    <exclusiveGateway id="ExclusiveGateway_SettleListValidateYesOrNo" name="是否清单校验"></exclusiveGateway>
    <sequenceFlow id="flow39" sourceRef="Task_CleanData" targetRef="ExclusiveGateway_SettleListValidateYesOrNo"></sequenceFlow>
    <sequenceFlow id="flow40" sourceRef="ExclusiveGateway_SettleListValidateYesOrNo" targetRef="Task_Settle_List_Validate">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[#{isSettleListValidate == 1}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="flow41" sourceRef="ExclusiveGateway_SettleListValidateYesOrNo" targetRef="ExclusiveGateway_DrgsGroupYesOrNO">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[#{isSettleListValidate == 0}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="flow42" sourceRef="ExclusiveGateway_CleanDateYesOrNo" targetRef="ExclusiveGateway_SettleListValidateYesOrNo">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${isCleanData == 0}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="flow43" sourceRef="ExclusiveGateway_DrgsGroupYesOrNO" targetRef="Task_DrgsGroup">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[#{isDrgsGroup == 1}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="flow44" sourceRef="ExclusiveGateway_DrgsGroupYesOrNO" targetRef="ExclusiveGateway_DipGroupYesOrNo">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[#{isDrgsGroup == 0}]]></conditionExpression>
    </sequenceFlow>
  </process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_drg_process_2020">
    <bpmndi:BPMNPlane bpmnElement="drg_process_2020" id="BPMNPlane_drg_process_2020">
      <bpmndi:BPMNShape bpmnElement="start_event" id="BPMNShape_start_event">
        <omgdc:Bounds height="35.0" width="35.0" x="150.0" y="20.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="EndEvent_Main" id="BPMNShape_EndEvent_Main">
        <omgdc:Bounds height="35.0" width="35.0" x="1019.0" y="624.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="ExclusiveGateway_ValidateYesOrNo" id="BPMNShape_ExclusiveGateway_ValidateYesOrNo">
        <omgdc:Bounds height="40.0" width="40.0" x="147.0" y="100.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="Task_Validate" id="BPMNShape_Task_Validate">
        <omgdc:Bounds height="55.0" width="105.0" x="115.0" y="180.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="Task_DrgsGroup" id="BPMNShape_Task_DrgsGroup">
        <omgdc:Bounds height="55.0" width="105.0" x="510.0" y="617.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="ExclusiveGateway_DipGroupYesOrNo" id="BPMNShape_ExclusiveGateway_DipGroupYesOrNo">
        <omgdc:Bounds height="40.0" width="40.0" x="542.0" y="517.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="Task_DipGroup" id="BPMNShape_Task_DipGroup">
        <omgdc:Bounds height="55.0" width="105.0" x="510.0" y="398.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="ExclusiveGateway_PpsGroupYesOrNo" id="BPMNShape_ExclusiveGateway_PpsGroupYesOrNo">
        <omgdc:Bounds height="40.0" width="40.0" x="542.0" y="307.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="Task_PpsGroup" id="BPMNShape_Task_PpsGroup">
        <omgdc:Bounds height="55.0" width="105.0" x="510.0" y="183.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="ExclusiveGateway_ExtractBusKeyYesOrNo" id="BPMNShape_ExclusiveGateway_ExtractBusKeyYesOrNo">
        <omgdc:Bounds height="40.0" width="40.0" x="770.0" y="190.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="Task_ExtractBusKey" id="BPMNShape_Task_ExtractBusKey">
        <omgdc:Bounds height="55.0" width="105.0" x="738.0" y="398.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="Task_CleanData" id="BPMNShape_Task_CleanData">
        <omgdc:Bounds height="55.0" width="105.0" x="115.0" y="398.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="ExclusiveGateway_CleanDateYesOrNo" id="BPMNShape_ExclusiveGateway_CleanDateYesOrNo">
        <omgdc:Bounds height="40.0" width="40.0" x="147.0" y="307.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="Task_GeneraScore" id="BPMNShape_Task_GeneraScore">
        <omgdc:Bounds height="55.0" width="105.0" x="738.0" y="614.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="ExclusiveGateway_GeneraScoreYesOrNo" id="BPMNShape_ExclusiveGateway_GeneraScoreYesOrNo">
        <omgdc:Bounds height="40.0" width="40.0" x="770.0" y="517.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="Task_Settle_List_Validate" id="BPMNShape_Task_Settle_List_Validate">
        <omgdc:Bounds height="55.0" width="105.0" x="115.0" y="617.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="ExclusiveGateway_DrgsGroupYesOrNO" id="BPMNShape_ExclusiveGateway_DrgsGroupYesOrNO">
        <omgdc:Bounds height="40.0" width="40.0" x="350.0" y="624.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="ExclusiveGateway_SettleListValidateYesOrNo" id="BPMNShape_ExclusiveGateway_SettleListValidateYesOrNo">
        <omgdc:Bounds height="40.0" width="40.0" x="147.0" y="515.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge bpmnElement="flow5" id="BPMNEdge_flow5">
        <omgdi:waypoint x="167.0" y="55.0"></omgdi:waypoint>
        <omgdi:waypoint x="167.0" y="100.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow2" id="BPMNEdge_flow2">
        <omgdi:waypoint x="167.0" y="140.0"></omgdi:waypoint>
        <omgdi:waypoint x="167.0" y="180.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow13" id="BPMNEdge_flow13">
        <omgdi:waypoint x="562.0" y="617.0"></omgdi:waypoint>
        <omgdi:waypoint x="562.0" y="557.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow15" id="BPMNEdge_flow15">
        <omgdi:waypoint x="562.0" y="517.0"></omgdi:waypoint>
        <omgdi:waypoint x="562.0" y="453.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow16" id="BPMNEdge_flow16">
        <omgdi:waypoint x="562.0" y="398.0"></omgdi:waypoint>
        <omgdi:waypoint x="562.0" y="347.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow17" id="BPMNEdge_flow17">
        <omgdi:waypoint x="542.0" y="537.0"></omgdi:waypoint>
        <omgdi:waypoint x="466.0" y="537.0"></omgdi:waypoint>
        <omgdi:waypoint x="466.0" y="407.0"></omgdi:waypoint>
        <omgdi:waypoint x="466.0" y="326.0"></omgdi:waypoint>
        <omgdi:waypoint x="542.0" y="327.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow18" id="BPMNEdge_flow18">
        <omgdi:waypoint x="562.0" y="307.0"></omgdi:waypoint>
        <omgdi:waypoint x="562.0" y="238.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow19" id="BPMNEdge_flow19">
        <omgdi:waypoint x="615.0" y="210.0"></omgdi:waypoint>
        <omgdi:waypoint x="770.0" y="210.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow20" id="BPMNEdge_flow20">
        <omgdi:waypoint x="562.0" y="307.0"></omgdi:waypoint>
        <omgdi:waypoint x="790.0" y="230.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow21" id="BPMNEdge_flow21">
        <omgdi:waypoint x="790.0" y="230.0"></omgdi:waypoint>
        <omgdi:waypoint x="790.0" y="398.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow28" id="BPMNEdge_flow28">
        <omgdi:waypoint x="167.0" y="235.0"></omgdi:waypoint>
        <omgdi:waypoint x="167.0" y="307.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow29" id="BPMNEdge_flow29">
        <omgdi:waypoint x="167.0" y="347.0"></omgdi:waypoint>
        <omgdi:waypoint x="167.0" y="398.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow30" id="BPMNEdge_flow30">
        <omgdi:waypoint x="147.0" y="120.0"></omgdi:waypoint>
        <omgdi:waypoint x="85.0" y="119.0"></omgdi:waypoint>
        <omgdi:waypoint x="85.0" y="223.0"></omgdi:waypoint>
        <omgdi:waypoint x="85.0" y="326.0"></omgdi:waypoint>
        <omgdi:waypoint x="147.0" y="327.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow32" id="BPMNEdge_flow32">
        <omgdi:waypoint x="790.0" y="453.0"></omgdi:waypoint>
        <omgdi:waypoint x="790.0" y="517.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow33" id="BPMNEdge_flow33">
        <omgdi:waypoint x="810.0" y="210.0"></omgdi:waypoint>
        <omgdi:waypoint x="922.0" y="209.0"></omgdi:waypoint>
        <omgdi:waypoint x="922.0" y="536.0"></omgdi:waypoint>
        <omgdi:waypoint x="810.0" y="537.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow34" id="BPMNEdge_flow34">
        <omgdi:waypoint x="790.0" y="557.0"></omgdi:waypoint>
        <omgdi:waypoint x="790.0" y="614.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow35" id="BPMNEdge_flow35">
        <omgdi:waypoint x="790.0" y="557.0"></omgdi:waypoint>
        <omgdi:waypoint x="1036.0" y="624.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow36" id="BPMNEdge_flow36">
        <omgdi:waypoint x="843.0" y="641.0"></omgdi:waypoint>
        <omgdi:waypoint x="1019.0" y="641.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow38" id="BPMNEdge_flow38">
        <omgdi:waypoint x="220.0" y="644.0"></omgdi:waypoint>
        <omgdi:waypoint x="350.0" y="644.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow39" id="BPMNEdge_flow39">
        <omgdi:waypoint x="167.0" y="453.0"></omgdi:waypoint>
        <omgdi:waypoint x="167.0" y="515.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow40" id="BPMNEdge_flow40">
        <omgdi:waypoint x="167.0" y="555.0"></omgdi:waypoint>
        <omgdi:waypoint x="167.0" y="617.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow41" id="BPMNEdge_flow41">
        <omgdi:waypoint x="167.0" y="555.0"></omgdi:waypoint>
        <omgdi:waypoint x="370.0" y="624.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow42" id="BPMNEdge_flow42">
        <omgdi:waypoint x="187.0" y="327.0"></omgdi:waypoint>
        <omgdi:waypoint x="251.0" y="327.0"></omgdi:waypoint>
        <omgdi:waypoint x="251.0" y="432.0"></omgdi:waypoint>
        <omgdi:waypoint x="251.0" y="534.0"></omgdi:waypoint>
        <omgdi:waypoint x="187.0" y="535.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow43" id="BPMNEdge_flow43">
        <omgdi:waypoint x="390.0" y="644.0"></omgdi:waypoint>
        <omgdi:waypoint x="510.0" y="644.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow44" id="BPMNEdge_flow44">
        <omgdi:waypoint x="370.0" y="624.0"></omgdi:waypoint>
        <omgdi:waypoint x="562.0" y="557.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</definitions>