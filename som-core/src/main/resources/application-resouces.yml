app:
  avatar:
    win:
      # win文件保存路径，如果不写默认保存到项目静态资源路径 /static/avatar 路径下
#      path:
      # win访问静态资源的url前缀（不需要写最后的斜杠）
      url: http://127.0.0.1:8088
    linux:
      # linux文件保存路径，如果不写使用默认保存到 /usr/local/avatarImages/ 路径下
      path: /usr/local/avatarImages/
      # linux访问文件资源url的前缀（需要写扛）
      url: http://192.168.56.37:8080/avatar/

report:
  path: /usr/local/reports/
  winPath: D:\jsoft\App\runjar\reports\
