spring:
  profiles:
    active: prod,liteflow,forecast,app,resouces #默认为开发环境dev
  #    active: prod #默认为开发环境dev
  servlet:
    multipart:
      enabled: true
      max-file-size: 60MB  #单个文件上传最大size
      max-request-size: 300MB  #一次上传多个文件最大size

  redis:
    host: 127.0.0.1 #Redis服务器地址
    database: 0 #Redis数据库索引(默认为0)
    port: 6379
#    password: Erp201103! #中江县人民医院Redis服务器连接密码
  jedis:
    pool:
      max-active: 8 # 连接池最大连接数
      max-wait: -1ms #连接池最大阻塞等待时间(毫秒)
      max-idle: 8 #连接池中的最大空闲连接
      min-idle: 0 #连接池中的最小空闲连接
    timeout: 3000ms # 连接超时时间(毫秒)
  activiti:
    # flase：       默认值。activiti在启动时，会对比数据库表中保存的版本，如果没有表或者版本不匹配，将抛出异常。（生产环境常用）
    # true：        activiti会对数据库中所有表进行更新操作。如果表不存在，则自动创建。（开发时常用）
    # create_drop： 在activiti启动时创建表，在关闭时删除表（必须手动关闭引擎，才能删除表）。（单元测试常用）
    # drop-create： 在activiti启动时删除原来的旧表，然后在创建新表（不需要手动关闭引擎）。
    check-process-definitions: true #自动部署
    database-schema-update: true #自动更新数据库结构
    #流程定义文件存放目录
    process-definition-location-prefix: classpath:/processes/
    #    process-definition-location-suffixes:
    #      - **.bpmn
    #      - **.bpmn20.xml
    history-level: full
    #none:不记录历史流程，性能高，流程结束后不可读取
    #activiti：归档流程实例和活动实例，流程变量不同步
    #audit：默认值，在activiti基础上同步变量值，保存表单属性
    #full：性能较差，记录所有实例和变量细节变化
    db-history-used: true #创建历史表
    # freemarker配置
    freemarker:
      expose-request-attributes: true
      expose-session-attributes: true
      request-context-attribute: request
  main:
    allow-bean-definition-overriding: true
  cache:
    type: redis

mybatis-plus:
  mapper-locations:
    - classpath:dao/**/*.xml
    - classpath*:mapper/**/*.xml
    - classpath*:com/**/mapper/**/*.xml
  configuration:
    map-underscore-to-camel-case: false
    call-setters-on-nulls: true
  global-config:
    banner: false

jwt:
  tokenHeader: Authorization #JWT存储的请求头
  secret: drg-core-secret #JWT加解密使用的密钥
  expiration: 604800 #JWT的超期限时间(60*60*24)
  tokenHead: Bearer  #JWT负载中拿到开头

system:
  config:
    passwordErrorNum: 3 #密码错误次数
secure:
  ignored:
    urls: #安全路径白名单
      - /index.html
      - /swagger-ui.html
      - /swagger-resources/**
      - /swagger/**
      - /**/v2/api-docs
      - /listUploadController/extractUplodadeList
      - /**/*.js
      - /**/*.css
      - /**/*.png
      - /**/*.jpg
      - /**/*.jpeg
      - /**/*.ico
      - /**/*.html
      - /**/*.woff2
      - /**/*.woff
      - /**/*.ttf
      - /webjars/springfox-swagger-ui/**
      - /actuator/**
      - /druid/**
      - /user/querySysOutOfDate
      - /user/queryHospitalInfo
      - /user/login
      - /sysInitController/verifyCode
      - /user/casLogin
      - /user/register
      - /user/verifyToken
      - /preGroupInterface/**
      - /preValidateInterface/**
      #开放所有对外接口服务
      - /services/**
      - /drgCommon/queryDictionary # 码表信息
      - /drgCommon/querySettleListDictionary # 码表信息
      - /drgCommon/querySelectTreeAndSelectList # 科室信息
      - /sysCommonConfigController/queryGroupType # 分组类型
      - /commonController/**
      - /appUpdateController/** # app更新版本检查
      - /settleListManage/updateLockState
      - /app2TestController/**
      - /cnGroup/**
      - /preAiPaymentPrediction/**
      - /zhongyangApi/**
      - /hisView/**
      - /basicHea/**
      - /hospitalizationMedical/**
      - /dts/**
      - /api/**
logging:
  level:
    root: INFO #日志配置DEBUG,INFO,WARN,ERROR
    com.my.som: debug
  file: som_log #配置日志文件名称
  #path: D:/drgLog #配置日志生成路径
redis:
  key:
    prefix:
      authCode: "portal:authCode:"
      settleListDictCode: "portal:settleListDictCode:"
    expire:
      timeOut: 120 # 验证码超期时间

server:
  port: 8088

# webservice两定版本接口，是否旧版字段 true：是 false：不是
webservice:
  interface:
    old: false
    view: false
  # 病案号是否转换为住院号
  par-num:
    convert: false
  # 就诊ID是否作为唯一
  visit-unique:
  #    SOM2103接口，传unique_id则为false
  #    雷波设置为false
   useVisit: true
   
# 数据转换平台配置
data-transform:
  config:
    route-file: "classpath:config/route-config.yml"
    enable-hot-reload: true
    default-timeout: 30000
    validation:
      strict-mode: false
      warn-on-duplicate: true
  http:
    connection-timeout: 5000
    read-timeout: 30000
    max-connections: 100
    connection-request-timeout: 5000
    retry-count: 3
  cache:
    route-config-ttl: 300
  reload-delay: 1000

# 是否外部访问使用一次性token， true：是， false：不是
out-sys:
  # 中江县人民医院设置为false
  access-token: true
  use-passwd: false

#系统注册码
sys:
  unique-id: "54 06 05 00 FF FB 8B 0F"
  linux:
    clear-cache: false

# 视图表名
view:
  tableName:
    baseinfo: SOM_V_MEDICAL_BASE_INFO  # 基本信息
    diaginfo: SOM_V_MEDICAL_DISE_INFO  # 诊断信息
    oprninfo: SOM_V_MEDICAL_OPRN_INFO  # 手术信息
    icuinfo: SOM_V_MEDICAL_ICU_INFO   # 重症信息
    payinfo: SOM_V_MEDICAL_PAY_INFO  # 基金支付信息
    iteminfo: SOM_V_MEDICAL_ITEM_INFO # 收费信息
    bldinfo: SOM_V_MEDICAL_BLD_INFO # 输血信息
    setlinfo: SOM_V_MEDICAL_SEL_INFO # 结算信息
#    opspinfo: SOM_V_MEDICAL_OPSP_DISE_INFO # 门慢特
#    baseinfo: cdymbase.SOM_V_MEDICAL_BASE_INFO  # 基本信息
#    diaginfo: cdymbase.SOM_V_MEDICAL_DISE_INFO  # 诊断信息
#    oprninfo: cdymbase.SOM_V_MEDICAL_OPRN_INFO  # 手术信息
#    icuinfo: cdymbase.SOM_V_MEDICAL_ICU_INFO   # 重症信息
#    payinfo: cdymbase.SOM_V_MEDICAL_PAY_INFO  # 基金支付信息
#    iteminfo: cdymbase.SOM_V_MEDICAL_ITEM_INFO # 收费信息
#    opspinfo: SOM_V_MEDICAL_OPSP_DISE_INFO # 门慢特
#    bldinfo: cdymbase.SOM_V_MEDICAL_BLD_INFO # 输血信息
#    setlinfo: cdymbase.SOM_V_MEDICAL_SEL_INFO # 结算信息
#    dept: SOM_V_DEPT #科室信息
#    medicalstaff: SOM_V_STAFF #医护人员