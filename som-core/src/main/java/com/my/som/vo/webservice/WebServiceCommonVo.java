package com.my.som.vo.webservice;

import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Getter
@Setter
public class WebServiceCommonVo {

    private Map<String, Object> di05Map = new HashMap<>();
    private List<Map<String, Object>> di05CheckItemList = new ArrayList<>();
    private List<Map<String, Object>> di05CheckSpecimenList = new ArrayList<>();
    private List<Map<String, Object>> di05ImageInspectionList = new ArrayList<>();

    private Map<String, Object> di06Map = new HashMap<>();
    private List<Map<String, Object>> di06CheckItemList = new ArrayList<>();
    private List<Map<String, Object>> di06CheckSpecimenList = new ArrayList<>();

    private Map<String, Object> di07Map = new HashMap<>();
    private List<Map<String, Object>> di07DiagnosisList = new ArrayList<>();
    private List<Map<String, Object>> di07CourseRecordList = new ArrayList<>();
    private List<Map<String, Object>> di07OperationList = new ArrayList<>();
    private List<Map<String, Object>> di07ResuceList = new ArrayList<>();
    private List<Map<String, Object>> di07ObituaryList = new ArrayList<>();
    private List<Map<String, Object>> di07OutHosList = new ArrayList<>();

    private Map<String, Object> di08Map = new HashMap<>();
    private List<Map<String, Object>> di08DiagnosisList = new ArrayList<>();

    private List<Map<String, Object>> di09List = new ArrayList<>();

    private Map<String, Object> di10Map = new HashMap<>();

    public WebServiceCommonVo(Map data){
        super();
        this.di05Map.put("mdtrt_sn",data.get("mdtrt_sn"));
        this.di05Map.put("mdtrt_id",data.get("mdtrt_id"));
        this.di05Map.put("psn_no",data.get("psn_no"));
        this.di05Map.put("appy_no",data.get("appy_no"));
        this.di05Map.put("appy_doc_name",data.get("appy_doc_name"));
        this.di05Map.put("rpotc_no",data.get("rpotc_no"));
        this.di05Map.put("rpotc_type_code",data.get("rpotc_type_code"));
        this.di05Map.put("exam_rpotc_name",data.get("exam_rpotc_name"));
        this.di05Map.put("exam_date",data.get("exam_date"));
        this.di05Map.put("rpt_date",data.get("rpt_date"));
        this.di05Map.put("cma_date",data.get("cma_date"));
        this.di05Map.put("sapl_date",data.get("sapl_date"));
        this.di05Map.put("spcm_no",data.get("spcm_no"));
        this.di05Map.put("spcm_name",data.get("spcm_name"));
        this.di05Map.put("exam_type_code",data.get("exam_type_code"));
        this.di05Map.put("exam_item_code",data.get("exam_item_code"));
        this.di05Map.put("exam_type_name",data.get("exam_type_name"));
        this.di05Map.put("exam_item_name",data.get("exam_item_name"));
        this.di05Map.put("inhosp_exam_item_code",data.get("inhosp_exam_item_code"));
        this.di05Map.put("inhosp_exam_item_name",data.get("inhosp_exam_item_name"));
        this.di05Map.put("exam_part",data.get("exam_part"));
        this.di05Map.put("exam_rslt_poit_flag",data.get("exam_rslt_poit_flag"));
        this.di05Map.put("exam_rslt_abn",data.get("exam_rslt_abn"));
        this.di05Map.put("exam_ccls",data.get("exam_ccls"));
        this.di05Map.put("appy_org_name",data.get("appy_org_name"));
        this.di05Map.put("appy_dept_code",data.get("appy_dept_code"));
        this.di05Map.put("exam_dept_code",data.get("exam_dept_code"));
        this.di05Map.put("ipt_dept_code",data.get("ipt_dept_code"));
        this.di05Map.put("ipt_dept_name",data.get("ipt_dept_name"));
        this.di05Map.put("bilg_dr_codg",data.get("bilg_dr_codg"));
        this.di05Map.put("bilg_dr_name",data.get("bilg_dr_name"));
        this.di05Map.put("exe_org_name",data.get("exe_org_name"));
        this.di05Map.put("vali_flag",data.get("vali_flag"));
        if(data.get("iteminfo") != null && !data.get("iteminfo").toString().trim().equals("")) {
            List<Map> checkItemList = (List<Map>)data.get("iteminfo");
            for (Map map : checkItemList) {
                Map<String, Object> checkItemMap = new HashMap<>();
                checkItemMap.put("appy_no",map.get("appy_no"));
                checkItemMap.put("rpotc_no",map.get("rpotc_no"));
                checkItemMap.put("exam_item_code",map.get("exam_item_code"));
                checkItemMap.put("exam_item_name",map.get("exam_item_name"));
                checkItemMap.put("inhosp_exam_item_code",map.get("inhosp_exam_item_code"));
                checkItemMap.put("inhosp_exam_item_name",map.get("inhosp_exam_item_name"));
                checkItemMap.put("exam_charge",map.get("exam_charge"));
                checkItemMap.put("mdtrt_id",data.get("mdtrt_id"));
                this.di05CheckItemList.add(checkItemMap);
            }
        }

        if(data.get("sampleinfo") != null && !data.get("sampleinfo").toString().trim().equals("")) {
            List<Map> checkSpecimenList = (List<Map>)data.get("sampleinfo");
            for (Map map : checkSpecimenList) {
                Map<String, Object> checkSpecimenMap = new HashMap<>();
                checkSpecimenMap.put("rpotc_no",map.get("rpotc_no"));
                checkSpecimenMap.put("appy_no",map.get("appy_no"));
                checkSpecimenMap.put("sapl_date",map.get("sapl_date"));
                checkSpecimenMap.put("spcm_no",map.get("spcm_no"));
                checkSpecimenMap.put("spcm_name",map.get("spcm_name"));
                checkSpecimenMap.put("mdtrt_id",data.get("mdtrt_id"));
                this.di05CheckSpecimenList.add(checkSpecimenMap);
            }
        }

        if(data.get("imageinfo") != null && !data.get("imageinfo").toString().trim().equals("")) {
            List<Map> imageInspectionList = (List<Map>)data.get("imageinfo");
            for (Map map : imageInspectionList) {
                Map<String, Object> imageInspectionMap = new HashMap<>();
                imageInspectionMap.put("rpotc_no",map.get("rpotc_no"));
                imageInspectionMap.put("study_uid",map.get("study_uid"));
                imageInspectionMap.put("patient_id",map.get("patient_id"));
                imageInspectionMap.put("patient_name",map.get("patient_name"));
                imageInspectionMap.put("acession_no",map.get("acession_no"));
                imageInspectionMap.put("study_time",map.get("study_time"));
                imageInspectionMap.put("modality",map.get("modality"));
                imageInspectionMap.put("store_path",map.get("store_path"));
                imageInspectionMap.put("series_count",map.get("series_count"));
                imageInspectionMap.put("image_count",map.get("image_count"));
                imageInspectionMap.put("mdtrt_id",data.get("mdtrt_id"));
                this.di05ImageInspectionList.add(imageInspectionMap);
            }
        }

        handlerDi06Map(data);

        if(data.get("iteminfo") != null && !data.get("iteminfo").toString().trim().equals("")) {
            handlerDi06CheckItemList(data);
        }

        if(data.get("sampleinfo") != null && !data.get("sampleinfo").toString().trim().equals("")) {
            handlerDi06CheckSpecimenList(data);
        }

        handlerDi07Map(data);
        handlerDi07DiagnosisList(data);
        handlerDi07CourseRecordList(data);
        handlerDi07OperationList(data);
        handlerDi07ResuceList(data);
        handlerDi07ObituaryList(data);
        handlerDi07OutHosList(data);

        handlerDi08Map(data);
        handlerDi08DiagnosisList(data);

        handlerDi09List(data);

        handlerDi10Map(data);
    }

    public void handlerDi06Map(Map data) {
        this.di06Map.put("mdtrt_sn",data.get("mdtrt_sn"));
        this.di06Map.put("mdtrt_id",data.get("mdtrt_id"));
        this.di06Map.put("psn_no",data.get("psn_no"));
        this.di06Map.put("appy_no",data.get("appy_no"));
        this.di06Map.put("appy_org_code",data.get("appy_org_code"));
        this.di06Map.put("appy_org_name",data.get("appy_org_name"));
        this.di06Map.put("bilg_dr_codg",data.get("bilg_dr_codg"));
        this.di06Map.put("bilg_dr_name",data.get("bilg_dr_name"));
        this.di06Map.put("exam_org_code",data.get("exam_org_code"));
        this.di06Map.put("exam_org_name",data.get("exam_org_name"));
        this.di06Map.put("appy_dept_code",data.get("appy_dept_code"));
        this.di06Map.put("exam_dept_code",data.get("exam_dept_code"));
        this.di06Map.put("exam_mtd",data.get("exam_mtd"));
        this.di06Map.put("rpotc_no",data.get("rpotc_no"));
        this.di06Map.put("exam_item_code",data.get("exam_item_code"));
        this.di06Map.put("exam_item_name",data.get("exam_item_name"));
        this.di06Map.put("inhosp_exam_item_code",data.get("inhosp_exam_item_code"));
        this.di06Map.put("inhosp_exam_item_name",data.get("inhosp_exam_item_name"));
        this.di06Map.put("rpt_date",data.get("rpt_date"));
        this.di06Map.put("rpot_doc",data.get("rpot_doc"));
        this.di06Map.put("exam_charge",data.get("exam_charge"));
        this.di06Map.put("vali_flag",data.get("vali_flag"));
    }

    public void handlerDi06CheckItemList(Map data) {
        List<Map> checkItemList = (List<Map>)data.get("iteminfo");
        for (Map map : checkItemList) {
            Map<String, Object> checkItemMap = new HashMap<>();
            checkItemMap.put("rpotc_no",map.get("rpotc_no"));
            checkItemMap.put("appy_no",map.get("appy_no"));
            checkItemMap.put("exam_mtd",map.get("exam_mtd"));
            checkItemMap.put("ref_val",map.get("ref_val"));
            checkItemMap.put("exam_unt",map.get("exam_unt"));
            checkItemMap.put("exam_rslt_val",map.get("exam_rslt_val"));
            checkItemMap.put("exam_rslt_dicm",map.get("exam_rslt_dicm"));
            checkItemMap.put("exam_item_detl_code",map.get("exam_item_detl_code"));
            checkItemMap.put("exam_item_detl_name",map.get("exam_item_detl_name"));
            checkItemMap.put("exam_rslt_abn",map.get("exam_rslt_abn"));
            checkItemMap.put("mdtrt_id",data.get("mdtrt_id"));
            this.di06CheckItemList.add(checkItemMap);
        }
    }

    public void handlerDi06CheckSpecimenList(Map data) {
        List<Map> checkSpecimenList = (List<Map>)data.get("sampleinfo");
        for (Map map : checkSpecimenList) {
            Map<String, Object> checkSpecimenMap = new HashMap<>();
            checkSpecimenMap.put("rpotc_no",map.get("rpotc_no"));
            checkSpecimenMap.put("appy_no",map.get("appy_no"));
            checkSpecimenMap.put("sapl_date",map.get("sapl_date"));
            checkSpecimenMap.put("spcm_no",map.get("spcm_no"));
            checkSpecimenMap.put("spcm_name",map.get("spcm_name"));
            checkSpecimenMap.put("mdtrt_id",data.get("mdtrt_id"));
            this.di06CheckSpecimenList.add(checkSpecimenMap);
        }
    }

    public void handlerDi07Map(Map data) {
        this.di07Map.put("mdtrt_sn",data.get("mdtrt_sn"));
        this.di07Map.put("mdtrt_id",data.get("mdtrt_id"));
        this.di07Map.put("psn_no",data.get("psn_no"));
        this.di07Map.put("mdtrtsn",data.get("mdtrtsn"));
        this.di07Map.put("name",data.get("name"));
        this.di07Map.put("gend",data.get("gend"));
        this.di07Map.put("age",data.get("age"));
        this.di07Map.put("adm_rec_no",data.get("adm_rec_no"));
        this.di07Map.put("wardarea_name",data.get("wardarea_name"));
        this.di07Map.put("dept_code",data.get("dept_code"));
        this.di07Map.put("dept_name",data.get("dept_name"));
        this.di07Map.put("bedno",data.get("bedno"));
        this.di07Map.put("adm_time",data.get("adm_time"));
        this.di07Map.put("illhis_stte_name",data.get("illhis_stte_name"));
        this.di07Map.put("illhis_stte_rltl",data.get("illhis_stte_rltl"));
        this.di07Map.put("stte_rele",data.get("stte_rele"));
        this.di07Map.put("chfcomp",data.get("chfcomp"));
        this.di07Map.put("dise_now",data.get("dise_now"));
        this.di07Map.put("hlcon",data.get("hlcon"));
        this.di07Map.put("dise_his",data.get("dise_his"));
        this.di07Map.put("ifet",data.get("ifet"));
        this.di07Map.put("ifet_his",data.get("ifet_his"));
        this.di07Map.put("prev_vcnt",data.get("prev_vcnt"));
        this.di07Map.put("oprn_his",data.get("oprn_his"));
        this.di07Map.put("bld_his",data.get("bld_his"));
        this.di07Map.put("algs_his",data.get("algs_his"));
        this.di07Map.put("psn_his",data.get("psn_his"));
        this.di07Map.put("mrg_his",data.get("mrg_his"));
        this.di07Map.put("mena_his",data.get("mena_his"));
        this.di07Map.put("fmhis",data.get("fmhis"));
        this.di07Map.put("physexm_tprt",data.get("physexm_tprt"));
        this.di07Map.put("physexm_pule",data.get("physexm_pule"));
        this.di07Map.put("physexm_vent_frqu",data.get("physexm_vent_frqu"));
        this.di07Map.put("physexm_systolic_pre",data.get("physexm_systolic_pre"));
        this.di07Map.put("physexm_dstl_pre",data.get("physexm_dstl_pre"));
        this.di07Map.put("physexm_height",data.get("physexm_height"));
        this.di07Map.put("physexm_wt",data.get("physexm_wt"));
        this.di07Map.put("physexm_ordn_stas",data.get("physexm_ordn_stas"));
        this.di07Map.put("physexm_skin_musl",data.get("physexm_skin_musl"));
        this.di07Map.put("physexm_spef_lymph",data.get("physexm_spef_lymph"));
        this.di07Map.put("physexm_head",data.get("physexm_head"));
        this.di07Map.put("physexm_neck",data.get("physexm_neck"));
        this.di07Map.put("physexm_chst",data.get("physexm_chst"));
        this.di07Map.put("physexm_abd",data.get("physexm_abd"));
        this.di07Map.put("physexm_finger_exam",data.get("physexm_finger_exam"));
        this.di07Map.put("physexm_genital_area",data.get("physexm_genital_area"));
        this.di07Map.put("physexm_spin",data.get("physexm_spin"));
        this.di07Map.put("physexm_all_fors",data.get("physexm_all_fors"));
        this.di07Map.put("nersys",data.get("nersys"));
        this.di07Map.put("spcy_info",data.get("spcy_info"));
        this.di07Map.put("asst_exam_rslt",data.get("asst_exam_rslt"));
        this.di07Map.put("tcm4d_rslt",data.get("tcm4d_rslt"));
        this.di07Map.put("syddclft",data.get("syddclft"));
        this.di07Map.put("syddclft_name",data.get("syddclft_name"));
        this.di07Map.put("prnp_trt",data.get("prnp_trt"));
        this.di07Map.put("rec_doc_code",data.get("rec_doc_code"));
        this.di07Map.put("rec_doc_name",data.get("rec_doc_name"));
        this.di07Map.put("ipdr_code",data.get("ipdr_code"));
        this.di07Map.put("ipdr_name",data.get("ipdr_name"));
        this.di07Map.put("chfdr_code",data.get("chfdr_code"));
        this.di07Map.put("chfdr_name",data.get("chfdr_name"));
        this.di07Map.put("chfpdr_code",data.get("chfpdr_code"));
        this.di07Map.put("chfpdr_name",data.get("chfpdr_name"));
        this.di07Map.put("main_symp",data.get("main_symp"));
        this.di07Map.put("adm_rea",data.get("adm_rea"));
        this.di07Map.put("adm_way",data.get("adm_way"));
        this.di07Map.put("apgr",data.get("apgr"));
        this.di07Map.put("diet_info",data.get("diet_info"));
        this.di07Map.put("growth_deg",data.get("growth_deg"));
        this.di07Map.put("mtl_stas_norm",data.get("mtl_stas_norm"));
        this.di07Map.put("slep_info",data.get("slep_info"));
        this.di07Map.put("sp_info",data.get("sp_info"));
        this.di07Map.put("mind_info",data.get("mind_info"));
        this.di07Map.put("nurt",data.get("nurt"));
        this.di07Map.put("self_ablt",data.get("self_ablt"));
        this.di07Map.put("nurscare_obsv_item_name",data.get("nurscare_obsv_item_name"));
        this.di07Map.put("nurscare_obsv_rslt",data.get("nurscare_obsv_rslt"));
        this.di07Map.put("smoke",data.get("smoke"));
        this.di07Map.put("stop_smok_days",data.get("stop_smok_days"));
        this.di07Map.put("smok_info",data.get("smok_info"));
        this.di07Map.put("smok_day",data.get("smok_day"));
        this.di07Map.put("drnk",data.get("drnk"));
        this.di07Map.put("drnk_frqu",data.get("drnk_frqu"));
        this.di07Map.put("drnk_day",data.get("drnk_day"));
        this.di07Map.put("eval_time",data.get("eval_time"));
        this.di07Map.put("resp_nurs_name",data.get("resp_nurs_name"));
        this.di07Map.put("vali_flag",data.get("vali_flag"));
    }

    public void handlerDi07DiagnosisList(Map data) {
        if(data.get("diseinfo") != null && !data.get("diseinfo").toString().trim().equals("")) {
            List<Map> list = (List<Map>)data.get("diseinfo");
            for (Map map : list) {
                Map<String, Object> tempMap = new HashMap<>();
                tempMap.put("inout_diag_type",map.get("inout_diag_type"));
                tempMap.put("maindiag_flag",map.get("maindiag_flag"));
                tempMap.put("diag_seq",map.get("diag_seq"));
                tempMap.put("diag_time",map.get("diag_time"));
                tempMap.put("wm_diag_code",map.get("wm_diag_code"));
                tempMap.put("wm_diag_name",map.get("wm_diag_name"));
                tempMap.put("tcm_dise_code",map.get("tcm_dise_code"));
                tempMap.put("tcm_dise_name",map.get("tcm_dise_name"));
                tempMap.put("tcmsymp_code",map.get("tcmsymp_code"));
                tempMap.put("tcmsymp",map.get("tcmsymp"));
                tempMap.put("vali_flag",map.get("vali_flag"));
                tempMap.put("mdtrt_id",data.get("mdtrt_id"));
                this.di07DiagnosisList.add(tempMap);
            }
        }
    }

    public void handlerDi07CourseRecordList(Map data) {
        if(data.get("coursrinfo") != null && !data.get("coursrinfo").toString().trim().equals("")) {
            List<Map> list = (List<Map>)data.get("coursrinfo");
            for (Map map : list) {
                Map<String, Object> tempMap = new HashMap<>();
                tempMap.put("codse_rcd_id",map.get("codse_rcd_id"));
                tempMap.put("dept_code",map.get("dept_code"));
                tempMap.put("dept_name",map.get("dept_name"));
                tempMap.put("wardarea_name",map.get("wardarea_name"));
                tempMap.put("bedno",map.get("bedno"));
                tempMap.put("rcd_time",map.get("rcd_time"));
                tempMap.put("chfcomp",map.get("chfcomp"));
                tempMap.put("cas_ftur",map.get("cas_ftur"));
                tempMap.put("tcm4d_rslt",map.get("tcm4d_rslt"));
                tempMap.put("dise_evid",map.get("dise_evid"));
                tempMap.put("prel_wm_diag_code",map.get("prel_wm_diag_code"));
                tempMap.put("prel_wm_dise_name",map.get("prel_wm_dise_name"));
                tempMap.put("prel_tcm_diag_code",map.get("prel_tcm_diag_code"));
                tempMap.put("prel_tcm_dise_name",map.get("prel_tcm_dise_name"));
                tempMap.put("prel_tcmsymp_code",map.get("prel_tcmsymp_code"));
                tempMap.put("prel_tcmsymp",map.get("prel_tcmsymp"));
                tempMap.put("finl_wm_diag_code",map.get("finl_wm_diag_code"));
                tempMap.put("finl_wm_diag_name",map.get("finl_wm_diag_name"));
                tempMap.put("finl_tcm_dise_code",map.get("finl_tcm_dise_code"));
                tempMap.put("finl_tcm_dise_name",map.get("finl_tcm_dise_name"));
                tempMap.put("finl_tcmsymp_code",map.get("finl_tcmsymp_code"));
                tempMap.put("finl_tcmsymp",map.get("finl_tcmsymp"));
                tempMap.put("dise_plan",map.get("dise_plan"));
                tempMap.put("prnp_trt",map.get("prnp_trt"));
                tempMap.put("ipdr_code",map.get("ipdr_code"));
                tempMap.put("ipdr_name",map.get("ipdr_name"));
                tempMap.put("prnt_doc_name",map.get("prnt_doc_name"));
                tempMap.put("vali_flag",map.get("vali_flag"));
                tempMap.put("mdtrt_id",data.get("mdtrt_id"));
                this.di07CourseRecordList.add(tempMap);
            }
        }
    }

    public void handlerDi07OperationList(Map data) {
        if(data.get("oprninfo") != null && !data.get("oprninfo").toString().trim().equals("")) {
            List<Map> list = (List<Map>)data.get("oprninfo");
            for (Map map : list) {
                Map<String, Object> tempMap = new HashMap<>();
                tempMap.put("oprn_appy_id",map.get("oprn_appy_id"));
                tempMap.put("oprn_seq",map.get("oprn_seq"));
                tempMap.put("blotype_abo",map.get("blotype_abo"));
                tempMap.put("oprn_time",map.get("oprn_time"));
                tempMap.put("oprn_type_code",map.get("oprn_type_code"));
                tempMap.put("oprn_type_name",map.get("oprn_type_name"));
                tempMap.put("bfpn_diag_code",map.get("bfpn_diag_code"));
                tempMap.put("bfpn_oprn_diag_name",map.get("bfpn_oprn_diag_name"));
                tempMap.put("bfpn_inhosp_ifet",map.get("bfpn_inhosp_ifet"));
                tempMap.put("afpn_diag_code",map.get("afpn_diag_code"));
                tempMap.put("afpn_diag_name",map.get("afpn_diag_name"));
                tempMap.put("sinc_heal_lv_code",map.get("sinc_heal_lv_code"));
                tempMap.put("sinc_heal_lv",map.get("sinc_heal_lv"));
                tempMap.put("back_oprn",map.get("back_oprn"));
                tempMap.put("selv",map.get("selv"));
                tempMap.put("prev_abtl_medn",map.get("prev_abtl_medn"));
                tempMap.put("abtl_medn_days",map.get("abtl_medn_days"));
                tempMap.put("oprn_oprt_code",map.get("oprn_oprt_code"));
                tempMap.put("oprn_oprt_name",map.get("oprn_oprt_name"));
                tempMap.put("oprn_lv_code",map.get("oprn_lv_code"));
                tempMap.put("oprn_lv_name",map.get("oprn_lv_name"));
                tempMap.put("anst_mtd_code",map.get("anst_mtd_code"));
                tempMap.put("anst_mtd_name",map.get("anst_mtd_name"));
                tempMap.put("anst_lv_code",map.get("anst_lv_code"));
                tempMap.put("anst_lv_name",map.get("anst_lv_name"));
                tempMap.put("exe_anst_dept_code",map.get("exe_anst_dept_code"));
                tempMap.put("exe_anst_dept_name",map.get("exe_anst_dept_name"));
                tempMap.put("anst_efft",map.get("anst_efft"));
                tempMap.put("oprn_begntime",map.get("oprn_begntime"));
                tempMap.put("oprn_endtime",map.get("oprn_endtime"));
                tempMap.put("oprn_asps",map.get("oprn_asps"));
                tempMap.put("oprn_asps_ifet",map.get("oprn_asps_ifet"));
                tempMap.put("afpn_info",map.get("afpn_info"));
                tempMap.put("oprn_merg",map.get("oprn_merg"));
                tempMap.put("oprn_conc",map.get("oprn_conc"));
                tempMap.put("oprn_anst_dept_code",map.get("oprn_anst_dept_code"));
                tempMap.put("oprn_anst_dept_name",map.get("oprn_anst_dept_name"));
                tempMap.put("palg_dise",map.get("palg_dise"));
                tempMap.put("oth_med_dspo",map.get("oth_med_dspo"));
                tempMap.put("out_std_oprn_time",map.get("out_std_oprn_time"));
                tempMap.put("oprn_oper_name",map.get("oprn_oper_name"));
                tempMap.put("oprn_asit_name1",map.get("oprn_asit_name1"));
                tempMap.put("oprn_asit_name2",map.get("oprn_asit_name2"));
                tempMap.put("anst_dr_name",map.get("anst_dr_name"));
                tempMap.put("anst_asa_lv_code",map.get("anst_asa_lv_code"));
                tempMap.put("anst_asa_lv_name",map.get("anst_asa_lv_name"));
                tempMap.put("anst_medn_code",map.get("anst_medn_code"));
                tempMap.put("anst_medn_name",map.get("anst_medn_name"));
                tempMap.put("anst_medn_dos",map.get("anst_medn_dos"));
                tempMap.put("anst_dosunt",map.get("anst_dosunt"));
                tempMap.put("anst_begntime",map.get("anst_begntime"));
                tempMap.put("anst_endtime",map.get("anst_endtime"));
                tempMap.put("anst_merg_symp_code",map.get("anst_merg_symp_code"));
                tempMap.put("anst_merg_symp",map.get("anst_merg_symp"));
                tempMap.put("anst_merg_symp_dscr",map.get("anst_merg_symp_dscr"));
                tempMap.put("pacu_begntime",map.get("pacu_begntime"));
                tempMap.put("pacu_endtime",map.get("pacu_endtime"));
                tempMap.put("oprn_selv",map.get("oprn_selv"));
                tempMap.put("canc_oprn",map.get("canc_oprn"));
                tempMap.put("vali_flag",map.get("vali_flag"));
                tempMap.put("mdtrt_id",data.get("mdtrt_id"));
                this.di07OperationList.add(tempMap);
            }
        }
    }

    public void handlerDi07ResuceList(Map data) {
        if(data.get("rescinfo") != null && !data.get("rescinfo").toString().trim().equals("")) {
            List<Map> list = (List<Map>)data.get("rescinfo");
            for (Map map : list) {
                Map<String, Object> tempMap = new HashMap<>();
                tempMap.put("resc_id",map.get("resc_id"));
                tempMap.put("dept",map.get("dept"));
                tempMap.put("dept_name",map.get("dept_name"));
                tempMap.put("wardarea_name",map.get("wardarea_name"));
                tempMap.put("bedno",map.get("bedno"));
                tempMap.put("diag_name",map.get("diag_name"));
                tempMap.put("diag_code",map.get("diag_code"));
                tempMap.put("cond_chg",map.get("cond_chg"));
                tempMap.put("resc_mes",map.get("resc_mes"));
                tempMap.put("oprn_oprt_code",map.get("oprn_oprt_code"));
                tempMap.put("oprn_oprt_name",map.get("oprn_oprt_name"));
                tempMap.put("oprn_oper_part",map.get("oprn_oper_part"));
                tempMap.put("itvt_name",map.get("itvt_name"));
                tempMap.put("oprt_mtd",map.get("oprt_mtd"));
                tempMap.put("oprt_cnt",map.get("oprt_cnt"));
                tempMap.put("resc_begntime",map.get("resc_begntime"));
                tempMap.put("resc_endtime",map.get("resc_endtime"));
                tempMap.put("dise_item_name",map.get("dise_item_name"));
                tempMap.put("dise_ccls",map.get("dise_ccls"));
                tempMap.put("dise_ccls_qunt",map.get("dise_ccls_qunt"));
                tempMap.put("dise_ccls_code",map.get("dise_ccls_code"));
                tempMap.put("mnan",map.get("mnan"));
                tempMap.put("resc_psn_list",map.get("resc_psn_list"));
                tempMap.put("proftechttl_code",map.get("proftechttl_code"));
                tempMap.put("doc_code",map.get("doc_code"));
                tempMap.put("dr_name",map.get("dr_name"));
                tempMap.put("vali_flag",map.get("vali_flag"));
                tempMap.put("mdtrt_id",data.get("mdtrt_id"));
                this.di07ResuceList.add(tempMap);
            }
        }
    }

    public void handlerDi07ObituaryList(Map data) {
        if(data.get("dieinfo") != null && !data.get("dieinfo").toString().trim().equals("")) {
            List<Map> list = (List<Map>)data.get("dieinfo");
            for (Map map : list) {
                Map<String, Object> tempMap = new HashMap<>();
                tempMap.put("dept",map.get("dept"));
                tempMap.put("dept_name",map.get("dept_name"));
                tempMap.put("wardarea_name",map.get("wardarea_name"));
                tempMap.put("bedno",map.get("bedno"));
                tempMap.put("adm_time",map.get("adm_time"));
                tempMap.put("adm_dise",map.get("adm_dise"));
                tempMap.put("adm_info",map.get("adm_info"));
                tempMap.put("trt_proc_dscr",map.get("trt_proc_dscr"));
                tempMap.put("die_time",map.get("die_time"));
                tempMap.put("die_drt_rea",map.get("die_drt_rea"));
                tempMap.put("die_drt_rea_code",map.get("die_drt_rea_code"));
                tempMap.put("die_dise_name",map.get("die_dise_name"));
                tempMap.put("die_diag_code",map.get("die_diag_code"));
                tempMap.put("agre_corp_dset",map.get("agre_corp_dset"));
                tempMap.put("ipdr_name",map.get("ipdr_name"));
                tempMap.put("chfpdr_code",map.get("chfpdr_code"));
                tempMap.put("chfpdr_name",map.get("chfpdr_name"));
                tempMap.put("chfdr_name",map.get("chfdr_name"));
                tempMap.put("sign_time",map.get("sign_time"));
                tempMap.put("vali_flag",map.get("vali_flag"));
                tempMap.put("mdtrt_id",data.get("mdtrt_id"));
                this.di07ObituaryList.add(tempMap);
            }
        }
    }

    public void handlerDi07OutHosList(Map data) {
        if(data.get("dscginfo") != null && !data.get("dscginfo").toString().trim().equals("")) {
            List<Map> list = (List<Map>)data.get("dscginfo");
            for (Map map : list) {
                Map<String, Object> tempMap = new HashMap<>();
                tempMap.put("dscg_date",map.get("dscg_date"));
                tempMap.put("adm_diag_dscr",map.get("adm_diag_dscr"));
                tempMap.put("dscg_dise_dscr",map.get("dscg_dise_dscr"));
                tempMap.put("adm_info",map.get("adm_info"));
                tempMap.put("trt_proc_rslt_dscr",map.get("trt_proc_rslt_dscr"));
                tempMap.put("dscg_info",map.get("dscg_info"));
                tempMap.put("dscg_drord",map.get("dscg_drord"));
                tempMap.put("caty",map.get("caty"));
                tempMap.put("rec_doc",map.get("rec_doc"));
                tempMap.put("main_drug_name",map.get("main_drug_name"));
                tempMap.put("oth_imp_info",map.get("oth_imp_info"));
                tempMap.put("vali_flag",map.get("vali_flag"));
                tempMap.put("mdtrt_id",data.get("mdtrt_id"));
                this.di07OutHosList.add(tempMap);
            }
        }
    }

    public void handlerDi08Map(Map data) {
        this.di08Map.put("mdtrt_id",data.get("mdtrt_id"));
        this.di08Map.put("psn_no",data.get("psn_no"));
        this.di08Map.put("med_type",data.get("med_type"));
        this.di08Map.put("begntime",data.get("begntime"));
        this.di08Map.put("main_cond_dscr",data.get("main_cond_dscr"));
        this.di08Map.put("dise_codg",data.get("dise_codg"));
        this.di08Map.put("dise_name",data.get("dise_name"));
        this.di08Map.put("birctrl_type",data.get("birctrl_type"));
        this.di08Map.put("birctrl_matn_date",data.get("birctrl_matn_date"));
        this.di08Map.put("matn_type",data.get("matn_type"));
        this.di08Map.put("geso_val",data.get("geso_val"));
    }

    public void handlerDi08DiagnosisList(Map data) {
        if(data.get("diseinfo") != null && !data.get("diseinfo").toString().trim().equals("")) {
            List<Map> list = (List<Map>)data.get("diseinfo");
            for (Map map : list) {
                Map<String, Object> tempMap = new HashMap<>();
                tempMap.put("diag_type",map.get("diag_type"));
                tempMap.put("diag_srt_no",map.get("diag_srt_no"));
                tempMap.put("diag_code",map.get("diag_code"));
                tempMap.put("diag_name",map.get("diag_name"));
                tempMap.put("diag_dept",map.get("diag_dept"));
                tempMap.put("dise_dor_no",map.get("dise_dor_no"));
                tempMap.put("dise_dor_name",map.get("dise_dor_name"));
                tempMap.put("diag_time",map.get("diag_time"));
                tempMap.put("vali_flag",map.get("vali_flag"));
                tempMap.put("mdtrt_id",data.get("jzid"));
                this.di08DiagnosisList.add(tempMap);
            }
        }
    }

    public void handlerDi09List(Map data) {
        if(data.get("feedetaiList") != null && !data.get("feedetaiList").toString().trim().equals("")) {
            List<Map> list = (List<Map>)data.get("feedetaiList");
            for (Map map : list) {
                Map<String, Object> tempMap = new HashMap<>();
                tempMap.put("feedetl_sn",map.get("feedetlSn"));
                tempMap.put("mdtrt_id",map.get("mdtrtId"));
                tempMap.put("psn_no",map.get("psnNo"));
                tempMap.put("chrg_bchno",map.get("chrgBchno"));
                tempMap.put("dise_codg",map.get("diseCodg"));
                tempMap.put("rxno",map.get("rxno"));
                tempMap.put("rx_circ_flag",map.get("rxCircFlag"));
                tempMap.put("fee_ocur_time",map.get("feeOcurTime"));
                tempMap.put("med_list_codg",map.get("medListCodg"));
                tempMap.put("medins_list_codg",map.get("medinsListCodg"));
                tempMap.put("det_item_fee_sumamt",map.get("detItemFeeSumamt"));
                tempMap.put("cnt",map.get("cnt"));
                tempMap.put("pric",map.get("pric"));
                tempMap.put("sin_dos_dscr",map.get("sinDosDscr"));
                tempMap.put("used_frqu_dscr",map.get("usedFrquDscr"));
                tempMap.put("prd_days",map.get("prdDays"));
                tempMap.put("medc_way_dscr",map.get("medcWayDscr"));
                tempMap.put("bilg_dept_codg",map.get("bilgDeptCodg"));
                tempMap.put("bilg_dept_name",map.get("bilgDeptName"));
                tempMap.put("bilg_dr_codg",map.get("bilgDrCodg"));
                tempMap.put("bilg_dr_name",map.get("bilgDrName"));
                tempMap.put("acord_dept_codg",map.get("acordDeptCodg"));
                tempMap.put("acord_dept_name",map.get("acordDeptName"));
                tempMap.put("orders_dr_code",map.get("ordersDrCode"));
                tempMap.put("orders_dr_name",map.get("ordersDrName"));
                tempMap.put("hosp_appr_flag",map.get("hospApprFlag"));
                tempMap.put("tcmdrug_used_way",map.get("tcmdrugUsedWay"));
                tempMap.put("etip_flag",map.get("etipFlag"));
                tempMap.put("etip_hosp_code",map.get("etipHospCode"));
                tempMap.put("dscg_tkdrug_flag",map.get("dscgTkdrugFlag"));
                tempMap.put("matn_fee_flag",map.get("matnFeeFlag"));
//                tempMap.put("mdtrt_id",data.get("initFeedetlSn"));
//                tempMap.put("mdtrt_id",data.get("drordNo"));
//                tempMap.put("mdtrt_id",data.get("medType"));
//                tempMap.put("mdtrt_id",data.get("memo"));
//                tempMap.put("mdtrt_id",data.get("extData"));

                tempMap.put("dosform_code",map.get("dosform_code"));
                tempMap.put("mcs_prov_code",map.get("mcs_prov_code"));
                this.di09List.add(tempMap);
            }
        }
    }

    public void handlerDi10Map(Map data) {
        this.di10Map.put("hosp_dept_codg",data.get("yyksbm"));
        this.di10Map.put("caty",data.get("kb"));
        this.di10Map.put("hosp_dept_name",data.get("yyksmc"));
        this.di10Map.put("begntime",data.get("kssj"));
        this.di10Map.put("endtime",data.get("jssj"));
        this.di10Map.put("itro",data.get("jj"));
        this.di10Map.put("dept_resper_name",data.get("ksfzrxm"));
        this.di10Map.put("dept_resper_tel",data.get("ksfzrdh"));
        this.di10Map.put("dept_med_serv_scp",data.get("ksylfwfw"));
        this.di10Map.put("dept_estbdat",data.get("ksclrq"));
        this.di10Map.put("aprv_bed_cnt",data.get("pzcws"));
        this.di10Map.put("hi_crtf_bed_cnt",data.get("ybrkcws"));
        this.di10Map.put("poolarea_no",data.get("tcqbh"));
        this.di10Map.put("dr_psncnt",data.get("yisrs"));
        this.di10Map.put("phar_psncnt",data.get("ysrs"));
        this.di10Map.put("nurs_psncnt",data.get("hsrs"));
        this.di10Map.put("tecn_psncnt",data.get("jsrs"));
        this.di10Map.put("memo",data.get("bz"));
    }
}
