package com.my.som.vo.cnBusiness;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.List;

/**
 * CN分组器入组参数
 */

@Setter
@Getter
public class CNGroupDataInfoVo {

    /** 唯一ID */
    private String k00;

    /** 姓名 */
    private String name;

    /** 病历首页的主键 */
    private String B_WT4_V1_ID;

    /** 主要诊断编码 */
    private String DISEASE_CODE;

    /** 年龄 */
    private int AGE;

    /** 性别（男、女） */
    private String GENDER;

    /** 新生儿天数 */
    private Integer SF0100;

    /** 新生儿出生体重 */
    private Integer SF0101;

    /** 新生儿入院体重 */
    private Integer SF0102;

    /** 呼吸机使用时间 */
    private Integer SF0104;

    /** 出院转归 */
    private Integer SF0108;

    /** 住院天数 */
    private int ACCTUAL_DAYS;

    /** 总费用 */
    private BigDecimal TOTAL_EXPENSE;

    /** 其他诊断 */
    private List<String> diags_code;

    /** 其他手术 */
    private List<String> opers_code;

    /** 入院时间 */
    private String inHosTime;

    /** 出院时间 */
    private String outHosTime;

    /** 住院医师 */
    private String drCodg;

    /** 出院科别 */
    private String outDept;

    /** 其他诊断编码 */
    private String c06c_1;

    /** 其他诊断编码 */
    private String c06c_2;

    /** 其他诊断编码 */
    private String c06c_3;

    /** 其他诊断编码 */
    private String c06c_4;

    /** 其他诊断编码 */
    private String c06c_5;

    /** 其他诊断编码 */
    private String c06c_6;

    /** 其他诊断编码 */
    private String c06c_7;

    /** 其他诊断编码 */
    private String c06c_8;

    /** 其他诊断编码 */
    private String c06c_9;

    /** 其他诊断编码 */
    private String c06c_10;

    /** 其他诊断编码 */
    private String c06c_11;

    /** 其他诊断编码 */
    private String c06c_12;

    /** 其他诊断编码 */
    private String c06c_13;

    /** 其他诊断编码 */
    private String c06c_14;

    /** 其他诊断编码 */
    private String c06c_15;

    /** 手术操作编码0 */
    private String c35c_0;

    /** 手术操作编码1 */
    private String c35c_1;

    /** 手术操作编码2 */
    private String c35c_2;

    /** 手术操作编码3 */
    private String c35c_3;

    /** 手术操作编码4 */
    private String c35c_4;

    /** 手术操作编码5 */
    private String c35c_5;

    /** 手术操作编码6 */
    private String c35c_6;

    /**
     *  返回结果
     */

    /** drg编码 */
    private String drg;
    /** drg名称 */
    private String drgName;
}
