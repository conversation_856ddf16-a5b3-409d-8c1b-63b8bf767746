package com.my.som.vo.medicalQuality;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

@Getter
@Setter
public class DeptComparAnalysisVo {
    /** 科室编码 */
    private String deptCode;
    /** 科室名称 */
    private String deptName;
    /** 病案总数 */
    private String medcasVal;
    /**组数*/
    private String dipNum;
    /** 入组病案数 */
    private String drgInGroupMedcasVal;
    /** 未入组病案数 */
    private String notInGroupNum;
    /** 入组率 */
    private String inGroupRate;
    /** 权重 */
    private String dipWt;
    /** cmi */
    private String cmi;
    /** 住院总费用 */
    private String sumfee;
    /** 平均住院费用 */
    private String avgCost;
    /** 平均住院天数 */
    private String actIpt;
    /** 床位费 */
    private String cwf;
    /** 诊查费 */
    private String zcf;
    /** 检查费 */
    private String jcf;
    /** 化验费 */
    private String hyf;
    /** 治疗费 */
    private String treat_fee;
    /** 手术费 */
    private String ssf;
    /** 护理费 */
    private String nursfee;
    /** 卫生材料费 */
    private String wsclf;
    /** 西药费 */
    private String west_fee;
    /** 中药饮片费 */
    private String zyypf;
    /** 中成药 */
    private String zcy;
    /** 一般治疗费 */
    private String ybzlf;
    /** 挂号费 */
    private String ghf;
    /** 其他费 */
    private String qt;

    /** 城职人数 */
    private BigDecimal cztotalCount;
    /** 费用 */
    private BigDecimal cztotalCost;
    /** 其他费 */
    private String czpoint;
    /** 费用 */
    private BigDecimal czpredictCost;
    /** 费用 */
    private BigDecimal cxtotalCount;
    /** 费用 */
    private BigDecimal cxtotalCost;
    /** 费用 */
    private String cxpoint;
    /** 费用 */
    private BigDecimal cxpredictCost;
    /** 费用 */
    private String paytotalCost;
    /** 费用 */
    private BigDecimal predictCost;
    /** 费用 */
    private BigDecimal payCost;
    /** 差异比 */
    private BigDecimal cxcyb;
    /** 差异比 */
    private BigDecimal czcyb;
    /** 差异比 */
    private BigDecimal cyb;
    /**人数*/
    private BigDecimal count;

    private BigDecimal yccy;

    private BigDecimal cxyccy;
    private BigDecimal czyccy;



}
