package com.my.som.vo.listManagement;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class SetLSybInfoVo {

    /**
     * 唯一标识
     */
    private String k00;
    /** settleList表id */
    private Long settleListId;
    /** 结算月份 */
    private String setl_mon;
    /**
     * 就诊id
     */
    private String mdtrt_id;
    /**
     * 人员编号
     */
    private String psn_no;
    /**
     * 结算ID
     */
    private String setl_id;

    /**
     * 定点医药机构名称
     */
    private String fixmedins_name;

    /**
     * 定点医药机构代码
     */
    private String fixmedins_code;

    /**
     * 定点医疗机构收费等级
     */
    private String hi_setl_lv;
    //医保编号
    private String hi_no;
    //病案号
    private String medcasno;
    //申报时间
    private String dcla_time;
    //人员姓名
    private String psn_name;
    // 性别
    private String gend;
    //出生日期
    private String brdy;
    //年龄
    private String age;
    //国籍
    private String ntly;
    //（年龄不足 1 周岁）年龄
    private String nwb_age;
    //民族
    private String naty;
    //患者证件类别
    private String patn_cert_type;
    //证件号码
    private String certno;
    //职业
    private String prfs;
    // 现住址
    private String curr_addr;
    //单位名称
    private String emp_name;
    //单位地址
    private String emp_addr;
    //单位电话
    private String emp_tel;
    //邮编
    private String poscode;
    //联系人姓名
    private String coner_name;
    //与患者关系
    private String patn_rlts;
    //联系人地址
    private String coner_addr;
    //联系人电话
    private String coner_tel;
    //医保类型
    private String hi_type;
    //参保地
    private String insuplc;
    //特殊人员类 型
    private String sp_psn_type;

    //新生儿入院 类型
    private String nwb_adm_type;
    //新生儿出生 体重
    private String nwb_bir_wt;
    //新生儿入院 体重
    private String nwb_adm_wt;
    //门诊慢特病 诊断科别
    private String opsp_diag_caty;
    //门诊慢特病 就诊日期
    private String opsp_mdtrt_date;
    //住院医疗类 型
    private String ipt_med_type;
    //入院途径
    private String adm_way;
    //治疗类别
    private String trt_type;
    //入院时间
    private String adm_time;
    //入院科别
    private String adm_caty;
    //转科科别
    private String refldept_dept;
    //出院时间
    private String dscg_time;
    //出院科别
    private String dscg_caty;
    //实际住院天 数
    private String act_ipt_days;
    //门（急）诊西医诊断
    private String otp_wm_dise;
    //西医诊断疾病代码
    private String wm_dise_code;
    //门（急）诊中医诊断
    private String otp_tcm_dise;
    //中医诊断代码
    private String tcm_dise_code;
    //诊断代码计数
    private String diag_code_cnt;
    // 主诊断标志
    private String maindiag_flag;
    //手术操作代 码计数
    private String oprn_oprt_code_cnt;
    //呼吸机使用 时长
    private String vent_used_dura;
    /** 状态分类 */
    private String stas_type;
    //颅脑损伤患者入院前昏迷时长
    private String pwcry_bfadm_coma_dura;
    //颅脑损伤患者入院后昏 迷时长
    private String pwcry_afadm_coma_dura;
    //特级护理天 数
    private String spga_nurscare_days;
    //一级护理天 数
    private String lv1_nurscare_days;
    //二级护理天 数
    private String scd_nurscare_days;
    //三级护理天 数
    private String lv3_nurscare_days;
    //离院方式
    private String dscg_way;

    //拟接收机构 名称
    private String acp_medins_name;
    //票据代码
    private String bill_code;
    //票据号码
    private String bill_no;
    //业务流水号
    private String biz_sn;
    //出院 31 天内再住院计划标志
    private String days_rinp_flag_31;
    //出院 31 天内 再住院目的
    private String days_rinp_pup_31;
    //主诊医师姓 名
    private String chfpdr_name;

    //主诊医师代 码
    private String chfpdr_code;
    //责任护士姓 名
    private String charge_nurse_name;
    //责任护士代 码
    private String charge_nurse_code;
    //结算开始日 期
    private String setl_begn_date;
    //结算结束日 期
    private String setl_end_date;
    //个人自付
    private String psn_selfpay;
    //个人自费
    private String psn_ownpay;
    //个人账户支 出
    private String acct_pay;
    //个人现金支 付
    private String psn_cashpay;
    //医保支付方 式
    private String hi_paymtd;
    //医保机构
    private String hsorg;
    //医保机构经 办人
    private String hsorg_opter;
    //定点医疗机 构填报部门
    private String medins_fill_dept;
    //定点医疗机 构填报人
    private String medins_fill_psn;
    //X 病种收费名称+代码
    private String single_disease_name;
    //X 病种收费名称+代码 金额
    private String single_disease_amt;

    //X 病种收费 名称+代码甲类费用
    private String single_disease_claa_sumfee;

    //X 病种收费名称+代码 乙类金额
    private String single_disease_clab_amt;
    //自费金额
    private String single_disease_fulamt_ownpay_amt;
    //X 病种收费名称+代码其他金额
    private String single_disease_oth_amt;


}
