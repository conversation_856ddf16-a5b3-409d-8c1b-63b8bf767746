package com.my.som.vo.medicalQuality;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class AllErrorVo {
    /**编码*/
    private String id;
    /**完整性错误*/
    private String completeError;
    /**所有完整性错误*/
    private String allCompleteError;
    /**逻辑性错误*/
    private String logicError;
    /**所有完整性错误*/
    private String allLogicError;
    /**完整性错误数量*/
    private Integer completeErrorNum;
    /**逻辑性错误数量*/
    private Integer logicErrorNum;
    /**数量*/
    private String resNumber;
}
