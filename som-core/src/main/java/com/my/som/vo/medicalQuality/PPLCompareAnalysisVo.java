package com.my.som.vo.medicalQuality;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * @author: zyd
 * @Date 2021/9/23 2:41 下午
 * @Version 1.0
 * @Description: 人员信息对比
 */
@Getter
@Setter
public class PPLCompareAnalysisVo {

    /** id */
    private String id;

    /** 病案号 */
    private String patientId;

    /** 名称 */
    private String name;

    /** 性别 */
    private String gend;

    /** 年龄 */
    private String age;

    /** 科室名称 */
    private String deptName;

    /** 入院时间 */
    private String inHosTime;

    /** 出院时间 */
    private String outHosTime;

    /** 入院科室 */
    private String indeptname;

    /** 出院科室 */
    private String outdeptname;

    /** 实际住院天数*/
    private  String inHosdays;

    /** drg编码 */
    private String  drgcode;

    /** drg名称 */
    private String  drgname;

    /** 平均住院天数(区域) */
    private BigDecimal  dipareaStandardIndays;

    /** 平均住院天数(级别) */
    private BigDecimal  diplevelStandardIndays;

    /** 住院天数差异 */
    private String  drgindaycy;

    /** dip编码 */
    private String  dipcode;

    /** dip名称 */
    private String  dipname;

    /** dip住院天数差异(区域) */
    private BigDecimal  dipindayqycy;

    /** dip住院天数差异(级别) */
    private BigDecimal  dipindayjbcy;

    /** 预付drg费用*/
    private String  predictCost;

    /** 总费用*/
    private String  zong;

    /** 差异*/
    private String  drgcy;

    /** dip预付金额*/
    private String  dippredictCost;

    /** 总费用*/
    private String  dipzong;

    /** dip支付差异*/
    private String  dipcy;

    /** 床位费 */
    private String cwf;

    /** 诊查费 */
    private String zcf;

    /** 检查费 */
    private String jcf;

    /** 化验费 */
    private String hyf;

    /** 治疗费 */
    private String treat_fee;

    /** 手术费 */
    private String ssf;

    /** 护理费 */
    private String nursfee;

    /** 卫生材料费 */
    private String wsclf ;

    /** 西药费 */
    private String west_fee;

    /** 中药饮片费 */
    private String zyypf;

    /** 中成药 */
    private String zcy;

    /** 一般治疗费 */
    private String ybzlf;

    /** 挂号费 */
    private String ghf ;

    /** 其他费 */
    private String qt;

    /** dip标杆费用(区域) */
    private BigDecimal dipareaStandardCost;

    /** dip标杆费用(级别) */
    private BigDecimal diplevelStandardCost;

    /** drg标杆费用 */
    private BigDecimal drgareaStandardCost;

    /** dip平均住院费用*/
    private String dipavgcost;

    /** drg平均住院费用*/
    private BigDecimal drgavgcost;

    /** dip差异比例*/
    private String dipcybl;

    /** drg差异比例*/
    private String drgcybl;

    /** dip费用差异(区域)*/
    private  BigDecimal dipqyfycy;

    /** dip费用差异(级别)*/
    private  BigDecimal dipjbfycy;

    /** drg费用差异*/
    private  String drgfycy;

    /** drg标杆住院天数 */
    private BigDecimal drgindayavg;

    /** 参保类型 */
    private String insuredType;




}
