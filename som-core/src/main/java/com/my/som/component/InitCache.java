package com.my.som.component;

import com.my.som.service.dictmenagement.DictMenagementService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

/**
 * @author: zyd
 * Case:          drg
 * FileName:      InitCache
 * Date:          2020/1/10 17:14
 * Description:   描述
 * Ver:       1.0
 * Copyright (C),2012-2022 Freedom
 */
@Component
public class InitCache implements ApplicationRunner {
    @Autowired
    private DictMenagementService dictMenagementService;

    @Override
    @Async
    public void run(ApplicationArguments args) throws Exception {
        dictMenagementService.initDictCache();
        dictMenagementService.initSettleListDictCache();
        dictMenagementService.initChargeDetail();
    }
}
