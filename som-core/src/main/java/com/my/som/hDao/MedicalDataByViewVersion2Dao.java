package com.my.som.hDao;

import com.my.som.common.constant.DrgConst;
import com.my.som.common.util.JDBCUtil;
import com.my.som.common.util.ValidateUtil;
import com.my.som.model.dataHandle.SomDataView;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class MedicalDataByViewVersion2Dao {

    //判断系统的有效性
    public Integer queryA01CountByMysqlCj(Map map, SomDataView somDataView) throws Exception {
        //需要查询的sql
        String sql = getA01CountInfo01Sql(somDataView.getMedcas());
        //无填充参数
        String[] args = null;
        return ((Long) JDBCUtil.selectOne(map,sql,args)).intValue();
    }

    //病案统计数据
    public Integer queryMedicalCountByMysqlCj(Map queryParam, Map map, SomDataView somDataView) throws Exception {
        //需要查询的sql
        String sql = getCountInfo01Sql(queryParam, DrgConst.DRG_DATASOURCE_TYPE_MYSQL_CJ,somDataView.getMedcas());
        //填充参数
        String[] args = getInfo01Args(queryParam);
        return ((Long) JDBCUtil.selectOne(map,sql,args)).intValue();
    }

    //门诊慢特编码总数
    public Integer queryOutClinicCodeCountByMysqlCj(Map queryParam, Map map, SomDataView somDataView) throws Exception {
        //需要查询的sql
        String sql = getCountInfo01Sql(queryParam, DrgConst.DRG_DATASOURCE_TYPE_MYSQL_CJ,somDataView.getOtpSlowSpecialDataViewName());
        //填充参数
        String[] args = getInfo01Args(queryParam);
        return ((Long) JDBCUtil.selectOne(map,sql,args)).intValue();
    }

    //疾病诊断编码总数
    public Integer queryDieaseCodeCountByMysqlCj(Map queryParam, Map map, SomDataView somDataView) throws Exception {
        //需要查询的sql
        String sql = getCountInfo01Sql(queryParam, DrgConst.DRG_DATASOURCE_TYPE_MYSQL_CJ,somDataView.getDisediagDataViewName());
        //填充参数
        String[] args = getInfo01Args(queryParam);
        return ((Long) JDBCUtil.selectOne(map,sql,args)).intValue();
    }

    //手术总例数
    public Integer queryOperateCountByMysqlCj(Map queryParam, Map map, SomDataView somDataView) throws Exception {
        //需要查询的sql
        String sql = getCountInfo01Sql(queryParam, DrgConst.DRG_DATASOURCE_TYPE_MYSQL_CJ,somDataView.getOprnInfoDataViewName());
        //填充参数
        String[] args = getInfo01Args(queryParam);
        return ((Long) JDBCUtil.selectOne(map,sql,args)).intValue();
    }

    //清单医疗费用总数
    public Integer queryMedicalCostCountByMysqlCj(Map queryParam, Map map, SomDataView somDataView) throws Exception {
        //需要查询的sql
        String sql = getCountInfo01Sql(queryParam, DrgConst.DRG_DATASOURCE_TYPE_MYSQL_CJ,somDataView.getHiSetlInvyMedFeeItemInfoDataViewName());
        //填充参数
        String[] args = getInfo01Args(queryParam);
        return ((Long) JDBCUtil.selectOne(map,sql,args)).intValue();
    }

    //ICU记录数
    public Integer queryIcuCountByMysqlCj(Map queryParam, Map map, SomDataView somDataView) throws Exception {
        //需要查询的sql
        String sql = getCountInfo01Sql(queryParam, DrgConst.DRG_DATASOURCE_TYPE_MYSQL_CJ,somDataView.getScsIcuInfoDataViewName());
        //填充参数
        String[] args = getInfo01Args(queryParam);
        return ((Long) JDBCUtil.selectOne(map,sql,args)).intValue();
    }

    //基金支付记录数
    public Integer queryFundPayCountByMysqlCj(Map queryParam, Map map, SomDataView somDataView) throws Exception {
        //需要查询的sql
        String sql = getCountInfo01Sql(queryParam, DrgConst.DRG_DATASOURCE_TYPE_MYSQL_CJ,somDataView.getHiSetlInvyFundPayDataView());
        //填充参数
        String[] args = getInfo01Args(queryParam);
        return ((Long) JDBCUtil.selectOne(map,sql,args)).intValue();
    }

    //结算项目总例数
    public Integer queryProCountByMysqlCj(Map queryParam, Map map, SomDataView somDataView) throws Exception {
        //需要查询的sql
        String sql = getCountInfo01Sql(queryParam, DrgConst.DRG_DATASOURCE_TYPE_MYSQL_CJ,somDataView.getSetlDetlDataViewName());
        //填充参数
        String[] args = getInfo01Args(queryParam);
        return ((Long) JDBCUtil.selectOne(map,sql,args)).intValue();
    }

    //结算病案总例数
    public Integer queryBalanceMedicalCountByMysqlCj(Map queryParam, Map map, SomDataView somDataView) throws Exception {
        //需要查询的sql
        String sql = getCountInfo01Sql(queryParam, DrgConst.DRG_DATASOURCE_TYPE_MYSQL_CJ,somDataView.getSetlDataViewName());
        //填充参数
        String[] args = getInfo01Args(queryParam);
        return ((Long) JDBCUtil.selectOne(map,sql,args)).intValue();
    }

    private String getA01CountInfo01Sql(String viewName) {
        StringBuffer sb = new StringBuffer();
        sb.append(" SELECT COUNT(DISTINCT A01) FROM "+viewName);
        return sb.toString();
    }


    private String getCountInfo01Sql(Map queryParam,String type,String viewName) {
        StringBuffer sb = new StringBuffer();
        sb.append(" SELECT ")
                .append(" COUNT(1) ")
                .append(" FROM "+viewName)
                .append("  WHERE 1=1 ");
        if (queryParam != null)
            switch (viewName){
                case DrgConst.MEDCAS:getMedicineWhereSql(sb,queryParam,type);break;
                case DrgConst.OTP_SLOW_SPECIAL_DATA_VIEW_NAME:getCodeCountWhereSql(sb,queryParam,type);break;
                case DrgConst.DISEDIAG_DATA_VIEW_NAME:getCodeCountWhereSql(sb,queryParam,type);break;
                case DrgConst.OPRN_INFO_DATA_VIEW_NAME:getCodeCountWhereSql(sb,queryParam,type);break;
                case DrgConst.DRG_MEDICAL_COST_VIEW_NAME:getCodeCountWhereSql(sb,queryParam,type);break;
                case DrgConst.SCS_ICU_INFO_DATA_VIEW_NAME:getCodeCountWhereSql(sb,queryParam,type);break;
                case DrgConst.DRG_FUND_PAY_VIEW_NAME:getCodeCountWhereSql(sb,queryParam,type);break;
                case DrgConst.SETL_DATA_VIEW_NAME:getBalanceMedicineWhereSql(sb,queryParam,type);break;
                case DrgConst.DRG_BALANCE_PROJECT_VIEW_NAME:getProCountWhereSql(sb,queryParam,type);break;
            }
        return sb.toString();
    }

    private void getMedicineWhereSql(StringBuffer sb, Map queryParam,String type) {
        String begn_date = (String) queryParam.get("begn_date");
        String expi_date = (String) queryParam.get("expi_date");
        if (ValidateUtil.isNotEmpty(begn_date)){
            if (DrgConst.DRG_DATASOURCE_TYPE_ORACLE.equals(type)){
                sb.append(" and to_date(b15,'yyyy-MM-dd') >= to_date(?,'yyyy-MM-dd') ");
            }else {  //mysql查自己数据库
                sb.append(" and STR_TO_DATE(b15,'%Y-%m-%d') >= STR_TO_DATE(?,'%Y-%m-%d') ");
            }
        }
        if (ValidateUtil.isNotEmpty(expi_date)){
            if (DrgConst.DRG_DATASOURCE_TYPE_ORACLE.equals(type)){
                sb.append(" and to_date(b15,'yyyy-MM-dd') <= to_date(?,'yyyy-MM-dd')");
            }else {
                sb.append(" and STR_TO_DATE(b15,'%Y-%m-%d') <= STR_TO_DATE(?,'%Y-%m-%d')");
            }
        }
    }

    private void getCodeCountWhereSql(StringBuffer sb, Map queryParam,String type) {
        String begn_date = (String) queryParam.get("begn_date");
        String expi_date = (String) queryParam.get("expi_date");
        sb.append(" and k00 in (select k00 from "+DrgConst.MEDCAS +" where 1=1 ");
        if (ValidateUtil.isNotEmpty(begn_date)){
            if (DrgConst.DRG_DATASOURCE_TYPE_ORACLE.equals(type)){
                sb.append("and  to_date(b15,'yyyy-MM-dd') >= to_date(?,'yyyy-MM-dd') ");
            }else {  //mysql查自己数据库
                sb.append(" and STR_TO_DATE(b15,'%Y-%m-%d') >= STR_TO_DATE(?,'%Y-%m-%d') ");
            }
        }
        if (ValidateUtil.isNotEmpty(expi_date)){
            if (DrgConst.DRG_DATASOURCE_TYPE_ORACLE.equals(type)){
                sb.append(" and to_date(b15,'yyyy-MM-dd') <= to_date(?,'yyyy-MM-dd')");
            }else {
                sb.append(" and STR_TO_DATE(b15,'%Y-%m-%d') <= STR_TO_DATE(?,'%Y-%m-%d')");
            }
        }
        sb.append(" ) ");
    }

    private void getBalanceMedicineWhereSql(StringBuffer sb, Map queryParam,String type) {
        String begn_date = (String) queryParam.get("begn_date");
        String expi_date = (String) queryParam.get("expi_date");
        if (ValidateUtil.isNotEmpty(begn_date)){
            if (DrgConst.DRG_DATASOURCE_TYPE_ORACLE.equals(type)){
                sb.append(" and to_date(cysj,'yyyy-MM-dd') >= to_date(?,'yyyy-MM-dd') ");
            }else {  //mysql查自己数据库
                sb.append(" and STR_TO_DATE(cysj,'%Y-%m-%d') >= STR_TO_DATE(?,'%Y-%m-%d') ");
            }
        }
        if (ValidateUtil.isNotEmpty(expi_date)){
            if (DrgConst.DRG_DATASOURCE_TYPE_ORACLE.equals(type)){
                sb.append(" and to_date(cysj,'yyyy-MM-dd') <= to_date(?,'yyyy-MM-dd')");
            }else {  //mysql查自己数据库
                sb.append(" and STR_TO_DATE(cysj,'%Y-%m-%d') <= STR_TO_DATE(?,'%Y-%m-%d') ");
            }
        }
    }

    private void getProCountWhereSql(StringBuffer sb, Map queryParam,String type) {
        String begn_date = (String) queryParam.get("begn_date");
        String expi_date = (String) queryParam.get("expi_date");
        if (ValidateUtil.isNotEmpty(begn_date)){
            if (DrgConst.DRG_DATASOURCE_TYPE_ORACLE.equals(type)){
                sb.append(" and to_date(DSCG_TIME,'yyyy-MM-dd') >= to_date(?,'yyyy-MM-dd') ");
            }else {
                sb.append(" and DSCG_TIME >= ? ");
            }
        }
        if (ValidateUtil.isNotEmpty(expi_date)){
            if (DrgConst.DRG_DATASOURCE_TYPE_ORACLE.equals(type)){
                sb.append(" and to_date(DSCG_TIME,'yyyy-MM-dd') <= to_date(?,'yyyy-MM-dd')");
            }else {
                sb.append(" and DSCG_TIME <= ? ");
            }
        }
    }

    private String[] getInfo01Args(Map queryParam) {
        if (queryParam != null){
            List argsList = new ArrayList();
            String begn_date = (String) queryParam.get("begn_date");
            String expi_date = (String) queryParam.get("expi_date");
            if (ValidateUtil.isNotEmpty(begn_date)){
                argsList.add(queryParam.get("begn_date"));
            }
            if (ValidateUtil.isNotEmpty(expi_date)){
                argsList.add(expi_date);
            }
            return getStrings(argsList);
        }
        return null;
    }
    private String[] getStrings(List argsList) {
        if (argsList!=null &&argsList.size() > 0){
            String[] array =new String[argsList.size()];
            argsList.toArray(array);
            return array;
        }
        return null;
    }

    //病案统计数据
    public List queryMedicalDataByMysqlCj(Map queryParam, Map map, SomDataView currentView) throws Exception {
        //需要查询的sql
        String sql = getMedicalInfo02Sql(queryParam, DrgConst.DRG_DATASOURCE_TYPE_MYSQL_CJ,currentView);
        //填充参数
        String[] args = getInfo01Args(queryParam);
        return JDBCUtil.select(map,sql,args, (Integer) queryParam.get("pageBegin"),(Integer) queryParam.get("pageEnd"));
    }

    private String getMedicalInfo02Sql(Map queryParam,String type, SomDataView currentView) {
        StringBuffer sb = new StringBuffer();
        sb.append(" SELECT  DISTINCT")
                .append(" a.K00 as k00  ,   ") /* 病案唯一标识ID     */
                .append(" a.a47 as a47  ,   ") /* 健康卡号     */
                .append(" a.a01 as a01,   ") /* 组织机构代码     */
                .append(" a.a02 as a02,   ") /* 机构名称  */
                .append(" a.a46c as a46c, ")/* 医疗付款方式 */
                .append(DrgConst.CURRENT_MEDICAL_TYPE+" as a03,   ") /* 病案类型（1：西医，2：中医）     */
                .append(" a.a49   as a49  ,   ") /* 住院次数     */
                .append(" a.a48    as a48   ,   ") /* 病案号       */
                .append(" a.a11     as a11    ,   ") /* 姓名         */
                .append(" a.a12c     as a12c    ,   ") /* 性别         */
                .append(" a.a13    as a13  ,   ") /* 出生日期     */
                .append(" a.a14     as a14    ,   ") /* 年龄         */
                .append(" a.a15c     as a15c    ,   ") /* 国籍         */
                .append(" a.a16 as a16,   ") /* (年龄不足1周岁的)年龄(月) */
                .append(" a.a18 as a18,   ") /* 新生儿出生体重(克) */
                .append(" a.a17 as a17,   ") /* 新生儿入院体重(克） */
                .append(" a.a22    as a22   ,   ") /* 出生地       */
                .append(" a.a23c     as a23c    ,   ") /* 籍贯         */
                .append(" a.a19c     as a19c    ,   ") /* 民族         */
                .append(" a.a20   as a20  ,   ") /* 身份证号     */
                .append(" a.a38c     as a38c    ,   ") /* 职业         */
                .append(" a.a21c     as a21c    ,   ") /* 婚姻         */
                .append(" a.a26    as a26   ,   ") /* 现住址       */
                .append(" a.a27     as a27    ,   ") /* 电话         */
                .append(" a.a28c    as a28c   ,   ") /* 邮编         */
                .append(" a.a24   as a24  ,   ") /* 户口地址     */
                .append(" a.a25c    as a25c   ,   ") /* 邮编         */
                .append(" a.a29 as a29,   ") /* 工作单位及地址 */
                .append(" a.a30   as a30  ,   ") /* 单位电话     */
                .append(" a.a31c    as a31c   ,   ") /* 邮编         */
                .append(" a.a32  as a32 ,   ") /* 联系人姓名   */
                .append(" a.a33c     as a33c    ,   ") /* 关系         */
                .append(" a.a34     as a34    ,   ") /* 地址         */
                .append(" a.a35    as a35   ,   ") /* 电话         */
                .append(" a.a29n as a29n ,     ") /* 工作单位名称 */
                .append(" a.a50 as a50 ,     ") /* 医保结算等级 */
                .append(" a.a51 as a51 ,     ") /* 医保编号 */
                .append(" a.a52 as a52 ,     ") /* 申报时间 */
                .append(" a.a53 as a53 ,     ") /* 患者证件类型 */
                .append(" a.a54 as a54 ,     ") /* 医保类型 */
                .append(" a.a55 as a55 ,     ") /* 特殊人员类型 */
                .append(" a.a56 as a56 ,     ") /* 参保地 */
                .append(" a.a57 as a57 ,     ") /* 新生儿入院类型 */
                .append(" a.a58 as a58 ,     ") /* 结算清单流水号 */
                .append(" a.b11c   as b11c  ,   ") /* 入院途径     */
                .append(" a.b12   as b12  ,   ") /* 入院时间     */
                .append(" a.b12s  as b12s  ,   ") /* 时     */
                .append(" a.b13c   as b13c  ,   ") /* 入院科别     */
                .append(" a.b13n     as b13n ,     ") /* 入院科别名称 */
                .append(" a.b14n   as b14n  ,   ") /* 入院病房     */
                .append(" a.b14c   as b14c  ,   ") /* 入院病房     */
                .append(" a.b21c   as b21c  ,   ") /* 转科科别     */
                .append(" a.b15   as b15  ,   ") /* 出院时间     */
                .append(" a.b15s  as b15s  ,   ") /* 时     */
                .append(" a.b16c   as b16c  ,   ") /* 出院科别     */
                .append(" a.b16n     as b16n ,     ") /* 出院科别名称 */
                .append(" a.b17c   as b17c  ,   ") /* 出院病房     */
                .append(" a.b17n   as b17n  ,   ") /* 出院病房     */
                .append(" a.b20 as b20,   ") /* 实际住院(天) */
                .append(" a.b51c as b51c ,     ") /* 主诊医师代码 */
                .append(" a.b52n as b52n ,     ") /* 主诊医师姓名 */
                .append(" a.b22c    as b22c   ,   ") /* 科主任代码       */
                .append(" a.b22n    as b22n   ,   ") /* 科主任       */
                .append(" a.b23c   as b23c  ,   ") /* 主任（副主任）医师代码 */
                .append(" a.b23n   as b23n  ,   ") /* 主任（副主任）医师 */
                .append(" a.b24c   as b24c  ,   ") /* 主治医师代码 */
                .append(" a.b24n   as b24n  ,   ") /* 主治医师 */
                .append(" a.b25c   as b25c  ,   ") /* 住院医师代码 */
                .append(" a.b25n   as b25n  ,   ") /* 住院医师 */
                .append(" a.b26c   as b26c  ,   ") /* 责任护士代码     */
                .append(" a.b26n   as b26n  ,   ") /* 责任护士     */
                .append(" a.b27c   as b27c  ,   ") /* 进修医师代码   */
                .append(" a.b27n   as b27n  ,   ") /* 进修医师   */
                .append(" a.b28   as b28  ,   ") /* 实习医师     */
                .append(" a.b29c    as b29c   ,   ") /* 编码员代码       */
                .append(" a.b29n    as b29n   ,   ") /* 编码员       */
                .append(" a.b30c   as b30c  ,   ") /* 病案质量     */
                .append(" a.b31c   as b31c  ,   ") /* 质控医师代码     */
                .append(" a.b31n   as b31n  ,   ") /* 质控医师     */
                .append(" a.b32c   as b32c  ,   ") /* 质控护士代码     */
                .append(" a.b32n   as b32n  ,   ") /* 质控护士     */
                .append(" a.b33   as b33  ,   ") /* 质控日期     */
                .append(" a.b34c   as b34c  ,   ") /* 离院方式     */
                .append(" a.b49 as b49,   ") /* 医嘱转院，拟接收医疗机构名称 */
                .append(" a.b36c as b36c,   ") /* 是否有出院31天内再住院计划手术情况 */
                .append(" a.b37     as b37    ,   ") /* 目的         */
                .append(" a.b38 as b38 ,     ") /* 住院医疗类型 */
                .append(" a.b39 as b39 ,     ") /* 治疗类别 */
                .append(" a.b44 as b44 ,     ") /* 特级护理天数 */
                .append(" a.b45 as b45 ,     ") /* 一级护理天数 */
                .append(" a.b46 as b46 ,     ") /* 二级护理天数 */
                .append(" a.b47 as b47 ,     ") /* 三级护理天数 */
                .append(" a.b48 as b48 ,     ") /* 转院拟接受机构代码 */
                .append(" a.c02n   as c02n  ,   ") /* 门(急)诊诊断 */
                .append(" a.c01c   as c01c  ,   ") /* 疾病编码     */
                .append(" a.c04n   as c04n  ,   ") /* 主要诊断     */
                .append(" a.c03c   as c03c  ,   ") /* 疾病编码     */
                .append(" a.c05c   as c05c  ,   ") /* 入院病情     */
                .append(" a.c13n   as c13n  ,   ") /* 中毒的外部原因 */
                .append(" a.c12c   as c12c   ,   ") /* 中毒外部原因疾病编码     */
                .append(" a.c10n   as c10n  ,   ") /* 病理诊断名称   */
                .append(" a.c09c   as c09c  ,   ") /* 病理诊断出疾病编码     */
                .append(" a.c11    as c11   ,   ") /* 病理号       */
                .append(" a.c24c   as c24c  ,   ") /* 药物过敏     */
                .append(" a.c25   as c25  ,   ") /* 过敏药物疾病 */
                .append(" a.c34c as c34c,   ") /* 死亡患者尸检 */
                .append(" a.c26c     as c26c    ,   ") /* 血型         */
                .append(" a.c27c     as c27c    ,   ") /* Rh           */
                .append(" a.c14x01c  as c14x01c,   ") /* 手术及操作编码 */
                .append(" a.c16x01  as c16x01,   ") /* 手术及操作日期 */
                .append(" a.c17x01   as c17x01 ,   ") /* 手术级别     */
                .append(" a.c15x01n  as c15x01n,   ") /* 手术及操作名称 */
                .append(" a.c18x01    as c18x01   ,   ") /* 术者         */
                .append(" a.c19x01    as c19x01   ,   ") /* I助          */
                .append(" a.c20x01    as c20x01   ,   ") /* II助         */
                .append(" a.c21x01c  as c21x01c ,   ") /* 切口等级     */
                .append(" a.c22x01c  as c22x01c ,   ") /* 麻醉方式     */
                .append(" a.c23x01  as c23x01 ,   ") /* 麻醉医师     */
                .append(" a.c28  as c28 ,   ") /* 颅脑损伤患者昏迷入院前时间：天 */
                .append(" a.c29 as c29,   ") /* 颅脑损伤患者昏迷入院前时间：小时 */
                .append(" a.c30  as c30 ,   ") /* 颅脑损伤患者昏迷入院前时间：分 */
                .append(" a.c31  as c31 ,   ") /* 颅脑损伤患者昏迷入院后时间：天 */
                .append(" a.c32 as c32,   ") /* 颅脑损伤患者昏迷入院后时间：小时 */
                .append(" a.c33  as c33 ,   ") /* 颅脑损伤患者昏迷入院后时间：分 */
                .append(" c35c as c35c ,     ") /* 门（急）诊诊断编码（中医） */
                .append(" c36n as c36n ,     ") /* 门（急）诊诊断名称（中医） */
                .append(" c37c as c37c ,     ") /* 出院主要诊断编码（中医） */
                .append(" c38n as c38n ,     ") /* 出院主要诊断名称（中医） */
                .append(" c39c as c39c ,     ") /* 出院主要诊断入院病情（中医） */
                .append(" c42 as c42 ,     ") /* 呼吸机使用时间（天） */
                .append(" c43 as c43 ,     ") /* 呼吸机使用时间（小时） */
                .append(" c44 as c44 ,     ") /* 呼吸机使用时间（分钟） */
                .append(" c45 as c45 ,     ") /* 输血品种 */
                .append(" c46 as c46 ,     ") /* 输血量 */
                .append(" c47 as c47 ,     ") /* 输血计量单位 */
                .append(" c48c as c48c ,     ") /* 出院主要诊断出院病情（西医） */
                .append(" c49c as c49c ,     ") /* 出院主要诊断出院病情（中医） */
                .append(" a.d01    as d01   ,   ") /* 住院费用(元)：总费用 */
                .append(" a.d09   as d09  ,   ") /* 自付金额     */
                .append(" a.d11  as d11 ,   ") /* 综合医疗服务类：(1)一般医疗服务费 */
                .append(" a.d12  as d12 ,   ") /* 一般治疗操作费 */
                .append(" a.d13    as d13   ,   ") /* 护理费住院费 */
                .append(" a.d14   as d14  ,   ") /* 其他费用     */
                .append(" a.d15  as d15 ,   ") /* 诊断类：(5)病理诊断费 */
                .append(" a.d16 as d16,   ") /* 实验室诊断费 */
                .append(" a.d17 as d17,   ") /* 影像学诊断费 */
                .append(" a.d18 as d18,   ") /* 临床诊断项目费 */
                .append(" a.d19 as d19,   ") /* 治疗类：(9)非手术治疗项目费 */
                .append(" a.d19x01  as d19x01 ,   ") /* 临床物理治疗费 */
                .append(" a.d20  as d20 ,   ") /* 手术治疗费   */
                .append(" a.d20x01    as d20x01   ,   ") /* 麻醉费       */
                .append(" a.d20x02    as d20x02   ,   ") /* 手术费       */
                .append(" a.d21    as d21   ,   ") /* 康复类：(11)康复费 */
                .append(" a.d22  as d22 ,   ") /* 中医类:(12)中医治疗费 */
                .append(" a.d23    as d23   ,   ") /* 西药类:(13)西药费 */
                .append(" a.d23x01  as d23x01 ,   ") /* 抗菌药物费   */
                .append(" a.d24   as d24  ,   ") /* 中药类:(14)中成药费 */
                .append(" a.d25  as d25 ,   ") /* 中草药费     */
                .append(" a.d26     as d26    ,   ") /* 血液和血液制品类:(16)血费 */
                .append(" a.d27 as d27,   ") /* 白蛋白类制品费 */
                .append(" a.d28 as d28,   ") /* 球蛋白类制品费 */
                .append(" a.d29 as d29,   ") /* 凝血因子类制品费 */
                .append(" a.d30 as d30,   ") /* 细胞因子类制品费 */
                .append(" a.d31 as d31,   ") /* 耗材类:(21)检查用一次性医用材料费 */
                .append(" a.d32  as d32 ,   ") /* (22)治疗用一次性医用材料费 */
                .append(" a.d33 as d33,   ") /* (23)手术用一次性医用材料费 */
                .append(" a.d34 as d34 ,     ") /* 其他类：(24)其他费 */
                .append(" d35 as d35 ,     ") /* 业务流水号 */
                .append(" d36 as d36 ,     ") /* 结算开始时间 */
                .append(" d37 as d37 ,     ") /* 结算结束时间 */
                .append(" d38 as d38 ,     ") /* 票据代码 */
                .append(" d39 as d39 ,     ") /* 票据号 */
                .append(" d54 as d54 ,     ") /* 个人自付 */
                .append(" d55 as d55 ,     ") /* 个人自费 */
                .append(" d56 as d56 ,     ") /* 个人账户支付 */
                .append(" d57 as d57 ,     ") /* 个人现金支付 */
                .append(" d58 as d58 ,     ") /* 医保支付方式 */
                .append(" d59 as d59 ,     ") /* 医疗机构填报部门 */
                .append(" d60 as d60 ,     ") /* 医疗机构填报人 */
                .append(" d61 as d61 ,     ") /* 中医辨证论治费 */
                .append(" d62 as d62 ,     ") /* 中医辨证论治会诊费 */
                .append(" d63 as d63 ,     ") /* 中医类(中医和名族医医疗服务)中医诊断 */
                .append(" d64 as d64 ,     ") /* 中医其他 */
                .append(" d65 as d65 ,     ") /* 中医特殊调配加工 */
                .append(" d66 as d66 ,     ") /* 辨证施膳 */
                .append(" d67 as d67 ,     ") /* 医疗机构中药制剂费 */
                .append(" d68 as d68 ,     ") /* 针刺与灸法 */
                .append(" d69 as d69 ,     ") /* 中医肛肠治疗 */
                .append(" d70 as d70 ,     ") /* 中医骨伤 */
                .append(" d71 as d71 ,     ") /* 中医推拿治疗 */
                .append(" d72 as d72 ,     ") /* 中医特殊治疗 */
                .append(" d73 as d73       ") /* 中医外治 */
                .append(" FROM "+ currentView.getMedcas() +" a ")
                .append(" WHERE 1=1 ");
        if (queryParam != null)
            getMedicineWhereSql(sb,queryParam,type);
        return sb.toString();
    }

    //病案门诊慢特数据
    public List queryOutClinicDataByMysqlCj(Map queryParam, Map map, SomDataView currentView) throws Exception {
        //需要查询的sql
        String sql = getMedicalInfo03Sql(queryParam, DrgConst.DRG_DATASOURCE_TYPE_MYSQL_CJ,currentView);
        //填充参数
        String[] args = getInfo01Args(queryParam);
        return JDBCUtil.select(map,sql,args, (Integer) queryParam.get("pageBegin"),(Integer) queryParam.get("pageEnd"));
    }

    private String getMedicalInfo03Sql(Map queryParam,String type, SomDataView currentView) {
        StringBuffer sb = new StringBuffer();
        sb.append(" SELECT DISTINCT")
                .append(" k00 as k00  , ") /* 病案唯一标识ID     */
                .append(" diag_code as diag_code  , ") /* 诊断代码     */
                .append(" diag_name as diag_name  , ") /* 诊断名称     */
                .append(" oprn_oprt_code as oprn_oprt_code  , ") /* 手术及操作代码     */
                .append(" oprn_oprt_name as oprn_oprt_name  , ") /* 手术及操作名称     */
                .append(" dept_code as dept_code  , ") /* 诊断科别代码     */
                .append(" dept_name as dept_name  , ") /* 诊断科别名称     */
                .append(" mdtrt_date as mdtrt_date  ") /* 就诊日期     */
            .append(" FROM "+ currentView.getOtpSlowSpecialDataViewName())
            .append(" WHERE 1=1 ");
        if (queryParam != null)
            getCodeCountWhereSql(sb,queryParam,type);
        return sb.toString();
    }

    //病案疾病数据
    public List queryDiseaseDataByMysqlCj(Map queryParam, Map map, SomDataView currentView) throws Exception {
        //需要查询的sql
        String sql = getMedicalInfo04Sql(queryParam, DrgConst.DRG_DATASOURCE_TYPE_MYSQL_CJ,currentView);
        //填充参数
        String[] args = getInfo01Args(queryParam);
        return JDBCUtil.select(map,sql,args, (Integer) queryParam.get("pageBegin"),(Integer) queryParam.get("pageEnd"));
    }

    private String getMedicalInfo04Sql(Map queryParam,String type, SomDataView currentView) {
        StringBuffer sb = new StringBuffer();
        sb.append(" SELECT DISTINCT " )
                .append(" k00 as k00  , ") /* 病案唯一标识ID     */
                .append(" seq as seq  , ") /* 顺序号（从0开始，0：主要诊断，大于等于1：其他诊断）     */
                .append(" type as type  , ") /* 诊断类型（1：西医诊断，2：中医诊断）     */
                .append(" dscg_diag_codg as dscg_diag_codg  , ") /* 出院诊断编码     */
                .append(" dscg_diag_name as dscg_diag_name  , ") /* 出院诊断名称     */
                .append(" dscg_diag_adm_cond as dscg_diag_adm_cond  , ") /* 出院诊断入院病情     */
                .append(" dscg_diag_dscg_cond as dscg_diag_dscg_cond   ") /* 出院诊断出院病情     */
            .append(" FROM "+ currentView.getDisediagDataViewName())
            .append(" WHERE 1=1 ");
        if (queryParam != null)
            getCodeCountWhereSql(sb,queryParam,type);
        return sb.toString();
    }

    //病案手术数据
    public List queryOperateDataByMysqlCj(Map queryParam, Map map, SomDataView currentView) throws Exception {
        //需要查询的sql
        String sql = getMedicalInfo05Sql(queryParam, DrgConst.DRG_DATASOURCE_TYPE_MYSQL_CJ,currentView);
        //填充参数
        String[] args = getInfo01Args(queryParam);
        return JDBCUtil.select(map,sql,args, (Integer) queryParam.get("pageBegin"),(Integer) queryParam.get("pageEnd"));
    }

    //病案重症监护室数据
    public List queryIcuDataByMysqlCj(Map queryParam, Map map, SomDataView currentView) throws Exception {
        //需要查询的sql
        String sql = getMedicalInfo07Sql(queryParam, DrgConst.DRG_DATASOURCE_TYPE_MYSQL_CJ,currentView);
        //填充参数
        String[] args = getInfo01Args(queryParam);
        return JDBCUtil.select(map,sql,args, (Integer) queryParam.get("pageBegin"),(Integer) queryParam.get("pageEnd"));
    }

    //病案医保结算清单医疗费用数据
    public List queryMedicalCostDataByMysqlCj(Map queryParam, Map map, SomDataView currentView) throws Exception {
        //需要查询的sql
        String sql = getMedicalInfo08Sql(queryParam, DrgConst.DRG_DATASOURCE_TYPE_MYSQL_CJ,currentView);
        //填充参数
        String[] args = getInfo01Args(queryParam);
        return JDBCUtil.select(map,sql,args, (Integer) queryParam.get("pageBegin"),(Integer) queryParam.get("pageEnd"));
    }

    //病案医保结算清单基金支付数据
    public List queryFundPayDataByMysqlCj(Map queryParam, Map map, SomDataView currentView) throws Exception {
        //需要查询的sql
        String sql = getMedicalInfo09Sql(queryParam, DrgConst.DRG_DATASOURCE_TYPE_MYSQL_CJ,currentView);
        //填充参数
        String[] args = getInfo01Args(queryParam);
        return JDBCUtil.select(map,sql,args, (Integer) queryParam.get("pageBegin"),(Integer) queryParam.get("pageEnd"));
    }

    private String getMedicalInfo05Sql(Map queryParam,String type, SomDataView currentView) {
        StringBuffer sb = new StringBuffer();
        sb.append(" SELECT DISTINCT ")
                .append(" k00 as k00  , ") /* 病案唯一标识ID     */
                .append(" seq as seq  , ") /* 顺序号（从0开始，0：主要操作，大于等于1：其他操作）     */
                .append(" c35c as c35c  , ") /* 手术操作编码   */
                .append(" c36n as c36n  , ") /* 手术操作名称     */
                .append(" oprn_oprt_date as oprn_oprt_date  , ") /* 手术操作日期     */
                .append(" oprn_oprt_lv as oprn_oprt_lv  , ") /* 手术操作级别     */
                .append(" c39c as c39c ,  ") /* 手术操作术者代码     */
                .append(" oprn_oprt_oper_name as oprn_oprt_oper_name  , ") /* 手术操作术者姓名     */
                .append(" oprn_oprt_1_asit as oprn_oprt_1_asit  , ") /* 手术操作Ⅰ助     */
                .append(" oprn_oprt_2_asit as oprn_oprt_2_asit  , ") /* 手术操作Ⅱ助     */
                .append(" c42 as c42 ,  ") /* 手术操作切口愈合等级     */
                .append(" c43 as c43  , ") /* 手术操作麻醉方式     */
                .append(" oprn_oprt_anst_dr_code as oprn_oprt_anst_dr_code  , ") /* 手术操作麻醉医师代码     */
                .append(" oprn_oprt_anst_dr_name as oprn_oprt_anst_dr_name   ") /* 手术操作麻醉医师姓名     */
            .append(" FROM "+ currentView.getOprnInfoDataViewName())
            .append(" WHERE 1=1 ");
        if (queryParam != null)
            getCodeCountWhereSql(sb,queryParam,type);
        return sb.toString();
    }

    private String getMedicalInfo07Sql(Map queryParam,String type, SomDataView currentView) {
        StringBuffer sb = new StringBuffer();
        sb.append(" SELECT DISTINCT ")
                .append(" k00 as k00  , ") /* 病案唯一标识ID     */
                .append(" scs_cutd_ward_type as scs_cutd_ward_type  , ") /* 重症监护病房类型     */
                .append(" scs_cutd_inpool_time as scs_cutd_inpool_time  , ") /* 重症监护进入时间   */
                .append(" scs_cutd_exit_time as scs_cutd_exit_time  , ") /* 重症监护退出时间     */
                .append(" scs_cutd_sum_dura as scs_cutd_sum_dura   ") /* 重症监护合计时长     */
            .append(" FROM "+ currentView.getScsIcuInfoDataViewName())
            .append(" WHERE 1=1 ");
        if (queryParam != null)
            getCodeCountWhereSql(sb,queryParam,type);
        return sb.toString();
    }

    private String getMedicalInfo08Sql(Map queryParam,String type, SomDataView currentView) {
        StringBuffer sb = new StringBuffer();
        sb.append(" SELECT DISTINCT ")
                .append(" k00 as k00  , ") /* 病案唯一标识ID     */
                .append(" med_chrg_itemname as med_chrg_itemname  , ") /* 医疗收费项目名称     */
                .append(" amt as amt  , ") /* 金额   */
                .append(" claa as claa  , ") /* 甲类     */
                .append(" clab as clab ,   ") /* 乙类     */
                .append(" ownpay as ownpay ,   ") /* 自费     */
                .append(" oth as oth   ") /* 其他     */
                .append(" FROM "+ currentView.getHiSetlInvyMedFeeItemInfoDataViewName())
                .append(" WHERE 1=1 ");
        if (queryParam != null)
            getCodeCountWhereSql(sb,queryParam,type);
        return sb.toString();
    }

    private String getMedicalInfo09Sql(Map queryParam,String type, SomDataView currentView) {
        StringBuffer sb = new StringBuffer();
        sb.append(" SELECT DISTINCT ")
                .append(" k00 as k00  , ") /* 病案唯一标识ID     */
                .append(" fund_pay_type as fund_pay_type  , ") /* 基金支付类型     */
                .append(" pool_coty_fund_pay_type as pool_coty_fund_pay_type  , ") /* 统筹区基金支付类型   */
                .append(" pool_coty_fund_pay_type_name as pool_coty_fund_pay_type_name  , ") /* 统筹区基金支付类型名称     */
                .append(" fund_payamt as fund_payamt   ") /* 基金支付金额     */
                .append(" FROM "+ currentView.getHiSetlInvyFundPayDataView())
                .append(" WHERE 1=1 ");
        if (queryParam != null)
            getCodeCountWhereSql(sb,queryParam,type);
        return sb.toString();
    }

    //抽取结算项目信息
    public List queryBalanceProjectDataByMysqlCj(Map queryParam, Map map, SomDataView somDataView) throws Exception {
        //需要查询的sql
        String sql = getMedicalInfo06Sql(queryParam, DrgConst.DRG_DATASOURCE_TYPE_MYSQL_CJ, somDataView);
        //填充参数
        String[] args = getInfo01Args(queryParam);
        return JDBCUtil.select(map,sql,args, (Integer) queryParam.get("pageBegin"),(Integer) queryParam.get("pageEnd"));
    }

    private String getMedicalInfo06Sql(Map queryParam,String type,SomDataView somDataView) {
        StringBuffer sb = new StringBuffer();
        sb.append(" SELECT a.*,")
                //病案表上不存在的字段,唯一标识
                .append(" CONCAT(IFNULL(a.CITI_IDET_NO,''),IFNULL(a.PATIENT_ID,''),IFNULL(a.ADM_TIME,''),IFNULL(a.DSCG_TIME,'')) as k00  ") /* 病案唯一标识ID（主要在病案抽取中关联诊断和手术）,这里不能为空，我们通过身份证号、病案号、入院时间、出院时间拼接构造唯一标识     */
                .append(" FROM "+somDataView.getSetlDetlDataViewName() +" a")
                .append("  WHERE 1=1 ");
        if (queryParam != null)
            getProCountWhereSql(sb,queryParam,type);
        return sb.toString();
    }

    public static void main(String[] args) throws Exception {
        Map cmap = new HashMap();
        cmap.put("serv_ip_adrr","127.0.0.1");
        cmap.put("serv_port","3306");
        cmap.put("serv_inst_name","drg");
        cmap.put("ds_user_name","root");
        cmap.put("ds_pwd","8#_d150r26g9437s");
        cmap.put("ds_type",DrgConst.DRG_DATASOURCE_TYPE_MYSQL_CJ);
        Map queryParam = new HashMap();
        queryParam.put("pageBegin",0);
        queryParam.put("pageEnd",1000);
        queryParam.put("begn_date","2016-01-01");
        queryParam.put("expi_date","2020-10-31");

        MedicalDataByViewVersion2Dao dao = new MedicalDataByViewVersion2Dao();
        SomDataView somDataView = new SomDataView();
        somDataView.setMedcas("SOM_DRG_SETL_INVY_VIEW");
        System.out.println(dao.queryMedicalDataByMysqlCj(queryParam,cmap,somDataView));
    }
}
