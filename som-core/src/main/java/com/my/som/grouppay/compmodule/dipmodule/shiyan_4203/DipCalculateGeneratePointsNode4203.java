package com.my.som.grouppay.compmodule.dipmodule.shiyan_4203;


import com.my.som.common.constant.DrgConst;
import com.my.som.common.util.ValidateUtil;
import com.my.som.grouppay.vo.HospBaseBusiVo;
import com.my.som.vo.dataHandle.PayToPredict.PayToPredictVo;
import com.my.som.vo.dipBusiness.DipPayToPredictVo;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import com.yomahub.liteflow.core.NodeComponent;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.List;

@LiteflowComponent(value = "DipCalculateGeneratePointsNode4203", name = "分值计算")
public class DipCalculateGeneratePointsNode4203 extends NodeComponent {
    @Override
    public void process() throws Exception {
        // 获取业务上下文
        HospBaseBusiVo hospBaseBusiVo = this.getContextBean(HospBaseBusiVo.class);
        List<DipPayToPredictVo> payToPredictVoList = hospBaseBusiVo.getDipPayToPredictVoList();

        for (DipPayToPredictVo payToPredictVo : payToPredictVoList) {
            String paymentDiseType = payToPredictVo.getPaymentDiseType();
            BigDecimal score = BigDecimal.ZERO;
            BigDecimal referSco = payToPredictVo.getRefer_sco();
            BigDecimal adjmCof = payToPredictVo.getAdjm_cof();
            //超高超低偏差
            BigDecimal biasRateFactor = payToPredictVo.getBiasRateFactor();
            BigDecimal asstListAdm = payToPredictVo.getAsstListAdm();


            switch (paymentDiseType) {
                case DrgConst.DISETYPE_CORE: // 核心病种
                    //基础分值 * 病种级别系数 * 辅助目录调节系数 * 超高（超低）病例系数
                    // 计算核心病种的总分值
                    score = calculateScore(referSco, adjmCof, biasRateFactor, asstListAdm);
                    break;

                case DrgConst.DISETYPE_COMPOSITE: // 综合病种
                    //基础分值 * 病种级别系数 * 超高（超低）病例系数
                    // 计算综合病种的总分值
                    score = calculateScore(referSco, adjmCof, biasRateFactor);
                    break;

                case DrgConst.DISETYPE_BASIC: // 基层病种
                  //  基础分值 * 0.9
                    // 计算基层病种的总分值
                    BigDecimal grstCof = new BigDecimal(0.9);
                    payToPredictVo.setGrstCof(grstCof);
                    score = referSco.multiply(grstCof).setScale(4, BigDecimal.ROUND_HALF_UP);
                    break;

                case DrgConst.DISETYPE_CHM: // 中医病种
                    //基础分值 * 病种级别系数 * 中医加成系数
                    // 计算中医病种的总分值
                    BigDecimal tcmAdtCof = payToPredictVo.getAsstListAdm();
                    payToPredictVo.setTcmAdtCof(tcmAdtCof);
                    score = calculateScore(referSco, adjmCof, tcmAdtCof);
                    break;

                case DrgConst.DISETYPE_NONE_INGROUP: // 未入组
                    // 计算未入组病种的总分值
                    score = payToPredictVo.getInHosTotalCost()
                            .multiply(new BigDecimal(0.8))
                            .multiply(new BigDecimal(1000))
                            .divide(hospBaseBusiVo.getRegiAverageFee(), BigDecimal.ROUND_HALF_UP)
                            .setScale(4, BigDecimal.ROUND_HALF_UP);
                    break;
                case DrgConst.DISETYPE_EXTREMELYE_HIGH: // 未入组
                    // 计算未入组病种的总分值
                    score = payToPredictVo.getInHosTotalCost()
                            .multiply(new BigDecimal(1000))
                            .divide(hospBaseBusiVo.getRegiAverageFee(), BigDecimal.ROUND_HALF_UP)
                            .setScale(4, BigDecimal.ROUND_HALF_UP);
                    break;
//                case DrgConst.DISETYPE_NO_BENCHMARK: //无标杆
//                    //无标杆=总费用/标杆*1000
//                    if (!ValidateUtil.isEmpty(hospBaseBusiVo.getRegiAverageFee())) {
//                        score = payToPredictVo.getInHosTotalCost()
//                                .divide(hospBaseBusiVo.getRegiAverageFee(),BigDecimal.ROUND_HALF_UP)
//                                .multiply(new BigDecimal(1000))
//                                .setScale(4, BigDecimal.ROUND_HALF_UP);
//                    } else {
//                        score = new BigDecimal(BigInteger.ZERO);
//                    }
//                    break;

                default:
                    break;
            }

            // 设置计算后的分值和其他属性
            payToPredictVo.setTotlSco(score);
            payToPredictVo.setSettleMentScore(score);
            payToPredictVo.setAddItiveValue(BigDecimal.ZERO);
        }
    }

    /**
     * 计算病种分值
     *
     * @param referSco  基础分值
     * @param cofactors 可变参数系数列表
     * @return 计算后的分值
     */
    private BigDecimal calculateScore(BigDecimal referSco, BigDecimal... cofactors) {
        BigDecimal score = referSco;
        for (BigDecimal factor : cofactors) {
            if (factor != null) {
                score = score.multiply(factor);
            }
        }
        return score.setScale(4, BigDecimal.ROUND_HALF_UP);
    }
}
