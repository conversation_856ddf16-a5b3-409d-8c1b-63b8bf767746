package com.my.som.grouppay.compmodule.drgmodule.common.config;

import com.my.som.common.util.ValidateUtil;
import com.my.som.dto.pregroup.PreGroupDto;
import com.my.som.grouppay.vo.HospBaseBusiVo;
import com.my.som.mapper.dataHandle.SomDrgStandardMapper;
import com.my.som.model.dataHandle.SomDrgStandard;
import com.my.som.model.dataHandle.SomDrgStandardExample;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import com.yomahub.liteflow.core.NodeComponent;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

@LiteflowComponent(value = "DrgPayStandardsInfoNode", name = "获取DRG标杆")
public class DrgPayStandardsInfoNode extends NodeComponent {

    @Autowired
    private SomDrgStandardMapper busBenchmarkMapper;

    @Override
    public void process() throws Exception {
        // 1.获取业务参数
        PreGroupDto preGroupDto = this.getRequestData();
        // 2.获取业务对象
        HospBaseBusiVo hospBaseBusiVo = this.getContextBean(HospBaseBusiVo.class);
        SomDrgStandardExample busBenchmarkExample = getQueryBenchmarkDto(preGroupDto);
        List<SomDrgStandard> drgBenchmarkList = busBenchmarkMapper.selectByExample(busBenchmarkExample);

        //遍历出最小分值
        BigDecimal minPointsSize = null;
        for (SomDrgStandard somDrgStandard : drgBenchmarkList) {
            if(!ValidateUtil.isEmpty(somDrgStandard.getRefer_sco())) {
                if (somDrgStandard.getRefer_sco().compareTo(new BigDecimal(0)) > 0) {
                    if (ValidateUtil.isEmpty(minPointsSize)) {
                        minPointsSize = somDrgStandard.getRefer_sco();
                    } else if (somDrgStandard.getRefer_sco().compareTo(minPointsSize) < 0) {
                        minPointsSize = somDrgStandard.getRefer_sco();
                    }
                }
            }
        }
        hospBaseBusiVo.setMinPointsSize(minPointsSize);
        hospBaseBusiVo.setDrgBenchmarkList(drgBenchmarkList);
    }

    private SomDrgStandardExample getQueryBenchmarkDto(PreGroupDto dto) {
        SomDrgStandardExample busBenchmarkExample = new SomDrgStandardExample();
        SomDrgStandardExample.Criteria criteria = busBenchmarkExample.createCriteria();
        criteria.andHospitalIdEqualTo(dto.getHospital_id());
        if (ValidateUtil.isNotEmpty(dto.getCysj()) && dto.getCysj().length() > 4) {
            if (dto.getCysj().contains("1900")) {
                criteria.andStandardYearEqualTo(String.valueOf(LocalDate.now().getYear()));
            } else {
                criteria.andStandardYearEqualTo(dto.getCysj().substring(0, 4));
            }
        } else {
            criteria.andStandardYearEqualTo(String.valueOf(LocalDate.now().getYear()));
        }
        return busBenchmarkExample;
    }
}
