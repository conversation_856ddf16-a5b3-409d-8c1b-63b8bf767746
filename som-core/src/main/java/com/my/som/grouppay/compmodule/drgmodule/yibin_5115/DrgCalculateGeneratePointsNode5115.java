package com.my.som.grouppay.compmodule.drgmodule.yibin_5115;

import com.my.som.common.constant.DrgConst;
import com.my.som.common.util.ValidateUtil;
import com.my.som.grouppay.vo.HospBaseBusiVo;
import com.my.som.vo.dipBusiness.DipPayToPredictVo;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import com.yomahub.liteflow.core.NodeComponent;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;

@LiteflowComponent(value = "DrgCalculateGeneratePointsNode5115", name = "计算病例分值")
public class DrgCalculateGeneratePointsNode5115 extends NodeComponent {
    @Override
    public void process() throws Exception {
        // 2.获取业务上下文
        HospBaseBusiVo hospBaseBusiVo = this.getContextBean(HospBaseBusiVo.class);
        // 全市病例均费
        BigDecimal regiAverageFee = hospBaseBusiVo.getRegiAverageFee();
        List<DipPayToPredictVo> dipPayToPredictVoList = hospBaseBusiVo.getDipPayToPredictVoList();
        for (DipPayToPredictVo vo : dipPayToPredictVoList) {
            /*（一）稳定病组病例点数
                1．高倍率病例点数
                （1）未纳入特例单议的高倍率病例点数按照正常倍率病例点数计算；
                （2）纳入特例单议的高倍率病例点数按照特例单议相关点数计算规定执行（详见本细则第十七条）。
                2．低倍率病例点数
                低倍率病例点数＝该病例实际发生医疗费用÷全市所有病组次均住院费用×100。
                3．正常倍率病例点数
                正常倍率病例点数＝对应病组基准点数×对应病组医疗机构差异系数。
                （二）非稳定病组病例点数
                非稳定病组病例点数＝该病例实际发生医疗费用÷全市所有病组次均住院费用×100。
                （三）历史空组病例点数
                历史空组病例点数＝该病例实际发生医疗费用÷全市所有病组次均住院费用×100。
                （四）无法正常入组病例点数
                无法正常入组病例点数＝该病例实际发生医疗费用÷全市所有病组次均住院费用×100×70%。
            */

            // 高倍率病例 = 未纳入特例单议的高倍率病例点数按照正常倍率病例点数计算；
            if (vo.getDiseType().equals(DrgConst.CASE_TYPE_1)) {
                vo.setCalculateScore(vo.getRefer_sco().multiply(vo.getAdjm_cof()));
            }

            // 低倍率 = 该病例实际发生医疗费用÷全市所有病组次均住院费用×100。
            if (vo.getDiseType().equals(DrgConst.CASE_TYPE_2)) {
                vo.setCalculateScore(vo.getInHosTotalCost().multiply(DrgConst.SCORE_FACTOR_5155).divide(regiAverageFee, BigDecimal.ROUND_HALF_UP));
            }

            // 正常病例 = 基准点数*差异系数
            if (vo.getDiseType().equals(DrgConst.CASE_TYPE_3)) {
                vo.setCalculateScore(vo.getRefer_sco().multiply(vo.getAdjm_cof()));
            }

            // 无标杆病例 = 该病例实际发生医疗费用÷全市所有病组次均住院费用×100。
            if (vo.getDiseType().equals(DrgConst.CASE_TYPE_4)) {
                vo.setCalculateScore(vo.getInHosTotalCost().multiply(DrgConst.SCORE_FACTOR_5155).divide(regiAverageFee, BigDecimal.ROUND_HALF_UP));
            }

            // 非稳定病组 = 该病例实际发生医疗费用÷全市所有病组次均住院费用×100。
            if (vo.getDiseType().equals(DrgConst.CASE_TYPE_5)) {
                vo.setCalculateScore(vo.getInHosTotalCost().multiply(DrgConst.SCORE_FACTOR_5155).divide(regiAverageFee, BigDecimal.ROUND_HALF_UP));
            }

            // 未入组 = 该病例实际发生医疗费用÷全市所有病组次均住院费用×100×70%
            if (vo.getDiseType().equals(DrgConst.CASE_TYPE_NONE_GROUP)) {
                vo.setCalculateScore(vo.getInHosTotalCost().multiply(DrgConst.SCORE_FACTOR_5155).multiply(DrgConst.NOT_GROUP_RATIO_5155).divide(regiAverageFee, BigDecimal.ROUND_HALF_UP));
            }

            //计算追加分值
            if (vo.getHospCof() != null) {
                vo.setAddScore((vo.getCalculateScore().multiply(vo.getAddRate()).subtract(vo.getCalculateScore())).add(vo.getCalculateScore().multiply(vo.getAddRate()).multiply(vo.getHospCof().subtract(BigDecimal.ONE))));
                vo.setTotlSco(vo.getCalculateScore().add(vo.getAddScore()));
            } else {
                vo.setAddScore(BigDecimal.ZERO);
                vo.setTotlSco(vo.getCalculateScore().add(vo.getAddScore()));
            }
        }
    }
}
