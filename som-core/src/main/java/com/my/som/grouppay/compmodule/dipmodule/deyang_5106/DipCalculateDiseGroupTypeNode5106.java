package com.my.som.grouppay.compmodule.dipmodule.deyang_5106;

import com.my.som.common.util.ValidateUtil;
import com.my.som.grouppay.vo.HospBaseBusiVo;
import com.my.som.vo.dataConfig.ViewHospitalVo;
import com.my.som.vo.dipBusiness.DipPayToPredictVo;
import com.my.som.vo.dipBusiness.PayToPredictBaseInfoVo;
import com.my.som.vo.pregroup.PreGroupVo;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import com.yomahub.liteflow.core.NodeComponent;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@LiteflowComponent(value = "DipCalculateDiseGroupTypeNode5106", name = "病种支付类型判断")
public class DipCalculateDiseGroupTypeNode5106 extends NodeComponent {



    @Override
    public void process() throws Exception {
        // 1.获取业务参数
        List<PreGroupVo> preGroupVos = this.getRequestData();
        // 获取上下文
        HospBaseBusiVo hospBaseBusiVo = this.getContextBean(HospBaseBusiVo.class);
        //组织计算病例类型及分值参数
        List<DipPayToPredictVo> scoreList = new ArrayList<>();
        //基础参数
        PayToPredictBaseInfoVo payToPredictBaseInfoVo;
        for (PreGroupVo dipGroupVo : preGroupVos) {
            payToPredictBaseInfoVo = dipGroupVo.getPayToPredictBaseInfoVo();
            DipPayToPredictVo payToPredictVo = new DipPayToPredictVo();
            payToPredictVo.setHospLv(dipGroupVo.getHosp_lv());
            payToPredictVo.setDipCodg(dipGroupVo.getDipCodg());
            payToPredictVo.setDipName(dipGroupVo.getDipName());
            payToPredictVo.setGrpStas(dipGroupVo.getGroupStatus());

            payToPredictVo.setInHosDays(payToPredictBaseInfoVo.getInHosDays());
            payToPredictVo.setAge(payToPredictBaseInfoVo.getAge());
            payToPredictVo.setDeptCode(payToPredictBaseInfoVo.getDeptCode());
            payToPredictVo.setWMCode(payToPredictBaseInfoVo.getWMCode());
            payToPredictVo.setTCMCode(payToPredictBaseInfoVo.getTCMCode());
            payToPredictVo.setMax(dipGroupVo.getHighFee());
            payToPredictVo.setMin(dipGroupVo.getMinFee());
            payToPredictVo.setRefer_sco(dipGroupVo.getRefer_sco());
            payToPredictVo.setAdjm_cof(dipGroupVo.getAdjm_cof());
            payToPredictVo.setLastYearLevelStandardCost(dipGroupVo.getDipLastYearAvgCost());
            payToPredictVo.setUplmtMag(dipGroupVo.getUplmtMag());
            payToPredictVo.setLowlmtMag(dipGroupVo.getLowlmtMag());
            payToPredictVo.setDisType(dipGroupVo.getDisType());
            payToPredictVo.setLevelCost(dipGroupVo.getDipAvgCostLevel());
            payToPredictVo.setYm(payToPredictBaseInfoVo.getYm());
            payToPredictVo.setPreHospExamfee(ValidateUtil.isEmpty(payToPredictBaseInfoVo.getPreHospExamfee())?BigDecimal.ZERO:payToPredictBaseInfoVo.getPreHospExamfee());
            payToPredictVo.setHospitalId(payToPredictBaseInfoVo.getHospitalId());
            payToPredictVo.setInsuType(payToPredictBaseInfoVo.getInsuType());

            BigDecimal tcm_treat_fee = ValidateUtil.isEmpty(payToPredictBaseInfoVo.getTCMTreatmentCost()) ? BigDecimal.ZERO : payToPredictBaseInfoVo.getTCMTreatmentCost();
            BigDecimal tcmherb = ValidateUtil.isEmpty(payToPredictBaseInfoVo.getTcmherb()) ? BigDecimal.ZERO : payToPredictBaseInfoVo.getTcmherb();
            BigDecimal west_fee = ValidateUtil.isEmpty(payToPredictBaseInfoVo.getWest_fee()) ? BigDecimal.ZERO : payToPredictBaseInfoVo.getWest_fee();
            payToPredictVo.setTCMTreatmentCost(tcm_treat_fee.add(tcmherb));
            payToPredictVo.setHospitalizationExpenses(tcm_treat_fee.add(west_fee));

            payToPredictVo.setAreaStandardCost(dipGroupVo.getDipAvgCost());
            payToPredictVo.setStableFlag(dipGroupVo.getStableFlag());
            payToPredictVo.setAsstListDiseSevDeg(dipGroupVo.getAsstListDiseSevDeg());
            payToPredictVo.setAsstListTmorSevDeg(dipGroupVo.getAsstListTmorSevDeg());
            payToPredictVo.setAuxiliaryBurn(dipGroupVo.getAuxiliaryBurn());
            payToPredictVo.setAsstListAgeGrp(dipGroupVo.getAsstListAgeGrp());
            if(ValidateUtil.isNotEmpty(dipGroupVo.getDipCodg())){
               payToPredictVo.setDiseaseAverageCost(hospBaseBusiVo.getRegiAverageFee());
            }
            payToPredictVo.setInHosTotalCost(payToPredictBaseInfoVo.getInHosTotalCost());
            payToPredictVo.setInHospCost(payToPredictBaseInfoVo.getInHospCost());
            payToPredictVo.setPreHospExamfee(ValidateUtil.isEmpty(payToPredictBaseInfoVo.getPreHospExamfee())?BigDecimal.ZERO:payToPredictBaseInfoVo.getPreHospExamfee());
            payToPredictVo.setBusinessSerial(payToPredictBaseInfoVo.getYwlsh());
            payToPredictVo.setCheckTotalFee(payToPredictBaseInfoVo.getCheckTotalFee());
            payToPredictVo.setLevelStandardDays(dipGroupVo.getDipAvgDaysLevel().toString());
            payToPredictVo.setIs_base_dise(dipGroupVo.getIs_base_dise());
            payToPredictVo.setIs_tcm_dise(dipGroupVo.getIs_tcm_dise());
            if (dipGroupVo.getAsstListAdm() != null) {
                payToPredictVo.setAsstListAdm(dipGroupVo.getAsstListAdm());
            } else {
                payToPredictVo.setAsstListAdm(BigDecimal.ONE);
            }

            payToPredictVo.setFundAmtSum(payToPredictBaseInfoVo.getFundAmtSum());
            payToPredictVo.setEmpFundRatio(payToPredictBaseInfoVo.getEmpFundRatio());
            payToPredictVo.setResidFundRatio(payToPredictBaseInfoVo.getResidFundRatio());

            scoreList.add(payToPredictVo);
        }
        hospBaseBusiVo.setDipPayToPredictVoList(scoreList);
    }


}
