package com.my.som.grouppay.compmodule.drgmodule.guangan5116;

import com.my.som.common.constant.DrgConst;
import com.my.som.common.util.ValidateUtil;
import com.my.som.grouppay.vo.HospBaseBusiVo;
import com.my.som.vo.dipBusiness.DipPayToPredictVo;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import com.yomahub.liteflow.core.NodeComponent;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;

@LiteflowComponent(value = "DrgCalculateGeneratePointsNode5116", name = "计算病例分值")
public class DrgCalculateGeneratePointsNode5116 extends NodeComponent {
    @Override
    public void process() throws Exception {

        // 2.获取业务上下文
        HospBaseBusiVo hospBaseBusiVo = this.getContextBean(HospBaseBusiVo.class);
        BigDecimal regiAverageFee  =  hospBaseBusiVo.getRegiAverageFee();
        List<DipPayToPredictVo> dipPayToPredictVoList = hospBaseBusiVo.getDipPayToPredictVoList();
        for (DipPayToPredictVo vo : dipPayToPredictVoList) {

            BigDecimal standardFee;
            if (!ValidateUtil.isEmpty(vo.getStandardFee()) && BigDecimal.ZERO.compareTo(vo.getStandardFee()) != 0) {
                standardFee = vo.getStandardFee();
            } else {
                standardFee = vo.getLevelStandardCost();
            }

            /*
            、住院天数大于 60 天的病例，其支付点数=
        长期住院病例点数=(该病例实际发生住院医疗费用-该病例不合理住院医疗费用):所有病组次均住院医疗费用x100。
             */

            if(DrgConst.CASE_TYPE_TS_2.equals(vo.getDiseType())){
                vo.setCalculateScore(vo.getInHosTotalCost().divide(regiAverageFee, 4,BigDecimal.ROUND_CEILING).multiply(new BigDecimal(100)));
            }
            //DGR病例计算
            /*高倍率分值计算=DRG 基准点数xDRG差异系数+
            DRG 基准点数x(实际发生医疗费用:该DRG组次均费用-高倍率阈值)，。
            = DRG 基准点数  * (DRG差异系数+(实际发生医疗费用:该DRG组次均费用-高倍率阈值))
            */
            /*20240919应客服要求更改分值计算方式为  基准点数*差异系数

            * */
            if (DrgConst.CASE_TYPE_1.equals(vo.getDiseType())) {
                BigDecimal biasRateFactor = vo.getBiasRateFactor();
                BigDecimal refer = vo.getInHosTotalCost().divide(standardFee, 4,BigDecimal.ROUND_CEILING).subtract(biasRateFactor).add(vo.getAdjm_cof());
                BigDecimal referSco = vo.getRefer_sco().multiply(refer);
                vo.setCalculateScore(referSco);
            }
            //低倍率病例支付点数=DRG 基准点数x(病例住院总费用÷该 DRG 组次均费用)，最高不得超过该 DRG 基准点数。
            if (DrgConst.CASE_TYPE_2.equals(vo.getDiseType())) {
                BigDecimal referSco =vo.getRefer_sco().multiply(vo.getInHosTotalCost().divide(standardFee, 4,BigDecimal.ROUND_CEILING));
                if(vo.getRefer_sco().compareTo(referSco) <0 ){
                    referSco = vo.getRefer_sco();
                }
                vo.setCalculateScore(referSco);
            }

            //正常病例分值计算 = 基准点数*差异系数
            if (DrgConst.CASE_TYPE_3.equals(vo.getDiseType())) {
                vo.setCalculateScore(vo.getRefer_sco().multiply(vo.getAdjm_cof()).setScale(4, BigDecimal.ROUND_HALF_UP) );
            }

            //未入组按点数=合理总费用/全部 DRG 住院次均费用x100。
            if (DrgConst.CASE_TYPE_NONE_GROUP.equals(vo.getDiseType())) {
                vo.setCalculateScore(vo.getInHosTotalCost().divide(regiAverageFee, 4,BigDecimal.ROUND_CEILING).multiply(new BigDecimal(100)));
            }

            //计算追加分值
            if (vo.getHospCof() != null) {
                vo.setAddScore((vo.getCalculateScore().multiply(vo.getAddRate()).subtract(vo.getCalculateScore())).add(vo.getCalculateScore().multiply(vo.getAddRate()).multiply(vo.getHospCof().subtract(BigDecimal.ONE))).setScale(4, BigDecimal.ROUND_HALF_UP) );
                vo.setTotlSco(vo.getCalculateScore().add(vo.getAddScore()));
            } else {
                vo.setAddScore(BigDecimal.ZERO);
                vo.setTotlSco(vo.getCalculateScore().add(vo.getAddScore()));
            }
        }
    }
}
