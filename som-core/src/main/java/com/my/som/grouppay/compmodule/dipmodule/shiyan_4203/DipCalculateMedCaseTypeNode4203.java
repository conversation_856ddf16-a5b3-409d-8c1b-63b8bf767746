package com.my.som.grouppay.compmodule.dipmodule.shiyan_4203;


import com.my.som.common.constant.DrgConst;
import com.my.som.common.util.ValidateUtil;
import com.my.som.grouppay.vo.HospBaseBusiVo;
import com.my.som.vo.dataConfig.DipDiseaseTypeVo;
import com.my.som.vo.dataHandle.PayToPredict.PayToPredictVo;
import com.my.som.vo.dipBusiness.DipPayToPredictVo;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import com.yomahub.liteflow.core.NodeComponent;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

import static com.my.som.grouppay.compmodule.dipmodule.common.PaymentConst.KEY_CHINESE_DISEASE;
import static com.my.som.util.GenerateScoreUtil.KEY_BASE_DISEASE;

@LiteflowComponent(value = "DipCalculateMedCaseTypeNode4203", name = "病例类型判断")
public class DipCalculateMedCaseTypeNode4203 extends NodeComponent {
    @Override
    public void process() throws Exception {
        // 获取配置信息
        Map calculateScoreParams = this.getContextBean(Map.class);
        // 获取业务上下文
        HospBaseBusiVo hospBaseBusiVo = this.getContextBean(HospBaseBusiVo.class);
        List<DipPayToPredictVo> payToPredictVoList = hospBaseBusiVo.getDipPayToPredictVoList();

        for (DipPayToPredictVo payToPredictVo : payToPredictVoList) {
            // 病种级别均费
            BigDecimal levelAverageCostForDisease = payToPredictVo.getLevelCost();
            // 患者住院总费用
            BigDecimal totalHospitalizationCost = payToPredictVo.getInHosTotalCost();

            //判断是否为中医病钟
            setTCMdise(payToPredictVo, calculateScoreParams);

            //判断是否为基层病种
            setBaseDisa(payToPredictVo, calculateScoreParams);

            //病种类型判断
            if (payToPredictVo.getDipCodg() == null) {
                // 未入组
                payToPredictVo.setPaymentDiseType(DrgConst.DISETYPE_NONE_INGROUP);
                payToPredictVo.setDiseType(DrgConst.CASE_TYPE_NONE_GROUP);
            } else if (DrgConst.DIS_CHM_1.equals(payToPredictVo.getIs_tcm_dise())) {
                // 中治率计算
                processTcm(payToPredictVo);
                if (payToPredictVo.getTCMRatio() != null && payToPredictVo.getTCMRatio().compareTo(new BigDecimal(0.3)) >= 0) {
                    // 中医病种
                    payToPredictVo.setPaymentDiseType(DrgConst.DISETYPE_CHM);
                    if( payToPredictVo.getTCMRatio().compareTo(new BigDecimal(0.5)) <= 0)     {
                        payToPredictVo.setTcmAdtCof(new BigDecimal(1.03));
                    }else{
                        payToPredictVo.setTcmAdtCof(new BigDecimal(1.06));
                    }
                } else {
                    if (ValidateUtil.isNotEmpty(payToPredictVo.getDisType())) {
                        // 核心 / 综合病种
                        payToPredictVo.setPaymentDiseType(payToPredictVo.getDisType());
                    }
                }
            } else if (DrgConst.DIS_BASE_1.equals(payToPredictVo.getIs_base_dise())) {
                // 基层病种
                payToPredictVo.setPaymentDiseType(DrgConst.DISETYPE_BASIC);
            } else {
                if (ValidateUtil.isNotEmpty(payToPredictVo.getDisType())) {
                    // 核心 / 综合病种
                    payToPredictVo.setPaymentDiseType(payToPredictVo.getDisType());
                } else {
                    //设置默认核心病种
                    payToPredictVo.setPaymentDiseType(DrgConst.DISETYPE_CORE);
                }
            }

            //病例类型判断
            //入组且有标杆
            if (!ValidateUtil.isEmpty(payToPredictVo.getDipCodg()) && !ValidateUtil.isEmpty(levelAverageCostForDisease)) {
                if (levelAverageCostForDisease.compareTo(BigDecimal.ZERO) > 0) {
                    //费用倍率
                    BigDecimal rate = totalHospitalizationCost.divide(levelAverageCostForDisease, 4, BigDecimal.ROUND_HALF_UP);
                    // 倍率判断
                    if (rate.compareTo(new BigDecimal(0.5)) <= 0) {
                        // 低倍率
                        payToPredictVo.setDiseType(DrgConst.CASE_TYPE_2);
                        payToPredictVo.setPayMentType(DrgConst.PAYMENT_TYPE_DIP);

                        // 倍率调节系数 = 病种级别均费 / 患者住院总费用
                        payToPredictVo.setBiasRateFactor(rate);

                    } else if (rate.compareTo(new BigDecimal(2)) >= 0 && rate.compareTo(new BigDecimal(5)) < 0) {
                        // 高倍率
                        payToPredictVo.setDiseType(DrgConst.CASE_TYPE_1);
                        payToPredictVo.setPayMentType(DrgConst.PAYMENT_TYPE_DIP);
                        rate = rate.subtract(new BigDecimal(2)).add(new BigDecimal(1));
                        // 倍率调节系数 = 病种级别均费 / 患者住院总费用 - 2 + 1
                        payToPredictVo.setBiasRateFactor(rate);
                    } else if(rate.compareTo(new BigDecimal(5)) >= 0 || totalHospitalizationCost.compareTo(new BigDecimal(150000)) >= 0){
                        payToPredictVo.setPaymentDiseType(DrgConst.DISETYPE_EXTREMELYE_HIGH);
                        payToPredictVo.setDiseType(DrgConst.CASE_TYPE_6);
                    }
                        else {
                        // 正常倍率
                        payToPredictVo.setDiseType(DrgConst.CASE_TYPE_3);
                        payToPredictVo.setPayMentType(DrgConst.PAYMENT_TYPE_DIP);
                        payToPredictVo.setBiasRateFactor(BigDecimal.ONE);
                    }
                } else {
                    payToPredictVo.setPaymentDiseType(DrgConst.DISETYPE_NONE_INGROUP);
                }
            }
            //设置是否启动月度单价
            payToPredictVo.setRunMonthPrice(false);
        }
    }

    private static void setBaseDisa(DipPayToPredictVo payToPredictVo, Map calculateScoreParams) {
        List<DipDiseaseTypeVo> baseDisease = (List<DipDiseaseTypeVo>) calculateScoreParams.get(KEY_BASE_DISEASE);
        if (ValidateUtil.isEmpty(baseDisease)) {
            payToPredictVo.setIs_base_dise("0");
        } else {
            for (DipDiseaseTypeVo disease : baseDisease) {
                if (disease.getDipCodg().equals(payToPredictVo.getDipCodg())) {
                    payToPredictVo.setIs_base_dise("1");
                }
            }
        }
    }

    private void setTCMdise(DipPayToPredictVo payToPredictVo, Map calculateScoreParams) {
        List<DipDiseaseTypeVo> chinsesDisease = (List<DipDiseaseTypeVo>) calculateScoreParams.get(KEY_CHINESE_DISEASE);
        if (ValidateUtil.isEmpty(chinsesDisease)) {
            payToPredictVo.setIs_tcm_dise("0");
        } else {
            for (DipDiseaseTypeVo disease : chinsesDisease) {
                if ((!ValidateUtil.isEmpty(payToPredictVo.getDipCodg()) && disease.getDipCodg().equals(payToPredictVo.getDipCodg()))) {
                    payToPredictVo.setIs_tcm_dise("1");
                }
            }
        }
    }

    /**
     * 中治率计算
     * （中草药费 + 中医治疗费）/ 住院总费用
     */
    private void processTcm(DipPayToPredictVo payToPredictVo) {
        BigDecimal tcmTreatmentCost = payToPredictVo.getTCMTreatmentCost();
        BigDecimal ra = tcmTreatmentCost.divide(payToPredictVo.getInHosTotalCost(), BigDecimal.ROUND_HALF_UP).setScale(2, BigDecimal.ROUND_HALF_UP);
        payToPredictVo.setTCMRatio(ra);
    }
}
