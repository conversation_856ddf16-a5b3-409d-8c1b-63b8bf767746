package com.my.som.grouppay.compmodule.drgmodule.dazhou_5117;

import com.my.som.common.constant.DrgConst;
import com.my.som.grouppay.compmodule.dipmodule.common.PaymentConst;
import com.my.som.grouppay.vo.HospBaseBusiVo;
import com.my.som.vo.dipBusiness.DipPayToPredictVo;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import com.yomahub.liteflow.core.NodeComponent;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;

@LiteflowComponent(value = "DrgCalculateGeneratePointsNode5117", name = "计算病例分值")
public class DrgCalculateGeneratePointsNode5117 extends NodeComponent {
    @Override
    public void process() throws Exception {

        // 2.获取业务上下文
        HospBaseBusiVo hospBaseBusiVo = this.getContextBean(HospBaseBusiVo.class);
        List<DipPayToPredictVo> dipPayToPredictVoList = hospBaseBusiVo.getDipPayToPredictVoList();
        for (DipPayToPredictVo vo : dipPayToPredictVoList) {

            //一、项目直接折算
            /*
             * 1、大于60天[PAYMENT_TYPE_PROJECT_CONVER]，结算点数=该病例实际费用÷所有病例的次均住院费用×100
             * 2、非稳定病组[PAYMENT_TYPE_PROJECT_CONVER]，结算点数=该病例实际费用÷所有病例的次均住院费用×100
             * 3、空白组、歧义组【PAYMENT_TYPE_PROJECT_CONVER_RATIO】，结算点数=该病例实际费用÷所有病例的次均住院费用×100×0.9
             */
            // [PAYMENT_TYPE_PROJECT_CONVER]该病例实际费用÷所有病例的次均住院费用×100
            if (DrgConst.PAYMENT_TYPE_PROJECT_CONVER.contains(vo.getPayMentType())) {
                //归属项目折算点数支付
                vo.setCalculateScore(vo.getInHosTotalCost().add(vo.getPreHospExamfee()).divide(hospBaseBusiVo.getRegiAverageFee(), 4, RoundingMode.HALF_UP).multiply(PaymentConst.PROPORTION_BASE));
            }

            // [PAYMENT_TYPE_PROJECT_CONVER_RATIO]该病例实际费用÷所有病例的次均住院费用×100×0.9
            if (DrgConst.PAYMENT_TYPE_PROJECT_CONVER_RATIO.contains(vo.getPayMentType())) {
                //归属项目折算点数支付*0.9
                vo.setCalculateScore(vo.getInHosTotalCost().add(vo.getPreHospExamfee()).divide(hospBaseBusiVo.getRegiAverageFee(), 4, RoundingMode.HALF_UP).multiply(PaymentConst.PROPORTION_BASE).multiply(new BigDecimal("0.7")));
            }


            // 无标杆病例
            if (DrgConst.PAYMENT_TYPE_PROJECT.contains(vo.getPayMentType())) {
                vo.setCalculateScore(new BigDecimal(0));
            }

            //二、DGR病例计算
            /*
             *（1）高倍率：>=1.4倍同级别均费,结算点数 =（该病例实际费用÷该等级医疗机构该 DRG 组次均住院费用-0.4）×该病组结算点数
             *（2）正常倍率：非高倍率和低倍率病例，结算点数=DRG 组基准点数×等级系数×调整系数
             *（3）低倍率：<=0.7倍同级别均费，结算点数=该病例实际费用÷所有病例的次均住院费用×100
             * */
            //高倍率
            if (DrgConst.CASE_TYPE_1.equals(vo.getDiseType())) {
                vo.setCalculateScore(
                        (vo.getInHosTotalCost().add(vo.getPreHospExamfee())
                                .divide(vo.getLevelStandardCost(), 4, RoundingMode.HALF_UP)
                                .subtract(vo.getUplmtMag()).add(vo.getAdjm_cof())
                                .multiply(vo.getRefer_sco())));
            }

            //低倍率
            if (DrgConst.CASE_TYPE_2.equals(vo.getDiseType())) {
                vo.setCalculateScore(
                        vo.getInHosTotalCost().add(vo.getPreHospExamfee()).divide(hospBaseBusiVo.getRegiAverageFee(), 4, RoundingMode.HALF_UP).multiply(PaymentConst.PROPORTION_BASE));
            }

            //正常病例
            if (DrgConst.CASE_TYPE_3.equals(vo.getDiseType())) {
                vo.setCalculateScore(vo.getRefer_sco().multiply(vo.getAdjm_cof()));
            }

            if (DrgConst.DISE_TYPE_8.equals(vo.getDiseType())) {
                vo.setCalculateScore(vo.getRefer_sco().multiply(vo.getAdjm_cof())
                        .multiply(vo.getInHosTotalCost().add(vo.getPreHospExamfee())
                                .divide(vo.getLevelStandardCost(), 4, RoundingMode.HALF_UP))
                );
            }

            vo.setAddScore(BigDecimal.ZERO);
            vo.setTotlSco(vo.getCalculateScore().add(vo.getAddScore()));
        }
    }
}
