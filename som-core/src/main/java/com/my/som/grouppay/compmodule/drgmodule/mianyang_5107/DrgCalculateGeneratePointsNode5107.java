package com.my.som.grouppay.compmodule.drgmodule.mianyang_5107;

import com.my.som.common.constant.DrgConst;
import com.my.som.common.util.ValidateUtil;
import com.my.som.grouppay.vo.HospBaseBusiVo;
import com.my.som.vo.dipBusiness.DipPayToPredictVo;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import com.yomahub.liteflow.core.NodeComponent;

import java.math.BigDecimal;
import java.util.List;

@LiteflowComponent(value = "DrgCalculateGeneratePointsNode5107", name = "计算病例分值")
public class DrgCalculateGeneratePointsNode5107 extends NodeComponent {
    @Override
    public void process() throws Exception {
        // 2.获取业务上下文
        HospBaseBusiVo hospBaseBusiVo = this.getContextBean(HospBaseBusiVo.class);
        List<DipPayToPredictVo> dipPayToPredictVoList = hospBaseBusiVo.getDipPayToPredictVoList();
        for (DipPayToPredictVo vo : dipPayToPredictVoList) {
            if (ValidateUtil.isEmpty(vo.getBed_type())) {
                //DGR病例计算
                /*高倍率分值计算=基准点数*差异系数+（(该病例实际发生医疗费用－该病例不合理医疗费用）÷全市试点医疗机构该病组历史次均住院
                费用－高倍率界值)）× 该病组基准点数*/
                if (vo.getDiseType().equals(DrgConst.CASE_TYPE_1)) {
                    vo.setCalculateScore(vo.getRefer_sco().multiply(vo.getAdjm_cof()).add(
                            vo.getInHosTotalCost().add(vo.getPreHospExamfee()).divide(vo.getLevelStandardCost(), BigDecimal.ROUND_CEILING).subtract(vo.getUplmtMag()).multiply(vo.getRefer_sco())));
                }
                //低倍率分值计算=对应病组基准点数×（该病例实际发生医疗费用÷所有试点医疗机构该病组次均住院费用）
                if (vo.getDiseType().equals(DrgConst.CASE_TYPE_2)) {
                    vo.setCalculateScore(vo.getInHosTotalCost().add(vo.getPreHospExamfee()).divide(vo.getLevelStandardCost(), BigDecimal.ROUND_CEILING).multiply(vo.getRefer_sco()));
                }
                //正常病例分值计算 = 基准点数*差异系数
                if (vo.getDiseType().equals(DrgConst.CASE_TYPE_3)) {
                    vo.setCalculateScore(vo.getRefer_sco().multiply(vo.getAdjm_cof()));
                }
                //无支付标杆 = 0
                if (vo.getDiseType().equals(DrgConst.CASE_TYPE_4)) {
                    vo.setCalculateScore(BigDecimal.ZERO);
                }
                //未入组=该病例实际发生医疗费用÷全市医疗机构所有病组次均住院费用×100×30%
                if (vo.getDiseType().equals(DrgConst.CASE_TYPE_NONE_GROUP)) {
                    vo.setCalculateScore(vo.getInHosTotalCost().add(vo.getPreHospExamfee()).divide(hospBaseBusiVo.getRegiAverageFee()).multiply(DrgConst.NONE_GROUP_CONVERT_RATE));
                }
                //计算追加分值
                if (vo.getHospCof() != null) {
                    vo.setAddScore((vo.getCalculateScore().multiply(vo.getAddRate()).subtract(vo.getCalculateScore())).add(vo.getCalculateScore().multiply(vo.getAddRate()).multiply(vo.getHospCof().subtract(BigDecimal.ONE))));
                    vo.setTotlSco(vo.getCalculateScore().add(vo.getAddScore()));
                } else {
                    vo.setAddScore(BigDecimal.ZERO);
                    vo.setTotlSco(vo.getCalculateScore().add(vo.getAddScore()));
                }
            } else {
                //床日病例计算=住院天数*床日标准
                vo.setCalculateScore(vo.getBed_refer_sco().multiply(vo.getInHosDays()));
                vo.setAddScore(BigDecimal.ZERO);
                vo.setTotlSco(vo.getCalculateScore().add(vo.getAddScore()));
            }
        }
    }
}
