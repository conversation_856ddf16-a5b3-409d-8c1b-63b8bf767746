package com.my.som.grouppay.compmodule.dipmodule.common.calc;

import com.my.som.common.constant.DrgConst;
import com.my.som.common.util.ValidateUtil;
import com.my.som.grouppay.vo.HospBaseBusiVo;
import com.my.som.util.SysCommonConfigUtil;
import com.my.som.vo.dataHandle.PayToPredict.PayToPredictVo;
import com.my.som.vo.dipBusiness.DipPayToPredictVo;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import com.yomahub.liteflow.core.NodeComponent;

import java.math.BigDecimal;
import java.util.List;


@LiteflowComponent(value = "DipCalculateBusiProcPayForecastNode", name = "支付计算-流程")
public class DipCalculateBusiProcPayForecastNode extends NodeComponent {
    @Override
    public void process() throws Exception {
        // 获取业务上下文
        HospBaseBusiVo hospBaseBusiVo = this.getContextBean(HospBaseBusiVo.class);
        List<DipPayToPredictVo> payToPredictVoList = hospBaseBusiVo.getDipPayToPredictVoList();
        // 设置默认价格
        BigDecimal defaultPrice = BigDecimal.ZERO;
        for (DipPayToPredictVo payToPredictVo : payToPredictVoList) {
            BigDecimal forecastPrice = (payToPredictVo.getPrice() != null) ? payToPredictVo.getPrice() : defaultPrice;
            // 计算预计费用
            BigDecimal forecastFee = payToPredictVo.getTotlSco().multiply(forecastPrice).setScale(2, BigDecimal.ROUND_HALF_UP);
            // 预测单价
            payToPredictVo.setScorePrice(forecastPrice);
            // 预测费用
            payToPredictVo.setForecast_fee(forecastFee);
            if(ValidateUtil.isEmpty(payToPredictVo.getSumfee())){
                payToPredictVo.setSumfee(
                        BigDecimal.ZERO
                        .add(ValidateUtil.isEmpty(payToPredictVo.getInHosTotalCost())? BigDecimal.ZERO : payToPredictVo.getInHosTotalCost())
                        .add(ValidateUtil.isEmpty(payToPredictVo.getPreHospExamfee())? BigDecimal.ZERO : payToPredictVo.getPreHospExamfee()));
            }
            // 预测盈亏
            payToPredictVo.setProfitloss(forecastFee.subtract(payToPredictVo.getSumfee()));
            String ALGORITHMA_TYPE = (String) SysCommonConfigUtil.get(DrgConst.ALGORITHMA_TYPE);
            if (ALGORITHMA_TYPE != null &&  !ValidateUtil.isEmpty(ALGORITHMA_TYPE)) {
                if("1".equals(ALGORITHMA_TYPE)) {
                    BigDecimal fundAmtSum = payToPredictVo.getFundAmtSum();
                    if (!ValidateUtil.isEmpty(fundAmtSum) &&BigDecimal.ZERO.compareTo(fundAmtSum) != 0) {
                        BigDecimal ZJforecastFee = forecastFee.multiply(payToPredictVo.getReferenceFundRatio());
                        if(!ValidateUtil.isEmpty(ZJforecastFee) && BigDecimal.ZERO.compareTo(ZJforecastFee) != 0) {
                            payToPredictVo.setOriginal_forecast_fee(forecastFee);
                            payToPredictVo.setForecast_fee(ZJforecastFee);
                            payToPredictVo.setPreFundFee(ZJforecastFee);
                            payToPredictVo.setOriginalProfitloss(payToPredictVo.getProfitloss());
                            payToPredictVo.setProfitloss(ZJforecastFee.subtract(payToPredictVo.getFundAmtSum()));
                            //计算补偿比里
                            BigDecimal preFundFee = payToPredictVo.getPreFundFee();
                            calculateFundRatio(payToPredictVo, preFundFee);
                        }else {
                            payToPredictVo.setOriginal_forecast_fee(BigDecimal.ZERO);
                            payToPredictVo.setForecast_fee(BigDecimal.ZERO);
                            payToPredictVo.setPreFundFee(BigDecimal.ZERO);
                            payToPredictVo.setOriginalProfitloss(BigDecimal.ZERO);
                            payToPredictVo.setProfitloss(BigDecimal.ZERO);
                            payToPredictVo.setFundRatio(BigDecimal.ZERO + "%");
                        }
                    }else{
                        morensuanfa(payToPredictVo);
                    }
                }else{
                    morensuanfa(payToPredictVo);
                }
            }else{
                morensuanfa(payToPredictVo);
            }
        }
    }

    private static void morensuanfa(DipPayToPredictVo payToPredictVo) {
        if(!ValidateUtil.isEmpty(payToPredictVo.getFundSourceType())){
            // 计算预测报账部分
            payToPredictVo.setPreFundFee(payToPredictVo.getForecast_fee().subtract(payToPredictVo.getPresonBearCost()));
            BigDecimal preFundFee = payToPredictVo.getPreFundFee();
            calculateFundRatio(payToPredictVo,preFundFee);
        }
    }

    private static void calculateFundRatio(DipPayToPredictVo payToPredictVo,BigDecimal preFundFee) {
        //补偿比
        BigDecimal fundRatio = payToPredictVo.getFundAmtSum().divide(preFundFee, 4, BigDecimal.ROUND_DOWN).multiply(new BigDecimal(100)).setScale(2, BigDecimal.ROUND_DOWN);
        payToPredictVo.setFundRatio(fundRatio +"%");
        if(new BigDecimal(100).compareTo(fundRatio)< 0){
            // 补偿比类型
            payToPredictVo.setFundRatioType("1");
        }else if (new BigDecimal(85).compareTo(fundRatio)>0) {
            // 补偿比类型
            payToPredictVo.setFundRatioType("2");
        }else {
            // 补偿比类型
            payToPredictVo.setFundRatioType("3");
        }
    }
}
