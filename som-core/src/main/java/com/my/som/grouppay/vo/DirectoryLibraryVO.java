package com.my.som.grouppay.vo;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * 病种目录库VO
 */
@Getter
@Setter
public class DirectoryLibraryVO {
    // dip病种编码
    private String dipCode;
    // dip病种名称
    private String dipName;

    // drg病种编码
    private String drgCode;
    // drg病种名称
    private String drgName;

    // 病种三年均费
    private BigDecimal dipAvgFee;
    // 病种同级别均费
    private BigDecimal sameLevelDiseaseAvgFee;
    // 全市三年均费
    private BigDecimal cityAvgFee;
    // 病种调节系数
    private BigDecimal diseaseLevelCoeff;
    //病种分值
    private BigDecimal scoreValue;
    // 病种类型
    private String disType;
    //病种标杆住院天数
    private BigDecimal sameLevelDiseaseAvgDay;
    //辅助目录类
    private String auxType;
    //辅助目录名称
    private String axuName;
    //辅助目录系数
    private BigDecimal auxXs;
    //是否基层病种
    private String isTypeBase;
    //是否中医病种
    private String isTypeChm;
}
