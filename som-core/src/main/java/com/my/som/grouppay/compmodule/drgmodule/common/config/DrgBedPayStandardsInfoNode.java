package com.my.som.grouppay.compmodule.drgmodule.common.config;

import com.my.som.common.exception.AppException;
import com.my.som.common.util.ValidateUtil;
import com.my.som.dto.dataHandle.DrgBedStandardDto;
import com.my.som.dto.pregroup.PreGroupDto;
import com.my.som.grouppay.vo.BedDrgDiseCodeCfgVo;
import com.my.som.grouppay.vo.HospBaseBusiVo;
import com.my.som.mapper.common.BenchmarkConfigMapper;
import com.my.som.model.dataHandle.DrgBedStandard;
import com.my.som.model.dataHandle.SomDrgStandardExample;
import com.my.som.vo.dataConfig.ViewHospitalVo;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import com.yomahub.liteflow.core.NodeComponent;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@LiteflowComponent(value = "DrgBedPayStandardsInfoNode", name = "获取床日支付标杆")
public class DrgBedPayStandardsInfoNode extends NodeComponent {

    @Autowired
    private BenchmarkConfigMapper benchmarkConfigMapper;

    @Override
    public void process() throws Exception {
        // 1.获取业务参数
        PreGroupDto preGroupDto = this.getRequestData();
        // 2.获取业务对象
        HospBaseBusiVo hospBaseBusiVo = this.getContextBean(HospBaseBusiVo.class);
        DrgBedStandardDto drgBedStandardDto = getQueryDto(preGroupDto, hospBaseBusiVo);
        List<DrgBedStandard> drgBedStandardList = benchmarkConfigMapper.selectDrgBedDiseStandard(drgBedStandardDto);
        Map<String, DrgBedStandard> bedDrgDiseCodeCfgVoMap = drgBedStandardList.stream().collect(Collectors.toMap(DrgBedStandard::getBed_dise_codg, bedStandard -> bedStandard));
        hospBaseBusiVo.setDrgBedStandardMap(bedDrgDiseCodeCfgVoMap);
    }

    private DrgBedStandardDto getQueryDto(PreGroupDto dto, HospBaseBusiVo hospBaseBusiVo) {
        DrgBedStandardDto drgBedStandardDto = new DrgBedStandardDto();
        if (ValidateUtil.isNotEmpty(dto.getCysj()) && dto.getCysj().length() > 4) {
            if (dto.getCysj().contains("1900")) {
                drgBedStandardDto.setYear(String.valueOf(LocalDate.now().getYear()));
            } else {
                drgBedStandardDto.setYear(dto.getCysj().substring(0, 4));
            }
        } else {
            drgBedStandardDto.setYear(String.valueOf(LocalDate.now().getYear()));
        }
        ViewHospitalVo viewHospitalVo = hospBaseBusiVo.getHospitalVoMap().get(dto.getHospital_id());
        if (ValidateUtil.isEmpty(viewHospitalVo)) {
            throw new AppException("医疗机构编码不匹配" + dto.getHospital_id());
        }
        drgBedStandardDto.setMedins_lv(viewHospitalVo.getHospLv());
        return drgBedStandardDto;
    }
}
