package com.my.som.grouppay.vo;

import com.my.som.model.common.SomDipStandard;
import com.my.som.model.dataHandle.DrgBedStandard;
import com.my.som.model.dataHandle.SomDrgStandard;
import com.my.som.vo.common.DipConfigVo;
import com.my.som.vo.dataConfig.ViewHospitalVo;
import com.my.som.vo.dataHandle.PayToPredict.PayToPredictVo;
import com.my.som.vo.dipBusiness.DipPayToPredictVo;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@Getter
@Setter
public class HospBaseBusiVo {
    /**
     * 医疗机构编码
     */
    private String hospitalId;
    /**
     * 医疗机构信息（等级、）
     **/
    private Map<String, ViewHospitalVo> hospitalVoMap;
    /**
     * 职工单价
     **/
    private BigDecimal czPrice;

    /**
     * 居民单价
     **/
    private BigDecimal cxPrice;

    /**
     * 平均预算
     **/
    private BigDecimal price;


    /**
     * 支付预测单价信息
     */
    private Map<String, DipConfigVo> dipConfigVoMap;

    /**
     * DRG支付标准
     */
    List<SomDrgStandard> drgBenchmarkList;

    /**
     * DIP支付标准
     */
    List<SomDipStandard> dipBenchmarkList;

    /**
     * drg床日病种支付标准
     */
    Map<String, DrgBedStandard> drgBedStandardMap;

    /**
     * drg床日疾病诊断配置
     */
    Map<String, BedDrgDiseCodeCfgVo> bedDrgDiseCodeCfgVoMap;

    /**
     * dip付费预测list对象
     */
    List<DipPayToPredictVo> dipPayToPredictVoList;

    /**
     * drg付费预测list对象
     */
    List<PayToPredictVo> payToPredictVoList;


    /**
     * 支付结果参数
     */
    List<Map<String, Object>> resultMap;

    /**
     * 参保地
     */
    String insuplcAdmdvs;

    /**
     * 区域病例均费
     */
    BigDecimal regiAverageFee;
    /**
     * 最小分值
     */
    BigDecimal minPointsSize;
    /**
     * 医疗机构DRG调整系数
     */
    BigDecimal hospCof;
    /**
     * 医疗机构床日调整系数
     */
    BigDecimal hospBeddayCof;

}
