package com.my.som.grouppay.bo;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

@Getter
@Setter
public class PayBusinessBO {
    /**
     * 病种历史三年均费
     **/
    private BigDecimal diseaseAvgFeeHist3Y;

    /**
     * 同级别病种三年均费
     **/
    private BigDecimal sameLevelDiseaseAvgFee;

    /**
     * 住院总费用
     **/
    private BigDecimal totalHospitalFee;

    /**
     * 同级别病种等级系数
     **/
    private BigDecimal diseaseLevelCoeff;

    /**
     * 机构系数
     **/
    private BigDecimal instCoeff;

    /**
     * 分值
     **/
    private BigDecimal scoreValue;

    /**
     * 全市均费
     **/
    private BigDecimal cityAvgFee;

    /**
     * DIP病种编码
     **/
    private String dipCode;

    /**
     * DRG病组编码
     **/
    private String drgCode;

    /**
     * 辅助目录分型
     **/
    private String auxType;

    /**
     * 辅助目录名称
     **/
    private String auxName;

    /**
     * 中药费占比
     **/
    private BigDecimal tcmFeeRatio;

    /**
     * 偏差倍率类型
     **/
    private String deviationRatioType;

    /**
     * 偏差倍率调节系数
     **/
    private BigDecimal deviationRatioAdjCoeff;

    /**
     * 偏差倍率判断系数
     **/
    private BigDecimal deviationRatioJudgCoeff;

    /**
     * 辅助目录调节系数
     **/
    private BigDecimal auxAdjCoeff;

    /**
     * 中医扶持系数
     **/
    private BigDecimal tcmSupportCoeff;

    /**
     * 病种支付类型
     **/
    private String payTypeOfDisease;

    /**
     * 参保类型
     **/
    private String insutype;

    /**
     * 职工单价
     **/
    private BigDecimal workerUnitPrice;

    /**
     * 居民单价
     **/
    private BigDecimal residentUnitPrice;

    /**
     * DIP支付标准
     **/
    private BigDecimal dipPayStd;

    /**
     * DIP偿付额（支付基金）
     **/
    private BigDecimal dipPaymentFund;


    /**
     * Drg支付标准
     **/
    private BigDecimal drgPayStd;

    /**
     * Drg偿付额（支付基金）
     **/
    private BigDecimal drgPaymentFund;

    /**
     * 盈亏额
     **/
    private BigDecimal diff;

    /**
     * 支付率
     **/
    private BigDecimal payRa;

    /**
     * 回款率
     **/
    private BigDecimal receRa;


}
