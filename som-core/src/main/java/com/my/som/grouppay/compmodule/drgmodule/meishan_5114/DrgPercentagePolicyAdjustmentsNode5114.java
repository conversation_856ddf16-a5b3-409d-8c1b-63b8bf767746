package com.my.som.grouppay.compmodule.drgmodule.meishan_5114;


import com.my.som.grouppay.vo.HospBaseBusiVo;

import com.my.som.vo.dipBusiness.DipPayToPredictVo;

import com.yomahub.liteflow.annotation.LiteflowComponent;
import com.yomahub.liteflow.core.NodeComponent;


import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

@LiteflowComponent(value = "DrgPercentagePolicyAdjustmentsNode5114", name = "政策调整")
public class DrgPercentagePolicyAdjustmentsNode5114 extends NodeComponent {
    @Override
    public void process() throws Exception {
        //根据入参计算是床日病种还是其它病种，床日病种需要从缓存中获取床日病种配置信息
        // 1.获取业务参数
        HospBaseBusiVo hospBaseBusiVo = this.getContextBean(HospBaseBusiVo.class);
        List<DipPayToPredictVo> scoreList = hospBaseBusiVo.getDipPayToPredictVoList();

        //取出之前被政策调整过的数据
        List<DipPayToPredictVo> updatedList = scoreList.stream()
                .filter(vo -> vo.getScoRemark() != null && !vo.getScoRemark().isEmpty())  // 筛选 remark 不为空
                .peek(vo -> vo.setScoRemark(null))
                .peek(vo -> vo.setScoForecastFee(vo.getScoPrice().multiply(vo.getScoTotlSco())))
                .peek(vo->vo.setScoProfitloss(vo.getScoForecastFee().subtract(vo.getScoSumfee())))// 将 remark 字段置空
                .collect(Collectors.toList())
                ;
        List<DipPayToPredictVo> filteredList = scoreList.stream()
                .filter(vo -> {
                    // 查 a56 前四位是否与 insuplcAdmdvs 相同
//                    if (vo.getYdjy() != null && !vo.getYdjy().isEmpty()) {
//                        return vo.getYdjy().equals("0");
//                    }
//                    // 如果 ydjy 为空，检
//                    else {
                        return vo.getA56().substring(0, 4).equals(vo.getInsuplcAdmdvs());
  //                  }
                })
                .collect(Collectors.toList());

// 排序过滤后的列表
        filteredList.sort(Comparator.comparing(DipPayToPredictVo::getScoProfitloss));

        int numberOfItems = (int) Math.round(filteredList.size() * 0.05);  // 5% 四舍五入
        List<DipPayToPredictVo> top5Percent = filteredList.subList(0, numberOfItems);
        LocalDate currentDate = LocalDate.now();
        top5Percent.stream()
                .peek(vo -> vo.setScoRemark(currentDate +"按照政策调整（亏损前5%的病例根据项目收费）"))
                .peek(vo -> vo.setScoForecastFee(vo.getScoSumfee()))
                .peek(vo->vo.setScoProfitloss(BigDecimal.ZERO))// 将 remark 字段置空
                .collect(Collectors.toList())
        ;
        updatedList.addAll(top5Percent);
        hospBaseBusiVo.setDipPayToPredictVoList(updatedList);
    }
}
