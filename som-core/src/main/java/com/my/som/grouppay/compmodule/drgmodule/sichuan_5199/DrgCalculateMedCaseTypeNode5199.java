package com.my.som.grouppay.compmodule.drgmodule.sichuan_5199;

import com.my.som.common.constant.DrgConst;
import com.my.som.common.util.ValidateUtil;
import com.my.som.grouppay.compmodule.dipmodule.common.PaymentConst;
import com.my.som.grouppay.vo.HospBaseBusiVo;
import com.my.som.util.SysCommonConfigUtil;
import com.my.som.vo.dipBusiness.DipPayToPredictVo;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import com.yomahub.liteflow.core.NodeComponent;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

@LiteflowComponent(value = "DrgCalculateMedCaseTypeNode5199", name = "计算病例类型")
public class DrgCalculateMedCaseTypeNode5199 extends NodeComponent {
    private final static String P000 = "P000";
    private final static String P0000 = "0000";
    @Override
    public void process() throws Exception {
        // 2.获取业务上下文
        HospBaseBusiVo hospBaseBusiVo = this.getContextBean(HospBaseBusiVo.class);
        List<DipPayToPredictVo> dipPayToPredictVoList = hospBaseBusiVo.getDipPayToPredictVoList();
        String MODIFY_OFF_SITE_CASE = (String) SysCommonConfigUtil.get(DrgConst.MODIFY_OFF_SITE_CASE);


        for (DipPayToPredictVo vo : dipPayToPredictVoList) {
            //加成系数暂时设置为1
            vo.setAddRate(BigDecimal.ONE);
            //根据政策调整，默认按DRG支付
            vo.setPayMentType(DrgConst.PAYMENT_TYPE_DRG);

            /*
             * (1)大于60天按实际费用折算点数支付
             */
            if (DrgConst.LONG_TERM_HOSP_DAYS.compareTo(vo.getInHosDays()) < 0) {
                vo.setDiseType(DrgConst.CASE_TYPE_TS_2);
                vo.setPayMentType(DrgConst.PAYMENT_TYPE_PROJECT_CONVER);
                // 判断是否属于异地
                if (MODIFY_OFF_SITE_CASE != null && "true".equals(MODIFY_OFF_SITE_CASE.toString().trim())) {
                    if(!vo.getIsLocal()){
                        vo.setPayMentType(DrgConst.PAYMENT_TYPE_PROJECT);
                    }
                }
                continue;
            }

            /*
             * (2)空白组、歧义组，结算点数=该病例实际费用÷所有病例的次均住院费用×100×0.9
             * (3)非稳定病组，结算点数=该病例实际费用÷所有病例的次均住院费用×100
             * */
            //1、未入组
            if (ValidateUtil.isEmpty(vo.getDrgCodg()) || ValidateUtil.isEmpty(vo.getDrgName()) || P000.equals(vo.getDrgCodg()) || P0000.equals(vo.getDrgCodg())) {
                //空白组
                vo.setDiseType(DrgConst.CASE_TYPE_NONE_GROUP);
                vo.setPayMentType(DrgConst.PAYMENT_TYPE_PROJECT_CONVER_RATIO);
                // 判断是否属于异地
                if (MODIFY_OFF_SITE_CASE != null && "true".equals(MODIFY_OFF_SITE_CASE.toString().trim())) {
                    if(!vo.getIsLocal()){
                        vo.setPayMentType(DrgConst.PAYMENT_TYPE_PROJECT);
                    }
                }
                continue;
            }

            //2、歧义组
            if (vo.getDrgCodg().endsWith(PaymentConst.CASE_TYPE_QY_KEY)) {
                vo.setDiseType(DrgConst.CASE_TYPE_QY_GROUP);
                vo.setPayMentType(DrgConst.PAYMENT_TYPE_PROJECT_CONVER_RATIO);
                continue;
            }


            //3、非稳定病组
            if (PaymentConst.IS_SD_DISE.equals(vo.getStableFlag())) {
                vo.setDiseType(DrgConst.CASE_TYPE_5);
                vo.setPayMentType(DrgConst.PAYMENT_TYPE_PROJECT_CONVER);
                // 判断是否属于异地
                if (MODIFY_OFF_SITE_CASE != null && "true".equals(MODIFY_OFF_SITE_CASE.toString().trim())) {
                    if(!vo.getIsLocal()){
                        vo.setPayMentType(DrgConst.PAYMENT_TYPE_PROJECT);
                    }
                }
                continue;
            }

            //无标杆
            if ( ValidateUtil.isEmpty(vo.getMax())  || ValidateUtil.isEmpty(vo.getMin())
                    || BigDecimal.ZERO.compareTo(vo.getMax()) == 0 || BigDecimal.ZERO.compareTo(vo.getMin()) == 0) {
                vo.setDiseType(DrgConst.CASE_TYPE_4);
                vo.setPayMentType(DrgConst.PAYMENT_TYPE_PROJECT);
                // 判断是否属于异地
                if (MODIFY_OFF_SITE_CASE != null && "true".equals(MODIFY_OFF_SITE_CASE.toString().trim())) {
                    if(!vo.getIsLocal()){
                        vo.setPayMentType(DrgConst.PAYMENT_TYPE_PROJECT);
                    }
                }
                continue;
            }

            /*
             * 正常病组高低倍率判断
             *（1）高倍率：>=1.4倍同级别均费,结算点数 =（该病例实际费用÷该等级医疗机构该 DRG 组次均住院费用-0.4）×该病组结算点数
             *（2）正常倍率：非高倍率和低倍率病例，结算点数=DRG 组基准点数×等级系数×调整系数
             *（3）低倍率：<=0.7倍同级别均费，结算点数=该病例实际费用÷所有病例的次均住院费用×100
             */
            //高倍率
            if (vo.getInHosTotalCost().compareTo(Optional.ofNullable(vo.getMax()).orElse(BigDecimal.ZERO)) >= 0) {
                if (ValidateUtil.isEmpty(vo.getDiseType())) {
                    if (Optional.ofNullable(vo.getMax()).orElse(BigDecimal.ZERO).compareTo(BigDecimal.ZERO) == 0) {
                        vo.setDiseType(DrgConst.CASE_TYPE_4);
                        vo.setPayMentType(DrgConst.PAYMENT_TYPE_PROJECT_CONVER);
                    } else {
                        vo.setDiseType(DrgConst.CASE_TYPE_1);
                        vo.setPayMentType(DrgConst.PAYMENT_TYPE_DRG);
                    }
                }
            }
            //低倍率
            if (vo.getInHosTotalCost().compareTo(Optional.ofNullable(vo.getMin()).orElse(BigDecimal.ZERO)) <= 0) {
                if (ValidateUtil.isEmpty(vo.getDiseType())) {
                    vo.setDiseType(DrgConst.CASE_TYPE_2);
                    vo.setPayMentType(DrgConst.PAYMENT_TYPE_DRG);
                }
            }
            //正常倍率
            if (vo.getInHosTotalCost().compareTo(Optional.ofNullable(vo.getMax()).orElse(BigDecimal.ZERO)) < 0 &&
                    vo.getInHosTotalCost().compareTo(Optional.ofNullable(vo.getMin()).orElse(BigDecimal.ZERO)) > 0) {
                if (ValidateUtil.isEmpty(vo.getMax()) || ValidateUtil.isEmpty(vo.getMin())) {
                    if (ValidateUtil.isEmpty(vo.getDiseType())) {
                        vo.setDiseType(DrgConst.CASE_TYPE_4);
                        vo.setPayMentType(DrgConst.PAYMENT_TYPE_PROJECT_CONVER);
                    }
                } else {
                    if (ValidateUtil.isEmpty(vo.getDiseType())) {
                        vo.setDiseType(DrgConst.CASE_TYPE_3);
                        vo.setPayMentType(DrgConst.PAYMENT_TYPE_DRG);
                    }
                }
            }


            // 判断是否属于异地
            if (MODIFY_OFF_SITE_CASE != null && "true".equals(MODIFY_OFF_SITE_CASE.toString().trim())) {
                if(!vo.getIsLocal()){
                    vo.setPayMentType(DrgConst.PAYMENT_TYPE_PROJECT);
                }
            }

            //设置是否启动月度单价
            vo.setRunMonthPrice(true);
            //设置调整系数(如执行数据中hospCof为null,则从执行上下文中获取并设置值)
            if (ValidateUtil.isEmpty(vo.getHospCof()) && !ValidateUtil.isEmpty(hospBaseBusiVo.getHospCof())) {
                //省市两级多分值适配，从分组配置表中获取
                vo.setHospCof(hospBaseBusiVo.getHospCof());
            }
        }
    }
}
