package com.my.som.grouppay.compmodule.dipmodule.deyang_5106;


import com.my.som.common.constant.DrgConst;
import com.my.som.common.util.ValidateUtil;
import com.my.som.grouppay.vo.HospBaseBusiVo;
import com.my.som.util.SysCommonConfigUtil;
import com.my.som.vo.dipBusiness.DipPayToPredictVo;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import com.yomahub.liteflow.core.NodeComponent;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

import static com.my.som.grouppay.compmodule.dipmodule.common.PaymentConst.DEFAULT_COST_0;
import static com.my.som.grouppay.compmodule.dipmodule.common.PaymentConst.EXTRE_COEFFICIENT;
import static com.my.som.util.GenerateScoreUtil.CASE_TYPE_6;

@LiteflowComponent(value = "DipCalculateMedCaseTypeNode5106", name = "病例类型判断")
public class DipCalculateMedCaseTypeNode5106 extends NodeComponent {



    private final static String IS_STABLE_FLAG = "0";

    @Override
    public void process() throws Exception {

        // 获取业务上下文
        HospBaseBusiVo hospBaseBusiVo = this.getContextBean(HospBaseBusiVo.class);
        List<DipPayToPredictVo> dipPayToPredictVoList = hospBaseBusiVo.getDipPayToPredictVoList();

        for (DipPayToPredictVo payToPredictVo : dipPayToPredictVoList) {

            BigDecimal magnification = BigDecimal.valueOf(2);
            if (payToPredictVo.getRefer_sco().compareTo(BigDecimal.valueOf(200)) > 0) {
                magnification = BigDecimal.valueOf(1.8);
            }
            // 倍率调节系数 = 病种级别均费 / 患者住院总费用 - 2/1.8 + 1
            payToPredictVo.setBiasRateFactor(magnification);
            genareteGroup(payToPredictVo);
            //设置是否启动月度单价
            payToPredictVo.setRunMonthPrice(false);
        }
    }


    /*
           1.不稳定病组：该病例医疗费用/全部费用均值*100
           2.辅助分型病例：
                      辅助分型调整结算分数=该病种辅助分型后费用均值*基本系数*辅助分型前费用均值
                      辅助分型调整系数=该病种辅助分型后费用均值/该病种辅助分型前费用均值
           3.费用偏差病例
                      低倍率结算分值：该病例医疗费用/该病例所在病种费用均值*该病种基准分值
                      高倍率结算分值：(该病例医疗费用/上年度同级别定点医疗机构该病种次均医疗费用-判定倍率+1)*该病种基准分值*基本系数*辅助分型调整系数
                                      判定倍率 基准分值 > 2 ? 1.8:2
           4.特殊病例
              1.极高费用(极长住院)病例
                      结算分值：该病例医疗费用/该病例所在病种均值*该病种基准分值
              2.采用已备案新医疗技术的病例

              3.未入组
          */
    private void genareteGroup(DipPayToPredictVo payToPredictVo) {
        BigDecimal lastYearLevelStandardCost = payToPredictVo.getLastYearLevelStandardCost()== new BigDecimal(0)?new BigDecimal(1):payToPredictVo.getLastYearLevelStandardCost();
        BigDecimal dipStandardInpf = payToPredictVo.getAreaStandardCost() == new BigDecimal(0)?new BigDecimal(1):payToPredictVo.getAreaStandardCost();

        //判断是否为医保病人
        if( ValidateUtil.isEmpty(payToPredictVo.getBusinessSerial() ) ) {
            payToPredictVo.setSumfee(payToPredictVo.getInHospCost().add(payToPredictVo.getPreHospExamfee()));
        }else{
            if(ValidateUtil.isEmpty(payToPredictVo.getCheckTotalFee()) || BigDecimal.ZERO.compareTo(payToPredictVo.getCheckTotalFee())== 0 ){
                payToPredictVo.setSumfee(payToPredictVo.getInHospCost().add(payToPredictVo.getPreHospExamfee()));
            }else{
            payToPredictVo.setSumfee(payToPredictVo.getCheckTotalFee().add(payToPredictVo.getPreHospExamfee()) );}
        }

        if (ValidateUtil.isEmpty(payToPredictVo.getPaymentDiseType())) {
            if (ValidateUtil.isEmpty(payToPredictVo.getDipCodg())) {
                // 未入组
                payToPredictVo.setDiseType(DrgConst.DISE_TYPE_0);
                payToPredictVo.setPaymentDiseType(DrgConst.DISETYPE_NONE_INGROUP);

            } else if (DrgConst.DIS_BASE_1.equals(payToPredictVo.getIs_base_dise())) {
                // 基层病种
                payToPredictVo.setDiseType(DrgConst.DISE_TYPE_7);
                payToPredictVo.setPaymentDiseType(DrgConst.DISETYPE_BASIC);
            } else if (DrgConst.DIS_CHM_1.equals(payToPredictVo.getIs_tcm_dise())) {
                // 中治率计算
                processTcm(payToPredictVo);
                if (payToPredictVo.getTCMRatio() != null && payToPredictVo.getTCMRatio().compareTo(new BigDecimal(0.3)) > 0) {
                    // 中医病种
                    payToPredictVo.setPaymentDiseType(DrgConst.DISETYPE_CHM);
                } else {
                    // 核心病种
                    payToPredictVo.setPaymentDiseType(DrgConst.DISETYPE_CORE);
                }
            } else if (IS_STABLE_FLAG.equals(payToPredictVo.getStableFlag())) {
                //非稳定
                payToPredictVo.setDiseType(DrgConst.DISE_TYPE_5);
                payToPredictVo.setPaymentDiseType(DrgConst.DISETYPE_INSTABILITY);

            } else if (Optional.ofNullable(payToPredictVo.getMax()).orElse(BigDecimal.ZERO).compareTo(BigDecimal.ZERO) == 0
                    || Optional.ofNullable(payToPredictVo.getMin()).orElse(BigDecimal.ZERO).compareTo(BigDecimal.ZERO) == 0) {
                //无标杆
                payToPredictVo.setDiseType(DrgConst.DISE_TYPE_4);
                payToPredictVo.setPaymentDiseType(DrgConst.DISETYPE_NO_BENCHMARK);

            }   // 判断是否为极高费用病例
            else if ((payToPredictVo.getAreaStandardCost() != null && payToPredictVo.getAreaStandardCost().compareTo(DEFAULT_COST_0) != 0
                    && (payToPredictVo.getSumfee()
                    .divide(dipStandardInpf, 8, BigDecimal.ROUND_HALF_UP).compareTo(EXTRE_COEFFICIENT) >= 0))){
                payToPredictVo.setDiseType(DrgConst.DISE_TYPE_6);
                payToPredictVo.setPaymentDiseType(DrgConst.DISETYPE_EXTREMELYE_HIGH);
            }
            else if (payToPredictVo.getInHosDays()!= null
                    && payToPredictVo.getInHosDays().compareTo(DEFAULT_COST_0) != 0
                    &&payToPredictVo.getLevelStandardDays()!= null
                    &&(new BigDecimal(payToPredictVo.getLevelStandardDays()).compareTo(DEFAULT_COST_0) != 0)
                    && payToPredictVo.getInHosDays().divide(new BigDecimal(payToPredictVo.getLevelStandardDays()), 8, BigDecimal.ROUND_HALF_UP).compareTo(EXTRE_COEFFICIENT) >= 0){
                payToPredictVo.setDiseType(DrgConst.DISE_TYPE_6);
                payToPredictVo.setPaymentDiseType(DrgConst.DISETYPE_EXTREMELYE_HIGH);
            } else {
                // 判断超高
                if (payToPredictVo.getSumfee()
                        .divide(lastYearLevelStandardCost,8, BigDecimal.ROUND_HALF_UP).compareTo(payToPredictVo.getBiasRateFactor()) > 0 ) {
                    // 高倍率
                    payToPredictVo.setPayMentType(DrgConst.PAYMENT_TYPE_DIP);
                    if (ValidateUtil.isEmpty(payToPredictVo.getPaymentDiseType())) {
                        payToPredictVo.setDiseType(DrgConst.DISE_TYPE_1);
                        payToPredictVo.setPaymentDiseType(DrgConst.DISETYPE_HIGH_COMPUTE);
                    }
                }
                // 判断超低
                else if (payToPredictVo.getSumfee()
                        .divide(lastYearLevelStandardCost, 8, BigDecimal.ROUND_HALF_UP).compareTo(new BigDecimal("0.5")) < 0 )
                  {
                    if (ValidateUtil.isEmpty(payToPredictVo.getPaymentDiseType())) {
                        payToPredictVo.setDiseType(DrgConst.DISE_TYPE_2);
                        payToPredictVo.setPaymentDiseType(DrgConst.DISETYPE_LOW_COMPUTE);
                    }
                } else if (ValidateUtil.isNotEmpty(payToPredictVo.getPaymentDiseType())) {
                    ///2.辅助/ 核心 / 综合 正常病种 没有对应的判断方式
                    payToPredictVo.setPaymentDiseType(payToPredictVo.getDisType());
                } else {
                    //设置默认正常病种
                    payToPredictVo.setPaymentDiseType(DrgConst.DISETYPE_NORMAL);
                    payToPredictVo.setDiseType(DrgConst.DISE_TYPE_3);
                }
            }
        }

    }

    /**
     * 中治率计算
     * （中草药费 + 中医治疗费）/ 住院总费用
     */
    private void processTcm(DipPayToPredictVo payToPredictVo) {
        BigDecimal tcmTreatmentCost = payToPredictVo.getTCMTreatmentCost();
        BigDecimal ra = tcmTreatmentCost.divide(payToPredictVo.getSumfee(), BigDecimal.ROUND_HALF_UP).setScale(2, BigDecimal.ROUND_HALF_UP);
        payToPredictVo.setTCMRatio(ra);
    }
}
