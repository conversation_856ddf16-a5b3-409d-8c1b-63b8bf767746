package com.my.som.grouppay.compmodule.drgmodule.mianyang_5107;

import com.my.som.common.constant.DrgConst;
import com.my.som.common.util.ValidateUtil;
import com.my.som.grouppay.vo.BedDrgDiseCodeCfgVo;
import com.my.som.grouppay.vo.HospBaseBusiVo;
import com.my.som.model.dataHandle.DrgBedStandard;
import com.my.som.vo.dataConfig.ViewHospitalVo;
import com.my.som.vo.dipBusiness.DipPayToPredictVo;
import com.my.som.vo.pregroup.PreGroupVo;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import com.yomahub.liteflow.core.NodeComponent;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@LiteflowComponent(value = "DrgCalculateDiseGroupTypeNode5107", name = "计算病组类型")
public class DrgCalculateDiseGroupTypeNode5107 extends NodeComponent {
    @Override
    public void process() throws Exception {
        //根据入参计算是床日病种还是其它病种，床日病种需要从缓存中获取床日病种配置信息
        // 1.获取业务参数
        List<PreGroupVo> preGroupVos = this.getRequestData();
        // 2.获取业务上下文
        HospBaseBusiVo hospBaseBusiVo = this.getContextBean(HospBaseBusiVo.class);
        /**
         *  判断床日与正常病种
         *  1、获取主要诊断编码，判断是否在床日病种诊断编码范围，在则进入床日类型（精神类）BS/（康复类）BR
         *  2、判断是否住院天数大于60天，大于则进入（住院超60日）BL床日
         *  3、判断是否入组成功
         *      正常病组：
         *      异常病组：未DRG入组成功、无支付标杆组、非稳定病组
         */
        //医疗机构编码
        String hospitalId = hospBaseBusiVo.getHospitalId();
        ViewHospitalVo viewHospitalVo = hospBaseBusiVo.getHospitalVoMap().get(hospitalId);
        //获取床日信息
        Map<String, BedDrgDiseCodeCfgVo> bedDrgDiseCodeCfgVoMap = hospBaseBusiVo.getBedDrgDiseCodeCfgVoMap();
        Map<String, DrgBedStandard> drgBedStandardMap = hospBaseBusiVo.getDrgBedStandardMap();
        DrgBedStandard drgBedStandard;
        //组织计算病例类型及分值参数
        List<DipPayToPredictVo> scoreList = new ArrayList<>();
        boolean is_bed_dise = false;
        BigDecimal inHosDays;
        String mainDiseCode;
        for (PreGroupVo preGroupVo : preGroupVos) {
            //预估费用对象
            DipPayToPredictVo drgPayToPredictVo = new DipPayToPredictVo();
            //获取住院天数
            inHosDays = preGroupVo.getPayToPredictBaseInfoVo().getInHosDays();
            //获取主要诊断
            mainDiseCode = preGroupVo.getPayToPredictBaseInfoVo().getMainDiagnoseCode();
            //设置主要诊断
            drgPayToPredictVo.setDiagnoseCode(mainDiseCode);
            if (ValidateUtil.isNotEmpty(preGroupVo.getDrgCodg())) {
                //取第二位，判断是否为内科组
                String internal_key = preGroupVo.getDrgCodg().substring(1, 2);
                if (DrgConst.INTERNAL_MEDICINE_GROUP_LIST.contains(internal_key)) {
                /*床日病例分类。住院天数超过 60 天的精神类疾病内科组病例、住院超过 15 天的内科组康复病例、单次住院天数超过 60 天的
        内科组长期住院病例纳入床日点数付费管理。单次住院不能拆分为 DRG 点数付费和床日点数付费结算;*/
                    if (!ValidateUtil.isEmpty(bedDrgDiseCodeCfgVoMap)) {
                        BedDrgDiseCodeCfgVo bedDrgDiseCodeCfgVo = bedDrgDiseCodeCfgVoMap.get(mainDiseCode);
                        if (!ValidateUtil.isEmpty(bedDrgDiseCodeCfgVo)) {
                            //判断是否满足床日类型 1、住院天数超过 60 天的精神类疾病内科组病例；住院超过 15 天的内科组康复病例
                            if (DrgConst.HOSP_BED_TYPE_MENTAL.equals(bedDrgDiseCodeCfgVo.getBed_type()) && (DrgConst.LONG_TERM_HOSP_DAYS.compareTo(inHosDays) < 0)) {
                                is_bed_dise = true;
                            } else if (DrgConst.HOSP_BED_TYPE_RECOVERY.equals(bedDrgDiseCodeCfgVo.getBed_type()) && (DrgConst.RECOVERY_HOSP_DAYS.compareTo(inHosDays) < 0)) {
                                is_bed_dise = true;
                            }
                            if (is_bed_dise) {
                                //设置床日病种类型及病种编码
                                drgPayToPredictVo.setBed_type(bedDrgDiseCodeCfgVo.getBed_type());
                                String bed_dise_code = bedDrgDiseCodeCfgVo.getBed_type() + "0" + viewHospitalVo.getHospLv();
                                drgPayToPredictVo.setBed_dise_codg(bed_dise_code);
                                drgBedStandard = drgBedStandardMap.get(bed_dise_code);
                                drgPayToPredictVo.setBed_refer_sco(drgBedStandard.getBed_stand_val());
                            }
                        }
                    }
                    //判断是否满足超长床日
                    if (ValidateUtil.isEmpty(drgPayToPredictVo.getBed_type())) {
                        if (DrgConst.LONG_TERM_HOSP_DAYS.compareTo((inHosDays)) < 0) {
                            //大于60天
                            drgPayToPredictVo.setBed_type(DrgConst.HOSP_BED_TYPE_LONG_TERM);
                            String bed_dise_code = DrgConst.HOSP_BED_TYPE_LONG_TERM + "0" + viewHospitalVo.getHospLv();
                            drgPayToPredictVo.setBed_dise_codg(bed_dise_code);
                            drgBedStandard = drgBedStandardMap.get(bed_dise_code);
                            drgPayToPredictVo.setBed_refer_sco(drgBedStandard.getBed_stand_val());
                        }
                    }
                }
                drgPayToPredictVo.setDrgCodg(preGroupVo.getDrgCodg());
                drgPayToPredictVo.setDrgName(preGroupVo.getDrgName());
                drgPayToPredictVo.setInHosTotalCost(preGroupVo.getPayToPredictBaseInfoVo().getInHosTotalCost());
                drgPayToPredictVo.setAge(preGroupVo.getPayToPredictBaseInfoVo().getAge());
                drgPayToPredictVo.setDeptCode(preGroupVo.getPayToPredictBaseInfoVo().getDeptCode());
                drgPayToPredictVo.setWMCode(preGroupVo.getDiagnoseCode());
                drgPayToPredictVo.setDiagnoseCode(preGroupVo.getDiagnoseCode());
                drgPayToPredictVo.setTCMCode(preGroupVo.getPayToPredictBaseInfoVo().getTCMCode());
                drgPayToPredictVo.setHospitalId(preGroupVo.getPayToPredictBaseInfoVo().getHospitalId());
                drgPayToPredictVo.setMax(preGroupVo.getHighFee());
                drgPayToPredictVo.setMin(preGroupVo.getMinFee());
                drgPayToPredictVo.setRefer_sco(preGroupVo.getRefer_sco());
                drgPayToPredictVo.setInsuType(preGroupVo.getPayToPredictBaseInfoVo().getInsuType());
                drgPayToPredictVo.setInHosDays(inHosDays);
                //如果是基层病种则，系数设置为1
                if (DrgConst.IS_BASE_DISE_1.equals(preGroupVo.getIs_base_dise())) {
                    drgPayToPredictVo.setAdjm_cof(new BigDecimal(1));
                } else {
                    drgPayToPredictVo.setAdjm_cof(preGroupVo.getAdjm_cof());
                }
                drgPayToPredictVo.setLastYearLevelStandardCost(preGroupVo.getNearAvgCost());
                drgPayToPredictVo.setUplmtMag(preGroupVo.getUplmtMag());
                drgPayToPredictVo.setHospLv(viewHospitalVo.getHospLv());
                drgPayToPredictVo.setMedinsType(viewHospitalVo.getMedinsType());
                drgPayToPredictVo.setLevelStandardCost(preGroupVo.getDrgAvgCost());
                drgPayToPredictVo.setYm(preGroupVo.getPayToPredictBaseInfoVo().getYm());
                drgPayToPredictVo.setPreHospExamfee(BigDecimal.ZERO);
                drgPayToPredictVo.setHospCof(viewHospitalVo.getHospCof());
                drgPayToPredictVo.setIs_base_dise(preGroupVo.getIs_base_dise());
                drgPayToPredictVo.setStandardFee(preGroupVo.getDrgAvgCost());
            } else {
                //未入组
                drgPayToPredictVo.setInHosTotalCost(preGroupVo.getPayToPredictBaseInfoVo().getInHosTotalCost());
                drgPayToPredictVo.setAge(preGroupVo.getPayToPredictBaseInfoVo().getAge());
                drgPayToPredictVo.setDeptCode(preGroupVo.getPayToPredictBaseInfoVo().getDeptCode());
                drgPayToPredictVo.setWMCode(preGroupVo.getDiagnoseCode());
                drgPayToPredictVo.setDiagnoseCode(preGroupVo.getDiagnoseCode());
                drgPayToPredictVo.setTCMCode(preGroupVo.getPayToPredictBaseInfoVo().getTCMCode());
                drgPayToPredictVo.setHospitalId(preGroupVo.getPayToPredictBaseInfoVo().getHospitalId());
                drgPayToPredictVo.setRefer_sco(preGroupVo.getRefer_sco());
                drgPayToPredictVo.setAdjm_cof(preGroupVo.getAdjm_cof());
                drgPayToPredictVo.setLastYearLevelStandardCost(preGroupVo.getNearAvgCost());
                drgPayToPredictVo.setUplmtMag(preGroupVo.getUplmtMag());
                drgPayToPredictVo.setHospLv(viewHospitalVo.getHospLv());
                drgPayToPredictVo.setMedinsType(viewHospitalVo.getMedinsType());
                drgPayToPredictVo.setYm(preGroupVo.getPayToPredictBaseInfoVo().getYm());
                drgPayToPredictVo.setPreHospExamfee(BigDecimal.ZERO);
                drgPayToPredictVo.setHospCof(viewHospitalVo.getHospCof());
                drgPayToPredictVo.setInsuType(preGroupVo.getPayToPredictBaseInfoVo().getInsuType());
            }
            scoreList.add(drgPayToPredictVo);
        }
        hospBaseBusiVo.setDipPayToPredictVoList(scoreList);
    }
}
