package com.my.som.grouppay.compmodule.drgmodule.yaan5118;

import com.my.som.common.constant.DrgConst;
import com.my.som.common.util.ValidateUtil;
import com.my.som.grouppay.vo.HospBaseBusiVo;
import com.my.som.vo.dipBusiness.DipPayToPredictVo;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import com.yomahub.liteflow.core.NodeComponent;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;

@LiteflowComponent(value = "DrgCalculateGeneratePointsNode5118", name = "计算病例分值")
public class DrgCalculateGeneratePointsNode5118 extends NodeComponent {
    @Override
    public void process() throws Exception {

        // 2.获取业务上下文
        HospBaseBusiVo hospBaseBusiVo = this.getContextBean(HospBaseBusiVo.class);
        BigDecimal regiAverageFee  =  hospBaseBusiVo.getRegiAverageFee();
        BigDecimal hospCof = hospBaseBusiVo.getHospCof();
        List<DipPayToPredictVo> dipPayToPredictVoList = hospBaseBusiVo.getDipPayToPredictVoList();
        for (DipPayToPredictVo vo : dipPayToPredictVoList) {

            BigDecimal standardFee;
            if (!ValidateUtil.isEmpty(vo.getStandardFee()) && BigDecimal.ZERO.compareTo(vo.getStandardFee()) != 0) {
                standardFee = vo.getStandardFee();
            } else {
                standardFee = vo.getLevelStandardCost();
            }

            /*
            、住院天数大于 60 天的病例，其支付点数=
        长期住院病例点数=(该病例实际发生住院医疗费用-该病例不合理住院医疗费用):所有病组次均住院医疗费用x100。
             */

            if(DrgConst.CASE_TYPE_TS_2.equals(vo.getDiseType())){
                vo.setCalculateScore(vo.getInHosTotalCost().divide(regiAverageFee, 4,BigDecimal.ROUND_CEILING).multiply(new BigDecimal(100)));
            }
            //DGR病例计算
            /*高倍率分值计算= 对应的DRG基准点数x医院等级基准系数
            +(该病例实际发生医疗费用/本病组次均住院费用-病组高倍率判定倍率)x对应的DRG基准点数
            */
            /*20240919应客服要求更改分值计算方式为  基准点数*差异系数

            * */
            if (DrgConst.CASE_TYPE_1.equals(vo.getDiseType())) {
                BigDecimal  sco= vo.getRefer_sco();
                BigDecimal uplmtMag =  vo.getUplmtMag();
                BigDecimal refer = vo.getInHosTotalCost().divide(standardFee, 4,BigDecimal.ROUND_CEILING).subtract(uplmtMag);

                BigDecimal referSco = sco.multiply(hospCof.add(refer)).setScale(4, RoundingMode.HALF_UP);
                vo.setCalculateScore(referSco);
            }
            //低倍率病例支付点数=DRG 基准点数x(病例住院总费用÷该 DRG 组次均费用)，最高不得超过该 DRG 基准点数。
            if (DrgConst.CASE_TYPE_2.equals(vo.getDiseType())) {
                BigDecimal sco =vo.getRefer_sco().multiply(hospCof).multiply(vo.getInHosTotalCost().divide(standardFee, 4,BigDecimal.ROUND_CEILING));
                BigDecimal referSco = sco.setScale(4, RoundingMode.HALF_UP);
                BigDecimal maxSco = vo.getRefer_sco().multiply(hospCof).setScale(4, RoundingMode.HALF_UP);
                if(referSco.compareTo(maxSco)>0){
                    referSco = maxSco;
                }
                vo.setCalculateScore(referSco);
            }

            //正常病例分值计算 = 基准点数*差异系数
            if (DrgConst.CASE_TYPE_3.equals(vo.getDiseType())) {
                vo.setCalculateScore(vo.getRefer_sco().multiply(hospCof).setScale(4, BigDecimal.ROUND_HALF_UP) );
            }

            if (DrgConst.DISE_TYPE_8.equals(vo.getDiseType())) {
                BigDecimal sco =vo.getRefer_sco().multiply(hospCof).multiply(vo.getInHosTotalCost().divide(standardFee, 4,BigDecimal.ROUND_CEILING));
                BigDecimal referSco = sco.setScale(4, RoundingMode.HALF_UP);
                BigDecimal maxSco = vo.getRefer_sco().multiply(hospCof).setScale(4, RoundingMode.HALF_UP);
                if(referSco.compareTo(maxSco)>0){
                    referSco = maxSco;
                }
                vo.setCalculateScore(referSco);
            }
            if (DrgConst.CASE_TYPE_CHILD_REHAB.equals(vo.getDiseType())) {
                vo.setCalculateScore(vo.getInHosTotalCost().divide(regiAverageFee, 4,BigDecimal.ROUND_CEILING).multiply(new BigDecimal(100)));

            }
                vo.setAddScore(BigDecimal.ZERO);
                vo.setTotlSco(vo.getCalculateScore().add(vo.getAddScore()));

        }
    }
}
