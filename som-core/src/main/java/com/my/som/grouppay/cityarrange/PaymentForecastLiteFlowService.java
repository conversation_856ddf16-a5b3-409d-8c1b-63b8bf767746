package com.my.som.grouppay.cityarrange;

import com.my.som.dto.medicalQuality.BusSettleListMainInfo;
import com.my.som.dto.medicalQuality.SettleListMainInfoQueryParam;
import com.my.som.dto.pregroup.PreGroupDto;
import com.my.som.grouppay.vo.HospBaseBusiVo;
import com.my.som.vo.dipBusiness.DipPayToPredictVo;
import com.my.som.vo.pregroup.PreGroupVo;

import java.util.List;
import java.util.Map;

public interface PaymentForecastLiteFlowService {
    /**
     * DRG获取支付预测基本参数信息
     *
     * @param preGroupDto
     * @return
     */
    HospBaseBusiVo getDrgHospBaseAndStandardsCfgInfo(PreGroupDto preGroupDto);

    /**
     * DRG预分组-执行生成分值以及支付费用预测-
     *
     * @param preGroupVos
     * @param hospBaseBusiVo
     * @return
     */
    HospBaseBusiVo drgGenerateScoreAndForecastCostByPreGroup(List<PreGroupVo> preGroupVos, PreGroupDto preGroupDto, HospBaseBusiVo hospBaseBusiVo);

    /**
     * DRG业务流程-执行生成分值以及支付费用预测
     *
     * @param hospBaseBusiVo
     * @return
     */
    HospBaseBusiVo drgGenerateScoreAndForecastCostByBusiProc(HospBaseBusiVo hospBaseBusiVo);

    /**
     * DRG获取支付预测基本参数信息
     *
     * @param preGroupDto
     * @return
     */
    HospBaseBusiVo getDipHospBaseAndStandardsCfgInfo(PreGroupDto preGroupDto);

    /**
     * DIP预分组-执行生成分值以及支付费用预测-
     *
     * @param preGroupVos
     * @param hospBaseBusiVo
     * @return
     */
    HospBaseBusiVo dipGenerateScoreAndForecastCostByPreGroup(List<PreGroupVo> preGroupVos, PreGroupDto preGroupDto, HospBaseBusiVo hospBaseBusiVo, Map<String, Object> calculateScoreParams);


    /**
     * DIP业务流程-执行生成分值以及支付费用预测
     *
     * @param hospBaseBusiVo
     * @return
     */
    HospBaseBusiVo dipGenerateScoreAndForecastCostByBusiProc(HospBaseBusiVo hospBaseBusiVo, Map<String, Object> calculateScoreParams);

    /**
     * 实现兼容不同地区的不同政策的 费用调整
     *
     * @param hospBaseBusiVo
     * @return
     */
    List<DipPayToPredictVo> PercentagePolicyAdjustments (HospBaseBusiVo hospBaseBusiVo);

    /**
     * 实现兼容不同地区的特例病例 费用调整
     *
     * @param
     * @return
     */
    List<BusSettleListMainInfo> querySpecialDisease (List<BusSettleListMainInfo> busSettleListMainInfos, SettleListMainInfoQueryParam queryParam);
}
