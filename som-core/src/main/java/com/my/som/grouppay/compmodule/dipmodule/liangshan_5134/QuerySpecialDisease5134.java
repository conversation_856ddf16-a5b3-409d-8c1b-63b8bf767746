package com.my.som.grouppay.compmodule.dipmodule.liangshan_5134;

import com.alibaba.fastjson.JSON;
import com.my.som.common.util.ValidateUtil;
import com.my.som.dto.medicalQuality.BusSettleListMainInfo;
import com.my.som.dto.medicalQuality.SettleListMainInfoQueryParam;
import com.my.som.grouppay.vo.HospBaseBusiVo;
import com.my.som.vo.dipBusiness.DipPayToPredictVo;
import com.my.som.vo.pregroup.PreGroupVo;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import com.yomahub.liteflow.core.NodeComponent;
import springfox.documentation.spring.web.json.Json;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@LiteflowComponent(value = "QuerySpecialDisease5134", name = "特病单议汇总")
public class QuerySpecialDisease5134 extends NodeComponent {
    /**
     * (一) 重症及多学科诊治病例:危急症抢救患者重症监护病房床位使用天数大于3天且大于当次住院床位使用总天数60%的，
            或者重症监护病房床位使用天数大于15天的;
     * (二) 超长住院病例: 住院天数大于60日，或者平均住院天数大于等于5天 且住院天数大于该病种平均住院天数5倍以上的;
     * (三) 超高费用病例:单次住院医疗费用超过所对应病种次均医疗总费用5倍及以上的;
     */

    @Override
    public void process() throws Exception {
        List<BusSettleListMainInfo> busSettleListMainInfos = this.getRequestData();
        SettleListMainInfoQueryParam param = this.getContextBean(SettleListMainInfoQueryParam.class);
        List<BusSettleListMainInfo> resultList = busSettleListMainInfos.stream()
                    .map(info -> {
                        if(ValidateUtil.isEmpty(info.getB20())) {
                            info.setB20(0);
                        }
                        if( ValidateUtil.isEmpty(info.getAvgDays())){
                            info.setAvgDays(0.00);
                        }
                        if(ValidateUtil.isEmpty(info.getSumfee())){
                            info.setSumfee(0.00);
                        }
                        if(   ValidateUtil.isEmpty(info.getAvgFee())){
                            info.setAvgFee(0.00);
                        }
                            info.setRefeFee(info.getAvgFee());
                            String type = "" ;
                            String desc = "" ;
                            
                            if (info.getTotalDays() >= 15) {
                                type +="重症及多学科诊治病例;";
                                desc += "重症监护病房床位使用天数大于15天;";
                            }
                             if (info.getTotalDays() < 15 && info.getTotalDays()> 3  &&  info.getTotalDays() > info.getB20() * 0.6) {
                                type +="重症及多学科诊治病例;";
                                desc +="重症监护病房床位使用天数大于3天且大于当次住院床位使用总天数60%;";
                            }
                             if (info.getB20() > 60) {
                                type +="超长住院病例;";
                                desc +="住院超过60天;";
                            }

                            if (info.getB20() <= 60 && info.getAvgDays() >=5 && info.getB20() > info.getAvgDays() * 5) {
                                type +="超长住院病例;";
                                desc +="住院超过病种平均住院天数5倍以上;";
                            }
                             if (info.getSumfee() > info.getRefeFee() * 5) {
                                type +="超高费用病例;";
                                desc +="住院费用超过所对应病种次均医疗总费用5倍及以上;";
                            }
                             if(!ValidateUtil.isEmpty(type)){
                                 info.setType(type);
                                 info.setDesc(desc);
                             }

                        return info;
                    })
                // 过滤掉不满足条件的数据
                    .filter(info -> info.getType() != null )

                .filter(info -> {
                    // 根据 param.getScsMDT() 决定是否添加额外的过滤条件
                    if (!ValidateUtil.isEmpty(param.getScsMDT()) && "1".equals(param.getScsMDT())) {
                        return info.getType().contains("重症及多学科诊治病例");
                    } else {
                        return true; // 保留所有满足 type != null 的记录
                    }
                })
                .filter(info -> {
                    // 根据 param.getScsMDT() 决定是否添加额外的过滤条件
                    if (!ValidateUtil.isEmpty(param.getIsUltraLong()) &&  "1".equals(param.getIsUltraLong())) {
                        return info.getType().contains("超长住院病例");
                    } else {
                        return true; // 保留所有满足 type != null 的记录
                    }
                })
                .filter(info -> {
                    // 根据 param.getScsMDT() 决定是否添加额外的过滤条件
                    if (!ValidateUtil.isEmpty(param.getIsUltraHigh()) && "1".equals(param.getIsUltraHigh())) {
                        return info.getType().contains("超高费用病例");
                    } else {
                        return true; // 保留所有满足 type != null 的记录
                    }
                })
                .collect(Collectors.toList());
        busSettleListMainInfos.clear();
        busSettleListMainInfos.addAll(resultList);
    }
}
