package com.my.som.grouppay.compmodule.drgmodule.guangan5116;

import com.my.som.common.constant.DrgConst;
import com.my.som.common.util.ValidateUtil;
import com.my.som.grouppay.vo.HospBaseBusiVo;
import com.my.som.vo.dipBusiness.DipPayToPredictVo;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import com.yomahub.liteflow.core.NodeComponent;

import java.math.BigDecimal;
import java.util.List;


@LiteflowComponent(value = "DrgCalculateMedCaseTypeNode5116", name = "计算病例类型")
public class DrgCalculateMedCaseTypeNode5116 extends NodeComponent {

    private final static String IS_STABLE_FLAG = "0";
    private final static String P000 = "P000";
    private final static String P0000 = "0000";

    @Override
    public void process() throws Exception {
        // 2.获取业务上下文
        HospBaseBusiVo hospBaseBusiVo = this.getContextBean(HospBaseBusiVo.class);

        List<DipPayToPredictVo> dipPayToPredictVoList = hospBaseBusiVo.getDipPayToPredictVoList();
        for (DipPayToPredictVo vo : dipPayToPredictVoList) {

            BigDecimal standardFee;
            if (!ValidateUtil.isEmpty(vo.getStandardFee()) && BigDecimal.ZERO.compareTo(vo.getStandardFee()) != 0) {
                standardFee = vo.getStandardFee();
            } else {
                standardFee = vo.getLevelStandardCost();
            }

            //加成系数暂时设置为1
            vo.setAddRate(BigDecimal.ONE);
            //根据政策调整，默认按DRG支付
            vo.setPayMentType(DrgConst.PAYMENT_TYPE_DRG);
            if (ValidateUtil.isEmpty(vo.getDrgCodg()) || ValidateUtil.isEmpty(vo.getDrgName()) || P000.equals(vo.getDrgCodg()) || P0000.equals(vo.getDrgCodg())) {
                //未入组
                vo.setDiseType(DrgConst.CASE_TYPE_NONE_GROUP);
            }


            //1、支付类型特殊判断：是否是日间手术 IPT_MED_TYPE_2= '2',
//            if (DrgConst.IPT_MED_TYPE_2.equals(vo.getIptMedType())) {
//                if (Optional.ofNullable(vo.getMax()).orElse(BigDecimal.ZERO).compareTo(BigDecimal.ZERO) == 0) {
//                    vo.setDiseType(DrgConst.CASE_TYPE_4);
//                    vo.setPayMentType(DrgConst.PAYMENT_TYPE_DRG);
//                } else {
//                    vo.setDiseType(DrgConst.CASE_TYPE_TS_1);
//                }
//            }

            //2、支付类型特殊判断：是否住院天数<=1、住院天数大于60
            if (DrgConst.LONG_TERM_HOSP_DAYS.compareTo(vo.getInHosDays()) < 0 ) {
                vo.setDiseType(DrgConst.CASE_TYPE_TS_2);
                vo.setPayMentType(DrgConst.PAYMENT_TYPE_DRG);
            }
            /**
             * 先判断是否未非稳定病例
             */
            if (IS_STABLE_FLAG.equals(vo.getStableFlag())) {
                //非稳定
                if (ValidateUtil.isEmpty(vo.getDiseType())) {
                    vo.setDiseType(DrgConst.DISE_TYPE_5);
                    vo.setPayMentType(DrgConst.PAYMENT_TYPE_DRG);
                }
            } else {
                if (ValidateUtil.isEmpty(vo.getDiseType())) {
                    judgeHighMagnification(vo, standardFee);
                }
                //高倍率

                //低倍率
                if (ValidateUtil.isEmpty(vo.getDiseType())) {
                    if (vo.getInHosTotalCost().divide(standardFee, 8, BigDecimal.ROUND_HALF_UP).compareTo(new BigDecimal(0.4)) <= 0) {
                            vo.setDiseType(DrgConst.CASE_TYPE_2);
                            vo.setPayMentType(DrgConst.PAYMENT_TYPE_DRG);
                    } else {
                        if (ValidateUtil.isEmpty(vo.getMax()) || ValidateUtil.isEmpty(vo.getMin())) {
                                vo.setDiseType(DrgConst.CASE_TYPE_4);
                                vo.setPayMentType(DrgConst.PAYMENT_TYPE_DRG);
                        } else {
                                vo.setDiseType(DrgConst.CASE_TYPE_3);
                        }
                    }
                }
            }
            //设置是否启动月度单价
            vo.setRunMonthPrice(false);
        }
    }

    /**
     * 判断是否为高倍率
     */
    private void judgeHighMagnification(DipPayToPredictVo vo, BigDecimal standardFee) {
        BigDecimal referSco = vo.getRefer_sco();
        BigDecimal inHosTotalCost = vo.getInHosTotalCost();

        // 计算比例
        BigDecimal ratio = inHosTotalCost.divide(standardFee, 8, BigDecimal.ROUND_HALF_UP);

        BigDecimal ratioThreshold = BigDecimal.ZERO; // 初始值

// 根据refer_sco范围设置阈值
        if (referSco.compareTo(new BigDecimal("100")) < 0) {
            ratioThreshold = new BigDecimal(3);
        } else if (referSco.compareTo(new BigDecimal("100")) >= 0 && referSco.compareTo(new BigDecimal("200")) < 0) {
            ratioThreshold = new BigDecimal(2);
        } else if (referSco.compareTo(new BigDecimal("200")) >= 0 ) {
            ratioThreshold = new BigDecimal(1.5);
        }
        if (BigDecimal.ZERO.compareTo(ratioThreshold) == 0) {
            return;
        }
// 比较ratio与阈值
        if (ratio.compareTo(ratioThreshold) >= 0) {
            vo.setBiasRateFactor(ratioThreshold);
            // 满足条件的处理逻辑
            vo.setDiseType(DrgConst.CASE_TYPE_1);
            vo.setPayMentType(DrgConst.PAYMENT_TYPE_DRG);
        }
    }
}
