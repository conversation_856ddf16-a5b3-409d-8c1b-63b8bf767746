package com.my.som.grouppay.compmodule.drgmodule.meishan_5114;

import com.my.som.common.constant.DrgConst;
import com.my.som.common.util.ValidateUtil;
import com.my.som.grouppay.vo.HospBaseBusiVo;
import com.my.som.vo.dipBusiness.DipPayToPredictVo;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import com.yomahub.liteflow.core.NodeComponent;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;

@LiteflowComponent(value = "DrgCalculateGeneratePointsNode5114", name = "计算病例分值")
public class DrgCalculateGeneratePointsNode5114 extends NodeComponent {
    @Override
    public void process() throws Exception {

        // 2.获取业务上下文
        HospBaseBusiVo hospBaseBusiVo = this.getContextBean(HospBaseBusiVo.class);
        List<DipPayToPredictVo> dipPayToPredictVoList = hospBaseBusiVo.getDipPayToPredictVoList();
        for (DipPayToPredictVo vo : dipPayToPredictVoList) {

            //1、特殊支付判断
            /* 【CASE_TYPE_TS_2、CASE_TYPE_4、CASE_TYPE_2】
            1、低倍率按项目
            2、非日间手术病例的住院天数<=1、住院天数大于60天、无标杆数据等三类按项目支付
            3、未入组按最低分值计算（19.5800）
            4、日间手术=基准分值*调整系数*0.9
            */
            List projectPayCaseType = Arrays.asList(DrgConst.CASE_TYPE_TS_2, DrgConst.CASE_TYPE_4, DrgConst.CASE_TYPE_2);
            if (projectPayCaseType.contains(vo.getDiseType())) {
                //归属项目支付
                vo.setPayMentType(DrgConst.PAYMENT_TYPE_PROJECT);
                // 支付结算点数置为0
                vo.setCalculateScore(new BigDecimal(0));
            }

            //日间手术=基准分值*调整系数*0.9
            if (vo.getDiseType().equals(DrgConst.CASE_TYPE_TS_1)) {
                vo.setCalculateScore(vo.getRefer_sco().multiply(vo.getAdjm_cof()).multiply(DrgConst.DAY_SURGERY_RATIO));
            }

            //DGR病例计算
            /*高倍率分值计算=基准点数*差异系数+（(该病例实际发生医疗费用－该病例不合理医疗费用）÷全市试点医疗机构该病组历史次均住院
            费用－高倍率界值)）× 该病组基准点数*/
            /*20240919应客服要求更改分值计算方式为  基准点数*差异系数

            * */
            if (vo.getDiseType().equals(DrgConst.CASE_TYPE_1)) {
//                vo.setCalculateScore(vo.getRefer_sco().multiply(vo.getAdjm_cof()).add(
//                        vo.getInHosTotalCost().add(vo.getPreHospExamfee()).divide(vo.getStandardFee(), BigDecimal.ROUND_CEILING).subtract(vo.getUplmtMag()).multiply(vo.getRefer_sco())));
                vo.setCalculateScore(vo.getRefer_sco().multiply(vo.getAdjm_cof()));
            }

            //正常病例分值计算 = 基准点数*差异系数
            if (vo.getDiseType().equals(DrgConst.CASE_TYPE_3)) {
                vo.setCalculateScore(vo.getRefer_sco().multiply(vo.getAdjm_cof()));
            }

            //未入组按最低分值计算（minPointsSize）
            if (vo.getDiseType().equals(DrgConst.CASE_TYPE_NONE_GROUP)) {
                vo.setCalculateScore(hospBaseBusiVo.getMinPointsSize());
            }

            //计算追加分值
            if (vo.getHospCof() != null) {
                vo.setAddScore((vo.getCalculateScore().multiply(vo.getAddRate()).subtract(vo.getCalculateScore())).add(vo.getCalculateScore().multiply(vo.getAddRate()).multiply(vo.getHospCof().subtract(BigDecimal.ONE))));
                vo.setTotlSco(vo.getCalculateScore().add(vo.getAddScore()));
            } else {
                vo.setAddScore(BigDecimal.ZERO);
                vo.setTotlSco(vo.getCalculateScore().add(vo.getAddScore()));
            }
        }
    }
}
