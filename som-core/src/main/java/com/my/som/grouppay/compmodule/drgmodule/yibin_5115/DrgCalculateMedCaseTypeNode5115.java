package com.my.som.grouppay.compmodule.drgmodule.yibin_5115;

import com.my.som.common.constant.DrgConst;
import com.my.som.common.util.ValidateUtil;
import com.my.som.grouppay.vo.HospBaseBusiVo;
import com.my.som.vo.dipBusiness.DipPayToPredictVo;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import com.yomahub.liteflow.core.NodeComponent;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

@LiteflowComponent(value = "DrgCalculateMedCaseTypeNode5115", name = "计算病例类型")
public class DrgCalculateMedCaseTypeNode5115 extends NodeComponent {
    private final static String IS_STABLE_FLAG = "0";
    private final static String P000 = "P000";
    private final static String P0000 = "0000";

    @Override
    public void process() throws Exception {
        // 2.获取业务上下文
        HospBaseBusiVo hospBaseBusiVo = this.getContextBean(HospBaseBusiVo.class);
        List<DipPayToPredictVo> dipPayToPredictVoList = hospBaseBusiVo.getDipPayToPredictVoList();
        for (DipPayToPredictVo vo : dipPayToPredictVoList) {
            //加成系数暂时设置为1
            vo.setAddRate(BigDecimal.ONE);
            //根据政策调整，默认按DRG支付
            // 1、未入组
            vo.setPayMentType(DrgConst.PAYMENT_TYPE_DRG);
            if (ValidateUtil.isEmpty(vo.getDrgCodg()) || ValidateUtil.isEmpty(vo.getDrgName()) || P000.equals(vo.getDrgCodg()) || P0000.equals(vo.getDrgCodg())) {
                //未入组
                vo.setDiseType(DrgConst.CASE_TYPE_NONE_GROUP);
                continue;
            }
            // 2、非稳定组
            if (IS_STABLE_FLAG.equals(vo.getStableFlag())) {
                //非稳定
                if (ValidateUtil.isEmpty(vo.getDiseType())) {
                    vo.setDiseType(DrgConst.CASE_TYPE_5);
                    vo.setPayMentType(DrgConst.PAYMENT_TYPE_DRG);
                }
            }

            //3、正常病组高低倍率判断
            //高倍率
            if (vo.getInHosTotalCost().compareTo(Optional.ofNullable(vo.getMax()).orElse(BigDecimal.ZERO)) > 0) {
                if (ValidateUtil.isEmpty(vo.getDiseType())) {
                    if (Optional.ofNullable(vo.getMax()).orElse(BigDecimal.ZERO).compareTo(BigDecimal.ZERO) == 0) {
                        vo.setDiseType(DrgConst.CASE_TYPE_4);
                        vo.setPayMentType(DrgConst.PAYMENT_TYPE_DRG);
                    } else {
                        vo.setDiseType(DrgConst.CASE_TYPE_1);
                    }
                }
            }
            //低倍率
            if (vo.getInHosTotalCost().compareTo(Optional.ofNullable(vo.getMin()).orElse(BigDecimal.ZERO)) < 0) {
                if (ValidateUtil.isEmpty(vo.getDiseType())) {
                    vo.setDiseType(DrgConst.CASE_TYPE_2);
                    vo.setPayMentType(DrgConst.PAYMENT_TYPE_DRG);
                }
            }
            //正常倍率
            if (vo.getInHosTotalCost().compareTo(Optional.ofNullable(vo.getMax()).orElse(BigDecimal.ZERO)) <= 0 &&
                    vo.getInHosTotalCost().compareTo(Optional.ofNullable(vo.getMin()).orElse(BigDecimal.ZERO)) >= 0) {
                if (ValidateUtil.isEmpty(vo.getMax()) || ValidateUtil.isEmpty(vo.getMin())) {
                    if (ValidateUtil.isEmpty(vo.getDiseType())) {
                        vo.setDiseType(DrgConst.CASE_TYPE_4);
                        vo.setPayMentType(DrgConst.PAYMENT_TYPE_DRG);
                    }
                } else {
                    if (ValidateUtil.isEmpty(vo.getDiseType())) {
                        vo.setDiseType(DrgConst.CASE_TYPE_3);
                    }
                }
            }
            //设置是否启动月度单价
            vo.setRunMonthPrice(true);
        }
    }
}
