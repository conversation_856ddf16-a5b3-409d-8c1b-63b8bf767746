package com.my.som.grouppay.compmodule.basic;

import com.my.som.dto.common.PriceDto;
import com.my.som.grouppay.vo.HospBaseBusiVo;
import com.my.som.mapper.common.DipConfigMapper;
import com.my.som.mapper.common.DrgConfigMapper;
import com.my.som.vo.common.DipConfigVo;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import com.yomahub.liteflow.core.NodeComponent;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@LiteflowComponent(value = "YearForecastPriceNode", name = "获取年度预测单价")
public class YearForecastPriceNode extends NodeComponent {
    @Autowired
    private DrgConfigMapper drgConfigMapper;

    @Override
    public void process() throws Exception {
        // 1.获取业务对象
        HospBaseBusiVo hospBaseBusiVo = this.getContextBean(HospBaseBusiVo.class);
        //获取城乡城职单价，可构建参数查出所有期号及年度单价配置，病例根据结算期号匹配,根据配置判断DRG,或是dip

        PriceDto queryPriceDto = new PriceDto();
        queryPriceDto.setInsuplcAdmdvs(hospBaseBusiVo.getInsuplcAdmdvs());
        List<DipConfigVo> drgConfigVos  = drgConfigMapper.selectDrgData(queryPriceDto);

        Map<String, DipConfigVo> viewHospitalVoMap = drgConfigVos.stream().collect(Collectors.toMap(DipConfigVo::getKey, dipConfigVo -> dipConfigVo));
        hospBaseBusiVo.setDipConfigVoMap(viewHospitalVoMap);
    }
}