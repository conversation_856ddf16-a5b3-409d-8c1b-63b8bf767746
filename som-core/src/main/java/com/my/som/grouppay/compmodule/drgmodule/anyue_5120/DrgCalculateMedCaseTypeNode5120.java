package com.my.som.grouppay.compmodule.drgmodule.anyue_5120;

import com.my.som.common.constant.DrgConst;
import com.my.som.common.util.ValidateUtil;
import com.my.som.grouppay.vo.HospBaseBusiVo;
import com.my.som.vo.dipBusiness.DipPayToPredictVo;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import com.yomahub.liteflow.core.NodeComponent;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

@LiteflowComponent(value = "DrgCalculateMedCaseTypeNode5120", name = "计算病例类型")
public class DrgCalculateMedCaseTypeNode5120 extends NodeComponent {
    @Override
    public void process() throws Exception {
        // 2.获取业务上下文
        HospBaseBusiVo hospBaseBusiVo = this.getContextBean(HospBaseBusiVo.class);
        List<DipPayToPredictVo> dipPayToPredictVoList = hospBaseBusiVo.getDipPayToPredictVoList();
        for (DipPayToPredictVo vo : dipPayToPredictVoList) {
            //根据政策调整，默认按DRG支付
            vo.setPayMentType(DrgConst.PAYMENT_TYPE_DRG);
            if (ValidateUtil.isEmpty(vo.getDrgCodg())) {
                //未入组
                vo.setDiseType(DrgConst.CASE_TYPE_NONE_GROUP);
                continue;
            }
            //加成系数暂时设置为1
            vo.setAddRate(BigDecimal.ONE);
            if (ValidateUtil.isEmpty(vo.getBed_type())) {
                //高倍率
                if (vo.getInHosTotalCost().compareTo(Optional.ofNullable(vo.getMax()).orElse(BigDecimal.ZERO)) > 0) {
                    if (ValidateUtil.isEmpty(vo.getDiseType())) {
                        if (Optional.ofNullable(vo.getMax()).orElse(BigDecimal.ZERO).compareTo(BigDecimal.ZERO) == 0) {
                            vo.setDiseType(DrgConst.CASE_TYPE_4);
                        } else {
                            vo.setDiseType(DrgConst.CASE_TYPE_1);
                        }
                    }
                }
                //低倍率
                if (vo.getInHosTotalCost().compareTo(Optional.ofNullable(vo.getMin()).orElse(BigDecimal.ZERO)) < 0) {
                    if (ValidateUtil.isEmpty(vo.getDiseType())) {
                        vo.setDiseType(DrgConst.CASE_TYPE_2);
                    }
                }
                //正常倍率
                if (vo.getInHosTotalCost().compareTo(Optional.ofNullable(vo.getMax()).orElse(BigDecimal.ZERO)) <= 0 &&
                        vo.getInHosTotalCost().compareTo(Optional.ofNullable(vo.getMin()).orElse(BigDecimal.ZERO)) >= 0) {
                    if (ValidateUtil.isEmpty(vo.getMax()) || ValidateUtil.isEmpty(vo.getMin())) {
                        if (ValidateUtil.isEmpty(vo.getDiseType())) {
                            vo.setDiseType(DrgConst.CASE_TYPE_4);
                        }
                    } else {
                        if (ValidateUtil.isEmpty(vo.getDiseType())) {
                            vo.setDiseType(DrgConst.CASE_TYPE_3);
                        }
                    }
                }
            } else {
                //床日病种，直接不判断高低倍率，默认正常
                vo.setDiseType(DrgConst.CASE_TYPE_3);
                vo.setPayMentType(DrgConst.PAYMENT_TYPE_BEDDAY);
            }
            //设置是否启动月度单价
            vo.setRunMonthPrice(false);
        }
    }
}
