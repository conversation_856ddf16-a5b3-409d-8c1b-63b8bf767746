package com.my.som.grouppay.compmodule.drgmodule.guangan5116;

import com.my.som.common.util.ValidateUtil;
import com.my.som.grouppay.vo.HospBaseBusiVo;
import com.my.som.vo.dataConfig.ViewHospitalVo;
import com.my.som.vo.dipBusiness.DipPayToPredictVo;
import com.my.som.vo.pregroup.PreGroupVo;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import com.yomahub.liteflow.core.NodeComponent;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@LiteflowComponent(value = "DrgCalculateDiseGroupTypeNode5116", name = "计算病组类型")
public class DrgCalculateDiseGroupTypeNode5116 extends NodeComponent {
    @Override
    public void process() throws Exception {
        //根据入参计算是床日病种还是其它病种，床日病种需要从缓存中获取床日病种配置信息
        // 1.获取业务参数
        List<PreGroupVo> preGroupVos = this.getRequestData();
        // 2.获取业务上下文
        HospBaseBusiVo hospBaseBusiVo = this.getContextBean(HospBaseBusiVo.class);
        /**
         *  判断入组病例与非入组病例
         *  1、判断是否入组成功
         *      正常病组：
         *      异常病组：未DRG入组成功、无支付标杆组、非稳定病组
         */
        //医疗机构编码
        String hospitalId = hospBaseBusiVo.getHospitalId();
        ViewHospitalVo viewHospitalVo = hospBaseBusiVo.getHospitalVoMap().get(hospitalId);
        //组织计算病例类型及分值参数
        List<DipPayToPredictVo> scoreList = new ArrayList<>();
        BigDecimal inHosDays;
        String mainDiseCode;
        for (PreGroupVo preGroupVo : preGroupVos) {
            //预估费用对象
            DipPayToPredictVo drgPayToPredictVo = new DipPayToPredictVo();
            //获取住院天数
            inHosDays = preGroupVo.getPayToPredictBaseInfoVo().getInHosDays();
            //获取主要诊断
            mainDiseCode = preGroupVo.getPayToPredictBaseInfoVo().getMainDiagnoseCode();
            //设置主要诊断
            drgPayToPredictVo.setDiagnoseCode(mainDiseCode);
            drgPayToPredictVo.setInHosDays(inHosDays);
            if (ValidateUtil.isNotEmpty(preGroupVo.getDrgCodg())) {
                drgPayToPredictVo.setDrgCodg(preGroupVo.getDrgCodg());
                drgPayToPredictVo.setDrgName(preGroupVo.getDrgName());
                drgPayToPredictVo.setInHosTotalCost(preGroupVo.getPayToPredictBaseInfoVo().getInHosTotalCost());
                drgPayToPredictVo.setAge(preGroupVo.getPayToPredictBaseInfoVo().getAge());
                drgPayToPredictVo.setDeptCode(preGroupVo.getPayToPredictBaseInfoVo().getDeptCode());
                drgPayToPredictVo.setWMCode(preGroupVo.getDiagnoseCode());
                drgPayToPredictVo.setDiagnoseCode(preGroupVo.getDiagnoseCode());
                drgPayToPredictVo.setTCMCode(preGroupVo.getPayToPredictBaseInfoVo().getTCMCode());
                drgPayToPredictVo.setHospitalId(preGroupVo.getPayToPredictBaseInfoVo().getHospitalId());
                drgPayToPredictVo.setMax(preGroupVo.getHighFee());
                drgPayToPredictVo.setMin(preGroupVo.getMinFee());
                drgPayToPredictVo.setRefer_sco(preGroupVo.getRefer_sco());
                drgPayToPredictVo.setAdjm_cof(preGroupVo.getAdjm_cof());
                drgPayToPredictVo.setLastYearLevelStandardCost(preGroupVo.getNearAvgCost());
                drgPayToPredictVo.setUplmtMag(preGroupVo.getUplmtMag());
                drgPayToPredictVo.setHospLv(viewHospitalVo.getHospLv());
                drgPayToPredictVo.setMedinsType(viewHospitalVo.getMedinsType());
                drgPayToPredictVo.setLevelStandardCost(preGroupVo.getDrgAvgCost());
                drgPayToPredictVo.setYm(preGroupVo.getPayToPredictBaseInfoVo().getYm());
                drgPayToPredictVo.setPreHospExamfee(BigDecimal.ZERO);
                drgPayToPredictVo.setHospCof(viewHospitalVo.getHospCof());
                drgPayToPredictVo.setIs_base_dise(preGroupVo.getIs_base_dise());
                drgPayToPredictVo.setStandardFee(preGroupVo.getDrgAvgCost());
                drgPayToPredictVo.setInsuType(preGroupVo.getPayToPredictBaseInfoVo().getInsuType());
            } else {
                //未入组
                drgPayToPredictVo.setInHosTotalCost(preGroupVo.getPayToPredictBaseInfoVo().getInHosTotalCost());
                drgPayToPredictVo.setAge(preGroupVo.getPayToPredictBaseInfoVo().getAge());
                drgPayToPredictVo.setDeptCode(preGroupVo.getPayToPredictBaseInfoVo().getDeptCode());
                drgPayToPredictVo.setWMCode(preGroupVo.getDiagnoseCode());
                drgPayToPredictVo.setDiagnoseCode(preGroupVo.getDiagnoseCode());
                drgPayToPredictVo.setTCMCode(preGroupVo.getPayToPredictBaseInfoVo().getTCMCode());
                drgPayToPredictVo.setHospitalId(preGroupVo.getPayToPredictBaseInfoVo().getHospitalId());
                drgPayToPredictVo.setRefer_sco(preGroupVo.getRefer_sco());
                drgPayToPredictVo.setAdjm_cof(preGroupVo.getAdjm_cof());
                drgPayToPredictVo.setLastYearLevelStandardCost(preGroupVo.getNearAvgCost());
                drgPayToPredictVo.setUplmtMag(preGroupVo.getUplmtMag());
                drgPayToPredictVo.setHospLv(viewHospitalVo.getHospLv());
                drgPayToPredictVo.setMedinsType(viewHospitalVo.getMedinsType());
                drgPayToPredictVo.setYm(preGroupVo.getPayToPredictBaseInfoVo().getYm());
                drgPayToPredictVo.setPreHospExamfee(BigDecimal.ZERO);
                drgPayToPredictVo.setHospCof(viewHospitalVo.getHospCof());
                drgPayToPredictVo.setInsuType(preGroupVo.getPayToPredictBaseInfoVo().getInsuType());
            }
            scoreList.add(drgPayToPredictVo);
        }
        hospBaseBusiVo.setDipPayToPredictVoList(scoreList);
    }
}
