package com.my.som.grouppay.compmodule.drgmodule.dazhou_5117;

import com.my.som.common.constant.DrgConst;
import com.my.som.common.util.ValidateUtil;
import com.my.som.grouppay.vo.HospBaseBusiVo;
import com.my.som.vo.common.DipConfigVo;
import com.my.som.vo.dipBusiness.DipPayToPredictVo;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import com.yomahub.liteflow.core.NodeComponent;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@LiteflowComponent(value = "DrgCalculatePreGroupPayForecastNode5117", name = "计算支付预测金额-在院预分组")
public class DrgCalculatePreGroupPayForecastNode5117 extends NodeComponent {
    @Override
    public void process() throws Exception {
        //1、获取业务上下文
        HospBaseBusiVo hospBaseBusiVo = this.getContextBean(HospBaseBusiVo.class);
        List<DipPayToPredictVo> dipPayToPredictVoList = hospBaseBusiVo.getDipPayToPredictVoList();
        Map<String, DipConfigVo> dipConfigVoMap = hospBaseBusiVo.getDipConfigVoMap();
        BigDecimal forecastPrice;
        BigDecimal forecastFee;
        DipConfigVo dipConfigVo;
        Map<String, DipConfigVo> viewHospitalVoMap;
        for (DipPayToPredictVo vo : dipPayToPredictVoList) {
            //1or01 czPrice;2or02 cxPrice;Price,有期号取期号单价，没有取默认单价
            dipConfigVo = dipConfigVoMap.get(getPriceKey(vo.getInsuType(), vo.getYm(), vo.isRunMonthPrice()));
            if (!ValidateUtil.isEmpty(dipConfigVo)) {
                forecastPrice = new BigDecimal(dipConfigVo.getValue());
            } else {
                forecastPrice = new BigDecimal(dipConfigVoMap.get(DrgConst.DEFAULT_PRICE_KEY).getValue());
            }
            forecastFee = vo.getTotlSco().multiply(forecastPrice);
            //正常病例正常计算
            vo.setZfz(vo.getTotlSco());
            vo.setYcfy(forecastFee);
            if (DrgConst.PAYMENT_TYPE_PROJECT.equals(vo.getPayMentType())) {
                //是否执行按项目付费
                vo.setYcfy(vo.getInHosTotalCost());
            }

            vo.setFycy((vo.getYcfy().subtract(vo.getInHosTotalCost())).setScale(2, BigDecimal.ROUND_HALF_UP));
            vo.setJzfz(vo.getRefer_sco());
            vo.setJbjbxs(vo.getAdjm_cof());
            vo.setYcfzFirst(vo.getCalculateScore());
            vo.setJcfz(vo.getAddScore());
            vo.setFzdj(forecastPrice);
            vo.setBalx(DrgConst.CASE_TYPE_MAP.get(vo.getDiseType()));
            vo.setPayMentType(DrgConst.PAYMENT_TYPE_MAP.get(vo.getPayMentType()));
        }
    }

    /**
     * 结合期号进行判断
     * 根据参保类型获取单价配置key
     *
     * @param insuType
     * @param ym
     * @return
     */
    String getPriceKey(String insuType, String ym, boolean runMonthPrice) {
        String priceKey = DrgConst.PRICE_KEY_DEFAULT;
        if (DrgConst.INSURANCES_TYPE_1.contains(insuType)) {
            priceKey = DrgConst.PRICE_KEY_CZ;
        } else if (DrgConst.INSURANCES_TYPE_2.contains(insuType)) {
            priceKey = DrgConst.PRICE_KEY_CX;
        }
        if (runMonthPrice && !ValidateUtil.isEmpty(ym)) {
            priceKey = ym + "-" + priceKey;
        }
        return priceKey;
    }
}
