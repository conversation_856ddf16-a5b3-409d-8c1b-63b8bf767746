package com.my.som.grouppay.compmodule.basic;

import com.my.som.common.util.ValidateUtil;
import com.my.som.dto.dataConfig.HospitalInfoDto;
import com.my.som.dto.pregroup.PreGroupDto;
import com.my.som.grouppay.dto.BedDrgDiseCodeCfgDto;
import com.my.som.grouppay.vo.BedDrgDiseCodeCfgVo;
import com.my.som.grouppay.vo.HospBaseBusiVo;
import com.my.som.mapper.dataConfig.ViewHospitalMapper;
import com.my.som.vo.dataConfig.ViewHospitalVo;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import com.yomahub.liteflow.core.NodeComponent;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@LiteflowComponent(value = "HospCostRateNode", name = "获取等级和成本系数")
public class HospCostRateNode extends NodeComponent {
    @Resource
    ViewHospitalMapper viewHospitalMapper;

    @Override
    public void process() throws Exception {
        // 1.获取业务参数
        PreGroupDto preGroupDto = this.getRequestData();
        // 2.获取业务对象
        HospBaseBusiVo hospBaseBusiVo = this.getContextBean(HospBaseBusiVo.class);
        //获取当前医院级别
        List<ViewHospitalVo> viewHospitalVos = viewHospitalMapper.queryHospitalInfo(new HospitalInfoDto());
        Map<String, ViewHospitalVo> viewHospitalVoMap = viewHospitalVos.stream().collect(Collectors.toMap(ViewHospitalVo::getHospitalId, viewHospitalVo -> viewHospitalVo));
        hospBaseBusiVo.setHospitalVoMap(viewHospitalVoMap);
        //获取当前床日诊断配置
        BedDrgDiseCodeCfgDto bedDrgDiseCodeCfgDto = getQueryDto(preGroupDto);
        List<BedDrgDiseCodeCfgVo> bedDrgDiseCodeCfgVoList = viewHospitalMapper.queryBedDrgDiseCodeList(bedDrgDiseCodeCfgDto);
        Map<String, BedDrgDiseCodeCfgVo> bedDrgDiseCodeCfgVoMap = bedDrgDiseCodeCfgVoList.stream().collect(Collectors.toMap(BedDrgDiseCodeCfgVo::getMain_diag_codg, bedDrgDiseCodeCfgVo -> bedDrgDiseCodeCfgVo));
        hospBaseBusiVo.setBedDrgDiseCodeCfgVoMap(bedDrgDiseCodeCfgVoMap);
    }

    private BedDrgDiseCodeCfgDto getQueryDto(PreGroupDto dto) {
        BedDrgDiseCodeCfgDto drgBedStandardDto = new BedDrgDiseCodeCfgDto();
        if (ValidateUtil.isNotEmpty(dto.getCysj()) && dto.getCysj().length() > 4) {
            if (dto.getCysj().contains("1900")) {
                drgBedStandardDto.setYear(String.valueOf(LocalDate.now().getYear()));
            } else {
                drgBedStandardDto.setYear(dto.getCysj().substring(0, 4));
            }
        } else {
            drgBedStandardDto.setYear(String.valueOf(LocalDate.now().getYear()));
        }
        return drgBedStandardDto;
    }
}
