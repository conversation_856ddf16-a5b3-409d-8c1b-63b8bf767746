package com.my.som.grouppay.compmodule.drgmodule.chengdu_5101;

import com.my.som.common.constant.DrgConst;
import com.my.som.grouppay.compmodule.dipmodule.common.PaymentConst;
import com.my.som.grouppay.vo.HospBaseBusiVo;
import com.my.som.vo.dipBusiness.DipPayToPredictVo;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import com.yomahub.liteflow.core.NodeComponent;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Arrays;
import java.util.List;

@LiteflowComponent(value = "DrgCalculateGeneratePointsNode5101", name = "计算病例分值")
public class DrgCalculateGeneratePointsNode5101 extends NodeComponent {
    @Override
    public void process() throws Exception {

        // 2.获取业务上下文
        HospBaseBusiVo hospBaseBusiVo = this.getContextBean(HospBaseBusiVo.class);
        List<DipPayToPredictVo> dipPayToPredictVoList = hospBaseBusiVo.getDipPayToPredictVoList();
        for (DipPayToPredictVo vo : dipPayToPredictVoList) {

            //一、项目直接折算
            /*
             * 1、大于60天[PAYMENT_TYPE_PROJECT_CONVER]，结算点数=该病例实际费用÷所有病例的次均住院费用×100
             * 2、非稳定病组[PAYMENT_TYPE_PROJECT_CONVER]，结算点数=该病例实际费用÷所有病例的次均住院费用×100
             * 3、空白组、歧义组【PAYMENT_TYPE_PROJECT_CONVER_RATIO】，结算点数=该病例实际费用÷所有病例的次均住院费用×100×0.9
             */
            // [PAYMENT_TYPE_PROJECT_CONVER]该病例实际费用÷所有病例的次均住院费用×100
            if (DrgConst.PAYMENT_TYPE_PROJECT_CONVER.contains(vo.getPayMentType())) {
                //归属项目折算点数支付
                vo.setCalculateScore(vo.getInHosTotalCost().add(vo.getPreHospExamfee()).divide(hospBaseBusiVo.getRegiAverageFee(), 4, RoundingMode.HALF_UP).multiply(PaymentConst.PROPORTION_BASE));
            }

            // [PAYMENT_TYPE_PROJECT_CONVER_RATIO]该病例实际费用÷所有病例的次均住院费用×100×0.9
            if (DrgConst.PAYMENT_TYPE_PROJECT_CONVER_RATIO.contains(vo.getPayMentType())) {
                //归属项目折算点数支付*0.9
                vo.setCalculateScore(vo.getInHosTotalCost().add(vo.getPreHospExamfee()).divide(hospBaseBusiVo.getRegiAverageFee(), 4, RoundingMode.HALF_UP).multiply(PaymentConst.PROPORTION_BASE).multiply(PaymentConst.CONVER_RATIO_5101));
            }


            // 无标杆病例
            if (DrgConst.PAYMENT_TYPE_PROJECT.contains(vo.getPayMentType())) {
                vo.setCalculateScore(new BigDecimal(0));
            }

            //二、DGR病例计算
            /*
             *（1）高倍率：>=1.4倍同级别均费,结算点数 =（该病例实际费用÷该等级医疗机构该 DRG 组次均住院费用-0.4）×该病组结算点数
             *（2）正常倍率：非高倍率和低倍率病例，结算点数=DRG 组基准点数×等级系数×调整系数
             *（3）低倍率：<=0.7倍同级别均费，结算点数=该病例实际费用÷所有病例的次均住院费用×100
             * */
            //高倍率
            if (DrgConst.CASE_TYPE_1.equals(vo.getDiseType())) {
                vo.setCalculateScore(
                        (vo.getInHosTotalCost().add(vo.getPreHospExamfee()).divide(vo.getLevelStandardCost(), 4, RoundingMode.HALF_UP).subtract(PaymentConst.DRG_HEIGHT_RATIO_5101.subtract(BigDecimal.ONE))).multiply(vo.getRefer_sco().multiply(vo.getAdjm_cof())));
            }

            //低倍率
            if (DrgConst.CASE_TYPE_2.equals(vo.getDiseType())) {
                vo.setCalculateScore(
                        vo.getInHosTotalCost().add(vo.getPreHospExamfee()).divide(hospBaseBusiVo.getRegiAverageFee(), 4, RoundingMode.HALF_UP).multiply(PaymentConst.PROPORTION_BASE));
            }

            //正常病例
            if (DrgConst.CASE_TYPE_3.equals(vo.getDiseType())) {
                vo.setCalculateScore(vo.getRefer_sco().multiply(vo.getAdjm_cof()));
            }

            //低倍率、歧义、空白、住院大于60天、非稳定，不参与调整系数加成，只有高倍率和正常倍率参与【CASE_TYPE_1、CASE_TYPE_3】
            List<String> enableAdjustmentPayCaseType = Arrays.asList(DrgConst.CASE_TYPE_3,DrgConst.CASE_TYPE_1);
            //计算追加分值
            if (enableAdjustmentPayCaseType.contains(vo.getDiseType()) && vo.getHospCof() != null) {
                vo.setAddScore((vo.getCalculateScore().multiply(vo.getAddRate()).subtract(vo.getCalculateScore())).add(vo.getCalculateScore().multiply(vo.getAddRate()).multiply(vo.getHospCof().subtract(BigDecimal.ONE))));
                vo.setTotlSco(vo.getCalculateScore().add(vo.getAddScore()));
            } else {
                vo.setAddScore(BigDecimal.ZERO);
                vo.setTotlSco(vo.getCalculateScore().add(vo.getAddScore()));
            }

        }
    }
}
