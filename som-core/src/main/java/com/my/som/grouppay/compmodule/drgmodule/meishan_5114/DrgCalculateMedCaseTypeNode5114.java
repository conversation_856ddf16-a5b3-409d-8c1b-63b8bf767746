package com.my.som.grouppay.compmodule.drgmodule.meishan_5114;

import com.my.som.common.constant.DrgConst;
import com.my.som.common.util.ValidateUtil;
import com.my.som.grouppay.vo.HospBaseBusiVo;
import com.my.som.vo.dipBusiness.DipPayToPredictVo;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import com.yomahub.liteflow.core.NodeComponent;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

@LiteflowComponent(value = "DrgCalculateMedCaseTypeNode5114", name = "计算病例类型")
public class DrgCalculateMedCaseTypeNode5114 extends NodeComponent {
    @Override
    public void process() throws Exception {
        // 2.获取业务上下文
        HospBaseBusiVo hospBaseBusiVo = this.getContextBean(HospBaseBusiVo.class);
        List<DipPayToPredictVo> dipPayToPredictVoList = hospBaseBusiVo.getDipPayToPredictVoList();
        for (DipPayToPredictVo vo : dipPayToPredictVoList) {
            //加成系数暂时设置为1
            vo.setAddRate(BigDecimal.ONE);
            //根据政策调整，默认按DRG支付
            vo.setPayMentType(DrgConst.PAYMENT_TYPE_DRG);
            if (ValidateUtil.isEmpty(vo.getDrgCodg())) {
                //未入组
                vo.setDiseType(DrgConst.CASE_TYPE_NONE_GROUP);
                continue;
            }

            //1、支付类型特殊判断：是否是日间手术 IPT_MED_TYPE_2= '2',
            if (DrgConst.IPT_MED_TYPE_2.equals(vo.getIptMedType())) {
                if (Optional.ofNullable(vo.getMax()).orElse(BigDecimal.ZERO).compareTo(BigDecimal.ZERO) == 0) {
                    vo.setDiseType(DrgConst.CASE_TYPE_4);
                    vo.setPayMentType(DrgConst.PAYMENT_TYPE_PROJECT);
                } else {
                    vo.setDiseType(DrgConst.CASE_TYPE_TS_1);
                    continue;
                }
            }

            //2、支付类型特殊判断：是否住院天数<=1、住院天数大于60
            if (DrgConst.LONG_TERM_HOSP_DAYS.compareTo(vo.getInHosDays()) < 0 || DrgConst.LOW_TERM_HOSP_DAYS.compareTo(vo.getInHosDays()) >= 0) {
                vo.setDiseType(DrgConst.CASE_TYPE_TS_2);
                vo.setPayMentType(DrgConst.PAYMENT_TYPE_PROJECT);
                continue;
            }

            //3、正常病组高低倍率判断
            //高倍率
            if (vo.getInHosTotalCost().compareTo(Optional.ofNullable(vo.getMax()).orElse(BigDecimal.ZERO)) > 0) {
                if (ValidateUtil.isEmpty(vo.getDiseType())) {
                    if (Optional.ofNullable(vo.getMax()).orElse(BigDecimal.ZERO).compareTo(BigDecimal.ZERO) == 0) {
                        vo.setDiseType(DrgConst.CASE_TYPE_4);
                        vo.setPayMentType(DrgConst.PAYMENT_TYPE_PROJECT);
                    } else {
                        vo.setDiseType(DrgConst.CASE_TYPE_1);
                    }
                }
            }
            //低倍率
            if (vo.getInHosTotalCost().compareTo(Optional.ofNullable(vo.getMin()).orElse(BigDecimal.ZERO)) < 0) {
                if (ValidateUtil.isEmpty(vo.getDiseType())) {
                    vo.setDiseType(DrgConst.CASE_TYPE_2);
                    vo.setPayMentType(DrgConst.PAYMENT_TYPE_PROJECT);
                }
            }
            //正常倍率
            if (vo.getInHosTotalCost().compareTo(Optional.ofNullable(vo.getMax()).orElse(BigDecimal.ZERO)) <= 0 &&
                    vo.getInHosTotalCost().compareTo(Optional.ofNullable(vo.getMin()).orElse(BigDecimal.ZERO)) >= 0) {
                if (ValidateUtil.isEmpty(vo.getMax()) || ValidateUtil.isEmpty(vo.getMin())) {
                    if (ValidateUtil.isEmpty(vo.getDiseType())) {
                        vo.setDiseType(DrgConst.CASE_TYPE_4);
                        vo.setPayMentType(DrgConst.PAYMENT_TYPE_PROJECT);
                    }
                } else {
                    if (ValidateUtil.isEmpty(vo.getDiseType())) {
                        vo.setDiseType(DrgConst.CASE_TYPE_3);
                    }
                }
            }
            //设置是否启动月度单价
            vo.setRunMonthPrice(true);
        }
    }
}
