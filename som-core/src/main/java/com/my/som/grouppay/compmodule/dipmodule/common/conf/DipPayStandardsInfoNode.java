package com.my.som.grouppay.compmodule.dipmodule.common.conf;


import com.my.som.dto.pregroup.PreGroupDto;
import com.my.som.grouppay.vo.HospBaseBusiVo;
import com.my.som.mapper.common.SomDipStandardMapper;
import com.my.som.model.common.SomDipStandard;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import com.yomahub.liteflow.core.NodeComponent;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

@LiteflowComponent(value = "DipPayStandardsInfoNode",name = "DIP病种标杆获取")
public class DipPayStandardsInfoNode extends NodeComponent {


    @Autowired
    private SomDipStandardMapper tpdDipBenchmarkMapper;

    @Override
    public void process() throws Exception {
        // 1.获取业务参数
        PreGroupDto preGroupDto = this.getRequestData();
        // 2.获取业务对象
        HospBaseBusiVo hospBaseBusiVo = this.getContextBean(HospBaseBusiVo.class);
        // 3.获取DIP标杆信息
        List<SomDipStandard> dipBenchmarkList = tpdDipBenchmarkMapper.queryDipBenchmark(preGroupDto.getHospital_id());
        hospBaseBusiVo.setDipBenchmarkList(dipBenchmarkList);
    }
}
