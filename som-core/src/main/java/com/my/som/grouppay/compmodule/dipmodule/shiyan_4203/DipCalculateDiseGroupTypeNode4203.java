package com.my.som.grouppay.compmodule.dipmodule.shiyan_4203;


import com.my.som.common.constant.DrgConst;
import com.my.som.common.util.ValidateUtil;
import com.my.som.dto.pregroup.PreGroupDto;
import com.my.som.grouppay.vo.HospBaseBusiVo;
import com.my.som.vo.dataHandle.PayToPredict.PayToPredictVo;
import com.my.som.vo.dipBusiness.DipPayToPredictVo;
import com.my.som.vo.dipBusiness.PayToPredictBaseInfoVo;
import com.my.som.vo.pregroup.PreGroupVo;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import com.yomahub.liteflow.core.NodeComponent;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@LiteflowComponent(value = "DipCalculateDiseGroupTypeNode4203",name = "病种支付类型判断")
public class DipCalculateDiseGroupTypeNode4203 extends NodeComponent {

    @Override
    public void process() throws Exception {
        // 1.获取业务参数
        List<PreGroupVo> preGroupVos = this.getRequestData();
        // 2.获取业务上下文
        HospBaseBusiVo hospBaseBusiVo = this.getContextBean(HospBaseBusiVo.class);
        PreGroupDto dto = this.getContextBean(PreGroupDto.class);
        List<DipPayToPredictVo> payToPredictVos = new ArrayList<>();
        //基础参数
        PayToPredictBaseInfoVo payToPredictBaseInfoVo;
        for (PreGroupVo dipGroupVo : preGroupVos) {
            DipPayToPredictVo payToPredictVo = new DipPayToPredictVo();
            payToPredictBaseInfoVo = dipGroupVo.getPayToPredictBaseInfoVo();
            payToPredictVo.setInHosTotalCost(payToPredictBaseInfoVo.getInHosTotalCost());
            payToPredictVo.setAge(payToPredictBaseInfoVo.getAge());
            payToPredictVo.setDeptCode(payToPredictBaseInfoVo.getDeptCode());
            payToPredictVo.setWMCode(payToPredictBaseInfoVo.getWMCode());
            payToPredictVo.setTCMCode(payToPredictBaseInfoVo.getTCMCode());
            payToPredictVo.setInsuType(payToPredictBaseInfoVo.getInsuType());
            BigDecimal tcm_treat_fee = ValidateUtil.isEmpty(payToPredictBaseInfoVo.getTCMTreatmentCost()) ? BigDecimal.ZERO : payToPredictBaseInfoVo.getTCMTreatmentCost();
            BigDecimal tcmherb = ValidateUtil.isEmpty(payToPredictBaseInfoVo.getTcmherb()) ? BigDecimal.ZERO : payToPredictBaseInfoVo.getTcmherb();
            BigDecimal west_fee = ValidateUtil.isEmpty(payToPredictBaseInfoVo.getWest_fee()) ? BigDecimal.ZERO : payToPredictBaseInfoVo.getWest_fee();
            payToPredictVo.setTCMTreatmentCost(tcm_treat_fee.add(tcmherb));
            payToPredictVo.setHospitalizationExpenses(tcm_treat_fee.add(west_fee));
            payToPredictVo.setCheckTotalFee(payToPredictBaseInfoVo.getCheckTotalFee());

            payToPredictVo.setDipCodg(dipGroupVo.getDipCodg());
            payToPredictVo.setDipName(dipGroupVo.getDipName());
            payToPredictVo.setGrpStas(dipGroupVo.getGroupStatus());
            payToPredictVo.setInHosTotalCost(!ValidateUtil.isEmpty(dto.getZyzfy()) ? dto.getZyzfy() : BigDecimal.ZERO);
//            payToPredictVo.setAge(!ValidateUtil.isEmpty(dto.getNl()) ? new BigDecimal(dto.getNl()) : BigDecimal.ZERO);
//            payToPredictVo.setDeptCode(ValidateUtil.isNotEmpty(dto.getCykbbm()) ? dto.getCykbbm() : dto.getRykbbm());
//            payToPredictVo.setWMCode(ValidateUtil.isNotEmpty(dipGroupVo.getDiagnoseCode()) ? dipGroupVo.getDiagnoseCode() : "-");
//            payToPredictVo.setTCMCode(ValidateUtil.isNotEmpty(dto.getZyzd_jbbm()) ? dto.getZyzd_jbbm() : "-");
            payToPredictVo.setMax(dipGroupVo.getHighFee());
            payToPredictVo.setMin(dipGroupVo.getMinFee());
            payToPredictVo.setRefer_sco(dipGroupVo.getRefer_sco());
            payToPredictVo.setAdjm_cof(dipGroupVo.getAdjm_cof());
            payToPredictVo.setLastYearLevelStandardCost(dipGroupVo.getDipAvgCostLevel());
            payToPredictVo.setUplmtMag(dipGroupVo.getUplmtMag());
            payToPredictVo.setLowlmtMag(dipGroupVo.getLowlmtMag());
            payToPredictVo.setDisType(dipGroupVo.getDisType());
            payToPredictVo.setLevelCost(dipGroupVo.getDipAvgCostLevel());
            payToPredictVo.setYm(ValidateUtil.isNotEmpty(dto.getRysj()) ? dto.getRysj() : "-");
            BigDecimal yqjcf = (!ValidateUtil.isEmpty(dto.getYqjcf()) && dto.getYqjcf().compareTo(BigDecimal.ZERO) != 0) ? dto.getYqjcf() : BigDecimal.ZERO;
            payToPredictVo.setPreHospExamfee(yqjcf);
            payToPredictVo.setHospitalId(dto.getHospital_id());
//            BigDecimal tcm_treat_fee = ValidateUtil.isNotEmpty(dto.getTcm_treat_fee()) ? new BigDecimal(dto.getTcm_treat_fee()) : BigDecimal.ZERO;
//            BigDecimal tcmherb = ValidateUtil.isNotEmpty(dto.getTcmherb()) ? new BigDecimal(dto.getTcmherb()) : BigDecimal.ZERO;
//            BigDecimal west_fee = ValidateUtil.isNotEmpty(dto.getWest_fee()) ? new BigDecimal(dto.getWest_fee()) : BigDecimal.ZERO;
//
//            payToPredictVo.setTCMTreatmentCost(tcm_treat_fee.add(tcmherb));
//            payToPredictVo.setHospitalizationExpenses(tcm_treat_fee.add(west_fee));
            payToPredictVo.setAreaStandardCost(dipGroupVo.getDipAvgCost());
            payToPredictVo.setStableFlag(dipGroupVo.getStableFlag());
            payToPredictVo.setAsstListDiseSevDeg(dipGroupVo.getAsstListDiseSevDeg());
            payToPredictVo.setAsstListTmorSevDeg(dipGroupVo.getAsstListTmorSevDeg());
            payToPredictVo.setAuxiliaryBurn(dipGroupVo.getAuxiliaryBurn());
            payToPredictVo.setAsstListAgeGrp(dipGroupVo.getAsstListAgeGrp());
            if(ValidateUtil.isNotEmpty(dipGroupVo.getDipCodg())){
                payToPredictVo.setDiseaseAverageCost(dipGroupVo.getDipAvgCost().divide(dipGroupVo.getRefer_sco(), 8, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal("1000")));
            }else{
                payToPredictVo.setDiseaseAverageCost(hospBaseBusiVo.getRegiAverageFee()) ;
            }
            payToPredictVo.setSumfee(payToPredictVo.getInHosTotalCost());
            payToPredictVo.setIs_base_dise(dipGroupVo.getIs_base_dise());
            payToPredictVo.setIs_tcm_dise(dipGroupVo.getIs_tcm_dise());
            if (dipGroupVo.getAsstListAdm() != null) {
                payToPredictVo.setAsstListAdm(dipGroupVo.getAsstListAdm());
            } else {
                payToPredictVo.setAsstListAdm(BigDecimal.ONE);
            }
            payToPredictVos.add(payToPredictVo);
        }
        hospBaseBusiVo.setDipPayToPredictVoList(payToPredictVos);
    }


}
