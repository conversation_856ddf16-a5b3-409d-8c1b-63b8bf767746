package com.my.som.grouppay.compmodule.dipmodule.liangshan_5134;

import com.my.som.common.constant.DrgConst;
import com.my.som.common.util.ValidateUtil;
import com.my.som.grouppay.compmodule.dipmodule.common.PaymentConst;
import com.my.som.grouppay.vo.HospBaseBusiVo;
import com.my.som.util.GenerateScoreUtil;
import com.my.som.vo.dataConfig.DipDiseaseTypeVo;
import com.my.som.vo.dataConfig.ViewHospitalVo;
import com.my.som.vo.dipBusiness.DipPayToPredictVo;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import com.yomahub.liteflow.core.NodeComponent;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static com.my.som.grouppay.compmodule.dipmodule.common.PaymentConst.DEFAULT_COEFFICIENT_1;
import static com.my.som.util.GenerateScoreUtil.*;


@LiteflowComponent(value = "DipCalculateOtherSpecialNode5134", name = "计算特殊政策病例分值")
public class DipCalculateOtherSpecialNode5134 extends NodeComponent {

    @Override
    public void process() throws Exception {
        // 获取业务上下文
        HospBaseBusiVo hospBaseBusiVo = this.getContextBean(HospBaseBusiVo.class);
        List<DipPayToPredictVo> payToPredictVoList = hospBaseBusiVo.getDipPayToPredictVoList();
        Map calculateScoreParams = this.getContextBean(Map.class);

        for (DipPayToPredictVo payToPredictVo : payToPredictVoList) {
            generaFundRatioType(payToPredictVo);
            if (!payToPredictVo.getDiseType().equals(DrgConst.CASE_TYPE_3)) {
                payToPredictVo.setProfessionalRate(BigDecimal.ONE);
                payToPredictVo.setTcmAdtCof(BigDecimal.ONE);
                payToPredictVo.setYoungCof(BigDecimal.ONE);
                payToPredictVo.setHospCof(BigDecimal.ONE);
                payToPredictVo.setGrstCof(BigDecimal.ONE);
                payToPredictVo.setTcmOrgCof(BigDecimal.ONE);

                payToPredictVo.setAddItiveValue(BigDecimal.ZERO);
                // 计算总分值
                payToPredictVo.setTotlSco(payToPredictVo.getSettleMentScore());
                continue;
            }
            computeAddItiveScore(payToPredictVo, calculateScoreParams);
        }
    }

    private static void generaFundRatioType(DipPayToPredictVo payToPredictVo) {

        BigDecimal empFundRatio = payToPredictVo.getEmpFundRatio();
        BigDecimal residFundRatio = payToPredictVo.getResidFundRatio();
        if (BigDecimal.ZERO.compareTo(empFundRatio) != 0 && BigDecimal.ZERO.compareTo(residFundRatio) != 0) {
            //处理医保报账逆差
            if (!ValidateUtil.isEmpty(payToPredictVo.getBusinessSerial())) {
                //计算比例类型
                payToPredictVo.setFundSourceType("实际费用");
                //基金总费用
                BigDecimal fundAmtSum = payToPredictVo.getFundAmtSum();

                if (ValidateUtil.isEmpty(fundAmtSum) || BigDecimal.ZERO.compareTo(fundAmtSum) == 0) {
                    String cblx = payToPredictVo.getInsuType();
                    BigDecimal sumfee = payToPredictVo.getSumfee();
                    payToPredictVo.setFundSourceType("无基金估算费用");
                    if ("1".equals(cblx) || "01".equals(cblx) || "310".equals(cblx)) {
                        payToPredictVo.setFundAmtSum(sumfee.multiply(empFundRatio));
                    } else if ("2".equals(cblx) || "02".equals(cblx) || "390".equals(cblx)) {
                        payToPredictVo.setFundAmtSum(sumfee.multiply(residFundRatio));
                    } else {
                        BigDecimal fundRatio = empFundRatio.add(residFundRatio).divide(new BigDecimal(2), 4, BigDecimal.ROUND_HALF_UP);
                        payToPredictVo.setFundAmtSum(sumfee.multiply(fundRatio));
                        payToPredictVo.setFundSourceType("其他类型估算费用");
                    }
                }


            } else {
                String cblx = payToPredictVo.getInsuType();
                BigDecimal sumfee = payToPredictVo.getSumfee();
                //基金比例类型
                payToPredictVo.setFundSourceType("估算费用");
                if ("1".equals(cblx) || "01".equals(cblx) || "310".equals(cblx)) {
                    payToPredictVo.setFundAmtSum(sumfee.multiply(empFundRatio));
                } else if ("2".equals(cblx) || "02".equals(cblx) || "390".equals(cblx)) {
                    payToPredictVo.setFundAmtSum(sumfee.multiply(residFundRatio));
                } else {
                    BigDecimal fundRatio = empFundRatio.add(residFundRatio).divide(new BigDecimal(2), 4, BigDecimal.ROUND_HALF_UP);
                    payToPredictVo.setFundAmtSum(sumfee.multiply(fundRatio));
                    payToPredictVo.setFundSourceType("其他类型估算费用");
                }
            }
            //个人承担费用
            payToPredictVo.setPresonBearCost(payToPredictVo.getSumfee().subtract(payToPredictVo.getFundAmtSum()));
        }
    }

    private void computeAddItiveScore(DipPayToPredictVo vo, Map<String, Object> params) {
        BigDecimal addItiveValue = PaymentConst.DEFAULT_SCORE_0;

        List<BigDecimal> addItiveValueList = new ArrayList<>();
        //CMI加成 暂无

        //重点学科（专科）加成
        BigDecimal professionalCof = addItiveProfessionalDisease(vo, params);
        addItiveValueList.add(professionalCof);
        //中医优势病种加成
        BigDecimal tcmDiagCof = addItiveChineseDisease(vo, params);
        addItiveValueList.add(tcmDiagCof);
        // 年龄病种加成
        BigDecimal olderAddItiveCof = addItiveOlderDisease(vo);
        addItiveValueList.add(olderAddItiveCof);
        // 单一就高原则 重点/中医优势/年龄 满足任意两项
        addItiveValue = getMaxAddItiveValue(addItiveValueList);
        // 医院系数加成  高低倍率不需要此加成
        addItiveValue = addItiveHospital(vo, params).add(addItiveValue);
        BigDecimal addSco =addItiveValue.multiply(vo.getRefer_sco());
        // 信用等级加成 州医疗保障基金监管信用评价等级(A) 加成0.1% 不用
        //基层病种统一取同城同病同价
        vo.setGrstCof(DEFAULT_COEFFICIENT_1);

//        //中医医疗机构加成
//        BigDecimal ChineseOrangeSco = addItiveChineseOrange(vo,params,addItiveValueList);
//        //加成分值
//        addItiveValue= addItiveValue.add(ChineseOrangeSco).multiply(vo.getRefer_sco());

        // 计算总分值
        vo.setAddItiveValue(addSco);
        vo.setTotlSco(vo.getSettleMentScore().add(addSco));
    }

    private BigDecimal addItiveChineseDisease(DipPayToPredictVo vo, Map<String, Object> params) {
        BigDecimal tcmDiagSco = BigDecimal.ZERO;
        List<DipDiseaseTypeVo> chinsesDisease = (List<DipDiseaseTypeVo>) params.get(GenerateScoreUtil.KEY_CHINESE_DISEASE);


        if (ValidateUtil.isEmpty(chinsesDisease)) {
            vo.setTcmAdtCof(tcmDiagSco.add(BigDecimal.ONE));
            return tcmDiagSco;
        }
        for (DipDiseaseTypeVo disease : chinsesDisease) {
            if (disease.getDipCodg().equals(vo.getDipCodg())) {
                if (vo.getTCMRatio() != null && vo.getTCMRatio().compareTo(new BigDecimal(0.50)) > 0) {
                    tcmDiagSco = new BigDecimal("0.02");
                }
            }
        }

        vo.setTcmAdtCof(tcmDiagSco.add(BigDecimal.ONE));
        return tcmDiagSco;
    }

    private BigDecimal addItiveChineseOrange(DipPayToPredictVo vo, Map<String, Object> params, List<BigDecimal> addItiveValueList) {
        BigDecimal ChineseOrangeSco = BigDecimal.ZERO;
        if (vo.getTCMRatio() != null) {
            if (vo.getTCMRatio().compareTo(new BigDecimal(0.3)) > 0 && vo.getTCMRatio().compareTo(new BigDecimal(0.4)) <= 0) {
                ChineseOrangeSco = new BigDecimal("0.015");
            } else if (vo.getTCMRatio().compareTo(new BigDecimal(0.4)) > 0) {
                ChineseOrangeSco = new BigDecimal("0.02");
            }
        }
        vo.setTcmOrgCof(ChineseOrangeSco.add(BigDecimal.ONE));
        return ChineseOrangeSco;
    }

    private BigDecimal addItiveProfessionalDisease(DipPayToPredictVo vo, Map<String, Object> params) {
        BigDecimal professionalRate = BigDecimal.ZERO;
        // 重点专科
        List<DipDiseaseTypeVo> focusDisease = (List<DipDiseaseTypeVo>) params.get(KEY_FOCUS_DISEASE);

        if (ValidateUtil.isEmpty(focusDisease)) {
            vo.setProfessionalRate(professionalRate.add(BigDecimal.ONE));
            return professionalRate;
        }

        for (DipDiseaseTypeVo diease : focusDisease) {
            // 判断是否为重点学(专)科
            if (!ValidateUtil.isEmpty(diease.getInhospDeptCodg())
                    && !ValidateUtil.isEmpty(vo.getDeptCode())
                    && diease.getInhospDeptCodg().equals(vo.getDeptCode())) {
                if (!ValidateUtil.isEmpty(diease.getKeySpcyType())) {
                    //重点学（专）科等级 国家
                    if (PROFESSIONAL_LEVEL_1.equals(diease.getKeySpcyType()) || PROFESSIONAL_LEVEL_2.equals(diease.getKeySpcyType())) {
                        professionalRate = new BigDecimal("0.01");
                    }
                    //重点学（专）科等级 省
                    else if (PROFESSIONAL_LEVEL_3.equals(diease.getKeySpcyType()) || PROFESSIONAL_LEVEL_4.equals(diease.getKeySpcyType())) {
                        professionalRate = new BigDecimal("0.005");
                    }
                    //重点学（专）科等级 州
                    else if (PROFESSIONAL_LEVEL_7.equals(diease.getKeySpcyType()) || PROFESSIONAL_LEVEL_8.equals(diease.getKeySpcyType())) {
                        professionalRate = new BigDecimal("0.003");
                    }
                }
            }
        }
        vo.setProfessionalRate(professionalRate.add(BigDecimal.ONE));
        return professionalRate;
    }


    /**
     * 计算年龄病种加成
     * 年龄大于60周岁，结算分值加成1.05
     *
     * @param vo
     * @return
     */
    private BigDecimal addItiveOlderDisease(DipPayToPredictVo vo) {
        BigDecimal ageSco = PaymentConst.DEFAULT_SCORE_0;
        BigDecimal age = vo.getAge();
        BigDecimal dayAge = vo.getDayAge();
        BigDecimal nwbDagAge = new BigDecimal(28);
        BigDecimal age80 = new BigDecimal(80);
        BigDecimal age70 = new BigDecimal(70);
        BigDecimal age60 = new BigDecimal(60);
        BigDecimal age6 = new BigDecimal(6);
        if (ValidateUtil.isEmpty(dayAge)) {
            dayAge = BigDecimal.ZERO;
        }
        if (ValidateUtil.isEmpty(age)) {
            dayAge = BigDecimal.ZERO;
        }
        //0-28天 、 80周岁以上加成2.5%
        if ((BigDecimal.ZERO.compareTo(dayAge) < 0 && nwbDagAge.compareTo(dayAge) >= 0) || age80.compareTo(age) < 0) {
            ageSco = new BigDecimal(0.025);
        }
        //29天-1周岁 70-79周岁 加成2%
        if ((nwbDagAge.compareTo(dayAge) < 0) || (age70.compareTo(dayAge) <= 0 && age80.compareTo(age) > 0)) {
            ageSco = new BigDecimal(0.02);
        }
        //1-6周岁、60-69周岁加成 1%
        if ((BigDecimal.ONE.compareTo(age) < 0 && age6.compareTo(age) >= 0) ||
                (age60.compareTo(age) <= 0 && age70.compareTo(age) > 0)) {
            ageSco = new BigDecimal(0.01);
        }


        vo.setYoungCof(ageSco.add(BigDecimal.ONE));
        return ageSco;

    }

    /**
     * 单一就高原则
     * 获取在中医、基层、低龄|高龄、重点中最高的分值，相同随机选一个
     *
     * @param addItiveValueList
     * @return
     */
    private BigDecimal getMaxAddItiveValue(List<BigDecimal> addItiveValueList) {
        BigDecimal max = PaymentConst.DEFAULT_SCORE_0;
        for (BigDecimal value :
                addItiveValueList) {
            if (value.compareTo(max) > 0) {
                max = value;
            }
        }
        return max;
    }


    /**
     * 医院系数加成
     * 基层病种、特殊病种、非稳定病种不参与医院系数加成
     *
     * @param vo
     * @return
     */
    private BigDecimal addItiveHospital(DipPayToPredictVo vo, Map<String, Object> params) {

        BigDecimal refer_sco = PaymentConst.DEFAULT_SCORE_0;
        // 获取配置信息
        List<ViewHospitalVo> hospitalInfos = (List<ViewHospitalVo>) params.get(PaymentConst.HOSPITAL_INFO);

        // 判断是否为德昌高低倍率不考虑添加医疗机构系数
        if (DrgConst.CASE_TYPE_3.equals(vo.getDiseType())) {
            // 判断是否找到对应的医疗机构系数配置
            if (!ValidateUtil.isEmpty(vo.getHospitalId())) {
                for (ViewHospitalVo hospital : hospitalInfos) {
                    if (vo.getHospitalId().equals(hospital.getHospitalId())) {
                        vo.setHospCof(hospital.getHospCof());
                        refer_sco = hospital.getHospCof().subtract(PaymentConst.DEFAULT_COEFFICIENT_1);
                    }
                }
            }
            vo.setHospCof(PaymentConst.DEFAULT_COEFFICIENT_1);

        }else{
            vo.setHospCof(PaymentConst.DEFAULT_COEFFICIENT_1);

        }
        return refer_sco;


    }
}
