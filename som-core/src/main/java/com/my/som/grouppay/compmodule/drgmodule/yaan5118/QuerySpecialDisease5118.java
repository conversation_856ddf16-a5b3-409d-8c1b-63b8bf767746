package com.my.som.grouppay.compmodule.drgmodule.yaan5118;

import com.my.som.common.constant.DrgConst;
import com.my.som.common.util.ValidateUtil;
import com.my.som.dto.medicalQuality.BusSettleListMainInfo;
import com.my.som.dto.medicalQuality.SettleListMainInfoQueryParam;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import com.yomahub.liteflow.core.NodeComponent;

import java.util.List;
import java.util.stream.Collectors;

@LiteflowComponent(value = "QuerySpecialDisease5118", name = "特病单议汇总")
public class QuerySpecialDisease5118 extends NodeComponent {
    /**
     * (一) 重症及多学科诊治病例:危急症抢救患者重症监护病房床位使用天数大于3天且大于当次住院床位使用总天数60%的，
            或者重症监护病房床位使用天数大于15天的;
     * (二) 超长住院病例: 住院天数大于60日，或者平均住院天数大于等于5天 且住院天数大于该病种平均住院天数5倍以上的;
     * (三) 超高费用病例:单次住院医疗费用超过所对应病种次均医疗总费用5倍及以上的;
     */

    @Override
    public void process() throws Exception {
        List<BusSettleListMainInfo> busSettleListMainInfos = this.getRequestData();
        SettleListMainInfoQueryParam param = this.getContextBean(SettleListMainInfoQueryParam.class);
        List<BusSettleListMainInfo> resultList = busSettleListMainInfos.stream()
                    .map(info -> {
                            info.setRefeFee(info.getAvgFee());
                            String type = "" ;
                            String desc = "" ;
                            
                            if (ValidateUtil.isEmpty(info.getDisetype() )) {
                                type +="未入组病例;";
                                desc += "未入组病例;";
                            }else{
                                if (DrgConst.CASE_TYPE_NONE_GROUP.equals(info.getDisetype() )) {
                                    type +="未入组病例;";
                                    desc +="未入组病例;";
                                }
                                if (DrgConst.CASE_TYPE_4.equals(info.getDisetype() )) {
                                    type +="无标杆病例;";
                                    desc +="无标杆病例%;";
                                }
                                if (DrgConst.DISE_TYPE_5.equals(info.getDisetype() )) {
                                    type +="非稳定病例;";
                                    desc +="非稳定病例;";
                                }
                                if (DrgConst.CASE_TYPE_QY_GROUP.equals(info.getDisetype() )) {
                                    type +="QY组病例;";
                                    desc +="QY组病例;";
                                }


                            }
                             if(!ValidateUtil.isEmpty(type)){
                                 info.setType(type);
                                 info.setDesc(desc);
                             }
                        return info;
                    })
                // 过滤掉不满足条件的数据
                    .filter(info -> info.getType() != null )
                .filter(info -> {
                    // 根据 param.getScsMDT() 决定是否添加额外的过滤条件
                    if (!ValidateUtil.isEmpty(param.getQYDiseType()) && "1".equals(param.getQYDiseType())) {
                        return info.getType().contains("QY组病例");
                    } else {
                        return true; // 保留所有满足 type != null 的记录
                    }
                })
                .filter(info -> {
                    // 根据 param.getScsMDT() 决定是否添加额外的过滤条件
                    if (!ValidateUtil.isEmpty(param.getNoBenchmarkDiseType()) &&  "1".equals(param.getNoBenchmarkDiseType())) {
                        return info.getType().contains("无标杆病例");
                    } else {
                        return true; // 保留所有满足 type != null 的记录
                    }
                })
                .filter(info -> {
                    // 根据 param.getScsMDT() 决定是否添加额外的过滤条件
                    if (!ValidateUtil.isEmpty(param.getNotStableDiseType()) && "1".equals(param.getNotStableDiseType())) {
                        return info.getType().contains("非稳定病例");
                    } else {
                        return true; // 保留所有满足 type != null 的记录
                    }
                })
                .filter(info -> {
                    // 根据 param.getScsMDT() 决定是否添加额外的过滤条件
                    if (!ValidateUtil.isEmpty(param.getNoGroupDiseType()) && "1".equals(param.getNoGroupDiseType())) {
                        return info.getType().contains("未入组病例");
                    } else {
                        return true; // 保留所有满足 type != null 的记录
                    }
                })
                .collect(Collectors.toList());
        busSettleListMainInfos.clear();
        busSettleListMainInfos.addAll(resultList);
    }
}
