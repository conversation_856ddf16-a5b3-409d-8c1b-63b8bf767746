package com.my.som.grouppay.compmodule.dipmodule.liangshan_5134;

import com.my.som.common.constant.DrgConst;
import com.my.som.common.util.ValidateUtil;
import com.my.som.grouppay.compmodule.dipmodule.common.PaymentConst;
import com.my.som.grouppay.vo.HospBaseBusiVo;
import com.my.som.util.SysCommonConfigUtil;
import com.my.som.vo.dataHandle.PayToPredict.PayToPredictVo;
import com.my.som.vo.dipBusiness.DipPayToPredictVo;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import com.yomahub.liteflow.core.NodeComponent;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.List;

@LiteflowComponent(value = "DipCalculateGeneratePointsNode5134", name = "分值计算")
public class DipCalculateGeneratePointsNode5134 extends NodeComponent {
    @Override
    public void process() throws Exception {
        // 获取业务上下文
        HospBaseBusiVo hospBaseBusiVo = this.getContextBean(HospBaseBusiVo.class);
        List<DipPayToPredictVo> payToPredictVoList = hospBaseBusiVo.getDipPayToPredictVoList();

        for (DipPayToPredictVo payToPredictVo : payToPredictVoList) {
            String paymentDiseType = payToPredictVo.getPaymentDiseType();
            BigDecimal score = BigDecimal.ZERO;
            BigDecimal referSco = payToPredictVo.getRefer_sco();
            BigDecimal adjmCof = payToPredictVo.getAdjm_cof();
            BigDecimal biasRateFactor = payToPredictVo.getBiasRateFactor();
            BigDecimal asstListAdm = payToPredictVo.getAsstListAdm();
            //判断是否为医保病人
            if( ValidateUtil.isEmpty(payToPredictVo.getBusinessSerial() ) ) {
                payToPredictVo.setSumfee(payToPredictVo.getInHosTotalCost());
            }else{
                if(ValidateUtil.isEmpty(payToPredictVo.getCheckTotalFee()) || BigDecimal.ZERO.compareTo(payToPredictVo.getCheckTotalFee())== 0){
                    payToPredictVo.setSumfee(payToPredictVo.getInHosTotalCost());
                }else{
                    payToPredictVo.setSumfee(payToPredictVo.getCheckTotalFee() );}
            }
            // 患者住院总费用
            BigDecimal totalHospitalizationCost = payToPredictVo.getSumfee();

            switch (paymentDiseType) {
                case DrgConst.DISETYPE_CORE: // 核心病种
                    // 计算核心病种的总分值
                    score = calculateScore(referSco, adjmCof, biasRateFactor, asstListAdm);
                    break;

                case DrgConst.DISETYPE_COMPOSITE: // 综合病种
                    // 计算综合病种的总分值
                    score = calculateScore(referSco, adjmCof, biasRateFactor);
                    break;

                case DrgConst.DISETYPE_BASIC: // 基层病种
                    // 计算基层病种的总分值
                    BigDecimal grstCof = new BigDecimal(1);
                    payToPredictVo.setBaseDiseaseRate(grstCof);
                    score = referSco.multiply(grstCof).setScale(4, BigDecimal.ROUND_HALF_UP);
                    break;

                case DrgConst.DISETYPE_CHM: // 中医病种
                    // 计算中医病种的总分值
//                    BigDecimal tcmAdtCof = new BigDecimal(1.03);
                    BigDecimal tcmAdtCof = new BigDecimal(1);
                    payToPredictVo.setTcmAdtCof(tcmAdtCof);
                    score = calculateScore(referSco, adjmCof, biasRateFactor, tcmAdtCof);
                    break;

                case DrgConst.DISETYPE_NONE_INGROUP: // 未入组
                    // 计算未入组病例，取区域最低病种分值
                    score = hospBaseBusiVo.getMinPointsSize();
                    break;

                case DrgConst.DISETYPE_NO_BENCHMARK: //无标杆
                    //无标杆=总费用/标杆*1000
                    if (!ValidateUtil.isEmpty(hospBaseBusiVo.getRegiAverageFee())) {
                        score = totalHospitalizationCost
                                .divide(hospBaseBusiVo.getRegiAverageFee(),BigDecimal.ROUND_HALF_UP)
                                .multiply(new BigDecimal(1000))
                                .setScale(4, BigDecimal.ROUND_HALF_UP);
                    } else {
                        score = new BigDecimal(BigInteger.ZERO);
                    }
                    break;

                default:
                    break;
            }

            // 设置计算后的分值和其他属性
            payToPredictVo.setTotlSco(score);
            payToPredictVo.setSettleMentScore(score);
            payToPredictVo.setAddItiveValue(BigDecimal.ZERO);
            setOtherCoefficient(payToPredictVo);
        }

    }

    /**
     * 没有加成情况下，设置其他系数
     *
     * @param vo
     */
    private void setOtherCoefficient(DipPayToPredictVo vo) {
        vo.setTcmAdtCof(PaymentConst.DEFAULT_COEFFICIENT_0);
        vo.setGrstCof(PaymentConst.DEFAULT_COEFFICIENT_0);
        //凉山关闭低龄系数
        vo.setYoungCof(null);
        vo.setOlderCof(PaymentConst.DEFAULT_COEFFICIENT_0);
        vo.setProfessionalRate(PaymentConst.DEFAULT_COEFFICIENT_0);
        vo.setHospCof(PaymentConst.DEFAULT_COEFFICIENT_0);
        vo.setAddItiveValue(PaymentConst.DEFAULT_SCORE_0);
    }

    /**
     * 计算病种分值
     *
     * @param referSco  基础分值
     * @param cofactors 可变参数系数列表
     * @return 计算后的分值
     */
    private BigDecimal calculateScore(BigDecimal referSco, BigDecimal... cofactors) {
        BigDecimal score = referSco;
        for (BigDecimal factor : cofactors) {
            if (factor != null) {
                score = score.multiply(factor);
            }
        }
        return score.setScale(4, BigDecimal.ROUND_HALF_UP);
    }

}
