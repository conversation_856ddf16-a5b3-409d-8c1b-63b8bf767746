package com.my.som.grouppay.compmodule.dipmodule.common.calc;

import com.my.som.common.constant.DrgConst;
import com.my.som.common.util.MapUtil;
import com.my.som.common.util.ValidateUtil;
import com.my.som.dto.pregroup.PreGroupDto;
import com.my.som.grouppay.vo.HospBaseBusiVo;
import com.my.som.util.GenerateScoreUtil;
import com.my.som.util.SysCommonConfigUtil;
import com.my.som.vo.dipBusiness.DipPayToPredictVo;
import com.my.som.vo.pregroup.PreGroupVo;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import com.yomahub.liteflow.core.NodeComponent;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@LiteflowComponent(value = "DipPaymentResultComponent", name = "前端返回结果封装")
public class DipPaymentResultComponent extends NodeComponent {

    @Override
    public void process() throws Exception {
        // 1.获取业务参数
        List<PreGroupVo> preGroupVos = this.getRequestData();
        // 2.获取业务上下文
        HospBaseBusiVo hospBaseBusiVo = this.getContextBean(HospBaseBusiVo.class);
        PreGroupDto dto = this.getContextBean(PreGroupDto.class);

        List<Map<String, Object>> mapList = new ArrayList<>();

        for (DipPayToPredictVo payToPredictVo : hospBaseBusiVo.getDipPayToPredictVoList()) {
            Map<String, Object> map = MapUtil.objectToMap(payToPredictVo);
            // 设置编码
            map.put("dipCodg", payToPredictVo.getDipCodg());
            map.put("preHosCost", payToPredictVo.getPreHospExamfee());
            //辅助目录使用情况
            map.put("auxiliaryAge", payToPredictVo.getAsstListAgeGrp());
            map.put("auxiliaryIllness", payToPredictVo.getAsstListDiseSevDeg());
            map.put("auxiliaryTumour", payToPredictVo.getAsstListTmorSevDeg());
            map.put("auxiliaryBurn", payToPredictVo.getAuxiliaryBurn());
            payToPredictVo.setInHosTotalCost(ValidateUtil.isEmpty(payToPredictVo.getInHosTotalCost())? BigDecimal.ZERO : payToPredictVo.getInHosTotalCost() );
            payToPredictVo.setPreHospExamfee(ValidateUtil.isEmpty(payToPredictVo.getPreHospExamfee())? BigDecimal.ZERO : payToPredictVo.getPreHospExamfee());

            map.put("sumfee",payToPredictVo.getSumfee());
            // 设置名称
            map.put("dipName", payToPredictVo.getDipName());
            //基准分值
            map.put("jzfz", payToPredictVo.getRefer_sco());
            //级别基本系数
            map.put("jbjbxs", payToPredictVo.getAdjm_cof());
            //预测分值
            map.put("ycfzFirst", payToPredictVo.getSettleMentScore());

            //中医优势加成系数
            map.put("zyysjcxs", payToPredictVo.getTcmAdtCof());
            //基层病种加成系数
            map.put("jcbzjcxs", payToPredictVo.getGrstCof());
            //年龄病种加成系数
            map.put("dlbzjcxs", payToPredictVo.getYoungCof());
            //重点学专科加成系数
            map.put("zdxkjcxs", payToPredictVo.getProfessionalRate());

            //加成分值
            map.put("jcfz", payToPredictVo.getAddItiveValue());
            //总分值
            map.put("zfz", ValidateUtil.isEmpty(payToPredictVo.getTotlSco()) ? BigDecimal.ZERO : payToPredictVo.getTotlSco());
            //分值单价
            map.put("fzdj", payToPredictVo.getScorePrice());

            String diseType = payToPredictVo.getDiseType();
            switch (diseType) {
                case "0":
                    diseType = "未入组";
                    break;
                case "1":
                    diseType = "超高病例";
                    break;
                case "2":
                    diseType = "超低病例";
                    break;
                case "3":
                    diseType = "正常病例";
                    break;
                case "4":
                    diseType = "无标杆病例";
                    break;
                case "5":
                    diseType = "非稳定病例";
                    break;
                case "6":
                    diseType = "极高费用病例";
                    break;
                case "7":
                    diseType = "基层病种";
                    break;
            }
            if (DrgConst.DISETYPE_BASIC.equals(payToPredictVo.getPaymentDiseType())) {
                diseType = "基层病种";
            }
            if (DrgConst.DISETYPE_CHM.equals(payToPredictVo.getPaymentDiseType())) {
                diseType = "中医病种";
            }
            //病案类型
            map.put("balx", diseType);
            //预估费用
            map.put("ycfy", payToPredictVo.getForecast_fee());
            //预估结算差参考
            map.put("fycy", payToPredictVo.getProfitloss());
            //
            map.put("diagnoseCode", payToPredictVo.getWMCode());
            //
            map.put("caseTypeT", GenerateScoreUtil.CASE_TYPE_3.equals(payToPredictVo.getDiseType()) ? DrgConst.TYPE_1 : DrgConst.TYPE_2);
            // 智能查漏使用
            map.put("payToPredictVo", payToPredictVo);
            map.put("ybzfy", ValidateUtil.isEmpty(payToPredictVo.getCheckTotalFee())?BigDecimal.ZERO : payToPredictVo.getCheckTotalFee());
            // 基金比例部分
            //基金总额
            map.put("fundAmtSum", payToPredictVo.getFundAmtSum());
            //个人自付部分
            map.put("presonBearCost", payToPredictVo.getPresonBearCost());

            //预测报销费用
            map.put("preFundFee", payToPredictVo.getPreFundFee());

            map.put("FundFeeDiff", payToPredictVo.getPreFundFee().subtract(payToPredictVo.getFundAmtSum()));
            //比例
            map.put("fundRatio", payToPredictVo.getFundRatio());
            // 比例计算类型
            map.put("fundSourceType", payToPredictVo.getFundSourceType());
            String ALGORITHMA_TYPE = (String) SysCommonConfigUtil.get(DrgConst.ALGORITHMA_TYPE);
            if (ALGORITHMA_TYPE != null &&  !ValidateUtil.isEmpty(ALGORITHMA_TYPE)) {
                if("1".equals(ALGORITHMA_TYPE)){
                    BigDecimal fundAmtSum = payToPredictVo.getFundAmtSum();
                    if (!ValidateUtil.isEmpty(fundAmtSum) &&BigDecimal.ZERO.compareTo(fundAmtSum) != 0) {
//                        map.put("ycfy", payToPredictVo.getOriginal_forecast_fee());
//                        map.put("fycy", payToPredictVo.getOriginalProfitloss());
                        map.put("preFundFee", payToPredictVo.getForecast_fee());
                        map.put("FundFeeDiff", payToPredictVo.getForecast_fee().subtract(payToPredictVo.getFundAmtSum()));
                    }
                }
            }
            mapList.add(map);
        }
        hospBaseBusiVo.setResultMap(mapList);
    }
}


