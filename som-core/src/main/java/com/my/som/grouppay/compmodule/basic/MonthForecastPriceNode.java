package com.my.som.grouppay.compmodule.basic;

import com.my.som.common.constant.DrgConst;
import com.my.som.common.util.ValidateUtil;
import com.my.som.dto.common.PriceDto;
import com.my.som.dto.dataConfig.HospitalInfoDto;
import com.my.som.grouppay.vo.HospBaseBusiVo;
import com.my.som.mapper.common.DipConfigMapper;
import com.my.som.mapper.common.DrgConfigMapper;
import com.my.som.util.SysCommonConfigUtil;
import com.my.som.vo.common.DipConfigVo;
import com.my.som.vo.dataConfig.ViewHospitalVo;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import com.yomahub.liteflow.core.NodeComponent;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@LiteflowComponent(value = "MonthForecastPriceNode", name = "获取月度预测单价")
public class MonthForecastPriceNode extends NodeComponent {

    @Autowired
    private DrgConfigMapper drgConfigMapper;

    @Autowired
    private DipConfigMapper dipConfigMapper;

    @Override
    public void process() throws Exception {
        // 1.获取业务对象
        HospBaseBusiVo hospBaseBusiVo = this.getContextBean(HospBaseBusiVo.class);
        List<DipConfigVo> dipConfigVos = null;
        //获取城乡城职单价，可构建参数查出所有期号及年度单价配置，病例根据结算期号匹配
        if (DrgConst.GROUP_TYPE_NAME_DIP.equals(SysCommonConfigUtil.get(DrgConst.FZLX))) {
            dipConfigVos = dipConfigMapper.selectData();
        } else if (DrgConst.GROUP_TYPE_NAME_DRG.equals(SysCommonConfigUtil.get(DrgConst.FZLX))) {
            PriceDto queryPriceDto = new PriceDto();
            queryPriceDto.setInsuplcAdmdvs(hospBaseBusiVo.getInsuplcAdmdvs());
            dipConfigVos = drgConfigMapper.selectDrgData(queryPriceDto);
        }
        Map<String, DipConfigVo> viewHospitalVoMap = dipConfigVos.stream().collect(Collectors.toMap(dipConfigVo -> {
            if (ValidateUtil.isEmpty(dipConfigVo.getYm())) {
                return dipConfigVo.getKey();
            } else {
                return dipConfigVo.getYm() + "-" + dipConfigVo.getKey();
            }
        }, dipConfigVo -> dipConfigVo));
        hospBaseBusiVo.setDipConfigVoMap(viewHospitalVoMap);
    }
}