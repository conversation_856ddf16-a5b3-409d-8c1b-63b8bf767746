package com.my.som.grouppay.compmodule.dipmodule.deyang_5106;

import com.my.som.common.constant.DrgConst;
import com.my.som.common.util.ValidateUtil;
import com.my.som.grouppay.compmodule.dipmodule.common.PaymentConst;
import com.my.som.grouppay.vo.HospBaseBusiVo;
import com.my.som.util.GenerateScoreUtil;
import com.my.som.vo.dataConfig.DipDiseaseTypeVo;
import com.my.som.vo.dataConfig.ViewHospitalVo;
import com.my.som.vo.dipBusiness.DipPayToPredictVo;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import com.yomahub.liteflow.core.NodeComponent;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.*;
import java.util.stream.Collectors;

import static com.my.som.grouppay.compmodule.dipmodule.common.PaymentConst.*;
import static com.my.som.util.GenerateScoreUtil.*;


@LiteflowComponent(value = "DipCalculateOtherSpecialNode5106", name = "计算特殊政策病例分值")
public class DipCalculateOtherSpecialNode5106 extends NodeComponent {

    @Override
    public void process() throws Exception {
        // 加成系数配置信息
//        private  Map<String, Object> params ;
        // 获取业务上下文
        HospBaseBusiVo hospBaseBusiVo = this.getContextBean(HospBaseBusiVo.class);
        List<DipPayToPredictVo> payToPredictVoList = hospBaseBusiVo.getDipPayToPredictVoList();
        // 获取配置信息
        Map calculateScoreParams = this.getContextBean(Map.class);
        for (DipPayToPredictVo payToPredictVo : payToPredictVoList) {
            generaFundRatioType(payToPredictVo);
            computeAddItiveScore(payToPredictVo, calculateScoreParams);
        }
    }

    private static void generaFundRatioType(DipPayToPredictVo payToPredictVo) {

        BigDecimal empFundRatio = payToPredictVo.getEmpFundRatio();
        BigDecimal residFundRatio = payToPredictVo.getResidFundRatio();

            //基金总费用
            BigDecimal fundAmtSum = payToPredictVo.getFundAmtSum();

            String cblx = payToPredictVo.getInsuType();

            //基金比例类型
            if("1".equals(cblx) || "01".equals(cblx) || "310".equals(cblx)) {
                payToPredictVo.setReferenceFundRatio(empFundRatio);
            }
            if("2".equals(cblx) || "02".equals(cblx) || "390".equals(cblx)) {
                payToPredictVo.setReferenceFundRatio(residFundRatio);
            }
        if (!ValidateUtil.isEmpty(fundAmtSum) &&BigDecimal.ZERO.compareTo(fundAmtSum) != 0) {
            payToPredictVo.setFundSourceType("实际费用");
            //个人承担费用
            payToPredictVo.setPresonBearCost(payToPredictVo.getSumfee().subtract(payToPredictVo.getFundAmtSum()));
        }else {
            payToPredictVo.setPresonBearCost(payToPredictVo.getSumfee());
        }
    }

    /**
     * 医疗系数加成：dip病例总和/dip总病例数 /100 （不包含未入组以及不稳定病例）
     *              加成范围除 不稳定病组、特殊、基层病例以外的所有dip 病例
     * 单一就高原则：当某一病例同时符合中医优势病例、基层病例、低龄病例和重点学（转）科病例中两项以及以上时
     */
    /**
     * 病例结算分值倾斜规则
     *  1.中医优势病例
     *  2.基层病种
     *  3.低龄病例
     *  4.重点学（专）科病例
     */
    /**
     *
     * @param vo
     * @param params
     */
    private void computeAddItiveScore(DipPayToPredictVo vo, Map<String, Object> params) {

        BigDecimal addItiveValue = DEFAULT_SCORE_0;
        List<BigDecimal> addItiveValueList = new ArrayList<>();
        // 中医优势病种加成
        BigDecimal chineseAddItiveValue = addItiveChineseDisease(vo, params);
        addItiveValueList.add(chineseAddItiveValue);
        // 基层病种加成
        BigDecimal baseAddItiveValue = addItiveBaseDisease(vo, params);
        addItiveValueList.add(baseAddItiveValue);
        // 年龄病种加成
        BigDecimal youngerAddItiveValue = addItiveYoungerDisease(vo, params);
        addItiveValueList.add(youngerAddItiveValue);
        // 单一就高原则
        addItiveValue = getMaxAddItiveValue(addItiveValueList);

        // 医院系数加成
        addItiveValue = addItiveHospital(vo, params).add(addItiveValue);


        // 辅助目录加成
        addAuxValue(vo, params);
        // 重点专科病种加成 2024 年新规定 除了基层 非稳定 极高之外的病例
//        BigDecimal professionalAddItiveValue = addItiveProfessionalDisease(vo, params);
//        addItiveValueList.add(professionalAddItiveValue);
        // 判断是否为基层病种、非稳定病种、特殊病种
        List<DipDiseaseTypeVo> focusDisease = (List<DipDiseaseTypeVo>) params.get(GenerateScoreUtil.KEY_FOCUS_DISEASE);
        if(focusDisease.size() > 0) {
            addItiveValue = getItiveProfessionalDisease(vo, addItiveValue, params);
        }else {
            vo.setProfessionalRate(DEFAULT_COEFFICIENT_1);
        }

        vo.setAddItiveValue(addItiveValue);
        // 计算总分值
        vo.setTotlSco(vo.getSettleMentScore().add(addItiveValue));
    }

    private static BigDecimal getItiveProfessionalDisease(DipPayToPredictVo vo, BigDecimal addItiveValue, Map<String, Object> params) {

        // 是否为基层病种
        // 获取配置信息

        if ((ValidateUtil.isNotEmpty(vo.getBaseDiseaseStatus()) && PaymentConst.DISEASE_STATUS_1.equals(vo.getBaseDiseaseStatus())) // 基层
                ||(ValidateUtil.isNotEmpty(vo.getStableFlag()) && PaymentConst.IS_SD_DISE.equals(vo.getStableFlag())) // 非稳定
                || (ValidateUtil.isNotEmpty(vo.getDiseType()) && vo.getDiseType().equals(DrgConst.DISE_TYPE_6))) { //极高费用病例"
            vo.setProfessionalRate(DEFAULT_COEFFICIENT_1);
            return addItiveValue;
        }

        //重点专科系数 1.001
        BigDecimal professionalAddItiveValue = new BigDecimal(1.001);
        vo.setProfessionalRate(professionalAddItiveValue);
        if(addItiveValue.compareTo(BigDecimal.ZERO) >0){
            addItiveValue =  addItiveValue.add(vo.getSettleMentScore().multiply(professionalAddItiveValue.subtract(BigDecimal.ONE)));
        }else {
            addItiveValue = vo.getSettleMentScore().multiply(professionalAddItiveValue.subtract(BigDecimal.ONE));
        }

        return addItiveValue;
    }

    /**
     * 计算中医优势加成分值
     * 在中医优势病种中中医费用占比达到50%的，为中医优势病种
     *
     * @param vo
     * @param params
     * @return
     */
    private static BigDecimal addItiveChineseDisease(DipPayToPredictVo vo, Map<String, Object> params) {

        BigDecimal refer_sco = DEFAULT_SCORE_0;

        // 获取配置信息
        List<DipDiseaseTypeVo> chinsesDisease = (List<DipDiseaseTypeVo>) params.get(GenerateScoreUtil.KEY_CHINESE_DISEASE);
        List<DipDiseaseTypeVo> coefficients = (List<DipDiseaseTypeVo>) params.get(GenerateScoreUtil.KEY_DISEASE_COEFFICIENT);

        if (ValidateUtil.isEmpty(chinsesDisease) || ValidateUtil.isEmpty(coefficients)) {
            vo.setTcmAdtCof(DEFAULT_COEFFICIENT_1);
            return refer_sco;
        }

        for (DipDiseaseTypeVo disease :
                chinsesDisease) {
            // 获取中医编码集合
            String[] split = disease.getChineseDiseaseCode().split("或");
            List<String> TCMCodeList = Arrays.asList(split);
            // 判断是否为中医优势病种
            if ((ValidateUtil.isNotEmpty(vo.getTCMCode()) && TCMCodeList.contains(vo.getTCMCode())) ||
                    (ValidateUtil.isNotEmpty(disease.getMedicalCode()) && disease.getMedicalCode().equals(vo.getWMCode()))) {
                // 判断中医花费是否占 50%
                if (vo.getHospitalizationExpenses() != null && vo.getHospitalizationExpenses().compareTo(DEFAULT_COST_0) != 0
                        && vo.getTCMTreatmentCost().divide(vo.getHospitalizationExpenses(), 8, BigDecimal.ROUND_HALF_UP)
                        .multiply(PROPORTION_BASE).compareTo(CHINESE_DISEASE_PROPORTION) == 1) {
                    // 计算中医优势加成分值
                    vo.setDieaseType(CHINESE_DISEASE);
                    vo.setChineseDiseaseStatus(DISEASE_STATUS_1);
                    for (DipDiseaseTypeVo adjm_cof :
                            coefficients) {
                        if (adjm_cof.getDiseaseCFTType().equals(vo.getDieaseType())) {
                            vo.setTcmAdtCof(DEFAULT_COEFFICIENT_1.add(adjm_cof.getDiseCof().divide(PROPORTION_BASE)));
                            refer_sco = vo.getSettleMentScore().multiply(adjm_cof.getDiseCof().divide(PROPORTION_BASE));
                            return refer_sco;
                        } else {
                            vo.setTcmAdtCof(DEFAULT_COEFFICIENT_1);
                        }
                    }
                } else {
                    vo.setTcmAdtCof(DEFAULT_COEFFICIENT_1);
                }
            } else {
                vo.setTcmAdtCof(DEFAULT_COEFFICIENT_1);
            }
        }
        return refer_sco;
    }

    /**
     * 计算基层病种加成
     * 通过医院等级判断 : 二乙为1%、一级和未定级为2%、基层为3%
     *
     * @param vo
     * @param params
     * @return
     */
    private static BigDecimal addItiveBaseDisease(DipPayToPredictVo vo, Map<String, Object> params) {

        BigDecimal refer_sco = DEFAULT_SCORE_0;

        // 获取配置信息
        List<DipDiseaseTypeVo> baseDisease = (List<DipDiseaseTypeVo>) params.get(GenerateScoreUtil.KEY_BASE_DISEASE);
        List<DipDiseaseTypeVo> coefficients = (List<DipDiseaseTypeVo>) params.get(GenerateScoreUtil.KEY_DISEASE_COEFFICIENT);

        if (ValidateUtil.isEmpty(baseDisease) || ValidateUtil.isEmpty(coefficients)) {
            vo.setGrstCof(DEFAULT_COEFFICIENT_1);
            return refer_sco;
        }
        for (DipDiseaseTypeVo disease :baseDisease) {
            // 判断是否为基层病种
            if (disease.getDipCodg().equals(vo.getDipCodg())) {

                // 判断医院级别是否满足
                if (BASE_HOSPITAL_LEVEL.compareTo(new BigDecimal(vo.getHospLv())) <= 0) {
                    vo.setDieaseType(BASE_DISEASE);
                    // 计算基层病种加成分值
                    for (DipDiseaseTypeVo adjm_cof :coefficients) {
                        if (vo.getDieaseType().equals(adjm_cof.getDiseaseCFTType())) {
                            // 确定医院等级
                            if (vo.getHospLv().equals(adjm_cof.getHospLv()) && ValidateUtil.isNotEmpty(adjm_cof.getHospLv())) {
                                vo.setGrstCof(DEFAULT_COEFFICIENT_1.add(adjm_cof.getDiseCof().divide(PROPORTION_BASE)));
                                vo.setBaseDiseaseStatus(DISEASE_STATUS_1);
                                refer_sco = vo.getSettleMentScore().multiply(adjm_cof.getDiseCof().divide(PROPORTION_BASE));
                                return refer_sco;
                            } else {
                                vo.setGrstCof(DEFAULT_COEFFICIENT_1);
                            }
                        } else {
                            vo.setGrstCof(DEFAULT_COEFFICIENT_1);
                        }
                    }
                } else {
                    vo.setGrstCof(DEFAULT_COEFFICIENT_1);
                }
            } else {
                vo.setGrstCof(DEFAULT_COEFFICIENT_1);
            }
        }

        return refer_sco;
    }

    /**
     * 计算低龄病种加成
     * 年龄不满14周岁 结算分值加成1.05
     *
     * @param vo
     * @param params
     * @return
     */
    private static BigDecimal addItiveYoungerDisease(DipPayToPredictVo vo, Map<String, Object> params) {
        BigDecimal refer_sco = DEFAULT_SCORE_0;

        // 获取配置参数
        List<DipDiseaseTypeVo> coefficients = (List<DipDiseaseTypeVo>) params.get(GenerateScoreUtil.KEY_DISEASE_COEFFICIENT);

        if (ValidateUtil.isEmpty(coefficients)) {
            vo.setYoungCof(DEFAULT_COEFFICIENT_1);
            return refer_sco;
        }

        // 判断是否为低龄
        if (vo.getAge() != null && vo.getAge() != BigDecimal.ZERO && vo.getAge().compareTo(YOUNGER_AGE) == -1) {
            vo.setDieaseType(YOUNGER_DISEASE);
            vo.setYoungerDiseaseStatus(DISEASE_STATUS_1);
            for (DipDiseaseTypeVo adjm_cof :
                    coefficients) {
                if (adjm_cof.getDiseaseCFTType().equals(vo.getDieaseType())) {
                    vo.setYoungCof(DEFAULT_COEFFICIENT_1.add(adjm_cof.getDiseCof().divide(PROPORTION_BASE)));
                    refer_sco = vo.getSettleMentScore().multiply(adjm_cof.getDiseCof().divide(PROPORTION_BASE));
                    return refer_sco;
                } else {
                    vo.setYoungCof(DEFAULT_COEFFICIENT_1);
                }
            }
        } else {
            vo.setYoungCof(DEFAULT_COEFFICIENT_1);
        }
        return refer_sco;
    }

    /**
     * 重点专科病种加成
     *
     * @param vo
     * @param params
     * @return
     */
    private static BigDecimal addItiveProfessionalDisease(DipPayToPredictVo vo, Map<String, Object> params) {

        BigDecimal refer_sco = DEFAULT_SCORE_0;

        // 获取配置信息
        // 重点专科
        List<DipDiseaseTypeVo> focusDisease = (List<DipDiseaseTypeVo>) params.get(GenerateScoreUtil.KEY_FOCUS_DISEASE);
       // 病种系数
        List<DipDiseaseTypeVo> coefficients = (List<DipDiseaseTypeVo>) params.get(GenerateScoreUtil.KEY_DISEASE_COEFFICIENT);

        if (ValidateUtil.isEmpty(focusDisease) || ValidateUtil.isEmpty(coefficients)) {
            vo.setProfessionalRate(DEFAULT_COEFFICIENT_1);
            return refer_sco;
        }

        for (DipDiseaseTypeVo diease :
                focusDisease) {
            // 判断是否为重点学(专)科
            if (ValidateUtil.isNotEmpty(diease.getInhospDeptCodg()) && ValidateUtil.isNotEmpty(vo.getDeptCode())
                    && diease.getInhospDeptCodg().equals(vo.getDeptCode())) {
                vo.setDieaseType(PROFESSIONAL_DISEASE);
                vo.setProfessionalDiseaseStatus(DISEASE_STATUS_1);
                vo.setProfessionalDiseaseType(diease.getKeySpcyType());
                for (DipDiseaseTypeVo coefficient :
                        coefficients) {
                    if (coefficient.getDiseaseCFTType().equals(vo.getDieaseType())) {
                        // 判断是否为市级重点专科
                        if (vo.getProfessionalDiseaseType().equals(PROFESSIONAL_LEVEL_5)) {
                            if(ValidateUtil.isNotEmpty(coefficient.getHospLv())){
                                if (vo.getHospLv().equals(coefficient.getHospLv())) {
                                    vo.setProfessionalRate(DEFAULT_COEFFICIENT_1.add(coefficient.getDiseCof().divide(PROPORTION_BASE)));
                                    refer_sco = vo.getSettleMentScore().multiply(coefficient.getDiseCof().divide(PROPORTION_BASE));
                                    return refer_sco;
                                } else {
                                    vo.setProfessionalRate(DEFAULT_COEFFICIENT_1);
                                }
                            } else {
                                vo.setProfessionalRate(DEFAULT_COEFFICIENT_1);
                            }
                        } else {
                            if (vo.getProfessionalDiseaseType().equals(coefficient.getKeyDisc())) {
                                vo.setProfessionalRate(DEFAULT_COEFFICIENT_1.add(coefficient.getDiseCof().divide(PROPORTION_BASE)));
                                refer_sco = vo.getSettleMentScore().multiply(coefficient.getDiseCof().divide(PROPORTION_BASE));
                                return refer_sco;
                            } else {
                                vo.setProfessionalRate(DEFAULT_COEFFICIENT_1);
                            }
                        }
                    } else {
                        vo.setProfessionalRate(DEFAULT_COEFFICIENT_1);
                    }
                }
            } else {
                vo.setProfessionalRate(DEFAULT_COEFFICIENT_1);
            }
        }
        return refer_sco;
    }

    /**
     * 单一就高原则
     * 获取在中医、基层、低龄|高龄、重点中最高的分值，相同随机选一个
     *
     * @param addItiveValueList
     * @return
     */
    private BigDecimal getMaxAddItiveValue(List<BigDecimal> addItiveValueList) {
        BigDecimal max = DEFAULT_SCORE_0;
        for (BigDecimal value :
                addItiveValueList) {
            if (value.compareTo(max) > 0) {
                max = value;
            }
        }
        return max;
    }


    /**
     * 医院系数加成
     * 基层病种、特殊病种、非稳定病种不参与医院系数加成
     *
     * @param vo
     * @return
     */
    private BigDecimal addItiveHospital(DipPayToPredictVo vo, Map<String, Object> params) {

        BigDecimal refer_sco = DEFAULT_SCORE_0;

        // 获取配置信息
        List<ViewHospitalVo> hospitalInfos = (List<ViewHospitalVo>) params.get(GenerateScoreUtil.HOSPITAL_INFO);

        // 判断是否为基层病种、非稳定病种、特殊病种
        if ((ValidateUtil.isNotEmpty(vo.getBaseDiseaseStatus()) && PaymentConst.DISEASE_STATUS_1.equals(vo.getBaseDiseaseStatus()))
                || (ValidateUtil.isNotEmpty(vo.getStableFlag()) && PaymentConst.IS_SD_DISE.equals(vo.getStableFlag()))
                || (ValidateUtil.isNotEmpty(vo.getDiseType()) && vo.getDiseType().equals(DrgConst.DISE_TYPE_6))) {
            vo.setHospCof(DEFAULT_COEFFICIENT_1);
            return refer_sco;
        }

        // 判断是否找到对应的医疗机构系数配置
        if (ValidateUtil.isNotEmpty(vo.getHospitalId())) {
            for (ViewHospitalVo hospital :
                    hospitalInfos) {
                if (vo.getHospitalId().equals(hospital.getHospitalId())) {
                    vo.setHospCof(hospital.getHospCof());
                    refer_sco = vo.getSettleMentScore().multiply(hospital.getHospCof().subtract(DEFAULT_COEFFICIENT_1));
                    return refer_sco;
                } else {
                    vo.setHospCof(DEFAULT_COEFFICIENT_1);
                }
            }
        } else {
            vo.setHospCof(DEFAULT_COEFFICIENT_1);
        }
        return refer_sco;
    }

    /**
     * 辅助目录
     * @param vo
     * @param params
     */
    private static void addAuxValue(DipPayToPredictVo vo, Map<String, Object> params) {

        List<DipDiseaseTypeVo> list = (List<DipDiseaseTypeVo>) params.get(GenerateScoreUtil.KEY_AUX_DISEASE);
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        // 创建Map
        Map<String, DipDiseaseTypeVo> auxMap = list.stream()
                .collect(Collectors.toMap(
                        v -> v.getDipCodg() + "-" + v.getAuxType(),
                        v -> v,
                        (existing, replacement) -> existing // 如果有重复键，保留原值
                ));

        String dipCodg = vo.getDipCodg();
        // 获取辅助目录类型并过滤非空元素,后续可能有cci
        List<String> auxs = Arrays.asList(vo.getAsstListDiseSevDeg(), vo.getAsstListTmorSevDeg(), vo.getAsstListAgeGrp(),vo.getAuxiliaryBurn())
                .stream()
                .filter(Objects::nonNull)  // 保留非空元素
                .filter(s -> !s.isEmpty()) // 保留非空字符串
                .collect(Collectors.toList());

        // 如果过滤后的列表为空，直接返回
        if (auxs.isEmpty()) {
            return;
        }

        // 获取第一个非空辅助值
        BigDecimal currAuxAdm = new BigDecimal(BigInteger.ZERO);
        for (String currAuxType : auxs) {
            // 根据生成的键值对从Map中获取值,是否判断获取最大的辅助目录系数
            DipDiseaseTypeVo dipDiseaseTypeVo = auxMap.get(dipCodg + "-" + currAuxType);
            // 如果找到对应的DipDiseaseTypeVo，进行结算得分的计算
            if (dipDiseaseTypeVo != null) {
                BigDecimal auxXs = dipDiseaseTypeVo.getAuxXs();
                if (auxXs != null && auxXs.compareTo(currAuxAdm) > 0) {
                    currAuxAdm = auxXs;
                }
            }
        }

        if (currAuxAdm.compareTo(BigDecimal.ZERO) > 0 && vo.getSettleMentScore() != null) {
            //辅助目录调节系数大于0
            vo.setSettleMentScore(vo.getSettleMentScore().multiply(currAuxAdm));
        }

    }
}
