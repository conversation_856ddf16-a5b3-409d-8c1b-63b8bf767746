package com.my.som.grouppay.compmodule.dipmodule.common.calc;

import com.my.som.common.constant.DrgConst;
import com.my.som.common.util.ValidateUtil;
import com.my.som.grouppay.vo.HospBaseBusiVo;
import com.my.som.util.SysCommonConfigUtil;
import com.my.som.vo.common.DipConfigVo;
import com.my.som.vo.dataHandle.PayToPredict.PayToPredictVo;
import com.my.som.vo.dipBusiness.DipPayToPredictVo;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import com.yomahub.liteflow.core.NodeComponent;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;


@LiteflowComponent(value = "DipCalculatePreGroupPayForecastNode", name = "支付计算-预分组")
public class DipCalculatePreGroupPayForecastNode extends NodeComponent {
    @Override
    public void process() throws Exception {
        // 获取业务上下文
        HospBaseBusiVo hospBaseBusiVo = this.getContextBean(HospBaseBusiVo.class);
        Map<String, DipConfigVo> dipConfigVoMap = hospBaseBusiVo.getDipConfigVoMap();
        List<DipPayToPredictVo> payToPredictVoList = hospBaseBusiVo.getDipPayToPredictVoList();
        // 设置默认价格
        BigDecimal defaultPrice = new BigDecimal(dipConfigVoMap.get(DrgConst.DEFAULT_PRICE_KEY).getValue());
        for (DipPayToPredictVo payToPredictVo : payToPredictVoList) {
            // 根据参保类型和年月获取价格配置
            String priceKey = getPriceKey(payToPredictVo.getInsuType(), payToPredictVo.getYm(), payToPredictVo.isRunMonthPrice());
            DipConfigVo dipConfigVo = dipConfigVoMap.get(priceKey);
            // 如果价格存在，使用配置的价格，否则使用默认价格
            BigDecimal forecastPrice = (dipConfigVo != null) ? new BigDecimal(dipConfigVo.getValue()) : defaultPrice;
            // 计算预计费用
            BigDecimal forecastFee = payToPredictVo.getTotlSco().multiply(forecastPrice).setScale(2, BigDecimal.ROUND_HALF_UP);
            // 预测单价
            payToPredictVo.setScorePrice(forecastPrice);
            // 预测费用
            payToPredictVo.setForecast_fee(forecastFee);
            if (ValidateUtil.isEmpty(payToPredictVo.getSumfee())) {
                payToPredictVo.setSumfee(
                        BigDecimal.ZERO
                                .add(ValidateUtil.isEmpty(payToPredictVo.getInHosTotalCost()) ? BigDecimal.ZERO : payToPredictVo.getInHosTotalCost())
                                .add(ValidateUtil.isEmpty(payToPredictVo.getPreHospExamfee()) ? BigDecimal.ZERO : payToPredictVo.getPreHospExamfee()));
            }
            // 预测盈亏
            payToPredictVo.setProfitloss(forecastFee
                    .subtract(ValidateUtil.isEmpty(payToPredictVo.getSumfee()) ? BigDecimal.ZERO : payToPredictVo.getSumfee()));


            String ALGORITHMA_TYPE = (String) SysCommonConfigUtil.get(DrgConst.ALGORITHMA_TYPE);
            if (ALGORITHMA_TYPE != null && !ValidateUtil.isEmpty(ALGORITHMA_TYPE)) {
                if("1".equals(ALGORITHMA_TYPE)) {
                    BigDecimal fundAmtSum = payToPredictVo.getFundAmtSum();
                    if (!ValidateUtil.isEmpty(fundAmtSum) && BigDecimal.ZERO.compareTo(fundAmtSum) != 0) {
                        BigDecimal ZJforecastFee = forecastFee.multiply(payToPredictVo.getReferenceFundRatio());
                        if(!ValidateUtil.isEmpty(ZJforecastFee) && BigDecimal.ZERO.compareTo(ZJforecastFee) != 0){
                            payToPredictVo.setOriginal_forecast_fee(forecastFee);
                            payToPredictVo.setForecast_fee(ZJforecastFee);
                            payToPredictVo.setOriginalProfitloss(payToPredictVo.getProfitloss());
                            payToPredictVo.setProfitloss(ZJforecastFee.subtract(payToPredictVo.getFundAmtSum()));
                            BigDecimal fundRatio = payToPredictVo.getFundAmtSum().divide(payToPredictVo.getForecast_fee(), 4, BigDecimal.ROUND_DOWN).multiply(new BigDecimal(100)).setScale(2, BigDecimal.ROUND_DOWN);
                            payToPredictVo.setFundRatio(fundRatio + "%");
                        }else {
                            payToPredictVo.setForecast_fee(BigDecimal.ZERO);
                            payToPredictVo.setProfitloss(BigDecimal.ZERO);
                            payToPredictVo.setFundRatio(BigDecimal.ZERO + "%");
                        }

                    }else{
                        morensuanfa(payToPredictVo);
                    }
                }else {
                    morensuanfa(payToPredictVo);
                }
            } else {
                morensuanfa(payToPredictVo);
            }
        }
    }

    private static void morensuanfa(DipPayToPredictVo payToPredictVo) {
        if (!ValidateUtil.isEmpty(payToPredictVo.getFundSourceType())) {
            // 计算预测报账部分
            payToPredictVo.setPreFundFee(payToPredictVo.getForecast_fee().subtract(payToPredictVo.getPresonBearCost()));
            //基金比例
            BigDecimal fundRatio = payToPredictVo.getFundAmtSum().divide(payToPredictVo.getPreFundFee(), 4, BigDecimal.ROUND_DOWN).multiply(new BigDecimal(100)).setScale(2, BigDecimal.ROUND_DOWN);
            payToPredictVo.setFundRatio(fundRatio + "%");
        }
    }

    /**
     * 结合期号进行判断
     * 根据参保类型获取单价配置key
     *
     * @param insuType
     * @param ym
     * @return
     */
    String getPriceKey(String insuType, String ym, boolean runMonthPrice) {
        String priceKey = DrgConst.PRICE_KEY_DEFAULT;
        if (DrgConst.INSURANCES_TYPE_1.contains(insuType)) {
            priceKey = DrgConst.PRICE_KEY_CZ;
        } else if (DrgConst.INSURANCES_TYPE_2.contains(insuType)) {
            priceKey = DrgConst.PRICE_KEY_CX;
        }
        if (runMonthPrice && !ValidateUtil.isEmpty(ym)) {
            priceKey = ym + "-" + priceKey;
        }
        return priceKey;
    }
}
