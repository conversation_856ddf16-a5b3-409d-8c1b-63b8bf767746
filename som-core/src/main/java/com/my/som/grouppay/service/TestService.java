package com.my.som.grouppay.service;

import com.my.som.grouppay.bo.GroupDiseasesBO;
import com.my.som.grouppay.bo.PayBusinessBO;
import com.my.som.grouppay.vo.DirectoryLibraryVO;
import com.my.som.grouppay.vo.GroupDiseasesVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class TestService {
//    @Autowired
//    private ShiyanImpl2023DIP shiyanImpl2023DIP;
//
//
//    /**
//     *
//     * @param vo 病种相关信息
//     * @param bo 其他业务信息
//     */
//    public void test(DirectoryLibraryVO vo, GroupDiseasesBO bo) {
//        GroupDiseasesVO groupDiseasesVO = new GroupDiseasesVO();
//
//        //1.获取计算上下文
//        PayBusinessBO payBusinessBO = shiyanImpl2023DIP.diseaseBenchmarkBusinessClass(vo, bo);
//        //2.执行计算
//        shiyanImpl2023DIP.ruleOrchestrationImpl(vo, bo, payBusinessBO);
//        //3.地市赋值
//        shiyanImpl2023DIP.dataProduction(payBusinessBO, groupDiseasesVO);
//        //4. 分值计算完成，入库
//
//    }
}
