package com.my.som.grouppay.compmodule.drgmodule.common.calc;

import com.my.som.common.constant.DrgConst;
import com.my.som.common.util.ValidateUtil;
import com.my.som.grouppay.vo.HospBaseBusiVo;
import com.my.som.vo.dipBusiness.DipPayToPredictVo;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import com.yomahub.liteflow.core.NodeComponent;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.util.List;

@LiteflowComponent(value = "DrgCalculateBusiProcPayForecastNode", name = "计算支付预测金额-业务流程")
public class DrgCalculateBusiProcPayForecastNode extends NodeComponent {
    private Logger logger = LoggerFactory.getLogger(DrgCalculateBusiProcPayForecastNode.class);

    @Override
    public void process() throws Exception {
        //1、获取业务上下文
        HospBaseBusiVo hospBaseBusiVo = this.getContextBean(HospBaseBusiVo.class);
        List<DipPayToPredictVo> dipPayToPredictVoList = hospBaseBusiVo.getDipPayToPredictVoList();
        BigDecimal forecastFee;
        for (DipPayToPredictVo vo : dipPayToPredictVoList) {
            // 计算预测总费用及付费差异
            if (!ValidateUtil.isEmpty(vo.getTotlSco()) && !ValidateUtil.isEmpty(vo.getPrice())) {
                //单价及总分值均不为空
                vo.setForecast_fee(vo.getTotlSco().multiply(vo.getPrice()));
            } else {
                vo.setForecast_fee(new BigDecimal(0));
            }
            forecastFee = vo.getForecast_fee();
            vo.setJzfz(vo.getRefer_sco());
            vo.setJbjbxs(vo.getAdjm_cof());
            vo.setYcfzFirst(vo.getCalculateScore());
            vo.setJcfz(vo.getAddScore());
            vo.setZfz(vo.getTotlSco());
            vo.setFzdj(vo.getPrice());
            if (DrgConst.PAYMENT_TYPE_PROJECT.equals(vo.getPayMentType())) {
                //是否执行按项目付费
                vo.setYcfy(vo.getSumfee());
                vo.setForecast_fee(vo.getSumfee());
            } else if (DrgConst.PAYMENT_TYPE_BEDDAY.equals(vo.getPayMentType())) {
                //床日暂无
                vo.setYcfy(forecastFee);
            } else if (DrgConst.PAYMENT_TYPE_DRG.equals(vo.getPayMentType())) {
                vo.setYcfy(forecastFee);
            }else {
                logger.error("未定义的支付结算类型：" + vo.getPayMentType());
            }
            vo.setProfitloss(vo.getForecast_fee().subtract(vo.getSumfee()));
            vo.setBalx(DrgConst.CASE_TYPE_MAP.get(vo.getDiseType()));
            vo.setPayMentType(DrgConst.PAYMENT_TYPE_MAP.get(vo.getPayMentType()));
            vo.setFycy(vo.getForecast_fee().subtract(vo.getSumfee()).setScale(2, BigDecimal.ROUND_HALF_UP));
        }
    }

    /**
     * 结合期号进行判断
     * 根据参保类型获取单价配置key
     *
     * @param insuType
     * @param ym
     * @return
     */
    String getPriceKey(String insuType, String ym) {
        String priceKey = DrgConst.PRICE_KEY_DEFAULT;
        if (DrgConst.INSURANCES_TYPE_1.contains(insuType)) {
            priceKey = DrgConst.PRICE_KEY_CZ;
        } else if (DrgConst.INSURANCES_TYPE_2.contains(insuType)) {
            priceKey = DrgConst.PRICE_KEY_CX;
        }
        if (!ValidateUtil.isEmpty(ym)) {
            priceKey = ym + "-" + priceKey;
        }
        return priceKey;
    }
}
