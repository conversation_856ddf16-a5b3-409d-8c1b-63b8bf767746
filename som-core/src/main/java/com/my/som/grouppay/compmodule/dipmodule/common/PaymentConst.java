package com.my.som.grouppay.compmodule.dipmodule.common;

import java.math.BigDecimal;

public interface PaymentConst {

    /**
     * 默认系数
     * DEFAULT_SCORE_0 : 默认分值为0
     * DEFAULT_COEFFICIENT_1 : 默认系数为1
     * DEFAULT_COST_0 : 默认花费为0
     */
    BigDecimal DEFAULT_SCORE_0 = BigDecimal.ZERO;
    BigDecimal DEFAULT_COEFFICIENT_1 = BigDecimal.ONE;
    BigDecimal DEFAULT_COEFFICIENT_0 = BigDecimal.ZERO;
    BigDecimal DEFAULT_COST_0 = BigDecimal.ZERO;

    /**
     * 比例参数
     * PROPORTION_BASE : 基础比值 100
     * CHINESE_DISEASE_PROPORTION : 中医费用占比
     * YOUNGER_AGE : 低龄年龄
     * OLDER_AGE：高龄年龄
     * BASE_HOSPITAL_LEVEL : 基层病种在医院等级中的划分    三甲:1 二甲:2 三乙:3 二乙:4 一级:5 未定级:6 基层:7
     * HOSPITAL_COSFFICIENT : 基础医院系数(管理员)
     * EXTRE_COEFFICIENT : 极高(极长)系数
     * OLDER_AGE_COEFFICIENT : 极高(极长)系数
     */
    BigDecimal PROPORTION_BASE = new BigDecimal("100");
    BigDecimal CHINESE_DISEASE_PROPORTION = new BigDecimal("50");
    BigDecimal YOUNGER_AGE = new BigDecimal("14");
    BigDecimal OLDER_AGE = new BigDecimal("60");
    BigDecimal BASE_HOSPITAL_LEVEL = new BigDecimal("4");
    BigDecimal HOSPITAL_COSFFICIENT = new BigDecimal("1.005");
    BigDecimal EXTRE_COEFFICIENT = new BigDecimal("5");
    BigDecimal OLDER_AGE_COEFFICIENT = new BigDecimal("5");
    //成都项目折算分值比例
    BigDecimal CONVER_RATIO_5101 = new BigDecimal("0.9");

    //成都低倍率临界值
    BigDecimal DRG_LOW_RATIO_5101 = new BigDecimal("0.7");
    //高倍率临界值
    BigDecimal DRG_HEIGHT_RATIO_5101 = new BigDecimal("1.4");


    /**
     * 参数 : diseType
     * CHINESE_DISEASE : 中医优势病种
     * BASE_DISEASE : 基层病种
     * YOUNGER_DISEASE : 低龄病种
     * PROFESSIONAL_DISEASE : 重点专科病种
     * OLDER_DISEASE : 高龄病种
     */
    String CHINESE_DISEASE = "1";
    String BASE_DISEASE = "2";
    String YOUNGER_DISEASE = "3";
    String PROFESSIONAL_DISEASE = "4";
    String OLDER_DISEASE = "5";


    /**
     * KEY_CHINESE_DISEASE : 中医优势病种
     * KEY_BASE_DISEASE : 基层病种
     * KEY_FOCUS_DISEASE : 重点专科病种
     * KEY_DISEASE_COEFFICIENT : 病种系数
     * HOSP_COF : 医院系数
     * KEY_AUX_DISEASE: 辅助目录病种
     */
    String KEY_CHINESE_DISEASE = "chinsesDisease";
    String KEY_BASE_DISEASE = "baseDisease";
    String KEY_FOCUS_DISEASE = "focusDisease";
    String KEY_DISEASE_COEFFICIENT = "diseCof";
    String HOSPITAL_INFO = "hospCof";
    String KEY_AUX_DISEASE = "auxDisease";

    /**
     * 病种状态
     * DISEASE_STATUS_1 : 病种状态启用
     * UNSTABLE_FLAG : 非稳定病组标志
     */
    String DISEASE_STATUS_1 = "1";

    /**
     * 0非稳定、1稳定
     */
    String IS_SD_DISE = "0";

    /**
     * 病例类型
     * CASE_TYPE_0 : 未入组
     * CASE_TYPE_1 : 超高病例
     * CASE_TYPE_2 : 超低病例
     * CASE_TYPE_3 : 正常病例
     * CASE_TYPE_4 : 无标杆病例
     * CASE_TYPE_5 : 非稳定病例
     * CASE_TYPE_6 : 极高费用(住院天数)病例
     * CASE_TYPE_7 : 基层病种算法
     */
    String CASE_TYPE_0 = "0";
    String CASE_TYPE_1 = "1";
    String CASE_TYPE_2 = "2";
    String CASE_TYPE_3 = "3";
    String CASE_TYPE_4 = "4";
    String CASE_TYPE_5 = "5";
    String CASE_TYPE_6 = "6";
    String CASE_TYPE_7 = "7";
    String CASE_TYPE_QY_KEY = "QY";

}

