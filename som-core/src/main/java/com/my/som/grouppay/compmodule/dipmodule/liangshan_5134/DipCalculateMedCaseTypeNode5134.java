package com.my.som.grouppay.compmodule.dipmodule.liangshan_5134;


import com.my.som.common.constant.DrgConst;
import com.my.som.common.util.ValidateUtil;
import com.my.som.grouppay.vo.HospBaseBusiVo;
import com.my.som.util.GenerateScoreUtil;
import com.my.som.vo.dataConfig.DipDiseaseTypeVo;
import com.my.som.vo.dataHandle.PayToPredict.PayToPredictVo;
import com.my.som.vo.dipBusiness.DipPayToPredictVo;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import com.yomahub.liteflow.core.NodeComponent;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

import static com.my.som.grouppay.compmodule.dipmodule.common.PaymentConst.DEFAULT_COEFFICIENT_1;

@LiteflowComponent(value = "DipCalculateMedCaseTypeNode5134", name = "病例类型判断")
public class DipCalculateMedCaseTypeNode5134 extends NodeComponent {
    @Override
    public void process() throws Exception {

        // 获取业务上下文
        HospBaseBusiVo hospBaseBusiVo = this.getContextBean(HospBaseBusiVo.class);
        List<DipPayToPredictVo> dipPayToPredictVoList = hospBaseBusiVo.getDipPayToPredictVoList();
        Map params = this.getContextBean(Map.class);
        List<DipDiseaseTypeVo> baseDisease = (List<DipDiseaseTypeVo>) params.get(GenerateScoreUtil.KEY_BASE_DISEASE);

        for (DipPayToPredictVo payToPredictVo : dipPayToPredictVoList) {
            // 病种均费
            BigDecimal standardCost = payToPredictVo.getAreaStandardCost();

            //判断是否为医保病人
            if( ValidateUtil.isEmpty(payToPredictVo.getBusinessSerial() ) ) {
                payToPredictVo.setSumfee(payToPredictVo.getInHosTotalCost());
            }else{
                if(ValidateUtil.isEmpty(payToPredictVo.getCheckTotalFee()) || BigDecimal.ZERO.compareTo(payToPredictVo.getCheckTotalFee())== 0){
                    payToPredictVo.setSumfee(payToPredictVo.getInHosTotalCost());
                }else{
                    payToPredictVo.setSumfee(payToPredictVo.getCheckTotalFee() );}
            }
            // 患者住院总费用
           BigDecimal totalHospitalizationCost = payToPredictVo.getSumfee();
            if (!ValidateUtil.isEmpty(baseDisease) ) {
                for (DipDiseaseTypeVo disease :baseDisease) {
                    if (disease.getDipCodg().equals(payToPredictVo.getDipCodg())) {
                        payToPredictVo.setIs_base_dise(DrgConst.DIS_BASE_1);
                    }
                }
            }

            // 中治率计算
            processTcm(payToPredictVo);
            if (ValidateUtil.isEmpty(payToPredictVo.getDipCodg())) {
                // 未入组
                payToPredictVo.setPaymentDiseType(DrgConst.DISETYPE_NONE_INGROUP);
                payToPredictVo.setDiseType(DrgConst.DISE_TYPE_0);
                continue;
            } else if (DrgConst.DIS_BASE_1.equals(payToPredictVo.getIs_base_dise())) {
                // 基层病种
                payToPredictVo.setPaymentDiseType(DrgConst.DISETYPE_BASIC);
                payToPredictVo.setDiseType(DrgConst.DISE_TYPE_7);
                continue;
            } else if (DrgConst.DIS_CHM_1.equals(payToPredictVo.getIs_tcm_dise())) {

                if (payToPredictVo.getTCMRatio() != null && payToPredictVo.getTCMRatio().compareTo(new BigDecimal(0.3)) > 0) {
                    // 中医病种
                    payToPredictVo.setPaymentDiseType(DrgConst.DISETYPE_CHM);
                } else {
                    // 核心病种
                    payToPredictVo.setPaymentDiseType(DrgConst.DISETYPE_CORE);
                }
            } else {
                if (ValidateUtil.isNotEmpty(payToPredictVo.getDisType())) {
                    // 核心 / 综合病种
                    payToPredictVo.setPaymentDiseType(payToPredictVo.getDisType());
                } else {
                    //设置默认核心病种
                    payToPredictVo.setPaymentDiseType(DrgConst.DISETYPE_CORE);
                }
            }


            //入组且有标杆
            if (!ValidateUtil.isEmpty(payToPredictVo.getDipCodg()) && !ValidateUtil.isEmpty(standardCost)) {
                if (standardCost.compareTo(BigDecimal.ZERO) > 0) {
                    //费用倍率 不进行四舍五入
                    BigDecimal rate = totalHospitalizationCost.divide(standardCost, 4, BigDecimal.ROUND_DOWN);
                    // 费用判断
                    // 低倍率低于50%
                    if (rate.compareTo(payToPredictVo.getLowlmtMag()) < 0) {
                        // 低倍率
                        payToPredictVo.setDiseType(DrgConst.CASE_TYPE_2);
                        payToPredictVo.setPayMentType(DrgConst.PAYMENT_TYPE_DIP);
                        // 倍率调节系数 = 病种级别均费 / 患者住院总费用
                        payToPredictVo.setBiasRateFactor(rate);
                    } else if (rate.compareTo(new BigDecimal("1.5")) > 0 &&  new BigDecimal("2000").compareTo(payToPredictVo.getRefer_sco()) < 0)  {
                        // 高倍率
                        //当基础分值大于2000 且倍率调节系数超过1.5倍
                        payToPredictVo.setDiseType(DrgConst.CASE_TYPE_1);
                        payToPredictVo.setPayMentType(DrgConst.PAYMENT_TYPE_DIP);
                        // 倍率调节系数 = 病种级别均费 / 患者住院总费用 - 2 + 1
                        payToPredictVo.setBiasRateFactor(rate.subtract(new BigDecimal(0.5)));
                    } else if (rate.compareTo(new BigDecimal("2")) > 0 &&  new BigDecimal("2000").compareTo(payToPredictVo.getRefer_sco()) > 0)  {
                        // 高倍率
                        //当基础分值小于于2000 且倍率调节系数超过2倍
                        payToPredictVo.setDiseType(DrgConst.CASE_TYPE_1);
                        payToPredictVo.setPayMentType(DrgConst.PAYMENT_TYPE_DIP);
                        // 倍率调节系数 = 病种级别均费 / 患者住院总费用 - 2 + 1
                        payToPredictVo.setBiasRateFactor(rate.subtract(new BigDecimal(1)));
                    }else {
                        // 正常倍率
                        payToPredictVo.setDiseType(DrgConst.CASE_TYPE_3);
                        payToPredictVo.setPayMentType(DrgConst.PAYMENT_TYPE_DIP);
                        payToPredictVo.setBiasRateFactor(BigDecimal.ONE);
                    }
                } else {
                    payToPredictVo.setPaymentDiseType(DrgConst.DISETYPE_NO_BENCHMARK);
                    payToPredictVo.setDiseType(DrgConst.CASE_TYPE_4);
                    payToPredictVo.setPayMentType(DrgConst.PAYMENT_TYPE_DIP);
                    payToPredictVo.setBiasRateFactor(BigDecimal.ONE);
                }
            }

            //设置是否启动月度单价
            payToPredictVo.setRunMonthPrice(false);
        }
    }

    /**
     * 中治率计算
     * （中草药费 + 中医治疗费）/ 住院总费用
     */
    private void processTcm(DipPayToPredictVo payToPredictVo) {
        BigDecimal tcmTreatmentCost = payToPredictVo.getTCMTreatmentCost();
        BigDecimal ra = tcmTreatmentCost.divide(payToPredictVo.getInHosTotalCost(), BigDecimal.ROUND_HALF_UP).setScale(2, BigDecimal.ROUND_HALF_UP);
        payToPredictVo.setTCMRatio(ra);
    }
}
