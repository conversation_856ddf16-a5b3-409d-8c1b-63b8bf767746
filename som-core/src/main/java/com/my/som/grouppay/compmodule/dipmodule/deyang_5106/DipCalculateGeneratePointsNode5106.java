package com.my.som.grouppay.compmodule.dipmodule.deyang_5106;

import com.my.som.common.constant.DrgConst;
import com.my.som.common.util.ValidateUtil;
import com.my.som.grouppay.compmodule.dipmodule.common.PaymentConst;
import com.my.som.grouppay.vo.HospBaseBusiVo;
import com.my.som.vo.dataConfig.DipDiseaseTypeVo;
import com.my.som.vo.dataHandle.PayToPredict.PayToPredictVo;
import com.my.som.vo.dipBusiness.DipPayToPredictVo;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import com.yomahub.liteflow.core.NodeComponent;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

import static com.my.som.grouppay.compmodule.dipmodule.common.PaymentConst.*;

@LiteflowComponent(value = "DipCalculateGeneratePointsNode5106", name = "分值计算")
public class DipCalculateGeneratePointsNode5106 extends NodeComponent {
    @Override
    public void process() throws Exception {
        // 获取业务上下文
        HospBaseBusiVo hospBaseBusiVo = this.getContextBean(HospBaseBusiVo.class);
        List<DipPayToPredictVo> payToPredictVoList = hospBaseBusiVo.getDipPayToPredictVoList();
        BigDecimal regiAverageFee = hospBaseBusiVo .getRegiAverageFee();
        for (DipPayToPredictVo payToPredictVo : payToPredictVoList) {
            String paymentDiseType = payToPredictVo.getPaymentDiseType();
            BigDecimal score = BigDecimal.ZERO;
            BigDecimal referSco = payToPredictVo.getRefer_sco();
            BigDecimal adjmCof = payToPredictVo.getAdjm_cof();
            BigDecimal biasRateFactor = payToPredictVo.getBiasRateFactor();
            BigDecimal asstListAdm = ValidateUtil.isEmpty(payToPredictVo.getAsstListAdm())?BigDecimal.ONE:payToPredictVo.getAsstListAdm();
            // 区域
            BigDecimal areaStandardCost = payToPredictVo.getAreaStandardCost()== new BigDecimal(0)?new BigDecimal(1):payToPredictVo.getAreaStandardCost();
            // 上一年同级别
            BigDecimal lastYearLevelStandardCost = payToPredictVo.getLastYearLevelStandardCost()== new BigDecimal(0)?new BigDecimal(1):payToPredictVo.getLastYearLevelStandardCost();

                /*
           1.不稳定病组：该病例医疗费用/全部费用均值*100
           2.辅助分型病例：
                      辅助分型调整结算分数=该病种辅助分型后费用均值*基本系数*辅助分型前费用均值
                      辅助分型调整系数=该病种辅助分型后费用均值/该病种辅助分型前费用均值
           3.费用偏差病例
                      低倍率结算分值：该病例医疗费用/该病例所在病种费用均值*该病种基准分值
                      高倍率结算分值：(该病例医疗费用/上年度同级别定点医疗机构该病种次均医疗费用-判定倍率+1)*该病种基准分值*基本系数*辅助分型调整系数
                                      判定倍率 基准分值 > 200 ? 1.8:2
           4.特殊病例
              1.极高费用(极长住院)病例
                      结算分值：该病例医疗费用/该病例所在病种均值*该病种基准分值
              2.采用已备案新医疗技术的病例

              3.未入组
          */

            switch (paymentDiseType) {
                case DrgConst.DISETYPE_CORE: // 核心病种
                    // 计算核心病种的总分值
                    score = calculateScore(referSco, adjmCof, biasRateFactor, asstListAdm);
                    break;

                case DrgConst.DISETYPE_COMPOSITE: // 综合病种
                    // 计算综合病种的总分值
                    score = calculateScore(referSco, adjmCof, biasRateFactor);
                    break;

//                case DrgConst.DISETYPE_BASIC: // 基层病种
//                    // 计算基层病种的总分值
//                    score =  payToPredictVo.getRefer_sco().multiply(new BigDecimal(0.9));
//                    break;

                case DrgConst.DISETYPE_CHM: // 中医病种
                    // 计算中医病种的总分值
                    BigDecimal tcmAdtCof = new BigDecimal(1.05);
                    payToPredictVo.setTcmAdtCof(tcmAdtCof);
                    score = calculateScore(referSco, tcmAdtCof);
                    break;

//                case DrgConst.DISETYPE_NONE_INGROUP: // 未入组  分值由专家评估后
//                    // 计算未入组病种的总分值
//                    score = payToPredictVo.getInHosTotalCost()
//                            .divide(payToPredictVo.getDiseaseAverageCost(), BigDecimal.ROUND_HALF_UP)
//                            .multiply(new BigDecimal(0.5))
//                            .multiply(new BigDecimal(1000))
//                            .setScale(4, BigDecimal.ROUND_HALF_UP);
//                    break;
                case DrgConst.DISETYPE_INSTABILITY: //不稳定病组：该病例医疗费用/全部费用均值*100
                    score = payToPredictVo.getSumfee()
                            .divide(regiAverageFee, 8,BigDecimal.ROUND_HALF_UP)
                            .multiply(new BigDecimal(100.0))
                            .setScale(4, BigDecimal.ROUND_HALF_UP);
                    break;
//                case DrgConst.DISETYPE_AUXILIARY_TYPE: // 辅助分型病例:该病种辅助分型后费用均值*基本系数*辅助分型前费用均值
//
//                    break;
                case DrgConst.DISETYPE_EXTREMELYE_HIGH: // 极高费用(极长住院)病例 该病例医疗费用/该病例所在病种均值*该病种基准分值
                    score =  payToPredictVo.getSumfee()
                            .divide(areaStandardCost, 8, BigDecimal.ROUND_HALF_UP)
                            .multiply(adjmCof)
                            .multiply(asstListAdm)
                            .multiply(payToPredictVo.getRefer_sco());
                    break;
                case DrgConst.DISETYPE_NORMAL :
                    score = payToPredictVo.getRefer_sco().multiply(payToPredictVo.getAdjm_cof())
                            .multiply(asstListAdm);
                    break;

                case DrgConst.DISETYPE_HIGH_COMPUTE:
                    score =  payToPredictVo.getSumfee()
                            .divide(lastYearLevelStandardCost,8,BigDecimal.ROUND_HALF_UP)
                            .subtract(biasRateFactor)
                            .add(BigDecimal.ONE)
                            .multiply(referSco)
                            .multiply(adjmCof)
                            .multiply(asstListAdm);
                    break;
                case DrgConst.DISETYPE_LOW_COMPUTE:
                    score =payToPredictVo.getSumfee()
                            .divide(areaStandardCost, 8, BigDecimal.ROUND_HALF_UP)
                            .multiply(referSco)
                            .multiply(asstListAdm);

                    break;
                default:
                    break;
            }

            // 设置计算后的分值和其他属性
            payToPredictVo.setTotlSco(score);
            payToPredictVo.setSettleMentScore(score);
            payToPredictVo.setAddItiveValue(BigDecimal.ZERO);
            setOtherCoefficient(payToPredictVo);
        }
    }

    /**
     * 没有加成情况下，设置其他系数
     * @param vo
     */
    private void setOtherCoefficient(DipPayToPredictVo vo) {
        vo.setTcmAdtCof(PaymentConst.DEFAULT_COEFFICIENT_0);
        vo.setGrstCof(PaymentConst.DEFAULT_COEFFICIENT_0);
        //德阳开启低龄系数
        vo.setYoungCof(PaymentConst.DEFAULT_COEFFICIENT_0);
        vo.setOlderCof(null);
        vo.setProfessionalRate(PaymentConst.DEFAULT_COEFFICIENT_0);
        //设置医疗机构系数
        vo.setHospCof(PaymentConst.DEFAULT_COEFFICIENT_0);
        vo.setAddItiveValue(PaymentConst.DEFAULT_SCORE_0);
    }

    /**
     * 计算病种分值
     *
     * @param referSco  基础分值
     * @param cofactors 可变参数系数列表
     * @return 计算后的分值
     */
    private BigDecimal calculateScore(BigDecimal referSco, BigDecimal... cofactors) {
        BigDecimal score = referSco;
        for (BigDecimal factor : cofactors) {
            if (factor != null) {
                //score = score * factor
                score = score.multiply(factor);
            }
        }
        return score.setScale(4, BigDecimal.ROUND_HALF_UP);
    }

}
