package com.my.som.grouppay.compmodule.drgmodule.neijiang_5110;


import com.my.som.common.constant.DrgConst;
import com.my.som.common.util.ValidateUtil;
import com.my.som.grouppay.vo.HospBaseBusiVo;
import com.my.som.vo.dipBusiness.DipPayToPredictVo;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import com.yomahub.liteflow.core.NodeComponent;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 内江政策
 *
 * 超过某医疗机构当期病例数 5%的高倍率病例支付点数=该
 * 病组基准点数×该医疗机构该病组调整系数。
 * 不超过某医疗机构当期病例数 5%的高倍率病例支付点数=该
 * 病组基准点数×（该病例实际发生住院医疗费用÷该病组次均住
 * 院医疗费用）
 */
@LiteflowComponent(value = "DrgPercentagePolicyAdjustmentsNode5110", name = "政策调整")
public class DrgPercentagePolicyAdjustmentsNode5110 extends NodeComponent {
    @Override
    public void process() throws Exception {
        //根据入参计算是床日病种还是其它病种，床日病种需要从缓存中获取床日病种配置信息
        // 1.获取业务参数
        HospBaseBusiVo hospBaseBusiVo = this.getContextBean(HospBaseBusiVo.class);
        List<DipPayToPredictVo> scoreList = hospBaseBusiVo.getDipPayToPredictVoList();

        //先取出高倍率中的数据
        List<DipPayToPredictVo> highMagnification = scoreList.stream()
                .filter(vo -> DrgConst.CASE_TYPE_1.equals(vo.getScoDiseType()))
                .collect(Collectors.toList())
                ;
        //取出之前被政策调整过的数据
        List<DipPayToPredictVo> updatedList = highMagnification.stream()
                .filter(vo -> vo.getScoRemark() != null && !vo.getScoRemark().isEmpty())  // 筛选 remark 不为空
                .peek(vo -> vo.setScoRemark(null))
                .peek(vo -> vo.setScoTotlSco(vo.getRefer_sco().multiply(vo.getScoSumfee().divide((ValidateUtil.isEmpty(vo.getStandardFee()) ||  BigDecimal.ZERO.compareTo(vo.getStandardFee()) == 0)?vo.getLastYearLevelStandardCost():vo.getStandardFee(),8,BigDecimal.ROUND_CEILING ))))
                .peek(vo -> vo.setScoForecastFee(vo.getScoPrice().multiply(vo.getScoTotlSco())))
                .peek(vo->vo.setScoProfitloss(vo.getScoForecastFee().subtract(vo.getScoSumfee())))// 将 remark 字段置空
                .collect(Collectors.toList())
                ;

        //获取到旧数据
        highMagnification.sort(Comparator.comparing(DipPayToPredictVo::getScoProfitloss));

        int numberOfItems = (int) Math.round(highMagnification.size() * 0.05);  // 5% 四舍五入
        List<DipPayToPredictVo> top5Percent = highMagnification.subList(0, numberOfItems);
        LocalDate currentDate = LocalDate.now();
        top5Percent.stream()
                .peek(vo -> vo.setScoRemark(currentDate +"按照政策调整（高倍率亏损前5%的病例）"))
                .peek(vo -> vo.setScoTotlSco(vo.getRefer_sco().multiply(vo.getAdjm_cof())))
                .peek(vo -> vo.setScoForecastFee(vo.getScoPrice().multiply(vo.getScoTotlSco())))
                .peek(vo->vo.setScoProfitloss(vo.getScoForecastFee().subtract(vo.getScoSumfee())))
                .collect(Collectors.toList())
        ;
        updatedList.addAll(top5Percent);
        hospBaseBusiVo.setDipPayToPredictVoList(updatedList);
    }
}
