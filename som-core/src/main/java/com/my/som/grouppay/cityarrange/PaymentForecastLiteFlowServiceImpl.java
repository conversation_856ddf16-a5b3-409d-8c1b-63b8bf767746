package com.my.som.grouppay.cityarrange;

import com.my.som.common.constant.DrgConst;
import com.my.som.common.util.ValidateUtil;
import com.my.som.dto.medicalQuality.BusSettleListMainInfo;
import com.my.som.dto.medicalQuality.SettleListMainInfoQueryParam;
import com.my.som.dto.pregroup.PreGroupDto;
import com.my.som.grouppay.vo.HospBaseBusiVo;
import com.my.som.model.dataHandle.SomDrgStandard;
import com.my.som.util.SysCommonConfigUtil;
import com.my.som.vo.dipBusiness.DipPayToPredictVo;
import com.my.som.vo.pregroup.PreGroupVo;
import com.yomahub.liteflow.core.FlowExecutor;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * 支付预测标准实现
 * 1、获取医疗机构基础配置信息
 * 2、获取支付结算标杆信息
 * 3、生成支付预测分值
 * 4、生成支付预测费用
 */
@Service
public class PaymentForecastLiteFlowServiceImpl implements PaymentForecastLiteFlowService {
    @Resource
    private FlowExecutor flowExecutor;

    /**
     * 1、执行规则配置中的 GainHospBaseInfoChain[THEN(HospCostRateNode、MonthForecastPriceNode,GainDrgPayStandardsInfoChain)]调用链
     * 2、执行规则配置中的 GainDrgPayStandardsInfoChain(THEN(DrgPayStandardsInfoNode,DrgBedPayStandardsInfoNode))调用链
     */
    @Override
    public HospBaseBusiVo getDrgHospBaseAndStandardsCfgInfo(PreGroupDto preGroupDto) {
        // 1.构建基础配置上下文
        HospBaseBusiVo hospBaseBusiVo = new HospBaseBusiVo();
        hospBaseBusiVo.setHospitalId(preGroupDto.getHospital_id());
        hospBaseBusiVo.setInsuplcAdmdvs(preGroupDto.getInsuplc());
        flowExecutor.execute2Resp("DrgGainHospBaseInfoChain", preGroupDto, hospBaseBusiVo);
        return hospBaseBusiVo;
    }

    @Override
    public HospBaseBusiVo getDipHospBaseAndStandardsCfgInfo(PreGroupDto preGroupDto) {
        // 1.构建基础配置上下文
        HospBaseBusiVo hospBaseBusiVo = new HospBaseBusiVo();
        hospBaseBusiVo.setHospitalId(preGroupDto.getHospital_id());
        flowExecutor.execute2Resp("DipGainHospBaseInfoChain", preGroupDto, hospBaseBusiVo);
        return hospBaseBusiVo;
    }

    /**
     * 3、执行规则配置中的 DrgGainHospPreGroupPaymentChain【THEN(DrgPreGroupGenerateScoreChain5107,DrgPreGroupForecastCostChain)】调用链
     * DrgPreGroupGenerateScoreChain5107	【预分组】生成支付分值-绵阳
     * DrgPreGroupGenerateScoreChain5114	【预分组】生成支付分值-眉山
     */
    @Override
    public HospBaseBusiVo drgGenerateScoreAndForecastCostByPreGroup(List<PreGroupVo> preGroupVos, PreGroupDto preGroupDto, HospBaseBusiVo hospBaseBusiVo) {
        //如果为空则通过配置获取区域最小分值、区域病例均费
        if (ValidateUtil.isEmpty(hospBaseBusiVo.getMinPointsSize())) {
            BigDecimal minPointsSize = new BigDecimal((String) SysCommonConfigUtil.get(DrgConst.SYS_AREA_MIN_POINT));
            hospBaseBusiVo.setMinPointsSize(minPointsSize);
        }
        if (ValidateUtil.isEmpty(hospBaseBusiVo.getRegiAverageFee())) {
            BigDecimal regiAverageFee = new BigDecimal((String) SysCommonConfigUtil.get(DrgConst.SYS_AREA_COST));
            hospBaseBusiVo.setRegiAverageFee(regiAverageFee);
        }

        String runChainName = "DrgGainHospPreGroupPaymentChain_" + hospBaseBusiVo.getInsuplcAdmdvs();
        //执行预分组付费预测计算算法
        flowExecutor.execute2Resp(runChainName, preGroupVos, preGroupDto, hospBaseBusiVo);
        return hospBaseBusiVo;
    }

    /**
     * 4、执行规则配置中的 DrgGainHospBusiProcPaymentChain【THEN(DrgBusiProcGenerateScoreChain5107,DrgBusiProcForecastCostChain)】调用链
     * DrgBusiProcGenerateScoreChain5107	【业务流程】生成支付分值-绵阳
     * DrgBusiProcGenerateScoreChain5114	【业务流程】生成支付分值-眉山
     */
    @Override
    public HospBaseBusiVo drgGenerateScoreAndForecastCostByBusiProc(HospBaseBusiVo hospBaseBusiVo) {
        ///执行业务流程付费预测计算算法，如与预分组计算无差异可选择预分组调用链
        //如果为空则通过配置获取区域最小分值、区域病例均费
        if (ValidateUtil.isEmpty(hospBaseBusiVo.getMinPointsSize())) {
            BigDecimal minPointsSize = new BigDecimal((String) SysCommonConfigUtil.get(DrgConst.SYS_AREA_MIN_POINT));
            hospBaseBusiVo.setMinPointsSize(minPointsSize);
        }
        if (ValidateUtil.isEmpty(hospBaseBusiVo.getRegiAverageFee())) {
            BigDecimal regiAverageFee = new BigDecimal((String) SysCommonConfigUtil.get(DrgConst.SYS_AREA_COST));
            hospBaseBusiVo.setRegiAverageFee(regiAverageFee);
        }
        String runChainName = "DrgGainHospBusiProcPaymentChain_" + hospBaseBusiVo.getInsuplcAdmdvs();
        flowExecutor.execute2Resp(runChainName, null, hospBaseBusiVo);
        return hospBaseBusiVo;
    }

    @Override
    public HospBaseBusiVo dipGenerateScoreAndForecastCostByPreGroup(List<PreGroupVo> preGroupVos, PreGroupDto preGroupDto, HospBaseBusiVo hospBaseBusiVo, Map<String, Object> calculateScoreParams) {
        ///执行预分组付费预测计算算法
        //如果为空则通过配置获取区域最小分值、区域病例均费
        if (ValidateUtil.isEmpty(hospBaseBusiVo.getMinPointsSize())) {
            BigDecimal minPointsSize = new BigDecimal((String) SysCommonConfigUtil.get(DrgConst.SYS_AREA_MIN_POINT));
            hospBaseBusiVo.setMinPointsSize(minPointsSize);
        }
        if (ValidateUtil.isEmpty(hospBaseBusiVo.getRegiAverageFee())) {
            BigDecimal regiAverageFee = new BigDecimal(SysCommonConfigUtil.get(DrgConst.SYS_AREA_COST).toString().trim());
            hospBaseBusiVo.setRegiAverageFee(regiAverageFee);
        }
        //执行预分组付费预测计算算法
        flowExecutor.execute2Resp("DipGainHospPreGroupPaymentChain", preGroupVos, preGroupDto, hospBaseBusiVo, calculateScoreParams);
        return hospBaseBusiVo;
    }

    @Override
    public HospBaseBusiVo dipGenerateScoreAndForecastCostByBusiProc(HospBaseBusiVo hospBaseBusiVo, Map<String, Object> calculateScoreParams) {
        ///执行业务流程付费预测计算算法
        //获取通过配置获取区域最小分值、区域病例均费
        if (ValidateUtil.isEmpty(hospBaseBusiVo.getMinPointsSize())) {
            BigDecimal minPointsSize = new BigDecimal((String) SysCommonConfigUtil.get(DrgConst.SYS_AREA_MIN_POINT));
            hospBaseBusiVo.setMinPointsSize(minPointsSize);
        }
        if (ValidateUtil.isEmpty(hospBaseBusiVo.getRegiAverageFee())) {
            BigDecimal regiAverageFee = new BigDecimal((String) SysCommonConfigUtil.get(DrgConst.SYS_AREA_COST));
            hospBaseBusiVo.setRegiAverageFee(regiAverageFee);
        }
        flowExecutor.execute2Resp("DipGainHospBusiProcPaymentChain", null, hospBaseBusiVo, calculateScoreParams);
        return hospBaseBusiVo;
    }

    @Override
    public List<DipPayToPredictVo> PercentagePolicyAdjustments(HospBaseBusiVo hospBaseBusiVo) {
        //获取通过配置获取区域最小分值、区域病例均费
        if (ValidateUtil.isEmpty(hospBaseBusiVo.getMinPointsSize())) {
            BigDecimal minPointsSize = new BigDecimal((String) SysCommonConfigUtil.get(DrgConst.SYS_AREA_MIN_POINT));
            hospBaseBusiVo.setMinPointsSize(minPointsSize);
        }
        if (ValidateUtil.isEmpty(hospBaseBusiVo.getRegiAverageFee())) {
            BigDecimal regiAverageFee = new BigDecimal((String) SysCommonConfigUtil.get(DrgConst.SYS_AREA_COST));
            hospBaseBusiVo.setRegiAverageFee(regiAverageFee);
        }
        flowExecutor.execute2Resp("PercentagePolicyAdjustments", null, hospBaseBusiVo);
        return hospBaseBusiVo.getDipPayToPredictVoList();
    }

    @Override
    public List<BusSettleListMainInfo> querySpecialDisease(List<BusSettleListMainInfo> busSettleListMainInfos, SettleListMainInfoQueryParam queryParam) {
        flowExecutor.execute2Resp("QuerySpecialDisease", busSettleListMainInfos,queryParam );
        return busSettleListMainInfos;
    }

}
