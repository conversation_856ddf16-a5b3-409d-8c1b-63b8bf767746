package com.my.som.util;

import com.my.som.common.util.ValidateUtil;
import com.my.som.vo.dataConfig.DipDiseaseTypeVo;
import com.my.som.vo.dataConfig.ViewHospitalVo;
import com.my.som.vo.dipBusiness.DipPayToPredictVo;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 生成分值2工具类
 * ** 极长住院天数无法判断，没有病种平均天数
 */
public class GenerateScoreUtil {

    /**
     * 病例类型
     * CASE_TYPE_0 : 未入组
     * CASE_TYPE_1 : 超高病例
     * CASE_TYPE_2 : 超低病例
     * CASE_TYPE_3 : 正常病例
     * CASE_TYPE_4 : 无标杆病例
     * CASE_TYPE_5 : 非稳定病例
     * CASE_TYPE_6 : 极高费用(住院天数)病例
     * CASE_TYPE_7 : 基层病种算法
     */
    public final static String CASE_TYPE_0 = "0";
    public final static String CASE_TYPE_1 = "1";
    public final static String CASE_TYPE_2 = "2";
    public final static String CASE_TYPE_3 = "3";
    public final static String CASE_TYPE_4 = "4";
    public final static String CASE_TYPE_5 = "5";
    public final static String CASE_TYPE_6 = "6";
    public final static String CASE_TYPE_7 = "7";

    /**
     * 病种类型
     * DIS_TYPE_1 : 核心病种
     * DIS_TYPE_2 : 综合病种
     * DIS_TYPE_3 : 中医病种
     * DIS_TYPE_4 : 基层病种
     */
    public final static String DIS_TYPE_1 = "1";
    public final static String DIS_TYPE_2 = "2";
    public final static String DIS_TYPE_3 = "3";
    public final static String DIS_TYPE_4 = "4";

    /**
     * 默认系数
     * DEFAULT_SCORE_0 : 默认分值为0
     * DEFAULT_COEFFICIENT_1 : 默认系数为1
     * DEFAULT_COST_0 : 默认花费为0
     */
    private final static BigDecimal DEFAULT_SCORE_0 = BigDecimal.ZERO;
    private final static BigDecimal DEFAULT_COEFFICIENT_1 = BigDecimal.ONE;
    private final static BigDecimal DEFAULT_COST_0 = BigDecimal.ZERO;

    /**
     * KEY_CHINESE_DISEASE : 中医优势病种
     * KEY_BASE_DISEASE : 基层病种
     * KEY_FOCUS_DISEASE : 重点专科病种
     * KEY_DISEASE_COEFFICIENT : 病种系数
     * HOSP_COF : 医院系数
     * KEY_AUX_DISEASE: 辅助目录病种
     */
    public final static String KEY_CHINESE_DISEASE = "chinsesDisease";
    public final static String KEY_BASE_DISEASE = "baseDisease";
    public final static String KEY_FOCUS_DISEASE = "focusDisease";
    public final static String KEY_DISEASE_COEFFICIENT = "diseCof";
    public final static String HOSPITAL_INFO = "hospCof";
    public final static String KEY_AUX_DISEASE = "auxDisease";

    /**
     * 参数 : diseType
     * CHINESE_DISEASE : 中医优势病种
     * BASE_DISEASE : 基层病种
     * YOUNGER_DISEASE : 低龄病种
     * PROFESSIONAL_DISEASE : 重点专科病种
     * OLDER_DISEASE : 高龄病种
     */
    private final static String CHINESE_DISEASE = "1";
    private final static String BASE_DISEASE = "2";
    private final static String YOUNGER_DISEASE = "3";
    private final static String PROFESSIONAL_DISEASE = "4";
    private final static String OLDER_DISEASE = "5";


    /**
     * 病种状态
     * DISEASE_STATUS_1 : 病种状态启用
     * UNSTABLE_FLAG : 非稳定病组标志
     */
    private final static String DISEASE_STATUS_1 = "1";
    private final static String IS_SD_DISE = "0";

    /**
     * 比例参数
     * PROPORTION_BASE : 基础比值 100
     * CHINESE_DISEASE_PROPORTION : 中医费用占比
     * YOUNGER_AGE : 低龄年龄
     * BASE_HOSPITAL_LEVEL : 基层病种在医院等级中的划分    三甲:1 二甲:2 三乙:3 二乙:4 一级:5 未定级:6 基层:7
     * HOSPITAL_COSFFICIENT : 基础医院系数(管理员)
     * EXTRE_COEFFICIENT : 极高(极长)系数
     */
    private final static BigDecimal PROPORTION_BASE = new BigDecimal("100");
    private final static BigDecimal CHINESE_DISEASE_PROPORTION = new BigDecimal("50");
    private final static BigDecimal YOUNGER_AGE = new BigDecimal("14");
    private final static BigDecimal BASE_HOSPITAL_LEVEL = new BigDecimal("4");
    private final static BigDecimal HOSPITAL_COSFFICIENT = new BigDecimal("1.005");
    private final static BigDecimal EXTRE_COEFFICIENT = new BigDecimal("5");

    /**
     * 重点学(专)科等级
     * PROFESSIONAL_LEVEL_1 : 国家级重点学科
     * PROFESSIONAL_LEVEL_2 : 国家级重点专科
     * PROFESSIONAL_LEVEL_3 : 省级重点学科
     * PROFESSIONAL_LEVEL_4 : 省级重点专科
     * PROFESSIONAL_LEVEL_5 : 市级重点学科
     * PROFESSIONAL_LEVEL_5 : 市级重点专科
     * PROFESSIONAL_LEVEL_6 : 市级重点学科
     * PROFESSIONAL_LEVEL_7 : 州级重点学科
     * PROFESSIONAL_LEVEL_8 : 州级重专科
     */
    public final static String PROFESSIONAL_LEVEL_1 = "1";
    public final static String PROFESSIONAL_LEVEL_2 = "2";
    public final static String PROFESSIONAL_LEVEL_3 = "3";
    public final static String PROFESSIONAL_LEVEL_4 = "4";
    public final static String PROFESSIONAL_LEVEL_5 = "5";
    public final static String PROFESSIONAL_LEVEL_6 = "6";
    public final static String PROFESSIONAL_LEVEL_7 = "7";
    public final static String PROFESSIONAL_LEVEL_8 = "8";

    /**
     * 计算分值
     *
     * @param vo
     */
    public static void computeScore(DipPayToPredictVo vo, Map<String, Object> params) {
        // 计算基础分值
        computeBaseScore(vo);
        // 计算加成分值
        computeAddItiveScore(vo, params);
        // 计算支付结算费用
        computeForecastFee(vo);
    }

    /**
     * 计算支付结算预测费用
     *
     * @param vo
     */
    public static void computeForecastFee(DipPayToPredictVo vo) {
        // 计算预测总费用及付费差异
        if (!ValidateUtil.isEmpty(vo.getTotlSco()) && !ValidateUtil.isEmpty(vo.getPrice())) {
            //单价及总分值均不为空
            vo.setForecast_fee(vo.getTotlSco().multiply(vo.getPrice()));
        } else {
            vo.setForecast_fee(new BigDecimal(0));
        }
        vo.setProfitloss(vo.getForecast_fee().subtract(vo.getSumfee()));
    }

    /**
     * 计算基础分值
     *
     * @param vo
     */
    private static void computeBaseScore(DipPayToPredictVo vo) {

        // 获取病例类型
        String diseType = getDiseType(vo);
        // 分类型算分
        if (ValidateUtil.isNotEmpty(diseType)) {
            switch (diseType) {
                case CASE_TYPE_0:
                    notGroupCompute(vo);
                    break;
                case CASE_TYPE_1:
                    highCompute(vo);
                    break;
                case CASE_TYPE_2:
                    lowCompute(vo);
                    break;
                case CASE_TYPE_3:
                    normalCompute(vo);
                    break;
                case CASE_TYPE_4:
                    notBenchMarkCompute(vo);
                    break;
                case CASE_TYPE_5:
                    unstableCompute(vo);
                    break;
                case CASE_TYPE_6:
                    extreCompute(vo);
                    break;
                case CASE_TYPE_7:
                    basicDisCompute(vo);
                    break;
            }
        } else {
            dataExceptionCompute(vo);
        }
    }

    /**
     * 基层病种分值 = 基础分值 * 0.9
     */
    private static void basicDisCompute(DipPayToPredictVo vo) {
        BigDecimal refer_sco = vo.getRefer_sco().multiply(new BigDecimal(0.9));
        vo.setSettleMentScore(refer_sco);
        vo.setTotlSco(refer_sco);
        setOtherCoefficient(vo);
    }

    /**
     * 获取病例类型
     *
     * @param vo
     * @return
     */
    private static String getDiseType(DipPayToPredictVo vo) {

        String diseType = null;

        // 基层病种
        if (vo.getDisType() != null && vo.getDisType().equals(DIS_TYPE_4)) {
            vo.setDiseType(CASE_TYPE_7);
            diseType = CASE_TYPE_7;
            return diseType;
        }

        if (CASE_TYPE_0.equals(vo.getGrpStas()) || ValidateUtil.isEmpty(vo.getGrpStas())) {
            // 未入组
            vo.setDiseType(CASE_TYPE_0);
            diseType = CASE_TYPE_0;
            // 判断是否为不稳定病种
        }
        // 判断是否为无标杆
        else if (Optional.ofNullable(vo.getMax()).orElse(BigDecimal.ZERO).compareTo(BigDecimal.ZERO) == 0 || Optional.ofNullable(vo.getMin()).orElse(BigDecimal.ZERO).compareTo(BigDecimal.ZERO) == 0) {
            vo.setDiseType(CASE_TYPE_4);
            diseType = CASE_TYPE_4;
        }
        // 判断是否为极高费用病例
        else if (vo.getAreaStandardCost() != null && vo.getAreaStandardCost().compareTo(DEFAULT_COST_0) != 0
                && (vo.getInHosTotalCost().add(vo.getPreHospExamfee())).divide(vo.getAreaStandardCost(), 8, BigDecimal.ROUND_HALF_UP).compareTo(EXTRE_COEFFICIENT) >= 0) {
            vo.setDiseType(CASE_TYPE_6);
            diseType = CASE_TYPE_6;
        } else {
            // 判断超高
            if ((vo.getInHosTotalCost().add(vo.getPreHospExamfee())).compareTo(Optional.ofNullable(vo.getMax()).orElse(BigDecimal.ZERO)) == 1) {
                if (ValidateUtil.isEmpty(vo.getDiseType())) {
                    vo.setDiseType(CASE_TYPE_1);
                    diseType = CASE_TYPE_1;
                }
            }
            // 判断超低
            else if ((vo.getInHosTotalCost().add(vo.getPreHospExamfee())).compareTo(Optional.ofNullable(vo.getMin()).orElse(BigDecimal.ZERO)) == -1) {
                if (ValidateUtil.isEmpty(vo.getDiseType())) {
                    vo.setDiseType(CASE_TYPE_2);
                    diseType = CASE_TYPE_2;
                }
            } else {
                vo.setDiseType(CASE_TYPE_3);
                diseType = CASE_TYPE_3;
            }
        }
        return diseType;
    }

    /**
     * 未入组计算分值
     *
     * @param vo
     */
    private static void notGroupCompute(DipPayToPredictVo vo) {
        dataExceptionCompute(vo);
    }

    /**
     * 无标杆计算分值
     *
     * @param vo
     */
    private static void notBenchMarkCompute(DipPayToPredictVo vo) {
        dataExceptionCompute(vo);
    }

    /**
     * 非稳定病种计算分值
     * 该病种费用 ÷ 全市例均费用 * 100
     *
     * @param vo
     */
    private static void unstableCompute(DipPayToPredictVo vo) {
        if (vo.getDiseaseAverageCost().compareTo(DEFAULT_COST_0) != 0) {
            BigDecimal refer_sco = vo.getInHosTotalCost().add(vo.getPreHospExamfee())
                    .divide(vo.getDiseaseAverageCost(), 8, BigDecimal.ROUND_HALF_UP)
                    .multiply(PROPORTION_BASE);
            vo.setSettleMentScore(refer_sco);
            vo.setTotlSco(refer_sco);
        } else {
            dataExceptionCompute(vo);
        }
    }

    /**
     * 正常计算分值
     * 正常算法 : 基础分值 × 病种系数
     *
     * @param vo
     */
    private static void normalCompute(DipPayToPredictVo vo) {
        BigDecimal refer_sco = vo.getRefer_sco().multiply(vo.getAdjm_cof());
        vo.setSettleMentScore(refer_sco);
        vo.setTotlSco(refer_sco);
        setOtherCoefficient(vo);
    }

    /**
     * 超高计算分值
     * 高倍率算法 :
     * (病种总费用 ÷ 上一年该病种的例均费用 - 上限倍率 + 1) × 基础分值 × 病种系数
     *
     * @param vo
     */
    private static void highCompute(DipPayToPredictVo vo) {
        if (vo.getLastYearLevelStandardCost().compareTo(DEFAULT_COST_0) != 0) {
            BigDecimal refer_sco = vo.getInHosTotalCost().add(vo.getPreHospExamfee())
                    .divide(vo.getLastYearLevelStandardCost(), 8, BigDecimal.ROUND_HALF_UP)
                    .subtract(vo.getUplmtMag())
                    .add(BigDecimal.ONE)
                    .multiply(vo.getRefer_sco())
                    .multiply(vo.getAdjm_cof());
            vo.setSettleMentScore(refer_sco);
            vo.setTotlSco(refer_sco);
            setOtherCoefficient(vo);
        } else {
            dataExceptionCompute(vo);
        }
    }

    /**
     * 超低计算分值
     * 低倍率算法 :
     * 病种总费用 ÷ 同区域例均费用 × 基础分值 x 等级系数
     *
     * @param vo
     */
    private static void lowCompute(DipPayToPredictVo vo) {
        if (vo.getAreaStandardCost().compareTo(DEFAULT_COST_0) != 0) {
            BigDecimal refer_sco = vo.getInHosTotalCost().add(vo.getPreHospExamfee())
                    .divide(vo.getAreaStandardCost(), 8, BigDecimal.ROUND_HALF_UP)
                    .multiply(vo.getRefer_sco()).multiply(vo.getAdjm_cof());
            vo.setSettleMentScore(refer_sco);
            vo.setTotlSco(refer_sco);
            setOtherCoefficient(vo);
        } else {
            dataExceptionCompute(vo);
        }
    }

    /**
     * 极高(极长)病例计算分值
     * 算法 : 病例总费用 ÷ 区域例均费用 × 基础分值
     *
     * @param vo
     */
    private static void extreCompute(DipPayToPredictVo vo) {
        if (vo.getAreaStandardCost().compareTo(DEFAULT_COST_0) != 0) {
            BigDecimal refer_sco = vo.getInHosTotalCost().add(vo.getPreHospExamfee())
                    .divide(vo.getAreaStandardCost(), 8, BigDecimal.ROUND_HALF_UP)
                    .multiply(vo.getRefer_sco());
            vo.setSettleMentScore(refer_sco);
            vo.setTotlSco(refer_sco);
            setOtherCoefficient(vo);
        } else {
            dataExceptionCompute(vo);
        }
    }

    /**
     * 数据异常设置
     *
     * @param vo
     */
    private static void dataExceptionCompute(DipPayToPredictVo vo) {
        vo.setSettleMentScore(DEFAULT_SCORE_0);
        vo.setTotlSco(DEFAULT_SCORE_0);
        setOtherCoefficient(vo);
    }

    /**
     * 没有加成情况下，设置其他系数
     *
     * @param vo
     */
    private static void setOtherCoefficient(DipPayToPredictVo vo) {
        vo.setTcmAdtCof(DEFAULT_COEFFICIENT_1);
        vo.setGrstCof(DEFAULT_COEFFICIENT_1);
        vo.setYoungCof(DEFAULT_COEFFICIENT_1);
        vo.setProfessionalRate(DEFAULT_COEFFICIENT_1);
        vo.setHospCof(DEFAULT_COEFFICIENT_1);
        vo.setAddItiveValue(DEFAULT_SCORE_0);
    }

    /**
     * 计算加成分值
     * 在中医、基层、低龄、重点中，选取最高的分值进行加成
     *
     * @param vo
     * @param params
     */
    private static void computeAddItiveScore(DipPayToPredictVo vo, Map<String, Object> params) {

        BigDecimal addItiveValue = DEFAULT_SCORE_0;

        List<BigDecimal> addItiveValueList = new ArrayList<>();
        // 中医优势病种加成
        BigDecimal chineseAddItiveValue = addItiveChineseDisease(vo, params);
        addItiveValueList.add(chineseAddItiveValue);
        // 基层病种加成
        BigDecimal baseAddItiveValue = addItiveBaseDisease(vo, params);
        addItiveValueList.add(baseAddItiveValue);
        // 低龄病种加成
        BigDecimal youngerAddItiveValue = addItiveYoungerDisease(vo, params);
        addItiveValueList.add(youngerAddItiveValue);
        // 重点专科病种加成
        BigDecimal professionalAddItiveValue = addItiveProfessionalDisease(vo, params);
        addItiveValueList.add(professionalAddItiveValue);

        // 单一就高原则
        addItiveValue = getMaxAddItiveValue(addItiveValueList);

        // 医院系数加成
        addItiveValue = addItiveHospital(vo, params).add(addItiveValue);

        // 辅助目录加成
        addAuxValue(vo, params);

        // 计算总分值
        vo.setAddItiveValue(addItiveValue);
        vo.setTotlSco(vo.getSettleMentScore().add(addItiveValue));

    }

    private static void addAuxValue(DipPayToPredictVo vo, Map<String, Object> params) {

        List<DipDiseaseTypeVo> list = (List<DipDiseaseTypeVo>) params.get(GenerateScoreUtil.KEY_AUX_DISEASE);
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        // 创建Map
        Map<String, DipDiseaseTypeVo> auxMap = list.stream()
                .collect(Collectors.toMap(
                        v -> v.getDipCodg() + "-" + v.getAuxType(),
                        v -> v,
                        (existing, replacement) -> existing // 如果有重复键，保留原值
                ));

        String dipCodg = vo.getDipCodg();
        // 获取辅助目录类型并过滤非空元素,后续可能有cci
        List<String> auxs = Arrays.asList(vo.getAsstListDiseSevDeg(), vo.getAsstListTmorSevDeg(), vo.getAsstListAgeGrp(), vo.getAuxiliaryBurn())
                .stream()
                .filter(Objects::nonNull)  // 保留非空元素
                .filter(s -> !s.isEmpty()) // 保留非空字符串
                .collect(Collectors.toList());

        // 如果过滤后的列表为空，直接返回
        if (auxs.isEmpty()) {
            return;
        }

        // 获取第一个非空辅助值
        BigDecimal currAuxAdm = new BigDecimal(BigInteger.ZERO);
        for (String currAuxType : auxs) {
            // 根据生成的键值对从Map中获取值,是否判断获取最大的辅助目录系数
            DipDiseaseTypeVo dipDiseaseTypeVo = auxMap.get(dipCodg + "-" + currAuxType);
            // 如果找到对应的DipDiseaseTypeVo，进行结算得分的计算
            if (dipDiseaseTypeVo != null) {
                BigDecimal auxXs = dipDiseaseTypeVo.getAuxXs();
                if (auxXs != null && auxXs.compareTo(currAuxAdm) > 0) {
                    currAuxAdm = auxXs;
                }
            }
        }

        if (currAuxAdm.compareTo(BigDecimal.ZERO) > 0 && vo.getSettleMentScore() != null) {
            //辅助目录调节系数大于0
            vo.setSettleMentScore(vo.getSettleMentScore().multiply(currAuxAdm));
        }
    }

    /**
     * 计算中医优势加成分值
     * 在中医优势病种中中医费用占比达到50%的，为中医优势病种
     *
     * @param vo
     * @param params
     * @return
     */
    private static BigDecimal addItiveChineseDisease(DipPayToPredictVo vo, Map<String, Object> params) {

        BigDecimal refer_sco = DEFAULT_SCORE_0;

        // 获取配置信息
        List<DipDiseaseTypeVo> chinsesDisease = (List<DipDiseaseTypeVo>) params.get(GenerateScoreUtil.KEY_CHINESE_DISEASE);
        List<DipDiseaseTypeVo> coefficients = (List<DipDiseaseTypeVo>) params.get(GenerateScoreUtil.KEY_DISEASE_COEFFICIENT);

        if (ValidateUtil.isEmpty(chinsesDisease) || ValidateUtil.isEmpty(coefficients)) {
            vo.setTcmAdtCof(DEFAULT_COEFFICIENT_1);
            return refer_sco;
        }

        for (DipDiseaseTypeVo disease :
                chinsesDisease) {
            if(!ValidateUtil.isEmpty(disease.getChineseDiseaseCode())){
                // 获取中医编码集合
                String[] split = disease.getChineseDiseaseCode().split("或");
                List<String> TCMCodeList = Arrays.asList(split);
                // 判断是否为中医优势病种
                if ((ValidateUtil.isNotEmpty(vo.getTCMCode()) && TCMCodeList.contains(vo.getTCMCode())) ||
                        (ValidateUtil.isNotEmpty(disease.getMedicalCode()) && disease.getMedicalCode().equals(vo.getWMCode()))) {
                    // 判断中医花费是否占 50%
                    if (vo.getHospitalizationExpenses() != null && vo.getHospitalizationExpenses().compareTo(DEFAULT_COST_0) != 0
                            && vo.getTCMTreatmentCost().divide(vo.getHospitalizationExpenses(), 8, BigDecimal.ROUND_HALF_UP)
                            .multiply(PROPORTION_BASE).compareTo(CHINESE_DISEASE_PROPORTION) == 1) {
                        // 计算中医优势加成分值
                        vo.setDieaseType(CHINESE_DISEASE);
                        vo.setChineseDiseaseStatus(DISEASE_STATUS_1);
                        for (DipDiseaseTypeVo adjm_cof :
                                coefficients) {
                            if (adjm_cof.getDiseaseCFTType().equals(vo.getDieaseType())) {
                                vo.setTcmAdtCof(DEFAULT_COEFFICIENT_1.add(adjm_cof.getDiseCof().divide(PROPORTION_BASE)));
                                refer_sco = vo.getSettleMentScore().multiply(adjm_cof.getDiseCof().divide(PROPORTION_BASE));
                                return refer_sco;
                            } else {
                                vo.setTcmAdtCof(DEFAULT_COEFFICIENT_1);
                            }
                        }
                    } else {
                        vo.setTcmAdtCof(DEFAULT_COEFFICIENT_1);
                    }
                } else {
                    vo.setTcmAdtCof(DEFAULT_COEFFICIENT_1);
                }
            }


        }
        return refer_sco;
    }

    /**
     * 计算基层病种加成
     * 通过医院等级判断 : 二乙为1%、一级和未定级为2%、基层为3%
     *
     * @param vo
     * @param params
     * @return
     */
    private static BigDecimal addItiveBaseDisease(DipPayToPredictVo vo, Map<String, Object> params) {

        BigDecimal refer_sco = DEFAULT_SCORE_0;

        // 获取配置信息
        List<DipDiseaseTypeVo> baseDisease = (List<DipDiseaseTypeVo>) params.get(KEY_BASE_DISEASE);
        List<DipDiseaseTypeVo> coefficients = (List<DipDiseaseTypeVo>) params.get(KEY_DISEASE_COEFFICIENT);

        if (ValidateUtil.isEmpty(baseDisease) || ValidateUtil.isEmpty(coefficients)) {
            vo.setGrstCof(DEFAULT_COEFFICIENT_1);
            return refer_sco;
        }
        for (DipDiseaseTypeVo disease :
                baseDisease) {
            // 判断是否为基层病种
            if (disease.getDipCodg().equals(vo.getDipCodg())) {
                // 判断医院级别是否满足
                if (BASE_HOSPITAL_LEVEL.compareTo(new BigDecimal(vo.getHospLv())) <= 0) {
                    vo.setDieaseType(BASE_DISEASE);
                    vo.setBaseDiseaseStatus(DISEASE_STATUS_1);
                    // 计算基层病种加成分值
                    for (DipDiseaseTypeVo adjm_cof :
                            coefficients) {
                        if (vo.getDieaseType().equals(adjm_cof.getDiseaseCFTType())) {
                            // 确定医院等级
                            if (vo.getHospLv().equals(adjm_cof.getHospLv()) && ValidateUtil.isNotEmpty(adjm_cof.getHospLv())) {
                                vo.setGrstCof(DEFAULT_COEFFICIENT_1.add(adjm_cof.getDiseCof().divide(PROPORTION_BASE)));
                                refer_sco = vo.getSettleMentScore().multiply(adjm_cof.getDiseCof().divide(PROPORTION_BASE));
                                return refer_sco;
                            } else {
                                vo.setGrstCof(DEFAULT_COEFFICIENT_1);
                            }
                        } else {
                            vo.setGrstCof(DEFAULT_COEFFICIENT_1);
                        }
                    }
                } else {
                    vo.setGrstCof(DEFAULT_COEFFICIENT_1);
                }
            } else {
                vo.setGrstCof(DEFAULT_COEFFICIENT_1);
            }
        }

        return refer_sco;
    }

    /**
     * 计算低龄病种加成
     * 年龄不满14周岁
     *
     * @param vo
     * @param params
     * @return
     */
    private static BigDecimal addItiveYoungerDisease(DipPayToPredictVo vo, Map<String, Object> params) {
        BigDecimal refer_sco = DEFAULT_SCORE_0;

        // 获取配置参数
        List<DipDiseaseTypeVo> coefficients = (List<DipDiseaseTypeVo>) params.get(KEY_DISEASE_COEFFICIENT);

        if (ValidateUtil.isEmpty(coefficients)) {
            vo.setTcmAdtCof(DEFAULT_COEFFICIENT_1);
            return refer_sco;
        }

        // 判断是否为低龄
        if (vo.getAge() != null && vo.getAge() != BigDecimal.ZERO && vo.getAge().compareTo(YOUNGER_AGE) == -1) {
            vo.setDieaseType(YOUNGER_DISEASE);
            vo.setYoungerDiseaseStatus(DISEASE_STATUS_1);
            for (DipDiseaseTypeVo adjm_cof :
                    coefficients) {
                if (adjm_cof.getDiseaseCFTType().equals(vo.getDieaseType())) {
                    vo.setYoungCof(DEFAULT_COEFFICIENT_1.add(adjm_cof.getDiseCof().divide(PROPORTION_BASE)));
                    refer_sco = vo.getSettleMentScore().multiply(adjm_cof.getDiseCof().divide(PROPORTION_BASE));
                    return refer_sco;
                } else {
                    vo.setYoungCof(DEFAULT_COEFFICIENT_1);
                }
            }
        } else {
            vo.setYoungCof(DEFAULT_COEFFICIENT_1);
        }
        return refer_sco;
    }

    /**
     * 重点专科病种加成
     *
     * @param vo
     * @param params
     * @return
     */
    private static BigDecimal addItiveProfessionalDisease(DipPayToPredictVo vo, Map<String, Object> params) {

        BigDecimal refer_sco = DEFAULT_SCORE_0;

        // 获取配置信息
        List<DipDiseaseTypeVo> focusDisease = (List<DipDiseaseTypeVo>) params.get(KEY_FOCUS_DISEASE);
        List<DipDiseaseTypeVo> coefficients = (List<DipDiseaseTypeVo>) params.get(KEY_DISEASE_COEFFICIENT);

        if (ValidateUtil.isEmpty(focusDisease) || ValidateUtil.isEmpty(coefficients)) {
            vo.setProfessionalRate(DEFAULT_COEFFICIENT_1);
            return refer_sco;
        }

        for (DipDiseaseTypeVo diease :
                focusDisease) {
            // 判断是否为重点学(专)科
            if (ValidateUtil.isNotEmpty(diease.getInhospDeptCodg()) && ValidateUtil.isNotEmpty(vo.getDeptCode())
                    && diease.getInhospDeptCodg().equals(vo.getDeptCode())) {
                vo.setDieaseType(PROFESSIONAL_DISEASE);
                vo.setProfessionalDiseaseStatus(DISEASE_STATUS_1);
                vo.setProfessionalDiseaseType(diease.getKeySpcyType());
                for (DipDiseaseTypeVo adjm_cof :
                        coefficients) {
                    if (adjm_cof.getDiseaseCFTType().equals(vo.getDieaseType())) {
                        // 判断是否为市级重点专科
                        if (vo.getProfessionalDiseaseType().equals(PROFESSIONAL_LEVEL_5)) {
                            if (vo.getHospLv().equals(adjm_cof.getHospLv())) {
                                vo.setProfessionalRate(DEFAULT_COEFFICIENT_1.add(adjm_cof.getDiseCof().divide(PROPORTION_BASE)));
                                refer_sco = vo.getSettleMentScore().multiply(adjm_cof.getDiseCof().divide(PROPORTION_BASE));
                                return refer_sco;
                            } else {
                                vo.setProfessionalRate(DEFAULT_COEFFICIENT_1);
                            }
                        } else {
                            if (vo.getProfessionalDiseaseType().equals(adjm_cof.getKeyDisc())) {
                                vo.setProfessionalRate(DEFAULT_COEFFICIENT_1.add(adjm_cof.getDiseCof().divide(PROPORTION_BASE)));
                                refer_sco = vo.getSettleMentScore().multiply(adjm_cof.getDiseCof().divide(PROPORTION_BASE));
                                return refer_sco;
                            } else {
                                vo.setProfessionalRate(DEFAULT_COEFFICIENT_1);
                            }
                        }
                    } else {
                        vo.setProfessionalRate(DEFAULT_COEFFICIENT_1);
                    }
                }
            } else {
                vo.setProfessionalRate(DEFAULT_COEFFICIENT_1);
            }
        }
        return refer_sco;
    }

    /**
     * 医院系数加成
     * 基层病种、特殊病种、非稳定病种不参与医院系数加成
     *
     * @param vo
     * @return
     */
    private static BigDecimal addItiveHospital(DipPayToPredictVo vo, Map<String, Object> params) {

        BigDecimal refer_sco = DEFAULT_SCORE_0;

        // 获取配置信息
        List<ViewHospitalVo> hospitalInfos = (List<ViewHospitalVo>) params.get(HOSPITAL_INFO);

        // 判断是否为基层病种、非稳定病种、特殊病种
        if ((ValidateUtil.isNotEmpty(vo.getBaseDiseaseStatus()) && vo.getBaseDiseaseStatus().equals(DISEASE_STATUS_1))
                || (ValidateUtil.isNotEmpty(vo.getStableFlag()) && vo.getStableFlag().equals(IS_SD_DISE))
                || (ValidateUtil.isNotEmpty(vo.getDiseType()) && vo.getDiseType().equals(CASE_TYPE_6))) {
            vo.setHospCof(DEFAULT_COEFFICIENT_1);
            return refer_sco;
        }

        // 判断是医院账号，还是管理员
        if (ValidateUtil.isNotEmpty(vo.getHospitalId())) {
            for (ViewHospitalVo hospital :
                    hospitalInfos) {
                if (vo.getHospitalId().equals(hospital.getHospitalId())) {
                    vo.setHospCof(hospital.getHospCof());
                    refer_sco = vo.getSettleMentScore().multiply(hospital.getHospCof().subtract(DEFAULT_COEFFICIENT_1));
                    return refer_sco;
                } else {
                    vo.setHospCof(HOSPITAL_COSFFICIENT);
                    refer_sco = vo.getSettleMentScore().multiply(HOSPITAL_COSFFICIENT.subtract(DEFAULT_COEFFICIENT_1));
                }
            }
        } else {
            vo.setHospCof(HOSPITAL_COSFFICIENT);
            refer_sco = vo.getSettleMentScore().multiply(HOSPITAL_COSFFICIENT.subtract(DEFAULT_COEFFICIENT_1));
        }
        return refer_sco;
    }

    /**
     * 单一就高原则
     * 获取在中医、基层、低龄、重点中最高的分值，相同随机选一个
     *
     * @param addItiveValueList
     * @return
     */
    private static BigDecimal getMaxAddItiveValue(List<BigDecimal> addItiveValueList) {

        BigDecimal max = DEFAULT_SCORE_0;

        for (BigDecimal value :
                addItiveValueList) {
            if (value.compareTo(max) > 0) {
                max = value;
            }
        }
        return max;
    }
}
