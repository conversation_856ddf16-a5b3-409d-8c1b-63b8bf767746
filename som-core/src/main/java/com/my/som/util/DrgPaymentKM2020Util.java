package com.my.som.util;

/**
 * 昆明医保2020年模拟支付业务处理util
 */

import com.my.som.common.constant.DrgConst;
import com.my.som.common.util.ValidateUtil;
import com.my.som.model.drgPayment.SomDrgPayDetl;
import com.my.som.vo.dataHandle.DrgsPaymentMedicalVo;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;


public class DrgPaymentKM2020Util {

    public static List<SomDrgPayDetl> drgPaymentBusiness(List<DrgsPaymentMedicalVo> drgsPaymentMedicalVoList) throws Exception{
        List<SomDrgPayDetl> busDrgPaymentList = new ArrayList<>();
        for(DrgsPaymentMedicalVo dpmv:drgsPaymentMedicalVoList){
            SomDrgPayDetl somDrgPayDetl = new SomDrgPayDetl();
            //基本信息赋值
            somDrgPayDetl.setSettleListId(dpmv.getSettleListId());
            somDrgPayDetl.setMdtrtNo(dpmv.getMdtrtNo());
            somDrgPayDetl.setPatientId(dpmv.getPatientId());
            somDrgPayDetl.setPsnNo(dpmv.getPsnNo());
            somDrgPayDetl.setName(dpmv.getName());
            somDrgPayDetl.setGend(dpmv.getGend());
            somDrgPayDetl.setAge(dpmv.getAge());
            somDrgPayDetl.setCitiIdetNo(dpmv.getCitiIdetNo());
            somDrgPayDetl.setHospitalId(dpmv.getHospitalId());
            somDrgPayDetl.setMedinsName(dpmv.getMedinsName());
            somDrgPayDetl.setHospLv(dpmv.getHospLv());
            somDrgPayDetl.setAdmTime(dpmv.getAdmTime());
            somDrgPayDetl.setDscgTime(dpmv.getDscgTime());
            somDrgPayDetl.setSetlTime(dpmv.getSetlTime());
            somDrgPayDetl.setIptSumfee(dpmv.getIptSumfee());
            somDrgPayDetl.setActIpt(dpmv.getActIpt());
            somDrgPayDetl.setPsnInsuType(dpmv.getPsnInsuType());
            somDrgPayDetl.setDrgCodg(dpmv.getDrgCodg());
            somDrgPayDetl.setDrgName(dpmv.getDrgName());
            somDrgPayDetl.setDrgPayAmt(new BigDecimal("0"));
            if(!ValidateUtil.isEmpty(dpmv.getDrgPayStd())){
                somDrgPayDetl.setDrgPayStd(new BigDecimal(dpmv.getDrgPayStd()));
            }
            if(!ValidateUtil.isEmpty(dpmv.getDrgWt())){
                somDrgPayDetl.setDrgWt(new BigDecimal(dpmv.getDrgWt()));
            }
            if(!ValidateUtil.isEmpty(dpmv.getDrgWt())){
                somDrgPayDetl.setRate(new BigDecimal(dpmv.getDrgWt()));
            }
            somDrgPayDetl.setGrperId(dpmv.getDrgGroupID());
            somDrgPayDetl.setPayStdId(dpmv.getPayStdId());
            somDrgPayDetl.setSpSubsAmt(new BigDecimal("0"));
            somDrgPayDetl.setInitItemPayAmt(new BigDecimal("0"));
            somDrgPayDetl.setActPayAmt(new BigDecimal("0"));
            somDrgPayDetl.setOverExpBlncAmt(new BigDecimal("0"));
            somDrgPayDetl.setDataLogId(dpmv.getDataLogId());
            somDrgPayDetl.setActiveFlag(dpmv.getActiveFlag());
            //1.输入参数校验：visit_code、settle_list_id不能同时为空
            if(ValidateUtil.isEmpty(dpmv.getMdtrtNo())&&ValidateUtil.isEmpty(dpmv.getSettleListId())){
                throw new RuntimeException("就诊号和结算清单病案ID不能同时为空！");
            }
            //2.结算办法开始,如果病案状态为空或者0，
            // 医疗机构结算方式=8 不拨付结算、支付类别 =0 自费、原项目支付类别38 无病历、实际支付金额0
            if(ValidateUtil.isEmpty(dpmv.getMedcasStas())||"0".equals(dpmv.getMedcasStas())){
                somDrgPayDetl.setMedinsSetlWay("8"); //不拨付结算
                somDrgPayDetl.setPayType("0");  //自费
                somDrgPayDetl.setItemPayType("38");  //无病历
                somDrgPayDetl.setActPayAmt(new BigDecimal("0"));//实际支付金额0
            }
            //3.如果医疗类别!='21'非普通住院时
            //医疗机构结算方式=7 项目付费结算、支付类别 =1 项目支付
            //否则：即是普通住院病人 医疗机构结算方式=9  DRGS结算
            if(!"21".equals(dpmv.getMedType())){
                somDrgPayDetl.setMedinsSetlWay("7"); //项目付费结算
                somDrgPayDetl.setPayType("1");  //项目支付
                //再判断如果医疗类别='52'，项目支付类别='60' 生育住院
                //否则医疗类别 in ('211','212','213'）时，项目支付类别='61' 光明工程
                List gmgc = new ArrayList();
                gmgc.add("211");gmgc.add("212");gmgc.add("213");
                if("52".equals(dpmv.getMedType())){
                    somDrgPayDetl.setItemPayType("60");//生育住院
                }else if(gmgc.contains(dpmv.getMedType())){
                    somDrgPayDetl.setItemPayType("61");//光明工程
                }
                //原项目实际金额根据人群类别计算不同
                //如果人群类别='2'城乡 实际支付金额 = 基本医疗基金支出
                //否则 实际支付金额 =基本医疗基金支出 + 大额医疗基金支出(重特病医疗统筹基金支出)
                if(DrgConst.INSURANCE_TYPE_2.equals(dpmv.getPsnInsuType())){
                    somDrgPayDetl.setActPayAmt(dpmv.getBasMedFundPay());
                }else{
                    somDrgPayDetl.setActPayAmt(dpmv.getBasMedFundPay().add(dpmv.getHifobMedFundPay()));
                }
            }else{
                somDrgPayDetl.setMedinsSetlWay("9"); //DRGS结算
            }
            //4.如果单病种编码='9019' 支付类别=1 项目支付、项目支付类别=39 重新结算，
            //否则如果病种编码不为空，医疗机构结算方式=7 项目付费结算、支付类别=1 项目支付、项目支付类别=37 单病种结算
            //否则如果DRG_CODE为空，支付类别=1 项目支付、项目支付类别=35 未入组,
            //否则支付类别=2 DRGs支付、DRG支付类别=11 按DGRs组支付标准，
            //再判断如果日间手术标志=1,支付类别=1 项目支付、项目支付类别=36 日间手术不符合病种,
            //  否则如果住院总费用>3倍支付标准 支付类别=3 总费用高于3倍,先打标志，后面再确定和判断其支付方式
            //  否则如果住院总费用<0.3倍支付标准  支付类别=1 项目支付、项目支付类别=33 项目支付方式
            if("9019".equals(dpmv.getSinDiseCodg())){
                somDrgPayDetl.setPayType("1");  //项目支付
                somDrgPayDetl.setItemPayType("39");//重新结算
            }else if(!ValidateUtil.isEmpty(dpmv.getSinDiseCodg())){
                somDrgPayDetl.setMedinsSetlWay("7"); //项目付费结算
                somDrgPayDetl.setPayType("1");  //项目支付
                somDrgPayDetl.setItemPayType("37");//单病种结算
            }else if(ValidateUtil.isEmpty(dpmv.getDrgCodg())){
                somDrgPayDetl.setPayType("1");  //项目支付
                somDrgPayDetl.setItemPayType("35");//未入组
            }else{
                somDrgPayDetl.setPayType("2");  //DRGs支付
                somDrgPayDetl.setDrgPayType("11");//按DGRs组支付标准
                if("1".equals(dpmv.getDaySurgeryFlag())){
                    somDrgPayDetl.setPayType("1");  //项目支付
                    somDrgPayDetl.setItemPayType("36");//日间手术不符合病种
                }else{
                    if("2".equals(somDrgPayDetl.getPayType())){  //DRG支付
                        if(ValidateUtil.isEmpty(dpmv.getDrgPayStd())){
                            somDrgPayDetl.setPayType("1");  //项目支付
                            somDrgPayDetl.setItemPayType("34");//无支付标准
                        }else{
                            if(dpmv.getIptSumfee().compareTo(new BigDecimal(dpmv.getDrgPayStd()).multiply(new BigDecimal("3")))>-1){
                                somDrgPayDetl.setPayType("3");//总费用高于3倍的,暂未确定属于哪种支付方式
                            }else if(dpmv.getIptSumfee().compareTo(new BigDecimal(dpmv.getDrgPayStd()).multiply(new BigDecimal("0.3")))<1){
                                somDrgPayDetl.setPayType("1");  //项目支付
                                somDrgPayDetl.setItemPayType("33");//总费用低于DRGs支付标准30%
                            }
                        }
                    }
                }
            }
            //5.费用特殊项处理
            //如果住院总费用>3倍支付标准 支付类别=3 总费用高于3倍,先按照住院费用高于DRGs支付标准的差额从高到低排序，
            //取结算人次占入组人次前5%（四舍五入取整计算）的病例，按照项目支付方式结算，其余病例仍按照DRGs支付标准结算。
            //暂时先按照DRGs支付方式结算
            if("3".equals(somDrgPayDetl.getPayType())){
                somDrgPayDetl.setPayType("2");//DRGs支付
            }
            //6.根据各自支付方式，模拟计算费用
            // 如果支付类别='2',
            //    如果人群类别=2 城乡居民,
            //        如果建档立卡人员不足70%,补足70%的补助
            //        否则计算实际金额
            //    否则
            //        如果重大疾病补助、70岁及以上老人不足80%,补足80%的补助
            //        否则计算实际金额
            // 否则 如果人群类别='2' 城乡 实际支付金额 = 基本医疗基金支出
            //      否则 实际支付金额 =基本医疗基金支出 + 大额医疗基金支出(重特病医疗统筹基金支出)
            //如果实际金额<0 如果实际金额=0
            if("2".equals(somDrgPayDetl.getPayType())){
                if(DrgConst.INSURANCE_TYPE_2.equals(dpmv.getPsnInsuType())){
                    BigDecimal fileCardGrant = dpmv.getBasMedFundPay().add(dpmv.getBasMedPoolSelfpayAmt()).
                            subtract(dpmv.getFiliRegisterPsnSubsPay());
                    if(fileCardGrant.compareTo(BigDecimal.ZERO)==0){
                        somDrgPayDetl.setActPayAmt(new BigDecimal("0"));//实际支付金额0
                    }else{
                        BigDecimal payCost = new BigDecimal(dpmv.getDrgPayStd()).subtract(dpmv.getAllOwnpayAmt()).
                                subtract(dpmv.getCoupSelfpayAmt()).subtract(dpmv.getDedcSelfpayAmt()).subtract(dpmv.getOverSelfpayAmt()).
                                add(dpmv.getFiliRegisterPsnSubsPay());
                        BigDecimal fundPayRatio = (dpmv.getBasMedFundPay().subtract(dpmv.getFiliRegisterPsnSubsPay())).
                                divide((dpmv.getBasMedFundPay().add(dpmv.getBasMedPoolSelfpayAmt()).subtract(dpmv.getFiliRegisterPsnSubsPay())),8, BigDecimal.ROUND_HALF_UP);
                        BigDecimal actPayAmt = payCost.multiply(fundPayRatio).setScale(4,BigDecimal.ROUND_HALF_UP);
                        somDrgPayDetl.setActPayAmt(actPayAmt);//城乡居民实际支付金额
                    }
                }else{
                    BigDecimal seriousCardGrant = dpmv.getBasMedFundPay().add(dpmv.getBasMedPoolSelfpayAmt()).
                            add(dpmv.getHifobMedFundPay()).add(dpmv.getHifobMedPoolSelfpayAmt()).
                            subtract(dpmv.getOldSubsPay()).subtract(dpmv.getBasMedFundMajorDiseSubsPay());
                    if(seriousCardGrant.compareTo(BigDecimal.ZERO)==0){
                        somDrgPayDetl.setActPayAmt(new BigDecimal("0"));//实际支付金额0
                    }else{
                        BigDecimal payCost = new BigDecimal(dpmv.getDrgPayStd()).subtract(dpmv.getAllOwnpayAmt()).
                                subtract(dpmv.getCoupSelfpayAmt()).subtract(dpmv.getDedcSelfpayAmt()).subtract(dpmv.getOverSelfpayAmt()).
                                add(dpmv.getOldSubsPay()).add(dpmv.getBasMedFundMajorDiseSubsPay());
                        BigDecimal fundPayRatio = (dpmv.getBasMedFundPay().add(dpmv.getHifobMedFundPay()).subtract(dpmv.getOldSubsPay()).
                                subtract(dpmv.getBasMedFundMajorDiseSubsPay())).
                                divide((dpmv.getBasMedFundPay().add(dpmv.getBasMedPoolSelfpayAmt()).add(dpmv.getHifobMedFundPay()).
                                        add(dpmv.getHifobMedPoolSelfpayAmt()).subtract(dpmv.getOldSubsPay()).subtract(dpmv.getBasMedFundMajorDiseSubsPay())),8, BigDecimal.ROUND_HALF_UP);
                        BigDecimal actPayAmt = payCost.multiply(fundPayRatio).setScale(4,BigDecimal.ROUND_HALF_UP);
                        somDrgPayDetl.setActPayAmt(actPayAmt);//城乡居民实际支付金额
                    }
                }
            }else{
                if(DrgConst.INSURANCE_TYPE_2.equals(dpmv.getPsnInsuType())){
                    somDrgPayDetl.setActPayAmt(dpmv.getBasMedFundPay());
                }else{
                    somDrgPayDetl.setActPayAmt(dpmv.getBasMedFundPay().add(dpmv.getHifobMedFundPay()));
                }
            }
            if(somDrgPayDetl.getActPayAmt().compareTo(BigDecimal.ZERO)==-1){
                somDrgPayDetl.setActPayAmt(new BigDecimal("0"));
            }
            //7.补足支出：如果人群类别=2 城乡居民,特殊补助=建档立卡人员补足70%补助支出
            //否则：特殊补助=基本医疗基金重大疾病补助支出+70岁及以上老人补足80%补助支出
            if(DrgConst.INSURANCE_TYPE_2.equals(dpmv.getPsnInsuType())){
                somDrgPayDetl.setSpSubsAmt(dpmv.getFiliRegisterPsnSubsPay());
            }else{
                somDrgPayDetl.setSpSubsAmt(dpmv.getOldSubsPay().add(dpmv.getBasMedFundMajorDiseSubsPay()));
            }
            //8.drg支付金额：如果人群类别=2 城乡居民, DRG支付金额=实际支付金额 否则：DRG支付金额=0
            //原项目支付金额：如果人群类别=1 城镇职工, 原项目支付金额=基本医疗基金支出+大额医疗基金支出，否则，原项目支付金额=基本医疗基金支出
            //超支结余：超支结余=实际支付金额-项目支付金额
            if("2".equals(somDrgPayDetl.getPayType())){
                somDrgPayDetl.setDrgPayAmt(somDrgPayDetl.getActPayAmt());
            }else{
                somDrgPayDetl.setDrgPayAmt(new BigDecimal("0"));
            }
            if(DrgConst.INSURANCE_TYPE_1.equals(dpmv.getPsnInsuType())){
                somDrgPayDetl.setInitItemPayAmt(dpmv.getBasMedFundPay().add(dpmv.getHifobMedFundPay()));
            }else if(DrgConst.INSURANCE_TYPE_2.equals(dpmv.getPsnInsuType())){
                somDrgPayDetl.setInitItemPayAmt(dpmv.getBasMedFundPay());
            }else{
                somDrgPayDetl.setInitItemPayAmt(new BigDecimal("0"));
            }
            somDrgPayDetl.setOverExpBlncAmt(somDrgPayDetl.getActPayAmt().subtract(somDrgPayDetl.getInitItemPayAmt()));//超支结余
            busDrgPaymentList.add(somDrgPayDetl);
        }
        return busDrgPaymentList;
    }

    public static void main(String[] args) throws Exception {
        List<DrgsPaymentMedicalVo> drgsPaymentMedicalVoList = new ArrayList<>();
        DrgsPaymentMedicalVo drgsPaymentMedicalVo = new DrgsPaymentMedicalVo();
        drgsPaymentMedicalVo.setMdtrtNo("12415635330");
        drgsPaymentMedicalVo.setPatientId("12415635330");
        drgsPaymentMedicalVo.setMedcasStas("1");
        drgsPaymentMedicalVo.setIptSumfee(new BigDecimal("7930.85"));
        drgsPaymentMedicalVo.setAllOwnpayAmt(new BigDecimal("412"));
        drgsPaymentMedicalVo.setCoupSelfpayAmt(new BigDecimal("125"));
        drgsPaymentMedicalVo.setSpCoupSelfpayAmt(new BigDecimal("48"));
        drgsPaymentMedicalVo.setFitReimScpAmt(new BigDecimal("125"));
        drgsPaymentMedicalVo.setCoupSelfpayAmt(new BigDecimal("8383.85"));
        drgsPaymentMedicalVo.setDedcSelfpayAmt(new BigDecimal("152"));
        drgsPaymentMedicalVo.setBasMedPoolSelfpayAmt(new BigDecimal("7192.85"));
        drgsPaymentMedicalVo.setBasMedFundPay(new BigDecimal("7589.85"));
        drgsPaymentMedicalVo.setHifobMedPoolSelfpayAmt(new BigDecimal("0"));
        drgsPaymentMedicalVo.setHifobMedFundPay(new BigDecimal("0"));
        drgsPaymentMedicalVo.setOthSubsFundPay(new BigDecimal("228"));
        drgsPaymentMedicalVo.setDaySurgeryFlag("0");
        drgsPaymentMedicalVo.setOldSubsPay(new BigDecimal("0"));
        drgsPaymentMedicalVo.setFiliRegisterPsnSubsPay(new BigDecimal("0"));
        drgsPaymentMedicalVo.setBasMedFundMajorDiseSubsPay(new BigDecimal("0"));
        drgsPaymentMedicalVoList.add(drgsPaymentMedicalVo);
        long start = System.currentTimeMillis();  //计时开始
        List<SomDrgPayDetl> busDrgPaymentList = drgPaymentBusiness(drgsPaymentMedicalVoList);
        System.out.println(busDrgPaymentList);
        System.out.println("drg支付调用1次;耗时:"+((System.currentTimeMillis() - start))/1000+"s");
    }
}
