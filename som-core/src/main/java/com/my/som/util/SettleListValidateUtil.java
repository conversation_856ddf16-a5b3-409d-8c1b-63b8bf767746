package com.my.som.util;

import com.my.som.common.constant.DrgConst;
import com.my.som.common.exception.AppException;
import com.my.som.common.util.ValidateUtil;
import com.my.som.dto.medicalQuality.BusDiseaseDiagnosisTrim;
import com.my.som.model.dataHandle.SomOprnOprtInfo;
import com.my.som.model.dataHandle.SomHiInvyBasInfo;
import com.my.som.model.medicalQuality.BusFeeBreakDown;
import com.my.som.model.medicalQuality.SomSetlInvyScsCutdInfo;
import com.my.som.vo.dataConfig.MedicareTollVo;
import com.my.som.vo.dataConfig.MergeDisCodeVo;
import com.my.som.vo.dataHandle.SettleListHandlerVo;
import com.my.som.vo.dataHandle.SettleListRuleVo;
import com.my.som.vo.dataHandle.SettleListValidateVo;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * @version 1.0
 * @author: zyd
 * @date: 2023-07-31
 * @description: 结算清单校验
 */
@Slf4j
public class SettleListValidateUtil {

    /**
     * 公共参数
     * KEY_RULES：校验规则
     * KEY_DICT：字典
     * KEY_ICD9：ICD9
     * KEY_ICD10：ICD10
     * KEY_DRG_GRAY_CODE：DRG灰码
     * KEY_GRAY_CODE：灰码
     * KEY_ICD10_GRAY_CODE：ICD10灰码
     * KEY_ICD9_GRAY_CODE：ICD9灰码
     * KEY_NOT_GROUP：非入组编码
     * KEY_INAPPROPRIATE：不适用做为主诊编码
     * KEY_REMNANT：残余编码
     * KEY_DOCTOR_CODE：医保医师编码
     */
    public final static String KEY_RULES = "rules";
    public final static String KEY_DICT = "dict";
    public final static String KEY_ICD9 = "icd9";
    public final static String KEY_ICD10 = "icd10";
    public final static String KEY_DIS_SECTION = "diag_sec";
    public final static String KEY_DIS_SECTION_LIST = "diag_sec_list";
    public final static String KEY_DRG_GRAY_CODE = "drg_gray_code";
    public final static String KEY_ICD10_GRAY_CODE = "icd10_gray_code";
    public final static String KEY_ICD9_GRAY_CODE = "icd_9_gray_code";
    public final static String KEY_ICD10_GRAY_CODE_2 = "icd10_gray_code_2";
    public final static String KEY_ICD9_GRAY_CODE_2 = "icd_9_gray_code_2";
    public final static String KEY_NOT_GROUP = "not_group";
    public final static String KEY_INAPPROPRIATE = "inappropriate";
    public final static String KEY_REMNANT = "remnant";
    public final static String KEY_DOCTOR_CODE = "dr_codg";
    public final static String KEY_DOCTOR_NAME = "dr_name";
    public final static String KEY_NURSE_CODE = "nurse_code";
    public final static String KEY_NURSE_NAME = "nurse_name";
    public final static String KEY_TRANSFUSION_CODE = "transfusion_code";
    public final static String KEY_MERGE_CODE = "merge_code";
    public final static String KEY_COMB_CODE = "comb_code";
    public final static String MEDICARE_TOLL_DATA = "medicare_toll";
    public final static int KEY_DIFFER_DAY = -2;
    public final static String COMB_TYPE_DIAG = "1";
    public final static String COMB_TYPE_OPRN = "2";
    public final static String KEY_TCM_MAIN_DISS = "tcm_main_diss";
    public final static String KEY_TCM_PRINCIPAL = "tcm_principal";
    public final static String KEY_DEPT_CODE = "dept_code";
    public final static String KEY_NOT_BE_MAIN_DIAG = "not_be_main_diag";
    public final static String KEY_NOT_BE_MAIN_OPRT = "not_be_main_oprt";
    public final static String KEY_DIAG_CONFLICT = "diag_conflict";
    public final static String KEY_DIFF_BODY_PARTS = "diff_body_parts";
    public final static String KEY_NWB_AGE_CODE = "new_age_code";
    private final static Map<String, String> KEYS_MAP = new HashMap<String, String>() {
        {
            put(KEY_RULES, "校验规则为空");
            put(KEY_DICT, "清单校验字典为空");
            put(KEY_ICD9, "ICD9编码为空");
            put(KEY_ICD10, "ICD10编码为空");
            put(KEY_DIS_SECTION, "诊断段数据为空");
            put(KEY_DRG_GRAY_CODE, "未查询到DRG灰码");
            put(KEY_ICD10_GRAY_CODE, "未查询到医保2.0诊断灰码");
            put(KEY_ICD9_GRAY_CODE, "未查询到医保2.0手术灰码");
            put(KEY_NOT_GROUP, "未查询到医保非分组方案编码");
            put(KEY_INAPPROPRIATE, "未查询到不适用编码");
            put(KEY_REMNANT, "未查询到残余编码");
            put(KEY_DOCTOR_CODE, "未查询到医保医师编码");
            put(KEY_NURSE_CODE, "未查询到护士编码");
            put(KEY_TRANSFUSION_CODE, "未查询到输血对照编码");
            put(KEY_MERGE_CODE, "未查询到联合编码");
        }
    };

    /**
     * 空
     */
    private final static String NULL = "NULL-VALUE";
    private final static String NULL_ = "-";
    /**
     * 校验类型1：完整性 2：深度质控 3：逻辑性
     */
    private final static String VALIDATE_TYPE_1 = "1";
    private final static String VALIDATE_TYPE_2 = "2";
    private final static String VALIDATE_TYPE_3 = "3";

    /**
     * 校验状态 success: 成功 fail: 失败
     */
    private final static String VALIDATE_STATE_SUCCESS = "1";
    private final static String VALIDATE_STATE_FAIL = "0";

    /**
     * 是否启用
     */
    private final static String IS_Y = "-Y";

    /**
     * 字段前缀
     */
    private final static String FIELD_PREFIX = "get";
    /**
     * 错误分隔符
     */
    private final static String ERROR_DESC_SEPARATOR = ";";


    /**
     * 空值后缀
     */
    private final static String ERROR_NULL_FIELD_SUFFIX = "为空";
    /**
     * 字典未找到后缀
     */
    private final static String ERROR_DICT_DESC_SUFFIX = "未找到对应字典值";
    /**
     * 正则校验后缀
     */
    private final static String ERROR_REG_SUFFIX = "填写不规范";
    /**
     * 长度衣校验后缀
     */
    private final static String ERROR_LENGTH_SUFFIX = "长度填写不规范";

    /**
     * 联合编码间隔字符
     */
    private final static String MERGE_DELIMITER = "@";


    /**
     * 其他常量请勿以 ERROR_TYPE_ 开头！
     * 1：空值
     * 2：字典填写错误
     * 3：正则匹配错误
     * 4：长度错误
     * <p>
     * 深度校验
     * 5: 手术不是医保2.0编码
     * 6: 诊断不是医保2.0编码
     * 7: 性别与编码不匹配
     * 8: 年龄与编码不匹配
     * 9: 不能作为主诊编码
     * 10；主要诊断编码不恰当
     * 11：使用残余类目编码
     * 12：使用医保2.0诊断灰码
     * 13：使用医保2.0手术灰码
     * 14：手术操作名称填写不正确
     * 15：疑似漏编错编码
     * 16：联合编码校验
     * <p>
     * <p>
     * DRG
     * 101: DRG灰码
     * 102: DRG非分组编码
     * 200: DRG医保排除规则
     * <p>
     * 逻辑性
     * 301: 性别与身份证不符
     * 302: 年龄或天龄错误
     * 1.年龄不能大于150岁
     * 2.年龄-（入院日期-出生日期）不小于1岁
     * 3.天龄存在，年龄填写不正确（年龄不是0）
     * 4.天龄不能大于365
     * 5.年龄和天龄不能同时为空
     * 303: 新生儿错误
     * 1.新生儿出生体重(克)不能大于10000
     * 2.新生儿 (年龄小于 28天) 入院时，新生儿出生体重必须填写
     * 3.新生儿出生体重(克)不能小于200
     * 4.新生儿入院体重(克)不能大于20000
     * 5.新生儿入院体重(克)不能小于200
     * 6.新生儿 (年龄小于 28天) 入院时，新生儿入院体重必须填写
     * 304: 时间错误
     * 1.入院时间不能大于出院时间
     * 2.入院时间不应早于出生日期
     * 3.出院时间不应早于出生日期
     * 4.当天入院出院实际住院天数为1
     * 5.实际住院天数填写不正确
     * 305:  门（急）诊诊断错误
     * 1.门（急）诊诊断名称不为空，但对应的门（急）诊诊断编码为空
     * 2.门（急）诊诊断编码不合理
     * 3.门（急）诊诊断编码不为空，但对应的门（急）诊诊断名称为空
     * 4.门（急）诊诊断编码出现P10～P15,入院日期减出生日期不小于365天
     * 5.门（急）诊诊断编码出现P10～P15，且（年龄不足1周岁的）天龄必须小于365天
     * 6.门（急）诊诊断编码与性别不符
     * 306: 诊断编码错误
     * 1.缺少主诊断
     * 2.主诊断重复
     * 3.诊断代码重复
     * 4.诊断编码中含有Z37.0/Z37.2/Z37.3/Z37.5/Z37.6,新生儿出生体重为空
     * 5.诊断编码不合理
     * 6.诊断编码出现P10～P15，入院日期减出生日期必须小于365天
     * 7.诊断编码出现P10～P15,且（年龄不足1周岁的）天龄必须小于365天
     * 8.当主要诊断或者其它诊断编码出现O80-O84编码，且无流产结局编码出现O00-O08编码时，其它诊断编码必须有分娩结局编码Z37
     * 9.诊断编码与性别不符
     * 307: 手术编码错误
     * 1.缺少主手术
     * 2.主手术重复
     * 3.手术操作名称存在手术操作代码不能为空
     * 4.手术操作代码存在手术操作日期不能为空
     * 5.手术操作代码存在手术操作名称不能为空
     * 6.手术操作重复
     * 7.手术操作代码存在术者为空
     */
    public final static String ERROR_TYPE_NULL = "1";
    public final static String ERROR_TYPE_DICT = "2";
    public final static String ERROR_TYPE_REG = "3";
    public final static String ERROR_TYPE_LENGTH = "4";
    public final static String ERROR_TYPE_OPEN2 = "5";
    public final static String ERROR_TYPE_DISN2 = "6";
    public final static String ERROR_TYPE_SEXNM = "7";
    public final static String ERROR_TYPE_AGENM = "8";
    public final static String ERROR_TYPE_NPDIS = "9";
    public final static String ERROR_TYPE_INAPPROPRIATE = "10";
    public final static String ERROR_TYPE_REMNANT = "11";
    public final static String ERROR_TYPE_DIS2GC = "12";
    public final static String ERROR_TYPE_OPE2GC = "13";
    public final static String ERROR_TYPE_OPENAME = "14";
    public final static String ERROR_TYPE_MISSCODE = "15";
    public final static String ERROR_TYPE_MERGECODE = "16";


    public final static String ERROR_TYPE_DRGGC = "101";
    public final static String ERROR_TYPE_DRGNC = "102";
    public final static String ERROR_TYPE_DRGMC = "200";

    public final static String ERROR_TYPE_LG_SIC = "301";
    public final static String ERROR_TYPE_LG_NLTLE = "302";
    public final static String ERROR_TYPE_LG_NBORN = "303";
    public final static String ERROR_TYPE_LG_INOUTTIME = "304";
    public final static String ERROR_TYPE_LG_OET = "305";
    public final static String ERROR_TYPE_LG_DIAGNOSIS = "306";
    public final static String ERROR_TYPE_LG_OPERATION = "307";
    public final static String ERROR_TYPE_LG_CONTACT = "308";

    /**
     * 校验错误类型汇总，前缀
     */
    private final static String PREFIX_ERROR_TYPE = "ERROR_TYPE_";


    /**
     * 基本信息校验使用
     * 类型(1.结算清单信息，2.基金支付信息，3.门诊慢特病诊断信息，4.住院诊断信息，5.收费项目信息，6.手术操作信息，7.重症监护信息，12.输血)
     */
    private final static String VALIDATE_INFO_TYPE_BASE = "1";
    private final static String VALIDATE_INFO_TYPE_PAY = "2";
    private final static String VALIDATE_INFO_TYPE_MMMT = "3";
    private final static String VALIDATE_INFO_TYPE_DIS = "4";
    private final static String VALIDATE_INFO_TYPE_ITEM = "5";
    private final static String VALIDATE_INFO_TYPE_OPE = "6";
    private final static String VALIDATE_INFO_TYPE_ICU = "7";
    private final static String VALIDATE_INFO_TYPE_TRF = "12";

    /**
     * 段代码
     * 1:当性别为男性时，不能使用编码
     * 2:当性别为女性时，不能使用编码
     * 3:年龄与编码不匹配
     * 4:疾病和死亡的外因编码不能作为主要诊断
     * 5:形态学编码（病理）不能作为主要诊断
     */
    private final static String DIS_SECTION_TYPE_1 = "1";
    private final static String DIS_SECTION_TYPE_2 = "2";
    private final static String DIS_SECTION_TYPE_3 = "3";
    private final static String DIS_SECTION_TYPE_4 = "4";
    private final static String DIS_SECTION_TYPE_5 = "5";


    /**
     * 清单校验类型-校验手术编码为医保2.0编码
     */
    private final static String SLVT001 = "SLVT001";
    /**
     * 清单校验类型-校验诊断编码为医保2.0编码
     */
    private final static String SLVT002 = "SLVT002";
    /**
     * 清单校验类型-校验性别与编码不匹配
     */
    private final static String SLVT003 = "SLVT003";
    /**
     * 清单校验类型-校验年龄与编码不匹配
     */
    private final static String SLVT004 = "SLVT004";
    /**
     * 清单校验类型-校验不能作为主诊编码
     */
    private final static String SLVT005 = "SLVT005";
    /**
     * 清单校验类型-校验主要诊断编码不恰当
     */
    private final static String SLVT006 = "SLVT006";
    /**
     * 清单校验类型-校验使用残余类目编码
     */
    private final static String SLVT007 = "SLVT007";
    /**
     * 清单校验类型-校验使用医保2.0诊断灰码
     */
    private final static String SLVT008 = "SLVT008";
    /**
     * 清单校验类型-校验使用医保2.0手术灰码
     */
    private final static String SLVT009 = "SLVT009";
    /**
     * 清单校验类型-校验费用明细错编漏编
     */
    private final static String SLVT010 = "SLVT010";
    /**
     * 清单校验类型-校验诊断编码中是否存在联合编码冲突
     */
    private final static String SLVT011 = "SLVT011";

    /**
     * DRG诊断灰码
     */
    private final static String SLVT101 = "SLVT101";
    /**
     * DRG非分组编码
     */
    private final static String SLVT102 = "SLVT102";


    /**
     * DRG医保排除
     */
    private final static String SLVT2XX = "SLVT2";
    /**
     * 住院天数＞60天
     */
    private final static String SLVT201 = "SLVT201";
    /**
     * 住住院天数＞60天，总费用＜5元
     */
    private final static String SLVT202 = "SLVT202";
    /**
     * 总费用＜5元天
     */
    private final static String SLVT203 = "SLVT203";


    /**
     * 转换
     */
    private final static String CONVERT_DOCTOR_CODE = "DR_CODG";
    private final static String CONVERT_NURSE_CODE = "NURSE_CODE";
    private final static String CONVERT_TRANSFUSION_CODE = "TRANSFUSION_CODE";
    private final static String CONVERT_DOCTOR_AND_NURSE_CODE = "DOCTOR_AND_NURSE_CODE";
    private final static Map<String, Map<String, Object>> CONVERT_MAP = new HashMap<>();


    private final static String SEX_MAN = "1";
    private final static String SEX_WOMAN = "2";

    private final static List<String> MAN_CODE_LIST = Arrays.asList("B26.0", "C60", "C61", "C62", "C63", "D07.4", "D07.5", "D07.6", "D17.6", "D29", "D40",
            "E29", "E89.5", "F52.4", "I86.1", "L29.1", "N40", "N41", "N42", "N43", "N44", "N45", "N46",
            "N47", "N48", "N49", "N50", "N51", "Q53", "Q54", "Q55", "R86", "S31.2", "S31.3", "Z12.5");

    private final static List<String> WOMAN_CODE_LIST = Arrays.asList("A34", "B37.3", "C51", "C52", "C53", "C54", "C55", "C56", "C57", "C58", "C79.6", "D06",
            "D07.0", "D07.1", "D07.2", "D07.3", "D25", "D26", "D27", "D28", "D39", "E28", "E89.4",
            "F52.5", "F53", "I86.3", "L29.2", "M80.0", "M80.1", "M81.0", "M81.1", "M83.0", "N70",
            "N71", "N72", "N73", "N74", "N75", "N76", "N77", "N78", "N79", "N80", "N81", "N82",
            "N83", "N84", "N85", "N86", "N87", "N88", "N89", "N90", "N91", "N92", "N93", "N94",
            "N95", "N96", "N97", "N98", "N99.2", "N99.3", "O", "P54.6", "Q50", "Q51", "Q52", "R87",
            "S31.4", "S37.4", "S37.5", "S37.6", "T19.2", "T19.3", "T83.3", "Z01.4", "Z12.4", "Z30.1",
            "Z30.3", "Z30.5", "Z31.1", "Z31.2", "Z32", "Z33", "Z34", "Z35", "Z36", "Z37", "Z39", "Z87.5", "Z97.5");

    private final static Map<String, List<String>> SEX_CODE_MAP = new HashMap() {
        {
            // 反着加入，因为判断的是性别与编码不符
            put(SEX_MAN, WOMAN_CODE_LIST);
            put(SEX_WOMAN, MAN_CODE_LIST);
        }
    };

    private final static List<String> UNREASONABLE_LIST = Arrays.asList("V", "W", "X", "Y");
    public final static List<String> P10TP15_LIST = Arrays.asList("P10", "P11", "P12", "P13", "P14", "P15");


    /**
     * 执行校验
     *
     * @param settleListValidateVo 校验数据
     * @param settleListParams     清单校验参数
     *                             1、校验规则
     *                             2、清单字典
     * @return
     */
    public static SettleListHandlerVo validate(SettleListValidateVo settleListValidateVo,
                                               Map<String, Object> settleListParams) throws InvocationTargetException, IllegalAccessException {
        SettleListHandlerVo result = new SettleListHandlerVo();
        result.setSettleListId(settleListValidateVo.getSomHiInvyBasInfo().getId());
        result.setChkStas(VALIDATE_STATE_SUCCESS);
        result.setErrorDetails(new ArrayList<>());

        // 初始化，新增类型请修增减ERROR_TYPE
        List<String> errorTypeSummary = getCollectByPrefix(PREFIX_ERROR_TYPE);
        for (String errType : errorTypeSummary) {
            result.getErrorDetails().add(new SettleListHandlerVo(errType));
        }

        // 参数为空抛出异常
        throwNull(settleListParams);

        // 获取参数
        List<SettleListRuleVo> settleListRuleVos = (List<SettleListRuleVo>) settleListParams.get(KEY_RULES);
        Map<String, List<String>> dictMap = (Map<String, List<String>>) settleListParams.get(KEY_DICT);
        // 设置转换数据
        setConvertData(settleListParams, settleListValidateVo.getSomHiInvyBasInfo().getHospitalId());
        // 拆分校验类型
        Map<String, List<SettleListRuleVo>> validateTypeMap = settleListRuleVos.stream().collect(Collectors.groupingBy(SettleListRuleVo::getChkTypeType));

        // 基本字段校验
        baseValidate(validateTypeMap.get(VALIDATE_TYPE_1), settleListValidateVo, result, dictMap);
        // 深度校验
        deepnessValidate(validateTypeMap.get(VALIDATE_TYPE_2), settleListValidateVo, result, settleListParams);
        // 逻辑性校验
        logicValidate(validateTypeMap.get(VALIDATE_TYPE_3), settleListValidateVo, result);

        // 设置汇总
        StringBuilder summaryFields = new StringBuilder();
        StringBuilder summaryDesc = new StringBuilder();
        List<SettleListHandlerVo> removeList = new ArrayList<>();
        for (SettleListHandlerVo errorDetail : result.getErrorDetails()) {
            if (ValidateUtil.isNotEmpty(errorDetail.getErrorFields())) {
                summaryFields.append(errorDetail.getErrorFields());
            } else {
                // 添加移除
                removeList.add(errorDetail);
            }

            if (ValidateUtil.isNotEmpty(errorDetail.getErrDscr())) {
                summaryDesc.append(errorDetail.getErrDscr());
            }
        }
        result.getErrorDetails().removeAll(removeList);
        result.setErrorFields(summaryFields.toString());
        result.setErrDscr(summaryDesc.toString());
        return result;
    }

    /**
     * 基本信息校验
     *
     * @param integrityRules       校验规则
     * @param settleListValidateVo 数据
     * @param result               结果
     * @param dictMap              字典
     * @throws InvocationTargetException
     * @throws IllegalAccessException
     */
    private static void baseValidate(List<SettleListRuleVo> integrityRules,
                                     SettleListValidateVo settleListValidateVo,
                                     SettleListHandlerVo result,
                                     Map<String, List<String>> dictMap) throws InvocationTargetException, IllegalAccessException {
        if (integrityRules != null) {
            // 未找到字段
            StringBuffer validateFieldNotFindFields = new StringBuffer();
            for (SettleListRuleVo integrityRule : integrityRules) {
                // 1.结算清单基本信息校验
                validateInfo(Collections.singletonList(settleListValidateVo.getSomHiInvyBasInfo()), integrityRule, settleListValidateVo, result, dictMap, validateFieldNotFindFields, VALIDATE_INFO_TYPE_BASE);
                // 2.基金支付信息校验
                validateInfo(settleListValidateVo.getBusFundPayList(), integrityRule, settleListValidateVo, result, dictMap, validateFieldNotFindFields, VALIDATE_INFO_TYPE_PAY);
                // 3.门诊慢特病诊断信息校验
                validateInfo(settleListValidateVo.getBusOutpatientClinicDiagnosisList(), integrityRule, settleListValidateVo, result, dictMap, validateFieldNotFindFields, VALIDATE_INFO_TYPE_MMMT);
                // 4.住院诊断信息校验
                validateInfo(settleListValidateVo.getBusDiseaseDiagnosisTrimsList(), integrityRule, settleListValidateVo, result, dictMap, validateFieldNotFindFields, VALIDATE_INFO_TYPE_DIS);
                // 5.收费项目信息校验
                validateInfo(settleListValidateVo.getBusMedicalCostList(), integrityRule, settleListValidateVo, result, dictMap, validateFieldNotFindFields, VALIDATE_INFO_TYPE_ITEM);
                // 6.手术操作信息校验
                validateInfo(settleListValidateVo.getBusOperateDiagnosisList(), integrityRule, settleListValidateVo, result, dictMap, validateFieldNotFindFields, VALIDATE_INFO_TYPE_OPE);
                // 7.重症监护信息校验
                validateInfo(settleListValidateVo.getBusIcuList(), integrityRule, settleListValidateVo, result, dictMap, validateFieldNotFindFields, VALIDATE_INFO_TYPE_ICU);
                // 8.输血信息
                validateInfo(settleListValidateVo.getBusTransfusionList(), integrityRule, settleListValidateVo, result, dictMap, validateFieldNotFindFields, VALIDATE_INFO_TYPE_TRF);
            }

            // 设置数据
            result.setValidateFieldNotFindFields(validateFieldNotFindFields.toString());
        }
    }

    /**
     * 深度校验
     *
     * @param deepnessRules        校验规则
     * @param settleListValidateVo 数据
     * @param result               结果
     * @param settleListParams     参数
     */
    private static void deepnessValidate(List<SettleListRuleVo> deepnessRules,
                                         SettleListValidateVo settleListValidateVo,
                                         SettleListHandlerVo result,
                                         Map<String, Object> settleListParams) {
        if (deepnessRules != null) {
            for (SettleListRuleVo deepnessRule : deepnessRules) {
                switch (deepnessRule.getFldCode()) {
                    case SLVT001:
                        // 校验手术是否为医保2.0编码
                        validateSLVT001(settleListValidateVo, deepnessRule, result, settleListParams);
                        break;
                    case SLVT002:
                        // 校验诊断是否为医保2.0编码
                        validateSLVT002(settleListValidateVo, deepnessRule, result, settleListParams);
                        break;
                    case SLVT003:
                        // 校验性别与编码不匹配
                        validateSLVT003(settleListValidateVo, deepnessRule, result, settleListParams);
                        break;
                    case SLVT004:
                        // 校验年龄与编码不匹配
                        validateSLVT004(settleListValidateVo, deepnessRule, result, settleListParams);
                        break;
                    case SLVT005:
                        // 校验不能作为主诊编码(外因编码,病理编码)
                        validateSLVT005(settleListValidateVo, deepnessRule, result, settleListParams);
                        break;
                    case SLVT006:
                        // 校验主要诊断编码不恰当
                        validateSLVT006(settleListValidateVo, deepnessRule, result, settleListParams);
                        break;
                    case SLVT007:
                        // 校验使用残余类目编码
                        validateSLVT007(settleListValidateVo, deepnessRule, result, settleListParams);
                        break;
                    case SLVT008:
                        // 校验使用医保2.0诊断灰码
                        validateSLVT008(settleListValidateVo, deepnessRule, result, settleListParams);
                        break;
                    case SLVT009:
                        // 校验使用医保2.0手术灰码
                        validateSLVT009(settleListValidateVo, deepnessRule, result, settleListParams);
                        break;
                    case SLVT010:
                        // 校验费用明细错编漏编
                        validateSLVT010(settleListValidateVo, deepnessRule, result, settleListParams);
                        break;
                    case SLVT011:
                        // 校验诊断编码中是否存在联合编码冲突
                        validateSLVT011(settleListValidateVo, deepnessRule, result, settleListParams);
                        break;

                    case SLVT101:
                        // DRG诊断灰码
                        validateSLVT101(settleListValidateVo, deepnessRule, result, settleListParams);
                        break;

                    case SLVT102:
                        // DRG非分组编码
                        validateSLVT102(settleListValidateVo, deepnessRule, result, settleListParams);
                        break;
                }

                /*
                不和上面规则写一块原因为当前排除规则会根据当地医保发生变动，可能会重新调整
                 */
                // DRG医保排除规则
                if (deepnessRule.getFldCode().startsWith(SLVT2XX)) {
                    validateDrgMedicalExclude(settleListValidateVo, deepnessRule, result, settleListParams);
                }
            }
        }
    }

    /**
     * 逻辑性校验
     *
     * @param logicRuleVos         校验规则
     * @param settleListValidateVo 数据
     * @param result               结果
     */
    private static void logicValidate(List<SettleListRuleVo> logicRuleVos,
                                      SettleListValidateVo settleListValidateVo,
                                      SettleListHandlerVo result) throws InvocationTargetException, IllegalAccessException {
        if (logicRuleVos != null) {
            for (SettleListRuleVo logicRuleVo : logicRuleVos) {
                String methodName = "validate" + logicRuleVo.getFldCode();
                Method method = null;
                try {
                    method = SettleListValidateUtil.class.getDeclaredMethod(methodName, settleListValidateVo.getClass(), logicRuleVo.getClass(), result.getClass());
                } catch (NoSuchMethodException e) {
                    log.info("逻辑性校验未找到方法,方法名称: {}, 含义: {}", methodName, logicRuleVo.getVerfRule());
                }
                if (method != null) {
                    method.invoke(SettleListValidateUtil.class, settleListValidateVo, logicRuleVo, result);
                }
            }

        }
    }

    /**
     * 性别与身份证不符
     *
     * @param settleListValidateVo 数据
     * @param ruleVo               校验规则
     * @param result               结果
     */
    private static void validateLG001(SettleListValidateVo settleListValidateVo,
                                      SettleListRuleVo ruleVo,
                                      SettleListHandlerVo result) {

        SomHiInvyBasInfo somHiInvyBasInfo = settleListValidateVo.getSomHiInvyBasInfo();
        // 1男 2女
        String gend = somHiInvyBasInfo.getA12c();
        List<String> womanSexList = Arrays.asList("0", "2", "4", "6", "8");
        List<String> manSexList = Arrays.asList("1", "3", "5", "7", "9");
        Map<String, List<String>> sexMap = new HashMap<>();
        sexMap.put(SEX_MAN, manSexList);
        sexMap.put(SEX_WOMAN, womanSexList);
        // 身份证
        String citiIdetNo = somHiInvyBasInfo.getA20();
        if (ValidateUtil.isNotEmpty(gend) &&
                ValidateUtil.isNotEmpty(citiIdetNo)) {
            if (citiIdetNo.length() >= 17 && sexMap.get(gend) != null && !sexMap.get(gend).contains(citiIdetNo.substring(16, 17))) {
                String error = "当前性别为: " + (SEX_MAN.equals(gend) ? "男" : "女") + "，身份证中第17位(奇男偶女)为: " + citiIdetNo.substring(16, 17);
                addError(result, ruleVo, settleListValidateVo, null, ERROR_TYPE_LG_SIC, null, VALIDATE_TYPE_3, error);
            }
        }
    }

    /**
     * 年龄或天龄错误
     * 1.年龄不能大于150岁
     * 2.年龄 -（入院日期-出生日期）不小于1岁
     * 3.天龄存在，年龄填写不正确（年龄不是0）
     * 4.天龄不能大于365
     * 5.年龄和天龄不能同时为空
     *
     * @param settleListValidateVo 数据
     * @param ruleVo               校验规则
     * @param result               结果
     */
    private static void validateLG002(SettleListValidateVo settleListValidateVo,
                                      SettleListRuleVo ruleVo,
                                      SettleListHandlerVo result) {
        SomHiInvyBasInfo somHiInvyBasInfo = settleListValidateVo.getSomHiInvyBasInfo();
        // 年龄
        Integer age = somHiInvyBasInfo.getA14();
        // 天龄（不足一周岁的年龄）
        Integer ageDays = somHiInvyBasInfo.getA16();
        // 入院日期
        String inHosTime = somHiInvyBasInfo.getB12();
        // 出生日期
        String brdy = somHiInvyBasInfo.getA13();
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        if (!ValidateUtil.isEmpty(age)) {
            if (age > 150) {
                addError(result, ruleVo, settleListValidateVo, null, ERROR_TYPE_LG_NLTLE, null, VALIDATE_TYPE_3, "年龄不能大于150岁");
            }

            if (ValidateUtil.isNotEmpty(inHosTime) && ValidateUtil.isNotEmpty(brdy) && judgeDatePattern(inHosTime, brdy)) {
                try {
                    Date inHosDate = format.parse(inHosTime);
                    Date birthdayDate = format.parse(brdy);
                    int calcAge = (int) ((inHosDate.getTime() - birthdayDate.getTime()) / 365 / 24 / 60 / 60 / 1000);
                    if (age - calcAge >= 1) {
                        addError(result, ruleVo, settleListValidateVo, null, ERROR_TYPE_LG_NLTLE, null, VALIDATE_TYPE_3, "年龄 -（入院日期-出生日期）不小于1岁");
                    }
                } catch (ParseException e) {
                    throw new RuntimeException(e);
                } catch (NumberFormatException e) {
                    e.printStackTrace();
                }
            }

            if (!ValidateUtil.isEmpty(ageDays) && ageDays > 0 && age != 0) {
                addError(result, ruleVo, settleListValidateVo, null, ERROR_TYPE_LG_NLTLE, null, VALIDATE_TYPE_3, "天龄存在，年龄不是0");
            }
        } else {
            if (ValidateUtil.isEmpty(ageDays)) {
                addError(result, ruleVo, settleListValidateVo, null, ERROR_TYPE_LG_NLTLE, null, VALIDATE_TYPE_3, "年龄和天龄不能同时为空");
            }
        }

        if (!ValidateUtil.isEmpty(ageDays) && ageDays > 365) {
            addError(result, ruleVo, settleListValidateVo, null, ERROR_TYPE_LG_NLTLE, null, VALIDATE_TYPE_3, "天龄不能大于365");
        }
    }

    /**
     * 新生儿错误
     * 1.新生儿出生体重(克)不能大于10000
     * 2.新生儿 (年龄小于 28天) 入院时，新生儿出生体重必须填写
     * 3.新生儿出生体重(克)不能小于200
     * 4.新生儿入院体重(克)不能大于20000
     * 5.新生儿入院体重(克)不能小于200
     * 6.新生儿 (年龄小于 28天) 入院时，新生儿入院体重必须填写
     *
     * @param settleListValidateVo 数据
     * @param ruleVo               校验规则
     * @param result               结果
     */
    private static void validateLG003(SettleListValidateVo settleListValidateVo,
                                      SettleListRuleVo ruleVo,
                                      SettleListHandlerVo result) {
        SomHiInvyBasInfo somHiInvyBasInfo = settleListValidateVo.getSomHiInvyBasInfo();
        // 新生儿入院体重（克）
        Double newBornInHosWeight = somHiInvyBasInfo.getA17();
        // 新生儿出生体重(克)
        Double newBornWeight = somHiInvyBasInfo.getA18();
        // 年龄
        Integer age = somHiInvyBasInfo.getA14();
        // 天龄（不足一周岁的年龄）
        Integer ageDays = somHiInvyBasInfo.getA16();
        // 判断新生儿
        if ((ValidateUtil.isEmpty(age) || age == 0)) {
            if (!ValidateUtil.isEmpty(newBornWeight)) {
                if (newBornWeight > 10000) {
                    addError(result, ruleVo, settleListValidateVo, null, ERROR_TYPE_LG_NBORN, null, VALIDATE_TYPE_3, "新生儿出生体重(克)不能大于10000");
                }
                if (newBornWeight < 200) {
                    addError(result, ruleVo, settleListValidateVo, null, ERROR_TYPE_LG_NBORN, null, VALIDATE_TYPE_3, "新生儿出生体重(克)不能小于200");
                }
            }

            if (!ValidateUtil.isEmpty(newBornInHosWeight)) {
                if (newBornInHosWeight > 20000) {
                    addError(result, ruleVo, settleListValidateVo, null, ERROR_TYPE_LG_NBORN, null, VALIDATE_TYPE_3, "新生儿入院体重(克)不能大于20000");
                }
                if (newBornInHosWeight < 200) {
                    addError(result, ruleVo, settleListValidateVo, null, ERROR_TYPE_LG_NBORN, null, VALIDATE_TYPE_3, "新生儿入院体重(克)不能小于200");
                }
            }

            if (!ValidateUtil.isEmpty(ageDays) && ageDays < 28) {
                if (ValidateUtil.isEmpty(newBornWeight)) {
                    addError(result, ruleVo, settleListValidateVo, null, ERROR_TYPE_LG_NBORN, null, VALIDATE_TYPE_3, "新生儿 (年龄小于 28天) 入院时，新生儿出生体重必须填写");
                }
                if (ValidateUtil.isEmpty(newBornInHosWeight)) {
                    addError(result, ruleVo, settleListValidateVo, null, ERROR_TYPE_LG_NBORN, null, VALIDATE_TYPE_3, "新生儿 (年龄小于 28天) 入院时，新生儿入院体重必须填写");
                }
            }

        }
    }

    /**
     * 时间错误
     * 1.入院时间不能大于出院时间
     * 2.入院时间不应早于出生日期
     * 3.出院时间不应早于出生日期
     * 4.当天入院出院实际住院天数为1
     * 5.实际住院天数填写不正确
     * 6.手术开始时间(天) - 入院时间(天) 大于等于 -2
     * 7.手术结束时间 < 出院时间
     * 8.麻醉开始时间(天) - 入院时间(天) 大于等于 -2
     * 9.麻醉结束时间 < 出院时间
     *
     * @param settleListValidateVo 数据
     * @param ruleVo               校验规则
     * @param result               结果
     */
    private static void validateLG004(SettleListValidateVo settleListValidateVo,
                                      SettleListRuleVo ruleVo,
                                      SettleListHandlerVo result) {
        SomHiInvyBasInfo somHiInvyBasInfo = settleListValidateVo.getSomHiInvyBasInfo();
        List<SomOprnOprtInfo> busOperateDiagnosisList = settleListValidateVo.getBusOperateDiagnosisList();
        // 入院时间
        String inHosTime = somHiInvyBasInfo.getB12();
        // 出院时间
        String outHosTime = somHiInvyBasInfo.getB15();
        // 实际住院天数
        String inHosDays = somHiInvyBasInfo.getB20();
        // 出生日期
        String brdy = somHiInvyBasInfo.getA13();
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        if (ValidateUtil.isNotEmpty(inHosTime) && judgeDatePattern(inHosTime)) {
            try {
                Date inHosDate = format.parse(inHosTime);
                if (ValidateUtil.isNotEmpty(outHosTime) && judgeDatePattern(outHosTime)) {
                    Date outHosDate = format.parse(outHosTime);
                    if (inHosDate.getTime() > outHosDate.getTime()) {
                        addError(result, ruleVo, settleListValidateVo, null, ERROR_TYPE_LG_INOUTTIME, null, VALIDATE_TYPE_3, "入院时间不能大于出院时间");
                    }

                    int calcTime = (int) ((outHosDate.getTime() - inHosDate.getTime()) / 24 / 60 / 60 / 1000);
                    if (ValidateUtil.isNotEmpty(inHosDays)) {
                        if (calcTime == 0 && Integer.parseInt(inHosDays) != 1) {
                            addError(result, ruleVo, settleListValidateVo, null, ERROR_TYPE_LG_INOUTTIME, null, VALIDATE_TYPE_3, "当天入院出院实际住院天数不为1");
                        }

                        if (calcTime == 0) {
                            calcTime = 1;
                        }
                        if (calcTime != Integer.parseInt(inHosDays)) {
//                            addError(result, ruleVo, settleListValidateVo, null, ERROR_TYPE_LG_INOUTTIME, null, VALIDATE_TYPE_3, "实际住院天数填写不正确");
                        }
                    }
                }

                if (ValidateUtil.isNotEmpty(brdy) && judgeDatePattern(brdy)) {
                    Date birDate = format.parse(brdy);
                    if (inHosDate.getTime() < birDate.getTime()) {
                        addError(result, ruleVo, settleListValidateVo, null, ERROR_TYPE_LG_INOUTTIME, null, VALIDATE_TYPE_3, "入院时间不能早于出生日期");
                    }
                }
            } catch (ParseException e) {
                throw new RuntimeException(e);
            } catch (NumberFormatException e) {
                e.printStackTrace();
            }
        }

        if (ValidateUtil.isNotEmpty(outHosTime) && ValidateUtil.isNotEmpty(brdy) && judgeDatePattern(outHosTime, brdy)) {
            try {
                Date outHosDate = format.parse(outHosTime);
                Date birDate = format.parse(brdy);
                if (outHosDate.getTime() < birDate.getTime()) {
                    addError(result, ruleVo, settleListValidateVo, null, ERROR_TYPE_LG_INOUTTIME, null, VALIDATE_TYPE_3, "出院时间不能早于出生日期");
                }
            } catch (ParseException e) {
                throw new RuntimeException(e);
            } catch (NumberFormatException e) {
                e.printStackTrace();
            }
        }

        if (ValidateUtil.isNotEmpty(busOperateDiagnosisList)) {
            try {
                for (SomOprnOprtInfo somOprnOprtInfo :
                        busOperateDiagnosisList) {
                    if (ValidateUtil.isNotEmpty(somOprnOprtInfo.getOprnOprtBegntime())
                            && ValidateUtil.isNotEmpty(somOprnOprtInfo.getOprnOprtEndtime())
                            && ValidateUtil.isNotEmpty(inHosTime) && ValidateUtil.isNotEmpty(outHosTime)
                            && judgeDatePattern(somOprnOprtInfo.getOprnOprtBegntime(), somOprnOprtInfo.getOprnOprtEndtime(), inHosTime, outHosTime)) {
                        Date oprnOprtBegnDate = format.parse(somOprnOprtInfo.getOprnOprtBegntime());
                        Date OprnOprtEndDate = format.parse(somOprnOprtInfo.getOprnOprtEndtime());
                        Date inHosDate = format.parse(inHosTime);
                        Date outHosDate = format.parse(outHosTime);
                        int diffOprnDay = (int) ((oprnOprtBegnDate.getTime() - inHosDate.getTime()) / 24 / 60 / 60 / 1000);
                        if (diffOprnDay <= KEY_DIFFER_DAY) {
                            String error = getOpeErrorPrefix(somOprnOprtInfo.getSeq() + 1) + "：手术开始时间不应早于入院时间前两天";
                            addError(result, ruleVo, settleListValidateVo, null, ERROR_TYPE_LG_INOUTTIME, null, VALIDATE_TYPE_3, error);
                        }
                        if (OprnOprtEndDate.getTime() > outHosDate.getTime()) {
                            String error = getOpeErrorPrefix(somOprnOprtInfo.getSeq() + 1) + "：手术结束时间应小于出院时间";
                            addError(result, ruleVo, settleListValidateVo, null, ERROR_TYPE_LG_INOUTTIME, null, VALIDATE_TYPE_3, error);
                        }
                        if (ValidateUtil.isNotEmpty(somOprnOprtInfo.getAnstBegntime())
                                && ValidateUtil.isNotEmpty(somOprnOprtInfo.getAnstEndtime())) {
                            Date anstBegnDate = format.parse(somOprnOprtInfo.getAnstBegntime());
                            Date anstEndDate = format.parse(somOprnOprtInfo.getAnstEndtime());
                            int diffAnstDay = (int) ((anstBegnDate.getTime() - inHosDate.getTime()) / 24 / 60 / 60 / 1000);
                            if (diffAnstDay <= KEY_DIFFER_DAY) {
                                String error = getOpeErrorPrefix(somOprnOprtInfo.getSeq() + 1) + "：麻醉开始时间不应早于入院时间前两天";
                                addError(result, ruleVo, settleListValidateVo, null, ERROR_TYPE_LG_INOUTTIME, null, VALIDATE_TYPE_3, error);
                            }
                            if (anstEndDate.getTime() > outHosDate.getTime()) {
                                String error = getOpeErrorPrefix(somOprnOprtInfo.getSeq() + 1) + "：麻醉结束时间应小于出院时间";
                                addError(result, ruleVo, settleListValidateVo, null, ERROR_TYPE_LG_INOUTTIME, null, VALIDATE_TYPE_3, error);
                            }
                        }
                    }
                }
            } catch (ParseException e) {
                throw new RuntimeException(e);
            } catch (NumberFormatException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 门（急）诊诊断错误
     * 1.门（急）诊诊断名称不为空，但对应的门（急）诊诊断编码为空
     * 2.门（急）诊诊断编码不合理
     * 3.门（急）诊诊断编码不为空，但对应的门（急）诊诊断名称为空
     * 4.门（急）诊诊断编码出现P10～P15,入院日期减出生日期不小于365天
     * 5.门（急）诊诊断编码出现P10～P15，且（年龄不足1周岁的）天龄必须小于365天
     * 6.门（急）诊诊断编码与性别不符
     *
     * @param settleListValidateVo 数据
     * @param ruleVo               校验规则
     * @param result               结果
     */
    private static void validateLG005(SettleListValidateVo settleListValidateVo,
                                      SettleListRuleVo ruleVo,
                                      SettleListHandlerVo result) {
        SomHiInvyBasInfo somHiInvyBasInfo = settleListValidateVo.getSomHiInvyBasInfo();
        // 门急诊诊断编码
        String oetCode = somHiInvyBasInfo.getC01c();
        // 门急诊诊断名称
        String octName = somHiInvyBasInfo.getC02n();


        if (ValidateUtil.isNotEmpty(octName) &&
                ValidateUtil.isEmpty(oetCode)) {
            addError(result, ruleVo, settleListValidateVo, null, ERROR_TYPE_LG_OET, null, VALIDATE_TYPE_3, "门（急）诊诊断名称不为空，但对应的门（急）诊诊断编码为空");
        }

        if (ValidateUtil.isNotEmpty(oetCode) &&
                ValidateUtil.isEmpty(octName)) {
            addError(result, ruleVo, settleListValidateVo, null, ERROR_TYPE_LG_OET, null, VALIDATE_TYPE_3, "门（急）诊诊断编码不为空，但对应的门（急）诊诊断名称为空");
        }

        if (ValidateUtil.isNotEmpty(oetCode)) {
            if (judgeUnreasonableCode(oetCode)) {
                addError(result, ruleVo, settleListValidateVo, null, ERROR_TYPE_LG_OET, null, VALIDATE_TYPE_3, "门（急）诊诊断编码不合理");
            }

            if (judgeP10TP15Code(somHiInvyBasInfo, oetCode, DrgConst.TYPE_1)) {
                addError(result, ruleVo, settleListValidateVo, null, ERROR_TYPE_LG_OET, null, VALIDATE_TYPE_3, "门（急）诊诊断编码出现P10～P15时,入院日期减出生日期应小于365天");
            }

            if (judgeP10TP15Code(somHiInvyBasInfo, oetCode, DrgConst.TYPE_2)) {
                addError(result, ruleVo, settleListValidateVo, null, ERROR_TYPE_LG_OET, null, VALIDATE_TYPE_3, "门（急）诊诊断编码出现P10～P15，且（年龄不足1周岁的）天龄必须小于365天");
            }

            if (judgeSexCode(somHiInvyBasInfo, oetCode)) {
                addError(result, ruleVo, settleListValidateVo, null, ERROR_TYPE_LG_OET, null, VALIDATE_TYPE_3, "门（急）诊诊断编码与性别不符");
            }
        }
    }

    /**
     * 诊断编码错误
     * 1.缺少主诊断
     * 2.主诊断重复
     * 3.诊断代码重复
     * 4.诊断编码中含有Z37.0/Z37.2/Z37.3/Z37.5/Z37.6,新生儿出生体重为空
     * 5.诊断编码不合理
     * 6.诊断编码出现P10～P15，入院日期减出生日期必须小于365天
     * 7.诊断编码出现P10～P15,且（年龄不足1周岁的）天龄必须小于365天
     * 8.当主要诊断或者其它诊断编码出现O80-O84编码，且无流产结局编码出现O00-O08编码时，其它诊断编码必须有分娩结局编码Z37
     * 9.诊断编码与性别不符
     *
     * @param settleListValidateVo 数据
     * @param ruleVo               校验规则
     * @param result               结果
     */
    private static void validateLG006(SettleListValidateVo settleListValidateVo,
                                      SettleListRuleVo ruleVo,
                                      SettleListHandlerVo result) {

        List<BusDiseaseDiagnosisTrim> diagnosisList = settleListValidateVo.getBusDiseaseDiagnosisTrimsList();
        SomHiInvyBasInfo somHiInvyBasInfo = settleListValidateVo.getSomHiInvyBasInfo();
        List<String> diagnosisError1List = Arrays.asList("Z37.0", "Z37.2", "Z37.3", "Z37.5", "Z37.6");
        List<String> diagnosisError2List = Arrays.asList("O80", "O81", "O82", "O83", "O84");
        List<String> diagnosisAINList = Arrays.asList("O00", "O01", "O02", "O03", "O04", "O05", "O06", "O07", "O08");
        // 新生儿出生体重(克)
        Double newBornWeight = somHiInvyBasInfo.getA18();
        if (ValidateUtil.isNotEmpty(diagnosisList)) {
            boolean primaryDiagnosisFlag = true;
            boolean primaryDiagnosisRepeatFlag = false;
            boolean newBornNullFlag = false;
            boolean p10TP15ErrorFlag1 = false;
            boolean p10TP15ErrorFlag2 = false;
            boolean o80TO84Flag = false;
            boolean o00TO08Flag = false;
            boolean z37Flag = false;
            boolean sexCodeNonConformityFlag = false;
            String primaryDiagnosisCode = diagnosisList.get(0).getC06c1();
            Set<String> diagnosisSet = new HashSet<>();
            List<String> unreasonableCodeList = new ArrayList<>();
            for (BusDiseaseDiagnosisTrim diagnosis : diagnosisList) {
                if (diagnosis.getSeq() == 0) {
                    primaryDiagnosisFlag = false;
                }

                if (ValidateUtil.isNotEmpty(diagnosis.getC06c1())) {
                    if (diagnosis.getSeq() != 0 &&
                            ValidateUtil.isNotEmpty(primaryDiagnosisCode) &&
                            primaryDiagnosisCode.equals(diagnosis.getC06c1())) {
                        primaryDiagnosisRepeatFlag = true;
                    }

                    if (diagnosis.getC06c1().length() > 5 &&
                            diagnosisError1List.contains(diagnosis.getC06c1().substring(0, 5)) &&
                            ValidateUtil.isEmpty(newBornWeight)) {
                        newBornNullFlag = true;
                    }

                    if (judgeUnreasonableCode(diagnosis.getC06c1())) {
                        unreasonableCodeList.add(diagnosis.getC06c1());
                    }

                    if (judgeP10TP15Code(somHiInvyBasInfo, diagnosis.getC06c1(), DrgConst.TYPE_1)) {
                        p10TP15ErrorFlag1 = true;
                    }

                    if (judgeP10TP15Code(somHiInvyBasInfo, diagnosis.getC06c1(), DrgConst.TYPE_2)) {
                        p10TP15ErrorFlag2 = true;
                    }

                    if (diagnosis.getC06c1().length() >= 3) {
                        if (diagnosisError2List.contains(diagnosis.getC06c1().substring(0, 3))) {
                            o80TO84Flag = true;
                        }
                        if (diagnosisAINList.contains(diagnosis.getC06c1().substring(0, 3))) {
                            o00TO08Flag = true;
                        }
                        if (diagnosis.getSeq() != 0 && "Z37".equals(diagnosis.getC06c1().substring(0, 3))) {
                            z37Flag = true;
                        }
                    }

                    if (judgeSexCode(somHiInvyBasInfo, diagnosis.getC06c1())) {
                        sexCodeNonConformityFlag = true;
                    }
                }
                diagnosisSet.add(diagnosis.getC06c1());
            }

            if (primaryDiagnosisFlag) {
                addError(result, ruleVo, settleListValidateVo, null, ERROR_TYPE_LG_DIAGNOSIS, null, VALIDATE_TYPE_3, "缺少主诊断");
            }

            if (primaryDiagnosisRepeatFlag) {
                addError(result, ruleVo, settleListValidateVo, null, ERROR_TYPE_LG_DIAGNOSIS, null, VALIDATE_TYPE_3, "主诊断重复");
            }

            // 如果主诊重复则不显示诊断代码重复
            if (diagnosisSet.size() != diagnosisList.size() && !primaryDiagnosisRepeatFlag) {
                addError(result, ruleVo, settleListValidateVo, null, ERROR_TYPE_LG_DIAGNOSIS, null, VALIDATE_TYPE_3, "诊断代码重复");
            }

            if (newBornNullFlag) {
                addError(result, ruleVo, settleListValidateVo, null, ERROR_TYPE_LG_DIAGNOSIS, null, VALIDATE_TYPE_3, "诊断编码中含有Z37.0/Z37.2/Z37.3/Z37.5/Z37.6,新生儿出生体重为空");
            }

            if (unreasonableCodeList.size() != 0) {
                addError(result, ruleVo, settleListValidateVo, null, ERROR_TYPE_LG_DIAGNOSIS, null, VALIDATE_TYPE_3, "诊断编码不合理，不合理编码: " + unreasonableCodeList);
            }

            if (p10TP15ErrorFlag1) {
                addError(result, ruleVo, settleListValidateVo, null, ERROR_TYPE_LG_DIAGNOSIS, null, VALIDATE_TYPE_3, "诊断编码出现P10～P15，入院日期减出生日期必须小于365天");
            }

            if (p10TP15ErrorFlag2) {
                addError(result, ruleVo, settleListValidateVo, null, ERROR_TYPE_LG_DIAGNOSIS, null, VALIDATE_TYPE_3, "诊断编码出现P10～P15,且（年龄不足1周岁的）天龄必须小于365天");
            }

            if (o80TO84Flag && o00TO08Flag && !z37Flag) {
                addError(result, ruleVo, settleListValidateVo, null, ERROR_TYPE_LG_DIAGNOSIS, null, VALIDATE_TYPE_3, "当主要诊断或者其它诊断编码出现O80-O84编码，且无流产结局编码出现O00-O08编码时，其它诊断编码必须有分娩结局编码Z37");
            }

            if (sexCodeNonConformityFlag) {
                addError(result, ruleVo, settleListValidateVo, null, ERROR_TYPE_LG_DIAGNOSIS, null, VALIDATE_TYPE_3, "性别与编码不符");
            }
        }
    }


    /**
     * 手术编码错误
     * 1.缺少主手术
     * 2.主手术重复
     * 3.手术操作名称存在手术操作代码不能为空
     * 4.手术操作代码存在手术操作日期不能为空
     * 5.手术操作代码存在手术操作名称不能为空
     * 6.手术操作重复
     * 7.手术操作代码存在术者为空
     * 8.手术操作名称填写不正确
     *
     * @param settleListValidateVo 数据
     * @param ruleVo               校验规则
     * @param result               结果
     */
    private static void validateLG007(SettleListValidateVo settleListValidateVo,
                                      SettleListRuleVo ruleVo,
                                      SettleListHandlerVo result) {
        List<SomOprnOprtInfo> operationList = settleListValidateVo.getBusOperateDiagnosisList();
        if (ValidateUtil.isNotEmpty(operationList)) {
            boolean primaryOperationCodeFlag = true;
            boolean primaryOperationRepeatFlag = false;
            boolean nameExistsCodeNotExists = false;
            boolean codeExistsDateNotExists = false;
            boolean codeExistsNameNotExists = false;
            boolean codeExistsSurgeonNotExists = false;
            String primaryOperationCode = operationList.get(0).getC35c();
            Set<String> operationSet = new HashSet<>();
            for (SomOprnOprtInfo oprt : operationList) {
                if (oprt.getSeq() == 0) {
                    primaryOperationCodeFlag = false;
                }

                if (ValidateUtil.isNotEmpty(oprt.getC35c())) {
                    if (oprt.getSeq() != 0 &&
                            ValidateUtil.isNotEmpty(primaryOperationCode) &&
                            primaryOperationCode.equals(oprt.getC35c())) {
                        primaryOperationRepeatFlag = true;
                    }

                    if (ValidateUtil.isEmpty(oprt.getOprn_oprt_date())) {
                        codeExistsDateNotExists = true;
                    }

                    if (ValidateUtil.isEmpty(oprt.getC36n())) {
                        codeExistsNameNotExists = true;
                    }

                    if (ValidateUtil.isEmpty(oprt.getC39c())) {
                        codeExistsSurgeonNotExists = true;
                    }
                }

                if (ValidateUtil.isNotEmpty(oprt.getC36n()) && ValidateUtil.isEmpty(oprt.getC35c())) {
                    nameExistsCodeNotExists = true;
                }

                operationSet.add(oprt.getC35c());
            }

            if (primaryOperationCodeFlag) {
                addError(result, ruleVo, settleListValidateVo, null, ERROR_TYPE_LG_OPERATION, null, VALIDATE_TYPE_3, "缺少主手术");
            }

            if (primaryOperationRepeatFlag) {
                addError(result, ruleVo, settleListValidateVo, null, ERROR_TYPE_LG_OPERATION, null, VALIDATE_TYPE_3, "主手术重复");
            }

            if (operationSet.size() != operationList.size() && !primaryOperationRepeatFlag) {
                addError(result, ruleVo, settleListValidateVo, null, ERROR_TYPE_LG_OPERATION, null, VALIDATE_TYPE_3, "手术操作重复");
            }

            if (nameExistsCodeNotExists) {
                addError(result, ruleVo, settleListValidateVo, null, ERROR_TYPE_LG_OPERATION, null, VALIDATE_TYPE_3, "手术操作名称存在手术操作代码不能为空");
            }

            if (codeExistsDateNotExists) {
//                addError(result, ruleVo, settleListValidateVo, null, ERROR_TYPE_LG_OPERATION, null, VALIDATE_TYPE_3, "手术操作代码存在手术操作日期不能为空");
            }

            if (codeExistsNameNotExists) {
                addError(result, ruleVo, settleListValidateVo, null, ERROR_TYPE_LG_OPERATION, null, VALIDATE_TYPE_3, "手术操作代码存在手术操作名称不能为空");
            }

            if (codeExistsSurgeonNotExists) {
                addError(result, ruleVo, settleListValidateVo, null, ERROR_TYPE_LG_OPERATION, null, VALIDATE_TYPE_3, "手术操作代码存在术者为空");
            }
        }
    }

    /**
     * 联系人信息错误
     *
     * @param settleListValidateVo 数据
     * @param ruleVo               校验规则
     * @param result               结果
     */
    private static void validateLG008(SettleListValidateVo settleListValidateVo,
                                      SettleListRuleVo ruleVo,
                                      SettleListHandlerVo result) {
        SomHiInvyBasInfo somHiInvyBasInfo = settleListValidateVo.getSomHiInvyBasInfo();
        if (ValidateUtil.isNotEmpty(somHiInvyBasInfo.getA32())) {
            if (ValidateUtil.isEmpty(somHiInvyBasInfo.getA33c())) {
                String error = "联系人存在，联系人关系不能为空";
                addError(result, ruleVo, settleListValidateVo, null, ERROR_TYPE_LG_CONTACT, null, VALIDATE_TYPE_3, error);
            }
            if (ValidateUtil.isEmpty(somHiInvyBasInfo.getA34())) {
                String error = "联系人存在，联系人地址不能为空";
                addError(result, ruleVo, settleListValidateVo, null, ERROR_TYPE_LG_CONTACT, null, VALIDATE_TYPE_3, error);
            }
            if (ValidateUtil.isEmpty(somHiInvyBasInfo.getA35())) {
                String error = "联系人存在，联系人电话不能为空";
                addError(result, ruleVo, settleListValidateVo, null, ERROR_TYPE_LG_CONTACT, null, VALIDATE_TYPE_3, error);
            }
        }
    }


    /**
     * 判断不合理编码
     *
     * @param code 编码，要求传入编码不为null
     * @return
     */
    private static boolean judgeUnreasonableCode(String code) {
        return code.length() >= 1 && UNREASONABLE_LIST.contains(code.substring(0, 1));
    }

    /**
     * 判断诊断编码出现P10～P15
     *
     * @param somHiInvyBasInfo 数据
     * @param code             编码
     * @param type             1：入院日期减出生日期必须小于365天
     *                         2：（年龄不足1周岁的）天龄必须小于365天
     * @return
     */
    private static boolean judgeP10TP15Code(SomHiInvyBasInfo somHiInvyBasInfo,
                                            String code,
                                            String type) {
        // 出生日期
        String brdy = somHiInvyBasInfo.getA13();
        // 入院日期
        String inHosTime = somHiInvyBasInfo.getB12();
        // 天龄（不足一周岁的年龄）
        Integer ageDays = somHiInvyBasInfo.getA16();
        // 年龄
        Integer age = somHiInvyBasInfo.getA14();
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        if (ValidateUtil.isNotEmpty(inHosTime) &&
                ValidateUtil.isNotEmpty(brdy) && DrgConst.TYPE_1.equals(type) && judgeDatePattern(inHosTime, brdy)) {
            try {
                int calcDays = (int) ((format.parse(inHosTime).getTime() - (format.parse(brdy).getTime())) / 24 / 60 / 60 / 1000);
                if (code.length() >= 3 && P10TP15_LIST.contains(code.substring(0, 3)) && calcDays >= 365) {
                    return true;
                }
            } catch (ParseException e) {
                return false;
            }
        }

        if (!ValidateUtil.isEmpty(ageDays) && DrgConst.TYPE_2.equals(type) &&
                (ValidateUtil.isEmpty(age) || age == 0 || age < 1) &&
                code.length() >= 3 && P10TP15_LIST.contains(code.substring(0, 3)) && ageDays >= 365) {
            return true;
        }
        return false;
    }

    /**
     * 判断性别与编码不符
     *
     * @param somHiInvyBasInfo 数据
     * @param code             编码
     * @return
     */
    private static boolean judgeSexCode(SomHiInvyBasInfo somHiInvyBasInfo,
                                        String code) {
        // 1男 2女
        String gend = somHiInvyBasInfo.getA12c();
        return (code.length() >= 3 && SEX_CODE_MAP.get(gend) != null && SEX_CODE_MAP.get(gend).contains(code.substring(0, 3))) ||
                (code.length() >= 5 && SEX_CODE_MAP.get(gend) != null && SEX_CODE_MAP.get(gend).contains(code.substring(0, 5)));
    }

    /**
     * 匹配日期格式
     *
     * @param updt_date 日期
     * @return
     */
    private static boolean judgeDatePattern(String... updt_date) {
        Pattern yyyyMMddHHmmssPattern = Pattern.compile("([0-9]{3}[1-9]|[0-9]{2}[1-9][0-9]|[0-9][1-9][0-9]{2}|[1-9][0-9]{3})-(((0[13578]|1[02])-(0[1-9]|[12][0-9]|3[01]))|((0[469]|11)-(0[1-9]|[12][0-9]|30))|(02-(0[1-9]|[1][0-9]|2[0-8])))([ ])([0-1]?[0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])");
        Pattern yyyyMMddPattern = Pattern.compile("([0-9]{3}[1-9]|[0-9]{2}[1-9][0-9]|[0-9][1-9][0-9]{2}|[1-9][0-9]{3})-(((0[13578]|1[02])-(0[1-9]|[12][0-9]|3[01]))|((0[469]|11)-(0[1-9]|[12][0-9]|30))|(02-(0[1-9]|[1][0-9]|2[0-8])))");
        for (String d : updt_date) {
            if (!yyyyMMddHHmmssPattern.matcher(d).matches() &&
                    !yyyyMMddPattern.matcher(d).matches()) {
                return false;
            }
        }
        return true;
    }

    /**
     * 设置转换数据
     *
     * @param settleListParams 参数
     * @param hospitalId       医疗机构ID
     */
    public static void setConvertData(Map<String, Object> settleListParams, String hospitalId) {
        Map<String, Map<String, Object>> doctorMap = (Map<String, Map<String, Object>>) settleListParams.get(KEY_DOCTOR_CODE);
        Map<String, Map<String, Object>> nurseMap = (Map<String, Map<String, Object>>) settleListParams.get(KEY_NURSE_CODE);
        Map<String, Map<String, Object>> transfusionMap = (Map<String, Map<String, Object>>) settleListParams.get(KEY_TRANSFUSION_CODE);
        Map<String, Object> dm = ValidateUtil.isNotEmpty(hospitalId) ? doctorMap.get(hospitalId) : (doctorMap.keySet().toArray().length > 0 ? doctorMap.get(doctorMap.keySet().toArray()[0]) : new HashMap<>());
        Map<String, Object> nm = ValidateUtil.isNotEmpty(hospitalId) ? nurseMap.get(hospitalId) : (nurseMap.keySet().toArray().length > 0 ? nurseMap.get(nurseMap.keySet().toArray()[0]) : new HashMap<>());
        Map<String, Object> sx = ValidateUtil.isNotEmpty(hospitalId) ? transfusionMap.get(hospitalId) : (transfusionMap.keySet().toArray().length > 0 ? transfusionMap.get(transfusionMap.keySet().toArray()[0]) : new HashMap<>());
        Map<String, Object> dnm = new HashMap<>();
        if (ValidateUtil.isEmpty(dm)) {
            dm = new HashMap<>();
        }
        if (ValidateUtil.isEmpty(nm)) {
            nm = new HashMap<>();
        }
        if (ValidateUtil.isEmpty(sx)) {
            sx = new HashMap<>();
        }
        dnm.putAll(nm);
        dnm.putAll(dm);
        CONVERT_MAP.put(CONVERT_DOCTOR_CODE, dm);
        CONVERT_MAP.put(CONVERT_NURSE_CODE, nm);
        CONVERT_MAP.put(CONVERT_TRANSFUSION_CODE, sx);
        CONVERT_MAP.put(CONVERT_DOCTOR_AND_NURSE_CODE, dnm);
    }

    /**
     * 通过前缀获取工具类值
     *
     * @param prefix
     * @return
     * @throws IllegalAccessException
     */
    private static List<String> getCollectByPrefix(String prefix) throws IllegalAccessException {
        List<String> list = new ArrayList<>();
        Field[] fields = SettleListValidateUtil.class.getDeclaredFields();
        for (Field fld : fields) {
            if (fld.getName().startsWith(prefix)) {
                list.add(fld.get(SettleListValidateUtil.class).toString());
            }
        }
        return list;
    }

    /**
     * 参数为空抛出异常
     *
     * @param settleListParams
     */
    private static void throwNull(Map<String, Object> settleListParams) {
        for (String key : KEYS_MAP.keySet()) {
            if (settleListParams.get(key) == null) {
                throw new AppException(KEYS_MAP.get(key));
            }
        }
    }


    /**
     * DRG医保排除规则
     *
     * @param settleListValidateVo 数据
     * @param ruleVo               规则
     * @param result               结果
     * @param settleListParams     参数
     */
    private static void validateDrgMedicalExclude(SettleListValidateVo settleListValidateVo,
                                                  SettleListRuleVo ruleVo,
                                                  SettleListHandlerVo result,
                                                  Map<String, Object> settleListParams) {
        SomHiInvyBasInfo somHiInvyBasInfo = settleListValidateVo.getSomHiInvyBasInfo();
        // 实际住院天数
        String b20 = somHiInvyBasInfo.getB20();
        // 总费用
        Double d01 = somHiInvyBasInfo.getD01();
        if (ValidateUtil.isEmpty(b20) || ValidateUtil.isEmpty(d01)) {
            return;
        }
        boolean addErrorFlag = false;
        switch (ruleVo.getFldCode()) {
            case SLVT201:
                if (Integer.parseInt(b20) > 60 && d01 >= 5) {
                    addErrorFlag = true;
                }
                break;
            case SLVT202:
                if (Integer.parseInt(b20) > 60 && d01 < 5) {
                    addErrorFlag = true;
                }
                break;
            case SLVT203:
                if (d01 < 5 && Integer.parseInt(b20) <= 60) {
                    addErrorFlag = true;
                }
                break;
        }
        if (addErrorFlag) {
            ruleVo.setAppend(true);
            addError(result, ruleVo, settleListValidateVo, null, ERROR_TYPE_DRGMC, null, VALIDATE_TYPE_2, ruleVo.getVerfRule());
        }
    }


    /**
     * 校验费用明细错编漏编
     *
     * @param settleListValidateVo 数据
     * @param ruleVo               规则
     * @param result               结果
     * @param settleListParams     参数
     */
    private static void validateSLVT010(SettleListValidateVo settleListValidateVo,
                                        SettleListRuleVo ruleVo,
                                        SettleListHandlerVo result,
                                        Map<String, Object> settleListParams) {
        List<MedicareTollVo> medicareTollVos = (List<MedicareTollVo>) settleListParams.get(MEDICARE_TOLL_DATA);
        List<BusFeeBreakDown> busFeeBreakDownList = settleListValidateVo.getBusFeeBreakDownList();
        List<SomOprnOprtInfo> busOperateDiagnosisList = settleListValidateVo.getBusOperateDiagnosisList();
        List<String> sameFee = new ArrayList<>();

        if (ValidateUtil.isNotEmpty(medicareTollVos) && ValidateUtil.isNotEmpty(busFeeBreakDownList)) {
            for (BusFeeBreakDown busFeeBreakDown :
                    busFeeBreakDownList) {
                if (!sameFee.contains(busFeeBreakDown.getMedListCodg())) {
                    // 获取存在的收费项目
                    List<MedicareTollVo> existFeeVos = new ArrayList<>();
                    boolean existFlag = true;// 用于判断是否为疑似错编漏编
                    String missCode = "";// 错编漏编名称
                    if (ValidateUtil.isNotEmpty(busFeeBreakDown.getMedListCodg())) {
                        for (MedicareTollVo medicareTollVo :
                                medicareTollVos) {
                            if (busFeeBreakDown.getMedListCodg().equals(medicareTollVo.getSfxCode())) {
                                // 判断是否入组
                                // .....
                                existFeeVos.add(medicareTollVo);
                            }
                        }
                    }
                    // 判断患者手术是否包含在收费项目中
                    if (ValidateUtil.isNotEmpty(existFeeVos) && ValidateUtil.isNotEmpty(busOperateDiagnosisList)) {
                        for (MedicareTollVo existFeeVo :
                                existFeeVos) {
                            for (SomOprnOprtInfo operateVo :
                                    busOperateDiagnosisList) {
                                if (existFeeVo.getICD9().equals(operateVo.getC35c())) {
                                    existFlag = false;
                                }
                            }
                            missCode += "{" + existFeeVo.getICD9() + ": " + existFeeVo.getICD9NAME() + "}";
                        }
                    }
                    if (existFlag && missCode != "") {
                        addError(result, ruleVo, settleListValidateVo, null, ERROR_TYPE_MISSCODE, null, VALIDATE_TYPE_2,
                                "疑似漏编错编：[" + missCode + "]");
                    }
                    sameFee.add(busFeeBreakDown.getMedListCodg());
                }
            }
        }
    }

    /**
     * 校验诊断编码中是否存在联合编码冲突
     *
     * @param settleListValidateVo 数据
     * @param ruleVo               规则
     * @param result               结果
     * @param settleListParams     参数
     */
    private static void validateSLVT011(SettleListValidateVo settleListValidateVo,
                                        SettleListRuleVo ruleVo,
                                        SettleListHandlerVo result,
                                        Map<String, Object> settleListParams) {
        //获取联合编码配置
        List<MergeDisCodeVo> mergeCodeHashMapList = (List<MergeDisCodeVo>) settleListParams.get(KEY_MERGE_CODE);
        Map<String, Object> mergeCodeHashMap = mergeCodeHashMapList.stream().collect(Collectors.toMap(MergeDisCodeVo::getMerge_dis_code, MergeDisCodeVo::getMerge_dis_name));
        //获取诊断信息并组装成array
        List<BusDiseaseDiagnosisTrim> busDiseaseDiagnosisTrimList = settleListValidateVo.getBusDiseaseDiagnosisTrimsList();
        List<String> disCodeList = new ArrayList<>();
        busDiseaseDiagnosisTrimList.stream().forEach(discode -> {
            if (ValidateUtil.isNotEmpty(discode.getC06c1())) {
                disCodeList.add(discode.getC06c1().toLowerCase());
            }
        });
        StringBuffer errorMsgBuffer = new StringBuffer();
        if (!ValidateUtil.isEmpty(mergeCodeHashMap)) {
            for (Map.Entry<String, Object> entry : mergeCodeHashMap.entrySet()) {
                List<String> mergeCodeList = Arrays.asList(entry.getKey().split(MERGE_DELIMITER)).stream().filter(discode -> ValidateUtil.isNotEmpty(discode)).map(String::toLowerCase).collect(Collectors.toList());
                List<String> mergeNameList = Arrays.asList(entry.getValue().toString().split(MERGE_DELIMITER)).stream().filter(disname -> ValidateUtil.isNotEmpty(disname)).collect(Collectors.toList());
                //判断是否存在存在联合编排冲突 0-倒数第二个元素，倒数第一个元素为应该使用的联合编码
                if (disCodeList.containsAll(mergeCodeList.subList(0,mergeCodeList.size()-1))) {
                    errorMsgBuffer.append("诊断编码" + mergeCodeList.get(0).toUpperCase() + "-" + mergeNameList.get(0));
                    errorMsgBuffer.append("与诊断编码");
                    for (int i = 0; i < mergeCodeList.size() - 1; i++) {
                        if (i != 0) {
                            errorMsgBuffer.append(mergeCodeList.get(i).toUpperCase() + "-" + mergeNameList.get(i));
                            errorMsgBuffer.append("、");
                        }
                    }
                    errorMsgBuffer.append("存在联合诊断编码" + mergeCodeList.get(mergeCodeList.size() - 1).toUpperCase() + "-" + mergeNameList.get(mergeCodeList.size() - 1));
                    // 获取联合编码情况组装提示文字；诊断编码I34.000与诊断编码I07.100、存在联合诊断编码I08.103;
                    addError(result, ruleVo, settleListValidateVo, null, ERROR_TYPE_MERGECODE, null, VALIDATE_TYPE_2,
                            "存在联合编码异常：[" + errorMsgBuffer.toString() + "]");
                }
            }
        }
    }


    /**
     * DRG非分组编码
     *
     * @param settleListValidateVo 数据
     * @param ruleVo               规则
     * @param result               结果
     * @param settleListParams     参数
     */
    private static void validateSLVT102(SettleListValidateVo settleListValidateVo,
                                        SettleListRuleVo ruleVo,
                                        SettleListHandlerVo result,
                                        Map<String, Object> settleListParams) {
        validateCannotPrimaryDisCode(settleListValidateVo, ruleVo, result, settleListParams, KEY_NOT_GROUP, ERROR_TYPE_DRGNC, "是非分组方案编码,");
    }

    /**
     * 校验不能作为主诊编码
     *
     * @param settleListValidateVo
     * @param ruleVo
     * @param result
     * @param settleListParams
     * @param key
     * @param errType
     * @param errDscr
     */
    private static void validateCannotPrimaryDisCode(SettleListValidateVo settleListValidateVo,
                                                     SettleListRuleVo ruleVo,
                                                     SettleListHandlerVo result,
                                                     Map<String, Object> settleListParams,
                                                     String key,
                                                     String errType,
                                                     String errDscr) {
        Map<String, Object> map = (Map<String, Object>) settleListParams.get(key);
        List<BusDiseaseDiagnosisTrim> busDiseaseDiagnosisTrimsList = settleListValidateVo.getBusDiseaseDiagnosisTrimsList();
        if (ValidateUtil.isNotEmpty(busDiseaseDiagnosisTrimsList)) {
            busDiseaseDiagnosisTrimsList.forEach(somDiag -> {
                // 主诊
                if (checkDisCode(somDiag) &&
                        !ValidateUtil.isEmpty(somDiag.getSeq()) &&
                        somDiag.getSeq() == 0 && map.get(somDiag.getC06c1()) != null) {
                    addError(result, ruleVo, settleListValidateVo, null, errType, null, VALIDATE_TYPE_2,
                            getDisErrorPrefix((somDiag.getSeq() + 1)) +
                                    "[" + somDiag.getC06c1() + "]" + errDscr + map.get(somDiag.getC06c1()));
                }
            });
        }
    }


    /**
     * DRG诊断灰码
     *
     * @param settleListValidateVo 数据
     * @param ruleVo               规则
     * @param result               结果
     * @param settleListParams     参数
     */
    private static void validateSLVT101(SettleListValidateVo settleListValidateVo,
                                        SettleListRuleVo ruleVo,
                                        SettleListHandlerVo result,
                                        Map<String, Object> settleListParams) {
        Map<String, Object> drgGrayCodeMap = (Map<String, Object>) settleListParams.get(KEY_DRG_GRAY_CODE);
        List<BusDiseaseDiagnosisTrim> busDiseaseDiagnosisTrimsList = settleListValidateVo.getBusDiseaseDiagnosisTrimsList();
        if (ValidateUtil.isNotEmpty(busDiseaseDiagnosisTrimsList)) {
            busDiseaseDiagnosisTrimsList.forEach(somDiag -> {
                // 主诊
                if (checkDisCode(somDiag) &&
                        !ValidateUtil.isEmpty(somDiag.getSeq()) &&
                        somDiag.getSeq() == 0 && drgGrayCodeMap.get(somDiag.getC06c1()) != null) {
                    addError(result, ruleVo, settleListValidateVo, null, ERROR_TYPE_DRGGC, null, VALIDATE_TYPE_2,
                            getDisErrorPrefix((somDiag.getSeq() + 1)) +
                                    "[" + somDiag.getC06c1() + "]不能进入CHS-DRG分组,建议更换为拓展码：" + drgGrayCodeMap.get(somDiag.getC06c1()));
                }
            });
        }
    }

    /**
     * 校验使用医保2.0手术灰码
     *
     * @param settleListValidateVo 数据
     * @param ruleVo               规则
     * @param result               结果
     * @param settleListParams     参数
     */
    private static void validateSLVT009(SettleListValidateVo settleListValidateVo,
                                        SettleListRuleVo ruleVo,
                                        SettleListHandlerVo result,
                                        Map<String, Object> settleListParams) {
        List<SomOprnOprtInfo> busOperateDiagnosisList = settleListValidateVo.getBusOperateDiagnosisList();
        if (ValidateUtil.isNotEmpty(busOperateDiagnosisList)) {
            Map<String, Object> grayCodeMap = (Map<String, Object>) settleListParams.get(KEY_ICD9_GRAY_CODE);
            busOperateDiagnosisList.forEach(somOprnOprtInfo -> {
                if (checkOpeCode(somOprnOprtInfo) && grayCodeMap.get(somOprnOprtInfo.getC35c()) != null) {
                    String msg = "【" + somOprnOprtInfo.getC35c() + "/" + somOprnOprtInfo.getC36n() + "】为灰码,建议更换为拓展码：" + grayCodeMap.get(somOprnOprtInfo.getC35c());
                    if (!ValidateUtil.isEmpty(grayCodeMap.get(somOprnOprtInfo.getC35c())) &&
                            "[]".equals(grayCodeMap.get(somOprnOprtInfo.getC35c()))) {
                        msg = "【" + somOprnOprtInfo.getC35c() + "】为灰码,建议更换其他编码";
                    }
                    addError(result, ruleVo, settleListValidateVo, null, ERROR_TYPE_OPE2GC, null, VALIDATE_TYPE_2,
                            getOpeErrorPrefix((somOprnOprtInfo.getSeq() + 1)) + msg);
                }
            });
        }
    }

    /**
     * 校验使用医保2.0灰码
     *
     * @param settleListValidateVo 数据
     * @param ruleVo               规则
     * @param result               结果
     * @param settleListParams     参数
     */
    private static void validateSLVT008(SettleListValidateVo settleListValidateVo,
                                        SettleListRuleVo ruleVo,
                                        SettleListHandlerVo result,
                                        Map<String, Object> settleListParams) {
        Map<String, Object> grayCodeMap = (Map<String, Object>) settleListParams.get(KEY_ICD10_GRAY_CODE);
        List<BusDiseaseDiagnosisTrim> busDiseaseDiagnosisTrimsList = settleListValidateVo.getBusDiseaseDiagnosisTrimsList();
        if (ValidateUtil.isNotEmpty(busDiseaseDiagnosisTrimsList)) {
            busDiseaseDiagnosisTrimsList.forEach(somDiag -> {
                // 主诊
                if (checkDisCode(somDiag) && grayCodeMap.get(somDiag.getC06c1()) != null) {
                    addError(result, ruleVo, settleListValidateVo, null, ERROR_TYPE_DIS2GC, null, VALIDATE_TYPE_2,
                            getDisErrorPrefix((somDiag.getSeq() + 1)) +
                                    "【" + somDiag.getC06c1() + "/" + somDiag.getC07n1() + "】为灰码,建议更换为拓展码：" + grayCodeMap.get(somDiag.getC06c1()));
                }
            });
        }
    }

    /**
     * 校验使用残余类目编码
     *
     * @param settleListValidateVo 数据
     * @param ruleVo               规则
     * @param result               结果
     * @param settleListParams     参数
     */
    private static void validateSLVT007(SettleListValidateVo settleListValidateVo,
                                        SettleListRuleVo ruleVo,
                                        SettleListHandlerVo result,
                                        Map<String, Object> settleListParams) {
        validateCannotPrimaryDisCode(settleListValidateVo, ruleVo, result, settleListParams, KEY_REMNANT, ERROR_TYPE_REMNANT, "是残余类目编码,");
    }

    /**
     * 校验主要诊断编码不恰当
     *
     * @param settleListValidateVo 数据
     * @param ruleVo               规则
     * @param result               结果
     * @param settleListParams     参数
     */
    private static void validateSLVT006(SettleListValidateVo settleListValidateVo,
                                        SettleListRuleVo ruleVo,
                                        SettleListHandlerVo result,
                                        Map<String, Object> settleListParams) {
        validateCannotPrimaryDisCode(settleListValidateVo, ruleVo, result, settleListParams, KEY_INAPPROPRIATE, ERROR_TYPE_INAPPROPRIATE, "使用不恰当,");
    }

    /**
     * 校验不能作为主诊编码(外因, 病理)
     *
     * @param settleListValidateVo 数据
     * @param ruleVo               规则
     * @param result               结果
     * @param settleListParams     参数
     */
    private static void validateSLVT005(SettleListValidateVo settleListValidateVo,
                                        SettleListRuleVo ruleVo,
                                        SettleListHandlerVo result,
                                        Map<String, Object> settleListParams) {
        Map<String, Object> disMap = (Map<String, Object>) settleListParams.get(KEY_DIS_SECTION);
        // 外因
        Map<String, Object> externalCauseDisMap = (Map<String, Object>) disMap.get(DIS_SECTION_TYPE_4);
        // 病理
        Map<String, Object> pathologyDisMap = (Map<String, Object>) disMap.get(DIS_SECTION_TYPE_5);
        Map<String, Object> allDisMap = new HashMap<String, Object>() {
            {
                putAll(externalCauseDisMap);
                putAll(pathologyDisMap);
            }
        };
        List<BusDiseaseDiagnosisTrim> busDiseaseDiagnosisTrimsList = settleListValidateVo.getBusDiseaseDiagnosisTrimsList();
        if (ValidateUtil.isNotEmpty(busDiseaseDiagnosisTrimsList)) {
            busDiseaseDiagnosisTrimsList.forEach(somDiag -> {
                // 主诊
                if (checkDisCode(somDiag) &&
                        !ValidateUtil.isEmpty(somDiag.getSeq()) &&
                        somDiag.getSeq() == 0) {
                    if (somDiag.getC06c1().length() < 3) {
                        return;
                    }
                    Object str1 = allDisMap.get(somDiag.getC06c1().substring(0, 3).toUpperCase());
                    Object str2 = allDisMap.get(somDiag.getC06c1().substring(0, 5).toUpperCase());
                    Object str3 = allDisMap.get(somDiag.getC06c1());
                    if (str1 != null || str2 != null || str3 != null) {
                        addError(result, ruleVo, settleListValidateVo, null, ERROR_TYPE_NPDIS, null, VALIDATE_TYPE_2,
                                getDisErrorPrefix((somDiag.getSeq() + 1)) + "[" + somDiag.getC06c1() + "]" +
                                        (str1 == null ? str2 == null ? str3 : str2 : str1));
                    }
                }
            });
        }
    }

    /**
     * 校验校验年龄与编码不匹配
     *
     * @param settleListValidateVo 数据
     * @param ruleVo               规则
     * @param result               结果
     * @param settleListParams     参数
     */
    private static void validateSLVT004(SettleListValidateVo settleListValidateVo,
                                        SettleListRuleVo ruleVo,
                                        SettleListHandlerVo result,
                                        Map<String, Object> settleListParams) {
        Map<String, Object> disMap = (Map<String, Object>) settleListParams.get(KEY_DIS_SECTION);
        Map<String, Object> ageDisMap = (Map<String, Object>) disMap.get(DIS_SECTION_TYPE_3);
        // 年龄不足一周岁，天
        Integer a16 = settleListValidateVo.getSomHiInvyBasInfo().getA16();
        // 年龄
        Integer a14 = settleListValidateVo.getSomHiInvyBasInfo().getA14();
        List<BusDiseaseDiagnosisTrim> busDiseaseDiagnosisTrimsList = settleListValidateVo.getBusDiseaseDiagnosisTrimsList();
        if (ValidateUtil.isNotEmpty(busDiseaseDiagnosisTrimsList)) {
            busDiseaseDiagnosisTrimsList.forEach(somDiag -> {
                // 主诊
                if (checkDisCode(somDiag) &&
                        !ValidateUtil.isEmpty(somDiag.getSeq()) &&
                        !ValidateUtil.isEmpty(a16) && somDiag.getSeq() == 0 && a16 == 0 && a14 > 0) {
                    disSectionErrorJudge(ageDisMap, somDiag, settleListValidateVo, ruleVo, result, ERROR_TYPE_AGENM);
                }
            });
        }
    }

    /**
     * 校验校验性别与编码不匹配
     *
     * @param settleListValidateVo 数据
     * @param ruleVo               规则
     * @param result               结果
     * @param settleListParams     参数
     */
    private static void validateSLVT003(SettleListValidateVo settleListValidateVo,
                                        SettleListRuleVo ruleVo,
                                        SettleListHandlerVo result,
                                        Map<String, Object> settleListParams) {
        Map<String, Object> disMap = (Map<String, Object>) settleListParams.get(KEY_DIS_SECTION);
        Map<String, Object> sexDisMap;
        List<BusDiseaseDiagnosisTrim> busDiseaseDiagnosisTrimsList = settleListValidateVo.getBusDiseaseDiagnosisTrimsList();
        SomHiInvyBasInfo somHiInvyBasInfo = settleListValidateVo.getSomHiInvyBasInfo();
        // 1男 2女
        String gend = somHiInvyBasInfo.getA12c();
        if (DrgConst.MAN.equals(gend)) {
            sexDisMap = (Map<String, Object>) disMap.get(DIS_SECTION_TYPE_1);
        } else {
            sexDisMap = (Map<String, Object>) disMap.get(DIS_SECTION_TYPE_2);
        }
        if (ValidateUtil.isNotEmpty(busDiseaseDiagnosisTrimsList)) {
            busDiseaseDiagnosisTrimsList.forEach(somDiag -> {
                if (checkDisCode(somDiag)) {
                    disSectionErrorJudge(sexDisMap, somDiag, settleListValidateVo, ruleVo, result, ERROR_TYPE_SEXNM);
                }
            });
        }
    }

    /**
     * 诊断错误判断
     *
     * @param disSectionMap
     * @param somDiag
     * @param settleListValidateVo
     * @param ruleVo
     * @param result
     * @param errType
     */
    private static void disSectionErrorJudge(Map<String, Object> disSectionMap,
                                             BusDiseaseDiagnosisTrim somDiag,
                                             SettleListValidateVo settleListValidateVo,
                                             SettleListRuleVo ruleVo,
                                             SettleListHandlerVo result,
                                             String errType) {
        if (somDiag.getC06c1().length() < 3) {
            return;

        }
        Object str1 = disSectionMap.get(somDiag.getC06c1().substring(0, 3).toUpperCase());
        Object str2 = disSectionMap.get(somDiag.getC06c1().substring(0, 5).toUpperCase());
        if (str1 != null || str2 != null) {
            addError(result, ruleVo, settleListValidateVo, null, errType, null, VALIDATE_TYPE_2,
                    getDisErrorPrefix((somDiag.getSeq() + 1)) + "[" + somDiag.getC06c1() + "]" + (str1 == null ? str2 : str1));
        }
    }

    /**
     * 校验诊断是否为医保2.0编码
     *
     * @param settleListValidateVo 数据
     * @param ruleVo               规则
     * @param result               结果
     * @param settleListParams     参数
     */
    private static void validateSLVT002(SettleListValidateVo settleListValidateVo,
                                        SettleListRuleVo ruleVo,
                                        SettleListHandlerVo result,
                                        Map<String, Object> settleListParams) {
        List<BusDiseaseDiagnosisTrim> busDiseaseDiagnosisTrimsList = settleListValidateVo.getBusDiseaseDiagnosisTrimsList();
        if (ValidateUtil.isNotEmpty(busDiseaseDiagnosisTrimsList)) {
            Map<String, Object> icd10Map = (Map<String, Object>) settleListParams.get(KEY_ICD10);
            busDiseaseDiagnosisTrimsList.forEach(somDiag -> {
                if (checkDisCode(somDiag) &&
                        icd10Map.get(somDiag.getC06c1()) == null) {
                    addError(result, ruleVo, settleListValidateVo, null, ERROR_TYPE_DISN2, null, VALIDATE_TYPE_2,
                            getDisErrorPrefix((somDiag.getSeq() + 1)) + "[" + somDiag.getC06c1() + "]不是医保2.0编码");
                }
            });
        }
    }

    /**
     * 检查诊断编码
     *
     * @param somDiag
     * @return
     */
    private static boolean checkDisCode(BusDiseaseDiagnosisTrim somDiag) {
        if (ValidateUtil.isNotEmpty(somDiag.getC06c1()) && !NULL_.equals(somDiag.getC06c1())) {
            return true;
        }
        return false;
    }

    /**
     * 检查手术编码
     *
     * @param somOprnOprtInfo
     * @return
     */
    private static boolean checkOpeCode(SomOprnOprtInfo somOprnOprtInfo) {
        if (ValidateUtil.isNotEmpty(somOprnOprtInfo.getC35c()) && !NULL_.equals(somOprnOprtInfo.getC35c())) {
            return true;
        }
        return false;
    }

    /**
     * 获取诊断错误描述前缀
     *
     * @param seq 诊断或手术序号
     * @return
     */
    private static String getDisErrorPrefix(Integer seq) {
        if (seq == 1) {
            return "主要诊断操作编码";
        }
        return "诊断操作编码" + seq;
    }

    /**
     * 获取手术错误描述前缀
     *
     * @param seq 诊断或手术序号
     * @return
     */
    private static String getOpeErrorPrefix(Integer seq) {
        if (seq == 1) {
            return "主要手术操作编码";
        }
        return "手术操作编码" + seq;
    }

    /**
     * 校验手术是否为医保2.0编码
     *
     * @param settleListValidateVo 数据
     * @param ruleVo               规则
     * @param result               结果
     * @param settleListParams     参数
     */
    private static void validateSLVT001(SettleListValidateVo settleListValidateVo,
                                        SettleListRuleVo ruleVo,
                                        SettleListHandlerVo result,
                                        Map<String, Object> settleListParams) {
        List<SomOprnOprtInfo> busOperateDiagnosisList = settleListValidateVo.getBusOperateDiagnosisList();
        if (ValidateUtil.isNotEmpty(busOperateDiagnosisList)) {
            Map<String, Object> icd9Map = (Map<String, Object>) settleListParams.get(KEY_ICD9);
            busOperateDiagnosisList.forEach(somOprnOprtInfo -> {
                if (checkOpeCode(somOprnOprtInfo) && icd9Map.get(somOprnOprtInfo.getC35c()) == null) {
                    addError(result, ruleVo, settleListValidateVo, null, ERROR_TYPE_OPEN2, null, VALIDATE_TYPE_2,
                            getOpeErrorPrefix((somOprnOprtInfo.getSeq() + 1)) + "[" + somOprnOprtInfo.getC35c() + "]不是医保2.0编码");
                }
                if (checkOpeCode(somOprnOprtInfo)) {
                    if (!ValidateUtil.isEmpty(somOprnOprtInfo.getC36n()) && !(icd9Map.get(somOprnOprtInfo.getC35c()) == null)
                            && !icd9Map.get(somOprnOprtInfo.getC35c()).equals(somOprnOprtInfo.getC36n())) {
                        addError(result, ruleVo, settleListValidateVo, null, ERROR_TYPE_OPENAME, null, VALIDATE_TYPE_2,
                                getOpeErrorPrefix((somOprnOprtInfo.getSeq() + 1))
                                        + "[" + somOprnOprtInfo.getC35c() + "]编码名称错误，"
                                        + "正确名称为" + "[" + icd9Map.get(somOprnOprtInfo.getC35c()) + "]");
                    }
                }
            });
        }
    }

    /**
     * 清单校验
     *
     * @param ruleVo                     规则
     * @param settleListValidateVo       数据
     * @param result                     返回结果
     * @param dictMap                    字典
     * @param validateFieldNotFindFields 未找到字段
     */
    private static <T> void validateInfo(List<T> t,
                                         SettleListRuleVo ruleVo,
                                         SettleListValidateVo settleListValidateVo,
                                         SettleListHandlerVo result,
                                         Map<String, List<String>> dictMap,
                                         StringBuffer validateFieldNotFindFields,
                                         String type) throws InvocationTargetException, IllegalAccessException {
        if (type.equals(ruleVo.getType()) && ValidateUtil.isNotEmpty(t)) {
            for (T t1 : t) {
                executeIntegrityValidate(getValue(t1, ruleVo.getFldCode()),
                        ruleVo, settleListValidateVo, result, dictMap, validateFieldNotFindFields, t1);
            }
        }

    }


    /**
     * 执行完整性校验
     * 1、非空
     * 2、长度
     * 3、字典
     * 4、正则
     *
     * @param value                      值
     * @param ruleVo                     规则
     * @param settleListValidateVo       数据
     * @param result                     返回结果
     * @param dictMap                    字典
     * @param validateFieldNotFindFields 未找到字段
     */
    private static void executeIntegrityValidate(Object value,
                                                 SettleListRuleVo ruleVo,
                                                 SettleListValidateVo settleListValidateVo,
                                                 SettleListHandlerVo result,
                                                 Map<String, List<String>> dictMap,
                                                 StringBuffer validateFieldNotFindFields,
                                                 Object t) {
        if (value != null) {

            // 非空校验
            if (!NULL.equals(value) && ValidateUtil.isNotEmpty(value.toString())) {
                // 根据标识放过校验
                if (ValidateUtil.isEmpty(ruleVo.getSkipChkFlag()) || !ruleVo.getSkipChkFlag().equals(value.toString())) {
                    if (ValidateUtil.isNotEmpty(ruleVo.getLength()) && isEnabled(ruleVo.getLength()) &&
                            value.toString().length() > Integer.parseInt(getRuleValue(ruleVo.getLength()))) {
                        // 长度校验
                        addError(result, ruleVo, settleListValidateVo, value, ERROR_TYPE_LENGTH, t, VALIDATE_TYPE_1, null);
                    }

                    if (ValidateUtil.isNotEmpty(ruleVo.getDictCode()) && isEnabled(ruleVo.getDictCode()) &&
                            !dictMap.get(getRuleValue(ruleVo.getDictCode())).contains(value.toString())) {
                        // 字典校验
                        addError(result, ruleVo, settleListValidateVo, value, ERROR_TYPE_DICT, t, VALIDATE_TYPE_1, null);
                    }

                    if (ValidateUtil.isNotEmpty(ruleVo.getChkRegl()) && isEnabled(ruleVo.getChkRegl()) &&
                            !value.toString().matches(getRuleValue(ruleVo.getChkRegl()))) {
                        // 正则校验
                        addError(result, ruleVo, settleListValidateVo, value, ERROR_TYPE_REG, t, VALIDATE_TYPE_1, null);
                    }

                    if (ValidateUtil.isNotEmpty(ruleVo.getConverter()) && isEnabled(ruleVo.getConverter()) &&
                            !ValidateUtil.isEmpty(CONVERT_MAP.get(getRuleValue(ruleVo.getConverter()))) &&
                            !CONVERT_MAP.get(getRuleValue(ruleVo.getConverter())).containsValue(value.toString()) &&
                            ValidateUtil.isEmpty(CONVERT_MAP.get(getRuleValue(ruleVo.getConverter())).get(value.toString().trim()))) {
                        // 转换值
                        addError(result, ruleVo, settleListValidateVo, value, ERROR_TYPE_NULL, t, VALIDATE_TYPE_1, "【" + value + "】未找到对应值");
                    }
                }
            } else {
                // 空值
                addError(result, ruleVo, settleListValidateVo, value, ERROR_TYPE_NULL, t, VALIDATE_TYPE_1, null);
            }
        } else {
            // 校验字段未找到数据字段
            validateFieldNotFindFields.append(ruleVo.getFldCode()).append(ERROR_DESC_SEPARATOR);
        }
    }


    /**
     * 获取规则的值
     *
     * @param val 值 如：测试-Y
     * @return 测试
     */
    public static String getRuleValue(String val) {
        return val.replace(IS_Y, "");
    }

    /**
     * 是否启用
     *
     * @param val
     * @return
     */
    public static boolean isEnabled(String val) {
        if (val.contains(IS_Y)) {
            return true;
        }
        return false;
    }


    /**
     * 新增错误
     *
     * @param result               返回结果
     * @param ruleVo               规则
     * @param settleListValidateVo 数据
     * @param value                当前值
     * @param errType              错误类型
     * @param t
     */
    private static void addError(SettleListHandlerVo result,
                                 SettleListRuleVo ruleVo,
                                 SettleListValidateVo settleListValidateVo,
                                 Object value,
                                 String errType,
                                 Object t,
                                 String type,
                                 String error) {
        try {
            SettleListHandlerVo settleListHandlerVo = result.getErrorDetails().stream().filter(sv -> sv.getIndex().equals(errType)).findFirst().orElse(new SettleListHandlerVo());
            result.setChkStas(VALIDATE_STATE_FAIL);
            if (ValidateUtil.isEmpty(settleListHandlerVo.getErrType())) {
                settleListHandlerVo.setErrType(errType);
                settleListHandlerVo.setSettleListId(settleListValidateVo.getSomHiInvyBasInfo().getId());
            }
            String errorFields, errDscr = "";
            if (VALIDATE_TYPE_1.equals(type)) {
                errorFields = appendError(settleListHandlerVo.getErrorFields(), ruleVo.getFldCode());
                errDscr = appendError(settleListHandlerVo.getErrDscr(), getErrDscr(ruleVo, ruleVo.getChkName(), value, errType, t, error));
            } else {
                errorFields = ruleVo.isAppend() ? appendError(settleListHandlerVo.getErrorFields(), ruleVo.getFldCode()) : ruleVo.getFldCode();
                errDscr = appendError(settleListHandlerVo.getErrDscr(), error);
            }
            settleListHandlerVo.setErrorFields(errorFields);
            settleListHandlerVo.setErrDscr(errDscr);
        } catch (IndexOutOfBoundsException e) {
            throw new AppException("未初始化集合");
        }
    }

    /**
     * 获取错误描述
     *
     * @param ruleVo  规则
     * @param error   错误/字段
     * @param value   当前校验值
     * @param errType 错误描述
     * @param t       数据
     * @param s       传入错误描述
     * @return
     */
    private static String getErrDscr(SettleListRuleVo ruleVo,
                                     String error,
                                     Object value,
                                     String errType,
                                     Object t,
                                     String s) {
        if (t instanceof BusDiseaseDiagnosisTrim) {
            error = error + (((BusDiseaseDiagnosisTrim) t).getSeq() + 1);
        }
        if (t instanceof SomOprnOprtInfo) {
            error = error + (((SomOprnOprtInfo) t).getSeq() + 1);
        }
        if (t instanceof SomSetlInvyScsCutdInfo) {
            error = error + (((SomSetlInvyScsCutdInfo) t).getSeq() + 1);
        }
        if (ValidateUtil.isNotEmpty(s)) {
            return error + s;
        }
        switch (errType) {
            case ERROR_TYPE_NULL:
                return error + ERROR_NULL_FIELD_SUFFIX;
            case ERROR_TYPE_DICT:
                return error + ERROR_DICT_DESC_SUFFIX;
            case ERROR_TYPE_REG:
                return error + ERROR_REG_SUFFIX;
            case ERROR_TYPE_LENGTH:
                return error + ERROR_LENGTH_SUFFIX + "-最大" + getRuleValue(ruleVo.getLength()) + "位,当前" + value.toString().length() + "位";
            default:
                return null;
        }
    }

    /**
     * 获取值
     * 可采用toMap 后get获取值，未测试效率
     *
     * @param t       校验数据
     * @param fldCode 校验字段
     * @return
     * @throws InvocationTargetException
     * @throws IllegalAccessException
     */
    private static <T> Object getValue(T t,
                                       String fldCode) throws InvocationTargetException, IllegalAccessException {
        if (t != null) {
            Method[] methods = t.getClass().getMethods();
            for (Method method : methods) {
                if (method.getName().equals(FIELD_PREFIX + StringUtils.capitalize(fldCode))) {
                    Object value = method.invoke(t, null);
                    return ValidateUtil.isEmpty(value) ? NULL : value;
                }
            }
        }
        return null;
    }

    /**
     * 追加错误
     *
     * @param value 值
     * @param error 错误描述/字段
     */
    private static String appendError(String value,
                                      String error) {
        String addErr = error + ERROR_DESC_SEPARATOR;
        if (ValidateUtil.isNotEmpty(value)) {
            return value + addErr;
        } else {
            return addErr;
        }
    }
}
