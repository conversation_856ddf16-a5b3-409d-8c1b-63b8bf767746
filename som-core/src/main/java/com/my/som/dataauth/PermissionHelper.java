package com.my.som.dataauth;

import cn.hutool.core.util.ReflectUtil;
import com.my.som.common.constant.DrgConst;
import org.apache.ibatis.binding.MapperMethod;
import org.apache.ibatis.cache.CacheKey;
import org.apache.ibatis.executor.Executor;
import org.apache.ibatis.executor.statement.PreparedStatementHandler;
import org.apache.ibatis.executor.statement.StatementHandler;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.plugin.*;
import org.apache.ibatis.session.ResultHandler;
import org.apache.ibatis.session.RowBounds;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.sql.Connection;
import java.sql.Statement;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Properties;

/**
 * @author: zyd
 * @Date 2021/7/15 3:25 下午
 * @Version 1.0
 * @Description: sql拦截器，拦截指定需要数据权限控制功能
 */
//@Component
@Intercepts({
        @Signature(type = StatementHandler.class, method = "query", args = {Statement.class, ResultHandler.class})
})
public class PermissionHelper implements Interceptor {

    Logger logger =  LoggerFactory.getLogger(PermissionHelper.class);

    private final String SQL_SKIP_COUNT_0 = "SELECT count(0) FROM";
    private final String SQL_SKIP_COUNT_1 = "SELECT count(1) FROM";
    private final String SQL_SKIP_TABLE_COUNT = "table_count";
    private final String PREFIX = "SELECT som_table.* FROM (";
    private final String SUFFIX = ") som_table";

    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        Object result = null;
        BaseVo baseVo = null;
        try {
            if (invocation.getTarget() instanceof StatementHandler) {
                StatementHandler ps =  (StatementHandler)invocation.getTarget();
                BoundSql boundSql = ps.getBoundSql();
                Object parameterObject = boundSql.getParameterObject();

                // 参数直接是 baseVo的
                if (parameterObject instanceof BaseVo) {
                    baseVo = (BaseVo) parameterObject;
                }

                // 参数不止一个时判断是否有一个参数是 baseVo
                if (parameterObject instanceof Map) {
                    Map map = (Map) parameterObject;
                    Object obj = null;
                    for (Object key : map.keySet()) {
                        Object o = map.get(key);
                        if (o instanceof BaseVo) {
                            obj = o;
                            break;
                        }
                    }
                    if (obj != null) {
                        baseVo = (BaseVo) obj;
                    }
                }

                if (baseVo != null) {
                    if (baseVo.isPermissionControl()) {
                        String sql = boundSql.getSql();
                        // 排除PageHelper分页情况
                        if ((sql.startsWith(SQL_SKIP_COUNT_0) || sql.startsWith(SQL_SKIP_COUNT_1)) && sql.endsWith(SQL_SKIP_TABLE_COUNT)) {
                            return invocation.proceed();
                        }
                        String userRoleNames = baseVo.getSysUserBase().getUserRoleNames();
                        //  部门级权限
                        if (userRoleNames.contains(DrgConst.PERMISSION_DEPT) && !userRoleNames.contains(DrgConst.PERMISSION_HOSPITAL)) {
                            // 拼接sql
                            String dept_code = baseVo.getSysUserBase().getB16c();
                            String where_dept = " WHERE dept_code ='" + dept_code + "'";
                            sql = PREFIX + sql + SUFFIX + where_dept;
                            logger.info(sql);
                            // 替换sql
//                            ReflectUtil.setFieldValue(boundSql, "sql", sql);
                        }
                    }
                }
            }
            result = invocation.proceed();
        } catch (Exception e){
            logger.error("调用sql拦截器出错：{}", e.getMessage());
            result = invocation.proceed();
        }
        return result;
    }

    @Override
    public Object plugin(Object target) {
        return Plugin.wrap(target, this);
    }

    @Override
    public void setProperties(Properties properties) {

    }
}
