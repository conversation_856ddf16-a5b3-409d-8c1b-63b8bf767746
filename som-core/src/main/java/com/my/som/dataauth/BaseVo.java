package com.my.som.dataauth;

import com.my.som.common.vo.SysUserBase;
import lombok.Getter;
import lombok.Setter;

/**
 * @author: zyd
 * @Date 2021/7/15 4:38 下午
 * @Version 1.0
 * @Description:
 */
@Getter
@Setter
public class BaseVo {

    /** 是否进行数据权限控制 */
    private boolean permissionControl;

    /** 是否是分页 */
    private boolean pageHelper;

    /** 用户信息 */
    private SysUserBase sysUserBase;

    private String enableSeAns;



    /**
     * 设置是否进行数据控制
     * @param permissionControl 是否控制 true：是 false：否（默认）
     * @param sysUserBase   用户信息
     */
    public void setPermissionControl(boolean permissionControl, SysUserBase sysUserBase) {
        this.permissionControl = permissionControl;
        this.sysUserBase = sysUserBase;
    }
}
