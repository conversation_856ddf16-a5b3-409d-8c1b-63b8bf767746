package com.my.som.controller.hospitalAnalysis;

import com.my.som.common.api.CommonPage;
import com.my.som.common.api.CommonResult;
import com.my.som.common.util.BeanUtil;
import com.my.som.controller.BaseController;
import com.my.som.dto.hospitalAnalysis.HospitalAnalysisQueryParam;
import com.my.som.service.hospitalAnalysis.DoctorAnalysisService;
import com.my.som.vo.hospitalAnalysis.DoctorAnalysisCountInfo;
import com.my.som.vo.hospitalAnalysis.DoctorAnalysisInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;

/**
 * 医院医生分析Controller
 * Created by sky on 2020/3/17.
 */
@Controller
@Api(tags = "DoctorAnalysisController", description = "医院医生分析")
@RequestMapping("/doctorAnalysis")
public class DoctorAnalysisController extends BaseController {
    @Autowired
    private DoctorAnalysisService doctorAnalysisService;

    @ApiOperation("查询医院费用分析主要信息")
    @RequestMapping(value = "/mainInfoList", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<CommonPage<DoctorAnalysisInfo>> list(HospitalAnalysisQueryParam queryParam,
                                                             @RequestParam(value = "pageSize", defaultValue = "200") Integer pageSize,
                                                             @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum) {
//        BeanUtil.copyPropertiesIgnoreNull(getUserBaseInfo(), queryParam);
        List<DoctorAnalysisInfo> doctorAnalysisInfoList = doctorAnalysisService.list(queryParam, pageSize, pageNum);
        return CommonResult.success(CommonPage.restPage(doctorAnalysisInfoList));
    }

    @ApiOperation("查询医院费用分析统计信息")
    @RequestMapping(value = "/getCountInfo", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<List<DoctorAnalysisCountInfo>> getCountInfo(HospitalAnalysisQueryParam queryParam) {
//        BeanUtil.copyPropertiesIgnoreNull(getUserBaseInfo(), queryParam);
        List<DoctorAnalysisCountInfo> doctorAnalysisCountInfoList = doctorAnalysisService.getCountInfo(queryParam);
        return CommonResult.success(doctorAnalysisCountInfoList);
    }

}
