package com.my.som.controller.medicalQuality;

import com.my.som.common.api.CommonPage;
import com.my.som.common.api.CommonResult;
import com.my.som.common.util.BeanUtil;
import com.my.som.controller.BaseController;
import com.my.som.dto.medicalQuality.*;
import com.my.som.model.dataHandle.SomSetlInvyQltDeduPointDetl;
import com.my.som.service.medicalQuality.MedicalQualityScoreService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;

/**
 * 病案质量得分Controller
 * Created by sky on 2020/3/3.
 */
@Controller
@Api(tags = "MedicalQualityScoreController", description = "病案质量得分")
@RequestMapping("/medicalQualityScore")
public class MedicalQualityScoreController extends BaseController {
    @Autowired
    private MedicalQualityScoreService medicalQualityScoreService;

    @ApiOperation("查询所有病案质量得分情况")
    @RequestMapping(value = "/getList", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<CommonPage<MedicalAndScoreInfo>> getList(SettleListMainInfoQueryParam queryParam,
                                                                 @RequestParam(value = "pageSize", defaultValue = "200") Integer pageSize,
                                                                 @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum) {
//        BeanUtil.copyPropertiesIgnoreNull(getUserBaseInfo(), queryParam);
        List<MedicalAndScoreInfo> list = medicalQualityScoreService.list(queryParam, pageSize, pageNum);
        return CommonResult.success(CommonPage.restPage(list));
    }

    @ApiOperation("查询病案得分统计信息")
    @RequestMapping(value = "/getScoreCountInfo", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<MedicalQualityScoreCountInfo> getScoreCountInfo(SettleListMainInfoQueryParam queryParam) {
//        BeanUtil.copyPropertiesIgnoreNull(getUserBaseInfo(), queryParam);
        MedicalQualityScoreCountInfo medicalQualityScoreCountInfo = medicalQualityScoreService.getScoreCountInfo(queryParam);
        return CommonResult.success(medicalQualityScoreCountInfo);
    }

    @ApiOperation("查询病案质量得分最差top10")
    @RequestMapping(value = "/getScoreLowTopInfo", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<List<ScoreLowTopInfo>> getScoreLowTopInfo(SettleListMainInfoQueryParam queryParam) {
//        BeanUtil.copyPropertiesIgnoreNull(getUserBaseInfo(), queryParam);
        List<ScoreLowTopInfo> scoreLowTopInfoLists = medicalQualityScoreService.getScoreLowTopInfo(queryParam);
        return CommonResult.success(scoreLowTopInfoLists);
    }

    @ApiOperation("根据结算清单ID查询病案质量得分详情")
    @RequestMapping(value = "/getScoreDetailById", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<SomSetlInvyQltDeduPointDetl> getScoreDetailById(@RequestParam(value = "id") Long id) {
        SomSetlInvyQltDeduPointDetl somSetlInvyQltDeduPointDetl = medicalQualityScoreService.getMedicalQualityScoreDetailById(id);
        return CommonResult.success(somSetlInvyQltDeduPointDetl);
    }

}
