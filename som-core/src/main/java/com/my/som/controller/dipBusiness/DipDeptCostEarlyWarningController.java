package com.my.som.controller.dipBusiness;

import com.my.som.common.api.CommonResult;
import com.my.som.dto.dipBusiness.DipDeptCostEarlyWarningDto;
import com.my.som.service.dipBusiness.DipDeptCostEarlyWarningService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/dipDeptCostEarlyWarningController")
public class DipDeptCostEarlyWarningController {
    @Autowired
    private DipDeptCostEarlyWarningService dipDeptCostEarlyWarningService;
    /**
     * 获取数据
     * @param dto 参数
     * @return
     */
    @RequestMapping("/getList")
    public CommonResult getList(DipDeptCostEarlyWarningDto dto){
        return CommonResult.success(dipDeptCostEarlyWarningService.getList(dto));
    }
}
