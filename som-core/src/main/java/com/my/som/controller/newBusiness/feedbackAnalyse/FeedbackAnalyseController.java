package com.my.som.controller.newBusiness.feedbackAnalyse;

import com.my.som.common.api.CommonPage;
import com.my.som.common.api.CommonResult;
import com.my.som.dto.newBusiness.feedbackAnalyse.FeedbackAnalysisDto;
import com.my.som.service.newBusiness.FeedbackAnalysis.FeedbackAnalyseService;
import com.my.som.vo.newBusiness.NewBusinessCommonVo;
import com.my.som.vo.newBusiness.feedbackAnalyse.FeedbackAnalyseVo;
import com.my.som.vo.newBusiness.feedbackAnalyse.FeedbackAnalysisVo;
import com.my.som.vo.newBusiness.feedbackAnalyse.FeedbackDataComparisonVo;
import com.my.som.vo.newBusiness.feedbackAnalyse.FeedbackDimeVo;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * @BelongsProject: medicalback
 * @BelongsPackage: com.my.som.controller.newBusiness.feebackAnalyse
 * @author: zyd
 * @CreateTime: 2023-07-31
 * @Description: 反馈数据分析
 * @Version: 1.0
 */
@RestController
@RequestMapping("/feedbackAnalyseController")
public class FeedbackAnalyseController {

    @Resource
    private FeedbackAnalyseService feedbackAnalyseService;

    /**
     * 查询数据变化趋势
     * @param dto
     * @return
     */
    @RequestMapping("/queryDataThread")
    public CommonResult<List<FeedbackAnalyseVo>> queryDataThread(@RequestBody FeedbackAnalysisDto dto){
        List<FeedbackAnalyseVo> analyseVos = feedbackAnalyseService.queryDataTrend(dto);
        return CommonResult.success(analyseVos);
    }


    /**
     * 查询统计数据
     * @param dto
     * @return
     */
    @RequestMapping("/selectSummary")
    public CommonResult<FeedbackAnalyseVo> selectSummary(@RequestBody FeedbackAnalysisDto dto){
        FeedbackAnalyseVo analyseVos = feedbackAnalyseService.selectSummary(dto);
        return CommonResult.success(analyseVos);
    }

    /**
     * 当年数据变化趋势
     * @param dto
     * @return
     */
    @RequestMapping("/selectDataChange")
    public CommonResult<FeedbackAnalyseVo> selectDataChange(@RequestBody FeedbackAnalysisDto dto){
        FeedbackAnalyseVo analyseVos = feedbackAnalyseService.selectDataChange(dto);
        return CommonResult.success(analyseVos);
    }


    /**
     * 结算完整饼状图数据
     * @param dto
     * @return
     */
    @RequestMapping("/selectParTypePie")
    public CommonResult<FeedbackAnalyseVo> selectParTypePie(@RequestBody FeedbackAnalysisDto dto){
        FeedbackAnalyseVo vo = feedbackAnalyseService.selectParTypePie(dto);
        return CommonResult.success(vo);
    }

    /**
     * 查询差异费用排序数据
     * @param dto
     * @return
     */
    @RequestMapping("/selectOrderData")
    public CommonResult<FeedbackAnalyseVo> selectOrderData(@RequestBody FeedbackAnalysisDto dto){
        FeedbackAnalyseVo vo = feedbackAnalyseService.selectOrderData(dto);
        return CommonResult.success(vo);
    }

    /**
     * 查询下拉
     * @return
     */
    @RequestMapping("/queryDropdown")
    public CommonResult<List<NewBusinessCommonVo>> queryDropdown(@RequestBody FeedbackAnalysisDto dto){
        return CommonResult.success(feedbackAnalyseService.queryDropdown(dto));
    }

    /**
     * 查询下拉
     * @return
     */
    @RequestMapping("/queryDeptDropdown")
    public CommonResult<List<NewBusinessCommonVo>> queryDeptDropdown(@RequestBody FeedbackAnalysisDto dto){
        return CommonResult.success(feedbackAnalyseService.queryDeptDropdown(dto));
    }

    /**
     * 根据科室查询反馈数据
     */
    @RequestMapping("/selectFeedbackDeptData")
    public CommonResult<CommonPage<FeedbackDimeVo>> selectFeedbackDeptData(@RequestBody FeedbackAnalysisDto dto){
        List<FeedbackDimeVo> feedbackDimeVos = feedbackAnalyseService.selectFeedbackDeptData(dto);
        return CommonResult.success(CommonPage.restPage(feedbackDimeVos));
    }

    /**
     * 查询科室下拉选
     */
    @RequestMapping("/selectFeedbackDeptOptions")
    public CommonResult<List<NewBusinessCommonVo>> selectFeedbackDeptOptions(@RequestBody FeedbackAnalysisDto dto){
        List<NewBusinessCommonVo> newBusinessCommonVos = feedbackAnalyseService.selectFeedbackDeptOptions(dto);
        return CommonResult.success(newBusinessCommonVos);
    }
    /**
     * 查询反馈数据
     * @param dto
     * @return
     */
    @RequestMapping("/queryFeedAnalysis")
    public CommonResult<CommonPage<FeedbackDimeVo>> queryFeedAnalysis(@RequestBody FeedbackAnalysisDto dto){
        List<FeedbackDimeVo> list = feedbackAnalyseService.queryFeedAnalysis(dto);
        return CommonResult.success(CommonPage.restPage(list));
    }

    /**
     * 根据患者查询反馈数据
     */
    @RequestMapping("/selectFeedbackPersonData")
    public CommonResult<CommonPage<FeedbackAnalysisVo>> selectFeedbackPersonData(@RequestBody FeedbackAnalysisDto dto){
        List<FeedbackAnalysisVo> feedbackAnalysisVos = feedbackAnalyseService.selectFeedbackPersonData(dto);
        return CommonResult.success(CommonPage.restPage(feedbackAnalysisVos));
    }

    /**
     * 根据病组查询反馈数据
     */
    @RequestMapping("/selectFeedbackDiseaseData")
    public CommonResult<CommonPage<FeedbackDimeVo>> selectFeedbackDiseaseData(@RequestBody FeedbackAnalysisDto dto){
        List<FeedbackDimeVo> feedbackDimeVos = feedbackAnalyseService.selectFeedbackDiseaseData(dto);
        return CommonResult.success(CommonPage.restPage(feedbackDimeVos));
    }

    /**
     * 查询病组下拉选
     */
    @RequestMapping("/selectFeedbackDiseaseOptions")
    public CommonResult<List<NewBusinessCommonVo>> selectFeedbackDiseaseOptions(@RequestBody FeedbackAnalysisDto dto){
        List<NewBusinessCommonVo> newBusinessCommonVos = feedbackAnalyseService.selectFeedbackDiseaseOptions(dto);
        return CommonResult.success(newBusinessCommonVos);
    }

    /**
     * 反馈数据科室对比右边
     * @param dto
     * @return
     */
    @RequestMapping("/selectFeedbackDataRight")
    public CommonResult selectFeedbackDataRight(@RequestBody FeedbackAnalysisDto dto){
        Map map = feedbackAnalyseService.selectFeedbackDataRight(dto);
        return CommonResult.success(map);

    }


    /**
     * 反馈数据医生对比右边
     * @param dto
     * @return
     */
    @RequestMapping("/selectFeedbackDoctorRight")
    public CommonResult selectFeedbackDoctorRight(@RequestBody FeedbackAnalysisDto dto){
        Map map = feedbackAnalyseService.selectFeedbackDoctorRight(dto);
        return CommonResult.success(map);
    }
}
