package com.my.som.controller.hosPerfAppraisal;

import com.my.som.common.api.CommonPage;
import com.my.som.common.api.CommonResult;
import com.my.som.dto.hosPerfAppraisal.HosPerfAppraisalKpiItemDto;
import com.my.som.service.hosPerfAppraisal.HosPerfAppraisalKpiItemService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @author: zyd
 * @version 1.0
 * @date: 2023-07-31
 * @description:
 */
@RestController
@RequestMapping("/hosPerfAppraisalKpiItem")
public class HosPerfAppraisalKpiItemController {


    @Autowired
    private HosPerfAppraisalKpiItemService hosPerfAppraisalKpiItemService;

    /**
     * 查询数据
     * @param dto
     * @return
     */
    @RequestMapping("/queryList")
    public CommonResult<?> queryList(@RequestBody HosPerfAppraisalKpiItemDto dto) {
        return CommonResult.success(hosPerfAppraisalKpiItemService.queryList(dto));
    }

    /**
     * 新增
     * @param dto
     * @return
     */
    @RequestMapping("/add")
    public CommonResult<?> add(@RequestBody HosPerfAppraisalKpiItemDto dto){
        hosPerfAppraisalKpiItemService.add(dto);
        return CommonResult.success();
    }

    /**
     * 修改
     * @param dto
     * @return
     */
    @RequestMapping("/update")
    public CommonResult<?> update(@RequestBody HosPerfAppraisalKpiItemDto dto){
        hosPerfAppraisalKpiItemService.update(dto);
        return CommonResult.success();
    }

    /**
     * 移除
     * @param dto
     * @return
     */
    @RequestMapping("/remove")
    public CommonResult<?> remove(@RequestBody HosPerfAppraisalKpiItemDto dto){
        hosPerfAppraisalKpiItemService.remove(dto);
        return CommonResult.success();
    }
}
