package com.my.som.controller.integratedQuery;

import com.my.som.common.api.CommonPage;
import com.my.som.common.api.CommonResult;
import com.my.som.dto.integratedQuery.IntegratedQueryDto;
import com.my.som.service.integratedQuery.IntegratedQueryService;
import com.my.som.vo.integratedQuery.IntegratedQueryVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;


import java.util.List;
import java.util.Map;

/**
 * @author: zyd
 * @version 1.0
 * @description:自定义综合查询
 * @date: 2023-07-31
 */
@Controller
@RequestMapping("/integratedQuery")
public class IntegratedQueryController {

    @Autowired
    private IntegratedQueryService integratedQueryService;
    @RequestMapping(value = "/list")
    @ResponseBody
    public CommonResult<Map<String, List<IntegratedQueryVo>>> list(@RequestBody IntegratedQueryDto dto){
        dto.setType("1");
        return  CommonResult.success(integratedQueryService.list(dto));
    }
    @RequestMapping(value = "/querydimensionality")
    @ResponseBody
    public CommonResult<Map<String, List<IntegratedQueryVo>>> querydimensionality(@RequestBody IntegratedQueryDto dto){
        dto.setType("2");
        return  CommonResult.success(integratedQueryService.list(dto));
    }

    @RequestMapping(value = "/queryData")
    @ResponseBody
    public CommonResult queryData(@RequestBody IntegratedQueryDto dto){
        List<Map<String,Object>> res = integratedQueryService.queryData(dto);
        return  CommonResult.success(CommonPage.restPage(res));
    }
}
