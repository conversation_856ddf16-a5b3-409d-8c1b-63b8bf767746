package com.my.som.controller.hospitalAnalysis;

import com.my.som.common.api.CommonPage;
import com.my.som.common.api.CommonResult;
import com.my.som.common.util.BeanUtil;
import com.my.som.controller.BaseController;
import com.my.som.dto.hospitalAnalysis.HospitalAnalysisQueryParam;
import com.my.som.service.hospitalAnalysis.DrgsDeptIndexService;
import com.my.som.vo.hospitalAnalysis.DrgsDeptCountVo;
import com.my.som.vo.hospitalAnalysis.DrgsDeptIndexVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;

/**
 * DRGs分组科室指标Controller
 * Created by sky on 2020/7/13.
 */
@Controller
@Api(tags = "DrgsDeptIndexController", description = "科室DRGs指标")
@RequestMapping("/drgsDeptIndex")
public class DrgsDeptIndexController extends BaseController {
    @Autowired
    private DrgsDeptIndexService drgsDeptIndexService;

    @ApiOperation("查询科室DRGs指标信息")
    @RequestMapping(value = "/getList", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<CommonPage<DrgsDeptIndexVo>> list(HospitalAnalysisQueryParam queryParam,
                                                          @RequestParam(value = "pageSize", defaultValue = "200") Integer pageSize,
                                                          @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum) {
//        BeanUtil.copyPropertiesIgnoreNull(getUserBaseInfo(), queryParam);
        List<DrgsDeptIndexVo> drgsDeptIndexVoList = drgsDeptIndexService.list(queryParam, pageSize, pageNum);
        return CommonResult.success(CommonPage.restPage(drgsDeptIndexVoList));
    }

    @ApiOperation("查询科室DRGs指标统计信息")
    @RequestMapping(value = "/getCountInfo", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<List<DrgsDeptCountVo>> getCountInfo(HospitalAnalysisQueryParam queryParam) {
//        BeanUtil.copyPropertiesIgnoreNull(getUserBaseInfo(), queryParam);
        List<DrgsDeptCountVo> drgsDeptCountVoList = drgsDeptIndexService.getCountInfo(queryParam);
        return CommonResult.success(drgsDeptCountVoList);
    }

}
