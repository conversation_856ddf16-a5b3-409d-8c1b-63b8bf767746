package com.my.som.controller.medicalQuality;

import com.my.som.common.api.CommonPage;
import com.my.som.common.api.CommonResult;
import com.my.som.controller.BaseController;
import com.my.som.dto.medicalQuality.CompareAnalysisDto;
import com.my.som.service.medicalQuality.PPLCompareAnalysisControllerService;
import com.my.som.vo.medicalQuality.PPLCompareAnalysisVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @author: zyd
 * @Date 2021/9/23 2:32 下午
 * @Version 1.0
 * @Description:
 */
@RestController
@RequestMapping("pplCompareAnalysisController")
public class PPLCompareAnalysisController extends BaseController {


    @Autowired
    private PPLCompareAnalysisControllerService pplCompareAnalysisControllerService;

    /**
     * 查询表格数据
     * @param dto
     * @return
     */
    @RequestMapping("/getList")
    public CommonResult<CommonPage<PPLCompareAnalysisVo>> getList(@RequestBody CompareAnalysisDto dto) {
        List<PPLCompareAnalysisVo> list = pplCompareAnalysisControllerService.getList(dto);
        return CommonResult.success(CommonPage.restPage(list));
    }

    /**
     * 查询人员基本数据
     * @param dto
     * @return
     */
    @RequestMapping("/getInfo")
    public CommonResult<List<PPLCompareAnalysisVo>> getInfo(@RequestBody CompareAnalysisDto dto){
        List<PPLCompareAnalysisVo> list = pplCompareAnalysisControllerService.getInfo(dto);
        return CommonResult.success(list);
    }

    /**
     * 查询人员入组数据
     * @param dto
     * @return
     */
    @RequestMapping("/getIngroup")
    public CommonResult<List<PPLCompareAnalysisVo>> getIngroup(@RequestBody CompareAnalysisDto dto){
        List<PPLCompareAnalysisVo> list = pplCompareAnalysisControllerService.getIngroup(dto);
        return CommonResult.success(list);
    }

    /**
     * 查询人员费用差值数据
     * @param dto
     * @return
     */
    @RequestMapping("/getCost")
    public CommonResult<List<PPLCompareAnalysisVo>> getCost(@RequestBody CompareAnalysisDto dto){
        List<PPLCompareAnalysisVo> list = pplCompareAnalysisControllerService.getCost(dto);
        return CommonResult.success(list);
    }
    /**
     * 查询人员基本费用数据
     * @param dto
     * @return
     */
    @RequestMapping("/getCostPay")
    public CommonResult<List<PPLCompareAnalysisVo>> getCostPay(@RequestBody CompareAnalysisDto dto){
        List<PPLCompareAnalysisVo> list = pplCompareAnalysisControllerService.getCostPay(dto);
        return CommonResult.success(list);
    }

}
