package com.my.som.controller.groupManagement;

import com.my.som.common.api.CommonPage;
import com.my.som.common.api.CommonResult;
import com.my.som.common.constant.DrgConst;
import com.my.som.dto.groupManagement.MedicalManagementDto;
import com.my.som.service.groupManagement.MedicalManagementService;
import com.my.som.vo.groupManagement.MedicalManagementVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 病案管理
 */
@RestController
@RequestMapping("/medicalManagementController")
public class MedicalManagementController {

    @Autowired
    private MedicalManagementService medicalManagementService;

    /**
     * DIP测算
     * @param dto
     * @return
     */
    @RequestMapping("/calculationData")
    public CommonResult calculationData(MedicalManagementDto dto) {
        dto.setType(DrgConst.TYPE_2);
        Boolean aBoolean = medicalManagementService.calculationData(dto);
        return CommonResult.success(aBoolean);
    }

    /**
     * 病案查询
     * @param dto
     * @return
     */
    @RequestMapping("/queryPatientInfo")
    public CommonResult<CommonPage> queryPatientInfo(MedicalManagementDto dto) {
        List<MedicalManagementVo> list = medicalManagementService.queryPatientInfo(dto);
        return CommonResult.success(CommonPage.restPage(list));
    }

    /**
     * 详情查询
     * @param dto
     * @return
     */
    @RequestMapping("/queryGroupsInfo")
    public CommonResult queryGroupsInfo(MedicalManagementDto dto) {
        List<MedicalManagementVo> list = medicalManagementService.queryGroupsInfo(dto);
        return CommonResult.success(list);
    }

    /**
     * DRG测算
     * @param dto
     * @return
     */
    @RequestMapping("/drgCalculationData")
    public CommonResult drgCalculationData(MedicalManagementDto dto) {
        dto.setType(DrgConst.TYPE_2);
        medicalManagementService.drgCalculationData(dto);
        return CommonResult.success();
    }
}
