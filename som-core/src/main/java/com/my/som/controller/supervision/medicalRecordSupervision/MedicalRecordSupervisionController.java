package com.my.som.controller.supervision.medicalRecordSupervision;

import com.my.som.common.api.CommonPage;
import com.my.som.common.api.CommonResult;
import com.my.som.dto.supervision.MedicalRecordSupervisionDto;
import com.my.som.service.supervision.medicalRecordSupervision.MedicalRecordSupervisionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/medicalRecordSupervisionController")
public class MedicalRecordSupervisionController {

    @Autowired
    MedicalRecordSupervisionService medicalRecordSupervisionService;

    @RequestMapping("/queryPatientInfo")
    public CommonResult<CommonPage<?>> queryPatientInfo(MedicalRecordSupervisionDto dto) {
        return CommonResult.success(medicalRecordSupervisionService.queryPatientInfo(dto));
    }
}
