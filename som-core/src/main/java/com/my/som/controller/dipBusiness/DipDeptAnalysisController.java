package com.my.som.controller.dipBusiness;

import com.my.som.common.api.CommonResult;
import com.my.som.dto.dipBusiness.DipPatientAnalysisDto;
import com.my.som.service.dipBusiness.DipDeptAnalysisService;
import com.my.som.vo.dipBusiness.DipDeptAnalysisVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/dipDeptAnalysisController")
public class DipDeptAnalysisController {

    @Autowired
    private DipDeptAnalysisService dipDeptAnalysisService;

    @RequestMapping("/selectDeptData")
    public CommonResult selectDeptData(@RequestBody DipPatientAnalysisDto dto) {
        List<DipDeptAnalysisVo> list = dipDeptAnalysisService.selectDeptData(dto);
        return CommonResult.success(list);
    }
}
