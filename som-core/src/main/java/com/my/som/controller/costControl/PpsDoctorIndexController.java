package com.my.som.controller.costControl;

import com.my.som.common.api.CommonPage;
import com.my.som.common.api.CommonResult;
import com.my.som.common.util.BeanUtil;
import com.my.som.controller.BaseController;
import com.my.som.dto.costControl.CostControlQueryParam;
import com.my.som.service.costControl.PpsDoctorIndexService;
import com.my.som.vo.costControl.PpsDoctorCountVo;
import com.my.som.vo.costControl.PpsDoctorIndexVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;

/**
 * 成都分组医生指标Controller
 * Created by sky on 2020/7/13.
 */
@Controller
@Api(tags = "PpsDoctorIndexController", description = "医生病组指标")
@RequestMapping("/ppsDoctorIndex")
public class PpsDoctorIndexController extends BaseController {
    @Autowired
    private PpsDoctorIndexService ppsDoctorIndexService;

    @ApiOperation("查询医生病组指标信息")
    @RequestMapping(value = "/getList", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<CommonPage<PpsDoctorIndexVo>> list(CostControlQueryParam queryParam,
                                                           @RequestParam(value = "pageSize", defaultValue = "200") Integer pageSize,
                                                           @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum) {
//        BeanUtil.copyPropertiesIgnoreNull(getUserBaseInfo(), queryParam);
        List<PpsDoctorIndexVo> ppsDoctorIndexVoList = ppsDoctorIndexService.list(queryParam, pageSize, pageNum);
        return CommonResult.success(CommonPage.restPage(ppsDoctorIndexVoList));
    }

    @ApiOperation("查询医生病组指标统计信息")
    @RequestMapping(value = "/getCountInfo", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<List<PpsDoctorCountVo>> getCountInfo(CostControlQueryParam queryParam) {
//        BeanUtil.copyPropertiesIgnoreNull(getUserBaseInfo(), queryParam);
        List<PpsDoctorCountVo> ppsDoctorCountVoList = ppsDoctorIndexService.getCountInfo(queryParam);
        return CommonResult.success(ppsDoctorCountVoList);
    }

}
