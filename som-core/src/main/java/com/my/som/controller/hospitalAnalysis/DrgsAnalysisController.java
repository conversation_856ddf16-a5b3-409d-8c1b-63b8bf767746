package com.my.som.controller.hospitalAnalysis;

import com.my.som.common.api.CommonPage;
import com.my.som.common.api.CommonResult;
import com.my.som.common.util.BeanUtil;
import com.my.som.controller.BaseController;
import com.my.som.dto.hospitalAnalysis.HospitalAnalysisQueryParam;
import com.my.som.service.hospitalAnalysis.DrgsAnalysisService;
import com.my.som.vo.hospitalAnalysis.DrgsAnalysisCountInfo;
import com.my.som.vo.hospitalAnalysis.DrgsAnalysisInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;

/**
 * 医院分组分析Controller
 * Created by sky on 2020/3/17.
 */
@Controller
@Api(tags = "DrgsAnalysisController", description = "医院分组分析")
@RequestMapping("/drgsAnalysis")
public class DrgsAnalysisController extends BaseController {
    @Autowired
    private DrgsAnalysisService drgsAnalysisService;

    @ApiOperation("查询医院分组分析主要信息")
    @RequestMapping(value = "/mainInfoList", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<CommonPage<DrgsAnalysisInfo>> list(HospitalAnalysisQueryParam queryParam,
                                                           @RequestParam(value = "pageSize", defaultValue = "200") Integer pageSize,
                                                           @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum) {
//        BeanUtil.copyPropertiesIgnoreNull(getUserBaseInfo(), queryParam);
        List<DrgsAnalysisInfo> costAnalysisInfoList = drgsAnalysisService.list(queryParam, pageSize, pageNum);
        return CommonResult.success(CommonPage.restPage(costAnalysisInfoList));
    }

    @ApiOperation("查询医院分组分析统计信息")
    @RequestMapping(value = "/getCountInfo", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<List<DrgsAnalysisCountInfo>> getCountInfo(HospitalAnalysisQueryParam queryParam) {
//        BeanUtil.copyPropertiesIgnoreNull(getUserBaseInfo(), queryParam);
        List<DrgsAnalysisCountInfo> drgsAnalysisInfoList = drgsAnalysisService.getCountInfo(queryParam);
        return CommonResult.success(drgsAnalysisInfoList);
    }

}
