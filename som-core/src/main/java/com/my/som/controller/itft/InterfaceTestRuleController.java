package com.my.som.controller.itft;

import com.my.som.common.api.CommonPage;
import com.my.som.common.api.CommonResult;
import com.my.som.dto.itft.ITFTRuleDto;
import com.my.som.service.itft.InterfaceTestRuleService;
import com.my.som.util.ITFTValidateUtil;
import com.my.som.vo.itft.ITFTRuleVo;
import com.my.som.vo.itft.ItftLogVo;
import com.my.som.vo.itft.ITFTValidateLogVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @author: zyd
 * @version 1.0
 * @date: 2023-07-31
 * @description: 接口测试规则控制器
 */
@RestController
@RequestMapping("/interfaceTestRuleController")
public class InterfaceTestRuleController {

    @Autowired
    private InterfaceTestRuleService interfaceTestRuleService;

    /**
     * 查询数据
     * @param dto
     * @return
     */
    @RequestMapping("/queryData")
    public CommonResult<?> queryData(@RequestBody ITFTRuleDto dto){
        List<ITFTRuleVo> list = interfaceTestRuleService.queryData(dto);
        return CommonResult.success(CommonPage.restPage(list));
    }

    /**
     * 修改数据
     * @param dto
     * @return
     */
    @RequestMapping("/updateRule")
    public CommonResult<?> updateRule(@RequestBody ITFTRuleDto dto){
        interfaceTestRuleService.updateRule(dto);
        return CommonResult.success();
    }

    /**
     * 新增数据
     * @param dto
     * @return
     */
    @RequestMapping("/addRule")
    public CommonResult<?> addRule(@RequestBody ITFTRuleDto dto){
        interfaceTestRuleService.addRule(dto);
        return CommonResult.success();
    }

    /**
     * 查询校验日志
     * @param dto
     * @return
     */
    @RequestMapping("/queryValLog")
    public CommonResult<?> queryValLog(@RequestBody ITFTRuleDto dto){
        List<ITFTValidateLogVo> list = interfaceTestRuleService.queryValLog(dto);
        return CommonResult.success(list);
    }

    /**
     * 刷新配置
     * @param dto
     * @return
     */
    @RequestMapping("/refreshConfig")
    public CommonResult<?> refreshConfig(@RequestBody ITFTRuleDto dto){
        ITFTValidateUtil.refreshConfig();
        return CommonResult.success();
    }

    /**
     * 查询测试日志
     * @return
     */
    @RequestMapping("/queryLog")
    public CommonResult<List<ItftLogVo>> queryLog(){
        List<ItftLogVo> list = interfaceTestRuleService.queryLog();
        return CommonResult.success(list);
    }
}
