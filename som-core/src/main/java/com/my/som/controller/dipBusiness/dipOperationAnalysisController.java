package com.my.som.controller.dipBusiness;


import com.my.som.common.api.CommonPage;
import com.my.som.common.api.CommonResult;
import com.my.som.common.util.BeanUtil;
import com.my.som.controller.BaseController;
import com.my.som.dto.hospitalAnalysis.HospitalAnalysisQueryParam;
import com.my.som.service.dipBusiness.DipOperativeAnalysisService;
import com.my.som.service.hospitalAnalysis.OperativeAnalysisService;
import com.my.som.vo.dipBusiness.DipOperativeAnalysisCountInfo;
import com.my.som.vo.dipBusiness.DipOperativeAnalysisVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;

@Controller
@Api(tags = "dipOperativeAnalysisController", description = "医院手术分析")
@RequestMapping("/dip/operativeAnalysis")
public class dipOperationAnalysisController extends BaseController {
    @Autowired
    private DipOperativeAnalysisService dipOperativeAnalysisService;

    @ApiOperation("查询医院手术分析主要信息")
    @RequestMapping(value = "/mainInfoList", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<CommonPage<DipOperativeAnalysisVo>> list(HospitalAnalysisQueryParam queryParam,
                                                                @RequestParam(value = "pageSize", defaultValue = "200") Integer pageSize,
                                                                @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum) {
//        BeanUtil.copyPropertiesIgnoreNull(getUserBaseInfo(), queryParam);
        List<DipOperativeAnalysisVo> operativeAnalysisInfoList = dipOperativeAnalysisService.list(queryParam, pageSize, pageNum);
        return CommonResult.success(CommonPage.restPage(operativeAnalysisInfoList));
    }

    @ApiOperation("查询医院手术分析统计信息")
    @RequestMapping(value = "/getCountInfo", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<List<DipOperativeAnalysisCountInfo>> getCountInfo(HospitalAnalysisQueryParam queryParam) {
        BeanUtil.copyPropertiesIgnoreNull(getUserBaseInfo(), queryParam);
        List<DipOperativeAnalysisCountInfo> operativeAnalysisCountInfo = dipOperativeAnalysisService.getCountInfo(queryParam);
        return CommonResult.success(operativeAnalysisCountInfo);
    }
}
