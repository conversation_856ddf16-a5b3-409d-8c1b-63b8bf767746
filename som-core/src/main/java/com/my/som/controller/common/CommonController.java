package com.my.som.controller.common;

import com.my.som.common.api.CommonResult;
import com.my.som.common.api.ResultCode;
import com.my.som.common.constant.DrgConst;
import com.my.som.common.util.ComputerUtil;
import com.my.som.common.util.RedisUtils;
import com.my.som.config.DynamicSplitTask;
import com.my.som.dto.SysUserLoginParam;
import com.my.som.security.util.JwtTokenUtil;
import com.my.som.service.sys.impl.SysUserServiceImpl;
import com.my.som.util.SysCommonConfigUtil;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.web.bind.annotation.*;

/**
 * @author: zyd
 * @version 1.0
 * @date: 2023-07-31
 * @description:
 */
@RestController
@RequestMapping("/commonController")
@Slf4j
public class CommonController {
    Logger logger = LoggerFactory.getLogger(DynamicSplitTask.class);
    @Autowired
    private SysUserServiceImpl sysUserService;

    @Autowired
    private JwtTokenUtil jwtTokenUtil;

    /**
     * 检查应用心跳
     *
     * @return
     */
    @RequestMapping("/heartbeat")
    public CommonResult heartbeat() throws InterruptedException {
        return CommonResult.success("pong");
    }


    /**
     * 获取访问token
     *
     * @return
     */
//    @RequestMapping("/accessToken")
//    public CommonResult accessToken(@RequestBody SysUserLoginParam sysUserLoginParam) {
//        UserDetails userDetails = sysUserService.loadUserByUsername(sysUserLoginParam.getUsername());
//        if (userDetails == null) {
//            return CommonResult.failed(ResultCode.FORBIDDEN);
//        }
//        String token = jwtTokenUtil.generateToken(userDetails);
//        RedisUtils.set(token, userDetails);
//        return CommonResult.success(token);
//    }

    /**
     * 获取访问token
     *
     * @return
     */
    @GetMapping("/accessToken")
    public CommonResult accessToken(@RequestParam String username) {
        logger.info("外部调用生成token; 账号为：" +username);
        UserDetails userDetails = sysUserService.loadUserByUsername(username);
        if (userDetails == null) {
            return CommonResult.failed(ResultCode.FORBIDDEN);
        }
        String token = jwtTokenUtil.generateToken(userDetails);
        RedisUtils.set(token, userDetails);
        return CommonResult.success(token);
    }

    @GetMapping("/getHospType")
    public CommonResult getHospType() {
        String type = SysCommonConfigUtil.get(DrgConst.FZLX).toString().toUpperCase();
        String title = type ;
        return CommonResult.success(title.trim());
    }

    @PostMapping("/accessToken")
    public CommonResult accessTokenByPost(@RequestBody SysUserLoginParam sysUserLoginParam) {
        UserDetails userDetails = sysUserService.loadUserByUsername(sysUserLoginParam.getUsername());
        if (userDetails == null) {
            return CommonResult.failed(ResultCode.FORBIDDEN);
        }
        String token = jwtTokenUtil.generateToken(userDetails);
        RedisUtils.set(token, userDetails);
        return CommonResult.success(token);

    }
    /**
     * 获取访问token
     *
     * @return
     */
    @RequestMapping("/getAccessToken")
    public CommonResult getAccessToken(@RequestBody SysUserLoginParam sysUserLoginParam) {
        UserDetails userDetails = sysUserService.loadUserByUsername(sysUserLoginParam.getUsername());
        if (userDetails == null) {
            return CommonResult.failed(ResultCode.FORBIDDEN);
        }
        String token = jwtTokenUtil.generateToken(userDetails);
        RedisUtils.set(token, userDetails);
        return CommonResult.success(token);
    }
}
