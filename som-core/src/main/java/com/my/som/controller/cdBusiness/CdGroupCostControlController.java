package com.my.som.controller.cdBusiness;

import com.my.som.common.api.CommonPage;
import com.my.som.common.api.CommonResult;
import com.my.som.controller.BaseController;
import com.my.som.dto.cdBusiness.CostControlDto;
import com.my.som.service.cdBusiness.CdGroupCostControlService;
import com.my.som.vo.cdBusiness.CdCostControlVo;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: zyd
 * @Date 2021/8/21 4:12 下午
 * @Version 1.0
 * @Description: 成都分组控费
 */
@RestController
@RequestMapping("/cdGroupCostControlController")
public class CdGroupCostControlController extends BaseController {

    @Resource
    private CdGroupCostControlService cdGroupCostControlService;

    /**
     * 获取数据
     * @param dto   参数
     * @return
     */
    @RequestMapping("/getList")
    public CommonResult<CommonPage<CdCostControlVo>> getList(CostControlDto dto){
        List<CdCostControlVo> list = cdGroupCostControlService.getList(dto);
        return CommonResult.success(CommonPage.restPage(list));
    }
}
