package com.my.som.controller.costControl;

import com.my.som.common.api.CommonPage;
import com.my.som.common.api.CommonResult;
import com.my.som.common.util.BeanUtil;
import com.my.som.controller.BaseController;
import com.my.som.dto.costControl.CostControlQueryParam;
import com.my.som.service.costControl.PpsDeptIndexService;
import com.my.som.vo.costControl.PpsDeptCountVo;
import com.my.som.vo.costControl.PpsDeptIndexVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;

/**
 * 成都分组科室指标Controller
 * Created by sky on 2020/7/13.
 */
@Controller
@Api(tags = "PpsDeptIndexController", description = "科室病组指标")
@RequestMapping("/ppsDeptIndex")
public class PpsDeptIndexController extends BaseController {
    @Autowired
    private PpsDeptIndexService ppsDeptIndexService;

    @ApiOperation("查询科室病组指标信息")
    @RequestMapping(value = "/getList", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<CommonPage<PpsDeptIndexVo>> list(CostControlQueryParam queryParam,
                                                             @RequestParam(value = "pageSize", defaultValue = "200") Integer pageSize,
                                                             @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum) {
//        BeanUtil.copyPropertiesIgnoreNull(getUserBaseInfo(), queryParam);
        List<PpsDeptIndexVo> ppsDeptIndexVoList = ppsDeptIndexService.list(queryParam, pageSize, pageNum);
        return CommonResult.success(CommonPage.restPage(ppsDeptIndexVoList));
    }

    @ApiOperation("查询科室病组指标统计信息")
    @RequestMapping(value = "/getCountInfo", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<List<PpsDeptCountVo>> getCountInfo(CostControlQueryParam queryParam) {
//        BeanUtil.copyPropertiesIgnoreNull(getUserBaseInfo(), queryParam);
        List<PpsDeptCountVo> ppsDeptCountVoList = ppsDeptIndexService.getCountInfo(queryParam);
        return CommonResult.success(ppsDeptCountVoList);
    }

}
