package com.my.som.controller.dipBusiness;

import com.my.som.common.api.CommonPage;
import com.my.som.common.api.CommonResult;
import com.my.som.common.util.BeanUtil;
import com.my.som.controller.BaseController;
import com.my.som.dto.dipBusiness.DipAnalysisDto;
import com.my.som.service.dipBusiness.DipAnalysisService;
import com.my.som.vo.dipBusiness.DipAnalysisCountInfo;
import com.my.som.vo.dipBusiness.DipAnalysisVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;

/**
 * 医院分组分析Controller
 * Created by sky on 2020/3/17.
 */
@Controller
@Api(tags = "DipAnalysisController", description = "医院分组分析")
@RequestMapping("/dipAnalysis")
public class DipAnalysisController extends BaseController {
    @Autowired
    private DipAnalysisService dipAnalysisService;

    @ApiOperation("查询医院分组分析主要信息")
    @RequestMapping(value = "/mainInfoList", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<CommonPage<DipAnalysisVo>> list(DipAnalysisDto queryParam,
                                                        @RequestParam(value = "pageSize", defaultValue = "200") Integer pageSize,
                                                        @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum) {
//        BeanUtil.copyPropertiesIgnoreNull(getUserBaseInfo(), queryParam);
        List<DipAnalysisVo> costAnalysisInfoList = dipAnalysisService.list(queryParam, pageSize, pageNum);
        return CommonResult.success(CommonPage.restPage(costAnalysisInfoList));
    }

    @ApiOperation("查询医院分组分析统计信息")
    @RequestMapping(value = "/getCountInfo", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<List<DipAnalysisCountInfo>> getCountInfo(DipAnalysisDto queryParam) {
//        BeanUtil.copyPropertiesIgnoreNull(getUserBaseInfo(), queryParam);
        List<DipAnalysisCountInfo> dipAnalysisInfoList = dipAnalysisService.getCountInfo(queryParam);
        return CommonResult.success(dipAnalysisInfoList);
    }

}
