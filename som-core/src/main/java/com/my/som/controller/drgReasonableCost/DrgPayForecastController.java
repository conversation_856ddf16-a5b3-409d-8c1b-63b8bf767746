package com.my.som.controller.drgReasonableCost;

import com.my.som.common.api.CommonPage;
import com.my.som.common.api.CommonResult;
import com.my.som.common.util.BeanUtil;
import com.my.som.controller.BaseController;
import com.my.som.dto.drgReasonableCost.DrgReasonableCostQueryParam;
import com.my.som.service.drgReasonableCost.DrgPayForecastService;
import com.my.som.vo.drgReasonableCost.DrgPayForecastVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;

/**
 * DRG付费预测Controller
 * Created by sky on 2020/10/23.
 */
@Controller
@Api(tags = "DrgPayForecastController", description = "DRG付费预测")
@RequestMapping("/drgPayForecast")
public class DrgPayForecastController extends BaseController {
    @Autowired
    private DrgPayForecastService drgPayForecastService;

    @ApiOperation("获取DRG付费预测数据")
    @RequestMapping(value = "/mainInfoList", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<CommonPage<DrgPayForecastVo>> list(DrgReasonableCostQueryParam queryParam,
                                                           @RequestParam(value = "pageSize", defaultValue = "200") Integer pageSize,
                                                           @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum) {
        BeanUtil.copyPropertiesIgnoreNull(getUserBaseInfo(), queryParam);
        List<DrgPayForecastVo> drgPayForecastVoList = drgPayForecastService.list(queryParam, pageSize, pageNum);
        return CommonResult.success(CommonPage.restPage(drgPayForecastVoList));
    }


}
