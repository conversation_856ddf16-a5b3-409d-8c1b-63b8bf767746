package com.my.som.controller.newBusiness.disease;

import com.my.som.common.api.CommonPage;
import com.my.som.common.api.CommonResult;
import com.my.som.dto.newBusiness.disease.NewBusinessDiseaseDto;
import com.my.som.service.newBusiness.disease.NewDrgBusinessDiseaseAnalysisService;
import com.my.som.vo.newBusiness.disease.NewBusinessDiseaseVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 病组分析
 */
@RestController
@RequestMapping("/newDrgBusinessDiseaseAnalysisController")
public class NewDrgBusinessDiseaseAnalysisController {

    @Autowired
    private NewDrgBusinessDiseaseAnalysisService newDrgBusinessDiseaseAnalysisService;

    @RequestMapping("/queryDrgDiseaseKpiData")
    public CommonResult<CommonPage<NewBusinessDiseaseVo>> queryDrgDiseaseKpiData(NewBusinessDiseaseDto dto){
        List<NewBusinessDiseaseVo> list = newDrgBusinessDiseaseAnalysisService.queryDrgDiseaseKpiData(dto);
        return CommonResult.success(CommonPage.restPage(list));
    }

    @RequestMapping("/queryDrgDiseaseForecastData")
    public CommonResult<CommonPage<NewBusinessDiseaseVo>> queryDrgDiseaseForecastData(NewBusinessDiseaseDto dto){
        List<NewBusinessDiseaseVo> list = newDrgBusinessDiseaseAnalysisService.queryDrgDiseaseForecastData(dto);
        return CommonResult.success(CommonPage.restPage(list));
    }
}
