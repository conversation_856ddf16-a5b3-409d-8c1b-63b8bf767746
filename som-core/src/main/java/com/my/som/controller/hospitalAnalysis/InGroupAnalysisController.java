package com.my.som.controller.hospitalAnalysis;

import com.my.som.common.api.CommonPage;
import com.my.som.common.api.CommonResult;
import com.my.som.common.util.BeanUtil;
import com.my.som.controller.BaseController;
import com.my.som.dto.hospitalAnalysis.HospitalAnalysisQueryParam;
import com.my.som.service.hospitalAnalysis.InGroupAnalysisService;
import com.my.som.vo.hospitalAnalysis.InGroupTopCountInfo;
import com.my.som.vo.hospitalAnalysis.InGroupAnalysisCountInfo;
import com.my.som.vo.hospitalAnalysis.InGroupAnalysisInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;

/**
 * 医院入组分析Controller
 * Created by sky on 2020/3/27.
 */
@Controller
@Api(tags = "InGroupAnalysisController", description = "医院入组分析")
@RequestMapping("/inGroupAnalysis")
public class InGroupAnalysisController extends BaseController {
    @Autowired
    private InGroupAnalysisService inGroupAnalysisService;

    @ApiOperation("查询医院入组分析主要信息")
    @RequestMapping(value = "/mainInfoList", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<CommonPage<InGroupAnalysisInfo>> list(HospitalAnalysisQueryParam queryParam,
                                                                @RequestParam(value = "pageSize", defaultValue = "200") Integer pageSize,
                                                                @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum) {
//        BeanUtil.copyPropertiesIgnoreNull(getUserBaseInfo(), queryParam);
        List<InGroupAnalysisInfo> inGroupAnalysisInfoList = inGroupAnalysisService.list(queryParam, pageSize, pageNum);
        return CommonResult.success(CommonPage.restPage(inGroupAnalysisInfoList));
    }

    @ApiOperation("查询全院入组情况人次统计信息")
    @RequestMapping(value = "/getTopCountInfo", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<InGroupTopCountInfo> getTopCountInfo(HospitalAnalysisQueryParam queryParam) {
//        BeanUtil.copyPropertiesIgnoreNull(getUserBaseInfo(), queryParam);
        InGroupTopCountInfo topCountInfo = inGroupAnalysisService.getTopCountInfo(queryParam);
        return CommonResult.success(topCountInfo);
    }

    @ApiOperation("查询未入组原因统计信息")
    @RequestMapping(value = "/getNoGroupResonCountInfo", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<List<InGroupAnalysisCountInfo>> getNoGroupResonCountInfo(HospitalAnalysisQueryParam queryParam) {
//        BeanUtil.copyPropertiesIgnoreNull(getUserBaseInfo(), queryParam);
        List<InGroupAnalysisCountInfo> inGroupAnalysisCountInfoList = inGroupAnalysisService.getNoGroupResonCountInfo(queryParam);
        return CommonResult.success(inGroupAnalysisCountInfoList);
    }

}
