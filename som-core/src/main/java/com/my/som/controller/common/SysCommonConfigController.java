package com.my.som.controller.common;

import com.my.som.common.api.CommonDictResult;
import com.my.som.common.api.CommonResult;
import com.my.som.controller.BaseController;
import com.my.som.dto.common.SysCommonConfigDto;
import com.my.som.service.common.read.SysCommonConfigReadService;
import com.my.som.service.common.write.SysCommonConfigWriteService;
import com.my.som.vo.common.SeletInputInfo;
import com.my.som.vo.common.SysCommonConfigVo;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * @author: zyd
 * @Date 2021/7/14 5:25 下午
 * @Version 1.0
 * @Description:
 */
@RestController
@RequestMapping("/sysCommonConfigController")
public class SysCommonConfigController extends BaseController {

    @Resource
    private SysCommonConfigReadService sysCommonConfigReadService;

    @Resource
    private SysCommonConfigWriteService sysCommonConfigWriteService;

    /**
     * 查询系统配置
     * @return
     */
    @RequestMapping("/queryData")
    public CommonDictResult<List<SysCommonConfigVo>> queryData(SysCommonConfigDto dto) {
        List<SysCommonConfigVo> sysCommonConfigVoList = sysCommonConfigReadService.queryData(dto);
        return CommonDictResult.success(sysCommonConfigVoList);
    }

    /**
     * 修改配置
     * @param dto
     */
    @RequestMapping("/modifyConfig")
    public CommonDictResult modifyConfig(SysCommonConfigDto dto){
        sysCommonConfigWriteService.modifyConfig(dto);
        return CommonDictResult.success();
    }

    /**
     * 获取已启用的组
     * @return
     */
    @RequestMapping("/queryEnableGroup")
    public CommonResult queryEnableGroup(){
        List<SeletInputInfo> list = sysCommonConfigReadService.queryEnableGroup();
        return CommonResult.success(list);
    }

    /**
     * 查询清单配置
     * @return
     */
    @RequestMapping("/querySettleListData")
    public CommonResult<List<Map<String, String>>> querySettleListData(SysCommonConfigDto dto) {
        dto.setHospitalId(getUserBaseInfo().getHospitalId());
        List<Map<String, String>> list = sysCommonConfigReadService.querySettleListData(dto);
        return CommonResult.success(list);
    }

    @RequestMapping("/updateSysSettleListConfig")
    public CommonResult updateSysSettleListConfig(SysCommonConfigDto dto) {
        dto.setHospitalId(getUserBaseInfo().getHospitalId());
        sysCommonConfigWriteService.updateSysSettleListConfig(dto);
        return CommonResult.success();
    }

    /**
     * 获取分组类型
     * @param dto
     * @return
     */
    @RequestMapping("/queryGroupType")
    public CommonDictResult<SysCommonConfigVo> queryGroupType(@RequestBody SysCommonConfigDto dto){
        return CommonDictResult.success(sysCommonConfigReadService.queryGroupType(dto));
    }
}
