package com.my.som.controller.basicHea;

import com.my.som.common.api.CommonResult;
import com.my.som.common.vo.SomBackUser;
import com.my.som.controller.BaseController;
import com.my.som.dto.basicHea.BasicHealthDTO;
import com.my.som.dto.hisview.HisViewDTO;
import com.my.som.service.basicHea.BasicHealthService;
import com.my.som.service.hisview.HisViewService;
import com.my.som.vo.basicHea.MedicalCaseDataPullVO;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;


@RestController
@RequestMapping("/basicHea")
public class BasicHealthController extends BaseController {

    @Autowired
    private BasicHealthService basicHealthService;

    @ApiOperation("数据采集")
    @PostMapping("/get")
    @ResponseBody
    public CommonResult get(@RequestBody BasicHealthDTO dto) {

        if(dto.getFixCode() == null){
            SomBackUser userInfo = getUserInfo();
            dto.setFixCode(userInfo.getHospitalId());
        }

        basicHealthService.MedicalCaseDataPullInterface(dto);
        return CommonResult.success();
    }

    @ApiOperation("科室、医生信息维护")
    @PostMapping("/downLoadMedical")
    public void downLoadMedical(@RequestBody BasicHealthDTO dto,HttpServletResponse response) {

        if(dto.getFixCode() == null){
            SomBackUser userInfo = getUserInfo();
            dto.setFixCode(userInfo.getHospitalId());
        }


        basicHealthService.downLoadMedical(dto,response);
    }
}
