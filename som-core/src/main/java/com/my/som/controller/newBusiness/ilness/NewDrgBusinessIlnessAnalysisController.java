package com.my.som.controller.newBusiness.ilness;

import com.my.som.common.api.CommonPage;
import com.my.som.common.api.CommonResult;
import com.my.som.dto.newBusiness.disease.NewBusinessDiseaseDto;
import com.my.som.service.newBusiness.ilness.NewDrgBusinessIlnessAnalysisService;
import com.my.som.vo.newBusiness.disease.NewBusinessDiseaseVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/newDrgBusinessIlnessAnalysisController")
public class NewDrgBusinessIlnessAnalysisController {

    @Autowired
    private NewDrgBusinessIlnessAnalysisService newDrgBusinessIlnessAnalysisService;

    /**
     * 查询drg病种指标数据
     * @param dto
     * @return
     */
    @RequestMapping("/queryDrgIlnessKpiData")
    public CommonResult queryDrgIlnessKpiData(NewBusinessDiseaseDto dto){
        List<NewBusinessDiseaseVo> list = newDrgBusinessIlnessAnalysisService.queryDrgIlnessKpiData(dto);
        return CommonResult.success(CommonPage.restPage(list));
    }

    /**
     * 查询预测drg病种数据
     * @param dto
     * @return
     */
    @RequestMapping("/queryForecastIlnessData")
    public CommonResult queryForecastIlnessData(NewBusinessDiseaseDto dto){
        List<NewBusinessDiseaseVo> list = newDrgBusinessIlnessAnalysisService.queryForecastIlnessData(dto);
        return CommonResult.success(CommonPage.restPage(list));
    }

}
