package com.my.som.controller.costControl;

import com.my.som.common.api.CommonPage;
import com.my.som.common.api.CommonResult;
import com.my.som.common.util.BeanUtil;
import com.my.som.controller.BaseController;
import com.my.som.dto.costControl.CostControlQueryParam;
import com.my.som.service.costControl.CdDrgsPpsService;
import com.my.som.vo.common.CommonObject;
import com.my.som.vo.costControl.CdDrgsPpsMainInfo;
import com.my.som.vo.costControl.PpsGroupAndCostControlVo;
import com.my.som.vo.costControl.PpsInGroupCostTop10Vo;
import com.my.som.vo.costControl.PpsInGroupNumTop10Vo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;

/**
 * 成都分组病案信息Controller
 * Created by sky on 2020/3/23.
 */
@Controller
@Api(tags = "CdDrgsPpsController", description = "成都分组病案信息")
@RequestMapping("/costControl")
public class CdDrgsPpsController extends BaseController {
    @Autowired
    private CdDrgsPpsService cdDrgsPpsService;

    @ApiOperation("查询成都分组结果和控费信息")
    @RequestMapping(value = "/getPpsGroupAndCostControl", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<CommonPage<PpsGroupAndCostControlVo>> getPpsGroupAndCostControl(CostControlQueryParam queryParam,
                                                                                        @RequestParam(value = "pageSize", defaultValue = "200") Integer pageSize,
                                                                                        @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum) {
//        BeanUtil.copyPropertiesIgnoreNull(getUserBaseInfo(), queryParam);
        List<PpsGroupAndCostControlVo> ppsGroupAndCostControlVoList = cdDrgsPpsService.getPpsGroupAndCostControl(queryParam, pageSize, pageNum);
        return CommonResult.success(CommonPage.restPage(ppsGroupAndCostControlVoList));
    }

    @ApiOperation("查询成都分组病案信息")
    @RequestMapping(value = "/getList", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<CommonPage<CdDrgsPpsMainInfo>> getList(CostControlQueryParam queryParam,
                                                               @RequestParam(value = "pageSize", defaultValue = "200") Integer pageSize,
                                                               @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum) {
//        BeanUtil.copyPropertiesIgnoreNull(getUserBaseInfo(), queryParam);
        List<CdDrgsPpsMainInfo> cdDrgsPpsMainInfoList = cdDrgsPpsService.list(queryParam, pageSize, pageNum);
        return CommonResult.success(CommonPage.restPage(cdDrgsPpsMainInfoList));
    }

    @ApiOperation("成都分组人次TOP10")
    @RequestMapping(value = "/getGroupNumTop10", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<List<PpsInGroupNumTop10Vo>> getGroupNumTop10(CostControlQueryParam queryParam) {
//        BeanUtil.copyPropertiesIgnoreNull(getUserBaseInfo(), queryParam);
        List<PpsInGroupNumTop10Vo> ppsInGroupNumTop10VoList = cdDrgsPpsService.getGroupNumTop10(queryParam);
        return CommonResult.success(ppsInGroupNumTop10VoList);
    }

    @ApiOperation("成都分组住院费用TOP10")
    @RequestMapping(value = "/getGroupCostTop10", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<List<PpsInGroupCostTop10Vo>> getGroupCostTop10(CostControlQueryParam queryParam) {
//        BeanUtil.copyPropertiesIgnoreNull(getUserBaseInfo(), queryParam);
        List<PpsInGroupCostTop10Vo> ppsInGroupCostTop10VoList = cdDrgsPpsService.getGroupCostTop10(queryParam);
        return CommonResult.success(ppsInGroupCostTop10VoList);
    }

    @ApiOperation("查询成都分组费用信息")
    @RequestMapping(value = "/getCostCountInfo", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<List<CommonObject>> getCostCountInfo(CostControlQueryParam queryParam) {
//        BeanUtil.copyPropertiesIgnoreNull(getUserBaseInfo(), queryParam);
        List<CommonObject> commonObjectList = cdDrgsPpsService.getCostCountInfo(queryParam);
        return CommonResult.success(commonObjectList);
    }

}
