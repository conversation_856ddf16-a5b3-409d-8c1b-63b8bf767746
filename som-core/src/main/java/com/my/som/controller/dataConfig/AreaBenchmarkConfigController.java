package com.my.som.controller.dataConfig;

import com.my.som.common.api.CommonPage;
import com.my.som.common.api.CommonResult;
import com.my.som.common.constant.DrgConst;
import com.my.som.common.util.ExcelUtil;
import com.my.som.dto.dataConfig.AreaBenchmarkConfigDto;
import com.my.som.service.dataConfig.AreaBenchmarkConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

@RestController
@RequestMapping("/AreaBenchmarkConfigController")
public class AreaBenchmarkConfigController {
    @Autowired
    private AreaBenchmarkConfigService areaBenchmarkConfigService;

    @RequestMapping("/queryAreaBenchmarkInfo")
    public CommonResult queryAreaBenchmarkInfo(AreaBenchmarkConfigDto dto){
        List<?> list = areaBenchmarkConfigService.queryAreaBenchmarkInfo(dto);
        return CommonResult.success(CommonPage.restPage(list));
    }

    /**
     * 区域标杆信息删除
     * @param dto
     * @return
     */
    @RequestMapping("/deleteAreaBenchmarkInfo")
    public CommonResult deleteAreaBenchmarkInfo(AreaBenchmarkConfigDto dto){
        areaBenchmarkConfigService.deleteAreaBenchmarkInfo(dto);
        return CommonResult.success();
    }

    /**
     * 区域标杆信息修改
     * @param dto
     * @return
     */
    @RequestMapping("/updateAreaBenchmarkInfo")
    public CommonResult updateAreaBenchmarkInfo(AreaBenchmarkConfigDto dto){
        areaBenchmarkConfigService.updateAreaBenchmarkInfo(dto);
        return CommonResult.success();
    }

    /**
     * 区域标杆信息新增
     * @param dto
     * @return
     */
    @RequestMapping("/insertAreaBenchmarkInfo")
    public CommonResult insertAreaBenchmarkInfo(AreaBenchmarkConfigDto dto){
        areaBenchmarkConfigService.insertAreaBenchmarkInfo(dto);
        return CommonResult.success();
    }

    /**
     * 区域标杆信息上传
     * @param file
     * @param dto
     * @return
     */
    @RequestMapping("/areaBenchmarkUpload")
    public CommonResult areaBenchmarkUpload(@RequestParam("file") MultipartFile file, AreaBenchmarkConfigDto dto){
        areaBenchmarkConfigService.areaBenchmarkUpload(file,dto.getGroup());
        return CommonResult.success();
    }

    /**
     * 下载区域标杆模板
     * @param response
     * @return
     * @throws IOException
     */
    @RequestMapping("/downloadAreaBenchmarkTemplate")
    public CommonResult downloadAreaBenchmarkTemplate(HttpServletResponse response, AreaBenchmarkConfigDto dto) {
        if (DrgConst.GROUP_TYPE_DIP.equals(dto.getGroup())){
            ExcelUtil.exportTemplateToWeb(response,"区域标杆模板", "classpath:templates/areaBenchmarkDipTemplate.xlsx");
        }else {
            ExcelUtil.exportTemplateToWeb(response,"区域标杆模板", "classpath:templates/areaBenchmarkDrgTemplate.xlsx");
        }
        return CommonResult.success();
    }


}
