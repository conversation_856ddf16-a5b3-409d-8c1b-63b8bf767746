package com.my.som.controller.medicalQuality;

import com.my.som.common.api.CommonPage;
import com.my.som.common.api.CommonResult;
import com.my.som.common.util.BeanUtil;
import com.my.som.controller.BaseController;
import com.my.som.dto.medicalQuality.*;
import com.my.som.service.medicalQuality.CompleteValidateService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 病例完整性校验Controller
 * Created by sky on 2020/3/2.
 */
@Controller
@Api(tags = "CompleteValidateController", description = "病例完整性校验")
@RequestMapping("/completeValidate")
public class CompleteValidateController extends BaseController {
    @Autowired
    private CompleteValidateService completeValidateService;

    @ApiOperation("查询所有病例完整性校对结果")
    @RequestMapping(value = "/getList", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<CommonPage<ValidateMainInfo>> getList(SettleListMainInfoQueryParam queryParam,
                                                              @RequestParam(value = "pageSize", defaultValue = "200") Integer pageSize,
                                                              @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum) {
//        BeanUtil.copyPropertiesIgnoreNull(getUserBaseInfo(), queryParam);
        List<ValidateMainInfo> list = completeValidateService.list(queryParam, pageSize, pageNum);
        return CommonResult.success(CommonPage.restPage(list));
    }

    @ApiOperation("查询病例完整性校对统计信息")
    @RequestMapping(value = "/getCountInfo", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<CompleteValidateCountInfo> getCountInfo(SettleListMainInfoQueryParam queryParam) {
//        BeanUtil.copyPropertiesIgnoreNull(getUserBaseInfo(), queryParam);
        CompleteValidateCountInfo completeValidateCountInfo = completeValidateService.getCountInfo(queryParam);
        return CommonResult.success(completeValidateCountInfo);
    }

}
