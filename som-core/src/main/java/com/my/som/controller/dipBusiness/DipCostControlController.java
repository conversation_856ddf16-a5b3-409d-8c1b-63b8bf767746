package com.my.som.controller.dipBusiness;

import com.my.som.common.api.CommonPage;
import com.my.som.common.api.CommonResult;
import com.my.som.common.util.BeanUtil;
import com.my.som.controller.BaseController;
import com.my.som.dto.dipBusiness.DipBusinessQueryDto;
import com.my.som.service.dipBusiness.DipCostControlService;
import com.my.som.vo.common.CommonObject;
import com.my.som.vo.dipBusiness.DipInGroupCostTop10Vo;
import com.my.som.vo.dipBusiness.DipInGroupNumTop10Vo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;
import java.util.Map;

/**
 * DIP分组病案信息Controller
 * Created by sky on 2020/3/23.
 */
@Controller
@Api(tags = "DipCostControlController", description = "DIP分组病案信息")
@RequestMapping("/dipCostControl")
public class DipCostControlController extends BaseController {
    @Autowired
    private DipCostControlService dipCostControlService;

    @ApiOperation("查询DIP分组结果和控费信息")
    @RequestMapping(value = "/getDipGroupAndCostControl", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<CommonPage<Map<String,Object>>> getDipGroupAndCostControl(DipBusinessQueryDto queryParam,
                                                                                  @RequestParam(value = "pageSize", defaultValue = "200") Integer pageSize,
                                                                                  @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum) {
//        BeanUtil.copyPropertiesIgnoreNull(getUserBaseInfo(), queryParam);
        List<Map<String,Object>> dipGroupAndCostControlVoList = dipCostControlService.getDipGroupAndCostControl(queryParam, pageSize, pageNum);
        return CommonResult.success(CommonPage.restPage(dipGroupAndCostControlVoList));
    }

    @ApiOperation("DIP分组人次TOP10")
    @RequestMapping(value = "/getDipGroupNumTop10", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<List<DipInGroupNumTop10Vo>> getDipGroupNumTop10(DipBusinessQueryDto queryParam) {
//        BeanUtil.copyPropertiesIgnoreNull(getUserBaseInfo(), queryParam);
        List<DipInGroupNumTop10Vo> dipInGroupNumTop10VoList = dipCostControlService.getDipGroupNumTop10(queryParam);
        return CommonResult.success(dipInGroupNumTop10VoList);
    }

    @ApiOperation("DIP分组住院费用TOP10")
    @RequestMapping(value = "/getDipGroupCostTop10", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<List<DipInGroupCostTop10Vo>> getDipGroupCostTop10(DipBusinessQueryDto queryParam) {
//        BeanUtil.copyPropertiesIgnoreNull(getUserBaseInfo(), queryParam);
        List<DipInGroupCostTop10Vo> dipInGroupCostTop10VoList = dipCostControlService.getDipGroupCostTop10(queryParam);
        return CommonResult.success(dipInGroupCostTop10VoList);
    }

    @ApiOperation("查询DIP分组费用信息")
    @RequestMapping(value = "/getCostCountInfo", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<List<CommonObject>> getCostCountInfo(DipBusinessQueryDto queryParam) {
        BeanUtil.copyPropertiesIgnoreNull(getUserBaseInfo(), queryParam);
        List<CommonObject> commonObjectList = dipCostControlService.getCostCountInfo(queryParam);
        return CommonResult.success(commonObjectList);
    }

}
