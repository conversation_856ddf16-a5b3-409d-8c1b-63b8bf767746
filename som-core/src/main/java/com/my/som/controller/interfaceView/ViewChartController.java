package com.my.som.controller.interfaceView;

import com.my.som.common.api.CommonResult;
import com.my.som.common.dto.CommonQueryDto;
import com.my.som.service.interfaceView.ViewChartService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

@RestController
@RequestMapping("/viewChartController")
public class ViewChartController {

    @Autowired
    private ViewChartService viewChartService;

    @RequestMapping("/queryViewData")
    public CommonResult queryViewData(CommonQueryDto dto) {
        String json = viewChartService.queryViewData(dto);
        return CommonResult.success(json);
    }
}
