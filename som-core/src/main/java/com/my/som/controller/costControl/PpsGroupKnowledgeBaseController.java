package com.my.som.controller.costControl;

import com.my.som.common.api.CommonPage;
import com.my.som.common.api.CommonResult;
import com.my.som.common.util.BeanUtil;
import com.my.som.controller.BaseController;
import com.my.som.dto.costControl.CostControlQueryParam;
import com.my.som.service.costControl.PpsGroupKnowledgeBaseService;
import com.my.som.vo.common.CommonObject;
import com.my.som.vo.costControl.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;

/**
 * 成都分组知识库Controller
 * Created by sky on 2020/6/16.
 */
@Controller
@Api(tags = "PpsGroupKnowledgeBaseController", description = "成都分组知识库")
@RequestMapping("/ppsGroupKnowledgeBase")
public class PpsGroupKnowledgeBaseController extends BaseController {
    @Autowired
    private PpsGroupKnowledgeBaseService ppsGroupKnowledgeBaseService;

    @ApiOperation("成都分组覆盖情况")
    @RequestMapping(value = "/getList", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<CommonPage<PpsGroupKnowledgeVo>> getList(CostControlQueryParam queryParam,
                                                               @RequestParam(value = "pageSize", defaultValue = "200") Integer pageSize,
                                                               @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum) {
//        BeanUtil.copyPropertiesIgnoreNull(getUserBaseInfo(), queryParam);
        List<PpsGroupKnowledgeVo> ppsGroupKnowledgeVoList = ppsGroupKnowledgeBaseService.list(queryParam, pageSize, pageNum);
        return CommonResult.success(CommonPage.restPage(ppsGroupKnowledgeVoList));
    }

    @ApiOperation("查询全院成都分组主诊断和分组覆盖占比")
    @RequestMapping(value = "/getCountByCoverRate", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<PpsGroupCoverRateVo> getCount(CostControlQueryParam queryParam) {
//        BeanUtil.copyPropertiesIgnoreNull(getUserBaseInfo(), queryParam);
        PpsGroupCoverRateVo ppsGroupCoverRateVo = ppsGroupKnowledgeBaseService.getCountByCoverRate(queryParam);
        return CommonResult.success(ppsGroupCoverRateVo);
    }

    @ApiOperation("查询全院成都分组大类别情况")
    @RequestMapping(value = "/getCountByGroupClass", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<List<CommonObject>> getInGroupIndex(CostControlQueryParam queryParam) {
//        BeanUtil.copyPropertiesIgnoreNull(getUserBaseInfo(), queryParam);
        List<CommonObject> commonObjectList = ppsGroupKnowledgeBaseService.getCountByGroupClass(queryParam);
        return CommonResult.success(commonObjectList);
    }

}
