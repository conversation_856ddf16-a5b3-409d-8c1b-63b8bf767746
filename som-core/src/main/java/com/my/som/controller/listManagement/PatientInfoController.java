package com.my.som.controller.listManagement;

import com.my.som.common.api.CommonPage;
import com.my.som.common.api.CommonResult;
import com.my.som.dto.listManagement.CheckAnalysisDto;
import com.my.som.dto.listManagement.PatientInfoDto;
import com.my.som.service.listManagement.PatientInfoService;
import com.my.som.vo.listManagement.CheckAnalysisVo;
import com.my.som.vo.listManagement.PatientInfoVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @author: zyd
 * @date: 2023-07-31
 **/
@RestController
@RequestMapping("/patientInfoController")
public class PatientInfoController {
    @Autowired
    private PatientInfoService patientInfoService;

    @RequestMapping("/queryPatientInfo")
    public CommonResult<CommonPage<PatientInfoVo>> queryPatientInfo(@RequestBody PatientInfoDto dto) {
        List<PatientInfoVo> list = patientInfoService.queryPatientInfo(dto);
        return CommonResult.success(CommonPage.restPage(list));
    }

    /**
     * 删除标记
     * @param dto
     * @return
     */
    @RequestMapping("/deleteSettlementMark")
    public CommonResult deleteSettlementMark(PatientInfoDto dto) {
        patientInfoService.deleteSettlementMark(dto);
        return CommonResult.success();
    }


}
