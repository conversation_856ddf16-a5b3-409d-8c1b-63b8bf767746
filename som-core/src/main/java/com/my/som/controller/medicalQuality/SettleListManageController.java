package com.my.som.controller.medicalQuality;

import com.my.som.common.api.CommonMapResult;
import com.my.som.common.api.CommonPage;
import com.my.som.common.api.CommonResult;
import com.my.som.common.constant.DrgConst;
import com.my.som.common.dto.CommonQueryDto;
import com.my.som.common.dto.ReportDto;
import com.my.som.common.util.ComputerUtil;
import com.my.som.common.util.ValidateUtil;
import com.my.som.controller.BaseController;
import com.my.som.dto.hisview.HisViewDTO;
import com.my.som.dto.medicalQuality.*;
import com.my.som.dto.patienInfo.PatienInfo;
import com.my.som.dto.pregroup.*;
import com.my.som.dto.somDiagInfo.SomDiagInfo;
import com.my.som.dto.somOprnInfo.SomOprnInfo;
import com.my.som.model.dataHandle.SomDiag;
import com.my.som.model.dataHandle.SomHiInvyBasInfo;
import com.my.som.service.hisview.HisViewService;
import com.my.som.service.medicalQuality.SettleListManageService;
import com.my.som.service.pregroup.PreGroupServiceInterface;
import com.my.som.util.GroupCommonUtil;
import com.my.som.util.SysCommonConfigUtil;
import com.my.som.vo.dataHandle.SettleListValidateVo;
import com.my.som.vo.medicalQuality.*;
import com.my.som.vo.pregroup.PreGroupVo;
import freemarker.template.TemplateException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 医保结算清单Controller
 * Created by sky on 2020/1/7.
 */
@Controller
@Api(tags = "SettleListManageController", description = "医保结算清单管理")
@RequestMapping("/settleListManage")
public class SettleListManageController extends BaseController {

    private Logger logger = LoggerFactory.getLogger(SettleListManageController.class);

    @Autowired
    private SettleListManageService settleListManageService;

    @Autowired
    private HisViewService hisViewService;

    @Autowired
    private PreGroupServiceInterface preGroupServiceInterface;


    @ApiOperation("查询医保结算清单主要信息")
    @RequestMapping(value = "/mainInfoList", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<CommonPage<BusSettleListMainInfo>> list(SettleListMainInfoQueryParam queryParam,
                                                                @RequestParam(value = "pageSize", defaultValue = "200") Integer pageSize,
                                                                @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum) {
//        BeanUtil.copyPropertiesIgnoreNull(getUserBaseInfo(), queryParam);
        List<BusSettleListMainInfo> settleList = settleListManageService.list(queryParam, pageSize, pageNum);
        return CommonResult.success(CommonPage.restPage(settleList));
    }

    @ApiOperation("根据结算清单id获取结算清单所有信息")
    @RequestMapping(value = "/getSettleListAllInfo", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<AllBusSettleListInfo> getSettleListAllInfo(@RequestParam(value = "id") String id,
                                                                   @RequestParam(value = "k00") String k00) {
        AllBusSettleListInfo allBusSettleListInfo = settleListManageService.getSettleListAllInfoById(id, k00);
        settleListManageService.setSettleLevel(allBusSettleListInfo);
        return CommonResult.success(allBusSettleListInfo);
    }


    @ApiOperation("更新结算清单数据")
    @RequestMapping(value = "/updateSettleListAllInfo", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult updateSettleListAllInfo(@RequestBody AllBusSettleListInfo settleListParam, BindingResult bindingResult) {
        int count = settleListManageService.updateSettleListAllInfo(settleListParam);
        if (count > 0) {
            return CommonResult.success(count);
        } else {
            return CommonResult.failed("操作失败！（未修改任何数据）");
        }
    }

    @ApiOperation("校验结算清单病案质量")
    @RequestMapping(value = "/validateMedical", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<ValidateMedicalResult> validateMedical(@RequestBody AllBusSettleListInfo settleListParam, BindingResult bindingResult) {
        ValidateMedicalResult validateMedicalResult = settleListManageService.getValidateMedicalResult(settleListParam);
        return CommonResult.success(validateMedicalResult);
    }

    @ApiOperation("获取该疾病参考费用、时间、入组信息")
    @RequestMapping(value = "/getStandCostAndDayAndGroupInfo", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<DrgsCostDayGroupInfo> getStandCostAndDayAndGroupInfo(@RequestBody AllBusSettleListInfo settleListParam, BindingResult bindingResult) {
        DrgsCostDayGroupInfo drgsCostDayGroupInfo = settleListManageService.getStandCostAndDayAndGroupInfo(settleListParam, getUserBaseInfo());
        return CommonResult.success(drgsCostDayGroupInfo);
    }

    @ApiOperation("查询医保结算清单主要信息（用于医生诊断辅助）")
    @RequestMapping(value = "/fetchListForDoctor", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<CommonPage<BusSettleListMainInfoForDoctor>> fetchListForDoctor(SettleListMainInfoQueryParam queryParam,
                                                                                       @RequestParam(value = "pageSize", defaultValue = "5000") Integer pageSize,
                                                                                       @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum) {
//        BeanUtil.copyPropertiesIgnoreNull(getUserBaseInfo(), queryParam);
        List<BusSettleListMainInfoForDoctor> settleList = settleListManageService.fetchListForDoctor(queryParam, getUserBaseInfo(), pageSize, pageNum);
        return CommonResult.success(CommonPage.restPage(settleList));
    }


    @RequestMapping("/deleteDataById")
    @ResponseBody
    public CommonResult deleteDataById(@RequestBody SettleListMainInfoQueryParam dto) {
        settleListManageService.deleteDataById(dto);
        return CommonResult.success();
    }


    @RequestMapping("/recoveryDataById")
    @ResponseBody
    public CommonResult recoveryDataById(@RequestBody SettleListMainInfoQueryParam dto) {
        settleListManageService.recoveryDataById(dto);
        return CommonResult.success();
    }


    @ApiOperation("根据结算清单id获取完整性错误和逻辑性错误数")
    @RequestMapping(value = "/getErrorInfo", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<AllErrorVo> getErrorInfo(@RequestParam(value = "id") Long id) {
        AllErrorVo allErrorInfo = settleListManageService.getErrorInfo(id);
        return CommonResult.success(allErrorInfo);
    }

    /**
     * 获取医保结算清单的检验结果
     *
     * @param id
     * @return
     */
    @RequestMapping("/selectBusSettleErrorLIst")
    @ResponseBody
    public CommonResult<Boolean> selectBusSettleErrorLIst(@RequestParam(value = "id") Long id,
                                                          @RequestParam(value = "k00") String k00) {
        Boolean result = settleListManageService.selectBusSettleErrorLIst(id, k00);
        return CommonResult.success(result);
    }


    /**
     * 获取医保结算清单的检验结果
     *
     * @param id
     * @return
     */
    @RequestMapping("/selectBusSettleLIstError")
    @ResponseBody
    public CommonResult<Map<String, List<BusSettleListResult>>> selectBusSettleLIstError(@RequestParam(value = "id") Long id,
                                                                                         @RequestParam(value = "k00") String k00) {
        Map<String, List<BusSettleListResult>> list = settleListManageService.selectBusSettleLIstError(id, k00);
        return CommonResult.success(list);
    }

    /**
     * 重新校验医保结算清单
     *
     * @param dto
     * @return
     */
    @RequestMapping("/restartCheck")
    @ResponseBody
    public CommonResult<Map<String, List<BusSettleListResult>>> restartCheck(@RequestBody SettleListValidateVo dto) {
        Map<String, List<BusSettleListResult>> list = settleListManageService.restartCheck(dto);
        return CommonResult.success(list);
    }

    /**
     * 修改bus_settle_list数据
     *
     * @param dto
     * @return
     */
    @RequestMapping("/updateSettleList")
    @ResponseBody
    public CommonResult<ModifyBusSettleListDto> updateSettleList(@RequestBody ModifyBusSettleListDto dto) {
        ModifyBusSettleListDto vo = settleListManageService.updateSettleList(dto);
        return CommonResult.success(vo);
    }


    /**
     * 修改bus_settle_list数据
     *
     * @param dto
     * @return
     */
    @RequestMapping("/insertHistory")
    @ResponseBody
    public CommonResult insertHistory(@RequestBody ModifyBusSettleListDto dto) {
        settleListManageService.insertHistory(dto);
        return CommonResult.success();
    }

    /**
     * 更新settleList查看历史数据状态
     *
     * @return
     */
    @RequestMapping("/updateSettleListHisState")
    @ResponseBody
    public CommonResult updateSettleListHisState(@RequestParam(value = "id") String id,
                                                 @RequestParam(value = "k00") String k00) {
        settleListManageService.updateSettleListHisState(k00);
        return CommonResult.success();
    }


    /**
     * 更新settleLists是否点击完成
     *
     * @return
     */
    @RequestMapping("/updateSettleListLookOver")
    @ResponseBody
    public CommonResult updateSettleListLookOver(ModifyBusSettleListDto dto) {
        dto.setNknm(getUserBaseInfo().getNknm());
        dto.setUserName(getUserBaseInfo().getUsername());
        settleListManageService.updateSettleListLookOver(dto);
        return CommonResult.success();
    }


    /**
     * 根据时间节点还原数据
     *
     * @param dto
     * @return
     */
    @RequestMapping("/restoreHistoryBusSettle")
    @ResponseBody
    public CommonResult restoreHistoryBusSettle(@RequestBody ModifyBusSettleListDto dto) {
        settleListManageService.restoreHistoryBusSettle(dto);
        return CommonResult.success();
    }


    /**
     * 查询bus_key_dip是否有结果判断流程是否结束
     *
     * @return
     */
    @RequestMapping("/selectProcessResult")
    @ResponseBody
    public CommonResult<AllErrorVo> selectProcessResult(@RequestParam(value = "id") String id) {
        AllErrorVo vo = settleListManageService.selectProcessResult(id);
        return CommonResult.success(vo);
    }

    /**
     * 查询bus_key_dip是否有结果判断流程是否结束
     *
     * @return
     */
    @RequestMapping("/queryICDCode")
    @ResponseBody
    public CommonResult<Map<String, Object>> queryICDCode(CommonQueryDto dto) {
        Map<String, Object> res = settleListManageService.queryICDCode(dto);
        return CommonResult.success(res);
    }

    /**
     * 结算清单
     *
     * @param dto
     * @return
     */
    @RequestMapping("/modifySettleListInfo")
    @ResponseBody
    public CommonResult<UpdateBusSettleListResultVo> modifySettleListInfo(@RequestBody ModifyDto dto) {
        dto.setUsername(getUserBaseInfo().getUsername());
        UpdateBusSettleListResultVo vo = settleListManageService.modifySettleListInfo(dto);
        return CommonResult.success(vo);
    }

    /**
     * 查询结算清单标识记录
     *
     * @param dto
     * @return
     */
    @RequestMapping("/querySettleListOpeLog")
    @ResponseBody
    public CommonResult<List<SettleListOpeLogInfo>> querySettleListOpeLog(@RequestBody ModifyBusSettleListDto dto) {
        List<SettleListOpeLogInfo> vo = settleListManageService.querySettleListOpeLog(dto);
        return CommonResult.success(vo);
    }

    /**
     * 查询结算清单标识记录
     *
     * @param dto
     * @return
     */
    @RequestMapping("/updateLockState")
    @ResponseBody
    public CommonResult updateLockState(@RequestBody ModifyBusSettleListDto dto, HttpServletRequest request) {
        setParams(dto);
        settleListManageService.updateLockState(dto);
        return CommonResult.success();
    }

    /**
     * 查询结算清单标识记录
     *
     * @param dto
     * @return
     */
    @RequestMapping("/queryLockState")
    @ResponseBody
    public CommonResult<SettleListTempLockVo> queryLockState(@RequestBody ModifyBusSettleListDto dto) {
        setParams(dto);
        return CommonResult.success(settleListManageService.queryLockState(dto));
    }

    /**
     * 设置参数
     *
     * @param dto
     */
    private void setParams(ModifyBusSettleListDto dto) {
        dto.setUserName(getUserBaseInfo().getUsername());
        dto.setIp(ComputerUtil.getIp(request));
    }

    /**
     * 查询结算清单标识记录
     *
     * @param dto
     * @return
     */
    @RequestMapping("/querySkipData")
    @ResponseBody
    public CommonResult<SomHiInvyBasInfo> querySkipData(@RequestBody ModifyBusSettleListDto dto) {
        return CommonResult.success(settleListManageService.querySkipData(dto));
    }

    /**
     * 同步结算清单数据（视图方式）
     *
     * @return
     */
    @RequestMapping("/dataSynchronization")
    @ResponseBody
    public CommonResult<SomHiInvyBasInfo> dataSynchronization(@RequestBody ModifyBusSettleListDto modifyBusSettleListDto) {
        logger.info("===============同步视图调用=" + modifyBusSettleListDto.getK00());
        String uniqueId = modifyBusSettleListDto.getK00();
        if (ValidateUtil.isNotEmpty(uniqueId)) {
            HisViewDTO hisViewDTO = new HisViewDTO();
            hisViewDTO.setUniqueId(uniqueId);
            hisViewDTO.setMedcasno(modifyBusSettleListDto.getMedcasno());
            hisViewDTO.setMdtrtId(modifyBusSettleListDto.getMdtrtId());
            hisViewService.mdcsInfoExtract(hisViewDTO);
        }
        return CommonResult.success();
    }

    /**
     * 预览文件
     *
     * @param response
     * @throws IOException
     * @throws TemplateException
     */
    @GetMapping(value = "/preview")
    public void preview(ReportDto dto, HttpServletResponse response) {
        settleListManageService.preview(dto, response);
    }

    @ApiOperation("查询出院诊断code信息")
    @GetMapping("/getDiag")
    @ResponseBody
    public CommonResult<List> diagList( String query){
        if(query != null) {
            List<SomDiagInfo> diagInfoList = settleListManageService.queryDiagInfo(query);
            if (!diagInfoList.isEmpty()) {
                return CommonResult.success(diagInfoList);
            }else{
                return CommonResult.failed("输入编码不符医保2.0");
            }
        }
        return null;
    }

    @ApiOperation("查询出院诊断name信息")
    @GetMapping("/getDiagName")
    @ResponseBody
    public CommonResult<List> diagNameList( String query){

        if(query != null) {
            List<SomDiagInfo> diagInfoList = settleListManageService.queryDiagNameInfo(query);
            if (!diagInfoList.isEmpty()) {
                return CommonResult.success(diagInfoList);
            }else{
                return CommonResult.failed("输入名称不符医保2.0");
            }
        }
        return null;
    }

    @ApiOperation("查询手术code信息")
    @GetMapping("/getOprns")
    @ResponseBody
    public CommonResult<List> oprnList(String query){
        if(query != null) {
            List<SomOprnInfo> oprnInfos = settleListManageService.queryOprnInfo(query);
            if (!oprnInfos.isEmpty()) {
                return CommonResult.success(oprnInfos);
            }else{
                return CommonResult.failed("输入编码不符医保2.0");
            }
        }
        return null;
    }

    @ApiOperation("查询手术name信息")
    @GetMapping("/getOprnsName")
    @ResponseBody
    public CommonResult<List> oprnNameList(String query){
        if(query != null) {
            List<SomOprnInfo> oprnInfos = settleListManageService.queryOprnNameInfo(query);
            if (!oprnInfos.isEmpty()) {
                return CommonResult.success(oprnInfos);
            }else{
                return CommonResult.failed("输入名称不符医保2.0");
            }
        }
        return null;
    }

    @ApiOperation("获取医院编码信息")
    @GetMapping("/getHosInfo")
    @ResponseBody
    public CommonResult getHosInfo(){
        return CommonResult.success(settleListManageService.queryHosInfo());
    }

    @ApiOperation("查询模拟预分组结果")
    @PostMapping("/getPreGroupResult")
    @ResponseBody
    public CommonResult preGroupResult(@RequestBody PatienInfo patienInfo,HttpServletRequest request,HttpServletResponse response){
        handleInsuplc(patienInfo);
        PreGroupVo groups =  settleListManageService.getSimlatePreGroup(patienInfo,request,response);
        if(groups.getDrgCodg() != null || groups.getDrgCodg() != "" ||groups.getDipCodg() != null ||groups.getDipCodg() != "")
            return CommonResult.success(groups);
        else
            return CommonResult.failed("入组失败,请检查输入信息");
    }

    private void handleInsuplc(PatienInfo patienInfo) {
        String provLevelInsuplcAdmdvs =
                ValidateUtil.isEmpty(GroupCommonUtil.getInsuplcAdmdvs())?"0000":GroupCommonUtil.getInsuplcAdmdvs();
        String insuplc =  patienInfo.getInsuplc();
        String insuPlaceType = (String) SysCommonConfigUtil.get(DrgConst.INSUPLC_ADMDVS_AREA);
        switch (insuplc){
            case "0":
                insuplc=insuPlaceType;
                break;
            case "1":
                insuplc=insuPlaceType.substring(0,2) + "00";
                break;
            case "2":
                insuplc= "0000";
                break;
            case "3":
                insuplc= provLevelInsuplcAdmdvs;
                break;
        }
        patienInfo.setInsuplc(insuplc);
    }

    @ApiOperation("查询特病单议")
    @RequestMapping(value = "/querySpecialDisease", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<CommonPage<BusSettleListMainInfo>> querySpecialDisease(SettleListMainInfoQueryParam queryParam,
                                                                @RequestParam(value = "pageSize", defaultValue = "200") Integer pageSize,
                                                                @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum) {
//        BeanUtil.copyPropertiesIgnoreNull(getUserBaseInfo(), queryParam);
        CommonPage<BusSettleListMainInfo> settleList = settleListManageService.querySpecialDisease(queryParam, pageSize, pageNum);
        return CommonResult.success(settleList);
    }

    @ApiOperation("查询特病单议地区码")
    @RequestMapping(value = "/getSpecialDiseaseType", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<String> getSpecialDiseaseType() {
        String place= settleListManageService.getSpecialDiseaseType();
        return CommonResult.success(place);
    }
}
