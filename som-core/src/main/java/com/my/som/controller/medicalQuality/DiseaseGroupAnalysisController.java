package com.my.som.controller.medicalQuality;

import com.my.som.common.api.CommonPage;
import com.my.som.common.api.CommonResult;
import com.my.som.dto.medicalQuality.DiseaseGroupAnalysisDto;
import com.my.som.service.medicalQuality.DiseaseGroupAnalysisService;
import com.my.som.vo.medicalQuality.DiseaseGroupAnalysisVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("diseaseGroupAnalysisController")
public class DiseaseGroupAnalysisController {
    @Autowired
    private DiseaseGroupAnalysisService diseaseGroupAnalysisService;
    /**
     * 查询表格数据
     * @param dto
     * @return
     */
    @RequestMapping("/getList")
    public CommonResult<CommonPage<DiseaseGroupAnalysisVo>> getList(@RequestBody DiseaseGroupAnalysisDto dto,
                                                                     @RequestParam(value = "pageSize", defaultValue = "200") Integer pageSize,
                                                                    @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum){
        List<DiseaseGroupAnalysisVo> list= diseaseGroupAnalysisService.getList(dto, pageSize, pageNum);
        return CommonResult.success(CommonPage.restPage(list));
    }

    /**
     * 查询基本数据
     * @param dto
     * @return
     */
    @RequestMapping("/getInfo")
    public CommonResult<List<DiseaseGroupAnalysisVo>> getInfo(@RequestBody DiseaseGroupAnalysisDto dto){
        List<DiseaseGroupAnalysisVo> list=diseaseGroupAnalysisService.getInfo(dto);
        return CommonResult.success(list);
    }

    /**
     * 查询人员入组数据
     * @param dto
     * @return
     */
    @RequestMapping("/getInGroup")
    public CommonResult<List<DiseaseGroupAnalysisVo>> getIsInGroup(@RequestBody DiseaseGroupAnalysisDto dto){
        List<DiseaseGroupAnalysisVo> list=diseaseGroupAnalysisService.getIsInGroup(dto);
        return CommonResult.success(list);
    }

    /**
     * 查询基本费用数据
     * @param dto
     * @return
     */
    @RequestMapping("/getCostPay")
    public CommonResult<List<DiseaseGroupAnalysisVo>> getCostPay(@RequestBody DiseaseGroupAnalysisDto dto){
        List<DiseaseGroupAnalysisVo> list=diseaseGroupAnalysisService.getCostPay(dto);
        return CommonResult.success(list);
    }
    /**
     * 查询费用数据
     * @param dto
     * @return
     */
    @RequestMapping("/getCost")
    public CommonResult<List<DiseaseGroupAnalysisVo>> getCost(@RequestBody DiseaseGroupAnalysisDto dto){
        List<DiseaseGroupAnalysisVo> list=diseaseGroupAnalysisService.getCost(dto);
        return CommonResult.success(list);
    }
}
