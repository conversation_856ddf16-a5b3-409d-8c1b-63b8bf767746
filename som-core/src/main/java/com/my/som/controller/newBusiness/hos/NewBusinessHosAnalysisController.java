package com.my.som.controller.newBusiness.hos;

import com.my.som.common.api.CommonMapResult;
import com.my.som.common.api.CommonResult;
import com.my.som.dto.newBusiness.NewBusinessCommonDto;
import com.my.som.dto.newBusiness.dept.NewBusinessDeptDto;
import com.my.som.service.newBusiness.hos.NewBusinessHosAnalysisService;
import com.my.som.vo.dipBusiness.DipInGroupAnalysisVo;
import com.my.som.vo.newBusiness.NewBusinessAnalysisVo;
import com.my.som.vo.newBusiness.NewBusinessCommonVo;
import com.my.som.vo.newBusiness.dept.NewBusinessDeptVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.beans.IntrospectionException;
import java.lang.reflect.InvocationTargetException;

/**
 * @author: zyd
 * @version 1.0
 * @date: 2023-07-31
 * @description:
 */
@RestController
@RequestMapping("/newBusinessHosAnalysisController")
public class NewBusinessHosAnalysisController {


    @Autowired
    private NewBusinessHosAnalysisService newBusinessHosAnalysisService;

    /**
     * 查询头部汇总数据
     * @param dto
     * @return
     */
    @RequestMapping("querySummaryData")
    public CommonResult<NewBusinessDeptVo> querySummaryData(NewBusinessDeptDto dto){
        NewBusinessDeptVo vo = newBusinessHosAnalysisService.querySummaryData(dto);
        return CommonResult.success(vo);
    }

    /**
     * 查询错误病例
     * @param dto
     * @return
     */
    @RequestMapping("queryErrorData")
    public CommonResult<DipInGroupAnalysisVo> queryErrorData(NewBusinessCommonDto dto) throws IntrospectionException, InvocationTargetException, IllegalAccessException {
        DipInGroupAnalysisVo vo = newBusinessHosAnalysisService.queryErrorData(dto);
        return CommonResult.success(vo);
    }

    /**
     * 查询排序数据
     * @param dto
     * @return
     */
    @RequestMapping("queryOrderData")
    public CommonResult<NewBusinessAnalysisVo> queryOrderData(NewBusinessCommonDto dto){
        NewBusinessAnalysisVo vo = newBusinessHosAnalysisService.queryOrderData(dto);
        return CommonResult.success(vo);
    }

    /**
     * 查询趋势分析
     */
    @RequestMapping("queryTrendData")
    public CommonMapResult queryTrendData(NewBusinessDeptDto dto){
        CommonMapResult result = newBusinessHosAnalysisService.queryTrendData(dto);
        return result;
    }
}
