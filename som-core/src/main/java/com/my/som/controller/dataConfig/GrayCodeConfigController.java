package com.my.som.controller.dataConfig;

import com.my.som.common.api.CommonPage;
import com.my.som.common.api.CommonResult;
import com.my.som.dto.dataConfig.GrayCodeDto;
import com.my.som.dto.dataConfig.TpdStandardOperationDto;
import com.my.som.model.dataConfig.SomOprnLv;
import com.my.som.service.dataConfig.GrayCodeConfigService;
import com.my.som.service.dataConfig.TpdStandardOperationService;
import com.my.som.vo.common.GrayCodeVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 灰码配置
 *
 * @author: zyd
 * @date: 2023-07-31
 */
@RestController
@Api(tags = "GrayCodeConfigController", description = "灰码管理")
@RequestMapping("/grayCode")
public class GrayCodeConfigController {

    @Autowired
    private GrayCodeConfigService grayCodeConfigService;

    @ApiOperation("查询灰码")
    @RequestMapping(value = "/queryDiagGrayCodeList", method = RequestMethod.POST)
    public CommonResult<CommonPage<GrayCodeVo>> queryDiagGrayCodeList(GrayCodeDto grayCodeDto,
                                                                      @RequestParam(value = "pageSize", defaultValue = "200") Integer pageSize,
                                                                      @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum) {
        List<GrayCodeVo> diagGrayCodeList = grayCodeConfigService.queryDiagGrayCodeList(grayCodeDto, pageSize, pageNum);
        return CommonResult.success(CommonPage.restPage(diagGrayCodeList));
    }

    @PostMapping(value = "/saveGrayCode")
    public CommonResult save(@RequestBody GrayCodeDto grayCodeDto) {
        return CommonResult.success(grayCodeConfigService.save(grayCodeDto));
    }

    @PostMapping(value = "/deleteGrayCode")
    public CommonResult delete(@RequestParam("id") String id) {
        return CommonResult.success(grayCodeConfigService.delete(id));
    }

}
