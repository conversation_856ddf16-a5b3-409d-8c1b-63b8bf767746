package com.my.som.controller.firstPage;

import com.my.som.common.api.CommonResult;
import com.my.som.controller.BaseController;
import com.my.som.dto.firstpage.FirstPageDto;
import com.my.som.service.firstPage.FirstPage3Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @author: zyd
 * @Date 2023/1/28 5：27 下午
 * @Version
 * @Description:获取首页相关数据
 */


@RestController
@RequestMapping("/firstPage3")
public class FirstPage3Controller extends BaseController {
    @Autowired
    private FirstPage3Service firstPage3Service;

    /**
     * @description:汇总数据信息
     * @param dto
     * @return 预测/反馈和病例数相关数据
     */
    @RequestMapping(path = "/getSummaryInfo")
    public CommonResult getCostInfo(FirstPageDto dto){
        setUserInfo(dto);
        return CommonResult.success(firstPage3Service.getSummaryInfo(dto));
    }

    /**
     * @description:汇总数据信息
     * @param dto
     * @return 清单上传数据
     */
    @RequestMapping(path = "/getSettleListUploadList")
    public CommonResult getSettleListUploadList(FirstPageDto dto){
        return CommonResult.success(firstPage3Service.getSettleListUploadList(dto));
    }

    /**
     * @description:统计科室清单修改
     * @param dto
     * @return
     */
    @RequestMapping(path = "/countSettleListModify")
    public CommonResult countSettleListModify(FirstPageDto dto){

        return CommonResult.success(firstPage3Service.countSettleListModify(dto));
    }

    /**
     * @description:科室亏损排名
     * @param dto
     * @return
     */
    @RequestMapping(path = "/queryOrderData")
    public CommonResult queryOrderData(FirstPageDto dto){
        return CommonResult.success(firstPage3Service.queryOrderData(dto));
    }

    /**
     * 查询待办信息
     * @param dto
     * @return
     */
    @RequestMapping(path = "/queryTodo")
    public CommonResult<?> queryTodo(FirstPageDto dto){
        return CommonResult.success(firstPage3Service.queryTodo(dto));
    }

    /**
     * 设置用户信息
     * @param dto
     */
    private void setUserInfo(FirstPageDto dto){
        dto.setUsername(getUserBaseInfo().getUsername());
    }


}
