package com.my.som.controller.upload;

import com.alibaba.excel.EasyExcel;
import com.my.som.common.api.CommonResult;
import com.my.som.common.constant.DrgConst;
import com.my.som.common.exception.AppException;
import com.my.som.controller.BaseController;
import com.my.som.dto.upload.FileUploadDto;
import com.my.som.dto.upload.WorkersUploadDto;
import com.my.som.listener.upload.WorkersFileUploadListener;
import com.my.som.service.upload.WorkersUploadService;
import com.my.som.common.util.ExcelUtil;
import com.my.som.vo.upload.FileUploadVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

@RestController
@RequestMapping("/workersUploadController")
public class WorkersUploadController extends BaseController {

    @Autowired
    private WorkersUploadService workersUploadService;

    /**
     * 医护人员数据上传
     * @param file excel 文件
     * @return
     * @throws IOException
     */
    @RequestMapping("/workersUpload")
    public CommonResult workersdUpload(@RequestParam("file") MultipartFile file, FileUploadDto dto1) throws IOException {
        dto1.setName(file.getOriginalFilename());
        dto1.setType(DrgConst.FILE_UPLOAD_TYPE_DEPT_2);
        try {
            dto1.setState(DrgConst.START_FLAG_1);
            EasyExcel.read(file.getInputStream(), WorkersUploadDto.class, new WorkersFileUploadListener(workersUploadService, dto1)).sheet().doRead();
        } catch (Exception e){
            dto1.setErrMsg(e.getCause().getLocalizedMessage());
            dto1.setState(DrgConst.START_FLAG_0);
            throw new AppException(e.getCause().getLocalizedMessage());
        } finally {
            workersUploadService.saveLog(dto1);
        }
        return CommonResult.success();
    }
    /**
     * 下载模板
     * @return
     */
    @RequestMapping("/downloadworkersTemplate")
    public void downloadworkersTemplate(HttpServletResponse response) throws IOException {
        ExcelUtil.exportworkersToWeb(response,"医护人员信息模板",getUserBaseInfo());
    }


    /**
     * 查询医护人员文件上传日志
     * @param dto
     * @return
     * @throws IOException
     */
    @RequestMapping("/workersFileUploadLog")
    public CommonResult<List<FileUploadVo>> workersFileUploadLog(FileUploadDto dto) throws IOException {
        List<FileUploadVo> list = workersUploadService.workersFileUploadLog(dto);
        return CommonResult.success(list);
    }

}
