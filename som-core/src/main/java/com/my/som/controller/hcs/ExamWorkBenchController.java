package com.my.som.controller.hcs;

import com.my.som.common.api.CommonPage;
import com.my.som.common.api.CommonResult;
import com.my.som.hcs.engine.dto.MedWorkBenchQueryDto;
import com.my.som.hcs.engine.vo.workbench.ViolationItemDetilVo;
import com.my.som.hcs.engine.vo.workbench.WorkBenchGraphicResultVo;
import com.my.som.service.hcs.ExamCorrectionResultService;
import com.my.som.service.hcs.RunHcsDataProcessService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;

@Controller
@Api(tags = "ExamWorkBenchController", description = "自查自纠工作台查询控制层")
@RequestMapping("/examWorkBenchController")
public class ExamWorkBenchController {
    @Autowired
    private ExamCorrectionResultService examCorrectionResultService;

    @Autowired
    private RunHcsDataProcessService runHcsDataProcessService;

    @ApiOperation("查询自查自纠工作台图形化数据")
    @RequestMapping(value = "/queryAnalysisGraphic", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<WorkBenchGraphicResultVo> queryAnalysisGraphic(MedWorkBenchQueryDto queryParam) {
        WorkBenchGraphicResultVo workBenchGraphicResultVo = runHcsDataProcessService.queryAnalysisGraphic(queryParam);
        return CommonResult.success(workBenchGraphicResultVo);
    }

    @ApiOperation("查询自查自纠工作台数据报表")
    @RequestMapping(value = "/queryAnalysisDataList", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<CommonPage<ViolationItemDetilVo>> queryAnalysisDataList(MedWorkBenchQueryDto queryParam) {
        List<ViolationItemDetilVo> violationItemDetilVos = runHcsDataProcessService.queryAnalysisDataList(queryParam);
        return CommonResult.success(CommonPage.restPage(violationItemDetilVos));
    }
}
