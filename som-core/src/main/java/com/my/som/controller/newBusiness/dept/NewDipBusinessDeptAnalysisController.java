package com.my.som.controller.newBusiness.dept;

import com.my.som.common.api.CommonPage;
import com.my.som.common.api.CommonResult;
import com.my.som.dto.newBusiness.dept.NewBusinessDeptDto;
import com.my.som.service.newBusiness.dept.NewDipBusinessDeptAnalysisService;
import com.my.som.vo.newBusiness.dept.NewBusinessDeptVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @author: zyd
 * @version 1.0
 * @date: 2023-07-31
 * @description:
 */
@RestController
@RequestMapping("/newDipBusinessDeptAnalysisController")
public class NewDipBusinessDeptAnalysisController {

    @Autowired
    private NewDipBusinessDeptAnalysisService newDipBusinessDeptAnalysisService;

    /**
     * 查询科室Kpi
     * @return
     */
    @RequestMapping("queryKpiData")
    public CommonResult<CommonPage<NewBusinessDeptVo>> queryKpiData(NewBusinessDeptDto dto){
        List<NewBusinessDeptVo> list = newDipBusinessDeptAnalysisService.queryKpiData(dto);
        return CommonResult.success(CommonPage.restPage(list));
    }

    /**
     * 查询科室预测
     * @return
     */
    @RequestMapping("queryForecastData")
    public CommonResult<CommonPage<NewBusinessDeptVo>> queryForecastData(NewBusinessDeptDto dto){
        List<NewBusinessDeptVo> list = newDipBusinessDeptAnalysisService.queryForecastData(dto);
        return CommonResult.success(CommonPage.restPage(list));
    }
}
