package com.my.som.controller.conditionalOptions;

import com.my.som.common.api.CommonResult;
import com.my.som.controller.BaseController;
import com.my.som.dto.conditionalOptions.ConditionalOptionsDto;
import com.my.som.service.conditionalOptions.ConditionalOptionsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/conditionalOptionsController")
public class ConditionalOptionsController extends BaseController {
//
//    @Autowired
//    private ConditionalOptionsService conditionalOptionsService;
//    /**
//     * 根据条件查询数据
//     * @param dto
//     * @return
//     */
//    @RequestMapping("/getData")
//    public CommonResult getData(ConditionalOptionsDto dto){
////        List<String> queryConditions = dto.getQueryConditions();
//        conditionalOptionsService.getData(dto);
//        return CommonResult.success();
//    }
}
