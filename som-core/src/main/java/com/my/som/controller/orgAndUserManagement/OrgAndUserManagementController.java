package com.my.som.controller.orgAndUserManagement;

import com.my.som.common.api.CommonDictResult;
import com.my.som.common.api.CommonResult;
import com.my.som.common.constant.DrgConst;
import com.my.som.common.util.DictOperationUtil;
import com.my.som.common.vo.SomBackUser;
import com.my.som.controller.BaseController;
import com.my.som.model.orgAndUserManagement.SomBasDept;
import com.my.som.model.orgAndUserManagement.BaseOrgNode;
import com.my.som.service.orgAndUserManagement.OrgAndUserManagementService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import java.util.List;

/**
 * @author: zyd
 * Case:          drg
 * FileName:      OrgAndUserManagementController
 * Date:          2020/1/9 10:23
 * Description:   组织人员管理
 * Ver:       1.0
 * Copyright (C),2012-2022 Freedom
 */
@RestController
@Api(tags = "OrgAndUserManagementController",description = "组织人员管理")
@RequestMapping("/org")
public class OrgAndUserManagementController extends BaseController {
    @Autowired
    private OrgAndUserManagementService orgAndUserManagementService;

    /**
     * 获取组织机构列表，组装成树状结构
     * @Param code_types 码表所需列表
     * @return
     */
    @ApiOperation("获取组织机构树形结构")
    @RequestMapping(value = "somBasDept/getListTree",method = RequestMethod.POST)
    public CommonDictResult<List<BaseOrgNode>> getListTree(@RequestParam(value = "codeKeys", defaultValue = "") String codeKeys){
        String hospitalId = getUserBaseInfo().getHospitalId();
        return CommonDictResult.success(orgAndUserManagementService.getListTree(hospitalId),DictOperationUtil.getDictList(codeKeys,DrgConst.TYPE_1));
    }

    /**
     * 新增组织
     * @param somBasDept
     * @return
     */
    @ApiOperation("新增组织")
    @RequestMapping(value = "somBasDept/addOrg",method = RequestMethod.POST)
    public CommonResult<BaseOrgNode> addOrg(@RequestBody SomBasDept somBasDept){
        getUserBaseInfo();
        SomBackUser somBackUser = getUserInfo();
        return CommonResult.success(orgAndUserManagementService.addOrg(somBasDept,somBackUser));
    }

    /**
     * 删除组织
     * 删除组织应该是移除组织以及子级下的所有人组织及人员
     * @param somBasDept
     * @return
     */
    @ApiOperation("删除组织")
    @RequestMapping(value = "somBasDept/removeOrg",method = RequestMethod.POST)
    public CommonResult<BaseOrgNode> removeOrg(@RequestBody SomBasDept somBasDept){
        SomBackUser somBackUser = getUserInfo();
        return CommonResult.success(orgAndUserManagementService.removeOrg(somBasDept,somBackUser));
    }

    /**
     * 编辑组织
     * @param somBasDept
     * @return
     */
    @ApiOperation("编辑组织")
    @RequestMapping(value = "somBasDept/editOrg",method = RequestMethod.POST)
    public CommonResult<BaseOrgNode> updateOrg(@RequestBody SomBasDept somBasDept){
        SomBackUser somBackUser = getUserInfo();
        return CommonResult.success(orgAndUserManagementService.updateOrg(somBasDept,somBackUser));
    }
    /**
     * 抽取科室
     * @param somBasDept
     * @return
     */
    @RequestMapping(value = "/extractDept",method = RequestMethod.POST)
    public CommonResult<BaseOrgNode> extractDept(@RequestBody SomBasDept somBasDept){
        orgAndUserManagementService.extractDept(somBasDept, getUserBaseInfo());
        return CommonResult.success();
    }
}
