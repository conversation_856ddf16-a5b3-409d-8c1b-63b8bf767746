package com.my.som.controller.dipBusiness;

import com.my.som.common.api.CommonPage;
import com.my.som.common.api.CommonResult;
import com.my.som.common.util.BeanUtil;
import com.my.som.controller.BaseController;
import com.my.som.dto.dipBusiness.DipBusinessQueryDto;
import com.my.som.service.dipBusiness.DipDeptIndexService;
import com.my.som.vo.dipBusiness.DipDeptCountVo;
import com.my.som.vo.dipBusiness.DipDeptIndexVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;

/**
 * 成都分组科室指标Controller
 * Created by sky on 2020/7/13.
 */
@Controller
@Api(tags = "DipDeptIndexController", description = "科室病组指标")
@RequestMapping("/dipDeptIndex")
public class DipDeptIndexController extends BaseController {
    @Autowired
    private DipDeptIndexService dipDeptIndexService;

    @ApiOperation("查询科室病组指标信息")
    @RequestMapping(value = "/getList", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<CommonPage<DipDeptIndexVo>> list(DipBusinessQueryDto queryParam,
                                                         @RequestParam(value = "pageSize", defaultValue = "200") Integer pageSize,
                                                         @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum) {
//        BeanUtil.copyPropertiesIgnoreNull(getUserBaseInfo(), queryParam);
        List<DipDeptIndexVo> dipDeptIndexVoList = dipDeptIndexService.list(queryParam, pageSize, pageNum);
        return CommonResult.success(CommonPage.restPage(dipDeptIndexVoList));
    }

    @ApiOperation("查询科室病组指标统计信息")
    @RequestMapping(value = "/getCountInfo", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<List<DipDeptCountVo>> getCountInfo(DipBusinessQueryDto queryParam) {
//        BeanUtil.copyPropertiesIgnoreNull(getUserBaseInfo(), queryParam);
        List<DipDeptCountVo> dipDeptCountVoList = dipDeptIndexService.getCountInfo(queryParam);
        return CommonResult.success(dipDeptCountVoList);
    }

    @ApiOperation("查询指数详情信息")
    @RequestMapping(value = "/queryConsumptionIndex", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult queryConsumptionIndex(DipBusinessQueryDto queryParam) {
//        BeanUtil.copyPropertiesIgnoreNull(getUserBaseInfo(), queryParam);
        List<DipDeptIndexVo> dipDeptIndexVoList = dipDeptIndexService.queryConsumptionIndex(queryParam);
        return CommonResult.success(dipDeptIndexVoList);
    }
}
