package com.my.som.controller.dipBusiness;

import com.my.som.common.api.CommonPage;
import com.my.som.common.api.CommonResult;
import com.my.som.common.util.BeanUtil;
import com.my.som.controller.BaseController;
import com.my.som.dto.dipBusiness.HospitalAnalysisDto;
import com.my.som.service.dipBusiness.DipInGroupAnalysisService;
import com.my.som.vo.dipBusiness.DipInGroupAnalysisCountVo;
import com.my.som.vo.dipBusiness.DipInGroupAnalysisVo;
import com.my.som.vo.dipBusiness.DipInGroupTopCountVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;

/**
 * 医院DIP入组分析
 * @Date 2021-09-06 10:12
 */
@Controller
@Api(tags = "dipInGroupAnalysisController", description = "医院DIP入组分析")
//@RequestMapping("/dipInGroupAnalysis")
public class DipInGroupAnalysisController extends BaseController {
    @Autowired(required = true)
    private DipInGroupAnalysisService dipInGroupAnalysisService;

    @ApiOperation("查询医院DIP入组分析主要信息")
//    @RequestMapping(value = "/mainInfoList", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<CommonPage<DipInGroupAnalysisVo>> list(HospitalAnalysisDto dto,
                                                               @RequestParam(value = "pageSize", defaultValue = "200") Integer pageSize,
                                                               @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum) {
//        BeanUtil.copyPropertiesIgnoreNull(getUserBaseInfo(),dto);
        List<DipInGroupAnalysisVo> dipInGroupAnalysisVoList = dipInGroupAnalysisService.list(dto,pageSize,pageNum);
        return CommonResult.success(CommonPage.restPage(dipInGroupAnalysisVoList));
    }



    @ApiOperation("查询全院入组情况人次统计信息")
//    @RequestMapping(value = "/getTopCountInfo", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<DipInGroupTopCountVo> getTopCountInfo(HospitalAnalysisDto dto) {
        BeanUtil.copyPropertiesIgnoreNull(getUserBaseInfo(),dto);
        DipInGroupTopCountVo topCountInfo = dipInGroupAnalysisService.getTopCountInfo(dto);
        return CommonResult.success(topCountInfo);
    }


    @ApiOperation("查询未入组原因统计信息")
//    @RequestMapping(value = "/getNoGroupResonCountInfo", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<List<DipInGroupAnalysisCountVo>> getNoGroupResonCountInfo(HospitalAnalysisDto dto) {
        BeanUtil.copyPropertiesIgnoreNull(getUserBaseInfo(), dto);
        List<DipInGroupAnalysisCountVo> inGroupAnalysisCountInfoList = dipInGroupAnalysisService.getNoGroupResonCountInfo(dto);
        return CommonResult.success(inGroupAnalysisCountInfoList);
    }

}
