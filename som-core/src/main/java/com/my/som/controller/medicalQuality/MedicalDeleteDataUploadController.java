package com.my.som.controller.medicalQuality;

import com.my.som.common.api.CommonResult;
import com.my.som.dto.upload.MedicalRecordUploadDto;
import com.my.som.service.medicalQuality.MedicalDeleteDataUploadService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;

@RestController
@RequestMapping("/medicalDeleteDataUploadController")
public class MedicalDeleteDataUploadController {

    @Autowired
    private MedicalDeleteDataUploadService medicalDeleteDataUploadService;

    @RequestMapping("/medicalDeleteDataUpload")
    public CommonResult medicalDeleteDataUpload(@RequestParam("file") MultipartFile file, MedicalRecordUploadDto dto) throws IOException {
        medicalDeleteDataUploadService.medicalDeleteDataUpload(file,dto);
        return CommonResult.success();
    }
}
