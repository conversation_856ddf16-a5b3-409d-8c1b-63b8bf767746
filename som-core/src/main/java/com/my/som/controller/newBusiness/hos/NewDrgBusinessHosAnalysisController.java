package com.my.som.controller.newBusiness.hos;

import com.my.som.common.api.CommonMapResult;
import com.my.som.common.api.CommonResult;
import com.my.som.dto.newBusiness.NewBusinessCommonDto;
import com.my.som.dto.newBusiness.dept.NewBusinessDeptDto;
import com.my.som.service.newBusiness.hos.NewDrgBusinessHosAnalysisService;
import com.my.som.vo.dipBusiness.DipInGroupAnalysisVo;
import com.my.som.vo.newBusiness.NewBusinessAnalysisVo;
import com.my.som.vo.newBusiness.NewBusinessCommonVo;
import com.my.som.vo.newBusiness.dept.NewBusinessDeptVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.beans.IntrospectionException;
import java.lang.reflect.InvocationTargetException;
import java.util.List;
import java.util.Map;

/**
 * @author: zyd
 * @version 1.0
 * @date: 2023-07-31
 * @description:
 */
@RestController
@RequestMapping("/newDrgBusinessHosAnalysisController")
public class NewDrgBusinessHosAnalysisController {


    @Autowired
    private NewDrgBusinessHosAnalysisService newDrgBusinessHosAnalysisService;

    /**
     * 查询头部汇总数据
     * @param dto
     * @return
     */
    @RequestMapping("querySummaryData")
    public CommonResult<NewBusinessDeptVo> querySummaryData(NewBusinessDeptDto dto){
        NewBusinessDeptVo vo = newDrgBusinessHosAnalysisService.querySummaryData(dto);
        return CommonResult.success(vo);
    }

    /**
     * 查询错误病例
     * @param dto
     * @return
     */
    @RequestMapping("queryErrorData")
    public CommonResult<DipInGroupAnalysisVo> queryErrorData(NewBusinessCommonDto dto) throws IntrospectionException, InvocationTargetException, IllegalAccessException {
        DipInGroupAnalysisVo vo = newDrgBusinessHosAnalysisService.queryErrorData(dto);
        return CommonResult.success(vo);
    }

    /**
     * 查询排序数据
     * @param dto
     * @return
     */
    @RequestMapping("queryOrderData")
    public CommonResult<NewBusinessAnalysisVo> queryOrderData(NewBusinessCommonDto dto){
        NewBusinessAnalysisVo vo = newDrgBusinessHosAnalysisService.queryOrderData(dto);
        return CommonResult.success(vo);
    }

    /**
     * 查询趋势分析
     * @param dto
     * @return
     */
    @RequestMapping("queryTrendData")
    public CommonMapResult queryTrendData(NewBusinessDeptDto dto){
        CommonMapResult result = newDrgBusinessHosAnalysisService.queryTrendData(dto);
        return result;
    }

    /**
     * 查询象限图数据
     * @param dto
     * @return
     */
    @RequestMapping("/queryQuadrantData")
    public CommonResult<List<NewBusinessDeptVo>> queryQuadrantData(NewBusinessCommonDto dto){
        List<NewBusinessDeptVo> list = newDrgBusinessHosAnalysisService.queryQuadrantData(dto);
        return CommonResult.success(list);
    }

    /**
     * 查询drg医生象限图数据
     * @param dto
     * @return
     */
    @RequestMapping("/queryQuadrantDrgDoctorData")
    public CommonResult<List<NewBusinessDeptVo>> queryQuadrantDrgDoctorData(NewBusinessCommonDto dto){
        List<NewBusinessDeptVo> list = newDrgBusinessHosAnalysisService.queryQuadrantDrgDoctorData(dto);
        return CommonResult.success(list);
    }


    /**
     * 查询dip象限图数据
     * @param dto
     * @return
     */
    @RequestMapping("/queryQuadrantDipData")
    public CommonResult<List<NewBusinessDeptVo>> queryQuadrantDipData(NewBusinessCommonDto dto){
        List<NewBusinessDeptVo> list = newDrgBusinessHosAnalysisService.queryQuadrantDipData(dto);
        return CommonResult.success(list);
    }

    /**
     * 查询dip医生象限图数据
     * @param dto
     * @return
     */
    @RequestMapping("/queryQuadrantDipDoctorData")
    public CommonResult<List<NewBusinessDeptVo>> queryQuadrantDipDoctorData(NewBusinessCommonDto dto){
        List<NewBusinessDeptVo> list = newDrgBusinessHosAnalysisService.queryQuadrantDipDoctorData(dto);
        return CommonResult.success(list);
    }

    /**
     * 查询cmi，时耗，费耗
     * @param dto
     * @return
     */
    @RequestMapping("/queryEntireAssessment")
    public CommonMapResult queryEntireAssessment (NewBusinessDeptDto dto ){
        CommonMapResult result = newDrgBusinessHosAnalysisService.queryEntireAssessment(dto);
        return result;
    }
}
