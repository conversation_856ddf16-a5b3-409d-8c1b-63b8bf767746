package com.my.som.controller.dataHandle;


import com.my.som.common.api.CommonPage;
import com.my.som.common.api.CommonResult;
import com.my.som.common.exception.AppException;
import com.my.som.common.util.ValidateUtil;
import com.my.som.common.vo.SomBackUser;
import com.my.som.config.DynamicSplitNewTask;
import com.my.som.controller.BaseController;
import com.my.som.dto.dataHandle.DataSyncDto;
import com.my.som.dto.dataHandle.ManualSyncDataDto;
import com.my.som.entity.sync.SyncDataLog;
import com.my.som.service.dataHandle.DataSynchronizationService;
import com.my.som.vo.dataHandle.DataSynchronization.DataSyncBaseVo;
import com.my.som.vo.dataHandle.DataSynchronization.DataSyncResidentVo;
import com.my.som.vo.dataHandle.DataSynchronization.SyncNameVo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * 视图数据手动同步处理
 */
@RestController
@RequestMapping("/dataSynchronizationController")
public class DataSynchronizationController extends BaseController {
    Logger logger = LoggerFactory.getLogger(DataSynchronizationController.class);

    @Autowired
    DataSynchronizationService dataSynchronizationService;

    @RequestMapping("/queryData")
    public CommonResult<CommonPage> queryData(@RequestBody DataSyncDto dto){
        List<DataSyncBaseVo> list = dataSynchronizationService.queryData(dto);
        return CommonResult.success(CommonPage.restPage(list));
    }

    @RequestMapping("/queryResident")
    public CommonResult<List<DataSyncResidentVo>> queryResident(@RequestBody DataSyncDto dto){
        List<DataSyncResidentVo> list = dataSynchronizationService.queryResident(dto);
        return CommonResult.success(list);
    }

    @RequestMapping("/querySyncName")
    public CommonResult<List<SyncNameVo>> querySyncName(){
        List<SyncNameVo> list = dataSynchronizationService.querySyncName();
        return CommonResult.success(list);
    }

    @RequestMapping("/manualSyncData")
    public CommonResult manualSyncData(@RequestBody ManualSyncDataDto dto) {
        List<String> list = new ArrayList<>();
        try {
            dataSynchronizationService.manualSyncData(dto);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return CommonResult.success(list);
    }

    /**
     * 触发器同步
     * @param uniqueId
     * @return
     */
    @PostMapping("/triggerSync")
    public CommonResult<?> triggerSync(String uniqueId){
        logger.info("===============触发器调用=" + uniqueId);
        if (ValidateUtil.isNotEmpty(uniqueId)) {
            ManualSyncDataDto dto = new ManualSyncDataDto();
            dto.setSysUserBase(getUserBaseInfo());
            dto.setUniqueIdArr(Collections.singletonList(uniqueId));
            dataSynchronizationService.triggerSync(dto);
        }
        return CommonResult.success();
    }

    /**
     * 同步数据
     * @param dto
     * @return
     */
    @PostMapping("/sync")
    public CommonResult<?> syncData(@RequestBody ManualSyncDataDto dto){
        dto.setSysUserBase(getUserBaseInfo());
        dataSynchronizationService.syncData(dto);
        return CommonResult.success();
    }

    /**
     * 同步所有数据
     * @param dto
     * @return
     */
    @PostMapping("/syncAll")
    public CommonResult<?> syncAllData(@RequestBody DataSyncDto dto){
        dto.setSysUserBase(getUserBaseInfo());
        dataSynchronizationService.syncAllData(dto);
        return CommonResult.success();
    }

    /**
     * 差量同步
     * @param dto
     * @return
     */
    @PostMapping("/diffSync")
    public CommonResult<?> diffSync(@RequestBody DataSyncDto dto){
        dto.setSysUserBase(getUserBaseInfo());
        dataSynchronizationService.diffSync(dto);
        return CommonResult.success();
    }

    /**
     * 查询同步数据
     * @param dto
     * @return
     */
    @PostMapping("/querySyncData")
    public CommonResult<?> querySyncData(@RequestBody DataSyncDto dto){
        List<SyncDataLog> list =  dataSynchronizationService.querySyncData(dto);
        return CommonResult.success(CommonPage.restPage(list));
    }

    /**
     * 文件上传同步数据
     * @param file
     * @return
     */
    @RequestMapping("uploadData")
    public CommonResult<?> uploadData(@RequestParam("file") MultipartFile file){
        dataSynchronizationService.uploadData(file, getUserBaseInfo());
        return CommonResult.success();
    }

}
