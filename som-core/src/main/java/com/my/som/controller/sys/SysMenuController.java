package com.my.som.controller.sys;

import com.my.som.common.api.CommonDictResult;
import com.my.som.common.api.CommonResult;
import com.my.som.common.constant.DrgConst;
import com.my.som.common.util.DictOperationUtil;
import com.my.som.common.util.ValidateUtil;
import com.my.som.controller.BaseController;
import com.my.som.model.orgAndUserManagement.BaseOrgNode;
import com.my.som.service.sys.SysMenuService;
import com.my.som.vo.SomMenuMgt;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 菜单控制器
 * @author: zyd
 * @date: 2023-07-31
 */
@RestController
@Api(tags = "SysMenuController",description = "菜单管理")
@RequestMapping("/menu")
public class SysMenuController extends BaseController {

	@Autowired
	private SysMenuService sysMenuService;

    @ApiOperation("获取码表")
    @RequestMapping(value = "/queryCollection",method = RequestMethod.POST)
    public CommonDictResult<List<SomMenuMgt>> queryCollection(@RequestParam(value = "codeKeys", defaultValue = "") String codeKeys){
        return CommonDictResult.success(null, DictOperationUtil.getDictList(codeKeys, DrgConst.TYPE_1));
    }

//	@PreAuthorize("hasAuthority('sys:menu:add') AND hasAuthority('sys:menu:edit')")
	@PostMapping(value="/save")
	public CommonResult save(@RequestBody SomMenuMgt record) {
		return CommonResult.success(sysMenuService.save(record));
	}

//	@PreAuthorize("hasAuthority('sys:menu:delete')")
	@PostMapping(value="/delete")
	public CommonResult delete(@RequestBody List<SomMenuMgt> records) {
		return CommonResult.success(sysMenuService.delete(records));
	}

//	@PreAuthorize("hasAuthority('sys:menu:view')")
	@GetMapping(value="/findNavTree")
	public CommonResult<List<SomMenuMgt>> findNavTree() {
		String userName = getUserBaseInfo().getUsername();
		return CommonResult.success(sysMenuService.findTree(userName, 1));
	}

//	@PreAuthorize("hasAuthority('sys:menu:view')")
	@GetMapping(value="/findMenuTree")
	public CommonResult<List<SomMenuMgt>> findMenuTree() {
		String userName = null;
		if (ValidateUtil.isEmpty(userName)) {
			userName = getUserBaseInfo().getUsername();
		}
		return CommonResult.success(sysMenuService.findTree(userName, 0));
	}

	/**
	 * 查询双重缓存数据
	 * @return
	 */
	@PostMapping("/queryDoubleCacheData")
	public CommonResult queryDoubleCacheData(){
		return CommonResult.success(sysMenuService.queryDoubleCacheData());
	}

}
