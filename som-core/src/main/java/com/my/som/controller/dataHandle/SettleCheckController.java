package com.my.som.controller.dataHandle;

import com.my.som.common.api.CommonResult;
import com.my.som.dto.dataHandle.MedicalSettleCheckDto;
import com.my.som.dto.dataHandle.SettleCheckDto;
import com.my.som.service.dataHandle.SettleCheckService;
import com.my.som.vo.dataHandle.SettleCheckVo;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/SettleCheckController")
public class SettleCheckController {

    @Resource
    private SettleCheckService settleCheckService;

    @RequestMapping("/QuerySettle")
    public CommonResult<Map<String,List<SettleCheckVo>>> QuerySettle(SettleCheckDto dto){
        return CommonResult.success(settleCheckService.QuerySettle(dto));
    }

    @RequestMapping("/update")
    public CommonResult update(SettleCheckDto dto){
        settleCheckService.update(dto);
        return CommonResult.success();
    }



}
