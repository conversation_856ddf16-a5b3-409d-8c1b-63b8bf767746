package com.my.som.controller.cdBusiness;

import com.my.som.common.api.CommonPage;
import com.my.som.common.api.CommonResult;
import com.my.som.common.util.BeanUtil;
import com.my.som.controller.BaseController;
import com.my.som.dto.cdBusiness.CdAnalysisDto;
import com.my.som.dto.hospitalAnalysis.HospitalAnalysisQueryParam;
import com.my.som.service.cdBusiness.CdDiseaseAnalysisService;
import com.my.som.vo.cdBusiness.CdDiseaseAnalysisInfo;
import com.my.som.vo.common.CommonObject;
import com.my.som.vo.hospitalAnalysis.DiseaseAnalysisInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;

/**
 * 医院病种分析Controller
 * Created by sky on 2020/3/23.
 */
@Controller
@Api(tags = "cdDiseaseAnalysisController", description = "医院病种分析")
@RequestMapping("/cdDiseaseAnalysis")
public class CdDiseaseAnalysisController extends BaseController {

    @Autowired
    private CdDiseaseAnalysisService cdDiseaseAnalysisService;

    @ApiOperation("查询医院病种分析主要信息")
    @RequestMapping(value = "/mainInfoList", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<CommonPage<CdDiseaseAnalysisInfo>> list(CdAnalysisDto queryParam,
                                                                @RequestParam(value = "pageSize", defaultValue = "100") Integer pageSize,
                                                                @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum) {
//        BeanUtil.copyPropertiesIgnoreNull(getUserBaseInfo(), queryParam);
        List<CdDiseaseAnalysisInfo> cdDiseaseAnalysisInfos = cdDiseaseAnalysisService.list(queryParam, pageSize, pageNum);
        return CommonResult.success(CommonPage.restPage(cdDiseaseAnalysisInfos));
    }

    @ApiOperation("查询医院病种分析统计信息")
    @RequestMapping(value = "/getCountInfo", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<List<CommonObject>> getCountInfo(HospitalAnalysisQueryParam queryParam) {
//        BeanUtil.copyPropertiesIgnoreNull(getUserBaseInfo(), queryParam);
        List<CommonObject> commonObject = cdDiseaseAnalysisService.getCountInfo(queryParam);
        return CommonResult.success(commonObject);
    }
}
