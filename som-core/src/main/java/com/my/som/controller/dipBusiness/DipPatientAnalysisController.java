package com.my.som.controller.dipBusiness;

import com.my.som.common.api.CommonPage;
import com.my.som.common.api.CommonResult;
import com.my.som.dto.dipBusiness.DipPatientAnalysisDto;
import com.my.som.service.dipBusiness.DipPatientAnalysisService;
import com.my.som.vo.dipBusiness.DipPatientAnalysisVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/dipPatientAnalysisController")
public class DipPatientAnalysisController {

    @Autowired
    private DipPatientAnalysisService dipPatientAnalysisService;

    @RequestMapping("/selectPatientData")
    public CommonResult selectPatientData(@RequestBody DipPatientAnalysisDto dto) {
        List<DipPatientAnalysisVo> list = dipPatientAnalysisService.selectPatientData(dto);
        return CommonResult.success(CommonPage.restPage(list));
    }
}
