package com.my.som.controller.hcs;

import com.my.som.common.api.CommonPage;
import com.my.som.common.api.CommonResult;
import com.my.som.dto.hcs.ExamCorrectionResultParam;
import com.my.som.service.hcs.ExamCorrectionResultService;
import com.my.som.vo.hcs.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;

@Controller
@Api(tags = "ExamCorrectionResultController", description = "自查自纠结果表查询控制层")
@RequestMapping("/examCorrectionResultController")
public class ExamCorrectionResultController {

    @Autowired
    private ExamCorrectionResultService examCorrectionResultService;


    @ApiOperation("查询患者明细")
    @RequestMapping(value = "/getResultListByPatient", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<CommonPage<ExamCorrectionPatientVo>> getResultListByPatient(ExamCorrectionResultParam queryParam) {

        List<ExamCorrectionPatientVo> resultList = examCorrectionResultService.getResultListByPatient(queryParam);
        return CommonResult.success(CommonPage.restPage(resultList));
    }

    @ApiOperation("查询违规汇总")
    @RequestMapping(value = "/getViolationsSummary", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<CommonPage<ViolationsSummaryVo>> getViolationsSummary(ExamCorrectionResultParam queryParam) {
        List<ViolationsSummaryVo> resultList = examCorrectionResultService.getViolationsSummary(queryParam);
        return CommonResult.success(CommonPage.restPage(resultList));
    }

    @ApiOperation("查询月度汇总")
    @RequestMapping(value = "/getResultListByMouth", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<CommonPage<ExamCorrectionMouthVo>> getResultListByMouth(ExamCorrectionResultParam queryParam) {
        List<ExamCorrectionMouthVo> resultList = examCorrectionResultService.getResultListByMouth(queryParam);
        return CommonResult.success(CommonPage.restPage(resultList));
    }
    @ApiOperation("查询科室汇总")
    @RequestMapping(value = "/getResultListByDept", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<CommonPage<ExamCorrectionMouthVo>> getResultListByDept(ExamCorrectionResultParam queryParam) {
        List<ExamCorrectionMouthVo> resultList = examCorrectionResultService.getResultListByDept(queryParam);
        return CommonResult.success(CommonPage.restPage(resultList));
    }


    @ApiOperation("查询医生汇总")
    @RequestMapping(value = "/getResultListByDoctor", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<CommonPage<ExamCorrectionMouthVo>> getResultListByDoctor(ExamCorrectionResultParam queryParam) {
        List<ExamCorrectionMouthVo> resultList = examCorrectionResultService.getResultListByDoctor(queryParam);
        return CommonResult.success(CommonPage.restPage(resultList));
    }


    @ApiOperation("查询违规汇总中的违规元素详情")
    @RequestMapping(value = "/getTupleDetailByRuleCode", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<CommonPage<ViolationsSummaryVo>> getTupleDetailByRuleCode(ExamCorrectionResultParam queryParam ) {
        List<ViolationsSummaryVo> resultList = examCorrectionResultService.getTupleDetailByRuleCode(queryParam);
        return CommonResult.success(CommonPage.restPage(resultList));
    }


    @ApiOperation("跳转页面查询查询违规详情")
    @RequestMapping(value = "/queryVioalDetailList", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<CommonPage<ExamCorrectionPatientVo>> queryVioalDetailList(ExamCorrectionResultParam queryParam) {
        List<ExamCorrectionPatientVo> resultList = examCorrectionResultService.queryVioalDetailList(queryParam);
        return CommonResult.success(CommonPage.restPage(resultList));
    }


    @ApiOperation("根据uniqueId树状返回违规信息")
    @RequestMapping(value = "/fetchViolationRulesByUniqueId", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<List<ExamCorrectionTreeNodeVo>> fetchViolationRulesByUniqueId(ExamCorrectionResultParam queryParam) {
        List<ExamCorrectionTreeNodeVo> resultList = examCorrectionResultService.fetchViolationRulesByUniqueId(queryParam);
        return CommonResult.success(resultList);
    }

    @ApiOperation("根据uniqueId树状返回违规信息")
    @RequestMapping(value = "/filteredChargeItemsByUniqueId", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<CommonPage<ChargeItemsVo>> filteredChargeItemsByUniqueId(ExamCorrectionResultParam queryParam) {
        List<ChargeItemsVo> resultList = examCorrectionResultService.filteredChargeItemsByUniqueId(queryParam);
        return CommonResult.success(CommonPage.restPage(resultList));
    }

    @ApiOperation("查询患者明细")
    @RequestMapping(value = "/fetchPatientInfoByUniqueId", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<ExamCorrectionPatientVo> fetchPatientInfoByUniqueId(ExamCorrectionResultParam queryParam) {

        ExamCorrectionPatientVo resultList = examCorrectionResultService.fetchPatientInfoByUniqueId(queryParam);
        return CommonResult.success(resultList);
    }

    @ApiOperation("查询违规月度汇总")
    @RequestMapping(value = "/getViolationsSummaryByMouth", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<CommonPage<ViolationsSummaryVo>> getViolationsSummaryByMouth(ExamCorrectionResultParam queryParam) {
        List<ViolationsSummaryVo> resultList = examCorrectionResultService.getViolationsSummaryByMouth(queryParam);
        return CommonResult.success(CommonPage.restPage(resultList));
    }

    @ApiOperation("查询违规项目明细")
    @RequestMapping(value = "/getViolationItemDetail", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<CommonPage<ExamCorrectionResutlVo>> getViolationItemDetail(ExamCorrectionResultParam queryParam) {
        List<ExamCorrectionResutlVo> resultList = examCorrectionResultService.fetchViolationRulesByParam(queryParam);
        return CommonResult.success(CommonPage.restPage(resultList));
    }

    @ApiOperation("查询违规项目统计")
    @RequestMapping(value = "/getViolationItemSummary", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<CommonPage<ViolationsItemSummaryVo>> getViolationItemSummary(ExamCorrectionResultParam queryParam) {
        List<ViolationsItemSummaryVo> resultList = examCorrectionResultService.getViolationItemSummary(queryParam);
        return CommonResult.success(CommonPage.restPage(resultList));
    }

    @ApiOperation("查询违规明细统计")
    @RequestMapping(value = "/getViolationDetailSummary", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<CommonPage<ViolationsDetailSummaryVo>> getViolationDetailSummary(ExamCorrectionResultParam queryParam) {
        List<ViolationsDetailSummaryVo> resultList = examCorrectionResultService.getViolationDetailSummary(queryParam);
        return CommonResult.success(CommonPage.restPage(resultList));
    }
}
