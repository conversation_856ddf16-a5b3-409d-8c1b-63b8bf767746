package com.my.som.controller.sys;

import com.my.som.common.api.CommonDictResult;
import com.my.som.common.util.ValidateUtil;
import com.my.som.config.InitConfig;
import com.my.som.dto.sys.VerifyDto;
import com.my.som.service.sys.SysInitService;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.Date;


/**
 * @BelongsProject: medicalback
 * @BelongsPackage: com.my.som.controller.sys
 * @author: zyd
 * @CreateTime: 2023-07-31
 * @Description:
 * @Version: 1.0
 */
@RestController
@RequestMapping("/sysInitController")
public class SysInitController {

    @Resource
    private SysInitService service;

    @ApiOperation("验证")
    @RequestMapping(value = "/crtfCode", method = RequestMethod.POST)
    public CommonDictResult<?> queryCollection(@RequestBody VerifyDto dto) {
        service.insertVerifyCode(dto);
        boolean type = InitConfig.refreshRegister(dto.getCrtfCode());
        if (type) {
            return CommonDictResult.success("1");
        } else {
            return CommonDictResult.success("2");
        }
    }

    @ApiOperation("时间查询")
    @RequestMapping(value = "/querySystemTime", method = RequestMethod.POST)
    public CommonDictResult<?> querySystemTime(@RequestBody VerifyDto dto) {
        String updt_date = "-";
        Date time = InitConfig.time;
        if (!ValidateUtil.isEmpty(time)){
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            updt_date = sdf.format(time);
        }
        return CommonDictResult.success(updt_date);
    }
}
