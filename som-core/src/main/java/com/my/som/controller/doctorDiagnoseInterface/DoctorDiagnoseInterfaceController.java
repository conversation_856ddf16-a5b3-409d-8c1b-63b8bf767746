package com.my.som.controller.doctorDiagnoseInterface;

import com.my.som.common.api.CommonPage;
import com.my.som.common.api.CommonResult;
import com.my.som.dto.doctorDiagnoseInterface.DiagnoseFeedback;
import com.my.som.dto.medicalQuality.AllBusSettleListInfo;
import com.my.som.dto.medicalQuality.BusSettleListMainInfo;
import com.my.som.dto.medicalQuality.SettleListMainInfoQueryParam;
import com.my.som.dto.medicalQuality.ValidateMedicalResult;
import com.my.som.service.doctorDiagnose.DoctorDiagnoseInterfaceService;
import com.my.som.service.medicalQuality.SettleListManageService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 医生下诊断接口实例Controller
 * Created by sky on 2020/3/12.
 */
@Controller
@Api(tags = "DoctorDiagnoseInterfaceController", description = "医生下诊断接口实例")
@RequestMapping("/doctorDiagnoseInterface")
public class DoctorDiagnoseInterfaceController {
    @Autowired
    private DoctorDiagnoseInterfaceService doctorDiagnoseInterfaceService;

    @ApiOperation("根据诊断查询时间费用、分组情况")
    @RequestMapping(value = "/queryCostTimeAndGroupInfo", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<DiagnoseFeedback> queryCostTimeAndGroupInfo(@RequestParam(value = "queryIcd") String queryIcd) {
        DiagnoseFeedback diagnoseFeedback= doctorDiagnoseInterfaceService.queryCostTimeAndGroupInfo(queryIcd);
        return CommonResult.success(diagnoseFeedback);
    }

}
