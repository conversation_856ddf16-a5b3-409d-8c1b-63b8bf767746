package com.my.som.controller.firstPage;

import com.my.som.common.api.CommonResult;
import com.my.som.common.util.BeanUtil;
import com.my.som.controller.BaseController;
import com.my.som.dto.hospitalAnalysis.HospitalAnalysisQueryParam;
import com.my.som.service.firstPage.FirstPageService;
import com.my.som.vo.common.CommonObject;
import com.my.som.vo.firstPage.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;

/**
 * 首页Controller
 * Created by sky on 2020/4/8.
 */
@Controller
@Api(tags = "FirstPageController", description = "首页")
@RequestMapping("/firstPage")
public class FirstPageController extends BaseController {
    @Autowired
    private FirstPageService firstPageService;

    @ApiOperation("查询首页统计信息")
    @RequestMapping(value = "/getCountInfo", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<FirstPageCountVo> getCountInfo(HospitalAnalysisQueryParam queryParam) {
//        BeanUtil.copyPropertiesIgnoreNull(getUserBaseInfo(), queryParam);
        FirstPageCountVo firstPageCountVo = firstPageService.getCountInfo(queryParam);
        return CommonResult.success(firstPageCountVo);
    }

    @ApiOperation("查询一年内DRGs指标")
    @RequestMapping(value = "/getDrgIndexInfo", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<List<FirstPageCountVo>> getDrgIndexInfo(HospitalAnalysisQueryParam queryParam) {
//        BeanUtil.copyPropertiesIgnoreNull(getUserBaseInfo(), queryParam);
        List<FirstPageCountVo> drgIndexInfoList = firstPageService.getDrgIndexInfo(queryParam);
        return CommonResult.success(drgIndexInfoList);
    }

    @ApiOperation("查询病案首页和结算清单各项主要费用以及人次")
    @RequestMapping(value = "/getMedicalTreatmentCostInfo", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<List<FirstPageMedicalTreatmentCostVo>> getMedicalTreatmentCostInfo(HospitalAnalysisQueryParam queryParam) {
//        BeanUtil.copyPropertiesIgnoreNull(getUserBaseInfo(), queryParam);
        List<FirstPageMedicalTreatmentCostVo> firstPageMedicalTreatmentCostVoList = firstPageService.getMedicalTreatmentCostInfo(queryParam);
        return CommonResult.success(firstPageMedicalTreatmentCostVoList);
    }

    @ApiOperation("查询医生DRGs指标")
    @RequestMapping(value = "/getDoctorDrgIndexInfo", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<List<FirstPageCountVo>> getDoctorDrgIndexInfo(HospitalAnalysisQueryParam queryParam) {
//        BeanUtil.copyPropertiesIgnoreNull(getUserBaseInfo(), queryParam);
        List<FirstPageCountVo> doctorDrgIndexInfoList = firstPageService.getDoctorDrgIndexInfo(queryParam);
        return CommonResult.success(doctorDrgIndexInfoList);
    }

    @ApiOperation("查询手术统计信息")
    @RequestMapping(value = "/getOperativeInfo", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<List<FirstPageOperativeVo>> getOperativeInfo(HospitalAnalysisQueryParam queryParam) {
//        BeanUtil.copyPropertiesIgnoreNull(getUserBaseInfo(), queryParam);
        List<FirstPageOperativeVo> firstPageOperativeVoList = firstPageService.getOperativeInfo(queryParam);
        return CommonResult.success(firstPageOperativeVoList);
    }

    @ApiOperation("查询病种人次统计信息")
    @RequestMapping(value = "/getDiseaseInfo", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<List<CommonObject>> getDiseaseInfo(HospitalAnalysisQueryParam queryParam) {
//        BeanUtil.copyPropertiesIgnoreNull(getUserBaseInfo(), queryParam);
        List<CommonObject> commonObjectList = firstPageService.getDiseaseInfo(queryParam);
        return CommonResult.success(commonObjectList);
    }

    @ApiOperation("查询病种群落费用和群落住院日")
    @RequestMapping(value = "/getDiseaseCostInfo", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<List<FirstPageDiseaseCostVo>> getDiseaseCostInfo(HospitalAnalysisQueryParam queryParam) {
//        BeanUtil.copyPropertiesIgnoreNull(getUserBaseInfo(), queryParam);
        List<FirstPageDiseaseCostVo> firstPageDiseaseCostVoList = firstPageService.getDiseaseCostInfo(queryParam);
        return CommonResult.success(firstPageDiseaseCostVoList);
    }

    @ApiOperation("查询DIP费用信息")
    @RequestMapping(value = "/getDipCostInfo", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<List<FirstPageDipCostVo>> getDipCostInfo(HospitalAnalysisQueryParam queryParam) {
//        BeanUtil.copyPropertiesIgnoreNull(getUserBaseInfo(), queryParam);
        List<FirstPageDipCostVo> firstPageDipCostVoList = firstPageService.getDipCostInfo(queryParam);
        return CommonResult.success(firstPageDipCostVoList);
    }

    @ApiOperation("查询病组各指标统计信息")
    @RequestMapping(value = "/getIndexTop10DrgsInfo", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<List<FirstPageCountVo>> getIndexTop10DrgsInfo(HospitalAnalysisQueryParam queryParam) {
//        BeanUtil.copyPropertiesIgnoreNull(getUserBaseInfo(), queryParam);
        List<FirstPageCountVo> firstPageCountVoList = firstPageService.getIndexTop10DrgsInfo(queryParam);
        return CommonResult.success(firstPageCountVoList);
    }

    @ApiOperation("查询DIP各指标统计信息")
    @RequestMapping(value = "/getIndexTop10DipInfo", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<List<FirstPageCountVo>> getIndexTop10DipInfo(HospitalAnalysisQueryParam queryParam) {
//        BeanUtil.copyPropertiesIgnoreNull(getUserBaseInfo(), queryParam);
        List<FirstPageCountVo> firstPageCountVoList = firstPageService.getIndexTop10DipInfo(queryParam);
        return CommonResult.success(firstPageCountVoList);
    }

}
