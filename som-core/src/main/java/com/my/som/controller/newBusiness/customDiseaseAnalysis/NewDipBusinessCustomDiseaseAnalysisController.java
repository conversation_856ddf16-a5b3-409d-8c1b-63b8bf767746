package com.my.som.controller.newBusiness.customDiseaseAnalysis;

import com.my.som.common.api.CommonPage;
import com.my.som.common.api.CommonResult;

import com.my.som.dto.newBusiness.customDisease.NewBusinessCustomDiseaseDto;
import com.my.som.service.newBusiness.customDisease.NewDipBusinessCustomDiseaseAnalysisService;

import com.my.som.vo.newBusiness.customDisease.NewBusinessCustomDiseaseVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 病组分析
 */
@RestController
@RequestMapping("/newDipBusinessCustomDiseaseAnalysisController")
public class NewDipBusinessCustomDiseaseAnalysisController {

    @Autowired
    private NewDipBusinessCustomDiseaseAnalysisService newDipBusinessDiseaseAnalysisService;

    @RequestMapping("/queryDiseaseKpiData")
    public CommonResult<CommonPage<NewBusinessCustomDiseaseVo>> queryDiseaseKpiData(NewBusinessCustomDiseaseDto dto){
        List<NewBusinessCustomDiseaseVo> list = newDipBusinessDiseaseAnalysisService.queryDiseaseKpiData(dto);
        return CommonResult.success(CommonPage.restPage(list));
    }

    @RequestMapping("/queryDiseaseForecastData")
    public CommonResult<CommonPage<NewBusinessCustomDiseaseVo>> queryDiseaseForecastData(NewBusinessCustomDiseaseDto dto){
        List<NewBusinessCustomDiseaseVo> list = newDipBusinessDiseaseAnalysisService.queryDiseaseForecastData(dto);
        return CommonResult.success(CommonPage.restPage(list));
    }
}
