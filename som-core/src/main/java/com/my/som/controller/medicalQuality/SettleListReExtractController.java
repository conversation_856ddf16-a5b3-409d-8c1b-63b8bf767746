package com.my.som.controller.medicalQuality;

import com.my.som.common.api.CommonResult;
import com.my.som.controller.BaseController;
import com.my.som.dto.medicalQuality.*;
import com.my.som.service.medicalQuality.SettleListReExtractService;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

/**
 * 医保结算清单Controller
 * Created by sky on 2020/1/7.
 */
@Controller
@Api(tags = "SettleListReExtractController", description = "医保结算清单数据同步")
@RequestMapping("/settleListReExtract")
public class SettleListReExtractController extends BaseController {

    @Autowired
    private SettleListReExtractService settleListReExtractService;

    /**
     * 查询结算清单标识记录
     * @param dto
     * @return
     */
    @RequestMapping(value = "/extract", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<?> extract(@RequestBody SettleListReExtractDto dto){
        settleListReExtractService.reExtract(dto);
        return CommonResult.success();
    }
}
