package com.my.som.controller.medicalQuality;

import com.my.som.common.api.CommonResult;
import com.my.som.dto.medicalQuality.CompareAnalysisDto;
import com.my.som.service.medicalQuality.DeptComparAnalysisService;
import com.my.som.vo.medicalQuality.DeptComparAnalysisVo;
import com.my.som.vo.medicalQuality.DrgDeptComparAnalysisVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RequestMapping("/deptComparAnalysis")
@RestController
public class DeptComparAnalysisController {
    @Autowired
    private DeptComparAnalysisService deptComparAnalysisService;

    /**
     *
     * @param dto
     * @return
     */
    @RequestMapping("/getData")
    public CommonResult<DeptComparAnalysisVo> getData(CompareAnalysisDto dto) {
        DeptComparAnalysisVo deptComparAnalysisVo = deptComparAnalysisService.getData(dto);
        return CommonResult.success(deptComparAnalysisVo);
    }

    @RequestMapping("/getCost")
    public CommonResult<DeptComparAnalysisVo> getCost(CompareAnalysisDto dto) {
        DeptComparAnalysisVo cost = deptComparAnalysisService.getCost(dto);
        return CommonResult.success(cost);
    }

    @RequestMapping("/getDrgData")
    public CommonResult<DrgDeptComparAnalysisVo> getDrgData(CompareAnalysisDto dto) {
        DrgDeptComparAnalysisVo deptComparAnalysisVo = deptComparAnalysisService.getDrgData(dto);
        return CommonResult.success(deptComparAnalysisVo);
    }

    @RequestMapping("/getPayCostData")
    public CommonResult<DeptComparAnalysisVo> getPayCostData(CompareAnalysisDto dto) {
        DeptComparAnalysisVo payCostData = deptComparAnalysisService.getPayCostData(dto);
        return CommonResult.success(payCostData);
    }


    @RequestMapping("/getDrgPayCostData")
    public CommonResult<DrgDeptComparAnalysisVo> getDrgPayCostData(CompareAnalysisDto dto) {
        DrgDeptComparAnalysisVo drgPayCostData = deptComparAnalysisService.getDrgPayCostData(dto);
        return CommonResult.success(drgPayCostData);
    }

}
