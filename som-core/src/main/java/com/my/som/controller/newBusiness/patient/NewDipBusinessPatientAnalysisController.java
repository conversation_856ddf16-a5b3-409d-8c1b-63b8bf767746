package com.my.som.controller.newBusiness.patient;

import com.my.som.common.api.CommonPage;
import com.my.som.common.api.CommonResult;
import com.my.som.dto.newBusiness.patient.NewBusinessPatientDto;
import com.my.som.service.newBusiness.patient.NewDipBusinessPatientAnalysisService;
import com.my.som.vo.newBusiness.patient.NewBusinessPatientVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/newDipBusinessPatientAnalysisController")
public class NewDipBusinessPatientAnalysisController {

    @Autowired
    private NewDipBusinessPatientAnalysisService newDipBusinessPatientAnalysisService;

    /**
     * 查询患者基本信息
     * @param dto
     * @return
     */
    @RequestMapping("/queryPatientBasicInfoData")
    public CommonResult<CommonPage<NewBusinessPatientVo>> queryPatientBasicInfoData(@RequestBody NewBusinessPatientDto dto) {
        List<NewBusinessPatientVo> list = newDipBusinessPatientAnalysisService.queryPatientBasicInfoData(dto);
        return CommonResult.success(CommonPage.restPage(list));
    }

    /**
     * 查询患者费用信息
     * @param dto
     * @return
     */
    @RequestMapping("/queryPatientCostInfoData")
    public CommonResult<CommonPage<NewBusinessPatientVo>> queryPatientCostInfoData(@RequestBody NewBusinessPatientDto dto) {
        List<NewBusinessPatientVo> list = newDipBusinessPatientAnalysisService.queryPatientCostInfoData(dto);
        return CommonResult.success(CommonPage.restPage(list));
    }

    /**
     * 查询患者预测信息
     * @param dto
     * @return
     */
    @RequestMapping("/queryPatientForecastData")
    public CommonResult<CommonPage<NewBusinessPatientVo>> queryPatientForecastData(@RequestBody NewBusinessPatientDto dto) {
        List<NewBusinessPatientVo> list = newDipBusinessPatientAnalysisService.queryPatientForecastData(dto);
        return CommonResult.success(CommonPage.restPage(list));
    }
}
