package com.my.som.controller.hosPerfAppraisal;

import com.my.som.common.api.CommonPage;
import com.my.som.common.api.CommonResult;
import com.my.som.dto.hosPerfAppraisal.HosPerfAppraisalDsItemDto;
import com.my.som.service.hosPerfAppraisal.HosPerfAppraisalDsItemService;
import com.my.som.vo.hosPerfAppraisal.HosPerfAppraisalDsItemVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @author: zyd
 * @version 1.0
 * @date: 2023-07-31
 * @description:
 */
@RestController
@RequestMapping("/hosPerfAppraisalDsItem")
public class HosPerfAppraisalDsItemController {

    @Autowired
    private HosPerfAppraisalDsItemService hosPerfAppraisalDsItemService;

    /**
     * 查询数据
     * @param dto
     * @return
     */
    @RequestMapping("/queryList")
    public CommonResult<?> queryList(@RequestBody HosPerfAppraisalDsItemDto dto) {
        return CommonResult.success(CommonPage.restPage(hosPerfAppraisalDsItemService.queryList(dto)));
    }

    /**
     * 新增
     * @param dto
     * @return
     */
    @RequestMapping("/add")
    public CommonResult<?> add(@RequestBody HosPerfAppraisalDsItemDto dto){
        hosPerfAppraisalDsItemService.add(dto);
        return CommonResult.success();
    }

    /**
     * 修改
     * @param dto
     * @return
     */
    @RequestMapping("/update")
    public CommonResult<?> update(@RequestBody HosPerfAppraisalDsItemDto dto){
        hosPerfAppraisalDsItemService.update(dto);
        return CommonResult.success();
    }

    /**
     * 移除
     * @param dto
     * @return
     */
    @RequestMapping("/remove")
    public CommonResult<?> remove(@RequestBody HosPerfAppraisalDsItemDto dto){
        hosPerfAppraisalDsItemService.remove(dto);
        return CommonResult.success();
    }
}
