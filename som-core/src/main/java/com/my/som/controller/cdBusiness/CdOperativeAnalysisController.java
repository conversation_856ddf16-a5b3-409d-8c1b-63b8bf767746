package com.my.som.controller.cdBusiness;

import com.my.som.common.api.CommonPage;
import com.my.som.common.api.CommonResult;
import com.my.som.common.util.BeanUtil;
import com.my.som.controller.BaseController;
import com.my.som.dto.cdBusiness.CdAnalysisDto;
import com.my.som.dto.hospitalAnalysis.HospitalAnalysisQueryParam;
import com.my.som.service.cdBusiness.CdOperativeAnalysisService;
import com.my.som.service.hospitalAnalysis.OperativeAnalysisService;
import com.my.som.vo.cdBusiness.CdOperativeAnalysisCountInfo;
import com.my.som.vo.cdBusiness.CdOperativeAnalysisInfo;
import com.my.som.vo.hospitalAnalysis.OperativeAnalysisCountInfo;
import com.my.som.vo.hospitalAnalysis.OperativeAnalysisInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;

/**
 * 成都组医院手术分析Controller
 * Created by sky on 2022/3/09.
 */
@Controller
@Api(tags = "CdOperativeAnalysisController", description = "医院手术分析")
@RequestMapping("/CdOperativeAnalysis")
public class CdOperativeAnalysisController extends BaseController {
    @Autowired
    private CdOperativeAnalysisService cdOperativeAnalysisService;

    @ApiOperation("查询医院手术分析主要信息")
    @RequestMapping(value = "/mainInfoList", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<CommonPage<CdOperativeAnalysisInfo>> list(CdAnalysisDto queryParam,
                                                                  @RequestParam(value = "pageSize", defaultValue = "200") Integer pageSize,
                                                                  @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum) {
//        BeanUtil.copyPropertiesIgnoreNull(getUserBaseInfo(), queryParam);
        List<CdOperativeAnalysisInfo> cdOperativeAnalysisInfos = cdOperativeAnalysisService.list(queryParam, pageSize, pageNum);
        return CommonResult.success(CommonPage.restPage(cdOperativeAnalysisInfos));
    }

    @ApiOperation("查询医院手术分析统计信息")
    @RequestMapping(value = "/getCountInfo", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<List<CdOperativeAnalysisCountInfo>> getCountInfo(CdAnalysisDto queryParam) {
//        BeanUtil.copyPropertiesIgnoreNull(getUserBaseInfo(), queryParam);
        List<CdOperativeAnalysisCountInfo> cdOperativeAnalysisCountInfos = cdOperativeAnalysisService.getCountInfo(queryParam);
        return CommonResult.success(cdOperativeAnalysisCountInfos);
    }

}
