package com.my.som.controller.dictmenament;

import com.my.som.common.api.CommonResult;
import com.my.som.model.BaseCode;
import com.my.som.service.dictmenagement.DictMenagementService;
import com.my.som.vo.SomSysCode;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;

/**
 * @author: zyd
 * Case:          drg
 * FileName:      DictMenamentController
 * Date:          2020/1/10 16:31
 * Description:   描述
 * Ver:       1.0
 * Copyright (C),2012-2022 Freedom
 */
@Controller
@Api(tags="DictMenamentController",description = "数据字典管理")
@RequestMapping("/dict")
public class DictMenamentController {
    @Autowired
    private DictMenagementService dictMenagementService;

    /**
     * 获取字典列表
     * @return
     */
    @RequestMapping(value = "/getDictList", method = RequestMethod.POST)
    @ApiOperation("获取字典列表")
    @ResponseBody
    public CommonResult<List<SomSysCode>> getDictList(){
        return CommonResult.success(dictMenagementService.getList(null));
    }
}
