package com.my.som.controller.cdBusiness;

import com.my.som.common.api.CommonMapResult;
import com.my.som.common.api.CommonPage;
import com.my.som.common.api.CommonResult;
import com.my.som.controller.BaseController;
import com.my.som.dto.cdBusiness.CdDetailDto;
import com.my.som.service.cdBusiness.CdGroupDetailService;
import com.my.som.vo.cdBusiness.CdDetailVo;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: zyd
 * @Date 2021/8/23 4.05 下午
 * @Version 1.0
 * @Description: 分组控费
 */
@RestController
@RequestMapping("/cdGroupDetailController")
public class CdGroupDetailController extends BaseController {
    @Resource
    private CdGroupDetailService cdGroupDetailService;

    /**
     * 获取数据
     * @param dto   参数
     * @return
     */
    @RequestMapping("/getList")
    public CommonResult<CommonPage<CdDetailVo>> getList(CdDetailDto dto){
        List<CdDetailVo> list = cdGroupDetailService.getList(dto);
        return CommonResult.success(CommonPage.restPage(list));
    }

    /**
     * 获取其他数据
     * @param dto   参数
     * @return
     */
    @RequestMapping("/queryOtherData")
    public CommonMapResult queryOtherData(CdDetailDto dto){
        return cdGroupDetailService.queryOtherData(dto);
    }
}
