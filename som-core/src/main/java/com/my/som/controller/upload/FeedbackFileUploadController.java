package com.my.som.controller.upload;

import com.alibaba.excel.EasyExcel;
import com.my.som.common.api.CommonPage;
import com.my.som.common.api.CommonResult;
import com.my.som.common.constant.DrgConst;
import com.my.som.common.exception.AppException;
import com.my.som.common.util.EasyPoiUtil;
import com.my.som.common.util.ExcelUtil;
import com.my.som.controller.BaseController;
import com.my.som.dto.dataHandle.DataHandleCommonDto;
import com.my.som.dto.upload.FeedbackDto;
import com.my.som.dto.upload.FileUploadDto;
import com.my.som.entity.upload.FeedbackEntity;
import com.my.som.entity.upload.FeedbackEntity2;
import com.my.som.listener.upload.FeedbackFileUploadListener;
import com.my.som.service.dataHandle.impl.GeneraScoreJobServiceImpl;
import com.my.som.service.upload.FeedbackFileUploadService;
import com.my.som.vo.newBusiness.upload.FeedbackDetailVo;
import com.my.som.vo.upload.FileUploadVo;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * @author: zyd
 * @Date 2021/8/6 4:11 下午
 * @Version 1.0
 * @Description:    分组反馈信息文件上传
 */
@RestController
@RequestMapping("/feedbackFileUploadController")
public class FeedbackFileUploadController extends BaseController {

    @Resource
    private FeedbackFileUploadService feedbackFileUploadService;

    @Autowired
    private GeneraScoreJobServiceImpl generaScoreJobService;

    /**
     * 分组反馈文件上传
     * @param file
     * @return
     */
    @RequestMapping("/feedbackUpload")
    public CommonResult<?> feedbackUpload(@RequestParam("file") MultipartFile file, FileUploadDto dto){
        dto.setFile(file);
        dto.setName(file.getOriginalFilename());
        dto.setHospitalId(getUserBaseInfo().getHospitalId());
        feedbackFileUploadService.addFeedbackRecord2(dto);
        return CommonResult.success();
    }

    /**
     * 上传费用信息
     * @param file
     * @return
     */
    @RequestMapping("/feedbackUploadFunds")
    public CommonResult feedbackUploadFunds(@RequestParam("file") MultipartFile file, FileUploadDto dto){
        try {
            EasyExcel.read(file.getInputStream(), FeedbackEntity.class, new FeedbackFileUploadListener(feedbackFileUploadService, dto)).sheet().doRead();
        } catch (Exception e){
            throw new AppException(e.getCause().getLocalizedMessage());
        }
        return CommonResult.success();
    }

    /**
     * 下载模板
     * @return
     */
    @RequestMapping("/downloadTemplate")
    public CommonResult downloadTemplate(HttpServletResponse response) throws IOException {
        ExcelUtil.exportTemplateToWeb(response,"科室模板", "classpath:templates/deptTemplate.xlsx");
        return CommonResult.success();
    }

    /**
     * 查询文件上传日志
     * @param dto
     * @return
     * @throws IOException
     */
    @RequestMapping("/queryFileUploadLog")
    public CommonResult<List<FileUploadVo>> queryFileUploadLog(FileUploadDto dto) {
        List<FileUploadVo> list = feedbackFileUploadService.queryFileUploadLog(dto);
        return CommonResult.success(list);
    }

    /**
     * 查询分组反馈详情
     * @param dto
     * @return
     * @throws IOException
     */
    @RequestMapping("/queryFeedbackDetail")
    public CommonResult<CommonPage<FeedbackEntity>> queryFeedbackDetail(FeedbackDto dto) {
        List<FeedbackEntity> list = feedbackFileUploadService.queryFeedbackDetail(dto);
        return CommonResult.success(CommonPage.restPage(list));
    }

    /**
     * 查询分组反馈详情2
     * @param dto
     * @return
     * @throws IOException
     */
    @RequestMapping("/queryFeedbackDetail2")
    public CommonResult<CommonPage<FeedbackEntity2>> queryFeedbackDetail2(FeedbackDto dto) {
        List<FeedbackEntity2> list = feedbackFileUploadService.queryFeedbackDetail2(dto);
        return CommonResult.success(CommonPage.restPage(list));
    }

    /**
     * 查询反馈汇总
     * @param dto
     * @return
     * @throws IOException
     */
    @RequestMapping("/queryFeedbackDetail2Summary")
    public CommonResult<?> queryFeedbackDetail2Summary(FeedbackDto dto) {
        return CommonResult.success(feedbackFileUploadService.queryFeedbackDetail2Summary(dto));
    }

    /**
     * 查询反馈汇总
     * @param dto
     * @return
     * @throws IOException
     */
    @RequestMapping("/feedbackExtract")
    public CommonResult<?> feedbackExtract(FeedbackDto dto) {
        DataHandleCommonDto dataHandleCommonDto = new DataHandleCommonDto();
        BeanUtils.copyProperties(dto, dataHandleCommonDto);
        generaScoreJobService.generateDipScore(dataHandleCommonDto, true);
        return CommonResult.success();
    }

}
