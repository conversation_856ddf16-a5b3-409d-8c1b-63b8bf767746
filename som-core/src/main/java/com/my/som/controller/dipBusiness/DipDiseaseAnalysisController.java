package com.my.som.controller.dipBusiness;

import com.my.som.common.api.CommonPage;
import com.my.som.common.api.CommonResult;
import com.my.som.common.util.BeanUtil;
import com.my.som.controller.BaseController;
import com.my.som.dto.dipBusiness.HospitalAnalysisDto;
import com.my.som.service.dipBusiness.DipDiseaseAnalysisService;
import com.my.som.vo.common.CommonObject;
import com.my.som.vo.dipBusiness.DipDiseaseAnalysisInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;

@Controller
@Api(tags = "DipDiseaseAnalysisController", description = "医院病种分析")
@RequestMapping("/dipDiseaseAnalysis")
public class DipDiseaseAnalysisController extends BaseController {
    @Autowired
    private DipDiseaseAnalysisService dipdiseaseAnalysisService;

    @ApiOperation("查询医院病种分析主要信息")
    @RequestMapping(value = "/mainInfoList", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<CommonPage<DipDiseaseAnalysisInfo>> list(HospitalAnalysisDto queryParam,
                                                                 @RequestParam(value = "pageSize", defaultValue = "100") Integer pageSize,
                                                                 @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum) {
//        BeanUtil.copyPropertiesIgnoreNull(getUserBaseInfo(), queryParam);
        List<DipDiseaseAnalysisInfo> dipDiseaseAnalysisInfoList = dipdiseaseAnalysisService.list(queryParam, pageSize, pageNum);
        return CommonResult.success(CommonPage.restPage(dipDiseaseAnalysisInfoList));
    }

    @ApiOperation("查询医院病种分析统计信息")
    @RequestMapping(value = "/getCountInfo", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<List<CommonObject>> getCountInfo(HospitalAnalysisDto queryParam) {
//        BeanUtil.copyPropertiesIgnoreNull(getUserBaseInfo(), queryParam);
        List<CommonObject> commonObject = dipdiseaseAnalysisService.getCountInfo(queryParam);
        return CommonResult.success(commonObject);
    }

}
