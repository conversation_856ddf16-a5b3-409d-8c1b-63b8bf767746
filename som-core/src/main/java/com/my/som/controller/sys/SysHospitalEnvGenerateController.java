package com.my.som.controller.sys;

import com.my.som.common.api.CommonResult;
import com.my.som.common.util.ExcelUtil;
import com.my.som.dto.SysHospitalEnvGenerateDto;
import com.my.som.entity.env.EvnTodoItem;
import com.my.som.service.sys.SysHospitalEnvGenerateService;
import com.my.som.vo.SysHospitalEnvGenerateVo;
import com.my.som.vo.SysHospitalTableVo;
import com.my.som.vo.sys.EnvConfirmVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * @author: zyd
 * @version 1.0
 * @date: 2023-07-31
 * @description:
 */
@RestController
@RequestMapping("/sysHospitalEnvGenerateController")
public class SysHospitalEnvGenerateController {

    @Autowired
    private SysHospitalEnvGenerateService sysHospitalEnvGenerateService;

    /**
     * 删除用户组织架构和用户账号角色信息
     * @return
     */
    @RequestMapping("/deleteUserConstruction")
    public CommonResult deleteUserConstruction(SysHospitalEnvGenerateDto dto){
        sysHospitalEnvGenerateService.deleteUserConstruction(dto);
        return CommonResult.success();
    }

    /**
     * 查询科室和医生信息
     * @param dto
     * @return
     */
    @RequestMapping("/queryDeptAndDoctorInfo")
    public CommonResult<SysHospitalEnvGenerateVo> queryDeptAndDoctorInfo(SysHospitalEnvGenerateDto dto){
        return CommonResult.success(sysHospitalEnvGenerateService.queryDeptAndDoctorInfo(dto));
    }

    /**
     * 修改系统配置
     * @return
     */
    @RequestMapping("/modifySysConfig")
    public CommonResult modifySysConfig(@RequestBody SysHospitalEnvGenerateDto dto){
        sysHospitalEnvGenerateService.modifySysConfig(dto);
        return CommonResult.success();
    }

    /**
     * 删除所有环境
     * @return
     */
    @RequestMapping("/truncateAllEnv")
    public CommonResult truncateAllEnv(@RequestBody SysHospitalEnvGenerateDto dto){
        sysHospitalEnvGenerateService.truncateAllEnv(dto);
        return CommonResult.success();
    }

    /**
     * 生成医院标杆
     * @return
     */
    @RequestMapping("/generateHosBenchmark")
    public CommonResult generateHosBenchmark(SysHospitalEnvGenerateDto dto){
        sysHospitalEnvGenerateService.generateHosBenchmark(dto);
        return CommonResult.success();
    }

    /**
     * 上传DRG数据信息
     * @param file
     * @param dto
     * @return
     */
    @RequestMapping("/uploadDrgData")
    public CommonResult uploadDrgData(@RequestParam("file") MultipartFile file,SysHospitalEnvGenerateDto dto){
        sysHospitalEnvGenerateService.uploadDrgData(file,dto);
        return CommonResult.success();
    }

    /**
     * 下载模板
     * @return
     */
    @RequestMapping("/downloadDrgData")
    public CommonResult downloadDrgData(HttpServletResponse response) throws IOException {
        ExcelUtil.exportTemplateToWeb(response,"DRG信息模板","classpath:templates/drgData.xlsx");
        return CommonResult.success();
    }

    /**
     * 查询需要更新的表的数据
     * @param dto
     * @return
     */
    @RequestMapping("/selectTableCount")
    public CommonResult<List<EvnTodoItem>> selectUpdateTableInfo(@RequestBody SysHospitalEnvGenerateDto dto){
        List<EvnTodoItem> list = sysHospitalEnvGenerateService.selectUpdateTableInfo(dto);
        return CommonResult.success(list);
    }

    /**
     * 更新确认项
     * @param dto
     * @return
     */
    @RequestMapping("/updateConfirm")
    public CommonResult updateConfirm(@RequestBody SysHospitalEnvGenerateDto dto){
        sysHospitalEnvGenerateService.updateConfirm(dto);
        return CommonResult.success();
    }

    /**
     * 查询确认项
     * @param dto
     * @return
     */
    @RequestMapping("/queryConfirm")
    public CommonResult<?> queryConfirm(@RequestBody SysHospitalEnvGenerateDto dto){
        List<EnvConfirmVo> list = sysHospitalEnvGenerateService.queryConfirm(dto);
        return CommonResult.success(list);
    }
}
