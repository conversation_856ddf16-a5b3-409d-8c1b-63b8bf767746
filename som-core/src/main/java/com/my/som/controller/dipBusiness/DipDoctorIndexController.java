package com.my.som.controller.dipBusiness;

import com.my.som.common.api.CommonPage;
import com.my.som.common.api.CommonResult;
import com.my.som.common.util.BeanUtil;
import com.my.som.controller.BaseController;
import com.my.som.dto.dipBusiness.DipBusinessQueryDto;
import com.my.som.service.dipBusiness.DipDoctorIndexService;
import com.my.som.vo.dipBusiness.DipDoctorCountVo;
import com.my.som.vo.dipBusiness.DipDoctorIndexVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;

/**
 * 成都分组医生指标Controller
 * Created by sky on 2020/7/13.
 */
@Controller
@Api(tags = "DipDoctorIndexController", description = "医生病组指标")
@RequestMapping("/dipDoctorIndex")
public class DipDoctorIndexController extends BaseController {
    @Autowired
    private DipDoctorIndexService dipDoctorIndexService;

    @ApiOperation("查询医生病组指标信息")
    @RequestMapping(value = "/getList", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<CommonPage<DipDoctorIndexVo>> list(DipBusinessQueryDto queryParam,
                                                           @RequestParam(value = "pageSize", defaultValue = "200") Integer pageSize,
                                                           @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum) {
//        BeanUtil.copyPropertiesIgnoreNull(getUserBaseInfo(), queryParam);
        List<DipDoctorIndexVo> dipDoctorIndexVoList = dipDoctorIndexService.list(queryParam, pageSize, pageNum);
        return CommonResult.success(CommonPage.restPage(dipDoctorIndexVoList));
    }

    @ApiOperation("查询医生病组指标统计信息")
    @RequestMapping(value = "/getCountInfo", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<List<DipDoctorCountVo>> getCountInfo(DipBusinessQueryDto queryParam) {
//        BeanUtil.copyPropertiesIgnoreNull(getUserBaseInfo(), queryParam);
        List<DipDoctorCountVo> dipDoctorCountVoList = dipDoctorIndexService.getCountInfo(queryParam);
        return CommonResult.success(dipDoctorCountVoList);
    }

}
