package com.my.som.controller.dipBusiness;

import com.my.som.common.api.CommonMapResult;
import com.my.som.common.api.CommonPage;
import com.my.som.common.api.CommonResult;
import com.my.som.common.util.BeanUtil;
import com.my.som.controller.BaseController;
import com.my.som.dto.dipBusiness.DipBusinessQueryDto;
import com.my.som.service.dipBusiness.DipPayToPredictService;
import com.my.som.vo.dipBusiness.DipPayToPredictVo;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: zyd
 * @Date 2021/8/19 5:22 下午
 * @Version 1.0
 * @Description: dip 支付预测
 */
@RestController
@RequestMapping("/dipPayToPredictController")
public class DipPayToPredictController extends BaseController {

    @Resource
    private DipPayToPredictService dipPayToPredictService;

    /**
     * 获取表格数
     * @param queryParam 查询条件
     * @param pageSize 分页数量
     * @param pageNum 当前页数
     * @return
     */
    @RequestMapping("/getList")
    public CommonResult<CommonPage<DipPayToPredictVo>> getList(DipBusinessQueryDto queryParam,
                                                                @RequestParam(value = "pageSize", defaultValue = "200") Integer pageSize,
                                                                @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum) {
//        BeanUtil.copyPropertiesIgnoreNull(getUserBaseInfo(), queryParam);
        List<DipPayToPredictVo> List = dipPayToPredictService.getList(queryParam, pageSize, pageNum);
        return CommonResult.success(CommonPage.restPage(List));
    }

    /**
     * 查询DIP逆差预测信息
     * @param dto 参数
     * @return DIP逆差预测信息
     */
    @RequestMapping("/getListPoint")
    public CommonResult getListPoint(DipBusinessQueryDto dto){
        CommonMapResult result = dipPayToPredictService.getListPoint(dto);
        return CommonResult.success(result);
    }


    /**
     * 查询前几月数据
     * @param dto 参数
     * @return 数据信息
     */
    @RequestMapping("/getMonthData")
    public CommonResult getMonthData(DipBusinessQueryDto dto){
        CommonMapResult result = dipPayToPredictService.getMonthData(dto);
        return CommonResult.success(result);
    }
}
