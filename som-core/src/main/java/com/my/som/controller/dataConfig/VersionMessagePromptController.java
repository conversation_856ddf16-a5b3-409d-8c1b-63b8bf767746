package com.my.som.controller.dataConfig;

import com.my.som.common.api.CommonDictResult;
import com.my.som.common.api.CommonResult;
import com.my.som.dto.dataConfig.VersionMessagePromptDto;
import com.my.som.service.dataConfig.DipDiseaseTypeConfigService;

import com.my.som.service.dataConfig.VersionMessagePromptService;
import com.my.som.vo.common.BursterConfigVo;
import com.my.som.vo.dataConfig.VersionMessagePromptVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

@RestController
@RequestMapping("/versionMessagePromptController")
public class VersionMessagePromptController {
    @Autowired
    private VersionMessagePromptService versionMessagePromptService;

    /**
     * 查询版本更新数据
     * @param dto
     * @return
     */
    @RequestMapping("/queryData")
    public CommonResult<List<VersionMessagePromptVo>> queryData(@RequestBody VersionMessagePromptDto dto){
        List<VersionMessagePromptVo> list = versionMessagePromptService.queryData(dto);
        return  CommonResult.success(list);
    }

    /**
     * 删除版本更新数据
     * @param dto
     * @return
     */
    @RequestMapping("/deleteData")
    public CommonResult deleteData(VersionMessagePromptDto dto){
        versionMessagePromptService.deleteData(dto);
        return CommonResult.success();
    }

    /**
     * 增加版本更新数据
     * @param dto
     * @return
     */
    @RequestMapping("/addData")
    public CommonResult addData(VersionMessagePromptDto dto){
        versionMessagePromptService.addData(dto);
        return CommonResult.success();
    }

    /**
     * 修改版本更新数据
     * @param dto
     * @return
     */
    @RequestMapping("/updateData")
    public CommonResult updateData(VersionMessagePromptDto dto){
        versionMessagePromptService.updateData(dto);
        return CommonResult.success();
    }

    /**
     * 更新用户是否查看历史更新消息
     * @param dto
     * @return
     */
    @RequestMapping("/updateSys")
    public CommonResult updateSys(VersionMessagePromptDto dto){
        versionMessagePromptService.updateSys(dto);
        return CommonResult.success();
    }

    /**
     * 更新用户查看消息状态
     * @param dto
     * @return
     */
    @RequestMapping("/updateMessage")
    public CommonResult updateMessage(@RequestBody VersionMessagePromptDto dto){
        versionMessagePromptService.updateMessage(dto);
        return CommonResult.success();
    }
}
