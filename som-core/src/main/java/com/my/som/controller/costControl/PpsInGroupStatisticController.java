package com.my.som.controller.costControl;

import com.my.som.common.api.CommonPage;
import com.my.som.common.api.CommonResult;
import com.my.som.common.util.BeanUtil;
import com.my.som.controller.BaseController;
import com.my.som.dto.costControl.CostControlQueryParam;
import com.my.som.service.costControl.PpsInGroupStatisticService;
import com.my.som.vo.common.CommonObject;
import com.my.som.vo.costControl.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;

/**
 * 成都分组入组情况Controller
 * Created by sky on 2020/6/16.
 */
@Controller
@Api(tags = "PpsInGroupStatisticController", description = "成都入组情况")
@RequestMapping("/ppsInGroupStatistic")
public class PpsInGroupStatisticController extends BaseController {
    @Autowired
    private PpsInGroupStatisticService ppsInGroupStatisticService;

    @ApiOperation("查询全院成都入组情况")
    @RequestMapping(value = "/getList", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<CommonPage<PpsInGroupStatisticVo>> getList(CostControlQueryParam queryParam,
                                                               @RequestParam(value = "pageSize", defaultValue = "200") Integer pageSize,
                                                               @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum) {
//        BeanUtil.copyPropertiesIgnoreNull(getUserBaseInfo(), queryParam);
        List<PpsInGroupStatisticVo> ppsInGroupStatisticVoList = ppsInGroupStatisticService.list(queryParam, pageSize, pageNum);
        return CommonResult.success(CommonPage.restPage(ppsInGroupStatisticVoList));
    }

    @ApiOperation("查询全院成都入组数量指标情况")
    @RequestMapping(value = "/getCount", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<PpsInGroupCountVo> getCount(CostControlQueryParam queryParam) {
//        BeanUtil.copyPropertiesIgnoreNull(getUserBaseInfo(), queryParam);
        PpsInGroupCountVo ppsInGroupCountVo = ppsInGroupStatisticService.getCount(queryParam);
        return CommonResult.success(ppsInGroupCountVo);
    }

    @ApiOperation("查询全院成都入组类别指标情况")
    @RequestMapping(value = "/getInGroupIndex", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<List<CommonObject>> getInGroupIndex(CostControlQueryParam queryParam) {
//        BeanUtil.copyPropertiesIgnoreNull(getUserBaseInfo(), queryParam);
        List<CommonObject> commonObjectList = ppsInGroupStatisticService.getInGroupIndex(queryParam);
        return CommonResult.success(commonObjectList);
    }

    @ApiOperation("查询成都未入组原因统计信息")
    @RequestMapping(value = "/getNotInPpsGroup", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<List<NotInPpsGroupCountVo>> getNotInPpsGroup(CostControlQueryParam queryParam) {
//        BeanUtil.copyPropertiesIgnoreNull(getUserBaseInfo(), queryParam);
        List<NotInPpsGroupCountVo> notInPpsGroupCountList = ppsInGroupStatisticService.getNotInPpsGroup(queryParam);
        return CommonResult.success(notInPpsGroupCountList);
    }
}
