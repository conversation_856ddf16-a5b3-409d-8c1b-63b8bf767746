package com.my.som.controller.upload;

import com.alibaba.excel.EasyExcel;
import com.my.som.common.api.CommonResult;
import com.my.som.common.constant.DrgConst;
import com.my.som.common.exception.AppException;
import com.my.som.controller.BaseController;
import com.my.som.dto.upload.DeptUploadDto;
import com.my.som.dto.upload.FileUploadDto;
import com.my.som.listener.upload.DeptFileUploadListener;
import com.my.som.service.upload.DeptFileUploadService;
import com.my.som.common.util.ExcelUtil;
import com.my.som.vo.upload.FileUploadVo;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * @author: zyd
 * @Date 2021/8/6 4:11 下午
 * @Version 1.0
 * @Description:    科室信息文件上传
 */
@RestController
@RequestMapping("/deptFileUploadController")
public class DeptFileUploadController extends BaseController {

    @Resource
    private DeptFileUploadService deptFileUploadService;

    /**
     * 科室文件上传
     * @param file
     * @return
     */
    @RequestMapping("/deptUpload")
    public CommonResult deptUpload(@RequestParam("file") MultipartFile file, FileUploadDto dto) throws IOException {
//        dto.setHospitalId(getUserBaseInfo().getHospitalId());
        dto.setName(file.getOriginalFilename());
        dto.setType(DrgConst.FILE_UPLOAD_TYPE_DEPT);
        try {
            dto.setState(DrgConst.START_FLAG_1);
            EasyExcel.read(file.getInputStream(), DeptUploadDto.class, new DeptFileUploadListener(deptFileUploadService, dto)).sheet().doRead();
        } catch (Exception e){
            dto.setErrMsg(e.getCause().getLocalizedMessage());
            dto.setState(DrgConst.START_FLAG_0);
            throw new AppException(e.getCause().getLocalizedMessage());
        } finally {
            deptFileUploadService.saveLog(dto);
        }
        return CommonResult.success();
    }

    /**
     * 下载模板
     * @return
     */
    @RequestMapping("/downloadTemplate")
    public void downloadTemplate(HttpServletResponse response) throws IOException {
        ExcelUtil.exportTemplateToWeb(response,"科室模板", "templates/deptTemplate.xlsx");
    }

    /**
     * 查询科室文件上传日志
     * @param dto
     * @return
     * @throws IOException
     */
    @RequestMapping("/queryDeptFileUploadLog")
    public CommonResult<List<FileUploadVo>> queryFileUploadLog(FileUploadDto dto) throws IOException {
        List<FileUploadVo> list = deptFileUploadService.queryDeptFileUploadLog(dto);
        return CommonResult.success(list);
    }

}
