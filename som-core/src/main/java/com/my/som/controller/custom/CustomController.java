package com.my.som.controller.custom;

import com.my.som.common.api.CommonPage;
import com.my.som.common.api.CommonResult;
import com.my.som.dto.common.BenchmarkConfigDto;
import com.my.som.service.custom.CustomService;
import com.my.som.vo.common.BenchmarkConfigVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * @author: zyd
 * @version 1.0
 * @date: 2023-07-31
 * @description:
 */
@RestController
@RequestMapping("/customController")
public class CustomController {

    @Autowired
    private CustomService customService;

    /**
     * 查询数据
     * @param dto 参数
     * @return
     */
    @RequestMapping("/queryData")
    public CommonResult<CommonPage<BenchmarkConfigVo>> queryData(BenchmarkConfigDto dto){
        return CommonResult.success(CommonPage.restPage(customService.queryData(dto)));
    }

    @RequestMapping("/queryDatas")
    public CommonResult<CommonPage<BenchmarkConfigVo>> queryDatas(@RequestBody BenchmarkConfigDto dto){
        return CommonResult.success(CommonPage.restPage(customService.queryDatas(dto)));
    }
}
