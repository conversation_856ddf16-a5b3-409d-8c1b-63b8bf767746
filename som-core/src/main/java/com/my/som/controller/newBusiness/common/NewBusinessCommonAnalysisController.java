package com.my.som.controller.newBusiness.common;

import com.my.som.common.api.CommonPage;
import com.my.som.common.api.CommonResult;
import com.my.som.common.vo.SomBackUser;
import com.my.som.dto.newBusiness.NewBusinessCommonDto;
import com.my.som.dto.newBusiness.dept.NewBusinessDeptDto;
import com.my.som.service.newBusiness.common.NewBusinessCommonAnalysisService;
import com.my.som.vo.dipBusiness.DipInGroupAnalysisVo;
import com.my.som.vo.newBusiness.NewBusinessAnalysisVo;
import com.my.som.vo.newBusiness.NewBusinessCommonVo;
import com.my.som.vo.newBusiness.dept.NewBusinessDeptVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @author: zyd
 * @version 1.0
 * @date: 2023-07-31
 * @description:
 */
@RestController
@RequestMapping("/newBusinessCommonAnalysisController")
public class NewBusinessCommonAnalysisController {

    @Autowired
    private NewBusinessCommonAnalysisService newBusinessCommonAnalysisService;

    /**
     * 查询病组亏损
     * @return
     */
    @RequestMapping("queryDiseaseLoss")
    public CommonResult<CommonPage<NewBusinessAnalysisVo>> queryDiseaseLoss(NewBusinessCommonDto dto){
        List<NewBusinessAnalysisVo> list = newBusinessCommonAnalysisService.queryDiseaseLoss(dto);
        return CommonResult.success(CommonPage.restPage(list));
    }

    /**
     * 查询医生亏损
     * @return
     */
    @RequestMapping("queryDoctorLoss")
    public CommonResult<CommonPage<NewBusinessAnalysisVo>> queryDoctorLoss(NewBusinessCommonDto dto){
        List<NewBusinessAnalysisVo> list = newBusinessCommonAnalysisService.queryDoctorLoss(dto);
        return CommonResult.success(CommonPage.restPage(list));
    }

    /**
     * 查询患者亏损
     * @return
     */
    @RequestMapping("queryPatientLoss")
    public CommonResult<CommonPage<NewBusinessAnalysisVo>> queryPatientLoss(NewBusinessCommonDto dto){
        List<NewBusinessAnalysisVo> list = newBusinessCommonAnalysisService.queryPatientLoss(dto);
        return CommonResult.success(CommonPage.restPage(list));
    }

    /**
     * 查询下拉
     * @return
     */
    @RequestMapping("queryDropdown")
    public CommonResult<List<NewBusinessCommonVo>> queryDropdown(NewBusinessCommonDto dto){
        return CommonResult.success(newBusinessCommonAnalysisService.queryDropdown(dto));
    }

    /**
     * 查询入组错误
     * @return
     */
    @RequestMapping("queryMedError")
    public CommonResult<List<DipInGroupAnalysisVo>> queryMedError(NewBusinessCommonDto dto){
        return CommonResult.success(newBusinessCommonAnalysisService.queryMedError(dto));
    }


    /**
     * 查询汇总页面表格数据
     * @return
     */
    @RequestMapping("queryAnalysisSummary")
    public CommonResult<List<NewBusinessAnalysisVo>> queryAnalysisSummary(NewBusinessCommonDto dto){
        return CommonResult.success(newBusinessCommonAnalysisService.queryAnalysisSummary(dto));
    }

    @RequestMapping("queryDoctorDropDown")
    public CommonResult<List<NewBusinessCommonVo>> queryDoctorDropDown(){
        List<NewBusinessCommonVo> list = newBusinessCommonAnalysisService.queryDoctorDropDown();
        return CommonResult.success(list);
    }

    @RequestMapping("/updateSwitchState")
    public CommonResult updateSwitchState(SomBackUser dto) {
        newBusinessCommonAnalysisService.updateSwitchState(dto);
        return CommonResult.success();
    }



    @RequestMapping("queryDrgDiseaseLoss")
    public CommonResult<CommonPage<NewBusinessAnalysisVo>> queryDrgDiseaseLoss(NewBusinessCommonDto dto){
        List<NewBusinessAnalysisVo> list = newBusinessCommonAnalysisService.queryDrgDiseaseLoss(dto);
        return CommonResult.success(CommonPage.restPage(list));
    }

    @RequestMapping("queryDrgDoctorLoss")
    public CommonResult<CommonPage<NewBusinessAnalysisVo>> queryDrgDoctorLoss(NewBusinessCommonDto dto){
        List<NewBusinessAnalysisVo> list = newBusinessCommonAnalysisService.queryDrgDoctorLoss(dto);
        return CommonResult.success(CommonPage.restPage(list));
    }

    @RequestMapping("queryDrgPatientLoss")
    public CommonResult<CommonPage<NewBusinessAnalysisVo>> queryDrgPatientLoss(NewBusinessCommonDto dto){
        List<NewBusinessAnalysisVo> list = newBusinessCommonAnalysisService.queryDrgPatientLoss(dto);
        return CommonResult.success(CommonPage.restPage(list));
    }

    @RequestMapping("queryDrgAnalysisSummary")
    public CommonResult<List<NewBusinessAnalysisVo>> queryDrgAnalysisSummary(NewBusinessCommonDto dto){
        return CommonResult.success(newBusinessCommonAnalysisService.queryDrgAnalysisSummary(dto));
    }

    @RequestMapping("queryDrgMedError")
    public CommonResult<List<DipInGroupAnalysisVo>> queryDrgMedError(NewBusinessCommonDto dto){
        return CommonResult.success(newBusinessCommonAnalysisService.queryDrgMedError(dto));
    }

    @RequestMapping("queryDrgDropdown")
    public CommonResult<List<NewBusinessCommonVo>> queryDrgDropdown(NewBusinessCommonDto dto){
        return CommonResult.success(newBusinessCommonAnalysisService.queryDrgDropdown(dto));
    }

    /**
     * 对比数据
     * @param dto
     * @return
     */
    @PostMapping("/queryContrastData")
    public CommonResult queryContrastData(NewBusinessCommonDto dto) {
        List<NewBusinessAnalysisVo> list = newBusinessCommonAnalysisService.queryContrastData(dto);
        return CommonResult.success(list);
    }

    @PostMapping("/queryDeptDropdown")
    public CommonResult queryDeptDropdown(NewBusinessCommonDto dto) {
        List<NewBusinessCommonVo> list = newBusinessCommonAnalysisService.queryDeptDropdown(dto);
        return CommonResult.success(list);
    }

    @PostMapping("/queryDoctorDropdown")
    public CommonResult queryDoctorDropdown(NewBusinessCommonDto dto) {
        List<NewBusinessCommonVo> list = newBusinessCommonAnalysisService.queryDoctorDropdown(dto);
        return CommonResult.success(list);
    }

    @PostMapping("/queryDiseaseDropdown")
    public CommonResult queryDiseaseDropdown(NewBusinessCommonDto dto) {
        List<NewBusinessCommonVo> list = newBusinessCommonAnalysisService.queryDiseaseDropdown(dto);
        return CommonResult.success(list);
    }

    @PostMapping("/queryPatientContrastData")
    public CommonResult queryPatientContrastData(NewBusinessCommonDto dto) {
        List<NewBusinessAnalysisVo> list = newBusinessCommonAnalysisService.queryPatientContrastData(dto);
        return CommonResult.success(list);
    }

    @PostMapping("/queryDoctorContrastData")
    public CommonResult queryDoctorContrastData(NewBusinessCommonDto dto) {
        List<NewBusinessAnalysisVo> list = newBusinessCommonAnalysisService.queryDoctorContrastData(dto);
        return CommonResult.success(list);
    }

    @PostMapping("/selectPatientContrastData")
    public CommonResult selectPatientContrastData(NewBusinessCommonDto dto) {
        List<NewBusinessAnalysisVo> list = newBusinessCommonAnalysisService.selectPatientContrastData(dto);
        return CommonResult.success(list);
    }

    /**
     * 查询患者亏损
     * @return
     */
    @RequestMapping("/queryPatientLoss2")
    public CommonResult<CommonPage<NewBusinessAnalysisVo>> queryPatientLoss2(NewBusinessCommonDto dto){
        List<NewBusinessAnalysisVo> list = newBusinessCommonAnalysisService.queryPatientLoss2(dto);
        return CommonResult.success(CommonPage.restPage(list));
    }

    @RequestMapping("/queryDrgPatientLoss2")
    public CommonResult<CommonPage<NewBusinessAnalysisVo>> queryDrgPatientLoss2(NewBusinessCommonDto dto){
        List<NewBusinessAnalysisVo> list = newBusinessCommonAnalysisService.queryDrgPatientLoss2(dto);
        return CommonResult.success(CommonPage.restPage(list));
    }

    /**
     * 查询病组象限图数据
     * @param dto
     * @return
     */
    @RequestMapping("/selectSickGroupQuadrant")
    public CommonResult<List<NewBusinessDeptVo>> selectSickGroupQuadrant(NewBusinessCommonDto dto){
        List<NewBusinessDeptVo> list = newBusinessCommonAnalysisService.selectSickGroupQuadrant(dto);
        return CommonResult.success(list);
    }

}
