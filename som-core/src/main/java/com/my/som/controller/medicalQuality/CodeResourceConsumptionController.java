package com.my.som.controller.medicalQuality;

import com.my.som.common.api.CommonPage;
import com.my.som.common.api.CommonResult;
import com.my.som.common.util.BeanUtil;
import com.my.som.controller.BaseController;
import com.my.som.dto.medicalQuality.*;
import com.my.som.service.medicalQuality.CodeResourceConsumptionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;

/**
 * 疾病诊断资源消耗Controller
 * Created by sky on 2020/3/3.
 */
@Controller
@Api(tags = "CodeResourceConsumptionController", description = "疾病诊断资源消耗")
@RequestMapping("/codeResourceConsumption")
public class CodeResourceConsumptionController extends BaseController {
    @Autowired
    private CodeResourceConsumptionService codeResourceConsumptionService;

    @ApiOperation("查询所有疾病诊断资源消耗结果")
    @RequestMapping(value = "/getList", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<CommonPage<CodeResourceConsumptionVo>> getList(SettleListMainInfoQueryParam queryParam,
                                                                       @RequestParam(value = "pageSize", defaultValue = "200") Integer pageSize,
                                                                       @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum) {
//        BeanUtil.copyPropertiesIgnoreNull(getUserBaseInfo(), queryParam);
        List<CodeResourceConsumptionVo> list = codeResourceConsumptionService.list(queryParam, pageSize, pageNum);
        return CommonResult.success(CommonPage.restPage(list));
    }

    @ApiOperation("查询疾病诊断资源消耗统计信息")
    @RequestMapping(value = "/getCountInfo", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<CodeResourceConsumptionCountVo> getCountInfo(SettleListMainInfoQueryParam queryParam) {
//        BeanUtil.copyPropertiesIgnoreNull(getUserBaseInfo(), queryParam);
        CodeResourceConsumptionCountVo codeResourceConsumptionCountVo = codeResourceConsumptionService.getCountInfo(queryParam);
        return CommonResult.success(codeResourceConsumptionCountVo);
    }

}
