package com.my.som.controller.hcs;

import com.my.som.common.api.CommonPage;
import com.my.som.common.api.CommonResult;
import com.my.som.dto.hcs.ExamCorrectionRuleParam;
import com.my.som.dto.hcs.ExamCorrectionTupleParam;
import com.my.som.service.hcs.ExamCorrectionService;
import com.my.som.vo.hcs.ExamCorrectionRuleVo;
import com.my.som.vo.hcs.ExamCorrectionTupleVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;

@Controller
@Api(tags = "ExamCorrectionController", description = "自查自纠质控规则和元组控制层")
@RequestMapping("/examCorrectionController")
public class ExamCorrectionController {

    @Autowired
    private ExamCorrectionService examCorrectionService;


    @ApiOperation("查询自查自纠总规则")
    @RequestMapping(value = "/getRuleList", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<CommonPage<ExamCorrectionRuleVo>> getRuleList(ExamCorrectionRuleParam queryParam,
                                                               @RequestParam(value = "pageSize", defaultValue = "200") Integer pageSize,
                                                               @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum) {
//        BeanUtil.copyPropertiesIgnoreNull(getUserBaseInfo(), queryParam);
        List<ExamCorrectionRuleVo> examCorrectionList = examCorrectionService.getRuleList(queryParam, pageSize, pageNum);
        return CommonResult.success(CommonPage.restPage(examCorrectionList));
    }


    @ApiOperation("查询自查自纠元素列表类型")
    @RequestMapping(value = "/getTupleTypeList", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<List<ExamCorrectionTupleVo>> getTupleTypeList() {
        List<ExamCorrectionTupleVo> examCorrectionList = examCorrectionService.getTupleTypeList();
        return CommonResult.success(examCorrectionList);
    }

    @ApiOperation("查询自查自纠元素信息")
    @RequestMapping(value = "/queryTupleList", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<CommonPage<ExamCorrectionTupleVo>> queryTupleList(ExamCorrectionTupleParam queryParam,
                                                                         @RequestParam(value = "pageSize", defaultValue = "200") Integer pageSize,
                                                                         @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum) {
//        BeanUtil.copyPropertiesIgnoreNull(getUserBaseInfo(), queryParam);
        List<ExamCorrectionTupleVo> examCorrectionList = examCorrectionService.queryTupleList(queryParam, pageSize, pageNum);
        return CommonResult.success(CommonPage.restPage(examCorrectionList));
    }

    @ApiOperation("保存自查自纠元素信息")
    @RequestMapping(value = "/saveNewRule", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<CommonPage<ExamCorrectionTupleVo>> saveNewRule(ExamCorrectionRuleVo examCorrectionRuleVo) {
        ExamCorrectionRuleVo newRule =  examCorrectionService.handleRule(examCorrectionRuleVo);
        List<ExamCorrectionTupleVo> tuples = examCorrectionService.handleTuple(examCorrectionRuleVo);
         examCorrectionService.saveNewRule(newRule , tuples);
        return CommonResult.success();
    }

    @RequestMapping(value = "/queryDataGroup")
    @ResponseBody
    public CommonResult<List<ExamCorrectionTupleVo>> queryDataGroup() {
        return CommonResult.success(examCorrectionService.getDataGroup());
    }
}
