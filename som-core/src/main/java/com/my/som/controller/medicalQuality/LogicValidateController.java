package com.my.som.controller.medicalQuality;

import com.my.som.common.api.CommonPage;
import com.my.som.common.api.CommonResult;
import com.my.som.common.util.BeanUtil;
import com.my.som.controller.BaseController;
import com.my.som.dto.medicalQuality.LogicValidateCountInfo;
import com.my.som.dto.medicalQuality.SettleListMainInfoQueryParam;
import com.my.som.dto.medicalQuality.ValidateMainInfo;
import com.my.som.service.medicalQuality.LogicValidateService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;

/**
 * 病例逻辑性校验Controller
 * Created by sky on 2020/3/3.
 */
@Controller
@Api(tags = "LogicValidateController", description = "病例逻辑性校验")
@RequestMapping("/logicValidate")
public class LogicValidateController extends BaseController {
    @Autowired
    private LogicValidateService logicValidateService;

    @ApiOperation("查询所有病例逻辑性性校对结果")
    @RequestMapping(value = "/getList", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<CommonPage<ValidateMainInfo>> getList(SettleListMainInfoQueryParam queryParam,
                                                              @RequestParam(value = "pageSize", defaultValue = "200") Integer pageSize,
                                                              @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum) {
//        BeanUtil.copyPropertiesIgnoreNull(getUserBaseInfo(), queryParam);
        List<ValidateMainInfo> list = logicValidateService.list(queryParam, pageSize, pageNum);
        return CommonResult.success(CommonPage.restPage(list));
    }

    @ApiOperation("查询病例逻辑性校对统计信息")
    @RequestMapping(value = "/getCountInfo", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<LogicValidateCountInfo> getCountInfo(SettleListMainInfoQueryParam queryParam) {
//        BeanUtil.copyPropertiesIgnoreNull(getUserBaseInfo(), queryParam);
        LogicValidateCountInfo logicValidateCountInfo = logicValidateService.getCountInfo(queryParam);
        return CommonResult.success(logicValidateCountInfo);
    }

}
