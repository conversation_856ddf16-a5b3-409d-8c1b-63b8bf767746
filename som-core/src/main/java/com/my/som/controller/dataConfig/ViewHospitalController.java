package com.my.som.controller.dataConfig;

import com.my.som.common.api.CommonResult;
import com.my.som.dto.dataConfig.HospitalInfoDto;
import com.my.som.service.dataConfig.ViewHospitalService;
import com.my.som.vo.dataConfig.ViewHospitalVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/viewHospitalController")
public class ViewHospitalController {

    @Autowired
    private ViewHospitalService viewHospitalService;
    /**
     * 医院信息查询
     * @param
     * @return
     */
    @RequestMapping("/queryHospitalInfo")
    public CommonResult queryHospitalInfo(HospitalInfoDto dto){
        List<ViewHospitalVo> list = viewHospitalService.queryHospitalInfo(dto);
        return CommonResult.success(list);
    }
    /**
     * 医院信息新增
     */
    @RequestMapping("/insertHospitalInfo")
    public CommonResult insertHospitalInfo(HospitalInfoDto dto){
        viewHospitalService.insertHospitalInfo(dto);
        return CommonResult.success();
    }

    /**
     * 医院信息修改
     * @param dto
     * @return
     */
    @RequestMapping("/updateHospitalInfo")
    public CommonResult updateHospitalInfo(HospitalInfoDto dto){
        viewHospitalService.updateHospitalInfo(dto);
        return CommonResult.success();
    }

    /**
     * 医院信息删除
     */
    @RequestMapping("/deleteHospitalInfo")
    public CommonResult deleteHospitalInfo(HospitalInfoDto dto){
        viewHospitalService.deleteHospitalInfo(dto);
        return CommonResult.success();
    }
}
