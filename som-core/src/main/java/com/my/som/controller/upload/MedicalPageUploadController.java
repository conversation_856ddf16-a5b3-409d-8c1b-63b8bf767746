package com.my.som.controller.upload;

import com.my.som.common.api.CommonResult;
import com.my.som.controller.BaseController;
import com.my.som.dto.upload.MedicalRecordUploadDto;
import com.my.som.service.upload.MedicalPageUploadService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

/**
 * 病案首页数据上传
 */
@RestController
@RequestMapping("/medicalPageUploadController")
public class MedicalPageUploadController extends BaseController {

    @Autowired
    private MedicalPageUploadService medicalPageUploadService;

    /**
     * 病案首页数据上传
     * @param file 文件
     */
    @RequestMapping("/medicalPageUpload")
    public CommonResult medicalPageUpload(@RequestParam("file") MultipartFile file, MedicalRecordUploadDto dto) throws Exception {
        dto.setHospitalId(getUserBaseInfo().getHospitalId());
        medicalPageUploadService.analysis(file,dto);
        return CommonResult.success();
    }
}
