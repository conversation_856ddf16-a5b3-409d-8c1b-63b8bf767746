package com.my.som.controller.listManagement;

import com.my.som.common.api.CommonResult;
import com.my.som.controller.BaseController;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/listStatisticsController")
public class ListStatisticsController extends BaseController {

    @PostMapping("/")
    public CommonResult queryUnmarkData() {
        return CommonResult.success();
    }
}
