package com.my.som.controller.hisview;

import com.my.som.common.api.CommonPage;
import com.my.som.common.api.CommonResult;
import com.my.som.dto.hisview.HisViewDTO;
import com.my.som.service.hisview.HisViewService;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;


@RestController
@RequestMapping("/hisView")
public class HisViewController {

    @Autowired
    private HisViewService hisViewService;

    @ApiOperation("视图抽取")
    @PostMapping("/get")
    @ResponseBody
    public CommonResult get(@RequestBody HisViewDTO hisViewDTO) {
        hisViewService.mdcsInfoExtract(hisViewDTO);
        return CommonResult.success();
    }

    @ApiOperation("视图抽取")
    @PostMapping("/getHisFeedeTail")
    @ResponseBody
    public CommonResult getHisFeedeTail(@RequestBody HisViewDTO hisViewDTO) {
        hisViewService.mdcsFeedeTailExtract(hisViewDTO);
        return CommonResult.success();
    }

//    @ApiOperation("视图抽取")
//    @PostMapping("/getTest")
//    @ResponseBody
//    public CommonResult getTest( @RequestBody Map<String, Object> data) {
//      hisViewService.getTest( data );
//        return CommonResult.success();
//    }
    /**
     * 誉美等医院抽取方式
     */
    @ApiOperation("通过视图抽取抽取科室、医生数据")
    @PostMapping("/extractDeptAndDoctoerByView")
    public void extractDeptAndDoctoerByView() {

        Map<String,Object> map = hisViewService.extractDeptAndDoctoerByView();
        Map<String,Object> map1 = hisViewService.extractDeptAndDoctoerByMq();

        hisViewService.updateDeptAndDoctoerByView(map);

    }
}
