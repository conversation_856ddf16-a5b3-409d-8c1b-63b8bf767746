package com.my.som.controller.sys;

import com.my.som.common.api.CommonPage;
import com.my.som.common.api.CommonResult;
import com.my.som.common.page.PageRequest;
import com.my.som.service.sys.SysDictService;
import com.my.som.vo.SomSysCode;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 字典控制器
 * @author: zyd
 * @date: 2023-07-31
 */
@RestController
@Api(tags = "SysDictController",description = "字典管理")
@RequestMapping("/dict")
public class SysDictController {

	@Autowired
	private SysDictService sysDictService;

//	@PreAuthorize("hasAuthority('sys:dict:add') OR hasAuthority('sys:dict:edit')")
	@PostMapping(value="/save")
	public CommonResult save(@RequestBody SomSysCode record) {
		return CommonResult.success(sysDictService.save(record));
	}

//	@PreAuthorize("hasAuthority('sys:dict:delete')")
	@PostMapping(value="/delete")
	public CommonResult delete(@RequestBody List<SomSysCode> records) {
		return CommonResult.success(sysDictService.delete(records));
	}

	@RequestMapping("/refreshCache")
	public CommonResult flushCache() {
		sysDictService.flushCache();
		return CommonResult.success();
	}

//	@PreAuthorize("hasAuthority('sys:dict:view')")
	@PostMapping(value="/findPage")
	public CommonResult<CommonPage<SomSysCode>> findPage(@RequestBody PageRequest pageRequest) {
		return CommonResult.success(sysDictService.findPage(pageRequest));
	}

//	@PreAuthorize("hasAuthority('sys:dict:view')")
	@GetMapping(value="/findByLabel")
	public CommonResult<List<SomSysCode>> findByLabel(@RequestParam String label) {
		return CommonResult.success(sysDictService.findByLabel(label));
	}
}
