package com.my.som.controller.common;

import com.my.som.common.api.CommonDictResult;
import com.my.som.controller.BaseController;
import com.my.som.dto.common.BursterConfigDto;
import com.my.som.service.common.BursterConfigService;
import com.my.som.vo.common.BursterConfigVo;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping("/bursterConfigController")
public class BursterConfigController extends BaseController {

    @Resource
    private BursterConfigService bursterConfigService;
    /**
     * 查询分组器配置数据
     * @return
     */
    @RequestMapping("/queryData")
    public CommonDictResult<List<BursterConfigVo>> queryData(BursterConfigDto dto) {
        List<BursterConfigVo> BursterConfigVoList = bursterConfigService.queryData(dto);
        return CommonDictResult.success(BursterConfigVoList);
    }

    /**
     * 修改分组器配置数据
     * @param dto
     */
    @RequestMapping("/update")
    public CommonDictResult update(BursterConfigDto dto){
        bursterConfigService.update(dto);
        return CommonDictResult.success();
    }
}
