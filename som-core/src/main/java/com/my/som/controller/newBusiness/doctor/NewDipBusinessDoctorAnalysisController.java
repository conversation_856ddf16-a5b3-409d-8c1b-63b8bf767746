package com.my.som.controller.newBusiness.doctor;

import com.my.som.common.api.CommonPage;
import com.my.som.common.api.CommonResult;
import com.my.som.dto.newBusiness.doctor.NewBusinessDoctorDto;
import com.my.som.service.newBusiness.doctor.NewDipBusinessDoctorAnalysisService;
import com.my.som.vo.newBusiness.doctor.NewBusinessDoctorVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/newDipBusinessDoctorAnalysisController")
public class NewDipBusinessDoctorAnalysisController {

    @Autowired
    private NewDipBusinessDoctorAnalysisService newDipBusinessDoctorAnalysisService;

    /**
     * 查询医生kpi
     * @param dto
     * @return
     */
    @RequestMapping("/queryDoctorKpiData")
    public CommonResult<CommonPage<NewBusinessDoctorVo>> queryDoctorKpiData(NewBusinessDoctorDto dto) {
        List<NewBusinessDoctorVo> list = newDipBusinessDoctorAnalysisService.queryDoctorKpiData(dto);
        return CommonResult.success(CommonPage.restPage(list));
    }

    /**
     * 查询医生预测
     * @param dto
     * @return
     */
    @RequestMapping("/queryDoctorForecastData")
    public CommonResult<CommonPage<NewBusinessDoctorVo>> queryDoctorForecastData(NewBusinessDoctorDto dto) {
        List<NewBusinessDoctorVo> list = newDipBusinessDoctorAnalysisService.queryDoctorForecastData(dto);
        return CommonResult.success(CommonPage.restPage(list));
    }
}
