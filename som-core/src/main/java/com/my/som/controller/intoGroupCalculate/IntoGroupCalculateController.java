package com.my.som.controller.intoGroupCalculate;

import com.my.som.common.api.CommonPage;
import com.my.som.common.api.CommonResult;
import com.my.som.common.util.ValidateUtil;
import com.my.som.dto.intoGroupCalculate.IntoGroupCalculateDto;
import com.my.som.paths.entity.PathsEntity;
import com.my.som.service.intoGroupCalculate.IntoGroupCalculateService;
import com.my.som.vo.intoGroupCalculate.IntoGroupCalculateVo;
import com.my.som.vo.intoGroupCalculate.ResultVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: zyd
 * @date: 2023-07-31
 **/
@RestController
@RequestMapping("/intoGroupCalculateController")
public class IntoGroupCalculateController {

    /** 入组测算表 dip t1表 */
    private final String TABLE_NAME_DIP_1 = "bus_into_group_calculate_dip";
    /** 入组测算记录表 dip t2表 */
    private final String TABLE_NAME_DIP_2 = "bus_into_group_calculate_record_dip";
    /** 入组测算表 drg t1表 */
    private final String TABLE_NAME_DRG_1 = "bus_into_group_calculate_drg";
    /** 入组测算记录表 drg t2表 */
    private final String TABLE_NAME_DRG_2 = "bus_into_group_calculate_record_drg";
    /**入组记录表，sts_dip_group_record  */
    private final String SOM_DIP_GRP_RCD = "som_dip_grp_rcd";
    /** 入组记录表，sts_drg_group_record */
    private final String SOM_DRG_GRP_RCD = "som_drg_grp_rcd";
    /** 标杆表 dip */
    private final String SOM_DIP_STANDARD = "som_dip_standard";
    /** 标杆表 drg */
    private final String SOM_DRG_STANDARD = "som_drg_standard";
    /** 入组测算统计表 dip t3表 */
    private final String TABLE_NAME_DIP_3 = "bus_into_group_calculate_count_dip";
    /** 入组测算统计表 drg t3表 */
    private final String TABLE_NAME_DRG_3 = "bus_into_group_calculate_count_drg";
    // 表 som_dip_sco
    private final String PATIENT_SCORE_DIP = "som_dip_sco";

    //表 som_drg_sco
    private final String PATIENT_SCORE_DRG = "som_drg_sco";
    private final String SOM_DIP_GEN_CFG = "som_dip_gen_cfg";
    private final String SOM_DRG_GEN_CFG = "som_drg_gen_cfg";
    //分组类型，1dip，3drg
    private final String GROUP_TYPE_DIP = "1";
    private final String GROUP_TYPE_DRG = "3";
    @Autowired
    private IntoGroupCalculateService intoGroupCalculateService;


    /**
     * 根据条件查询入组信息
     * @param dto
     * @return
     */
    @RequestMapping(value = "/queryIntoGroupCalculate", method = RequestMethod.POST)
    public CommonResult queryIntoGroupCalculate(@RequestBody IntoGroupCalculateDto dto)  {
        setTabName(dto);
        List<IntoGroupCalculateVo> list = intoGroupCalculateService.queryGroupCalculate(dto);
        if(ValidateUtil.isEmpty(list) ) {
            //添加新的记录进去
//            list = intoGroupCalculateService.addNewRecord(dto);
            return CommonResult.success(CommonPage.restPage(Collections.emptyList()));
        }
        //分组
        List<List<IntoGroupCalculateVo>> resList = intoGroupCalculateService.intoGroup(list,dto);
        return CommonResult.success(CommonPage.restPage(resList));
    }

    private void setTabName(IntoGroupCalculateDto dto) {
        if(GROUP_TYPE_DIP.equals(dto.getType())) {
            dto.setTabName(TABLE_NAME_DIP_1);
            dto.setTableName2(TABLE_NAME_DIP_2);
            dto.setTableName3(TABLE_NAME_DIP_3);
            dto.setGroupRecordTable(SOM_DIP_GRP_RCD);
            dto.setBenchmarkTable(SOM_DIP_STANDARD);
            dto.setPatientScoreTable(PATIENT_SCORE_DIP);
            dto.setCfgCommon(SOM_DIP_GEN_CFG);
        } else if (GROUP_TYPE_DRG.equals(dto.getType())) {
            dto.setTabName(TABLE_NAME_DRG_1);
            dto.setTableName2(TABLE_NAME_DRG_2);
            dto.setTableName3(TABLE_NAME_DRG_3);
            dto.setGroupRecordTable(SOM_DRG_GRP_RCD);
            dto.setBenchmarkTable(SOM_DRG_STANDARD);
            dto.setPatientScoreTable(PATIENT_SCORE_DRG);
            dto.setCfgCommon(SOM_DRG_GEN_CFG);
        }
    }

    /**
     * 测算
     * @param dto
     * @return
     */
    @RequestMapping(value = "/calCulate", method = RequestMethod.POST)
    public CommonResult calCulate(@RequestBody IntoGroupCalculateDto dto)  {
        setTabName(dto);
        intoGroupCalculateService.calCulate(dto);
        return CommonResult.success();
    }


    /**
     * 根据 id 集合查询诊断和手术
     * @param dto
     * @return
     */
    @RequestMapping("/queryDiagnoseAndOperation")
    public CommonResult queryDiagnoseAndOperation(@RequestBody IntoGroupCalculateDto dto) throws Exception {
        setTabName(dto);
        List<String> ids = dto.getList().stream().map(IntoGroupCalculateVo::getId).collect(Collectors.toList());
        List<List<IntoGroupCalculateVo>>  list = intoGroupCalculateService.queryDiagnoseAndOperation(ids,dto);
        //诊断和手术 select 值
        ResultVo resultVo = intoGroupCalculateService.setSelectValue(list);
        return CommonResult.success(resultVo);
    }


  /**
     * 查询项目使用
     * @param dto
     * @return
     */
    @RequestMapping("/queryCostDetails")
    public CommonResult queryCostDetails(@RequestBody IntoGroupCalculateDto dto)  {
        setTabName(dto);
        PathsEntity pathsEntity = intoGroupCalculateService.queryCostDetails(dto);
        return CommonResult.success(pathsEntity);
    }

}
