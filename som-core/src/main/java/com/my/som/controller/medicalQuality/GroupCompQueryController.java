package com.my.som.controller.medicalQuality;

import com.my.som.common.api.CommonPage;
import com.my.som.common.api.CommonResult;
import com.my.som.dto.dipBusiness.DipAnalysisDto;
import com.my.som.dto.dipBusiness.DipBusinessQueryDto;
import com.my.som.service.medicalQuality.GroupCompQueryService;
import com.my.som.vo.dataHandle.dataGroup.DipGroupDataInfoVo;
import com.my.som.vo.medicalQuality.DiseaseGroupAnalysisVo;
import com.my.som.vo.medicalQuality.GroupCompQueryVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * @author: zyd
 * @version 1.0
 * @date: 2023-07-31
 * @description:
 */
@RestController
@RequestMapping("groupCompQueryController")
public class GroupCompQueryController {

    @Autowired
    private GroupCompQueryService groupCompQueryService;

    /**
     * 查询表格数据
     * @param dto
     * @return
     */
    @RequestMapping("/getGroupData")
    public CommonResult<?> getGroupData(@RequestBody DipGroupDataInfoVo dto){
        Map<String, Object> map = groupCompQueryService.getGroupData(dto);
        return CommonResult.success(map);
    }

    /**
     * 查询表格数据
     * @param dto
     * @return
     */
    @RequestMapping(value = "/queryRecords", method = RequestMethod.POST)
    public CommonResult<?> queryRecords(@RequestBody DipBusinessQueryDto dto){
        return CommonResult.success(groupCompQueryService.queryRecords(dto));
    }
}
