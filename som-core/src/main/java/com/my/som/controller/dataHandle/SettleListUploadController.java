package com.my.som.controller.dataHandle;

import com.my.som.common.api.CommonResult;
import com.my.som.common.util.ValidateUtil;
import com.my.som.controller.BaseController;
import com.my.som.model.dataHandle.SomDataprosLog;
import com.my.som.service.dataHandle.SettleListUploadService;
import com.my.som.common.vo.SysUserBase;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 结算清单上传Controller
 * Created by sky on 2019/1/2.
 */
@Controller
@Api(tags = "SettleListUploadController", description = "结算清单数据上传")
@RequestMapping("/settleListUpload")
public class SettleListUploadController extends BaseController {
    @Autowired
    private SettleListUploadService settleListUploadService;

   /* @ApiOperation("文件上传")
    @RequestMapping(value = "/fileUpload", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult fileUpload(MultipartHttpServletRequest request){
        MultipartFile file = request.getFile("file");
        //上传文件到FTP服务器
        logInfo.putAll(VfsNewFileUtil.uploadFile(file));
        //文件解析到数据库
        //logInfo.putAll(settleListUploadService.fileParse(file));
        //数据校验
        //int count = settleListUploadService.insertDataBatchInfo(logInfo);
//        if (count > 0) {
//            return CommonResult.success(count);
//        }
//        return CommonResult.success(1);
    }*/

    @ApiOperation("接口上传,通过视图查询病案数据")
    @RequestMapping(value = "/queryMedicalData", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<List<Map<String,Object>>> queryMedicalInfo(@RequestParam("begn_date")String begn_date, @RequestParam("expi_date")String expi_date) throws Exception {
        SysUserBase sysUserBase =  getUserBaseInfo();
        List<Map<String,Object>> list = settleListUploadService.queryMedicalData(begn_date,expi_date,sysUserBase);
        return CommonResult.success(list);
    }

    @ApiOperation("接口上传,写入病案数据")
    @RequestMapping(value = "/savaMedicalData", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult savaMedicalData(@RequestParam("begn_date")String begn_date,@RequestParam("expi_date")String expi_date) throws Exception {
        SysUserBase sysUserBase =  getUserBaseInfo();
        Long logId = settleListUploadService.savaMedicalData(begn_date, expi_date,sysUserBase);
        if(ValidateUtil.isNotEmpty(String.valueOf(logId))){
            return CommonResult.success(logId);
        }
        return CommonResult.failed();
    }

    @ApiOperation("查询正在执行的批次和存在异常的批次")
    @RequestMapping(value = "/getRunningAndExptionProcess", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<List<SomDataprosLog>> getRunningAndExptionProcess(){
        List<SomDataprosLog> list = settleListUploadService.getRunningAndExptionProcess();
        return CommonResult.success(list);
    }

}
