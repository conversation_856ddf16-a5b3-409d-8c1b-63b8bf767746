package com.my.som.controller.preValidate;

import com.my.som.common.api.CommonMapResult;
import com.my.som.dto.preValidate.PreValidateDto;
import com.my.som.service.preValidate.PreValidateInterfaceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.Map;

/**
 * @author: zyd
 * @version 1.0
 * @date: 2023-07-31
 * @description: 预校验
 */
@RestController
@RequestMapping("/preValidateInterface")
public class PreValidateInterfaceController {


    @Autowired
    private PreValidateInterfaceService preValidateInterfaceService;

    /**
     * 预分组返回 url
     * @param dto
     * @param response
     * @param request
     * @return
     */
    @RequestMapping("/preValidate")
    public CommonMapResult preGroup(@RequestBody PreValidateDto dto, HttpServletResponse response, HttpServletRequest request){
        Map<String,Object> map = new HashMap<>();
        map.put("url", preValidateInterfaceService.getPreValidateInfo(dto, request, response).getImageUrl());
        return CommonMapResult.ok(map);
    }
}
