package com.my.som.controller.listManagement;

import com.my.som.common.api.CommonPage;
import com.my.som.common.api.CommonResult;
import com.my.som.dto.listManagement.CheckAnalysisDto;
import com.my.som.service.listManagement.CheckAnalysisService;
import com.my.som.vo.listManagement.CheckAnalysisVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/checkAnalysisController")
public class CheckAnalysisController {

    @Autowired
    private CheckAnalysisService checkAnalysisService;

    /**
     * 查询清单校验错误数量
     * @param dto
     * @return
     */
    @RequestMapping("/queryErrorNum")
    public CommonResult<List<CheckAnalysisVo>> queryErrorNum(CheckAnalysisDto dto) {
        List<CheckAnalysisVo> list = checkAnalysisService.queryErrorNum(dto);
        return CommonResult.success(list);
    }

    /**
     * 查询清单校验错误数据
     * @param dto
     * @return
     */
    @RequestMapping("/queryErrorData")
    public CommonResult<CommonPage<CheckAnalysisVo>> queryErrorData(CheckAnalysisDto dto) {
        List<CheckAnalysisVo> list = checkAnalysisService.queryErrorData(dto);
        return CommonResult.success(CommonPage.restPage(list));
    }

    /**
     * 查询科室错误数量
     * @param dto
     * @return
     */
    @RequestMapping("/queryDeptErrorNum")
    public CommonResult<List<CheckAnalysisVo>> queryDeptErrorNum(CheckAnalysisDto dto) {
        List<CheckAnalysisVo> list = checkAnalysisService.queryDeptErrorNum(dto);
        return CommonResult.success(list);
    }

    /**
     * 查询错误类型
     * @param dto
     * @return
     */
    @PostMapping("/queryErrorType")
    public CommonResult queryErrorType(CheckAnalysisDto dto) {
        List<String> list = checkAnalysisService.queryErrorType(dto);
        return CommonResult.success(list);
    }
}
