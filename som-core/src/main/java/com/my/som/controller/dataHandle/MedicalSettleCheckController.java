package com.my.som.controller.dataHandle;

import com.my.som.common.api.CommonResult;
import com.my.som.dto.dataHandle.MedicalSettleCheckDto;
import com.my.som.service.dataHandle.MedicalSettleCheckService;
import com.my.som.vo.dataHandle.MedicalSettleCheckVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


@RestController
@RequestMapping("/MedicalSettleCheckController")
public class MedicalSettleCheckController {

    @Autowired
    private MedicalSettleCheckService medicalSettleCheckService;

    @RequestMapping("/QueryMedical")
    public CommonResult<List<MedicalSettleCheckVo>> QueryMedical(MedicalSettleCheckDto dto){
        List<MedicalSettleCheckVo> list = medicalSettleCheckService.QueryMedical(dto);
        return CommonResult.success(list);
    }

    @RequestMapping("/updateMedical")
    public CommonResult updateMedical(MedicalSettleCheckDto dto){
        medicalSettleCheckService.updateMedical(dto);
        return CommonResult.success();
    }

    @RequestMapping("/insertMedical")
    public CommonResult insertMedical(MedicalSettleCheckDto dto){
        medicalSettleCheckService.insertMedical(dto);
        return CommonResult.success();
    }

    @RequestMapping("/deleteMedical")
    public CommonResult deleteMedical(MedicalSettleCheckDto dto){
        medicalSettleCheckService.deleteMedical(dto);
        return CommonResult.success();
    }

    @RequestMapping("/updateAllMedical")
    public CommonResult updateAllMedical(MedicalSettleCheckDto dto){
        medicalSettleCheckService.updateAllMedical(dto);
        return CommonResult.success();
    }

    @RequestMapping("/updateStart")
    public CommonResult updateStart(MedicalSettleCheckDto dto){
        medicalSettleCheckService.updateStart(dto);
        return CommonResult.success();
    }

    @RequestMapping("/updateStartFlag")
    public CommonResult updateStartFlag(MedicalSettleCheckDto dto){
        medicalSettleCheckService.updateStartFlag(dto);
        return CommonResult.success();
    }

    @RequestMapping("/update")
    public CommonResult update(MedicalSettleCheckDto dto){
        medicalSettleCheckService.update(dto);
        return CommonResult.success();
    }

    @RequestMapping("/reduction")
    public CommonResult reduction(){
        medicalSettleCheckService.reduction();
        return CommonResult.success();
    }

}
