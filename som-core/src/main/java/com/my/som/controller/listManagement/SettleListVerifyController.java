package com.my.som.controller.listManagement;

import com.my.som.common.api.CommonPage;
import com.my.som.common.api.CommonResult;
import com.my.som.dto.listManagement.PatientInfoDto;
import com.my.som.service.listManagement.SettleListVerifyService;
import com.my.som.vo.listManagement.PatientInfoVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * @author: zyd
 * @version 1.0
 * @date: 2023-07-31
 * @description:
 */
@RestController
@RequestMapping("/settleListVerifyController")
public class SettleListVerifyController {

    @Autowired
    private SettleListVerifyService settleListVerifyService;

    /**
     * 查询核对数据
     * @param dto
     * @return
     */
    @RequestMapping("/queryVerifyInfo")
    public CommonResult<CommonPage<PatientInfoVo>> queryVerifyInfo(@RequestBody PatientInfoDto dto) {
        List<PatientInfoVo> list = settleListVerifyService.queryVerifyInfo(dto);
        return CommonResult.success(CommonPage.restPage(list));
    }

    /**
     * 修改标识状态
     * @param dto
     * @return
     */
    @RequestMapping("/modifyMarkState")
    public CommonResult<?> modifyMarkState(@RequestBody PatientInfoDto dto){
        settleListVerifyService.modifyMarkState(dto);
        return CommonResult.success();
    }

    /**
     * 上传结算数据
     * @param dto
     * @return
     */
    @RequestMapping("/uploadSettleData")
    public CommonResult<?> uploadSettleData(@RequestParam("file") MultipartFile file, PatientInfoDto dto){
        settleListVerifyService.uploadSettleData(file, dto);
        return CommonResult.success();
    }
}
