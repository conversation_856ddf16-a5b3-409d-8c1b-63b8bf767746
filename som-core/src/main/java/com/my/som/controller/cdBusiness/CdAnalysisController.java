package com.my.som.controller.cdBusiness;

import com.my.som.common.api.CommonPage;
import com.my.som.common.api.CommonResult;
import com.my.som.common.util.BeanUtil;
import com.my.som.controller.BaseController;
import com.my.som.dto.cdBusiness.CdAnalysisDto;
import com.my.som.dto.dipBusiness.DipAnalysisDto;
import com.my.som.service.cdBusiness.CdAnalysisService;
import com.my.som.vo.cdBusiness.CdAnalysisCountInfo;
import com.my.som.vo.cdBusiness.CdAnalysisVo;
import com.my.som.vo.dipBusiness.DipAnalysisCountInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;

/**
 * 成都分组分析Controller
 * Created by sky on 2022/3/9.
 */
@Controller
@Api(tags = "CdAnalysisController", description = "医院分组分析")
@RequestMapping("/cdAnalysis")
public class CdAnalysisController extends BaseController {
    @Autowired
    private CdAnalysisService cdAnalysisService;

    @ApiOperation("查询医院分组分析主要信息")
    @RequestMapping(value = "/mainInfoList", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<CommonPage<CdAnalysisVo>> list(CdAnalysisDto queryParam,
                                                       @RequestParam(value = "pageSize", defaultValue = "200") Integer pageSize,
                                                       @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum) {
//        BeanUtil.copyPropertiesIgnoreNull(getUserBaseInfo(), queryParam);
        List<CdAnalysisVo> costAnalysisInfoList = cdAnalysisService.list(queryParam, pageSize, pageNum);
        return CommonResult.success(CommonPage.restPage(costAnalysisInfoList));
    }

    @ApiOperation("查询医院分组分析统计信息")
    @RequestMapping(value = "/getCountInfo", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<List<CdAnalysisCountInfo>> getCountInfo(CdAnalysisDto queryParam) {
//        BeanUtil.copyPropertiesIgnoreNull(getUserBaseInfo(), queryParam);
        List<CdAnalysisCountInfo> cdAnalysisInfoList = cdAnalysisService.getCountInfo(queryParam);
        return CommonResult.success(cdAnalysisInfoList);
    }
}
