package com.my.som.controller.common;

import com.my.som.common.api.CommonPage;
import com.my.som.common.api.CommonResult;
import com.my.som.controller.BaseController;
import com.my.som.dto.common.BenchmarkConfigDto;
import com.my.som.service.common.BenchmarkConfigService;
import com.my.som.vo.common.BenchmarkConfigVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/benchmarkConfigController")
public class BenchmarkConfigController  extends BaseController {
    @Autowired
    private BenchmarkConfigService benchmarkConfigService;

    /**
     * 查询标杆费用
     * @param dto
     * @return
     */
    @RequestMapping("/selectDip")
    public CommonResult selectDip(BenchmarkConfigDto dto,
                                  @RequestParam(value = "pageSize", defaultValue = "200") Integer pageSize,
                                  @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum){
        dto.setHospitalId(getUserBaseInfo().getHospitalId());
        List<BenchmarkConfigVo> list=benchmarkConfigService.selectDip(dto, pageSize, pageNum);
        return CommonResult.success(CommonPage.restPage(list));
    }

    /**
     * 更新标杆数据
     * @param dto
     * @return
     */
    @RequestMapping("/update")
    public CommonResult update(BenchmarkConfigDto dto){
        dto.setHospitalId(getUserBaseInfo().getHospitalId());
        benchmarkConfigService.update(dto);
        return CommonResult.success();
    }

    /**
     * 删除标杆数据
     * @param dto
     * @return
     */
    @RequestMapping("/delete")
    public CommonResult delete(BenchmarkConfigDto dto){
        dto.setHospitalId(getUserBaseInfo().getHospitalId());
        benchmarkConfigService.delete(dto);
        return CommonResult.success();
    }
    @RequestMapping("/insertData")
    public CommonResult insertData(BenchmarkConfigDto dto){
        benchmarkConfigService.insertData(dto);
        return CommonResult.success();
    }

    /**
     * 查询倍率区间
     * @param dto
     * @return
     */
    @RequestMapping("/queryRatioRange")
    public CommonResult queryRatioRange(BenchmarkConfigDto dto){
        dto.setHospitalId(getUserInfo().getHospitalId());
        return CommonResult.success(benchmarkConfigService.queryRatioRange(dto));
    }

    /**
     * 重新生成分值
     * @param dto
     * @return
     */
    @RequestMapping("/anewScore")
    public CommonResult anewScore(BenchmarkConfigDto dto){
        benchmarkConfigService.anewScore(dto);
        return CommonResult.success();
    }

    /**
     * 重新生成分值2
     * @param dto
     * @return
     */
    @RequestMapping("/generateScore")
    public CommonResult generateScore(BenchmarkConfigDto dto){
        benchmarkConfigService.generateScore(dto);
        return CommonResult.success();
    }
}
