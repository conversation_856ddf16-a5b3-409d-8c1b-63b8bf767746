package com.my.som.controller.newBusiness.disease;

import com.my.som.common.api.CommonPage;
import com.my.som.common.api.CommonResult;
import com.my.som.dto.newBusiness.disease.NewBusinessDiseaseDto;
import com.my.som.service.newBusiness.disease.NewDipBusinessDiseaseAnalysisService;
import com.my.som.vo.newBusiness.disease.NewBusinessDiseaseVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 病组分析
 */
@RestController
@RequestMapping("/newDipBusinessDiseaseAnalysisController")
public class NewDipBusinessDiseaseAnalysisController {

    @Autowired
    private NewDipBusinessDiseaseAnalysisService newDipBusinessDiseaseAnalysisService;

    @RequestMapping("/queryDiseaseKpiData")
    public CommonResult<CommonPage<NewBusinessDiseaseVo>> queryDiseaseKpiData(NewBusinessDiseaseDto dto){
        List<NewBusinessDiseaseVo> list = newDipBusinessDiseaseAnalysisService.queryDiseaseKpiData(dto);
        return CommonResult.success(CommonPage.restPage(list));
    }

    @RequestMapping("/queryDiseaseForecastData")
    public CommonResult<CommonPage<NewBusinessDiseaseVo>> queryDiseaseForecastData(NewBusinessDiseaseDto dto){
        List<NewBusinessDiseaseVo> list = newDipBusinessDiseaseAnalysisService.queryDiseaseForecastData(dto);
        return CommonResult.success(CommonPage.restPage(list));
    }
}
