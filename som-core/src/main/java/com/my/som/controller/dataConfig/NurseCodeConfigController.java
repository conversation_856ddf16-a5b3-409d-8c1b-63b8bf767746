package com.my.som.controller.dataConfig;

import com.my.som.common.api.CommonPage;
import com.my.som.common.api.CommonResult;
import com.my.som.common.util.ExcelUtil;
import com.my.som.controller.BaseController;
import com.my.som.dto.dataConfig.DoctorCodeDto;
import com.my.som.dto.dataConfig.NurseCodeDto;
import com.my.som.service.dataConfig.NurseCodeConfigService;
import com.my.som.vo.dataConfig.NurseCodeVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * @author: zyd
 */
@RestController
@RequestMapping("/NurseCodeConfigController")
public class NurseCodeConfigController extends BaseController {
    @Autowired
    private NurseCodeConfigService nurseCodeConfigService;

    /**
     * 查询护士代码信息
     * @param dto
     * @return
     */
    @RequestMapping("/queryNurseCodeInfo")
    public CommonResult queryAreaBenchmarkInfo(NurseCodeDto dto){
        List<NurseCodeVo> list = nurseCodeConfigService.queryNurseCodeInfo(dto);
        return CommonResult.success(CommonPage.restPage(list));
    }

    /**
     * 删除护士代码信息
     * @param dto
     * @return
     */
    @RequestMapping("/deleteNurseCodeInfo")
    public CommonResult deleteNurseCodeInfo(NurseCodeDto dto){
        nurseCodeConfigService.deleteNurseCodeInfo(dto);
        return CommonResult.success();
    }

    /**
     * 修改护士代码信息
     * @param dto
     * @return
     */
    @RequestMapping("/updateNurseCodeInfo")
    public CommonResult updateNurseCodeInfo(NurseCodeDto dto){
        nurseCodeConfigService.updateNurseCodeInfo(dto);
        return CommonResult.success();
    }

    /**
     * 新增护士编码信息
     * @param dto
     * @return
     */
    @RequestMapping("/insertNurseCodeInfo")
    public CommonResult insertNurseCodeInfo(NurseCodeDto dto){
        dto.setHospitalId(getUserBaseInfo().getHospitalId());
        nurseCodeConfigService.insertNurseCodeInfo(dto);
        return CommonResult.success();
    }

    /**
     * 文件上传
     * @param file
     * @return
     */
    @RequestMapping("/nurseCodeUpload")
    public CommonResult nurseCodeUpload(@RequestParam("file") MultipartFile file, NurseCodeDto dto){
        nurseCodeConfigService.nurseCodeUpload(file,dto);
        return CommonResult.success();
    }

    /**
     * 模板下载
     * @param response
     * @return
     */
    @RequestMapping("/downNurseCodeTemplate")
    public CommonResult downNurseCodeTemplate(HttpServletResponse response){
        ExcelUtil.exportTemplateToWeb(response,"护士编码对照", "classpath:templates/nurseCodeConfigTemplate.xlsx");
        return CommonResult.success();
    }
}
