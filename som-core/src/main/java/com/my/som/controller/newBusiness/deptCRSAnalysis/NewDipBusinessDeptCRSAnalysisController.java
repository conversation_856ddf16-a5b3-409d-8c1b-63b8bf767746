package com.my.som.controller.newBusiness.deptCRSAnalysis;

import com.my.som.common.api.CommonResult;
import com.my.som.dto.newBusiness.deptCRSAnalysis.NewDipBusinessDeptCRSAnalysisDto;
import com.my.som.service.newBusiness.deptCRSAnalysis.NewDipBusinessDeptCRSAnalysisService;
import com.my.som.vo.newBusiness.deptCRSAnalysis.NewDipBusinessDeptCRSAnalysisVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @author: zyd
 * @date: 2023-07-31
 * @description 科室全面分析
 */
@RestController
@RequestMapping("/newDipBusinessDeptCRSAnalysisController")
public class NewDipBusinessDeptCRSAnalysisController {

    @Autowired
    private NewDipBusinessDeptCRSAnalysisService newDipBusinessDeptCRSAnalysisService;

    /**
     * 查询科室数据
     * @param dto
     * @return
     */
    @PostMapping("/queryDeptCRSData")
    public CommonResult queryDeptCRSData(NewDipBusinessDeptCRSAnalysisDto dto) {
        List<NewDipBusinessDeptCRSAnalysisVo> list = newDipBusinessDeptCRSAnalysisService.queryDeptCRSData(dto);
        return CommonResult.success(list);
    }
}
