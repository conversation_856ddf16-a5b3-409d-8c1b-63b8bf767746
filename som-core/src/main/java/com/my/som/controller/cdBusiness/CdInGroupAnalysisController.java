package com.my.som.controller.cdBusiness;

import com.my.som.common.api.CommonPage;
import com.my.som.common.api.CommonResult;
import com.my.som.common.util.BeanUtil;
import com.my.som.controller.BaseController;
import com.my.som.dto.cdBusiness.CdInGroupAnalysisQueryParam;
import com.my.som.service.cdBusiness.CdInGroupAnalysisService;
import com.my.som.vo.cdBusiness.CdInGroupAnalysisCountInfo;
import com.my.som.vo.cdBusiness.CdInGroupAnalysisInfo;
import com.my.som.vo.cdBusiness.CdInGroupTopCountInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;

/**
 * cd入组分析Controller
 * Created by sky on 2021/09/06.
 */
@Controller
@Api(tags = "CdInGroupAnalysisController", description = "成都入组分析")
@RequestMapping("/cdinGroupAnalysis")
public class CdInGroupAnalysisController extends BaseController {
    @Autowired
    private CdInGroupAnalysisService cdInGroupAnalysisService;

    @ApiOperation("查询成都入组分析主要信息")
    @RequestMapping(value ="/mainInfoList",method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<CommonPage<CdInGroupAnalysisInfo>> list(CdInGroupAnalysisQueryParam queryParam,
                                                                @RequestParam(value = "pageSize",defaultValue = "200")Integer pageSize,
                                                                @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum){
//        BeanUtil.copyPropertiesIgnoreNull(getUserBaseInfo(), queryParam);
        List<CdInGroupAnalysisInfo> cdInGroupAnalysisInfoList = cdInGroupAnalysisService.list(queryParam,pageSize,pageNum);
        return CommonResult.success(CommonPage.restPage(cdInGroupAnalysisInfoList));
    }
    @ApiOperation("查询全院入组情况人次统计信息")
    @RequestMapping(value = "/getTopCountInfo", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<CdInGroupTopCountInfo> getTopCountInfo(CdInGroupAnalysisQueryParam queryParam) {
//        BeanUtil.copyPropertiesIgnoreNull(getUserBaseInfo(), queryParam);
        CdInGroupTopCountInfo topCountInfo = cdInGroupAnalysisService.getTopCountInfo(queryParam);
        return CommonResult.success(topCountInfo);
    }

    @ApiOperation("查询未入组原因统计信息")
    @RequestMapping(value = "/getNoGroupResonCountInfo",method =RequestMethod.POST )
    @ResponseBody
    public CommonResult<List<CdInGroupAnalysisCountInfo>> getNoGroupResonCountInfo(CdInGroupAnalysisQueryParam queryParam) {
//        BeanUtil.copyPropertiesIgnoreNull(getUserBaseInfo(), queryParam);
        List<CdInGroupAnalysisCountInfo> cdinGroupAnalysisCountInfoList = cdInGroupAnalysisService.getNoGroupResonCountInfo(queryParam);
        return CommonResult.success(cdinGroupAnalysisCountInfoList);
    }


}
