package com.my.som.controller.dataConfig;

import com.my.som.common.api.CommonPage;
import com.my.som.common.api.CommonResult;
import com.my.som.dto.dataConfig.TpdStandardOperationDto;
import com.my.som.model.dataConfig.SomOprnLv;
import com.my.som.service.dataConfig.TpdStandardOperationService;
import com.my.som.vo.SomMenuMgt;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 标准手术配置
 * @author: zyd
 * @date: 2023-07-31
 */
@RestController
@Api(tags = "StandardOperationConfigController",description = "标准手术管理")
@RequestMapping("/standardOpr")
public class StandardOperationConfigController {

	@Autowired
	private TpdStandardOperationService tpdStandardOperationService;

    @ApiOperation("查询标准手术")
//	@PreAuthorize("hasAuthority('dataConfig:standardOperationConfig:view')")
    @RequestMapping(value = "/queryList",method = RequestMethod.POST)
    public CommonResult<CommonPage<SomOprnLv>> queryList(TpdStandardOperationDto tpdStandardOperationDto,
																	@RequestParam(value = "pageSize", defaultValue = "200") Integer pageSize,
																	@RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum) {
		List<SomOprnLv> tpdStandardOperationList = tpdStandardOperationService.list(tpdStandardOperationDto, pageSize, pageNum);
		return CommonResult.success(CommonPage.restPage(tpdStandardOperationList));
    }

//	@PreAuthorize("hasAuthority('dataConfig:standardOperationConfig:add') AND hasAuthority('dataConfig:standardOperationConfig:edit')")
	@PostMapping(value="/save")
	public CommonResult save(@RequestBody SomOprnLv somOprnLv) {
		return CommonResult.success(tpdStandardOperationService.save(somOprnLv));
	}

//	@PreAuthorize("hasAuthority('dataConfig:standardOperationConfig:delete')")
	@PostMapping(value="/delete")
	public CommonResult delete(@RequestParam("id") String id) {
		return CommonResult.success(tpdStandardOperationService.delete(id));
	}




}
