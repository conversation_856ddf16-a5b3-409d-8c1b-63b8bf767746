package com.my.som.controller.drgReasonableCost;

import com.my.som.common.api.CommonPage;
import com.my.som.common.api.CommonResult;
import com.my.som.common.util.BeanUtil;
import com.my.som.controller.BaseController;
import com.my.som.dto.drgReasonableCost.DrgReasonableCostQueryParam;
import com.my.som.service.drgReasonableCost.BedDayCostService;
import com.my.som.vo.drgReasonableCost.BedCostDayVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;

/**
 * 床日付费病组Controller
 * Created by sky on 2020/10/22.
 */
@Controller
@Api(tags = "BedDayCostController", description = "床日付费病组")
@RequestMapping("/bedDayCost")
public class BedDayCostController extends BaseController {
    @Autowired
    private BedDayCostService bedDayCostService;

    @ApiOperation("查询床日付费病组主要信息")
    @RequestMapping(value = "/mainInfoList", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<CommonPage<BedCostDayVo>> list(DrgReasonableCostQueryParam queryParam,
                                                       @RequestParam(value = "pageSize", defaultValue = "200") Integer pageSize,
                                                       @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum) {
        BeanUtil.copyPropertiesIgnoreNull(getUserBaseInfo(), queryParam);
        List<BedCostDayVo> bedCostDayVoList = bedDayCostService.list(queryParam, pageSize, pageNum);
        return CommonResult.success(CommonPage.restPage(bedCostDayVoList));
    }


}
