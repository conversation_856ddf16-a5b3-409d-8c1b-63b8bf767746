package com.my.som.controller.hospitalAnalysis;

import com.my.som.common.api.CommonPage;
import com.my.som.common.api.CommonResult;
import com.my.som.dto.hospitalAnalysis.DrgsDeptCostEarlyWarningDto;
import com.my.som.service.hospitalAnalysis.DrgsDeptCostEarlyWarningService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @author: zyd
 * @Date 2021/09/09 18:14 上午
 * @Version 1.0
 * @Description: drg费用预警
 */
@RestController
@RequestMapping("/drgDeptCostEarlyWarningController")
public class DrgsDeptCostEarlyWarningController {

    @Autowired
    private DrgsDeptCostEarlyWarningService drgsDeptCostEarlyWarningService;
    /**
     * 获取数据
     * @param dto 参数
     * @return
     */
    @RequestMapping("/getList")
    public CommonResult getList(DrgsDeptCostEarlyWarningDto dto){
        return CommonResult.success(drgsDeptCostEarlyWarningService.getList(dto));
    }

}
