package com.my.som.controller.dataConfig;


import com.github.pagehelper.PageHelper;
import com.my.som.common.api.CommonPage;
import com.my.som.common.api.CommonResult;
import com.my.som.common.util.ExcelUtil;
import com.my.som.common.vo.SysUserBase;
import com.my.som.controller.BaseController;
import com.my.som.dto.dataConfig.DoctorCodeDto;
import com.my.som.dto.dataConfig.RegulatoryParametersConfigDto;
import com.my.som.service.dataConfig.RegulatoryParametersConfigService;
import com.my.som.vo.dataConfig.RegulatoryParametersConfigVo;
import com.my.som.vo.newBusiness.NewBusinessCommonVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

@RestController
@RequestMapping("/RegulatoryParametersConfigController")
public class RegulatoryParametersConfigController extends BaseController {

    @Autowired
    private RegulatoryParametersConfigService regulatoryParametersConfigService;


    /**
     * 查询病组参数配置数据
     * @param dto
     * @return
     */
    @RequestMapping("/selectDiseaseGroupData")
    public CommonResult<CommonPage<?>> selectDiseaseGroupData(RegulatoryParametersConfigDto dto) {
        List<RegulatoryParametersConfigVo> list = regulatoryParametersConfigService.selectDiseaseGroupData(dto);
        return CommonResult.success(CommonPage.restPage(list));
    }

    /**
     * 通过ID删除数据
     * @param dto
     * @return
     */
    @RequestMapping("/deleteDiseaseGroup")
    public CommonResult deleteDiseaseGroup(RegulatoryParametersConfigDto dto){
        regulatoryParametersConfigService.deleteDiseaseGroup(dto);
        return CommonResult.success();
    }


    /**
     * 添加病组数据
     * @param dto
     * @return
     */
    @RequestMapping("/insertDiseaseGroup")
    public CommonResult insertDiseaseGroup(RegulatoryParametersConfigDto dto){
        dto.setModifier(getUserBaseInfo().getNknm());
        regulatoryParametersConfigService.insertDiseaseGroup(dto);
        return CommonResult.success();
    }

    /**
     * 修改数据Byid
     * @param dto
     * @return
     */
    @RequestMapping("/updateDiseaseGroup")
    public CommonResult updateDiseaseGroup(RegulatoryParametersConfigDto dto){
        dto.setModifier(getUserBaseInfo().getNknm());
        regulatoryParametersConfigService.updateDiseaseGroup(dto);
        return CommonResult.success();
    }

    /**
     * 查询诊断参数配置数据
     * @param dto
     * @return
     */
    @RequestMapping("/selectDiagnosisData")
    public CommonResult<CommonPage<?>> selectDiagnosisData(RegulatoryParametersConfigDto dto) {
        List<RegulatoryParametersConfigVo> list = regulatoryParametersConfigService.selectDiagnosisData(dto);
        return CommonResult.success(CommonPage.restPage(list));
    }

    /**
     * 添加诊断数据
     * @param dto
     * @return
     */
    @RequestMapping("/insertDiagnosis")
    public CommonResult insertDiagnosis(RegulatoryParametersConfigDto dto){
        dto.setModifier(getUserBaseInfo().getNknm());
        regulatoryParametersConfigService.insertDiagnosis(dto);
        return CommonResult.success();
    }

    /**
     * 查询手术参数配置数据
     * @param dto
     * @return
     */
    @RequestMapping("/selectOperationData")
    public CommonResult<CommonPage<?>> selectOperationData(RegulatoryParametersConfigDto dto) {
        List<RegulatoryParametersConfigVo> list = regulatoryParametersConfigService.selectOperationData(dto);
        return CommonResult.success(CommonPage.restPage(list));
    }

    /**
     * 添加手术数据
     * @param dto
     * @return
     */
    @RequestMapping("/insertOperation")
    public CommonResult insertOperation(RegulatoryParametersConfigDto dto){
        dto.setModifier(getUserBaseInfo().getNknm());
        regulatoryParametersConfigService.insertOperation(dto);
        return CommonResult.success();
    }
    /**
     * 查询标准病组数据
     * @param dto
     * @return
     */
    @RequestMapping("/selectDiseaseGroup")
    public CommonResult<List<RegulatoryParametersConfigVo>> selectDiseaseGroup(RegulatoryParametersConfigDto dto){
        List<RegulatoryParametersConfigVo> list = regulatoryParametersConfigService.selectDiseaseGroup(dto);
        return CommonResult.success(list);
    }
    /**
     * 查询标准诊断数据
     * @param dto
     * @return
     */
    @RequestMapping("/selectDiagnosis")
    public CommonResult<List<RegulatoryParametersConfigVo>> selectDiagnosis(RegulatoryParametersConfigDto dto){
        List<RegulatoryParametersConfigVo> list = regulatoryParametersConfigService.selectDiagnosis(dto);
        return CommonResult.success(list);
    }
    /**
     * 查询标准手术数据
     * @param dto
     * @return
     */
    @RequestMapping("/selectOperation")
    public CommonResult<List<RegulatoryParametersConfigVo>> selectOperation(RegulatoryParametersConfigDto dto){
        List<RegulatoryParametersConfigVo> list = regulatoryParametersConfigService.selectOperation(dto);
        return CommonResult.success(list);
    }

    /**
     * 病组参数模板下载
     * @param response
     * @return
     */
    @RequestMapping("/downPatientTemplate")
    public CommonResult downPatientTemplate(HttpServletResponse response){
        ExcelUtil.exportTemplateToWeb(response,"病组参数模板下载", "classpath:templates/patientConfigTemplate.xlsx");
        return CommonResult.success();
    }
    /**
     * 诊断参数模板下载
     * @param response
     * @return
     */
    @RequestMapping("/downDiagnosticTemplate")
    public CommonResult downDiagnosticTemplate(HttpServletResponse response){
        ExcelUtil.exportTemplateToWeb(response,"诊断参数模板下载", "classpath:templates/diagnosticConfigTemplate.xlsx");
        return CommonResult.success();
    }
    /**
     * 手术参数模板下载
     * @param response
     * @return
     */
    @RequestMapping("/downOperationTemplate")
    public CommonResult downOperationTemplate(HttpServletResponse response){
        ExcelUtil.exportTemplateToWeb(response,"手术参数模板下载", "classpath:templates/operationConfigTemplate.xlsx");
        return CommonResult.success();
    }

    /**
     * 病组参数模板上传
     * @param file
     * @return
     */
    @RequestMapping("/patientTemplateUpload")
    public CommonResult patientTemplateUpload(@RequestParam("file") MultipartFile file,RegulatoryParametersConfigDto dto){
        dto.setModifier(getUserBaseInfo().getNknm());
        regulatoryParametersConfigService.patientTemplateUpload(file,dto);
        return CommonResult.success();
    }

    /**
     * 诊断参数模板上传
     * @param file
     * @return
     */
    @RequestMapping("/diagnosticTemplateUpload")
    public CommonResult diagnosticTemplateUpload(@RequestParam("file") MultipartFile file,RegulatoryParametersConfigDto dto){
        dto.setModifier(getUserBaseInfo().getNknm());
        regulatoryParametersConfigService.diagnosticTemplateUpload(file,dto);
        return CommonResult.success();
    }
    /**
     * 手术参数模板上传
     * @param file
     * @return
     */
    @RequestMapping("/operationTemplateUpload")
    public CommonResult operationTemplateUpload(@RequestParam("file") MultipartFile file,RegulatoryParametersConfigDto dto){
        dto.setModifier(getUserBaseInfo().getNknm());
        regulatoryParametersConfigService.operationTemplateUpload(file, dto);
        return CommonResult.success();
    }

}
