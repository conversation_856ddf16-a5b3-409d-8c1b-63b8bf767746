package com.my.som.controller.itft;

import com.my.som.common.api.CommonPage;
import com.my.som.common.api.CommonResult;
import com.my.som.controller.BaseController;
import com.my.som.dto.itft.ITFTDictDto;
import com.my.som.service.itft.ITFTDictService;
import com.my.som.vo.SomSysCode;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @author: zyd
 */
@RequestMapping("/itftDictController")
@RestController
public class ITFTDictController extends BaseController {
    @Autowired
    private ITFTDictService itftDictService;

    /**
     * 查询dict数据
     * @param dto
     * @return
     */
    @RequestMapping("/queryData")
    public CommonResult<?> queryData(@RequestBody ITFTDictDto dto){
        List<SomSysCode> list = itftDictService.queryData(dto);
        if ("1".equals(dto.getIsPage())) {
            return CommonResult.success(CommonPage.restPage(list));
        }
        return CommonResult.success(list);
    }

    /**
     * 修改dict数据
     * @param dto
     * @return
     */
    @RequestMapping("/updateDict")
    public CommonResult<?> updateDict(@RequestBody ITFTDictDto dto){
        itftDictService.updateDict(dto);
        return CommonResult.success();
    }

    /**
     * 删除dict数据
     * @param dtos
     * @return
     */
    @RequestMapping("/deleteDict")
    public CommonResult<?> deleteDict(@RequestBody List<ITFTDictDto> dtos){
        itftDictService.deleteDict(dtos);
        return CommonResult.success();
    }

    /**
     * 保存dict数据
     * @param dto
     * @return
     */
    @RequestMapping("/saveDict")
    public CommonResult<?> saveDict(@RequestBody ITFTDictDto dto){
        String userId = String.valueOf(getUserInfo().getId());
        dto.setCrter(userId);
        dto.setUpdtPsn(userId);
        itftDictService.saveDict(dto);
        return CommonResult.success();
    }
}
