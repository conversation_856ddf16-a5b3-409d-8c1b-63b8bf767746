package com.my.som.controller.dataConfig;

import com.my.som.common.api.CommonResult;
import com.my.som.dto.dataConfig.HospitalCoefficientDto;
import com.my.som.service.dataConfig.HospitalCoefficientService;
import com.my.som.vo.dataConfig.HospCofVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


@RestController
@RequestMapping("/HospCof")
public class HospitalCoefficientController {

    @Autowired
    private HospitalCoefficientService hospitalCoefficientService;

    @RequestMapping("/queryHC")
    public CommonResult queryHC(HospitalCoefficientDto hospitalCoefficientDto){
        List<HospCofVo> list = hospitalCoefficientService.queryHC(hospitalCoefficientDto);
        return CommonResult.success(list);
    }

    @RequestMapping("/insertHC")
    public CommonResult insertHC(HospitalCoefficientDto hospitalCoefficientDto){
        hospitalCoefficientService.insertHC(hospitalCoefficientDto);
        return CommonResult.success();
    }

    @RequestMapping("/updateHC")
    public CommonResult updateHC(HospitalCoefficientDto hospitalCoefficientDto){
        hospitalCoefficientService.updateHC(hospitalCoefficientDto);
        return CommonResult.success();
    }

    @RequestMapping("/deleteHC")
    public CommonResult deleteHC(HospitalCoefficientDto hospitalCoefficientDto){
        hospitalCoefficientService.deleteHC(hospitalCoefficientDto);
        return CommonResult.success();
    }
}
