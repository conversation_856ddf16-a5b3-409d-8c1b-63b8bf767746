package com.my.som.controller.hospitalAnalysis;

import com.my.som.common.api.CommonPage;
import com.my.som.common.api.CommonResult;
import com.my.som.common.util.BeanUtil;
import com.my.som.controller.BaseController;
import com.my.som.dto.hospitalAnalysis.HospitalAnalysisQueryParam;
import com.my.som.service.hospitalAnalysis.CostAnalysisService;
import com.my.som.vo.common.CommonObject;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;
import java.util.Map;

/**
 * 医院费用分析Controller
 * Created by sky on 2020/3/17.
 */
@Controller
@Api(tags = "CostAnalysisController", description = "医院费用分析")
@RequestMapping("/costAnalysis")
public class CostAnalysisController extends BaseController {
    @Autowired
    private CostAnalysisService costAnalysisService;

    @ApiOperation("查询医院费用分析主要信息")
    @RequestMapping(value = "/mainInfoList", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<CommonPage<Map<String,Object>>> list(HospitalAnalysisQueryParam queryParam,
                                                                @RequestParam(value = "pageSize", defaultValue = "200") Integer pageSize,
                                                                @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum) {
        BeanUtil.copyPropertiesIgnoreNull(getUserBaseInfo(), queryParam);
        List<Map<String,Object>> costAnalysisInfoList = costAnalysisService.list(queryParam, pageSize, pageNum);
        return CommonResult.success(CommonPage.restPage(costAnalysisInfoList));
    }

    @ApiOperation("查询医院费用分析统计信息")
    @RequestMapping(value = "/getCountInfo", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<List<CommonObject>> getCountInfo(HospitalAnalysisQueryParam queryParam) {
        BeanUtil.copyPropertiesIgnoreNull(getUserBaseInfo(), queryParam);
        List<CommonObject> commonObjectList = costAnalysisService.getCountInfo(queryParam);
        return CommonResult.success(commonObjectList);
    }

}
