package com.my.som.controller.sys;

import com.my.som.common.api.CommonResult;
import com.my.som.dto.dataConfig.HospitalInfoDto;
import com.my.som.dto.sys.ProcessJudgeDto;
import com.my.som.service.dataConfig.ViewHospitalService;
import com.my.som.service.sys.ProcessJudgeService;
import com.my.som.vo.dataConfig.ViewHospitalVo;
import com.my.som.vo.sys.ProcessJudgeVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/ProcessJudgeController")
public class ProcessJudgeController {
    @Autowired
    private ProcessJudgeService processJudgeService;

    /**
     * 查询任务流程
     * @param dto
     * @return
     */
    @RequestMapping("/queryProcessJudge")
    public CommonResult queryHospitalInfo(ProcessJudgeDto dto){
        List<ProcessJudgeVo> list = processJudgeService.queryProcessJudge(dto);
        return CommonResult.success(list);
    }

    /**
     * 流程测试
     * @param dto
     * @return
     */
    @RequestMapping("/processTest")
    public CommonResult processTest(ProcessJudgeDto dto) throws Exception {
        processJudgeService.processTest(dto);
        return CommonResult.success();
    }

    /**
     * 修改启动标志
     * @param dto
     * @return
     */
    @RequestMapping("/updateStart")
    public CommonResult updateStart(ProcessJudgeDto dto){
        processJudgeService.updateStart(dto);
        return CommonResult.success();
    }
}
