package com.my.som.controller.sys;

import com.my.som.common.api.CommonPage;
import com.my.som.common.api.CommonResult;
import com.my.som.common.page.PageRequest;
import com.my.som.common.vo.CodeConst;
import com.my.som.dao.sys.SomRoleMgtMapper;
import com.my.som.service.sys.SysRoleService;
import com.my.som.vo.SomRoleMgt;
import com.my.som.vo.SomRoleMenu;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 角色控制器
 * @author: zyd
 * @date: 2023-07-31
 */
@RestController
@Api(tags = "SysRoleController",description = "角色管理")
@RequestMapping("/role")
public class SysRoleController {

	@Autowired
	private SysRoleService sysRoleService;
	@Autowired
	private SomRoleMgtMapper sysRoleMapper;

	//@PreAuthorize("hasAuthority('sys:role:add') AND hasAuthority('sys:role:edit')")
	@PostMapping(value="/save")
	public CommonResult save(@RequestBody SomRoleMgt record) {
		SomRoleMgt role = sysRoleService.findById(record.getId());
		if(role != null) {
			if(CodeConst.ADMIN.equalsIgnoreCase(role.getName())) {
				return CommonResult.failed("超级管理员不允许修改!");
			}
		}
		// 新增角色
		if((record.getId() == null || record.getId() ==0) && !sysRoleService.findByName(record.getName()).isEmpty()) {
			return CommonResult.failed("角色名已存在!");
		}
		return CommonResult.success(sysRoleService.save(record));
	}

	//@PreAuthorize("hasAuthority('sys:role:delete')")
	@PostMapping(value="/delete")
	public CommonResult delete(@RequestBody List<SomRoleMgt> records) {
		return CommonResult.success(sysRoleService.delete(records));
	}

	//@PreAuthorize("hasAuthority('sys:role:view')")
	@PostMapping(value="/findPage")
	public CommonResult<CommonPage<SomRoleMgt>> findPage(@RequestBody PageRequest pageRequest) {
		return CommonResult.success(sysRoleService.findPage(pageRequest));
	}

	//@PreAuthorize("hasAuthority('sys:role:view')")
	@GetMapping(value="/findAll")
	public CommonResult<List<SomRoleMgt>> findAll() {
		return CommonResult.success(sysRoleService.findAll());
	}

	//@PreAuthorize("hasAuthority('sys:role:view')")
	@GetMapping(value="/findRoleMenus")
	public CommonResult findRoleMenus(@RequestParam Long roleId) {
		return CommonResult.success(sysRoleService.findRoleMenus(roleId));
	}

	//@PreAuthorize("hasAuthority('sys:role:view')")
	@PostMapping(value="/saveRoleMenus")
	public CommonResult saveRoleMenus(@RequestBody List<SomRoleMenu> records) {
		for(SomRoleMenu record:records) {
			SomRoleMgt somRoleMgt = sysRoleMapper.selectByPrimaryKey(record.getRoleId());
			if(CodeConst.ADMIN.equalsIgnoreCase(somRoleMgt.getName())) {
				// 如果是超级管理员，不允许修改
				return CommonResult.failed("超级管理员拥有所有菜单权限，不允许修改！");
			}
		}
		return CommonResult.success(sysRoleService.saveRoleMenus(records));
	}
}
