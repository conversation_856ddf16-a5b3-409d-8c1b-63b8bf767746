package com.my.som.controller.newBusiness.dept;

import com.my.som.common.api.CommonPage;
import com.my.som.common.api.CommonResult;
import com.my.som.dto.newBusiness.NewBusinessCommonDto;
import com.my.som.dto.newBusiness.dept.NewBusinessDeptDto;
import com.my.som.service.newBusiness.dept.NewDrgBusinessDeptAnalysisService;
import com.my.som.vo.newBusiness.dept.NewBusinessDeptVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @author: zyd
 * @version 1.0
 * @date: 2023-07-31
 * @description:
 */
@RestController
@RequestMapping("/newDrgBusinessDeptAnalysisController")
public class NewDrgBusinessDeptAnalysisController {

    @Autowired
    private NewDrgBusinessDeptAnalysisService newDrgBusinessDeptAnalysisService;

    /**
     * 查询科室Kpi
     * @return
     */
    @RequestMapping("queryKpiData")
    public CommonResult<CommonPage<NewBusinessDeptVo>> queryKpiData(NewBusinessDeptDto dto){
        List<NewBusinessDeptVo> list = newDrgBusinessDeptAnalysisService.queryKpiData(dto);
        return CommonResult.success(CommonPage.restPage(list));
    }

    /**
     * 查询科室预测
     * @return
     */
    @RequestMapping("queryForecastData")
    public CommonResult<CommonPage<NewBusinessDeptVo>> queryForecastData(NewBusinessDeptDto dto){
        List<NewBusinessDeptVo> list = newDrgBusinessDeptAnalysisService.queryForecastData(dto);
        return CommonResult.success(CommonPage.restPage(list));
    }

    /**
     * 查询病例总数以及入组和未入组总数
     */
    @RequestMapping("queryCountData")
    public CommonResult<CommonPage<NewBusinessDeptVo>> queryCountData(NewBusinessDeptDto dto){
        List<NewBusinessDeptVo> list = newDrgBusinessDeptAnalysisService.queryCountData(dto);
        return CommonResult.success(CommonPage.restPage(list));
    }

    /**
     * 查询病例总数以及入组和未入组总数
     */
    @RequestMapping("queryYCCountData")
    public CommonResult<CommonPage<NewBusinessDeptVo>> queryYCCountData(NewBusinessDeptDto dto){
        List<NewBusinessDeptVo> list = newDrgBusinessDeptAnalysisService.queryYCCountData(dto);
        return CommonResult.success(CommonPage.restPage(list));
    }

    /**
     * 查询病例总数以及入组和未入组总数
     */
    @RequestMapping("selectDrgSickGroupQuadrant")
    public CommonResult<List<NewBusinessDeptVo>> selectDrgSickGroupQuadrant(NewBusinessCommonDto dto){
        List<NewBusinessDeptVo> list = newDrgBusinessDeptAnalysisService.selectDrgSickGroupQuadrant(dto);
        return CommonResult.success(list);
    }


}
