package com.my.som.controller.newBusiness.DiseType;

import com.my.som.common.api.CommonPage;
import com.my.som.common.api.CommonResult;
import com.my.som.dto.dipBusiness.HospitalAnalysisDto;
import com.my.som.dto.newBusiness.NewBusinessCommonDto;
import com.my.som.dto.newBusiness.disease.NewBusinessDiseaseDto;
import com.my.som.service.newBusiness.DiseType.NewDipBusinessDiseaseAnalysisTypeService;
import com.my.som.vo.dipBusiness.DipDiseaseAnalysisInfo;
import com.my.som.vo.newBusiness.NewBusinessAnalysisVo;
import com.my.som.vo.newBusiness.disease.NewBusinessDiseaseVo;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 *  病种分析
 * */
@RestController
@RequestMapping("/NewDipBusinessDiseaseAnalysisTypeController")
public class NewDipBusinessDiseaseAnalysisTypeController {

    @Autowired
    private NewDipBusinessDiseaseAnalysisTypeService service;

    @ApiOperation("查询医院病种指标信息")
    @RequestMapping(value = "/mainInfoList", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<CommonPage<NewBusinessDiseaseVo>> list(NewBusinessDiseaseDto dto) {
//        BeanUtil.copyPropertiesIgnoreNull(getUserBaseInfo(), queryParam);
        List<NewBusinessDiseaseVo> vos = service.list(dto);
        return CommonResult.success(CommonPage.restPage(vos));
    }

    @ApiOperation("查询病种预测信息")
    @RequestMapping(value = "/queryDiseaseForecastData", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<CommonPage<NewBusinessDiseaseVo>> queryDiseaseForecastData(NewBusinessDiseaseDto dto){
        List<NewBusinessDiseaseVo> list = service.queryDiseaseForecastData(dto);
        return CommonResult.success(CommonPage.restPage(list));
    }


    /**
     * 查询汇总页面表格数据
     * @return
     */
    @RequestMapping("queryAnalysisSummary")
    public CommonResult<List<NewBusinessAnalysisVo>> queryAnalysisSummary(NewBusinessCommonDto dto){
        return CommonResult.success(service.queryAnalysisSummary(dto));
    }


}
