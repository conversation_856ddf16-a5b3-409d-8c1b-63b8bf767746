package com.my.som.controller.cdBusiness;

import com.my.som.common.api.CommonPage;
import com.my.som.common.api.CommonResult;
import com.my.som.common.util.BeanUtil;
import com.my.som.controller.BaseController;
import com.my.som.dto.cdBusiness.CdAnalysisDto;
import com.my.som.dto.hospitalAnalysis.HospitalAnalysisQueryParam;
import com.my.som.service.cdBusiness.CdDoctorAnalysisService;
import com.my.som.service.hospitalAnalysis.DoctorAnalysisService;
import com.my.som.vo.cdBusiness.CdDoctorAnalysisCountInfo;
import com.my.som.vo.cdBusiness.CdDoctorAnalysisInfo;
import com.my.som.vo.hospitalAnalysis.DoctorAnalysisCountInfo;
import com.my.som.vo.hospitalAnalysis.DoctorAnalysisInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;

/**
 * CD医院医生分析Controller
 * Created by sky on 2022/3/09.
 */
@Controller
@Api(tags = "CdDoctorAnalysisController", description = "医院医生分析")
@RequestMapping("/cdDoctorAnalysis")
public class CdDoctorAnalysisController extends BaseController {
    @Autowired
    private CdDoctorAnalysisService cdDoctorAnalysisService;
    @ApiOperation("查询医院费用分析主要信息")
    @RequestMapping(value = "/mainInfoList", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<CommonPage<CdDoctorAnalysisInfo>> list(CdAnalysisDto queryParam,
                                                             @RequestParam(value = "pageSize", defaultValue = "200") Integer pageSize,
                                                             @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum) {
//        BeanUtil.copyPropertiesIgnoreNull(getUserBaseInfo(), queryParam);
        List<CdDoctorAnalysisInfo> cdDoctorAnalysisInfos = cdDoctorAnalysisService.list(queryParam, pageSize, pageNum);
        return CommonResult.success(CommonPage.restPage(cdDoctorAnalysisInfos));
    }

    @ApiOperation("查询医院费用分析统计信息")
    @RequestMapping(value = "/getCountInfo", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<List<CdDoctorAnalysisCountInfo>> getCountInfo(CdAnalysisDto queryParam) {
//        BeanUtil.copyPropertiesIgnoreNull(getUserBaseInfo(), queryParam);
        List<CdDoctorAnalysisCountInfo> doctorAnalysisCountInfoList = cdDoctorAnalysisService.getCountInfo(queryParam);
        return CommonResult.success(doctorAnalysisCountInfoList);
    }

}
