package com.my.som.controller.common;


import com.my.som.common.util.ExcelUtil;
import com.my.som.dto.common.CommonExportExcelDto;
import com.my.som.util.ExportExcelUtil;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;


import javax.servlet.http.HttpServletResponse;


/**
 * @author: zyd
 * @version 1.0
 * @date: 2023-07-31
 * @description:
 */
@Controller
@RequestMapping("/commonExportExcelController")
public class CommonExportExcelController {

    /**
     * 导出excel
     * @param dto
     */
    @RequestMapping(value = "/export")
    public void export(@RequestBody CommonExportExcelDto dto, HttpServletResponse response){
//        ExportExcelUtil.convertToData(dto.getData(),dto.getColumns());
        ExportExcelUtil.convertToData(dto.getData() ,dto.getColumns());
        ExcelUtil.export(response, dto.getFileName(), dto.getColumns(), dto.getData());
    }
}
