package com.my.som.controller.common;

import com.my.som.common.api.CommonDictResult;
import com.my.som.common.api.CommonPage;
import com.my.som.common.api.CommonResult;
import com.my.som.controller.BaseController;
import com.my.som.dto.common.DipConfigDto;
import com.my.som.service.common.DipConfigService;
import com.my.som.vo.common.DipConfigVo;


import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping("/dipConfigController")
public class DipConfigController extends BaseController {

    @Resource
    private DipConfigService dipConfigService;

    /**
     * 查询dip配置数据
     *
     * @return
     */
    @RequestMapping("/queryData")
    public CommonResult queryData(DipConfigDto dto) {
        List<?> list = dipConfigService.queryData(dto);
        return CommonResult.success(CommonPage.restPage(list));
    }

    /**
     * 修改dip配置数据
     *
     * @param dto
     */
    @RequestMapping("/updateDip")
    public CommonDictResult updateDip(@RequestBody DipConfigDto dto) {
        dipConfigService.updateDip(dto);
        return CommonDictResult.success();
    }

    /**
     * 新增dip配置数据
     *
     * @param dto
     */
    @RequestMapping("/addDipConfig")
    public CommonDictResult addDipConfig(DipConfigDto dto) {
        dipConfigService.addDipConfig(dto);
        return CommonDictResult.success();
    }

    /**
     * 修改dip配置数据
     *
     * @param dto
     */
    @RequestMapping("/updateDipConfigBatch")
    public CommonDictResult updateDipConfigBatch(@RequestBody DipConfigDto dto) {
        dipConfigService.updateDipConfigBatch(dto);
        return CommonDictResult.success();
    }


}
