package com.my.som.controller.sys;

import com.my.som.common.api.CommonPage;
import com.my.som.common.api.CommonResult;
import com.my.som.common.constant.DrgConst;
import com.my.som.common.util.RedisUtils;
import com.my.som.common.util.ValidateUtil;
import com.my.som.common.vo.SomBackUser;
import com.my.som.controller.BaseController;
import com.my.som.dto.SysUserLoginParam;
import com.my.som.dto.SysUserParam;
import com.my.som.dto.UpdateAdminPasswordParam;
import com.my.som.dto.orgAndUserManagement.ModifyInfoDto;
import com.my.som.dto.orgAndUserManagement.QueryUserByOrgManagementParam;
import com.my.som.dto.orgAndUserManagement.ResetPasswordParam;
import com.my.som.model.SomBackUserRole;
import com.my.som.service.sys.SysUserService;
import com.my.som.vo.SomMenuMgt;
import com.my.som.vo.SysOutOfDateVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.security.Principal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 后台用户管理
 * Created by macro on 2018/4/26.
 */
@RestController
@Api(tags = "SysUserController", description = "后台用户管理")
@RequestMapping("/user")
public class SysUserController extends BaseController {
    @Autowired
    private SysUserService userService;
    @Value("${jwt.tokenHeader}")
    private String tokenHeader;
    @Value("${jwt.tokenHead}")
    private String tokenHead;

    /**
     * 权限名称
     */
    private final String AUTH_HOSPITAL = "hospital";
    private final String AUTH_DEPT = "dept";
    private final String AUTH_DOCTOR = "doctor";


    @ApiOperation(value = "用户注册")
    @RequestMapping(value = "/register", method = RequestMethod.POST)
    public CommonResult<SomBackUser> register(@RequestBody @Valid SysUserParam sysUserParam) {
        SomBackUser currentLoginSysUser = getUserInfo();
        sysUserParam.setExpireTime(currentLoginSysUser.getExpireTime());
        SomBackUser somBackUser = userService.register(sysUserParam);
        if (somBackUser == null) {
            CommonResult.failed();
        }
        return CommonResult.success(somBackUser);
    }

    @ApiOperation(value = "登录以后返回token")
    @RequestMapping(value = "/login", method = RequestMethod.POST)
    public CommonResult login(@RequestBody SysUserLoginParam sysUserLoginParam, HttpServletRequest request) {
        String token = userService.login(sysUserLoginParam.getUsername(), sysUserLoginParam.getPassword(), sysUserLoginParam.getToken());
        if (token == null) {
            return CommonResult.failed("用户名或密码错误");
        }
        if (token == "illegalOperation") {
            return CommonResult.failed("存在非法操作，系统已无法使用! ");
        }
        if (token == "errorDevPassword") {
            return CommonResult.failed("系统管理员密码被篡改，系统暂时无法使用! ");
        }
        if (token == "errorSysTime") {
            return CommonResult.failed("系统时间被篡改，系统暂时无法使用，请恢复正常时间后再继续访问! ");
        }
        if (token == "errorSysData") {
            return CommonResult.failed("试图篡改系统数据，系统暂时无法使用，请恢复正常数据后再继续访问! ");
        }
        if (token == "errorExpiredTime") {
            return CommonResult.failed("试用时间被篡改，系统暂时无法使用！");
        }
        if (token == "expired") {
            return CommonResult.failed("账户试用期已过！");
        }
        if (token == "accessTokenFail") {
            return CommonResult.failed("外部访问token认证失败！");
        }
        if (token == "authorCodeInvalid") {
            return CommonResult.failed("授权码认证失败！");
        }

        Map<String, Object> tokenMap = new HashMap<>();
        tokenMap.put("token", token);
        tokenMap.put("tokenHead", tokenHead);
        tokenMap.put("userInfo", getUserBaseInfo());
        if (!DrgConst.DEV_NAME.equals(sysUserLoginParam.getUsername())) {
            tokenMap.put("auth", getAuth());
        }
        RedisUtils.set(DrgConst.USER_INFO_KEY, getUserBaseInfo());
        return CommonResult.success(tokenMap);
    }

    @ApiOperation(value = "刷新token")
    @RequestMapping(value = "/refreshToken", method = RequestMethod.GET)
    public CommonResult refreshToken(HttpServletRequest request) {
        String token = request.getHeader(tokenHeader);
        String refreshToken = userService.refreshToken(token);
        if (refreshToken == null) {
            return CommonResult.failed("token已经过期！");
        }
        Map<String, String> tokenMap = new HashMap<>();
        tokenMap.put("token", refreshToken);
        tokenMap.put("tokenHead", tokenHead);
        return CommonResult.success(tokenMap);
    }

    @ApiOperation(value = "获取当前登录用户信息")
    @RequestMapping(value = "/info", method = RequestMethod.GET)
    public CommonResult getAdminInfo(Principal principal) {
        String username = principal.getName();
        SomBackUser somBackUser = userService.getAdminByUsername(username);
        Map<String, Object> data = new HashMap<>();
        data.put("id", somBackUser.getId());
        data.put("username", somBackUser.getUsername());
        data.put("nknm", somBackUser.getNknm());
        data.put("roles", new String[]{"TEST"});
        if (!DrgConst.DEV_NAME.equals(username)) {
            data.put("auth", getAuth());
        }
        data.put("icon", somBackUser.getIcon());
        data.put("deptCode", somBackUser.getBlngOrgOrgId());
        data.put("feeStas", somBackUser.getFeeStas());
        data.put("enableSeAns", somBackUser.getEnableSeAns());
        data.put("hospitalName", somBackUser.getHospitalName());
        if (ValidateUtil.isNotEmpty(somBackUser.getMessage())) {
            String str1 = somBackUser.getMessage();
            String[] messages = str1.split("/");
            List<String> message = Arrays.stream(messages).distinct().collect(Collectors.toList());
            data.put("message", message);
        }
        return CommonResult.success(data);
    }

    @ApiOperation(value = "登出功能")
    @RequestMapping(value = "/logout", method = RequestMethod.POST)
    public CommonResult logout() {
        // 删除用户信息
        RedisUtils.delete(getUserBaseInfo().getUsername());
        return CommonResult.success(null);
    }

    @ApiOperation("根据用户名或姓名分页获取用户列表")
    @RequestMapping(value = "/list", method = RequestMethod.POST)
    public CommonResult<CommonPage<SomBackUser>> list(@RequestBody @Valid QueryUserByOrgManagementParam queryParam) {

        queryParam.setHospitalId(getUserBaseInfo().getHospitalId());

        List<SomBackUser> adminList = userService.list(queryParam);
        return CommonResult.success(CommonPage.restPage(adminList));
    }

    @ApiOperation("获取指定用户信息")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    public CommonResult<SomBackUser> getItem(@PathVariable Long id) {
        SomBackUser admin = userService.getItem(id);
        return CommonResult.success(admin);
    }

    @ApiOperation("修改指定用户信息")
    @RequestMapping(value = "/update/{id}", method = RequestMethod.POST)
    public CommonResult update(@PathVariable Long id, @RequestBody SomBackUser somBackUser) {
        int count = userService.update(id, somBackUser);
        if (count > 0) {
            return CommonResult.success(count);
        }
        return CommonResult.failed();
    }

    @ApiOperation("重置指定用户密码")
    @RequestMapping(value = "/resetPassword/{id}", method = RequestMethod.POST)
    public CommonResult resetPassword(@PathVariable Long id, @RequestBody @Valid ResetPasswordParam resetPasswordParam) {
        int status = userService.resetPassword(resetPasswordParam);
        if (status > 0) {
            return CommonResult.success(status);
        } else {
            return CommonResult.failed();
        }
    }

    @ApiOperation("修改个人信息")
    @RequestMapping(value = "/modifyInformation")
    public CommonResult modifyInformation(ModifyInfoDto dto) {
        userService.modifyInformation(dto);
        return CommonResult.success();
    }


    @ApiOperation("修改指定用户密码")
    @RequestMapping(value = "/updatePassword", method = RequestMethod.POST)
    public CommonResult updatePassword(UpdateAdminPasswordParam updatePasswordParam) {
        int status = userService.updatePassword(updatePasswordParam);
        if (status > 0) {
            return CommonResult.success(status);
        } else if (status == -1) {
            return CommonResult.failed("提交参数不合法");
        } else if (status == -2) {
            return CommonResult.failed("找不到该用户");
        } else if (status == -3) {
            return CommonResult.failed("旧密码错误");
        } else {
            return CommonResult.failed();
        }
    }

    @ApiOperation("删除指定用户信息")
    @RequestMapping(value = "/delete/{id}", method = RequestMethod.POST)
    public CommonResult delete(@PathVariable Long id) {
        int count = userService.delete(id);
        if (count > 0) {
            return CommonResult.success(count);
        }
        return CommonResult.failed();
    }

    @ApiOperation("给用户分配角色")
    @RequestMapping(value = "/role/update", method = RequestMethod.POST)
    public CommonResult updateRole(@RequestParam("adminId") Long adminId,
                                   @RequestParam("roleIds") List<Long> roleIds) {
        int count = userService.updateRole(adminId, roleIds);
        if (count >= 0) {
            return CommonResult.success(count);
        }
        return CommonResult.failed();
    }

    @ApiOperation("获取指定用户的角色")
    @RequestMapping(value = "/role/{adminId}", method = RequestMethod.GET)
    public CommonResult<List<SomBackUserRole>> getRoleList(@PathVariable Long adminId) {
        List<SomBackUserRole> roleList = userService.getRoleList(adminId);
        return CommonResult.success(roleList);
    }

    @ApiOperation("给用户分配+-权限")
    @RequestMapping(value = "/permission/update", method = RequestMethod.POST)
    public CommonResult updatePermission(@RequestParam Long adminId,
                                         @RequestParam("permissionIds") List<Long> permissionIds) {
        int count = userService.updatePermission(adminId, permissionIds);
        if (count > 0) {
            return CommonResult.success(count);
        }
        return CommonResult.failed();
    }

    @ApiOperation("获取用户所有权限（包括+-权限）")
    @RequestMapping(value = "/permission/{adminId}", method = RequestMethod.GET)
    public CommonResult<List<SomMenuMgt>> getPermissionList(@PathVariable Long adminId) {
        List<SomMenuMgt> permissionList = userService.getPermissionList(adminId);
        return CommonResult.success(permissionList);
    }

    // @PreAuthorize("hasAuthority('sys:user:view')")
    @ApiOperation("获取用户所有授权标识")
    @GetMapping(value = "/findPermissions")
    public CommonResult<Set<String>> findPermissions() {
        String username = getUserBaseInfo().getUsername();
        return CommonResult.success(userService.findPermissions(username));
    }

    @ApiOperation(value = "系统是否过期")
    @RequestMapping(value = "/querySysOutOfDate", method = RequestMethod.GET)
    public CommonResult<SysOutOfDateVo> querySysOutOfDate() {
        SysOutOfDateVo sysOutOfDateVo = userService.querySysOutOfDate();
        return CommonResult.success(sysOutOfDateVo);
    }

    @ApiOperation(value = "恢复系统")
    @RequestMapping(value = "/recoverSystem", method = RequestMethod.POST)
    public CommonResult recoverSystem() {
        int count = userService.recoverSystem();
        if (count > 0) {
            return CommonResult.success(count);
        }
        return CommonResult.failed();
    }

    /**
     * 验证token
     *
     * @param sysUserLoginParam
     * @return
     */
    @RequestMapping(value = "/verifyToken", method = RequestMethod.POST)
    public CommonResult verifyToken(@RequestBody SysUserLoginParam sysUserLoginParam) {
        return CommonResult.success(userService.verifyToken(sysUserLoginParam));
    }


    /**
     * 获取权限
     *
     * @return
     */
    private String getAuth() {
        String auth = "";
        if (ValidateUtil.isNotEmpty(getUserBaseInfo().getUserRoleNames())) {
            if (getUserBaseInfo().getUserRoleNames().contains(AUTH_HOSPITAL)) {
                auth = AUTH_HOSPITAL;
            } else if (getUserBaseInfo().getUserRoleNames().contains(AUTH_DEPT)) {
                auth = AUTH_DEPT;
            } else if (getUserBaseInfo().getUserRoleNames().contains(AUTH_DOCTOR)) {
                auth = AUTH_DOCTOR;
            }
        }
        return auth;
    }

    /**
     * 修改用户信息
     *
     * @param sysUserLoginParam
     * @return
     */
    @RequestMapping(value = "/modifySysUserInfo", method = RequestMethod.POST)
    public CommonResult<?> modifySysUserInfo(@RequestBody SysUserLoginParam sysUserLoginParam) {
        sysUserLoginParam.setUsername(getUserBaseInfo().getUsername());
        RedisUtils.delete(sysUserLoginParam.getUsername());
        userService.modifySysUserInfo(sysUserLoginParam);
        // 刷新用户信息
        getUserInfo();
        return CommonResult.success();
    }

    /**
     * 查询医院信息
     */
    @RequestMapping("/queryHospitalInfo")
    public CommonResult<?> queryHospitalInfo() {
        return CommonResult.success(userService.queryHospitalInfo());
    }
}
