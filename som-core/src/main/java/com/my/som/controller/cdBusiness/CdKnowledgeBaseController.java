package com.my.som.controller.cdBusiness;

import com.my.som.common.api.CommonPage;
import com.my.som.common.api.CommonResult;
import com.my.som.common.util.BeanUtil;
import com.my.som.controller.BaseController;
import com.my.som.dto.cdBusiness.CdBusinessQueryDto;
import com.my.som.dto.dipBusiness.DipBusinessQueryDto;
import com.my.som.service.cdBusiness.CdKnowledgeBaseService;
import com.my.som.vo.cdBusiness.CdCoverRateVo;
import com.my.som.vo.cdBusiness.CdNumByMonthVo;
import com.my.som.vo.dipBusiness.DipCoverRateVo;
import com.my.som.vo.dipBusiness.DipNumByMonthVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;
import java.util.Map;

/**
 * CD分组知识库Controller
 * Created by sky on 2020/6/16.
 */
@Controller
@Api(tags = "CdKnowledgeBaseController", description = "DIP分组知识库")
@RequestMapping("/cdKnowledgeBase")
public class CdKnowledgeBaseController extends BaseController {

    @Autowired
    private CdKnowledgeBaseService cdKnowledgeBaseService;

    @ApiOperation("CD分组情况")
    @RequestMapping(value = "/getList", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<CommonPage<Map<String,Object>>> getList(CdBusinessQueryDto queryParam,
                                                                @RequestParam(value = "pageSize", defaultValue = "200") Integer pageSize,
                                                                @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum) {
//        BeanUtil.copyPropertiesIgnoreNull(getUserBaseInfo(), queryParam);
        List<Map<String,Object>> list = cdKnowledgeBaseService.list(queryParam, pageSize, pageNum);
        return CommonResult.success(CommonPage.restPage(list));
    }

    @ApiOperation("查询全院CD分组覆盖占比")
    @RequestMapping(value = "/getCountByCoverRate", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<CdCoverRateVo> getCount(CdBusinessQueryDto queryParam) {
//        BeanUtil.copyPropertiesIgnoreNull(getUserBaseInfo(), queryParam);
        CdCoverRateVo cdCoverRateVo = cdKnowledgeBaseService.getCountByCoverRate(queryParam);
        return CommonResult.success(cdCoverRateVo);
    }

    @ApiOperation("查询本期同期各月CD组数")
    @RequestMapping(value = "/getCountByMonth", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<List<CdNumByMonthVo>> getCountByMonth(CdBusinessQueryDto queryParam) {
//        BeanUtil.copyPropertiesIgnoreNull(getUserBaseInfo(), queryParam);
        List<CdNumByMonthVo> cdNumByMonthVos = cdKnowledgeBaseService.getCountByMonth(queryParam);
        return CommonResult.success(cdNumByMonthVos);
    }
}
