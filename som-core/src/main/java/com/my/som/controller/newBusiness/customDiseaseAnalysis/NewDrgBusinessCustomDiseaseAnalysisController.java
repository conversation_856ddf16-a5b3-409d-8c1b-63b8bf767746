package com.my.som.controller.newBusiness.customDiseaseAnalysis;

import com.my.som.common.api.CommonPage;
import com.my.som.common.api.CommonResult;

import com.my.som.dto.newBusiness.customDisease.NewBusinessCustomDiseaseDto;
import com.my.som.service.newBusiness.customDisease.NewDrgBusinessCustomDiseaseAnalysisService;

import com.my.som.vo.newBusiness.customDisease.NewBusinessCustomDiseaseVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 定制病组分析-德阳
 */
@RestController
@RequestMapping("/newDrgBusinesCustomsDiseaseAnalysisController")
public class NewDrgBusinessCustomDiseaseAnalysisController {

    @Autowired
    private NewDrgBusinessCustomDiseaseAnalysisService newDrgBusinessDiseaseAnalysisService;

    @RequestMapping("/queryDrgDiseaseKpiData")
    public CommonResult<CommonPage<NewBusinessCustomDiseaseVo>> queryDrgDiseaseKpiData(NewBusinessCustomDiseaseDto dto){
        List<NewBusinessCustomDiseaseVo> list = newDrgBusinessDiseaseAnalysisService.queryDrgDiseaseKpiData(dto);
        return CommonResult.success(CommonPage.restPage(list));
    }

    @RequestMapping("/queryDrgDiseaseForecastData")
    public CommonResult<CommonPage<NewBusinessCustomDiseaseVo>> queryDrgDiseaseForecastData(NewBusinessCustomDiseaseDto dto){
        List<NewBusinessCustomDiseaseVo> list = newDrgBusinessDiseaseAnalysisService.queryDrgDiseaseForecastData(dto);
        return CommonResult.success(CommonPage.restPage(list));
    }
}
