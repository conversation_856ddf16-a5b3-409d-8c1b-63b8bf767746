package com.my.som.controller.newBusiness.patient;

import com.my.som.common.api.CommonPage;
import com.my.som.common.api.CommonResult;
import com.my.som.dto.newBusiness.patient.NewBusinessPatientDto;
import com.my.som.service.newBusiness.patient.NewDrgBusinessPatientAnalysisService;
import com.my.som.vo.newBusiness.patient.NewBusinessPatientVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/newDrgBusinessPatientAnalysisController")
public class NewDrgBusinessPatientAnalysisController {

    @Autowired
    private NewDrgBusinessPatientAnalysisService newDrgBusinessPatientAnalysisService;

    @RequestMapping("/queryDrgPatientBasicInfoData")
    public CommonResult<CommonPage<NewBusinessPatientVo>> queryDrgPatientBasicInfoData(@RequestBody NewBusinessPatientDto dto) {
        List<NewBusinessPatientVo> list = newDrgBusinessPatientAnalysisService.queryDrgPatientBasicInfoData(dto);
        return CommonResult.success(CommonPage.restPage(list));
    }

    @RequestMapping("/queryDrgPatientCostInfoData")
    public CommonResult<CommonPage<NewBusinessPatientVo>> queryDrgPatientCostInfoData(@RequestBody NewBusinessPatientDto dto) {
        List<NewBusinessPatientVo> list = newDrgBusinessPatientAnalysisService.queryDrgPatientCostInfoData(dto);
        return CommonResult.success(CommonPage.restPage(list));
    }

    @RequestMapping("/queryDrgPatientForecastData")
    public CommonResult<CommonPage<NewBusinessPatientVo>> queryDrgPatientForecastData(@RequestBody NewBusinessPatientDto dto) {
        List<NewBusinessPatientVo> list = newDrgBusinessPatientAnalysisService.queryDrgPatientForecastData(dto);
        return CommonResult.success(CommonPage.restPage(list));
    }

    @RequestMapping("/queryDrgPatientBasicSortInfo")
    public CommonResult queryDrgPatientBasicSortInfo(@RequestBody NewBusinessPatientDto dto) {
        List<NewBusinessPatientVo> list = newDrgBusinessPatientAnalysisService.queryDrgPatientBasicSortInfo(dto);
        return CommonResult.success(list);
    }
}
