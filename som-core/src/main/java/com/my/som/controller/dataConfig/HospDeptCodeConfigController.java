package com.my.som.controller.dataConfig;

import com.my.som.common.api.CommonPage;
import com.my.som.common.api.CommonResult;
import com.my.som.common.util.ExcelUtil;
import com.my.som.controller.BaseController;
import com.my.som.dto.dataConfig.DoctorCodeDto;
import com.my.som.dto.dataConfig.HospDeptCodeDto;
import com.my.som.service.dataConfig.DoctorCodeConfigService;
import com.my.som.service.dataConfig.HospDeptCodeConfigService;
import com.my.som.vo.dataConfig.DrCodgVo;
import com.my.som.vo.dataConfig.HospDeptCodeVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

@RestController
@RequestMapping("/HospDeptCodeConfigController")
public class HospDeptCodeConfigController extends BaseController{
    @Autowired
    private HospDeptCodeConfigService hospDeptCodeConfigService;

    /**
     * 查询医师代码信息
     * @param dto
     * @return
     */
    @RequestMapping("/queryHospDeptCodeInfo")
    public CommonResult queryHospDeptCodeInfo(HospDeptCodeDto dto){
        List<HospDeptCodeVo> list = hospDeptCodeConfigService.queryHospDeptCodeInfo(dto);
        return CommonResult.success(CommonPage.restPage(list));
    }

}
