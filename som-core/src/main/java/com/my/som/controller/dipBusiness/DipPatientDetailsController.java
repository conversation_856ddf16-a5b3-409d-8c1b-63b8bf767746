package com.my.som.controller.dipBusiness;

import com.my.som.app.dto.deptAnalysis.AppDeptAnalysisDto;
import com.my.som.common.api.CommonPage;
import com.my.som.common.api.CommonResult;
import com.my.som.service.dipBusiness.DipPatientDetailsService;
import com.my.som.vo.dipBusiness.DipSTDHandlerVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.List;

public class DipPatientDetailsController {
    @Autowired
    private DipPatientDetailsService dipPatientDetailsService;

    @RequestMapping("/selectPatientInfo")
    public CommonResult<CommonPage<DipSTDHandlerVo>> selectPatientInfo(@RequestBody AppDeptAnalysisDto dto) {
        List<DipSTDHandlerVo> list = dipPatientDetailsService.selectPatientInfo(dto);
        return CommonResult.success(CommonPage.restPage(list));
    }

}
