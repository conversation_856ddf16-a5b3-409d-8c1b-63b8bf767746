package com.my.som.controller.pregroup;

import com.alibaba.fastjson.JSONObject;
import com.my.som.common.api.CommonMapResult;
import com.my.som.common.exception.AppException;
import com.my.som.common.util.ValidateUtil;
import com.my.som.dto.pregroup.PreGroup2Dto;
import com.my.som.dto.pregroup.PreGroupDto;
import com.my.som.service.pregroup.PreGroupServiceInterface;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 中江 适配老系统预分组
 */
@Slf4j
@RestController
@RequestMapping("/preGroupInterface")
public class PreGroupInterfaceController {
    @Resource
    private PreGroupServiceInterface preGroupServiceInterface;

    /**
     * 旧系统接口预分组返回 url（中江卫宁）
     *
     * @param dto
     * @param response
     * @param request
     * @return
     */
    @RequestMapping("/preGroup")
    public CommonMapResult preGroup(@RequestBody PreGroupDto dto, HttpServletResponse response, HttpServletRequest request) {
        Map<String, Object> map = new HashMap<>();
        //处理住院总费用
        handleZyzfy(dto);
        log.info("旧接口his预分组传参：" + JSONObject.toJSON(dto).toString());
        map.put("url", preGroupServiceInterface.getPreGroupInfo4(dto, response, request).getImageUrl());
        return CommonMapResult.ok(map);
    }

    private void handleZyzfy(PreGroupDto dto) {
        dto.setZyfy(dto.getZyzfy());
        String a48 = dto.getBah();
        Map<String, Object> ybmap = preGroupServiceInterface.selectYb14FeeByBah(a48);
        if(!ValidateUtil.isEmpty(ybmap)&&!ValidateUtil.isEmpty(ybmap.get("d35")) && !ValidateUtil.isEmpty(ybmap.get("yb14Fee"))
            && new BigDecimal(String.valueOf(ybmap.get("yb14Fee"))).compareTo(BigDecimal.ZERO) > 0){

              dto.setYb14fee(new BigDecimal(String.valueOf(ybmap.get("yb14Fee"))));
              dto.setYwlsh(String.valueOf(ybmap.get("yb14Fee")));
              dto.setZyzfy(dto.getYb14fee().add(dto.getYqjcf()));

        }else{
            dto.setZyzfy(dto.getZyfy().add(dto.getYqjcf()));
        }
        if(ValidateUtil.isEmpty(dto.getZyzfy()) || BigDecimal.ZERO.compareTo(dto.getZyzfy())==0 ){
            throw new AppException("住院总费用不能为0或者空");
        }
    }

    /**
     * 龙台老系统转新系统预分组接口
     *
     * @param dto
     * @param response
     * @param request
     * @return
     */
    @RequestMapping("/preGroup2")
    public CommonMapResult preGroup2(@RequestBody PreGroup2Dto dto, HttpServletResponse response, HttpServletRequest request) {
        PreGroup2Dto dto2 =new PreGroup2Dto();
        Map<String, Object> map = new HashMap<>();
        log.info("预分组传参：" + JSONObject.toJSON(dto).toString());
        map.put("url", preGroupServiceInterface.getPreGroupInfo5(dto, response, request).getImageUrl());
        return CommonMapResult.ok(map);
    }
}
