package com.my.som.controller.cdBusiness;

import com.my.som.common.api.CommonMapResult;
import com.my.som.common.api.CommonPage;
import com.my.som.common.api.CommonResult;
import com.my.som.common.util.BeanUtil;
import com.my.som.controller.BaseController;
import com.my.som.dto.cdBusiness.CdBusinessQueryDto;
import com.my.som.service.cdBusiness.CdPayToPredictService;
import com.my.som.vo.cdBusiness.CdPayToPredictVo;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: zyd
 * @Date 2021/8/19 5:22 下午
 * @Version 1.0
 * @Description: dip 支付预测
 */
@RestController
@RequestMapping("/cdPayToPredictController")
public class CdPayToPredictController extends BaseController {

    @Resource
    private CdPayToPredictService cdPayToPredictService;

    /**
     * 获取表格数据
     * @param queryParam 查询条件
     * @param pageSize 分页数量
     * @param pageNum 当前页数
     * @return
     */
    @RequestMapping("/getList")
    public CommonResult<CommonPage<CdPayToPredictVo>> getList(CdBusinessQueryDto queryParam,
                                                              @RequestParam(value = "pageSize", defaultValue = "200") Integer pageSize,
                                                              @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum) {
//        BeanUtil.copyPropertiesIgnoreNull(getUserBaseInfo(), queryParam);
        List<CdPayToPredictVo> List = cdPayToPredictService.getList(queryParam, pageSize, pageNum);
        return CommonResult.success(CommonPage.restPage(List));
    }

    /**
     * 获取cd预测数据
     * @param dto 参数
     * @return 预测信息
     */
    @RequestMapping("/getListPoint")
    public CommonMapResult getListPoint(CdBusinessQueryDto dto){
         CommonMapResult result = cdPayToPredictService.getListPoint(dto);
         return result;
    }


    /**
     * 查询前几月数据
     * @param dto 参数
     * @return 数据信息
     */
    @RequestMapping("/getMonthData")
    public CommonMapResult getMonthData(CdBusinessQueryDto dto){
        CommonMapResult result = cdPayToPredictService.getMonthData(dto);
        return result;
    }
}
