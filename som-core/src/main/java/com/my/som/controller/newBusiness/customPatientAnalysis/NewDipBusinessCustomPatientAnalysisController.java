package com.my.som.controller.newBusiness.customPatientAnalysis;

import com.my.som.common.api.CommonPage;
import com.my.som.common.api.CommonResult;
import com.my.som.dto.newBusiness.customPatient.NewBusinessCustomPatientDto;

import com.my.som.service.newBusiness.customPatient.NewDipBusinessCustomPatientAnalysisService;
import com.my.som.vo.newBusiness.customPatient.NewBusinessCustomPatientVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/newDipBusinessCustomPatientAnalysisController")
public class NewDipBusinessCustomPatientAnalysisController {

    @Autowired
    private NewDipBusinessCustomPatientAnalysisService newDipBusinessPatientAnalysisService;

    /**
     * 查询患者基本信息
     * @param dto
     * @return
     */
    @RequestMapping("/queryPatientBasicInfoData")
    public CommonResult<CommonPage<NewBusinessCustomPatientVo>> queryPatientBasicInfoData(@RequestBody NewBusinessCustomPatientDto dto) {
        List<NewBusinessCustomPatientVo> list = newDipBusinessPatientAnalysisService.queryPatientBasicInfoData(dto);
        return CommonResult.success(CommonPage.restPage(list));
    }

    /**
     * 查询患者费用信息
     * @param dto
     * @return
     */
    @RequestMapping("/queryPatientCostInfoData")
    public CommonResult<CommonPage<NewBusinessCustomPatientVo>> queryPatientCostInfoData(@RequestBody NewBusinessCustomPatientDto dto) {
        List<NewBusinessCustomPatientVo> list = newDipBusinessPatientAnalysisService.queryPatientCostInfoData(dto);
        return CommonResult.success(CommonPage.restPage(list));
    }

    /**
     * 查询患者预测信息
     * @param dto
     * @return
     */
    @RequestMapping("/queryPatientForecastData")
    public CommonResult<CommonPage<NewBusinessCustomPatientVo>> queryPatientForecastData(@RequestBody NewBusinessCustomPatientDto dto) {
        List<NewBusinessCustomPatientVo> list = newDipBusinessPatientAnalysisService.queryPatientForecastData(dto);
        return CommonResult.success(CommonPage.restPage(list));
    }
}
