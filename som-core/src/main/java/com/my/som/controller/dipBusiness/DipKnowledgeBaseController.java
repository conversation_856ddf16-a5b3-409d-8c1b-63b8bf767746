package com.my.som.controller.dipBusiness;

import com.my.som.common.api.CommonPage;
import com.my.som.common.api.CommonResult;
import com.my.som.common.util.BeanUtil;
import com.my.som.controller.BaseController;
import com.my.som.dto.dipBusiness.DipBusinessQueryDto;
import com.my.som.service.dipBusiness.DipKnowledgeBaseService;
import com.my.som.vo.dipBusiness.DipCoverRateVo;
import com.my.som.vo.dipBusiness.DipNumByMonthVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;
import java.util.Map;

/**
 * DIP分组知识库Controller
 * Created by sky on 2020/6/16.
 */
@Controller
@Api(tags = "DipKnowledgeBaseController", description = "DIP分组知识库")
@RequestMapping("/dipKnowledgeBase")
public class DipKnowledgeBaseController extends BaseController {
    @Autowired
    private DipKnowledgeBaseService dipKnowledgeBaseService;

    @ApiOperation("DIP分组情况")
    @RequestMapping(value = "/getList", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<CommonPage<Map<String,Object>>> getList(DipBusinessQueryDto queryParam,
                                                                @RequestParam(value = "pageSize", defaultValue = "200") Integer pageSize,
                                                                @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum) {
//        BeanUtil.copyPropertiesIgnoreNull(getUserBaseInfo(), queryParam);
        List<Map<String,Object>> dipKnowledgeVoList = dipKnowledgeBaseService.list(queryParam, pageSize, pageNum);
        return CommonResult.success(CommonPage.restPage(dipKnowledgeVoList));
    }

    @ApiOperation("查询全院DIP分组覆盖占比")
    @RequestMapping(value = "/getCountByCoverRate", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<DipCoverRateVo> getCount(DipBusinessQueryDto queryParam) {
//        BeanUtil.copyPropertiesIgnoreNull(getUserBaseInfo(), queryParam);
        DipCoverRateVo dipCoverRateVo = dipKnowledgeBaseService.getCountByCoverRate(queryParam);
        return CommonResult.success(dipCoverRateVo);
    }

    @ApiOperation("查询本期同期各月DIP组数")
    @RequestMapping(value = "/getCountByMonth", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<List<DipNumByMonthVo>> getCountByMonth(DipBusinessQueryDto queryParam) {
        BeanUtil.copyPropertiesIgnoreNull(getUserBaseInfo(), queryParam);
        List<DipNumByMonthVo> dipNumByMonthVoList = dipKnowledgeBaseService.getCountByMonth(queryParam);
        return CommonResult.success(dipNumByMonthVoList);
    }

}
