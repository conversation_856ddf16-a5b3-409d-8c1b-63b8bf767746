package com.my.som.controller.dataHandle;

import com.my.som.common.api.CommonPage;
import com.my.som.common.api.CommonResult;
import com.my.som.controller.BaseController;
import com.my.som.dto.dataHandle.DataHandleQueryParam;
import com.my.som.model.dataHandle.SomDataprosLog;
import com.my.som.service.dataHandle.DataHandleLogService;
import com.my.som.common.vo.SysUserBase;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 数据处理日志Controller
 * Created by sky on 2020/1/5.
 */
@Controller
@Api(tags = "DataHandleLogController", description = "数据处理日志")
@RequestMapping("/dataHandleLog")
public class DataHandleLogController extends BaseController {
    @Autowired
    private DataHandleLogService dataHandleLogService;

    @ApiOperation("查询数据处理日志")
    @RequestMapping(value = "/logList", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<CommonPage<SomDataprosLog>> list(DataHandleQueryParam queryParam,
                   @RequestParam(value = "pageSize", defaultValue = "50") Integer pageSize,
                   @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum) {
        SysUserBase sysUserBase =  getUserBaseInfo();
        List<SomDataprosLog> logList = dataHandleLogService.list(queryParam, pageSize, pageNum, sysUserBase);
        return CommonResult.success(CommonPage.restPage(logList));
    }

    @ApiOperation("当前批次有效数据数量")
    @RequestMapping(value = "/queryCurrentBusSettleList", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<Long> queryCurrentBusSettleList(@RequestParam("logId") Long logId) {
        Long count = dataHandleLogService.queryCurrentBusSettleList(logId);
        return CommonResult.success(count);
    }

    @ApiOperation("根据批次号重新执行该批次")
    @RequestMapping(value = "/restartProcessByLogId", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult restartProcessByLogId(@RequestParam("logId") Long logId) {
        SysUserBase sysUserBase =  getUserBaseInfo();
        try{
            dataHandleLogService.restartProcessByLogId(logId,sysUserBase);
            return CommonResult.success();
        }catch (Exception e){
            return CommonResult.failed("数据处理重新调用出现异常！");
        }
    }

    @ApiOperation("根据批次号继续执行该批次")
    @RequestMapping(value = "/keepProcessByLogId", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult keepProcessByLogId(@RequestParam("logId") Long logId) {
        SysUserBase sysUserBase =  getUserBaseInfo();
        try{
            dataHandleLogService.keepProcessByLogId(logId,sysUserBase);
            return CommonResult.success();
        }catch (Exception e){
            return CommonResult.failed("数据处理继续调用出现异常！");
        }
    }

}
