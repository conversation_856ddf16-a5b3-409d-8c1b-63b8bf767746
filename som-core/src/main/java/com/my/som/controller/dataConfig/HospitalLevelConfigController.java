package com.my.som.controller.dataConfig;

import com.my.som.common.api.CommonResult;
import com.my.som.common.dto.CommonQueryDto;
import com.my.som.dto.dataConfig.UpdateHospitalLevelDto;
import com.my.som.service.dataConfig.HospitalLevelConfigService;
import com.my.som.vo.dataConfig.HospitalDataVo;
import com.my.som.vo.dataConfig.HospLvVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

/**
 * 医院等级配置
 */
@RestController
@RequestMapping("/hospitalLevelConfigController")
public class HospitalLevelConfigController {

    @Autowired
    HospitalLevelConfigService hospitalLevelConfigService;

    @RequestMapping("/queryHospital")
    public CommonResult<List<HospitalDataVo>> queryHospital(@RequestBody UpdateHospitalLevelDto dto){
        List<HospitalDataVo> list = hospitalLevelConfigService.queryHospital(dto);
        return CommonResult.success(list);
    }

    @RequestMapping("/selectLevels")
    public CommonResult<List<HospLvVo>> selectLevels(){
        List<HospLvVo> list = hospitalLevelConfigService.queryHospitalLevel();
        return CommonResult.success(list);
    }

    @RequestMapping("/updateLevel")
    public CommonResult<List<String>> updateLevel(@RequestBody UpdateHospitalLevelDto dto){
        List<String> list = new ArrayList<>();
        hospitalLevelConfigService.updateLevel(dto);
        return CommonResult.success(list);
    }
}
