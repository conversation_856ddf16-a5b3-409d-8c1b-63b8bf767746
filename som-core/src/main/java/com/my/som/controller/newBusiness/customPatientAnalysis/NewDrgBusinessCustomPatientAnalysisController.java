package com.my.som.controller.newBusiness.customPatientAnalysis;

import com.my.som.common.api.CommonPage;
import com.my.som.common.api.CommonResult;
import com.my.som.dto.newBusiness.customPatient.NewBusinessCustomPatientDto;

import com.my.som.service.newBusiness.customPatient.NewDrgBusinessCustomPatientAnalysisService;
import com.my.som.vo.newBusiness.customPatient.NewBusinessCustomPatientVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/newDrgBusinessCustomPatientAnalysisController")
public class NewDrgBusinessCustomPatientAnalysisController {

    @Autowired
    private NewDrgBusinessCustomPatientAnalysisService newDrgBusinessPatientAnalysisService;

    @RequestMapping("/queryDrgPatientBasicInfoData")
    public CommonResult<CommonPage<NewBusinessCustomPatientVo>> queryDrgPatientBasicInfoData(@RequestBody NewBusinessCustomPatientDto dto) {
        List<NewBusinessCustomPatientVo> list = newDrgBusinessPatientAnalysisService.queryDrgPatientBasicInfoData(dto);
        return CommonResult.success(CommonPage.restPage(list));
    }

    @RequestMapping("/queryDrgPatientCostInfoData")
    public CommonResult<CommonPage<NewBusinessCustomPatientVo>> queryDrgPatientCostInfoData(@RequestBody NewBusinessCustomPatientDto dto) {
        List<NewBusinessCustomPatientVo> list = newDrgBusinessPatientAnalysisService.queryDrgPatientCostInfoData(dto);
        return CommonResult.success(CommonPage.restPage(list));
    }

    @RequestMapping("/queryDrgPatientForecastData")
    public CommonResult<CommonPage<NewBusinessCustomPatientVo>> queryDrgPatientForecastData(@RequestBody NewBusinessCustomPatientDto dto) {
        List<NewBusinessCustomPatientVo> list = newDrgBusinessPatientAnalysisService.queryDrgPatientForecastData(dto);
        return CommonResult.success(CommonPage.restPage(list));
    }

    @RequestMapping("/queryDrgPatientBasicSortInfo")
    public CommonResult queryDrgPatientBasicSortInfo(@RequestBody NewBusinessCustomPatientDto dto) {
        List<NewBusinessCustomPatientVo> list = newDrgBusinessPatientAnalysisService.queryDrgPatientBasicSortInfo(dto);
        return CommonResult.success(list);
    }
}
