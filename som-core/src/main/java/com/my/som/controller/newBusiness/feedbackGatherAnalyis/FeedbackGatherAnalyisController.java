package com.my.som.controller.newBusiness.feedbackGatherAnalyis;

import com.my.som.app.vo.listManagement.ListManagementVo;
import com.my.som.common.api.CommonPage;
import com.my.som.common.api.CommonResult;
import com.my.som.dto.newBusiness.FeedbackGatherAnalyis.FeedbackGatherAnalyisDto;

import com.my.som.service.newBusiness.feedbackGatherAnalyis.FeedbackGatherAnalyisService;
import com.my.som.vo.newBusiness.FeedbackGatherAnalyis.FeedbackGatherAnalyisVo;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 *
 * 分组反馈汇总分析
 */

@RestController
@RequestMapping(value = "/feedbackGatherAnalyisController")
public class FeedbackGatherAnalyisController {

    @Autowired
    private FeedbackGatherAnalyisService feedbackGatherAnalyisService;
    @RequestMapping("/queryFeedbackGatherData")
    public CommonResult<CommonPage> queryDataThread(FeedbackGatherAnalyisDto dto){

        return CommonResult.success(CommonPage.restPage(feedbackGatherAnalyisService.queryFeedbackGatherData(dto)));
    }
}
