package com.my.som.controller.firstPage;

import com.my.som.common.api.CommonMapResult;
import com.my.som.controller.BaseController;
import com.my.som.dto.firstpage.FirstPageDto;
import com.my.som.service.firstPage.FirstPage2Service;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @author: zyd
 * @Date 2021/8/2 6:28 下午
 * @Version 1.0
 * @Description:   首页
 */
@RestController
@RequestMapping("/firstPage2")
public class FirstPage2Controller extends BaseController {

    @Resource
    private FirstPage2Service firstPage2Service;

    /**
     * 查询基本情况信息
     * @param dto 参数
     * @return 基本情况信息
     */
    @RequestMapping("/queryBaseInfo")
    public CommonMapResult queryBaseInfo(FirstPageDto dto){
        setUserInfo(dto);
        CommonMapResult result = firstPage2Service.queryBaseInfo(dto);
        return result;
    }

    /**
     * 查询逆差预测信息
     * @param dto 参数
     * @return 逆差预测信息
     */
    @RequestMapping("/queryForecastInfo")
    public CommonMapResult queryForecastInfo(FirstPageDto dto){
        setUserInfo(dto);
        CommonMapResult result = firstPage2Service.queryForecastInfo(dto);
        return result;
    }

    /**
     * 查询病例质控信息
     * @param dto 参数
     * @return 逆差预测信息
     */
    @RequestMapping("/queryControlInfo")
    public CommonMapResult queryControlInfo(FirstPageDto dto){
        setUserInfo(dto);
        CommonMapResult result = firstPage2Service.queryControlInfo(dto);
        return result;
    }

    /**
     * 设置用户信息
     * @param dto
     */
    private void setUserInfo(FirstPageDto dto){
        dto.setUsername(getUserBaseInfo().getUsername());
    }

}
