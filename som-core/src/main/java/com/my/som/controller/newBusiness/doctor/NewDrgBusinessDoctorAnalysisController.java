package com.my.som.controller.newBusiness.doctor;

import com.my.som.common.api.CommonPage;
import com.my.som.common.api.CommonResult;
import com.my.som.dto.newBusiness.doctor.NewBusinessDoctorDto;
import com.my.som.service.newBusiness.doctor.NewDrgBusinessDoctorAnalysisService;
import com.my.som.vo.newBusiness.doctor.NewBusinessDoctorVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/newDrgBusinessDoctorAnalysisController")
public class NewDrgBusinessDoctorAnalysisController {

    @Autowired
    private NewDrgBusinessDoctorAnalysisService newDrgBusinessDoctorAnalysisService;

    /**
     * 查询医生kpi
     * @param dto
     * @return
     */
    @RequestMapping("/queryDrgDoctorKpiData")
    public CommonResult<CommonPage<NewBusinessDoctorVo>> queryDrgDoctorKpiData(NewBusinessDoctorDto dto) {
        List<NewBusinessDoctorVo> list = newDrgBusinessDoctorAnalysisService.queryDrgDoctorKpiData(dto);
        return CommonResult.success(CommonPage.restPage(list));
    }

    /**
     * 查询医生预测
     * @param dto
     * @return
     */
    @RequestMapping("/queryDrgDoctorForecastData")
    public CommonResult<CommonPage<NewBusinessDoctorVo>> queryDrgDoctorForecastData(NewBusinessDoctorDto dto) {
        List<NewBusinessDoctorVo> list = newDrgBusinessDoctorAnalysisService.queryDrgDoctorForecastData(dto);
        return CommonResult.success(CommonPage.restPage(list));
    }
}
