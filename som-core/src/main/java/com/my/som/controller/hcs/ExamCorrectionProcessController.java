package com.my.som.controller.hcs;

import com.my.som.common.api.CommonPage;
import com.my.som.common.api.CommonResult;
import com.my.som.common.constant.HcsConst;
import com.my.som.dto.hcs.ExamCorrectionResultParam;
import com.my.som.hcs.engine.dto.HcmDataprosLogDto;
import com.my.som.hcs.engine.dto.MedFeeItemQueryDto;
import com.my.som.hcs.engine.vo.HcmDataprosLog;
import com.my.som.service.hcs.ExamCorrectionResultService;
import com.my.som.service.hcs.RunHcsDataProcessService;
import com.my.som.vo.hcs.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;

@Controller
@Api(tags = "ExamCorrectionProcessController", description = "自查自纠过程控制层")
@RequestMapping("/examCorrectionProcessController")
public class ExamCorrectionProcessController {

    @Autowired
    private ExamCorrectionResultService examCorrectionResultService;

    @Autowired
    private RunHcsDataProcessService runHcsDataProcessService;

    @ApiOperation("执行批量跑批流程")
    @RequestMapping(value = "/runProcHcs", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult runProcHcs(MedFeeItemQueryDto queryParam) {
        //1、设置执行门诊自查自纠
        queryParam.setRuleScenType(HcsConst.RULE_SCEN_TYPE_OUTPAT);
        runHcsDataProcessService.runProcBatchByDate(queryParam);

        //1、设置执行住院自查自纠
        queryParam.setRuleScenType(HcsConst.RULE_SCEN_TYPE_INHOSP);
        runHcsDataProcessService.runProcBatchByDate(queryParam);

        return CommonResult.success();
    }

    @ApiOperation("查询跑批流程")
    @RequestMapping(value = "/queryHcsProcList", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<CommonPage<HcmDataprosLog>> queryHcsProcList(HcmDataprosLogDto queryParam) {
        List<HcmDataprosLog> hcmDataprosLogList = runHcsDataProcessService.queryHcsProcList(queryParam);
        return CommonResult.success(CommonPage.restPage(hcmDataprosLogList));
    }
}
