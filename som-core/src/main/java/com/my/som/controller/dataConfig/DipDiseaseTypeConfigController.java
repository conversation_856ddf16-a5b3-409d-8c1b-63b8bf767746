package com.my.som.controller.dataConfig;

import com.my.som.common.api.CommonPage;
import com.my.som.common.api.CommonResult;
import com.my.som.common.dto.CommonQueryDto;
import com.my.som.dto.dataConfig.DipDiseaseTypeConfigDto;
import com.my.som.service.dataConfig.DipDiseaseTypeConfigService;
import com.my.som.vo.dataConfig.DipDiseaseTypeVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @author: zyd
 * @version 1.0
 * @date: 2023-07-31
 * @description: dip病种类型配置
 */
@RestController
@RequestMapping("/dipDiseaseTypeConfigController")
public class DipDiseaseTypeConfigController {


    @Autowired
    private DipDiseaseTypeConfigService dipDiseaseTypeConfigService;

    /**
     * 查询数据
     * @param dto 参数
     * @return
     */
    @RequestMapping("/queryData")
    public CommonResult<CommonPage<DipDiseaseTypeVo>> queryData(DipDiseaseTypeConfigDto dto){
        return CommonResult.success(CommonPage.restPage(dipDiseaseTypeConfigService.queryData(dto)));
    }

    /**
     * 插入数据
     * @param dto
     * @return
     */
    @RequestMapping("/insertDipDiseaseType")
    public CommonResult insertDipDiseaseType(DipDiseaseTypeConfigDto dto) {
        dipDiseaseTypeConfigService.insertDipDiseaseType(dto);
        return CommonResult.success();
    }

    /**
     * 删除数据
     * @param dto
     * @return
     */
    @RequestMapping("/deleteDipDiseaseType")
    public CommonResult deleteDipDiseaseType(DipDiseaseTypeConfigDto dto) {
        dipDiseaseTypeConfigService.deleteDipDiseaseType(dto);
        return CommonResult.success();
    }

    /**
     * 更新数据
     * @param dto
     * @return
     */
    @RequestMapping("/updateDipDiseaseType")
    public CommonResult updateDipDiseaseType(DipDiseaseTypeConfigDto dto) {
        dipDiseaseTypeConfigService.updateDipDiseaseType(dto);
        return CommonResult.success();
    }
}
