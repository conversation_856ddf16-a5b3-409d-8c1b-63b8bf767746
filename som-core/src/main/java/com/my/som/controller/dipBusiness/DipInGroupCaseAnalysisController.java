package com.my.som.controller.dipBusiness;

import com.my.som.common.api.CommonPage;
import com.my.som.common.api.CommonResult;
import com.my.som.common.util.BeanUtil;
import com.my.som.controller.BaseController;
import com.my.som.dto.dipBusiness.HospitalAnalysisDto;
import com.my.som.service.dipBusiness.DipInGroupCaseAnalysisService;
import com.my.som.vo.dipBusiness.DipInGroupAnalysisCountVo;
import com.my.som.vo.dipBusiness.DipInGroupAnalysisVo;
import com.my.som.vo.dipBusiness.DipInGroupTopCountVo;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;

@Controller
@RequestMapping("/dipInGroupAnalysis")
public class DipInGroupCaseAnalysisController extends BaseController {
    @Autowired
    private DipInGroupCaseAnalysisService dipInGroupCaseAnalysisService;

    @RequestMapping(value = "/mainInfoList", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<CommonPage<DipInGroupAnalysisVo>> getData(HospitalAnalysisDto dto,
                                                                  @RequestParam(value = "pageSize", defaultValue = "200") Integer pageSize,
                                                                  @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum) {
//        BeanUtil.copyPropertiesIgnoreNull(getUserBaseInfo(),dto);
        List<DipInGroupAnalysisVo> dipInGroupAnalysisVoList = dipInGroupCaseAnalysisService.getData(dto,pageSize,pageNum);
        return CommonResult.success(CommonPage.restPage(dipInGroupAnalysisVoList));
    }

    @ApiOperation("查询全院入组情况人次统计信息")
    @RequestMapping(value = "/getTopCountInfo", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<DipInGroupTopCountVo> getTopCountInfo(HospitalAnalysisDto dto) {
//        BeanUtil.copyPropertiesIgnoreNull(getUserBaseInfo(),dto);
        DipInGroupTopCountVo topCountInfo = dipInGroupCaseAnalysisService.getTopCountInfo(dto);
        return CommonResult.success(topCountInfo);
    }



    @RequestMapping(value = "/getNoGroupResonCountInfo", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<List<DipInGroupAnalysisCountVo>> getNoGroupResonCountInfo(HospitalAnalysisDto dto) {
//        BeanUtil.copyPropertiesIgnoreNull(getUserBaseInfo(), dto);
        List<DipInGroupAnalysisCountVo> inGroupAnalysisCountInfoList = dipInGroupCaseAnalysisService.getNoGroupResonCountInfo(dto);
        return CommonResult.success(inGroupAnalysisCountInfoList);
    }


}
