package com.my.som.controller.sys;

import com.my.som.common.api.CommonPage;
import com.my.som.common.api.CommonResult;
import com.my.som.common.page.PageRequest;
import com.my.som.service.sys.SysSettleListDictService;
import com.my.som.vo.SomSysCode;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/settleListDict")
public class SysSettleListDictController {

    @Autowired
    private SysSettleListDictService sysSettleListDictService;

    //@PreAuthorize("hasAuthority('sys:dict:add') OR hasAuthority('sys:dict:edit')")
    @RequestMapping("/save")
    public CommonResult save(SomSysCode record) {
        return CommonResult.success(sysSettleListDictService.save(record));
    }

    //@PreAuthorize("hasAuthority('sys:dict:delete')")
    @RequestMapping("/delete")
    public CommonResult delete(@RequestBody List<SomSysCode> records) {
        return CommonResult.success(sysSettleListDictService.delete(records));
    }

    @RequestMapping("/refreshCache")
    public CommonResult flushCache() {
        sysSettleListDictService.flushCache();
        return CommonResult.success();
    }

    //@PreAuthorize("hasAuthority('sys:dict:view')")
    @RequestMapping("/queryPage")
    public CommonResult<CommonPage<SomSysCode>> queryPage(@RequestBody PageRequest pageRequest) {
        return CommonResult.success(sysSettleListDictService.findPage(pageRequest));
    }
}
