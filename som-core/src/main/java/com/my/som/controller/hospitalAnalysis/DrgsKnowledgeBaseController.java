package com.my.som.controller.hospitalAnalysis;

import com.my.som.common.api.CommonResult;
import com.my.som.common.util.BeanUtil;
import com.my.som.controller.BaseController;
import com.my.som.dto.hospitalAnalysis.HospitalAnalysisQueryParam;
import com.my.som.service.hospitalAnalysis.DrgsKnowledgeBaseService;
import com.my.som.vo.hospitalAnalysis.DrgsCoverRateVo;
import com.my.som.vo.hospitalAnalysis.DrgsKnowledgeVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;

/**
 * DRGs知识库Controller
 * Created by sky on 2020/7/20.
 */
@Controller
@Api(tags = "DrgsKnowledgeBaseController", description = "DRGs知识库")
@RequestMapping("/drgsKnowledgeBase")
public class DrgsKnowledgeBaseController extends BaseController {
    @Autowired
    private DrgsKnowledgeBaseService drgsKnowledgeBaseService;

    @ApiOperation("DRGs知识库总体指标情况")
    @RequestMapping(value = "/getList", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<List<DrgsKnowledgeVo>> getList(HospitalAnalysisQueryParam queryParam) {
//        BeanUtil.copyPropertiesIgnoreNull(getUserBaseInfo(), queryParam);
        List<DrgsKnowledgeVo> drgsKnowledgeVoList = drgsKnowledgeBaseService.list(queryParam);
        return CommonResult.success(drgsKnowledgeVoList);
    }

    @ApiOperation("查询全院DRGs覆盖占比")
    @RequestMapping(value = "/getCountByCoverRate", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<DrgsCoverRateVo> getCount(HospitalAnalysisQueryParam queryParam) {
//        BeanUtil.copyPropertiesIgnoreNull(getUserBaseInfo(), queryParam);
        DrgsCoverRateVo drgsCoverRateVo = drgsKnowledgeBaseService.getCountByCoverRate(queryParam);
        return CommonResult.success(drgsCoverRateVo);
    }

}
