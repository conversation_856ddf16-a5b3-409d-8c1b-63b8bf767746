package com.my.som.controller.hospitalAnalysis;

import com.my.som.common.api.CommonPage;
import com.my.som.common.api.CommonResult;
import com.my.som.common.util.BeanUtil;
import com.my.som.controller.BaseController;
import com.my.som.dto.hospitalAnalysis.HospitalAnalysisQueryParam;
import com.my.som.service.hospitalAnalysis.DrgsSafetyAndQualityService;
import com.my.som.vo.hospitalAnalysis.DrgsSafetyAndQualityCountVo;
import com.my.som.vo.hospitalAnalysis.RiskLevelMedicalDetailVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;


/**
 * drgs安全质量分析Controller
 * Created by sky on 2020/3/17.
 */
@Controller
@Api(tags = "DrgsSafetyAndQualityController", description = "drgs安全质量分析")
@RequestMapping("/drgsSafetyAndQuality")
public class DrgsSafetyAndQualityController extends BaseController {
    @Autowired
    private DrgsSafetyAndQualityService drgsSafetyAndQualityService;

    @ApiOperation("drgs安全质量病案详情")
    @RequestMapping(value = "/getList", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<CommonPage<RiskLevelMedicalDetailVo>> getList(HospitalAnalysisQueryParam queryParam,
                                                                @RequestParam(value = "pageSize", defaultValue = "200") Integer pageSize,
                                                                @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum) {
//        BeanUtil.copyPropertiesIgnoreNull(getUserBaseInfo(), queryParam);
        List<RiskLevelMedicalDetailVo> riskLevelMedicalDetailVoList = drgsSafetyAndQualityService.list(queryParam, pageSize, pageNum);
        return CommonResult.success(CommonPage.restPage(riskLevelMedicalDetailVoList));
    }

    @ApiOperation("drgs安全质量分析指标信息")
    @RequestMapping(value = "/getCountInfo", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<DrgsSafetyAndQualityCountVo> getCountInfo(HospitalAnalysisQueryParam queryParam) {
        BeanUtil.copyPropertiesIgnoreNull(getUserBaseInfo(), queryParam);
        DrgsSafetyAndQualityCountVo drgsSafetyAndQualityCountVo = drgsSafetyAndQualityService.getCountInfo(queryParam);
        return CommonResult.success(drgsSafetyAndQualityCountVo);
    }

}
