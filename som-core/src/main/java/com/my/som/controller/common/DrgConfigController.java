package com.my.som.controller.common;

import com.my.som.common.api.CommonDictResult;
import com.my.som.common.api.CommonPage;
import com.my.som.common.api.CommonResult;
import com.my.som.dto.common.DipConfigDto;
import com.my.som.dto.common.PriceDto;
import com.my.som.service.common.DrgConfigService;
import com.my.som.vo.common.DipConfigVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/drgConfigController")
public class DrgConfigController {

    @Autowired
    private DrgConfigService drgConfigService;

    @RequestMapping("/queryData")
    public CommonResult queryData(DipConfigDto dto){
        List<?> list = drgConfigService.queryData(dto);
        return CommonResult.success(CommonPage.restPage(list));
    }

    @RequestMapping("/updateValue")
    public CommonDictResult updateValue(@RequestBody DipConfigDto dto){
        drgConfigService.updateValue(dto);
        return CommonDictResult.success();
    }

    @RequestMapping("/addDrgConfig")
    public CommonDictResult addDrgConfig(DipConfigDto dto){
        drgConfigService.addDrgConfig(dto);
        return CommonDictResult.success();
    }

    @RequestMapping("/deleteDrg")
    public CommonResult deleteDrg(DipConfigDto dto){
        drgConfigService.deleteDrg(dto);
        return CommonResult.success();
    }

    @RequestMapping("/deleteDip")
    public CommonResult deleteDip(DipConfigDto dto){
        drgConfigService.deleteDip(dto);
        return CommonResult.success();
    }
    @RequestMapping("/resetFee")
    public CommonResult resetFee(PriceDto dto){
        drgConfigService.exists(dto);
        drgConfigService.insertOrUpdatePrice(dto);
        drgConfigService.resetFee(dto);
        return CommonResult.success();
    }
    @RequestMapping("/updatePriceAndResetFee")
    public CommonResult updatePriceAndResetFee(PriceDto dto){
        drgConfigService.updatePriceAndResetFee(dto);
        return CommonResult.success();
    }

    @RequestMapping("/queryPrice")
    public CommonResult queryPrice(PriceDto dto){
        PriceDto priceDto = drgConfigService.queryPrice(dto);
        return CommonResult.success(priceDto);
    }

}
