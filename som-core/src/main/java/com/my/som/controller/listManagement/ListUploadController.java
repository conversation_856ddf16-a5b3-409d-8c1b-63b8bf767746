package com.my.som.controller.listManagement;

import com.my.som.common.api.CommonPage;
import com.my.som.common.api.CommonResult;
import com.my.som.controller.BaseController;
import com.my.som.dto.dataHandle.DataHandleCommonDto;
import com.my.som.dto.listIUploadedQuery.ListUploadedQueryDto;
import com.my.som.dto.listManagement.ListUploadDto;

import com.my.som.dto.listManagement.MainCode;
import com.my.som.service.listManagement.ListUploadService;
import com.my.som.service.medicalQuality.SettleListManageService;
import com.my.som.vo.listManagement.ListUploadVo;
import com.my.som.vo.listManagement.UploadFalseVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/listUploadController")
public class ListUploadController extends BaseController {

    @Autowired
    private ListUploadService listUploadService;

    @Autowired
    private SettleListManageService settleListManageService;

    @RequestMapping("/queryData")
    public CommonResult<CommonPage> queryData(@RequestBody ListUploadDto dto) {
        List<ListUploadVo> list = listUploadService.queryData(dto);
        return CommonResult.success(CommonPage.restPage(list));
    }

    @RequestMapping("/querySybData")
    public CommonResult<CommonPage> querySybData(@RequestBody ListUploadDto dto) {
        List<ListUploadVo> list = listUploadService.querySybData(dto);
        return CommonResult.success(CommonPage.restPage(list));
    }
    @RequestMapping("/queryYASybData")
    public CommonResult<CommonPage> queryYASybData(@RequestBody ListUploadDto dto) {
        List<ListUploadVo> list = listUploadService.queryYASybData(dto);
        return CommonResult.success(CommonPage.restPage(list));
    }

    @RequestMapping("/updateData")
    public CommonResult updateData(@RequestBody ListUploadDto dto) {
        dto.setOpe(getUserBaseInfo().getNknm());
        dto.setHospitalId(getUserBaseInfo().getHospitalId());
        return CommonResult.success(listUploadService.updateData(dto));
    }

    @RequestMapping("/updateSybData")
    public CommonResult updateSybData(@RequestBody ListUploadDto dto) {
        dto.setOpe(getUserBaseInfo().getNknm());
        return CommonResult.success(listUploadService.updateSybData(dto));
    }

    @RequestMapping("/updateYASybData")
    public CommonResult updateYASybData(@RequestBody ListUploadDto dto) {
        dto.setOpe(getUserBaseInfo().getNknm());
        return CommonResult.success(listUploadService.updateYASybData(dto));
    }

    @RequestMapping("/queryRecordData")
    public CommonResult queryRecordData(@RequestBody ListUploadDto dto) {
        List<ListUploadVo> list = listUploadService.queryRecordData(dto);
        return CommonResult.success(list);
    }

    @RequestMapping("/queryRecordSybData")
    public CommonResult queryRecordSybData(@RequestBody ListUploadDto dto) {
        List<ListUploadVo> list = listUploadService.queryRecordSybData(dto);
        return CommonResult.success(list);
    }

    @RequestMapping("/queryDetailData")
    public CommonResult<CommonPage> queryDetailData(@RequestBody ListUploadDto dto) {
        List<ListUploadVo> list = listUploadService.queryDetailData(dto);
        return CommonResult.success(CommonPage.restPage(list));
    }

    @RequestMapping("/queryDetailSybData")
    public CommonResult<CommonPage> queryDetailSybData(@RequestBody ListUploadDto dto) {
        List<ListUploadVo> list = listUploadService.queryDetailSybData(dto);
        return CommonResult.success(CommonPage.restPage(list));
    }

    /**
     * 查询修改状态数据
     * @param dto
     * @return
     */
    @RequestMapping("/queryUpdateTypeData")
    public CommonResult<CommonPage> queryUpdateTypeData(@RequestBody ListUploadDto dto) {
        List<ListUploadVo> list = listUploadService.queryUpdateTypeData(dto);
        return CommonResult.success(CommonPage.restPage(list));
    }

    /**
     * 查询上传后数据
     * @param dto
     * @return
     */
    @RequestMapping("/queryUploadedData")
    public CommonResult<CommonPage> queryUploadedData(@RequestBody ListUploadDto dto) {
        List<ListUploadVo> list = listUploadService.queryUploadedData(dto);
        return CommonResult.success(CommonPage.restPage(list));
    }

    /**
     * 查询上传市医保后数据
     * @param dto
     * @return
     */
    @RequestMapping("/queryUploadedSybData")
    public CommonResult<CommonPage> queryUploadedSybData(@RequestBody ListUploadDto dto) {
        List<ListUploadVo> list = listUploadService.queryUploadedSybData(dto);
        return CommonResult.success(CommonPage.restPage(list));
    }

    /**
     * 查询清单信息（4003接口）
     * @param dto
     * @return
     */
    @RequestMapping("/querySettleListInfo")
    public CommonResult querySettleListInfo(@RequestBody ListUploadDto dto) {
        Map<String, Object> data = listUploadService.querySettleListInfo(dto);
        return CommonResult.success(data);
    }

    /**
     * 统计失败结算清单的类型
     * @return
     */
    @RequestMapping("/queryUploadFalseData")
    public CommonResult queryUploadFalseData(@RequestBody ListUploadDto dto) {
        List<UploadFalseVo> data = listUploadService.queryUploadFalseData(dto);
        return CommonResult.success(data);
    }

    /**
     * 批量上传清单
     * @return
     */
    @RequestMapping("/batchListUpload")
    public CommonResult batchListUpload(@RequestParam("file") MultipartFile file) throws IOException {
        Map<String, Object> res = listUploadService.batchListUpload(file);
        return CommonResult.success(res);
    }


    /**
     * 修改清单状态 stas_type
     * @return
     */
    @RequestMapping("/modifySettleListType")
    public CommonResult modifySettleListType(@RequestBody ListUploadDto dto) throws IOException {
        listUploadService.modifySettleListType(dto);
        return CommonResult.success();
    }

    /**
     * 清单撤回
     * @return
     */
    @RequestMapping("/listWithdrawn")
    public CommonResult listWithdrawn(@RequestBody ListUploadDto dto) throws IOException {
        listUploadService.listWithdrawn(dto);
        return CommonResult.success();
    }

/*
    */
/**
     * 抽取4103查询已经上传的结算清单
     * @param dto
     * @return
     * @throws IOException
     */
    @RequestMapping("/extractUplodadeList")
    public CommonResult extractUplodadeList (@RequestBody ListUploadedQueryDto dto) throws IOException{
        listUploadService.queryUploadedlist(dto);
        return CommonResult.success();
    }

    /**
     * 政策调整
     * @param dto
     * @return
     */
    @RequestMapping("/policyAdjustments")
    public CommonResult policyAdjustments (@RequestBody DataHandleCommonDto dto) {
        listUploadService.policyAdjustments(dto);
        return CommonResult.success();
    }

    /**
     * 查询编码对照
     * @param dto
     * @return
     */
    @PostMapping(value = "/codingControls")
    public CommonResult<CommonPage> codingControls(@RequestBody ListUploadDto dto) {
        List<MainCode> list =  listUploadService.codingControls(dto);
        return CommonResult.success(CommonPage.restPage(list));

    }

    /**
     * 取消标记
     * @param dto
     * @return
     */
    @PostMapping(value = "/revokeTheidentity")
    public CommonResult revokeTheidentity(@RequestBody ListUploadDto dto) {
        int num = listUploadService.revokeTheidentity(dto);
        return CommonResult.success(num);
    }

    /**
     * 撤回数据
     * @param dto
     * @return
     */
    @PostMapping(value = "/dataWithdrawal")
    public CommonResult dataWithdrawal(@RequestBody ListUploadDto dto) {
        //先查询撤回的数据是否已经修改提交
        listUploadService.selectStateUpdate(dto);
        int num = listUploadService.dataWithdrawal(dto);
        return CommonResult.success(num);
    }
///**
//     *
//     * 查询4103已抽取的结算清单数据
//     * @param param
//     * @return
//     * @throws IOException
//     */
//
//    @RequestMapping("/queryUplodadeList")
//    public CommonResult queryUplodadeList (SettleListMainInfoQueryParam param,
//                                           @RequestParam(value = "pageSize", defaultValue = "200") Integer pageSize,
//                                           @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum) throws IOException{
//        List<BusSettleListMainInfo> settleList =listUploadService.queryExtractedUploadedlist(param,pageSize,pageNum);
//        return CommonResult.success(CommonPage.restPage(settleList));
//    }
//
///**
//     * 根据结算id查询结算清单信息
//     * @param dto
//     * @return
//     */
//
//    @RequestMapping(value = "/queryUplodadeListAllinfo" ,method = RequestMethod.POST)
//    public CommonResult queryUplodadeListAllinfo (@RequestBody UplodListDto dto) {
//        AllBusSettleListInfo allBusSettleListInfo = listUploadService.queryUplodadeListAllinfo(dto.getId());
//        return CommonResult.success(allBusSettleListInfo);
//    }

}
