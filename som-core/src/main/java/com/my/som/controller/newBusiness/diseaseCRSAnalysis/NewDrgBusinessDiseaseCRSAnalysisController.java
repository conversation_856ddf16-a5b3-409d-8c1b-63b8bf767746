package com.my.som.controller.newBusiness.diseaseCRSAnalysis;

import com.my.som.common.api.CommonResult;
import com.my.som.dto.newBusiness.diseaseCRSAnalysis.NewDrgBusinessDiseaseCRSAnalysisDto;
import com.my.som.service.newBusiness.diseaseCRSAnalysis.NewDrgBusinessDiseaseCRSAnalysisService;
import com.my.som.vo.newBusiness.diseaseCRSAnalysis.NewDrgBusinessDiseaseCRSAnalysisVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/newDrgBusinessDiseaseCRSAnalysisController")
public class NewDrgBusinessDiseaseCRSAnalysisController {

    @Autowired
    private NewDrgBusinessDiseaseCRSAnalysisService newDrgBusinessDiseaseCRSAnalysisService;

    @PostMapping("/queryDiseaseCRSData")
    public CommonResult queryDiseaseCRSData(NewDrgBusinessDiseaseCRSAnalysisDto dto) {
        List<NewDrgBusinessDiseaseCRSAnalysisVo> listMap = newDrgBusinessDiseaseCRSAnalysisService.queryDiseaseCRSData(dto);
        return CommonResult.success(listMap);
    };
}
