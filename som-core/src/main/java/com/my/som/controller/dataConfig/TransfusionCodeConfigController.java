package com.my.som.controller.dataConfig;

import com.my.som.common.api.CommonPage;
import com.my.som.common.api.CommonResult;
import com.my.som.common.util.ExcelUtil;
import com.my.som.dto.dataConfig.NurseCodeDto;
import com.my.som.dto.dataConfig.TransfusionCodeDto;
import com.my.som.service.dataConfig.NurseCodeConfigService;
import com.my.som.service.dataConfig.TransfusionCodeConfigService;
import com.my.som.vo.dataConfig.NurseCodeVo;
import com.my.som.vo.dataConfig.TransfusionCodeVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * @author: zyd
 */
@RestController
@RequestMapping("/TransfusionCodeConfigController")
public class TransfusionCodeConfigController {
    @Autowired
    private TransfusionCodeConfigService transfusionCodeConfigService;

    /**
     * 查询输血代码信息
     * @param dto
     * @return
     */
    @RequestMapping("/queryTransfusionCodeInfo")
    public CommonResult queryTransfusionCodeInfo(TransfusionCodeDto dto){
        List<TransfusionCodeVo> list = transfusionCodeConfigService.queryTransfusionCodeInfo(dto);
        return CommonResult.success(CommonPage.restPage(list));
    }

    /**
     * 删除指定输血代码信息
     */
    @RequestMapping("/deleteTransfusionCodeInfo")
    public CommonResult deleteTransfusionCodeInfo(TransfusionCodeDto dto) {
        transfusionCodeConfigService.deleteTransfusionCodeInfo(dto);
        return CommonResult.success();
    }

    /**
     * 添加输血代码信息
     */
    @RequestMapping("/insertTransfusionCodeInfo")
    public CommonResult insertTransfusionCodeInfo(TransfusionCodeDto dto) {
        transfusionCodeConfigService.insertTransfusionCodeInfo(dto);
        return CommonResult.success();
    }

    /**
     * 修改输血代码信息
     */
    @RequestMapping("/updateTransfusionCodeInfo")
    public CommonResult updateTransfusionCodeInfo(TransfusionCodeDto dto) {
        transfusionCodeConfigService.updateTransfusionCodeInfo(dto);
        return CommonResult.success();
    }

    /**
     * 文件上传
     */
    @RequestMapping("/transfusionCodeUpload")
    public CommonResult transfusionCodeUpload(@RequestParam("file") MultipartFile file, TransfusionCodeDto dto) {
        transfusionCodeConfigService.transfusionCodeUpload(file, dto);
        return CommonResult.success();
    }

    /**
     * 模板下载
     * @param response
     * @return
     */
    @RequestMapping("/downTransfusionCodeTemplate")
    public CommonResult downTransfusionCodeTemplate(HttpServletResponse response){
        ExcelUtil.exportTemplateToWeb(response,"输血编码对照", "classpath:templates/transfusionCodeConfigTemplate.xlsx");
        return CommonResult.success();
    }
}
