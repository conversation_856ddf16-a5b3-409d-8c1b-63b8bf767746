package com.my.som.controller.zhongYangApi;

import com.my.som.common.api.CommonPage;
import com.my.som.common.api.CommonResult;
import com.my.som.common.exception.AppException;
import com.my.som.common.vo.SomBackUser;
import com.my.som.controller.BaseController;
import com.my.som.dto.pregroup.PreGroup2Dto;
import com.my.som.dto.zhongYangApi.Get2_2_3Dto;
import com.my.som.dto.zhongYangApi.ZhongYangApiDto;
import com.my.som.service.pregroup.PreGroupServiceInterface;
import com.my.som.service.zhongYangApi.ZhongYangApiService;
import com.my.som.vo.pregroup.PreGroupVo;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/zhongyangApi")
public class ZhongYangApiController extends BaseController{
    @Resource
    private ZhongYangApiService zhongYangApiService;


    @PostMapping("/get")
    @ResponseBody
    public CommonResult get(@RequestBody ZhongYangApiDto zhongYangApiDto){
        zhongYangApiService.get(zhongYangApiDto);
        return CommonResult.success();
    }

    /**
     * 调用2_23_1获取在院病人病案首页信息
     * @param zhongYangApiDto
     * @return
     */
    @RequestMapping("/get2_23_1")
    public CommonResult get2_23_1(@RequestBody ZhongYangApiDto zhongYangApiDto){
        zhongYangApiService.get2_23_1(zhongYangApiDto);
        return CommonResult.success();
    }

    /**
     * 调用2_23_3根据病案号获取病案首页信息
     * @param zhongYangApiDto
     * @return
     */

    public CommonResult get2_23_3(@RequestBody ZhongYangApiDto zhongYangApiDto){
        zhongYangApiService.get2_23_3(zhongYangApiDto);
        return CommonResult.success();
    }

    /**
     * 根据入院时间获取在院患者信息
     * @param zhongYangApiDto
     * @return
     */
    @RequestMapping("/get2_2_3")
    @ResponseBody
    public CommonResult<CommonPage<Get2_2_3Dto>> get2_2_3(@RequestBody ZhongYangApiDto zhongYangApiDto){
        SomBackUser userInfo = getUserInfo();
        if (userInfo.getBlngOrgOrgId() == userInfo.getHospitalId() || userInfo.getBlngOrgOrgId() == null){
            throw new AppException("当前登录用户不是医生或科室权限不能查看");
        }
        zhongYangApiDto.setDeptId(Long.parseLong(userInfo.getBlngOrgOrgId()));
        List<Get2_2_3Dto> zhongYangApiService223 = zhongYangApiService.get2_2_3(zhongYangApiDto);
        return CommonResult.success(CommonPage.restPage(zhongYangApiService223));
    }


    /**
     * 根据出入院时间获取病案首页信息  批量抽取
     * @param zhongYangApiDto
     * @return
     */
    @RequestMapping("/get2_23_4")
    @ResponseBody
    public CommonResult get2_23_4(@RequestBody ZhongYangApiDto zhongYangApiDto){
        zhongYangApiService.get2_23_4(zhongYangApiDto);
        return CommonResult.success();
    }

    /**
     * 调用2_27_1_5 根据结算清单获取主键
     * @param zhongYangApiDto
     * @return
     */
    @RequestMapping("/get2_27_1_5")
    @ResponseBody
    public CommonResult get2_27_1_5(@RequestBody ZhongYangApiDto zhongYangApiDto){
        zhongYangApiService.get2_27_1_5(zhongYangApiDto);
        return CommonResult.success();
    }


    /**
     * 预分组
     * @param zhongYangApiDto
     * @return
     */
        @RequestMapping("/preGroupSettle")
    @ResponseBody
    public CommonResult preGroupSettle(@RequestBody ZhongYangApiDto zhongYangApiDto,HttpServletResponse response,HttpServletRequest request){
            PreGroupVo preGroupVo = zhongYangApiService.preGroupSettle(zhongYangApiDto, response, request);
            Map<String, Object> map = new HashMap<>();
            map.put("url",preGroupVo.getImageUrl());
            return CommonResult.success(map);
    }
    /**
     * 预分组
     * @param zhongYangApiDto
     * @return
     */
    @RequestMapping("/mdcsInfoExtractOneByJzid")
    @ResponseBody
    public CommonResult mdcsInfoExtractOneByJzid(@RequestBody ZhongYangApiDto zhongYangApiDto){
        zhongYangApiService.mdcsInfoExtractByApai(zhongYangApiDto);
        return CommonResult.success();
    }

}
