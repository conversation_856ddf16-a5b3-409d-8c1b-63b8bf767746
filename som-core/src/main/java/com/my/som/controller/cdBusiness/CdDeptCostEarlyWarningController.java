package com.my.som.controller.cdBusiness;

import com.my.som.common.api.CommonResult;
import com.my.som.dto.cdBusiness.CdDeptCostEarlyWarningDto;
import com.my.som.service.cdBusiness.CdDeptCostEarlyWarningService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @author: zyd
 * @Date 2021/8/30 11:04 上午
 * @Version 1.0
 * @Description: 成都费用预警
 */
@RestController
@RequestMapping("/cdDeptCostEarlyWarningController")
public class CdDeptCostEarlyWarningController {

    @Autowired
    private CdDeptCostEarlyWarningService cdDeptCostEarlyWarningService;

    /**
     * 获取数据
     * @param dto 参数
     * @return
     */
    @RequestMapping("/getList")
    public CommonResult getList(CdDeptCostEarlyWarningDto dto){
        return CommonResult.success(cdDeptCostEarlyWarningService.getList(dto));
    }
}
