package com.my.som.controller.dataConfig;

import com.my.som.common.api.CommonPage;
import com.my.som.common.api.CommonResult;
import com.my.som.common.util.ExcelUtil;
import com.my.som.dto.dataConfig.DoctorCodeDto;
import com.my.som.service.dataConfig.DoctorCodeConfigService;
import com.my.som.vo.dataConfig.DrCodgVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import com.my.som.controller.BaseController;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

@RestController
@RequestMapping("/DoctorCodeConfigController")
public class DoctorCodeConfigController extends BaseController{
    @Autowired
    private DoctorCodeConfigService doctorCodeConfigService;

    /**
     * 查询医师代码信息
     * @param dto
     * @return
     */
    @RequestMapping("/queryDoctorCodeInfo")
    public CommonResult queryAreaBenchmarkInfo(DoctorCodeDto dto){
        List<DrCodgVo> list = doctorCodeConfigService.queryDoctorCodeInfo(dto);
        return CommonResult.success(CommonPage.restPage(list));
    }

    /**
     * 查询医师代码信息
     * @param dto
     * @return
     */
    @RequestMapping("/deleteDoctorCodeInfo")
    public CommonResult deleteDoctorCodeInfo(DoctorCodeDto dto){
        doctorCodeConfigService.deleteDoctorCodeInfo(dto);
        return CommonResult.success();
    }

    /**
     * 查询医师代码信息
     * @param dto
     * @return
     */
    @RequestMapping("/updateDoctorCodeInfo")
    public CommonResult updateDoctorCodeInfo(DoctorCodeDto dto){
        doctorCodeConfigService.updateDoctorCodeInfo(dto);
        return CommonResult.success();
    }

    /**
     * 新增医师代码信息
     * @param dto
     * @return
     */
    @RequestMapping("/insertDoctorCodeInfo")
    public CommonResult insertDoctorCodeInfo(DoctorCodeDto dto){
        dto.setHospitalId(getUserBaseInfo().getHospitalId());
        doctorCodeConfigService.insertDoctorCodeInfo(dto);
        return CommonResult.success();
    }

    /**
     * 医师代码信息上传
     * @param file
     * @return
     */
    @RequestMapping("/doctorCodeUpload")
    public CommonResult doctorCodeUpload(@RequestParam("file") MultipartFile file, DoctorCodeDto dto){
        doctorCodeConfigService.doctorCodeUpload(file, dto);
        return CommonResult.success();
    }

    /**
     * 医师代码信息模板下载
     * @param response
     * @return
     */
    @RequestMapping("/downDoctorCodeTemplate")
    public CommonResult downDoctorCodeTemplate(HttpServletResponse response){
        ExcelUtil.exportTemplateToWeb(response,"医师编码对照", "classpath:templates/doctorCodeConfigTemplate.xlsx");
        return CommonResult.success();
    }
}
