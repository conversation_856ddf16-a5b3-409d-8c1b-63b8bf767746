package com.my.som.controller.cnGroupController;


import com.my.som.controller.BaseController;
import com.my.som.service.cnBusiness.CNGroupService;
import com.my.som.vo.cnBusiness.CNGroupDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;


/**
 * CN分组结果
 * Created by sky on 2022/3/9.
 */
@Controller
@Api(tags = "CNGroupController", description = "CN分组结果")
@RequestMapping("/cnGroup")
public class CNGroupController extends BaseController {
    @Autowired
    private CNGroupService cnGroupService;

    /**
     * 执行CN分组
     */
    @ApiOperation("CN分组结果")
    @RequestMapping(value = "/group", method = RequestMethod.POST)
    @ResponseBody
    public void cnGroup() {
        CNGroupDto dto = new CNGroupDto();
        dto.setBegnDate("2023-06-01");
        dto.setExpiDate("2023-06-01");
        cnGroupService.CNGroup(dto);
    }
}
