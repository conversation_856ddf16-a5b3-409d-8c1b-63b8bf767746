package com.my.som.controller.dataHandle;

import com.my.som.common.api.CommonResult;
import com.my.som.common.constant.DrgConst;
import com.my.som.controller.BaseController;
import com.my.som.model.dataHandle.SomMedcasInGroupCfg;
import com.my.som.service.dataHandle.MedicelIngroupConfigService;
import com.my.som.common.vo.SysUserBase;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 病案入组配置Controller
 * Created by sky on 2020/3/27.
 */
@Controller
@Api(tags = "MedicelIngroupConfigController", description = "病案入组配置")
@RequestMapping("/medicalIngroupConfig")
public class MedicelIngroupConfigController extends BaseController {
    @Autowired
    private MedicelIngroupConfigService medicelIngroupConfigService;

    @ApiOperation("查询病案入组配置")
    @RequestMapping(value = "/getList", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<List<SomMedcasInGroupCfg>> list(@RequestParam(value = "errorType", required = false) String errType) {
        List<SomMedcasInGroupCfg> logList = medicelIngroupConfigService.list(errType);
        return CommonResult.success(logList);
    }

    @ApiOperation("启用或者停用某一规则")
    @PostMapping(value="/updateState")
    @ResponseBody
    public CommonResult updateState(@RequestBody SomMedcasInGroupCfg somMedcasInGroupCfg) {
        SysUserBase sysUserBase = getUserBaseInfo();
        if(DrgConst.ORG_TYPE_1.equals(sysUserBase.getMainOrgType())){ //医疗机构用户
            return CommonResult.success(medicelIngroupConfigService.updateState(somMedcasInGroupCfg));
        }else{
            return CommonResult.failed("抱歉，您无权限修改该规则状态！");
        }
    }
}
