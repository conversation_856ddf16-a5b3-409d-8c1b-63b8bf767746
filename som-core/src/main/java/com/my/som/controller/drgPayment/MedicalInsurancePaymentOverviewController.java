package com.my.som.controller.drgPayment;

import com.my.som.common.api.CommonResult;
import com.my.som.common.util.BeanUtil;
import com.my.som.controller.BaseController;
import com.my.som.dto.drgPayment.DrgPaymentQueryParam;
import com.my.som.service.drgPayment.MedicalInsurancePaymentOverviewService;
import com.my.som.vo.drgPayment.PaymentOverviewMonthCountInfo;
import com.my.som.vo.drgPayment.PaymentOverviewTopCountInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;


/**
 * 医保付费总览Controller
 * Created by sky on 2020/8/27.
 */
@Controller
@Api(tags = "MedicalInsurancePaymentOverviewController", description = "医保付费总览")
@RequestMapping("/medicalInsurancePaymentOverview")
public class MedicalInsurancePaymentOverviewController extends BaseController {
    @Autowired
    private MedicalInsurancePaymentOverviewService medicalInsurancePaymentOverviewService;

    @ApiOperation("查询医保付费费用、人次整体统计信息")
    @RequestMapping(value = "/getTopCountInfo", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<PaymentOverviewTopCountInfo> getTopCountInfo(DrgPaymentQueryParam queryParam) {
//        BeanUtil.copyPropertiesIgnoreNull(getUserBaseInfo(), queryParam);
        PaymentOverviewTopCountInfo topCountInfo = medicalInsurancePaymentOverviewService.getTopCountInfo(queryParam);
        return CommonResult.success(topCountInfo);
    }

    @ApiOperation("查询医保付费费用、人次每月统计情况(柱状图）")
    @RequestMapping(value = "/getBarEchartInfo", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<PaymentOverviewMonthCountInfo> getBarEchartInfo(DrgPaymentQueryParam queryParam) {
//        BeanUtil.copyPropertiesIgnoreNull(getUserBaseInfo(), queryParam);
        PaymentOverviewMonthCountInfo paymentOverviewMonthCountInfo = medicalInsurancePaymentOverviewService.getBarEchartInfo(queryParam);
        return CommonResult.success(paymentOverviewMonthCountInfo);
    }

}
