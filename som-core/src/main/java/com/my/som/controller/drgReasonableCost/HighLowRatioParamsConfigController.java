package com.my.som.controller.drgReasonableCost;

import com.my.som.common.api.CommonResult;
import com.my.som.common.util.BeanUtil;
import com.my.som.controller.BaseController;
import com.my.som.dto.drgReasonableCost.DrgReasonableCostQueryParam;
import com.my.som.service.drgReasonableCost.HighLowRatioParamsConfigService;
import com.my.som.vo.drgReasonableCost.HighLowRatioParamsConfigVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;


/**
 * 高低费率参数配置Controller
 * Created by sky on 2020/10/26.
 */
@Controller
@Api(tags = "HighLowRatioParamsConfigController", description = "高低费率参数配置")
@RequestMapping("/highLowRatioParamsConfig")
public class HighLowRatioParamsConfigController extends BaseController {
    @Autowired
    private HighLowRatioParamsConfigService highLowRatioParamsConfigService;

    @ApiOperation("获取高低费率参数")
    @RequestMapping(value = "/getAllParams", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<HighLowRatioParamsConfigVo> getAllParams(DrgReasonableCostQueryParam queryParam) {
        BeanUtil.copyPropertiesIgnoreNull(getUserBaseInfo(), queryParam);
        HighLowRatioParamsConfigVo highLowRatioParamsConfigVo = highLowRatioParamsConfigService.getAllParams(queryParam);
        return CommonResult.success(highLowRatioParamsConfigVo);
    }

    @ApiOperation("更新高低费率参数")
//    @PreAuthorize("hasAuthority('highLowRatio:params:update')")
    @RequestMapping(value = "/updateAllInfo", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult updateAllInfo(@RequestBody HighLowRatioParamsConfigVo listQuery, BindingResult bindingResult) {
        int count = highLowRatioParamsConfigService.updateAllInfo(listQuery);
        if (count > 0) {
            return CommonResult.success(count);
        } else {
            return CommonResult.failed("操作失败！（未修改任何数据）");
        }
    }
}
