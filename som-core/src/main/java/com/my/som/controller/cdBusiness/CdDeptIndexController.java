package com.my.som.controller.cdBusiness;

import com.my.som.common.api.CommonPage;
import com.my.som.common.api.CommonResult;
import com.my.som.common.util.BeanUtil;
import com.my.som.controller.BaseController;
import com.my.som.dto.cdBusiness.CdAnalysisDto;
import com.my.som.dto.cdBusiness.CdBusinessQueryDto;
import com.my.som.dto.dipBusiness.DipBusinessQueryDto;
import com.my.som.service.cdBusiness.CdDeptIndexService;
import com.my.som.service.dipBusiness.DipDeptIndexService;
import com.my.som.vo.cdBusiness.CdDeptCountVo;
import com.my.som.vo.cdBusiness.CdDeptIndexVo;
import com.my.som.vo.dipBusiness.DipDeptCountVo;
import com.my.som.vo.dipBusiness.DipDeptIndexVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;

/**
 * 成都分组科室指标Controller
 * Created by sky on 2020/7/13.
 */
@Controller
@Api(tags = "CdDeptIndexController", description = "科室病组指标")
@RequestMapping("/cdDeptIndex")
public class CdDeptIndexController extends BaseController {
    @Autowired
    private CdDeptIndexService cdDeptIndexService;

    @ApiOperation("查询科室病组指标信息")
    @RequestMapping(value = "/getList", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<CommonPage<CdDeptIndexVo>> list(CdBusinessQueryDto queryParam,
                                                        @RequestParam(value = "pageSize", defaultValue = "200") Integer pageSize,
                                                        @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum) {
//        BeanUtil.copyPropertiesIgnoreNull(getUserBaseInfo(), queryParam);
        List<CdDeptIndexVo> cdDeptIndexVos = cdDeptIndexService.list(queryParam, pageSize, pageNum);
        return CommonResult.success(CommonPage.restPage(cdDeptIndexVos));
    }

    @ApiOperation("查询科室病组指标统计信息")
    @RequestMapping(value = "/getCountInfo", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<List<CdDeptCountVo>> getCountInfo(CdBusinessQueryDto queryParam) {
//        BeanUtil.copyPropertiesIgnoreNull(getUserBaseInfo(), queryParam);
        List<CdDeptCountVo> cdDeptCountVos = cdDeptIndexService.getCountInfo(queryParam);
        return CommonResult.success(cdDeptCountVos);
    }

    @ApiOperation("查询指数详情信息")
    @RequestMapping(value = "/queryConsumptionIndex", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult queryConsumptionIndex(CdBusinessQueryDto queryParam) {
//        BeanUtil.copyPropertiesIgnoreNull(getUserBaseInfo(), queryParam);
        List<CdDeptIndexVo> cdDeptIndexVos = cdDeptIndexService.queryConsumptionIndex(queryParam);
        return CommonResult.success(cdDeptIndexVos);
    }
}
