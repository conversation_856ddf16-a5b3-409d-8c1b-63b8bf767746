package com.my.som.controller.drgReasonableCost;

import com.my.som.common.api.CommonPage;
import com.my.som.common.api.CommonResult;
import com.my.som.common.util.BeanUtil;
import com.my.som.controller.BaseController;
import com.my.som.dto.drgReasonableCost.DrgReasonableCostQueryParam;
import com.my.som.service.drgReasonableCost.DrgPayAdverseService;
import com.my.som.vo.drgReasonableCost.DrgPayAdverseVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;

/**
 * DRG付费逆差Controller
 * Created by sky on 2020/10/24.
 */
@Controller
@Api(tags = "DrgPayAdverseController", description = "DRG付费逆差")
@RequestMapping("/drgPayAdverse")
public class DrgPayAdverseController extends BaseController {
    @Autowired
    private DrgPayAdverseService drgPayAdverseService;

    @ApiOperation("获取DRG付费逆差数据")
    @RequestMapping(value = "/mainInfoList", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<CommonPage<DrgPayAdverseVo>> list(DrgReasonableCostQueryParam queryParam,
                                                          @RequestParam(value = "pageSize", defaultValue = "200") Integer pageSize,
                                                          @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum) {
        BeanUtil.copyPropertiesIgnoreNull(getUserBaseInfo(), queryParam);
        List<DrgPayAdverseVo> drgPayAdverseVoList = drgPayAdverseService.list(queryParam, pageSize, pageNum);
        return CommonResult.success(CommonPage.restPage(drgPayAdverseVoList));
    }


}
