package com.my.som.controller.dataHandle;

import com.my.som.common.api.CommonResult;
import com.my.som.controller.BaseController;
import com.my.som.service.dataHandle.DataHandleProcessService;
import com.my.som.common.vo.SysUserBase;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;


/**
 *调用流程引擎处理数据Controller
 * Created by sky on 2020/04/26.
 */
@Controller
@Api(tags = "DataHandleProcessController", description = "数据处理过程")
@RequestMapping("/dataHandleProcess")
public class DataHandleProcessController extends BaseController {
    @Autowired
    private DataHandleProcessService dataHandleProcessService;

    @ApiOperation("数据流程入口")
    @RequestMapping(value = "/runPro", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult runPro(@RequestParam("logId") Long logId) {
        SysUserBase sysUserBase =  getUserBaseInfo();
        try{
            dataHandleProcessService.runPro(logId,sysUserBase);
            return CommonResult.success();
        }catch (Exception e){
            return CommonResult.failed("数据处理调用出现异常！");
        }
    }
}
