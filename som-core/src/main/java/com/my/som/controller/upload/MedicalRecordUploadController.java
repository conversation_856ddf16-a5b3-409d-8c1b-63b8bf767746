package com.my.som.controller.upload;

import com.my.som.common.api.CommonResult;
import com.my.som.dto.upload.MedicalRecordUploadDto;
import com.my.som.entity.upload.MedType;
import com.my.som.service.upload.MedicalRecordUploadService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

/**
 * @author: zyd
 * @Date 2021/8/23 1:41 下午
 * @Version 1.0
 * @Description: 病案数据上传
 */
@RestController
@RequestMapping("/MedicalRecordUploadController")
public class MedicalRecordUploadController {

    @Autowired
    private MedicalRecordUploadService medicalRecordUploadService;

    private final Object LOCK = new Object();

    /**
     * 病案数据上传
     * @param file excel 文件
     * @return
     * @throws IOException
     */
    @RequestMapping("/medicalRecordUpload")
    public CommonResult medicalRecordUpload(@RequestParam("file") MultipartFile file, MedicalRecordUploadDto dto) throws IOException {
        medicalRecordUploadService.analysis(file, dto);
        return CommonResult.success();
    }


    /**
     * 病例结算类型上传
     * @param file excel 文件
     * @return
     * @throws IOException
     */
    @RequestMapping("/medicalTypeUpload")
    public CommonResult medicalTypeUpload(@RequestParam("file") MultipartFile file){
        synchronized (LOCK){
            List<MedType> medicalTypes = medicalRecordUploadService.medicalTypeUpload(file);
            return CommonResult.success(medicalTypes);
        }
    }

}
