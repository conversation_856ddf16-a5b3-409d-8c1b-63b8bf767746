package com.my.som.controller.pregroup;

import com.alibaba.fastjson.JSONObject;
import com.my.som.app.util.IPushUtil;
import com.my.som.common.api.CommonMapResult;
import com.my.som.common.api.CommonResult;
import com.my.som.common.util.ValidateUtil;
import com.my.som.dto.patienInfo.DataGroupInfo;
import com.my.som.dto.patienInfo.PatienInfo;
import com.my.som.dto.patienInfo.PatienInfoByHis;
import com.my.som.dto.pregroup.PreGroup2Dto;
import com.my.som.dto.pregroup.PreGroupBasicDto;
import com.my.som.dto.pregroup.PreGroupDto;
import com.my.som.service.medicalQuality.SettleListManageService;
import com.my.som.service.pregroup.PreGroupServiceInterface;
import com.my.som.vo.pregroup.PreGroupVo;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.Map;

/**
 * @author: zyd
 * @Date 2021/7/20 6:56 下午
 * @Version 1.0
 * @Description: 预分组控制
 */
@Slf4j
@RestController
@RequestMapping("/preAiPaymentPrediction")
public class PreAiPaymentPredictionController {


    @Resource
    private PreGroupServiceInterface preGroupServiceInterface;

    @Autowired
    private SettleListManageService settleListManageService;
    /**
     * 获取预分组结果图片流
     *
     * @param dto
     * @param response
     */
    @RequestMapping("/dipPreGroupMedical")
    public void getPreImg(@RequestBody PreGroupDto dto, HttpServletResponse response) {
        preGroupServiceInterface.writeImg(dto, response);
    }

    /**
     * 预分组返回 url
     *
     * @param dto
     * @param response
     * @param request
     * @return
     */
    @RequestMapping("/preGroupMedical")
    public CommonMapResult preGroupMedical(@RequestBody PreGroupDto dto, HttpServletResponse response, HttpServletRequest request) {
        Map<String, Object> map = new HashMap<>();
        log.info("预分组传参：" + JSONObject.toJSON(dto).toString());
        map.put("url", preGroupServiceInterface.getPreGroupInfo4(dto, response, request).getImageUrl());
        return CommonMapResult.ok(map);
    }

    /**
     * 预分组返回 url
     *
     * @param dto
     * @param response
     * @param request
     * @return
     */
    @RequestMapping("/preGroupSettle")
    public CommonMapResult preGroupSettle(@RequestBody PreGroup2Dto dto, HttpServletResponse response, HttpServletRequest request) {
        Map<String, Object> map = new HashMap<>();
        //新增打印
        log.info("预分组传参：" + JSONObject.toJSON(dto).toString());
        map.put("url", preGroupServiceInterface.getPreGroupInfo5(dto, response, request).getImageUrl());
        return CommonMapResult.ok(map);
    }

//    /**
//     * 旧系统接口预分组返回 url
//     *
//     * @param dto
//     * @param response
//     * @param request
//     * @return
//     */
//    @RequestMapping("/preGroup")
//    public CommonMapResult preGroup(@RequestBody PreGroupDto dto, HttpServletResponse response, HttpServletRequest request) {
//        Map<String, Object> map = new HashMap<>();
//        map.put("url", preGroupServiceInterface.getPreGroupInfo4(dto, response, request).getImageUrl());
////        PreGroup2Dto dto2 = preGroupServiceInterface.convertToPreGroup2Dto(dto);
////        map.put("url", preGroupServiceInterface.getPreGroupInfo5(dto2, response, request).getImageUrl());
//        return CommonMapResult.ok(map);
//    }

    /**
     * 预分组返回 url(基层医疗机构)
     *
     * @param dto
     * @param response
     * @param request
     * @return
     */
    @RequestMapping("/preGroupBasic")
    public CommonMapResult preGroupBasic(@RequestBody PreGroupBasicDto dto, HttpServletResponse response, HttpServletRequest request) {
        Map<String, Object> map = new HashMap<>();
        log.info("预分组传参：" + JSONObject.toJSON(dto).toString());
        map.put("url", preGroupServiceInterface.getPreGroupInfoByBasicDto(dto, response, request).getImageUrl());
        return CommonMapResult.ok(map);
    }

    /**
     * 检查是否可用
     *
     * @param json
     * @param response
     * @param request
     * @return
     */
    @RequestMapping("/check")
    public CommonMapResult check(@RequestBody String json, HttpServletResponse response, HttpServletRequest request) {
        Map<String, Object> map = new HashMap<>();
        PreGroupDto parse = JSONObject.parseObject(json, PreGroupDto.class);
        map.put("url", preGroupServiceInterface.check(json, response, request).getImageUrl());
        return CommonMapResult.ok(map);
    }

    @ApiOperation("查询模拟预分组结果")
    @PostMapping("/dataGroup")
    @ResponseBody
    public CommonResult preGroupResult(@RequestBody PatienInfoByHis patienInfo, HttpServletRequest request, HttpServletResponse response){
        log.info("预分组传参：" + JSONObject.toJSON(patienInfo).toString());
        DataGroupInfo groups =  settleListManageService.preGroupResult(patienInfo);
        if(!ValidateUtil.isEmpty(groups))
            return CommonResult.success(groups);
        else
            return CommonResult.failed("入组失败,请检查输入信息");
    }
}
