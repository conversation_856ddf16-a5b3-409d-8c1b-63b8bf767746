package com.my.som.controller.dipBusiness;

import com.my.som.common.api.CommonResult;
import com.my.som.controller.BaseController;
import com.my.som.dto.dipBusiness.HospitalAnalysisDto;
import com.my.som.service.dipBusiness.DipHospitalCoefficientService;
import com.my.som.vo.dipBusiness.DipDeptIndexVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 医院系数
 */
@RestController
@RequestMapping("/dipHospitalCoefficientController")
public class DipHospitalCoefficientController extends BaseController {

    @Autowired
    private DipHospitalCoefficientService dipHospitalCoefficientService;

    /**
     * 查询医院系数
     * @param dto
     * @return
     */
    @RequestMapping("/queryHospitalCoefficient")
    public CommonResult queryHospitalCoefficient(HospitalAnalysisDto dto){
        dto.setHospitalId(getUserBaseInfo().getHospitalId());
        List<DipDeptIndexVo> vo = dipHospitalCoefficientService.queryHospitalCoefficient(dto);
        return CommonResult.success(vo);
    }
}
