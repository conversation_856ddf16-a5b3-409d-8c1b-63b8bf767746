package com.my.som.controller.common;

import com.my.som.common.api.CommonDictResult;
import com.my.som.common.api.CommonPage;
import com.my.som.common.api.CommonResult;
import com.my.som.common.constant.DrgConst;
import com.my.som.common.util.BeanUtil;
import com.my.som.common.util.DictOperationUtil;
import com.my.som.common.util.ValidateUtil;
import com.my.som.common.vo.DictVo;
import com.my.som.controller.BaseController;
import com.my.som.dto.common.*;
import com.my.som.dto.hospitalAnalysis.HospitalAnalysisQueryParam;
import com.my.som.model.common.SomIcdCrsp;
import com.my.som.service.common.CommonService;
import com.my.som.common.vo.SysUserBase;
import com.my.som.util.SysCommonConfigUtil;
import com.my.som.vo.common.*;
import com.my.som.vo.hospitalAnalysis.DrgsAnalysisInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 公共Controller
 * Created by sky on 2020/3/5.
 */
@Controller
@Api(tags = "DrgCommonController", description = "公共控制处理")
@RequestMapping("/drgCommon")
public class DrgCommonController extends BaseController {
    @Autowired
    private CommonService commonService;

    @ApiOperation("获取科室下拉树以及所有码表下拉列表")
    @RequestMapping(value = "/querySelectTreeAndSelectList", method = RequestMethod.POST)
    @ResponseBody
    public CommonDictResult<List<BusDeptNode>> querySelectTreeAndSelectList(@RequestParam(value = "type", defaultValue = "") String type,
                                                                            @RequestParam(value = "codeKeys", defaultValue = "") String codeKeys){
        return CommonDictResult.success(commonService.getListTree(type,getUserBaseInfo().getHospitalId()), DictOperationUtil.getDictList(codeKeys, DrgConst.TYPE_1));
    }

    /**
     * 获取编码表数据
     * @param codeKeys 码表字段
     * @return
     */
    @RequestMapping(value = "/queryDictionary", method = RequestMethod.POST)
    @CrossOrigin
    @ResponseBody
    public CommonDictResult<Map<String, List<DictVo>>> querySelectTreeAndSelectList(@RequestParam(value = "codeKeys", defaultValue = "") String codeKeys){
        return CommonDictResult.success(commonService.getDictList(codeKeys));
    }

    /**
     * 获取清单编码表数据
     * @param codeKeys 码表字段
     * @return
     */
    @RequestMapping(value = "/querySettleListDictionary", method = RequestMethod.POST)
    @CrossOrigin
    @ResponseBody
    public CommonDictResult<Map<String, List<DictVo>>> querySettleListSelectTreeAndSelectList(@RequestParam(value = "codeKeys", defaultValue = "") String codeKeys){
        return CommonDictResult.success(commonService.getSettleListDictList(codeKeys));
    }

    @ApiOperation("模糊查询ICD下拉数据")
    @RequestMapping(value = "/queryLikeIcdsByPram", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<List<SomIcdCrsp>> queryLikeIcdsByPram(@RequestBody BusIcdQueryParam busIcdQueryParam) {
        busIcdQueryParam.setHospital_id(getUserBaseInfo().getHospitalId());
        List<SomIcdCrsp> busIcds = commonService.queryLikeIcdsByPram(busIcdQueryParam);
        return CommonResult.success(busIcds);
    }

    @ApiOperation("模糊查询DRGs下拉数据")
    @RequestMapping(value = "/queryLikeDrgsByPram", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<List<DrgsSuggestInfo>> queryLikeDrgsByPram(@RequestBody DrgsQueryParam drgsQueryParam) {
        drgsQueryParam.setHospital_id(getUserBaseInfo().getHospitalId());
        List<DrgsSuggestInfo> drgsSuggestInfo = commonService.queryLikeDrgsByPram(drgsQueryParam);
        return CommonResult.success(drgsSuggestInfo);
    }

    @ApiOperation("模糊查询成都分组下拉数据")
    @RequestMapping(value = "/queryLikePpsGroupByPram", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<List<PpsGroupSuggestInfo>> queryLikePpsGroupByPram(@RequestBody DrgsQueryParam drgsQueryParam) {
        drgsQueryParam.setHospital_id(getUserBaseInfo().getHospitalId());
        List<PpsGroupSuggestInfo> ppsGroupSuggestInfo = commonService.queryLikePpsGroupByPram(drgsQueryParam);
        return CommonResult.success(ppsGroupSuggestInfo);
    }

    @ApiOperation("查询各类型医生下拉数据（病案上填写医生）")
    @RequestMapping(value = "/queryMedicalDoctorSelectInput", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<List<SeletInputInfo>> queryMedicalDoctorSelectInput(@RequestBody DoctorQueryParam doctorQueryParam) {
//        BeanUtil.copyPropertiesIgnoreNull(getUserBaseInfo(), doctorQueryParam);
        List<SeletInputInfo> doctorSeletInputInfo = commonService.queryMedicalDoctorSelectInput(doctorQueryParam);
        return CommonResult.success(doctorSeletInputInfo);
    }

    @ApiOperation("查询病组下转详细信息")
    @RequestMapping(value = "/queryDrgDetailList", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<CommonPage<DrgsAnalysisInfo>> queryDrgDetailList(DrgDeatilQueryParam queryParam,
                                                           @RequestParam(value = "pageSize", defaultValue = "200") Integer pageSize,
                                                           @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum) {
//        BeanUtil.copyPropertiesIgnoreNull(getUserBaseInfo(), queryParam);
        if(!ValidateUtil.isEmpty(getUserBaseInfo().getB16c())){
            queryParam.setPriOutHosDeptCode(getUserBaseInfo().getB16c());
        }
        List<DrgsAnalysisInfo> costAnalysisInfoList = commonService.queryDrgDetailList(queryParam, pageSize, pageNum);
        return CommonResult.success(CommonPage.restPage(costAnalysisInfoList));
    }

    @ApiOperation("查询病案下转详细信息")
    @RequestMapping(value = "/queryMedicalDetailList", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<CommonPage<MedicalMainDetailInfo>> queryMedicalDetailList(MedicalDeatilQueryParam queryParam,
                                                                                  @RequestParam(value = "pageSize", defaultValue = "200") Integer pageSize,
                                                                                  @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum) {
//        BeanUtil.copyPropertiesIgnoreNull(getUserBaseInfo(), queryParam);
        if(!ValidateUtil.isEmpty(getUserBaseInfo().getB16c())){
            queryParam.setPriOutHosDeptCode(getUserBaseInfo().getB16c());
        }
        List<MedicalMainDetailInfo> mdicalMainDetailInfoList = commonService.queryMedicalDetailList(queryParam, pageSize, pageNum);
        return CommonResult.success(CommonPage.restPage(mdicalMainDetailInfoList));
    }

    @ApiOperation("查询DRGs组科室数下转详细信息")
    @RequestMapping(value = "/queryDeptDetailList", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<CommonPage<DeptMainDetailInfo>> queryDeptDetailList(MedicalDeatilQueryParam queryParam,
                                                                            @RequestParam(value = "pageSize", defaultValue = "200") Integer pageSize,
                                                                            @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum) {
//        BeanUtil.copyPropertiesIgnoreNull(getUserBaseInfo(), queryParam);
        if(!ValidateUtil.isEmpty(getUserBaseInfo().getB16c())){
            queryParam.setPriOutHosDeptCode(getUserBaseInfo().getB16c());
        }
        List<DeptMainDetailInfo> deptMainDetailInfoList = commonService.queryDeptDetailList(queryParam, pageSize, pageNum);
        return CommonResult.success(CommonPage.restPage(deptMainDetailInfoList));
    }

    @ApiOperation("查询成都分组科室数下转详细信息")
    @RequestMapping(value = "/queryPpsDeptDetailList", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<CommonPage<PpsDeptMainDetailInfo>> queryPpsDeptDetailList(MedicalDeatilQueryParam queryParam,
                                                                            @RequestParam(value = "pageSize", defaultValue = "200") Integer pageSize,
                                                                            @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum) {
//        BeanUtil.copyPropertiesIgnoreNull(getUserBaseInfo(), queryParam);
        if(!ValidateUtil.isEmpty(getUserBaseInfo().getB16c())){
            queryParam.setPriOutHosDeptCode(getUserBaseInfo().getB16c());
        }
        List<PpsDeptMainDetailInfo> ppsDeptMainDetailInfoList = commonService.queryPpsDeptDetailList(queryParam, pageSize, pageNum);
        return CommonResult.success(CommonPage.restPage(ppsDeptMainDetailInfoList));
    }

    @ApiOperation("查询床日付费病案下转详细信息")
    @RequestMapping(value = "/queryMedicalDetailForBedDayCostList", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<CommonPage<MedicalMainDetailForBedDayCostInfo>> queryMedicalDetailForBedDayCostList(MedicalDeatilQueryParam queryParam,
                                                                                  @RequestParam(value = "pageSize", defaultValue = "200") Integer pageSize,
                                                                                  @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum) {
//        BeanUtil.copyPropertiesIgnoreNull(getUserBaseInfo(), queryParam);
        if(!ValidateUtil.isEmpty(getUserBaseInfo().getB16c())){
            queryParam.setPriOutHosDeptCode(getUserBaseInfo().getB16c());
        }
        List<MedicalMainDetailForBedDayCostInfo> medicalMainDetailForBedDayCostInfoList = commonService.queryMedicalDetailForBedDayCostList(queryParam, pageSize, pageNum);
        return CommonResult.success(CommonPage.restPage(medicalMainDetailForBedDayCostInfoList));
    }

    @ApiOperation("查询医保结算清单动态列信息")
    @RequestMapping(value = "/getCols", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<SettletListDynamicColsVo> getCols(HospitalAnalysisQueryParam queryParam) {
//        BeanUtil.copyPropertiesIgnoreNull(getUserBaseInfo(), queryParam);
        SettletListDynamicColsVo settletListDynamicColsVo = commonService.querySettleListDynamicCols(queryParam);
        return CommonResult.success(settletListDynamicColsVo);
    }

    @ApiOperation("模糊查询DIP分组下拉数据")
    @RequestMapping(value = "/queryLikeDipGroupByPram", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<List<DipGroupSuggestInfo>> queryLikeDipGroupByPram(@RequestBody DrgsQueryParam drgsQueryParam) {
        List<DipGroupSuggestInfo> dipGroupSuggestInfo = commonService.queryLikeDipGroupByPram(drgsQueryParam);
        return CommonResult.success(dipGroupSuggestInfo);
    }

    @ApiOperation("获取数据期号")
    @RequestMapping(value = "/queryDataIsuue", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<DataIssueVo> queryDataIsuue() {
        DataIssueVo dataIssueVo = commonService.queryDataIsuue();
        return CommonResult.success(dataIssueVo);
    }

    /**
     * 获取用户信息
     * @return
     */
    @RequestMapping(value = "/queryUserInfo", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<SysUserBase> queryUserInfo() {
//        SysUserBase userInfo = commonService.queryUserInfo();
        SysUserBase baseInfo = commonService.getUserInfo(getUserBaseInfo());
        return CommonResult.success(baseInfo);
    }

    @RequestMapping("/queryDiagnosis")
    @ResponseBody
    public CommonResult<List<DipGroupSuggestInfo>> queryDiagnosis(@RequestBody DrgsQueryParam drgsQueryParam) {
        List<DipGroupSuggestInfo> dipGroupSuggestInfo = commonService.queryDiagnosis(drgsQueryParam);
        return CommonResult.success(dipGroupSuggestInfo);
    }

    @RequestMapping(value = "/queryDIPGroup", method = RequestMethod.POST)
    @ResponseBody
    public CommonResult<List<SeletInputInfo>> queryDIPGroup(DrgsQueryParam drgsQueryParam) {
        List<SeletInputInfo> doctorSeletInputInfo = commonService.queryDIPGroup(drgsQueryParam);
        return CommonResult.success(doctorSeletInputInfo);
    }

    @RequestMapping("/queryHospitalId")
    @ResponseBody
    public CommonResult<List<SeletInputInfo>> queryHospitalId(DrgsQueryParam drgsQueryParam) {
        List<SeletInputInfo> infos = commonService.queryHospitalId(drgsQueryParam);
        return CommonResult.success(infos);
    }

    @RequestMapping("/queryHospital")
    public CommonResult<List<MedicalInstitutionVo>> queryHospital(MedicalInstitutionDto dto){
        List<MedicalInstitutionVo> voList = commonService.queryHospital(dto);
        return CommonResult.success(voList);
    }

    /**
     * 通过key获取值
     * @param key
     * @return
     */
    @RequestMapping("/getSysConfigByKey")
    @ResponseBody
    public CommonResult<Map<String,Object>> getSysConfigByKey(@RequestParam("key") String key){
        Map<String,Object> map = new HashMap<>();
        Object o = SysCommonConfigUtil.get(key, true);
        if(o != null){
            map.put("value", o);
            return CommonResult.success(map);
        }
        return CommonResult.success(map);
    }

}
