package com.my.som.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.my.som.common.constant.DrgConst;
import com.my.som.common.util.RedisUtils;
import com.my.som.common.util.ValidateUtil;
import com.my.som.common.vo.SomBackUser;
import com.my.som.util.SysCommonConfigUtil;
import com.my.som.vo.SomRoleMgt;
import com.my.som.dao.sys.SomRoleMgtMapper;
import com.my.som.dao.sys.SomUserRoleMapper;
import com.my.som.model.common.SomHospInfo;
import com.my.som.model.orgAndUserManagement.SomBasDept;
import com.my.som.service.common.CommonService;
import com.my.som.service.sys.SysUserService;
import com.my.som.common.vo.SysUserBase;
import com.my.som.common.vo.SomUserRole;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

/**
 * @author: zyd
 * Case:          drg
 * FileName:      BaseController
 * Date:          2020/1/13 17:56
 * Description:   通用Controller 用于对公共属性
 * Ver:       1.0
 * Copyright (C),2012-2022 Freedom
 */
@Component
public class BaseController {
    @Autowired
    private SysUserService adminService;
    @Autowired
    CommonService commonService;

    @Resource
    private SomUserRoleMapper sysUserRoleMapper;

    @Resource
    private SomRoleMgtMapper sysRoleMapper;

    //user 过期时间 1800 秒
    private long userExpireTime = 1800;

    @Autowired
    protected HttpServletRequest request;

    /**
     * 根据request中loginId获取对应userInfo
     *
     * @return
     */
    protected SomBackUser getUserInfo() {
        HttpServletRequest request = this.request;
        String loginId = request.getRemoteUser();
        Object resultObject = RedisUtils.get(loginId);
        if (StrUtil.isBlankIfStr(resultObject) && ValidateUtil.isNotEmpty(loginId)) {
            //无缓存则查询数据获取用户信息
            resultObject = adminService.getAdminByUsername(loginId);
            RedisUtils.set(loginId, resultObject);
            RedisUtils.expire(loginId, userExpireTime);
        }
        if (resultObject != null) {
            return (SomBackUser) resultObject;
        }
        return new SomBackUser();
    }

    /**
     * 获取用户信息，业务代码调用此方法，包含用户信息更全，用户的
     *
     * @return
     */
    protected SysUserBase getUserBaseInfo() {
        SomBackUser somBackUser = getUserInfo();//用户表信息
        SysUserBase sysUserBase = new SysUserBase();
        BeanUtil.copyProperties(somBackUser, sysUserBase);

        // 获取所有用户角色
        List<SomUserRole> userRoles = sysUserRoleMapper.findUserRoles(sysUserBase.getId());
        somBackUser.setUserRoles(userRoles);
        // 获取用户角色名称
        List<SomRoleMgt> sysRoles = sysRoleMapper.findPage();
        StringBuffer userRoleNames = new StringBuffer();
        StringBuffer userRoleCodes = new StringBuffer();
        for (SomUserRole role : userRoles) {
            for (SomRoleMgt somRoleMgt : sysRoles) {
                if (role.getRoleId().equals(somRoleMgt.getId())) {
                    userRoleNames.append(somRoleMgt.getName());
                    userRoleNames.append(";");
                    userRoleCodes.append(somRoleMgt.getId());
                    userRoleCodes.append(";");
                }
            }
        }

        if (!ValidateUtil.isEmpty(userRoleNames.toString())) {
            sysUserBase.setUserRoleNames(userRoleNames.toString());
            sysUserBase.setUserRoleCodes(userRoleCodes.toString());
        }

        //获取当前用户权限
        List<SomUserRole> somUserRoles = somBackUser.getUserRoles();
        String medHospitalAuthCode = (String) SysCommonConfigUtil.get(DrgConst.SCC_MED_SYSTEMS_AUTH);
        //医疗机构权限
        String medHospitalAuth = (String) SysCommonConfigUtil.get(DrgConst.SCC_HOSPITAL_AUTH);
        //科室权限
        String medDeptAuth = (String) SysCommonConfigUtil.get(DrgConst.SCC_DEPT_AUTH);
        //医生权限
        String medDoctorAuth = (String) SysCommonConfigUtil.get(DrgConst.SCC_DOCTOR_AUTH);

        boolean hasMedSystemsRule = false;
        boolean hasHospitalRule = false;
        boolean hasMedDeptRule = false;
        boolean hasMedDoctorRule = false;
        for (SomUserRole userRole :
                somUserRoles) {
            if (medHospitalAuthCode.equals(userRole.getRoleId() + "")) {
                //有医疗集体权限
                hasMedSystemsRule = true;
                sysUserBase.setBizOrgType(DrgConst.BIZ_ORG_TYPE_1);
            }
            if (medHospitalAuth.equals(userRole.getRoleId() + "")) {
                //有医疗机构权限
                hasHospitalRule = true;
                sysUserBase.setBizOrgType(DrgConst.BIZ_ORG_TYPE_2);
            }
            if (medDeptAuth.equals(userRole.getRoleId() + "")) {
                //确认有科室权限权限
                hasMedDeptRule = true;
                sysUserBase.setBizOrgType(DrgConst.BIZ_ORG_TYPE_4);
            }
            if (medDoctorAuth.equals(userRole.getRoleId() + "")) {
                //确认有医生权限
                hasMedDoctorRule = true;
                sysUserBase.setBizOrgType(DrgConst.BIZ_ORG_TYPE_6);
            }
        }

        List<SomBasDept> baseOrgs = commonService.getOrgNodeList();//获取所有组织架构
        SomBasDept currentBaseOrg = new SomBasDept();
        List<String> hospitalIdList = new ArrayList<>();
        for (SomBasDept bo : baseOrgs) {
            if (bo.getOrgId().equals(sysUserBase.getBlngOrgOrgId())) {
                currentBaseOrg = bo;
            }
            if (hasMedSystemsRule && (sysUserBase.getBlngOrgOrgId().equals(bo.getPrntDeptId()))) {
                //如果当前的用户的权限是医疗集团的，则遍历组织取出当前组织下的子组织，组装hospitalIds
                hospitalIdList.add(bo.getOrgId());
            }
        }

        if (!ValidateUtil.isEmpty(currentBaseOrg.getOrgId())) {
            if (hasMedSystemsRule) {
                //医疗集团权限类型,设置医疗机构ids
                sysUserBase.setHospitalIdList(hospitalIdList);
            } else if (hasHospitalRule) {
                //医疗机构权限类型,设置医疗机构id
                sysUserBase.setHospitalId(currentBaseOrg.getOrgId());
            }else if (hasMedDeptRule) {
                //科室权限类型,设置医疗机构id、科室id
                sysUserBase.setB16c(currentBaseOrg.getOrgId());
            }else if (hasMedDoctorRule) {
                //医生权限类型,设置医疗机构id、医生id,默认已有不做特殊处理
            }
        }
        return sysUserBase;
    }

    //获取该组织架构的上级组织架构信息
    public SomBasDept getParentBaseOrg(List<SomBasDept> baseOrgs, String currentOrgPId) {
        SomBasDept somBasDept = new SomBasDept();
        for (SomBasDept bo : baseOrgs) {
            if (bo.getOrgId().equals(currentOrgPId)) {
                somBasDept = bo;
            }
        }
        return somBasDept;
    }
}
