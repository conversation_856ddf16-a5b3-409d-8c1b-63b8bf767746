package com.my.som.controller.newBusiness.trend;

import com.my.som.common.api.CommonResult;
import com.my.som.dto.newBusiness.trend.NewBusinessTrendAnalysisDto;
import com.my.som.service.newBusiness.trend.NewDipBusinessTrendAnalysisService;
import com.my.som.vo.newBusiness.trend.NewBusinessTrendAnalysisVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/NewDipBusinessTrendAnalysisController")
public class NewDipBusinessTrendAnalysisController {

    @Autowired
    private NewDipBusinessTrendAnalysisService newDipBusinessTrendAnalysisService;

    @RequestMapping("/queryData")
    public CommonResult queryData(NewBusinessTrendAnalysisDto dto){
        List<NewBusinessTrendAnalysisVo> list = newDipBusinessTrendAnalysisService.queryData(dto);
        return CommonResult.success(list);
    }

}
