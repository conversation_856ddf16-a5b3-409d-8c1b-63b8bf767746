package com.my.som.activiti.listener;

import com.my.som.common.constant.DrgConst;
import com.my.som.common.util.RedisUtils;
import com.my.som.common.util.ValidateUtil;
import com.my.som.config.DynamicScheduleTask;
import com.my.som.controller.BaseController;
import com.my.som.model.dataHandle.SomDataprosLog;
import com.my.som.service.dataHandle.*;
import org.activiti.api.model.shared.event.RuntimeEvent;
import org.activiti.api.task.model.Task;
import org.activiti.api.task.runtime.events.*;
import org.activiti.api.task.runtime.events.listener.TaskRuntimeEventListener;
import org.activiti.engine.TaskService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;


@Service
public class MyTaskEventListener extends BaseController implements TaskRuntimeEventListener {
    private Logger logger = LoggerFactory.getLogger(MyTaskEventListener.class);
    @Autowired
    private TaskService taskService;
    @Autowired
    private ValidateAndCleanJobService validateAndCleanJobService;
    @Autowired
    private ValidateJobService validateService;
    @Autowired
    private CleanDataJobService cleanDataJobService;
    @Autowired
    private DrgGroupJobService drgGroupJobService;
    @Autowired
    private ExtractBusKeyDataJobService extractBusKeyDataJobService;
    @Autowired
    private PpsGroupJobService ppsGroupJobService;
    @Autowired
    private DrgsPaymentJobService drgsPaymentJobService;
    @Autowired
    private DataRecordService dataRecordService;
    @Resource
    private DIPGroupJobService dipGroupJobService;
    @Autowired
    private GeneraScoreJobService generaScoreJobService;

    @Autowired
    private SettleListValidateJobService settleListValidateJobService;

    @Resource
    private CDGroupJobService cdGroupJobService;

    //redis中正在跑流程的k00的key
    private static final String RUNNING_K00S_REDIS_KEY_NAME = "queue";

    @Override
    public void onEvent(RuntimeEvent runtimeEvent) {
        //SomBackUser somBackUser = getUserInfo();
        if (runtimeEvent instanceof TaskActivatedEvent)
            logger.info("Do something, task is activated: " + runtimeEvent.toString());
        else if (runtimeEvent instanceof TaskAssignedEvent) {
            TaskAssignedEvent taskEvent = (TaskAssignedEvent) runtimeEvent;
            Task task = taskEvent.getEntity();
            logger.info("Do something, task is assigned: " + task.toString());
            String dataLog_id = task.getBusinessKey();
            //根据任务名称调用业务处理代码，若业务代码没有返回异常，那么提交当前任务执行下一步，否则等待业务代码异常处理完成之后再提交任务
            //业务代码返回：异常字符串，若为空不存在异常，否则将异常信息保存数据库
            try {
                switch (task.getName()) {
                    case DrgConst.VALIDATEANDCLEANDATA:  //病案校验和数据清洗
                        String validateAndCleanDataExceptionStr = validateAndCleanJobService.validateAndCleanDataByLogId(task.getBusinessKey());
                        if (ValidateUtil.isEmpty(validateAndCleanDataExceptionStr)) {
                            taskService.complete(task.getId());
                        } else {
                            updateDataHandleState(validateAndCleanDataExceptionStr, task.getBusinessKey());
                        }
                        break;
                    case DrgConst.VALIDATE:   //病案校验
                        String validateExceptionStr = validateService.validateDataByLogId(task.getBusinessKey());
                        if (ValidateUtil.isEmpty(validateExceptionStr)) {
                            taskService.complete(task.getId());
                        } else {
                            updateDataHandleState(validateExceptionStr, task.getBusinessKey());
                        }
                        break;
                    case DrgConst.CLEANDATA: //数据清洗
                        String cleanDataExceptionStr = cleanDataJobService.cleanDataByLogId(task.getBusinessKey());
                        if (ValidateUtil.isEmpty(cleanDataExceptionStr)) {
                            taskService.complete(task.getId());
                        } else {
                            updateDataHandleState(cleanDataExceptionStr, task.getBusinessKey());
                        }
                        break;
                    case DrgConst.DRGSGROUP: //DRGs分组
                        String groupExceptionStr = drgGroupJobService.drgGroupByLogId(task.getBusinessKey());
                        if (ValidateUtil.isEmpty(groupExceptionStr)) {
                            taskService.complete(task.getId());
                        } else {
                            updateDataHandleState(groupExceptionStr, task.getBusinessKey());
                        }
                        break;
                    case DrgConst.EXTRACTBUSKEY:  //抽取bus_key
                        String extractBusKeyDataExceptionStr = extractBusKeyDataJobService.extractBusKeyDataLogId(task.getBusinessKey());
                        if (ValidateUtil.isEmpty(extractBusKeyDataExceptionStr)) {
                            taskService.complete(task.getId());
                        } else {
                            updateDataHandleState(extractBusKeyDataExceptionStr, task.getBusinessKey());
                        }
                        break;
                    case DrgConst.PPSGROUP:  //成都分组
                        String ppsGroupExceptionStr = cdGroupJobService.cdGroupJobServiceByLogId(task.getBusinessKey());
                        if (ValidateUtil.isEmpty(ppsGroupExceptionStr)) {
                            taskService.complete(task.getId());
                        } else {
                            updateDataHandleState(ppsGroupExceptionStr, task.getBusinessKey());
                        }
                        break;
                    case DrgConst.DRGSPAYMENT:  //DRGs支付
                        String drgsPaymentExceptionStr = drgsPaymentJobService.drgsPaymentJobServiceByLogId(task.getBusinessKey());
                        if (ValidateUtil.isEmpty(drgsPaymentExceptionStr)) {
                            taskService.complete(task.getId());
                        } else {
                            updateDataHandleState(drgsPaymentExceptionStr, task.getBusinessKey());
                        }
                        break;
                    case DrgConst.DIPGROUP: // DIP分组
                        String dipGroupExceptionStr = dipGroupJobService.dipGroupByLogId(task.getBusinessKey());
                        if (ValidateUtil.isEmpty(dipGroupExceptionStr)) {
                            taskService.complete(task.getId());
                        } else {
                            updateDataHandleState(dipGroupExceptionStr, task.getBusinessKey());
                        }
                        break;
                    case DrgConst.GENERA_SCORE: // 生成分值
                        String generaScoreExceptionStr = generaScoreJobService.generaScoreByLogId(task.getBusinessKey());
                        if (ValidateUtil.isEmpty(generaScoreExceptionStr)) {
                            taskService.complete(task.getId());
                        } else {
                            updateDataHandleState(generaScoreExceptionStr, task.getBusinessKey());
                        }
                        RedisUtils.delete(RUNNING_K00S_REDIS_KEY_NAME + task.getBusinessKey());
                        if (!DynamicScheduleTask.taskQueue.isEmpty()) {
                            String data_log_id = DynamicScheduleTask.taskQueue.poll();
                            //获取到任务执行的data_log_id;
                            //此处结束删除
                            RedisUtils.delete(RUNNING_K00S_REDIS_KEY_NAME + data_log_id);
                        }
                        break;
                    case DrgConst.SETTLE_LIST_VALIDATE: // 结算清单校验
                        String settleListValidateExceptionStr = settleListValidateJobService.validate(task.getBusinessKey());
                        if (ValidateUtil.isEmpty(settleListValidateExceptionStr)) {
                            taskService.complete(task.getId());
                        } else {
                            updateDataHandleState(settleListValidateExceptionStr, task.getBusinessKey());
                        }
                        break;
                }
            } catch (Exception e) {
                logger.info(task.getName() + "节点异常 " + e.getMessage());
                //此处结束删除
                RedisUtils.delete(RUNNING_K00S_REDIS_KEY_NAME+dataLog_id);
                DynamicScheduleTask.taskQueue.poll();
            }
        } else if (runtimeEvent instanceof TaskCancelledEvent)
            logger.info("Do something, task is cancelled: " + runtimeEvent.toString());
        else if (runtimeEvent instanceof TaskCompletedEvent)
            logger.info("Do something, task is completed: " + runtimeEvent.toString());
        else if (runtimeEvent instanceof TaskCreatedEvent)
            logger.info("Do something, task is created: " + runtimeEvent.toString());
        else if (runtimeEvent instanceof TaskSuspendedEvent)
            logger.info("Do something, task is suspended: " + runtimeEvent.toString());
        else
            logger.info("Unknown event: " + runtimeEvent.toString());

    }

    /**
     * 更新流程处理状态
     *
     * @param exceptionStr 处理流程状态 success , exp:...
     * @param businessKey  业务id
     */
    private void updateDataHandleState(String exceptionStr, String businessKey) {
        DynamicScheduleTask.taskQueue.poll();
        SomDataprosLog handleLog = new SomDataprosLog();
        if (exceptionStr.length() > 1000) {
            handleLog.setResult(exceptionStr.substring(0, 1000));
        } else {
            handleLog.setResult(exceptionStr);
        }
        handleLog.setId(Long.valueOf(businessKey));
        dataRecordService.updateDataHandleLogById(handleLog);

    }
}
