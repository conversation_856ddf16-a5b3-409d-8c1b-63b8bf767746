package com.my.som;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableAsync;

/**
 * 应用启动入口
 *
 * Created by drg on 2018/4/26.
 */
@SpringBootApplication
@MapperScan(basePackages = "com.my.som.**.mapper")
@EnableCaching
@EnableAsync
public class DrgApplication extends SpringBootServletInitializer{
    public static void main(String[] args) {
        SpringApplication.run(DrgApplication.class, args);
    }

    @Override//为了打包springboot项目
    protected SpringApplicationBuilder configure(
            SpringApplicationBuilder builder) {
        return builder.sources(this.getClass());
    }
}
