package com.my.som.settlevalidate.script.depth;

import com.my.som.model.dataHandle.SomHiInvyBasInfo;
import com.my.som.model.dataHandle.SomOprnOprtInfo;
import com.my.som.settlevalidate.constant.SettleValidateConst;
import com.my.som.settlevalidate.util.SettleValidateUtil;
import com.my.som.settlevalidate.vo.SetlValidBaseBusiVo;
import com.my.som.vo.dataHandle.SettleListHandlerVo;
import com.my.som.vo.dataHandle.SettleListValidateVo;
import com.yomahub.liteflow.script.ScriptExecuteWrap;
import com.yomahub.liteflow.script.body.JaninoCommonScriptBody;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 事后判断 检测麻醉时间
 */
public class check_anst_time_hubei_after_depth implements JaninoCommonScriptBody {
    private static final Logger logger = LoggerFactory.getLogger(check_anst_time_hubei_after_depth.class);
    private static final String MAIN_CHECK_FIELD = "anstTime";
    private static final String MAIN_CHECK_NAME = "麻醉时间";
    private static final String MAIN_CHECK_FIELD1 = "c43";
    private static final String MAIN_CHECK_FIELD_1 = "anstBegntime";
    private static final String MAIN_CHECK_NAME_1 = "麻醉开始时间";

    private static final String MAIN_CHECK_FIELD_2 = "anstEndtime";
    private static final String MAIN_CHECK_NAME_2 = "麻醉结束时间";
    private static final String MAIN_CHECK_FIELD_3 = "b12";
    private static final String MAIN_CHECK_FIELD_4 = "b15";
    private static final String MAIN_CHECK_REGION = "湖北";
    /**
     * 麻醉开始时间(anst_begntime)基础质控
     * 麻醉结束时间(anst_endtime)基础质控
     * 判断麻醉开始时间是否为空、麻醉结束时间是否为空
     * 麻醉开始时间是否小于麻醉结束时间
     *
     * @param wrap
     * @return
     */
    @Override
    public Void body(ScriptExecuteWrap wrap) {
        SetlValidBaseBusiVo setlValidBaseBusiVo = (SetlValidBaseBusiVo) wrap.cmp.getFirstContextBean();
        SettleListValidateVo settleListValidateVo = (SettleListValidateVo) wrap.cmp.getRequestData();
        Map<String, String> keysBasicResultMap = setlValidBaseBusiVo.getKeysBasicResultMap();
        List<SomOprnOprtInfo> busOperateDiagnosisList = settleListValidateVo.getBusOperateDiagnosisList();
        SomHiInvyBasInfo somHiInvyBasInfo = settleListValidateVo.getSomHiInvyBasInfo();
        SettleListHandlerVo settleListHandlerVo = new SettleListHandlerVo();

        // 检查 somHiInvyBasInfo 是否为空
        if (SettleValidateUtil.isEmpty(somHiInvyBasInfo)) {
            addErrorDetail(SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY, SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY,
                    (String) SettleValidateConst.CHECK_TYPE_MAP.get(SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY), MAIN_CHECK_FIELD_1,
                    SettleValidateConst.VALIDATE_STATE_FAIL, setlValidBaseBusiVo);
            return null;
        }

        // 检查 busOperateDiagnosisList(手术操作) 是否为空
        if (SettleValidateUtil.isEmpty(busOperateDiagnosisList)) {
            return null;
        }

        for (int i = 0; i < busOperateDiagnosisList.size(); i++) {
            String c43_stas = (String) keysBasicResultMap.get(i + MAIN_CHECK_FIELD1);
            if (SettleValidateConst.PASS.equals(c43_stas)) {
                SomOprnOprtInfo somOprnOprtInfo = (SomOprnOprtInfo) busOperateDiagnosisList.get(i);
                String anstBegntime = somOprnOprtInfo.getAnstBegntime();
                String anstEndtime = somOprnOprtInfo.getAnstEndtime();
                if (SettleValidateUtil.isNotEmpty(anstBegntime) && SettleValidateUtil.isNotEmpty(anstEndtime)) {
                    //说明此时需要麻醉时间判断的规范性
                    checkAnstTimestandards(i, anstBegntime, somOprnOprtInfo, setlValidBaseBusiVo, anstEndtime);
                } else {
                    //判断麻醉时间非空性
                    checkAnstTimeIsEmpty(anstBegntime, i, somOprnOprtInfo, setlValidBaseBusiVo, anstEndtime);
                }}
        }
         if (SettleValidateConst.PASS.equals((String) keysBasicResultMap.get(MAIN_CHECK_FIELD_3))
                && SettleValidateConst.PASS.equals((String) keysBasicResultMap.get(MAIN_CHECK_FIELD_4))) {
            //入院时间
            String b12 = somHiInvyBasInfo.getB12();
            //出院时间
            String b15 = somHiInvyBasInfo.getB15();

            for (int i = 0; i < busOperateDiagnosisList.size(); i++) {
                //麻醉方式
                String c43_stas = (String) keysBasicResultMap.get(i + MAIN_CHECK_FIELD1);
                if (SettleValidateConst.PASS.equals(c43_stas)) {
                    SomOprnOprtInfo somOprnOprtInfo = (SomOprnOprtInfo) busOperateDiagnosisList.get(i);
                    String anstBegntime = somOprnOprtInfo.getAnstBegntime();
                    String anstEndtime = somOprnOprtInfo.getAnstEndtime();
                  //如果麻醉时间不为空
                    if (SettleValidateUtil.isNotEmpty(anstBegntime) && SettleValidateUtil.isNotEmpty(anstEndtime)) {
                        //判断麻醉时间合理性
                        checkAnstIsplausible(i,b12, anstBegntime, somOprnOprtInfo, setlValidBaseBusiVo, anstEndtime, b15);
                    }
                }
            }
        }
        return null;
    }

    /**
     * 判断麻醉时间不为空
     */
    private void checkAnstTimeIsEmpty(String anstBegntime, int i, SomOprnOprtInfo somOprnOprtInfo, SetlValidBaseBusiVo setlValidBaseBusiVo, String anstEndtime) {
        if (SettleValidateUtil.isEmpty(anstBegntime)) {
            //麻醉开始时间为空
            addErrorDetail(SettleValidateConst.VALIDATE_TYPE_2, SettleValidateConst.VALIDATE_TYPE_1,
                    "第" + (i + 1) + "个手术操作：编码为["+somOprnOprtInfo.getC35c()+"]的" + MAIN_CHECK_NAME_1 + "[" + somOprnOprtInfo.getAnstBegntime() + "]为空", MAIN_CHECK_FIELD_1,
                    SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
        }
        if (SettleValidateUtil.isEmpty(anstEndtime)) {
            //麻醉结束时间为空
            addErrorDetail(SettleValidateConst.VALIDATE_TYPE_2, SettleValidateConst.VALIDATE_TYPE_1,
                    "第" + (i + 1) + "个手术操作：编码为["+somOprnOprtInfo.getC35c()+"]的" + MAIN_CHECK_NAME_2 + "[" + somOprnOprtInfo.getAnstEndtime() + "]为空", MAIN_CHECK_FIELD_2,
                    SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
        }
    }

    /**
     * 判断麻醉时间的合理性
     */
    private void checkAnstIsplausible( int i,String b12, String anstBegntime, SomOprnOprtInfo somOprnOprtInfo, SetlValidBaseBusiVo setlValidBaseBusiVo, String anstEndtime, String b15) {
        //麻醉开始时间大于 入院时间
        if (judgeTimeOK(anstEndtime,b12)) {
            addErrorDetail(SettleValidateConst.VALIDATE_TYPE_2, SettleValidateConst.VALIDATE_ERROR_TYPE_2,
                    "第" + (i + 1) + "个手术操作：编码为["+somOprnOprtInfo.getC35c()+"]的" +  MAIN_CHECK_NAME_1 + "[" + somOprnOprtInfo.getAnstBegntime() + "] 应该大于入院时间["+b12+"]", MAIN_CHECK_FIELD_1,
                    SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
        }else{
            //麻醉开始时间（天）-入院时间（天）大于等于 2
            if (getDateDifferenceInDays(b12,anstEndtime) < 2  ) {
                addErrorDetail(SettleValidateConst.VALIDATE_TYPE_2, SettleValidateConst.VALIDATE_ERROR_TYPE_2,
                        "第" + (i + 1) + "个手术操作：编码为["+somOprnOprtInfo.getC35c()+"]的" +  "麻醉开始时间["+somOprnOprtInfo.getAnstBegntime()+"]减去入院时间[" +b12+"] 应该大于等于 2 ", MAIN_CHECK_FIELD_1,
                        SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
            }
        }
        //麻醉结束时间 小于  出院时间
        if (judgeTimeOK(b15, anstEndtime)) {
            addErrorDetail(SettleValidateConst.VALIDATE_TYPE_2, SettleValidateConst.VALIDATE_ERROR_TYPE_2,
                    "第" + (i + 1) + "个手术操作：编码为["+somOprnOprtInfo.getC35c()+"]的" +  MAIN_CHECK_NAME_2 + "[" + somOprnOprtInfo.getAnstEndtime() + "] 应该小于出院时间["+b15+"]", MAIN_CHECK_FIELD_2,
                    SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
        }
        //麻醉结束时间大于 麻醉开始时间
        if (judgeTimeOK(anstEndtime,anstBegntime)) {
            addErrorDetail(SettleValidateConst.VALIDATE_TYPE_2, SettleValidateConst.VALIDATE_ERROR_TYPE_2,
                    "第" + (i + 1) + "个手术操作：编码为["+somOprnOprtInfo.getC35c()+"]的" +  "麻醉开始时间["+somOprnOprtInfo.getAnstBegntime()+"]应该小于麻醉结束时间[" +somOprnOprtInfo.getAnstEndtime()+"]", MAIN_CHECK_FIELD_1,
                    SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
        }


    }

    /**
     * 判断麻醉时间是否规范
     */
    private boolean checkAnstTimestandards(int i, String anstBegntime, SomOprnOprtInfo somOprnOprtInfo, SetlValidBaseBusiVo setlValidBaseBusiVo, String anstEndtime) {
        boolean flag = true;
        if (!isValidDateTime(anstBegntime)) {
            addErrorDetail(SettleValidateConst.VALIDATE_TYPE_2, SettleValidateConst.VALIDATE_ERROR_TYPE_2,
                    "第" + (i + 1) + "个手术操作：编码为["+somOprnOprtInfo.getC35c()+"]的" + MAIN_CHECK_NAME_1 + "[" + somOprnOprtInfo.getAnstBegntime() + "] 格式错误", MAIN_CHECK_FIELD_1,
                    SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
            flag = false;
        }

        if (!isValidDateTime(anstEndtime)) {
            addErrorDetail(SettleValidateConst.VALIDATE_TYPE_2, SettleValidateConst.VALIDATE_ERROR_TYPE_2,
                    "第" + (i + 1) + "个手术操作：编码为["+somOprnOprtInfo.getC35c()+"]的" + MAIN_CHECK_NAME_2 + "[" + somOprnOprtInfo.getAnstEndtime() + "] 格式错误", MAIN_CHECK_FIELD_2,
                    SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
            flag = false;
        }
        return flag;
    }

    /**
     * 判断麻醉开始时间（天）-入院时间（天）大于等于 2 天
     * @param date1Str
     * @param date2Str
     * @return
     */
    public static long getDateDifferenceInDays(String date1Str, String date2Str) {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String formattedDifference = "";
        try {
            // 将时间字符串转换为 Date 对象
            Date date1 = dateFormat.parse(date1Str);
            Date date2 = dateFormat.parse(date2Str);

            // 计算时间差（毫秒）
            long differenceInMillis = date2.getTime() - date1.getTime();

            // 将毫秒差值转换为天数、小时数和分钟数
            long differenceInDays = TimeUnit.MILLISECONDS.toDays(differenceInMillis);
            long remainderMillisAfterDays = differenceInMillis % TimeUnit.DAYS.toMillis(1);
            long differenceInHours = TimeUnit.MILLISECONDS.toHours(remainderMillisAfterDays);

            // 格式化天数/小时数/分钟数字符串
            formattedDifference = String.format("%d", differenceInDays);

        } catch (ParseException e) {
            logger.error(" 麻醉时间转换失败: " + e.getMessage());
        }
        return Long.parseLong(formattedDifference);
    }

    private void addErrorDetail(String index, String errType, String errDscr, String errorFields, String
            chkStas, SetlValidBaseBusiVo baseBusiVo) {
        SettleListHandlerVo handlerVo = new SettleListHandlerVo();
        handlerVo.setIndex(index);
        handlerVo.setErrorFields(errorFields);
        handlerVo.setErrDscr(errDscr);
        handlerVo.setChkStas(chkStas);
        handlerVo.setErrType(errType);
        baseBusiVo.getErrorDetails().add(handlerVo);
    }

    public static boolean isValidDateTime(String dateTimeStr) {
        String format = "yyyy-MM-dd HH:mm:ss";
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(format);
        try {
            LocalDateTime.parse(dateTimeStr, formatter);
            return true;
        } catch (DateTimeParseException e) {
            return false;
        }
    }

    /**
     * 时间2 大于  时间1  返回 true
     *
     * @param time1 时间1
     * @param time2 时间2
     * @return
     */
    private boolean judgeTimeOK(String time1, String time2) {

        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        boolean flag = false;
        try {
            Date admDate = dateFormat.parse(time1);
            Date dscgDate = dateFormat.parse(time2);
            // 入院时间大于出院时间
            if (dscgDate.after(admDate)) {
                flag = true;
            }
        } catch (ParseException e) {
            logger.error("麻醉时间质检 日期解析失败: " + e.getMessage());
        }
        return flag;
    }
}
