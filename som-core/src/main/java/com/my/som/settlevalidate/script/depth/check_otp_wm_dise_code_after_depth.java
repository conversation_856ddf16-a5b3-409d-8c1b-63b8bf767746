package com.my.som.settlevalidate.script.depth;

import com.my.som.common.constant.DrgConst;
import com.my.som.model.dataHandle.SomHiInvyBasInfo;
import com.my.som.settlevalidate.constant.SettleValidateConst;
import com.my.som.settlevalidate.util.SettleValidateUtil;
import com.my.som.settlevalidate.vo.SetlValidBaseBusiVo;
import com.my.som.util.SettleListValidateUtil;
import com.my.som.vo.dataHandle.SettleListDisSectionVo;
import com.my.som.vo.dataHandle.SettleListHandlerVo;
import com.my.som.vo.dataHandle.SettleListValidateVo;
import com.yomahub.liteflow.script.ScriptExecuteWrap;
import com.yomahub.liteflow.script.body.JaninoCommonScriptBody;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 判断 门（急）诊诊断（西医诊断编码）
 */
public class check_otp_wm_dise_code_after_depth implements JaninoCommonScriptBody {
    private static final Logger logger = LoggerFactory.getLogger(check_otp_wm_dise_code_after_depth.class);

    private static final String MAIN_CHECK_FIELD = "c01c";
    private static final String MAIN_CHECK_NAME = "门（急）诊诊断编码（西医）";
    private static final String MAIN_CHECK_REGION = "四川,湖北";

    private static final String MAIN_CHECK_FIELD_2 = "c02n";
    private static final String MAIN_CHECK_NAME_2 = "门（急）诊诊断名称（西医）";
    private static final List<String> ERROR_CODE_BEGIN_LIST = Arrays.asList("V", "W", "X", "Y");

    /**
     * 事后校验
     * 门（急）诊诊断编码（西医）(otp_wm_dise_name)基础质控
     * 判断门（急）诊诊断编码（西医）是否为空
     *
     * @param wrap
     * @return
     */
    @Override
    public Void body(ScriptExecuteWrap wrap) {
        SetlValidBaseBusiVo setlValidBaseBusiVo = (SetlValidBaseBusiVo) wrap.cmp.getFirstContextBean();
        SettleListValidateVo settleListValidateVo = (SettleListValidateVo) wrap.cmp.getRequestData();
        Map<String, String> keysBasicResultMap = setlValidBaseBusiVo.getKeysBasicResultMap();
        SomHiInvyBasInfo somHiInvyBasInfo = settleListValidateVo.getSomHiInvyBasInfo();
        SettleListHandlerVo settleListHandlerVo = new SettleListHandlerVo();
        

        // 检查 somHiInvyBasInfo 是否为空
        if (SettleValidateUtil.isEmpty(somHiInvyBasInfo)) {
            addErrorDetail(SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY, SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY,
                    (String) SettleValidateConst.CHECK_TYPE_MAP.get(SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY), null,
                    SettleValidateConst.VALIDATE_STATE_FAIL, setlValidBaseBusiVo);
            return null;
        }

        // 获取门（急）诊诊断名称（西医）
        String c01c = somHiInvyBasInfo.getC01c();
        String c02n = somHiInvyBasInfo.getC02n();
        if (SettleValidateConst.PASS.equals(keysBasicResultMap.get(MAIN_CHECK_FIELD))) {
            // 判断其他诊断是否和开头是否符合
            checkDiagnosisCodeConsistency(c01c, settleListHandlerVo, setlValidBaseBusiVo, keysBasicResultMap);

            // 判断其他诊断是否和性别相符合
            String a12c = settleListValidateVo.getSomHiInvyBasInfo().getA12c();
            checkDiagnosisGenderConsistency(a12c, c01c,settleListHandlerVo,setlValidBaseBusiVo);

            //判断 是否为医保2.0版ICD-10里的标准诊断名称
            checkDiagnosisStandConsistency(setlValidBaseBusiVo,c01c,c02n);
            checkDiagCodeIsGrep(setlValidBaseBusiVo,c01c);
            //判断 出现P10～P15，入院日期减出生日期必须<365天。
            checkDiagnosisAgeConsistency(c01c, somHiInvyBasInfo,setlValidBaseBusiVo);
        }
        return null;
    }

    private void checkDiagnosisAgeConsistency(String c01c,  SomHiInvyBasInfo somHiInvyBasInfo , SetlValidBaseBusiVo setlValidBaseBusiVo) {
        if (SettleListValidateUtil.P10TP15_LIST.contains(c01c.substring(0,3))) {
            String b12 = somHiInvyBasInfo.getB12();
            String a13 = somHiInvyBasInfo.getA13();
            if (SettleValidateUtil.isNotEmpty(b12) && SettleValidateUtil.isNotEmpty(a13) ) {
                //判断入院日期减出生日期必须<365天。
                long  diffDate = getDateDifferenceInDays(a13,b12 );
                if (diffDate > 365) {
                    //不在合理值范围，提示
                    addErrorDetail(SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_ERROR_TYPE_2,
                            "门(急)诊诊断编码出现P10-P15入院日期减出生日期必须<365天",   MAIN_CHECK_FIELD,
                            SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
                }
            } else {
                if(SettleValidateUtil.isEmpty(b12) ){
                addErrorDetail(SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_ERROR_TYPE_1,
                        "门(急)诊诊断编码出现P10-P15年龄 入院时间" + "[" + b12 + "]为空","b12",
                        SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);}

                if(SettleValidateUtil.isEmpty(a13) ){
                    addErrorDetail(SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_ERROR_TYPE_1,
                            "门(急)诊诊断编码出现P10-P15年龄 出生日期" + "[" + a13 + "]为空","a13",
                            SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);}
            }
        }
    }

    public static long getDateDifferenceInDays(String date1Str, String date2Str) {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String formattedDifference = "";
        try {
            // 将时间字符串转换为 Date 对象
            Date date1 = dateFormat.parse(date1Str);
            Date date2 = dateFormat.parse(date2Str);

            // 计算时间差（毫秒）
            long differenceInMillis = date2.getTime()-date1.getTime() ;

            // 将毫秒差值转换为天数、小时数和分钟数
            long differenceInDays = TimeUnit.MILLISECONDS.toDays(differenceInMillis);
            long remainderMillisAfterDays = differenceInMillis % TimeUnit.DAYS.toMillis(1);
            long differenceInHours = TimeUnit.MILLISECONDS.toHours(remainderMillisAfterDays);

            if(differenceInHours > 0  ){
                differenceInDays =differenceInDays +1;
            }

            // 格式化天数/小时数/分钟数字符串
            formattedDifference = String.format("%d",differenceInDays);

        } catch (ParseException e) {
            logger.error(" 计算差失败: " + e.getMessage());
        }
        return Long.parseLong(formattedDifference);
    }

    private void checkDiagnosisStandConsistency(SetlValidBaseBusiVo setlValidBaseBusiVo,String diagCode,  String diagName ) {
        Map<String, Object> icd10Map = (Map<String, Object>) setlValidBaseBusiVo.getSettleListParams().get(SettleListValidateUtil.KEY_ICD10);

        // 验证编码是否符合ICD-10标准
        if (!icd10Map.containsKey(diagCode)) {
            addErrorDetail(SettleValidateConst.VALIDATE_TYPE_3,SettleValidateConst.VALIDATE_ERROR_TYPE_6,
       MAIN_CHECK_NAME + "[" + diagCode + "]不符合医保2.0",            MAIN_CHECK_FIELD,
                    SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
        } else {
            // 验证名称是否符合ICD-10标准
            String icdName = (String) icd10Map.get(diagCode);
            if (!diagName.equals(icdName)) {
                addErrorDetail(SettleValidateConst.VALIDATE_TYPE_3,SettleValidateConst.VALIDATE_ERROR_TYPE_6,
                        MAIN_CHECK_NAME_2 + "[" + diagName + "]不符合医保2.0,应改为["+icdName+"]",    MAIN_CHECK_FIELD_2,
                        SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
            }
        }
    }
    private void checkDiagCodeIsGrep(SetlValidBaseBusiVo setlValidBaseBusiVo,String diagCode) {
        Map<String, Object> grepMap = (Map<String, Object>) setlValidBaseBusiVo.getSettleListParams().get(SettleListValidateUtil.KEY_ICD10_GRAY_CODE_2);

        // 检查主要诊断是否存在医保停用码
        if (grepMap.containsKey(diagCode)) {
            addErrorDetail(SettleValidateConst.VALIDATE_TYPE_2, SettleValidateConst.VALIDATE_ERROR_TYPE_12,

                    MAIN_CHECK_NAME + "[" + diagCode + "]是医保停用码",    MAIN_CHECK_FIELD,
                    SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
        }
    }


        private void checkDiagnosisCodeConsistency(String c01c, SettleListHandlerVo settleListHandlerVo, SetlValidBaseBusiVo setlValidBaseBusiVo, Map<String, String> keysBasicResultMap) {
        for(String str : ERROR_CODE_BEGIN_LIST){
            if (c01c.startsWith(str)){
                addErrorDetail(SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_TYPE_1,
                        MAIN_CHECK_NAME + "[" + c01c + "]应填写A~U开头和Z开头的编码及名称",  MAIN_CHECK_FIELD,
                        SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
            }
        }
    }
    private void checkDiagnosisGenderConsistency(String gender, String c01c,SettleListHandlerVo settleListHandlerVo,SetlValidBaseBusiVo setlValidBaseBusiVo) {
        Map<String, Object> map = (Map<String, Object>) setlValidBaseBusiVo.getSettleListParams().get(SettleListValidateUtil.KEY_DIS_SECTION_LIST);

        if (DrgConst.MAN.equals(gender)) {
            List<SettleListDisSectionVo> settleListDisSectionVos = (List<SettleListDisSectionVo>) map.get(DrgConst.MAN);
            List<String> femaleDiagSections = new ArrayList<>();
            for (SettleListDisSectionVo settleListDisSectionVo : settleListDisSectionVos) {
                femaleDiagSections.add(settleListDisSectionVo.getDiagSec());
            }
            checkGenderIsSame(c01c, setlValidBaseBusiVo, femaleDiagSections);
        } else if (DrgConst.WOMAN.equals(gender)) {
            List<SettleListDisSectionVo> settleListDisSectionVos = (List<SettleListDisSectionVo>) map.get(DrgConst.WOMAN);
            List<String> maleDiagSections = new ArrayList<>();
            for (SettleListDisSectionVo settleListDisSectionVo : settleListDisSectionVos) {
                maleDiagSections.add(settleListDisSectionVo.getDiagSec());
            }
            checkGenderIsSame(c01c, setlValidBaseBusiVo, maleDiagSections);
        }
    }

    private void checkGenderIsSame(String c01c, SetlValidBaseBusiVo setlValidBaseBusiVo, List<String> maleDiagSections) {

        if (maleDiagSections.contains(c01c.substring(0, 3)) || maleDiagSections.contains(c01c.substring(0, 5))) {
            addErrorDetail(SettleValidateConst.VALIDATE_TYPE_2, SettleValidateConst.VALIDATE_ERROR_TYPE_306,
                    MAIN_CHECK_NAME + "[" + c01c + "]与性别不符",              MAIN_CHECK_FIELD,
                    SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
        }
    }

    private void addErrorDetail( String index, String errType, String errDscr, String errorFields, String chkStas, SetlValidBaseBusiVo baseBusiVo) {
        SettleListHandlerVo handlerVo =new SettleListHandlerVo();
        handlerVo.setIndex(index);
        handlerVo.setErrDscr(errDscr);
        handlerVo.setErrorFields(errorFields);
        handlerVo.setChkStas(chkStas);
        handlerVo.setErrType(errType);
        baseBusiVo.getErrorDetails().add(handlerVo);
    }
}
