package com.my.som.settlevalidate.script.base;

import com.my.som.model.dataHandle.SomHiInvyBasInfo;
import com.my.som.settlevalidate.constant.SettleValidateConst;
import com.my.som.settlevalidate.util.SettleValidateUtil;
import com.my.som.settlevalidate.vo.SetlValidBaseBusiVo;
import com.my.som.vo.dataHandle.SettleListHandlerVo;
import com.my.som.vo.dataHandle.SettleListValidateVo;
import com.yomahub.liteflow.script.ScriptExecuteWrap;
import com.yomahub.liteflow.script.body.JaninoCommonScriptBody;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

public class check_nwb_bir_wt_basic implements JaninoCommonScriptBody {
    private static final Logger logger = LoggerFactory.getLogger(check_nwb_bir_wt_basic.class);
    private static final String MAIN_CHECK_FIELD = "a18";
    private static final String MAIN_CHECK_NAME = "新生儿出生体重";
    private static final String MAIN_CHECK_REGION = "四川,湖北";
    /**
     * 新生儿出生体重-[a18]-基础校验
     * 1、非空时判断是否为整数，不为数字时则提示异常
     * 2、单项数值范围是否在[200,10000]之间
     * 3、多新生儿是否用逗号隔开（隔开后每个判断数字范围）
     *
     * @param wrap
     * @return
     */
    @Override
    public Void body(ScriptExecuteWrap wrap) {
        String main_check_field = "a18";
        String main_check_name = "新生儿出生体重";
        SetlValidBaseBusiVo setlValidBaseBusiVo = (SetlValidBaseBusiVo) wrap.cmp.getFirstContextBean();
        SettleListValidateVo settleListValidateVo = (SettleListValidateVo) wrap.cmp.getRequestData();
        Map<String, String> keysBasicResultMap = setlValidBaseBusiVo.getKeysBasicResultMap();
        SomHiInvyBasInfo somHiInvyBasInfo = settleListValidateVo.getSomHiInvyBasInfo();
        SettleListHandlerVo settleListHandlerVo = new SettleListHandlerVo();
        if (SettleValidateUtil.isEmpty(somHiInvyBasInfo)) {
            settleListHandlerVo.setIndex(SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY);
            settleListHandlerVo.setErrDscr((String) SettleValidateConst.CHECK_TYPE_MAP.get(SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY));
            settleListHandlerVo.setChkStas(SettleValidateConst.VALIDATE_STATE_FAIL);
            settleListHandlerVo.setErrType(SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY);
            setlValidBaseBusiVo.getErrorDetails().add(settleListHandlerVo);
            return null;
        }
        Double a18 = somHiInvyBasInfo.getA18();
        String mulNwbBirWt = somHiInvyBasInfo.getMulNwbBirWt();

        if (!SettleValidateUtil.isEmpty(a18)) {
            //不为空,则一定是一个正常值，判断范围是否在[200,10000]
            if (a18 == 0.0) {
                //处理为null,被默认赋值为0.0数据
                keysBasicResultMap.put(main_check_field, SettleValidateConst.NULL);
                return null;
            }
            if (!(a18 >= 200 && a18 <= 10000)) {
                //不在合理值范围，提示
                settleListHandlerVo.setIndex(SettleValidateConst.VALIDATE_TYPE_1);
                settleListHandlerVo.setSettleListId(somHiInvyBasInfo.getId());
                settleListHandlerVo.setErrorFields(main_check_field);
                settleListHandlerVo.setErrDscr(main_check_name + "[" + a18 + "]超出合理值范围");
                settleListHandlerVo.setChkStas(SettleValidateConst.VALIDATE_STATE_SUCCESS);
                settleListHandlerVo.setErrType(SettleValidateConst.VALIDATE_ERROR_TYPE_2);
                setlValidBaseBusiVo.getErrorDetails().add(settleListHandlerVo);
                //放入字段基础校验
                keysBasicResultMap.put(main_check_field, SettleValidateConst.OUTLIER);
            } else {
                //正常值，基础校验通过不写错误信息
                keysBasicResultMap.put(main_check_field, SettleValidateConst.PASS);
            }
        } else {
            keysBasicResultMap.put(main_check_field, SettleValidateConst.NULL);
        }

        checkMulNwbBirWt(mulNwbBirWt, settleListHandlerVo, somHiInvyBasInfo, setlValidBaseBusiVo, keysBasicResultMap);
        return null;
    }


    /**
     * 检查多新生儿出生体重
     * @param mulNwbBirWt
     * @param settleListHandlerVo
     * @param somHiInvyBasInfo
     * @param setlValidBaseBusiVo
     * @param keysBasicResultMap
     */
    private  void checkMulNwbBirWt(String mulNwbBirWt, SettleListHandlerVo settleListHandlerVo, SomHiInvyBasInfo somHiInvyBasInfo, SetlValidBaseBusiVo setlValidBaseBusiVo, Map<String, String> keysBasicResultMap) {
        if (!SettleValidateUtil.isEmpty(mulNwbBirWt)) {
            if(!mulNwbBirWt.contains(",")){
                addErrorDetail(SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_ERROR_TYPE_303,
                        "mulNwbBirWt", "多新生儿体重["+mulNwbBirWt+"]不存在,请将体重填写到【新生儿入院体重】中",
                        SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
            }else{
            // 分割字符串
            String[] stringValues = mulNwbBirWt.split(",");
            // 转换并添加到 List 中
            for (int i = 0; i < stringValues.length; i++) {
                String value = stringValues[i];
                try {
                    Double wt = Double.parseDouble(value.trim());
                    if (!(wt >= 200 && wt <= 10000)) {
                        addErrorDetail(SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_ERROR_TYPE_2,
                                "mulNwbBirWt", "多新生儿体重："+"第"+(i +1)+"个新生儿体重"+ "[" + value + "]的超出合理值范围",
                                SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
                    }
                } catch (NumberFormatException e) {
                    addErrorDetail(SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_ERROR_TYPE_2,
                            "mulNwbBirWt", "多新生儿体重："+"第"+(i+1)+"个新生儿体重"+ "[" + value + "]不为体重阿拉伯数字",
                            SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
                }
            }}
        } else {
            keysBasicResultMap.put("mulNwbBirWt", SettleValidateConst.NULL);
        }
    }

    private  void addErrorDetail(String index, String errType, String errorFields, String errDscr, String chkStas, SetlValidBaseBusiVo baseBusiVo) {
        SettleListHandlerVo handlerVo = new SettleListHandlerVo();
        handlerVo.setIndex(index);
        handlerVo.setErrorFields(errorFields);
        handlerVo.setErrDscr(errDscr);
        handlerVo.setChkStas(chkStas);
        handlerVo.setErrType(errType);
        baseBusiVo.getErrorDetails().add(handlerVo);
    }
}
