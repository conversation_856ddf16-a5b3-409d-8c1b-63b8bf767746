package com.my.som.settlevalidate.script.base;

import com.my.som.model.dataHandle.SomHiInvyBasInfo;
import com.my.som.settlevalidate.constant.SettleValidateConst;
import com.my.som.settlevalidate.util.SettleValidateUtil;
import com.my.som.settlevalidate.vo.SetlValidBaseBusiVo;
import com.my.som.util.SettleListValidateUtil;
import com.my.som.vo.dataHandle.SettleListHandlerVo;
import com.my.som.vo.dataHandle.SettleListValidateVo;
import com.yomahub.liteflow.script.ScriptExecuteWrap;
import com.yomahub.liteflow.script.body.JaninoCommonScriptBody;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.util.List;
import java.util.Map;

/**
 * 事后判断 出院科别编码不为空
 */
public class check_dscg_caty_code_after_basic implements JaninoCommonScriptBody {
    private static final Logger logger = LoggerFactory.getLogger(check_dscg_caty_code_after_basic.class);

    private static final String MAIN_CHECK_FIELD = "b16c";
    private static final String MAIN_CHECK_NAME = "出院科别编码";
    private static final String MAIN_CHECK_REGION = "四川";

    /**
     * 事后 校验
     * 出院科别编码(dscg_caty_code) 基础质控
     * 判断出院科别编码是否为空
     *
     * @param wrap
     * @return
     */
    @Override
    public Void body(ScriptExecuteWrap wrap) {

        SetlValidBaseBusiVo setlValidBaseBusiVo = (SetlValidBaseBusiVo) wrap.cmp.getFirstContextBean();
        SettleListValidateVo settleListValidateVo = (SettleListValidateVo) wrap.cmp.getRequestData();
        Map<String, String> keysBasicResultMap = setlValidBaseBusiVo.getKeysBasicResultMap();
        SomHiInvyBasInfo somHiInvyBasInfo = settleListValidateVo.getSomHiInvyBasInfo();
        SettleListHandlerVo settleListHandlerVo = new SettleListHandlerVo();

        // 检查 somHiInvyBasInfo 是否为空
        if (SettleValidateUtil.isEmpty(somHiInvyBasInfo)) {
            addErrorDetail(settleListHandlerVo, SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY, SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY,
                    (String) SettleValidateConst.CHECK_TYPE_MAP.get(SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY), null,
                    SettleValidateConst.VALIDATE_STATE_FAIL, setlValidBaseBusiVo);
            return null;
        }

        // 获取出院科别
        String b16c = somHiInvyBasInfo.getB16c();
        if (SettleValidateUtil.isEmpty(b16c)) {
            addErrorDetail(settleListHandlerVo, SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_TYPE_1,
                    MAIN_CHECK_NAME + "[" + b16c + "]为空", MAIN_CHECK_FIELD,
                    SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
            keysBasicResultMap.put(MAIN_CHECK_FIELD, SettleValidateConst.NULL);
        } else {
            //获取出院科别
           // 获取科室编码
            List<String> dscgCatyCodeList = (List<String>)setlValidBaseBusiVo.getSettleListParams().get(SettleListValidateUtil.KEY_DEPT_CODE);
            // 获取诊疗类别
          // List<String> trtTypeList = (List<String>)((Map<String,Object>)setlValidBaseBusiVo.getSettleListParams().get(SettleListValidateUtil.KEY_DICT)).get("ZLLB");
           // if(! dscgCatyCodeList.contains(b16c) && !trtTypeList.contains(b16c)){
            if(! dscgCatyCodeList.contains(b16c) ){
                addErrorDetail(settleListHandlerVo, SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_TYPE_1,
                        MAIN_CHECK_NAME + "[" + b16c + "]不符合规范", MAIN_CHECK_FIELD,
                        SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
                keysBasicResultMap.put(MAIN_CHECK_FIELD, SettleValidateConst.OUTLIER);
            }else{

                    keysBasicResultMap.put(MAIN_CHECK_FIELD, SettleValidateConst.PASS);
            }
        }
        return null;
    }

    private void addErrorDetail(SettleListHandlerVo handlerVo, String index, String errType, String errDscr, String errorFields, String chkStas, SetlValidBaseBusiVo baseBusiVo) {
        handlerVo.setIndex(index);
        handlerVo.setErrDscr(errDscr);
        handlerVo.setErrorFields(errorFields);
        handlerVo.setChkStas(chkStas);
        handlerVo.setErrType(errType);
        baseBusiVo.getErrorDetails().add(handlerVo);
    }
}

