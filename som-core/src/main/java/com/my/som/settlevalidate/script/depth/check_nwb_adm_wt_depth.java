package com.my.som.settlevalidate.script.depth;

import com.my.som.model.dataHandle.SomHiInvyBasInfo;
import com.my.som.settlevalidate.constant.SettleValidateConst;
import com.my.som.settlevalidate.util.SettleValidateUtil;
import com.my.som.settlevalidate.vo.SetlValidBaseBusiVo;
import com.my.som.vo.dataHandle.SettleListHandlerVo;
import com.my.som.vo.dataHandle.SettleListValidateVo;
import com.yomahub.liteflow.script.ScriptExecuteWrap;
import com.yomahub.liteflow.script.body.JaninoCommonScriptBody;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

public class check_nwb_adm_wt_depth implements JaninoCommonScriptBody {
    private static final Logger logger = LoggerFactory.getLogger(check_nwb_adm_wt_depth.class);
    private static final String MAIN_CHECK_FIELD = "a17";
    private static final String MAIN_CHECK_NAME = "患者是新生儿,新生儿入院体重必填";
    private static final String MAIN_CHECK_REGION = "四川,湖北";
    /**
     * 新生儿入院体重-[17]-深度校验
     * 1、新生儿(天龄小于28天)入院时，新生儿入院体重必须填写
     * 2、获取基础质控结果，判断是否需要走校验
     *
     * @param wrap
     * @return
     */
    @Override
    public Void body(ScriptExecuteWrap wrap) {
        String main_check_field = "a17";
        String main_check_name = "新生儿入院体重";
        String tieup_field = "a16";
        String tieup_field_name = "年龄不足1周岁的年龄";
        SetlValidBaseBusiVo setlValidBaseBusiVo = (SetlValidBaseBusiVo) wrap.cmp.getFirstContextBean();
        SettleListValidateVo settleListValidateVo = (SettleListValidateVo) wrap.cmp.getRequestData();
        Map<String, String> keysBasicResultMap = setlValidBaseBusiVo.getKeysBasicResultMap();
        SomHiInvyBasInfo somHiInvyBasInfo = settleListValidateVo.getSomHiInvyBasInfo();
        SettleListHandlerVo settleListHandlerVo = new SettleListHandlerVo();
        String main_field_baisc_stas = (String) keysBasicResultMap.get(main_check_field);

        if (SettleValidateConst.NULL.equals(main_field_baisc_stas)) {
            Double a17 = somHiInvyBasInfo.getA17();
            //判断是否逻辑必填
            checkA16LessThan28(somHiInvyBasInfo,settleListHandlerVo,main_check_field,main_check_name,a17, setlValidBaseBusiVo);
        }
        return null;
    }

    private static void checkA16LessThan28(SomHiInvyBasInfo somHiInvyBasInfo, SettleListHandlerVo settleListHandlerVo, String main_check_field, String main_check_name, Double a17, SetlValidBaseBusiVo setlValidBaseBusiVo) {
        Integer a16 = somHiInvyBasInfo.getA16();
        if (!SettleValidateUtil.isEmpty(a16)&&a16 <= 28) {
            //是新生儿，需要逻辑必填
            settleListHandlerVo.setIndex(SettleValidateConst.VALIDATE_TYPE_3);
            settleListHandlerVo.setSettleListId(somHiInvyBasInfo.getId());
            settleListHandlerVo.setErrorFields(main_check_field);
            settleListHandlerVo.setErrDscr((String) main_check_name + "[" + a17 + "]，患者是新生儿时，" + main_check_name + "必填");
            settleListHandlerVo.setChkStas(SettleValidateConst.VALIDATE_STATE_SUCCESS);
            settleListHandlerVo.setErrType(SettleValidateConst.VALIDATE_ERROR_TYPE_303);
            setlValidBaseBusiVo.getErrorDetails().add(settleListHandlerVo);
        }
    }
}
