package com.my.som.settlevalidate.script.depth;

import com.my.som.model.dataHandle.SomHiInvyBasInfo;
import com.my.som.settlevalidate.constant.SettleValidateConst;
import com.my.som.settlevalidate.util.SettleValidateUtil;
import com.my.som.settlevalidate.vo.SetlValidBaseBusiVo;
import com.my.som.vo.dataHandle.SettleListHandlerVo;
import com.my.som.vo.dataHandle.SettleListValidateVo;
import com.yomahub.liteflow.script.ScriptExecuteWrap;
import com.yomahub.liteflow.script.body.JaninoCommonScriptBody;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

public class check_nwb_bir_wt_depth implements JaninoCommonScriptBody {
    private static final Logger logger = LoggerFactory.getLogger(check_nwb_bir_wt_depth.class);
    private static final String MAIN_CHECK_FIELD = "a18";
    private static final String MAIN_CHECK_NAME = "新生儿出生体重";
    private static final String MAIN_CHECK_REGION = "四川,湖北";
    /**
     * 新生儿出生体重-[18]-深度校验
     * 1、新生儿(天龄小于28天nwb_age)入院时，新生儿出生体重必须填写。
     * 2、获取基础质控结果，判断是否需要走校验
     *
     * @param wrap
     * @return
     */
    @Override
    public Void body(ScriptExecuteWrap wrap) {
        String main_check_field = "a18";
        String main_check_name = "新生儿出生体重";
        String tieup_field = "a16";
        String tieup_field_name = "年龄不足1周岁的年龄";
        SetlValidBaseBusiVo setlValidBaseBusiVo = (SetlValidBaseBusiVo) wrap.cmp.getFirstContextBean();
        SettleListValidateVo settleListValidateVo = (SettleListValidateVo) wrap.cmp.getRequestData();
        Map<String, String> keysBasicResultMap = setlValidBaseBusiVo.getKeysBasicResultMap();
        SomHiInvyBasInfo somHiInvyBasInfo = settleListValidateVo.getSomHiInvyBasInfo();
        SettleListHandlerVo settleListHandlerVo = new SettleListHandlerVo();
        if (SettleValidateUtil.isEmpty(somHiInvyBasInfo)) {
            return null;
        }
        //新生儿体重
        String main_field_baisc_stas = (String) keysBasicResultMap.get(main_check_field);
        //多新生儿体重
        String mulNwbBirWt_stas = (String) keysBasicResultMap.get("mulNwbBirWt");
        //获取关联字段基础校验结果
        String tieup_field_baisc_stas = (String) keysBasicResultMap.get(tieup_field);

        if (SettleValidateConst.NULL.equals(main_field_baisc_stas)
                &&SettleValidateConst.NULL.equals(mulNwbBirWt_stas) ) {
            //判断是否逻辑必填
            if (isNewBaby(somHiInvyBasInfo, tieup_field_baisc_stas)) {
                Double a18 = somHiInvyBasInfo.getA18();
                //是新生儿，需要逻辑必填
                settleListHandlerVo.setIndex(SettleValidateConst.VALIDATE_TYPE_3);
                settleListHandlerVo.setSettleListId(somHiInvyBasInfo.getId());
                settleListHandlerVo.setErrorFields(main_check_field);
                settleListHandlerVo.setErrDscr((String) main_check_name + "[" + a18 + "]，患者是新生儿时，" + main_check_name + "必填");
                settleListHandlerVo.setChkStas(SettleValidateConst.VALIDATE_STATE_SUCCESS);
                settleListHandlerVo.setErrType(SettleValidateConst.VALIDATE_ERROR_TYPE_303);
                setlValidBaseBusiVo.getErrorDetails().add(settleListHandlerVo);
            }
        }else {
            if (!SettleValidateConst.NULL.equals(main_field_baisc_stas)
                    &&!SettleValidateUtil.isEmpty(somHiInvyBasInfo.getMulNwbBirWt())){
                settleListHandlerVo.setIndex(SettleValidateConst.VALIDATE_TYPE_3);
                settleListHandlerVo.setSettleListId(somHiInvyBasInfo.getId());
                settleListHandlerVo.setErrorFields(main_check_field);
                settleListHandlerVo.setErrDscr((String) main_check_name + "[" + somHiInvyBasInfo.getA18() + "]，和多新生儿体重["+ somHiInvyBasInfo.getMulNwbBirWt()+"]不能同时存在" );
                settleListHandlerVo.setChkStas(SettleValidateConst.VALIDATE_STATE_SUCCESS);
                settleListHandlerVo.setErrType(SettleValidateConst.VALIDATE_ERROR_TYPE_303);
                setlValidBaseBusiVo.getErrorDetails().add(settleListHandlerVo);
            }
        }
        return null;
    }

    /**
     * 判断是否为新生儿【基础校验通过且新生儿天数<=28天】
     *
     * @param somHiInvyBasInfo
     * @param tieup_field_baisc_stas
     * @return
     */
    private boolean isNewBaby(SomHiInvyBasInfo somHiInvyBasInfo, String tieup_field_baisc_stas) {
        if (SettleValidateConst.PASS.equals(tieup_field_baisc_stas) && somHiInvyBasInfo.getA16() <= 28) {
            return true;
        }
        return false;
    }
}
