package com.my.som.settlevalidate.script.depth;

import com.my.som.model.dataHandle.SomHiInvyBasInfo;
import com.my.som.model.medicalQuality.SomSetlInvyScsCutdInfo;
import com.my.som.settlevalidate.constant.SettleValidateConst;
import com.my.som.settlevalidate.util.SettleValidateUtil;
import com.my.som.settlevalidate.vo.SetlValidBaseBusiVo;
import com.my.som.vo.dataHandle.SettleListHandlerVo;
import com.my.som.vo.dataHandle.SettleListValidateVo;
import com.yomahub.liteflow.script.ScriptExecuteWrap;
import com.yomahub.liteflow.script.body.JaninoCommonScriptBody;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 事后判断 重症监护合计时长
 */
public class check_scs_cutd_sum_dura_after_depth implements JaninoCommonScriptBody {
    private static final Logger logger = LoggerFactory.getLogger(check_scs_cutd_sum_dura_after_depth.class);

    private static final String MAIN_CHECK_FIELD = "scsCutdSumDura";
    private static final String MAIN_CHECK_NAME = "重症监护合计时长";
    private static final String MAIN_CHECK_FIELD_1 = "scsCutdInpoolTime";
    private static final String MAIN_CHECK_NAME_1 = "重症监护进入时间";
    private static final String MAIN_CHECK_FIELD_2 = "scsCutdExitTime";
    private static final String MAIN_CHECK_NAME_2 = "重症监护退出时间";
    private static final Pattern PATTERN = Pattern.compile(SettleValidateUtil.SCS_CUTD_SUM_DURA_PATTERN);
    private static final String MAIN_CHECK_FIELD_3 = "b12";
    private static final String MAIN_CHECK_FIELD_4 = "b15";
    private static final String MAIN_CHECK_NAME_3 = "结算清单";
    private static final String MAIN_CHECK_REGION = "四川";

    /**
     * 重症监护合计时长(scs_cutd_sum_dura)基础质控
     * 重症监护合计时长是否合理
     *
     * @param wrap
     * @return
     */
    @Override
    public Void body(ScriptExecuteWrap wrap) {
        SetlValidBaseBusiVo setlValidBaseBusiVo = (SetlValidBaseBusiVo) wrap.cmp.getFirstContextBean();
        SettleListValidateVo settleListValidateVo = (SettleListValidateVo) wrap.cmp.getRequestData();
        Map<String, String> keysBasicResultMap = setlValidBaseBusiVo.getKeysBasicResultMap();
        SettleListHandlerVo settleListHandlerVo = new SettleListHandlerVo();
        SomHiInvyBasInfo somHiInvyBasInfo = settleListValidateVo.getSomHiInvyBasInfo();
        // 检查 somHiInvyBasInfo 是否为空
        if (SettleValidateUtil.isEmpty(somHiInvyBasInfo)) {
            addErrorDetail(SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY, SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY,
                    (String) SettleValidateConst.CHECK_TYPE_MAP.get(SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY), MAIN_CHECK_FIELD_1,
                    SettleValidateConst.VALIDATE_STATE_FAIL, setlValidBaseBusiVo);
            return null;
        }

        List<SomSetlInvyScsCutdInfo> busIcuList = settleListValidateVo.getBusIcuList();
        // 检查 somHiInvyBasInfo 是否为空
        if (SettleValidateUtil.isEmpty(busIcuList)) {
            return null;
        }

        if (SettleValidateConst.PASS.equals(keysBasicResultMap.get(MAIN_CHECK_FIELD_3))
                && SettleValidateConst.PASS.equals(keysBasicResultMap.get(MAIN_CHECK_FIELD_4))) {
            Date b12 = StringToConvert(somHiInvyBasInfo.getB12(), "入院时间");
            Date b15 = StringToConvert(somHiInvyBasInfo.getB15(), "入院时间");
            for (int i = 0; i < busIcuList.size(); i++) {
                SomSetlInvyScsCutdInfo somSetlInvyScsCutdInfo = (SomSetlInvyScsCutdInfo) busIcuList.get(i);
                if (SettleValidateConst.PASS.equals(keysBasicResultMap.get(i + MAIN_CHECK_FIELD_1))) {
                    Date inpool = StringToConvert(i, somSetlInvyScsCutdInfo.getScsCutdInpoolTime(), MAIN_CHECK_FIELD_1, MAIN_CHECK_NAME_1,
                            somSetlInvyScsCutdInfo.getSettleListId(), setlValidBaseBusiVo);
                    Date exit = StringToConvert(i, somSetlInvyScsCutdInfo.getScsCutdExitTime(), MAIN_CHECK_FIELD_2, MAIN_CHECK_NAME_2,
                            somSetlInvyScsCutdInfo.getSettleListId(), setlValidBaseBusiVo);

                    //如果大于入院时间
                    if (inpool.before(b12)) {
                        addErrorDetail(SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_ERROR_TYPE_2,
                                MAIN_CHECK_FIELD_1,
                                "第" + (i + 1) + "个重症监护: id为[" + somSetlInvyScsCutdInfo.getId() + "]的" + MAIN_CHECK_NAME_1 + "[" + inpool + "]小于于入院时间[" + b12 + "]",
                                SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
                    }

                    //是否小于出院时间
                    if (b15.before(exit)) {
                        addErrorDetail(SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_ERROR_TYPE_2,
                                MAIN_CHECK_FIELD_2,
                                "第" + (i + 1) + "个重症监护: id为[" + somSetlInvyScsCutdInfo.getId() + "]的" +  MAIN_CHECK_NAME_2 + "[" + exit + "]大于出院时间[" + b15 + "]",
                                SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
                    }

                    if (exit.before(inpool)) {
                        addErrorDetail(SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_ERROR_TYPE_2,
                                MAIN_CHECK_FIELD_1,
                                "第" + (i + 1) + "个重症监护: id为[" + somSetlInvyScsCutdInfo.getId() + "]的" +  MAIN_CHECK_NAME_2 + "[" + inpool + "]大于" + MAIN_CHECK_NAME_1 + "[" + exit + "]",
                                SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
                    }
                    if (SettleValidateUtil.isNotEmpty(somSetlInvyScsCutdInfo.getScsCutdSumDura())) {
                        //格式规范
                        if (!isValidFormat(somSetlInvyScsCutdInfo.getScsCutdSumDura())) {
                            addErrorDetail(SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_ERROR_TYPE_2,
                                    MAIN_CHECK_FIELD,
                                    "第" + (i + 1) + "个重症监护: id为[" + somSetlInvyScsCutdInfo.getId() + "]的" +  MAIN_CHECK_NAME + "[" + somSetlInvyScsCutdInfo.getScsCutdSumDura() + "]的日期填写格式错误",
                                    SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
                        } else {
                            //比较时间是否能匹配上
                            String diff = getDifference(somSetlInvyScsCutdInfo.getSettleListId(), somSetlInvyScsCutdInfo.getScsCutdInpoolTime(), somSetlInvyScsCutdInfo.getScsCutdExitTime());
                            if (!diff.equals(somSetlInvyScsCutdInfo.getScsCutdSumDura())) {
                                addErrorDetail(SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_ERROR_TYPE_2,
                                        MAIN_CHECK_FIELD,
                                        "第" + (i + 1) + "个重症监护: id为[" + somSetlInvyScsCutdInfo.getId() + "]的" +  MAIN_CHECK_NAME + "[" + somSetlInvyScsCutdInfo.getScsCutdSumDura() + "] 与 出入重症监护室时间差额 [" + diff + "] 不符",
                                        SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
                            }
                        }
                    } else {
                        addErrorDetail(SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_ERROR_TYPE_1,
                                MAIN_CHECK_FIELD,
                                "第" + (i + 1) + "个重症监护: id为[" + somSetlInvyScsCutdInfo.getId() + "]的" +  MAIN_CHECK_NAME + "[" + somSetlInvyScsCutdInfo.getScsCutdSumDura() + "]为空",
                                SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
                    }
                }
            }
        }

        return null;
    }

    private String getDifference(Long settleListId, String time1, String time2) {
        // 定义时间格式
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String formattedDifference = "";
        try {
            // 将时间字符串转换为 Date 对象
            Date date1 = dateFormat.parse(time1);
            Date date2 = dateFormat.parse(time2);

            // 计算时间差（毫秒）
            long differenceInMillis = date2.getTime() - date1.getTime();

            // 将毫秒差值转换为天数、小时数和分钟数
            long differenceInDays = TimeUnit.MILLISECONDS.toDays(differenceInMillis);
            long remainderMillisAfterDays = differenceInMillis % TimeUnit.DAYS.toMillis(1);
            long differenceInHours = TimeUnit.MILLISECONDS.toHours(remainderMillisAfterDays);
            long remainderMillisAfterHours = remainderMillisAfterDays % TimeUnit.HOURS.toMillis(1);
            long differenceInMinutes = TimeUnit.MILLISECONDS.toMinutes(remainderMillisAfterHours);

            // 格式化天数/小时数/分钟数字符串
            formattedDifference = String.format("%d/%d/%d",
                    differenceInDays, differenceInHours, differenceInMinutes);


        } catch (ParseException e) {
            logger.error("清单编码为[" + settleListId + "] 计算重症时间差失败: " + e.getMessage());
        }
        return formattedDifference;
    }


    /**
     * 判断格式是否规范
     *
     * @param input
     * @return
     */
    public boolean isValidFormat(String input) {

        // 简化的正则表达式：匹配两个正整数用斜杠分隔
        Matcher matcher = PATTERN.matcher(input);

        if (matcher.matches()) {
            try {
                int days = Integer.parseInt(matcher.group(1));
                int hours = Integer.parseInt(matcher.group(2));
                int minutes = Integer.parseInt(matcher.group(3));
                // 可选：进一步验证值的范围
                if (days >= 0 && hours >= 0 && hours < 24 && minutes >= 0 && minutes < 60) {
                    return true;
                }
            } catch (NumberFormatException e) {
                // 处理转换错误
                return false;
            }
        }
        return false;
    }

    private void addErrorDetail(String index, String errType, String errorFields, String errDscr, String chkStas, SetlValidBaseBusiVo baseBusiVo) {
        SettleListHandlerVo handlerVo = new SettleListHandlerVo();
        handlerVo.setIndex(index);
        handlerVo.setErrDscr(errDscr);
        handlerVo.setErrorFields(errorFields);
        handlerVo.setChkStas(chkStas);
        handlerVo.setErrType(errType);
        baseBusiVo.getErrorDetails().add(handlerVo);
    }

    private Date StringToConvert(int i, String time, String field, String name, Long settleListId, SetlValidBaseBusiVo setlValidBaseBusiVo) {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date date = new Date();
        try {
            date = dateFormat.parse(time);

        } catch (ParseException e) {
            addErrorDetail(SettleValidateConst.VALIDATE_TYPE_2, SettleValidateConst.VALIDATE_ERROR_TYPE_2,
                    field,
                    "第" + (i + 1) + "个的" + name + "[" + field + "]日期格式不符合规范",
                    SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
            logger.error("清单编码为[" + settleListId + "]的" + name + "日期解析失败: " + e.getMessage());
        }
        return date;
    }

    private Date StringToConvert(String time, String name) {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date date = new Date();
        try {
            date = dateFormat.parse(time);
        } catch (ParseException e) {
            logger.error(name + ":[" + time + "]" + "日期解析失败: " + e.getMessage());
        }
        return date;
    }
}
