package com.my.som.settlevalidate.script.depth;

import com.my.som.common.constant.DrgConst;
import com.my.som.dto.medicalQuality.BusDiseaseDiagnosisTrim;
import com.my.som.model.dataHandle.SomHiInvyBasInfo;
import com.my.som.settlevalidate.constant.SettleValidateConst;
import com.my.som.settlevalidate.util.SettleValidateUtil;
import com.my.som.settlevalidate.vo.SetlValidBaseBusiVo;
import com.my.som.vo.dataHandle.SettleListHandlerVo;
import com.my.som.vo.dataHandle.SettleListValidateVo;
import com.yomahub.liteflow.script.ScriptExecuteWrap;
import com.yomahub.liteflow.script.body.JaninoCommonScriptBody;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 事后  诊断  (西医)入院病情合理性
 */
public class check_wm_adm_cond_after_depth implements JaninoCommonScriptBody {
    private static final Logger logger = LoggerFactory.getLogger(check_wm_adm_cond_after_depth.class);

    private static final String MAIN_CHECK_FIELD = "c06c1";
    private static final String MAIN_CHECK_NAME = "出院主要诊断入院病情（西医）";

    private static final List<String> COND_LIST = Arrays.asList("1", "2", "3", "4");
    private static final String ADM_CODE_WAY_4 = "4";
    //1："有", 2："临床未确定", 3："情况不明", 4："无"
    private static final String MAIN_CHECK_REGION = "四川";
    /**
     * 事后 校验
     * 入院病情（西医）(wm_adm_cond) 基础质控
     * 判断出院主要诊断入院病情（西医）是否为空
     *
     * @param wrap
     * @return
     */
    @Override
    public Void body(ScriptExecuteWrap wrap) {
        SetlValidBaseBusiVo setlValidBaseBusiVo = (SetlValidBaseBusiVo) wrap.cmp.getFirstContextBean();
        SettleListValidateVo settleListValidateVo = (SettleListValidateVo) wrap.cmp.getRequestData();
        Map<String, String> keysBasicResultMap = setlValidBaseBusiVo.getKeysBasicResultMap();
        SomHiInvyBasInfo somHiInvyBasInfo = settleListValidateVo.getSomHiInvyBasInfo();
        SettleListHandlerVo settleListHandlerVo = new SettleListHandlerVo();
        List<BusDiseaseDiagnosisTrim> busDiseaseDiagnosisTrimsList = settleListValidateVo.getBusDiseaseDiagnosisTrimsList();
        // 检查 somHiInvyBasInfo 是否为空
        if (SettleValidateUtil.isEmpty(somHiInvyBasInfo)) {
           addErrorDetail( SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY, SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY,
                    (String) SettleValidateConst.CHECK_TYPE_MAP.get(SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY), MAIN_CHECK_FIELD,
                    SettleValidateConst.VALIDATE_STATE_FAIL, setlValidBaseBusiVo);
            return null;
        }
        // 检查 busDiseaseDiagnosisTrimsList(诊断) 是否为空
        if (SettleValidateUtil.isEmpty(busDiseaseDiagnosisTrimsList)) {
            return null;
        }
        for (int i = 0; i < busDiseaseDiagnosisTrimsList.size(); i++) {
            BusDiseaseDiagnosisTrim busDiseaseDiagnosisTrim = (BusDiseaseDiagnosisTrim) busDiseaseDiagnosisTrimsList.get(i);
            if (SettleValidateUtil.isNotEmpty(busDiseaseDiagnosisTrim.getC06c1())) {
                // 获取西医入院病情
                String c08c1 = busDiseaseDiagnosisTrim.getC08c1();
                if (SettleValidateUtil.isEmpty(c08c1)) {
                   addErrorDetail( SettleValidateConst.VALIDATE_TYPE_2, SettleValidateConst.VALIDATE_TYPE_1,
                           "第" + (i + 1) + "个疾病诊断：编码为[" +busDiseaseDiagnosisTrim.getC06c1()+"]的"+  MAIN_CHECK_NAME + "[" + c08c1 + "]为空", MAIN_CHECK_FIELD,
                            SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
                } else {
                    //判断是否符合入院病情规范
                    if (!COND_LIST.contains(c08c1)) {
                       addErrorDetail( SettleValidateConst.VALIDATE_TYPE_2, SettleValidateConst.VALIDATE_ERROR_TYPE_2,
                               "第" + (i + 1) + "个疾病诊断：编码为[" +busDiseaseDiagnosisTrim.getC06c1()+"]的"+ MAIN_CHECK_NAME + "[" + c08c1 + "]不符合入院病情规范性", MAIN_CHECK_FIELD,
                                SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
                    } else {
                        if (DrgConst.MAINDIAG.equals(busDiseaseDiagnosisTrim.getMainDiagFlag())) {
                            if (ADM_CODE_WAY_4.equals(c08c1)) {
                               addErrorDetail( SettleValidateConst.VALIDATE_TYPE_2, SettleValidateConst.VALIDATE_TYPE_1,
                                       "第" + (i + 1) + "个疾病诊断：编码为[" +busDiseaseDiagnosisTrim.getC06c1()+"]的"+  MAIN_CHECK_NAME + ":" + "主要病情不能为“无”", MAIN_CHECK_FIELD,
                                        SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
                            }
                        }
                    }
                }
            }
        }
        return null;
    }
    private void addErrorDetail( String index,String errType,String errDscr,String errorFields,  String chkStas, SetlValidBaseBusiVo baseBusiVo) {
        SettleListHandlerVo handlerVo =new SettleListHandlerVo();
        handlerVo.setIndex(index);
        handlerVo.setErrDscr(errDscr);
        handlerVo.setErrorFields(errorFields);
        handlerVo.setChkStas(chkStas);
        handlerVo.setErrType(errType);
        baseBusiVo.getErrorDetails().add(handlerVo);
    }
}

