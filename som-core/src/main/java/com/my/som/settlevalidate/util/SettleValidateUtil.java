package com.my.som.settlevalidate.util;

import com.my.som.common.exception.AppException;
import com.my.som.common.util.DateUtil;
import com.my.som.common.util.ValidateUtil;
import com.my.som.model.dataHandle.SomHiInvyBasInfo;
import com.my.som.settlevalidate.constant.SettleValidateConst;
import com.my.som.settlevalidate.vo.SetlValidBaseBusiVo;
import com.my.som.vo.dataHandle.SettleListHandlerVo;
import org.springframework.stereotype.Component;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.net.URL;
import java.net.URLConnection;
import java.sql.Time;
import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.ParsePosition;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.Year;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Component
public class SettleValidateUtil {
    public static final String module = ValidateUtil.class.getName();
    public static final boolean defaultEmptyOK = true;
    public static final String digits = "0123456789";
    public static final String lowercaseLetters = "abcdefghijklmnopqrstuvwxyz";
    public static final String uppercaseLetters = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
    public static final String letters = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ";
    public static final String whitespace = " \t\n\r";
    public static final String decimalPointDelimiter = ".";
    public static final String phoneNumberDelimiters = "()- ";
    public static final String validUSPhoneChars = "0123456789()- ";
    public static final String validWorldPhoneChars = "0123456789()- +";
    public static final String SSNDelimiters = "- ";
    public static final String validSSNChars = "0123456789- ";
    public static final int digitsInSocialSecurityNumber = 9;
    public static final int digitsInUSPhoneNumber = 10;
    public static final int digitsInUSPhoneAreaCode = 3;
    public static final int digitsInUSPhoneMainNumber = 7;
    public static final String ZipCodeDelimiters = "-";
    public static final String ZipCodeDelimeter = "-";
    public static final String validZipCodeChars = "0123456789-";
    public static final int digitsInZipCode1 = 5;
    public static final int digitsInZipCode2 = 9;
    public static final String creditCardDelimiters = " -";
    public static final int[] daysInMonth = new int[]{31, 29, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31};
    public static final String USStateCodeDelimiter = "|";
    public static final String USStateCodes = "AL|AK|AS|AZ|AR|CA|CO|CT|DE|DC|FM|FL|GA|GU|HI|ID|IL|IN|IA|KS|KY|LA|ME|MH|MD|MA|MI|MN|MS|MO|MT|NE|NV|NH|NJ|NM|NY|NC|ND|MP|OH|OK|OR|PW|PA|PR|RI|SC|SD|TN|TX|UT|VT|VI|VA|WA|WV|WI|WY|AE|AA|AE|AE|AP";
    public static final String ContiguousUSStateCodes = "AL|AZ|AR|CA|CO|CT|DE|DC|FL|GA|ID|IL|IN|IA|KS|KY|LA|ME|MD|MA|MI|MN|MS|MO|MT|NE|NV|NH|NJ|NM|NY|NC|ND|OH|OK|OR|PA|RI|SC|SD|TN|TX|UT|VT|VA|WA|WV|WI|WY";
    public static final List<String> lyfsCodeList = Arrays.asList("1", "2", "3", "4", "5", "9");

    public static final String ZZYSDM_REGEX_STR = "^D\\d{6}\\d{6}$";
    public static final String ZRHSDM_REGEX_STR = "^N\\d{6}\\d{6}$";
    // 创建Pattern对象
    public static final Pattern ZZYSDM_REGEX_PATTERN = Pattern.compile(ZZYSDM_REGEX_STR);
    public static final Pattern ZRHSDM_REGEX_PATTERN = Pattern.compile(ZRHSDM_REGEX_STR);

    public static final String TIMESTAMPFORMAT = "yyyy-MM-dd HH:mm:ss";
    public static final String TIMEFORMAT = "HH:mm:ss";
    public static final String DATEFORMAT = "yyyy-MM-dd";
    public static final String DATETIMEFORMAT = "yyyy-MM-dd HH:mm:ss";
    public static final int COMPUTE_YEAR = 1;
    public static final int COMPUTE_MONTH = 2;
    public static final int COMPUTE_DAY = 5;
    //邮政编码格式 \d{6}
    public static final String POS_CODE_PATTERN = "\\d{6}";

    //重症监护时长格式
    public static final String SCS_CUTD_SUM_DURA_PATTERN = "(\\d+)/(\\d+)/(\\d+)";
    //身份证号码格式
    public static final String ID_CARD_REGEX = "^(\\d{15}|\\d{17}(\\d|X))$";

    public SettleValidateUtil() {
    }

    public static boolean areEqual(Object obj, Object obj2) {
        if (obj == null) {
            return obj2 == null;
        } else {
            return obj.equals(obj2);
        }
    }

    public static boolean areEqualIgnoreCase(String obj, String obj2) {
        if (obj == null) {
            return obj2 == null;
        } else {
            return obj.equalsIgnoreCase(obj2);
        }
    }

    public static boolean isEmpty(Object value) {
        if (value == null) {
            return true;
        } else {
            if (value instanceof String) {
                if (((String) value).length() == 0) {
                    return true;
                }
            } else if (value instanceof Collection) {
                if (((Collection) value).size() == 0) {
                    return true;
                }
            } else if (value instanceof Map) {
                if (((Map) value).size() == 0) {
                    return true;
                }
            } else if (value instanceof String[] && ((String[]) ((String[]) value)).length == 0) {
                return true;
            }

            return false;
        }
    }

    public static boolean isEmpty(String s) {
        return s == null || s.length() == 0;
    }

    public static boolean isEmpty(Collection c) {
        return c == null || c.size() == 0;
    }

    public static boolean isNotEmpty(String s) {
        return s != null && s.length() > 0;
    }

    public static boolean isNotEmpty(Collection c) {
        return c != null && c.size() > 0;
    }

    public static boolean isString(Object obj) {
        return obj != null && obj instanceof String;
    }

    public static boolean isWhitespace(String s) {
        if (isEmpty(s)) {
            return true;
        } else {
            for (int i = 0; i < s.length(); ++i) {
                char c = s.charAt(i);
                if (" \t\n\r".indexOf(c) == -1) {
                    return false;
                }
            }

            return true;
        }
    }

    public static String stripCharsInBag(String s, String bag) {
        StringBuilder returnString = new StringBuilder("");

        for (int i = 0; i < s.length(); ++i) {
            char c = s.charAt(i);
            if (bag.indexOf(c) == -1) {
                returnString.append(c);
            }
        }

        return returnString.toString();
    }

    public static String stripCharsNotInBag(String s, String bag) {
        StringBuilder returnString = new StringBuilder("");

        for (int i = 0; i < s.length(); ++i) {
            char c = s.charAt(i);
            if (bag.indexOf(c) != -1) {
                returnString.append(c);
            }
        }

        return returnString.toString();
    }

    public static String stripWhitespace(String s) {
        return stripCharsInBag(s, " \t\n\r");
    }

    public static boolean charInString(char c, String s) {
        return s.indexOf(c) != -1;
    }

    public static String stripInitialWhitespace(String s) {
        int i;
        for (i = 0; i < s.length() && charInString(s.charAt(i), " \t\n\r"); ++i) {
            ;
        }

        return s.substring(i);
    }

    public static boolean isLetter(char c) {
        return Character.isLetter(c);
    }

    public static boolean isDigit(char c) {
        return Character.isDigit(c);
    }

    public static boolean isLetterOrDigit(char c) {
        return Character.isLetterOrDigit(c);
    }

    public static boolean isInteger(String s) {
        if (isEmpty(s)) {
            return true;
        } else {
            for (int i = 0; i < s.length(); ++i) {
                char c = s.charAt(i);
                if (!isDigit(c)) {
                    return false;
                }
            }

            return true;
        }
    }

    public static boolean isSignedInteger(String s) {
        if (isEmpty(s)) {
            return true;
        } else {
            try {
                Integer.parseInt(s);
                return true;
            } catch (Exception var2) {
                return false;
            }
        }
    }

    public static boolean isSignedLong(String s) {
        if (isEmpty(s)) {
            return true;
        } else {
            try {
                Long.parseLong(s);
                return true;
            } catch (Exception var2) {
                return false;
            }
        }
    }

    public static boolean isPositiveInteger(String s) {
        if (isEmpty(s)) {
            return true;
        } else {
            try {
                long temp = Long.parseLong(s);
                return temp > 0L;
            } catch (Exception var3) {
                return false;
            }
        }
    }

    public static boolean isNonnegativeInteger(String s) {
        if (isEmpty(s)) {
            return true;
        } else {
            try {
                int temp = Integer.parseInt(s);
                return temp >= 0;
            } catch (Exception var2) {
                return false;
            }
        }
    }

    public static boolean isNegativeInteger(String s) {
        if (isEmpty(s)) {
            return true;
        } else {
            try {
                int temp = Integer.parseInt(s);
                return temp < 0;
            } catch (Exception var2) {
                return false;
            }
        }
    }

    public static boolean isNonpositiveInteger(String s) {
        if (isEmpty(s)) {
            return true;
        } else {
            try {
                int temp = Integer.parseInt(s);
                return temp <= 0;
            } catch (Exception var2) {
                return false;
            }
        }
    }

    public static boolean isFloat(String s) {
        if (isEmpty(s)) {
            return true;
        } else {
            boolean seenDecimalPoint = false;
            if (s.startsWith(".")) {
                return false;
            } else {
                for (int i = 0; i < s.length(); ++i) {
                    char c = s.charAt(i);
                    if (c == ".".charAt(0)) {
                        if (seenDecimalPoint) {
                            return false;
                        }

                        seenDecimalPoint = true;
                    } else if (!isDigit(c)) {
                        return false;
                    }
                }

                return true;
            }
        }
    }

    public static boolean isSignedFloat(String s) {
        if (isEmpty(s)) {
            return true;
        } else {
            try {
                float temp = Float.parseFloat(s);
                return temp <= 0.0F;
            } catch (Exception var2) {
                return false;
            }
        }
    }

    public static boolean isSignedDouble(String s) {
        if (isEmpty(s)) {
            return true;
        } else {
            try {
                Double.parseDouble(s);
                return true;
            } catch (Exception var2) {
                return false;
            }
        }
    }

    public static boolean isAlphabetic(String s) {
        if (isEmpty(s)) {
            return true;
        } else {
            for (int i = 0; i < s.length(); ++i) {
                char c = s.charAt(i);
                if (!isLetter(c)) {
                    return false;
                }
            }

            return true;
        }
    }

    public static boolean isAlphanumeric(String s) {
        if (isEmpty(s)) {
            return true;
        } else {
            for (int i = 0; i < s.length(); ++i) {
                char c = s.charAt(i);
                if (!isLetterOrDigit(c)) {
                    return false;
                }
            }

            return true;
        }
    }

    public static boolean isSSN(String s) {
        if (isEmpty(s)) {
            return true;
        } else {
            String normalizedSSN = stripCharsInBag(s, "- ");
            return isInteger(normalizedSSN) && normalizedSSN.length() == 9;
        }
    }

    public static boolean isUSPhoneNumber(String s) {
        if (isEmpty(s)) {
            return true;
        } else {
            String normalizedPhone = stripCharsInBag(s, "()- ");
            return isInteger(normalizedPhone) && normalizedPhone.length() == 10;
        }
    }

    public static boolean isUSPhoneAreaCode(String s) {
        if (isEmpty(s)) {
            return true;
        } else {
            String normalizedPhone = stripCharsInBag(s, "()- ");
            return isInteger(normalizedPhone) && normalizedPhone.length() == 3;
        }
    }

    public static boolean isUSPhoneMainNumber(String s) {
        if (isEmpty(s)) {
            return true;
        } else {
            String normalizedPhone = stripCharsInBag(s, "()- ");
            return isInteger(normalizedPhone) && normalizedPhone.length() == 7;
        }
    }

    public static boolean isInternationalPhoneNumber(String s) {
        if (isEmpty(s)) {
            return true;
        } else {
            String normalizedPhone = stripCharsInBag(s, "()- ");
            return isPositiveInteger(normalizedPhone);
        }
    }

    public static boolean isZipCode(String s) {
        if (isEmpty(s)) {
            return true;
        } else {
            String normalizedZip = stripCharsInBag(s, "-");
            return isInteger(normalizedZip) && (normalizedZip.length() == 5 || normalizedZip.length() == 9);
        }
    }

    public static boolean isContiguousZipCode(String s) {
        boolean retval = false;
        if (isZipCode(s)) {
            if (isEmpty(s)) {
                retval = true;
            } else {
                String normalizedZip = s.substring(0, 5);
                int iZip = Integer.parseInt(normalizedZip);
                if ((iZip < 96701 || iZip > 96898) && (iZip < 99501 || iZip > 99950)) {
                    retval = true;
                } else {
                    retval = false;
                }
            }
        }

        return retval;
    }

    public static boolean isStateCode(String s) {
        if (isEmpty(s)) {
            return true;
        } else {
            return "AL|AK|AS|AZ|AR|CA|CO|CT|DE|DC|FM|FL|GA|GU|HI|ID|IL|IN|IA|KS|KY|LA|ME|MH|MD|MA|MI|MN|MS|MO|MT|NE|NV|NH|NJ|NM|NY|NC|ND|MP|OH|OK|OR|PW|PA|PR|RI|SC|SD|TN|TX|UT|VT|VI|VA|WA|WV|WI|WY|AE|AA|AE|AE|AP".contains(s) && !s.contains("|");
        }
    }

    public static boolean isContiguousStateCode(String s) {
        if (isEmpty(s)) {
            return true;
        } else {
            return "AL|AZ|AR|CA|CO|CT|DE|DC|FL|GA|ID|IL|IN|IA|KS|KY|LA|ME|MD|MA|MI|MN|MS|MO|MT|NE|NV|NH|NJ|NM|NY|NC|ND|OH|OK|OR|PA|RI|SC|SD|TN|TX|UT|VT|VA|WA|WV|WI|WY".contains(s) && !s.contains("|");
        }
    }

    public static boolean isEmail(String s) {
        if (isEmpty(s)) {
            return true;
        } else if (isWhitespace(s)) {
            return false;
        } else {
            int i = 1;

            int sLength;
            for (sLength = s.length(); i < sLength && s.charAt(i) != '@'; ++i) {
                ;
            }

            return i < sLength - 1 && s.charAt(i) == '@';
        }
    }

    public static boolean isUrl(String s) {
        if (isEmpty(s)) {
            return true;
        } else {
            return s.contains("://");
        }
    }

    public static boolean isYear(String s) {
        if (isEmpty(s)) {
            return true;
        } else if (!isNonnegativeInteger(s)) {
            return false;
        } else {
            return s.length() == 2 || s.length() == 4;
        }
    }

    public static boolean isIntegerInRange(String s, int a, int b) {
        if (isEmpty(s)) {
            return true;
        } else if (!isSignedInteger(s)) {
            return false;
        } else {
            int cnt = Integer.parseInt(s);
            return cnt >= a && cnt <= b;
        }
    }

    public static boolean isMonth(String s) {
        return isEmpty(s) ? true : isIntegerInRange(s, 1, 12);
    }

    public static boolean isDay(String s) {
        return isEmpty(s) ? true : isIntegerInRange(s, 1, 31);
    }

    public static int daysInFebruary(int year) {
        return year % 4 != 0 || year % 100 == 0 && year % 400 != 0 ? 28 : 29;
    }

    public static boolean isHour(String s) {
        return isEmpty(s) ? true : isIntegerInRange(s, 0, 23);
    }

    public static boolean isMinute(String s) {
        return isEmpty(s) ? true : isIntegerInRange(s, 0, 59);
    }

    public static boolean isSecond(String s) {
        return isEmpty(s) ? true : isIntegerInRange(s, 0, 59);
    }

    public static boolean isDate(String year, String month, String day) {
        if (isYear(year) && isMonth(month) && isDay(day)) {
            int intYear = Integer.parseInt(year);
            int intMonth = Integer.parseInt(month);
            int intDay = Integer.parseInt(day);
            if (intDay > daysInMonth[intMonth - 1]) {
                return false;
            } else {
                return intMonth != 2 || intDay <= daysInFebruary(intYear);
            }
        } else {
            return false;
        }
    }

    public static boolean isDate(String updt_date) {
        if (isEmpty(updt_date)) {
            return true;
        } else {
            int dateSlash1 = updt_date.indexOf("-");
            int dateSlash2 = updt_date.lastIndexOf("-");
            if (dateSlash1 > 0 && dateSlash1 != dateSlash2) {
                String year = updt_date.substring(0, dateSlash1);
                String month = updt_date.substring(dateSlash1 + 1, dateSlash2);
                String day = updt_date.substring(dateSlash2 + 1);
                return isDate(year, month, day);
            } else {
                return false;
            }
        }
    }

    public static boolean isDateAfterToday(String updt_date) {
        if (isEmpty(updt_date)) {
            return true;
        } else {
            int dateSlash1 = updt_date.indexOf("-");
            int dateSlash2 = updt_date.lastIndexOf("-");
            if (dateSlash1 <= 0) {
                return false;
            } else {
                Date passed = null;
                String year;
                String month;
                if (dateSlash1 == dateSlash2) {
                    year = updt_date.substring(0, dateSlash1);
                    month = "28";
                    month = updt_date.substring(dateSlash1 + 1);
                    if (!isDate(year, month, month)) {
                        return false;
                    }

                    try {
                        int monthInt = Integer.parseInt(month);
                        int yearInt = Integer.parseInt(year);
                        Calendar calendar = Calendar.getInstance();
                        calendar.set(yearInt, monthInt - 1, 0, 0, 0, 0);
                        calendar.add(2, 1);
                        passed = new Date(calendar.getTime().getTime());
                    } catch (Exception var10) {
                        passed = null;
                    }
                } else {
                    year = updt_date.substring(0, dateSlash1);
                    month = updt_date.substring(dateSlash1 + 1, dateSlash2);
                    month = updt_date.substring(dateSlash2 + 1);
                    if (!isDate(year, month, month)) {
                        return false;
                    }

                    passed = DateUtil.toDate(month, month, year, "0", "0", "0");
                }

                Date now = DateUtil.nowDate();
                return passed != null ? passed.after(now) : false;
            }
        }
    }

    public static boolean isDateAfterDate(String updt_date, java.sql.Date theDate) {
        if (isEmpty(updt_date)) {
            return true;
        } else {
            int dateSlash1 = updt_date.indexOf("-");
            int dateSlash2 = updt_date.lastIndexOf("-");
            if (dateSlash1 <= 0) {
                return false;
            } else {
                Date passed = null;
                String year;
                String month;
                if (dateSlash1 == dateSlash2) {
                    year = updt_date.substring(0, dateSlash1);
                    month = "28";
                    month = updt_date.substring(dateSlash1 + 1);
                    if (!isDate(year, month, month)) {
                        return false;
                    }

                    try {
                        int monthInt = Integer.parseInt(month);
                        int yearInt = Integer.parseInt(year);
                        Calendar calendar = Calendar.getInstance();
                        calendar.set(yearInt, monthInt - 1, 0, 0, 0, 0);
                        calendar.add(2, 1);
                        passed = new Date(calendar.getTime().getTime());
                    } catch (Exception var11) {
                        passed = null;
                    }
                } else {
                    year = updt_date.substring(0, dateSlash1);
                    month = updt_date.substring(dateSlash1 + 1, dateSlash2);
                    month = updt_date.substring(dateSlash2 + 1);
                    if (!isDate(year, month, month)) {
                        return false;
                    }

                    passed = DateUtil.getUpdt_date(year, month, month);
                }

                return passed != null ? ((Date) passed).after(theDate) : false;
            }
        }
    }

    public static boolean isTime(String hour, String minute, String second) {
        return isHour(hour) && isMinute(minute) && isSecond(second);
    }

    public static boolean isTime(String time) {
        if (isEmpty(time)) {
            return true;
        } else {
            int timeColon1 = time.indexOf(":");
            int timeColon2 = time.lastIndexOf(":");
            if (timeColon1 <= 0) {
                return false;
            } else {
                String hour = time.substring(0, timeColon1);
                String minute;
                String second;
                if (timeColon1 == timeColon2) {
                    minute = time.substring(timeColon1 + 1);
                    second = "0";
                } else {
                    minute = time.substring(timeColon1 + 1, timeColon2);
                    second = time.substring(timeColon2 + 1);
                }

                return isTime(hour, minute, second);
            }
        }
    }

    public static boolean isValueLinkCard(String stPassed) {
        if (isEmpty(stPassed)) {
            return true;
        } else {
            String st = stripCharsInBag(stPassed, " -");
            return st.length() == 16 && (st.startsWith("7") || st.startsWith("6"));
        }
    }

    public static boolean isGiftCard(String stPassed) {
        return isValueLinkCard(stPassed);
    }

    public static int getLuhnSum(String stPassed) {
        stPassed = stPassed.replaceAll("\\D", "");
        int len = stPassed.length();
        int sum = 0;
        int mul = 1;

        for (int i = len - 1; i >= 0; --i) {
            int digit = Character.digit(stPassed.charAt(i), 10);
            digit *= mul == 1 ? mul++ : mul--;
            sum += digit >= 10 ? digit % 10 + 1 : digit;
        }

        return sum;
    }

    public static int getLuhnCheckDigit(String stPassed) {
        int sum = getLuhnSum(stPassed);
        int mod = ((sum / 10 + 1) * 10 - sum) % 10;
        return 10 - mod;
    }

    public static boolean sumIsMod10(int sum) {
        return sum % 10 == 0;
    }

    public static String appendCheckDigit(String stPassed) {
        String checkDigit = Integer.valueOf(getLuhnCheckDigit(stPassed)).toString();
        return stPassed + checkDigit;
    }

    public static boolean isCreditCard(String stPassed) {
        if (isEmpty(stPassed)) {
            return true;
        } else {
            String st = stripCharsInBag(stPassed, " -");
            return st.length() > 19 ? false : sumIsMod10(getLuhnSum(st));
        }
    }

    public static boolean isVisa(String cc) {
        return (cc.length() == 16 || cc.length() == 13) && cc.substring(0, 1).equals("4") ? isCreditCard(cc) : false;
    }

    public static boolean isMasterCard(String cc) {
        int firstdig = Integer.parseInt(cc.substring(0, 1));
        int seconddig = Integer.parseInt(cc.substring(1, 2));
        return cc.length() == 16 && firstdig == 5 && seconddig >= 1 && seconddig <= 5 ? isCreditCard(cc) : false;
    }

    public static boolean isAmericanExpress(String cc) {
        int firstdig = Integer.parseInt(cc.substring(0, 1));
        int seconddig = Integer.parseInt(cc.substring(1, 2));
        return cc.length() != 15 || firstdig != 3 || seconddig != 4 && seconddig != 7 ? false : isCreditCard(cc);
    }

    public static boolean isDinersClub(String cc) {
        int firstdig = Integer.parseInt(cc.substring(0, 1));
        int seconddig = Integer.parseInt(cc.substring(1, 2));
        return cc.length() != 14 || firstdig != 3 || seconddig != 0 && seconddig != 6 && seconddig != 8 ? false : isCreditCard(cc);
    }

    public static boolean isCarteBlanche(String cc) {
        return isDinersClub(cc);
    }

    public static boolean isDiscover(String cc) {
        String first4digs = cc.substring(0, 4);
        return cc.length() == 16 && first4digs.equals("6011") ? isCreditCard(cc) : false;
    }

    public static boolean isEnRoute(String cc) {
        String first4digs = cc.substring(0, 4);
        return cc.length() != 15 || !first4digs.equals("2014") && !first4digs.equals("2149") ? false : isCreditCard(cc);
    }

    public static boolean isJCB(String cc) {
        String first4digs = cc.substring(0, 4);
        return cc.length() != 16 || !first4digs.equals("3088") && !first4digs.equals("3096") && !first4digs.equals("3112") && !first4digs.equals("3158") && !first4digs.equals("3337") && !first4digs.equals("3528") ? false : isCreditCard(cc);
    }

    public static boolean isAnyCard(String ccPassed) {
        if (isEmpty(ccPassed)) {
            return true;
        } else {
            String cc = stripCharsInBag(ccPassed, " -");
            if (!isCreditCard(cc)) {
                return false;
            } else {
                return isMasterCard(cc) || isVisa(cc) || isAmericanExpress(cc) || isDinersClub(cc) || isDiscover(cc) || isEnRoute(cc) || isJCB(cc);
            }
        }
    }

    public static String getCardType(String ccPassed) {
        if (isEmpty(ccPassed)) {
            return "Unknown";
        } else {
            String cc = stripCharsInBag(ccPassed, " -");
            if (!isCreditCard(cc)) {
                return "Unknown";
            } else if (isMasterCard(cc)) {
                return "MasterCard";
            } else if (isVisa(cc)) {
                return "Visa";
            } else if (isAmericanExpress(cc)) {
                return "AmericanExpress";
            } else if (isDinersClub(cc)) {
                return "DinersClub";
            } else if (isDiscover(cc)) {
                return "Discover";
            } else if (isEnRoute(cc)) {
                return "EnRoute";
            } else {
                return isJCB(cc) ? "JCB" : "Unknown";
            }
        }
    }

    public static boolean isCardMatch(String cardType, String cardNumberPassed) {
        if (isEmpty(cardType)) {
            return true;
        } else if (isEmpty(cardNumberPassed)) {
            return true;
        } else {
            String cardNumber = stripCharsInBag(cardNumberPassed, " -");
            if (cardType.equalsIgnoreCase("VISA") && isVisa(cardNumber)) {
                return true;
            } else if (cardType.equalsIgnoreCase("MASTERCARD") && isMasterCard(cardNumber)) {
                return true;
            } else if ((cardType.equalsIgnoreCase("AMERICANEXPRESS") || cardType.equalsIgnoreCase("AMEX")) && isAmericanExpress(cardNumber)) {
                return true;
            } else if (cardType.equalsIgnoreCase("DISCOVER") && isDiscover(cardNumber)) {
                return true;
            } else if (cardType.equalsIgnoreCase("JCB") && isJCB(cardNumber)) {
                return true;
            } else if ((cardType.equalsIgnoreCase("DINERSCLUB") || cardType.equalsIgnoreCase("DINERS")) && isDinersClub(cardNumber)) {
                return true;
            } else if (cardType.equalsIgnoreCase("CARTEBLANCHE") && isCarteBlanche(cardNumber)) {
                return true;
            } else {
                return cardType.equalsIgnoreCase("ENROUTE") && isEnRoute(cardNumber);
            }
        }
    }

    public static boolean isNotPoBox(String s) {
        if (isEmpty(s)) {
            return true;
        } else {
            String sl = s.toLowerCase();
            if (sl.contains("p.o. b")) {
                return false;
            } else if (sl.contains("p.o.b")) {
                return false;
            } else if (sl.contains("p.o b")) {
                return false;
            } else if (sl.contains("p o b")) {
                return false;
            } else if (sl.contains("po b")) {
                return false;
            } else if (sl.contains("pobox")) {
                return false;
            } else if (sl.contains("po#")) {
                return false;
            } else if (sl.contains("po #")) {
                return false;
            } else if (sl.contains("p.0. b")) {
                return false;
            } else if (sl.contains("p.0.b")) {
                return false;
            } else if (sl.contains("p.0 b")) {
                return false;
            } else if (sl.contains("p 0 b")) {
                return false;
            } else if (sl.contains("p0 b")) {
                return false;
            } else if (sl.contains("p0box")) {
                return false;
            } else if (sl.contains("p0#")) {
                return false;
            } else {
                return !sl.contains("p0 #");
            }
        }
    }


    /**
     * 获取当前时间
     *
     * @param pattern 规则 example: yyyy-MM-dd
     * @return
     */
    public static String getCurTimeByPattern(String pattern) {
        try {
            DateTimeFormatter fmDate = DateTimeFormatter.ofPattern(pattern);
            LocalDate today = LocalDate.now();
            return today.format(fmDate);
        } catch (Exception e) {
            e.printStackTrace();
            throw new AppException("格式化错误，当前pattern为：" + pattern);
        }
    }

    /**
     * 根据当前月份获取上月
     * 需要提供的格式为 yyyy-MM
     *
     * @param curMonth 当前月
     * @return
     */
    public static String getLastMonthByCurMonth(String curMonth) {
        YearMonth yearMonth = YearMonth.parse(curMonth);
        return yearMonth.minus(1, ChronoUnit.MONTHS).toString();
    }

    /**
     * 根据当前月份获取上年同月
     * 需要提供的格式为 yyyy-MM
     *
     * @param curMonth 当前月
     * @return
     */
    public static String getLastYearMonthByCurMonth(String curMonth) {
        YearMonth yearMonth = YearMonth.parse(curMonth);
        return yearMonth.minus(1, ChronoUnit.YEARS).toString();
    }

    /**
     * 根据当前年份获取上年
     * 需要提供格式为 yyyy
     *
     * @param curYear 当前年份
     * @return
     */
    public static String getLastYearByCurYear(String curYear) {
        Year year = Year.parse(curYear);
        return year.minus(1, ChronoUnit.YEARS).toString();
    }

    public static String getFullCurrentTime() {
        String returnStr = null;
        long currentTime = System.currentTimeMillis();
        Date updt_date = new Date(currentTime);
        returnStr = DateFormat.getDateTimeInstance().format(updt_date);
        return returnStr;
    }

    public static String getCurrentTime() {
        String returnStr = null;
        SimpleDateFormat f = new SimpleDateFormat("yyyyMMddHHmmss");
        Date updt_date = new Date();
        returnStr = f.format(updt_date);
        return returnStr;
    }

    public static String getCurrentTime(String format) {
        String returnStr = null;
        SimpleDateFormat f = new SimpleDateFormat(format);
        Date updt_date = new Date();
        returnStr = f.format(updt_date);
        return returnStr;
    }

    public static Date getCurrentDateTime() {
        return new Date(System.currentTimeMillis());
    }

    public static int getWeekOfYear() {
        Calendar oneCalendar = Calendar.getInstance();
        return oneCalendar.get(3);
    }

    public static int getMonth() {
        Calendar oneCalendar = Calendar.getInstance();
        return oneCalendar.get(2) + 1;
    }

    public static String getCurDateTime() {
        Calendar calendar = Calendar.getInstance();
        SimpleDateFormat simpledateformat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String s = simpledateformat.format(calendar.getTime());
        return s;
    }

    public static Date getCurDate() {
        Calendar calendar = Calendar.getInstance();
        SimpleDateFormat simpledateformat = new SimpleDateFormat("yyyy-MM-dd");
        String s = simpledateformat.format(calendar.getTime());
        return java.sql.Date.valueOf(s);
    }

    public static java.sql.Date getUpdt_date() {
        Calendar oneCalendar = Calendar.getInstance();
        return getUpdt_date(oneCalendar.get(1), oneCalendar.get(2) + 1, oneCalendar.get(5));
    }

    public static java.sql.Date getUpdt_date(int yyyy, int MM, int dd) {
        if (!verityDate(yyyy, MM, dd)) {
            throw new IllegalArgumentException("This is illegimate updt_date!");
        } else {
            Calendar oneCalendar = Calendar.getInstance();
            oneCalendar.clear();
            oneCalendar.set(yyyy, MM - 1, dd);
            return new java.sql.Date(oneCalendar.getTime().getTime());
        }
    }

    public static java.sql.Date getUpdt_date(String year, String month, String day) {
        int iYear = Integer.parseInt(year);
        int iMonth = Integer.parseInt(month);
        int iDay = Integer.parseInt(day);
        return getUpdt_date(iYear, iMonth, iDay);
    }

    public static Date getUpdt_date(int month, int day, int year, int hour, int minute, int second) {
        Calendar calendar = Calendar.getInstance();

        try {
            calendar.set(year, month - 1, day, hour, minute, second);
        } catch (Exception var8) {
            return null;
        }

        return new Date(calendar.getTime().getTime());
    }

    public static Date getUpdt_date(String month, String day, String year, String hour, String minute, String second) {
        int iYear = Integer.parseInt(year);
        int iMonth = Integer.parseInt(month);
        int iDay = Integer.parseInt(day);
        int iHour = Integer.parseInt(hour);
        int iMinute = Integer.parseInt(minute);
        int iSecond = Integer.parseInt(second);
        return getUpdt_date(iMonth, iDay, iYear, iHour, iMinute, iSecond);
    }

    public static boolean verityDate(int yyyy, int MM, int dd) {
        boolean flag = false;
        if (MM >= 1 && MM <= 12 && dd >= 1 && dd <= 31) {
            if (MM != 4 && MM != 6 && MM != 9 && MM != 11) {
                if (MM == 2) {
                    if ((yyyy % 100 == 0 || yyyy % 4 != 0) && yyyy % 400 != 0) {
                        if (dd <= 28) {
                            flag = true;
                        }
                    } else if (dd <= 29) {
                        flag = true;
                    }
                } else {
                    flag = true;
                }
            } else if (dd <= 30) {
                flag = true;
            }
        }

        return flag;
    }

    public static String dateToString(Date updt_date) {
        if (updt_date == null) {
            return null;
        } else {
            String strDate = "";
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
            strDate = simpleDateFormat.format(updt_date);
            return strDate;
        }
    }

    public static String datetimeToString(Date updt_date) {
        if (updt_date == null) {
            return null;
        } else {
            String strDate = "";
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            strDate = simpleDateFormat.format(updt_date);
            return strDate;
        }
    }

    public static String datetimeToString(Date updt_date, String format) {
        if (updt_date == null) {
            return null;
        } else {
            String strDate = "";
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat(format);
            strDate = simpleDateFormat.format(updt_date);
            return strDate;
        }
    }

    public static String timeToString(Date updt_date) {
        if (updt_date == null) {
            return null;
        } else {
            String strDate = "";
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("HH:mm:ss");
            strDate = simpleDateFormat.format(updt_date);
            return strDate;
        }
    }

    public static Date stringToDate(String strDate, String srcDateFormat, String dstDateFormat) {
        Date rtDate = null;
        Date tmpDate = (new SimpleDateFormat(srcDateFormat)).parse(strDate, new ParsePosition(0));
        String tmpString = null;
        if (tmpDate != null) {
            tmpString = (new SimpleDateFormat(dstDateFormat)).format(tmpDate);
        }

        if (tmpString != null) {
            rtDate = (new SimpleDateFormat(dstDateFormat)).parse(tmpString, new ParsePosition(0));
        }

        return rtDate;
    }

    public static Date stringToDate(String strDate, String srcDateFormat) {
        return (new SimpleDateFormat(srcDateFormat)).parse(strDate, new ParsePosition(0));
    }

    public static Date stringToDate(String strDate) {
        return stringToDate(strDate, "yyyy-MM-dd");
    }

    public static java.sql.Date utilDateToSqlDate(Date updt_date) {
        return updt_date == null ? null : new java.sql.Date(updt_date.getTime());
    }

    public static Time utilDateToSqlTime(Date updt_date) {
        return updt_date == null ? null : new Time(updt_date.getTime());
    }

    public static Timestamp utilDateToSqlTimestamp(Date updt_date) {
        return updt_date == null ? null : new Timestamp(updt_date.getTime());
    }

    public static java.sql.Date stringToSqlDate(String strDate) {
        return stringToSqlDate(strDate, "yyyy-MM-dd");
    }

    public static java.sql.Date stringToSqlDate(String strDate, String srcDateFormat) {
        return stringToSqlDate(strDate, srcDateFormat, "yyyy-MM-dd");
    }

    public static java.sql.Date stringToSqlDate(String strDate, String srcDateFormat, String dstDateFormat) {
        return utilDateToSqlDate(stringToDate(strDate, srcDateFormat, dstDateFormat));
    }

    public static Time stringToSqlTime(String strDate) {
        return stringToSqlTime(strDate, "HH:mm:ss");
    }

    public static Time stringToSqlTime(String strDate, String srcDateFormat) {
        return stringToSqlTime(strDate, srcDateFormat, "HH:mm:ss");
    }

    public static Time stringToSqlTime(String strDate, String srcDateFormat, String dstDateFormat) {
        return utilDateToSqlTime(stringToDate(strDate, srcDateFormat, dstDateFormat));
    }

    public static Timestamp stringToSqlTimestamp(String strDate) {
        return stringToSqlTimestamp(strDate, "yyyy-MM-dd HH:mm:ss");
    }

    public static Timestamp stringToSqlTimestamp(String strDate, String srcDateFormat) {
        return stringToSqlTimestamp(strDate, srcDateFormat, "yyyy-MM-dd HH:mm:ss");
    }

    public static Timestamp stringToSqlTimestamp(String strDate, String srcDateFormat, String dstDateFormat) {
        return utilDateToSqlTimestamp(stringToDate(strDate, srcDateFormat, dstDateFormat));
    }

    public static String toTimeString(int hour, int minute, int second) {
        String hourStr;
        if (hour < 10) {
            hourStr = "0" + hour;
        } else {
            hourStr = "" + hour;
        }

        String minuteStr;
        if (minute < 10) {
            minuteStr = "0" + minute;
        } else {
            minuteStr = "" + minute;
        }

        String secondStr;
        if (second < 10) {
            secondStr = "0" + second;
        } else {
            secondStr = "" + second;
        }

        return second == 0 ? hourStr + ":" + minuteStr : hourStr + ":" + minuteStr + ":" + secondStr;
    }

    public static java.sql.Date getDayStart(java.sql.Date updt_date) {
        return getDayStart(updt_date, 0);
    }

    public static java.sql.Date getDayStart(java.sql.Date updt_date, int daysLater) {
        Calendar tempCal = Calendar.getInstance();
        tempCal.setTime(new Date(updt_date.getTime()));
        tempCal.set(tempCal.get(1), tempCal.get(2), tempCal.get(5), 0, 0, 0);
        tempCal.add(5, daysLater);
        return new java.sql.Date(tempCal.getTime().getTime());
    }

    public static java.sql.Date getNextDayStart(java.sql.Date updt_date) {
        return getDayStart(updt_date, 1);
    }

    public static java.sql.Date getDayEnd(java.sql.Date stamp) {
        return getDayEnd(stamp, 0);
    }

    public static java.sql.Date getDayEnd(java.sql.Date stamp, int daysLater) {
        Calendar tempCal = Calendar.getInstance();
        tempCal.setTime(new Date(stamp.getTime()));
        tempCal.set(tempCal.get(1), tempCal.get(2), tempCal.get(5), 23, 59, 59);
        tempCal.add(5, daysLater);
        return new java.sql.Date(tempCal.getTime().getTime());
    }

    public static java.sql.Date monthBegin() {
        Calendar mth = Calendar.getInstance();
        mth.set(5, 1);
        mth.set(11, 0);
        mth.set(12, 0);
        mth.set(13, 0);
        mth.set(9, 0);
        return new java.sql.Date(mth.getTime().getTime());
    }

    public static String getDateTimeDisp(String datetime) {
        if (datetime != null && !datetime.equals("")) {
            DateFormat formatter = DateFormat.getDateTimeInstance(2, 2);
            long datel = Long.parseLong(datetime);
            return formatter.format(new Date(datel));
        } else {
            return "";
        }
    }

    private static int compute(Date arg1, Date arg2, int computeFlag, boolean bExact) {
        Calendar ca1 = Calendar.getInstance();
        ca1.setTime(arg1);
        ca1.set(10, 0);
        ca1.set(12, 0);
        ca1.set(13, 0);
        ca1.set(14, 0);
        Calendar ca2 = Calendar.getInstance();
        ca2.setTime(arg2);
        ca2.set(10, 0);
        ca2.set(12, 0);
        ca2.set(13, 0);
        ca2.set(14, 0);
        int elapsed = 0;
        if (ca1.after(ca2)) {
            while (ca1.after(ca2)) {
                ca1.add(computeFlag, -1);
                --elapsed;
            }

            if (bExact) {
                if (2 == computeFlag && ca1.get(5) != ca2.get(5)) {
                    ++elapsed;
                }

                if (1 == computeFlag) {
                    if (ca1.get(2) != ca2.get(2)) {
                        ++elapsed;
                    } else if (ca1.get(5) != ca2.get(5)) {
                        ++elapsed;
                    }
                }
            }

            return -elapsed;
        } else if (!ca1.before(ca2)) {
            return 0;
        } else {
            while (ca1.before(ca2)) {
                ca1.add(computeFlag, 1);
                ++elapsed;
            }

            if (bExact) {
                if (2 == computeFlag && ca1.get(5) != ca2.get(5)) {
                    --elapsed;
                }

                if (1 == computeFlag) {
                    if (ca1.get(2) > ca2.get(2)) {
                        --elapsed;
                    } else if (ca1.get(5) != ca2.get(5)) {
                        --elapsed;
                    }
                }
            }

            return -elapsed;
        }
    }

    public static int computeDay(Date arg1, Date arg2) {
        return compute(arg1, arg2, 5, true);
    }

    public static String fnGetStr4Y2M(String szStr) {
        String szRst = szStr.replaceAll("[ \\|\\-:\\.]", "");
        szRst = szRst.substring(0, 6);
        return szRst;
    }

    public static int getIntervalMonth(Date begnDate, Date expiDate) {
        return computeMonthOnly(begnDate, expiDate);
    }

    public static int getIntervalDay(java.sql.Date begnDate, java.sql.Date expiDate) {
        long startdate = begnDate.getTime();
        long enddate = expiDate.getTime();
        long interval = enddate - startdate;
        int intervalday = (int) (interval / 86400000L);
        return intervalday;
    }

    public static int getIntervalDay(Timestamp begnDate, Timestamp expiDate) {
        Calendar calendar1 = Calendar.getInstance();
        Calendar calendar2 = Calendar.getInstance();
        calendar1.setTimeInMillis(begnDate.getTime());
        calendar2.setTimeInMillis(expiDate.getTime());
        calendar1.set(calendar1.get(1), calendar1.get(2), calendar1.get(5), 0, 0, 0);
        calendar2.set(calendar2.get(1), calendar2.get(2), calendar2.get(5), 0, 0, 0);
        long startdate = calendar1.getTimeInMillis();
        long enddate = calendar2.getTimeInMillis();
        long interval = enddate - startdate;
        int intervalday = (int) (interval / 86400000L);
        return intervalday;
    }

    public static int getIntervalDay(String begnDate, String expiDate) {
        long startdate = stringToDate(begnDate).getTime();
        long enddate = stringToDate(expiDate).getTime();
        long interval = enddate - startdate;
        int intervalday = (int) (interval / 86400000L);
        return intervalday;
    }

    public static int computeMonthOnly(Date dateBegin, Date dateEnd) {
        Calendar ca1 = Calendar.getInstance();
        ca1.setTime(dateBegin);
        ca1.set(5, 1);
        ca1.set(10, 0);
        ca1.set(12, 0);
        ca1.set(13, 0);
        ca1.set(14, 0);
        Calendar ca2 = Calendar.getInstance();
        ca2.setTime(dateEnd);
        ca2.set(5, 1);
        ca2.set(10, 0);
        ca2.set(12, 0);
        ca2.set(13, 0);
        ca2.set(14, 0);
        return compute(ca1.getTime(), ca2.getTime(), 2, true);
    }

    public static int computeMonthOnly(Date dateBegin, Date dateEnd, boolean bWith) {
        int value = computeMonthOnly(dateBegin, dateEnd);
        if (bWith) {
            if (dateBegin.after(dateEnd)) {
                ++value;
            } else if (dateBegin.before(dateEnd)) {
                --value;
            } else {
                value = 1;
            }
        }

        return value;
    }

    public static int computeYearOnly(Date dateBegin, Date dateEnd) {
        Calendar ca1 = Calendar.getInstance();
        ca1.setTime(dateBegin);
        ca1.set(5, 1);
        ca1.set(2, 1);
        ca1.set(10, 0);
        ca1.set(12, 0);
        ca1.set(13, 0);
        ca1.set(14, 0);
        Calendar ca2 = Calendar.getInstance();
        ca2.setTime(dateEnd);
        ca2.set(5, 1);
        ca2.set(2, 1);
        ca2.set(10, 0);
        ca2.set(12, 0);
        ca2.set(13, 0);
        ca2.set(14, 0);
        return compute(ca1.getTime(), ca2.getTime(), 1, true);
    }

    public static int computeYearOnly(Date dateBegin, Date dateEnd, boolean bWith) {
        int value = computeYearOnly(dateBegin, dateEnd);
        if (bWith) {
            if (dateBegin.after(dateEnd)) {
                ++value;
            } else if (dateBegin.before(dateEnd)) {
                --value;
            } else {
                value = 1;
            }
        }

        return value;
    }

    public static int computeDateOnly(Date dateBegin, Date dateEnd) {
        Calendar ca1 = Calendar.getInstance();
        ca1.setTime(dateBegin);
        ca1.set(10, 0);
        ca1.set(12, 0);
        ca1.set(13, 0);
        ca1.set(14, 0);
        Calendar ca2 = Calendar.getInstance();
        ca2.setTime(dateEnd);
        ca2.set(10, 0);
        ca2.set(12, 0);
        ca2.set(13, 0);
        ca2.set(14, 0);
        return compute(ca1.getTime(), ca2.getTime(), 5, true);
    }

    public static int computeDateOnly(Date dateBegin, Date dateEnd, boolean bWith) {
        int value = computeDateOnly(dateBegin, dateEnd);
        if (bWith) {
            if (dateBegin.after(dateEnd)) {
                ++value;
            } else if (dateBegin.before(dateEnd)) {
                --value;
            } else {
                value = 1;
            }
        }

        return value;
    }

    public static Date nowDate() {
        return new Date();
    }

    public static Date toDate(int month, int day, int year, int hour, int minute, int second) {
        Calendar calendar = Calendar.getInstance();

        try {
            calendar.set(year, month - 1, day, hour, minute, second);
        } catch (Exception var8) {
            return null;
        }

        return new Date(calendar.getTime().getTime());
    }

    public static Date toDate(String monthStr, String dayStr, String yearStr, String hourStr, String minuteStr, String secondStr) {
        int month;
        int day;
        int year;
        int hour;
        int minute;
        int second;
        try {
            month = Integer.parseInt(monthStr);
            day = Integer.parseInt(dayStr);
            year = Integer.parseInt(yearStr);
            hour = Integer.parseInt(hourStr);
            minute = Integer.parseInt(minuteStr);
            second = Integer.parseInt(secondStr);
        } catch (Exception var13) {
            return null;
        }

        return toDate(month, day, year, hour, minute, second);
    }

    public static int getDaysInMonth(Calendar cal) {
        return getDaysInMonth(cal.get(2), cal.get(1));
    }

    public static int getDaysInMonth(int month, int year) {
        if (month != 1 && month != 3 && month != 5 && month != 7 && month != 8 && month != 10 && month != 12) {
            if (month != 4 && month != 6 && month != 9 && month != 11) {
                return (year % 4 != 0 || year % 100 == 0) && year % 400 != 0 ? 28 : 29;
            } else {
                return 30;
            }
        } else {
            return 31;
        }
    }

    public static int getLastDayOfWeek(Calendar cal) {
        int firstDayOfWeek = cal.getFirstDayOfWeek();
        if (firstDayOfWeek == 1) {
            return 7;
        } else if (firstDayOfWeek == 2) {
            return 1;
        } else if (firstDayOfWeek == 3) {
            return 2;
        } else if (firstDayOfWeek == 4) {
            return 3;
        } else if (firstDayOfWeek == 5) {
            return 4;
        } else {
            return firstDayOfWeek == 6 ? 5 : 6;
        }
    }

    public static Date getLastDayOfMonth(Date updt_date) {
        //设置为指定日期
        Calendar c = Calendar.getInstance();
        c.setTime(updt_date);
        c.set(Calendar.DATE, c.getActualMaximum(Calendar.DATE));
        //获取最终的时间
        return c.getTime();
    }

    public static boolean isGregorianDate(int month, int day, int year) {
        if (month >= 0 && month <= 11) {
            int[] months = new int[]{31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31};
            if (month != 1) {
                if (day < 0 || day > months[month]) {
                    return false;
                }
            } else {
                int febMax = 28;
                if (year % 4 == 0 && year % 100 != 0 || year % 400 == 0) {
                    febMax = 29;
                }

                if (day < 0 || day > febMax) {
                    return false;
                }
            }

            return true;
        } else {
            return false;
        }
    }

    public static boolean isJulianDate(int month, int day, int year) {
        if (month >= 0 && month <= 11) {
            int[] months = new int[]{31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31};
            if (month == 1) {
                int febMax = 28;
                if (year % 4 == 0) {
                    febMax = 29;
                }

                if (day < 0 || day > febMax) {
                    return false;
                }
            } else if (day < 0 || day > months[month]) {
                return false;
            }

            return true;
        } else {
            return false;
        }
    }

    public static Date addYears(Date updt_date, int amount) {
        return add(updt_date, 1, amount);
    }

    public static Date addMonths(Date updt_date, int amount) {
        return add(updt_date, 2, amount);
    }

    public static Date addWeeks(Date updt_date, int amount) {
        return add(updt_date, 3, amount);
    }

    public static Date addDays(Date updt_date, int amount) {
        return add(updt_date, 5, amount);
    }

    public static Date addHours(Date updt_date, int amount) {
        return add(updt_date, 11, amount);
    }

    public static Date addMinutes(Date updt_date, int amount) {
        return add(updt_date, 12, amount);
    }

    public static Date addSeconds(Date updt_date, int amount) {
        return add(updt_date, 13, amount);
    }

    public static Date addMilliseconds(Date updt_date, int amount) {
        return add(updt_date, 14, amount);
    }

    private static Date add(Date updt_date, int calendarField, int amount) {
        if (updt_date == null) {
            throw new IllegalArgumentException("The updt_date must not be null");
        } else {
            Calendar c = Calendar.getInstance();
            c.setTime(updt_date);
            c.add(calendarField, amount);
            return c.getTime();
        }
    }

    /**
     * 判断该日期是否是该月的最后一天
     *
     * @param updt_date 需要判断的日期
     * @return
     */
    public static boolean isLastDayOfMonth(Date updt_date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(updt_date);
        return calendar.get(Calendar.DAY_OF_MONTH) == calendar
                .getActualMaximum(Calendar.DAY_OF_MONTH);
    }

    /**
     * 判断该日期是否是该月的最后一天
     *
     * @param updt_date 需要判断的日期
     * @return
     */
    public static boolean isLastDayOfMonth(String updt_date, String srcDateFormat) {
        Date d = stringToDate(updt_date, srcDateFormat);
        return isLastDayOfMonth(d);
    }

    /**
     * 获取某一天的上期
     * 如果该期号是该月最后一天，那么获取上月的最后一天，否则，获取上月的同一天（这里同一天，如果上月不存在改天，也即是大于上月的最后一天，比如3月30号，会自动获取上月的最后一天）
     *
     * @param ym
     * @param srcDateFormat
     * @return
     */
    public static String getLastMonthIssue(String ym, String srcDateFormat) {
        String lastMonthIssue = ym;
        if (DateUtil.isLastDayOfMonth(ym, srcDateFormat)) {
            lastMonthIssue = DateUtil.datetimeToString(getLastDayOfMonth(addMonths(DateUtil.stringToDate(ym, srcDateFormat), -1)), srcDateFormat);
        } else {
            lastMonthIssue = DateUtil.datetimeToString(addMonths(DateUtil.stringToDate(ym, srcDateFormat), -1), srcDateFormat);
        }
        return lastMonthIssue;
    }

    /**
     * 获取某一天的同期
     * 如果该期号是该月最后一天，那么获取上月的最后一天，否则，获取上月的同一天（这里同一天，如果上月不存在改天，也即是大于上月的最后一天，比如3月30号，会自动获取上月的最后一天）
     *
     * @param ym
     * @param srcDateFormat
     * @return
     */
    public static String getLastYearIssue(String ym, String srcDateFormat) {
        String lastYearIssue = ym;
        if (DateUtil.isLastDayOfMonth(ym, srcDateFormat)) {
            lastYearIssue = DateUtil.datetimeToString(getLastDayOfMonth(addYears(DateUtil.stringToDate(ym, srcDateFormat), -1)), srcDateFormat);
        } else {
            lastYearIssue = DateUtil.datetimeToString(addYears(DateUtil.stringToDate(ym, srcDateFormat), -1), srcDateFormat);
        }
        return lastYearIssue;
    }

    /**
     * 获取期号
     */
    public static String getIssueByStr(String ym) {
        String issue = "";
        if (ym.indexOf("/") > -1 || ym.indexOf("-") > -1) {
            //存在格式
            issue = ym.substring(0, 7).replace("/", "").replace("-", "");
        } else {
            issue = ym.substring(0, 6);
        }
        return issue;
    }

    /**
     * 获取当前网络时间
     *
     * @param webUrl
     * @return
     * @throws Exception
     */
    public static Date getWebCurrentTime(String webUrl) {
        try {
            URL url = new URL(webUrl);
            URLConnection conn = url.openConnection();
            conn.connect();
            long dateL = conn.getDate();
            Date updt_date = new Date(dateL);
            return updt_date;
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 测试环境下启用需要对数据进行输出打印
     *
     * @param code
     * @param keysBasicResultMap
     * @param main_check_field
     * @return
     */
    public static boolean checkNotNullOrNotBlank(String code, Map<String, String> keysBasicResultMap, String main_check_field
    ) {
        if (SettleValidateUtil.isEmpty(code)) {
            keysBasicResultMap.put(main_check_field, SettleValidateConst.NULL);
            return false;
        }
        return true;
    }


    public static boolean checkCodeList(String code, List<String> codeList,
                                        Map<String, String> keysBasicResultMap,
                                        SettleListHandlerVo settleListHandlerVo, String main_check_field,
                                        String main_check_name, SetlValidBaseBusiVo setlValidBaseBusiVo,
                                        SomHiInvyBasInfo somHiInvyBasInfo) {
        if (SettleValidateUtil.isNotEmpty(codeList)) {
            if (!codeList.contains(code)) {
                // 不在合理值范围，提示
                settleListHandlerVo.setIndex(SettleValidateConst.VALIDATE_TYPE_2);
                settleListHandlerVo.setSettleListId(somHiInvyBasInfo.getId());
                settleListHandlerVo.setErrorFields(main_check_field);
                settleListHandlerVo.setErrDscr(main_check_name + "[" + code + "]不属于码值范围");
                settleListHandlerVo.setChkStas(SettleValidateConst.VALIDATE_STATE_SUCCESS);
                settleListHandlerVo.setErrType(SettleValidateConst.VALIDATE_TYPE_2);
                setlValidBaseBusiVo.getErrorDetails().add(settleListHandlerVo);
                //放入字段基础校验
                keysBasicResultMap.put(main_check_field, SettleValidateConst.OUTLIER);
                return false;
            }
        }
        return true;
    }

    public static boolean checkCodeByRegex(String code, Pattern pattern, Map<String, String> keysBasicResultMap,
                                           String main_check_field, String main_check_name,
                                           SetlValidBaseBusiVo setlValidBaseBusiVo, SettleListHandlerVo settleListHandlerVo,
                                           SomHiInvyBasInfo somHiInvyBasInfo) {
        if (pattern != null) {
            Matcher matcher = pattern.matcher(code);
            // 未匹配成功
            if (!matcher.matches()) {
                // 不在合理值范围，提示
                settleListHandlerVo.setIndex(SettleValidateConst.VALIDATE_TYPE_2);
                settleListHandlerVo.setSettleListId(somHiInvyBasInfo.getId());
                settleListHandlerVo.setErrorFields(main_check_field);
                settleListHandlerVo.setErrDscr(main_check_name + "[" + code + "]不符合该字段数据格式");
                settleListHandlerVo.setChkStas(SettleValidateConst.VALIDATE_STATE_SUCCESS);
                settleListHandlerVo.setErrType(SettleValidateConst.VALIDATE_TYPE_2);
                setlValidBaseBusiVo.getErrorDetails().add(settleListHandlerVo);
                //放入字段基础校验
                keysBasicResultMap.put(main_check_field, SettleValidateConst.OUTLIER);

                return false;
            }
        }
        return true;
    }

    /**
     * 添加错误信息
     *
     * @param handlerVo
     * @param index
     * @param errType
     * @param errDscr
     * @param errorFields
     * @param chkStas
     * @param baseBusiVo
     */
    public static void addErrorDetail(SettleListHandlerVo handlerVo, String index, String errType, String errDscr, String errorFields, String chkStas, SetlValidBaseBusiVo baseBusiVo) {
        handlerVo.setIndex(index);
        handlerVo.setErrDscr(errDscr);
        handlerVo.setErrorFields(errorFields);
        handlerVo.setChkStas(chkStas);
        handlerVo.setErrType(errType);
        baseBusiVo.getErrorDetails().add(handlerVo);
    }

    public static String printErrorInfoFromException(Exception requestExe) {
        try {
            StringWriter strWriter = new StringWriter();
            PrintWriter printWriter = new PrintWriter(strWriter);
            requestExe.printStackTrace(printWriter);
            String errStr = strWriter.toString();
            strWriter.close();
            printWriter.close();
            return "\r\n" + errStr + "\r\n";

        } catch (Exception tre) {
            tre.printStackTrace();
            return "ErrorInfoFromException";
        }
    }

    //判断性别
    public static String determineGender(char genderDigit) {
        String result = null;
        if((genderDigit - '0') % 2== 0){
            result = "2";
        }else{
            result= "1" ;
        }
        return result;
    }
}
