package com.my.som.settlevalidate.script.depth;

import com.my.som.model.medicalQuality.SomSetlInvyBldInfo;
import com.my.som.settlevalidate.constant.SettleValidateConst;
import com.my.som.settlevalidate.util.SettleValidateUtil;
import com.my.som.settlevalidate.vo.SetlValidBaseBusiVo;
import com.my.som.vo.dataHandle.SettleListHandlerVo;
import com.my.som.vo.dataHandle.SettleListValidateVo;
import com.yomahub.liteflow.script.ScriptExecuteWrap;
import com.yomahub.liteflow.script.body.JaninoCommonScriptBody;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Map;

/**
 * 事后判断 输血量不为空
 */
public class check_bld_amt_after_depth implements JaninoCommonScriptBody {
    private static final Logger logger = LoggerFactory.getLogger(check_bld_amt_after_depth.class);
    private static final String MAIN_CHECK_FIELD = "c46";
    private static final String MAIN_CHECK_NAME = "输血量";
    private static final String MAIN_CHECK_FIELD_1 = "c45";
    private static final String MAIN_CHECK_NAME_1 = "输血品种";
    private static final String MAIN_CHECK_FIELD_2 = "c46";
    private static final String MAIN_CHECK_NAME_2 = "输血量";
    private static final String MAIN_CHECK_REGION = "四川";
    /**
     * 事后校验
     * 输血量(bld_amt)基础质控
     * 判断 输血量 是否为空
     *
     * @param wrap
     * @return
     */
    @Override
    public Void body(ScriptExecuteWrap wrap) {
        SetlValidBaseBusiVo setlValidBaseBusiVo = (SetlValidBaseBusiVo) wrap.cmp.getFirstContextBean();
        SettleListValidateVo settleListValidateVo = (SettleListValidateVo) wrap.cmp.getRequestData();
        Map<String, String> keysBasicResultMap = setlValidBaseBusiVo.getKeysBasicResultMap();
        SettleListHandlerVo settleListHandlerVo = new SettleListHandlerVo();

        List<SomSetlInvyBldInfo> busTransfusionList = settleListValidateVo.getBusTransfusionList();
        // 检查 busTransfusionList 是否为空
        if (SettleValidateUtil.isEmpty(busTransfusionList)) {
            return null;
        }
        for (int i = 0; i < busTransfusionList.size(); i++) {
            SomSetlInvyBldInfo somSetlInvyBldInfo = (SomSetlInvyBldInfo) busTransfusionList.get(i);
            if (SettleValidateConst.PASS.equals(keysBasicResultMap.get(i+ MAIN_CHECK_FIELD_1))) {
                if (SettleValidateUtil.isNotEmpty(somSetlInvyBldInfo.getBldAmt())) {
                    Double c46 = Double.valueOf(somSetlInvyBldInfo.getBldAmt());
                    // 获取输血量
                    if (c46 < 0 || c46 == 0) {
                        addErrorDetail(settleListHandlerVo, SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_ERROR_TYPE_2,
                                "第" + (i + 1) + "个输血信息：id为[" + somSetlInvyBldInfo.getId() + "]的" +MAIN_CHECK_NAME_2 + "[" + c46 + "]不符合规范", MAIN_CHECK_FIELD_2,
                                SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
                    }
                } else {
                    addErrorDetail(settleListHandlerVo, SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_TYPE_1,
                            "第" + (i + 1) + "个输血信息：id为[" + somSetlInvyBldInfo.getId() + "]的" +MAIN_CHECK_NAME_2 + "[" + somSetlInvyBldInfo.getBldAmt() + "]为空", MAIN_CHECK_FIELD_2,
                            SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
                }
            }
        }
        return null;
    }

    private void addErrorDetail(SettleListHandlerVo handlerVo, String index, String errType, String errDscr, String errorFields, String chkStas, SetlValidBaseBusiVo baseBusiVo) {
        handlerVo.setIndex(index);
        handlerVo.setErrDscr(errDscr);
        handlerVo.setErrorFields(errorFields);
        handlerVo.setChkStas(chkStas);
        handlerVo.setErrType(errType);
        baseBusiVo.getErrorDetails().add(handlerVo);
    }
}
