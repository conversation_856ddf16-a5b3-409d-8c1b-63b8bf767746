package com.my.som.settlevalidate.script.base;

import com.my.som.model.dataHandle.SomHiInvyBasInfo;
import com.my.som.settlevalidate.constant.SettleValidateConst;
import com.my.som.settlevalidate.util.SettleValidateUtil;
import com.my.som.settlevalidate.vo.SetlValidBaseBusiVo;
import com.my.som.util.SettleListValidateUtil;
import com.my.som.vo.dataHandle.SettleListHandlerVo;
import com.my.som.vo.dataHandle.SettleListValidateVo;
import com.yomahub.liteflow.script.ScriptExecuteWrap;
import com.yomahub.liteflow.script.body.JaninoCommonScriptBody;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Map;

public class check_prfs_basic implements JaninoCommonScriptBody {
    private static final Logger logger = LoggerFactory.getLogger(check_prfs_basic.class);

    private static final String MAIN_CHECK_FIELD = "a38c";
    private static final String MAIN_CHECK_NAME = "职业";
    private static final String MAIN_CHECK_REGION = "四川";
    /**
     * 职业(prfs)基础质控
     * 1.判断职业是否为空
     * 2.判断是否符合GB/T2261.4-2003 标准
     * @param wrap
     * @return
     */
    @Override
    public Void body(ScriptExecuteWrap wrap) {
        SetlValidBaseBusiVo setlValidBaseBusiVo = (SetlValidBaseBusiVo) wrap.cmp.getFirstContextBean();
        SettleListValidateVo settleListValidateVo = (SettleListValidateVo) wrap.cmp.getRequestData();
        Map<String, String> keysBasicResultMap = setlValidBaseBusiVo.getKeysBasicResultMap();
        SomHiInvyBasInfo somHiInvyBasInfo = settleListValidateVo.getSomHiInvyBasInfo();
        SettleListHandlerVo settleListHandlerVo = new SettleListHandlerVo();

        // 检查 somHiInvyBasInfo 是否为空
        if (SettleValidateUtil.isEmpty(somHiInvyBasInfo)) {
            addErrorDetail(settleListHandlerVo, SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY,SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY,
                    MAIN_CHECK_FIELD,
                    (String)SettleValidateConst.CHECK_TYPE_MAP.get(SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY),
                    SettleValidateConst.VALIDATE_STATE_FAIL, setlValidBaseBusiVo);
            return null;
        }

        // 获取职业
        String a38c = somHiInvyBasInfo.getA38c();
        if (SettleValidateUtil.isEmpty(a38c)) {
            addErrorDetail(settleListHandlerVo, SettleValidateConst.VALIDATE_TYPE_1,SettleValidateConst.VALIDATE_ERROR_TYPE_1,
                    MAIN_CHECK_FIELD,
                    MAIN_CHECK_NAME + "[" + a38c + "]为空",
                    SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
            keysBasicResultMap.put(MAIN_CHECK_FIELD, SettleValidateConst.NULL);
        } else {
            // 判断是否符合标准字典
            Map<String,Object> map = (Map<String,Object>)setlValidBaseBusiVo.getSettleListParams().get(SettleListValidateUtil.KEY_DICT);
            List<String> zy = (List<String>)map.get("ZY");
            if(!zy.contains(a38c)){
                addErrorDetail(settleListHandlerVo, SettleValidateConst.VALIDATE_TYPE_1,SettleValidateConst.VALIDATE_ERROR_TYPE_2,
                        MAIN_CHECK_FIELD,
                        MAIN_CHECK_NAME + "[" + a38c + "]未按照标准字典映射",
                        SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
                keysBasicResultMap.put(MAIN_CHECK_FIELD, SettleValidateConst.OUTLIER);
            }else{
                keysBasicResultMap.put(MAIN_CHECK_FIELD, SettleValidateConst.PASS);
            }

        }

        return null;
    }

    private void addErrorDetail(SettleListHandlerVo handlerVo, String index,String errType,String errorFields, String errDscr, String chkStas, SetlValidBaseBusiVo baseBusiVo) {
        handlerVo.setIndex(index);
        handlerVo.setErrDscr(errDscr);
        handlerVo.setErrorFields(errorFields);
        handlerVo.setChkStas(chkStas);
        handlerVo.setErrType(errType);
        baseBusiVo.getErrorDetails().add(handlerVo);
    }
}
