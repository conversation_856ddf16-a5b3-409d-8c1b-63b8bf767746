package com.my.som.settlevalidate.script.base;

import com.my.som.model.dataHandle.SomHiInvyBasInfo;
import com.my.som.settlevalidate.constant.SettleValidateConst;
import com.my.som.settlevalidate.util.SettleValidateUtil;
import com.my.som.settlevalidate.vo.SetlValidBaseBusiVo;
import com.my.som.util.SettleListValidateUtil;
import com.my.som.vo.dataHandle.SettleListHandlerVo;
import com.my.som.vo.dataHandle.SettleListValidateVo;
import com.yomahub.liteflow.script.ScriptExecuteWrap;
import com.yomahub.liteflow.script.body.JaninoCommonScriptBody;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 判断 转科科别不为空
 */
public class check_refl_dept_dept_after_basic implements JaninoCommonScriptBody {
    private static final Logger logger = LoggerFactory.getLogger(check_refl_dept_dept_after_basic.class);

    private static final String MAIN_CHECK_FIELD = "b21c";
    private static final String MAIN_CHECK_NAME = "转科科别";
    private static final String MAIN_CHECK_REGION = "四川,湖北";
    /**
     * 事后 校验
     * 转科科别(refldept_dept) 基础质控
     * 判断转科科别是否为空
     *
     * @param wrap
     * @return
     */
    @Override
    public Void body(ScriptExecuteWrap wrap) {
        SetlValidBaseBusiVo setlValidBaseBusiVo = (SetlValidBaseBusiVo) wrap.cmp.getFirstContextBean();
        SettleListValidateVo settleListValidateVo = (SettleListValidateVo) wrap.cmp.getRequestData();
        Map<String, String> keysBasicResultMap = setlValidBaseBusiVo.getKeysBasicResultMap();
        SomHiInvyBasInfo somHiInvyBasInfo = settleListValidateVo.getSomHiInvyBasInfo();
        SettleListHandlerVo settleListHandlerVo = new SettleListHandlerVo();

        // 检查 somHiInvyBasInfo 是否为空
        if (SettleValidateUtil.isEmpty(somHiInvyBasInfo)) {
            addErrorDetail(SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY, SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY,
                    (String) SettleValidateConst.CHECK_TYPE_MAP.get(SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY), null,
                    SettleValidateConst.VALIDATE_STATE_FAIL, setlValidBaseBusiVo);
            return null;
        }

        // 获取转科科别
        String b21c = somHiInvyBasInfo.getB21c();

        if (SettleValidateUtil.isNotEmpty(b21c)) {

            //获取出院科别
            // 获取科室编码
            List<String> dscgCatyCodeList = (List<String>) setlValidBaseBusiVo.getSettleListParams().get(SettleListValidateUtil.KEY_DEPT_CODE);
            // 获取诊疗类别
            List<String> trtTypeList = (List<String>) ((Map<String, Object>) setlValidBaseBusiVo.getSettleListParams().get(SettleListValidateUtil.KEY_DICT)).get("ZLLB");

            //说明此时转科有多个
            List<String> list = Arrays.asList(b21c.split("→"));
            for (int i = 0; i < list.size(); i++) {
                //转科科别 不符合《医疗卫生机构业务科室分类与代码》(CT 08.00.002)里的代码或诊疗科目两项中的其中一项
                if (!dscgCatyCodeList.contains(list.get(i)) && !trtTypeList.contains(list.get(i))) {
                    addErrorDetail(SettleValidateConst.VALIDATE_ERROR_TYPE_2, SettleValidateConst.VALIDATE_TYPE_1,
                            MAIN_CHECK_NAME + "为[" + b21c + "], 其中第" + (i + 1) + "个转科科别不符合科别规范", MAIN_CHECK_FIELD,
                            SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
                    keysBasicResultMap.put(MAIN_CHECK_FIELD, SettleValidateConst.OUTLIER);
                } else {
                    keysBasicResultMap.put(MAIN_CHECK_FIELD, SettleValidateConst.PASS);
                }
            }
        }
        return null;
    }

    private void addErrorDetail(String index, String errType, String errDscr, String errorFields, String chkStas, SetlValidBaseBusiVo baseBusiVo) {
        SettleListHandlerVo handlerVo = new SettleListHandlerVo();
        handlerVo.setIndex(index);
        handlerVo.setErrDscr(errDscr);
        handlerVo.setErrorFields(errorFields);
        handlerVo.setChkStas(chkStas);
        handlerVo.setErrType(errType);
        baseBusiVo.getErrorDetails().add(handlerVo);
    }
}

