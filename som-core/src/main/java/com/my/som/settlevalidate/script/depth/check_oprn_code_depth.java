package com.my.som.settlevalidate.script.depth;

import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.my.som.common.constant.DrgConst;
import com.my.som.model.dataHandle.SomHiInvyBasInfo;
import com.my.som.model.dataHandle.SomOprnOprtInfo;
import com.my.som.settlevalidate.constant.SettleValidateConst;
import com.my.som.settlevalidate.util.SettleValidateUtil;
import com.my.som.settlevalidate.vo.SetlValidBaseBusiVo;
import com.my.som.util.SettleListValidateUtil;
import com.my.som.vo.common.OprtDiffBodyParts;
import com.my.som.vo.dataHandle.SettleListDisSectionVo;
import com.my.som.vo.dataHandle.SettleListHandlerVo;
import com.my.som.vo.dataHandle.SettleListValidateVo;
import com.yomahub.liteflow.script.ScriptExecuteWrap;
import com.yomahub.liteflow.script.body.JaninoCommonScriptBody;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

public class check_oprn_code_depth implements JaninoCommonScriptBody {
    private static final Logger logger = LoggerFactory.getLogger(check_oprn_code_depth.class);
    private static final String MAIN_CHECK_NAME = "手术编码";
    private static final String MAIN_CHECK_FIELD = "c35c";
    private static final String MAIN_CHECK_NAME_1 = "手术编码";
    private static final String MAIN_CHECK_NAME_2 = "手术名称";

    private static final String MAIN_CHECK_CODE1 = "c35c";
    private static final String MAIN_CHECK_CODE2 = "c36n";
    private static final String MAIN_CHECK_REGION = "四川,湖北";
    /**
     * 手术信息质控
     * 1.判断手术代码与名称是否符合医保2.0版ICD-9标准。
     * 2.判断手术代码是否存在医保停用码。
     * 3.判断手术代码是否存在重复
     *
     * @param wrap
     * @return
     */
    @Override
    public Void body(ScriptExecuteWrap wrap) {

        SetlValidBaseBusiVo setlValidBaseBusiVo = (SetlValidBaseBusiVo) wrap.cmp.getFirstContextBean();
        SettleListValidateVo settleListValidateVo = (SettleListValidateVo) wrap.cmp.getRequestData();
        List<SomOprnOprtInfo> busOperateDiagnosisList = settleListValidateVo.getBusOperateDiagnosisList();
        // 检查手术信息是否为空
        if (SettleValidateUtil.isEmpty(busOperateDiagnosisList)) {
            return null;
        }
        SomHiInvyBasInfo somHiInvyBasInfo = settleListValidateVo.getSomHiInvyBasInfo();
        if (SettleValidateUtil.isEmpty(somHiInvyBasInfo)) {
            return null;
        }
        // 获取清单字典和ICD-10相关映射
        Map<String, Object> grepMap = (Map<String, Object>) setlValidBaseBusiVo.getSettleListParams().get(SettleListValidateUtil.KEY_ICD9_GRAY_CODE_2);
        Map<String, Object> icd9Map = (Map<String, Object>) setlValidBaseBusiVo.getSettleListParams().get(SettleListValidateUtil.KEY_ICD9);

        // 构造set判断是否存在重复编码
        Set<String> oprnCodeSet = new HashSet<>();
        Map<String, Object> dictMap = (Map<String, Object>) setlValidBaseBusiVo.getSettleListParams().get(SettleListValidateUtil.KEY_DIS_SECTION_LIST);

        Map<String, String> oprtDiffBodyPartMap = getOprtDiffBodyPartMap(setlValidBaseBusiVo);

        //主诊端与年龄需要判断的集合
        Integer a14 = somHiInvyBasInfo.getA14();
        List<String> checkAgeCodeList = getCheckAgeCodeList(a14, dictMap);


        // 遍历操作列表
        for (int i = 0; i < busOperateDiagnosisList.size(); i++) {
            SomOprnOprtInfo somOprnOprtInfo = (SomOprnOprtInfo) busOperateDiagnosisList.get(i);
            String oprnCode = somOprnOprtInfo.getC35c();
            String oprnName = somOprnOprtInfo.getC36n();
            //检测主诊段

            if (SettleValidateUtil.isNotEmpty(oprnCode) && SettleValidateUtil.isNotEmpty(oprnCode)) {

                if (checkAgeCodeList.size() > 0) {
                    if (checkAgeCodeList.contains(oprnCode)) {
                        addErrorDetail(SettleValidateConst.VALIDATE_TYPE_2, SettleValidateConst.VALIDATE_ERROR_TYPE_1,
                                MAIN_CHECK_NAME_1,
                                "第" + (i + 1) + "个手术操作：编码为["+oprnCode+"]的" + "患者年龄为" + a14 + "岁 大于等于 11岁，手术操作编码不应填报" + MAIN_CHECK_NAME_1 + "[" + oprnCode + "]",
                                SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
                    }
                }
                //判断手术部位一致
                if (oprtDiffBodyPartMap.containsKey(oprnCode)) {
                    String codes = (String) oprtDiffBodyPartMap.get(oprnCode);
                    List<String> codeList = Arrays.asList(codes.split(","));
                    for (SomOprnOprtInfo newSomOprnOprtInfo : busOperateDiagnosisList) {
                        String c35c = newSomOprnOprtInfo.getC35c().toUpperCase();
                        for (String code : codeList){
                            if (code.toUpperCase().equals(c35c)) {
                                addErrorDetail(SettleValidateConst.VALIDATE_TYPE_2, SettleValidateConst.VALIDATE_ERROR_TYPE_1,
                                        MAIN_CHECK_NAME_1,
                                        "第" + (i + 1) + "个手术操作：编码为["+oprnCode+"]的" + " 编码 [" + oprnCode + "] 与 手术编码为 [" + c35c + "]的手术部位不匹配",
                                        SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
                            }
                        }
                    }
                }

                // 验证编码是否符合ICD-10标准
                checkICD10Specification(icd9Map, oprnCode, i, setlValidBaseBusiVo, oprnName, grepMap);
                // 判断是否有重复编码
                checkRepeat(oprnCodeSet, oprnCode, i, setlValidBaseBusiVo);
            } else {
                if (SettleValidateUtil.isEmpty(oprnCode)) {
                    addErrorDetail(SettleValidateConst.VALIDATE_TYPE_2, SettleValidateConst.VALIDATE_ERROR_TYPE_1,
                            MAIN_CHECK_CODE1,
                            "第" + (i + 1) + "个手术操作：编码为["+oprnCode+"]的" +  MAIN_CHECK_NAME_1 + "[" + oprnCode + "]为空 ",
                            SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
                }
                if (SettleValidateUtil.isEmpty(oprnName) ){
                    addErrorDetail(SettleValidateConst.VALIDATE_TYPE_2, SettleValidateConst.VALIDATE_ERROR_TYPE_1,
                            MAIN_CHECK_CODE2,
                            "第" + (i + 1) + "个手术操作：编码为["+oprnCode+"]的" + MAIN_CHECK_NAME_2 + "[" + oprnName + "]为空 ",
                            SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
                }
            }
        }
        return null;
    }

    private Map<String, String> getOprtDiffBodyPartMap(SetlValidBaseBusiVo setlValidBaseBusiVo) {
        Map<String, String> map = new HashMap<>();
        List<OprtDiffBodyParts> oprtDiffBodyPartList = (List<OprtDiffBodyParts>) setlValidBaseBusiVo.getSettleListParams().get(SettleListValidateUtil.KEY_DIFF_BODY_PARTS);
        for (OprtDiffBodyParts oprtDiffBodyParts : oprtDiffBodyPartList) {
            map.put(oprtDiffBodyParts.getCleanCode(), oprtDiffBodyParts.getOprtCode());
        }
        return map;
    }

    private List<String> getCheckAgeCodeList(Integer a14, Map<String, Object> map) {
        List<String> list = new ArrayList<>();
        if (ObjectUtils.isNotEmpty(a14) && (a14 > 11 || a14 == 11)) {
            List<SettleListDisSectionVo> settleListDisSectionVos = (List<SettleListDisSectionVo>) map.get(DrgConst.AGE_MORE_THAN_11Y);
            for (SettleListDisSectionVo settleListDisSectionVo : settleListDisSectionVos) {
                list.add(settleListDisSectionVo.getDiagSec());
            }
        }
        return list;
    }


    private void checkRepeat(Set<String> oprnCodeSet, String oprnCode, int i, SetlValidBaseBusiVo setlValidBaseBusiVo) {
        if (!oprnCodeSet.add(oprnCode)) {
            addErrorDetail(SettleValidateConst.VALIDATE_TYPE_2, SettleValidateConst.VALIDATE_ERROR_TYPE_307,
                    MAIN_CHECK_CODE1,
                    "第" + (i + 1) + "个手术操作："+  MAIN_CHECK_NAME_1 + "[" + oprnCode + "]重复",
                    SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
        }
    }


    private void checkICD10Specification(Map<String, Object> icd9Map, String oprnCode, int i, SetlValidBaseBusiVo setlValidBaseBusiVo, String oprnName, Map<String, Object> grepMap) {
        if (!icd9Map.containsKey(oprnCode)) {
            addErrorDetail(SettleValidateConst.VALIDATE_TYPE_2, SettleValidateConst.VALIDATE_ERROR_TYPE_5,
                    MAIN_CHECK_CODE1,
                    "第" + (i + 1) + "个手术操作：编码为["+oprnCode+"]的" +  MAIN_CHECK_NAME_1 + "[" + oprnCode + "]不符合医保ICD-9",
                    SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
        } else {
            // 验证名称是否符合ICD-10标准
            String icdName = (String) icd9Map.get(oprnCode);
            if (!oprnName.equals(icdName)) {
                addErrorDetail(SettleValidateConst.VALIDATE_TYPE_2, SettleValidateConst.VALIDATE_ERROR_TYPE_5,
                        MAIN_CHECK_CODE1,
                        "第" + (i + 1) + "个手术操作：编码为["+oprnCode+"]的" +  MAIN_CHECK_NAME_2 + "[" + oprnName + "]与医保编码[" + oprnCode + "]名称不符",
                        SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
            }
        }
        // 检查操作编码是否存在医保停用码
        if (grepMap.containsKey(oprnCode)) {
            addErrorDetail(SettleValidateConst.VALIDATE_TYPE_2, SettleValidateConst.VALIDATE_ERROR_TYPE_13,
                    MAIN_CHECK_CODE1,
                    "第" + (i + 1) + "个手术操作：编码为["+oprnCode+"]的" +  MAIN_CHECK_NAME_1 + "[" + oprnCode + "]是医保停用码",
                    SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
        }
    }

    private void addErrorDetail(String index, String errType, String errorFields, String errDscr, String chkStas, SetlValidBaseBusiVo baseBusiVo) {
        SettleListHandlerVo handlerVo = new SettleListHandlerVo();
        handlerVo.setIndex(index);
        handlerVo.setErrorFields(errorFields);
        handlerVo.setErrDscr(errDscr);
        handlerVo.setChkStas(chkStas);
        handlerVo.setErrType(errType);
        baseBusiVo.getErrorDetails().add(handlerVo);
    }
}
