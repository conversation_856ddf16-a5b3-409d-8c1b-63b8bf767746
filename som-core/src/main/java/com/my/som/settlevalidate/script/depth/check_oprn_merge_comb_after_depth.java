package com.my.som.settlevalidate.script.depth;

import com.my.som.model.dataHandle.SomOprnOprtInfo;
import com.my.som.settlevalidate.constant.SettleValidateConst;
import com.my.som.settlevalidate.vo.SetlValidBaseBusiVo;
import com.my.som.util.SettleListValidateUtil;
import com.my.som.vo.dataConfig.CombCodeVO;
import com.my.som.vo.dataHandle.SettleListHandlerVo;
import com.my.som.vo.dataHandle.SettleListValidateVo;
import com.yomahub.liteflow.script.ScriptExecuteWrap;
import com.yomahub.liteflow.script.body.JaninoCommonScriptBody;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

public class check_oprn_merge_comb_after_depth implements JaninoCommonScriptBody {
    private static final Logger logger = LoggerFactory.getLogger(check_oprn_merge_comb_after_depth.class);

    private static final String MAIN_CHECK_FIELD = "c35c";
    private static final String MAIN_CHECK_NAME = "手术联合编码";
    private static final String MAIN_CHECK_REGION = "四川,湖北";
    /**
     * 操作联合编码校验
     * @param wrap
     * @return
     */
    @Override
    public Void body(ScriptExecuteWrap wrap) {
        SetlValidBaseBusiVo setlValidBaseBusiVo = (SetlValidBaseBusiVo) wrap.cmp.getFirstContextBean();
        SettleListValidateVo settleListValidateVo = (SettleListValidateVo) wrap.cmp.getRequestData();
        List<SomOprnOprtInfo> oprnOprtInfos = settleListValidateVo.getBusOperateDiagnosisList();

        if (!oprnOprtInfos.isEmpty()) {
            List<String> oprns = new ArrayList<>();
            for (SomOprnOprtInfo vo : oprnOprtInfos) {
                oprns.add(vo.getC35c());
            }

            Map<String, Object> map = (Map<String, Object>) setlValidBaseBusiVo.getSettleListParams().get(SettleListValidateUtil.KEY_COMB_CODE);
            List<CombCodeVO> combCodeVOS = (List<CombCodeVO>) map.get(SettleListValidateUtil.COMB_TYPE_OPRN);

            for (CombCodeVO combCodeVO : combCodeVOS) {
                List<String> combCodes = Arrays.asList(combCodeVO.getCombCodes().split(";"));
                if (oprns.containsAll(combCodes)) {
                    // 存在联合编码合并时，进行质控提示
                    addErrorDetail(SettleValidateConst.VALIDATE_TYPE_2,SettleValidateConst.VALIDATE_ERROR_TYPE_307,
                            MAIN_CHECK_FIELD,
                            "手术操作编码"+combCodes.toString() + "需调整为联合操作编码[" + combCodeVO.getGeneCode() + "]",
                            SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
                }
            }
        }

        return null;
    }

    private void addErrorDetail(String index,String errType,String errorFields, String errDscr, String chkStas, SetlValidBaseBusiVo baseBusiVo) {
        SettleListHandlerVo handlerVo = new SettleListHandlerVo();
        handlerVo.setIndex(index);
        handlerVo.setErrorFields(errorFields);
        handlerVo.setErrDscr(errDscr);
        handlerVo.setChkStas(chkStas);
        handlerVo.setErrType(errType);
        baseBusiVo.getErrorDetails().add(handlerVo);
    }
}
