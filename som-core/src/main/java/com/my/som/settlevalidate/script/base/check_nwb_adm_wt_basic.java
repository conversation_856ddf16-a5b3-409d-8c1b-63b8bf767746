package com.my.som.settlevalidate.script.base;

import com.my.som.model.dataHandle.SomHiInvyBasInfo;
import com.my.som.settlevalidate.constant.SettleValidateConst;
import com.my.som.settlevalidate.util.SettleValidateUtil;
import com.my.som.settlevalidate.vo.SetlValidBaseBusiVo;
import com.my.som.vo.dataHandle.SettleListHandlerVo;
import com.my.som.vo.dataHandle.SettleListValidateVo;
import com.yomahub.liteflow.script.ScriptExecuteWrap;
import com.yomahub.liteflow.script.body.JaninoCommonScriptBody;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

public class check_nwb_adm_wt_basic implements JaninoCommonScriptBody {
    private static final Logger logger = LoggerFactory.getLogger(check_nwb_adm_wt_basic.class);

    private static final String MAIN_CHECK_FIELD = "a17";
    private static final String MAIN_CHECK_NAME = "新生儿入院体重";
    private static final String MAIN_CHECK_REGION = "四川,湖北";
    /**
     * 新生儿出生体重-[a17]-基础校验
     * 1、当新生儿入院体重为空时，不用判断新生儿入院体重项下的规则；当新生儿入院体重不为空时，判断新生儿入院体重是否为阿拉伯数字，若否则不通过。
     * 2、审核新生儿入院体重范围是否在[200,20000]内，若否则不通过。
     *
     * @param wrap
     * @return
     */
    @Override
    public Void body(ScriptExecuteWrap wrap) {
        String main_check_field = "a17";
        String main_check_name = "新生儿入院体重";
        SetlValidBaseBusiVo setlValidBaseBusiVo = (SetlValidBaseBusiVo) wrap.cmp.getFirstContextBean();
        SettleListValidateVo settleListValidateVo = (SettleListValidateVo) wrap.cmp.getRequestData();
        Map<String, String> keysBasicResultMap = setlValidBaseBusiVo.getKeysBasicResultMap();
        SomHiInvyBasInfo somHiInvyBasInfo = settleListValidateVo.getSomHiInvyBasInfo();
        SettleListHandlerVo settleListHandlerVo = new SettleListHandlerVo();
        if (SettleValidateUtil.isEmpty(somHiInvyBasInfo)) {
            settleListHandlerVo.setIndex(SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY);
            settleListHandlerVo.setErrDscr((String) SettleValidateConst.CHECK_TYPE_MAP.get(SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY));
            settleListHandlerVo.setChkStas(SettleValidateConst.VALIDATE_STATE_FAIL);
            settleListHandlerVo.setErrType(SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY);
            setlValidBaseBusiVo.getErrorDetails().add(settleListHandlerVo);
            return null;
        }
        Double a17 = somHiInvyBasInfo.getA17();
        if (!SettleValidateUtil.isEmpty(a17)) {
            //不为空,则一定是一个正常值，判断范围是否在[200,20000]
            if (a17 == 0.0) {
                //处理为null,被默认赋值为0.0数据
                keysBasicResultMap.put(main_check_field, SettleValidateConst.NULL);
                return null;
            }
            if (!(a17 >= 200 && a17 <= 20000)) {
                //不在合理值范围，提示
                settleListHandlerVo.setIndex(SettleValidateConst.VALIDATE_TYPE_1);
                settleListHandlerVo.setSettleListId(somHiInvyBasInfo.getId());
                settleListHandlerVo.setErrorFields(main_check_field);
                settleListHandlerVo.setErrDscr(main_check_name + "[" + a17 + "]超出合理值范围");
                settleListHandlerVo.setChkStas(SettleValidateConst.VALIDATE_STATE_SUCCESS);
                settleListHandlerVo.setErrType(SettleValidateConst.VALIDATE_ERROR_TYPE_2);
                setlValidBaseBusiVo.getErrorDetails().add(settleListHandlerVo);
                //放入字段基础校验
                keysBasicResultMap.put(main_check_field, SettleValidateConst.OUTLIER);
            } else {
                //正常值，基础校验通过不写错误信息
                keysBasicResultMap.put(main_check_field, SettleValidateConst.PASS);
            }
        } else {
            keysBasicResultMap.put(main_check_field, SettleValidateConst.NULL);
        }
        return null;
    }
}
