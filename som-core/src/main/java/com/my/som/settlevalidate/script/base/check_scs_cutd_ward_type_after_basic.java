package com.my.som.settlevalidate.script.base;

import com.my.som.model.dataHandle.SomHiInvyBasInfo;
import com.my.som.model.medicalQuality.SomSetlInvyScsCutdInfo;
import com.my.som.settlevalidate.constant.SettleValidateConst;
import com.my.som.settlevalidate.util.SettleValidateUtil;
import com.my.som.settlevalidate.vo.SetlValidBaseBusiVo;
import com.my.som.util.SettleListValidateUtil;
import com.my.som.vo.dataHandle.SettleListHandlerVo;
import com.my.som.vo.dataHandle.SettleListValidateVo;
import com.yomahub.liteflow.script.ScriptExecuteWrap;
import com.yomahub.liteflow.script.body.JaninoCommonScriptBody;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Map;

/**
 * 事后判断 重症监护室
 */
public class check_scs_cutd_ward_type_after_basic implements JaninoCommonScriptBody {
    private static final Logger logger = LoggerFactory.getLogger(check_scs_cutd_ward_type_after_basic.class);

    private static final String MAIN_CHECK_FIELD = "scsCutdWardType";
    private static final String MAIN_CHECK_NAME = "重症监护病房类型";
    private static final String MAIN_CHECK_REGION = "四川";

    private static final String MAIN_CHECK_FIELD_1 = "scsCutdInpoolTime";
    private static final String MAIN_CHECK_NAME_1 = "重症监护进入时间";
    private static final String MAIN_CHECK_FIELD_2 = "scsCutdExitTime";
    private static final String MAIN_CHECK_NAME_2 = "重症监护退出时间";
    /**
     * 重症监护病房类型(scs_cutd_ward_type)基础质控
     * 判断入院时间是否为空
     * @param wrap
     * @return
     */
    @Override
    public Void body(ScriptExecuteWrap wrap) {
        SetlValidBaseBusiVo setlValidBaseBusiVo = (SetlValidBaseBusiVo) wrap.cmp.getFirstContextBean();
        SettleListValidateVo settleListValidateVo = (SettleListValidateVo) wrap.cmp.getRequestData();
        Map<String, String> keysBasicResultMap = setlValidBaseBusiVo.getKeysBasicResultMap();
        SomHiInvyBasInfo somHiInvyBasInfo = settleListValidateVo.getSomHiInvyBasInfo();
        SettleListHandlerVo settleListHandlerVo = new SettleListHandlerVo();
        List<SomSetlInvyScsCutdInfo> busIcuList = settleListValidateVo.getBusIcuList();
        // 检查 somHiInvyBasInfo 是否为空
        if (SettleValidateUtil.isEmpty(busIcuList)) {
            return null;
        }

        for (int i = 0; i < busIcuList.size(); i++) {
            SomSetlInvyScsCutdInfo somSetlInvyScsCutdInfo = (SomSetlInvyScsCutdInfo) busIcuList.get(i);
            if(SettleValidateUtil.isNotEmpty(somSetlInvyScsCutdInfo.getScsCutdWardType())){
                //判断 重症监护病房类型是否 合理
                Map<String,Object> map = (Map<String,Object>)setlValidBaseBusiVo.getSettleListParams().get(SettleListValidateUtil.KEY_DICT);
                List<String> scsCutdWardTypeList = (List<String>)map.get("ZZJHBFLX");
                if(!scsCutdWardTypeList.contains(somSetlInvyScsCutdInfo.getScsCutdWardType())){
                    addErrorDetail(SettleValidateConst.VALIDATE_TYPE_1,SettleValidateConst.VALIDATE_ERROR_TYPE_2,
                            MAIN_CHECK_FIELD,
                            "第" + (i + 1) + "个重症监护: id为[" + somSetlInvyScsCutdInfo.getId() + "]的" +MAIN_CHECK_NAME + "[" + somSetlInvyScsCutdInfo.getScsCutdWardType() + "]不符合规范",
                            SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
                    keysBasicResultMap.put(i+MAIN_CHECK_FIELD_1, SettleValidateConst.OUTLIER);
                }

                //进入时间
                if(SettleValidateUtil.isNotEmpty(somSetlInvyScsCutdInfo.getScsCutdInpoolTime())){
                    keysBasicResultMap.put(somSetlInvyScsCutdInfo.getSettleListId()+MAIN_CHECK_FIELD_1, SettleValidateConst.PASS);
                }else{
                    addErrorDetail(SettleValidateConst.VALIDATE_TYPE_1,SettleValidateConst.VALIDATE_ERROR_TYPE_1,
                            MAIN_CHECK_FIELD_1,
                            "第" + (i + 1) + "个重症监护: id为[" + somSetlInvyScsCutdInfo.getId() + "]的" +MAIN_CHECK_NAME_1 + "[" + MAIN_CHECK_FIELD_1 + "]为空",
                            SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
                    keysBasicResultMap.put(i+MAIN_CHECK_FIELD_1, SettleValidateConst.NULL);
                }
                //退出时间
                if(SettleValidateUtil.isNotEmpty(somSetlInvyScsCutdInfo.getScsCutdInpoolTime())){
                    keysBasicResultMap.put(i+MAIN_CHECK_FIELD_2, SettleValidateConst.PASS);
                }else{
                    addErrorDetail(SettleValidateConst.VALIDATE_TYPE_1,SettleValidateConst.VALIDATE_ERROR_TYPE_1,
                            MAIN_CHECK_FIELD_2,
                            "第" + (i + 1) + "个重症监护: id为[" + somSetlInvyScsCutdInfo.getId() + "]的" +MAIN_CHECK_NAME_2 + "[" + MAIN_CHECK_FIELD_2 + "]为空",
                            SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
                    keysBasicResultMap.put(i+MAIN_CHECK_FIELD_2, SettleValidateConst.NULL);
                }
            }
        }
        return null;
    }
    private void addErrorDetail( String index,String errType,String errorFields, String errDscr, String chkStas, SetlValidBaseBusiVo baseBusiVo) {
        SettleListHandlerVo handlerVo =new SettleListHandlerVo();
        handlerVo.setIndex(index);
        handlerVo.setErrDscr(errDscr);
        handlerVo.setErrorFields(errorFields);
        handlerVo.setChkStas(chkStas);
        handlerVo.setErrType(errType);
        baseBusiVo.getErrorDetails().add(handlerVo);
    }
}
