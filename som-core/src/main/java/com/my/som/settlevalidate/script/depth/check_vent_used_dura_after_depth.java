package com.my.som.settlevalidate.script.depth;

import com.my.som.model.dataHandle.SomHiInvyBasInfo;
import com.my.som.model.dataHandle.SomOprnOprtInfo;
import com.my.som.settlevalidate.constant.SettleValidateConst;
import com.my.som.settlevalidate.vo.SetlValidBaseBusiVo;
import com.my.som.vo.dataHandle.SettleListHandlerVo;
import com.my.som.vo.dataHandle.SettleListValidateVo;
import com.yomahub.liteflow.script.ScriptExecuteWrap;
import com.yomahub.liteflow.script.body.JaninoCommonScriptBody;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Arrays;
import java.util.List;

/**
 * 事后 呼吸机合理性校验
 */
public class check_vent_used_dura_after_depth implements JaninoCommonScriptBody {
    private static final Logger logger = LoggerFactory.getLogger(check_vent_used_dura_after_depth.class);

    private static final String MAIN_CHECK_FIELD = "vent_used_dura";
    private static final String MAIN_CHECK_NAME = "呼吸机使用时长";

    private static final List<String> HX_CODE = Arrays.asList("96.7101", "96.7201");
    private static final String MAIN_CHECK_REGION = "四川";
    /**
     * 呼吸机合理性校验
     *
     * @param wrap
     * @return
     */
    @Override
    public Void body(ScriptExecuteWrap wrap) {
        SetlValidBaseBusiVo setlValidBaseBusiVo = (SetlValidBaseBusiVo) wrap.cmp.getFirstContextBean();
        SettleListValidateVo settleListValidateVo = (SettleListValidateVo) wrap.cmp.getRequestData();
        List<SomOprnOprtInfo> oprnOprtInfos = settleListValidateVo.getBusOperateDiagnosisList();
        SomHiInvyBasInfo somHiInvyBasInfo = settleListValidateVo.getSomHiInvyBasInfo();

        if (!oprnOprtInfos.isEmpty()) {
            for (int i = 0; i < oprnOprtInfos.size(); i++) {
                SomOprnOprtInfo vo = (SomOprnOprtInfo) oprnOprtInfos.get(i);
                String c35c = vo.getC35c();
                if (HX_CODE.contains(c35c)) {
                    // 存在呼吸机相关操作编码，判断是否存在呼吸机使用时间
                    Integer c42 = somHiInvyBasInfo.getC42();
                    Short c43 = somHiInvyBasInfo.getC43();
                    Short c44 = somHiInvyBasInfo.getC44();
                    if (c42 == null || c43 == null || c44 == null || (c42 == 0 && c43 == 0 && c44 == 0)) {
                        addErrorDetail(SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_ERROR_TYPE_1,
                                MAIN_CHECK_FIELD,
                                "第" + (i + 1) + "个手术操作：编码为["+c35c+"]的" + "使用呼吸机时间不合理",
                                SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
                    }
                }
            }
        }

        return null;
    }

    private void addErrorDetail(String index, String errType, String errorFields, String errDscr, String chkStas, SetlValidBaseBusiVo baseBusiVo) {
        SettleListHandlerVo handlerVo = new SettleListHandlerVo();
        handlerVo.setIndex(index);
        handlerVo.setErrorFields(errorFields);
        handlerVo.setErrDscr(errDscr);
        handlerVo.setChkStas(chkStas);
        handlerVo.setErrType(errType);
        baseBusiVo.getErrorDetails().add(handlerVo);
    }
}
