package com.my.som.settlevalidate.script.base;

import com.my.som.model.dataHandle.SomHiInvyBasInfo;
import com.my.som.settlevalidate.constant.SettleValidateConst;
import com.my.som.settlevalidate.util.SettleValidateUtil;
import com.my.som.settlevalidate.vo.SetlValidBaseBusiVo;
import com.my.som.util.SettleListValidateUtil;
import com.my.som.vo.dataHandle.SettleListHandlerVo;
import com.my.som.vo.dataHandle.SettleListValidateVo;
import com.yomahub.liteflow.script.ScriptExecuteWrap;
import com.yomahub.liteflow.script.body.JaninoCommonScriptBody;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

public class check_zzysdm_basic implements JaninoCommonScriptBody {
    private static final Logger logger = LoggerFactory.getLogger(check_zzysdm_basic.class);
    private static final String MAIN_CHECK_FIELD = "b51c";
    private static final String MAIN_CHECK_NAME = "主诊医师代码";
    private static final String MAIN_CHECK_REGION = "四川";
    /**
     *
     * @param wrap
     * @return
     */
    @Override
    public Void body(ScriptExecuteWrap wrap) {
        SetlValidBaseBusiVo setlValidBaseBusiVo = (SetlValidBaseBusiVo) wrap.cmp.getFirstContextBean();
        SettleListValidateVo settleListValidateVo = (SettleListValidateVo) wrap.cmp.getRequestData();
        Map<String, String> keysBasicResultMap = setlValidBaseBusiVo.getKeysBasicResultMap();
        SomHiInvyBasInfo somHiInvyBasInfo = settleListValidateVo.getSomHiInvyBasInfo();
        SettleListHandlerVo settleListHandlerVo = new SettleListHandlerVo();
        if (SettleValidateUtil.isEmpty(somHiInvyBasInfo)) {
            SettleValidateUtil.addErrorDetail(settleListHandlerVo, SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY, SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY,
                    (String) SettleValidateConst.CHECK_TYPE_MAP.get(SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY), MAIN_CHECK_FIELD,
                    SettleValidateConst.VALIDATE_STATE_FAIL, setlValidBaseBusiVo);
            return null;
        }

        //获取主诊医师代码
        String b51c = somHiInvyBasInfo.getB51c();
        boolean checkBool = SettleValidateUtil.checkNotNullOrNotBlank(b51c,keysBasicResultMap,MAIN_CHECK_FIELD);
        if (checkBool) {
            Map<String,Object> map = (Map<String,Object>)(setlValidBaseBusiVo.getSettleListParams().get(SettleListValidateUtil.KEY_DOCTOR_CODE));

            Map<String,Object> doctorCodeMap = (Map<String,Object>)map.get(somHiInvyBasInfo.getHospitalId());


            if (doctorCodeMap.containsKey(b51c)){
                // 正常值，基础校验通过不写错误信息
                keysBasicResultMap.put(MAIN_CHECK_FIELD, SettleValidateConst.PASS);
            }else {
                SettleValidateUtil.addErrorDetail(settleListHandlerVo, SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_TYPE_1,
                        MAIN_CHECK_NAME + "[" + b51c + "] 不是医院医生代码", MAIN_CHECK_FIELD,
                        SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
                keysBasicResultMap.put(MAIN_CHECK_FIELD, SettleValidateConst.OUTLIER);
            }
        }else{
            SettleValidateUtil.addErrorDetail(settleListHandlerVo, SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_TYPE_1,
                    MAIN_CHECK_NAME + "[" + b51c + "]为空", MAIN_CHECK_FIELD,
                    SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
            keysBasicResultMap.put(MAIN_CHECK_FIELD, SettleValidateConst.NULL);
        }
        return null;
    }
}
