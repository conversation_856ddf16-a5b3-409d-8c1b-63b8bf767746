package com.my.som.settlevalidate.script.base;

import com.my.som.dto.medicalQuality.BusDiseaseDiagnosisTrim;
import com.my.som.settlevalidate.constant.SettleValidateConst;
import com.my.som.settlevalidate.util.SettleValidateUtil;
import com.my.som.settlevalidate.vo.SetlValidBaseBusiVo;
import com.my.som.vo.dataHandle.SettleListHandlerVo;
import com.my.som.vo.dataHandle.SettleListValidateVo;
import com.yomahub.liteflow.script.ScriptExecuteWrap;
import com.yomahub.liteflow.script.body.JaninoCommonScriptBody;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


import java.util.List;
import java.util.Map;

/**
 * 诊断编码基础质检
 */
public class check_diag_code_basic implements JaninoCommonScriptBody {
    private static final Logger logger = LoggerFactory.getLogger(check_diag_code_basic.class);
    private static final String MAIN_CHECK_NAME = "出院西医诊断编码";
    private static final String MAIN_CHECK_FIELD = "c06c1";
    private static final String MAIN_CHECK_NAME1 = "出院西医诊断名称";
    private static final String MAIN_CHECK_CODE = "c06c1";
    private static final String MAIN_CHECK_CODE1 = "c07n1";
    private static final String MAIN_CHECK_REGION = "四川,湖北";
    /**
     * 诊断编码基础质检
     * @param wrap
     * @return
     */
    @Override
    public Void body(ScriptExecuteWrap wrap) {
        SetlValidBaseBusiVo setlValidBaseBusiVo = (SetlValidBaseBusiVo) wrap.cmp.getFirstContextBean();
        SettleListValidateVo settleListValidateVo = (SettleListValidateVo) wrap.cmp.getRequestData();
        Map<String, String> keysBasicResultMap = setlValidBaseBusiVo.getKeysBasicResultMap();

        List<BusDiseaseDiagnosisTrim> busDiseaseDiagnosisTrimsList = settleListValidateVo.getBusDiseaseDiagnosisTrimsList();

        // 检查诊断信息是否为空
        if (SettleValidateUtil.isEmpty(busDiseaseDiagnosisTrimsList)) {
            return null;
        }

        for (int i = 0; i < busDiseaseDiagnosisTrimsList.size(); i++) {
            BusDiseaseDiagnosisTrim busDiseaseDiagnosisTrim =(BusDiseaseDiagnosisTrim)busDiseaseDiagnosisTrimsList.get(i);

            String diagCode = busDiseaseDiagnosisTrim.getC06c1();
            String diagName = busDiseaseDiagnosisTrim.getC07n1();
            if(SettleValidateUtil.isEmpty(diagCode)) {
                addErrorDetail(SettleValidateConst.VALIDATE_TYPE_1,SettleValidateConst.VALIDATE_ERROR_TYPE_1,
                        MAIN_CHECK_CODE,
                        "第" + (i + 1) + "个疾病诊断："+MAIN_CHECK_NAME + "[" + diagCode + "]为空",
                        SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
                keysBasicResultMap.put(i+MAIN_CHECK_CODE, SettleValidateConst.NULL);
            }else{
                if(diagCode.length() < 5){
                    addErrorDetail(SettleValidateConst.VALIDATE_TYPE_1,SettleValidateConst.VALIDATE_ERROR_TYPE_6,
                            MAIN_CHECK_CODE,
                            "第" + (i + 1) + "个疾病诊断："+MAIN_CHECK_NAME + "[" + diagCode + "]格式错误",
                            SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
                    keysBasicResultMap.put(i+MAIN_CHECK_CODE, SettleValidateConst.OUTLIER);
                }else {
                    keysBasicResultMap.put(i+MAIN_CHECK_CODE, SettleValidateConst.PASS);
                }
            }
            if(SettleValidateUtil.isEmpty(diagName)){
                addErrorDetail(SettleValidateConst.VALIDATE_TYPE_1,SettleValidateConst.VALIDATE_ERROR_TYPE_1,
                        MAIN_CHECK_CODE,
                        "第" + (i + 1) + "个疾病诊断："+MAIN_CHECK_NAME1 + "[" + diagName + "]为空",
                        SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
                keysBasicResultMap.put(i+MAIN_CHECK_CODE1, SettleValidateConst.NULL);
            }else{
                keysBasicResultMap.put(i+MAIN_CHECK_CODE1, SettleValidateConst.PASS);
            }

        }
        return null;
    }

    private void addErrorDetail(String index,String errType,String errorFields, String errDscr, String chkStas, SetlValidBaseBusiVo baseBusiVo) {
        SettleListHandlerVo handlerVo = new SettleListHandlerVo();
        handlerVo.setIndex(index);
        handlerVo.setErrorFields(errorFields);
        handlerVo.setErrDscr(errDscr);
        handlerVo.setChkStas(chkStas);
        handlerVo.setErrType(errType);
        baseBusiVo.getErrorDetails().add(handlerVo);
    }
}