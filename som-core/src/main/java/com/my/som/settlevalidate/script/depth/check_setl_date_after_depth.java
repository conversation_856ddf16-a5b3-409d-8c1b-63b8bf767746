package com.my.som.settlevalidate.script.depth;

import com.my.som.model.dataHandle.SomHiInvyBasInfo;
import com.my.som.settlevalidate.constant.SettleValidateConst;
import com.my.som.settlevalidate.util.SettleValidateUtil;
import com.my.som.settlevalidate.vo.SetlValidBaseBusiVo;
import com.my.som.vo.dataHandle.SettleListHandlerVo;
import com.my.som.vo.dataHandle.SettleListValidateVo;
import com.yomahub.liteflow.script.ScriptExecuteWrap;
import com.yomahub.liteflow.script.body.JaninoCommonScriptBody;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Map;

/**
 * 事后判断 结算日期深度质检
 */
public class check_setl_date_after_depth implements JaninoCommonScriptBody {
    private static final Logger logger = LoggerFactory.getLogger(check_setl_date_after_depth.class);

    private static final String MAIN_CHECK_FIELD = "d36";
    private static final String MAIN_CHECK_NAME = "结算开始时间";
    private static final String MAIN_CHECK_FIELD_2 = "d37";
    private static final String MAIN_CHECK_NAME_2 = "结算结束时间";
    private static final String MAIN_CHECK_FIELD_3 = "b12";
    private static final String MAIN_CHECK_FIELD_4 = "b15";
    private static final String MAIN_CHECK_REGION = "四川";
    /**
     * 事后校验
     * 结算开始时间(setl_date)是否在合理范围
     * 判断结算开始时间是否为空
     * @param wrap
     * @return
     */
    @Override
    public Void body(ScriptExecuteWrap wrap) {
        SetlValidBaseBusiVo setlValidBaseBusiVo = (SetlValidBaseBusiVo) wrap.cmp.getFirstContextBean();
        SettleListValidateVo settleListValidateVo = (SettleListValidateVo) wrap.cmp.getRequestData();
        Map<String, String> keysBasicResultMap = setlValidBaseBusiVo.getKeysBasicResultMap();
        SomHiInvyBasInfo somHiInvyBasInfo = settleListValidateVo.getSomHiInvyBasInfo();
        SettleListHandlerVo settleListHandlerVo = new SettleListHandlerVo();

        // 检查 somHiInvyBasInfo 是否为空
        if (SettleValidateUtil.isEmpty(somHiInvyBasInfo)) {
            addErrorDetail(settleListHandlerVo, SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY,SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY,
                    (String) SettleValidateConst.CHECK_TYPE_MAP.get(SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY), null,
                    SettleValidateConst.VALIDATE_STATE_FAIL, setlValidBaseBusiVo);
            return null;
        }

        // 获取结算开始日期
        String d36_stas =  (String) keysBasicResultMap.get(MAIN_CHECK_FIELD);
        // 获取结算开始日期
        String d36 = somHiInvyBasInfo.getD36();
        // 获取结算开始日期
        String d37_stas =  (String) keysBasicResultMap.get(MAIN_CHECK_FIELD_2);
        // 获取计算时间
        String d37 = somHiInvyBasInfo.getD37();
        String b12_stas =  (String) keysBasicResultMap.get(MAIN_CHECK_FIELD_3);
        //入院时间
        String b12 =  somHiInvyBasInfo.getB12();

        String b15_stas =  (String) keysBasicResultMap.get(MAIN_CHECK_FIELD_4);
        //出院时间
        String b15 =  somHiInvyBasInfo.getB15();
       if (SettleValidateConst.PASS.equals(d36_stas) && SettleValidateConst.PASS.equals(b12_stas)) {
            //此时判断结算开始时间是否大于或等于入院时间
            //如果入院时间大于 结算开始时间
            if(judgeTimeOK(d36,b12)){
                addErrorDetail(settleListHandlerVo, SettleValidateConst.VALIDATE_TYPE_1,SettleValidateConst.VALIDATE_ERROR_TYPE_2,
                        MAIN_CHECK_NAME + "[" + d36 + "] 应该大于入院时间["+b12+"]",MAIN_CHECK_FIELD,
                        SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
            }
        }
        if (SettleValidateConst.PASS.equals(d37_stas) && SettleValidateConst.PASS.equals(b15_stas)) {
            //此时判断结算截止时间是否大于或等于出院时间
            //如果出院院时间大于 结算截止时间
            if(judgeTimeOK(d37,b15)){
                addErrorDetail(settleListHandlerVo, SettleValidateConst.VALIDATE_TYPE_1,SettleValidateConst.VALIDATE_ERROR_TYPE_2,
                        MAIN_CHECK_NAME_2 + "[" + d37 + "] 应该大于出院时间["+b15+"]",MAIN_CHECK_FIELD,
                        SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
            }
        }

        //此时判断结算截止时间是否大于或等于结算开始时间
        //如果  结算开始时间 大于 结算截止时间
        if(SettleValidateConst.PASS.equals(d36_stas) &&SettleValidateConst.PASS.equals(d37_stas)) {
            if (judgeTimeOK(d37, d36)) {
                addErrorDetail(settleListHandlerVo, SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_ERROR_TYPE_2,
                        MAIN_CHECK_NAME + "[" + d36 + "] " + "应该小于" + MAIN_CHECK_NAME_2 + "[" + d37 + "] ", MAIN_CHECK_FIELD,
                        SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
            }
        }
        return null;
    }



    /**
     * 时间2 大于  时间1  返回 true
     * @param time1 时间1
     * @param time2 时间2
     * @return
     */
    private boolean judgeTimeOK(String time1, String time2) {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        boolean flag = false;
        try{
            Date admDate = dateFormat.parse(time1);
            Date dscgDate = dateFormat.parse(time2);
            // 时间2 大于或等于时间1
            if (dscgDate.after(admDate)) {
                flag = true;
            }
        }catch (ParseException e){
            logger.error("结算开始时间质检 日期解析失败: " + e.getMessage());
        }
        return flag ;
    }

    private void addErrorDetail(SettleListHandlerVo handlerVo, String index, String errType, String errDscr, String errorFields, String chkStas, SetlValidBaseBusiVo baseBusiVo) {
        handlerVo.setIndex(index);
        handlerVo.setErrDscr(errDscr);
        handlerVo.setErrorFields(errorFields);
        handlerVo.setChkStas(chkStas);
        handlerVo.setErrType(errType);
        baseBusiVo.getErrorDetails().add(handlerVo);
    }
}
