package com.my.som.settlevalidate.script.base;

import com.my.som.model.dataHandle.SomHiInvyBasInfo;
import com.my.som.settlevalidate.constant.SettleValidateConst;
import com.my.som.settlevalidate.util.SettleValidateUtil;
import com.my.som.settlevalidate.vo.SetlValidBaseBusiVo;
import com.my.som.vo.dataHandle.SettleListHandlerVo;
import com.my.som.vo.dataHandle.SettleListValidateVo;
import com.yomahub.liteflow.script.ScriptExecuteWrap;
import com.yomahub.liteflow.script.body.JaninoCommonScriptBody;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 判断 门（急）诊诊断（西医诊断编码）不为空
 */
public class check_otp_wm_dise_code_after_basic implements JaninoCommonScriptBody {
    private static final Logger logger = LoggerFactory.getLogger(check_otp_wm_dise_code_after_basic.class);

    private static final String MAIN_CHECK_FIELD = "c01c";
    private static final String MAIN_CHECK_NAME = "门（急）诊诊断编码（西医）";
    private static final List<String> ERROR_CODE_BEGIN_LIST = Arrays.asList("V", "W", "X", "Y");
    private static final String MAIN_CHECK_REGION = "四川";
    /**
     * 事后校验
     * 门（急）诊诊断编码（西医）(otp_wm_dise_name)基础质控
     * 判断门（急）诊诊断编码（西医）是否为空
     *
     * @param wrap
     * @return
     */
    @Override
    public Void body(ScriptExecuteWrap wrap) {
        SetlValidBaseBusiVo setlValidBaseBusiVo = (SetlValidBaseBusiVo) wrap.cmp.getFirstContextBean();
        SettleListValidateVo settleListValidateVo = (SettleListValidateVo) wrap.cmp.getRequestData();
        Map<String, String> keysBasicResultMap = setlValidBaseBusiVo.getKeysBasicResultMap();
        SomHiInvyBasInfo somHiInvyBasInfo = settleListValidateVo.getSomHiInvyBasInfo();
        SettleListHandlerVo settleListHandlerVo = new SettleListHandlerVo();


        // 检查 somHiInvyBasInfo 是否为空
        if (SettleValidateUtil.isEmpty(somHiInvyBasInfo)) {
            return null;
        }

        // 获取门（急）诊诊断名称（西医）
        String c01c = somHiInvyBasInfo.getC01c();
        if (SettleValidateUtil.isEmpty(c01c)) {
            keysBasicResultMap.put(MAIN_CHECK_FIELD, SettleValidateConst.NULL);
        } else {
            if(c01c.length() < 5){
                //不在合理值范围，提示
                addErrorDetail(SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_ERROR_TYPE_2,
                        "门(急)诊诊断编码格式错误",   MAIN_CHECK_FIELD,
                        SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
            }
            keysBasicResultMap.put(MAIN_CHECK_FIELD, SettleValidateConst.PASS);
        }
        return null;
    }

    private void addErrorDetail(String index, String errType, String errDscr, String errorFields, String chkStas, SetlValidBaseBusiVo baseBusiVo) {
        SettleListHandlerVo handlerVo = new SettleListHandlerVo();
        handlerVo.setIndex(index);
        handlerVo.setErrDscr(errDscr);
        handlerVo.setErrorFields(errorFields);
        handlerVo.setChkStas(chkStas);
        handlerVo.setErrType(errType);
        baseBusiVo.getErrorDetails().add(handlerVo);
    }
}
