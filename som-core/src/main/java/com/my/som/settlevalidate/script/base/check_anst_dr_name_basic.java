package com.my.som.settlevalidate.script.base;

import com.my.som.model.dataHandle.SomHiInvyBasInfo;
import com.my.som.model.dataHandle.SomOprnOprtInfo;
import com.my.som.settlevalidate.constant.SettleValidateConst;
import com.my.som.settlevalidate.util.SettleValidateUtil;
import com.my.som.settlevalidate.vo.SetlValidBaseBusiVo;
import com.my.som.util.SettleListValidateUtil;
import com.my.som.vo.dataHandle.SettleListHandlerVo;
import com.my.som.vo.dataHandle.SettleListValidateVo;
import com.yomahub.liteflow.script.ScriptExecuteWrap;
import com.yomahub.liteflow.script.body.JaninoCommonScriptBody;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Map;

public class check_anst_dr_name_basic implements JaninoCommonScriptBody {
    private static final Logger logger = LoggerFactory.getLogger(check_anst_dr_name_basic.class);

    private static final String MAIN_CHECK_FIELD = "oprn_oprt_anst_dr_name";
    private static final String MAIN_CHECK_NAME = "麻醉医师姓名";
    private static final String MAIN_CHECK_REGION = "四川";
    /**
     * 麻醉医师姓名(anst_dr_name)质控
     * 1.存在手术或操作时且麻醉方式不为空，麻醉医师姓名不能为空
     * @param wrap
     * @return
     */
    @Override
    public Void body(ScriptExecuteWrap wrap) {

        SetlValidBaseBusiVo setlValidBaseBusiVo = (SetlValidBaseBusiVo) wrap.cmp.getFirstContextBean();
        SettleListValidateVo settleListValidateVo = (SettleListValidateVo) wrap.cmp.getRequestData();
        List<SomOprnOprtInfo> busOperateDiagnosisList = settleListValidateVo.getBusOperateDiagnosisList();
        SomHiInvyBasInfo somHiInvyBasInfo = settleListValidateVo.getSomHiInvyBasInfo();
        if (SettleValidateUtil.isEmpty(somHiInvyBasInfo)) {
            return null;
        }
        if (!SettleValidateUtil.isEmpty(busOperateDiagnosisList)) {
            for (int i = 0; i < busOperateDiagnosisList.size(); i++) {
                SomOprnOprtInfo somOprnOprtInfo = (SomOprnOprtInfo)busOperateDiagnosisList.get(i);
                // 麻醉方式不为空
                if(!SettleValidateUtil.isEmpty(somOprnOprtInfo.getC43())){
                    String anstName = somOprnOprtInfo.getOprn_oprt_anst_dr_name();
                    String anstCode = somOprnOprtInfo.getOprn_oprt_anst_dr_code();
                    if (SettleValidateUtil.isEmpty(anstName)) {
                        addErrorDetail(SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_ERROR_TYPE_1,
                                MAIN_CHECK_FIELD,
                                "第" + (i + 1) + "个手术操作：编码为["+somOprnOprtInfo.getC35c()+"]的" +MAIN_CHECK_NAME + "[" + somOprnOprtInfo.getOprn_oprt_anst_dr_name() + "]为空",
                                SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
                    }else{
                        if(SettleValidateUtil.isNotEmpty(anstCode)){
                            Map<String,Object> map = (Map<String,Object>)(setlValidBaseBusiVo.getSettleListParams().get(SettleListValidateUtil.KEY_DOCTOR_NAME));
                            Map<String,Object> doctorNameMap = (Map<String,Object>)map.get(somHiInvyBasInfo.getHospitalId()+SettleListValidateUtil.KEY_DOCTOR_NAME);
                            String doctorName= (String)doctorNameMap.get(anstCode);
                            if(SettleValidateUtil.isNotEmpty(doctorName)){
                                if(!anstName.equals(doctorName)){
                                    // 正常值，基础校验通过不写错误信息
                                    addErrorDetail( SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_ERROR_TYPE_2,
                                            MAIN_CHECK_FIELD,
                                            "第" + (i + 1) +"个手术操作：编码为["+somOprnOprtInfo.getC35c()+"]的"+ MAIN_CHECK_NAME + "[" + anstName + "]与该编码[" + anstCode + "]对应的医院麻醉医生名称[" + doctorName + "]不匹配",
                                            SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
                                }
                            }
                        }
                    }
                }

            }
        }
        return null;
    }

    private void addErrorDetail(String index,String errType,String errorFields, String errDscr, String chkStas, SetlValidBaseBusiVo baseBusiVo) {
        SettleListHandlerVo handlerVo = new SettleListHandlerVo();
        handlerVo.setIndex(index);
        handlerVo.setErrorFields(errorFields);
        handlerVo.setErrDscr(errDscr);
        handlerVo.setChkStas(chkStas);
        handlerVo.setErrType(errType);
        baseBusiVo.getErrorDetails().add(handlerVo);
    }
}
