package com.my.som.settlevalidate.script.base;

import com.my.som.model.dataHandle.SomHiInvyBasInfo;
import com.my.som.settlevalidate.constant.SettleValidateConst;
import com.my.som.settlevalidate.util.SettleValidateUtil;
import com.my.som.settlevalidate.vo.SetlValidBaseBusiVo;
import com.my.som.util.SettleListValidateUtil;
import com.my.som.vo.dataHandle.SettleListHandlerVo;
import com.my.som.vo.dataHandle.SettleListValidateVo;
import com.yomahub.liteflow.script.ScriptExecuteWrap;
import com.yomahub.liteflow.script.body.JaninoCommonScriptBody;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 判断 新生儿入院类型不为空
 */
public class check_nwb_adm_type_after_basic implements JaninoCommonScriptBody {
    private static final Logger logger = LoggerFactory.getLogger(check_nwb_adm_type_after_basic.class);

    private static final String MAIN_CHECK_FIELD = "a57";
    private static final String MAIN_CHECK_NAME = "新生儿入院类型";
    private static final String MAIN_CHECK_REGION = "四川";
    /**
     * 事后 校验
     * 新生儿入院类型(nwb_admtype) 基础质控
     * 判断新生儿入院类型是否为空
     *
     * @param wrap
     * @return
     */
    @Override
    public Void body(ScriptExecuteWrap wrap) {
        SetlValidBaseBusiVo setlValidBaseBusiVo = (SetlValidBaseBusiVo) wrap.cmp.getFirstContextBean();
        SettleListValidateVo settleListValidateVo = (SettleListValidateVo) wrap.cmp.getRequestData();
        Map<String, String> keysBasicResultMap = setlValidBaseBusiVo.getKeysBasicResultMap();
        SomHiInvyBasInfo somHiInvyBasInfo = settleListValidateVo.getSomHiInvyBasInfo();
        SettleListHandlerVo settleListHandlerVo = new SettleListHandlerVo();

        // 检查 somHiInvyBasInfo 是否为空
        if (SettleValidateUtil.isEmpty(somHiInvyBasInfo)) {
            addErrorDetail(settleListHandlerVo, SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY, SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY,
                    (String) SettleValidateConst.CHECK_TYPE_MAP.get(SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY), null,
                    SettleValidateConst.VALIDATE_STATE_FAIL, setlValidBaseBusiVo);
            return null;
        }

        // 新生儿入院类型
        String a57 = somHiInvyBasInfo.getA57();
        if (SettleValidateUtil.isEmpty(a57)) {
            keysBasicResultMap.put(MAIN_CHECK_FIELD, SettleValidateConst.NULL);
        } else {
            //新生儿入院类型 不为空，判断是否属于规范
            //获取规范类型
            Map<String,Object> map = (Map<String,Object>)setlValidBaseBusiVo.getSettleListParams().get(SettleListValidateUtil.KEY_DICT);
            List<String> birthTypes = (List<String>)map.get("XSELX");

            List<String> a57List = Arrays.asList(a57.split(","));

                boolean flag = true;
                //说明此时是多选情况
                for (int i = 0; i < a57List.size(); i++) {
                    if(!birthTypes.contains(a57List.get(i))){
                        SettleValidateUtil.addErrorDetail(settleListHandlerVo, SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_TYPE_2,
                                MAIN_CHECK_NAME + "为[" + a57 + "]其中第"+(i+1)+"个新生儿入院类型不符合规范", MAIN_CHECK_FIELD,
                                SettleValidateConst.OUTLIER, setlValidBaseBusiVo);
                        flag = false;
                    }
                }
                if(flag){
                    keysBasicResultMap.put(MAIN_CHECK_FIELD, SettleValidateConst.PASS);
                }else{
                    keysBasicResultMap.put(MAIN_CHECK_FIELD, SettleValidateConst.OUTLIER);
                }

        }
        return null;
    }

    private void addErrorDetail(SettleListHandlerVo handlerVo, String index, String errType, String errDscr, String errorFields, String chkStas, SetlValidBaseBusiVo baseBusiVo) {
        handlerVo.setIndex(index);
        handlerVo.setErrDscr(errDscr);
        handlerVo.setErrorFields(errorFields);
        handlerVo.setChkStas(chkStas);
        handlerVo.setErrType(errType);
        baseBusiVo.getErrorDetails().add(handlerVo);
    }
}

