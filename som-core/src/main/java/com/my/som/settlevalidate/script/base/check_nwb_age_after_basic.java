package com.my.som.settlevalidate.script.base;

import com.my.som.model.dataHandle.SomHiInvyBasInfo;
import com.my.som.settlevalidate.constant.SettleValidateConst;
import com.my.som.settlevalidate.util.SettleValidateUtil;
import com.my.som.settlevalidate.vo.SetlValidBaseBusiVo;
import com.my.som.util.SettleListValidateUtil;
import com.my.som.vo.dataHandle.SettleListHandlerVo;
import com.my.som.vo.dataHandle.SettleListValidateVo;
import com.yomahub.liteflow.script.ScriptExecuteWrap;
import com.yomahub.liteflow.script.body.JaninoCommonScriptBody;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

/**
 * [新生儿天龄-[a16]-基础校验]
 */
public class check_nwb_age_after_basic implements JaninoCommonScriptBody {
    private static final Logger logger = LoggerFactory.getLogger(check_nwb_age_after_basic.class);
    private static final String MAIN_CHECK_FIELD = "a16";
    private static final String MAIN_CHECK_NAME = "新生儿天龄";
    private static final String MAIN_CHECK_REGION = "四川,湖北";

    /**
     * [新生儿天龄-[a16]-基础校验]
     * 1、门(急)诊诊断编码出现P10-P15,
     * 2. 天龄不为空
     * 3. 且(年龄不足1周岁的)天龄必须小于365天
     *
     * @param wrap
     * @return
     */
    @Override
    public Void body(ScriptExecuteWrap wrap) {

        SetlValidBaseBusiVo setlValidBaseBusiVo = (SetlValidBaseBusiVo) wrap.cmp.getFirstContextBean();
        SettleListValidateVo settleListValidateVo = (SettleListValidateVo) wrap.cmp.getRequestData();
        Map<String, String> keysBasicResultMap = setlValidBaseBusiVo.getKeysBasicResultMap();
        SomHiInvyBasInfo somHiInvyBasInfo = settleListValidateVo.getSomHiInvyBasInfo();
        SettleListHandlerVo settleListHandlerVo = new SettleListHandlerVo();
        if (SettleValidateUtil.isEmpty(somHiInvyBasInfo)) {
            return null;
        }
        Integer a16 = somHiInvyBasInfo.getA16();

        String c01c = somHiInvyBasInfo.getC01c();

        if (!SettleValidateUtil.isEmpty(a16) && SettleValidateUtil.isNotEmpty(c01c))  {
            for(String code : SettleListValidateUtil.P10TP15_LIST) {
                if (c01c.startsWith(code)) {
                    if (!SettleValidateUtil.isEmpty(a16)) {
                        //不为空,则一定是一个正常值，判断范围是否在[200,10000]
                        if (!(a16 >= 1 && a16 <= 365)) {
                            //不在合理值范围，提示
                            addErrorDetail(SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_ERROR_TYPE_2,
                                    MAIN_CHECK_FIELD,
                                    "门(急)诊诊断编码出现P10-P15" + MAIN_CHECK_NAME + "[" + a16 + "]超出合理值范围",
                                    SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
                        }
                    } else {
                        addErrorDetail(SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_ERROR_TYPE_2,
                                MAIN_CHECK_FIELD,
                                "门(急)诊诊断编码出现P10-P15" + MAIN_CHECK_NAME + "[" + a16 + "]为空",
                                SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
                    }
                }
            }
        }
        return null;
    }

    private void addErrorDetail(String index, String errType, String errorFields, String errDscr, String chkStas, SetlValidBaseBusiVo baseBusiVo) {
        SettleListHandlerVo handlerVo = new SettleListHandlerVo();
        handlerVo.setIndex(index);
        handlerVo.setErrorFields(errorFields);
        handlerVo.setErrDscr(errDscr);
        handlerVo.setChkStas(chkStas);
        handlerVo.setErrType(errType);
        baseBusiVo.getErrorDetails().add(handlerVo);
    }
}
