package com.my.som.settlevalidate.script.base;

import com.my.som.model.dataHandle.SomHiInvyBasInfo;
import com.my.som.settlevalidate.constant.SettleValidateConst;
import com.my.som.settlevalidate.util.SettleValidateUtil;
import com.my.som.settlevalidate.vo.SetlValidBaseBusiVo;
import com.my.som.util.SettleListValidateUtil;
import com.my.som.vo.dataHandle.SettleListHandlerVo;
import com.my.som.vo.dataHandle.SettleListValidateVo;
import com.yomahub.liteflow.script.ScriptExecuteWrap;
import com.yomahub.liteflow.script.body.JaninoCommonScriptBody;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 判断 门（急）诊诊断（中医诊断）
 */
public class check_otp_tcm_dise_after_basic implements JaninoCommonScriptBody {
    private static final Logger logger = LoggerFactory.getLogger(check_otp_tcm_dise_after_basic.class);
    private static final String MAIN_CHECK_FIELD = "c35c";
    private static final String MAIN_CHECK_NAME = "门（急）诊诊断编码（中医）";
    private static final String MAIN_CHECK_FIELD1 = "c36n";
    private static final String MAIN_CHECK_NAME1 = "门（急）诊诊断名称（中医）";
    private static final String MAIN_CHECK_REGION = "四川,湖北";
    /**
     * 事后校验
     * 门（急）诊诊断编码（中医）(otp_tcm_dise_name)基础质控
     * 判断门（急）诊诊断编码（中医）是否为空
     *
     * @param wrap
     * @return
     */
    @Override
    public Void body(ScriptExecuteWrap wrap) {
        SetlValidBaseBusiVo setlValidBaseBusiVo = (SetlValidBaseBusiVo) wrap.cmp.getFirstContextBean();
        SettleListValidateVo settleListValidateVo = (SettleListValidateVo) wrap.cmp.getRequestData();
        Map<String, String> keysBasicResultMap = setlValidBaseBusiVo.getKeysBasicResultMap();
        SomHiInvyBasInfo somHiInvyBasInfo = settleListValidateVo.getSomHiInvyBasInfo();
        SettleListHandlerVo settleListHandlerVo = new SettleListHandlerVo();

        // 检查 somHiInvyBasInfo 是否为空
        if (SettleValidateUtil.isEmpty(somHiInvyBasInfo)) {
            addErrorDetail(SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY, SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY,
                    (String) SettleValidateConst.CHECK_TYPE_MAP.get(SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY), null,
                    SettleValidateConst.VALIDATE_STATE_FAIL, setlValidBaseBusiVo);
            return null;
        }
        // 获取主证诊断
        Map<String, String> principaltMap = (Map<String, String>) setlValidBaseBusiVo.getSettleListParams().get(SettleListValidateUtil.KEY_TCM_PRINCIPAL);
        //获取主病诊断
        Map<String, String> mainDiseasetMap = (Map<String, String>) setlValidBaseBusiVo.getSettleListParams().get(SettleListValidateUtil.KEY_TCM_MAIN_DISS);

        // 获取门（急）诊诊断名称（中医）
        String c35c = somHiInvyBasInfo.getC35c();
        String c36n = somHiInvyBasInfo.getC36n();

        if (SettleValidateUtil.isNotEmpty(c35c)) {
            if (SettleValidateUtil.isEmpty(c36n)) {
                addErrorDetail(SettleValidateConst.VALIDATE_TYPE_2, SettleValidateConst.VALIDATE_ERROR_TYPE_1,
                        MAIN_CHECK_NAME1 + "[" + c36n + "]为空", MAIN_CHECK_FIELD1,
                        SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
            }

            if (principaltMap.containsKey(c35c)) {
                    List<String> list = Arrays.asList(principaltMap.get(c35c).toString().split(","));
                    if (!list.contains(c36n)) {
                        addErrorDetail(SettleValidateConst.VALIDATE_TYPE_2, SettleValidateConst.VALIDATE_ERROR_TYPE_2,
                                MAIN_CHECK_NAME1 + "[" + c36n + "]与中医主证编码对应的名称不符", MAIN_CHECK_FIELD1,
                                SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
                    }
            } else if (mainDiseasetMap.containsKey(c35c)) {
                List<String> list = Arrays.asList(mainDiseasetMap.get(c35c).toString().split(","));
                if (!list.contains(c36n)) {
                    addErrorDetail(SettleValidateConst.VALIDATE_TYPE_2, SettleValidateConst.VALIDATE_ERROR_TYPE_2,
                            MAIN_CHECK_NAME1 + "[" + c36n + "]与中医主病编码对应的名称不符", MAIN_CHECK_FIELD1,
                            SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
                }
            } else {
                addErrorDetail(SettleValidateConst.VALIDATE_TYPE_2, SettleValidateConst.VALIDATE_ERROR_TYPE_2,
                        MAIN_CHECK_NAME + "[" + c35c + "]不属于中医主证或者中医主病编码标准代码", MAIN_CHECK_FIELD,
                        SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
            }

        } else {
            if (SettleValidateUtil.isNotEmpty(c36n)) {
                addErrorDetail(SettleValidateConst.VALIDATE_TYPE_2, SettleValidateConst.VALIDATE_ERROR_TYPE_1,
                        MAIN_CHECK_NAME + "[" + c35c + "]为空但是" + MAIN_CHECK_NAME1 + "存在", MAIN_CHECK_FIELD,
                        SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
            }
        }
        return null;
    }

    private void addErrorDetail(String index, String errType, String errDscr, String errorFields, String chkStas, SetlValidBaseBusiVo baseBusiVo) {
        SettleListHandlerVo handlerVo = new SettleListHandlerVo();
        handlerVo.setIndex(index);
        handlerVo.setErrDscr(errDscr);
        handlerVo.setErrorFields(errorFields);
        handlerVo.setChkStas(chkStas);
        handlerVo.setErrType(errType);
        baseBusiVo.getErrorDetails().add(handlerVo);
    }
}
