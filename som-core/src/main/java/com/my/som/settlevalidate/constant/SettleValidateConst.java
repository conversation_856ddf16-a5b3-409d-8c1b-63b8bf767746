package com.my.som.settlevalidate.constant;

import com.my.som.common.util.ValidateUtil;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 结算清单质控常量
 *
 * @author: zyd
 */
public interface SettleValidateConst {

    /**
     * 基本信息校验使用 结算清单错误runtime异常类型 SLET
     * 类型(SLET001.结算清单信息，SLET002.基金支付信息，SLET03.门诊慢特病诊断信息，SLET004.住院诊断信息，SLET005.收费项目信息，SLET006.手术操作信息，SLET007.重症监护信息，SLET012.输血)
     */
    String VALIDATE_INFO_TYPE_BASE_EMPTY = "SLET001";
    String VALIDATE_INFO_TYPE_PAY_EMPTY = "SLET002";
    String VALIDATE_INFO_TYPE_MMMT_EMPTY = "SLET03";
    String VALIDATE_INFO_TYPE_DIS_EMPTY = "SLET004";
    String VALIDATE_INFO_TYPE_ITEM_EMPTY = "SLET005";
    String VALIDATE_INFO_TYPE_OPE_EMPTY = "SLET006";
    String VALIDATE_INFO_TYPE_ICU_EMPTY = "SLET007";
    String VALIDATE_INFO_TYPE_TRF_EMPTY = "SLET012";


    /**
     * 清单校验类型-校验手术编码为医保2.0编码
     */
    String SLVT001 = "SLVT001";
    /**
     * 清单校验类型-校验诊断编码为医保2.0编码
     */
    String SLVT002 = "SLVT002";
    /**
     * 清单校验类型-校验性别与编码不匹配
     */
    String SLVT003 = "SLVT003";
    /**
     * 清单校验类型-校验年龄与编码不匹配
     */
    String SLVT004 = "SLVT004";
    /**
     * 清单校验类型-校验不能作为主诊编码
     */
    String SLVT005 = "SLVT005";
    /**
     * 清单校验类型-校验主要诊断编码不恰当
     */
    String SLVT006 = "SLVT006";
    /**
     * 清单校验类型-校验使用残余类目编码
     */
    String SLVT007 = "SLVT007";
    /**
     * 清单校验类型-校验使用医保2.0诊断灰码
     */
    String SLVT008 = "SLVT008";
    /**
     * 清单校验类型-校验使用医保2.0手术灰码
     */
    String SLVT009 = "SLVT009";
    /**
     * 清单校验类型-校验费用明细错编漏编
     */
    String SLVT010 = "SLVT010";
    /**
     * 清单校验类型-校验诊断编码中是否存在联合编码冲突
     */
    String SLVT011 = "SLVT011";

    String PASS = "PASS-VALUE";
    /**
     * 不合理值
     */
    String OUTLIER = "OUTLIER-VALUE";
    /**
     * 空
     */
    String NULL = "NULL-VALUE";
    String NULL_ = "-";
    /**
     * 校验类型1：基础质控 2：编码质控 3：逻辑质控
     */
    String VALIDATE_TYPE_1 = "1";
    String VALIDATE_TYPE_2 = "2";
    String VALIDATE_TYPE_3 = "3";

    /**
     * 校验状态 success: 成功 fail: 失败
     */
    String VALIDATE_STATE_SUCCESS = "1";
    String VALIDATE_STATE_FAIL = "0";

    /**
     * 是否启用
     */
    String IS_Y = "-Y";

    /**
     * 字段前缀
     */
    String FIELD_PREFIX = "get";
    /**
     * 错误分隔符
     */
    String ERROR_DESC_SEPARATOR = ";";

    /**
     * 空值后缀
     */
    String ERROR_NULL_FIELD_SUFFIX = "为空";
    /**
     * 字典未找到后缀
     */
    String ERROR_DICT_DESC_SUFFIX = "未找到对应字典值";
    /**
     * 正则校验后缀
     */
    String ERROR_REG_SUFFIX = "填写不规范";
    /**
     * 长度衣校验后缀
     */
    String ERROR_LENGTH_SUFFIX = "长度填写不规范";

    String ERR_TYPE_1 = "";

    /**
     * 联合编码间隔字符
     */
    String MERGE_DELIMITER = "@";

    /**
     * 空值
     */
    String VALIDATE_ERROR_TYPE_1 = "1";
    /**
     * 字典填写错误
     */
    String VALIDATE_ERROR_TYPE_2 = "2";
    /**
     * 正则匹配错误
     */
    String VALIDATE_ERROR_TYPE_3 = "3";
    /**
     * 长度错误
     */
    String VALIDATE_ERROR_TYPE_4 = "4";
    /**
     * 手术不是医保2.0编码
     */
    String VALIDATE_ERROR_TYPE_5 = "5";
    /**
     * 诊断不是医保2.0编码
     */
    String VALIDATE_ERROR_TYPE_6 = "6";
    /**
     * 性别与编码不匹配
     */
    String VALIDATE_ERROR_TYPE_7 = "7";
    /**
     * 年龄与编码不匹配
     */
    String VALIDATE_ERROR_TYPE_8 = "8";
    /**
     * 不能作为主诊编码
     */
    String VALIDATE_ERROR_TYPE_9 = "9";
    /**
     * 主要诊断编码不恰当
     */
    String VALIDATE_ERROR_TYPE_10 = "10";
    /**
     * 使用残余类目编码
     */
    String VALIDATE_ERROR_TYPE_11 = "11";
    /**
     * 使用医保2.0诊断灰码
     */
    String VALIDATE_ERROR_TYPE_12 = "12";
    /**
     * 使用医保2.0手术灰码
     */
    String VALIDATE_ERROR_TYPE_13 = "13";
    /**
     * 手术操作名称填写不正确
     */
    String VALIDATE_ERROR_TYPE_14 = "14";
    /**
     * 费用明细错编漏编
     */
    String VALIDATE_ERROR_TYPE_15 = "15";
    /**
     * 脚本异常错误
     */
    String VALIDATE_ERROR_TYPE_17 = "17";
    /**
     * DRG灰码
     */
    String VALIDATE_ERROR_TYPE_101 = "101";
    /**
     * 性别与身份证不符
     */
    String VALIDATE_ERROR_TYPE_301 = "301";
    /**
     * 年龄或天龄错误
     */
    String VALIDATE_ERROR_TYPE_302 = "302";
    /**
     * 新生儿错误
     */
    String VALIDATE_ERROR_TYPE_303 = "303";
    /**
     * 时间错误
     */
    String VALIDATE_ERROR_TYPE_304 = "304";
    /**
     * 门急诊编码错误
     */
    String VALIDATE_ERROR_TYPE_305 = "305";
    /**
     * 诊断编码错误
     */
    String VALIDATE_ERROR_TYPE_306 = "306";
    /**
     * 手术编码错误
     */
    String VALIDATE_ERROR_TYPE_307 = "307";
    /**
     * 联系人信息错误
     */
    String VALIDATE_ERROR_TYPE_308 = "308";


    Map<String, String> CHECK_TYPE_MAP = new HashMap<String, String>() {
        {
            put("SLET001", "结算清单信息为空");
            put("SLET002", "基金支付信息为空");
            put("SLET03", "门诊慢特病诊断信息为空");
            put("SLET004", "住院诊断信息为空");
            put("SLET005", "收费项目信息为空");
            put("SLET006", "手术操作信息为空");
            put("SLET007", "重症监护信息为空");
            put("SLET012", "输血信息为空");
            put("SLVT001", "校验手术编码为医保2.0编码");
            put("SLVT002", "校验诊断编码为医保2.0编码");
            put("SLVT003", "校验性别与编码不匹配");
            put("SLVT004", "校验年龄与编码不匹配");
            put("SLVT005", "校验不能作为主诊编码");
            put("SLVT006", "校验主要诊断编码不恰当");
            put("SLVT007", "校验使用残余类目编码");
            put("SLVT008", "校验使用医保2.0诊断灰码");
            put("SLVT009", "校验使用医保2.0手术灰码");
            put("SLVT010", "校验费用明细错编漏编");
            put("SLVT011", "校验诊断编码中是否存在联合编码冲突");
        }
    };

}

