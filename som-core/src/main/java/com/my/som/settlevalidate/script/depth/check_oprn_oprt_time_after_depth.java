package com.my.som.settlevalidate.script.depth;

import com.my.som.model.dataHandle.SomHiInvyBasInfo;
import com.my.som.model.dataHandle.SomOprnOprtInfo;
import com.my.som.settlevalidate.constant.SettleValidateConst;
import com.my.som.settlevalidate.util.SettleValidateUtil;
import com.my.som.settlevalidate.vo.SetlValidBaseBusiVo;
import com.my.som.vo.dataHandle.SettleListHandlerVo;
import com.my.som.vo.dataHandle.SettleListValidateVo;
import com.yomahub.liteflow.script.ScriptExecuteWrap;
import com.yomahub.liteflow.script.body.JaninoCommonScriptBody;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.*;

/**
 * 事后判断 手术操作时间合理性
 */
public class check_oprn_oprt_time_after_depth implements JaninoCommonScriptBody {
    private static final Logger logger = LoggerFactory.getLogger(check_oprn_oprt_time_after_depth.class);
    private static final String MAIN_CHECK_FIELD = "oprnOprtTime";
    private static final String MAIN_CHECK_NAME = "手术时间";
    private static final String MAIN_CHECK_REGION = "四川";

    private static final String MAIN_CHECK_FIELD_1 = "oprnOprtBegntime";
    private static final String MAIN_CHECK_NAME_1 = "手术开始时间";

    private static final String MAIN_CHECK_FIELD_2 = "oprnOprtEndtime";
    private static final String MAIN_CHECK_NAME_2 = "手术结束时间";
    private static final String MAIN_CHECK_FIELD_3 = "b12";
    private static final String MAIN_CHECK_FIELD_4 = "b15";

    /**
     * 手术及操作起止时间
     * <p>
     * 1.出院时间 大于 手术结束时间 大于 手术开始时间 大于 入院时间按
     * 2.判断手术代码是否存在医保停用码。
     * 3.判断手术代码是否存在重复
     *
     * @param wrap
     * @return
     */
    @Override
    public Void body(ScriptExecuteWrap wrap) {

        SetlValidBaseBusiVo setlValidBaseBusiVo = (SetlValidBaseBusiVo) wrap.cmp.getFirstContextBean();
        SettleListValidateVo settleListValidateVo = (SettleListValidateVo) wrap.cmp.getRequestData();
        List<SomOprnOprtInfo> busOperateDiagnosisList = settleListValidateVo.getBusOperateDiagnosisList();
        SomHiInvyBasInfo somHiInvyBasInfo = settleListValidateVo.getSomHiInvyBasInfo();
        SettleListHandlerVo settleListHandlerVo = new SettleListHandlerVo();
        Map<String, String> keysBasicResultMap = setlValidBaseBusiVo.getKeysBasicResultMap();

        // 检查 somHiInvyBasInfo 是否为空
        if (SettleValidateUtil.isEmpty(somHiInvyBasInfo)) {
            addErrorDetail(SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY, SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY,
                    (String) SettleValidateConst.CHECK_TYPE_MAP.get(SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY), MAIN_CHECK_FIELD_2,
                    SettleValidateConst.VALIDATE_STATE_FAIL, setlValidBaseBusiVo);
            return null;
        }

        // 检查手术信息是否为空
        if (SettleValidateUtil.isEmpty(busOperateDiagnosisList)) {
            return null;
        }
        if (SettleValidateConst.PASS.equals((String) keysBasicResultMap.get(MAIN_CHECK_FIELD_3))
                && SettleValidateConst.PASS.equals((String) keysBasicResultMap.get(MAIN_CHECK_FIELD_4))) {
            //入院时间
            String b12 = somHiInvyBasInfo.getB12();
            //出院时间
            String b15 = somHiInvyBasInfo.getB15();

            // 遍历操作列表
            for (int i = 0; i < busOperateDiagnosisList.size(); i++) {
                SomOprnOprtInfo somOprnOprtInfo = (SomOprnOprtInfo)busOperateDiagnosisList.get(i);
                String oprnOprtBegntime = somOprnOprtInfo.getOprnOprtBegntime();
                String oprnOprtEndtime = somOprnOprtInfo.getOprnOprtEndtime();

                //如果手术时间不为空
                if (SettleValidateConst.PASS.equals((String) keysBasicResultMap.get(i + MAIN_CHECK_FIELD_1))
                        && SettleValidateConst.PASS.equals((String) keysBasicResultMap.get(i + MAIN_CHECK_FIELD_2))) {
                    //说明此时需要麻醉时间判断的规范性
                    boolean isStandards = checkOprtTimestandards(i, oprnOprtBegntime, somOprnOprtInfo, setlValidBaseBusiVo, oprnOprtEndtime);
                    if (!isStandards) {
                        continue;
                    }
                    //判断手术时间合理性
                    checkOprtIsplausible(i, b12, oprnOprtBegntime, somOprnOprtInfo, setlValidBaseBusiVo, oprnOprtEndtime, b15);
                }
            }
        }
        return null;
    }

    private void checkOprtIsplausible(int i, String b12, String oprnOprtBegntime, SomOprnOprtInfo somOprnOprtInfo, SetlValidBaseBusiVo setlValidBaseBusiVo, String oprnOprtEndtime, String b15) {
        //手术开始时间应该大于入院时间
        if (judgeTimeOK(oprnOprtBegntime, b12)) {
            addErrorDetail(SettleValidateConst.VALIDATE_TYPE_2, SettleValidateConst.VALIDATE_TYPE_2,
                    "第" + (i + 1) + "个手术操作：编码为["+somOprnOprtInfo.getC35c()+"]的" + MAIN_CHECK_NAME_1 + "[" + oprnOprtBegntime + "] 应该大于入院时间[" + b12 + "]",
                    MAIN_CHECK_FIELD_1,
                    SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
        }


        //手术结束时间应该小于出院时间
        if (judgeTimeOK(b15,oprnOprtEndtime)) {
            addErrorDetail(SettleValidateConst.VALIDATE_TYPE_2, SettleValidateConst.VALIDATE_TYPE_2,
                    "第" + (i + 1) + "个手术操作：编码为["+somOprnOprtInfo.getC35c()+"]的" +  MAIN_CHECK_NAME_2 + "[" + oprnOprtEndtime + "] 应该小于出院时间[" + b15 + "]",
                    MAIN_CHECK_FIELD_2,
                    SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
        }


        //手术结束时间应该大于手术开始时间
        if (judgeTimeOK(oprnOprtEndtime, oprnOprtBegntime)) {
            addErrorDetail(SettleValidateConst.VALIDATE_TYPE_2, SettleValidateConst.VALIDATE_TYPE_2,
                    "第" + (i + 1) + "个手术操作：编码为["+somOprnOprtInfo.getC35c()+"]的" +  "手术开始时间[" + oprnOprtBegntime + "]应该小于手术结束时间[" + oprnOprtEndtime + "]",
                    MAIN_CHECK_FIELD_1,
                    SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
        }
    }

    private boolean checkOprtTimestandards(int i, String oprnOprtBegntime, SomOprnOprtInfo somOprnOprtInfo, SetlValidBaseBusiVo setlValidBaseBusiVo, String oprnOprtEndtime) {
        boolean flag = true;
        if (!isValidDateTime(oprnOprtBegntime)) {
            addErrorDetail(SettleValidateConst.VALIDATE_TYPE_2, SettleValidateConst.VALIDATE_ERROR_TYPE_2,
                    "第" + (i + 1) + "个手术操作：编码为["+somOprnOprtInfo.getC35c()+"]的" +  MAIN_CHECK_NAME_1 + "[" + oprnOprtBegntime + "] 格式错误",
                    MAIN_CHECK_FIELD_1,
                    SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
            flag = false;
        }

        if (!isValidDateTime(oprnOprtEndtime)) {
            addErrorDetail(SettleValidateConst.VALIDATE_TYPE_2, SettleValidateConst.VALIDATE_ERROR_TYPE_2,
                    "第" + (i + 1) + "个手术操作：编码为["+somOprnOprtInfo.getC35c()+"]的" +  MAIN_CHECK_NAME_2 + "[" + oprnOprtEndtime + "] 格式错误",
                    MAIN_CHECK_FIELD_2,
                    SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
            flag = false;
        }
        return flag;
    }

    public static boolean isValidDateTime(String dateTimeStr) {
        String format = "yyyy-MM-dd HH:mm:ss";
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(format);
        try {
            LocalDateTime.parse(dateTimeStr, formatter);
            return true;
        } catch (DateTimeParseException e) {
            return false;
        }
    }

    private void addErrorDetail(String index, String errType, String errDscr, String errorFields, String chkStas, SetlValidBaseBusiVo baseBusiVo) {
        SettleListHandlerVo handlerVo = new SettleListHandlerVo();
        handlerVo.setIndex(index);
        handlerVo.setErrorFields(errorFields);
        handlerVo.setErrDscr(errDscr);
        handlerVo.setChkStas(chkStas);
        handlerVo.setErrType(errType);
        baseBusiVo.getErrorDetails().add(handlerVo);
    }

    /**
     * 时间2 大于  时间1  返回 true
     *
     * @param time1 时间1
     * @param time2 时间2
     * @return
     */
    private boolean judgeTimeOK(String time1, String time2) {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        boolean flag = false;
        try {
            Date admDate = dateFormat.parse(time1);
            Date dscgDate = dateFormat.parse(time2);
            // 入院时间大于出院时间
            if (dscgDate.after(admDate)) {
                flag = true;
            }
        } catch (ParseException e) {
            logger.error("手术时间质检 日期解析失败: " + e.getMessage());
        }
        return flag;
    }
}
