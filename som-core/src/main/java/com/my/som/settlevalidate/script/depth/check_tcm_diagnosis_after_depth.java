package com.my.som.settlevalidate.script.depth;

import com.my.som.dto.medicalQuality.BusDiseaseDiagnosisTrim;
import com.my.som.settlevalidate.constant.SettleValidateConst;
import com.my.som.settlevalidate.util.SettleValidateUtil;
import com.my.som.settlevalidate.vo.SetlValidBaseBusiVo;
import com.my.som.util.SettleListValidateUtil;
import com.my.som.vo.dataHandle.SettleListHandlerVo;
import com.my.som.vo.dataHandle.SettleListValidateVo;
import com.yomahub.liteflow.script.ScriptExecuteWrap;
import com.yomahub.liteflow.script.body.JaninoCommonScriptBody;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Map;

/**
 * 出院中医诊断
 */
public class check_tcm_diagnosis_after_depth implements JaninoCommonScriptBody {
    private static final Logger logger = LoggerFactory.getLogger(check_tcm_diagnosis_after_depth.class);

    private static final String MAIN_CHECK_NAME = "出院中医诊断编码";
    private static final String MAIN_CHECK_FIELD = "c06c2";
    private static final String MAIN_CHECK_NAME1 = "出院中医诊断名称";
    private static final String MAIN_CHECK_CODE = "c06c2";

    private static final String MAIN_CHECK_CODE1 = "c07n2";
    private static final String TCM_MAIN_DISE = "2";
    private static final String TCM_PRINCIPAL_DIAG = "3";
    private static final String MAIN_CHECK_REGION = "四川,湖北";
    /**
     * 主要诊断 逻辑质控
     *
     * @param wrap
     * @return
     */
    @Override
    public Void body(ScriptExecuteWrap wrap) {

        SetlValidBaseBusiVo setlValidBaseBusiVo = (SetlValidBaseBusiVo) wrap.cmp.getFirstContextBean();
        SettleListValidateVo settleListValidateVo = (SettleListValidateVo) wrap.cmp.getRequestData();


        // 获取主证诊断
        Map<String, Object> principaltMap = (Map<String, Object>) setlValidBaseBusiVo.getSettleListParams().get(SettleListValidateUtil.KEY_TCM_PRINCIPAL);
        //获取主病诊断
        Map<String, Object> mainDiseasetMap = (Map<String, Object>) setlValidBaseBusiVo.getSettleListParams().get(SettleListValidateUtil.KEY_TCM_MAIN_DISS);

        List<BusDiseaseDiagnosisTrim> busDiseaseDiagnosisTrimsList = settleListValidateVo.getBusDiseaseDiagnosisTrimsList();

        // 检查诊断信息是否为空
        if (SettleValidateUtil.isEmpty(busDiseaseDiagnosisTrimsList)) {
            return null;
        }
        for (int i = 0; i < busDiseaseDiagnosisTrimsList.size(); i++) {
            BusDiseaseDiagnosisTrim vo = (BusDiseaseDiagnosisTrim)busDiseaseDiagnosisTrimsList.get(i);
            if(SettleValidateUtil.isNotEmpty(vo.getC06c1()) ){
                if(SettleValidateUtil.isEmpty(vo.getC07n1())){
                    addErrorDetail(SettleValidateConst.VALIDATE_TYPE_2, SettleValidateConst.VALIDATE_ERROR_TYPE_1,
                            MAIN_CHECK_CODE1,
                            "第" + (i + 1) + "个疾病诊断：编码为[" +vo.getC06c1()+"]的"+  MAIN_CHECK_NAME1 + "[" + vo.getC07n1() + "]为空",
                            SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
                }else{
                    if(TCM_PRINCIPAL_DIAG.equals(vo.getType())) {
                        if(principaltMap.containsKey(vo.getC06c1())){
                            if(!principaltMap.get(vo.getC06c1()).toString().contains(vo.getC07n1())){
                                addErrorDetail(SettleValidateConst.VALIDATE_TYPE_2, SettleValidateConst.VALIDATE_ERROR_TYPE_2,
                                        MAIN_CHECK_CODE1,
                                        "第" + (i + 1) + "个疾病诊断：编码为[" +vo.getC06c1()+"]的"+MAIN_CHECK_NAME1 + "[" + vo.getC07n1() + "]与中医主证编码对应的名称不符",
                                        SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
                            }
                       }else{
                            addErrorDetail(SettleValidateConst.VALIDATE_TYPE_2, SettleValidateConst.VALIDATE_ERROR_TYPE_2,
                                    MAIN_CHECK_CODE1,
                                    "第" + (i + 1) + "个疾病诊断：编码为[" +vo.getC06c1()+"]的"+MAIN_CHECK_NAME1 + "[" + vo.getC07n1() + "]未在中医主证编码中",
                                    SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
                        }
                    }
                    else if(TCM_MAIN_DISE.equals(vo.getType())) {
                        if (mainDiseasetMap.containsKey(vo.getC06c1())) {
                            if(!mainDiseasetMap.get(vo.getC06c1()).toString().contains(vo.getC07n1())){
                            addErrorDetail(SettleValidateConst.VALIDATE_TYPE_2, SettleValidateConst.VALIDATE_ERROR_TYPE_2,
                                    MAIN_CHECK_CODE1,
                                    "第" + (i + 1) + "个疾病诊断：编码为[" +vo.getC06c1()+"]的"+MAIN_CHECK_NAME1 + "[" + vo.getC07n1()+ "]与中医主病编码对应的名称不符",
                                    SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
                         }
                        }else{
                            addErrorDetail(SettleValidateConst.VALIDATE_TYPE_2, SettleValidateConst.VALIDATE_ERROR_TYPE_2,
                                    MAIN_CHECK_CODE1,
                                    "第" + (i + 1) + "个疾病诊断：编码为[" +vo.getC06c1()+"]的"+MAIN_CHECK_NAME1 + "[" + vo.getC07n1() + "]未在中医主病编码中",
                                    SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
                        }
                    }
//                    else {
//                        addErrorDetail(SettleValidateConst.VALIDATE_TYPE_2, SettleValidateConst.VALIDATE_ERROR_TYPE_1,
//                                MAIN_CHECK_CODE,
//                                "第" + (i + 1) + "个疾病诊断：编码为[" +vo.getC06c1()+"]的"+MAIN_CHECK_NAME + "[" + vo.getC06c1() + "]不属于中医主证或者中医主病编码标准代码",
//                                SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
//                    }
                }
            }
//                else{
//                if(SettleValidateUtil.isNotEmpty(vo.getC07n1())){
//                    addErrorDetail(SettleValidateConst.VALIDATE_TYPE_2, SettleValidateConst.VALIDATE_ERROR_TYPE_1,
//                            MAIN_CHECK_CODE,
//                            "第" + (i + 1) + "个疾病诊断：编码为[" +vo.getC06c1()+"]的"+MAIN_CHECK_NAME + "[" + vo.getC06c1() + "]为空但是"+MAIN_CHECK_NAME1+"存在",
//                            SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
//                }
//            }

        }
        return null;
    }

    private void addErrorDetail(String index,String errType,String errorFields, String errDscr, String chkStas, SetlValidBaseBusiVo baseBusiVo) {
        SettleListHandlerVo handlerVo = new SettleListHandlerVo();
        handlerVo.setIndex(index);
        handlerVo.setErrorFields(errorFields);
        handlerVo.setErrDscr(errDscr);
        handlerVo.setChkStas(chkStas);
        handlerVo.setErrType(errType);
        baseBusiVo.getErrorDetails().add(handlerVo);
    }
}
