package com.my.som.settlevalidate.script.depth;

import com.my.som.model.dataHandle.SomHiInvyBasInfo;
import com.my.som.settlevalidate.constant.SettleValidateConst;
import com.my.som.settlevalidate.util.SettleValidateUtil;
import com.my.som.settlevalidate.vo.SetlValidBaseBusiVo;
import com.my.som.vo.dataHandle.SettleListHandlerVo;
import com.my.som.vo.dataHandle.SettleListValidateVo;
import com.yomahub.liteflow.script.ScriptExecuteWrap;
import com.yomahub.liteflow.script.body.JaninoCommonScriptBody;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

public class check_nwb_age_depth implements JaninoCommonScriptBody {
    private static final Logger logger = LoggerFactory.getLogger(check_nwb_age_depth.class);

    private static final String MAIN_CHECK_FIELD = "a16";
    private static final String MAIN_CHECK_NAME = "不足一周岁年龄(天)";
    private static final String MAIN_CHECK_FIELD_1 = "a16";
    private static final String MAIN_CHECK_NAME_1 = "不足一周岁年龄(天)";
    private static final String MAIN_CHECK_FIELD_2 = "a14";
    private static final String MAIN_CHECK_NAME_2 = "年龄(岁)";
    private static final String MAIN_CHECK_REGION = "四川";
    /**
     * 新生儿年龄(nwb_age)深度质控
     * 1.年龄(岁)和年龄(天)不能同时存在
     * 2.年龄(岁)为空时，年龄(天)不能同时为空
     * @param wrap
     * @return
     */
    @Override
    public Void body(ScriptExecuteWrap wrap) {
        SetlValidBaseBusiVo setlValidBaseBusiVo = (SetlValidBaseBusiVo) wrap.cmp.getFirstContextBean();
        SettleListValidateVo settleListValidateVo = (SettleListValidateVo) wrap.cmp.getRequestData();
        Map<String, String> keysBasicResultMap = setlValidBaseBusiVo.getKeysBasicResultMap();
        SomHiInvyBasInfo somHiInvyBasInfo = settleListValidateVo.getSomHiInvyBasInfo();
        SettleListHandlerVo settleListHandlerVo = new SettleListHandlerVo();

        // 检查 somHiInvyBasInfo 是否为空
        if (SettleValidateUtil.isEmpty(somHiInvyBasInfo)) {
            addErrorDetail(settleListHandlerVo, SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY,SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY,
                    null,
                    (String)SettleValidateConst.CHECK_TYPE_MAP.get(SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY),
                    SettleValidateConst.VALIDATE_STATE_FAIL, setlValidBaseBusiVo);
            return null;
        }

        // 获取年龄天校验结果
        String a16_stas = (String) keysBasicResultMap.get(MAIN_CHECK_FIELD_1);

        // 获取年龄岁校验结果
        String a14_stas = (String) keysBasicResultMap.get(MAIN_CHECK_FIELD_2);

        // 同时存在时
        if(SettleValidateConst.PASS.equals(a16_stas) && SettleValidateConst.PASS.equals(a14_stas) ){
            addErrorDetail(settleListHandlerVo, SettleValidateConst.VALIDATE_TYPE_2,SettleValidateConst.VALIDATE_ERROR_TYPE_1,
                    MAIN_CHECK_FIELD_2,
                    MAIN_CHECK_NAME_1 + "[" + somHiInvyBasInfo.getA16() + "]和" + MAIN_CHECK_NAME_2 + "[" + somHiInvyBasInfo.getA14()  + "] 不能同时存在",
                    SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
        }else if (SettleValidateConst.NULL.equals(a16_stas) && SettleValidateConst.NULL.equals(a14_stas)){
            addErrorDetail(settleListHandlerVo, SettleValidateConst.VALIDATE_TYPE_2,SettleValidateConst.VALIDATE_ERROR_TYPE_1,
                     MAIN_CHECK_FIELD_2,
                    MAIN_CHECK_NAME_2 + "[" + somHiInvyBasInfo.getA14() + "]为空或者为 0时" + MAIN_CHECK_NAME_1 + "[" + somHiInvyBasInfo.getA16() + "] 不能为空",
                    SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
        }

        return null;
    }

    private void addErrorDetail(SettleListHandlerVo handlerVo, String index,String errType,String errorFields, String errDscr, String chkStas, SetlValidBaseBusiVo baseBusiVo) {
        handlerVo.setIndex(index);
        handlerVo.setErrDscr(errDscr);
        handlerVo.setErrorFields(errorFields);
        handlerVo.setChkStas(chkStas);
        handlerVo.setErrType(errType);
        baseBusiVo.getErrorDetails().add(handlerVo);
    }
}
