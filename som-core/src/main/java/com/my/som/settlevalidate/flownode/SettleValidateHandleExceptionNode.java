package com.my.som.settlevalidate.flownode;

import com.alibaba.fastjson.JSON;
import com.my.som.mapper.engine.SettleListValidateMapper;
import com.my.som.service.dataHandle.SettleListValidateJobService;
import com.my.som.settlevalidate.constant.SettleValidateConst;
import com.my.som.settlevalidate.util.SettleValidateUtil;
import com.my.som.vo.dataHandle.ErrorLogVo;
import com.my.som.vo.dataHandle.SettleListHandlerVo;
import com.my.som.vo.dataHandle.SettleListValidateVo;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import com.yomahub.liteflow.core.NodeComponent;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Arrays;
import java.util.List;

@LiteflowComponent("settleValidateHandleExceptionNode")
public class SettleValidateHandleExceptionNode extends NodeComponent {
    private static final Logger logger = LoggerFactory.getLogger(SettleValidateHandleExceptionNode.class);

    @Autowired
    private SettleListValidateJobService settleListValidateJobService;


    @Override
    public void process() {
        Exception exception = this.getSlot().getException();
        String ChainId = this.getSlot().getChainId();
        this.getSlot().getRequestData();
        String errorStr = SettleValidateUtil.printErrorInfoFromException(exception);
        SettleListValidateVo settleListValidateVo = this.getSlot().getRequestData();
        List<String> scriptNameList = Arrays.asList(this.getSlot().getExecuteStepStr().split("==>"));
        settleListValidateJobService.logErrorMessages(settleListValidateVo,errorStr,scriptNameList);

    }


}
