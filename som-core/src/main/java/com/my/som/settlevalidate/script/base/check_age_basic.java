package com.my.som.settlevalidate.script.base;

import com.my.som.model.dataHandle.SomHiInvyBasInfo;
import com.my.som.settlevalidate.constant.SettleValidateConst;
import com.my.som.settlevalidate.util.SettleValidateUtil;
import com.my.som.settlevalidate.vo.SetlValidBaseBusiVo;
import com.my.som.vo.dataHandle.SettleListHandlerVo;
import com.my.som.vo.dataHandle.SettleListValidateVo;
import com.yomahub.liteflow.script.ScriptExecuteWrap;
import com.yomahub.liteflow.script.body.JaninoCommonScriptBody;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

public class check_age_basic implements JaninoCommonScriptBody {
    private static final Logger logger = LoggerFactory.getLogger(check_age_basic.class);

    private static final String MAIN_CHECK_FIELD = "a14";
    private static final String MAIN_CHECK_NAME = "年龄(岁)";
    private static final String MAIN_CHECK_REGION = "四川,湖北";
    /**
     * 年龄(age)基础质控
     * 1.判断年龄是否为空
     * 2.不为空判断数值是否合理
     * @param wrap
     * @return
     */
    @Override
    public Void body(ScriptExecuteWrap wrap) {
        SetlValidBaseBusiVo setlValidBaseBusiVo = (SetlValidBaseBusiVo) wrap.cmp.getFirstContextBean();
        SettleListValidateVo settleListValidateVo = (SettleListValidateVo) wrap.cmp.getRequestData();
        Map<String, String> keysBasicResultMap = setlValidBaseBusiVo.getKeysBasicResultMap();
        SomHiInvyBasInfo somHiInvyBasInfo = settleListValidateVo.getSomHiInvyBasInfo();
        SettleListHandlerVo settleListHandlerVo = new SettleListHandlerVo();

        // 检查 somHiInvyBasInfo 是否为空
        if (SettleValidateUtil.isEmpty(somHiInvyBasInfo)) {
            return null;
        }

        // 获取年龄
        Integer a14 = somHiInvyBasInfo.getA14();
        if (SettleValidateUtil.isEmpty(a14) || a14 == 0 ){
            keysBasicResultMap.put(MAIN_CHECK_FIELD, SettleValidateConst.NULL);
        } else {
            // 判断数值是否合理
            if(a14 < 1 || a14 > 150){
                addErrorDetail(settleListHandlerVo, SettleValidateConst.VALIDATE_TYPE_1,SettleValidateConst.VALIDATE_ERROR_TYPE_4,
                        MAIN_CHECK_FIELD,
                        MAIN_CHECK_NAME + "[" + a14 + "]超出[1-150]合理值范围",
                        SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
                keysBasicResultMap.put(MAIN_CHECK_FIELD, SettleValidateConst.OUTLIER);
            }else{
                keysBasicResultMap.put(MAIN_CHECK_FIELD, SettleValidateConst.PASS);
            }

        }

        return null;
    }

    private void addErrorDetail(SettleListHandlerVo handlerVo, String index,String errType,String errorFields, String errDscr, String chkStas, SetlValidBaseBusiVo baseBusiVo) {
        handlerVo.setIndex(index);
        handlerVo.setErrDscr(errDscr);
        handlerVo.setErrorFields(errorFields);
        handlerVo.setChkStas(chkStas);
        handlerVo.setErrType(errType);
        baseBusiVo.getErrorDetails().add(handlerVo);
    }
}

