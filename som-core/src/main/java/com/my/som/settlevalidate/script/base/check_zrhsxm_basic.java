package com.my.som.settlevalidate.script.base;

import com.my.som.model.dataHandle.SomHiInvyBasInfo;
import com.my.som.settlevalidate.constant.SettleValidateConst;
import com.my.som.settlevalidate.util.SettleValidateUtil;
import com.my.som.settlevalidate.vo.SetlValidBaseBusiVo;
import com.my.som.util.SettleListValidateUtil;
import com.my.som.vo.dataHandle.SettleListHandlerVo;
import com.my.som.vo.dataHandle.SettleListValidateVo;
import com.yomahub.liteflow.script.ScriptExecuteWrap;
import com.yomahub.liteflow.script.body.JaninoCommonScriptBody;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

public class check_zrhsxm_basic implements JaninoCommonScriptBody {
    private static final Logger logger = LoggerFactory.getLogger(check_zrhsxm_basic.class);
    private static final String MAIN_CHECK_FIELD = "b26n";
    private static final String MAIN_CHECK_NAME = "责任护士姓名";
    private static final String MAIN_CHECK_REGION = "四川";
    /**
     * 离院方式
     * 1、判断离院方式是否为空。
     * 2、判断离院方式是否为；(1)医嘱离
     * 院(代码1)、(2)医嘱转院(代
     * 码2)、(3)医嘱转社区卫生服务
     * 机构/乡镇卫生院(代码为3)、(4)
     * 非医嘱离院(代码4)、(5)死亡
     * (代码5)、(6)其他(代码9)以
     * 上六种中的任意一种。
     *
     * @param wrap
     * @return
     */
    @Override
    public Void body(ScriptExecuteWrap wrap) {
        SetlValidBaseBusiVo setlValidBaseBusiVo = (SetlValidBaseBusiVo) wrap.cmp.getFirstContextBean();
        SettleListValidateVo settleListValidateVo = (SettleListValidateVo) wrap.cmp.getRequestData();
        Map<String, String> keysBasicResultMap = setlValidBaseBusiVo.getKeysBasicResultMap();
        SomHiInvyBasInfo somHiInvyBasInfo = settleListValidateVo.getSomHiInvyBasInfo();
        SettleListHandlerVo settleListHandlerVo = new SettleListHandlerVo();
        if (SettleValidateUtil.isEmpty(somHiInvyBasInfo)) {
            addErrorDetail(settleListHandlerVo, SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY, SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY,
                    (String) SettleValidateConst.CHECK_TYPE_MAP.get(SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY), MAIN_CHECK_FIELD,
                    SettleValidateConst.VALIDATE_STATE_FAIL, setlValidBaseBusiVo);
            return null;
        }

        //获取责任护士姓名
        String b26n = somHiInvyBasInfo.getB26n();
        //获取责任护士代码
        String b26c = somHiInvyBasInfo.getB26c();

        boolean checkBool = SettleValidateUtil.checkNotNullOrNotBlank(b26n, keysBasicResultMap, MAIN_CHECK_FIELD);
        if (checkBool) {
            Map<String, Object> map = (Map<String, Object>) (setlValidBaseBusiVo.getSettleListParams().get(SettleListValidateUtil.KEY_NURSE_NAME));
            //获取护士名称
            Map<String, String> NurseNameMap = (Map<String, String>) map.get(somHiInvyBasInfo.getHospitalId() + SettleListValidateUtil.KEY_NURSE_NAME);
            if (SettleValidateUtil.isNotEmpty(b26c)) {
                String NurseName = (String) NurseNameMap.get(b26c);
                if(SettleValidateUtil.isNotEmpty(NurseName)){
                    if (b26n.equals(NurseName)) {
                        // 正常值，基础校验通过不写错误信息
                        keysBasicResultMap.put(MAIN_CHECK_FIELD, SettleValidateConst.PASS);
                    } else {
                        addErrorDetail(settleListHandlerVo, SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_ERROR_TYPE_2,
                                "页面中的" + MAIN_CHECK_NAME + "[" + b26n + "]与该编码[" + b26c + "]对应的医院护士名称[" + NurseName + "]不匹配",
                                MAIN_CHECK_FIELD,
                                SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
                        keysBasicResultMap.put(MAIN_CHECK_FIELD, SettleValidateConst.OUTLIER);
                    }
                }
            }
        } else {
            addErrorDetail(settleListHandlerVo, SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_TYPE_1,
                    MAIN_CHECK_NAME + "[" + b26n + "]为空",
                    MAIN_CHECK_FIELD,
                    SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
            keysBasicResultMap.put(MAIN_CHECK_FIELD, SettleValidateConst.NULL);
        }
        return null;
    }

    private void addErrorDetail(SettleListHandlerVo handlerVo, String index, String errType, String errDscr, String errorFields, String chkStas, SetlValidBaseBusiVo baseBusiVo) {
        handlerVo.setIndex(index);
        handlerVo.setErrDscr(errDscr);
        handlerVo.setErrorFields(errorFields);
        handlerVo.setChkStas(chkStas);
        handlerVo.setErrType(errType);
        baseBusiVo.getErrorDetails().add(handlerVo);
    }
}
