package com.my.som.settlevalidate.script.base;

import com.my.som.model.dataHandle.SomHiInvyBasInfo;
import com.my.som.settlevalidate.constant.SettleValidateConst;
import com.my.som.settlevalidate.util.SettleValidateUtil;
import com.my.som.settlevalidate.vo.SetlValidBaseBusiVo;
import com.my.som.vo.dataHandle.SettleListHandlerVo;
import com.my.som.vo.dataHandle.SettleListValidateVo;
import com.yomahub.liteflow.script.ScriptExecuteWrap;
import com.yomahub.liteflow.script.body.JaninoCommonScriptBody;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

/**
 * 事后判断 工作单位地址不为空
 */
public class check_emp_addr_after_basic implements JaninoCommonScriptBody {
    private static final Logger logger = LoggerFactory.getLogger(check_emp_addr_after_basic.class);

    private static final String MAIN_CHECK_FIELD = "a29";
    private static final String MAIN_CHECK_NAME = "工作单位地址";
    private static final String MAIN_CHECK_REGION = "四川";

    /**
     * 事后校验 基础质检
     * 判断就诊前的工作地址是否为空(emp_addr)
     *
     * @param wrap
     * @return
     */
    @Override
    public Void body(ScriptExecuteWrap wrap) {
        SetlValidBaseBusiVo setlValidBaseBusiVo = (SetlValidBaseBusiVo) wrap.cmp.getFirstContextBean();
        SettleListValidateVo settleListValidateVo = (SettleListValidateVo) wrap.cmp.getRequestData();
        Map<String, String> keysBasicResultMap = setlValidBaseBusiVo.getKeysBasicResultMap();
        SomHiInvyBasInfo somHiInvyBasInfo = settleListValidateVo.getSomHiInvyBasInfo();
        SettleListHandlerVo settleListHandlerVo = new SettleListHandlerVo();

        // 检查 somHiInvyBasInfo 是否为空
        if (SettleValidateUtil.isEmpty(somHiInvyBasInfo)) {
            SettleValidateUtil.addErrorDetail(settleListHandlerVo, SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY, SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY,
                    (String)SettleValidateConst.CHECK_TYPE_MAP.get(SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY),MAIN_CHECK_FIELD,
                    SettleValidateConst.VALIDATE_STATE_FAIL, setlValidBaseBusiVo);
            return null;
        }

        // 获取工作单位地址
        String a29 = somHiInvyBasInfo.getA29();
        //判断工作单位是否为空
        if (SettleValidateUtil.isEmpty(a29)) {
            keysBasicResultMap.put(MAIN_CHECK_FIELD, SettleValidateConst.NULL);
        } else {
            keysBasicResultMap.put(MAIN_CHECK_FIELD, SettleValidateConst.PASS);
        }
        return null;
    }
}
