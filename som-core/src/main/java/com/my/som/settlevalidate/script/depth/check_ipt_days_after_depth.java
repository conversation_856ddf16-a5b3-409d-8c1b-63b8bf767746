package com.my.som.settlevalidate.script.depth;

import com.my.som.model.dataHandle.SomHiInvyBasInfo;
import com.my.som.model.dataHandle.SomOprnOprtInfo;
import com.my.som.settlevalidate.constant.SettleValidateConst;
import com.my.som.settlevalidate.util.SettleValidateUtil;
import com.my.som.settlevalidate.vo.SetlValidBaseBusiVo;
import com.my.som.vo.dataHandle.SettleListHandlerVo;
import com.my.som.vo.dataHandle.SettleListValidateVo;
import com.yomahub.liteflow.script.ScriptExecuteWrap;
import com.yomahub.liteflow.script.body.JaninoCommonScriptBody;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 实际住院天数
 */
public class check_ipt_days_after_depth implements JaninoCommonScriptBody {
    private static final Logger logger = LoggerFactory.getLogger(check_ipt_days_after_depth.class);
    private static final String MAIN_CHECK_FIELD = "b20";
    private static final String MAIN_CHECK_NAME = "实际住院天数";
    private static final String MAIN_CHECK_REGION = "四川,湖北";
    private static final String MAIN_CHECK_FIELD_1 = "oprnOprtBegntime";
    private static final String MAIN_CHECK_NAME_1 = "手术开始时间";

    private static final String MAIN_CHECK_FIELD_2 = "oprnOprtEndtime";
    private static final String MAIN_CHECK_NAME_2 = "手术结束时间";


    /**
     * [实际住院天数-[b20]-基础校验]
     * 1、手术与住院天数不符,
     *
     * @param wrap
     * @return
     */
    @Override
    public Void body(ScriptExecuteWrap wrap) {

        SetlValidBaseBusiVo setlValidBaseBusiVo = (SetlValidBaseBusiVo) wrap.cmp.getFirstContextBean();
        SettleListValidateVo settleListValidateVo = (SettleListValidateVo) wrap.cmp.getRequestData();
        Map<String, String> keysBasicResultMap = setlValidBaseBusiVo.getKeysBasicResultMap();
        SomHiInvyBasInfo somHiInvyBasInfo = settleListValidateVo.getSomHiInvyBasInfo();
        SettleListHandlerVo settleListHandlerVo = new SettleListHandlerVo();

        List<SomOprnOprtInfo> busOperateDiagnosisList = settleListValidateVo.getBusOperateDiagnosisList();
        if (SettleValidateUtil.isEmpty(busOperateDiagnosisList)) {
            return null;
        }
        if (SettleValidateUtil.isEmpty(somHiInvyBasInfo)) {
            return null;
        }
        String b20 = somHiInvyBasInfo.getB20();
        if (SettleValidateUtil.isEmpty(b20)) {
            addErrorDetail(SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_ERROR_TYPE_2,
                    "存在手术时间但" + MAIN_CHECK_NAME + "[" + b20 + "]为空", MAIN_CHECK_FIELD,
                    SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
            return null;
        }
        String mimDate = "9999-12-31 00:00:00";
        String maxDate = "0000-01-01 00:00:00";
        boolean flag = false;

        for (int i = 0; i < busOperateDiagnosisList.size(); i++) {
            SomOprnOprtInfo somOprnOprtInfo = (SomOprnOprtInfo)busOperateDiagnosisList.get(i);
            String oprnOprtBegntime = somOprnOprtInfo.getOprnOprtBegntime();
            String oprnOprtEndtime = somOprnOprtInfo.getOprnOprtEndtime();
            //如果手术时间不为空
            if (SettleValidateConst.PASS.equals((String) keysBasicResultMap.get(i + MAIN_CHECK_FIELD_1))
                    && SettleValidateConst.PASS.equals((String)keysBasicResultMap.get(i + MAIN_CHECK_FIELD_2))) {
                keysBasicResultMap.put(i + MAIN_CHECK_FIELD_2, SettleValidateConst.PASS);
                if (judgeTimeOK(oprnOprtBegntime, mimDate)) {
                    mimDate = oprnOprtBegntime;
                }
                if (judgeTimeOK(maxDate, oprnOprtEndtime)) {
                    maxDate = oprnOprtEndtime;
                }
                flag = true;
            }
        }
        if (flag) {
            long daysDiff = getDateDifferenceInDays(mimDate, maxDate);
            if (daysDiff > Long.parseLong(b20)) {
                addErrorDetail(SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_ERROR_TYPE_2,
                        MAIN_CHECK_NAME + "[" + b20 + "]与所有手术时间开始结束的时间差" + daysDiff + "不符", MAIN_CHECK_FIELD,
                        SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
            }
        }
        return null;
    }

    public static long getDateDifferenceInDays(String date1Str, String date2Str) {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String formattedDifference = "";
        try {
            // 将时间字符串转换为 Date 对象
            Date date1 = dateFormat.parse(date1Str);
            Date date2 = dateFormat.parse(date2Str);

            // 计算时间差（毫秒）
            long differenceInMillis = date2.getTime() - date1.getTime();

            // 将毫秒差值转换为天数、小时数和分钟数
            long differenceInDays = TimeUnit.MILLISECONDS.toDays(differenceInMillis);
            long remainderMillisAfterDays = differenceInMillis % TimeUnit.DAYS.toMillis(1);
            long differenceInHours = TimeUnit.MILLISECONDS.toHours(remainderMillisAfterDays);

            if (differenceInHours > 0 || differenceInDays == 0) {
                differenceInDays = differenceInDays + 1;
            }

            // 格式化天数/小时数/分钟数字符串
            formattedDifference = String.format("%d", differenceInDays);

        } catch (ParseException e) {
            logger.error(" 实际住院天数时间转换失败: " + e.getMessage());
        }
        return Long.parseLong(formattedDifference);
    }

    /**
     * 判断时间2 大于时间1
     *
     * @param time1
     * @param time2
     * @return
     */
    private boolean judgeTimeOK(String time1, String time2) {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        boolean flag = false;
        try {
            Date admDate = dateFormat.parse(time1);
            Date dscgDate = dateFormat.parse(time2);
            // 入院时间大于出院时间
            if (dscgDate.after(admDate)) {
                flag = true;
            }
        } catch (ParseException e) {
            logger.error("实际住院天数 日期解析失败: " + e.getMessage());
        }
        return flag;
    }

    private void addErrorDetail(String index, String errType, String errDscr, String errorFields, String chkStas, SetlValidBaseBusiVo baseBusiVo) {
        SettleListHandlerVo handlerVo = new SettleListHandlerVo();
        handlerVo.setIndex(index);
        handlerVo.setErrorFields(errorFields);
        handlerVo.setErrDscr(errDscr);
        handlerVo.setChkStas(chkStas);
        handlerVo.setErrType(errType);
        baseBusiVo.getErrorDetails().add(handlerVo);
    }
}
