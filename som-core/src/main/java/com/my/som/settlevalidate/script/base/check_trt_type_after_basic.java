package com.my.som.settlevalidate.script.base;

import com.my.som.model.dataHandle.SomHiInvyBasInfo;
import com.my.som.settlevalidate.constant.SettleValidateConst;
import com.my.som.settlevalidate.util.SettleValidateUtil;
import com.my.som.settlevalidate.vo.SetlValidBaseBusiVo;
import com.my.som.vo.dataHandle.SettleListHandlerVo;
import com.my.som.vo.dataHandle.SettleListValidateVo;
import com.yomahub.liteflow.script.ScriptExecuteWrap;
import com.yomahub.liteflow.script.body.JaninoCommonScriptBody;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 判断 治疗类别不为空
 */
public class check_trt_type_after_basic implements JaninoCommonScriptBody {
    private static final Logger logger = LoggerFactory.getLogger(check_trt_type_after_basic.class);

    private static final String MAIN_CHECK_FIELD = "b39";
    private static final String MAIN_CHECK_NAME = "治疗类别";
    private static final String MAIN_CHECK_REGION = "四川,湖北";
    //国家码以及省码治疗类别
    private static final List<String> STANDARD_TREATMENT_CATEGORY = Arrays.asList("1","2.1,","2.2","3","10","21","22","30");


    /**
     * 事后 校验
     * 治疗类别(trt_type) 基础质控
     * 判断治疗类别是否为空
     *
     * @param wrap
     * @return
     */
    @Override
    public Void body(ScriptExecuteWrap wrap) {
        SetlValidBaseBusiVo setlValidBaseBusiVo = (SetlValidBaseBusiVo) wrap.cmp.getFirstContextBean();
        SettleListValidateVo settleListValidateVo = (SettleListValidateVo) wrap.cmp.getRequestData();
        Map<String, String> keysBasicResultMap = setlValidBaseBusiVo.getKeysBasicResultMap();
        SomHiInvyBasInfo somHiInvyBasInfo = settleListValidateVo.getSomHiInvyBasInfo();
        SettleListHandlerVo settleListHandlerVo = new SettleListHandlerVo();

        // 检查 somHiInvyBasInfo 是否为空
        if (SettleValidateUtil.isEmpty(somHiInvyBasInfo)) {
            SettleValidateUtil.addErrorDetail(settleListHandlerVo, SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY, SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY,
                    (String) SettleValidateConst.CHECK_TYPE_MAP.get(SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY), null,
                    SettleValidateConst.VALIDATE_STATE_FAIL, setlValidBaseBusiVo);
            return null;
        }
        // 获取医保类型
        String b39 = somHiInvyBasInfo.getB39();
        if (SettleValidateUtil.isEmpty(b39)) {
            SettleValidateUtil.addErrorDetail(settleListHandlerVo, SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_TYPE_1,
                    MAIN_CHECK_NAME + "[" + b39 + "]为空",
                    MAIN_CHECK_FIELD,
                    SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
            keysBasicResultMap.put(MAIN_CHECK_FIELD, SettleValidateConst.NULL);
        } else {
            //判断是否符合国家或省码的标准码
            if(STANDARD_TREATMENT_CATEGORY.contains(b39)){
                keysBasicResultMap.put(MAIN_CHECK_FIELD, SettleValidateConst.PASS);
            }else {
                SettleValidateUtil.addErrorDetail(settleListHandlerVo, SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_TYPE_1,
                        MAIN_CHECK_NAME + "[" + b39 + "]不符合国家码或省码的治疗类别",
                        MAIN_CHECK_FIELD,
                        SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
                keysBasicResultMap.put(MAIN_CHECK_FIELD, SettleValidateConst.OUTLIER);
            }
        }
        return null;
    }
}

