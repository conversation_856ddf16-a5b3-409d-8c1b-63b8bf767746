package com.my.som.settlevalidate.service;

import com.my.som.common.constant.DrgConst;
import com.my.som.common.util.BatchUtil;
import com.my.som.common.util.ValidateUtil;
import com.my.som.mapper.engine.SettleListValidateMapper;
import com.my.som.model.dataHandle.SomHiInvyBasInfo;
import com.my.som.service.dataHandle.SettleListValidateJobService;
import com.my.som.settlevalidate.constant.SettleValidateConst;
import com.my.som.settlevalidate.util.SettleValidateUtil;
import com.my.som.settlevalidate.vo.SetlValidBaseBusiVo;
import com.my.som.vo.dataHandle.SettleListHandlerVo;
import com.my.som.vo.dataHandle.SettleListValidateVo;
import com.my.som.util.SettleListValidateUtil;
import com.yomahub.liteflow.core.FlowExecutor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class SettleListValidateLiteFlowServiceImpl implements SettleListValidateLiteFlowService {

    private Logger logger = LoggerFactory.getLogger(BatchUtil.class);
    @Resource
    private FlowExecutor flowExecutor;

    /**
     * 校验状态 success: 成功 fail: 失败
     */
    private final static String VALIDATE_STATE_SUCCESS = "1";
    private final static String VALIDATE_STATE_FAIL = "0";

    @Resource
    private SettleListValidateJobService settleListValidateJobService;

    @Autowired
    private SettleListValidateMapper settleListValidateMapper;

    /**
     * 1、执行规则配置中的 GlobalExecSettleValidateChain[THEN(GlobalExecSettleValidateBasicChain,GlobalExecSettleValidateDepthChain)] 调用链
     * 2、执行规则配置中的 GlobalExecSettleValidateBasicChain[THEN(check_nwb_bir_wt_basic)] 调用链
     * 3、执行规则配置中的 GlobalExecSettleValidateDepthChain[THEN(check_nwb_bir_wt_depth)] 调用链
     */
    // todo 更新为结算清单的质控初始化数据
    @Override
    public SettleListHandlerVo executeSettlVaildate(SettleListValidateVo settleListValidateVo, Map<String, Object> settleListParams) {
        //构建清单质控上下文
        SetlValidBaseBusiVo setlValidBaseBusiVo = new SetlValidBaseBusiVo();
        setlValidBaseBusiVo.setSettleListParams(settleListParams);
        setlValidBaseBusiVo.setErrorDetails(new ArrayList<>());
        setlValidBaseBusiVo.setKeysBasicResultMap(new HashMap<>());

        //结算清单质控结果
        SettleListHandlerVo validateResult = new SettleListHandlerVo();
        validateResult.setSettleListId(settleListValidateVo.getSomHiInvyBasInfo().getId());
        validateResult.setChkStas(VALIDATE_STATE_SUCCESS);
        validateResult.setErrorDetails(new ArrayList<>());

        //转换settleListValidateVo中的-为空
        transformNullValue(settleListValidateVo.getSomHiInvyBasInfo());

        //设置转换数据(医生编码、护士编码、输血编码)
        SettleListValidateUtil.setConvertData(settleListParams, settleListValidateVo.getSomHiInvyBasInfo().getHospitalId());
        if(SettleValidateUtil.isEmpty(settleListValidateVo.getSomHiInvyBasInfo().getK00())){
            settleListValidateVo.getSomHiInvyBasInfo().setK00("PRE");
        }
        //删除原有的日志质检
        settleListValidateMapper.deleteErrorBySettleId(settleListValidateVo.getSomHiInvyBasInfo().getK00());
        //调用规则链执行质控
        flowExecutor.execute2Resp("GlobalExecSettleValidateChain", settleListValidateVo, setlValidBaseBusiVo);

        List<SettleListHandlerVo> settleListHandlerVoList = setlValidBaseBusiVo.getErrorDetails();
        // 设置单次汇总
        StringBuilder summaryFields = new StringBuilder();
        StringBuilder summaryDesc = new StringBuilder();
        for (SettleListHandlerVo errorDetail : settleListHandlerVoList) {
            if (ValidateUtil.isNotEmpty(errorDetail.getErrorFields())) {
                summaryFields.append(errorDetail.getErrorFields()+";");
            }
            if (ValidateUtil.isNotEmpty(errorDetail.getErrDscr())) {
                summaryDesc.append(errorDetail.getErrDscr()+";");
            }
            errorDetail.setSettleListId(settleListValidateVo.getSomHiInvyBasInfo().getId());
        }
        validateResult.setErrorFields(summaryFields.toString());
        validateResult.setErrDscr(summaryDesc.toString());
        validateResult.setErrorDetails(settleListHandlerVoList);
        if (ValidateUtil.isNotEmpty(settleListHandlerVoList)) {
            //设置校验不通过标志
            validateResult.setChkStas(VALIDATE_STATE_FAIL);
        }
        return validateResult;
    }

    /**
     * 结算清单基础数据-转空
     *
     * @param somHiInvyBasInfo
     */
    public void transformNullValue(SomHiInvyBasInfo somHiInvyBasInfo) {
        try {
            Field[] validateFieldArray = somHiInvyBasInfo.getClass().getDeclaredFields();
            for (Field field : validateFieldArray) {
                field.setAccessible(true);
                if (SettleValidateConst.NULL_.equals(field.get(somHiInvyBasInfo))) {
                    field.set(somHiInvyBasInfo, null);
                }
            }
        } catch (SecurityException e) {
            logger.error("反射-到空失败：" + e);
            e.printStackTrace();
        } catch (Exception e) {
            logger.error("反射-到空失败：" + e);
            e.printStackTrace();
        }
    }
}
