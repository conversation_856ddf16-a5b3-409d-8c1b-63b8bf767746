package com.my.som.settlevalidate.script.base;

import com.my.som.model.dataHandle.SomHiInvyBasInfo;
import com.my.som.settlevalidate.constant.SettleValidateConst;
import com.my.som.settlevalidate.util.SettleValidateUtil;
import com.my.som.settlevalidate.vo.SetlValidBaseBusiVo;
import com.my.som.vo.dataHandle.SettleListHandlerVo;
import com.my.som.vo.dataHandle.SettleListValidateVo;
import com.yomahub.liteflow.script.ScriptExecuteWrap;
import com.yomahub.liteflow.script.body.JaninoCommonScriptBody;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

public class check_nwb_age_basic implements JaninoCommonScriptBody {
    private static final Logger logger = LoggerFactory.getLogger(check_nwb_age_basic.class);
    private static final String MAIN_CHECK_FIELD = "a16";
    private static final String MAIN_CHECK_NAME = "新生儿天龄";
    private static final String MAIN_CHECK_REGION = "四川,湖北";
    /**
     * [新生儿天龄-[a16]-基础校验]
     * 1、判断是否为空，非空时判断是否为数字，不为数字时则提示异常
     *
     * @param wrap
     * @return
     */
    @Override
    public Void body(ScriptExecuteWrap wrap) {
        String main_check_field = "a16";
        String main_check_name = "新生儿天龄";
        SetlValidBaseBusiVo setlValidBaseBusiVo = (SetlValidBaseBusiVo) wrap.cmp.getFirstContextBean();
        SettleListValidateVo settleListValidateVo = (SettleListValidateVo) wrap.cmp.getRequestData();
        Map<String, String> keysBasicResultMap = setlValidBaseBusiVo.getKeysBasicResultMap();
        SomHiInvyBasInfo somHiInvyBasInfo = settleListValidateVo.getSomHiInvyBasInfo();
        SettleListHandlerVo settleListHandlerVo = new SettleListHandlerVo();
        if (SettleValidateUtil.isEmpty(somHiInvyBasInfo)) {
            settleListHandlerVo.setIndex(SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY);
            settleListHandlerVo.setErrDscr((String) SettleValidateConst.CHECK_TYPE_MAP.get(SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY));
            settleListHandlerVo.setChkStas(SettleValidateConst.VALIDATE_STATE_FAIL);
            settleListHandlerVo.setErrType(SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY);
            setlValidBaseBusiVo.getErrorDetails().add(settleListHandlerVo);
            return null;
        }
        Integer a16 = somHiInvyBasInfo.getA16();
        if (!SettleValidateUtil.isEmpty(a16)) {
            if (!(a16 >= 0 && a16 <= 365)) {
                //不在合理值范围，提示
                settleListHandlerVo.setIndex(SettleValidateConst.VALIDATE_TYPE_1);
                settleListHandlerVo.setSettleListId(somHiInvyBasInfo.getId());
                settleListHandlerVo.setErrorFields(main_check_field);
                settleListHandlerVo.setErrDscr(main_check_name + "[" + a16 + "]超出合理值范围");
                settleListHandlerVo.setChkStas(SettleValidateConst.VALIDATE_STATE_SUCCESS);
                settleListHandlerVo.setErrType(SettleValidateConst.VALIDATE_ERROR_TYPE_2);
                setlValidBaseBusiVo.getErrorDetails().add(settleListHandlerVo);
                //放入字段基础校验
                keysBasicResultMap.put(main_check_field, SettleValidateConst.OUTLIER);
            }else {
                //正常值，基础校验通过
                keysBasicResultMap.put(main_check_field, SettleValidateConst.PASS);
            }
        } else {
            keysBasicResultMap.put(main_check_field, SettleValidateConst.NULL);
        }
        return null;
    }
}
