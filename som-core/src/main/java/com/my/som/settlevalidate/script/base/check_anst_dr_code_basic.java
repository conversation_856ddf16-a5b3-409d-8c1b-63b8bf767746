package com.my.som.settlevalidate.script.base;

import com.my.som.model.dataHandle.SomHiInvyBasInfo;
import com.my.som.model.dataHandle.SomOprnOprtInfo;
import com.my.som.settlevalidate.constant.SettleValidateConst;
import com.my.som.settlevalidate.util.SettleValidateUtil;
import com.my.som.settlevalidate.vo.SetlValidBaseBusiVo;
import com.my.som.util.SettleListValidateUtil;
import com.my.som.vo.dataHandle.SettleListHandlerVo;
import com.my.som.vo.dataHandle.SettleListValidateVo;
import com.yomahub.liteflow.script.ScriptExecuteWrap;
import com.yomahub.liteflow.script.body.JaninoCommonScriptBody;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Map;

public class check_anst_dr_code_basic implements JaninoCommonScriptBody {
    private static final Logger logger = LoggerFactory.getLogger(check_anst_dr_code_basic.class);

    private static final String MAIN_CHECK_FIELD = "oprn_oprt_anst_dr_code";
    private static final String MAIN_CHECK_NAME = "麻醉医师代码";
    private static final String MAIN_CHECK_REGION = "四川";
    /**
     * 麻醉医师代码(oper_dr_code)质控
     * 1.存在手术或操作时且麻醉方式不为空，麻醉医师代码不能为空。
     * 2.判断麻醉医师代码是否与医保业务编码标准动态维护平合上的麻醉医师姓名相匹配。
     * @param wrap
     * @return
     */
    @Override
    public Void body(ScriptExecuteWrap wrap) {

        SetlValidBaseBusiVo setlValidBaseBusiVo = (SetlValidBaseBusiVo) wrap.cmp.getFirstContextBean();
        SettleListValidateVo settleListValidateVo = (SettleListValidateVo) wrap.cmp.getRequestData();
        List<SomOprnOprtInfo> busOperateDiagnosisList = settleListValidateVo.getBusOperateDiagnosisList();
        SomHiInvyBasInfo somHiInvyBasInfo = settleListValidateVo.getSomHiInvyBasInfo();

        Map<String, Object> doctorMap = (Map<String, Object>) setlValidBaseBusiVo.getSettleListParams().get(SettleListValidateUtil.KEY_DOCTOR_CODE);

        if (!SettleValidateUtil.isEmpty(busOperateDiagnosisList)) {
            for (int i = 0; i < busOperateDiagnosisList.size(); i++) {
                SomOprnOprtInfo somOprnOprtInfo = (SomOprnOprtInfo)busOperateDiagnosisList.get(i);
                // 麻醉方式不为空时
                if(!SettleValidateUtil.isEmpty(somOprnOprtInfo.getC43())){
                    String anstCode = somOprnOprtInfo.getOprn_oprt_anst_dr_code();
                    if (SettleValidateUtil.isEmpty(anstCode)) {
                        addErrorDetail(SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_ERROR_TYPE_1,
                                MAIN_CHECK_FIELD,
                                "第" + (i + 1) + "个手术操作：编码为["+somOprnOprtInfo.getC35c()+"]的" +MAIN_CHECK_NAME + "[" + anstCode + "]为空",
                                SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
                    }else{
                        // 不为空 判断是否符合医保医师代码标准

                        // 获取机构编码
                        String hospitalId = somHiInvyBasInfo.getHospitalId();
                        // 获取医保医师映射表
                        Map<String, Object> map = (Map<String, Object>)doctorMap.get(hospitalId);
                        if(SettleValidateUtil.isEmpty(map)){
                            addErrorDetail(SettleValidateConst.VALIDATE_TYPE_2, SettleValidateConst.VALIDATE_ERROR_TYPE_1,
                                    MAIN_CHECK_FIELD,
                                    "未获取到医保医师映射编码信息" ,
                                    SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
                        }else{
                            // 存在医保医师编码映射信息质控通过
                            if(!map.containsKey(anstCode)){
                                addErrorDetail(SettleValidateConst.VALIDATE_TYPE_2, SettleValidateConst.VALIDATE_ERROR_TYPE_1,
                                        MAIN_CHECK_FIELD,
                                        "第" + (i + 1) + "个手术：编码为["+somOprnOprtInfo.getC35c()+"]的" +MAIN_CHECK_NAME + "[" + anstCode + "] 未在医院医师表中",
                                        SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
                            }
                        }
                    }
                }

            }
        }
        return null;
    }

    private void addErrorDetail(String index,String errType,String errorFields, String errDscr, String chkStas, SetlValidBaseBusiVo baseBusiVo) {
        SettleListHandlerVo handlerVo = new SettleListHandlerVo();
        handlerVo.setIndex(index);
        handlerVo.setErrorFields(errorFields);
        handlerVo.setErrDscr(errDscr);
        handlerVo.setChkStas(chkStas);
        handlerVo.setErrType(errType);
        baseBusiVo.getErrorDetails().add(handlerVo);
    }
}
