package com.my.som.settlevalidate.script.base;

import com.my.som.model.dataHandle.SomOprnOprtInfo;
import com.my.som.settlevalidate.constant.SettleValidateConst;
import com.my.som.settlevalidate.util.SettleValidateUtil;
import com.my.som.settlevalidate.vo.SetlValidBaseBusiVo;
import com.my.som.vo.dataHandle.SettleListHandlerVo;
import com.my.som.vo.dataHandle.SettleListValidateVo;
import com.yomahub.liteflow.script.ScriptExecuteWrap;
import com.yomahub.liteflow.script.body.JaninoCommonScriptBody;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.List;
import java.util.Map;

/**
 * 判断 手术时间不为空且规范
 */
public class check_oprn_oprt_time_after_basic implements JaninoCommonScriptBody {
    private static final Logger logger = LoggerFactory.getLogger(check_oprn_oprt_time_after_basic.class);

    private static final String MAIN_CHECK_FIELD = "oprnOprtTime";
    private static final String MAIN_CHECK_NAME = "手术时间";
    private static final String MAIN_CHECK_REGION = "四川,湖北";
    private static final String MAIN_CHECK_FIELD_1 = "oprnOprtBegntime";
    private static final String MAIN_CHECK_NAME_1 = "手术开始时间";

    private static final String MAIN_CHECK_FIELD_2 = "oprnOprtEndtime";
    private static final String MAIN_CHECK_NAME_2 = "手术结束时间";

    /**
     * 事后校验
     * 结算结束时间(setl_date)基础质控
     * 判断结算时间是否为空
     *
     * @param wrap
     * @return
     */
    @Override
    public Void body(ScriptExecuteWrap wrap) {
        SetlValidBaseBusiVo setlValidBaseBusiVo = (SetlValidBaseBusiVo) wrap.cmp.getFirstContextBean();
        SettleListValidateVo settleListValidateVo = (SettleListValidateVo) wrap.cmp.getRequestData();
        Map<String, String> keysBasicResultMap = setlValidBaseBusiVo.getKeysBasicResultMap();
        List<SomOprnOprtInfo> busOperateDiagnosisList = settleListValidateVo.getBusOperateDiagnosisList();

        // 检查 somHiInvyBasInfo 是否为空
        if (SettleValidateUtil.isEmpty(busOperateDiagnosisList)) {
            return null;
        }

        // 获取结算 结束时间
        for (int i = 0; i < busOperateDiagnosisList.size(); i++) {
            SomOprnOprtInfo somOprnOprtInfo = (SomOprnOprtInfo)busOperateDiagnosisList.get(i);
            String oprnOprtBegntime = somOprnOprtInfo.getOprnOprtBegntime();
            String oprnOprtEndtime = somOprnOprtInfo.getOprnOprtEndtime();
            //手术开始时间
            if (SettleValidateUtil.isEmpty(oprnOprtBegntime)) {
                //手术开始时间为空
                addErrorDetail(SettleValidateConst.VALIDATE_TYPE_2, SettleValidateConst.VALIDATE_TYPE_1,
                        "第" + (i + 1) + "个手术操作：编码为["+somOprnOprtInfo.getC35c()+"]的" + MAIN_CHECK_NAME_1 + "[" + oprnOprtBegntime + "]为空",
                        MAIN_CHECK_FIELD_1,
                        SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
                keysBasicResultMap.put(i + i+MAIN_CHECK_FIELD_1, SettleValidateConst.NULL);
            } else {
                if (!isValidDateTime(oprnOprtBegntime)) {
                    addErrorDetail(SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_ERROR_TYPE_2,
                            "第" + (i + 1) + "个手术操作：编码为["+somOprnOprtInfo.getC35c()+"]的" + MAIN_CHECK_NAME_1 + "[" + oprnOprtBegntime + "]时间格式错误",
                            MAIN_CHECK_FIELD_1,
                            SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
                    keysBasicResultMap.put(i+MAIN_CHECK_FIELD_1, SettleValidateConst.OUTLIER);
                } else {
                    keysBasicResultMap.put(i+MAIN_CHECK_FIELD_1, SettleValidateConst.PASS);
                }
            }

            // 手术结束时间
            if (SettleValidateUtil.isEmpty(oprnOprtEndtime)) {
                addErrorDetail(SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_TYPE_1,
                        "第" + (i + 1) + "个手术操作：编码为["+somOprnOprtInfo.getC35c()+"]的" + MAIN_CHECK_NAME_2 + "[" + oprnOprtEndtime + "]为空",
                        MAIN_CHECK_FIELD_2,
                        SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
                keysBasicResultMap.put(i+MAIN_CHECK_FIELD_2, SettleValidateConst.NULL);
            } else {
                if (!isValidDateTime(oprnOprtEndtime)) {
                    addErrorDetail(SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_ERROR_TYPE_2,
                            "第" + (i + 1) + "个手术操作：编码为["+somOprnOprtInfo.getC35c()+"]的" + MAIN_CHECK_NAME_2 + "[" + oprnOprtEndtime + "]时间格式错误",
                            MAIN_CHECK_FIELD_2,
                            SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
                    keysBasicResultMap.put(i+MAIN_CHECK_FIELD_2, SettleValidateConst.OUTLIER);
                } else {
                    keysBasicResultMap.put(i+MAIN_CHECK_FIELD_2, SettleValidateConst.PASS);
                }
            }
        }
        return null;
    }

    public static boolean isValidDateTime(String dateTimeStr) {
        String format = "yyyy-MM-dd HH:mm:ss";
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(format);
        try {
            LocalDateTime.parse(dateTimeStr, formatter);
            return true;
        } catch (DateTimeParseException e) {
            return false;
        }
    }

    private void addErrorDetail(String index, String errType, String errDscr, String errorFields, String chkStas, SetlValidBaseBusiVo baseBusiVo) {
        SettleListHandlerVo handlerVo = new SettleListHandlerVo();
        handlerVo.setIndex(index);
        handlerVo.setErrDscr(errDscr);
        handlerVo.setErrorFields(errorFields);
        handlerVo.setChkStas(chkStas);
        handlerVo.setErrType(errType);
        baseBusiVo.getErrorDetails().add(handlerVo);
    }
}
