package com.my.som.settlevalidate.vo;

import com.my.som.vo.dataHandle.SettleListHandlerVo;
import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Map;

@Getter
@Setter
public class SetlValidBaseBusiVo {

    /**
     * 结算清单质控参数
     */
    private Map<String, Object> settleListParams;

    /**
     * 错误详情
     */
    private List<SettleListHandlerVo> errorDetails;

    /**
     * 各字段基础性校验结果
     */
    private Map<String, String> keysBasicResultMap;

}
