package com.my.som.settlevalidate.script.depth;

import com.my.som.common.vo.NwbCodeVo;
import com.my.som.dto.medicalQuality.BusDiseaseDiagnosisTrim;
import com.my.som.model.dataHandle.SomHiInvyBasInfo;
import com.my.som.settlevalidate.constant.SettleValidateConst;
import com.my.som.settlevalidate.util.SettleValidateUtil;
import com.my.som.settlevalidate.vo.SetlValidBaseBusiVo;
import com.my.som.util.SettleListValidateUtil;
import com.my.som.vo.dataHandle.SettleListHandlerVo;
import com.my.som.vo.dataHandle.SettleListValidateVo;
import com.yomahub.liteflow.script.ScriptExecuteWrap;
import com.yomahub.liteflow.script.body.JaninoCommonScriptBody;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 事后 出院西医诊断 新生儿年龄
 */
public class check_dscg_wm_other_dise_new_age_depth implements JaninoCommonScriptBody {
    private static final Logger logger = LoggerFactory.getLogger(check_dscg_wm_other_dise_new_age_depth.class);

    private static final String MAIN_CHECK_FIELD = "c06c1";
    private static final String MAIN_CHECK_CODE = "a16";
    private static final String MAIN_CHECK_NAME = "出院西医诊断编码";

    private static final String MAIN_CHECK_REGION = "四川,湖北";
    private static final String NWE_AGE_TYPE = "1";



    /**
     * 出院西医其他诊断(dscg_wm_other_dise)基础质控
     * 判断入院时间是否为空
     *
     * @param wrap
     * @return
     */
    @Override
    public Void body(ScriptExecuteWrap wrap) {
        SetlValidBaseBusiVo setlValidBaseBusiVo = (SetlValidBaseBusiVo) wrap.cmp.getFirstContextBean();
        SettleListValidateVo settleListValidateVo = (SettleListValidateVo) wrap.cmp.getRequestData();
        Map<String, String> keysBasicResultMap = setlValidBaseBusiVo.getKeysBasicResultMap();
        SettleListHandlerVo settleListHandlerVo = new SettleListHandlerVo();
        List<BusDiseaseDiagnosisTrim> busDiseaseDiagnosisTrimsList = settleListValidateVo.getBusDiseaseDiagnosisTrimsList();
        SomHiInvyBasInfo somHiInvyBasInfo = settleListValidateVo.getSomHiInvyBasInfo();
        // 检查 busDiseaseDiagnosisTrimsList 是否为空
        if (SettleValidateUtil.isEmpty(busDiseaseDiagnosisTrimsList)) {
            return null;
        }
        // 检查 somHiInvyBasInfo 是否为空
        if (SettleValidateUtil.isEmpty(somHiInvyBasInfo)) {
            return null;
        }
        //先获取 新生儿 诊断的目录
      Map<String, Object>  nwbAgeMap = (Map<String, Object>) setlValidBaseBusiVo.getSettleListParams().get(SettleListValidateUtil.KEY_NWB_AGE_CODE);


        boolean flag = false;
        String code = null;
        for (BusDiseaseDiagnosisTrim busDiseaseDiagnosisTrim : busDiseaseDiagnosisTrimsList) {
            //说明此时诊断不为空
            //判断 新生婴儿/早产婴儿，其他的与患者年龄不符
            if (nwbAgeMap.containsKey(busDiseaseDiagnosisTrim.getC06c1())) {
                //说明是新生儿，此时判断新生儿年龄
                // 获取年龄天校验结果
               flag = true;
                code = busDiseaseDiagnosisTrim.getC06c1();
            }
        }
        String a16_stas = (String) keysBasicResultMap.get(MAIN_CHECK_CODE);
        Integer a16 =  somHiInvyBasInfo.getA16();
       ;
        if ( checkA16IsStandard(flag,a16_stas,a16)) {
            addErrorDetail(SettleValidateConst.VALIDATE_TYPE_2, SettleValidateConst.VALIDATE_TYPE_2,
                    MAIN_CHECK_NAME + " : [" + code +
                            "]为新生诊断,新生儿年龄["+a16 +" 天]与规范[1天——28天]不符", MAIN_CHECK_FIELD,
                    SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
        }
        return null;
    }

    private boolean checkA16IsStandard(boolean flag, String a16Stas, Integer a16) {
        if(!flag){
            return false;
        }
        if (!SettleValidateConst.PASS.equals(a16Stas)) {
            return true;
        }
        if ( 1> a16 && a16 > 28) {
            return true;
        }
        return false;
    }

    private void addErrorDetail(String index, String errType, String errDscr, String errorFields, String chkStas, SetlValidBaseBusiVo baseBusiVo) {
        SettleListHandlerVo handlerVo = new SettleListHandlerVo();
        handlerVo.setIndex(index);
        handlerVo.setErrorFields(errorFields);
        handlerVo.setErrDscr(errDscr);
        handlerVo.setChkStas(chkStas);
        handlerVo.setErrType(errType);
        baseBusiVo.getErrorDetails().add(handlerVo);
    }
}
