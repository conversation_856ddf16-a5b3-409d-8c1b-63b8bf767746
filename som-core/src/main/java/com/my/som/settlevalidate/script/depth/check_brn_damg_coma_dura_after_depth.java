package com.my.som.settlevalidate.script.depth;

import com.my.som.common.constant.DrgConst;
import com.my.som.dto.medicalQuality.BusDiseaseDiagnosisTrim;
import com.my.som.model.dataHandle.SomHiInvyBasInfo;
import com.my.som.settlevalidate.constant.SettleValidateConst;
import com.my.som.settlevalidate.util.SettleValidateUtil;
import com.my.som.settlevalidate.vo.SetlValidBaseBusiVo;
import com.my.som.util.SettleListValidateUtil;
import com.my.som.vo.dataHandle.SettleListDisSectionVo;
import com.my.som.vo.dataHandle.SettleListHandlerVo;
import com.my.som.vo.dataHandle.SettleListValidateVo;
import com.yomahub.liteflow.script.ScriptExecuteWrap;
import com.yomahub.liteflow.script.body.JaninoCommonScriptBody;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
/**
 * 颅脑损伤昏迷时间校验
 */
public class check_brn_damg_coma_dura_after_depth implements JaninoCommonScriptBody {
    private static final Logger logger = LoggerFactory.getLogger(check_brn_damg_coma_dura_after_depth.class);
    private static final String MAIN_CHECK_FIELD = "C28";
    private static final String MAIN_CHECK_NAME = "入院前昏迷时间（天）";
    private static final String MAIN_CHECK_REGION = "四川";
    /**
     * 颅脑损伤昏迷时间校验
     * 1. 颅脑损伤患者入院前昏迷时长
     * 2. 颅脑损伤患者入院后昏迷时长
     */
    @Override
    public Void body(ScriptExecuteWrap wrap) {
        SetlValidBaseBusiVo setlValidBaseBusiVo = (SetlValidBaseBusiVo) wrap.cmp.getFirstContextBean();
        SettleListValidateVo settleListValidateVo = (SettleListValidateVo) wrap.cmp.getRequestData();

        SomHiInvyBasInfo somHiInvyBasInfo = settleListValidateVo.getSomHiInvyBasInfo();
        SettleListHandlerVo settleListHandlerVo = new SettleListHandlerVo();
        List<BusDiseaseDiagnosisTrim> busDiseaseDiagnosisTrimsList = settleListValidateVo.getBusDiseaseDiagnosisTrimsList();
        // 检查 somHiInvyBasInfo 是否为空

        // 检查诊断信息是否为空
        if (SettleValidateUtil.isEmpty(busDiseaseDiagnosisTrimsList)) {
            return null;
        }

        // 颅脑损伤入院前和入院后的字段
        Integer[] admissionBefore = {somHiInvyBasInfo.getC28(), somHiInvyBasInfo.getC29(), somHiInvyBasInfo.getC30()};
        Integer[] admissionAfter = {somHiInvyBasInfo.getC31(), somHiInvyBasInfo.getC32(), somHiInvyBasInfo.getC33()};

        // 检查颅脑损伤入院时间是否合法
        validateAdmissionTime(admissionBefore, setlValidBaseBusiVo, "颅脑损伤入院前昏迷时间不合理");
        validateAdmissionTime(admissionAfter, setlValidBaseBusiVo, "颅脑损伤入院后昏迷时间不合理");

        checkReasonable(admissionBefore, setlValidBaseBusiVo, busDiseaseDiagnosisTrimsList);
        return null;
    }

    private void checkReasonable(Integer[] admissionBefore, SetlValidBaseBusiVo setlValidBaseBusiVo, List<BusDiseaseDiagnosisTrim> busDiseaseDiagnosisTrimsList) {
        boolean flag = false;
        for(Integer num : admissionBefore){
            if (num != null && num != 0) {
                flag =true;
            }
        }
        for(Integer num : admissionBefore){
            if (num != null && num != 0) {
                flag =true;
            }
        }

        if(flag){
            boolean has = false;
            Map<String, Object> dictMap = (Map<String, Object>) setlValidBaseBusiVo.getSettleListParams().get(SettleListValidateUtil.KEY_DIS_SECTION_LIST);
            List<String> codeList = new ArrayList<>();
            List<SettleListDisSectionVo> settleListDisSectionVos = (List<SettleListDisSectionVo>) dictMap.get(DrgConst.COMA_CODE_FLAG);
            for (SettleListDisSectionVo settleListDisSectionVo : settleListDisSectionVos) {
                codeList.add(settleListDisSectionVo.getDiagSec());
            }
            for(String code: codeList){
                for(BusDiseaseDiagnosisTrim busDiseaseDiagnosisTrim : busDiseaseDiagnosisTrimsList){
                    if(code.equals(busDiseaseDiagnosisTrim.getC06c1())){
                        has= true;
                    }
                }
            }
            if(!has){
                addErrorDetail(SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_ERROR_TYPE_4,
                        "c06c1",
                        "当昏迷时间的“天”“时”“分”其中之一值≠0时，主要诊断或者其他诊断需包含与昏迷诊断相关的编码",
                        SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
            }
        }
    }

    private void addErrorDetail( String index,String errType,String errorFields, String errDscr, String chkStas, SetlValidBaseBusiVo baseBusiVo) {
        SettleListHandlerVo handlerVo =new SettleListHandlerVo();
        handlerVo.setIndex(index);
        handlerVo.setErrDscr(errDscr);
        handlerVo.setErrorFields(errorFields);
        handlerVo.setChkStas(chkStas);
        handlerVo.setErrType(errType);
        baseBusiVo.getErrorDetails().add(handlerVo);
    }

    private void validateAdmissionTime(Integer[] admissionTimes,SetlValidBaseBusiVo baseBusiVo, String errorMessage) {
        Integer c28 = admissionTimes[0];
        Integer c29 = admissionTimes[1];
        Integer c30 = admissionTimes[2];

        if (!SettleValidateUtil.isEmpty(c28) || !SettleValidateUtil.isEmpty(c29) || !SettleValidateUtil.isEmpty(c30)) {
            if (c28 == null || c28 < 0 || c29 == null || c29 < 0 || c29 > 24 || c30 == null || c30 < 0 || c30 > 60) {
                addErrorDetail( SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_ERROR_TYPE_4,
                        "c28", errorMessage, SettleValidateConst.VALIDATE_STATE_SUCCESS, baseBusiVo);
            }
        }
    }
}
