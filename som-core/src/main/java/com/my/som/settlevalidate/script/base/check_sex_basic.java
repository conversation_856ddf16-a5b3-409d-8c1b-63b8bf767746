package com.my.som.settlevalidate.script.base;

import com.my.som.model.dataHandle.SomHiInvyBasInfo;
import com.my.som.settlevalidate.constant.SettleValidateConst;
import com.my.som.settlevalidate.util.SettleValidateUtil;
import com.my.som.settlevalidate.vo.SetlValidBaseBusiVo;
import com.my.som.vo.dataHandle.SettleListHandlerVo;
import com.my.som.vo.dataHandle.SettleListValidateVo;
import com.yomahub.liteflow.script.ScriptExecuteWrap;
import com.yomahub.liteflow.script.body.JaninoCommonScriptBody;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;
import java.util.regex.Pattern;

/**
 *
 */
public class check_sex_basic implements JaninoCommonScriptBody {
    private static final Logger logger = LoggerFactory.getLogger(check_sex_basic.class);

    private static final String MAIN_CHECK_FIELD = "a12c";
    private static final String MAIN_CHECK_FIELD_1 = "a20";
    private static final String MAIN_CHECK_NAME = "性别";
    private static final String MAIN_CHECK_NAME_1 = "患者证件号码";
    private static final String ID_CARD_REGEX = SettleValidateUtil.ID_CARD_REGEX;
    private static final String MAIN_CHECK_REGION = "湖北";
    @Override
    public Void body(ScriptExecuteWrap wrap) {
        SetlValidBaseBusiVo setlValidBaseBusiVo = (SetlValidBaseBusiVo) wrap.cmp.getFirstContextBean();
        SettleListValidateVo settleListValidateVo = (SettleListValidateVo) wrap.cmp.getRequestData();
        Map<String, String> keysBasicResultMap = setlValidBaseBusiVo.getKeysBasicResultMap();
        SomHiInvyBasInfo somHiInvyBasInfo = settleListValidateVo.getSomHiInvyBasInfo();
        SettleListHandlerVo settleListHandlerVo = new SettleListHandlerVo();
        if (SettleValidateUtil.isEmpty(somHiInvyBasInfo)) {
            return null;
        }
        String sex = somHiInvyBasInfo.getA12c();

        if (SettleValidateUtil.isEmpty(sex)) {
            addErrorDetail(SettleValidateConst.VALIDATE_TYPE_2, SettleValidateConst.VALIDATE_ERROR_TYPE_1,
                    MAIN_CHECK_FIELD,MAIN_CHECK_NAME + "[" + sex + "] 为空",
                    SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
        } else {
            String a20 = somHiInvyBasInfo.getA20();
            if (SettleValidateUtil.isEmpty(a20)) {
                addErrorDetail(SettleValidateConst.VALIDATE_TYPE_2, SettleValidateConst.VALIDATE_ERROR_TYPE_1,
                        MAIN_CHECK_FIELD_1,MAIN_CHECK_NAME_1 + "[" + a20 + "] 为空",
                        SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
            } else {
                if (isValidIDCard(a20)) {
                    char genderDigit = a20.charAt(16);
                    String result = SettleValidateUtil.determineGender(genderDigit);
                    if (!sex.equals(result)) {
                        addErrorDetail(SettleValidateConst.VALIDATE_TYPE_2, SettleValidateConst.VALIDATE_ERROR_TYPE_7,
                                MAIN_CHECK_FIELD, MAIN_CHECK_NAME + "[" + sex + "] 与证件号码不匹配",
                                SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
                    }
                } else {
                    //如果证件号无效
                    addErrorDetail(SettleValidateConst.VALIDATE_TYPE_2, SettleValidateConst.VALIDATE_ERROR_TYPE_7,
                            MAIN_CHECK_FIELD_1, MAIN_CHECK_NAME_1 + "[" + a20 + "] 格式错误",
                            SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
                }
            }
        }
        return null;
    }

    /**
     * 判断证件号码是否有效
     *
     * @param idCard
     * @return
     */
    public static boolean isValidIDCard(String idCard) {
        return Pattern.matches(ID_CARD_REGEX, idCard);
    }

    private void addErrorDetail(String index, String errType, String errorFields, String errDscr, String chkStas, SetlValidBaseBusiVo baseBusiVo) {
        SettleListHandlerVo handlerVo = new SettleListHandlerVo();
        handlerVo.setIndex(index);
        handlerVo.setErrDscr(errDscr);
        handlerVo.setErrorFields(errorFields);
        handlerVo.setChkStas(chkStas);
        handlerVo.setErrType(errType);
        baseBusiVo.getErrorDetails().add(handlerVo);
    }
}
