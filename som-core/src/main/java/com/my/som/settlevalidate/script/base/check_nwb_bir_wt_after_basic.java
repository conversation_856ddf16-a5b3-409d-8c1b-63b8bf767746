package com.my.som.settlevalidate.script.base;

import com.my.som.dto.medicalQuality.BusDiseaseDiagnosisTrim;
import com.my.som.model.dataHandle.SomHiInvyBasInfo;
import com.my.som.settlevalidate.constant.SettleValidateConst;
import com.my.som.settlevalidate.util.SettleValidateUtil;
import com.my.som.settlevalidate.vo.SetlValidBaseBusiVo;
import com.my.som.vo.dataHandle.SettleListHandlerVo;
import com.my.som.vo.dataHandle.SettleListValidateVo;
import com.yomahub.liteflow.script.ScriptExecuteWrap;
import com.yomahub.liteflow.script.body.JaninoCommonScriptBody;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 事后
 * 分娩结局新生儿 出生体重非空性
 */
public class check_nwb_bir_wt_after_basic implements JaninoCommonScriptBody {
    private static final Logger logger = LoggerFactory.getLogger(check_nwb_bir_wt_after_basic.class);

    private static final String MAIN_CHECK_FIELD = "a18";
    private static final String MAIN_CHECK_NAME = "新生儿出生体重";
    private static final String MAIN_CHECK_REGION = "四川,湖北";
    List<String> PARTURITION_CODE_LIST = Arrays.asList("Z37.0", "Z37.2", "Z37.3", "Z37.5", "Z37.6");

    /**
     * 新生儿出生体重-[a18]-基础校验
     * 1、非空时判断是否为整数，不为数字时则提示异常
     * 2、单项数值范围是否在[200,10000]之间
     * 3、多新生儿是否用逗号隔开（隔开后每个判断数字范围）
     *
     * @param wrap
     * @return
     */
    @Override
    public Void body(ScriptExecuteWrap wrap) {
        SetlValidBaseBusiVo setlValidBaseBusiVo = (SetlValidBaseBusiVo) wrap.cmp.getFirstContextBean();
        SettleListValidateVo settleListValidateVo = (SettleListValidateVo) wrap.cmp.getRequestData();
        Map<String, String> keysBasicResultMap = setlValidBaseBusiVo.getKeysBasicResultMap();
        //获取所有诊断集合
        List<BusDiseaseDiagnosisTrim> busDiseaseDiagnosisTrimsList = settleListValidateVo.getBusDiseaseDiagnosisTrimsList();
        SettleListHandlerVo settleListHandlerVo = new SettleListHandlerVo();
        // 检查 somHiInvyBasInfo 是否为空
        SomHiInvyBasInfo somHiInvyBasInfo = settleListValidateVo.getSomHiInvyBasInfo();


        if (SettleValidateUtil.isEmpty(busDiseaseDiagnosisTrimsList)) {
            return null;
        }
        if (SettleValidateUtil.isEmpty(somHiInvyBasInfo.getA18())) {
            String mulNwbBirWt = somHiInvyBasInfo.getMulNwbBirWt();
            if (SettleValidateUtil.isEmpty(mulNwbBirWt)) {
                for (BusDiseaseDiagnosisTrim diagnosis : busDiseaseDiagnosisTrimsList) {
                    if (diagnosis.getC06c1().length() > 5) {
                        //此时说明诊断中含有分娩诊断
                        if (PARTURITION_CODE_LIST.contains(diagnosis.getC06c1().substring(0, 5))) {

                            addErrorDetail(SettleValidateConst.VALIDATE_TYPE_2, SettleValidateConst.VALIDATE_ERROR_TYPE_1,
                                    MAIN_CHECK_FIELD,
                                    "诊断编码中含有Z37.0/Z37.2/Z37.3/Z37.5/Z37.6," + MAIN_CHECK_NAME + "[" + somHiInvyBasInfo.getA18() + "]或多新生儿体重["+mulNwbBirWt+"]不能为空",
                                    SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);

                        }
                    }
                }
            }
        }
        return null;
    }

    private void addErrorDetail(String index, String errType, String errorFields, String errDscr, String chkStas, SetlValidBaseBusiVo baseBusiVo) {
        SettleListHandlerVo handlerVo = new SettleListHandlerVo();
        handlerVo.setIndex(index);
        handlerVo.setErrorFields(errorFields);
        handlerVo.setErrDscr(errDscr);
        handlerVo.setChkStas(chkStas);
        handlerVo.setErrType(errType);
        baseBusiVo.getErrorDetails().add(handlerVo);
    }
}
