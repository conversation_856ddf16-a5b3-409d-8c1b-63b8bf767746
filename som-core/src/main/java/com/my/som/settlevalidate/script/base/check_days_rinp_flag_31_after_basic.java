package com.my.som.settlevalidate.script.base;

import com.my.som.model.dataHandle.SomHiInvyBasInfo;
import com.my.som.settlevalidate.constant.SettleValidateConst;
import com.my.som.settlevalidate.util.SettleValidateUtil;
import com.my.som.settlevalidate.vo.SetlValidBaseBusiVo;
import com.my.som.vo.dataHandle.SettleListHandlerVo;
import com.my.som.vo.dataHandle.SettleListValidateVo;
import com.yomahub.liteflow.script.ScriptExecuteWrap;
import com.yomahub.liteflow.script.body.JaninoCommonScriptBody;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

/**
 * 判断 是否有31天内再住院计划不为空
 */
public class check_days_rinp_flag_31_after_basic implements JaninoCommonScriptBody {
    private static final Logger logger = LoggerFactory.getLogger(check_days_rinp_flag_31_after_basic.class);
    private static final String MAIN_CHECK_FIELD = "b36c";
    private static final String MAIN_CHECK_NAME = "是否有出院31日内再住院计划";
    private static final String MAIN_CHECK_FIELD_1 = "b36c";
    private static final String MAIN_CHECK_NAME_1 = "是否有出院31日内再住院计划";
    private static final String MAIN_CHECK_FIELD_2 = "b37";
    private static final String MAIN_CHECK_NAME_2 = "是否有出院31日内再住院目的";
    private static final String MAIN_CHECK_REGION = "四川";
    /**
     * 事后校验
     * 结算开是否有出院31日内再住院计划始时间(days_rinp_flag_31)基础质控
     * 判断 是否有出院31日内再住院计划 是否为空
     *  不为空则判断 出院31日内再住院目的不为空
     * @param wrap
     * @return
     */
    @Override
    public Void body(ScriptExecuteWrap wrap) {
        SetlValidBaseBusiVo setlValidBaseBusiVo = (SetlValidBaseBusiVo) wrap.cmp.getFirstContextBean();
        SettleListValidateVo settleListValidateVo = (SettleListValidateVo) wrap.cmp.getRequestData();
        Map<String, String> keysBasicResultMap = setlValidBaseBusiVo.getKeysBasicResultMap();
        SomHiInvyBasInfo somHiInvyBasInfo = settleListValidateVo.getSomHiInvyBasInfo();
        SettleListHandlerVo settleListHandlerVo = new SettleListHandlerVo();

        // 检查 somHiInvyBasInfo 是否为空
        if (SettleValidateUtil.isEmpty(somHiInvyBasInfo)) {
            addErrorDetail(settleListHandlerVo, SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY,SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY,
                    (String) SettleValidateConst.CHECK_TYPE_MAP.get(SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY), null,
                    SettleValidateConst.VALIDATE_STATE_FAIL, setlValidBaseBusiVo);
            return null;
        }

        // 获取是否有31天内再住院计划
         String b36c = somHiInvyBasInfo.getB36c();
        if (SettleValidateUtil.isNotEmpty(b36c)) {
            //判断 是否为；(1)无(代码1)、(2)有(代码2)以上两种中的任意一种。
            if(b36c.equals("1")||b36c.equals("2")){
                if(b36c.equals("2")){
                    //获取是否有31天内再住院目的
                    String b37 = somHiInvyBasInfo.getB37();
                    //判断31天内再住院目的是否为空
                    if (SettleValidateUtil.isEmpty(b37)){
                        addErrorDetail(settleListHandlerVo, SettleValidateConst.VALIDATE_TYPE_1,SettleValidateConst.VALIDATE_ERROR_TYPE_1,
                                MAIN_CHECK_NAME_2 + "[" + b37 + "]为空",MAIN_CHECK_FIELD_2,
                                SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
                        keysBasicResultMap.put(MAIN_CHECK_FIELD_2, SettleValidateConst.NULL);
                    }else {
                        keysBasicResultMap.put(MAIN_CHECK_FIELD_2, SettleValidateConst.PASS);
                    }
                }else {
                    keysBasicResultMap.put(MAIN_CHECK_FIELD_2, SettleValidateConst.PASS);
                }
            }else {
                addErrorDetail(settleListHandlerVo, SettleValidateConst.VALIDATE_TYPE_1,SettleValidateConst.VALIDATE_ERROR_TYPE_2,
                        MAIN_CHECK_NAME_1 + "[" + b36c + "]不符合规范",MAIN_CHECK_FIELD_1,
                        SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
                keysBasicResultMap.put(MAIN_CHECK_FIELD_1, SettleValidateConst.OUTLIER);
            }
        }else {
            addErrorDetail(settleListHandlerVo, SettleValidateConst.VALIDATE_TYPE_1,SettleValidateConst.VALIDATE_ERROR_TYPE_1,
                    MAIN_CHECK_NAME_1 + "[" + b36c + "]为空",MAIN_CHECK_FIELD_1,
                    SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
            keysBasicResultMap.put(MAIN_CHECK_FIELD_1, SettleValidateConst.NULL);
        }
        return  null;
    }

    private void addErrorDetail(SettleListHandlerVo handlerVo, String index, String errType, String errDscr, String errorFields, String chkStas, SetlValidBaseBusiVo baseBusiVo) {
        handlerVo.setIndex(index);
        handlerVo.setErrDscr(errDscr);
        handlerVo.setErrorFields(errorFields);
        handlerVo.setChkStas(chkStas);
        handlerVo.setErrType(errType);
        baseBusiVo.getErrorDetails().add(handlerVo);
    }
}
