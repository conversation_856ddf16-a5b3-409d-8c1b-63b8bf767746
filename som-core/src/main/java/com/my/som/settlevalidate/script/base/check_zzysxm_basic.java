package com.my.som.settlevalidate.script.base;

import com.my.som.model.dataHandle.SomHiInvyBasInfo;
import com.my.som.settlevalidate.constant.SettleValidateConst;
import com.my.som.settlevalidate.util.SettleValidateUtil;
import com.my.som.settlevalidate.vo.SetlValidBaseBusiVo;
import com.my.som.util.SettleListValidateUtil;
import com.my.som.vo.dataHandle.SettleListHandlerVo;
import com.my.som.vo.dataHandle.SettleListValidateVo;
import com.yomahub.liteflow.script.ScriptExecuteWrap;
import com.yomahub.liteflow.script.body.JaninoCommonScriptBody;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

public class check_zzysxm_basic implements JaninoCommonScriptBody {
    private static final Logger logger = LoggerFactory.getLogger(check_zzysxm_basic.class);

    private static final String MAIN_CHECK_FIELD = "b52n";
    private static final String MAIN_CHECK_NAME = "主诊医师姓名";
    private static final String MAIN_CHECK_REGION = "四川";
    /**
     *
     *
     * @param wrap
     * @return
     */
    @Override
    public Void body(ScriptExecuteWrap wrap) {
        SetlValidBaseBusiVo setlValidBaseBusiVo = (SetlValidBaseBusiVo) wrap.cmp.getFirstContextBean();
        SettleListValidateVo settleListValidateVo = (SettleListValidateVo) wrap.cmp.getRequestData();
        Map<String, String> keysBasicResultMap = setlValidBaseBusiVo.getKeysBasicResultMap();
        SomHiInvyBasInfo somHiInvyBasInfo = settleListValidateVo.getSomHiInvyBasInfo();
        SettleListHandlerVo settleListHandlerVo = new SettleListHandlerVo();
        if (SettleValidateUtil.isEmpty(somHiInvyBasInfo)) {
            return null;
        }

        //获取主诊医师代码
        String b51c = somHiInvyBasInfo.getB51c();
        //获取主诊医师姓名
        String b52n = somHiInvyBasInfo.getB52n();
        boolean checkBool = SettleValidateUtil.checkNotNullOrNotBlank(b52n,keysBasicResultMap,MAIN_CHECK_FIELD);
        if (checkBool) {
            if(SettleValidateUtil.isNotEmpty(b51c)){
                Map<String,Object> map = (Map<String,Object>)(setlValidBaseBusiVo.getSettleListParams().get(SettleListValidateUtil.KEY_DOCTOR_NAME));
                Map<String,Object> doctorNameMap = (Map<String,Object>)map.get(somHiInvyBasInfo.getHospitalId()+SettleListValidateUtil.KEY_DOCTOR_NAME);
                String doctorName= (String)doctorNameMap.get(b51c);
                if(SettleValidateUtil.isNotEmpty(doctorName)){
                    if(b52n.equals(doctorName)){
                        // 正常值，基础校验通过不写错误信息
                        keysBasicResultMap.put(MAIN_CHECK_FIELD, SettleValidateConst.PASS);
                    }else {
                        addErrorDetail(settleListHandlerVo, SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_ERROR_TYPE_2,
                                "页面中的" + MAIN_CHECK_NAME + "[" + b52n + "]与该编码[" + b51c + "]对应的医院医生名称[" + doctorName + "]不匹配",
                                MAIN_CHECK_FIELD,
                                SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
                        keysBasicResultMap.put(MAIN_CHECK_FIELD, SettleValidateConst.OUTLIER);
                    }
                }
            }
        }else {
            SettleValidateUtil.addErrorDetail(settleListHandlerVo, SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_TYPE_1,
                    MAIN_CHECK_NAME + "[" + b52n + "]为空", MAIN_CHECK_FIELD,
                    SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
            keysBasicResultMap.put(MAIN_CHECK_FIELD, SettleValidateConst.NULL);
        }
        return null;
    }

    private void addErrorDetail(SettleListHandlerVo handlerVo, String index, String errType, String errDscr, String errorFields, String chkStas, SetlValidBaseBusiVo baseBusiVo) {
        handlerVo.setIndex(index);
        handlerVo.setErrDscr(errDscr);
        handlerVo.setErrorFields(errorFields);
        handlerVo.setChkStas(chkStas);
        handlerVo.setErrType(errType);
        baseBusiVo.getErrorDetails().add(handlerVo);
    }
}
