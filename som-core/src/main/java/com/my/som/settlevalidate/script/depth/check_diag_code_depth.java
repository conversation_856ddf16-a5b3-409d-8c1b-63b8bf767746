package com.my.som.settlevalidate.script.depth;

import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.my.som.common.constant.DrgConst;
import com.my.som.common.exception.AppException;
import com.my.som.dto.medicalQuality.BusDiseaseDiagnosisTrim;
import com.my.som.model.dataHandle.SomHiInvyBasInfo;
import com.my.som.settlevalidate.constant.SettleValidateConst;
import com.my.som.settlevalidate.util.SettleValidateUtil;
import com.my.som.settlevalidate.vo.SetlValidBaseBusiVo;
import com.my.som.util.SettleListValidateUtil;
import com.my.som.vo.common.DiagConFlictCodeVo;
import com.my.som.vo.dataHandle.SettleListDisSectionVo;
import com.my.som.vo.dataHandle.SettleListHandlerVo;
import com.my.som.vo.dataHandle.SettleListValidateVo;
import com.yomahub.liteflow.script.ScriptExecuteWrap;
import com.yomahub.liteflow.script.body.JaninoCommonScriptBody;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

/**
 * 诊断编码深度质控
 */
public class check_diag_code_depth implements JaninoCommonScriptBody {
    private static final Logger logger = LoggerFactory.getLogger(check_diag_code_depth.class);

    private static final String MAIN_CHECK_NAME = "出院西医诊断编码";
    private static final String MAIN_CHECK_NAME1 = "出院西医诊断名称";
    private static final String MAIN_CHECK_FIELD = "c06c1";
    private static final String MAIN_CHECK_FIELD1 = "c07n1";
    private static final String CODE = "b34c";
    private static final String MAIN_CHECK_REGION = "四川,湖北";
    private static final String WX_DIAG = "1";
    private static final List<String> ERROR_CODE_BEGIN_LIST = Arrays.asList("V", "W", "X", "Y");
    //分娩
    private static final List<String> DELIVER_CODE_LIST = Arrays.asList("O80", "O81", "O82", "O83", "O84");
    //流产
    private static final List<String> ABORTION_CODE_LIST = Arrays.asList("O00", "O01", "O02", "O03", "O04", "O05", "O06", "O07", "O08");
    //编码长度
    private static final int CODE_LENGTH = 5;
    // 离院方式 5
    private static final String DSCG_WAY_5 = "5";

    /**
     * @param wrap
     * @return
     */
    @Override
    public Void body(ScriptExecuteWrap wrap) {

        SetlValidBaseBusiVo setlValidBaseBusiVo = (SetlValidBaseBusiVo) wrap.cmp.getFirstContextBean();
        SettleListValidateVo settleListValidateVo = (SettleListValidateVo) wrap.cmp.getRequestData();
        Map<String, String> keysBasicResultMap = setlValidBaseBusiVo.getKeysBasicResultMap();

        List<BusDiseaseDiagnosisTrim> busDiseaseDiagnosisTrimsList = settleListValidateVo.getBusDiseaseDiagnosisTrimsList();
        SomHiInvyBasInfo somHiInvyBasInfo = settleListValidateVo.getSomHiInvyBasInfo();
        if (SettleValidateUtil.isEmpty(busDiseaseDiagnosisTrimsList)) {
            return null;
        }
        // 检查诊断信息是否为空
        if (SettleValidateUtil.isEmpty(somHiInvyBasInfo)) {
            return null;
        }

        //获取编码为灰码的集合
        Map<String, Object> grepMap = (Map<String, Object>) setlValidBaseBusiVo.getSettleListParams().get(SettleListValidateUtil.KEY_ICD10_GRAY_CODE_2);
        // 获取清单字典和ICD-10相关映射
        Map<String, Object> icd10Map = (Map<String, Object>) setlValidBaseBusiVo.getSettleListParams().get(SettleListValidateUtil.KEY_ICD10);

        Map<String, Object> dictMap = (Map<String, Object>) setlValidBaseBusiVo.getSettleListParams().get(SettleListValidateUtil.KEY_DIS_SECTION_LIST);
        //获取性别校对集合
        List<String> checkSexList = getCheckSexList(dictMap, somHiInvyBasInfo, setlValidBaseBusiVo);
        // 获取不能作为主要诊断编码集合
        Map<String, Object> notBeMainCodemap = getNotBeMainCodeList(setlValidBaseBusiVo);
        //获取离院方式为5的编码集合
        String b34c = somHiInvyBasInfo.getB34c();
        List<String> dscgWayIs5CodeList = getDscgWayIs5CodeList(setlValidBaseBusiVo, keysBasicResultMap);
        Map<String, String> codeConflictMap = getCodeConflictMap(setlValidBaseBusiVo);


        // 构造set判断是否存在重复编码
        Set<String> diagCodeSet = new HashSet<>();
        //主诊端集合
        List<BusDiseaseDiagnosisTrim> mainCodeList = new ArrayList<>();


        for (int i = 0; i < busDiseaseDiagnosisTrimsList.size(); i++) {
            BusDiseaseDiagnosisTrim busDiseaseDiagnosisTrim = (BusDiseaseDiagnosisTrim) busDiseaseDiagnosisTrimsList.get(i);
            //检测主诊段
            checkMainDiag(busDiseaseDiagnosisTrim, mainCodeList, i, setlValidBaseBusiVo);

            if(WX_DIAG.equals(busDiseaseDiagnosisTrim.getType())){

            if (!SettleValidateConst.NULL.equals((String) keysBasicResultMap.get(i + MAIN_CHECK_FIELD))) {
                String diagCode = busDiseaseDiagnosisTrim.getC06c1().toUpperCase();

                if (diagCode.length() < CODE_LENGTH) {
                    throw new AppException("第" + (i + 1) + "个疾病诊断的" + MAIN_CHECK_NAME + "的字符串长度不符");
                }
                //判断编码规范性 编码是否符合ICD-10标准
                checkCodeSpecification(i, busDiseaseDiagnosisTrim, keysBasicResultMap, icd10Map, setlValidBaseBusiVo, grepMap);

                if (checkSexList.size() > 0) {
                    // 判断是否和性别相符合
                    checkDiagnosisGenderConsistency(i, checkSexList, diagCode, setlValidBaseBusiVo);
                }
                // 判断诊断是否和开头是否符合
                checkDiagnosisCodeConsistency(i, diagCode, setlValidBaseBusiVo);

                if (dscgWayIs5CodeList.size() > 0) {
                    //判断诊断编码是死亡诊断 离院方式却不为5
                    checkCodeBelongDeathDiag(dscgWayIs5CodeList, diagCode, b34c, setlValidBaseBusiVo);
                }

                // 判断是否有重复编码
                checkRepeat(i, diagCodeSet, diagCode, setlValidBaseBusiVo);
            }
            }
        }
        //判断离院方式为死亡 ，但没有死亡诊断
        checkDeathRelatedDiag(b34c, busDiseaseDiagnosisTrimsList, dscgWayIs5CodeList, setlValidBaseBusiVo);
        checkDiagMainCode(mainCodeList, somHiInvyBasInfo, dictMap, notBeMainCodemap, setlValidBaseBusiVo, keysBasicResultMap);
        //判断诊断代码是否存在冲突

        checkDiagConflit(codeConflictMap, busDiseaseDiagnosisTrimsList, setlValidBaseBusiVo);

        //主要诊断或者其它诊断编码出现080-084编码
        checkDiagnosisLackDelivery(busDiseaseDiagnosisTrimsList, setlValidBaseBusiVo, keysBasicResultMap);

        return null;
    }

    private void checkDeathRelatedDiag(String b34c, List<BusDiseaseDiagnosisTrim> busDiseaseDiagnosisTrimsList, List<String> dscgWayIs5CodeList, SetlValidBaseBusiVo setlValidBaseBusiVo) {

        if (DSCG_WAY_5.equals(b34c)) {
            boolean flag = true;
            for (int i = 0; i < busDiseaseDiagnosisTrimsList.size(); i++) {
                BusDiseaseDiagnosisTrim busDiseaseDiagnosisTrim = (BusDiseaseDiagnosisTrim) busDiseaseDiagnosisTrimsList.get(i);
                if(WX_DIAG.equals(busDiseaseDiagnosisTrim.getType())){
                String diagCode = busDiseaseDiagnosisTrim.getC06c1().toUpperCase();
                if (dscgWayIs5CodeList.contains(diagCode)) {
                    flag = false;
                }}
            }
            if (flag) {
                addErrorDetail(SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_ERROR_TYPE_2,
                        MAIN_CHECK_FIELD, "离院方式编码[" + b34c + "]为5 但无与死亡相关疾病诊断",
                        SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
            }
        }
    }

    /**
     * 获取转换后的诊断冲突集合
     *
     * @param setlValidBaseBusiVo
     * @return
     */
    private Map<String, String> getCodeConflictMap(SetlValidBaseBusiVo setlValidBaseBusiVo) {
        List<DiagConFlictCodeVo> codeConflictList = (List<DiagConFlictCodeVo>) setlValidBaseBusiVo.getSettleListParams().get(SettleListValidateUtil.KEY_DIAG_CONFLICT);
        Map<String, String> map = new HashMap<>();
        for (DiagConFlictCodeVo diagConFlictCodeVo : codeConflictList) {
            String cateCode = diagConFlictCodeVo.getCateCode();
            map.put(diagConFlictCodeVo.getConflictCode(), cateCode);
        }
        return map;
    }

    /**
     * 判断诊断代码是否存在冲突
     */
    private void checkDiagConflit(Map<String, String> codeConflictList, List<BusDiseaseDiagnosisTrim> busDiseaseDiagnosisTrimsList, SetlValidBaseBusiVo setlValidBaseBusiVo) {
        List<String> keys = new ArrayList<>();
        for (BusDiseaseDiagnosisTrim busDiseaseDiagnosisTrim : busDiseaseDiagnosisTrimsList) {
            if(WX_DIAG.equals(busDiseaseDiagnosisTrim.getType())){
            String code = busDiseaseDiagnosisTrim.getC06c1().toUpperCase();
            if (SettleValidateUtil.isNotEmpty(code)) {
                for (String key : codeConflictList.keySet()) {
                    if (code.startsWith(key)) {
                        //将主冲突的编码放入集合
                        keys.add(key);
                    }
                }
            }
            }
        }

        if (!SettleValidateUtil.isEmpty(keys)) {
            for (String key : keys) {
                //获取到具体的次要冲突编码集合
                String codeListStr = (String) codeConflictList.get(key);
                List<String> list = Arrays.asList(codeListStr.split(","));
                for (String codeStart : list) {
                    for (BusDiseaseDiagnosisTrim busDiseaseDiagnosisTrim : busDiseaseDiagnosisTrimsList) {
                        String oldcode = busDiseaseDiagnosisTrim.getC06c1().toUpperCase();
                        if (SettleValidateUtil.isNotEmpty(oldcode) && oldcode.startsWith(codeStart.toUpperCase())) {
                            addErrorDetail(SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_ERROR_TYPE_306,
                                    MAIN_CHECK_FIELD,
                                    "开头为 [" + key + "]的疾病诊断编码 与开头为[" + list + "]的疾病诊断编码 冲突 ",
                                    SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
                        }
                    }
                }
            }
        }
    }

    /**
     * 检查主诊端编码
     */
    private void checkDiagMainCode(List<BusDiseaseDiagnosisTrim> mainCodeList, SomHiInvyBasInfo somHiInvyBasInfo, Map<String, Object> dictMap, Map<String, Object> notBeMainCodemap, SetlValidBaseBusiVo setlValidBaseBusiVo, Map<String, String> keysBasicResultMap) {
        if (mainCodeList.size() > 1) {
            //说明重复
            addErrorDetail(SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_ERROR_TYPE_10,
                    MAIN_CHECK_FIELD,
                    "存在多个主诊断编码",
                    SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
        } else if (mainCodeList.size() == 0) {
            //没有主诊端
            addErrorDetail(SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_ERROR_TYPE_10,
                    MAIN_CHECK_FIELD,
                    "主诊断编码不存在",
                    SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
            //没有主诊端
        } else {
            //判断不为作为该患者的主要编码是否不嫩作为主要编码
            if (notBeMainCodemap.size() > 0) {
                // 判断年龄大于29天不能作为主要诊断相符合
                BusDiseaseDiagnosisTrim busDiseaseDiagnosisTrim = (BusDiseaseDiagnosisTrim) mainCodeList.get(0);
                checkDiagnosisAgeConsistency(busDiseaseDiagnosisTrim, somHiInvyBasInfo, dictMap, notBeMainCodemap, setlValidBaseBusiVo, keysBasicResultMap);
            }
        }
    }

    private void checkRepeat(int i, Set<String> diagCodeSet, String diagCode, SetlValidBaseBusiVo setlValidBaseBusiVo) {
        if (!diagCodeSet.add(diagCode)) {
            addErrorDetail(SettleValidateConst.VALIDATE_TYPE_2, SettleValidateConst.VALIDATE_ERROR_TYPE_306,
                    MAIN_CHECK_FIELD,
                    "第" + (i + 1) + "个疾病诊断：" + MAIN_CHECK_NAME + "[" + diagCode + "]重复",
                    SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
        }
    }

    private void checkCodeBelongDeathDiag(List<String> dscgWayIs5CodeList, String diagCode, String b34c, SetlValidBaseBusiVo setlValidBaseBusiVo) {
        if (dscgWayIs5CodeList.contains(diagCode)) {
            if (!DSCG_WAY_5.equals(b34c)) {
                addErrorDetail(SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_ERROR_TYPE_2,
                        MAIN_CHECK_FIELD, "存在与死亡相关疾病诊断[" + diagCode + "] 但患者离院方式[" + b34c + "]不为5",
                        SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
            }
        }
    }

    private List<String> getDscgWayIs5CodeList(SetlValidBaseBusiVo setlValidBaseBusiVo, Map<String, String> keysBasicResultMap) {
        List<String> dscgWayIs5CodeList = new ArrayList<>();
        if (SettleValidateConst.PASS.equals((String) keysBasicResultMap.get(CODE))) {
            //获取院系 科别编码
            Map<String, Object> map = (Map<String, Object>) setlValidBaseBusiVo.getSettleListParams().get(SettleListValidateUtil.KEY_DICT);
            dscgWayIs5CodeList.addAll((List<String>) map.get("SWZDBM"));
        }
        return listToUpCase(dscgWayIs5CodeList);
    }

    private Map<String, Object> getNotBeMainCodeList(SetlValidBaseBusiVo setlValidBaseBusiVo) {
        Map<String, Object> orgMap = (Map<String, Object>) setlValidBaseBusiVo.getSettleListParams().get(SettleListValidateUtil.KEY_NOT_BE_MAIN_DIAG);
//        Map<String, Object> resultMap = new HashMap<>();
//        if (orgMap == null || orgMap.isEmpty()) {
//            return resultMap;
//        }
//        Set<Map.Entry<String,Object>> entrySet = orgMap.entrySet();
//        for (Map.Entry<String, Object> entry : entrySet) {
//            String key = entry.getKey();
//            Object value = entry.getValue();
//            resultMap.put(key.toUpperCase(), value.toString());
//        }
        return orgMap;
    }


    private List<String> getCheckSexList(Map<String, Object> map, SomHiInvyBasInfo somHiInvyBasInfo, SetlValidBaseBusiVo setlValidBaseBusiVo) {
        String a12c = somHiInvyBasInfo.getA12c();
        List<String> diagSections = new ArrayList<>();
        if (!SettleValidateUtil.isEmpty(a12c)) {
            if (DrgConst.MAN.equals(a12c)) {
                List<SettleListDisSectionVo> settleListDisSectionVos = (List<SettleListDisSectionVo>) map.get(DrgConst.MAN);
                for (SettleListDisSectionVo settleListDisSectionVo : settleListDisSectionVos) {
                    diagSections.add(settleListDisSectionVo.getDiagSec());
                }
            } else if (DrgConst.WOMAN.equals(a12c)) {
                List<SettleListDisSectionVo> settleListDisSectionVos = (List<SettleListDisSectionVo>) map.get(DrgConst.WOMAN);
                for (SettleListDisSectionVo settleListDisSectionVo : settleListDisSectionVos) {
                    diagSections.add(settleListDisSectionVo.getDiagSec());
                }
            } else {
                addErrorDetail(SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_ERROR_TYPE_2,
                        MAIN_CHECK_FIELD,
                        "该性别性别填写不规范无法获取到诊断该性别编码的目录",
                        SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
            }
        } else {
            addErrorDetail(SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_ERROR_TYPE_1,
                    MAIN_CHECK_FIELD,
                    "性别为空",
                    SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
        }

        return listToUpCase(diagSections);
    }

    private void checkMainDiag(BusDiseaseDiagnosisTrim busDiseaseDiagnosisTrim, List<BusDiseaseDiagnosisTrim> mainCodeList, int i, SetlValidBaseBusiVo setlValidBaseBusiVo) {
        // 判断是否存在主要诊断
        if (busDiseaseDiagnosisTrim.getSeq() == 0) {
            if (DrgConst.MAINDIAG.equals(busDiseaseDiagnosisTrim.getMainDiagFlag())) {
                mainCodeList.add(busDiseaseDiagnosisTrim);
            } else {
                addErrorDetail(SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_ERROR_TYPE_1,
                        MAIN_CHECK_FIELD,
                        "主要诊断信息应该排列在第一位",
                        SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
            }
        } else if (SettleValidateUtil.isEmpty(busDiseaseDiagnosisTrim.getSeq())) {
            addErrorDetail(SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_ERROR_TYPE_1,
                    MAIN_CHECK_FIELD,
                    "第" + (i + 1) + "个疾病诊断：编码为[" + busDiseaseDiagnosisTrim.getC06c1() + "]的" + "Seq数据为空",
                    SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
        }
    }


    /**
     * 判断开头是否符合标准
     *
     * @param setlValidBaseBusiVo
     */
    private void checkDiagnosisCodeConsistency(int i, String diseCode, SetlValidBaseBusiVo setlValidBaseBusiVo) {
        if (ERROR_CODE_BEGIN_LIST.contains(diseCode.substring(0, 1))) {
            addErrorDetail(SettleValidateConst.VALIDATE_TYPE_2, SettleValidateConst.VALIDATE_ERROR_TYPE_2,
                    MAIN_CHECK_FIELD,
                    "第" + (i + 1) + "个疾病诊断：" + MAIN_CHECK_NAME + "[" + diseCode + "]诊断及编码范围应填写A~U开头和Z开头的编码及名称",
                    SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
        }
    }

    /**
     * 不能作为主要诊断
     */
    private void checkDiagnosisAgeConsistency(BusDiseaseDiagnosisTrim busDiseaseDiagnosisTrim, SomHiInvyBasInfo somHiInvyBasInfo, Map<String, Object> dicmap,
                                              Map<String, Object> notBeMainCodemap, SetlValidBaseBusiVo setlValidBaseBusiVo, Map<String, String> keysBasicResultMap) {
        String diagCode = busDiseaseDiagnosisTrim.getC06c1();
        if (notBeMainCodemap.containsKey(diagCode)) {
            String dscr = (String) notBeMainCodemap.get(diagCode);
            addErrorDetail(SettleValidateConst.VALIDATE_TYPE_2, SettleValidateConst.VALIDATE_ERROR_TYPE_306,
                    MAIN_CHECK_FIELD,
                    MAIN_CHECK_NAME + "[" + busDiseaseDiagnosisTrim.getC06c1() + "]" + dscr,
                    SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
        }
        if (diagCode.length() < 3) {
            addErrorDetail(SettleValidateConst.VALIDATE_TYPE_2, SettleValidateConst.VALIDATE_ERROR_TYPE_306,
                    MAIN_CHECK_FIELD,
                    MAIN_CHECK_NAME + "[" + busDiseaseDiagnosisTrim.getC06c1() + "]编码格式错误",
                    SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
        }
        List<String> dayMoreThanList = getlist(somHiInvyBasInfo, dicmap);
        if (!SettleValidateUtil.isEmpty(dayMoreThanList)) {
            diagCode = busDiseaseDiagnosisTrim.getC06c1().toUpperCase();
            if (dayMoreThanList.contains(diagCode.substring(0, 3))) {
                addErrorDetail(SettleValidateConst.VALIDATE_TYPE_2, SettleValidateConst.VALIDATE_ERROR_TYPE_306,
                        MAIN_CHECK_FIELD,
                        MAIN_CHECK_NAME + "[" + busDiseaseDiagnosisTrim.getC06c1() + "],疾病编码指起源于围生期,患者年龄大于29天不能作为主要诊断",
                        SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
            }
        }
    }


    private List<String> getlist(SomHiInvyBasInfo somHiInvyBasInfo, Map<String, Object> dicmap) {
        Integer a16 = somHiInvyBasInfo.getA16();
        Integer a14 = somHiInvyBasInfo.getA14();
        List<String> dayMoreThanList = new ArrayList<String>();
        if ((ObjectUtils.isNotEmpty(a16) && a16 > 29) || (ObjectUtils.isNotEmpty(a14) && a14 > 0)) {
            List<SettleListDisSectionVo> settleListDisSectionVos = (List<SettleListDisSectionVo>) dicmap.get(DrgConst.AGE_MORE_THAN_29D);
            for (SettleListDisSectionVo settleListDisSectionVo : settleListDisSectionVos) {
                dayMoreThanList.add(settleListDisSectionVo.getDiagSec());
            }
            return listToUpCase(dayMoreThanList);
        }
        return null;
    }

    private void checkDiagnosisGenderConsistency(int i, List<String> checkSexList, String diagCode, SetlValidBaseBusiVo setlValidBaseBusiVo) {
        if (checkSexList.contains(diagCode.substring(0, 3)) || checkSexList.contains(diagCode.substring(0, 5))) {
            addErrorDetail(SettleValidateConst.VALIDATE_TYPE_2, SettleValidateConst.VALIDATE_ERROR_TYPE_306,
                    MAIN_CHECK_FIELD,
                    "第" + (i + 1) + "个疾病诊断：" + MAIN_CHECK_NAME + "[" + diagCode + "]与性别不符",
                    SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
        }
    }

    private void checkCodeSpecification(int i, BusDiseaseDiagnosisTrim busDiseaseDiagnosisTrim, Map<String, String> keysBasicResultMap,
                                        Map<String, Object> icd10Map, SetlValidBaseBusiVo setlValidBaseBusiVo,
                                        Map<String, Object> grepMap) {
        String diagCode = busDiseaseDiagnosisTrim.getC06c1();
        String diagName = busDiseaseDiagnosisTrim.getC07n1();
        if (SettleValidateConst.PASS.equals((String) keysBasicResultMap.get(i + MAIN_CHECK_FIELD))
                && SettleValidateConst.PASS.equals((String) keysBasicResultMap.get(i + MAIN_CHECK_FIELD1))) {
            // 验证编码是否符合ICD-10标准
            if (!icd10Map.containsKey(diagCode)) {
                addErrorDetail(SettleValidateConst.VALIDATE_TYPE_2, SettleValidateConst.VALIDATE_ERROR_TYPE_6,
                        MAIN_CHECK_FIELD,
                        "第" + (i + 1) + "个疾病诊断：" + MAIN_CHECK_NAME + "[" + diagCode + "]不符合医保2.0",
                        SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
            } else {
                // 验证名称是否符合ICD-10标准
                String icdName = (String) icd10Map.get(diagCode);
                if (!diagName.equals(icdName)) {
                    addErrorDetail(SettleValidateConst.VALIDATE_TYPE_2, SettleValidateConst.VALIDATE_ERROR_TYPE_6,
                            MAIN_CHECK_FIELD,
                            "第" + (i + 1) + "个疾病诊断：" + MAIN_CHECK_NAME1 + "[" + diagName + "]不符合医保2.0,应改为[" + icdName + "]",
                            SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
                }
            }
            // 检查主要诊断是否存在医保停用码
            if (grepMap.containsKey(diagCode)) {
                addErrorDetail(SettleValidateConst.VALIDATE_TYPE_2, SettleValidateConst.VALIDATE_ERROR_TYPE_12,
                        MAIN_CHECK_FIELD,
                        "第" + (i + 1) + "个疾病诊断：" + MAIN_CHECK_NAME + "[" + diagCode + "]是医保停用码",
                        SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
            }
        }
    }

    private void checkDiagnosisLackDelivery(List<BusDiseaseDiagnosisTrim> busDiseaseDiagnosisTrimsList, SetlValidBaseBusiVo setlValidBaseBusiVo, Map<String, String> keysBasicResultMap) {
        boolean isAbortion = false;
        boolean isDeliver = false;
        boolean bornOK = false;
        for (int i = 0; i < busDiseaseDiagnosisTrimsList.size(); i++) {
            if (!SettleValidateConst.NULL.equals(keysBasicResultMap.get(i + MAIN_CHECK_FIELD))) {
                BusDiseaseDiagnosisTrim busDiseaseDiagnosisTrim = (BusDiseaseDiagnosisTrim) busDiseaseDiagnosisTrimsList.get(i);
                if(WX_DIAG.equals(busDiseaseDiagnosisTrim.getType())){
                String diagCode = busDiseaseDiagnosisTrim.getC06c1();
                if (DELIVER_CODE_LIST.contains(diagCode.substring(0, 3))) {
                    isDeliver = true;
                }
                if (ABORTION_CODE_LIST.contains(diagCode.substring(0, 3))) {
                    isAbortion = true;
                }
                if (busDiseaseDiagnosisTrim.getC06c1().substring(0, 3).equals("Z37")) {
                    bornOK = true;
                }
                }
            }
        }

        if (isDeliver) {
            if (!isAbortion && !bornOK) {
                addErrorDetail(SettleValidateConst.VALIDATE_TYPE_2, SettleValidateConst.VALIDATE_ERROR_TYPE_2,
                        MAIN_CHECK_FIELD,
                        MAIN_CHECK_NAME + "不规范" + "诊断编码出现080-084编码 无流产编码 也没有有分娩结局编码Z37",
                        SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
            }
        }

    }

    private List<String> listToUpCase(List<String> list) {
        List<String> upperCaseStrings = new ArrayList<>();
        for (String str : list) {
            upperCaseStrings.add(str.toUpperCase());
        }
        return upperCaseStrings;
    }

    private void addErrorDetail(String index, String errType, String errorFields, String errDscr, String chkStas, SetlValidBaseBusiVo baseBusiVo) {
        SettleListHandlerVo handlerVo = new SettleListHandlerVo();
        handlerVo.setIndex(index);
        handlerVo.setErrorFields(errorFields);
        handlerVo.setErrDscr(errDscr);
        handlerVo.setChkStas(chkStas);
        handlerVo.setErrType(errType);
        baseBusiVo.getErrorDetails().add(handlerVo);
    }
}
