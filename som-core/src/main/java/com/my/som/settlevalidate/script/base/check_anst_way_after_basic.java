package com.my.som.settlevalidate.script.base;

import com.my.som.model.dataHandle.SomOprnOprtInfo;
import com.my.som.settlevalidate.constant.SettleValidateConst;
import com.my.som.settlevalidate.util.SettleValidateUtil;
import com.my.som.settlevalidate.vo.SetlValidBaseBusiVo;
import com.my.som.util.SettleListValidateUtil;
import com.my.som.vo.dataHandle.SettleListHandlerVo;
import com.my.som.vo.dataHandle.SettleListValidateVo;
import com.yomahub.liteflow.script.ScriptExecuteWrap;
import com.yomahub.liteflow.script.body.JaninoCommonScriptBody;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Map;

/**
 * 事后质检 麻醉方式
 */
public class check_anst_way_after_basic implements JaninoCommonScriptBody {
    private static final Logger logger = LoggerFactory.getLogger(check_anst_way_after_basic.class);

    private static final String MAIN_CHECK_FIELD = "c43";
    private static final String MAIN_CHECK_NAME = "麻醉方式";
    private static final String MZFS = "MZFS";
    private static final String MAIN_CHECK_REGION = "四川,湖北";
    /**
     * 麻醉开始时间(anst_begntime)基础质控
     * 麻醉结束时间(anst_endtime)基础质控
     * 判断麻醉开始时间是否为空、麻醉结束时间是否为空
     * 麻醉开始时间是否小于麻醉结束时间
     *
     * @param wrap
     * @return
     */
    @Override
    public Void body(ScriptExecuteWrap wrap) {
        SetlValidBaseBusiVo setlValidBaseBusiVo = (SetlValidBaseBusiVo) wrap.cmp.getFirstContextBean();
        SettleListValidateVo settleListValidateVo = (SettleListValidateVo) wrap.cmp.getRequestData();
        Map<String, String> keysBasicResultMap = setlValidBaseBusiVo.getKeysBasicResultMap();
        List<SomOprnOprtInfo> busOperateDiagnosisList = settleListValidateVo.getBusOperateDiagnosisList();
        SettleListHandlerVo settleListHandlerVo = new SettleListHandlerVo();

        // 检查 busOperateDiagnosisList(手术操作) 是否为空
        if (SettleValidateUtil.isEmpty(busOperateDiagnosisList)) {
            return null;
        }

        //判断是否符合规范 获取麻醉方式字典 集合
        Map<String, Object> map = (Map<String, Object>) setlValidBaseBusiVo.getSettleListParams().get(SettleListValidateUtil.KEY_DICT);
        List<String> anstWayList = (List<String>) map.get(MZFS);

        for (int i = 0; i < busOperateDiagnosisList.size(); i++) {
            SomOprnOprtInfo somOprnOprtInfo = (SomOprnOprtInfo)busOperateDiagnosisList.get(i);

            if (SettleValidateUtil.isNotEmpty(somOprnOprtInfo.getC43())) {
                if (!anstWayList.contains(somOprnOprtInfo.getC43())) {
                    addErrorDetail(SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_ERROR_TYPE_2,
                            MAIN_CHECK_FIELD, "第" + (i + 1) + "个手术操作：编码为["+somOprnOprtInfo.getC35c()+"]的" + MAIN_CHECK_NAME + "[" + somOprnOprtInfo.getC35c() + "]不符合规范",
                            SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
                    keysBasicResultMap.put(i + MAIN_CHECK_FIELD, SettleValidateConst.OUTLIER);
                } else {
                    keysBasicResultMap.put(i + MAIN_CHECK_FIELD, SettleValidateConst.PASS);
                }
            } else {
                keysBasicResultMap.put(i + MAIN_CHECK_FIELD, SettleValidateConst.NULL);
            }
        }
        return null;
    }

    private void addErrorDetail(String index, String errType, String errorFields, String errDscr, String chkStas, SetlValidBaseBusiVo baseBusiVo) {
        SettleListHandlerVo handlerVo = new SettleListHandlerVo();
        handlerVo.setIndex(index);
        handlerVo.setErrorFields(errorFields);
        handlerVo.setErrDscr(errDscr);
        handlerVo.setChkStas(chkStas);
        handlerVo.setErrType(errType);
        baseBusiVo.getErrorDetails().add(handlerVo);
    }
}
