package com.my.som.settlevalidate.service;

import com.my.som.settlevalidate.vo.SetlValidBaseBusiVo;
import com.my.som.vo.dataHandle.SettleListHandlerVo;
import com.my.som.vo.dataHandle.SettleListValidateVo;

import java.util.Map;

public interface SettleListValidateLiteFlowService {


    /**
     * 执行结算清单质控规则
     *
     * @param settleListValidateVo
     * @param settleListParams
     * @return
     */
    SettleListHandlerVo executeSettlVaildate(SettleListValidateVo settleListValidateVo, Map<String, Object> settleListParams);

}
