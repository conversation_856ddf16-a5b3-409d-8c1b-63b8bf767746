package com.my.som.settlevalidate.script.base;
import com.my.som.model.dataHandle.SomHiInvyBasInfo;
import com.my.som.model.dataHandle.SomOprnOprtInfo;
import com.my.som.settlevalidate.constant.SettleValidateConst;
import com.my.som.settlevalidate.util.SettleValidateUtil;
import com.my.som.settlevalidate.vo.SetlValidBaseBusiVo;
import com.my.som.util.SettleListValidateUtil;
import com.my.som.vo.dataHandle.SettleListHandlerVo;
import com.my.som.vo.dataHandle.SettleListValidateVo;
import com.yomahub.liteflow.script.ScriptExecuteWrap;
import com.yomahub.liteflow.script.body.JaninoCommonScriptBody;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Map;

public class check_oper_dr_name_basic implements JaninoCommonScriptBody {
    private static final Logger logger = LoggerFactory.getLogger(check_oper_dr_name_basic.class);

    private static final String MAIN_CHECK_FIELD = "oprn_oprt_oper_name";
    private static final String MAIN_CHECK_NAME = "术者医师姓名";
    private static final String MAIN_CHECK_REGION = "四川,湖北";
    /**
     * 术者医师姓名(oper_dr_name)质控
     * 1.存在手术或操作时，术者医师姓名不能为空
     * @param wrap
     * @return
     */
    @Override
    public Void body(ScriptExecuteWrap wrap) {

        SetlValidBaseBusiVo setlValidBaseBusiVo = (SetlValidBaseBusiVo) wrap.cmp.getFirstContextBean();
        SettleListValidateVo settleListValidateVo = (SettleListValidateVo) wrap.cmp.getRequestData();
        List<SomOprnOprtInfo> busOperateDiagnosisList = settleListValidateVo.getBusOperateDiagnosisList();
        SomHiInvyBasInfo somHiInvyBasInfo = settleListValidateVo.getSomHiInvyBasInfo();
        if (SettleValidateUtil.isEmpty(somHiInvyBasInfo)) {
            return null;
        }
        Map<String,Object> doctormap = (Map<String,Object>)(setlValidBaseBusiVo.getSettleListParams().get(SettleListValidateUtil.KEY_DOCTOR_NAME));
        Map<String,Object> doctorNameMap = (Map<String,Object>)doctormap.get(somHiInvyBasInfo.getHospitalId()+SettleListValidateUtil.KEY_DOCTOR_NAME);
        Map<String, Object> nurseMap = (Map<String, Object>) (setlValidBaseBusiVo.getSettleListParams().get(SettleListValidateUtil.KEY_NURSE_NAME));
        //获取护士名称
        Map<String, String> nurseNameMap = (Map<String, String>) nurseMap.get(somHiInvyBasInfo.getHospitalId() + SettleListValidateUtil.KEY_NURSE_NAME);
        if(!SettleValidateUtil.isEmpty(nurseNameMap) && !SettleValidateUtil.isEmpty(doctorNameMap))    {
            if (!SettleValidateUtil.isEmpty(busOperateDiagnosisList)) {
                for (int i = 0; i < busOperateDiagnosisList.size(); i++) {
                    SomOprnOprtInfo somOprnOprtInfo = (SomOprnOprtInfo) busOperateDiagnosisList.get(i);
                    String operName = somOprnOprtInfo.getOprn_oprt_oper_name();
                    String operCode = somOprnOprtInfo.getC39c();
                    if (SettleValidateUtil.isEmpty(operName)) {
                        addErrorDetail(SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_ERROR_TYPE_1,
                                MAIN_CHECK_FIELD,
                                "第" + (i + 1) + "个手术操作：编码为["+somOprnOprtInfo.getC35c()+"]的" +MAIN_CHECK_NAME + "[" + operName + "]为空",
                                SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
                    }else{
                        String doctorName= (String)doctorNameMap.get(operCode);
                        String nurseName= (String)nurseNameMap.get(operCode);
                        if(!SettleValidateUtil.isEmpty(doctorName) || !SettleValidateUtil.isEmpty(nurseName) ){
                            if(!operName.equals(doctorName) && !operName.equals(nurseName) ){

                                addErrorDetail( SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_ERROR_TYPE_2,
                                        MAIN_CHECK_FIELD,
                                        "第" + (i + 1) + "个手术操作：编码为["+somOprnOprtInfo.getC35c()+"]的" + MAIN_CHECK_NAME + "[" + operName + "]与该编码[" + operCode + "]对应的医院医生名称[" + doctorName + "]不匹配",

                                        SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
                            }
                        }
                    }
                }
            }

        }else{
            if(SettleValidateUtil.isEmpty(doctorNameMap) ){
                addErrorDetail(SettleValidateConst.VALIDATE_TYPE_2, SettleValidateConst.VALIDATE_ERROR_TYPE_1,
                        MAIN_CHECK_FIELD,
                        "未获取到医保医师映射名称信息",
                        SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
            }
            if(SettleValidateUtil.isEmpty(nurseNameMap) ){
                addErrorDetail(SettleValidateConst.VALIDATE_TYPE_2, SettleValidateConst.VALIDATE_ERROR_TYPE_1,
                        MAIN_CHECK_FIELD,
                        "未获取到医保护士映射名称信息",
                        SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
            }
        }
        return null;
    }

    private void addErrorDetail(String index,String errType,String errorFields, String errDscr, String chkStas, SetlValidBaseBusiVo baseBusiVo) {
        SettleListHandlerVo handlerVo = new SettleListHandlerVo();
        handlerVo.setIndex(index);
        handlerVo.setErrorFields(errorFields);
        handlerVo.setErrDscr(errDscr);
        handlerVo.setChkStas(chkStas);
        handlerVo.setErrType(errType);
        baseBusiVo.getErrorDetails().add(handlerVo);
    }
}