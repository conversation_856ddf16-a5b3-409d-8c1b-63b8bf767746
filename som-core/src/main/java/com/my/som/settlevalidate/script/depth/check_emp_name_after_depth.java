package com.my.som.settlevalidate.script.depth;

import com.my.som.model.dataHandle.SomHiInvyBasInfo;
import com.my.som.settlevalidate.constant.SettleValidateConst;
import com.my.som.settlevalidate.script.base.check_coner_tel_basic;
import com.my.som.settlevalidate.util.SettleValidateUtil;
import com.my.som.settlevalidate.vo.SetlValidBaseBusiVo;
import com.my.som.vo.dataHandle.SettleListHandlerVo;
import com.my.som.vo.dataHandle.SettleListValidateVo;
import com.yomahub.liteflow.script.ScriptExecuteWrap;
import com.yomahub.liteflow.script.body.JaninoCommonScriptBody;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

/**
 * 职工医保时 单位名称不为空
 */
public class check_emp_name_after_depth implements JaninoCommonScriptBody {
    private static final Logger logger = LoggerFactory.getLogger(check_emp_name_after_depth.class);

    private static final String MAIN_CHECK_FIELD = "a29n";
    private static final String MAIN_CHECK_NAME  = "工作单位名称";
    private static final String MAIN_CHECK_FIELD_1 = "a54";
    private static final String MAIN_CHECK_NAME_1  = "医保类型";
    private static final String EMPLOYEE_MEDICARE_CODE  = "310";
    private static final String MAIN_CHECK_FIELD_2 = "a29n";
    private static final String MAIN_CHECK_NAME_2  = "工作单位名称";
    private static final String MAIN_CHECK_REGION = "四川";
    /**
     * 事后校验
     *
     * 判断是否为职工医疗保险
     * 如果是：
     * 判断就诊前的工作单位是否为空(emp_name)
     * @param wrap
     * @return
     */
    @Override
    public Void body(ScriptExecuteWrap wrap) {
        SetlValidBaseBusiVo setlValidBaseBusiVo = (SetlValidBaseBusiVo) wrap.cmp.getFirstContextBean();
        SettleListValidateVo settleListValidateVo = (SettleListValidateVo) wrap.cmp.getRequestData();
        Map<String, String> keysBasicResultMap = setlValidBaseBusiVo.getKeysBasicResultMap();
        SomHiInvyBasInfo somHiInvyBasInfo = settleListValidateVo.getSomHiInvyBasInfo();
        SettleListHandlerVo settleListHandlerVo = new SettleListHandlerVo();

        // 检查 somHiInvyBasInfo 是否为空
        if (SettleValidateUtil.isEmpty(somHiInvyBasInfo)) {
            SettleValidateUtil.addErrorDetail(settleListHandlerVo, SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY,SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY,
                    (String)SettleValidateConst.CHECK_TYPE_MAP.get(SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY),null,
                    SettleValidateConst.VALIDATE_STATE_FAIL, setlValidBaseBusiVo);
            return null;
        }

        //获取医保类型质检结果
        String mdeic_type_stas = (String)keysBasicResultMap.get(MAIN_CHECK_FIELD_1) ;
        //获取工作单位名称质检结果
        String emp_name_stas = (String)keysBasicResultMap.get(MAIN_CHECK_FIELD_2) ;

        if(SettleValidateConst.PASS.equals(mdeic_type_stas)){
            // 获取医保类型
            String a54 = somHiInvyBasInfo.getA54();
            if(EMPLOYEE_MEDICARE_CODE.equals(a54)){
                //判断当医保类型为职工医保时，工作单位不能为空
                String a29n = somHiInvyBasInfo.getA29n();
                if(SettleValidateConst.NULL.equals(emp_name_stas)){
                    SettleValidateUtil.addErrorDetail(settleListHandlerVo, SettleValidateConst.VALIDATE_TYPE_2,SettleValidateConst.VALIDATE_TYPE_1,
                            MAIN_CHECK_NAME_2 + "[" + a29n + "]为空", MAIN_CHECK_FIELD_2,
                            SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
                }
            }
        }
        return null;
    }
}
