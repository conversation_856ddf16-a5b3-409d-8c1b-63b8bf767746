package com.my.som.settlevalidate.script.base;

import com.my.som.model.dataHandle.SomHiInvyBasInfo;
import com.my.som.settlevalidate.constant.SettleValidateConst;
import com.my.som.settlevalidate.util.SettleValidateUtil;
import com.my.som.settlevalidate.vo.SetlValidBaseBusiVo;
import com.my.som.vo.dataHandle.SettleListHandlerVo;
import com.my.som.vo.dataHandle.SettleListValidateVo;
import com.yomahub.liteflow.script.ScriptExecuteWrap;
import com.yomahub.liteflow.script.body.JaninoCommonScriptBody;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.Map;

/**
 * 出院时间
 */
public class check_dscg_time_basic implements JaninoCommonScriptBody {
    private static final Logger logger = LoggerFactory.getLogger(check_dscg_time_basic.class);

    private static final String MAIN_CHECK_FIELD = "b15";
    private static final String MAIN_CHECK_NAME = "出院时间";
    private static final String MAIN_CHECK_REGION = "四川,湖北";
    /**
     * 出院时间(dscg_time)基础质控
     * 判断出院时间是否为空
     * @param wrap
     * @return
     */
    @Override
    public Void body(ScriptExecuteWrap wrap) {
        SetlValidBaseBusiVo setlValidBaseBusiVo = (SetlValidBaseBusiVo) wrap.cmp.getFirstContextBean();
        SettleListValidateVo settleListValidateVo = (SettleListValidateVo) wrap.cmp.getRequestData();
        Map<String, String> keysBasicResultMap = setlValidBaseBusiVo.getKeysBasicResultMap();
        SomHiInvyBasInfo somHiInvyBasInfo = settleListValidateVo.getSomHiInvyBasInfo();
        SettleListHandlerVo settleListHandlerVo = new SettleListHandlerVo();

        // 检查 somHiInvyBasInfo 是否为空
        if (SettleValidateUtil.isEmpty(somHiInvyBasInfo)) {
            addErrorDetail(settleListHandlerVo, SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY,SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY,
                    MAIN_CHECK_FIELD,
                    (String)SettleValidateConst.CHECK_TYPE_MAP.get(SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY),
                    SettleValidateConst.VALIDATE_STATE_FAIL, setlValidBaseBusiVo);
            return null;
        }

        // 获取出院时间
        String b15 = somHiInvyBasInfo.getB15();
        if (SettleValidateUtil.isEmpty(b15)) {
            addErrorDetail(settleListHandlerVo, SettleValidateConst.VALIDATE_TYPE_1,SettleValidateConst.VALIDATE_ERROR_TYPE_4,
                    MAIN_CHECK_FIELD,
                    MAIN_CHECK_NAME + "[" + b15 + "]为空",
                    SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
            keysBasicResultMap.put(MAIN_CHECK_FIELD, SettleValidateConst.NULL);
        } else {
            if (!isValidDateTime(b15)) {
                addErrorDetail(settleListHandlerVo, SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_ERROR_TYPE_2,
                        MAIN_CHECK_FIELD,MAIN_CHECK_NAME + "[" + b15 + "]时间格式错误",
                        SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
                keysBasicResultMap.put(MAIN_CHECK_FIELD, SettleValidateConst.OUTLIER);
            } else {
                keysBasicResultMap.put(MAIN_CHECK_FIELD, SettleValidateConst.PASS);
            }
        }
        return null;
    }

    public static boolean isValidDateTime(String dateTimeStr) {
        String format =  "yyyy-MM-dd HH:mm:ss";
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(format);
        try {
            LocalDateTime.parse(dateTimeStr, formatter);
            return true;
        } catch (DateTimeParseException e) {
            return false;
        }
    }

    private void addErrorDetail(SettleListHandlerVo handlerVo, String index,String errType,String errorFields, String errDscr, String chkStas, SetlValidBaseBusiVo baseBusiVo) {
        handlerVo.setIndex(index);
        handlerVo.setErrDscr(errDscr);
        handlerVo.setErrorFields(errorFields);
        handlerVo.setChkStas(chkStas);
        handlerVo.setErrType(errType);
        baseBusiVo.getErrorDetails().add(handlerVo);
    }
}
