package com.my.som.settlevalidate.script.base;

import com.my.som.model.dataHandle.SomHiInvyBasInfo;
import com.my.som.settlevalidate.constant.SettleValidateConst;
import com.my.som.settlevalidate.util.SettleValidateUtil;
import com.my.som.settlevalidate.vo.SetlValidBaseBusiVo;
import com.my.som.vo.dataHandle.SettleListHandlerVo;
import com.my.som.vo.dataHandle.SettleListValidateVo;
import com.yomahub.liteflow.script.ScriptExecuteWrap;
import com.yomahub.liteflow.script.body.JaninoCommonScriptBody;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

public class check_coner_addr_basic implements Jan<PERSON>CommonScriptBody {
    private static final Logger logger = LoggerFactory.getLogger(check_coner_addr_basic.class);

    private static final String MAIN_CHECK_FIELD = "a34";
    private static final String MAIN_CHECK_NAME = "联系人地址";
    private static final String MAIN_CHECK_REGION = "四川";
    /**
     * 联系人地址(coner_addr)基础质控
     * 判断联系人地址是否为空
     * @param wrap
     * @return
     */
    @Override
    public Void body(ScriptExecuteWrap wrap) {
        SetlValidBaseBusiVo setlValidBaseBusiVo = (SetlValidBaseBusiVo) wrap.cmp.getFirstContextBean();
        SettleListValidateVo settleListValidateVo = (SettleListValidateVo) wrap.cmp.getRequestData();
        Map<String, String> keysBasicResultMap = setlValidBaseBusiVo.getKeysBasicResultMap();
        SomHiInvyBasInfo somHiInvyBasInfo = settleListValidateVo.getSomHiInvyBasInfo();
        SettleListHandlerVo settleListHandlerVo = new SettleListHandlerVo();

        // 检查 somHiInvyBasInfo 是否为空
        if (SettleValidateUtil.isEmpty(somHiInvyBasInfo)) {
            addErrorDetail(settleListHandlerVo, SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY,SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY,
                    MAIN_CHECK_FIELD,
                    (String)SettleValidateConst.CHECK_TYPE_MAP.get(SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY),
                    SettleValidateConst.VALIDATE_STATE_FAIL, setlValidBaseBusiVo);
            return null;
        }

        // 获取联系人地址
        String a34 = somHiInvyBasInfo.getA34();
        if (SettleValidateUtil.isEmpty(a34)) {
            addErrorDetail(settleListHandlerVo, SettleValidateConst.VALIDATE_TYPE_1,SettleValidateConst.VALIDATE_TYPE_1,
                    MAIN_CHECK_FIELD,
                    MAIN_CHECK_NAME + "[" + a34 + "]为空",
                    SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
            keysBasicResultMap.put(MAIN_CHECK_FIELD, SettleValidateConst.NULL);
        } else {
            keysBasicResultMap.put(MAIN_CHECK_FIELD, SettleValidateConst.PASS);
        }

        return null;
    }

    private void addErrorDetail(SettleListHandlerVo handlerVo, String index,String errType,String errorFields, String errDscr, String chkStas, SetlValidBaseBusiVo baseBusiVo) {
        handlerVo.setIndex(index);
        handlerVo.setErrDscr(errDscr);
        handlerVo.setErrorFields(errorFields);
        handlerVo.setChkStas(chkStas);
        handlerVo.setErrType(errType);
        baseBusiVo.getErrorDetails().add(handlerVo);
    }
}
