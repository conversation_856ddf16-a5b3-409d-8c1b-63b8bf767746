package com.my.som.settlevalidate.script.depth;

import com.my.som.model.dataHandle.SomHiInvyBasInfo;
import com.my.som.settlevalidate.constant.SettleValidateConst;
import com.my.som.settlevalidate.util.SettleValidateUtil;
import com.my.som.settlevalidate.vo.SetlValidBaseBusiVo;
import com.my.som.vo.dataHandle.SettleListHandlerVo;
import com.my.som.vo.dataHandle.SettleListValidateVo;
import com.yomahub.liteflow.script.ScriptExecuteWrap;
import com.yomahub.liteflow.script.body.JaninoCommonScriptBody;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Map;

public class check_dscg_time_depth implements JaninoCommonScriptBody {
    private static final Logger logger = LoggerFactory.getLogger(check_dscg_time_depth.class);
    private static final String MAIN_CHECK_FIELD = "b15";
    private static final String MAIN_CHECK_NAME = "出院时间";
    private static final String MAIN_CHECK_FIELD_1 = "b12";
    private static final String MAIN_CHECK_NAME_1 = "入院时间";
    private static final String MAIN_CHECK_FIELD_2 = "b15";
    private static final String MAIN_CHECK_NAME_2 = "出院时间";
    private static final String MAIN_CHECK_REGION = "四川,湖北";
    /**
     * 出院时间(dscg_time)深度质控
     * 1.判断入院时间是否小于出院时间 若否则不通过
     * @param wrap
     * @return
     */
    @Override
    public Void body(ScriptExecuteWrap wrap) {
        SetlValidBaseBusiVo setlValidBaseBusiVo = (SetlValidBaseBusiVo) wrap.cmp.getFirstContextBean();
        SettleListValidateVo settleListValidateVo = (SettleListValidateVo) wrap.cmp.getRequestData();
        Map<String, String> keysBasicResultMap = setlValidBaseBusiVo.getKeysBasicResultMap();
        SomHiInvyBasInfo somHiInvyBasInfo = settleListValidateVo.getSomHiInvyBasInfo();
        SettleListHandlerVo settleListHandlerVo = new SettleListHandlerVo();

        // 检查 somHiInvyBasInfo 是否为空
        if (SettleValidateUtil.isEmpty(somHiInvyBasInfo)) {
            addErrorDetail(settleListHandlerVo, SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY,SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY,
                    MAIN_CHECK_FIELD_2,
                    (String)SettleValidateConst.CHECK_TYPE_MAP.get(SettleValidateConst.VALIDATE_INFO_TYPE_BASE_EMPTY),
                    SettleValidateConst.VALIDATE_STATE_FAIL, setlValidBaseBusiVo);
            return null;
        }

        // 获取入院时间校验结果
        String b12_stas = (String) keysBasicResultMap.get(MAIN_CHECK_FIELD_1);

        // 获取出院时间校验结果
        String b15_stas = (String) keysBasicResultMap.get(MAIN_CHECK_FIELD_2);

        if(SettleValidateConst.PASS.equals(b12_stas) && SettleValidateConst.PASS.equals(b15_stas)){
            // 入院时间、出院时间基础质控通过 进一步判断入院时间合理性
            // 获取入院时间
            String b12 = somHiInvyBasInfo.getB12();
            // 获取出院时间
            String b15 = somHiInvyBasInfo.getB15();
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            try{
                Date admDate = dateFormat.parse(b12);
                Date dscgDate = dateFormat.parse(b15);
                // 入院时间大于出院时间
                if (admDate.after(dscgDate)) {
                    addErrorDetail(settleListHandlerVo, SettleValidateConst.VALIDATE_TYPE_2,SettleValidateConst.VALIDATE_ERROR_TYPE_304,
                            MAIN_CHECK_FIELD_2,
                            MAIN_CHECK_NAME_2 + "[" + b15 + "]小于"+MAIN_CHECK_NAME_1 + "[" + b12 + "]",
                            SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
                }
            }catch (ParseException e){
                logger.error("日期解析失败: " + e.getMessage());
            }
        }

        return null;
    }

    private void addErrorDetail(SettleListHandlerVo handlerVo, String index,String errType,String errorFields, String errDscr, String chkStas, SetlValidBaseBusiVo baseBusiVo) {
        handlerVo.setIndex(index);
        handlerVo.setErrDscr(errDscr);
        handlerVo.setErrorFields(errorFields);
        handlerVo.setChkStas(chkStas);
        handlerVo.setErrType(errType);
        baseBusiVo.getErrorDetails().add(handlerVo);
    }
}
