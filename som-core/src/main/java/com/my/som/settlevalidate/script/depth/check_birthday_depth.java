package com.my.som.settlevalidate.script.depth;

import com.my.som.model.dataHandle.SomHiInvyBasInfo;
import com.my.som.settlevalidate.constant.SettleValidateConst;
import com.my.som.settlevalidate.util.SettleValidateUtil;
import com.my.som.settlevalidate.vo.SetlValidBaseBusiVo;
import com.my.som.vo.dataHandle.SettleListHandlerVo;
import com.my.som.vo.dataHandle.SettleListValidateVo;
import com.yomahub.liteflow.script.ScriptExecuteWrap;
import com.yomahub.liteflow.script.body.JaninoCommonScriptBody;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.Period;
import java.util.Date;
import java.util.Locale;
import java.util.Map;

/**
 *
 */
public class check_birthday_depth implements JaninoCommonScriptBody {
    private static final Logger logger = LoggerFactory.getLogger(check_birthday_depth.class);

    private static final String MAIN_CHECK_FIELD = "a13";
    private static final String MAIN_CHECK_NAME = "出生日期";
    private static final String MAIN_CHECK_FIELD_1 = "b12";
    private static final String MAIN_CHECK_NAME_1 = "入院时间";
    private static final String MAIN_CHECK_FIELD_2 = "b15";
    private static final String MAIN_CHECK_FIELD_3 = "a14";
    private static final String MAIN_CHECK_REGION = "湖北";

    @Override
    public Void body(ScriptExecuteWrap wrap) {
        SetlValidBaseBusiVo setlValidBaseBusiVo = (SetlValidBaseBusiVo) wrap.cmp.getFirstContextBean();
        SettleListValidateVo settleListValidateVo = (SettleListValidateVo) wrap.cmp.getRequestData();
        Map<String, String> keysBasicResultMap = setlValidBaseBusiVo.getKeysBasicResultMap();
        SomHiInvyBasInfo somHiInvyBasInfo = settleListValidateVo.getSomHiInvyBasInfo();
        SettleListHandlerVo settleListHandlerVo = new SettleListHandlerVo();
        if (SettleValidateUtil.isEmpty(somHiInvyBasInfo)) {
            return null;
        }

        String a13_stas = (String) keysBasicResultMap.get(MAIN_CHECK_FIELD);
        String b12_stas = (String) keysBasicResultMap.get(MAIN_CHECK_FIELD_1);
        String b15_stas = (String) keysBasicResultMap.get(MAIN_CHECK_FIELD_2);
        String a14_stas = (String) keysBasicResultMap.get(MAIN_CHECK_FIELD_1);

        if (SettleValidateConst.PASS.equals(a13_stas)) {
            String a13 = somHiInvyBasInfo.getA13();
            //判断 出生日期的格式
            if (SettleValidateConst.PASS.equals(b12_stas) && SettleValidateConst.PASS.equals(b15_stas)) {
                String b12 = somHiInvyBasInfo.getB12();
                String b15 = somHiInvyBasInfo.getB15();
                //判断出生日期大于入院时间
                if (judgeTimeOK(b12, a13)) {
                    addErrorDetail(SettleValidateConst.VALIDATE_TYPE_2, SettleValidateConst.VALIDATE_TYPE_2,
                            MAIN_CHECK_FIELD,
                            MAIN_CHECK_NAME + "[" + a13 + "] 应该小于入院时间[" + b12 + "]",
                            SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
                }
                //判断出院时间大于出生时间
                if (judgeTimeOK(b15, a13)) {
                    addErrorDetail(SettleValidateConst.VALIDATE_TYPE_2, SettleValidateConst.VALIDATE_TYPE_2,
                            MAIN_CHECK_FIELD,
                            MAIN_CHECK_NAME + "[" + a13 + "] 应该小于出院时间[" + b15 + "]",
                            SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
                }
                if (SettleValidateConst.PASS.equals(a14_stas)) {
                    Integer a14 = somHiInvyBasInfo.getA14();
                    int diff = getDateDifferenceInDays(a13, b12, a14);
                    if (diff > 1 || diff < 0) {
                        addErrorDetail(SettleValidateConst.VALIDATE_TYPE_1, SettleValidateConst.VALIDATE_ERROR_TYPE_2,
                                MAIN_CHECK_FIELD,
                                "年龄[" + a14 + "] - (入院日期[" + b12 + "] - 出生日期[" + a13 + "])应小于一岁,且不为负数",
                                SettleValidateConst.VALIDATE_STATE_SUCCESS, setlValidBaseBusiVo);
                    }
                }
            }
        }
        return null;
    }

    public int getDateDifferenceInDays(String date1Str, String date2Str, Integer a14) {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd", Locale.ENGLISH);
        SimpleDateFormat dateTimeFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.ENGLISH);
        LocalDate birthLocalDate = null;
        LocalDate currentLocalDate = null;
        try {
            // 将第一个日期字符串解析为日期
            Date birthDate = dateFormat.parse(date1Str);
            birthLocalDate = new java.sql.Date(birthDate.getTime()).toLocalDate();

            // 将第二个日期字符串解析为日期时间
            Date currentDateTime = dateTimeFormat.parse(date2Str);
            currentLocalDate = new java.sql.Date(currentDateTime.getTime()).toLocalDate();
        } catch (ParseException e) {
            throw new RuntimeException("Error parsing dates: " + e.getMessage(), e);
        }
        // 返回 a14 减去年龄（以年为单位）
        return a14 - Period.between(birthLocalDate, currentLocalDate).getYears();
    }

    /**
     * 时间2 大于  时间1  返回 true
     *
     * @param time1 时间1
     * @param time2 时间2
     * @return
     */
    private boolean judgeTimeOK(String time1, String time2) {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        boolean flag = false;
        try {
            Date admDate = dateFormat.parse(time1);
            Date dscgDate = dateFormat.parse(time2);
            // dscgDate大于admDate
            if (dscgDate.after(admDate)) {
                flag = true;
            }
        } catch (ParseException e) {
            logger.error("手术时间质检 日期解析失败: " + e.getMessage());
        }
        return flag;
    }


    private void addErrorDetail(String index, String errType, String errorFields, String errDscr, String chkStas, SetlValidBaseBusiVo baseBusiVo) {
        SettleListHandlerVo handlerVo = new SettleListHandlerVo();
        handlerVo.setIndex(index);
        handlerVo.setErrDscr(errDscr);
        handlerVo.setErrorFields(errorFields);
        handlerVo.setChkStas(chkStas);
        handlerVo.setErrType(errType);
        baseBusiVo.getErrorDetails().add(handlerVo);
    }
}
