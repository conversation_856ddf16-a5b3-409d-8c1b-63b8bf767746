package com.my.som.dts.service.impl;

import com.my.som.dts.entity.param.Hcm9001Param;
import com.my.som.dts.entity.vo.PreGroupVo;
import com.my.som.dts.service.SettleListManageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 结算清单管理服务实现类
 */
@Slf4j
@Service
public class SettleListManageServiceImpl implements SettleListManageService {

    @Override
    public PreGroupVo getSimlatePreGroup(Hcm9001Param hcm9001Param, 
                                        HttpServletRequest request, 
                                        HttpServletResponse response) {
        
        log.info("开始执行模拟预分组，患者信息: {}", 
                hcm9001Param != null && hcm9001Param.getData() != null 
                && hcm9001Param.getData().getPatient_dtos() != null 
                ? hcm9001Param.getData().getPatient_dtos().getPatn_name() : "未知");

        try {
            // 1. 参数验证
            if (!validateInput(hcm9001Param)) {
                return PreGroupVo.failed("输入参数不完整");
            }

            // 2. 获取患者基本信息
            Hcm9001Param.Patient_dtos patientDtos = hcm9001Param.getData().getPatient_dtos();
            Hcm9001Param.Fsi_encounter_dtos encounterDtos = patientDtos.getFsi_encounter_dtos();

            // 3. 执行预分组逻辑
            PreGroupVo result = executePreGrouping(encounterDtos);

            // 4. 设置通用信息
            result.setGroupTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            result.setGroupVersion("1.0");

            log.info("模拟预分组完成，DRG编码: {}, DIP编码: {}", result.getDrgCodg(), result.getDipCodg());
            return result;

        } catch (Exception e) {
            log.error("模拟预分组执行异常", e);
            return PreGroupVo.failed("预分组执行异常: " + e.getMessage());
        }
    }

    /**
     * 验证输入参数
     */
    private boolean validateInput(Hcm9001Param hcm9001Param) {
        if (hcm9001Param == null || hcm9001Param.getData() == null) {
            return false;
        }

        Hcm9001Param.data data = hcm9001Param.getData();
        if (data.getPatient_dtos() == null) {
            return false;
        }

        Hcm9001Param.Patient_dtos patientDtos = data.getPatient_dtos();
        if (patientDtos.getFsi_encounter_dtos() == null) {
            return false;
        }

        return true;
    }

    /**
     * 执行预分组逻辑
     */
    private PreGroupVo executePreGrouping(Hcm9001Param.Fsi_encounter_dtos encounterDtos) {
        PreGroupVo result = new PreGroupVo();

        // 根据医疗类型判断分组方式
        String medType = encounterDtos.getMed_type();
        
        if ("1".equals(medType) || "11".equals(medType)) {
            // 住院 - 执行DRG分组
            executeDrgGrouping(encounterDtos, result);
        } else {
            // 门诊 - 执行DIP分组  
            executeDipGrouping(encounterDtos, result);
        }

        return result;
    }

    /**
     * 执行DRG分组
     */
    private void executeDrgGrouping(Hcm9001Param.Fsi_encounter_dtos encounterDtos, PreGroupVo result) {
        try {
            // 模拟DRG分组逻辑
            String mainDiagCode = encounterDtos.getDscg_main_dise_codg();
            
            if (StringUtils.hasText(mainDiagCode)) {
                // 根据主诊断编码模拟分组
                if (mainDiagCode.startsWith("I21")) {
                    // 急性心肌梗死
                    result.setDrgCodg("DRG_CV01");
                    result.setDrgName("急性心肌梗死");
                    result.setGroupWeight(new BigDecimal("2.5"));
                    result.setPaymentStandard(new BigDecimal("25000.00"));
                } else if (mainDiagCode.startsWith("J44")) {
                    // 慢性阻塞性肺疾病
                    result.setDrgCodg("DRG_RE01");
                    result.setDrgName("慢性阻塞性肺疾病");
                    result.setGroupWeight(new BigDecimal("1.8"));
                    result.setPaymentStandard(new BigDecimal("18000.00"));
                } else {
                    // 其他诊断
                    result.setDrgCodg("DRG_OT01");
                    result.setDrgName("其他疾病");
                    result.setGroupWeight(new BigDecimal("1.0"));
                    result.setPaymentStandard(new BigDecimal("10000.00"));
                }
                
                result.setGroupType("DRG");
                result.setIsGrouped(true);
                result.setGroupStatus("SUCCESS");
                result.setGroupMessage("DRG分组成功");
            } else {
                result.setIsGrouped(false);
                result.setGroupStatus("FAILED");
                result.setErrorMessage("缺少主诊断信息");
            }

        } catch (Exception e) {
            log.error("DRG分组异常", e);
            result.setIsGrouped(false);
            result.setGroupStatus("ERROR");
            result.setErrorMessage("DRG分组异常: " + e.getMessage());
        }
    }

    /**
     * 执行DIP分组
     */
    private void executeDipGrouping(Hcm9001Param.Fsi_encounter_dtos encounterDtos, PreGroupVo result) {
        try {
            // 模拟DIP分组逻辑
            String mainDiagCode = encounterDtos.getDscg_main_dise_codg();
            
            if (StringUtils.hasText(mainDiagCode)) {
                // 根据主诊断编码模拟分组
                if (mainDiagCode.startsWith("H25")) {
                    // 白内障
                    result.setDipCodg("DIP_EY01");
                    result.setDipName("白内障");
                    result.setGroupWeight(new BigDecimal("0.8"));
                    result.setPaymentStandard(new BigDecimal("3000.00"));
                } else if (mainDiagCode.startsWith("K80")) {
                    // 胆石症
                    result.setDipCodg("DIP_DI01");
                    result.setDipName("胆石症");
                    result.setGroupWeight(new BigDecimal("1.2"));
                    result.setPaymentStandard(new BigDecimal("5000.00"));
                } else {
                    // 其他诊断
                    result.setDipCodg("DIP_OT01");
                    result.setDipName("其他门诊疾病");
                    result.setGroupWeight(new BigDecimal("0.5"));
                    result.setPaymentStandard(new BigDecimal("1000.00"));
                }
                
                result.setGroupType("DIP");
                result.setIsGrouped(true);
                result.setGroupStatus("SUCCESS");
                result.setGroupMessage("DIP分组成功");
            } else {
                result.setIsGrouped(false);
                result.setGroupStatus("FAILED");
                result.setErrorMessage("缺少主诊断信息");
            }

        } catch (Exception e) {
            log.error("DIP分组异常", e);
            result.setIsGrouped(false);
            result.setGroupStatus("ERROR");
            result.setErrorMessage("DIP分组异常: " + e.getMessage());
        }
    }
}
