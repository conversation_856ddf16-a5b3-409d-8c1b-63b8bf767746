package com.my.som.dts.entity.param;

import lombok.Data;

@Data
public class Som9001Param {

    public static class baseinfo{

        private String fixmedins_code;

        private String medfee_paymtd_code;

        private String patn_ipt_cnt;

        private String medcasno;

        private String psn_name;

        private String gend;

        private String brdy;

        private String age;

        private String ntly;

        private String nwb_age;

        private String nwb_bir_wt;

        private String nwb_adm_wt;

        private String birplc;

        private String napl;

        private String naty;

        private String certno;

        private String prfs;

        private String mrg_stas;

        private String curr_addr_poscode;

        private String curr_addr;

        private String psn_tel;

        private String resd_addr;

        private String resd_addr_poscode;

        private String empr_tel;

        private String empr_poscode;

        private String empr_addr;

        private String coner_tel;

        private String coner_name;

        private String coner_addr;

        private String coner_rlts_code;

        private String adm_way_code;

        private String adm_date;

        private String adm_caty;

        private String adm_ward;

        private String refldept_dept;

        private String dscg_date;

        private String dscg_caty;

        private String dscg_ward;

        private String ipt_days;

        private String otp_wm_dise;

        private String wm_dise_code;

        private String damg_intx_ext_rea;

        private String damg_intx_ext_rea_disecode;

        private String palg_no;

        private String drug_dicm_flag;

        private String dicm_drug_name;

        private String die_autp_flag;

        private String abo_code;

        private String rh_code;

        private String deptdrt_code;

        private String chfdr_code;

        private String atddr_code;

        private String ipt_dr_code;

        private String resp_nurs_code;

        private String train_dr_code;

        private String intn_dr_code;

        private String codr_code;

        private String qltctrl_dr_code;

        private String qltctrl_nurs_code;

        private String medcas_qlt_code;

        private String qltctrl_date;

        private String dscg_way;

        private String acp_medins_code;

        private String acp_medins_name;

        private String dscg_31days_rinp_flag;

        private String dscg_31days_rinp_pup;

        private String brn_damg_bfadm_coma_dura;

        private String brn_damg_afadm_coma_dura;

        private String vent_used_dura;

        private String medfee_sumamt;
    }

}
