package com.my.som.dts.controller;


import com.alibaba.fastjson.JSON;
import com.my.som.util.dataTransform.DtsRequestUtil;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

@RestController
@RequestMapping("/dts")
public class Test {

    @RequestMapping("/test")
    public String test() {

        return  JSON.toJSONString(DtsRequestUtil.doPost("{\"infno\": \"mysql\"}"));

    }

}
