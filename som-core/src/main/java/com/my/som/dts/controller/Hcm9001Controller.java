package com.my.som.dts.controller;


import com.my.som.common.exception.AppException;
import com.my.som.dts.entity.param.CommonParam;
import com.my.som.dts.entity.param.Hcm9001Param;
import com.my.som.dts.entity.po.Hcm9001Vo;
import com.my.som.dts.service.SelfExamCorrectionService;
import com.my.som.service.hcs.RunHcsDataProcessService;
import com.my.som.service.hcs.impl.RunHcsDataProcessServiceImpl;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

@Controller
@RequestMapping("/dts/api")
public class Hcm9001Controller {

    @Resource
    private SelfExamCorrectionService selfExamCorrectionService;

    @Resource
    private RunHcsDataProcessService runHcsDataProcessService;

    @RequestMapping(value = "/hcm9001")
    @ResponseBody
    public ResponseEntity hcm9001(@RequestBody @Valid CommonParam<Hcm9001Param> param) {
        Hcm9001Vo hcm9001Vo = selfExamCorrectionService.SelfExamCorrectionRemind(param.getInput());
        int i = runHcsDataProcessService.runProcSingle(param);
        return ResponseEntity.ok(hcm9001Vo);
    }


    @GetMapping("/hcm9001/hcmAuditInfo")
    public String testSelfExam(@RequestParam String hisid,
                               @RequestParam String type,
                               @RequestParam String token,
                               Model model) {
        //TODO token验证
        model.addAttribute("pageTitle", "医院智能审核管理系统");
        model.addAttribute("systemTitle", "医院智能审核管理系统");

        try {
            setRealData(model, hisid, type);
        } catch (Exception e) {
            throw new AppException("查询数据失败: " + e.getMessage());
        }

        return "selfExam";
    }

    private void setRealData(Model model, String hisid, String type) {
        model.addAttribute("userInfo", selfExamCorrectionService.getUserInfo(hisid, type));

        model.addAttribute("violationTable", selfExamCorrectionService.getViolationTable(hisid, type));

        model.addAttribute("rightPanel", selfExamCorrectionService.getRightsTable(hisid, type));

        model.addAttribute("detailSection", selfExamCorrectionService.getDetailTable(hisid, type));

    }

    private void setDefaultData(Model model) {
        // 设置默认的空数据，确保模板不会出错
        model.addAttribute("userInfo", null);
        model.addAttribute("violationTable", null);
        model.addAttribute("rightPanel", null);
        model.addAttribute("detailSection", null);
    }


}
