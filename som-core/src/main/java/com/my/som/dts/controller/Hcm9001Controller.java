package com.my.som.dts.controller;


import com.my.som.common.exception.AppException;
import com.my.som.common.result.CommonResult;
import com.my.som.dts.entity.param.CommonParam;
import com.my.som.dts.entity.param.Hcm9001Param;
import com.my.som.dts.entity.po.Hcm9001Vo;
import com.my.som.dts.entity.vo.PreGroupVo;
import com.my.som.dts.service.SelfExamCorrectionService;
import com.my.som.dts.service.SettleListManageService;
import com.my.som.service.hcs.RunHcsDataProcessService;
import com.my.som.service.hcs.impl.RunHcsDataProcessServiceImpl;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

@Controller
@RequestMapping("/dts/api")
public class Hcm9001Controller {

    @Resource
    private SelfExamCorrectionService selfExamCorrectionService;

    @Resource
    private RunHcsDataProcessService runHcsDataProcessService;

    @Resource
    private SettleListManageService settleListManageService;

    @RequestMapping(value = "/hcm9001")
    @ResponseBody
    public ResponseEntity hcm9001(@RequestBody @Valid CommonParam<Hcm9001Param> param) {
        selfExamCorrectionService.SelfExamCorrectionRemind(param.getInput());
        runHcsDataProcessService.runProcSingle(param);
        return ResponseEntity.ok("success");
    }

    @ApiOperation("查询模拟预分组结果")
    @PostMapping("/getPreGroupResult")
    @ResponseBody
    public CommonResult preGroupResult(@RequestBody Hcm9001Param hcm9001Param,
                                     HttpServletRequest request,
                                     HttpServletResponse response) {
        handleInsuplc(hcm9001Param);
        PreGroupVo groups = settleListManageService.getSimlatePreGroup(hcm9001Param, request, response);
        if ((StringUtils.hasText(groups.getDrgCodg())) || (StringUtils.hasText(groups.getDipCodg()))) {
            return CommonResult.success(groups);
        } else {
            return CommonResult.failed("入组失败,请检查输入信息");
        }
    }


    @GetMapping("/hcm9001/hcmAuditInfo")
    public String testSelfExam(@RequestParam String hisid,
                               @RequestParam String type,
                               @RequestParam String token,
                               Model model) {
        //TODO token验证
        model.addAttribute("pageTitle", "医院智能审核管理系统");
        model.addAttribute("systemTitle", "医院智能审核管理系统");

        try {
            setRealData(model, hisid, type);
        } catch (Exception e) {
            throw new AppException("查询数据失败: " + e.getMessage());
        }

        return "selfExam";
    }

    private void setRealData(Model model, String hisid, String type) {
        model.addAttribute("userInfo", selfExamCorrectionService.getUserInfo(hisid, type));

        model.addAttribute("violationTable", selfExamCorrectionService.getViolationTable(hisid, type));

        model.addAttribute("rightPanel", selfExamCorrectionService.getRightsTable(hisid, type));

        model.addAttribute("detailSection", selfExamCorrectionService.getDetailTable(hisid, type));

    }

    private void setDefaultData(Model model) {
        // 设置默认的空数据，确保模板不会出错
        model.addAttribute("userInfo", null);
        model.addAttribute("violationTable", null);
        model.addAttribute("rightPanel", null);
        model.addAttribute("detailSection", null);
    }

    /**
     * 处理参保地信息
     * @param hcm9001Param 患者信息参数
     */
    private void handleInsuplc(Hcm9001Param hcm9001Param) {
        // 处理参保地相关逻辑
        // 这里可以根据实际业务需求进行参保地信息的处理
        // 例如：验证参保地编码、设置默认参保地等
        if (hcm9001Param != null && hcm9001Param.getData() != null
            && hcm9001Param.getData().getPatient_dtos() != null
            && hcm9001Param.getData().getPatient_dtos().getPoolarea() != null) {
            // 参保地信息处理逻辑
            String poolarea = hcm9001Param.getData().getPatient_dtos().getPoolarea();
            // 可以在这里添加参保地验证、转换等逻辑
        }
    }


}
