package com.my.som.dts.service.impl;

import com.my.som.dts.entity.param.Som9001Param;
import com.my.som.dts.entity.vo.PreGroupVo;
import com.my.som.dts.service.Som9001SettleListManageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * Som9001结算清单管理服务实现类
 */
@Slf4j
@Service
public class Som9001SettleListManageServiceImpl implements Som9001SettleListManageService {

    @Override
    public PreGroupVo getSimlatePreGroup(Som9001Param som9001Param, 
                                        HttpServletRequest request, 
                                        HttpServletResponse response) {
        
        log.info("开始执行Som9001模拟预分组，患者信息: {}", 
                som9001Param != null && som9001Param.getBaseinfo() != null 
                ? som9001Param.getBaseinfo().getPsn_name() : "未知");

        try {
            // 1. 参数验证
            if (!validateInput(som9001Param)) {
                return PreGroupVo.failed("输入参数不完整");
            }

            // 2. 获取患者基本信息
            Som9001Param.baseinfo baseinfo = som9001Param.getBaseinfo();
            Som9001Param.diseinfo diseinfo = som9001Param.getDiseinfo();

            // 3. 执行预分组逻辑
            PreGroupVo result = executePreGrouping(baseinfo, diseinfo);

            // 4. 设置通用信息
            result.setGroupTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            result.setGroupVersion("1.0");

            log.info("Som9001模拟预分组完成，DRG编码: {}, DIP编码: {}", result.getDrgCodg(), result.getDipCodg());
            return result;

        } catch (Exception e) {
            log.error("Som9001模拟预分组执行异常", e);
            return PreGroupVo.failed("预分组执行异常: " + e.getMessage());
        }
    }

    /**
     * 验证输入参数
     */
    private boolean validateInput(Som9001Param som9001Param) {
        if (som9001Param == null) {
            return false;
        }

        if (som9001Param.getBaseinfo() == null) {
            return false;
        }

        if (som9001Param.getDiseinfo() == null) {
            return false;
        }

        return true;
    }

    /**
     * 执行预分组逻辑
     */
    private PreGroupVo executePreGrouping(Som9001Param.baseinfo baseinfo, Som9001Param.diseinfo diseinfo) {
        PreGroupVo result = new PreGroupVo();

        // 根据费用支付方式判断分组方式
        String medfeePaymtdCode = baseinfo.getMedfee_paymtd_code();
        
        // 判断是否为住院（通常住院天数大于0表示住院）
        boolean isInpatient = StringUtils.hasText(baseinfo.getIpt_days()) && 
                             Integer.parseInt(baseinfo.getIpt_days()) > 0;
        
        if (isInpatient) {
            // 住院 - 执行DRG分组
            executeDrgGrouping(diseinfo, baseinfo, result);
        } else {
            // 门诊 - 执行DIP分组  
            executeDipGrouping(diseinfo, baseinfo, result);
        }

        return result;
    }

    /**
     * 执行DRG分组
     */
    private void executeDrgGrouping(Som9001Param.diseinfo diseinfo, Som9001Param.baseinfo baseinfo, PreGroupVo result) {
        try {
            // 模拟DRG分组逻辑
            String diagCode = diseinfo.getDiag_code();
            
            if (StringUtils.hasText(diagCode)) {
                // 根据主诊断编码模拟分组
                if (diagCode.startsWith("I21")) {
                    // 急性心肌梗死
                    result.setDrgCodg("DRG_CV01");
                    result.setDrgName("急性心肌梗死");
                    result.setGroupWeight(new BigDecimal("2.5"));
                    result.setPaymentStandard(new BigDecimal("25000.00"));
                } else if (diagCode.startsWith("J44")) {
                    // 慢性阻塞性肺疾病
                    result.setDrgCodg("DRG_RE01");
                    result.setDrgName("慢性阻塞性肺疾病");
                    result.setGroupWeight(new BigDecimal("1.8"));
                    result.setPaymentStandard(new BigDecimal("18000.00"));
                } else if (diagCode.startsWith("K80")) {
                    // 胆石症
                    result.setDrgCodg("DRG_DI01");
                    result.setDrgName("胆石症");
                    result.setGroupWeight(new BigDecimal("1.5"));
                    result.setPaymentStandard(new BigDecimal("15000.00"));
                } else {
                    // 其他诊断
                    result.setDrgCodg("DRG_OT01");
                    result.setDrgName("其他疾病");
                    result.setGroupWeight(new BigDecimal("1.0"));
                    result.setPaymentStandard(new BigDecimal("10000.00"));
                }
                
                // 根据实际费用调整预估金额
                if (StringUtils.hasText(baseinfo.getMedfee_sumamt())) {
                    result.setEstimatedAmount(new BigDecimal(baseinfo.getMedfee_sumamt()));
                }
                
                result.setGroupType("DRG");
                result.setIsGrouped(true);
                result.setGroupStatus("SUCCESS");
                result.setGroupMessage("DRG分组成功");
            } else {
                result.setIsGrouped(false);
                result.setGroupStatus("FAILED");
                result.setErrorMessage("缺少主诊断信息");
            }

        } catch (Exception e) {
            log.error("DRG分组异常", e);
            result.setIsGrouped(false);
            result.setGroupStatus("ERROR");
            result.setErrorMessage("DRG分组异常: " + e.getMessage());
        }
    }

    /**
     * 执行DIP分组
     */
    private void executeDipGrouping(Som9001Param.diseinfo diseinfo, Som9001Param.baseinfo baseinfo, PreGroupVo result) {
        try {
            // 模拟DIP分组逻辑
            String diagCode = diseinfo.getDiag_code();
            
            if (StringUtils.hasText(diagCode)) {
                // 根据主诊断编码模拟分组
                if (diagCode.startsWith("H25")) {
                    // 白内障
                    result.setDipCodg("DIP_EY01");
                    result.setDipName("白内障");
                    result.setGroupWeight(new BigDecimal("0.8"));
                    result.setPaymentStandard(new BigDecimal("3000.00"));
                } else if (diagCode.startsWith("K80")) {
                    // 胆石症
                    result.setDipCodg("DIP_DI01");
                    result.setDipName("胆石症");
                    result.setGroupWeight(new BigDecimal("1.2"));
                    result.setPaymentStandard(new BigDecimal("5000.00"));
                } else if (diagCode.startsWith("M17")) {
                    // 膝关节病
                    result.setDipCodg("DIP_OR01");
                    result.setDipName("膝关节病");
                    result.setGroupWeight(new BigDecimal("1.0"));
                    result.setPaymentStandard(new BigDecimal("4000.00"));
                } else {
                    // 其他诊断
                    result.setDipCodg("DIP_OT01");
                    result.setDipName("其他门诊疾病");
                    result.setGroupWeight(new BigDecimal("0.5"));
                    result.setPaymentStandard(new BigDecimal("1000.00"));
                }
                
                // 根据实际费用调整预估金额
                if (StringUtils.hasText(baseinfo.getMedfee_sumamt())) {
                    result.setEstimatedAmount(new BigDecimal(baseinfo.getMedfee_sumamt()));
                }
                
                result.setGroupType("DIP");
                result.setIsGrouped(true);
                result.setGroupStatus("SUCCESS");
                result.setGroupMessage("DIP分组成功");
            } else {
                result.setIsGrouped(false);
                result.setGroupStatus("FAILED");
                result.setErrorMessage("缺少主诊断信息");
            }

        } catch (Exception e) {
            log.error("DIP分组异常", e);
            result.setIsGrouped(false);
            result.setGroupStatus("ERROR");
            result.setErrorMessage("DIP分组异常: " + e.getMessage());
        }
    }
}
