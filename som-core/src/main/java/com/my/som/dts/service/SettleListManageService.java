package com.my.som.dts.service;

import com.my.som.dts.entity.param.Hcm9001Param;
import com.my.som.dts.entity.vo.PreGroupVo;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 结算清单管理服务接口
 * 提供预分组相关功能
 */
public interface SettleListManageService {

    /**
     * 获取模拟预分组结果
     * 
     * @param hcm9001Param 患者信息参数
     * @param request HTTP请求
     * @param response HTTP响应
     * @return 预分组结果
     */
    PreGroupVo getSimlatePreGroup(Hcm9001Param hcm9001Param, 
                                 HttpServletRequest request, 
                                 HttpServletResponse response);
}
