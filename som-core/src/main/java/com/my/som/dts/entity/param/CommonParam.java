package com.my.som.dts.entity.param;

import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;

@Data
public class CommonParam<T> {

    /**
     * 交易编号
     */
    @NotEmpty(message = "交易编号不能为空")
    private String infno;

    /**
     * 发送方报文id
     */
    //@NotEmpty(message = "发送方报文id不能为空")
    private String msgid;

    /**
     * 就医地医保区划
     */
   // @NotEmpty(message = "就医地医保区划不能为空")
    private String mdtrtarea_admvs;

    /**
     * 参保地医保区划
     */
    private String insuplc_admdvs;

    /**
     * 接收方系统编号
     */
    //@NotEmpty(message = "接收方系统编号不能为空")
    private String recer_sys_code;

    /**
     * 数字签名信息
     */
    private String cainfo;

    /**
     * 签名类型
     */
    private String signtype;

    /**
     * 经办人
     */
    @NotEmpty(message = "经办人不能为空")
    private String opter;

    /**
     * 经办人姓名
     */
    @NotEmpty(message = "经办人姓名不能为空")
    private String opter_name;

    /**
     * 交易时间
     */
    //@NotEmpty(message = "交易时间不能为空")
    private String inf_time;

    /**
     * 定点医药机构编号
     */
    //@NotEmpty(message = "定点医药机构编号不能为空")
    private String fixmedins_code;

    /**
     * 定点医药机构名称
     */
    //@NotEmpty(message = "定点医药机构名称不能为空")
    private String fixmedins_name;

    /**
     * 交易签到流水号
     */
    private String sign_no;

    /**
     * 定点医药机构软件厂商全称
     */
    //@NotEmpty(message = "定点医药机构软件厂商全称不能为空")
    private String fixmedins_soft_fcty;

    @Valid
    @NotEmpty(message = "输入参数不能为空")
    private T input;
}
