package com.my.som.dts.controller;

import com.my.som.common.api.CommonResult;
import com.my.som.dts.entity.param.CommonParam;
import com.my.som.dts.entity.param.Som9001Param;
import com.my.som.dts.entity.vo.PreGroupVo;
import com.my.som.dts.service.Som9001SettleListManageService;
import io.swagger.annotations.ApiOperation;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@RestController
@RequestMapping("/dts")
public class Som9001Controller {

    @Resource
    private Som9001SettleListManageService som9001SettleListManageService;

    @RequestMapping("/Som9001")
    public String Som9001(@RequestBody CommonParam<Som9001Param> param) {
        return "Som9001";
    }

    @ApiOperation("查询模拟预分组结果")
    @PostMapping("/getPreGroupResult")
    public CommonResult preGroupResult(@RequestBody Som9001Param som9001Param,
                                       HttpServletRequest request,
                                       HttpServletResponse response) {
        handleInsuplc(som9001Param);
        PreGroupVo groups = som9001SettleListManageService.getSimlatePreGroup(som9001Param, request, response);
        if ((StringUtils.hasText(groups.getDrgCodg())) || (StringUtils.hasText(groups.getDipCodg()))) {
            return CommonResult.success(groups);
        } else {
            return CommonResult.failed("入组失败,请检查输入信息");
        }
    }

    /**
     * 处理参保地信息
     * @param som9001Param 患者信息参数
     */
    private void handleInsuplc(Som9001Param som9001Param) {
        // 处理参保地相关逻辑
        // 这里可以根据实际业务需求进行参保地信息的处理
        // 例如：验证参保地编码、设置默认参保地等
        if (som9001Param != null && som9001Param.getBaseinfo() != null
            && som9001Param.getBaseinfo().getInsuplc() != null) {
            // 参保地信息处理逻辑
            String insuplc = som9001Param.getBaseinfo().getInsuplc();
            // 可以在这里添加参保地验证、转换等逻辑
        }
    }

}
