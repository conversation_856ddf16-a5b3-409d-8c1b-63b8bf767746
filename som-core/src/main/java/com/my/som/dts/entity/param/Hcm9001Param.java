package com.my.som.dts.entity.param;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.my.som.common.annotations.DateFormat;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class Hcm9001Param {

    @Valid
    private data data;


    @Data
    public static class data {

        /**
         * 系统编号
         */
        @NotEmpty
        private String syscode;

        /**
         * 系统授权码
         */
        @NotEmpty
        private String encrypt_key;

        /**
         * 参保人信息集合
         */
        @NotNull
        private Patient_dtos patient_dtos;

        /**
         * 任务ID
         */
        private String task_id;

        /**
         * 触发场景
         * 1-普通/门特门诊处方
         * 2-医生医嘱审核
         * 3-护士计费审核
         * 4-医生站出院审核
         * 5-医保办出院审核
         * 7护士站出院审核
         * 12-医生站转科审核
         * 13-护士站转科审核
         */
        @NotEmpty
        private String trig_scen;
    }

    @Data
    public static class Patient_dtos {

        /**
         * 参保人ID
         */
        @NotEmpty
        private String patn_id;

        /**
         * 姓名
         */
        @NotEmpty
        private String patn_name;

        /**
         * 性别
         */
        @NotEmpty
        private String gend;

        /**
         * 出生日期
         */
        @NotNull
        @JsonFormat(pattern = "yyyy-MM-dd")
        private Date brdy;

        /**
         * 统筹区编码
         */
        private String poolarea;

        /**
         * 当前就诊ID
         */
        @NotEmpty
        private String unique_id;

        /**
         * 就诊信息集合
         */
        @NotNull
        @Valid
        private Fsi_encounter_dtos fsi_encounter_dtos;

    }

    @Data
    public static class Fsi_encounter_dtos {

        /**
         * 就诊ID
         */
        @NotEmpty
        private String mdtrt_id;

        /**
         * 医疗服务机构标识
         */
        @NotEmpty
        private String medins_id;

        /**
         * 医疗机构名称
         */
        @NotEmpty
        private String medins_name;

        /**
         * 医疗机构清算区划编码
         */
        private String medins_admdvs;

        /**
         * 医疗机构等级（1.一级 2.二级 3.三级 4.未定级）
         */
        @NotEmpty
        private String medins_lv;

        /**
         * 入院日期
         */
        @NotEmpty
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private Date adm_date;

        /**
         * 出院日期
         */
        @NotEmpty
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private Date dscg_date;

        /**
         * 主诊断编码
         */
        @NotEmpty
        private String dscg_main_dise_codg;

        /**
         * 主诊断名称
         */
        @NotEmpty
        private String dscg_main_dise_name;

        /**
         * 诊断信息DTO
         */
        @NotNull
        @Valid
        private List<Fsi_diagnose_dtos> fsi_diagnose_dtos;

        /**
         * 医师标识 经管医生唯一ID
         */
        @NotEmpty
        private String dr_codg;

        /**
         * 医师姓名
         */
        @NotEmpty
        private String dr_name;

        /**
         * 入院科室标识
         */
        @NotEmpty
        private String adm_dept_codg;

        /**
         * 入院科室名称
         */
        @NotEmpty
        private String adm_dept_name;

        /**
         * 当前科室标识
         */
        @NotEmpty
        private String cur_dept_codg;

        /**
         * 当前科室名称
         */
        @NotEmpty
        private String cur_dept_name;

        /**
         * 出院科室标识
         */
        private String dscg_dept_codg;

        /**
         * 出院科室标识
         */
        private String dscg_dept_name;

        /**
         * 就诊类型 1门诊2住院
         */
        @NotEmpty
        private String med_mdtrt_type;

        /**
         * 医疗类型
         */
        @NotEmpty
        private String med_type;

        /**
         * 处方/医嘱/费用项目信息集合
         */
        @NotNull
        @Valid
        private List<Fsi_order_dtos> fsi_order_dtos;

        /**
         * 生育状态
         */
        @NotEmpty
        private String matn_stas;

        /**
         * 总费用
         */
        @NotEmpty
        private BigDecimal medfee_sumamt;

        /**
         * 结算总次数
         */
        @NotEmpty
        private Integer setl_totlnum;

        /**
         * 险种
         */
        @NotEmpty
        private String insutype;

        /**
         * 报销标志
         */
        @NotEmpty
        private String reim_flag;

        /**
         * 异地结算标志
         */
        @NotEmpty
        private String out_setl_flag;

        /**
         * 手术操作集合
         */
        @Valid
        private List<Fsi_operation_dtos> fsi_operation_dtos;

        /**
         * 患者住院次数
         */
        @NotEmpty
        private String patn_ipt_cnt;

        /**
         * 医保费用结算类型
         */
        @NotEmpty
        private String hi_feesetl_type;

        /**
         * 医保支付方式
         */
        @NotEmpty
        private String hi_paymtd;

        /**
         * 住院号
         */
        @NotEmpty
        private String ipt_no;

        /**
         * 人员类别
         */
        @NotEmpty
        private String psn_type;

        /**
         * 入院时病情代码
         * 1危 2急 3一般
         */
        private String adm_cond_code;

        /**
         * 费用分页上传完成标志
         * 1.已上传所有费用
         * 0.未上传完;
         * 当trig_scen为4/5/7/12/13且fsi_encounter_dtos节点不为空时必填
         */
        private String fee_batch_sign;

        /**
         * 审核结果输出方式
         * 0 返回url
         * 1 按两定接口标准返回具体数据
         */
        private String out_put_type;

        /**
         * 当前就诊ID
         * 本次就诊记录唯一ID,每次就诊不一样
         */
        @NotEmpty
        private String unique_id;
    }

    @Data
    public static class Fsi_diagnose_dtos {

        /**
         * 诊断标识
         */
        @NotEmpty
        private String dise_id;

        /**
         * 诊断类别
         * 1出院诊断,4入院诊断，
         * 6修正诊断
         */
        @NotEmpty
        private String inout_dise_type;

        /**
         * 主诊断标志
         * 0否,1是
         */
        @NotEmpty
        private String maindise_flag;

        /**
         * 诊断排序号
         * 例如：1,2,3…
         */
        @NotEmpty
        private String dias_srt_no;

        /**
         * 疾病诊断编码
         * 传医保诊断，无医保诊断时需传临床诊断，临床诊断可为空，医保诊断不为空
         */
        private String dise_codg;

        /**
         * 疾病诊断名称
         * 传医保诊断，无医保诊断时需传临床诊断
         */
        @NotEmpty
        private String dise_name;

        /**
         * 疾病诊断时间
         */
        @NotEmpty
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private Date dise_date;

        /**
         * 疾病诊断编码体系
         * 1 ICD10
         * 2中医病证
         * 3病种编码
         * 5临床诊断
         * 9其他
         */
        @NotEmpty
        private String dise_sys;

        /**
         * 当前就诊ID
         */
        @NotEmpty
        private String unique_id;

    }

    @Data
    @Valid
    public static class Fsi_order_dtos {

        /**
         * 处方/医嘱/费用明细标识
         */
        @NotEmpty
        private String rx_id;

        /**
         * 处方/医嘱号
         */
        @NotEmpty
        private String rxno;

        /**
         * 组编号
         */
        private String grpno;

        /**
         * 医嘱明细id
         */
        private String drord_detl_id;

        /**
         * 长期医嘱标志
         * 1=是,0=否
         */
        @NotEmpty
        private String long_drord_flag;

        /**
         * 医保目录类别
         */
        @NotEmpty
        private String hilist_type;

        /**
         * 收费类别代码
         */
        @NotEmpty
        private String chrg_type;

        /**
         * 收费类别名称
         */
        @NotEmpty
        private String chrg_name;

        /**
         * 医嘱行为
         * 参考字典-[0=其他,1=出院带药]
         */
        @NotEmpty
        private String drord_bhvr;

        /**
         * 医保目录代码
         */
        @NotEmpty
        private String hilist_code;

        /**
         * 医保目录名称
         */
        @NotEmpty
        private String hilist_name;

        /**
         * 医保目录(药品)剂型
         */
        private String hilist_dosform;

        /**
         * 医保目录等级
         */
        @NotEmpty
        private String hilist_lv;

        /**
         * 医保目录价格
         */
        @NotEmpty
        private String hilist_pric;

        /**
         * 医院目录代码
         */
        @NotEmpty
        private String hosplist_code;

        /**
         * 医院目录名称
         */
        @NotEmpty
        private String hosplist_name;

        /**
         * 医院目录(药品)剂型
         */
        private String hosplist_dosform;

        /**
         * 数量
         */
        @NotNull
        private BigDecimal cnt;

        /**
         * 单价
         */
        @NotNull
        private BigDecimal pric;

        /**
         * 总费用
         */
        @NotNull
        private BigDecimal sumamt;

        /**
         * 自费金额
         */
        private BigDecimal ownpay_amt;

        /**
         * 自付金额
         */
        private BigDecimal selfpay_amt;

        /**
         * 规格
         */
        @NotEmpty
        private String spec;

        /**
         * 数量单位
         */
        @NotEmpty
        private String spec_unt;

        /**
         * 费用项目明细发生时间
         */
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private Date fee_ocur_time;

        /**
         * 处方/医嘱下达时间
         */
        @NotEmpty
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private Date drord_ocur_time;

        /**
         * 处方/医嘱开始日期（用药开始时间）
         */
        @NotEmpty
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private Date drord_begn_date;

        /**
         * 处方/医嘱停止日期（用药停止时间）
         */
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private Date drord_stop_date;

        /**
         * 下达处方/医嘱的科室标识
         */
        @NotEmpty
        private String drord_dept_codg;

        /**
         * 下达处方/医嘱科室名称
         */
        @NotEmpty
        private String drord_dept_name;

        /**
         * 开处方/医嘱医生标识
         */
        @NotEmpty
        private String drord_dr_codg;

        /**
         * 开处方/医嘱医生姓名
         */
        @NotEmpty
        private String drord_dr_name;

        /**
         * 开处方/医嘱医生职称
         */
        @NotEmpty
        private String drord_dr_profttl;

        /**
         * 是否当前处方
         * 本次处方标记[1=是,0=否]
         */
        @NotEmpty
        private String curr_drord_flag;

        /**
         * 计费护士标识
         * 护士计费的项目必填
         */
        private String fee_nr_codg;

        /**
         * 计费护士姓名
         * 护士计费的项目必填
         */
        private String fee_nr_name;


        /**
         * 医嘱名称
         */
        private String drord_name;

        /**
         * 医院审批标志
         */
        @NotEmpty
        private String hosp_appr_flag;

        /**
         * 药物使用-频率
         */
        private BigDecimal drug_used_frqu;

        /**
         * 药物使用-次剂量
         */
        private String drug_used_sdose;

        /**
         * 药物使用-剂量单位
         */
        private String medn_used_dosunt;

        /**
         * 用药天数
         */
        private String medc_days;

        /**
         * 病种编码
         */
        private String dise_code;

        /**
         * 病种名称
         */
        private String dise_name;

        /**
         * 组套编号
         */
        private String comb_no;

        /**
         * 组套名称
         */
        private String comb_name;

        /**
         * 字段扩展
         */
        private String exp_content;

        /**
         * 当前就诊ID
         */
        @NotEmpty
        private String unique_id;
    }

    @Data
    public static class Fsi_operation_dtos {

        /**
         * 手术操作ID
         */
        @NotEmpty
        private String setl_list_oprn_id;

        /**
         * 手术操作代码
         */
        @NotEmpty
        private String oprn_code;

        /**
         * 手术操作名称
         */
        @NotEmpty
        private String oprn_name;

        /**
         * 主手术操作标志
         */
        @NotEmpty
        private String main_oprn_flag;

        /**
         * 手术操作日期
         */
        @NotEmpty
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private Date oprn_date;

        /**
         * 麻醉方式
         */
        private String anst_way;

        /**
         * 术者医师姓名
         */
        private String oper_dr_name;

        /**
         * 术者医师代码
         */
        private String oper_dr_code;

        /**
         * 麻醉医师姓名
         */
        private String anst_dr_name;

        @NotEmpty
        private String unique_id;
    }
}
