package com.my.som.config;

import com.my.som.common.util.ValidateUtil;
import com.my.som.mapper.sys.SomTimerTaskTimeCfgMapper;
import com.my.som.service.drgInterface.DipDataService;
import com.my.som.service.webservice.impl.SomDataAcquisitionInterfaceServiceImpl;
import com.my.som.vo.webservice.MedicalInsuranceDataVo;
import com.my.som.vo.webservice.MedicalPageDataVo;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.SchedulingConfigurer;
import org.springframework.scheduling.config.ScheduledTaskRegistrar;
import org.springframework.scheduling.support.CronTrigger;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ConcurrentLinkedQueue;

/**
 * 定时抽取数据
 */
//@Configuration      //1.主要用于标记配置类，兼备Component的效果。
@Slf4j
public class DynamicExtractDataTask implements SchedulingConfigurer {
    Logger logger = LoggerFactory.getLogger(DynamicExtractDataTask.class);

    @Autowired
    private SomTimerTaskTimeCfgMapper sysScheduleCronMapper;

    @Autowired
    private DipDataService dipDataService;

    private final static int EXTRACTNUM = 20;

    @Value("${sys.queueExtract:#{false}}")
    public boolean queueExtract;

    @Override
    public void configureTasks(ScheduledTaskRegistrar scheduledTaskRegistrar) {
        scheduledTaskRegistrar.addTriggerTask(
                //1.添加任务内容(Runnable)
                () ->
                {
                    try {
                        if (queueExtract) {
                            // 处理di01
                            queueHandleDI01();
                            // 处理di02
                            queueHandleDI02();
                        }
                    }
                    catch(Exception e) {
                        e.printStackTrace();
                    }
                },
                //2.设置执行周期(Trigger)
                triggerContext -> {
                    String cron_expr = "* * * 31 3 ?";
                    if(queueExtract) {
                        //2.1 从数据库获取执行周期
                        cron_expr = sysScheduleCronMapper.selectByPrimaryKey(Long.valueOf(3)).getCron_expr();
                        //2.2 合法性校验.
                        if (ValidateUtil.isEmpty(cron_expr)) {
                            logger.error("cron表达式有误:,请仔细检查后重新输入！");
                        }
                        //2.3 返回执行周期(Date)
                    }
                    return new CronTrigger(cron_expr).nextExecutionTime(triggerContext);
                }
        );
    }

    /**
     * 队列处理DI01
     * @throws Exception
     */
    private void queueHandleDI01() throws Exception {
        // 清单队列数据
        ConcurrentLinkedQueue queuedatalist = SomDataAcquisitionInterfaceServiceImpl.QUEUEDATALIST;
        log.info("在队列中清单数据条数:{" + queuedatalist.size() + "}条");
        int numList = dipDataService.queryNoExtractList();
        if (numList < EXTRACTNUM && ValidateUtil.isNotEmpty(queuedatalist)) {
            List<MedicalInsuranceDataVo> dataMapForMedicalList = new ArrayList<>();
            if (queuedatalist.size() < EXTRACTNUM - numList) {
                for (int i = 0; i < queuedatalist.size(); i++) {
                    dataMapForMedicalList.add((MedicalInsuranceDataVo) queuedatalist.poll());
                }
            } else {
                for (int i = 0; i < EXTRACTNUM - numList; i++) {
                    dataMapForMedicalList.add((MedicalInsuranceDataVo) queuedatalist.poll());
                }
            }
            dipDataService.uploadMedicalInsuranceData(dataMapForMedicalList);
        }
    }

    /**
     * 队列处理DI02表
     * @throws Exception
     */
    private void queueHandleDI02() throws Exception {
        // 首页队列数据
        ConcurrentLinkedQueue queuedata = SomDataAcquisitionInterfaceServiceImpl.QUEUEDATA;
        log.info("在队列中首页数据条数:{" + queuedata.size() + "}条");
        // 待抽取数量
        int cnt = dipDataService.queryNoExtract();
        if (cnt < EXTRACTNUM && ValidateUtil.isNotEmpty(queuedata)) {
            List<MedicalPageDataVo> dataMapForMedicalPageList = new ArrayList<>();
            if (queuedata.size() < EXTRACTNUM - cnt) {
                for (int i = 0; i < queuedata.size(); i++) {
                    dataMapForMedicalPageList.add((MedicalPageDataVo) queuedata.poll());
                }
            } else {
                for (int i = 0; i < EXTRACTNUM - cnt; i++) {
                    dataMapForMedicalPageList.add((MedicalPageDataVo) queuedata.poll());
                }
            }
            dipDataService.uploadMedicalPageData(dataMapForMedicalPageList);
        }
    }
}
