package com.my.som.config;

import com.my.som.common.constant.DrgConst;
import com.my.som.common.exception.AppException;
import com.my.som.common.util.DateUtil;
import com.my.som.common.util.RedisUtils;
import com.my.som.common.util.ValidateUtil;
import com.my.som.dao.common.CommonDao;
import com.my.som.mapper.common.SomHospInfoMapper;
import com.my.som.mapper.dataHandle.SomDataprosLogMapper;
import com.my.som.mapper.sys.SomTimerTaskTimeCfgMapper;
import com.my.som.model.common.SomHospInfo;
import com.my.som.model.dataHandle.SomDataprosLog;
import com.my.som.service.dataHandle.DataHandleProcessService;
import com.my.som.service.dataHandle.SettleListUploadService;
import com.my.som.util.SysCommonConfigUtil;
import com.my.som.common.vo.SysUserBase;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.TaskScheduler;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.SchedulingConfigurer;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;
import org.springframework.scheduling.config.ScheduledTaskRegistrar;
import org.springframework.scheduling.support.CronTrigger;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.concurrent.ConcurrentLinkedQueue;


/**
 * @author: zyd
 * @date: 2023-07-31
 */
@Configuration      //1.主要用于标记配置类，兼备Component的效果。
@Slf4j
public class DynamicScheduleTask implements SchedulingConfigurer {
    Logger logger = LoggerFactory.getLogger(DynamicScheduleTask.class);

    @Autowired
    private SomTimerTaskTimeCfgMapper sysScheduleCronMapper;
    @Autowired
    private DataHandleProcessService dataHandleProcessService;
    @Autowired
    private SettleListUploadService settleListUploadService;
    @Autowired
    private SomHospInfoMapper busHospitalMapper;
    @Autowired
    private CommonDao commonDao;
    @Autowired
    private SomDataprosLogMapper stsDataHandleLogMapper;

    //redis中正在跑流程的k00的key
    private static final String  RUNNING_K00S_REDIS_KEY_NAME = "queue";
    /**
     * 初始化队列
     */
    public static ConcurrentLinkedQueue<String> taskQueue = new ConcurrentLinkedQueue<>();
    //获取正在等待需要被处理的流程ID
//    public Long getDataLogIdForWaited(){
//        SomDataprosLog somDataprosLog = commonDao.getMinIdByStsDataHandleLogForInterface();
//        if(!ValidateUtil.isEmpty(somDataprosLog)){
//            return somDataprosLog.getId();
//        }else{
//            return null;
//        }
//    }

    public SysUserBase getTempUser() {
        SysUserBase sysUserBase = new SysUserBase();
//        SomHospInfo somHospInfo = busHospitalMapper.selectByPrimaryKey(Long.valueOf((String) SysCommonConfigUtil.get(DrgConst.SCC_CUR_HOSPITAL)));  //只是针对一家医院
        sysUserBase.setUsername("自动化数据抽取-临时用户");
//        sysUserBase.setHospitalId(somHospInfo.getHospitalId());
//        sysUserBase.setHospLv(somHospInfo.getHospLv());
        return sysUserBase;
    }

    /**
     * 执行数据抽取的定时任务.
     */
    @Override
    public void configureTasks(ScheduledTaskRegistrar taskRegistrar) {
        taskRegistrar.addTriggerTask(
                //1.添加任务内容(Runnable)
                () ->
                        //数据抽取
                {
                    try {
                        this.startScheduleTask(null);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                },
                //2.设置执行周期(Trigger)
                triggerContext -> {
                    //2.1 从数据库获取执行周期
                    String cron_expr = sysScheduleCronMapper.selectByPrimaryKey(Long.valueOf(1)).getCron_expr();
                    //2.2 合法性校验.
                    if (ValidateUtil.isEmpty(cron_expr)) {
                        logger.error("cron表达式有误:,请仔细检查后重新输入！");
                    }
                    Object o = SysCommonConfigUtil.get(DrgConst.SCC_ENABLE_ITFT);
                    if (o != null && DrgConst.ACTIVE_FLAG_1.equals(o.toString())) {
                        // 不生效
                        cron_expr = "* * * 13 * *";
                    }
                    //2.3 返回执行周期(Date)
                    return new CronTrigger(cron_expr).nextExecutionTime(triggerContext);
                }
        );
    }

    /**
     * 开启流程
     *
     * @throws Exception
     */
//    public synchronized void startScheduleTask() throws Exception {
//        // 删除 data_log_id 无数据行
//        // deleteNonDataLog();
//        //先插入数据处理日志信息（这个返回一个data_log_id)，但不做流程处理，result=waited
//        //查询是否data_log_id为空的数据
//        String count = commonDao.hasDataLogIdIsNullData(null);
//        logger.info("=========待处理数据：" + count + "=====Queue.size：" + taskQueue.size() + "=========");
//        if (!"0".equals(count) && taskQueue.isEmpty()) {
//            processExecution(count);
//        }
//    }

    public synchronized void startScheduleTask(String id) throws Exception {
        // 删除 data_log_id 无数据行
        // deleteNonDataLog();
        //先插入数据处理日志信息（这个返回一个data_log_id)，但不做流程处理，result=waited
        //查询是否data_log_id为空的数据
        String count = commonDao.hasDataLogIdIsNullData(id);
        logger.info("=========待处理数据：" + count + "=====Queue.size：" + taskQueue.size() + "=========");
        if (!"0".equals(count) && taskQueue.isEmpty()) {
            processExecution(count);
        }
    }

    public void processExecution(String count) {
        SysUserBase tempUser = getTempUser();
        Long data_log_id = addOneDataHandleLog(count);
        taskQueue.add(data_log_id.toString());
        List<String> ids = commonDao.selectIdByDataLogId();
        //查询k00s并放入redis
        List<String> k00s = commonDao.selectK00ByDataLogId();
        //key +data_log_id  防止未跑完流程被的k00 被覆盖
        RedisUtils.set("queue" + data_log_id, k00s);
        Integer upNum = commonDao.updateDataLogIdIsNull(data_log_id, ids);
        logger.info("更新日志id:{},更新数量为:{}", data_log_id, upNum);
        if (!ValidateUtil.isEmpty(data_log_id) && upNum > 0) {
            try {
                dataHandleProcessService.runPro(
                        settleListUploadService.savaMedicalDataForSchedual(data_log_id)
                        , tempUser);
            } catch (Exception e) {
                //此处结束删除
                RedisUtils.delete(RUNNING_K00S_REDIS_KEY_NAME + data_log_id);
                logger.error(data_log_id + "批次执行业务流程异常：", e);
                //出现异常，清除不跑流程的队列
                if (!taskQueue.isEmpty()) {
                    taskQueue.poll();
                }
                throw new AppException(data_log_id + "跑批业务流程执行异常", e);
            }
        } else {
            //清除不跑流程的队列
            if (!taskQueue.isEmpty()) {
                taskQueue.poll();
            }
        }
    }

    /**
     * 是否可以跑数据
     *
     * @return
     */
    public boolean canRun() {
        // 查询正在执行的个数
        int processNum = commonDao.queryExeProcessNum();
        Object o = SysCommonConfigUtil.get(DrgConst.SCC_MAX_PROCESS_NUM);
        int maxProcessNum = 50;
        if (!ValidateUtil.isEmpty(o)) {
            maxProcessNum = Integer.parseInt(o.toString());
        }
        if (processNum < maxProcessNum) {
            return true;
        }
        return false;
    }

    /**
     * 删除因为死锁导致空数据情况
     */
    private void deleteNonDataLog() {
        List<Integer> countList = commonDao.queryNonDataLogNum();
        if (countList.size() > 0) {
            int dataNum = commonDao.queryNonDataNum();
            if (dataNum == 0) {
                int delRowNum = commonDao.deleteNonDataLog(countList);
                log.error("删除死锁导致空数据数量：{}，ids：{}", delRowNum, countList);
            }
        }
    }

    //生成一个批次的日志记录，为后面做准备
    private Long addOneDataHandleLog(String count) {
        SomDataprosLog sdhl = new SomDataprosLog();
        sdhl.setMedcasVal(Integer.valueOf(count));//上传病案总数
        sdhl.setDataUpldTime(DateUtil.getCurrentDateTime()); //上传时间
        sdhl.setResult("waited");
        sdhl.setPrcs_prgs(0); //默认进度为0
        sdhl.setOprt_psn("数据接口"); //操作人
        sdhl.setActiveFlag(DrgConst.ACTIVE_FLAG_1);
        stsDataHandleLogMapper.insert(sdhl);
        return sdhl.getId();
    }
}
