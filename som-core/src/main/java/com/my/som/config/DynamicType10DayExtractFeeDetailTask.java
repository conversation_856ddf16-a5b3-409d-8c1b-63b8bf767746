package com.my.som.config;

import com.my.som.common.exception.AppException;
import com.my.som.common.util.ValidateUtil;
import com.my.som.dao.dataHandle.BusSettleListDao;
import com.my.som.dto.hisview.HisViewDTO;
import com.my.som.mapper.hisview.HisViewMapper;
import com.my.som.mapper.sys.SomTimerTaskTimeCfgMapper;
import com.my.som.model.sys.SomTimerTaskTimeCfg;
import com.my.som.service.hisview.HisViewService;
import com.my.som.vo.webservice.FeeDetailDataVo;
import com.my.som.vo.webservice.InhosDetailDataVo;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.SchedulingConfigurer;
import org.springframework.scheduling.config.ScheduledTaskRegistrar;
import org.springframework.scheduling.support.CronTrigger;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.DefaultTransactionDefinition;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * @version 1.0
 * @author: zyd
 * @date: 2023-07-31
 * @description: 定时任务类，可写多个
 * 已定义：
 * 每天三点抽取昨天费用明细
 */
@Configuration      //1.主要用于标记配置类，兼备Component的效果。
@Slf4j
public class DynamicType10DayExtractFeeDetailTask implements SchedulingConfigurer {
    Logger logger = LoggerFactory.getLogger(DynamicType10DayExtractFeeDetailTask.class);

    @Autowired
    private PlatformTransactionManager transactionManager;

    @Autowired
    private HisViewMapper hisViewMapper;

    @Autowired
    private BusSettleListDao busSettleListDao;

    @Autowired
    private SomTimerTaskTimeCfgMapper sysScheduleCronMapper;

    @Autowired
    private ViewTableConfig config;

    @Autowired
    private HisViewService hisViewService;

    @Override
    public void configureTasks(ScheduledTaskRegistrar scheduledTaskRegistrar) {
        scheduledTaskRegistrar.addTriggerTask(
                //1.添加任务内容(Runnable)
                () ->
                {
                    try {
                        HisViewDTO hisViewDTO = new HisViewDTO();
                        // 获取今天的日期
                        LocalDate today = LocalDate.now();

                        // 计算1天前的日期
                        LocalDate yesterday = LocalDate.now().minusDays(1);
                        LocalDate startDate = yesterday;
                        LocalDate endDate = yesterday;
                        // 将日期转换为 LocalDateTime，并设置时间为开始和结束
                        LocalDateTime startDateTime = startDate.atStartOfDay();
                        LocalDateTime endDateTime = endDate.atTime(LocalTime.MAX);

                        // 格式化日期时间
                        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                        // 格式化输出
                        String firstDayFormatted = endDateTime.format(formatter);
                        String lastDayFormatted = startDateTime.format(formatter);

                        hisViewDTO.setEndTime(lastDayFormatted);
                        hisViewDTO.setStartTime(firstDayFormatted);
                        Map<String, String> tableName = config.getTableName();
                        List<String> uniqueIds  = new ArrayList<>();
                        if (tableName.get("setlinfo") != null) {
                            hisViewDTO.setBaseTableName(tableName.get("setlinfo"));
                            hisViewDTO.setSqlType(hisViewService.getSqlType());
                            uniqueIds  = hisViewMapper.getUniqueIdBySetlInfo(hisViewDTO);
                            logger.debug("抽取的病例人数为:{}", uniqueIds.size());
                        }
                        if(ValidateUtil.isEmpty(uniqueIds)){
                            return;
                        }
                        logger.info("=============定时任务--抽取病例数" + uniqueIds.size() + "===================");
                        if (tableName.get("feedetailList") != null) {
                            hisViewDTO.setOthTableName(tableName.get("feedetailList"));
                        }else {
                            throw new AppException("application.yml 文件未配置住院费用明细视图名称");
                        }
                            int batchUniqueIdSize = 10;
                        for (int i = 0; i < uniqueIds.size(); i += batchUniqueIdSize) {
                            int end = Math.min(i + batchUniqueIdSize, uniqueIds.size());
                            List<String> batchUniqueIds = uniqueIds.subList(i, end);
                            List<FeeDetailDataVo> dataMapForInhosDetailList = hisViewMapper.getInhosDetailInfo(hisViewDTO, batchUniqueIds);
                            if(!ValidateUtil.isEmpty(dataMapForInhosDetailList)){
                                hisViewService.deleteAndInsert(dataMapForInhosDetailList,batchUniqueIds);
                            }
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                },
                //2.设置执行周期(Trigger)
                triggerContext -> {
                    //2.1 从数据库获取执行周期
                    SomTimerTaskTimeCfg somTimerTaskTimeCfg = sysScheduleCronMapper.selectByPrimaryKey(Long.valueOf(10));
                    if (!ValidateUtil.isEmpty(somTimerTaskTimeCfg)) {
                        String cron_expr = somTimerTaskTimeCfg.getCron_expr();
                        //2.2 合法性校验.
                        if (!ValidateUtil.isEmpty(cron_expr)) {
                            //2.3 返回执行周期(Date)
                            return new CronTrigger(cron_expr).nextExecutionTime(triggerContext);
                        }
                    }
                    return null;
                }
        );
    }
}
