package com.my.som.config;


import javax.annotation.Resource;
import javax.xml.ws.Endpoint;


import com.my.som.service.webservice.*;
import org.apache.cxf.Bus;
import org.apache.cxf.bus.spring.SpringBus;
import org.apache.cxf.jaxws.EndpointImpl;
import org.apache.cxf.transport.servlet.CXFServlet;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.web.servlet.ServletRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;


@Configuration
public class WebServiceConfig {
    @Autowired
    private SomAiPaymentPrediction somAiPaymentPrediction;
    @Autowired
    private DataAcquisitionDipInterfaceService dataAcquisitionDipInterfaceService;
    @Autowired
    private SomDataAcquisitionInterfaceService somDataAcquisitionInterfaceService;
    @Autowired
    private SomDataAcquisitionInterfaceNewService somDataAcquisitionInterfaceNewService;
    @Autowired
    private SomDataAcquisitionInterfaceNewServiceCS somDataAcquisitionInterfaceNewServiceCS;

    @Autowired
    private HISDataExtractInterfaceService hisDataExtractInterfaceService;

    /**
     * 注入servlet bean name不能dispatcherServlet 否则会覆盖dispatcherServlet
     *
     * @return
     */
    @Bean(name = "cxfServlet")
    public ServletRegistrationBean cxfServlet() {
        return new ServletRegistrationBean(new CXFServlet(), "/services/*");
    }

    @Bean(name = Bus.DEFAULT_BUS_ID)
    public SpringBus springBus() {
        return new SpringBus();
    }

    /**
     * 注册somAiPaymentPrediction接口到webservice服务
     *
     * @return
     */
    @Bean(name = "SomAiPaymentPrediction")
    public Endpoint execPreGroupMethodEndpoint() {
        EndpointImpl endpoint = new EndpointImpl(springBus(), somAiPaymentPrediction);
        endpoint.publish("/somAiPaymentPrediction");
        return endpoint;
    }


    /**
     * 注册dataExtractInterfaceService接口到webservice服务
     *
     * @return
     */
    @Bean(name = "DataAcquisitionDipInterfaceService")
    public Endpoint execDipDataAcqMethodEndpoint() {
        EndpointImpl endpoint = new EndpointImpl(springBus(), dataAcquisitionDipInterfaceService);
        endpoint.publish("/dataAcquisitionDipInterfaceService");
        return endpoint;
    }

    /**
     * 注册somDataAcquisitionInterfaceService接口到webservice服务
     *
     * @return
     */
    @Bean(name = "SomDataAcquisitionInterfaceService")
    public Endpoint execDataAcqMethodEndpoint() {
        EndpointImpl endpoint = new EndpointImpl(springBus(), somDataAcquisitionInterfaceService);
        endpoint.publish("/somDataAcquisitionInterfaceService");
        return endpoint;
    }

    @Bean(name = "SomDataAcquisitionInterfaceNewService")
    public Endpoint execDataAcqMethodNewEndpoint() {
        EndpointImpl endpoint = new EndpointImpl(springBus(), somDataAcquisitionInterfaceNewService);
        endpoint.publish("/somDataAcquisitionInterfaceNewService");
        return endpoint;
    }

    @Bean(name = "HISDataExtractInterfaceService")
    public Endpoint execHISDataExtractInterfaceService() {
        EndpointImpl endpoint = new EndpointImpl(springBus(), hisDataExtractInterfaceService);
        endpoint.publish("/HISDataExtractInterfaceService");
        return endpoint;
    }

    /**
     * 德阳仓山 4101上传兼容
     * @return
     */
    @Bean(name = "SomDataAcquisitionInterfaceNewServiceCS")
    public Endpoint execDataAcqMethodNewCSEndpoint() {
        EndpointImpl endpoint = new EndpointImpl(springBus(), somDataAcquisitionInterfaceNewServiceCS);
        endpoint.publish("/somDataAcquisitionInterfaceNewServiceCS");
        return endpoint;
    }

    /**
     * 德阳龙台 4101上传兼容
     * @return
     */
    @Bean(name = "HISDataExtractInterfaceNewServiceLT")
    public Endpoint execDataAcqMethodNewLTEndpoint() {
        EndpointImpl endpoint = new EndpointImpl(springBus(), somDataAcquisitionInterfaceNewServiceCS);
        endpoint.publish("/hisDataExtractInterfaceNewService");
        return endpoint;
    }

}
