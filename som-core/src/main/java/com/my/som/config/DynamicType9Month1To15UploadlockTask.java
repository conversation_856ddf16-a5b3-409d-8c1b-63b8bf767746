package com.my.som.config;

import com.my.som.common.constant.DrgConst;
import com.my.som.common.exception.AppException;
import com.my.som.common.util.ValidateUtil;
import com.my.som.dto.listManagement.ListUploadDto;
import com.my.som.mapper.listManagement.ListUploadMapper;
import com.my.som.mapper.sys.SomTimerTaskTimeCfgMapper;
import com.my.som.model.sys.SomTimerTaskTimeCfg;
import com.my.som.service.listManagement.ListUploadService;
import com.my.som.util.GroupCommonUtil;
import com.my.som.util.SysCommonConfigUtil;
import com.my.som.vo.listManagement.ListUploadVo;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.SchedulingConfigurer;
import org.springframework.scheduling.config.ScheduledTaskRegistrar;
import org.springframework.scheduling.support.CronTrigger;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * @version 1.0
 * @author: zyd
 * @date: 2023-07-31
 * @description: 定时任务类，可写多个
 * 已定义：
 * 每月1-15日自动提交上月数据
 */
@Configuration      //1.主要用于标记配置类，兼备Component的效果。
@Slf4j
public class DynamicType9Month1To15UploadlockTask implements SchedulingConfigurer {
    Logger logger = LoggerFactory.getLogger(DynamicType9Month1To15UploadlockTask.class);
    private final String UPLOAD_STATE_KEY = "state";
    private final String UPLOAD_STATE_SUCCESS = "1";

    @Autowired
    private ListUploadMapper listUploadMapper;
    @Autowired
    private ListUploadService listUploadService;

    @Autowired
    private SomTimerTaskTimeCfgMapper sysScheduleCronMapper;

    @Override
    public void configureTasks(ScheduledTaskRegistrar scheduledTaskRegistrar) {
        scheduledTaskRegistrar.addTriggerTask(
                //1.添加任务内容(Runnable)
                () ->
                {
                    try {
                        ListUploadDto dto = new ListUploadDto();
                        if (DrgConst.GROUP_TYPE_NAME_DIP.equals(SysCommonConfigUtil.get(DrgConst.FZLX))) {
                            dto.setGrperType("1");
                        }else{
                            dto.setGrperType("3");
                        }
                        dto.setInHosFlag("1");
                        dto.setUpldStas("1");
                        dto.setUploadFlag("0");
//                        LocalDate currentDate = LocalDate.now();
//                        LocalDate lastMonthDate = currentDate.minusMonths(1);
//                        // 获取上个月的第一天
//                        LocalDate firstDayOfLastMonth = lastMonthDate.withDayOfMonth(1);
//                        // 获取上个月的最后一天
//                        LocalDate lastDayOfLastMonth = lastMonthDate.withDayOfMonth(lastMonthDate.lengthOfMonth());
//                        // 定义日期格式
//                        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
//                        // 格式化输出
//                        String firstDayFormatted = firstDayOfLastMonth.format(formatter);
//                        String lastDayFormatted = lastDayOfLastMonth.format(formatter);
                        // 获取当前日期
                        LocalDate currentDate = LocalDate.now();
                        // 获取当月的第一天
                        LocalDate firstDayOfCurrentMonth = currentDate.withDayOfYear(1);
                        // 获取当月的最后一天
                        LocalDate lastDayOfCurrentMonth = currentDate;
                        // 定义日期格式
                        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
                        // 格式化输出
                        String firstDayFormatted = firstDayOfCurrentMonth.format(formatter);
                        String lastDayFormatted = lastDayOfCurrentMonth.format(formatter);

                        dto.setBegnDate(firstDayFormatted);
                        dto.setExpiDate(lastDayFormatted);

                        String insuPlaceType = (String) SysCommonConfigUtil.get(DrgConst.INSUPLC_ADMDVS_AREA);
                        dto.setInsuplcAdmdvs(insuPlaceType);
                        String rovLevelInsuplcAdmdvs = GroupCommonUtil.getInsuplcAdmdvs();
                        if(ValidateUtil.isEmpty(rovLevelInsuplcAdmdvs)){
                            rovLevelInsuplcAdmdvs = "0000";
                        }
                        dto.setProvLevelInsuplcAdmdvs(rovLevelInsuplcAdmdvs);
                        //查询上传的未提交数据
                        List<ListUploadVo> list = listUploadMapper.queryUploadedData(dto);
                        logger.info("=============定时任务--每月1-15日查询待提交数据：" + list.size() + "===================");
                        if (ValidateUtil.isEmpty(list)) {
                            return;
                        }
                        List<String> k00s = new ArrayList<>();
                        List<String> ids = new ArrayList<>();
                        multipartUpload(dto, list, k00s, ids);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                },
                //2.设置执行周期(Trigger)
                triggerContext -> {
                    //2.1 从数据库获取执行周期
                    SomTimerTaskTimeCfg somTimerTaskTimeCfg = sysScheduleCronMapper.selectByPrimaryKey(Long.valueOf(9));
                    if (!ValidateUtil.isEmpty(somTimerTaskTimeCfg)) {
                        String cron_expr = somTimerTaskTimeCfg.getCron_expr();
                        //2.2 合法性校验.
                        if (!ValidateUtil.isEmpty(cron_expr)) {
                            //2.3 返回执行周期(Date)
                            return new CronTrigger(cron_expr).nextExecutionTime(triggerContext);

                        }
                    }
                    return null;
                }
        );
    }



    private void multipartUpload(ListUploadDto dto, List<ListUploadVo> list, List<String> k00s, List<String> ids) {
        int batchSize = 50;
        int count = 0;
        ListUploadDto dto1 = new ListUploadDto();
        if (DrgConst.GROUP_TYPE_NAME_DIP.equals(SysCommonConfigUtil.get(DrgConst.FZLX))) {
            dto1.setGrperType("1");
        }else{
            dto1.setGrperType("3");
        }
        dto1.setIsSuccess("0");
        dto1.setUpldStas("1");
        dto1.setUploadType("2");
        dto1.setUploadFlag("0");
        dto1.setOpe("定时任务");
        dto1.setBegnDate(dto.getBegnDate());
        dto1.setExpiDate(dto.getExpiDate());
        for (ListUploadVo vo : list) {
            k00s.add(vo.getK00());
            ids.add(vo.getId());
            count++;

            // 每 50 个元素添加一次
            if (count == batchSize) {
                dto1.setIds(ids);
                dto1.setK00s(k00s);
                setUploadParams(dto1);
                listUploadService.modifySettleListType(dto1);
                // 清空临时集合
                k00s.clear();
                ids.clear();

                // 重置计数器
                count = 0;
            }

        }
        if (!k00s.isEmpty()) {
            dto1.setIds(ids);
            dto1.setK00s(k00s);
            setUploadParams(dto1);
            listUploadService.modifySettleListType(dto1);
        }
    }

    private Map<String, Object> setUploadParams(ListUploadDto dto) {
        Map<String, Object> res = new HashMap<>();
        String uuid = UUID.randomUUID().toString();
        BigDecimal decimal = new BigDecimal(dto.getUploadFlag());
        dto.setBatchNum(uuid);
        dto.setType(decimal.add(BigDecimal.ONE).toString());
        List<String> idsList = new ArrayList<>();
        List<String> k00sList = new ArrayList<>();
        // 全部上传时查询
        if (ValidateUtil.isEmpty(dto.getIds())) {
            dto.setAllUpload(true);
            dto.setLookOver(DrgConst.TYPE_1);
            List<ListUploadVo> list;
            if (DrgConst.TYPE_1.equals(dto.getUploadType())) {
                list = listUploadMapper.queryData(dto);
            } else {
                list = listUploadMapper.queryUploadedData(dto);
            }
            for (ListUploadVo listUploadVo : list) {
                idsList.add(listUploadVo.getId());
                k00sList.add(listUploadVo.getK00());
            }
            dto.setIds(idsList);
            dto.setK00s(k00sList);
        }
        res.put(UPLOAD_STATE_KEY, UPLOAD_STATE_SUCCESS);
        return res;
    }

}
