package com.my.som.constant;

import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

/**
 * 流程测试相关变量
 */
public interface ProcessJudgeConst {
    /** 根据年份查询 */
    public static final String DATE_TYPE_1 = "1";
    /** 根据月份查询 */
    public static final String DATE_TYPE_2 = "2";
    /** 根据时间范围查询 */
    public static final String DATE_TYPE_3 = "3";

    /** 需要根据settleListId删除的表 */
    List<String> specialTable = Arrays.asList(
            "som_datapros_log"
    );

    /** 需要根据settleListId删除的表 */
    List<String> truncateTableNames = Arrays.asList(
            "som_drg_grp_rcd",
            "som_drg_grper_intf_trns_log",
            "som_dip_grp_rcd",
            "som_dip_grper_intf_trns_log",
            "som_grp_rcd",
            "som_cd_grper_intf_trns_log",
            "som_drg_grp_info",
            "som_dip_grp_info",
            "som_cd_grp_info",
            "som_datapros_log",
            "som_codg_resu_adjm_rcd",
            "som_setl_invy_chk_err_rcd",
            "som_setl_invy_qlt_dedu_point_detl",
            "som_dip_sco",
            "som_drg_sco",
            "som_setl_invy_chk",
            "som_invy_chk_detl",
            "som_in_group_rcd",
            "som_can_opt_medcas_info"
    );

    List<String> TABLE_NAMES_TO_DELETE = Arrays.asList(
            "som_setl_invy_chk_err_rcd",
            "som_setl_invy_qlt_dedu_point_detl"
    );

}
