package com.my.som.constant;

import java.util.HashMap;
import java.util.Map;

public interface SysInitConst {

     Map<String, String> IGNORE = new HashMap<String, String>() {{
        put("/user/login", "login");
        put("/user/logout", "logout");
        put("/sysInitController/crtfCode","register");
        put("/menu/findNavTree","menu");
        put("/user/findPermissions","user");
        put("/menu/queryDoubleCacheData","queryDoubleCacheData");
        put("/user/info","info");
        put("/sysInitController/querySystemTime","querySystemTime");
    }};

    String SALT = "drg-core-secret";
}
