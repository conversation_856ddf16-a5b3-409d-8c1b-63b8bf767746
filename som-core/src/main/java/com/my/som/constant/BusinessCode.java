package com.my.som.constant;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

public enum BusinessCode {
    PREGROUP("C0001","DRGs预先分组", "xsd/C0001.xsd");
    String code;
    String name;
    String xsd_path;

    private BusinessCode(String code, String name, String xsd_path) {
        this.code = code;
        this.name = name;
        this.xsd_path = xsd_path;
    }

    /**
     * 通过CODE获取枚举
     * @param code
     * @return
     */
    public static BusinessCode getFromCode(String code){
        List<BusinessCode> list = Arrays.asList(values()).stream().filter(item-> item.getCode().equals(code)).collect(Collectors.toList());
        if(list.size()>0){
            return list.get(0);
        }
        return null;
    }

    /**
     * 获取 xsd_path
     *
     * @return xsd_path
     */
    public String getXsd_path() {
        return xsd_path;
    }

    /**
     * 获取 code
     *
     * @return code
     */
    public String getCode() {
        return code;
    }

    /**
     * 获取 name
     *
     * @return name
     */
    public String getName() {
        return name;
    }
}
