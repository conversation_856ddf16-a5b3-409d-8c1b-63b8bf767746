package com.my.som.entity.upload;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class WorkersUpload {

    /**医护人员代码*/
    @Excel(name ="医护人员代码")
    private String code;

    /**姓名*/
    @Excel(name ="姓名")
    private String name;

    /**公民身份号码*/
    @Excel(name ="公民身份号码")
    private String citiIdetNo;

    /**出生日期*/
    @Excel(name ="出生日期")
    private String brdy;

    /**性别*/
    @Excel(name ="性别")
    private String gend;

    /**职称*/
    @Excel(name ="职称")
    private String profttl;

    /**职业类别*/
    @Excel(name ="执业类别")
    private String type;

    /**医生类别*/
    @Excel(name ="医生类别")
    private String doctorType;

    /**科室编码*/
    @Excel(name ="科室编码")
    private String deptCode;

    /**医疗机构ID*/
    @Excel(name ="医院编码")
    private String hospitalId;

    /**启用标志*/
    private String activeFlag;

}
