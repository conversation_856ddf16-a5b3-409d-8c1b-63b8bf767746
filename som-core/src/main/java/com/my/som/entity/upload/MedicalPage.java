package com.my.som.entity.upload;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.my.som.common.annotations.DateFormat;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 *病案数据
 */
@Getter
@Setter
@ToString
public class MedicalPage {
    /** 病案唯一标识ID（主要在病案抽取中关联诊断、手术、费用、重症监护室信息） */
    private String k00;

    /** 医疗机构代码 */
    @Excel(name = "DDYLJGDM")
    private String ddyljgdm;

    /** 医疗机构名称 */
    @Excel(name = "USERNAME")
    private String username;

    /** 病案首页类型 */
    @Excel(name = "BASYLX")
    private String basylx;

    /** 姓名 */
    @Excel(name = "XM")
    private String xm;

    /** 性别 */
    @Excel(name = "XB")
    private String xb;

    /** 出生日期 */
    @Excel(name = "CSRQ")
    @DateFormat(pattern = "yyyy-MM-dd")
    private String csrq;

    /** 年龄 */
    @Excel(name = "NL")
    private String nl;
    
     /** 结算时间 */
     @Excel(name = "JSSJ")
     private String jssj;

     /** 住院医师代码 */
     @Excel(name = "ZYYSDM")
     private String zyysdm;

    /** 国籍 */
    @Excel(name = "GJ")
    private String gj;

    /** (不足一周岁)年龄(月) */
    @Excel(name = "BZYZSNL")
    private String bzyzsnl;

    /** 新生儿入院体重 */
    @Excel(name = "XSERYTZ")
    private String xserytz;

    /** 新生儿出生体重 */
    @Excel(name = "XSECSTZ")
    private String xsecstz;

    /** 名族 */
    @Excel(name = "MZ")
    private String mz;

    /** 身份证号 */
    @Excel(name = "SFZH")
    private String sfzh;

    /** 婚姻 */
    @Excel(name = "HY")
    private String hy;

    /** 出生地 */
    @Excel(name = "CSD")
    private String csd;

    /** 籍贯 */
    @Excel(name = "GG")
    private String gg;

    /** 医疗付款方式 */
    @Excel(name = "YLFKFS")
    private String ylfkfs;

    /** 健康卡号 */
    @Excel(name = "JKKH")
    private String jkkh;

    /** 住院次数 */
    @Excel(name = "ZYCS")
    private String zycs;

    /** 病案号 */
    @Excel(name = "BAH")
    private String bah;

    /** 职业 */
    @Excel(name = "ZY")
    private String zy;

    /** 现住址 */
    @Excel(name = "XZZ")
    private String xzz;

    /** 电话 */
    @Excel(name = "DH")
    private String dh;

    /** 邮编1 */
    @Excel(name = "YB1")
    private String yb1;

    /** 户口地址 */
    @Excel(name = "HKDZ")
    private String hkdz;

    /** 邮编2 */
    @Excel(name = "YB2")
    private String yb2;

    /** 工作单位及地址 */
    @Excel(name = "GZDWJDZ")
    private String gzdwjdz;

    /** 单位电话 */
    @Excel(name = "DWDH")
    private String dwdh;

    /** 邮编3 */
    @Excel(name = "YB3")
    private String yb3;

    /** 联系人姓名 */
    @Excel(name = "LXRXM")
    private String lxrxm;

    /** 关系 */
    @Excel(name = "GX")
    private String gx;

    /** 地址 */
    @Excel(name = "DZ")
    private String dz;

    /** 电话2 */
    @Excel(name = "DH2")
    private String dh2;

    /** 入院途径 */
    @Excel(name = "RYTJ")
    private String rytj;

    /** 入院时间 */
    @Excel(name = "RYSJ")
    @DateFormat(pattern = "yyyy-MM-dd")
    private String rysj;

    /** 入院时间(时) */
    @Excel(name = "RYSJS")
    private String rysjs;

    /** 入院科别 */
    @Excel(name = "RYKB")
    private String rykb;

    /** 入院病房 */
    @Excel(name = "RYBF")
    private String rybf;

    /** 转科科别 */
    @Excel(name = "ZKKB")
    private String zkkb;

    /** 出院时间 */
    @Excel(name = "CYSJ")
    @DateFormat(pattern = "yyyy-MM-dd")
    private String cysj;

    /** 出院时间(时) */
    @Excel(name = "CYSJS")
    private String cysjs;

    /** 出院科别 */
    @Excel(name = "CYKB")
    private String cykb;

    /** 出院病房 */
    @Excel(name = "CYBF")
    private String cybf;

    /** 实际住院天数 */
    @Excel(name = "SJZYTS")
    private String sjzyts;

    /** 门诊诊断 */
    @Excel(name = "MZZD")
    private String mzzd;

    /** 疾病编码 */
    @Excel(name = "JBBM")
    private String jbbm;

    /** 主要诊断 */
    @Excel(name = "ZYZD")
    private String zyzd;

    /** 疾病编码 */
    @Excel(name = "JBDM")
    private String jbdm;

    /** 入院病情 */
    @Excel(name = "RYBQ")
    private String rybq;

    /** 其他诊断 */
    @Excel(name = "QTZD8")
    private String qtzd8;

    /** 疾病编码 */
    @Excel(name = "JBDM8")
    private String jbdm8;

    /** 入院病情 */
    @Excel(name = "RYBQ8")
    private String rybq8;

    /** 其他诊断 */
    @Excel(name = "QTZD1")
    private String qtzd1;

    /** 疾病编码 */
    @Excel(name = "JBDM1")
    private String jbdm1;

    /** 入院病情 */
    @Excel(name = "RYBQ1")
    private String rybq1;

    /** 其他诊断 */
    @Excel(name = "QTZD9")
    private String qtzd9;

    /** 疾病编码 */
    @Excel(name = "JBDM9")
    private String jbdm9;

    /** 入院病情 */
    @Excel(name = "RYBQ9")
    private String rybq9;

    /** 其他诊断 */
    @Excel(name = "QTZD2")
    private String qtzd2;

    /** 疾病编码 */
    @Excel(name = "JBDM2")
    private String jbdm2;

    /** 入院病情 */
    @Excel(name = "RYBQ2")
    private String rybq2;

    /** 其他诊断 */
    @Excel(name = "QTZD10")
    private String qtzd10;

    /** 疾病编码 */
    @Excel(name = "JBDM10")
    private String jbdm10;

    /** 入院病情 */
    @Excel(name = "RYBQ10")
    private String rybq10;

    /** 其他诊断 */
    @Excel(name = "QTZD3")
    private String qtzd3;

    /** 疾病编码 */
    @Excel(name = "JBDM3")
    private String jbdm3;

    /** 入院病情 */
    @Excel(name = "RYBQ3")
    private String rybq3;

    /** 其他诊断 */
    @Excel(name = "QTZD11")
    private String qtzd11;

    /** 疾病编码 */
    @Excel(name = "JBDM11")
    private String jbdm11;

    /** 入院病情 */
    @Excel(name = "RYBQ11")
    private String rybq11;

    /** 其他诊断 */
    @Excel(name = "QTZD4")
    private String qtzd4;

    /** 疾病编码 */
    @Excel(name = "JBDM4")
    private String jbdm4;

    /** 入院病情 */
    @Excel(name = "RYBQ4")
    private String rybq4;

    /** 其他诊断 */
    @Excel(name = "QTZD12")
    private String qtzd12;

    /** 疾病编码 */
    @Excel(name = "JBDM12")
    private String jbdm12;

    /** 入院病情 */
    @Excel(name = "RYBQ12")
    private String rybq12;

    /** 其他诊断 */
    @Excel(name = "QTZD5")
    private String qtzd5;

    /** 疾病编码 */
    @Excel(name = "JBDM5")
    private String jbdm5;

    /** 入院病情 */
    @Excel(name = "RYBQ5")
    private String rybq5;

    /** 其他诊断 */
    @Excel(name = "QTZD13")
    private String qtzd13;

    /** 疾病编码 */
    @Excel(name = "JBDM13")
    private String jbdm13;

    /** 入院病情 */
    @Excel(name = "RYBQ13")
    private String rybq13;

    /** 其他诊断 */
    @Excel(name = "QTZD6")
    private String qtzd6;

    /** 疾病编码 */
    @Excel(name = "JBDM6")
    private String jbdm6;

    /** 入院病情 */
    @Excel(name = "RYBQ6")
    private String rybq6;

    /** 其他诊断 */
    @Excel(name = "QTZD14")
    private String qtzd14;

    /** 疾病编码 */
    @Excel(name = "JBDM14")
    private String jbdm14;

    /** 入院病情 */
    @Excel(name = "RYBQ14")
    private String rybq14;

    /** 其他诊断 */
    @Excel(name = "QTZD7")
    private String qtzd7;

    /** 疾病编码 */
    @Excel(name = "JBDM7")
    private String jbdm7;

    /** 入院病情 */
    @Excel(name = "RYBQ7")
    private String rybq7;

    /** 其他诊断 */
    @Excel(name = "QTZD15")
    private String qtzd15;

    /** 疾病编码 */
    @Excel(name = "JBDM15")
    private String jbdm15;

    /** 入院病情 */
    @Excel(name = "RYBQ15")
    private String rybq15;

    /** 中毒原因 */
    @Excel(name = "WBYY")
    private String wbyy;

    /** 疾病编码 */
    @Excel(name = "H23")
    private String h23;

    /** 病理诊断 */
    @Excel(name = "BLZD")
    private String blzd;

    /** 疾病编码 */
    @Excel(name = "JBMM")
    private String jbmm;

    /** 病理号 */
    @Excel(name = "BLH")
    private String blh;

    /** 药物过敏 */
    @Excel(name = "YWGM")
    private String ywgm;

    /** 过敏药物疾病 */
    @Excel(name = "GMYW")
    private String gmyw;

    /** 死亡患者尸检 */
    @Excel(name = "SWHZSJ")
    private String swhzsj;

    /** 血型 */
    @Excel(name = "XX")
    private String xx;

    /** rh */
    @Excel(name = "RH")
    private String rh;

    /** 科主任 */
    @Excel(name = "KZR")
    private String kzr;

    /** 主（副主）任医师代码 */

    private String b23c;

    /** 主任医师 */
    @Excel(name = "ZRYS")
    private String zrys;

    /** 主治医师代码 */

    private String b24c;

    /** 主治医师 */
    @Excel(name = "ZZYS")
    private String zzys;

    /** 住院医师代码 */

    private String b25c;

    /** 住院医师 */
    @Excel(name = "ZYYS")
    private String zyys;

    /** 责任护士代码 */

    private String b26c;

    /** 责任护士 */
    @Excel(name = "ZRHS")
    private String zrhs;

    /** 进修医师代码 */

    private String b27c;

    /** 进修医师 */
    @Excel(name = "JXYS")
    private String jxys;

    /** 实习医师 */
    @Excel(name = "SXYS")
    private String sxys;

    /** 编码员代码 */

    private String b29c;

    /** 编码员 */
    @Excel(name = "BMY")
    private String bmy;

    /** 病案质量 */
    @Excel(name = "BAZL")
    private String bazl;

    /** 质控医师代码 */

    private String b31c;

    /** 质控医师 */
    @Excel(name = "ZKYS")
    private String zkys;

    /** 质控护师代码 */

    private String b32c;

    /** 质控护士 */
    @Excel(name = "ZKHS")
    private String zkhs;

    /** 质控日期 */
    @Excel(name = "ZKRQ")
    private String zkrq;

    /** 手术及操作编码 */
    @Excel(name = "SSJCZBM1")
    private String ssjczbm1;

    /** 手术及操作日期 */
    @Excel(name = "SSJCZRQ1")
    private String ssjczrq1;

    /** 手术级别 */
    @Excel(name = "SSJB1")
    private String ssjb1;

    /** 手术及操作名称 */
    @Excel(name = "SSJCZMC1")
    private String ssjczmc1;

    /** 术者 */
    @Excel(name = "SZ1")
    private String sz1;

    /** I助 */
    @Excel(name = "YZ1")
    private String yz1;

    /** II助 */
    @Excel(name = "EZ1")
    private String ez1;

    /** 切口等级 */
    @Excel(name = "QKDJ1")
    private String qkdj1;

    /** 切口愈合类别 */
    @Excel(name = "QKYHLB1")
    private String qkyhlb1;

    /** 麻醉方式 */
    @Excel(name = "MZFS1")
    private String mzfs1;

    /** 麻醉医师 */
    @Excel(name = "MZYS1")
    private String mzys1;

    /** 手术及操作编码 */
    @Excel(name = "SSJCZBM2")
    private String ssjczbm2;

    /** 手术及操作日期 */
    @Excel(name = "SSJCZRQ2")
    private String ssjczrq2;

    /** 手术级别 */
    @Excel(name = "SSJB2")
    private String ssjb2;

    /** 手术及操作名称 */
    @Excel(name = "SSJCZMC2")
    private String ssjczmc2;

    /** 术者 */
    @Excel(name = "SZ2")
    private String sz2;

    /** I助 */
    @Excel(name = "YZ2")
    private String yz2;

    /** II助 */
    @Excel(name = "EZ2")
    private String ez2;

    /** 切口等级 */
    @Excel(name = "QKDJ2")
    private String qkdj2;

    /** 切口愈合类别 */
    @Excel(name = "QKYHLB2")
    private String qkyhlb2;

    /** 麻醉方式 */
    @Excel(name = "MZFS2")
    private String mzfs2;

    /** 麻醉医师 */
    @Excel(name = "MZYS2")
    private String mzys2;

    /** 手术及操作编码 */
    @Excel(name = "SSJCZBM3")
    private String ssjczbm3;

    /** 手术及操作日期 */
    @Excel(name = "SSJCZRQ3")
    private String ssjczrq3;

    /** 手术级别 */
    @Excel(name = "SSJB3")
    private String ssjb3;

    /** 手术及操作名称 */
    @Excel(name = "SSJCZMC3")
    private String ssjczmc3;

    /** 术者 */
    @Excel(name = "SZ3")
    private String sz3;

    /** I助 */
    @Excel(name = "YZ3")
    private String yz3;

    /** II助 */
    @Excel(name = "EZ3")
    private String ez3;

    /** 切口等级 */
    @Excel(name = "QKDJ3")
    private String qkdj3;

    /** 切口愈合类别 */
    @Excel(name = "QKYHLB3")
    private String qkyhlb3;

    /** 麻醉方式 */
    @Excel(name = "MZFS3")
    private String mzfs3;

    /** 麻醉医师 */
    @Excel(name = "MZYS3")
    private String mzys3;

    /** 手术及操作编码 */
    @Excel(name = "SSJCZBM4")
    private String ssjczbm4;

    /** 手术及操作日期 */
    @Excel(name = "SSJCZRQ4")
    private String ssjczrq4;

    /** 手术级别 */
    @Excel(name = "SSJB4")
    private String ssjb4;

    /** 手术及操作名称 */
    @Excel(name = "SSJCZMC4")
    private String ssjczmc4;

    /** 术者 */
    @Excel(name = "SZ4")
    private String sz4;

    /** I助 */
    @Excel(name = "YZ4")
    private String yz4;

    /** II助 */
    @Excel(name = "EZ4")
    private String ez4;

    /** 切口等级 */
    @Excel(name = "QKDJ4")
    private String qkdj4;

    /** 切口愈合类别 */
    @Excel(name = "QKYHLB4")
    private String qkyhlb4;

    /** 麻醉方式 */
    @Excel(name = "MZFS4")
    private String mzfs4;

    /** 麻醉医师 */
    @Excel(name = "MZYS4")
    private String mzys4;

    /** 手术及操作编码 */
    @Excel(name = "SSJCZBM5")
    private String ssjczbm5;

    /** 手术及操作日期 */
    @Excel(name = "SSJCZRQ5")
    private String ssjczrq5;

    /** 手术级别 */
    @Excel(name = "SSJB5")
    private String ssjb5;

    /** 手术及操作名称 */
    @Excel(name = "SSJCZMC5")
    private String ssjczmc5;

    /** 术者 */
    @Excel(name = "SZ5")
    private String sz5;

    /** I助 */
    @Excel(name = "YZ5")
    private String yz5;

    /** II助 */
    @Excel(name = "EZ5")
    private String ez5;

    /** 切口等级 */
    @Excel(name = "QKDJ5")
    private String qkdj5;

    /** 切口愈合类别 */
    @Excel(name = "QKYHLB5")
    private String qkyhlb5;

    /** 麻醉方式 */
    @Excel(name = "MZFS5")
    private String mzfs5;

    /** 麻醉医师 */
    @Excel(name = "MZYS5")
    private String mzys5;

    /** 手术及操作编码 */
    @Excel(name = "SSJCZBM6")
    private String ssjczbm6;

    /** 手术及操作日期 */
    @Excel(name = "SSJCZRQ6")
    private String ssjczrq6;

    /** 手术级别 */
    @Excel(name = "SSJB6")
    private String ssjb6;

    /** 手术及操作名称 */
    @Excel(name = "SSJCZMC6")
    private String ssjczmc6;

    /** 术者 */
    @Excel(name = "SZ6")
    private String sz6;

    /** I助 */
    @Excel(name = "YZ6")
    private String yz6;

    /** II助 */
    @Excel(name = "EZ6")
    private String ez6;

    /** 切口等级 */
    @Excel(name = "QKDJ6")
    private String qkdj6;

    /** 切口愈合类别 */
    @Excel(name = "QKYHLB6")
    private String qkyhlb6;

    /** 麻醉方式 */
    @Excel(name = "MZFS6")
    private String mzfs6;

    /** 麻醉医师 */
    @Excel(name = "MZYS6")
    private String mzys6;

    /** 手术及操作编码 */
    @Excel(name = "SSJCZBM7")
    private String ssjczbm7;

    /** 手术及操作日期 */
    @Excel(name = "SSJCZRQ7")
    private String ssjczrq7;

    /** 手术级别 */
    @Excel(name = "SSJB7")
    private String ssjb7;

    /** 手术及操作名称 */
    @Excel(name = "SSJCZMC7")
    private String ssjczmc7;

    /** 术者 */
    @Excel(name = "SZ7")
    private String sz7;

    /** I助 */
    @Excel(name = "YZ7")
    private String yz7;

    /** II助 */
    @Excel(name = "EZ7")
    private String ez7;

    /** 切口等级 */
    @Excel(name = "QKDJ7")
    private String qkdj7;

    /** 切口愈合类别 */
    @Excel(name = "QKYHLB7")
    private String qkyhlb7;

    /** 麻醉方式 */
    @Excel(name = "MZFS7")
    private String mzfs7;

    /** 麻醉医师 */
    @Excel(name = "MZYS7")
    private String mzys7;

    /** 离院方式 */
    @Excel(name = "LYFS")
    private String lyfs;

    /** 医嘱转院,医疗机构 */
    @Excel(name = "YZZY_YLJG")
    private String yzzy_yljg;

    /** 医嘱转院社区卫生院,医疗机构 */
    @Excel(name = "WSY_YLJG")
    private String wsy_yljg;

    /** 是否有出院31天在住院计划 */
    @Excel(name = "SFZZYJH")
    private String sfzzyjh;

    /** 目的 */
    @Excel(name = "MD")
    private String md;

    /** 住院医疗类型 */

    private String b38;

    /** 治疗类别 */

    private String b39;

    /** 特级护理天数 */

    private String b44;

    /** 一级护理天数 */

    private String b45;

    /** 二级护理天数 */

    private String b46;

    /** 三级护理天数 */

    private String b47;

    /** 转院拟接受机构代码 */

    private String b48;

    /** 转院拟接受机构名称 */

    private String b49;

    /** 颅脑损伤患者昏迷入院前时间(天) */
    @Excel(name = "RYQ_T")
    private String ryq_t;

    /** 颅脑损伤患者昏迷入院前时间(小时) */
    @Excel(name = "RYQ_XS")
    private String ryq_xs;

    /** 颅脑损伤患者昏迷入院前时间(分) */
    @Excel(name = "RYQ_F")
    private String ryq_f;

    /** 颅脑损伤患者昏迷入院后时间(天) */
    @Excel(name = "RYH_T")
    private String ryh_t;

    /** 颅脑损伤患者昏迷入院后时间(小时) */
    @Excel(name = "RYH_XS")
    private String ryh_xs;

    /** 颅脑损伤患者昏迷入院后时间(分) */
    @Excel(name = "RYH_F")
    private String ryh_f;

    /** 住院费用:总费用 */
    @Excel(name = "ZFY")
    private String zfy;

    /** 自付金额 */
    @Excel(name = "ZFJE")
    private String zfje;

    /** 一般医疗服务费 */
    @Excel(name = "YLFUF")
    private String ylfuf;

    /** 一般治疗操作费 */
    @Excel(name = "ZLCZF")
    private String zlczf;

    /** 护理费 */
    @Excel(name = "NURSFEE")
    private String nursfee;

    /** 其他费用 */
    @Excel(name = "OTH_FEE_COM")
    private String oth_fee_com;

    /** 病理诊断费 */
    @Excel(name = "CAS_DIAG_FEE")
    private String cas_diag_fee;

    /** 实验室诊断费 */
    @Excel(name = "LAB_DIAG_FEE")
    private String lab_diag_fee;

    /** 影像学诊断费 */
    @Excel(name = "RDHY_DIAG_FEE")
    private String rdhy_diag_fee;

    /** 临床诊断项目费 */
    @Excel(name = "CLNC_DIAG_ITEM_FEE")
    private String clnc_diag_item_fee;

    /** 非手术治疗项目费 */
    @Excel(name = "NSRGTRT_ITEM_FEE")
    private String nsrgtrt_item_fee;

    /** 临床物理治疗费 */
    @Excel(name = "WLZLF")
    private String wlzlf;

    /** 手术治疗费 */
    @Excel(name = "OPRN_TREAT_FEE")
    private String oprn_treat_fee;

    /** 麻醉费 */
    @Excel(name = "MAF")
    private String maf;

    /** 手术费 */
    @Excel(name = "SSF")
    private String ssf;

    /** 康复费 */
    @Excel(name = "RHAB_FEE")
    private String rhab_fee;

    /** 中医治疗费 */
    @Excel(name = "TCM_TREAT_FEE")
    private String tcm_treat_fee;

    /** 西药费 */
    @Excel(name = "WEST_FEE")
    private String west_fee;

    /** 抗菌药物费 */
    @Excel(name = "KJYWF")
    private String kjywf;

    /** 中成药费 */
    @Excel(name = "TCMPAT_FEE")
    private String tcmpat_fee;

    /** 中草药费 */
    @Excel(name = "TCMHERB")
    private String tcmherb;

    /** 血费 */
    @Excel(name = "BLO_FEE")
    private String blo_fee;

    /** 白蛋白类制品费 */
    @Excel(name = "BDBLZPF")
    private String bdblzpf;

    /** 球蛋白类制品费 */
    @Excel(name = "QDBLZPF")
    private String qdblzpf;

    /** 凝血因子类制品费 */
    @Excel(name = "NXYZLZPF")
    private String nxyzlzpf;

    /** 细胞因子类制品费 */
    @Excel(name = "XBYZLZPF")
    private String xbyzlzpf;

    /** 检查一次性医用材料费 */
    @Excel(name = "HCYYCLF")
    private String hcyyclf;

    /** 治疗一次性医用材料费 */
    @Excel(name = "YYCLF")
    private String yyclf;

    /** 手术一次性医用材料费 */
    @Excel(name = "YCXYYCLF")
    private String ycxyyclf;

    /** 其他费 */
    @Excel(name = "OTH_FEE")
    private String oth_fee;

    /** 医疗机构ID */

    private String hospital_id;

    /** 数据处理日志ID */
    private String data_log_id;

    /** 数据有效标志（0：有效，1：有效） */
    private String active_flag;

    /** 批次号 */
    private String batchNum;

    /** 医保结算等级 */

    private String a50;

    /** 医保编号 */

    private String a51;

    /** 申报时间 */

    private String a52;

    /** 患者证件类型 */

    private String a53;

    /** 医保类型 */
    @Excel(name = "YBLX")
    private String a54;

    /** 特殊人员类型 */

    private String a55;

    /** 参保地 */

    private String a56;

    /** 新生儿入院类型 */

    private String a57;

    /** 结算清单流水号 */

    private String a58;

    /** 入院科别名称 */

    private String b13n;

    /** 入院病房名称 */

    private String b14n;

    /** 出院科别名称 */

    private String b16n;

    /** 出院病房名称 */

    private String b17n;

    /** 科主任代码 */

    private String b22c;

    /** 主诊医师代码 */

    private String b51c;

    /** 主诊医师姓名 */

    private String b52n;

    /** 损伤、中毒外部原因编码 */

    private String c12c;

    /** 损伤、中毒外部原因名称 */

    private String c13n;

    /** 死亡患者尸检 */

    private String c34c;

    /** 门（急）诊诊断编码（中医） */

    private String c35c;

    /** 门（急）诊诊断名称（中医） */

    private String c36n;

    /** 出院主要诊断编码（中医） */

    private String c37c;

    /** 出院主要诊断名称（中医） */

    private String c38n;

    /** 出院主要诊断入院病情（中医） */

    private String c39c;

    /** 呼吸机使用时间（天） */

    private String c42;

    /** 呼吸机使用时间（小时） */

    private String c43;

    /** 呼吸机使用时间（分钟） */

    private String c44;

    /** 输血品种 */

    private String c45;

    /** 输血量 */

    private String c46;

    /** 输血计量单位 */

    private String c47;

    /** 出院主要诊断出院病情（西医） */

    private String c48c;

    /** 出院主要诊断出院病情（中医） */

    private String c49c;

    /** 业务流水号 */

    private String d35;

    /** 结算开始时间 */

    private String d36;

    /** 结算结束时间 */

    private String d37;

    /** 票据代码 */

    private String d38;

    /** 票据号 */

    private String d39;

    /** 个人自付 */

    private String d54;

    /** 个人自费 */

    private String d55;

    /** 个人账户支付 */

    private String d56;

    /** 个人现金支付 */

    private String d57;

    /** 医保支付方式 */

    private String d58;

    /** 医疗机构填报部门 */

    private String d59;

    /** 医疗机构填报人 */

    private String d60;

    /** 中医辨证论治费 */

    private String d61;

    /** 中医辨证论治会诊费 */

    private String d62;

    /** 中医类(中医和名族医医疗服务)中医诊断 */

    private String d63;

    /** 中医其他 */

    private String d64;

    /** 中医特殊调配加工 */

    private String d65;

    /** 辨证施膳 */

    private String d66;

    /** 医疗机构中药制剂费 */

    private String d67;

    /** 针刺与灸法 */

    private String d68;

    /** 中医肛肠治疗 */

    private String d69;

    /** 中医骨伤 */

    private String d70;

    /** 中医推拿治疗 */

    private String d71;

    /** 中医特殊治疗 */

    private String d72;

    /** 中医外治 */

    private String d73;

    /** 床位费 */
    @Excel(name = "床位费")
    private String cwf;

    /** 放射检查费 */

    private String fsjcf;

    /** 检查费 */

    private String jcf;

    /** 化验费 */

    private String hyf;

    /** 卫生材料费 */

    private String wsclf;

    /** 输氧费 */

    private String syf;

    /** 治疗费 */

    private String treat_fee;

    /** 诊查费 */

    private String zcf;

    /** 原始统筹总费用 */
    @Excel(name = "C_002")
    private String originalOverallCost;

}
