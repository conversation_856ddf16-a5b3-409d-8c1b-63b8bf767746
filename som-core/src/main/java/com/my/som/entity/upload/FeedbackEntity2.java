package com.my.som.entity.upload;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.my.som.common.annotations.DateFormat;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @author: zyd
 * @version 1.0
 * @date: 2023-07-31
 * @description: 分组反馈数据
 */
@Data
public class FeedbackEntity2 {

    /** 病组编码 */
    @Excel(name = "病组编码")
    private String disGpCodg;

    /** 病组名称 */
    @Excel(name = "病组名称")
    private String disGpName;

    /** 辅助目录-肿瘤严重程度 */
    @Excel(name = "肿瘤严重程度辅助目录")
    private String asstListTmorSevDeg;

    /** 辅助目录-年龄 */
    @Excel(name = "年龄辅助目录")
    private String asstListAgeGrp;

    /** 辅助目录-疾病严重程度 */
    @Excel(name = "疾病严重程度辅助目录")
    private String asstListDiseSevDeg;

    /** 就诊ID */
    @Excel(name = "就诊ID")
    private String mdtrtId;

    /** 病案号 */
    @Excel(name = "病案号")
    private String medcasNo;

    /** 姓名 */
    @Excel(name = "姓名")
    private String psmName;

    /** 入院时间 */
    @Excel(name = "入院时间")
    @DateFormat("yyyy-MM-dd")
    private String admTime;

    /** 出院时间 */
    @Excel(name = "出院时间")
    @DateFormat("yyyy-MM-dd")
    private String dscgTime;

    /** 结算时间 */
    @Excel(name = "结算时间")
    @DateFormat("yyyy-MM-dd")
    private String setlTime;

    /** 出院科室 */
    @Excel(name = "出院科室")
    private String deptName;

    /** 医生名称 */
    @Excel(name = "医生名称")
    private String drName;

    /** 结算点数 */
    @Excel(name = "结算点数")
    private BigDecimal setlPtNum;

    /** 每点数费用 */
    @Excel(name = "每点数费用")
    private BigDecimal ptNumFee;

    /** 总费用 */
    @Excel(name = "总费用")
    private BigDecimal sumfee;

    /** 统筹费用 */
    @Excel(name = "统筹费用")
    private BigDecimal poolFee;

    /** 病组总费用 */
    @Excel(name = "病组总费用")
    private BigDecimal disGpSumfee;

    /** 病组统筹费用 */
    @Excel(name = "病组统筹费用")
    private BigDecimal disGpPoolFee;

    /** 结算患者类型 */
    @Excel(name = "结算患者类型")
    private String  setlPatnType;

    /** 参保类型 */
    @Excel(name = "参保类型")
    private String insuredType;

    /** 是否入组 */
    private String isInGroup;

    /** 批次号 */
    private String batchNum;

    /** 系统入组编码 */
    private String sysGroupCode;

    /** 系统入组名称 */
    private String sysGroupName;

    /** 辅助目录-年龄 */
    private String sysAuxiliaryAge;

    /** 辅助目录-疾病 */
    private String sysAuxiliaryIllness;

    /** 辅助目录-肿瘤 */
    private String sysAuxiliaryTumour;

    /** 是否匹配 */
    private String matched;

    /** 住院总费用 */
    private BigDecimal sysTotalCost;

    /** 结算分值 */
    private BigDecimal sysSelPoint;

    /** 差额 */
    private BigDecimal balance;

    /** 病案号 */
    private String a48;
}
