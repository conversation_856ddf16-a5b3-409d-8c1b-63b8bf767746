package com.my.som.entity.upload;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Getter;
import lombok.Setter;

/**
 * @author: zyd
 * @version 1.0
 * @date: 2023-07-31
 * @description: 清单结算数据
 */
@Getter
@Setter
public class SettleListData {

    /** 唯一ID */
    @Excel(name = "唯一ID")
    private String k00;

    /** 姓名 */
    @Excel(name = "姓名")
    private String a11;

    /** 病案号 */
    @Excel(name = "病案号")
    private String a48;

    /** 入院时间 */
    @Excel(name = "入院时间")
    private String b12;

    /** 出院时间 */
    @Excel(name = "出院时间")
    private String b15;

    /** 结算时间 */
    @Excel(name = "结算时间")
    private String d37;

    /** 期号 */
    private String ym;

    /** 医疗机构ID */
    private String hospitalId;
}
