package com.my.som.service.hospitalAnalysis;

import com.my.som.dto.hospitalAnalysis.HospitalAnalysisQueryParam;
import com.my.som.vo.common.CommonObject;
import com.my.som.vo.hospitalAnalysis.DiseaseAnalysisInfo;

import java.util.List;

/**
 * Service
 * Created by sky on 2020/3/23.
 */
public interface DiseaseAnalysisService {
    /**
     * 查询医院病种分析主要信息
     * @param queryParam
     * @return
     */
    List<DiseaseAnalysisInfo> list(HospitalAnalysisQueryParam queryParam, Integer pageSize, Integer pageNum);

    /**
     * 查询医院病种分析统计信息
     * @param queryParam
     * @return
     */
    List<CommonObject> getCountInfo(HospitalAnalysisQueryParam queryParam);

}
