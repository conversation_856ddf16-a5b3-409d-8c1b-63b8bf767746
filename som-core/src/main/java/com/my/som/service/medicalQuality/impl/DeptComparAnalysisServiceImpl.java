package com.my.som.service.medicalQuality.impl;

import com.my.som.dao.medicalQuality.DeptComparAnalysisDao;
import com.my.som.dto.medicalQuality.CompareAnalysisDto;
import com.my.som.service.medicalQuality.DeptComparAnalysisService;
import com.my.som.vo.medicalQuality.DeptComparAnalysisVo;
import com.my.som.vo.medicalQuality.DrgDeptComparAnalysisVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

@Service
public class DeptComparAnalysisServiceImpl implements DeptComparAnalysisService {
    @Autowired
    private DeptComparAnalysisDao deptComparAnalysisDao;

    @Override
    public DeptComparAnalysisVo getData(CompareAnalysisDto dto) {
        return deptComparAnalysisDao.getData(dto);
    }

    @Override
    public DeptComparAnalysisVo getCost(CompareAnalysisDto dto) {
        return deptComparAnalysisDao.getCost(dto);
    }

    @Override
    public DrgDeptComparAnalysisVo getDrgData(CompareAnalysisDto dto) {
        return deptComparAnalysisDao.getDrgData(dto);
    }

    public DeptComparAnalysisVo getPayCostData(CompareAnalysisDto dto) {
        DeptComparAnalysisVo payCostData = deptComparAnalysisDao.getPayCostData(dto);
        payCostData.setPredictCost(payCostData.getCxpredictCost().add(payCostData.getCzpredictCost()));
        payCostData.setPayCost(payCostData.getCxtotalCost().add(payCostData.getCztotalCost()));
        payCostData.setCount(payCostData.getCxtotalCount().add(payCostData.getCztotalCount()));
        payCostData.setYccy(payCostData.getPredictCost().subtract(payCostData.getPayCost()).setScale(2,BigDecimal.ROUND_HALF_UP));
        payCostData.setCxyccy(payCostData.getCxpredictCost().subtract(payCostData.getCxtotalCost()).setScale(2,BigDecimal.ROUND_HALF_UP));
        payCostData.setCzyccy(payCostData.getCzpredictCost().subtract(payCostData.getCztotalCost()).setScale(2,BigDecimal.ROUND_HALF_UP));
        if(payCostData.getCxpredictCost().compareTo(BigDecimal.ZERO) == 0){
            payCostData.setCxcyb(new BigDecimal(0));
        }else{
        payCostData.setCxcyb((payCostData.getCxpredictCost().subtract(payCostData.getCxtotalCost()))
                .divide(payCostData.getCxpredictCost(),4,BigDecimal.ROUND_HALF_UP)
                .setScale(2, BigDecimal.ROUND_HALF_UP));}

        if(payCostData.getCzpredictCost().compareTo(BigDecimal.ZERO) == 0){
            payCostData.setCzcyb(new BigDecimal(0));
        }else{
            payCostData.setCzcyb((payCostData.getCzpredictCost().subtract(payCostData.getCztotalCost()))
                    .divide(payCostData.getCzpredictCost(),4,BigDecimal.ROUND_HALF_UP)
                    .setScale(2, BigDecimal.ROUND_HALF_UP));}
        if(payCostData.getPredictCost().compareTo(BigDecimal.ZERO) == 0){
            payCostData.setCyb(new BigDecimal(0));
        }else{
            payCostData.setCyb((payCostData.getPredictCost().subtract(payCostData.getPayCost()))
                    .divide(payCostData.getPredictCost(),4,BigDecimal.ROUND_HALF_UP)
                    .setScale(2, BigDecimal.ROUND_HALF_UP));}
        payCostData.setPredictCost(payCostData.getPredictCost().setScale(2,BigDecimal.ROUND_HALF_UP));
        payCostData.setCxpredictCost(payCostData.getCxpredictCost().setScale(2,BigDecimal.ROUND_HALF_UP));
        payCostData.setCzpredictCost(payCostData.getCzpredictCost().setScale(2,BigDecimal.ROUND_HALF_UP));
        return payCostData;
    }

    @Override
    public DrgDeptComparAnalysisVo getDrgPayCostData(CompareAnalysisDto dto) {
        DrgDeptComparAnalysisVo drgPayCostData = deptComparAnalysisDao.getDrgPayCostData(dto);
        drgPayCostData.setPredictCost(drgPayCostData.getCxpredictCost().add(drgPayCostData.getCzpredictCost()));
        drgPayCostData.setPayCost(drgPayCostData.getCxtotalCost().add(drgPayCostData.getCztotalCost()));
        drgPayCostData.setCount(drgPayCostData.getCxtotalCount().add(drgPayCostData.getCztotalCount()));
        drgPayCostData.setYccy(drgPayCostData.getPredictCost().subtract(drgPayCostData.getPayCost()).setScale(2,BigDecimal.ROUND_HALF_UP));
        drgPayCostData.setCxyccy(drgPayCostData.getCxpredictCost().subtract(drgPayCostData.getCxtotalCost()).setScale(2,BigDecimal.ROUND_HALF_UP));
        drgPayCostData.setCzyccy(drgPayCostData.getCzpredictCost().subtract(drgPayCostData.getCztotalCost()).setScale(2,BigDecimal.ROUND_HALF_UP));
        if(drgPayCostData.getCxpredictCost().compareTo(BigDecimal.ZERO) == 0){
            drgPayCostData.setCxcyb(new BigDecimal(0));
        }else{
            drgPayCostData.setCxcyb((drgPayCostData.getCxpredictCost().subtract(drgPayCostData.getCxtotalCost()))
                    .divide(drgPayCostData.getCxpredictCost(),4,BigDecimal.ROUND_HALF_UP)
                    .setScale(2, BigDecimal.ROUND_HALF_UP));}

        if(drgPayCostData.getCzpredictCost().compareTo(BigDecimal.ZERO) == 0){
            drgPayCostData.setCzcyb(new BigDecimal(0));
        }else{
            drgPayCostData.setCzcyb((drgPayCostData.getCzpredictCost().subtract(drgPayCostData.getCztotalCost()))
                    .divide(drgPayCostData.getCzpredictCost(),4,BigDecimal.ROUND_HALF_UP)
                    .setScale(2, BigDecimal.ROUND_HALF_UP));}
        if(drgPayCostData.getPredictCost().compareTo(BigDecimal.ZERO) == 0){
            drgPayCostData.setCyb(new BigDecimal(0));
        }else{
            drgPayCostData.setCyb((drgPayCostData.getPredictCost().subtract(drgPayCostData.getPayCost()))
                    .divide(drgPayCostData.getPredictCost(),4,BigDecimal.ROUND_HALF_UP)
                    .setScale(2, BigDecimal.ROUND_HALF_UP));}
        drgPayCostData.setPredictCost(drgPayCostData.getPredictCost().setScale(2,BigDecimal.ROUND_HALF_UP));
        drgPayCostData.setCxpredictCost(drgPayCostData.getCxpredictCost().setScale(2,BigDecimal.ROUND_HALF_UP));
        drgPayCostData.setCzpredictCost(drgPayCostData.getCzpredictCost().setScale(2,BigDecimal.ROUND_HALF_UP));
        return drgPayCostData;
    }

}
