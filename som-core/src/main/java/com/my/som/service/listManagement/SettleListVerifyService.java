package com.my.som.service.listManagement;

import com.my.som.dto.listManagement.PatientInfoDto;
import com.my.som.vo.listManagement.PatientInfoVo;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * @author: zyd
 * @version 1.0
 * @date: 2023-07-31
 * @description:
 */
public interface SettleListVerifyService {

    /**
     * 查询清单需要验证信息
     * @param dto
     * @return
     */
    List<PatientInfoVo> queryVerifyInfo(PatientInfoDto dto);

    /**
     * 修改标识状态
     * @param dto
     */
    void modifyMarkState(PatientInfoDto dto);

    /**
     * 上传结算数据
     * @param file
     * @param dto
     */
    void uploadSettleData(MultipartFile file, PatientInfoDto dto);
}
