package com.my.som.service.integratedQuery.impl;

import com.github.pagehelper.PageHelper;
import com.my.som.common.constant.DrgConst;
import com.my.som.dto.integratedQuery.IntegratedQueryDto;
import com.my.som.mapper.integratedQuery.IntegratedQueryMapper;
import com.my.som.service.integratedQuery.IntegratedQueryService;
import com.my.som.vo.integratedQuery.IntegratedQueryVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @author: zyd
 * @version 1.0
 * @description:
 * @date: 2023-07-31
 */
@Service
public class IntegratedQueryServiceImpl implements IntegratedQueryService {
    @Autowired
    private IntegratedQueryMapper integratedQueryMapper;
    @Override
    public Map<String,List<IntegratedQueryVo>> list(IntegratedQueryDto dto) {
        List<IntegratedQueryVo> list=new ArrayList<>();
        list=integratedQueryMapper.list(dto);
        Map<String,List<IntegratedQueryVo>> mapList=new HashMap<>();
        if(list.size()>0){
            mapList=list.stream().collect(Collectors.groupingBy(IntegratedQueryVo::getFldTypeName));
        }
        return mapList;
    }



    @Override
    public List<Map<String,Object>> queryData(IntegratedQueryDto dto) {
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        setTabName(dto);
        return integratedQueryMapper.queryData(dto);
    }

    private void setTabName(IntegratedQueryDto dto) {
        if (DrgConst.GROUP_TYPE_DIP.equals(dto.getGrperType())) {
            dto.setSomDrgGrpInfo("som_dip_grp_info");
            dto.setBusPatientScore("som_dip_sco");
            dto.setCfgCommon("som_dip_gen_cfg");
            dto.setSomDrgStandard("som_dip_standard");
        } else {
            dto.setSomDrgGrpInfo("som_drg_grp_info");
            dto.setBusPatientScore("som_drg_sco");
            dto.setCfgCommon("som_drg_gen_cfg");
            dto.setSomDrgStandard("som_drg_standard");
        }
    }
}
