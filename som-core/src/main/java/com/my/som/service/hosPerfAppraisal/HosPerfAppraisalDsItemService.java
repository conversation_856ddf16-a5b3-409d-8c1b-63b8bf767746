package com.my.som.service.hosPerfAppraisal;

import com.my.som.dto.hosPerfAppraisal.HosPerfAppraisalDsItemDto;
import com.my.som.vo.hosPerfAppraisal.HosPerfAppraisalDsItemVo;

import java.util.List;

/**
 * @author: zyd
 * @version 1.0
 * @date: 2023-07-31
 * @description:
 */
public interface HosPerfAppraisalDsItemService {

    /**
     * 查询数据
     * @param dto
     * @return
     */
    List<HosPerfAppraisalDsItemVo> queryList(HosPerfAppraisalDsItemDto dto);

    /**
     * 新增
     * @param dto
     */
    void add(HosPerfAppraisalDsItemDto dto);

    /**
     * 修改
     * @param dto
     */
    void update(HosPerfAppraisalDsItemDto dto);

    /**
     * 删除
     * @param dto
     */
    void remove(HosPerfAppraisalDsItemDto dto);
}
