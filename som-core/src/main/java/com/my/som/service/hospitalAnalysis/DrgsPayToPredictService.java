package com.my.som.service.hospitalAnalysis;

import com.my.som.common.api.CommonMapResult;
import com.my.som.dto.dipBusiness.DipBusinessQueryDto;
import com.my.som.dto.hospitalAnalysis.DrgsBusinessQueryDto;
import com.my.som.vo.hospitalAnalysis.DrgsPayToPredictVo;

import java.util.List;

/**
 * @author: zyd
 * @Date 2021/8/20 2:25 下午
 * @Version 1.0
 * @Description: DIP支付预测
 */
public interface DrgsPayToPredictService {

    /**
     * 获取表格数据
     * @param queryParam 查询条件
     * @param pageNum 当前页数
     * @param pageSize 每页大小
     * @return
     */
    List<DrgsPayToPredictVo> getList(DrgsBusinessQueryDto queryParam,
                                     Integer pageSize,
                                     Integer pageNum);

    /**
     * 查询点数信息
     * @param dto 参数
     * @return
     */
    CommonMapResult getListPoint(DrgsBusinessQueryDto dto);


    /**
     * 查询前几月数据
     * @param dto 参数
     */
    CommonMapResult getMonthData(DrgsBusinessQueryDto dto);
}
