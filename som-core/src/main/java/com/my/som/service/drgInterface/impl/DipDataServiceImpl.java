package com.my.som.service.drgInterface.impl;

import com.my.som.common.constant.DrgConst;
import com.my.som.common.exception.AppException;
import com.my.som.common.util.DateUtil;
import com.my.som.common.util.ValidateUtil;
import com.my.som.common.vo.ICDCodeVo;
import com.my.som.dao.common.CommonDao;
import com.my.som.dao.dataHandle.*;
import com.my.som.mapper.common.SomDeptMapper;
import com.my.som.mapper.common.SomDeptPsnRltsMapper;
import com.my.som.mapper.common.SomHospInfoMapper;
import com.my.som.mapper.common.SomMedstffInfoMapper;
import com.my.som.mapper.dataHandle.SomHiInvyBasInfoMapper;
import com.my.som.mapper.dataHandle.SomDataprosLogMapper;
import com.my.som.mapper.handleUpdateData.HandleUpdateDataMapper;
import com.my.som.model.common.*;
import com.my.som.model.dataHandle.*;
import com.my.som.service.drgInterface.DipDataService;
import com.my.som.service.webservice.impl.SomDataAcquisitionInterfaceServiceImpl;
import com.my.som.util.CodeConverterUtil;
import com.my.som.util.SqlServerConnectionUtil;
import com.my.som.util.SysCommonConfigUtil;
import com.my.som.vo.common.SettleListVo;
import com.my.som.vo.webservice.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.DefaultTransactionDefinition;

import java.math.BigDecimal;
import java.sql.Connection;
import java.util.*;

@Service
@Slf4j
public class DipDataServiceImpl implements DipDataService {
    @Autowired
    private PlatformTransactionManager transactionManager;
    @Autowired
    private SomHospInfoMapper busHospitalMapper;
    @Autowired
    private SomDataprosLogMapper stsDataHandleLogMapper;
    @Autowired
    private SomHiInvyBasInfoMapper busSettleListMapper;
    @Autowired
    private SomDeptMapper busDeptMapper;
    @Autowired
    private SomMedstffInfoMapper busMedicalWorkersMapper;
    @Autowired
    private SomDeptPsnRltsMapper busDeptWorkersRelationshipMapper;
    @Autowired
    private BusSettleListDao busSettleListDao;
    @Autowired
    BusDiseaseDiagnosisDao busDiseaseDiagnosisDao;
    @Autowired
    BusOperateDiagnosisDao busOperateDiagnosisDao;
    @Autowired
    BusOutPatientClinicDiagnosisDao busOutPatientClinicDiagnosisDao;
    @Autowired
    BusIcuDao busIcuDao;
    @Autowired
    BusMedicalCostDao busMedicalCostDao;
    @Autowired
    BusFundPayDao busFundPayDao;
    @Autowired
    BusTransfusionDao busTransfusionDao;
    @Autowired
    CommonDao commonDao;
    @Autowired
    HandleUpdateDataMapper handleUpdateDataMapper;
    @Autowired
    SomHospInfoMapper somHospInfoMapper;

    @Value("${webservice.interface.view:#{false}}")
    private boolean enableView;

    private final static Map<String, String> FUND_PAY_FIELD_MAPPING = new HashMap() {
        {
            put("医保统筹基金支付", "hifp_pay");
            put("其他支付", "oth_pay");
            put("大病保险支付", "hifmi_pay");
            put("医疗救助支付", "maf_pay");
            put("公务员医疗补助", "cvlserv_pay");
            put("大额补充", "hifob_pay");
            put("企业补充", "hifes_pay");
        }
    };

    @Override
    public Long uploadMedicalData(List<MedicalUploadVo> medicalDataList) throws Exception {
        //利用编程式事务处理，start transaction
        DefaultTransactionDefinition transactionDefinition = new DefaultTransactionDefinition();
        TransactionStatus transactionStatus = transactionManager.getTransaction(transactionDefinition);
        try {
            if (ValidateUtil.isEmpty(medicalDataList)) {
                throw new AppException("请检查信息是否填写完整，当前无数据!");
            }
            //一个事务内的业务代码
            // 转码
            if (DrgConst.START_FLAG_1.equals(SysCommonConfigUtil.get(DrgConst.SCC_ENABLED_CONTRAST_CODE).toString())) {
                codeConverter(medicalDataList);
            }
            // 批次号
            String batchNum = UUID.randomUUID().toString();
            //先插入数据处理日志信息（这个返回一个data_log_id)，但不做流程处理，result=waited
            //Long data_log_id = addOneDataHandleLog(medicalDataList,hospitalId);
            /* 医保结算清单处理开始 */
            //1.处理医保结算清单基础数据
            handleBusSettleList(medicalDataList, batchNum);
            //查询刚插入的bus_settle_list中k00和settle_list_id的对应关系
//            Map<String,Long> k00Map = getK00ToSettleListId();
            Map<String, Long> k00Map = getK00ToSettleListId(batchNum);

            // 删除原始诊断、手术数据
            ArrayList<String> k00List = new ArrayList<>(k00Map.keySet());
            busSettleListMapper.deleteDiseaseRecord(k00List);
            busSettleListMapper.deleteOperateRecord(k00List);
            busSettleListMapper.deleteBusKeyRecord(k00List);
            busSettleListMapper.deleteSupervisionRecord(k00List);

            //2.处理疾病诊断
            handleBusDiseaseDiagnosis(medicalDataList, k00Map);
            //3.处理手术
            handleBusOperateDiagnosis(medicalDataList, k00Map);
            //4.处理门诊慢特疾病
            handleBusOutpatientClinicDiagnosis(medicalDataList, k00Map);
            //5.处理icu
            handleBusIcu(medicalDataList, k00Map);
            //6.处理医保结算医疗费用
            handleBusMedicalCost(medicalDataList, k00Map);
            //7.处理医保基金支付
            handleBusFundPay(medicalDataList, k00Map);
            //8.处理输血
            handleTransfusion(medicalDataList, k00Map);

            /* 医保结算清单处理结束 */

            // 处理病案首页
            handleMedicalRecord(k00Map);

            // 处理di03数据到bus_fund_pay
//            handlerFundPay("2",k00Map);

            //最后提交事务commit
            transactionManager.commit(transactionStatus);
            return null;
        } catch (Exception e) {
            e.printStackTrace();
            transactionManager.rollback(transactionStatus);
            throw new AppException(e.getMessage());
        }
    }

    /**
     * 处理输血
     *
     * @param medicalDataList
     * @param k00Map
     */
    private void handleTransfusion(List<MedicalUploadVo> medicalDataList, Map<String, Long> k00Map) throws Exception {
        List<Map<String, Object>> transfusionData = new ArrayList<>();
        try {
            for (MedicalUploadVo data : medicalDataList) {
                List<Map<String, Object>> transfusionList = data.getTransfusionList();
                //填充settle_list_id值，然后插入数据
                fillSettleListId(transfusionList, k00Map);
                transfusionData.addAll(transfusionList);
            }
            if (!ValidateUtil.isEmpty(transfusionData)) {
                busTransfusionDao.insertTransfusionData(transfusionData);//批量抽取bus_transfusion
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new Exception("数据采集-医保结算清单输血出现异常！" + e.toString());
        }
    }

    /**
     * 处理诊断原始数据
     *
     * @param medicalDataList
     * @param k00Map
     */
    private void handleBusDiseaseDiagnosisRecord(List<MedicalUploadVo> medicalDataList, Map<String, Long> k00Map) throws Exception {
        List<Map<String, Object>> diseaseData = new ArrayList<>();
        try {
            for (MedicalUploadVo data : medicalDataList) {
                List<Map<String, Object>> diseaseDiagnosisList = data.getDiseaseDiagnosisList();
                diseaseData.addAll(diseaseDiagnosisList);
            }
            if (!ValidateUtil.isEmpty(diseaseData)) {
                busDiseaseDiagnosisDao.insertDiseaseRecordData(diseaseData);
            }

        } catch (Exception e) {
            e.printStackTrace();
            throw new Exception("数据采集-医保结算清单疾病诊断原始数据出现异常！" + e.toString());
        }
    }

    /**
     * 处理手术原始数据
     *
     * @param medicalDataList
     * @param k00Map
     */
    private void handleBusOperateDiagnosisRecord(List<MedicalUploadVo> medicalDataList, Map<String, Long> k00Map) throws Exception {
        List<Map<String, Object>> operateData = new ArrayList<>();
        try {
            for (MedicalUploadVo data : medicalDataList) {
                List<Map<String, Object>> operateDiagnosisList = data.getOperateDiagnosisList();
                operateData.addAll(operateDiagnosisList);
            }
            if (!ValidateUtil.isEmpty(operateData)) {
                busOperateDiagnosisDao.insertOperateRecordData(operateData);
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new Exception("数据采集-医保结算清单手术操作数据出现异常！" + e.toString());
        }
    }

    /**
     * 处理病案首页
     *
     * @param k00Map
     */
    private void handleMedicalRecord(Map<String, Long> k00Map) {
        // 获取结算清清单id
        List<Long> settleListIds = new ArrayList<>();
        for (String key : k00Map.keySet()) {
            settleListIds.add(k00Map.get(key));
        }
        insertMedicalRecord(settleListIds);
    }

    /**
     * 写入病案首页
     *
     * @param settleListIds id
     */
    public void insertMedicalRecord(List<Long> settleListIds) {
        List<SomHiInvyBasInfo> busSettleLists = busHospitalMapper.queryMedicalRecordBaseInfo(settleListIds);
        // 病案首页
        if (ValidateUtil.isNotEmpty(busSettleLists)) {
            busHospitalMapper.extractMedicalRecordBaseInfo(busSettleLists);
        }

        List<SomDiag> busDiseaseDiagnoses = busHospitalMapper.queryMedicalRecordDiseaseInfo(settleListIds);
        // 病案首页-诊断
        if (ValidateUtil.isNotEmpty(busDiseaseDiagnoses)) {
            busHospitalMapper.extractMedicalRecordDiseaseInfo(busDiseaseDiagnoses);
        }

        List<SomOprnOprtInfo> busOperateDiagnoses = busHospitalMapper.queryMedicalRecordOperationInfo(settleListIds);
        if (ValidateUtil.isNotEmpty(busOperateDiagnoses)) {
            busHospitalMapper.extractMedicalRecordOperationInfo(busOperateDiagnoses);
        }
    }

    private String convertCodeName(Object code) {
        return ValidateUtil.isEmpty(code) ? null : code.toString();
    }

    /**
     * 转码
     *
     * @param medicalDataList 数据抽取接口抽取数据
     */
    private void codeConverter(List<MedicalUploadVo> medicalDataList) {
        try {
            medicalDataList.stream().forEach(medicalUploadVo -> {
                // 主要诊断编码、som_hi_invy_bas_info
                Map<String, Object> settleListMap = medicalUploadVo.getSettleListMap();
                String primaryDiagnosisCode = convertCodeName(settleListMap.get("c03c"));
                String primaryDiagnosisCodeName = convertCodeName(settleListMap.get("c04n"));
                converterAndSetValue(primaryDiagnosisCode, primaryDiagnosisCodeName, DrgConst.ICD10, settleListMap, "1");
                // 主要手术编码
                String primaryOperationCode = convertCodeName(settleListMap.get("c14x01c"));
                String primaryOperationCodeName = convertCodeName(settleListMap.get("c15x01n"));
                converterAndSetValue(primaryOperationCode, primaryOperationCodeName, DrgConst.ICD9, settleListMap, "2");

                // som_diag
                List<Map<String, Object>> diseaseDiagnosisList = medicalUploadVo.getDiseaseDiagnosisList();
                diseaseDiagnosisList.stream().forEach(diseaseDiagnosis -> {
                    //中医主病
                    if ("2".equals(diseaseDiagnosis.get("type"))) {
                        String diagCode = convertCodeName(diseaseDiagnosis.get("dscg_diag_codg"));
                        String diagCodeName = convertCodeName(diseaseDiagnosis.get("dscg_diag_name"));
                        converterAndSetValueTCM(diagCode, diagCodeName, diseaseDiagnosis, "1");
                    }
                    //中医主证
                    else if ("3".equals(diseaseDiagnosis.get("type"))) {
                        String diagCode = convertCodeName(diseaseDiagnosis.get("dscg_diag_codg"));
                        String diagCodeName = convertCodeName(diseaseDiagnosis.get("dscg_diag_name"));
                        converterAndSetValueTCM(diagCode, diagCodeName, diseaseDiagnosis, "2");
                    }
                    //西医
                    else {
                        String diagCode = convertCodeName(diseaseDiagnosis.get("dscg_diag_codg"));
                        String diagCodeName = convertCodeName(diseaseDiagnosis.get("dscg_diag_name"));
                        converterAndSetValue(diagCode, diagCodeName, DrgConst.ICD10, diseaseDiagnosis, "3");
                    }

                });

                // som_diag
                List<Map<String, Object>> operateDiagnosisList = medicalUploadVo.getOperateDiagnosisList();
                operateDiagnosisList.stream().forEach(oprt -> {
                    String oprnOprtCode = convertCodeName(oprt.get("c35c"));
                    String oprnOprtCodeName = convertCodeName(oprt.get("c36n"));
                    converterAndSetValue(oprnOprtCode, oprnOprtCodeName, DrgConst.ICD9, oprt, "4");
                });

                // som_hi_invy_bas_info
                //[c01c]门（急）诊诊断编码（西医）\[c02n]门（急）诊诊断名称（西医）
                String outpDiagnosisCode = convertCodeName(settleListMap.get("c01c"));
                String outpDiagnosisName = convertCodeName(settleListMap.get("c02n"));
                converterAndSetValue(outpDiagnosisCode, outpDiagnosisName, DrgConst.ICD10, settleListMap, "5");
                // som_hi_invy_bas_info
                //[c01c]门（急）诊诊断编码（中医）\[c02n]门（急）诊诊断名称（西医）
                String outoptDiagnosisCode = convertCodeName(settleListMap.get("c35c"));
                String outoptDiagnosisName = convertCodeName(settleListMap.get("c36n"));
                converterAndSetValueTCM(outoptDiagnosisCode, outoptDiagnosisName, settleListMap, "3");
            });
        } catch (Exception e) {
            e.printStackTrace();
            throw new AppException("转码出现异常！");
        }
    }

    private void converterAndSetValueTCM(String code, String name, Map<String, Object> data, String type) {
        if ("1".equals(type)) {
            List<String> tcmMainList = SysCommonConfigUtil.getTCMMainName(code);
            if (!ValidateUtil.isEmpty(tcmMainList)) {
                if (!tcmMainList.contains(name)) {
                    data.put("dscg_diag_name", tcmMainList.get(0));
                }
            }
        } else if ("2".equals(type)) {
            List<String> tcmPrincipalList = SysCommonConfigUtil.getTCMPrincipalName(code);
            if (!ValidateUtil.isEmpty(tcmPrincipalList)) {
                if (!tcmPrincipalList.contains(name)) {
                    data.put("dscg_diag_name", tcmPrincipalList.get(0));
                }
            }
        } else {
            List<String> tcmMainList = SysCommonConfigUtil.getTCMPrincipalName(code);
            if (!ValidateUtil.isEmpty(tcmMainList)) {
                if (!tcmMainList.contains(name)) {
                    data.put("c36n", tcmMainList.get(0));
                }
            }
            List<String> tcmPrincipalList = SysCommonConfigUtil.getTCMMainName(code);
            if (!ValidateUtil.isEmpty(tcmPrincipalList)) {
                if (!tcmPrincipalList.contains(name)) {
                    data.put("c36n", tcmPrincipalList.get(0));
                }
            }
        }
    }

    /**
     * 编码转换和设置到对应数据中
     *
     * @param code    编码
     * @param icdType icd类型
     * @param data    数据
     * @param type    类型   1：bus_settle_list诊断
     *                2：bus_settle_list手术
     *                3：bus_disease_diagnosis诊断
     *                4：bus_operate_diagnosis诊断
     */
    private void converterAndSetValue(String code,
                                      String codeName,
                                      String icdType,
                                      Map<String, Object> data,
                                      String type) {
        if (ValidateUtil.isNotEmpty(code)) {

            ICDCodeVo vo = SysCommonConfigUtil.getIcdCodeMap(code);
            String ybCode = code;
            ICDCodeVo icdCodeVo = null;
            boolean setCode = false;

            if (vo != null) {
                if (ValidateUtil.isNotEmpty(vo.getIcdName())) {
                    if (checkcodeName(codeName, vo)) {
                        switch (type) {
                            case "1":
                                // som_hi_invy_bas_info 中的主要诊断编码和名称设置
                                data.put("c04n", vo.getIcdName());
                                break;
                            case "2":
                                // som_hi_invy_bas_info 中的主要手术编码和名称设置
                                data.put("c15x01n", vo.getIcdName());
                                break;
                            case "3":
                                // som_diag 中的诊断编码设置
                                data.put("dscg_diag_name", vo.getIcdName());
                                break;
                            case "4":
                                // som_oprn_oprt_info 中的诊断编码设置
                                data.put("c36n", vo.getIcdName());
                                break;
                            case "5":
                                // som_hi_invy_bas_info 中的门急诊西医诊断编码和名称设置
                                data.put("c02n", vo.getIcdName());
                                break;
                        }
                    } else {
                        icdCodeVo = CodeConverterUtil.defaultConverter(code, icdType);
                        setCode = true;
                    }
                }
            } else {
                icdCodeVo = CodeConverterUtil.defaultConverter(code, icdType);
                setCode = true;
            }

            if (icdCodeVo != null && ValidateUtil.isNotEmpty(icdCodeVo.getCrspIcdName())) {
                ybCode = icdCodeVo.getCrspIcdCodg();
                switch (type) {
                    case "1":
                        // som_hi_invy_bas_info 中的主要诊断编码和名称设置
                        if (setCode) {
                            data.put("c03c", icdCodeVo.getCrspIcdCodg());

                        }
                        data.put("c04n", icdCodeVo.getCrspIcdName());
                        break;
                    case "2":
                        // som_hi_invy_bas_info 中的主要手术编码和名称设置
                        if (setCode) {
                            data.put("c14x01c", icdCodeVo.getCrspIcdCodg());
                        }
                        data.put("c15x01n", icdCodeVo.getCrspIcdName());
                        break;
                    case "3":
                        // som_diag 中的诊断编码设置
                        if (setCode) {
                            data.put("dscg_diag_codg", icdCodeVo.getCrspIcdCodg());
                        }
                        data.put("dscg_diag_name", icdCodeVo.getCrspIcdName());
                        break;
                    case "4":
                        // som_oprn_oprt_info 中的诊断编码设置
                        if (setCode) {
                            data.put("c35c", icdCodeVo.getCrspIcdCodg());
                        }
                        data.put("c36n", icdCodeVo.getCrspIcdName());
                        break;
                    case "5":
                        // som_hi_invy_bas_info 中的门急诊西医诊断编码和名称设置
                        if (setCode) {
                            data.put("c01c", icdCodeVo.getCrspIcdCodg());
                        }
                        data.put("c02n", icdCodeVo.getCrspIcdName());
                        break;
                }
            }

            //医保2.0名称转换 此时认为所有的编码都是医保2.0
            ICDCodeVo ybVo = SysCommonConfigUtil.getIcdCodeMap(ybCode);
            if (ybVo != null) {
                if (!ValidateUtil.isEmpty(ybVo.getIcdName())) {
                    switch (type) {
                        case "1":
                            // som_hi_invy_bas_info 中的主要诊断编码和名称设置
                            data.put("c04n", ybVo.getIcdName());
                            break;
                        case "2":
                            // som_hi_invy_bas_info 中的主要手术编码和名称设置
                            data.put("c15x01n", ybVo.getIcdName());
                            break;
                        case "3":
                            // som_diag 中的诊断编码设置
                            data.put("dscg_diag_name", ybVo.getIcdName());
                            break;
                        case "4":
                            // som_oprn_oprt_info 中的诊断编码设置
                            data.put("c36n", ybVo.getIcdName());
                            break;
                        case "5":
                            // som_hi_invy_bas_info 中的门急诊西医诊断编码和名称设置
                            data.put("c02n", ybVo.getIcdName());
                            break;
                    }
                }
            }

            //此处判断灰码转码
            Map<String, ICDCodeVo> grayMap = SysCommonConfigUtil.getIcdGrayCodeVoList(icdType);
            if (grayMap != null && !grayMap.isEmpty()) {

                switch (type) {
                    case "1":
                        if (grayMap.containsKey((String) data.get("c03c"))) {
                            ICDCodeVo icdCodeVoCrspGrey = grayMap.get((String) data.get("c03c"));
                            data.put("c03c", icdCodeVoCrspGrey.getCrspIcdCodg());
                            data.put("c04n", icdCodeVoCrspGrey.getCrspIcdName());
                        }
                        break;
                    case "2":
                        if (grayMap.containsKey((String) data.get("c14x01c"))) {
                            ICDCodeVo icdCodeVoCrspGrey = grayMap.get((String) data.get("c14x01c"));
                            data.put("c14x01c", icdCodeVoCrspGrey.getCrspIcdCodg());
                            data.put("c15x01n", icdCodeVoCrspGrey.getCrspIcdName());
                        }
                        break;
                    case "3":
                        if (grayMap.containsKey((String) data.get("dscg_diag_codg"))) {
                            ICDCodeVo icdCodeVoCrspGrey = grayMap.get((String) data.get("dscg_diag_codg"));
                            data.put("dscg_diag_codg", icdCodeVoCrspGrey.getCrspIcdCodg());
                            data.put("dscg_diag_name", icdCodeVoCrspGrey.getCrspIcdName());
                        }
                        break;
                    case "4":
                        if (grayMap.containsKey((String) data.get("c35c"))) {
                            ICDCodeVo icdCodeVoCrspGrey = grayMap.get((String) data.get("c35c"));
                            data.put("c35c", icdCodeVoCrspGrey.getCrspIcdCodg());
                            data.put("c36n", icdCodeVoCrspGrey.getCrspIcdName());
                        }
                        break;
                    case "5":
                        if (grayMap.containsKey((String) data.get("c01c"))) {
                            ICDCodeVo icdCodeVoCrspGrey = grayMap.get((String) data.get("c01c"));
                            data.put("c01c", icdCodeVoCrspGrey.getCrspIcdCodg());
                            data.put("c02n", icdCodeVoCrspGrey.getCrspIcdName());
                        }
                        break;
                }

            }

        }
    }

    private boolean checkcodeName(String codeName, ICDCodeVo vo) {
        if (!ValidateUtil.isEmpty(codeName)) {
            if (codeName.contains("(") || codeName.contains(")")) {
                // 如果包含括号，则替换
                codeName = codeName.replace("(", "（").replace(")", "）");
            }
        }
        return vo.getIcdName().equals(codeName);
    }

    /**
     * 获取当前上传数据所有 k00
     *
     * @param medicalDataList
     * @return
     */
    private List<String> getAllK00(List<MedicalUploadVo> medicalDataList) {
        List<String> k00List = new ArrayList<>();
        medicalDataList.stream().forEach(medicalUploadVo -> {
            k00List.add((String) medicalUploadVo.getSettleListMap().get("k00"));
        });
        return k00List;
    }

    @Override
    public void uploadDeptAndWorkerData(List<DeptAndWorkerUploadVo> dataList0) throws Exception {
        DefaultTransactionDefinition transactionDefinition1 = new DefaultTransactionDefinition();
        TransactionStatus transactionStatus1 = transactionManager.getTransaction(transactionDefinition1);
        try {
            String hospitalId = "";
            //获取医院信息
            List<SomHospInfo> somHospInfo = busHospitalMapper.selectByExample(new SomHospInfoExample());
            if (!ValidateUtil.isEmpty(somHospInfo)) {
                hospitalId = somHospInfo.get(0).getHospitalId();
            }
            //先整理获取到的最新数据
            List<SomDept> lastBusDeptList = new ArrayList<>();
            List<SomMedstffInfo> busMedicalWorkersList = new ArrayList<>();
            List<SomDeptPsnRlts> lastBusDeptWorkersRelationshipList = new ArrayList<>();
            for (DeptAndWorkerUploadVo data : dataList0) {
                SomDept somDept = data.getSomDept();
                somDept.setType(Integer.valueOf(DrgConst.DEPT_TYPE_2));
                somDept.setHospitalId(hospitalId);
                somDept.setActiveFlag(DrgConst.ACTIVE_FLAG_1);
                somDept.setDept_area(null);
                somDept.setIsOprnDept(null);
                lastBusDeptList.add(somDept);
                busMedicalWorkersList.addAll(data.getBusMedicalWorkersList());
                lastBusDeptWorkersRelationshipList.addAll(data.getBusDeptWorkersRelationshipList());
            }
            //busMedicalWorkersList去重
            List<SomMedstffInfo> lastBusMedicalWorkersList = new ArrayList<>();
            for (SomMedstffInfo bmwl : busMedicalWorkersList) {
                if (!lastBusMedicalWorkersList.contains(bmwl)) {
                    lastBusMedicalWorkersList.add(bmwl);
                }
            }
            //首先查询是否存在院内科室数据
            SomDeptExample busDeptExample = new SomDeptExample();
            SomDeptExample.Criteria criteria = busDeptExample.createCriteria();
            criteria.andHospitalIdEqualTo(hospitalId);
            criteria.andTypeEqualTo(Integer.valueOf(DrgConst.DEPT_TYPE_2));
            criteria.andActiveFlagEqualTo(DrgConst.ACTIVE_FLAG_1);
            criteria.andIsOpreIsNull();
            List<SomDept> busDeptList = busDeptMapper.selectByExample(busDeptExample);
            if (ValidateUtil.isEmpty(busDeptList)) {
                if (!ValidateUtil.isEmpty(lastBusDeptList)) {
                    commonDao.batchInsertBusDept(lastBusDeptList);
                }
                if (!ValidateUtil.isEmpty(lastBusMedicalWorkersList)) {
                    commonDao.batchInsertBusMedicalWorkers(lastBusMedicalWorkersList);
                    commonDao.batchInsertBusDeptWorkersRelationship(lastBusDeptWorkersRelationshipList);
                }
            } else { //先删除再插入
                if (!ValidateUtil.isEmpty(lastBusDeptList)) {
                    busDeptMapper.deleteByExample(busDeptExample);//删除当前所有院内科室数据
                    commonDao.batchInsertBusDept(lastBusDeptList);
                }
                if (!ValidateUtil.isEmpty(lastBusMedicalWorkersList)) {
                    busMedicalWorkersMapper.deleteByExample(new SomMedstffInfoExample());
                    busDeptWorkersRelationshipMapper.deleteByExample(new SomDeptPsnRltsExample());
                    commonDao.batchInsertBusMedicalWorkers(lastBusMedicalWorkersList);
                    commonDao.batchInsertBusDeptWorkersRelationship(lastBusDeptWorkersRelationshipList);
                }
            }
            //最后提交事务commit
            transactionManager.commit(transactionStatus1);
        } catch (Exception e) {
            transactionManager.rollback(transactionStatus1);
            throw new Exception("数据采集-院内科室和医护人员数据出现异常！" + e.toString());
        }
    }

    //生成一个批次的日志记录，为后面做准备
    private Long addOneDataHandleLog(List<MedicalUploadVo> medicalDataList, String hospitalId) {
        SomDataprosLog sdhl = new SomDataprosLog();
        if (!ValidateUtil.isEmpty(medicalDataList)) {
            sdhl.setMedcasVal(medicalDataList.size());//上传病案总数
        } else {
            sdhl.setMedcasVal(0);//上传病案总数
        }
        sdhl.setDataUpldTime(DateUtil.getCurrentDateTime()); //上传时间
        sdhl.setResult("waited");
        sdhl.setPrcs_prgs(0); //默认进度为0
        sdhl.setOprt_psn("数据接口"); //操作人
        sdhl.setHospitalId(hospitalId);
        sdhl.setActiveFlag(DrgConst.ACTIVE_FLAG_1);
        stsDataHandleLogMapper.insert(sdhl);
        return sdhl.getId();
    }

    /**
     * 处理医保结算清单基础数据
     *
     * @param medicalDataList
     * @param batchNum
     * @return
     */
    private void handleBusSettleList(List<MedicalUploadVo> medicalDataList, String batchNum) throws Exception {
        List<Map<String, Object>> settleListData = new ArrayList<>();
        try {
            for (MedicalUploadVo data : medicalDataList) {
                Map<String, Object> settleListMap = data.getSettleListMap();
                settleListMap.put("history_tip_state", BigDecimal.ZERO.toString());
                settleListMap.put("look_over", ValidateUtil.isEmpty(data.getSettleListMap().get("look_over")) ? BigDecimal.ZERO.toString() : data.getSettleListMap().get("look_over"));
                settleListMap.put("upload_flag", BigDecimal.ZERO.toString());
                if (ValidateUtil.isEmpty(settleListMap.get("a01"))) {
                    settleListMap.put("a01", SysCommonConfigUtil.get(DrgConst.SCC_HOSPITAL_ID));
                }
                SomHiInvyBasInfo hospInfo = somHospInfoMapper.queryBusHospitalByHospitalId(settleListMap.get("a01").toString());
                settleListMap.put("a02", hospInfo.getMedinsName());
                settleListMap.put("batchNum", batchNum);
                settleListData.add(settleListMap);
            }
            fillSettleList(settleListData, DrgConst.ACTIVE_FLAG_1); //添加vo缺失字段
            if (!ValidateUtil.isEmpty(settleListData)) {
                //分片执行 100条一次
                int batchSize = 100;
                List<Map<String, Object>> insertBatchTempList = new ArrayList<>();
                int currIndex = 0;
                for (Map<String, Object> stringObjectMap :
                        settleListData) {
                    insertBatchTempList.add(stringObjectMap);
                    currIndex++;
                    if (currIndex % batchSize == 0) {
                        busSettleListDao.insertBusSettleList(insertBatchTempList);  //批量抽取bus_settle_list
                        insertBatchTempList.clear();
                    }
                }

                if (ValidateUtil.isNotEmpty(insertBatchTempList)) {
                    busSettleListDao.insertBusSettleList(insertBatchTempList);  //批量抽取bus_settle_list
                    insertBatchTempList.clear();
                }
//                busSettleListDao.insertBusSettleList(settleListData);  //批量抽取bus_settle_list
//                busSettleListDao.insertVDrgSettleList(settleListData);  //备份bus_settle_list数据
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new Exception("数据采集-医保结算清单基础数据出现异常！" + e.toString());
        }
    }

    /**
     * 处理疾病诊断
     *
     * @param medicalDataList
     * @return
     */
    private void handleBusDiseaseDiagnosis(List<MedicalUploadVo> medicalDataList, Map<String, Long> k00Map) throws Exception {
        List<Map<String, Object>> diseaseData = new ArrayList<>();
        try {
            for (MedicalUploadVo data : medicalDataList) {
                List<Map<String, Object>> diseaseDiagnosisList = data.getDiseaseDiagnosisList();
                //填充settle_list_id值，然后插入数据
                fillSettleListId(diseaseDiagnosisList, k00Map);
                diseaseData.addAll(diseaseDiagnosisList);
            }
            if (!ValidateUtil.isEmpty(diseaseData)) {
                busDiseaseDiagnosisDao.insertDiseaseData(diseaseData);//批量抽取bus_disease_diagnosis
//                busDiseaseDiagnosisDao.insertVDrgDisease(diseaseData);  //备份bus_disease_diagnosis数据
            }

        } catch (Exception e) {
            e.printStackTrace();
            throw new Exception("数据采集-医保结算清单疾病诊断数据出现异常！" + e.toString());
        }
    }

    /**
     * 处理手术操作
     *
     * @param medicalDataList
     * @return
     */
    private void handleBusOperateDiagnosis(List<MedicalUploadVo> medicalDataList, Map<String, Long> k00Map) throws Exception {
        List<Map<String, Object>> operateData = new ArrayList<>();
        try {
            for (MedicalUploadVo data : medicalDataList) {
                List<Map<String, Object>> operateDiagnosisList = data.getOperateDiagnosisList();
                //填充settle_list_id值，然后插入数据
                fillSettleListId(operateDiagnosisList, k00Map);
                operateData.addAll(operateDiagnosisList);
            }
            if (!ValidateUtil.isEmpty(operateData)) {
                busOperateDiagnosisDao.insertOperateData(operateData);//批量抽取bus_operate_diagnosis
//                busOperateDiagnosisDao.insertVDrgOperate(operateData);  //备份bus_operate_diagnosis数据
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new Exception("数据采集-医保结算清单手术操作数据出现异常！" + e.toString());
        }
    }

    /**
     * 处理门慢特
     *
     * @param medicalDataList
     * @return
     */
    private void handleBusOutpatientClinicDiagnosis(List<MedicalUploadVo> medicalDataList, Map<String, Long> k00Map) throws Exception {
        List<Map<String, Object>> outClinicData = new ArrayList<>();
        try {
            for (MedicalUploadVo data : medicalDataList) {
                List<Map<String, Object>> outClinicList = data.getOutClinicList();
                //填充settle_list_id值，然后插入数据
                fillSettleListId(outClinicList, k00Map);
                outClinicData.addAll(outClinicList);
            }
            if (!ValidateUtil.isEmpty(outClinicData)) {
                busOutPatientClinicDiagnosisDao.insertOutClinicData(outClinicData);   //批量抽取bus_outpatient_clinic_diagnosis
//                busOutPatientClinicDiagnosisDao.insertVDrgOutClinic(outClinicData);  //备份bus_outpatient_clinic_diagnosis数据
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new Exception("数据采集-医保结算清单门慢特诊疗数据出现异常！" + e.toString());
        }
    }

    /**
     * 处理ICU
     *
     * @param medicalDataList
     * @return
     */
    private void handleBusIcu(List<MedicalUploadVo> medicalDataList, Map<String, Long> k00Map) throws Exception {
        List<Map<String, Object>> icuData = new ArrayList<>();
        try {
            for (MedicalUploadVo data : medicalDataList) {
                List<Map<String, Object>> icuList = data.getIcuList();
                //填充settle_list_id值，然后插入数据
                fillSettleListId(icuList, k00Map);
                //转换重症监护合计时长、scs_cutd_sum_dura
                transformIcuSumTimeDura(icuList);
                icuData.addAll(icuList);
            }
            if (!ValidateUtil.isEmpty(icuData)) {
                busIcuDao.insertIcuData(icuData);//批量抽取bus_icu
//                busIcuDao.insertVDrgIcu(icuData);  //备份bus_icu数据
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new Exception("数据采集-医保结算清单重症监护室数据出现异常！" + e.toString());
        }
    }

    /**
     * 处理医疗费用
     *
     * @param medicalDataList
     * @return
     */
    private void handleBusMedicalCost(List<MedicalUploadVo> medicalDataList, Map<String, Long> k00Map) throws Exception {
        List<Map<String, Object>> medicalCostData = new ArrayList<>();
        try {
            for (MedicalUploadVo data : medicalDataList) {
                List<Map<String, Object>> medicalCostList = data.getMedicalCostList();
                //填充settle_list_id值，然后插入数据
                fillSettleListId(medicalCostList, k00Map);
                medicalCostData.addAll(medicalCostList);
            }
            if (!ValidateUtil.isEmpty(medicalCostData)) {
                busMedicalCostDao.insertMedicalCostData(medicalCostData);//批量抽取bus_medical_cost
//                busMedicalCostDao.insertVDrgMedicalCost(medicalCostData); //备份bus_medical_cost数据
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new Exception("数据采集-医保结算清单医疗收费数据出现异常！" + e.toString());
        }
    }

    /**
     * 处理医保基金支付
     *
     * @param medicalDataList
     * @return
     */
    private void handleBusFundPay(List<MedicalUploadVo> medicalDataList, Map<String, Long> k00Map) throws Exception {
        List<Map<String, Object>> fundPayData = new ArrayList<>();
        try {
            for (MedicalUploadVo data : medicalDataList) {
                List<Map<String, Object>> fundPayList = data.getFundPayList();
                //填充settle_list_id值，然后插入数据
                fillSettleListId(fundPayList, k00Map);
                fundPayData.addAll(fundPayList);
            }
            if (!ValidateUtil.isEmpty(fundPayData)) {
                busFundPayDao.insertFundPayData(fundPayData);//批量抽取bus_fund_pay
//                busFundPayDao.insertVDrgFundPay(fundPayData); //备份bus_fund_pay数据
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new Exception("数据采集-医保结算清单医疗收费数据出现异常！" + e.toString());
        }
    }

    /**
     * 补充除开病案原始数据的其他信息到bus_settle_list表中
     *
     * @param voList01
     * @param active_flag
     */
    private void fillSettleList(List voList01, String active_flag) {
        for (Object o : voList01) {
            Map vMap = (Map) o;
            vMap.put("active_flag", active_flag);
            vMap.put("hospital_id", vMap.get("a01"));
        }
    }

    /**
     * 查询bus_settle_list中k00和settle_list_id的对应关系
     *
     * @param
     * @param batchNum
     * @return Map<String, Long>
     */
    private Map<String, Long> getK00ToSettleListId(String batchNum) {
        Map<String, Long> map = new HashMap<String, Long>();
//        SomHiInvyBasInfoExample busSettleListExample = new SomHiInvyBasInfoExample();
//        busSettleListExample.createCriteria().andDataLogIdIsNull().andActiveFlagEqualTo(DrgConst.ACTIVE_FLAG_1);
//        List<SomHiInvyBasInfo> busSettleLists = busSettleListMapper.selectByExample(busSettleListExample);
//        //处理为k00和settle_list_id的对应关系Map，key:k00，value:settle_list_id
//        for(SomHiInvyBasInfo vo:busSettleLists){
//            map.put(vo.getK00(), vo.getId());
//        }

        List<Map<String, Object>> dataMap = busSettleListMapper.queryK00ToSettleListIdByBatchNum(batchNum);
        dataMap.forEach(m -> {
            map.put(m.get("k00").toString(), Long.parseLong(m.get("id").toString()));
        });
        return map;
    }

    /**
     * 补充除开bus_settle_list表的其他信息表中的settle_list_id
     *
     * @param res
     * @param k00Map
     */
    private void fillSettleListId(List<Map<String, Object>> res, Map<String, Long> k00Map) {
        for (Map<String, Object> vo : res) {
            vo.put("settle_list_id", k00Map.get(vo.get("k00").toString()));
        }
    }

    /**
     * 转换重症监护合计时长，由小时转为天/小时/分钟格式,scs_cutd_sum_dura-
     *
     * @param res
     */
    private void transformIcuSumTimeDura(List<Map<String, Object>> res) {
        String str_scs_cutd_sum_dura = "";
        double duble_scs_cutd_sum_dura = 0;
        int days, remainingHours, minutes;
        for (Map<String, Object> vo : res) {
            str_scs_cutd_sum_dura = (String) vo.get("scs_cutd_sum_dura");
            if (!ValidateUtil.isEmpty(str_scs_cutd_sum_dura)) {
                //不包含/并且能够转为Double对象，则进行转换
                if (!str_scs_cutd_sum_dura.contains("/") && ValidateUtil.isSignedDouble(str_scs_cutd_sum_dura)) {
                    duble_scs_cutd_sum_dura = Double.parseDouble(str_scs_cutd_sum_dura);
                    days = (int) (duble_scs_cutd_sum_dura / 24);
                    remainingHours = (int) (duble_scs_cutd_sum_dura % 24);
                    minutes = (int) ((duble_scs_cutd_sum_dura - (days * 24 + remainingHours)) * 60);
                    vo.put("scs_cutd_sum_dura", days + "/" + remainingHours + "/" + minutes);
                }
            }
        }
    }

    /**
     * 处理医保结算清单数据
     *
     * @param dataMapForMedicalList
     * @return
     * @throws Exception
     */
    public Long uploadMedicalInsuranceData(List<MedicalInsuranceDataVo> dataMapForMedicalList) throws Exception {
        //利用编程式事务处理，start transaction
        DefaultTransactionDefinition transactionDefinition = new DefaultTransactionDefinition();
        TransactionStatus transactionStatus = transactionManager.getTransaction(transactionDefinition);
        try {
            if (ValidateUtil.isEmpty(dataMapForMedicalList)) {
                throw new AppException("请检查信息是否填写完整，当前无数据!");
            }
            //一个事务内的业务代码
            String batchNum = UUID.randomUUID().toString();
            handleDi01(dataMapForMedicalList, batchNum);
            Map<String, Object> map = getDiIdMapping(batchNum, DrgConst.DI_REPEAT_TABLE_DI01);
            handleDi01PayInfo(dataMapForMedicalList, map);
            handleDi01OpspdiseInfo(dataMapForMedicalList, map);
            handleDi01DiseInfo(dataMapForMedicalList, map);
            handleDi01ItemInfo(dataMapForMedicalList, map);
            handleDi01OprnInfo(dataMapForMedicalList, map);
            handleDi01IcuInfo(dataMapForMedicalList, map);
            handleDi01BldInfo(dataMapForMedicalList, map);
            //最后提交事务commit
            transactionManager.commit(transactionStatus);
            return null;
        } catch (Exception e) {
            transactionManager.rollback(transactionStatus);
            throw new AppException(e.getMessage());
        }
    }

    /**
     * 获取就诊id和id对应
     *
     * @param batchNum 批次号
     * @param tabName  表名
     * @return
     */
    private Map<String, Object> getDiIdMapping(String batchNum, String tabName) {
        Map<String, Object> map = new HashMap<>();
        Map<String, Object> params = new HashMap<>();
        params.put("batchNum", batchNum);
        params.put("tabName", tabName);
        params.put("useVisit", SomDataAcquisitionInterfaceServiceImpl.useVisit ? "1" : "0");
        List<SomMedcasIntfBasInfo> mappingIdList = busSettleListDao.getDiIdMapping(params);
        mappingIdList.forEach(m -> {
            map.put(m.getMdtrt_id(), m.getId());
        });
        return map;
    }

    /**
     * 插入医保结算清单数据
     *
     * @param dataMapForMedicalList
     * @param batchNum              批次号
     * @throws Exception
     */
    private void handleDi01(List<MedicalInsuranceDataVo> dataMapForMedicalList,
                            String batchNum) throws Exception {
        List<Map<String, Object>> di01Data = new ArrayList<>();
        try {
            for (MedicalInsuranceDataVo data : dataMapForMedicalList) {
                Map<String, Object> di01Map = data.getDi01Map();
                // 判断住院医疗类型
                if (ValidateUtil.isEmpty(di01Map.get("ipt_med_type"))) {
                    di01Map.remove("ipt_med_type");
                    di01Map.put("ipt_med_type", "1");
                }
                di01Map.put("extract_flag", !ValidateUtil.isEmpty(di01Map.get("runProcees")) ? DrgConst.ACTIVE_FLAG_1 : DrgConst.ACTIVE_FLAG_0);
                di01Map.put("batchNum", batchNum);
                di01Data.add(di01Map);
            }
            if (!ValidateUtil.isEmpty(di01Data)) {
                busSettleListDao.insertDi01(di01Data);
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new Exception("数据采集-医保结算清单基础数据出现异常！" + e.toString());
        }
    }

    /**
     * 插入医保结算清单基金支付数据
     *
     * @param dataMapForMedicalList
     * @param map
     * @throws Exception
     */
    private void handleDi01PayInfo(List<MedicalInsuranceDataVo> dataMapForMedicalList, Map<String, Object> map) throws Exception {
        List<Map<String, Object>> fundData = new ArrayList<>();
        try {
            for (MedicalInsuranceDataVo data : dataMapForMedicalList) {
                List<Map<String, Object>> fundList = data.getFundList();
                fillId(fundList, map, DrgConst.DI_REPEAT_TABLE_DI01_PKID);
                fundData.addAll(fundList);
            }
            if (!ValidateUtil.isEmpty(fundData)) {
                busSettleListDao.insertDi01PayInfo(fundData);
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new Exception("数据采集-医保结算清单基金支付数据出现异常！" + e.toString());
        }
    }

    /**
     * 插入医保结算清单门诊慢特病数据
     *
     * @param dataMapForMedicalList
     * @param map
     * @throws Exception
     */
    private void handleDi01OpspdiseInfo(List<MedicalInsuranceDataVo> dataMapForMedicalList, Map<String, Object> map) throws Exception {
        List<Map<String, Object>> ocidData = new ArrayList<>();
        try {
            for (MedicalInsuranceDataVo data : dataMapForMedicalList) {
                List<Map<String, Object>> ocidList = data.getOcidList();
                fillId(ocidList, map, DrgConst.DI_REPEAT_TABLE_DI01_PKID);
                ocidData.addAll(ocidList);
            }
            if (!ValidateUtil.isEmpty(ocidData)) {
                busSettleListDao.insertDi01OpspdiseInfo(ocidData);
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new Exception("数据采集-医保结算清单门诊慢特病数据出现异常！" + e.toString());
        }
    }

    /**
     * 插入医保结算清单诊断数据
     *
     * @param dataMapForMedicalList
     * @param map
     * @throws Exception
     */
    private void handleDi01DiseInfo(List<MedicalInsuranceDataVo> dataMapForMedicalList, Map<String, Object> map) throws Exception {
        List<Map<String, Object>> diagnosisData = new ArrayList<>();
        try {
            for (MedicalInsuranceDataVo data : dataMapForMedicalList) {
                List<Map<String, Object>> diagnosisList = data.getDiagnosisList();
                fillId(diagnosisList, map, DrgConst.DI_REPEAT_TABLE_DI01_PKID);
                diagnosisData.addAll(diagnosisList);
            }
            if (!ValidateUtil.isEmpty(diagnosisData)) {
                busSettleListDao.insertDi01DiseInfo(diagnosisData);
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new Exception("数据采集-医保结算清单诊断数据出现异常！" + e.toString());
        }
    }

    /**
     * 插入医保结算清单收费项数据
     *
     * @param dataMapForMedicalList
     * @param map
     * @throws Exception
     */
    private void handleDi01ItemInfo(List<MedicalInsuranceDataVo> dataMapForMedicalList, Map<String, Object> map) throws Exception {
        List<Map<String, Object>> costData = new ArrayList<>();
        try {
            for (MedicalInsuranceDataVo data : dataMapForMedicalList) {
                List<Map<String, Object>> costList = data.getCostList();
                fillId(costList, map, DrgConst.DI_REPEAT_TABLE_DI01_PKID);
                costData.addAll(costList);
            }
            if (!ValidateUtil.isEmpty(costData)) {
                busSettleListDao.insertDi01ItemInfo(costData);
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new Exception("数据采集-医保结算清单收费项数据出现异常！" + e.toString());
        }
    }

    /**
     * 插入医保结算清单手术操作数据
     *
     * @param dataMapForMedicalList
     * @param map
     * @throws Exception
     */
    private void handleDi01OprnInfo(List<MedicalInsuranceDataVo> dataMapForMedicalList, Map<String, Object> map) throws Exception {
        List<Map<String, Object>> operationData = new ArrayList<>();
        try {
            for (MedicalInsuranceDataVo data : dataMapForMedicalList) {
                List<Map<String, Object>> operationList = data.getOperationList();
                fillId(operationList, map, DrgConst.DI_REPEAT_TABLE_DI01_PKID);
                operationData.addAll(operationList);
            }
            if (!ValidateUtil.isEmpty(operationData)) {
                busSettleListDao.insertDi01OprnInfo(operationData);
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new Exception("数据采集-医保结算清单手术操作数据出现异常！" + e.toString());
        }
    }

    /**
     * 插入医保结算清单重症监护数据
     *
     * @param dataMapForMedicalList
     * @param map
     * @throws Exception
     */
    private void handleDi01IcuInfo(List<MedicalInsuranceDataVo> dataMapForMedicalList, Map<String, Object> map) throws Exception {
        List<Map<String, Object>> icuData = new ArrayList<>();
        try {
            for (MedicalInsuranceDataVo data : dataMapForMedicalList) {
                List<Map<String, Object>> icuList = data.getIcuList();
                fillId(icuList, map, DrgConst.DI_REPEAT_TABLE_DI01_PKID);
                icuData.addAll(icuList);
            }
            if (!ValidateUtil.isEmpty(icuData)) {
                busSettleListDao.insertDi01IcuInfo(icuData);
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new Exception("数据采集-医保结算清单重症监护数据出现异常！" + e.toString());
        }
    }

    private void handleDi01BldInfo(List<MedicalInsuranceDataVo> dataMapForMedicalList, Map<String, Object> map) throws Exception {
        List<Map<String, Object>> bldData = new ArrayList<>();
        try {
            for (MedicalInsuranceDataVo data : dataMapForMedicalList) {
                List<Map<String, Object>> bldList = data.getBldList();
                fillId(bldList, map, DrgConst.DI_REPEAT_TABLE_DI01_PKID);
                bldData.addAll(bldList);
            }
            if (!ValidateUtil.isEmpty(bldData)) {
                busSettleListDao.insertDi01BldInfo(bldData);
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new Exception("数据采集-医保结算清单输血数据出现异常！" + e.toString());
        }
    }

    /**
     * 处理病案首页数据
     *
     * @param dataMapForMedicalPageList
     * @return
     * @throws Exception
     */
    public Long uploadMedicalPageData(List<MedicalPageDataVo> dataMapForMedicalPageList) throws Exception {
        //利用编程式事务处理，start transaction
        DefaultTransactionDefinition transactionDefinition = new DefaultTransactionDefinition();
        TransactionStatus transactionStatus = transactionManager.getTransaction(transactionDefinition);
        try {
            if (ValidateUtil.isEmpty(dataMapForMedicalPageList)) {
                throw new AppException("请检查信息是否填写完整，当前无数据!");
            }
            String batchNum = UUID.randomUUID().toString();
            handleDi02(dataMapForMedicalPageList, batchNum);

            if (enableView) {
                // 获取di02 mdtrt_id集合
                List<String> mdtrtIdList = new ArrayList<>();
                for (MedicalPageDataVo v :
                        dataMapForMedicalPageList) {
                    String mdtrtId = (String) v.getDi02Map().get("mdtrt_id");
                    mdtrtIdList.add(mdtrtId);
                }
                // 处理视图数据
                handleDataView(mdtrtIdList);
            }

            Map<String, Object> map = getDiIdMapping(batchNum, DrgConst.DI_REPEAT_TABLE_DI02);

            handleDi02DiseInfo(dataMapForMedicalPageList, map);
            handleDi02OprnInfo(dataMapForMedicalPageList, map);
            handleDi02IcuInfo(dataMapForMedicalPageList, map);
            //最后提交事务commit
            transactionManager.commit(transactionStatus);
            return null;
        } catch (Exception e) {
            transactionManager.rollback(transactionStatus);
            throw new AppException(e.getMessage());
        }
    }

    private void handleDi02(List<MedicalPageDataVo> dataMapForMedicalPageList, String batchNum) throws Exception {
        List<Map<String, Object>> di02Data = new ArrayList<>();
        try {
            for (MedicalPageDataVo data : dataMapForMedicalPageList) {
                Map<String, Object> di02Map = data.getDi02Map();
                di02Map.put("extract_flag", DrgConst.ACTIVE_FLAG_0);
                di02Map.put("batchNum", batchNum);
                di02Data.add(di02Map);
            }
            if (!ValidateUtil.isEmpty(di02Data)) {
                busSettleListDao.insertDi02(di02Data);
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new Exception("数据采集-病案首页基础数据出现异常！" + e.toString());
        }
    }

    private void handleDi02DiseInfo(List<MedicalPageDataVo> dataMapForMedicalPageList, Map<String, Object> map) throws Exception {
        List<Map<String, Object>> diagnosisData = new ArrayList<>();
        try {
            for (MedicalPageDataVo data : dataMapForMedicalPageList) {
                List<Map<String, Object>> diagnosisList = data.getDiagnosisList();
                fillId(diagnosisList, map, DrgConst.DI_REPEAT_TABLE_DI02_PKID);
                diagnosisData.addAll(diagnosisList);
            }
            if (!ValidateUtil.isEmpty(diagnosisData)) {
                busSettleListDao.insertDi02DiseInfo(diagnosisData);
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new Exception("数据采集-病案首页诊断数据出现异常！" + e.toString());
        }
    }

    private void handleDi02OprnInfo(List<MedicalPageDataVo> dataMapForMedicalPageList, Map<String, Object> map) throws Exception {
        List<Map<String, Object>> operationData = new ArrayList<>();
        try {
            for (MedicalPageDataVo data : dataMapForMedicalPageList) {
                List<Map<String, Object>> operationList = data.getOperationList();
                fillId(operationList, map, DrgConst.DI_REPEAT_TABLE_DI02_PKID);
                operationData.addAll(operationList);
            }
            if (!ValidateUtil.isEmpty(operationData)) {
                busSettleListDao.insertDi02OprnInfo(operationData);
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new Exception("数据采集-病案首页手术数据出现异常！" + e.toString());
        }
    }

    private void handleDi02IcuInfo(List<MedicalPageDataVo> dataMapForMedicalPageList, Map<String, Object> map) throws Exception {
        List<Map<String, Object>> icuData = new ArrayList<>();
        try {
            for (MedicalPageDataVo data : dataMapForMedicalPageList) {
                List<Map<String, Object>> icuList = data.getIcuList();
                fillId(icuList, map, DrgConst.DI_REPEAT_TABLE_DI02_PKID);
                icuData.addAll(icuList);
            }
            if (!ValidateUtil.isEmpty(icuData)) {
                busSettleListDao.insertDi02IcuInfo(icuData);
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new Exception("数据采集-病案首页重症监护数据出现异常！" + e.toString());
        }
    }

    private Map<String, Object> getMsToId() {
        Map<String, Object> map = new HashMap<>();
        List<SomMedcasIntfBasInfo> di02s = busSettleListDao.selectInfo();
        for (SomMedcasIntfBasInfo som_medcas_intf_bas_info : di02s) {
            map.put(som_medcas_intf_bas_info.getMdtrt_sn(), som_medcas_intf_bas_info.getId());
        }
        return map;
    }

    private Map<String, Object> getDi05Id() {
        Map<String, Object> map = new HashMap<>();
        List<SomMedcasIntfBasInfo> di02s = busSettleListDao.selectDi05Info();
        for (SomMedcasIntfBasInfo som_medcas_intf_bas_info : di02s) {
            map.put(som_medcas_intf_bas_info.getMdtrt_sn(), som_medcas_intf_bas_info.getId());
        }
        return map;
    }

    private Map<String, Object> getDi06Id() {
        Map<String, Object> map = new HashMap<>();
        List<SomMedcasIntfBasInfo> di02s = busSettleListDao.selectDi06Info();
        for (SomMedcasIntfBasInfo som_medcas_intf_bas_info : di02s) {
            map.put(som_medcas_intf_bas_info.getMdtrt_sn(), som_medcas_intf_bas_info.getId());
        }
        return map;
    }

    private Map<String, Object> getDi07Id() {
        Map<String, Object> map = new HashMap<>();
        List<SomMedcasIntfBasInfo> di02s = busSettleListDao.selectDi07Info();
        for (SomMedcasIntfBasInfo som_medcas_intf_bas_info : di02s) {
            map.put(som_medcas_intf_bas_info.getMdtrt_sn(), som_medcas_intf_bas_info.getId());
        }
        return map;
    }

    private Map<String, Object> getDi08Id() {
        Map<String, Object> map = new HashMap<>();
        List<SomMedcasIntfBasInfo> di02s = busSettleListDao.selectDi08Info();
        for (SomMedcasIntfBasInfo som_medcas_intf_bas_info : di02s) {
            map.put(som_medcas_intf_bas_info.getMdtrt_id(), som_medcas_intf_bas_info.getId());
        }
        return map;
    }

    private void fillId(List<Map<String, Object>> list, Map<String, Object> map, String mainTableName) {
        for (Map<String, Object> objectMap : list) {
            objectMap.put(mainTableName, map.get(objectMap.get("mdtrt_id")));
        }
    }

    /**
     * 处理住院结算数据
     *
     * @param dataMapForInhosSettleList
     * @return
     * @throws Exception
     */
    public Long uploadInhosSettleData(List<InhosSettleDataVo> dataMapForInhosSettleList) throws Exception {
        //利用编程式事务处理，start transaction
        DefaultTransactionDefinition transactionDefinition = new DefaultTransactionDefinition();
        TransactionStatus transactionStatus = transactionManager.getTransaction(transactionDefinition);
        try {
            if (ValidateUtil.isEmpty(dataMapForInhosSettleList)) {
                throw new AppException("请检查信息是否填写完整，当前无数据!");
            }
            handleDi03(dataMapForInhosSettleList);
            handlerFundPay("1", new HashMap<>());
            //最后提交事务commit
            transactionManager.commit(transactionStatus);
            return null;
        } catch (Exception e) {
            transactionManager.rollback(transactionStatus);
            throw new AppException(e.getMessage());
        }
    }

    private void handleDi03(List<InhosSettleDataVo> dataMapForInhosSettleList) throws Exception {
        List<Map<String, Object>> di03Data = new ArrayList<>();
        try {
            for (InhosSettleDataVo data : dataMapForInhosSettleList) {
                Map<String, Object> di03Map = data.getDi03Map();
                di03Data.add(di03Map);
            }
            if (!ValidateUtil.isEmpty(di03Data)) {
                busSettleListDao.insertDi03(di03Data);
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new Exception("数据采集-病案首页基础数据出现异常！" + e.toString());
        }
    }

    public Long uploadInhosDetailData(List<InhosDetailDataVo> dataMapForInhosDetailList) throws Exception {
        //利用编程式事务处理，start transaction
        DefaultTransactionDefinition transactionDefinition = new DefaultTransactionDefinition();
        TransactionStatus transactionStatus = transactionManager.getTransaction(transactionDefinition);
        try {
            if (ValidateUtil.isEmpty(dataMapForInhosDetailList)) {
                throw new AppException("请检查信息是否填写完整，当前无数据!");
            }
            //删除本次数据中涉及到的unique_id明细数据
            deleteDi11Detail(dataMapForInhosDetailList);
            //新增数据
            handleDi11Detail(dataMapForInhosDetailList);
            //最后提交事务commit
            transactionManager.commit(transactionStatus);
            //清除数据array 便于数据更快进行GC
            dataMapForInhosDetailList.clear();
            return null;
        } catch (Exception e) {
            transactionManager.rollback(transactionStatus);
            throw new AppException(e.getMessage());
        }
    }

    private Map<String, Object> getDi03Info() {
        Map<String, Object> map = new HashMap<>();
        List<SomMedcasIntfBasInfo> list = busSettleListDao.selectDi03Info();
        for (SomMedcasIntfBasInfo som_chrg_item_intf : list) {
            map.put(som_chrg_item_intf.getMdtrt_id(), som_chrg_item_intf.getId());
        }
        return map;
    }

    /**
     * 删除费用明细去重
     *
     * @param dataMapForInhosDetailList
     * @throws Exception
     */
    private void deleteDi11Detail(List<InhosDetailDataVo> dataMapForInhosDetailList) throws Exception {
        List<String> uniqueIds = new ArrayList<>();
        try {
            for (InhosDetailDataVo data : dataMapForInhosDetailList) {
                List<Map<String, Object>> di03DetailList = data.getDi03DetailList();
                di03DetailList.forEach(s -> {
                    Object unique_id = s.get("unique_id");
                    if (!ValidateUtil.isEmpty(unique_id)) {
                        if (!uniqueIds.contains(unique_id.toString())) {
                            uniqueIds.add(unique_id.toString());
                        }
                    }
                });
            }
            if (!ValidateUtil.isEmpty(uniqueIds)) {
                busSettleListDao.deleteDi11Detail(uniqueIds);
            }
            uniqueIds.clear();

        } catch (Exception e) {
            e.printStackTrace();
            throw new Exception("数据采集-病案首页基础数据删除重复明细出现异常！" + e.toString());
        }
    }

    private void handleDi11Detail(List<InhosDetailDataVo> dataMapForInhosDetailList) throws Exception {
        List<Map<String, Object>> di11DetailData = new ArrayList<>();
        try {
            String batchNum = UUID.randomUUID().toString();
            for (InhosDetailDataVo data : dataMapForInhosDetailList) {
                List<Map<String, Object>> di03DetailList = data.getDi03DetailList();
                di03DetailList.forEach(s -> {
                    s.put("batchNum", batchNum);
                });
                di11DetailData.addAll(di03DetailList);
            }
            if (!ValidateUtil.isEmpty(di11DetailData)) {
                busSettleListDao.insertDi11Detail(di11DetailData);
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new Exception("数据采集-病案首页基础数据出现异常！" + e.toString());
        }
    }

    private void fillDi03Id(List<Map<String, Object>> list, Map<String, Object> map) {
        for (Map<String, Object> objectMap : list) {
            objectMap.put("di03_id", map.get(objectMap.get("mdtrt_id")));
        }
    }

    public Long uploadInPatientOrdersData(List<InPatientOrdersDataVo> dataMapForInPatientOrdersList) throws Exception {
        //利用编程式事务处理，start transaction
        DefaultTransactionDefinition transactionDefinition = new DefaultTransactionDefinition();
        TransactionStatus transactionStatus = transactionManager.getTransaction(transactionDefinition);
        try {
            if (ValidateUtil.isEmpty(dataMapForInPatientOrdersList)) {
                throw new AppException("请检查信息是否填写完整，当前无数据!");
            }
            handleDi04(dataMapForInPatientOrdersList);

            //最后提交事务commit
            transactionManager.commit(transactionStatus);
            return null;
        } catch (Exception e) {
            transactionManager.rollback(transactionStatus);
            throw new AppException(e.getMessage());
        }
    }

    private void handleDi04(List<InPatientOrdersDataVo> dataMapForInPatientOrdersList) throws Exception {
        List<Map<String, Object>> di04Data = new ArrayList<>();
        try {
            String batchNum = UUID.randomUUID().toString();
            for (InPatientOrdersDataVo data : dataMapForInPatientOrdersList) {
                List<Map<String, Object>> di04List = data.getDi04List();
                di04List.forEach(s -> {
                    s.put("batchNum", batchNum);
                });
                di04Data.addAll(di04List);
            }
            if (!ValidateUtil.isEmpty(di04Data)) {
                busSettleListDao.insertDi04(di04Data);
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new Exception("数据采集-病案首页基础数据出现异常！" + e.toString());
        }
    }

    public Long uploadClinicalExaminationData(List<WebServiceCommonVo> dataMapForClinicalExaminationList) throws Exception {
        //利用编程式事务处理，start transaction
        DefaultTransactionDefinition transactionDefinition = new DefaultTransactionDefinition();
        TransactionStatus transactionStatus = transactionManager.getTransaction(transactionDefinition);
        try {
            if (ValidateUtil.isEmpty(dataMapForClinicalExaminationList)) {
                throw new AppException("请检查信息是否填写完整，当前无数据!");
            }
            String batchNum = UUID.randomUUID().toString();
            handleDi05(dataMapForClinicalExaminationList, batchNum);
            Map<String, Object> map = getDiIdMapping(batchNum, DrgConst.DI_REPEAT_TABLE_DI05);

            handleDi05CheckItem(dataMapForClinicalExaminationList, map);
            handleDi05CheckSpecimen(dataMapForClinicalExaminationList, map);
            handleDi05ImageInspection(dataMapForClinicalExaminationList, map);

            //最后提交事务commit
            transactionManager.commit(transactionStatus);
            return null;
        } catch (Exception e) {
            transactionManager.rollback(transactionStatus);
            throw new AppException(e.getMessage());
        }
    }

    private void handleDi05(List<WebServiceCommonVo> dataMapForClinicalExaminationList, String batchNum) throws Exception {
        List<Map<String, Object>> di05Data = new ArrayList<>();
        try {
            for (WebServiceCommonVo data : dataMapForClinicalExaminationList) {
                Map<String, Object> di05Map = data.getDi05Map();
                di05Map.put("batchNum", batchNum);
                di05Data.add(di05Map);
            }
            if (!ValidateUtil.isEmpty(di05Data)) {
                busSettleListDao.insertDi05(di05Data);
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new Exception("数据采集-病案首页基础数据出现异常！" + e.toString());
        }
    }

    private void handleDi05CheckItem(List<WebServiceCommonVo> dataMapForClinicalExaminationList, Map<String, Object> map) throws Exception {
        List<Map<String, Object>> di05CheckItemData = new ArrayList<>();
        try {
            for (WebServiceCommonVo data : dataMapForClinicalExaminationList) {
                List<Map<String, Object>> di05CheckItemList = data.getDi05CheckItemList();
                fillId(di05CheckItemList, map, DrgConst.DI_REPEAT_TABLE_DI05_PKID);
                di05CheckItemData.addAll(di05CheckItemList);
            }
            if (!ValidateUtil.isEmpty(di05CheckItemData)) {
                busSettleListDao.insertDi05CheckItem(di05CheckItemData);
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new Exception("数据采集-病案首页基础数据出现异常！" + e.toString());
        }
    }

    private void handleDi05CheckSpecimen(List<WebServiceCommonVo> dataMapForClinicalExaminationList, Map<String, Object> map) throws Exception {
        List<Map<String, Object>> di05CheckSpecimenData = new ArrayList<>();
        try {
            for (WebServiceCommonVo data : dataMapForClinicalExaminationList) {
                List<Map<String, Object>> di05CheckSpecimenList = data.getDi05CheckSpecimenList();
                fillId(di05CheckSpecimenList, map, DrgConst.DI_REPEAT_TABLE_DI05_PKID);
                di05CheckSpecimenData.addAll(di05CheckSpecimenList);
            }
            if (!ValidateUtil.isEmpty(di05CheckSpecimenData)) {
                busSettleListDao.insertDi05CheckSpecimen(di05CheckSpecimenData);
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new Exception("数据采集-病案首页基础数据出现异常！" + e.toString());
        }
    }

    private void handleDi05ImageInspection(List<WebServiceCommonVo> dataMapForClinicalExaminationList, Map<String, Object> map) throws Exception {
        List<Map<String, Object>> di05ImageInspectionData = new ArrayList<>();
        try {
            for (WebServiceCommonVo data : dataMapForClinicalExaminationList) {
                List<Map<String, Object>> di05ImageInspectionList = data.getDi05ImageInspectionList();
                fillId(di05ImageInspectionList, map, DrgConst.DI_REPEAT_TABLE_DI05_PKID);
                di05ImageInspectionData.addAll(di05ImageInspectionList);
            }
            if (!ValidateUtil.isEmpty(di05ImageInspectionData)) {
                busSettleListDao.insertDi05ImageInspection(di05ImageInspectionData);
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new Exception("数据采集-病案首页基础数据出现异常！" + e.toString());
        }
    }

    public Long uploadClinicalTrialsData(List<WebServiceCommonVo> dataMapForClinicalTrialsList) throws Exception {
        //利用编程式事务处理，start transaction
        DefaultTransactionDefinition transactionDefinition = new DefaultTransactionDefinition();
        TransactionStatus transactionStatus = transactionManager.getTransaction(transactionDefinition);
        try {
            if (ValidateUtil.isEmpty(dataMapForClinicalTrialsList)) {
                throw new AppException("请检查信息是否填写完整，当前无数据!");
            }
            String batchNum = UUID.randomUUID().toString();
            handleDi06(dataMapForClinicalTrialsList, batchNum);
            Map<String, Object> map = getDiIdMapping(batchNum, DrgConst.DI_REPEAT_TABLE_DI06);

            handleDi06CheckItem(dataMapForClinicalTrialsList, map);
            handleDi06CheckSpecimen(dataMapForClinicalTrialsList, map);

            //最后提交事务commit
            transactionManager.commit(transactionStatus);
            return null;
        } catch (Exception e) {
            transactionManager.rollback(transactionStatus);
            throw new AppException(e.getMessage());
        }
    }

    private void handleDi06(List<WebServiceCommonVo> dataMapForClinicalTrialsList, String batchNum) throws Exception {
        List<Map<String, Object>> di06Data = new ArrayList<>();
        try {
            for (WebServiceCommonVo data : dataMapForClinicalTrialsList) {
                Map<String, Object> di06Map = data.getDi06Map();
                di06Map.put("batchNum", batchNum);
                di06Data.add(di06Map);
            }
            if (!ValidateUtil.isEmpty(di06Data)) {
                busSettleListDao.insertDi06(di06Data);
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new Exception("数据采集-临床检验报告数据出现异常！" + e.toString());
        }
    }

    private void handleDi06CheckItem(List<WebServiceCommonVo> dataMapForClinicalTrialsList, Map<String, Object> map) throws Exception {
        List<Map<String, Object>> di06CheckItemData = new ArrayList<>();
        try {
            for (WebServiceCommonVo data : dataMapForClinicalTrialsList) {
                List<Map<String, Object>> di06CheckItemList = data.getDi06CheckItemList();
                fillId(di06CheckItemList, map, DrgConst.DI_REPEAT_TABLE_DI06_PKID);
                di06CheckItemData.addAll(di06CheckItemList);
            }
            if (!ValidateUtil.isEmpty(di06CheckItemData)) {
                busSettleListDao.insertDi06CheckItem(di06CheckItemData);
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new Exception("数据采集-临床检验报告数据出现异常！" + e.toString());
        }
    }

    private void handleDi06CheckSpecimen(List<WebServiceCommonVo> dataMapForClinicalTrialsList, Map<String, Object> map) throws Exception {
        List<Map<String, Object>> di06CheckSpecimenData = new ArrayList<>();
        try {
            for (WebServiceCommonVo data : dataMapForClinicalTrialsList) {
                List<Map<String, Object>> di06CheckSpecimenList = data.getDi06CheckSpecimenList();
                fillId(di06CheckSpecimenList, map, DrgConst.DI_REPEAT_TABLE_DI06_PKID);
                di06CheckSpecimenData.addAll(di06CheckSpecimenList);
            }
            if (!ValidateUtil.isEmpty(di06CheckSpecimenData)) {
                busSettleListDao.insertDi06CheckSpecimen(di06CheckSpecimenData);
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new Exception("数据采集-临床检验报告数据出现异常！" + e.toString());
        }
    }

    public Long uploadElectronicMedicalData(List<WebServiceCommonVo> list) throws Exception {
        //利用编程式事务处理，start transaction
        DefaultTransactionDefinition transactionDefinition = new DefaultTransactionDefinition();
        TransactionStatus transactionStatus = transactionManager.getTransaction(transactionDefinition);
        try {
            if (ValidateUtil.isEmpty(list)) {
                throw new AppException("请检查信息是否填写完整，当前无数据!");
            }
            String batchNum = UUID.randomUUID().toString();
            handleDi07(list, batchNum);
            Map<String, Object> map = getDiIdMapping(batchNum, DrgConst.DI_REPEAT_TABLE_DI07);

            handleDi07Diagnosis(list, map);
            handleDi07CourseRecord(list, map);
            handleDi07Operation(list, map);
            handleDi07Resuce(list, map);
            handleDi07Obituary(list, map);
            handleDi07OutHos(list, map);

            //最后提交事务commit
            transactionManager.commit(transactionStatus);
            return null;
        } catch (Exception e) {
            transactionManager.rollback(transactionStatus);
            throw new AppException(e.getMessage());
        }
    }

    private void handleDi07(List<WebServiceCommonVo> list, String batchNum) throws Exception {
        List<Map<String, Object>> tempList = new ArrayList<>();
        try {
            for (WebServiceCommonVo data : list) {
                Map<String, Object> di07Map = data.getDi07Map();
                di07Map.put("batchNum", batchNum);
                tempList.add(di07Map);
            }
            if (!ValidateUtil.isEmpty(tempList)) {
                busSettleListDao.insertDi07(tempList);
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new Exception("数据采集-电子病历数据出现异常！" + e.toString());
        }
    }

    private void handleDi07Diagnosis(List<WebServiceCommonVo> list, Map<String, Object> map) throws Exception {
        List<Map<String, Object>> tempList = new ArrayList<>();
        try {
            for (WebServiceCommonVo data : list) {
                List<Map<String, Object>> di07DiagnosisList = data.getDi07DiagnosisList();
                fillId(di07DiagnosisList, map, DrgConst.DI_REPEAT_TABLE_DI07_PKID);
                tempList.addAll(di07DiagnosisList);
            }
            if (!ValidateUtil.isEmpty(tempList)) {
                busSettleListDao.insertDi07Diagnosis(tempList);
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new Exception("数据采集-电子病历数据出现异常！" + e.toString());
        }
    }

    private void handleDi07CourseRecord(List<WebServiceCommonVo> list, Map<String, Object> map) throws Exception {
        List<Map<String, Object>> tempList = new ArrayList<>();
        try {
            for (WebServiceCommonVo data : list) {
                List<Map<String, Object>> di07CourseRecordList = data.getDi07CourseRecordList();
                fillId(di07CourseRecordList, map, DrgConst.DI_REPEAT_TABLE_DI07_PKID);
                tempList.addAll(di07CourseRecordList);
            }
            if (!ValidateUtil.isEmpty(tempList)) {
                busSettleListDao.insertDi07CourseRecord(tempList);
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new Exception("数据采集-电子病历数据出现异常！" + e.toString());
        }
    }

    private void handleDi07Operation(List<WebServiceCommonVo> list, Map<String, Object> map) throws Exception {
        List<Map<String, Object>> tempList = new ArrayList<>();
        try {
            for (WebServiceCommonVo data : list) {
                List<Map<String, Object>> di07OperationList = data.getDi07OperationList();
                fillId(di07OperationList, map, DrgConst.DI_REPEAT_TABLE_DI07_PKID);
                tempList.addAll(di07OperationList);
            }
            if (!ValidateUtil.isEmpty(tempList)) {
                busSettleListDao.insertDi07Operation(tempList);
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new Exception("数据采集-电子病历数据出现异常！" + e.toString());
        }
    }

    private void handleDi07Resuce(List<WebServiceCommonVo> list, Map<String, Object> map) throws Exception {
        List<Map<String, Object>> tempList = new ArrayList<>();
        try {
            for (WebServiceCommonVo data : list) {
                List<Map<String, Object>> di07ResuceList = data.getDi07ResuceList();
                fillId(di07ResuceList, map, DrgConst.DI_REPEAT_TABLE_DI07_PKID);
                tempList.addAll(di07ResuceList);
            }
            if (!ValidateUtil.isEmpty(tempList)) {
                busSettleListDao.insertDi07Resuce(tempList);
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new Exception("数据采集-电子病历数据出现异常！" + e.toString());
        }
    }

    private void handleDi07Obituary(List<WebServiceCommonVo> list, Map<String, Object> map) throws Exception {
        List<Map<String, Object>> tempList = new ArrayList<>();
        try {
            for (WebServiceCommonVo data : list) {
                List<Map<String, Object>> di07ObituaryList = data.getDi07ObituaryList();
                fillId(di07ObituaryList, map, DrgConst.DI_REPEAT_TABLE_DI07_PKID);
                tempList.addAll(di07ObituaryList);
            }
            if (!ValidateUtil.isEmpty(tempList)) {
                busSettleListDao.insertDi07Obituary(tempList);
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new Exception("数据采集-电子病历数据出现异常！" + e.toString());
        }
    }

    private void handleDi07OutHos(List<WebServiceCommonVo> list, Map<String, Object> map) throws Exception {
        List<Map<String, Object>> tempList = new ArrayList<>();
        try {
            for (WebServiceCommonVo data : list) {
                List<Map<String, Object>> di07OutHosList = data.getDi07OutHosList();
                fillId(di07OutHosList, map, DrgConst.DI_REPEAT_TABLE_DI07_PKID);
                tempList.addAll(di07OutHosList);
            }
            if (!ValidateUtil.isEmpty(tempList)) {
                busSettleListDao.insertDi07OutHos(tempList);
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new Exception("数据采集-电子病历数据出现异常！" + e.toString());
        }
    }

    public Long uploadOutPatientTreatmentData(List<WebServiceCommonVo> list) throws Exception {
        //利用编程式事务处理，start transaction
        DefaultTransactionDefinition transactionDefinition = new DefaultTransactionDefinition();
        TransactionStatus transactionStatus = transactionManager.getTransaction(transactionDefinition);
        try {
            if (ValidateUtil.isEmpty(list)) {
                throw new AppException("请检查信息是否填写完整，当前无数据!");
            }
            //一个事务内的业务代码
            String hospitalId = "";
//            //获取医院信息
//            List<SomHospInfo> somHospInfo = busHospitalMapper.selectByExample(new SomHospInfoExample());
//            if(!ValidateUtil.isEmpty(somHospInfo)){
//                hospitalId = somHospInfo.get(0).getHospitalId();
//            }

            List<Map<String, Object>> tempList = new ArrayList<>();
            for (WebServiceCommonVo vo : list) {
                Map<String, Object> list1 = vo.getDi08Map();
                tempList.add(list1);
            }
            if (!ValidateUtil.isEmpty(tempList)) {
                List<SomMedcasIntfBasInfo> di02s = busSettleListDao.selectDi08DataInfo(tempList);
                if (ValidateUtil.isNotEmpty(di02s)) {
                    List<String> list1 = new ArrayList<>();
                    for (SomMedcasIntfBasInfo som_medcas_intf_bas_info : di02s) {
                        list1.add(som_medcas_intf_bas_info.getId());
                        commonDao.deleteDi08Data(list1);
                        commonDao.deleteDi08DiagnosisData(list1);
                    }
                }
            }

            handleDi08(list, hospitalId);
            Map<String, Object> map = getDi08Id();

            handleDi08Diagnosis(list, map);

            //最后提交事务commit
            transactionManager.commit(transactionStatus);
            return null;
        } catch (Exception e) {
            transactionManager.rollback(transactionStatus);
            throw new AppException(e.getMessage());
        }
    }

    private void handleDi08(List<WebServiceCommonVo> list, String hospitalId) throws Exception {
        List<Map<String, Object>> tempList = new ArrayList<>();
        try {
            for (WebServiceCommonVo data : list) {
                Map<String, Object> di08Map = data.getDi08Map();
                tempList.add(di08Map);
            }
            if (!ValidateUtil.isEmpty(tempList)) {
                busSettleListDao.insertDi08(tempList);
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new Exception("数据采集-门诊就诊信息数据出现异常！" + e.toString());
        }
    }

    private void handleDi08Diagnosis(List<WebServiceCommonVo> list, Map<String, Object> map) throws Exception {
        List<Map<String, Object>> tempList = new ArrayList<>();
        try {
            for (WebServiceCommonVo data : list) {
                List<Map<String, Object>> di08DiagnosisList = data.getDi08DiagnosisList();
                for (Map<String, Object> objectMap : di08DiagnosisList) {
                    objectMap.put("di08_id", map.get(objectMap.get("mdtrt_id")));
                }
                tempList.addAll(di08DiagnosisList);
            }
            if (!ValidateUtil.isEmpty(tempList)) {
                busSettleListDao.insertDi08Diagnosis(tempList);
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new Exception("数据采集-门诊就诊信息数据出现异常！" + e.toString());
        }
    }

    public Long uploadPatientExpensesData(List<WebServiceCommonVo> list) throws Exception {
        //利用编程式事务处理，start transaction
        DefaultTransactionDefinition transactionDefinition = new DefaultTransactionDefinition();
        TransactionStatus transactionStatus = transactionManager.getTransaction(transactionDefinition);
        try {
            if (ValidateUtil.isEmpty(list)) {
                throw new AppException("请检查信息是否填写完整，当前无数据!");
            }
            //一个事务内的业务代码
            String hospitalId = "";
//            //获取医院信息
//            List<SomHospInfo> somHospInfo = busHospitalMapper.selectByExample(new SomHospInfoExample());
//            if(!ValidateUtil.isEmpty(somHospInfo)){
//                hospitalId = somHospInfo.get(0).getHospitalId();
//            }

            List<Map<String, Object>> tempList = new ArrayList<>();
            for (WebServiceCommonVo vo : list) {
                List<Map<String, Object>> list1 = vo.getDi09List();
                tempList.addAll(list1);
            }
            if (!ValidateUtil.isEmpty(tempList)) {
                List<SomMedcasIntfBasInfo> di02s = busSettleListDao.selectDi09DataInfo(tempList);
                if (ValidateUtil.isNotEmpty(di02s)) {
                    List<String> list1 = new ArrayList<>();
                    for (SomMedcasIntfBasInfo som_medcas_intf_bas_info : di02s) {
                        list1.add(som_medcas_intf_bas_info.getId());
                        commonDao.deleteDi09Data(list1);
                    }
                }
            }

            handleDi09(list, hospitalId);

            //最后提交事务commit
            transactionManager.commit(transactionStatus);
            return null;
        } catch (Exception e) {
            transactionManager.rollback(transactionStatus);
            throw new AppException(e.getMessage());
        }
    }

    private void handleDi09(List<WebServiceCommonVo> list, String hospitalId) throws Exception {
        List<Map<String, Object>> tempList = new ArrayList<>();
        try {
            for (WebServiceCommonVo data : list) {
                List<Map<String, Object>> di09List = data.getDi09List();
                tempList.addAll(di09List);
            }
            if (!ValidateUtil.isEmpty(tempList)) {
                busSettleListDao.insertDi09(tempList);
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new Exception("数据采集-门诊费用明细信息数据出现异常！" + e.toString());
        }
    }

    public Long uploadDeptItemData(List<WebServiceCommonVo> list) throws Exception {
        //利用编程式事务处理，start transaction
        DefaultTransactionDefinition transactionDefinition = new DefaultTransactionDefinition();
        TransactionStatus transactionStatus = transactionManager.getTransaction(transactionDefinition);
        try {
            if (ValidateUtil.isEmpty(list)) {
                throw new AppException("请检查信息是否填写完整，当前无数据!");
            }
            //一个事务内的业务代码
            String hospitalId = "";
//            //获取医院信息
//            List<SomHospInfo> somHospInfo = busHospitalMapper.selectByExample(new SomHospInfoExample());
//            if(!ValidateUtil.isEmpty(somHospInfo)){
//                hospitalId = somHospInfo.get(0).getHospitalId();
//            }

            List<Map<String, Object>> tempList = new ArrayList<>();
            for (WebServiceCommonVo vo : list) {
                Map<String, Object> list1 = vo.getDi10Map();
                tempList.add(list1);
            }
            if (!ValidateUtil.isEmpty(tempList)) {
                List<SomMedcasIntfBasInfo> di02s = busSettleListDao.selectDi10DataInfo(tempList);
                if (ValidateUtil.isNotEmpty(di02s)) {
                    List<String> list1 = new ArrayList<>();
                    for (SomMedcasIntfBasInfo som_medcas_intf_bas_info : di02s) {
                        list1.add(som_medcas_intf_bas_info.getId());
                        commonDao.deleteDi10Data(list1);
                    }
                }
            }

            handleDi10(list, hospitalId);

            //最后提交事务commit
            transactionManager.commit(transactionStatus);
            return null;
        } catch (Exception e) {
            transactionManager.rollback(transactionStatus);
            throw new AppException(e.getMessage());
        }
    }

    private void handleDi10(List<WebServiceCommonVo> list, String hospitalId) throws Exception {
        List<Map<String, Object>> tempList = new ArrayList<>();
        try {
            for (WebServiceCommonVo data : list) {
                Map<String, Object> di10List = data.getDi10Map();
                tempList.add(di10List);
            }
            if (!ValidateUtil.isEmpty(tempList)) {
                busSettleListDao.insertDi10(tempList);
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new Exception("数据采集-科室信息数据出现异常！" + e.toString());
        }
    }

    /**
     * 查询k00及对应settle_list表id
     */
    private void handlerFundPay(String type, Map<String, Long> map) {
        List<String> k00s = new ArrayList<>();
        List<Map<String, Object>> mapList = new ArrayList<>();
        if (type.equals("1")) {
            mapList.addAll(busSettleListDao.queryDi03Data());
        }
        if (type.equals("2")) {
            for (String s : map.keySet()) {
                Map<String, Object> hashMap = new HashMap<>();
                hashMap.put("mdtrt_id", s);
                mapList.add(hashMap);
            }
        }
        List<SettleListVo> listVos = new ArrayList<>();
        if (!ValidateUtil.isEmpty(mapList)) {
            for (Map<String, Object> stringObjectMap : mapList) {
                SettleListVo listVo = new SettleListVo();
                listVo.setK00((String) stringObjectMap.get("mdtrt_id"));
                k00s.add((String) stringObjectMap.get("mdtrt_id"));
                listVos.add(listVo);
            }

            Integer i = removalDi03(listVos);
            if (i >= 0) {
                List<SettleListVo> idsToK00s = busSettleListDao.queryIdsToK00s(k00s);
                if (!ValidateUtil.isEmpty(idsToK00s)) {
                    List<Map<String, Object>> mapsList = busSettleListDao.queryDi03DetailData(idsToK00s);
                    List<InhosSettleDataVo> dataVos = new ArrayList<>();
                    if (!ValidateUtil.isEmpty(mapsList)) {
                        for (Map<String, Object> stringObjectMap : mapsList) {
                            InhosSettleDataVo inhosSettleDataVo = new InhosSettleDataVo(stringObjectMap);
                            dataVos.add(inhosSettleDataVo);
                        }
                        Integer n = removalBusFundPay(idsToK00s);
                        putFundPay(idsToK00s, dataVos);
                    }

                }
            }
        }


    }

    private Integer removalDi03(List<SettleListVo> idsToK00s) {
        Integer i = busSettleListDao.deleteDi03RepeatData(idsToK00s);
        return i;
    }

    private Integer removalBusFundPay(List<SettleListVo> idsToK00s) {
        Integer n = busSettleListDao.deleteBusFundPayData(idsToK00s);
        return n;
    }

    /**
     * settleList表id与患者对应，插入bus_fund_pay
     *
     * @param idsToK00s
     * @param list
     */
    private void putFundPay(List<SettleListVo> idsToK00s, List<InhosSettleDataVo> list) {
        List<Map<String, Object>> fundPayData = new ArrayList<>();
        for (SettleListVo idToK00 : idsToK00s) {
            for (InhosSettleDataVo fundPayVo : list) {
                if (idToK00.getK00().equals(fundPayVo.getDi03Map().get("mdtrt_id"))) {
                    fieldMapping(idToK00, fundPayVo, fundPayData);
                }
            }
        }
        int i = busFundPayDao.insertFundPayData(fundPayData);
        if (i >= 1) {
            busSettleListDao.updateDi03(fundPayData);
        }
    }

    /**
     * 设置需要插入的值
     *
     * @param idToK00
     * @param fundPayVo
     * @param fundPayData
     */
    private void fieldMapping(SettleListVo idToK00, InhosSettleDataVo fundPayVo, List<Map<String, Object>> fundPayData) {
        for (String type : FUND_PAY_FIELD_MAPPING.keySet()) {
            Map<String, Object> fundPayMap = new HashMap<>();
            fundPayMap.put("settle_list_id", idToK00.getId());
            fundPayMap.put("k00", idToK00.getK00());
            fundPayMap.put("fund_pay_type", type);
            fundPayMap.put("pool_coty_fund_pay_type", null);
            fundPayMap.put("pool_coty_fund_pay_type_name", null);
            fundPayMap.put("fund_payamt", fundPayVo.getDi03Map().get(FUND_PAY_FIELD_MAPPING.get(type)));
            fundPayData.add(fundPayMap);
        }
    }

    /**
     * 处理视图数据
     */
    public void handleDataView(List<String> mdtrtIdList) throws Exception {
        Connection connection = SqlServerConnectionUtil.getSqlServerConnection();
        handleDi04Data(mdtrtIdList, connection);
        handleDi11Data(mdtrtIdList, connection);
        handleDi05Data(mdtrtIdList, connection);
        handleDi06Data(mdtrtIdList, connection);
        handleDi07Data(mdtrtIdList, connection);
        SqlServerConnectionUtil.closeSqlServerConnection(connection);
    }

    /**
     * 处理di04视图数据
     * 住院医嘱记录
     */
    private void handleDi04Data(List<String> ids, Connection connection) throws Exception {
        List<String> haveIds = new ArrayList<>();
        // 查询ids在di04没有的id
        for (String id :
                ids) {
            int count = handleUpdateDataMapper.selectDi04ById(id);
            if (count == 0) {
                haveIds.add(id);
            }
        }
        // 更新数据
        if (!ValidateUtil.isEmpty(haveIds)) {
            // 通过id去查SQL server
            Map<String, List<Map<String, Object>>> advRecordsMap = SqlServerConnectionUtil.getadvRecords(haveIds, connection);
            if (!ValidateUtil.isEmpty(advRecordsMap)) {
                // 添加到di04表中
                List<InPatientOrdersDataVo> list = new ArrayList();
                InPatientOrdersDataVo vo = new InPatientOrdersDataVo(advRecordsMap);
                list.add(vo);
                uploadInPatientOrdersData(list);
            }
        }
    }

    /**
     * 处理di11视图数据
     * 住院费用明细
     */
    private void handleDi11Data(List<String> ids, Connection connection) throws Exception {
        List<String> haveIds = new ArrayList<>();
        // 查询ids在di11没有的id
        for (String id :
                ids) {
            int count = handleUpdateDataMapper.selectDi11ById(id);
            if (count == 0) {
                haveIds.add(id);
            }
        }
        // 更新数据
        if (!ValidateUtil.isEmpty(haveIds)) {
            // 通过id去查SQL server
            Map<String, List<Map<String, Object>>> costDetailMap = SqlServerConnectionUtil.getcostDetail(haveIds, connection);
            if (!ValidateUtil.isEmpty(costDetailMap)) {
                // 添加到di11表中
                List<InhosDetailDataVo> list = new ArrayList<>();
                InhosDetailDataVo vo = new InhosDetailDataVo(costDetailMap);
                list.add(vo);
                uploadInhosDetailData(list);
            }
        }
    }

    /**
     * 处理di05视图数据
     * 临床检查报告
     */
    private void handleDi05Data(List<String> ids, Connection connection) throws Exception {
        List<String> haveIds = new ArrayList<>();
        // 查询ids在di05没有的id
        for (String id :
                ids) {
            int count = handleUpdateDataMapper.selectDi05ById(id);
            if (count == 0) {
                haveIds.add(id);
            }
        }
        // 更新数据
        if (!ValidateUtil.isEmpty(haveIds)) {
            List<Map<String, Object>> clinicalExaData = SqlServerConnectionUtil.getclinicalExa(haveIds, connection);
            if (!ValidateUtil.isEmpty(clinicalExaData)) {
                // 添加到di05表中
                List<WebServiceCommonVo> list = new ArrayList<>();
                for (Map<String, Object> dataMap :
                        clinicalExaData) {
                    WebServiceCommonVo vo = new WebServiceCommonVo(dataMap);
                    list.add(vo);
                }
                uploadClinicalExaminationData(list);
            }
        }
    }

    /**
     * 处理di06视图数据
     * 临床检验报告
     */
    private void handleDi06Data(List<String> ids, Connection connection) throws Exception {
        List<String> haveIds = new ArrayList<>();
        // 查询ids在di06没有的id
        for (String id :
                ids) {
            int count = handleUpdateDataMapper.selectDi06ById(id);
            if (count == 0) {
                haveIds.add(id);
            }
        }
        // 更新数据
        if (!ValidateUtil.isEmpty(haveIds)) {
            List<Map<String, Object>> clinicalTestData = SqlServerConnectionUtil.getclinicalTest(haveIds, connection);
            if (!ValidateUtil.isEmpty(clinicalTestData)) {
                // 添加到di06表中
                List<WebServiceCommonVo> list = new ArrayList<>();
                for (Map<String, Object> dataMap :
                        clinicalTestData) {
                    WebServiceCommonVo vo = new WebServiceCommonVo(dataMap);
                    list.add(vo);
                }
                uploadClinicalTrialsData(list);
            }
        }
    }

    /**
     * 处理di07视图数据
     * 电子病历数据
     */
    private void handleDi07Data(List<String> ids, Connection connection) throws Exception {
        List<String> haveIds = new ArrayList<>();
        // 查询ids在di07没有的id
        for (String id :
                ids) {
            int count = handleUpdateDataMapper.selectDi07ById(id);
            if (count == 0) {
                haveIds.add(id);
            }
        }
        // 更新数据
        if (!ValidateUtil.isEmpty(haveIds)) {
            List<Map<String, Object>> emrData = SqlServerConnectionUtil.getEmr(haveIds, connection);
            if (!ValidateUtil.isEmpty(emrData)) {
                // 添加到di07表中
                List<WebServiceCommonVo> list = new ArrayList<>();
                for (Map<String, Object> dataMap :
                        emrData) {
                    WebServiceCommonVo vo = new WebServiceCommonVo(dataMap);
                    list.add(vo);
                }
                uploadElectronicMedicalData(list);
            }
        }
    }

    /**
     * 查询未抽取数量
     *
     * @return
     */
    public int queryNoExtract() {
        return busSettleListDao.queryNoExtract();
    }

    /**
     * 查询未抽取数量
     *
     * @return
     */
    public int queryNoExtractList() {
        return busSettleListDao.queryNoExtractList();
    }


}

