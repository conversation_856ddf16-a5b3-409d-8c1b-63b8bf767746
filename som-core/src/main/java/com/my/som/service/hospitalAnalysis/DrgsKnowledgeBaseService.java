package com.my.som.service.hospitalAnalysis;

import com.my.som.dto.hospitalAnalysis.HospitalAnalysisQueryParam;
import com.my.som.vo.hospitalAnalysis.DrgsCoverRateVo;
import com.my.som.vo.hospitalAnalysis.DrgsKnowledgeVo;

import java.util.List;

/**
 * Service
 * Created by sky on 2020/3/20.
 */
public interface DrgsKnowledgeBaseService {
    /**
     * DRGs知识库总体指标情况
     * @param queryParam
     * @return
     */
    List<DrgsKnowledgeVo> list(HospitalAnalysisQueryParam queryParam);

    /**
     * 查询全院DRGs分组覆盖占比
     * @param queryParam
     * @return
     */
    DrgsCoverRateVo getCountByCoverRate(HospitalAnalysisQueryParam queryParam);

}
