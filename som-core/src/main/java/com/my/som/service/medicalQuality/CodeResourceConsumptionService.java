package com.my.som.service.medicalQuality;

import com.my.som.dto.medicalQuality.*;

import java.util.List;

/**
 * 疾病诊断资源消耗Service
 * Created by sky on 2020/3/2.
 */
public interface CodeResourceConsumptionService {

    /**
     * 分页查询所有疾病诊断资源消耗结果
     * @param queryParam
     * @param pageSize
     * @param pageNum
     * @return
     */
    List<CodeResourceConsumptionVo> list(SettleListMainInfoQueryParam queryParam, Integer pageSize, Integer pageNum);


    /**
     * 查询疾病诊断资源消耗统计信息
     * @param queryParam
     * @return
     */
    CodeResourceConsumptionCountVo getCountInfo(SettleListMainInfoQueryParam queryParam);
}
