package com.my.som.service.dataHandle.impl;

import com.my.som.common.constant.DrgConst;
import com.my.som.common.util.DateUtil;
import com.my.som.common.util.RedisUtils;
import com.my.som.common.util.ValidateUtil;
import com.my.som.config.DynamicSplitNewTask;
import com.my.som.model.dataHandle.*;
import com.my.som.service.dataHandle.DataRecordService;
import com.my.som.service.dataHandle.ValidateAndCleanJobService;
import com.my.som.util.DrgValidateUtil;
import com.my.som.vo.dataHandle.DataHandleProgressVo;
import com.my.som.vo.dataHandle.dataGroup.HospitalDipVo;
import com.my.som.vo.dataHandle.dataGroup.HospitalDrgVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.*;



/**
 * @author: zyd
 * 病案校验和数据清洗Service实现类
 * Ver:       1.0
 * Date:          2020/4/20 16:00
 * Copyright (C),2020 Freedom
 */
@Service
public class ValidateAndCleanJobServiceImpl implements ValidateAndCleanJobService {
    Logger logger = LoggerFactory.getLogger(ValidateAndCleanJobServiceImpl.class);
    @Autowired
    private DataRecordService dataRecordService;

    /**
     * 案校验和数据清洗
     * @param logId 数据处理日志号--批次号
     * @return
     */
    @Override
    public String validateAndCleanDataByLogId(String logId) {
        String exceptionStr = "";
        Map<String,Object> queryMap = new HashMap<>();
        logger.info("==========批次号："+logId+",执行任务：病案校验和数据清洗！==========");
        SomDataprosLog validateAndCleanInfo = new SomDataprosLog();
        try{
            //查询进度值应有值
            queryMap.put("logId",logId);
            SomDataprosLog somDataprosLog = dataRecordService.queryRecordById(queryMap);
            String hospitalId = somDataprosLog.getHospitalId();//获取医疗机构编码
            queryMap.put("hospital_id",hospitalId);
            queryMap.put("enab_flag",DrgConst.START_FLAG_1);
            List<DataHandleProgressVo> DataHandleProgressVoList= dataRecordService.queryCurProgress(queryMap);
            Integer prcs_prgs = 0;
            for(DataHandleProgressVo dhp:DataHandleProgressVoList){
                if(DrgConst.VALIDATE.equals(dhp.getTaskName())){
                    prcs_prgs = Integer.valueOf(dhp.getEndProgress());
                }
                if(DrgConst.CLEANDATA.equals(dhp.getTaskName())){
                    prcs_prgs = Integer.valueOf(dhp.getEndProgress());
                }
            }
            SomDataprosLog validateAndCleanInfoIng = new SomDataprosLog();
            validateAndCleanInfoIng.setResult("正在处理病案校验和数据清洗");
            validateAndCleanInfoIng.setId(Long.valueOf(logId));
            dataRecordService.updateDataHandleLogById(validateAndCleanInfoIng);
            long start = System.currentTimeMillis();
            validateAndCleanInfo = validateAndCleanSettleList(logId,hospitalId,DrgConst.CLEANDATA_TRUE);
            long end = System.currentTimeMillis();
            validateAndCleanInfo.setId(Long.valueOf(logId));
            if(ValidateUtil.isEmpty(somDataprosLog.getDataprosDura())){
                somDataprosLog.setDataprosDura(new BigDecimal("0"));
            }
            validateAndCleanInfo.setDataprosDura(somDataprosLog.getDataprosDura().add(new BigDecimal((end-start)/1000))); //校验和清洗耗时（m）
            validateAndCleanInfo.setResult("病案校验和数据清洗处理成功");
            validateAndCleanInfo.setPrcs_prgs(prcs_prgs);
            if(prcs_prgs==100){
                validateAndCleanInfo.setResult("success");  //标志整个流程执行成功
            }
            dataRecordService.updateDataHandleLogById(validateAndCleanInfo);
            logger.info("==========病案校验和数据清洗信息：总共校验"+validateAndCleanInfo.getMedcasVal()+"条病案");
            logger.info("==========完整性校验通过数:"+validateAndCleanInfo.getIntegrityChkPassVal());
            logger.info("==========逻辑性校验通过数:"+validateAndCleanInfo.getLogicChkPassVal());
            logger.info("==========数据清洗通过数:"+validateAndCleanInfo.getDataCleanPassVal());
        }catch (Exception e){
            //此处结束删除
            RedisUtils.delete("queue"+logId);
            e.printStackTrace();
            logger.info("==========病案校验和数据清洗出现异常！异常信息："+e.toString());
            exceptionStr = "exp:病案校验和数据清洗出现异常！异常信息："+e.toString();
            return exceptionStr;
        }
        return exceptionStr;
    }

    /**
     * 校验和清洗结算清单数据
     * @param logId
     * @param hospitalId
     * @return
     */
    private SomDataprosLog validateAndCleanSettleList(String logId,String hospitalId,boolean isCleanData) throws Exception {
        Integer start = 0, limit = 500,totals = 0,integrity_chk_pass_val = 0,logic_chk_pass_val = 0,data_clean_pass_val=0;
        List<Map<String, Object>> datas = new ArrayList<>();
        Map<String, List<BusDiseaseDiagnosisCost>> diseaseMap = new HashMap<>(); //settle_list_id和疾病诊断信息的对应关系
        Map<String, List<BusOperateDiagnosisCost>> operationMap = new HashMap<>();//settle_list_id和手术信息的对应关系
        SomCodgResuAdjmRcd somCodgResuAdjmRcd = null;
        SomSetlInvyChkErrRcd somSetlInvyChkErrRcd = null;
        SomSetlInvyQltDeduPointDetl somSetlInvyQltDeduPointDetl = null;
        List<SomSetlInvyChkErrRcd> sves = null;
        List<SomSetlInvyQltDeduPointDetl> svlss = null;
        List<SomCodgResuAdjmRcd> scrc = null;
        Map<String,Object> queryParam = new HashMap<>();
        SomDataprosLog somDataprosLog = new SomDataprosLog();
        queryParam.put("logId",logId);
        List<SomMedcasInGroupCfg> cfgMedicalGroups = dataRecordService.getSomMedcasInGroupCfg();  //全查已启用的清洗规则
        HospitalDrgVo drgBrustInfo = dataRecordService.getHospitalDrgByHospitalId(hospitalId);//根据医院编号获取医院DRG分组器信息
        HospitalDipVo dipBrustInfo = dataRecordService.getHospitalDipByHospitalId(hospitalId);//根据医院编号获取医院DIP分组器信息
        queryParam.put("hospLv",drgBrustInfo.getHospLv());
        List<String> cleanDataRules = new ArrayList<>();
        for(SomMedcasInGroupCfg cmg:cfgMedicalGroups){
            cleanDataRules.add(cmg.getErrorCode());
        }
        //开8个线程
        ExecutorService threadPool = Executors.newFixedThreadPool(8);
        CompletionService<List<Map<String,Object>>> cs = new ExecutorCompletionService<List<Map<String,Object>>>(threadPool);
        List<Map<String,Object>> errors_and_score = new ArrayList<>();
        do{
            sves = new ArrayList<>();
            svlss = new ArrayList<>();
            scrc = new ArrayList<>();
            //多线程校验
            for (final Map<String, Object> data:datas) {
                //查询该病人所有疾病、手术信息，放入各自list中
                List<BusDiseaseDiagnosisCost> disease = new ArrayList<>();
                List<BusOperateDiagnosisCost> oprt = new ArrayList<>();
                if(!ValidateUtil.isEmpty(diseaseMap.get(String.valueOf(data.get("id"))))){
                    disease = diseaseMap.get(String.valueOf(data.get("id")));
                }
                if(!ValidateUtil.isEmpty(operationMap.get(String.valueOf(data.get("id"))))){
                    oprt = operationMap.get(String.valueOf(data.get("id")));
                }
                List<BusDiseaseDiagnosisCost> finalDisease = disease;
                List<BusOperateDiagnosisCost> finalOperation = oprt;
                cs.submit(new Callable<List<Map<String,Object>>>() {
                    public List<Map<String,Object>> call() throws Exception {
                        return DrgValidateUtil.validateData(data, finalDisease, finalOperation,isCleanData,cleanDataRules,drgBrustInfo,dipBrustInfo);
                    }
                });
            }
            //校验后处理
            totals = totals + datas.size();
            for (int i = 0; i < datas.size(); i++) {
                errors_and_score = cs.take().get();
                //
                if(!ValidateUtil.isEmpty(errors_and_score)){//更新校验不通过
                    //完整性校验病案通过数
                    if(!errors_and_score.get(0).containsKey(DrgConst.ERROR_TYPE_CE01)&&!errors_and_score.get(0).containsKey(DrgConst.ERROR_TYPE_CE02)
                        &&!errors_and_score.get(0).containsKey(DrgConst.ERROR_TYPE_CE03)&&!errors_and_score.get(0).containsKey(DrgConst.ERROR_TYPE_CE04)){
                        integrity_chk_pass_val++;
                    }
                    //逻辑性校验病案通过数
                    if(!errors_and_score.get(0).containsKey(DrgConst.ERROR_TYPE_LE01)&&!errors_and_score.get(0).containsKey(DrgConst.ERROR_TYPE_LE02)
                        &&!errors_and_score.get(0).containsKey(DrgConst.ERROR_TYPE_LE03)&&!errors_and_score.get(0).containsKey(DrgConst.ERROR_TYPE_LE04)
                        &&!errors_and_score.get(0).containsKey(DrgConst.ERROR_TYPE_LE05)&&!errors_and_score.get(0).containsKey(DrgConst.ERROR_TYPE_LE06)
                        &&!errors_and_score.get(0).containsKey(DrgConst.ERROR_TYPE_LE07)){
                        logic_chk_pass_val++;
                    }
                    //清洗病案通过数
                    if(!errors_and_score.get(0).containsKey(DrgConst.ERROR_TYPE_EE01)&&!errors_and_score.get(0).containsKey(DrgConst.ERROR_TYPE_EE02)
                            &&!errors_and_score.get(0).containsKey(DrgConst.ERROR_TYPE_EE03)){
                        data_clean_pass_val++;
                    }
                    //第一部分：字段完整性校验、逻辑性校验
                    for(String key:errors_and_score.get(0).keySet()){
                        if("id".equals(key)||"completeErrors".equals(key)||"logicErrors".equals(key)){
                            continue;
                        }else if("somCodgResuAdjmRcd".equals(key)){
                            somCodgResuAdjmRcd = (SomCodgResuAdjmRcd) errors_and_score.get(0).get(key);
//                            somCodgResuAdjmRcd.setSettleListId(((SomCodgResuAdjmRcd) errors_and_score.get(0).get(key)).getSettleListId());
//                            somCodgResuAdjmRcd.setHospitalId(((SomCodgResuAdjmRcd) errors_and_score.get(0).get(key)).getHospitalId());
//                            somCodgResuAdjmRcd.setMainDiagDiseCodg(((SomCodgResuAdjmRcd) errors_and_score.get(0).get(key)).getMainDiagDiseCodg());
//                            somCodgResuAdjmRcd.setMainOprnCodg(((SomCodgResuAdjmRcd) errors_and_score.get(0).get(key)).getMainOprnCodg());
//                            somCodgResuAdjmRcd.setMainDiagIsChoErr(((SomCodgResuAdjmRcd) errors_and_score.get(0).get(key)).getMainDiagIsChoErr());
//                            somCodgResuAdjmRcd.setMainOprnIsChoErr(((SomCodgResuAdjmRcd) errors_and_score.get(0).get(key)).getMainOprnIsChoErr());
//                            somCodgResuAdjmRcd.setResuCosmAdjmNewMainDiagCodg(((SomCodgResuAdjmRcd) errors_and_score.get(0).get(key)).getResuCosmAdjmNewMainDiagCodg());
//                            somCodgResuAdjmRcd.setResuCosmAdjmNewMainOprnCodg(((SomCodgResuAdjmRcd) errors_and_score.get(0).get(key)).getResuCosmAdjmNewMainOprnCodg());
//                            somCodgResuAdjmRcd.setResuCosmAdjmNewDrgCodg(((SomCodgResuAdjmRcd) errors_and_score.get(0).get(key)).getResuCosmAdjmNewDrgCodg());
                            somCodgResuAdjmRcd.setDataLogId(Long.valueOf(logId));
                            scrc.add(somCodgResuAdjmRcd);
                        }else{
                            //写入错误日志表
                            somSetlInvyChkErrRcd = new SomSetlInvyChkErrRcd();
                            somSetlInvyChkErrRcd.setSettleListId(Long.valueOf((String) errors_and_score.get(0).get("id")));//结算清单ID
                            somSetlInvyChkErrRcd.setHospitalId(hospitalId);//医疗机构id
                            somSetlInvyChkErrRcd.setErrType(key);//错误类型
                            somSetlInvyChkErrRcd.setErrDscr((String) errors_and_score.get(0).get(key));//错误描述
                            somSetlInvyChkErrRcd.setDataLogId(Long.valueOf(logId));//校验日志号
                            somSetlInvyChkErrRcd.setOprDate(DateUtil.getCurDate());
                            sves.add(somSetlInvyChkErrRcd);
                        }
                    }
                    //第二部分：病案质量扣分校验
                    //写入扣分日志表
                    if(errors_and_score.size()>1){
                        somSetlInvyQltDeduPointDetl = new SomSetlInvyQltDeduPointDetl();
                        somSetlInvyQltDeduPointDetl.setSettleListId(Long.valueOf(errors_and_score.get(1).get("settle_list_id").toString()));//结算清单ID
                        somSetlInvyQltDeduPointDetl.setHospitalId(hospitalId);//医疗机构id;
                        somSetlInvyQltDeduPointDetl.setRefer_sco(new BigDecimal(errors_and_score.get(1).get("refer_sco")==null?"0":(String)errors_and_score.get(1).get("refer_sco")));//病案总得分
                        somSetlInvyQltDeduPointDetl.setNwbAdmWtDeduPoint(new BigDecimal(errors_and_score.get(1).get("nwb_adm_wt_dedu_point")==null?"0":(String)errors_and_score.get(1).get("nwb_adm_wt_dedu_point")));//新生儿入院体重扣分
                        somSetlInvyQltDeduPointDetl.setNwbBirWtDeduPoint(new BigDecimal(errors_and_score.get(1).get("lose_score_xsecstz")==null?"0":(String)errors_and_score.get(1).get("lose_score_xsecstz")));//新生儿出生体重扣分
                        somSetlInvyQltDeduPointDetl.setMedcasNoDeduPoint(new BigDecimal(errors_and_score.get(1).get("medcas_no_dedu_point")==null?"0":(String)errors_and_score.get(1).get("medcas_no_dedu_point")));//病案号扣分
                        somSetlInvyQltDeduPointDetl.setGendDeduPoint(new BigDecimal(errors_and_score.get(1).get("gend_dedu_point")==null?"0":(String)errors_and_score.get(1).get("gend_dedu_point")));//性别扣分
                        somSetlInvyQltDeduPointDetl.setBrdyDeduPoint(new BigDecimal(errors_and_score.get(1).get("brdy_dedu_point")==null?"0":(String)errors_and_score.get(1).get("brdy_dedu_point")));//出生日期扣分
                        somSetlInvyQltDeduPointDetl.setAgeDeduPoint(new BigDecimal(errors_and_score.get(1).get("age_dedu_point")==null?"0":(String)errors_and_score.get(1).get("age_dedu_point")));//年龄扣分
                        somSetlInvyQltDeduPointDetl.setMedPayWayDeduPoint(new BigDecimal(errors_and_score.get(1).get("med_pay_way_dedu_point")==null?"0":(String)errors_and_score.get(1).get("med_pay_way_dedu_point")));//医疗付费方式扣分
                        somSetlInvyQltDeduPointDetl.setOthPatnBasInfoDeduPoint(new BigDecimal(errors_and_score.get(1).get("oth_patn_bas_info_dedu_point")==null?"0":(String)errors_and_score.get(1).get("oth_patn_bas_info_dedu_point")));//其他患者基本信息扣分
                        somSetlInvyQltDeduPointDetl.setDscgWayDeduPoint(new BigDecimal(errors_and_score.get(1).get("dscg_way_dedu_point")==null?"0":(String)errors_and_score.get(1).get("dscg_way_dedu_point")));//离院方式扣分
                        somSetlInvyQltDeduPointDetl.setAdmTimeDeduPoint(new BigDecimal(errors_and_score.get(1).get("adm_time_dedu_point")==null?"0":(String)errors_and_score.get(1).get("adm_time_dedu_point")));//入院时间扣分
                        somSetlInvyQltDeduPointDetl.setDscgTimeDeduPoint(new BigDecimal(errors_and_score.get(1).get("dscg_time_dedu_point")==null?"0":(String)errors_and_score.get(1).get("dscg_time_dedu_point")));//出院时间扣分
                        somSetlInvyQltDeduPointDetl.setActIptDaysDeduPoint(new BigDecimal(errors_and_score.get(1).get("act_ipt_days_dedu_point")==null?"0":(String)errors_and_score.get(1).get("act_ipt_days_dedu_point")));//实际住院天数扣分
                        somSetlInvyQltDeduPointDetl.setDscgCatyDeduPoint(new BigDecimal(errors_and_score.get(1).get("dscg_caty_dedu_point")==null?"0":(String)errors_and_score.get(1).get("dscg_caty_dedu_point")));//出院科别扣分
                        somSetlInvyQltDeduPointDetl.setIs31DayInIptPlanDeduPoint(new BigDecimal(errors_and_score.get(1).get("is_31_day_in_ipt_plan_dedu_point")==null?"0":(String)errors_and_score.get(1).get("is_31_day_in_ipt_plan_dedu_point")));//是否有31天内再住院计划扣分
                        somSetlInvyQltDeduPointDetl.setAdmWayDeduPoint(new BigDecimal(errors_and_score.get(1).get("adm_way_dedu_point")==null?"0":(String)errors_and_score.get(1).get("adm_way_dedu_point")));//入院途径扣分
                        somSetlInvyQltDeduPointDetl.setAdmCatyDeduPoint(new BigDecimal(errors_and_score.get(1).get("adm_caty_dedu_point")==null?"0":(String)errors_and_score.get(1).get("adm_caty_dedu_point")));//入院科别扣分
                        somSetlInvyQltDeduPointDetl.setRefldeptCatyDeduPoint(new BigDecimal(errors_and_score.get(1).get("refldept_caty_dedu_point")==null?"0":(String)errors_and_score.get(1).get("refldept_caty_dedu_point")));//转科科别扣分
                        somSetlInvyQltDeduPointDetl.setDscgMainDiagDeduPoint(new BigDecimal(errors_and_score.get(1).get("dscg_main_diag_dedu_point")==null?"0":(String)errors_and_score.get(1).get("dscg_main_diag_dedu_point")));//出院主要诊断扣分
                        somSetlInvyQltDeduPointDetl.setMainDiagCodgDeduPoint(new BigDecimal(errors_and_score.get(1).get("main_diag_codg_dedu_point")==null?"0":(String)errors_and_score.get(1).get("main_diag_codg_dedu_point")));//主要诊断编码扣分
                        somSetlInvyQltDeduPointDetl.setOthDiagDeduPoint(new BigDecimal(errors_and_score.get(1).get("oth_diag_dedu_point")==null?"0":(String)errors_and_score.get(1).get("oth_diag_dedu_point")));//其他诊断扣分
                        somSetlInvyQltDeduPointDetl.setOthDiagCodgDeduPoint(new BigDecimal(errors_and_score.get(1).get("oth_diag_codg_dedu_point")==null?"0":(String)errors_and_score.get(1).get("oth_diag_codg_dedu_point")));//其他诊断编码扣分
                        somSetlInvyQltDeduPointDetl.setMainOprnOprtNameDeduPoint(new BigDecimal(errors_and_score.get(1).get("main_oprn_oprt_name_dedu_point")==null?"0":(String)errors_and_score.get(1).get("main_oprn_oprt_name_dedu_point")));//主要手术或操作名称扣分
                        somSetlInvyQltDeduPointDetl.setMainOprnOprtCodgDeduPoint(new BigDecimal(errors_and_score.get(1).get("main_oprn_oprt_codg_dedu_point")==null?"0":(String)errors_and_score.get(1).get("main_oprn_oprt_codg_dedu_point")));//主要手术或操作编码扣分
                        somSetlInvyQltDeduPointDetl.setAdmCondDeduPoint(new BigDecimal(errors_and_score.get(1).get("adm_cond_dedu_point")==null?"0":(String)errors_and_score.get(1).get("adm_cond_dedu_point")));//入院病情扣分
                        somSetlInvyQltDeduPointDetl.setPalgDiagDeduPoint(new BigDecimal(errors_and_score.get(1).get("palg_diag_dedu_point")==null?"0":(String)errors_and_score.get(1).get("palg_diag_dedu_point")));//病理诊断扣分
                        somSetlInvyQltDeduPointDetl.setPalgDiagCodgDeduPoint(new BigDecimal(errors_and_score.get(1).get("palg_diag_codg_dedu_point")==null?"0":(String)errors_and_score.get(1).get("palg_diag_codg_dedu_point")));//病理诊断编码扣分
                        somSetlInvyQltDeduPointDetl.setIncsHealLvDeduPoint(new BigDecimal(errors_and_score.get(1).get("incs_heal_lv_dedu_point")==null?"0":(String)errors_and_score.get(1).get("incs_heal_lv_dedu_point")));//切口愈合等级扣分
                        somSetlInvyQltDeduPointDetl.setBrnDamgPatnComaTimeDeduPoint(new BigDecimal(errors_and_score.get(1).get("brn_damg_patn_coma_time_dedu_point")==null?"0":(String)errors_and_score.get(1).get("brn_damg_patn_coma_time_dedu_point")));//颅脑损伤患者昏迷时间扣分
                        somSetlInvyQltDeduPointDetl.setOthOprnOprtNameDeduPoint(new BigDecimal(errors_and_score.get(1).get("oth_oprn_oprt_name_dedu_point")==null?"0":(String)errors_and_score.get(1).get("oth_oprn_oprt_name_dedu_point")));//其他手术或操作名称扣分
                        somSetlInvyQltDeduPointDetl.setOthOprnOprtCodgDeduPoint(new BigDecimal(errors_and_score.get(1).get("oth_oprn_oprt_codg_dedu_point")==null?"0":(String)errors_and_score.get(1).get("oth_oprn_oprt_codg_dedu_point")));//其他手术或操作编码扣分
                        somSetlInvyQltDeduPointDetl.setOprnOprtDateDeduPoint(new BigDecimal(errors_and_score.get(1).get("oprn_oprt_date_dedu_point")==null?"0":(String)errors_and_score.get(1).get("oprn_oprt_date_dedu_point")));//手术及操作日期扣分
                        somSetlInvyQltDeduPointDetl.setOtpDiagDeduPoint(new BigDecimal(errors_and_score.get(1).get("otp_diag_dedu_point")==null?"0":(String)errors_and_score.get(1).get("otp_diag_dedu_point")));//门（急）诊诊断扣分
                        somSetlInvyQltDeduPointDetl.setOtpDiagDiseCodgDeduPoint(new BigDecimal(errors_and_score.get(1).get("otp_diag_dise_codg_dedu_point")==null?"0":(String)errors_and_score.get(1).get("otp_diag_dise_codg_dedu_point")));//门（急）诊诊断疾病编码扣分
                        somSetlInvyQltDeduPointDetl.setAnstWayDeduPoint(new BigDecimal(errors_and_score.get(1).get("anst_way_dedu_point")==null?"0":(String)errors_and_score.get(1).get("anst_way_dedu_point")));//麻醉方式扣分
                        somSetlInvyQltDeduPointDetl.setOthTrtInfoDeduPoint(new BigDecimal(errors_and_score.get(1).get("oth_trt_info_dedu_point")==null?"0":(String)errors_and_score.get(1).get("oth_trt_info_dedu_point")));//其他诊疗信息扣分
                        somSetlInvyQltDeduPointDetl.setSumfeeDeduPoint(new BigDecimal(errors_and_score.get(1).get("sumfee_dedu_point")==null?"0":(String)errors_and_score.get(1).get("sumfee_dedu_point")));//总费用扣分
                        somSetlInvyQltDeduPointDetl.setOthFeeInfoDeduPoint(new BigDecimal(errors_and_score.get(1).get("oth_ast_info_dedu_point")==null?"0":(String)errors_and_score.get(1).get("oth_ast_info_dedu_point")));//其他费用信息扣分
                        somSetlInvyQltDeduPointDetl.setDeduPointRea((String)errors_and_score.get(1).get("dedu_point_rea"));//扣分原因
                        somSetlInvyQltDeduPointDetl.setDataLogId(logId);//校验日志号
                        somSetlInvyQltDeduPointDetl.setOprDate(DateUtil.getCurDate());//经办时间
                        svlss.add(somSetlInvyQltDeduPointDetl);
                    }
                }
                //处理n条数据，更新进度
//                proNum = proNum + 1;
//                if(proNum%9==0){
//                    SomDataprosLog sdhl = dataRecordService.queryRecordById(queryParam);
//                    sdhl.setPrcs_prgs((ValidateUtil.isEmpty(sdhl.getPrcs_prgs())?0:sdhl.getPrcs_prgs())+1);
//                    dataRecordService.updateDataHandleLogById(sdhl);
//                }
            }
            if(sves.size()>0){
                dataRecordService.savePreValidateErrorBatch(sves);
            }
            if(svlss.size()>0){
                dataRecordService.savePreValidateLoseScoreBatch(svlss);
            }
            if(scrc.size()>0){
                dataRecordService.savePreValidateCodeResourceConsumptionBatch(scrc);
                dataRecordService.updateStsCodeResourceConsumptionNewDrgName(queryParam);
            }
            queryParam.put("active_flag",DrgConst.ACTIVE_FLAG_1);
            queryParam.put("start",start);
            queryParam.put("limit",limit);
            datas = dataRecordService.getSettleListForPreValidate(queryParam);//结算清单数据
            queryParam.put("diagnostic_type",DrgConst.DIAGNOSTIC_TYPE_1);//西医诊断
            diseaseMap = dataRecordService.getDiseaseMap(queryParam); //疾病信息
            operationMap = dataRecordService.getOperationMap(queryParam); //手术信息
            if(datas.size()>0){
                if(isCleanData){
                    logger.info("==========已校验和清洗"+(start+datas.size())+"条病案");
                }else{
                    logger.info("==========已校验"+(start+datas.size())+"条病案");
                }
            }
            start += limit;
        }while(datas.size()>0);
        somDataprosLog.setMedcasVal(totals);
        somDataprosLog.setIntegrityChkPassVal(integrity_chk_pass_val);
        somDataprosLog.setLogicChkPassVal(logic_chk_pass_val);
        somDataprosLog.setDataCleanPassVal(data_clean_pass_val);
        return somDataprosLog;
    }

}
