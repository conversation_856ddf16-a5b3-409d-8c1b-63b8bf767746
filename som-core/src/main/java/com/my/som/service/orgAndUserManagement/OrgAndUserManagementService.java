package com.my.som.service.orgAndUserManagement;

import com.my.som.common.exception.AppException;
import com.my.som.common.vo.SomBackUser;
import com.my.som.model.orgAndUserManagement.SomBasDept;
import com.my.som.model.orgAndUserManagement.BaseOrgNode;
import com.my.som.common.vo.SysUserBase;

import java.util.List;

/**
 * @author: zyd
 * Case:          drg
 * FileName:      OrgAndUserManagementService
 * Date:          2020/1/9 10:26
 * Description:   描述...
 * Ver:       1.0
 * Copyright (C),2012-2022 Freedom
 */
public interface OrgAndUserManagementService {
    /**
     * 获取组织机构树形结构数据
     * @return
     */
    List<BaseOrgNode> getListTree(String s);

    /**
     * 新增组织
     * @param somBasDept
     */
    BaseOrgNode addOrg(SomBasDept somBasDept, SomBackUser somBackUser) throws AppException;

    /**
     * 删除组织
     * @param somBasDept
     * @param somBackUser
     * @return
     */
    BaseOrgNode removeOrg(SomBasDept somBasDept, SomBackUser somBackUser);

    /**
     * 编辑组织
     * @param somBasDept
     * @param somBackUser
     * @return
     */
    BaseOrgNode updateOrg(SomBasDept somBasDept, SomBackUser somBackUser);

    /**
     * 查询科室
     * @param somBasDept
     * @return
     */
    void extractDept(SomBasDept somBasDept, SysUserBase sysUserBase);
}
