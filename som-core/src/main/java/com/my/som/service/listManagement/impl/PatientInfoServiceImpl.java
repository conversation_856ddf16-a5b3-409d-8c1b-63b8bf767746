package com.my.som.service.listManagement.impl;

import com.github.pagehelper.PageHelper;
import com.my.som.common.constant.DrgConst;
import com.my.som.common.util.ValidateUtil;
import com.my.som.dao.medicalQuality.SettleListManageDao;
import com.my.som.dao.medicalQuality.SettleListMarkMapper;
import com.my.som.dto.listManagement.PatientInfoDto;
import com.my.som.dto.medicalQuality.ModifyBusSettleListDto;
import com.my.som.dto.medicalQuality.SettleListMarkDto;
import com.my.som.mapper.listManagement.PatientInfoMapper;
import com.my.som.service.listManagement.PatientInfoService;
import com.my.som.vo.listManagement.PatientInfoVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * @author: zyd
 * @date: 2023-07-31
 **/
@Service
public class PatientInfoServiceImpl implements PatientInfoService {

    @Autowired
    private PatientInfoMapper patientInfoMapper;

    @Autowired
    private SettleListMarkMapper settleListMarkMapper;

    @Autowired
    private SettleListManageDao settleListManageDao;

    @Override
    public List<PatientInfoVo> queryPatientInfo(PatientInfoDto dto) {
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        if(!ValidateUtil.isEmpty(dto.getErrorTypeDesc())) {
            List<String> info = new ArrayList<>();
            for (String s : dto.getErrorTypeDesc()) {
                if(s.contains("空")) {
                    info.add("1");
                }
                if(s.contains("住院时间")) {
                    info.add("2");
                }
                if(s.contains("自费")) {
                    info.add("3");
                }
            }
            dto.setErrType(info);
        }
        List<String> list1 = new ArrayList<>();
        for(String key : DrgConst.RE_TRANSFER_ERROR.keySet()) {
            list1.add(key);
        }
        List<PatientInfoVo> list = patientInfoMapper.queryPatientInfo(dto, list1);
        return list;
    }

    @Override
    public void deleteSettlementMark(PatientInfoDto dto) {
        settleListMarkMapper.delete(new SettleListMarkDto(dto.getK00()));
        ModifyBusSettleListDto modifyBusSettleListDto = new ModifyBusSettleListDto();
        modifyBusSettleListDto.setLookOver(DrgConst.ACTIVE_FLAG_0);
        modifyBusSettleListDto.setK00(dto.getK00());
        settleListManageDao.updateSettleListLookOver(modifyBusSettleListDto);
    }
}
