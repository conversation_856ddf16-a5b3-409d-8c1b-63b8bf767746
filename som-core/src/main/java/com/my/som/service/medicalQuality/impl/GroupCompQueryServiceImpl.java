package com.my.som.service.medicalQuality.impl;

import com.alibaba.fastjson.JSON;
import com.my.som.common.constant.DrgConst;
import com.my.som.common.exception.AppException;
import com.my.som.common.util.ValidateUtil;
import com.my.som.dao.medicalQuality.GroupCompQueryMapper;
import com.my.som.dto.dipBusiness.DipBusinessQueryDto;
import com.my.som.model.common.SomDipStandard;
import com.my.som.service.dataHandle.impl.DIPGroupJobServiceImpl;
import com.my.som.service.medicalQuality.GroupCompQueryService;
import com.my.som.util.HttpGroupUtil;
import com.my.som.util.JSONUtil;
import com.my.som.vo.dataHandle.dataGroup.DipGroupDataInfoVo;
import com.my.som.vo.dataHandle.dataGroup.HospitalVo;
import com.my.som.vo.group.DipGroupResponseVo;
import com.my.som.vo.medicalQuality.GroupCompQueryVo;
import com.my.som.vo.pregroup.CoreGroupVo;
import com.my.som.vo.pregroup.PreGroupVo;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: zyd
 * @version 1.0
 * @date: 2023-07-31
 * @description:
 */
@Service
public class GroupCompQueryServiceImpl implements GroupCompQueryService {

    @Autowired
    private DIPGroupJobServiceImpl dipGroupJobService;

    @Autowired
    private GroupCompQueryMapper groupCompQueryMapper;

    @Override
    public Map<String, Object> getGroupData(DipGroupDataInfoVo dto) {
        Map<String, Object> map = new HashMap<>();
        // 根据页面传的参数获取组
        List<String> operationList = new ArrayList<>();
        HospitalVo hospitalVo = dipGroupJobService.getHospitalDipByHospitalId();

        List<GroupCompQueryVo> groupCompQueryVos = new ArrayList<>();
        Set<String> inGroupCodes = new HashSet<>();
        try {
            for (int i = 0; i < 7; i++) {
                String methodName = "getC35c_" + i;
                Method oprnMethod = dto.getClass().getMethod(methodName);
                Object o = oprnMethod.invoke(dto);
                if(o != null){
                    operationList.add(o.toString());
                }
            }
            if (operationList.size() > 0) {
                List<List<String>> combinations = generateCombinations(operationList);
                for (List<String> combination : combinations) {
                    if (combination.size() == 0) {
                        continue;
                    }
                    DipGroupDataInfoVo reqParam = new DipGroupDataInfoVo();
                    BeanUtils.copyProperties(dto, reqParam);
                    // 设置手术
                    for (int i = 0; i < 7; i++) {
                        String methodName = "setC35c_" + i;
                        Method oprnMethod = reqParam.getClass().getMethod(methodName, String.class);
                        if (i < combination.size()) {
                            oprnMethod.invoke(reqParam, combination.get(i));
                        } else {
                            oprnMethod.invoke(reqParam, "");
                        }
                    }
                    GroupCompQueryVo compQueryVo = new GroupCompQueryVo();
                    // 设置移除的手术
                    ArrayList<String> newOriList = new ArrayList<>(operationList);
                    newOriList.removeAll(combination);
                    compQueryVo.setDelCodes(newOriList);
                    req(reqParam, hospitalVo, compQueryVo, groupCompQueryVos, inGroupCodes);
                }
            } else {
                GroupCompQueryVo compQueryVo = new GroupCompQueryVo();
                req(dto, hospitalVo, compQueryVo, groupCompQueryVos, inGroupCodes);
            }
            String year = groupCompQueryMapper.selectNew(dto.getHospitalId());
            List<SomDipStandard> groupVos = groupCompQueryMapper.selectGroupData(year, dto.getHospitalId());
            if (!groupVos.isEmpty()){
                Map<String, List<SomDipStandard>> collect = groupVos.stream().collect(Collectors.groupingBy(SomDipStandard::getDipCodg));
                for (GroupCompQueryVo queryVo : groupCompQueryVos) {
                    List<SomDipStandard> dipBenchmarkList = collect.get(queryVo.getDip().getDisease_code());
                    if (!Objects.isNull(dipBenchmarkList)){
                        for (SomDipStandard dipBenchmark : dipBenchmarkList) {
                            if (dipBenchmark.getAsstListAgeGrp().equals(queryVo.getDip().getAsst_list_age_grp()) && dipBenchmark.getAsstListTmorSevDeg().equals(queryVo.getDip().getAsst_list_tmor_sev_deg()) && dipBenchmark.getAsstListDiseSevDeg().equals(queryVo.getDip().getAsst_list_dise_sev_deg())){
                                queryVo.setLine(dipBenchmark);
                                break;
                            }
                        }
                    }
                }
            }
        } catch (NoSuchMethodException | IllegalAccessException | InvocationTargetException e) {
            throw new RuntimeException(e);
        }
        map.put("groupData", groupCompQueryVos);
        return map;
    }

    @Override
    public List<Map<String, Object>> queryRecords(DipBusinessQueryDto dto) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy");
        dto.setYear(sdf.format(new Date()));
        List<Map<String, Object>> objects = groupCompQueryMapper.queryRecords(dto);
        for (Map<String, Object> record : objects) {
            String codes = String.valueOf(record.get("oper"));
            if (ValidateUtil.isNotEmpty(codes) && !"null".equals(codes)){
                record.put("oper",codes.split(","));
            }
        }
        return objects;
    }


    /**
     * DIP分组请求
     *
     * @param reqParam          请求参数
     * @param hospitalVo        分组器地址
     * @param compQueryVo       分组返回vo
     * @param groupCompQueryVos 分组返回vo集合
     * @param inGroupCodes
     */
    private void req(DipGroupDataInfoVo reqParam,
                     HospitalVo hospitalVo,
                     GroupCompQueryVo compQueryVo,
                     List<GroupCompQueryVo> groupCompQueryVos,
                     Set<String> inGroupCodes){
        // 请求分组器
        JSONUtil<DipGroupDataInfoVo> jsonUtil = new JSONUtil<>();
        String json = jsonUtil.getGroupJsonSingle(reqParam, DrgConst.GROUP_TYPE_DIP);
        String response = HttpGroupUtil.post(json, hospitalVo.getGroupUrl());
        if (ValidateUtil.isEmpty(response) || response.equals(DrgConst.RESULT_ERROR)) {
            throw new AppException("网络异常");
        } else {
            DipGroupResponseVo responseVo = JSON.parseObject(response, DipGroupResponseVo.class);
            if (!DrgConst.GROUP_RESPONSE_CODE_LOST_1.equals(responseVo.get_State__code())) {
                DipGroupDataInfoVo data = responseVo.get_State__data();
                if (!inGroupCodes.contains(data.getDisease_code())) {
                    compQueryVo.setDip(data);
                    groupCompQueryVos.add(compQueryVo);
                    inGroupCodes.add(data.getDisease_code());
                }
            }
        }
    }

    public List<List<String>> generateCombinations(List<String> elements) {
        List<List<String>> combinations = new ArrayList<>();
        generateCombinationsHelper(elements, new ArrayList<>(), 0, combinations);
        return combinations;
    }

    private void generateCombinationsHelper(List<String> elements, List<String> currentCombination, int startIndex, List<List<String>> combinations) {
        combinations.add(new ArrayList<>(currentCombination));

        for (int i = startIndex; i < elements.size(); i++) {
            currentCombination.add(elements.get(i));
            generateCombinationsHelper(elements, currentCombination, i + 1, combinations);
            currentCombination.remove(currentCombination.size() - 1);
        }
    }
}
