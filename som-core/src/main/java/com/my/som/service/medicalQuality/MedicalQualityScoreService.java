package com.my.som.service.medicalQuality;

import com.my.som.dto.medicalQuality.*;
import com.my.som.model.dataHandle.SomSetlInvyQltDeduPointDetl;

import java.util.List;

/**
 * 病案质量得分Service
 * Created by sky on 2020/3/1.
 */
public interface MedicalQualityScoreService {

    /**
     * 查询所有病案质量得分情况
     * @param queryParam
     * @param pageSize
     * @param pageNum
     * @return
     */
    List<MedicalAndScoreInfo> list(SettleListMainInfoQueryParam queryParam, Integer pageSize, Integer pageNum);


    /**
     * 查询病例完整性校对统计信息
     * @param queryParam
     * @return
     */
    MedicalQualityScoreCountInfo getScoreCountInfo(SettleListMainInfoQueryParam queryParam);

    /**
     * 查询病案得分统计信息
     * @param queryParam
     * @return
     */
    List<ScoreLowTopInfo> getScoreLowTopInfo(SettleListMainInfoQueryParam queryParam);

    /**
     * 根据结算清单ID查询病案质量得分详情
     * @param id
     * @return
     */
    SomSetlInvyQltDeduPointDetl getMedicalQualityScoreDetailById(Long id);
}
