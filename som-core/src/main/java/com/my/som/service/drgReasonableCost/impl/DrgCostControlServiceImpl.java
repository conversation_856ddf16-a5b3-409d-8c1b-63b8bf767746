package com.my.som.service.drgReasonableCost.impl;


import com.github.pagehelper.PageHelper;
import com.my.som.dao.drgReasonableCost.DrgCostControlDao;
import com.my.som.dto.drgReasonableCost.DrgReasonableCostQueryParam;
import com.my.som.service.drgReasonableCost.DrgCostControlService;
import com.my.som.vo.drgReasonableCost.DrgCostControlVo;
import com.my.som.vo.drgReasonableCost.DrgPayAdverseVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * Service实现类
 * Created by sky on 2020/1/5.
 */
@Service
public class DrgCostControlServiceImpl implements DrgCostControlService {
    @Autowired
    private DrgCostControlDao drgCostControlDao;

    @Override
    public List<DrgCostControlVo> list(DrgReasonableCostQueryParam queryParam, Integer pageSize, Integer pageNum) {
        PageHelper.startPage(pageNum, pageSize);
        return drgCostControlDao.list(queryParam);
    }

}
