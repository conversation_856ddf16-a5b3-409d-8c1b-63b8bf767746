package com.my.som.service.hospitalAnalysis;

import com.my.som.dto.hospitalAnalysis.HospitalAnalysisQueryParam;
import com.my.som.vo.hospitalAnalysis.DrgsAnalysisCountInfo;
import com.my.som.vo.hospitalAnalysis.DrgsAnalysisInfo;

import java.util.List;

/**
 * Service
 * Created by sky on 2020/3/17.
 */
public interface DrgsAnalysisService {
    /**
     * 查询医院病组分析主要信息
     * @param queryParam
     * @return
     */
    List<DrgsAnalysisInfo> list(HospitalAnalysisQueryParam queryParam,Integer pageSize,Integer pageNum);

    /**
     * 查询医院病组分析统计信息
     * @param queryParam
     * @return
     */
    List<DrgsAnalysisCountInfo> getCountInfo(HospitalAnalysisQueryParam queryParam);

}
