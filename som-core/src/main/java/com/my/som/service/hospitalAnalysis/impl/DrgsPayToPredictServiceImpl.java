package com.my.som.service.hospitalAnalysis.impl;

import com.github.pagehelper.PageHelper;
import com.my.som.common.api.CommonMapResult;
import com.my.som.common.constant.DrgConst;
import com.my.som.common.exception.AppException;
import com.my.som.common.util.ValidateUtil;
import com.my.som.dao.hospitalAnalysis.DrgsPayToPredictDao;
import com.my.som.dto.hospitalAnalysis.DrgsBusinessQueryDto;
import com.my.som.service.hospitalAnalysis.DrgsPayToPredictService;

import com.my.som.util.GroupCommonUtil;
import com.my.som.util.SysCommonConfigUtil;
import com.my.som.vo.dataHandle.dataGroup.HospitalDrgVo;
import com.my.som.vo.dataHandle.dataGroup.HospitalVo;
import com.my.som.vo.hospitalAnalysis.DrgsPayToPredictVo;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.YearMonth;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Optional;

/**
 * @author: zyd
 * @Date 2021/8/20 2:25 下午
 * @Version 1.0
 * @Description: DIP 支付预测
 */
@Service
public class DrgsPayToPredictServiceImpl implements DrgsPayToPredictService {


    @Resource
    private DrgsPayToPredictDao drgsPayToPredictDao;

    private final String ADM_TIME = "ADM_TIME";
    private final String DSCG_TIME = "DSCG_TIME";
    private final String SETL_END_TIME = "SETL_END_TIME";
    @Override
    public List<DrgsPayToPredictVo> getList(DrgsBusinessQueryDto queryParam, Integer pageSize, Integer pageNum) {
        List<DrgsPayToPredictVo> list= null;
        try {
            updateInsuPlaceType(queryParam);
            //标杆费用乘以系数
            Object drgGrouperVerObj = SysCommonConfigUtil.get(DrgConst.FEE_MULTI_ADJMCON);
            if (drgGrouperVerObj != null && "true".equals(drgGrouperVerObj.toString())) {
                queryParam.setFeeMulAdjmConf("true");
            }
            PageHelper.startPage(pageNum, pageSize);
            list = drgsPayToPredictDao.getList(queryParam);
        }catch (Exception e){
            e.printStackTrace();
        }

        return list;
    }

    @Override
    public CommonMapResult getListPoint(DrgsBusinessQueryDto dto) {
        CommonMapResult result = new CommonMapResult();
        //查询城职城乡
        if(ValidateUtil.isNotEmpty(dto.getCy_start_date())) {
            dto.setCy_start_date(dto.getCy_start_date());
            dto.setCy_end_date(dto.getCy_end_date().concat(" 23:59:59"));
        }
        updateInsuPlaceType(dto);
        List<DrgsPayToPredictVo> listPoint = drgsPayToPredictDao.getListPoint(dto);
        DrgsPayToPredictVo cityPointVo = new DrgsPayToPredictVo();
        DrgsPayToPredictVo countrysidePointVo = new DrgsPayToPredictVo();
        DrgsPayToPredictVo QTPointVo = new DrgsPayToPredictVo();
        DrgsPayToPredictVo balanceVo = new DrgsPayToPredictVo();
        for(DrgsPayToPredictVo point : listPoint){
            if(DrgConst.INSURANCES_TYPE_1.contains(point.getInsuType())){
                //城职
                cityPointVo = point;
                cityPointVo.setPoint(point.getLowPoint().add(point.getUpPoint()).add(point.getNCPoint()).add(point.getWBPoint()).add(point.getWBLPoint()));
                //预测金额
                cityPointVo.setPredictCost(cityPointVo.getPoint().multiply(cityPointVo.getCzPrice()));
                cityPointVo.setSumfee(cityPointVo.getLowCost().add(cityPointVo.getUpCost()).add(cityPointVo.getNCCost()).add(cityPointVo.getWBCost()).add(cityPointVo.getWBLCost()));

            } else if(DrgConst.INSURANCES_TYPE_2.contains(point.getInsuType())){
                //城乡
                countrysidePointVo = point;
                countrysidePointVo.setPoint(point.getLowPoint().add(point.getUpPoint()).add(point.getNCPoint()).add(point.getWBPoint()).add(point.getWBLPoint()).setScale(4, BigDecimal.ROUND_HALF_UP));
                //预测金额
                countrysidePointVo.setPredictCost(countrysidePointVo.getPoint().multiply(countrysidePointVo.getCxPrice()).setScale(2, BigDecimal.ROUND_HALF_UP));
                countrysidePointVo.setSumfee(countrysidePointVo.getLowCost().add(countrysidePointVo.getUpCost()).add(countrysidePointVo.getNCCost()).add(countrysidePointVo.getWBCost()).add(countrysidePointVo.getWBLCost()).setScale(2, BigDecimal.ROUND_HALF_UP));
            } else{
                QTPointVo.setInsuType("99");
                QTPointVo.setCxPrice(point.getCxPrice());
                QTPointVo.setCzPrice(point.getCzPrice());
                QTPointVo.setPrice(point.getPrice());
                QTPointVo.setLowPoint(Optional.ofNullable(QTPointVo.getLowPoint()).orElse(BigDecimal.ZERO).add(point.getLowPoint()));
                QTPointVo.setUpPoint(Optional.ofNullable(QTPointVo.getUpPoint()).orElse(BigDecimal.ZERO).add(point.getUpPoint()));
                QTPointVo.setNCPoint(Optional.ofNullable(QTPointVo.getNCPoint()).orElse(BigDecimal.ZERO).add(point.getNCPoint()));
                QTPointVo.setWBPoint(Optional.ofNullable(QTPointVo.getWBPoint()).orElse(BigDecimal.ZERO).add(point.getWBPoint()));
                QTPointVo.setWBLPoint(Optional.ofNullable(QTPointVo.getWBLPoint()).orElse(BigDecimal.ZERO).add(point.getWBLPoint()));
                QTPointVo.setLowCost(Optional.ofNullable(QTPointVo.getLowCost()).orElse(BigDecimal.ZERO).add(point.getLowCost()));
                QTPointVo.setUpCost(Optional.ofNullable(QTPointVo.getUpCost()).orElse(BigDecimal.ZERO).add(point.getUpCost()));
                QTPointVo.setNCCost(Optional.ofNullable(QTPointVo.getNCCost()).orElse(BigDecimal.ZERO).add(point.getNCCost()));
                QTPointVo.setWBCost(Optional.ofNullable(QTPointVo.getWBCost()).orElse(BigDecimal.ZERO).add(point.getWBCost()));
                QTPointVo.setWBLCost(Optional.ofNullable(QTPointVo.getWBLCost()).orElse(BigDecimal.ZERO).add(point.getWBLCost()));
                QTPointVo.setPoint(QTPointVo.getLowPoint().add(QTPointVo.getUpPoint()).add(QTPointVo.getNCPoint()).add(QTPointVo.getWBPoint()).add(QTPointVo.getWBLPoint()));
                QTPointVo.setPredictCost(QTPointVo.getPoint().multiply(QTPointVo.getPrice()));
                QTPointVo.setSumfee(QTPointVo.getLowCost().add(QTPointVo.getUpCost()).add(QTPointVo.getNCCost()).add(QTPointVo.getWBCost()).add(QTPointVo.getWBLCost()));

            }
        }
//        QTPointVo.setPoint(QTPointVo.getLowPoint().add(QTPointVo.getUpPoint()).add(QTPointVo.getNCPoint()));
//        QTPointVo.setPredictCost(QTPointVo.getPoint().multiply(QTPointVo.getPrice()));
//        QTPointVo.setSumfee(QTPointVo.getLowCost().add(QTPointVo.getUpCost()).add(QTPointVo.getNCCost()));
        if(ValidateUtil.isNotEmpty(listPoint)){
            if(cityPointVo.getPoint() !=null && countrysidePointVo.getPoint() != null ){
                balanceVo.setPoint(cityPointVo.getPoint()
                        .add(countrysidePointVo.getPoint()).add(QTPointVo.getPoint()).setScale(2,BigDecimal.ROUND_HALF_UP));

                balanceVo.setPredictCost(cityPointVo.getPredictCost()
                        .add(countrysidePointVo.getPredictCost()).add(QTPointVo.getPredictCost()).setScale(2,BigDecimal.ROUND_HALF_UP));

                balanceVo.setSumfee(cityPointVo.getSumfee().add(countrysidePointVo.getSumfee()).add(QTPointVo.getSumfee()));
            }
        }

        result.put("cityPoint",cityPointVo);
        result.put("countrysidePoint",countrysidePointVo);
        result.put("QTPointPoint",QTPointVo);
        result.put("balancePoint",balanceVo);
        return result;
    }

    @Override
    public CommonMapResult getMonthData(DrgsBusinessQueryDto dto) {
        CommonMapResult result = new CommonMapResult();
        if(ValidateUtil.isNotEmpty(dto.getCy_start_date())) {
            dto.setExpiDate(dto.getCy_end_date().substring(0,7));
            setBegnDate(dto);
            dto.setBusKeyField(DSCG_TIME);
        } else if (ValidateUtil.isNotEmpty(dto.getInStartTime())) {
            dto.setExpiDate(dto.getInEndTime().substring(0,7));
            setBegnDate(dto);
            dto.setBusKeyField(ADM_TIME);
        } else if (ValidateUtil.isNotEmpty(dto.getSeStartTime())) {
            dto.setExpiDate(dto.getSeEndTime().substring(0,7));
            setBegnDate(dto);
            dto.setBusKeyField(SETL_END_TIME);
        }
        updateInsuPlaceType(dto);
        List<DrgsPayToPredictVo> monthDataList = drgsPayToPredictDao.getMonthData(dto);
        HashMap<String, Object> controlMap = new HashMap<>();
        List<String> monthList = new ArrayList<>();
        List<BigDecimal> totalList = new ArrayList<>();
        List<BigDecimal> drgList = new ArrayList<>();
        List<String> cybList = new ArrayList<>();
        for (DrgsPayToPredictVo pointVo : monthDataList){
            monthList.add(pointVo.getMonth());
            totalList.add(pointVo.getSumfee());
            drgList.add(pointVo.getPredictCost());
            cybList.add(pointVo.getCyb());
        }
        controlMap.put("monthList",monthList);
        controlMap.put("totalList",totalList);
        controlMap.put("drgList",drgList);
        controlMap.put("cybList",cybList);
        result.put("controlMap",controlMap);

        return result;
    }

    /**
     * 修改将参保地类型转为具体的参保地数据
     * @param dto
     */
    private static void updateInsuPlaceType(DrgsBusinessQueryDto dto) {
        String insuPlaceType = (String) SysCommonConfigUtil.get(DrgConst.INSUPLC_ADMDVS_AREA);
        dto.setInsuplcAdmdvs(insuPlaceType);
        String rovLevelInsuplcAdmdvs =GroupCommonUtil.getInsuplcAdmdvs();
        if(ValidateUtil.isEmpty(rovLevelInsuplcAdmdvs)){
            rovLevelInsuplcAdmdvs = "0000";
        }
        dto.setProvLevelInsuplcAdmdvs(rovLevelInsuplcAdmdvs);
    }

    private static void setBegnDate(DrgsBusinessQueryDto dto) {
        YearMonth yearMonth = YearMonth.parse(dto.getExpiDate());
        String begnDate = yearMonth.minus(4, ChronoUnit.MONTHS).toString();
        dto.setBegnDate(begnDate);
    }
}
