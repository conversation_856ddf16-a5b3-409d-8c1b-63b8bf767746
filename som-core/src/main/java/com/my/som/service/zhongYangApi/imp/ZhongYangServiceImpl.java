package com.my.som.service.zhongYangApi.imp;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.my.som.common.exception.AppException;
import com.my.som.dao.medicalQuality.SettleListManageDao;
import com.my.som.dto.medicalQuality.ModifyBusSettleListDto;
import com.my.som.dto.pregroup.*;
import com.my.som.dto.zhongYangApi.Get2_2_3Dto;
import com.my.som.dto.zhongYangApi.ZhongYangApiDto;
import com.my.som.mapper.zhongYangApi.ZhongYangApiMapper;
import com.my.som.service.listManagement.impl.ListUploadServiceImpl;
import com.my.som.service.pregroup.PreGroupServiceInterface;
import com.my.som.service.zhongYangApi.ZhongYangApiService;
import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.math.BigDecimal;
import java.net.HttpURLConnection;
import java.net.URL;
import java.security.KeyFactory;
import java.security.PrivateKey;
import java.security.Signature;
import java.security.spec.PKCS8EncodedKeySpec;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

import com.my.som.vo.pregroup.PreGroupVo;
import com.yomahub.liteflow.util.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64; // 确保使用这个包
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.DefaultTransactionDefinition;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.text.ParseException;
import java.util.Date;

import static com.yomahub.liteflow.util.JsonUtil.toJsonString;


@Service
@Slf4j
public class ZhongYangServiceImpl implements ZhongYangApiService {
    //密钥
    public static final String APPSECRET = "MIICdwIBADANBgkqhkiG9w0BAQEFAASCAmEwggJdAgEAAoGBAMVWX91Nq6F7KSFcnoBMBbJclDAC7ufjRWc95/pbGyTBhTTnFGIISNtvQVVhFf0kTxJgqHgg7XXYcnBAlBBELbBfioSxZVh2Cz+OWKE82RFA0KnabBbDutTr2X2yX/tZnIWsKQVpXNhGMcTE20ZWCO27bNa5dJz/dCXiH3DOYP1vAgMBAAECgYA9+usRLxwBFBXPDLAby8TeSIWirskqvA9IZgE+eO8FlGKqQwn74G5BvaffkSa+43CSbRl84ZqzsNAAwce/dCqE9S4boF7yIKiI7KZ/Pm7MXVfYhG9GwrHZtCF3RvHPQDsSJbaAvrMmPj4IHkyY+zD4T1a5RlNv1mInp70FsSyEAQJBAPbPN7joK9nWNYv4FcPbo661Rs/vZy1s+jZlddrdKe81pK9o//COYoJuGi3lW5/xIP7ZBM0LQVs7Z0ydQ6n3Ai8CQQDMr4zdfm/GKS32Pvd2bWyfr3YwshMoJ+RiiOHUacSOMOxR6NVZ6ihXKZHC+mcujo2Nujek0PRxWuVDkO76/yjBAkArOrpg2dGscxen74koaBUwabIAg8dHrpnY40aUUj6hFn1E+Bg0M0xP2icduNmmlx6XZL2xiHInH62FPtp2L7yjAkEAqS2QKKKQkCEJNDbnIqNe3L2R2r3Ux6PxSOAKiB7DryMQsMLZVPANV72sFJQTJI/+v93ij/+Fiwut4yKv5BVyAQJBAJeoP+6kLF3nmjM5/FKN96UGRPfpsh/QInG3Kt8ZoutIzgNuoiQMrUc69L6pYVaNl8RiLsvjieB8DxpWPAyiRgs=";
    //机构id
    public static final String ORGID = "10259";
    //医院id
    public static final String HOSPITALID = "10259001";
    //应用id
    public static final String APPID = "app7045988164989882246";
    //加密方式
    public static final String SIGNTYPE = "RSA2";
    //API2.2.3路径
    public static final String API_2_2_3_url = "https://sysyyqzyyy.msunhis.com/msun-middle-aggregate-hsz/v1/patients";
    public static final String API_2_23_4_URL = "https://sysyyqzyyy.msunhis.com/msun-middle-business-mr/v1/mr-home-pages";
    public static final String API_2_27_1_5_URL = "https://sysyyqzyyy.msunhis.com/msun-yb-app-misl/v2/settlement-infos";
    public static final String API_2_2_3_URL = "https://sysyyqzyyy.msunhis.com/msun-middle-aggregate-hsz/v1/patients";
    public static final String API_2_23_1_URL = "https://sysyyqzyyy.msunhis.com/msun-middle-business-mr/v1/mr-home-pages/";
    private  Logger logger = LoggerFactory.getLogger(ListUploadServiceImpl.class);
    @Resource
    ZhongYangApiMapper zhongYangApiMapper;

    @Autowired
    private PlatformTransactionManager transactionManager;

    @Autowired
    private SettleListManageDao settleListManageDao;
    @Resource
    private PreGroupServiceInterface preGroupServiceInterface;

    @Override
    public void get(ZhongYangApiDto zhongYangApiDto) {

        // 组装 2.2.3住院患者基本信息查询参数
        Map<String, String> getReqParamsMap = new HashMap<>();
        if (zhongYangApiDto != null && zhongYangApiDto.getWardId() != null){
            getReqParamsMap.put("wardId", zhongYangApiDto.getWardId().toString());
        }
        if (zhongYangApiDto != null && zhongYangApiDto.getDeptId() != null){
            getReqParamsMap.put("deptId", zhongYangApiDto.getDeptId().toString());
        }
        if (zhongYangApiDto != null && zhongYangApiDto.getPatStatus() != null){
            getReqParamsMap.put("patStatus", zhongYangApiDto.getPatStatus().toString());
        }
        if (zhongYangApiDto != null && zhongYangApiDto.getPatInHosChildStatus() != null){
            getReqParamsMap.put("patInHosChildStatus", zhongYangApiDto.getPatInHosChildStatus().toString());
        }
        if (zhongYangApiDto != null && zhongYangApiDto.getOutHospitalStartTime() != null){
            getReqParamsMap.put("outHospitalStartTime", zhongYangApiDto.getOutHospitalStartTime());
        }
        if (zhongYangApiDto != null && zhongYangApiDto.getOutHospitalEndTime() != null){
            getReqParamsMap.put("outHospitalEndTime", zhongYangApiDto.getOutHospitalEndTime());
        }
        if (zhongYangApiDto != null && zhongYangApiDto.getInHospitalBeginTime() != null){
            getReqParamsMap.put("inHospitalBeginTime", zhongYangApiDto.getInHospitalBeginTime());
        }
        if (zhongYangApiDto != null && zhongYangApiDto.getInHospitalEndTime() != null){
            getReqParamsMap.put("inHospitalEndTime", zhongYangApiDto.getInHospitalEndTime());
        }

        Map<String, String> getParamsMap = new TreeMap<>(Comparator.naturalOrder());

        for (Map.Entry<String, String> m : getReqParamsMap.entrySet()) {
            getParamsMap.put(m.getKey(), m.getValue());
        }
        long timestamp = Instant.now().toEpochMilli();
        logger.info("生成的时间戳：{}",timestamp);
        String sortedParamsStr = "";
        if (CollectionUtil.isNotEmpty(getParamsMap)) {
            StringBuilder sb = new StringBuilder();
            for (Map.Entry<String, String> entry : getParamsMap.entrySet()) {
                sb.append(entry.getKey()).append("=").append(entry.getValue()).append("&");
            }
            sortedParamsStr = sb.deleteCharAt(sb.length() - 1).append(timestamp).toString();
        }
        // 生成签名sign 并加时间戳
        // 这里需要调用 getSign 方法，您需要传入 sortedParamsStr 和 appSecret
        String sign = "";
        try {
            sign = getSign(sortedParamsStr, APPSECRET);
            logger.info("2.2.3住院患者基本信息查询sign值:{}",sign);
        } catch (Exception e){
            e.printStackTrace();
            logger.info("2.2.3住院患者基本信息查询参数组装失败");
        }

        //设置请求头
        HashMap<String, String> objectObjectHashMap = new HashMap<>();
        objectObjectHashMap.put("orgId",ORGID);
        objectObjectHashMap.put("hospitalId",HOSPITALID);
        objectObjectHashMap.put("appId",APPID);
        objectObjectHashMap.put("sign",sign);
        objectObjectHashMap.put("timestamp",timestamp+"");
        objectObjectHashMap.put("signType",SIGNTYPE);

        //发送请求
        try{
            //组装url
            StringBuilder url_2_2_3 = new StringBuilder(API_2_2_3_url + "?");
            url_2_2_3.append("outHospitalEndTime");
            url_2_2_3.append("=");
            url_2_2_3.append(zhongYangApiDto.getOutHospitalEndTime());
            logger.info("请求url:{}",url_2_2_3);

            //创建http请求
            URL url = new URL(url_2_2_3.toString());
            HttpURLConnection  connection  = (HttpURLConnection)url.openConnection();
            connection.setRequestMethod("GET");

            //添加请求头
            for(Map.Entry<String,String> map : objectObjectHashMap.entrySet() ){
                connection.setRequestProperty(map.getKey(),map.getValue());
            }
            // 获取响应
            int responseCode = connection.getResponseCode();
            System.out.println("Response Code: " + responseCode);

            BufferedReader in = new BufferedReader(new InputStreamReader(connection.getInputStream()));
            String inputLine;
            StringBuilder response = new StringBuilder();
            while ((inputLine = in.readLine()) != null) {
                response.append(inputLine);
            }
            in.close();

            // 输出响应内容
            System.out.println("Response: " + response.toString());


        }catch (Exception e){
            e.printStackTrace();
        }

    }

    /**
     * 根据时间获取病案首页基本信息
     * @param zhongYangApiDto
     */
    @Override
    public void get2_23_4(ZhongYangApiDto zhongYangApiDto) {

        Integer total = getApai(zhongYangApiDto);
        int pageSize = 100;
        // 计算总页数
        int totalPages = (total + pageSize - 1) / pageSize; // 向上取整
        for (int pageNum = 2; pageNum <= totalPages; pageNum++) {
            log.info("抽取第{}页数据，共{}页",pageNum,totalPages);
            zhongYangApiDto.setPage_no(pageNum+"");
            zhongYangApiDto.setPage_size(pageSize+"");
            getApai(zhongYangApiDto);
        }

//        //批量修改抽取标识
        zhongYangApiMapper.updateFlag(zhongYangApiDto);
    }

    /**
     * 根据结算时间获取待上传的住院流水号列表
     * @param zhongYangApiDto
     */
    @Override
    public void get2_27_1_5(ZhongYangApiDto zhongYangApiDto) {

        //组装参数
        Map<String, String> getReqParamsMap = new TreeMap<>(Comparator.naturalOrder());
        getReqParamsMap.put("endSettleTime",convertDateToUrlEncoded(zhongYangApiDto.getEndSettleTime())); // 结算时间范围结束
        getReqParamsMap.put("startSettleTime",convertDateToUrlEncoded(zhongYangApiDto.getStartSettleTime())); // 结算时间范围开始
        getReqParamsMap.put("hospitalId",HOSPITALID); // 医院id
        getReqParamsMap.put("diffPlacePersonType",zhongYangApiDto.getDiffPlacePersonType()); // 异地人员类型: 0：非异地，1：省内异地，2：省外异地，空不限制

        //生成待签名参数
        Map<String, String> signStrMAp = getSignStr(getReqParamsMap);

        String sign = "";
        //生成签名
        try {
            sign = getSign(signStrMAp.get("sortedParamsStr"), APPSECRET);
            log.info("获取到的sign:{}",sign);
            log.info("获取到的时间戳：{}",signStrMAp.get("timestamp"));
        } catch (Exception e){
            e.printStackTrace();
            log.info("2_27_1_5接口生成sing失败");
        }
        //生成请求头
        Map<String, String> headersMap = getHeaders(sign, signStrMAp.get("timestamp"));

        //接受2_27_1_5 接口返回的参数
        StringBuilder response2_27_1_5;
        //调用2_27_1_5接口
        try {
            //组装url
            StringBuilder api_2_27_1_5_url = new StringBuilder(API_2_27_1_5_URL);
            api_2_27_1_5_url.append("?endSettleTime=");
            api_2_27_1_5_url.append(convertDateToUrlEncoded(zhongYangApiDto.getEndSettleTime()));
            api_2_27_1_5_url.append("&startSettleTime=");
            api_2_27_1_5_url.append(convertDateToUrlEncoded(zhongYangApiDto.getStartSettleTime()));
            api_2_27_1_5_url.append("&hospitalId=");
            api_2_27_1_5_url.append(HOSPITALID);
            api_2_27_1_5_url.append("&diffPlacePersonType=");
            api_2_27_1_5_url.append(zhongYangApiDto.getDiffPlacePersonType());
            log.info("拼接的url:{}",api_2_27_1_5_url);

            //创建请求
            URL url = new URL(api_2_27_1_5_url.toString());
            HttpURLConnection connection  = (HttpURLConnection)url.openConnection();
            connection.setRequestMethod("GET");

            //添加请求头
            for(Map.Entry<String,String> map : headersMap.entrySet() ){
                connection.setRequestProperty(map.getKey(),map.getValue());
            }
            // 获取响应
            int responseCode = connection.getResponseCode();
            BufferedReader responseString = new BufferedReader(new InputStreamReader(connection.getInputStream()));
            String inputLine;
            response2_27_1_5 = new StringBuilder();
            while ((inputLine = responseString.readLine()) != null) {
                response2_27_1_5.append(inputLine);
            }
            responseString.close();

            log.info("拿到的返回数据：{}",response2_27_1_5);
        } catch (Exception e){
            e.printStackTrace();
            log.info("2_27_1_5接口调用失败");
        }




    }

    @Override
    public void get2_23_3(ZhongYangApiDto zhongYangApiDto) {
        //根据病案号获取在病人信息
        

    }

    /**
     * 根据入院时间获取在院病人信息
     * @param zhongYangApiDto
     */
    @Override
    public List<Get2_2_3Dto> get2_2_3(ZhongYangApiDto zhongYangApiDto) {

        String inHospitalBeginTime = zhongYangApiDto.getInHospitalBeginTime();
        String inHospitalEndTime = zhongYangApiDto.getInHospitalEndTime();

        zhongYangApiDto.setInHospitalBeginTime(inHospitalBeginTime +" 00:00:00");
        zhongYangApiDto.setInHospitalEndTime(inHospitalEndTime+" 23:59:59");

/*        ArrayList<String> deptIdList = new ArrayList<>();
        deptIdList.add("702553628055834634");
        deptIdList.add("702553628034863119");
        deptIdList.add("702553628043251729");
        deptIdList.add("702553628030668800");
        deptIdList.add("702553628026474515");
        deptIdList.add("702553628030668803");
        deptIdList.add("5527567534601014792");
        deptIdList.add("702553628030668804");
        deptIdList.add("5904128286938893829");
        deptIdList.add("702553628055834631");
        deptIdList.add("702553628030668802");
        deptIdList.add("702553628055834633");
        deptIdList.add("5747362482000104961");
        deptIdList.add("702553628055834632");
        deptIdList.add("702553628047446025");
        deptIdList.add("702553628030668807");
        deptIdList.add("702553628055834635");
        deptIdList.add("702553628047446028");
        deptIdList.add("702553628030668805");*/

        ArrayList<Get2_2_3Dto> get223DtoList = new ArrayList<>();

/*        for (int i = 0; i < deptIdList.size(); i++) {*/
            Map<String, String> getReqParamsMap = new TreeMap<>(Comparator.naturalOrder());
            getReqParamsMap.put("deptId",zhongYangApiDto.getDeptId()+"");
            getReqParamsMap.put("patStatus","1");
            getReqParamsMap.put("inHospitalBeginTime",zhongYangApiDto.getInHospitalBeginTime());
            getReqParamsMap.put("inHospitalEndTime",zhongYangApiDto.getInHospitalEndTime());

            StringBuilder apiUrl = new StringBuilder(API_2_2_3_url);
            apiUrl.append("?");
            apiUrl.append("deptId");
            apiUrl.append("=");
            apiUrl.append(zhongYangApiDto.getDeptId());
            apiUrl.append("&");
            apiUrl.append("patStatus=1");
            apiUrl.append("&");
            apiUrl.append("inHospitalBeginTime");
            apiUrl.append("=");
            apiUrl.append(convertDateToUrlEncoded(zhongYangApiDto.getInHospitalBeginTime()));
            apiUrl.append("&");
            apiUrl.append("inHospitalEndTime");
            apiUrl.append("=");
            apiUrl.append(convertDateToUrlEncoded(zhongYangApiDto.getInHospitalEndTime()));
//            log.info("生成的URL:{}",apiUrl);
            StringBuilder response = getConnection(getReqParamsMap, apiUrl);
            log.error("科室id是---{}返回的参数：{}",zhongYangApiDto.getDeptId(),response);
            Map<String,Object> responseMap = JSON.parseObject(response.toString(), Map.class);
            Object data = responseMap.get("data");
                // 解析 JSON 字符串并转换为 List<Map<String, String>>
            List<Map<String, String>>  dataList = JSON.parseObject(data.toString(), new TypeReference<List<Map<String, String>>>() {});
            for (int j = 0; j < dataList.size(); j++) {
                Map<String, String> dataMap = dataList.get(j);
                ObjectMapper objectMapper = new ObjectMapper();
                Get2_2_3Dto get223Dto = objectMapper.convertValue(dataMap, Get2_2_3Dto.class);
                if (zhongYangApiDto.getPatInHosId() != null && Objects.equals(zhongYangApiDto.getPatInHosId(), get223Dto.getPatInHosCode())){
                    get223DtoList.add(get223Dto);
                }else if ( zhongYangApiDto.getPatInHosId() == null || zhongYangApiDto.getPatInHosId().isEmpty()){
                    get223DtoList.add(get223Dto);
                }
            }
/*        }*/
        return get223DtoList;
    }

    @Override
    public StringBuilder get2_23_1(ZhongYangApiDto zhongYangApiDto) {

        StringBuilder apiUrl = new StringBuilder(API_2_23_1_URL);
        apiUrl.append(zhongYangApiDto.getPatInHosId());
        apiUrl.append("?");
        apiUrl.append("data_source=0");

        Map<String, String> getParamsMap = new TreeMap<>();
        getParamsMap.put("data_source","0");
        StringBuilder response = getConnection(getParamsMap, apiUrl);
        return response;
    }

    @Override
    public PreGroupVo preGroupSettle(ZhongYangApiDto zhongYangApiDto,HttpServletResponse response,HttpServletRequest request) {

        StringBuilder responseData = new StringBuilder();
        //获取2.23.1的返回值
        try {
             responseData = get2_23_1(zhongYangApiDto);
             log.info("获取到的2_23_1的值:{}",responseData);
        } catch (Exception e){
            e.printStackTrace();
            log.info("2_23_1接口调用失败");
        }
        JSONObject responseObj = JSONUtil.parseObj(responseData);
        Map<String,String> data = JSON.parseObject(responseObj.get("data").toString(), Map.class);
        JSONObject DataObj = JSONUtil.parseObj(data);
        if (DataObj.get("patientMainInfo") == null){
            throw  new AppException("请确认该病人的病案首页是否保存");
        }
        //获取病人主信息
        Map<String,String> patientMainInfoMap = JSON.parseObject(DataObj.get("patientMainInfo").toString(), Map.class);
        //获取获取原始诊疗信息treatments节点
        Map<String,String> patientBaseInfoMap = JSON.parseObject(DataObj.get("patientBaseInfo").toString(), Map.class);
        //获取原始诊疗信息
        Map<String,String> treatmentsMap = JSON.parseObject(DataObj.get("treatments").toString(), Map.class);
        //获取原始入院诊断信息
        Map<String,String> diagnosisInfosMap = JSON.parseObject(DataObj.get("diagnosisInfos").toString(), Map.class);
        //获取原始出院诊断信息
        List<Map> leaveDiagnosisListMap = JSON.parseArray(DataObj.get("leaveDiagnosis").toString(), Map.class);
        //获取原始手术信息
        List<Map> surgeryInfosListMap = JSON.parseArray(DataObj.get("surgeryInfos").toString(), Map.class);
        //获取原始icu信息
        List<Map> icuInfosListMap = JSON.parseArray(DataObj.get("icuInfos").toString(), Map.class);
        //获取原始费用信息
        Map<String,String> costInfoMap = JSON.parseObject(DataObj.get("costInfo").toString(), Map.class);
        //获取原始签名信息
        Map<String,String> doctorSignMap = JSON.parseObject(DataObj.get("doctorSign").toString(), Map.class);
        //获取原始其他信息
        Map<String,String> otherInfoMap = JSON.parseObject(DataObj.get("otherInfo").toString(), Map.class);

        //组装分组参数
        PreGroup2Dto preGroup2Dto = new PreGroup2Dto();
        //基础
        PreGroup2Base preGroup2Base = new PreGroup2Base();
        //诊断
        ArrayList<PreGroup2Dis> preGroup2DisList = new ArrayList<>();
        //手术
        ArrayList<PreGroup2Ope> preGroup2OpesList = new ArrayList<>();
//        //ICU
//        PreGroup2Icu preGroup2Icu = new PreGroup2Icu();

        //处理基本信息
        preGroup2Base.setAbtl_medn_fee(String.valueOf(costInfoMap.get("antibioticsCost")));
        preGroup2Base.setAdm_caty(patientMainInfoMap.get("patInDeptId"));
        preGroup2Base.setAdm_date(patientMainInfoMap.get("patInTime"));
        preGroup2Base.setAdm_ward(patientMainInfoMap.get("patInSickroom"));
        preGroup2Base.setAdm_way_code(patientMainInfoMap.get("patInWayCode"));
        preGroup2Base.setAge(String.valueOf(patientMainInfoMap.get("patAgeYear")));
        preGroup2Base.setAlbu_fee(String.valueOf(costInfoMap.get("albumin")));
        preGroup2Base.setAnst_fee(String.valueOf(costInfoMap.get("anesExpenses")));
        preGroup2Base.setAtddr_code(doctorSignMap.get("mainDoctorId"));
        preGroup2Base.setAtddr_name(doctorSignMap.get("mainDoctor"));
        preGroup2Base.setBirplc(patientBaseInfoMap.get("bornDetailAddress"));
        preGroup2Base.setBlo_fee(String.valueOf(costInfoMap.get("bloodCost")));
        preGroup2Base.setBrdy(patientMainInfoMap.get("birth"));
        // 拼接颅脑损伤患者昏迷时间入院前时间
        StringBuilder brn_damg_afadm_coma_dura = new StringBuilder();
        String beforeComaDays = treatmentsMap.get("beforeComaDays") != null ? String.valueOf(treatmentsMap.get("beforeComaDays")) : "0";
        String beforeComaHours = treatmentsMap.get("beforeComaHours") != null ? String.valueOf(treatmentsMap.get("beforeComaHours")) : "0";
        String beforeComaMinutes = treatmentsMap.get("beforeComaMinutes") != null ? String.valueOf(treatmentsMap.get("beforeComaMinutes")) : "0";
        brn_damg_afadm_coma_dura.append(beforeComaDays).append("/").append(beforeComaHours).append("/").append(beforeComaMinutes);
        preGroup2Base.setBrn_damg_afadm_coma_dura(brn_damg_afadm_coma_dura.toString());
        // 拼接颅脑损伤患者昏迷时间入院后时间
        StringBuilder brn_damg_bfadm_coma_dura = new StringBuilder();
        String afterComaDays = treatmentsMap.get("afterComaDays") != null ? String.valueOf(treatmentsMap.get("afterComaDays")) : "0";
        String afterComaHours = treatmentsMap.get("afterComaHours") != null ? String.valueOf(treatmentsMap.get("afterComaHours")) : "0";
        String afterComaMinutes = treatmentsMap.get("afterComaMinutes") != null ? String.valueOf(treatmentsMap.get("afterComaMinutes")) : "0";
        brn_damg_bfadm_coma_dura.append(afterComaDays).append("/").append(afterComaHours).append("/").append(afterComaMinutes);
        preGroup2Base.setBrn_damg_bfadm_coma_dura(brn_damg_bfadm_coma_dura.toString());
        preGroup2Base.setCertno(patientMainInfoMap.get("cardNum"));
        preGroup2Base.setChfdr_code(doctorSignMap.get("deputyHeadId"));
        preGroup2Base.setClnc_dise_fee(String.valueOf(costInfoMap.get("clinicalDiagnosisCost")));
        preGroup2Base.setClnc_phys_trt_fee(String.valueOf(costInfoMap.get("clinicalPhysicalFee")));
        preGroup2Base.setClotfac_fee(String.valueOf(costInfoMap.get("coagulationFactor")));
        preGroup2Base.setCodr_code(doctorSignMap.get("codePersonId"));
        preGroup2Base.setCom_med_serv_oth_fee("0");
        preGroup2Base.setConer_addr(patientBaseInfoMap.get("contactDetailAddress"));
        preGroup2Base.setConer_name(patientMainInfoMap.get("contact"));
        preGroup2Base.setConer_rlts_code(patientMainInfoMap.get("contactRelationshipCode"));
        preGroup2Base.setConer_tel(patientBaseInfoMap.get("contactPhone"));
        preGroup2Base.setCurr_addr(patientBaseInfoMap.get("addressDetailAddress"));
        preGroup2Base.setCurr_addr_poscode(patientBaseInfoMap.get("addressZip"));
        preGroup2Base.setCyki_fee(String.valueOf(costInfoMap.get("cytokine")));
        preGroup2Base.setDamg_intx_ext_rea(diagnosisInfosMap.get("toxicosisReason"));
        preGroup2Base.setDamg_intx_ext_rea_disecode(diagnosisInfosMap.get("toxicosisReasonCode"));
        preGroup2Base.setDeptdrt_code(doctorSignMap.get("deptHeadId"));
        preGroup2Base.setDicm_drug_name(treatmentsMap.get("hyperMedicine"));
        preGroup2Base.setDie_autp_flag("");
        preGroup2Base.setDrug_dicm_flag(treatmentsMap.get("hyper"));
        preGroup2Base.setDscg_31days_rinp_flag(treatmentsMap.get("patInAgain"));
        preGroup2Base.setDscg_caty(patientMainInfoMap.get("patOutDeptId"));
        preGroup2Base.setDscg_date(patientMainInfoMap.get("patOutTime"));
        preGroup2Base.setDscg_ward(patientMainInfoMap.get("patOutSickroom"));
        preGroup2Base.setDscg_way(treatmentsMap.get("patOutMethod"));
        StringBuilder empr_addr = new StringBuilder();
        empr_addr.append(patientBaseInfoMap.get("jobCompany")).append(patientBaseInfoMap.get("jobDetailAddress"));
        preGroup2Base.setEmpr_addr(empr_addr.toString());
        preGroup2Base.setEmpr_poscode(patientBaseInfoMap.get("jobZip"));
        preGroup2Base.setEmpr_tel(patientBaseInfoMap.get("jobPhone"));
        preGroup2Base.setExam_dspo_matl_fee(String.valueOf(costInfoMap.get("checkMaterials")));
        preGroup2Base.setFixmedins_code("H42030401471");
        preGroup2Base.setGend(patientMainInfoMap.get("sexCode"));
        preGroup2Base.setGlon_fee(String.valueOf(costInfoMap.get("globulin")));
        preGroup2Base.setHi_no("");
        preGroup2Base.setIntn_dr_code(doctorSignMap.get("studyDoctorId"));
        preGroup2Base.setIpt_days(new BigDecimal(String.valueOf(patientMainInfoMap.get("patInDayCount"))));
        preGroup2Base.setIpt_dr_code(doctorSignMap.get("patInDoctorId"));
        preGroup2Base.setLab_diag_fee(String.valueOf(costInfoMap.get("laboratoryCost")));
        preGroup2Base.setLv1_nurscare_days(treatmentsMap.get("yjhlT"));
        preGroup2Base.setLv3_nurscare_days(treatmentsMap.get("sjhlT"));
        preGroup2Base.setMedcas_qlt_code("");
        preGroup2Base.setMedcasno(patientMainInfoMap.get("medicalRecordCode"));
        String payMethodId = patientMainInfoMap.get("payMethodId");
        String medfee_paymtd_code;

        switch (payMethodId) {
            case "1.1":
                medfee_paymtd_code = "01";
                break;
            case "2.1":
                medfee_paymtd_code = "02";
                break;
            case "4" :
                medfee_paymtd_code = "04";
                break;
            case "5" :
                medfee_paymtd_code = "05";
                break;
            case "6" :
                medfee_paymtd_code = "06";
                break;
            case "7" :
                medfee_paymtd_code = "07";
                break;
            case "9" :
                medfee_paymtd_code = "99";
                break;
            case "3.1" :
                medfee_paymtd_code = "03";
            default:
                medfee_paymtd_code = "99";
                break;
        }
        preGroup2Base.setMedfee_paymtd_code(medfee_paymtd_code);
        preGroup2Base.setMedfee_sumamt(new BigDecimal(String.valueOf(costInfoMap.get("totalCost"))));
        preGroup2Base.setMrg_stas("");
        //处理籍贯
        StringBuilder napl = new StringBuilder();
        napl.append(patientBaseInfoMap.get("hometownProvience")).append(patientBaseInfoMap.get("hometownCity"));
        preGroup2Base.setNapl(napl.toString());
        preGroup2Base.setNaty(patientBaseInfoMap.get("nationCode"));
        preGroup2Base.setNsrgtrt_item_fee(String.valueOf(costInfoMap.get("nonsurgicalTreatment")));
        preGroup2Base.setNtly(patientBaseInfoMap.get("countryCode"));
        preGroup2Base.setNurs_fee(String.valueOf(costInfoMap.get("nursingExpenses")));
        preGroup2Base.setOprn_dspo_matl_fee(String.valueOf(costInfoMap.get("operationMaterials")));
        preGroup2Base.setOprn_fee(String.valueOf(costInfoMap.get("operationCost")));
        preGroup2Base.setOrdn_med_servfee(String.valueOf(costInfoMap.get("generalMedical")));
        preGroup2Base.setOrdn_trt_oprt_fee(String.valueOf(costInfoMap.get("generalTreatment")));
        preGroup2Base.setOth_fee("0");
        preGroup2Base.setOtp_wm_dise(diagnosisInfosMap.get("xyMzzd"));
        preGroup2Base.setPalg_diag_fee(String.valueOf(costInfoMap.get("pathologicalCost")));
        preGroup2Base.setPatn_ipt_cnt(String.valueOf(patientMainInfoMap.get("patInCount")));
        preGroup2Base.setPrfs(patientMainInfoMap.get("jobCode"));
        preGroup2Base.setPsn_name(patientMainInfoMap.get("patName"));
        preGroup2Base.setPsn_tel(patientBaseInfoMap.get("addressPhone"));
        preGroup2Base.setQltctrl_date(treatmentsMap.get("qualityDate"));
        preGroup2Base.setQltctrl_dr_code(doctorSignMap.get("qualityDoctorId"));
        preGroup2Base.setQltctrl_nurs_code(doctorSignMap.get("qualityNurseId"));
        preGroup2Base.setRdhy_diag_fee(String.valueOf(costInfoMap.get("pacsCost")));
        preGroup2Base.setRefldept_dept(patientMainInfoMap.get("patChangeDeptId"));
        String resc_cnt = treatmentsMap.get("saveCount").isEmpty() ? null : treatmentsMap.get("saveCount");
        preGroup2Base.setResc_cnt(resc_cnt);
        preGroup2Base.setResd_addr(patientBaseInfoMap.get("residenceDetailAddress"));
        preGroup2Base.setResd_addr_poscode(patientBaseInfoMap.get("residenceZip"));
        preGroup2Base.setResp_nurs_code(doctorSignMap.get("respNurseId"));
        preGroup2Base.setRgtrt_trt_fee(String.valueOf(costInfoMap.get("operationTreatmentCost")));
        preGroup2Base.setRh_code(treatmentsMap.get("rh"));
        preGroup2Base.setRhab_fee(String.valueOf(costInfoMap.get("rehabilitationCost")));
        preGroup2Base.setSpga_nurscare_days(treatmentsMap.get("tjhlT"));
        preGroup2Base.setTcm_trt_fee(String.valueOf(costInfoMap.get("tcmTreatment")));
        preGroup2Base.setTcmherb_fee("0");
        preGroup2Base.setTrain_dr_code(doctorSignMap.get("advancedDoctorId"));
        preGroup2Base.setTrt_dspo_matl_fee(String.valueOf(costInfoMap.get("medicalMaterials")));
        //呼吸机使用时长 vent_used_dura
        StringBuilder vent_used_dura = new StringBuilder();
        String respiratorUseTimeDay = treatmentsMap.get("respiratorUseTimeDay") != null ? String.valueOf(treatmentsMap.get("respiratorUseTimeDay")) : "0";
        String respiratorUseTimeHour = treatmentsMap.get("respiratorUseTimeHour") != null ? String.valueOf(treatmentsMap.get("respiratorUseTimeHour")) : "0";
        String respiratorUseTimeMin = treatmentsMap.get("respiratorUseTimeMin") != null ? String.valueOf(treatmentsMap.get("respiratorUseTimeMin")) : "0";
        vent_used_dura.append(respiratorUseTimeDay).append("/").append(respiratorUseTimeHour).append("/").append(respiratorUseTimeMin);
        preGroup2Base.setVent_used_dura(vent_used_dura.toString());
        preGroup2Base.setWm_dise_code(diagnosisInfosMap.get("xyMzzdCode"));
        preGroup2Base.setWmfee(String.valueOf(costInfoMap.get("westernMedicineFee")));

        //处理诊断信息
        for (int i = 0; i < leaveDiagnosisListMap.size(); i++) {
            Map leaveDiagnosisMap = leaveDiagnosisListMap.get(i);
            PreGroup2Dis preGroup2Dis = new PreGroup2Dis();
            preGroup2Dis.setAdm_cond(leaveDiagnosisMap.get("inSituation").toString());
            preGroup2Dis.setDiag_name(leaveDiagnosisMap.get("diagnosisName").toString());
            preGroup2Dis.setDiag_code(leaveDiagnosisMap.get("diagnosisCode").toString());
            String diagType = leaveDiagnosisMap.get("isMainDiagnosis").toString();
            String maindiag_flag =  ("1".equals(diagType)) ? "1":"0";
            preGroup2Dis.setMaindiag_flag(maindiag_flag);
            preGroup2DisList.add(preGroup2Dis);
        }

        //处理手术信息
        for (int i = 0; i < surgeryInfosListMap.size(); i++) {
            Map surgeryInfostMap = surgeryInfosListMap.get(i);
            PreGroup2Ope preGroup2Ope = new PreGroup2Ope();
            preGroup2Ope.setOper_name(surgeryInfostMap.get("operator").toString());
            preGroup2Ope.setOprn_oprt_code(surgeryInfostMap.get("operationCode").toString());
            preGroup2Ope.setOprn_oprt_name(surgeryInfostMap.get("operationName").toString());
            preGroup2Ope.setOprn_oprt_date(surgeryInfostMap.get("operationDate").toString());
            preGroup2Ope.setOprn_oprt_type(i+"");
            preGroup2OpesList.add(preGroup2Ope);
        }

        preGroup2Dto.setBaseinfo(preGroup2Base);
        preGroup2Dto.setDiseinfo(preGroup2DisList);
        preGroup2Dto.setOprninfo(preGroup2OpesList);
        preGroup2Dto.setIsWeb("1");
        preGroup2Dto.setSkippingValidation(true);

        // 创建请求和响应对象
        PreGroupVo preGroupInfo5 = preGroupServiceInterface.getPreGroupInfo5(preGroup2Dto, response, request);

        return preGroupInfo5;

    }

    /**
     * 生成签名
     * @param preStr 待签名字符串
     * @param appSecret 私钥字符串
     * @return base64 字符串
     * @throws Exception
     */
    public String getSign(String preStr, String appSecret) throws Exception {
        appSecret = appSecret.replace("-----BEGIN PRIVATE KEY-----", "")
                .replace("-----END PRIVATE KEY-----", "")
                .replace("\r", "").replace("\n", "").trim();
        // 初始化算法 SHA256
        String suite = "SHA256WithRSA";
        Signature signature = Signature.getInstance(suite);

        PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(Base64.decodeBase64(appSecret));
        KeyFactory keyFac = KeyFactory.getInstance("RSA");
        PrivateKey privateKey = keyFac.generatePrivate(keySpec);
        signature.initSign(privateKey);

        // 待签名字符串转 byte 数组使用 UTF8
        byte[] msgBuf = preStr.getBytes("UTF-8");
        signature.update(msgBuf);
        byte[] byteSign = signature.sign();

        // 签名值 byte 数组转字符串用 BASE64
        return Base64.encodeBase64String(byteSign);
    }

    /**
     * 生成待签名参数
     * @param getParamsMap
     * @return
     */
    public Map<String,String> getSignStr(Map<String,String>  getParamsMap){

        String sortedParamsStr = "";

        long timestamp = System.currentTimeMillis();

        if (CollectionUtil.isNotEmpty(getParamsMap)) {
            StringBuilder sb = new StringBuilder();
            for (Map.Entry<String, String> entry : getParamsMap.entrySet()) {
                sb.append(entry.getKey()).append("=").append(entry.getValue()).append("&");
            }
            sortedParamsStr = sb.deleteCharAt(sb.length() - 1).append(timestamp).toString();
        }

        HashMap<String, String> singsMap = new HashMap<>();
        singsMap.put("sortedParamsStr",sortedParamsStr);
        singsMap.put("timestamp",timestamp+"");

        return singsMap;
    }

    /**
     * 生成请求头
     * @param sign
     * @param timestamp
     * @return
     */
    public Map<String,String> getHeaders(String sign,String timestamp){

        HashMap<String, String> headersMap = new HashMap<>();
        headersMap.put("orgId",ORGID);
        headersMap.put("hospitalId",HOSPITALID);
        headersMap.put("appId",APPID);
        headersMap.put("sign",sign);
        headersMap.put("timestamp",timestamp);
        headersMap.put("signType",SIGNTYPE);

        return headersMap;

    }

    /**
     * 将日期字符串转换为 URL 编码格式
     * @param dateString
     * @return
     */
    public  String convertDateToUrlEncoded(String dateString) {
        try {
            // 定义输入日期格式
            SimpleDateFormat inputFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            // 解析字符串为 Date 对象
            Date date = inputFormat.parse(dateString);

            // 定义输出日期格式
            SimpleDateFormat outputFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            // 格式化 Date 对象为字符串
            String formattedDate = outputFormat.format(date);

            // 对字符串进行 URL 编码
            return URLEncoder.encode(formattedDate, "UTF-8");
        } catch (ParseException | UnsupportedEncodingException e) {
            e.printStackTrace();
            return null; // 返回 null 如果有异常
        }
    }

    /**
     * 调用2_23_4接口
     * @param zhongYangApiDto
     * @return
     */
    public Integer getApai (ZhongYangApiDto zhongYangApiDto){
        //组装url
        StringBuilder url_2_23_4 = new StringBuilder(API_2_23_4_URL);
        url_2_23_4.append("/");
        url_2_23_4.append(zhongYangApiDto.getPat_out_time_start());
        url_2_23_4.append("/");
        url_2_23_4.append(zhongYangApiDto.getPat_out_time_end());
        url_2_23_4.append("/");
        url_2_23_4.append(zhongYangApiDto.getPage_no());
        url_2_23_4.append("/");
        url_2_23_4.append(zhongYangApiDto.getPage_size());
        url_2_23_4.append("?");
        url_2_23_4.append("data_source");
        url_2_23_4.append("=");
        url_2_23_4.append(zhongYangApiDto.getData_source());
        Map<String, String> getReqParamsMap = new TreeMap<>(Comparator.naturalOrder());
        getReqParamsMap.put("data_source",zhongYangApiDto.getData_source()+"");
        //生成代签名参数
        Map<String, String> signStrMAp = getSignStr(getReqParamsMap);
        String sign = "";
        HashMap<String, HashMap<String, Object>> stringHashMapHashMap = new HashMap<>();
        //生成签名
        try {
            sign = getSign(signStrMAp.get("sortedParamsStr"), APPSECRET);
            logger.info("调用2_23_4接口生成的sing:{}",sign);
            logger.info("调用2_23_4接口生成的时间戳timestamp：{}",signStrMAp.get("timestamp"));
        } catch (Exception e){
            e.printStackTrace();
            logger.info("2_23_4接口生成sing失败");
        }
        //生成请求头
        Map<String, String> headersMap = getHeaders(sign, signStrMAp.get("timestamp"));
        List<Map> listMap = new ArrayList<>();
        Integer total  = 0;
        try {
            //创建请求
            URL url = new URL(url_2_23_4.toString());
            HttpURLConnection  connection  = (HttpURLConnection)url.openConnection();
            connection.setRequestMethod("GET");
            //添加请求头
            for(Map.Entry<String,String> map : headersMap.entrySet() ){
                connection.setRequestProperty(map.getKey(),map.getValue());
            }

            // 获取响应
            int responseCode = connection.getResponseCode();
            System.out.println("Response Code: " + responseCode);

            BufferedReader in = new BufferedReader(new InputStreamReader(connection.getInputStream()));
            String inputLine;
            StringBuilder response = new StringBuilder();
            while ((inputLine = in.readLine()) != null) {
                response.append(inputLine);
            }
            JSONObject responseData = JSONUtil.parseObj(response);
            Object data = responseData.get("data");
            Object mrInFoExternalVO = JSONUtil.parseObj(data).get("mrInFoExternalVOList");
            total = Integer.parseInt(JSONUtil.parseObj(data).get("total").toString());
            String mrInFoExternalVOString = mrInFoExternalVO.toString();
            listMap = JSON.parseArray(mrInFoExternalVOString, Map.class);
            logger.info("拿到的信息：{}",response);
            log.info("拿到的信息：{}",response);
        }catch (Exception e){
            e.printStackTrace();
            log.info("2_23_4接口调用失败");
        }



        for (int i = 0; i < listMap.size(); i++) {
            //存放基本信息节点数据
            HashMap<String, String> basinfoMap = new HashMap<>();
            //存放出院诊断信息节点数据
            ArrayList<Map<String, String>> diagInfoListMap = new ArrayList<>();
            // 存放icu息节点数据
            ArrayList<Map<String, String>> icuInfoListMap = new ArrayList<>();
            //存放手术操作节点信息
            ArrayList<Map<String, String>> oprnInfoListMap = new ArrayList<>();
            //存放基金支付节点信息
            HashMap<String, String> payInfoListMap = new HashMap<>();
            //存放收费项目节点信息
            HashMap<String, String> itemInfoMap = new HashMap<>();



            Map map = listMap.get(i);
            //获取病人主信息
            Map<String,String> patientMainInfoMap = JSON.parseObject(map.get("patientMainInfo").toString(), Map.class);
            //获取获取原始诊疗信息treatments节点
            Map<String,String> patientBaseInfoMap = JSON.parseObject(map.get("patientBaseInfo").toString(), Map.class);
            //获取原始诊疗信息
            Map<String,String> treatmentsMap = JSON.parseObject(map.get("treatments").toString(), Map.class);
            //获取原始入院诊断信息
            Map<String,String> diagnosisInfosMap = JSON.parseObject(map.get("diagnosisInfos").toString(), Map.class);
            //获取原始出院诊断信息
            List<Map> leaveDiagnosisListMap = JSON.parseArray(map.get("leaveDiagnosis").toString(), Map.class);
            //获取原始手术信息
            List<Map> surgeryInfosListMap = JSON.parseArray(map.get("surgeryInfos").toString(), Map.class);
            //获取原始icu信息
            List<Map> icuInfosListMap = JSON.parseArray(map.get("icuInfos").toString(), Map.class);
            //获取原始费用信息
            Map<String,String> costInfoMap = JSON.parseObject(map.get("costInfo").toString(), Map.class);
            //获取原始签名信息
            Map<String,String> doctorSignMap = JSON.parseObject(map.get("doctorSign").toString(), Map.class);
            //获取原始其他信息
            Map<String,String> otherInfoMap = JSON.parseObject(map.get("otherInfo").toString(), Map.class);


            //处理基本信息
            StringBuilder unique_id = new StringBuilder();
            String medicalRecordCode = String.valueOf(patientMainInfoMap.get("medicalRecordCode"));
            String patInCount = String.valueOf(patientMainInfoMap.get("patInCount"));

            unique_id.append(medicalRecordCode);
            if (patInCount != null && !patInCount.trim().isEmpty()){
                unique_id.append(patInCount);
            }
            basinfoMap.put("unique_id",unique_id.toString()); //唯一ID
            basinfoMap.put("mdtrt_sn","");//就医流水号
            basinfoMap.put("mdtrt_id",patientMainInfoMap.get("patInHosId"));//就诊ID
            basinfoMap.put("psn_no",patientMainInfoMap.get("patId"));//人员编号
            basinfoMap.put("patn_ipt_cnt",patientMainInfoMap.get("patInCount"));//患者住院次数
            basinfoMap.put("ipt_no",patientMainInfoMap.get("patInHosCode"));//住院号
            basinfoMap.put("medcasno",patientMainInfoMap.get("medicalRecordCode"));//病案号
            basinfoMap.put("psn_name",patientMainInfoMap.get("patName"));//人员姓名
            basinfoMap.put("gend",patientMainInfoMap.get("sexCode"));//性别
            basinfoMap.put("brdy",patientMainInfoMap.get("birth"));//出生日期
            basinfoMap.put("age",patientMainInfoMap.get("patAgeYear"));//年龄
            basinfoMap.put("ntly",patientBaseInfoMap.get("countryCode"));//国籍
            //处理年龄不足一岁
            int patAgeDay = Integer.parseInt(patientMainInfoMap.get("patAgeDay"));
            int patAgeMonth = Integer.parseInt(patientMainInfoMap.get("patAgeMonth"));
            Integer nwb_age = patAgeMonth * 30 + patAgeDay;
            nwb_age = (nwb_age == 0) ? null : nwb_age;
            if (nwb_age != null) {
                basinfoMap.put("nwb_age", nwb_age.toString()); // 年龄不足1周岁
            } else {
                basinfoMap.put("nwb_age", null); // 处理 null 情况
            }
            //处理多新生儿出生体重
            StringBuilder mul_nwb_bir_wt = new StringBuilder();
            ArrayList<String> bir_wt_list = new ArrayList<>();
            String[] keys = {"babyBornWeight", "babyBornWeight2", "babyBornWeight3", "babyBornWeight4", "babyBornWeight5"};
            for (String key : keys) {
                String value = patientBaseInfoMap.get(key);
                if (value != null) {
                    if (mul_nwb_bir_wt.length() > 0) {
                        mul_nwb_bir_wt.append(","); // 添加逗号分隔
                    }
                    bir_wt_list.add(value);
                    mul_nwb_bir_wt.append(value);
                }
            }
            if (bir_wt_list.size() > 1){
                basinfoMap.put("mul_nwb_bir_wt",mul_nwb_bir_wt.toString()); //多新生儿出生体重
                basinfoMap.put("nwb_bir_wt",null); //新生儿出生体重
            } else {
                basinfoMap.put("nwb_bir_wt",mul_nwb_bir_wt.toString()); //新生儿出生体重
                basinfoMap.put("mul_nwb_bir_wt",""); //多新生儿出生体重
            }
            String nwb_adm_wt = patientBaseInfoMap.get("babyPatInWeight").isEmpty() ? null : patientBaseInfoMap.get("babyPatInWeight");
            basinfoMap.put("nwb_adm_wt",nwb_adm_wt);//新生儿入院体重
            basinfoMap.put("birplc",patientBaseInfoMap.get("bornDetailAddress"));//出生地

            //处理籍贯
            StringBuilder napl = new StringBuilder();
            napl.append(patientBaseInfoMap.get("hometownProvience")).append(patientBaseInfoMap.get("hometownCity"));
            basinfoMap.put("napl",napl.toString());//籍贯
            basinfoMap.put("naty",patientBaseInfoMap.get("nationCode"));//民族
            basinfoMap.put("certno",patientMainInfoMap.get("cardNum"));//证件号码
            basinfoMap.put("prfs",patientMainInfoMap.get("jobCode"));//职业
            basinfoMap.put("mrg_stas","");//婚姻状态
            basinfoMap.put("curr_addr_poscode",patientBaseInfoMap.get("addressZip"));//现住址-邮政编码
            basinfoMap.put("curr_addr",patientBaseInfoMap.get("addressDetailAddress"));//现住址
            basinfoMap.put("psn_tel",patientBaseInfoMap.get("addressPhone"));//个人联系电话
            basinfoMap.put("resd_addr_prov",patientBaseInfoMap.get("residenceProvience"));//户口地址-省（自治区、直辖市）
            basinfoMap.put("resd_addr_city",patientBaseInfoMap.get("residenceCity"));//户口地址-市（地区）
            basinfoMap.put("resd_addr_coty",patientBaseInfoMap.get("residenceTown"));//户口地址-县（区）
            basinfoMap.put("resd_addr_subd","");//户口地址-乡（镇、街道办事处）
            basinfoMap.put("resd_addr_vil","");//户口地址-村（街、路、弄等）
            basinfoMap.put("resd_addr_housnum","");//户口地址-门牌号码
            basinfoMap.put("resd_addr_poscode",patientBaseInfoMap.get("residenceZip"));//户口地址- 邮政编码
            basinfoMap.put("resd_addr",patientBaseInfoMap.get("residenceDetailAddress"));//户口地址
            basinfoMap.put("empr_tel",patientBaseInfoMap.get("jobPhone"));//工作单位联系电话
            basinfoMap.put("empr_poscode",patientBaseInfoMap.get("jobZip"));//工作单位- 邮政编码
            StringBuilder empr_addr = new StringBuilder();
            empr_addr.append(patientBaseInfoMap.get("jobCompany")).append(patientBaseInfoMap.get("jobDetailAddress"));
            basinfoMap.put("empr_addr",empr_addr.toString());//工作单位及地址
            basinfoMap.put("coner_tel",patientBaseInfoMap.get("contactPhone"));//联系人电话
            basinfoMap.put("coner_name",patientMainInfoMap.get("contact"));//联系人姓名
            basinfoMap.put("coner_addr",patientBaseInfoMap.get("contactDetailAddress"));//联系人地址
            basinfoMap.put("coner_rlts_code",patientMainInfoMap.get("contactRelationshipCode"));//与联系人关系代码
            basinfoMap.put("adm_way_name",patientMainInfoMap.get("patInWay"));//入院途径名称
            basinfoMap.put("adm_way_code",patientMainInfoMap.get("patInWayCode"));//入院途径代码

            String trt_type_name = "" ;
            String trt_type = "";
            String treatmentCategory = treatmentsMap.get("treatmentCategory");
            switch (treatmentCategory){
                case "1" :
                    trt_type_name = "中医";
                    trt_type = "2.1";
                    break;
                case "1.1" :
                    trt_type_name = "中医";
                    trt_type = "2.1";
                    break;
                case "1.2" :
                    trt_type_name = "民族医";
                    trt_type = "2.2";
                    break;
                case "2" :
                    trt_type_name = "中西医";
                    trt_type = "3";
                    break;
                case "3" :
                    trt_type_name = "西医";
                    trt_type = "1";
                    break;
            }
            basinfoMap.put("trt_type_name",trt_type_name);//治疗类别名称
            basinfoMap.put("trt_type",trt_type);//治疗类别
            basinfoMap.put("adm_caty",patientMainInfoMap.get("patInDeptId"));//入院科别
            basinfoMap.put("adm_ward",patientMainInfoMap.get("patInSickroom"));//入院病房
            basinfoMap.put("adm_date",patientMainInfoMap.get("patInTime"));//入院日期
            basinfoMap.put("dscg_date",patientMainInfoMap.get("patOutTime"));//出院日期
            basinfoMap.put("dscg_caty",patientMainInfoMap.get("patOutDeptId"));//出院科别
            basinfoMap.put("refldept_dept",patientMainInfoMap.get("patChangeDeptId"));//转科科别
            basinfoMap.put("dscg_ward",patientMainInfoMap.get("patOutSickroom"));//出院病房
            basinfoMap.put("ipt_days",patientMainInfoMap.get("patInDayCount"));//住院天数
            basinfoMap.put("drug_dicm_flag",treatmentsMap.get("hyper"));//药物过敏标志
            basinfoMap.put("dicm_drug_name",treatmentsMap.get("hyperMedicine"));//过敏药物名称
            basinfoMap.put("die_autp_flag","");//死亡患者尸检标志
            basinfoMap.put("abo_code","");//ABO血型代码
            basinfoMap.put("abo_name","");//ABO血型名称
            basinfoMap.put("rh_code",treatmentsMap.get("rh"));//Rh血型代码
            basinfoMap.put("rh_name",treatmentsMap.get("rhCode"));//RH血型
            basinfoMap.put("die_flag","");//死亡标志
            basinfoMap.put("deptdrt_name",doctorSignMap.get("deptHead"));//科主任姓名
            basinfoMap.put("deptdrt_code",doctorSignMap.get("deptHeadId"));//科主任代码
            basinfoMap.put("chfdr_name",doctorSignMap.get("deputyHead"));//主任( 副主任)医师姓名
            basinfoMap.put("chfdr_code",doctorSignMap.get("deputyHeadId"));//主任( 副主任)医师代码
            basinfoMap.put("atddr_name",doctorSignMap.get("mainDoctor"));//主治医生姓名
            basinfoMap.put("atddr_code",doctorSignMap.get("mainDoctorId"));//主治医生代码
            basinfoMap.put("chfpdr_name",doctorSignMap.get("mainDoctor"));//主诊医师姓名
            basinfoMap.put("chfpdr_code",doctorSignMap.get("mainDoctorId"));//主诊医师代码
            basinfoMap.put("ipt_dr_name",doctorSignMap.get("patInDoctor"));//住院医师姓名
            basinfoMap.put("ipt_dr_code",doctorSignMap.get("patInDoctorId"));//住院医师代码
            basinfoMap.put("resp_nurs_name",doctorSignMap.get("respNurse"));//责任护士姓名
            basinfoMap.put("resp_nurs_code",doctorSignMap.get("respNurseId"));//责任护士代码
            basinfoMap.put("train_dr_name",doctorSignMap.get("advancedDoctor"));//进修医师姓名
            basinfoMap.put("train_dr_code",doctorSignMap.get("advancedDoctorId"));//进修医师代码
            basinfoMap.put("intn_dr_name",doctorSignMap.get("studyDoctor"));//实习医师姓名
            basinfoMap.put("intn_dr_code",doctorSignMap.get("studyDoctorId"));//实习医师代码
            basinfoMap.put("codr_name",doctorSignMap.get("codePerson"));//编码员姓名
            basinfoMap.put("codr_code",doctorSignMap.get("codePersonId"));//编码员代码
            basinfoMap.put("qltctrl_dr_name",doctorSignMap.get("qualityDoctor"));//质控医师姓名
            basinfoMap.put("qltctrl_dr_code",doctorSignMap.get("qualityDoctorId"));//质控医师代码
            basinfoMap.put("qltctrl_nurs_name",doctorSignMap.get("qualityNurse"));//质控护士姓名
            basinfoMap.put("qltctrl_nurs_code",doctorSignMap.get("qualityNurseId"));//质控护士代码
            basinfoMap.put("medcas_qlt_name","");//病案质量名称
            basinfoMap.put("medcas_qlt_code","");//病案质量代码
            basinfoMap.put("qltctrl_date",treatmentsMap.get("qualityDate"));
            basinfoMap.put("dscg_way_name",treatmentsMap.get("patOutMethodName")); //离院方式名称
            basinfoMap.put("dscg_way",treatmentsMap.get("patOutMethod"));//离院方式
            basinfoMap.put("acp_medins_code","");//拟接收医疗机构代码
            basinfoMap.put("acp_medins_name","");//拟接收医疗机构名称
            basinfoMap.put("dscg_31days_rinp_flag",treatmentsMap.get("patInAgain"));//出院 31天内再住院计划标志
            basinfoMap.put("patInReason",treatmentsMap.get("patInAgain"));//出院31天内再住院目的
            basinfoMap.put("damg_intx_ext_rea",diagnosisInfosMap.get("toxicosisReason"));//损伤、中毒的外部原因
            basinfoMap.put("damg_intx_ext_rea_disecode",diagnosisInfosMap.get("toxicosisReasonCode"));//损伤、中毒的外部原因疾病编码
            // 拼接颅脑损伤患者昏迷时间入院前时间
            StringBuilder brn_damg_afadm_coma_dura = new StringBuilder();
            String beforeComaDays = treatmentsMap.get("beforeComaDays") != null ? String.valueOf(treatmentsMap.get("beforeComaDays")) : "0";
            String beforeComaHours = treatmentsMap.get("beforeComaHours") != null ? String.valueOf(treatmentsMap.get("beforeComaHours")) : "0";
            String beforeComaMinutes = treatmentsMap.get("beforeComaMinutes") != null ? String.valueOf(treatmentsMap.get("beforeComaMinutes")) : "0";
            brn_damg_afadm_coma_dura.append(beforeComaDays).append("/").append(beforeComaHours).append("/").append(beforeComaMinutes);
            basinfoMap.put("brn_damg_bfadm_coma_dura",brn_damg_afadm_coma_dura.toString());//颅脑损伤患者入院前昏迷时长
            // 拼接颅脑损伤患者昏迷时间入院后时间
            StringBuilder brn_damg_bfadm_coma_dura = new StringBuilder();
            String afterComaDays = treatmentsMap.get("afterComaDays") != null ? String.valueOf(treatmentsMap.get("afterComaDays")) : "0";
            String afterComaHours = treatmentsMap.get("afterComaHours") != null ? String.valueOf(treatmentsMap.get("afterComaHours")) : "0";
            String afterComaMinutes = treatmentsMap.get("afterComaMinutes") != null ? String.valueOf(treatmentsMap.get("afterComaMinutes")) : "0";
            brn_damg_bfadm_coma_dura.append(afterComaDays).append("/").append(afterComaHours).append("/").append(afterComaMinutes);
            basinfoMap.put("brn_damg_afadm_coma_dura",brn_damg_bfadm_coma_dura.toString());//颅脑损伤患者入院后昏迷时长
            //呼吸机使用时长 vent_used_dura
            StringBuilder vent_used_dura = new StringBuilder();
            String respiratorUseTimeDay = treatmentsMap.get("respiratorUseTimeDay") != null ? String.valueOf(treatmentsMap.get("respiratorUseTimeDay")) : "0";
            String respiratorUseTimeHour = treatmentsMap.get("respiratorUseTimeHour") != null ? String.valueOf(treatmentsMap.get("respiratorUseTimeHour")) : "0";
            String respiratorUseTimeMin = treatmentsMap.get("respiratorUseTimeMin") != null ? String.valueOf(treatmentsMap.get("respiratorUseTimeMin")) : "0";
            vent_used_dura.append(respiratorUseTimeDay).append("/").append(respiratorUseTimeHour).append("/").append(respiratorUseTimeMin);
            basinfoMap.put("vent_used_dura",vent_used_dura.toString());//呼吸机使用时长
            basinfoMap.put("cnfm_date",treatmentsMap.get("mainDiagnosisDate"));//确诊日期
            basinfoMap.put("patn_dise_diag_crsp","");//患者疾病诊断对照
            basinfoMap.put("patn_dise_diag_crsp_code","");//住院患者疾病诊断对照代码
            basinfoMap.put("ipt_patn_diag_inscp","");//住院患者诊断符合情况
            basinfoMap.put("ipt_patn_diag_inscp_code","");//住院患者诊断符合情况代码
            basinfoMap.put("dscg_trt_rslt","");//出院治疗结果
            basinfoMap.put("dscg_trt_rslt_code","");//出院治疗结果代码
            basinfoMap.put("medins_orgcode","10259001");//医疗机构组织机构代码
            basinfoMap.put("aise","");//过敏源
            basinfoMap.put("pote_intn_dr_name","");//研究生实习医师姓名
            basinfoMap.put("hbsag",treatmentsMap.get("hbsAg"));//乙肝表面抗原（HBsAg）
            basinfoMap.put("hcvab",treatmentsMap.get("hcvAb"));//丙型肝炎抗体（HCV-Ab）
            basinfoMap.put("hivab",treatmentsMap.get("hivAb"));//艾滋病毒抗体（hiv-ab）
            String resc_cnt = treatmentsMap.get("saveCount").isEmpty() ? null : treatmentsMap.get("saveCount");
            basinfoMap.put("resc_cnt",resc_cnt);//抢救次数
            String resc_succ_cnt = treatmentsMap.get("saveSuccessCount").isEmpty() ? null : treatmentsMap.get("saveSuccessCount");
            basinfoMap.put("resc_succ_cnt",resc_succ_cnt);//抢救成功次数
            basinfoMap.put("hosp_dise_fsttime","");//手术、治疗、检查、诊断为本院第一例
            basinfoMap.put("hif_pay_way_name","");//医保基金付费方式名称
            basinfoMap.put("hif_pay_way_code","");//医保基金付费方式代码
            basinfoMap.put("med_fee_paymtd_name",patientMainInfoMap.get("payMethod"));//医疗费用支付方式名称
            String payMethodId = patientMainInfoMap.get("payMethodId");
            String medfee_paymtd_code;

            switch (payMethodId) {
                case "1.1":
                    medfee_paymtd_code = "01";
                    break;
                case "2.1":
                    medfee_paymtd_code = "02";
                    break;
                case "4" :
                    medfee_paymtd_code = "04";
                    break;
                case "5" :
                    medfee_paymtd_code = "05";
                    break;
                case "6" :
                    medfee_paymtd_code = "06";
                    break;
                case "7" :
                    medfee_paymtd_code = "07";
                    break;
                case "9" :
                    medfee_paymtd_code = "99";
                    break;
                case "3.1" :
                    medfee_paymtd_code = "03";
                default:
                    medfee_paymtd_code = "99";
                    break;
            }

            basinfoMap.put("medfee_paymtd_code",medfee_paymtd_code);//医疗费用支付方式代码
            basinfoMap.put("selfpay_amt",costInfoMap.get("selfCost"));//自付金额
            basinfoMap.put("medfee_sumamt",costInfoMap.get("totalCost"));//医疗费总额
            basinfoMap.put("ordn_med_servfee",costInfoMap.get("generalMedical"));//一般医疗服务费
            basinfoMap.put("ordn_trt_oprt_fee",costInfoMap.get("generalTreatment"));//一般治疗操作费
            basinfoMap.put("nurs_fee",costInfoMap.get("nursingExpenses"));//护理费
            basinfoMap.put("com_med_serv_oth_fee","0");//综合医疗服务类其他费用
            basinfoMap.put("palg_diag_fee",costInfoMap.get("pathologicalCost"));//病理诊断费
            basinfoMap.put("lab_diag_fee",costInfoMap.get("laboratoryCost"));//实验室诊断费
            basinfoMap.put("rdhy_diag_fee",costInfoMap.get("pacsCost"));//影像学诊断费
            basinfoMap.put("clnc_dise_fee",costInfoMap.get("clinicalDiagnosisCost"));//临床诊断项目费
            basinfoMap.put("nsrgtrt_item_fee",costInfoMap.get("nonsurgicalTreatment"));//非手术治疗项目费
            basinfoMap.put("clnc_phys_trt_fee",costInfoMap.get("clinicalPhysicalFee"));//临床物理治疗费
            basinfoMap.put("rgtrt_trt_fee",costInfoMap.get("operationTreatmentCost"));//手术治疗费
            basinfoMap.put("anst_fee",costInfoMap.get("anesExpenses"));//麻醉费
            basinfoMap.put("oprn_fee",costInfoMap.get("operationCost"));//手术费
            basinfoMap.put("rhab_fee",costInfoMap.get("rehabilitationCost"));//康复费
            basinfoMap.put("tcm_trt_fee",costInfoMap.get("tcmTreatment"));//中医治疗费
            basinfoMap.put("wmfee",costInfoMap.get("westernMedicineFee"));//西药费
            basinfoMap.put("abtl_medn_fee",costInfoMap.get("antibioticsCost"));//抗菌药物费用
            basinfoMap.put("tcmpat_fee",costInfoMap.get("tcmHerbalFee"));//中成药费
            basinfoMap.put("tcmherb_fee","0");//中药饮片费
            basinfoMap.put("blo_fee",costInfoMap.get("bloodCost"));//血费
            basinfoMap.put("albu_fee",costInfoMap.get("albumin"));//白蛋白类制品费
            basinfoMap.put("glon_fee",costInfoMap.get("globulin"));//球蛋白类制品费
            basinfoMap.put("clotfac_fee",costInfoMap.get("coagulationFactor"));//凝血因子类制品费
            basinfoMap.put("cyki_fee",costInfoMap.get("cytokine"));//细胞因子类制品费
            basinfoMap.put("exam_dspo_matl_fee",costInfoMap.get("checkMaterials"));//检查用一次性医用材料费
            basinfoMap.put("trt_dspo_matl_fee",costInfoMap.get("medicalMaterials"));//治疗用一次性医用材料费
            basinfoMap.put("oprn_dspo_matl_fee",costInfoMap.get("operationMaterials"));//手术用一次性医用材料费
            basinfoMap.put("oth_fee",costInfoMap.get("other"));//其他费
            basinfoMap.put("vali_flag","1");//有效标志
            basinfoMap.put("fixmedins_code","H42030401471");//定点医药机构编号
            basinfoMap.put("offsite_med_treat","");//异地就医标志
            basinfoMap.put("pre_exam","0");//院前检查费
            basinfoMap.put("patn_rlts","");//与患者关系
            String nwb_adm_type = "5".equals(otherInfoMap.get("babyPatInWay")) ? "9":otherInfoMap.get("c");
            basinfoMap.put("nwb_adm_type",nwb_adm_type);//新生儿入院类型
            basinfoMap.put("mul_nwb_adm_wt","");//多新生儿入院体重
            basinfoMap.put("opsp_diag_caty","");//门诊慢特病诊断科别
            basinfoMap.put("opsp_mdtrt_date","");//门诊慢特病就诊日期
            basinfoMap.put("spga_nurscare_days",treatmentsMap.get("tjhlT")); //特级护理天数
            basinfoMap.put("lv1_nurscare_days",treatmentsMap.get("yjhlT")); //一级护理天数
            basinfoMap.put("scd_nurscare_days","");//二级护理天数
            basinfoMap.put("lv3_nurscare_days",treatmentsMap.get("sjhlT")); //三级护理天数
            basinfoMap.put("otp_wm_diag",diagnosisInfosMap.get("xyMzzd")); //门（急）诊西医诊断
            basinfoMap.put("otp_wm_diag_dise_code",diagnosisInfosMap.get("xyMzzdCode")); //西医诊断疾病代码
            basinfoMap.put("otp_tcm_diag",diagnosisInfosMap.get("zyMzzd")); //门（急）诊中医诊断
            basinfoMap.put("zyMzzdCode",diagnosisInfosMap.get("zyMzzdCode")); //中医诊断代码
            basinfoMap.put("bld_cat","");//输血品种
            basinfoMap.put("bld_amt","");//输血量
            basinfoMap.put("bld_unt","");//输血计量单位
            basinfoMap.put("extract_flag","0");//抽取标识


                //处理诊断信息
                for (int j = 0; j < leaveDiagnosisListMap.size(); j++) {
                    Map<String,String> leaveDiagnosisMap = leaveDiagnosisListMap.get(j); // 原始诊断字段
                    // 判断诊断类型isChineseDiagnosis 原始数据 1:代表是中医，2:代表是西医
                    if (("1").equals(leaveDiagnosisMap.get("diagnosisCode"))){
                        continue;
                    }

                    HashMap<String, String> diagInfoMap = new HashMap<>();

                    diagInfoMap.put("unique_id",unique_id.toString());//唯一ID
                    diagInfoMap.put("palg_no","");//病理号
                    diagInfoMap.put("ipt_patn_disediag_type_code","");//住院患者疾病诊断类型代码
                    diagInfoMap.put("disediag_type","");//疾病诊断类型
                    // 处理主诊断标识 原始1:代表主要诊断，2:代表其他诊断
                    String diagType = leaveDiagnosisMap.get("isMainDiagnosis");
                    String maindiag_flag =  ("1".equals(diagType)) ? "1":"0";
                    diagInfoMap.put("maindiag_flag",maindiag_flag); // 主诊断标识
                    diagInfoMap.put("diag_code",leaveDiagnosisMap.get("diagnosisCode"));//诊断代码
                    diagInfoMap.put("diag_name",leaveDiagnosisMap.get("diagnosisName"));//诊断名称
                    diagInfoMap.put("inhosp_diag_code",leaveDiagnosisMap.get("diagnosisCode"));//院内诊断代码
                    diagInfoMap.put("inhosp_diag_name",leaveDiagnosisMap.get("diagnosisName"));//院内诊断名称
                    diagInfoMap.put("adm_dise_cond_name","");//入院疾病病情名称
                    diagInfoMap.put("adm_dise_cond_code","");//入院疾病病情代码
                    diagInfoMap.put("adm_cond",leaveDiagnosisMap.get("inSituation"));//入院病情
                    diagInfoMap.put("adm_cond_code","");//入院时病情代码
                    diagInfoMap.put("high_diag_evid","");//最高诊断依据
                    diagInfoMap.put("bkup_deg","");//分化程度
                    diagInfoMap.put("bkup_deg_code","");//分化程度代码
                    diagInfoMap.put("vali_flag","1");//有效标志
                    diagInfoMap.put("ipt_medcas_hmpg_sn","");//住院病案首页流水号
                    diagInfoMap.put("mdtrt_sn","");//就医流水号
                    diagInfoMap.put("fixmedins_code","");//定点医药机构编号
                    if(isMapValid(diagInfoMap)){
                        diagInfoListMap.add(diagInfoMap);}

                }
                //处理icu信息
                if(icuInfosListMap != null && !icuInfosListMap.isEmpty()) {
                    for (int k = 0; k < icuInfosListMap.size(); k++) {
                        HashMap<String, String> icuinfo = new HashMap<>();
                        Map<String, String> icuInfoMap = icuInfosListMap.get(k);
                        icuinfo.put("unique_id", unique_id.toString()); // 唯一id
                        String icuRoom = icuInfoMap.get("icuRoom");
                        String scs_cutd_ward_type;
                        switch (icuRoom) {
                            case "CUU":
                                scs_cutd_ward_type = "1";
                                break;
                            case "NICU":
                                scs_cutd_ward_type = "2";
                                break;
                            case "ECU":
                                scs_cutd_ward_type = "3";
                                break;
                            case "SICU":
                                scs_cutd_ward_type = "4";
                                break;
                            case "PICU":
                                scs_cutd_ward_type = "5";
                                break;
                            case "RICU":
                                scs_cutd_ward_type = "6";
                                break;
                            case "ICU":
                                scs_cutd_ward_type = "7";
                                break;
                            case "其他":
                                scs_cutd_ward_type = "9";
                                break;
                            default:
                                scs_cutd_ward_type = "";
                        }
                        icuinfo.put("scs_cutd_ward_type", scs_cutd_ward_type); // 重症监护病房类型
                        icuinfo.put("scs_cutd_inpool_time", icuInfoMap.get("enterIcuRoomTime")); // 重症监护进入时间
                        icuinfo.put("scs_cutd_exit_time", icuInfoMap.get("backIcuRoomTime")); // 重症监护退出时间

                        String totalTime = String.valueOf(icuInfoMap.get("totalTime"));
                        int days = 0;
                        int hours = 0;
                        int minutes = 0;
                        if (totalTime != null && !totalTime.isEmpty()) {
                            try {
                                int totalTimes = Integer.parseInt(totalTime);
                                days = totalTimes / 24; // 计算天数
                                hours = totalTimes % 24; // 剩余的小时数
                            } catch (NumberFormatException e) {
                                // 处理解析错误，例如记录日志或设置默认值
                                System.err.println("Invalid totalTime format: " + totalTime);
                            }
                        }
                        StringBuilder scs_cutd_sum_dura_StringBuilder = new StringBuilder();
                        StringBuilder scs_cutd_sum_dura = scs_cutd_sum_dura_StringBuilder.append(days).append("/").append(hours).append("/").append(minutes);
                        icuinfo.put("scs_cutd_sum_dura", scs_cutd_sum_dura.toString()); // 重症监护合计时长

                        if (isMapValid(icuinfo)){
                            icuInfoListMap.add(icuinfo);}
                    }
                }
                //处理手术操作信息
                for (int o = 0; o < surgeryInfosListMap.size(); o++) {
                    HashMap<String, String> oprnInfo = new HashMap<>();
                    Map<String, String> surgeryInfostMap = surgeryInfosListMap.get(o);
                    oprnInfo.put("unique_id",unique_id.toString());//唯一ID
                    oprnInfo.put("oprn_oprt_date",surgeryInfostMap.get("operationDate"));//手术操作日期
                    oprnInfo.put("oprn_oprt_name",surgeryInfostMap.get("operationName"));//手术操作名称
                    oprnInfo.put("oprn_oprt_code",surgeryInfostMap.get("operationCode"));//手术操作代码
                    oprnInfo.put("oprn_oprt_sn",surgeryInfostMap.get("orderNo"));//手术操作序列号
                    oprnInfo.put("oprn_lv_code",surgeryInfostMap.get("operationLevel"));//手术级别代码
                    oprnInfo.put("oprn_lv_name","");//手术级别名称
                    oprnInfo.put("oper_name",surgeryInfostMap.get("operator"));//手术者姓名
                    oprnInfo.put("asit1_name",surgeryInfostMap.get("firstAssistant"));//助手Ⅰ姓名
                    oprnInfo.put("asit2_name",surgeryInfostMap.get("firstAssistant"));//助手Ⅱ姓名
                    oprnInfo.put("sinc_heal_lv",surgeryInfostMap.get("healLevel"));//手术切口愈合
                    oprnInfo.put("sinc_heal_lv_code",surgeryInfostMap.get("healLevel"));//手术切口愈合等级代码
                    oprnInfo.put("anst_mtd_name","");//麻醉-方法名称
                    oprnInfo.put("anst_mtd_code",surgeryInfostMap.get("firstAssistant"));//麻醉-方法代码
                    oprnInfo.put("anst_dr_name",surgeryInfostMap.get("hocusDoctor"));//麻醉医师姓名
                    oprnInfo.put("anst_dr_code",surgeryInfostMap.get("hocusDoctorId"));//麻醉医师代码
                    oprnInfo.put("oprn_oper_part",surgeryInfostMap.get("hocusDoctorId"));//手术操作部位
                    oprnInfo.put("oprn_oper_part_code","");//手术操作部位
                    oprnInfo.put("oprn_con_time",surgeryInfostMap.get("operateDuration"));//手术持续时间
                    oprnInfo.put("anst_lv_name","");//麻醉分级名称
                    oprnInfo.put("anst_lv_code","");//麻醉分级代码
                    oprnInfo.put("oprn_patn_type","");//手术患者类型
                    oprnInfo.put("oprn_patn_type_code","");//手术患者类型代码
                    oprnInfo.put("main_oprn_flag","");//主要手术标志
                    oprnInfo.put("anst_asa_lv_code","");//麻醉ASA分级代码
                    oprnInfo.put("anst_asa_lv_name","");//麻醉ASA分级名称
                    oprnInfo.put("anst_medn_code","");//麻醉药物代码
                    oprnInfo.put("anst_medn_name","");//麻醉药物名称
                    oprnInfo.put("anst_medn_dos","");//麻醉药物剂量
                    oprnInfo.put("unt","");//计量单位
                    oprnInfo.put("anst_begntime",null);//麻醉开始时间
                    oprnInfo.put("anst_endtime",null);//麻醉结束时间
                    oprnInfo.put("anst_copn_code","");//麻醉合并症代码
                    oprnInfo.put("anst_copn_name","");//麻醉合并症名称
                    oprnInfo.put("anst_copn_dscr","");//麻醉合并症描述
                    oprnInfo.put("pacu_begntime",null);//复苏室开始时间
                    oprnInfo.put("pacu_endtime",null);//复苏室结束时间
                    oprnInfo.put("canc_oprn_flag","");//取消手术标志
                    oprnInfo.put("vali_flag","1");//有效标志
                    oprnInfo.put("ipt_medcas_hmpg_sn","");//住院病案首页流水号
                    oprnInfo.put("mdtrt_sn","");//住院病案首页流水号
                    oprnInfo.put("oprn_oprt_begntime",surgeryInfostMap.get("operationDate"));//手术操作开始时间
                    String oprn_oprt_endtime = null;
/*                if (surgeryInfostMap.get("operationDate") != null && surgeryInfostMap.get("operationDate").isEmpty() &&  surgeryInfostMap.get("operateDuration") != null && surgeryInfostMap.get("operateDuration").isEmpty()) {
                    oprn_oprt_endtime = addMinutes(surgeryInfostMap.get("operationDate"), surgeryInfostMap.get("operateDuration"));
                }*/
                    oprnInfo.put("oprn_oprt_endtime",oprn_oprt_endtime);//手术操作结束时间

                    if(isMapValid(oprnInfo)){
                        oprnInfoListMap.add(oprnInfo);}
                }

            //单个病人同步
            if(zhongYangApiDto.getUniqueId() != null && basinfoMap.get("unique_id").equals(zhongYangApiDto.getUniqueId())) {

                ModifyBusSettleListDto modifyBusSettleListDto = new ModifyBusSettleListDto();
                modifyBusSettleListDto.setK00(zhongYangApiDto.getUniqueId());
                int num = settleListManageDao.queryUploadState(modifyBusSettleListDto);
                if (num > 0){
                    throw new RuntimeException("该病例已经上传不能同步");
                }
                zhongYangApiMapper.insertBaseInfo(basinfoMap);
                for (int o = 0; o < diagInfoListMap.size(); o++) {
                    Map<String, String> diagInfoMap = diagInfoListMap.get(o);
                    diagInfoMap.put("di20_id", basinfoMap.get("id"));
                }
                zhongYangApiMapper.insertDiagInfo(diagInfoListMap);

                for (Map<String, String> icuInfoMap : icuInfoListMap) {
                    icuInfoMap.put("di20_id", basinfoMap.get("id"));
                }
                if (icuInfoListMap != null && !icuInfoListMap.isEmpty()) {
                    zhongYangApiMapper.insertIcuInfo(icuInfoListMap);
                }

                for (Map<String, String> oprnInfo : oprnInfoListMap) {
                    oprnInfo.put("di20_id", basinfoMap.get("id"));
                }
                if (oprnInfoListMap != null && !oprnInfoListMap.isEmpty()) {
                    zhongYangApiMapper.insertOprnInfo(oprnInfoListMap);
                }
                //批量抽取
            } else if(zhongYangApiDto.getUniqueId() == null){
                zhongYangApiMapper.insertBaseInfo(basinfoMap);
                for (int o = 0; o < diagInfoListMap.size(); o++) {
                    Map<String, String> diagInfoMap = diagInfoListMap.get(o);
                    diagInfoMap.put("di20_id", basinfoMap.get("id"));
                }
                zhongYangApiMapper.insertDiagInfo(diagInfoListMap);

                for (Map<String, String> icuInfoMap : icuInfoListMap) {
                    icuInfoMap.put("di20_id", basinfoMap.get("id"));
                }
                if (icuInfoListMap != null && !icuInfoListMap.isEmpty()) {
                    zhongYangApiMapper.insertIcuInfo(icuInfoListMap);
                }

                for (Map<String, String> oprnInfo : oprnInfoListMap) {
                    oprnInfo.put("di20_id", basinfoMap.get("id"));
                }
                if (oprnInfoListMap != null && !oprnInfoListMap.isEmpty()) {
                    zhongYangApiMapper.insertOprnInfo(oprnInfoListMap);
                }
            }

        }
        return total;
    }

    public  void mdcsInfoExtractByApai (ZhongYangApiDto zhongYangApiDto){
        //组装url
        StringBuilder url_2_23_4 = new StringBuilder(API_2_23_4_URL);
//        url_2_23_4.append("/");
//        url_2_23_4.append(zhongYangApiDto.getPat_out_time_start());
//        url_2_23_4.append("/");
//        url_2_23_4.append(zhongYangApiDto.getPat_out_time_end());
        url_2_23_4.append("/");
        url_2_23_4.append(zhongYangApiDto.getPatInHosId());
        url_2_23_4.append("?");
        url_2_23_4.append("data_source");
        url_2_23_4.append("=");
        url_2_23_4.append(zhongYangApiDto.getData_source());
        Map<String, String> getReqParamsMap = new TreeMap<>(Comparator.naturalOrder());
        getReqParamsMap.put("data_source",zhongYangApiDto.getData_source()+"");
        //生成代签名参数
        Map<String, String> signStrMAp = getSignStr(getReqParamsMap);
        String sign = "";
        HashMap<String, HashMap<String, Object>> stringHashMapHashMap = new HashMap<>();
        //生成签名
        try {
            sign = getSign(signStrMAp.get("sortedParamsStr"), APPSECRET);
            logger.info("调用2_23_4接口生成的sing:{}",sign);
            logger.info("调用2_23_4接口生成的时间戳timestamp：{}",signStrMAp.get("timestamp"));
        } catch (Exception e){
            e.printStackTrace();
            logger.info("2_23_4接口生成sing失败");
        }
        //生成请求头
        Map<String, String> headersMap = getHeaders(sign, signStrMAp.get("timestamp"));
//        List<Map> listMap = new ArrayList<>();
        //单个data
        Map<String,String> dataMap = new HashMap<>();


        try {
            //创建请求
            URL url = new URL(url_2_23_4.toString());
            HttpURLConnection connection  = (HttpURLConnection)url.openConnection();
            connection.setRequestMethod("GET");
            //添加请求头
            for(Map.Entry<String,String> map : headersMap.entrySet() ){
                connection.setRequestProperty(map.getKey(),map.getValue());
            }

            // 获取响应
            int responseCode = connection.getResponseCode();
            System.out.println("Response Code: " + responseCode);

            BufferedReader in = new BufferedReader(new InputStreamReader(connection.getInputStream()));
            String inputLine;
            StringBuilder response = new StringBuilder();
            while ((inputLine = in.readLine()) != null) {
                response.append(inputLine);
            }

            JSONObject responseData = JSONUtil.parseObj(response);
            Object data = responseData.get("data");
            String dataString = JsonUtil.toJsonString(data);

            dataMap = JSON.parseObject( dataString,
                    new TypeReference<Map<String, String>>() {}
            );
            logger.info("拿到的信息：{}",response);

        }catch (Exception e){
            e.printStackTrace();
            log.info("2_23_4接口调用失败");
        }




        //存放基本信息节点数据
        HashMap<String, String> basinfoMap = new HashMap<>();
        //存放出院诊断信息节点数据
        ArrayList<Map<String, String>> diagInfoListMap = new ArrayList<>();
        // 存放icu息节点数据
        ArrayList<Map<String, String>> icuInfoListMap = new ArrayList<>();
        //存放手术操作节点信息
        ArrayList<Map<String, String>> oprnInfoListMap = new ArrayList<>();
        //存放基金支付节点信息
        HashMap<String, String> payInfoListMap = new HashMap<>();
        //存放收费项目节点信息
        HashMap<String, String> itemInfoMap = new HashMap<>();
        //set_info表
        HashMap<String,String> setInfoMap = new HashMap<>();

        Map map = dataMap;


        //获取病人主信息
        Map<String,String> patientMainInfoMap = JSON.parseObject(map.get("patientMainInfo").toString(), Map.class);
        //获取获取原始诊疗信息treatments节点patientMainInfo
        Map<String,String> patientBaseInfoMap = JSON.parseObject(map.get("patientBaseInfo").toString(), Map.class);
        //获取原始诊疗信息
        Map<String,String> treatmentsMap = JSON.parseObject(map.get("treatments").toString(), Map.class);
        //获取原始入院诊断信息
        Map<String,String> diagnosisInfosMap = JSON.parseObject(map.get("diagnosisInfos").toString(), Map.class);
        //获取原始出院诊断信息
        List<Map> leaveDiagnosisListMap = JSON.parseArray(map.get("leaveDiagnosis").toString(), Map.class);
        //获取原始手术信息
        List<Map> surgeryInfosListMap = JSON.parseArray(map.get("surgeryInfos").toString(), Map.class);
        //获取原始icu信息
        List<Map> icuInfosListMap = JSON.parseArray(map.get("icuInfos").toString(), Map.class);
        //获取原始费用信息
        Map<String,String> costInfoMap = JSON.parseObject(map.get("costInfo").toString(), Map.class);
        //获取原始签名信息
        Map<String,String> doctorSignMap = JSON.parseObject(map.get("doctorSign").toString(), Map.class);
        //获取原始其他信息
        Map<String,String> otherInfoMap = JSON.parseObject(map.get("otherInfo").toString(), Map.class);


        //处理基本信息
        StringBuilder unique_id = new StringBuilder();
        String medicalRecordCode = String.valueOf(patientMainInfoMap.get("medicalRecordCode"));
        String patInCount = String.valueOf(patientMainInfoMap.get("patInCount"));



        unique_id.append(medicalRecordCode);
        if (patInCount != null && !patInCount.trim().isEmpty()){
            unique_id.append(patInCount);
        }



        basinfoMap.put("unique_id",unique_id.toString()); //唯一ID
        basinfoMap.put("mdtrt_sn","");//就医流水号
        basinfoMap.put("mdtrt_id",patientMainInfoMap.get("patInHosId"));//就诊ID
        basinfoMap.put("psn_no",patientMainInfoMap.get("patId"));//人员编号
        basinfoMap.put("patn_ipt_cnt",patientMainInfoMap.get("patInCount"));//患者住院次数
        basinfoMap.put("ipt_no",patientMainInfoMap.get("patInHosCode"));//住院号
        basinfoMap.put("medcasno",patientMainInfoMap.get("medicalRecordCode"));//病案号
        basinfoMap.put("psn_name",patientMainInfoMap.get("patName"));//人员姓名
        basinfoMap.put("gend",patientMainInfoMap.get("sexCode"));//性别
        basinfoMap.put("brdy",patientMainInfoMap.get("birth"));//出生日期
        basinfoMap.put("age",patientMainInfoMap.get("patAgeYear"));//年龄
        basinfoMap.put("ntly",patientBaseInfoMap.get("countryCode"));//国籍
        //处理年龄不足一岁
        int patAgeDay = Integer.parseInt(patientMainInfoMap.get("patAgeDay"));
        int patAgeMonth = Integer.parseInt(patientMainInfoMap.get("patAgeMonth"));
        Integer nwb_age = patAgeMonth * 30 + patAgeDay;
        nwb_age = (nwb_age == 0) ? null : nwb_age;
        if (nwb_age != null) {
            basinfoMap.put("nwb_age", nwb_age.toString()); // 年龄不足1周岁
        } else {
            basinfoMap.put("nwb_age", null); // 处理 null 情况
        }
        //处理多新生儿出生体重
        StringBuilder mul_nwb_bir_wt = new StringBuilder();
        ArrayList<String> bir_wt_list = new ArrayList<>();
        String[] keys = {"babyBornWeight", "babyBornWeight2", "babyBornWeight3", "babyBornWeight4", "babyBornWeight5"};
        for (String key : keys) {
            String value = patientBaseInfoMap.get(key);
            if (value != null) {
                if (mul_nwb_bir_wt.length() > 0) {
                    mul_nwb_bir_wt.append(","); // 添加逗号分隔
                }
                bir_wt_list.add(value);
                mul_nwb_bir_wt.append(value);
            }
        }
        if (bir_wt_list.size() > 1){
            basinfoMap.put("mul_nwb_bir_wt",mul_nwb_bir_wt.toString()); //多新生儿出生体重
            basinfoMap.put("nwb_bir_wt",null); //新生儿出生体重
        } else {
            basinfoMap.put("nwb_bir_wt",mul_nwb_bir_wt.toString()); //新生儿出生体重
            basinfoMap.put("mul_nwb_bir_wt",""); //多新生儿出生体重
        }
        String nwb_adm_wt = patientBaseInfoMap.get("babyPatInWeight").isEmpty() ? null : patientBaseInfoMap.get("babyPatInWeight");
        basinfoMap.put("nwb_adm_wt",nwb_adm_wt);//新生儿入院体重
        basinfoMap.put("birplc",patientBaseInfoMap.get("bornDetailAddress"));//出生地

        //处理籍贯
        StringBuilder napl = new StringBuilder();
        napl.append(patientBaseInfoMap.get("hometownProvience")).append(patientBaseInfoMap.get("hometownCity"));
        basinfoMap.put("napl",napl.toString());//籍贯
        basinfoMap.put("naty",patientBaseInfoMap.get("nationCode"));//民族
        basinfoMap.put("certno",patientMainInfoMap.get("cardNum"));//证件号码
        basinfoMap.put("prfs",patientMainInfoMap.get("jobCode"));//职业
        basinfoMap.put("mrg_stas","");//婚姻状态
        basinfoMap.put("curr_addr_poscode",patientBaseInfoMap.get("addressZip"));//现住址-邮政编码
        basinfoMap.put("curr_addr",patientBaseInfoMap.get("addressDetailAddress"));//现住址
        basinfoMap.put("psn_tel",patientBaseInfoMap.get("addressPhone"));//个人联系电话
        basinfoMap.put("resd_addr_prov",patientBaseInfoMap.get("residenceProvience"));//户口地址-省（自治区、直辖市）
        basinfoMap.put("resd_addr_city",patientBaseInfoMap.get("residenceCity"));//户口地址-市（地区）
        basinfoMap.put("resd_addr_coty",patientBaseInfoMap.get("residenceTown"));//户口地址-县（区）
        basinfoMap.put("resd_addr_subd","");//户口地址-乡（镇、街道办事处）
        basinfoMap.put("resd_addr_vil","");//户口地址-村（街、路、弄等）
        basinfoMap.put("resd_addr_housnum","");//户口地址-门牌号码
        basinfoMap.put("resd_addr_poscode",patientBaseInfoMap.get("residenceZip"));//户口地址- 邮政编码
        basinfoMap.put("resd_addr",patientBaseInfoMap.get("residenceDetailAddress"));//户口地址
        basinfoMap.put("empr_tel",patientBaseInfoMap.get("jobPhone"));//工作单位联系电话
        basinfoMap.put("empr_poscode",patientBaseInfoMap.get("jobZip"));//工作单位- 邮政编码
        StringBuilder empr_addr = new StringBuilder();
        empr_addr.append(patientBaseInfoMap.get("jobCompany")).append(patientBaseInfoMap.get("jobDetailAddress"));
        basinfoMap.put("empr_addr",empr_addr.toString());//工作单位及地址
        basinfoMap.put("coner_tel",patientBaseInfoMap.get("contactPhone"));//联系人电话
        basinfoMap.put("coner_name",patientMainInfoMap.get("contact"));//联系人姓名
        basinfoMap.put("coner_addr",patientBaseInfoMap.get("contactDetailAddress"));//联系人地址
        basinfoMap.put("coner_rlts_code",patientMainInfoMap.get("contactRelationshipCode"));//与联系人关系代码
        basinfoMap.put("adm_way_name",patientMainInfoMap.get("patInWay"));//入院途径名称
        basinfoMap.put("adm_way_code",patientMainInfoMap.get("patInWayCode"));//入院途径代码

        String trt_type_name = "" ;
        String trt_type = "";
        String treatmentCategory = treatmentsMap.get("treatmentCategory");
        switch (treatmentCategory){
            case "1" :
                trt_type_name = "中医";
                trt_type = "2.1";
                break;
            case "1.1" :
                trt_type_name = "中医";
                trt_type = "2.1";
                break;
            case "1.2" :
                trt_type_name = "民族医";
                trt_type = "2.2";
                break;
            case "2" :
                trt_type_name = "中西医";
                trt_type = "3";
                break;
            case "3" :
                trt_type_name = "西医";
                trt_type = "1";
                break;
        }
        basinfoMap.put("trt_type_name",trt_type_name);//治疗类别名称
        basinfoMap.put("trt_type",trt_type);//治疗类别
        basinfoMap.put("adm_caty",patientMainInfoMap.get("patInDeptId"));//入院科别
        basinfoMap.put("adm_ward",patientMainInfoMap.get("patInSickroom"));//入院病房
        basinfoMap.put("adm_date",patientMainInfoMap.get("patInTime"));//入院日期
        basinfoMap.put("dscg_date",patientMainInfoMap.get("patOutTime"));//出院日期
        basinfoMap.put("dscg_caty",patientMainInfoMap.get("patOutDeptId"));//出院科别
        basinfoMap.put("refldept_dept",patientMainInfoMap.get("patChangeDeptId"));//转科科别
        basinfoMap.put("dscg_ward",patientMainInfoMap.get("patOutSickroom"));//出院病房
        basinfoMap.put("ipt_days",patientMainInfoMap.get("patInDayCount"));//住院天数
        basinfoMap.put("drug_dicm_flag",treatmentsMap.get("hyper"));//药物过敏标志
        basinfoMap.put("dicm_drug_name",treatmentsMap.get("hyperMedicine"));//过敏药物名称
        basinfoMap.put("die_autp_flag","");//死亡患者尸检标志
        basinfoMap.put("abo_code","");//ABO血型代码
        basinfoMap.put("abo_name","");//ABO血型名称
        basinfoMap.put("rh_code",treatmentsMap.get("rh"));//Rh血型代码
        basinfoMap.put("rh_name",treatmentsMap.get("rhCode"));//RH血型
        basinfoMap.put("die_flag","");//死亡标志
        basinfoMap.put("deptdrt_name",doctorSignMap.get("deptHead"));//科主任姓名
        basinfoMap.put("deptdrt_code",doctorSignMap.get("deptHeadId"));//科主任代码
        basinfoMap.put("chfdr_name",doctorSignMap.get("deputyHead"));//主任( 副主任)医师姓名
        basinfoMap.put("chfdr_code",doctorSignMap.get("deputyHeadId"));//主任( 副主任)医师代码
        basinfoMap.put("atddr_name",doctorSignMap.get("mainDoctor"));//主治医生姓名
        basinfoMap.put("atddr_code",doctorSignMap.get("mainDoctorId"));//主治医生代码
        basinfoMap.put("chfpdr_name",doctorSignMap.get("mainDoctor"));//主诊医师姓名
        basinfoMap.put("chfpdr_code",doctorSignMap.get("mainDoctorId"));//主诊医师代码
        basinfoMap.put("ipt_dr_name",doctorSignMap.get("patInDoctor"));//住院医师姓名
        basinfoMap.put("ipt_dr_code",doctorSignMap.get("patInDoctorId"));//住院医师代码
        basinfoMap.put("resp_nurs_name",doctorSignMap.get("respNurse"));//责任护士姓名
        basinfoMap.put("resp_nurs_code",doctorSignMap.get("respNurseId"));//责任护士代码
        basinfoMap.put("train_dr_name",doctorSignMap.get("advancedDoctor"));//进修医师姓名
        basinfoMap.put("train_dr_code",doctorSignMap.get("advancedDoctorId"));//进修医师代码
        basinfoMap.put("intn_dr_name",doctorSignMap.get("studyDoctor"));//实习医师姓名
        basinfoMap.put("intn_dr_code",doctorSignMap.get("studyDoctorId"));//实习医师代码
        basinfoMap.put("codr_name",doctorSignMap.get("codePerson"));//编码员姓名
        basinfoMap.put("codr_code",doctorSignMap.get("codePersonId"));//编码员代码
        basinfoMap.put("qltctrl_dr_name",doctorSignMap.get("qualityDoctor"));//质控医师姓名
        basinfoMap.put("qltctrl_dr_code",doctorSignMap.get("qualityDoctorId"));//质控医师代码
        basinfoMap.put("qltctrl_nurs_name",doctorSignMap.get("qualityNurse"));//质控护士姓名
        basinfoMap.put("qltctrl_nurs_code",doctorSignMap.get("qualityNurseId"));//质控护士代码
        basinfoMap.put("medcas_qlt_name","");//病案质量名称
        basinfoMap.put("medcas_qlt_code","");//病案质量代码
        basinfoMap.put("qltctrl_date",treatmentsMap.get("qualityDate"));
        basinfoMap.put("dscg_way_name",treatmentsMap.get("patOutMethodName")); //离院方式名称
        basinfoMap.put("dscg_way",treatmentsMap.get("patOutMethod"));//离院方式
        basinfoMap.put("acp_medins_code","");//拟接收医疗机构代码
        basinfoMap.put("acp_medins_name","");//拟接收医疗机构名称
        basinfoMap.put("dscg_31days_rinp_flag",treatmentsMap.get("patInAgain"));//出院 31天内再住院计划标志
        basinfoMap.put("patInReason",treatmentsMap.get("patInAgain"));//出院31天内再住院目的
        basinfoMap.put("damg_intx_ext_rea",diagnosisInfosMap.get("toxicosisReason"));//损伤、中毒的外部原因
        basinfoMap.put("damg_intx_ext_rea_disecode",diagnosisInfosMap.get("toxicosisReasonCode"));//损伤、中毒的外部原因疾病编码
        // 拼接颅脑损伤患者昏迷时间入院前时间
        StringBuilder brn_damg_afadm_coma_dura = new StringBuilder();
        String beforeComaDays = treatmentsMap.get("beforeComaDays") != null ? String.valueOf(treatmentsMap.get("beforeComaDays")) : "0";
        String beforeComaHours = treatmentsMap.get("beforeComaHours") != null ? String.valueOf(treatmentsMap.get("beforeComaHours")) : "0";
        String beforeComaMinutes = treatmentsMap.get("beforeComaMinutes") != null ? String.valueOf(treatmentsMap.get("beforeComaMinutes")) : "0";
        brn_damg_afadm_coma_dura.append(beforeComaDays).append("/").append(beforeComaHours).append("/").append(beforeComaMinutes);
        basinfoMap.put("brn_damg_bfadm_coma_dura",brn_damg_afadm_coma_dura.toString());//颅脑损伤患者入院前昏迷时长
        // 拼接颅脑损伤患者昏迷时间入院后时间
        StringBuilder brn_damg_bfadm_coma_dura = new StringBuilder();
        String afterComaDays = treatmentsMap.get("afterComaDays") != null ? String.valueOf(treatmentsMap.get("afterComaDays")) : "0";
        String afterComaHours = treatmentsMap.get("afterComaHours") != null ? String.valueOf(treatmentsMap.get("afterComaHours")) : "0";
        String afterComaMinutes = treatmentsMap.get("afterComaMinutes") != null ? String.valueOf(treatmentsMap.get("afterComaMinutes")) : "0";
        brn_damg_bfadm_coma_dura.append(afterComaDays).append("/").append(afterComaHours).append("/").append(afterComaMinutes);
        basinfoMap.put("brn_damg_afadm_coma_dura",brn_damg_bfadm_coma_dura.toString());//颅脑损伤患者入院后昏迷时长
        //呼吸机使用时长 vent_used_dura
        StringBuilder vent_used_dura = new StringBuilder();
        String respiratorUseTimeDay = treatmentsMap.get("respiratorUseTimeDay") != null ? String.valueOf(treatmentsMap.get("respiratorUseTimeDay")) : "0";
        String respiratorUseTimeHour = treatmentsMap.get("respiratorUseTimeHour") != null ? String.valueOf(treatmentsMap.get("respiratorUseTimeHour")) : "0";
        String respiratorUseTimeMin = treatmentsMap.get("respiratorUseTimeMin") != null ? String.valueOf(treatmentsMap.get("respiratorUseTimeMin")) : "0";
        vent_used_dura.append(respiratorUseTimeDay).append("/").append(respiratorUseTimeHour).append("/").append(respiratorUseTimeMin);
        basinfoMap.put("vent_used_dura",vent_used_dura.toString());//呼吸机使用时长
        basinfoMap.put("cnfm_date",treatmentsMap.get("mainDiagnosisDate"));//确诊日期
        basinfoMap.put("patn_dise_diag_crsp","");//患者疾病诊断对照
        basinfoMap.put("patn_dise_diag_crsp_code","");//住院患者疾病诊断对照代码
        basinfoMap.put("ipt_patn_diag_inscp","");//住院患者诊断符合情况
        basinfoMap.put("ipt_patn_diag_inscp_code","");//住院患者诊断符合情况代码
        basinfoMap.put("dscg_trt_rslt","");//出院治疗结果
        basinfoMap.put("dscg_trt_rslt_code","");//出院治疗结果代码
        basinfoMap.put("medins_orgcode","10259001");//医疗机构组织机构代码
        basinfoMap.put("aise","");//过敏源
        basinfoMap.put("pote_intn_dr_name","");//研究生实习医师姓名
        basinfoMap.put("hbsag",treatmentsMap.get("hbsAg"));//乙肝表面抗原（HBsAg）
        basinfoMap.put("hcvab",treatmentsMap.get("hcvAb"));//丙型肝炎抗体（HCV-Ab）
        basinfoMap.put("hivab",treatmentsMap.get("hivAb"));//艾滋病毒抗体（hiv-ab）
        String resc_cnt = treatmentsMap.get("saveCount").isEmpty() ? null : treatmentsMap.get("saveCount");
        basinfoMap.put("resc_cnt",resc_cnt);//抢救次数
        String resc_succ_cnt = treatmentsMap.get("saveSuccessCount").isEmpty() ? null : treatmentsMap.get("saveSuccessCount");
        basinfoMap.put("resc_succ_cnt",resc_succ_cnt);//抢救成功次数
        basinfoMap.put("hosp_dise_fsttime","");//手术、治疗、检查、诊断为本院第一例
        basinfoMap.put("hif_pay_way_name","");//医保基金付费方式名称
        basinfoMap.put("hif_pay_way_code","");//医保基金付费方式代码
        basinfoMap.put("med_fee_paymtd_name",patientMainInfoMap.get("payMethod"));//医疗费用支付方式名称
        String payMethodId = patientMainInfoMap.get("payMethodId");
        String medfee_paymtd_code;

        switch (payMethodId) {
            case "1.1":
                medfee_paymtd_code = "01";
                break;
            case "2.1":
                medfee_paymtd_code = "02";
                break;
            case "4" :
                medfee_paymtd_code = "04";
                break;
            case "5" :
                medfee_paymtd_code = "05";
                break;
            case "6" :
                medfee_paymtd_code = "06";
                break;
            case "7" :
                medfee_paymtd_code = "07";
                break;
            case "9" :
                medfee_paymtd_code = "99";
                break;
            case "3.1" :
                medfee_paymtd_code = "03";
            default:
                medfee_paymtd_code = "99";
                break;
        }

        basinfoMap.put("medfee_paymtd_code",medfee_paymtd_code);//医疗费用支付方式代码
        basinfoMap.put("selfpay_amt",costInfoMap.get("selfCost"));//自付金额
        basinfoMap.put("medfee_sumamt",costInfoMap.get("totalCost"));//医疗费总额
        basinfoMap.put("ordn_med_servfee",costInfoMap.get("generalMedical"));//一般医疗服务费
        basinfoMap.put("ordn_trt_oprt_fee",costInfoMap.get("generalTreatment"));//一般治疗操作费
        basinfoMap.put("nurs_fee",costInfoMap.get("nursingExpenses"));//护理费
        basinfoMap.put("com_med_serv_oth_fee","0");//综合医疗服务类其他费用
        basinfoMap.put("palg_diag_fee",costInfoMap.get("pathologicalCost"));//病理诊断费
        basinfoMap.put("lab_diag_fee",costInfoMap.get("laboratoryCost"));//实验室诊断费
        basinfoMap.put("rdhy_diag_fee",costInfoMap.get("pacsCost"));//影像学诊断费
        basinfoMap.put("clnc_dise_fee",costInfoMap.get("clinicalDiagnosisCost"));//临床诊断项目费
        basinfoMap.put("nsrgtrt_item_fee",costInfoMap.get("nonsurgicalTreatment"));//非手术治疗项目费
        basinfoMap.put("clnc_phys_trt_fee",costInfoMap.get("clinicalPhysicalFee"));//临床物理治疗费
        basinfoMap.put("rgtrt_trt_fee",costInfoMap.get("operationTreatmentCost"));//手术治疗费
        basinfoMap.put("anst_fee",costInfoMap.get("anesExpenses"));//麻醉费
        basinfoMap.put("oprn_fee",costInfoMap.get("operationCost"));//手术费
        basinfoMap.put("rhab_fee",costInfoMap.get("rehabilitationCost"));//康复费
        basinfoMap.put("tcm_trt_fee",costInfoMap.get("tcmTreatment"));//中医治疗费
        basinfoMap.put("wmfee",costInfoMap.get("westernMedicineFee"));//西药费
        basinfoMap.put("abtl_medn_fee",costInfoMap.get("antibioticsCost"));//抗菌药物费用
        basinfoMap.put("tcmpat_fee",costInfoMap.get("tcmHerbalFee"));//中成药费
        basinfoMap.put("tcmherb_fee","0");//中药饮片费
        basinfoMap.put("blo_fee",costInfoMap.get("bloodCost"));//血费
        basinfoMap.put("albu_fee",costInfoMap.get("albumin"));//白蛋白类制品费
        basinfoMap.put("glon_fee",costInfoMap.get("globulin"));//球蛋白类制品费
        basinfoMap.put("clotfac_fee",costInfoMap.get("coagulationFactor"));//凝血因子类制品费
        basinfoMap.put("cyki_fee",costInfoMap.get("cytokine"));//细胞因子类制品费
        basinfoMap.put("exam_dspo_matl_fee",costInfoMap.get("checkMaterials"));//检查用一次性医用材料费
        basinfoMap.put("trt_dspo_matl_fee",costInfoMap.get("medicalMaterials"));//治疗用一次性医用材料费
        basinfoMap.put("oprn_dspo_matl_fee",costInfoMap.get("operationMaterials"));//手术用一次性医用材料费
        basinfoMap.put("oth_fee",costInfoMap.get("other"));//其他费
        basinfoMap.put("vali_flag","1");//有效标志
        basinfoMap.put("fixmedins_code","H42030401471");//定点医药机构编号
        basinfoMap.put("offsite_med_treat","");//异地就医标志
        basinfoMap.put("pre_exam","0");//院前检查费
        basinfoMap.put("patn_rlts","");//与患者关系
        String nwb_adm_type = "5".equals(otherInfoMap.get("babyPatInWay")) ? "9":otherInfoMap.get("c");
        basinfoMap.put("nwb_adm_type",nwb_adm_type);//新生儿入院类型
        basinfoMap.put("mul_nwb_adm_wt","");//多新生儿入院体重
        basinfoMap.put("opsp_diag_caty","");//门诊慢特病诊断科别
        basinfoMap.put("opsp_mdtrt_date","");//门诊慢特病就诊日期
        basinfoMap.put("spga_nurscare_days",treatmentsMap.get("tjhlT")); //特级护理天数
        basinfoMap.put("lv1_nurscare_days",treatmentsMap.get("yjhlT")); //一级护理天数
        basinfoMap.put("scd_nurscare_days","");//二级护理天数
        basinfoMap.put("lv3_nurscare_days",treatmentsMap.get("sjhlT")); //三级护理天数
        basinfoMap.put("otp_wm_diag",diagnosisInfosMap.get("xyMzzd")); //门（急）诊西医诊断
        basinfoMap.put("otp_wm_diag_dise_code",diagnosisInfosMap.get("xyMzzdCode")); //西医诊断疾病代码
        basinfoMap.put("otp_tcm_diag",diagnosisInfosMap.get("zyMzzd")); //门（急）诊中医诊断
        basinfoMap.put("zyMzzdCode",diagnosisInfosMap.get("zyMzzdCode")); //中医诊断代码
        basinfoMap.put("bld_cat","");//输血品种
        basinfoMap.put("bld_amt","");//输血量
        basinfoMap.put("bld_unt","");//输血计量单位
        basinfoMap.put("extract_flag","0");//抽取标识


        //处理诊断信息
        for (int j = 0; j < leaveDiagnosisListMap.size(); j++) {
            Map<String,String> leaveDiagnosisMap = leaveDiagnosisListMap.get(j); // 原始诊断字段
            // 判断诊断类型isChineseDiagnosis 原始数据 1:代表是中医，2:代表是西医
            if (("1").equals(leaveDiagnosisMap.get("diagnosisCode"))){
                continue;
            }

            HashMap<String, String> diagInfoMap = new HashMap<>();

            diagInfoMap.put("unique_id",unique_id.toString());//唯一ID
            diagInfoMap.put("palg_no","");//病理号
            diagInfoMap.put("ipt_patn_disediag_type_code","");//住院患者疾病诊断类型代码
            diagInfoMap.put("disediag_type","");//疾病诊断类型
            // 处理主诊断标识 原始1:代表主要诊断，2:代表其他诊断
            String diagType = leaveDiagnosisMap.get("isMainDiagnosis");
            String maindiag_flag =  ("1".equals(diagType)) ? "1":"0";
            diagInfoMap.put("maindiag_flag",maindiag_flag); // 主诊断标识
            diagInfoMap.put("diag_code",leaveDiagnosisMap.get("diagnosisCode"));//诊断代码
            diagInfoMap.put("diag_name",leaveDiagnosisMap.get("diagnosisName"));//诊断名称
            diagInfoMap.put("inhosp_diag_code",leaveDiagnosisMap.get("diagnosisCode"));//院内诊断代码
            diagInfoMap.put("inhosp_diag_name",leaveDiagnosisMap.get("diagnosisName"));//院内诊断名称
            diagInfoMap.put("adm_dise_cond_name","");//入院疾病病情名称
            diagInfoMap.put("adm_dise_cond_code","");//入院疾病病情代码
            diagInfoMap.put("adm_cond",leaveDiagnosisMap.get("inSituation"));//入院病情
            diagInfoMap.put("adm_cond_code","");//入院时病情代码
            diagInfoMap.put("high_diag_evid","");//最高诊断依据
            diagInfoMap.put("bkup_deg","");//分化程度
            diagInfoMap.put("bkup_deg_code","");//分化程度代码
            diagInfoMap.put("vali_flag","1");//有效标志
            diagInfoMap.put("ipt_medcas_hmpg_sn","");//住院病案首页流水号
            diagInfoMap.put("mdtrt_sn","");//就医流水号
            diagInfoMap.put("fixmedins_code","");//定点医药机构编号
            if(isMapValid(diagInfoMap)){
                diagInfoListMap.add(diagInfoMap);}

        }
        //处理icu信息
        if(icuInfosListMap != null && !icuInfosListMap.isEmpty()) {
            for (int k = 0; k < icuInfosListMap.size(); k++) {
                HashMap<String, String> icuinfo = new HashMap<>();
                Map<String, String> icuInfoMap = icuInfosListMap.get(k);
                icuinfo.put("unique_id", unique_id.toString()); // 唯一id
                String icuRoom = icuInfoMap.get("icuRoom");
                String scs_cutd_ward_type;
                switch (icuRoom) {
                    case "CUU":
                        scs_cutd_ward_type = "1";
                        break;
                    case "NICU":
                        scs_cutd_ward_type = "2";
                        break;
                    case "ECU":
                        scs_cutd_ward_type = "3";
                        break;
                    case "SICU":
                        scs_cutd_ward_type = "4";
                        break;
                    case "PICU":
                        scs_cutd_ward_type = "5";
                        break;
                    case "RICU":
                        scs_cutd_ward_type = "6";
                        break;
                    case "ICU":
                        scs_cutd_ward_type = "7";
                        break;
                    case "其他":
                        scs_cutd_ward_type = "9";
                        break;
                    default:
                        scs_cutd_ward_type = "";
                }
                icuinfo.put("scs_cutd_ward_type", scs_cutd_ward_type); // 重症监护病房类型
                icuinfo.put("scs_cutd_inpool_time", icuInfoMap.get("enterIcuRoomTime")); // 重症监护进入时间
                icuinfo.put("scs_cutd_exit_time", icuInfoMap.get("backIcuRoomTime")); // 重症监护退出时间

                String totalTime = String.valueOf(icuInfoMap.get("totalTime"));
                int days = 0;
                int hours = 0;
                int minutes = 0;
                if (totalTime != null && !totalTime.isEmpty()) {
                    try {
                        int totalTimes = Integer.parseInt(totalTime);
                        days = totalTimes / 24; // 计算天数
                        hours = totalTimes % 24; // 剩余的小时数
                    } catch (NumberFormatException e) {
                        // 处理解析错误，例如记录日志或设置默认值
                        System.err.println("Invalid totalTime format: " + totalTime);
                    }
                }
                StringBuilder scs_cutd_sum_dura_StringBuilder = new StringBuilder();
                StringBuilder scs_cutd_sum_dura = scs_cutd_sum_dura_StringBuilder.append(days).append("/").append(hours).append("/").append(minutes);
                icuinfo.put("scs_cutd_sum_dura", scs_cutd_sum_dura.toString()); // 重症监护合计时长

                if (isMapValid(icuinfo)){
                    icuInfoListMap.add(icuinfo);}
            }
        }
        //处理手术操作信息
        for (int o = 0; o < surgeryInfosListMap.size(); o++) {
            HashMap<String, String> oprnInfo = new HashMap<>();
            Map<String, String> surgeryInfostMap = surgeryInfosListMap.get(o);
            oprnInfo.put("unique_id",unique_id.toString());//唯一ID
            oprnInfo.put("oprn_oprt_date",surgeryInfostMap.get("operationDate"));//手术操作日期
            oprnInfo.put("oprn_oprt_name",surgeryInfostMap.get("operationName"));//手术操作名称
            oprnInfo.put("oprn_oprt_code",surgeryInfostMap.get("operationCode"));//手术操作代码
            oprnInfo.put("oprn_oprt_sn",surgeryInfostMap.get("orderNo"));//手术操作序列号
            oprnInfo.put("oprn_lv_code",surgeryInfostMap.get("operationLevel"));//手术级别代码
            oprnInfo.put("oprn_lv_name","");//手术级别名称
            oprnInfo.put("oper_name",surgeryInfostMap.get("operator"));//手术者姓名
            oprnInfo.put("asit1_name",surgeryInfostMap.get("firstAssistant"));//助手Ⅰ姓名
            oprnInfo.put("asit2_name",surgeryInfostMap.get("firstAssistant"));//助手Ⅱ姓名
            oprnInfo.put("sinc_heal_lv",surgeryInfostMap.get("healLevel"));//手术切口愈合
            oprnInfo.put("sinc_heal_lv_code",surgeryInfostMap.get("healLevel"));//手术切口愈合等级代码
            oprnInfo.put("anst_mtd_name","");//麻醉-方法名称
            oprnInfo.put("anst_mtd_code",surgeryInfostMap.get("firstAssistant"));//麻醉-方法代码
            oprnInfo.put("anst_dr_name",surgeryInfostMap.get("hocusDoctor"));//麻醉医师姓名
            oprnInfo.put("anst_dr_code",surgeryInfostMap.get("hocusDoctorId"));//麻醉医师代码
            oprnInfo.put("oprn_oper_part",surgeryInfostMap.get("hocusDoctorId"));//手术操作部位
            oprnInfo.put("oprn_oper_part_code","");//手术操作部位
            oprnInfo.put("oprn_con_time",surgeryInfostMap.get("operateDuration"));//手术持续时间
            oprnInfo.put("anst_lv_name","");//麻醉分级名称
            oprnInfo.put("anst_lv_code","");//麻醉分级代码
            oprnInfo.put("oprn_patn_type","");//手术患者类型
            oprnInfo.put("oprn_patn_type_code","");//手术患者类型代码
            oprnInfo.put("main_oprn_flag","");//主要手术标志
            oprnInfo.put("anst_asa_lv_code","");//麻醉ASA分级代码
            oprnInfo.put("anst_asa_lv_name","");//麻醉ASA分级名称
            oprnInfo.put("anst_medn_code","");//麻醉药物代码
            oprnInfo.put("anst_medn_name","");//麻醉药物名称
            oprnInfo.put("anst_medn_dos","");//麻醉药物剂量
            oprnInfo.put("unt","");//计量单位
            oprnInfo.put("anst_begntime",null);//麻醉开始时间
            oprnInfo.put("anst_endtime",null);//麻醉结束时间
            oprnInfo.put("anst_copn_code","");//麻醉合并症代码
            oprnInfo.put("anst_copn_name","");//麻醉合并症名称
            oprnInfo.put("anst_copn_dscr","");//麻醉合并症描述
            oprnInfo.put("pacu_begntime",null);//复苏室开始时间
            oprnInfo.put("pacu_endtime",null);//复苏室结束时间
            oprnInfo.put("canc_oprn_flag","");//取消手术标志
            oprnInfo.put("vali_flag","1");//有效标志
            oprnInfo.put("ipt_medcas_hmpg_sn","");//住院病案首页流水号
            oprnInfo.put("mdtrt_sn","");//住院病案首页流水号
            oprnInfo.put("oprn_oprt_begntime",surgeryInfostMap.get("operationDate"));//手术操作开始时间
            String oprn_oprt_endtime = null;
/*                if (surgeryInfostMap.get("operationDate") != null && surgeryInfostMap.get("operationDate").isEmpty() &&  surgeryInfostMap.get("operateDuration") != null && surgeryInfostMap.get("operateDuration").isEmpty()) {
                    oprn_oprt_endtime = addMinutes(surgeryInfostMap.get("operationDate"), surgeryInfostMap.get("operateDuration"));
                }*/
            oprnInfo.put("oprn_oprt_endtime",oprn_oprt_endtime);//手术操作结束时间

            if(isMapValid(oprnInfo)){
                oprnInfoListMap.add(oprnInfo);}
        }
        //TODO
        //结算清单
        setInfoMap.put("unique_id",unique_id.toString());
        setInfoMap.put("psn_no",patientMainInfoMap.get("patId"));//人员编号
        setInfoMap.put("mdtrt_id",patientMainInfoMap.get("patInHosId"));//就诊id
        setInfoMap.put("setl_id",costInfoMap.get("costInfoId"));//结算id
        setInfoMap.put("bill_code","");//票据代码
        setInfoMap.put("bill_no","");//票据号码
        setInfoMap.put("biz_sn","");//业务流水号
        setInfoMap.put("setl_begn_date",null);//结算开始日期
        setInfoMap.put("setl_end_date",null);//结算结束日期
        setInfoMap.put("medins_fill_dept",null);//医疗机构填报部门
        setInfoMap.put("medins_fill_psn",null);//医疗机构填报人
        setInfoMap.put("hsorg",null);//医保机构
        setInfoMap.put("hsorg_opter",null);//医保机构经办人
        setInfoMap.put("hi_paymtd",patientMainInfoMap.get("payMethod"));//医保支付方式
        setInfoMap.put("psn_selfpay",null);//个人自付
        setInfoMap.put("psn_ownpay",null);//个人自费
        setInfoMap.put("acct_pay",null);//个人账户支出
        setInfoMap.put("psn_cashpay",null);//个人现金支付
        setInfoMap.put("med_ins_fund",null);//医保统筹基金
        setInfoMap.put("hi_no",null);//医保编号
        setInfoMap.put("hi_setl_lv",null);//医保结算等级
        setInfoMap.put("hi_type",null);//医保类型
        setInfoMap.put("sp_psn_type",null);//特殊人员类型
        setInfoMap.put("insuplc",null);//参保地
        setInfoMap.put("ipt_med_type",null);//住院医疗类型
        // setInfoMap.put("di20_id");



        //单个病人同步
        if(zhongYangApiDto.getUniqueId() != null && basinfoMap.get("unique_id").equals(zhongYangApiDto.getUniqueId())) {

            ModifyBusSettleListDto modifyBusSettleListDto = new ModifyBusSettleListDto();
            modifyBusSettleListDto.setK00(zhongYangApiDto.getUniqueId());
            int num = settleListManageDao.queryUploadState(modifyBusSettleListDto);
            if (num > 0){
                throw new RuntimeException("该病例已经上传不能同步");
            }
            zhongYangApiMapper.insertBaseInfo(basinfoMap);
            for (int o = 0; o < diagInfoListMap.size(); o++) {
                Map<String, String> diagInfoMap = diagInfoListMap.get(o);
                diagInfoMap.put("di20_id", basinfoMap.get("id"));
            }
            zhongYangApiMapper.insertDiagInfo(diagInfoListMap);

            for (Map<String, String> icuInfoMap : icuInfoListMap) {
                icuInfoMap.put("di20_id", basinfoMap.get("id"));
            }
            if (icuInfoListMap != null && !icuInfoListMap.isEmpty()) {
                zhongYangApiMapper.insertIcuInfo(icuInfoListMap);
            }

            for (Map<String, String> oprnInfo : oprnInfoListMap) {
                oprnInfo.put("di20_id", basinfoMap.get("id"));
            }
            if (oprnInfoListMap != null && !oprnInfoListMap.isEmpty()) {
                zhongYangApiMapper.insertOprnInfo(oprnInfoListMap);
            }
            //批量抽取
        } else if(zhongYangApiDto.getUniqueId() == null){
            zhongYangApiMapper.insertBaseInfo(basinfoMap);
            for (int o = 0; o < diagInfoListMap.size(); o++) {
                Map<String, String> diagInfoMap = diagInfoListMap.get(o);
                diagInfoMap.put("di20_id", basinfoMap.get("id"));
            }
            zhongYangApiMapper.insertDiagInfo(diagInfoListMap);

            for (Map<String, String> icuInfoMap : icuInfoListMap) {
                icuInfoMap.put("di20_id", basinfoMap.get("id"));
            }
            if (icuInfoListMap != null && !icuInfoListMap.isEmpty()) {
                zhongYangApiMapper.insertIcuInfo(icuInfoListMap);
            }

            for (Map<String, String> oprnInfo : oprnInfoListMap) {
                oprnInfo.put("di20_id", basinfoMap.get("id"));
            }
            if (oprnInfoListMap != null && !oprnInfoListMap.isEmpty()) {
                zhongYangApiMapper.insertOprnInfo(oprnInfoListMap);
            }
        }
    }
    /**
     * 将时间字符串和字符串类型的分钟数相加
     * @param dateTimeStr
     * @param minutesToAddStr
     * @return
     */
    public static String addMinutes(String dateTimeStr, String minutesToAddStr) {
        // 定义日期时间格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        // 将字符串转换为 LocalDateTime
        LocalDateTime dateTime = LocalDateTime.parse(dateTimeStr, formatter);

        // 将字符串类型的分钟数转换为整数
        int minutesToAdd = Integer.parseInt(minutesToAddStr);

        // 加指定的分钟数
        dateTime = dateTime.plusMinutes(minutesToAdd);

        // 转换回字符串
        return dateTime.format(formatter);
    }

    /**
     * 判断map里面的值不为空
     * @param map
     * @return
     */
    public static boolean isMapValid(Map<String, String> map) {
        // 检查Map是否为null或为空
        if (map == null || map.isEmpty()) {
            return false;
        }
        // 标记是否存在至少一个非空值
        boolean hasNonEmptyValue = false;
        // 遍历Map，检查每个值是否为null或为空字符串，排除unique_id
        for (Map.Entry<String, String> entry : map.entrySet()) {
            String key = entry.getKey();
            String value = entry.getValue();

            // 排除 unique_id 的值
            if (!"unique_id".equals(key) && value != null && !value.isEmpty()) {
                hasNonEmptyValue = true;
                break; // 找到一个非空值后可以提前退出
            }
        }
        // 如果Map不为空且所有值都不为空，返回true
        return hasNonEmptyValue;
}

    /**
     * 创建连接获取返回值
     * @param getReqParamsMap
     * @param apiUrl
     * @return
     */
    public StringBuilder getConnection (Map<String, String> getReqParamsMap,StringBuilder apiUrl){

//        log.info("待签名参数:{}",apiUrl);
        //生成代签名参数
        Map<String, String> signStrMAp = getSignStr(getReqParamsMap);
        String sign = "";
        //生成签名
        try {
            sign = getSign(signStrMAp.get("sortedParamsStr"), APPSECRET);
//            log.info("调用2_2_3接口生成的sing:{}",sign);
//            log.info("调用2_2_3接口生成的时间戳timestamp：{}",signStrMAp.get("timestamp"));
        } catch (Exception e){
            e.printStackTrace();
//            log.info("2_2_3接口生成sing失败");
        }
        StringBuilder response = new StringBuilder();
        //生成请求头
        Map<String, String> headersMap = getHeaders(sign, signStrMAp.get("timestamp"));
        Integer total  = 0;
        try {
            //创建请求
            URL url = new URL(apiUrl.toString());
            HttpURLConnection connection  = (HttpURLConnection)url.openConnection();
            connection.setRequestMethod("GET");
            //添加请求头
            for(Map.Entry<String,String> map : headersMap.entrySet() ){
                connection.setRequestProperty(map.getKey(),map.getValue());
            }
            // 获取响应
            int responseCode = connection.getResponseCode();
            System.out.println("Response Code: " + responseCode);
            BufferedReader in = new BufferedReader(new InputStreamReader(connection.getInputStream()));
            String inputLine;
            while ((inputLine = in.readLine()) != null) {
                response.append(inputLine);
            }
            JSONObject responseData = JSONUtil.parseObj(response);
            Object data = responseData.get("data");
//            log.info("拿到的信息：{}",response);
        }catch (Exception e){
            e.printStackTrace();
            log.info("2_2_3接口调用失败");
        }

        return response;
    }

}