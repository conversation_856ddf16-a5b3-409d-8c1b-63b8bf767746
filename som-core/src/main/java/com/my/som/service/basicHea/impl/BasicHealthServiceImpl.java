package com.my.som.service.basicHea.impl;

import cn.hutool.core.util.XmlUtil;
import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import cn.hutool.poi.excel.ExcelUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.gexin.fastjson.JSON;
import com.my.som.config.ViewTableConfig;
import com.my.som.dto.basicHea.BasicHealthDTO;
import com.my.som.mapper.basicHea.BasicHealthMapper;
import com.my.som.mapper.hisview.HisViewMapper;
import com.my.som.model.common.SomDept;
import com.my.som.service.basicHea.BasicHealthService;
import com.my.som.service.basicHea.mapping.BasicHealthMapping;
import com.my.som.service.hisview.impl.DataInsertService;
import com.my.som.vo.basicHea.*;
import com.my.som.vo.hisview.SetlBaseInfoVO;
import com.my.som.vo.hisview.SetlDiagInfoVO;
import com.my.som.vo.hisview.SetlOprnInfoVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.w3c.dom.Document;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.parsers.ParserConfigurationException;
import javax.xml.xpath.XPathConstants;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class BasicHealthServiceImpl implements BasicHealthService {

    @Autowired
    private BasicHealthDataInsertService basicHealthDataInsertService;

    Logger logger =  LoggerFactory.getLogger(BasicHealthServiceImpl.class);

    @Autowired
    private BasicHealthMapper basicHealthMapper;

    @Override
    public void MedicalCaseDataPullInterface(BasicHealthDTO dto) {
        // 获取基层机构信息
        List<BasicHealthHospitalVO> basicHealthHospitalVOs = basicHealthMapper.queryHosp(dto);
        if(CollectionUtils.isEmpty(basicHealthHospitalVOs)){
            logger.error("未查询到基卫机构信息、停止抽取");
            return;
        }

        for (BasicHealthHospitalVO basicHealthHospitalVO : basicHealthHospitalVOs) {
            // 调用10号接口（根据时间范围获取患者业务ID）
            List<PatientRecordVO> patientRecords = fetchPatientRecords(basicHealthHospitalVO, dto);

            // 调用202号接口（根据业务ID查询患者住院病案首页）
            List<MedicalCaseDataPullVO> medicalCaseDataList = fetchMedicalCaseData(basicHealthHospitalVO, patientRecords);

            // 业务表属性映射
            List<SetlBaseInfoVO> baseInfoList = new ArrayList<>();
            List<SetlDiagInfoVO> diagInfoList = new ArrayList<>();
            List<SetlOprnInfoVO> oprnInfoList = new ArrayList<>();

            for (MedicalCaseDataPullVO medicalCaseData : medicalCaseDataList) {
                // 字段映射
                SetlBaseInfoVO baseInfoVO = BasicHealthMapping.convertBaseInfo(medicalCaseData.getBaseInfoVO());
                List<SetlDiagInfoVO> diagInfoVOS = BasicHealthMapping.convertDiagInfo(medicalCaseData.getDiagnosis());
                List<SetlOprnInfoVO> oprnInfoVOS = BasicHealthMapping.convertOprnInfo(medicalCaseData.getSurgerys());

                // 特殊处理
                processBaseInfo(baseInfoVO);
                processDiagInfo(baseInfoVO,diagInfoVOS);
                processOprnInfo(baseInfoVO,oprnInfoVOS);


                baseInfoList.add(baseInfoVO);
                diagInfoList.addAll(diagInfoVOS);
                oprnInfoList.addAll(oprnInfoVOS);
            }

            // 数据写入
            basicHealthDataInsertService.performAllInserts(
                    baseInfoList,
                    diagInfoList,
                    oprnInfoList, null, null, null, null, null, null
            );
            baseInfoList.clear();
            diagInfoList.clear();
            oprnInfoList.clear();
        }
    }


    @Override
    public void downLoadMedical(BasicHealthDTO dto,HttpServletResponse response) {
        // 获取机构信息
        List<BasicHealthHospitalVO> basicHealthHospitalVOs = basicHealthMapper.queryHosp(dto);

        if(CollectionUtils.isEmpty(basicHealthHospitalVOs)){
            logger.error("未查询到基卫机构信息");
            return;
        }

        // 准备请求参数
        for (BasicHealthHospitalVO healthHospitalVO : basicHealthHospitalVOs) {
            Map<String, Object> map = new HashMap<>();
            map.put("机构编码", healthHospitalVO.getOrgCode());
            map.put("验证码", healthHospitalVO.getVerCode());
            map.put("目录类型", dto.getType());

            // 调用03号接口 获取（科室、医生信息）
            String res = HttpRequest
                    .post(healthHospitalVO.getApiUrl())
                    .form("TradeCode", "03")
                    .form("InputParameter", JSONObject.toJSONString(map))
                    .execute()
                    .body();

            // 解析XML响应
            String json = parseXmlResponse(res);
            JSONObject jsonObject = JSONObject.parseObject(json);
            Integer result = jsonObject.getInteger("Result");

            if (result != 1) {
                throw new RuntimeException("Error: " + jsonObject.getString("Msg"));
            }
            JSONArray msgArray = jsonObject.getJSONArray("Msg");

            // 根据类型处理数据并生成文件
            try {
                if ("0".equals(dto.getType())) {
                    List<MedicalKsVO> dataList = JSONObject.parseObject(msgArray.toJSONString(), new TypeReference<List<MedicalKsVO>>() {});
                    handleDownload(response,"科室信息",MedicalKsVO.class,dataList);
                } else if ("1".equals(dto.getType())) {
                    List<MedicalStaffVO> dataList = JSONObject.parseObject(msgArray.toJSONString(), new TypeReference<List<MedicalStaffVO>>() {});
                    handleDownload(response,"医生信息", MedicalStaffVO.class,dataList);
                } else {
                    throw new IllegalArgumentException("Invalid directory type: " + dto.getType());
                }
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }

    }

    private void handleDownload(HttpServletResponse response,String title, Class clazz,Object data) throws IOException {
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("UTF-8");
        String fileName = URLEncoder.encode(title + System.currentTimeMillis() + ".xlsx", "UTF-8");
        response.setHeader("Content-disposition", "attachment;filename=" + fileName);
        EasyExcel.write(response.getOutputStream(), clazz)
                .sheet()
                .doWrite((List)data);
    }

    private List<PatientRecordVO> fetchPatientRecords(BasicHealthHospitalVO hospitalVO, BasicHealthDTO dto) {
        Map<String, Object> map = new HashMap<>();
        map.put("机构编码", hospitalVO.getOrgCode());
        map.put("验证码", hospitalVO.getVerCode());
        map.put("开始时间", dto.getBeginTime());
        map.put("结束时间", dto.getEndTime());

        String res = HttpRequest
                .post(hospitalVO.getApiUrl())
                .form("TradeCode", 10)
                .form("InputParameter", JSONObject.toJSONString(map))
                .execute()
                .body();

        String json = parseXmlResponse(res);
        JSONObject jsonObject = JSONObject.parseObject(json);
        Integer result = jsonObject.getInteger("Result");

        if(result != 1){
            throw new RuntimeException(jsonObject.getString("Msg"));
        }

        JSONArray msgArray = jsonObject.getJSONArray("Msg");

        return JSONObject.parseObject(msgArray.toJSONString(), new TypeReference<List<PatientRecordVO>>() {});
    }

    private List<MedicalCaseDataPullVO> fetchMedicalCaseData(BasicHealthHospitalVO hospitalVO, List<PatientRecordVO> patientRecords) {
        List<MedicalCaseDataPullVO> medicalCaseDataList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(patientRecords)) {
            for (PatientRecordVO patientRecord : patientRecords) {
                Map<String, Object> map = new HashMap<>();
                map.put("业务ID", patientRecord.getBusinessId());
                map.put("验证码", hospitalVO.getVerCode());

                String res = HttpRequest
                        .post(hospitalVO.getApiUrl())
                        .form("TradeCode", 202)
                        .form("InputParameter", JSONObject.toJSONString(map))
                        .execute()
                        .body();

                String json = parseXmlResponse(res);
                JSONObject jsonObject = JSONObject.parseObject(json);

                Integer result = jsonObject.getInteger("Result");

                if(result != 1){
                    logger.error("业务ID = {}:暂未生成病案首页",patientRecord.getBusinessId());
                    logger.error(jsonObject.getString("Msg"));
                    continue;
                }

                logger.error("基卫抽取到的数据为：" +jsonObject );
                MedicalCaseDataPullVO medicalCaseData = JSONObject.parseObject(jsonObject.getString("Msg"), MedicalCaseDataPullVO.class);
                medicalCaseDataList.add(medicalCaseData);

                // 特殊处理
                processMedicalCaseDataPullVO(medicalCaseData,patientRecord);
            }
        }
        return medicalCaseDataList;
    }

    private void processMedicalCaseDataPullVO(MedicalCaseDataPullVO medicalCaseData, PatientRecordVO patientRecord) {
        // 病案首页返回的入院科别、出院科别（存在重复）、设置HIS科室编码作为患者科室编码信息
        medicalCaseData.getBaseInfoVO().setAdmissionDepartmentHisCode(patientRecord.getAdmissionDepartmentCode());
        medicalCaseData.getBaseInfoVO().setDischargeDepartmentHisCode(patientRecord.getDischargeDepartmentCode());
        // 就诊ID存在重复，获取业务ID作为unqiue_id
        medicalCaseData.getBaseInfoVO().setBid(patientRecord.getBusinessId());
    }

    private void processBaseInfo(SetlBaseInfoVO baseInfoVO) {
        baseInfoVO.setMdtrtSn(baseInfoVO.getMedcasno());
        baseInfoVO.setValiFlag("1");
        if ("09".equals(baseInfoVO.getMedfeePaymtdCode())) {
            baseInfoVO.setMedfeePaymtdCode("02");
        }
    }
    private void processDiagInfo(SetlBaseInfoVO baseInfoVO,List<SetlDiagInfoVO> diagList) {
        for (int i = 0; i < diagList.size(); i++) {
            SetlDiagInfoVO diagInfoVO = diagList.get(i);
            diagInfoVO.setUniqueId(baseInfoVO.getUniqueId());
            diagInfoVO.setValiFlag("1");
            diagInfoVO.setMaindiagFlag(i == 0 ? "1" : "0");
        }
    }
    private void processOprnInfo(SetlBaseInfoVO baseInfoVO,List<SetlOprnInfoVO> oprnList) {
        for (int i = 0; i < oprnList.size(); i++) {
            SetlOprnInfoVO oprnInfoVO = oprnList.get(i);
            oprnInfoVO.setUniqueId(baseInfoVO.getUniqueId());
            oprnInfoVO.setValiFlag("1");
            oprnInfoVO.setMainOprnFlag(i == 0 ? "1" : "0");
        }
    }

    private String parseXmlResponse(String xmlResponse) {
        try {
            DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
            DocumentBuilder builder = factory.newDocumentBuilder();
            Document document = builder.parse(new ByteArrayInputStream(xmlResponse.getBytes("UTF-8")));
            NodeList nodeList = document.getElementsByTagName("string");
            if (nodeList.getLength() > 0) {
                Node node = nodeList.item(0);
                return node.getTextContent();
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return "";
    }




}
