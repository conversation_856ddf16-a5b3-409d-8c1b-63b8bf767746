package com.my.som.service.dictmenagement.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.my.som.common.util.RedisUtils;
import com.my.som.common.util.ValidateUtil;
import com.my.som.dao.dictmenagement.DictMenagementDao;
import com.my.som.dto.dictmenament.DictQueryParam;
import com.my.som.dto.hisview.HisViewDTO;
import com.my.som.model.BaseCode;
import com.my.som.model.medicalQuality.BusFeeBreakDown;
import com.my.som.service.dictmenagement.DictMenagementService;
import com.my.som.common.vo.DictRedisVo;
import com.my.som.common.vo.DictVo;
import com.my.som.vo.SomSysCode;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * @author: zyd
 * Case:          drg
 * FileName:      DictMenagementServiceImpl
 * Date:          2020/1/10 16:13
 * Description:   字典操作 实现类
 * Ver:       1.0
 * Copyright (C),2012-2022 Freedom
 */
@Service
public class DictMenagementServiceImpl implements DictMenagementService {
    @Autowired
    DictMenagementDao dictMenagementDao;



    @Override
    public List<SomSysCode> getList(DictQueryParam dictQueryParam) {
        return dictMenagementDao.getList(dictQueryParam);
    }

    @Value("${redis.key.prefix.authCode}")
    private String REDIS_KEY_PREFIX_AUTH_CODE;
    @Value("${redis.key.prefix.settleListDictCode}")
    private String SETTLE_LIST_DICT_PREFIX;
    @Value("${redis.key.expire.timeOut}")
    private Long AUTH_CODE_EXPIRE_TIMEOUT;


    @Override
    public void initDictCache() {
        addToRedis(dictMenagementDao.getList(null), REDIS_KEY_PREFIX_AUTH_CODE);
    }

    @Override
    public void initSettleListDictCache() {
        addToRedis(dictMenagementDao.getSettleListDictList(null), SETTLE_LIST_DICT_PREFIX);
    }

    @Override
    public void initChargeDetail() {
        Set<String> keys =  RedisUtils.redisTemplate.keys("CHARGE_DETAIL*");
        if (!ValidateUtil.isEmpty(keys)) {
            for (String key : keys) {
                //此处结束删除
                RedisUtils.delete(key);
            }
        }
        LocalDate today = LocalDate.now();
        LocalDate startDate;
        LocalDate endDate;

        startDate = today.minusMonths(1).withDayOfMonth(1);

        // 设置当前月最后一天
        endDate = today.withDayOfMonth(today.lengthOfMonth());

        // 将日期转换为 LocalDateTime，并设置时间为开始和结束
        List<BusFeeBreakDown> list = dictMenagementDao.getChargeDetail(String.valueOf(startDate),String.valueOf(endDate));
        List<String> k00List = dictMenagementDao.getChargeUniqueId(String.valueOf(startDate),String.valueOf(endDate));

        addChargeToRedis(list, "CHARGE_DETAIL:");
        RedisUtils.set("CHARGE_DETAIL_UNIQUEID:",k00List);
    }


    /**
     * 添加到缓存
     *
     * @param codeList
     * @param prefix
     */
    private void addToRedis(List<SomSysCode> codeList,
                            String prefix) {
        Map<String, DictRedisVo> redisVoMap = new HashMap<>(16);
        String dictCode = "";
        DictVo dictVo;
        if (CollectionUtils.isNotEmpty(codeList)) {
            for (SomSysCode baseCode : codeList) {
                dictCode = baseCode.getCodeType().toUpperCase();
                dictVo = new DictVo(dictCode, baseCode.getDataVal(), baseCode.getLablName());
                if (StrUtil.isBlankIfStr(redisVoMap.get(dictCode))) {
                    DictRedisVo dictRedisVo = new DictRedisVo(dictCode);
                    dictRedisVo.getDictVoList().add(dictVo);
                    redisVoMap.put(dictCode, dictRedisVo);
                } else {
                    redisVoMap.get(dictCode).getDictVoList().add(dictVo);
                }
            }
        }
        //遍历数据 缓存到Redis中
        Set<String> keySet = redisVoMap.keySet();
        for (String key : keySet) {
            RedisUtils.set(prefix + key, redisVoMap.get(key));
        }
    }

    private void addChargeToRedis(List<BusFeeBreakDown> codeList,
                                  String prefix) {
        Map<String, List<String>> redisVoMap = new HashMap<>(16);

        if (CollectionUtils.isNotEmpty(codeList)) {
            for (BusFeeBreakDown baseCode : codeList) {
                String uniqueId = baseCode.getUniqueId();
                List<String> medListCodg = Arrays.asList(baseCode.getMedListCodg().split(";"));
                redisVoMap.put(uniqueId, medListCodg);
            }
        }
        //遍历数据 缓存到Redis中
        Set<String> keySet = redisVoMap.keySet();
        for (String key : keySet) {
            RedisUtils.set(prefix + key, redisVoMap.get(key));
        }
    }
}
