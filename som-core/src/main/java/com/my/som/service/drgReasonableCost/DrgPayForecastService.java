package com.my.som.service.drgReasonableCost;

import com.my.som.dto.drgReasonableCost.DrgReasonableCostQueryParam;
import com.my.som.vo.drgReasonableCost.DrgPayForecastVo;

import java.util.List;

/**
 * Service
 * Created by sky on 2020/3/17.
 */
public interface DrgPayForecastService {
    /**
     * 获取DRG付费预测数据
     * @param queryParam
     * @return
     */
    List<DrgPayForecastVo> list(DrgReasonableCostQueryParam queryParam, Integer pageSize, Integer pageNum);

}
