package com.my.som.service.hospitalAnalysis;

import com.my.som.dto.costControl.CostControlQueryParam;
import com.my.som.dto.hospitalAnalysis.HospitalAnalysisQueryParam;
import com.my.som.vo.costControl.PpsDeptCountVo;
import com.my.som.vo.costControl.PpsDeptIndexVo;
import com.my.som.vo.hospitalAnalysis.DrgsDeptCountVo;
import com.my.som.vo.hospitalAnalysis.DrgsDeptIndexVo;

import java.util.List;

/**
 * Service
 * Created by sky on 2020/3/20.
 */
public interface DrgsDeptIndexService {
    /**
     * 查询科室病组指标信息
     * @param queryParam
     * @return
     */
    List<DrgsDeptIndexVo> list(HospitalAnalysisQueryParam queryParam, Integer pageSize, Integer pageNum);

    /**
     * 查询科室病组指标统计信息
     * @param queryParam
     * @return
     */
    List<DrgsDeptCountVo> getCountInfo(HospitalAnalysisQueryParam queryParam);

}
