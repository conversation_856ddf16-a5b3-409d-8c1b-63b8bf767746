package com.my.som.service.hospitalAnalysis.impl;


import com.github.pagehelper.PageHelper;
import com.my.som.common.constant.DrgConst;
import com.my.som.common.util.ValidateUtil;
import com.my.som.dao.common.CommonDao;
import com.my.som.dao.doctorDiagnoseInterface.BusIcdDao;
import com.my.som.dao.hospitalAnalysis.DrgsAnalysisDao;
import com.my.som.dto.common.BusIcdQueryParam;
import com.my.som.dto.common.DrgsQueryParam;
import com.my.som.dto.hospitalAnalysis.HospitalAnalysisQueryParam;
import com.my.som.mapper.common.SomDeptMapper;
import com.my.som.model.common.SomDept;
import com.my.som.model.common.SomDeptExample;
import com.my.som.model.common.SomIcdCrsp;
import com.my.som.service.hospitalAnalysis.DrgsAnalysisService;
import com.my.som.util.SysCommonConfigUtil;
import com.my.som.vo.common.BusDeptNode;
import com.my.som.vo.common.DrgsSuggestInfo;
import com.my.som.vo.hospitalAnalysis.DrgsAnalysisCountInfo;
import com.my.som.vo.hospitalAnalysis.DrgsAnalysisInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Service实现类
 * Created by sky on 2020/1/5.
 */
@Service
public class DrgsAnalysisServiceImpl implements DrgsAnalysisService {
    @Autowired
    private DrgsAnalysisDao drgsAnalysisDao;

    @Override
    public List<DrgsAnalysisInfo> list(HospitalAnalysisQueryParam queryParam, Integer pageSize, Integer pageNum) {
        Object drgGrouperVerObj = SysCommonConfigUtil.get(DrgConst.FEE_MULTI_ADJMCON);
        if (drgGrouperVerObj != null && "true".equals(drgGrouperVerObj.toString())) {
            queryParam.setFeeMulAdjmConf("true");
        }
        String insuPlaceType = (String) SysCommonConfigUtil.get(DrgConst.INSUPLC_ADMDVS_AREA);
        queryParam.setInsuplcAdmdvs(insuPlaceType);
        PageHelper.startPage(pageNum, pageSize);
        return drgsAnalysisDao.list(queryParam);
    }

    @Override
    public List<DrgsAnalysisCountInfo> getCountInfo(HospitalAnalysisQueryParam queryParam) {
        Map<String,String> map = drgsAnalysisDao.getCountInfo(queryParam);
        List<DrgsAnalysisCountInfo> result = new ArrayList<>();
        if(!ValidateUtil.isEmpty(map)){
            DrgsAnalysisCountInfo daci0 = new DrgsAnalysisCountInfo();
            daci0.setIndex("病案数");
            daci0.setSeriousComplicationDrgs(String.valueOf(map.get("seriousComplicationMedicalNum")));
            daci0.setComorbiditiesComplicationDrgs(String.valueOf(map.get("comorbiditiesComplicationMedicalNum")));
            daci0.setNormalComplicationDrgs(String.valueOf(map.get("normalComplicationMedicalNum")));
            daci0.setNoSeriousComorbiditiesComplicationDrgs(String.valueOf(map.get("noSeriousComorbiditiesComplicationMedicalNum")));
            daci0.setNoComplicationDrgs(String.valueOf(map.get("noComplicationMedicalNum")));
            daci0.setNotDifferentiatedDrgs(String.valueOf(map.get("notDifferentiatedMedicalNum")));
            daci0.setMedicalDeptDrgs(String.valueOf(map.get("medicalDeptMedicalNum")));
            daci0.setNotOperationDrgs(String.valueOf(map.get("notOperationMedicalNum")));
            daci0.setSurgeryDeptDrgs(String.valueOf(map.get("surgeryDeptMedicalNum")));
            result.add(daci0);
            DrgsAnalysisCountInfo daci1 = new DrgsAnalysisCountInfo();
            daci1.setIndex("DRGs组数");
            daci1.setSeriousComplicationDrgs(String.valueOf(map.get("seriousComplicationDrgsNum")));
            daci1.setComorbiditiesComplicationDrgs(String.valueOf(map.get("comorbiditiesComplicationDrgsNum")));
            daci1.setNormalComplicationDrgs(String.valueOf(map.get("normalComplicationDrgsNum")));
            daci1.setNoSeriousComorbiditiesComplicationDrgs(String.valueOf(map.get("noSeriousComorbiditiesComplicationDrgsNum")));
            daci1.setNoComplicationDrgs(String.valueOf(map.get("noComplicationDrgsNum")));
            daci1.setNotDifferentiatedDrgs(String.valueOf(map.get("notDifferentiatedDrgsNum")));
            daci1.setMedicalDeptDrgs(String.valueOf(map.get("medicalDeptDrgsNum")));
            daci1.setNotOperationDrgs(String.valueOf(map.get("notOperationDrgsNum")));
            daci1.setSurgeryDeptDrgs(String.valueOf(map.get("surgeryDeptDrgsNum")));
            result.add(daci1);
            DrgsAnalysisCountInfo daci2 = new DrgsAnalysisCountInfo();
            daci2.setIndex("DRGs组数占比");
            daci2.setSeriousComplicationDrgs(String.valueOf(map.get("seriousComplicationDrgsRate")));
            daci2.setComorbiditiesComplicationDrgs(String.valueOf(map.get("comorbiditiesComplicationDrgsRate")));
            daci2.setNormalComplicationDrgs(String.valueOf(map.get("normalComplicationDrgsRate")));
            daci2.setNoSeriousComorbiditiesComplicationDrgs(String.valueOf(map.get("noSeriousComorbiditiesComplicationDrgsRate")));
            daci2.setNoComplicationDrgs(String.valueOf(map.get("noComplicationDrgsRate")));
            daci2.setNotDifferentiatedDrgs(String.valueOf(map.get("notDifferentiatedDrgsRate")));
            daci2.setMedicalDeptDrgs(String.valueOf(map.get("medicalDeptDrgsRate")));
            daci2.setNotOperationDrgs(String.valueOf(map.get("notOperationDrgsRate")));
            daci2.setSurgeryDeptDrgs(String.valueOf(map.get("surgeryDeptDrgsRate")));
            result.add(daci2);
            DrgsAnalysisCountInfo daci3 = new DrgsAnalysisCountInfo();
            daci3.setIndex("总权重");
            daci3.setSeriousComplicationDrgs(String.valueOf(map.get("seriousComplicationTotalDrgWeight")));
            daci3.setComorbiditiesComplicationDrgs(String.valueOf(map.get("comorbiditiesComplicationTotalDrgWeight")));
            daci3.setNormalComplicationDrgs(String.valueOf(map.get("normalComplicationTotalDrgWeight")));
            daci3.setNoSeriousComorbiditiesComplicationDrgs(String.valueOf(map.get("noSeriousComorbiditiesComplicationTotalDrgWeight")));
            daci3.setNoComplicationDrgs(String.valueOf(map.get("noComplicationTotalDrgWeight")));
            daci3.setNotDifferentiatedDrgs(String.valueOf(map.get("notDifferentiatedTotalDrgWeight")));
            daci3.setMedicalDeptDrgs(String.valueOf(map.get("medicalDeptTotalDrgWeight")));
            daci3.setNotOperationDrgs(String.valueOf(map.get("notOperationTotalDrgWeight")));
            daci3.setSurgeryDeptDrgs(String.valueOf(map.get("surgeryDeptTotalDrgWeight")));
            result.add(daci3);
            DrgsAnalysisCountInfo daci4 = new DrgsAnalysisCountInfo();
            daci4.setIndex("CMI");
            daci4.setSeriousComplicationDrgs(String.valueOf(map.get("seriousComplicationCmi")));
            daci4.setComorbiditiesComplicationDrgs(String.valueOf(map.get("comorbiditiesComplicationCmi")));
            daci4.setNormalComplicationDrgs(String.valueOf(map.get("normalComplicationCmi")));
            daci4.setNoSeriousComorbiditiesComplicationDrgs(String.valueOf(map.get("noSeriousComorbiditiesComplicationCmi")));
            daci4.setNoComplicationDrgs(String.valueOf(map.get("noComplicationCmi")));
            daci4.setNotDifferentiatedDrgs(String.valueOf(map.get("notDifferentiatedCmi")));
            daci4.setMedicalDeptDrgs(String.valueOf(map.get("medicalDeptCmi")));
            daci4.setNotOperationDrgs(String.valueOf(map.get("notOperationCmi")));
            daci4.setSurgeryDeptDrgs(String.valueOf(map.get("surgeryDeptCmi")));
            result.add(daci4);
        }
        return result;
    }
}
