package com.my.som.service.hosPerfAppraisal.impl;

import com.my.som.common.constant.DrgConst;
import com.my.som.common.exception.AppException;
import com.my.som.common.util.ValidateUtil;
import com.my.som.dto.hosPerfAppraisal.HosPerfAppraisalDsItemDto;
import com.my.som.dto.hosPerfAppraisal.HosPerfAppraisalKpiItemDto;
import com.my.som.mapper.hosPerfAppraisal.HosPerfAppraisalDsItemMapper;
import com.my.som.mapper.hosPerfAppraisal.HosPerfAppraisalKpiItemMapper;
import com.my.som.service.hosPerfAppraisal.HosPerfAppraisalKpiItemService;
import com.my.som.vo.hosPerfAppraisal.HosPerfAppraisalDsItemVo;
import com.my.som.vo.hosPerfAppraisal.HosPerfAppraisalKpiItemVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @author: zyd
 * @version 1.0
 * @date: 2023-07-31
 * @description:
 */
@Service
public class HosPerfAppraisalKpiItemServiceImpl implements HosPerfAppraisalKpiItemService {

    @Autowired
    private HosPerfAppraisalKpiItemMapper hosPerfAppraisalKpiItemMapper;

    @Autowired
    private HosPerfAppraisalDsItemMapper hosPerfAppraisalDsItemMapper;

    @Override
    public List<HosPerfAppraisalKpiItemVo> queryList(HosPerfAppraisalKpiItemDto dto) {
        if (ValidateUtil.isEmpty(dto.getVer())) {
            throw new AppException("请选择版本");
        }
        List<HosPerfAppraisalKpiItemVo> hosPerfAppraisalKpiItemVos = hosPerfAppraisalKpiItemMapper.queryList(dto);
        if (ValidateUtil.isNotEmpty(hosPerfAppraisalKpiItemVos)) {
            List<HosPerfAppraisalDsItemVo> hosPerfAppraisalDsItemVos = hosPerfAppraisalDsItemMapper.queryList(new HosPerfAppraisalDsItemDto());
            if (ValidateUtil.isNotEmpty(hosPerfAppraisalDsItemVos)) {
                Map<String, String> dsItemMap = new HashMap<>();
                for (HosPerfAppraisalDsItemVo dsItemVo : hosPerfAppraisalDsItemVos) {
                    dsItemMap.put(dsItemVo.getDsItemCode(), dsItemVo.getDsItemName());
                }

                for (HosPerfAppraisalKpiItemVo kpiItemVo : hosPerfAppraisalKpiItemVos) {
                    kpiItemVo.setDisplayKpiCalcFormula(kpiItemVo.getKpiCalcFormula());
                    // 2为指标
                    if ("2".equals(kpiItemVo.getKpiType())) {
                        for (String k : dsItemMap.keySet()) {
                            if (kpiItemVo.getKpiCalcFormula().contains(k)) {
                                kpiItemVo.setDisplayKpiCalcFormula(kpiItemVo.getDisplayKpiCalcFormula().replaceAll(k, dsItemMap.get(k)));
                            }
                        }
                    }
                }
            }
        }
        return hosPerfAppraisalKpiItemVos;
    }

    @Override
    public void add(HosPerfAppraisalKpiItemDto dto) {
        hosPerfAppraisalKpiItemMapper.add(dto);
    }

    @Override
    public void update(HosPerfAppraisalKpiItemDto dto) {
        hosPerfAppraisalKpiItemMapper.update(dto);
    }

    @Override
    public void remove(HosPerfAppraisalKpiItemDto dto) {
        hosPerfAppraisalKpiItemMapper.remove(dto);
    }
}
