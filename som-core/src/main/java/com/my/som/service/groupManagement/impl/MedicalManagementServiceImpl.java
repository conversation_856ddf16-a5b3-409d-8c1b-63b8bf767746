package com.my.som.service.groupManagement.impl;

import cn.hutool.core.bean.BeanUtil;
import com.github.pagehelper.PageHelper;
import com.my.som.common.constant.DrgConst;
import com.my.som.common.exception.AppException;
import com.my.som.common.util.ValidateUtil;
import com.my.som.dto.common.HospitalDrgQueryParam;
import com.my.som.dto.common.TpdDrgsDto;
import com.my.som.dto.dataConfig.HospitalInfoDto;
import com.my.som.dto.groupManagement.MedicalManagementDto;
import com.my.som.dto.pregroup.PreGroupDto;
import com.my.som.mapper.common.DipConfigMapper;
import com.my.som.mapper.common.DrgConfigMapper;
import com.my.som.mapper.common.SomDipStandardMapper;
import com.my.som.mapper.dataConfig.DipDiseaseTypeConfigMapper;
import com.my.som.mapper.dataConfig.ViewHospitalMapper;
import com.my.som.mapper.dataHandle.SomDrgStandardMapper;
import com.my.som.mapper.engine.GroupInfoMapper;
import com.my.som.mapper.groupManagement.MedicalManagementMapper;
import com.my.som.model.common.SomDipStandard;
import com.my.som.model.dataHandle.*;
import com.my.som.service.common.impl.BenchmarkConfigServiceImpl;
import com.my.som.service.dataHandle.DataRecordService;
import com.my.som.service.dataHandle.impl.GeneraScoreJobServiceImpl;
import com.my.som.service.dataHandle.impl.SettleListValidateJobServiceImpl;
import com.my.som.service.groupManagement.MedicalManagementService;
import com.my.som.util.*;
import com.my.som.vo.common.DipConfigVo;
import com.my.som.vo.dataConfig.ViewHospitalVo;
import com.my.som.vo.dataHandle.PayToPredict.PayToPredictVo;
import com.my.som.vo.dataHandle.SettleListValidateVo;
import com.my.som.vo.dataHandle.dataGroup.*;
import com.my.som.vo.dipBusiness.DipGroupResult;
import com.my.som.vo.dipBusiness.DipPayToPredictVo;
import com.my.som.vo.groupManagement.MedicalManagementVo;
import com.my.som.vo.pregroup.PreGroupVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

/**
 * 病案管理
 */
@Service
public class MedicalManagementServiceImpl implements MedicalManagementService {

    private static final String NULL = null;

    @Autowired
    private MedicalManagementMapper medicalManagementMapper;

    @Autowired
    private GroupInfoMapper groupInfoMapper;

    @Autowired
    private SomDipStandardMapper tpdDipBenchmarkMapper;

    @Autowired
    private SomDrgStandardMapper busBenchmarkMapper;

    @Autowired
    private DipConfigMapper dipConfigMapper;

    @Autowired
    private DrgConfigMapper drgConfigMapper;

    @Autowired
    private DipDiseaseTypeConfigMapper dipDiseaseTypeConfigMapper;

    @Autowired
    private GeneraScoreJobServiceImpl generaScoreJobService;

    @Autowired
    private ViewHospitalMapper viewHospitalMapper;

    @Autowired
    BenchmarkConfigServiceImpl benchmarkConfigServiceImpl;

    @Autowired
    private SettleListValidateJobServiceImpl settleListValidateJobService;

    @Autowired
    private DataRecordService dataRecordService;

    /**
     * DIP测算
     *
     * @param dto
     */
    @Override
    @Transactional(readOnly = false)
    public Boolean calculationData(MedicalManagementDto dto) {
        // 设置分组器需要的参数
        HospitalDrgQueryParam groupParams = new HospitalDrgQueryParam();
        groupParams.setActive_flag(DrgConst.ACTIVE_FLAG_1);
        // 获取分组器
        List<HospitalVo> groupDevices = groupInfoMapper.queryAllGroupInfo(groupParams);
        // DIP分组器
        HospitalVo groupDevice = groupDevices.stream().filter(vo -> vo.getGrperType().equals(DrgConst.GROUP_TYPE_DIP)).findAny().get();

        // 获取DIP标杆信息
        List<SomDipStandard> dipBenchmarkList = null;
        Map<String, List<SomDipStandard>> benchmarkMap = null;
        if (DrgConst.TYPE_1.equals(dto.getType())) {
            List<SomDipStandard> allDipBenchmarkList = tpdDipBenchmarkMapper.queryDipBenchmark(null);
            benchmarkMap = allDipBenchmarkList.stream().collect(Collectors.groupingBy(vo -> vo.getStandardYear() + vo.getHospitalId()));
        } else {
            dipBenchmarkList = tpdDipBenchmarkMapper.queryDipBenchmark(dto.getHospitalId());
        }

        //获取城乡城职单价
        List<DipConfigVo> dipConfigVos = dipConfigMapper.selectData();

//        //获取医院等级
//        String s1 = dipConfigMapper.selectHospitalLevel(dto.getHospitalId());
//
//        DipDiseaseTypeConfigDto dipDiseaseTypeConfigDto = new DipDiseaseTypeConfigDto();
//        List<DipDiseaseTypeVo> vos1 = dipDiseaseTypeConfigMapper.queryDipChineseDisease(dipDiseaseTypeConfigDto);
//        List<DipDiseaseTypeVo> vos2 = dipDiseaseTypeConfigMapper.queryDipBaseDisease(dipDiseaseTypeConfigDto);
//        List<DipDiseaseTypeVo> vos4 = dipDiseaseTypeConfigMapper.queryDipProfessionalDisease(dipDiseaseTypeConfigDto);
//        List<DipDiseaseTypeVo> vos5 = dipDiseaseTypeConfigMapper.queryDipDiseaseCoefficient(dipDiseaseTypeConfigDto);
//        List<DipDiseaseTypeVo> vos6 = dipDiseaseTypeConfigMapper.queryUnstableDisease(dipDiseaseTypeConfigDto);
//        List<Map<String, BigDecimal>> areaVo = dipDiseaseTypeConfigMapper.queryAreaData(dipDiseaseTypeConfigDto);
//
//        //获取当前医院信息
//        HospitalInfoDto hosDto=new HospitalInfoDto();
//        hosDto.setHospital(dto.getHospitalId());
//        List<ViewHospitalVo> viewHospitalVos = viewHospitalMapper.queryHospitalInfo(hosDto);
//
//        //获取当前医院级别
//        Map<String, BigDecimal> contrast = generaScoreJobService.getHospitalCoefficientContrast();
        // 获取配置信息
        Map<String, Object> params = benchmarkConfigServiceImpl.getParams();

        //结算清单校验规则
        Map<String, Object> settleListParams = settleListValidateJobService.getSettleListParams();

        Boolean data = false;

        if (DrgConst.TYPE_2.equals(dto.getType())) {
            medicalManagementMapper.deleteGroupsData(dto);
            medicalManagementMapper.deletePatientData(dto);
        }
        Integer start = 0, limit = 200, totals = 0;
        List<MedicalManagementVo> basicInfos = new ArrayList<>();
        do {
            if (basicInfos.size() > 0) {
                try {
//                            handleData(basicInfos, groupDevice, dipBenchmarkList, dipConfigVos, s1, vos1, vos2, vos4, vos5, vos6, areaVo, viewHospitalVos, contrast, settleListParams);
                    dipHandleData(basicInfos, groupDevice, dipBenchmarkList, dipConfigVos, settleListParams, params, benchmarkMap, dto.getType());
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
            dto.setStart(start);
            dto.setLimit(limit);
            basicInfos = medicalManagementMapper.queryBasicData(dto);
            totals = totals + basicInfos.size();
            start += limit;
        } while (basicInfos.size() > 0);
        data = true;
        return data;
    }

    /**
     * dip数据处理
     *
     * @param basicInfos
     * @param groupDevice
     * @param dipBenchmarkList
     * @param dipConfigVos
     * @param settleListParams
     * @param params
     * @param benchmarkMap
     * @param type
     */
    private void dipHandleData(List<MedicalManagementVo> basicInfos,
                               HospitalVo groupDevice,
                               List<SomDipStandard> dipBenchmarkList,
                               List<DipConfigVo> dipConfigVos,
                               Map<String, Object> settleListParams,
                               Map<String, Object> params,
                               Map<String, List<SomDipStandard>> benchmarkMap,
                               String type) throws InterruptedException, ExecutionException {
        ExecutorService threadPool = Executors.newFixedThreadPool(8);
        CompletionService<MedicalManagementDto> cs = new ExecutorCompletionService<>(threadPool);
        for (MedicalManagementVo basicInfo : basicInfos) {
            if (DrgConst.TYPE_1.equals(type) &&
                    ValidateUtil.isNotEmpty(basicInfo.getHospitalId()) &&
                    ValidateUtil.isNotEmpty(basicInfo.getOutHosTime())) {
                if (basicInfo.getOutHosTime().length() > 4) {
                    String year = basicInfo.getOutHosTime().substring(0, 4);
                    dipBenchmarkList = benchmarkMap.get(year + basicInfo.getHospitalId());
                }
            }
            if (ValidateUtil.isEmpty(dipBenchmarkList) && !ValidateUtil.isEmpty(benchmarkMap)) {
                dipBenchmarkList = benchmarkMap.get(new ArrayList<>(benchmarkMap.keySet()).get(0));
            }
            List<SomDipStandard> finalDipBenchmarkList = dipBenchmarkList;
            cs.submit(() -> patientGrouping(basicInfo, groupDevice, finalDipBenchmarkList, dipConfigVos, settleListParams, params));
        }

        List<MedicalManagementDto> managementDtos = new ArrayList<>();
        for (int i = 0; i < basicInfos.size(); i++) {
            MedicalManagementDto managementDto = cs.take().get();
            if (ValidateUtil.isNotEmpty(managementDto.getGroupsInfo2())) {
                managementDto.setType(DrgConst.GROUP_TYPE_DIP);
                List<DipPayToPredictVo> groupsInfo2 = managementDto.getGroupsInfo2();
                for (DipPayToPredictVo vo : groupsInfo2) {
                    vo.setForecast_fee(vo.getScorePrice().multiply(vo.getTotlSco()).setScale(4, RoundingMode.HALF_UP));
                    vo.setProfitloss(vo.getForecast_fee().subtract(vo.getSumfee()));
                }
                managementDtos.add(managementDto);
            }
        }
        if (!ValidateUtil.isEmpty(managementDtos)) {
            medicalManagementMapper.insertOptimizedMedical(managementDtos);
            medicalManagementMapper.insertRecommendGroup(managementDtos);
        }
        threadPool.shutdown();
    }

//    /**
//     * 处理数据
//     * @param basicInfos
//     * @param groupDevice
//     * @param dipBenchmarkList
//     * @param dipConfigVos
//     * @param s1
//     * @param vos1
//     * @param vos2
//     * @param vos4
//     * @param vos5
//     * @param vos6
//     * @param areaVo
//     * @param viewHospitalVos
//     * @param contrast
//     * @throws Exception
//     */
//    @Transactional(readOnly = false)
//    public void handleData(List<MedicalManagementVo> basicInfos,
//                            HospitalVo groupDevice,
//                            List<SomDipStandard> dipBenchmarkList,
//                            List<DipConfigVo> dipConfigVos,
//                            String s1,
//                            List<DipDiseaseTypeVo> vos1,
//                            List<DipDiseaseTypeVo> vos2,
//                            List<DipDiseaseTypeVo> vos4,
//                            List<DipDiseaseTypeVo> vos5,
//                            List<DipDiseaseTypeVo> vos6,
//                            List<Map<String, BigDecimal>> areaVo,
//                            List<ViewHospitalVo> viewHospitalVos,
//                            Map<String, BigDecimal> contrast,
//                            Map<String, Object> settleListParams) throws Exception {
//        ExecutorService threadPool = Executors.newFixedThreadPool(8);
//        CompletionService<MedicalManagementDto> cs = new ExecutorCompletionService<>(threadPool);
//        for (MedicalManagementVo basicInfo : basicInfos) {
//            cs.submit(() -> patientGrouping(basicInfo, groupDevice, dipBenchmarkList, dipConfigVos, s1, vos1, vos2, vos4, vos5, vos6, areaVo, viewHospitalVos, contrast, settleListParams));
//        }
//
//        List<MedicalManagementDto> managementDtos = new ArrayList<>();
//        for (int i = 0; i < basicInfos.size(); i++) {
//            MedicalManagementDto managementDto = cs.take().get();
//            if(!ValidateUtil.isEmpty(managementDto) && ValidateUtil.isNotEmpty(managementDto.getHospitalId())) {
//                managementDtos.add(managementDto);
//            }
//        }
//        if(!ValidateUtil.isEmpty(managementDtos)) {
//            medicalManagementMapper.deleteRecommendGroup(managementDtos);
//            medicalManagementMapper.deleteOptimizedMedical(managementDtos);
//
//            medicalManagementMapper.insertOptimizedMedical(managementDtos);
//            medicalManagementMapper.insertRecommendGroup(managementDtos);
//        }
//    }

    /**
     * 数据入组
     *
     * @param basicInfo
     * @param groupDevice
     * @param dipBenchmarkList
     * @param dipConfigVos
     * @return
     */
    private MedicalManagementDto patientGrouping(MedicalManagementVo basicInfo,
                                                 HospitalVo groupDevice,
                                                 List<SomDipStandard> dipBenchmarkList,
                                                 List<DipConfigVo> dipConfigVos,
//                                    String s1,
//                                    List<DipDiseaseTypeVo> vos1,
//                                    List<DipDiseaseTypeVo> vos2,
//                                    List<DipDiseaseTypeVo> vos4,
//                                    List<DipDiseaseTypeVo> vos5,
//                                    List<DipDiseaseTypeVo> vos6,
//                                    List<Map<String, BigDecimal>> areaVo,
//                                    List<ViewHospitalVo> viewHospitalVos,
//                                    Map<String, BigDecimal> contrast,
                                                 Map<String, Object> settleListParams,
                                                 Map<String, Object> params) {
        List<String> c06cs = new ArrayList<>();
        List<String> c35cs = new ArrayList<>();
        mergeCode(c06cs, basicInfo.getDscg_diag_codg());
        mergeCode(c35cs, basicInfo.getC35c());

        List<PreGroupVo> preGroupDipVos = new ArrayList<>();

        PreGroupDto preGroupDto = new PreGroupDto();
        preGroupDto.setZyzfy(basicInfo.getInHosTotalCost().add(Optional.ofNullable(basicInfo.getD02()).orElse(BigDecimal.ZERO)));
        preGroupDto.setNl(basicInfo.getAge());
        preGroupDto.setCykbbm(basicInfo.getDeptCode());
        preGroupDto.setJbdm(basicInfo.getWMCode());
        preGroupDto.setZyzd_jbbm(basicInfo.getTCMCode());
        preGroupDto.setRysj(basicInfo.getOutHosTime());
        preGroupDto.setHospital_id(basicInfo.getHospitalId());
        preGroupDto.setCblx(basicInfo.getInsuType());

        SettleListValidateVo settleListValidateVo = new SettleListValidateVo();
        SomHiInvyBasInfo somHiInvyBasInfo = new SomHiInvyBasInfo();
        somHiInvyBasInfo.setId(basicInfo.getSettleListId());
        settleListValidateVo.setSomHiInvyBasInfo(somHiInvyBasInfo);

        if (c06cs.size() > 0) {
            for (String dscg_diag_codg : c06cs) {
                DipGroupDataInfoVo dipGroupDataInfoVo = new DipGroupDataInfoVo();
                Map<String, String> codeMap = new HashMap<>();
                dipGroupDataInfoVo.setDscg_diag_codg(dscg_diag_codg);
                for (int i = 0; i < c35cs.size(); i++) {
                    codeMap.put("c35c_" + i, c35cs.get(i));
                }
                codeMap.put("c40c", basicInfo.getC40c());
                codeMap.put("c42c", basicInfo.getC42c());
                codeMap.put("c41c", basicInfo.getC41c());
                dipGroupDataInfoVo.setC06c_q(getOtherDisese(c06cs, dscg_diag_codg));
                BeanUtil.copyProperties(codeMap, dipGroupDataInfoVo);

                if (PreGroupUtil.callSettleCheck(dipGroupDataInfoVo, settleListParams, settleListValidateVo)) {
                    DipGroupResult groupResult = DipGroupUtil.group(dipGroupDataInfoVo, groupDevice, null, null);
                    if (!ValidateUtil.isEmpty(groupResult) &&
                            !ValidateUtil.isEmpty(groupResult.getSomDipGrpRcd()) &&
                            DrgConst.GROUP_STATE_1.equals(groupResult.getSomDipGrpRcd().getGrpStas())) {
                        PreGroupVo preGroupVo = PreGroupUtil.generaDipPreGroupInfo(groupResult, dipBenchmarkList);
                        preGroupVo.setDiagnoseCode(dscg_diag_codg);
                        preGroupDipVos.add(preGroupVo);
                    }
                }
            }
        }
        List<SomDipStandard> distinctNameBooks4 = PreGroupUtil.dipAiRecommend(preGroupDipVos, dipBenchmarkList, preGroupDto);
        List<PreGroupVo> adc = PreGroupUtil.tpdExchangePre(distinctNameBooks4);

//        List<DipPayToPredictVo> dipPayToPredictVos = PreGroupUtil.dipForecastCostList(dipConfigVos, adc, preGroupDto, s1, vos1, vos2, vos4, vos5, vos6, areaVo, viewHospitalVos, contrast);

        List<Map<String, Object>> resultMaps = PreGroupUtil.getForecastCostList(dipConfigVos, adc, preGroupDto, params);

        // 获取payToPredictVos
        List<DipPayToPredictVo> payToPredictVos = new ArrayList<>();
        for (Map<String, Object> resultMap :
                resultMaps) {
            payToPredictVos.add((DipPayToPredictVo) resultMap.get("payToPredictVo"));
        }
        payToPredictVos = payToPredictVos.stream().sorted(Comparator.comparing(DipPayToPredictVo::getTotlSco).reversed()).collect(Collectors.toList());


        List<DipPayToPredictVo> list = new ArrayList<>();
        if (!ValidateUtil.isEmpty(payToPredictVos)) {
            if (!ValidateUtil.isEmpty(basicInfo.getDipCodg())) {
                for (int i = 0; i < payToPredictVos.size(); i++) {
                    if (basicInfo.getDipCodg().equals(payToPredictVos.get(i).getDipCodg())) {
                        break;
                    } else {
                        payToPredictVos.get(i).setHospitalId(basicInfo.getHospitalId());
                        payToPredictVos.get(i).setSettleListId(String.valueOf(basicInfo.getSettleListId()));
                        payToPredictVos.get(i).setStandardFee(payToPredictVos.get(i).getLastYearLevelStandardCost());
                        list.add(payToPredictVos.get(i));
                    }
                }
            } else {
                for (int i = 0; i < payToPredictVos.size(); i++) {
                    payToPredictVos.get(i).setHospitalId(basicInfo.getHospitalId());
                    payToPredictVos.get(i).setSettleListId(String.valueOf(basicInfo.getSettleListId()));
                    payToPredictVos.get(i).setStandardFee(payToPredictVos.get(i).getLastYearLevelStandardCost());
                    list.add(payToPredictVos.get(i));
                }
            }
        }

        MedicalManagementDto medicalManagementDto = new MedicalManagementDto();
        if (!ValidateUtil.isEmpty(list)) {
            medicalManagementDto.setK00(basicInfo.getK00());
            medicalManagementDto.setSettleListId(String.valueOf(basicInfo.getSettleListId()));
            medicalManagementDto.setHospitalId(basicInfo.getHospitalId());
            medicalManagementDto.setName(basicInfo.getName());
            medicalManagementDto.setPatientId(basicInfo.getPatientId());
            medicalManagementDto.setAge(basicInfo.getAge());
            medicalManagementDto.setDipCodg(basicInfo.getDipCodg());
            medicalManagementDto.setDipName(basicInfo.getDipName());
            medicalManagementDto.setInHosTotalCost(basicInfo.getInHosTotalCost());
            medicalManagementDto.setPreHospExamfee(basicInfo.getD02());
            medicalManagementDto.setOutHosTime(basicInfo.getOutHosTime());
            medicalManagementDto.setDeptCode(basicInfo.getDeptCode());
            medicalManagementDto.setDrCodg(basicInfo.getDrCodg());
            medicalManagementDto.setGrperType(DrgConst.GROUP_TYPE_DIP);
            medicalManagementDto.setGroupsInfo2(list);
        }
        return medicalManagementDto;
    }

    /**
     * 设置其他诊断
     *
     * @param c06cs
     * @param dscg_diag_codg
     */
    private List<String> getOtherDisese(List<String> c06cs, String dscg_diag_codg) {
        List<String> otherDisese = new ArrayList<>();
        for (String disese :
                c06cs) {
            if (!disese.equals(dscg_diag_codg)) {
                otherDisese.add(disese);
            }
        }
        return otherDisese;
    }

    /**
     * 联合编码
     *
     * @param list
     * @param s
     */
    private void mergeCode(List<String> list, String s) {
        if (ValidateUtil.isNotEmpty(s)) {
            String[] codes = s.split(",");
            if (!ValidateUtil.isEmpty(codes)) {
                for (String code : codes) {
                    if (!code.equals("-")) {
                        list.add(code);
                    }
                }
            }
        }
    }

    /**
     * 查询患者信息
     *
     * @param dto
     * @return
     */
    @Override
    public List<MedicalManagementVo> queryPatientInfo(MedicalManagementDto dto) {
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        List<MedicalManagementVo> list = medicalManagementMapper.queryPatientInfo(dto);
        return list;
    }

    /**
     * 病组查询
     *
     * @param dto
     * @return
     */
    @Override
    public List<MedicalManagementVo> queryGroupsInfo(MedicalManagementDto dto) {
        List<MedicalManagementVo> groupsList = medicalManagementMapper.queryGroupsInfo(dto);
        return groupsList;
    }

    /**
     * DRG测算
     *
     * @param dto
     */
    @Override
    public void drgCalculationData(MedicalManagementDto dto) {

        // 设置分组器需要的参数
        HospitalDrgQueryParam groupParams = new HospitalDrgQueryParam();
        groupParams.setActive_flag(DrgConst.ACTIVE_FLAG_1);
        // 获取分组器
        List<HospitalVo> groupDevices = groupInfoMapper.queryAllGroupInfo(groupParams);
        HospitalVo groupDevice = groupDevices.stream().filter(vo -> vo.getGrperType().equals(DrgConst.GROUP_TYPE_DRG)).findAny().get();

        HospitalDrgVo drgVo = new HospitalDrgVo();
        drgVo.setDrgGroupType(groupDevice.getGrperType());
        drgVo.setGrperInfoId(groupDevice.getGrperInfoId());
        drgVo.setGrperUrlDrg(groupDevice.getGroupUrl());

        TpdDrgsDto tpdDrgsDto = new TpdDrgsDto();
        tpdDrgsDto.setGrper_type(groupDevices.stream().filter(hospitalVo -> hospitalVo.getGrperType().equals(DrgConst.GROUP_TYPE_DRG)).findAny().get().getGrperType());
        Map<String, SomDrgNameVo> tpdDRGVoMap = dataRecordService.queryTpdDrgsVo(tpdDrgsDto);

        // 获取DRG标杆信息
        List<SomDrgStandard> drgBenchmarkList = busBenchmarkMapper.selectByExample(new SomDrgStandardExample());
        if (ValidateUtil.isEmpty(drgBenchmarkList)) {
            throw new AppException("无DRG标杆数据");
        }
        Map<String, List<SomDrgStandard>> benchmarkMap = drgBenchmarkList.stream().collect(Collectors.groupingBy(vo -> vo.getStandardYear() + vo.getHospitalId()));

        // 获取城乡城职单价
        List<DipConfigVo> drgConfigVos = drgConfigMapper.selectDrgData(null);

        // 获取医院等级
        String dept_lv = dipConfigMapper.selectHospitalLevel(dto.getHospitalId());

        // 获取当前医院信息
        HospitalInfoDto hosDto = new HospitalInfoDto();
        hosDto.setHospital(dto.getHospitalId());
        List<ViewHospitalVo> viewHospitalVos = viewHospitalMapper.queryHospitalInfo(hosDto);

        // 获取当前医院级别
        Map<String, BigDecimal> contrast = generaScoreJobService.getHospitalCoefficientContrast();

        if (DrgConst.TYPE_2.equals(dto.getType())) {
            medicalManagementMapper.deleteGroupsData(dto);
            medicalManagementMapper.deletePatientData(dto);
        }

        Integer start = 0, limit = 200, totals = 0;
        List<MedicalManagementVo> basicInfos = new ArrayList<>();
        do {
            if (basicInfos.size() > 0) {
                // 患者数据处理
                handlePatientData(basicInfos, drgVo, tpdDRGVoMap, drgBenchmarkList, drgConfigVos, dept_lv, viewHospitalVos, contrast, benchmarkMap);
            }
            dto.setStart(start);
            dto.setLimit(limit);
            // 查询患者基础信息
            basicInfos = medicalManagementMapper.queryBasicData(dto);
            totals = totals + basicInfos.size();
            start += limit;
        } while (basicInfos.size() > 0);
    }

    /**
     * 处理drg患者数据
     *
     * @param basicInfos
     * @param drgVo
     * @param tpdDRGVoMap
     * @param drgBenchmarkList
     * @param drgConfigVos
     * @param dept_lv
     * @param viewHospitalVos
     * @param contrast
     * @param benchmarkMap
     */
    @Transactional(readOnly = false)
    public Boolean handlePatientData(List<MedicalManagementVo> basicInfos,
                                     HospitalDrgVo drgVo,
                                     Map<String, SomDrgNameVo> tpdDRGVoMap,
                                     List<SomDrgStandard> drgBenchmarkList,
                                     List<DipConfigVo> drgConfigVos,
                                     String dept_lv,
                                     List<ViewHospitalVo> viewHospitalVos,
                                     Map<String, BigDecimal> contrast,
                                     Map<String, List<SomDrgStandard>> benchmarkMap) {
        Boolean handleResult = false;
        Integer cnt = 0;
        ExecutorService threadPool = Executors.newFixedThreadPool(8);
        CompletionService<MedicalManagementDto> cs = new ExecutorCompletionService<>(threadPool);
        for (MedicalManagementVo basicInfo : basicInfos) {

            if (!ValidateUtil.isEmpty(basicInfo.getHospitalId())){
                String year = "";
                if(!ValidateUtil.isEmpty(basicInfo.getSetlHosTime()) && basicInfo.getSetlHosTime().length() > 4){
                        year = basicInfo.getSetlHosTime().substring(0, 4);
                }else if (!ValidateUtil.isEmpty(basicInfo.getOutHosTime()) && basicInfo.getOutHosTime().length() > 4) {
                        year = basicInfo.getOutHosTime().substring(0, 4);
                }

                if (!ValidateUtil.isEmpty(year)) {
                   drgBenchmarkList = benchmarkMap.get(year + basicInfo.getHospitalId());
                }
            }
            if(!ValidateUtil.isEmpty(drgBenchmarkList)){
                drgBenchmarkList = benchmarkMap.get(new ArrayList<>(benchmarkMap.keySet()).get(0));
            }
            List<SomDrgStandard> finalDrgBenchmarkList = drgBenchmarkList;
            cs.submit(() -> handleCode(basicInfo, drgVo, tpdDRGVoMap, finalDrgBenchmarkList, drgConfigVos, dept_lv, viewHospitalVos, contrast));
//            MedicalManagementDto medicalManagementDto = handleCode(basicInfo, drgVo, tpdDRGVoMap, drgBenchmarkList, drgConfigVos, dept_lv, viewHospitalVos, contrast);
        }

        List<MedicalManagementDto> managementDtos = new ArrayList<>();
        for (int i = 0; i < basicInfos.size(); i++) {
            try {
                MedicalManagementDto managementDto = cs.take().get();
                if (!ValidateUtil.isEmpty(managementDto.getGroupsInfo())) {
                    managementDto.setType(DrgConst.GROUP_TYPE_DRG);
                    managementDtos.add(managementDto);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }

        }
        if (!ValidateUtil.isEmpty(managementDtos)) {
            medicalManagementMapper.insertOptimizedMedical(managementDtos);
            medicalManagementMapper.insertRecommendGroup(managementDtos);
        }

        if (ValidateUtil.isNotEmpty(basicInfos)) {
            if (cnt.equals(basicInfos.size())) {
                handleResult = true;
            }
        }
        threadPool.shutdown();
        return handleResult;
    }

    /**
     * 处理drg编码
     *
     * @param basicInfo
     * @param drgVo
     * @param tpdDRGVoMap
     * @param drgBenchmarkList
     * @param drgConfigVos
     * @param dept_lv
     * @param viewHospitalVos
     * @param contrast
     * @return
     */
    private MedicalManagementDto handleCode(MedicalManagementVo basicInfo,
                                            HospitalDrgVo drgVo,
                                            Map<String, SomDrgNameVo> tpdDRGVoMap,
                                            List<SomDrgStandard> drgBenchmarkList,
                                            List<DipConfigVo> drgConfigVos,
                                            String dept_lv,
                                            List<ViewHospitalVo> viewHospitalVos,
                                            Map<String, BigDecimal> contrast) {

        List<PreGroupVo> preGroupDrgVos = new ArrayList<>();

        PreGroupDto preGroupDto = new PreGroupDto();
        preGroupDto.setZyzfy(basicInfo.getInHosTotalCost().add(Optional.ofNullable(basicInfo.getD02()).orElse(BigDecimal.ZERO)));
        preGroupDto.setNl(basicInfo.getAge());
        preGroupDto.setCykbbm(basicInfo.getDeptCode());
        preGroupDto.setJbdm(basicInfo.getWMCode());
        preGroupDto.setZyzd_jbbm(basicInfo.getTCMCode());
        preGroupDto.setRysj(basicInfo.getOutHosTime());
        preGroupDto.setHospital_id(basicInfo.getHospitalId());
        preGroupDto.setCblx(basicInfo.getInsuType());

        List<String> c06cs = new ArrayList<>();
        List<String> c35cs = new ArrayList<>();
        mergeCode(c06cs, basicInfo.getDscg_diag_codg());
        mergeCode(c35cs, basicInfo.getC35c());

        Integer cnt = 0;


        if (ValidateUtil.isNotEmpty(c06cs)) {
            for (String dscg_diag_codg : c06cs) {
                handleDrgData(c06cs, dscg_diag_codg, c35cs, cnt, basicInfo, drgVo, tpdDRGVoMap, drgBenchmarkList, preGroupDrgVos, drgConfigVos, preGroupDto, dept_lv, viewHospitalVos, contrast);
                cnt++;
            }
        }

        List<SomDrgStandard> busBenchmarks = DrgPreGroupUtil.drgAiRecommend(preGroupDrgVos, drgBenchmarkList, preGroupDto);
        List<PreGroupVo> preGroupVos = DrgPreGroupUtil.tpdExchangePre(busBenchmarks, preGroupDto.getCblx());
        List<DipPayToPredictVo> dipPayToPredictVos = DrgPreGroupUtil.drgForecastCostList(drgConfigVos, preGroupVos, preGroupDto, dept_lv, viewHospitalVos, contrast, false);
        dipPayToPredictVos = dipPayToPredictVos.stream().sorted(Comparator.comparing(DipPayToPredictVo::getZfz).reversed()).collect(Collectors.toList());

        List<DipPayToPredictVo> payToPredictVos = new ArrayList<>();
        if (!ValidateUtil.isEmpty(dipPayToPredictVos)) {
            if (!ValidateUtil.isEmpty(basicInfo.getDrgCodg())) {
                for (int i = 0; i < dipPayToPredictVos.size(); i++) {
                    if (dipPayToPredictVos.get(i).getDrgCodg().equals(basicInfo.getDrgCodg())) {
                        break;
                    } else {
                        DipPayToPredictVo vo = dipPayToPredictVos.get(i);
                        vo.setSettleListId(String.valueOf(basicInfo.getSettleListId()));
                        vo.setHospitalId(basicInfo.getHospitalId());
                        vo.setDipCodg(vo.getDrgCodg());
                        vo.setDipName(vo.getDrgName());
                        vo.setStandardFee(vo.getLevelStandardCost());
                        payToPredictVos.add(vo);
                    }
                }
            } else {
                for (int i = 0; i < dipPayToPredictVos.size(); i++) {
                    dipPayToPredictVos.get(i).setDipCodg(dipPayToPredictVos.get(i).getDrgCodg());
                    dipPayToPredictVos.get(i).setDipName(dipPayToPredictVos.get(i).getDrgName());
                    dipPayToPredictVos.get(i).setHospitalId(basicInfo.getHospitalId());
                    dipPayToPredictVos.get(i).setSettleListId(String.valueOf(basicInfo.getSettleListId()));
                    dipPayToPredictVos.get(i).setStandardFee(dipPayToPredictVos.get(i).getLevelStandardCost());
                    payToPredictVos.add(dipPayToPredictVos.get(i));
                }
            }

        }

        MedicalManagementDto medicalManagementDto = new MedicalManagementDto();
        if (!ValidateUtil.isEmpty(payToPredictVos)) {
            medicalManagementDto.setK00(basicInfo.getK00());
            medicalManagementDto.setSettleListId(String.valueOf(basicInfo.getSettleListId()));
            medicalManagementDto.setHospitalId(basicInfo.getHospitalId());
            medicalManagementDto.setName(basicInfo.getName());
            medicalManagementDto.setPatientId(basicInfo.getPatientId());
            medicalManagementDto.setAge(basicInfo.getAge());
            medicalManagementDto.setDipCodg(basicInfo.getDrgCodg());
            medicalManagementDto.setDipName(basicInfo.getDrgName());
            medicalManagementDto.setInHosTotalCost(basicInfo.getInHosTotalCost());
            medicalManagementDto.setPreHospExamfee(basicInfo.getD02());
            medicalManagementDto.setOutHosTime(basicInfo.getOutHosTime());
            medicalManagementDto.setDeptCode(basicInfo.getDeptCode());
            medicalManagementDto.setDrCodg(basicInfo.getDrCodg());
            medicalManagementDto.setGrperType(DrgConst.GROUP_TYPE_DRG);
            medicalManagementDto.setGroupsInfo(payToPredictVos);
        }
        return medicalManagementDto;
    }

    /**
     * 处理drg数据
     *
     * @param c06cs
     * @param dscg_diag_codg
     * @param c35cs
     * @param cnt
     * @param basicInfo
     * @param drgVo
     * @param tpdDRGVoMap
     * @param drgBenchmarkList
     * @param preGroupDrgVos
     * @param drgConfigVos
     * @param preGroupDto
     * @param dept_lv
     * @param viewHospitalVos
     * @param contrast
     * @return
     */
    private void handleDrgData(List<String> c06cs,
                               String dscg_diag_codg,
                               List<String> c35cs,
                               Integer cnt,
                               MedicalManagementVo basicInfo,
                               HospitalDrgVo drgVo,
                               Map<String, SomDrgNameVo> tpdDRGVoMap,
                               List<SomDrgStandard> drgBenchmarkList,
                               List<PreGroupVo> preGroupDrgVos,
                               List<DipConfigVo> drgConfigVos,
                               PreGroupDto preGroupDto,
                               String dept_lv,
                               List<ViewHospitalVo> viewHospitalVos,
                               Map<String, BigDecimal> contrast) {
        try {
            CaseYB2020Vo caseYB2020Vo = new CaseYB2020Vo();
            handleGroupData(caseYB2020Vo, basicInfo, c06cs, c35cs, cnt);
            PreGroupVo preGroupVo = new PreGroupVo();
            DrgsResultVo groupResult = DrgYB2020GroupUtil.excuteDrgGroup(caseYB2020Vo, drgVo);
            if (!ValidateUtil.isEmpty(groupResult)
                    && !ValidateUtil.isEmpty(groupResult.getSomDrgGrpRcd()) &&
                    DrgConst.GROUP_STATE_1.equals(groupResult.getSomDrgGrpRcd().getGrpStas())) {
                SomDrgNameVo tpdDrgsVo = tpdDRGVoMap.get(groupResult.getSomDrgGrpRcd().getDrgCodg());
                if (!ValidateUtil.isEmpty(tpdDrgsVo)) {
                    preGroupVo.setDrgCodg(groupResult.getSomDrgGrpRcd().getDrgCodg());
                    preGroupVo.setDrgName(tpdDrgsVo.getDrgName());
                    preGroupVo.setDiagnoseCode(dscg_diag_codg);
                    preGroupVo.setCblx(preGroupDto.getCblx());
                    DrgPreGroupUtil.setDrgBenchmark(preGroupVo, drgBenchmarkList);
                    preGroupVo.setDiagnoseCode(dscg_diag_codg);
                    preGroupDrgVos.add(preGroupVo);
                }
            }

        } catch (Exception e) {
            e.printStackTrace();
            throw new AppException("Drg分组异常");
        }
    }

    /**
     * 处理排序及选择组数据
     *
     * @param caseYB2020Vo
     * @param basicInfo
     * @param c06cs
     * @param c35cs
     * @param cnt
     */
    private void handleGroupData(CaseYB2020Vo caseYB2020Vo, MedicalManagementVo basicInfo, List<String> c06cs, List<String> c35cs, Integer cnt) {

        caseYB2020Vo.setA14(basicInfo.getAge());
        caseYB2020Vo.setA12c(basicInfo.getA12c());
        caseYB2020Vo.setSf0100(basicInfo.getA16());
        caseYB2020Vo.setA17(basicInfo.getA17());
        caseYB2020Vo.setB34c(basicInfo.getB34c());
        caseYB2020Vo.setB20(basicInfo.getInHosDays());
        caseYB2020Vo.setD01(String.valueOf(basicInfo.getInHosTotalCost()));

        List<String> zdbm = new ArrayList<>();
        zdbm.addAll(c06cs);
        String s = "";
        String t = "";
        if (ValidateUtil.isNotEmpty(zdbm)) {
            s = zdbm.get(0);
            t = zdbm.get(cnt);
            zdbm.set(0, t);
            zdbm.set(cnt, s);
        }
        List<SomDiag> somDiag = new ArrayList<>();
        if (ValidateUtil.isNotEmpty(zdbm)) {
            for (int i = 0; i < zdbm.size(); i++) {
                SomDiag diagnosis = new SomDiag();
                diagnosis.setDscg_diag_codg(zdbm.get(i));
                diagnosis.setSeq(i);
                somDiag.add(diagnosis);
            }
        }
        caseYB2020Vo.setSomDiag(somDiag);

        List<SomOprnOprtInfo> somOprnOprtInfo = new ArrayList<>();
        if (ValidateUtil.isNotEmpty(c35cs)) {
            for (int i = 0; i < c35cs.size(); i++) {
                SomOprnOprtInfo operateDiagnosis = new SomOprnOprtInfo();
                operateDiagnosis.setC35c(c35cs.get(i));
                operateDiagnosis.setSeq(i);
                somOprnOprtInfo.add(operateDiagnosis);
            }
        }
        caseYB2020Vo.setSomOprnOprtInfo(somOprnOprtInfo);
    }

}
