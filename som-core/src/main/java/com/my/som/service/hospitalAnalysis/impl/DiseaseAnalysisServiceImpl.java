package com.my.som.service.hospitalAnalysis.impl;

import com.github.pagehelper.PageHelper;
import com.my.som.common.constant.DrgConst;
import com.my.som.dao.hospitalAnalysis.DiseaseAnalysisDao;
import com.my.som.dto.hospitalAnalysis.HospitalAnalysisQueryParam;
import com.my.som.service.hospitalAnalysis.DiseaseAnalysisService;
import com.my.som.vo.common.CommonObject;
import com.my.som.vo.hospitalAnalysis.CostAnalysisInfo;
import com.my.som.vo.hospitalAnalysis.DiseaseAnalysisInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Service实现类
 * Created by sky on 2020/1/5.
 */
@Service
public class DiseaseAnalysisServiceImpl implements DiseaseAnalysisService {
    @Autowired
    private DiseaseAnalysisDao diseaseAnalysisDao;


    @Override
    public List<DiseaseAnalysisInfo> list(HospitalAnalysisQueryParam queryParam, Integer pageSize, Integer pageNum) {
        PageHelper.startPage(pageNum, pageSize);
        queryParam.setActiveFlag(DrgConst.ACTIVE_FLAG_1);
        //queryParam.setEnabFlag(DrgConst.START_FLAG_1);//查询医院有权限看的疾病
        int totalPatients = 0;
        //BigDecimal sumfee = new BigDecimal("0");
        BigDecimal totalInHosCost = new BigDecimal("0");
        List<DiseaseAnalysisInfo> list = diseaseAnalysisDao.list(queryParam);
        for(DiseaseAnalysisInfo dai:list){
            totalPatients = totalPatients + Integer.parseInt(dai.getTotalPatients());
//            sumfee = sumfee.add(new BigDecimal(dai.getSumfee()));
            totalInHosCost = totalInHosCost.add(new BigDecimal(dai.getTotalInHosCost()));
        }
        if(totalPatients>0){
            for(DiseaseAnalysisInfo dai:list){
                dai.setTotalPatientsRate(String.valueOf(new BigDecimal((float)Integer.parseInt(dai.getTotalPatients())/totalPatients*100).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue()));
            }
        }
//        if(sumfee.compareTo(BigDecimal.ZERO)>0){
//            for(DiseaseAnalysisInfo dai:list){
//                dai.setTotalCostRate(String.valueOf(new BigDecimal(dai.getSumfee()).divide(sumfee,4, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal("100")))+"%");
//            }
//        }
        if(totalInHosCost.compareTo(BigDecimal.ZERO)>0){
            for(DiseaseAnalysisInfo dai:list){
                BigDecimal val = new BigDecimal(dai.getTotalInHosCost())
                        .divide(totalInHosCost,8, BigDecimal.ROUND_HALF_UP)
                        .multiply(new BigDecimal("100"))
                        .setScale(2,BigDecimal.ROUND_HALF_UP);
                dai.setTotalInHosCostRate(val.toString());
            }
        }
        return list;
    }

    @Override
    public List<CommonObject> getCountInfo(HospitalAnalysisQueryParam queryParam) {
        List<CommonObject> list = new ArrayList<>();
        Map<String,String> diseaseCountInfo = diseaseAnalysisDao.getCountInfo(queryParam);
        CommonObject object0 = new CommonObject();
        object0.setName("某些传染病和寄生虫病");
        object0.setValue(String.valueOf(diseaseCountInfo.get("a00b99")));
        list.add(object0);
        CommonObject object1 = new CommonObject();
        object1.setName("肿瘤");
        object1.setValue(String.valueOf(diseaseCountInfo.get("c00d48")));
        list.add(object1);
        CommonObject object2 = new CommonObject();
        object2.setName("血液及造血器官疾病和涉及免疫机制的某些疾患");
        object2.setValue(String.valueOf(diseaseCountInfo.get("d50d89")));
        list.add(object2);
        CommonObject object3 = new CommonObject();
        object3.setName("内分泌、营养和代谢疾病");
        object3.setValue(String.valueOf(diseaseCountInfo.get("e00e90")));
        list.add(object3);
        CommonObject object4 = new CommonObject();
        object4.setName("精神和行为障碍");
        object4.setValue(String.valueOf(diseaseCountInfo.get("f00f99")));
        list.add(object4);
        CommonObject object5 = new CommonObject();
        object5.setName("神经系统疾病");
        object5.setValue(String.valueOf(diseaseCountInfo.get("g00g99")));
        list.add(object5);
        CommonObject object6 = new CommonObject();
        object6.setName("眼和附器疾病及耳和乳突疾病");
        object6.setValue(String.valueOf(diseaseCountInfo.get("h00h59")));
        list.add(object6);
        CommonObject object61 = new CommonObject();
        object61.setName("耳和乳突疾病");
        object61.setValue(String.valueOf(diseaseCountInfo.get("h60h95")));
        list.add(object61);
        CommonObject object7 = new CommonObject();
        object7.setName("循环系统疾病");
        object7.setValue(String.valueOf(diseaseCountInfo.get("i00i99")));
        list.add(object7);
        CommonObject object8 = new CommonObject();
        object8.setName("呼吸系统疾病");
        object8.setValue(String.valueOf(diseaseCountInfo.get("j00j99")));
        list.add(object8);
        CommonObject object9 = new CommonObject();
        object9.setName("消化系统疾病");
        object9.setValue(String.valueOf(diseaseCountInfo.get("k00k93")));
        list.add(object9);
        CommonObject object10 = new CommonObject();
        object10.setName("皮肤和皮下组织疾病");
        object10.setValue(String.valueOf(diseaseCountInfo.get("l00l99")));
        list.add(object10);
        CommonObject object11 = new CommonObject();
        object11.setName("肌肉骨骼系统和结缔组织疾病");
        object11.setValue(String.valueOf(diseaseCountInfo.get("m00m99")));
        list.add(object11);
        CommonObject object12 = new CommonObject();
        object12.setName("泌尿生殖系统疾病");
        object12.setValue(String.valueOf(diseaseCountInfo.get("n00n99")));
        list.add(object12);
        CommonObject object13 = new CommonObject();
        object13.setName("妊娠、分娩和产褥期");
        object13.setValue(String.valueOf(diseaseCountInfo.get("o00o99")));
        list.add(object13);
        CommonObject object14 = new CommonObject();
        object14.setName("起源于围生期的某些情况");
        object14.setValue(String.valueOf(diseaseCountInfo.get("p00p96")));
        list.add(object14);
        CommonObject object15 = new CommonObject();
        object15.setName("先天性畸形、变形和染色体异常");
        object15.setValue(String.valueOf(diseaseCountInfo.get("q00q99")));
        list.add(object15);
        CommonObject object16 = new CommonObject();
        object16.setName("症状、体征和临床与实验室异常所见，不可归类在他处者");
        object16.setValue(String.valueOf(diseaseCountInfo.get("r00r99")));
        list.add(object16);
        CommonObject object17 = new CommonObject();
        object17.setName("损伤、中毒和外因的某些其他后果");
        object17.setValue(String.valueOf(diseaseCountInfo.get("s00t98")));
        list.add(object17);
        CommonObject object18 = new CommonObject();
        object18.setName("疾病和死亡的外因");
        object18.setValue(String.valueOf(diseaseCountInfo.get("v01y98")));
        list.add(object18);
        CommonObject object19 = new CommonObject();
        object19.setName("用于特殊目的的编码");
        object19.setValue(String.valueOf(diseaseCountInfo.get("u00u99")));
        list.add(object19);
        CommonObject object20 = new CommonObject();
        object20.setName("影响健康状态和与保健机构接触的因素");
        object20.setValue(String.valueOf(diseaseCountInfo.get("z00z99")));
        list.add(object20);
        CommonObject object21 = new CommonObject();
        object21.setName("其他");
        object21.setValue(String.valueOf(diseaseCountInfo.get("other")));
        list.add(object21);
        list.sort((o1, o2) -> new BigDecimal(o2.getValue()).compareTo(new BigDecimal(o1.getValue()))); //按照人次降序排列，最后加入总人次
        CommonObject object22 = new CommonObject();
        object22.setName("总人次");
        object22.setValue(String.valueOf(diseaseCountInfo.get("totalPatients")));
        list.add(object22);
        return list;
    }
}
