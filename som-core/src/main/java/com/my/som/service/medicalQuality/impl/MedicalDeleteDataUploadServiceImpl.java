package com.my.som.service.medicalQuality.impl;

import com.my.som.common.util.EasyPoiUtil;
import com.my.som.common.util.ValidateUtil;
import com.my.som.dao.medicalQuality.SettleListManageDao;
import com.my.som.dto.medicalQuality.BusSettleListMainInfo;
import com.my.som.dto.medicalQuality.SettleListMainInfoQueryParam;
import com.my.som.dto.upload.MedicalRecordUploadDto;
import com.my.som.entity.upload.MedicalDeleteData;
import com.my.som.service.medicalQuality.MedicalDeleteDataUploadService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Service
@Slf4j
@Transactional
public class MedicalDeleteDataUploadServiceImpl implements MedicalDeleteDataUploadService {

    @Autowired
    private SettleListManageDao settleListManageDao;

    List<String> TABLE_NAMES = Arrays.asList("som_drg_grp_rcd",
                                             "som_drg_grper_intf_trns_log",
                                             "som_dip_grp_rcd",
                                             "som_dip_grper_intf_trns_log",
                                             "som_grp_rcd",
                                             "som_cd_grper_intf_trns_log",
                                             "som_drg_grp_info",
                                             "som_dip_grp_info",
                                             "som_cd_grp_info",
                                             "som_codg_resu_adjm_rcd",
                                             "som_setl_invy_chk_err_rcd",
                                             "som_setl_invy_qlt_dedu_point_detl",
                                             "som_dip_sco",
                                             "som_drg_sco",
                                             "som_setl_invy_chk",
                                             "som_invy_chk_detl",
                                             "som_setl_invy_scs_cutd_info",
                                             "som_fund_pay",
                                             "som_hi_setl_invy_med_fee_info",
                                             "som_diag",
                                             "som_oprn_oprt_info",
                                             "som_otp_slow_special_trt_info");

    @Override
    public void medicalDeleteDataUpload(MultipartFile file, MedicalRecordUploadDto dto) {
        try {
            List<MedicalDeleteData> medicalRecords = EasyPoiUtil.importExcel(file, MedicalDeleteData.class);
            List<Long> list = new ArrayList<>();
            for (MedicalDeleteData medicalRecord : medicalRecords) {
                BusSettleListMainInfo infos = settleListManageDao.selectMedicalDeleteData(medicalRecord);
                if(!ValidateUtil.isEmpty(infos)) {
                    list.add(infos.getId());
                }
            }
            SettleListMainInfoQueryParam param = new SettleListMainInfoQueryParam();
            param.setTableNames(TABLE_NAMES);
            int i = settleListManageDao.deletePatientAllTableByIdAndHospitalId(param);
            if(i >= 0) {
                param.setTableNames(Arrays.asList("som_hi_invy_bas_info"));
                settleListManageDao.deletePatientAllTableByIdAndHospitalId(param);
            }
        } catch(IOException e) {
            e.printStackTrace();
        }
    }
}
