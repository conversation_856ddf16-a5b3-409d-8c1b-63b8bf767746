package com.my.som.service.hosPerfAppraisal.impl;

import com.github.pagehelper.PageHelper;
import com.my.som.common.constant.DrgConst;
import com.my.som.common.exception.AppException;
import com.my.som.common.util.ValidateUtil;
import com.my.som.dto.hosPerfAppraisal.HosPerfAppraisalDsItemDto;
import com.my.som.mapper.hosPerfAppraisal.HosPerfAppraisalDsItemMapper;
import com.my.som.service.hosPerfAppraisal.HosPerfAppraisalDsItemService;
import com.my.som.vo.hosPerfAppraisal.HosPerfAppraisalDsItemVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @author: zyd
 * @version 1.0
 * @date: 2023-07-31
 * @description:
 */
@Service
public class HosPerfAppraisalDsItemServiceImpl implements HosPerfAppraisalDsItemService {

    @Autowired
    private HosPerfAppraisalDsItemMapper hosPerfAppraisalDsItemMapper;

    @Override
    public List<HosPerfAppraisalDsItemVo> queryList(HosPerfAppraisalDsItemDto dto) {
        PageHelper.startPage(dto);
        return hosPerfAppraisalDsItemMapper.queryList(dto);
    }

    @Override
    public void add(HosPerfAppraisalDsItemDto dto) {
        dto.setActiveFlag(DrgConst.ACTIVE_FLAG_1);
        // 查询编码是否存在
        HosPerfAppraisalDsItemDto hosPerfAppraisalDsItemDto = new HosPerfAppraisalDsItemDto();
        hosPerfAppraisalDsItemDto.setDsItemCode(dto.getDsItemCode());
        List<HosPerfAppraisalDsItemVo> hosPerfAppraisalDsItemVos = hosPerfAppraisalDsItemMapper.queryList(hosPerfAppraisalDsItemDto);
        if (ValidateUtil.isNotEmpty(hosPerfAppraisalDsItemVos)) {
            throw new AppException("编码已存在");
        }
        hosPerfAppraisalDsItemMapper.add(dto);
    }

    @Override
    public void update(HosPerfAppraisalDsItemDto dto) {
        hosPerfAppraisalDsItemMapper.update(dto);
    }

    @Override
    public void remove(HosPerfAppraisalDsItemDto dto) {
        if (dto.getId() != null) {
            hosPerfAppraisalDsItemMapper.remove(dto);
        }
    }
}
