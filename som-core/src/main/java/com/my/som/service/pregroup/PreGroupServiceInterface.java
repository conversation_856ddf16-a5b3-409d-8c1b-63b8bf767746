package com.my.som.service.pregroup;

import com.my.som.dto.common.HospitalDrgQueryParam;
import com.my.som.dto.patienInfo.PatienInfo;
import com.my.som.dto.pregroup.PreGroup2Base;
import com.my.som.dto.pregroup.PreGroup2Dto;
import com.my.som.dto.pregroup.PreGroupBasicDto;
import com.my.som.dto.pregroup.PreGroupDto;
import com.my.som.model.dataHandle.SomDrgStandard;
import com.my.som.model.dataHandle.SomDrgStandardExample;
import com.my.som.vo.dataHandle.dataGroup.HospitalVo;
import com.my.som.vo.pregroup.PreGroupVo;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * @author: zyd
 * @Date 2021/7/20 7:01 下午
 * @Version 1.0
 * @Description: 预分组
 */
public interface PreGroupServiceInterface {


    /**
     * 构建img流
     *
     * @param dto
     * @param response
     */
    void writeImg(PreGroupDto dto, HttpServletResponse response);

    /**
     * 获取预分组数据
     *
     * @param dto
     * @param response
     * @param request
     * @return
     */
    PreGroupVo getPreGroupInfo(PreGroupDto dto, HttpServletResponse response, HttpServletRequest request);

    /**
     * 获取预分组数据
     *
     * @param dto
     * @param response
     * @param request
     * @return
     */
    PreGroupVo getPreGroupInfo3(PreGroupDto dto, HttpServletResponse response, HttpServletRequest request);

    PreGroupVo getPreGroupInfo4(PreGroupDto dto, HttpServletResponse response, HttpServletRequest request);

    /**
     * 检查是否可用
     *
     * @param json
     * @return
     */
    PreGroupVo check(String json, HttpServletResponse response, HttpServletRequest request);

    /**
     * 预分组
     *
     * @param dto
     * @param response
     * @param request
     * @return
     */
    PreGroupVo getPreGroupInfo5(PreGroup2Dto dto, HttpServletResponse response, HttpServletRequest request);

    /**
     * 基层机构预分组
     *
     * @param dto
     * @param response
     * @param request
     * @return
     */
    PreGroupVo getPreGroupInfoByBasicDto(PreGroupBasicDto dto, HttpServletResponse response, HttpServletRequest request);


    Map<String, Object> selectYb14FeeByBah(String a48);


    /**
     * 模拟预分组结果
     */
    Map<String, Object> getPreGroupResult(PatienInfo patienInfo);
}
