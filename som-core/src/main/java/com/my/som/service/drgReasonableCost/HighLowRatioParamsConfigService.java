package com.my.som.service.drgReasonableCost;

import com.my.som.dto.drgReasonableCost.DrgReasonableCostQueryParam;
import com.my.som.vo.drgReasonableCost.HighLowRatioParamsConfigVo;


/**
 * Service
 * Created by sky on 2020/3/17.
 */
public interface HighLowRatioParamsConfigService {
    /**
     * 获取高低费率参数
     * @param queryParam
     * @return
     */
    HighLowRatioParamsConfigVo getAllParams(DrgReasonableCostQueryParam queryParam);

    /**
     * 更新高低费率参数
     * @param listQuery
     * @return
     */
    int updateAllInfo(HighLowRatioParamsConfigVo listQuery);
}
