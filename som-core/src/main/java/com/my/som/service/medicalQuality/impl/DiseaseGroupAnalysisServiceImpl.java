package com.my.som.service.medicalQuality.impl;

import com.github.pagehelper.PageHelper;
import com.my.som.dao.medicalQuality.DiseaseGroupAnalysisMapper;
import com.my.som.dto.medicalQuality.DiseaseGroupAnalysisDto;
import com.my.som.service.medicalQuality.DiseaseGroupAnalysisService;
import com.my.som.vo.medicalQuality.DiseaseGroupAnalysisVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
@Service
public class DiseaseGroupAnalysisServiceImpl implements DiseaseGroupAnalysisService {
    @Autowired
    private DiseaseGroupAnalysisMapper diseaseGroupAnalysisMapper;
    @Override
    public List<DiseaseGroupAnalysisVo> getList(DiseaseGroupAnalysisDto dto, Integer pageSize, Integer pageNum) {
        PageHelper.startPage(pageNum, pageSize);
        return diseaseGroupAnalysisMapper.getList(dto);
    }

    @Override
    public List<DiseaseGroupAnalysisVo> getInfo(DiseaseGroupAnalysisDto dto) {
        return diseaseGroupAnalysisMapper.getInfo(dto);
    }

    @Override
    public List<DiseaseGroupAnalysisVo> getIsInGroup(DiseaseGroupAnalysisDto dto) {
        return diseaseGroupAnalysisMapper.getCostPay(dto);
    }

    @Override
    public List<DiseaseGroupAnalysisVo> getCostPay(DiseaseGroupAnalysisDto dto) {
        return diseaseGroupAnalysisMapper.getCostPay(dto);
    }

    @Override
    public List<DiseaseGroupAnalysisVo> getCost(DiseaseGroupAnalysisDto dto) {
        return diseaseGroupAnalysisMapper.getCost(dto);
    }
}
