package com.my.som.service.hospitalAnalysis;

import com.my.som.dto.hospitalAnalysis.HospitalAnalysisQueryParam;
import com.my.som.vo.common.CommonObject;
import com.my.som.vo.hospitalAnalysis.CostAnalysisInfo;
import com.my.som.vo.hospitalAnalysis.DrgsAnalysisCountInfo;
import com.my.som.vo.hospitalAnalysis.DrgsAnalysisInfo;

import java.util.List;
import java.util.Map;

/**
 * Service
 * Created by sky on 2020/3/17.
 */
public interface CostAnalysisService {
    /**
     * 查询医院费用分析主要信息
     * @param queryParam
     * @return
     */
    List<Map<String,Object>> list(HospitalAnalysisQueryParam queryParam, Integer pageSize, Integer pageNum);

    /**
     * 查询医院费用分析统计信息
     * @param queryParam
     * @return
     */
    List<CommonObject> getCountInfo(HospitalAnalysisQueryParam queryParam);

}
