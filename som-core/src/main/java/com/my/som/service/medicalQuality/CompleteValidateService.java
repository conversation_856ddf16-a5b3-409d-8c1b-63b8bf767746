package com.my.som.service.medicalQuality;

import com.my.som.dto.medicalQuality.*;

import java.util.List;

/**
 * 病例完整性校验Service
 * Created by sky on 2020/3/1.
 */
public interface CompleteValidateService {

    /**
     * 分页查询病例完整性结果
     * @param queryParam
     * @param pageSize
     * @param pageNum
     * @return
     */
    List<ValidateMainInfo> list(SettleListMainInfoQueryParam queryParam, Integer pageSize, Integer pageNum);


    /**
     * 查询病例完整性校对统计信息
     * @param queryParam
     * @return
     */
    CompleteValidateCountInfo getCountInfo(SettleListMainInfoQueryParam queryParam);
}
