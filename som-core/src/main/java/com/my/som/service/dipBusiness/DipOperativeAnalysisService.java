package com.my.som.service.dipBusiness;


import com.my.som.dto.hospitalAnalysis.HospitalAnalysisQueryParam;
import com.my.som.vo.dipBusiness.DipOperativeAnalysisCountInfo;
import com.my.som.vo.dipBusiness.DipOperativeAnalysisVo;



import java.util.List;

public interface DipOperativeAnalysisService {
    List<DipOperativeAnalysisVo> list(HospitalAnalysisQueryParam queryParam, Integer pageSize, Integer pageNum);

    List<DipOperativeAnalysisCountInfo> getCountInfo(HospitalAnalysisQueryParam queryParam);
}
