package com.my.som.service.intoGroupCalculate.impl;

import com.alibaba.druid.util.StringUtils;
import com.github.pagehelper.PageHelper;
import com.my.som.common.util.ListUtil;
import com.my.som.common.util.ValidateUtil;
import com.my.som.common.vo.LabelValueVo;
import com.my.som.dto.intoGroupCalculate.IntoGroupCalculateDto;
import com.my.som.mapper.intoGroupCalculate.IntoGroupCalculateMapper;
import com.my.som.paths.entity.PathsEntity;
import com.my.som.paths.entity.PathsItems;
import com.my.som.service.intoGroupCalculate.IntoGroupCalculateService;
import com.my.som.thread.intoGroupCalculate.Mythread;
import com.my.som.util.MapBeanUtil;
import com.my.som.vo.intoGroupCalculate.IntoGroupCalculateVo;
import com.my.som.vo.intoGroupCalculate.ResultVo;
import com.my.som.vo.intoGroupCalculate.SelectVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.DecimalFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: zyd
 * @date: 2023-07-31
 **/
@Service
@Slf4j
public class IntoGroupCalculateServiceImpl implements IntoGroupCalculateService {

    private final String GROUP_TYPE_DIP = "1";
    private final String GROUP_TYPE_DRG = "3";

    @Autowired
    private IntoGroupCalculateMapper intoGroupCalculateMapper;

    @Override
    public List<IntoGroupCalculateVo> queryGroupCalculate(IntoGroupCalculateDto dto) {
//        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        return intoGroupCalculateMapper.queryLocal(dto);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void calCulate(IntoGroupCalculateDto dto) {
        //删除表数据
        intoGroupCalculateMapper.deleteCalculate(dto);
        intoGroupCalculateMapper.deleteRecord(dto);
//        intoGroupCalculateMapper.deleteCount(dto);
        //1.查出所有记录，bus_settle_list
        List<IntoGroupCalculateVo> list = new ArrayList<>();
        List<Integer> ids = new ArrayList<>();
        int limit = 1000;
        int start = 0;
        do {
            dto.setStart(start);
            dto.setLimit(limit);
            //settleListIds
            ids = intoGroupCalculateMapper.queryPage(dto);
            if (ValidateUtil.isNotEmpty(ids)) {
                dto.setIds(ids);
            } else {
                break;
            }
            List<IntoGroupCalculateVo> vos = intoGroupCalculateMapper.queryAllRecord(dto);
              if (ValidateUtil.isNotEmpty(vos)) {
                list.addAll(vos);
                start += limit;
            }
        }while (ids.size() == limit);
        //分组
        Map<String, List<IntoGroupCalculateVo>> listMap = getListMap(list,dto);
        //生成t1，t2, t3表
        insertToTable(listMap,dto);

    }

    private void insertToTable(Map<String, List<IntoGroupCalculateVo>> listMap, IntoGroupCalculateDto dto) {
        IntoGroupCalculateDto calculateDto = new IntoGroupCalculateDto();
        calculateDto.setType(dto.getType());
        calculateDto.setTabName(dto.getTabName());
        calculateDto.setTableName2(dto.getTableName2());
        calculateDto.setTableName3(dto.getTableName3());
        calculateDto.setBenchmarkTable(dto.getBenchmarkTable());
        calculateDto.setGroupRecordTable(dto.getGroupRecordTable());
        //t1
        ArrayList<IntoGroupCalculateVo> t1 = new ArrayList<>();
        ArrayList<IntoGroupCalculateVo> t = new ArrayList<>();
        //t2
        ArrayList<IntoGroupCalculateVo> t2 = new ArrayList<>();
        ArrayList<IntoGroupCalculateVo> t22 = new ArrayList<>();
        //t3
        ArrayList<IntoGroupCalculateVo> t3 = new ArrayList<>();
        ArrayList<IntoGroupCalculateVo> t33 = new ArrayList<>();
        listMap.forEach((key,value) -> {
            t1.add(value.get(0));
            t2.addAll(value);
        });
        //添加t1表，设置t2表的父id
        addCalculateAndSetParentId(dto, calculateDto, t1, t, t2, t22);
        //设置t3表数据
//        Map<String, List<IntoGroupCalculateVo>> collect = t22.stream().collect(Collectors.groupingBy(IntoGroupCalculateVo::getId));
//        collect.forEach((key, value) ->{
//            calculateDto.setList(value);
//            IntoGroupCalculateVo vo = intoGroupCalculateMapper.selectCountInfo(calculateDto);
//            if (vo != null) {
//                vo.setId(value.get(0).getId());
//                t3.add(vo);
//            }
//        });
        //批量新增t2表
        addCalculateRecord(calculateDto, t22);
        //批量新增 t3表
//        List<List<IntoGroupCalculateVo>> partition4 = ListUtil.partition(t3, 1000);
//        partition4.forEach(item -> {
//            calculateDto.setList(item);
//            intoGroupCalculateMapper.batchAddDetails(calculateDto);
//        });

//        listMap.forEach((key, value) -> {
//            //生成t1表，记录对应诊断和手术下的分组信息
//            calculateDto.setCalculateVo(value.get(0));
//            calculateDto.setType(dto.getType());
//            calculateDto.setTabName(dto.getTabName());
//            calculateDto.setTableName2(dto.getTableName2());
//            intoGroupCalculateMapper.addCalculate(calculateDto);
//            //生成t2表，中间表，保存k00，parentId，settleListId
//            calculateDto.setList(value);
//            intoGroupCalculateMapper.batchAddRecord(calculateDto);
//            //生成t3表，计算相关统计信息
//            // 查询出统计信息，平均住院费，平均住院天数
//            calculateDto.setType(dto.getType());
//            calculateDto.setTableName3(dto.getTableName3());
//            calculateDto.setBenchmarkTable(dto.getBenchmarkTable());
//            calculateDto.setGroupRecordTable(dto.getGroupRecordTable());
//            IntoGroupCalculateVo vo = intoGroupCalculateMapper.selectCountInfo(calculateDto);
//            if (vo == null) {
//                StringBuilder str = new StringBuilder();
//                value.forEach(a -> {
//                    str.append(a.getSettleListId());
//                });
//                log.warn("结算清单为：" + str + " 未能统计");
//            } else {
//                vo.setId(calculateDto.getId());
//                vo.setTableName3(dto.getTableName3());
//                intoGroupCalculateMapper.addDetails(vo);
//            }
//
//        });
    }

    private void addCalculateRecord(IntoGroupCalculateDto calculateDto, ArrayList<IntoGroupCalculateVo> t22) {
        List<List<IntoGroupCalculateVo>> partition3 = ListUtil.partition(t22, 1000);
        partition3.forEach(item -> {
            calculateDto.setList(item);
            intoGroupCalculateMapper.batchAddRecord(calculateDto);
        });
    }

    private void addCalculateAndSetParentId(IntoGroupCalculateDto dto, IntoGroupCalculateDto calculateDto, ArrayList<IntoGroupCalculateVo> t1, ArrayList<IntoGroupCalculateVo> t, ArrayList<IntoGroupCalculateVo> t2, ArrayList<IntoGroupCalculateVo> t22) {
        List<List<IntoGroupCalculateVo>> partition = ListUtil.partition(t1, 1000);
        partition.forEach(item -> {
            intoGroupCalculateMapper.addCalculate(calculateDto, item);
            t.addAll(item);
        });
        Map<String, List<IntoGroupCalculateVo>> tMap = t.stream().collect(Collectors.groupingBy(item -> getString(dto, item)));
        List<List<IntoGroupCalculateVo>> partition2 = ListUtil.partition(t2, 2000);
        partition2.forEach(list -> {
            list.forEach(o ->{
                String str = getString(dto, o);
                //设置对应的父 id
                o.setId(tMap.get(str).get(0).getId());
            });
            t22.addAll(list);
        });
    }

    @Override
    public List<IntoGroupCalculateVo> addNewRecord(IntoGroupCalculateDto dto) {
        //诊断表查找
        //手术表查找
        //比较 settleListId
        List<IntoGroupCalculateVo> list = intoGroupCalculateMapper.querySettleId(dto);
        if (ValidateUtil.isEmpty(list)) {
            return null;
        }
        //分组
        Map<String, List<IntoGroupCalculateVo>> listMap = getListMap(list,dto);
        //根据 settleListId 去分组记录表获取分组信息
        //生成t1，t2表
        insertToTable(listMap,dto);
        return queryGroupCalculate(dto);
    }

    @Override
    public List<List<IntoGroupCalculateVo>> intoGroup(List<IntoGroupCalculateVo> list, IntoGroupCalculateDto dto) {
        if (ValidateUtil.isEmpty(list)) {
            return new ArrayList<>();
        }
        List<List<IntoGroupCalculateVo>> resList = new ArrayList<>();
        Map<String, List<IntoGroupCalculateVo>> listMap = new HashMap<>();
        if (GROUP_TYPE_DIP.equals(dto.getType())) {
            listMap = list.stream().collect(Collectors.groupingBy(IntoGroupCalculateVo::getDipCodg));
        } else if (GROUP_TYPE_DRG.equals(dto.getType())) {
            listMap = list.stream().collect(Collectors.groupingBy(IntoGroupCalculateVo::getDrgCodg));
        }
        if (listMap.size() > 0) {
            resList.addAll(listMap.values());
        }
        resList.forEach(item ->{
            //平均费用
//            double avgCost = item.stream().mapToDouble(x -> new Double(x.getInHosCostAvg())).average().orElse(0d);
            DecimalFormat df=new DecimalFormat("0.0000");//设置保留位数
            DecimalFormat df2=new DecimalFormat("0.0");//设置保留位数
            double avgCost = 0;
            //平均天数
            double avgDay = 0;
            for (IntoGroupCalculateVo vo : item) {
                if (ValidateUtil.isNotEmpty(vo.getInHosCostAvg())) {
                    avgCost += new Double(vo.getInHosCostAvg());
                }
                if (ValidateUtil.isNotEmpty(vo.getInHosDayAvg())) {
                    avgDay += new Double(vo.getInHosDayAvg());
                }
            }
            item.get(0).setInHosCostAvg(String.valueOf(df.format(avgCost / item.size())));
//            double avgDay = item.stream().mapToDouble(x -> new Double(x.getInHosDayAvg())).average().orElse(0d);
            item.get(0).setInHosDayAvg(String.valueOf(df2.format(avgDay / item.size())));
        });
        return resList;
    }

    @Override
    public List<List<IntoGroupCalculateVo>> queryDiagnoseAndOperation(List<String> ids, IntoGroupCalculateDto dto) {
        List<IntoGroupCalculateVo> vos = intoGroupCalculateMapper.queryDiagnoseAndOperation(ids, dto);
        Map<String, List<IntoGroupCalculateVo>> listMap = vos.stream().collect(Collectors.groupingBy(item -> getString(dto, item)));
        ArrayList<List<IntoGroupCalculateVo>> reslist = new ArrayList<>(listMap.values());
        for (List<IntoGroupCalculateVo> list : reslist) {
            Integer peopleNum = 0;
            for (IntoGroupCalculateVo vo : list) {
                peopleNum += Integer.parseInt(vo.getPeopleNum());
            }
            list.get(0).setPeopleNum(String.valueOf(peopleNum));
        }
        return reslist;
    }


    @Override
    public PathsEntity queryCostDetails(IntoGroupCalculateDto dto) {
        PathsEntity pathsEntity = new PathsEntity();
        pathsEntity.setItemsData(intoGroupCalculateMapper.queryCostDetails(dto));
        return pathsEntity;
    }

    @Override
    public ResultVo setSelectValue(List<List<IntoGroupCalculateVo>> list) throws Exception {
        ResultVo result = new ResultVo();
        String dianoseStr = "c06c_";
        String dianoseStrName = "c07n_";
        String operationStr = "c35c_";
        String operationStrName = "c36n_";
        result.setList(list);
        Map<String, String> dianoseMap = new HashMap<>();
        Map<String, String> operationMap = new HashMap<>();
        ArrayList<SelectVo> dianoseList = new ArrayList<>();
        ArrayList<SelectVo> operationList = new ArrayList<>();

        for (List<IntoGroupCalculateVo> vos : list) {
            for (IntoGroupCalculateVo vo : vos) {
                Map<String, Object> map = MapBeanUtil.bean2map(vo);
                //诊断
                for (int i = 0; i < 16; i++) {
                    String str1 = dianoseStr + i;
                    String str2 = dianoseStrName + i;
                    if (map.get(str1) != null) {
                        String o = (String) map.get(str1);
                        if (dianoseMap.get(o) == null) {
                            SelectVo selectVo = new SelectVo();
                            String o2 = (String) map.get(str2);
                            selectVo.setLabel(o2);
                            selectVo.setValue(o);
                            dianoseMap.put(o,"1");
                            dianoseList.add(selectVo);
                        }
                    }
                }
                //手术
                for (int i = 0; i < 7; i++) {
                    String str1 = operationStr + i;
                    String str2 = operationStrName + i;
                    if (map.get(str1) != null) {
                        String o = (String) map.get(str1);
                        if (operationMap.get(o) == null) {
                            SelectVo selectVo = new SelectVo();
                            String o2 = (String) map.get(str2);
                            selectVo.setLabel(o2);
                            selectVo.setValue(o);
                            operationMap.put(o,"1");
                            operationList.add(selectVo);
                        }
                    }
                }
            }
        }
        result.setDiagnoseOptions(dianoseList);
        result.setOperationOptions(operationList);
        return result;
    }

    /**
     *根据诊断，手术等条件分组
     * @param list
     * @return
     */
    private Map<String, List<IntoGroupCalculateVo>> getListMap(List<IntoGroupCalculateVo> list, IntoGroupCalculateDto dto) {
        return list.stream().collect(Collectors.groupingBy(item -> getString(dto, item)));
    }

    /**
     * 拼接的分组字段
     * @param dto
     * @param item
     * @return
     */
    private String getString(IntoGroupCalculateDto dto, IntoGroupCalculateVo item) {
        StringBuilder str;
        str = new StringBuilder();
        //诊断
        str.append(item.getC06c_0()).append(item.getC06c_1()).append(item.getC06c_2()).append(item.getC06c_3()).append(item.getC06c_4()).append(item.getC06c_5()).append(item.getC06c_6()).append(item.getC06c_7()).append(item.getC06c_8()).append(item.getC06c_9()).append(item.getC06c_10()).append(item.getC06c_11()).append(item.getC06c_12()).append(item.getC06c_13()).append(item.getC06c_14()).append(item.getC06c_15());
        //手术
        str.append(item.getC35c_0()).append(item.getC35c_1()).append(item.getC35c_2()).append(item.getC35c_3()).append(item.getC35c_4()).append(item.getC35c_5()).append(item.getC35c_6());
//        str.append(item.getGend()).append(item.getAge()).append(item.getUsedAsstList()).append(item.getAsstListAgeGrp()).append(item.getAsstListDise()).append(item.getAsstListTmorSevDeg());
        //组
        if (GROUP_TYPE_DIP.equals(dto.getType())){
            str.append(item.getDipCodg());
            //辅助目录
            str.append(item.getUsedAsstList()).append(item.getAsstListAgeGrp()).append(item.getAsstListDise()).append(item.getAsstListTmorSevDeg());
        } else if(GROUP_TYPE_DRG.equals(dto.getType())) {
            str.append(item.getDrgCodg());
        }
        //1性别.2年龄,
        if (dto.getXianshi() != null) {
            if (dto.getXianshi().contains("1")) {
                str.append(item.getGend());
            }
            if (dto.getXianshi().contains("2")) {
                str.append(item.getAge());
            }
        }

        return String.valueOf(str);
    }

    private void createAddRecordThread(IntoGroupCalculateDto dto, List<IntoGroupCalculateVo> partList) {
        Mythread mythread = new Mythread();
        dto.setList(partList);
        mythread.setIntoGroupCalculateMapper(intoGroupCalculateMapper);
        mythread.setDto(dto);
        Thread thread = new Thread(mythread);
        thread.start();
    }

    private void joinEmpty(IntoGroupCalculateDto dto) {
        List<LabelValueVo> disList = dto.getDis();
        LabelValueVo vo = new LabelValueVo();
        if (disList.size() < 15) {
            for (int i = disList.size(); i < 15; i++) {
                disList.add(vo);
            }
            dto.setDis(disList);
        }
        if (ValidateUtil.isNotEmpty(dto.getOpe())) {
            List<LabelValueVo> opeList = dto.getOpe();
            if (opeList.size() < 7) {
                for (int i = opeList.size(); i < 7; i++) {
                    opeList.add(vo);
                }
                dto.setOpe(opeList);
            }
        } else {
            ArrayList<LabelValueVo> emptyList = new ArrayList<>();
            for (int i = 0; i < 7; i++) {
                emptyList.add(vo);
            }
            dto.setOpe(emptyList);
        }
    }

    private void joinOperation(IntoGroupCalculateDto dto) {
        List<LabelValueVo> opeList = dto.getOpe();
        StringBuilder str = new StringBuilder();
        StringBuilder str2 = new StringBuilder();
        if (opeList.size() > 1) {
            joinStr(opeList, str, str2);
            dto.setOprnOprtCode(String.valueOf(str));
            dto.setOperationCode2(String.valueOf(str2));
        }else if(opeList.size() == 1) {
            dto.setOprnOprtCode(dto.getOpe().get(0).getValue());
        }
    }

    private void joinDiagnose(IntoGroupCalculateDto dto) {
        List<LabelValueVo> disList = dto.getDis();
        if(disList.size() > 1) {
            StringBuilder str = new StringBuilder();
            StringBuilder str2 = new StringBuilder();
            joinStr(disList, str, str2);
            dto.setMainDiagDiseCodg(String.valueOf(str));
            dto.setMainDiagDiseCodg(String.valueOf(str2));
        } else if (disList.size() == 1){
            dto.setMainDiagDiseCodg(disList.get(0).getValue());
        }
    }

    private void joinStr(List<LabelValueVo> disList, StringBuilder str, StringBuilder str2) {
        for (int i = 0; i < disList.size(); i++) {
            if (i == 0) {
                str.append(disList.get(0).getValue());
                str2.append(disList.get(0).getValue());
            } else {
                str.append("+").append(disList.get(i).getValue());
                str2.append("/").append(disList.get(i).getValue());
            }
        }
    }
}
