package com.my.som.service.hospitalAnalysis;

import com.my.som.dto.hospitalAnalysis.HospitalAnalysisQueryParam;
import com.my.som.vo.common.CommonObject;
import com.my.som.vo.hospitalAnalysis.CostAnalysisInfo;
import com.my.som.vo.hospitalAnalysis.DoctorAnalysisCountInfo;
import com.my.som.vo.hospitalAnalysis.DoctorAnalysisInfo;

import java.util.List;

/**
 * Service
 * Created by sky on 2020/3/20.
 */
public interface DoctorAnalysisService {
    /**
     * 查询医院医生分析主要信息
     * @param queryParam
     * @return
     */
    List<DoctorAnalysisInfo> list(HospitalAnalysisQueryParam queryParam, Integer pageSize, Integer pageNum);

    /**
     * 查询医院医生分析统计信息
     * @param queryParam
     * @return
     */
    List<DoctorAnalysisCountInfo> getCountInfo(HospitalAnalysisQueryParam queryParam);

}
