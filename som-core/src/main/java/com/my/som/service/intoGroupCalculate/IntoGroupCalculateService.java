package com.my.som.service.intoGroupCalculate;

import com.my.som.dto.intoGroupCalculate.IntoGroupCalculateDto;
import com.my.som.paths.entity.PathsEntity;
import com.my.som.vo.intoGroupCalculate.IntoGroupCalculateVo;
import com.my.som.vo.intoGroupCalculate.ResultVo;

import java.util.List;

/**
 * @author: zyd
 * @date: 2023-07-31
 **/
public interface IntoGroupCalculateService {

    /**
     * 查询本地入组测算记录
     * @param dto
     * @return
     */
    List<IntoGroupCalculateVo> queryGroupCalculate(IntoGroupCalculateDto dto);

    /**
     * 测算
     * @param dto
     */
    void calCulate(IntoGroupCalculateDto dto);

    /**
     * 本地没有，添加新的记录
     * @param dto
     * @return
     */
    List<IntoGroupCalculateVo> addNewRecord(IntoGroupCalculateDto dto);

    /**
     * 将list 根据 dipName 分组
     * @param list
     * @return
     */
    List<List<IntoGroupCalculateVo>> intoGroup(List<IntoGroupCalculateVo> list, IntoGroupCalculateDto dto);

    /**
     * 查询 诊断和手术
     * @param ids
     * @return
     */
    List<List<IntoGroupCalculateVo>>  queryDiagnoseAndOperation(List<String> ids, IntoGroupCalculateDto dto);

    /**
     * 查询项目使用
     * @param dto
     * @return
     */
    PathsEntity queryCostDetails(IntoGroupCalculateDto dto);

    /**
     * 诊断和手术 select 值
     * @param list
     * @return
     */
    ResultVo setSelectValue(List<List<IntoGroupCalculateVo>> list) throws Exception;
}
