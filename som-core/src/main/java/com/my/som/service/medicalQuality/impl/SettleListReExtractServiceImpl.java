package com.my.som.service.medicalQuality.impl;

import com.my.som.dao.medicalQuality.SettleListReExtractMapper;
import com.my.som.dto.medicalQuality.SettleListReExtractDto;
import com.my.som.service.medicalQuality.SettleListReExtractService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 数据处理日志Service
 * Created by sky on 2020/1/5.
 */
@Service
public class SettleListReExtractServiceImpl implements SettleListReExtractService {

    @Autowired
    private SettleListReExtractMapper settleListReExtractMapper;


    @Override
    public void reExtract(SettleListReExtractDto dto) {
        List<String> strings = settleListReExtractMapper.queryReExtractK00(dto);
    }
}
