package com.my.som.service.medicalQuality;

import com.my.som.dto.dipBusiness.DipBusinessQueryDto;
import com.my.som.vo.dataHandle.dataGroup.DipGroupDataInfoVo;

import java.util.List;
import java.util.Map;

/**
 * @author: zyd
 * @version 1.0
 * @date: 2023-07-31
 * @description:
 */
public interface GroupCompQueryService {

    /**
     * 获取分组数据
     * @param dto
     * @return
     */
    Map<String, Object> getGroupData(DipGroupDataInfoVo dto);

    /**
     * 查询入组病例
     * @param dto
     * @return
     */
    List<Map<String, Object>> queryRecords(DipBusinessQueryDto dto);
}
