package com.my.som.service.hosPerfAppraisal;

import com.my.som.dto.hosPerfAppraisal.HosPerfAppraisalKpiItemDto;
import com.my.som.vo.hosPerfAppraisal.HosPerfAppraisalKpiItemVo;

import java.util.List;

/**
 * @author: zyd
 * @version 1.0
 * @date: 2023-07-31
 * @description:
 */
public interface HosPerfAppraisalKpiItemService {

    /**
     * 查询数据
     * @param dto
     * @return
     */
    List<HosPerfAppraisalKpiItemVo> queryList(HosPerfAppraisalKpiItemDto dto);

    /**
     * 新增
     * @param dto
     */
    void add(HosPerfAppraisalKpiItemDto dto);

    /**
     * 修改
     * @param dto
     */
    void update(HosPerfAppraisalKpiItemDto dto);

    /**
     * 删除
     * @param dto
     */
    void remove(HosPerfAppraisalKpiItemDto dto);
}
