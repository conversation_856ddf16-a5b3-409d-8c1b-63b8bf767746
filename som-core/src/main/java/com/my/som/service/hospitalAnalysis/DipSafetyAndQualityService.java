package com.my.som.service.hospitalAnalysis;

import com.my.som.dto.hospitalAnalysis.HospitalAnalysisQueryParam;
import com.my.som.vo.hospitalAnalysis.DrgsSafetyAndQualityCountVo;
import com.my.som.vo.hospitalAnalysis.RiskLevelMedicalDetailVo;

import java.util.List;

/**
 * Service
 * Created by sky on 2020/3/17.
 */
public interface DipSafetyAndQualityService {
    /**
     * drgs安全质量病案详情
     * @param queryParam
     * @return
     */
    List<RiskLevelMedicalDetailVo> list(HospitalAnalysisQueryParam queryParam,Integer pageSize, Integer pageNum);

    /**
     * drgs安全质量分析指标信息
     * @param queryParam
     * @return
     */
    DrgsSafetyAndQualityCountVo getCountInfo(HospitalAnalysisQueryParam queryParam);

}
