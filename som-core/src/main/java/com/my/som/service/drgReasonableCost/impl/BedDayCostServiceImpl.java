package com.my.som.service.drgReasonableCost.impl;


import com.github.pagehelper.PageHelper;
import com.my.som.common.constant.DrgConst;
import com.my.som.dao.drgReasonableCost.BedDayCostDao;
import com.my.som.dto.drgReasonableCost.DrgReasonableCostQueryParam;
import com.my.som.service.drgReasonableCost.BedDayCostService;
import com.my.som.vo.drgReasonableCost.BedCostDayVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Service实现类
 * Created by sky on 2020/1/5.
 */
@Service
public class BedDayCostServiceImpl implements BedDayCostService {
    @Autowired
    private BedDayCostDao bedDayCostDao;

    @Override
    public List<BedCostDayVo> list(DrgReasonableCostQueryParam queryParam, Integer pageSize, Integer pageNum) {
        PageHelper.startPage(pageNum, pageSize);
        queryParam.setActiveFlag(DrgConst.ACTIVE_FLAG_1);
        return bedDayCostDao.list(queryParam);
    }

}
