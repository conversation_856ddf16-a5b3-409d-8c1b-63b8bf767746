package com.my.som.service.listManagement;

import com.my.som.dto.listManagement.CheckAnalysisDto;
import com.my.som.vo.listManagement.CheckAnalysisVo;

import java.util.List;

public interface CheckAnalysisService {

    /**
     * 查询清单校验错误数量
     * @param dto
     * @return
     */
    List<CheckAnalysisVo> queryErrorNum(CheckAnalysisDto dto);

    /**
     * 查询清单校验错误数据
     * @param dto
     * @return
     */
    List<CheckAnalysisVo> queryErrorData(CheckAnalysisDto dto);

    /**
     * 查询科室错误数量
     * @param dto
     * @return
     */
    List<CheckAnalysisVo> queryDeptErrorNum(CheckAnalysisDto dto);

    List<String> queryErrorType(CheckAnalysisDto dto);
}
