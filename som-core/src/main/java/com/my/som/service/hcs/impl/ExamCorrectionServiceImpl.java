package com.my.som.service.hcs.impl;

import com.github.pagehelper.PageHelper;
import com.my.som.common.exception.AppException;
import com.my.som.common.util.RedisUtils;
import com.my.som.common.util.ValidateUtil;
import com.my.som.common.vo.DictRedisVo;
import com.my.som.common.vo.DictVo;
import com.my.som.dto.hcs.ExamCorrectionRuleParam;
import com.my.som.dto.hcs.ExamCorrectionTupleParam;
import com.my.som.mapper.hcs.ExamCorrectionMapper;
import com.my.som.service.hcs.ExamCorrectionService;
import com.my.som.vo.hcs.ExamCorrectionRuleVo;
import com.my.som.vo.hcs.ExamCorrectionTupleVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

@Service
public class ExamCorrectionServiceImpl implements ExamCorrectionService {

    @Value("${redis.key.prefix.authCode}")
    private String REDIS_KEY_PREFIX_AUTH_CODE;
    @Autowired
    private ExamCorrectionMapper examCorrectionMapper;

    @Override
    public List<ExamCorrectionRuleVo> getRuleList(ExamCorrectionRuleParam queryParam, Integer pageSize, Integer pageNum) {
        PageHelper.startPage(pageNum, pageSize);
        return examCorrectionMapper.getRuleList(queryParam);
    }

    @Override
    public List<ExamCorrectionTupleVo> queryTupleList(ExamCorrectionTupleParam queryParam, Integer pageSize, Integer pageNum) {
        PageHelper.startPage(pageNum, pageSize);
        return examCorrectionMapper.queryTupleList(queryParam);
    }

    @Override
    public List<ExamCorrectionTupleVo> getTupleTypeList() {
        return examCorrectionMapper.getTupleTypeList();
    }

    @Override
    public List<ExamCorrectionTupleVo> handleTuple (ExamCorrectionRuleVo examCorrectionRuleVo) {
        List<ExamCorrectionTupleVo> tupleVoList = new ArrayList<>();
        List<String> listA = Arrays.asList(examCorrectionRuleVo.getTupleDataA().split(","));
        if (!ValidateUtil.isEmpty(listA)) {
            for(String str : listA){
                // 正则表达式：匹配 code 和 名称
                String code = str.split("【")[0];  // 提取 code 部分
                String name = str.split("【")[1].replace("】", "");  // 提取名称并去除【】
                ExamCorrectionTupleVo  tupleVo = new ExamCorrectionTupleVo();
                tupleVo.setDataGrpCode(examCorrectionRuleVo.getRuleDataMeta());
                tupleVo.setDataDetailCode("G1");
                tupleVo.setDataCode(code);
                tupleVo.setDataName(name);
                tupleVo.setAddTime(examCorrectionRuleVo.getAddTime());
                tupleVo.setValiFlag("1");
                tupleVo.setRuleYear(examCorrectionRuleVo.getRuleYear());
                tupleVoList.add(tupleVo);
            }
        }
        if (!ValidateUtil.isEmpty(examCorrectionRuleVo.getTupleDataB())
                && !"ExistOperator".equals(examCorrectionRuleVo.getOpraType()) ) {
            List<String> listB = Arrays.asList(examCorrectionRuleVo.getTupleDataB().split(","));
            for(String str : listB){
                // 正则表达式：匹配 code 和 名称
                String code = str.split("【")[0];  // 提取 code 部分
                String name = str.split("【")[1].replace("】", "");  // 提取名称并去除【】
                ExamCorrectionTupleVo  tupleVo = new ExamCorrectionTupleVo();
                tupleVo.setDataGrpCode(examCorrectionRuleVo.getRuleDataMeta());
                tupleVo.setDataDetailCode("G2");
                tupleVo.setDataName(name);
                tupleVo.setDataCode(code);
                tupleVo.setAddTime(examCorrectionRuleVo.getAddTime());
                tupleVo.setValiFlag("1");
                tupleVo.setRuleYear(examCorrectionRuleVo.getRuleYear());

                tupleVoList.add(tupleVo);
            }
        }
        return  tupleVoList;
    }

    @Override
    @Transactional
    public void saveNewRule(ExamCorrectionRuleVo newRule, List<ExamCorrectionTupleVo> tuples) {

        examCorrectionMapper.insertRule(newRule);
        examCorrectionMapper.insertTuples(tuples);
    }

    @Override
    public ExamCorrectionRuleVo handleRule(ExamCorrectionRuleVo examCorrectionRuleVo) {
        if(ValidateUtil.isEmpty(examCorrectionRuleVo.getTupleDataA())){
            throw new AppException("规则元组A不能为空，请添加规则元组A");
        }
        if(ValidateUtil.isEmpty(examCorrectionRuleVo.getTupleDataB()) && "DoubleExistOperator".equals(examCorrectionRuleVo.getOpraType())) {
            throw new AppException("算子规则类型为不能同时存在，请添加规则元组B");
        }

        //规则类型
        String ruleTypeCodg = examCorrectionRuleVo.getRuleTypeCodg();
        String ruleTypeName = getLabelOnRedisByValue(ruleTypeCodg,"RULE_TYPE_CODE");
        examCorrectionRuleVo.setRuleTypeName(ruleTypeName);
        examCorrectionRuleVo.setValiFlag("1");

        String ruleGrpCodgOld =  examCorrectionMapper.selectMaxRuleGrpCodgOld(ruleTypeCodg);
        String newRuleGrpCodg = getString(ruleGrpCodgOld,3);
        examCorrectionRuleVo.setRuleGrpCodg(newRuleGrpCodg);
        String ruleDataMetaOld =  examCorrectionMapper.selectMaxRuleDataMetaOld(ruleTypeCodg);
        String newRuleDataMeta = getString(ruleDataMetaOld,4);
        examCorrectionRuleVo.setRuleDataMeta(newRuleDataMeta);
        String newRuleDetlCodg = newRuleGrpCodg + "01";
        examCorrectionRuleVo.setRuleDetlCodg(newRuleDetlCodg);
        // 获取当前时间
        LocalDateTime now = LocalDateTime.now();
        // 定义时间格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        // 格式化当前时间
        String formattedDate = now.format(formatter);
        examCorrectionRuleVo.setAddTime(formattedDate);
        return examCorrectionRuleVo;
    }

    private String getLabelOnRedisByValue(String ruleTypeCodg ,String label) {
        DictRedisVo ruleTypeCodeMap = (DictRedisVo) RedisUtils.get(REDIS_KEY_PREFIX_AUTH_CODE + label);
        if(!ValidateUtil.isEmpty(ruleTypeCodeMap)) {
            for (DictVo dictVo : ruleTypeCodeMap.getDictVoList()) {
                if(ruleTypeCodg.equals(dictVo.getValue())) {
                    return dictVo.getLabel();
                }
            }
        }
        return null;
    }

    private  String getString(String ruleGrpCodgOld, int num) {
        // 提取字母部分（例如 R01）
        String prefix = ruleGrpCodgOld.substring(0, num);
        // 提取数字部分（例如 01）
        String numericPart = ruleGrpCodgOld.substring(num);
        // 将数字部分转为整数
        int number = Integer.parseInt(numericPart);
        // 增加 1
        number++;
        // 将增加后的数字部分格式化为5位数字
        String newNumericPart = String.format("%05d", number);
        // 合成新的 code
        String newRuleGrpCodg = prefix + newNumericPart;
        return newRuleGrpCodg;
    }

    @Override
    public List<ExamCorrectionTupleVo> getDataGroup() {
        return examCorrectionMapper.getDataGroup();
    }
}
