package com.my.som.service.medicalQuality;

import com.my.som.dto.medicalQuality.CompareAnalysisDto;
import com.my.som.vo.medicalQuality.DeptComparAnalysisVo;
import com.my.som.vo.medicalQuality.DrgDeptComparAnalysisVo;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

public interface DeptComparAnalysisService {
    /**
     *
     * @param dto
     * @return
     */
    DeptComparAnalysisVo getData(CompareAnalysisDto dto);

    DeptComparAnalysisVo getCost(CompareAnalysisDto dto);

    DrgDeptComparAnalysisVo getDrgData(CompareAnalysisDto dto);

    DeptComparAnalysisVo getPayCostData(CompareAnalysisDto dto);

    DrgDeptComparAnalysisVo getDrgPayCostData(CompareAnalysisDto dto);
}
