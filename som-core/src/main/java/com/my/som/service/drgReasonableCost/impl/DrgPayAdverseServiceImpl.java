package com.my.som.service.drgReasonableCost.impl;


import com.github.pagehelper.PageHelper;
import com.my.som.common.util.ValidateUtil;
import com.my.som.dao.drgReasonableCost.DrgPayAdverseDao;
import com.my.som.dto.drgReasonableCost.DrgReasonableCostQueryParam;
import com.my.som.service.drgReasonableCost.DrgPayAdverseService;
import com.my.som.vo.drgReasonableCost.DrgPayAdverseVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * Service实现类
 * Created by sky on 2020/1/5.
 */
@Service
public class DrgPayAdverseServiceImpl implements DrgPayAdverseService {
    @Autowired
    private DrgPayAdverseDao drgPayAdverseDao;

    @Override
    public List<DrgPayAdverseVo> list(DrgReasonableCostQueryParam queryParam, Integer pageSize, Integer pageNum) {
        List<DrgPayAdverseVo> result = new ArrayList<>();
        PageHelper.startPage(pageNum, pageSize);
        if(!ValidateUtil.isEmpty(queryParam.getPsnInsuType())){
            queryParam.setInsuranceTypeList(Arrays.asList(queryParam.getPsnInsuType().split(",")));
        }
        switch(queryParam.getQueryType()){
            case "1": result = drgPayAdverseDao.list1(queryParam); break;
            case "2": result = drgPayAdverseDao.list2(queryParam); break;
            case "3": result = drgPayAdverseDao.list3(queryParam); break;
            case "4": result = drgPayAdverseDao.list4(queryParam); break;
        }
        return result;
    }

}
