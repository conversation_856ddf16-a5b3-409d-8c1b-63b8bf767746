package com.my.som.service.drgReasonableCost.impl;


import com.github.pagehelper.PageHelper;
import com.my.som.common.constant.DrgConst;
import com.my.som.common.util.ValidateUtil;
import com.my.som.dao.drgReasonableCost.BedDayCostDao;
import com.my.som.dao.drgReasonableCost.DrgPayForecastDao;
import com.my.som.dto.drgReasonableCost.DrgReasonableCostQueryParam;
import com.my.som.service.drgReasonableCost.BedDayCostService;
import com.my.som.service.drgReasonableCost.DrgPayForecastService;
import com.my.som.vo.drgReasonableCost.BedCostDayVo;
import com.my.som.vo.drgReasonableCost.DrgPayForecastVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * Service实现类
 * Created by sky on 2020/1/5.
 */
@Service
public class DrgPayForecastServiceImpl implements DrgPayForecastService {
    @Autowired
    private DrgPayForecastDao drgPayForecastDao;

    @Override
    public List<DrgPayForecastVo> list(DrgReasonableCostQueryParam queryParam, Integer pageSize, Integer pageNum) {
        List<DrgPayForecastVo> result = new ArrayList<>();
        PageHelper.startPage(pageNum, pageSize);
        if(!ValidateUtil.isEmpty(queryParam.getPsnInsuType())){
            queryParam.setInsuranceTypeList(Arrays.asList(queryParam.getPsnInsuType().split(",")));
        }
        switch(queryParam.getQueryType()){
            case "1": result = drgPayForecastDao.list1(queryParam); break;
            case "2": result = drgPayForecastDao.list2(queryParam); break;
            case "3": result = drgPayForecastDao.list3(queryParam); break;
            case "4": result = drgPayForecastDao.list4(queryParam); break;
        }
        return result;
    }

}
