package com.my.som.service.medicalQuality.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.github.pagehelper.PageHelper;
import com.my.som.app.dto.AppMessageDto;
import com.my.som.app.mapper.message.AppMessageMapper;
import com.my.som.app.service.message.AppMessageService;
import com.my.som.common.api.CommonPage;
import com.my.som.common.constant.DrgConst;
import com.my.som.common.dto.CommonQueryDto;
import com.my.som.common.dto.ReportDto;
import com.my.som.common.exception.AppException;
import com.my.som.common.mapper.CommonMapper;
import com.my.som.common.util.*;
import com.my.som.common.vo.ICDCodeVo;
import com.my.som.common.vo.SysUserBase;
import com.my.som.config.DynamicScheduleTask;
import com.my.som.dao.common.CommonDao;
import com.my.som.dao.medicalQuality.SettleListManageDao;
import com.my.som.dao.medicalQuality.SettleListMarkMapper;
import com.my.som.dao.sys.SomSysCodeMapper;
import com.my.som.dao.sys.SomSysDicMapper;
import com.my.som.dto.common.HospitalDrgQueryParam;
import com.my.som.dto.dataConfig.HospitalInfoDto;
import com.my.som.dto.listManagement.ListUploadDto;
import com.my.som.dto.medicalQuality.*;
import com.my.som.dto.patienInfo.*;
import com.my.som.dto.pregroup.*;
import com.my.som.dto.somDiagInfo.SomDiagInfo;
import com.my.som.dto.somOprnInfo.SomOprnInfo;
import com.my.som.grouppay.cityarrange.PaymentForecastLiteFlowService;
import com.my.som.mapper.common.SomHospInfoMapper;
import com.my.som.mapper.common.SomInGroupAddrMapper;
import com.my.som.mapper.common.SomDipStandardMapper;
import com.my.som.mapper.common.SomCodeStandFeeMapper;
import com.my.som.mapper.dataHandle.*;
import com.my.som.mapper.engine.GroupInfoMapper;
import com.my.som.mapper.engine.SettleListValidateMapper;
import com.my.som.mapper.listManagement.ListUploadMapper;
import com.my.som.mapper.medicalQuality.SomFundPayMapper;
import com.my.som.mapper.medicalQuality.SomSetlInvyScsCutdInfoMapper;
import com.my.som.mapper.medicalQuality.SomHiSetlInvyMedFeeInfoMapper;
import com.my.som.mapper.supervision.MedicalRecordSupervisionMapper;
import com.my.som.model.common.*;
import com.my.som.model.dataHandle.*;
import com.my.som.model.medicalQuality.*;
import com.my.som.service.common.impl.CommonServiceImpl;
import com.my.som.service.dataHandle.DataHandleProcessService;
import com.my.som.service.dataHandle.DataRecordService;
import com.my.som.service.dataHandle.SettleListUploadService;
import com.my.som.service.dataHandle.impl.SettleListValidateJobServiceImpl;
import com.my.som.service.drgInterface.impl.DipDataServiceImpl;
import com.my.som.service.listManagement.impl.ListUploadServiceImpl;
import com.my.som.service.medicalQuality.SettleListManageService;
import com.my.som.service.pregroup.PreGroupServiceInterface;
import com.my.som.util.*;
import com.my.som.vo.SomSysCode;
import com.my.som.vo.dataHandle.SettleListValidateVo;
import com.my.som.vo.dataHandle.dataGroup.*;
import com.my.som.vo.dipBusiness.DipGroupResponVO;
import com.my.som.vo.dipBusiness.DipGroupResult;
import com.my.som.vo.group.DipGroupResponseVo;
import com.my.som.vo.listManagement.SettleListConfigVo;
import com.my.som.vo.listManagement.SettleListUploadLogVo;
import com.my.som.vo.medicalQuality.*;
import com.my.som.vo.pregroup.PreGroupVo;
import com.my.som.vo.settleListInfo2.SettleListLookOverVo;
import com.my.som.vo.supervision.*;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.DefaultTransactionDefinition;
import org.springframework.util.StringUtils;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.stream.Collectors;

import static com.my.som.util.DipGroupUtil.checkUsesAsstList;
import static com.my.som.util.DrgYB2020GroupUtil.*;


/**
 * 医保结算清单Service实现类
 * Created by sky on 2020/1/5.
 */
@Service
@Slf4j
public class SettleListManageServiceImpl extends Thread implements SettleListManageService {

    //redis中正在跑流程的k00的key  *代表以什么开头
    private static final String  RUNNING_K00S_REDIS_KEY_NAME = "queue*";

    private static final String SPECIAL_CHAR_REGEX = "[^0-9a-zA-Z\u4e00-\u9fa5.，,。？“”+*]+";
    @Autowired
    private ListUploadServiceImpl listUploadService;

    @Autowired
    private AppMessageService appMessageService;

    @Autowired
    private DynamicScheduleTask dynamicScheduleTask;

    @Autowired
    private CommonServiceImpl commonService;

    @Autowired
    private DataRecordService dataRecordService;
    @Autowired
    private DataHandleProcessService dataHandleProcessService;

    @Autowired
    private SettleListUploadService settleListUploadService;

    @Autowired
    private SettleListManageDao settleListManageDao;

    @Autowired
    private CommonDao commonDao;

    @Autowired
    private SomHiInvyBasInfoMapper busSettleListMapper;

    @Autowired
    private SomOtpSlowSpecialTrtInfoMapper busOutpatientClinicDiagnosisMapper;

    @Autowired
    private PaymentForecastLiteFlowService paymentForecastLiteFlowService;

    @Autowired
    private SomDiagMapper busDiseaseDiagnosisMapper;

    @Autowired
    private SomOprnOprtInfoMapper busOperateDiagnosisMapper;

    @Autowired
    private SomSetlInvyScsCutdInfoMapper busIcuMapper;

    @Autowired
    private SomHiSetlInvyMedFeeInfoMapper busMedicalCostMapper;

    @Autowired
    private SomFundPayMapper busFundPayMapper;

    @Autowired
    private SomDrgNameMapper tpdDrgsMapper;

    @Autowired
    private SomCdDiseValPayDisGpMapper tpdCdDrgppsMapper;

    @Autowired
    private SomInGroupAddrMapper cfgDrgsGroupMapper;

    @Autowired
    private SomMedcasInGroupCfgMapper cfgMedicalGroupMapper;

    @Autowired
    private SomDrgStandardMapper busBenchmarkMapper;

    @Autowired
    private SomDipStandardMapper tpdDipBenchmarkMapper;

    @Autowired
    private SomCodeStandFeeMapper tpdIcdStandardCostMapper;

    @Autowired
    private GroupInfoMapper groupInfoMapper;

    @Autowired
    private SettleListValidateMapper settleListValidateMapper;

    @Autowired
    private SettleListValidateJobServiceImpl settleListValidateJobService;

    @Autowired
    private DipDataServiceImpl dipDataService;

    @Autowired
    private CommonMapper commonMapper;

    @Autowired
    private PlatformTransactionManager transactionManager;

    @Autowired
    private AppMessageMapper appMessageMapper;

    @Autowired
    private SettleListMarkMapper settleListMarkMapper;

    @Autowired
    private SettleListManageService settleListManageService;

    @Autowired
    private MedicalRecordSupervisionMapper medicalRecordSupervisionMapper;

    @Autowired
    private PreGroupServiceInterface preGroupServiceInterface;

    @Autowired
    private SomDataprosLogMapper stsDataHandleLogMapper;
    /**
     * 空格
     */
    private final String BLACK = " ";
    /**
     * 2个空格
     */
    private final int BLACK_SUM = 1;
    /**
     * 空
     */
    private final String NULL = " ";

    /**
     * 表名
     */
    private final String SOM_DIP_SCO = "som_dip_sco";
    private final String SOM_DRG_SCO = "som_drg_sco";
    private static final BigDecimal DEFAULT_VALUE = new BigDecimal("0");


    private static final String DIAG_INFO_KEY = "diagInfo";
    private static final String OPRN_INFO_KEY = "oprnInfo";

    List<String> TABLE_NAMES = Arrays.asList("som_drg_grp_rcd",
            "som_drg_grper_intf_trns_log",
            "som_dip_grp_rcd",
            "som_dip_grper_intf_trns_log",
            "som_grp_rcd",
            "som_cd_grper_intf_trns_log",
            "som_drg_grp_info",
            "som_dip_grp_info",
            "som_cd_grp_info",
            "som_codg_resu_adjm_rcd",
            "som_setl_invy_chk_err_rcd",
            "som_setl_invy_qlt_dedu_point_detl",
            "som_dip_sco",
            "som_drg_sco",
            "som_setl_invy_chk",
            "som_invy_chk_detl",
            "som_setl_invy_scs_cutd_info",
            "som_fund_pay",
            "som_hi_setl_invy_med_fee_info",
            "som_diag",
            "som_oprn_oprt_info",
            "som_otp_slow_special_trt_info");

    @Override
    public List<BusSettleListMainInfo> list(SettleListMainInfoQueryParam queryParam, Integer pageSize, Integer pageNum) {
        updateInsuPlaceType(queryParam);
        PageHelper.startPage(pageNum, pageSize);
        return settleListManageDao.getMainInfo(queryParam);
    }
    /**
     * 修改将参保地类型转为具体的参保地数据
     * @param dto
     */
    private void updateInsuPlaceType(SettleListMainInfoQueryParam dto) {
        String insuPlaceType = (String) SysCommonConfigUtil.get(DrgConst.INSUPLC_ADMDVS_AREA);
        dto.setInsuplcAdmdvs(insuPlaceType);
        String provLevelInsuplcAdmdvs = GroupCommonUtil.getInsuplcAdmdvs();
        if(!ValidateUtil.isEmpty(provLevelInsuplcAdmdvs)){
            dto.setProvLevelInsuplcAdmdvs(provLevelInsuplcAdmdvs);
        }else {
            dto.setProvLevelInsuplcAdmdvs("0000");
        }

    }

    @Override
    public AllBusSettleListInfo getSettleListAllInfoById(String tempId, String k00) {
        Long id;
        Long newId = settleListManageDao.selectBusSettleListId(k00);
        try {
            if (!ValidateUtil.isEmpty(newId)) {
                id = newId;
            } else {
                id = Long.valueOf(tempId);
            }
        } catch (Exception e) {
            throw new AppException("唯一ID错误、请重新传输唯一ID");
        }
        SomHiInvyBasInfo somHiInvyBasInfo = new SomHiInvyBasInfo();
        //查询历史修改数据
//        Map<String, List<BusHistoryModify>> modifyMapList=new HashMap<>();
//        List<BusHistoryModify> busHistoryModifyList =settleListManageDao.selectHistoryModify(k00);
//        if(ValidateUtil.isNotEmpty(busHistoryModifyList)){
//            somHiInvyBasInfo = busSettleListMapper.selectByK00(k00);
//            modifyMapList =busHistoryModifyList.stream().filter(map -> !map.getModi_time().isEmpty()).collect(Collectors.groupingBy(BusHistoryModify::getModi_time));
//        }else {
//            somHiInvyBasInfo = getSettleListBaseInfoById(id);
//        }
        //结算清单基础数据
//        if(ValidateUtil.isEmpty(somHiInvyBasInfo)){
        somHiInvyBasInfo = busSettleListMapper.selectByK00(k00);
//            id=settleListManageDao.selectBusSettleListId(k00);
        id = somHiInvyBasInfo.getId();
//        }
        //查询上传后是否提交
        SomHiInvyBasInfo upldStas = busSettleListMapper.selectUploadState(k00);
        if (!ValidateUtil.isEmpty(upldStas) && ValidateUtil.isNotEmpty(upldStas.getUploadFlag())) {
            somHiInvyBasInfo.setUploadFlag(upldStas.getUploadFlag());
        }
//        if(ValidateUtil.isNotEmpty(busHistoryModifyList)){
//            modifyMapList =busHistoryModifyList.stream().filter(map -> !map.getModi_time().isEmpty()).collect(Collectors.groupingBy(BusHistoryModify::getModi_time));
//        }
        //结算清单门诊慢特数据
        List<SomOtpSlowSpecialTrtInfo> busOutpatientClinicDiagnosisList = getSettleListMzmtInfoById(id);
        //结算清单疾病诊断数据
        List<BusDiseaseDiagnosisTrim> busDiseaseDiagnosisTrimList = getSettleListDiseaseInfoById(newId);

        //西医诊断
        List<BusDiseaseDiagnosisTrim> westernDiseaseDiagnosisTrimList = new ArrayList<>();
        //中医诊断
        List<BusDiseaseDiagnosisTrim> chineseDiseaseDiagnosisTrimList = new ArrayList<>();
        //处理区分中西医
        if (ValidateUtil.isNotEmpty(busDiseaseDiagnosisTrimList)) {
            Map<String, List<BusDiseaseDiagnosisTrim>> busDiseaseDiagnosisTrimMap = busDiseaseDiagnosisTrimList.stream().collect(Collectors.groupingBy(BusDiseaseDiagnosisTrim::getType));
            westernDiseaseDiagnosisTrimList = busDiseaseDiagnosisTrimMap.get(DrgConst.DIAGNOSTIC_TYPE_1);
            if (!ValidateUtil.isEmpty(busDiseaseDiagnosisTrimMap.get(DrgConst.DIAGNOSTIC_TYPE_2))) {
                chineseDiseaseDiagnosisTrimList.addAll(busDiseaseDiagnosisTrimMap.get(DrgConst.DIAGNOSTIC_TYPE_2));
            }
            if (!ValidateUtil.isEmpty(busDiseaseDiagnosisTrimMap.get(DrgConst.DIAGNOSTIC_TYPE_3))) {
                chineseDiseaseDiagnosisTrimList.addAll(busDiseaseDiagnosisTrimMap.get(DrgConst.DIAGNOSTIC_TYPE_3));
            }
        }

        //结算清单手术数据
        List<SomOprnOprtInfo> busOperateDiagnosisList = getSettleListOperationInfoById(newId);
        //结算清单重症监护室数据
        List<SomSetlInvyScsCutdInfo> busIcuList = getBusIcuInfoById(id);
        // 输血数据
        List<SomSetlInvyBldInfo> busTransfusionList = settleListManageDao.queryBusTransfusionList(newId);

        //结算清单医疗费用数据
        List<SomHiSetlInvyMedFeeInfo> busMedicalCostList = getBusMedicalCostInfoById(id);
        if (!ValidateUtil.isEmpty(busMedicalCostList)) {
            SomHiSetlInvyMedFeeInfo sumbusMedicalCost = new SomHiSetlInvyMedFeeInfo();
            BigDecimal otherCost = new BigDecimal("0");
            BigDecimal otherClaa = new BigDecimal("0");
            BigDecimal otherClab = new BigDecimal("0");
            BigDecimal otherOwnpay = new BigDecimal("0");
            BigDecimal otherOth = new BigDecimal("0");
            BigDecimal sumCostItemAmount = new BigDecimal("0");
            BigDecimal sumCostItemAAmount = new BigDecimal("0");
            BigDecimal sumCostItemBAmount = new BigDecimal("0");
            BigDecimal sumCostItemSelfPayAmount = new BigDecimal("0");
            BigDecimal sumCostItemOtherAmount = new BigDecimal("0");
            for (SomHiSetlInvyMedFeeInfo bmcl : busMedicalCostList) {
                String itemName = SettleListDictUtil.getLabelByValue("portal:settleListDictCode:YLSFXMLB", bmcl.getMedChrgItemname());
                if (ValidateUtil.isEmpty(itemName)) {
                    otherCost =otherCost.add(bmcl.getAmt());
                    otherClaa =otherClaa.add(bmcl.getClaa());
                    otherClab =otherClab.add(bmcl.getClab());
                    otherOwnpay =otherOwnpay.add(bmcl.getOwnpay());
                    otherOth =otherOth.add(bmcl.getOth());
                    itemName = bmcl.getMedChrgItemname();
                }
                bmcl.setMedChrgItemname(itemName);
                sumCostItemAmount = sumCostItemAmount.add(bmcl.getAmt());
                sumCostItemAAmount = sumCostItemAAmount.add(bmcl.getClaa());
                sumCostItemBAmount = sumCostItemBAmount.add(bmcl.getClab());
                sumCostItemSelfPayAmount = sumCostItemSelfPayAmount.add(bmcl.getOwnpay());
                sumCostItemOtherAmount = sumCostItemOtherAmount.add(bmcl.getOth());
            }
            boolean flag = false;
            for (SomHiSetlInvyMedFeeInfo bmcl : busMedicalCostList) {
                if("其他费".equals(bmcl.getMedChrgItemname())){
                    flag =true;
                }
            }
            if(flag){
                for (SomHiSetlInvyMedFeeInfo bmcl : busMedicalCostList) {
                    if("其他费".equals(bmcl.getMedChrgItemname())){
                        bmcl.setAmt(bmcl.getAmt().add(otherCost));
                        bmcl.setClaa(bmcl.getClaa().add(otherClaa));
                        bmcl.setClab(bmcl.getClab().add(otherClab));
                        bmcl.setOwnpay(bmcl.getOwnpay().add(otherOwnpay));
                        bmcl.setOth(bmcl.getOth().add(otherOth));
                    }
                }
            }else {
                SomHiSetlInvyMedFeeInfo other = new SomHiSetlInvyMedFeeInfo();
                other.setMedChrgItemname("其他费");
                other.setAmt(otherCost);
                other.setClaa(otherClaa);
                other.setClab(otherClab);
                other.setOwnpay(otherOwnpay);
                other.setOth(otherOth);
                busMedicalCostList.add(other);
            }
            sumbusMedicalCost.setMedChrgItemname("金额合计");
            sumbusMedicalCost.setAmt(sumCostItemAmount);
            sumbusMedicalCost.setClaa(sumCostItemAAmount);
            sumbusMedicalCost.setClab(sumCostItemBAmount);
            sumbusMedicalCost.setOwnpay(sumCostItemSelfPayAmount);
            sumbusMedicalCost.setOth(sumCostItemOtherAmount);
            busMedicalCostList.add(sumbusMedicalCost);
        }
        //结算清单基金支付数据
        List<SomFundPay> busFundPayList = getBusFundPayInfoById(id);

        AllBusSettleListInfo allBusSettleListInfo = new AllBusSettleListInfo();
        allBusSettleListInfo.setSomHiInvyBasInfo(somHiInvyBasInfo);
        allBusSettleListInfo.setBusOutpatientClinicDiagnosisList(busOutpatientClinicDiagnosisList);
        allBusSettleListInfo.setBusDiseaseDiagnosisTrimList(westernDiseaseDiagnosisTrimList);
        allBusSettleListInfo.setBusChineseDiseaseDiagnosisTrimList(chineseDiseaseDiagnosisTrimList);
        allBusSettleListInfo.setBusOperateDiagnosisList(busOperateDiagnosisList);
        allBusSettleListInfo.setBusIcuList(busIcuList);
        allBusSettleListInfo.setBusMedicalCostList(busMedicalCostList);
        allBusSettleListInfo.setBusFundPayList(busFundPayList);
        allBusSettleListInfo.setBusTransfusionList(busTransfusionList);
        allBusSettleListInfo.setShowFlag("0");
//        allBusSettleListInfo.setBusHistoryModifyMap(modifyMapList);
        return allBusSettleListInfo;
    }

    @Override
    public int updateSettleListAllInfo(AllBusSettleListInfo allBusSettleListInfo) {
        int insertbsl = 0, insertbocd = 0, deleteobocd = 0, insertbddtxy = 0, insertbddtzy = 0, deleteobddt = 0, insertbod = 0, deleteobod = 0;
        SomHiInvyBasInfo somHiInvyBasInfo = allBusSettleListInfo.getSomHiInvyBasInfo();
        Long settle_list_id = allBusSettleListInfo.getSomHiInvyBasInfo().getId();  //结算清单ID
        //更新结算清单基础数据
        SomHiInvyBasInfo oldBusSettleList = getSettleListBaseInfoById(settle_list_id);
        //如果数据库数据和页面数据不一致，需要修改，数据更新，否则不做操作
        //if(!ValidateUtil.isEmpty(somHiInvyBasInfo)&&!ValidateUtil.isEmpty(oldBusSettleList)&&!somHiInvyBasInfo.equals(oldBusSettleList)){
        SomHiInvyBasInfoExample example = new SomHiInvyBasInfoExample();
        example.createCriteria().andIdEqualTo(settle_list_id);
        insertbsl = busSettleListMapper.updateByExample(somHiInvyBasInfo, example);
        //}

        //更新门诊慢特疾病数据
        List<SomOtpSlowSpecialTrtInfo> bocdList = allBusSettleListInfo.getBusOutpatientClinicDiagnosisList();
        //首先判断是否修改过门诊慢特信息，将原有数据的页面的数据对比，如果一致，未曾被修改
        List<SomOtpSlowSpecialTrtInfo> obocdList = getSettleListMzmtInfoById(settle_list_id);
        if (!ValidateUtil.isEmpty(bocdList) && !ValidateUtil.isEmpty(obocdList) && !bocdList.equals(obocdList)) {
            //数据库没有的，页面有的，向数据库添加新有数据
            for (SomOtpSlowSpecialTrtInfo bocd : bocdList) {
                if (obocdList.contains(bocd)) {
                    continue;
                } else {
                    insertbocd = busOutpatientClinicDiagnosisMapper.insertSelective(bocd);
                }
            }
            //数据库有的，页面没有的，数据库删除数据
            for (SomOtpSlowSpecialTrtInfo obocd : obocdList) {
                if (bocdList.contains(obocd)) {
                    continue;
                } else {
                    deleteobocd = busOutpatientClinicDiagnosisMapper.deleteByPrimaryKey(obocd.getId());
                }
            }
        }

        //更新疾病数据
        List<BusDiseaseDiagnosisTrim> bddtList = allBusSettleListInfo.getBusDiseaseDiagnosisTrimList();
        //首先判断是否修改过疾病信息，将原有数据的页面的数据对比，如果一致，未曾被修改
        List<BusDiseaseDiagnosisTrim> obddtList = getSettleListDiseaseInfoById(settle_list_id);
        if (!ValidateUtil.isEmpty(bddtList) && !ValidateUtil.isEmpty(obddtList) && !bddtList.equals(obddtList)) {
            //数据库没有的，页面有的，向数据库添加新有数据
            for (BusDiseaseDiagnosisTrim bddt : bddtList) {
                if (obddtList.contains(bddt)) {
                    continue;
                } else {
                    //插入西医诊断
                    SomDiag busDiseaseDiagnosisXy = new SomDiag();
                    busDiseaseDiagnosisXy.setSettleListId(settle_list_id);
                    busDiseaseDiagnosisXy.setSeq(bddt.getSeq());
                    busDiseaseDiagnosisXy.setType(DrgConst.DIAGNOSTIC_TYPE_1);
                    busDiseaseDiagnosisXy.setDscg_diag_codg(bddt.getC06c1());
                    busDiseaseDiagnosisXy.setDscg_diag_name(bddt.getC07n1());
                    busDiseaseDiagnosisXy.setDscg_diag_adm_cond(bddt.getC08c1());
                    busDiseaseDiagnosisXy.setDscg_diag_dscg_cond(bddt.getC50c1());
                    insertbddtxy = busDiseaseDiagnosisMapper.insertSelective(busDiseaseDiagnosisXy);
                    //插入中医诊断
                    SomDiag busDiseaseDiagnosisZy = new SomDiag();
                    busDiseaseDiagnosisZy.setSettleListId(settle_list_id);
                    busDiseaseDiagnosisZy.setSeq(bddt.getSeq());
                    busDiseaseDiagnosisZy.setType(DrgConst.DIAGNOSTIC_TYPE_2);
                    busDiseaseDiagnosisZy.setDscg_diag_codg(bddt.getC06c2());
                    busDiseaseDiagnosisZy.setDscg_diag_name(bddt.getC07n2());
                    busDiseaseDiagnosisZy.setDscg_diag_adm_cond(bddt.getC08c2());
                    busDiseaseDiagnosisZy.setDscg_diag_dscg_cond(bddt.getC50c2());
                    insertbddtzy = busDiseaseDiagnosisMapper.insertSelective(busDiseaseDiagnosisZy);
                }
            }
            //数据库有的，页面没有的，数据库删除数据
            for (BusDiseaseDiagnosisTrim obddt : obddtList) {
                if (bddtList.contains(obddt)) {
                    continue;
                } else {
                    SomDiagExample busDiseaseDiagnosisExample = new SomDiagExample();
                    busDiseaseDiagnosisExample.createCriteria().andSettleListIdEqualTo(settle_list_id);
                    busDiseaseDiagnosisExample.createCriteria().andSnoNotEqualTo(obddt.getSeq());
                    deleteobddt = busDiseaseDiagnosisMapper.deleteByExample(busDiseaseDiagnosisExample);
                }
            }
        }

        //更新手术数据
        List<SomOprnOprtInfo> bodList = allBusSettleListInfo.getBusOperateDiagnosisList();
        //首先判断是否修改过疾病信息，将原有数据的页面的数据对比，如果一致，未曾被修改
        List<SomOprnOprtInfo> obodList = getSettleListOperationInfoById(settle_list_id);
        if (!ValidateUtil.isEmpty(bodList) && !ValidateUtil.isEmpty(obodList) && !bodList.equals(obodList)) {
            //数据库没有的，页面有的，向数据库添加新有数据
            for (SomOprnOprtInfo bod : bodList) {
                if (obodList.contains(bod)) {
                    continue;
                } else {
                    insertbod = busOperateDiagnosisMapper.insert(bod);
                }
            }
            //数据库有的，页面没有的，数据库删除数据
            for (SomOprnOprtInfo obod : obodList) {
                if (bodList.contains(obod)) {
                    continue;
                } else {
                    deleteobod = busOperateDiagnosisMapper.deleteByPrimaryKey(obod.getId());
                }
            }
        }
        return insertbsl + insertbocd + deleteobocd + insertbddtxy + insertbddtzy + deleteobddt + insertbod + deleteobod;//返回总共调整数据条数
    }

    @Override
    public ValidateMedicalResult getValidateMedicalResult(AllBusSettleListInfo allBusSettleListInfo) {
        Long settle_list_id = allBusSettleListInfo.getSomHiInvyBasInfo().getId();  //结算清单ID
        //处理结算清单基本数据，对象转Map
        Map<String, Object> data = MapUtil.objectToMap(allBusSettleListInfo.getSomHiInvyBasInfo());
        //处理疾病、手术
        List<BusDiseaseDiagnosisTrim> bddtList = allBusSettleListInfo.getBusDiseaseDiagnosisTrimList();
        List<BusDiseaseDiagnosisCost> disease = new ArrayList<>();
        for (BusDiseaseDiagnosisTrim bdd : bddtList) {
            BusDiseaseDiagnosisCost somDiag = new BusDiseaseDiagnosisCost();
            somDiag.setSettleListId(settle_list_id);
            somDiag.setSeq(bdd.getSeq());
            somDiag.setType(DrgConst.DIAGNOSTIC_TYPE_1);
            somDiag.setDscg_diag_codg(bdd.getC06c1());
            somDiag.setDscg_diag_name(bdd.getC07n1());
            somDiag.setDscg_diag_adm_cond(bdd.getC08c1());
            somDiag.setDscg_diag_dscg_cond(bdd.getC50c1());
            disease.add(somDiag);
        }
        List<SomOprnOprtInfo> oprt = allBusSettleListInfo.getBusOperateDiagnosisList();
        List<BusOperateDiagnosisCost> operationCost = new ArrayList<>();
        for (SomOprnOprtInfo bod : oprt) {
            BusOperateDiagnosisCost busOperateDiagnosisCost = new BusOperateDiagnosisCost();
            busOperateDiagnosisCost.setSettleListId(bod.getSettleListId());
            busOperateDiagnosisCost.setSeq(bod.getSeq());
            busOperateDiagnosisCost.setC35c(bod.getC35c());
            busOperateDiagnosisCost.setC36n(bod.getC36n());
            busOperateDiagnosisCost.setOprn_oprt_date(bod.getOprn_oprt_date());
            busOperateDiagnosisCost.setOprn_oprt_lv(bod.getOprn_oprt_lv());
            busOperateDiagnosisCost.setC39c(bod.getC39c());
            busOperateDiagnosisCost.setOprn_oprt_oper_name(bod.getOprn_oprt_oper_name());
            busOperateDiagnosisCost.setOprn_oprt_1_asit(bod.getOprn_oprt_1_asit());
            busOperateDiagnosisCost.setOprn_oprt_2_asit(bod.getOprn_oprt_2_asit());
            busOperateDiagnosisCost.setC42(bod.getC42());
            busOperateDiagnosisCost.setC43(bod.getC43());
            busOperateDiagnosisCost.setOprn_oprt_anst_dr_code(bod.getOprn_oprt_anst_dr_code());
            busOperateDiagnosisCost.setOprn_oprt_anst_dr_name(bod.getOprn_oprt_anst_dr_name());
            operationCost.add(busOperateDiagnosisCost);
        }
        SomMedcasInGroupCfgExample cfgMedicalGroupExample = new SomMedcasInGroupCfgExample();
        List<SomMedcasInGroupCfg> cfgMedicalGroups = cfgMedicalGroupMapper.selectByExample(cfgMedicalGroupExample);//全查已启用的清洗规则
        List<String> cleanDataRules = new ArrayList<>();
        for (SomMedcasInGroupCfg cmg : cfgMedicalGroups) {
            cleanDataRules.add(cmg.getErrorCode());
        }
        //调用校验工具,这里要做清洗处理
        List<Map<String, Object>> errors_and_score = DrgValidateUtil.validateData(data, disease, operationCost, DrgConst.CLEANDATA_TRUE, cleanDataRules, null, null);
        ValidateMedicalResult validateMedicalResult = new ValidateMedicalResult();
        if (!ValidateUtil.isEmpty(errors_and_score)) {
            //完整性、逻辑性校验情况
            Map<String, Object> completAndLogicErrorMap = errors_and_score.get(0);
            StringBuffer completErrorMsg = new StringBuffer("");
            StringBuffer logicErrorMsg = new StringBuffer("");
            for (String key : completAndLogicErrorMap.keySet()) {
                if (DrgConst.ERROR_TYPE_CE01.equals(key) || DrgConst.ERROR_TYPE_CE02.equals(key) ||
                        DrgConst.ERROR_TYPE_CE03.equals(key) || DrgConst.ERROR_TYPE_CE04.equals(key)) {
                    completErrorMsg.append(completAndLogicErrorMap.get(key));
                } else if (DrgConst.ERROR_TYPE_LE01.equals(key) || DrgConst.ERROR_TYPE_LE02.equals(key) ||
                        DrgConst.ERROR_TYPE_LE03.equals(key) || DrgConst.ERROR_TYPE_LE04.equals(key) ||
                        DrgConst.ERROR_TYPE_LE05.equals(key) || DrgConst.ERROR_TYPE_LE06.equals(key) ||
                        DrgConst.ERROR_TYPE_LE07.equals(key)) {
                    logicErrorMsg.append(completAndLogicErrorMap.get(key));
                }
            }
            if (completErrorMsg.length() > 0) {
                validateMedicalResult.setCompleteError(completErrorMsg.toString().split(";").length);  //有多少个分号，有多少个错误
                validateMedicalResult.setCompleteErrorMsg(completErrorMsg.deleteCharAt(completErrorMsg.length() - 1).toString()); //删除最后一个分号
            }
            if (logicErrorMsg.length() > 0) {
                validateMedicalResult.setLogicError(logicErrorMsg.toString().split(";").length);  //有多少个分号，有多少个错误
                validateMedicalResult.setLogicErrorMsg(logicErrorMsg.deleteCharAt(logicErrorMsg.length() - 1).toString()); //删除最后一个分号
            }
            //得分情况
            if (errors_and_score.size() > 1) {
                Map<String, Object> scoreMap = errors_and_score.get(1);
                validateMedicalResult.setRefer_sco(Double.valueOf((String) scoreMap.get("refer_sco")));
                validateMedicalResult.setScoreMsg((String) scoreMap.get("dedu_point_rea"));
                validateMedicalResult.setScoreErrors((HashMap<String, String>) scoreMap.get("scoreErrors"));
            }
            //获取前端字段id和错误描述
            validateMedicalResult.setCompleteErrors((HashMap<String, String>) completAndLogicErrorMap.get("completeErrors"));
            validateMedicalResult.setLogicErrors((HashMap<String, String>) completAndLogicErrorMap.get("logicErrors"));
        }
        return validateMedicalResult;
    }

    @Override
    public DrgsCostDayGroupInfo getStandCostAndDayAndGroupInfo(AllBusSettleListInfo allBusSettleListInfo, SysUserBase sysUserBase) {
        CaseYB2020Vo info = new CaseYB2020Vo();
        PreGroupDto dto = new PreGroupDto();
        //疾病诊断
        List<BusDiseaseDiagnosisTrim> bddtList = allBusSettleListInfo.getBusDiseaseDiagnosisTrimList();
        List<SomDiag> bddList = new ArrayList<>();
        for (BusDiseaseDiagnosisTrim bddt : bddtList) {
            if ("-".equals(bddt.getC06c1()) || "--".equals(bddt.getC06c1()) || "".equals(bddt.getC06c1())) {
                continue;
            } else {
                SomDiag somDiag = new SomDiag();
                somDiag.setSeq(bddt.getSeq());
                somDiag.setDscg_diag_codg(bddt.getC06c1());
                bddList.add(somDiag);
            }
        }
        info.setSomDiag(bddList);
        //手术
        List<SomOprnOprtInfo> bodList = new ArrayList<>();
        List<SomOprnOprtInfo> busOperateDiagnosisList = allBusSettleListInfo.getBusOperateDiagnosisList();
        for (SomOprnOprtInfo bod : busOperateDiagnosisList) {
            if ("-".equals(bod.getC35c()) || "--".equals(bod.getC35c()) || "".equals(bod.getC35c())) {
                continue;
            } else {
                bodList.add(bod);
            }
        }
        info.setSomOprnOprtInfo(bodList);
        //基础信息
        SomHiInvyBasInfo somHiInvyBasInfo = allBusSettleListInfo.getSomHiInvyBasInfo();
        if (!ValidateUtil.isEmpty(somHiInvyBasInfo.getB20())) {
            info.setB20(String.valueOf(somHiInvyBasInfo.getB20()));
        }
        if (!ValidateUtil.isEmpty(somHiInvyBasInfo.getA46c())) {
            info.setA46c(somHiInvyBasInfo.getA46c());
        }
        if (!ValidateUtil.isEmpty(somHiInvyBasInfo.getD01())) {
            info.setD01(String.valueOf(somHiInvyBasInfo.getD01()));
            dto.setZyzfy(new BigDecimal(somHiInvyBasInfo.getD01()));
        }
        if (!ValidateUtil.isEmpty(somHiInvyBasInfo.getA17())) {
            info.setA17(String.valueOf(somHiInvyBasInfo.getA17()));
        }
        if (!ValidateUtil.isEmpty(somHiInvyBasInfo.getB34c())) {
            info.setB34c(somHiInvyBasInfo.getB34c());
        }
        if (!ValidateUtil.isEmpty(somHiInvyBasInfo.getId())) {
            info.setSettleListId(String.valueOf(somHiInvyBasInfo.getId()));
        }
        if (!ValidateUtil.isEmpty(somHiInvyBasInfo.getA14())) {
            info.setA14(somHiInvyBasInfo.getA14().toString());
            if (!ValidateUtil.isEmpty(somHiInvyBasInfo.getB12()) && !ValidateUtil.isEmpty(somHiInvyBasInfo.getA13())) {
                long daysBetween = (DateUtil.stringToDate(somHiInvyBasInfo.getB12()).getTime() - DateUtil.stringToDate(somHiInvyBasInfo.getA13()).getTime()) / (3600 * 24 * 1000);
                if (daysBetween <= 28) {
                    info.setSf0100(String.valueOf(daysBetween));
                }
            }
        }
        if (!ValidateUtil.isEmpty(somHiInvyBasInfo.getA12c())) {
            info.setA12c(somHiInvyBasInfo.getA12c());
        }
        if (!ValidateUtil.isEmpty(somHiInvyBasInfo.getA48())) {
            info.setA48(somHiInvyBasInfo.getA48());
        }
        info.setA03(DrgConst.CURRENT_MEDICAL_TYPE);
        /**智能推荐*/
        HospitalDrgQueryParam hospitalDrgQueryParam = new HospitalDrgQueryParam();
        List<PreGroupVo> preGroupDipVos = new ArrayList<>();
        hospitalDrgQueryParam.setActive_flag(DrgConst.ACTIVE_FLAG_1);
        // 获取分组器信息
        List<HospitalVo> hospitalVoList = groupInfoMapper.queryAllGroupInfo(hospitalDrgQueryParam);
        // 获取DIP标杆信息
        List<SomDipStandard> dipBenchmarkList = tpdDipBenchmarkMapper.queryDipBenchmark(sysUserBase.getHospitalId());
        HospitalVo dipBuster = hospitalVoList.stream().filter(hospitalVo -> hospitalVo.getGrperType().equals(DrgConst.GROUP_TYPE_DIP)).findAny().get();
        ArrayList<String> diagnoseList = new ArrayList<>();
        for (SomDiag diagnose : info.getSomDiag()) {
            diagnoseList.add(diagnose.getDscg_diag_codg());
        }
        ArrayList<String> operateList = new ArrayList<>();
        for (SomOprnOprtInfo Operate : info.getSomOprnOprtInfo()) {
            operateList.add(Operate.getC35c());
        }
        for (String diagnose : diagnoseList) {
            DipGroupDataInfoVo dipGroupDataInfoVo = new DipGroupDataInfoVo();
            PreGroupUtil.setDipGroupInfo(dipGroupDataInfoVo, diagnose, operateList);
            DipGroupResult result = DipGroupUtil.group(dipGroupDataInfoVo, dipBuster, null, null);
            if (!ValidateUtil.isEmpty(result)
                    && !ValidateUtil.isEmpty(result.getSomDipGrpRcd())
                    && DrgConst.GROUP_STATE_1.equals(result.getSomDipGrpRcd().getGrpStas())) {
                PreGroupVo preGroupVo = PreGroupUtil.generaDipPreGroupInfo(result, dipBenchmarkList);
                preGroupVo.setDiagnoseCode(diagnose);
                preGroupDipVos.add(preGroupVo);
            }
        }
        //智能推荐
        List<SomDipStandard> dipAiRecommend = PreGroupUtil.dipAiRecommend(preGroupDipVos, dipBenchmarkList, dto);
        /***/
        // 分组信息
        DrgsCostDayGroupInfo drgsCostDayGroupInfo = settleListManageDao.queryGroupInfo(allBusSettleListInfo.getSomHiInvyBasInfo());
        if (ValidateUtil.isEmpty(drgsCostDayGroupInfo)) {
            drgsCostDayGroupInfo = new DrgsCostDayGroupInfo();
        }
        drgsCostDayGroupInfo.setDipAi(dipAiRecommend);

//
//        //查询该病人所有诊断、手术费用信息，并进行排序
//        List<BusDiseaseDiagnosisCost> busDiseaseDiagnosisCostList = new ArrayList<>();
//        for(BusDiseaseDiagnosisTrim bddt:bddtList){
//            if(ValidateUtil.isEmpty(bddt.getC06c1())||"-".equals(bddt.getC06c1())||"--".equals(bddt.getC06c1())||"".equals(bddt.getC06c1())){
//                continue;
//            }else {
//                BusDiseaseDiagnosisCost busDiseaseDiagnosisCost = new BusDiseaseDiagnosisCost();
//                busDiseaseDiagnosisCost.setSeq(bddt.getSeq());
//                busDiseaseDiagnosisCost.setDscg_diag_codg(bddt.getC06c1());
//                SomCodeStandFeeExample tpdIcdStandardCostExample = new SomCodeStandFeeExample();
//                tpdIcdStandardCostExample.createCriteria().andIcdCodeEqualTo(bddt.getC06c1());
//                tpdIcdStandardCostExample.createCriteria().andHospitalLevelEqualTo(sysUserBase.getHospLv());
////                if (!ValidateUtil.isEmpty(somHiInvyBasInfo.getB15()) && somHiInvyBasInfo.getB15().length() >= 4) {
////                    tpdIcdStandardCostExample.createCriteria().andStandardYearEqualTo(somHiInvyBasInfo.getB15().substring(0, 4));
////                }
//                List<SomCodeStandFee> tpdIcdStandardCostList = tpdIcdStandardCostMapper.selectByExample(tpdIcdStandardCostExample);//查询每一个诊断的费用情况
//                if (!ValidateUtil.isEmpty(tpdIcdStandardCostList)) {
//                    busDiseaseDiagnosisCost.setStandardFee(String.valueOf(tpdIcdStandardCostList.get(0).getStandardFee()));
//                } else {
//                    busDiseaseDiagnosisCost.setStandardFee("0");
//                }
//                busDiseaseDiagnosisCostList.add(busDiseaseDiagnosisCost);
//            }
//        }
//        List<BusOperateDiagnosisCost> busOperateDiagnosisCostList = new ArrayList<>();
//        for(SomOprnOprtInfo bodt:bodList){
//            if(ValidateUtil.isEmpty(bodt.getC35c())||"-".equals(bodt.getC35c())||"--".equals(bodt.getC35c())||"".equals(bodt.getC35c())){
//                continue;
//            }else {
//                BusOperateDiagnosisCost busOperateDiagnosisCost= new BusOperateDiagnosisCost();
//                busOperateDiagnosisCost.setSeq(bodt.getSeq());
//                busOperateDiagnosisCost.setC35c(bodt.getC35c());
//                SomCodeStandFeeExample tpdIcdStandardCostExample = new SomCodeStandFeeExample();
//                tpdIcdStandardCostExample.createCriteria().andIcdCodeEqualTo(bodt.getC35c());
//                tpdIcdStandardCostExample.createCriteria().andHospitalLevelEqualTo(sysUserBase.getHospLv());
//                List<SomCodeStandFee> tpdIcdStandardCostList1= tpdIcdStandardCostMapper.selectByExample(tpdIcdStandardCostExample);//查询每一个手术的费用情况
//                if(!ValidateUtil.isEmpty(tpdIcdStandardCostList1)){
//                    busOperateDiagnosisCost.setStandardFee(String.valueOf(tpdIcdStandardCostList1.get(0).getStandardFee()));
//                }else{
//                    busOperateDiagnosisCost.setStandardFee("0");
//                }
//                busOperateDiagnosisCostList.add(busOperateDiagnosisCost);
//            }
//        }
//        List<BusDiseaseDiagnosisCost> tempdDisease = new ArrayList<>();  //必须重新赋值到另外一个list，不能用同一个，否则修改了内容会对后面有影响
//        List<BusOperateDiagnosisCost> tempOperation = new ArrayList<>();
//        if (!ValidateUtil.isEmpty(busDiseaseDiagnosisCostList)) {
//            tempdDisease.addAll(busDiseaseDiagnosisCostList);
//            tempdDisease.sort((o1, o2) -> new BigDecimal(o2.getStandardFee()).compareTo(new BigDecimal(o1.getStandardFee())));//降序排序
//            for (int i = 0; i < tempdDisease.size(); i++) {
//                tempdDisease.get(i).setSeq(i);
//            }
//        }
//        if (!ValidateUtil.isEmpty(busOperateDiagnosisCostList)) {
//            tempOperation.addAll(busOperateDiagnosisCostList);
//            tempOperation.sort((o1, o2) -> new BigDecimal(o2.getStandardFee()).compareTo(new BigDecimal(o1.getStandardFee())));//降序排序
//            for (int i = 0; i < tempOperation.size(); i++) {
//                tempOperation.get(i).setSeq(i);
//            }
//        }
//        //将排序后的诊断和手术调整后重新调用分组服务，给出建议入组
//        List<SomDiag> finalDisease = new ArrayList<>();
//        for (BusDiseaseDiagnosisCost bdd : tempdDisease) {
//            SomDiag somDiag = new SomDiag();
//            somDiag.setSeq(bdd.getSeq());
//            somDiag.setDscg_diag_codg(bdd.getDscg_diag_codg());
//            finalDisease.add(somDiag);
//        }
//        List<SomOprnOprtInfo> finalOperation = new ArrayList<>();
//        for (BusOperateDiagnosisCost bod : tempOperation) {
//            SomOprnOprtInfo somOprnOprtInfo = new SomOprnOprtInfo();
//            somOprnOprtInfo.setSeq(bod.getSeq());
//            somOprnOprtInfo.setC35c(bod.getC35c());
//            finalOperation.add(somOprnOprtInfo);
//        }
//        String suggestDrgCode="";
//        if(!ValidateUtil.isEmpty(finalDisease)){
//            CaseYB2020Vo info1 = new CaseYB2020Vo();
//            //疾病诊断
//            info1.setSomDiag(finalDisease);
//            //手术
//            if(!ValidateUtil.isEmpty(finalOperation)){
//                info1.setSomOprnOprtInfo(finalOperation);
//            }
//            //基础信息
//            if(!ValidateUtil.isEmpty(somHiInvyBasInfo.getB20())){
//                info1.setB20(String.valueOf(somHiInvyBasInfo.getB20()));
//            }
//            if(!ValidateUtil.isEmpty(somHiInvyBasInfo.getA46c())){
//                info1.setA46c(somHiInvyBasInfo.getA46c());
//            }
//            if(!ValidateUtil.isEmpty(somHiInvyBasInfo.getD01())){
//                info1.setD01(String.valueOf(somHiInvyBasInfo.getD01()));
//            }
//            if(!ValidateUtil.isEmpty(somHiInvyBasInfo.getA17())){
//                info1.setA17(String.valueOf(somHiInvyBasInfo.getA17()));
//            }
//            if(!ValidateUtil.isEmpty(somHiInvyBasInfo.getB34c())){
//                info1.setB34c(somHiInvyBasInfo.getB34c());
//            }
//            if(!ValidateUtil.isEmpty(somHiInvyBasInfo.getId())){
//                info1.setSettleListId(String.valueOf(somHiInvyBasInfo.getId()));
//            }
//            if(!ValidateUtil.isEmpty(somHiInvyBasInfo.getA14())){
//                info1.setA14(somHiInvyBasInfo.getA14());
//                if(!ValidateUtil.isEmpty(somHiInvyBasInfo.getB12())&&!ValidateUtil.isEmpty(somHiInvyBasInfo.getA13())){
//                    long daysBetween = (DateUtil.stringToDate(somHiInvyBasInfo.getB12()).getTime()-DateUtil.stringToDate(somHiInvyBasInfo.getA13()).getTime())/(3600*24*1000);
//                    if(daysBetween<=28){
//                        info1.setSf0100(String.valueOf(daysBetween));
//                    }
//                }
//            }
//            if(!ValidateUtil.isEmpty(somHiInvyBasInfo.getA12c())){
//                info1.setA12c(somHiInvyBasInfo.getA12c());
//            }
//            if(!ValidateUtil.isEmpty(somHiInvyBasInfo.getA48())){
//                info1.setA48(somHiInvyBasInfo.getA48());
//            }
//            info1.setA03(DrgConst.CURRENT_MEDICAL_TYPE);
//            try {
////                suggestDrgCode = DrgYB2020GroupUtil.excuteDrgGroup(info1, drg).getSomDrgGrpRcd().getDrgCodg();//调用分组器接口
//            } catch (Exception e) {
//                e.printStackTrace();
//            }
//        }
//        if(!ValidateUtil.isEmpty(suggestDrgCode)){
//            drgsCostDayGroupInfo.setSuggestDrgCode(suggestDrgCode);
//            SomDrgNameExample tpdDrgsExample = new SomDrgNameExample();
//            tpdDrgsExample.createCriteria().andDrgCodeEqualTo(suggestDrgCode);
//            List<SomDrgName> tpdDrgsList= tpdDrgsMapper.selectByExample(tpdDrgsExample);
//            if(!ValidateUtil.isEmpty(tpdDrgsList)){
//                drgsCostDayGroupInfo.setSuggestDrgName(tpdDrgsList.get(0).getDrgName());
//            }
//            SomDrgStandardExample busBenchmarkExample = new SomDrgStandardExample();
//            SomDrgStandardExample.Criteria criteria = busBenchmarkExample.createCriteria();
//            criteria.andDrgCodeEqualTo(suggestDrgCode);
//            if(!ValidateUtil.isEmpty(somHiInvyBasInfo.getB15())&&busSettleList.getB15().length()>=4){
//                criteria.andStandardYearEqualTo(somHiInvyBasInfo.getB15().substring(0,4));
//            }else{
//                String nowYear = DateUtil.getCurDateTime().substring(0,4);
//                criteria.andStandardYearEqualTo(nowYear);
//            }
//            List<SomDrgStandard> busBenchmarkList = busBenchmarkMapper.selectByExample(busBenchmarkExample);
//            if(!ValidateUtil.isEmpty(busBenchmarkList)){
//                if(!ValidateUtil.isEmpty(busBenchmarkList.get(0).getStandardAvgFee())){
//                    drgsCostDayGroupInfo.setSuggestAvgInHosCost(String.valueOf(busBenchmarkList.get(0).getStandardAvgFee()));
//                }
//                if(!ValidateUtil.isEmpty(busBenchmarkList.get(0).getStandardAveHospDay())){
//                    drgsCostDayGroupInfo.setSuggestAvgDays(String.valueOf(busBenchmarkList.get(0).getStandardAveHospDay()));
//                }
//            }
//            //判断建议的分组费用和当前的分组费用对比，如果不符合条件，还原分组
//            if(!ValidateUtil.isEmpty(drgsCostDayGroupInfo.getSuggestAvgInHosCost())&&
//                    !ValidateUtil.isEmpty(drgsCostDayGroupInfo.getAvgInHosCost())){
//                if(new BigDecimal(drgsCostDayGroupInfo.getSuggestAvgInHosCost()).
//                        compareTo(new BigDecimal(drgsCostDayGroupInfo.getAvgInHosCost()))<0){
//                    drgsCostDayGroupInfo.setSuggestDrgCode("(无需调整)"+(drgsCostDayGroupInfo.getDrgCodg()==null?"":drgsCostDayGroupInfo.getDrgCodg()));
//                    drgsCostDayGroupInfo.setSuggestDrgName(drgsCostDayGroupInfo.getDrgName());
//                    drgsCostDayGroupInfo.setSuggestAvgInHosCost(drgsCostDayGroupInfo.getAvgInHosCost());
//                    drgsCostDayGroupInfo.setSuggestAvgDays(drgsCostDayGroupInfo.getAvgDays());
//                }
//            }
//        }else{
//            drgsCostDayGroupInfo.setSuggestDrgCode("(无需调整)"+(drgsCostDayGroupInfo.getDrgCodg()==null?"":drgsCostDayGroupInfo.getDrgCodg()));
//            drgsCostDayGroupInfo.setSuggestDrgName(drgsCostDayGroupInfo.getDrgName());
//            drgsCostDayGroupInfo.setSuggestAvgInHosCost(drgsCostDayGroupInfo.getAvgInHosCost());
//            drgsCostDayGroupInfo.setSuggestAvgDays(drgsCostDayGroupInfo.getAvgDays());
//        }
//
//        //DIP建议分组
//        String suggestDipCode="";
//        String suggestDipName="";
//        if(!ValidateUtil.isEmpty(finalDisease)){
//            CaseYBDip2020Vo dipInfo1 = new CaseYBDip2020Vo();
//            dipInfo1.setSomDiag(finalDisease);
//            dipInfo1.setSomOprnOprtInfo(finalOperation);
//            //基础信息
//            if(!ValidateUtil.isEmpty(somHiInvyBasInfo.getA14())){
//                dipInfo1.setA14(somHiInvyBasInfo.getA14());
//            }
//            if(!ValidateUtil.isEmpty(somHiInvyBasInfo.getA12c())){
//                dipInfo1.setA12c(somHiInvyBasInfo.getA12c());
//            }
//            if(!ValidateUtil.isEmpty(somHiInvyBasInfo.getB20())){
//                dipInfo1.setB20(String.valueOf(somHiInvyBasInfo.getB20()));
//            }
//            if(!ValidateUtil.isEmpty(somHiInvyBasInfo.getD01())){
//                dipInfo1.setD01(String.valueOf(somHiInvyBasInfo.getD01()));
//            }
//            if(!ValidateUtil.isEmpty(somHiInvyBasInfo.getB34c())){
//                dipInfo1.setB34c(somHiInvyBasInfo.getB34c());
//            }
//            if(!ValidateUtil.isEmpty(somHiInvyBasInfo.getId())){
//                dipInfo1.setSettleListId(String.valueOf(somHiInvyBasInfo.getId()));
//            }
//            if(!ValidateUtil.isEmpty(somHiInvyBasInfo.getA48())){
//                dipInfo1.setA48(somHiInvyBasInfo.getA48());
//            }
//            dipInfo1.setA03(DrgConst.CURRENT_MEDICAL_TYPE);
//            try {
////                DipResultVo dipResultVo = DipYB2020GroupUtil.excuteDipGroup(dipInfo1, dip);//调用DIP分组器接口
////                suggestDipCode = dipResultVo.getSomDipGrpRcd().getDipCodg();
////                suggestDipName = dipResultVo.getSomDipGrpRcd().getDipName();
//            } catch (Exception e) {
//                e.printStackTrace();
//            }
//        }
//        drgsCostDayGroupInfo.setSuggestDipCode(suggestDipCode);
//        drgsCostDayGroupInfo.setSuggestDipName(suggestDipName);
//        //获取DIP建议分组标杆
//        if(!ValidateUtil.isEmpty(suggestDipCode)){
////            drgsCostDayGroupInfo.setSuggestDipAvgInHosCost(getDipStandardCost(suggestDipCode));
////            drgsCostDayGroupInfo.setSuggestDipAvgDays("0");
//            SomDipStandardExample tpdDipBenchmarkExample = new SomDipStandardExample();
//            SomDipStandardExample.Criteria criteria = tpdDipBenchmarkExample.createCriteria();
//            criteria.andDipCodeEqualTo(suggestDipCode);
//            if(!ValidateUtil.isEmpty(somHiInvyBasInfo.getB15())&&busSettleList.getB15().length()>=4){
//                criteria.andStandardYearEqualTo(somHiInvyBasInfo.getB15().substring(0,4));
//            }else{
//                String nowYear = DateUtil.getCurDateTime().substring(0,4);
//                criteria.andStandardYearEqualTo(nowYear);
//            }
//            List<SomDipStandard> tpdDipBenchmarkList = tpdDipBenchmarkMapper.selectByExample(tpdDipBenchmarkExample);
//            if(!ValidateUtil.isEmpty(tpdDipBenchmarkList)){
//                if(!ValidateUtil.isEmpty(tpdDipBenchmarkList.get(0).getDipStandardInpf())){
//                    drgsCostDayGroupInfo.setSuggestDipAvgInHosCost(String.valueOf(tpdDipBenchmarkList.get(0).getDipStandardInpf()));
//                }
//                if(!ValidateUtil.isEmpty(tpdDipBenchmarkList.get(0).getDipStandardIptDays())){
//                    drgsCostDayGroupInfo.setSuggestDipAvgDays(String.valueOf(tpdDipBenchmarkList.get(0).getDipStandardIptDays()));
//                }
//            }
//            if(!ValidateUtil.isEmpty(drgsCostDayGroupInfo.getSuggestDipAvgInHosCost())&&
//                    !ValidateUtil.isEmpty(drgsCostDayGroupInfo.getDipAvgInHosCost())){
//                //判断建议的分组费用和当前的分组费用对比，如果不符合条件，还原分组
//                if(new BigDecimal(drgsCostDayGroupInfo.getSuggestDipAvgInHosCost()).
//                        compareTo(new BigDecimal(drgsCostDayGroupInfo.getDipAvgInHosCost()))<=0){
//                    drgsCostDayGroupInfo.setSuggestDipCode("(无需调整)"+(drgsCostDayGroupInfo.getDipCodg()==null?"":drgsCostDayGroupInfo.getDipCodg()));
//                    drgsCostDayGroupInfo.setSuggestDipName(drgsCostDayGroupInfo.getDipName());
//                    drgsCostDayGroupInfo.setSuggestDipAvgInHosCost(drgsCostDayGroupInfo.getDipAvgInHosCost());
//                    drgsCostDayGroupInfo.setSuggestDipAvgDays(drgsCostDayGroupInfo.getDipAvgDays());
//                }else{
//                    //判断dip编码是否含有病人手术，有则建议，否则不建议
//                    int flag=0;
//                    for(SomOprnOprtInfo bod:busOperateDiagnosisList){
//                        if(!ValidateUtil.isEmpty(bod)){
//                            if(drgsCostDayGroupInfo.getSuggestDipCode().indexOf(bod.getC35c()) != -1){
//                                flag++;
//                            }
//                        }
//                    }
//                    if(flag==0){
//                        drgsCostDayGroupInfo.setSuggestDipCode("(无需调整)"+(drgsCostDayGroupInfo.getDipCodg()==null?"":drgsCostDayGroupInfo.getDipCodg()));
//                        drgsCostDayGroupInfo.setSuggestDipName(drgsCostDayGroupInfo.getDipName());
//                        drgsCostDayGroupInfo.setSuggestDipAvgInHosCost(drgsCostDayGroupInfo.getDipAvgInHosCost());
//                        drgsCostDayGroupInfo.setSuggestDipAvgDays(drgsCostDayGroupInfo.getDipAvgDays());
//                    }
//                }
//            }
//        }else{
//            drgsCostDayGroupInfo.setSuggestDipCode("(无需调整)"+(drgsCostDayGroupInfo.getDipCodg()==null?"":drgsCostDayGroupInfo.getDipCodg()));
//            drgsCostDayGroupInfo.setSuggestDipName(drgsCostDayGroupInfo.getDipName());
//            drgsCostDayGroupInfo.setSuggestDipAvgInHosCost(drgsCostDayGroupInfo.getDipAvgInHosCost());
//            drgsCostDayGroupInfo.setSuggestDipAvgDays(drgsCostDayGroupInfo.getDipAvgDays());
//        }
//        //疾病、手术资源消耗排名
//        List<DiagnoseCostRank> diagnoseCostRankList = new ArrayList<>();
//        for(BusDiseaseDiagnosisCost td:tempdDisease){
//            DiagnoseCostRank diagnoseCostRank = new DiagnoseCostRank();
//            String currentCode = td.getDscg_diag_codg();
//            for(SomDiag bddl:bddList){
//                if(currentCode.equals(bddl.getDscg_diag_codg())){
//                    diagnoseCostRank.setSeq(String.valueOf(bddl.getSeq()));
//                }
//            }
//            diagnoseCostRank.setCost(td.getStandardFee());
//            diagnoseCostRankList.add(diagnoseCostRank);
//        }
//        if(!ValidateUtil.isEmpty(diagnoseCostRankList)){
//            drgsCostDayGroupInfo.setDiagnoseCostRankList(diagnoseCostRankList);
//        }
//        List<DiagnoseCostRank> operateCostRankList = new ArrayList<>();
//        for(BusOperateDiagnosisCost to : tempOperation){
//            DiagnoseCostRank diagnoseCostRank = new DiagnoseCostRank();
//            String currentOperate = to.getC35c();
//            for(SomOprnOprtInfo bodl:bodList){
//                if(currentOperate.equals(bodl.getC35c())){
//                    diagnoseCostRank.setSeq(String.valueOf(bodl.getSeq()));
//                }
//            }
//            diagnoseCostRank.setCost(to.getStandardFee());
//            operateCostRankList.add(diagnoseCostRank);
//        }
//        if(!ValidateUtil.isEmpty(operateCostRankList)){
//            drgsCostDayGroupInfo.setOperateCostRankList(operateCostRankList);
//        }
        return drgsCostDayGroupInfo;
    }

    @Override
    public List<BusSettleListMainInfoForDoctor> fetchListForDoctor(SettleListMainInfoQueryParam queryParam, SysUserBase sysUserBase, Integer pageSize, Integer pageNum) {
        PageHelper.startPage(pageNum, pageSize);
        List<BusSettleListMainInfoForDoctor> list = settleListManageDao.getListForDoctor(queryParam);
        for (BusSettleListMainInfoForDoctor sl : list) {
            //结算清单基础数据
            SomHiInvyBasInfo somHiInvyBasInfo = getSettleListBaseInfoById(sl.getId());
            //结算清单疾病诊断数据
            List<BusDiseaseDiagnosisTrim> busDiseaseDiagnosisTrimList = getSettleListDiseaseInfoById(sl.getId());
            //结算清单手术数据
            List<SomOprnOprtInfo> busOperateDiagnosisList = getSettleListOperationInfoById(sl.getId());


            AllBusSettleListInfo allBusSettleListInfo = new AllBusSettleListInfo();
            allBusSettleListInfo.setSomHiInvyBasInfo(somHiInvyBasInfo);
            allBusSettleListInfo.setBusDiseaseDiagnosisTrimList(busDiseaseDiagnosisTrimList);
            //写入手术数据
            allBusSettleListInfo.setBusOperateDiagnosisList(busOperateDiagnosisList);
            //调用分组和推荐分组
            SuggestGroupForDoctor suggestGroupForDoctor = getSuggestGroupForDoctor(allBusSettleListInfo, sysUserBase);
            sl.setMainDiagIsChoErr(suggestGroupForDoctor.getMainDiagIsChoErr());
            sl.setMainOprnIsChoErr(suggestGroupForDoctor.getMainOprnIsChoErr());
            sl.setDipCodeAndName(suggestGroupForDoctor.getDipCodeAndName());
            sl.setNewDipCodeAndName(suggestGroupForDoctor.getNewDipCodeAndName());
            sl.setDrgsCodeAndName(suggestGroupForDoctor.getDrgsCodeAndName());
            sl.setNewDrgCodeAndName(suggestGroupForDoctor.getNewDrgCodeAndName());
        }
        list.sort((o1, o2) -> o2.getMainDiagIsChoErr().compareTo(o1.getMainDiagIsChoErr()));
        list.sort((o1, o2) -> o2.getMainOprnIsChoErr().compareTo(o1.getMainOprnIsChoErr()));
        return list;
    }

    public SuggestGroupForDoctor getSuggestGroupForDoctor(AllBusSettleListInfo allBusSettleListInfo, SysUserBase sysUserBase) {
        CaseYB2020Vo info = new CaseYB2020Vo();
        //疾病诊断
        List<BusDiseaseDiagnosisTrim> bddtList = allBusSettleListInfo.getBusDiseaseDiagnosisTrimList();
        List<SomDiag> bddList = new ArrayList<>();
        for (BusDiseaseDiagnosisTrim bddt : bddtList) {
            if ("-".equals(bddt.getC06c1()) || "--".equals(bddt.getC06c1()) || "".equals(bddt.getC06c1())) {
                continue;
            } else {
                SomDiag somDiag = new SomDiag();
                somDiag.setSeq(bddt.getSeq());
                somDiag.setDscg_diag_codg(bddt.getC06c1());
                bddList.add(somDiag);
            }
        }
        info.setSomDiag(bddList);
        //手术
        List<SomOprnOprtInfo> bodList = new ArrayList<>();
        List<SomOprnOprtInfo> busOperateDiagnosisList = allBusSettleListInfo.getBusOperateDiagnosisList();
        for (SomOprnOprtInfo bod : busOperateDiagnosisList) {
            if ("-".equals(bod.getC35c()) || "--".equals(bod.getC35c()) || "".equals(bod.getC35c())) {
                continue;
            } else {
                bodList.add(bod);
            }
        }
        info.setSomOprnOprtInfo(bodList);
        //基础信息
        SomHiInvyBasInfo somHiInvyBasInfo = allBusSettleListInfo.getSomHiInvyBasInfo();
        if (!ValidateUtil.isEmpty(somHiInvyBasInfo.getB20())) {
            info.setB20(String.valueOf(somHiInvyBasInfo.getB20()));
        }
        if (!ValidateUtil.isEmpty(somHiInvyBasInfo.getA46c())) {
            info.setA46c(somHiInvyBasInfo.getA46c());
        }
        if (!ValidateUtil.isEmpty(somHiInvyBasInfo.getD01())) {
            info.setD01(String.valueOf(somHiInvyBasInfo.getD01()));
        }
        if (!ValidateUtil.isEmpty(somHiInvyBasInfo.getA17())) {
            info.setA17(String.valueOf(somHiInvyBasInfo.getA17()));
        }
        if (!ValidateUtil.isEmpty(somHiInvyBasInfo.getB34c())) {
            info.setB34c(somHiInvyBasInfo.getB34c());
        }
        if (!ValidateUtil.isEmpty(somHiInvyBasInfo.getId())) {
            info.setSettleListId(String.valueOf(somHiInvyBasInfo.getId()));
        }
        if (!ValidateUtil.isEmpty(somHiInvyBasInfo.getA14())) {
            info.setA14(somHiInvyBasInfo.getA14().toString());
            if (!ValidateUtil.isEmpty(somHiInvyBasInfo.getB12()) && !ValidateUtil.isEmpty(somHiInvyBasInfo.getA13())) {
                long daysBetween = (DateUtil.stringToDate(somHiInvyBasInfo.getB12()).getTime() - DateUtil.stringToDate(somHiInvyBasInfo.getA13()).getTime()) / (3600 * 24 * 1000);
                if (daysBetween <= 28) {
                    info.setSf0100(String.valueOf(daysBetween));
                }
            }
        }
        if (!ValidateUtil.isEmpty(somHiInvyBasInfo.getA12c())) {
            info.setA12c(somHiInvyBasInfo.getA12c());
        }
        if (!ValidateUtil.isEmpty(somHiInvyBasInfo.getA48())) {
            info.setA48(somHiInvyBasInfo.getA48());
        }
        info.setA03(DrgConst.CURRENT_MEDICAL_TYPE);
        String a56 = somHiInvyBasInfo.getA56();
        if (!ValidateUtil.isEmpty(somHiInvyBasInfo.getA56()) && a56.length() > 4) {
            a56 = a56.substring(0, 4);
        } else {
            throw new AppException("参保地有误");
        }


        //查询DRG分组器配置信息，URL地址
        HospitalDrgVo drg = GroupCommonUtil.getDrgGroupInfo(a56);
        String drgCodg = "";
        try {
            drgCodg = DrgYB2020GroupUtil.excuteDrgGroup(info, drg).getSomDrgGrpRcd().getDrgCodg();//调用分组器接口
        } catch (Exception e) {
            e.printStackTrace();
        }
        SuggestGroupForDoctor suggestGroupForDoctor = new SuggestGroupForDoctor();
        if (!ValidateUtil.isEmpty(drgCodg)) {
            String drgName = "";
            SomDrgNameExample tpdDrgsExample = new SomDrgNameExample();
            tpdDrgsExample.createCriteria().andDrgCodeEqualTo(drgCodg);
            List<SomDrgName> tpdDrgsList = tpdDrgsMapper.selectByExample(tpdDrgsExample);
            if (!ValidateUtil.isEmpty(tpdDrgsList)) {
                drgName = tpdDrgsList.get(0).getDrgName();
            }
            suggestGroupForDoctor.setDrgsCodeAndName(drgCodg + drgName);
            SomDrgStandardExample busBenchmarkExample = new SomDrgStandardExample();
            SomDrgStandardExample.Criteria criteria = busBenchmarkExample.createCriteria();
            criteria.andDrgCodeEqualTo(drgCodg);
            if (!ValidateUtil.isEmpty(somHiInvyBasInfo.getB15()) && somHiInvyBasInfo.getB15().length() >= 4) {
                criteria.andStandardYearEqualTo(somHiInvyBasInfo.getB15().substring(0, 4));
            } else {
                String nowYear = DateUtil.getCurDateTime().substring(0, 4);
                criteria.andStandardYearEqualTo(nowYear);
            }
            List<SomDrgStandard> busBenchmarkList = busBenchmarkMapper.selectByExample(busBenchmarkExample);
            if (!ValidateUtil.isEmpty(busBenchmarkList)) {
                if (!ValidateUtil.isEmpty(busBenchmarkList.get(0).getStandardAvgFee())) {
                    suggestGroupForDoctor.setAvgInHosCost(String.valueOf(busBenchmarkList.get(0).getStandardAvgFee()));
                }
            }
        }
        //DIP分组
        CaseYBDip2020Vo dipInfo = new CaseYBDip2020Vo();
        dipInfo.setSomDiag(bddList);
        dipInfo.setSomOprnOprtInfo(bodList);
        //基础信息
        if (!ValidateUtil.isEmpty(somHiInvyBasInfo.getA14())) {
            dipInfo.setA14(somHiInvyBasInfo.getA14());
        }
        if (!ValidateUtil.isEmpty(somHiInvyBasInfo.getA12c())) {
            dipInfo.setA12c(somHiInvyBasInfo.getA12c());
        }
        if (!ValidateUtil.isEmpty(somHiInvyBasInfo.getB20())) {
            dipInfo.setB20(String.valueOf(somHiInvyBasInfo.getB20()));
        }
        if (!ValidateUtil.isEmpty(somHiInvyBasInfo.getD01())) {
            dipInfo.setD01(String.valueOf(somHiInvyBasInfo.getD01()));
        }
        if (!ValidateUtil.isEmpty(somHiInvyBasInfo.getB34c())) {
            dipInfo.setB34c(somHiInvyBasInfo.getB34c());
        }
        if (!ValidateUtil.isEmpty(somHiInvyBasInfo.getId())) {
            dipInfo.setSettleListId(String.valueOf(somHiInvyBasInfo.getId()));
        }
        if (!ValidateUtil.isEmpty(somHiInvyBasInfo.getA48())) {
            dipInfo.setA48(somHiInvyBasInfo.getA48());
        }


        dipInfo.setA03(DrgConst.CURRENT_MEDICAL_TYPE);
        //查询分DIP组器配置信息，URL地址
        HospitalDipVo dip = new HospitalDipVo();
        HospitalDrgQueryParam hospitalDrgQueryParam = new HospitalDrgQueryParam();
        hospitalDrgQueryParam.setActive_flag(DrgConst.ACTIVE_FLAG_1);
        hospitalDrgQueryParam.setGrper_type(DrgConst.GROUP_TYPE_DIP);
        List<HospitalVo> groupInfo = groupInfoMapper.getGroupInfo(hospitalDrgQueryParam);
        dip.setGrperInfoId(groupInfo.get(0).getGrperInfoId());
        dip.setDipGroupType(groupInfo.get(0).getGrperType());
        dip.setDipGroupVersion(groupInfo.get(0).getGrperVer());
        dip.setGrperUrlDip(groupInfo.get(0).getGroupUrl());
        dip.setHospitalId(sysUserBase.getHospitalId());
        String dipCodg = "-";
        String dipName = "-";
        try {
            DipResultVo dipResultVo = DipYB2020GroupUtil.excuteDipGroup(dipInfo, dip);//调用DIP分组器接口
            dipCodg = dipResultVo.getSomDipGrpRcd().getDipCodg();
            dipName = dipResultVo.getSomDipGrpRcd().getDipName();
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (!ValidateUtil.isEmpty(dipCodg)) {
            suggestGroupForDoctor.setDipCodeAndName(dipCodg + dipName);
        } else {
            suggestGroupForDoctor.setDipCodeAndName("未入组");
        }
        //获取DIP标杆
        if (!ValidateUtil.isEmpty(dipCodg)) {
            suggestGroupForDoctor.setDipAvgInHosCost("0");
            //drgsCostDayGroupInfo.setDipAvgInHosCost(getDipStandardCost(dipCodg));
            SomDipStandardExample tpdDipBenchmarkExample = new SomDipStandardExample();
            SomDipStandardExample.Criteria criteria = tpdDipBenchmarkExample.createCriteria();
            criteria.andDipCodeEqualTo(dipCodg);
            if (!ValidateUtil.isEmpty(somHiInvyBasInfo.getB15()) && somHiInvyBasInfo.getB15().length() >= 4) {
                criteria.andStandardYearEqualTo(somHiInvyBasInfo.getB15().substring(0, 4));
            } else {
                String nowYear = DateUtil.getCurDateTime().substring(0, 4);
                criteria.andStandardYearEqualTo(nowYear);
            }
            List<SomDipStandard> tpdDipBenchmarkList = tpdDipBenchmarkMapper.selectByExample(tpdDipBenchmarkExample);
            if (!ValidateUtil.isEmpty(tpdDipBenchmarkList)) {
                if (!ValidateUtil.isEmpty(tpdDipBenchmarkList.get(0).getDipStandardInpf())) {
                    suggestGroupForDoctor.setDipAvgInHosCost(String.valueOf(tpdDipBenchmarkList.get(0).getDipStandardInpf()));
                }
            }
        }
        //查询该病人所有诊断、手术费用信息，并进行排序
        List<BusDiseaseDiagnosisCost> busDiseaseDiagnosisCostList = new ArrayList<>();
        String mainDia = "";
        for (BusDiseaseDiagnosisTrim bddt : bddtList) {
            if (ValidateUtil.isEmpty(bddt.getC06c1()) || "-".equals(bddt.getC06c1()) || "--".equals(bddt.getC06c1()) || "".equals(bddt.getC06c1())) {
                continue;
            } else {
                if (bddt.getSeq() == 0) {
                    mainDia = bddt.getC06c1();
                }
                BusDiseaseDiagnosisCost busDiseaseDiagnosisCost = new BusDiseaseDiagnosisCost();
                busDiseaseDiagnosisCost.setSeq(bddt.getSeq());
                busDiseaseDiagnosisCost.setDscg_diag_codg(bddt.getC06c1());
                SomCodeStandFeeExample tpdIcdStandardCostExample = new SomCodeStandFeeExample();
                tpdIcdStandardCostExample.createCriteria().andIcdCodeEqualTo(bddt.getC06c1());
                tpdIcdStandardCostExample.createCriteria().andHospitalLevelEqualTo(sysUserBase.getHospLv());
                List<SomCodeStandFee> tpdIcdStandardCostList = tpdIcdStandardCostMapper.selectByExample(tpdIcdStandardCostExample);//查询每一个诊断的费用情况
                if (!ValidateUtil.isEmpty(tpdIcdStandardCostList)) {
                    busDiseaseDiagnosisCost.setStandardFee(String.valueOf(tpdIcdStandardCostList.get(0).getStandardFee()));
                } else {
                    busDiseaseDiagnosisCost.setStandardFee("0");
                }
                busDiseaseDiagnosisCostList.add(busDiseaseDiagnosisCost);
            }
        }
        List<BusOperateDiagnosisCost> busOperateDiagnosisCostList = new ArrayList<>();
        String mainOpr = "";
        for (SomOprnOprtInfo bodt : bodList) {
            if (ValidateUtil.isEmpty(bodt.getC35c()) || "-".equals(bodt.getC35c()) || "--".equals(bodt.getC35c()) || "".equals(bodt.getC35c())) {
                continue;
            } else {
                if (bodt.getSeq() == 0) {
                    mainOpr = bodt.getC35c();
                }
                BusOperateDiagnosisCost busOperateDiagnosisCost = new BusOperateDiagnosisCost();
                busOperateDiagnosisCost.setSeq(bodt.getSeq());
                busOperateDiagnosisCost.setC35c(bodt.getC35c());
                SomCodeStandFeeExample tpdIcdStandardCostExample = new SomCodeStandFeeExample();
                tpdIcdStandardCostExample.createCriteria().andIcdCodeEqualTo(bodt.getC35c());
                tpdIcdStandardCostExample.createCriteria().andHospitalLevelEqualTo(sysUserBase.getHospLv());
                List<SomCodeStandFee> tpdIcdStandardCostList1 = tpdIcdStandardCostMapper.selectByExample(tpdIcdStandardCostExample);//查询每一个手术的费用情况
                if (!ValidateUtil.isEmpty(tpdIcdStandardCostList1)) {
                    busOperateDiagnosisCost.setStandardFee(String.valueOf(tpdIcdStandardCostList1.get(0).getStandardFee()));
                } else {
                    busOperateDiagnosisCost.setStandardFee("0");
                }
                busOperateDiagnosisCostList.add(busOperateDiagnosisCost);
            }
        }
        List<BusDiseaseDiagnosisCost> tempdDisease = new ArrayList<>();  //必须重新赋值到另外一个list，不能用同一个，否则修改了内容会对后面有影响
        List<BusOperateDiagnosisCost> tempOperation = new ArrayList<>();
        suggestGroupForDoctor.setMainDiagIsChoErr("0");
        if (!ValidateUtil.isEmpty(busDiseaseDiagnosisCostList)) {
            tempdDisease.addAll(busDiseaseDiagnosisCostList);
            tempdDisease.sort((o1, o2) -> new BigDecimal(o2.getStandardFee()).compareTo(new BigDecimal(o1.getStandardFee())));//降序排序
            for (int i = 0; i < tempdDisease.size(); i++) {
                tempdDisease.get(i).setSeq(i);
            }
            if (!(tempdDisease.get(0).getDscg_diag_codg().equals(mainDia))) {
                suggestGroupForDoctor.setMainDiagIsChoErr("1");
            } else {
                suggestGroupForDoctor.setMainDiagIsChoErr("0");
            }
        } else {
            suggestGroupForDoctor.setMainDiagIsChoErr("0");
        }
        suggestGroupForDoctor.setMainOprnIsChoErr("0");
        if (!ValidateUtil.isEmpty(busOperateDiagnosisCostList)) {
            tempOperation.addAll(busOperateDiagnosisCostList);
            tempOperation.sort((o1, o2) -> new BigDecimal(o2.getStandardFee()).compareTo(new BigDecimal(o1.getStandardFee())));//降序排序
            for (int i = 0; i < tempOperation.size(); i++) {
                tempOperation.get(i).setSeq(i);
            }
            if (!(tempOperation.get(0).getC35c().equals(mainOpr))) {
                suggestGroupForDoctor.setMainOprnIsChoErr("1");
            } else {
                suggestGroupForDoctor.setMainOprnIsChoErr("0");
            }
        } else {
            suggestGroupForDoctor.setMainOprnIsChoErr("0");
        }
        //将排序后的诊断和手术调整后重新调用分组服务，给出建议入组
        List<SomDiag> finalDisease = new ArrayList<>();
        for (BusDiseaseDiagnosisCost bdd : tempdDisease) {
            SomDiag somDiag = new SomDiag();
            somDiag.setSeq(bdd.getSeq());
            somDiag.setDscg_diag_codg(bdd.getDscg_diag_codg());
            finalDisease.add(somDiag);
        }
        List<SomOprnOprtInfo> finalOperation = new ArrayList<>();
        for (BusOperateDiagnosisCost bod : tempOperation) {
            SomOprnOprtInfo somOprnOprtInfo = new SomOprnOprtInfo();
            somOprnOprtInfo.setSeq(bod.getSeq());
            somOprnOprtInfo.setC35c(bod.getC35c());
            finalOperation.add(somOprnOprtInfo);
        }
        String suggestDrgCode = "";
        if (!ValidateUtil.isEmpty(finalDisease)) {
            CaseYB2020Vo info1 = new CaseYB2020Vo();
            //疾病诊断
            info1.setSomDiag(finalDisease);
            //手术
            if (!ValidateUtil.isEmpty(finalOperation)) {
                info1.setSomOprnOprtInfo(finalOperation);
            }
            //基础信息
            if (!ValidateUtil.isEmpty(somHiInvyBasInfo.getB20())) {
                info1.setB20(String.valueOf(somHiInvyBasInfo.getB20()));
            }
            if (!ValidateUtil.isEmpty(somHiInvyBasInfo.getA46c())) {
                info1.setA46c(somHiInvyBasInfo.getA46c());
            }
            if (!ValidateUtil.isEmpty(somHiInvyBasInfo.getD01())) {
                info1.setD01(String.valueOf(somHiInvyBasInfo.getD01()));
            }
            if (!ValidateUtil.isEmpty(somHiInvyBasInfo.getA17())) {
                info1.setA17(String.valueOf(somHiInvyBasInfo.getA17()));
            }
            if (!ValidateUtil.isEmpty(somHiInvyBasInfo.getB34c())) {
                info1.setB34c(somHiInvyBasInfo.getB34c());
            }
            if (!ValidateUtil.isEmpty(somHiInvyBasInfo.getId())) {
                info1.setSettleListId(String.valueOf(somHiInvyBasInfo.getId()));
            }
            if (!ValidateUtil.isEmpty(somHiInvyBasInfo.getA14())) {
                info1.setA14(somHiInvyBasInfo.getA14().toString());
                if (!ValidateUtil.isEmpty(somHiInvyBasInfo.getB12()) && !ValidateUtil.isEmpty(somHiInvyBasInfo.getA13())) {
                    long daysBetween = (DateUtil.stringToDate(somHiInvyBasInfo.getB12()).getTime() - DateUtil.stringToDate(somHiInvyBasInfo.getA13()).getTime()) / (3600 * 24 * 1000);
                    if (daysBetween <= 28) {
                        info1.setSf0100(String.valueOf(daysBetween));
                    }
                }
            }
            if (!ValidateUtil.isEmpty(somHiInvyBasInfo.getA12c())) {
                info1.setA12c(somHiInvyBasInfo.getA12c());
            }
            if (!ValidateUtil.isEmpty(somHiInvyBasInfo.getA48())) {
                info1.setA48(somHiInvyBasInfo.getA48());
            }
            info1.setA03(DrgConst.CURRENT_MEDICAL_TYPE);
            try {
                suggestDrgCode = DrgYB2020GroupUtil.excuteDrgGroup(info1, drg).getSomDrgGrpRcd().getDrgCodg();//调用分组器接口
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        if (!ValidateUtil.isEmpty(suggestDrgCode)) {
            String suggestDrgName = "";
            SomDrgNameExample tpdDrgsExample = new SomDrgNameExample();
            tpdDrgsExample.createCriteria().andDrgCodeEqualTo(suggestDrgCode);
            List<SomDrgName> tpdDrgsList = tpdDrgsMapper.selectByExample(tpdDrgsExample);
            if (!ValidateUtil.isEmpty(tpdDrgsList)) {
                suggestDrgName = tpdDrgsList.get(0).getDrgName();
            }
            suggestGroupForDoctor.setNewDrgCodeAndName(suggestDrgCode + suggestDrgName);
            SomDrgStandardExample busBenchmarkExample = new SomDrgStandardExample();
            SomDrgStandardExample.Criteria criteria = busBenchmarkExample.createCriteria();
            criteria.andDrgCodeEqualTo(suggestDrgCode);
            if (!ValidateUtil.isEmpty(somHiInvyBasInfo.getB15()) && somHiInvyBasInfo.getB15().length() >= 4) {
                criteria.andStandardYearEqualTo(somHiInvyBasInfo.getB15().substring(0, 4));
            } else {
                String nowYear = DateUtil.getCurDateTime().substring(0, 4);
                criteria.andStandardYearEqualTo(nowYear);
            }
            List<SomDrgStandard> busBenchmarkList = busBenchmarkMapper.selectByExample(busBenchmarkExample);
            if (!ValidateUtil.isEmpty(busBenchmarkList)) {
                if (!ValidateUtil.isEmpty(busBenchmarkList.get(0).getStandardAvgFee())) {
                    suggestGroupForDoctor.setSuggestAvgInHosCost(String.valueOf(busBenchmarkList.get(0).getStandardAvgFee()));
                }
            }
            //判断建议的分组费用和当前的分组费用对比，如果不符合条件，还原分组
            if (!ValidateUtil.isEmpty(suggestGroupForDoctor.getSuggestAvgInHosCost()) &&
                    !ValidateUtil.isEmpty(suggestGroupForDoctor.getAvgInHosCost())) {
                if (new BigDecimal(suggestGroupForDoctor.getSuggestAvgInHosCost()).
                        compareTo(new BigDecimal(suggestGroupForDoctor.getAvgInHosCost())) < 0) {
                    suggestGroupForDoctor.setNewDrgCodeAndName("(无需调整)" + (suggestGroupForDoctor.getDrgsCodeAndName() == null ? "" : suggestGroupForDoctor.getDrgsCodeAndName()));
                    suggestGroupForDoctor.setSuggestAvgInHosCost(suggestGroupForDoctor.getAvgInHosCost());
                }
            }
        } else {
            suggestGroupForDoctor.setNewDrgCodeAndName("(无需调整)" + (suggestGroupForDoctor.getDrgsCodeAndName() == null ? "" : suggestGroupForDoctor.getDrgsCodeAndName()));
            suggestGroupForDoctor.setSuggestAvgInHosCost(suggestGroupForDoctor.getAvgInHosCost());
        }

        //DIP建议分组
        String suggestDipCode = "";
        String suggestDipName = "";
        if (!ValidateUtil.isEmpty(finalDisease)) {
            CaseYBDip2020Vo dipInfo1 = new CaseYBDip2020Vo();
            dipInfo1.setSomDiag(finalDisease);
            dipInfo1.setSomOprnOprtInfo(finalOperation);
            //基础信息
            if (!ValidateUtil.isEmpty(somHiInvyBasInfo.getA14())) {
                dipInfo1.setA14(somHiInvyBasInfo.getA14());
            }
            if (!ValidateUtil.isEmpty(somHiInvyBasInfo.getA12c())) {
                dipInfo1.setA12c(somHiInvyBasInfo.getA12c());
            }
            if (!ValidateUtil.isEmpty(somHiInvyBasInfo.getB20())) {
                dipInfo1.setB20(String.valueOf(somHiInvyBasInfo.getB20()));
            }
            if (!ValidateUtil.isEmpty(somHiInvyBasInfo.getD01())) {
                dipInfo1.setD01(String.valueOf(somHiInvyBasInfo.getD01()));
            }
            if (!ValidateUtil.isEmpty(somHiInvyBasInfo.getB34c())) {
                dipInfo1.setB34c(somHiInvyBasInfo.getB34c());
            }
            if (!ValidateUtil.isEmpty(somHiInvyBasInfo.getId())) {
                dipInfo1.setSettleListId(String.valueOf(somHiInvyBasInfo.getId()));
            }
            if (!ValidateUtil.isEmpty(somHiInvyBasInfo.getA48())) {
                dipInfo1.setA48(somHiInvyBasInfo.getA48());
            }
            dipInfo1.setA03(DrgConst.CURRENT_MEDICAL_TYPE);
            try {
                DipResultVo dipResultVo = DipYB2020GroupUtil.excuteDipGroup(dipInfo1, dip);//调用DIP分组器接口
                suggestDipCode = dipResultVo.getSomDipGrpRcd().getDipCodg();
                suggestDipName = dipResultVo.getSomDipGrpRcd().getDipName();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        if (!ValidateUtil.isEmpty(suggestDipCode)) {
            suggestGroupForDoctor.setNewDipCodeAndName(suggestDipCode + suggestDipName);
        } else {
            suggestGroupForDoctor.setNewDipCodeAndName("未入组");
        }
        //获取DIP建议分组标杆
        if (!ValidateUtil.isEmpty(suggestDipCode)) {
            suggestGroupForDoctor.setSuggestDipAvgInHosCost("0");
            SomDipStandardExample tpdDipBenchmarkExample = new SomDipStandardExample();
            SomDipStandardExample.Criteria criteria = tpdDipBenchmarkExample.createCriteria();
            criteria.andDipCodeEqualTo(suggestDipCode);
            if (!ValidateUtil.isEmpty(somHiInvyBasInfo.getB15()) && somHiInvyBasInfo.getB15().length() >= 4) {
                criteria.andStandardYearEqualTo(somHiInvyBasInfo.getB15().substring(0, 4));
            } else {
                String nowYear = DateUtil.getCurDateTime().substring(0, 4);
                criteria.andStandardYearEqualTo(nowYear);
            }
            List<SomDipStandard> tpdDipBenchmarkList = tpdDipBenchmarkMapper.selectByExample(tpdDipBenchmarkExample);
            if (!ValidateUtil.isEmpty(tpdDipBenchmarkList)) {
                if (!ValidateUtil.isEmpty(tpdDipBenchmarkList.get(0).getDipStandardInpf())) {
                    suggestGroupForDoctor.setSuggestDipAvgInHosCost(String.valueOf(tpdDipBenchmarkList.get(0).getDipStandardInpf()));
                }
            }
            if (!ValidateUtil.isEmpty(suggestGroupForDoctor.getSuggestDipAvgInHosCost()) &&
                    !ValidateUtil.isEmpty(suggestGroupForDoctor.getDipAvgInHosCost())) {
                //判断建议的分组费用和当前的分组费用对比，如果不符合条件，还原分组
                if (new BigDecimal(suggestGroupForDoctor.getSuggestDipAvgInHosCost()).
                        compareTo(new BigDecimal(suggestGroupForDoctor.getDipAvgInHosCost())) <= 0) {
                    suggestGroupForDoctor.setMainDiagIsChoErr("0");
                    suggestGroupForDoctor.setMainOprnIsChoErr("0");
                    suggestGroupForDoctor.setNewDipCodeAndName("(无需调整)" + (suggestGroupForDoctor.getDipCodeAndName() == null ? "" : suggestGroupForDoctor.getDipCodeAndName()));
                    suggestGroupForDoctor.setSuggestDipAvgInHosCost(suggestGroupForDoctor.getDipAvgInHosCost());
                } else {
                    //判断dip编码是否含有病人手术，有则建议，否则不建议
                    int flag = 0;
                    for (SomOprnOprtInfo bod : busOperateDiagnosisList) {
                        if (!ValidateUtil.isEmpty(bod)) {
                            if (suggestGroupForDoctor.getNewDipCodeAndName().indexOf(bod.getC35c()) != -1) {
                                flag++;
                            }
                        }
                    }
                    if (flag == 0) {
                        suggestGroupForDoctor.setMainDiagIsChoErr("0");
                        suggestGroupForDoctor.setMainOprnIsChoErr("0");
                        suggestGroupForDoctor.setNewDipCodeAndName("(无需调整)" + (suggestGroupForDoctor.getDipCodeAndName() == null ? "" : suggestGroupForDoctor.getDipCodeAndName()));
                        suggestGroupForDoctor.setSuggestDipAvgInHosCost(suggestGroupForDoctor.getDipAvgInHosCost());
                    }
                }
            }
        } else {
            suggestGroupForDoctor.setMainDiagIsChoErr("0");
            suggestGroupForDoctor.setMainOprnIsChoErr("0");
            suggestGroupForDoctor.setNewDipCodeAndName("(无需调整)" + (suggestGroupForDoctor.getDipCodeAndName() == null ? "" : suggestGroupForDoctor.getDipCodeAndName()));
            suggestGroupForDoctor.setSuggestDipAvgInHosCost(suggestGroupForDoctor.getDipAvgInHosCost());
        }
        return suggestGroupForDoctor;
    }

    /**
     * 根据dipCode获取标杆费用
     *
     * @param dipCodg
     * @return
     */
    public String getDipStandardCost(String dipCodg) {
        BigDecimal sum = new BigDecimal("0");
        List<String> codes = Arrays.asList(dipCodg.split("-"));
        if (!ValidateUtil.isEmpty(codes) && !ValidateUtil.isEmpty(codes.get(0))) {
            //查询主诊断亚目前4位标准费用
            String mainIcdCost = commonDao.getMainIcd4StandardCost(codes.get(0));
            sum = sum.add(new BigDecimal(mainIcdCost));
        }
        if (!ValidateUtil.isEmpty(codes) && codes.size() > 1) {
            for (int i = 1; i < codes.size(); i++) {
                sum = sum.add(new BigDecimal(getCodeToStandardCost().get(codes.get(i))));
            }
        }
        return sum.toString();
    }

    /**
     * 处理为icd_code和STANDARD_COST的对应关系Map
     *
     * @param
     * @return Map<String, String>
     */
    private Map<String, String> getCodeToStandardCost() {
        Map<String, String> map = new HashMap<String, String>();
        SomCodeStandFeeExample tpdIcdStandardCostExample = new SomCodeStandFeeExample();
        tpdIcdStandardCostExample.createCriteria().andIcdTypeEqualTo(DrgConst.ICD9);
        List<SomCodeStandFee> tpdIcdStandardCostList = tpdIcdStandardCostMapper.selectByExample(tpdIcdStandardCostExample);
        //处理为icd_code和STANDARD_COST的对应关系Map，key:icd_code，value:STANDARD_FEE
        for (SomCodeStandFee vo : tpdIcdStandardCostList) {
            map.put(vo.getIcdCodg(), vo.getStandardFee());
        }
        return map;
    }


    public SomHiInvyBasInfo getSettleListBaseInfoById(Long id) {
        return busSettleListMapper.selectByPrimaryKey(id);
    }

    public List<SomOtpSlowSpecialTrtInfo> getSettleListMzmtInfoById(Long id) {
        SomOtpSlowSpecialTrtInfoExample example = new SomOtpSlowSpecialTrtInfoExample();
        SomOtpSlowSpecialTrtInfoExample.Criteria criteria = example.createCriteria();
        if (!StringUtils.isEmpty(id)) {
            criteria.andSettltListIdEqualTo(id);
        }
        List<SomOtpSlowSpecialTrtInfo> busOutpatientClinicDiagnoses = busOutpatientClinicDiagnosisMapper.selectByExample(example);
        if (ValidateUtil.isNotEmpty(busOutpatientClinicDiagnoses)) {
            for (int i = 0; i < busOutpatientClinicDiagnoses.size(); i++) {
                busOutpatientClinicDiagnoses.get(i).setSeq(i);
            }
        }
        return busOutpatientClinicDiagnoses;
    }
    //获取出院表
    public List<BusDiseaseDiagnosisTrim> getSettleListDiseaseInfoById(Long id) {
        List<BusDiseaseDiagnosisTrim> busDiseaseDiagnosisTrimList = new ArrayList<>();
        List<SomDiag> busDiseaseDiagnosisList = new ArrayList<>();   //存出院诊断表
        int westernMediNum = 0, chineseMediNum = 0;
        SomDiagExample example = new SomDiagExample();
        SomDiagExample.Criteria criteria = example.createCriteria();
        if (!StringUtils.isEmpty(id)) {
            criteria.andSettleListIdEqualTo(id);
        }
        busDiseaseDiagnosisList = busDiseaseDiagnosisMapper.selectByExample(example);
        for (SomDiag bddl : busDiseaseDiagnosisList) {
            BusDiseaseDiagnosisTrim busDiseaseDiagnosisTrim = new BusDiseaseDiagnosisTrim();
            if (DrgConst.DIAGNOSTIC_TYPE_1.equals(bddl.getType())) {
                busDiseaseDiagnosisTrim.setId(bddl.getId().toString());
                busDiseaseDiagnosisTrim.setSeq(westernMediNum);
                busDiseaseDiagnosisTrim.setC06c1(bddl.getDscg_diag_codg());
                busDiseaseDiagnosisTrim.setC07n1(bddl.getDscg_diag_name());
                busDiseaseDiagnosisTrim.setC08c1(bddl.getDscg_diag_adm_cond());
                busDiseaseDiagnosisTrim.setC50c1(bddl.getDscg_diag_dscg_cond());
                busDiseaseDiagnosisTrim.setType(bddl.getType());
                //判断分组类型
                String fzlx = (String) SysCommonConfigUtil.get(DrgConst.FZLX, true);

                if(bddl.getCcName() != null){
                    busDiseaseDiagnosisTrim.setCcName(bddl.getCcName());
                }else{
                    //如果没有Mcc/cc就显示否
                    busDiseaseDiagnosisTrim.setCcName("否");
                }
                if(bddl.getDiaNoCode() != null){
                    busDiseaseDiagnosisTrim.setDiaNoCode("否");
                }else{
                    busDiseaseDiagnosisTrim.setDiaNoCode("是");
                }

                busDiseaseDiagnosisTrimList.add(busDiseaseDiagnosisTrim);

                westernMediNum++;
            } else if (Arrays.asList(DrgConst.DIAGNOSTIC_TYPE_2, DrgConst.DIAGNOSTIC_TYPE_3).contains(bddl.getType())) {
                busDiseaseDiagnosisTrim.setId(bddl.getId().toString());
                busDiseaseDiagnosisTrim.setSeq(chineseMediNum);
                busDiseaseDiagnosisTrim.setC06c2(bddl.getDscg_diag_codg());
                busDiseaseDiagnosisTrim.setC07n2(bddl.getDscg_diag_name());
                busDiseaseDiagnosisTrim.setC08c2(bddl.getDscg_diag_adm_cond());
                busDiseaseDiagnosisTrim.setC50c2(bddl.getDscg_diag_dscg_cond());
                busDiseaseDiagnosisTrim.setType(bddl.getType());
                busDiseaseDiagnosisTrimList.add(busDiseaseDiagnosisTrim);
                chineseMediNum++;
            }
        }

//        //西医诊断生成
//        for (int i = 0; i < westernMediNum; i++) {
//            BusDiseaseDiagnosisTrim busDiseaseDiagnosisTrim = new BusDiseaseDiagnosisTrim();
//            for (SomDiag bddl : busDiseaseDiagnosisList) {
//                if (DrgConst.DIAGNOSTIC_TYPE_1.equals(bddl.getType()) && bddl.getSeq() == i) {
//                    busDiseaseDiagnosisTrim.setId(bddl.getId().toString());
//                    busDiseaseDiagnosisTrim.setSeq(i);
//                    busDiseaseDiagnosisTrim.setC06c1(bddl.getDscg_diag_codg());
//                    busDiseaseDiagnosisTrim.setC07n1(bddl.getDscg_diag_name());
//                    busDiseaseDiagnosisTrim.setC08c1(bddl.getDscg_diag_adm_cond());
//                    busDiseaseDiagnosisTrim.setC50c1(bddl.getDscg_diag_dscg_cond());
//                    busDiseaseDiagnosisTrim.setType(bddl.getType());
//                    busDiseaseDiagnosisTrimList.add(busDiseaseDiagnosisTrim);
//                    break;
//                }
//            }
//        }
//
//        //中医诊断生成
//        for (int i = 0; i < chineseMediNum; i++) {
//            BusDiseaseDiagnosisTrim busDiseaseDiagnosisTrim = new BusDiseaseDiagnosisTrim();
//            for (SomDiag bddl : busDiseaseDiagnosisList) {
//                if (Arrays.asList(DrgConst.DIAGNOSTIC_TYPE_2, DrgConst.DIAGNOSTIC_TYPE_3).contains(bddl.getType())) {
//                    busDiseaseDiagnosisTrim.setId(bddl.getId().toString());
//                    busDiseaseDiagnosisTrim.setSeq(i);
//                    busDiseaseDiagnosisTrim.setC06c2(bddl.getDscg_diag_codg());
//                    busDiseaseDiagnosisTrim.setC07n2(bddl.getDscg_diag_name());
//                    busDiseaseDiagnosisTrim.setC08c2(bddl.getDscg_diag_adm_cond());
//                    busDiseaseDiagnosisTrim.setC50c2(bddl.getDscg_diag_dscg_cond());
//                    busDiseaseDiagnosisTrim.setType(bddl.getType());
//                    busDiseaseDiagnosisTrimList.add(busDiseaseDiagnosisTrim);
//                    break;
//                }
//            }
//        }

        if (ValidateUtil.isNotEmpty(busDiseaseDiagnosisTrimList)) {
            // 升序
            busDiseaseDiagnosisTrimList = busDiseaseDiagnosisTrimList.stream().sorted(Comparator.comparing(BusDiseaseDiagnosisTrim::getSeq)).collect(Collectors.toList());
        }
        return busDiseaseDiagnosisTrimList;
    }
    //手术数据
    public List<SomOprnOprtInfo> getSettleListOperationInfoById(Long id) {
        SomOprnOprtInfoExample example = new SomOprnOprtInfoExample();
        SomOprnOprtInfoExample.Criteria criteria = example.createCriteria();
        if (!StringUtils.isEmpty(id)) {
            criteria.andSettleListIdEqualTo(id);
        }
        example.setOrderByClause("seq asc");
        List<SomOprnOprtInfo> busOperateDiagnoses = busOperateDiagnosisMapper.selectByExample(example);//手术数据
        if (ValidateUtil.isNotEmpty(busOperateDiagnoses)) {
            busOperateDiagnoses = busOperateDiagnoses.stream().sorted(Comparator.comparing(SomOprnOprtInfo::getSeq)).collect(Collectors.toList());
        }
        return busOperateDiagnoses;
    }

    public List<SomSetlInvyScsCutdInfo> getBusIcuInfoById(Long id) {
        SomSetlInvyScsCutdInfoExample example = new SomSetlInvyScsCutdInfoExample();
        SomSetlInvyScsCutdInfoExample.Criteria criteria = example.createCriteria();
        if (!StringUtils.isEmpty(id)) {
            criteria.andSettltListIdEqualTo(id);
        }
        example.setOrderByClause("scs_cutd_inpool_time asc");
        List<SomSetlInvyScsCutdInfo> busIcus = busIcuMapper.selectByExample(example);
        if (ValidateUtil.isNotEmpty(busIcus)) {
            for (int i = 0; i < busIcus.size(); i++) {
                busIcus.get(i).setSeq(i);
            }
        }
        return busIcus;
    }

    public List<SomHiSetlInvyMedFeeInfo> getBusMedicalCostInfoById(Long id) {
        SomHiSetlInvyMedFeeInfoExample example = new SomHiSetlInvyMedFeeInfoExample();
        SomHiSetlInvyMedFeeInfoExample.Criteria criteria = example.createCriteria();
        if (!StringUtils.isEmpty(id)) {
            criteria.andSettltListIdEqualTo(id);
        }
        return busMedicalCostMapper.selectByExample(example);
    }

    public List<SomFundPay> getBusFundPayInfoById(Long id) {
        SomFundPayExample example = new SomFundPayExample();
        SomFundPayExample.Criteria criteria = example.createCriteria();
        if (!StringUtils.isEmpty(id)) {
            criteria.andSettltListIdEqualTo(id);
        }
        return busFundPayMapper.selectByExample(example);
    }

//    public void deleteDataById(SettleListMainInfoQueryParam dto) {
//        int i = settleListManageDao.insertBackDataById(dto);
//        if(i > 0) {
//            settleListManageDao.deleteBusKeyDipById(dto);
//        }
//    }


    public void recoveryDataById(SettleListMainInfoQueryParam dto) {
        int i = settleListManageDao.insertBusKeyById(dto);
        if (i > 0) {
            settleListManageDao.deleteBackDipById(dto);
        }
    }

    @Override
    public AllErrorVo getErrorInfo(Long id) {
        AllErrorVo vo = new AllErrorVo();
        List<AllErrorVo> completeError = commonDao.getcompleteError(id);
        //完整性
        if (ValidateUtil.isNotEmpty(completeError)) {
            for (AllErrorVo allErrorVo : completeError) {
                if (ValidateUtil.isNotEmpty(allErrorVo.getCompleteError())) {
                    if (ValidateUtil.isEmpty(vo.getAllCompleteError())) {
                        vo.setAllCompleteError(allErrorVo.getCompleteError());
                    } else {
                        vo.setAllCompleteError(vo.getAllCompleteError() + allErrorVo.getCompleteError());
                    }
                }
            }
        }
        if (ValidateUtil.isNotEmpty(vo.getAllCompleteError())) {
            String[] CompleteSplit = vo.getAllCompleteError().split(";");
            vo.setCompleteErrorNum(CompleteSplit.length);
        } else {
            vo.setCompleteErrorNum(0);
        }

        List<AllErrorVo> logicError = commonDao.getlogicError(id);
        //逻辑性
        if (ValidateUtil.isNotEmpty(logicError)) {
            for (AllErrorVo logic : logicError) {
                if (ValidateUtil.isNotEmpty(logic.getLogicError())) {
                    if (ValidateUtil.isEmpty(vo.getAllLogicError())) {
                        vo.setAllLogicError(logic.getLogicError());
                    } else {
                        vo.setAllLogicError(vo.getAllLogicError() + logic.getLogicError());
                    }
                }
            }
        }
        if (ValidateUtil.isNotEmpty(vo.getAllLogicError())) {
            String[] LogicSplit = vo.getAllLogicError().split(";");
            vo.setLogicErrorNum(LogicSplit.length);
        } else {
            vo.setLogicErrorNum(0);
        }
        return vo;
    }

    @Override
    public  Boolean selectBusSettleErrorLIst(Long id, String k00){
        SettleListMainInfoQueryParam dto = new SettleListMainInfoQueryParam();
        dto.setId(String.valueOf(settleListManageDao.selectBusSettleListId(k00)));
        String chkStas = settleListManageDao.selectBusSettleErrorLIst(dto);
        if("1".equals(chkStas)){
            return true;
        }
        return false;
    }

    @Override
    public Map<String, List<BusSettleListResult>> selectBusSettleLIstError(Long id, String k00) {
        SettleListMainInfoQueryParam dto = new SettleListMainInfoQueryParam();
        dto.setId(String.valueOf(settleListManageDao.selectBusSettleListId(k00)));
        List<BusSettleListResult> busSettleListResult = settleListManageDao.selectBusSettleLIstError(dto);
//        BusSettleListResult ResultList = new BusSettleListResult();
        Map<String, List<BusSettleListResult>> resMap = new HashMap<>();
        if (ValidateUtil.isNotEmpty(busSettleListResult)) {
            for (BusSettleListResult settleListResult : busSettleListResult) {
                String[] s = new String[0];
                String[] s1 = new String[0];
                s = settleListResult.getErrDscr().split(";");
                s1 = settleListResult.getErrorFields().split(";");
                List<BusSettleListResult> list = new ArrayList<>();
//            BusSettleListResult resultList1 = new BusSettleListResult();
                for (int i = 0; i < s.length; i++) {
                    for (int m = 0; m < s1.length; m++) {
                        if (Integer.valueOf(settleListResult.getErrType()) < 5) {
                            if (i == m) {
                                BusSettleListResult resultList = new BusSettleListResult();
                                resultList.setError(s[i]);
                                resultList.setFld(s1[m]);
                                list.add(resultList);
                            }
                        } else {
                            if (i == m) {
                                BusSettleListResult resultList = new BusSettleListResult();
                                resultList.setError(s[i]);
                                resultList.setFld(s1[m]);
                                list.add(resultList);
                            }
                        }
                    }
                }
                resMap.put(settleListResult.getErrType(), list);
            }
        }
        return resMap;
    }

    /**
     * 转化成list
     *
     * @param busSettleListResult
     * @param type
     * @return
     */
    public BusSettleListResult setErrorValue(BusSettleListResult busSettleListResult, String type) {
        String[] s = new String[0];
        String[] s1 = new String[0];
        if (type.equals("1")) {
            s = busSettleListResult.getNullErrorDesc().split(";");
            s1 = busSettleListResult.getNullErrorFields().split(";");
        } else if (type.equals("2")) {
            s = busSettleListResult.getDictErrorDesc().split(";");
            s1 = busSettleListResult.getDictErrorFields().split(";");
        } else if (type.equals("3")) {
            s = busSettleListResult.getRegErrorDesc().split(";");
            s1 = busSettleListResult.getRegErrorFields().split(";");
        } else if (type.equals("4")) {
            s = busSettleListResult.getLengthErrorDesc().split(";");
            s1 = busSettleListResult.getLengthErrorFields().split(";");
        }
        BusSettleListResult ResultList = new BusSettleListResult();
        List<BusSettleListResult> list = new ArrayList<>();
        for (int i = 0; i < s.length; i++) {
            for (int m = 0; m < s1.length; m++) {
                if (i == m) {
                    BusSettleListResult settleListResult = new BusSettleListResult();
                    settleListResult.setError(s[i]);
                    settleListResult.setFld(s1[m]);
                    list.add(settleListResult);
                    if (type.equals("1")) {
                        ResultList.setNullErrorList(list);
                    } else if (type.equals("2")) {
                        ResultList.setDictErrorList(list);
                    } else if (type.equals("3")) {
                        ResultList.setRegErrorList(list);
                    } else if (type.equals("4")) {
                        ResultList.setLengthErrorList(list);
                    }
                }
            }
        }
        return ResultList;
    }


    /**
     * 重新校验医保结算清单
     *
     * @param dto
     * @return
     */
    @Override
    public Map<String, List<BusSettleListResult>> restartCheck(SettleListValidateVo dto) {
        Map<String, List<BusSettleListResult>> stringListMap = selectBusSettleLIstError(dto.getSomHiInvyBasInfo().getId(), dto.getSomHiInvyBasInfo().getK00());
        return stringListMap;
    }

    @Override
    public ModifyBusSettleListDto updateSettleList(ModifyBusSettleListDto modifyBusSettleListDto) {
        ModifyBusSettleListDto dto = setTime(modifyBusSettleListDto);
        dto.setState(DrgConst.TYPE_1);
        dto.setId(settleListManageDao.selectBusSettleListId(dto.getK00()));
        updateAll(dto, DrgConst.TYPE_1);
        Long id = dto.getId();
        //历史诊断数据
        settleListManageDao.insertDiseaseHistory(dto);
        //历史手术数据
        settleListManageDao.insertOperateHistory(dto);
//        //删除诊断表信息
//        settleListManageDao.deleteDisease(dto);
//        //删除手术表信息
//        settleListManageDao.deleteOperate(dto);
//        //将最原始的历史数据存放到历史数据表（bus_settle_list）
//        settleListManageDao.insertBusSettleListHistory();
        //复制一份settleList表的信息获取新的settleListID
        settleListManageDao.copyId(dto);
        //更新ICU表的settleListId
        updateAllBySettleListId("som_setl_invy_scs_cutd_info", id, dto.getId());
        //更新门慢门特表settleListID
        updateAllBySettleListId("som_otp_slow_special_trt_info", id, dto.getId());
        //更新结算清单医疗费用数据表settleListID
        updateAllBySettleListId("som_hi_setl_invy_med_fee_info", id, dto.getId());
        //更新查看历史修改状态
        settleListManageDao.updateSettleListByHisState(dto);
//        settleListManageDao.deleteBusSettleListById(id);
        for (BusDiseaseDiagnosisTrim bddt : dto.getBusDiseaseDiagnosisTrimsList()) {
            bddt.setSeq(dto.getBusDiseaseDiagnosisTrimsList().indexOf(bddt));
        }
        settleListManageDao.insertDisease(dto);
        for (SomOprnOprtInfo s2 : dto.getBusOperateDiagnosisList()) {
            s2.setSeq(dto.getBusOperateDiagnosisList().indexOf(s2));
        }
        settleListManageDao.insertOperate(dto);
        // 处理病案首页
        dipDataService.insertMedicalRecord(Arrays.asList(dto.getId()));
        //更新dataLogId为null重新跑数据
        updateAll(dto, DrgConst.TYPE_2);
        return dto;
    }


    /**
     * 插入修改数据表
     *
     * @param dto
     */
    @Override
    public void insertHistory(ModifyBusSettleListDto dto) {
        ModifyBusSettleListDto setTimeDto = setTime(dto);
        //插入修改数据表
        settleListManageDao.insertHistory(setTimeDto);
    }

    /**
     * 设置相同的时间
     *
     * @param dto
     * @return
     */
    private ModifyBusSettleListDto setTime(ModifyBusSettleListDto dto) {
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        dto.setModi_time(df.format(new Date()));
        return dto;
    }

    /**
     * 更新settleList查看历史数据状态
     *
     * @return
     */
    @Override
    public void updateSettleListHisState(String k00) {
        settleListManageDao.updateSettleListByHistoryState(k00);
    }

    /**
     * 更新settleLists是否点击完成
     *
     * @return
     */
    @Transactional
    @Override
    public void updateSettleListLookOver(ModifyBusSettleListDto dto) {
        if (ValidateUtil.isNotEmpty(dto.getK00())) {
            //修改清单结算标识
            settleListManageDao.updateSettleListLookOver(dto);
            //添加清单结算标识日志
            settleListManageDao.insertSettleListOpeLog(dto);
            if (DrgConst.ACTIVE_FLAG_0.equals(dto.getLookOver())) {
                settleListMarkMapper.delete(new SettleListMarkDto(dto.getK00()));
            } else {
                SettleListMarkDto settleListMarkDto = new SettleListMarkDto();
                settleListMarkDto.setK00(dto.getK00());
                settleListMarkDto.setSettleListId(dto.getId());
                settleListMarkDto.setHospitalId(dto.getHospitalId());
                settleListMarkMapper.insert(settleListMarkDto);
            }
        }
        //消息推送
//        pushMessage(dto);
        // 判断是否还有错误校验信息
        int errorNum = settleListMarkMapper.queryErrorNum(dto.getId());
        if (errorNum > 0) {
            throw new RuntimeException("清单校验有错，请联系管理人员！！！");
        }
    }

    private void pushMessage(ModifyBusSettleListDto dto) {
        String role = "settle_list_manager";
        String tagIds = "4;";
        //科室
        //查询该科室的总病例
        if (dto.getB15() == null) {
            throw new AppException("没有传入出院时间");
        }
        String time = dto.getB15().substring(0, 7);
        dto.setB15(time);
        //查询科室清单数量
        int deptCount = settleListManageDao.queryAllCountByB16c(dto);
        //查询科室已经查看病例数
        SettleListLookOverVo vo = settleListManageDao.queryCaseByB16c(dto);
        //所有科室标识已查看，推送消息
        if (vo == null) {
            return;
        }
        if (deptCount == vo.getCount()) {
            //查询全院病例数量
            int hospitalAllCount = settleListManageDao.queryAllHospitalCount(dto);
            //查询全院已查看数量
            int hospitalLookOverCount = settleListManageDao.queryLookOverHospitalCount(dto);
//            AppMessageVo messageVo = new AppMessageVo();
            int i = Math.round(hospitalLookOverCount * 100 / hospitalAllCount);
            String message = "[" + vo.getB16n() + "] 清单查看已完成！\n" + "全院进度：" + hospitalLookOverCount + "/" + hospitalAllCount + " ！";
            AppMessageDto messageDto = new AppMessageDto();
            messageDto.setPushUsername(DrgConst.DEV_NAME);
            messageDto.setPushText(message);
            messageDto.setTagIds(tagIds);
            messageDto.setType(DrgConst.TYPE_2);
            messageDto.setRole(role);
            messageDto.setParamsData(JSON.toJSONString(i));
            appMessageService.addMessage(messageDto);
            //推送下拉栏消息
            listUploadService.pushDropdownMessage(role, message);
        }
    }

    /**
     * 根据时间节点还原数据
     *
     * @param dto
     * @return
     */
    @Override
    public void restoreHistoryBusSettle(ModifyBusSettleListDto dto) {
        dto.setId(settleListManageDao.selectBusSettleListId(dto.getK00()));
        dto.setState(DrgConst.TYPE_2);
        List<ModifyBusSettleListDto> collect2 = dto.getExchange().stream().filter(item -> item.getFld().contains("诊断")).collect(Collectors.toList());
        List<ModifyBusSettleListDto> collect3 = dto.getExchange().stream().filter(item -> item.getFld().contains("手术")).collect(Collectors.toList());
        updateAll(dto, DrgConst.TYPE_1);
        Long id = dto.getId();
//        if(ValidateUtil.isNotEmpty(collect2)){
//            //删除诊断表信息
//            settleListManageDao.deleteDisease(dto);
//        }
//        if(ValidateUtil.isNotEmpty(collect3)){
//            // 删除手术表信息
//            settleListManageDao.deleteOperate(dto);
//        }
        //复制一份settleList表的信息获取新的settleListID
        settleListManageDao.copyId(dto);
        if (ValidateUtil.isNotEmpty(collect2)) {
            //还原诊断表
            settleListManageDao.insertHistoryDisease(dto);
        } else {
            updateODBySettleListId("som_diag", id, dto.getId());
        }
        if (ValidateUtil.isNotEmpty(collect3)) {
            //还原手术表
            settleListManageDao.insertHistoryOperate(dto);
        } else {
            updateODBySettleListId("som_oprn_oprt_info", id, dto.getId());
        }
        //更新ICU表的settleListId
        updateAllBySettleListId("som_setl_invy_scs_cutd_info", id, dto.getId());
        //更新门慢门特表settleListID
        updateAllBySettleListId("som_otp_slow_special_trt_info", id, dto.getId());
        //更新结算清单医疗费用数据表settleListID
        updateAllBySettleListId("som_hi_setl_invy_med_fee_info", id, dto.getId());

//        settleListManageDao.deleteBusSettleListById(id);
        // 处理病案首页
        dipDataService.insertMedicalRecord(Arrays.asList(dto.getId()));
        //更新dataLogId为null重新跑数据
        updateAll(dto, DrgConst.TYPE_2);
        for (ModifyBusSettleListDto changeDto : dto.getExchange()) {
            dto.setModi_time(changeDto.getModi_time());
        }
        settleListManageDao.deleteHistoryByK00(dto);

    }

    @Override
    public AllErrorVo selectProcessResult(String id) {
        AllErrorVo vo = settleListManageDao.selectProcessResult(id);
        return vo;
    }

//    /**
//     * 判断还原的是诊断还是手术
//     * @param list
//     * @param s
//     * @param type
//     * @return
//     */
//    private boolean restoreHistoryType(List<ModifyBusSettleListDto> list, String s,String type) {
//        boolean res=false;
//        if(type.equals("1")){
//            List<ModifyBusSettleListDto> collect = list.stream().filter(item -> !item.getFld().contains(s)).collect(Collectors.toList());
//            if(ValidateUtil.isNotEmpty(collect)){
//                res=true;
//            }else {
//                res=false;
//            }
//        }
//        if(type.equals("2")){
//            List<ModifyBusSettleListDto> collect = list.stream().filter(item -> item.getFld().contains(s)).collect(Collectors.toList());
//            if(ValidateUtil.isNotEmpty(collect)){
//                res=true;
//            }else {
//                res=false;
//            }
//        }
//        return res;
//    }

    /**
     * 更新bus_settle_list
     *
     * @param dto
     * @param type
     */
    public void updateAll(ModifyBusSettleListDto dto, String type) {
        dto.setType(type);
        List<ModifyBusSettleListDto> list = new ArrayList<>();
        if (!ValidateUtil.isEmpty(dto.getExchange())) {
            if (dto.getState().equals("1")) {
                list = dto.getExchange().stream().filter(item -> !item.getZd().contains("第")).collect(Collectors.toList());
            } else {
                list = dto.getExchange().stream().filter(item -> !item.getFld().contains("第")).collect(Collectors.toList());
            }
        }
        dto.setExchange(list);
        if (ValidateUtil.isNotEmpty(dto.getExchange()) || dto.getType().equals("2")) {
            settleListManageDao.updateSettleList(dto);
        }
    }

    /**
     * 更新无法修改表的settleListID
     *
     * @param s
     * @param id
     * @param newId
     */
    private void updateAllBySettleListId(String s, Long id, Long newId) {
        settleListManageDao.updateAllBySettleListId(s, id, newId);
    }


    /**
     * 更新手术和诊断表的settleListID
     *
     * @param s
     * @param id
     * @param newId
     */
    private void updateODBySettleListId(String s, Long id, Long newId) {
        settleListManageDao.updateODBySettleListId(s, id, newId);
    }


    public void deleteDataById(SettleListMainInfoQueryParam dto) {
        dto.setTableNames(TABLE_NAMES);
        int i = settleListManageDao.deletePatientAllTableByIdAndHospitalId(dto);
        if (i >= 0) {
            dto.setTableNames(Arrays.asList("som_hi_invy_bas_info"));
            settleListManageDao.deletePatientAllTableByIdAndHospitalId(dto);
        }
    }

    @Override
    public Map<String, Object> queryICDCode(CommonQueryDto dto) {
        Map<String, Object> res = new HashMap<>();
        if (ValidateUtil.isEmpty(dto.getVer())) {
            dto.setVer(Optional.ofNullable(SysCommonConfigUtil.get(DrgConst.SCC_CONTRAST_CODE_VERSION).toString()).orElse("10"));
        }
        List<ICDCodeVo> codeList = new ArrayList<>();
        //主证
        List<ICDCodeVo> tcmPrincipal =commonMapper.queryTcmPrincipal();
        if (ValidateUtil.isNotEmpty(tcmPrincipal)) {
            codeList.addAll(tcmPrincipal);
        }
        //主病
        List<ICDCodeVo> tcmMain =commonMapper.queryTcmMain();
        if (ValidateUtil.isNotEmpty(tcmMain)) {
            codeList.addAll(tcmMain);
        }
        // ICD编码
        List<ICDCodeVo> icdCodeVoList = commonMapper.queryIcdCodeByType(dto.getVer());
        if (ValidateUtil.isNotEmpty(icdCodeVoList)) {
            Map<String, List<ICDCodeVo>> icdMap = icdCodeVoList.stream().collect(Collectors.groupingBy(ICDCodeVo::getIcdType));
            codeList.addAll(icdMap.get(DrgConst.ICD10));
            res.put(SettleListValidateUtil.KEY_ICD9, icdMap.get(DrgConst.ICD9));
            res.put(SettleListValidateUtil.KEY_ICD10, codeList);
        }
        return res;
    }

    /**
     * 结算清单修改新增重跑流程
     *
     * @param dto1
     * @return
     */
    @Override
//    @Transactional
    public UpdateBusSettleListResultVo modifySettleListInfo(ModifyDto dto1) {

        //去重
        List<String> queue = RedisUtils.getAllValueByNameKey(RUNNING_K00S_REDIS_KEY_NAME);
        //添加修改记录
        String tempK00 = getK00(dto1);
        if( !ValidateUtil.isEmpty(queue) && queue.contains(tempK00)){
            throw  new AppException("数据正在执行流程，请等待");
        }
        int count = settleListManageDao.queryCountK00Count(tempK00);
        if(count>1){
            throw  new AppException("k00为"+tempK00+"的数据正在等待执行流程，请稍后");
        }

        //去重
        List<settleListUpdateVo> list = settleListManageDao.queryUpdateLogByK00(tempK00);
        //设置参数
        SomInvyUpldModiRcdVo updateLogVo = getSomInvyUpldModiRcdVo(dto1, tempK00);
        if (ValidateUtil.isEmpty(list)) {
            //添加记录
            settleListManageDao.addUpdateLog(updateLogVo);
        } else {
            //修改记录
            updateLogVo.setId(list.get(0).getId());
            settleListManageDao.UpdateLog(updateLogVo);
        }
        DefaultTransactionDefinition transactionDefinition = new DefaultTransactionDefinition();
        TransactionStatus transactionStatus = transactionManager.getTransaction(transactionDefinition);
        UpdateBusSettleListResultVo resultVo = new UpdateBusSettleListResultVo();
        try {
            ModifySettleListDto modifySettleListDto = new ModifySettleListDto();
            Long oldId = null;
            Long newId = null;
            String k00 = null;
            // 调整了诊断、手术对的标志
            boolean adjustFlag = false;
            //基本信息
            if (!ValidateUtil.isEmpty(dto1.getBaseInfoDto())) {
                ModifySettleListDto dto = dto1.getBaseInfoDto();
                k00 = dto.getK00();
                dataProcessing(dto, oldId, newId);
            }
            //门慢门特
            if (!ValidateUtil.isEmpty(dto1.getOcdInfoDto())) {
                k00 = dto1.getOcdInfoDto().getK00();
                dataProcessing(dto1.getOcdInfoDto(), oldId, newId);
            }
            //复制除新的settleListId
            if (!ValidateUtil.isEmpty(dto1.getBaseInfoDto()) ||
                    !ValidateUtil.isEmpty(dto1.getDisInfoDto()) ||
                    !ValidateUtil.isEmpty(dto1.getOpeInfoDto()) ||
                    !ValidateUtil.isEmpty(dto1.getIcuInfoDto())) {
                if (!ValidateUtil.isEmpty(dto1.getBaseInfoDto())) {
                    oldId = Long.valueOf(dto1.getBaseInfoDto().getId()).longValue();
                } else if (!ValidateUtil.isEmpty(dto1.getDisInfoDto())) {
                    oldId = Long.valueOf(dto1.getDisInfoDto().getId()).longValue();
                } else if (!ValidateUtil.isEmpty(dto1.getOpeInfoDto())) {
                    oldId = Long.valueOf(dto1.getOpeInfoDto().getId()).longValue();
                } else {
                    oldId = Long.valueOf(dto1.getIcuInfoDto().getId()).longValue();
                }
                modifySettleListDto.setId(oldId.toString());
                settleListManageDao.copyBusSettleList(modifySettleListDto);
                newId = Long.valueOf(modifySettleListDto.getId()).longValue();
            }
            //诊断
            if (!ValidateUtil.isEmpty(dto1.getDisInfoDto())) {
                // 原始数据
                insertOriginalData(tempK00, oldId, dto1.getGrperType());
                adjustFlag = true;
                k00 = dto1.getDisInfoDto().getK00();
                dataProcessing(dto1.getDisInfoDto(), oldId, newId);
            }
            //手术
            if (!ValidateUtil.isEmpty(dto1.getOpeInfoDto())) {
                // 原始数据
                insertOriginalData(tempK00, oldId, dto1.getGrperType());
                adjustFlag = true;
                k00 = dto1.getOpeInfoDto().getK00();
                dataProcessing(dto1.getOpeInfoDto(), oldId, newId);
            }
            // icu
            if (!ValidateUtil.isEmpty(dto1.getIcuInfoDto())) {
                k00 = dto1.getIcuInfoDto().getK00();
                dataProcessing(dto1.getIcuInfoDto(), oldId, newId);
            }
            // 输血
            if (!ValidateUtil.isEmpty(dto1.getBldInfoDto())) {
                k00 = dto1.getBldInfoDto().getK00();
                dataProcessing(dto1.getBldInfoDto(), oldId, newId);
            }
            //跑流程
            if (!ValidateUtil.isEmpty(dto1.getBaseInfoDto()) ||
                    !ValidateUtil.isEmpty(dto1.getDisInfoDto()) ||
                    !ValidateUtil.isEmpty(dto1.getOpeInfoDto()) ||
                    !ValidateUtil.isEmpty(dto1.getIcuInfoDto())) {
                //1、将其他关联表更新为最新的settleListId
                //更新诊断表
                updateAllBySettleListId("som_diag", oldId, newId);
                //更新手术表
                updateAllBySettleListId("som_oprn_oprt_info", oldId, newId);
                //更新ICU表的settleListId
                updateAllBySettleListId("som_setl_invy_scs_cutd_info", oldId, newId);
                //更新门慢门特表settleListID
                updateAllBySettleListId("som_otp_slow_special_trt_info", oldId, newId);
                //更新结算清单医疗费用数据表settleListID
                updateAllBySettleListId("som_hi_setl_invy_med_fee_info", oldId, newId);
                //更新输血表
                updateAllBySettleListId("som_setl_invy_bld_info", oldId, newId);
                //更新支付信息表
                updateAllBySettleListId("som_fund_pay", oldId, newId);
                //2、将BUS_SETTLE_LIST的DATA_log_id更新为null
                settleListManageDao.updateBusDataLogId(newId);

                //modify modify by qf 241206 修改为统一10秒跑批处理
                //dynamicScheduleTask.startScheduleTask();
                transactionManager.commit(transactionStatus);

                resultVo.setId(newId);
                resultVo.setK00(k00);

                //3、查询流程情况 modify by qf 241206 修改为统一10秒跑批处理
//                Integer run = run(newId.toString());
//                if (run > 0) {
//                    resultVo.setId(newId);
//                    resultVo.setK00(k00);
//                }

            }
            // 调整了手术和诊断，添加操作记录
            if (adjustFlag) {
                addSupervisionInfo(tempK00, newId, dto1.getUsername(), dto1.getGrperType());
            }
        } catch (Exception e) {
            e.printStackTrace();
            transactionManager.rollback(transactionStatus);
            throw new AppException("清单修改失败");
        }

        try {
            dynamicScheduleTask.startScheduleTask(resultVo.getK00());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        // processExecution(dto1, resultVo);

        return resultVo;
    }

    /**
     * 添加监管信息
     *
     * @param k00
     */
    void addSupervisionInfo(String k00, Long newId, String username, String grperType) {
        // 删除之前的监管信息
        medicalRecordSupervisionMapper.deleteOldSupervisionInfo(k00);
        // 查询调整数据
        MedicalRecordSupervisionVo supervisionVo = medicalRecordSupervisionMapper.queryAdjustData(k00, grperType);
        // 查询原始诊断数据
        List<OriginalDiseaseVo> originalDiseaseVos = medicalRecordSupervisionMapper.queryOriginalDiseaseData(k00);
        // 查询最新诊断信息
        List<NowDiseaseVo> nowDiseaseVos = medicalRecordSupervisionMapper.queryNowDiseaseData(newId);
        // 查询原始手术数据
        List<OriginalOperateVo> originalOperateVos = medicalRecordSupervisionMapper.queryOriginalOperateData(k00);
        // 查询最新手术信息
        List<NowOperateVo> nowOperateVos = medicalRecordSupervisionMapper.queryNowOperateData(newId);
        // 判断诊断、手术条数变化
        String adjustDiseaseInfo = "";
        String adjustOperateInfo = "";
        if (originalDiseaseVos.size() != nowDiseaseVos.size()) {
            adjustDiseaseInfo = "调整了诊断数量;";
        }
        if (originalOperateVos.size() != nowOperateVos.size()) {
            adjustOperateInfo = "调整了手术数量;";
        } else {
            if (originalOperateVos.size() > 0) {
                for (OriginalOperateVo originalOperateVo :
                        originalOperateVos) {
                    boolean flag = true;
                    for (NowOperateVo nowOperateVo :
                            nowOperateVos) {
                        if (originalOperateVo.getC35c().equals(nowOperateVo.getC35c())
                                && originalOperateVo.getSeq() == nowOperateVo.getSeq()) {
                            flag = false;
                        }
                    }
                    if (flag) {
                        adjustOperateInfo = "调整了手术顺序";
                        break;
                    }
                }
            }
        }

        // 判断主诊变化
        String adjustMainDiseaseInfo = "";
        for (OriginalDiseaseVo originalDiseaseVo :
                originalDiseaseVos) {
            if (originalDiseaseVo.getSeq() == 0) {
                for (NowDiseaseVo nowDiseaseVo :
                        nowDiseaseVos) {
                    if (nowDiseaseVo.getSeq() == 0) {
                        if (!originalDiseaseVo.getDscg_diag_codg().equals(nowDiseaseVo.getDscg_diag_codg())) {
                            adjustMainDiseaseInfo = "主要诊断由[" + originalDiseaseVo.getDscg_diag_codg() + "]调整为[" + nowDiseaseVo.getDscg_diag_codg() + "];";
                        }
                    }
                }
            }
        }

        if (adjustMainDiseaseInfo != "" || adjustDiseaseInfo != "" || adjustOperateInfo != "") {
            supervisionVo.setOprtRcd(adjustMainDiseaseInfo + adjustDiseaseInfo + adjustOperateInfo);
            supervisionVo.setModiPsn(username);
            if (ValidateUtil.isEmpty(supervisionVo.getInitDipCodg()) && grperType.equals("1")) {
                supervisionVo.setInitDipCodg("未入组");
                supervisionVo.setInitSco(DEFAULT_VALUE);
                supervisionVo.setInitDif(DEFAULT_VALUE);
            }
            if (ValidateUtil.isEmpty(supervisionVo.getInitDrgCodg()) && grperType.equals("3")) {
                supervisionVo.setInitDrgCodg("未入组");
                supervisionVo.setInitSco(DEFAULT_VALUE);
                supervisionVo.setInitDif(DEFAULT_VALUE);
            }
            medicalRecordSupervisionMapper.insertSupervisionInfo(supervisionVo);
        }
    }

    /**
     * 添加原始数据
     *
     * @param k00
     * @param oldId
     * @param grperType
     */
    private void insertOriginalData(String k00, Long oldId, String grperType) {
        int count = medicalRecordSupervisionMapper.queryBusKeyCount(k00);
        // 添加BusKey原始数据
        int busKeyCount = medicalRecordSupervisionMapper.queryBusKeyRecordCount(k00);
        if (busKeyCount == 0 && count == 0) {
            medicalRecordSupervisionMapper.insertOriginalBusKeyData(oldId, grperType);
        }
        // 添加原始诊断信息
        int diseaseCount = medicalRecordSupervisionMapper.queryDiseaseRecordCount(k00);
        if (diseaseCount == 0 && count == 0) {
            medicalRecordSupervisionMapper.insertOriginalDiseaseData(k00, oldId, grperType);
        }
        // 添加原始手术信息
        int operateCount = medicalRecordSupervisionMapper.queryOperateRecordCount(k00);
        if (operateCount == 0 && count == 0) {
            medicalRecordSupervisionMapper.insertOriginalOperateData(k00, oldId, grperType);
        }
    }

    private SomInvyUpldModiRcdVo getSomInvyUpldModiRcdVo(ModifyDto dto1, String tempK00) {
        SomInvyUpldModiRcdVo updateLogVo = new SomInvyUpldModiRcdVo();
        updateLogVo.setK00(tempK00);
        updateLogVo.setUserName(dto1.getUsername());
        Date updt_date = new Date();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        updateLogVo.setUpdtTime(sdf.format(updt_date));
        //分数
        String settleListId = getSettleListId(dto1);
        updateLogVo.setSettleListId(settleListId);

        String fzlx = (String) SysCommonConfigUtil.get(DrgConst.FZLX, true);

        if (DrgConst.GROUP_TYPE_NAME_DIP.equals(fzlx)) {
            dto1.setGrperType(DrgConst.GROUP_TYPE_DIP);
            updateLogVo.setTabName(SOM_DIP_SCO);
        } else if (DrgConst.GROUP_TYPE_NAME_DRG.equals(fzlx)) {
            dto1.setGrperType(DrgConst.GROUP_TYPE_DRG);
            updateLogVo.setTabName(SOM_DRG_SCO);
        }
        String refer_sco = settleListManageDao.selectScore(updateLogVo);
        updateLogVo.setRefer_sco(refer_sco);
        return updateLogVo;
    }

    private String getK00(ModifyDto dto1) {
        if (!ValidateUtil.isEmpty(dto1.getBaseInfoDto())) {
            return dto1.getBaseInfoDto().getK00();
        }
        if (!ValidateUtil.isEmpty(dto1.getDisInfoDto())) {
            return dto1.getDisInfoDto().getK00();
        }
        if (!ValidateUtil.isEmpty(dto1.getOpeInfoDto())) {
            return dto1.getOpeInfoDto().getK00();
        }
        if (!ValidateUtil.isEmpty(dto1.getBldInfoDto())) {
            return dto1.getBldInfoDto().getK00();
        }
        if (!ValidateUtil.isEmpty(dto1.getIcuInfoDto())) {
            return dto1.getIcuInfoDto().getK00();
        }
        if (!ValidateUtil.isEmpty(dto1.getOcdInfoDto())) {
            return dto1.getOcdInfoDto().getK00();
        }
        return null;
    }

    private String getSettleListId(ModifyDto dto1) {
        if (!ValidateUtil.isEmpty(dto1.getBaseInfoDto())) {
            return dto1.getBaseInfoDto().getId();
        }
        if (!ValidateUtil.isEmpty(dto1.getDisInfoDto())) {
            return dto1.getDisInfoDto().getId();
        }
        if (!ValidateUtil.isEmpty(dto1.getOpeInfoDto())) {
            return dto1.getOpeInfoDto().getId();
        }
        if (!ValidateUtil.isEmpty(dto1.getBldInfoDto())) {
            return dto1.getBldInfoDto().getId();
        }
        if (!ValidateUtil.isEmpty(dto1.getIcuInfoDto())) {
            return dto1.getIcuInfoDto().getId();
        }
        if (!ValidateUtil.isEmpty(dto1.getOcdInfoDto())) {
            return dto1.getOcdInfoDto().getId();
        }
        return null;
    }

    @Override
    public List<SettleListOpeLogInfo> querySettleListOpeLog(ModifyBusSettleListDto dto) {
        List<SettleListOpeLogInfo> list = settleListManageDao.querySettleListOpeLogInfo(dto);
        return list;
    }

    /**
     * 数据处理
     *
     * @param dto
     */
    private void dataProcessing(ModifySettleListDto dto, Long oldId, Long newId) {
        //修改
        if (ValidateUtil.isNotEmpty(dto.getModifyData()) && dto.getModify()) {
            if ("1".equals(dto.getType())) {
                //bus_settle_list表
                //只修改数据不新增不重跑
                List<UpdateDataDto> listDtos = setList(dto, false);
                List<Map<Object, Object>> mapList = setMap(listDtos);
                settleListManageDao.updateDataById(mapList, "som_hi_invy_bas_info");
            } else if ("2".equals(dto.getType())) {
                //只修改数据不新增不重跑
                //门慢门特
                List<UpdateDataDto> listDtos = setList(dto, false);
                List<Map<Object, Object>> mapList = setMap(listDtos);
                settleListManageDao.updateDataById(mapList, "som_otp_slow_special_trt_info");
            } else if ("3".equals(dto.getType())) {
                //修改诊断
                List<UpdateDataDto> listDtos = setList(dto, true);
                List<Map<Object, Object>> mapList = setMap(listDtos);
                settleListManageDao.updateDataById(mapList, "som_diag");
            } else if ("4".equals(dto.getType())) {
                //修改手术
                List<UpdateDataDto> listDtos = setList(dto, false);
                List<Map<Object, Object>> mapList = setMap(listDtos);
                settleListManageDao.updateDataById(mapList, "som_oprn_oprt_info");
            } else if ("5".equals(dto.getType())) {
                //修改ICU
                List<UpdateDataDto> listDtos = setList(dto, false);
                List<Map<Object, Object>> mapList = setMap(listDtos);
                settleListManageDao.updateDataById(mapList, "som_setl_invy_scs_cutd_info");
            } else if ("6".equals(dto.getType())) {
                // 修改输血
                List<UpdateDataDto> listDtos = setList(dto, false);
                List<Map<Object, Object>> mapList = setMap(listDtos);
                settleListManageDao.updateDataById(mapList, "som_setl_invy_bld_info");
            }
        }
        //新增或删除
        if (!dto.getModify()) {
            //门慢门特
            if ("2".equals(dto.getType())) {
                List<SomOtpSlowSpecialTrtInfo> list = (List<SomOtpSlowSpecialTrtInfo>) dto.getInsertData();
                dto.setBusOutpatientClinicDiagnosisList(list);
                if (ValidateUtil.isNotEmpty(dto.getBusOutpatientClinicDiagnosisList())) {
                    settleListManageDao.deleteDataBySettleList(dto, "som_otp_slow_special_trt_info");
                    settleListManageDao.insertOutPatient(dto);
                }
            }
            //诊断
            if ("3".equals(dto.getType())) {
                List<BusDiseaseDiagnosisTrim> list = (List<BusDiseaseDiagnosisTrim>) dto.getInsertData();
                list = JSONObject.parseArray(JSONObject.toJSONString(list)).toJavaList(BusDiseaseDiagnosisTrim.class);
                List<BusDiseaseDiagnosisTrim> listSort = list.stream().sorted(Comparator.comparing(BusDiseaseDiagnosisTrim::getSeq)).collect(Collectors.toList());
                for (int i = 0; i < listSort.size(); i++) {
                    list.get(i).setSeq(i);
                }
                dto.setBusDiseaseDiagnosisTrimList(list);
                if (ValidateUtil.isNotEmpty(dto.getBusDiseaseDiagnosisTrimList())) {
                    settleListManageDao.deleteDataBySettleList(dto, "som_diag");
                    List<BusDiseaseDiagnosisTrim> busDiseaseDiagnosisTrimLists = dto.getBusDiseaseDiagnosisTrimList();
                    List<BusDiseaseDiagnosisTrim> busDiseaseDiagnosisTrimList = busDiseaseDiagnosisTrimLists.stream()
                            .peek(diagnosis -> diagnosis.setMainDiagFlag(diagnosis.getSeq() == 0 ? "1" : "0"))
                            .peek(diagnosis -> diagnosis.setType(ValidateUtil.isEmpty(diagnosis.getType()) ? "1" :diagnosis.getType()))
                            .collect(Collectors.toList());

                    settleListManageDao.insertBusDisease(busDiseaseDiagnosisTrimList, newId);
                }
            }
            //手术
            if ("4".equals(dto.getType())) {
                List<SomOprnOprtInfo> list = (List<SomOprnOprtInfo>) dto.getInsertData();
                list = JSONObject.parseArray(JSONObject.toJSONString(list)).toJavaList(SomOprnOprtInfo.class);
                List<SomOprnOprtInfo> listSort = list.stream().sorted(Comparator.comparing(SomOprnOprtInfo::getSeq)).collect(Collectors.toList());
                for (int i = 0; i < listSort.size(); i++) {
                    list.get(i).setSeq(i);
                }
                dto.setBusOperateDiagnosisList(list);
                settleListManageDao.deleteDataBySettleList(dto, "som_oprn_oprt_info");
                if (ValidateUtil.isNotEmpty(dto.getBusOperateDiagnosisList())) {
                    settleListManageDao.insertBusOperate(dto.getBusOperateDiagnosisList(), newId);
                }
            }
        }
    }

    /**
     * 查询流程进展情况
     *
     * @param newID
     * @return
     */
    private Integer run(String newID) {
        String resNumber = null;
        int count = 0;
        do {
            try {
                if (count == 30) {
                    break;
                }
                Thread.sleep(100);
                AllErrorVo allErrorVo = settleListManageDao.selectProcessResult(newID);
                resNumber = allErrorVo.getResNumber();
                count++;
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        } while (Integer.valueOf(resNumber) <= 0);
        return Integer.valueOf(resNumber);
    }

    /**
     * 将list转换为List<map>
     *
     * @param listDtos
     * @return
     */
    private List<Map<Object, Object>> setMap(List<UpdateDataDto> listDtos) {
        Map<String, List<UpdateDataDto>> collect = listDtos.stream().collect(Collectors.groupingBy(UpdateDataDto::getId));
        List<Map<Object, Object>> mapList = new ArrayList<>();
        for (String s : collect.keySet()) {
            Map<Object, Object> objectMap = new HashMap<>();
            objectMap.put("id", s);
            objectMap.put("data", collect.get(s));
            mapList.add(objectMap);
        }
        return mapList;
    }


    /**
     * 将List<Map>转成list
     *
     * @param dto
     * @return
     */
    public static List<UpdateDataDto> setList(ModifySettleListDto dto, Boolean boole) {
        List<UpdateDataDto> listDtos = new ArrayList<>();
        for (Map<String, Object> stringObjectMap : dto.getModifyData()) {
            UpdateDataDto dto1 = new UpdateDataDto();
            if ("c06c1".equals(String.valueOf(stringObjectMap.get("key")))) {
                dto1.setType("1");
            }
            if ("c07n1".equals(String.valueOf(stringObjectMap.get("key")))) {
                dto1.setType("1");
            }
            if ("c06c2".equals(String.valueOf(stringObjectMap.get("key")))) {
                dto1.setType("2");
            }
            if ("c07n2".equals(String.valueOf(stringObjectMap.get("key")))) {
                dto1.setType("2");
            }
            dto1.setSettleListId(dto.getId());
            dto1.setZd(setZd(String.valueOf(stringObjectMap.get("key"))));

            if(("OPRN_OPRT_BEGNTIME".equals(dto1.getZd()) || "OPRN_OPRT_ENDTIME".equals(dto1.getZd())
                    ||"ANST_BEGNTIME".equals(dto1.getZd()) || "ANST_ENDTIME".equals(dto1.getZd()))
                    && "null".equals(String.valueOf(stringObjectMap.get("value"))) ){
                dto1.setValue(null);
            }else {
                dto1.setValue(String.valueOf(stringObjectMap.get("value")));
            }
//            if(boole){
//                dto1.setId(dto.getId());
//            }else {
            dto1.setId(setId(stringObjectMap.get("id")));
//            }
            listDtos.add(dto1);
        }
        return listDtos;
    }


    /**
     * 将传过来的字段转为对应表的字段
     *
     * @param s
     * @return
     */
    public static String setZd(String s) {
        Map<String, String> zdMap = new HashMap<>();
        String zd = "";
        zdMap.put("diagCode", "DIAG_CODE");
        zdMap.put("diagName", "DIAG_NAME");
        zdMap.put("oprnOprtCode", "OPRN_OPRT_CODE");
        zdMap.put("oprnOprtName", "OPRN_OPRT_NAME");
        zdMap.put("oprnOprtBegntime", "OPRN_OPRT_BEGNTIME");
        zdMap.put("oprnOprtEndtime", "OPRN_OPRT_ENDTIME");
        zdMap.put("anstBegntime", "ANST_BEGNTIME");
        zdMap.put("anstEndtime", "ANST_ENDTIME");
        zdMap.put("c06c1", "dscg_diag_codg");
        zdMap.put("c07n1", "dscg_diag_name");
        zdMap.put("c08c1", "dscg_diag_adm_cond");
        zdMap.put("c50c1", "dscg_diag_dscg_cond");
        zdMap.put("c06c2", "dscg_diag_codg");
        zdMap.put("c07n2", "dscg_diag_name");
        zdMap.put("c08c2", "dscg_diag_adm_cond");
        zdMap.put("c50c2", "dscg_diag_dscg_cond");
        zdMap.put("mtmmImpDeptName", "MTMM_IMP_DEPT_NAME");
        zdMap.put("mtmmInpDate", "MTMM_INP_DATE");
//        zdMap.put("mtmmInpDate","INHOS_MEDICAL_TYPE");
        zdMap.put("medInsOrgan", "MED_INS_ORGAN");
        zdMap.put("medInsOrganOperator", "MED_INS_ORGAN_OPERATOR");
        zdMap.put("clinicId", "CLINIC_ID");
        zdMap.put("settlementId", "SETTLEMENT_ID");
        zdMap.put("psnNo", "PSN_NO");
        zdMap.put("mulNwbBirWt", "MUL_NWB_BIR_WT");
        zdMap.put("mulNwbAdmWt", "MUL_NWB_ADM_WT");
        zdMap.put("listSerialNum", "LIST_SERIAL_NUM");
        zdMap.put("stasType", "STAS_TYPE");
        zdMap.put("scsCutdWardType", "SCS_CUTD_WARD_TYPE");
        zdMap.put("scsCutdInpoolTime", "SCS_CUTD_INPOOL_TIME");
        zdMap.put("scsCutdExitTime", "SCS_CUTD_EXIT_TIME");
        zdMap.put("scsCutdSumDura", "SCS_CUTD_SUM_DURA");
        if (ValidateUtil.isNotEmpty(zdMap.get(s))) {
            zd = zdMap.get(s);
        } else {
            zd = s;
        }
        return zd;
    }

    /**
     * 设置ID
     *
     * @param o
     * @return
     */
    public static String setId(Object o) {
        String id;
        if (!ValidateUtil.isEmpty(o)) {
            id = String.valueOf(o);
        } else {
            id = null;
        }
        return id;
    }

    @Override
    public void updateLockState(ModifyBusSettleListDto dto) {
        if (ValidateUtil.isNotEmpty(dto.getK00())) {
            if (DrgConst.ACTIVE_FLAG_0.equals(dto.getLockState())) {
                settleListManageDao.deleteLockData(dto);
            } else {
                settleListManageDao.insertLockData(dto);
            }
        }
    }

    @Override
    public SettleListTempLockVo queryLockState(ModifyBusSettleListDto dto) {
        SettleListTempLockVo tempLockVo = null;
        if (ValidateUtil.isNotEmpty(dto.getK00())) {
            String curUseranme = dto.getUserName();
            String curIP = dto.getIp();
            tempLockVo = settleListManageDao.queryLockState(dto);
            // 判断IP和用户名是否一致，如果一致则可能是卡主导致锁住，删除即可
            if (!ValidateUtil.isEmpty(tempLockVo)) {
                int count = settleListManageDao.queryLockStateByIpUsername(dto);
                if (count > 0) {
                    settleListManageDao.deleteLockData(dto);
                    tempLockVo = null;
                } else {
                    tempLockVo.setLockUsername(curUseranme);
                    tempLockVo.setLockIp(curIP);
                }
            }
        }
        return tempLockVo;
    }

    @Override
    public SomHiInvyBasInfo querySkipData(ModifyBusSettleListDto dto) {
        if (ValidateUtil.isEmpty(dto.getK00())) {
            throw new AppException("唯一标识错误，请确认唯一标识");
        }
        SomHiInvyBasInfo somHiInvyBasInfo = settleListManageDao.querySkipData(dto);
        if (somHiInvyBasInfo == null) {
            throw new AppException("未查询到病案，请确认数据是否传输到系统");
        }
        return settleListManageDao.querySkipData(dto);
    }

    @Autowired
    private SomSysCodeMapper sysDictMapper;
    @Autowired
    private SomSysDicMapper sysSettleListDictMapper;
    @Autowired
    private ListUploadMapper listUploadMapper;
    @Autowired
    private SomHospInfoMapper busHospitalMapper;

    //默认长度
    private final String DEFAULT_LENGTH = "defaultLength";
    //总长度
    private final String TOTAL_LENGTH = "totalLength";
    //长度
    private final String LENGTH = "length";

    @Override
    public void preview(ReportDto dto, HttpServletResponse response) {
        //查询出清单数据
        //年
        final String YEAR = "year";
        //月
        final String MONTH = "month";
        //日
        final String DAY = "day";
        //时
        final String HOUR = "hour";
        //省
        final String PROVINCE = "provice";
        //市
        final String CITY = "city";
        //县
        final String COUNTY = "county";
        //详细地址
        final String DETAIL_ADRESS = "detailAddress";


        //查询基本数据
        AllBusSettleListInfo allBusSettleListInfo = getAllBusSettleListInfo(dto);
        if ("无".equals(allBusSettleListInfo.getSomHiInvyBasInfo().getB36c())) {
            allBusSettleListInfo.getSomHiInvyBasInfo().setB36c("1");
        } else {
            allBusSettleListInfo.getSomHiInvyBasInfo().setB36c("2");
        }
        //数据准备
        //时间格式的数据，页面该数据所占的长度
        HashMap<String, Map<String, Integer>> timeMap = getTimeMap(YEAR, MONTH, DAY, HOUR);
        //地址格式的数据（省，市分散的），页面该数据所占的长度
//        HashMap<String, Map<String, Integer>> addressMap = getAddressMap(PROVINCE, CITY, COUNTY, DETAIL_ADRESS);
        //联系人地址
        HashMap<String, Map<String, Integer>> addressMap2 = getAddressMap2(PROVINCE, CITY, COUNTY, DETAIL_ADRESS);
        //一般的数据，页面该数据所占的长度
        Map<String, Map<String, Integer>> map = getMap();
        //将查询到的数据转换成map
        HashMap<String, Object> variables = getVariablesMap(allBusSettleListInfo);

//        for (String key : variables.keySet()) {
//            Map<String, Integer> tempMap = map.get(key);
//            Integer total = tempMap.get(TOTAL_LENGTH);
//            int length = variables.get(key).toString().length();
////            res.put(key, variables.get(key));
////            res.put(key + "Length", getEmpty(total - length));
//        }

        //获取属性，拼接空格
        HashMap<String, Object> res = new HashMap<>();
        for (String key : variables.keySet()) {
            StringBuilder str = new StringBuilder();
            //拼接空格
            if (map.containsKey(key)) {
                //设置普通数据
                putCommonData(map, variables, res, key, str);
            } else if (timeMap.containsKey(key)) {
                //设置时间类型的数据
                putTimeData(YEAR, MONTH, DAY, HOUR, timeMap, variables, res, key);

//            } else if(addressMap.containsKey(key)){
//                StringBuilder provice = new StringBuilder("四川");
//                StringBuilder city = new StringBuilder("德阳");
//                StringBuilder county = new StringBuilder("中江");
////                StringBuilder city = new StringBuilder(allBusSettleListInfo.getSomHiInvyBasInfo().getA23c().substring(0,allBusSettleListInfo.getSomHiInvyBasInfo().getA23c().length() - 1));
////                StringBuilder county = new StringBuilder(allBusSettleListInfo.getSomHiInvyBasInfo().getA22().substring(0,allBusSettleListInfo.getSomHiInvyBasInfo().getA22().length() - 1));
//                StringBuilder detailAddress = new StringBuilder(allBusSettleListInfo.getSomHiInvyBasInfo().getA26());
//                if (provice.length() < addressMap.get(key).get(PROVINCE)){
//                    joinBlank(provice, addressMap.get(key).get(PROVINCE) - provice.length(),BLACK);
//                }
//                if (city.length() < addressMap.get(key).get(CITY)){
//                    joinBlank(city, addressMap.get(key).get(PROVINCE) - city.length(),BLACK);
//                }
//                if (county.length() < addressMap.get(key).get(COUNTY)){
//                    joinBlank(county, addressMap.get(key).get(PROVINCE) - county.length(),BLACK);
//                }
//                if (detailAddress.length() < addressMap.get(key).get(DETAIL_ADRESS)){
//                    joinBlank(detailAddress, addressMap.get(key).get(DETAIL_ADRESS) - detailAddress.length(),BLACK);
//                }
//                HashMap<String, String> address = new HashMap<String, String>() {
//                    {
//                        put(PROVINCE, provice.toString());
//                        put(CITY, city.toString());
//                        put(COUNTY, county.toString());
//                        put(DETAIL_ADRESS, detailAddress.toString());
//                    }
//                };
//                res.put(key,address);
            } else if (addressMap2.containsKey(key)) {
                //设置地址类型的数据
                putAddressData(PROVINCE, CITY, COUNTY, DETAIL_ADRESS, addressMap2, variables, res, key);
            }

        }
        //结算清单门诊慢特数据
        setSomOtpSlowSpecialTrtInfo(allBusSettleListInfo, res);
        //结算清单疾病诊断数据
        setBusDiseaseDiagnosisTrim(allBusSettleListInfo, res);
        //结算清单手术数据
        setSomOprnOprtInfo(allBusSettleListInfo, res);
        //结算清单重症监护室数据
        setSomSetlInvyScsCutdInfo(allBusSettleListInfo, res);
        //结算清单医疗费用数据
        setSomHiSetlInvyMedFeeInfo(allBusSettleListInfo, res);
        //结算清单基金支付数据
        setSomFundPay(allBusSettleListInfo, res);

        dto.setVariables(res);
        //生成报表
        ReportUtil.judgeReportExists(dto);
        ReportUtil.handlerReport(dto, "/templates/ftls", response);
    }

    private void putCommonData(Map<String, Map<String, Integer>> map, HashMap<String, Object> variables, HashMap<String, Object> res, String key, StringBuilder str) {
        Integer defaultLen = map.get(key).get(DEFAULT_LENGTH);
        Integer totalLen = map.get(key).get(TOTAL_LENGTH) - BLACK_SUM;
        Integer len = 0;
        if (variables.get(key) != null) {
            String variable = variables.get(key).toString();
            str.append(variable);
            if (ValidateUtil.isNotEmpty(variable)) {
                int variableLen = getVariableLen(variable);
                joinBlank(str, BLACK_SUM, NULL);
                if (totalLen >= variableLen + BLACK_SUM) {
                    len = (totalLen - variableLen) * 2;
                }
            } else {
                joinBlank(str, defaultLen * 2, BLACK);
                joinBlank(str, BLACK_SUM, NULL);
                len = (totalLen - defaultLen) * 2;
            }
//            if (variable.length() < defaultLen) {
//                joinBlank(str, BLACK_SUM, BLACK);
//                len = totalLen - defaultLen;
//            } else {
//                if (variable.length() < totalLen) {
//                    len = totalLen - defaultLen;
//                }
//            }

        } else {
            joinBlank(str, defaultLen * 2, BLACK);
            joinBlank(str, defaultLen, NULL);
            len = (totalLen - defaultLen) * 2;
//            throw new AppException(key + "没有！");
        }
        StringBuilder str2 = new StringBuilder("");
        joinBlank(str2, len, BLACK);
        res.put(key, str.toString());
        res.put(key + LENGTH, str2);
    }

    private int getVariableLen(String variable) {
        int letter = 0;//字母的个数
        int number = 0;//数字的个数
        int other = 0;//其他字符个数
        for (int i = 0; i < variable.length(); i++) {
            if ((variable.charAt(i) >= 'a' && variable.charAt(i) <= 'z') || (variable.charAt(i) >= 'A' && variable.charAt(i) <= 'Z')) {
                letter++;
            } else if (variable.charAt(i) >= '0' && variable.charAt(i) <= '9') {
                number++;
            } else {
                other++;
            }
        }
        if (number % 2 == 0) {
            number = number / 2;
        } else {
            number = number / 2 + 1;
        }
        return other + letter + number;
    }

    private void putTimeData(String YEAR, String MONTH, String DAY, String HOUR, HashMap<String, Map<String, Integer>> timeMap, HashMap<String, Object> variables, HashMap<String, Object> res, String key) {
        StringBuilder year = new StringBuilder();
        StringBuilder month = new StringBuilder();
        StringBuilder day = new StringBuilder();
        StringBuilder hour = new StringBuilder();
        //获取字符串的长度
        Map<String, Integer> time = timeMap.get(key);
        Integer yearLen = time.get(YEAR);
        Integer monthLen = time.get(MONTH);
        Integer dayLen = time.get(DAY);

        HashMap<String, String> timeRest = new HashMap<String, String>();
        if (ValidateUtil.isNotEmpty((String) variables.get(key))) {
            String[] s = variables.get(key).toString().split(" ");
            String variable = s[0];
            String[] timeSplit = variable.split("-");
            for (int i = 0; i < 3; i++) {
                if (timeSplit.length > i) {
                    if (i == 0) {
                        year.append(timeSplit[i]);
                    }
                    if (i == 1) {
                        month.append(timeSplit[i]);
                    }
                    if (i == 2) {
                        day.append(timeSplit[i]);
                    }
                } else {
                    if (i == 0) {
                        year.append(0);
                    }
                    if (i == 1) {
                        month.append(0);
                    }
                    if (i == 2) {
                        day.append(0);
                    }
                }
            }
            if (timeSplit.length > 0 && timeSplit[0].length() > 0) {
                joinBlank(year, BLACK_SUM, NULL);
            } else {
                joinBlank(year, yearLen, BLACK);
            }
            if (timeSplit.length > 1 && timeSplit[1].length() > 0) {
                joinBlank(month, BLACK_SUM, NULL);
            } else {
                joinBlank(month, monthLen, BLACK);
            }
            if (timeSplit.length > 2 && timeSplit[2].length() > 0) {
                joinBlank(day, BLACK_SUM, NULL);
            } else {
                joinBlank(day, dayLen, BLACK);
            }
            //判断时间是否带有 时
            if (time.get(HOUR) != null && s.length > 1) {
                hour.append(s[1].substring(0, 2));
                if (s[1].substring(0, 2).length() > 0) {
                    joinBlank(hour, BLACK_SUM, NULL);
                }
                timeRest.put(HOUR, hour.toString());
            } else {
                timeRest.put(HOUR, "0 ");
            }

        } else {
            joinBlank(year, yearLen, BLACK);
            joinBlank(month, yearLen, BLACK);
            joinBlank(day, yearLen, BLACK);
            if (time.get(HOUR) != null) {
                joinBlank(hour, time.get(HOUR), BLACK);
            }
        }
        StringBuilder str2 = new StringBuilder("");
        int len = timeMap.get(key).get(key + LENGTH);
        joinBlank(str2, len, BLACK);
        timeRest.put(YEAR, year.toString());
        timeRest.put(MONTH, month.toString());
        timeRest.put(DAY, day.toString());
        timeRest.put(key + LENGTH, str2.toString());
        res.put(key, timeRest);
    }

    private void putAddressData(String PROVINCE, String CITY, String COUNTY, String DETAIL_ADRESS, HashMap<String, Map<String, Integer>> addressMap2, HashMap<String, Object> variables, HashMap<String, Object> res, String key) {
        StringBuilder provice = new StringBuilder();
        StringBuilder city = new StringBuilder();
        StringBuilder county = new StringBuilder();
        StringBuilder detailAddress = new StringBuilder();
        if (variables.get(key) != null) {
            String variable = variables.get(key).toString();
            int p = variable.indexOf("省");
            int c = variable.indexOf("市");
            int cou = variable.indexOf("县");
            String str3 = "";
            String str4 = "";
            if (cou < 0) {
                int qu = variable.indexOf("区");
                if (qu > 0 && c > 0) {
                    str3 = variable.substring(c + 1, qu);
                } else if (qu > 0) {
                    str3 = variable.substring(0, qu);
                    str4 = variable.substring(qu + 1);
                } else {
                    str4 = variable;
                }
            } else if (cou > 0) {
                if (c > 0) {
                    str3 = variable.substring(c + 1, cou);
                } else if (p > 0) {
                    str3 = variable.substring(p + 1, cou);
                } else {
                    str3 = variable.substring(0, cou);
                }
                str4 = variable.substring(cou + 1);
            }
            String str1 = "";
            if (p > 0) {
                str1 = variable.substring(0, p);
            }
            String str2 = "";
            if (c > 0 && p > 0) {
                str2 = variable.substring(p + 1, c);
            } else if (c > 0) {
                str2 = variable.substring(0, c);
            }

            provice.append(str1);
            city.append(str2);
            county.append(str3);
            detailAddress.append(str4);
            if (provice.length() < addressMap2.get(key).get(PROVINCE)) {
                joinBlank(provice, BLACK_SUM, NULL);
            }
            if (city.length() < addressMap2.get(key).get(CITY)) {
                joinBlank(city, BLACK_SUM, NULL);
            }
            if (county.length() < addressMap2.get(key).get(COUNTY)) {
                joinBlank(county, BLACK_SUM, NULL);
            }
            if (detailAddress.length() < addressMap2.get(key).get(DETAIL_ADRESS)) {
                joinBlank(detailAddress, BLACK_SUM, NULL);
            }
        } else {
            joinBlank(provice, addressMap2.get(key).get(PROVINCE), BLACK);
            joinBlank(provice, BLACK_SUM, NULL);
            joinBlank(city, addressMap2.get(key).get(CITY), BLACK);
            joinBlank(city, BLACK_SUM, NULL);
            joinBlank(county, addressMap2.get(key).get(COUNTY), BLACK);
            joinBlank(county, BLACK_SUM, NULL);
            joinBlank(detailAddress, addressMap2.get(key).get(DETAIL_ADRESS), BLACK);
            joinBlank(detailAddress, BLACK_SUM, NULL);
        }
        //间隔空格
        StringBuilder str2 = new StringBuilder("");
        int len = addressMap2.get(key).get(key + LENGTH);
        joinBlank(str2, len, BLACK);

        HashMap<String, String> address = new HashMap<String, String>() {
            {
                put(PROVINCE, provice.toString());
                put(CITY, city.toString());
                put(COUNTY, county.toString());
                put(DETAIL_ADRESS, detailAddress.toString());
                put(key + LENGTH, str2.toString());
            }
        };
        res.put(key, address);
    }

    private void setSomFundPay(AllBusSettleListInfo allBusSettleListInfo, HashMap<String, Object> res) {
        List<SomFundPay> list = allBusSettleListInfo.getBusFundPayList();
//        list.stream().forEach(item -> setNullValue(item));
        if (ValidateUtil.isNotEmpty(list)) {
            list.forEach(item -> {
                if ("医保统筹基金支付".equals(item.getFundPayType())) {
                    res.put("consolidatedFound", item);
                }
                if ("大病保险".equals(item.getFundPayType())) {
                    res.put("criticalIllnessInsurance", item);
                }
                if ("医疗救助".equals(item.getFundPayType())) {
                    res.put("medicalAssistance", item);
                }
                if ("公务员医疗补助".equals(item.getFundPayType())) {
                    res.put("civilServant", item);
                }
                if ("大额补充".equals(item.getFundPayType())) {
                    res.put("largeSupplement", item);
                }
                if ("企业补充".equals(item.getFundPayType())) {
                    res.put("privateGroupBenefits", item);
                }
                if ("其他支付".equals(item.getFundPayType())) {
                    res.put("otherPaymentMethods", item);
                }
                if ("城乡居民基本医疗保险基金".equals(item.getFundPayType())) {
                    res.put("otherPaymentMethods", item);
                }
                if ("城镇职工基本医疗保险统筹基金".equals(item.getFundPayType())) {
                    res.put("otherPaymentMethods", item);
                }
                if ("企业补充医疗保险基金".equals(item.getFundPayType())) {
                    res.put("otherPaymentMethods", item);
                }
                if ("公务员医疗补助基金".equals(item.getFundPayType())) {
                    res.put("otherPaymentMethods", item);
                }
                if ("城镇职工基本医疗保险个人账户基金".equals(item.getFundPayType())) {
                    res.put("otherPaymentMethods", item);
                }
                if ("医疗救助基金".equals(item.getFundPayType())) {
                    res.put("otherPaymentMethods", item);
                }
                if ("大额医疗费用补助基金".equals(item.getFundPayType())) {
                    res.put("otherPaymentMethods", item);
                }
                if ("城乡居民大病医疗保险基金".equals(item.getFundPayType())) {
                    res.put("otherPaymentMethods", item);
                }
            });
        }
    }

    private void setSomHiSetlInvyMedFeeInfo(AllBusSettleListInfo allBusSettleListInfo, HashMap<String, Object> res) {
        List<SomHiSetlInvyMedFeeInfo> list = allBusSettleListInfo.getBusMedicalCostList();
//        list.stream().forEach(item -> setNullValue(item));
        if (ValidateUtil.isNotEmpty(list)) {
            list.forEach(item -> {
                //床位费
                if ("床位费".equals(item.getMedChrgItemname())) {
                    res.put("bedCost", item);
                }
                //诊察费
                if ("诊察费".equals(item.getMedChrgItemname())) {
                    res.put("examinationCost", item);
                }
                //检查费
                if ("检查费".equals(item.getMedChrgItemname())) {
                    res.put("checkCost", item);
                }
                //化验费
                if ("化验费".equals(item.getMedChrgItemname())) {
                    res.put("testCost", item);
                }
                //治疗费
                if ("治疗费".equals(item.getMedChrgItemname())) {
                    res.put("cureCost", item);
                }
                //手术费
                if ("手术费".equals(item.getMedChrgItemname())) {
                    res.put("operationCost", item);
                }
                //护理费
                if ("护理费".equals(item.getMedChrgItemname())) {
                    res.put("nurseCost", item);
                }
                //卫生材料费
                if ("卫生材料费".equals(item.getMedChrgItemname())) {
                    res.put("materialsCost", item);
                }
                //西药费
                if ("西药费".equals(item.getMedChrgItemname())) {
                    res.put("westernCost", item);
                }
                //中药饮片费
                if ("中药饮片费".equals(item.getMedChrgItemname())) {
                    res.put("traditionalCost", item);
                }
                //中成药费
                if ("中成药费".equals(item.getMedChrgItemname())) {
                    res.put("patentCost", item);
                }
                //一般诊疗费
                if ("一般诊疗费".equals(item.getMedChrgItemname())) {
                    res.put("diagnosisCost", item);
                }
                //挂号费
                if ("挂号费".equals(item.getMedChrgItemname())) {
                    res.put("registrationCost", item);
                }
                //其他费
                if ("其他费".equals(item.getMedChrgItemname())) {
                    res.put("othFee", item);
                }
                //金额合计
                if ("金额合计".equals(item.getMedChrgItemname())) {
                    res.put("sumfee", item);
                }

            });
        }
    }

    private void setSomSetlInvyScsCutdInfo(AllBusSettleListInfo allBusSettleListInfo, HashMap<String, Object> res) {
        List<SomSetlInvyScsCutdInfo> list = allBusSettleListInfo.getBusIcuList();
        list.stream().forEach(item -> setNullValue(item));
        if (ValidateUtil.isNotEmpty(list)) {
            if (list.size() < 3) {
                for (int i = list.size(); i < 3; i++) {
                    SomSetlInvyScsCutdInfo vo = new SomSetlInvyScsCutdInfo();
                    vo.setScsCutdWardType("");
                    vo.setScsCutdInpoolTime("");
                    vo.setScsCutdExitTime("");
                    vo.setScsCutdSumDura("");
                    list.add(vo);
                }
            }
        } else {
            for (int i = 0; i < 3; i++) {
                SomSetlInvyScsCutdInfo vo = new SomSetlInvyScsCutdInfo();
                vo.setScsCutdWardType("");
                vo.setScsCutdInpoolTime("");
                vo.setScsCutdExitTime("");
                vo.setScsCutdSumDura("");
                list.add(vo);
            }
        }
        res.put("busIcuList", list);
    }


    private void setNullValue(Object obj) {
        Method[] declaredMethods = obj.getClass().getDeclaredMethods();
        for (int i = 0; i < declaredMethods.length; i++) {
            Method method = declaredMethods[i];
            String methodName = method.getName();
            if (methodName.contains("set")) {
                String suffix = methodName.substring(methodName.indexOf("set") + 3);
                Method getMethod = null;
                try {
                    for (Method declaredMethod : declaredMethods) {
                        if (("get" + suffix).equals(declaredMethod.getName())) {
                            getMethod = declaredMethod;
                            break;
                        }
                    }
                    if (getMethod != null) {
                        Object invoke = getMethod.invoke(obj);
                        if (invoke == null) {
                            if (getMethod.getReturnType().getName().contains("Integer")) {
                                method.invoke(obj, 0);
                            } else if (getMethod.getReturnType().getName().contains("Long")) {
                                method.invoke(obj, 0L);
                            } else {
                                method.invoke(obj, "");
                            }
                        }
                    }
                } catch (IllegalAccessException | InvocationTargetException e) {
                    throw new RuntimeException(e);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
    }

    private void setSomOprnOprtInfo(AllBusSettleListInfo allBusSettleListInfo, HashMap<String, Object> res) {
        List<SomOprnOprtInfo> list = allBusSettleListInfo.getBusOperateDiagnosisList();
        list.stream().forEach(somOprnOprtInfo -> setNullValue(somOprnOprtInfo));
        res.put("operationSum", list.size());
        if (ValidateUtil.isNotEmpty(list)) {
            if (list.size() < 10) {
                for (int i = list.size(); i < 10; i++) {
                    SomOprnOprtInfo vo = new SomOprnOprtInfo();
                    vo.setC36n("");
                    vo.setC35c("");
                    vo.setOprnOprtBegntime("");
                    vo.setOprnOprtEndtime("");
                    vo.setC43("");
                    vo.setOprn_oprt_oper_name("");
                    vo.setC39c("");
                    vo.setOprn_oprt_anst_dr_name("");
                    vo.setOprn_oprt_anst_dr_code("");
                    list.add(vo);
                }
            }

        } else {
            for (int i = 0; i < 10; i++) {
                SomOprnOprtInfo vo = new SomOprnOprtInfo();
                vo.setC36n("");
                vo.setC35c("");
                vo.setOprnOprtBegntime("");
                vo.setOprnOprtEndtime("");
                vo.setC43("");
                vo.setOprn_oprt_oper_name("");
                vo.setC39c("");
                vo.setOprn_oprt_anst_dr_name("");
                vo.setOprn_oprt_anst_dr_code("");
                list.add(vo);
            }
        }
        //主要和其他
        res.put("mainOperation", list.get(0));
        res.put("otherOperation", list.get(1));
        List<SomOprnOprtInfo> list2 = new ArrayList<>();
        for (int i = 2; i < list.size(); i++) {
            list2.add(list.get(i));
        }
        res.put("busOperateDiagnosisList", list2);
    }

    private void setBusDiseaseDiagnosisTrim(AllBusSettleListInfo allBusSettleListInfo, HashMap<String, Object> res) {
        List<BusDiseaseDiagnosisTrim> list = allBusSettleListInfo.getBusDiseaseDiagnosisTrimList();
        list.stream().forEach(item -> setNullValue(item));
        res.put("diagnoseSum", list.size());
        if (ValidateUtil.isNotEmpty(list)) {
            for (BusDiseaseDiagnosisTrim trim : list) {
                if (ValidateUtil.isEmpty(trim.getC07n1())) {
                    trim.setC07n1("");
                }
                if (ValidateUtil.isEmpty(trim.getC06c1())) {
                    trim.setC06c1("");
                }
                if (ValidateUtil.isEmpty(trim.getC08c1())) {
                    trim.setC08c1("");
                }
                if (ValidateUtil.isEmpty(trim.getC07n2())) {
                    trim.setC07n2("");
                }
                if (ValidateUtil.isEmpty(trim.getC07n2())) {
                    trim.setC06c2("");
                }
                if (ValidateUtil.isEmpty(trim.getC08c2())) {
                    trim.setC08c2("");
                }
            }
            if (list.size() < 9) {
                for (int i = list.size(); i < 9; i++) {
                    BusDiseaseDiagnosisTrim vo = new BusDiseaseDiagnosisTrim();
                    vo.setC07n1("");
                    vo.setC06c1("");
                    vo.setC08c1("");
                    vo.setC07n2("");
                    vo.setC06c2("");
                    vo.setC08c2("");
                    list.add(vo);
                }
            }
        } else {
            for (int i = 0; i < 9; i++) {
                BusDiseaseDiagnosisTrim vo = new BusDiseaseDiagnosisTrim();
                vo.setC07n1("");
                vo.setC06c1("");
                vo.setC08c1("");
                vo.setC07n2("");
                vo.setC06c2("");
                vo.setC08c2("");
                list.add(vo);
            }
        }
        //主要诊断
        List<BusDiseaseDiagnosisTrim> list2 = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            if (i == 0) {
                res.put("main", list.get(0));
            }
            if (i == 1) {
                res.put("other", list.get(1));
            }
            if (i > 1) {
                list2.add(list.get(i));
            }
        }
        res.put("busDiseaseDiagnosisTrimList", list2);
    }

    private void setSomOtpSlowSpecialTrtInfo(AllBusSettleListInfo allBusSettleListInfo, HashMap<String, Object> res) {
        List<SomOtpSlowSpecialTrtInfo> diagnosisList = allBusSettleListInfo.getBusOutpatientClinicDiagnosisList();
        diagnosisList.stream().forEach(item -> setNullValue(item));
        if (ValidateUtil.isNotEmpty(diagnosisList)) {
            if (diagnosisList.size() < 6) {
                for (int i = diagnosisList.size(); i < 6; i++) {
                    SomOtpSlowSpecialTrtInfo vo = new SomOtpSlowSpecialTrtInfo();
                    vo.setDiagName("");
                    vo.setDiagCode("");
                    vo.setOprnOprtName("");
                    vo.setOprnOprtCode("");
                    diagnosisList.add(vo);
                }
            }

        } else {
            for (int i = 0; i < 6; i++) {
                SomOtpSlowSpecialTrtInfo vo = new SomOtpSlowSpecialTrtInfo();
                vo.setDiagName("");
                vo.setDiagCode("");
                vo.setOprnOprtName("");
                vo.setOprnOprtCode("");
                diagnosisList.add(vo);
            }
        }
        res.put("busOutpatientClinicDiagnosisList", diagnosisList);
    }

    private AllBusSettleListInfo getAllBusSettleListInfo(ReportDto dto) {

        final String PSN_INSU_TYPE = "PSN_INSU_TYPE";
        final String TSRYLX = "TSRYLX";
        final String XSELX = "XSELX";
        final String HZZJLB = "HZZJLB";
        final String JTGX = "JTGX";
        final String RYBQ = "RYBQ";
        final String GJ = "GJ";
        final String MZ = "MZ";
        final String ZY = "ZY";
        final String MZFS = "MZFS";
        //字典转化
        //1.医保类型
        List<SomSysCode> medicareDict = sysDictMapper.queryDictByCodeType(PSN_INSU_TYPE);
        //2.特殊人员类型
        List<SomSysCode> tsrmDict = sysSettleListDictMapper.queryDictByCodeType(TSRYLX);
        //3.新生儿入院类型
        List<SomSysCode> xseDict = sysSettleListDictMapper.queryDictByCodeType(XSELX);
        //4.患者证件类别
        List<SomSysCode> hzzjDict = sysSettleListDictMapper.queryDictByCodeType(HZZJLB);
        //5.关系
        List<SomSysCode> gxDict = sysSettleListDictMapper.queryDictByCodeType(JTGX);
        //6.入院病情
        List<SomSysCode> rybqDict = sysSettleListDictMapper.queryDictByCodeType(RYBQ);
        //7.国籍
        List<SomSysCode> gjDict = sysSettleListDictMapper.queryDictByCodeType(GJ);
        //8.民族
        List<SomSysCode> mzDict = sysDictMapper.queryDictByCodeType(MZ);
        //9.职业
        List<SomSysCode> zyDict = sysSettleListDictMapper.queryDictByCodeType(ZY);
        List<SomSysCode> mzfsDict = sysSettleListDictMapper.queryDictByCodeType(MZFS);

        AllBusSettleListInfo allBusSettleListInfo = settleListManageService.getSettleListAllInfoById(dto.getId(), dto.getK00());
        //设置清单流水号
        SettleListUploadLogVo uploadLogVo = listUploadMapper.queryUploadedLogByK00(allBusSettleListInfo.getSomHiInvyBasInfo().getK00());
        if (uploadLogVo != null) {
            if (ValidateUtil.isNotEmpty(uploadLogVo.getResSettleListNo())) {
                allBusSettleListInfo.getSomHiInvyBasInfo().setA58(uploadLogVo.getResSettleListNo());
            }
        }
        setSettleLevel(allBusSettleListInfo);
        //定点医疗机构代码
        ListUploadDto listUploadDto = new ListUploadDto();
        listUploadDto.setInsuplcAdmdvs((String) SysCommonConfigUtil.get(DrgConst.INSUPLC_ADMDVS_AREA));
        List<SettleListConfigVo> settleListConfigVoList = listUploadMapper.querySettleConfig(listUploadDto);
        SettleListConfigVo configVo = null;
        if (ValidateUtil.isNotEmpty(settleListConfigVoList)) {
            configVo = settleListConfigVoList.get(0);
        }
        if (configVo != null) {
            allBusSettleListInfo.getSomHiInvyBasInfo().setA01(configVo.getFixmedins_code());
            allBusSettleListInfo.getSomHiInvyBasInfo().setA02(configVo.getFixmedins_name());
        }

        for (SomSysCode dict : medicareDict) {
            if (dict.getDataVal().equals(allBusSettleListInfo.getSomHiInvyBasInfo().getA54())) {
                allBusSettleListInfo.getSomHiInvyBasInfo().setA54(dict.getLablName());
            }
        }
        for (SomSysCode dict : tsrmDict) {
            if (dict.getDataVal().equals(allBusSettleListInfo.getSomHiInvyBasInfo().getA55())) {
                allBusSettleListInfo.getSomHiInvyBasInfo().setA55(dict.getLablName());
            }
        }
        for (SomSysCode dict : xseDict) {
            if (dict.getDataVal().equals(allBusSettleListInfo.getSomHiInvyBasInfo().getA57())) {
                allBusSettleListInfo.getSomHiInvyBasInfo().setA57(dict.getLablName());
            }
        }
        for (SomSysCode dict : hzzjDict) {
            if (dict.getDataVal().equals(allBusSettleListInfo.getSomHiInvyBasInfo().getA53())) {
                allBusSettleListInfo.getSomHiInvyBasInfo().setA53(dict.getLablName());
            }
        }
        for (SomSysCode dict : gxDict) {
            if (dict.getDataVal().equals(allBusSettleListInfo.getSomHiInvyBasInfo().getA33c())) {
                allBusSettleListInfo.getSomHiInvyBasInfo().setA33c(dict.getLablName());
            }
        }
        for (SomSysCode dict : gjDict) {
            if (dict.getDataVal().equals(allBusSettleListInfo.getSomHiInvyBasInfo().getA15c())) {
                allBusSettleListInfo.getSomHiInvyBasInfo().setA15c(dict.getLablName());
            }
        }
        for (SomSysCode dict : mzDict) {
            if (dict.getDataVal().equals(allBusSettleListInfo.getSomHiInvyBasInfo().getA19c())) {
                allBusSettleListInfo.getSomHiInvyBasInfo().setA19c(dict.getLablName());
            }
        }
        for (SomSysCode dict : zyDict) {
            if (dict.getDataVal().equals(allBusSettleListInfo.getSomHiInvyBasInfo().getA38c())) {
                allBusSettleListInfo.getSomHiInvyBasInfo().setA38c(dict.getLablName());
            }
        }
        allBusSettleListInfo.getBusDiseaseDiagnosisTrimList().forEach(item -> {
            for (SomSysCode dict : rybqDict) {
                if (dict.getDataVal().equals(item.getC08c1())) {
                    item.setC08c1(dict.getLablName());
                }
            }
        });
        allBusSettleListInfo.getBusOperateDiagnosisList().forEach(item -> {
            for (SomSysCode dict : mzfsDict) {
                if (dict.getDataVal().equals(item.getC43())) {
                    item.setC43(dict.getLablName());
                }
            }
        });
        return allBusSettleListInfo;
    }

    @Override
    public void setSettleLevel(AllBusSettleListInfo allBusSettleListInfo) {
        //医保结算等级
        final String HOSPITAL_lEVEL_1 = "1";
        final String HOSPITAL_lEVEL_2 = "2";
        final String HOSPITAL_lEVEL_3 = "3";
        final String SETTLE_LEVE_1 = "一级";
        final String SETTLE_LEVE_2 = "二级";
        final String SETTLE_LEVE_3 = "三级";
        SomHiInvyBasInfo somHiInvyBasInfo = busHospitalMapper.queryBusHospitalByHospitalId(allBusSettleListInfo.getSomHiInvyBasInfo().getHospitalId());
        if (somHiInvyBasInfo != null) {
            String dept_lv = somHiInvyBasInfo.getHospLv();
            if (HOSPITAL_lEVEL_1.equals(dept_lv)) {
                allBusSettleListInfo.getSomHiInvyBasInfo().setA50(SETTLE_LEVE_1);
            } else if (HOSPITAL_lEVEL_2.equals(dept_lv)) {
                allBusSettleListInfo.getSomHiInvyBasInfo().setA50(SETTLE_LEVE_2);
            } else if (HOSPITAL_lEVEL_3.equals(dept_lv)) {
                allBusSettleListInfo.getSomHiInvyBasInfo().setA50(SETTLE_LEVE_3);
            }
        }
    }



    private HashMap<String, Object> getVariablesMap(AllBusSettleListInfo allBusSettleListInfo) {
        return JSON.parseObject(JSON.toJSONString(allBusSettleListInfo.getSomHiInvyBasInfo(),
                SerializerFeature.WriteMapNullValue), HashMap.class);
    }

    private Map<String, Map<String, Integer>> getMap() {
        Map<String, Map<String, Integer>> map = new HashMap<String, Map<String, Integer>>() {
            {
                put("a58", new HashMap<String, Integer>() {
                    {
                        put(DEFAULT_LENGTH, 6);
                        put(TOTAL_LENGTH, 15);
                    }
                });
                put("a02", new HashMap<String, Integer>() {
                    {
                        put(DEFAULT_LENGTH, 6);
                        put(TOTAL_LENGTH, 10);
                    }
                });
                put("a01", new HashMap<String, Integer>() {
                    {
                        put(DEFAULT_LENGTH, 6);
                        put(TOTAL_LENGTH, 10);
                    }
                });
                put("a50", new HashMap<String, Integer>() {
                    {
                        put(DEFAULT_LENGTH, 4);
                        put(TOTAL_LENGTH, 4);
                    }
                });
                put("a51", new HashMap<String, Integer>() {
                    {
                        put(DEFAULT_LENGTH, 6);
                        put(TOTAL_LENGTH, 14);
                    }
                });
                put("a48", new HashMap<String, Integer>() {
                    {
                        put(DEFAULT_LENGTH, 6);
                        put(TOTAL_LENGTH, 14);
                    }
                });
                put("a11", new HashMap<String, Integer>() {
                    {
                        put(DEFAULT_LENGTH, 3);
                        put(TOTAL_LENGTH, 6);
                    }
                });
                put("a12c", new HashMap<String, Integer>() {
                    {
                        put(DEFAULT_LENGTH, 1);
                        put(TOTAL_LENGTH, 3);
                    }
                });
                put("a14", new HashMap<String, Integer>() {
                    {
                        put(DEFAULT_LENGTH, 1);
                        put(TOTAL_LENGTH, 5);
                    }
                });
                put("a15c", new HashMap<String, Integer>() {
                    {
                        put(DEFAULT_LENGTH, 3);
                        put(TOTAL_LENGTH, 5);
                    }
                });
                put("a16", new HashMap<String, Integer>() {
                    {
                        put(DEFAULT_LENGTH, 1);
                        put(TOTAL_LENGTH, 3);
                    }
                });
                put("a19c", new HashMap<String, Integer>() {
                    {
                        put(DEFAULT_LENGTH, 1);
                        put(TOTAL_LENGTH, 3);
                    }
                });
                put("a53", new HashMap<String, Integer>() {
                    {
                        put(DEFAULT_LENGTH, 4);
                        put(TOTAL_LENGTH, 7);
                    }
                });
                put("a20", new HashMap<String, Integer>() {
                    {
                        put(DEFAULT_LENGTH, 18);
                        put(TOTAL_LENGTH, 21);
                    }
                });
                put("a38c", new HashMap<String, Integer>() {
                    {
                        put(DEFAULT_LENGTH, 4);
                        put(TOTAL_LENGTH, 10);
                    }
                });
                put("a29n", new HashMap<String, Integer>() {
                    {
                        put(DEFAULT_LENGTH, 6);
                        put(TOTAL_LENGTH, 7);
                    }
                });
                put("a29", new HashMap<String, Integer>() {
                    {
                        put(DEFAULT_LENGTH, 6);
                        put(TOTAL_LENGTH, 7);
                    }
                });
                put("a30", new HashMap<String, Integer>() {
                    {
                        put(DEFAULT_LENGTH, 11);
                        put(TOTAL_LENGTH, 13);
                    }
                });
                put("a31c", new HashMap<String, Integer>() {
                    {
                        put(DEFAULT_LENGTH, 4);
                        put(TOTAL_LENGTH, 6);
                    }
                });
                put("a32", new HashMap<String, Integer>() {
                    {
                        put(DEFAULT_LENGTH, 3);
                        put(TOTAL_LENGTH, 5);
                    }
                });
                put("a33c", new HashMap<String, Integer>() {
                    {
                        put(DEFAULT_LENGTH, 2);
                        put(TOTAL_LENGTH, 4);
                    }
                });
                put("a35", new HashMap<String, Integer>() {
                    {
                        put(DEFAULT_LENGTH, 11);
                        put(TOTAL_LENGTH, 13);
                    }
                });
                put("a54", new HashMap<String, Integer>() {
                    {
                        put(DEFAULT_LENGTH, 3);
                        put(TOTAL_LENGTH, 11);
                    }
                });
                put("a55", new HashMap<String, Integer>() {
                    {
                        put(DEFAULT_LENGTH, 3);
                        put(TOTAL_LENGTH, 11);
                    }
                });
                put("a56", new HashMap<String, Integer>() {
                    {
                        put(DEFAULT_LENGTH, 8);
                        put(TOTAL_LENGTH, 10);
                    }
                });
                put("a57", new HashMap<String, Integer>() {
                    {
                        put(DEFAULT_LENGTH, 3);
                        put(TOTAL_LENGTH, 8);
                    }
                });
                put("a18", new HashMap<String, Integer>() {
                    {
                        put(DEFAULT_LENGTH, 2);
                        put(TOTAL_LENGTH, 10);
                    }
                });
                put("a17", new HashMap<String, Integer>() {
                    {
                        put(DEFAULT_LENGTH, 2);
                        put(TOTAL_LENGTH, 10);
                    }
                });
                put("mtmmImpDeptName", new HashMap<String, Integer>() {
                    {
                        put(DEFAULT_LENGTH, 5);
                        put(TOTAL_LENGTH, 25);
                    }
                });
                put("mtmmInpDate", new HashMap<String, Integer>() {
                    {
                        put(DEFAULT_LENGTH, 5);
                        put(TOTAL_LENGTH, 13);
                    }
                });
                put("b38", new HashMap<String, Integer>() {
                    {
                        put(DEFAULT_LENGTH, 1);
                        put(TOTAL_LENGTH, 1);
                    }
                });
                put("b11c", new HashMap<String, Integer>() {
                    {
                        put(DEFAULT_LENGTH, 1);
                        put(TOTAL_LENGTH, 1);
                    }
                });
                put("b39", new HashMap<String, Integer>() {
                    {
                        put(DEFAULT_LENGTH, 1);
                        put(TOTAL_LENGTH, 1);
                    }
                });
                put("b13n", new HashMap<String, Integer>() {
                    {
                        put(DEFAULT_LENGTH, 5);
                        put(TOTAL_LENGTH, 11);
                    }
                });
                put("b21c", new HashMap<String, Integer>() {
                    {
                        put(DEFAULT_LENGTH, 5);
                        put(TOTAL_LENGTH, 8);
                    }
                });
                put("b16n", new HashMap<String, Integer>() {
                    {
                        put(DEFAULT_LENGTH, 5);
                        put(TOTAL_LENGTH, 12);
                    }
                });
                put("b20", new HashMap<String, Integer>() {
                    {
                        put(DEFAULT_LENGTH, 6);
                        put(TOTAL_LENGTH, 8);
                    }
                });
                put("c02n", new HashMap<String, Integer>() {
                    {
                        put(DEFAULT_LENGTH, 6);
                        put(TOTAL_LENGTH, 14);
                    }
                });
                put("c01c", new HashMap<String, Integer>() {
                    {
                        put(DEFAULT_LENGTH, 5);
                        put(TOTAL_LENGTH, 16);
                    }
                });
                put("c36n", new HashMap<String, Integer>() {
                    {
                        put(DEFAULT_LENGTH, 5);
                        put(TOTAL_LENGTH, 14);
                    }
                });
                put("c35c", new HashMap<String, Integer>() {
                    {
                        put(DEFAULT_LENGTH, 7);
                        put(TOTAL_LENGTH, 9);
                    }
                });
                put("c42", new HashMap<String, Integer>() {
                    {
                        put(DEFAULT_LENGTH, 2);
                        put(TOTAL_LENGTH, 3);
                    }
                });
                put("c43", new HashMap<String, Integer>() {
                    {
                        put(DEFAULT_LENGTH, 2);
                        put(TOTAL_LENGTH, 3);
                    }
                });
                put("c44", new HashMap<String, Integer>() {
                    {
                        put(DEFAULT_LENGTH, 2);
                        put(TOTAL_LENGTH, 3);
                    }
                });
                put("c28", new HashMap<String, Integer>() {
                    {
                        put(DEFAULT_LENGTH, 2);
                        put(TOTAL_LENGTH, 3);
                    }
                });
                put("c29", new HashMap<String, Integer>() {
                    {
                        put(DEFAULT_LENGTH, 2);
                        put(TOTAL_LENGTH, 3);
                    }
                });
                put("c30", new HashMap<String, Integer>() {
                    {
                        put(DEFAULT_LENGTH, 2);
                        put(TOTAL_LENGTH, 3);
                    }
                });
                put("c31", new HashMap<String, Integer>() {
                    {
                        put(DEFAULT_LENGTH, 2);
                        put(TOTAL_LENGTH, 3);
                    }
                });
                put("c32", new HashMap<String, Integer>() {
                    {
                        put(DEFAULT_LENGTH, 2);
                        put(TOTAL_LENGTH, 3);
                    }
                });
                put("c33", new HashMap<String, Integer>() {
                    {
                        put(DEFAULT_LENGTH, 2);
                        put(TOTAL_LENGTH, 3);
                    }
                });
                put("c45", new HashMap<String, Integer>() {
                    {
                        put(DEFAULT_LENGTH, 2);
                        put(TOTAL_LENGTH, 16);
                    }
                });
                put("c46", new HashMap<String, Integer>() {
                    {
                        put(DEFAULT_LENGTH, 8);
                        put(TOTAL_LENGTH, 16);
                    }
                });
                put("c47", new HashMap<String, Integer>() {
                    {
                        put(DEFAULT_LENGTH, 3);
                        put(TOTAL_LENGTH, 16);
                    }
                });
                put("b44", new HashMap<String, Integer>() {
                    {
                        put(DEFAULT_LENGTH, 2);
                        put(TOTAL_LENGTH, 7);
                    }
                });
                put("b45", new HashMap<String, Integer>() {
                    {
                        put(DEFAULT_LENGTH, 2);
                        put(TOTAL_LENGTH, 7);
                    }
                });
                put("b46", new HashMap<String, Integer>() {
                    {
                        put(DEFAULT_LENGTH, 2);
                        put(TOTAL_LENGTH, 7);
                    }
                });
                put("b47", new HashMap<String, Integer>() {
                    {
                        put(DEFAULT_LENGTH, 2);
                        put(TOTAL_LENGTH, 7);
                    }
                });
                put("b34c", new HashMap<String, Integer>() {
                    {
                        put(DEFAULT_LENGTH, 1);
                        put(TOTAL_LENGTH, 1);
                    }
                });
                put("b49", new HashMap<String, Integer>() {
                    {
                        put(DEFAULT_LENGTH, 10);
                        put(TOTAL_LENGTH, 10);
                    }
                });
                put("b48", new HashMap<String, Integer>() {
                    {
                        put(DEFAULT_LENGTH, 10);
                        put(TOTAL_LENGTH, 10);
                    }
                });
                put("b36c", new HashMap<String, Integer>() {
                    {
                        put(DEFAULT_LENGTH, 1);
                        put(TOTAL_LENGTH, 1);
                    }
                });
                put("b37", new HashMap<String, Integer>() {
                    {
                        put(DEFAULT_LENGTH, 14);
                        put(TOTAL_LENGTH, 15);
                    }
                });
                put("b52n", new HashMap<String, Integer>() {
                    {
                        put(DEFAULT_LENGTH, 10);
                        put(TOTAL_LENGTH, 11);
                    }
                });
                put("b51c", new HashMap<String, Integer>() {
                    {
                        put(DEFAULT_LENGTH, 10);
                        put(TOTAL_LENGTH, 11);
                    }
                });
                put("d35", new HashMap<String, Integer>() {
                    {
                        put(DEFAULT_LENGTH, 7);
                        put(TOTAL_LENGTH, 7);
                    }
                });
                put("d38", new HashMap<String, Integer>() {
                    {
                        put(DEFAULT_LENGTH, 10);
                        put(TOTAL_LENGTH, 10);
                    }
                });
                put("d39", new HashMap<String, Integer>() {
                    {
                        put(DEFAULT_LENGTH, 10);
                        put(TOTAL_LENGTH, 10);
                    }
                });
                put("d58", new HashMap<String, Integer>() {
                    {
                        put(DEFAULT_LENGTH, 1);
                        put(TOTAL_LENGTH, 1);
                    }
                });
                put("d59", new HashMap<String, Integer>() {
                    {
                        put(DEFAULT_LENGTH, 6);
                        put(TOTAL_LENGTH, 16);
                    }
                });
                put("d60", new HashMap<String, Integer>() {
                    {
                        put(DEFAULT_LENGTH, 7);
                        put(TOTAL_LENGTH, 17);
                    }
                });
                put("medInsOrgan", new HashMap<String, Integer>() {
                    {
                        put(DEFAULT_LENGTH, 6);
                        put(TOTAL_LENGTH, 6);
                    }
                });
                put("medInsOrganOperator", new HashMap<String, Integer>() {
                    {
                        put(DEFAULT_LENGTH, 7);
                        put(TOTAL_LENGTH, 7);
                    }
                });
                put("d54", new HashMap<String, Integer>() {
                    {
                        put(DEFAULT_LENGTH, 7);
                        put(TOTAL_LENGTH, 7);
                    }
                });
                put("d55", new HashMap<String, Integer>() {
                    {
                        put(DEFAULT_LENGTH, 7);
                        put(TOTAL_LENGTH, 7);
                    }
                });
                put("d56", new HashMap<String, Integer>() {
                    {
                        put(DEFAULT_LENGTH, 7);
                        put(TOTAL_LENGTH, 7);
                    }
                });
                put("d57", new HashMap<String, Integer>() {
                    {
                        put(DEFAULT_LENGTH, 7);
                        put(TOTAL_LENGTH, 7);
                    }
                });
            }
        };
//        Map<String, Integer> map = new HashMap<String, Integer>(){
//            {
//                put("a58",15);
//                put("a02",10);
//                put("a01",12);
//                put("a50",4);
//                put("a51",18);
//                put("a48",15);
//                put("a11",7);
//                put("a12c",1);
//                put("a14",5);
//                put("a15c",7);
//                put("a16",4);
//                put("a19c",6);
//                put("a53",7);
//                put("a20",5);
//                put("a38c",9);
//                //现住址
//                put("a29n",9);
//                put("a29",22);
//                put("a30",11);
//                put("a31c",11);
//                put("a32",7);
//                put("a33c",4);
//                put("a35",11);
//                put("a54",11);
//                put("a55",7);
//                put("a56",8);
//                put("a57",10);
//                put("mulNwbBirWt",6);
//                put("mulNwbAdmWt",6);
//
//                put("mtmmImpDeptName",11);
//                put("mtmmInpDate",11);
//                put("b38",1);
//                put("b11c",1);
//                put("b39",1);
//                put("b13n",13);
//                put("b21c",6);
//                put("b16n",13);
//                put("b20",6);
//                put("c02n",20);
//                put("c01c",7);
//                put("c36n",20);
//                put("c35c",7);
//                put("c42",6);
//                put("c43",6);
//                put("c44",6);
//                put("c28",6);
//                put("c29",6);
//                put("c30",6);
//                put("c31",6);
//                put("c32",6);
//                put("c33",6);
//                put("c45",8);
//                put("c46",8);
//                put("c47",10);
//                put("b44",4);
//                put("b45",4);
//                put("b46",4);
//                put("b47",4);
//                put("b34c",1);
//                put("b49",15);
//                put("b48",10);
//                put("b36c",1);
//                put("b37",14);
//                put("b52n",10);
//                put("b51c",10);
//
//                put("d35",7);
//                put("d38",10);
//                put("d39",10);
//                put("d58",6);
//                put("d59",12);
//                put("d60",14);
//                put("medInsOrgan",6);
//                put("medInsOrganOperator",7);
//                put("d54",7);
//                put("d55",7);
//                put("d56",7);
//                put("d57",7);
//            }
//        };
        return map;
    }

    private HashMap<String, Map<String, Integer>> getAddressMap2(String PROVINCE, String CITY, String COUNTY, String DETAIL_ADRESS) {
        HashMap<String, Map<String, Integer>> addressMap2 = new HashMap<String, Map<String, Integer>>() {
            {
                put("a34", new HashMap<String, Integer>() {
                    {
                        put(PROVINCE, 2);
                        put(CITY, 2);
                        put(COUNTY, 2);
                        put(DETAIL_ADRESS, 5);
                        put("a34" + LENGTH, 2);
                    }
                });
                put("a26", new HashMap<String, Integer>() {
                    {
                        put(PROVINCE, 3);
                        put(CITY, 3);
                        put(COUNTY, 3);
                        put(DETAIL_ADRESS, 6);
                        put("a26" + LENGTH, 1);
                    }
                });
            }
        };
        return addressMap2;
    }

    private HashMap<String, Map<String, Integer>> getAddressMap(String PROVINCE, String CITY, String COUNTY, String DETAIL_ADRESS) {
        HashMap<String, Map<String, Integer>> addressMap = new HashMap<String, Map<String, Integer>>() {
            {
                put("a26", new HashMap<String, Integer>() {
                    {
                        put(PROVINCE, 5);
                        put(CITY, 5);
                        put(COUNTY, 5);
                        put(DETAIL_ADRESS, 14);
                        put("a26" + LENGTH, 14);
                    }
                });
            }
        };
        return addressMap;
    }

    private HashMap<String, Map<String, Integer>> getTimeMap(String YEAR, String MONTH, String DAY, String HOUR) {
        HashMap<String, Map<String, Integer>> timeMap = new HashMap<String, Map<String, Integer>>() {
            {
                put("a52", new HashMap<String, Integer>() {
                    {
                        put(YEAR, 1);
                        put(MONTH, 1);
                        put(DAY, 1);
                        put("a52" + LENGTH, 1);
                    }
                });
                put("a13", new HashMap<String, Integer>() {
                    {
                        put(YEAR, 3);
                        put(MONTH, 3);
                        put(DAY, 3);
                        put("a13" + LENGTH, 2);
                    }
                });
                put("b12", new HashMap<String, Integer>() {
                    {
                        put(YEAR, 2);
                        put(MONTH, 1);
                        put(DAY, 1);
                        put(HOUR, 1);
                        put("b12" + LENGTH, 3);
                    }
                });
                put("b15", new HashMap<String, Integer>() {
                    {
                        put(YEAR, 2);
                        put(MONTH, 1);
                        put(DAY, 1);
                        put(HOUR, 1);
                        put("b15" + LENGTH, 3);
                    }
                });
                put("d36", new HashMap<String, Integer>() {
                    {
                        put(YEAR, 2);
                        put(MONTH, 1);
                        put(DAY, 1);
                        put("d36" + LENGTH, 1);
                    }
                });
                put("d37", new HashMap<String, Integer>() {
                    {
                        put(YEAR, 2);
                        put(MONTH, 1);
                        put(DAY, 1);
                        put("d37" + LENGTH, 1);
                    }
                });
            }
        };
        return timeMap;
    }

    private void joinBlank(StringBuilder str, int len, String s) {
        for (int i = 0; i < len; i++) {
            str.append(s);
        }
    }

    private String getEmpty(int len) {
        StringBuilder str = new StringBuilder();
        joinBlank(str, len, "　");
        return str.toString();
    }

    /**
     * 模拟预分组诊断数据(code)
     * @param query
     * @return
     */
    @Override
    public List<SomDiagInfo> queryDiagInfo(String query) {
//        List<SomDiagInfo> diagInfoList = (List<SomDiagInfo>)RedisUtils.get(DIAG_INFO_KEY);
//        if (diagInfoList == null) {
//            diagInfoList = settleListManageDao.queryDiagInfo(query);
//            if (diagInfoList != null && !diagInfoList.isEmpty()) {
//                RedisUtils.set(DIAG_INFO_KEY, diagInfoList);
//            }
//        }
        if(!ValidateUtil.isEmpty(query)){
        List<SomDiagInfo> diagInfoList = settleListManageDao.queryDiagInfo(query);
        return diagInfoList;
    }
        return null;
    }

    @Override
    public List<SomDiagInfo> queryDiagNameInfo(String query) {
        if(!ValidateUtil.isEmpty(query)){
        List<SomDiagInfo> diagInfoList = settleListManageDao.queryDiagNameInfo(query);
        return diagInfoList;
        }
        return null;
    }

    /**
     * 模拟预分组手术数据(code)
     * @param query
     * @return
     */
    @Override
    public List<SomOprnInfo> queryOprnInfo(String query){
        if(!ValidateUtil.isEmpty(query)){
            return settleListManageDao.queryOprnInfo(query);
        }
        return null;
    }

    @Override
    public List<SomOprnInfo> queryOprnNameInfo(String query){
        if(!ValidateUtil.isEmpty(query)){
            return settleListManageDao.queryOprnNameInfo(query);
        }
        return null;
    }

    @Override
    public String queryHosInfo(){
        return settleListManageDao.queryHosInfo();
    }

    @Override
    public PreGroupVo getSimlatePreGroup(PatienInfo patienInfo, HttpServletRequest request, HttpServletResponse response) {
        //基本信息映射
        PreGroup2Base preGroup2Base = new PreGroup2Base();
        preGroup2Base.setAge(patienInfo.getAge());
        preGroup2Base.setIpt_days(patienInfo.getHosDays());
        preGroup2Base.setGend(patienInfo.getSex());
        preGroup2Base.setNwb_bir_wt(patienInfo.getCstz());
        preGroup2Base.setNwb_adm_wt(patienInfo.getRytz());
        preGroup2Base.setNwb_age(patienInfo.getDays());
        preGroup2Base.setHi_type(patienInfo.getCanBaoType());
        preGroup2Base.setFixmedins_code(patienInfo.getHosId());
        preGroup2Base.setDscg_way(patienInfo.getLyfs());
        preGroup2Base.setInsuplc(patienInfo.getInsuplc());
        preGroup2Base.setInsurancePlace(patienInfo.getInsuplc());
        preGroup2Base.setVent_used_dura(patienInfo.getHxjHours());
        if(patienInfo.getZyFee() != null) {
            BigDecimal mdeFee = new BigDecimal(patienInfo.getZyFee().toString());
            //dip传参需要住院费
            preGroup2Base.setMedfee_sumamt(mdeFee);
        }
//        preGroup2Base.setHi_type("390");

        //实体类映射
        PreGroup2Dto preGroup2Dto = new PreGroup2Dto();


        PreGroup2Icu preGroup2Icu = new PreGroup2Icu();

        List<PreGroup2Dis> disList = new ArrayList<>();
        List<PreGroup2Ope> oprnsList = new ArrayList<>();
        List<PreGroup2Icu> icuList = new ArrayList<>();
        //诊断
        for(int i = 0;i < patienInfo.getDiagCodes().size();i++){
            PreGroup2Dis preGroup2Dis = new PreGroup2Dis();
            //疾病编码
            if(i == 0){
                preGroup2Dis.setMaindiag_flag("1");
            }else
                preGroup2Dis.setMaindiag_flag("0");
            preGroup2Dis.setDiag_code(patienInfo.getDiagCodes().get(i));
            disList.add(preGroup2Dis);
        }
        //手术
        for(int i = 0;i < patienInfo.getOprnsCode().size();i++){
            PreGroup2Ope preGroup2Ope = new PreGroup2Ope();
            if(i == 0){
                preGroup2Ope.setOprn_oprt_type("1");
            }else{
                preGroup2Ope.setOprn_oprt_type("0");
            }
            preGroup2Ope.setOprn_oprt_code(patienInfo.getOprnsCode().get(i));
//            preGroup2Ope.setOprn_oprt_date("2024-12-26 13:00:00");
            oprnsList.add(preGroup2Ope);
        }
        preGroup2Dto.setDiseinfo(disList);
        preGroup2Dto.setOprninfo(oprnsList);
        preGroup2Dto.setBaseinfo(preGroup2Base);
        //暂时只有一条icu信息（时长）
        preGroup2Icu.setIcu_time_sum_dura(patienInfo.getIcuHours());
        icuList.add(preGroup2Icu);
        preGroup2Dto.setIcuinfo(icuList);
        preGroup2Dto.setIsWeb("1");
        preGroup2Dto.setSkippingValidation(true);

        //获取分组结果
        PreGroupVo groups =  preGroupServiceInterface.getPreGroupInfo5(preGroup2Dto, response, request);
        return groups;

    }

    @Override
    public DataGroupInfo preGroupResult(PatienInfoByHis patienInfo) {
        checkDataEnable(patienInfo);
        convertToStandradParam(patienInfo);
        List<HospitalVo> hospitalVoList =  getHospitalVo(patienInfo);
        if (DrgConst.GROUP_TYPE_NAME_DIP.equals(SysCommonConfigUtil.get(DrgConst.FZLX))) {
            //获取到分组器的url
            HospitalVo dipBuster = hospitalVoList.stream().filter(hospitalVo -> hospitalVo.getGrperType().equals(DrgConst.GROUP_TYPE_DIP)).findAny().get();
            groupByDIP(patienInfo, dipBuster);
            return  groupInfoMapper.selectPreGroupDIPResult(patienInfo);
        } else if (DrgConst.GROUP_TYPE_NAME_DRG.equals(SysCommonConfigUtil.get(DrgConst.FZLX))) {
            //获取到分组器的url
            HospitalVo drgBuster = hospitalVoList.stream()
                    .filter(hospitalVo -> hospitalVo.getGrperType().equals(DrgConst.GROUP_TYPE_DRG)
                            && hospitalVo.getInsuplcAdmdvs().equals(patienInfo.getInsuplc_admdvs()))
                    .findAny().get();
            groupByDRG(patienInfo, drgBuster);
          return  groupInfoMapper.selectPreGroupDRGResult(patienInfo);
        }
        return null;
    }


    private void groupByDRG(PatienInfoByHis patienInfo, HospitalVo dipBuster) {
        CaseYB2020Vo caseYB2020Vo = getCaseYB2020Vo(patienInfo);
        getDrgStandInfo(patienInfo, dipBuster, caseYB2020Vo);
    }

    private static void getDrgStandInfo(PatienInfoByHis patienInfo, HospitalVo dipBuster, CaseYB2020Vo caseYB2020Vo) {
        Object drgGrouperVerObj = SysCommonConfigUtil.get(DrgConst.DRG_GROUPER_VER);
        String postJson = null ;
        if (drgGrouperVerObj != null && DrgConst.DRG_GROUPER_VER_V10.equals(drgGrouperVerObj.toString())) {
            // 老版分组参数,生成SomDrgGrpRcd
            postJson = parseDrgsJsonV10(caseYB2020Vo);
        } else {

            //新版分组参数，生成SomDrgGrpRcd
            postJson = parseDrgsJsonV20(caseYB2020Vo);
        }
        SomDrgGrpRcd sdgr;
        String groupUrl = dipBuster.getGroupUrl();
        String drgsContent = HttpUtilYbDrgGroup.post(postJson, groupUrl);
        if (ValidateUtil.isEmpty(drgsContent) || "ERROR".equals(drgsContent)) {
            throw new AppException("网络异常,URL:" + groupUrl);
        } else {
            if (drgGrouperVerObj != null && DrgConst.DRG_GROUPER_VER_V10.equals(drgGrouperVerObj.toString())) {
                // 老版分组参数,生成SomDrgGrpRcd
                sdgr = generateDrgGrpRcdV10(drgsContent, caseYB2020Vo);
            } else {
                //新版分组参数，生成SomDrgGrpRcd
                sdgr = generateDrgGrpRcdV20(drgsContent, caseYB2020Vo);
            }
        }
        patienInfo.setGroup_code (sdgr.getDrgCodg());
        patienInfo.setGroup_name(sdgr.getDrgName());
    }

    private static CaseYB2020Vo getCaseYB2020Vo(PatienInfoByHis patienInfo) {
        CaseYB2020Vo caseYB2020Vo = new CaseYB2020Vo();
        caseYB2020Vo.setB20(String.valueOf(patienInfo.getIpt_days()));
        caseYB2020Vo.setD01(patienInfo.getMedfee_sumamt());
        caseYB2020Vo.setA14(patienInfo.getAge());
        caseYB2020Vo.setA16(patienInfo.getNwb_age());
        caseYB2020Vo.setA17(patienInfo.getNwb_adm_wt());
        caseYB2020Vo.setB34c(patienInfo.getDscg_way());
        caseYB2020Vo.setA12c(patienInfo.getGend());
        caseYB2020Vo.setDiseinfo(patienInfo.getDiseinfo());
        caseYB2020Vo.setOprninfo(patienInfo.getOprninfo());
        String icuCareDura = patienInfo.getIcu_care_dura();
        if(!ValidateUtil.isEmpty(icuCareDura)){
            caseYB2020Vo.setIntensiveCareDuration(Double.parseDouble(icuCareDura));
        }

        caseYB2020Vo.setA48(patienInfo.getMedcasno());
        caseYB2020Vo.setA01(patienInfo.getFixmedins_code());
        caseYB2020Vo.setInsuplcAdmdvs(patienInfo.getInsuplc());
        return caseYB2020Vo;
    }


    private List<HospitalVo>  getHospitalVo(PatienInfoByHis patienInfo) {
        HospitalDrgQueryParam hospitalDrgQueryParam = new HospitalDrgQueryParam();
        hospitalDrgQueryParam.setHospital_id(patienInfo.getFixmedins_code());
        hospitalDrgQueryParam.setActive_flag(DrgConst.ACTIVE_FLAG_1);
        hospitalDrgQueryParam.setInsuplcAdmdvs(patienInfo.getInsuplc_admdvs());
        // 获取分组器信息
        return  groupInfoMapper.queryAllGroupInfo(hospitalDrgQueryParam);

    }

    private void convertToStandradParam(PatienInfoByHis patienInfo) {
        //处理手术和诊断编码
        List<PreDiseInfo> diseinfo  = patienInfo.getDiseinfo();
        diseinfo.stream().forEach(diseaseDiagnosis -> {
            String diagCode = convertCodeName(diseaseDiagnosis.getDiag_code());
            String diagCodeName = convertCodeName(diseaseDiagnosis.getDiag_name());
            converterAndSetValue(diagCode, diagCodeName, DrgConst.ICD10, diseaseDiagnosis,null, "1");
        });
        List<PreOprnInfo> oprninfo = patienInfo.getOprninfo();
        oprninfo.stream().forEach(oprn -> {
            String diagCode = convertCodeName(oprn.getOprn_oprt_code());
            String diagCodeName = convertCodeName(oprn.getOprn_oprt_name());
            converterAndSetValue(diagCode, diagCodeName, DrgConst.ICD10, null, oprn, "2");
        });
        //中江传的hhospital_id需要转换
        if("H51062300184".equals(patienInfo.getFixmedins_code())){
            patienInfo.setHospital_id("210021");
        }else{
            patienInfo.setHospital_id(patienInfo.getFixmedins_code());
        }
        //是否判断异地【5114、5199】，根据分组信息配置表获取统筹区
        //参保地
        String insuplc = patienInfo.getInsuplc();
        List<String> medicalInsuranceAreaList = commonService.getMedicalInsuranceArea(patienInfo.getFixmedins_code());
        if (ValidateUtil.isEmpty(insuplc)) {
            insuplc = (String) SysCommonConfigUtil.get(DrgConst.INSUPLC_ADMDVS_AREA);
        } else {
            if (insuplc.length() > 4) {
                insuplc = insuplc.substring(0, 4);
            }
        }
        //判断是否为异地
        if (ValidateUtil.isNotEmpty(medicalInsuranceAreaList) && !medicalInsuranceAreaList.contains(insuplc)) {
            //不在配置的医保统筹区范围内属于异地，更改为默认市医保统筹区
            insuplc = (String) SysCommonConfigUtil.get(DrgConst.INSUPLC_ADMDVS_AREA);
        }
        patienInfo.setInsuplc_admdvs(insuplc);
    }

    private static void checkDataEnable(PatienInfoByHis patienInfo) {
        if (ValidateUtil.isEmpty(patienInfo.getDiseinfo())) {
            throw new AppException("诊断编码不能");
        }
        if (ValidateUtil.isEmpty(patienInfo.getAge())) {
            throw new AppException("年龄不能为空");
        }
        if (ValidateUtil.isEmpty(patienInfo.getIpt_days())) {
            throw new AppException("住院天数不能为空");
        }
        if (ValidateUtil.isEmpty(patienInfo.getFixmedins_code())) {
            throw new AppException("定点医药机构编号不能为空");
        }
        if (ValidateUtil.isEmpty(patienInfo.getInsuplc())) {
            throw new AppException("参保地不能为空");
        }
        if (ValidateUtil.isEmpty(patienInfo.getDscg_date())) {
            throw new AppException("出院时间不能为空");
        }
    }

    private String convertCodeName(Object code) {
        return ValidateUtil.isEmpty(code) ? null : code.toString();
    }

    private void converterAndSetValue(String code,
                                      String codeName,
                                      String icdType,
                                      PreDiseInfo dise,
                                      PreOprnInfo oprn,
                                      String type) {
        if (ValidateUtil.isNotEmpty(code)) {
            ICDCodeVo vo = SysCommonConfigUtil.getIcdCodeMap(code);
            String ybCode = code;
            ICDCodeVo icdCodeVo = null;
            boolean setCode = false;

            if (vo != null) {
                if (ValidateUtil.isNotEmpty(vo.getIcdName())) {
                    if (checkcodeName(codeName, vo)) {
                        switch (type) {
                            case "1":
                                // som_diag 中的诊断编码设置
                                dise.setDiag_name(vo.getIcdName());
                                break;
                            case "2":
                                // som_oprn_oprt_info 中的诊断编码设置
                                oprn.setOprn_oprt_name(vo.getIcdName());
                                break;
                        }
                    } else {
                        icdCodeVo = CodeConverterUtil.defaultConverter(code, icdType);
                        setCode = true;
                    }
                }
            } else {
                icdCodeVo = CodeConverterUtil.defaultConverter(code, icdType);
                setCode = true;
            }

            if (icdCodeVo != null && ValidateUtil.isNotEmpty(icdCodeVo.getCrspIcdName())) {
                ybCode = icdCodeVo.getCrspIcdCodg();
                switch (type) {
                    case "1":
                        // som_diag 中的诊断编码设置
                        if (setCode) {
                            dise.setDiag_code(icdCodeVo.getCrspIcdCodg());
                        }
                        dise.setDiag_name(icdCodeVo.getCrspIcdName());
                        break;
                    case "2":
                        // som_oprn_oprt_info 中的诊断编码设置
                        if (setCode) {
                            oprn.setOprn_oprt_code(icdCodeVo.getCrspIcdCodg());
                        }
                        oprn.setOprn_oprt_name(icdCodeVo.getCrspIcdName());
                        break;

                }
            }

            //医保2.0名称转换 此时认为所有的编码都是医保2.0
            ICDCodeVo ybVo = SysCommonConfigUtil.getIcdCodeMap(ybCode);
            if (ybVo != null) {
                if (!ValidateUtil.isEmpty(ybVo.getIcdName())) {
                    switch (type) {
                        case "1":
                            // som_diag 中的诊断编码设置
                            dise.setDiag_name(ybVo.getIcdName());
                            break;
                        case "2":
                            // som_oprn_oprt_info 中的诊断编码设置
                            oprn.setOprn_oprt_name(ybVo.getIcdName());
                            break;

                    }
                }
            }

            //此处判断灰码转码
            Map<String, ICDCodeVo> grayMap = SysCommonConfigUtil.getIcdGrayCodeVoList(icdType);
            if (grayMap != null && !grayMap.isEmpty()) {

                switch (type) {
                    case "1":
                        if (grayMap.containsKey(dise.getDiag_code())) {
                            ICDCodeVo icdCodeVoCrspGrey = grayMap.get(dise.getDiag_code());
                            dise.setDiag_code(icdCodeVoCrspGrey.getCrspIcdCodg());
                            dise.setDiag_name(icdCodeVoCrspGrey.getCrspIcdName());
                        }
                        break;
                    case "2":
                        if (grayMap.containsKey(oprn.getOprn_oprt_code())) {
                            ICDCodeVo icdCodeVoCrspGrey = grayMap.get(oprn.getOprn_oprt_code());
                            oprn.setOprn_oprt_code(icdCodeVoCrspGrey.getCrspIcdCodg());
                            oprn.setOprn_oprt_name(icdCodeVoCrspGrey.getCrspIcdName());
                        }
                        break;
                }
            }
        }
    }


    private boolean checkcodeName(String codeName, ICDCodeVo vo) {
        if (!ValidateUtil.isEmpty(codeName)) {
            if (codeName.contains("(") || codeName.contains(")")) {
                // 如果包含括号，则替换
                codeName = codeName.replace("(", "（").replace(")", "）");
            }
        }
        return vo.getIcdName().equals(codeName);
    }

    private static void groupByDIP(PatienInfoByHis patienInfo, HospitalVo hospitalVo) {
        DipGroupDataInfoVo dipGroupDataInfoVo = getDipGroupDataInfoVo(patienInfo);
        Object drgGrouperVerObj = SysCommonConfigUtil.get(DrgConst.DIP_GROUPER_VER);
        if (drgGrouperVerObj != null && DrgConst.DIP_GROUPER_VER_V20.equals(drgGrouperVerObj.toString())) {
            preDIP20(patienInfo, hospitalVo, dipGroupDataInfoVo);
        } else {
            preDIP10(patienInfo, hospitalVo, dipGroupDataInfoVo);
        }
    }

    private static void preDIP10(PatienInfoByHis patienInfo, HospitalVo hospitalVo, DipGroupDataInfoVo dipGroupDataInfoVo) {
        // 分组器请求参数构造
        String request =  DipGroupUtil.createRequestInfo(dipGroupDataInfoVo);
        // 分组器调用
        HttpResponse res = HttpRequest.post(hospitalVo.getGroupUrl())
                .body(request)
                .execute();
        String result = res.body();
        DipGroupResponVO dipGroupResponVO = JSONObject.parseObject(result.toString(), DipGroupResponVO.class);
        if("0".equals(dipGroupResponVO.getGroupStatus())){
            patienInfo.setGrpFaleRea("不在支付体系中");
        }
        if(StringUtils.hasText(dipGroupResponVO.getAuxType()) && checkUsesAsstList()){
            patienInfo.setUsed_asst_list("1");
            String[] s = dipGroupResponVO.getAuxType().split("-");
            // A : 年龄分型；D:严重程度分型；C: CCI分型；T:肿瘤分型
            if(s[0].equals("A")){
                patienInfo.setAuxiliary_age(dipGroupResponVO.getAuxType());
            }else if(s[0].equals("D")){
                patienInfo.setAuxiliary_illness(dipGroupResponVO.getAuxType());
            }else if(s[0].equals("T")){
                patienInfo.setAuxiliary_tumour(dipGroupResponVO.getAuxType());
            }
        } else {
            patienInfo.setUsed_asst_list("0");
            patienInfo.setAuxiliary_age("未使用");
            patienInfo.setAuxiliary_illness("未使用");
            patienInfo.setAuxiliary_tumour("未使用");
            patienInfo.setAuxiliary_burn("未使用");
        }
    }

    private static void preDIP20(PatienInfoByHis patienInfo, HospitalVo hospitalVo, DipGroupDataInfoVo dipGroupDataInfoVo) {
        String json = DipGroupUtil.createParam(dipGroupDataInfoVo, DrgConst.GROUP_TYPE_DIP);
//        System.out.println("分组器参数：" +json);
        String response = HttpGroupUtil.post(json, hospitalVo.getGroupUrl());
        if (ValidateUtil.isEmpty(response) || response.equals(DrgConst.RESULT_ERROR)) {
        throw new AppException("网络异常");
    } else {
      DipGroupResponseVo responseVo = JSON.parseObject(response, DipGroupResponseVo.class);
       if (DrgConst.GROUP_RESPONSE_CODE_LOST_1.equals(responseVo.get_State__code())) {
            throw new AppException(responseVo.get_State__message());
       } else {
           patienInfo.setGroup_code (responseVo.get_State__data().getDisease_code());
           patienInfo.setGroup_name(responseVo.get_State__data().getDisease_name());
           patienInfo.setAuxiliary_age(responseVo.get_State__data().getAuxiliary_age());
           patienInfo.setAuxiliary_illness(responseVo.get_State__data().getAuxiliary_illness());
           patienInfo.setAuxiliary_tumour(responseVo.get_State__data().getAuxiliary_tumour());
           String burn =responseVo.get_State__data().getAuxiliary_burn();
           patienInfo.setAuxiliary_burn(ValidateUtil.isEmpty(burn)?"未使用":burn);
       }
    }
    }

    private static DipGroupDataInfoVo getDipGroupDataInfoVo(PatienInfoByHis patienInfo) {
        DipGroupDataInfoVo dipGroupDataInfoVo =new DipGroupDataInfoVo();
        dipGroupDataInfoVo.setC40c(patienInfo.getAge());
        dipGroupDataInfoVo.setC41c(patienInfo.getDscg_way());
        dipGroupDataInfoVo.setC42c(String.valueOf(patienInfo.getIpt_days()));
        dipGroupDataInfoVo.setD01c(String.valueOf(patienInfo.getMedfee_sumamt()));
        dipGroupDataInfoVo.setK00(patienInfo.getMedcasno());
        List<PreDiseInfo> diseinfo = patienInfo.getDiseinfo();
        List<String> otherDises = new ArrayList<>();
        diseinfo.stream().forEach(diseaseDiagnosis -> {
            //主要诊断
            if ("1".equals(diseaseDiagnosis.getMaindiag_flag())) {
                dipGroupDataInfoVo.setDscg_diag_codg(diseaseDiagnosis.getDiag_code());
                dipGroupDataInfoVo.setDscg_diag_name(diseaseDiagnosis.getDiag_name());
                }
            else { otherDises.add(diseaseDiagnosis.getDiag_code());  }
        });
        //其他诊断
        dipGroupDataInfoVo.setC06c_q(otherDises);


        List<PreOprnInfo> oprninfo = patienInfo.getOprninfo();
        List<String> otherOprninfos = new ArrayList<>();
        if(!ValidateUtil.isEmpty(oprninfo)){
            oprninfo.stream().forEach(oprn -> {
                //主要诊断
                if ("1".equals(oprn.getOprn_oprt_type())) {
                    dipGroupDataInfoVo.setC35c_0(oprn.getOprn_oprt_code());
                }
                else {otherOprninfos.add(oprn.getOprn_oprt_code()); }
            });
            for (int i = 0; i < 6; i++) {
                String value = (i < otherOprninfos.size() && !ValidateUtil.isEmpty(otherOprninfos.get(i)))
                        ? otherOprninfos.get(i)
                        : "-";
                // Dynamically set the value based on index
                switch (i) {
                    case 0: dipGroupDataInfoVo.setC35c_1(value); break;
                    case 1: dipGroupDataInfoVo.setC35c_2(value); break;
                    case 2: dipGroupDataInfoVo.setC35c_3(value); break;
                    case 3: dipGroupDataInfoVo.setC35c_4(value); break;
                    case 4: dipGroupDataInfoVo.setC35c_5(value); break;
                    case 5: dipGroupDataInfoVo.setC35c_6(value); break;
                }
            }
        }
        return dipGroupDataInfoVo;
    }

    private static String createParam(PatienInfoByHis patienInfo, String groupTypeDip) {
        HashMap<Object, Object> map = new HashMap<>();
        map.put("type", groupTypeDip);
        HashMap<Object, Object> data = new HashMap<>();
        List<PreDiseInfo> diseinfo = patienInfo.getDiseinfo();
        List<String> otherDises = new ArrayList<>();
        diseinfo.stream().forEach(diseaseDiagnosis -> {
            //主要诊断
            if ("1".equals(diseaseDiagnosis.getMaindiag_flag())) { data.put("c06c",diseaseDiagnosis.getDiag_code()); }
            else { otherDises.add(diseaseDiagnosis.getDiag_code());  }
        });
        //其他诊断
        data.put("c06c_q",otherDises);
        List<PreOprnInfo> oprninfo = patienInfo.getOprninfo();
        List<String> otherOprninfos = new ArrayList<>();
        if(!ValidateUtil.isEmpty(oprninfo)){
            oprninfo.stream().forEach(oprn -> {
                //主要诊断
                if ("1".equals(oprn.getOprn_oprt_type())) { data.put("c35c_0",oprn.getOprn_oprt_code()); }
                else {otherOprninfos.add(oprn.getOprn_oprt_code()); }
            });
            for (int i = 1; i < 7; i++) {
                data.put("c35c_"+i,ValidateUtil.isEmpty(otherOprninfos.get(i-1)) ? "-" : otherOprninfos.get(i-1));
            }
        }

        //年龄
        data.put("c40c",DipGroupUtil.parseStringToInteger(patienInfo.getAge()));
        //住院天数
        data.put("c41c",patienInfo.getIpt_days());
        //离院方式
        Integer dscg_way = DipGroupUtil.parseStringToInteger(patienInfo.getDscg_way());
        data.put("c42c",ValidateUtil.isEmpty(dscg_way)?1:dscg_way);
        data.put("medcasno",patienInfo.getMedcasno());
        map.put("data",data);
        return  ((JSONObject) JSONObject.toJSON(map)).toJSONString();
    }


    @Override
    public  CommonPage<BusSettleListMainInfo> querySpecialDisease(SettleListMainInfoQueryParam queryParam, Integer pageSize, Integer pageNum) {

        List<BusSettleListMainInfo> busSettleListMainInfos = settleListManageDao.querySpecialDisease(queryParam);

        List<BusSettleListMainInfo> scoreList = paymentForecastLiteFlowService.querySpecialDisease(busSettleListMainInfos, queryParam);
        CommonPage<BusSettleListMainInfo> busSettleListMainInfoCommonPage = CommonPage.restPage(scoreList);

        int totalItems = scoreList.size();
        int fromIndex = (pageNum - 1) * pageSize;
        int toIndex = Math.min(fromIndex + pageSize, totalItems);

        // 如果起始索引超出范围，返回空列表
        if (fromIndex >= totalItems) {
            busSettleListMainInfoCommonPage.setList(Collections.emptyList());
        } else {
            busSettleListMainInfoCommonPage.setList(scoreList.subList(fromIndex, toIndex));
        }
        return busSettleListMainInfoCommonPage;
    }

    @Override
    public String getSpecialDiseaseType() {
            Object specialDiseasePlace = SysCommonConfigUtil.get(DrgConst.INSUPLC_ADMDVS_AREA);
            if (specialDiseasePlace != null) {
                return specialDiseasePlace.toString();
            }
            return null;
        }



}

