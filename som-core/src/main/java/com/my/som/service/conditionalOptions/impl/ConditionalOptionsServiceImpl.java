package com.my.som.service.conditionalOptions.impl;

import com.my.som.dto.conditionalOptions.ConditionalOptionsDto;
import com.my.som.mapper.conditionalOptions.ConditionalOptionsMapper;
import com.my.som.service.conditionalOptions.ConditionalOptionsService;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;

public class ConditionalOptionsServiceImpl implements ConditionalOptionsService {

    @Autowired
    private ConditionalOptionsMapper conditionalOptionsMapper;
    /**
     * 根据条件查询数据
     * @param dto
     */
    @Override
    public void getData(ConditionalOptionsDto dto) {
//        List<String> list = new ArrayList<>();
//        list.add("a");
//        list.add("b");
//        ConditionalOptionsDto dto1 = new ConditionalOptionsDto();
//        dto1.setQueryConditions(list);
//        int data = conditionalOptionsMapper.getData(dto1);
    }
}
