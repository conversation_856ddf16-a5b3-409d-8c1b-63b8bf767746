package com.my.som.service.medicalQuality;


import com.my.som.dto.medicalQuality.DiseaseGroupAnalysisDto;
import com.my.som.vo.medicalQuality.DiseaseGroupAnalysisVo;

import java.util.List;

public interface DiseaseGroupAnalysisService {

    /**
     * 获取人员信息
     * @param dto
     * @return
     */
    List<DiseaseGroupAnalysisVo> getList(DiseaseGroupAnalysisDto dto, Integer pageSize, Integer pageNum);
    /**
     * 获取基本信息
     * @param dto
     * @return
     */
    List<DiseaseGroupAnalysisVo> getInfo(DiseaseGroupAnalysisDto dto);
    /**
     * 获取基本信息
     * @param dto
     * @return
     */
    List<DiseaseGroupAnalysisVo> getIsInGroup(DiseaseGroupAnalysisDto dto);

    /**
     * 获取费用信息
     * @param dto
     * @return
     */
    List<DiseaseGroupAnalysisVo> getCostPay(DiseaseGroupAnalysisDto dto);
    /**
     * 获取费用信息
     * @param dto
     * @return
     */
    List<DiseaseGroupAnalysisVo> getCost(DiseaseGroupAnalysisDto dto);

}
