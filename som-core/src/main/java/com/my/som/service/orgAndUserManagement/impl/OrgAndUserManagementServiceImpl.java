package com.my.som.service.orgAndUserManagement.impl;

import cn.hutool.core.util.StrUtil;
import com.my.som.common.constant.DrgConst;
import com.my.som.common.exception.AppException;
import com.my.som.common.util.ChineseCharToEnUtil;
import com.my.som.common.util.ValidateUtil;
import com.my.som.common.vo.CodeConst;
import com.my.som.common.vo.SomBackUser;
import com.my.som.common.vo.SysUserBase;
import com.my.som.common.vo.SomUserRole;
import com.my.som.dao.orgAndUserManagement.OrgAndUserManagementDao;
import com.my.som.dao.sys.SomBackUserMapper;
import com.my.som.dao.sys.SomUserRoleMapper;
import com.my.som.dto.orgAndUserManagement.OrgAndUserManagementParam;
import com.my.som.mapper.common.SomDeptMapper;
import com.my.som.model.common.SomDept;
import com.my.som.model.common.SomDeptExample;
import com.my.som.model.orgAndUserManagement.SomBasDept;
import com.my.som.model.orgAndUserManagement.BaseOrgNode;
import com.my.som.service.orgAndUserManagement.OrgAndUserManagementService;
import com.my.som.util.SysCommonConfigUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @author: zyd
 * Case:          drg
 * FileName:      OrgAndUserManagementService
 * Date:          2020/1/9 10:26
 * Description:   组织机构管理 Service
 * Ver:       1.0
 * Copyright (C),2012-2022 Freedom
 */
@Service
@Transactional
public class OrgAndUserManagementServiceImpl implements OrgAndUserManagementService {

    @Autowired
    private PasswordEncoder passwordEncoder;

    @Autowired
    private SomUserRoleMapper sysUserRoleMapper;

    @Autowired
    private SomBackUserMapper sysUserMapper;

    @Autowired
    private OrgAndUserManagementDao orgAndUserManagementDao;

    @Autowired
    private SomDeptMapper busDeptMapper;

    @Override
    public List<BaseOrgNode> getListTree(String s) {
        List<BaseOrgNode> resultList = new ArrayList<>();
        OrgAndUserManagementParam param = new OrgAndUserManagementParam();
        param.setHospitalId(s);
        List<SomBasDept> listOrg = orgAndUserManagementDao.getOrgNodeList(param);
        List<BaseOrgNode> topOrgList = new ArrayList<>();
        List<BaseOrgNode> allOrgList = new ArrayList<>();
        listOrg.forEach(somBasDept -> {
            BaseOrgNode orgNodeByBaseOrg = getOrgNodeByBaseOrg(somBasDept);
            if(ValidateUtil.isEmpty(somBasDept.getPrntDeptId())){
                topOrgList.add(orgNodeByBaseOrg);
            };
            allOrgList.add(orgNodeByBaseOrg);
        });
        findChildren(topOrgList, allOrgList);
//        List<BaseOrgNode> nodeList = new ArrayList<>();
//        Iterator<SomBasDept> iteratorOne = listOrg.iterator();
//        int listSize = listOrg.size();
//        SomBasDept somBasDept , subBaseOrg;
//        JSONObject orgNodes = new JSONObject();
//        BaseOrgNode rootOrgNode = new BaseOrgNode();
//        BaseOrgNode itemOrgNode,childrenOrgNode;
//        List<BaseOrgNode> childrenList;
//        while (iteratorOne.hasNext()){
//            somBasDept = iteratorOne.next();
//            itemOrgNode = getOrgNodeByBaseOrg(somBasDept);
//            childrenList =  new ArrayList<>();
//            if(ValidateUtil.isEmpty(somBasDept.getPrntDeptId())){
//                rootOrgNode = itemOrgNode;
//            }
//            for(int i = 0;i<listSize;i++){
//                subBaseOrg = listOrg.get(i);
//                if(subBaseOrg.getPrntDeptId().equals(somBasDept.getOrgId())){
//                    childrenOrgNode = getOrgNodeByBaseOrg(subBaseOrg);
//                    childrenList.add(childrenOrgNode);
//                }
//            }
//            itemOrgNode.setChildren(childrenList);
//            nodeList.add(itemOrgNode);
//        }
//        resultList.add(getNodeTreeObject(rootOrgNode,nodeList));
        return topOrgList;
    }

    private void findChildren(List<BaseOrgNode> topOrgList, List<BaseOrgNode> listOrg) {
        for (BaseOrgNode somBasDept : topOrgList) {
            List<BaseOrgNode> children = new ArrayList<>();
            for (BaseOrgNode org : listOrg) {
                if (org.getPrntDeptId().equals(somBasDept.getOrgId())) {
                    children.add(org);
                }
            }

            if (ValidateUtil.isNotEmpty(children)) {
                somBasDept.setChildren(children);
                findChildren(children, listOrg);
            }
        }
    }

    @Transactional
    @Override
    public BaseOrgNode addOrg(SomBasDept somBasDept, SomBackUser somBackUser) throws AppException {
        //todo 切换为使用类型判断
        if(ValidateUtil.isEmpty(somBasDept.getOrgId()) && somBasDept.getOrgName().contains("医院")){
            String hospitalId = busDeptMapper.selectHospital(somBasDept.getOrgName());
            if(ValidateUtil.isEmpty(hospitalId)){
                throw new AppException("未查询到当前医院信息");
            }
            somBasDept.setOrgId(hospitalId);
        }
        if(!ValidateUtil.isEmpty(somBasDept.getOrgId())){
            SomDeptExample example = new SomDeptExample();
            //example.createCriteria().andTypeEqualTo(Integer.valueOf(DrgConst.DEPT_TYPE_2));
            example.createCriteria().andCodeEqualTo(somBasDept.getOrgId());
            List<SomDept> listDept = busDeptMapper.selectByExample(example);
            if(!ValidateUtil.isEmpty(listDept)&&!ValidateUtil.isEmpty(listDept.get(0).getType())){
                if(listDept.get(0).getType()==2){
                    somBasDept.setOrgName("(院内)-"+listDept.get(0).getName());
                }else{
                    somBasDept.setOrgName(listDept.get(0).getName());
                }
            }
        }

        if (ValidateUtil.isEmpty(somBasDept.getOrgId()) && !ValidateUtil.isEmpty(somBasDept.getYzbm())){
            somBasDept.setOrgId(somBasDept.getYzbm());
        }

        //查询医疗机构名称是否重复
        if(orgAndUserManagementDao.queryBaseOrgByName(somBasDept.getOrgName()) >= 1){
            throw new AppException("医疗机构名称重复，不能新增!");
        }
        somBasDept.setIs_vali(CodeConst.EEFFECTIVE_1);
        somBasDept.setCrterId(somBackUser.getId().intValue());
        if (ValidateUtil.isNotEmpty(somBasDept.getDeptNamePath())) {
            somBasDept.setDeptNamePath(somBasDept.getDeptNamePath()+"/"+somBasDept.getOrgName());
        } else {
            somBasDept.setDeptNamePath(somBasDept.getOrgName());
        }
        somBasDept.setDeptResper(String.valueOf(somBackUser.getId()));
        orgAndUserManagementDao.addOrg(somBasDept);
        if (ValidateUtil.isNotEmpty(somBasDept.getDeptIdPath())) {
            somBasDept.setDeptIdPath(somBasDept.getDeptIdPath()+"/"+somBasDept.getOrgId());
        } else {
            somBasDept.setDeptIdPath(somBasDept.getOrgId());
        }
        if(orgAndUserManagementDao.updateByPrimaryKeySelective(somBasDept) != 1){
            throw new AppException("更新组织路径异常，请稍后再试!");
        }
        return getOrgNodeByBaseOrg(somBasDept);
    }

    @Transactional
    @Override
    public BaseOrgNode removeOrg(SomBasDept somBasDept, SomBackUser somBackUser) {
        /*
         * 1、查询当前节点下的子级组织
         * 2、查询当前节点以及子节点下的所有用户 （后期实现）
         * 3、批量删除用户 （后期实现）
         * 4、批量删除子级组织
         * */
        //判断无法删除根节点
        if(ValidateUtil.isEmpty(somBasDept.getPrntDeptId())){
            throw new AppException("无法对根节点执行删除操作!");
        }
        List<SomBasDept> orgList = orgAndUserManagementDao.queryListOrgByOrgId(somBasDept.getOrgId());
        if(orgList.size()>0){
            int udpdateSize = orgAndUserManagementDao.removeListOrgByOrgId(orgList);
            if(orgList.size() != udpdateSize){
                throw new AppException("删除组织异常，请稍后再试!");
            }
        }
        return getOrgNodeByBaseOrg(somBasDept);
    }

    @Transactional
    @Override
    public BaseOrgNode updateOrg(SomBasDept somBasDept, SomBackUser somBackUser) {
        int updateSize = orgAndUserManagementDao.updateByPrimaryKeySelective(somBasDept);
        if (updateSize != 1){
            throw new AppException("更新失败，请稍后再试!");
        }
        return getOrgNodeByBaseOrg(somBasDept);
    }

    @Override
    public void extractDept(SomBasDept somBasDept, SysUserBase sysUserBase) {
        List<Map> list = sysUserMapper.queryHospitalInfo();
        if (ValidateUtil.isNotEmpty(somBasDept.getHospitalId())) {
            somBasDept.setOrgId(somBasDept.getHospitalId());
        }  else {
            somBasDept.setOrgId(sysUserBase.getHospitalId());
        }

        if (ValidateUtil.isEmpty(somBasDept.getUsername())) {
            somBasDept.setUsername(sysUserBase.getUsername());
        }
//        somBasDept.setOrgId(sysUserBase.getHospitalId());
        // 科室信息
        List<SomDept> deptDetails = orgAndUserManagementDao.extractDept(somBasDept);
        // 医生信息
        List<SomDept> selectWorkers = orgAndUserManagementDao.selectWorkers(somBasDept);
        if(ValidateUtil.isEmpty(deptDetails) && ValidateUtil.isEmpty(selectWorkers) ){
            throw new AppException("当前医院没有科室和医生可抽取");
        }

        // 当前组织架构
        OrgAndUserManagementParam param = new OrgAndUserManagementParam();
        param.setHospitalId(somBasDept.getHospitalId());
        List<SomBasDept> orgNodeList = orgAndUserManagementDao.getOrgNodeList(param);
        // 当前用户信息
        List<SysUserBase> sysWorkerList = sysUserMapper.selectByWorkerName(sysUserBase);
        List<SomDept> busDeptList = new ArrayList<>();
        List<SomDept> busUserList = new ArrayList<>();
        // 判断科室是否需要重新抽取
        for (SomDept dept : deptDetails) {
            boolean flag = true;
            for (SomBasDept org : orgNodeList) {
                if (dept.getCode().equals(org.getOrgId())){
                    flag = false;
                    break;
                }
            }
            if (flag) {
                busDeptList.add(dept);
            }
        }
        // 判断医生是否需要重新抽取
        for(SomDept workers : selectWorkers){
            boolean flag = true;
            for (SysUserBase user : sysWorkerList) {
                if (workers.getCode().equals(user.getBlngOrgOrgId()) && workers.getWorkerName().equals(user.getNknm())){
                    flag = false;
                    break;
                }
            }
            if (flag) {
                busUserList.add(workers);
            }
        }
        if (busDeptList.size() == 0 && busUserList.size() ==0 ){
            throw new AppException("已存在科室和医生无需再抽取");
        }

        // 科室--用户，组织
        if(busDeptList.size()!=0) {
            SomBasDept selectDetail = orgAndUserManagementDao.selectDetail(somBasDept);
            List<SomBasDept> orgList = new ArrayList<>();
            List<SomBackUser> sysUserList = new ArrayList<>();
            for (SomDept dept : busDeptList) {
                String suffix = "";
                if (DrgConst.TYPE_2.equals(dept.getType().toString())) {
                    suffix = "-院内";
                }
                //组织架构
                SomBasDept org = new SomBasDept();
                org.setOrgId(dept.getCode());
                org.setPrntDeptId(somBasDept.getOrgId());
                org.setOrgName(dept.getName() + suffix);
                org.setTeamleadType(DrgConst.ORG_TYPE_2);
                org.setArgtSeq(selectDetail.getArgtSeq() + 1);
                org.setDeptHery(selectDetail.getDeptHery() + 1);
                org.setCrterId(Integer.valueOf(sysUserBase.getId().toString()));
                org.setIs_vali(DrgConst.START_FLAG_1);
                org.setDeptResper(sysUserBase.getId().toString());
                org.setDeptIdPath(selectDetail.getDeptIdPath() + "/" + dept.getCode());
                org.setDeptNamePath(selectDetail.getDeptNamePath() + "/" + dept.getName() + suffix);
                orgList.add(org);
                //系统用户
                SomBackUser somBackUser = new SomBackUser();
                String username = ChineseCharToEnUtil.getAllFirstLetter((dept.getName() + (suffix.length() == 0 ? "" : suffix.substring(1))));
                if(list.size() > 1) {
                    somBackUser.setUsername(dept.getHospitalId()+somBasDept.getUsername() + '-' + username);
                } else {
                    somBackUser.setUsername(somBasDept.getUsername() + '-' + username);
                }
                somBackUser.setNknm(dept.getName());
                String encodePassword = passwordEncoder.encode("123456");
                somBackUser.setPassword(encodePassword);
                somBackUser.setIsLckUser(CodeConst.ISLOCK_0);
                somBackUser.setExpireTime(sysUserBase.getExpireTime());
                somBackUser.setUserErrPwdCnt(Short.valueOf("0"));
                somBackUser.setBlngOrgOrgId(dept.getCode());

                somBackUser.setBlngOrgOrgName(dept.getName());
                somBackUser.setStatus(Integer.valueOf(DrgConst.START_FLAG_1));
                somBackUser.setFeeStas(0);
                somBackUser.setHospitalId(somBasDept.getHospitalId());
                sysUserList.add(somBackUser);
            }
            orgAndUserManagementDao.batchInsertOrg(orgList);
            sysUserMapper.batchInsertUser(sysUserList);
        }

        // 医生--用户
        if (busUserList.size() != 0) {
            List<SomBackUser> sysWorkersList = new ArrayList<>();
            for(SomDept workers : busUserList){
                SomBackUser sysWorkers = new SomBackUser();
                if(list.size() > 1) {
                    if ("1".equals(somBasDept.getType())) {
                        String username = ChineseCharToEnUtil.getAllFirstLetter(workers.getWorkerName());
                        sysWorkers.setUsername(workers.getHospitalId()+somBasDept.getUsername() + '-' + username);
                    } else {
                        sysWorkers.setUsername(workers.getHospitalId()+workers.getWorkerCode());
                    }
                } else {
                    if ("1".equals(somBasDept.getType())) {
                        String username = ChineseCharToEnUtil.getAllFirstLetter(workers.getWorkerName());
                        sysWorkers.setUsername(somBasDept.getUsername() + '-' + username);
                    } else {
                        sysWorkers.setUsername(workers.getWorkerCode());
                    }
                }

                sysWorkers.setNknm(workers.getWorkerName());
                String encodePassword = passwordEncoder.encode("123456");
                sysWorkers.setPassword(encodePassword);
                sysWorkers.setIsLckUser(CodeConst.ISLOCK_0);
                sysWorkers.setExpireTime(sysUserBase.getExpireTime());
                sysWorkers.setUserErrPwdCnt(Short.valueOf("0"));
                sysWorkers.setStatus(Integer.valueOf(DrgConst.START_FLAG_1));
                sysWorkers.setFeeStas(0);
                sysWorkers.setDrCodg(workers.getWorkerCode());
                sysWorkers.setHospitalId(somBasDept.getHospitalId());
                sysWorkers.setBlngOrgOrgId(workers.getCode());
                sysWorkers.setBlngOrgOrgName(workers.getName());
//                for (SomDept dept : deptDetails) {
//                    if(workers.getCode().equals(dept.getCode())){
//                        sysWorkers.setBlngOrgOrgId(dept.getCode());
//                        sysWorkers.setBlngOrgOrgName(dept.getName());
//                        break;
//                    }
//                }
                sysWorkersList.add(sysWorkers);
            }
            sysUserMapper.batchInsertUser(sysWorkersList);
        }

        // 科室权限
        List<String> deptNames = new ArrayList<>();
        busDeptList.stream().forEach(dept -> {
            deptNames.add(dept.getName());
        });

        if (ValidateUtil.isNotEmpty(deptNames)) {
            List<String> ids = sysUserMapper.selectByUserName(deptNames);
            List<SomUserRole> sysUserRoles = new ArrayList<>();
            for (String id : ids) {
                SomUserRole sysUserRole1 = new SomUserRole();
                sysUserRole1.setUserId(Long.valueOf(id));
                sysUserRole1.setRoleId(Long.valueOf(String.valueOf(SysCommonConfigUtil.get(DrgConst.SCC_DEPT_AUTH,true))));

                SomUserRole sysUserRole2 = new SomUserRole();
                sysUserRole2.setUserId(Long.valueOf(id));
                sysUserRole2.setRoleId(Long.valueOf(String.valueOf(SysCommonConfigUtil.get(DrgConst.SCC_BASIC_AUTH,true))));
                sysUserRoles.add(sysUserRole1);
                sysUserRoles.add(sysUserRole2);
            }

            // 科室--角色权限
            if (ValidateUtil.isNotEmpty(sysUserRoles)) {
                sysUserRoleMapper.batchInsertSysUserRole(sysUserRoles);
            }
        }

        // 医生权限
        List<String> doctorNames = new ArrayList<>();
        busUserList.stream().forEach(doctor -> {
            doctorNames.add(doctor.getWorkerName());
        });
        if (ValidateUtil.isNotEmpty(doctorNames)) {
            List<String> workersId = sysUserMapper.selectByUserName(doctorNames);
            List<SomUserRole> sysWorkerRoles = new ArrayList<>();
            for (String id : workersId) {
                SomUserRole sysUserRole1 = new SomUserRole();
                sysUserRole1.setUserId(Long.valueOf(id));
                sysUserRole1.setRoleId(Long.valueOf(String.valueOf(SysCommonConfigUtil.get(DrgConst.SCC_DOCTOR_AUTH,true))));
                sysWorkerRoles.add(sysUserRole1);
            }
            // 医生--角色权限
            if(ValidateUtil.isNotEmpty(sysWorkerRoles)){
                sysUserRoleMapper.batchInsertSysUserRole(sysWorkerRoles);
            }
        }

    }

    /**
     * 根据BaseOrg 对象，生成对应的node节点对象
     * @param orgNode
     * @return
     */
    private BaseOrgNode getNodeTreeObject(BaseOrgNode orgNode,List<BaseOrgNode> nodeList){
        int listSize = nodeList.size();
        BaseOrgNode itemOrgNode;
        BaseOrgNode childrenNode;
        int childrenSize = orgNode.getChildren().size();
        for (int i=0;i<childrenSize;i++){
            childrenNode = orgNode.getChildren().get(i);
            for(int j = 0;j<listSize;j++){
                itemOrgNode = nodeList.get(j);
                if(childrenNode.getOrgId().equals(itemOrgNode.getOrgId())){
                    if(itemOrgNode.getChildren().size()>0){
                        orgNode.getChildren().set(i,getNodeTreeObject(itemOrgNode,nodeList));
                    }else {
                        orgNode.getChildren().set(i,itemOrgNode);
                    }
                }
            }
        }
        return orgNode;
    }

    /**
     * 根据 somBasDept 获取 BaseOrgNode
     * @param somBasDept
     * @return
     */
    private BaseOrgNode getOrgNodeByBaseOrg(SomBasDept somBasDept){
        BaseOrgNode baseOrgNode = new BaseOrgNode();
        if(!StrUtil.isBlankIfStr(somBasDept)){
            baseOrgNode.setOrgId(somBasDept.getOrgId());
            baseOrgNode.setPrntDeptId(somBasDept.getPrntDeptId());
            baseOrgNode.setOrgName(somBasDept.getOrgName());
            baseOrgNode.setTeamleadType(somBasDept.getTeamleadType());
            baseOrgNode.setDeptNamePath(somBasDept.getDeptNamePath());
            baseOrgNode.setArgtSeq(somBasDept.getArgtSeq());
            baseOrgNode.setDeptHery(somBasDept.getDeptHery());
            baseOrgNode.setDeptIdPath(somBasDept.getDeptIdPath());
            baseOrgNode.setChildren(new ArrayList<>());
        }
        return baseOrgNode;
    }
}
