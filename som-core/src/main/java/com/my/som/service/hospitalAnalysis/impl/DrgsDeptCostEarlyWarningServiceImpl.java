package com.my.som.service.hospitalAnalysis.impl;

import com.my.som.dto.hospitalAnalysis.DrgsDeptCostEarlyWarningDto;
import com.my.som.mapper.hospitalAnalysis.DrgsDeptCostEarlyWarningMapper;
import com.my.som.service.hospitalAnalysis.DrgsDeptCostEarlyWarningService;
import com.my.som.vo.hospitalAnalysis.DrgsDeptCostEarlyWarningVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
@Service
public class DrgsDeptCostEarlyWarningServiceImpl implements DrgsDeptCostEarlyWarningService {

    @Autowired
    private DrgsDeptCostEarlyWarningMapper drgsDeptCostEarlyWarningMapper;

    @Override
    public List<DrgsDeptCostEarlyWarningVo> getList(DrgsDeptCostEarlyWarningDto dto) {
        return drgsDeptCostEarlyWarningMapper.getList(dto);
    }
}
