package com.my.som.service.dipBusiness;

import com.my.som.dto.dipBusiness.DipBusinessQueryDto;
import com.my.som.vo.dipBusiness.DipCoverRateVo;
import com.my.som.vo.dipBusiness.DipNumByMonthVo;

import java.util.List;
import java.util.Map;

/**
 * Service
 * Created by sky on 2020/3/20.
 */
public interface DipKnowledgeBaseService {
    /**
     * DIP分组情况
     * @param queryParam
     * @return
     */
    List<Map<String,Object>> list(DipBusinessQueryDto queryParam, Integer pageSize, Integer pageNum);

    /**
     * 查询全院DIP分组覆盖占比
     * @param queryParam
     * @return
     */
    DipCoverRateVo getCountByCoverRate(DipBusinessQueryDto queryParam);

    /**
     * 查询本期同期各月DIP组数
     * @param queryParam
     * @return
     */
    List<DipNumByMonthVo> getCountByMonth(DipBusinessQueryDto queryParam);

}
