package com.my.som.service.hospitalAnalysis.impl;

import com.github.pagehelper.PageHelper;
import com.my.som.common.exception.AppException;
import com.my.som.dao.hospitalAnalysis.DoctorAnalysisDao;
import com.my.som.dto.hospitalAnalysis.HospitalAnalysisQueryParam;
import com.my.som.service.hospitalAnalysis.DoctorAnalysisService;
import com.my.som.vo.hospitalAnalysis.DoctorAnalysisCountInfo;
import com.my.som.vo.hospitalAnalysis.DoctorAnalysisInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Service实现类
 * Created by sky on 2020/1/5.
 */
@Service
public class DoctorAnalysisServiceImpl implements DoctorAnalysisService {
    @Autowired
    private DoctorAnalysisDao doctorAnalysisDao;


    @Override
    public List<DoctorAnalysisInfo> list(HospitalAnalysisQueryParam queryParam, Integer pageSize, Integer pageNum) {
        List<DoctorAnalysisInfo> list = null;
        try{
            PageHelper.startPage(pageNum, pageSize);
            list = doctorAnalysisDao.list(queryParam);
        }catch (Exception e){
            e.printStackTrace();
            throw new AppException("查询医生DRGs指标异常");
        }
        return list;
    }

    @Override
    public List<DoctorAnalysisCountInfo> getCountInfo(HospitalAnalysisQueryParam queryParam) {
        return doctorAnalysisDao.getCountInfo(queryParam);
    }
}
