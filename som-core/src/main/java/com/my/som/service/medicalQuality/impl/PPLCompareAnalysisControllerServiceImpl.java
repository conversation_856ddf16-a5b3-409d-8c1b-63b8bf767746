package com.my.som.service.medicalQuality.impl;

import com.github.pagehelper.PageHelper;
import com.my.som.dao.medicalQuality.PPLCompareAnalysisControllerMapper;
import com.my.som.dto.medicalQuality.CompareAnalysisDto;
import com.my.som.service.medicalQuality.PPLCompareAnalysisControllerService;
import com.my.som.util.ConfigProcessJudgeUtil;
import com.my.som.vo.medicalQuality.PPLCompareAnalysisVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

/**
 * @author: zyd
 * @Date 2021/9/23 2:37 下午
 * @Version 1.0
 * @Description: 人员信息对比
 */
@Service
public class PPLCompareAnalysisControllerServiceImpl implements PPLCompareAnalysisControllerService {

    @Autowired
    private PPLCompareAnalysisControllerMapper pplCompareAnalysisControllerMapper;

    @Override
    public List<PPLCompareAnalysisVo> getList(CompareAnalysisDto dto) {
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        return pplCompareAnalysisControllerMapper.getList(dto);
    }

    @Override
    public List<PPLCompareAnalysisVo> getInfo(CompareAnalysisDto dto) {
        return pplCompareAnalysisControllerMapper.getInfo(dto);
    }

    @Override
    public List<PPLCompareAnalysisVo> getIngroup(CompareAnalysisDto dto) {
        return pplCompareAnalysisControllerMapper.getIngroup(dto);
    }

    @Override
    public List<PPLCompareAnalysisVo> getCost(CompareAnalysisDto dto) {
        return pplCompareAnalysisControllerMapper.getCost(dto);
    }

    @Override
    public List<PPLCompareAnalysisVo> getCostPay(CompareAnalysisDto dto) {
        return pplCompareAnalysisControllerMapper.getCostPay(dto);
    }


}
