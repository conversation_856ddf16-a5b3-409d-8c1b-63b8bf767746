package com.my.som.service.hospitalAnalysis.impl;

import com.github.pagehelper.PageHelper;
import com.my.som.dao.hospitalAnalysis.DrgsDeptDao;
import com.my.som.dto.hospitalAnalysis.HospitalAnalysisQueryParam;
import com.my.som.service.hospitalAnalysis.DrgsDeptIndexService;
import com.my.som.vo.hospitalAnalysis.DrgsDeptCountVo;
import com.my.som.vo.hospitalAnalysis.DrgsDeptIndexVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Service实现类
 * Created by sky on 2020/1/5.
 */
@Service
public class DrgsDeptServiceImpl implements DrgsDeptIndexService {
    @Autowired
    private DrgsDeptDao drgsDeptDao;


    @Override
    public List<DrgsDeptIndexVo> list(HospitalAnalysisQueryParam queryParam, Integer pageSize, Integer pageNum) {
        PageHelper.startPage(pageNum, pageSize);
        return drgsDeptDao.list(queryParam);
    }

    @Override
    public List<DrgsDeptCountVo> getCountInfo(HospitalAnalysisQueryParam queryParam) {
        return drgsDeptDao.getCountInfo(queryParam);
    }
}
