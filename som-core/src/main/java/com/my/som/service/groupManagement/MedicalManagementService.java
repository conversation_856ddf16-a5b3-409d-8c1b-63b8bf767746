package com.my.som.service.groupManagement;

import com.my.som.dto.groupManagement.MedicalManagementDto;
import com.my.som.vo.groupManagement.MedicalManagementVo;

import java.util.List;

/**
 * 病案管理
 */
public interface MedicalManagementService {

    /**
     * DIP测算
     * @param dto
     */
    Boolean calculationData(MedicalManagementDto dto);

    /**
     * DRG测算
     * @param dto
     */
    void drgCalculationData(MedicalManagementDto dto);

    /**
     * 数据查询
     * @param dto
     * @return
     */
    List<MedicalManagementVo> queryPatientInfo(MedicalManagementDto dto);

    List<MedicalManagementVo> queryGroupsInfo(MedicalManagementDto dto);
}
