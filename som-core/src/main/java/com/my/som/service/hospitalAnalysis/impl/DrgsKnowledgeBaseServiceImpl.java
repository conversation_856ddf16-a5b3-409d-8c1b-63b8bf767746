package com.my.som.service.hospitalAnalysis.impl;

import com.my.som.common.constant.DrgConst;
import com.my.som.common.util.ValidateUtil;
import com.my.som.dao.common.CommonDao;
import com.my.som.dao.hospitalAnalysis.DrgsKnowledgeBaseDao;
import com.my.som.dto.common.HospitalDrgQueryParam;
import com.my.som.dto.hospitalAnalysis.HospitalAnalysisQueryParam;
import com.my.som.service.hospitalAnalysis.DrgsKnowledgeBaseService;
import com.my.som.vo.dataHandle.dataGroup.HospitalDrgVo;
import com.my.som.vo.hospitalAnalysis.AdrgContainDrgsVo;
import com.my.som.vo.hospitalAnalysis.MdcContainAdrgVo;
import com.my.som.vo.hospitalAnalysis.DrgsCoverRateVo;
import com.my.som.vo.hospitalAnalysis.DrgsKnowledgeVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * Service实现类
 * Created by sky on 2020/5/20.
 */
@Service
public class DrgsKnowledgeBaseServiceImpl implements DrgsKnowledgeBaseService {
    @Autowired
    private CommonDao commonDao;
    @Autowired
    private DrgsKnowledgeBaseDao drgsKnowledgeBaseDao;

    @Override
    public List<DrgsKnowledgeVo> list(HospitalAnalysisQueryParam queryParam) {
        List<DrgsKnowledgeVo> result = new ArrayList<>();
        //根据医院编号查询该医院当前使用哪个版本分组器
        HospitalDrgQueryParam hospitalDrgQueryParam = new HospitalDrgQueryParam();
        hospitalDrgQueryParam.setHospital_id(queryParam.getHospitalId());
        hospitalDrgQueryParam.setActive_flag(DrgConst.ACTIVE_FLAG_1);
        hospitalDrgQueryParam.setGrper_type(DrgConst.GROUP_TYPE_DRG);
        HospitalDrgVo hospitalDrgVo = new HospitalDrgVo();
        if(!ValidateUtil.isEmpty(commonDao.getHospitalDrgByHospitalId(hospitalDrgQueryParam))){
            hospitalDrgVo = commonDao.getHospitalDrgByHospitalId(hospitalDrgQueryParam).get(0);
        }
        queryParam.setDrgGroupType(hospitalDrgVo.getDrgGroupType());
        //查询所有MDC和ADRG的关系，MDC包含的ADRG
        List<MdcContainAdrgVo> mdcContainAdrgVoList = drgsKnowledgeBaseDao.getMdcAdrg(queryParam);
        //查询所有ADRG和DRGs的关系,ADRG包含的DRGs
        List<AdrgContainDrgsVo> adrgContainDrgsVoList = drgsKnowledgeBaseDao.getAdrgAndDrgs(queryParam);
        //查询所有DRGs指标情况
        List<DrgsKnowledgeVo> drgsIndexList = drgsKnowledgeBaseDao.getDrgsIndex(queryParam);
        List<DrgsKnowledgeVo> adrgDrgsKnowledgeVoList = new ArrayList<>();
        BigDecimal id=new BigDecimal(0);
        for(AdrgContainDrgsVo aad:adrgContainDrgsVoList){
            DrgsKnowledgeVo adrgDrgsKnowledgeVo = new DrgsKnowledgeVo();
            adrgDrgsKnowledgeVo.setId(id);
            id=id.add(new BigDecimal(1));
            adrgDrgsKnowledgeVo.setDrgsCode(aad.getAdrgCodg());
            adrgDrgsKnowledgeVo.setDrgsName(aad.getAdrgName());
            List<DrgsKnowledgeVo> drgsKnowledgeVoList = new ArrayList<>();
            for(DrgsKnowledgeVo dk:drgsIndexList){
                if(Arrays.asList(aad.getDrgsCodes().split(",")).contains(dk.getDrgsCode())){
                    DrgsKnowledgeVo drgsKnowledgeVo = new DrgsKnowledgeVo();
                    drgsKnowledgeVo.setId(id);
                    id=id.add(new BigDecimal(1));
                    drgsKnowledgeVo.setDrgsCode(dk.getDrgsCode());
                    drgsKnowledgeVo.setDrgsName(dk.getDrgsName());
                    drgsKnowledgeVo.setMedcasVal(dk.getMedcasVal());
                    drgsKnowledgeVo.setAvgCost(dk.getAvgCost());
                    drgsKnowledgeVo.setAvgDays(dk.getAvgDays());
                    drgsKnowledgeVo.setAvgDrugFee(dk.getAvgDrugFee());
                    drgsKnowledgeVo.setAvgMaterailCost(dk.getAvgMaterailCost());
                    drgsKnowledgeVo.setAvgAbtFee(dk.getAvgAbtFee());
                    drgsKnowledgeVo.setInspectFeeStandardVal(dk.getInspectFeeStandardVal());
                    drgsKnowledgeVoList.add(drgsKnowledgeVo);
                }
            }
            adrgDrgsKnowledgeVo.setChildren(drgsKnowledgeVoList);
            adrgDrgsKnowledgeVoList.add(adrgDrgsKnowledgeVo);
        }
        for(MdcContainAdrgVo maa:mdcContainAdrgVoList){
            DrgsKnowledgeVo mdcDrgsKnowledgeVo = new DrgsKnowledgeVo();
            mdcDrgsKnowledgeVo.setId(id);
            id=id.add(new BigDecimal(1));
            mdcDrgsKnowledgeVo.setDrgsCode(maa.getMdcCodg());
            mdcDrgsKnowledgeVo.setDrgsName(maa.getMdcName());
            List<DrgsKnowledgeVo> mdcKnowledgeVoList = new ArrayList<>();
            for(DrgsKnowledgeVo adk:adrgDrgsKnowledgeVoList){

                if(Arrays.asList(maa.getAdrgCodes().split(",")).contains(adk.getDrgsCode())){
                    mdcDrgsKnowledgeVo.setId(id);
                    id=id.add(new BigDecimal(1));
                    mdcKnowledgeVoList.add(adk);
                }
            }
            mdcDrgsKnowledgeVo.setChildren(mdcKnowledgeVoList);
            result.add(mdcDrgsKnowledgeVo);
        }
        return result;
    }

    @Override
    public DrgsCoverRateVo getCountByCoverRate(HospitalAnalysisQueryParam queryParam) {
        return drgsKnowledgeBaseDao.getCountByCoverRate(queryParam);
    }
}
