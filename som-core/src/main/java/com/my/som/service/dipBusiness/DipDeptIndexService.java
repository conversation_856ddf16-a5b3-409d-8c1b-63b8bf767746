package com.my.som.service.dipBusiness;

import com.my.som.dto.dipBusiness.DipBusinessQueryDto;
import com.my.som.vo.dipBusiness.DipDeptCountVo;
import com.my.som.vo.dipBusiness.DipDeptIndexVo;

import java.util.List;

/**
 * Service
 * Created by sky on 2020/3/20.
 */
public interface DipDeptIndexService {
    /**
     * 查询科室病组指标信息
     * @param queryParam
     * @return
     */
    List<DipDeptIndexVo> list(DipBusinessQueryDto queryParam, Integer pageSize, Integer pageNum);

    /**
     * 查询科室病组指标统计信息
     * @param queryParam
     * @return
     */
    List<DipDeptCountVo> getCountInfo(DipBusinessQueryDto queryParam);

    List<DipDeptIndexVo> queryConsumptionIndex(DipBusinessQueryDto queryParam);

}
