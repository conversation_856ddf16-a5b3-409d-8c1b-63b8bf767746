package com.my.som.service.listManagement.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.ValueFilter;
import com.github.pagehelper.PageHelper;
import com.my.som.app.dto.AppMessageDto;
import com.my.som.app.dto.AppUserDto;
import com.my.som.app.mapper.user.AppUserMapper;
import com.my.som.app.service.message.AppMessageService;
import com.my.som.app.util.IPushUtil;
import com.my.som.app.vo.AppUserVo;
import com.my.som.app.vo.message.AppMessageVo;
import com.my.som.common.constant.DrgConst;
import com.my.som.common.exception.AppException;
import com.my.som.common.util.EasyPoiUtil;
import com.my.som.common.util.MapUtil;
import com.my.som.common.util.ValidateUtil;
import com.my.som.dao.dataHandle.MedicalSettleCheckDao;
import com.my.som.dao.medicalQuality.SettleListManageDao;
import com.my.som.dto.common.DipConfigDto;
import com.my.som.dto.dataConfig.DoctorCodeDto;
import com.my.som.dto.dataConfig.NurseCodeDto;
import com.my.som.dto.dataConfig.SomDeptConfigDto;
import com.my.som.dto.dataConfig.TransfusionCodeDto;
import com.my.som.dto.dataHandle.DataHandleCommonDto;
import com.my.som.dto.dataHandle.MedicalSettleCheckDto;
import com.my.som.dto.listIUploadedQuery.*;
import com.my.som.dto.listManagement.ListUploadDto;
import com.my.som.dto.listManagement.MainCode;
import com.my.som.dto.listManagement.UploadDto;
import com.my.som.dto.medicalQuality.AllBusSettleListInfo;
import com.my.som.entity.upload.BatchListUploadConfig;
import com.my.som.entity.upload.Signature;
import com.my.som.grouppay.cityarrange.PaymentForecastLiteFlowService;
import com.my.som.grouppay.vo.HospBaseBusiVo;
import com.my.som.mapper.dataConfig.DoctorCodeConfigMapper;
import com.my.som.mapper.dataConfig.NurseCodeConfigMapper;
import com.my.som.mapper.dataConfig.SomDeptCrspConfigMapper;
import com.my.som.mapper.dataConfig.TransfusionCodeConfigMapper;
import com.my.som.mapper.engine.GeneraScoreMapper;
import com.my.som.mapper.listManagement.ListUploadMapper;
import com.my.som.mapper.uploadedList.UploadedListMapper;
import com.my.som.service.dataHandle.impl.SettleListValidateJobServiceImpl;
import com.my.som.service.listManagement.ListUploadService;
import com.my.som.service.medicalQuality.SettleListManageService;
import com.my.som.util.*;
import com.my.som.vo.dataConfig.DrCodgVo;
import com.my.som.vo.dataConfig.NurseCodeVo;
import com.my.som.vo.dataConfig.SomDeptConfigVo;
import com.my.som.vo.dataConfig.TransfusionCodeVo;
import com.my.som.vo.dataHandle.MedicalSettleCheckVo;
import com.my.som.vo.dataHandle.dataGroup.HospitalDrgVo;
import com.my.som.vo.dipBusiness.DipPayToPredictVo;
import com.my.som.vo.listManagement.*;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;

import java.util.stream.Collectors;



@Service
@Transactional
@Slf4j
public class ListUploadServiceImpl implements ListUploadService {
    private static Logger logger = LoggerFactory.getLogger(ListUploadServiceImpl.class);

    @Autowired
    private AppUserMapper appUserMapper;

    @Autowired
    private SettleListManageDao settleListManageDao;

    @Autowired
    private AppMessageService appMessageService;

    @Autowired
    private ListUploadMapper listUploadMapper;

    @Autowired
    private SettleListValidateJobServiceImpl settleListValidateJobService;

    @Autowired
    private DoctorCodeConfigMapper doctorCodeConfigMapper;

    @Autowired
    private NurseCodeConfigMapper nurseCodeConfigMapper;

    @Autowired
    private TransfusionCodeConfigMapper transfusionCodeConfigMapper;

    @Autowired
    private MedicalSettleCheckDao medicalSettleCheckDao;

    @Autowired
    private SomDeptCrspConfigMapper deptCrspConfigMapper;

    @Autowired
    private SettleListManageService settleListManageService;

    @Autowired
    private UploadedListMapper uploadedListMapper;

    @Autowired
    private PaymentForecastLiteFlowService paymentForecastLiteFlowService;

    @Autowired
    private GeneraScoreMapper generaScoreMapper;
    /**
     * 顺序号
     */
    private final String ORDER_NO = "0307";
    /**
     * 移除ID
     */
    private final String REMOVE_ID_KEY = "settleListId";


    /**
     * 参保地医保区划key
     */
    private final String INS_AREA_KEY = "insuplc_admdvs";

    /**
     * 空
     */
    private final String NULL_ = "-";

    /**
     * 上传状态
     */
    private final String UPLOAD_STATE_KEY = "state";
    private final String UPLOAD_STATE_SUCCESS = "1";
    private final String UPLOAD_STATE_FAIL = "0";

    @Override
    public List<ListUploadVo> queryData(ListUploadDto dto) {
        updateInsuPlaceType(dto);
        //德阳配置 未入组不允许上传
        Object drgGrouperVerObj = SysCommonConfigUtil.get(DrgConst.ONLY_GROUY_CAN_UPLOAD);
        if (drgGrouperVerObj != null && "true".equals(drgGrouperVerObj.toString())) {
            dto.setOnlyGroupCanUpload("true");
        }

        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        List<ListUploadVo> list = listUploadMapper.queryData(dto);
        return list;
    }
    private static void updateInsuPlaceType(ListUploadDto dto) {
        String insuPlaceType = (String) SysCommonConfigUtil.get(DrgConst.INSUPLC_ADMDVS_AREA);
        dto.setInsuplcAdmdvs(insuPlaceType);
        String rovLevelInsuplcAdmdvs =GroupCommonUtil.getInsuplcAdmdvs();
        if(ValidateUtil.isEmpty(rovLevelInsuplcAdmdvs)){
            rovLevelInsuplcAdmdvs = "0000";
        }
        dto.setProvLevelInsuplcAdmdvs(rovLevelInsuplcAdmdvs);
    }


    @Override
    public List<ListUploadVo> querySybData(ListUploadDto dto) {
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        List<ListUploadVo> list = listUploadMapper.querySybData(dto);
        return list;
    }


    @Override
    @Transactional
    public Map<String, Object> updateData(ListUploadDto dto) {
        // updateInsuPlaceType(dto);
        Map<String, Object> res = setUploadParams(dto);
        if(ValidateUtil.isEmpty(dto.getHospitalId())){
            dto.setHospitalId((String) SysCommonConfigUtil.get(DrgConst.SCC_HOSPITAL_ID));
        }
        uploadSettleData(dto, res);
        //清单上传完成推送消息
//        pushUploadMessage(dto);
        return res;
    }

    @Override
    public Map<String, Object> updateSybData(ListUploadDto dto) {
        Map<String, Object> res = setUploadSybParams(dto);
        sybJsonAndUpload1(dto, res);
        listUploadMapper.updateSybData(dto);
        //清单上传完成推送消息
//        pushUploadMessage(dto);
        return res;
    }

    @Override
    public Map<String, Object> updateYASybData(ListUploadDto dto) {
        Map<String, Object> res = setUploadSybParams(dto);
        sybJsonAndUpload1(dto, res);
        listUploadMapper.updateSybData(dto);
        //清单上传完成推送消息
//        pushUploadMessage(dto);
        return res;
    }

    @Override
    public List<ListUploadVo> queryYASybData(ListUploadDto dto) {
        return Collections.emptyList();
    }


    /**
     * 设置上传参数
     *
     * @param dto
     * @return
     */
    private Map<String, Object> setUploadParams(ListUploadDto dto) {
        Map<String, Object> res = new HashMap<>();
        String uuid = UUID.randomUUID().toString();
        BigDecimal decimal = new BigDecimal(dto.getUploadFlag());
        dto.setBatchNum(uuid);
        dto.setType(decimal.add(BigDecimal.ONE).toString());
        List<String> idsList = new ArrayList<>();
        List<String> k00sList = new ArrayList<>();
        // 全部上传时查询
        if (ValidateUtil.isEmpty(dto.getIds())) {
            dto.setAllUpload(true);
            dto.setLookOver(DrgConst.TYPE_1);
            List<ListUploadVo> list;
            if (DrgConst.TYPE_1.equals(dto.getUploadType())) {
                list = listUploadMapper.queryData(dto);
            } else {
                list = listUploadMapper.queryUploadedData(dto);
            }
            for (ListUploadVo listUploadVo : list) {
                idsList.add(listUploadVo.getId());
                k00sList.add(listUploadVo.getK00());
            }
            dto.setIds(idsList);
            dto.setK00s(k00sList);
        }
        res.put(UPLOAD_STATE_KEY, UPLOAD_STATE_SUCCESS);
        if (ValidateUtil.isEmpty(dto.getIds())) {
            throw new AppException("没有可上传数据");
        }
        return res;
    }

    /**
     * 设置上传市医保参数
     *
     * @param dto
     * @return
     */
    private Map<String, Object> setUploadSybParams(ListUploadDto dto) {
        Map<String, Object> res = new HashMap<>();
        String uuid = UUID.randomUUID().toString();
        BigDecimal decimal = new BigDecimal(dto.getUploadFlag());
        dto.setBatchNum(uuid);
        dto.setType(decimal.add(BigDecimal.ONE).toString());
        List<String> idsList = new ArrayList<>();
        List<String> k00sList = new ArrayList<>();
        // 全部上传时查询
        if (ValidateUtil.isEmpty(dto.getIds())) {
            dto.setAllUpload(true);
            dto.setLookOver(DrgConst.TYPE_1);
            List<ListUploadVo> list;
            if (DrgConst.TYPE_1.equals(dto.getUploadType())) {
                list = listUploadMapper.querySybData(dto);
            } else {
                list = listUploadMapper.queryUploadedSybData(dto);
            }
            for (ListUploadVo listUploadVo : list) {
                idsList.add(listUploadVo.getId());
                k00sList.add(listUploadVo.getK00());
            }
            dto.setIds(idsList);
            dto.setK00s(k00sList);
        }
        res.put(UPLOAD_STATE_KEY, UPLOAD_STATE_SUCCESS);
        if (ValidateUtil.isEmpty(dto.getIds())) {
            throw new AppException("没有可上传数据");
        }
        return res;
    }

    private void pushUploadMessage(ListUploadDto dto) {
        //判断该月份所有清单是否上传成功
        //查询未上传数量
        String time = dto.getBegnDate().substring(0, 7);
        dto.setBegnDate(time);
        int count = settleListManageDao.noUploadCount(dto);
        if (count == 0) {
            String tagIds = "4;";
            String role = "settle_list_manager";
            //推送消息
            AppMessageVo messageVo = new AppMessageVo();
            String message = time + " 月份清单上传已完成！";
            AppMessageDto messageDto = new AppMessageDto();
            messageDto.setPushUsername(DrgConst.DEV_NAME);
            messageDto.setPushText(message);
            messageDto.setTagIds(tagIds);
            messageDto.setType(DrgConst.TYPE_2);
            messageDto.setRole(role);
            appMessageService.addMessage(messageDto);
            //推送下拉栏消息
            pushDropdownMessage(role, message);
        }
    }

    public void pushDropdownMessage(String role, String message) {
        //查询拥有清单管理角色列表
        List<AppUserVo> appUserVos = new ArrayList<>();
        AppUserDto appUserDto = new AppUserDto();
        appUserDto.setRole(role);
        appUserVos = appUserMapper.queryUserListByRole(appUserDto);
        //根据 username 去拿 clientId
        List<String> clientIds = new ArrayList<>();
        if (ValidateUtil.isNotEmpty(appUserVos)) {
            clientIds = appUserMapper.queryClientIdsByName(appUserVos);
            clientIds.forEach(id -> {
                IPushUtil.pushSingle("清单管理", message, id);
            });
        } else {
            throw new AppException("没有查询拥有清单管理的角色");
        }
    }

    /**
     * 上传4101A接口到省上
     *
     * @param dto 参数
     * @param res 返回结果
     */
    private void uploadSettleData(ListUploadDto dto,
                                  Map<String, Object> res) {
        // 获取清单配置信息
        List<SettleListConfigVo> settleListConfigVoList = listUploadMapper.querySettleConfig(dto);
        Map<String, SettleListConfigVo> settleListConfigVoMap = settleListConfigVoList.stream().collect(Collectors.toMap(SettleListConfigVo::getInsuplcAdmdvs, settleListConfigVo -> settleListConfigVo));

        // 获取清单校验配置信息，获取那些字段不需要转换为null
        List<String> notConvertFieldsList = getNotConvertFields();
        // 清单基本信息
        List<SetlInfoVo> setlInfoVos = listUploadMapper.querySetlInfoData(dto);

        // 清单门慢门特信息
        List<OpspDiseInfoVo> opspDiseInfoVos = listUploadMapper.queryOpspDiseInfoData(dto);

        if (ValidateUtil.isNotEmpty(opspDiseInfoVos)) {
            List<OpspDiseInfoVo> removeVoList = new ArrayList<>();
            for (OpspDiseInfoVo opspDiseInfoVo : opspDiseInfoVos) {
                if (ValidateUtil.isEmpty(opspDiseInfoVo.getOprn_oprt_code()) &&
                        ValidateUtil.isEmpty(opspDiseInfoVo.getOprn_oprt_name())) {
                    removeVoList.add(opspDiseInfoVo);
                }
            }
            opspDiseInfoVos.removeAll(removeVoList);
        }
        // 清单诊断信息
        List<DiseInfoVo> diseInfoVos = listUploadMapper.queryDiseInfoData(dto);
        // 处理诊断信息
        if (ValidateUtil.isNotEmpty(diseInfoVos)) {
            List<DiseInfoVo> removeVoList = new ArrayList<>();
            for (DiseInfoVo diseInfoVo : diseInfoVos) {
                if (NULL_.equals(diseInfoVo.getDiag_code())) {
                    removeVoList.add(diseInfoVo);
                }
            }
            diseInfoVos.removeAll(removeVoList);
        }
        // 清单手术信息
        List<OprnInfoVo> oprnInfoVos = listUploadMapper.queryOprnInfoData(dto);
        // 查询医师代码
        List<DrCodgVo> codeVos = doctorCodeConfigMapper.queryDoctorCodeInfo(new DoctorCodeDto(dto.getHospitalId()));
        if (ValidateUtil.isEmpty(codeVos)) {
            throw new AppException("未查询到医师编码");
        }
        // 查询护士编码
        List<NurseCodeVo> nurseCodeVos = nurseCodeConfigMapper.selectAll(new NurseCodeDto(dto.getHospitalId()));
        if (ValidateUtil.isEmpty(nurseCodeVos)) {
            throw new AppException("未查询到护士编码");
        }
        // 查询科室编码
        SomDeptConfigDto somDeptConfigDto = new SomDeptConfigDto();
        somDeptConfigDto.setHospital_id(dto.getHospitalId());
        List<SomDeptConfigVo> somDeptConfigVos = deptCrspConfigMapper.queryDeptTransCode(somDeptConfigDto);

        // 处理手术信息
        if (ValidateUtil.isNotEmpty(oprnInfoVos)) {
            List<OprnInfoVo> removeVoList = new ArrayList<>();
            for (OprnInfoVo oprnInfoVo : oprnInfoVos) {
                if (NULL_.equals(oprnInfoVo.getOprn_oprt_code())) {
                    removeVoList.add(oprnInfoVo);
                }
            }
            oprnInfoVos.removeAll(removeVoList);
        }
        // 清单重症信息
        List<IcuInfoVo> icuInfoVos = listUploadMapper.queryIcuInfoData(dto);
        List<IcuInfoVo> icuRemoveVos = new ArrayList<>();
        // 如果重症全部为空则删除信息，表示当前数据为空
        icuInfoVos.forEach(icuInfoVo -> {
            if (ValidateUtil.isEmpty(icuInfoVo.getScs_cutd_exit_time()) &&
                    ValidateUtil.isEmpty(icuInfoVo.getScs_cutd_inpool_time()) &&
                    ValidateUtil.isEmpty(icuInfoVo.getScs_cutd_sum_dura()) &&
                    ValidateUtil.isEmpty(icuInfoVo.getScs_cutd_ward_type())) {
                icuRemoveVos.add(icuInfoVo);
            } else {
                // icu时间不做置空处理
                //icuInfoVo.setScs_cutd_sum_dura(null);
            }
        });
        if (ValidateUtil.isNotEmpty(icuRemoveVos)) {
            icuInfoVos.removeAll(icuRemoveVos);
        }
        if (ValidateUtil.isEmpty(setlInfoVos)) {
            throw new AppException("未查询到需要上传信息！");
        }
        // 输血信息
        List<TransfusionVo> transfusionVos = listUploadMapper.queryTransfusionData(dto);
        // 输血转码
        List<TransfusionCodeVo> transfusionCodeVos = transfusionCodeConfigMapper.queryTransfusionCodeInfo(new TransfusionCodeDto(dto.getHospitalId()));
        List<TransfusionVo> transfusionRemoveVos = new ArrayList<>();
        // 如果输血全部为空则删除信息，表示当前数据为空
        transfusionVos.forEach(transfusionVo -> {
            if (ValidateUtil.isEmpty(transfusionVo.getBld_amt()) &&
                    ValidateUtil.isEmpty(transfusionVo.getBld_cat()) &&
                    ValidateUtil.isEmpty(transfusionVo.getBld_unt())) {
                transfusionRemoveVos.add(transfusionVo);
            }
        });
        if (ValidateUtil.isNotEmpty(transfusionRemoveVos)) {
            transfusionVos.removeAll(transfusionRemoveVos);
        }
        //添加科室编码对照，转换
        transferCode(oprnInfoVos, codeVos, setlInfoVos, nurseCodeVos, transfusionVos, transfusionCodeVos, somDeptConfigVos);

        // 上传明细
        List<SettleListUploadLogVo> uploadLogVoList = new ArrayList<>();
        List<SettleListUploadLogVo> uploadLog4101AVoList = new ArrayList<>();
        UUID uuid = UUID.randomUUID();

        String hospitalId = null;
        SettleListConfigVo configVo;
        // 循环上传
        for (SetlInfoVo setlInfoVo : setlInfoVos) {
            //根据基础信息中的清算机构统筹区，获取对应的configVo
            configVo = settleListConfigVoMap.get(setlInfoVo.getInsuplcAdmdvs());
            hospitalId = configVo.getMsgid().substring(0,12);
            // 设置上传日志信息
            SettleListUploadLogVo uploadLog4101AVo = new SettleListUploadLogVo();
            uploadLog4101AVo.setK00(setlInfoVo.getK00());
            uploadLog4101AVo.setSettleListId(setlInfoVo.getSettleListId().toString());
            uploadLog4101AVo.setBatchNum(uuid.toString());
            uploadLog4101AVo.setUpldStas(DrgConst.START_FLAG_1);
            uploadLog4101AVo.setStasType(setlInfoVo.getStas_type());

            // 设置配置信息
            // 设置发送报文ID
            String curTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
            configVo.setMsgid(hospitalId + curTime + ORDER_NO);
            configVo.setInf_time(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            Map configMap = MapUtil.objectToMap(configVo, false, true);
            configMap.remove("url");

            Map<String, Object> params4101A = new LinkedHashMap<>();
            // 设置配置信息
            params4101A.putAll(configMap);
            // 设置交易编号
            params4101A.put(SettleListUploadUtil.INFO_NO, SettleListUploadUtil.INFO_NO_4101A);
            Map<String, Object> inputNode = new LinkedHashMap<>();

            try {
                List<DiseInfoVo> disList = settleListValidateJobService.find(diseInfoVos, setlInfoVo.getSettleListId());
                List<OprnInfoVo> oprList = settleListValidateJobService.find(oprnInfoVos, setlInfoVo.getSettleListId());
                // 设置基本信息
                Map setlinfoMap = MapUtil.objectToMap(setlInfoVo, false, true);
                // 将-转为空
                transfusionToNull(setlinfoMap, notConvertFieldsList);
                setlinfoMap.remove(REMOVE_ID_KEY);
                setlinfoMap.remove("k00");
                setlinfoMap.remove("a11");
                setlinfoMap.remove("a20");
                inputNode.put("setlinfo", setlinfoMap);
                // 设置门慢门特信息
                addInputNodeInfo("opspdiseinfo", toMapList(settleListValidateJobService.find(opspDiseInfoVos, setlInfoVo.getSettleListId())), inputNode);
                // 设置诊断信息
                addInputNodeInfo("diseinfo", toMapList(disList), inputNode);
                // 设置手术信息
                addInputNodeInfo("oprninfo", toMapList(oprList), inputNode);
                // 设置重症信息
                addInputNodeInfo("icuinfo", toMapList(settleListValidateJobService.find(icuInfoVos, setlInfoVo.getSettleListId())), inputNode);
                // 设置输血信息
                addInputNodeInfo("bldinfo", toMapList(settleListValidateJobService.find(transfusionVos, setlInfoVo.getSettleListId())), inputNode);
                // 设置input节点信息INPUT_NODE_NAME
                params4101A.put(SettleListUploadUtil.INPUT_NODE_NAME, inputNode);
            } catch (Exception e) {
                e.printStackTrace();
                throw new AppException("设置清单对应信息失败");
            }

            // 获取9001
            Map response9001Map = SettleListUploadUtil.get9001Params(configMap, configVo);
            Object signNo = SettleListUploadUtil.getResByKey(response9001Map,
                    "output:signinoutb:sign_no", SettleListUploadUtil.INFO_NO_9001, setlInfoVo);
            params4101A.put(SettleListUploadUtil.SIGN_NO_KEY, signNo);
            configVo.setSign_no(signNo.toString());


            // 获取1101
//            Map response1101Map = SettleListUploadUtil.get1101Params(configMap, configVo, setlInfoVo);
//            Object insuplcAdmdvs = SettleListUploadUtil.getResByKey(response1101Map,
//                    "output:insuinfo:insuplc_admdvs", SettleListUploadUtil.INFO_NO_1101, setlInfoVo);
            if (!ValidateUtil.isEmpty(setlInfoVo.getInsuredPlace()) && setlInfoVo.getInsuredPlace().length() >= 6) {
                params4101A.put(INS_AREA_KEY, setlInfoVo.getInsuredPlace().substring(0, 6));
            }else if (!ValidateUtil.isEmpty(setlInfoVo.getSetl_id())) {
                params4101A.put(INS_AREA_KEY, setlInfoVo.getSetl_id().substring(0, 6));
            } else {
                params4101A.put(INS_AREA_KEY, configVo.getInsuplc_admdvs());
            }
            // 上传(4101A)
            uploadLog4101AVo.setReqtPara(mapToString(params4101A));
            String upload4101AResponse = HttpRequestUtil.post(params4101A, configVo.getUrl());
            Map response4101AMap = JSON.parseObject(JSON.parse(upload4101AResponse).toString(), Map.class);
            log.info("[4101A]请求返回结果：{}", upload4101AResponse);
            if (SettleListUploadUtil.REQUEST_SUCCESS.equals(response4101AMap.get(SettleListUploadUtil.REQUEST_SUCCESS_KEY).toString())) {
                log.info("[4101A]请求成功，返回结果: {}", response4101AMap);
                uploadLog4101AVo.setResSettleListNo(Optional.ofNullable(SettleListUploadUtil.responseMapGet(response4101AMap, "output:setl_list_id")).orElse("").toString());
            } else {
                log.info("[4101A]请求失败，病案号：{}，返回结果: {}", setlInfoVo.getMedcasno(), response4101AMap.get(SettleListUploadUtil.REQUEST_ERROR).toString());
                uploadLog4101AVo.setTrns_no(SettleListUploadUtil.INFO_NO_4101A);
                uploadLog4101AVo.setUpldStas(DrgConst.START_FLAG_0);
                uploadLog4101AVo.setErrMsg(response4101AMap.get(SettleListUploadUtil.REQUEST_ERROR).toString());
            }
            uploadLog4101AVoList.add(uploadLog4101AVo);
        }

        // 加入4101A数据
        if (ValidateUtil.isNotEmpty(uploadLog4101AVoList)) {
            uploadLogVoList.addAll(uploadLog4101AVoList);
        }
        // 新增操作记录
        ListUploadVo listUploadVo = new ListUploadVo();
        listUploadVo.setBatchNum(uuid.toString());
        Integer succCnt = 0;
        Integer faleCnt = 0;
        for (SettleListUploadLogVo uploadLogVo : uploadLog4101AVoList) {
            if (DrgConst.START_FLAG_1.equals(uploadLogVo.getUpldStas())) {
                succCnt++;
            } else {
                faleCnt++;
            }
        }
        listUploadVo.setUserName(dto.getNknm());
        listUploadVo.setSuccCnt(succCnt.toString());
        listUploadVo.setFaleCnt(faleCnt.toString());
        listUploadMapper.insertUploadOpeLog(listUploadVo);
        // 删除操作明细
        if (ValidateUtil.isNotEmpty(dto.getK00s())) {
            listUploadMapper.deleteUploadLog(dto);
        }
        // 新增操作明细
        listUploadMapper.insertUploadLog(uploadLogVoList);

        //根据上传结果更新对应业务主表上传状态
        if (ValidateUtil.isNotEmpty(uploadLogVoList)) {
            for (SettleListUploadLogVo settleListUploadLogVo :
                    uploadLogVoList) {
                //循环根据每条数据更新对应主表上传状态
                listUploadMapper.updateDataBySetlId(settleListUploadLogVo);
            }
        }

        // 设置返回结果
        if (faleCnt > 0) {
            res.put(UPLOAD_STATE_KEY, UPLOAD_STATE_FAIL);
            res.put("errMsg", "存在上传失败数据，请及时查看,上传成功数量：" + succCnt + "，上传失败数量：" + faleCnt);
        }
    }

    public static int no = 1000;
//    /**
//     * 上传市医保
//     *
//     * @param dto
//     */
//    private void sybJsonAndUpload(ListUploadDto dto, Map<String, Object> res) {
//        // 获取清单配置信息
//        SettleListSybConfigVo configVo = listUploadMapper.querySettleSybConfig(dto);
//        // 获取清单校验配置信息，获取那些字段不需要转换为null
//        List<String> notConvertFieldsList = getNotConvertFields();
//        // 查询医院id
//        List<ListUploadDto> hospId = listUploadMapper.queryHospId();
//        // 清单诊断信息
//        List<DiseInfoVo> diseInfoVos = listUploadMapper.queryDiseInfoData(dto);
//        // 处理诊断信息
//        if (ValidateUtil.isNotEmpty(diseInfoVos)) {
//            List<DiseInfoVo> removeVoList = new ArrayList<>();
//            for (DiseInfoVo diseInfoVo : diseInfoVos) {
//                if (NULL_.equals(diseInfoVo.getDiag_code())) {
//                    removeVoList.add(diseInfoVo);
//                }
//            }
//            diseInfoVos.removeAll(removeVoList);
//        }
//
//        // 清单手术信息
//        List<OprnSybInfoVo> oprnInfoVos = listUploadMapper.queryOprnSybInfoData(dto);
//        List<OprnSybInfoVo> modifedOprnInfoVos = new ArrayList<>();
//        for (OprnSybInfoVo vo : oprnInfoVos) {
//            // 处理麻醉开始时间
//            if (ValidateUtil.isNotEmpty(vo.getAnst_time_begin())) {
//                if (vo.getAnst_time_begin().length() == 10) {
//                    String dclaDate = vo.getAnst_time_begin();
//                    String dclaDateTime = dclaDate + " 00:00:00";
//                    vo.setAnst_time_begin(dclaDateTime);
//                }
//            }
//            // 处理麻醉结束时间
//            if (ValidateUtil.isNotEmpty(vo.getAnst_time_end())) {
//                if (vo.getAnst_time_end().length() == 10) {
//                    String dclaDate = vo.getAnst_time_end();
//                    String dclaDateTime = dclaDate + " 00:00:00";
//                    vo.setAnst_time_end(dclaDateTime);
//                }
//            }
//            modifedOprnInfoVos.add(vo);
//        }
//        oprnInfoVos = modifedOprnInfoVos;
//        // 处理手术信息
//        if (ValidateUtil.isNotEmpty(oprnInfoVos)) {
//            List<OprnSybInfoVo> removeVoList = new ArrayList<>();
//            for (OprnSybInfoVo oprnInfoVo : oprnInfoVos) {
//                if (NULL_.equals(oprnInfoVo.getOprn_oprt_code())) {
//                    removeVoList.add(oprnInfoVo);
//                }
//            }
//            oprnInfoVos.removeAll(removeVoList);
//        }
//
//        // 清单基本信息
//        List<SetLSybInfoVo> setlInfoVos = listUploadMapper.querySetlSybInfoData(dto);
//        List<SetLSybInfoVo> modifiedSetlInfoVos = new ArrayList<>();
//        //调整字段不一致的值
//        for (SetLSybInfoVo vo : setlInfoVos) {
//            // 判断国籍编码
//            if (ValidateUtil.isNotEmpty(vo.getNtly())) {
//                if (vo.getNtly().equals("156")) {
//                    vo.setNtly("CHN");
//                }
//            }
//            // 处理医院ID
//            if (!ValidateUtil.isNotEmpty(vo.getFixmedins_code())) {
//                for (ListUploadDto listUploadDto : hospId) {
//                    vo.setFixmedins_code(listUploadDto.getHospitalId());
//                    if (!ValidateUtil.isNotEmpty(vo.getFixmedins_name())) {
//                        vo.setFixmedins_name(listUploadDto.getMedinsName());
//                    }
//                }
//            }
//            // 设置主诊断标志
//            vo.setMaindiag_flag("1");
//            // 判断医保基金付费方式
//            if (ValidateUtil.isNotEmpty(vo.getHi_type())) {
//                // 02 居民
//                if (vo.getHi_type().equals("02") || vo.getHi_type().equals("2")) {
//                    vo.setHi_type("390");
//                    // 01 职工
//                } else if (vo.getHi_type().equals("01") || vo.getHi_type().equals("1")) {
//                    vo.setHi_type("310");
//                    // 其他
//                } else {
//                    vo.setHi_type("999");
//                }
//            } else {
//                vo.setHi_type("999");
//            }
//            // 处理新生儿体重
//            if (ValidateUtil.isNotEmpty(vo.getNwb_bir_wt())) {
//                if (vo.getNwb_bir_wt().equals("0") || vo.getNwb_bir_wt().equals("0.0")) {
//                    vo.setNwb_bir_wt("");
//                } else {
//                    BigDecimal nwbBirWt = new BigDecimal(vo.getNwb_bir_wt());
//                    vo.setNwb_bir_wt(nwbBirWt.setScale(0, BigDecimal.ROUND_HALF_UP).toString());
//                }
//            }
//            if (ValidateUtil.isNotEmpty(vo.getNwb_adm_wt())) {
//                if (vo.getNwb_adm_wt().equals("0") || vo.getNwb_adm_wt().equals("0.0")) {
//                    vo.setNwb_adm_wt("");
//                } else {
//                    BigDecimal nwbAdmWt = new BigDecimal(vo.getNwb_adm_wt());
//                    vo.setNwb_adm_wt(nwbAdmWt.setScale(0, BigDecimal.ROUND_HALF_UP).toString());
//                }
//            }
//            // 申报时间格式
//            if (ValidateUtil.isNotEmpty(vo.getDcla_time())) {
//                if (vo.getDcla_time().length() == 10) {
//                    // 假设 vo.getDcla_time() 返回的是一个字符串类型的日期值，格式为 "yyyy-MM-dd"
//                    String dclaDate = vo.getDcla_time();
//                    // 添加默认时间 "00:00:00"
//                    String dclaDateTime = dclaDate + " 00:00:00";
//                    vo.setDcla_time(dclaDateTime);
//                }
//            }
//
//            // 出院时间
//            if (ValidateUtil.isNotEmpty(vo.getDscg_time())) {
//                if (vo.getDscg_time().length() == 10) {
//                    // 假设 vo.getDcla_time() 返回的是一个字符串类型的日期值，格式为 "yyyy-MM-dd"
//                    String dclaDate = vo.getDscg_time();
//                    // 添加默认时间 "00:00:00"
//                    String dclaDateTime = dclaDate + " 00:00:00";
//                    vo.setDscg_time(dclaDateTime);
//                }
//            }
//            // 入院时间
//            if (ValidateUtil.isNotEmpty(vo.getAdm_time())) {
//                if (vo.getAdm_time().length() == 10) {
//                    // 假设 vo.getDcla_time() 返回的是一个字符串类型的日期值，格式为 "yyyy-MM-dd"
//                    String dclaDate = vo.getAdm_time();
//                    // 添加默认时间 "00:00:00"
//                    String dclaDateTime = dclaDate + " 00:00:00";
//                    vo.setAdm_time(dclaDateTime);
//                }
//            }
//            // 结算开始时间
//            if (ValidateUtil.isNotEmpty(vo.getSetl_begn_date())) {
//                if (vo.getSetl_begn_date().length() > 10) {
//                    // 假设 vo.getDcla_time() 返回的是一个字符串类型的日期值，格式为 "yyyy-MM-dd"
//                    String dclaDate = vo.getSetl_begn_date();
//                    // 添加默认时间 "00:00:00"
//                    String dclaDateTime = dclaDate.substring(0, 10);
//                    vo.setSetl_begn_date(dclaDateTime);
//                }
//            }
//            // 结算结束时间
//            if (ValidateUtil.isNotEmpty(vo.getSetl_end_date())) {
//                if (vo.getSetl_end_date().length() > 10) {
//                    // 假设 vo.getDcla_time() 返回的是一个字符串类型的日期值，格式为 "yyyy-MM-dd"
//                    String dclaDate = vo.getSetl_end_date();
//                    // 添加默认时间 "00:00:00"
//                    String dclaDateTime = dclaDate.substring(0, 10);
//                    vo.setSetl_end_date(dclaDateTime);
//                }
//            }
//
//            // 出生年月日
//            if (ValidateUtil.isNotEmpty(vo.getBrdy())) {
//                if (vo.getBrdy().length() > 10) {
//                    // 假设 vo.getDcla_time() 返回的是一个字符串类型的日期值，格式为 "yyyy-MM-dd"
//                    String dclaDate = vo.getBrdy();
//                    // 添加默认时间 "00:00:00"
//                    String dclaDateTime = dclaDate.substring(0, 10);
//                    vo.setBrdy(dclaDateTime);
//                }
//            }
//
//            // 还需补充 X病种收费 84-89
//            modifiedSetlInfoVos.add(vo);
//        }
//        setlInfoVos = modifiedSetlInfoVos;
//        // 清单基金支付信息
//        List<PayInfoVo> payInfoVos = listUploadMapper.queryPayInfoData(dto);
//        // 处理基金支付信息
//        if (ValidateUtil.isNotEmpty(payInfoVos)) {
//            List<PayInfoVo> payInfoVoList = new ArrayList<>();
//            for (PayInfoVo payInfoVo : payInfoVos) {
//                if (ValidateUtil.isEmpty(payInfoVo.getFund_pay_type()) && ValidateUtil.isEmpty(payInfoVo.getFund_payamt())) {
//                    payInfoVoList.add(payInfoVo);
//                }
//            }
//            payInfoVos.removeAll(payInfoVoList);
//        }
//        // 清单门慢门特信息
//        List<OpspDiseInfoVo> opspDiseInfoVos = listUploadMapper.querySybOpspDiseInfoData(dto);
//        List<OpspDiseInfoVo> opspDiseInfoVos1 = new ArrayList<>();
//
//        // 添加主诊断标志
//        if (ValidateUtil.isNotEmpty(opspDiseInfoVos)) {
//            for (OpspDiseInfoVo vo : opspDiseInfoVos) {
//                vo.setMaindiag_flag("1");
//                opspDiseInfoVos1.add(vo);
//            }
//            opspDiseInfoVos = opspDiseInfoVos1;
//        }
//
//        // 处理门慢门特
//        if (ValidateUtil.isNotEmpty(opspDiseInfoVos)) {
//            List<OpspDiseInfoVo> removeVoList = new ArrayList<>();
//            for (OpspDiseInfoVo opspDiseInfoVo : opspDiseInfoVos) {
//                if (ValidateUtil.isEmpty(opspDiseInfoVo.getOprn_oprt_code()) && ValidateUtil.isEmpty(opspDiseInfoVo.getOprn_oprt_name())) {
//                    removeVoList.add(opspDiseInfoVo);
//                }
//            }
//            opspDiseInfoVos.removeAll(removeVoList);
//        }
//
//        // 清单收费项目信息
//        List<ItemInfoVo> itemInfoVos = listUploadMapper.queryItemSybInfoData(dto);
//        // 处理收费项目信息
//        if (ValidateUtil.isNotEmpty(itemInfoVos)) {
//            List<ItemInfoVo> infoVoList = new ArrayList<>();
//            for (ItemInfoVo itemInfoVo : itemInfoVos) {
//                if (ValidateUtil.isEmpty(itemInfoVo.getAmt()) && ValidateUtil.isEmpty(itemInfoVo.getClab_amt())
//                        && ValidateUtil.isEmpty(itemInfoVo.getClaa_sumfee()) && ValidateUtil.isEmpty(itemInfoVo.getMed_chrgitm())
//                        && ValidateUtil.isEmpty(itemInfoVo.getFulamt_ownpay_amt()) && ValidateUtil.isEmpty(itemInfoVo.getOth_amt())) {
//                    infoVoList.add(itemInfoVo);
//                }
//            }
//            itemInfoVos.removeAll(infoVoList);
//        }
//
//        // 查询医师代码
//        List<DrCodgVo> codeVos = doctorCodeConfigMapper.queryDoctorCodeInfo(new DoctorCodeDto(dto.getHospitalId()));
//        if (ValidateUtil.isEmpty(codeVos)) {
//            throw new AppException("未查询到医师编码");
//        }
//        // 查询护士编码
//        List<NurseCodeVo> nurseCodeVos = nurseCodeConfigMapper.selectAll(new NurseCodeDto(dto.getHospitalId()));
//        if (ValidateUtil.isEmpty(nurseCodeVos)) {
//            throw new AppException("未查询到护士编码");
//        }
//        // 清单重症信息
//        List<IcuInfoVo> icuInfoVos = listUploadMapper.queryIcuInfoData(dto);
//        List<IcuInfoVo> icuRemoveVos = new ArrayList<>();
//        // 如果重症全部为空则删除信息，表示当前数据为空
//        icuInfoVos.forEach(icuInfoVo -> {
//            if (ValidateUtil.isEmpty(icuInfoVo.getScs_cutd_exit_time()) &&
//                    ValidateUtil.isEmpty(icuInfoVo.getScs_cutd_inpool_time()) &&
//                    ValidateUtil.isEmpty(icuInfoVo.getScs_cutd_sum_dura()) &&
//                    ValidateUtil.isEmpty(icuInfoVo.getScs_cutd_ward_type())) {
//                icuRemoveVos.add(icuInfoVo);
//            } else {
//                icuInfoVo.setScs_cutd_sum_dura(formatHour(icuInfoVo.getScs_cutd_sum_dura()));
//            }
//        });
//        if (ValidateUtil.isNotEmpty(icuRemoveVos)) {
//            icuInfoVos.removeAll(icuRemoveVos);
//        }
//        if (ValidateUtil.isEmpty(setlInfoVos)) {
//            throw new AppException("未查询到需要上传信息！");
//        }
//        // 输血信息
//        List<TransfusionSybVo> transfusionVos = listUploadMapper.querySybTransfusionData(dto);
//
//        // 输血转码
//        List<TransfusionCodeVo> transfusionCodeVos = transfusionCodeConfigMapper.queryTransfusionCodeInfo(new TransfusionCodeDto(dto.getHospitalId()));
//        List<TransfusionSybVo> transfusionRemoveVos = new ArrayList<>();
//        // 如果输血全部为空则删除信息，表示当前数据为空
//        transfusionVos.forEach(transfusionVo -> {
//            if (ValidateUtil.isEmpty(transfusionVo.getBld_cat())) {
//                transfusionRemoveVos.add(transfusionVo);
//            }
//        });
//        if (ValidateUtil.isNotEmpty(transfusionRemoveVos)) {
//            transfusionVos.removeAll(transfusionRemoveVos);
//        }
//        transferSybCode(oprnInfoVos, codeVos, setlInfoVos, nurseCodeVos, transfusionVos, transfusionCodeVos);
//
//        // 上传明细
//        List<SettleListUploadLogVo> uploadLogVoList = new ArrayList<>();
//        List<SettleListUploadLogVo> uploadLogSybVoList = new ArrayList<>();
//        UUID uuid = UUID.randomUUID();
//
//        // 循环上传
//        for (SetLSybInfoVo setLSybInfoVo : setlInfoVos) {
//            // 设置上传日志信息
//            SettleListUploadLogVo uploadLogSybVo = new SettleListUploadLogVo();
//            uploadLogSybVo.setK00(setLSybInfoVo.getK00());
//            uploadLogSybVo.setSettleListId(setLSybInfoVo.getSettleListId().toString());
//            uploadLogSybVo.setBatchNum(uuid.toString());
//            uploadLogSybVo.setUpldStas(DrgConst.START_FLAG_1);
//            uploadLogSybVo.setStasType(setLSybInfoVo.getStas_type());
//
//            // 设置配置信息
//            Map configMap = MapUtil.objectToMap(configVo, false, false);
//            configMap.remove("url");
//
//
//            Map<String, Object> inputNode = new LinkedHashMap<>();
//            Map<String, Object> paramsSyb = new LinkedHashMap<>();
//
//            try {
//                // 设置诊断信息
//                List<DiseInfoVo> disList = settleListValidateJobService.find(diseInfoVos, setLSybInfoVo.getSettleListId());
//                // 设置手术信息
//                List<OprnSybInfoVo> oprList = settleListValidateJobService.find(oprnInfoVos, setLSybInfoVo.getSettleListId());
//                // 设置门慢门特信息
//                List<OpspDiseInfoVo> opspDiseInfoVoList = settleListValidateJobService.find(opspDiseInfoVos, setLSybInfoVo.getSettleListId());
//                // 设置基金支付信息
//                List<PayInfoVo> payInfoVoList = settleListValidateJobService.find(payInfoVos, setLSybInfoVo.getSettleListId());
//                // 设置收费项目信息
//                List<ItemInfoVo> itemInfoVoList = settleListValidateJobService.find(itemInfoVos, setLSybInfoVo.getSettleListId());
//                // 设置icu
//                List<IcuInfoVo> icuInfoVoList = settleListValidateJobService.find(icuInfoVos, setLSybInfoVo.getSettleListId());
//                // 设置输血
//                List<TransfusionSybVo> transfusionSybVoList = settleListValidateJobService.find(transfusionVos, setLSybInfoVo.getSettleListId());
//
//                // 设置基本信息
//                Map setlinfoMap = MapUtil.objectToMap(setLSybInfoVo, false, true);
//                // 将-转为空
//                transfusionToNull(setlinfoMap, notConvertFieldsList);
//                setlinfoMap.remove(REMOVE_ID_KEY);
//                setlinfoMap.remove("k00");
//                inputNode.put("setlinfo", setlinfoMap);
//                // 设置诊断节点信息
//                addInputNodeInfo1("diseinfo", toMapList(disList), inputNode);
//                // 设置手术节点信息
//                addInputNodeInfo1("oprninfo", toMapList(oprList), inputNode);
//                // 设置门慢门特节点信息
//                addInputNodeInfo1("opspdiseinfo", toMapList(opspDiseInfoVoList), inputNode);
//                // 设置基金支付节点信息
//                addInputNodeInfo1("payinfo", toMapList(payInfoVoList), inputNode);
//                // 设置收费项目信息
//                addInputNodeInfo1("iteminfo", toMapList(itemInfoVoList), inputNode);
//                // 设置icu节点信息
//                addInputNodeInfo1("icuinfo", toMapList(icuInfoVoList), inputNode);
//                // 设置输血节点信息
//                addInputNodeInfo1("bloodinfo", toMapList(transfusionSybVoList), inputNode);
//                // 设置input节点信息
//                paramsSyb.put(SettleListUploadUtil.INPUT_NODE_NAME, inputNode);
//            } catch (Exception e) {
//                e.printStackTrace();
//                throw new AppException("设置清单对应信息失败");
//            }
////            // 上传
//            uploadLogSybVo.setReqtPara("MED_SETTLE_INFO=" + mapToString(paramsSyb));
//            System.out.println(uploadLogSybVo.getReqtPara());
//            String respose = sendData(uploadLogSybVo.getReqtPara(), configVo);
//            Map returnVo = JSON.parseObject(JSON.parse(respose).toString(), Map.class);
//            if ("200".equals(returnVo.get("CODE").toString())) {
//                log.info("[市医保]请求成功，返回结果: {}", returnVo);
//                uploadLogSybVo.setResSettleListNo(Optional.ofNullable(SettleListUploadUtil.responseMapGet(returnVo, "DATA:QDLSH")).orElse("").toString());
//            } else {
//                log.info("[市医保]请求失败，病案号: {}， 返回结果: {}", setLSybInfoVo.getMedcasno(), returnVo.get("FAILDATA").toString());
//                uploadLogSybVo.setTrns_no("市医保");
//                uploadLogSybVo.setUpldStas(DrgConst.START_FLAG_0);
//                uploadLogSybVo.setErrMsg(returnVo.get("FAILDATA").toString());
//            }
//            uploadLogSybVoList.add(uploadLogSybVo);
//        }
//        // 加入4101A数据
//        if (ValidateUtil.isNotEmpty(uploadLogSybVoList)) {
//            uploadLogVoList.addAll(uploadLogSybVoList);
//        }
//        // 新增操作记录
//        ListUploadVo listUploadVo = new ListUploadVo();
//        listUploadVo.setBatchNum(uuid.toString());
//        Integer succCnt = 0;
//        Integer faleCnt = 0;
//        for (SettleListUploadLogVo uploadLogVo : uploadLogSybVoList) {
//            if (DrgConst.START_FLAG_1.equals(uploadLogVo.getUpldStas())) {
//                succCnt++;
//            } else {
//                faleCnt++;
//            }
//        }
//        listUploadVo.setUserName(dto.getNknm());
//        listUploadVo.setSuccCnt(succCnt.toString());
//        listUploadVo.setFaleCnt(faleCnt.toString());
//        listUploadMapper.insertUploadSybOpeLog(listUploadVo);
//        // 删除操作明细
//        if (ValidateUtil.isNotEmpty(dto.getK00s())) {
//            listUploadMapper.deleteUploadSybLog(dto);
//        }
//        // 新增上传日志明细
//        listUploadMapper.insertUploadSybLog(uploadLogVoList);
//
//        // 设置返回结果
//        if (faleCnt > 0) {
//            res.put(UPLOAD_STATE_KEY, UPLOAD_STATE_FAIL);
//            res.put("errMsg", "存在上传失败数据，请及时查看,上传成功数量：" + succCnt + "，上传失败数量：" + faleCnt);
//        }
//    }

    /**
     * 上传市医保，通过省平台将下载数据再传入市医保
     * @param dto
     */
    /**
     * 上传市医保，通过省平台将下载数据再传入市医保
     * @param dto
     */
    private void sybJsonAndUpload1(ListUploadDto dto,Map<String, Object> res)  {
        // 获取两定平台接口配置
        SettleListConfigVo config = listUploadMapper.querySettleConfig(dto).get(0);
        // 清单基本信息
        List<SetLSybInfoVo> setlInfoVos = listUploadMapper.querySetlSybInfoData(dto);
        // 上传明细
        List<SettleListUploadLogVo> uploadLogVoList = new ArrayList<>();
        List<SettleListUploadLogVo> uploadLogSybVoList = new ArrayList<>();
        UUID uuid = UUID.randomUUID();

        Map<String, List<SetLSybInfoVo>> collect = setlInfoVos.stream().collect(Collectors.groupingBy(SetLSybInfoVo::getFixmedins_code));
        collect.forEach((s, setLSybInfoVos)  -> {
            // 批次信息
            Map<String, Object> batchMap = new HashMap<>();
            batchMap.put("fixmedins_code", s);
            batchMap.put("setl_mon", setLSybInfoVos.get(0).getSetl_mon());
            batchMap.put("trans_stas", "1");
            batchMap.put("data_ct", setLSybInfoVos.size());
            String batchInfo = null;
            CityBatchVo cityBatchVo = null;
            try {
                batchInfo = HttpRequestUtil.post(batchMap, SettleListUploadUtil.UPLOAD_URL_C_BATCH, getHeaders());
                cityBatchVo = JSON.parseObject(JSON.parse(batchInfo).toString(), CityBatchVo.class);
                log.info("===批次信息接口-获取批次号===: {}", batchInfo);
            } catch (Exception e) {
                log.error("获取headers失败：{}", e.getMessage());
            }
            if(cityBatchVo == null){
                throw new AppException("获取批次信息失败");
            }

            Map<String, Object> data = cityBatchVo.getData();
            Object batchNum = data.get("batch_num");
            log.info("=====雅安批次号：{}",batchNum);

            // 循环上传
            for (SetLSybInfoVo setLSybInfoVo : setLSybInfoVos) {
                // 设置上传日志信息
                SettleListUploadLogVo uploadLogSybVo = new SettleListUploadLogVo();
                try {
                    uploadLogSybVo.setK00(setLSybInfoVo.getK00());
                    uploadLogSybVo.setSettleListId(setLSybInfoVo.getSettleListId().toString());
                    uploadLogSybVo.setBatchNum(uuid.toString());
                    uploadLogSybVo.setUpldStas(DrgConst.START_FLAG_1);
                    uploadLogSybVo.setStasType(setLSybInfoVo.getStas_type());
                    // 设置发送报文ID
                    String curTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
                    // 省上地址
                    config.setUrl(SettleListUploadUtil.UPLOAD_URL_P);
                    config.setMsgid(setLSybInfoVo.getFixmedins_code() + curTime + no);
                    String curDate = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                    config.setInf_time(curDate);
                    Map configMap = MapUtil.objectToMap(config, false , true);

                    // 请求外层
                    Map<String,Object> reqParams = new LinkedHashMap<>();
                    // 设置配置信息
                    reqParams.putAll(configMap);
                    log.info("[雅安4103]configMap：{}", configMap);
                    // 获取9001
                    Map response9001Map = SettleListUploadUtil.get9001Params(configMap, config);
                    Object signNo = SettleListUploadUtil.getResByKey(response9001Map,
                            "output:signinoutb:sign_no", SettleListUploadUtil.INFO_NO_9001);
                    log.info("[雅安4103]response9001Map：{}", response9001Map);
                    log.info("[雅安4103]signNo：{}", signNo);
                    reqParams.put(SettleListUploadUtil.SIGN_NO_KEY, signNo);
                    config.setSign_no(signNo.toString());
                    if (ValidateUtil.isNotEmpty(setLSybInfoVo.getMdtrt_id()) &&
                            ValidateUtil.isNotEmpty(setLSybInfoVo.getPsn_no()) &&
                            ValidateUtil.isNotEmpty(setLSybInfoVo.getSetl_id())) {
                        reqParams.put(SettleListUploadUtil.INPUT_NODE_NAME, new HashMap<String, Object>(){
                            {
                                put("data", new HashMap<String, Object>(){
                                    {
                                        put("psn_no", setLSybInfoVo.getPsn_no());
                                        put("setl_id", setLSybInfoVo.getSetl_id());
                                        put("mdtrt_id", setLSybInfoVo.getMdtrt_id());
                                    }
                                });
                            }
                        });

                        if (batchNum != null) {
                            UploadDto uploadDto = new UploadDto();
                            uploadDto.setFixmedins_code(setLSybInfoVo.getFixmedins_code());
                            uploadDto.setSetl_mon(setLSybInfoVo.getSetl_mon());
                            uploadDto.setBatch_num(batchNum.toString());
                            uploadDto.setNum(1);

                            // 4103 查询结算清单
                            reqParams.put(SettleListUploadUtil.INFO_NO, SettleListUploadUtil.INFO_NO_4103);
                            log.info("[雅安4103]reqParams：{}", reqParams);
                            String r4103 = HttpRequestUtil.post(reqParams, config.getUrl(), null);
                            log.info("[雅安4103]r4103：{}", r4103);

                            Map response4103Map = JSON.parseObject(JSON.parse(r4103).toString(), Map.class);
                            uploadLogSybVo.setErrMsg(response4103Map.get("err_msg") +"");
                            log.info("[4103]返回结果：{}", response4103Map);
                            Map o4103Map = (Map) response4103Map.get("output");
                            Map<String, Object> seltinfo = (Map<String, Object>) o4103Map.get("setlinfo");
                            Map<String, Object> baseInfo = convertObjectToMap(setLSybInfoVo);
                            if (!ValidateUtil.isEmpty(baseInfo)) {
                                // 入院科别
                                Object admCaty = baseInfo.get("adm_caty");
                                if (!ValidateUtil.isEmpty(admCaty)) {
                                    // 转码
                                    seltinfo.put("adm_caty", convertCode(admCaty));
                                }
                                // 出院科别
                                Object dscgCaty = baseInfo.get("dscg_caty");
                                if (!ValidateUtil.isEmpty(dscgCaty)) {
                                    seltinfo.put("dscg_caty", convertCode(dscgCaty));
                                }
                                // 调整清单结算开始时间为入院时间
                                Object adm_time = baseInfo.get("adm_time");
                                seltinfo.put("setl_begndate", adm_time);
                                // 调整清单结算结束时间为出院时间
                                Object dscg_time = baseInfo.get("dscg_time");
                                seltinfo.put("setl_enddate", dscg_time);
                                seltinfo.put("hi_paymtd", "1");
                            }
                            // 处理申报时间
                            seltinfo.put("dcla_time", curDate);
                            uploadDto.setSetlinfo(seltinfo);
                            uploadDto.setPayinfo(handlerListMapNull(o4103Map, "payinfo"));
                            uploadDto.setOpspdiseinfo(handlerListMapNull(o4103Map, "opspdiseinfo"));
                            uploadDto.setDiseinfo(handlerListMapNull(o4103Map, "diseinfo"));

                            List<Map<String, Object>>  iteminfo = handlerListMapNull(o4103Map, "iteminfo");


                            if (!ValidateUtil.isEmpty(iteminfo)) {
                                iteminfo.forEach(stringObjectMap -> {
                                    if (ValidateUtil.isEmpty(stringObjectMap.get("setl_id"))) {
                                        stringObjectMap.put("setl_id", baseInfo.get("setl_id"));
                                    }
                                    if (ValidateUtil.isEmpty(stringObjectMap.get("mdtrt_id"))) {
                                        stringObjectMap.put("mdtrt_id",baseInfo.get("mdtrt_id"));
                                    }
                                    if (ValidateUtil.isEmpty(stringObjectMap.get("psn_no"))) {
                                        stringObjectMap.put("psn_no", baseInfo.get("psn_no"));
                                    }
                                    if (ValidateUtil.isEmpty(stringObjectMap.get("setl_list_chrgitm_id"))) {
                                        stringObjectMap.put("setl_list_chrgitm_id", "");
                                    }
                                });
                            }

                            uploadDto.setIteminfo(iteminfo);
                            List<Map<String, Object>> oprninfo = handlerListMapNull(o4103Map, "oprninfo");
                            // 调整麻醉存在时 必须有麻醉结束时间
                            if (!ValidateUtil.isEmpty(oprninfo)) {
                                oprninfo.forEach(stringObjectMap -> {
                                    if (!ValidateUtil.isEmpty(stringObjectMap.get("anst_way"))) {
                                        stringObjectMap.put("anst_endtime", stringObjectMap.get("oprn_oprt_endtime"));
                                    }
                                });
                            }
                            uploadDto.setOprninfo(oprninfo);
                            uploadDto.setIcuinfo(handlerListMapNull(o4103Map, "icuinfo"));
                            uploadDto.setBldinfo(handlerListMapNull(o4103Map, "bldinfo"));

                            // 5203 查询结算信息
                            reqParams.put(SettleListUploadUtil.INFO_NO, SettleListUploadUtil.INFO_NO_5203);
                            String r5203 = HttpRequestUtil.post(reqParams, config.getUrl(), null);
                            Map response5203Map = JSON.parseObject(JSON.parse(r5203).toString(), Map.class);
                            log.info("[5203]返回结果：{}", response5203Map);
                            Map o5203Map = (Map) response5203Map.get("output");
                            Map<String, Object> o5203SetlinfoMap = o5203Map.get("setlinfo") != null ? (Map<String, Object>) o5203Map.get("setlinfo") : new HashMap<>();
                            // 医院等级，二级甲等
                            o5203SetlinfoMap.put("hosp_lv", "5");
                            uploadDto.setKc24info(o5203SetlinfoMap);
                            uploadDto.setKc24detailinfo(handlerListMapNull(o5203Map, "setldetail"));

                            // 5204 费用明细
                            reqParams.put(SettleListUploadUtil.INFO_NO, SettleListUploadUtil.INFO_NO_5204);
                            String r5204 = HttpRequestUtil.post(reqParams, config.getUrl(), null);
                            Map response5204Map = JSON.parseObject(JSON.parse(r5204).toString(), Map.class);
                            log.info("[5204]返回结果：{}", response5204Map);
                            uploadDto.setKc22info(handlerListMapNull(response5204Map, "output"));
                            // 传数据到市上
                            uploadLogSybVo.setReqtPara(JSONObject.toJSONString(uploadDto));
                            String dataRes = HttpRequestUtil.post(MapUtil.objectToMap(uploadDto), SettleListUploadUtil.UPLOAD_URL_C_DATA, getHeaders());
                            UploadResVo uploadResVo = JSON.parseObject(JSON.parse(dataRes).toString(), UploadResVo.class);
                            if (200 == uploadResVo.getStatus()) {
                                log.info("[市医保]请求成功，返回结果: {}",uploadResVo);
                                uploadLogSybVo.setUpldStas(DrgConst.START_FLAG_1);
                            } else {
                                log.info("[市医保]请求失败，病案号: {}， 返回结果: {}",setLSybInfoVo.getMedcasno(), uploadResVo.getMessage());
                                uploadLogSybVo.setTrns_no("市医保");
                                uploadLogSybVo.setUpldStas(DrgConst.START_FLAG_0);
                                uploadLogSybVo.setErrMsg(uploadResVo.getMessage());
                            }
                        }
                    }
                } catch (Exception e){
                    e.printStackTrace();
                    uploadLogSybVo.setTrns_no("市医保");
                    uploadLogSybVo.setUpldStas(DrgConst.START_FLAG_0);
                    uploadLogSybVo.setErrMsg(e.getMessage());
                }
                uploadLogSybVoList.add(uploadLogSybVo);
            }

            // 完成批次
            CityBatchVo complete1 = completeBatch(batchMap);
            if(complete1 != null){
                if (complete1.getStatus() == 200) {
                    log.info("完成批次成功，批次号: {}", batchNum);
                } else {
                    log.error("完成批次失败，重试1次");
                    CityBatchVo complete2 = completeBatch(batchMap);
                    if(complete2 != null){
                        if (complete2.getStatus() == 200) {
                            log.info("完成批次成功，批次号: {}", batchNum);
                        } else {
                            log.error("完成批次失败，重试2次");
                            CityBatchVo complete3 = completeBatch(batchMap);
                            if(complete3 != null){
                                if (complete3.getStatus() == 200) {
                                    log.info("完成批次成功，批次号: {}", batchNum);
                                } else {
                                    log.error("重试2次失败");
                                }
                            } else {
                                log.error("完成批次失败，报错");
                            }
                        }
                    } else {
                        log.error("完成批次失败，报错");
                    }
                }
            } else {
                log.error("完成批次失败，报错");
            }
        });

        // 加入4101A数据
        if (ValidateUtil.isNotEmpty(uploadLogSybVoList)) {
            uploadLogVoList.addAll(uploadLogSybVoList);
        }
        // 新增操作记录
        ListUploadVo listUploadVo = new ListUploadVo();
        listUploadVo.setBatchNum(uuid.toString());
        Integer successNum = 0;
        Integer loseNum = 0;
        for (SettleListUploadLogVo uploadLogVo : uploadLogSybVoList) {
            if (DrgConst.START_FLAG_1.equals(uploadLogVo.getUpldStas())) {
                successNum++;
            } else {
                loseNum++;
            }
        }
        listUploadVo.setUserName(dto.getNknm());
        listUploadVo.setSuccCnt(successNum.toString());
        listUploadVo.setFaleCnt(loseNum.toString());
        listUploadMapper.insertUploadSybOpeLog(listUploadVo);
        // 删除操作明细
        if (ValidateUtil.isNotEmpty(dto.getK00s())) {
            listUploadMapper.deleteUploadSybLog(dto);
        }
        // 新增上传日志明细
        listUploadMapper.insertUploadSybLog(uploadLogVoList);

        // 设置返回结果
        if(loseNum > 0){
            res.put(UPLOAD_STATE_KEY, UPLOAD_STATE_FAIL);
            res.put("errorMsg", "存在上传失败数据，请及时查看,上传成功数量：" + successNum + "，上传失败数量：" + loseNum);
        }
    }
    private CityBatchVo completeBatch(Map<String, Object> batchMap){
        try {
            batchMap.put("trans_stas", "2");
            String batchInfoSuccess = null;
            batchInfoSuccess = HttpRequestUtil.post(batchMap, SettleListUploadUtil.UPLOAD_URL_C_BATCH, getHeaders());
            log.info("确认返回：{}", batchInfoSuccess);
            return JSON.parseObject(JSON.parse(batchInfoSuccess).toString(), CityBatchVo.class);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    private List<Map<String, Object>> handlerListMapNull(Map map, String key){
        Object payinfo = map.get(key);
        if (payinfo != null) {
            return (List<Map<String, Object>>)payinfo;
        }
        return null;
    }

    private Object convertCode(Object code){
        Map<String, String> map = new HashMap(){{
            put("0201", "A03");
            put("0202", "A04");
            put("0203", "A05");
        }};
        if (!ValidateUtil.isEmpty(code)) {
            String s = map.get(code.toString());
            if (ValidateUtil.isNotEmpty(s)) {
                return s;
            }
        }
        return code;
    }

    /**
     * 对象转map通用类
     */
    public static Map<String, Object> convertObjectToMap(Object obj) throws IllegalAccessException {
        Map<String, Object> map = new HashMap<>();
        // 获取对象的所有字段
        Field[] fields = obj.getClass().getDeclaredFields();

        // 遍历字段并将其添加到Map中
        for (Field field : fields) {
            field.setAccessible(true);
            String fieldName = field.getName();
            Object fieldValue = field.get(obj);
            map.put(fieldName, fieldValue);
        }
        return map;
    }

    private Map getHeaders() throws Exception {
        String paasToken = "d3b89ac16fd7491f816c716a5061de78";
        long now = new Date().getTime();
        String timestamp = Long.toString((long) Math.floor(now / 1000));
        String nonce = Long.toHexString(now) + "-" + Long.toHexString((long) Math.floor(Math.random() * 0xFFFFFF));
        String signature= Signature.toSHA256(timestamp + paasToken + nonce + timestamp);
        return new HashMap() {
            {
                put("x-tif-paasid", "drg_2700016");
                put("x-tif-timestamp", timestamp);
                put("x-tif-signature", signature);
                put("x-tif-nonce", nonce);
            }
        };
    }

    /**
     * 上传市医保请求
     *
     * @param json
     * @param configVo
     * @return
     */
    private String sendData(String json, SettleListSybConfigVo configVo) {
        log.info("[{}]请求参数: {}", json);
        String url = configVo.getUrl() + "?APP_ID=" + configVo.getAppid() + "&ORG_CODE=" + configVo.getOrgCode();
        String result = null;
        try {
            URL url1 = new URL(url);
            HttpURLConnection connection = (HttpURLConnection) url1.openConnection();
            connection.setRequestMethod("POST");
            connection.setRequestProperty("Content-Type", "application/x-www-form-urlencoded;charset=UTF-8");
            connection.setRequestProperty("Accept", "application/json;charset=UTF-8");
            byte[] postData = json.getBytes(StandardCharsets.UTF_8);
            connection.setDoOutput(true);
            try (OutputStream stream = connection.getOutputStream()) {
                stream.write(postData);
            }
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(connection.getInputStream()))) {
                String line;
                StringBuffer respose = new StringBuffer();
                while ((line = reader.readLine()) != null) {
                    respose.append(line);
                }
                System.out.println(respose.toString());
                result = respose.toString();
            }
            connection.disconnect();
        } catch (Exception e) {
            e.printStackTrace();
            throw new AppException("上传市医保-->调用POST请求失败");
        }
        return result;
    }

    /**
     * @param oprnInfoVos        手术
     * @param codeVos            医师对照
     * @param setlInfoVos        基本
     * @param nurseCodeVos       护士对照
     * @param transfusionVos     输血
     * @param transfusionCodeVos 输血对照
     */
    private void transferSybCode(List<OprnSybInfoVo> oprnInfoVos,
                                 List<DrCodgVo> codeVos,
                                 List<SetLSybInfoVo> setlInfoVos,
                                 List<NurseCodeVo> nurseCodeVos,
                                 List<TransfusionSybVo> transfusionVos,
                                 List<TransfusionCodeVo> transfusionCodeVos) {
        if (ValidateUtil.isNotEmpty(codeVos)) {
            Map<String, String> doctorContrastMap = new HashMap<>();
            Map<String, String> nurseContrastMap = new HashMap<>();
            Map<String, String> transfusionContrastMap = new HashMap<>();
            Map<String, String> doctorAndNurseMap = new HashMap<>();
            codeVos.forEach(doctorCodeVo -> {
                if (ValidateUtil.isNotEmpty(doctorCodeVo.getOperDrCode())) {
                    doctorContrastMap.put(doctorCodeVo.getOperDrCode(), doctorCodeVo.getHiDrCode());
                }
            });
            nurseCodeVos.forEach(nurseCodeVo -> {
                if (ValidateUtil.isNotEmpty(nurseCodeVo.getEmpno())) {
                    nurseContrastMap.put(nurseCodeVo.getEmpno(), nurseCodeVo.getHiNursCode());
                }
            });
            transfusionCodeVos.forEach(transfusionCodeVo -> {
                if (ValidateUtil.isNotEmpty(transfusionCodeVo.getInhospBldCode())) {
                    transfusionContrastMap.put(transfusionCodeVo.getInhospBldCode(),
                            transfusionCodeVo.getHiBldCode());
                }
            });
            // 添加医生和护士
            if (doctorContrastMap.keySet().size() > 0) {
                doctorAndNurseMap.putAll(doctorContrastMap);
            }
            if (nurseContrastMap.keySet().size() > 0) {
                doctorAndNurseMap.putAll(nurseContrastMap);
            }
            // 手术中的术者医师代码和麻醉医师代码转换
            if (ValidateUtil.isNotEmpty(oprnInfoVos)) {
                oprnInfoVos.forEach(oprnInfoVo -> {
                    // 住院医师
                    if (ValidateUtil.isNotEmpty(oprnInfoVo.getOper_dr_code()) &&
                            !ValidateUtil.isEmpty(doctorAndNurseMap.get(oprnInfoVo.getOper_dr_code()))) {
                        oprnInfoVo.setOper_dr_code(doctorAndNurseMap.get(oprnInfoVo.getOper_dr_code()));
                    }
                    // 麻醉医师
                    if (ValidateUtil.isNotEmpty(oprnInfoVo.getAnst_dr_code()) &&
                            !ValidateUtil.isEmpty(doctorAndNurseMap.get(oprnInfoVo.getAnst_dr_code()))) {
                        oprnInfoVo.setAnst_dr_code(doctorAndNurseMap.get(oprnInfoVo.getAnst_dr_code()));
                    }
                });
            }

            if (ValidateUtil.isNotEmpty(setlInfoVos)) {
                setlInfoVos.forEach(setlInfoVo -> {
                    // 医生编码转换
                    if (ValidateUtil.isNotEmpty(setlInfoVo.getChfpdr_code()) &&
                            !ValidateUtil.isEmpty(doctorContrastMap.get(setlInfoVo.getChfpdr_code()))) {
                        setlInfoVo.setChfpdr_code(doctorContrastMap.get(setlInfoVo.getChfpdr_code()));
                    }
                    // 责任护士代码转换
                    if (ValidateUtil.isNotEmpty(setlInfoVo.getCharge_nurse_code()) &&
                            !ValidateUtil.isEmpty(nurseContrastMap.get(setlInfoVo.getCharge_nurse_code()))) {
                        setlInfoVo.setCharge_nurse_code(nurseContrastMap.get(setlInfoVo.getCharge_nurse_code()));
                    }
                });
            }


        }

    }

    /**
     * 获取不需要转换为null的字段
     *
     * @return
     */
    private List<String> getNotConvertFields() {
        List<String> fields = new LinkedList<>();
        List<MedicalSettleCheckVo> medicalSettleCheckVos = medicalSettleCheckDao.QueryMedical(new MedicalSettleCheckDto());
        if (ValidateUtil.isNotEmpty(medicalSettleCheckVos)) {
            medicalSettleCheckVos.forEach(medicalSettleCheckVo -> {
                if (ValidateUtil.isNotEmpty(medicalSettleCheckVo.getTransformation()) &&
                        SettleListValidateUtil.isEnabled(medicalSettleCheckVo.getTransformation())) {
                    fields.add(SettleListValidateUtil.getRuleValue(medicalSettleCheckVo.getTransformation()));
                }
            });
        }
        return fields;
    }

    /**
     * 转换-为空
     *
     * @param setlinfoMap
     * @param notConvertFieldsList 不需要转换字段
     */
    private void transfusionToNull(Map<String, Object> setlinfoMap, List<String> notConvertFieldsList) {
        if (ValidateUtil.isNotEmpty(setlinfoMap.keySet())) {
            for (String key : setlinfoMap.keySet()) {
                if (!ValidateUtil.isEmpty(setlinfoMap.get(key)) &&
                        NULL_.equals(setlinfoMap.get(key).toString()) &&
                        !notConvertFieldsList.contains(key)) {
                    setlinfoMap.put(key, "");
                }
            }
        }
    }

    /**
     * 处理院内医师便便转换到医保医师编码
     *
     * @param oprnInfoVos        手术信息
     * @param codeVos            医师对照编码
     * @param setlInfoVos        基本信息
     * @param nurseCodeVos       护士对照编码
     * @param transfusionVos     输血信息
     * @param transfusionCodeVos 输血对照编码
     * @param somDeptConfigVos   科室对照编码
     */
    private void
    transferCode(List<OprnInfoVo> oprnInfoVos,
                 List<DrCodgVo> codeVos,
                 List<SetlInfoVo> setlInfoVos,
                 List<NurseCodeVo> nurseCodeVos,
                 List<TransfusionVo> transfusionVos,
                 List<TransfusionCodeVo> transfusionCodeVos,
                 List<SomDeptConfigVo> somDeptConfigVos) {
        if (ValidateUtil.isNotEmpty(codeVos)) {
            Map<String, String> doctorContrastMap = new HashMap<>();
            Map<String, String> nurseContrastMap = new HashMap<>();
            Map<String, String> transfusionContrastMap = new HashMap<>();
            Map<String, String> deptContrastMap = new HashMap<>();
            Map<String, String> doctorAndNurseMap = new HashMap<>();
            HashMap<String, String> deptNameContrastmap = new HashMap<>();

            codeVos.forEach(doctorCodeVo -> {
                if (ValidateUtil.isNotEmpty(doctorCodeVo.getOperDrCode())) {
                    doctorContrastMap.put(doctorCodeVo.getOperDrCode(), doctorCodeVo.getHiDrCode());
                }
            });
            nurseCodeVos.forEach(nurseCodeVo -> {
                if (ValidateUtil.isNotEmpty(nurseCodeVo.getEmpno())) {
                    nurseContrastMap.put(nurseCodeVo.getEmpno(), nurseCodeVo.getHiNursCode());
                }
            });
            transfusionCodeVos.forEach(transfusionCodeVo -> {
                if (ValidateUtil.isNotEmpty(transfusionCodeVo.getInhospBldCode())) {
                    transfusionContrastMap.put(transfusionCodeVo.getInhospBldCode(),
                            transfusionCodeVo.getHiBldCode());
                }
            });

            somDeptConfigVos.forEach(somDeptConfigVo -> {
                if (ValidateUtil.isNotEmpty(somDeptConfigVo.getCode())) {
                    deptContrastMap.put(somDeptConfigVo.getCode(),
                            somDeptConfigVo.getStd_dept_codg());
                    deptNameContrastmap.put(somDeptConfigVo.getStd_dept_codg(), somDeptConfigVo.getStd_dept_name());
                }
            });
            // 添加医生和护士
            if (doctorContrastMap.keySet().size() > 0) {
                doctorAndNurseMap.putAll(doctorContrastMap);
            }
            if (nurseContrastMap.keySet().size() > 0) {
                doctorAndNurseMap.putAll(nurseContrastMap);
            }

            // 手术中的术者医师代码和麻醉医师代码转换
            if (ValidateUtil.isNotEmpty(oprnInfoVos)) {
                oprnInfoVos.forEach(oprnInfoVo -> {
                    // 住院医师
                    if (ValidateUtil.isNotEmpty(oprnInfoVo.getOper_dr_code()) &&
                            !ValidateUtil.isEmpty(doctorAndNurseMap.get(oprnInfoVo.getOper_dr_code()))) {
                        oprnInfoVo.setOper_dr_code(doctorAndNurseMap.get(oprnInfoVo.getOper_dr_code()));
                    }
                    // 麻醉医师
                    if (ValidateUtil.isNotEmpty(oprnInfoVo.getAnst_dr_code()) &&
                            !ValidateUtil.isEmpty(doctorAndNurseMap.get(oprnInfoVo.getAnst_dr_code()))) {
                        oprnInfoVo.setAnst_dr_code(doctorAndNurseMap.get(oprnInfoVo.getAnst_dr_code()));
                    }
                });
            }

            if (ValidateUtil.isNotEmpty(setlInfoVos)) {
                setlInfoVos.forEach(setlInfoVo -> {
                    // 医生编码转换
                    if (ValidateUtil.isNotEmpty(setlInfoVo.getChfpdr_code()) &&
                            !ValidateUtil.isEmpty(doctorContrastMap.get(setlInfoVo.getChfpdr_code()))) {
                        setlInfoVo.setChfpdr_code(doctorContrastMap.get(setlInfoVo.getChfpdr_code()));
                    }
                    // 责任护士代码转换
                    if (ValidateUtil.isNotEmpty(setlInfoVo.getResp_nurs_code()) &&
                            !ValidateUtil.isEmpty(nurseContrastMap.get(setlInfoVo.getResp_nurs_code()))) {
                        setlInfoVo.setResp_nurs_code(nurseContrastMap.get(setlInfoVo.getResp_nurs_code()));
                    }
                });
            }

            // 输血品种代码转换
            if (ValidateUtil.isNotEmpty(transfusionVos)) {
                transfusionVos.forEach(transfusionVo -> {
                    if (ValidateUtil.isNotEmpty(transfusionVo.getBld_cat()) &&
                            !ValidateUtil.isEmpty(transfusionContrastMap.get(transfusionVo.getBld_cat()))) {
                        transfusionVo.setBld_cat(transfusionContrastMap.get(transfusionVo.getBld_cat()));
                    }
                });
            }
            // 1、出院科别转换
            if (ValidateUtil.isNotEmpty(setlInfoVos)) {
                setlInfoVos.forEach(setlInfoVo -> {
                    //1、出院科别及名称转换
                    if (ValidateUtil.isNotEmpty(setlInfoVo.getDscg_caty()) &&
                            !ValidateUtil.isEmpty(deptContrastMap.get(setlInfoVo.getDscg_caty()))) {
                        setlInfoVo.setDscg_caty(deptContrastMap.get(setlInfoVo.getDscg_caty()));
                    }
                    //2.入院科室编码名字转化
                    if (ValidateUtil.isNotEmpty(setlInfoVo.getAdm_dept_codg()) &&
                            !ValidateUtil.isEmpty(deptContrastMap.get(setlInfoVo.getAdm_dept_codg()))) {
                        setlInfoVo.setAdm_dept_codg(deptContrastMap.get(setlInfoVo.getAdm_dept_codg()));
                        if (ValidateUtil.isNotEmpty(setlInfoVo.getAdm_dept_name()) &&
                                !ValidateUtil.isEmpty(deptNameContrastmap.get(setlInfoVo.getAdm_dept_codg()))) {
                            setlInfoVo.setAdm_dept_name(deptNameContrastmap.get(setlInfoVo.getAdm_dept_codg()));
                        }
                    }
                    //3出院科室及名称转换
                    if (ValidateUtil.isNotEmpty(setlInfoVo.getDscg_dept_codg()) &&
                            !ValidateUtil.isEmpty(deptContrastMap.get(setlInfoVo.getDscg_dept_codg()))) {
                        setlInfoVo.setDscg_dept_codg(deptContrastMap.get(setlInfoVo.getDscg_dept_codg()));
                        if (ValidateUtil.isNotEmpty(setlInfoVo.getDscg_dept_codg()) &&
                                !ValidateUtil.isEmpty(deptNameContrastmap.get(setlInfoVo.getDscg_dept_codg()))) {
                            setlInfoVo.setDscg_dept_name(deptNameContrastmap.get(setlInfoVo.getDscg_dept_codg()));
                        }
                    }
                    //4转院院科室及名称转换
                    if (!ValidateUtil.isEmpty(setlInfoVo.getRefldept_dept())
                            &&!"-".equals(setlInfoVo.getRefldept_dept())){

                        String newZkkb = "";
                        String[] zkkbs = setlInfoVo.getRefldept_dept().split("→");

                        for (int i = 0; i < zkkbs.length; i++) {
                            if (!ValidateUtil.isEmpty(zkkbs[i]) &&
                                    !ValidateUtil.isEmpty(deptContrastMap.get(zkkbs[i]))) {
                                String zkkb = deptContrastMap.get(zkkbs[i]);
                                if(i==0){
                                    newZkkb +=zkkb;
                                }else{
                                    newZkkb +="→"+zkkb;
                                }

                            }
                        }
                        if(!ValidateUtil.isEmpty(newZkkb)){
                            setlInfoVo.setRefldept_dept(newZkkb);
                        }
                    }
                });
            }
        }
    }

    /**
     * mapToString
     *
     * @param map
     * @return
     */
    private String mapToString(Map map) {
        return JSON.toJSONString(map, (ValueFilter) (obj, name, value) -> {
            if (value == null) {
                return "";
            }
            return value;
        });
    }

    /**
     * 设置input节点信息
     *
     * @param nodeName 节点名称
     * @param mapList  节点数据
     * @param params   数据
     */
    private void addInputNodeInfo(String nodeName,
                                  List<Map<String, Object>> mapList,
                                  Map<String, Object> params) {
        if (ValidateUtil.isNotEmpty(mapList)) {
            params.put(nodeName, mapList);
        }
    }

    private void addInputNodeInfo1(String nodeName,
                                   List<Map<String, Object>> mapList,
                                   Map<String, Object> params) {
        if (ValidateUtil.isNotEmpty(mapList)) {
            params.put(nodeName, mapList);
        } else {
            params.put(nodeName, Collections.emptyList());
        }
    }

    /**
     * 转换数据为map
     *
     * @param t   数据
     * @param <T>
     * @return
     */
    private <T> List<Map<String, Object>> toMapList(List<T> t) {
        List<Map<String, Object>> mapList = new ArrayList<>();
        for (T t1 : t) {
            Map map = MapUtil.objectToMap(t1, false, true);
            map.remove(REMOVE_ID_KEY);
            mapList.add(map);
        }
        return mapList;
    }


    @Override
    public List<ListUploadVo> queryRecordData(ListUploadDto dto) {
        List<ListUploadVo> list = listUploadMapper.queryRecordData(dto);
        return list;
    }

    @Override
    public List<ListUploadVo> queryRecordSybData(ListUploadDto dto) {
        List<ListUploadVo> list = listUploadMapper.queryRecordSybData(dto);
        return list;
    }


    @Override
    public List<ListUploadVo> queryDetailData(ListUploadDto dto) {

        PageHelper.startPage(
                ValidateUtil.isEmpty(dto.getPageNum())?1 :dto.getPageNum(),
                ValidateUtil.isEmpty(dto.getPageSize())?50:dto.getPageSize()
        );
        List<ListUploadVo> list = listUploadMapper.queryDetailData(dto);
        return list;
    }

    @Override
    public List<ListUploadVo> queryDetailSybData(ListUploadDto dto) {
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        List<ListUploadVo> list = listUploadMapper.queryDetailSybData(dto);
        return list;
    }

    @Override
    public List<ListUploadVo> queryUpdateTypeData(ListUploadDto dto) {

        updateInsuPlaceType(dto);
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        List<ListUploadVo> list = listUploadMapper.queryUploadedData(dto);
        return list;
    }

    @Override
    public void listWithdrawn(ListUploadDto dto) {
        List<Map<String, Object>> dataList = listUploadMapper.listWithdrawn(dto);
        if (ValidateUtil.isEmpty(dataList)) {
            throw new AppException("无可修改数据");
        }
        // 获取清单配置信息
        List<SettleListConfigVo> settleListConfigVoList = listUploadMapper.querySettleConfig(dto);
        Map<String, SettleListConfigVo> settleListConfigVoMap = settleListConfigVoList.stream().collect(Collectors.toMap(SettleListConfigVo::getInsuplcAdmdvs, settleListConfigVo -> settleListConfigVo));

        String hospitalId = null;
        SettleListConfigVo configVo;
        for (Map<String, Object> map : dataList) {
            //根据基础信息中的清算机构统筹区，获取对应的configVo
            configVo = settleListConfigVoMap.get(map.get("insuplcAdmdvs"));
            hospitalId = configVo.getFixmedins_code();

            // 设置发送报文ID
            String curTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
            configVo.setMsgid(hospitalId + curTime + ORDER_NO);
            configVo.setInf_time(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            Map configMap = MapUtil.objectToMap(configVo, false, true);
            configMap.remove("url");
            Map<String, Object> params4102 = new LinkedHashMap<>();
            params4102.putAll(configMap);
            params4102.put(SettleListUploadUtil.INFO_NO_KEY, SettleListUploadUtil.INFO_NO_4102);

            // 获取9001
            Map response9001Map = SettleListUploadUtil.get9001Params(configMap, configVo);
            Object signNo = SettleListUploadUtil.getResByKey(response9001Map,
                    "output:signinoutb:sign_no", SettleListUploadUtil.INFO_NO_9001, new SetlInfoVo());
            params4102.put(SettleListUploadUtil.SIGN_NO_KEY, signNo);
            Map<String, Object> inputNode = new LinkedHashMap<>();
            Map<String, Object> data = new LinkedHashMap<>();
            List<Map<String, Object>> stastinfo = new ArrayList<>();
            stastinfo.add(map);
            data.put("stastinfo", stastinfo);
            inputNode.put("data", data);
            params4102.put(SettleListUploadUtil.INPUT_NODE_NAME, inputNode);
            String resultData = HttpRequestUtil.post(params4102, configVo.getUrl());
            log.info(resultData);
        }
        //删除清单上传记录
        listUploadMapper.deleteUploadLog(dto);
        //将清单设置为未上传
        listUploadMapper.updateData(dto);

    }

    @Override
    public void queryUploadedlist(ListUploadedQueryDto dto) {
        log.info("4103抽取开始===============================================================");
        //根据结算时间获取结算数据
        List<Map<String, String>> setlinfoList = listUploadMapper.getSettleListByTime(dto);

        if (setlinfoList == null || setlinfoList.isEmpty()) {
            throw new AppException("当前时间内没有结算清单信息");
        } else {

            //根基现有的结算id先删除一遍数据
            uploadedListMapper.delSetinfo(setlinfoList);
            uploadedListMapper.delDiseinfo(setlinfoList);
            uploadedListMapper.delOprninfo(setlinfoList);
            uploadedListMapper.delBldinfo(setlinfoList);
            uploadedListMapper.delIcuinfo(setlinfoList);
            uploadedListMapper.delPayinfo(setlinfoList);
            uploadedListMapper.delIteminfo(setlinfoList);

            for (int i = 0; i < setlinfoList.size(); i++) {
                Map<String, String> setlinfoMap = setlinfoList.get(i);
                //获取清单配置
                ListUploadDto listUploadDto = new ListUploadDto();
                listUploadDto.setInsuplcAdmdvs(setlinfoMap.get("insuplc").substring(0,4));
                List<SettleListConfigVo> settleListConfigVos = listUploadMapper.querySettleConfig(listUploadDto);
                SettleListConfigVo configVo = settleListConfigVos.get(0);
                String hospitalId = configVo.getMsgid();
                String curTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
                configVo.setMsgid(hospitalId + curTime + ORDER_NO);
                configVo.setInf_time(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                Map configMap = MapUtil.objectToMap(configVo, false, true);
                configMap.remove("url");
                Map<String, Object> params4103 = new LinkedHashMap<>();
                params4103.putAll(configMap);
                params4103.put(SettleListUploadUtil.INFO_NO, SettleListUploadUtil.INFO_NO_4103);
                Map<String, String> map = new LinkedHashMap<>();
                map.put("psn_no", setlinfoMap.get("psn_no"));
                map.put("setl_id", setlinfoMap.get("setl_id"));
                Map<String, Object> inputNode = new LinkedHashMap<>();
                inputNode.put("data", map);

                //调用9001获取签名
                Map<String, Object> response9001Map = new HashMap<>();
                try {
                    response9001Map = SettleListUploadUtil.get9001Params(configMap, configVo);
                } catch (Exception e) {
                    e.printStackTrace();
                    throw new AppException("9001获取签名失败");
                }

                Object signNo = SettleListUploadUtil.getResByKey(response9001Map,
                        "output:signinoutb:sign_no", SettleListUploadUtil.INFO_NO_9001, new SetlInfoVo());
                params4103.put(SettleListUploadUtil.SIGN_NO_KEY, signNo);
                configVo.setSign_no(signNo.toString());

                params4103.put(SettleListUploadUtil.INPUT_NODE_NAME, inputNode);
                log.info("4103参数：{}", JSON.toJSONString(params4103));

                //调用4103
                String upload4103AResponse = "";
                try {
                    upload4103AResponse = HttpRequestUtil.post(params4103, configVo.getUrl());
                } catch (Exception e) {
                    e.printStackTrace();
                    throw new AppException("4103调用失败");
                }
//            String upload4103AResponse ="\"{\\\"output\\\":{\\\"diseinfo\\\":[{\\\"maindiag_flag\\\":\\\"1\\\",\\\"psn_no\\\":\\\"51000051140000001009812252\\\",\\\"adm_cond_type\\\":\\\"1\\\",\\\"mdtrt_id\\\":\\\"511400G0000054822180\\\",\\\"diag_type\\\":\\\"1\\\",\\\"setl_list_diag_id\\\":\\\"30078124\\\",\\\"diag_name\\\":\\\"腰椎间盘突出\\\",\\\"setl_id\\\":\\\"511400G0000040509125\\\",\\\"diag_code\\\":\\\"M51.202\\\"}],\\\"opspdiseinfo\\\":[],\\\"bldinfo\\\":[],\\\"iteminfo\\\":[{\\\"item_clab_amt\\\":0.00,\\\"med_chrgitm_type\\\":\\\"01\\\",\\\"psn_no\\\":\\\"51000051140000001009812252\\\",\\\"daysrg_code_name\\\":null,\\\"mdtrt_id\\\":\\\"511400G0000054822180\\\",\\\"item_oth_amt\\\":0.00,\\\"item_ownpay_amt\\\":0.00,\\\"setl_id\\\":\\\"511400G0000040509125\\\",\\\"item_sumamt\\\":288.00,\\\"item_claa_amt\\\":288.00,\\\"sindise_code_name\\\":null,\\\"setl_list_chrgitm_id\\\":\\\"184955653\\\"},{\\\"item_clab_amt\\\":0.00,\\\"med_chrgitm_type\\\":\\\"02\\\",\\\"psn_no\\\":\\\"51000051140000001009812252\\\",\\\"daysrg_code_name\\\":null,\\\"mdtrt_id\\\":\\\"511400G0000054822180\\\",\\\"item_oth_amt\\\":0.00,\\\"item_ownpay_amt\\\":0.00,\\\"setl_id\\\":\\\"511400G0000040509125\\\",\\\"item_sumamt\\\":252.00,\\\"item_claa_amt\\\":252.00,\\\"sindise_code_name\\\":null,\\\"setl_list_chrgitm_id\\\":\\\"184955654\\\"},{\\\"item_clab_amt\\\":132.00,\\\"med_chrgitm_type\\\":\\\"03\\\",\\\"psn_no\\\":\\\"51000051140000001009812252\\\",\\\"daysrg_code_name\\\":null,\\\"mdtrt_id\\\":\\\"511400G0000054822180\\\",\\\"item_oth_amt\\\":0.00,\\\"item_ownpay_amt\\\":0.00,\\\"setl_id\\\":\\\"511400G0000040509125\\\",\\\"item_sumamt\\\":145.00,\\\"item_claa_amt\\\":13.00,\\\"sindise_code_name\\\":null,\\\"setl_list_chrgitm_id\\\":\\\"184955655\\\"},{\\\"item_clab_amt\\\":39.00,\\\"med_chrgitm_type\\\":\\\"04\\\",\\\"psn_no\\\":\\\"51000051140000001009812252\\\",\\\"daysrg_code_name\\\":null,\\\"mdtrt_id\\\":\\\"511400G0000054822180\\\",\\\"item_oth_amt\\\":0.00,\\\"item_ownpay_amt\\\":43.00,\\\"setl_id\\\":\\\"511400G0000040509125\\\",\\\"item_sumamt\\\":281.00,\\\"item_claa_amt\\\":199.00,\\\"sindise_code_name\\\":null,\\\"setl_list_chrgitm_id\\\":\\\"184955656\\\"},{\\\"item_clab_amt\\\":418.00,\\\"med_chrgitm_type\\\":\\\"05\\\",\\\"psn_no\\\":\\\"51000051140000001009812252\\\",\\\"daysrg_code_name\\\":null,\\\"mdtrt_id\\\":\\\"511400G0000054822180\\\",\\\"item_oth_amt\\\":0.00,\\\"item_ownpay_amt\\\":0.00,\\\"setl_id\\\":\\\"511400G0000040509125\\\",\\\"item_sumamt\\\":422.00,\\\"item_claa_amt\\\":4.00,\\\"sindise_code_name\\\":null,\\\"setl_list_chrgitm_id\\\":\\\"184955657\\\"},{\\\"item_clab_amt\\\":0.00,\\\"med_chrgitm_type\\\":\\\"06\\\",\\\"psn_no\\\":\\\"51000051140000001009812252\\\",\\\"daysrg_code_name\\\":null,\\\"mdtrt_id\\\":\\\"511400G0000054822180\\\",\\\"item_oth_amt\\\":0.00,\\\"item_ownpay_amt\\\":0.00,\\\"setl_id\\\":\\\"511400G0000040509125\\\",\\\"item_sumamt\\\":0.00,\\\"item_claa_amt\\\":0.00,\\\"sindise_code_name\\\":null,\\\"setl_list_chrgitm_id\\\":\\\"184955658\\\"},{\\\"item_clab_amt\\\":0.00,\\\"med_chrgitm_type\\\":\\\"07\\\",\\\"psn_no\\\":\\\"51000051140000001009812252\\\",\\\"daysrg_code_name\\\":null,\\\"mdtrt_id\\\":\\\"511400G0000054822180\\\",\\\"item_oth_amt\\\":0.00,\\\"item_ownpay_amt\\\":0.00,\\\"setl_id\\\":\\\"511400G0000040509125\\\",\\\"item_sumamt\\\":204.00,\\\"item_claa_amt\\\":204.00,\\\"sindise_code_name\\\":null,\\\"setl_list_chrgitm_id\\\":\\\"184955659\\\"},{\\\"item_clab_amt\\\":0.00,\\\"med_chrgitm_type\\\":\\\"08\\\",\\\"psn_no\\\":\\\"51000051140000001009812252\\\",\\\"daysrg_code_name\\\":null,\\\"mdtrt_id\\\":\\\"511400G0000054822180\\\",\\\"item_oth_amt\\\":0.00,\\\"item_ownpay_amt\\\":0.00,\\\"setl_id\\\":\\\"511400G0000040509125\\\",\\\"item_sumamt\\\":0.00,\\\"item_claa_amt\\\":0.00,\\\"sindise_code_name\\\":null,\\\"setl_list_chrgitm_id\\\":\\\"184955660\\\"},{\\\"item_clab_amt\\\":250.50,\\\"med_chrgitm_type\\\":\\\"09\\\",\\\"psn_no\\\":\\\"51000051140000001009812252\\\",\\\"daysrg_code_name\\\":null,\\\"mdtrt_id\\\":\\\"511400G0000054822180\\\",\\\"item_oth_amt\\\":0.00,\\\"item_ownpay_amt\\\":0.00,\\\"setl_id\\\":\\\"511400G0000040509125\\\",\\\"item_sumamt\\\":332.56,\\\"item_claa_amt\\\":82.06,\\\"sindise_code_name\\\":null,\\\"setl_list_chrgitm_id\\\":\\\"184955661\\\"},{\\\"item_clab_amt\\\":0.00,\\\"med_chrgitm_type\\\":\\\"10\\\",\\\"psn_no\\\":\\\"51000051140000001009812252\\\",\\\"daysrg_code_name\\\":null,\\\"mdtrt_id\\\":\\\"511400G0000054822180\\\",\\\"item_oth_amt\\\":0.00,\\\"item_ownpay_amt\\\":0.00,\\\"setl_id\\\":\\\"511400G0000040509125\\\",\\\"item_sumamt\\\":0.00,\\\"item_claa_amt\\\":0.00,\\\"sindise_code_name\\\":null,\\\"setl_list_chrgitm_id\\\":\\\"184955662\\\"},{\\\"item_clab_amt\\\":0.00,\\\"med_chrgitm_type\\\":\\\"11\\\",\\\"psn_no\\\":\\\"51000051140000001009812252\\\",\\\"daysrg_code_name\\\":null,\\\"mdtrt_id\\\":\\\"511400G0000054822180\\\",\\\"item_oth_amt\\\":0.00,\\\"item_ownpay_amt\\\":0.00,\\\"setl_id\\\":\\\"511400G0000040509125\\\",\\\"item_sumamt\\\":0.00,\\\"item_claa_amt\\\":0.00,\\\"sindise_code_name\\\":null,\\\"setl_list_chrgitm_id\\\":\\\"184955663\\\"},{\\\"item_clab_amt\\\":0.00,\\\"med_chrgitm_type\\\":\\\"12\\\",\\\"psn_no\\\":\\\"51000051140000001009812252\\\",\\\"daysrg_code_name\\\":null,\\\"mdtrt_id\\\":\\\"511400G0000054822180\\\",\\\"item_oth_amt\\\":0.00,\\\"item_ownpay_amt\\\":0.00,\\\"setl_id\\\":\\\"511400G0000040509125\\\",\\\"item_sumamt\\\":0.00,\\\"item_claa_amt\\\":0.00,\\\"sindise_code_name\\\":null,\\\"setl_list_chrgitm_id\\\":\\\"184955664\\\"},{\\\"item_clab_amt\\\":0.00,\\\"med_chrgitm_type\\\":\\\"13\\\",\\\"psn_no\\\":\\\"51000051140000001009812252\\\",\\\"daysrg_code_name\\\":null,\\\"mdtrt_id\\\":\\\"511400G0000054822180\\\",\\\"item_oth_amt\\\":0.00,\\\"item_ownpay_amt\\\":0.00,\\\"setl_id\\\":\\\"511400G0000040509125\\\",\\\"item_sumamt\\\":0.00,\\\"item_claa_amt\\\":0.00,\\\"sindise_code_name\\\":null,\\\"setl_list_chrgitm_id\\\":\\\"184955665\\\"},{\\\"item_clab_amt\\\":1946.00,\\\"med_chrgitm_type\\\":\\\"14\\\",\\\"psn_no\\\":\\\"51000051140000001009812252\\\",\\\"daysrg_code_name\\\":null,\\\"mdtrt_id\\\":\\\"511400G0000054822180\\\",\\\"item_oth_amt\\\":0.00,\\\"item_ownpay_amt\\\":64.00,\\\"setl_id\\\":\\\"511400G0000040509125\\\",\\\"item_sumamt\\\":2027.00,\\\"item_claa_amt\\\":17.00,\\\"sindise_code_name\\\":null,\\\"setl_list_chrgitm_id\\\":\\\"184955666\\\"}],\\\"icuinfo\\\":[],\\\"setlinfo\\\":{\\\"setl_time\\\":null,\\\"bfadm_coma_days\\\":null,\\\"adm_dept_codg\\\":null,\\\"emp_tel\\\":\\\"\\\",\\\"curr_addr\\\":\\\"四川省眉山市东坡区太和镇红卫2组\\\",\\\"psn_no\\\":\\\"51000051140000001009812252\\\",\\\"stas_type\\\":\\\"0\\\",\\\"coner_addr\\\":\\\"四川省眉山市东坡区四川省眉山市东坡区太和镇红卫2组\\\",\\\"dscg_dept_name\\\":null,\\\"dcla_time\\\":null,\\\"age_days\\\":null,\\\"bfadm_coma_h_cnt\\\":null,\\\"gend\\\":\\\"1\\\",\\\"adm_way_code\\\":\\\"2\\\",\\\"insutype\\\":\\\"390\\\",\\\"yljzPay\\\":0.00,\\\"setl_list_sn\\\":\\\"240037450\\\",\\\"psn_cashpay\\\":1559.93,\\\"otp_tcm_diag_dise_code\\\":\\\"A07.06.\\\",\\\"otp_wm_diag_dise_code\\\":\\\"M51.202\\\",\\\"chfpdr_name\\\":\\\"梅郎\\\",\\\"biz_sn\\\":\\\"0b9cc9df21288081203d\\\",\\\"medins_fill_psn\\\":\\\"周杨\\\",\\\"billno\\\":\\\"Z24080144981\\\",\\\"otp_wm_diag\\\":\\\"腰椎间盘突出\\\",\\\"acp_optins_code\\\":null,\\\"bill_code\\\":\\\"Z24080144981\\\",\\\"dscg_time\\\":1722441600000,\\\"psn_selfpay_amt\\\":1452.93,\\\"naty\\\":\\\"01\\\",\\\"opsp_dise_code\\\":null,\\\"spcasRea\\\":null,\\\"afadm_coma_m_cnt\\\":null,\\\"certno\\\":\\\"511122197703224139\\\",\\\"fixmedins_code\\\":\\\"H51140300271\\\",\\\"hi_no\\\":\\\"51000051140000001009812252\\\",\\\"spga_nurscare_days\\\":0.0,\\\"coner_tel\\\":\\\"13890332185\\\",\\\"oprn_oprt_code_cnt\\\":4,\\\"acct_payamt\\\":0.00,\\\"resp_nurs_name\\\":\\\"赵丹\\\",\\\"mul_nwb_bir_wt\\\":\\\"\\\",\\\"dipDiseGrpCode\\\":null,\\\"otp_tcm_diag\\\":\\\"痹证类病\\\",\\\"refl_caty\\\":\\\"\\\",\\\"setl_id\\\":\\\"511400G0000040509125\\\",\\\"emp_poscode\\\":\\\"620010\\\",\\\"psn_name\\\":\\\"张春桃\\\",\\\"emp_addr\\\":\\\"太和镇红卫2组\\\",\\\"mul_nwb_adm_wt\\\":\\\"\\\",\\\"zgdePay\\\":0.00,\\\"adm_caty\\\":null,\\\"hsorg_opter_code\\\":null,\\\"patn_rlts\\\":\\\"01\\\",\\\"prfs\\\":\\\"27\\\",\\\"spcasExitSco\\\":null,\\\"insu_admdvs\\\":\\\"511402\\\",\\\"dscg_dept_codg\\\":null,\\\"diag_code_cnt\\\":1,\\\"sp_psn_type\\\":\\\"\\\",\\\"psn_ownpay_fee\\\":107.00,\\\"spcasExitWt\\\":null,\\\"setl_begndate\\\":1721404800000,\\\"hsorg_code\\\":null,\\\"hsorg_opter_name\\\":null,\\\"hi_type\\\":\\\"390\\\",\\\"mdtrt_id\\\":\\\"511400G0000054822180\\\",\\\"adm_dept_name\\\":null,\\\"nwb_adm_type\\\":\\\"\\\",\\\"opsp_diag_caty\\\":\\\"\\\",\\\"afadm_coma_h_cnt\\\":null,\\\"scd_nurscare_days\\\":0.0,\\\"hi_setl_lv\\\":\\\"2\\\",\\\"hi_paymtd\\\":\\\"9\\\",\\\"chk_cont\\\":null,\\\"fixmedins_name\\\":\\\"眉山市彭山区中医医院\\\",\\\"coner_name\\\":\\\"张春桃\\\",\\\"lv1_nurscare_days\\\":0.0,\\\"opsp_mdtrt_time\\\":null,\\\"vent_used_h_cnt\\\":null,\\\"chfpdr_code\\\":\\\"D511403006247\\\",\\\"dscg31days_rinp_flag\\\":\\\"1\\\",\\\"resp_nurs_code\\\":\\\"N511403003076\\\",\\\"rinp_pup\\\":\\\"\\\",\\\"emp_name\\\":\\\"东坡区太和镇永丰村村民委员会\\\",\\\"dscg_way\\\":\\\"1\\\",\\\"brdy\\\":227808000000,\\\"ybtcPay\\\":2391.63,\\\"dipDiseGrpName\\\":null,\\\"trt_type\\\":\\\"3\\\",\\\"opsp_dise_name\\\":null,\\\"acp_optins_name\\\":\\\"\\\",\\\"gwyPay\\\":0.00,\\\"psn_cert_type\\\":\\\"01\\\",\\\"ntly\\\":\\\"CHN\\\",\\\"afadm_coma_days\\\":null,\\\"nwb_adm_wt\\\":null,\\\"bfadm_coma_m_cnt\\\":null,\\\"jmdbPay\\\":0.00,\\\"act_ipt_days\\\":12,\\\"lv3_nurscare_days\\\":12.0,\\\"setl_enddate\\\":1722441600000,\\\"nwb_bir_wt\\\":null,\\\"dscg_diag\\\":null,\\\"adm_time\\\":1721404800000,\\\"medins_fill_dept\\\":\\\"病案科\\\",\\\"medcasno\\\":\\\"00034107\\\",\\\"vent_used_m_cnt\\\":null,\\\"dscg_caty\\\":\\\"626\\\",\\\"vent_used_days\\\":null,\\\"ipt_med_type\\\":\\\"1\\\",\\\"age\\\":47.0,\\\"hsorg_name\\\":null},\\\"payinfo\\\":[{\\\"fund_pay_type\\\":\\\"390100\\\",\\\"poolarea_fund_pay_type\\\":\\\"390101\\\",\\\"fund_payamt\\\":2391.63,\\\"psn_no\\\":\\\"51000051140000001009812252\\\",\\\"poolarea_fund_pay_name\\\":\\\"城乡居民基本医疗保险基金\\\",\\\"mdtrt_id\\\":\\\"511400G0000054822180\\\",\\\"setl_id\\\":\\\"511400G0000040509125\\\"}],\\\"drgAppyinfo\\\":null,\\\"oprninfo\\\":[{\\\"anst_dr_name\\\":\\\"\\\",\\\"setl_list_oprn_id\\\":\\\"11432359\\\",\\\"main_oprn_flag\\\":\\\"1\\\",\\\"mdtrt_id\\\":\\\"511400G0000054822180\\\",\\\"anst_dr_code\\\":\\\"\\\",\\\"oprn_oprt_begntime\\\":1721404800000,\\\"setl_id\\\":\\\"511400G0000040509125\\\",\\\"oprn_oprt_name\\\":\\\"电针治疗\\\",\\\"anst_endtime\\\":null,\\\"anst_way\\\":\\\"\\\",\\\"psn_no\\\":\\\"51000051140000001009812252\\\",\\\"oper_dr_code\\\":\\\"D511403003226\\\",\\\"oper_dr_name\\\":\\\"刘天森\\\",\\\"oprn_oprt_code\\\":\\\"17.912A0\\\",\\\"oprn_oprt_date\\\":null,\\\"oprn_oprt_endtime\\\":1721404800000,\\\"anst_begntime\\\":null},{\\\"anst_dr_name\\\":\\\"\\\",\\\"setl_list_oprn_id\\\":\\\"11432360\\\",\\\"main_oprn_flag\\\":\\\"0\\\",\\\"mdtrt_id\\\":\\\"511400G0000054822180\\\",\\\"anst_dr_code\\\":\\\"\\\",\\\"oprn_oprt_begntime\\\":1721404800000,\\\"setl_id\\\":\\\"511400G0000040509125\\\",\\\"oprn_oprt_name\\\":\\\"悬灸治疗\\\",\\\"anst_endtime\\\":null,\\\"anst_way\\\":\\\"\\\",\\\"psn_no\\\":\\\"51000051140000001009812252\\\",\\\"oper_dr_code\\\":\\\"D511403003226\\\",\\\"oper_dr_name\\\":\\\"刘天森\\\",\\\"oprn_oprt_code\\\":\\\"17.91330\\\",\\\"oprn_oprt_date\\\":null,\\\"oprn_oprt_endtime\\\":1721404800000,\\\"anst_begntime\\\":null},{\\\"anst_dr_name\\\":\\\"\\\",\\\"setl_list_oprn_id\\\":\\\"11432361\\\",\\\"main_oprn_flag\\\":\\\"0\\\",\\\"mdtrt_id\\\":\\\"511400G0000054822180\\\",\\\"anst_dr_code\\\":\\\"\\\",\\\"oprn_oprt_begntime\\\":1721404800000,\\\"setl_id\\\":\\\"511400G0000040509125\\\",\\\"oprn_oprt_name\\\":\\\"中药局部熏洗治疗\\\",\\\"anst_endtime\\\":null,\\\"anst_way\\\":\\\"\\\",\\\"psn_no\\\":\\\"51000051140000001009812252\\\",\\\"oper_dr_code\\\":\\\"D511403003226\\\",\\\"oper_dr_name\\\":\\\"刘天森\\\",\\\"oprn_oprt_code\\\":\\\"17.95510\\\",\\\"oprn_oprt_date\\\":null,\\\"oprn_oprt_endtime\\\":1721404800000,\\\"anst_begntime\\\":null},{\\\"anst_dr_name\\\":\\\"\\\",\\\"setl_list_oprn_id\\\":\\\"11432362\\\",\\\"main_oprn_flag\\\":\\\"0\\\",\\\"mdtrt_id\\\":\\\"511400G0000054822180\\\",\\\"anst_dr_code\\\":\\\"\\\",\\\"oprn_oprt_begntime\\\":1722096000000,\\\"setl_id\\\":\\\"511400G0000040509125\\\",\\\"oprn_oprt_name\\\":\\\"腰椎间盘突出推拿治疗\\\",\\\"anst_endtime\\\":null,\\\"anst_way\\\":\\\"\\\",\\\"psn_no\\\":\\\"51000051140000001009812252\\\",\\\"oper_dr_code\\\":\\\"D511403003226\\\",\\\"oper_dr_name\\\":\\\"刘天森\\\",\\\"oprn_oprt_code\\\":\\\"17.92650\\\",\\\"oprn_oprt_date\\\":null,\\\"oprn_oprt_endtime\\\":1722096000000,\\\"anst_begntime\\\":null}]},\\\"infcode\\\":0,\\\"warn_msg\\\":null,\\\"cainfo\\\":null,\\\"err_msg\\\":null,\\\"refmsg_time\\\":\\\"20241104162542578\\\",\\\"signtype\\\":null,\\\"respond_time\\\":\\\"20241104162544468\\\",\\\"inf_refmsgid\\\":\\\"510000202411041625420370921884\\\"}\"";

                log.info("4103获取的返回值{}", upload4103AResponse);
                Map response4101AMap = JSON.parseObject(JSON.parse(upload4103AResponse).toString(), Map.class);
                log.info("获取到的response4101AMap：{}", response4101AMap);
                Object output = response4101AMap.get("output");
                log.info("获取到的output：", output);

                //处理4103返回的数据
                Map outputMap = JSON.parseObject(JSON.parse(output.toString()).toString(), Map.class);

                //获取主要信息节点数据
                Object setlinfo = outputMap.get("setlinfo");
                SomSettlementSetlinfoDto somSettlementSetlinfoDto = JSON.parseObject(setlinfo.toString(), SomSettlementSetlinfoDto.class);
                if (somSettlementSetlinfoDto != null) {
                    //转换时间,插入主要节点信息
                    somSettlementSetlinfoDto.setDscgTime(convertTimestampStringToFormattedString(somSettlementSetlinfoDto.getDscgTime()));
                    somSettlementSetlinfoDto.setSetlBegndate(convertTimestampStringToFormattedString(somSettlementSetlinfoDto.getSetlBegndate()));
                    somSettlementSetlinfoDto.setBrdy(convertTimestampStringToFormattedString(somSettlementSetlinfoDto.getBrdy()));
                    somSettlementSetlinfoDto.setSetlEnddate(convertTimestampStringToFormattedString(somSettlementSetlinfoDto.getSetlTime()));
                    somSettlementSetlinfoDto.setAdmTime(convertTimestampStringToFormattedString(somSettlementSetlinfoDto.getAdmTime()));
                    uploadedListMapper.insertSetinfo(somSettlementSetlinfoDto);
                }
                //获取诊断节点数据
                Object diseinfo = outputMap.get("diseinfo");
                List<SomSettlementDiseinfo> somSettlementDiseinfoList = JSON.parseArray(diseinfo.toString(), SomSettlementDiseinfo.class);
                if (somSettlementDiseinfoList != null && !somSettlementDiseinfoList.isEmpty()) {
                    uploadedListMapper.insertDiseinfo(somSettlementDiseinfoList);
                }

                //获取手术节点数据
                Object oprninfo = outputMap.get("oprninfo");
                List<SomSettlementOprninfo> SomSettlementOprninfoList = JSON.parseArray(oprninfo.toString(), SomSettlementOprninfo.class);
                if (SomSettlementOprninfoList != null && !SomSettlementOprninfoList.isEmpty()) {
                    //处理时间
                    for (int j = 0; j < SomSettlementOprninfoList.size(); j++) {
                        SomSettlementOprninfo somSettlementOprninfo = SomSettlementOprninfoList.get(j);
                        somSettlementOprninfo.setOprnOprtDate(convertTimestampStringToFormattedString(somSettlementOprninfo.getOprnOprtDate()));
                        somSettlementOprninfo.setOprnOprtBegntime(convertTimestampStringToFormattedString(somSettlementOprninfo.getOprnOprtBegntime()));
                        somSettlementOprninfo.setOprnOprtEndTime(convertTimestampStringToFormattedString(somSettlementOprninfo.getOprnOprtEndTime()));
                        somSettlementOprninfo.setAnstBeginTime(convertTimestampStringToFormattedString(somSettlementOprninfo.getAnstBeginTime()));
                        somSettlementOprninfo.setAnstEndTime(convertTimestampStringToFormattedString(somSettlementOprninfo.getAnstEndTime()));
                    }
                    uploadedListMapper.insertOprninfo(SomSettlementOprninfoList);
                }
                //获取输血信息
                Object bldinfo = outputMap.get("bldinfo");
                List<SomSettlementBldinfo> SomSettlementBldinfoList = JSON.parseArray(bldinfo.toString(), SomSettlementBldinfo.class);
                if (SomSettlementBldinfoList != null && !SomSettlementBldinfoList.isEmpty()) {
                    uploadedListMapper.insertBldinfo(SomSettlementBldinfoList);
                }

                //获取icu数据
                Object icuinfo = outputMap.get("icuinfo");
                if (SomSettlementBldinfoList != null && !SomSettlementBldinfoList.isEmpty()) {
                    List<SomSettlementIcuinfo> SomSettlementIcuinfoList = JSON.parseArray(icuinfo.toString(), SomSettlementIcuinfo.class);
                    uploadedListMapper.insertIcuinfo(SomSettlementIcuinfoList);
                }

                //获取基金支付信息
                Object payinfo = outputMap.get("payinfo");
                List<SomSettlementPayinfo> SomSettlementPayinfoList = JSON.parseArray(payinfo.toString(), SomSettlementPayinfo.class);
                if (SomSettlementPayinfoList != null && !SomSettlementPayinfoList.isEmpty()) {
                    uploadedListMapper.insertPayinfo(SomSettlementPayinfoList);
                }

                //获取收费项目信息
                Object iteminfo = outputMap.get("iteminfo");
                List<SomSettlementIteminfo> SomSettlementIteminfoList = JSON.parseArray(iteminfo.toString(), SomSettlementIteminfo.class);
                if (SomSettlementIteminfoList != null && !SomSettlementIteminfoList.isEmpty()) {
                    uploadedListMapper.insertIteminfo(SomSettlementIteminfoList);
                }


            }
        }
    }

    @Override
    public void policyAdjustments(DataHandleCommonDto dto) {
        //先查询是否配置了政策调整
        Boolean  isAdjust = false;
        Object flag =SysCommonConfigUtil.get(DrgConst.PERCENTAGE_POLICY_ADJUSTMENTS);
        if(!ValidateUtil.isEmpty(flag)){
            isAdjust= Boolean.valueOf(flag.toString());
        }

        List<DipPayToPredictVo> settleList;
        if(isAdjust){
            if (DrgConst.GROUP_TYPE_NAME_DIP.equals(SysCommonConfigUtil.get(DrgConst.FZLX))) {
                settleList = generaScoreMapper.queryDipGeneraScoreData(dto);
            } else if (DrgConst.GROUP_TYPE_NAME_DRG.equals(SysCommonConfigUtil.get(DrgConst.FZLX))) {
                settleList = generaScoreMapper.queryDrgGeneraScoreData(dto);
            }
            else{
                throw new AppException("请配置医院医保支付类型是属于DRG还是DIP");
            }
            HospBaseBusiVo hospBaseBusiVo = new HospBaseBusiVo();
            hospBaseBusiVo.setDipPayToPredictVoList(settleList);

            List<DipPayToPredictVo> scoreList =  paymentForecastLiteFlowService.PercentagePolicyAdjustments(hospBaseBusiVo);
            if(scoreList.size() ==settleList.size()){
                throw new AppException("政策调整失败,请查看是否配置该政策");
            }
            if (DrgConst.GROUP_TYPE_NAME_DIP.equals(SysCommonConfigUtil.get(DrgConst.FZLX))) {
                generaScoreMapper.updateDipScoData(scoreList);
            } else if (DrgConst.GROUP_TYPE_NAME_DRG.equals(SysCommonConfigUtil.get(DrgConst.FZLX))) {
                generaScoreMapper.updateDrgScoData(scoreList);
            }
        }
    }

    @Override
    public List<MainCode> codingControls(ListUploadDto dto) {
        if("1".equals(dto.getType())){
            return settleListManageDao.codingControlsOnWm(dto);
        }else if( "2".equals(dto.getType())){
            return settleListManageDao.codingControlsOnTcm(dto);
        }else{
            return settleListManageDao.codingControlsOnOprn(dto);
        }
    }

    public static String convertTimestampStringToFormattedString(String timestampStr) {
        if (timestampStr == null || timestampStr.trim().isEmpty()) {
            return null; // 或者返回空字符串 ""
        }

        try {
            // 将字符串转换为 long 类型的时间戳
            long timestamp = Long.parseLong(timestampStr);

            // 将毫秒时间戳转换为 Instant
            Instant instant = Instant.ofEpochMilli(timestamp);

            // 设置时区，这里使用的是系统默认时区
            ZoneId zoneId = ZoneId.systemDefault();

            // 转换为 LocalDateTime
            LocalDateTime localDateTime = instant.atZone(zoneId).toLocalDateTime();

            // 定义日期时间格式
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss");

            // 格式化日期时间
            return localDateTime.format(formatter);
        } catch (NumberFormatException e) {
            throw new IllegalArgumentException("无效的时间戳字符串: " + timestampStr, e);
        }
    }

    @Override
    public List<ListUploadVo> queryUploadedData(ListUploadDto dto) {
        updateInsuPlaceType(dto);
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        return listUploadMapper.queryUploadedData(dto);
    }

    @Override
    public List<ListUploadVo> queryUploadedSybData(ListUploadDto dto) {
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        return listUploadMapper.queryUploadedSybData(dto);
    }

    @Override
    public Map<String, Object> querySettleListInfo(ListUploadDto dto) {
        Map<String, Object> res = new HashMap<>();
        if (ValidateUtil.isEmpty(dto.getPsnNo()) || ValidateUtil.isEmpty(dto.getSetlId())) {
            return res;
        }

        // 获取清单配置信息
        String defalutInsuplcAdmdvs = (String) SysCommonConfigUtil.get(DrgConst.INSUPLC_ADMDVS_AREA);
        // 获取清单配置信息
        List<SettleListConfigVo> settleListConfigVoList = listUploadMapper.querySettleConfig(dto);
        Map<String, SettleListConfigVo> settleListConfigVoMap = settleListConfigVoList.stream().collect(Collectors.toMap(SettleListConfigVo::getInsuplcAdmdvs, settleListConfigVo -> settleListConfigVo));

        //dto必须传值省医保还是市医保，默认市医保
        if(ValidateUtil.isEmpty(dto.getInsuplcAdmdvs())){
            dto.setInsuplcAdmdvs(defalutInsuplcAdmdvs);
        }
        SettleListConfigVo configVo = settleListConfigVoMap.get(dto.getInsuplcAdmdvs());

        // 设置发送报文ID
        String curTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        configVo.setMsgid(configVo.getMsgid() + curTime + ORDER_NO);
        configVo.setInf_time(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        Map configMap = MapUtil.objectToMap(configVo, false, true);
        configMap.remove("url");
        Map<String, Object> params4103 = new HashMap<>();
        params4103.putAll(configMap);
        params4103.put(SettleListUploadUtil.INFO_NO_KEY, SettleListUploadUtil.INFO_NO_4103);

        // 获取9001
        Map response9001Map = SettleListUploadUtil.get9001Params(configMap, configVo);
        Object signNo = SettleListUploadUtil.getResByKey(response9001Map,
                "output:signinoutb:sign_no", SettleListUploadUtil.INFO_NO_9001, new SetlInfoVo());
        params4103.put(SettleListUploadUtil.SIGN_NO_KEY, signNo);

        Map<String, Object> inputNode = new HashMap<>();
        Map<String, Object> data = new HashMap<>();
        data.put("psn_no", dto.getPsnNo());
        data.put("setl_id", dto.getSetlId());
        inputNode.put("data", data);
        params4103.put(SettleListUploadUtil.INPUT_NODE_NAME, inputNode);
        String upload4103Response = HttpRequestUtil.post(params4103, configVo.getUrl());
        Map response4103AMap = JSON.parseObject(JSON.parse(upload4103Response).toString(), Map.class);
        if (SettleListUploadUtil.REQUEST_SUCCESS.equals(response4103AMap.get(SettleListUploadUtil.REQUEST_SUCCESS_KEY).toString())) {
            log.info("[4103]请求成功，返回结果: {}", response4103AMap);
            res.put("info", upload4103Response);
        } else {
            log.info("[4103]请求失败，返回结果: {}, - {}", response4103AMap.get(SettleListUploadUtil.REQUEST_ERROR).toString(), upload4103Response);
            res.put("errMsg", response4103AMap.get(SettleListUploadUtil.REQUEST_ERROR).toString());
        }
        return res;
    }

    @Override
    public List<UploadFalseVo> queryUploadFalseData(ListUploadDto dto) {
        List<ListUploadVo> lists = listUploadMapper.queryUploadedData(dto);
        final String SPLIT_STEP_1 = "异常流水号";
        final String TYPE_1 = "FSI-FSI";
        final String TYPE_2 = "fsi调用mbs异常";
        final String ERROR_1 = "未查询到医师信息";
        //分组
        Map<String, UploadFalseVo> falseVoMap = new HashMap<>();
        if (ValidateUtil.isNotEmpty(lists)) {
            lists.forEach(item -> {
                String errMsg = item.getErrMsg();
                String errMsgStep1Str = errMsg.substring(0, errMsg.indexOf(SPLIT_STEP_1) - 1);
                String typeStr = null;

                if (errMsgStep1Str.contains(TYPE_1)) {
                    typeStr = errMsgStep1Str.substring(errMsgStep1Str.indexOf(TYPE_1) + TYPE_1.length() + 1);
                    String[] splitError = typeStr.split(",");
                    if (splitError.length > 1) {
                        for (String e : splitError) {
                            judgeError(falseVoMap, item, e);
                        }
                    } else if (splitError.length == 1) {
                        judgeError(falseVoMap, item, splitError[0]);
                    }
                } else if (errMsgStep1Str.contains(TYPE_2)) {
                    typeStr = errMsgStep1Str.substring(errMsgStep1Str.indexOf(TYPE_2) + TYPE_2.length() + 1);
                    if (typeStr.contains(ERROR_1)) {
                        typeStr = ERROR_1;
                    }
                    judgeError(falseVoMap, item, typeStr);
                }
            });
        }

        List<UploadFalseVo> list = new ArrayList<>();
        falseVoMap.keySet().forEach(key -> list.add(falseVoMap.get(key)));
        return list;
    }

    /**
     * 批量上传清单
     *
     * @param file
     * @return
     */
    @Override
    public Map<String, Object> batchListUpload(MultipartFile file) throws IOException {
        try {
            List<BatchListUploadConfig> files = EasyPoiUtil.importExcel(file, BatchListUploadConfig.class);
            List<String> settleIds = new ArrayList<>();
            for (BatchListUploadConfig config :
                    files) {
                settleIds.add(config.getSettleId());
            }
            List<BatchListUploadVo> batchListUploadVos = listUploadMapper.queryIDAndK00By(settleIds);
            List<String> k00s = new ArrayList<>();
            List<String> ids = new ArrayList<>();
            for (BatchListUploadVo vo :
                    batchListUploadVos) {
                k00s.add(vo.getK00());
                ids.add(vo.getId());
            }
            if (ValidateUtil.isEmpty(k00s) || ValidateUtil.isEmpty(ids)) {
                throw new AppException("未查询到数据，无法上传！！！");
            }
            ListUploadDto dto = new ListUploadDto();
            dto.setK00s(k00s);
            dto.setIds(ids);
            // 手动批量上传 : 0 代表未上传 1 代表已上传
            dto.setUploadFlag("0");
            Map<String, Object> res = updateData(dto);
            return res;
        } catch (IOException e) {
            throw new IOException("上传文件为空！！！");
        }
    }

    @Override
    public void modifySettleListType(ListUploadDto dto) {
        setUploadParams(dto);
        modifySettleListState(dto);
    }

    private void modifySettleListState(ListUploadDto dto) {
        List<Map<String, Object>> dataList = listUploadMapper.queryModifySettleListData(dto);
        if (ValidateUtil.isEmpty(dataList)) {
            throw new AppException("无可修改数据");
        }
        // 获取清单配置信息
//        SettleListConfigVo configVo = listUploadMapper.querySettleConfig(dto);
        // 获取清单配置信息
        List<SettleListConfigVo> settleListConfigVoList = listUploadMapper.querySettleConfig(dto);
        Map<String, SettleListConfigVo> settleListConfigVoMap = settleListConfigVoList.stream().collect(Collectors.toMap(SettleListConfigVo::getInsuplcAdmdvs, settleListConfigVo -> settleListConfigVo));

        String hospitalId = null;
        SettleListConfigVo configVo;
        for (Map<String, Object> map : dataList) {
            //根据基础信息中的清算机构统筹区，获取对应的configVo
            configVo = settleListConfigVoMap.get(map.get("insuplcAdmdvs"));
            hospitalId = configVo.getFixmedins_code();

            // 设置发送报文ID
            String curTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
            configVo.setMsgid(hospitalId + curTime + ORDER_NO);
            configVo.setInf_time(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            Map configMap = MapUtil.objectToMap(configVo, false, true);
            configMap.remove("url");
            Map<String, Object> params4102 = new LinkedHashMap<>();
            params4102.putAll(configMap);
            //采用交易编号字段
            params4102.put(SettleListUploadUtil.INFO_NO, SettleListUploadUtil.INFO_NO_4102);

            // 获取9001
            Map response9001Map = SettleListUploadUtil.get9001Params(configMap, configVo);
            Object signNo = SettleListUploadUtil.getResByKey(response9001Map,
                    "output:signinoutb:sign_no", SettleListUploadUtil.INFO_NO_9001, new SetlInfoVo());
            params4102.put(SettleListUploadUtil.SIGN_NO_KEY, signNo);
            Map<String, Object> inputNode = new LinkedHashMap<>();
            Map<String, Object> data = new LinkedHashMap<>();
            List<Map<String, Object>> stastinfo = new ArrayList<>();
            stastinfo.add(map);
            data.put("stastinfo", stastinfo);
            inputNode.put("data", data);
            params4102.put(SettleListUploadUtil.INPUT_NODE_NAME, inputNode);
            String resultData = HttpRequestUtil.post(params4102, configVo.getUrl());
            log.info("输出: " + resultData);
        }
        if (DrgConst.STAS_TYPE_0.equals(dto.getIsSuccess())) {
            //未提交，修改后变为已提交
            dto.setIsSuccess(DrgConst.STAS_TYPE_1);
            // 修改状态
            listUploadMapper.updateStasType(dto);
        } else {
            //已提交，修改后变为未提交
            dto.setIsSuccess(DrgConst.STAS_TYPE_0);
            //删除清单上传记录
            listUploadMapper.deleteUploadLog(dto);
            //将清单设置为未上传
            listUploadMapper.updateData(dto);
        }
    }

    private void judgeError(Map<String, UploadFalseVo> falseVoMap, ListUploadVo item, String errorStr) {
        if (falseVoMap.containsKey(errorStr)) {
            UploadFalseVo vo = falseVoMap.get(errorStr);
            vo.setName(vo.getName());
            vo.setValue(vo.getValue() + 1);
            vo.setK00(vo.getK00() + ',' + item.getK00());
            falseVoMap.put(errorStr, vo);
        } else {
            UploadFalseVo falseVo = new UploadFalseVo();
            falseVo.setName(errorStr);
            falseVo.setK00(item.getK00());
            falseVo.setValue(1);
            falseVoMap.put(errorStr, falseVo);
        }
    }

    public String formatHour(String hour) {
        String scs_cutd_sum_dura = "0/0/0";
        if (!ValidateUtil.isEmpty(hour)) {
            BigDecimal calHour = new BigDecimal(hour);
            BigDecimal actDay = calHour.divide(new BigDecimal("24"), 2, BigDecimal.ROUND_HALF_UP).setScale(0, BigDecimal.ROUND_DOWN);
            BigDecimal tempHour = actDay.multiply(new BigDecimal("24"));
            BigDecimal actHour = calHour.subtract(tempHour).setScale(0, BigDecimal.ROUND_DOWN);
            BigDecimal actMin = calHour.subtract(tempHour).subtract(actHour).multiply(new BigDecimal("60"));
            scs_cutd_sum_dura = actDay + "/" + actHour + "/" + actMin.toBigInteger();
            return scs_cutd_sum_dura;
        } else {
            return scs_cutd_sum_dura;
        }
    }


    @Override
    @Transactional
    public int revokeTheidentity(ListUploadDto dto) {
        //记录进lable表
       int lablenum = listUploadMapper.deleteLabl(dto);
        //添加清单结算标识日志
        int lognum = listUploadMapper.insertSettleListOpeLog(dto);
        //修改清单结算标识
        int locknum = listUploadMapper.updateSettleListLookOverBySeTime(dto);
        if(locknum!=lognum || locknum!=lablenum || lablenum!=lognum){
            throw new AppException("修改条数不一致");
        }
        return locknum;
    }

    @Override
    @Transactional
    public int dataWithdrawal(ListUploadDto dto) {

        int deleteFlagenum =  listUploadMapper.deleteUploadLog(dto);

        if(dto.getUpldStas().equals("1")) {
            //说明吃此时处理上传
            //修改upload_flag
            int updateFlagenum = listUploadMapper.updateUpldFlag(dto);
            if(deleteFlagenum!=updateFlagenum ){
                throw new AppException("撤回上传成功条数有误");
            }
        }

        String UPLOAD_DRAWN_UNMARK = (String) SysCommonConfigUtil.get(DrgConst.UPLOAD_DRAWN_UNMARK);
        if (UPLOAD_DRAWN_UNMARK != null && "true".equals(UPLOAD_DRAWN_UNMARK.toString().trim())) {
            int locknum = revokeTheidentity(dto);
            if (deleteFlagenum != locknum) {
                throw new AppException("撤回条数有误");
            }
        }
        return deleteFlagenum;
    }

    @Override
    public void selectStateUpdate(ListUploadDto dto) {
        List<String> list = listUploadMapper.selectStateUpdateMedsno(dto);
        if(list.size() > 0 ){
            throw new AppException("请先在状态修改撤回，病案号为" + JSONObject.toJSON(list).toString() +"的数据");
        }
    }

}
