package com.my.som.service.hospitalAnalysis;

import com.my.som.dto.hospitalAnalysis.HospitalAnalysisQueryParam;
import com.my.som.vo.hospitalAnalysis.OperativeAnalysisCountInfo;
import com.my.som.vo.hospitalAnalysis.OperativeAnalysisInfo;

import java.util.List;

/**
 * Service
 * Created by sky on 2020/3/17.
 */
public interface OperativeAnalysisService {
    /**
     * 查询医院手术分析主要信息
     * @param queryParam
     * @return
     */
    List<OperativeAnalysisInfo> list(HospitalAnalysisQueryParam queryParam, Integer pageSize, Integer pageNum);

    /**
     * 查询医院手术分析统计信息
     * @param queryParam
     * @return
     */
    List<OperativeAnalysisCountInfo> getCountInfo(HospitalAnalysisQueryParam queryParam);

}
