package com.my.som.service.drgReasonableCost.impl;


import com.my.som.dao.drgReasonableCost.HighLowRatioParamsConfigDao;
import com.my.som.dto.drgReasonableCost.DrgReasonableCostQueryParam;
import com.my.som.mapper.common.SomCdControlFeeStsbCfgMapper;
import com.my.som.model.common.SomCdControlFeeStsbCfg;
import com.my.som.model.common.SomCdControlFeeStsbCfgExample;
import com.my.som.service.drgReasonableCost.HighLowRatioParamsConfigService;
import com.my.som.vo.drgReasonableCost.HighLowRatioParamsConfigVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;


/**
 * Service实现类
 * Created by sky on 2020/1/5.
 */
@Service
public class HighLowRatioParamsConfigServiceImpl implements HighLowRatioParamsConfigService {
    @Autowired
    private HighLowRatioParamsConfigDao highLowRatioParamsConfigDao;

    @Autowired
    private SomCdControlFeeStsbCfgMapper cfgCostControlRateMapper;

    @Override
    public HighLowRatioParamsConfigVo getAllParams(DrgReasonableCostQueryParam queryParam) {
        return highLowRatioParamsConfigDao.getAllParams(queryParam);
    }

    @Override
    public int updateAllInfo(HighLowRatioParamsConfigVo listQuery) {
        SomCdControlFeeStsbCfg somCdControlFeeStsbCfg = new SomCdControlFeeStsbCfg();
        somCdControlFeeStsbCfg.setControlFeeStsbMinProp(new BigDecimal(listQuery.getLowRatioDrgPayCost()));
        somCdControlFeeStsbCfg.setControlFeeStsbMaxProp(new BigDecimal(listQuery.getHighRatioDrgPayCost()));
        somCdControlFeeStsbCfg.setStdWtLowMagLowlmt(new BigDecimal(listQuery.getLowRatioDrgWeight()));
        somCdControlFeeStsbCfg.setStdWtHighmagUplmt(new BigDecimal(listQuery.getHighRatioDrgWeight()));
        somCdControlFeeStsbCfg.setStdTimeLowMagLowlmt(new BigDecimal(listQuery.getLowRatioInHosDays()));
        somCdControlFeeStsbCfg.setStdTimeHighmagUplmt(new BigDecimal(listQuery.getHighRatioInHosDays()));
        somCdControlFeeStsbCfg.setStdFeeMagLmt(new BigDecimal(listQuery.getLowRatioInHosCost()));
        somCdControlFeeStsbCfg.setStdFeeHighmagUplmt(new BigDecimal(listQuery.getHighRatioInHosCost()));
        //更新参数信息
        SomCdControlFeeStsbCfgExample example = new SomCdControlFeeStsbCfgExample();
        example.createCriteria().andIdEqualTo(new Long(1));
        return cfgCostControlRateMapper.updateByExampleSelective(somCdControlFeeStsbCfg,example);
    }
}
