package com.my.som.service.drgReasonableCost;

import com.my.som.dto.drgReasonableCost.DrgReasonableCostQueryParam;
import com.my.som.vo.drgReasonableCost.DrgCostControlVo;

import java.util.List;

/**
 * Service
 * Created by sky on 2020/3/17.
 */
public interface DrgCostControlService {
    /**
     * 获取DRG付费逆差数据
     * @param queryParam
     * @return
     */
    List<DrgCostControlVo> list(DrgReasonableCostQueryParam queryParam, Integer pageSize, Integer pageNum);

}
