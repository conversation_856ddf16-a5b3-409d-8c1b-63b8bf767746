package com.my.som.service.integratedQuery;

import com.my.som.dto.integratedQuery.IntegratedQueryDto;
import com.my.som.vo.integratedQuery.IntegratedQueryVo;

import java.util.List;
import java.util.Map;

/**
 * @author: zyd
 * @version 1.0
 * @description:
 * @date: 2023-07-31
 */
public interface IntegratedQueryService {
    /**
     * 获取页面展示字段
     * @param dto
     * @return
     */
    Map<String,List<IntegratedQueryVo>> list(IntegratedQueryDto dto);



    /**
     * 查询数据
     * @param dto
     * @return
     */
    List<Map<String,Object>> queryData(IntegratedQueryDto dto);
}
