package com.my.som.service.hospitalAnalysis.impl;

import com.github.pagehelper.PageHelper;
import com.my.som.common.exception.AppException;
import com.my.som.common.util.ValidateUtil;
import com.my.som.dao.hospitalAnalysis.DipSafetyAndQualityDao;
import com.my.som.dao.hospitalAnalysis.DrgsSafetyAndQualityDao;
import com.my.som.dto.hospitalAnalysis.HospitalAnalysisQueryParam;
import com.my.som.service.hospitalAnalysis.DipSafetyAndQualityService;
import com.my.som.service.hospitalAnalysis.DrgsSafetyAndQualityService;
import com.my.som.vo.hospitalAnalysis.DrgsSafetyAndQualityCountVo;
import com.my.som.vo.hospitalAnalysis.RiskGradeLineVo;
import com.my.som.vo.hospitalAnalysis.RiskLevelMedicalDetailVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;

/**
 * Service实现类
 * Created by sky on 2020/1/5.
 */
@Service
public class DipSafetyAndQualityServiceImpl implements DipSafetyAndQualityService {
    @Autowired
    private DipSafetyAndQualityDao dipSafetyAndQualityDao;

    @Override
    public List<RiskLevelMedicalDetailVo> list(HospitalAnalysisQueryParam queryParam,Integer pageSize, Integer pageNum) {
        PageHelper.startPage(pageNum, pageSize);
        if(!ValidateUtil.isEmpty(queryParam.getLyfs())){
            queryParam.setLyfsList(Arrays.asList(queryParam.getLyfs().split(",")));
        }
        if(!ValidateUtil.isEmpty(queryParam.getRiskLevel())){
            queryParam.setRiskLevelList(Arrays.asList(queryParam.getRiskLevel().split(",")));
        }
        return dipSafetyAndQualityDao.list(queryParam);
    }

    @Override
    public DrgsSafetyAndQualityCountVo getCountInfo(HospitalAnalysisQueryParam queryParam) {
        DrgsSafetyAndQualityCountVo drgsSafetyAndQualityCountVo = new DrgsSafetyAndQualityCountVo();
        try {
            drgsSafetyAndQualityCountVo.setRiskDRGsList(dipSafetyAndQualityDao.getRiskLevelData(queryParam));
            List<RiskGradeLineVo> riskLevelLine = dipSafetyAndQualityDao.getRiskLevelLine(queryParam);
            drgsSafetyAndQualityCountVo.setRiskLevelLine(riskLevelLine);
        } catch (Exception e){
            e.printStackTrace();
            throw new AppException("查询drgs安全质量分析指标信息出现异常");
        }
        return drgsSafetyAndQualityCountVo;
    }
}
