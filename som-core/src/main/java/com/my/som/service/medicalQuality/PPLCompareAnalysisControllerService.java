package com.my.som.service.medicalQuality;

import com.my.som.dto.medicalQuality.CompareAnalysisDto;
import com.my.som.vo.medicalQuality.PPLCompareAnalysisVo;

import java.util.List;

/**
 * @author: zyd
 * @Date 2021/9/23 2:37 下午
 * @Version 1.0
 * @Description: 人员信息对比
 */
public interface PPLCompareAnalysisControllerService {

    /**
     * 获取人员信息
     * @param dto
     * @return
     */
    List<PPLCompareAnalysisVo> getList(CompareAnalysisDto dto);
    /**
     * 获取基本信息
     * @param dto
     * @return
     */
    List<PPLCompareAnalysisVo> getInfo(CompareAnalysisDto dto);
    /**
     * 获取入组信息
     * @param dto
     * @return
     */
    List<PPLCompareAnalysisVo> getIngroup(CompareAnalysisDto dto);
    /**
     * 获取费用信息
     * @param dto
     * @return
     */
    List<PPLCompareAnalysisVo> getCost(CompareAnalysisDto dto);
    /**
     * 获取费用信息
     * @param dto
     * @return
     */
    List<PPLCompareAnalysisVo> getCostPay(CompareAnalysisDto dto);

}
