package com.my.som.service.newBusiness.feedbackGatherAnalyis;

import com.my.som.common.api.CommonResult;
import com.my.som.dto.newBusiness.FeedbackGatherAnalyis.FeedbackGatherAnalyisDto;
import com.my.som.vo.newBusiness.FeedbackGatherAnalyis.FeedbackGatherAnalyisVo;

import java.util.List;

public interface FeedbackGatherAnalyisService {
    /**
     * 分组反馈分析
     * @param dto
     */
    List<FeedbackGatherAnalyisVo> queryFeedbackGatherData(FeedbackGatherAnalyisDto dto);
}
