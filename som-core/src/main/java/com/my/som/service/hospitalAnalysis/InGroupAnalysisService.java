package com.my.som.service.hospitalAnalysis;

import com.my.som.dto.hospitalAnalysis.HospitalAnalysisQueryParam;
import com.my.som.vo.hospitalAnalysis.InGroupTopCountInfo;
import com.my.som.vo.hospitalAnalysis.InGroupAnalysisCountInfo;
import com.my.som.vo.hospitalAnalysis.InGroupAnalysisInfo;

import java.util.List;

/**
 * Service
 * Created by sky on 2020/3/17.
 */
public interface InGroupAnalysisService {
    /**
     * 查询医院入组分析主要信息
     * @param queryParam
     * @return
     */
    List<InGroupAnalysisInfo> list(HospitalAnalysisQueryParam queryParam, Integer pageSize, Integer pageNum);

    /**
     * 查询全院入组情况人次统计信息
     * @param queryParam
     * @return
     */
    InGroupTopCountInfo getTopCountInfo(HospitalAnalysisQueryParam queryParam);

    /**
     * 查询未入组原因统计信息
     * @param queryParam
     * @return
     */
    List<InGroupAnalysisCountInfo> getNoGroupResonCountInfo(HospitalAnalysisQueryParam queryParam);

}
