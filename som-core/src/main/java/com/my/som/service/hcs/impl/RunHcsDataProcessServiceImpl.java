package com.my.som.service.hcs.impl;

import com.alibaba.druid.util.StringUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.ctc.wstx.util.StringUtil;
import com.github.pagehelper.PageHelper;
import com.my.som.common.constant.HcsConst;
import com.my.som.common.exception.AppException;
import com.my.som.common.util.*;
import com.my.som.common.vo.ResultEntity;
import com.my.som.dto.pregroup.PreGroupDto;
import com.my.som.dts.entity.param.CommonParam;
import com.my.som.dts.entity.param.Hcm9001Param;
import com.my.som.hcs.engine.DataValidationEngine;
import com.my.som.hcs.engine.dto.HcmDataprosLogDto;
import com.my.som.hcs.engine.dto.MedFeeItemQueryDto;
import com.my.som.hcs.engine.dto.MedWorkBenchQueryDto;
import com.my.som.hcs.engine.vo.*;
import com.my.som.hcs.engine.vo.workbench.*;
import com.my.som.hcs.mapper.RuleConfigManagerMapper;
import com.my.som.hcs.mapper.RuleValidProcManagerMapper;
import com.my.som.service.dataHandle.HcsFeeItemHandle;
import com.my.som.service.dataHandle.HcsMedPersionHandle;
import com.my.som.service.hcs.RunHcsDataProcessService;
import com.my.som.task.HcsBatchRunProcTask;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.session.ResultContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.data.redis.core.RedisOperations;
import org.springframework.data.redis.core.SessionCallback;
import org.springframework.stereotype.Service;

import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class RunHcsDataProcessServiceImpl implements RunHcsDataProcessService {

    @Autowired
    DataValidationEngine dataValidationEngine;

    @Autowired
    RuleConfigManagerMapper ruleConfigManagerMapper;

    @Autowired
    RuleValidProcManagerMapper ruleValidProcManagerMapper;

    private static String EXCT_OPERATOR = "|";

    private final static Integer THREAD_COUNT = 5;
    private static ThreadUtil<ValidMedDataInfo> threadUtils = ThreadUtil.newInstance(THREAD_COUNT);

    @Override
    public boolean runHcmProcByDto(PreGroupDto dto) {
        ValidMedDataInfo validMedDataInfo = generateMainByPreGroup(dto);
        ValidationResultVo validationResultVo = dataValidationEngine.validate(validMedDataInfo);
        List<ValidateDetailResultVo> resultErrors = validationResultVo.getValidateBaseErrors();
        String jsonString = JSON.toJSONString(resultErrors);
        JSONArray jsonArray = JSON.parseArray(jsonString);
        System.out.println(jsonArray.toJSONString());
        // 分配插入
        BatchNewUtil.batch("insertValidResultByHosp", resultErrors, RuleValidProcManagerMapper.class);
        return true;
    }

    @Override
    public int runProcSingle(CommonParam<Hcm9001Param> dto) {
        long startTimes = System.currentTimeMillis();
        log.info("===========" + "自查自纠单条执行流程启动：" + startTimes + " ===========");
        if (null == dto || null == dto.getInput()) {
           return 0;
        }

        Hcm9001Param input = dto.getInput();

        //启动日志写入逻辑
        HcmDataprosLog hcmDataprosLog = new HcmDataprosLog();
        hcmDataprosLog.setMedcas_val(1);
        hcmDataprosLog.setData_time_scp("单条执行");
        hcmDataprosLog.setStart_time(DateUtil.getCurDateTime());
        hcmDataprosLog.setProc_result(HcsConst.HCS_PROC_RESULT_RUNNING);
        hcmDataprosLog.setOprt_psn(dto.getOpter()+"|"+dto.getOpter_name());
        hcmDataprosLog.setHospital_id(dto.getFixmedins_code());
        hcmDataprosLog.setActive_flag(HcsConst.ACTIVE_FLAG_1);
        ruleValidProcManagerMapper.insertHcsProcLog(hcmDataprosLog);

        int[] medDataRunningArray = new int[]{0, 0};

        try {
            // 1、从Hcm9001Param转换为ValidMedDataInfo
            ValidMedDataInfo validMedDataInfo = generateValidMedDataInfoByHcm9001(input);
            if (validMedDataInfo == null) {
                log.warn("转换ValidMedDataInfo失败，无法执行自查自纠");
                return 0;
            }

            medDataRunningArray[0] = 1; // 检查病例数

            // 2、执行自查自纠验证
            ValidationResultVo validationResultVo = dataValidationEngine.validate(validMedDataInfo);

            // 3、处理验证结果
            if (validationResultVo != null) {
                List<ValidateDetailResultVo> resultErrors = validationResultVo.getValidateBaseErrors();
                if (ValidateUtil.isNotEmpty(resultErrors)) {
                    // 直接插入验证结果，不需要批量删除和Redis操作
                    BatchNewUtil.batch("insertValidResultByHosp", resultErrors, RuleValidProcManagerMapper.class);
                    medDataRunningArray[1] = 1; // 违规病例数
                    log.info("单条自查自纠执行完成，发现违规项目{}条", resultErrors.size());
                } else {
                    log.info("单条自查自纠执行完成，未发现违规项目");
                }
            }

        } catch (Exception e) {
            log.error("单条自查自纠执行异常", e);
            // 更新执行日志为失败状态
            hcmDataprosLog.setProc_result("执行异常：" + e.getMessage());
            //更新为一致性完成
            hcmDataprosLog.setProc_status("1");
        }

        long endTimes = System.currentTimeMillis();

        // 4、更新执行日志信息
        hcmDataprosLog.setExam_val(medDataRunningArray[0]);
        hcmDataprosLog.setDoubt_val(medDataRunningArray[1]);
        hcmDataprosLog.setChk_pass_val(medDataRunningArray[0] - medDataRunningArray[1]);
        hcmDataprosLog.setEnd_time(DateUtil.getCurDateTime());
        hcmDataprosLog.setProc_dura((double) (endTimes - startTimes) / 1000);
        hcmDataprosLog.setProc_result("单条自查自纠流程完成");
        hcmDataprosLog.setProc_status(HcsConst.HCS_PROC_RESULT_DONE);
        ruleValidProcManagerMapper.updateHcsProcLogById(hcmDataprosLog);

        log.info("===========" + "单条自查自纠流程完成：耗时 " + (endTimes - startTimes) + " ms ===========");
        return medDataRunningArray[1]; // 返回违规病例数
    }

    @Override
    public int runProcBatchByDate(MedFeeItemQueryDto dto) {
        long startTimes = System.currentTimeMillis();
        log.info("===========" + dto.getRuleScenType() + "自查自纠流程启动：" + startTimes + " ===========");
        int pendRunSize  = ruleValidProcManagerMapper.queryMedPersonInfoCount(dto);
        if (pendRunSize <= 0) {
            return 0;
        }
        //启动日志写入逻辑
        HcmDataprosLog hcmDataprosLog = new HcmDataprosLog();
        hcmDataprosLog.setMedcas_val(pendRunSize);
        hcmDataprosLog.setData_time_scp(getDataTimeScp(dto));
        hcmDataprosLog.setStart_time(DateUtil.getCurDateTime());
        hcmDataprosLog.setProc_result(HcsConst.HCS_PROC_RESULT_RUNNING);
        hcmDataprosLog.setOprt_psn(ValidateUtil.isEmpty(dto.getUsername()) ? HcsConst.RUNNING_OPRN_PSN : dto.getUsername());
        hcmDataprosLog.setHospital_id(dto.getHospitalId());
        hcmDataprosLog.setActive_flag(HcsConst.ACTIVE_FLAG_1);
        ruleValidProcManagerMapper.insertHcsProcLog(hcmDataprosLog);

        int[] medDataRunningArray = new int[]{0, 0};

        //1、删除参数范围内的自查自纠明细（后续转为转存到执行日志表中）门诊住院共有数据表
        ruleValidProcManagerMapper.deleteValidRecordByTimeRange(dto);

        //2、先获取对应时间范围内的所有明细数据，写入到redis中
        HcsBatchRunProcTask hcsBatchRunProcTask = new HcsBatchRunProcTask(dataValidationEngine);
        HashSet<String> uniqueIdSet = new HashSet<>();
        HcsFeeItemHandle<MedFeeItemInfoVo> resultHandler = new HcsFeeItemHandle<MedFeeItemInfoVo>() {
            private static final int BATCH_SIZE = 1000;
            private int size = 0;
            private int cur_size = 0;
            // 当前处理数据
            private List<MedFeeItemInfoVo> dataExtractionVoList = new ArrayList<>();
            private List<MedFeeItemInfoVo> result = new ArrayList<>();

            @Override
            public void handleResult(ResultContext<? extends MedFeeItemInfoVo> resultContext) {
                dataExtractionVoList.add(resultContext.getResultObject());
                size++;
                if (size == BATCH_SIZE) {
                    handler();
                }
            }

            private void handler() {
                try {
                    //插入数据
                    // RedisUtils.hmset(); 分片写入redis 的hash
                    pushFeeItemToRedis(dataExtractionVoList);
                    cur_size += size;
                    logger.info("当前已处理{}条数据!", cur_size);
                } finally {
                    size = 0;
                    dataExtractionVoList.clear();
                }
            }

            @Override
            public void endHandler() {
                logger.info("最终数据处理开始...");
                handler();
            }
        };
        ruleValidProcManagerMapper.queryMedFeeItemByHandle(dto, resultHandler);
        resultHandler.endHandler();

        //3、获取患者明细进行分片执行自查自纠
        HcsMedPersionHandle<MedPersionInfoVo> hcsMedPersionHandle = new HcsMedPersionHandle<MedPersionInfoVo>() {
            private static final int BATCH_SIZE = 1000;
            private int size = 0;
            private int cur_size = 0;
            // 当前处理数据
            private List<MedPersionInfoVo> dataExtractionVoList = new ArrayList<>();
            private List<ValidationResultVo> result = new ArrayList<>();
            List<ValidMedDataInfo> validMedDataInfoList = new ArrayList<>();

            @Override
            public void handleResult(ResultContext<? extends MedPersionInfoVo> resultContext) {
                dataExtractionVoList.add(resultContext.getResultObject());
                size++;
                if (size == BATCH_SIZE) {
                    handler();
                }
            }

            private void handler() {
                try {
                    // 循环遍历从redis中获取明细后，组装ValidMedDataInfo参数
                    validMedDataInfoList = generaterValidParamByMedPersionInfo(dataExtractionVoList);
                    medDataRunningArray[0] += validMedDataInfoList.size();
                    result = runProcValid(validMedDataInfoList, null, hcsBatchRunProcTask);
                    if (!ValidateUtil.isEmpty(result)) {
                        //插入数据
                        // 分片插入
                        medDataRunningArray[1] += batchInsertValidResultList(result);
                    }

                    for (MedPersionInfoVo medPersionInfoVo : dataExtractionVoList) {
                        uniqueIdSet.add(medPersionInfoVo.getUniqueId());
                    }

                    cur_size += size;
                    logger.info("当前已处理{}条数据!", cur_size);
                } finally {
                    size = 0;
                    dataExtractionVoList.clear();
                    result.clear();
                    validMedDataInfoList.clear();
                }
            }

            @Override
            public void endHandler() {
                logger.info("最终数据处理开始...");
                handler();
            }
        };
        ruleValidProcManagerMapper.queryMedPersonInfoByHandle(dto, hcsMedPersionHandle);
        hcsMedPersionHandle.endHandler();


        //4、删除redis中的缓存数据
        deleteRedisHashByUniqueId(uniqueIdSet);
        int resultSize = uniqueIdSet.size();
        uniqueIdSet.clear();
        long endTimes = System.currentTimeMillis();


        //5、更新执行日志信息 todo 异常处理
        hcmDataprosLog.setExam_val(medDataRunningArray[0]);
        hcmDataprosLog.setDoubt_val(medDataRunningArray[1]);
        hcmDataprosLog.setChk_pass_val(medDataRunningArray[0] - medDataRunningArray[1]);
        hcmDataprosLog.setEnd_time(DateUtil.getCurDateTime());
        hcmDataprosLog.setProc_dura((endTimes - startTimes) / 1000);
        hcmDataprosLog.setProc_result("自查自纠流程完成");
        hcmDataprosLog.setProc_status(HcsConst.HCS_PROC_RESULT_DONE);
        ruleValidProcManagerMapper.updateHcsProcLogById(hcmDataprosLog);

        log.info("===========" + dto.getRuleScenType() + "自查自纠流程完成：耗时 " + (endTimes - startTimes) + " ms ===========");
        return resultSize;
    }

    @Override
    public List<HcmDataprosLog> queryHcsProcList(HcmDataprosLogDto dto) {
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        return ruleValidProcManagerMapper.queryHcsProcList(dto);
    }

    @Override
    public WorkBenchGraphicResultVo queryAnalysisGraphic(MedWorkBenchQueryDto workBenchQueryDto) {
        WorkBenchGraphicResultVo workBenchGraphicResultVo = new WorkBenchGraphicResultVo();
        ViolationMedAnalysisVo violationMedAnalysisVo;
        List<ViolationRuleTypeAnalysisVo> violationRuleTypeAnalysisVo;
        List<ViolationAmountAnalysisVo> violationAmountAnalysisVo;

        violationMedAnalysisVo = ruleValidProcManagerMapper.queryViolationMedAnalysis(workBenchQueryDto);
        violationRuleTypeAnalysisVo = ruleValidProcManagerMapper.queryViolationRuleTypeAnalysis(workBenchQueryDto);
        violationAmountAnalysisVo = ruleValidProcManagerMapper.queryViolationAmountAnalysis(workBenchQueryDto);

        // 构建违规类型统计图表数据
        generaterCount(violationRuleTypeAnalysisVo, workBenchGraphicResultVo);
        workBenchGraphicResultVo.setViolationMedAnalysisVo(violationMedAnalysisVo);
//        workBenchGraphicResultVo.setViolationRuleTypeAnalysisVo(violationRuleTypeAnalysisVo);
        workBenchGraphicResultVo.setViolationAmountAnalysisVo(violationAmountAnalysisVo);

        return workBenchGraphicResultVo;
    }

    /**
     * 构建违规类型统计图表数据
     *
     * @param violationRuleTypeAnalysisVo 基础数据
     * @param workBenchGraphicResultVo    目标数据
     */
    private void generaterCount(List<ViolationRuleTypeAnalysisVo> violationRuleTypeAnalysisVo, WorkBenchGraphicResultVo workBenchGraphicResultVo) {
        List<String> ruleTypes = new ArrayList<>();

        List<String> ruleViolationMedSize = new ArrayList<>();

        List<String> ruleViolationMedRatioSize = new ArrayList<>();
        DecimalFormat df = new DecimalFormat("#.00");
        for (ViolationRuleTypeAnalysisVo ruleTypeAnalysisVo :
                violationRuleTypeAnalysisVo) {
            ruleTypes.add(ruleTypeAnalysisVo.getErrorDesc());
            ruleViolationMedSize.add(String.valueOf(ruleTypeAnalysisVo.getItemViolationSize()));
            ruleViolationMedRatioSize.add(df.format(ruleTypeAnalysisVo.getItemViolationRatio() * 100));
        }

        workBenchGraphicResultVo.setRuleTypes(ruleTypes);
        workBenchGraphicResultVo.setRuleViolationMedSize(ruleViolationMedSize);
        workBenchGraphicResultVo.setRuleViolationMedRatioSize(ruleViolationMedRatioSize);

    }

    @Override
    public List<ViolationItemDetilVo> queryAnalysisDataList(MedWorkBenchQueryDto workBenchQueryDto) {
        return ruleValidProcManagerMapper.queryAnalysisDataList(workBenchQueryDto);
    }

    /**
     * 获取数据时间范围
     *
     * @param dto
     * @return
     */
    private String getDataTimeScp(MedFeeItemQueryDto dto) {
        String dataTimeScp = "";
        // 结算时间
        if (!ValidateUtil.isEmpty(dto.getSeStartTime())) {
            dataTimeScp += "se[" + dto.getSeStartTime() + "-" + dto.getSeEndTime() + "]";
        }

        // 出院时间
        if (!ValidateUtil.isEmpty(dto.getBegnDate())) {
            dataTimeScp += "cy[" + dto.getBegnDate() + "-" + dto.getExpiDate() + "]";
        }

        // 入院时间
        if (!ValidateUtil.isEmpty(dto.getInStartTime())) {
            dataTimeScp += "ry[" + dto.getInStartTime() + "" + dto.getInEndTime() + "]";
        }
        return dataTimeScp;
    }

    /**
     * 从redis中删除数据
     *
     * @param stringHashSet
     */
    private void deleteRedisHashByUniqueId(HashSet<String> stringHashSet) {
        for (String uniqueId :
                stringHashSet) {
            RedisUtils.delete(uniqueId);
        }
    }

    /**
     * 批量插入质控结果数据
     *
     * @param validationResultVos
     */
    private int batchInsertValidResultList(List<ValidationResultVo> validationResultVos) {
        List<ValidateDetailResultVo> validateDetailResultVoList = new ArrayList<>();
        List<ValidateDetailResultVo> currVoList;
        int errorMedSize = 0;

        for (ValidationResultVo validationResultVo :
                validationResultVos) {
            currVoList = validationResultVo.getValidateBaseErrors();
            if (ValidateUtil.isNotEmpty(currVoList)) {
                validateDetailResultVoList.addAll(currVoList);
                errorMedSize += 1;
            }
        }
        log.info("=============插入数据库明细size" + validateDetailResultVoList.size() + "=============");
        //批量插入
        BatchNewUtil.batch("insertValidResultByHosp", validateDetailResultVoList, RuleValidProcManagerMapper.class);
        validateDetailResultVoList.clear();
        return errorMedSize;
    }

    /**
     * 批量插入redis
     *
     * @param dataExtractionVoList
     * @return
     */
    private int pushFeeItemToRedis(List<MedFeeItemInfoVo> dataExtractionVoList) {
        int resultSize = 0;
        RedisUtils.redisTemplate.executePipelined(new SessionCallback<Object>() {
            @Override
            public Object execute(RedisOperations operations) throws DataAccessException {
                for (MedFeeItemInfoVo medFeeItemInfoVo :
                        dataExtractionVoList) {
                    RedisUtils.redisTemplate.opsForHash().put(medFeeItemInfoVo.getUniqueId(), medFeeItemInfoVo.getFeeItemUniqueid(), medFeeItemInfoVo);
                }
                return null; // 返回值无关紧要，结果通过返回的List获取
            }
        });
        return resultSize;
    }

    /**
     * 根据人员数据组装自查自纠参数
     *
     * @param medPersionInfoVos
     * @return
     */
    private List<ValidMedDataInfo> generaterValidParamByMedPersionInfo(List<MedPersionInfoVo> medPersionInfoVos) {
        List<ValidMedDataInfo> validMedDataInfoList = new ArrayList<>();
        List<MedDiagInfoVo> medDiagInfoVoList;
        List<MedOprnInfoVo> medOprnInfoList;
        List<MedFeeItemInfoVo> medFeeItemInfoVoList;
        String[] diagNames;
        String[] oprnOprtNames;
        String delimiter = "@";
        ValidMedDataInfo validMedDataInfo;
        for (MedPersionInfoVo medPersionInfoVo :
                medPersionInfoVos) {
            medFeeItemInfoVoList = generateFeeItemList(medPersionInfoVo);
            if (ValidateUtil.isEmpty(medFeeItemInfoVoList)) {
                //无明细
                continue;
            }
            medDiagInfoVoList = new ArrayList<>();
            medOprnInfoList = new ArrayList<>();
            validMedDataInfo = new ValidMedDataInfo();
            validMedDataInfo.setMedcasno(medPersionInfoVo.getMedcasno());
            validMedDataInfo.setYm(medPersionInfoVo.getYm());
            validMedDataInfo.setUniqueId(medPersionInfoVo.getUniqueId());
            validMedDataInfo.setDataType(medPersionInfoVo.getDataType());
            //组装诊断名称
            String medDiagInfoStr = "";
            if (ValidateUtil.isNotEmpty(medPersionInfoVo.getDiag_names())) {
                diagNames = medPersionInfoVo.getDiag_names().split(delimiter);
                for (String diagName : diagNames) {
//                    MedDiagInfoVo medDiagInfoVo = new MedDiagInfoVo();
//                    medDiagInfoVo.setDiagCodg(diagCode);
//                    medDiagInfoVoList.add(medDiagInfoVo);
                    medDiagInfoStr += diagName + EXCT_OPERATOR;
                }
            }
            validMedDataInfo.setMedDiagInfoStr(medDiagInfoStr);

            //组装手术名称
            String medOprnInfoStr = "";
            if (ValidateUtil.isNotEmpty(medPersionInfoVo.getOprn_oprt_names())) {
                oprnOprtNames = medPersionInfoVo.getOprn_oprt_names().split(delimiter);
                for (String oprnName : oprnOprtNames) {
//                    MedOprnInfoVo medOprnInfo = new MedOprnInfoVo();
//                    medOprnInfo.setOprnCodg(oprnName);
//                    medOprnInfoList.add(medOprnInfo);
                    medOprnInfoStr += oprnName + EXCT_OPERATOR;
                }
            }
            validMedDataInfo.setMedOprnInfoStr(medOprnInfoStr);
            //组装费用list
            validMedDataInfo.setMedFeeItemInfos(medFeeItemInfoVoList);
            validMedDataInfoList.add(validMedDataInfo);
        }
        return validMedDataInfoList;
    }

    /**
     * 从redis中获取费用明细数据
     *
     * @param medPersionInfoVo
     * @return
     */
    private List<MedFeeItemInfoVo> generateFeeItemList(MedPersionInfoVo medPersionInfoVo) {
        List<MedFeeItemInfoVo> medFeeItemInfoVoList = new ArrayList<>();
        Map<Object, Object> redisMapResult = RedisUtils.hmget(medPersionInfoVo.getUniqueId());
        if (!ValidateUtil.isEmpty(redisMapResult)) {
            for (Object object :
                    redisMapResult.values()) {
                medFeeItemInfoVoList.add((MedFeeItemInfoVo) object);
            }
        }
        return medFeeItemInfoVoList;
    }

    /**
     * 根据预分组参数构建自查自纠参数（可能缺少明细，需要从明细中查询）
     *
     * @param dto
     * @return
     */
    private ValidMedDataInfo generateMainByPreGroup(PreGroupDto dto) {
        ValidMedDataInfo validMedDataInfo = new ValidMedDataInfo();
        validMedDataInfo.setUniqueId(dto.getK00());
        validMedDataInfo.setMedcasno(dto.getBah());
        // 组装诊断信息
        List<MedDiagInfoVo> medDiagInfoVoList = new ArrayList<>();
        String medDiagInfoStr = "";
        for (String diagName : dto.getZdmc()) {
//            MedDiagInfoVo medDiagInfoVo = new MedDiagInfoVo();
//            medDiagInfoVo.setDiagCodg(diagName);
//            medDiagInfoVoList.add(medDiagInfoVo);
            medDiagInfoStr += diagName + EXCT_OPERATOR;
        }
        validMedDataInfo.setMedDiagInfoVos(medDiagInfoVoList);
        validMedDataInfo.setMedDiagInfoStr(medDiagInfoStr);

        // 组装手术信息
        String medOprnInfoStr = "";
        List<MedOprnInfoVo> medOprnInfoList = new ArrayList<>();
        for (String oprnName : dto.getSsjczmc()) {
//            MedOprnInfoVo medOprnInfo = new MedOprnInfoVo();
//            medOprnInfo.setOprnCodg(oprnCode);
//            medOprnInfoList.add(medOprnInfo);
            medOprnInfoStr += oprnName + EXCT_OPERATOR;
        }
        validMedDataInfo.setMedOprnInfos(medOprnInfoList);
        validMedDataInfo.setMedOprnInfoStr(medOprnInfoStr);

        //读取对应k00的参数
        MedFeeItemQueryDto medFeeItemQueryDto = new MedFeeItemQueryDto();
        medFeeItemQueryDto.setUniqueId(dto.getK00());
        List<MedFeeItemInfoVo> medFeeItemInfos = ruleValidProcManagerMapper.queryMedFeeItem(medFeeItemQueryDto);
        validMedDataInfo.setMedFeeItemInfos(medFeeItemInfos);
        return validMedDataInfo;
    }

    public List<ValidationResultVo> runProcValid(List<ValidMedDataInfo> voList, Map<String, Object> params, HcsBatchRunProcTask task) {
        ResultEntity resultEntity = threadUtils.execute(voList, params, task);
        if (ResultEntity.FAIL == resultEntity.getCode()) {
            throw new AppException("线程执行错误异常！");
        }
        List<ValidationResultVo> resultList = resultEntity.getAllData();
        return resultList;
    }

    /**
     * 从Hcm9001Param转换为ValidMedDataInfo
     *
     * @param hcm9001Param
     * @return
     */
    private ValidMedDataInfo generateValidMedDataInfoByHcm9001(Hcm9001Param hcm9001Param) {
        if (hcm9001Param == null || hcm9001Param.getData() == null ||
            hcm9001Param.getData().getPatient_dtos() == null ||
            hcm9001Param.getData().getPatient_dtos().getFsi_encounter_dtos() == null) {
            return null;
        }

        Hcm9001Param.data data = hcm9001Param.getData();
        Hcm9001Param.Patient_dtos patientDtos = data.getPatient_dtos();
        Hcm9001Param.Fsi_encounter_dtos encounterDtos = patientDtos.getFsi_encounter_dtos();

        ValidMedDataInfo validMedDataInfo = new ValidMedDataInfo();

        // 设置基本信息
        validMedDataInfo.setUniqueId(patientDtos.getUnique_id());
        validMedDataInfo.setMedcasno(encounterDtos.getIpt_no()); // 使用住院号作为病案号

        // 根据医疗类别判断数据类型（1住院、2门诊）
        String medType = encounterDtos.getMed_type();
        if ("1".equals(medType) || "11".equals(medType)) {
            validMedDataInfo.setDataType("1"); // 住院
        } else {
            validMedDataInfo.setDataType("2"); // 门诊
        }

        // 设置数据年度（从出院时间获取年份）
        if (encounterDtos.getDscg_date() != null) {
            String year = DateUtil.datetimeToString(encounterDtos.getDscg_date(), "yyyy");
            validMedDataInfo.setYm(year);
        }

        // 处理诊断信息
        String medDiagInfoStr = "";
        List<MedDiagInfoVo> medDiagInfoVoList = new ArrayList<>();

        // 主诊断
        if (ValidateUtil.isNotEmpty(encounterDtos.getDscg_main_dise_name())) {
            MedDiagInfoVo mainDiag = new MedDiagInfoVo();
            mainDiag.setDiagCodg(encounterDtos.getDscg_main_dise_codg());
            mainDiag.setDiagName(encounterDtos.getDscg_main_dise_name());
            medDiagInfoVoList.add(mainDiag);
            medDiagInfoStr += encounterDtos.getDscg_main_dise_name() + EXCT_OPERATOR;
        }

        // 其他诊断（从fsi_diagnose_dtos获取）
        if (encounterDtos.getFsi_diagnose_dtos() != null) {
            List<Hcm9001Param.Fsi_diagnose_dtos> diagnoseDtos = encounterDtos.getFsi_diagnose_dtos();
            for (Hcm9001Param.Fsi_diagnose_dtos diagnoseDto : diagnoseDtos) {
                if (ValidateUtil.isNotEmpty(diagnoseDto.getDise_name())) {
                    MedDiagInfoVo otherDiag = new MedDiagInfoVo();
                    otherDiag.setDiagCodg(diagnoseDto.getDise_codg());
                    otherDiag.setDiagName(diagnoseDto.getDise_name());
                    medDiagInfoVoList.add(otherDiag);
                }
            }
        }

        validMedDataInfo.setMedDiagInfoVos(medDiagInfoVoList);
        validMedDataInfo.setMedDiagInfoStr(medDiagInfoStr);

        // 处理手术信息
        String medOprnInfoStr = "";
        List<MedOprnInfoVo> medOprnInfoList = new ArrayList<>();

        if (encounterDtos.getFsi_operation_dtos() != null) {
            Hcm9001Param.Fsi_operation_dtos operationDtos = encounterDtos.getFsi_operation_dtos();
            if (ValidateUtil.isNotEmpty(operationDtos.getOprn_name())) {
                MedOprnInfoVo oprnInfo = new MedOprnInfoVo();
                oprnInfo.setOprnCodg(operationDtos.getOprn_code());
                oprnInfo.setOprnName(operationDtos.getOprn_name());
                medOprnInfoList.add(oprnInfo);
                medOprnInfoStr += operationDtos.getOprn_name() + EXCT_OPERATOR;
            }
        }

        validMedDataInfo.setMedOprnInfos(medOprnInfoList);
        validMedDataInfo.setMedOprnInfoStr(medOprnInfoStr);

        // 处理费用明细信息
        List<MedFeeItemInfoVo> medFeeItemInfos = new ArrayList<>();

        if (encounterDtos.getFsi_order_dtos() != null) {
            Hcm9001Param.Fsi_order_dtos orderDtos = encounterDtos.getFsi_order_dtos();

            MedFeeItemInfoVo feeItem = new MedFeeItemInfoVo();
            feeItem.setUniqueId(patientDtos.getUnique_id());
            feeItem.setMedListCodg(orderDtos.getHilist_code());

            // 设置费用发生时间
            if (orderDtos.getFee_ocur_time() != null) {
                String feeOcurDayTime = DateUtil.datetimeToString(orderDtos.getFee_ocur_time(), "yyyy-MM-dd");
                String feeOcurTime = DateUtil.datetimeToString(orderDtos.getFee_ocur_time(), "yyyy-MM-dd-HH");
                feeItem.setFeeOcurDayTime(feeOcurDayTime);
                feeItem.setFeeOcurTime(feeOcurTime);
                feeItem.setFeeItemUniqueid(orderDtos.getHilist_code() + "-" + feeOcurTime);
            }

            feeItem.setCountSize(orderDtos.getCnt());
            feeItem.setItemFeeSumamt(orderDtos.getSumamt());

            medFeeItemInfos.add(feeItem);
        }

        validMedDataInfo.setMedFeeItemInfos(medFeeItemInfos);

        return validMedDataInfo;
    }


}
