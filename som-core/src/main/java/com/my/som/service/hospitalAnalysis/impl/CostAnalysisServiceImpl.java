package com.my.som.service.hospitalAnalysis.impl;

import com.github.pagehelper.PageHelper;
import com.my.som.common.constant.DrgConst;
import com.my.som.common.util.ValidateUtil;
import com.my.som.dao.hospitalAnalysis.CostAnalysisDao;
import com.my.som.dto.hospitalAnalysis.HospitalAnalysisQueryParam;
import com.my.som.service.hospitalAnalysis.CostAnalysisService;
import com.my.som.vo.common.CommonObject;
import com.my.som.vo.firstPage.FirstPageMedicalTreatmentCostVo;
import com.my.som.vo.firstPage.MedicalTreatmentCostVo;
import com.my.som.vo.firstPage.SettleListMedicalCostCountVo;
import com.my.som.vo.hospitalAnalysis.CostAnalysisInfo;
import com.my.som.vo.hospitalAnalysis.PsnSelfpayVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.*;

/**
 * Service实现类
 * Created by sky on 2020/1/5.
 */
@Service
public class CostAnalysisServiceImpl implements CostAnalysisService {
    @Autowired
    private CostAnalysisDao costAnalysisDao;


//    @Override
//    public List<Map<String,Object>> list(HospitalAnalysisQueryParam queryParam, Integer pageSize, Integer pageNum) {
//        PageHelper.startPage(pageNum, pageSize);
//        List<Map<String,Object>> result = costAnalysisDao.list(queryParam);
//
//        //处理动态列表数据
//        for(Map<String,Object> res:result){
//            List<Map<String,Object>> medicalCostList = new ArrayList<>();
//            if("1".equals(queryParam.getQueryType())){  //按照科室查询时
//                queryParam.setTb16c(String.valueOf(res.get("priOutHosDeptCode")));  //获取科室
//                medicalCostList = costAnalysisDao.getMedicalCostList(queryParam);//查询当前行的结算医疗费用项
//            }else{
//                queryParam.settQueryDrg(String.valueOf(res.get("drgCodg")));  //获取Drg
//                medicalCostList = costAnalysisDao.getMedicalCostList(queryParam);//查询当前行的结算医疗费用项
//            }
//            for(int i=0;i<medicalCostList.size();i++){
//                res.put("medicalCostCol"+i,medicalCostList.get(i).get("cost"));
//            }
//            List<Map<String,Object>> fundPayList = new ArrayList<>();
//            if("1".equals(queryParam.getQueryType())){  //按照科室查询时
//                queryParam.setTb16c(String.valueOf(res.get("priOutHosDeptCode")));  //获取科室
//                fundPayList = costAnalysisDao.getFundPayList(queryParam);//查询当前行的结算基金支付项
//            }else{
//                queryParam.settQueryDrg(String.valueOf(res.get("drgCodg")));  //获取Drg
//                fundPayList = costAnalysisDao.getFundPayList(queryParam);//查询当前行的结算基金支付项
//            }
//            for(int i=0;i<fundPayList.size();i++){
//                res.put("fundPayCol"+i,fundPayList.get(i).get("cost"));
//            }
//        }
//        return result;
//    }
    @Override
    public List<Map<String, Object>> list(HospitalAnalysisQueryParam queryParam, Integer pageSize, Integer pageNum) {
        PageHelper.startPage(pageNum, pageSize);
        List<Map<String, Object>> result = costAnalysisDao.list(queryParam);

        // 创建线程池
        int corePoolSize = Runtime.getRuntime().availableProcessors();
        int maximumPoolSize = Runtime.getRuntime().availableProcessors() * 2;
        long keepAliveTime = 60;
        TimeUnit unit = TimeUnit.SECONDS;
        BlockingQueue<Runnable> workQueue = new ArrayBlockingQueue<>(100);
        ThreadFactory threadFactory = Executors.defaultThreadFactory();
        RejectedExecutionHandler handler = new ThreadPoolExecutor.CallerRunsPolicy();

        ExecutorService executorService = new ThreadPoolExecutor(
                corePoolSize,
                maximumPoolSize,
                keepAliveTime,
                unit,
                workQueue,
                threadFactory,
                handler
        );
        //List<Future<?>> futures = new ArrayList<>();

        for (Map<String, Object> res : result) {
            executorService.submit(() -> {
                List<Map<String, Object>> medicalCostList = new ArrayList<>();
                if ("1".equals(queryParam.getQueryType())) {
                    queryParam.setTb16c(String.valueOf(res.get("priOutHosDeptCode")));
                    medicalCostList = costAnalysisDao.getMedicalCostList(queryParam);
                } else {
                    queryParam.settQueryDrg(String.valueOf(res.get("drgCodg")));
                    //十四项费用
                    medicalCostList = costAnalysisDao.getMedicalCostList(queryParam);
                }
                for (int i = 0; i < medicalCostList.size(); i++) {
                    res.put("medicalCostCol" + i, medicalCostList.get(i).get("cost"));
                }

                List<Map<String, Object>> fundPayList = new ArrayList<>();
                if ("1".equals(queryParam.getQueryType())) {
                    queryParam.setTb16c(String.valueOf(res.get("priOutHosDeptCode")));
                    fundPayList = costAnalysisDao.getFundPayList(queryParam);
                } else {
                    queryParam.settQueryDrg(String.valueOf(res.get("drgCodg")));
                    fundPayList = costAnalysisDao.getFundPayList(queryParam);
                }
                for (int i = 0; i < fundPayList.size(); i++) {
                    res.put("fundPayCol" + i, fundPayList.get(i).get("cost"));
                }
            });
        }

        // 等待所有任务完成
//        for (Future<?> future : futures) {
//            try {
//                future.get();
//            } catch (InterruptedException | ExecutionException e) {
//                e.printStackTrace();
//            }
//        }
        // 关闭线程池
        executorService.shutdown();

        return result;
    }


//    @Override
//    public List<Map<String, Object>> list(HospitalAnalysisQueryParam queryParam, Integer pageSize, Integer pageNum) {
//        PageHelper.startPage(pageNum, pageSize);
//        List<Map<String, Object>> result = costAnalysisDao.list(queryParam);
//
//        // 提取所有科室代码和 DRG 代码
//        List<String> tb16cList = new ArrayList<>();
//        List<String> drgCodgList = new ArrayList<>();
//        for (Map<String, Object> res : result) {
//            if ("1".equals(queryParam.getQueryType())) {
//                tb16cList.add(String.valueOf(res.get("priOutHosDeptCode")));
//            } else {
//                drgCodgList.add(String.valueOf(res.get("drgCodg")));
//            }
//        }
//
//        // 批量查询医疗费用项和基金支付项
//        Map<String, List<Map<String, Object>>> medicalCostMap = new HashMap<>();
//        Map<String, List<Map<String, Object>>> fundPayMap = new HashMap<>();
//        if ("1".equals(queryParam.getQueryType())) {
//            if (!tb16cList.isEmpty()) {
//                queryParam.setTb16cList(tb16cList);
//                List<Map<String, Object>> medicalCostList = costAnalysisDao.getMedicalCostList(queryParam);
//                for (Map<String, Object> item : medicalCostList) {
//                    String tb16c = String.valueOf(item.get("tb16c"));
//                    medicalCostMap.computeIfAbsent(tb16c, k -> new ArrayList<>()).add(item);
//                }
//                List<Map<String, Object>> fundPayList = costAnalysisDao.getFundPayList(queryParam);
//                for (Map<String, Object> item : fundPayList) {
//                    String tb16c = String.valueOf(item.get("tb16c"));
//                    fundPayMap.computeIfAbsent(tb16c, k -> new ArrayList<>()).add(item);
//                }
//            }
//        } else {
//            if (!drgCodgList.isEmpty()) {
//                queryParam.setDrgCodgList(drgCodgList);
//                List<Map<String, Object>> medicalCostList = costAnalysisDao.getMedicalCostList(queryParam);
//                for (Map<String, Object> item : medicalCostList) {
//                    String drgCodg = String.valueOf(item.get("drgCodg"));
//                    medicalCostMap.computeIfAbsent(drgCodg, k -> new ArrayList<>()).add(item);
//                }
//                List<Map<String, Object>> fundPayList = costAnalysisDao.getFundPayList(queryParam);
//                for (Map<String, Object> item : fundPayList) {
//                    String drgCodg = String.valueOf(item.get("drgCodg"));
//                    fundPayMap.computeIfAbsent(drgCodg, k -> new ArrayList<>()).add(item);
//                }
//            }
//        }
//        // 处理动态列表数据
//        for (Map<String, Object> res : result) {
//            String key;
//            if ("1".equals(queryParam.getQueryType())) {
//                key = String.valueOf(res.get("priOutHosDeptCode"));
//            } else {
//                key = String.valueOf(res.get("drgCodg"));
//                System.out.println(key);
//            }
//            key = "null";
////            List<Map<String, Object>> medicalCostList = medicalCostMap.getOrDefault(key, new ArrayList<>());
//            List<Map<String, Object>> medicalCostList = medicalCostMap.get(key);
////            System.out.println(medicalCostList1);
////            List<Map<String, Object>> medicalCostList = medicalCostMap.get(key);
//            for (int i = 0; i < medicalCostList.size(); i++) {
//                res.put("medicalCostCol" + i, medicalCostList.get(i).get("cost"));
//            }
//
//            List<Map<String, Object>> fundPayList = fundPayMap.getOrDefault(key, new ArrayList<>());
//            for (int i = 0; i < fundPayList.size(); i++) {
//                res.put("fundPayCol" + i, fundPayList.get(i).get("cost"));
//            }
//        }
//
//        return result;
//    }
//



    @Override
    public List<CommonObject> getCountInfo(HospitalAnalysisQueryParam queryParam) {
        List<CommonObject> list = new ArrayList<>();
        if(DrgConst.DATA_TYPE_1.equals(queryParam.getDataType())){//结算清单的费用需要动态获取
            //医保结算清单基本医疗费用
            List<SettleListMedicalCostCountVo> settleListMedicalCostCountList = costAnalysisDao.getSettleListMedicalCostList(queryParam);
            BigDecimal sumfee = new BigDecimal("0");
            if(!ValidateUtil.isEmpty(settleListMedicalCostCountList)){
                for(SettleListMedicalCostCountVo slmccl:settleListMedicalCostCountList){
                    CommonObject objectx = new CommonObject();
                    objectx.setName(slmccl.getMedChrgItemname());
                    objectx.setValue(slmccl.getItemTotalCost());
                    list.add(objectx);
                    sumfee = sumfee.add(new BigDecimal(slmccl.getItemTotalCost()));
                }
                list.sort((o1, o2) -> new BigDecimal(o2.getValue()).compareTo(new BigDecimal(o1.getValue()))); //费用降序排列，最后加入总费用
            }
            //基金支付部分1：固定值，个人支付部分的值
            List<PsnSelfpayVo> personalPayVoList = costAnalysisDao.getPersonalPayList(queryParam);
            if(!ValidateUtil.isEmpty(personalPayVoList)){
//                CommonObject object1 = new CommonObject();
//                object1.setName("个人自付");
//                object1.setValue(String.valueOf(personalPayVoList.get(0).getPsnSelfpay()));
//                list.add(object1);
//                CommonObject object2 = new CommonObject();
//                object2.setName("个人自费");
//                object2.setValue(String.valueOf(personalPayVoList.get(0).getPsnOwnpay()));
//                list.add(object2);
                CommonObject object3 = new CommonObject();
                object3.setName("个人账户支付");
                object3.setValue(String.valueOf(personalPayVoList.get(0).getAcctPay()));
                list.add(object3);
                CommonObject object4 = new CommonObject();
                object4.setName("个人现金支付");
                object4.setValue(String.valueOf(personalPayVoList.get(0).getPsnCashpay()));
                list.add(object4);
            }
            //基金支付部分2：医保基金支付费用
            List<SettleListMedicalCostCountVo> settleListFundPayCountList = costAnalysisDao.getSettleListFundPayList(queryParam);
            if(!ValidateUtil.isEmpty(settleListFundPayCountList)){
                for(SettleListMedicalCostCountVo slfpcl:settleListFundPayCountList){
                    CommonObject objecty = new CommonObject();
                    objecty.setName(slfpcl.getMedChrgItemname());
                    objecty.setValue(slfpcl.getItemTotalCost());
                    list.add(objecty);
                }
            }
            //最后再插入基本医疗费用的总金额
            CommonObject object5 = new CommonObject();
            object5.setName("总费用");
            object5.setValue(String.valueOf(sumfee));
            list.add(object5);
        }else if(DrgConst.DATA_TYPE_2.equals(queryParam.getDataType())){
            MedicalTreatmentCostVo medicalTreatmentCostVo = costAnalysisDao.getInHosMedicalCostList(queryParam); //获取病案首页费用信息
            if(!ValidateUtil.isEmpty(medicalTreatmentCostVo)){
//                CommonObject object0 = new CommonObject();
//                object0.setName("自付费");
//                object0.setValue(String.valueOf(medicalTreatmentCostVo.getIptSumfeeInSelfpayAmt()));
//                list.add(object0);
                CommonObject object1 = new CommonObject();
                object1.setName("服务费");
                object1.setValue(String.valueOf(medicalTreatmentCostVo.getComMedServfee()));
                list.add(object1);
                CommonObject object2 = new CommonObject();
                object2.setName("康复费");
                object2.setValue(String.valueOf(medicalTreatmentCostVo.getRhabFee()));
                list.add(object2);
                CommonObject object3 = new CommonObject();
                object3.setName("诊断费");
                object3.setValue(String.valueOf(medicalTreatmentCostVo.getDiagFee()));
                list.add(object3);
                CommonObject object4 = new CommonObject();
                object4.setName("治疗费");
                object4.setValue(String.valueOf(medicalTreatmentCostVo.getTreatFee()));
                list.add(object4);
                CommonObject object5 = new CommonObject();
                object5.setName("药品费");
                object5.setValue(String.valueOf(medicalTreatmentCostVo.getDrugfee()));
                list.add(object5);
                CommonObject object6 = new CommonObject();
                object6.setName("血液血制品费");
                object6.setValue(String.valueOf(medicalTreatmentCostVo.getBloodBloPro()));
                list.add(object6);
                CommonObject object7 = new CommonObject();
                object7.setName("耗材费");
                object7.setValue(String.valueOf(medicalTreatmentCostVo.getMcsFee()));
                list.add(object7);
                CommonObject object8 = new CommonObject();
                object8.setName("其他费");
                object8.setValue(String.valueOf(medicalTreatmentCostVo.getOthFee()));
                list.add(object8);
                CommonObject object9 = new CommonObject();
                object9.setName("抗生素费");
                object9.setValue(String.valueOf(medicalTreatmentCostVo.getAbtFee()));
                list.add(object9);
                CommonObject object10 = new CommonObject();
                object10.setName("检验费");
                object10.setValue(String.valueOf(medicalTreatmentCostVo.getInspectFee()));
                list.add(object10);
                if(DrgConst.CURRENT_MEDICAL_TYPE.equals("2")){//中医首页
                    CommonObject object11 = new CommonObject();
                    object11.setName("中医其他费");
                    object11.setValue(String.valueOf(medicalTreatmentCostVo.getChineseOtherCost()));
                    list.add(object11);
                }
                list.sort((o1, o2) -> new BigDecimal(o2.getValue()).compareTo(new BigDecimal(o1.getValue()))); //费用降序排列，最后加入总费用
                //基金支付部分1：固定值，个人支付部分的值
                List<PsnSelfpayVo> personalPayVoList = costAnalysisDao.getPersonalPayList(queryParam);
                if(!ValidateUtil.isEmpty(personalPayVoList)){
                    CommonObject object12 = new CommonObject();
                    object12.setName("个人自付");
                    object12.setValue(String.valueOf(personalPayVoList.get(0).getPsnSelfpay()));
                    list.add(object12);
                    CommonObject object13 = new CommonObject();
                    object13.setName("个人自费");
                    object13.setValue(String.valueOf(personalPayVoList.get(0).getPsnOwnpay()));
                    list.add(object13);
                    CommonObject object14 = new CommonObject();
                    object14.setName("个人账户支付");
                    object14.setValue(String.valueOf(personalPayVoList.get(0).getAcctPay()));
                    list.add(object14);
                    CommonObject object15 = new CommonObject();
                    object15.setName("个人现金支付");
                    object15.setValue(String.valueOf(personalPayVoList.get(0).getPsnCashpay()));
                    list.add(object15);
                }
                //基金支付部分2：医保基金支付费用
                List<SettleListMedicalCostCountVo> settleListFundPayCountList = costAnalysisDao.getSettleListFundPayList(queryParam);
                if(!ValidateUtil.isEmpty(settleListFundPayCountList)){
                    for(SettleListMedicalCostCountVo slfpcl:settleListFundPayCountList){
                        CommonObject objecty = new CommonObject();
                        objecty.setName(slfpcl.getMedChrgItemname());
                        objecty.setValue(slfpcl.getItemTotalCost());
                        list.add(objecty);
                    }
                }
                CommonObject objectEnd = new CommonObject();
                objectEnd.setName("住院总费用");
                objectEnd.setValue(String.valueOf(medicalTreatmentCostVo.getIptSumfee()));
                list.add(objectEnd);
            }
        }
        return list;
    }
}
