package com.my.som.service.drgReasonableCost;

import com.my.som.dto.drgReasonableCost.DrgReasonableCostQueryParam;
import com.my.som.vo.drgReasonableCost.BedCostDayVo;

import java.util.List;

/**
 * Service
 * Created by sky on 2020/3/17.
 */
public interface BedDayCostService {
    /**
     * 查询床日付费病组主要信息
     * @param queryParam
     * @return
     */
    List<BedCostDayVo> list(DrgReasonableCostQueryParam queryParam, Integer pageSize, Integer pageNum);

}
