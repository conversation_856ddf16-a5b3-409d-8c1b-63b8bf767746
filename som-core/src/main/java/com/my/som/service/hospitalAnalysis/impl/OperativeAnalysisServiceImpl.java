package com.my.som.service.hospitalAnalysis.impl;

import com.github.pagehelper.PageHelper;
import com.my.som.common.constant.DrgConst;
import com.my.som.common.util.DateUtil;
import com.my.som.common.util.ValidateUtil;
import com.my.som.dao.hospitalAnalysis.OperativeAnalysisDao;
import com.my.som.dto.hospitalAnalysis.HospitalAnalysisQueryParam;
import com.my.som.service.hospitalAnalysis.OperativeAnalysisService;
import com.my.som.vo.hospitalAnalysis.OperativeAnalysisCountInfo;
import com.my.som.vo.hospitalAnalysis.OperativeAnalysisInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Service实现类
 * Created by sky on 2020/3/19.
 */
@Service
public class OperativeAnalysisServiceImpl implements OperativeAnalysisService {
    @Autowired
    private OperativeAnalysisDao operativeAnalysisDao;

    private final String ADM_TIME = "ADM_TIME";
    private final String DSCG_TIME = "DSCG_TIME";
    private final String SETL_END_TIME = "SETL_END_TIME";

    @Override
    public List<OperativeAnalysisInfo> list(HospitalAnalysisQueryParam queryParam, Integer pageSize, Integer pageNum) {
        PageHelper.startPage(pageNum, pageSize);
        queryParam.setEnabFlag(DrgConst.START_FLAG_1);
        queryParam.setActiveFlag(DrgConst.ACTIVE_FLAG_1);
        return operativeAnalysisDao.list(queryParam);
    }
    @Override
    public List<OperativeAnalysisCountInfo> getCountInfo(HospitalAnalysisQueryParam queryParam) {

        List<OperativeAnalysisCountInfo> result = new ArrayList<>();
        //本期时间不为空，计算同期时间、上期时间
        if(ValidateUtil.isNotEmpty(queryParam.getCy_start_date())&&ValidateUtil.isNotEmpty(queryParam.getCy_end_date())){
            queryParam.setLastMonth_cy_start_date(DateUtil.getLastMonthIssue(queryParam.getCy_start_date(),"yyyy-MM-dd"));
            queryParam.setLastMonth_cy_end_date(DateUtil.getLastMonthIssue(queryParam.getCy_end_date(),"yyyy-MM-dd"));
            queryParam.setLastYear_cy_start_date(DateUtil.getLastYearIssue(queryParam.getCy_start_date(),"yyyy-MM-dd"));
            queryParam.setLastYear_cy_end_date(DateUtil.getLastYearIssue(queryParam.getCy_end_date(),"yyyy-MM-dd"));
            queryParam.setBusKeyField(DSCG_TIME);
        } else if (ValidateUtil.isNotEmpty(queryParam.getInStartTime())&&ValidateUtil.isNotEmpty(queryParam.getInEndTime())) {
            queryParam.setLastMonth_cy_start_date(DateUtil.getLastMonthIssue(queryParam.getInStartTime(),"yyyy-MM-dd"));
            queryParam.setLastMonth_cy_end_date(DateUtil.getLastMonthIssue(queryParam.getInEndTime(),"yyyy-MM-dd"));
            queryParam.setLastYear_cy_start_date(DateUtil.getLastYearIssue(queryParam.getInStartTime(),"yyyy-MM-dd"));
            queryParam.setLastYear_cy_end_date(DateUtil.getLastYearIssue(queryParam.getInEndTime(),"yyyy-MM-dd"));
            queryParam.setCy_start_date(queryParam.getInStartTime());
            queryParam.setCy_end_date(queryParam.getInEndTime());
            queryParam.setBusKeyField(ADM_TIME);
        } else if(ValidateUtil.isNotEmpty(queryParam.getSeStartTime())&&ValidateUtil.isNotEmpty(queryParam.getSeEndTime())) {
            queryParam.setLastMonth_cy_start_date(DateUtil.getLastMonthIssue(queryParam.getSeStartTime(),"yyyy-MM-dd"));
            queryParam.setLastMonth_cy_end_date(DateUtil.getLastMonthIssue(queryParam.getSeEndTime(),"yyyy-MM-dd"));
            queryParam.setLastYear_cy_start_date(DateUtil.getLastYearIssue(queryParam.getSeStartTime(),"yyyy-MM-dd"));
            queryParam.setLastYear_cy_end_date(DateUtil.getLastYearIssue(queryParam.getSeEndTime(),"yyyy-MM-dd"));
            queryParam.setCy_start_date(queryParam.getSeStartTime());
            queryParam.setCy_end_date(queryParam.getSeEndTime());
            queryParam.setBusKeyField(SETL_END_TIME);
        }
        queryParam.setEnabFlag(DrgConst.START_FLAG_1);
        queryParam.setActiveFlag(DrgConst.ACTIVE_FLAG_1);
        Map<String,String>  map = operativeAnalysisDao.getCountInfo(queryParam);
        if(!ValidateUtil.isEmpty(map)){
            OperativeAnalysisCountInfo oaci1 = new OperativeAnalysisCountInfo();
            oaci1.setName("一级手术");
            oaci1.setIssueNum(String.valueOf(map.get("oneLvlOprHosIssueNum")));
            oaci1.setLastMonthNum(String.valueOf(map.get("oneLvlOprHosLastMonthNum")));
            oaci1.setLastYearNum(String.valueOf(map.get("oneLvlOprHosLastYearNum")));
            oaci1.setStandardNum(String.valueOf(map.get("oneLvlOprStandradNum")));
            result.add(oaci1);
            OperativeAnalysisCountInfo oaci2 = new OperativeAnalysisCountInfo();
            oaci2.setName("二级手术");
            oaci2.setIssueNum(String.valueOf(map.get("twoLvlOprHosIssueNum")));
            oaci2.setLastMonthNum(String.valueOf(map.get("twoLvlOprHosLastMonthNum")));
            oaci2.setLastYearNum(String.valueOf(map.get("twoLvlOprHosLastYearNum")));
            oaci2.setStandardNum(String.valueOf(map.get("twoLvlOprStandradNum")));
            result.add(oaci2);
            OperativeAnalysisCountInfo oaci3 = new OperativeAnalysisCountInfo();
            oaci3.setName("三级手术");
            oaci3.setIssueNum(String.valueOf(map.get("threeLvlOprHosIssueNum")));
            oaci3.setLastMonthNum(String.valueOf(map.get("threeLvlOprHosLastMonthNum")));
            oaci3.setLastYearNum(String.valueOf(map.get("threeLvlOprHosLastYearNum")));
            oaci3.setStandardNum(String.valueOf(map.get("threeLvlOprStandradNum")));
            result.add(oaci3);
            OperativeAnalysisCountInfo oaci4 = new OperativeAnalysisCountInfo();
            oaci4.setName("四级手术");
            oaci4.setIssueNum(String.valueOf(map.get("fourLvlOprHosIssueNum")));
            oaci4.setLastMonthNum(String.valueOf(map.get("fourLvlOprHosLastMonthNum")));
            oaci4.setLastYearNum(String.valueOf(map.get("fourLvlOprHosLastYearNum")));
            oaci4.setStandardNum(String.valueOf(map.get("fourLvlOprStandradNum")));
            result.add(oaci4);
            OperativeAnalysisCountInfo oaciOther = new OperativeAnalysisCountInfo();
            oaciOther.setName("其他手术");
            oaciOther.setIssueNum(String.valueOf(map.get("otherLvlOprHosIssueNum")));
            oaciOther.setLastMonthNum(String.valueOf(map.get("otherLvlOprHosLastMonthNum")));
            oaciOther.setLastYearNum(String.valueOf(map.get("otherLvlOprHosLastYearNum")));
            oaciOther.setStandardNum(String.valueOf(map.get("otherLvlOprStandradNum")));
            result.add(oaciOther);
        }
        return result;
    }
}
