package com.my.som.service.hospitalAnalysis.impl;

import com.github.pagehelper.PageHelper;
import com.my.som.common.constant.DrgConst;
import com.my.som.common.util.DateUtil;
import com.my.som.common.util.DictOperationUtil;
import com.my.som.common.util.ValidateUtil;
import com.my.som.common.vo.DictVo;
import com.my.som.dao.hospitalAnalysis.InGroupAnalysisDao;
import com.my.som.dto.hospitalAnalysis.HospitalAnalysisQueryParam;
import com.my.som.service.hospitalAnalysis.InGroupAnalysisService;
import com.my.som.vo.dipBusiness.DipGroupErrorMsgVo;
import com.my.som.vo.dipBusiness.DipInGroupAnalysisCountVo;
import com.my.som.vo.hospitalAnalysis.InGroupTopCountInfo;
import com.my.som.vo.hospitalAnalysis.InGroupAnalysisCountInfo;
import com.my.som.vo.hospitalAnalysis.InGroupAnalysisInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Queue;

/**
 * Service实现类
 * Created by sky on 2020/1/5.
 */
@Service
public class InGroupAnalysisServiceImpl implements InGroupAnalysisService {
    @Autowired
    private InGroupAnalysisDao inGroupAnalysisDao;

    @Override
    public List<InGroupAnalysisInfo> list(HospitalAnalysisQueryParam queryParam, Integer pageSize, Integer pageNum) {
        PageHelper.startPage(pageNum, pageSize);
        return inGroupAnalysisDao.list(queryParam);
    }

    @Override
    public InGroupTopCountInfo getTopCountInfo(HospitalAnalysisQueryParam queryParam) {
        final String ADM_TIME = "ADM_TIME";
        final String DSCG_TIME = "DSCG_TIME";
        final String SETL_END_TIME = "SETL_END_TIME";
        //本期时间不为空，计算同期时间、上期时间
        if(ValidateUtil.isNotEmpty(queryParam.getCy_start_date())&&ValidateUtil.isNotEmpty(queryParam.getCy_end_date())){
            setTime(queryParam, queryParam.getCy_start_date(), queryParam.getCy_end_date());
            queryParam.setBusKeyField(DSCG_TIME);
        } else if(ValidateUtil.isNotEmpty(queryParam.getInStartTime())&&ValidateUtil.isNotEmpty(queryParam.getInEndTime())){
            setTime(queryParam, queryParam.getInStartTime(), queryParam.getInEndTime());
            queryParam.setCy_start_date(queryParam.getInStartTime());
            queryParam.setCy_end_date(queryParam.getInEndTime());
            queryParam.setBusKeyField(ADM_TIME);
        }else if(ValidateUtil.isNotEmpty(queryParam.getSeStartTime())&&ValidateUtil.isNotEmpty(queryParam.getSeEndTime())){
            setTime(queryParam, queryParam.getSeStartTime(), queryParam.getSeEndTime());
            queryParam.setCy_start_date(queryParam.getSeStartTime());
            queryParam.setCy_end_date(queryParam.getSeEndTime());
            queryParam.setBusKeyField(SETL_END_TIME);
        }
        return inGroupAnalysisDao.getTopCountInfo(queryParam);
    }

    private static void setTime(HospitalAnalysisQueryParam queryParam, String queryParam1, String queryParam2) {
        queryParam.setLastMonth_cy_start_date(DateUtil.getLastMonthIssue(queryParam1,"yyyy-MM-dd"));
        queryParam.setLastMonth_cy_end_date(DateUtil.getLastMonthIssue(queryParam2,"yyyy-MM-dd"));
        queryParam.setLastYear_cy_start_date(DateUtil.getLastYearIssue(queryParam1,"yyyy-MM-dd"));
        queryParam.setLastYear_cy_end_date(DateUtil.getLastYearIssue(queryParam2,"yyyy-MM-dd"));
    }

    @Override
    public List<InGroupAnalysisCountInfo> getNoGroupResonCountInfo(HospitalAnalysisQueryParam dto) {
//        return inGroupAnalysisDao.getNoGroupResonCountInfo(queryParam);
        List<InGroupAnalysisCountInfo> InGroupAnalysisCountInfos = new ArrayList<>();
        List<DipGroupErrorMsgVo> noGroupResonCountInfo = inGroupAnalysisDao.getNoGroupResonCountInfo(dto);
        // 获取字典数据  GROUP_ERROR_MSG
        String code_types = "GROUP_ERROR_MSG_DRG";
        Map<String, List<DictVo>> group_error_msg = DictOperationUtil.getDictList(code_types, DrgConst.TYPE_1);
        List<DictVo> dictVos = group_error_msg.get(code_types);

        for (DictVo dictVo :
                dictVos) {
            String label = dictVo.getLabel();
            InGroupAnalysisCountInfo vo = new InGroupAnalysisCountInfo();
            int cnt = 0;
            for (DipGroupErrorMsgVo dipGroupErrorMsgVo : noGroupResonCountInfo) {
                if(dipGroupErrorMsgVo.getNotInGroupReason().contains(label)) {
                    vo.setNotInGroupName("未入组原因");
                    vo.setNotInGroupReason(label);
                    cnt++;
                }
            }
            if(cnt > 0) {
                vo.setMedcasVal(String.valueOf(cnt));
                if (ValidateUtil.isNotEmpty(noGroupResonCountInfo)) {
                    setRate(cnt,noGroupResonCountInfo.get(0),vo);
                }
                InGroupAnalysisCountInfos.add(vo);
            }
        }
        return InGroupAnalysisCountInfos;
    }
    /**
     * 设置比率
     * @param cnt
     * @param dipGroupErrorMsgVo
     * @param dipInGroupAnalysisCountVo
     * @return
     */
    private InGroupAnalysisCountInfo setRate(int cnt, DipGroupErrorMsgVo dipGroupErrorMsgVo, InGroupAnalysisCountInfo dipInGroupAnalysisCountVo){
        // 设置未入组占比
        dipInGroupAnalysisCountVo.setNotInGroupRate(
                String.valueOf(new BigDecimal(String.valueOf(cnt))
                        .divide(new BigDecimal(dipGroupErrorMsgVo.getMedcasVal()), 8, BigDecimal.ROUND_HALF_UP)
                        .multiply(new BigDecimal("100")).setScale(2, BigDecimal.ROUND_HALF_UP))
        );
        // 设置总体占比
        dipInGroupAnalysisCountVo.setAllRate(
                String.valueOf(new BigDecimal(String.valueOf(cnt))
                        .divide(new BigDecimal(dipGroupErrorMsgVo.getTotalNum()), 8, BigDecimal.ROUND_HALF_UP)
                        .multiply(new BigDecimal("100")).setScale(2, BigDecimal.ROUND_HALF_UP))
        );
        return dipInGroupAnalysisCountVo;
    }

}
