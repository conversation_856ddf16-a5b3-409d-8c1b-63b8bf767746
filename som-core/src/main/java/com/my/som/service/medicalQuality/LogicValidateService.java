package com.my.som.service.medicalQuality;

import com.my.som.dto.medicalQuality.CompleteValidateCountInfo;
import com.my.som.dto.medicalQuality.LogicValidateCountInfo;
import com.my.som.dto.medicalQuality.SettleListMainInfoQueryParam;
import com.my.som.dto.medicalQuality.ValidateMainInfo;

import java.util.List;

/**
 * 病例逻辑性校验Service
 * Created by sky on 2020/3/2.
 */
public interface LogicValidateService {

    /**
     * 分页查询病例逻辑性结果
     * @param queryParam
     * @param pageSize
     * @param pageNum
     * @return
     */
    List<ValidateMainInfo> list(SettleListMainInfoQueryParam queryParam, Integer pageSize, Integer pageNum);


    /**
     * 查询病例逻辑性校对统计信息
     * @param queryParam
     * @return
     */
    LogicValidateCountInfo getCountInfo(SettleListMainInfoQueryParam queryParam);
}
