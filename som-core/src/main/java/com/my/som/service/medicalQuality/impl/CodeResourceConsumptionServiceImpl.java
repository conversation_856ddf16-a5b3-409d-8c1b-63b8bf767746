package com.my.som.service.medicalQuality.impl;

import com.github.pagehelper.PageHelper;
import com.my.som.common.util.DateUtil;
import com.my.som.common.util.ValidateUtil;
import com.my.som.dao.medicalQuality.CodeResourceConsumptionDao;
import com.my.som.dto.medicalQuality.*;
import com.my.som.mapper.common.SomDipStandardMapper;
import com.my.som.mapper.dataHandle.SomOprnOprtInfoMapper;
import com.my.som.model.common.SomDipStandard;
import com.my.som.model.common.SomDipStandardExample;
import com.my.som.model.dataHandle.SomDrgStandard;
import com.my.som.model.dataHandle.SomDrgStandardExample;
import com.my.som.model.dataHandle.SomOprnOprtInfo;
import com.my.som.model.dataHandle.SomOprnOprtInfoExample;
import com.my.som.service.medicalQuality.CodeResourceConsumptionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * Service实现类
 * Created by sky on 2020/3/3.
 */
@Service
public class CodeResourceConsumptionServiceImpl implements CodeResourceConsumptionService {
    @Autowired
    private CodeResourceConsumptionDao codeResourceConsumptionDao;
    @Autowired
    private SomDipStandardMapper tpdDipBenchmarkMapper;
    @Autowired
    private SomOprnOprtInfoMapper busOperateDiagnosisMapper;

    @Override
    public List<CodeResourceConsumptionVo> list(SettleListMainInfoQueryParam queryParam, Integer pageSize, Integer pageNum) {
        PageHelper.startPage(pageNum, pageSize);
        List<CodeResourceConsumptionVo> list = codeResourceConsumptionDao.getCodeResourceConsumptionInfo(queryParam);
        for(CodeResourceConsumptionVo cr:list){
//            if("1".equals(cr.getMainDiagIsChoErr())||"1".equals(cr.getMainOprnIsChoErr())){
                String dipCodg = cr.getDipCodg();
                String resuCosmAdjmNewDipCodg = cr.getResuCosmAdjmNewDipCodg();
                String outhostime = cr.getOuthostime();
                if(!ValidateUtil.isEmpty(resuCosmAdjmNewDipCodg)){
                    //当前组费用
                    String dipAvgInHosCost = "0";
                    if(!ValidateUtil.isEmpty(dipCodg)){
                        SomDipStandardExample tpdDipBenchmarkExample = new SomDipStandardExample();
                        SomDipStandardExample.Criteria criteria = tpdDipBenchmarkExample.createCriteria();
                        criteria.andDipCodeEqualTo(dipCodg);
                        if(!ValidateUtil.isEmpty(outhostime)&&outhostime.length()>=4){
                            criteria.andStandardYearEqualTo(outhostime.substring(0,4));
                        }else{
                            String nowYear = DateUtil.getCurDateTime().substring(0,4);
                            criteria.andStandardYearEqualTo(nowYear);
                        }
                        List<SomDipStandard> tpdDipBenchmarkList = tpdDipBenchmarkMapper.selectByExample(tpdDipBenchmarkExample);
                        if(!ValidateUtil.isEmpty(tpdDipBenchmarkList)){
                            if(!ValidateUtil.isEmpty(tpdDipBenchmarkList.get(0).getDipStandardInpf())){
                                dipAvgInHosCost = String.valueOf(tpdDipBenchmarkList.get(0).getDipStandardInpf());
                            }
                        }
                    }
                    //建议组费用
                    String suggestDipAvgInHosCost = "0";
                    SomDipStandardExample tpdDipBenchmarkExample1 = new SomDipStandardExample();
                    SomDipStandardExample.Criteria criteria1 = tpdDipBenchmarkExample1.createCriteria();
                    criteria1.andDipCodeEqualTo(resuCosmAdjmNewDipCodg);
                    if(!ValidateUtil.isEmpty(outhostime)&&outhostime.length()>=4){
                        criteria1.andStandardYearEqualTo(outhostime.substring(0,4));
                    }else{
                        String nowYear = DateUtil.getCurDateTime().substring(0,4);
                        criteria1.andStandardYearEqualTo(nowYear);
                    }
                    List<SomDipStandard> tpdDipBenchmarkList1 = tpdDipBenchmarkMapper.selectByExample(tpdDipBenchmarkExample1);

                    if(!ValidateUtil.isEmpty(tpdDipBenchmarkList1)){
                        if(!ValidateUtil.isEmpty(tpdDipBenchmarkList1.get(0).getDipStandardInpf())){
                            suggestDipAvgInHosCost = String.valueOf(tpdDipBenchmarkList1.get(0).getDipStandardInpf());
                        }
                    }
                    if(!ValidateUtil.isEmpty(suggestDipAvgInHosCost)&&
                            !ValidateUtil.isEmpty(dipAvgInHosCost)){
                        if((new BigDecimal(suggestDipAvgInHosCost)).compareTo(new BigDecimal(dipAvgInHosCost))<=0){
                            cr.setMainDiagIsChoErr("0");
                            cr.setMainOprnIsChoErr("0");
                            cr.setNewDipCodeAndName("(无需调整)"+cr.getDipCodeAndName());
                        }else{
                            //判断dip编码是否含有病人手术，有则建议，否则不建议
                            int falg=0;
                            //查询该病人所有手术
                            SomOprnOprtInfoExample example = new SomOprnOprtInfoExample();
                            SomOprnOprtInfoExample.Criteria criteria = example.createCriteria();
                            if (!StringUtils.isEmpty(cr.getId())) {
                                criteria.andSettleListIdEqualTo(cr.getId());
                            }
                            List<SomOprnOprtInfo> busOperateDiagnosisList = busOperateDiagnosisMapper.selectByExample(example);
                            for(SomOprnOprtInfo bod:busOperateDiagnosisList){
                                if(!ValidateUtil.isEmpty(bod)){
                                    if(resuCosmAdjmNewDipCodg.indexOf(bod.getC35c()) != -1){
                                        falg++;
                                    }
                                }
                            }
                            if(falg==0){
                                cr.setMainDiagIsChoErr("0");
                                cr.setMainOprnIsChoErr("0");
                                cr.setNewDipCodeAndName("(无需调整)");
                            }
                        }
                    }
                }else{
                    cr.setMainDiagIsChoErr("0");
                    cr.setMainOprnIsChoErr("0");
                    cr.setNewDipCodeAndName("(无需调整)");
                }
//            }
        }
        list.sort((o1, o2) -> o2.getMainDiagIsChoErr().compareTo(o1.getMainDiagIsChoErr()));
        list.sort((o1, o2) -> o2.getMainOprnIsChoErr().compareTo(o1.getMainOprnIsChoErr()));
        return list;
    }

    @Override
    public CodeResourceConsumptionCountVo getCountInfo(SettleListMainInfoQueryParam queryParam) {
        CodeResourceConsumptionCountVo codeResourceConsumptionCountVo = codeResourceConsumptionDao.getCount(queryParam); //总指标赋值
        List<Long> ids = new ArrayList();
        List<CodeResourceConsumptionVo> list = codeResourceConsumptionDao.getCodeResourceConsumptionInfo(queryParam);
        if(!ValidateUtil.isEmpty(list)){
            String totalMedicalNum  = String.valueOf(list.size());
            for(CodeResourceConsumptionVo cr:list){
                if("1".equals(cr.getMainDiagIsChoErr())||"1".equals(cr.getMainOprnIsChoErr())){
                    String dipCodg = cr.getDipCodg();
                    String resuCosmAdjmNewDipCodg = cr.getResuCosmAdjmNewDipCodg();
                    String outhostime = cr.getOuthostime();
                    if(!ValidateUtil.isEmpty(resuCosmAdjmNewDipCodg)){
                        //当前组费用
                        String dipAvgInHosCost = "0";
                        if(!ValidateUtil.isEmpty(dipCodg)){
                            SomDipStandardExample tpdDipBenchmarkExample = new SomDipStandardExample();
                            SomDipStandardExample.Criteria criteria = tpdDipBenchmarkExample.createCriteria();
                            criteria.andDipCodeEqualTo(dipCodg);
                            if(!ValidateUtil.isEmpty(outhostime)&&outhostime.length()>=4){
                                criteria.andStandardYearEqualTo(outhostime.substring(0,4));
                            }else{
                                String nowYear = DateUtil.getCurDateTime().substring(0,4);
                                criteria.andStandardYearEqualTo(nowYear);
                            }
                            List<SomDipStandard> tpdDipBenchmarkList = tpdDipBenchmarkMapper.selectByExample(tpdDipBenchmarkExample);
                            if(!ValidateUtil.isEmpty(tpdDipBenchmarkList)){
                                if(!ValidateUtil.isEmpty(tpdDipBenchmarkList.get(0).getDipStandardInpf())){
                                    dipAvgInHosCost = String.valueOf(tpdDipBenchmarkList.get(0).getDipStandardInpf());
                                }
                            }
                        }
                        //建议组费用
                        String suggestDipAvgInHosCost = "0";
                        SomDipStandardExample tpdDipBenchmarkExample1 = new SomDipStandardExample();
                        SomDipStandardExample.Criteria criteria1 = tpdDipBenchmarkExample1.createCriteria();
                        criteria1.andDipCodeEqualTo(resuCosmAdjmNewDipCodg);
                        if(!ValidateUtil.isEmpty(outhostime)&&outhostime.length()>=4){
                            criteria1.andStandardYearEqualTo(outhostime.substring(0,4));
                        }else{
                            String nowYear = DateUtil.getCurDateTime().substring(0,4);
                            criteria1.andStandardYearEqualTo(nowYear);
                        }
                        List<SomDipStandard> tpdDipBenchmarkList1 = tpdDipBenchmarkMapper.selectByExample(tpdDipBenchmarkExample1);

                        if(!ValidateUtil.isEmpty(tpdDipBenchmarkList1)){
                            if(!ValidateUtil.isEmpty(tpdDipBenchmarkList1.get(0).getDipStandardInpf())){
                                suggestDipAvgInHosCost = String.valueOf(tpdDipBenchmarkList1.get(0).getDipStandardInpf());
                            }
                        }
                        if(!ValidateUtil.isEmpty(suggestDipAvgInHosCost)&&
                                !ValidateUtil.isEmpty(dipAvgInHosCost)){
                            if(new BigDecimal(suggestDipAvgInHosCost).
                                    compareTo(new BigDecimal(dipAvgInHosCost))<=0){
                                continue;
                            }else{
                                //查询该病人所有手术
                                SomOprnOprtInfoExample example = new SomOprnOprtInfoExample();
                                SomOprnOprtInfoExample.Criteria criteria = example.createCriteria();
                                if (!StringUtils.isEmpty(cr.getId())) {
                                    criteria.andSettleListIdEqualTo(cr.getId());
                                }
                                List<SomOprnOprtInfo> busOperateDiagnosisList = busOperateDiagnosisMapper.selectByExample(example);
                                for(SomOprnOprtInfo bod:busOperateDiagnosisList){
                                    if(!ValidateUtil.isEmpty(bod)){
                                        if(resuCosmAdjmNewDipCodg.indexOf(bod.getC35c()) != -1){
                                            ids.add(cr.getId());break;
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
            if(!ValidateUtil.isEmpty(ids)){
                queryParam.setIds(ids);
                CodeResourceConsumptionCountVo codeResourceConsumptionCountVo1 =  codeResourceConsumptionDao.getCountInfo(queryParam);
                codeResourceConsumptionCountVo.setDiagnoseErrorNum(codeResourceConsumptionCountVo1.getDiagnoseErrorNum());
                codeResourceConsumptionCountVo.setOperativeErrorNum(codeResourceConsumptionCountVo1.getOperativeErrorNum());
                codeResourceConsumptionCountVo.setDrgErrorNum(codeResourceConsumptionCountVo1.getDrgErrorNum());
                codeResourceConsumptionCountVo.setDipErrorNum(codeResourceConsumptionCountVo1.getDipErrorNum());
            }else{
                codeResourceConsumptionCountVo.setDiagnoseErrorNum("0");
                codeResourceConsumptionCountVo.setOperativeErrorNum("0");
                codeResourceConsumptionCountVo.setDrgErrorNum("0");
                codeResourceConsumptionCountVo.setDipErrorNum("0");
            }
        }
        return codeResourceConsumptionCountVo;
    }

}
