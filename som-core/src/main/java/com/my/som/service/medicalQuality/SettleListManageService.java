package com.my.som.service.medicalQuality;

import com.my.som.common.api.CommonPage;
import com.my.som.common.dto.CommonQueryDto;
import com.my.som.common.dto.ReportDto;
import com.my.som.common.vo.SysUserBase;
import com.my.som.dto.medicalQuality.*;
import com.my.som.dto.patienInfo.DataGroupInfo;
import com.my.som.dto.patienInfo.PatienInfo;
import com.my.som.dto.patienInfo.PatienInfoByHis;
import com.my.som.dto.somDiagInfo.SomDiagInfo;
import com.my.som.dto.somOprnInfo.SomOprnInfo;
import com.my.som.model.dataHandle.SomHiInvyBasInfo;
import com.my.som.vo.dataHandle.SettleListValidateVo;
import com.my.som.vo.medicalQuality.*;
import com.my.som.vo.pregroup.PreGroupVo;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * 数据处理日志Service
 * Created by sky on 2020/1/5.
 */
public interface SettleListManageService {

    /**
     * 分页查询日志
     */
    List<BusSettleListMainInfo> list(SettleListMainInfoQueryParam queryParam, Integer pageSize, Integer pageNum);

    /**
     * 根据结算清单id获取结算清单所有信息
     * @param id
     * @return
     */
    AllBusSettleListInfo getSettleListAllInfoById(String id,String k00);

    /**
     * 更新结算清单
     * @param allBusSettleListInfo
     * @return
     */
    int updateSettleListAllInfo(AllBusSettleListInfo allBusSettleListInfo);

    /**
     * 校验结算清单病案质量
     * @param allBusSettleListInfo
     * @return
     */
    ValidateMedicalResult getValidateMedicalResult(AllBusSettleListInfo allBusSettleListInfo);

    /**
     * 获取该疾病参考费用、时间、入组信息
     * @param
     * @return
     */
    DrgsCostDayGroupInfo getStandCostAndDayAndGroupInfo(AllBusSettleListInfo allBusSettleListInfo, SysUserBase sysUserBase);


    List<BusSettleListMainInfoForDoctor> fetchListForDoctor(SettleListMainInfoQueryParam queryParam, SysUserBase sysUserBase, Integer pageSize, Integer pageNum);

    void deleteDataById(SettleListMainInfoQueryParam dto);

    void recoveryDataById(SettleListMainInfoQueryParam dto);

    AllErrorVo getErrorInfo(Long id);

    Boolean selectBusSettleErrorLIst (Long id,String k00);

    Map<String,List<BusSettleListResult>> selectBusSettleLIstError(Long id,String k00);

    /**
     * 重新校验医保结算清单
     * @param dto
     * @return
     */
    Map<String,List<BusSettleListResult>> restartCheck(SettleListValidateVo dto);

    /**
     * 修改bus_settle_list数据
     * @param dto
     */
    ModifyBusSettleListDto updateSettleList(ModifyBusSettleListDto dto);

    /**
     * 插入历史数据
     * @param dto
     */
    void insertHistory(ModifyBusSettleListDto dto);
    /**
     * 更新settleList查看历史数据状态
     * @return
     */
    void updateSettleListHisState(String k00);
    /**
     * 更新settleLists是否点击完成
     * @return
     */
    void updateSettleListLookOver(ModifyBusSettleListDto dto);
    /**
     * 根据时间节点还原数据
     * @param dto
     * @return
     */
    void restoreHistoryBusSettle(ModifyBusSettleListDto dto);

    AllErrorVo selectProcessResult(String id);

    /**
     * 获取ICD编码
     * @param dto
     * @return
     */
    Map<String, Object> queryICDCode(CommonQueryDto dto);

    /**
     * 结算清单
     * @param dto
     * @return
     */
    UpdateBusSettleListResultVo modifySettleListInfo(ModifyDto dto);

    /**
     * 查询结算清单标识记录
     * @param dto
     * @return
     */
    List<SettleListOpeLogInfo> querySettleListOpeLog(ModifyBusSettleListDto dto);

    /**
     * 更改锁状态
     * @param dto
     */
    void updateLockState(ModifyBusSettleListDto dto);

    /**
     *
     * @param dto
     * @return
     */
    SettleListTempLockVo queryLockState(ModifyBusSettleListDto dto);

    /**
     * 查询结算清单标识记录
     * @param dto
     * @return
     */
    SomHiInvyBasInfo querySkipData(ModifyBusSettleListDto dto);

    /**
     * 预览文件
     * @param dto
     * @param response
     */
    void preview(ReportDto dto, HttpServletResponse response);
    /**
     * 设置医保结算等级
     */
    void setSettleLevel(AllBusSettleListInfo allBusSettleListInfo);

    /**
     * 查询出院诊断信息
     * @return
     */
    List<SomDiagInfo> queryDiagInfo(String query);
    List<SomDiagInfo> queryDiagNameInfo(String query);


    /**
     * 查询手术信息
     * @return
     */
    List<SomOprnInfo> queryOprnInfo(String query);
    List<SomOprnInfo> queryOprnNameInfo(String query);


    /**
     * 查询医院信息
     */
    String queryHosInfo();

    /**
     * 获取模拟预分组的结果
     * @param patienInfo 基本信息
     * @param request
     * @param response
     * @return
     */
    PreGroupVo getSimlatePreGroup(PatienInfo patienInfo, HttpServletRequest request, HttpServletResponse response);

    DataGroupInfo preGroupResult(PatienInfoByHis patienInfo);

    CommonPage<BusSettleListMainInfo> querySpecialDisease(SettleListMainInfoQueryParam queryParam, Integer pageSize, Integer pageNum);

    String getSpecialDiseaseType();
}
