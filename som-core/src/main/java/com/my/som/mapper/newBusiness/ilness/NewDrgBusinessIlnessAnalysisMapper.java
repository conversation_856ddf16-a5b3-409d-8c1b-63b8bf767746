package com.my.som.mapper.newBusiness.ilness;

import com.my.som.dto.newBusiness.disease.NewBusinessDiseaseDto;
import com.my.som.vo.newBusiness.disease.NewBusinessDiseaseVo;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * drg 病种分析
 */
@Component
public interface NewDrgBusinessIlnessAnalysisMapper {
    /**
     * 查询指标数据
     * @param dto
     * @return
     */
    List<NewBusinessDiseaseVo> queryDrgIlnessKpiData(NewBusinessDiseaseDto dto);

    /**
     * 查询预测分析数据
     * @param dto
     * @return
     */
    List<NewBusinessDiseaseVo> queryDrgDiseaseForecastData(NewBusinessDiseaseDto dto);
}
