package com.my.som.mapper.newBusiness.common;

import com.my.som.common.vo.SomBackUser;
import com.my.som.dto.newBusiness.NewBusinessCommonDto;
import com.my.som.dto.newBusiness.dept.NewBusinessDeptDto;
import com.my.som.vo.dipBusiness.DipInGroupAnalysisVo;
import com.my.som.vo.newBusiness.NewBusinessAnalysisVo;
import com.my.som.vo.newBusiness.NewBusinessCommonVo;
import com.my.som.vo.newBusiness.dept.NewBusinessDeptVo;

import java.util.List;

/**
 * @author: zyd
 * @version 1.0
 * @date: 2023-07-31
 * @description:
 */
public interface NewBusinessCommonAnalysisMapper {

    /**
     * 查询病组亏损
     * @param dto
     * @return
     */
    List<NewBusinessAnalysisVo> queryDiseaseLoss(NewBusinessCommonDto dto);

    /**
     * 查询医生亏损
     * @param dto
     * @return
     */
    List<NewBusinessAnalysisVo> queryDoctorLoss(NewBusinessCommonDto dto);

    /**
     * 查询患者亏损
     * @param dto
     * @return
     */
    List<NewBusinessAnalysisVo> queryPatientLoss(NewBusinessCommonDto dto);

    List<NewBusinessAnalysisVo> queryPatientLoss2(NewBusinessCommonDto dto);

    /**
     * 查询下拉
     * @param dto
     * @return
     */
    List<NewBusinessCommonVo> queryDropdown(NewBusinessCommonDto dto);

    /**
     * 查询病案错误情况
     * @param dto
     * @return
     */
    List<DipInGroupAnalysisVo> queryMedError(NewBusinessCommonDto dto);

    /**
     * 查询汇总页面表格数据
     * @param dto
     * @return
     */
    List<NewBusinessAnalysisVo> queryAnalysisSummary(NewBusinessCommonDto dto);

    /**
     * 查询医生下拉
     * @return
     */
    List<NewBusinessCommonVo> queryDoctorDropDown();

    void updateSwitchState(SomBackUser dto);



    List<NewBusinessAnalysisVo> queryDrgDiseaseLoss(NewBusinessCommonDto dto);

    List<NewBusinessAnalysisVo> queryDrgDoctorLoss(NewBusinessCommonDto dto);

    List<NewBusinessAnalysisVo> queryDrgPatientLoss(NewBusinessCommonDto dto);


    List<NewBusinessAnalysisVo> queryDrgPatientLoss2(NewBusinessCommonDto dto);

    List<NewBusinessAnalysisVo> queryDrgAnalysisSummary(NewBusinessCommonDto dto);

    List<DipInGroupAnalysisVo> queryDrgMedError(NewBusinessCommonDto dto);

    List<NewBusinessCommonVo> queryDrgDropdown(NewBusinessCommonDto dto);

    List<NewBusinessAnalysisVo> queryContrastData(NewBusinessCommonDto dto);

    List<NewBusinessCommonVo> queryDeptDropdown(NewBusinessCommonDto dto);

    List<NewBusinessCommonVo> queryDoctorDropdown(NewBusinessCommonDto dto);

    List<NewBusinessCommonVo> queryDiseaseDropdown(NewBusinessCommonDto dto);

    List<NewBusinessAnalysisVo> queryPatientContrastData(NewBusinessCommonDto dto);

    List<NewBusinessAnalysisVo> queryDoctorContrastData(NewBusinessCommonDto dto);

    List<NewBusinessAnalysisVo> selectPatientContrastData(NewBusinessCommonDto dto);

    /**
     * 查询病组象限图数据
     * @param dto
     * @return
     */
    List<NewBusinessDeptVo> selectSickGroupQuadrant(NewBusinessCommonDto dto);

}
