package com.my.som.dto.newBusiness.customPatient;

import com.my.som.common.dto.CommonQueryDto;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class NewBusinessCustomPatientDto extends CommonQueryDto {

    private String startYear;

    private String endYear;

    private String startIssue;

    private String endIssue;

    private String bah;

    private String isInGroup;

    private String isLoss;

    private String grpFlag;

    private List<String> notGroupReason;

    private List<String> errorReason;

    private String patientIds;

    private String costSection;

    private List<String> patientIdList;

    /** 病种编码 */
    private String icdCodg;

    private String selectVal;

    private Boolean sort;
}
