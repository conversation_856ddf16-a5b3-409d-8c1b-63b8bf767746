package com.my.som.dto.preValidate;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * @author: zyd
 * @version 1.0
 * @date: 2023-07-31
 * @description: 预校验
 */
@Getter
@Setter
public class PreValidateDto {

    /**医疗机构编码*/
    private String yljgdm;

    /** 病案首页类型 */
    private String basylx;

    /** 医保编号 **/
    private String ybbh;

    /** 参保类型 */
    private String cblx;

    /** 机构名称 */
    private String username;

    /** 医疗付款方式 */
    private String ylfkfs;

    /** 健康卡号 */
    private String jkkh;

    /** 住院次数 */
    private String zycs;

    /** 病案号 */
    private String bah;

    /** 姓名 */
    private String xm;

    /** 性别 */
    private String xb;

    /** 出生日期 */
    private String csrq;

    /** 年龄 */
    private String nl;

    /** 国籍 */
    private String gj;

    /** (年龄不足1周岁的)年龄(天) */
    private String bzyzsnl;

    /** 新生儿出生体重(克) */
    private String xsecstz;

    /** 新生儿入院体重(克） */
    private String xserytz;

    /** 出生地 */
    private String csd;

    /** 籍贯 */
    private String gg;

    /** 民族 */
    private String mz;

    /** 身份证号 */
    private String sfzh;

    /** 职业 */
    private String zy;

    /** 婚姻 */
    private String hy;

    /** 现住址 */
    private String xzz;

    /** 电话 */
    private String dh;

    /** 邮编 */
    private String yb1;

    /** 户口地址 */
    private String hkdz;

    /** 邮编 */
    private String yb2;

    /** 工作单位及地址 */
    private String gzdwjdz;

    /** 工作单位名称 */
    private String gzdwmc;

    /** 单位电话 */
    private String dwdh;

    /** 邮编 */
    private String yb3;

    /** 联系人姓名 */
    private String lxrxm;

    /** 关系 */
    private String gx;

    /** 地址 */
    private String dz;

    /** 电话 */
    private String dh2;

    /** 入院途径 */
    private String rytj;

    /** 入院时间 */
    private String rysj;

    /** 时 */
    private String rysjs;

    /** 入院科别 */
    private String rykbbm;

    /** 入院病房 */
    private String rybf;

    /** 转科科别 */
    private String zkkbbm;

    /** 出院时间 */
    private String cysj;

    /** 时 */
    private String cysjs;

    /** 出院科别 */
    private String cykbbm;

    /** 出院病房 */
    private String cybf;

    /** 实际住院(天) */
    private BigDecimal zyts;

    /** 门(急)诊诊断 */
    private String mzzd;

    /**出院主要诊断编码（中医）*/
    private String zyzd_jbbm;

    /**出院主要诊断名称（中医）*/
    private  String zb;

    /**出院主要诊断入院病情（中医）*/
    private String zyzd_rybq;

    /**出院主要诊断出院病情（中医）*/
    private String zyzd_cybq;

    /** 疾病编码 */
    private String jbbm;

    /** 主要诊断 */
    private String zyzd;

    /** 主要诊断标识 */
    private String zzdbs;

    /** 疾病编码 */
    private String jbdm;

    /** 疾病编码类型 */
    private String jbdmtype;

    /** 入院病情 */
    private String rybq;

    /** 其他诊断8 */
    private String qtzd8;

    /** 主要诊断标识 */
    private String zzdbs8;

    /** 疾病编码8 */
    private String jbdm8;

    /** 疾病编码8类型 */
    private String jbdmtype8;

    /** 入院病情8 */
    private String rybq8;

    /** 其他诊断1 */
    private String qtzd1;

    /** 主要诊断标识 */
    private String zzdbs1;

    /** 疾病编码1 */
    private String jbdm1;

    /** 疾病编码1类型 */
    private String jbdmtype1;

    /** 入院病情1 */
    private String rybq1;

    /** 其他诊断9 */
    private String qtzd9;

    /** 主要诊断标识 */
    private String zzdbs9;

    /** 疾病编码9 */
    private String jbdm9;

    /** 疾病编码9类型 */
    private String jbdmtype9;

    /** 入院病情9 */
    private String rybq9;

    /** 其他诊断2 */
    private String qtzd2;

    /** 主要诊断标识 */
    private String zzdbs2;

    /** 疾病编码2 */
    private String jbdm2;

    /** 疾病编码2类型 */
    private String jbdmtype2;

    /** 入院病情2 */
    private String rybq2;

    /** 其他诊断10 */
    private String qtzd10;

    /** 主要诊断标识 */
    private String zzdbs10;

    /** 疾病编码10 */
    private String jbdm10;

    /** 疾病编码10类型 */
    private String jbdmtype10;

    /** 入院病情10 */
    private String rybq10;

    /** 其他诊断3 */
    private String qtzd3;

    /** 主要诊断标识 */
    private String zzdbs3;

    /** 疾病编码3 */
    private String jbdm3;

    /** 疾病编码3类型 */
    private String jbdmtype3;

    /** 入院病情3 */
    private String rybq3;

    /** 其他诊断11 */
    private String qtzd11;

    /** 主要诊断标识 */
    private String zzdbs11;

    /** 疾病编码11 */
    private String jbdm11;

    /** 疾病编码11类型 */
    private String jbdmtype11;

    /** 入院病情11 */
    private String rybq11;

    /** 其他诊断4 */
    private String qtzd4;

    /** 疾病编码4 */
    private String jbdm4;

    /** 主要诊断标识 */
    private String zzdbs4;

    /** 疾病编码4类型 */
    private String jbdmtype4;

    /** 入院病情4 */
    private String rybq4;

    /** 其他诊断12 */
    private String qtzd12;

    /** 主要诊断标识 */
    private String zzdbs12;

    /** 疾病编码12 */
    private String jbdm12;

    /** 疾病编码12类型 */
    private String jbdmtype12;

    /** 入院病情12 */
    private String rybq12;

    /** 其他诊断5 */
    private String qtzd5;

    /** 主要诊断标识 */
    private String zzdbs5;

    /** 疾病编码5 */
    private String jbdm5;

    /** 疾病编码5类型 */
    private String jbdmtype5;

    /** 入院病情5 */
    private String rybq5;

    /** 其他诊断13 */
    private String qtzd13;

    /** 主要诊断标识 */
    private String zzdbs13;

    /** 疾病编码13 */
    private String jbdm13;

    /** 疾病编码13类型 */
    private String jbdmtype13;

    /** 入院病情13 */
    private String rybq13;

    /** 其他诊断6 */
    private String qtzd6;

    /** 主要诊断标识 */
    private String zzdbs6;

    /** 疾病编码6 */
    private String jbdm6;

    /** 疾病编码6类型 */
    private String jbdmtype6;

    /** 入院病情6 */
    private String rybq6;

    /** 其他诊断14 */
    private String qtzd14;

    /** 主要诊断标识 */
    private String zzdbs14;

    /** 疾病编码14 */
    private String jbdm14;

    /** 疾病编码14类型 */
    private String jbdmtype14;

    /** 入院病情14 */
    private String rybq14;

    /** 其他诊断7 */
    private String qtzd7;

    /** 主要诊断标识 */
    private String zzdbs7;

    /** 疾病编码7 */
    private String jbdm7;

    /** 疾病编码7类型 */
    private String jbdmtype7;

    /** 入院病情7 */
    private String rybq7;

    /** 其他诊断15 */
    private String qtzd15;

    /** 主要诊断标识 */
    private String zzdbs15;

    /** 疾病编码15 */
    private String jbdm15;

    /** 疾病编码15类型 */
    private String jbdmtype15;

    /** 入院病情15 */
    private String rybq15;

    /** 中毒的外部原因 */
    private String wbyy;

    /** 疾病编码 */
    private String h23;

    /** 病理诊断出 */
    private String blzd;

    /** 疾病编码 */
    private String jbmm;

    /** 病理号 */
    private String blh;

    /** 药物过敏 */
    private String ywgm;

    /** 过敏药物疾病 */
    private String gmyw;

    /** 死亡患者尸检 */
    private String swhzsj;

    /** abo血型 */
    private String xx;

    /** rh */
    private String rh;

    /** 科主任 */
    private String kzr;

    /** 主任（副主任）医师(取医生编码) */
    private String zrys;

    /** 主治医师病理号死亡患者尸检(取医生编码) */
    private String zzys;

    /** 住院医师(取医生编码) */
    private String zyys;

    /** 责任护士姓名 */
    private String zrhs;

    /** 责任护士代码 */
    private String zrhsCode;

    /** 进修医师住 */
    private String jxys;

    /** 实习医师 */
    private String sxys;

    /** 编码员 */
    private String bmy;

    /** 病案质量 */
    private String bazl;

    /** 质控医师 */
    private String zkys;

    /** 质控护士 */
    private String zkhs;

    /** 质控日期 */
    private String zkrq;

    /** 手术及操作编码 */
    private String ssjczbm1;

    /** 手术及操作日期 */
    private String ssjczrq1;

    /** 手术级别 */
    private String ssjb1;

    /** 手术及操作名称 */
    private String ssjczmc1;

    /** 术者 */
    private String sz1;

    /** 术者名称 */
    private String szmc1;

    /** i助 */
    private String yz1;

    /** ii助 */
    private String ez1;

    /** 切口等级 */
    private String qkdj1;

    /** 切口愈合类别 */
    private String qkyhlb1;

    /** 麻醉方式 */
    private String mzfs1;

    /** 麻醉医师 */
    private String mzys1;

    /** 麻醉医师 */
    private String mzysmc1;

    /** 手术开始时间 */
    private String sskssj1;

    /** 手术结束时间 */
    private String ssjssj1;

    /** 麻醉开始时间 */
    private String mzkssj1;

    /** 麻醉结束时间 */
    private String mzjssj1;

    /** 手术及操作编码 */
    private String ssjczbm2;

    /** 手术及操作日期 */
    private String ssjczrq2;

    /** 手术级别 */
    private String ssjb2;

    /** 手术及操作名称 */
    private String ssjczmc2;

    /** 术者 */
    private String sz2;

    /** 术者名称 */
    private String szmc2;

    /** i助 */
    private String yz2;

    /** ii助 */
    private String ez2;

    /** 切口等级 */
    private String qkdj2;

    /** 切口愈合类别 */
    private String qkyhlb2;

    /** 麻醉方式 */
    private String mzfs2;

    /** 麻醉医师 */
    private String mzys2;

    /** 麻醉医师 */
    private String mzysmc2;

    /** 手术开始时间 */
    private String sskssj2;

    /** 手术结束时间 */
    private String ssjssj2;

    /** 麻醉开始时间 */
    private String mzkssj2;

    /** 麻醉结束时间 */
    private String mzjssj2;

    /** 手术及操作编码 */
    private String ssjczbm3;

    /** 手术及操作日期 */
    private String ssjczrq3;

    /** 手术级别 */
    private String ssjb3;

    /** 手术及操作名称 */
    private String ssjczmc3;

    /** 术者 */
    private String sz3;

    /** 术者名称 */
    private String szmc3;

    /** i助 */
    private String yz3;

    /** ii助 */
    private String ez3;

    /** 切口等级 */
    private String qkdj3;

    /** 切口愈合类别 */
    private String qkyhlb3;

    /** 麻醉方式 */
    private String mzfs3;

    /** 麻醉医师 */
    private String mzys3;

    /** 麻醉医师 */
    private String mzysmc3;

    /** 手术开始时间 */
    private String sskssj3;

    /** 手术结束时间 */
    private String ssjssj3;

    /** 麻醉开始时间 */
    private String mzkssj3;

    /** 麻醉结束时间 */
    private String mzjssj3;

    /** 手术及操作编码 */
    private String ssjczbm4;

    /** 手术及操作日期 */
    private String ssjczrq4;

    /** 手术级别 */
    private String ssjb4;

    /** 手术及操作名称 */
    private String ssjczmc4;

    /** 术者 */
    private String sz4;

    /** 术者名称 */
    private String szmc4;

    /** i助 */
    private String yz4;

    /** ii助 */
    private String ez4;

    /** 切口等级 */
    private String qkdj4;

    /** 切口愈合类别 */
    private String qkyhlb4;

    /** 麻醉方式 */
    private String mzfs4;

    /** 情况麻醉医师 */
    private String mzys4;

    /** 麻醉医师 */
    private String mzysmc4;

    /** 手术开始时间 */
    private String sskssj4;

    /** 手术结束时间 */
    private String ssjssj4;

    /** 麻醉开始时间 */
    private String mzkssj4;

    /** 麻醉结束时间 */
    private String mzjssj4;

    /** 手术及操作编码 */
    private String ssjczbm5;

    /** 手术及操作日期 */
    private String ssjczrq5;

    /** 手术级别 */
    private String ssjb5;

    /** 手术及操作名称 */
    private String ssjczmc5;

    /** 术者 */
    private String sz5;

    /** 术者名称 */
    private String szmc5;

    /** i助 */
    private String yz5;

    /** ii助 */
    private String ez5;

    /** 切口等级 */
    private String qkdj5;

    /** 切口愈合类别 */
    private String qkyhlb5;

    /** 麻醉方式 */
    private String mzfs5;

    /** 麻醉医师 */
    private String mzys5;

    /** 麻醉医师 */
    private String mzysmc5;

    /** 手术开始时间 */
    private String sskssj5;

    /** 手术结束时间 */
    private String ssjssj5;

    /** 麻醉开始时间 */
    private String mzkssj5;

    /** 麻醉结束时间 */
    private String mzjssj5;

    /** 手术及操作编码 */
    private String ssjczbm6;

    /** 手术及操作日期 */
    private String ssjczrq6;

    /** 手术级别 */
    private String ssjb6;

    /** 手术及操作名称 */
    private String ssjczmc6;

    /** 术者 */
    private String sz6;

    /** 术者名称 */
    private String szmc6;

    /** i助 */
    private String yz6;

    /** ii助 */
    private String ez6;

    /** 切口等级 */
    private String qkdj6;

    /** 切口愈合类别 */
    private String qkyhlb6;

    /** 麻醉方式 */
    private String mzfs6;

    /** 麻醉医师 */
    private String mzys6;

    /** 麻醉医师 */
    private String mzysmc6;

    /** 手术开始时间 */
    private String sskssj6;

    /** 手术结束时间 */
    private String ssjssj6;

    /** 麻醉开始时间 */
    private String mzkssj6;

    /** 麻醉结束时间 */
    private String mzjssj6;

    /** 手术及操作编码 */
    private String ssjczbm7;

    /** 手术及操作日期 */
    private String ssjczrq7;

    /** 手术级别 */
    private String ssjb7;

    /** 手术及操作名称 */
    private String ssjczmc7;

    /** 术者 */
    private String sz7;

    /** 术者名称 */
    private String szmc7;

    /** i助 */
    private String yz7;

    /** ii助 */
    private String ez7;

    /** 切口等级 */
    private String qkdj7;

    /** 切口愈合类别 */
    private String qkyhlb7;

    /** 麻醉方式 */
    private String mzfs7;

    /** 麻醉医师 */
    private String mzys7;

    /** 麻醉医师名称 */
    private String mzysmc7;

    /** 手术开始时间 */
    private String sskssj7;

    /** 手术结束时间 */
    private String ssjssj7;

    /** 麻醉开始时间 */
    private String mzkssj7;

    /** 麻醉结束时间 */
    private String mzjssj7;

    /** 离院方式 */
    private String lyfs;

    /** 医嘱转院，拟接收医疗机构名称 */
    private String yzzy_yljg;

    /** 医嘱转社区卫生服务机构/乡镇卫生院，拟接收医疗机构名称 */
    private String wsy_yljg;

    /** 是否有出院31天内再住院计划手术情况 */
    private String sfzzyjh;

    /** 目的 */
    private String md;

    /** 颅脑损伤患者昏迷入院前时间：天 */
    private String ryq_t;

    /** 颅脑损伤患者昏迷入院前时间：小时 */
    private String ryq_xs;

    /** 颅脑损伤患者昏迷入院前时间：分 */
    private String ryq_f;

    /** 颅脑损伤患者昏迷入院后时间：天 */
    private String ryh_t;

    /** 颅脑损伤患者昏迷入院后时间：小时 */
    private String ryh_xs;

    /** 颅脑损伤患者昏迷入院后时间：分 */
    private String ryh_f;

    /** 住院费用(元)：总费用 */
    private BigDecimal zyzfy = new BigDecimal(0);
    /*住院费用*/
    private BigDecimal zyfy = new BigDecimal(0);

    /** 药占比 */
    private BigDecimal ypfyzb;

    /** 耗占比 */
    private BigDecimal hcfyzb;

    /** 检验费用占比 */
    private String jyfyzb;

    /** 检查费用占比 */
    private String jcfyzb;

    /** 自付金额 */
    private String zfje;

    /** 综合医疗服务类：(1)一般医疗服务费 */
    private String ylfuf;

    /** 一般治疗操作费 */
    private String zlczf;

    /** 护理费住院费 */
    private String nursfee;

    /** 其他费用 */
    private String oth_fee_com;

    /** 诊断类：(5)病理诊断费 */
    private String cas_diag_fee;

    /** 实验室诊断费 */
    private String lab_diag_fee;

    /** 影像学诊断费 */
    private String rdhy_diag_fee;

    /** 临床诊断项目费 */
    private String clnc_diag_item_fee;

    /** 治疗类：(9)非手术治疗项目费 */
    private String nsrgtrt_item_fee;

    /** 临床物理治疗费 */
    private String wlzlf;

    /** 手术治疗费 */
    private String oprn_treat_fee;

    /** 麻醉费 */
    private String maf;

    /** 手术费 */
    private String ssf;

    /** 康复类：(11)康复费 */
    private String rhab_fee;

    /** 中医类:(12)中医治疗费 */
    private String tcm_treat_fee;

    /** 西药类:(13)西药费 */
    private String west_fee;

    /** 抗菌药物费 */
    private String kjywf;

    /** 中药类:(14)中成药费 */
    private String tcmpat_fee;

    /** 中草药费 */
    private String tcmherb;

    /** 血液和血液制品类:(16)血费 */
    private String blo_fee;

    /** 白蛋白类制品费 */
    private String bdblzpf;

    /** 球蛋白类制品费 */
    private String qdblzpf;

    /** 凝血因子类制品费 */
    private String nxyzlzpf;

    /** 细胞因子类制品费 */
    private String xbyzlzpf;

    /** 耗材类:(21)检查用一次性医用材料费 */
    private String hcyyclf;

    /** (22)治疗用一次性医用材料费 */
    private String yyclf;

    /** (23)手术用一次性医用材料费 */
    private String ycxyyclf;

    /** 其他类：(24)其他费 */
    private String oth_fee;

    /** 呼吸机使用时间(小时) */
    private String hxjsysj;

    /** 重症监护病房类型1 */
    private String zzjhbflx1;

    /** 进重症监护室时间1 */
    private String jzzjhssj1;

    /** 出重症监护室时间1 */
    private String czzjhssj1;

    /** 重症监护合计时长1 */
    private String zzjhhjsc1;

    /** 重症监护病房类型2 */
    private String zzjhbflx2;

    /** 进重症监护室时间2 */
    private String jzzjhssj2;

    /** 出重症监护室时间2 */
    private String czzjhssj2;

    /** 重症监护合计时长2 */
    private String zzjhhjsc2;

    /** 重症监护病房类型3 */
    private String zzjhbflx3;

    /** 进重症监护室时间3 */
    private String jzzjhssj3;

    /** 出重症监护室时间3 */
    private String czzjhssj3;

    /** 重症监护合计时长3 */
    private String zzjhhjsc3;

    /** 抢救次数 */
    private String qjqk1;

    /** 抢救成功次数 */
    private String qjqk2;

    /** 特级护理天数 */
    private String tjhlts;

    /** 一级护理天数 */
    private String yjhlts;

    /** 二级护理天数 */
    private String ejhlts;

    /** 三级护理天数 */
    private String sjhlts;

    /**医院机构等级*/
    private String hosp_lv;

    /**分组类型*/
    private String fzlx;

    /**医院编码*/
    private String hospital_id;

    /**院前检查费  */
    private BigDecimal yqjcf = new BigDecimal(0);
    // 十四项费用 新增 本系统独有
    private BigDecimal yb14fee = new BigDecimal(0);

    /** 申报时间 */
    private String sbsj;

    /** 患者证件类型 */
    private String hzzjlx;

    /** 住院医疗类型 */
    private String zyyllx;

    /** 票据代码 */
    private String pjdm;

    /** 票据号码 */
    private String pjhm;

    /** 业务流水号 */
    private String ywlsh;

    /** 结算开始时间 */
    private String jskssj;

    /** 结算结束时间 */
    private String jsjssj;

    /** 医保支付方式 */
    private String ybzffs;

    /** 医保机构 */
    private String ybjg;

    /** 医保机构经办人 */
    private String ybjgjbr;

    /** 清单流水号 */
    private String qdlsh;

    /** 就诊id */
    private String jdid;

    /** 结算id */
    private String jsid;

    /** 人员编号 */
    private String rybh;

    /** 主诊医师代码 */
    private String zzysdm;

    /** 主诊医师姓名 */
    private String zzysxm;

    /** 参保地的区域 */
    private String insuplc;

    /**
     * 原始传入的参保地
     */
    private String input_insuplc;

    /**基金费用  */
    private BigDecimal fundAmtSum = new BigDecimal(0);

    /**
     * 职工补偿比
     */
    private BigDecimal empFundRatio = new BigDecimal(0);
    /**
     * 居民补偿比
     */
    private BigDecimal residFundRatio = new BigDecimal(0);
}
