package com.my.som.dto.common;

import io.swagger.annotations.ApiModelProperty;

/**
 * 医院分组器查询参数
 * Created by sky on 2020/1/5.
 */
public class HospitalDrgQueryParam {
    @ApiModelProperty("分组器信息ID")
    private String id;
    @ApiModelProperty("分组器类型")
    private String grper_type;
    @ApiModelProperty("分组器版本")
    private String grper_ver;
    @ApiModelProperty("drgs分组器服务接口地址URL")
    private String grper_url_drg;
    @ApiModelProperty("有效标志")
    private String active_flag;
    @ApiModelProperty("医疗机构ID")
    private String hospital_id;
    @ApiModelProperty("医疗机构名称")
    private String medins_name;
    @ApiModelProperty("医院等级")
    private String hosp_lv;

    /** 医院类型 */
    private String medins_type;
    /**
     * 参保地医保区划(4位)
     */
    private String insuplcAdmdvs;

    public String getInsuplcAdmdvs() {
        return insuplcAdmdvs;
    }

    public void setInsuplcAdmdvs(String insuplcAdmdvs) {
        this.insuplcAdmdvs = insuplcAdmdvs;
    }

    public String getMedins_type() {
        return medins_type;
    }

    public void setMedins_type(String medins_type) {
        this.medins_type = medins_type;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getGrper_type() {
        return grper_type;
    }

    public void setGrper_type(String grper_type) {
        this.grper_type = grper_type;
    }

    public String getGrper_ver() {
        return grper_ver;
    }

    public void setGrper_ver(String grper_ver) {
        this.grper_ver = grper_ver;
    }

    public String getGrper_url_drg() {
        return grper_url_drg;
    }

    public void setGrper_url_drg(String grper_url_drg) {
        this.grper_url_drg = grper_url_drg;
    }

    public String getActive_flag() {
        return active_flag;
    }

    public void setActive_flag(String active_flag) {
        this.active_flag = active_flag;
    }

    public String getHospital_id() {
        return hospital_id;
    }

    public void setHospital_id(String hospital_id) {
        this.hospital_id = hospital_id;
    }

    public String getMedins_name() {
        return medins_name;
    }

    public void setMedins_name(String medins_name) {
        this.medins_name = medins_name;
    }

    public String getHosp_lv() {
        return hosp_lv;
    }

    public void setHosp_lv(String hosp_lv) {
        this.hosp_lv = hosp_lv;
    }
}
