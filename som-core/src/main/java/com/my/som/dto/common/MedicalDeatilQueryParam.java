package com.my.som.dto.common;

import com.my.som.common.dto.CommonQueryDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 病组下转详细信息查询参数
 * Created by sky on 2020/3/31.
 */
@Getter
@Setter
public class MedicalDeatilQueryParam extends CommonQueryDto {
    @ApiModelProperty("医院编号")
    private String hospitalId;
    @ApiModelProperty("出院开始时间")
    private String cy_start_date;
    @ApiModelProperty("出院结束时间")
    private String cy_end_date;
    @ApiModelProperty("出院科室编码")
    private String priOutHosDeptCode;
    @ApiModelProperty("DRGs编码")
    private String queryDrgsCode;
    @ApiModelProperty("成都分组编码")
    private String queryPpsGroupCode;
    @ApiModelProperty("ICD编码")
    private String queryIcdCode;
    @ApiModelProperty("疾病大类")
    private String queryDiagnosisType;
    @ApiModelProperty("手术总例数")
    private String allOprLevelCode;
    @ApiModelProperty("医院手术等级")
    private String oprLevelCode;
    @ApiModelProperty("标准手术等级")
    private String stanOprLevelCode;
    @ApiModelProperty("医生代码")
    private String drCodg;
    @ApiModelProperty("医院等级")
    private String hospLv;
    @ApiModelProperty("查询类型")
    private String queryType;
    @ApiModelProperty("有效标志")
    private String activeFlag;

    @ApiModelProperty("床日付费标志")
    private String beddayPayType;
    @ApiModelProperty(value = "医生编号集合")
    private List<String> doctorIdList;

    /** 下转页面类型 1: DIP 2: DRG 3: CD */
    private String type;
    /** 医生名称 */
    private String drName;
    
    /** 标杆年份 */
    private String standardYear;

    private String deptCode;

    /*
  参保地
   */
    private String insuplcAdmdvs;

    /*
    省参保地
    */
    private String provLevelInsuplcAdmdvs;
    private String feeMulAdjmConf;
    private String standType;
    private String categories;
}
