package com.my.som.listener.upload;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.my.som.common.exception.AppException;
import com.my.som.common.util.ValidateUtil;
import com.my.som.dto.upload.FileUploadDto;
import com.my.som.dto.upload.WorkersUploadDto;
import com.my.som.service.upload.WorkersUploadService;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.List;
/**
 * @author: zyd
 * @Date 2021/8/27 2:31 下午
 * @Version 1.0
 * @Description: 医护人员信息上传监听器
 */
public class WorkersFileUploadListener extends AnalysisEventListener<WorkersUploadDto> {
    private static final int BATCH_COUNT = 3000;
    private WorkersUploadService workersUploadService;
    private List<WorkersUploadDto> workersUploadDtoList = new ArrayList<>();
    private FileUploadDto fileUploadDto;
    private MultipartFile file;

    public WorkersFileUploadListener(WorkersUploadService workersUploadService, FileUploadDto fileUploadDto) {
        this.workersUploadService=workersUploadService;
        fileUploadDto.setCount(0);
        this.fileUploadDto = fileUploadDto;
    }

    @Override
    public void invoke(WorkersUploadDto dto, AnalysisContext analysisContext) {
        if (ValidateUtil.isEmpty(dto.getCode()) || ValidateUtil.isEmpty(dto.getName())) {
            throw new AppException("未填写医护人员姓名或医护人员代码");
        }
        int count = fileUploadDto.getCount() + 1;
        fileUploadDto.setCount(count);
        workersUploadDtoList.add(dto);
        if (workersUploadDtoList.size() >= BATCH_COUNT) {
            saveData();
            workersUploadDtoList.clear();
        }

    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
        saveData();
    }
    /**
     * 保存数据
     */
    private void saveData() {
        workersUploadService.analysis(workersUploadDtoList,fileUploadDto,file);
    }
}
