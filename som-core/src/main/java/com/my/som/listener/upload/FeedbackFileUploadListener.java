package com.my.som.listener.upload;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.my.som.common.constant.DrgConst;
import com.my.som.common.exception.AppException;
import com.my.som.common.util.AnnotationHandlerUtil;
import com.my.som.common.util.ValidateUtil;
import com.my.som.dto.upload.FileUploadDto;
import com.my.som.entity.upload.FeedbackEntity;
import com.my.som.service.upload.FeedbackFileUploadService;
import com.my.som.util.SysCommonConfigUtil;

import java.util.*;

/**
 * @author: zyd
 * @version 1.0
 * @date: 2023-07-31
 * @description:
 */
public class FeedbackFileUploadListener extends AnalysisEventListener<FeedbackEntity> {

    private static final int BATCH_COUNT = 3000;
    private List<FeedbackEntity> list = new ArrayList<>();
    /** 校验唯一字段 */
    private Set<String> uniqueSet = new HashSet<>();

    private FileUploadDto fileUploadDto;
    private FeedbackFileUploadService feedbackFileUploadService;

    /** 德阳映射 */
    private Map<String, String> dyCodeMapping = new HashMap<>();
    private Map<String, String> dyNameMapping = new HashMap<>();
    private Set<String> dyMappingSet = new HashSet<>();
    /**
     * 传入数据
     * @param feedbackFileUploadService 处理类
     * @param fileUploadDto 参数
     */
    public FeedbackFileUploadListener(FeedbackFileUploadService feedbackFileUploadService,
                                      FileUploadDto fileUploadDto){
        // 德阳
        List<Map<String, String>> dyGroupList = feedbackFileUploadService.queryDyGroup();
        if (ValidateUtil.isNotEmpty(dyGroupList)) {
            dyGroupList.stream().forEach(map -> {
                if (!dyCodeMapping.containsKey(map.get("dyGroup"))) {
                    dyCodeMapping.put(map.get("dyGroup"), map.get("dipCodg"));
                    dyNameMapping.put(map.get("dyGroup"), map.get("dipName"));
                }
                dyMappingSet.add(map.get("dyGroup"));
            });
        }
        fileUploadDto.setCount(0);
        this.fileUploadDto = fileUploadDto;
        this.feedbackFileUploadService = feedbackFileUploadService;
    }

    @Override
    public void invoke(FeedbackEntity entity, AnalysisContext analysisContext) {

        if (ValidateUtil.isEmpty(entity.getUniqueId())) {
            throw new AppException("唯一字段为空，请确认后上传");
        }

        if (uniqueSet.contains(entity.getUniqueId())) {
            throw new AppException("当前批次唯一字段重复：" + entity.getUniqueId());
        }
        // 记录总数
        fileUploadDto.setCount(fileUploadDto.getCount() + 1);
        // 批次号
        entity.setBatchNum(fileUploadDto.getBatchNum());
        // 医院id
        entity.setHospitalId(fileUploadDto.getHospitalId());
        // 处理日期
        AnnotationHandlerUtil.handlerDateAnnotation(entity);
        // 转换值
        transform(entity);
        list.add(entity);
        uniqueSet.add(entity.getUniqueId());
        if (list.size() >= BATCH_COUNT) {
            add();
            list.clear();
        }
    }

    private void transform(FeedbackEntity entity) {
        final HashMap<String, String> YES_NO_MAP = new HashMap<String, String>(){{
            put("是", "1");
            put("Y", "1");
            put("否", "0");
            put("N", "0");
        }};

        final HashMap<String, String> INSURED_TYPE_MAP = new HashMap<String, String>(){{
            put("城镇职工", "1");
            put("城乡居民", "2");
        }};

        final HashMap<String, String> MED_TYPE_MAP = new HashMap<String, String>(){{
            put("超高", "1");
            put("高倍率", "1");
            put("超高病例", "1");
            put("高倍率病例", "1");
            put("超低", "2");
            put("低倍率", "2");
            put("超低病例", "2");
            put("正常", "3");
            put("正常病例", "3");
            put("DEFAULT", "4");
        }};
        // 是否入组
        if (ValidateUtil.isNotEmpty(entity.getIsInGroup()) && ValidateUtil.isNotEmpty(YES_NO_MAP.get(entity.getIsInGroup().toUpperCase()))) {
            entity.setIsInGroup(YES_NO_MAP.get(entity.getIsInGroup().toUpperCase()));
        }

        // 辅助目录
        if (ValidateUtil.isNotEmpty(entity.getUsedAsstList()) && ValidateUtil.isNotEmpty(YES_NO_MAP.get(entity.getUsedAsstList().toUpperCase()))) {
            entity.setUsedAsstList(YES_NO_MAP.get(entity.getUsedAsstList().toUpperCase()));
        }

        // 参保类型
        if (ValidateUtil.isNotEmpty(entity.getInsuredType()) && ValidateUtil.isNotEmpty(INSURED_TYPE_MAP.get(entity.getInsuredType().toUpperCase()))) {
            entity.setInsuredType(INSURED_TYPE_MAP.get(entity.getInsuredType().toUpperCase()));
        }

        // 病例类型
        if (ValidateUtil.isNotEmpty(entity.getMedType()) && ValidateUtil.isNotEmpty(MED_TYPE_MAP.get(entity.getMedType().toUpperCase()))) {
            entity.setMedType(MED_TYPE_MAP.get(entity.getMedType().toUpperCase()));
        } else {
            entity.setMedType(MED_TYPE_MAP.get("DEFAULT"));
        }


        // 德阳编码/名称
        if (!ValidateUtil.isEmpty(dyMappingSet)) {
            entity.setDipCodg(dyCodeMapping.get(entity.getDipCodg()));
            entity.setDipName(dyNameMapping.get(entity.getDipName()));
        }
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
        add();
    }

    private void add(){
        feedbackFileUploadService.addFeedbackRecord(list, fileUploadDto);
    }
}
