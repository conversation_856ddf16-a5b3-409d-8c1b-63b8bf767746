package com.my.som.listener.upload;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.my.som.common.exception.AppException;
import com.my.som.common.util.ValidateUtil;
import com.my.som.dto.upload.DeptUploadDto;
import com.my.som.dto.upload.FileUploadDto;
import com.my.som.service.upload.DeptFileUploadService;

import java.util.ArrayList;
import java.util.List;

/**
 * @author: zyd
 * @Date 2021/8/6 6:09 下午
 * @Version 1.0
 * @Description: 科室信息上传监听器
 */
public class DeptFileUploadListener extends AnalysisEventListener<DeptUploadDto> {

    private static final int BATCH_COUNT = 3000;
    private List<DeptUploadDto> deptUploadDtoList = new ArrayList<>();

    private DeptFileUploadService deptFileUploadService;
    private FileUploadDto fileUploadDto;

    public DeptFileUploadListener(DeptFileUploadService deptFileUploadService, FileUploadDto fileUploadDto){
        this.deptFileUploadService = deptFileUploadService;
        fileUploadDto.setCount(0);
        this.fileUploadDto = fileUploadDto;
    }

    @Override
    public void invoke(DeptUploadDto dto, AnalysisContext analysisContext) {
        if (ValidateUtil.isEmpty(dto.getCode()) || ValidateUtil.isEmpty(dto.getName())) {
            throw new AppException("未填写科室编码或科室名称");
        }
        int count = fileUploadDto.getCount() + 1;
        fileUploadDto.setCount(count);
        deptUploadDtoList.add(dto);
        if (deptUploadDtoList.size() >= BATCH_COUNT) {
            saveData();
            deptUploadDtoList.clear();
        }
    }

    /**
     * 相当于 destroy,最后会调用
     * @param analysisContext
     */
    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
        saveData();
    }

    /**
     * 保存数据
     */
    private void saveData(){
        deptFileUploadService.save(deptUploadDtoList,fileUploadDto);
    }
}
