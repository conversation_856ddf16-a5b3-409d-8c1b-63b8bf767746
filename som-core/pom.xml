<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.my.som</groupId>
    <artifactId>som-core</artifactId>
    <version>1.0-SNAPSHOT</version>
    <packaging>jar</packaging>
    <name>som-core</name>
    <description>som-core project for drg</description>

    <parent>
        <groupId>com.my.som</groupId>
        <artifactId>somplatform</artifactId>
        <version>1.0-SNAPSHOT</version>
    </parent>

    <dependencies>
        <dependency>
            <groupId>com.my.som</groupId>
            <artifactId>som-activiti</artifactId>
        </dependency>
        <dependency>
            <groupId>com.my.som</groupId>
            <artifactId>som-mbp</artifactId>
        </dependency>
        <dependency>
            <groupId>com.my.som</groupId>
            <artifactId>som-security</artifactId>
        </dependency>
<!--                <dependency>
                    <groupId>com.my.som</groupId>
                    <artifactId>som-cost</artifactId>
                </dependency>-->
        <dependency>
            <groupId>com.my.som</groupId>
            <artifactId>som-adm</artifactId>
        </dependency>
        <dependency>
            <groupId>com.my.som</groupId>
            <artifactId>som-app</artifactId>
        </dependency>
        <dependency>
            <groupId>com.my.som</groupId>
            <artifactId>som-paths</artifactId>
        </dependency>
        <dependency>
            <groupId>com.my.som</groupId>
            <artifactId>som-dtsf</artifactId>
        </dependency>
        <dependency>
            <groupId>com.my.som</groupId>
            <artifactId>som-myset</artifactId>
        </dependency>
        <dependency>
            <groupId>com.my.som</groupId>
            <artifactId>som-hcs</artifactId>
        </dependency>
        <dependency>
            <groupId>com.aliyun.oss</groupId>
            <artifactId>aliyun-sdk-oss</artifactId>
        </dependency>
        <dependency>
            <groupId>net.logstash.logback</groupId>
            <artifactId>logstash-logback-encoder</artifactId>
        </dependency>
        <dependency>
            <groupId>commons-httpclient</groupId>
            <artifactId>commons-httpclient</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web-services</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.cxf</groupId>
            <artifactId>cxf-spring-boot-starter-jaxws</artifactId>
        </dependency>
        <dependency>
            <groupId>org.dom4j</groupId>
            <artifactId>dom4j</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.activemq</groupId>
            <artifactId>activemq-client</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-log4j12</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>jcl-over-slf4j</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>log4j</groupId>
                    <artifactId>log4j</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.apache.xbean</groupId>
            <artifactId>xbean-spring</artifactId>
        </dependency>

        <dependency>
            <groupId>com.microsoft.sqlserver</groupId>
            <artifactId>mssql-jdbc</artifactId>
            <version>8.4.1.jre8</version>
            <!--                <version>7.4.1.jre8</version>-->
        </dependency>
        <dependency>
            <groupId>com.my.som</groupId>
            <artifactId>som-cost</artifactId>
        </dependency>

        <!-- jws service -->
        <!--<dependency>
            <groupId>javax.jws</groupId>
            <artifactId>javax.jws-api</artifactId>
            <version>1.1</version>
        </dependency>-->

        <!--        <dependency>-->
        <!--            <groupId>jakarta.jws</groupId>-->
        <!--            <artifactId>jakarta.jws-api</artifactId>-->
        <!--            <version>3.0.0</version>-->
        <!--        </dependency>-->


        <!-- sqlserver -->
        <!--        <dependency>-->
        <!--            <groupId>com.microsoft.sqlserver</groupId>-->
        <!--            <artifactId>sqljdbc4</artifactId>-->
        <!--            <version>4.0</version>-->
        <!--            <scope>runtime</scope>-->
        <!--        </dependency>-->

    </dependencies>
    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <mainClass>com.my.som.DrgApplication</mainClass>
                    <includeSystemScope>true</includeSystemScope>
                </configuration>
            </plugin>
            <!--            <plugin>-->
            <!--                <groupId>org.example</groupId>-->
            <!--                <artifactId>mixed-plugin</artifactId>-->
            <!--                <version>1.0.0</version>-->
            <!--                <executions>-->
            <!--                    <execution>-->
            <!--                        <goals>-->
            <!--                            <goal>build</goal>-->
            <!--                        </goals>-->
            <!--                        <phase>package</phase>-->
            <!--                        <configuration>-->
            <!--                            <source>${project.basedir}/target/${project.artifactId}-${project.version}.jar</source>-->
            <!--                            <target>${project.basedir}/target/${project.artifactId}.jar</target>-->
            <!--                            <exclude>-->
            <!--                                <exclude>static/</exclude>-->
            <!--                            </exclude>-->
            <!--                        </configuration>-->
            <!--                    </execution>-->
            <!--                </executions>-->
            <!--            </plugin>-->
        </plugins>
    </build>
</project>
