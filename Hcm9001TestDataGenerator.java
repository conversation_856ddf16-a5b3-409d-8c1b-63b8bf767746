package com.my.som.test;

import com.my.som.dts.entity.param.CommonParam;
import com.my.som.dts.entity.param.Hcm9001Param;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * HCM9001接口测试数据生成器
 */
public class Hcm9001TestDataGenerator {

    private static final SimpleDateFormat dateTimeFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    private static final SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");

    /**
     * 生成完整的测试数据
     */
    public static CommonParam<Hcm9001Param> generateTestData() {
        CommonParam<Hcm9001Param> commonParam = new CommonParam<>();
        
        // 设置公共参数
        commonParam.setOpter("TEST_USER");
        commonParam.setOpter_name("测试用户");
        commonParam.setFixmedins_code("H12345678901");
        
        // 创建HCM9001参数
        Hcm9001Param hcm9001Param = new Hcm9001Param();
        
        // 创建data对象
        Hcm9001Param.data data = new Hcm9001Param.data();
        data.setSyscode("HIS001");
        data.setEncrypt_key("test_encrypt_key_123456");
        data.setTask_id("TASK_20250808_001");
        data.setTrig_scen("4"); // 医生站出院审核
        
        // 创建患者信息
        Hcm9001Param.Patient_dtos patientDtos = new Hcm9001Param.Patient_dtos();
        patientDtos.setPatn_id("P123456789");
        patientDtos.setPatn_name("张三");
        patientDtos.setGend("1"); // 男性
        try {
            patientDtos.setBrdy(dateFormat.parse("1980-05-15"));
        } catch (ParseException e) {
            e.printStackTrace();
        }
        patientDtos.setPoolarea("510100");
        patientDtos.setUnique_id("UNIQUE_20250808_001");
        
        // 创建就诊信息
        Hcm9001Param.Fsi_encounter_dtos encounterDtos = new Hcm9001Param.Fsi_encounter_dtos();
        encounterDtos.setMdtrt_id("MDT_20250808_001");
        encounterDtos.setMedins_id("H12345678901");
        encounterDtos.setMedins_name("测试医院");
        encounterDtos.setMedins_admdvs("510100");
        encounterDtos.setMedins_lv("3"); // 三级医院
        
        try {
            encounterDtos.setAdm_date(dateTimeFormat.parse("2025-08-01 09:30:00"));
            encounterDtos.setDscg_date(dateTimeFormat.parse("2025-08-07 15:20:00"));
        } catch (ParseException e) {
            e.printStackTrace();
        }
        
        encounterDtos.setDscg_main_dise_codg("I21.900");
        encounterDtos.setDscg_main_dise_name("急性心肌梗死");
        
        // 创建诊断信息列表
        List<Hcm9001Param.Fsi_diagnose_dtos> diagnoseDtosList = new ArrayList<>();
        
        // 主诊断
        Hcm9001Param.Fsi_diagnose_dtos mainDiag = new Hcm9001Param.Fsi_diagnose_dtos();
        mainDiag.setDise_id("DIAG_001");
        mainDiag.setInout_dise_type("1"); // 出院诊断
        mainDiag.setMaindise_flag("1"); // 主诊断
        mainDiag.setDias_srt_no("1");
        mainDiag.setDise_codg("I21.900");
        mainDiag.setDise_name("急性心肌梗死");
        try {
            mainDiag.setDise_date(dateFormat.parse("2025-08-01"));
        } catch (ParseException e) {
            e.printStackTrace();
        }
        mainDiag.setDise_sys("1"); // ICD10
        mainDiag.setUnique_id("UNIQUE_20250808_001");
        diagnoseDtosList.add(mainDiag);
        
        // 次要诊断1
        Hcm9001Param.Fsi_diagnose_dtos secondDiag1 = new Hcm9001Param.Fsi_diagnose_dtos();
        secondDiag1.setDise_id("DIAG_002");
        secondDiag1.setInout_dise_type("1");
        secondDiag1.setMaindise_flag("0"); // 非主诊断
        secondDiag1.setDias_srt_no("2");
        secondDiag1.setDise_codg("E11.900");
        secondDiag1.setDise_name("2型糖尿病");
        try {
            secondDiag1.setDise_date(dateFormat.parse("2025-08-01"));
        } catch (ParseException e) {
            e.printStackTrace();
        }
        secondDiag1.setDise_sys("1");
        secondDiag1.setUnique_id("UNIQUE_20250808_001");
        diagnoseDtosList.add(secondDiag1);
        
        // 次要诊断2
        Hcm9001Param.Fsi_diagnose_dtos secondDiag2 = new Hcm9001Param.Fsi_diagnose_dtos();
        secondDiag2.setDise_id("DIAG_003");
        secondDiag2.setInout_dise_type("1");
        secondDiag2.setMaindise_flag("0");
        secondDiag2.setDias_srt_no("3");
        secondDiag2.setDise_codg("I10.x00");
        secondDiag2.setDise_name("原发性高血压");
        try {
            secondDiag2.setDise_date(dateFormat.parse("2025-08-01"));
        } catch (ParseException e) {
            e.printStackTrace();
        }
        secondDiag2.setDise_sys("1");
        secondDiag2.setUnique_id("UNIQUE_20250808_001");
        diagnoseDtosList.add(secondDiag2);
        
        encounterDtos.setFsi_diagnose_dtos(diagnoseDtosList);
        
        // 设置医生和科室信息
        encounterDtos.setDr_codg("DOC001");
        encounterDtos.setDr_name("李医生");
        encounterDtos.setAdm_dept_codg("DEPT001");
        encounterDtos.setAdm_dept_name("心内科");
        encounterDtos.setCur_dept_codg("DEPT001");
        encounterDtos.setCur_dept_name("心内科");
        encounterDtos.setDscg_dept_codg("DEPT001");
        encounterDtos.setDscg_dept_name("心内科");
        
        encounterDtos.setMed_mdtrt_type("2"); // 住院
        encounterDtos.setMed_type("1"); // 住院
        
        // 创建费用明细列表
        List<Hcm9001Param.Fsi_order_dtos> orderDtosList = new ArrayList<>();
        
        // 药品费用
        Hcm9001Param.Fsi_order_dtos drugOrder = createDrugOrder();
        orderDtosList.add(drugOrder);
        
        // 检查费用
        Hcm9001Param.Fsi_order_dtos examOrder = createExamOrder();
        orderDtosList.add(examOrder);
        
        // 化验费用
        Hcm9001Param.Fsi_order_dtos labOrder = createLabOrder();
        orderDtosList.add(labOrder);
        
        encounterDtos.setFsi_order_dtos(orderDtosList);
        
        // 设置其他就诊信息
        encounterDtos.setMatn_stas("1");
        encounterDtos.setMedfee_sumamt(new BigDecimal("170.50"));
        encounterDtos.setSetl_totlnum(1);
        encounterDtos.setInsutype("310");
        encounterDtos.setReim_flag("1");
        encounterDtos.setOut_setl_flag("0");
        
        // 创建手术信息列表
        List<Hcm9001Param.Fsi_operation_dtos> operationDtosList = new ArrayList<>();
        Hcm9001Param.Fsi_operation_dtos operation = new Hcm9001Param.Fsi_operation_dtos();
        operation.setSetl_list_oprn_id("OP_001");
        operation.setOprn_code("36.06");
        operation.setOprn_name("经皮冠状动脉介入治疗");
        operation.setMain_oprn_flag("1"); // 主手术
        try {
            operation.setOprn_date(dateFormat.parse("2025-08-02"));
        } catch (ParseException e) {
            e.printStackTrace();
        }
        operation.setAnst_way("局部麻醉");
        operation.setOper_dr_name("张主任");
        operation.setOper_dr_code("DOC002");
        operation.setAnst_dr_name("赵医生");
        operation.setUnique_id("UNIQUE_20250808_001");
        operationDtosList.add(operation);
        
        encounterDtos.setFsi_operation_dtos(operationDtosList);
        
        encounterDtos.setPatn_ipt_cnt("1");
        encounterDtos.setHi_feesetl_type("1");
        encounterDtos.setHi_paymtd("1");
        encounterDtos.setIpt_no("ZY20250808001");
        encounterDtos.setPsn_type("1");
        encounterDtos.setAdm_cond_code("2"); // 急症
        encounterDtos.setFee_batch_sign("1");
        encounterDtos.setOut_put_type("1");
        encounterDtos.setUnique_id("UNIQUE_20250808_001");
        
        // 组装数据
        patientDtos.setFsi_encounter_dtos(encounterDtos);
        data.setPatient_dtos(patientDtos);
        hcm9001Param.setData(data);
        commonParam.setInput(hcm9001Param);
        
        return commonParam;
    }
    
    /**
     * 创建药品费用项目
     */
    private static Hcm9001Param.Fsi_order_dtos createDrugOrder() {
        Hcm9001Param.Fsi_order_dtos order = new Hcm9001Param.Fsi_order_dtos();
        order.setRx_id("RX_001");
        order.setRxno("RX20250808001");
        order.setGrpno("GRP001");
        order.setDrord_detl_id("ORDER_001");
        order.setLong_drord_flag("0");
        order.setHilist_type("1"); // 药品
        order.setChrg_type("11");
        order.setChrg_name("西药费");
        order.setDrord_bhvr("0");
        order.setHilist_code("XY001234567890");
        order.setHilist_name("阿司匹林肠溶片");
        order.setHilist_dosform("片剂");
        order.setHilist_lv("1");
        order.setHilist_pric("0.85");
        order.setHosplist_code("HY001234567890");
        order.setHosplist_name("阿司匹林肠溶片");
        order.setHosplist_dosform("片剂");
        order.setCnt(new BigDecimal("30"));
        order.setPric(new BigDecimal("0.85"));
        order.setSumamt(new BigDecimal("25.50"));
        order.setOwnpay_amt(new BigDecimal("0.00"));
        order.setSelfpay_amt(new BigDecimal("0.00"));
        order.setSpec("100mg");
        order.setSpec_unt("片");
        
        try {
            order.setFee_ocur_time(dateTimeFormat.parse("2025-08-02 08:30:00"));
            order.setDrord_ocur_time(dateTimeFormat.parse("2025-08-02 08:00:00"));
            order.setDrord_begn_date(dateTimeFormat.parse("2025-08-02 08:00:00"));
            order.setDrord_stop_date(dateTimeFormat.parse("2025-08-07 08:00:00"));
        } catch (ParseException e) {
            e.printStackTrace();
        }
        
        order.setDrord_dept_codg("DEPT001");
        order.setDrord_dept_name("心内科");
        order.setDrord_dr_codg("DOC001");
        order.setDrord_dr_name("李医生");
        order.setDrord_dr_profttl("主治医师");
        order.setCurr_drord_flag("1");
        order.setFee_nr_codg("NURSE001");
        order.setFee_nr_name("王护士");
        order.setDrord_name("阿司匹林肠溶片口服");
        order.setHosp_appr_flag("1");
        order.setDrug_used_frqu(new BigDecimal("1"));
        order.setDrug_used_sdose("100mg");
        order.setMedn_used_dosunt("mg");
        order.setMedc_days("5");
        order.setUnique_id("UNIQUE_20250808_001");
        
        return order;
    }
    
    /**
     * 创建检查费用项目
     */
    private static Hcm9001Param.Fsi_order_dtos createExamOrder() {
        Hcm9001Param.Fsi_order_dtos order = new Hcm9001Param.Fsi_order_dtos();
        order.setRx_id("RX_002");
        order.setRxno("RX20250808002");
        order.setGrpno("GRP002");
        order.setDrord_detl_id("ORDER_002");
        order.setLong_drord_flag("0");
        order.setHilist_type("2"); // 诊疗项目
        order.setChrg_type("14");
        order.setChrg_name("检查费");
        order.setDrord_bhvr("0");
        order.setHilist_code("JC001234567890");
        order.setHilist_name("心电图检查");
        order.setHilist_lv("1");
        order.setHilist_pric("25.00");
        order.setHosplist_code("HJC001234567890");
        order.setHosplist_name("心电图检查");
        order.setCnt(new BigDecimal("1"));
        order.setPric(new BigDecimal("25.00"));
        order.setSumamt(new BigDecimal("25.00"));
        order.setOwnpay_amt(new BigDecimal("0.00"));
        order.setSelfpay_amt(new BigDecimal("0.00"));
        order.setSpec("常规心电图");
        order.setSpec_unt("次");
        
        try {
            order.setFee_ocur_time(dateTimeFormat.parse("2025-08-01 10:30:00"));
            order.setDrord_ocur_time(dateTimeFormat.parse("2025-08-01 10:00:00"));
            order.setDrord_begn_date(dateTimeFormat.parse("2025-08-01 10:00:00"));
            order.setDrord_stop_date(dateTimeFormat.parse("2025-08-01 11:00:00"));
        } catch (ParseException e) {
            e.printStackTrace();
        }
        
        order.setDrord_dept_codg("DEPT001");
        order.setDrord_dept_name("心内科");
        order.setDrord_dr_codg("DOC001");
        order.setDrord_dr_name("李医生");
        order.setDrord_dr_profttl("主治医师");
        order.setCurr_drord_flag("1");
        order.setFee_nr_codg("NURSE001");
        order.setFee_nr_name("王护士");
        order.setDrord_name("心电图检查");
        order.setHosp_appr_flag("1");
        order.setUnique_id("UNIQUE_20250808_001");
        
        return order;
    }
    
    /**
     * 创建化验费用项目
     */
    private static Hcm9001Param.Fsi_order_dtos createLabOrder() {
        Hcm9001Param.Fsi_order_dtos order = new Hcm9001Param.Fsi_order_dtos();
        order.setRx_id("RX_003");
        order.setRxno("RX20250808003");
        order.setGrpno("GRP003");
        order.setDrord_detl_id("ORDER_003");
        order.setLong_drord_flag("0");
        order.setHilist_type("2"); // 诊疗项目
        order.setChrg_type("13");
        order.setChrg_name("化验费");
        order.setDrord_bhvr("0");
        order.setHilist_code("HY001234567890");
        order.setHilist_name("心肌酶谱检测");
        order.setHilist_lv("1");
        order.setHilist_pric("120.00");
        order.setHosplist_code("HHY001234567890");
        order.setHosplist_name("心肌酶谱检测");
        order.setCnt(new BigDecimal("1"));
        order.setPric(new BigDecimal("120.00"));
        order.setSumamt(new BigDecimal("120.00"));
        order.setOwnpay_amt(new BigDecimal("0.00"));
        order.setSelfpay_amt(new BigDecimal("0.00"));
        order.setSpec("心肌酶谱全套");
        order.setSpec_unt("次");
        
        try {
            order.setFee_ocur_time(dateTimeFormat.parse("2025-08-01 11:00:00"));
            order.setDrord_ocur_time(dateTimeFormat.parse("2025-08-01 10:30:00"));
            order.setDrord_begn_date(dateTimeFormat.parse("2025-08-01 10:30:00"));
            order.setDrord_stop_date(dateTimeFormat.parse("2025-08-01 12:00:00"));
        } catch (ParseException e) {
            e.printStackTrace();
        }
        
        order.setDrord_dept_codg("DEPT001");
        order.setDrord_dept_name("心内科");
        order.setDrord_dr_codg("DOC001");
        order.setDrord_dr_name("李医生");
        order.setDrord_dr_profttl("主治医师");
        order.setCurr_drord_flag("1");
        order.setFee_nr_codg("NURSE001");
        order.setFee_nr_name("王护士");
        order.setDrord_name("心肌酶谱检测");
        order.setHosp_appr_flag("1");
        order.setUnique_id("UNIQUE_20250808_001");
        
        return order;
    }
}
