# 数据转换路由配置文件
# 此配置文件定义了根据info参数值匹配的不同服务类型和处理方式
# 主要用于：1. 暴露接口给外部系统调用（数据格式转换） 2. 调用第三方接口 3.参数转换

routes:

  - infno: "HCM2203"
    serviceType: "dataExpose"
    targetUrl: "internal://dts/api/hcm2203"
    method: "POST"
    targetFormat: "JSON"
    responseType: "wrapped"
    timeout: 15000
    headers:
      Content-Type: "application/xml"

  - infno: "SOM2301"
    serviceType: "dataExpose"
    targetUrl: "internal://dts/som2301"
    method: "POST"
    targetFormat: "JSON"
    responseType: "wrapped"
    timeout: 15000
    headers:
      Content-Type: "application/json"

  - infno: "HCM9001"
    serviceType: "dataExpose"
    targetUrl: "internal://dts/api/hcm9001"
    method: "POST"
    targetFormat: "JSON"
    responseType: "direct"
    timeout: 15000
    headers:
      Content-Type: "application/json"

  - infno: "PostgreSQL"
    serviceType: "dataBase"
    targetUrl: "local://resultset"  # 本地处理模式，不发送到外部URL
    dataSource:
      url: "********************************************"
      username: "root"
      password: "123456"
      driverClassName: "org.postgresql.Driver"
      maxActive: 5
      initialSize: 1
      maxWait: 30000
    sourceQuery:  'SELECT * FROM "public"."Sheet2" limit 10'
    responseFormat: "resultset"
    responseType: "direct"
    method: "POST"
    timeout: 30000
    headers:
      Content-Type: "application/json"
      X-Test-Type: "PostgreSQL-Connection-Test"