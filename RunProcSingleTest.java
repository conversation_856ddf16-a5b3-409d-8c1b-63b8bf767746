package com.my.som.test;

import com.my.som.dts.entity.param.CommonParam;
import com.my.som.dts.entity.param.Hcm9001Param;
import com.my.som.service.hcs.RunHcsDataProcessService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * runProcSingle方法测试类
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class RunProcSingleTest {

    @Autowired
    private RunHcsDataProcessService runHcsDataProcessService;

    /**
     * 测试单条自查自纠执行
     */
    @Test
    public void testRunProcSingle() {
        System.out.println("=== 开始测试单条自查自纠执行 ===");
        
        try {
            // 1. 生成测试数据
            CommonParam<Hcm9001Param> testData = Hcm9001TestDataGenerator.generateTestData();
            
            System.out.println("测试数据生成完成:");
            System.out.println("- 操作员: " + testData.getOpter());
            System.out.println("- 医疗机构: " + testData.getFixmedins_code());
            System.out.println("- 患者姓名: " + testData.getInput().getData().getPatient_dtos().getPatn_name());
            System.out.println("- 住院号: " + testData.getInput().getData().getPatient_dtos().getFsi_encounter_dtos().getIpt_no());
            System.out.println("- 主诊断: " + testData.getInput().getData().getPatient_dtos().getFsi_encounter_dtos().getDscg_main_dise_name());
            
            // 2. 执行单条自查自纠
            long startTime = System.currentTimeMillis();
            int violationCount = runHcsDataProcessService.runProcSingle(testData);
            long endTime = System.currentTimeMillis();
            
            // 3. 输出结果
            System.out.println("\n=== 执行结果 ===");
            System.out.println("执行时间: " + (endTime - startTime) + " ms");
            System.out.println("违规病例数: " + violationCount);
            
            if (violationCount > 0) {
                System.out.println("✅ 检测到违规项目，自查自纠执行成功！");
            } else {
                System.out.println("✅ 未检测到违规项目，数据符合规范！");
            }
            
        } catch (Exception e) {
            System.err.println("❌ 测试执行失败: " + e.getMessage());
            e.printStackTrace();
        }
        
        System.out.println("=== 测试完成 ===");
    }

    /**
     * 测试空数据处理
     */
    @Test
    public void testRunProcSingleWithNullData() {
        System.out.println("=== 测试空数据处理 ===");
        
        try {
            // 测试null参数
            int result1 = runHcsDataProcessService.runProcSingle(null);
            System.out.println("null参数测试结果: " + result1);
            assert result1 == 0 : "null参数应该返回0";
            
            // 测试空input
            CommonParam<Hcm9001Param> emptyParam = new CommonParam<>();
            emptyParam.setOpter("TEST_USER");
            emptyParam.setOpter_name("测试用户");
            emptyParam.setFixmedins_code("H12345678901");
            emptyParam.setInput(null);
            
            int result2 = runHcsDataProcessService.runProcSingle(emptyParam);
            System.out.println("空input测试结果: " + result2);
            assert result2 == 0 : "空input应该返回0";
            
            System.out.println("✅ 空数据处理测试通过");
            
        } catch (Exception e) {
            System.err.println("❌ 空数据测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 测试数据转换功能
     */
    @Test
    public void testDataConversion() {
        System.out.println("=== 测试数据转换功能 ===");
        
        try {
            // 生成测试数据
            CommonParam<Hcm9001Param> testData = Hcm9001TestDataGenerator.generateTestData();
            Hcm9001Param.data data = testData.getInput().getData();
            
            // 验证数据结构
            assert data != null : "data不能为空";
            assert data.getPatient_dtos() != null : "patient_dtos不能为空";
            assert data.getPatient_dtos().getFsi_encounter_dtos() != null : "fsi_encounter_dtos不能为空";
            
            // 验证诊断信息
            assert data.getPatient_dtos().getFsi_encounter_dtos().getFsi_diagnose_dtos() != null : "诊断信息不能为空";
            assert data.getPatient_dtos().getFsi_encounter_dtos().getFsi_diagnose_dtos().size() > 0 : "至少应该有一个诊断";
            
            // 验证费用信息
            assert data.getPatient_dtos().getFsi_encounter_dtos().getFsi_order_dtos() != null : "费用信息不能为空";
            assert data.getPatient_dtos().getFsi_encounter_dtos().getFsi_order_dtos().size() > 0 : "至少应该有一个费用项目";
            
            // 验证手术信息
            assert data.getPatient_dtos().getFsi_encounter_dtos().getFsi_operation_dtos() != null : "手术信息不能为空";
            assert data.getPatient_dtos().getFsi_encounter_dtos().getFsi_operation_dtos().size() > 0 : "至少应该有一个手术";
            
            System.out.println("✅ 数据转换功能测试通过");
            System.out.println("- 诊断数量: " + data.getPatient_dtos().getFsi_encounter_dtos().getFsi_diagnose_dtos().size());
            System.out.println("- 费用项目数量: " + data.getPatient_dtos().getFsi_encounter_dtos().getFsi_order_dtos().size());
            System.out.println("- 手术数量: " + data.getPatient_dtos().getFsi_encounter_dtos().getFsi_operation_dtos().size());
            
        } catch (Exception e) {
            System.err.println("❌ 数据转换测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 测试不同触发场景
     */
    @Test
    public void testDifferentTriggerScenarios() {
        System.out.println("=== 测试不同触发场景 ===");
        
        String[] scenarios = {"1", "2", "3", "4", "5", "7", "12", "13"};
        String[] scenarioNames = {
            "普通/门特门诊处方", "医生医嘱审核", "护士计费审核", "医生站出院审核", 
            "医保办出院审核", "护士站出院审核", "医生站转科审核", "护士站转科审核"
        };
        
        for (int i = 0; i < scenarios.length; i++) {
            try {
                System.out.println("\n--- 测试场景 " + scenarios[i] + ": " + scenarioNames[i] + " ---");
                
                CommonParam<Hcm9001Param> testData = Hcm9001TestDataGenerator.generateTestData();
                testData.getInput().getData().setTrig_scen(scenarios[i]);
                
                int result = runHcsDataProcessService.runProcSingle(testData);
                System.out.println("场景 " + scenarios[i] + " 执行结果: " + result);
                
            } catch (Exception e) {
                System.err.println("场景 " + scenarios[i] + " 执行失败: " + e.getMessage());
            }
        }
        
        System.out.println("✅ 不同触发场景测试完成");
    }
}
